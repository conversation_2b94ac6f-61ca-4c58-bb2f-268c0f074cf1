package consumer

import (
	"context"
	"time"

	"go.uber.org/zap"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/gamma/api/customdelayqueue/consumer"
	"github.com/epifi/gamma/customdelayqueue/metrics"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
)

type IntermediateConsumersService struct {
	orchestratorPub queue.Publisher
}

func NewIntermediateConsumersService(orchestratorPub queue.Publisher) *IntermediateConsumersService {
	return &IntermediateConsumersService{orchestratorPub: orchestratorPub}
}

// ProcessEvent consumes the orchestrator event and waits till the processAt time is reached and post that
// pushes the event to the orchestrator queue. The main job of the consumer is to delay the event till the
// processAt time.
func (i *IntermediateConsumersService) ProcessEvent(ctx context.Context, req *consumer.OrchestratorEventRequest) (*consumer.OrchestratorEventResponse, error) {
	waitForDuration := time.Until(req.GetProcessAt().AsTime())
	if waitForDuration.Seconds() > 0 {
		logger.Debug(ctx, "blocking thread for achieving delay", zap.Duration("wait_time", waitForDuration))
		time.Sleep(waitForDuration)
	}
	// if wait time is long, then current receipt of event won't get deleted even on returning SUCCESS as the receipt handle gets expired.
	// and as a result the event would get replayed. So ignoring the current receipt for publishing to orchestrator queue.
	// todo (utkarsh) : add this value in config
	if waitForDuration > 25*time.Second {
		logger.Debug(ctx, "not pushing current event to orchestrator, replayed event would be pushed instead", zap.Duration("wait_time", waitForDuration))
		return &consumer.OrchestratorEventResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}
	_, err := i.orchestratorPub.Publish(ctx, &consumer.OrchestratorConsumerRequest{
		Header: req.GetHeader(),
	})
	if err != nil {
		return &consumer.OrchestratorEventResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}
	// instrument additional delay (than required) introduced by the intermediate queue.
	metrics.MetricsRecorder.RecordIntermediateQueueAdditionalDelay(time.Since(req.GetProcessAt().AsTime()).Seconds())
	logger.Debug(ctx, "message published successfully to orchestrator", zap.Time("process_at", req.GetProcessAt().AsTime()))
	return &consumer.OrchestratorEventResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS}}, nil
}

// ProcessDlqEvent consumes the events from intermediate queue's DLQ and pushes it to the orchestrator queue.
// The orchestrator would now decide the appropriate queue to push these events into based on the remaining
// delay for these events.
func (i *IntermediateConsumersService) ProcessDlqEvent(ctx context.Context, req *consumer.OrchestratorEventRequest) (*consumer.OrchestratorEventResponse, error) {
	logger.Debug(ctx, "dlq message consumed", zap.Time("process_at", req.GetProcessAt().AsTime()))
	_, err := i.orchestratorPub.Publish(ctx, &consumer.OrchestratorConsumerRequest{
		Header: req.GetHeader(),
	})
	if err != nil {
		return &consumer.OrchestratorEventResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}
	logger.Debug(ctx, "dlq message published successfully to orchestrator", zap.Time("msg_process_at", req.GetProcessAt().AsTime()))
	return &consumer.OrchestratorEventResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS}}, nil
}
