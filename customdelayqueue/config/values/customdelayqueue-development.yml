Application:
  Environment: "development"
  Name: "customdelayqueue"
  IsSecureRedis : false

Server:
  Ports:
    GrpcPort: 9091
    GrpcSecurePort: 9515
    HttpPort: 9999
    HttpPProfPort: 9990

Aws:
  Region: "ap-south-1"

RedisOptions:
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 1

DelayQueueStackConfig :
  OrchestratorQueueSqsPublisher:
    QueueName: "custom-delay-orchestrator-queue"
  OrchestratorQueueSqsSubscriber:
    StartOnServerStart: true
    NumWorkers: 2
    PollingDuration: 20
    MaxMessages: 10
    QueueName: "custom-delay-orchestrator-queue"
    RetryStrategy:
      RegularInterval:
        Interval: 1
        MaxAttempts: 10
        TimeUnit: "Minute"

  IntermediateQueue1SqsPublisher:
    QueueName: "custom-delay-intermediate-queue-1.fifo"
    IsDestQueueFifo: true
    FifoMsgGroupId: "IntermediateQueue1FifoMsgGroupId1"
  IntermediateQueue1SqsSubscriber:
    StartOnServerStart: true
    NumWorkers: 1
    PollingDuration: 20
    MaxMessages: 1
    QueueName: "custom-delay-intermediate-queue-1.fifo"
    RetryStrategy:
      RegularInterval:
        Interval: 1
        MaxAttempts: 10
        TimeUnit: "Second"

  IntermediateQueue2SqsPublisher:
    QueueName: "custom-delay-intermediate-queue-2.fifo"
    IsDestQueueFifo: true
    FifoMsgGroupId: "IntermediateQueue2FifoMsgGroupId1"
  IntermediateQueue2SqsSubscriber:
    StartOnServerStart: true
    NumWorkers: 1
    PollingDuration: 20
    MaxMessages: 1
    QueueName: "custom-delay-intermediate-queue-2.fifo"
    RetryStrategy:
      RegularInterval:
        Interval: 1
        MaxAttempts: 10
        TimeUnit: "Second"

  IntermediateQueue3SqsPublisher:
    QueueName: "custom-delay-intermediate-queue-3.fifo"
    IsDestQueueFifo: true
    FifoMsgGroupId: "IntermediateQueue3FifoMsgGroupId1"
  IntermediateQueue3SqsSubscriber:
    StartOnServerStart: true
    NumWorkers: 1
    PollingDuration: 20
    MaxMessages: 1
    QueueName: "custom-delay-intermediate-queue-3.fifo"
    RetryStrategy:
      RegularInterval:
        Interval: 1
        MaxAttempts: 10
        TimeUnit: "Second"

  # common DLQ subscriber for all intermediate queues
  IntermediateQueueDLQSubscriber:
    StartOnServerStart: true
    NumWorkers: 1
    PollingDuration: 20
    MaxMessages: 1
    QueueName: "dlq-custom-delay-intermediate-queue-1.fifo"
    RetryStrategy:
      RegularInterval:
        Interval: 1
        MaxAttempts: 10
        TimeUnit: "Second"

DestDelayQueueSqsPublisherConfig:
  RewardsProcessingDelaySqsPublisher:
    QueueName: "rewards-processing-delay-queue"
  InAppReferralNotificationsDelaySqsPublisher:
    QueueName: "inappreferral-notifications-delay-queue"
  WealthOnboardingStepsRetrySqsCustomDelayPublisher:
    QueueName: "wo-steps-retry-delay-queue"
  ExchangerOrderNotificationDelaySqsPublisher:
    QueueName: "casper-exchanger-order-notification-delay-queue"
  SalaryProgramNotificationDelaySqsPublisher:
    QueueName: "salaryprogram-notification-delay-queue"
  RewardsClaimRewardEventDelaySqsPublisher:
    QueueName: "rewards-claim-reward-event-queue"
  RewardsDataCollectorEventDelaySqsPublisher:
    QueueName: "rewards-data-collector-queue"
  ReferralsEligibilityCollectedDataSqsPublisher:
    QueueName: "referrals-eligibility-datacollector-queue"
  ReferralsNotificationSqsPublisher:
    QueueName: "referrals-notification-queue"
  NudgeEntryEventDelaySqsPublisher:
    QueueName: "nudge-entry-event-queue"
  RewardsRewardClawbackEventDelaySqsPublisher:
    QueueName: "rewards-reward-clawback-event-collector-queue"
  RewardsRewardUnlockerSqsCustomDelayPublisher:
    QueueName: "rewards-reward-unlocker-queue"
  RewardsRewardExpirySqsCustomDelayPublisher:
    QueueName: "rewards-reward-expiry-queue"
  SalaryProgramSalaryStatusUpdateSqsCustomDelayPublisher:
    QueueName: "salaryprogram-salary-status-update-event-consumer-queue"
  InvestmentEventBasedNotificationsSqsCustomDelayPublisher:
    QueueName: "investment-event-based-notifications-queue"
  RewardsNotificationEventSqsCustomDelayPublisher:
    QueueName: "rewards-notification-queue"

Profiling:
  StackDriverProfiling:
    ProjectId: "development"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true
