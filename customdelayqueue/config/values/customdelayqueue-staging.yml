Application:
  Environment: "staging"
  Name: "customdelayqueue"
  IsSecureRedis : true

Server:
  Ports:
    GrpcPort: 9091
    GrpcSecurePort: 9515
    HttpPort: 9999
    HttpPProfPort: 9990

Aws:
  Region: "ap-south-1"

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    # on staging we are using common redis cluster with a different db
    DB: 4

DelayQueueStackConfig :
  OrchestratorQueueSqsPublisher:
    QueueName: "staging-custom-delay-orchestrator-queue"
  OrchestratorQueueSqsSubscriber:
    StartOnServerStart: true
    NumWorkers: 2
    PollingDuration: 20
    MaxMessages: 10
    QueueName: "staging-custom-delay-orchestrator-queue"
    RetryStrategy:
      RegularInterval:
        Interval: 1
        MaxAttempts: 10
        TimeUnit: "Minute"

  IntermediateQueue1SqsPublisher:
    QueueName: "staging-custom-delay-intermediate-queue-1.fifo"
    IsDestQueueFifo: true
    FifoMsgGroupId: "IntermediateQueue1FifoMsgGroupId1"
  IntermediateQueue1SqsSubscriber:
    StartOnServerStart: true
    NumWorkers: 1
    PollingDuration: 20
    MaxMessages: 1
    QueueName: "staging-custom-delay-intermediate-queue-1.fifo"
    RetryStrategy:
      RegularInterval:
        Interval: 1
        MaxAttempts: 10
        TimeUnit: "Second"

  IntermediateQueue2SqsPublisher:
    QueueName: "staging-custom-delay-intermediate-queue-2.fifo"
    IsDestQueueFifo: true
    FifoMsgGroupId: "IntermediateQueue2FifoMsgGroupId1"
  IntermediateQueue2SqsSubscriber:
    StartOnServerStart: true
    NumWorkers: 1
    PollingDuration: 20
    MaxMessages: 1
    QueueName: "staging-custom-delay-intermediate-queue-2.fifo"
    RetryStrategy:
      RegularInterval:
        Interval: 1
        MaxAttempts: 10
        TimeUnit: "Second"

  IntermediateQueue3SqsPublisher:
    QueueName: "staging-custom-delay-intermediate-queue-3.fifo"
    IsDestQueueFifo: true
    FifoMsgGroupId: "IntermediateQueue3FifoMsgGroupId1"
  IntermediateQueue3SqsSubscriber:
    StartOnServerStart: true
    NumWorkers: 1
    PollingDuration: 20
    MaxMessages: 1
    QueueName: "staging-custom-delay-intermediate-queue-3.fifo"
    RetryStrategy:
      RegularInterval:
        Interval: 1
        MaxAttempts: 10
        TimeUnit: "Second"

  # common DLQ subscriber for all intermediate queues
  IntermediateQueueDLQSubscriber:
    StartOnServerStart: true
    NumWorkers: 1
    PollingDuration: 20
    MaxMessages: 1
    QueueName: "staging-dlq-custom-delay-intermediate-queue-1.fifo"
    RetryStrategy:
      RegularInterval:
        Interval: 1
        MaxAttempts: 10
        TimeUnit: "Second"

DestDelayQueueSqsPublisherConfig:
  RewardsProcessingDelaySqsPublisher:
    QueueName: "staging-rewards-processing-delay-queue"
  InAppReferralNotificationsDelaySqsPublisher:
    QueueName: "staging-inappreferral-notifications-delay-queue"
  WealthOnboardingStepsRetrySqsCustomDelayPublisher:
    QueueName: "staging-wo-steps-retry-delay-queue"
  ExchangerOrderNotificationDelaySqsPublisher:
    QueueName: "staging-casper-exchanger-order-notification-delay-queue"
  SalaryProgramNotificationDelaySqsPublisher:
    QueueName: "staging-salaryprogram-notification-delay-queue"
  RewardsClaimRewardEventDelaySqsPublisher:
    QueueName: "staging-rewards-claim-reward-event-queue"
  RewardsDataCollectorEventDelaySqsPublisher:
    QueueName: "staging-rewards-data-collector-queue"
  ReferralsEligibilityCollectedDataSqsPublisher:
    QueueName: "staging-referrals-eligibility-datacollector-queue"
  ReferralsNotificationSqsPublisher:
    QueueName: "staging-referrals-notification-queue"
  NudgeEntryEventDelaySqsPublisher:
    QueueName: "staging-nudge-entry-event-queue"
  RewardsRewardClawbackEventDelaySqsPublisher:
    QueueName: "staging-rewards-reward-clawback-event-collector-queue"
  RewardsRewardUnlockerSqsCustomDelayPublisher:
    QueueName: "staging-rewards-reward-unlocker-queue"
  RewardsRewardExpirySqsCustomDelayPublisher:
    QueueName: "staging-rewards-reward-expiry-queue"
  SalaryProgramSalaryStatusUpdateSqsCustomDelayPublisher:
    QueueName: "staging-salaryprogram-salary-status-update-event-consumer-queue"
  InvestmentEventBasedNotificationsSqsCustomDelayPublisher:
    QueueName: "staging-investment-event-based-notifications-queue"
  RewardsNotificationEventSqsCustomDelayPublisher:
    QueueName: "staging-rewards-notification-queue"

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true
