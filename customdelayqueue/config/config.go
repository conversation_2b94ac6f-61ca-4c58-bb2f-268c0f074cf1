package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"

	"github.com/epifi/be-common/pkg/cfg"
)

var (
	once   sync.Once
	config *Config
	err    error
)

var (
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	configDirPath := testEnvConfigDir()
	conf := &Config{}
	k, _, err := cfg.LoadConfigUsingKoanf(*configDirPath, cfg.CUSTOM_DELAY_QUEUE_SERVICE)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	if err := readAndSetEnv(conf); err != nil {
		return nil, fmt.Errorf("failed to read and set env var: %w", err)
	}

	return conf, nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Port = intVal
	}

	if val, ok := os.LookupEnv("REDIS_HOST"); ok {
		c.RedisOptions.Addr = val
	}

	return nil
}

func testEnvConfigDir() *string {
	configPath := filepath.Join(b, "..", "values")
	return &configPath
}

type Config struct {
	Application                      *application
	Server                           *server
	Aws                              *aws
	DelayQueueStackConfig            *DelayQueueStackConfig
	DestDelayQueueSqsPublisherConfig *DestDelayQueueSqsPublisherConfig
	RedisOptions                     *cfg.RedisOptions
	Tracing                          *cfg.Tracing
	Profiling                        *cfg.Profiling
}

type DelayQueueStackConfig struct {
	// orchestrator publisher and subscriber
	OrchestratorQueueSqsPublisher  *cfg.SqsPublisher
	OrchestratorQueueSqsSubscriber *cfg.SqsSubscriber
	// intermediate queue 1 publisher and subscriber
	IntermediateQueue1SqsPublisher  *cfg.SqsPublisher
	IntermediateQueue1SqsSubscriber *cfg.SqsSubscriber
	// intermediate queue 2 publisher and subscriber
	IntermediateQueue2SqsPublisher  *cfg.SqsPublisher
	IntermediateQueue2SqsSubscriber *cfg.SqsSubscriber
	// intermediate queue 2 publisher and subscriber
	IntermediateQueue3SqsPublisher  *cfg.SqsPublisher
	IntermediateQueue3SqsSubscriber *cfg.SqsSubscriber
	// common DLQ subscriber for all intermediate queues
	IntermediateQueueDLQSubscriber *cfg.SqsSubscriber
}

// SQS Publisher config for all the destination queues supported by custom delay queue.
type DestDelayQueueSqsPublisherConfig struct {
	RewardsProcessingDelaySqsPublisher                       *cfg.SqsPublisher
	InAppReferralNotificationsDelaySqsPublisher              *cfg.SqsPublisher
	WealthOnboardingStepsRetrySqsCustomDelayPublisher        *cfg.SqsPublisher
	ExchangerOrderNotificationDelaySqsPublisher              *cfg.SqsPublisher
	SalaryProgramNotificationDelaySqsPublisher               *cfg.SqsPublisher
	RewardsClaimRewardEventDelaySqsPublisher                 *cfg.SqsPublisher
	RewardsDataCollectorEventDelaySqsPublisher               *cfg.SqsPublisher
	ReferralsEligibilityCollectedDataSqsPublisher            *cfg.SqsPublisher
	ReferralsNotificationSqsPublisher                        *cfg.SqsPublisher
	NudgeEntryEventDelaySqsPublisher                         *cfg.SqsPublisher
	RewardsRewardClawbackEventDelaySqsPublisher              *cfg.SqsPublisher
	RewardsRewardUnlockerSqsCustomDelayPublisher             *cfg.SqsPublisher
	RewardsRewardExpirySqsCustomDelayPublisher               *cfg.SqsPublisher
	SalaryProgramSalaryStatusUpdateSqsCustomDelayPublisher   *cfg.SqsPublisher
	InvestmentEventBasedNotificationsSqsCustomDelayPublisher *cfg.SqsPublisher
	RewardsNotificationEventSqsCustomDelayPublisher          *cfg.SqsPublisher
}

type application struct {
	Environment   string
	Name          string
	IsSecureRedis bool
}

type server struct {
	Port            int
	HealthCheckPort int
}

type aws struct {
	Region string
}
