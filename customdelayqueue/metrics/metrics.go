package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
)

var MetricsRecorder = NewCustomDelayQueueMetricsRecorder()

// CustomDelayQueueMetrics represents a collection of metrics to be registered on a
// Prometheus metrics registry for CustomDelayQueue.
type CustomDelayQueueMetrics struct {
	// histogram to measure additional delay (than required) introduced by the intermediate queue
	intermediateQueueAdditionalDelay *prometheus.HistogramVec
}

func NewCustomDelayQueueMetricsRecorder() *CustomDelayQueueMetrics {
	customDelayQueueMetrics := &CustomDelayQueueMetrics{
		intermediateQueueAdditionalDelay: prometheus.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "cdq_intermediate_queue_additional_delay_seconds",
			Help:    "additional delay (than required) introduced by the intermediate queue",
			Buckets: prometheus.ExponentialBuckets(5, 2, 11),
		}, []string{"queue_name"}),
	}
	// register all metrics
	prometheus.MustRegister(customDelayQueueMetrics.intermediateQueueAdditionalDelay)
	initialize(customDelayQueueMetrics)
	return customDelayQueueMetrics
}

// initialise metrics to 0
func initialize(metrics *CustomDelayQueueMetrics) {
	metrics.intermediateQueueAdditionalDelay.WithLabelValues("queue_1")
}

func (m *CustomDelayQueueMetrics) RecordIntermediateQueueAdditionalDelay(additionalDelayInSecs float64) {
	// TODO (utkarsh) : use actual queue name as label if we starting getting the queue name in intermediate queue consumer.
	m.intermediateQueueAdditionalDelay.WithLabelValues("queue_1").Observe(additionalDelayInSecs)
}
