package customdelayqueue

import (
	"context"
	"time"

	awsSqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/async/goroutine"

	"github.com/epifi/be-common/pkg/aws/v2/sqs"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"

	cdqConsumerPb "github.com/epifi/gamma/api/customdelayqueue/consumer"
	cdqConfig "github.com/epifi/gamma/customdelayqueue/config"
)

const (
	intermediateSubscriber1LockKey   = "cdq:intermediateSubscriber1LockKey"
	intermediateSubscriber2LockKey   = "cdq:intermediateSubscriber2LockKey"
	intermediateSubscriber3LockKey   = "cdq:intermediateSubscriber3LockKey"
	intermediateDLQSubscriberLockKey = "cdq:intermediateDLQSubscriberLockKey"
	lockLeaseDuration                = 6 * time.Second
)

// BootstrapIntermediateQueueSubscribers starts all the intermediate queue subscribers in separate goroutines. We require only single instance
// of the each intermediate queue subscriber to be running at a given time (as per custom delay queue design) and for achieving this we are
// using distributed locks. The method tries to acquire lock over a given key and if the lock was successfully acquired then only it starts
// the corresponding intermediate queue subscriber, else it waits for lock to be released and then retries acquiring the lock after sometime.
func (s *Service) BootstrapIntermediateQueueSubscribers(ctx context.Context, conf *cdqConfig.DelayQueueStackConfig, sqsClient *awsSqs.Client, subscriberService cdqConsumerPb.IntermediateConsumerServer, redisClient *redis.Client) {
	s.runSubscriberWithLock(ctx, intermediateSubscriber1LockKey, sqsClient, conf.IntermediateQueue1SqsSubscriber, subscriberService, cdqConsumerPb.IntermediateConsumerProcessEventMethodName, redisClient)
	s.runSubscriberWithLock(ctx, intermediateSubscriber2LockKey, sqsClient, conf.IntermediateQueue2SqsSubscriber, subscriberService, cdqConsumerPb.IntermediateConsumerProcessEventMethodName, redisClient)
	s.runSubscriberWithLock(ctx, intermediateSubscriber3LockKey, sqsClient, conf.IntermediateQueue3SqsSubscriber, subscriberService, cdqConsumerPb.IntermediateConsumerProcessEventMethodName, redisClient)
	s.runSubscriberWithLock(ctx, intermediateDLQSubscriberLockKey, sqsClient, conf.IntermediateQueueDLQSubscriber, subscriberService, cdqConsumerPb.IntermediateConsumerProcessDlqEventMethodName, redisClient)
}

// runSubscriberWithLock starts the subscriber in a separate goroutine taking a distributed lock over the given key. If the lock is successfully
// acquired then only the subscriber is started and if the acquired lock is released then the subscriber is stopped to prevent multiple
// instances of subscriber running at the same time.
func (s *Service) runSubscriberWithLock(ctx context.Context, lockKey string, sqsClient *awsSqs.Client, subscriberCfg *cfg.SqsSubscriber, subscriberService cdqConsumerPb.IntermediateConsumerServer, serviceMethodName string, redisClient *redis.Client) {
	if subscriberCfg.StartOnServerStart {
		goroutine.RunWithCtx(ctx, func(ctx context.Context) {
			for {
				// get automatic refresh lock to avoid manual refresh as much as possible.
				lock, err := s.lockManager.GetLockWithRegularRefresh(ctx, lockKey, lockLeaseDuration)
				if err != nil {
					<-time.After(lockLeaseDuration / 2)
					continue
				}
				// lock acquired, start the subscriber
				subscriber, err := sqs.NewSubscriberWithConfig(ctx, subscriberCfg, sqsClient, queue.NewDefaultMessage(), redisClient)
				if err != nil {
					panic(err)
				}
				cdqConsumerPb.RegisterServiceToSubscriber(subscriber, subscriberService, serviceMethodName)
				subscriber.StartWorkers()
				logger.Info(ctx, "started subscriber", zap.String("queue_name", subscriberCfg.GetQueueName()))

				// if lock failed to get automatically refreshed, stop the subscriber and retry acquiring the lock in next iteration
				<-lock.RegularRefreshFailedSignal(ctx)
				subscriber.StopWorkers()
				logger.Info(ctx, "stopped subscriber", zap.String("queue_name", subscriberCfg.GetQueueName()))
			}
		})
	}
}
