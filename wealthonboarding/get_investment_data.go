package wealthonboarding

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	cmap "github.com/orcaman/concurrent-map"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	employmentPb "github.com/epifi/gamma/api/employment"
	types "github.com/epifi/gamma/api/typesv2"
	wealthVendorPb "github.com/epifi/gamma/api/vendors/wealth"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	userPb "github.com/epifi/gamma/api/wealthonboarding/user"
	"github.com/epifi/gamma/wealthonboarding/helper"
)

// GetInvestmentData RPC returns the investment data on a best effort basis for a given list of actors
// Hence the RPC status always returned is StatusOk
func (s *Service) GetInvestmentData(ctx context.Context, req *woPb.GetInvestmentDataRequest) (*woPb.GetInvestmentDataResponse, error) {
	invData := s.getInvestmentData(ctx, req.GetActorIds())
	return &woPb.GetInvestmentDataResponse{
		Status:               rpc.StatusOk(),
		InvestmentDetailInfo: invData,
	}, nil
}

//nolint:funlen
func (s *Service) GetInvestmentDataV2(ctx context.Context, req *woPb.GetInvestmentDataV2Request) (*woPb.GetInvestmentDataV2Response, error) {
	investmentDetailInfo := cmap.New()
	grp, grpCtx := errgroup.WithContext(ctx)

	for _, actor := range req.GetActorIds() {
		actorId := actor // Create local copy to avoid closure capture issue
		grp.Go(func() error {
			preInvOnbDetails, err := s.triggerAndGetPreInvestmentOnboardingDetails(grpCtx, actorId)
			if err != nil {
				logger.Error(grpCtx, "error getting pre-investment onboarding details", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
				investmentDetailInfo.Set(actorId, getTransientErrorResForPreInvestmentDetailV2())
				return nil
			}
			if !preInvOnbDetails.GetMetadata().GetIsFreshKra() {
				onboardingStepDetailsResp, odErr := s.onboardingStepDetailsDao.GetByOnboardingDetailsIdAndStep(grpCtx, preInvOnbDetails.GetId(), woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC)
				if odErr != nil {
					logger.Error(grpCtx, "error while GetByOnboardingDetailsIdAndStep", zap.Error(odErr))
					investmentDetailInfo.Set(actorId, getTransientErrorResForPreInvestmentDetailV2())
					return nil
				}
				// sub status with dob mismatch leads to data not being present at wealthonboarding end due to incorrect request to kra
				if onboardingStepDetailsResp.GetSubStatus() == woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_EMPTY_DOWNLOAD_API_RESPONSE &&
					onboardingStepDetailsResp.GetStatus() != woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED {
					investmentDetailInfo.Set(actorId, s.retryOnboardingForDOBMismatches(grpCtx, actorId, preInvOnbDetails, onboardingStepDetailsResp))
					return nil
				}
			}

			if preInvOnbDetails.GetCurrentStep() == woPb.OnboardingStep_ONBOARDING_STEP_UPLOAD_DOCKET {
				investmentDetailInfo.Set(actorId, s.retryOnboardingIfAddressProofValidationRequired(grpCtx, actorId, preInvOnbDetails))
				return nil
			}

			preInvestmentDetails, err := s.getPreInvestmentDetail(grpCtx, actorId)
			if err != nil {
				logger.Error(grpCtx, "error while getPreInvestmentDetail", zap.Error(err))
				investmentDetailInfo.Set(actorId, getTransientErrorResForPreInvestmentDetailV2())
				return nil
			}

			investmentDetailInfo.Set(actorId, &woPb.PreInvestmentDetailsV2{
				InvestmentDetails: preInvestmentDetails,
			})
			return nil
		})
	}
	if err := grp.Wait(); err != nil {
		// execution shouldn't reach here as every early return gives nil
		return &woPb.GetInvestmentDataV2Response{
			Status: rpc.StatusInternal(),
		}, nil
	}

	// Convert concurrent map to regular map for response
	result := make(map[string]*woPb.PreInvestmentDetailsV2)
	for _, actorId := range req.GetActorIds() {
		if detail, ok := investmentDetailInfo.Get(actorId); ok {
			result[actorId] = detail.(*woPb.PreInvestmentDetailsV2)
		} else {
			logger.Info(ctx, "some issue occurred while fetching pre investment details for actor_id", zap.String(logger.ACTOR_ID_V2, actorId))
			result[actorId] = nil
		}
	}

	return &woPb.GetInvestmentDataV2Response{
		Status:               rpc.StatusOk(),
		InvestmentDetailInfo: result,
	}, nil
}

// getInvestmentData fetches the requested detail for an actor in async go routine and returns an actor id to Investment detail map
// it returns the data for an actor on best effort basis and silent logs the error instead of propagating to the parent func
func (s *Service) getInvestmentData(ctx context.Context, actorIds []string) map[string]*woPb.PreInvestmentDetail {
	grp, grpCtx := errgroup.WithContext(ctx)
	invData := cmap.New()
	for _, actor := range actorIds {
		actorId := actor // Create local copy to avoid closure capture issue
		grp.Go(func() error {
			_, err := s.triggerAndGetPreInvestmentOnboardingDetails(grpCtx, actorId)
			if err != nil {
				logger.Error(grpCtx, "error getting pre-investment onboarding details", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
				return nil
			}
			res, err := s.getPreInvestmentDetail(grpCtx, actorId)
			if err != nil {
				logger.Error(grpCtx, "error occurred while fetching investment detail for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
				return nil
			}
			invData.Set(actorId, res)
			return nil
		})
	}

	res := make(map[string]*woPb.PreInvestmentDetail)

	if err := grp.Wait(); err != nil {
		// execution shouldn't reach here as every early return gives nil
		return res
	}

	for _, actorId := range actorIds {
		detail, ok := invData.Get(actorId)
		if !ok {
			logger.Info(ctx, "some issue occurred while fetching pre investment details for actor_id", zap.String(logger.ACTOR_ID_V2, actorId))
			res[actorId] = nil
			continue
		}
		res[actorId] = detail.(*woPb.PreInvestmentDetail)
	}
	return res
}

// gets the pre-investment onboarding details if already present, else triggers pre-investment onboarding for actor first and then gets the details
func (s *Service) triggerAndGetPreInvestmentOnboardingDetails(ctx context.Context, actorId string) (*woPb.OnboardingDetails, error) {
	var (
		preInvOnbDetails *woPb.OnboardingDetails
		err, err2        error
	)
	preInvOnbDetails, err = s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, actorId, woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT)
	if err != nil {
		if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error getting pre-investment onb details", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
			return nil, err
		} else {
			nextOnbStepRes, nErr := s.GetNextOnboardingStep(ctx, &woPb.GetNextOnboardingStatusRequest{
				ActorId:        actorId,
				WealthFlow:     woPb.WealthFlow_WEALTH_FLOW_INVESTMENT,
				OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT,
			})
			if te := epifigrpc.RPCError(nextOnbStepRes, nErr); te != nil {
				logger.Error(ctx, "error while calling GetNextOnboardingStep", zap.Error(te),
					zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.WEALTH_ONB_FLOW, woPb.WealthFlow_WEALTH_FLOW_INVESTMENT.String()), zap.String(logger.WEALTH_ONB_TYPE, woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT.String()))
				return nil, te
			}
			if nextOnbStepRes.GetNextStep() != nil {
				logger.Info(ctx, "received non-nil deeplink from orchestrator", zap.String(logger.SCREEN, nextOnbStepRes.GetNextStep().GetScreen().String()), zap.String(logger.ACTOR_ID_V2, actorId))
			}
			preInvOnbDetails, err2 = s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, actorId, woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT)
			if err2 != nil {
				logger.Error(ctx, "error getting pre-investment onb details even after triggering orchestrator", zap.Error(err2), zap.String(logger.ACTOR_ID_V2, actorId))
				return nil, err2
			}
			return preInvOnbDetails, nil
		}
	}
	return preInvOnbDetails, nil
}

// For DOB mismatch cases, if user's KRA download process is not in progress, first bring it to in progress
// and then call next onboarding step, which would notify user to upload PAN for which we can get the correct DOB
func (s *Service) retryOnboardingForDOBMismatches(ctx context.Context, actorId string,
	onbDetails *woPb.OnboardingDetails, onbStepDetails *woPb.OnboardingStepDetails) *woPb.PreInvestmentDetailsV2 {
	// change status to in progress if the status is manual intervention or future scope
	if onbStepDetails.GetStatus() == woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED ||
		onbStepDetails.GetStatus() == woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE {
		if txnErr := s.idempotentTxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
			// update onboarding status
			onbDetails.Status = woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS
			uErr := s.onboardingDetailsDao.Update(txnCtx, onbDetails, []woPb.OnboardingDetailsFieldMask{
				woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_STATUS,
			})
			if uErr != nil {
				return errors.Wrap(uErr, "error while updating onboarding status")
			}

			// marking current step execution stale
			onbStepDetails.StaledAt = timestamppb.Now()
			uErr = s.onboardingStepDetailsDao.UpdateById(txnCtx, onbStepDetails, []woPb.OnboardingStepDetailsFieldMask{
				woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_STALED_AT,
			})
			if uErr != nil {
				return errors.Wrap(uErr, "error while updating onboarding step status")
			}
			return nil
		}); txnErr != nil {
			logger.Error(ctx, "error in txn", zap.Error(txnErr))
			return getTransientErrorResForPreInvestmentDetailV2()
		}
	}
	nRes, nErr := s.GetNextOnboardingStep(ctx, &woPb.GetNextOnboardingStatusRequest{
		ActorId:        actorId,
		WealthFlow:     woPb.WealthFlow_WEALTH_FLOW_INVESTMENT,
		OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT,
	})
	if te := epifigrpc.RPCError(nRes, nErr); te != nil {
		logger.Error(ctx, "error in GetNextOnboardingStep", zap.Error(te))
		return getTransientErrorResForPreInvestmentDetailV2()
	}

	// if orchestration doesn't return a deeplink, assume that user got validated in above call, and allow user to invest
	if nRes.GetNextStep() == nil {
		preInvestmentDetails, preInvDetailsErr := s.getPreInvestmentDetail(ctx, actorId)
		if preInvDetailsErr != nil {
			return getTransientErrorResForPreInvestmentDetailV2()
		}
		return &woPb.PreInvestmentDetailsV2{InvestmentDetails: preInvestmentDetails}
	}

	return &woPb.PreInvestmentDetailsV2{
		FailureType:   woPb.FailureType_FAILURE_TYPE_NOT_RETRYABLE,
		FailureReason: woPb.FailureReason_FAILURE_REASON_DOB_MISMATCH,
		NextStep:      nRes.GetNextStep(),
	}
}

func (s *Service) retryOnboardingIfAddressProofValidationRequired(ctx context.Context, actorId string, preInvOnbDetails *woPb.OnboardingDetails) *woPb.PreInvestmentDetailsV2 {
	isValidationRequired, validationErr := helper.IsAddressProofValidationRequired(ctx, preInvOnbDetails)
	if validationErr != nil {
		return getTransientErrorResForPreInvestmentDetailV2()
	}
	if !isValidationRequired {
		preInvestmentDetails, preInvDetailsErr := s.getPreInvestmentDetail(ctx, actorId)
		if preInvDetailsErr != nil {
			return getTransientErrorResForPreInvestmentDetailV2()
		}
		return &woPb.PreInvestmentDetailsV2{InvestmentDetails: preInvestmentDetails}
	}
	nextOnbStepRes, nextOnbStepErr := s.GetNextOnboardingStep(ctx, &woPb.GetNextOnboardingStatusRequest{
		ActorId:        actorId,
		WealthFlow:     woPb.WealthFlow_WEALTH_FLOW_INVESTMENT,
		OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT,
	})
	if te := epifigrpc.RPCError(nextOnbStepRes, nextOnbStepErr); te != nil {
		logger.Error(ctx, "error in getting next onboarding step", zap.Error(te))
		return getTransientErrorResForPreInvestmentDetailV2()
	}
	// if orchestration doesn't return a deeplink, assume that user got validated in above call, and allow user to invest
	if nextOnbStepRes.GetNextStep() == nil {
		preInvestmentDetails, preInvDetailsErr := s.getPreInvestmentDetail(ctx, actorId)
		if preInvDetailsErr != nil {
			return getTransientErrorResForPreInvestmentDetailV2()
		}
		return &woPb.PreInvestmentDetailsV2{InvestmentDetails: preInvestmentDetails}
	}
	return &woPb.PreInvestmentDetailsV2{
		FailureType:   woPb.FailureType_FAILURE_TYPE_NOT_RETRYABLE,
		FailureReason: woPb.FailureReason_FAILURE_REASON_AADHAAR_POA_NOT_VALIDATED,
		NextStep:      nextOnbStepRes.GetNextStep(),
	}
}

func getTransientErrorResForPreInvestmentDetailV2() *woPb.PreInvestmentDetailsV2 {
	return &woPb.PreInvestmentDetailsV2{
		FailureType:   woPb.FailureType_FAILURE_TYPE_RETRYABLE,
		FailureReason: woPb.FailureReason_FAILURE_REASON_UNSPECIFIED,
	}
}

func (s *Service) getPreInvestmentDetail(ctx context.Context, actorId string) (*woPb.PreInvestmentDetail, error) {
	// first check if data is present in wealth user db
	user, err := s.userService.GetByActorId(ctx, actorId)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Info(ctx, "user data not present in user table", zap.String(logger.ACTOR_ID_V2, actorId))
		} else {
			return nil, errors.Wrap(err, "error while fetching wealth user data")
		}
	}
	// if not present in wealth user db, get from wealth onboarding details. (currently this happens for users who onboarded before user service integration)
	od, err := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, actorId, woPb.OnboardingType_ONBOARDING_TYPE_WEALTH)
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching wealth onb details")
	}
	if od.GetStatus() != woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED {
		return nil, errors.Errorf("wealth onboarding for actor is not completed yet, onb status : %v", od.GetStatus())
	}
	piod, err := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, actorId, woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT)
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching pre investment onb details")
	}
	iKraPanDetails := piod.GetMetadata().GetKraData().GetDownloadedData().GetPanDetails()
	empData := getEmploymentData(piod)
	nationality := getNationality(od, iKraPanDetails, user)
	taxResStatus := getTaxResidentialStatus(od, iKraPanDetails, user)
	signatureDoc, signatureErr := s.getSignature(ctx, od, user)
	if err != nil {
		return nil, errors.Wrap(signatureErr, "error while getting signature doc")
	}
	res := &woPb.PreInvestmentDetail{
		CustomerName:              getName(od, iKraPanDetails, user),
		Dob:                       getDob(od, user),
		AddressType:               types.AddressType_PERMANENT,
		Address:                   getAddress(od, user, piod),
		Nationality:               nationality,
		Pan:                       getPanDetails(od, user),
		EmploymentData:            empData,
		IncomeSlab:                getIncomeSlab(od, user, piod),
		PoliticallyExposedStatus:  getPoliticallyExposedStatus(od, user),
		CustomerIpAddress:         helper.ParseUsersIp(getIpAddress(od, user)),
		TaxResidentialStatus:      taxResStatus,
		EmailId:                   getEmail(od, user),
		Gender:                    getGender(od, user, piod),
		MobileNo:                  getPhoneNumber(od, user),
		CustomerConsent:           true,
		BankDetails:               getBankDetails(od, piod, user),
		Signature:                 signatureDoc,
		NomineeDeclarationDetails: getNomineeDeclarationDetails(od, user, piod),
	}
	nominees := getNominees(od, user)
	if len(nominees) == 1 {
		// TODO(ismail): figure out fetching single nominee of savings account
		res.Nominee = nominees[0]
	}

	// address will be populated correctly if we have the kyc data for investment
	if res.GetAddress().GetAdministrativeArea() == "" {
		logger.Error(ctx, "state in address is empty", zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, errors.New("state in address is empty")
	}

	return res, nil
}

func getIncomeSlab(wOnb *woPb.OnboardingDetails, user *userPb.User, invOnb *woPb.OnboardingDetails) types.IncomeSlab {
	if invOnb.GetMetadata().GetIsFreshKra() {
		if user.GetPersonalDetails().GetIncomeSlab() != types.IncomeSlab_INCOME_SLAB_UNSPECIFIED {
			return user.GetPersonalDetails().GetIncomeSlab()
		} else {
			return wOnb.GetMetadata().GetPersonalDetails().GetIncomeSlab()
		}
	}
	return invOnb.GetMetadata().GetKraData().GetDownloadedData().GetPanDetails().GetIncomeSlab()
}

func getGender(wOnb *woPb.OnboardingDetails, user *userPb.User, invOnb *woPb.OnboardingDetails) types.Gender {
	if invOnb.GetMetadata().GetIsFreshKra() {
		if user.GetPersonalDetails().GetGender() != types.Gender_GENDER_UNSPECIFIED {
			return user.GetPersonalDetails().GetGender()
		} else {
			return wOnb.GetMetadata().GetPersonalDetails().GetGender()
		}
	}
	return invOnb.GetMetadata().GetKraData().GetDownloadedData().GetPanDetails().GetGen()
}

func getAddress(wOnb *woPb.OnboardingDetails, user *userPb.User, invOnb *woPb.OnboardingDetails) *types.PostalAddress {
	if invOnb.GetMetadata().GetIsFreshKra() {
		switch {
		case user.GetPersonalDetails().GetPermanentAddress() != nil:
			return user.GetPersonalDetails().GetPermanentAddress()
		case wOnb.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData() != nil:
			return wOnb.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData().GetAddress()
		default:
			return wOnb.GetMetadata().GetCkycData().GetDownloadData().GetPersonalDetails().GetPermanentAddress()
		}
	}
	return invOnb.GetMetadata().GetKraData().GetDownloadedData().GetPanDetails().GetPermAddress()
}

func getName(od *woPb.OnboardingDetails, kraDetails *woPb.KraDownloadedData_DownloadedPanDetails, user *userPb.User) *commontypes.Name {
	if kraDetails.GetName() != nil {
		return kraDetails.GetName()
	}
	if user.GetPersonalDetails().GetName() != nil {
		return user.GetPersonalDetails().GetName()
	}
	return od.GetMetadata().GetPersonalDetails().GetName()
}

func getDob(od *woPb.OnboardingDetails, user *userPb.User) *date.Date {
	if user.GetPersonalDetails().GetDob() != nil {
		return user.GetPersonalDetails().GetDob()
	}
	return od.GetMetadata().GetPersonalDetails().GetDob()
}

func getPanDetails(od *woPb.OnboardingDetails, user *userPb.User) *types.DocumentProof {
	if user.GetPersonalDetails().GetPanDetails() != nil {
		return user.GetPersonalDetails().GetPanDetails()
	}
	return od.GetMetadata().GetPanDetails()
}

func getPoliticallyExposedStatus(od *woPb.OnboardingDetails, user *userPb.User) types.PoliticallyExposedStatus {
	if user.GetPersonalDetails().GetPoliticallyExposedStatus() != types.PoliticallyExposedStatus_POLITICALLY_EXPOSED_STATUS_UNSPECIFIED {
		return user.GetPersonalDetails().GetPoliticallyExposedStatus()
	}
	return od.GetMetadata().GetPersonalDetails().GetPoliticallyExposedStatus()
}

func getIpAddress(od *woPb.OnboardingDetails, user *userPb.User) string {
	if user.GetMetadata().GetCustomerIpAddress() != "" {
		return user.GetMetadata().GetCustomerIpAddress()
	}
	return od.GetMetadata().GetCustomerIpAddress()
}

func getEmail(od *woPb.OnboardingDetails, user *userPb.User) string {
	if user.GetPersonalDetails().GetEmail() != "" {
		return user.GetPersonalDetails().GetEmail()
	}
	return od.GetMetadata().GetPersonalDetails().GetEmail()
}

func getPhoneNumber(od *woPb.OnboardingDetails, user *userPb.User) *commontypes.PhoneNumber {
	if user.GetPersonalDetails().GetPhoneNumber() != nil {
		return user.GetPersonalDetails().GetPhoneNumber()
	}
	return od.GetMetadata().GetPersonalDetails().GetPhoneNumber()
}

func getNominees(od *woPb.OnboardingDetails, user *userPb.User) []*types.Nominee {
	if user.GetPersonalDetails().GetNominees() != nil {
		return user.GetPersonalDetails().GetNominees()
	}
	return od.GetMetadata().GetPersonalDetails().GetNominees()
}

// ideally nominee details should have been kept at one place only
// but currently wealth onboarding is designed such that it copies data from one record to another multiple times
// resulting in these data not being in sync, hence we have to provide guardrails when fetching this data
func getNomineeDeclarationDetails(wod *woPb.OnboardingDetails, user *userPb.User, piod *woPb.OnboardingDetails) *woPb.NomineeDeclarationDetails {
	if wod.GetMetadata().GetPersonalDetails().GetNomineeDeclarationDetails().GetChoice() != commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
		return wod.GetMetadata().GetPersonalDetails().GetNomineeDeclarationDetails()
	}
	if user.GetPersonalDetails().GetNomineeDeclarationDetails().GetChoice() != commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
		return user.GetPersonalDetails().GetNomineeDeclarationDetails()
	}
	if piod.GetMetadata().GetPersonalDetails().GetNomineeDeclarationDetails().GetChoice() != commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
		return piod.GetMetadata().GetPersonalDetails().GetNomineeDeclarationDetails()
	}

	// default to user saved nominee if there is only one such nominee created by user
	userSavedNominees := getNominees(wod, user)
	if len(userSavedNominees) == 1 {
		return &woPb.NomineeDeclarationDetails{
			Choice:                commontypes.BooleanEnum_TRUE,
			WealthAccountNominees: []*woPb.WealthAccountNomineeInfo{{NomineeId: userSavedNominees[0].GetId(), PercentageShare: 100}},
		}
	}
	return nil
}

// getTaxResidentialStatus returns the tax residential status of the user, it is either fetched from user data or the wealth onboarding details or from kra downloaded details
func getTaxResidentialStatus(onbDetails *woPb.OnboardingDetails, kraDetails *woPb.KraDownloadedData_DownloadedPanDetails, user *userPb.User) types.ResidentialStatus {
	if kraDetails.GetResStatus() != types.ResidentialStatus_RESIDENTIAL_STATUS_UNSPECIFIED {
		return kraDetails.GetResStatus()
	}
	if user.GetPersonalDetails().GetResidentialStatus() != types.ResidentialStatus_RESIDENTIAL_STATUS_UNSPECIFIED {
		return user.GetPersonalDetails().GetResidentialStatus()
	}
	return onbDetails.GetMetadata().GetPersonalDetails().GetResidentialStatus()
}

// getNationality returns the nationality of the user, it is either fetched from user data the wealth onboarding details or from kra downloaded details
func getNationality(onbDetails *woPb.OnboardingDetails, panDetails *woPb.KraDownloadedData_DownloadedPanDetails, user *userPb.User) types.Nationality {
	if helper.IsIndianAsPerKraPanDetails(panDetails) {
		return types.Nationality_NATIONALITY_INDIAN
	}
	if panDetails.GetNationality() != types.Nationality_NATIONALITY_UNSPECIFIED {
		return panDetails.GetNationality()
	}
	if user.GetPersonalDetails().GetNationality() != types.Nationality_NATIONALITY_UNSPECIFIED {
		return user.GetPersonalDetails().GetNationality()
	}
	return onbDetails.GetMetadata().GetPersonalDetails().GetNationality()
}

// getBankDetails returns the bank details for a user, it is either fetched from user data or the wealth onboarding or pre investment onboarding phase
func getBankDetails(od *woPb.OnboardingDetails, piod *woPb.OnboardingDetails, user *userPb.User) *woPb.BankDetails {
	if user.GetPersonalDetails().GetBankDetails() != nil {
		return user.GetPersonalDetails().GetBankDetails()
	}
	if od.GetMetadata().GetBankDetails() != nil {
		return od.GetMetadata().GetBankDetails()
	}
	return piod.GetMetadata().GetBankDetails()
}

// getEmploymentData returns employment data of user based on data_collection or download_kra doc step
func getEmploymentData(piod *woPb.OnboardingDetails) *woPb.EmploymentData {
	// check if employment data is present from the data collection step
	collectedEmpData := piod.GetMetadata().GetEmploymentData()
	if collectedEmpData.GetEmploymentType() != employmentPb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED {
		return collectedEmpData
	}
	kraEmpData := &woPb.EmploymentData{}
	if !piod.GetMetadata().GetIsFreshKra() {
		// fetch employment type from kra occupation
		switch piod.GetMetadata().GetKraData().GetDownloadedData().GetPanDetails().GetOcc() {
		case wealthVendorPb.KraOccupation_KRA_OCCUPATION_PRIVATE_SECTOR_SERVICE, wealthVendorPb.KraOccupation_KRA_OCCUPATION_PUBLIC_SECTOR,
			wealthVendorPb.KraOccupation_KRA_OCCUPATION_GOVERNMENT_SERVICE, wealthVendorPb.KraOccupation_KRA_OCCUPATION_PUBLIC_SECTOR_OR_GOVERNMENT_SERVICE:
			kraEmpData.EmploymentType = employmentPb.EmploymentType_SALARIED
		case wealthVendorPb.KraOccupation_KRA_OCCUPATION_HOUSEWIFE:
			kraEmpData.EmploymentType = employmentPb.EmploymentType_OTHERS
		default:
			kraEmpData.EmploymentType = employmentPb.EmploymentType_SELF_EMPLOYED
		}
	} else {
		// as per discussion these fresh kra users would be limited for which we haven't collected employment data
		// falling back to salaried for such users
		kraEmpData.EmploymentType = employmentPb.EmploymentType_SALARIED
	}
	return kraEmpData
}

func (s *Service) getSignature(ctx context.Context, od *woPb.OnboardingDetails, user *userPb.User) (*types.DocumentProof, error) {
	var signature *types.DocumentProof
	switch {
	case user.GetPersonalDetails().GetSignature() != nil:
		signature = user.GetPersonalDetails().GetSignature()
	case od.GetMetadata().GetPersonalDetails().GetSignature() != nil:
		signature = od.GetMetadata().GetPersonalDetails().GetSignature()
	default:
		// no signature found for user, return nil
		return nil, nil
	}
	return s.docHelper.DownloadDoc(ctx, signature)
}
