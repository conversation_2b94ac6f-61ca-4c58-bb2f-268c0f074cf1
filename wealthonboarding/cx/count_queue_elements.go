package cx

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	pqPb "github.com/epifi/gamma/api/persistentqueue"
	woCxPb "github.com/epifi/gamma/api/wealthonboarding/cx"
	"github.com/epifi/be-common/pkg/logger"
)

func (s *Service) CountQueueElements(ctx context.Context, req *woCxPb.CountQueueElementsRequest) (*woCxPb.CountQueueElementsResponse, error) {
	if req.GetPayloadType() == pqPb.PayloadType_PAYLOAD_TYPE_UNSPECIFIED {
		logger.Error(ctx, "Invalid payload type received")
		return &woCxPb.CountQueueElementsResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("Invalid payload type received"),
		}, nil
	}

	count, err := s.persistentQueue.CountElements(ctx, req.GetPayloadType())
	if err != nil {
		logger.Error(ctx, "Failed to get count of queue elements", zap.Error(err))
		return nil, err
	}

	return &woCxPb.CountQueueElementsResponse{
		Status: rpc.StatusOk(),
		Count:  count,
	}, nil
}
