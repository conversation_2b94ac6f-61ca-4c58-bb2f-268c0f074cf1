package cx

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	woCxPb "github.com/epifi/gamma/api/wealthonboarding/cx"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/wealthonboarding/docket"
)

func (s *Service) ExtractAndStorePanFromDocket(ctx context.Context, req *woCxPb.ExtractAndStorePanFromDocketRequest) (*woCxPb.ExtractAndStorePanFromDocketResponse, error) {
	ctx = epificontext.CtxWithActorId(ctx, req.GetActorId())
	err := s.docketExtractionService.ExtractAndStoreKRADocketPAN(ctx, req.GetActorId(), &docket.PanLocation{FileName: req.GetPanImageDocketPageLocation().GetFileName(), PageNum: int(req.GetPanImageDocketPageLocation().GetPageNum())})
	if err != nil {
		logger.Error(ctx, "error extracting and storing KRA docket PAN",
			zap.Error(err),
			zap.String(logger.FILE_NAME, req.GetPanImageDocketPageLocation().GetFileName()),
			zap.Int32(logger.PAGE, req.GetPanImageDocketPageLocation().GetPageNum()),
		)
		return &woCxPb.ExtractAndStorePanFromDocketResponse{Status: rpc.StatusInternal()}, nil
	}
	return &woCxPb.ExtractAndStorePanFromDocketResponse{Status: rpc.StatusOk()}, nil
}
