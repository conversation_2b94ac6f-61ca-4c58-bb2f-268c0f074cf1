package cx

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"sync"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	pqPb "github.com/epifi/gamma/api/persistentqueue"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/vendors/wealth"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woCxPb "github.com/epifi/gamma/api/wealthonboarding/cx"
	"github.com/epifi/be-common/pkg/logger"
	pqPkg "github.com/epifi/gamma/pkg/persistentqueue"
	"github.com/epifi/gamma/wealthonboarding/helper"
)

const (
	maxLimit = 10000
)

// nolint: funlen
func (s *Service) GetQueueElements(ctx context.Context, req *woCxPb.GetQueueElementsRequest) (*woCxPb.GetQueueElementsResponse, error) {
	var limit int32
	if req.GetPayloadType() == pqPb.PayloadType_PAYLOAD_TYPE_KYC_DOCKET && req.GetKycDocketFilters() != nil {
		limit = maxLimit
	} else {
		limit = req.GetLimit()
	}
	elements, err := s.persistentQueue.GetElements(ctx, req.GetPayloadType(), int(limit), int(req.GetPageNo()), &timestampPb.Timestamp{Seconds: 0}, timestampPb.Now(), false, nil)
	if rpc.StatusFromError(err).IsRecordNotFound() {
		return &woCxPb.GetQueueElementsResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}
	if err != nil {
		logger.Error(ctx, "error while fetching element from PQ", zap.Error(err))
		return &woCxPb.GetQueueElementsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	logger.Info(ctx, fmt.Sprintf("number of elements in persistent queue: %v", len(elements)))
	queueElements := make([]*woCxPb.QueueElement, len(elements))
	var wg sync.WaitGroup
	for i, element := range elements {
		wg.Add(1)
		// TODO(sharath): use error group to propagate errors outside goroutine
		go func(i int, element *pqPkg.QueueElement) {
			defer wg.Done()
			switch element.GetPayloadType() {
			case pqPb.PayloadType_PAYLOAD_TYPE_WEALTH_DATA:
				rdErr := s.convertToSignedUrlDoc(ctx, element)
				if rdErr != nil {
					logger.Error(ctx, "error while converting document to signed url", zap.Error(rdErr))
					return
				}
				queueElements[i] = &woCxPb.QueueElement{
					Id: element.GetID(),
					WealthDataReview: &pqPb.WealthDataReview{
						ActorId:          element.GetActorID(),
						LivenessPayload:  element.GetPayload().GetWealthDataReview().GetLivenessPayload(),
						RedactionPayload: element.GetPayload().GetWealthDataReview().GetRedactionPayload(),
						ExpiryPayload:    element.GetPayload().GetWealthDataReview().GetExpiryPayload(),
					},
				}
			case pqPb.PayloadType_PAYLOAD_TYPE_KYC_DOCKET:
				od, odErr := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, element.GetActorID(), woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT)
				if odErr != nil {
					logger.Error(ctx, "error in getting onboarding details", zap.Error(odErr))
					return
				}
				if !s.isKycVerified(ctx, od) {
					// filter the elements based on the filters from request
					if req.GetKycDocketFilters().GetPan() != "" {
						if req.GetKycDocketFilters().GetPan() != od.GetMetadata().GetPanDetails().GetId() {
							return
						}
					}
					if req.GetKycDocketFilters().GetKraStatus() != wealth.KraStatus_KRA_STATUS_TYPE_UNSPECIFIED && len(od.GetMetadata().GetUploadKraDocData().GetUploadAttempts()) != 0 {
						latestUploadAttempt := od.GetMetadata().GetUploadKraDocData().GetUploadAttempts()[len(od.GetMetadata().GetUploadKraDocData().GetUploadAttempts())-1]
						if req.GetKycDocketFilters().GetKraStatus() != latestUploadAttempt.GetPanEnquiry().GetStatus() {
							return
						}
					}
					woCxElement, cErr := s.getKycDocketQueueElement(ctx, element, od)
					if cErr != nil {
						logger.Error(ctx, "error in getting cx queue element", zap.Error(cErr), zap.String(logger.ACTOR_ID_V2, element.GetActorID()))
						return
					}
					queueElements[i] = woCxElement
				} else {
					dErr := s.persistentQueue.DeleteElement(ctx, element)
					if dErr != nil {
						logger.Error(ctx, "error while deleting queue elements", zap.Error(dErr))
						return
					}
				}
			default:
				logger.Error(ctx, "unable to identify payload type", zap.String(logger.PAYLOAD, req.GetPayloadType().String()))
			}
		}(i, element)
	}
	wg.Wait()
	var nonNilQueueElements []*woCxPb.QueueElement
	for _, e := range queueElements {
		if e != nil {
			nonNilQueueElements = append(nonNilQueueElements, e)
		}
	}
	logger.Info(ctx, fmt.Sprintf("number of elements successfully converted for response: %v", len(nonNilQueueElements)))
	return &woCxPb.GetQueueElementsResponse{
		Status:   rpc.StatusOk(),
		Elements: nonNilQueueElements,
	}, nil
}

// convertToBase64Doc takes a document which has its content in s3, downloads all the content and updates it in place
func (s *Service) convertToSignedUrlDoc(ctx context.Context, element *pqPkg.QueueElement) error {
	var payload *pqPb.WealthDataReview_OcrPayload
	if element.GetPayload().GetWealthDataReview().GetRedactionPayload() != nil {
		payload = element.GetPayload().GetWealthDataReview().GetRedactionPayload()
	} else if element.GetPayload().GetWealthDataReview().GetExpiryPayload() != nil {
		payload = element.GetPayload().GetWealthDataReview().GetExpiryPayload()
	}
	// payload is already empty, no need to do anything
	if payload == nil {
		return nil
	}
	updateQueueElement := false
	if payload.GetOriginalDocument() != nil {
		if payload.GetRawOriginalDocument() == nil {
			var rErr error
			// TODO(sharath): do dochelper calls async
			// get raw document since original document is stored as base64 string
			payload.RawOriginalDocument, rErr = s.docHelper.GetRawS3Document(ctx, element.GetActorID(), payload.GetOriginalDocument())
			if rErr != nil {
				return rErr
			}
			updateQueueElement = true
		}
	}
	if payload.GetProcessedDocumentProof().GetDocumentProof() != nil {
		if payload.GetRawProcessedDocument() == nil {
			payload.RawProcessedDocument = &pqPb.WealthDataReview_OcrDocumentProof{
				ConfidenceScore: payload.GetProcessedDocumentProof().GetConfidenceScore(),
				ThresholdScore:  payload.GetProcessedDocumentProof().GetThresholdScore(),
			}
			var rErr error
			// TODO(sharath): do dochelper calls async
			payload.RawProcessedDocument.DocumentProof, rErr = s.docHelper.GetRawS3Document(ctx, element.GetActorID(), payload.GetProcessedDocumentProof().GetDocumentProof())
			if rErr != nil {
				return rErr
			}
			updateQueueElement = true
		}
	}
	// update the queue element with raw document
	if updateQueueElement {
		// TODO(sharath): do delete and insert in txn
		dErr := s.persistentQueue.DeleteElement(ctx, element)
		if dErr != nil {
			return dErr
		}
		iErr := s.persistentQueue.InsertElement(ctx, element)
		if iErr != nil {
			return iErr
		}
	}
	// get signed url for sending to cx
	err := s.updateSignedUrl(ctx, payload.GetRawOriginalDocument())
	if err != nil {
		return err
	}
	err = s.updateSignedUrl(ctx, payload.GetRawProcessedDocument().GetDocumentProof())
	if err != nil {
		return err
	}
	return nil
}

func (s *Service) isKycVerified(ctx context.Context, od *woPb.OnboardingDetails) bool {
	if len(od.GetMetadata().GetUploadKraDocData().GetUploadAttempts()) == 0 {
		logger.Error(ctx, "no upload attempt present in metadata")
		return false
	}
	upAttempts := od.GetMetadata().GetUploadKraDocData().GetUploadAttempts()
	return upAttempts[len(upAttempts)-1].GetPanEnquiry().GetStatus() == wealth.KraStatus_KRA_STATUS_KRA_VERIFIED
}

func (s *Service) getKycDocketQueueElement(ctx context.Context, element *pqPkg.QueueElement, od *woPb.OnboardingDetails) (*woCxPb.QueueElement, error) {
	doc, dErr := s.docHelper.DownloadDoc(ctx, od.GetMetadata().GetKraDocketInfo().GetDocAsProof())
	if dErr != nil {
		return nil, errors.Wrap(dErr, "error in downloading KYC docket")
	}
	if len(od.GetMetadata().GetUploadKraDocData().GetUploadAttempts()) == 0 {
		return nil, errors.New("no upload attempt present in metadata")
	}
	if len(doc.GetPhoto()) == 0 || doc.GetPhoto()[0].GetImageDataBase64() == "" {
		return nil, errors.New("empty kyc docket")
	}
	// encoding to base64 since kyc form is stored in s3 without encoding
	doc.GetPhoto()[0].ImageDataBase64 = ""
	// fetch pre signed url of the docket
	psuRes, psuErr := s.docHelper.GetSignedUrl(ctx, od.GetMetadata().GetKraDocketInfo().GetDocAsProof().GetS3Paths()[0], true, s.conf.S3Conf.Bucket, s.conf.CvlKra.PdfExpiryTime)
	if psuErr != nil {
		return nil, errors.Wrap(psuErr, "error while fetching pre signed url")
	}
	doc.GetPhoto()[0].ImageUrl = psuRes
	latestUploadAttempt := od.GetMetadata().GetUploadKraDocData().GetUploadAttempts()[len(od.GetMetadata().GetUploadKraDocData().GetUploadAttempts())-1]
	year, month, day := latestUploadAttempt.GetCreatedAt().AsTime().Date()
	rejectReason := pqPb.KraRejectReason_KRA_REJECT_REASON_UNSPECIFIED
	if latestUploadAttempt.GetPanEnquiry().GetStatus() == wealth.KraStatus_KRA_STATUS_REJECTED {
		rejectReason = pqPb.KraRejectReason_KRA_REJECT_REASON_UNKNOWN
	}
	userInputPendingData := ""
	for i, pendingData := range od.GetAgentProvidedData().GetUserInputPendingData() {
		if i != 0 {
			userInputPendingData += ","
		}
		userInputPendingData += pendingData.String()
	}
	return &woCxPb.QueueElement{
		Id: element.GetID(),
		KycDocketReview: &pqPb.KycDocketReview{
			ActorId: element.GetActorID(),
			KraFormPayload: &pqPb.KycDocketReview_KycDocketPayload{
				KycForm: doc,
				UploadDate: &date.Date{
					Year:  int32(year),
					Month: int32(month),
					Day:   int32(day),
				},
				KraStatus:               latestUploadAttempt.GetPanEnquiry().GetStatus(),
				KraRejectReason:         rejectReason,
				HoldOrDeactivateRemarks: latestUploadAttempt.GetPanEnquiry().GetHoldDeactivateRemarks(),
				KraUpdateRemarks:        latestUploadAttempt.GetPanEnquiry().GetUpdateRemarks(),
				UserInputPendingData:    userInputPendingData,
			},
			Pan: od.GetMetadata().GetPanDetails().GetId(),
		},
	}, nil
}

func (s *Service) updateSignedUrl(ctx context.Context, doc *types.DocumentProof) error {
	if doc == nil {
		return errors.New("nil document")
	}
	if len(doc.GetS3Paths()) == 0 {
		return errors.New("empty s3 path")
	}
	// fetch pre signed url of the docket
	psuRes, psuErr := s.docHelper.GetSignedUrl(ctx, doc.GetS3Paths()[0], true, s.conf.S3Conf.Bucket, s.conf.CvlKra.PdfExpiryTime)
	if psuErr != nil {
		return errors.Wrap(psuErr, "error while fetching pre signed url")
	}
	imageType := helper.GetDocumentFormatType(doc)
	if imageType == commontypes.ImageType_IMAGE_TYPE_UNSPECIFIED {
		return errors.New("image type unspecified")
	}
	if len(doc.GetPhoto()) == 0 {
		doc.Photo = []*commontypes.Image{{
			ImageType: imageType,
			ImageUrl:  psuRes,
		}}
	}
	doc.GetPhoto()[0].ImageUrl = psuRes
	return nil
}
