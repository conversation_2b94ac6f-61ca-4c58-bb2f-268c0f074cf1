//nolint:gocritic
package cx

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/gamma/api/auth/liveness/mocks"
	pQueuePb "github.com/epifi/gamma/api/persistentqueue"
	types "github.com/epifi/gamma/api/typesv2"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woCxPb "github.com/epifi/gamma/api/wealthonboarding/cx"
	"github.com/epifi/gamma/pkg/persistentqueue"
	pQueueMocks "github.com/epifi/gamma/pkg/persistentqueue/mocks"
	"github.com/epifi/gamma/wealthonboarding/dao"
	mock_dao "github.com/epifi/gamma/wealthonboarding/test/mocks/dao"
)

var (
	onboardingDetailsReq = &woPb.OnboardingDetails{
		Id:      "716cb1ad-1d2f-4b3e-b4df-c81b3989a3f4",
		ActorId: "AC220324or/yNUELQkGRuBBIriTXYg==",
		Metadata: &woPb.OnboardingMetadata{
			PanDetails: &types.DocumentProof{
				ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
				Id:        "panId",
				S3Paths:   []string{"pan s3 path"},
			},
			PoiDetails: nil,
			PoaDetails: &types.DocumentProof{
				ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR,
				Id:        "poaId",
				S3Paths:   []string{"poa s3 path"},
			},
			PersonalDetails: &woPb.PersonalDetails{
				Gender:            types.Gender_MALE,
				MaritalStatus:     types.MaritalStatus_MARRIED,
				Nationality:       types.Nationality_NATIONALITY_INDIAN,
				ResidentialStatus: types.ResidentialStatus_RESIDENT_INDIVIDUAL,
				Signature: &types.DocumentProof{
					ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_SIGNATURE,
					S3Paths:   []string{"signature s3 path"},
				},
				FatherName: &commontypes.Name{FirstName: "father name"},
				Email:      "email",
			},
			FaceMatchData: nil,
			NsdlData: &woPb.NsdlData{
				PanDetails: &woPb.NsdlData_NsdlPanDetails{
					PanCardName: "pan card name",
				},
			},
			KraData: nil,
			CkycData: &woPb.CkycData{
				SearchData: &woPb.CkycSearchData{
					Photo: &types.DocumentProof{
						ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PHOTOGRAPH,
						S3Paths: []string{
							"ckyc photo s3 path",
						},
					},
				},
				DownloadData: &woPb.CkycDownloadData{
					PersonalDetails: &woPb.CkycPersonalDetails{
						FathersName: &commontypes.Name{FirstName: "ckyc father name"},
						Dob: &date.Date{
							Year:  1990,
							Month: 1,
							Day:   1,
						},
						PermanentAddress: &types.PostalAddress{
							AddressLines: []string{"ckyc address"},
						},
						MobileNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 12345678,
						},
						EmailId: "ckyc email",
					},
				},
			},
		},
		Status:      woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED,
		CurrentStep: woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
	}
	onboardingStepDetailsReq = &woPb.OnboardingStepDetails{
		Id:                  "716cb1ad-1d2f-4b3e-b4df-c81b3989a3f4",
		OnboardingDetailsId: "716cb1ad-1d2f-4b3e-b4df-c81b3989a3f4",
		Step:                woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET,
		SubStatus:           woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CUSTOMER_SIGNED,
		Status:              woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
	}
	errOnbDetailsNotUpdated     = errors.New("error while updating onboarding details")
	errOnbStepDetailsNotUpdated = errors.New("error while updating onboarding step details")
)

func TestService_GetQueueElements(t *testing.T) {
	type args struct {
		req *woCxPb.GetQueueElementsRequest
	}
	tests := []struct {
		name          string
		args          args
		want          *woCxPb.GetQueueElementsResponse
		wantErr       bool
		mockPQElement []*persistentqueue.QueueElement
		mockPQError   error
	}{
		{
			name: "success",
			args: args{
				req: &woCxPb.GetQueueElementsRequest{
					PayloadType: pQueuePb.PayloadType_PAYLOAD_TYPE_WEALTH_DATA,
					Limit:       10,
					PageNo:      0,
				},
			},
			want: &woCxPb.GetQueueElementsResponse{
				Status: rpc.StatusOk(),
				Elements: []*woCxPb.QueueElement{
					{
						Id: "a05d25b2-be14-436c-9d32-2095350b8686",
						WealthDataReview: &pQueuePb.WealthDataReview{
							ActorId: "random_actor",
							LivenessPayload: &pQueuePb.WealthDataReview_LivenessPayload{
								VideoLocation: "random_liveness",
								RequestId:     "5efe9ea2-6e57-4139-9c01-4bb5ac03d1e6",
								Otp:           "3175",
								OtpScore:      66,
								LivenessScore: 78,
								CreatedAt:     timestamppb.Now(),
							},
						},
					},
				},
			},
			wantErr: false,
			mockPQElement: []*persistentqueue.QueueElement{{
				ID:          "a05d25b2-be14-436c-9d32-2095350b8686",
				ActorID:     "random_actor",
				PayloadType: pQueuePb.PayloadType_PAYLOAD_TYPE_WEALTH_DATA,
				Payload: &pQueuePb.Payload{
					WealthDataReview: &pQueuePb.WealthDataReview{
						ActorId: "random_actor",
						LivenessPayload: &pQueuePb.WealthDataReview_LivenessPayload{
							VideoLocation: "random_liveness",
							RequestId:     "5efe9ea2-6e57-4139-9c01-4bb5ac03d1e6",
							Otp:           "3175",
							OtpScore:      66,
							LivenessScore: 78,
							CreatedAt:     timestamppb.Now(),
						},
					},
				},
				DeletedAtUnix: 0,
				CreatedAt:     time.Time{},
				UpdatedAt:     time.Time{},
			}},
		},
		{
			name: "no records returned",
			args: args{
				req: &woCxPb.GetQueueElementsRequest{
					PayloadType: pQueuePb.PayloadType_PAYLOAD_TYPE_WEALTH_DATA,
					Limit:       10,
					PageNo:      100,
				},
			},
			want: &woCxPb.GetQueueElementsResponse{
				Status:   rpc.StatusOk(),
				Elements: nil,
			},
			mockPQElement: nil,
			wantErr:       false,
		},
		{
			name: "error fetching payload",
			args: args{
				req: &woCxPb.GetQueueElementsRequest{
					PayloadType: pQueuePb.PayloadType_PAYLOAD_TYPE_WEALTH_DATA,
					Limit:       10,
					PageNo:      1,
				},
			},
			want: &woCxPb.GetQueueElementsResponse{
				Status:   rpc.StatusInternal(),
				Elements: nil,
			},
			mockPQError: errors.New("random db error"),
			wantErr:     false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockPQueue := pQueueMocks.NewMockPersistentQueue(ctrl)
			mckLvClient := mocks.NewMockLivenessClient(ctrl)
			if tt.mockPQError != nil {
				mockPQueue.EXPECT().GetElements(gomock.Any(), tt.args.req.PayloadType, int(tt.args.req.Limit), int(tt.args.req.PageNo), gomock.Any(), gomock.Any(), false, gomock.Any()).Return(nil, tt.mockPQError)
			} else {
				mockPQueue.EXPECT().GetElements(gomock.Any(), tt.args.req.PayloadType, int(tt.args.req.Limit), int(tt.args.req.PageNo), gomock.Any(), gomock.Any(), false, gomock.Any()).Return(tt.mockPQElement, nil)

			}
			s := &Service{
				persistentQueue: mockPQueue,
				lvClient:        mckLvClient,
			}
			got, err := s.GetQueueElements(context.Background(), tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetQueueElements() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fixCreatedAt(got, tt.want)
			assert.Equal(t, tt.want, got)
		})
	}
}

// fixCreatedAt assigns the same createdAt date for got and want response so that they don't fail the assert check
func fixCreatedAt(got *woCxPb.GetQueueElementsResponse, want *woCxPb.GetQueueElementsResponse) {
	for idx := range got.GetElements() {
		got.GetElements()[idx].GetWealthDataReview().GetLivenessPayload().CreatedAt = want.GetElements()[idx].GetWealthDataReview().GetLivenessPayload().CreatedAt
	}
}

func TestService_CountQueueElements(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockPQueue := pQueueMocks.NewMockPersistentQueue(ctrl)
	type args struct {
		ctx   context.Context
		req   *woCxPb.CountQueueElementsRequest
		mocks []interface{}
	}
	tests := []struct {
		name    string
		args    args
		want    *woCxPb.CountQueueElementsResponse
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "happy case",
			args: args{
				ctx: context.Background(),
				req: &woCxPb.CountQueueElementsRequest{PayloadType: pQueuePb.PayloadType_PAYLOAD_TYPE_WEALTH_DATA},
				mocks: []interface{}{
					mockPQueue.EXPECT().CountElements(gomock.Any(), pQueuePb.PayloadType_PAYLOAD_TYPE_WEALTH_DATA).Return(int64(1), nil),
				},
			},
			want: &woCxPb.CountQueueElementsResponse{
				Status: rpc.StatusOk(),
				Count:  1,
			},
			wantErr: assert.NoError,
		},
		{
			name: "pq error",
			args: args{
				ctx: context.Background(),
				req: &woCxPb.CountQueueElementsRequest{PayloadType: pQueuePb.PayloadType_PAYLOAD_TYPE_WEALTH_DATA},
				mocks: []interface{}{
					mockPQueue.EXPECT().CountElements(gomock.Any(), pQueuePb.PayloadType_PAYLOAD_TYPE_WEALTH_DATA).Return(int64(0), errors.New("pq error")),
				},
			},
			want:    nil,
			wantErr: assert.Error,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				persistentQueue: mockPQueue,
			}
			got, err := s.CountQueueElements(tt.args.ctx, tt.args.req)
			if !tt.wantErr(t, err, fmt.Sprintf("CountQueueElements(%v, %v)", tt.args.ctx, tt.args.req)) {
				return
			}
			assert.Equalf(t, tt.want, got, "CountQueueElements(%v, %v)", tt.args.ctx, tt.args.req)
		})
	}
}

func TestService_MarkStepStale(t *testing.T) {
	ctrl := gomock.NewController(t)
	ctrl.Finish()
	odMock := mock_dao.NewMockOnboardingDetailsDao(ctrl)
	osdMock := mock_dao.NewMockOnboardingStepDetailsDao(ctrl)
	fmt.Println(osdMock)

	type fields struct {
		onboardingDetailsDao     dao.OnboardingDetailsDao
		onboardingStepDetailsDao dao.OnboardingStepDetailsDao
	}
	type args struct {
		ctx context.Context
		req *woCxPb.MarkStepStaleRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		mocks   []interface{}
		want    *woCxPb.MarkStepStaleResponse
		wantErr bool
		err     error
	}{
		{
			name: "no record present for given actorID in wealthonboarding",
			args: args{
				ctx: context.Background(),
				req: &woCxPb.MarkStepStaleRequest{
					ActorId: "AC220224jwZisV1fR2mpu2xKm4EUgA==",
					Step:    []woPb.OnboardingStep{woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET},
				},
			},
			fields: fields{
				onboardingDetailsDao:     odMock,
				onboardingStepDetailsDao: osdMock,
			},
			mocks: []interface{}{
				odMock.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
			},
			want:    &woCxPb.MarkStepStaleResponse{Status: rpc.StatusInternal()},
			err:     epifierrors.ErrRecordNotFound,
			wantErr: true,
		},
		{
			name: "no record present for given onboarding_details_id and step",
			fields: fields{
				onboardingDetailsDao:     odMock,
				onboardingStepDetailsDao: osdMock,
			},
			args: args{
				ctx: context.Background(),
				req: &woCxPb.MarkStepStaleRequest{
					ActorId: "AC220224jwZisV1fR2mpu2xKm4EUgA==",
					Step:    []woPb.OnboardingStep{woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET},
				},
			},
			mocks: []interface{}{
				odMock.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(onboardingDetailsReq, nil),
				osdMock.EXPECT().GetByOnboardingDetailsIdAndStep(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
			},
			want:    &woCxPb.MarkStepStaleResponse{Status: rpc.StatusInternal()},
			err:     epifierrors.ErrRecordNotFound,
			wantErr: true,
		},
		{
			name: "error while updating onboarding_step_details",
			fields: fields{
				onboardingDetailsDao:     odMock,
				onboardingStepDetailsDao: osdMock,
			},
			args: args{
				ctx: context.Background(),
				req: &woCxPb.MarkStepStaleRequest{
					ActorId: "AC220224jwZisV1fR2mpu2xKm4EUgA==",
					Step:    []woPb.OnboardingStep{woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET},
				},
			},
			mocks: []interface{}{
				odMock.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(onboardingDetailsReq, nil),
				osdMock.EXPECT().GetByOnboardingDetailsIdAndStep(gomock.Any(), gomock.Any(), gomock.Any()).Return(onboardingStepDetailsReq, nil),
				osdMock.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any()).Return(errOnbStepDetailsNotUpdated),
			},
			want:    &woCxPb.MarkStepStaleResponse{Status: rpc.StatusInternal()},
			err:     errOnbStepDetailsNotUpdated,
			wantErr: true,
		},
		{
			name: "error while given step is not supported",
			fields: fields{
				onboardingDetailsDao:     odMock,
				onboardingStepDetailsDao: osdMock,
			},
			args: args{
				ctx: context.Background(),
				req: &woCxPb.MarkStepStaleRequest{
					ActorId: "AC220224jwZisV1fR2mpu2xKm4EUgA==",
					Step:    []woPb.OnboardingStep{woPb.OnboardingStep_ONBOARDING_STEP_SIGN_AGREEMENT},
				},
			},
			mocks: []interface{}{
				odMock.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(onboardingDetailsReq, nil),
				osdMock.EXPECT().GetByOnboardingDetailsIdAndStep(gomock.Any(), gomock.Any(), gomock.Any()).Return(onboardingStepDetailsReq, nil),
			},
			want:    &woCxPb.MarkStepStaleResponse{Status: rpc.StatusInternal()},
			err:     fmt.Errorf("step not supported, step %v", woPb.OnboardingStep_ONBOARDING_STEP_SIGN_AGREEMENT.String()),
			wantErr: true,
		},
		{
			name: "error while updating onboarding_details",
			fields: fields{
				onboardingDetailsDao:     odMock,
				onboardingStepDetailsDao: osdMock,
			},
			args: args{
				ctx: context.Background(),
				req: &woCxPb.MarkStepStaleRequest{
					ActorId: "AC220224jwZisV1fR2mpu2xKm4EUgA==",
					Step:    []woPb.OnboardingStep{woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET},
				},
			},
			mocks: []interface{}{
				odMock.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(onboardingDetailsReq, nil),
				osdMock.EXPECT().GetByOnboardingDetailsIdAndStep(gomock.Any(), gomock.Any(), gomock.Any()).Return(onboardingStepDetailsReq, nil),
				osdMock.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				odMock.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(errOnbDetailsNotUpdated),
			},
			want:    &woCxPb.MarkStepStaleResponse{Status: rpc.StatusInternal()},
			err:     errOnbDetailsNotUpdated,
			wantErr: true,
		},
		{
			name: "provide valid details- happy testcase",
			fields: fields{
				onboardingDetailsDao:     odMock,
				onboardingStepDetailsDao: osdMock,
			},
			args: args{
				ctx: context.Background(),
				req: &woCxPb.MarkStepStaleRequest{
					ActorId: "AC220224jwZisV1fR2mpu2xKm4EUgA==",
					Step: []woPb.OnboardingStep{woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET,
						woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO, woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA},
				},
			},
			mocks: []interface{}{
				odMock.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(onboardingDetailsReq, nil).Times(3),
				osdMock.EXPECT().GetByOnboardingDetailsIdAndStep(gomock.Any(), gomock.Any(), gomock.Any()).Return(onboardingStepDetailsReq, nil).Times(3),
				osdMock.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(3),
				odMock.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
			},
			want:    &woCxPb.MarkStepStaleResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				onboardingDetailsDao:     tt.fields.onboardingDetailsDao,
				onboardingStepDetailsDao: tt.fields.onboardingStepDetailsDao,
			}
			got, err := s.MarkStepStale(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DownloadDoc() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
				if !reflect.DeepEqual(tt.want, got) {
					t.Errorf("DownloadDoc() got = %v, want %v", got, tt.want)
				}
			} else if !errors.Is(err, tt.err) && err.Error() != tt.err.Error() {
				t.Errorf("Error don't match, got = %v, want %v", err, tt.err)
				return
			}
			assert.Equalf(t, tt.want, got, "MarkStepStale(%v, %v)", tt.args.ctx, tt.args.req)
		})
	}
}
