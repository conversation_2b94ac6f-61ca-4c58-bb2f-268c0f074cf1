package cx

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"

	"github.com/epifi/be-common/api/rpc"
	types "github.com/epifi/gamma/api/typesv2"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woCxPb "github.com/epifi/gamma/api/wealthonboarding/cx"
	"github.com/epifi/be-common/pkg/logger"
)

// nolint: funlen
func (s *Service) UpdateManualReviewItem(ctx context.Context, req *woCxPb.UpdateManualReviewItemRequest) (*woCxPb.UpdateManualReviewItemResponse, error) {
	mrItem, mrErr := s.mrSvc.GetReviewItem(ctx, req.GetId())
	if mrErr != nil {
		logger.Error(ctx, "error while fetching manual review item", zap.Error(mrErr))
		return &woCxPb.UpdateManualReviewItemResponse{Status: rpc.StatusInternal()}, nil
	}
	// update status
	mrItem.ReviewStatus = req.GetReviewStatus()
	// update payload
	switch {
	// assign reject reason if the liveness video is rejected
	case mrItem.GetItemType() == woPb.ItemType_ITEM_TYPE_LIVENESS && mrItem.GetReviewStatus() == woPb.ReviewStatus_REVIEW_STATUS_REJECTED:
		if req.GetLivenessRejectReason() == woPb.LivenessRejectReason_LIVENESS_REJECT_REASON_UNSPECIFIED {
			logger.Error(ctx, "liveness rejected reason not specified while moving review item status to rejected")
			return &woCxPb.UpdateManualReviewItemResponse{Status: rpc.StatusInvalidArgument()}, nil
		}
		mrItem.ReviewPayload.RejectReason = &woPb.ReviewPayload_LivenessRejectReason{
			LivenessRejectReason: req.GetLivenessRejectReason(),
		}
	case mrItem.GetItemType() == woPb.ItemType_ITEM_TYPE_AADHAAR_REDACTION:
		// assign the agents document proof if review status is edited
		if mrItem.GetReviewStatus() == woPb.ReviewStatus_REVIEW_STATUS_EDITED {
			if len(req.GetRedactionInfo().GetAgentEditedImages()) == 0 {
				logger.Error(ctx, "redacted document not uploaded for review status edited")
				return &woCxPb.UpdateManualReviewItemResponse{Status: rpc.StatusInvalidArgument()}, errors.New("redacted document cannot be empty")
			}
			uploadedDoc, err := s.getEditedDocFromImages(ctx, req.GetRedactionInfo().GetAgentEditedImages(), mrItem.GetReviewPayload().GetRedactionReview().GetOriginalDocument(), mrItem.GetReviewPayload().GetRedactionReview().GetActorId())
			if err != nil {
				logger.Error(ctx, "error in getting document proof from edited images", zap.Error(err))
				return &woCxPb.UpdateManualReviewItemResponse{Status: rpc.StatusInternal()}, nil
			}
			mrItem.GetReviewPayload().GetRedactionReview().EditedDocument = uploadedDoc
		}
		if mrItem.GetReviewStatus() == woPb.ReviewStatus_REVIEW_STATUS_REJECTED {
			if req.GetRedactionInfo().GetRedactionRejectReason() == woPb.RedactionRejectReason_REDACTION_REJECT_REASON_UNSPECIFIED {
				logger.Error(ctx, "redaction rejected reason not specified while moving review item status to rejected")
				return &woCxPb.UpdateManualReviewItemResponse{Status: rpc.StatusInvalidArgument()}, nil
			}
			mrItem.GetReviewPayload().RejectReason = &woPb.ReviewPayload_RedactionRejectReason{RedactionRejectReason: req.GetRedactionInfo().GetRedactionRejectReason()}
		}
	case mrItem.GetItemType() == woPb.ItemType_ITEM_TYPE_DOCUMENT_EXPIRY:
		if mrItem.GetReviewStatus() == woPb.ReviewStatus_REVIEW_STATUS_EDITED {
			if req.GetExpiryInfo().GetCorrectExpiryDate() == nil {
				logger.Error(ctx, "expiry date not specified for review status edited")
				return &woCxPb.UpdateManualReviewItemResponse{Status: rpc.StatusInvalidArgument()}, errors.New("corrected expiry date cannot be empty")
			}
			mrItem.GetReviewPayload().GetExpiryReview().EditedDocument = getEditedExpiryDoc(mrItem.GetReviewPayload().GetExpiryReview().GetOriginalDocument(), req.GetExpiryInfo().GetCorrectExpiryDate())
		}
		if mrItem.GetReviewStatus() == woPb.ReviewStatus_REVIEW_STATUS_REJECTED {
			if req.GetExpiryInfo().GetExpiryRejectReason() == woPb.ExpiryRejectReason_EXPIRY_REJECT_REASON_UNSPECIFIED {
				logger.Error(ctx, "expiry rejected reason not specified while moving review item status to rejected")
				return &woCxPb.UpdateManualReviewItemResponse{Status: rpc.StatusInvalidArgument()}, nil
			}
			mrItem.GetReviewPayload().RejectReason = &woPb.ReviewPayload_ExpiryRejectReason{ExpiryRejectReason: req.GetExpiryInfo().GetExpiryRejectReason()}
		}
	}
	updErr := s.mrSvc.MarkReviewCompleted(ctx, mrItem.GetId(), mrItem.GetReviewStatus(), mrItem.GetReviewPayload(), req.GetReviewedAt())
	if updErr != nil {
		logger.Error(ctx, "error while updating manual review item", zap.Error(updErr))
		return &woCxPb.UpdateManualReviewItemResponse{Status: rpc.StatusInternal()}, nil
	}
	return &woCxPb.UpdateManualReviewItemResponse{Status: rpc.StatusOk()}, nil
}

func (s *Service) getEditedDocFromImages(ctx context.Context, editedImages []*commontypes.Image, originalDoc *types.DocumentProof, actorId string) (*types.DocumentProof, error) {
	editedDocProof := &types.DocumentProof{
		ProofType: originalDoc.GetProofType(),
		Id:        originalDoc.GetId(),
		Photo:     editedImages,
		Expiry:    originalDoc.GetExpiry(),
	}
	uploadedDocProof, err := s.docHelper.UploadDoc(ctx, actorId, editedDocProof)
	if err != nil {
		return nil, errors.Wrap(err, "error in uploading document to S3")
	}
	return uploadedDocProof, nil
}

func getEditedExpiryDoc(originalDoc *types.DocumentProof, expDate *date.Date) *types.DocumentProof {
	return &types.DocumentProof{
		ProofType: originalDoc.GetProofType(),
		Id:        originalDoc.GetId(),
		Photo:     originalDoc.GetPhoto(),
		Expiry:    expDate,
		S3Paths:   originalDoc.GetS3Paths(),
	}
}
