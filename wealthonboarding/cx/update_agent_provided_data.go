package cx

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/vendors/wealth"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woCxPb "github.com/epifi/gamma/api/wealthonboarding/cx"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

// UpdateAgentProvidedData is for the use of ops agents when a user's KYC is on hold or rejected
// based on the reason for hold or rejection, if there is an issue with the documents submitted, agent will get the proper documents from the customer
// and uploads them with the RPC. This RPC will store these documents in the db and also uploads them to CVL sftp for KYC resolution
func (s *Service) UpdateAgentProvidedData(ctx context.Context, req *woCxPb.UpdateAgentProvidedDataRequest) (*woCxPb.UpdateAgentProvidedDataResponse, error) {
	wod, wodErr := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, req.GetActorId(), woPb.OnboardingType_ONBOARDING_TYPE_WEALTH)
	if wodErr != nil {
		logger.Error(ctx, "failed to get onboarding details by actor-id and onboarding type", zap.Error(wodErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &woCxPb.UpdateAgentProvidedDataResponse{Status: rpc.StatusInternal()}, nil
	}

	if req.GetKycStatus() != wealth.KraStatus_KRA_STATUS_TYPE_UNSPECIFIED {
		wuErr := s.updateAgentProvidedData(wod, req)
		if wuErr != nil {
			logger.Error(ctx, "error in updating wealth onb object", zap.Error(wuErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			return &woCxPb.UpdateAgentProvidedDataResponse{Status: rpc.StatusInternal()}, nil
		}
	}
	var piod *woPb.OnboardingDetails
	if len(req.GetUserInputPendingData()) != 0 {
		var piodErr error
		// pending user data needs to be updated in pre investment onboarding details because
		// we fetch this along with KYC docket which is fetched from pre investment onboarding details
		piod, piodErr = s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, req.GetActorId(), woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT)
		if piodErr != nil {
			logger.Error(ctx, "failed to get pre inv onboarding details", zap.Error(piodErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			return &woCxPb.UpdateAgentProvidedDataResponse{Status: rpc.StatusInternal()}, nil
		}
		piuErr := updatePreInvestmentObject(piod, req)
		if piuErr != nil {
			logger.Error(ctx, "error in updating pre inv onb object", zap.Error(piuErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			return &woCxPb.UpdateAgentProvidedDataResponse{Status: rpc.StatusInternal()}, nil
		}
	}

	txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		return s.updateOnboardingDetailsInDb(txnCtx, wod, piod)
	})
	if txnErr != nil {
		logger.Error(ctx, "error in updating onb details in db", zap.Error(txnErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &woCxPb.UpdateAgentProvidedDataResponse{Status: rpc.StatusInternal()}, nil
	}
	mssResp, mssErr := s.MarkStepStale(ctx, &woCxPb.MarkStepStaleRequest{
		ActorId: req.GetActorId(),
		Step:    []woPb.OnboardingStep{woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET},
	})
	if te := epifigrpc.RPCError(mssResp, mssErr); te != nil {
		logger.Error(ctx, "error in marking step stale", zap.Error(te), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &woCxPb.UpdateAgentProvidedDataResponse{Status: rpc.StatusInternal()}, nil
	}

	onbStepResp, onbStepErr := s.wealthService.GetNextOnboardingStep(ctx, &woPb.GetNextOnboardingStatusRequest{
		ActorId:        req.GetActorId(),
		WealthFlow:     woPb.WealthFlow_WEALTH_FLOW_INVESTMENT,
		OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
	})
	if te := epifigrpc.RPCError(onbStepResp, onbStepErr); te != nil {
		logger.Error(ctx, "error in calling GetNextOnboardingStep", zap.Error(te), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &woCxPb.UpdateAgentProvidedDataResponse{Status: rpc.StatusInternal()}, nil
	}

	return &woCxPb.UpdateAgentProvidedDataResponse{Status: rpc.StatusOk()}, nil
}

// nolint:funlen
func (s *Service) updateAgentProvidedData(wod *woPb.OnboardingDetails, req *woCxPb.UpdateAgentProvidedDataRequest) error {
	if wod == nil {
		return errors.New("nil wealth onboarding details")
	}
	if wod.AgentProvidedData == nil {
		wod.AgentProvidedData = &woPb.AgentProvidedData{}
	}

	if req.GetKycStatus() != wealth.KraStatus_KRA_STATUS_HOLD && req.GetKycStatus() != wealth.KraStatus_KRA_STATUS_REJECTED {
		return fmt.Errorf("unexpected KYC status: %v", req.GetKycStatus().String())
	}

	wod.GetAgentProvidedData().KycStatus = req.GetKycStatus()
	wod.GetAgentProvidedData().ProvidedAt = timestamp.Now()

	if req.GetPoa() != nil {
		if req.GetPoa().GetS3Paths()[0] == "" {
			return errors.New("s3path not provided for poa")
		} else {
			wod.GetAgentProvidedData().Poa = req.GetPoa()
		}
	}

	if req.GetPan() != nil {
		if req.GetPan().GetS3Paths()[0] == "" {
			return errors.New("s3path not provided for pan")
		} else {
			wod.GetAgentProvidedData().Pan = req.GetPan()
		}
	}

	if req.GetSignature() != nil {
		if req.GetSignature().GetS3Paths()[0] == "" {
			return errors.New("s3path not provided for signature")
		} else {
			wod.GetAgentProvidedData().Signature = req.GetSignature()
		}
	}

	if req.GetUserImage() != nil {
		if req.GetUserImage().GetS3Paths()[0] == "" {
			return errors.New("s3path not provided for user image")
		} else {
			wod.GetAgentProvidedData().UserImage = req.GetUserImage()
		}
	}

	if req.GetAddress() != nil {
		wod.GetAgentProvidedData().Address = req.GetAddress()
	}

	return nil
}

func updatePreInvestmentObject(piod *woPb.OnboardingDetails, req *woCxPb.UpdateAgentProvidedDataRequest) error {
	if piod == nil {
		return errors.New("nil pre investment onboarding details")
	}
	if piod.GetAgentProvidedData() == nil {
		piod.AgentProvidedData = &woPb.AgentProvidedData{}
	}
	piod.GetAgentProvidedData().UserInputPendingData = req.GetUserInputPendingData()
	return nil
}

func (s *Service) updateOnboardingDetailsInDb(ctx context.Context, wod *woPb.OnboardingDetails, piod *woPb.OnboardingDetails) error {
	if wod != nil {
		err := s.onboardingDetailsDao.Update(ctx, wod, []woPb.OnboardingDetailsFieldMask{woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_AGENT_PROVIDED_DATA})
		if err != nil {
			return errors.Wrap(err, "error while updating wod in db")
		}
	}
	if piod != nil {
		err := s.onboardingDetailsDao.Update(ctx, piod, []woPb.OnboardingDetailsFieldMask{woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_AGENT_PROVIDED_DATA})
		if err != nil {
			return errors.Wrap(err, "error while updating piod in db")
		}
	}
	return nil
}
