package cx

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	actorPb "github.com/epifi/gamma/api/actor"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woCxPb "github.com/epifi/gamma/api/wealthonboarding/cx"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

func (s *Service) GetOnboardingDetailsByPan(ctx context.Context, req *woCxPb.GetOnboardingDetailsByPanRequest) (*woCxPb.GetOnboardingDetailsByPanResponse, error) {
	panToWealthOnbDetailsMap, err := s.getWealthOnbDetailsForPans(ctx, req.GetPans())
	if err != nil {
		logger.Error(ctx, "error getting wealth onboarding details for PANs", zap.Error(err))
		return &woCxPb.GetOnboardingDetailsByPanResponse{Status: rpc.StatusInternal()}, nil
	}
	return &woCxPb.GetOnboardingDetailsByPanResponse{
		Status:                  rpc.StatusOk(),
		PanOnboardingDetailsMap: panToWealthOnbDetailsMap,
	}, nil
}

func (s *Service) getWealthOnbDetailsForPans(ctx context.Context, pans []string) (map[string]*woPb.OnboardingDetails, error) {
	actorIdToPanMap, err := s.getActorIdToPanMap(ctx, pans)
	if err != nil {
		return nil, errors.Wrap(err, "error getting actor id to pan map")
	}
	var actorIds []string
	for actorId := range actorIdToPanMap {
		actorIds = append(actorIds, actorId)
	}
	wodList, err := s.onboardingDetailsDao.BatchGetByActorIdsAndOnbType(ctx, actorIds, woPb.OnboardingType_ONBOARDING_TYPE_WEALTH)
	if err != nil {
		return nil, errors.Wrap(err, "error getting onboarding details for batch")
	}
	panToWealthOnbDetailsMap := map[string]*woPb.OnboardingDetails{}
	for _, _wod := range wodList {
		wod, redactErr := s.getNecessaryOnbDetails(ctx, _wod)
		if redactErr != nil {
			return nil, errors.Wrap(redactErr, "error getting necessary onboarding details")
		}
		panToWealthOnbDetailsMap[actorIdToPanMap[wod.GetActorId()]] = wod
	}
	return panToWealthOnbDetailsMap, nil
}

func (s *Service) getActorIdToPanMap(ctx context.Context, pans []string) (map[string]string, error) {
	var userReqs []*user.GetUsersRequest_GetUsersIdentifier
	for _, pan := range pans {
		userReqs = append(userReqs, &user.GetUsersRequest_GetUsersIdentifier{Identifier: &user.GetUsersRequest_GetUsersIdentifier_Pan{Pan: pan}})
	}
	res, err := s.userClient.GetUsers(ctx, &user.GetUsersRequest{Identifier: userReqs})
	if err = epifigrpc.RPCError(res, err); err != nil {
		return nil, errors.Wrap(err, "error getting users")
	}
	if len(res.GetUsers()) == 0 {
		return nil, errors.New("no user found")
	}
	var (
		userIdToPanMap = make(map[string]string)
		actorEntityIds []string
	)
	for _, u := range res.GetUsers() {
		userIdToPanMap[u.GetId()] = u.GetProfile().GetPAN()
		actorEntityIds = append(actorEntityIds, u.GetId())
	}
	actorsRes, err := s.actorClient.GetActorsByEntityIds(ctx, &actorPb.GetActorsByEntityIdsRequest{EntityIds: actorEntityIds})
	if err = epifigrpc.RPCError(actorsRes, err); err != nil {
		return nil, errors.Wrap(err, "error getting actors")
	}
	if len(actorsRes.GetActors()) == 0 {
		return nil, errors.New("no actor found")
	}
	var actorIdToPanMap = make(map[string]string)
	for _, actor := range actorsRes.GetActors() {
		actorIdToPanMap[actor.GetId()] = userIdToPanMap[actor.GetEntityId()]
	}
	return actorIdToPanMap, nil
}

func (s *Service) getNecessaryOnbDetails(ctx context.Context, _wod *woPb.OnboardingDetails) (*woPb.OnboardingDetails, error) {
	// Keep fields that need to be exposed to CX only
	var redactedEsignInfos []*woPb.EsignInfo
	for _, esignInfo := range _wod.GetMetadata().GetKraDocketInfo().GetEsignInfos() {
		redactedEsignInfos = append(redactedEsignInfos, &woPb.EsignInfo{
			Status: esignInfo.GetStatus(),
		})
	}

	wod := &woPb.OnboardingDetails{
		ActorId: _wod.GetActorId(),
		Metadata: &woPb.OnboardingMetadata{
			KraDocketInfo: &woPb.DocketInfo{
				DocAsProof: &types.DocumentProof{S3Paths: _wod.GetMetadata().GetKraDocketInfo().GetDocAsProof().GetS3Paths()},
				EsignInfos: redactedEsignInfos,
			},
			DigilockerData: &woPb.DigilockerData{
				DigilockerAadhaarData: &woPb.DigilockerAadhaarData{
					AadhaarXmlFilePath: _wod.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData().GetAadhaarXmlFilePath(),
				},
			},
		},
		Status:      _wod.GetStatus(),
		CurrentStep: _wod.GetCurrentStep(),
		CreatedAt:   _wod.GetCreatedAt(),
		UpdatedAt:   _wod.GetUpdatedAt(),
		CompletedAt: _wod.GetCompletedAt(),
	}

	// Override docket S3 paths to pre-signed URLs
	docketPaths := wod.GetMetadata().GetKraDocketInfo().GetDocAsProof().GetS3Paths()
	for i, docketPath := range docketPaths {
		preSignedDocketUrl, err := s.docHelper.GetSignedUrl(ctx, docketPath, false, s.conf.S3Conf.Bucket, s.conf.CvlKra.PdfExpiryTime)
		if err != nil {
			return nil, errors.Wrap(err, "error getting pre-signed URL for docket S3 path")
		}
		docketPaths[i] = preSignedDocketUrl
	}

	// Override Aadhaar XML S3 path to pre-signed URL
	digiLockerAadhaarData := wod.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData()
	if digiLockerAadhaarData.GetAadhaarXmlFilePath() != "" {
		preSignedAadhaarXmlUrl, err := s.docHelper.GetSignedUrl(ctx, digiLockerAadhaarData.AadhaarXmlFilePath, false, s.conf.S3Conf.Bucket, s.conf.CvlKra.PdfExpiryTime)
		if err != nil {
			return nil, errors.Wrap(err, "error getting pre-signed URL for Aadhaar XML S3 path")
		}
		digiLockerAadhaarData.AadhaarXmlFilePath = preSignedAadhaarXmlUrl
	}
	return wod, nil
}
