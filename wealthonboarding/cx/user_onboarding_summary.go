package cx

import (
	"context"
	"fmt"
	"path/filepath"
	"reflect"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woCxPb "github.com/epifi/gamma/api/wealthonboarding/cx"
	"github.com/epifi/gamma/wealthonboarding/dao"
)

func (s *Service) GetUserOnboardingSummary(ctx context.Context, req *woCxPb.GetUserOnboardingSummaryRequest) (*woCxPb.GetUserOnboardingSummaryResponse, error) {
	switch req.GetUsecase() {
	case woCxPb.Usecase_USECASE_MANUAL_INTERVENTION_FILTERED:
		onbSummaries, err := s.getOnboardingSummariesStuckInMIandFS(ctx, req.GetStartTs(), req.GetEndTs())
		if err != nil {
			logger.Error(ctx, "error getting onboarding summaries", zap.Error(err))
			return &woCxPb.GetUserOnboardingSummaryResponse{Status: rpc.StatusInternal()}, nil
		}
		if len(onbSummaries) == 0 {
			logger.Error(ctx, "no onboarding summary found")
			return &woCxPb.GetUserOnboardingSummaryResponse{Status: rpc.StatusOk()}, nil
		}
		url, err := s.uploadCsvAndGetPreSignedUrl(ctx, onbSummaries, getFileName(req))
		if err != nil {
			logger.Error(ctx, "error uploading CSV", zap.Error(err))
			return &woCxPb.GetUserOnboardingSummaryResponse{Status: rpc.StatusInternal()}, nil
		}
		return &woCxPb.GetUserOnboardingSummaryResponse{
			Status:         rpc.StatusOk(),
			SummaryFileUrl: url,
		}, nil
	default:
		logger.Error(ctx, fmt.Sprintf("unhandled usecase: %s", req.GetUsecase().String()))
		return &woCxPb.GetUserOnboardingSummaryResponse{Status: rpc.StatusInternal()}, nil
	}
}

func getFileName(req *woCxPb.GetUserOnboardingSummaryRequest) string {
	return fmt.Sprintf("ONB_SUMMARY_%s_%s_%s.csv",
		req.GetUsecase().String(),
		req.GetStartTs().AsTime().In(datetime.IST).Format("2006-01-02"),
		req.GetEndTs().AsTime().In(datetime.IST).Format("2006-01-02"),
	)
}

// getOnboardingSummariesStuckInMIandFS, this gives WOB step details of users who are stuck in MI(manual_intervention) & FS(future_scope)
func (s *Service) getOnboardingSummariesStuckInMIandFS(ctx context.Context, fromTs, toTs *timestampPb.Timestamp) ([]*woPb.OnboardingSummary, error) {
	var summaries []*woPb.OnboardingSummary
	nextPageToken := &pagination.PageToken{}
	hasNextPage := true
	for hasNextPage {
		summaryPage, nextPageTokenRes, err := s.onboardingSummaryDao.GetByFilters(ctx,
			nextPageToken,
			dao.WithOnboardingDetailsUpdatedFrom(fromTs),
			dao.WithOnboardingDetailsUpdatedTill(toTs),
			dao.WithOnboardingStatuses([]woPb.OnboardingStatus{
				woPb.OnboardingStatus_ONBOARDING_STATUS_MANUAL_INTERVENTION_NEEDED,
				woPb.OnboardingStatus_ONBOARDING_STATUS_FUTURE_SCOPE,
			}))
		if err != nil {
			return nil, errors.Wrap(err, fmt.Sprintf("error getting onboarding summary page with token: %v", nextPageToken))
		}
		summaries = append(summaries, summaryPage...)
		hasNextPage = nextPageTokenRes.GetHasAfter()
		if hasNextPage {
			err = nextPageToken.Unmarshal(nextPageTokenRes.GetAfterToken())
			if err != nil {
				return nil, err
			}
		}
	}
	filteredSummaries := getFilteredManualInterventionOnbSummaries(summaries)
	return filteredSummaries, nil
}

func getFilteredManualInterventionOnbSummaries(onbSummaries []*woPb.OnboardingSummary) []*woPb.OnboardingSummary {
	var filteredOnbSummaries []*woPb.OnboardingSummary
	for _, summary := range onbSummaries {
		// TODO(Brijesh): Move away from marking all manual intervention cases as retries exhausted since the previous sub-status is lost
		// When all retries for downloading KYC docket from KRA have been exhausted and KRA still hasn't provided docket over SFTP
		if summary.GetOnboardingDetails().GetCurrentStep() == woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC &&
			summary.GetOnboardingDetails().GetStatus() == woPb.OnboardingStatus_ONBOARDING_STATUS_MANUAL_INTERVENTION_NEEDED &&
			summary.GetCurrentStepDetails().GetStatus() == woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED {
			continue
		}
		filteredOnbSummaries = append(filteredOnbSummaries, summary)
	}
	return filteredOnbSummaries
}

func (s *Service) uploadCsvAndGetPreSignedUrl(ctx context.Context, onbSummaries []*woPb.OnboardingSummary, fileName string) (string, error) {
	if len(onbSummaries) == 0 {
		return "", fmt.Errorf("no onboarding summary found")
	}
	var onbSummaryCsvRecords []*OnboardingSummaryCsvRecord
	for _, summary := range onbSummaries {
		onbSummaryCsvRecords = append(onbSummaryCsvRecords, convertSummaryToCsvRecord(summary))
	}
	var sb strings.Builder
	// Write header row
	sb.WriteString(strings.Join(onboardingSummaryCsvColNames, ","))
	sb.WriteString("\n")

	// Write data rows
	for _, record := range onbSummaryCsvRecords {
		value := reflect.ValueOf(record).Elem()
		var row []string
		for _, colName := range onboardingSummaryCsvColNames {
			row = append(row, value.FieldByName(colName).String())
		}
		sb.WriteString(strings.Join(row, ","))
		sb.WriteString("\n")
	}

	// Upload file
	filePath := filepath.Join("summaries", fileName)
	url, err := s.s3Client.WriteAndGetPreSignedUrl(ctx, filePath, []byte(sb.String()), 30*60)
	if err != nil {
		return "", err
	}
	return url, nil
}

// TODO(Brijesh): Retrieve col names from struct using reflection
var onboardingSummaryCsvColNames = []string{
	"ActorId",
	"OnboardingDetailsId",
	"CurrentStep",
	"Status",
	"OnboardingType",
	"CreatedAt",
	"UpdatedAt",
	"StepDetailsId",
	"StepStatus",
	"StepSubStatus",
	"StepCreatedAt",
}

type OnboardingSummaryCsvRecord struct {
	ActorId             string
	OnboardingDetailsId string

	// String representation of enum
	CurrentStep string

	// String representation of enum
	Status string

	// String representation of enum
	OnboardingType string

	// IST representation of time
	CreatedAt string
	UpdatedAt string

	// Current step's details
	StepDetailsId string

	// String representation of enum
	StepStatus string

	// String representation of enum
	StepSubStatus string

	// IST representation of time
	StepCreatedAt string
}

func convertSummaryToCsvRecord(summary *woPb.OnboardingSummary) *OnboardingSummaryCsvRecord {
	return &OnboardingSummaryCsvRecord{
		ActorId:             summary.GetOnboardingDetails().GetActorId(),
		OnboardingDetailsId: summary.GetOnboardingDetails().GetId(),
		CurrentStep:         summary.GetOnboardingDetails().GetCurrentStep().String(),
		Status:              summary.GetOnboardingDetails().GetStatus().String(),
		OnboardingType:      summary.GetOnboardingDetails().GetOnboardingType().String(),
		CreatedAt:           summary.GetOnboardingDetails().GetCreatedAt().AsTime().In(datetime.IST).Format(time.RFC3339),
		UpdatedAt:           summary.GetOnboardingDetails().GetUpdatedAt().AsTime().In(datetime.IST).Format(time.RFC3339),
		StepDetailsId:       summary.GetCurrentStepDetails().GetId(),
		StepStatus:          summary.GetCurrentStepDetails().GetStatus().String(),
		StepSubStatus:       summary.GetCurrentStepDetails().GetSubStatus().String(),
		StepCreatedAt:       summary.GetCurrentStepDetails().GetCreatedAt().AsTime().In(datetime.IST).Format(time.RFC3339),
	}
}
