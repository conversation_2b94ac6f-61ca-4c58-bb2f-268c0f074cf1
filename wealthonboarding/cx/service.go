package cx

import (
	"github.com/epifi/be-common/pkg/aws/v2/lambda"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	actorPb "github.com/epifi/gamma/api/actor"
	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	invProfile "github.com/epifi/gamma/api/investment/profile"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/wealth/cvl"
	woCxPb "github.com/epifi/gamma/api/wealthonboarding/cx"
	pqPkg "github.com/epifi/gamma/pkg/persistentqueue"
	"github.com/epifi/gamma/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/docket"
	"github.com/epifi/gamma/wealthonboarding/helper"
	manualReview "github.com/epifi/gamma/wealthonboarding/manual_review"
)

type Service struct {
	woCxPb.UnimplementedWealthCxServiceServer
	persistentQueue          pqPkg.PersistentQueue
	onboardingDetailsDao     dao.OnboardingDetailsDao
	mrSvc                    manualReview.IManualReview
	docHelper                helper.DocumentHelper
	lvClient                 livenessPb.LivenessClient
	wealthService            *wealthonboarding.Service
	onboardingStepDetailsDao dao.OnboardingStepDetailsDao
	userClient               user.UsersClient
	actorClient              actorPb.ActorClient
	invProfileClient         invProfile.InvestmentProfileServiceClient
	conf                     *config.Config
	txnExecutor              storagev2.IdempotentTxnExecutor
	cvlVgClient              cvl.CvlClient
	s3Client                 s3.S3Client
	onboardingSummaryDao     dao.OnboardingSummary
	docketExtractionService  *docket.ExtractionService
}

func NewService(
	persistentQueue pqPkg.PersistentQueue,
	onboardingDetailsDao dao.OnboardingDetailsDao,
	mrSvc manualReview.IManualReview,
	documentHelper helper.DocumentHelper,
	lvClient livenessPb.LivenessClient,
	wealthService *wealthonboarding.Service,
	onboardingStepDetailsDao dao.OnboardingStepDetailsDao,
	userClient user.UsersClient,
	actorClient actorPb.ActorClient,
	invProfileClient invProfile.InvestmentProfileServiceClient,
	conf *config.Config,
	txnExecutor storagev2.IdempotentTxnExecutor,
	cvlVgClient cvl.CvlClient,
	s3Client s3.S3Client,
	onboardingSummaryDao dao.OnboardingSummary,
	lambdaClient lambda.LambdaClient,
) *Service {
	return &Service{
		persistentQueue:          persistentQueue,
		onboardingDetailsDao:     onboardingDetailsDao,
		mrSvc:                    mrSvc,
		docHelper:                documentHelper,
		lvClient:                 lvClient,
		wealthService:            wealthService,
		onboardingStepDetailsDao: onboardingStepDetailsDao,
		userClient:               userClient,
		actorClient:              actorClient,
		invProfileClient:         invProfileClient,
		conf:                     conf,
		txnExecutor:              txnExecutor,
		cvlVgClient:              cvlVgClient,
		s3Client:                 s3Client,
		onboardingSummaryDao:     onboardingSummaryDao,
		docketExtractionService:  docket.NewExtractionService(s3Client, onboardingDetailsDao, lambdaClient),
	}
}
