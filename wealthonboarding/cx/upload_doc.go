package cx

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"encoding/base64"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	types "github.com/epifi/gamma/api/typesv2"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woCxPb "github.com/epifi/gamma/api/wealthonboarding/cx"
	"github.com/epifi/be-common/pkg/logger"
)

func (s *Service) UploadDocument(ctx context.Context, req *woCxPb.UploadDocumentRequest) (*woCxPb.UploadDocumentResponse, error) {
	// upload the document to s3 and get s3 path
	upRes, upErr := s.docHelper.UploadDoc(ctx, req.GetActorId(), &types.DocumentProof{
		ProofType: req.GetDocumentType(),
		Photo: []*commontypes.Image{
			{
				ImageType:       req.GetImageType(),
				ImageDataBase64: base64.StdEncoding.EncodeToString(req.GetImageData()),
			},
		},
	})
	if upErr != nil {
		logger.Error(ctx, "error in uploading to s3", zap.Error(upErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &woCxPb.UploadDocumentResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	if len(upRes.GetS3Paths()) == 0 {
		logger.Error(ctx, "no s3 paths in the document after upload", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &woCxPb.UploadDocumentResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	err := s.updateMetadataWithDocument(ctx, req.GetDocumentType(), req.GetActorId(), upRes.GetS3Paths()[0])
	if err != nil {
		logger.Error(ctx, "error in updating filepath in metadata", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &woCxPb.UploadDocumentResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	return &woCxPb.UploadDocumentResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) updateMetadataWithDocument(ctx context.Context, docType types.DocumentProofType, actorId string, docS3Path string) error {
	od, odErr := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, actorId, woPb.OnboardingType_ONBOARDING_TYPE_WEALTH)
	if odErr != nil {
		return errors.Wrap(odErr, "error in getting onboarding details")
	}
	// based on the document type, update the s3 paths in appropriate places in metadata
	switch docType {
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN:
		if od.GetMetadata().GetPanDetails() == nil {
			return errors.New("pan details not present in metadata")
		}
		// pan details can only contain 1 s3 path
		od.GetMetadata().GetPanDetails().S3Paths = []string{
			docS3Path,
		}
		// if pan image was uploaded by the user, update the same in customer provided data as well for consistency
		if len(od.GetMetadata().GetCustomerProvidedData().GetPanOcrAttempts()) != 0 {
			od.GetMetadata().GetCustomerProvidedData().Pan = od.GetMetadata().GetPanDetails()
		}
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_PHOTOGRAPH:
		switch {
		// photograph details can only contain 1 s3 path
		case od.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData().GetUserImageDocument() != nil:
			od.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData().GetUserImageDocument().S3Paths = []string{
				docS3Path,
			}
		case od.GetMetadata().GetCkycData().GetSearchData().GetPhoto() != nil:
			od.GetMetadata().GetCkycData().GetSearchData().GetPhoto().S3Paths = []string{
				docS3Path,
			}
		default:
			return errors.New("photograph not present in metadata")
		}
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_SIGNATURE:
		// signature details can only contain 1 s3 path
		if od.GetMetadata().GetPersonalDetails().GetSignature() == nil {
			return errors.New("signature not present in metadata")
		}
		od.GetMetadata().GetPersonalDetails().GetSignature().S3Paths = []string{
			docS3Path,
		}
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_UNSPECIFIED:
		return errors.New("document type cannot be unspecified")
	default:
		// assumed to be POA
		// poa details can only contain 1 s3 path
		if od.GetMetadata().GetPoaDetails().GetProofType() != docType {
			return fmt.Errorf("document type in request does not match with existing POA type, doc type in request: %v", docType)
		}
		od.GetMetadata().GetPoaDetails().S3Paths = []string{
			docS3Path,
		}
	}
	upErr := s.onboardingDetailsDao.Update(ctx, od, []woPb.OnboardingDetailsFieldMask{
		woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_METADATA,
	})
	if upErr != nil {
		return errors.Wrap(upErr, "error in updating onboarding details dao")
	}
	return nil
}
