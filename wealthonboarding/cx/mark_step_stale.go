package cx

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woCxPb "github.com/epifi/gamma/api/wealthonboarding/cx"
)

func (s *Service) MarkStepStale(ctx context.Context, req *woCxPb.MarkStepStaleRequest) (*woCxPb.MarkStepStaleResponse, error) {
	onboardingDetails, odErr := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, req.GetActorId(), woPb.OnboardingType_ONBOARDING_TYPE_WEALTH)
	if odErr != nil {
		logger.Error(ctx, "failed to get onboarding details by actor-id and onboarding type", zap.Error(odErr))
		return &woCxPb.MarkStepStaleResponse{Status: rpc.StatusInternal()}, errors.Wrap(odErr, "failed to get onboarding details by actor-id and onboarding type")
	}
	// minStepValue for storing the minimum OnboardingStep enum value
	minStepValue := req.GetStep()[0]
	for _, step := range req.GetStep() {
		onboardingStepDetailsResp, osdErr := s.onboardingStepDetailsDao.GetByOnboardingDetailsIdAndStep(ctx, onboardingDetails.GetId(), step)
		if osdErr != nil {
			logger.Error(ctx, "failed to get onboarding step details by onboarding id", zap.Error(osdErr))
			return &woCxPb.MarkStepStaleResponse{Status: rpc.StatusInternal()}, errors.Wrap(osdErr, "failed to get onboarding step details by onboarding id")
		}

		// emptyStepData func to check that given step is not supported or not
		err := emptyStepData(onboardingDetails, step)
		if err != nil {
			logger.Error(ctx, "step in not supported", zap.Error(err))
			return &woCxPb.MarkStepStaleResponse{Status: rpc.StatusInternal()}, err
		}

		onboardingStepDetailsResp.StaledAt = timestampPb.Now()
		// after marking step stale, updates corresponding row for onboarding step details and onboarding details
		stepUpdateErr := s.onboardingStepDetailsDao.UpdateById(ctx, onboardingStepDetailsResp, []woPb.OnboardingStepDetailsFieldMask{woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_STALED_AT})
		if stepUpdateErr != nil {
			logger.Error(ctx, "failed to update step by id", zap.Error(stepUpdateErr))
			return &woCxPb.MarkStepStaleResponse{Status: rpc.StatusInternal()}, errors.Wrap(stepUpdateErr, "failed to update step by id")
		}
		// find minimum OnboardingStep enum value
		if step < minStepValue {
			minStepValue = step
		}
	}
	onboardingDetails.CurrentStep = minStepValue
	onboardingDetails.Status = woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS

	obdUpdateErr := s.onboardingDetailsDao.Update(ctx, onboardingDetails, []woPb.OnboardingDetailsFieldMask{woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_CURRENT_STEP,
		woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_STATUS, woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_METADATA})
	if obdUpdateErr != nil {
		logger.Error(ctx, "failed to update onboarding details", zap.Error(obdUpdateErr))
		return &woCxPb.MarkStepStaleResponse{Status: rpc.StatusInternal()}, errors.Wrap(obdUpdateErr, "failed to update onboarding details")
	}

	return &woCxPb.MarkStepStaleResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func emptyStepData(od *woPb.OnboardingDetails, step woPb.OnboardingStep) error {
	switch step {
	case woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET:
		od.GetMetadata().KraDocketInfo = nil
	case woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_FROM_DIGILOCKER:
		od.GetMetadata().GetCustomerProvidedData().HasDigilockerAccount = commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED
	case woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO, woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA, woPb.OnboardingStep_ONBOARDING_STEP_INVESTMENT_RISK_PROFILING:
	default:
		return errors.Errorf("step not supported, step %v", step.String())
	}
	return nil
}
