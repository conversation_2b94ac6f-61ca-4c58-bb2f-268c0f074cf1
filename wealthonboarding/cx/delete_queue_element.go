package cx

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	pqPb "github.com/epifi/gamma/api/persistentqueue"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woCxPb "github.com/epifi/gamma/api/wealthonboarding/cx"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	pqPkg "github.com/epifi/gamma/pkg/persistentqueue"
)

func (s *Service) DeleteQueueElement(ctx context.Context, req *woCxPb.DeleteQueueElementRequest) (*woCxPb.DeleteQueueElementResponse, error) {
	// do not allow deleting wealth data element if review status is not terminal
	if req.GetPayloadType() == pqPb.PayloadType_PAYLOAD_TYPE_WEALTH_DATA {
		daoRes, daoErr := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, req.GetActorId(), woPb.OnboardingType_ONBOARDING_TYPE_WEALTH)
		if daoErr != nil {
			logger.Error(ctx, "error in getting onboarding details", zap.Error(daoErr))
			return &woCxPb.DeleteQueueElementResponse{Status: rpc.StatusInternal()}, nil
		}
		reviewMap := daoRes.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping()
		for _, v := range reviewMap {
			if v == nil {
				continue
			}
			if len(v.GetReviewAttempts()) == 0 {
				continue
			}
			// if the review status of latest review attempt is not terminal, do not delete the element
			status, sErr := s.mrSvc.CheckStatus(ctx, v.GetReviewAttempts()[len(v.GetReviewAttempts())-1].GetId())
			if sErr != nil {
				logger.Error(ctx, "error in getting review status", zap.Error(sErr))
				return &woCxPb.DeleteQueueElementResponse{Status: rpc.StatusInternal()}, nil
			}
			if !isTerminalReviewStatus(status) {
				logger.Error(ctx, fmt.Sprintf("cannot delete element because status is %v", v.GetReviewAttempts()[len(v.GetReviewAttempts())-1].GetStatus()))
				return &woCxPb.DeleteQueueElementResponse{Status: rpc.StatusInvalidArgument()}, nil
			}
		}
	}
	err := s.persistentQueue.DeleteElement(ctx, &pqPkg.QueueElement{
		ID:          req.GetId(),
		ActorID:     req.GetActorId(),
		PayloadType: req.GetPayloadType(),
	})
	if err != nil {
		logger.Error(ctx, "error while deleting queue elements", zap.Error(err))
		return &woCxPb.DeleteQueueElementResponse{Status: rpc.StatusInternal()}, nil
	}
	if req.GetPayloadType() == pqPb.PayloadType_PAYLOAD_TYPE_WEALTH_DATA {
		// trigger wealth onboarding orchestration once agent closes the case
		res, wErr := s.wealthService.GetNextOnboardingStep(ctx, &woPb.GetNextOnboardingStatusRequest{
			ActorId:        req.GetActorId(),
			OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
		})
		if te := epifigrpc.RPCError(res, wErr); te != nil {
			logger.Error(ctx, "error in GetNextOnboardingStep", zap.Error(te), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		}
	}
	return &woCxPb.DeleteQueueElementResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func isTerminalReviewStatus(status woPb.ReviewStatus) bool {
	return status == woPb.ReviewStatus_REVIEW_STATUS_APPROVED || status == woPb.ReviewStatus_REVIEW_STATUS_REJECTED || status == woPb.ReviewStatus_REVIEW_STATUS_EDITED
}
