package cx

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/postaladdress"

	"github.com/epifi/be-common/api/rpc"
	types "github.com/epifi/gamma/api/typesv2"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woCxPb "github.com/epifi/gamma/api/wealthonboarding/cx"
	epifierrors "github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
)

// nolint: funlen
func (s *Service) GetCustomerProfileDetails(ctx context.Context, req *woCxPb.GetCustomerProfileDetailsRequest) (*woCxPb.GetCustomerProfileDetailsResponse, error) {
	odw, err := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, req.GetActorId(), woPb.OnboardingType_ONBOARDING_TYPE_WEALTH)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &woCxPb.GetCustomerProfileDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		} else {
			logger.Error(ctx, "failed to fetch onboarding details", zap.Error(err))
			return &woCxPb.GetCustomerProfileDetailsResponse{
				Status: rpc.StatusInternal(),
			}, nil
		}
	}
	res := &woCxPb.GetCustomerProfileDetailsResponse{
		Status: rpc.StatusOk(),
		CustomerProfileDetails: &woCxPb.CustomerProfileDetails{
			Name: &commontypes.Name{
				FirstName:  odw.GetMetadata().GetPersonalDetails().GetName().GetFirstName(),
				MiddleName: odw.GetMetadata().GetPersonalDetails().GetName().GetMiddleName(),
				LastName:   mask.GetMaskedString(mask.MaskAllChars, odw.GetMetadata().GetPersonalDetails().GetName().GetLastName()),
				Honorific:  odw.GetMetadata().GetPersonalDetails().GetName().GetHonorific(),
			},
			Gender:      odw.GetMetadata().GetPersonalDetails().GetGender(),
			PhoneNumber: mask.GetMaskedPhoneNumber(odw.GetMetadata().GetPersonalDetails().GetPhoneNumber(), "X"),
			Email:       mask.GetMaskedString(mask.MaskCharTillAtSign, odw.GetMetadata().GetPersonalDetails().GetEmail()),
			BankDetails: &woPb.BankDetails{
				AccountNumber: mask.GetMaskedAccountNumber(odw.GetMetadata().GetBankDetails().GetAccountNumber(), "X"),
				IfscCode:      odw.GetMetadata().GetBankDetails().GetIfscCode(),
				AccountType:   odw.GetMetadata().GetBankDetails().GetAccountType(),
				BankName:      odw.GetMetadata().GetBankDetails().GetBankName(),
				BranchName:    odw.GetMetadata().GetBankDetails().GetBranchName(),
				BankCity:      odw.GetMetadata().GetBankDetails().GetBankCity(),
			},
		},
	}

	odp, err := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, req.GetActorId(), woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Info(ctx, "pre investment details not found", zap.String("actor id", req.GetActorId()))
		} else {
			logger.Error(ctx, "failed to fetch onboarding details", zap.Error(err))
		}
		return res, nil
	}
	count := len(odp.GetMetadata().GetDownloadedKraDocData().GetDownloadAttempts())
	if odp.GetMetadata().GetDownloadedKraDocData().GetDownloadAttempts() != nil && count > 0 {
		details := odp.GetMetadata().GetDownloadedKraDocData().GetDownloadAttempts()[count-1].GetKraDownloadedData().GetPanDetails()
		perAddress := &types.AddressWithType{Type: types.AddressType_PERMANENT, Address: &postaladdress.PostalAddress{
			PostalCode:   details.GetCorPincd(),
			AddressLines: make([]string, 0),
		}}
		corAddress := &types.AddressWithType{Type: types.AddressType_MAILING, Address: &postaladdress.PostalAddress{
			PostalCode:   details.GetCorPincd(),
			AddressLines: make([]string, 0),
		}}
		// masks address lines except first 6 letters of first line
		perAddress.GetAddress().AddressLines = append(perAddress.GetAddress().AddressLines,
			maskExceptFirstNLetters(details.GetPerAdd1(), 6),
			mask.GetMaskedString(mask.MaskAllChars, details.GetPerAdd2()),
			mask.GetMaskedString(mask.MaskAllChars, details.GetPerAdd3()))
		corAddress.GetAddress().AddressLines = append(corAddress.GetAddress().AddressLines,
			maskExceptFirstNLetters(details.GetCorAdd1(), 6),
			mask.GetMaskedString(mask.MaskAllChars, details.GetCorAdd2()),
			mask.GetMaskedString(mask.MaskAllChars, details.GetCorAdd3()))
		res.CustomerProfileDetails.AddressWithType = []*types.AddressWithType{perAddress, corAddress}
	}
	return res, nil
}

func maskExceptFirstNLetters(str string, n int) string {
	if len(str) > n {
		return mask.MaskLastNDigits(str, len(str)-n, "X")
	} else {
		return str
	}
}
