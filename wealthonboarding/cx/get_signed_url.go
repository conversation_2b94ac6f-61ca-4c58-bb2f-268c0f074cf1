package cx

import (
	"context"
	"strings"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	woCxPb "github.com/epifi/gamma/api/wealthonboarding/cx"
	"github.com/epifi/be-common/pkg/logger"
)

func (s *Service) GetSignedUrl(ctx context.Context, req *woCxPb.GetSignedUrlRequest) (*woCxPb.GetSignedUrlResponse, error) {
	s3Paths := strings.Split(req.GetS3Paths(), ",")
	if len(s3Paths) == 0 {
		return &woCxPb.GetSignedUrlResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("empty input s3 paths"),
		}, nil
	}
	var signedUrls []string
	for _, path := range s3Paths {
		// Wealth-ops team can take several days to find the PAN page in docket of users stuck
		// in PAN upload using these signed URLs.
		// Hence, the validity is kept high so that we don't have to provide them new URLs every 24 hours.
		signedUrl, err := s.docHelper.GetSignedUrl(ctx, path, false, s.conf.S3Conf.Bucket, 7*24*60*60)
		if err != nil {
			logger.Error(ctx, "error in getting signed url", zap.Error(err))
		}
		signedUrls = append(signedUrls, signedUrl)
	}
	return &woCxPb.GetSignedUrlResponse{
		Status:     rpc.StatusOk(),
		SignedUrls: signedUrls,
	}, nil
}
