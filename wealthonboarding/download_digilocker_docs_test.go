package wealthonboarding

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	types "github.com/epifi/gamma/api/typesv2"
	digilockerVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/digilocker"
	digiLockerMocks "github.com/epifi/gamma/api/vendorgateway/wealth/digilocker/mocks"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	daoMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/dao"
)

func TestService_DownloadDigilockerDocs(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockOnbDetailsDao := daoMocks.NewMockOnboardingDetailsDao(ctrl)
	mockDigiLockerClient := digiLockerMocks.NewMockDigilockerClient(ctrl)

	ctx := context.Background()
	type args struct {
		ctx context.Context
		req *woPb.DownloadDigilockerDocsRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func()
		want       *woPb.DownloadDigilockerDocsResponse
		wantErr    bool
	}{
		{
			name: "get DigiLocker access tokens",
			args: args{
				ctx: ctx,
				req: &woPb.DownloadDigilockerDocsRequest{
					ActorId:  "randomActorId",
					AuthCode: "randomAuthCode",
				},
			},
			setupMocks: func() {
				mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(ctx, "randomActorId", woPb.OnboardingType_ONBOARDING_TYPE_WEALTH).
					Return(&woPb.OnboardingDetails{Metadata: &woPb.OnboardingMetadata{PanDetails: &types.DocumentProof{Id: "randomPAN"}}}, nil).Times(2)
				mockDigiLockerClient.EXPECT().GetAccessToken(ctx, &digilockerVgPb.GetAccessTokenRequest{
					Header:      &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_DIGILOCKER},
					AuthCode:    "randomPAN",
					ActorId:     "randomActorId",
					AccessRoute: &digilockerVgPb.GetAccessTokenRequest_AuthorizationCode{AuthorizationCode: "randomPAN"},
				}).Return(&digilockerVgPb.GetAccessTokenResponse{
					Status: rpc.StatusOk(),
					TokenResponse: &digilockerVgPb.TokenResponse{
						RefreshToken: "randomRefreshToken",
						Name:         &commontypes.Name{FirstName: "Brijesh"},
					},
				}, nil)
				mockOnbDetailsDao.EXPECT().Update(ctx,
					&woPb.OnboardingDetails{Metadata: &woPb.OnboardingMetadata{
						PanDetails:           &types.DocumentProof{Id: "randomPAN"},
						CustomerProvidedData: &woPb.CustomerProvidedData{HasDigilockerAccount: commontypes.BooleanEnum_TRUE},
						DigilockerData: &woPb.DigilockerData{
							PersonalData: &woPb.DigilockerPersonalData{Name: &commontypes.Name{FirstName: "Brijesh"}},
							RefreshToken: "randomRefreshToken",
						},
					}},
					[]woPb.OnboardingDetailsFieldMask{woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_METADATA},
				).Return(nil)
			},
			want:    &woPb.DownloadDigilockerDocsResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				conf:                 conf,
				onboardingDetailsDao: mockOnbDetailsDao,
				digilockerVgClient:   mockDigiLockerClient,
			}
			tt.setupMocks()
			got, err := s.DownloadDigilockerDocs(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DownloadDigilockerDocs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DownloadDigilockerDocs() got = %v, want %v", got, tt.want)
			}
		})
	}
}
