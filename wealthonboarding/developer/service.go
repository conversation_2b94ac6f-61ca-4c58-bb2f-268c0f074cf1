package developer

import (
	"context"
	"errors"

	gormv2 "gorm.io/gorm"

	rpcPb "github.com/epifi/be-common/api/rpc"
	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	woDevPb "github.com/epifi/gamma/api/wealthonboarding/developer"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

type WoDevService struct {
	fac *DevFactory
}

func NewWoDevService(fac *DevFactory) *WoDevService {
	return &WoDevService{
		fac: fac,
	}
}

func (c *WoDevService) GetEntityList(_ context.Context, _ *cxDsPb.GetEntityListRequest) (*cxDsPb.GetEntityListResponse, error) {
	var entityList []string
	for _, entityEnumNum := range woDevPb.WoEntity_value {
		if entityEnumNum == 0 {
			continue
		}
		entityList = append(entityList, woDevPb.WoEntity(entityEnumNum).String())
	}
	return &cxDsPb.GetEntityListResponse{
		Status:     rpcPb.StatusOk(),
		EntityList: entityList,
	}, nil
}

func (c *WoDevService) GetParameterList(ctx context.Context, req *cxDsPb.GetParameterListRequest) (*cxDsPb.GetParameterListResponse, error) {
	ent, ok := woDevPb.WoEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetParameterListResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available in Connected Account"),
		}, nil
	}
	paramFetcher, err := c.fac.getParameterListImpl(woDevPb.WoEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available in comms")
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	paramList, err := paramFetcher.FetchParamList(ctx, woDevPb.WoEntity(ent))
	if err != nil {
		logger.Error(ctx, "unable to fetch parameter list")
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxDsPb.GetParameterListResponse{
		Status:        rpcPb.StatusOk(),
		ParameterList: paramList,
	}, nil
}

func (c *WoDevService) GetData(ctx context.Context, req *cxDsPb.GetDataRequest) (*cxDsPb.GetDataResponse, error) {
	ent, ok := woDevPb.WoEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetDataResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available in connected_acc"),
		}, nil
	}
	dataFetcher, err := c.fac.getDataImpl(woDevPb.WoEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available in connected_acc")
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	jsonResp, err := dataFetcher.FetchData(ctx, woDevPb.WoEntity(ent), req.GetFilters())
	if err != nil {
		logger.Error(ctx, "unable to get data")
		if errors.Is(err, gormv2.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &cxDsPb.GetDataResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxDsPb.GetDataResponse{
		Status:       rpcPb.StatusOk(),
		JsonResponse: jsonResp,
	}, nil
}
