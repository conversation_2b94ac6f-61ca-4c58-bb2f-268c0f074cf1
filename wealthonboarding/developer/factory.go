package developer

import (
	"fmt"

	woDevPb "github.com/epifi/gamma/api/wealthonboarding/developer"
	"github.com/epifi/gamma/wealthonboarding/developer/processor"
)

type DevFactory struct {
	devWoOnbEntity      *processor.DevWoOnbEntity
	devWoESignEntity    *processor.DevWoESignEntity
	devWoOnbUsersEntity *processor.DevWoOnbUsersEntity
	devWoCKYCDataEntity *processor.DevWoCKYCDataEntity
}

func NewDevFactory(
	devWoOnbEntity *processor.DevWoOnbEntity,
	devWoESignEntity *processor.DevWoESignEntity,
	devWoOnbUsersEntity *processor.DevWoOnbUsersEntity,
	devWoCKYCDataEntity *processor.DevWoCKYCDataEntity,
) *DevFactory {
	return &DevFactory{
		devWoOnbEntity:      devWoOnbEntity,
		devWoESignEntity:    devWoESignEntity,
		devWoOnbUsersEntity: devWoOnbUsersEntity,
		devWoCKYCDataEntity: devWoCKYCDataEntity,
	}
}

func (d *DevFactory) getParameterListImpl(entity woDevPb.WoEntity) (IParameterFetcher, error) {
	switch entity {
	case woDevPb.WoEntity_ONBOARDING_ENTITY:
		return d.devWoOnbEntity, nil
	case woDevPb.WoEntity_ESIGN_ENTITY:
		return d.devWoESignEntity, nil
	case woDevPb.WoEntity_USER_ENTITY:
		return d.devWoOnbUsersEntity, nil
	case woDevPb.WoEntity_CKYC_DATA_ENTITY:
		return d.devWoCKYCDataEntity, nil
	default:
		return nil, fmt.Errorf("no valid implementation found %v", entity)
	}
}

func (d *DevFactory) getDataImpl(entity woDevPb.WoEntity) (IDataFetcher, error) {
	switch entity {
	case woDevPb.WoEntity_ONBOARDING_ENTITY:
		return d.devWoOnbEntity, nil
	case woDevPb.WoEntity_ESIGN_ENTITY:
		return d.devWoESignEntity, nil
	case woDevPb.WoEntity_USER_ENTITY:
		return d.devWoOnbUsersEntity, nil
	case woDevPb.WoEntity_CKYC_DATA_ENTITY:
		return d.devWoCKYCDataEntity, nil
	default:
		return nil, fmt.Errorf("no valid implementation found %v", entity)
	}
}
