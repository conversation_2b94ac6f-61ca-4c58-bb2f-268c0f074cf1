package developer

import (
	"context"

	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	woDevPb "github.com/epifi/gamma/api/wealthonboarding/developer"
)

type IParameterFetcher interface {
	FetchParamList(ctx context.Context, entity woDevPb.WoEntity) ([]*cxDsPb.ParameterMeta, error)
}

type IDataFetcher interface {
	FetchData(ctx context.Context, entity woDevPb.WoEntity, filters []*cxDsPb.Filter) (string, error)
}
