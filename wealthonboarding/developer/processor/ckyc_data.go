package processor

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/wealthonboarding/ckyc"
	woDevPb "github.com/epifi/gamma/api/wealthonboarding/developer"
	"github.com/epifi/be-common/pkg/logger"
	dao2 "github.com/epifi/gamma/wealthonboarding/dao"
)

type DevWoCKYCDataEntity struct {
	ckycDataDao dao2.CkycDataDao
}

func NewDevWoCKYCDataEntity(ckycDataDao dao2.CkycDataDao) *DevWoCKYCDataEntity {
	return &DevWoCKYCDataEntity{
		ckycDataDao: ckycDataDao,
	}
}

const (
	CKYCDataTypeInputName  = "ckyc_data_type"
	CKYCDataTypeInputLabel = "Type of CKYC Data"
)

func (d *DevWoCKYCDataEntity) FetchParamList(_ context.Context, _ woDevPb.WoEntity) ([]*db_state.ParameterMeta, error) {
	var ckycDataTypes []string
	for i, ckycDataType := range ckyc.CkycDataType_name {
		if i == 0 {
			continue
		}
		ckycDataTypes = append(ckycDataTypes, ckycDataType)
	}

	paramList := []*db_state.ParameterMeta{
		{
			Name:            ActorId,
			Label:           ActorIdLabel,
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		}, {
			Name:            CKYCDataTypeInputName,
			Label:           CKYCDataTypeInputLabel,
			Type:            db_state.ParameterDataType_DROPDOWN,
			ParameterOption: db_state.ParameterOption_MANDATORY,
			Options:         ckycDataTypes,
		},
	}
	return paramList, nil
}

func (d *DevWoCKYCDataEntity) FetchData(ctx context.Context, _ woDevPb.WoEntity, filters []*db_state.Filter) (string, error) {
	var marshalledRes []byte
	marshalOptions := protojson.MarshalOptions{}
	marshalOptions.UseEnumNumbers = false

	ckycData, err := d.getMaskedCKYCData(ctx, filters)
	if err != nil {
		logger.Error(ctx, "error getting masked ckyc data", zap.Error(err))
		marshalledRes = []byte(err.Error())
		return string(marshalledRes), nil
	}

	marshalledRes, err = marshalOptions.Marshal(ckycData)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error marshalling to json: %v", ckycData), zap.Error(err))
		marshalledRes = []byte(fmt.Sprintf("error marshalling to json: %v, err: %s", ckycData, err.Error()))
		return string(marshalledRes), nil
	}
	return string(marshalledRes), nil
}

func (d *DevWoCKYCDataEntity) getMaskedCKYCData(ctx context.Context, filters []*db_state.Filter) (*ckyc.CkycData, error) {
	if len(filters) == 0 {
		return nil, fmt.Errorf("no filters found")
	}
	var (
		actorId      string
		ckycDataType ckyc.CkycDataType
	)
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case ActorId:
			actorId = filter.GetStringValue()
		case CKYCDataTypeInputName:
			ckycDataTypeEnumNum, ok := ckyc.CkycDataType_value[filter.GetDropdownValue()]
			if !ok {
				return nil, fmt.Errorf("error mapping ckyc data type dropdown value: %s", filter.GetDropdownValue())
			}
			ckycDataType = ckyc.CkycDataType(ckycDataTypeEnumNum)
		}
	}
	ckycData, err := d.ckycDataDao.GetByActorIdAndType(ctx, actorId, ckycDataType)
	if err != nil {
		return nil, fmt.Errorf("error getting ckyc data")
	}
	maskCKYCData(ctx, ckycData)
	return ckycData, nil
}
