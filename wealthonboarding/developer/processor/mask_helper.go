package processor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"strconv"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/encoding/protojson"

	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/api/wealthonboarding/ckyc"
	userPb "github.com/epifi/gamma/api/wealthonboarding/user"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
)

func maskOnbDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails) {
	if od.GetMetadata().GetPanDetails() != nil {
		od.GetMetadata().PanDetails = maskDocumentProof(ctx, od.GetMetadata().GetPanDetails())
	}
	if od.GetMetadata().GetPoiDetails() != nil {
		od.GetMetadata().PoiDetails = maskDocumentProof(ctx, od.GetMetadata().GetPoiDetails())
	}
	if od.GetMetadata().GetPoaDetails() != nil {
		od.GetMetadata().PoaDetails = maskDocumentProof(ctx, od.GetMetadata().GetPoaDetails())
	}
	if od.GetMetadata().GetPersonalDetails().GetPhoneNumber() != nil {
		od.GetMetadata().GetPersonalDetails().PhoneNumber = partialMaskPhoneNumber(ctx, od.GetMetadata().GetPersonalDetails().GetPhoneNumber())
	}
	if od.GetMetadata().GetCustomerProvidedData().GetPan() != nil {
		od.GetMetadata().GetCustomerProvidedData().Pan = maskDocumentProof(ctx, od.GetMetadata().GetCustomerProvidedData().GetPan())
	}
	if od.GetMetadata().GetCustomerProvidedData().GetPoa() != nil {
		od.GetMetadata().GetCustomerProvidedData().Poa = maskDocumentProof(ctx, od.GetMetadata().GetCustomerProvidedData().GetPoa())
	}
	if od.GetMetadata().GetPersonalDetails().GetEmail() != "" {
		od.GetMetadata().GetPersonalDetails().Email = mask.GetMaskedString(mask.MaskCharTillAtSign, od.GetMetadata().GetPersonalDetails().GetEmail())
	}
	if od.GetMetadata().GetPersonalDetails().GetDob() != nil {
		od.GetMetadata().GetPersonalDetails().Dob = maskDob(od.GetMetadata().GetPersonalDetails().GetDob())
	}
	if od.GetMetadata().GetPersonalDetails().GetFatherName() != nil {
		od.GetMetadata().GetPersonalDetails().FatherName = maskName(od.GetMetadata().GetPersonalDetails().GetFatherName())
	}
	if od.GetMetadata().GetPersonalDetails().GetMotherName() != nil {
		od.GetMetadata().GetPersonalDetails().MotherName = maskName(od.GetMetadata().GetPersonalDetails().GetMotherName())
	}
	if od.GetMetadata().GetKraData() != nil {
		od.GetMetadata().KraData = getFieldsFromKraData(od.GetMetadata().GetKraData())
	}
	if od.GetMetadata().GetCkycData() != nil {
		od.GetMetadata().CkycData = getFieldsFromCkycData(od.GetMetadata().GetCkycData())
	}
	if od.GetMetadata().GetDigilockerData().GetRefreshToken() != "" {
		od.GetMetadata().GetDigilockerData().RefreshToken = maskString(od.GetMetadata().GetDigilockerData().RefreshToken)
	}
}

// getFieldsFromCkycData returns only selective fields from CkycData
func getFieldsFromCkycData(data *wealthonboarding.CkycData) *wealthonboarding.CkycData {
	searchData := data.GetSearchData()
	downloadData := data.GetDownloadData()
	return &wealthonboarding.CkycData{
		SearchData: &wealthonboarding.CkycSearchData{
			CkycNumber:      searchData.GetCkycNumber(),
			Photo:           searchData.GetPhoto(),
			KycDate:         searchData.GetKycDate(),
			UpdatedDate:     searchData.GetUpdatedDate(),
			CkycAccountType: searchData.GetCkycAccountType(),
		},
		DownloadData: &wealthonboarding.CkycDownloadData{
			PersonalDetails: &wealthonboarding.CkycPersonalDetails{
				AccountType:      downloadData.GetPersonalDetails().GetAccountType(),
				Name:             downloadData.GetPersonalDetails().GetName(),
				Gender:           downloadData.GetPersonalDetails().GetGender(),
				Nationality:      downloadData.GetPersonalDetails().GetNationality(),
				PermanentAddress: downloadData.GetPersonalDetails().GetPermanentAddress(),
				CurrentAddress:   downloadData.GetPersonalDetails().GetCurrentAddress(),
				ProofOfAddress:   downloadData.GetPersonalDetails().GetProofOfAddress(),
				Remarks:          downloadData.GetPersonalDetails().GetRemarks(),
			},
		},
	}
}

// getFieldsFromKraData returns only selective fields from KraData
// nolint:funlen
func getFieldsFromKraData(data *wealthonboarding.KraData) *wealthonboarding.KraData {
	panStatusDetails := data.GetStatusData().GetPanStatusDetails()
	panDownloadDetails := data.GetDownloadedData().GetPanDetails()
	return &wealthonboarding.KraData{
		StatusData: &wealthonboarding.KraStatusData{PanStatusDetails: &wealthonboarding.KraStatusData_PanStatusDetails{
			Status:                panStatusDetails.GetStatus(),
			LastUpdateDate:        panStatusDetails.GetLastUpdateDate(),
			CreatedDate:           panStatusDetails.GetCreatedDate(),
			ModifiedDate:          panStatusDetails.GetModifiedDate(),
			AppStatusDelta:        panStatusDetails.GetAppStatusDelta(),
			UpdateStatus:          panStatusDetails.GetUpdateStatus(),
			HoldDeactivateRemarks: panStatusDetails.GetHoldDeactivateRemarks(),
			UpdateRemarks:         panStatusDetails.GetUpdateRemarks(),
			KycMode:               panStatusDetails.GetKycMode(),
			IpvFlag:               panStatusDetails.GetIpvFlag(),
			UboFlag:               panStatusDetails.GetUboFlag(),
			KraCode:               panStatusDetails.GetKraCode(),
			CorAddProof:           panStatusDetails.GetCorAddProof(),
			PerAddProof:           panStatusDetails.GetPerAddProof(),
		}},
		DownloadedData: &wealthonboarding.KraDownloadedData{
			PanDetails: &wealthonboarding.KraDownloadedData_DownloadedPanDetails{
				UpdateFlag:               panDownloadDetails.GetUpdateFlag(),
				Type:                     panDownloadDetails.GetType(),
				Date:                     panDownloadDetails.GetDate(),
				Exmt:                     panDownloadDetails.GetExmt(),
				ExmtCat:                  panDownloadDetails.GetExmtCat(),
				Name:                     panDownloadDetails.GetName(),
				IpvFlag:                  panDownloadDetails.GetIpvFlag(),
				IpvDate:                  panDownloadDetails.GetIpvDate(),
				Gen:                      panDownloadDetails.GetGen(),
				RegNo:                    panDownloadDetails.GetRegNo(),
				DobDt:                    panDownloadDetails.GetDobDt(),
				DoiDt:                    panDownloadDetails.GetDoiDt(),
				CommenceDt:               panDownloadDetails.GetCommenceDt(),
				Nationality:              panDownloadDetails.GetNationality(),
				OthNationality:           panDownloadDetails.GetOthNationality(),
				CompStatus:               panDownloadDetails.GetCompStatus(),
				OthCompStatus:            panDownloadDetails.GetOthCompStatus(),
				ResStatus:                panDownloadDetails.GetResStatus(),
				ResStatusProof:           panDownloadDetails.GetResStatusProof(),
				UidNo:                    panDownloadDetails.GetUidNo(),
				CorAdd1:                  panDownloadDetails.GetCorAdd1(),
				CorAdd2:                  panDownloadDetails.GetCorAdd2(),
				CorAdd3:                  panDownloadDetails.GetCorAdd3(),
				CorCity:                  panDownloadDetails.GetCorCity(),
				CorPincd:                 panDownloadDetails.GetCorPincd(),
				CorState:                 panDownloadDetails.GetCorState(),
				CorCtry:                  panDownloadDetails.GetCorCtry(),
				CorAddProof:              panDownloadDetails.GetCorAddProof(),
				CorAddRef:                panDownloadDetails.GetCorAddRef(),
				CorAddDt:                 panDownloadDetails.GetCorAddDt(),
				PerAdd1:                  panDownloadDetails.GetPerAdd1(),
				PerAdd2:                  panDownloadDetails.GetPerAdd2(),
				PerAdd3:                  panDownloadDetails.GetPerAdd3(),
				PerCity:                  panDownloadDetails.GetPerCity(),
				PerPincd:                 panDownloadDetails.GetPerPincd(),
				PerState:                 panDownloadDetails.GetPerState(),
				PerCtry:                  panDownloadDetails.GetPerCtry(),
				PerAddProof:              panDownloadDetails.GetPerAddProof(),
				PerAddRef:                panDownloadDetails.GetPerAddRef(),
				PerAddDt:                 panDownloadDetails.GetPerAddDt(),
				Income:                   panDownloadDetails.GetIncome(),
				Occ:                      panDownloadDetails.GetOcc(),
				OthOcc:                   panDownloadDetails.GetOthOcc(),
				PolConn:                  panDownloadDetails.GetPolConn(),
				DocProof:                 panDownloadDetails.GetDocProof(),
				InternalRef:              panDownloadDetails.GetInternalRef(),
				BranchCode:               panDownloadDetails.GetBranchCode(),
				MarStatus:                panDownloadDetails.GetMarStatus(),
				Netwrth:                  panDownloadDetails.GetNetwrth(),
				NetworthDt:               panDownloadDetails.GetNetworthDt(),
				IncorpPlc:                panDownloadDetails.GetIncorpPlc(),
				Otherinfo:                panDownloadDetails.GetOtherinfo(),
				Filler1:                  panDownloadDetails.GetFiller1(),
				Filler2:                  panDownloadDetails.GetFiller2(),
				Filler3:                  panDownloadDetails.GetFiller3(),
				Status:                   panDownloadDetails.GetStatus(),
				Statusdt:                 panDownloadDetails.GetStatusdt(),
				ErrorDesc:                panDownloadDetails.GetErrorDesc(),
				DumpType:                 panDownloadDetails.GetDumpType(),
				Dnlddt:                   panDownloadDetails.GetDnlddt(),
				KraInfo:                  panDownloadDetails.GetKraInfo(),
				Signature:                panDownloadDetails.GetSignature(),
				IopFlg:                   panDownloadDetails.GetIopFlg(),
				PosCode:                  panDownloadDetails.GetPosCode(),
				SummRec:                  panDownloadDetails.GetSummRec(),
				KycMode:                  panDownloadDetails.GetKycMode(),
				AppRemarks:               panDownloadDetails.GetAppRemarks(),
				KycStatus:                panDownloadDetails.GetKycStatus(),
				CorrAddress:              panDownloadDetails.GetCorrAddress(),
				PermAddress:              panDownloadDetails.GetPermAddress(),
				IncomeSlab:               panDownloadDetails.GetIncomeSlab(),
				PoliticallyExposedStatus: panDownloadDetails.GetPoliticallyExposedStatus(),
			},
		},
	}
}

func maskName(name *commontypes.Name) *commontypes.Name {
	return &commontypes.Name{
		FirstName:  maskString(name.GetFirstName()),
		MiddleName: maskString(name.GetMiddleName()),
		LastName:   maskString(name.GetLastName()),
		Honorific:  maskString(name.GetHonorific()),
	}
}

func maskDob(dob *date.Date) *date.Date {
	return &date.Date{
		Year:  dob.GetYear(),
		Month: int32(mask.GetMaskedInt(dob.GetMonth())),
		Day:   int32(mask.GetMaskedInt(dob.GetDay())),
	}
}

func maskString(s string) string {
	return mask.GetMaskedString(mask.MaskAllChars, s)
}

//nolint:dupl
func partialMaskPhoneNumber(ctx context.Context, number *commontypes.PhoneNumber) *commontypes.PhoneNumber {
	maskedRes := &commontypes.PhoneNumber{}
	maskedRes.CountryCode = number.GetCountryCode()
	strNumber := strconv.FormatUint(number.GetNationalNumber(), 10)
	if len(strNumber) == 10 {
		maskedNumber, maskErr := strconv.ParseUint("11111111"+strNumber[8:], 10, 64)
		if maskErr == nil {
			maskedRes.NationalNumber = maskedNumber
		} else {
			logger.Error(ctx, "error while masking phone number", zap.Error(maskErr))
		}
	}
	return maskedRes
}

//nolint:dupl
func maskDocumentProof(ctx context.Context, details *types.DocumentProof) *types.DocumentProof {
	maskedRes := &types.DocumentProof{}
	mRes, mErr := protojson.Marshal(details)
	if mErr != nil {
		logger.Error(ctx, "error while marshalling document proof", zap.Error(mErr))
		return nil
	}
	redactRes, redactErr := jsonRedactor.Redact(mRes, redactionConf)
	if redactErr != nil {
		logger.Error(ctx, "error while redacting document proof", zap.Error(redactErr))
		return nil
	}
	umErr := protojson.Unmarshal(redactRes, maskedRes)
	if umErr != nil {
		logger.Error(ctx, "error while unmarshalling masked document proof", zap.Error(umErr))
		return nil
	}
	return maskedRes
}

func maskUserDetails(ctx context.Context, ud *userPb.User) {
	if ud.GetMetadata().GetCustomerIpAddress() != "" {
		ud.GetMetadata().CustomerIpAddress = mask.GetMaskedString(mask.DontMaskLastFourChars, ud.GetMetadata().GetCustomerIpAddress())
	}
	if ud.GetPersonalDetails().GetPhoneNumber() != nil {
		ud.GetPersonalDetails().PhoneNumber = partialMaskPhoneNumber(ctx, ud.GetPersonalDetails().GetPhoneNumber())
	}
	if ud.GetPersonalDetails().GetEmail() != "" {
		ud.GetPersonalDetails().Email = mask.GetMaskedString(mask.MaskCharTillAtSign, ud.GetPersonalDetails().GetEmail())
	}
	if ud.GetPersonalDetails().GetDob() != nil {
		ud.GetPersonalDetails().Dob = maskDob(ud.GetPersonalDetails().GetDob())
	}
	if ud.GetPersonalDetails().GetFatherName() != nil {
		ud.GetPersonalDetails().FatherName = maskName(ud.GetPersonalDetails().GetFatherName())
	}
	if ud.GetPersonalDetails().GetMotherName() != nil {
		ud.GetPersonalDetails().MotherName = maskName(ud.GetPersonalDetails().GetMotherName())
	}
	if ud.GetPersonalDetails().GetPanDetails().GetId() != "" {
		ud.GetPersonalDetails().GetPanDetails().Id = mask.GetMaskedString(mask.DontMaskLastFourChars, ud.GetPersonalDetails().GetPanDetails().GetId())
	}
	if ud.GetKycData().GetStatusData().GetPanStatusDetails().GetPanNumber() != "" {
		ud.GetKycData().GetStatusData().GetPanStatusDetails().PanNumber = mask.GetMaskedString(mask.DontMaskLastFourChars, ud.GetKycData().GetStatusData().GetPanStatusDetails().GetPanNumber())
	}
}

func maskCKYCData(ctx context.Context, ckycData *ckyc.CkycData) {
	if ckycData.GetCkycPayload().GetCkycSearchData() != nil {
		maskCKYCSearchData(ckycData.GetCkycPayload().GetCkycSearchData())
	}
	if ckycData.GetCkycPayload().GetCkycDownloadData() != nil {
		maskCKYCDownloadData(ctx, ckycData.GetCkycPayload().GetCkycDownloadData())
	}
}

func maskCKYCSearchData(data *wealthonboarding.CkycSearchData) {
	if data.GetFathersName() != nil {
		data.FathersName = maskName(data.GetFathersName())
	}
}

func maskCKYCDownloadData(ctx context.Context, data *wealthonboarding.CkycDownloadData) {
	if data.GetPersonalDetails().GetFathersName() != nil {
		data.PersonalDetails.FathersName = maskName(data.PersonalDetails.FathersName)
	}
	if data.GetPersonalDetails().GetMothersName() != nil {
		data.PersonalDetails.MothersName = maskName(data.PersonalDetails.MothersName)
	}
	if data.GetPersonalDetails().GetMothersName() != nil {
		data.PersonalDetails.MothersName = maskName(data.PersonalDetails.MothersName)
	}
	if data.GetPersonalDetails().GetDob() != nil {
		data.PersonalDetails.Dob = maskDob(data.PersonalDetails.Dob)
	}
	if data.GetPersonalDetails().GetTelOffice() != nil {
		data.PersonalDetails.TelOffice = partialMaskLandline(data.PersonalDetails.TelOffice)
	}
	if data.GetPersonalDetails().GetTelOffice() != nil {
		data.PersonalDetails.TelResidential = partialMaskLandline(data.PersonalDetails.TelResidential)
	}
	if data.GetPersonalDetails().GetMobileNumber() != nil {
		data.PersonalDetails.MobileNumber = partialMaskPhoneNumber(ctx, data.PersonalDetails.MobileNumber)
	}
	if data.GetPersonalDetails().GetEmailId() != "" {
		data.PersonalDetails.EmailId = mask.GetMaskedString(mask.MaskCharTillAtSign, data.PersonalDetails.EmailId)
	}
	for i := range data.GetPersonalDetails().GetDocumentsList() {
		if data.PersonalDetails.DocumentsList[i] != nil {
			data.PersonalDetails.DocumentsList[i] = maskDocumentProof(ctx, data.PersonalDetails.DocumentsList[i])
		}
	}
	if data.GetPersonalDetails().GetPan() != "" {
		data.PersonalDetails.Pan = mask.GetMaskedString(mask.DontMaskLastFourChars, data.PersonalDetails.Pan)
	}
}

func partialMaskLandline(landline *commontypes.Landline) *commontypes.Landline {
	number := "<fully masked landline number>"
	if len(landline.GetNumber()) > 8 {
		number = "<11111111>" + landline.GetNumber()[8:]
	}
	return &commontypes.Landline{
		StdCode: landline.GetStdCode(),
		Number:  number,
	}
}
