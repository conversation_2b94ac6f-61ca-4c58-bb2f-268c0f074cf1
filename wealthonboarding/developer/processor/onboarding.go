package processor

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/wealthonboarding"
	woDevPb "github.com/epifi/gamma/api/wealthonboarding/developer"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/redactor"
	"github.com/epifi/gamma/wealthonboarding/dao"
)

const (
	ActorId             = "Actor_Id"
	ActorIdLabel        = "actor id of user"
	WoId                = "Wealth_Onboarding_Id"
	WoIdLabel           = "primary key of the onboarding details table"
	dbFetchError        = "{\"error\": \"error in getting details from db \"}"
	marshalErr          = "{\"error\": \"error in marshal response \"}"
	OnboardingType      = "Onboarding_Type"
	OnboardingTypeLabel = "onboarding type"
)

var jsonRedactor = redactor.JsonRedactor{}

// TODO(ismail): add more sensitive keys
var redactionConf = map[string]mask.MaskingStrategy{
	"id":             mask.DontMaskFirstTwoAndLastTwoChars, // Key is used in types.DocumentProof
	"nationalNumber": mask.DontMaskLastFourChars,           // Key is used in types.PhoneNumber
}

type DevWoOnbEntity struct {
	onboaringDetailsDao      dao.OnboardingDetailsDao
	onboardingStepDetailsDao dao.OnboardingStepDetailsDao
}

func NewDevWoOnbEntity(detailsDao dao.OnboardingDetailsDao, stepDetailsDao dao.OnboardingStepDetailsDao) *DevWoOnbEntity {
	return &DevWoOnbEntity{
		onboaringDetailsDao:      detailsDao,
		onboardingStepDetailsDao: stepDetailsDao,
	}
}

func (d *DevWoOnbEntity) FetchParamList(ctx context.Context, entity woDevPb.WoEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            ActorId,
			Label:           ActorIdLabel,
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		}, {
			Name:            WoId,
			Label:           WoIdLabel,
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		}, {
			Name:  OnboardingType,
			Label: OnboardingTypeLabel,
			Type:  db_state.ParameterDataType_DROPDOWN,
			Options: []string{
				wealthonboarding.OnboardingType_ONBOARDING_TYPE_WEALTH.String(),
				wealthonboarding.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT.String(),
			},
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
	}
	return paramList, nil
}

func (d *DevWoOnbEntity) FetchData(ctx context.Context, entity woDevPb.WoEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}
	var actorId, woId string
	var onbType wealthonboarding.OnboardingType
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case ActorId:
			actorId = filter.GetStringValue()
		case WoId:
			woId = filter.GetStringValue()
		case OnboardingType:
			onbTypeInt, ok := wealthonboarding.OnboardingType_value[filter.GetDropdownValue()]
			if !ok {
				return "", errors.New("Invalid Onboarding type")
			}
			onbType = wealthonboarding.OnboardingType(onbTypeInt)
		}
	}
	// fetch using consent_request_id
	if actorId != "" {
		return d.getEntityByActorId(ctx, actorId, onbType)
	}
	// fetch using consent_id
	if woId != "" {
		return d.getEntityByWoId(ctx, woId)
	}
	return "", nil
}

func (d *DevWoOnbEntity) getEntityByActorId(ctx context.Context, actorId string, onbType wealthonboarding.OnboardingType) (string, error) {
	resp := &woDevPb.OnboardingEntityResponse{}
	od, err := d.onboaringDetailsDao.GetByActorIdAndOnbType(ctx, actorId, onbType)
	if err != nil {
		logger.Error(ctx, "error while getting od using actor_id", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return dbFetchError, nil
	}
	osd, err := d.onboardingStepDetailsDao.GetByOnboardingDetailsId(ctx, od.GetId())
	if err != nil {
		logger.Error(ctx, "error while getting osd using actor_id", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId), zap.String("onb_id", od.GetId()))
		return dbFetchError, nil
	}
	maskOnbDetails(ctx, od)
	resp.OnboardingDetails = od
	resp.OnboardingStepDetailss = osd
	mres, merr := protojson.Marshal(resp)
	if merr != nil {
		logger.Error(ctx, "error while marshalling response", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return marshalErr, nil
	}
	return string(mres), nil
}

func (d *DevWoOnbEntity) getEntityByWoId(ctx context.Context, woId string) (string, error) {
	resp := &woDevPb.OnboardingEntityResponse{}
	od, err := d.onboaringDetailsDao.GetById(ctx, woId)
	if err != nil {
		logger.Error(ctx, "error while getting od using actor_id", zap.Error(err), zap.String("onb_id", woId))
		return dbFetchError, nil
	}
	osd, err := d.onboardingStepDetailsDao.GetByOnboardingDetailsId(ctx, od.GetId())
	if err != nil {
		logger.Error(ctx, "error while getting osd using actor_id", zap.Error(err), zap.String("onb_id", woId))
		return dbFetchError, nil
	}
	maskOnbDetails(ctx, od)
	resp.OnboardingDetails = od
	resp.OnboardingStepDetailss = osd
	mRes, mErr := protojson.Marshal(resp)
	if mErr != nil {
		logger.Error(ctx, "error while marshalling response", zap.Error(err), zap.String("onb_id", woId))
		return marshalErr, nil
	}
	return string(mRes), nil
}
