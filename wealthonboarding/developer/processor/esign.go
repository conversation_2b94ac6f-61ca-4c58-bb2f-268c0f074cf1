package processor

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	woDevPb "github.com/epifi/gamma/api/wealthonboarding/developer"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/wealthonboarding/dao"
)

const (
	ESignId      = "e_sign_id"
	ESignIdLabel = "e sign id of the document"
)

type DevWoESignEntity struct {
	eSignDetailsDao dao.ESignDetailsDao
}

func NewDDevWoESignEntity(detailsDao dao.ESignDetailsDao) *DevWoESignEntity {
	return &DevWoESignEntity{
		eSignDetailsDao: detailsDao,
	}
}

func (d *DevWoESignEntity) FetchParamList(ctx context.Context, entity woDevPb.WoEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            ESignId,
			Label:           ESignIdLabel,
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (d *DevWoESignEntity) FetchData(ctx context.Context, entity woDevPb.WoEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}
	var eSignId string
	for _, filter := range filters {
		if filter.GetParameterName() == ESignId {
			eSignId = filter.GetStringValue()
			break
		}
	}
	// fetch using consent_request_id
	if eSignId != "" {
		return d.getEntityById(ctx, eSignId)
	}
	return "", nil
}

func (d *DevWoESignEntity) getEntityById(ctx context.Context, eSignId string) (string, error) {
	esd, err := d.eSignDetailsDao.Get(ctx, eSignId)
	if err != nil {
		logger.Error(ctx, "error in fetching e_sign_details from db", zap.Error(err), zap.String("e_sign_id", eSignId))
		return dbFetchError, nil
	}
	mRes, mErr := protojson.Marshal(esd)
	if mErr != nil {
		logger.Error(ctx, "error while marshalling response", zap.Error(mErr))
		return marshalErr, nil
	}
	return string(mRes), nil
}
