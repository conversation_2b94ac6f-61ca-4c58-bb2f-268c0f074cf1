package processor

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	woDevPb "github.com/epifi/gamma/api/wealthonboarding/developer"
	userPb "github.com/epifi/gamma/api/wealthonboarding/user"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/wealthonboarding/dao"
)

const (
	UserId      = "wealth_onboarding_user_Id"
	UserIdLabel = "primary key of the users table"
)

type DevWoOnbUsersEntity struct {
	wonbUsersDao dao.UserDao
}

func NewDevWoOnbUsersEntity(userDao dao.UserDao) *DevWoOnbUsersEntity {
	return &DevWoOnbUsersEntity{
		wonbUsersDao: userDao,
	}
}

func (d *DevWoOnbUsersEntity) FetchParamList(ctx context.Context, entity woDevPb.WoEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            ActorId,
			Label:           ActorIdLabel,
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		}, {
			Name:            UserId,
			Label:           UserIdLabel,
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (d *DevWoOnbUsersEntity) FetchData(ctx context.Context, entity woDevPb.WoEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}
	var actorId, userId string
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case ActorId:
			actorId = filter.GetStringValue()
		case UserId:
			userId = filter.GetStringValue()
		}
	}
	// fetch using actorId or user id
	switch {
	case actorId != "":
		return d.getUserEntity(ctx, actorId, "")
	case userId != "":
		return d.getUserEntity(ctx, "", userId)
	default:
		return "", errors.New("actorId and userId both are empty")
	}
}

func (d *DevWoOnbUsersEntity) getUserEntity(ctx context.Context, actorId string, userId string) (string, error) {
	udResp := &userPb.User{}
	var err error

	if actorId != "" {
		udResp, err = d.wonbUsersDao.GetByActorId(ctx, actorId)
		if err != nil {
			logger.Error(ctx, "error while getting user details using actor_id", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
			return dbFetchError, nil
		}
	} else {
		udResp, err = d.wonbUsersDao.GetById(ctx, userId)
		if err != nil {
			logger.Error(ctx, "error while getting user details using id of user's table", zap.Error(err))
			return dbFetchError, nil
		}
	}

	maskUserDetails(ctx, udResp)
	mRes, mErr := protojson.Marshal(udResp)
	if mErr != nil {
		logger.Error(ctx, "error while marshalling response", zap.Error(mErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return marshalErr, nil
	}
	return string(mRes), nil
}
