package activity

import (
	"flag"
	"os"
	"testing"

	"github.com/golang/mock/gomock"

	lambdaMocks "github.com/epifi/be-common/pkg/aws/lambda/mocks"
	mockS3 "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	dateTimeMock "github.com/epifi/be-common/pkg/datetime/mocks"
	epifiTemporalLogging "github.com/epifi/be-common/pkg/epifitemporal/logging"
	epifitemporalTest "github.com/epifi/be-common/pkg/epifitemporal/test"
	"github.com/epifi/be-common/pkg/logger"
	storagev2Mocks "github.com/epifi/be-common/pkg/storage/v2/mocks"

	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	catalogMocks "github.com/epifi/gamma/api/investment/mutualfund/catalog/mocks"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	mockOcr "github.com/epifi/gamma/api/vendorgateway/wealth/inhouseocr/mocks"
	mockCkyc "github.com/epifi/gamma/api/wealthonboarding/ckyc/mocks"
	mfDaoMocks "github.com/epifi/gamma/investment/mutualfund/dao/mocks"
	"github.com/epifi/gamma/wealthonboarding/config/worker"
	"github.com/epifi/gamma/wealthonboarding/test"
	woDaoMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/dao"
	woHelperMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/helpers"
	woOcrMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/ocr"
)

var (
	wts  epifitemporalTest.WorkflowTestSuite
	conf *worker.Config
)

func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, _, conf, _, teardown = test.InitTestServer()
	wts.SetLogger(epifiTemporalLogging.NewZapAdapter(logger.Log))

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type mockDependencies struct {
	onboardingDetailsDao  *woDaoMocks.MockOnboardingDetailsDao
	s3Client              *mockS3.MockS3Client
	ocrClient             *mockOcr.MockOcrClient
	conf                  *worker.Config
	ckycClient            *mockCkyc.MockCkycClient
	docHelper             *woHelperMocks.MockDocumentHelper
	woOcr                 *woOcrMocks.MockOcr
	lambdaClient          *lambdaMocks.MockLambdaClient
	userClient            *userMocks.MockUsersClient
	actorClient           *actorMocks.MockActorClient
	catalogManagerClient  *catalogMocks.MockCatalogManagerClient
	mutualFundDao         *mfDaoMocks.MockMutualFundDao
	folioLedgerDao        *mfDaoMocks.MockFolioLedgerDao
	orderDao              *mfDaoMocks.MockOrderDao
	orderConfirmationDao  *mfDaoMocks.MockOrderConfirmationInfoDao
	idempotentTxnExecutor *storagev2Mocks.MockIdempotentTxnExecutor
	timeClient            *dateTimeMock.MockTime
}

func getProcessorWithMocks(t *testing.T) (*Processor, *mockDependencies, func()) {
	ctr := gomock.NewController(t)

	onboardingDetailsDao := woDaoMocks.NewMockOnboardingDetailsDao(ctr)
	s3Client := mockS3.NewMockS3Client(ctr)
	ocrClient := mockOcr.NewMockOcrClient(ctr)
	ckycClient := mockCkyc.NewMockCkycClient(ctr)
	docHelper := woHelperMocks.NewMockDocumentHelper(ctr)
	woOcr := woOcrMocks.NewMockOcr(ctr)
	lambdaClient := lambdaMocks.NewMockLambdaClient(ctr)
	userClient := userMocks.NewMockUsersClient(ctr)
	actorClient := actorMocks.NewMockActorClient(ctr)
	catalogManagerClient := catalogMocks.NewMockCatalogManagerClient(ctr)
	mutualFundDao := mfDaoMocks.NewMockMutualFundDao(ctr)
	folioLedgerDao := mfDaoMocks.NewMockFolioLedgerDao(ctr)
	orderDao := mfDaoMocks.NewMockOrderDao(ctr)
	orderConfirmationDao := mfDaoMocks.NewMockOrderConfirmationInfoDao(ctr)
	idempotentTxnExecutor := storagev2Mocks.NewMockIdempotentTxnExecutor(ctr)
	timeClient := dateTimeMock.NewMockTime(ctr)
	md := &mockDependencies{
		onboardingDetailsDao:  onboardingDetailsDao,
		s3Client:              s3Client,
		ocrClient:             ocrClient,
		conf:                  conf,
		ckycClient:            ckycClient,
		docHelper:             docHelper,
		woOcr:                 woOcr,
		lambdaClient:          lambdaClient,
		userClient:            userClient,
		actorClient:           actorClient,
		catalogManagerClient:  catalogManagerClient,
		mutualFundDao:         mutualFundDao,
		folioLedgerDao:        folioLedgerDao,
		orderDao:              orderDao,
		orderConfirmationDao:  orderConfirmationDao,
		idempotentTxnExecutor: idempotentTxnExecutor,
		timeClient:            timeClient,
	}

	p := NewProcessor(onboardingDetailsDao, s3Client, ocrClient, conf, ckycClient, docHelper, woOcr, lambdaClient, userClient, actorClient,
		nil, catalogManagerClient, mutualFundDao, folioLedgerDao, orderDao, orderConfirmationDao, idempotentTxnExecutor, timeClient)

	return p, md, func() { ctr.Finish() }
}
