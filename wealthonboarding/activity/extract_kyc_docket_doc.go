package activity

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"archive/zip"
	"bytes"
	"context"
	b64 "encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"io/fs"
	"io/ioutil"
	"os"
	"path/filepath"
	"sort"
	"strings"

	"github.com/chai2010/tiff"
	"github.com/imdario/mergo"
	"github.com/jinzhu/copier"
	pdfCpuApi "github.com/pdfcpu/pdfcpu/pkg/api"
	"github.com/pkg/errors"
	temporalActivity "go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/aws/v2/lambda"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/integer"
	"github.com/epifi/be-common/pkg/logger"

	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/vendorgateway/wealth/inhouseocr"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/api/wealthonboarding/activity"
)

const (
	KraDocketPath       = "kra_docket"
	docketZipFileName   = "docket.zip"
	panPdfFileName      = "detected_pan.pdf"
	panTiffFileName     = "detected_pan.tiff"
	lambadaFunctionName = "ImageConverterFunction"
)

func (p *Processor) ExtractDocument(ctx context.Context, req *activity.ExtractDocumentRequest) (*activity.ExtractDocumentResponse, error) {
	lg := temporalActivity.GetLogger(ctx)
	actorId := req.GetActorId()

	lg.Debug("extracting document for user", zap.String(logger.ACTOR_ID_V2, actorId))

	// Step 1: check if docket is present
	if req.GetDocumentType() != types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN {
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("unsupported document type %v:", req.GetDocumentType()))
	}

	onbDetails, err := p.onBoardingDetailsDao.GetByActorIdAndOnbType(ctx, actorId, woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT)
	if err != nil {
		lg.Error("error getting on-boarding details for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, errors.Wrap(epifierrors.ErrPermanent, "on-boarding details not found for the actor")
		}
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("database error %s:", err.Error()))
	}

	if onbDetails.GetCurrentStep() != woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC || onbDetails.GetStatus() != woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED {
		// if step is not DOWNLOAD_KRA_DOC or status is not COMPLETE means kra docket is not available in those cases we will send permanent error
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("docket is not available for the user as user's current step is: %v, with status: %v", onbDetails.GetCurrentStep(), onbDetails.GetStatus()))
	}
	pan := onbDetails.GetMetadata().GetPanDetails().GetId()
	// step 2: download docket
	lg.Debug("reading file from s3")

	deferFunc, docketZipFilePath, tmpDirectory, err := p.downloadAndSaveDocket(ctx, pan)
	if err != nil {
		lg.Error("failed to download and save docket", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, err
	}
	defer deferFunc()

	lg.Debug("temp directory path", zap.String(logger.PATH, tmpDirectory))

	// step 3: unzip the docket
	// adding + 1 in case of missing config
	unzippedDocketPath, err := p.unzipDocket(docketZipFilePath, tmpDirectory, p.conf.MaxLayersDeepUnzippingAllowed+1)
	if err != nil {
		lg.Error("failed to unzip docket", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, err
	}

	// get list of file names present in docket
	fileInfos, err := ioutil.ReadDir(unzippedDocketPath)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, "unable to read files from disk")
	}
	fileNames := make([]string, len(fileInfos))
	for i, info := range fileInfos {
		fileNames[i] = info.Name()
	}

	// sort list of all the file names
	sort.Strings(fileNames)

	// step 3: check if heartbeat is present
	lg.Debug("checking heartbeat")
	idx := 0
	if temporalActivity.HasHeartbeatDetails(ctx) {
		// we will retry from a failed attempt, and there is reported progress that we should resume from.
		var completedIdx int
		if err = temporalActivity.GetHeartbeatDetails(ctx, &completedIdx); err == nil {
			idx = completedIdx + 1
			lg.Info("Resuming from failed attempt", zap.Int("completedIdx", completedIdx))
		} else {
			lg.Error("failed to get heartbeat details", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, "failed to get heartbeat details")
		}
	}
	// step 4: loop through remaining fileNames and send to ocular service
	for ; idx < len(fileNames); idx++ {
		ocrRes, fileBuffer, err := p.checkDocWithOcrService(ctx, pan, fileNames[idx], unzippedDocketPath)
		if err != nil {
			lg.Error("failed to check file with ocr", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return nil, err
		}
		lg.Info("ocr service response",
			zap.String(logger.ACTOR_ID_V2, actorId),
			zap.String(logger.FILE_NAME, fileNames[idx]),
			zap.Float64(logger.SCORE, ocrRes.GetConfidenceScore()),
			zap.Bool(logger.FLAG, ocrRes.GetReview()),
			zap.Int32s(logger.PAGE, ocrRes.GetDetectedPages()),
			zap.Int(logger.COUNT, len(ocrRes.GetDetectedPages())),
			zap.Bool(logger.MATCH, strings.EqualFold(strings.ToUpper(pan), strings.ToUpper(ocrRes.GetMaskedDocumentProof().GetId()))),
		)
		// PAN is detected by the OCR service and the correct PAN number is detected
		// Note: PAN number match done below is redundant.
		// The PAN received from VG response is populated using the PAN provided in VG request.
		// TODO(Brijesh): Understand why this approach was taken and fix if needed, else remove the check.
		if !ocrRes.Review && strings.EqualFold(strings.ToUpper(pan), strings.ToUpper(ocrRes.GetMaskedDocumentProof().GetId())) {
			// step 5: extract the pan if pan is present in the doc
			// extract the exact page from doc or receive the page from ocular service
			convertedFileFormat := getImageType(fileNames[idx])
			if len(ocrRes.GetDetectedPages()) > 0 {
				fileBuffer, err, convertedFileFormat = p.extractPageFromDocument(ctx, fileBuffer, tmpDirectory, ocrRes.GetDetectedPages()[0], fileNames[idx])
				if err != nil {
					lg.Error("failed to extract image", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
					return nil, errors.Wrap(epifierrors.ErrPermanent, err.Error())
				}
			}
			// step 6: save the pan in s3
			s3Path, err := p.saveFileToS3(ctx, fileBuffer, fileNames[idx], req.GetDocumentType(), actorId, convertedFileFormat)
			if err != nil {
				lg.Error("failed to save file to s3", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
				return nil, err
			}
			// step 7: update the pan location in db
			ocrResCopy := &inhouseocr.GetExpiryDocResponse{}
			copyErr := copier.Copy(ocrResCopy, ocrRes)
			if copyErr != nil {
				return nil, fmt.Errorf("error copying ocr res: %w", copyErr)
			}
			ocrResCopy.MaskedDocumentProof.S3Paths = append(ocrResCopy.MaskedDocumentProof.S3Paths, s3Path)
			ocrResCopy.MaskedDocumentProof.Photo = nil
			extractedPAN := &woPb.OcrDocumentProof{
				Doc:              ocrResCopy.GetMaskedDocumentProof(),
				ConfidenceScore:  ocrResCopy.GetConfidenceScore(),
				VendorReviewFlag: ocrResCopy.GetReview(),
				ThresholdScore:   p.conf.OCRThresholdScoreConfig[req.GetDocumentType().String()],
			}
			err = p.storePANExtractedFromDocketWithOCRResults(ctx, onbDetails, extractedPAN)
			if err != nil {
				lg.Error("failed to update file address in db", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
				return nil, errors.Wrap(epifierrors.ErrTransient, "error storing PAN extracted from docket with OCR results")
			}
			return &activity.ExtractDocumentResponse{}, nil
		}
		temporalActivity.RecordHeartbeat(ctx, idx)
	}

	return nil, errors.Wrap(epifierrors.ErrPermanent, "unable to get document from kra docket")
}

func getImageType(fileName string) commontypes.ImageType {
	ext := strings.Split(fileName, ".")[1]
	imType, ok := commontypes.ImageType_value[strings.ToUpper(ext)]
	if !ok {
		if strings.ToUpper(ext) == "TIF" {
			return commontypes.ImageType_TIFF
		}
		return commontypes.ImageType_IMAGE_TYPE_UNSPECIFIED
	}
	return commontypes.ImageType(imType)
}

// downloadAndSaveDocket downloads the docket and save the file in temporary folder
func (p *Processor) downloadAndSaveDocket(ctx context.Context, pan string) (func(), string, string, error) {
	s3DocketPath := filepath.Join(KraDocketPath, pan, docketZipFileName)
	buff, err := p.s3Client.Read(ctx, s3DocketPath)
	if err != nil {
		return nil, "", "", errors.Wrap(epifierrors.ErrTransient, err.Error())
	}
	// create a temporary directory for the file
	var tmpDirectory string
	if tmpDirectory, err = ioutil.TempDir("", pan); err != nil {
		return nil, "", "", errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in creating temporary directory, err:%s", err.Error()))
	}

	docketPath := filepath.Join(tmpDirectory, docketZipFileName)

	// (U)ser / owner can read, can write and can execute.
	// (G)roup can't read, can't write and can't execute.
	// (O)thers can't read, can't write and can't execute.
	err = ioutil.WriteFile(docketPath, buff, fs.ModePerm)
	if err != nil {
		return nil, "", "", errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unable to read docket from s3, err:%s", err.Error()))
	}
	return func() {
		err = os.RemoveAll(tmpDirectory)
		if err != nil {
			logger.ErrorNoCtx(fmt.Sprintf("error removing tmp directory - %s", tmpDirectory), zap.Error(err))
		}
	}, docketPath, tmpDirectory, nil
}

// unzipDocket unzips the docket zip file and save the files in temporary directory
func (s *Processor) unzipDocket(docketZipFilePath string, tmpDirectory string, maxLayerToUnzip int) (string, error) {
	if maxLayerToUnzip == 0 {
		return "", nil
	}
	src := docketZipFilePath
	dest := filepath.Join(tmpDirectory, "unzip")
	err := os.MkdirAll(dest, fs.ModePerm)
	if err != nil {
		return "", errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	r, err := zip.OpenReader(src)
	if err != nil {
		return "", errors.Wrap(epifierrors.ErrTransient, err.Error())
	}
	defer func() {
		err = r.Close()
		if err != nil {
			logger.ErrorNoCtx(fmt.Sprintf("error clossing reader - %s", tmpDirectory), zap.Error(err))
		}
	}()

	for _, f := range r.File {
		p, _ := filepath.Abs(f.Name)
		if strings.Contains(p, "..") {
			continue
		}
		rc, err := f.Open()
		if err != nil {
			return "", errors.Wrap(epifierrors.ErrTransient, err.Error())
		}
		defer func() {
			err = rc.Close()
			if err != nil {
				logger.ErrorNoCtx(fmt.Sprintf("error clossing file - %s", f.Name), zap.Error(err))
			}
		}()

		path := filepath.Join(dest, filepath.Clean(f.FileInfo().Name()))
		logger.DebugNoCtx(fmt.Sprintf("%v , %v, %v", f.FileInfo().IsDir(), f.FileInfo().Name(), f.FileInfo().Sys()))
		if f.FileInfo().IsDir() {
			continue
		} else {
			f, err := os.OpenFile(
				path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode())
			if err != nil {
				return "", errors.Wrap(epifierrors.ErrTransient, err.Error())
			}
			defer func() {
				err = f.Close()
				if err != nil {
					logger.ErrorNoCtx(fmt.Sprintf("error clossing file - %s", f.Name()), zap.Error(err))
				}
			}()

			for {
				_, err := io.CopyN(f, rc, 1024)
				if err != nil {
					if err == io.EOF {
						break
					}
					return "", errors.Wrap(epifierrors.ErrTransient, err.Error())
				}
			}
			if strings.HasSuffix(path, ".zip") {
				_, err := s.unzipDocket(path, tmpDirectory, maxLayerToUnzip-1)
				if err != nil {
					return "", errors.Wrap(epifierrors.ErrTransient, err.Error())
				}
				err = os.RemoveAll(path)
				if err != nil {
					logger.ErrorNoCtx(fmt.Sprintf("error removing tmp directory - %s", tmpDirectory), zap.Error(err))
				}
				continue
			}
		}
	}
	return dest, nil
}

// checks sends the document to ocr service to check if pan is present in the file or not
func (p *Processor) checkDocWithOcrService(ctx context.Context, pan string, fileName string, unzippedDocketPath string) (*inhouseocr.GetExpiryDocResponse, []byte, error) {
	temporalActivity.GetLogger(ctx).Debug("requesting ocr service", zap.String(logger.FILE_NAME, fileName))
	buffer, err := ioutil.ReadFile(filepath.Join(unzippedDocketPath, filepath.Clean(fileName)))
	if err != nil {
		return nil, nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to read file from local, err:%s", err.Error()))
	}
	stream, err := p.ocrClient.GetExpiryDocWithStream(ctx)
	if err != nil {
		return nil, nil, errors.Wrap(err, "error while invoking rpc getExpiryDocStream")
	}

	getExpiryDocRequestObj := &inhouseocr.GetExpiryDocRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_INHOUSE_OCR},
		DocumentProof: &types.DocumentProof{
			ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
			Id:        pan,
			Photo: []*commontypes.Image{
				{
					ImageType:       getImageType(fileName),
					ImageDataBase64: b64.StdEncoding.EncodeToString(buffer),
				},
			},
		},
	}

	getExpiryDocRequestObjBytes, err := protojson.Marshal(getExpiryDocRequestObj)
	if err != nil {
		return nil, nil, errors.Wrap(err, "error while marshalling getExpiryDocRequestObj")
	}

	const packetSize = 50 * 1024
	for i := 0; i < len(getExpiryDocRequestObjBytes); i += packetSize {
		sendErr := stream.Send(&inhouseocr.GetExpiryDocWithStreamRequest{
			FileChunk: getExpiryDocRequestObjBytes[i:integer.Min(i+packetSize, len(getExpiryDocRequestObjBytes))],
		})
		if sendErr != nil {
			return nil, nil, errors.Wrap(sendErr, "error while sending bytes in GetExpiryDocWithStream")
		}
	}

	err = stream.CloseSend()
	if err != nil {
		return nil, nil, errors.Wrap(err, "error while closing send request in GetExpiryDocWithStream")
	}

	var expiryDocWithStreamResponseObjByte []byte

	for {
		// Get response and possible error message from the stream
		expiryDocWithStreamRequestObj, recvErr := stream.Recv()

		// Break for loop if there are no more response messages
		if errors.Is(recvErr, io.EOF) {
			logger.Debug(ctx, fmt.Sprint("len of bytes received ", len(expiryDocWithStreamResponseObjByte)))
			break
		}

		// Handle a possible error
		if te := epifigrpc.RPCError(expiryDocWithStreamRequestObj, recvErr); te != nil {
			return nil, nil, errors.Wrap(te, "Error when receiving response in GetExpiryDocWithStream")
		}

		expiryDocWithStreamResponseObjByte = append(expiryDocWithStreamResponseObjByte, expiryDocWithStreamRequestObj.GetFileChunk()...)
	}

	getExpiryDocResponseObj := &inhouseocr.GetExpiryDocResponse{}
	err = protojson.Unmarshal(expiryDocWithStreamResponseObjByte, getExpiryDocResponseObj)
	if err != nil {
		return nil, nil, errors.Wrap(err, "error while redacting doc")
	}
	return getExpiryDocResponseObj, buffer, nil
}

// saveFileToS3 saves buffer file in s3 bucket and returns the path where a file is stored in s3
func (p *Processor) saveFileToS3(ctx context.Context, fileBuffer []byte, fileName string, docType types.DocumentProofType, actorId string, format commontypes.ImageType) (string, error) {
	s3FileName := fmt.Sprintf("%v.%v", 0, format)
	dst := filepath.Join("converted_doc_proof_image/", actorId, docType.String(), s3FileName)
	err := p.s3Client.Write(ctx, dst, fileBuffer, "bucket-owner-full-control")
	if err != nil {
		return "", errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error saving pan image from ocr, err:%s", err.Error()))
	}
	return dst, nil
}

// nolint: dupl
func (p *Processor) storePANExtractedFromDocketWithOCRResults(ctx context.Context, preInvOnbDetails *woPb.OnboardingDetails, ocrPAN *woPb.OcrDocumentProof) error {
	err := mergo.Merge(preInvOnbDetails, &woPb.OnboardingDetails{Metadata: &woPb.OnboardingMetadata{
		PersonalDetails: &woPb.PersonalDetails{KraDocketPan: ocrPAN},
	}}, mergo.WithOverride)
	if err != nil {
		return fmt.Errorf("error merging ocr pan with pre-investment onboarding details: %w", err)
	}
	err = p.onBoardingDetailsDao.Update(ctx, preInvOnbDetails, []woPb.OnboardingDetailsFieldMask{
		woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_METADATA,
	})
	if err != nil {
		return fmt.Errorf("error updating pre-investment onboarding details: %w", err)
	}
	return nil
}

func (p *Processor) extractPageFromDocument(ctx context.Context, fileBuffer []byte, tmpDirectory string, detectedPage int32, filename string) ([]byte, error, commontypes.ImageType) {
	switch getImageType(filename) {
	case commontypes.ImageType_PDF:
		return extractPageFromPdfDocument(fileBuffer, tmpDirectory, detectedPage)
	case commontypes.ImageType_TIFF:
		return extractPageFromTiffDocumentV2(ctx, fileBuffer, tmpDirectory, detectedPage, filename, p.lambdaClient)
	default:
		// Assuming only pdf and tiff files have multiple pages rest all the file formats jpeg, png... etc. will be single page documents
		return fileBuffer, nil, getImageType(filename)
	}
}

func extractPageFromTiffDocument(fileBuffer []byte, tmpDirectory string, detectedPage int32, filename string) ([]byte, error) {
	reader := bytes.NewReader(fileBuffer)
	m, errs, err := tiff.DecodeAll(reader)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}
	if errs[detectedPage][0] != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, errs[detectedPage][0].Error())
	}

	var buf bytes.Buffer
	if err = tiff.Encode(&buf, m[detectedPage][0], nil); err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, errs[detectedPage][0].Error())
	}

	return buf.Bytes(), nil
}

// extractPageFromTiffDocumentV2 takes file in bytes, and page number where pan is detected and
// returns new buffer of a file which contains only single page of pan document in png format
func extractPageFromTiffDocumentV2(ctx context.Context, fileBuffer []byte, tmpDirectory string, detectedPage int32, filename string, lambdaClient lambda.LambdaClient) ([]byte, error, commontypes.ImageType) {
	data := b64.StdEncoding.EncodeToString(fileBuffer)
	convRes, convErr := lambdaClient.Execute(ctx, lambadaFunctionName, ([]byte)(fmt.Sprintf(`{"data":"%v", "selectedPage":%v}`, data, detectedPage+1)))
	if convErr != nil {
		return nil, convErr, 0
	}
	res := &struct {
		Data         string `json:"data,omitempty"`
		ErrorMessage string `json:"errorMessage,omitempty"`
	}{}
	err := json.Unmarshal(convRes, res)
	if err != nil {
		return nil, err, 0
	}
	if res.ErrorMessage != "" {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("Error in response payload: %v", res.ErrorMessage)), 0
	}
	if res.Data == "" {
		return nil, errors.Wrap(epifierrors.ErrTransient, "getting empty data response from lambda"), 0
	}
	decodedBytes, err := b64.StdEncoding.DecodeString(getRepairedBase64String(res.Data))
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("Error in decoding payload: %v", err)), 0
	}
	return decodedBytes, nil, commontypes.ImageType_PNG
}

func getRepairedBase64String(b64String string) string {
	return strings.ReplaceAll(b64String, "\n", "")
}

func extractPageFromPdfDocument(fileBuffer []byte, tmpDirectory string, detectedPage int32) ([]byte, error, commontypes.ImageType) {
	// convert to 1 based index
	detectedPage = detectedPage + 1
	reader := bytes.NewReader(fileBuffer)

	err := pdfCpuApi.ExtractPages(reader, tmpDirectory, panPdfFileName, []string{fmt.Sprintf("%d", detectedPage)}, nil)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error()), 0
	}
	fileName := strings.TrimSuffix(filepath.Base(panPdfFileName), ".pdf")

	fileBuffer, err = ioutil.ReadFile(filepath.Join(tmpDirectory, fmt.Sprintf("%s_page_%d.pdf", fileName, detectedPage)))
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error()), 0
	}
	return fileBuffer, nil, commontypes.ImageType_PDF
}
