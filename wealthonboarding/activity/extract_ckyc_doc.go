package activity

import (
	"context"
	"fmt"

	"github.com/imdario/mergo"
	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	pkgLogger "github.com/epifi/be-common/pkg/logger"

	types "github.com/epifi/gamma/api/typesv2"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woActivity "github.com/epifi/gamma/api/wealthonboarding/activity"
	woCkycPb "github.com/epifi/gamma/api/wealthonboarding/ckyc"
	"github.com/epifi/gamma/wealthonboarding/helper"
)

func (p *Processor) ExtractDocumentFromCKYC(ctx context.Context, req *woActivity.VerifyDocumentFromCKYCRequest) (*woActivity.VerifyDocumentFromCKYCResponse, error) {
	logger := activity.GetLogger(ctx)
	actorId := req.GetActorId()

	// Get PAN from onboarding details of user
	wealthOnbDetails, err := p.onBoardingDetailsDao.GetByActorIdAndOnbType(ctx, actorId, woPb.OnboardingType_ONBOARDING_TYPE_WEALTH)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error("no wealth onboarding record found for user", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrPermanent, err.Error())
		}
		logger.Error("error getting wealth onb details", zap.Error(err), zap.String(pkgLogger.ACTOR_ID_V2, actorId))
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	res, err := p.ckycClient.GetCkycData(ctx, &woCkycPb.GetCkycDataRequest{
		ActorId:  actorId,
		DataType: woCkycPb.CkycDataType_CKYC_DATA_TYPE_DOWNLOAD,
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		if res.GetStatus().IsRecordNotFound() {
			logger.Error("no ckyc download data record found for user")
			return nil, errors.Wrap(epifierrors.ErrPermanent, "no ckyc download data record found for user")
		}
		logger.Error("error getting ckyc data record for user", zap.Error(te))
		return nil, errors.Wrap(epifierrors.ErrTransient, te.Error())
	}
	ckycDocs := res.GetCkycData().GetCkycPayload().GetCkycDownloadData().GetPersonalDetails().GetDocumentsList()
	ckycPANDoc := helper.GetDocumentFromDocumentLists(ckycDocs, types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN)
	if len(ckycPANDoc.GetPhoto()) == 0 && len(ckycPANDoc.GetS3Paths()) == 0 {
		logger.Error("no pan doc found in ckyc record")
		return nil, errors.Wrap(epifierrors.ErrPermanent, "no pan doc found in ckyc record")
	}
	ocrRes, err := p.fetchPanWithOcr(ctx, ckycPANDoc, actorId)
	if err != nil {
		logger.Error("error getting pan with ocr results", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	// Since PAN image is fake in non-prod envs, hence PAN name check is skipped in these envs
	if cfg.IsProdEnv(p.conf.Application.Environment) {
		if wealthOnbDetails.GetMetadata().GetPanDetails().GetId() != ocrRes.GetDoc().GetId() {
			logger.Error("pan number mismatch")
			return nil, errors.Wrap(epifierrors.ErrTransient, "pan number mismatch")
		}
	}
	if ocrRes.VendorReviewFlag {
		logger.Error("ocr says manual review needed")
		return nil, errors.Wrap(epifierrors.ErrPermanent, "ocr says manual review needed")
	}
	err = p.storePANFromCKYCWithOCR(ctx, wealthOnbDetails, ocrRes)
	if err != nil {
		logger.Error("error storing ckyc pan with ocr", zap.String(pkgLogger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "error storing ckyc pan with ocr")
	}
	return &woActivity.VerifyDocumentFromCKYCResponse{}, nil
}

// This function is almost a copy (but not exactly) to helper.FetchPanWithOcr but is kept here because of different conf
func (p *Processor) fetchPanWithOcr(ctx context.Context, pan *types.DocumentProof, actorId string) (*woPb.OcrDocumentProof, error) {
	panB64, err := p.docHelper.DownloadDoc(ctx, pan)
	if err != nil {
		return nil, errors.Wrap(err, "error in downloading pan doc for ocr")
	}
	ocrDoc, err := p.woOcr.GetDocumentWithOCRResults(ctx, panB64)
	if err != nil {
		return nil, errors.Wrap(err, "error in getting pan OCR doc")
	}
	if !helper.IsImagePresent(ocrDoc.GetDoc()) {
		// assumption is that in case of a successful review from OCR service, it will always return an image
		return nil, errors.New("image not present in the PAN OCR doc")
	}
	docRes, docErr := p.docHelper.UploadRawDoc(ctx, actorId, ocrDoc.GetDoc())
	if docErr != nil {
		return nil, errors.Wrap(docErr, "error while uploading doc to s3")
	}
	ocrDoc.Doc = docRes
	return ocrDoc, nil
}

// nolint: dupl
func (p *Processor) storePANFromCKYCWithOCR(ctx context.Context, wealthOnbDetails *woPb.OnboardingDetails, ocrPAN *woPb.OcrDocumentProof) error {
	err := mergo.Merge(wealthOnbDetails, &woPb.OnboardingDetails{Metadata: &woPb.OnboardingMetadata{
		PersonalDetails: &woPb.PersonalDetails{CkycPan: ocrPAN},
	}}, mergo.WithOverride)
	if err != nil {
		return fmt.Errorf("error merging ocr pan with onboarding details: %w", err)
	}
	err = p.onBoardingDetailsDao.Update(ctx, wealthOnbDetails, []woPb.OnboardingDetailsFieldMask{
		woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_METADATA,
	})
	if err != nil {
		return fmt.Errorf("error updating wealth onboarding details: %w", err)
	}
	return nil
}
