package activity

import (
	"bytes"
	"context"
	"encoding/base64"
	json2 "encoding/json"
	"io"
	"io/ioutil"
	"path/filepath"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/jinzhu/copier"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	mocks5 "github.com/epifi/be-common/pkg/aws/lambda/mocks"
	epifiTemporalLogging "github.com/epifi/be-common/pkg/epifitemporal/logging"
	"github.com/epifi/be-common/pkg/epifitemporal/namespace/wealthonboarding"
	"github.com/epifi/be-common/pkg/integer"
	"github.com/epifi/be-common/pkg/logger"

	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/vendorgateway/wealth/inhouseocr"
	mocks3 "github.com/epifi/gamma/api/vendorgateway/wealth/inhouseocr/mocks"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/api/wealthonboarding/activity"
)

var sampleOnboardingData = &woPb.OnboardingDetails{
	Id:      "id-1",
	ActorId: "actor-id-1",
	Metadata: &woPb.OnboardingMetadata{
		PanDetails: &types.DocumentProof{
			Id: "pan-id",
		},
		PersonalDetails: &woPb.PersonalDetails{}},
	Status:            woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED,
	CurrentStep:       woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC,
	CreatedAt:         nil,
	UpdatedAt:         nil,
	CompletedAt:       nil,
	OnboardingType:    woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT,
	CurrentWealthFlow: 0,
	AgentProvidedData: nil,
}

func TestProcessor_ExtractDocument(t *testing.T) {
	p, md, assertTest := getProcessorWithMocks(t)
	defer assertTest()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	epifiTemporalLogging.NewZapAdapter(logger.Log)
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	const packetSize = 50 * 1024
	type args struct {
		ctx          context.Context
		req          *activity.ExtractDocumentRequest
		localZipPath string
	}
	tests := []struct {
		name    string
		args    args
		mockFn  func(*testing.T, string)
		want    *activity.ExtractDocumentResponse
		wantErr bool
	}{
		{
			name: "zip inside zip",
			args: args{
				ctx: context.Background(),
				req: &activity.ExtractDocumentRequest{
					RequestHeader: nil,
					DocumentType:  types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
					ActorId:       "actor-id-1",
				},
				localZipPath: "./testdockets/zip_inside_zip.zip",
			},
			mockFn: func(t *testing.T, localZipPath string) {
				md.onboardingDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), "actor-id-1", woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT).
					Return(sampleOnboardingData, nil)
				var bufferForSampleDocket []byte
				bufferForSampleDocket, err := ioutil.ReadFile(localZipPath)
				if err != nil {
					t.Errorf("error reading zip from path: %s", sampleOnboardingData.GetMetadata().GetPanDetails().GetS3Paths()[0])
				}
				md.s3Client.EXPECT().Read(gomock.Any(), filepath.Join(KraDocketPath, sampleOnboardingData.GetMetadata().GetPanDetails().GetId(), docketZipFileName)).
					Return(bufferForSampleDocket, nil)
				sampleOcrResp := &inhouseocr.GetExpiryDocResponse{
					Status: rpc.StatusOk(),
					MaskedDocumentProof: &types.DocumentProof{
						Id: sampleOnboardingData.GetMetadata().GetPanDetails().GetId(),
					},
					Review:        false,
					DetectedPages: []int32{0},
				}
				bytesOcrRes, _ := protojson.Marshal(sampleOcrResp)
				streamPanData := bytesOcrRes
				ExpiryDocStream := mocks3.NewMockOcr_GetExpiryDocWithStreamClient(ctrl)
				ExpiryDocStream.EXPECT().Send(gomock.Any()).Return(nil).AnyTimes()
				md.ocrClient.EXPECT().GetExpiryDocWithStream(gomock.Any()).Return(ExpiryDocStream, nil)
				ExpiryDocStream.EXPECT().CloseSend().Return(nil)
				for i := 0; i < len(streamPanData); i += packetSize {
					ExpiryDocStream.EXPECT().Recv().Return(&inhouseocr.GetExpiryDocWithStreamResponse{
						Status:    rpc.StatusOk(),
						FileChunk: streamPanData[i:integer.Min(i+packetSize, len(streamPanData))],
					}, nil)
				}
				ExpiryDocStream.EXPECT().Recv().Return(nil, io.EOF)
				s3Path := filepath.Join("converted_doc_proof_image/", sampleOnboardingData.ActorId, types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN.String(), "0.PDF")
				md.s3Client.EXPECT().Write(gomock.Any(), s3Path, gomock.Any(), gomock.Any())
				duplicateOnboardingData := &woPb.OnboardingDetails{}
				err = copier.Copy(duplicateOnboardingData, sampleOnboardingData)
				if err != nil {
					t.Errorf("error creating copy of onboarding details, err: %s", err.Error())
				}
				sampleOcrResp.MaskedDocumentProof.S3Paths = append(sampleOcrResp.MaskedDocumentProof.S3Paths, s3Path)
				duplicateOnboardingData.Metadata.PersonalDetails.KraDocketPan = &woPb.OcrDocumentProof{
					Doc:              sampleOcrResp.GetMaskedDocumentProof(),
					ConfidenceScore:  sampleOcrResp.GetConfidenceScore(),
					ThresholdScore:   p.conf.OCRThresholdScoreConfig[types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN.String()],
					VendorReviewFlag: false,
				}
				md.onboardingDetailsDao.EXPECT().Update(gomock.Any(), duplicateOnboardingData, []woPb.OnboardingDetailsFieldMask{
					woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_METADATA,
				}).Return(nil)
			},
			want:    &activity.ExtractDocumentResponse{},
			wantErr: false,
		},
		{
			name: "ideal zip docket",
			args: args{
				ctx: context.Background(),
				req: &activity.ExtractDocumentRequest{
					RequestHeader: nil,
					DocumentType:  types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
					ActorId:       "actor-id-1",
				},
				localZipPath: "./testdockets/ideal_docket.zip",
			},
			mockFn: func(t *testing.T, localZipPath string) {
				md.onboardingDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), "actor-id-1", woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT).
					Return(sampleOnboardingData, nil)
				var bufferForSampleDocket []byte
				bufferForSampleDocket, err := ioutil.ReadFile(localZipPath)
				if err != nil {
					t.Errorf("error reading zip from path: %s", sampleOnboardingData.GetMetadata().GetPanDetails().GetS3Paths()[0])
				}
				md.s3Client.EXPECT().Read(gomock.Any(), filepath.Join(KraDocketPath, sampleOnboardingData.GetMetadata().GetPanDetails().GetId(), docketZipFileName)).
					Return(bufferForSampleDocket, nil)
				sampleOcrResp := &inhouseocr.GetExpiryDocResponse{
					Status: rpc.StatusOk(),
					MaskedDocumentProof: &types.DocumentProof{
						Id: sampleOnboardingData.GetMetadata().GetPanDetails().GetId(),
					},
					Review:        false,
					DetectedPages: []int32{0},
				}
				bytesOcrRes, _ := protojson.Marshal(sampleOcrResp)
				streamPanData := bytesOcrRes
				ExpiryDocStream := mocks3.NewMockOcr_GetExpiryDocWithStreamClient(ctrl)
				ExpiryDocStream.EXPECT().Send(gomock.Any()).Return(nil).AnyTimes()
				md.ocrClient.EXPECT().GetExpiryDocWithStream(gomock.Any()).Return(ExpiryDocStream, nil)
				ExpiryDocStream.EXPECT().CloseSend().Return(nil)
				for i := 0; i < len(streamPanData); i += packetSize {
					ExpiryDocStream.EXPECT().Recv().Return(&inhouseocr.GetExpiryDocWithStreamResponse{
						Status:    rpc.StatusOk(),
						FileChunk: streamPanData[i:integer.Min(i+packetSize, len(streamPanData))],
					}, nil)
				}
				ExpiryDocStream.EXPECT().Recv().Return(nil, io.EOF)
				s3Path := filepath.Join("converted_doc_proof_image/", sampleOnboardingData.ActorId, types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN.String(), "0.PDF")
				md.s3Client.EXPECT().Write(gomock.Any(), s3Path, gomock.Any(), gomock.Any())
				duplicateOnboardingData := &woPb.OnboardingDetails{}
				err = copier.Copy(duplicateOnboardingData, sampleOnboardingData)
				if err != nil {
					t.Errorf("error creating copy of onboarding details, err: %s", err.Error())
				}
				sampleOcrResp.MaskedDocumentProof.S3Paths = append(sampleOcrResp.MaskedDocumentProof.S3Paths, s3Path)
				duplicateOnboardingData.Metadata.PersonalDetails.KraDocketPan = &woPb.OcrDocumentProof{
					Doc:              sampleOcrResp.GetMaskedDocumentProof(),
					ConfidenceScore:  sampleOcrResp.GetConfidenceScore(),
					ThresholdScore:   p.conf.OCRThresholdScoreConfig[types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN.String()],
					VendorReviewFlag: false,
				}
				md.onboardingDetailsDao.EXPECT().Update(gomock.Any(), duplicateOnboardingData, []woPb.OnboardingDetailsFieldMask{
					woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_METADATA,
				}).Return(nil)
			},
			want:    &activity.ExtractDocumentResponse{},
			wantErr: false,
		},
		{
			// zip inside zip inside zip inside zip
			name: "3 layer zip is allowed",
			args: args{
				ctx: context.Background(),
				req: &activity.ExtractDocumentRequest{
					RequestHeader: nil,
					DocumentType:  types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
					ActorId:       "actor-id-1",
				},
				localZipPath: "./testdockets/layer3.zip",
			},
			mockFn: func(t *testing.T, localZipPath string) {
				md.onboardingDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), "actor-id-1", woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT).
					Return(sampleOnboardingData, nil)
				var bufferForSampleDocket []byte
				bufferForSampleDocket, err := ioutil.ReadFile(localZipPath)
				if err != nil {
					t.Errorf("error reading zip from path: %s", sampleOnboardingData.GetMetadata().GetPanDetails().GetS3Paths()[0])
				}
				md.s3Client.EXPECT().Read(gomock.Any(), filepath.Join(KraDocketPath, sampleOnboardingData.GetMetadata().GetPanDetails().GetId(), docketZipFileName)).
					Return(bufferForSampleDocket, nil)
				sampleOcrResp := &inhouseocr.GetExpiryDocResponse{
					Status: rpc.StatusOk(),
					MaskedDocumentProof: &types.DocumentProof{
						Id: sampleOnboardingData.GetMetadata().GetPanDetails().GetId(),
					},
					Review:        false,
					DetectedPages: []int32{0},
				}
				bytesOcrRes, _ := protojson.Marshal(sampleOcrResp)
				streamPanData := bytesOcrRes
				ExpiryDocStream := mocks3.NewMockOcr_GetExpiryDocWithStreamClient(ctrl)
				ExpiryDocStream.EXPECT().Send(gomock.Any()).Return(nil).AnyTimes()
				md.ocrClient.EXPECT().GetExpiryDocWithStream(gomock.Any()).Return(ExpiryDocStream, nil)
				ExpiryDocStream.EXPECT().CloseSend().Return(nil)
				for i := 0; i < len(streamPanData); i += packetSize {
					ExpiryDocStream.EXPECT().Recv().Return(&inhouseocr.GetExpiryDocWithStreamResponse{
						Status:    rpc.StatusOk(),
						FileChunk: streamPanData[i:integer.Min(i+packetSize, len(streamPanData))],
					}, nil)
				}
				ExpiryDocStream.EXPECT().Recv().Return(nil, io.EOF)
				s3Path := filepath.Join("converted_doc_proof_image/", sampleOnboardingData.ActorId, types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN.String(), "0.PDF")
				md.s3Client.EXPECT().Write(gomock.Any(), s3Path, gomock.Any(), gomock.Any())
				duplicateOnboardingData := &woPb.OnboardingDetails{}
				err = copier.Copy(duplicateOnboardingData, sampleOnboardingData)
				if err != nil {
					t.Errorf("error creating copy of onboarding details, err: %s", err.Error())
				}
				sampleOcrResp.MaskedDocumentProof.S3Paths = append(sampleOcrResp.MaskedDocumentProof.S3Paths, s3Path)
				duplicateOnboardingData.Metadata.PersonalDetails.KraDocketPan = &woPb.OcrDocumentProof{
					Doc:              sampleOcrResp.GetMaskedDocumentProof(),
					ConfidenceScore:  sampleOcrResp.GetConfidenceScore(),
					ThresholdScore:   p.conf.OCRThresholdScoreConfig[types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN.String()],
					VendorReviewFlag: false,
				}
				md.onboardingDetailsDao.EXPECT().Update(gomock.Any(), duplicateOnboardingData, []woPb.OnboardingDetailsFieldMask{
					woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_METADATA,
				}).Return(nil)
			},
			want:    &activity.ExtractDocumentResponse{},
			wantErr: false,
		},
		{
			// zip inside zip inside zip inside zip
			name: "4 layer zip is not allowed",
			args: args{
				ctx: context.Background(),
				req: &activity.ExtractDocumentRequest{
					RequestHeader: nil,
					DocumentType:  types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
					ActorId:       "actor-id-1",
				},
				localZipPath: "./testdockets/layer4.zip",
			},
			mockFn: func(t *testing.T, localZipPath string) {
				md.onboardingDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), "actor-id-1", woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT).
					Return(sampleOnboardingData, nil)
				var bufferForSampleDocket []byte
				bufferForSampleDocket, err := ioutil.ReadFile(localZipPath)
				if err != nil {
					t.Errorf("error reading zip from path: %s", sampleOnboardingData.GetMetadata().GetPanDetails().GetS3Paths()[0])
				}
				md.s3Client.EXPECT().Read(gomock.Any(), filepath.Join(KraDocketPath, sampleOnboardingData.GetMetadata().GetPanDetails().GetId(), docketZipFileName)).
					Return(bufferForSampleDocket, nil)
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockFn(t, tt.args.localZipPath)
			var result *activity.ExtractDocumentResponse
			got, err := env.ExecuteActivity(wealthonboarding.ExtractDocument, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("ExtractDocument() error = %v failed to fetch type value from convertible", err)
					return
				}
			}
			if (err != nil) != tt.wantErr {
				t.Errorf("ExtractDocument() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want != nil && !proto.Equal(result, tt.want) {
				t.Errorf("ExtractDocument() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}

func Test_extractPageFromTiffDocumentV2(t *testing.T) {
	type args struct {
		tmpDirectory string
		detectedPage int32
		filename     string
	}
	tests := []struct {
		name         string
		args         args
		wantFilePath string
		wantErr      bool
	}{
		{
			name: "extract page from tiff file",
			args: args{
				tmpDirectory: "./",
				detectedPage: 3,
				filename:     "./testdockets/multi_page.tif",
			},
			wantFilePath: "./testdockets/detected_pan.png",
			wantErr:      false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			expected, err := ioutil.ReadFile(tt.wantFilePath)
			if err != nil {
				t.Errorf("unable to read wanted file error = %v", err)
			}
			encodedExpectedValue := base64.StdEncoding.EncodeToString(expected)

			res := &struct {
				Data         string `json:"data,omitempty"`
				ErrorMessage string `json:"errorMessage,omitempty"`
			}{
				Data:         encodedExpectedValue,
				ErrorMessage: "",
			}
			expectedResBuffer, err := json2.Marshal(res)
			if err != nil {
				t.Errorf("error marshalling response")
			}
			ctrl := gomock.NewController(t)
			mockLambdaClient := mocks5.NewMockLambdaClient(ctrl)
			mockLambdaClient.EXPECT().Execute(gomock.Any(), lambadaFunctionName, gomock.Any()).Return(expectedResBuffer, nil)
			buff, err := ioutil.ReadFile(filepath.Join(tt.args.tmpDirectory, tt.args.filename))
			if err != nil {
				t.Errorf("error reading file, error:%v", err)
			}
			got, err, _ := extractPageFromTiffDocumentV2(nil, buff, tt.args.tmpDirectory, tt.args.detectedPage, tt.args.filename, mockLambdaClient)
			if (err != nil) != tt.wantErr {
				t.Errorf("extractPageFromTiffDocumentV2() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !bytes.Equal(got, expected) {
				t.Errorf("got is not equal to want")
			}
		})
	}
}
