package activity

import (
	"github.com/slack-go/slack"

	"github.com/epifi/be-common/pkg/aws/v2/lambda"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/datetime"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/investment/mutualfund/catalog"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/wealth/inhouseocr"
	woCkycPb "github.com/epifi/gamma/api/wealthonboarding/ckyc"
	mfDao "github.com/epifi/gamma/investment/mutualfund/dao"
	"github.com/epifi/gamma/wealthonboarding/config/worker"
	"github.com/epifi/gamma/wealthonboarding/dao"
	woHelper "github.com/epifi/gamma/wealthonboarding/helper"
	woOcr "github.com/epifi/gamma/wealthonboarding/ocr"
)

type Processor struct {
	onBoardingDetailsDao  dao.OnboardingDetailsDao
	s3Client              s3.S3Client
	ocrClient             inhouseocr.OcrClient
	conf                  *worker.Config
	ckycClient            woCkycPb.CkycClient
	docHelper             woHelper.DocumentHelper
	woOcr                 woOcr.Ocr
	lambdaClient          lambda.LambdaClient
	userClient            user.UsersClient
	actorClient           actor.ActorClient
	slackClient           *slack.Client
	catalogManagerClient  catalog.CatalogManagerClient
	mutualFundDao         mfDao.MutualFundDao
	folioLedgerDao        mfDao.FolioLedgerDao
	orderDao              mfDao.OrderDao
	orderConfirmationDao  mfDao.OrderConfirmationInfoDao
	idempotentTxnExecutor storagev2.IdempotentTxnExecutor
	timeClient            datetime.Time
}

func NewProcessor(
	onBoardingDetailsDao dao.OnboardingDetailsDao,
	s3Client s3.S3Client,
	ocrClient inhouseocr.OcrClient,
	conf *worker.Config,
	ckycClient woCkycPb.CkycClient,
	docHelper woHelper.DocumentHelper,
	woOcr woOcr.Ocr,
	lambdaClient lambda.LambdaClient,
	userClient user.UsersClient,
	actorClient actor.ActorClient,
	slackClient *slack.Client,
	catalogManagerClient catalog.CatalogManagerClient,
	mutualFundDao mfDao.MutualFundDao,
	folioLedgerDao mfDao.FolioLedgerDao,
	orderDao mfDao.OrderDao,
	orderConfirmationDao mfDao.OrderConfirmationInfoDao,
	idempotentTxnExecutor storagev2.IdempotentTxnExecutor,
	timeClient datetime.Time,
) *Processor {
	return &Processor{
		onBoardingDetailsDao:  onBoardingDetailsDao,
		s3Client:              s3Client,
		ocrClient:             ocrClient,
		conf:                  conf,
		ckycClient:            ckycClient,
		docHelper:             docHelper,
		woOcr:                 woOcr,
		lambdaClient:          lambdaClient,
		userClient:            userClient,
		actorClient:           actorClient,
		slackClient:           slackClient,
		catalogManagerClient:  catalogManagerClient,
		mutualFundDao:         mutualFundDao,
		folioLedgerDao:        folioLedgerDao,
		orderDao:              orderDao,
		orderConfirmationDao:  orderConfirmationDao,
		idempotentTxnExecutor: idempotentTxnExecutor,
		timeClient:            timeClient,
	}
}
