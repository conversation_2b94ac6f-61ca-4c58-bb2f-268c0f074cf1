package kra_data

import (
	"context"

	"google.golang.org/protobuf/types/known/timestamppb"

	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/vendorgateway/wealth/digilocker"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
)

//go:generate mockgen -source=kra.go -destination=../test/mocks/kra_data/kra.go -package=mock_kra_data
type KraData interface {
	// GenerateKraFormPdf takes in data for generating KRA form pdf and returns the AWS endpoint of the pdf
	GenerateKraFormPdf(ctx context.Context, od *woPb.OnboardingDetails) (string, error)
	// DownloadKraData is used to download all the data related to a user present on the vendor end and then stores it in s3
	DownloadKraData(ctx context.Context, od *woPb.OnboardingDetails, createdAt *timestamppb.Timestamp, kraVendor string) (*types.DocumentProof, error)
	// UploadDocketToKra is used to upload the generated pdf data to KRA's SFTP
	UploadDocketToKra(ctx context.Context, panNumber string, latestUploadAttemptMadeAt *timestamppb.Timestamp, isKRAStatusOnHold bool, docketData []byte) error
	// GenerateAgreementPdf takes the signature as base64 string and returns the agreement with signature on it as pdf
	GenerateAgreementPdf(ctx context.Context, actorId string, b64SignatureData string, customerFullName string) (*types.DocumentProof, error)
	// GenerateAadhaarPdf takes the aadhaar details and return the aadhaar card in pdf format
	GenerateAadhaarPdf(ctx context.Context, actorId string, aadhaarRes *digilocker.GetAadhaarInXmlResponse) (*types.DocumentProof, error)
	// UploadAadhaarXmlToKra takes the aadhaar xml data for upload to kra
	UploadAadhaarXmlToKra(ctx context.Context, panNumber string, latestUploadAttemptMadeAt *timestamppb.Timestamp, isKRAStatusOnHold bool, aadhaarXmlData []byte) error
}
