// nolint:gocritic
package kra_data

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/integer"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/names"

	"github.com/epifi/gamma/api/docs"
	types "github.com/epifi/gamma/api/typesv2"
	cvlVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/cvl"
	"github.com/epifi/gamma/api/vendorgateway/wealth/digilocker"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/docket"
	woErr "github.com/epifi/gamma/wealthonboarding/errors"
	"github.com/epifi/gamma/wealthonboarding/helper"
)

const (
	TimeFormat       = "02/01/2006"
	ApplicationType  = "New"
	DeclarationPlace = "India"
	AddressType      = "Residential"
	Country          = "India"
	DocketDays       = 15
	NotSpecified     = "Not specified"
	PassportString   = "Passport Number"
	DLString         = "Driving License"
	DigilockerString = "Digilocker"
	OnlineKycString  = "Online KYC"
)

type CvlDataService struct {
	docsClient   docs.DocsClient
	cvlVgClient  cvlVgPb.CvlClient
	s3Client     s3.S3Client
	httpClient   *http.Client
	conf         *config.Config
	docHelper    helper.DocumentHelper
	commonHelper *helper.CommonHelper
}

func NewCvlDataService(docsClient docs.DocsClient, cvlVgClient cvlVgPb.CvlClient, s3Client s3.S3Client, httpClient *http.Client, conf *config.Config, docHelper helper.DocumentHelper, commonHelper *helper.CommonHelper) *CvlDataService {
	return &CvlDataService{
		docsClient:   docsClient,
		cvlVgClient:  cvlVgClient,
		s3Client:     s3Client,
		httpClient:   httpClient,
		conf:         conf,
		docHelper:    docHelper,
		commonHelper: commonHelper,
	}
}

func (c *CvlDataService) GenerateAadhaarPdf(ctx context.Context, actorId string, aadhaarRes *digilocker.GetAadhaarInXmlResponse) (*types.DocumentProof, error) {
	aadhaarDataPdf := &AadhaarPdf{
		DocumentType:        "e-Aadhaar generated from DigiLocker verified Aadhaar XML",
		GenerationDate:      GetDate(aadhaarRes.GetTs()),
		DownloadDate:        GetDate(aadhaarRes.GetTs()),
		MaskedAadhaarNumber: aadhaarRes.GetMaskedAadhaarNumber(),
		Name:                names.ToString(aadhaarRes.GetName()),
		DateOfBirth:         ConvertDob(aadhaarRes.GetDob()),
		Gender:              ConvertGender(aadhaarRes.GetGender()),
		CareOf:              aadhaarRes.GetCareOf(),
		Landmark:            aadhaarRes.GetLandmark(),
		Locality:            aadhaarRes.GetAddress().GetLocality(),
		City:                aadhaarRes.GetAddress().GetSublocality(),
		PinCode:             aadhaarRes.GetAddress().GetPostalCode(),
		State:               aadhaarRes.GetAddress().GetAdministrativeArea(),
		Photo:               aadhaarRes.GetUserImage().GetImageDataBase64(),
		Address:             GetAddress(aadhaarRes.GetAddress()),
	}
	data, err := json.Marshal(aadhaarDataPdf)
	if err != nil {
		return nil, errors.Wrap(err, "error while converting to aadhaar in pdf format")
	}
	resp, err := c.docsClient.GeneratePdf(ctx, &docs.GeneratePdfRequest{
		PdfTemplate: docs.PDFTemplate_AADHAAR_CARD_DIGILOCKER,
		Data:        data,
		FileName:    actorId + "_aadhaarCard.pdf",
		// expiry is 1 day, configure as per needs
		ExpiryTimeInSeconds: c.conf.CvlKra.PdfExpiryTime,
	})
	if respErr := epifigrpc.RPCError(resp, err); respErr != nil {
		return nil, errors.Wrap(respErr, "error while calling docs service to convert to pdf")
	}

	// aadhaar pdf by docs service has an expiry url of 1 day, using doc helper service we need to fetch and store it permanently
	aadhaarDoc := &types.DocumentProof{
		Id:        aadhaarRes.GetMaskedAadhaarNumber(),
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_UID,
		Photo: []*commontypes.Image{
			{
				ImageType: commontypes.ImageType_PDF,
				ImageUrl:  resp.GetFileUrl(),
			},
		},
	}
	uploadDoc, uploadDocErr := c.docHelper.UploadDoc(ctx, actorId, aadhaarDoc)
	if uploadDocErr != nil {
		return nil, errors.Wrap(uploadDocErr, "error while uploading aadhaar pdf using doc helper")
	}
	return uploadDoc, nil
}

func GetDate(ts *timestampPb.Timestamp) string {
	if !ts.IsValid() {
		return ""
	}
	return time.Unix(ts.GetSeconds(), 0).In(datetime.IST).Format(time.RFC3339)
}

func ConvertDob(dob *types.Date) string {
	return fmt.Sprint(dob.GetDay(), "-", dob.GetMonth(), "-", dob.GetYear())
}

func ConvertGender(g types.Gender) string {
	switch {
	case g == types.Gender_MALE:
		return "Male"
	case g == types.Gender_FEMALE:
		return "Female"
	case g == types.Gender_TRANSGENDER:
		return "Transgender"
	default:
		return "Other"
	}
}

func GetAddress(address *types.PostalAddress) string {
	addressLines := address.GetAddressLines()
	addressLines = append(addressLines, address.GetLocality(), address.GetSublocality(), address.GetAdministrativeArea(), address.GetPostalCode())
	return strings.Join(addressLines, ",")
}

func (c *CvlDataService) GenerateKraFormPdf(ctx context.Context, od *woPb.OnboardingDetails) (string, error) {
	cvlKraForm, err := c.convertToCvlKraForm(ctx, od)
	if err != nil {
		return "", errors.Wrap(err, "error while converting to cvl_kra_form")
	}
	data, err := json.Marshal(cvlKraForm)
	if err != nil {
		return "", errors.Wrap(err, "error while marshaling cvl_kra_form")
	}
	// TODO(vikas): change pdf file name
	resp, err := c.GeneratePdfWithStream(ctx, &docs.GeneratePdfRequest{
		PdfTemplate: docs.PDFTemplate_WEALTH_KRA,
		Data:        data,
		// expiry is 1 day, configure as per needs
		ExpiryTimeInSeconds: c.conf.CvlKra.PdfExpiryTime,
		FileName:            od.GetActorId() + "_kra_data.pdf",
		FileNamePrefix:      docs.FileNamePrefix_KRA_FORM,
	})

	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		return "", errors.Wrap(rpcErr, "failed to generate cvl_kra_form pdf")
	}
	// fetch pdf having signature image
	signaturePdf, fetchErr := c.generateAdditionalImagesAsPdf(ctx, od, cvlKraForm.Signature)
	if fetchErr != nil {
		return "", errors.Wrap(fetchErr, "error while fetching additional pages as pdf")
	}
	// merge any remaining pdf documents into kra docket
	mergeRes, mergeErr := c.mergeAdditionalPdfWithDocket(ctx, resp.GetFileUrl(), signaturePdf, od)
	if mergeErr != nil {
		return "", errors.Wrap(mergeErr, "error while merging pan/aadhaar pdf with kra docket")
	}
	return mergeRes, nil
}

func (c *CvlDataService) GeneratePdfWithStream(ctx context.Context, generatePdfRequestObj *docs.GeneratePdfRequest) (*docs.GeneratePdfResponse, error) {
	stream, err := c.docsClient.GeneratePdfWithStream(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "error while invoking GeneratePdfWithStream rpc")
	}

	generatePdfRequestBytes, err := protojson.Marshal(generatePdfRequestObj)
	if err != nil {
		return nil, errors.Wrap(err, "error while marshalling generatePdfRequestObj")
	}

	logger.Info(ctx, fmt.Sprintf("len of pdf data: %v", len(generatePdfRequestObj.GetData())))
	logger.Info(ctx, fmt.Sprintf("len of bytes sent: %v", len(generatePdfRequestBytes)))

	const packetSize = 300 * 1024
	for i := 0; i < len(generatePdfRequestBytes); i += packetSize {
		sendErr := stream.Send(&docs.GeneratePdfWithStreamRequest{
			FileChunk: generatePdfRequestBytes[i:integer.Min(i+packetSize, len(generatePdfRequestBytes))],
		})
		if sendErr != nil {
			return nil, errors.Wrap(sendErr, "error while sending bytes in GeneratePdfWithStream")
		}
	}

	err = stream.CloseSend()
	if err != nil {
		return nil, errors.Wrap(err, "error while closing send request in GeneratePdfWithStream")
	}

	generatePdfResp, err := stream.CloseAndRecv()
	if err != nil {
		return nil, errors.Wrap(err, "error while receiving GeneratePdfResponse")
	}
	return generatePdfResp, nil
}

// nolint:funlen
func (c *CvlDataService) convertToCvlKraForm(ctx context.Context, od *woPb.OnboardingDetails) (*CvlKraForm, error) {
	photograph, err := c.getPhotograph(ctx, od)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get photograph")
	}
	signature, err := c.getSignature(ctx, od)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get signature")
	}
	additionalPages, err := c.getAdditionalPages(ctx, od)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get additional pages")
	}
	poaDetails := od.GetMetadata().GetPoaDetails()
	if len(poaDetails.GetS3Paths()) == 0 {
		poaDetails = od.GetMetadata().GetPoaWithOcr().GetDoc()
	}
	declarationDate := time.Now().Format(TimeFormat)
	address, addErr := getKraFormAddress(od)
	if addErr != nil {
		return nil, errors.Wrap(woErr.ErrIncompleteKraFormDetails, addErr.Error())
	}
	fatherName := getFatherName(od)

	var verificationDetails *VerificationDetails
	if !helper.IsAadhaarCollectedFromDigilocker(od) {
		verificationDetails = &VerificationDetails{
			IpvDate:            c.getLivenessCompletionDate(od),
			OfficerName:        c.conf.KraDocketSignConfig.OfficerName,
			EmpCode:            c.conf.KraDocketSignConfig.EmployeeCode,
			OfficerDesignation: c.conf.KraDocketSignConfig.OfficerDesignation,
			IntermediaryCode:   c.conf.KraDocketSignConfig.IntermediaryCode,
			OfficerSignature:   c.conf.KraDocketSignConfig.OfficerSignature,
		}
	} else {
		// IPV details are not needed for digilocker mode because IPV is exempted
		verificationDetails = &VerificationDetails{
			IntermediaryCode: c.conf.KraDocketSignConfig.IntermediaryCode,
		}
	}

	cvlKraForm := &CvlKraForm{
		Name:                   strings.Title(strings.ToLower(helper.GetNameForDocket(od).ToString())),
		FatherNameOrSpouseName: strings.Title(strings.ToLower(fatherName.ToString())),
		// will be empty if ckyc data is not present
		MaidenName:    strings.Title(strings.ToLower(od.GetMetadata().GetCkycData().GetDownloadData().GetPersonalDetails().GetMaidenName().ToString())),
		Gender:        strings.Title(strings.ToLower(od.GetMetadata().GetPersonalDetails().GetGender().String())),
		MaritalStatus: convertToKraFormMaritalStatus(od.GetMetadata().GetPersonalDetails().GetMaritalStatus()),
		Dob:           getDobFromDetails(od),
		Nationality:   convertToFormNationality(getNationalityFromDetails(od)),
		Status:        convertToKraFormResStatus(types.ResidentialStatus_RESIDENT_INDIVIDUAL),
		Pan:           od.GetMetadata().GetPanDetails().GetId(),
		// adding residence address / correspondence address as the permanent one, since CVL requires its document proof
		// if it is different from permanent address
		ResidenceAddress: address,
		ContactDetails: &ContactDetails{
			// will be empty if ckyc data is not present
			OfficeTel: od.GetMetadata().GetCkycData().GetDownloadData().GetPersonalDetails().GetTelOffice().GetNumber(),
			// will be empty if ckyc data is not present
			ResTel:       od.GetMetadata().GetCkycData().GetDownloadData().GetPersonalDetails().GetTelResidential().GetNumber(),
			MobileNumber: helper.GetMobileNumberFromDetails(od).ToStringNationalNumber(),
			EmailId:      helper.GetEmailIdFromDetails(od),
		},
		ProofOfAddressSubmitted:  convertToKraFormPoaType(poaDetails.GetProofType()),
		PermanentAddress:         address,
		Signature:                signature,
		Photograph:               photograph,
		AdditionalData:           additionalPages,
		ProofOfAddressIdNumber:   GetIdNumberFromPoa(poaDetails),
		ProofOfAddressExpiryDate: fmt.Sprintf("%v/%v/%v", poaDetails.GetExpiry().GetDay(), poaDetails.GetExpiry().GetMonth(), poaDetails.GetExpiry().GetYear()),
		ApplicationType:          ApplicationType,
		KycMode:                  getKraformKycMode(od),
		DeclarationDate:          declarationDate,
		DeclarationPlace:         DeclarationPlace,
		VerificationDetails:      verificationDetails,
	}

	if vErr := validateMandatoryDetails(cvlKraForm); vErr != nil {
		if errors.Is(vErr, woErr.ErrPoaIdMissing) {
			return nil, vErr
		}
		return nil, errors.Wrap(woErr.ErrIncompleteKraFormDetails, vErr.Error())
	}

	return cvlKraForm, nil
}

// nolint: funlen
func validateMandatoryDetails(kraForm *CvlKraForm) error {
	if kraForm.Name == "" {
		return errors.New("Name missing in Kra form input")
	}
	if kraForm.FatherNameOrSpouseName == "" {
		return errors.New("Father name missing in Kra form input")
	}
	if kraForm.Gender == "GENDER_UNSPECIFIED" || kraForm.Gender == "" {
		return errors.New("Gender missing in Kra form input")
	}
	if kraForm.MaritalStatus == NotSpecified || kraForm.MaritalStatus == "" {
		return errors.New("Marital status missing in Kra form input")
	}
	if kraForm.Dob == "" {
		return errors.New("Dob missing in Kra form input")
	}
	if kraForm.Nationality == "" {
		return errors.New("Nationality missing in Kra form input")
	}
	if kraForm.Status == NotSpecified || kraForm.Status == "" {
		return errors.New("Status missing in Kra form input")
	}
	if kraForm.Pan == "" {
		return errors.New("Pan missing in Kra form input")
	}
	permErr := validateAddress(kraForm.PermanentAddress)
	if permErr != nil {
		return permErr
	}
	resErr := validateAddress(kraForm.ResidenceAddress)
	if resErr != nil {
		return resErr
	}
	if kraForm.ContactDetails == nil {
		return errors.New("contact details missing in Kra form input")
	}
	if kraForm.ContactDetails.MobileNumber == "" {
		return errors.New("mobile number missing in Kra form input")
	}
	if kraForm.ContactDetails.EmailId == "" {
		return errors.New("email missing in Kra form input")
	}
	if kraForm.ProofOfAddressSubmitted == "" {
		return errors.New("POA type missing in Kra form input")
	}
	if kraForm.Signature == "" {
		return errors.New("signature missing in Kra form input")
	}
	if kraForm.Photograph == "" {
		return errors.New("photo missing in Kra form input")
	}
	if kraForm.ProofOfAddressIdNumber == "" {
		return woErr.ErrPoaIdMissing
	}
	if kraForm.ProofOfAddressSubmitted == PassportString || kraForm.ProofOfAddressSubmitted == DLString {
		if kraForm.ProofOfAddressExpiryDate == "" {
			return errors.New("expiry date missing in Kra form input")
		}
	}
	if kraForm.ApplicationType == "" {
		return errors.New("application type missing in Kra form input")
	}
	if kraForm.DeclarationDate == "" {
		return errors.New("declaration date missing in Kra form input")
	}
	if kraForm.DeclarationPlace == "" {
		return errors.New("declaration place missing in Kra form input")
	}
	if kraForm.KycMode == "" {
		return errors.New("kyc mode missing in Kra form input")
	}
	if kraForm.KycMode == OnlineKycString {
		if kraForm.VerificationDetails == nil {
			return errors.New("verification details missing in Kra form input")
		}
		if kraForm.VerificationDetails.IpvDate == "" {
			return errors.New("ipv date missing in Kra form input")
		}
		if kraForm.VerificationDetails.OfficerName == "" {
			return errors.New("OfficerName missing in Kra form input")
		}
		if kraForm.VerificationDetails.IntermediaryCode == "" {
			return errors.New("IntermediaryCode missing in Kra form input")
		}
		if kraForm.VerificationDetails.EmpCode == "" {
			return errors.New("EmpCode missing in Kra form input")
		}
		if kraForm.VerificationDetails.OfficerDesignation == "" {
			return errors.New("OfficerDesignation missing in Kra form input")
		}
		if kraForm.VerificationDetails.OfficerSignature == "" {
			return errors.New("OfficerSignature missing in Kra form input")
		}
	}
	return nil
}

func validateAddress(address *Address) error {
	if address == nil {
		return errors.New("address empty in Kra form input")
	}
	if address.FirstLine == "" {
		return errors.New("address first line empty in Kra form input")
	}
	if address.City == "" {
		return errors.New("address city empty in Kra form input")
	}
	if address.PinCode == "" {
		return errors.New("address pincode empty in Kra form input")
	}
	if address.District == "" {
		return errors.New("address district empty in Kra form input")
	}
	if address.State == "" {
		return errors.New("address state empty in Kra form input")
	}
	if address.Country == "" {
		return errors.New("address country empty in Kra form input")
	}
	if address.Type == "" {
		return errors.New("address type empty in Kra form input")
	}
	return nil
}

func (c *CvlDataService) getAdditionalPages(ctx context.Context, od *woPb.OnboardingDetails) ([]string, error) {
	var poa, pan *types.DocumentProof
	if od.GetAgentProvidedData().GetPoa() != nil {
		poa = od.GetAgentProvidedData().GetPoa()
	} else {
		// get poa document from poaDetails, if not present which means poa is submitted for review, get from poaWithOcr
		poa = od.GetMetadata().GetPoaDetails()
	}
	if len(poa.GetS3Paths()) == 0 {
		poa = od.GetMetadata().GetPoaWithOcr().GetDoc()
	}
	if od.GetMetadata().GetPanDetails() == nil || poa == nil {
		return nil, errors.New("pan details or poa details not found")
	}
	if od.GetAgentProvidedData().GetPan() != nil {
		pan = od.GetAgentProvidedData().GetPan()
	} else {
		pan = od.GetMetadata().GetPanDetails()
	}
	panDetails, err := c.docHelper.DownloadDoc(ctx, pan)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get panDetails")
	}
	poaDetails, err := c.docHelper.DownloadDoc(ctx, poa)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get poaDetails")
	}
	var images []string
	// if PAN is in pdf format, we do not add it in the additional pages array
	if helper.GetDocumentFormatType(panDetails) != commontypes.ImageType_PDF {
		// iterate over all images and push b64 string of it in response
		for _, img := range panDetails.GetPhoto() {
			sRes, sErr := c.commonHelper.SanitizeImage(ctx, img)
			if sErr != nil {
				return nil, errors.Wrap(woErr.ErrImageConversionFailed, sErr.Error())
			}
			imgRes := c.fetchImage(sRes)
			// re assign imgRes to base64 data?
			images = append(images, imgRes)
		}
	}
	// since POA like Aadhaar is in a pdf format already, we do not add it in the additional pages array
	if helper.GetDocumentFormatType(poaDetails) != commontypes.ImageType_PDF {
		for _, img := range poaDetails.GetPhoto() {
			sRes, sErr := c.commonHelper.SanitizeImage(ctx, img)
			if sErr != nil {
				return nil, errors.Wrap(woErr.ErrImageConversionFailed, sErr.Error())
			}
			imgRes := c.fetchImage(sRes)
			images = append(images, imgRes)
		}
	}
	return images, nil
}

// fetchImage returns the image in base64 on best effort basis
func (c *CvlDataService) fetchImage(image *commontypes.Image) string {
	if image.GetImageDataBase64() != "" {
		return getRepairedBase64String(image.GetImageDataBase64())
	}
	// download the image
	request, err := http.NewRequestWithContext(context.Background(), http.MethodGet, image.GetImageUrl(), nil)
	if err != nil {
		logger.ErrorNoCtx("error while forming image request in kra_form", zap.Error(err))
		return ""
	}
	imageRes, err := c.httpClient.Do(request)
	if err != nil {
		logger.ErrorNoCtx("error while downloading image in kra_form", zap.Error(err))
		return ""
	}
	defer func() {
		err = imageRes.Body.Close()
		if err != nil {
			logger.ErrorNoCtx("error occurred while closing image file", zap.Error(err))
		}
	}()

	img, err := ioutil.ReadAll(imageRes.Body)
	if err != nil {
		logger.ErrorNoCtx("error while reading response body", zap.Error(err))
	}
	return getRepairedBase64String(base64.StdEncoding.EncodeToString(img))
}

func convertToKraFormResStatus(status types.ResidentialStatus) string {
	switch status {
	case types.ResidentialStatus_RESIDENT_INDIVIDUAL:
		return "Resident Individual"
	case types.ResidentialStatus_NON_RESIDENT_INDIAN:
		return "Non Resident Indian"
	case types.ResidentialStatus_FOREIGN_NATIONAL:
		return "Foreign National"
	case types.ResidentialStatus_PERSON_OF_INDIAN_ORIGIN:
		return "Person of Indian Origin"
	default:
		return NotSpecified
	}
}

func convertToKraFormMaritalStatus(status types.MaritalStatus) string {
	switch status {
	case types.MaritalStatus_MARRIED:
		return "Married"
	case types.MaritalStatus_UNMARRIED:
		return "Single"
	default:
		return NotSpecified
	}
}

func convertToKraFormPoaType(poaType types.DocumentProofType) string {
	switch poaType {
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR, types.DocumentProofType_DOCUMENT_PROOF_TYPE_OFFLINE_AADHAAR_VERIFICATION,
		types.DocumentProofType_DOCUMENT_PROOF_TYPE_UID:
		return "Aadhaar Card"
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_PASSPORT:
		return PassportString
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_DRIVING_LICENSE:
		return DLString
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_NREGA_JOB_CARD:
		return "NREGA Job Card"
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_NATIONAL_POPULATION_REGISTER_LETTER:
		return "NPR"
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_VOTER_ID:
		return "Voter ID Card"
	default:
		return "Others"
	}
}

// getIdentitySubmitted returns the identity detail which has a verification status as yes
func getIdentitySubmitted(identityDetails []*woPb.CkycIdentityDetail) string {
	for _, iDetails := range identityDetails {
		if iDetails.GetVerificationStatus() == woPb.CkycVerificationStatus_CKYC_VERIFICATION_STATUS_YES {
			return convertToFormIdentityDetail(iDetails.GetIdentityType())
		}
	}
	return ""
}

func convertToFormIdentityDetail(identityType woPb.CkycIdentityType) string {
	switch identityType {
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_PASSPORT:
		return "Passport"
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_VOTER_ID:
		return "Voter ID"
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_PAN:
		return "Pan"
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR:
		return "Aadhaar"
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_DRIVING_LICENSE:
		return "Driving license"
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_NREGA:
		return "Nrega"
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_NATIONAL_POPULATION_REGISTRY_LETTER:
		return "National population registry letter"
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_EKYC_AUTHENTICATION:
		return "Ekyc"
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_OFFLINE_AADHAAR_VERIFICATION:
		return "Offline Aadhaar"
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_OFFLINE_OTHERS:
		return "Offline Others"
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GOVERNMENT:
		return "Simplified Acc Issued Government"
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GAZETTED:
		return "Simplified Acc Issued Gazetted"
	default:
		return ""
	}
}

func convertToFormNationality(nationality types.Nationality) string {
	switch nationality {
	case types.Nationality_NATIONALITY_INDIAN:
		return "Indian"
	case types.Nationality_NATIONALITY_OTHERS:
		return "Others"
	default:
		return ""
	}
}

func GetIdNumberFromPoa(poaDetails *types.DocumentProof) string {
	switch poaDetails.GetProofType() {
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_OFFLINE_AADHAAR_VERIFICATION, types.DocumentProofType_DOCUMENT_PROOF_TYPE_UID, types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR:
		if len(poaDetails.GetId()) < 4 {
			return ""
		} else {
			return poaDetails.GetId()[len(poaDetails.GetId())-4:]
		}
	default:
		return poaDetails.GetId()
	}
}

func (c *CvlDataService) UploadDocketToKra(ctx context.Context, panNumber string, latestUploadAttemptMadeAt *timestampPb.Timestamp, isKRAStatusOnHold bool, docketData []byte) error {
	logger.Info(ctx, "processing UploadDocketToKra")
	// upload the docket pdf to sftp server
	if !latestUploadAttemptMadeAt.IsValid() {
		return errors.New(fmt.Sprint("timestamp is not valid -", latestUploadAttemptMadeAt))
	}
	fileName := fmt.Sprintf("%v.PDF", panNumber)
	var sftpDocketFolderPath string
	uploadAttemptDate := latestUploadAttemptMadeAt.AsTime().Format("02012006")
	if !isKRAStatusOnHold {
		sftpDocketFolderPath = filepath.Join(uploadAttemptDate, "CVLKRA")
	} else {
		sftpDocketFolderPath = filepath.Join(uploadAttemptDate, "HOLD/ALL_DOCS")
	}
	sftpDocketFilePath := filepath.Join(sftpDocketFolderPath, fileName)
	if cfg.IsSimulatedEnv(c.conf.Application.Environment) {
		fileName = strings.Join([]string{uuid.New().String(), fileName}, "_")
		logger.Info(ctx, fmt.Sprintf("file name- %v for pan number- %v", fileName, panNumber))
		sftpDocketFilePath = filepath.Join(c.conf.CvlKra.UploadPath, fileName)
	}
	// UploadFile method uploads a new file if not present, else overwrites the previous file content with the current one
	uploadRes, uploadErr := c.cvlVgClient.UploadFile(ctx, &cvlVgPb.UploadFileRequest{
		RemotePath: sftpDocketFilePath,
		FileData:   docketData,
	})
	if te := epifigrpc.RPCError(uploadRes, uploadErr); te != nil {
		return errors.Wrap(te, fmt.Sprintf("error while uploading file on sftp server, sftp file path = %v", sftpDocketFilePath))
	}
	logger.Info(ctx, "uploaded docket to CVL SFTP", zap.String(logger.DIR_PATH, sftpDocketFolderPath))
	return nil
}

//nolint:funlen
func (c *CvlDataService) DownloadKraData(ctx context.Context, od *woPb.OnboardingDetails, createdAt *timestampPb.Timestamp, kraVendor string) (*types.DocumentProof, error) {
	pan := od.GetMetadata().GetPanDetails().GetId()
	if pan == "" {
		// ideally this should never happen, but the check helps detect if there is data corruption
		return nil, errors.New("error downloading KRA data, PAN not found")
	}
	createdAtTime := createdAt.AsTime()
	dirToFilesMap := getFilesInDirs(ctx, c.cvlVgClient, createdAtTime, kraVendor)
	if len(dirToFilesMap) == 0 {
		logger.Error(ctx, fmt.Sprintf("no dirs found for next %d days starting from: %s", DocketDays, createdAtTime.Format("02012006")))
		return nil, epifierrors.ErrRecordNotFound
	}
	foundDocket := false
	docketPath := ""
	// iterate over all the dirs to find the file with PAN as part of file name
	for dir, files := range dirToFilesMap {
		// iterate over the file names in directory
		for _, fileName := range files {
			if strings.Contains(fileName, pan) {
				// found the docket file
				docketPath = filepath.Join(dir, fileName)
				foundDocket = true
				break
			}
		}
		if foundDocket {
			break
		}
	}
	if !foundDocket {
		logger.Info(ctx, "remote file not yet ready")
		return nil, epifierrors.ErrRecordNotFound
	}

	fd, err := c.downloadFileUsingStreams(ctx, docketPath)
	if err != nil {
		return nil, err
	}
	logger.Info(ctx, "received file from sftp with len", zap.Int("len", len(fd)))
	docRes, docErr := c.uploadRawKraDocketToS3(ctx, pan, fd)
	if docErr != nil {
		return nil, errors.Wrap(docErr, "error while converting to document proof")
	}
	return docRes, nil
}

// after making a successful request to vendor to download the KRA docket of a user, vendor is expected to upload the docket to the SFTP server within 2-3 days
// for all the dockets that the vendor uploads on a given day, the vendor creates a single dir for the date, and puts all user dockets within them
// e.g., all dockets uploaded by CVL-KRA on 25-Aug-2022, will be found inside 25082022/CVLKRA
// getFilesInDirs iterates over next DocketDays dirs starting from when the request was made and lists all the files present in those dirs
func getFilesInDirs(ctx context.Context, cvlVgClient cvlVgPb.CvlClient, requestedAt time.Time, kraVendor string) map[string][]string {
	var (
		dirsHit, dirsNotFound, dirsErrDownloading []string
		dirToFilesMap                             = map[string][]string{}
	)
	wg := sync.WaitGroup{}
	m := sync.Mutex{}
	// iterate over the next 15 days of date wise folder
	for d := 0; d < DocketDays; d++ {
		wg.Add(1)
		// TODO(Brijesh): Migrate to using pkg routine wrapper
		// nocustomlint:goroutine
		go func(daysToAdd int) {
			defer wg.Done()
			// TODO(Brijesh): Avoid checking future dates, since dirs won't be present for them
			nowDate := requestedAt.AddDate(0, 0, daysToAdd)
			remoteFolderPath := fmt.Sprintf("%v/%v", nowDate.Format("02012006"), kraVendor)
			// fetch the directory contents from sftp server
			ldRes, ldErr := cvlVgClient.ListDirFiles(ctx, &cvlVgPb.ListDirFilesRequest{RemotePath: remoteFolderPath})

			// taking a lock before modifying variables initialized outside goroutine
			m.Lock()
			defer m.Unlock()
			dirsHit = append(dirsHit, remoteFolderPath)
			if te := epifigrpc.RPCError(ldRes, ldErr); te != nil {
				if ldRes.GetStatus().IsRecordNotFound() {
					dirsNotFound = append(dirsNotFound, remoteFolderPath)
				} else {
					logger.Error(ctx, "error getting dir files", zap.Error(te), zap.String(logger.DIR_PATH, remoteFolderPath))
					dirsErrDownloading = append(dirsErrDownloading, remoteFolderPath)
				}
				return
			}
			dirToFilesMap[remoteFolderPath] = ldRes.GetFileNames()
		}(d)
	}
	wg.Wait()
	logger.Info(ctx, "dirs hit", zap.Strings(logger.DIR_PATH, dirsHit))
	logger.Info(ctx, "dirs not found", zap.Strings(logger.DIR_PATH, dirsNotFound))
	logger.Info(ctx, "dirs error downloading", zap.Strings(logger.DIR_PATH, dirsErrDownloading))
	return dirToFilesMap
}

func (c *CvlDataService) GenerateAgreementPdf(ctx context.Context, actorId string, b64SignatureData string, customerFullName string) (*types.DocumentProof, error) {
	signedDataPdf := &SignaturePdf{
		Signature: getRepairedBase64String(b64SignatureData),
		Name:      customerFullName,
	}
	data, err := json.Marshal(signedDataPdf)
	if err != nil {
		return nil, errors.Wrap(err, "error while marshalling signature data")
	}
	rs := getRepairedBase64String(string(data))
	// rs := []byte(getRepairedBase64String(string(data)))
	resp, err := c.docsClient.GeneratePdf(ctx, &docs.GeneratePdfRequest{
		PdfTemplate: docs.PDFTemplate_WEALTH_AGREEMENT_SIGNATURE,
		Data:        []byte(rs),
		// Data: data,
		// expiry is 1 day, configure as per needs
		ExpiryTimeInSeconds: c.conf.CvlKra.PdfExpiryTime,
		FileName:            actorId + "_signed_agreement.pdf",
		FileNamePrefix:      docs.FileNamePrefix_KRA_FORM,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		return nil, errors.Wrap(te, "error while calling docs service to convert to pdf")
	}
	// agreementPdf generated by docs service has an expiry url of 1 day, using doc helper service we need to fetch and store it permanently
	agreementDoc := &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_SIGNATURE,
		Photo: []*commontypes.Image{
			{
				ImageType: commontypes.ImageType_PDF,
				ImageUrl:  resp.GetFileUrl(),
			},
		},
	}
	uploadDoc, uploadDocErr := c.docHelper.UploadDoc(ctx, actorId, agreementDoc)
	if uploadDocErr != nil {
		return nil, errors.Wrap(uploadDocErr, "error while uploading agreement pdf using doc helper")
	}
	return uploadDoc, nil
}

func (c *CvlDataService) uploadRawKraDocketToS3(ctx context.Context, pan string, rawData []byte) (*types.DocumentProof, error) {
	docketS3Path, err := docket.GetDocketS3Path(pan)
	if err != nil {
		return nil, errors.Wrap(err, "error getting docket S3 path by PAN")
	}
	err = c.docHelper.UploadRawData(ctx, docketS3Path, rawData)
	if err != nil {
		return nil, errors.Wrap(err, "error while uploading raw kra data")
	}
	return &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_OTHERS,
		S3Paths:   []string{docketS3Path},
	}, nil
}

func (c *CvlDataService) getPhotograph(ctx context.Context, details *woPb.OnboardingDetails) (string, error) {
	var photographDoc *types.DocumentProof
	if details.GetAgentProvidedData().GetUserImage() != nil {
		photographDoc = details.GetAgentProvidedData().GetUserImage()
	} else if details.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData() != nil {
		photographDoc = details.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData().GetUserImageDocument()
	} else {
		photographDoc = details.GetMetadata().GetCkycData().GetSearchData().GetPhoto()
	}
	if photographDoc != nil {
		photographDocWithDocBytes, err := c.docHelper.DownloadDoc(ctx, photographDoc)
		if err != nil {
			return "", errors.Wrap(err, "failed to get photographDocWithDocBytes")
		}
		if photographDocWithDocBytes != nil && len(photographDocWithDocBytes.GetPhoto()) > 0 {
			photograph := photographDocWithDocBytes.GetPhoto()[0]
			photograph, sErr := c.commonHelper.SanitizeImage(ctx, photograph)
			if sErr != nil {
				return "", errors.Wrap(woErr.ErrImageConversionFailed, sErr.Error())
			}
			return getRepairedBase64String(photograph.GetImageDataBase64()), nil
		} else {
			return "", errors.New("empty data in photograph doc")
		}
	} else {
		return "", errors.New("no photograph found")
	}
}

func (c *CvlDataService) getSignature(ctx context.Context, details *woPb.OnboardingDetails) (string, error) {
	var signatureDoc *types.DocumentProof
	if details.GetAgentProvidedData().GetSignature() != nil {
		signatureDoc = details.GetAgentProvidedData().GetSignature()
	} else {
		signatureDoc = details.GetMetadata().GetPersonalDetails().GetSignature()
	}
	if signatureDoc != nil {
		signatureDocWithDocBytes, err := c.docHelper.DownloadDoc(ctx, signatureDoc)
		if err != nil {
			return "", errors.Wrap(err, "failed to get signatureDocWithDocBytes")
		}
		if signatureDocWithDocBytes != nil && len(signatureDocWithDocBytes.GetPhoto()) > 0 {
			return getRepairedBase64String(signatureDocWithDocBytes.GetPhoto()[0].GetImageDataBase64()), nil
		} else {
			return "", errors.New("empty data in signature doc")
		}
	} else {
		return "", errors.New("no signature doc found")
	}
}

func getRepairedBase64String(b64String string) string {
	return strings.ReplaceAll(b64String, "\n", "")
}

func (c *CvlDataService) getLivenessCompletionDate(od *woPb.OnboardingDetails) string {
	for _, lvAttempt := range od.GetMetadata().GetLivenessData().GetLivenessAttempts() {
		if lvAttempt.GetStatus() == woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_PASSED {
			return lvAttempt.GetCompletedAt().AsTime().Format(TimeFormat)
		}
	}
	logger.ErrorNoCtx("error getting liveness completed at time", zap.String("onb_id", od.GetId()), zap.String(logger.ACTOR_ID_V2, od.GetActorId()))
	return ""
}

// nolint: funlen
func (c *CvlDataService) mergeAdditionalPdfWithDocket(ctx context.Context, kraDocketUrl string, signaturePdfUrl string, od *woPb.OnboardingDetails) (string, error) {
	var poaDetails, panDetails *types.DocumentProof
	if od.GetAgentProvidedData().GetPoa() != nil {
		poaDetails = od.GetAgentProvidedData().GetPoa()
	} else {
		poaDetails = od.GetMetadata().GetPoaDetails()
		if len(poaDetails.GetS3Paths()) == 0 {
			poaDetails = od.GetMetadata().GetPoaWithOcr().GetDoc()
		}
	}
	if od.GetAgentProvidedData().GetPan() != nil {
		panDetails = od.GetAgentProvidedData().GetPan()
	} else {
		panDetails = od.GetMetadata().GetPanDetails()
	}
	// fetch KraDocket data from the url
	fRes := c.fetchImage(&commontypes.Image{
		ImageType: commontypes.ImageType_PDF,
		ImageUrl:  kraDocketUrl,
	})
	if fRes == "" {
		return "", errors.New("could not fetch kra docket from url")
	}
	// convert result of download to raw pdf byte array
	dRes, dErr := base64.StdEncoding.DecodeString(fRes)
	if dErr != nil {
		return "", dErr
	}
	docsToMerge := [][]byte{dRes}
	if helper.GetDocumentFormatType(panDetails) == commontypes.ImageType_PDF {
		// download the poa doc from s3
		downRes, downErr := c.docHelper.DownloadDoc(ctx, panDetails)
		if downErr != nil {
			return "", downErr
		}
		// TODO(ismail): assert for len of photos here?
		panRawData, decodeErr := base64.StdEncoding.DecodeString(downRes.GetPhoto()[0].GetImageDataBase64())
		if decodeErr != nil {
			return "", decodeErr
		}
		docsToMerge = append(docsToMerge, panRawData)
	}
	if helper.GetDocumentFormatType(poaDetails) == commontypes.ImageType_PDF {
		// download the poa doc from s3
		downRes, downErr := c.docHelper.DownloadDoc(ctx, poaDetails)
		if downErr != nil {
			return "", downErr
		}
		// TODO(ismail): assert for len of photos here?
		poaRawData, decodeErr := base64.StdEncoding.DecodeString(downRes.GetPhoto()[0].GetImageDataBase64())
		if decodeErr != nil {
			return "", decodeErr
		}
		docsToMerge = append(docsToMerge, poaRawData)
	}
	// fetch signature pdf data from the url
	fRes = c.fetchImage(&commontypes.Image{
		ImageType: commontypes.ImageType_PDF,
		ImageUrl:  signaturePdfUrl,
	})
	if fRes == "" {
		return "", errors.New("could not fetch signature pdf from url")
	}
	// convert result of download to raw pdf byte array
	dRes, dErr = base64.StdEncoding.DecodeString(fRes)
	if dErr != nil {
		return "", dErr
	}
	docsToMerge = append(docsToMerge, dRes)
	mRes, mErr := helper.MergePDFs(docsToMerge...)
	if mErr != nil {
		return "", mErr
	}
	// upload the merged doc to s3
	upRes, upErr := c.docHelper.UploadDoc(ctx, od.GetActorId(), &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_OTHERS,
		Photo: []*commontypes.Image{
			{
				ImageType: commontypes.ImageType_PDF,
				// we should have passed a base64 encoding of merged pdf, but since we have to expose this to manch vendor
				// we are passing the raw byte array representation of the merged pdf
				ImageDataBase64: string(mRes),
			},
		},
	})
	if upErr != nil {
		return "", upErr
	}
	signedUrlRes, signedUrlErr := c.docHelper.GetSignedUrl(ctx, upRes.GetS3Paths()[0], false, c.conf.S3Conf.Bucket, c.conf.CvlKra.PdfExpiryTime)
	if signedUrlErr != nil {
		return "", signedUrlErr
	}
	return signedUrlRes, nil
}

func (c *CvlDataService) generateAdditionalImagesAsPdf(ctx context.Context, od *woPb.OnboardingDetails, base64Signature string) (string, error) {
	data, err := json.Marshal(&CvlKraForm{AdditionalData: []string{base64Signature}})
	if err != nil {
		return "", errors.Wrap(err, "error while marshalling additional pages")
	}
	resp, err := c.docsClient.GeneratePdf(ctx, &docs.GeneratePdfRequest{
		PdfTemplate: docs.PDFTemplate_WEALTH_MULTIPLE_IMAGES_CONVERSION,
		Data:        data,
		// expiry is 1 day, configure as per needs
		ExpiryTimeInSeconds: c.conf.CvlKra.PdfExpiryTime,
		FileName:            od.GetActorId() + "_additional_data.pdf",
		FileNamePrefix:      docs.FileNamePrefix_KRA_FORM,
	})
	if respErr := epifigrpc.RPCError(resp, err); respErr != nil {
		return "", errors.Wrap(respErr, "error while calling docs service to convert to pdf for additional images")
	}
	return resp.GetFileUrl(), nil
}

// downloadFileUsingStreams downloads the docket from sftp server using streaming API
func (c *CvlDataService) downloadFileUsingStreams(ctx context.Context, docketPath string) ([]byte, error) {
	streamRes, err := c.cvlVgClient.DownloadFileWithStream(ctx, &cvlVgPb.DownloadFileWithStreamRequest{RemoteFilePath: docketPath})
	if err != nil {
		logger.Error(ctx, "error while downloading kra docket file from sftp server", zap.String(logger.PATH, docketPath))
		return nil, errors.Wrap(err, "error while downloading file from server")
	}
	var res []byte
	for {
		receivedRes, receivedErr := streamRes.Recv()
		if errors.Is(receivedErr, io.EOF) {
			break
		}
		if receivedErr != nil {
			return nil, errors.Wrap(receivedErr, "error while receiving file ")
		}
		if receivedRes.GetStatus().GetCode() != rpcPb.StatusOk().GetCode() {
			return nil, errors.New(fmt.Sprintf("received rpc status not ok, status_code: %d", receivedRes.GetStatus().GetCode()))
		}
		res = append(res, receivedRes.GetFileChunk()...)
	}
	return res, nil
}

// getNationalityFromDetails returns nationality from the ckyc details of user
// it falls back to INDIAN in case if it is unspecified in the CKYC response
func getNationalityFromDetails(od *woPb.OnboardingDetails) types.Nationality {
	personalDetails := od.GetMetadata().GetCkycData().GetDownloadData().GetPersonalDetails()
	if personalDetails.GetNationality() != types.Nationality_NATIONALITY_UNSPECIFIED {
		return personalDetails.GetNationality()
	}
	return types.Nationality_NATIONALITY_INDIAN
}

// nolint:gocritic
// getDobFromDetails returns dob from epifi tech (personal details) of user
// it falls back to dob from the digilocker or ckyc details in case if it is not in above details
func getDobFromDetails(od *woPb.OnboardingDetails) string {
	digilockerDob := od.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData().GetDob()
	ckycDob := od.GetMetadata().GetCkycData().GetDownloadData().GetPersonalDetails().GetDob()
	odDob := od.GetMetadata().GetPersonalDetails().GetDob()
	if odDob != nil {
		return fmt.Sprintf("%v %v %v", odDob.GetDay(), time.Month(odDob.GetMonth()).String()[:3], odDob.GetYear())
	} else if digilockerDob != nil {
		return fmt.Sprintf("%v %v %v", digilockerDob.GetDay(), time.Month(digilockerDob.GetMonth()).String()[:3], digilockerDob.GetYear())
	} else if ckycDob != nil {
		return fmt.Sprintf("%v %v %v", ckycDob.GetDay(), time.Month(ckycDob.GetMonth()).String()[:3], ckycDob.GetYear())
	}
	return ""
}

func getKraformKycMode(od *woPb.OnboardingDetails) string {
	if helper.IsAadhaarCollectedFromDigilocker(od) {
		return DigilockerString
	}
	return OnlineKycString
}

// getFatherName gets the father name from epifi tech (personal details) and falls back to ckyc data
func getFatherName(od *woPb.OnboardingDetails) *commontypes.Name {
	if od.GetMetadata().GetPersonalDetails().GetFatherName() != nil {
		return od.GetMetadata().GetPersonalDetails().GetFatherName()
	}
	return od.GetMetadata().GetCkycData().GetDownloadData().GetPersonalDetails().GetFathersName()
}

// getKraFormAddress gets the permanent address from details and converts it to Kra form address
func getKraFormAddress(od *woPb.OnboardingDetails) (*Address, error) {
	permAddress := helper.GetPermanentAddressFromDetails(od)
	firstLine, secondLine, thirdLine := "", "", ""
	if permAddress == nil || len(permAddress.GetAddressLines()) == 0 {
		return nil, errors.New("permanent address is empty")
	}
	firstLine = permAddress.GetAddressLines()[0]
	if len(permAddress.GetAddressLines()) > 1 {
		secondLine = permAddress.GetAddressLines()[1]
	}
	if len(permAddress.GetAddressLines()) > 2 {
		thirdLine = strings.TrimSpace(strings.Join(permAddress.GetAddressLines()[2:], ", "))
	}
	return &Address{
		FirstLine:  firstLine,
		SecondLine: secondLine,
		ThirdLine:  thirdLine,
		Street:     strings.TrimSpace(strings.Join(permAddress.GetAddressLines(), ", ")),
		City:       permAddress.GetLocality(),
		PinCode:    permAddress.GetPostalCode(),
		District:   permAddress.GetSublocality(),
		Type:       AddressType,
		State:      permAddress.GetAdministrativeArea(),
		Country:    Country,
	}, nil
}

func (c *CvlDataService) UploadAadhaarXmlToKra(ctx context.Context, panNumber string, latestUploadAttemptMadeAt *timestampPb.Timestamp, isKRAStatusOnHold bool, aadhaarXmlData []byte) error {
	// upload the xml data to sftp server
	if !latestUploadAttemptMadeAt.IsValid() {
		return errors.New(fmt.Sprint("timestamp is not valid -", latestUploadAttemptMadeAt))
	}
	if len(aadhaarXmlData) == 0 {
		return errors.New(fmt.Sprint("aadhaar xml data bytes are empty", panNumber))
	}
	aadhaarXMLFileName := fmt.Sprintf("%v.XML", panNumber)
	var sftpAadhaarXMLFilePath string
	uploadAttemptDate := latestUploadAttemptMadeAt.AsTime().Format("02012006")
	if !isKRAStatusOnHold {
		sftpAadhaarXMLFilePath = filepath.Join(uploadAttemptDate, "CVLKRA", aadhaarXMLFileName)
	} else {
		sftpAadhaarXMLFilePath = filepath.Join(uploadAttemptDate, "HOLD/ALL_DOCS", aadhaarXMLFileName)
	}
	if cfg.IsSimulatedEnv(c.conf.Application.Environment) {
		// to differentiate between docket and aadhaar data for non prod, I've added "aadhaar" in file name
		aadhaarXMLFileName = strings.Join([]string{uuid.New().String(), "AADHAAR", aadhaarXMLFileName}, "_")
		logger.Info(ctx, fmt.Sprintf("file name- %v for pan number- %v", aadhaarXMLFileName, panNumber))
		sftpAadhaarXMLFilePath = filepath.Join(c.conf.CvlKra.UploadPath, aadhaarXMLFileName)
	}
	uploadRes, uploadErr := c.cvlVgClient.UploadFile(ctx, &cvlVgPb.UploadFileRequest{
		RemotePath: sftpAadhaarXMLFilePath,
		FileData:   aadhaarXmlData,
	})
	if te := epifigrpc.RPCError(uploadRes, uploadErr); te != nil {
		return errors.Wrap(te, fmt.Sprintf("error while uploading xml file on sftp server, sftp file path = %v", sftpAadhaarXMLFilePath))
	}
	return nil
}

// form fields for CVL KRA
type CvlKraForm struct {
	Name                     string               `json:"name"`
	FatherNameOrSpouseName   string               `json:"father_name_or_spouse_name"`
	MaidenName               string               `json:"maidenName"`
	Gender                   string               `json:"gender"`
	MaritalStatus            string               `json:"marital_status"`
	Dob                      string               `json:"dob"`
	Nationality              string               `json:"nationality"`
	Status                   string               `json:"status"`
	Pan                      string               `json:"pan"`
	ProofOfIdentitySubmitted string               `json:"proof_of_identity_submitted"`
	ResidenceAddress         *Address             `json:"residence_address"`
	ContactDetails           *ContactDetails      `json:"contact_details"`
	ProofOfAddressSubmitted  string               `json:"proof_of_address_submitted"`
	PermanentAddress         *Address             `json:"permanent_address"`
	Signature                string               `json:"signature"`
	Photograph               string               `json:"photograph"`
	AdditionalData           []string             `json:"additional_data"`
	ProofOfAddressIdNumber   string               `json:"proof_of_address_id_number"`
	ProofOfAddressExpiryDate string               `json:"proof_of_address_expiry_date"`
	ApplicationType          string               `json:"application_type"`
	KycMode                  string               `json:"kyc_mode"`
	DeclarationDate          string               `json:"declaration_date"`
	DeclarationPlace         string               `json:"declaration_place"`
	VerificationDetails      *VerificationDetails `json:"verification_details"`
}
type Address struct {
	FirstLine  string `json:"line1"`
	SecondLine string `json:"line2"`
	ThirdLine  string `json:"line3"`
	Street     string `json:"street"`
	City       string `json:"city"`
	PinCode    string `json:"pin_code"`
	District   string `json:"district"`
	Type       string `json:"type"`
	State      string `json:"state"`
	Country    string `json:"country"`
}

type ContactDetails struct {
	OfficeTel    string `json:"office_tel"`
	ResTel       string `json:"res_tel"`
	MobileNumber string `json:"mobile_number"`
	EmailId      string `json:"email_id"`
}

type VerificationDetails struct {
	IpvDate            string `json:"ipv_date"`
	OfficerName        string `json:"officer_name"`
	EmpCode            string `json:"emp_code"`
	OfficerDesignation string `json:"officer_designation"`
	IntermediaryCode   string `json:"intermediary_code"`
	OfficerSignature   string `json:"officer_signature"`
}
type SignaturePdf struct {
	Signature string `json:"signature"`
	Name      string `json:"name"`
}

type AadhaarPdf struct {
	DocumentType        string `json:"DocumentType"`
	GenerationDate      string `json:"GenerationDate"`
	DownloadDate        string `json:"DownloadDate"`
	MaskedAadhaarNumber string `json:"MaskedAadhaarNumber"`
	Name                string `json:"Name"`
	DateOfBirth         string `json:"DateOfBirth"`
	Gender              string `json:"Gender"`
	CareOf              string `json:"CareOf"`
	Landmark            string `json:"Landmark"`
	Locality            string `json:"Locality"`
	City                string `json:"City"`
	PinCode             string `json:"PinCode"`
	State               string `json:"State"`
	Photo               string `json:"Photo"`
	Address             string `json:"Address"`
}
