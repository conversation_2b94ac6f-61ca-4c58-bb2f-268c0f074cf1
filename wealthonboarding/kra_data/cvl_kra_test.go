package kra_data

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"fmt"
	"io"
	"reflect"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"

	"github.com/epifi/be-common/api/rpc"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/vendorgateway/wealth/cvl"
	cvlVgMocks "github.com/epifi/gamma/api/vendorgateway/wealth/cvl/mocks"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woErr "github.com/epifi/gamma/wealthonboarding/errors"
	helperMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/helpers"

	"github.com/golang/mock/gomock"
	"google.golang.org/genproto/googleapis/type/date"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
)

var (
	personalDetails = &woPb.PersonalDetails{
		Gender:            types.Gender_MALE,
		MaritalStatus:     types.MaritalStatus_MARRIED,
		Nationality:       types.Nationality_NATIONALITY_INDIAN,
		ResidentialStatus: types.ResidentialStatus_RESIDENT_INDIVIDUAL,
		Signature:         signature,
		FatherName:        &commontypes.Name{FirstName: "father name"},
		Email:             "<EMAIL>",
	}
	signature = &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_SIGNATURE,
		S3Paths:   []string{"signature s3 path"},
	}
	signatureData = &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_SIGNATURE,
		Photo: []*commontypes.Image{
			{
				ImageType:       commontypes.ImageType_JPEG,
				ImageDataBase64: "signature data",
			},
		},
	}
	panDetails = &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
		Id:        "panId",
		S3Paths:   []string{"pan s3 path"},
	}
	panData = &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
		Id:        "panId",
		Photo: []*commontypes.Image{
			{
				ImageType:       commontypes.ImageType_JPEG,
				ImageDataBase64: "pan data",
			},
		},
	}
	poaDetails = &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR,
		Id:        "poaId",
		S3Paths:   []string{"poa s3 path"},
	}
	poaDetailsIdMissing = &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR,
		S3Paths:   []string{"poa s3 path"},
	}
	poaData = &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR,
		Id:        "poaId",
		Photo: []*commontypes.Image{
			{
				ImageType:       commontypes.ImageType_JPEG,
				ImageDataBase64: "poa data",
			},
		},
	}
	nsdlData = &woPb.NsdlData{
		PanDetails: &woPb.NsdlData_NsdlPanDetails{
			PanCardName: "pan card name",
		},
	}
	ckycDob = &date.Date{
		Year:  1990,
		Month: 1,
		Day:   1,
	}
	ckycDobStr = "1 Jan 1990"
	ckycMobile = &commontypes.PhoneNumber{
		CountryCode:    91,
		NationalNumber: 12345678,
	}
	ckycMobileStr = "12345678"
	ckycPhoto     = &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PHOTOGRAPH,
		S3Paths: []string{
			"ckyc photo s3 path",
		},
	}
	ckycPhotoData, livenessData = &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PHOTOGRAPH,
		Photo: []*commontypes.Image{
			{
				ImageDataBase64: "ckyc photo data",
			},
		},
	}, &woPb.LivenessData{
		LivenessAttempts: []*woPb.LivenessData_LivenessAttempt{
			{
				Status:      woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_PASSED,
				CompletedAt: timestampPb.New(time.Date(2020, 4, 1, 5, 0, 0, 0, time.UTC)),
			},
		},
	}
	ckycOd = &woPb.OnboardingDetails{
		ActorId: "dummy_actor",
		Metadata: &woPb.OnboardingMetadata{
			PanDetails:      panDetails,
			PoaDetails:      poaDetails,
			PersonalDetails: personalDetails,
			NsdlData:        nsdlData,
			CkycData: &woPb.CkycData{
				SearchData: &woPb.CkycSearchData{
					Photo: ckycPhoto,
				},
				DownloadData: &woPb.CkycDownloadData{
					PersonalDetails: &woPb.CkycPersonalDetails{
						FathersName: &commontypes.Name{FirstName: "ckyc father name"},
						Dob:         ckycDob,
						PermanentAddress: &types.PostalAddress{
							AddressLines:       []string{"ckyc address"},
							AdministrativeArea: "Karnataka",
							Locality:           "locality",
							Sublocality:        "subLocality",
							PostalCode:         "postalCode",
						},
						MobileNumber: ckycMobile,
						EmailId:      "ckyc email which can be invalid @ gmail com",
					},
				},
			},
			LivenessData: livenessData,
		},
	}
	ckycOdPoaIdMissing = &woPb.OnboardingDetails{
		ActorId: "dummy_actor",
		Metadata: &woPb.OnboardingMetadata{
			PanDetails:      panDetails,
			PoaDetails:      poaDetailsIdMissing,
			PersonalDetails: personalDetails,
			NsdlData:        nsdlData,
			CkycData: &woPb.CkycData{
				SearchData: &woPb.CkycSearchData{
					Photo: ckycPhoto,
				},
				DownloadData: &woPb.CkycDownloadData{
					PersonalDetails: &woPb.CkycPersonalDetails{
						FathersName: &commontypes.Name{FirstName: "ckyc father name"},
						Dob:         ckycDob,
						PermanentAddress: &types.PostalAddress{
							AddressLines:       []string{"ckyc address"},
							AdministrativeArea: "Karnataka",
							Locality:           "locality",
							Sublocality:        "subLocality",
							PostalCode:         "postalCode",
						},
						MobileNumber: ckycMobile,
						EmailId:      "ckyc email which can be invalid @ gmail com",
					},
				},
			},
			LivenessData: livenessData,
		},
	}
	ckycOdFatherMissing = &woPb.OnboardingDetails{
		ActorId: "dummy_actor",
		Metadata: &woPb.OnboardingMetadata{
			PanDetails: panDetails,
			PoaDetails: poaDetails,
			PersonalDetails: &woPb.PersonalDetails{
				Gender:            types.Gender_MALE,
				MaritalStatus:     types.MaritalStatus_MARRIED,
				Nationality:       types.Nationality_NATIONALITY_INDIAN,
				ResidentialStatus: types.ResidentialStatus_RESIDENT_INDIVIDUAL,
				Signature:         signature,
				Email:             "<EMAIL>",
			},
			NsdlData: nsdlData,
			CkycData: &woPb.CkycData{
				SearchData: &woPb.CkycSearchData{
					Photo: ckycPhoto,
				},
				DownloadData: &woPb.CkycDownloadData{
					PersonalDetails: &woPb.CkycPersonalDetails{
						Dob: ckycDob,
						PermanentAddress: &types.PostalAddress{
							AddressLines:       []string{"ckyc address"},
							AdministrativeArea: "Karnataka",
							Locality:           "locality",
							Sublocality:        "subLocality",
							PostalCode:         "postalCode",
						},
						MobileNumber: ckycMobile,
						EmailId:      "ckyc email which can be invalid @ gmail com",
					},
				},
			},
			LivenessData: livenessData,
		},
	}
	digilockerDob = &types.Date{
		Year:  1980,
		Month: 1,
		Day:   1,
	}
	digilockerDobStr    = "1 Jan 1980"
	digilockerMobile    = &commontypes.PhoneNumber{NationalNumber: 87654321}
	digilockerMobileStr = "87654321"
	digilockerPhoto     = &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PHOTOGRAPH,
		S3Paths: []string{
			"digilocker photo s3 path",
		},
	}
	digilockerPhotoData = &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PHOTOGRAPH,
		Photo: []*commontypes.Image{
			{
				ImageDataBase64: "digilocker photo data",
			},
		},
	}
	digilockerOd = &woPb.OnboardingDetails{
		ActorId: "dummy_actor",
		Metadata: &woPb.OnboardingMetadata{
			PanDetails:      panDetails,
			PoaDetails:      poaDetails,
			PersonalDetails: personalDetails,
			NsdlData:        nsdlData,
			DigilockerData: &woPb.DigilockerData{
				PersonalData: &woPb.DigilockerPersonalData{
					Mobile: digilockerMobile,
				},
				DigilockerAadhaarData: &woPb.DigilockerAadhaarData{
					Dob: digilockerDob,
					Address: &types.PostalAddress{
						AddressLines:       []string{"digilocker address"},
						AdministrativeArea: "Karnataka",
						Locality:           "locality",
						Sublocality:        "subLocality",
						PostalCode:         "postalCode",
					},
					UserImageDocument: digilockerPhoto,
				},
			},
		},
	}
)

func TestCvlDataService_convertToCvlKraForm(t *testing.T) {
	ctr := gomock.NewController(t)

	mockDocHelper := helperMocks.NewMockDocumentHelper(ctr)
	type args struct {
		ctx   context.Context
		od    *woPb.OnboardingDetails
		mocks []interface{}
	}
	tests := []struct {
		name    string
		args    args
		want    *CvlKraForm
		wantErr bool
		err     error
	}{
		{
			name: "ckyc data exists and no digilocker data",
			args: args{
				ctx: context.Background(),
				od:  ckycOd,
				mocks: []interface{}{
					mockDocHelper.EXPECT().DownloadDoc(gomock.Any(), signature).Return(signatureData, nil),
					mockDocHelper.EXPECT().DownloadDoc(gomock.Any(), panDetails).Return(panData, nil),
					mockDocHelper.EXPECT().DownloadDoc(gomock.Any(), poaDetails).Return(poaData, nil),
					mockDocHelper.EXPECT().DownloadDoc(gomock.Any(), ckycPhoto).Return(ckycPhotoData, nil),
				},
			},
			want: &CvlKraForm{
				Name:                   "Pan Card Name",
				FatherNameOrSpouseName: "Father Name",
				Gender:                 "Male",
				MaritalStatus:          "Married",
				Dob:                    ckycDobStr,
				Nationality:            "Indian",
				Status:                 "Resident Individual",
				Pan:                    "panId",
				ResidenceAddress: &Address{
					FirstLine: "ckyc address",
					Street:    "ckyc address",
					Type:      AddressType,
					Country:   Country,
					City:      "locality",
					PinCode:   "postalCode",
					District:  "subLocality",
					State:     "Karnataka",
				},
				ContactDetails: &ContactDetails{
					MobileNumber: ckycMobileStr,
					EmailId:      "<EMAIL>",
				},
				ProofOfAddressSubmitted: "Aadhaar Card",
				PermanentAddress: &Address{
					FirstLine: "ckyc address",
					Street:    "ckyc address",
					Type:      AddressType,
					Country:   Country,
					City:      "locality",
					PinCode:   "postalCode",
					District:  "subLocality",
					State:     "Karnataka",
				},
				Signature:              "signature data",
				Photograph:             "ckyc photo data",
				AdditionalData:         []string{"pan data", "poa data"},
				ProofOfAddressIdNumber: "oaId",
				ApplicationType:        ApplicationType,
				KycMode:                "Online KYC",
				DeclarationPlace:       DeclarationPlace,
				VerificationDetails: &VerificationDetails{
					IpvDate:            "01/04/2020",
					OfficerName:        conf.KraDocketSignConfig.OfficerName,
					EmpCode:            conf.KraDocketSignConfig.EmployeeCode,
					OfficerDesignation: conf.KraDocketSignConfig.OfficerDesignation,
					IntermediaryCode:   conf.KraDocketSignConfig.IntermediaryCode,
				},
			},
			wantErr: false,
		},
		{
			name: "no ckyc data and digilocker data exists",
			args: args{
				ctx: context.Background(),
				od:  digilockerOd,
				mocks: []interface{}{
					mockDocHelper.EXPECT().DownloadDoc(gomock.Any(), signature).Return(signatureData, nil),
					mockDocHelper.EXPECT().DownloadDoc(gomock.Any(), panDetails).Return(panData, nil),
					mockDocHelper.EXPECT().DownloadDoc(gomock.Any(), poaDetails).Return(poaData, nil),
					mockDocHelper.EXPECT().DownloadDoc(gomock.Any(), digilockerPhoto).Return(digilockerPhotoData, nil),
				},
			},
			want: &CvlKraForm{
				Name:                   "Pan Card Name",
				FatherNameOrSpouseName: "Father Name",
				Gender:                 "Male",
				MaritalStatus:          "Married",
				Dob:                    digilockerDobStr,
				Nationality:            "Indian",
				Status:                 "Resident Individual",
				Pan:                    "panId",
				ResidenceAddress: &Address{
					FirstLine: "digilocker address",
					Street:    "digilocker address",
					Type:      AddressType,
					Country:   Country,
					City:      "locality",
					PinCode:   "postalCode",
					District:  "subLocality",
					State:     "Karnataka",
				},
				ContactDetails: &ContactDetails{
					MobileNumber: digilockerMobileStr,
					EmailId:      "<EMAIL>",
				},
				ProofOfAddressSubmitted: "Aadhaar Card",
				PermanentAddress: &Address{
					FirstLine: "digilocker address",
					Street:    "digilocker address",
					Type:      AddressType,
					Country:   Country,
					City:      "locality",
					PinCode:   "postalCode",
					District:  "subLocality",
					State:     "Karnataka",
				},
				Signature:              "signature data",
				Photograph:             "digilocker photo data",
				AdditionalData:         []string{"pan data", "poa data"},
				ProofOfAddressIdNumber: "oaId",
				ApplicationType:        ApplicationType,
				KycMode:                "Digilocker",
				DeclarationPlace:       DeclarationPlace,
				VerificationDetails: &VerificationDetails{
					IntermediaryCode: conf.KraDocketSignConfig.IntermediaryCode,
				},
			},
			wantErr: false,
		},
		{
			name: "poa id missing",
			args: args{
				ctx: context.Background(),
				od:  ckycOdPoaIdMissing,
				mocks: []interface{}{
					mockDocHelper.EXPECT().DownloadDoc(gomock.Any(), signature).Return(signatureData, nil),
					mockDocHelper.EXPECT().DownloadDoc(gomock.Any(), panDetails).Return(panData, nil),
					mockDocHelper.EXPECT().DownloadDoc(gomock.Any(), poaDetailsIdMissing).Return(poaData, nil),
					mockDocHelper.EXPECT().DownloadDoc(gomock.Any(), ckycPhoto).Return(ckycPhotoData, nil),
				},
			},
			want:    nil,
			wantErr: true,
			err:     woErr.ErrPoaIdMissing,
		},
		{
			name: "father name missing",
			args: args{
				ctx: context.Background(),
				od:  ckycOdFatherMissing,
				mocks: []interface{}{
					mockDocHelper.EXPECT().DownloadDoc(gomock.Any(), signature).Return(signatureData, nil),
					mockDocHelper.EXPECT().DownloadDoc(gomock.Any(), panDetails).Return(panData, nil),
					mockDocHelper.EXPECT().DownloadDoc(gomock.Any(), poaDetails).Return(poaData, nil),
					mockDocHelper.EXPECT().DownloadDoc(gomock.Any(), ckycPhoto).Return(ckycPhotoData, nil),
				},
			},
			want:    nil,
			wantErr: true,
			err:     woErr.ErrIncompleteKraFormDetails,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CvlDataService{
				conf:      conf,
				docHelper: mockDocHelper,
			}
			got, err := c.convertToCvlKraForm(tt.args.ctx, tt.args.od)
			if (err != nil) != tt.wantErr {
				t.Errorf("convertToCvlKraForm() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("convertToCvlKraForm() error = %v, wantErr %v", err, tt.err)
				}
				return
			}
			got = sanitizeKraFormResp(got)
			if diff := cmp.Diff(tt.want, got); diff != "" {
				t.Errorf("convertToCvlKraForm() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func sanitizeKraFormResp(data *CvlKraForm) *CvlKraForm {
	data.ProofOfAddressExpiryDate = ""
	data.DeclarationDate = ""
	if data.VerificationDetails != nil {
		data.VerificationDetails.OfficerSignature = ""
	}
	return data
}

func TestCvlDataService_DownloadKraData(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockCvlVgClient := cvlVgMocks.NewMockCvlClient(ctrl)
	mockCvlVgDownloadFileStreamClient := cvlVgMocks.NewMockCvl_DownloadFileWithStreamClient(ctrl)
	mockDocHelper := helperMocks.NewMockDocumentHelper(ctrl)
	type args struct {
		ctx       context.Context
		od        *woPb.OnboardingDetails
		createdAt *timestampPb.Timestamp
		kraVendor string
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func()
		want       *types.DocumentProof
		wantErr    bool
	}{
		{
			name: "download docket when present",
			args: args{
				ctx:       context.Background(),
				od:        &woPb.OnboardingDetails{Metadata: &woPb.OnboardingMetadata{PanDetails: &types.DocumentProof{Id: "BRIJESH_PAN"}}},
				createdAt: timestampPb.Now(),
				kraVendor: "CVLKRA",
			},
			setupMocks: func() {
				mockCvlVgClient.EXPECT().ListDirFiles(gomock.Any(), gomock.Any()).Return(&cvl.ListDirFilesResponse{Status: rpc.StatusOk(), FileNames: nil}, nil).Times(14)
				mockCvlVgClient.EXPECT().ListDirFiles(gomock.Any(), gomock.Any()).Return(&cvl.ListDirFilesResponse{Status: rpc.StatusOk(), FileNames: []string{"X_BRIJESH_PAN_1"}}, nil).Times(1)
				mockCvlVgDownloadFileStreamClient.EXPECT().Recv().Return(nil, io.EOF)
				mockCvlVgClient.EXPECT().DownloadFileWithStream(gomock.Any(), gomock.Any()).Return(mockCvlVgDownloadFileStreamClient, nil)
				mockDocHelper.EXPECT().UploadRawData(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
			want: &types.DocumentProof{
				ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_OTHERS,
				S3Paths:   []string{"kra_docket/BRIJESH_PAN/docket.zip"},
			},
		},
		{
			name: "throw error when no file with pan name found",
			args: args{
				ctx:       context.Background(),
				od:        &woPb.OnboardingDetails{Metadata: &woPb.OnboardingMetadata{PanDetails: &types.DocumentProof{Id: "BRIJESH_PAN"}}},
				createdAt: timestampPb.Now(),
				kraVendor: "CVLKRA",
			},
			setupMocks: func() {
				mockCvlVgClient.EXPECT().ListDirFiles(gomock.Any(), gomock.Any()).Return(&cvl.ListDirFilesResponse{Status: rpc.StatusOk(), FileNames: nil}, nil).Times(15)
			},
			wantErr: true,
		},
		{
			name: "throw error when sftp down",
			args: args{
				ctx:       context.Background(),
				od:        &woPb.OnboardingDetails{Metadata: &woPb.OnboardingMetadata{PanDetails: &types.DocumentProof{Id: "BRIJESH_PAN"}}},
				createdAt: timestampPb.Now(),
				kraVendor: "CVLKRA",
			},
			setupMocks: func() {
				mockCvlVgClient.EXPECT().ListDirFiles(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("sftp down")).Times(15)
			},
			wantErr: true,
		},
	}
	c := &CvlDataService{
		cvlVgClient: mockCvlVgClient,
		docHelper:   mockDocHelper,
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()
			got, err := c.DownloadKraData(tt.args.ctx, tt.args.od, tt.args.createdAt, tt.args.kraVendor)
			if (err != nil) != tt.wantErr {
				t.Errorf("DownloadKraData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DownloadKraData() got = %v, want %v", got, tt.want)
			}
		})
	}
}
