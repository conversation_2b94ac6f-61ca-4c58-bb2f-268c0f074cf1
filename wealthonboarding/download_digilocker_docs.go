package wealthonboarding

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"

	"github.com/imdario/mergo"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	digilockerVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/digilocker"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

func (s *Service) DownloadDigilockerDocs(ctx context.Context, req *woPb.DownloadDigilockerDocsRequest) (*woPb.DownloadDigilockerDocsResponse, error) {
	if cfg.IsSimulatedEnv(s.conf.Application.Environment) {
		modErr := s.modifyReqForNonProdSimulation(ctx, req)
		if modErr != nil {
			return &woPb.DownloadDigilockerDocsResponse{Status: rpc.StatusInternal()}, nil
		}
	}

	tokenRes, tokenErr := s.digilockerVgClient.GetAccessToken(ctx, &digilockerVgPb.GetAccessTokenRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_DIGILOCKER},
		// TODO(Brijesh): Remove deprecated field after 1 deployment cycle
		AuthCode:    req.GetAuthCode(),
		ActorId:     req.GetActorId(),
		AccessRoute: &digilockerVgPb.GetAccessTokenRequest_AuthorizationCode{AuthorizationCode: req.GetAuthCode()},
	})

	if te := epifigrpc.RPCError(tokenRes, tokenErr); te != nil {
		logger.Error(ctx, "error getting access tokens from auth code")
		return &woPb.DownloadDigilockerDocsResponse{Status: rpc.StatusInternal()}, nil
	}

	wealthOnbDetails, wealthOnbDetailsErr := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, req.GetActorId(), woPb.OnboardingType_ONBOARDING_TYPE_WEALTH)
	if wealthOnbDetailsErr != nil {
		logger.Error(ctx, "error getting wealth onboarding details")
		return &woPb.DownloadDigilockerDocsResponse{Status: rpc.StatusInternal()}, nil
	}

	mergeErr := mergo.Merge(wealthOnbDetails, &woPb.OnboardingDetails{Metadata: &woPb.OnboardingMetadata{
		DigilockerData: &woPb.DigilockerData{
			RefreshToken: tokenRes.GetTokenResponse().GetRefreshToken(),
			PersonalData: &woPb.DigilockerPersonalData{
				Name:     tokenRes.GetTokenResponse().GetName(),
				Dob:      tokenRes.GetTokenResponse().GetDob(),
				Gender:   tokenRes.GetTokenResponse().GetGender(),
				EAadhaar: tokenRes.GetTokenResponse().GetEAadhaar(),
				Mobile:   tokenRes.GetTokenResponse().GetMobile(),
			},
			// rest of the fields are populated within orchestrator step where we actually download the DigiLocker documents
		},

		// In older clients, client didn't pass the HasDigilockerAccount flag when user opts into DigiLocker flow,
		// Hence, we are marking it as true explicitly
		CustomerProvidedData: &woPb.CustomerProvidedData{HasDigilockerAccount: commontypes.BooleanEnum_TRUE},
	}}, mergo.WithOverride)
	if mergeErr != nil {
		logger.Error(ctx, "error merging wealth onboarding details", zap.Error(mergeErr))
		return &woPb.DownloadDigilockerDocsResponse{Status: rpc.StatusInternal()}, nil
	}
	uErr := s.onboardingDetailsDao.Update(ctx, wealthOnbDetails, []woPb.OnboardingDetailsFieldMask{
		woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_METADATA,
	})
	if uErr != nil {
		logger.Error(ctx, "error updating wealth onboarding details", zap.Error(uErr))
		return &woPb.DownloadDigilockerDocsResponse{Status: rpc.StatusInternal()}, nil
	}
	return &woPb.DownloadDigilockerDocsResponse{Status: rpc.StatusOk()}, nil
}

// for non-prod envs, we use PAN as the identifier in place of authorization code to get expected simulation data
func (s *Service) modifyReqForNonProdSimulation(ctx context.Context, req *woPb.DownloadDigilockerDocsRequest) error {
	od, odErr := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, req.GetActorId(), woPb.OnboardingType_ONBOARDING_TYPE_WEALTH)
	if odErr != nil {
		return errors.Wrap(odErr, "error getting wealth onboarding details")
	}
	req.AuthCode = od.GetMetadata().GetPanDetails().GetId()
	return nil
}
