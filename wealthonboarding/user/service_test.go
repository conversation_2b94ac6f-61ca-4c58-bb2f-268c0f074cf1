package user

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"flag"
	"os"
	"sync"
	"testing"

	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/proto"

	cmdtypes "github.com/epifi/be-common/pkg/cmd/types"
	testv2 "github.com/epifi/be-common/pkg/test/v2"

	types "github.com/epifi/gamma/api/typesv2"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	userPb "github.com/epifi/gamma/api/wealthonboarding/user"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/dao/impl"
	"github.com/epifi/gamma/wealthonboarding/test"
)

type TestSuite struct {
	db     cmdtypes.EpifiWealthCRDB
	conf   *config.Config
	dbName string
}

var (
	AffectedTestTables   = []string{"users"}
	testSuite            TestSuite
	initialiseSameDbOnce sync.Once
	userInput1           = &userPb.User{
		ActorId:    "dummy-actor",
		SchemeCode: userPb.SchemeCode_SCHEME_CODE_CONNECTED_ACCOUNT_PLUS_INVESTMENT,
		Metadata:   &userPb.UserMetadata{CustomerIpAddress: "ip-1"},
		PersonalDetails: &userPb.PersonalDetails{
			Email: "email@2",
		},
		KycData: &userPb.KycData{StatusData: &woPb.KraStatusData{
			PanStatusDetails: &woPb.KraStatusData_PanStatusDetails{
				PanNumber: "PanNumber2",
				Name:      "Name2",
			},
		}},
	}
	userOutput1 = &userPb.User{
		ActorId:    "dummy-actor",
		SchemeCode: userPb.SchemeCode_SCHEME_CODE_CONNECTED_ACCOUNT_PLUS_INVESTMENT,
		Metadata:   &userPb.UserMetadata{CustomerIpAddress: "ip-1"},
		PersonalDetails: &userPb.PersonalDetails{
			Name: &commontypes.Name{
				FirstName:  "fn",
				MiddleName: "mn",
				LastName:   "ln",
				Honorific:  "h",
			},
			FatherName: &commontypes.Name{
				FirstName:  "fn1",
				MiddleName: "mn1",
				LastName:   "ln1",
				Honorific:  "h1",
			},
			MotherName: &commontypes.Name{
				FirstName:  "fn2",
				MiddleName: "mn2",
				LastName:   "ln2",
				Honorific:  "h2",
			},
			Dob: &date.Date{
				Year:  1990,
				Month: 7,
				Day:   11,
			},
			Gender:                   types.Gender_MALE,
			Email:                    "email@2",
			Nationality:              types.Nationality_NATIONALITY_INDIAN,
			ResidentialStatus:        types.ResidentialStatus_RESIDENT_INDIVIDUAL,
			PoliticallyExposedStatus: types.PoliticallyExposedStatus_POLITICALLY_EXPOSED_STATUS_NOT_APPLICABLE,
			Occupation:               "NA",
			MaritalStatus:            types.MaritalStatus_UNMARRIED,
			Photo: &types.DocumentProof{
				Photo: []*commontypes.Image{{
					ImageType: commontypes.ImageType_JPEG,
					ImageUrl:  "image-url",
				}},
			},
		},
		KycData: &userPb.KycData{StatusData: &woPb.KraStatusData{
			PanStatusDetails: &woPb.KraStatusData_PanStatusDetails{
				PanNumber: "PanNumber2",
				Name:      "Name2",
			},
		}},
	}
	userInput2 = &userPb.User{
		ActorId:    "dummy-actor6",
		SchemeCode: userPb.SchemeCode_SCHEME_CODE_CONNECTED_ACCOUNT_PLUS_INVESTMENT,
		Metadata:   &userPb.UserMetadata{CustomerIpAddress: "ip-1"},
		PersonalDetails: &userPb.PersonalDetails{
			Email: "email@2",
		},
		KycData: &userPb.KycData{StatusData: &woPb.KraStatusData{
			PanStatusDetails: &woPb.KraStatusData_PanStatusDetails{
				PanNumber: "PanNumber2",
				Name:      "Name2",
			},
		}},
	}
	userOutput2 = &userPb.User{
		ActorId:    "dummy-actor6",
		SchemeCode: userPb.SchemeCode_SCHEME_CODE_CONNECTED_ACCOUNT_PLUS_INVESTMENT,
		Metadata:   &userPb.UserMetadata{CustomerIpAddress: "ip-1"},
		PersonalDetails: &userPb.PersonalDetails{
			Name: &commontypes.Name{
				FirstName:  "fn",
				MiddleName: "mn",
				LastName:   "ln",
				Honorific:  "h",
			},
			FatherName: &commontypes.Name{
				FirstName:  "fn1",
				MiddleName: "mn1",
				LastName:   "ln1",
				Honorific:  "h1",
			},
			MotherName: &commontypes.Name{
				FirstName:  "fn2",
				MiddleName: "mn2",
				LastName:   "ln2",
				Honorific:  "h2",
			},
			Dob: &date.Date{
				Year:  1990,
				Month: 7,
				Day:   11,
			},
			Gender:                   types.Gender_MALE,
			Email:                    "email@2",
			Nationality:              types.Nationality_NATIONALITY_INDIAN,
			ResidentialStatus:        types.ResidentialStatus_RESIDENT_INDIVIDUAL,
			PoliticallyExposedStatus: types.PoliticallyExposedStatus_POLITICALLY_EXPOSED_STATUS_NOT_APPLICABLE,
			Occupation:               "NA",
			MaritalStatus:            types.MaritalStatus_UNMARRIED,
			Photo: &types.DocumentProof{
				Photo: []*commontypes.Image{{
					ImageType: commontypes.ImageType_JPEG,
					ImageUrl:  "image-url",
				}},
			},
		},
		KycData: &userPb.KycData{StatusData: &woPb.KraStatusData{
			PanStatusDetails: &woPb.KraStatusData_PanStatusDetails{
				PanNumber: "PanNumber2",
				Name:      "Name2",
			},
		}},
	}
	userOutput3 = &userPb.User{
		ActorId:    "dummy-actor6",
		SchemeCode: userPb.SchemeCode_SCHEME_CODE_CONNECTED_ACCOUNT_PLUS_INVESTMENT,
		Metadata:   &userPb.UserMetadata{CustomerIpAddress: "ip-1"},
		PersonalDetails: &userPb.PersonalDetails{
			Name: &commontypes.Name{
				FirstName:  "fn",
				MiddleName: "mn",
				LastName:   "ln",
				Honorific:  "h",
			},
			FatherName: &commontypes.Name{
				FirstName:  "fn1",
				MiddleName: "mn1",
				LastName:   "ln1",
				Honorific:  "h1",
			},
			MotherName: &commontypes.Name{
				FirstName:  "fn2",
				MiddleName: "mn2",
				LastName:   "ln2",
				Honorific:  "h2",
			},
			Dob: &date.Date{
				Year:  1990,
				Month: 7,
				Day:   11,
			},
			Gender:                   types.Gender_MALE,
			Email:                    "<EMAIL>",
			Nationality:              types.Nationality_NATIONALITY_INDIAN,
			ResidentialStatus:        types.ResidentialStatus_RESIDENT_INDIVIDUAL,
			PoliticallyExposedStatus: types.PoliticallyExposedStatus_POLITICALLY_EXPOSED_STATUS_NOT_APPLICABLE,
			Occupation:               "NA",
			MaritalStatus:            types.MaritalStatus_UNMARRIED,
			Photo: &types.DocumentProof{
				Photo: []*commontypes.Image{{
					ImageType: commontypes.ImageType_JPEG,
					ImageUrl:  "image-url",
				}},
			},
		},
		KycData: &userPb.KycData{StatusData: &woPb.KraStatusData{
			PanStatusDetails: &woPb.KraStatusData_PanStatusDetails{
				PanNumber: "PanNumber2",
				Name:      "Name2",
			},
		}},
	}
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()

	var teardown func()
	dbName, conf, _, db, teardown := test.InitTestServer()

	testSuite = TestSuite{
		db:     db,
		conf:   conf,
		dbName: dbName,
	}

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestService_CreateOrUpdateUser(t *testing.T) {
	type fields struct {
		user *userPb.User
	}
	type args struct {
		ctx                  context.Context
		newUser              *userPb.User
		forceUpdateFieldList []ForceUpdateField
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "update provided fields flow",
			fields: fields{user: userOutput1},
			args: args{
				ctx:     context.Background(),
				newUser: userInput1,
			},
			wantErr: false,
		},
		{
			name:   "update provided fields flow with force update field",
			fields: fields{user: userOutput2},
			args: args{
				ctx:                  context.Background(),
				newUser:              userInput2,
				forceUpdateFieldList: []ForceUpdateField{EmailId},
			},
			wantErr: false,
		},
		{
			name:   "update provided fields flow without force update field",
			fields: fields{user: userOutput3},
			args: args{
				ctx:                  context.Background(),
				newUser:              userInput2,
				forceUpdateFieldList: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testv2.PrepareRandomScopedCrdbDatabase(t, testSuite.conf.EpifiDb, testSuite.dbName, testSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			s := &Service{
				UserDao: impl.NewCrdbUserDao(testSuite.db),
			}
			if err := s.CreateOrUpdateUser(tt.args.ctx, tt.args.newUser, tt.args.forceUpdateFieldList); (err != nil) != tt.wantErr {
				t.Errorf("CreateOrUpdateUser() error = %v, wantErr %v", err, tt.wantErr)
			}
			user, getErr := s.UserDao.GetByActorId(tt.args.ctx, tt.args.newUser.GetActorId())
			if getErr != nil {
				t.Errorf("GetUserByActorId() error = %v", getErr)
			}
			user = getComparableUser(user)
			if !proto.Equal(user, tt.fields.user) {
				t.Errorf("GetUserByActorId() got = %v, want = %v", user, tt.fields.user)
			}
		})
	}
}

func getComparableUser(user *userPb.User) *userPb.User {
	user.Id = ""
	user.CreatedAt = nil
	user.UpdatedAt = nil
	return user
}
