package user

import (
	"context"
	"fmt"

	"github.com/thoas/go-funk"
	"go.uber.org/zap"

	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/wealthonboarding/user"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/wealthonboarding/dao"

	"github.com/pkg/errors"
)

// ForceUpdateField is used to signify fields which can be forcefully updated
type ForceUpdateField int8

const (
	PhoneNumber ForceUpdateField = iota
	EmailId
)

type IService interface {
	// CreateOrUpdateUser creates a single record for an actor which can be used to show the user their wealth profile
	// If a record is already present for the user, we iterate over the fields and update them in case they currently hold a zero value like 0, "", or nil.
	// ForceUpdateField is used when we want to update fields even when they currently hold a non-zero value. This is currently done for updating the mobile number
	// and/or email of the user in case they re-onboard to the app with a different phone number / email during bank onboarding.
	CreateOrUpdateUser(ctx context.Context, newUser *userPb.User, forceUpdateFields []ForceUpdateField) error
	GetByActorId(ctx context.Context, actorId string) (*userPb.User, error)
	Create(ctx context.Context, newUser *userPb.User) (*userPb.User, error)
}

type Service struct {
	UserDao dao.UserDao
}

func NewService(userDao dao.UserDao) *Service {
	return &Service{
		UserDao: userDao,
	}
}

func (s *Service) GetByActorId(ctx context.Context, actorId string) (*userPb.User, error) {
	user, err := s.UserDao.GetByActorId(ctx, actorId)
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching user by actorId")
	}
	return user, nil
}

func (s *Service) CreateOrUpdateUser(ctx context.Context, newUser *userPb.User, forceUpdateFieldList []ForceUpdateField) error {
	if newUser.GetActorId() == "" {
		return errors.New("actor_id cannot be empty")
	}
	actorId := newUser.GetActorId()

	// get user by actor id, if not present, create user
	user, daoErr := s.UserDao.GetByActorId(ctx, actorId)
	if daoErr != nil {
		if errors.Is(daoErr, epifierrors.ErrRecordNotFound) {
			logger.Info(ctx, fmt.Sprintf("user not found with actorId: %v, creating new user", actorId))
			_, daoErr = s.UserDao.Create(ctx, newUser)
			if daoErr != nil {
				return errors.Wrap(daoErr, fmt.Sprintf("error in creating user with actorId: %v", actorId))
			}
			return nil
		} else {
			return errors.Wrap(daoErr, "error in getting user by actorId")
		}
	}

	// update user details
	updateUserData(user, newUser, forceUpdateFieldList)
	// update user data in db
	updateErr := s.UserDao.Update(ctx, user, []userPb.UserFieldMask{
		userPb.UserFieldMask_USER_FIELD_MASK_SCHEME_CODE,
		userPb.UserFieldMask_USER_FIELD_MASK_METADATA,
		userPb.UserFieldMask_USER_FIELD_MASK_PERSONAL_DETAILS,
		userPb.UserFieldMask_USER_FIELD_MASK_KYC_DATA,
	})
	if updateErr != nil {
		return errors.Wrap(updateErr, fmt.Sprintf("error in updating user data for actorId: %v", actorId))
	}
	return nil
}

func (s *Service) Create(ctx context.Context, newUser *userPb.User) (*userPb.User, error) {
	user, err := s.UserDao.Create(ctx, newUser)
	if err != nil {
		return nil, errors.Wrap(err, "error while creating wealth user")
	}
	return user, nil
}

// nolint: funlen
func updateUserData(user *userPb.User, newUser *userPb.User, forceUpdateFieldList []ForceUpdateField) {
	// update scheme code
	if newUser.GetSchemeCode() != userPb.SchemeCode_SCHEME_CODE_UNSPECIFIED {
		user.SchemeCode = newUser.GetSchemeCode()
	}

	// update metadata
	if user.GetMetadata() == nil {
		user.Metadata = &userPb.UserMetadata{}
	}
	if newUser.GetMetadata() != nil {
		if newUser.GetMetadata().GetCustomerIpAddress() != "" {
			user.GetMetadata().CustomerIpAddress = newUser.GetMetadata().GetCustomerIpAddress()
		}
	}

	// update personal details
	if user.GetPersonalDetails() == nil {
		user.PersonalDetails = &userPb.PersonalDetails{}
	}
	if newUser.GetPersonalDetails() != nil {
		if newUser.GetPersonalDetails().GetName() != nil {
			user.GetPersonalDetails().Name = newUser.GetPersonalDetails().GetName()
		}
		if newUser.GetPersonalDetails().GetGender() != types.Gender_GENDER_UNSPECIFIED {
			user.GetPersonalDetails().Gender = newUser.GetPersonalDetails().GetGender()
		}
		if newUser.GetPersonalDetails().GetPhoneNumber() != nil && canUpdate(user, forceUpdateFieldList, PhoneNumber) {
			user.GetPersonalDetails().PhoneNumber = newUser.GetPersonalDetails().GetPhoneNumber()
		}
		if newUser.GetPersonalDetails().GetEmail() != "" && canUpdate(user, forceUpdateFieldList, EmailId) {
			user.GetPersonalDetails().Email = newUser.GetPersonalDetails().GetEmail()
		}
		if newUser.GetPersonalDetails().GetDob() != nil {
			user.GetPersonalDetails().Dob = newUser.GetPersonalDetails().GetDob()
		}
		if len(newUser.GetPersonalDetails().GetPhoto().GetPhoto()) > 0 && newUser.GetPersonalDetails().GetPhoto().GetPhoto()[0] != nil {
			user.GetPersonalDetails().Photo = newUser.GetPersonalDetails().GetPhoto()
		}
		if newUser.GetPersonalDetails().GetMaritalStatus() != types.MaritalStatus_UNSPECIFIED {
			user.GetPersonalDetails().MaritalStatus = newUser.GetPersonalDetails().GetMaritalStatus()
		}
		if newUser.GetPersonalDetails().GetFatherName() != nil {
			user.GetPersonalDetails().FatherName = newUser.GetPersonalDetails().GetFatherName()
		}
		if newUser.GetPersonalDetails().GetMotherName() != nil {
			user.GetPersonalDetails().MotherName = newUser.GetPersonalDetails().GetMotherName()
		}
		if newUser.GetPersonalDetails().GetNominees() != nil {
			user.GetPersonalDetails().Nominees = newUser.GetPersonalDetails().GetNominees()
		}
		if newUser.GetPersonalDetails().GetPoliticallyExposedStatus() != types.PoliticallyExposedStatus_POLITICALLY_EXPOSED_STATUS_UNSPECIFIED {
			user.GetPersonalDetails().PoliticallyExposedStatus = newUser.GetPersonalDetails().GetPoliticallyExposedStatus()
		}
		if newUser.GetPersonalDetails().GetSignature() != nil {
			user.GetPersonalDetails().Signature = newUser.GetPersonalDetails().GetSignature()
		}
		if newUser.GetPersonalDetails().GetOccupation() != "" {
			user.GetPersonalDetails().Occupation = newUser.GetPersonalDetails().GetOccupation()
		}
		if newUser.GetPersonalDetails().GetPanDetails() != nil {
			user.GetPersonalDetails().PanDetails = newUser.GetPersonalDetails().GetPanDetails()
		}
		if newUser.GetPersonalDetails().GetBankDetails() != nil {
			user.GetPersonalDetails().BankDetails = newUser.GetPersonalDetails().GetBankDetails()
		}
		if newUser.GetPersonalDetails().GetNationality() != types.Nationality_NATIONALITY_UNSPECIFIED {
			user.GetPersonalDetails().Nationality = newUser.GetPersonalDetails().GetNationality()
		}
		if newUser.GetPersonalDetails().GetResidentialStatus() != types.ResidentialStatus_RESIDENTIAL_STATUS_UNSPECIFIED {
			user.GetPersonalDetails().ResidentialStatus = newUser.GetPersonalDetails().GetResidentialStatus()
		}
		if newUser.GetPersonalDetails().GetIncomeSlab() != types.IncomeSlab_INCOME_SLAB_UNSPECIFIED {
			user.GetPersonalDetails().IncomeSlab = newUser.GetPersonalDetails().GetIncomeSlab()
		}
		if newUser.GetPersonalDetails().GetPermanentAddress() != nil {
			user.GetPersonalDetails().PermanentAddress = newUser.GetPersonalDetails().GetPermanentAddress()
		}
		if newUser.GetPersonalDetails().GetEmploymentData() != nil {
			user.GetPersonalDetails().EmploymentData = newUser.GetPersonalDetails().GetEmploymentData()
		}
	}

	// update kyc data
	if user.GetKycData() == nil {
		user.KycData = &userPb.KycData{}
	}
	if newUser.GetKycData() != nil {
		if newUser.GetKycData().GetStatusData() != nil && newUser.GetKycData().GetStatusData().GetPanStatusDetails() != nil {
			user.GetKycData().StatusData = newUser.GetKycData().GetStatusData()
		}
	}

}

// canUpdate returns a bool value of whether we can update the field based on the list provided for update
// this is done on best effort basis, and we just log the error
func canUpdate(user *userPb.User, forceUpdateFieldList []ForceUpdateField, field ForceUpdateField) bool {
	switch field {
	case PhoneNumber:
		// no value is set in phone number, we can update the field
		if user.GetPersonalDetails().GetPhoneNumber() == nil {
			return true
		}
		// value is present for phone number, we need to check if its present in the force update field list
		return funk.Contains(forceUpdateFieldList, field)
	case EmailId:
		// no value is set in email id, we can update the field
		if user.GetPersonalDetails().GetEmail() == "" {
			return true
		}
		// value is present for email, we need to check if its present in the force update field list
		return funk.Contains(forceUpdateFieldList, field)
	default:
		logger.ErrorNoCtx("update field not supported yet", zap.Int8("field", int8(field)))
		return false
	}
}
