//go:build wireinject
// +build wireinject

package wire

import (
	"fmt"
	"net/http"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
	"github.com/slack-go/slack"
	"golang.org/x/net/context"
	gormv2 "gorm.io/gorm"

	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/aws/v2/kinesis"
	"github.com/epifi/be-common/pkg/aws/v2/lambda"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cfg"
	cmdtypes "github.com/epifi/be-common/pkg/cmd/types"
	oncev2 "github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/docs"
	"github.com/epifi/gamma/api/employment"
	catalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	invProfile "github.com/epifi/gamma/api/investment/profile"
	rmsPb "github.com/epifi/gamma/api/rms/manager"
	savingsPb "github.com/epifi/gamma/api/savings"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	obfuscatorPb "github.com/epifi/gamma/api/user/obfuscator"
	ncPb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	vgBankCustomerPb "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	ckycVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/ckyc"
	cvlVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/cvl"
	digilockerVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/digilocker"
	digioVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/digio"
	"github.com/epifi/gamma/api/vendorgateway/wealth/inhouseocr"
	manchVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/manch"
	nsdlVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/nsdl"
	wealthOB "github.com/epifi/gamma/api/wealthonboarding"
	woCkycPb "github.com/epifi/gamma/api/wealthonboarding/ckyc"
	mfDaoImpl "github.com/epifi/gamma/investment/mutualfund/dao/impl"
	pqPkg "github.com/epifi/gamma/pkg/persistentqueue"
	"github.com/epifi/gamma/wealthonboarding"
	activityProcessorPb "github.com/epifi/gamma/wealthonboarding/activity"
	woCkyc "github.com/epifi/gamma/wealthonboarding/ckyc"
	"github.com/epifi/gamma/wealthonboarding/ckyc_helper"
	wealthComms "github.com/epifi/gamma/wealthonboarding/comms"
	"github.com/epifi/gamma/wealthonboarding/config"
	workerConf "github.com/epifi/gamma/wealthonboarding/config/worker"
	"github.com/epifi/gamma/wealthonboarding/consumer"
	wealthCx "github.com/epifi/gamma/wealthonboarding/cx"
	"github.com/epifi/gamma/wealthonboarding/dao"
	daoImpl "github.com/epifi/gamma/wealthonboarding/dao/impl"
	"github.com/epifi/gamma/wealthonboarding/dao/provider"
	"github.com/epifi/gamma/wealthonboarding/developer"
	"github.com/epifi/gamma/wealthonboarding/developer/processor"
	"github.com/epifi/gamma/wealthonboarding/esign"
	"github.com/epifi/gamma/wealthonboarding/helper"
	"github.com/epifi/gamma/wealthonboarding/kra_data"
	manualreview "github.com/epifi/gamma/wealthonboarding/manual_review"
	"github.com/epifi/gamma/wealthonboarding/ocr"
	"github.com/epifi/gamma/wealthonboarding/orchestrator"
	"github.com/epifi/gamma/wealthonboarding/orchestrator/steps"
	"github.com/epifi/gamma/wealthonboarding/release"
	repairData "github.com/epifi/gamma/wealthonboarding/repair_data"
	"github.com/epifi/gamma/wealthonboarding/troubleshooting"
	woTypes "github.com/epifi/gamma/wealthonboarding/types"
	"github.com/epifi/gamma/wealthonboarding/user"
)

func gormProvider(db cmdtypes.EpifiWealthCRDB) *gormv2.DB {
	return db
}

func redisProvider(redisClient woTypes.WealthRedisStore) *redis.Client {
	return redisClient
}

func S3BucketProvider(conf *config.Config) string { return conf.S3Conf.Bucket }

func vendorReqS3ClientProvider(awsConf aws.Config, conf *config.Config) woTypes.VendorRequestS3Client {
	return s3.NewClient(awsConf, conf.VendorRequestS3Conf.Bucket)
}

func InitializeService(
	db cmdtypes.EpifiWealthCRDB,
	conf *config.Config,
	publisher woTypes.RefreshFaceMatchStatusSqsPublisher,
	userClient userPb.UsersClient,
	userGroupClient userGroupPb.GroupClient,
	actorClient actorPb.ActorClient,
	livenessClient livenessPb.LivenessClient,
	vgNsdlClient nsdlVgPb.NsdlClient,
	vgCvlClient cvlVgPb.CvlClient,
	ckycClient ckycVgPb.CkycClient,
	docsClient docs.DocsClient,
	manchVgClient manchVgPb.ManchClient,
	digioVgClient digioVgPb.DigioClient,
	consentClient consent.ConsentClient,
	empClient employment.EmploymentClient,
	ocrClient inhouseocr.OcrClient,
	authClient authPb.AuthClient,
	vgDigilockerClient digilockerVgPb.DigilockerClient,
	stepsRetryDelayPublisher orchestrator.WealthOnboardingStepsRetrySqsCustomDelayPublisher,
	commsClient commsPb.CommsClient,
	obfuscatorClient obfuscatorPb.ObfuscatorClient,
	eventBroker events.Broker,
	userCommsDelayPublisher orchestrator.UserCommsDelayPublisher,
	ncClient ncPb.UNNameCheckClient,
	redisClient woTypes.WealthRedisStore,
	rmsClient rmsPb.RuleManagerClient,
	awsConf aws.Config,
	celestialClient celestialPb.CelestialClient,
	savingsClient savingsPb.SavingsClient,
	invProfileClient invProfile.InvestmentProfileServiceClient,
	vgBankCustomerClient vgBankCustomerPb.CustomerClient,
	bankCustomerClient bankCustomerPb.BankCustomerServiceClient,
) (*wealthonboarding.Service, error) {
	wire.Build(
		S3BucketProvider,
		s3.S3ClientWireSet,
		vendorReqS3ClientProvider,
		storagev2.IdempotentTxnExecutorWireSet,
		lock.DefaultLockMangerWireSet,
		release.EvaluatorWireSet,
		steps.NewDataCollectionStep,
		steps.NewVerifyPanStep,
		steps.NewFetchKycInfoStep,
		steps.NewPerformFaceMatchStep,
		wire.NewSet(ocr.NewOcrHelper, wire.Bind(new(ocr.IOcrHelper), new(*ocr.OcrHelperImpl))),
		steps.NewCollectMissingDataStep,
		wire.NewSet(helper.NewDocProofHelperImpl, wire.Bind(new(helper.DocumentHelper), new(*helper.DocHelperImpl))),
		wire.NewSet(ocr.NewInhouseOcr, wire.Bind(new(ocr.Ocr), new(*ocr.InhouseOcr))),
		helper.CommonHelperWireSet,
		steps.NewTakeConfirmationOnPepAndCitizenship,
		steps.NewCreateAndSignKraDocketStep,
		steps.NewSignAgreementStep,
		steps.NewPerformLivenessStep,
		steps.NewUploadKraDocketStep,
		steps.NewDownloadKraDocketStep,
		steps.NewPreInvestmentDataCollectionStep,
		steps.NewConsolidatedManualReviewStep,
		steps.NewDownloadFromDigilocker,
		steps.NewAdvisoryAgreementStep,
		wire.NewSet(kra_data.NewCvlDataService, wire.Bind(new(kra_data.KraData), new(*kra_data.CvlDataService))),
		wire.NewSet(esign.NewManchService, wire.Bind(new(esign.ESignManch), new(*esign.ManchService))),
		wire.NewSet(esign.NewDigioService, wire.Bind(new(esign.ESignDigio), new(*esign.DigioService))),
		esign.NewESignProviderService,
		pqPkg.NewPersistentQueue,
		repairData.NewRepairIP,
		repairData.NewRepairBankDetails,
		repairData.NewRepairProvider,
		wire.NewSet(user.NewService, wire.Bind(new(user.IService), new(*user.Service))),
		wire.NewSet(daoImpl.NewCrdbUserDao, wire.Bind(new(dao.UserDao), new(*daoImpl.CrdbUserDao))),
		wire.NewSet(esign.NewESignService, wire.Bind(new(esign.ESign), new(*esign.ESignService))),
		wire.NewSet(daoImpl.NewESignDetailsDao, wire.Bind(new(dao.ESignDetailsDao), new(*daoImpl.CrdbESignDetailsDao))),
		wire.NewSet(daoImpl.NewVendorRequestProducer, daoImpl.NewVendorRequestDao, daoImpl.NewVendorRequestDaoAndProducer, provider.ProvideVendorRequestStorageImpl),
		wire.NewSet(orchestrator.NewHandlerFactory, wire.Bind(new(orchestrator.IHandlerFactory), new(*orchestrator.HandlerFactory))),
		wire.NewSet(daoImpl.NewCrdbOnboardingDetailsDao, wire.Bind(new(dao.OnboardingDetailsDao), new(*daoImpl.CrdbOnboardingDetailsDao))),
		wire.NewSet(daoImpl.NewCrdbOnboardingStepDetailsDao, wire.Bind(new(dao.OnboardingStepDetailsDao), new(*daoImpl.CrdbOnboardingStepDetailsDao))),
		wire.NewSet(orchestrator.NewService, wire.Bind(new(orchestrator.IService), new(*orchestrator.Service))),
		wire.NewSet(troubleshooting.NewHandlerFactory, wire.Bind(new(troubleshooting.IHandlerFactory), new(*troubleshooting.HandlerFactory))),
		wire.NewSet(daoImpl.NewCrdbManualReviewDao, wire.Bind(new(dao.ManualReviewDao), new(*daoImpl.CrdbManualReviewDao))),
		wire.NewSet(manualreview.NewService, wire.Bind(new(manualreview.IManualReview), new(*manualreview.Service))),
		wire.NewSet(ckyc_helper.NewCkycHelper, wire.Bind(new(ckyc_helper.ICkycHelper), new(*ckyc_helper.CkycHelperImpl))),
		troubleshooting.NewDataCollectionHandler,
		troubleshooting.NewPanVerificationHandler,
		troubleshooting.NewFetchKycInfoFromKraHandler,
		troubleshooting.NewCollectMissingPersonalInfoHandler,
		troubleshooting.NewLivenessHandler,
		troubleshooting.NewFaceMatchHandler,
		troubleshooting.NewCreateAndSignKraDocketHandler,
		troubleshooting.NewConsolidatedManualReviewHandler,
		troubleshooting.NewConfirmPepAndCitizenshipHandler,
		troubleshooting.NewInvestmentDataCollectionHandler,
		troubleshooting.NewUploadKraDocketHandler,
		troubleshooting.NewDownloadKraDocsHandler,
		wealthonboarding.NewService,
		wire.NewSet(wealthComms.NewService, wire.Bind(new(wealthComms.IComms), new(*wealthComms.Service))),
		gormProvider,
		oncev2.NewDoOnce,
		redisProvider,
		LambdaClientProvider,
		HttpClientProvider,
		KinesisProducerProvider,
		InitializeWealthCkycService,
		steps.NewTakeUserConsent,
		steps.NewRiskProfilingStep,
		steps.NewNomineeModificationStep,
		steps.NewRecordWealthMITCConsent,
	)
	return &wealthonboarding.Service{}, nil
}

func InitializeConsumerService(
	livenessClient livenessPb.LivenessClient,
	wealthOnbClient wealthOB.WealthOnboardingClient,
	afuWealthPublisher woTypes.AuthFactorUpdateWealthPublisher,
	commsClient commsPb.CommsClient,
	db cmdtypes.EpifiWealthCRDB,
	ckycClient ckycVgPb.CkycClient,
	actorClient actorPb.ActorClient,
	userClient userPb.UsersClient,
	userGroupClient userGroupPb.GroupClient,
	conf *config.Config,
	employmentClient employment.EmploymentClient,
	awsConf aws.Config,
	savingsClient savingsPb.SavingsClient,
	authClient authPb.AuthClient,
	vgBankCustomerClient vgBankCustomerPb.CustomerClient,
	bankCustomerClient bankCustomerPb.BankCustomerServiceClient,
) (*consumer.Service,
	error) {
	wire.Build(
		storagev2.IdempotentTxnExecutorWireSet,
		wire.NewSet(daoImpl.NewCrdbUserDao, wire.Bind(new(dao.UserDao), new(*daoImpl.CrdbUserDao))),
		wire.NewSet(user.NewService, wire.Bind(new(user.IService), new(*user.Service))),
		wire.NewSet(daoImpl.NewCrdbOnboardingStepDetailsDao, wire.Bind(new(dao.OnboardingStepDetailsDao), new(*daoImpl.CrdbOnboardingStepDetailsDao))),
		wire.NewSet(daoImpl.NewCrdbOnboardingDetailsDao, wire.Bind(new(dao.OnboardingDetailsDao), new(*daoImpl.CrdbOnboardingDetailsDao))),
		consumer.NewService,
		gormProvider,
		InitializeWealthCkycService,
	)
	return &consumer.Service{}, nil
}

func InitializeESignConsumerService(db cmdtypes.EpifiWealthCRDB, vgManchClient manchVgPb.ManchClient, vgDigioClient digioVgPb.DigioClient, eSignService *esign.ESignService, config *config.Config, awsConf aws.Config) *consumer.ESignConsumerService {
	wire.Build(
		S3BucketProvider,
		s3.S3ClientWireSet,
		wire.NewSet(daoImpl.NewESignDetailsDao, wire.Bind(new(dao.ESignDetailsDao), new(*daoImpl.CrdbESignDetailsDao))),
		wire.NewSet(esign.NewManchService, wire.Bind(new(esign.ESignManch), new(*esign.ManchService))),
		wire.NewSet(esign.NewDigioService, wire.Bind(new(esign.ESignDigio), new(*esign.DigioService))),
		esign.NewESignProviderService,
		consumer.NewESignConsumerService,
	)
	return &consumer.ESignConsumerService{}
}

func InitializeDevService(db cmdtypes.EpifiWealthCRDB) *developer.WoDevService {
	wire.Build(
		wire.NewSet(daoImpl.NewCrdbOnboardingDetailsDao, wire.Bind(new(dao.OnboardingDetailsDao), new(*daoImpl.CrdbOnboardingDetailsDao))),
		wire.NewSet(daoImpl.NewCrdbOnboardingStepDetailsDao, wire.Bind(new(dao.OnboardingStepDetailsDao), new(*daoImpl.CrdbOnboardingStepDetailsDao))),
		wire.NewSet(daoImpl.NewESignDetailsDao, wire.Bind(new(dao.ESignDetailsDao), new(*daoImpl.CrdbESignDetailsDao))),
		wire.NewSet(daoImpl.NewCrdbUserDao, wire.Bind(new(dao.UserDao), new(*daoImpl.CrdbUserDao))),
		wire.NewSet(daoImpl.NewCrdbCkycDataDao, wire.Bind(new(dao.CkycDataDao), new(*daoImpl.CrdbCkycDataDao))),
		processor.NewDevWoOnbEntity,
		processor.NewDDevWoESignEntity,
		developer.NewDevFactory,
		developer.NewWoDevService,
		processor.NewDevWoOnbUsersEntity,
		processor.NewDevWoCKYCDataEntity,
	)
	return &developer.WoDevService{}
}

func InitializeWealthCxService(
	db cmdtypes.EpifiWealthCRDB,
	conf *config.Config,
	publisher woTypes.RefreshFaceMatchStatusSqsPublisher,
	userClient userPb.UsersClient,
	userGroupClient userGroupPb.GroupClient,
	actorClient actorPb.ActorClient,
	livenessClient livenessPb.LivenessClient,
	vgNsdlClient nsdlVgPb.NsdlClient,
	vgCvlClient cvlVgPb.CvlClient,
	ckycClient ckycVgPb.CkycClient,
	docsClient docs.DocsClient,
	manchVgClient manchVgPb.ManchClient,
	digioVgClient digioVgPb.DigioClient,
	consentClient consent.ConsentClient,
	empClient employment.EmploymentClient,
	ocrClient inhouseocr.OcrClient,
	authClient authPb.AuthClient,
	vgDigilockerClient digilockerVgPb.DigilockerClient,
	stepsRetryDelayPublisher orchestrator.WealthOnboardingStepsRetrySqsCustomDelayPublisher,
	commsClient commsPb.CommsClient,
	obfuscatorClient obfuscatorPb.ObfuscatorClient,
	eventBroker events.Broker,
	userCommsDelayPublisher orchestrator.UserCommsDelayPublisher,
	ncClient ncPb.UNNameCheckClient,
	redisClient woTypes.WealthRedisStore,
	rmsClient rmsPb.RuleManagerClient,
	awsConf aws.Config,
	celestialClient celestialPb.CelestialClient,
	savingsClient savingsPb.SavingsClient,
	invProfileClient invProfile.InvestmentProfileServiceClient,
	vgBankCustomerClient vgBankCustomerPb.CustomerClient,
	bankCustomerClient bankCustomerPb.BankCustomerServiceClient,
) (*wealthCx.Service, error) {
	wire.Build(
		S3BucketProvider,
		s3.S3ClientWireSet,
		pqPkg.NewPersistentQueue,
		wealthCx.NewService,
		storagev2.IdempotentTxnExecutorWireSet,
		wire.NewSet(daoImpl.NewCrdbOnboardingDetailsDao, wire.Bind(new(dao.OnboardingDetailsDao), new(*daoImpl.CrdbOnboardingDetailsDao))),
		wire.NewSet(daoImpl.NewCrdbManualReviewDao, wire.Bind(new(dao.ManualReviewDao), new(*daoImpl.CrdbManualReviewDao))),
		wire.NewSet(helper.NewDocProofHelperImpl, wire.Bind(new(helper.DocumentHelper), new(*helper.DocHelperImpl))),
		wire.NewSet(manualreview.NewService, wire.Bind(new(manualreview.IManualReview), new(*manualreview.Service))),
		wire.NewSet(daoImpl.NewCrdbOnboardingStepDetailsDao, wire.Bind(new(dao.OnboardingStepDetailsDao), new(*daoImpl.CrdbOnboardingStepDetailsDao))),
		wire.NewSet(daoImpl.NewOnboardingSummaryCrdb, wire.Bind(new(dao.OnboardingSummary), new(*daoImpl.OnboardingSummaryCrdb))),
		gormProvider,
		HttpClientProvider,
		LambdaClientProvider,
		InitializeService,
	)
	return &wealthCx.Service{}, nil
}

func InitializeWealthCkycService(
	db cmdtypes.EpifiWealthCRDB,
	ckycClient ckycVgPb.CkycClient,
	actorClient actorPb.ActorClient,
	userClient userPb.UsersClient,
	userGroupClient userGroupPb.GroupClient,
	conf *config.Config,
	employmentClient employment.EmploymentClient,
	awsConf aws.Config,
	savingsClient savingsPb.SavingsClient,
	authClient authPb.AuthClient,
	vgBankCustomerClient vgBankCustomerPb.CustomerClient,
	bankCustomerClient bankCustomerPb.BankCustomerServiceClient,
) (*woCkyc.Service, error) {
	wire.Build(
		S3BucketProvider,
		wire.NewSet(user.NewService, wire.Bind(new(user.IService), new(*user.Service))),
		wire.NewSet(daoImpl.NewCrdbUserDao, wire.Bind(new(dao.UserDao), new(*daoImpl.CrdbUserDao))),
		vendorReqS3ClientProvider,
		s3.S3ClientWireSet,
		wire.NewSet(helper.NewDocProofHelperImpl, wire.Bind(new(helper.DocumentHelper), new(*helper.DocHelperImpl))),
		helper.CommonHelperWireSet,
		wire.NewSet(daoImpl.NewCrdbCkycDataDao, wire.Bind(new(dao.CkycDataDao), new(*daoImpl.CrdbCkycDataDao))),
		wire.NewSet(daoImpl.NewVendorRequestProducer, daoImpl.NewVendorRequestDao, daoImpl.NewVendorRequestDaoAndProducer, provider.ProvideVendorRequestStorageImpl),
		woCkyc.NewService,
		HttpClientProvider,
		LambdaClientProvider,
		KinesisProducerProvider,
	)
	return &woCkyc.Service{}, nil
}

func KinesisProducerProvider(awsV2Config aws.Config, conf *config.Config) (woTypes.VendorRequestProducer, error) {
	kinesisClient := kinesis.NewKinesisClient(&awsV2Config)

	var vendorRequestProducer woTypes.VendorRequestProducer
	vendorRequestProducer, err := kinesis.NewKinesisProducer(context.Background(), conf.VendorRequestProducer, kinesisClient)
	if err != nil {
		err = fmt.Errorf("failed to initialise producer: %w", err)
	}
	return vendorRequestProducer, err
}

func LambdaClientProvider(awsConf aws.Config) lambda.LambdaClient {
	return lambda.NewAwsLambda(awsConf)
}

func HttpClientProvider() *http.Client {
	return &http.Client{}
}

func WealthS3ClientProvider(client woTypes.WealthS3Client) s3.S3Client {
	return client
}

func InitializeActivityProcessor(
	db cmdtypes.EpifiWealthCRDB,
	conf *workerConf.Config,
	s3Client s3.S3Client,
	ocrClient inhouseocr.OcrClient,
	ckycClient woCkycPb.CkycClient,
	awsConf aws.Config,
	userClient userPb.UsersClient,
	actorClient actorPb.ActorClient,
	catalogManagerClient catalogPb.CatalogManagerClient,
) *activityProcessorPb.Processor {
	wire.Build(
		mfAumReconSlackAlertClientProvider,
		daoImpl.OnboardingDetailsDaoWireSet,
		activityProcessorPb.NewProcessor,
		mfDaoImpl.MutualfundWireSet,
		mfDaoImpl.OrderWireSet,
		mfDaoImpl.FolioLedgerWireSet,
		mfDaoImpl.OrderConfirmationInfoWireSet,
		storagev2.IdempotentTxnExecutorWireSet,
		idgen.NewClock,
		idgen.WireSet,
		gormProvider,
		datetime.WireDefaultTimeSet,
		helper.DocProofHelperWireSet,
		HttpClientProvider,
		ocr.InhouseOCRWireSet,
		LambdaClientProvider,
	)
	return &activityProcessorPb.Processor{}
}

func mfAumReconSlackAlertClientProvider(conf *workerConf.Config) *slack.Client {
	var slackClient *slack.Client
	if cfg.IsProdEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[workerConf.CaptureMfAumFileSlackBotOauthToken], slack.OptionDebug(false))
	} else if cfg.IsSimulatedEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[workerConf.CaptureMfAumFileSlackBotOauthToken], slack.OptionDebug(true))
	}
	return slackClient
}
