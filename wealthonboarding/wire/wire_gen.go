// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"fmt"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/aws/v2/kinesis"
	"github.com/epifi/be-common/pkg/aws/v2/lambda"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/liveness"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/docs"
	"github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/investment/mutualfund/catalog"
	"github.com/epifi/gamma/api/investment/profile"
	"github.com/epifi/gamma/api/rms/manager"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/obfuscator"
	"github.com/epifi/gamma/api/vendorgateway/namecheck"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	"github.com/epifi/gamma/api/vendorgateway/wealth/ckyc"
	"github.com/epifi/gamma/api/vendorgateway/wealth/cvl"
	"github.com/epifi/gamma/api/vendorgateway/wealth/digilocker"
	"github.com/epifi/gamma/api/vendorgateway/wealth/digio"
	"github.com/epifi/gamma/api/vendorgateway/wealth/inhouseocr"
	"github.com/epifi/gamma/api/vendorgateway/wealth/manch"
	"github.com/epifi/gamma/api/vendorgateway/wealth/nsdl"
	wealthonboarding2 "github.com/epifi/gamma/api/wealthonboarding"
	ckyc3 "github.com/epifi/gamma/api/wealthonboarding/ckyc"
	impl2 "github.com/epifi/gamma/investment/mutualfund/dao/impl"
	"github.com/epifi/gamma/pkg/persistentqueue"
	"github.com/epifi/gamma/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/activity"
	ckyc2 "github.com/epifi/gamma/wealthonboarding/ckyc"
	"github.com/epifi/gamma/wealthonboarding/ckyc_helper"
	comms2 "github.com/epifi/gamma/wealthonboarding/comms"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/config/worker"
	"github.com/epifi/gamma/wealthonboarding/consumer"
	"github.com/epifi/gamma/wealthonboarding/cx"
	"github.com/epifi/gamma/wealthonboarding/dao/impl"
	"github.com/epifi/gamma/wealthonboarding/dao/provider"
	"github.com/epifi/gamma/wealthonboarding/developer"
	"github.com/epifi/gamma/wealthonboarding/developer/processor"
	"github.com/epifi/gamma/wealthonboarding/esign"
	"github.com/epifi/gamma/wealthonboarding/helper"
	"github.com/epifi/gamma/wealthonboarding/kra_data"
	"github.com/epifi/gamma/wealthonboarding/manual_review"
	"github.com/epifi/gamma/wealthonboarding/ocr"
	"github.com/epifi/gamma/wealthonboarding/orchestrator"
	"github.com/epifi/gamma/wealthonboarding/orchestrator/steps"
	"github.com/epifi/gamma/wealthonboarding/release"
	"github.com/epifi/gamma/wealthonboarding/repair_data"
	"github.com/epifi/gamma/wealthonboarding/troubleshooting"
	types2 "github.com/epifi/gamma/wealthonboarding/types"
	user2 "github.com/epifi/gamma/wealthonboarding/user"
	"github.com/redis/go-redis/v9"
	"github.com/slack-go/slack"
	"golang.org/x/net/context"
	"gorm.io/gorm"
	"net/http"
)

// Injectors from wire.go:

func InitializeService(db types.EpifiWealthCRDB, conf *config.Config, publisher types2.RefreshFaceMatchStatusSqsPublisher, userClient user.UsersClient, userGroupClient group.GroupClient, actorClient actor.ActorClient, livenessClient liveness.LivenessClient, vgNsdlClient nsdl.NsdlClient, vgCvlClient cvl.CvlClient, ckycClient ckyc.CkycClient, docsClient docs.DocsClient, manchVgClient manch.ManchClient, digioVgClient digio.DigioClient, consentClient consent.ConsentClient, empClient employment.EmploymentClient, ocrClient inhouseocr.OcrClient, authClient auth.AuthClient, vgDigilockerClient digilocker.DigilockerClient, stepsRetryDelayPublisher orchestrator.WealthOnboardingStepsRetrySqsCustomDelayPublisher, commsClient comms.CommsClient, obfuscatorClient obfuscator.ObfuscatorClient, eventBroker events.Broker, userCommsDelayPublisher orchestrator.UserCommsDelayPublisher, ncClient namecheck.UNNameCheckClient, redisClient types2.WealthRedisStore, rmsClient manager.RuleManagerClient, awsConf aws.Config, celestialClient celestial.CelestialClient, savingsClient savings.SavingsClient, invProfileClient profile.InvestmentProfileServiceClient, vgBankCustomerClient customer.CustomerClient, bankCustomerClient bankcust.BankCustomerServiceClient) (*wealthonboarding.Service, error) {
	crdbOnboardingDetailsDao := impl.NewCrdbOnboardingDetailsDao(db)
	crdbOnboardingStepDetailsDao := impl.NewCrdbOnboardingStepDetailsDao(db)
	string2 := S3BucketProvider(conf)
	client := s3.NewClient(awsConf, string2)
	httpClient := HttpClientProvider()
	docHelperImpl := helper.NewDocProofHelperImpl(client, httpClient)
	vendorRequestDaoImpl := impl.NewVendorRequestDao(db)
	vendorRequestProducer, err := KinesisProducerProvider(awsConf, conf)
	if err != nil {
		return nil, err
	}
	vendorRequestS3Client := vendorReqS3ClientProvider(awsConf, conf)
	vendorRequestProducerImpl := impl.NewVendorRequestProducer(vendorRequestProducer, vendorRequestS3Client, conf)
	vendorRequestDaoAndProducerImpl := impl.NewVendorRequestDaoAndProducer(vendorRequestDaoImpl, vendorRequestProducerImpl)
	vendorRequestDao := provider.ProvideVendorRequestStorageImpl(vendorRequestDaoImpl, vendorRequestProducerImpl, vendorRequestDaoAndProducerImpl, conf)
	inhouseOcr := ocr.NewInhouseOcr(ocrClient)
	lambdaClient := LambdaClientProvider(awsConf)
	crdbUserDao := impl.NewCrdbUserDao(db)
	service := user2.NewService(crdbUserDao)
	commonHelper := helper.NewCommonHelper(actorClient, userClient, userGroupClient, lambdaClient, conf, empClient, service, bankCustomerClient, savingsClient)
	ckycService, err := InitializeWealthCkycService(db, ckycClient, actorClient, userClient, userGroupClient, conf, empClient, awsConf, savingsClient, authClient, vgBankCustomerClient, bankCustomerClient)
	if err != nil {
		return nil, err
	}
	ckycHelperImpl := ckyc_helper.NewCkycHelper(ckycClient, docHelperImpl, vendorRequestDao, inhouseOcr, commonHelper, ckycService, eventBroker)
	crdbManualReviewDao := impl.NewCrdbManualReviewDao(db)
	gormDB := gormProvider(db)
	persistentQueue := persistentqueue.NewPersistentQueue(gormDB)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	manualreviewService := manualreview.NewService(crdbManualReviewDao, persistentQueue, crdbIdempotentTxnExecutor, docHelperImpl)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickyPercentageConstraint := release.NewStickyPercentageConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	appPlatformConstraint := release.NewAppPlatformConstraint()
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickyPercentageConstraint, userGroupConstraint, appPlatformConstraint)
	evaluator := release.NewEvaluator(conf, constraintFactoryImpl)
	ocrHelperImpl := ocr.NewOcrHelper(docHelperImpl, inhouseOcr, conf)
	collectMissingDataStep := steps.NewCollectMissingDataStep(ckycHelperImpl, manualreviewService, conf, livenessClient, crdbOnboardingStepDetailsDao, evaluator, ocrHelperImpl, eventBroker)
	dataCollectionStep := steps.NewDataCollectionStep(conf, userClient, actorClient, ckycService)
	fetchKycInfoStep := steps.NewFetchKycInfoStep(vgCvlClient, commonHelper, conf, evaluator)
	performFaceMatchStep := steps.NewPerformFaceMatchStep(conf, livenessClient, publisher, crdbOnboardingStepDetailsDao, ckycHelperImpl)
	cvlDataService := kra_data.NewCvlDataService(docsClient, vgCvlClient, client, httpClient, conf, docHelperImpl, commonHelper)
	takeConfirmationOnPepAndCitizenship := steps.NewTakeConfirmationOnPepAndCitizenship(conf, consentClient, cvlDataService, docHelperImpl, commonHelper, crdbOnboardingStepDetailsDao, evaluator, obfuscatorClient, vgCvlClient)
	verifyPanStep := steps.NewVerifyPanStep(vgNsdlClient, conf, ncClient, evaluator)
	manchService := esign.NewManchService(manchVgClient)
	digioService := esign.NewDigioService(digioVgClient, conf)
	eSignProviderService := esign.NewESignProviderService(manchService, digioService)
	crdbESignDetailsDao := impl.NewESignDetailsDao(db)
	eSignService := esign.NewESignService(eSignProviderService, crdbESignDetailsDao, publisher, conf, client)
	createAndSignKraDocketStep := steps.NewCreateAndSignKraDocketStep(conf, crdbOnboardingStepDetailsDao, cvlDataService, ckycHelperImpl, eSignService, evaluator, client)
	signAgreementStep := steps.NewSignAgreementStep(conf, crdbOnboardingStepDetailsDao, cvlDataService, docHelperImpl)
	performLivenessStep := steps.NewPerformLivenessStep(conf, livenessClient, crdbOnboardingStepDetailsDao, manualreviewService, evaluator)
	commsService := comms2.NewService(commsClient, conf)
	uploadKraDocketStep := steps.NewUploadKraDocketStep(cvlDataService, client, crdbOnboardingStepDetailsDao, vgCvlClient, manualreviewService, conf, vgDigilockerClient, docHelperImpl, commsService, consentClient)
	downloadKraDocsStep := steps.NewDownloadKraDocketStep(cvlDataService, crdbOnboardingStepDetailsDao, vgCvlClient, client, conf, commonHelper, evaluator)
	investmentDataCollectionStep := steps.NewPreInvestmentDataCollectionStep(empClient, crdbOnboardingDetailsDao, commonHelper, vgCvlClient, conf)
	consolidatedManualReviewStep := steps.NewConsolidatedManualReviewStep(manualreviewService, conf, commsService, eventBroker)
	downloadFromDigilocker := steps.NewDownloadFromDigilocker(conf, vgDigilockerClient, docHelperImpl, cvlDataService)
	advisoryAgreementStep := steps.NewAdvisoryAgreementStep(eSignService, conf, evaluator, client)
	takeUserConsent := steps.NewTakeUserConsent(consentClient, evaluator)
	riskProfilingStep := steps.NewRiskProfilingStep(conf, evaluator, invProfileClient, consentClient)
	nomineeModificationStep := steps.NewNomineeModificationStep(evaluator, userClient)
	recordWealthMITCConsent := steps.NewRecordWealthMITCConsent(conf, consentClient, evaluator)
	handlerFactory := orchestrator.NewHandlerFactory(collectMissingDataStep, dataCollectionStep, fetchKycInfoStep, performFaceMatchStep, takeConfirmationOnPepAndCitizenship, verifyPanStep, createAndSignKraDocketStep, signAgreementStep, performLivenessStep, uploadKraDocketStep, downloadKraDocsStep, investmentDataCollectionStep, consolidatedManualReviewStep, downloadFromDigilocker, advisoryAgreementStep, takeUserConsent, riskProfilingStep, nomineeModificationStep, recordWealthMITCConsent)
	doOnce := once.NewDoOnce(gormDB)
	client2 := redisProvider(redisClient)
	clock := lock.NewRealClockProvider()
	uuidGenerator := idgen.NewUuidGenerator()
	redisLockManager := lock.NewRedisLockManager(client2, clock, uuidGenerator)
	orchestratorService := orchestrator.NewService(crdbOnboardingDetailsDao, crdbOnboardingStepDetailsDao, handlerFactory, stepsRetryDelayPublisher, conf, service, eventBroker, crdbIdempotentTxnExecutor, userCommsDelayPublisher, doOnce, redisLockManager, evaluator, rmsClient, userClient)
	dataCollectionHandler := troubleshooting.NewDataCollectionHandler(conf)
	panVerificationHandler := troubleshooting.NewPanVerificationHandler(conf)
	fetchKycInfoFromKraHandler := troubleshooting.NewFetchKycInfoFromKraHandler(conf)
	collectMissingPersonalInfoHandler := troubleshooting.NewCollectMissingPersonalInfoHandler(conf)
	livenessHandler := troubleshooting.NewLivenessHandler(conf)
	faceMatchHandler := troubleshooting.NewFaceMatchHandler(conf)
	createAndSignKraDocketHandler := troubleshooting.NewCreateAndSignKraDocketHandler(conf)
	confirmPepAndCitizenshipHandler := troubleshooting.NewConfirmPepAndCitizenshipHandler(conf)
	investmentDataCollectionHandler := troubleshooting.NewInvestmentDataCollectionHandler(conf)
	downloadKraDocsHandler := troubleshooting.NewDownloadKraDocsHandler(conf)
	uploadKraDocketHandler := troubleshooting.NewUploadKraDocketHandler(conf)
	consolidatedManualReviewHandler := troubleshooting.NewConsolidatedManualReviewHandler(conf)
	troubleshootingHandlerFactory := troubleshooting.NewHandlerFactory(dataCollectionHandler, panVerificationHandler, fetchKycInfoFromKraHandler, collectMissingPersonalInfoHandler, livenessHandler, faceMatchHandler, createAndSignKraDocketHandler, confirmPepAndCitizenshipHandler, investmentDataCollectionHandler, downloadKraDocsHandler, uploadKraDocketHandler, consolidatedManualReviewHandler)
	repairIP := repair_data.NewRepairIP(crdbOnboardingDetailsDao)
	repairBankDetails := repair_data.NewRepairBankDetails(commonHelper, crdbOnboardingDetailsDao)
	repairProvider := repair_data.NewRepairProvider(repairIP, repairBankDetails)
	wealthonboardingService := wealthonboarding.NewService(orchestratorService, client, conf, crdbOnboardingDetailsDao, crdbOnboardingStepDetailsDao, docHelperImpl, troubleshootingHandlerFactory, vgDigilockerClient, service, cvlDataService, repairProvider, ckycHelperImpl, ocrHelperImpl, eventBroker, crdbIdempotentTxnExecutor, actorClient, userClient, evaluator, rmsClient, celestialClient, invProfileClient, commonHelper)
	return wealthonboardingService, nil
}

func InitializeConsumerService(livenessClient liveness.LivenessClient, wealthOnbClient wealthonboarding2.WealthOnboardingClient, afuWealthPublisher types2.AuthFactorUpdateWealthPublisher, commsClient comms.CommsClient, db types.EpifiWealthCRDB, ckycClient ckyc.CkycClient, actorClient actor.ActorClient, userClient user.UsersClient, userGroupClient group.GroupClient, conf *config.Config, employmentClient employment.EmploymentClient, awsConf aws.Config, savingsClient savings.SavingsClient, authClient auth.AuthClient, vgBankCustomerClient customer.CustomerClient, bankCustomerClient bankcust.BankCustomerServiceClient) (*consumer.Service, error) {
	crdbOnboardingStepDetailsDao := impl.NewCrdbOnboardingStepDetailsDao(db)
	crdbOnboardingDetailsDao := impl.NewCrdbOnboardingDetailsDao(db)
	crdbUserDao := impl.NewCrdbUserDao(db)
	service := user2.NewService(crdbUserDao)
	ckycService, err := InitializeWealthCkycService(db, ckycClient, actorClient, userClient, userGroupClient, conf, employmentClient, awsConf, savingsClient, authClient, vgBankCustomerClient, bankCustomerClient)
	if err != nil {
		return nil, err
	}
	gormDB := gormProvider(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	consumerService := consumer.NewService(livenessClient, crdbOnboardingStepDetailsDao, wealthOnbClient, crdbOnboardingDetailsDao, service, userClient, actorClient, ckycService, afuWealthPublisher, crdbIdempotentTxnExecutor, conf, commsClient)
	return consumerService, nil
}

func InitializeESignConsumerService(db types.EpifiWealthCRDB, vgManchClient manch.ManchClient, vgDigioClient digio.DigioClient, eSignService *esign.ESignService, config2 *config.Config, awsConf aws.Config) *consumer.ESignConsumerService {
	crdbESignDetailsDao := impl.NewESignDetailsDao(db)
	string2 := S3BucketProvider(config2)
	client := s3.NewClient(awsConf, string2)
	manchService := esign.NewManchService(vgManchClient)
	digioService := esign.NewDigioService(vgDigioClient, config2)
	eSignProviderService := esign.NewESignProviderService(manchService, digioService)
	eSignConsumerService := consumer.NewESignConsumerService(crdbESignDetailsDao, client, eSignService, eSignProviderService)
	return eSignConsumerService
}

func InitializeDevService(db types.EpifiWealthCRDB) *developer.WoDevService {
	crdbOnboardingDetailsDao := impl.NewCrdbOnboardingDetailsDao(db)
	crdbOnboardingStepDetailsDao := impl.NewCrdbOnboardingStepDetailsDao(db)
	devWoOnbEntity := processor.NewDevWoOnbEntity(crdbOnboardingDetailsDao, crdbOnboardingStepDetailsDao)
	crdbESignDetailsDao := impl.NewESignDetailsDao(db)
	devWoESignEntity := processor.NewDDevWoESignEntity(crdbESignDetailsDao)
	crdbUserDao := impl.NewCrdbUserDao(db)
	devWoOnbUsersEntity := processor.NewDevWoOnbUsersEntity(crdbUserDao)
	crdbCkycDataDao := impl.NewCrdbCkycDataDao(db)
	devWoCKYCDataEntity := processor.NewDevWoCKYCDataEntity(crdbCkycDataDao)
	devFactory := developer.NewDevFactory(devWoOnbEntity, devWoESignEntity, devWoOnbUsersEntity, devWoCKYCDataEntity)
	woDevService := developer.NewWoDevService(devFactory)
	return woDevService
}

func InitializeWealthCxService(db types.EpifiWealthCRDB, conf *config.Config, publisher types2.RefreshFaceMatchStatusSqsPublisher, userClient user.UsersClient, userGroupClient group.GroupClient, actorClient actor.ActorClient, livenessClient liveness.LivenessClient, vgNsdlClient nsdl.NsdlClient, vgCvlClient cvl.CvlClient, ckycClient ckyc.CkycClient, docsClient docs.DocsClient, manchVgClient manch.ManchClient, digioVgClient digio.DigioClient, consentClient consent.ConsentClient, empClient employment.EmploymentClient, ocrClient inhouseocr.OcrClient, authClient auth.AuthClient, vgDigilockerClient digilocker.DigilockerClient, stepsRetryDelayPublisher orchestrator.WealthOnboardingStepsRetrySqsCustomDelayPublisher, commsClient comms.CommsClient, obfuscatorClient obfuscator.ObfuscatorClient, eventBroker events.Broker, userCommsDelayPublisher orchestrator.UserCommsDelayPublisher, ncClient namecheck.UNNameCheckClient, redisClient types2.WealthRedisStore, rmsClient manager.RuleManagerClient, awsConf aws.Config, celestialClient celestial.CelestialClient, savingsClient savings.SavingsClient, invProfileClient profile.InvestmentProfileServiceClient, vgBankCustomerClient customer.CustomerClient, bankCustomerClient bankcust.BankCustomerServiceClient) (*cx.Service, error) {
	gormDB := gormProvider(db)
	persistentQueue := persistentqueue.NewPersistentQueue(gormDB)
	crdbOnboardingDetailsDao := impl.NewCrdbOnboardingDetailsDao(db)
	crdbManualReviewDao := impl.NewCrdbManualReviewDao(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	string2 := S3BucketProvider(conf)
	client := s3.NewClient(awsConf, string2)
	httpClient := HttpClientProvider()
	docHelperImpl := helper.NewDocProofHelperImpl(client, httpClient)
	service := manualreview.NewService(crdbManualReviewDao, persistentQueue, crdbIdempotentTxnExecutor, docHelperImpl)
	wealthonboardingService, err := InitializeService(db, conf, publisher, userClient, userGroupClient, actorClient, livenessClient, vgNsdlClient, vgCvlClient, ckycClient, docsClient, manchVgClient, digioVgClient, consentClient, empClient, ocrClient, authClient, vgDigilockerClient, stepsRetryDelayPublisher, commsClient, obfuscatorClient, eventBroker, userCommsDelayPublisher, ncClient, redisClient, rmsClient, awsConf, celestialClient, savingsClient, invProfileClient, vgBankCustomerClient, bankCustomerClient)
	if err != nil {
		return nil, err
	}
	crdbOnboardingStepDetailsDao := impl.NewCrdbOnboardingStepDetailsDao(db)
	onboardingSummaryCrdb := impl.NewOnboardingSummaryCrdb(db)
	lambdaClient := LambdaClientProvider(awsConf)
	cxService := cx.NewService(persistentQueue, crdbOnboardingDetailsDao, service, docHelperImpl, livenessClient, wealthonboardingService, crdbOnboardingStepDetailsDao, userClient, actorClient, invProfileClient, conf, crdbIdempotentTxnExecutor, vgCvlClient, client, onboardingSummaryCrdb, lambdaClient)
	return cxService, nil
}

func InitializeWealthCkycService(db types.EpifiWealthCRDB, ckycClient ckyc.CkycClient, actorClient actor.ActorClient, userClient user.UsersClient, userGroupClient group.GroupClient, conf *config.Config, employmentClient employment.EmploymentClient, awsConf aws.Config, savingsClient savings.SavingsClient, authClient auth.AuthClient, vgBankCustomerClient customer.CustomerClient, bankCustomerClient bankcust.BankCustomerServiceClient) (*ckyc2.Service, error) {
	crdbCkycDataDao := impl.NewCrdbCkycDataDao(db)
	lambdaClient := LambdaClientProvider(awsConf)
	crdbUserDao := impl.NewCrdbUserDao(db)
	service := user2.NewService(crdbUserDao)
	commonHelper := helper.NewCommonHelper(actorClient, userClient, userGroupClient, lambdaClient, conf, employmentClient, service, bankCustomerClient, savingsClient)
	string2 := S3BucketProvider(conf)
	client := s3.NewClient(awsConf, string2)
	httpClient := HttpClientProvider()
	docHelperImpl := helper.NewDocProofHelperImpl(client, httpClient)
	vendorRequestDaoImpl := impl.NewVendorRequestDao(db)
	vendorRequestProducer, err := KinesisProducerProvider(awsConf, conf)
	if err != nil {
		return nil, err
	}
	vendorRequestS3Client := vendorReqS3ClientProvider(awsConf, conf)
	vendorRequestProducerImpl := impl.NewVendorRequestProducer(vendorRequestProducer, vendorRequestS3Client, conf)
	vendorRequestDaoAndProducerImpl := impl.NewVendorRequestDaoAndProducer(vendorRequestDaoImpl, vendorRequestProducerImpl)
	vendorRequestDao := provider.ProvideVendorRequestStorageImpl(vendorRequestDaoImpl, vendorRequestProducerImpl, vendorRequestDaoAndProducerImpl, conf)
	ckycService := ckyc2.NewService(crdbCkycDataDao, ckycClient, commonHelper, docHelperImpl, vendorRequestDao, conf)
	return ckycService, nil
}

func InitializeActivityProcessor(db types.EpifiWealthCRDB, conf *worker.Config, s3Client s3.S3Client, ocrClient inhouseocr.OcrClient, ckycClient ckyc3.CkycClient, awsConf aws.Config, userClient user.UsersClient, actorClient actor.ActorClient, catalogManagerClient catalog.CatalogManagerClient) *activity.Processor {
	crdbOnboardingDetailsDao := impl.NewCrdbOnboardingDetailsDao(db)
	client := HttpClientProvider()
	docHelperImpl := helper.NewDocProofHelperImpl(s3Client, client)
	inhouseOcr := ocr.NewInhouseOcr(ocrClient)
	lambdaClient := LambdaClientProvider(awsConf)
	slackClient := mfAumReconSlackAlertClientProvider(conf)
	gormDB := gormProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	mutualFundCrdb := impl2.NewMutualFundCrdb(gormDB, domainIdGenerator)
	folioLedgerCrdb := impl2.NewFolioLedgerCrdb(gormDB, domainIdGenerator)
	orderCrdb := impl2.NewOrderCrdb(gormDB, domainIdGenerator)
	orderConfirmationInfoCrdb := impl2.NewMFOrderConfirmationInfoCrdb(gormDB)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	defaultTime := datetime.NewDefaultTime()
	activityProcessor := activity.NewProcessor(crdbOnboardingDetailsDao, s3Client, ocrClient, conf, ckycClient, docHelperImpl, inhouseOcr, lambdaClient, userClient, actorClient, slackClient, catalogManagerClient, mutualFundCrdb, folioLedgerCrdb, orderCrdb, orderConfirmationInfoCrdb, crdbIdempotentTxnExecutor, defaultTime)
	return activityProcessor
}

// wire.go:

func gormProvider(db types.EpifiWealthCRDB) *gorm.DB {
	return db
}

func redisProvider(redisClient types2.WealthRedisStore) *redis.Client {
	return redisClient
}

func S3BucketProvider(conf *config.Config) string { return conf.S3Conf.Bucket }

func vendorReqS3ClientProvider(awsConf aws.Config, conf *config.Config) types2.VendorRequestS3Client {
	return s3.NewClient(awsConf, conf.VendorRequestS3Conf.Bucket)
}

func KinesisProducerProvider(awsV2Config aws.Config, conf *config.Config) (types2.VendorRequestProducer, error) {
	kinesisClient := kinesis.NewKinesisClient(&awsV2Config)

	var vendorRequestProducer types2.VendorRequestProducer
	vendorRequestProducer, err := kinesis.NewKinesisProducer(context.Background(), conf.VendorRequestProducer, kinesisClient)
	if err != nil {
		err = fmt.Errorf("failed to initialise producer: %w", err)
	}
	return vendorRequestProducer, err
}

func LambdaClientProvider(awsConf aws.Config) lambda.LambdaClient {
	return lambda.NewAwsLambda(awsConf)
}

func HttpClientProvider() *http.Client {
	return &http.Client{}
}

func WealthS3ClientProvider(client types2.WealthS3Client) s3.S3Client {
	return client
}

func mfAumReconSlackAlertClientProvider(conf *worker.Config) *slack.Client {
	var slackClient *slack.Client
	if cfg.IsProdEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[worker.CaptureMfAumFileSlackBotOauthToken], slack.OptionDebug(false))
	} else if cfg.IsSimulatedEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[worker.CaptureMfAumFileSlackBotOauthToken], slack.OptionDebug(true))
	}
	return slackClient
}
