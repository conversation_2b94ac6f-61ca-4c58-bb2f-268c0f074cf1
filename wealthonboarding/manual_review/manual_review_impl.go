package manualreview

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/timestamppb"

	pqPb "github.com/epifi/gamma/api/persistentqueue"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	pqPkg "github.com/epifi/gamma/pkg/persistentqueue"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/helper"
)

var _ IManualReview = &Service{}

type Service struct {
	dao                   dao.ManualReviewDao
	pq                    pqPkg.PersistentQueue
	idempotentTxnExecutor storagev2.IdempotentTxnExecutor
	docHelper             helper.DocumentHelper
}

func NewService(
	dao dao.ManualReviewDao,
	persistentQueue pqPkg.PersistentQueue,
	idempotentTxnExecutor storagev2.IdempotentTxnExecutor,
	docHelper helper.DocumentHelper) *Service {
	return &Service{
		dao:                   dao,
		pq:                    persistentQueue,
		idempotentTxnExecutor: idempotentTxnExecutor,
		docHelper:             docHelper,
	}
}

func (m *Service) AddToReview(ctx context.Context, reviewPayload *woPb.ReviewPayload, itemType woPb.ItemType) (string, error) {
	if reviewPayload == nil || itemType == woPb.ItemType_ITEM_TYPE_UNSPECIFIED {
		return "", errors.New("invalid parameters")
	}
	createRes, err := m.dao.Create(ctx, &woPb.ManualReview{
		ReviewStatus:  woPb.ReviewStatus_REVIEW_STATUS_PENDING,
		ReviewPayload: reviewPayload,
		ItemType:      itemType,
	})
	if err != nil {
		return "", errors.Wrap(err, "error while creating manual review item")
	}
	return createRes.GetId(), nil
}

func (m *Service) CheckStatus(ctx context.Context, id string) (woPb.ReviewStatus, error) {
	mRes, mErr := m.dao.GetById(ctx, id)
	if mErr != nil {
		return woPb.ReviewStatus_REVIEW_STATUS_UNSPECIFIED, errors.Wrap(mErr, "error while fetching manual review item by id")
	}
	return mRes.GetReviewStatus(), nil
}

func (m *Service) SubmitForReview(ctx context.Context, actorId string, ids []string) error {
	if len(ids) == 0 {
		return errors.New("id list is empty")
	}
	for _, id := range ids {
		if id == "" {
			return errors.New("invalid argument, list contains empty id")
		}
	}
	txnErr := m.idempotentTxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		// change status to IN_PROGRESS
		for _, id := range ids {
			err := m.dao.UpdateById(txnCtx, &woPb.ManualReview{
				Id:           id,
				ReviewStatus: woPb.ReviewStatus_REVIEW_STATUS_IN_PROGRESS,
			}, []woPb.ManualReviewFieldMask{woPb.ManualReviewFieldMask_MANUAL_REVIEW_FIELD_MASK_STATUS})
			if err != nil {
				return errors.Wrap(err, "error while moving manual review item to in_progress")
			}
		}
		// create elements and insert into persistent queue
		err := m.createAndInsertIntoQueue(txnCtx, actorId, ids)
		return err
	})
	return txnErr
}

func (m *Service) UpdateStatus(ctx context.Context, id string, reviewStatus woPb.ReviewStatus) error {
	if id == "" || reviewStatus == woPb.ReviewStatus_REVIEW_STATUS_UNSPECIFIED {
		return errors.New("invalid arguments")
	}
	err := m.dao.UpdateById(ctx, &woPb.ManualReview{
		Id:           id,
		ReviewStatus: reviewStatus,
	}, []woPb.ManualReviewFieldMask{woPb.ManualReviewFieldMask_MANUAL_REVIEW_FIELD_MASK_STATUS})
	if err != nil {
		return errors.Wrap(err, "error while updating manual review item status")
	}
	return nil
}

func (m *Service) createAndInsertIntoQueue(ctx context.Context, actorId string, ids []string) error {
	var wdElement, kycDocketElement *pqPkg.QueueElement
	for _, id := range ids {
		mRes, mErr := m.dao.GetById(ctx, id)
		if mErr != nil {
			return errors.Wrap(mErr, "error while fetching manual review item")
		}
		switch mRes.ItemType {
		case woPb.ItemType_ITEM_TYPE_LIVENESS, woPb.ItemType_ITEM_TYPE_AADHAAR_REDACTION, woPb.ItemType_ITEM_TYPE_DOCUMENT_EXPIRY:
			if wdElement == nil {
				wdElement = &pqPkg.QueueElement{
					ActorID:     actorId,
					PayloadType: pqPb.PayloadType_PAYLOAD_TYPE_WEALTH_DATA,
					Payload: &pqPb.Payload{
						WealthDataReview: &pqPb.WealthDataReview{},
					},
				}
			}
			// add all wealth data items to WealthDataReview
			aErr := m.assignToReviewItem(ctx, wdElement.GetPayload().GetWealthDataReview(), mRes, actorId)
			if aErr != nil {
				return errors.Wrap(aErr, "error while assigning to review item")
			}
		case woPb.ItemType_ITEM_TYPE_KYC_DOCKET:
			kycDocketElement = &pqPkg.QueueElement{
				ActorID:     actorId,
				PayloadType: pqPb.PayloadType_PAYLOAD_TYPE_KYC_DOCKET,
				Payload: &pqPb.Payload{
					KycDocketReview: &pqPb.KycDocketReview{
						// populating only actor id and manual review id since all other data is populated from metadata after fetching from queue
						ActorId: actorId,
						KraFormPayload: &pqPb.KycDocketReview_KycDocketPayload{
							ManualReviewId: id,
						},
					},
				},
			}
		default:
			return errors.New("unhandled review item type")
		}
	}
	// push elements into persistent queue
	if wdElement != nil {
		err := m.pq.InsertElement(ctx, wdElement)
		if err != nil {
			return errors.Wrap(err, "error while inserting review item to persistent queue")
		}
	}
	if kycDocketElement != nil {
		err := m.pq.InsertElement(ctx, kycDocketElement)
		if err != nil {
			return errors.Wrap(err, "error while inserting review item to persistent queue")
		}
	}
	return nil
}

func (m *Service) assignToReviewItem(ctx context.Context, wdReview *pqPb.WealthDataReview, mrItem *woPb.ManualReview, actorId string) error {
	reviewId := mrItem.GetId()
	wdReview.ActorId = actorId
	switch mrItem.GetItemType() {
	case woPb.ItemType_ITEM_TYPE_LIVENESS:
		lvPayload := mrItem.GetReviewPayload().GetLivenessReview()
		// TODO(ismail): check if liveness payload is not nil before assigning?
		wdReview.LivenessPayload = &pqPb.WealthDataReview_LivenessPayload{
			VideoLocation:  lvPayload.GetVideoLocation(),
			Otp:            lvPayload.GetOtp(),
			OtpScore:       lvPayload.GetOtpScore(),
			LivenessScore:  lvPayload.GetLivenessScore(),
			ManualReviewId: reviewId,
			RequestId:      lvPayload.GetAttemptId(),
		}
		return nil
	case woPb.ItemType_ITEM_TYPE_AADHAAR_REDACTION:
		redactPayload := mrItem.GetReviewPayload().GetRedactionReview()
		// TODO(sharath): do dochelper calls async
		rawOriginalDoc, err := m.docHelper.GetRawS3Document(ctx, redactPayload.GetActorId(), redactPayload.GetOriginalDocument())
		if err != nil {
			return errors.Wrap(err, "error while getting raw original doc using helper")
		}
		rawProcessedDoc, err := m.docHelper.GetRawS3Document(ctx, redactPayload.GetActorId(), redactPayload.GetProcessedDocument().GetDoc())
		if err != nil {
			return errors.Wrap(err, "error while getting raw processed doc using helper")
		}
		wdReview.RedactionPayload = &pqPb.WealthDataReview_OcrPayload{
			RawOriginalDocument: rawOriginalDoc,
			RawProcessedDocument: &pqPb.WealthDataReview_OcrDocumentProof{
				DocumentProof:    rawProcessedDoc,
				ConfidenceScore:  redactPayload.GetProcessedDocument().GetConfidenceScore(),
				ThresholdScore:   redactPayload.GetProcessedDocument().GetThresholdScore(),
				VendorReviewFlag: redactPayload.GetProcessedDocument().GetVendorReviewFlag(),
			},
			ManualReviewId: reviewId,
		}
		return nil
	case woPb.ItemType_ITEM_TYPE_DOCUMENT_EXPIRY:
		expiryPayload := mrItem.GetReviewPayload().GetExpiryReview()
		// TODO(sharath): do dochelper calls async
		rawOriginalDoc, err := m.docHelper.GetRawS3Document(ctx, expiryPayload.GetActorId(), expiryPayload.GetOriginalDocument())
		if err != nil {
			return errors.Wrap(err, "error while getting raw original doc using helper")
		}
		rawProcessedDoc, err := m.docHelper.GetRawS3Document(ctx, expiryPayload.GetActorId(), expiryPayload.GetProcessedDocument().GetDoc())
		if err != nil {
			return errors.Wrap(err, "error while getting raw processed doc using helper")
		}
		wdReview.ExpiryPayload = &pqPb.WealthDataReview_OcrPayload{
			RawOriginalDocument: rawOriginalDoc,
			RawProcessedDocument: &pqPb.WealthDataReview_OcrDocumentProof{
				DocumentProof:    rawProcessedDoc,
				ConfidenceScore:  expiryPayload.GetProcessedDocument().GetConfidenceScore(),
				ThresholdScore:   expiryPayload.GetProcessedDocument().GetThresholdScore(),
				VendorReviewFlag: expiryPayload.GetProcessedDocument().GetVendorReviewFlag(),
			},
			ManualReviewId: reviewId,
		}
		return nil
	default:
		return errors.New(fmt.Sprintf("could not determine item type: %v", mrItem.GetItemType()))
	}
}

func (m *Service) GetReviewItem(ctx context.Context, id string) (*woPb.ManualReview, error) {
	mRes, mErr := m.dao.GetById(ctx, id)
	if mErr != nil {
		return nil, errors.Wrap(mErr, "error while fetching manual review item by id")
	}
	return mRes, nil
}

func (m *Service) MarkReviewCompleted(ctx context.Context, id string, reviewStatus woPb.ReviewStatus, reviewPayload *woPb.ReviewPayload, reviewedAt *timestamppb.Timestamp) error {
	if reviewStatus == woPb.ReviewStatus_REVIEW_STATUS_UNSPECIFIED || reviewedAt == nil || reviewPayload == nil {
		return errors.New(fmt.Sprintf("invalid argument, review_status: %v, reviewed_at: %v, review_payload: %v",
			reviewStatus, reviewedAt, reviewPayload))
	}
	// check if the status is in terminal state
	if !isTerminalStatus(reviewStatus) {
		return errors.New(fmt.Sprintf("invalid review status, status: %v", reviewStatus))
	}

	mRes := &woPb.ManualReview{
		Id:            id,
		ReviewStatus:  reviewStatus,
		ReviewPayload: reviewPayload,
		ReviewedAt:    reviewedAt,
	}
	// update values in db
	updErr := m.dao.UpdateById(ctx, mRes, []woPb.ManualReviewFieldMask{
		woPb.ManualReviewFieldMask_MANUAL_REVIEW_FIELD_MASK_STATUS,
		woPb.ManualReviewFieldMask_MANUAL_REVIEW_FIELD_MASK_PAYLOAD,
		woPb.ManualReviewFieldMask_MANUAL_REVIEW_FIELD_MASK_REVIEWED_AT,
	})
	if updErr != nil {
		return errors.Wrap(updErr, "error while updating manual review item")
	}
	return nil
}

func isTerminalStatus(status woPb.ReviewStatus) bool {
	return status == woPb.ReviewStatus_REVIEW_STATUS_APPROVED || status == woPb.ReviewStatus_REVIEW_STATUS_REJECTED || status == woPb.ReviewStatus_REVIEW_STATUS_EDITED
}
