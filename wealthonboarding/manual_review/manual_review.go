package manualreview

import (
	"context"

	"google.golang.org/protobuf/types/known/timestamppb"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
)

type IManualReview interface {
	// AddToReview pushes an item for manual review, it returns a unique identifier which can be used for polling the status of the review item
	AddToReview(ctx context.Context, reviewPayload *woPb.ReviewPayload, itemType woPb.ItemType) (string, error)
	// CheckStatus returns the current status of the item
	CheckStatus(ctx context.Context, id string) (woPb.ReviewStatus, error)
	// SubmitForReview takes a list of ids of review items, and then it pushes them to persistent queue
	// This queue would be then be used for reviewing items at the sherlock end
	SubmitForReview(ctx context.Context, actorId string, ids []string) error
	// UpdateStatus is for updating status of a manual review
	UpdateStatus(ctx context.Context, id string, reviewStatus woPb.ReviewStatus) error
	// GetReviewItem is to get manual review item corresponding to the given id
	GetReviewItem(ctx context.Context, id string) (*woPb.ManualReview, error)
	// MarkReviewCompleted helps in marking the review of an item as completed
	// It currently updates the review_status, payload and reviewed_at fields
	MarkReviewCompleted(ctx context.Context, id string, reviewStatus woPb.ReviewStatus, reviewPayload *woPb.ReviewPayload, reviewedAt *timestamppb.Timestamp) error
}
