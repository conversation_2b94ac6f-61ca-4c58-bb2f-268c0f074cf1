package manualreview

import (
	"context"
	"errors"
	"flag"
	"os"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"

	pqPb "github.com/epifi/gamma/api/persistentqueue"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/epifierrors"
	pqPkg "github.com/epifi/gamma/pkg/persistentqueue"
	mockPq "github.com/epifi/gamma/pkg/persistentqueue/mocks"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/test"
	mockDao "github.com/epifi/gamma/wealthonboarding/test/mocks/dao"
)

var idempotentTxnExecutor storagev2.IdempotentTxnExecutor

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, _, _, db, teardown := test.InitTestServer()
	idempotentTxnExecutor = storagev2.NewCRDBIdempotentTxnExecutor(db)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestService_AddToReview(t *testing.T) {
	ctr := gomock.NewController(t)
	reviewDao := mockDao.NewMockManualReviewDao(ctr)

	type fields struct {
		mDao dao.ManualReviewDao
	}

	type args struct {
		ctx           context.Context
		mocks         []interface{}
		reviewPayload *woPb.ReviewPayload
		itemType      woPb.ItemType
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
		err     error
	}{
		{
			name: "successfully added to review",
			fields: fields{
				mDao: reviewDao,
			},
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					reviewDao.EXPECT().Create(gomock.Any(), &woPb.ManualReview{
						ItemType:     woPb.ItemType_ITEM_TYPE_LIVENESS,
						ReviewStatus: woPb.ReviewStatus_REVIEW_STATUS_PENDING,
						ReviewPayload: &woPb.ReviewPayload{
							Payload: &woPb.ReviewPayload_LivenessReview_{
								LivenessReview: &woPb.ReviewPayload_LivenessReview{
									VideoLocation: "v1",
									AttemptId:     "a1",
									Otp:           "123456",
									OtpScore:      100,
									LivenessScore: 100,
								},
							}},
					}).
						Return(&woPb.ManualReview{Id: "random_id"}, nil).AnyTimes(),
				},
				reviewPayload: &woPb.ReviewPayload{Payload: &woPb.ReviewPayload_LivenessReview_{
					LivenessReview: &woPb.ReviewPayload_LivenessReview{
						VideoLocation: "v1",
						AttemptId:     "a1",
						Otp:           "123456",
						OtpScore:      100,
						LivenessScore: 100,
					},
				}},
				itemType: woPb.ItemType_ITEM_TYPE_LIVENESS,
			},
			want:    "random_id",
			wantErr: false,
			err:     nil,
		},
		{
			name: "fail to added to review",
			fields: fields{
				mDao: reviewDao,
			},
			args: args{
				ctx:           context.Background(),
				mocks:         []interface{}{},
				reviewPayload: nil,
				itemType:      woPb.ItemType_ITEM_TYPE_UNSPECIFIED,
			},
			want:    "",
			wantErr: true,
			err:     errors.New("invalid parameters"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Service{
				dao: reviewDao,
			}
			got, err := c.AddToReview(tt.args.ctx, tt.args.reviewPayload, tt.args.itemType)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddToReview() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if err == nil {
				assert.NotNil(t, got)
				if got != tt.want {
					t.Errorf("Error AddToReview() got = %v, want %v", got, tt.want)
					return
				}
			}
		})
	}
}

func TestService_CheckStatus(t *testing.T) {
	ctr := gomock.NewController(t)
	reviewDao := mockDao.NewMockManualReviewDao(ctr)

	type fields struct {
		mDao dao.ManualReviewDao
	}

	type args struct {
		ctx            context.Context
		mocks          []interface{}
		manualReviewId string
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    woPb.ReviewStatus
		wantErr bool
	}{
		{
			name: "successful check status",
			fields: fields{
				mDao: reviewDao,
			},
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					reviewDao.EXPECT().GetById(gomock.Any(), "random_id").
						Return(&woPb.ManualReview{Id: "random_id", ReviewStatus: woPb.ReviewStatus_REVIEW_STATUS_IN_PROGRESS}, nil).AnyTimes(),
				},
				manualReviewId: "random_id",
			},
			want:    woPb.ReviewStatus_REVIEW_STATUS_IN_PROGRESS,
			wantErr: false,
		},
		{
			name: "record not found for manual review id",
			fields: fields{
				mDao: reviewDao,
			},
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					reviewDao.EXPECT().GetById(gomock.Any(), "random_id_2").
						Return(nil, epifierrors.ErrRecordNotFound).AnyTimes(),
				},
				manualReviewId: "random_id_2",
			},
			want:    woPb.ReviewStatus_REVIEW_STATUS_UNSPECIFIED,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Service{
				dao: reviewDao,
			}
			got, err := c.CheckStatus(tt.args.ctx, tt.args.manualReviewId)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
				if got != tt.want {
					t.Errorf("Error CheckStatus() got = %v, want %v", got, tt.want)
					return
				}
			}
		})
	}
}

func TestService_SubmitForReview(t *testing.T) {
	ctr := gomock.NewController(t)
	reviewDao := mockDao.NewMockManualReviewDao(ctr)
	pq := mockPq.NewMockPersistentQueue(ctr)

	type fields struct {
		mDao dao.ManualReviewDao
		pq   pqPkg.PersistentQueue
	}

	type args struct {
		ctx             context.Context
		mocks           []interface{}
		manualReviewIds []string
		actorId         string
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "successful check status",
			fields: fields{
				mDao: reviewDao,
				pq:   pq,
			},
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					reviewDao.EXPECT().GetById(gomock.Any(), "random_id").
						Return(&woPb.ManualReview{
							Id:           "random_id",
							ItemType:     woPb.ItemType_ITEM_TYPE_LIVENESS,
							ReviewStatus: woPb.ReviewStatus_REVIEW_STATUS_IN_PROGRESS,
							ReviewPayload: &woPb.ReviewPayload{Payload: &woPb.ReviewPayload_LivenessReview_{LivenessReview: &woPb.ReviewPayload_LivenessReview{
								ActorId:       "random_actor_id",
								VideoLocation: "random_video_location",
								AttemptId:     "random_attempt_id",
								Otp:           "123456",
								OtpScore:      100,
								LivenessScore: 100,
							}}}}, nil).AnyTimes(),
					reviewDao.EXPECT().UpdateById(gomock.Any(), &woPb.ManualReview{
						Id:           "random_id",
						ReviewStatus: woPb.ReviewStatus_REVIEW_STATUS_IN_PROGRESS,
					}, []woPb.ManualReviewFieldMask{woPb.ManualReviewFieldMask_MANUAL_REVIEW_FIELD_MASK_STATUS}).
						Return(nil).AnyTimes(),
					pq.EXPECT().InsertElement(gomock.Any(), &pqPkg.QueueElement{
						ActorID:     "random_actor_id",
						PayloadType: pqPb.PayloadType_PAYLOAD_TYPE_WEALTH_DATA,
						Payload: &pqPb.Payload{WealthDataReview: &pqPb.WealthDataReview{
							ActorId: "random_actor_id",
							LivenessPayload: &pqPb.WealthDataReview_LivenessPayload{
								VideoLocation:  "random_video_location",
								RequestId:      "random_attempt_id",
								Otp:            "123456",
								OtpScore:       100,
								LivenessScore:  100,
								ManualReviewId: "random_id",
							}}},
					}),
				},
				manualReviewIds: []string{"random_id"},
				actorId:         "random_actor_id",
			},
			wantErr: false,
		},
		{
			name: "id list empty",
			fields: fields{
				mDao: reviewDao,
				pq:   pq,
			},
			args: args{
				ctx:             context.Background(),
				mocks:           []interface{}{},
				actorId:         "random_actor_id",
				manualReviewIds: []string{},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Service{
				dao:                   tt.fields.mDao,
				pq:                    tt.fields.pq,
				idempotentTxnExecutor: idempotentTxnExecutor,
			}
			err := c.SubmitForReview(tt.args.ctx, tt.args.actorId, tt.args.manualReviewIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("SubmitForReview() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestService_UpdateStatus(t *testing.T) {
	ctr := gomock.NewController(t)
	reviewDao := mockDao.NewMockManualReviewDao(ctr)

	type fields struct {
		mDao dao.ManualReviewDao
	}

	type args struct {
		ctx            context.Context
		mocks          []interface{}
		manualReviewId string
		reviewStatus   woPb.ReviewStatus
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "successful update status",
			fields: fields{
				mDao: reviewDao,
			},
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					reviewDao.EXPECT().UpdateById(gomock.Any(), &woPb.ManualReview{Id: "random_id", ReviewStatus: woPb.ReviewStatus_REVIEW_STATUS_APPROVED},
						[]woPb.ManualReviewFieldMask{woPb.ManualReviewFieldMask_MANUAL_REVIEW_FIELD_MASK_STATUS}).
						Return(nil).AnyTimes(),
				},
				manualReviewId: "random_id",
				reviewStatus:   woPb.ReviewStatus_REVIEW_STATUS_APPROVED,
			},
			wantErr: false,
		},
		{
			name: "failure to update, invalid review status",
			fields: fields{
				mDao: reviewDao,
			},
			args: args{
				ctx:            context.Background(),
				mocks:          []interface{}{},
				manualReviewId: "random_id",
				reviewStatus:   woPb.ReviewStatus_REVIEW_STATUS_UNSPECIFIED,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Service{
				dao: reviewDao,
			}
			err := c.UpdateStatus(tt.args.ctx, tt.args.manualReviewId, tt.args.reviewStatus)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestService_GetReviewItem(t *testing.T) {
	ctr := gomock.NewController(t)
	reviewDao := mockDao.NewMockManualReviewDao(ctr)

	type fields struct {
		mDao dao.ManualReviewDao
	}

	type args struct {
		ctx            context.Context
		mocks          []interface{}
		manualReviewId string
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *woPb.ManualReview
		wantErr bool
	}{
		{
			name: "successful case",
			fields: fields{
				mDao: reviewDao,
			},
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					reviewDao.EXPECT().GetById(gomock.Any(), "random_id").
						Return(&woPb.ManualReview{Id: "random_id", ReviewStatus: woPb.ReviewStatus_REVIEW_STATUS_IN_PROGRESS}, nil).AnyTimes(),
				},
				manualReviewId: "random_id",
			},
			want:    &woPb.ManualReview{Id: "random_id", ReviewStatus: woPb.ReviewStatus_REVIEW_STATUS_IN_PROGRESS},
			wantErr: false,
		},
		{
			name: "record not found for manual review id",
			fields: fields{
				mDao: reviewDao,
			},
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					reviewDao.EXPECT().GetById(gomock.Any(), "random_id_2").
						Return(nil, epifierrors.ErrRecordNotFound).AnyTimes(),
				},
				manualReviewId: "random_id_2",
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Service{
				dao: reviewDao,
			}
			got, err := c.GetReviewItem(tt.args.ctx, tt.args.manualReviewId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetReviewItem() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Error GetReviewItem() got = %v, want %v", got, tt.want)
				return
			}
		})
	}
}

func Test_MarkReviewCompleted(t *testing.T) {
	ctr := gomock.NewController(t)
	reviewDao := mockDao.NewMockManualReviewDao(ctr)

	type fields struct {
		mDao dao.ManualReviewDao
	}

	type args struct {
		ctx            context.Context
		mocks          []interface{}
		manualReviewId string
		reviewStatus   woPb.ReviewStatus
		reviewPayload  *woPb.ReviewPayload
		reviewedAt     *timestamppb.Timestamp
	}

	rAt := timestamppb.Now()
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "successfully marked review as completed",
			fields: fields{
				mDao: reviewDao,
			},
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					reviewDao.EXPECT().UpdateById(gomock.Any(), &woPb.ManualReview{
						Id:            "random_id",
						ReviewStatus:  woPb.ReviewStatus_REVIEW_STATUS_APPROVED,
						ReviewPayload: &woPb.ReviewPayload{},
						ReviewedAt:    rAt,
					},
						[]woPb.ManualReviewFieldMask{
							woPb.ManualReviewFieldMask_MANUAL_REVIEW_FIELD_MASK_STATUS,
							woPb.ManualReviewFieldMask_MANUAL_REVIEW_FIELD_MASK_PAYLOAD,
							woPb.ManualReviewFieldMask_MANUAL_REVIEW_FIELD_MASK_REVIEWED_AT,
						}).
						Return(nil).AnyTimes(),
				},
				manualReviewId: "random_id",
				reviewStatus:   woPb.ReviewStatus_REVIEW_STATUS_APPROVED,
				reviewPayload:  &woPb.ReviewPayload{},
				reviewedAt:     rAt,
			},
			wantErr: false,
		},
		{
			name: "failed to marked review as completed",
			fields: fields{
				mDao: reviewDao,
			},
			args: args{
				ctx:            context.Background(),
				mocks:          []interface{}{},
				manualReviewId: "random_id",
				reviewStatus:   woPb.ReviewStatus_REVIEW_STATUS_APPROVED,
				reviewPayload:  nil,
				reviewedAt:     rAt,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Service{
				dao: reviewDao,
			}
			err := c.MarkReviewCompleted(tt.args.ctx, tt.args.manualReviewId, tt.args.reviewStatus, tt.args.reviewPayload, tt.args.reviewedAt)
			if (err != nil) != tt.wantErr {
				t.Errorf("MarkReviewCompleted() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
