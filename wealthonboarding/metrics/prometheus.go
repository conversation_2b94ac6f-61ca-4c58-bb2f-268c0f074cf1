package metrics

import (
	"time"

	woPb "github.com/epifi/gamma/api/wealthonboarding"

	"github.com/prometheus/client_golang/prometheus"
)

type woMetrics struct {
	// counter to measure wealth onboarding step status by step, status and sub-status
	stepStatus         *prometheus.CounterVec
	onboardingStatus   *prometheus.CounterVec
	invalidIp          *prometheus.CounterVec
	stepCompletionTime *prometheus.HistogramVec
	onboardingTime     *prometheus.HistogramVec
}

var woMetricsRecorder = initialiseWoMetrics()

func initialiseWoMetrics() *woMetrics {
	wom := &woMetrics{
		stepStatus: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "wo_step_status",
				Help: "wealth onboarding step status by step, status and sub-status",
			},
			[]string{"step", "status", "sub_status"},
		),
		onboardingStatus: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "wo_status",
				Help: "wealth onboarding status",
			},
			[]string{"status"},
		),
		invalidIp: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "wo_invalid_customer_ip",
				Help: "wealth onboarding invalid customer ip",
			},
			[]string{},
		),
		stepCompletionTime: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "step_completion_time_in_seconds",
				Help:    "time taken to complete a step",
				Buckets: prometheus.ExponentialBuckets(1, 5, 10),
			},
			[]string{"step"},
		),
		onboardingTime: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "onboarding_time_in_minutes",
				Help:    "time taken to onboard",
				Buckets: prometheus.ExponentialBuckets(1, 5, 10),
			},
			[]string{},
		),
	}
	// register all metrics
	register(wom)
	// initialize to 0
	initialise(wom)
	return wom
}

func register(wom *woMetrics) {
	prometheus.MustRegister(wom.stepStatus)
	prometheus.MustRegister(wom.onboardingStatus)
	prometheus.MustRegister(wom.invalidIp)
}

// initialise metrics to 0
func initialise(wom *woMetrics) {
	// initializing will all possible step, status and sub-status combinations
	for k1 := range woPb.OnboardingStep_value {
		for k2 := range woPb.OnboardingStepStatus_value {
			for k3 := range woPb.OnboardingStepSubStatus_value {
				wom.stepStatus.WithLabelValues(k1, k2, k3)
			}
		}
	}
	// init with all onboarding status
	for k1 := range woPb.OnboardingStatus_value {
		wom.onboardingStatus.WithLabelValues(k1)
	}
	// init with all onboarding steps
	for k1 := range woPb.OnboardingStep_value {
		wom.stepCompletionTime.WithLabelValues(k1)
	}
	// init onboarding time
	wom.onboardingTime.WithLabelValues()
	wom.invalidIp.WithLabelValues()
}

func RecordStepStatus(step woPb.OnboardingStep, status woPb.OnboardingStepStatus, subStatus woPb.OnboardingStepSubStatus) {
	woMetricsRecorder.stepStatus.WithLabelValues(step.String(), status.String(), subStatus.String()).Inc()
}

func RecordOnboardingStatus(status woPb.OnboardingStatus) {
	woMetricsRecorder.onboardingStatus.WithLabelValues(status.String()).Inc()
}

func RecordStepCompletionTime(step woPb.OnboardingStep, duration time.Duration) {
	woMetricsRecorder.stepCompletionTime.WithLabelValues(step.String()).Observe(duration.Seconds())
}

func RecordOnboardingTime(duration time.Duration) {
	woMetricsRecorder.onboardingTime.WithLabelValues().Observe(duration.Minutes())
}

func RecordInvalidIp() {
	woMetricsRecorder.invalidIp.WithLabelValues().Inc()
}
