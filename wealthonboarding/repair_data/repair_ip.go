package repair_data

import (
	"github.com/pkg/errors"
	"golang.org/x/net/context"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/helper"
)

type RepairIP struct {
	onboardingDetailsDao dao.OnboardingDetailsDao
}

func NewRepairIP(onboardingDetailsDao dao.OnboardingDetailsDao) *RepairIP {
	return &RepairIP{onboardingDetailsDao: onboardingDetailsDao}
}

// Repair method updates the ip address of a user
func (r *RepairIP) Repair(ctx context.Context, od *woPb.OnboardingDetails) (*woPb.OnboardingDetails, error) {
	// TODO(ismail): adding more checks here based on private ip of intermediate servers, they usually have a prefix of 10.*
	if od.GetMetadata().GetCustomerIpAddress() != "127.0.0.1" && od.GetMetadata().GetCustomerIpAddress() != "" {
		logger.Info(ctx, "not repairing ip details since they are already present")
		return od, nil
	}
	od.GetMetadata().CustomerIpAddress = helper.ParseUsersIp(epificontext.XFwdForAddrFromContext(ctx))
	uErr := r.onboardingDetailsDao.Update(ctx, od, []woPb.OnboardingDetailsFieldMask{woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_METADATA})
	if uErr != nil {
		return nil, errors.Wrap(uErr, "error while updating od in db")
	}
	return od, nil
}
