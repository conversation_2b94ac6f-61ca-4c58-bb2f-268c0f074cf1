package repair_data

import (
	"github.com/pkg/errors"
	"golang.org/x/net/context"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/helper"
)

type RepairBankDetails struct {
	commonHelper         *helper.CommonHelper
	onboardingDetailsDao dao.OnboardingDetailsDao
}

func NewRepairBankDetails(commonHelper *helper.CommonHelper, onboardingDetailsDao dao.OnboardingDetailsDao) *RepairBankDetails {
	return &RepairBankDetails{commonHelper: commonHelper, onboardingDetailsDao: onboardingDetailsDao}
}

// Repair method updates the bank details of a user
func (r *RepairBankDetails) Repair(ctx context.Context, od *woPb.OnboardingDetails) (*woPb.OnboardingDetails, error) {
	if od.GetMetadata().GetBankDetails().GetAccountNumber() != "" {
		logger.Info(ctx, "not repairing bank details since they are already present")
		return od, nil
	}
	res, err := r.commonHelper.FetchBankAccountDetails(ctx, od.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching bank details during repair")
	}
	od.GetMetadata().BankDetails = res
	uErr := r.onboardingDetailsDao.Update(ctx, od, []woPb.OnboardingDetailsFieldMask{woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_METADATA})
	if uErr != nil {
		return nil, errors.Wrap(uErr, "error while updating od in db")
	}
	return od, nil
}
