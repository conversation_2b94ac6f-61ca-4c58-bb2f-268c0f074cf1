package repair_data

import (
	"golang.org/x/net/context"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
)

// RepairData interface provides with a method for repairing onboarding details of a user
type RepairData interface {
	Repair(ctx context.Context, od *woPb.OnboardingDetails) (*woPb.OnboardingDetails, error)
}

type RepairProvider struct {
	repairHandlers []RepairData
}

func NewRepairProvider(repairIp *RepairIP, repairBankDetails *RepairBankDetails) *RepairProvider {
	var repairProviders []RepairData
	repairProviders = append(repairProviders, repairIp, repairBankDetails)
	return &RepairProvider{
		repairHandlers: repairProviders,
	}
}

func (r *RepairProvider) GetRepairHandlers() []RepairData {
	return r.repairHandlers
}
