// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"fmt"
	"reflect"
	"strings"

	questtypes "github.com/epifi/be-common/api/quest/types"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	v "github.com/epifi/be-common/pkg/cfg/v2"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	worker "github.com/epifi/gamma/wealthonboarding/config/worker"
)

type Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Application                   *gencfg.TemporalWorkerApplication
	_Server                        *cfg.ServerPorts
	_WorkflowParamsList            v.WorkflowParamsList
	_DefaultActivityParamsList     v.ActivityParamsList
	_PausedWorkflowList            v.PausedWorkflowsList
	_DbConfigMap                   cfg.DbConfigMap
	_UsecaseDbConfigMap            cfg.UseCaseDbConfigMap
	_AWS                           *cfg.AWS
	_WorkflowUpdatePublisher       *cfg.SnsPublisher
	_Tracing                       *cfg.Tracing
	_Secrets                       *cfg.Secrets
	_Flags                         *worker.Flags
	_SignalWorkflowPublisher       *cfg.SqsPublisher
	_S3Conf                        *worker.S3Conf
	_OCRThresholdScoreConfig       map[string]float64
	_MaxLayersDeepUnzippingAllowed int
}

func (obj *Config) Application() *gencfg.TemporalWorkerApplication {
	return obj._Application
}
func (obj *Config) Server() *cfg.ServerPorts {
	return obj._Server
}
func (obj *Config) WorkflowParamsList() v.WorkflowParamsList {
	return obj._WorkflowParamsList
}
func (obj *Config) DefaultActivityParamsList() v.ActivityParamsList {
	return obj._DefaultActivityParamsList
}
func (obj *Config) PausedWorkflowList() v.PausedWorkflowsList {
	return obj._PausedWorkflowList
}
func (obj *Config) DbConfigMap() cfg.DbConfigMap {
	return obj._DbConfigMap
}
func (obj *Config) UsecaseDbConfigMap() cfg.UseCaseDbConfigMap {
	return obj._UsecaseDbConfigMap
}
func (obj *Config) AWS() *cfg.AWS {
	return obj._AWS
}
func (obj *Config) WorkflowUpdatePublisher() *cfg.SnsPublisher {
	return obj._WorkflowUpdatePublisher
}
func (obj *Config) Tracing() *cfg.Tracing {
	return obj._Tracing
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}
func (obj *Config) Flags() *worker.Flags {
	return obj._Flags
}
func (obj *Config) SignalWorkflowPublisher() *cfg.SqsPublisher {
	return obj._SignalWorkflowPublisher
}
func (obj *Config) S3Conf() *worker.S3Conf {
	return obj._S3Conf
}
func (obj *Config) OCRThresholdScoreConfig() map[string]float64 {
	return obj._OCRThresholdScoreConfig
}
func (obj *Config) MaxLayersDeepUnzippingAllowed() int {
	return obj._MaxLayersDeepUnzippingAllowed
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_Application, _fieldSetters := gencfg.NewTemporalWorkerApplication()
	_obj._Application = _Application
	helper.AddFieldSetters("application", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_Application, _fieldSetters := gencfg.NewTemporalWorkerApplication()
	_obj._Application = _Application
	helper.AddFieldSetters("application", _fieldSetters, _setters)

	return _obj, _setters
}

func (obj *Config) Init() {
	newObj, _ := NewConfig()
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*worker.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *worker.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "application":
		return obj._Application.Set(v.Application, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *worker.Config, dynamic bool, path []string) (err error) {

	err = obj._Application.Set(v.Application, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *worker.Config) error {

	obj._Server = v.Server
	obj._WorkflowParamsList = v.WorkflowParamsList
	obj._DefaultActivityParamsList = v.DefaultActivityParamsList
	obj._PausedWorkflowList = v.PausedWorkflowList
	obj._DbConfigMap = v.DbConfigMap
	obj._UsecaseDbConfigMap = v.UsecaseDbConfigMap
	obj._AWS = v.AWS
	obj._WorkflowUpdatePublisher = v.WorkflowUpdatePublisher
	obj._Tracing = v.Tracing
	obj._Secrets = v.Secrets
	obj._Flags = v.Flags
	obj._SignalWorkflowPublisher = v.SignalWorkflowPublisher
	obj._S3Conf = v.S3Conf
	obj._OCRThresholdScoreConfig = v.OCRThresholdScoreConfig
	obj._MaxLayersDeepUnzippingAllowed = v.MaxLayersDeepUnzippingAllowed
	return nil
}
