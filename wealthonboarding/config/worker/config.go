package worker

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"

	"github.com/knadh/koanf"

	"github.com/epifi/be-common/pkg/cfg"
	cfgV2 "github.com/epifi/be-common/pkg/cfg/v2"
	"github.com/epifi/be-common/pkg/epifitemporal"
)

var (
	_, b, _, _ = runtime.Caller(0)
	once       sync.Once
	config     *Config
	err        error
)

const CaptureMfAumFileSlackBotOauthToken = "CaptureMfAumFileSlackBotOauthToken"

//go:generate conf_gen github.com/epifi/gamma/wealthonboarding/config/worker Config
type Config struct {
	Server                        *cfg.ServerPorts
	Application                   *cfg.TemporalWorkerApplication `dynamic:"true"`
	WorkflowParamsList            cfgV2.WorkflowParamsList
	DefaultActivityParamsList     cfgV2.ActivityParamsList
	PausedWorkflowList            cfgV2.PausedWorkflowsList
	DbConfigMap                   cfg.DbConfigMap
	UsecaseDbConfigMap            cfg.UseCaseDbConfigMap
	AWS                           *cfg.AWS
	WorkflowUpdatePublisher       *cfg.SnsPublisher
	Tracing                       *cfg.Tracing
	Secrets                       *cfg.Secrets
	Flags                         *Flags
	SignalWorkflowPublisher       *cfg.SqsPublisher
	S3Conf                        *S3Conf
	OCRThresholdScoreConfig       map[string]float64
	MaxLayersDeepUnzippingAllowed int // MaxLayerDeepUnzippingAllowed+1 layer deep unzipping of zip will be allowed
}

type Application struct {
	Environment string
	Name        string
}

type Server struct {
	Port            int
	HealthCheckPort int
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool
}

func loadConfig() (*Config, error) {
	var (
		koanf *koanf.Koanf
	)

	conf := &Config{}

	koanf, _, err = cfg.LoadWorkerConfigUsingKoanf(testEnvConfigDir(epifitemporal.WealthOnboardingWorker), "", cfg.ServiceName(epifitemporal.WealthOnboardingWorker))
	if err != nil {
		return nil, err
	}

	if err = koanf.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf)); err != nil {
		return nil, err
	}

	dbOwnershipMap := conf.DbConfigMap.GetOwnershipToDbConfigMap()
	if err = updateDefaultConfig(conf, dbOwnershipMap); err != nil {
		return nil, fmt.Errorf("failed to update default values in the config: %w", err)
	}

	if err = readAndSetEnv(conf, dbOwnershipMap); err != nil {
		return nil, fmt.Errorf("failed to read and set env var: %w", err)
	}

	return conf, nil
}

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, nil
}

func testEnvConfigDir(worker epifitemporal.Worker) string {
	configPath := filepath.Join(b, "..", "..", "..", "..", "cmd", "worker", worker.GetDirectory(), "config")
	return configPath
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, dbOwnershipMap map[commontypes.Ownership]*cfg.DB) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	cfg.UpdateDbEndpointsInConfigMap(dbOwnershipMap, dbServerEndpoint)
	if _, err := cfg.LoadAndUpdateSecretValues(dbOwnershipMap, c.Secrets, c.Application.Environment, c.AWS.Region); err != nil {
		return fmt.Errorf("failed to load and update secret values %w", err)
	}
	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config, dbOwnershipMap map[commontypes.Ownership]*cfg.DB) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Namespace = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.HttpPort = intVal
	}

	if val, ok := os.LookupEnv("DB_HOST"); ok {
		cfg.OverwriteDbHost(dbOwnershipMap, val)
	}

	return nil
}

type S3Conf struct {
	Bucket string
}
