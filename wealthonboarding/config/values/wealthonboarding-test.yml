Application:
  Environment: "test"
  Name: "wealthonboarding"

Server:
  Ports:
    GrpcPort: 8089
    GrpcSecurePort: 9507
    HttpPort: 9999

VendorRequestProducer:
  StreamName: "wealthonboarding-vendor-request-publish-stream"

AWS:
  Region: "ap-south-1"

EpifiDb:
  AppName: "wealthonboarding"
  StatementTimeout: 5m
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "epifi_wealth_test"
  EnableDebug: false
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 0

Flags:
  TrimDebugMessageFromStatus: false
  VendorRequestStore: "kinesis"   # options are "kinesis" "crdb" "crdb_and_kinesis"

Secrets:
  Ids:
    RudderWriteKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    GoogleCloudProfilingServiceAccountKey: "development"
    OfficerSignature: "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"

RefreshFaceMatchStatusSqsPublisher:
  QueueName: "wo-refresh-fm-status-delay-queue"

RefreshFaceMatchStatusSqsSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "wo-refresh-fm-status-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

FetchFMStatusInitialDelayDuration: 60s

UpdateTxnStateDelayDuration: 5s

InitWealthOnboardingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "wo-onboarding-stage-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 4
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 15
        Period: 1m
    Namespace: "connectedaccount"


MinNameMatchScore: 1

CvlKra:
  UploadPath: "/data"
  PdfExpiryTime: 86400
  VerificationTimeInHours: 0

S3Conf:
  Bucket: "epifi-wealth-onboarding"
  LivenessPath: "liveness"
  DownloadedKraDocS3Path: "downloaded_kra_doc"

VendorRequestS3Conf:
  Bucket: "epifi-wealthonboarding-vendor-request"

Digio:
  BaseSignPath: "https://ext.digio.in/#/gateway/login"

LivenessConfig:
  OTPThreshold: 50
  LivenessThreshold: 50
  LivenessStalenessDuration: 72h

KraDocketSignConfig:
  OfficerName: "Jolly Joseph"
  EmployeeCode: "EPIFI_007"
  OfficerDesignation: "ASST MANAGER"
  IntermediaryCode: "IC_007"
  CallBackUrl: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"

AgreementPdf:
  RelativePath: "data/sample-agreement.pdf"

OCRThresholdScoreConfig:
    DOCUMENT_PROOF_TYPE_PAN: 40.0
    DOCUMENT_PROOF_TYPE_DRIVING_LICENSE: 100.0
    DOCUMENT_PROOF_TYPE_PASSPORT: 100.0
    DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR: 100.0
    DOCUMENT_PROOF_TYPE_OFFLINE_AADHAAR_VERIFICATION: 100.0

DaysToExpiry: 180

WealthOnboardingStepsRetrySqsCustomDelayPublisher:
  DestQueueName: "wo-steps-retry-delay-queue"
  OrchestratorSqsPublisher:
    QueueName: "custom-delay-orchestrator-queue"

WealthOnboardingStepsRetryDelaySqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "wo-steps-retry-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"

StepsRetryStrategy:
  "ONBOARDING_STEP_DOWNLOAD_KRA_DOC":
    "STEP_STATUS_IN_PROGRESS":
      "STEP_SUB_STATUS_KRA_DOCKET_DOWNLOAD_NOT_READY_YET":
        RegularInterval:
          Interval: 30
          MaxAttempts: 10
          TimeUnit: "Minute"
  "ALLSTEPS":
    "STEP_STATUS_TRANSIENT_FAILURE":
      "ALLSUBSTATUS":
        RegularInterval:
          Interval: 30
          MaxAttempts: 24
          TimeUnit: "Minute"
    "STEP_STATUS_IN_PROGRESS":
      "STEP_SUB_STATUS_KYC_NOT_VALIDATED_YET":
        RegularInterval:
          Interval: 1
          MaxAttempts: 3
          TimeUnit: "Minute"
      "STEP_SUB_STATUS_VENDOR_DOWNTIME":
        RegularInterval:
          Interval: 1
          MaxAttempts: 3
          TimeUnit: "Minute"

FeatureControlledReleaseConfig:
  WEALTH_ONBOARDING_FEATURE_NSDL_PAN_INQUIRY_WITH_NAME:
    StickyPercentageConstraintConfig:
      RolloutPercentage: 0
    AppVersionConstraintConfig:
      MinAndroidVersion: 10000
      MinIOSVersion: 10000
  WEALTH_ONBOARDING_FEATURE_RISK_PROFILING:
    AppVersionConstraintConfig:
      MinAndroidVersion: 10000
      MinIOSVersion: 10000
  WEALTH_ONBOARDING_FEATURE_MANDATORY_LIVENESS:
    AppVersionConstraintConfig:
      MinAndroidVersion: 1000
      MinIOSVersion: 1000

NotifyMeFix:
  MaxAndroidVersion: 9999
  MaxIOSVersion: 20

DigilockerConfig:
  ClientId: ""
  LoginUrl: "%vhttps://epifi-redirection.s3.ap-south-1.amazonaws.com/redirected-page.html?redirectURL=%v?code=dummyCode&redirectLatency=3000&redirectParamKey=redirectURL&contextText=Dummy_digilocker_page"
  CallbackUrl: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"

KraDowntime:
  DowntimeFrom: "2022-03-11 17:05:00.000"
  DowntimeTo: "2022-03-11 17:10:00.000"

LambdaFunctions:
  ImageConverterName: "ImageConverterFunction"

MaxPanUploadAttempts: 5

WealthAuthFactorUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "wealth-onb-auth-factor-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

AuthFactorUpdateWealthPublisher:
  TopicName: "auth-factor-update-wealth-topic"

UserCommsPublisher:
  QueueName: "wonb-wonb-user-comms-delay-queue"

UserCommsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "wonb-wonb-user-comms-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

InhouseNameMatchThreshold: 0.8

AdvisoryAgreement:
  UnsignedDocS3Path: "common/legal/Epifi_Wealth_Private_Limited_Investment_Advisory_Services_Agreement.pdf"
  AdvisoryAgreementUpdateAt: "2025-07-25T11:00:00+05:30"

Manch:
  DocketTemplateKey: "TMPTS01578"
  AdvisoryAgreementTemplateKey: "TMPTS02309"

InvestmentsFaqCategoryId: "82000180883"
MaxPanDobSubmitAttempts: 3
DisableCKYC: true
