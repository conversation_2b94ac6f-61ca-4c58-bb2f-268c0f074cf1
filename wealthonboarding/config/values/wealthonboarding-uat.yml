Application:
  Environment: "uat"
  Name: "wealthonboarding"

Server:
  Ports:
    GrpcPort: 8089
    GrpcSecurePort: 9507
    HttpPort: 9999
    HttpPProfPort: 9990

EpifiDb:
  AppName: "wealthonboarding"
  StatementTimeout: 5s
  Username: "epifi_wealth_dev_user"
  Password: ""
  Name: "epifi_wealth"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "uat/cockroach/ca.crt"
  SSLClientCert: "uat/cockroach/client.epifi_wealth_dev_user.crt"
  SSLClientKey: "uat/cockroach/client.epifi_wealth_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

# in non-prod envs, we use a common redis cluster with a different db
RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 7

VendorRequestProducer:
  StreamName: "uat-wealthonboarding-vendor-request-publish-stream"

AWS:
  Region: "ap-south-1"

Flags:
  TrimDebugMessageFromStatus: true
  VendorRequestStore: "kinesis"   # options are "kinesis" "crdb" "crdb_and_kinesis"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "uat/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "uat/gcloud/profiling-service-account-key"
    OfficerSignature: "uat/connectedaccount/docket-verification-officer-signature"

RefreshFaceMatchStatusSqsPublisher:
  QueueName: "uat-wo-refresh-fm-status-delay-queue"

RefreshFaceMatchStatusSqsSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-wo-refresh-fm-status-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

FetchFMStatusInitialDelayDuration: 60s

UpdateTxnStateSqsPublisher:
  QueueName: "uat-wo-e-sign-txn-state-delay-queue"

UpdateTxnStateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-wo-e-sign-txn-state-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 4
      MaxAttempts: 10
      TimeUnit: "Second"

UpdateTxnStateDelayDuration: 5s

InitWealthOnboardingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-wo-onboarding-stage-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 4
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 15
        Period: 1m
    Namespace: "connectedaccount"

MinNameMatchScore: 1

CvlKra:
  UploadPath: "/data"
  PdfExpiryTime: 86400
  VerificationTimeInHours: 0

S3Conf:
  Bucket: "epifi-uat-wealth-onboarding"
  LivenessPath: "liveness"
  DownloadedKraDocS3Path: "downloaded_kra_doc"

VendorRequestS3Conf:
  Bucket: "epifi-uat-wealthonboarding-vendor-request"

Digio:
  BaseSignPath: "https://ext.digio.in/#/gateway/login"

LivenessConfig:
  OTPThreshold: 50
  LivenessThreshold: 50
  LivenessStalenessDuration: 72h

KraDocketSignConfig:
  OfficerName: "Jolly Joseph"
  EmployeeCode: "EPIFI_007"
  OfficerDesignation: "ASST MANAGER"
  IntermediaryCode: "IC_007"
  CallBackUrl: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"

AgreementPdf:
  RelativePath: "data/sample-agreement.pdf"

OCRThresholdScoreConfig:
    DOCUMENT_PROOF_TYPE_PAN: 40.0
    DOCUMENT_PROOF_TYPE_DRIVING_LICENSE: 100.0
    DOCUMENT_PROOF_TYPE_PASSPORT: 100.0
    DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR: 100.0
    DOCUMENT_PROOF_TYPE_OFFLINE_AADHAAR_VERIFICATION: 100.0

DaysToExpiry: 180

WealthOnboardingStepsRetrySqsCustomDelayPublisher:
  DestQueueName: "uat-wo-steps-retry-delay-queue"
  OrchestratorSqsPublisher:
    QueueName: "uat-custom-delay-orchestrator-queue"

WealthOnboardingStepsRetryDelaySqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-wo-steps-retry-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"

StepsRetryStrategy:
  "ONBOARDING_STEP_DOWNLOAD_KRA_DOC":
    "STEP_STATUS_IN_PROGRESS":
      "STEP_SUB_STATUS_KRA_DOCKET_DOWNLOAD_NOT_READY_YET":
        RegularInterval:
          Interval: 30
          MaxAttempts: 10
          TimeUnit: "Minute"
  "ALLSTEPS":
    "STEP_STATUS_TRANSIENT_FAILURE":
      "ALLSUBSTATUS":
        RegularInterval:
          Interval: 1
          MaxAttempts: 24
          TimeUnit: "Hour"
    "STEP_STATUS_IN_PROGRESS":
      "STEP_SUB_STATUS_KYC_NOT_VALIDATED_YET":
        RegularInterval:
          Interval: 1
          MaxAttempts: 3
          TimeUnit: "Minute"
      "STEP_SUB_STATUS_VENDOR_DOWNTIME":
        RegularInterval:
          Interval: 1
          MaxAttempts: 3
          TimeUnit: "Minute"

FeatureControlledReleaseConfig:
  WEALTH_ONBOARDING_FEATURE_NSDL_PAN_INQUIRY_WITH_NAME:
    StickyPercentageConstraintConfig:
      RolloutPercentage: 0
    AppVersionConstraintConfig:
      MinAndroidVersion: 10000
      MinIOSVersion: 10000
  WEALTH_ONBOARDING_FEATURE_RISK_PROFILING:
    AppVersionConstraintConfig:
      MinAndroidVersion: 10000
      MinIOSVersion: 10000
  WEALTH_ONBOARDING_FEATURE_MANDATORY_LIVENESS:
    AppVersionConstraintConfig:
      MinAndroidVersion: 1000
      MinIOSVersion: 1000

NotifyMeFix:
  MaxAndroidVersion: 9999
  MaxIOSVersion: 156

DigilockerConfig:
  ClientId: "EBB0DE86"
  LoginUrl: "https://api.digitallocker.gov.in/public/oauth2/1/authorize?response_type=code&client_id=%v&redirect_uri=%v&state=myData"
  CallbackUrl: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"

KraDowntime:
  DowntimeFrom: "2022-03-11 17:05:00.000"
  DowntimeTo: "2022-03-11 17:10:00.000"

Tracing:
  Enable: true

LambdaFunctions:
  ImageConverterName: "ImageConverterFunction"

MaxPanUploadAttempts: 5

WealthAuthFactorUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-wealth-onb-auth-factor-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

AuthFactorUpdateWealthPublisher:
  TopicName: "uat-auth-factor-update-wealth-topic"

UserCommsPublisher:
  QueueName: "uat-wonb-user-comms-delay-queue"

UserCommsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-wonb-user-comms-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

InhouseNameMatchThreshold: 0.8

AdvisoryAgreement:
  UnsignedDocS3Path: "common/legal/Epifi_Wealth_Private_Limited_Investment_Advisory_Services_Agreement.pdf"

Manch:
  DocketTemplateKey: "TMPTS01578"
  AdvisoryAgreementTemplateKey: "TMPTS02309"

InvestmentsFaqCategoryId: "82000180883"
MaxPanDobSubmitAttempts: 3
DisableCKYC: true
