Flags:
  TrimDebugMessageFromStatus: true
  EnableStepStatusUpdateValidations: true
  EnableBankAccountValidation: false

EnableNameMatch: false

StageDescriptionMapping:
  ONBOARDING_STEP_DATA_COLLECTION: "This step is triggered for 2 scenarios:/n1. User's data exists in KRA but some data is missing/n2. We need to do fresh KYC of the user and need to collect some info from the user which we were not able to find in CKYC/nUsually this data would have to be collected:/nPAN image, Gender, Marital status, income slab, Signature"
  ONBOARDING_STEP_PAN_VERIFICATION: "This step validates the PAN against NSDL API, external vendor involved here so potential for downtimes/errors happening here"
  ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA: "This step checks if user's KYC details already exist with KYC & Registration agency(KRA), which is CVL in our case. If present and all details are sufficient, we need not collect any more info from the user and need not to a KYC again for wealth entity"
  ONBOARDING_STEP_FACE_MATCH: "Facematch of user's image with KY<PERSON> image is done. This is a non-blocking step and done async from the wealth onboarding flow. Low match cases would get flagged and user's wealth account can be put on hold"
  ONBOARDING_STEP_LIVENESS: "We rely on liveness done during Fi a/c onboarding. For certain scenarios we need to re-collect this, in that case we will collect it from the user in the onboarding flow and this step will become mandatory"
  ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO: "This steps determines if user's personal info such as mother's name, father's name, mobile no, email id, PAN no, DOB and nominee details are fetched from user profile"
  ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET: "This is needed for fresh KYC users of wealth onb. In this step we get the KYC form and documents of the users updated with Aadhaar e-sign. Here we rely on external vendor which in turn relies on NSDL and UIDAI, so chances of failure are high here. Also cases such as Aadhaar OTP not coming are expected"
  ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP: "This is implicitly part of the wealth TnC. There are 2 scenarios here:/n1. For already Fi a/c holders, we would recollect T&C consent as we are updating the content, so for such users it would get collected during fi wealth onboarding/n2. For new users, this would get collected as part of Fi a/c onboarding itself"
  OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION_FOR_INVESTMENT: "Various data points such as employment information etc needed to complete the pre-requisites for investment are collected in the back end in this step"
  OnboardingStep_ONBOARDING_STEP_UPLOAD_DOCKET: "The KYC details collected and Aadhaar e-signed by the user is submitted to the KRA in this step"
  OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC: "The KRA documents available for the user for whom KYC already existed is downloaded and stored at our end in this step"
  OnboardingStep_ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW: "In this step the liveness video, redaction of aadhaar number or extraction of expiry date from POA are verified manually by wealth ops on sherlock"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Tracing:
  Enable: false

NotificationIcons:
  WarningIconUrl: "https://epifi-icons.pointz.in/common/warning.png"
  SuccessIconUrl: "https://epifi-icons.pointz.in/common/success.png"
  FiBankIconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"

# Notifications to send users based on step, status and sub-status combination
# Step, Status, SubStatus and Campaign must be valid strings based on their respective enums
# Step: wealthonboarding.OnboardingStep, Status: wealthonboarding.OnboardingStepStatus, SubStatus: wealthonboarding.OnboardingStepSubStatus, Campaign: comms.CampaignName
# Delay must be a valid time.Duration
# TODO(Brijesh): Shift from unspecified sub-statuses to specific ones to reduce cases where PN is sent for a more broad set of reasons
# TODO Explanation: Even after user has accepted TnC if the TnC step of orchestrator fails due to a vendor downtime, user is sent a PN to accept TnC
UserComms:
  # PAN card verification of user failed because of DOB mismatch
  - Step: "ONBOARDING_STEP_DOWNLOAD_KRA_DOC"
    Status: "STEP_STATUS_IN_PROGRESS"
    SubStatus: "STEP_SUB_STATUS_EMPTY_DOWNLOAD_API_RESPONSE"
    Notifications:
      - Campaign: "CAMPAIGN_NAME_WONB_DOB_MISMATCH_PAN_NEEDED"
        NotificationData:
          Title: "ID Needed! 📸"
          Body: "Please upload a copy of your PAN card to start investing"
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "15m"
  # User visited dropped off from missing data input screen
  # TODO(Brijesh): This PN was also meant for users dropping off from intro screen
  # Currently intro screen is on way to be removed from all flows, hence de-prioritizing this for later
  - Step: "ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO"
    Status: "STEP_STATUS_IN_PROGRESS"
    SubStatus: "STEP_SUB_STATUS_USER_INPUT_NEEDED"
    Notifications:
      - Campaign: "CAMPAIGN_NAME_WONB_START_SCREEN_DROP_OFF_T15MIN"
        NotificationData:
          Title: "Reasons to invest from Fi 🥰 ⬇️"
          Body: "🗓 Daily, weekly and monthly SIPs\n0️⃣ Commission Fees\n⏳ 3-minutes joining process\n\nTap to get started!"
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "15m"
      - Campaign: "CAMPAIGN_NAME_WONB_START_SCREEN_DROP_OFF_T24HR"
        NotificationData:
          Title: "Unlock the next level of Fi ⭐️⭐️🌟"
          Body: "Complete a quick 3-minute joining process to start investing directly from Fi."
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "24h"
  # PAN card verification not completed due to technical error and user doesn't retry
  # Note: This doesn't cater to the DOB mismatch case while downloading KRA docs, separate PN is present for that
  - Step: "ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO"
    Status: "STEP_STATUS_IN_PROGRESS"
    SubStatus: "STEP_SUB_STATUS_USER_INPUT_INCL_PAN_REUPLOAD_NEEDED"
    Notifications:
      - Campaign: "CAMPAIGN_NAME_WONB_PAN_DROP_OFF_T15MIN"
        NotificationData:
          Title: "🤏 You're this close to the finish line"
          Body: "Give your PAN verification another shot! Make sure you hold the camera still while it captures your PAN card."
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "15m"
      - Campaign: "CAMPAIGN_NAME_WONB_PAN_DROP_OFF_T24HR"
        NotificationData:
          Title: "Almost there! Verify your PAN details ⬇️"
          Body: "Pick up where you left off! Give your PAN verification another shot and get closer to investing directly from Fi."
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "24h"
  # Video verification not completed due to error and user doesn't retry
  # TODO(Brijesh): Uncomment after testing successfully in non-prod
#  - Step: "ONBOARDING_STEP_LIVENESS"
#    Status: "STEP_STATUS_IN_PROGRESS"
#    SubStatus: "STEP_SUB_STATUS_LIVENESS_FAILED_WITH_RETRY"
#    Notifications:
#      - Campaign: "CAMPAIGN_NAME_WONB_VIDEO_DROP_OFF_T15MIN"
#        NotificationData:
#          Title: "Complete your video verification now! ⬇️"
#          Body: "You've got another chance to retry video verification! Make sure you are in a well-lit area and read out the 4-digits clearly to complete it."
#          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
#        Delay: "15m"
#      - Campaign: "CAMPAIGN_NAME_WONB_VIDEO_DROP_OFF_T24HR"
#        NotificationData:
#          Title: "Ready to retry video verification?"
#          Body: "Your video verification couldn't be completed last time. But, no worries! Tap now to retry ➡️"
#          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
#        Delay: "24h"
  # User has completed wealth onboarding but drops off before e-signing Aadhaar
  - Step: "ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET"
    Status: "STEP_STATUS_IN_PROGRESS"
    SubStatus: "STEP_SUB_STATUS_CUSTOMER_SIGN_PENDING"
    Notifications:
      - Campaign: "CAMPAIGN_NAME_WONB_AADHAAR_SIGN_DROP_OFF_T30MIN"
        NotificationData:
          Title: "Seal it with a sign ✍️️"
          Body: "Complete your joining process and start investing on Fi now!"
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "30m"
      - Campaign: "CAMPAIGN_NAME_WONB_AADHAAR_SIGN_DROP_OFF_T24HR"
        NotificationData:
          Title: "Give us a sign 👀 ✍️"
          Body: "Complete your joining process and start investing on Fi now!"
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "24h"
  # We have recreated docket which was in hold and need user's sign again
  - Step: "ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET"
    Status: "STEP_STATUS_IN_PROGRESS"
    SubStatus: "STEP_SUB_STATUS_CUSTOMER_SIGN_PENDING_HOLD_CASES"
    Notifications:
      - Campaign: "CAMPAIGN_NAME_WONB_AADHAAR_SIGN_DROP_OFF_T30MIN"
        NotificationData:
          Title: "Seal it with a sign ✍️️"
          Body: "Complete your joining process and start investing on Fi now!"
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "30m"
      - Campaign: "CAMPAIGN_NAME_WONB_AADHAAR_SIGN_DROP_OFF_T24HR"
        NotificationData:
          Title: "Give us a sign 👀 ✍️"
          Body: "Complete your joining process and start investing on Fi now!"
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "24h"
  # User drops-off before accepting terms and conditions
  - Step: "ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP"
    Status: "STEP_STATUS_IN_PROGRESS"
    SubStatus: "STEP_SUB_STATUS_UNSPECIFIED"
    Notifications:
      - Campaign: "CAMPAIGN_NAME_WONB_TNC_DROP_OFF_T15MIN"
        NotificationData:
          Title: "One last step! 💪️️"
          Body: "Read through and accept the T&Cs to start investing on Fi!"
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "15m"
      - Campaign: "CAMPAIGN_NAME_WONB_TNC_DROP_OFF_T24HR"
        NotificationData:
          Title: "Start Mutual Fund investments on Fi 💸️"
          Body: "Invest directly and enjoy 0 commission, 0 brokerage and 100% convenience."
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "24h"
  # User drops-off on visiting first wealth onboarding screen or digilocker CTAs
  # TODO(Brijesh): Use diff notification for drop-offs from first wealth onboarding screen
  - Step: "ONBOARDING_STEP_DOWNLOAD_FROM_DIGILOCKER"
    Status: "STEP_STATUS_PENDING"
    SubStatus: "STEP_SUB_STATUS_DIGILOCKER_ACCOUNT_AVAILABILITY_UNKNOWN"
    Notifications:
      - Campaign: "CAMPAIGN_NAME_WONB_DIGILOCKER_MODAL_DROP_OFF_T15MIN"
        NotificationData:
          Title: "This will only take a few minutes ⏳️"
          Body: "You are just a few steps away, connect your Digilocker account and start investing"
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "15m"
      - Campaign: "CAMPAIGN_NAME_WONB_DIGILOCKER_MODAL_DROP_OFF_T24HR"
        NotificationData:
          Title: "🤏 You're this close to investing on Fi"
          Body: "Finish your joining process now!"
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "24h"
  # User started digilocker flow but dropped off before completing it
  - Step: "ONBOARDING_STEP_DOWNLOAD_FROM_DIGILOCKER"
    Status: "STEP_STATUS_IN_PROGRESS"
    SubStatus: "STEP_SUB_STATUS_UNSPECIFIED"
    Notifications:
      - Campaign: "CAMPAIGN_NAME_WONB_DIGILOCKER_FLOW_DROP_OFF_T30MIN"
        NotificationData:
          Title: "Get one step closer to investing on Fi!"
          Body: "Your documents need a quick verification before you can hop on to investing on Fi. Complete the process now ➡️"
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "30m"
      - Campaign: "CAMPAIGN_NAME_WONB_DIGILOCKER_FLOW_DROP_OFF_T24HR"
        NotificationData:
          Title: "Take it to the finish line 🏁"
          Body: "You're so close! Finish your joining process to invest on Fi now."
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "24h"
