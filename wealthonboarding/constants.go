package wealthonboarding

const (
	ConnectedAccountSignAgreementTitle = "Securely connect your other bank accounts to Fi"
	ConnectedAccountSignAgreementDesc  = "An RBI-approved process is used to safely view your other bank transaction data on Fi"

	InvestmentSignAgreementTitle = "Start investing in mutual funds on Fi"
	InvestmentSignAgreementDesc  = "We've made investments effortless! Choose a fund, set an amount and tell us how often you wish to invest - voila! Watch your money grow"

	ContactCustomerSupportTitle = "Something Went Wrong"
	ContactCustomerSupportDesc  = "Please Contact Support"

	WaitTitle = "Please Wait"
	WaitDesc  = "we are verifying your data"

	FailedTitle = "Failed to verify your data"
	FailedDesc  = "This feature will be disabled for you"

	RetryTitle = "Something Went Wrong"
	RetryDesc  = "Please try after some time"

	ConnectedAccountFutureScopeTitle = "Connected Accounts is getting ready for you as we speak"
	ConnectedAccountFutureScopeDesc  = "We are working hard to make it available for all our users, how about we notify you when it’s ready? Here’s what you can do with Connected Accounts soon"

	InvestmentFutureScopeTitle  = "Mutual Funds is getting ready for you as we speak"
	InvestmentFutureScopeDesc   = "We are working hard to make it available for all our users, how about we notify you when it’s ready? Here’s what is in store for you with Mutual Funds"
	InvestmentFutureScopeDescV2 = "We're working hard to make Mutual Funds available for all users. We'll let you know when it's ready"

	TransientErrorTitle       = "Dang! This should not have happened"
	TransientErrorDescription = "Looks like the page failed to load. Please try again"

	TerminalStateTitle       = "Uh-oh! Your identity check was unsuccessful"
	TerminalStateDescription = "ERR-02 Your KYC verification failed"

	UpdateFiAppToStartInvestingTitle       = "Update Fi app to start investing"
	UpdateFiAppToStartInvestingDescription = "For a smoother and better investment experience on Fi, please update the app"

	ConnectedAccountNotSupportedTitle = "Uh-oh! Connected accounts is not supported on this app version"
	ConnectedAccountNotSupportedDesc  = "You need to update to the latest app version from %v to get access\n\nHere’s what you can do with Connected Accounts after the update"

	AndroidStore = "play store"
	IosStore     = "app store"
)
