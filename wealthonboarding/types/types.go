// Package types define custom type definitions to facilitate dependency injection via wire
package types

import (
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/stream"

	"github.com/redis/go-redis/v9"
)

// VendorRequestProducer publishing vendor request to kinesis stream
type VendorRequestProducer stream.Producer
type RefreshFaceMatchStatusSqsPublisher queue.DelayPublisher
type WealthRedisStore *redis.Client
type AuthFactorUpdateWealthPublisher queue.Publisher
type WealthS3Client s3.S3Client
type VendorRequestS3Client s3.S3Client
