package helper

import (
	"context"
	"time"

	"github.com/pkg/errors"

	types "github.com/epifi/gamma/api/typesv2"
	cvlVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/cvl"
	wVendor "github.com/epifi/gamma/api/vendors/wealth"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
)

// nolint:funlen
func GetKraDownloadData(details *cvlVgPb.PanDetails) *woPb.KraDownloadedData {
	res := &woPb.KraDownloadedData{
		PanDetails: &woPb.KraDownloadedData_DownloadedPanDetails{
			UpdateFlag:     details.GetUpdateFlag(),
			Type:           details.GetType(),
			No:             details.GetNo(),
			Date:           details.GetDate(),
			Exmt:           details.GetExmt(),
			ExmtCat:        details.GetExmtCat(),
			Name:           details.GetName(),
			IdProof:        details.GetIdProof(),
			IpvFlag:        details.GetIpvFlag(),
			IpvDate:        details.GetIpvDate(),
			Gen:            details.GetGen(),
			PanNo:          details.GetPanNo(),
			PanexNo:        details.GetPanexNo(),
			PanCopy:        details.GetPanCopy(),
			FName:          details.GetFName(),
			RegNo:          details.GetRegNo(),
			DobDt:          details.GetDobDt(),
			DoiDt:          details.GetDoiDt(),
			CommenceDt:     details.GetCommenceDt(),
			Nationality:    details.GetNationality(),
			OthNationality: details.GetOthNationality(),
			CompStatus:     details.GetCompStatus(),
			OthCompStatus:  details.GetOthCompStatus(),
			ResStatus:      details.GetResStatus(),
			ResStatusProof: details.GetResStatusProof(),
			UidNo:          details.GetUidNo(),
			CorAdd1:        details.GetCorAdd1(),
			CorAdd2:        details.GetCorAdd2(),
			CorAdd3:        details.GetCorAdd3(),
			CorCity:        details.GetCorCity(),
			CorPincd:       details.GetCorPincd(),
			CorState:       details.GetCorState(),
			CorCtry:        details.GetCorCtry(),
			OffNo:          details.GetOffNo(),
			ResNo:          details.GetResNo(),
			MobNo:          details.GetMobNo(),
			FaxNo:          details.GetFaxNo(),
			Email:          details.GetEmail(),
			CorAddProof:    details.GetCorAddProof(),
			CorAddRef:      details.GetCorAddRef(),
			CorAddDt:       details.GetCorAddDt(),
			PerAdd1:        details.GetPerAdd1(),
			PerAdd2:        details.GetPerAdd2(),
			PerAdd3:        details.GetPerAdd3(),
			PerCity:        details.GetPerCity(),
			PerPincd:       details.GetPerPincd(),
			PerState:       details.GetPerState(),
			PerCtry:        details.GetPerCtry(),
			PerAddProof:    details.GetPerAddProof(),
			PerAddRef:      details.GetPerAddRef(),
			PerAddDt:       details.GetPerAddDt(),
			Income:         details.GetIncome(),
			Occ:            details.GetOcc(),
			OthOcc:         details.GetOthOcc(),
			PolConn:        details.GetPolConn(),
			DocProof:       details.GetDocProof(),
			InternalRef:    details.GetInternalRef(),
			BranchCode:     details.GetBranchCode(),
			MarStatus:      details.GetMarStatus(),
			Netwrth:        details.GetNetwrth(),
			NetworthDt:     details.GetNetworthDt(),
			IncorpPlc:      details.GetIncorpPlc(),
			Otherinfo:      details.GetOtherinfo(),
			Filler1:        details.GetFiller1(),
			Filler2:        details.GetFiller2(),
			Filler3:        details.GetFiller3(),
			Status:         details.GetStatus(),
			Statusdt:       details.GetStatusdt(),
			ErrorDesc:      details.GetErrorDesc(),
			DumpType:       details.GetDumpType(),
			Dnlddt:         details.GetDnlddt(),
			KraInfo:        details.GetKraInfo(),
			Signature:      details.GetSignature(),
			IopFlg:         details.GetIopFlg(),
			PosCode:        details.GetPosCode(),
			SummRec: &woPb.KraDownloadedData_DownloadedPanDetails_SummaryRec{
				OthkraCode:   details.GetSummRec().GetOthkraCode(),
				OthkraBatch:  details.GetSummRec().GetOthkraBatch(),
				ReqDate:      details.GetSummRec().GetReqDate(),
				TotalRec:     details.GetSummRec().GetTotalRec(),
				ResponseDate: details.GetSummRec().GetResponseDate(),
			},
			KycMode:                  details.GetKycMode(),
			AppRemarks:               details.GetAppRemarks(),
			KycStatus:                details.GetKycStatus(),
			CorrAddress:              details.GetCorrAddress(),
			PermAddress:              details.GetPermAddress(),
			Dob:                      details.GetDob(),
			IncomeSlab:               details.GetIncomeSlab(),
			PoliticallyExposedStatus: details.GetPoliticallyExposedStatus(),
		},
	}
	for _, d := range details.GetAddlData() {
		ad := &woPb.KraDownloadedData_DownloadedPanDetails_AddData{
			AddldataUpdtflg:      d.GetAddldataUpdtflg(),
			EntityPan:            d.GetEntityPan(),
			AddldataPan:          d.GetAddldataPan(),
			AddldataName:         d.GetAddldataName(),
			AddldataDinUid:       d.GetAddldataDinUid(),
			AddldataRelationship: d.GetAddldataRelationship(),
			AddldataPolconn:      d.GetAddldataPolconn(),
			AddldataResadd_1:     d.GetAddldataResadd_1(),
			AddldataResadd_2:     d.GetAddldataResadd_2(),
			AddldataResadd_3:     d.GetAddldataResadd_3(),
			AddldataRescity:      d.GetAddldataRescity(),
			AddldataRespincd:     d.GetAddldataRespincd(),
			AddldataResstate:     d.GetAddldataResstate(),
			AddldataRescountry:   d.GetAddldataRescountry(),
			AddldataFiller1:      d.GetAddldataFiller1(),
			AddldataFiller2:      d.GetAddldataFiller2(),
			AddldataFiller3:      d.GetAddldataFiller3(),
		}
		res.GetPanDetails().AddlData = append(res.GetPanDetails().GetAddlData(), ad)
	}
	return res
}

func GetKraAddressProofType(od *woPb.OnboardingDetails) wVendor.KraAddressProof {
	poaType := od.GetMetadata().GetPoaDetails().GetProofType()
	if poaType == types.DocumentProofType_DOCUMENT_PROOF_TYPE_UNSPECIFIED {
		poaType = od.GetMetadata().GetPoaWithOcr().GetDoc().GetProofType()
	}
	if poaType == types.DocumentProofType_DOCUMENT_PROOF_TYPE_UNSPECIFIED {
		return wVendor.KraAddressProof_KRA_ADDRESS_PROOF_UNSPECIFIED
	}
	switch poaType {
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR:
		return wVendor.KraAddressProof_KRA_ADDRESS_PROOF_AADHAAR
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_PASSPORT:
		return wVendor.KraAddressProof_KRA_ADDRESS_PROOF_PASSPORT
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_DRIVING_LICENSE:
		return wVendor.KraAddressProof_KRA_ADDRESS_PROOF_DRIVING_LICENSE
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_VOTER_ID:
		return wVendor.KraAddressProof_KRA_ADDRESS_PROOF_VOTER_IDENTITY_CARD
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_UID:
		return wVendor.KraAddressProof_KRA_ADDRESS_PROOF_AADHAAR
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GOVERNMENT:
		return wVendor.KraAddressProof_KRA_ADDRESS_PROOF_PROOF_OF_ADDRESS_ISSUED_BY_ANY_GOVERNMENT_OR_STATUTORY_AUTHORITY
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GAZETTED:
		return wVendor.KraAddressProof_KRA_ADDRESS_PROOF_PROOF_OF_ADDRESS_ISSUED_BY_ANY_GOVERNMENT_OR_STATUTORY_AUTHORITY
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_UTILITY_BILL:
		return wVendor.KraAddressProof_KRA_ADDRESS_PROOF_GAS_BILL
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_BANK_ACCOUNT_OR_POST_OFFICE_SAVINGS_BANK_ACCOUNT_STATEMENT:
		return wVendor.KraAddressProof_KRA_ADDRESS_PROOF_LATEST_BANK_ACCOUNT_STATEMENT
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_LETTER_OF_ALLOTMENT_OF_ACCOMMODATION_FROM_EMPLOYER_ISSUED_BY_STATE_OR_CENTRAL_GOVERNMENT_DEPARTMENTS:
		return wVendor.KraAddressProof_KRA_ADDRESS_PROOF_ID_CARD_WITH_ADDRESS_ISSUED_BY_CENTRAL_OR_STATE_GOVERNMENT
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_DOCUMENTS_ISSUED_BY_GOVERNMENT_DEPARTMENTS_OF_FOREIGN_JURISDICTIONS:
		return wVendor.KraAddressProof_KRA_ADDRESS_PROOF_ID_CARD_WITH_ADDRESS_ISSUED_BY_CENTRAL_OR_STATE_GOVERNMENT
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_OFFLINE_AADHAAR_VERIFICATION:
		return wVendor.KraAddressProof_KRA_ADDRESS_PROOF_AADHAAR
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_SELF_DECLARATION:
		return wVendor.KraAddressProof_KRA_ADDRESS_PROOF_SELF_DECLARATION_BY_HIGH_COURT_OR_SUPREME_COURT_JUDGE
	default:
		return wVendor.KraAddressProof_KRA_ADDRESS_PROOF_ANY_OTHER_PROOF_OF_ADDRESS
	}
}

func IsCollectedAddressProofAadhaar(od *woPb.OnboardingDetails) bool {
	return GetKraAddressProofType(od) == wVendor.KraAddressProof_KRA_ADDRESS_PROOF_AADHAAR
}

// IsAddressProofValidationRequired checks if KRA has validated the address proof in case it is Aadhaar
// Validation of Aadhaar (submitted as address proof) is a SEBI mandated requirement from 1 Nov 2022
// For KYC records created before 1 Nov, KRAs will validate them within 180 days from 1 Nov and either accept or reject them
// Hence we are allowing users with KYC records created before 1 Nov 2022 to invest for now
// If onboarding is complete or address proof in docket is not Aadhaar, there is no need to check for validation
// Note: this function should be called only after docket upload process has been started
func IsAddressProofValidationRequired(ctx context.Context, od *woPb.OnboardingDetails) (bool, error) {
	if od.GetStatus() == woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED {
		return false, nil
	}
	if !IsCollectedAddressProofAadhaar(od) {
		return false, nil
	}
	uploadAttempts := od.GetMetadata().GetUploadKraDocData().GetUploadAttempts()
	if len(uploadAttempts) == 0 {
		return false, errors.New("no upload attempts present")
	}
	kycEnquiryDetails := uploadAttempts[len(uploadAttempts)-1].GetPanEnquiry()
	kycRecordCreatedAt := time.Now() // if KYC has no record yet for the user, default to current date
	if kycEnquiryDetails.GetCreatedDate().IsValid() {
		kycRecordCreatedAt = kycEnquiryDetails.GetCreatedDate().AsTime()
	}
	nov1IST := time.Date(2022, 11, 1, 0, 0, 0, 0, datetime.IST)
	if kycRecordCreatedAt.After(nov1IST) &&
		IsCollectedAddressProofAadhaar(od) &&
		kycEnquiryDetails.GetStatus() != wVendor.KraStatus_KRA_STATUS_VALIDATED {
		logger.Info(ctx, "KYC record created after Nov 1 with Aadhaar as POA is not validated")
		return true, nil
	} else {
		return false, nil
	}
}

// IsIndianAsPerKraPanDetails has special handling for some cases where KRA seems to be sending nationality as "Others"
// with that other nationality field populated as "IND".
func IsIndianAsPerKraPanDetails(panDetails *woPb.KraDownloadedData_DownloadedPanDetails) bool {
	if panDetails.GetNationality() == types.Nationality_NATIONALITY_INDIAN ||
		panDetails.GetOthNationality() == "IND" {
		return true
	}
	return false
}
