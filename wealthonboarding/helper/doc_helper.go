package helper

import (
	"context"

	types "github.com/epifi/gamma/api/typesv2"
)

// DocumentHelper interface provides functions to upload images and get their path, as well as download them
//
//go:generate mockgen -source=./doc_helper.go -destination=./../test/mocks/helpers/mock_doc_helper.go -package=mock_helpers
type DocumentHelper interface {
	// UploadDoc function uploads all the images present in the document proof to S3 and
	// returns the same document proof with S3 URL paths populated instead of images
	UploadDoc(ctx context.Context, actorId string, doc *types.DocumentProof) (*types.DocumentProof, error)

	// UploadRawDoc uploads the base-64 encoded file data to a s3 file in raw format
	// The file in S3 is NOT base-64 encoded
	UploadRawDoc(ctx context.Context, actorId string, doc *types.DocumentProof) (*types.DocumentProof, error)

	// DownloadDoc function downloads the images present in S3 URL paths and
	// returns the same document proof with images instead of S3 URL paths
	DownloadDoc(ctx context.Context, doc *types.DocumentProof) (*types.DocumentProof, error)

	// GetSignedUrl function returns a signed url with a pre-defined expiry time
	GetSignedUrl(ctx context.Context, dst string, isUiCompatible bool, bucket string, expiryTime int64) (string, error)

	// UploadRawData uploads the raw data which could be in any format for instance a zip file to s3
	UploadRawData(ctx context.Context, s3Path string, rawData []byte) error

	// GetRawS3Document downloads the document, converts the base64 data to raw data, uploads the raw data and returns the new document
	// It expects base-64 encoded data to be present in the request arg and returns s3 paths of uploaded files in the response
	GetRawS3Document(ctx context.Context, actorId string, doc *types.DocumentProof) (*types.DocumentProof, error)
}
