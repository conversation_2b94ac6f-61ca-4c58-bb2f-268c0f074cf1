package helper

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	gammanames "github.com/epifi/gamma/pkg/names"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"strings"

	"github.com/google/wire"
	pdfCpuApi "github.com/pdfcpu/pdfcpu/pkg/api"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/fieldmaskpb"

	"github.com/epifi/be-common/pkg/aws/v2/lambda"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/frontend/account/screening"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	clientstate "github.com/epifi/gamma/api/frontend/wealthonboarding/clientstate"
	rmsPb "github.com/epifi/gamma/api/rms/manager"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	vgBankCustomerPb "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	wVendorPb "github.com/epifi/gamma/api/vendors/wealth"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/api/wealthonboarding/user"
	pkgIncomeSlab "github.com/epifi/gamma/pkg/incomeslab"
	"github.com/epifi/gamma/wealthonboarding/config"
	woErr "github.com/epifi/gamma/wealthonboarding/errors"
	wealthUser "github.com/epifi/gamma/wealthonboarding/user"
)

var CommonHelperWireSet = wire.NewSet(NewCommonHelper, wire.Bind(new(ICommonHelper), new(*CommonHelper)))

//go:generate mockgen -source=common.go -destination=../test/mocks/helpers/common.go -package=mock_helpers
type ICommonHelper interface {
	IsWealthInternalUser(ctx context.Context, details *woPb.OnboardingDetails) (bool, error)
	FetchBankAccountDetails(ctx context.Context, actorId string) (*woPb.BankDetails, error)
	SanitizeImage(ctx context.Context, im *commontypes.Image) (*commontypes.Image, error)
	UpdateIncomeSlabForUser(ctx context.Context, details *woPb.OnboardingDetails) error
	CreateAndPopulateUserPersonalDetails(ctx context.Context, actorId string) (*woPb.GetOrCreateUserResponse_UserDetails, error)
	ValidateAndUpdateUserPersonalDetails(ctx context.Context, usr *user.User) (*woPb.GetOrCreateUserResponse_UserDetails, error)
}

type CommonHelper struct {
	actorClient        actorPb.ActorClient
	userClient         userPb.UsersClient
	userGroupClient    userGroupPb.GroupClient
	lambdaClient       lambda.LambdaClient
	conf               *config.Config
	employmentClient   employment.EmploymentClient
	userService        wealthUser.IService
	bankCustomerClient bankCustomerPb.BankCustomerServiceClient
	savingsClient      savingsPb.SavingsClient
}

func NewCommonHelper(
	actorClient actorPb.ActorClient,
	userClient userPb.UsersClient,
	userGroupClient userGroupPb.GroupClient,
	lambdaClient lambda.LambdaClient,
	conf *config.Config,
	employmentClient employment.EmploymentClient,
	userSvc wealthUser.IService,
	bankCustomerClient bankCustomerPb.BankCustomerServiceClient,
	savingsClient savingsPb.SavingsClient,
) *CommonHelper {
	return &CommonHelper{
		actorClient:        actorClient,
		userClient:         userClient,
		userGroupClient:    userGroupClient,
		lambdaClient:       lambdaClient,
		conf:               conf,
		employmentClient:   employmentClient,
		userService:        userSvc,
		bankCustomerClient: bankCustomerClient,
		savingsClient:      savingsClient,
	}
}

func (c *CommonHelper) IsWealthInternalUser(ctx context.Context, details *woPb.OnboardingDetails) (bool, error) {
	// get actor info
	actorResp, err := c.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: details.GetActorId()})
	if te := epifigrpc.RPCError(actorResp, err); te != nil {
		return false, errors.Wrap(te, fmt.Sprintf("failed to fetch actor: %s", details.GetActorId()))
	}

	userResp, err := c.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_Id{
			Id: actorResp.GetActor().GetEntityId(),
		},
	})
	if te := epifigrpc.RPCError(userResp, err); te != nil {
		return false, errors.Wrap(te, fmt.Sprintf("failed to fetch user: %s", actorResp.GetActor().GetEntityId()))
	}

	userGrpRes, userGrpErr := c.userGroupClient.GetGroupsMappedToEmail(ctx,
		&userGroupPb.GetGroupsMappedToEmailRequest{Email: userResp.GetUser().GetProfile().GetEmail()})
	if err := epifigrpc.RPCError(userGrpRes, userGrpErr); err != nil {
		return false, errors.Wrap(err, fmt.Sprintf("failed to fetch user group: %s", actorResp.GetActor().GetEntityId()))
	}
	for _, g := range userGrpRes.GetGroups() {
		if g == commontypes.UserGroup_WEALTH_ONBOARDING_INTERNAL {
			return true, nil
		}
	}
	return false, nil
}

// FetchBankAccountDetails returns the bank account details for the user
// NOTE: Validating the bank account number is a mandatory compliance requirement during onboarding.
// Hence, the account details are fetched directly from the bank API below.
// and compared against the account number stored in Fi's internal data stores to meet the requirement.
// Alternatively, if fetched indirectly from an internal data store,
// then the account number should be validated using a penny-drop or some other process.
func (c *CommonHelper) FetchBankAccountDetails(ctx context.Context, actorId string) (*woPb.BankDetails, error) {
	savingsAccRes, err := c.savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
		Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
				ActorId:     actorId,
				PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})
	if err = epifigrpc.RPCError(savingsAccRes, err); err != nil {
		return nil, errors.Wrap(err, "error getting savings account essentials")
	}
	if !c.conf.Flags.EnableBankAccountValidation {
		return &woPb.BankDetails{
			AccountNumber: savingsAccRes.GetAccount().GetAccountNo(),
			IfscCode:      savingsAccRes.GetAccount().GetIfscCode(),
		}, nil
	}
	customerAccounts, err := c.getCustomerAccountsFromBank(ctx, actorId)
	if err != nil {
		return nil, errors.Wrap(err, "error getting customer accounts from bank")
	}

	// NOTE: Please add a test if the validation logic below becomes more complex over time.
	savingsAccountValidatedWithBank := false
	for _, customerAccount := range customerAccounts {
		if customerAccount.GetAccountNumber() == savingsAccRes.GetAccount().GetAccountNo() {
			savingsAccountValidatedWithBank = true
			break
		}
	}
	if !savingsAccountValidatedWithBank {
		return nil, errors.Errorf("error validating savings account with bank: %s", actorId)
	}
	return &woPb.BankDetails{
		AccountNumber: savingsAccRes.GetAccount().GetAccountNo(),
		IfscCode:      savingsAccRes.GetAccount().GetIfscCode(),
	}, nil
}

func (c *CommonHelper) getCustomerAccountsFromBank(ctx context.Context, actorId string) ([]*vgBankCustomerPb.Account, error) {
	bankCustomerRes, err := c.bankCustomerClient.GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err = epifigrpc.RPCError(bankCustomerRes, err); err != nil {
		return nil, errors.Wrap(err, "error getting bank customer")
	}
	customerDetailsRes, err := c.userClient.GetCustomerDetails(ctx, &userPb.GetCustomerDetailsRequest{
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		UserId:     bankCustomerRes.GetBankCustomer().GetUserId(),
		ActorId:    bankCustomerRes.GetBankCustomer().GetActorId(),
		Provenance: userPb.Provenance_APP,
	})
	if err = epifigrpc.RPCError(customerDetailsRes, err); err != nil {
		return nil, errors.Wrap(err, "error getting customer details")
	}
	return customerDetailsRes.GetAccounts(), nil
}

func GetDowntimeDeeplink(msg string) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_ERROR_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingErrorScreenOptions{
			WealthOnboardingErrorScreenOptions: &deeplinkPb.WealthOnboardingErrorScreenOptions{
				OnboardingAction: deeplinkPb.WealthOnboardingAction_WEALTH_ONBOARDING_ACTION_FAILED,
				Title:            VendorDowntimeTitle,
				Description:      msg,
				IllustrationUrl:  TransientErrorIllustrationURL,
				Cta: &deeplinkPb.Cta{
					Text: "Okay",
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_HOME,
					},
				},
			},
		},
	}
}

func GetDowntimeDeeplinkDescription(downtimeErr error) string {
	for errors.Unwrap(downtimeErr) != epifierrors.ErrDowntimeExpected && downtimeErr != nil {
		downtimeErr = errors.Unwrap(downtimeErr)
	}
	sp := strings.Split(downtimeErr.Error(), ": ")
	if len(sp) == 0 {
		logger.ErrorNoCtx("downtime error doesn't wrap any other error")
		return ""
	}
	return sp[0]
}

// ParseUsersIp splits the ipAddress by , delimiter and returns the first element
func ParseUsersIp(ipAddress string) string {
	sp := strings.Split(ipAddress, ",")
	return sp[0]
}

// MergePDFs takes list a pdf in byte array format and merges them it concatenates them
// one after the other and returns a single pdf in result. Order is the same as the one present in the list provided
func MergePDFs(pdfs ...[]byte) ([]byte, error) {
	if len(pdfs) < 2 {
		return nil, errors.New("cannot merge pdf, len is less 2")
	}
	var rs []io.ReadSeeker
	// iterate over all the pdfs and create a ReaderSeeker for them
	for _, pdf := range pdfs {
		rs = append(rs, bytes.NewReader(pdf))
	}
	// creating a writer in which the result would be stored
	var b bytes.Buffer
	wr := io.Writer(&b)
	// merging pdfs
	err := pdfCpuApi.MergeRaw(rs, wr, false, nil)
	if err != nil {
		return nil, errors.Wrap(woErr.ErrPdfMergeFailed, err.Error())
	}
	return b.Bytes(), nil
}

func GetDocumentFormatType(doc *types.DocumentProof) commontypes.ImageType {
	// check if photo inside doc is populated
	if len(doc.GetPhoto()) > 0 {
		return doc.GetPhoto()[0].GetImageType()
	}
	// we need to fetch type from the s3 uploaded path
	s3Path := doc.GetS3Paths()
	if len(s3Path) > 0 {
		s3ImagePath := s3Path[0]
		imagePath := strings.Split(s3ImagePath, "/")
		imageType := getImageType(imagePath[len(imagePath)-1])
		return imageType
	}
	return commontypes.ImageType_IMAGE_TYPE_UNSPECIFIED
}

func (c *CommonHelper) SanitizeImage(ctx context.Context, im *commontypes.Image) (*commontypes.Image, error) {
	if im.GetImageType() != commontypes.ImageType_TIFF {
		return im, nil
	}
	// as of now, tiff format is not supported in web browsers like chrome and firefox
	// hence converting it into a png
	// TODO(sharath): check if it is better to use pkg image converter and use lambda as a fallback
	convRes, convErr := c.lambdaClient.Execute(ctx, c.conf.LambdaFunctions.ImageConverterName, ([]byte)(fmt.Sprintf(`{"data":"%v"}`, im.GetImageDataBase64())))
	if convErr != nil {
		return im, convErr
	}
	res := &struct {
		Data         string `json:"data,omitempty"`
		ErrorMessage string `json:"errorMessage,omitempty"`
	}{}
	err := json.Unmarshal(convRes, res)
	if err != nil {
		return im, err
	}
	if res.Data == "" {
		return im, errors.New("getting empty data response from vendor")
	}
	if res.ErrorMessage != "" {
		return im, errors.New(fmt.Sprintf("Error in response payload: %v", res.ErrorMessage))
	}
	return &commontypes.Image{
		ImageType:       commontypes.ImageType_PNG,
		ImageDataBase64: res.Data,
		ImageUrl:        im.GetImageUrl(),
	}, nil
}

func (c *CommonHelper) UpdateIncomeSlabForUser(ctx context.Context, details *woPb.OnboardingDetails) error {
	// check if user has provided their income slab
	incomeSlab := details.GetMetadata().GetCustomerProvidedData().GetIncomeSlab()
	if incomeSlab == types.IncomeSlab_INCOME_SLAB_UNSPECIFIED {
		// user has not provided us with income slab, we don't need to update it in the user service data
		return nil
	}
	logger.Info(ctx, "updating income slab in user service")
	// get salary range
	mn, mx, err := pkgIncomeSlab.GetSalaryRangeFromIncomeSlab(incomeSlab)
	if err != nil {
		return err
	}
	// get entity id of user using actor
	actorRes, actorErr := c.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: details.GetActorId()})
	if te := epifigrpc.RPCError(actorRes, actorErr); te != nil {
		return errors.Wrap(te, "error while fetching actor")
	}
	empSalaryUpdRes, empSalaryUpdErr := c.employmentClient.UpdateAnnualSalary(ctx, &employment.UpdateAnnualSalaryRequest{
		ActorId: details.GetActorId(),
		AnnualSalary: &screening.AnnualSalary{
			Range: &screening.AnnualSalaryRange{
				MinValue: mn,
				MaxValue: mx,
			},
		},
	})
	if te := epifigrpc.RPCError(empSalaryUpdRes, empSalaryUpdErr); te != nil {
		return errors.Wrap(te, "error while updating income slab in employment")
	}
	updRes, updErr := c.userClient.UpdateUser(ctx, &userPb.UpdateUserRequest{
		User: &userPb.User{
			Id: actorRes.GetActor().GetEntityId(),
			Profile: &userPb.Profile{
				SalaryRange: &userPb.SalaryRange{
					MinValue: mn,
					MaxValue: mx,
				},
			},
		},
		UpdateMask: []userPb.UserFieldMask{
			userPb.UserFieldMask_SALARY_RANGE,
		},
	})
	if te := epifigrpc.RPCError(updRes, updErr); te != nil {
		return errors.Wrap(te, "error while updating income slab in user")
	}
	return nil
}

func (c *CommonHelper) CreateAndPopulateUserPersonalDetails(ctx context.Context, actorId string) (*woPb.GetOrCreateUserResponse_UserDetails, error) {
	userResp, errResp := c.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err := epifigrpc.RPCError(userResp, errResp); err != nil {
		logger.Error(ctx, "error while fetching user", zap.Error(err))
		return nil, err
	}
	usr, err := c.userService.Create(ctx, &user.User{
		ActorId: actorId,
		PersonalDetails: &user.PersonalDetails{
			Name:        gammanames.BestNameFromProfile(ctx, userResp.GetUser().GetProfile()),
			PhoneNumber: userResp.GetUser().GetProfile().GetPhoneNumber(),
			Email:       userResp.GetUser().GetProfile().GetEmail(),
			Dob:         userResp.GetUser().GetProfile().GetDateOfBirth(),
			FatherName:  userResp.GetUser().GetProfile().GetFatherName(),
			MotherName:  userResp.GetUser().GetProfile().GetMotherName(),
			PanDetails: &types.DocumentProof{
				ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
				Id:        userResp.GetUser().GetProfile().GetPAN(),
			},
		},
	})
	if err != nil {
		logger.Error(ctx, "error in create wealth user", zap.Error(err))
		return nil, err
	}
	return &woPb.GetOrCreateUserResponse_UserDetails{
		Id:          usr.GetId(),
		Name:        usr.GetPersonalDetails().GetName(),
		Gender:      usr.GetPersonalDetails().GetGender(),
		PhoneNumber: usr.GetPersonalDetails().GetPhoneNumber(),
		Email:       usr.GetPersonalDetails().GetEmail(),
		Dob:         usr.GetPersonalDetails().GetDob(),
		PanDetails:  usr.GetPersonalDetails().GetPanDetails(),
	}, nil
}

// GetNameForDocket returns the name of the user to be used in the KRA docket
// it returns the PanCardName from NSDL data if it is not empty, else it falls back to Pan Name
func GetNameForDocket(od *woPb.OnboardingDetails) *commontypes.Name {
	// check if PanCardName string has a non-zero length
	if len(od.GetMetadata().GetNsdlData().GetPanDetails().GetPanCardName()) != 0 {
		return &commontypes.Name{FirstName: od.GetMetadata().GetNsdlData().GetPanDetails().GetPanCardName()}
	}
	// fallback to nsdl Pan name if PanCardName is not present
	if od.GetMetadata().GetNsdlData().GetPanDetails().GetName() != nil {
		return od.GetMetadata().GetNsdlData().GetPanDetails().GetName()
	}
	return od.GetMetadata().GetPersonalDetails().GetName()
}

func IsImagePresent(doc *types.DocumentProof) bool {
	if doc == nil || len(doc.GetPhoto()) == 0 || doc.GetPhoto()[0] == nil || doc.GetPhoto()[0].GetImageDataBase64() == "" {
		return false
	}
	return true
}

func GetDocumentFromDocumentLists(docs []*types.DocumentProof, dpt types.DocumentProofType) *types.DocumentProof {
	for _, doc := range docs {
		if doc.GetProofType() == dpt {
			return doc
		}
	}
	return nil
}

func IsDocumentStoreAllowed(dpt types.DocumentProofType) bool {
	for _, bdt := range BlackListedDocumentProofTypesToStore {
		if bdt == dpt {
			return false
		}
	}
	return true
}

var BlackListedDocumentProofTypesToStore = []types.DocumentProofType{
	types.DocumentProofType_DOCUMENT_PROOF_TYPE_OFFLINE_AADHAAR_VERIFICATION,
	types.DocumentProofType_DOCUMENT_PROOF_TYPE_EKYC_AUTHENTICATION,
	types.DocumentProofType_DOCUMENT_PROOF_TYPE_UID,
	types.DocumentProofType_DOCUMENT_PROOF_TYPE_LEGAL_ENTITY_DOCUMENT,
}

// GetEmailIdFromDetails returns email from onboarding details personal details metadata (received from EPIFI TECH)
// it falls back to the ckyc details of user in case if it is not in personal details metadata
func GetEmailIdFromDetails(od *woPb.OnboardingDetails) string {
	if od.GetMetadata().GetPersonalDetails().GetEmail() != "" {
		return od.GetMetadata().GetPersonalDetails().GetEmail()
	}
	return od.GetMetadata().GetCkycData().GetDownloadData().GetPersonalDetails().GetEmailId()
}

// GetMobileNumberFromDetails returns mobile number from onboarding details metadata
// it falls back to mobile number from digilocker or ckyc details of user in case if it is not in above details
// nolint:gocritic
func GetMobileNumberFromDetails(od *woPb.OnboardingDetails) *commontypes.PhoneNumber {
	if od.GetMetadata().GetPersonalDetails().GetPhoneNumber() != nil {
		return od.GetMetadata().GetPersonalDetails().GetPhoneNumber()
	} else if od.GetMetadata().GetDigilockerData().GetPersonalData().GetMobile() != nil {
		return od.GetMetadata().GetDigilockerData().GetPersonalData().GetMobile()
	} else {
		personalDetails := od.GetMetadata().GetCkycData().GetDownloadData().GetPersonalDetails()
		if personalDetails != nil && personalDetails.GetMobileNumber() != nil && personalDetails.GetMobileNumber().GetCountryCode() != 0 && personalDetails.GetMobileNumber().GetNationalNumber() != 0 {
			return personalDetails.GetMobileNumber()
		}
	}
	return nil
}

// GetPermanentAddressFromDetails returns the permanent address from digilocker or ckyc data
func GetPermanentAddressFromDetails(od *woPb.OnboardingDetails) *types.PostalAddress {
	if od.GetAgentProvidedData().GetAddress() != nil {
		return od.GetAgentProvidedData().GetAddress()
	}
	if od.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData() != nil {
		return od.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData().GetAddress()
	}
	return od.GetMetadata().GetCkycData().GetDownloadData().GetPersonalDetails().GetPermanentAddress()
}

// IsAadhaarCollectedFromDigilocker returns true if aadhaar is collected from digilocker
// Should be used only after digilocker step is completed
func IsAadhaarCollectedFromDigilocker(od *woPb.OnboardingDetails) bool {
	return od.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData() != nil
}

func UnblockRMSRuleExecutions(ctx context.Context, od *woPb.OnboardingDetails,
	rmsClient rmsPb.RuleManagerClient, blockedSubscriptionExecutionState rmsPb.SubscriptionExecutionState, unblockReason rmsPb.SubscriptionStateChangeReason) error {
	fm, fmErr := fieldmaskpb.New(&rmsPb.RuleSubscription{}, "id")
	if fmErr != nil {
		return errors.Wrap(fmErr, "error in generating field mask for subscription")
	}
	subsResp, subsErr := rmsClient.GetSubscriptionsByExecutionState(ctx, &rmsPb.GetSubscriptionsByExecutionStateRequest{
		ActorId:         od.GetActorId(),
		ExecutionStates: []rmsPb.SubscriptionExecutionState{blockedSubscriptionExecutionState},
		FieldMask:       fm,
	})
	if te := epifigrpc.RPCError(subsResp, subsErr); te != nil {
		return errors.Wrap(te, "error getting subscriptions by execution state")
	}
	var subIds []string
	for _, sub := range subsResp.GetSubscriptions().GetRuleSubscriptions() {
		subIds = append(subIds, sub.GetId())
	}
	if len(subIds) == 0 {
		logger.Info(ctx, "no blocked subscriptions found")
		return nil
	}

	updateSubExecStateRes, updSubExecStateErr := rmsClient.UpdateSubscriptionExecutionState(ctx, &rmsPb.UpdateSubscriptionExecutionStateRequest{
		ExecutionState:        rmsPb.SubscriptionExecutionState_SUBSCRIPTION_EXECUTION_ALLOWED,
		StateChangeReason:     unblockReason,
		StateChangeProvenance: rmsPb.SubscriptionStateChangeProvenance_INTERNAL,
		SubscriptionIds:       subIds,
	})
	if te := epifigrpc.RPCError(updateSubExecStateRes, updSubExecStateErr); te != nil {
		return errors.Wrap(te, "error updating subscription execution state")
	}
	return nil
}

// GetDigiLockerDeeplink returns the deeplink which allows user to log into their DigiLocker account and provide us permission to access it
// NOTE: Ensure flow params are populated inside the deeplink before it is sent to clients
func GetDigiLockerDeeplink(digiLockerConfig *config.DigilockerConfig) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_DIGILOCKER_DOWNLOAD,
		ScreenOptions: &deeplinkPb.Deeplink_DigilockerDownloadScreenOptions{
			DigilockerDownloadScreenOptions: &deeplinkPb.DigilockerDownloadScreenOptions{
				LoginUrl:          fmt.Sprintf(digiLockerConfig.LoginUrl, digiLockerConfig.ClientId, digiLockerConfig.CallbackUrl),
				CallbackUrl:       digiLockerConfig.CallbackUrl,
				Flow:              deeplinkPb.DigilockerFlow_DIGILOCKER_FLOW_WEALTH_ONBOARDING,
				Title:             "Sign up quickly with DigiLocker",
				SubTitle:          "You're minutes away from opening an investment account",
				SecurityInfo:      "Your data is safe with us",
				Description:       "Upload your PAN & Aadhaar info from your DigiLocker account",
				IllustrationUrl:   FinishLineFlagIllustrationURL,
				ReinforcementText: "⚡️ INSTANT APPROVAL",
				// flow must be populated at top-level based on client's entry point
			},
		},
	}
}

func GetRefreshAadhaarDeeplink(details *woPb.OnboardingDetails) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_ERROR_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingErrorScreenOptions{
			WealthOnboardingErrorScreenOptions: &deeplinkPb.WealthOnboardingErrorScreenOptions{
				IllustrationUrl: ManualVerificationIllustrationURL,
				Title:           "Refresh your Aadhaar page on DigiLocker",
				Description:     "There was an error in getting your Aadhaar data. Go to digilocker.gov.in, refresh your Aadhaar, and then try again on the Fi app.",
				Cta: &deeplinkPb.Cta{
					Text: "Retry",
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_LANDING_SCREEN,
						ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingLandingScreenOptions{
							WealthOnboardingLandingScreenOptions: &deeplinkPb.WealthOnboardingLandingScreenOptions{
								Flow: GetFeWealthFlow(details.GetCurrentWealthFlow()),
							},
						},
					},
				},
			},
		},
	}
}

func GetKycNotValidatedDeeplink(details *woPb.OnboardingDetails, faqCategoryId string) *deeplinkPb.Deeplink {
	var kraString string
	switch details.GetMetadata().GetKraData().GetStatusData().GetPanStatusDetails().GetKraCode() {
	case wVendorPb.KraCode_KRA_CODE_CVLKRA:
		kraString = "Our partner CVL KRA"
	case wVendorPb.KraCode_KRA_CODE_NDML:
		kraString = "NDML KRA"
	case wVendorPb.KraCode_KRA_CODE_DOTEX:
		kraString = "DOTEX KRA"
	case wVendorPb.KraCode_KRA_CODE_BSE:
		kraString = "BSE KRA"
	case wVendorPb.KraCode_KRA_CODE_CAMS:
		kraString = "CAMS KRA"
	case wVendorPb.KraCode_KRA_CODE_KARVY:
		kraString = "KARVY KRA"
	default:
		kraString = "KRA"
	}
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_ERROR_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingErrorScreenOptions{
			WealthOnboardingErrorScreenOptions: &deeplinkPb.WealthOnboardingErrorScreenOptions{
				IllustrationUrl: ManualVerificationIllustrationURL,
				Title:           "KYC validation pending!",
				Description:     kraString + " may take up to 3 days to validate your KYC details. Until then, you cannot invest in Mutual Funds.",
				Cta: &deeplinkPb.Cta{
					Text: "Read more",
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_FAQ_CATEGORY,
						ScreenOptions: &deeplinkPb.Deeplink_FaqCategoryOptions{
							FaqCategoryOptions: &deeplinkPb.FaqCategoryOptions{
								CategoryId: faqCategoryId,
							},
						},
					},
				},
			},
		},
	}
}

func GetFeWealthFlow(flow woPb.WealthFlow) clientstate.WealthFlow {
	switch flow {
	default:
		return clientstate.WealthFlow_WEALTH_FLOW_UNSPECIFIED
	case woPb.WealthFlow_WEALTH_FLOW_CONNECTED_ACCOUNT:
		return clientstate.WealthFlow_WEALTH_FLOW_CONNECTED_ACCOUNT
	case woPb.WealthFlow_WEALTH_FLOW_INVESTMENT:
		return clientstate.WealthFlow_WEALTH_FLOW_INVESTMENT
	}
}

func (c *CommonHelper) ValidateAndUpdateUserPersonalDetails(ctx context.Context, usr *user.User) (*woPb.GetOrCreateUserResponse_UserDetails, error) {
	if !areMandatoryDetailsPresent(usr) {
		userResp, errResp := c.userClient.GetUser(ctx, &userPb.GetUserRequest{
			Identifier: &userPb.GetUserRequest_ActorId{
				ActorId: usr.GetActorId(),
			},
		})
		if err := epifigrpc.RPCError(userResp, errResp); err != nil {
			logger.Error(ctx, "error while fetching user", zap.Error(err))
			return nil, err
		}
		usr.GetPersonalDetails().Name = gammanames.BestNameFromProfile(ctx, userResp.GetUser().GetProfile())
		usr.GetPersonalDetails().PhoneNumber = userResp.GetUser().GetProfile().GetPhoneNumber()
		usr.GetPersonalDetails().Email = userResp.GetUser().GetProfile().GetEmail()
		usr.GetPersonalDetails().Dob = userResp.GetUser().GetProfile().GetDateOfBirth()
		usr.GetPersonalDetails().FatherName = userResp.GetUser().GetProfile().GetFatherName()
		usr.GetPersonalDetails().MotherName = userResp.GetUser().GetProfile().GetMotherName()
		usr.GetPersonalDetails().PanDetails = &types.DocumentProof{
			ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
			Id:        userResp.GetUser().GetProfile().GetPAN(),
		}
		err := c.userService.CreateOrUpdateUser(ctx, usr, nil)
		if err != nil {
			logger.Error(ctx, "error in updating wealth user data", zap.Error(err))
			return nil, err
		}
	}
	return &woPb.GetOrCreateUserResponse_UserDetails{
		Id:          usr.GetId(),
		Name:        usr.GetPersonalDetails().GetName(),
		Gender:      usr.GetPersonalDetails().GetGender(),
		PhoneNumber: usr.GetPersonalDetails().GetPhoneNumber(),
		Email:       usr.GetPersonalDetails().GetEmail(),
		Dob:         usr.GetPersonalDetails().GetDob(),
		PanDetails:  usr.GetPersonalDetails().GetPanDetails(),
	}, nil
}

func areMandatoryDetailsPresent(usr *user.User) bool {
	if usr.GetPersonalDetails().GetPanDetails().GetId() == "" {
		return false
	}
	if usr.GetPersonalDetails().GetDob() == nil {
		return false
	}
	if usr.GetPersonalDetails().GetPhoneNumber() == nil {
		return false
	}
	if usr.GetPersonalDetails().GetEmail() == "" {
		return false
	}
	return true
}
