package helper

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"path/filepath"
	"strings"
	"time"

	aws "github.com/aws/aws-sdk-go-v2/aws"
	awsS3 "github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/google/uuid"
	"github.com/google/wire"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/aws/v2/s3"

	types "github.com/epifi/gamma/api/typesv2"
)

const (
	convertedDocProofImagePrefix = "converted_doc_proof_image/"
	aadhaarDocPrefix             = "converted_aadhaar_doc_image/"
)

var _ DocumentHelper = &DocHelperImpl{}
var DocProofHelperWireSet = wire.NewSet(NewDocProofHelperImpl, wire.Bind(new(DocumentHelper), new(*DocHelperImpl)))

type DocHelperImpl struct {
	s3Client   s3.S3Client
	httpClient *http.Client
}

func NewDocProofHelperImpl(
	s3Client s3.S3Client,
	httpClient *http.Client,
) *DocHelperImpl {
	return &DocHelperImpl{
		s3Client:   s3Client,
		httpClient: httpClient,
	}
}

type docDataWithFilePath struct {
	base64ImgDataStr string
	filePath         string
}

// getDocsWithFilePaths returns the image data in base-64 encoded format along with the file path to upload the data in S3
func (d *DocHelperImpl) getDocsWithFilePaths(actorId string, doc *types.DocumentProof) ([]*docDataWithFilePath, error) {
	if doc.GetProofType() == types.DocumentProofType_DOCUMENT_PROOF_TYPE_UNSPECIFIED || len(doc.GetPhoto()) == 0 {
		return nil, errors.New(fmt.Sprintf("document_proof is not specified : %v or no images present in doc : %v", doc.GetProofType(), len(doc.GetPhoto())))
	}
	// identifier is used to uniquely identify and store all the images for a particular document
	identifier := uuid.New().String()
	var docsWithFilePaths []*docDataWithFilePath
	dstPrefixPath := convertedDocProofImagePrefix
	// if the document type is aadhaar, we need to store it in a common directory place so that it would be easier to perform one time delete
	// all actors would have their aadhaar doc proof under converted_aadhaar_doc_image/
	if doc.GetProofType() == types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR || doc.GetProofType() == types.DocumentProofType_DOCUMENT_PROOF_TYPE_UID {
		dstPrefixPath = aadhaarDocPrefix
	}
	for imageIndex, image := range doc.GetPhoto() {
		imageType := image.GetImageType()
		if imageType == commontypes.ImageType_IMAGE_TYPE_UNSPECIFIED {
			return nil, errors.New("image type not specified")
		}
		base64ImgDataStr, err := d.fetchImage(image)
		if err != nil {
			return nil, errors.Wrap(err, "error while fetching base64 image data")
		}
		fileName := fmt.Sprintf("%v/%v.%v", doc.GetProofType().String(), imageIndex, imageType)
		docsWithFilePaths = append(docsWithFilePaths, &docDataWithFilePath{
			base64ImgDataStr: base64ImgDataStr,
			filePath:         filepath.Join(dstPrefixPath, actorId, identifier, fileName),
		})
	}
	return docsWithFilePaths, nil
}

func (d *DocHelperImpl) UploadDoc(ctx context.Context, actorId string, doc *types.DocumentProof) (*types.DocumentProof, error) {
	docsWithFilePaths, err := d.getDocsWithFilePaths(actorId, doc)
	if err != nil {
		return nil, errors.Wrap(err, "error getting doc file paths")
	}
	var s3ImagePaths []string
	for _, docWithFilePath := range docsWithFilePaths {
		err = d.s3Client.Write(ctx, docWithFilePath.filePath, []byte(docWithFilePath.base64ImgDataStr), "bucket-owner-full-control")
		if err != nil {
			return nil, errors.Wrap(err, "error while uploading image to s3")
		}
		s3ImagePaths = append(s3ImagePaths, docWithFilePath.filePath)
	}
	return &types.DocumentProof{
		ProofType: doc.GetProofType(),
		Id:        doc.GetId(),
		Expiry:    doc.GetExpiry(),
		Dob:       doc.GetDob(),
		S3Paths:   s3ImagePaths,
	}, nil
}

func (d *DocHelperImpl) UploadRawDoc(ctx context.Context, actorId string, doc *types.DocumentProof) (*types.DocumentProof, error) {
	docsWithFilePaths, err := d.getDocsWithFilePaths(actorId, doc)
	if err != nil {
		return nil, errors.Wrap(err, "error getting doc file paths")
	}
	var s3ImagePaths []string
	for _, docWithFilePath := range docsWithFilePaths {
		rawImgData, err := base64.StdEncoding.DecodeString(docWithFilePath.base64ImgDataStr)
		if err != nil {
			return nil, errors.Wrap(err, "error decoding base 64 image data")
		}
		err = d.s3Client.Write(ctx, docWithFilePath.filePath, rawImgData, "bucket-owner-full-control")
		if err != nil {
			return nil, errors.Wrap(err, "error while uploading image to s3")
		}
		s3ImagePaths = append(s3ImagePaths, docWithFilePath.filePath)
	}
	return &types.DocumentProof{
		ProofType: doc.GetProofType(),
		Id:        doc.GetId(),
		Expiry:    doc.GetExpiry(),
		Dob:       doc.GetDob(),
		S3Paths:   s3ImagePaths,
	}, nil
}

func (d *DocHelperImpl) DownloadDoc(ctx context.Context, doc *types.DocumentProof) (*types.DocumentProof, error) {
	s3ImagePath := doc.GetS3Paths()
	if len(s3ImagePath) == 0 {
		return nil, errors.New("no s3 path present")
	}
	resDoc := &types.DocumentProof{
		ProofType: doc.GetProofType(),
		Id:        doc.GetId(),
		Expiry:    doc.GetExpiry(),
		Dob:       doc.GetDob(),
	}
	for _, s3Image := range s3ImagePath {
		imagePath := strings.Split(s3Image, "/")
		imageType := getImageType(imagePath[len(imagePath)-1])
		if imageType == commontypes.ImageType_IMAGE_TYPE_UNSPECIFIED {
			return nil, errors.New(fmt.Sprintf("image type unspecified, path: %v", s3Image))
		}
		imgBytes, readErr := d.s3Client.Read(ctx, s3Image)
		if readErr != nil {
			return nil, errors.Wrap(readErr, fmt.Sprintf("error while reading image, path : %v", s3ImagePath))
		}
		image := &commontypes.Image{
			ImageType:       imageType,
			ImageDataBase64: string(imgBytes),
		}
		resDoc.Photo = append(resDoc.Photo, image)
	}
	return resDoc, nil
}

func (d *DocHelperImpl) UploadRawData(ctx context.Context, s3Path string, rawData []byte) error {
	err := d.s3Client.Write(ctx, s3Path, rawData, "bucket-owner-full-control")
	if err != nil {
		return errors.Wrap(err, "error while storing raw data to s3")
	}
	return nil
}

func (d *DocHelperImpl) fetchImage(image *commontypes.Image) (string, error) {
	if image.GetImageUrl() == "" && image.GetImageDataBase64() == "" {
		return "", errors.New("image base64 and url are empty")
	}
	// check if base64 data is present in image
	if image.GetImageDataBase64() != "" {
		return image.GetImageDataBase64(), nil
	}
	// try to fetch image from the url present
	request, err := http.NewRequestWithContext(context.Background(), http.MethodGet, image.GetImageUrl(), nil)
	if err != nil {
		return "", err
	}
	imageRes, imageErr := d.httpClient.Do(request)
	if imageErr != nil {
		return "", imageErr
	}
	defer func() {
		_ = imageRes.Body.Close()
	}()
	readRes, readErr := io.ReadAll(imageRes.Body)
	if readErr != nil {
		return "", readErr
	}
	return base64.StdEncoding.EncodeToString(readRes), nil
}

func getImageType(fileName string) commontypes.ImageType {
	ext := strings.Split(fileName, ".")[1]
	imType, ok := commontypes.ImageType_value[strings.ToUpper(ext)]
	if !ok {
		return commontypes.ImageType_IMAGE_TYPE_UNSPECIFIED
	}
	return commontypes.ImageType(imType)
}

func (d *DocHelperImpl) GetSignedUrl(ctx context.Context, dst string, isUiCompatible bool, bucket string, expiryTime int64) (string, error) {
	getObjReq := &awsS3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(dst),
	}
	if isUiCompatible {
		switch getImageType(dst) {
		case commontypes.ImageType_PDF:
			getObjReq.ResponseContentDisposition = aws.String("inline")
			getObjReq.ResponseContentType = aws.String("application/pdf")
		case commontypes.ImageType_JPEG:
			getObjReq.ResponseContentDisposition = aws.String("inline")
			getObjReq.ResponseContentType = aws.String("image/jpeg")
		case commontypes.ImageType_PNG:
			getObjReq.ResponseContentDisposition = aws.String("inline")
			getObjReq.ResponseContentType = aws.String("image/png")
		case commontypes.ImageType_TIFF:
			getObjReq.ResponseContentDisposition = aws.String("inline")
			getObjReq.ResponseContentType = aws.String("image/tiff")
		default:
			// do not set content type
		}
	}
	presignUrl, err := d.s3Client.GetPreSignedURLFromS3Input(ctx, getObjReq, time.Second*time.Duration(expiryTime))
	if err != nil {
		return "", errors.Wrap(err, "error while forming signed url for object")
	}
	return presignUrl, nil
}

func (d *DocHelperImpl) GetRawS3Document(ctx context.Context, actorId string, doc *types.DocumentProof) (*types.DocumentProof, error) {
	b64Doc, err := d.DownloadDoc(ctx, doc)
	if err != nil {
		return nil, errors.Wrap(err, "error while downloading original doc using helper")
	}
	if len(b64Doc.GetPhoto()) == 0 {
		return nil, errors.New("image base64 data is empty")
	}
	rawData, bErr := base64.StdEncoding.DecodeString(b64Doc.GetPhoto()[0].GetImageDataBase64())
	if bErr != nil {
		return nil, errors.Wrap(bErr, "error in converting to raw data")
	}
	imageType := GetDocumentFormatType(doc)
	if imageType == commontypes.ImageType_IMAGE_TYPE_UNSPECIFIED {
		return nil, errors.New("image type unspecified")
	}
	rawDoc, uErr := d.UploadDoc(ctx, actorId, &types.DocumentProof{
		ProofType: doc.GetProofType(),
		Photo: []*commontypes.Image{
			{
				ImageType: imageType,
				// passing raw data since we need raw data uploaded in s3
				ImageDataBase64: string(rawData),
			},
		},
	})
	if uErr != nil {
		return nil, errors.Wrap(uErr, "error in uploading document")
	}
	return rawDoc, nil
}
