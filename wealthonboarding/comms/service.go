package comms

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"strconv"
	"time"

	"go.uber.org/zap"

	commsPb "github.com/epifi/gamma/api/comms"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	fcmpb "github.com/epifi/gamma/api/frontend/fcm"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	wealthCommsPb "github.com/epifi/gamma/api/wealthonboarding/comms"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/helper"

	"github.com/pkg/errors"
)

//go:generate mockgen -source=./service.go -destination=./../test/mocks/comms/mock_service.go -package=mock_comms
type IComms interface {
	// SendCommunication sends the message to user through commsClient
	SendCommunication(ctx context.Context, sendCommunicationReq *SendCommunicationRequest) *SendCommunicationResponse

	// SendNotificationAsync is a helper function which internally calls SendCommunication to send notifications in a safe go routine
	SendNotificationAsync(ctx context.Context, od *woPb.OnboardingDetails, notificationType wealthCommsPb.WealthCommunicationType)
}

type SendCommunicationRequest struct {
	CommunicationType wealthCommsPb.WealthCommunicationType
	PhoneNumber       *commontypes.PhoneNumber
	WealthFlow        woPb.WealthFlow
}

type SendCommunicationResponse struct {
	Error      error
	MessageIds []string
}

type Service struct {
	commsClient commsPb.CommsClient
	conf        *config.Config
}

func NewService(commsClient commsPb.CommsClient, conf *config.Config) *Service {
	return &Service{
		commsClient: commsClient,
		conf:        conf,
	}
}

// SendNotificationAsync Sends a notification to user using a safe go routine
func (s *Service) SendNotificationAsync(ctx context.Context, od *woPb.OnboardingDetails, notificationType wealthCommsPb.WealthCommunicationType) {
	sendNotificationFn := func(ctx context.Context) {
		woCommsRes := s.SendCommunication(ctx, &SendCommunicationRequest{
			CommunicationType: notificationType,
			PhoneNumber:       od.GetMetadata().GetPersonalDetails().GetPhoneNumber(),
			WealthFlow:        od.GetCurrentWealthFlow(),
		})
		if woCommsRes == nil {
			logger.Error(ctx, "empty response from wealth wealth onboarding comms service")
			return
		}
		if woCommsRes.Error != nil {
			logger.Error(ctx, "failed to send notification", zap.Error(woCommsRes.Error))
		}
	}
	goroutine.Run(ctx, 5*time.Second, sendNotificationFn)
}

func (s *Service) SendCommunication(ctx context.Context, sendCommunicationReq *SendCommunicationRequest) *SendCommunicationResponse {
	if sendCommunicationReq.CommunicationType == wealthCommsPb.WealthCommunicationType_WEALTH_COMMUNICATION_TYPE_UNSPECIFIED || sendCommunicationReq.WealthFlow == woPb.WealthFlow_WEALTH_FLOW_UNSPECIFIED || sendCommunicationReq.PhoneNumber == nil {
		return &SendCommunicationResponse{Error: errors.New("invalid argument")}
	}
	commsReq, err := s.getNotificationMessage(sendCommunicationReq.CommunicationType, sendCommunicationReq.PhoneNumber, sendCommunicationReq.WealthFlow)
	if err != nil {
		return &SendCommunicationResponse{Error: errors.Wrap(err, "error in getting notification request")}
	}
	sendMessageResponse, err := s.commsClient.SendMessageBatch(ctx, commsReq)
	switch {
	case err != nil:
		return &SendCommunicationResponse{Error: errors.Wrap(err, "failed to send notification")}
	case sendMessageResponse.GetStatus().IsRecordNotFound():
		return &SendCommunicationResponse{Error: errors.New("failed to send notification with status record not found")}
	case sendMessageResponse.GetStatus().IsInvalidArgument():
		return &SendCommunicationResponse{Error: errors.New("failed to send notification with status invalid argument")}
	case !sendMessageResponse.GetStatus().IsSuccess():
		return &SendCommunicationResponse{Error: errors.New("failed to send notification")}
	}
	return &SendCommunicationResponse{MessageIds: sendMessageResponse.GetMessageIdList()}
}

func (s *Service) getNotificationMessage(notificationType wealthCommsPb.WealthCommunicationType, phoneNumber *commontypes.PhoneNumber, wealthFlow woPb.WealthFlow) (*commsPb.SendMessageBatchRequest, error) {
	phoneNumberStr := strconv.FormatUint(phoneNumber.GetNationalNumber(), 10)
	title, desc, iconUrl := s.getNotificationTitleDescriptionAndIconUrl(wealthFlow, notificationType)
	if title == "" || desc == "" || iconUrl == "" {
		return nil, errors.New(fmt.Sprintf("error in getting notification details, title: %v, desc: %v, iconUrl: %v", title, desc, iconUrl))
	}
	return &commsPb.SendMessageBatchRequest{
		Type:           commsPb.QoS_GUARANTEED,
		UserIdentifier: &commsPb.SendMessageBatchRequest_PhoneNumber{PhoneNumber: phoneNumberStr},
		CampaignName:   getCampaignName(notificationType),
		CommunicationList: []*commsPb.Communication{
			{
				Medium: commsPb.Medium_NOTIFICATION,
				Message: &commsPb.Communication_Notification{Notification: &commsPb.NotificationMessage{
					Priority: commsPb.NotificationPriority_NORMAL,
					Notification: &fcmpb.Notification{
						NotificationType: fcmpb.NotificationType_SYSTEM_TRAY,
						NotificationTemplates: &fcmpb.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcmpb.SystemTrayTemplate{
							CommonTemplateFields: &fcmpb.CommonTemplateFields{
								Title: title,
								Body:  desc,
								// TODO(sharath): direct to appropriate screen after making manual review as final step
								Deeplink: &deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_LANDING_SCREEN,
									ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingLandingScreenOptions{
										WealthOnboardingLandingScreenOptions: &deeplinkPb.WealthOnboardingLandingScreenOptions{
											Flow: helper.GetFeWealthFlow(wealthFlow)}},
								},
							},
						}},
					},
				}},
			},
			{
				Medium: commsPb.Medium_NOTIFICATION,
				Message: &commsPb.Communication_Notification{Notification: &commsPb.NotificationMessage{
					Priority: commsPb.NotificationPriority_NORMAL,
					Notification: &fcmpb.Notification{
						NotificationType: fcmpb.NotificationType_IN_APP,
						NotificationTemplates: &fcmpb.Notification_InAppTemplate{InAppTemplate: &fcmpb.InAppTemplate{
							CommonTemplateFields: &fcmpb.CommonTemplateFields{
								Title:          title,
								Body:           desc,
								IconAttributes: &fcmpb.IconAttributes{IconUrl: iconUrl},
								// TODO(sharath): direct to appropriate screen after making manual review as final step
								Deeplink: &deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_LANDING_SCREEN,
									ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingLandingScreenOptions{
										WealthOnboardingLandingScreenOptions: &deeplinkPb.WealthOnboardingLandingScreenOptions{
											Flow: helper.GetFeWealthFlow(wealthFlow)}},
								},
							},
							AfterClickAction: fcmpb.AfterClickAction_DISMISS,
						}},
					},
				}},
			},
		},
	}, nil
}

func (s *Service) getNotificationTitleDescriptionAndIconUrl(wealthFlow woPb.WealthFlow, commType wealthCommsPb.WealthCommunicationType) (string, string, string) {
	title, desc, iconUrl := "", "", ""
	switch commType {
	case wealthCommsPb.WealthCommunicationType_WEALTH_COMMUNICATION_TYPE_LIVENESS_MANUALLY_REJECTED:
		title = "Oops! Your video verification failed 😶‍🌫"
		iconUrl = s.conf.NotificationIcons.WarningIconUrl
		switch wealthFlow {
		case woPb.WealthFlow_WEALTH_FLOW_CONNECTED_ACCOUNT:
			desc = "No worries. Just try again and resubmit it from the app to connect accounts"
		case woPb.WealthFlow_WEALTH_FLOW_INVESTMENT:
			desc = "No worries. Just try again and resubmit it from the app to start investing"
		default:
			desc = "No worries. Just try again and resubmit it from the app to start investing and connecting your bank accounts"
		}
	case wealthCommsPb.WealthCommunicationType_WEALTH_COMMUNICATION_TYPE_MANUAL_REVIEW_APPROVED:
		title = "We’ve verified your documents!"
		iconUrl = s.conf.NotificationIcons.SuccessIconUrl
		switch wealthFlow {
		case woPb.WealthFlow_WEALTH_FLOW_CONNECTED_ACCOUNT:
			desc = "You’re all set to start connecting your bank accounts on Fi. Get started now!"
		case woPb.WealthFlow_WEALTH_FLOW_INVESTMENT:
			desc = "You’re all set to start investing on Fi. Get started now!"
		default:
			desc = "You’re all set to start investing and connecting your bank accounts on Fi. Get started now!"
		}
	case wealthCommsPb.WealthCommunicationType_WEALTH_COMMUNICATION_TYPE_ESIGN_NEEDED_AGAIN:
		title = "Your KYC data needs reconfirmation ✅"
		iconUrl = s.conf.NotificationIcons.WarningIconUrl
		switch wealthFlow {
		case woPb.WealthFlow_WEALTH_FLOW_CONNECTED_ACCOUNT:
			desc = "Please provide a reconfirmation of the same in the app to connect accounts"
		case woPb.WealthFlow_WEALTH_FLOW_INVESTMENT:
			desc = "Please provide a reconfirmation of the same in the app to start investing"
		default:
			desc = "Please provide a reconfirmation of the same in the app to start investing and connecting your bank accounts"
		}
	case wealthCommsPb.WealthCommunicationType_WEALTH_COMMUNICATION_TYPE_AADHAAR_VALIDATED_BY_KRA:
		title = "You're all set to invest ✅"
		desc = "All your documents for Mutual Fund investment are verified. Get started now ➡️"
		iconUrl = s.conf.NotificationIcons.FiBankIconUrl
	default:
		// do nothing
	}
	return title, desc, iconUrl
}

func getCampaignName(notificationType wealthCommsPb.WealthCommunicationType) commsPb.CampaignName {
	switch notificationType {
	case wealthCommsPb.WealthCommunicationType_WEALTH_COMMUNICATION_TYPE_LIVENESS_MANUALLY_REJECTED:
		return commsPb.CampaignName_CAMPAIGN_NAME_WEALTHONB_LIVENESS_FAILED
	case wealthCommsPb.WealthCommunicationType_WEALTH_COMMUNICATION_TYPE_ESIGN_NEEDED_AGAIN:
		return commsPb.CampaignName_CAMPAIGN_NAME_WEALTHONB_AADHAARESIGN_RESUBMIT
	case wealthCommsPb.WealthCommunicationType_WEALTH_COMMUNICATION_TYPE_ONBORADING_SUPPORTED_NOW:
		return commsPb.CampaignName_CAMPAIGN_NAME_WEALTHONB_ONBOARDING_NOWSUPPORTED
	case wealthCommsPb.WealthCommunicationType_WEALTH_COMMUNICATION_TYPE_MANUAL_REVIEW_APPROVED:
		return commsPb.CampaignName_CAMPAIGN_NAME_WEALTHONB_KYC_MANUALLYAPPROVED
	case wealthCommsPb.WealthCommunicationType_WEALTH_COMMUNICATION_TYPE_DOCUMENT_EXPIRED:
		return commsPb.CampaignName_CAMPAIGN_NAME_WEALTHONB_DOCUMENT_EXPIRED
	case wealthCommsPb.WealthCommunicationType_WEALTH_COMMUNICATION_TYPE_AADHAAR_VALIDATED_BY_KRA:
		return commsPb.CampaignName_CAMPAIGN_NAME_WEALTH_ONBOARDING_AADHAAR_VALIDATED_BY_KRA
	default:
		return commsPb.CampaignName_CAMPAIGN_NAME_UNSPECIFIED
	}
}
