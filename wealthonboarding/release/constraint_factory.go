package release

import (
	"context"
	"fmt"

	"github.com/epifi/gamma/wealthonboarding/config"
)

type ConstraintFactory interface {
	GetConstraint(ctx context.Context, data ConstraintData) (Constraint, error)
	GetConstraintData(conf *config.ConstraintConfig) []ConstraintData
}

type ConstraintFactoryImpl struct {
	appVersionConstraint       *AppVersionConstraint
	stickyPercentageConstraint *StickyPercentageConstraint
	userGroupConstraint        *UserGroupConstraint
	appPlatformConstraint      *AppPlatformConstraint
}

func NewConstraintFactoryImpl(
	appVersionConstraint *AppVersionConstraint,
	stickyPercentageConstraint *StickyPercentageConstraint,
	userGroupConstraint *UserGroupConstraint,
	appPlatformConstraint *AppPlatformConstraint) *ConstraintFactoryImpl {
	return &ConstraintFactoryImpl{
		appVersionConstraint:       appVersionConstraint,
		stickyPercentageConstraint: stickyPercentageConstraint,
		userGroupConstraint:        userGroupConstraint,
		appPlatformConstraint:      appPlatformConstraint,
	}
}

func (c *ConstraintFactoryImpl) GetConstraint(_ context.Context, data ConstraintData) (Constraint, error) {
	switch data.(type) {
	case *AppVersionConstraintData:
		return c.appVersionConstraint, nil
	case *StickyPercentageConstraintData:
		return c.stickyPercentageConstraint, nil
	case *UserGroupConstraintData:
		return c.userGroupConstraint, nil
	case *AppPlatformConstraintData:
		return c.appPlatformConstraint, nil
	default:
		return nil, fmt.Errorf("no constraint found for %t", data)
	}
}

func (c *ConstraintFactoryImpl) GetConstraintData(conf *config.ConstraintConfig) []ConstraintData {
	var cd []ConstraintData
	if conf.AppVersionConstraintConfig != nil {
		cd = append(cd, NewAppVersionConstraintData(conf.AppVersionConstraintConfig))
	}
	if conf.StickyPercentageConstraintConfig != nil {
		cd = append(cd, NewStickyPercentageConstraintData(conf.StickyPercentageConstraintConfig))
	}
	if conf.UserGroupConstraintConfig != nil {
		cd = append(cd, NewUserGroupConstraintData(conf.UserGroupConstraintConfig))
	}
	if conf.AppPlatformConstraintConfig != nil {
		cd = append(cd, NewAppPlatformConstraintData(conf.AppPlatformConstraintConfig))
	}
	return cd
}
