package release

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/wealthonboarding/config"
)

type AppVersionConstraintData struct {
	appVersionConstraintConfig *config.AppVersionConstraintConfig
}

func NewAppVersionConstraintData(appVersionConstraintConfig *config.AppVersionConstraintConfig) *AppVersionConstraintData {
	return &AppVersionConstraintData{appVersionConstraintConfig: appVersionConstraintConfig}
}

func (a *AppVersionConstraintData) IsConstraintData() {}

type AppVersionConstraint struct {
}

func NewAppVersionConstraint() *AppVersionConstraint {
	return &AppVersionConstraint{}
}

func (a *AppVersionConstraint) Evaluate(ctx context.Context, constraintData ConstraintData, _ *CommonConstraintData) (bool, error) {
	appVersionConstraintData, ok := constraintData.(*AppVersionConstraintData)
	if !ok {
		return false, fmt.Errorf("invalid constraint data passed: %t", constraintData)
	}
	platform, version := epificontext.AppPlatformAndVersion(ctx)
	switch platform {
	case commontypes.Platform_ANDROID:
		if version >= appVersionConstraintData.appVersionConstraintConfig.MinAndroidVersion {
			logger.Debug(ctx, "AppVersionConstraint evaluation returned true")
			return true, nil
		}
	case commontypes.Platform_IOS:
		if version >= appVersionConstraintData.appVersionConstraintConfig.MinIOSVersion {
			logger.Debug(ctx, "AppVersionConstraint evaluation returned true")
			return true, nil
		}
	default:
		return false, fmt.Errorf("unhandled platform: %v", platform.String())
	}
	return false, nil
}
