package release

import (
	"context"
	"fmt"
	"strings"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/wealthonboarding/config"

	"github.com/google/wire"
	"github.com/pkg/errors"
)

var constraintsWireSet = wire.NewSet(NewAppVersionConstraint, NewStickyPercentageConstraint, NewUserGroupConstraint, NewAppPlatformConstraint)

var constraintFactoryWireSet = wire.NewSet(
	constraintsWireSet, wire.NewSet(NewConstraintFactoryImpl, wire.Bind(new(ConstraintFactory), new(*ConstraintFactoryImpl))),
)

var evaluatorWireSet = wire.NewSet(NewEvaluator, wire.Bind(new(IEvaluator), new(*Evaluator)))

var EvaluatorWireSet = wire.NewSet(evaluatorWireSet, constraintFactoryWireSet)

type Evaluator struct {
	featureConstraintsData map[string][]ConstraintData
	constraintFactory      ConstraintFactory
}

func NewEvaluator(conf *config.Config, constraintFactory ConstraintFactory) *Evaluator {
	featureConstraintsData := make(map[string][]ConstraintData)
	for feature, constraintConfig := range conf.FeatureControlledReleaseConfig {
		featureConstraintsData[strings.ToUpper(feature)] = constraintFactory.GetConstraintData(constraintConfig)
	}
	return &Evaluator{featureConstraintsData: featureConstraintsData, constraintFactory: constraintFactory}
}

// nolint: funlen
func (e *Evaluator) Evaluate(ctx context.Context, commonConstraintData *CommonConstraintData) (bool, *deeplinkPb.Deeplink, error) {
	constraintDataList, ok := e.featureConstraintsData[commonConstraintData.Feature.String()]
	if !ok {
		logger.Info(ctx, fmt.Sprintf("no constraints found for feature: %v", commonConstraintData.Feature.String()))
		return true, nil, nil
	}
	// default values true
	appVersionResult, ugResult, stickinessResult := true, true, true
	noUgConstraint, noStickinessConstraint := true, true
	for _, constraintData := range constraintDataList {
		constraint, err := e.constraintFactory.GetConstraint(ctx, constraintData)
		if err != nil {
			return false, nil, errors.Wrap(err, fmt.Sprintf("failed to get constraint for the data: %t", constraintData))
		}
		val, err := constraint.Evaluate(ctx, constraintData, commonConstraintData)
		if err != nil {
			return false, nil, errors.Wrap(err, fmt.Sprintf("failed to evaluate constraint: %t", constraint))
		}
		logger.Debug(ctx, fmt.Sprintf("constraint: %t returned %t", constraint, val))
		switch constraint.(type) {
		case *AppVersionConstraint:
			appVersionResult = appVersionResult && val
		case *UserGroupConstraint:
			ugResult = ugResult && val
			noUgConstraint = false
		case *StickyPercentageConstraint:
			stickinessResult = stickinessResult && val
			noStickinessConstraint = false
		case *AppPlatformConstraint:
			if !val {
				return false, nil, nil
			}
		default:
			return false, nil, fmt.Errorf("unknown constraint %t", constraint)
		}
	}
	switch {
	// if both user group and rollout percent fails, return false
	case !ugResult && !stickinessResult:
		return false, nil, nil
	// if percent rollout fails and no user group constraint is present return false
	case !stickinessResult && noUgConstraint:
		return false, nil, nil
	// if user group fails and no percent rollout constraint is present return false
	case !ugResult && noStickinessConstraint:
		return false, nil, nil
	// if user fails app version constraint return update app deeplink
	case !appVersionResult:
		// default deeplink to update app, to be used whenever we want to force users to use a feature
		updateAppDeeplink := &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_STATUS_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingStatusScreenOptions{
				WealthOnboardingStatusScreenOptions: &deeplinkPb.WealthOnboardingStatusScreenOptions{
					OnboardingAction: deeplinkPb.WealthOnboardingAction_WEALTH_ONBOARDING_ACTION_UPDATE_APP,
					// deeplink title, description and other fields depend on which UI flow they entered from, hence populated at a top level
				},
			},
		}

		return false, updateAppDeeplink, nil
	// this case will come after user passes app version constraint for the following
	// 1. user group and stickiness constraint both are not present -> return true
	// 2. user passes percent rollout or user group constraint -> return true
	// 3. user group constraint not present and percent roll out passes -> return true
	// 4. percent roll out not present and user group constraint passes -> return true
	default:
		return true, nil, nil
	}
}
