package release

import (
	"context"
	"fmt"

	actorPb "github.com/epifi/gamma/api/actor"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/gamma/wealthonboarding/config"

	"github.com/pkg/errors"
)

type UserGroupConstraintData struct {
	userGroupConstraintConfig *config.UserGroupConstraintConfig
}

func NewUserGroupConstraintData(userGroupConstraintConfig *config.UserGroupConstraintConfig) *UserGroupConstraintData {
	return &UserGroupConstraintData{userGroupConstraintConfig: userGroupConstraintConfig}
}

func (u *UserGroupConstraintData) IsConstraintData() {}

type UserGroupConstraint struct {
	actorClient     actorPb.ActorClient
	userClient      userPb.UsersClient
	userGroupClient userGroupPb.GroupClient
}

func NewUserGroupConstraint(actorClient actorPb.ActorClient, userClient userPb.UsersClient, userGroupClient userGroupPb.GroupClient) *UserGroupConstraint {
	return &UserGroupConstraint{actorClient: actorClient, userClient: userClient, userGroupClient: userGroupClient}
}

func (u *UserGroupConstraint) Evaluate(ctx context.Context, constraintData ConstraintData, commonConstraintData *CommonConstraintData) (bool, error) {
	actorId := commonConstraintData.ActorId
	userGroups := commonConstraintData.UserGroups
	userGroupConstraintData, ok := constraintData.(*UserGroupConstraintData)
	if !ok {
		return false, fmt.Errorf("invalid constraint data passed: %t", constraintData)
	}
	if userGroups == nil && len(userGroups) == 0 {
		if actorId == "" {
			return false, errors.New("user group or actor id mandatory")
		}
		actorResp, err := u.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
		if te := epifigrpc.RPCError(actorResp, err); te != nil {
			return false, errors.Wrap(te, fmt.Sprintf("failed to fetch actor: %s", actorId))
		}

		userResp, err := u.userClient.GetUser(ctx, &userPb.GetUserRequest{
			Identifier: &userPb.GetUserRequest_Id{
				Id: actorResp.GetActor().GetEntityId(),
			},
		})
		if te := epifigrpc.RPCError(userResp, err); te != nil {
			return false, errors.Wrap(te, fmt.Sprintf("failed to fetch user: %s", actorResp.GetActor().GetEntityId()))
		}

		userGrpRes, userGrpErr := u.userGroupClient.GetGroupsMappedToEmail(ctx,
			&userGroupPb.GetGroupsMappedToEmailRequest{Email: userResp.GetUser().GetProfile().GetEmail()})
		if err := epifigrpc.RPCError(userGrpRes, userGrpErr); err != nil {
			return false, errors.Wrap(err, fmt.Sprintf("failed to fetch user group: %s", actorResp.GetActor().GetEntityId()))
		}
		userGroups = userGrpRes.GetGroups()
	}

	for _, g := range userGroups {
		if g.String() == userGroupConstraintData.userGroupConstraintConfig.UserGroup {
			return true, nil
		}
	}
	return false, nil
}
