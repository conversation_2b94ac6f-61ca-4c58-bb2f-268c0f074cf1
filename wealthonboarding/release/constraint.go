package release

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
)

type ConstraintData interface {
	IsConstraintData()
}

type Constraint interface {
	Evaluate(ctx context.Context, constraintData ConstraintData, commonConstraintData *CommonConstraintData) (bool, error)
}

type CommonConstraintData struct {
	ActorId    string
	Feature    woPb.WealthOnboardingFeature
	UserGroups []commontypes.UserGroup
}

// nolint:unused,deadcode
func NewCommonConstraintData(feature woPb.WealthOnboardingFeature) *CommonConstraintData {
	return &CommonConstraintData{Feature: feature}
}

// nolint:unused,deadcode
func (c *CommonConstraintData) WithActorId(actorId string) *CommonConstraintData {
	c.ActorId = actorId
	return c
}

// nolint:unused,deadcode
func (c *CommonConstraintData) WithUserGroup(userGroups []commontypes.UserGroup) *CommonConstraintData {
	c.UserGroups = userGroups
	return c
}
