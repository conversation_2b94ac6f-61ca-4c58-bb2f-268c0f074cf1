package release

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/wealthonboarding/config"
)

type AppPlatformConstraintData struct {
	appPlatformConstraintConfig *config.AppPlatformConstraintConfig
}

func NewAppPlatformConstraintData(appPlatformConstraintConfig *config.AppPlatformConstraintConfig) *AppPlatformConstraintData {
	return &AppPlatformConstraintData{appPlatformConstraintConfig: appPlatformConstraintConfig}
}

func (a *AppPlatformConstraintData) IsConstraintData() {}

type AppPlatformConstraint struct {
}

func NewAppPlatformConstraint() *AppPlatformConstraint {
	return &AppPlatformConstraint{}
}

func (a *AppPlatformConstraint) Evaluate(ctx context.Context, constraintData ConstraintData, _ *CommonConstraintData) (bool, error) {
	appPlatformConstraintData, ok := constraintData.(*AppPlatformConstraintData)
	if !ok {
		return false, fmt.Errorf("invalid constraint data passed: %t", constraintData)
	}
	platform := epificontext.AppPlatformFromContext(ctx)
	switch platform {
	case commontypes.Platform_ANDROID:
		if appPlatformConstraintData.appPlatformConstraintConfig.Android {
			logger.Debug(ctx, "AppVersionConstraint evaluation returned true")
			return true, nil
		}
	case commontypes.Platform_IOS:
		if appPlatformConstraintData.appPlatformConstraintConfig.Ios {
			logger.Debug(ctx, "AppVersionConstraint evaluation returned true")
			return true, nil
		}
	default:
		return false, fmt.Errorf("unhandled platform: %v", platform.String())
	}
	return false, nil
}
