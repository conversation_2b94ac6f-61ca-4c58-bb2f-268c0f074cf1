package release

import (
	"context"
	"fmt"
	"hash/fnv"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/wealthonboarding/config"

	"github.com/pkg/errors"
)

type StickyPercentageConstraintData struct {
	stickyPercentageConstraintConfig *config.StickyPercentageConstraintConfig
}

func NewStickyPercentageConstraintData(stickyPercentageConstraintConfig *config.StickyPercentageConstraintConfig) *StickyPercentageConstraintData {
	return &StickyPercentageConstraintData{stickyPercentageConstraintConfig: stickyPercentageConstraintConfig}
}

func (s *StickyPercentageConstraintData) IsConstraintData() {}

type StickyPercentageConstraint struct {
}

func NewStickyPercentageConstraint() *StickyPercentageConstraint {
	return &StickyPercentageConstraint{}
}

func (s *StickyPercentageConstraint) Evaluate(ctx context.Context, constraintData ConstraintData, commonConstraintData *CommonConstraintData) (bool, error) {
	stickyPercentageConstraintData, ok := constraintData.(*StickyPercentageConstraintData)
	if !ok {
		return false, fmt.Errorf("invalid constraint data passed: %t", constraintData)
	}
	num, err := GetHashNum(fmt.Sprintf("%v%v", commonConstraintData.Feature, commonConstraintData.ActorId))
	if err != nil {
		return false, errors.Wrap(err, "error while generating hash for actor+feature string")
	}
	if num%100 < uint64(stickyPercentageConstraintData.stickyPercentageConstraintConfig.RolloutPercentage) {
		logger.Debug(ctx, "StickyPercentageConstraint evaluation returned true")
		return true, nil
	}
	return false, nil
}

func GetHashNum(inputString string) (uint64, error) {
	h := fnv.New64()
	if _, err := h.Write([]byte(inputString)); err != nil {
		return 0, errors.New("error while generating uint64 hash")
	}
	return h.Sum64(), nil
}
