package orchestrator

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	employmentPb "github.com/epifi/gamma/api/employment"
	types "github.com/epifi/gamma/api/typesv2"
	wealthVendorPb "github.com/epifi/gamma/api/vendors/wealth"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	userPb "github.com/epifi/gamma/api/wealthonboarding/user"
	"github.com/epifi/gamma/wealthonboarding/helper"
)

func getWealthUser(od *woPb.OnboardingDetails) *userPb.User {
	user := &userPb.User{
		ActorId: od.GetActorId(),
	}

	// populate scheme code
	switch od.GetOnboardingType() {
	case woPb.OnboardingType_ONBOARDING_TYPE_WEALTH:
		user.SchemeCode = userPb.SchemeCode_SCHEME_CODE_CONNECTED_ACCOUNT
	case woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT:
		user.SchemeCode = userPb.SchemeCode_SCHEME_CODE_CONNECTED_ACCOUNT_PLUS_INVESTMENT
	default:
	}

	// populate metadata
	if od.GetMetadata().GetCustomerIpAddress() != "" {
		if user.GetMetadata() == nil {
			user.Metadata = &userPb.UserMetadata{}
		}
		user.GetMetadata().CustomerIpAddress = od.GetMetadata().GetCustomerIpAddress()
	}

	// populate kyc data
	if od.GetMetadata().GetKraData().GetStatusData() != nil {
		if user.GetKycData() == nil {
			user.KycData = &userPb.KycData{}
		}
		user.GetKycData().StatusData = od.GetMetadata().GetKraData().GetStatusData()
	}

	// populate personal details
	populatePersonalDetails(user, od)

	return user
}

// nolint: funlen
func populatePersonalDetails(user *userPb.User, od *woPb.OnboardingDetails) {
	if user.GetPersonalDetails() == nil {
		user.PersonalDetails = &userPb.PersonalDetails{}
	}

	// populate from onboarding personal details
	user.GetPersonalDetails().Name = od.GetMetadata().GetPersonalDetails().GetName()
	user.GetPersonalDetails().Gender = od.GetMetadata().GetPersonalDetails().GetGender()
	user.GetPersonalDetails().PhoneNumber = od.GetMetadata().GetPersonalDetails().GetPhoneNumber()
	user.GetPersonalDetails().Email = od.GetMetadata().GetPersonalDetails().GetEmail()
	user.GetPersonalDetails().Dob = od.GetMetadata().GetPersonalDetails().GetDob()
	user.GetPersonalDetails().Photo = od.GetMetadata().GetPersonalDetails().GetPhoto()
	user.GetPersonalDetails().MaritalStatus = od.GetMetadata().GetPersonalDetails().GetMaritalStatus()
	user.GetPersonalDetails().FatherName = od.GetMetadata().GetPersonalDetails().GetFatherName()
	user.GetPersonalDetails().MotherName = od.GetMetadata().GetPersonalDetails().GetMotherName()
	user.GetPersonalDetails().Nominees = od.GetMetadata().GetPersonalDetails().GetNominees()
	user.GetPersonalDetails().PoliticallyExposedStatus = od.GetMetadata().GetPersonalDetails().GetPoliticallyExposedStatus()
	user.GetPersonalDetails().Signature = od.GetMetadata().GetPersonalDetails().GetSignature()
	user.GetPersonalDetails().Occupation = od.GetMetadata().GetPersonalDetails().GetOccupation()

	// populate from onboarding metadata
	user.GetPersonalDetails().PanDetails = od.GetMetadata().GetPanDetails()
	user.GetPersonalDetails().BankDetails = od.GetMetadata().GetBankDetails()

	// populate from KRADownloadData or Onboarding personal details
	// If user has existing KYC records, verify if user is Indian otherwise populate nationality as others
	// Else assume user has no KYC records and populate nationality as declared by user when accepting T&C
	switch {
	case helper.IsIndianAsPerKraPanDetails(od.GetMetadata().GetKraData().GetDownloadedData().GetPanDetails()):
		user.GetPersonalDetails().Nationality = types.Nationality_NATIONALITY_INDIAN
	case od.GetMetadata().GetKraData().GetDownloadedData().GetPanDetails().GetNationality() != types.Nationality_NATIONALITY_UNSPECIFIED:
		user.GetPersonalDetails().Nationality = od.GetMetadata().GetKraData().GetDownloadedData().GetPanDetails().GetNationality()
	default:
		user.GetPersonalDetails().Nationality = od.GetMetadata().GetPersonalDetails().GetNationality()
	}
	if od.GetMetadata().GetKraData().GetDownloadedData().GetPanDetails().GetResStatus() != types.ResidentialStatus_RESIDENTIAL_STATUS_UNSPECIFIED {
		user.GetPersonalDetails().ResidentialStatus = od.GetMetadata().GetKraData().GetDownloadedData().GetPanDetails().GetResStatus()
	} else {
		user.GetPersonalDetails().ResidentialStatus = od.GetMetadata().GetPersonalDetails().GetResidentialStatus()
	}
	if od.GetMetadata().GetKraData().GetDownloadedData().GetPanDetails().GetIncomeSlab() != types.IncomeSlab_INCOME_SLAB_UNSPECIFIED {
		user.GetPersonalDetails().IncomeSlab = od.GetMetadata().GetKraData().GetDownloadedData().GetPanDetails().GetIncomeSlab()
	} else {
		user.GetPersonalDetails().IncomeSlab = od.GetMetadata().GetPersonalDetails().GetIncomeSlab()
	}

	if od.GetMetadata().GetIsFreshKra() {
		// populate permanent address from CkycDownloadData or digilocker data
		if od.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData() != nil {
			user.GetPersonalDetails().PermanentAddress = od.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData().GetAddress()
		} else {
			user.GetPersonalDetails().PermanentAddress = od.GetMetadata().GetCkycData().GetDownloadData().GetPersonalDetails().GetPermanentAddress()
		}
	} else {
		// populate permanent address from KRADownloadData
		user.GetPersonalDetails().PermanentAddress = od.GetMetadata().GetKraData().GetDownloadedData().GetPanDetails().GetPermAddress()
	}

	// populate employment data
	populateEmploymentData(user, od)

	if od.GetMetadata().GetPersonalDetails().GetNomineeDeclarationDetails().GetChoice() != commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
		user.GetPersonalDetails().NomineeDeclarationDetails = od.GetMetadata().GetPersonalDetails().GetNomineeDeclarationDetails()
	}
}

func populateEmploymentData(user *userPb.User, od *woPb.OnboardingDetails) {
	empData := od.GetMetadata().GetEmploymentData()
	if empData.GetEmploymentType() != employmentPb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED {
		return
	}
	// fetch employment type from kra occupation
	empData = &woPb.EmploymentData{}
	switch od.GetMetadata().GetKraData().GetDownloadedData().GetPanDetails().GetOcc() {
	case wealthVendorPb.KraOccupation_KRA_OCCUPATION_PRIVATE_SECTOR_SERVICE, wealthVendorPb.KraOccupation_KRA_OCCUPATION_PUBLIC_SECTOR,
		wealthVendorPb.KraOccupation_KRA_OCCUPATION_GOVERNMENT_SERVICE, wealthVendorPb.KraOccupation_KRA_OCCUPATION_PUBLIC_SECTOR_OR_GOVERNMENT_SERVICE:
		empData.EmploymentType = employmentPb.EmploymentType_SALARIED
	case wealthVendorPb.KraOccupation_KRA_OCCUPATION_HOUSEWIFE:
		empData.EmploymentType = employmentPb.EmploymentType_OTHERS
	default:
		empData.EmploymentType = employmentPb.EmploymentType_SELF_EMPLOYED
	}
	user.GetPersonalDetails().EmploymentData = empData
}
