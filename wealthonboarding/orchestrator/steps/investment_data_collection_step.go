package steps

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/employment"
	types "github.com/epifi/gamma/api/typesv2"
	cvlVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/cvl"
	wVendorPb "github.com/epifi/gamma/api/vendors/wealth"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/dao"
	woErr "github.com/epifi/gamma/wealthonboarding/errors"
	"github.com/epifi/gamma/wealthonboarding/helper"
)

type InvestmentDataCollectionStep struct {
	employmentClient      employment.EmploymentClient
	currentStep           woPb.OnboardingStep
	uploadKraDocketStep   woPb.OnboardingStep
	downloadKraDocketStep woPb.OnboardingStep
	onbDetailsDao         dao.OnboardingDetailsDao
	commonHelper          *helper.CommonHelper
	cvlVgClient           cvlVgPb.CvlClient
	conf                  *config.Config
}

func NewPreInvestmentDataCollectionStep(
	employmentClient employment.EmploymentClient,
	onbDetailsDao dao.OnboardingDetailsDao,
	commonHelper *helper.CommonHelper,
	cvlVgClient cvlVgPb.CvlClient,
	conf *config.Config) *InvestmentDataCollectionStep {
	return &InvestmentDataCollectionStep{
		employmentClient:      employmentClient,
		currentStep:           woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION_FOR_INVESTMENT,
		uploadKraDocketStep:   woPb.OnboardingStep_ONBOARDING_STEP_UPLOAD_DOCKET,
		downloadKraDocketStep: woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC,
		onbDetailsDao:         onbDetailsDao,
		commonHelper:          commonHelper,
		cvlVgClient:           cvlVgClient,
		conf:                  conf,
	}
}

func (d *InvestmentDataCollectionStep) Perform(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	wDetails, wErr := d.onbDetailsDao.GetByActorIdAndOnbType(ctx, details.GetActorId(), woPb.OnboardingType_ONBOARDING_TYPE_WEALTH)
	if wErr != nil {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, d.currentStep, d.downloadKraDocketStep), fmt.Errorf("failed to fetch wealth details for actor_id: %s, err: %v, %w",
			details.GetActorId(), wErr.Error(), queue.ErrTransient)
	}

	if wDetails.GetStatus() != woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, d.currentStep, d.downloadKraDocketStep), fmt.Errorf("wealth onb is not completed for actor_id: %s, current_step: %v, current_onb_status: %v", details.GetActorId(), wDetails.GetCurrentStep(), wDetails.GetStatus())
	}

	pan := wDetails.GetMetadata().GetPanDetails().GetId()
	psRes, psResErr := d.cvlVgClient.GetPanStatus(ctx, &cvlVgPb.GetPanStatusRequest{
		Header:    &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_CVLKRA},
		PanNumber: pan,
	})
	if te := epifigrpc.IsRPCErrorWithDowntime(psRes, psResErr); te != nil {
		logger.Error(ctx, "error while calling GetPanStatus", zap.Error(te))
		if errors.Is(te, epifierrors.ErrDowntimeExpected) {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, d.currentStep, d.downloadKraDocketStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(te), errors.Wrap(te, "error while calling GetPanStatus, downtime expected")
		}
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, d.currentStep, d.downloadKraDocketStep), errors.Wrap(te, "error while calling GetPanStatus")
	}
	nextStep, nextStepErr := getNextStepBasedOnKraData(ctx, psRes)
	if nextStepErr != nil {
		if errors.Is(nextStepErr, woErr.ErrKycNotValidatedYetForAadhaarPoa) || errors.Is(nextStepErr, woErr.ErrKycNotValidatedYet) {
			res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, d.currentStep, nextStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_KYC_NOT_VALIDATED_YET)
			res.Deeplink = helper.GetKycNotValidatedDeeplink(wDetails, d.conf.InvestmentsFaqCategoryId)
			return res, nil
		}
		if errors.Is(nextStepErr, woErr.ErrKYCOnHoldByKRA) {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, d.currentStep, nextStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_KRA_STATUS_HOLD), nextStepErr
		}
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, d.currentStep, nextStep), nextStepErr
	}

	// currently, we cannot process a user whose KYC was determined to be existing with KRA while completing "wealth" onboarding
	// but is now not existing as per the latest KYC status from KRA.
	// Reason: for these users we may not have all the data to create the KYC docket.
	if !wDetails.GetMetadata().GetIsFreshKra() && nextStep == woPb.OnboardingStep_ONBOARDING_STEP_UPLOAD_DOCKET {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, d.currentStep, nextStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSUPPORTED_KYC_STATUS_FROM_KRA), nil
	}
	if d.validateMandatoryDetails(details) {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, d.currentStep, nextStep), nil
	}

	d.populateWealthDetails(details, wDetails)
	if err := d.populateBankAccountDetails(ctx, details); err != nil {
		if errors.Is(err, epifierrors.ErrDowntimeExpected) {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, d.currentStep, nextStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(err), errors.Wrap(err, "error while fetching bank account details")
		}
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, d.currentStep, nextStep), errors.Wrap(err, "error while fetching bank account details")
	}
	empRes, empErr := d.employmentClient.GetEmploymentDetailsForActor(ctx, &employment.GetEmploymentDetailsForActorRequest{
		ActorId: details.GetActorId(),
	})
	if te := epifigrpc.RPCError(empRes, empErr); te != nil {
		if empRes.GetStatus().GetCode() != rpc.StatusRecordNotFound().GetCode() {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, d.currentStep, nextStep), fmt.Errorf("failed to fetch employment info for actor_id: %s, err: %v, %w",
				details.GetActorId(), te.Error(), queue.ErrTransient)
		}
	}
	d.populateEmploymentDetails(details, empRes.GetEmploymentType())
	if d.validateMandatoryDetails(details) {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, d.currentStep, nextStep), nil
	}

	return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, d.currentStep, nextStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_MANDATORY_DETAILS_MISSING_IN_FI), nil
}

func (d *InvestmentDataCollectionStep) populateEmploymentDetails(details *woPb.OnboardingDetails, empType types.EmploymentType) {
	if details.GetMetadata() == nil {
		details.Metadata = &woPb.OnboardingMetadata{}
	}
	if details.GetMetadata().GetEmploymentData() == nil {
		details.GetMetadata().EmploymentData = &woPb.EmploymentData{}
	}
	// TODO(ismail): switch to types.Employment when step metadata PR is merged
	details.GetMetadata().GetEmploymentData().EmploymentType = employment.EmploymentType(empType)
}

func (d *InvestmentDataCollectionStep) populateWealthDetails(preInvestmentDetails *woPb.OnboardingDetails, wDetails *woPb.OnboardingDetails) {
	if preInvestmentDetails.GetMetadata() == nil {
		preInvestmentDetails.Metadata = &woPb.OnboardingMetadata{}
	}
	preInvestmentDetails.GetMetadata().IsFreshKra = wDetails.GetMetadata().GetIsFreshKra()
	preInvestmentDetails.GetMetadata().PersonalDetails = wDetails.GetMetadata().GetPersonalDetails()
	preInvestmentDetails.GetMetadata().KraData = wDetails.GetMetadata().GetKraData()
	preInvestmentDetails.GetMetadata().LivenessData = wDetails.GetMetadata().GetLivenessData()
	preInvestmentDetails.GetMetadata().KraDocketInfo = wDetails.GetMetadata().GetKraDocketInfo()
	preInvestmentDetails.GetMetadata().PanDetails = wDetails.GetMetadata().GetPanDetails()
	preInvestmentDetails.GetMetadata().CkycData = wDetails.GetMetadata().GetCkycData()
	preInvestmentDetails.GetMetadata().BankDetails = wDetails.GetMetadata().GetBankDetails()
	preInvestmentDetails.GetMetadata().DigilockerData = wDetails.GetMetadata().GetDigilockerData()
	preInvestmentDetails.GetMetadata().PoaDetails = wDetails.GetMetadata().GetPoaDetails()
	preInvestmentDetails.GetMetadata().NsdlData = wDetails.GetMetadata().GetNsdlData()
}

func (d *InvestmentDataCollectionStep) validateMandatoryDetails(details *woPb.OnboardingDetails) bool {
	// checking only for valid bank account details
	// employment data can be populated in later stages
	bankDetails := details.GetMetadata().GetBankDetails()
	return bankDetails != nil
}

func (d *InvestmentDataCollectionStep) populateBankAccountDetails(ctx context.Context, details *woPb.OnboardingDetails) error {
	if details.GetMetadata().GetBankDetails() != nil {
		logger.Info(ctx, "bank details are already populated")
		return nil
	}
	details.GetMetadata().BankDetails = &woPb.BankDetails{}
	// fetch bank details from federal
	logger.Info(ctx, "populating bank account details for the user")
	bankDetails, err := d.commonHelper.FetchBankAccountDetails(ctx, details.GetActorId())
	if err != nil {
		return errors.Wrap(err, "error while fetching bank details in pre investment")
	}
	// store bank details
	details.GetMetadata().BankDetails = bankDetails
	return nil
}

// getNextStepBasedOnKraData returns the next step for the user i.e., Upload or Download based on current KRA KYC status
func getNextStepBasedOnKraData(ctx context.Context, psRes *cvlVgPb.GetPanStatusResponse) (woPb.OnboardingStep, error) {
	isAadhaarPOA := psRes.GetPanEnquiry().GetCorAddProof() == wVendorPb.KraAddressProof_KRA_ADDRESS_PROOF_AADHAAR ||
		psRes.GetPanEnquiry().GetPerAddProof() == wVendorPb.KraAddressProof_KRA_ADDRESS_PROOF_AADHAAR
	kycStatus := psRes.GetPanEnquiry().GetStatus()
	switch kycStatus {
	case wVendorPb.KraStatus_KRA_STATUS_NOT_AVAILABLE,
		wVendorPb.KraStatus_KRA_STATUS_EXISTING_KYC_SUBMITTED,
		wVendorPb.KraStatus_KRA_STATUS_EXISTING_KYC_VERIFIED,
		wVendorPb.KraStatus_KRA_STATUS_EXISTING_KYC_HOLD,
		wVendorPb.KraStatus_KRA_STATUS_KYC_REGISTERED_WITH_CVLMF,
		wVendorPb.KraStatus_KRA_STATUS_DEACTIVATED,
		wVendorPb.KraStatus_KRA_STATUS_REJECTED,
		wVendorPb.KraStatus_KRA_STATUS_EXISTING_KYC_REJECTED:
		return woPb.OnboardingStep_ONBOARDING_STEP_UPLOAD_DOCKET, nil
	case wVendorPb.KraStatus_KRA_STATUS_KRA_VERIFIED:
		kycRecordCreatedAt := psRes.GetPanEnquiry().GetCreatedDate()
		// Note: If KYC status is not available with any KRA, created at timestamp might not be valid, hence this check should only be done when KRA has some records for user
		if !kycRecordCreatedAt.IsValid() {
			return woPb.OnboardingStep_ONBOARDING_STEP_UNSPECIFIED, errors.New("invalid kyc created timestamp in kyc record")
		}
		if isAadhaarPOA {
			return woPb.OnboardingStep_ONBOARDING_STEP_UNSPECIFIED, errors.Wrap(woErr.ErrKycNotValidatedYetForAadhaarPoa, fmt.Sprintf("KYC status = %s with Aadhaar as POA even after CVL validation completed", kycStatus.String()))
		} else {
			return woPb.OnboardingStep_ONBOARDING_STEP_UNSPECIFIED, errors.Wrap(woErr.ErrKycNotValidatedYet, fmt.Sprintf("KYC status = %s with Non Aadhar POA even after CVL validation completed", kycStatus.String()))
		}
	case wVendorPb.KraStatus_KRA_STATUS_VALIDATED:
		return woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC, nil
	case wVendorPb.KraStatus_KRA_STATUS_SUBMITTED:
		// date check of last updated at of KRA data
		lastUpdateDate := psRes.GetPanEnquiry().GetLastUpdateDate()
		if lastUpdateDate.AsTime().Add(StatusSubmittedAllowedDuration).After(time.Now()) {
			return woPb.OnboardingStep_ONBOARDING_STEP_UNSPECIFIED, errors.Wrap(woErr.ErrKycNotValidatedYet, fmt.Sprintf("KYC status = %s", kycStatus.String()))
		} else {
			logger.Info(ctx, fmt.Sprintf("kra submitted more than %s ago, need to upload kra data", StatusSubmittedAllowedDuration.String()))
			return woPb.OnboardingStep_ONBOARDING_STEP_UPLOAD_DOCKET, nil
		}
	case wVendorPb.KraStatus_KRA_STATUS_NOT_CHECKED_WITH_MULTIPLE_KRA,
		wVendorPb.KraStatus_KRA_STATUS_NOT_CHECKED_WITH_RESPECTIVE_KRA:
		// this is a transient status received from vendor when other KRA systems are down
		// we need to fetch the status after some time in such cases
		return woPb.OnboardingStep_ONBOARDING_STEP_UNSPECIFIED, errors.New("transient invalid KRA status from vendor")
	case wVendorPb.KraStatus_KRA_STATUS_HOLD:
		return woPb.OnboardingStep_ONBOARDING_STEP_UNSPECIFIED, woErr.ErrKYCOnHoldByKRA
	default:
		return woPb.OnboardingStep_ONBOARDING_STEP_UNSPECIFIED, fmt.Errorf("unhandled kra status %v", kycStatus)
	}
}
