package steps

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/investment/wealthonboarding"
	ncPb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	nsdlVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/nsdl"
	wealthVendorPb "github.com/epifi/gamma/api/vendors/wealth"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	gammanames "github.com/epifi/gamma/pkg/names"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/helper"
	"github.com/epifi/gamma/wealthonboarding/release"
)

const (
	PanInoperativeCustomerMsgTitle = "Uh oh! Your Aadhaar is not linked with PAN"
	PanInoperativeCustomerMsgDesc  = "No worries! Just link it from ^^https://eportal.incometax.gov.in/iec/foservices/#/pre-login/bl-link-aadhaar^^here^^ and retry once linking is confirmed"

	PanDobScreenBottomInfoText = "By continuing, Your PAN & Date of Birth details will be used to fetch your CKYC records & create your wealth profile"
	InvalidPanText             = "Unable to find this PAN number"
	PanNameMismatchText        = "Name doesn't match PAN records"

	SomethingWentWrongTitle       = "Uh oh! Our server couldn't load the page temporarily."
	SomethingWentWrongDescription = "Please retry again"

	nameDoesNotMatchPanFailureMsg = "Name doesn't match PAN records"
	dobDoesNotMatchPanFailureMsg  = "Date doesn't match PAN records"
)

type VerifyPanStep struct {
	nsdlVgClient      nsdlVgPb.NsdlClient
	currentStep       woPb.OnboardingStep
	nextStepOnSuccess woPb.OnboardingStep
	conf              *config.Config
	ncClient          ncPb.UNNameCheckClient
	releaseEvaluator  release.IEvaluator
}

func NewVerifyPanStep(nsdlVgClient nsdlVgPb.NsdlClient, conf *config.Config, ncClient ncPb.UNNameCheckClient, releaseEvaluator release.IEvaluator) *VerifyPanStep {
	return &VerifyPanStep{
		nsdlVgClient:      nsdlVgClient,
		currentStep:       woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
		nextStepOnSuccess: woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
		conf:              conf,
		ncClient:          ncClient,
		releaseEvaluator:  releaseEvaluator,
	}
}

func (v *VerifyPanStep) Perform(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	isNewNsdlPanChangeFlowEnabled, updateAppDl, err := v.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_NSDL_PAN_INQUIRY_WITH_NAME).WithActorId(details.GetActorId()))
	if err != nil {
		logger.Error(ctx, "failed to evaluate if new nsdl pan inquiry flow is enabled", zap.Error(err))
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, v.currentStep, v.nextStepOnSuccess),
			fmt.Errorf("failed to evaluate if new nsdl pan inquiry flow is enabled : %w", err)
	}
	if updateAppDl != nil {
		updateAppDl.GetWealthOnboardingStatusScreenOptions().Title = "Update Fi app to start investing"
		updateAppDl.GetWealthOnboardingStatusScreenOptions().Description = "For a smoother and better investment experience on Fi, please update the app"
		res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, v.currentStep, v.nextStepOnSuccess)
		res.Deeplink = updateAppDl
		return res, nil
	}
	// if new flow is enabled (post nsdl v4 api change)
	if isNewNsdlPanChangeFlowEnabled {
		logger.Info(ctx, "new nsdl flow with name triggered")
		return v.newFlowPostNsdlApiChange(ctx, details)
	}
	logger.Info(ctx, "old nsdl flow triggered")
	isNewPanDobEditScreenEligible, _, err := v.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_PAN_DOB_SUBMIT_SCREEN).WithActorId(details.GetActorId()))
	if err != nil {
		logger.Error(ctx, "failed to evaluate if new pan dob edit flow is enabled", zap.Error(err))
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, v.currentStep, v.nextStepOnSuccess),
			fmt.Errorf("failed to evaluate if new pan dob edit flow is enabled : %w", err)
	}
	if isNewPanDobEditScreenEligible {
		if !hasUserSubmittedPanDob(details) {
			// trigger pan dob screen for user
			return v.handleNewPanDobEditFlow(details, false)
		} else {
			// update user submitted pan dob in onb details metadata
			v.updateUserSubmittedPanDobInMetadata(ctx, details, false)
		}
	}
	piRes, piResErr := v.nsdlVgClient.NsdlPanInquiry(ctx, &nsdlVgPb.NsdlPanInquiryRequest{
		Header:    &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_NSDL},
		PanNumber: details.GetMetadata().GetPanDetails().GetId(),
	})
	if err := epifigrpc.IsRPCErrorWithDowntime(piRes, piResErr); err != nil {
		if errors.Is(err, epifierrors.ErrDowntimeExpected) {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, v.currentStep, v.nextStepOnSuccess).
				WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).
				WithDowntimeDeeplink(err), errors.Wrap(piResErr, "error while calling NsdlPanInquiry")
		}
		dl := v.getPanDobValidateBeFailureDeeplink(details, isNewPanDobEditScreenEligible, false)
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, v.currentStep, v.nextStepOnSuccess).
			WithDeeplink(dl), errors.Wrap(err, "error while calling NsdlPanInquiry")
	}
	logger.Info(ctx, "nsdl call successful", zap.String("status", piRes.GetPanStatus().String()), zap.String("aadhaar_status", piRes.GetAadhaarStatus().String()))
	v.updateNsdlData(details, piRes)

	if piRes.GetPanStatus() == wealthVendorPb.NsdlPanStatus_NSDL_PAN_STATUS_TYPE_EXISTING_AND_VALID {
		// as perincome tax department, pan which is not linked with aadhaar will become inoperative after 31 march 2023
		if piRes.GetAadhaarStatus() != wealthVendorPb.NsdlAadhaarStatus_NSDL_AADHAAR_STATUS_TYPE_SUCCESSFUL {
			if v.getPanDobAttemptCount(details) >= v.conf.MaxPanDobSubmitAttempts {
				logger.Error(ctx, fmt.Sprintf("user has exceeded the max pan-dob submit attempts : %d", v.getPanDobAttemptCount(details)))
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, v.currentStep,
					v.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_NSDL_PAN_VERIFICATION_FAILED), nil
			} else {
				return v.getAadhaarSeedingIssueResponse(details, isNewPanDobEditScreenEligible), nil
			}
		}
		return v.performNameMatch(ctx, details, piRes, isNewPanDobEditScreenEligible)
	}
	logger.Info(ctx, "invalid pan as per nsdl", zap.String("status", piRes.GetPanStatus().String()))
	if isNewPanDobEditScreenEligible {
		if v.getPanDobAttemptCount(details) >= v.conf.MaxPanDobSubmitAttempts {
			logger.Error(ctx, fmt.Sprintf("user has exceeded the max pan-dob submit attempts : %d", v.getPanDobAttemptCount(details)))
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, v.currentStep,
				v.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_NSDL_PAN_VERIFICATION_FAILED), nil
		} else {
			dl := v.invalidPanFailureDeeplink(details, false)
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, v.currentStep, v.nextStepOnSuccess).
				WithDeeplink(dl), nil
		}
	}
	return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, v.currentStep, v.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_NSDL_PAN_VERIFICATION_FAILED), nil
}

func (v *VerifyPanStep) isNameMatchPass(result *gammanames.MatchResult, inhouseResp *ncPb.NameMatchRespone) bool {
	if inhouseResp != nil {
		return inhouseResp.GetDecision() == ncPb.NameMatchDecision_NAMEMATCH_PASS && inhouseResp.GetRiskMatch() == 0 && inhouseResp.GetScore() >= v.conf.InhouseNameMatchThreshold
	}
	return result.WeightedSumScore >= v.conf.MinNameMatchScore
}

func (v *VerifyPanStep) populateNameMatchInfo(result *gammanames.MatchResult, od *woPb.OnboardingDetails, inhouseResp *ncPb.NameMatchRespone) {
	if od.GetMetadata().GetNameMatchInfo() == nil {
		od.GetMetadata().NameMatchInfo = &woPb.NameMatchInfo{}
	}
	nmi := od.GetMetadata().GetNameMatchInfo()
	nmi.WeightedSumScore = result.WeightedSumScore
	for _, score := range result.Scores {
		s := &woPb.NameMatchInfo_MatchScore{
			Criteria: v.getNameMatchCriteria(score.Criteria),
			Weight:   int64(score.Weight),
			Score:    score.Score,
		}
		nmi.Scores = append(nmi.Scores, s)
	}
	if inhouseResp != nil {
		nmi.InhouseResult = inhouseResp.GetDecision() == ncPb.NameMatchDecision_NAMEMATCH_PASS
		nmi.InhouseScore = inhouseResp.GetScore()
		nmi.InhouseRiskMatch = inhouseResp.GetRiskMatch()
		nmi.InhouseThreshold = v.conf.InhouseNameMatchThreshold
	}
}

func (v *VerifyPanStep) getNameMatchCriteria(criteria gammanames.MatchingCriteria) woPb.NameMatchInfo_MatchScore_MatchCriteria {
	switch criteria {
	default:
		return woPb.NameMatchInfo_MatchScore_MATCH_CRITERIA_UNSPECIFIED
	case gammanames.FULL_NAME_MATCH:
		return woPb.NameMatchInfo_MatchScore_MATCH_CRITERIA_FULL_NAME_MATCH
	case gammanames.FIRST_LAST_MATCH:
		return woPb.NameMatchInfo_MatchScore_MATCH_CRITERIA_FIRST_LAST_MATCH
	case gammanames.REVERSE_FIRST_LAST_MATCH:
		return woPb.NameMatchInfo_MatchScore_MATCH_CRITERIA_REVERSE_FIRST_LAST_MATCH
	case gammanames.SHORT_NAME_MATCH:
		return woPb.NameMatchInfo_MatchScore_MATCH_CRITERIA_SHORT_NAME_MATCH
	case gammanames.CONCAT_MATCH:
		return woPb.NameMatchInfo_MatchScore_MATCH_CRITERIA_CONCAT_MATCH
	}
}

func (v *VerifyPanStep) updateNsdlData(details *woPb.OnboardingDetails, res *nsdlVgPb.NsdlPanInquiryResponse) {
	if details.GetMetadata().GetNsdlData().GetPanDetails() == nil {
		if details.GetMetadata().GetNsdlData() == nil {
			if details.GetMetadata() == nil {
				details.Metadata = &woPb.OnboardingMetadata{}
			}
			details.GetMetadata().NsdlData = &woPb.NsdlData{}
		}
		details.GetMetadata().GetNsdlData().PanDetails = &woPb.NsdlData_NsdlPanDetails{}
	}
	pd := details.GetMetadata().GetNsdlData().GetPanDetails()
	pd.PanStatus = res.GetPanStatus()
	pd.AadhaarStatus = res.GetAadhaarStatus()
	pd.Name = res.GetName()
	pd.LastUpdatedDate = res.GetLastUpdatedDate()
	pd.PanCardName = res.GetPanCardName()
}

func (v *VerifyPanStep) getAadhaarSeedingIssueResponse(details *woPb.OnboardingDetails, isNewPanDobEditScreenEnabled bool) *StepExecutionResponse {
	// return in progress status so that user can link aadhaar and come back to continue onboarding
	res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION, woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_NSDL_PAN_AADHAAR_SEEDING_PENDING)
	screenOptions := &deeplinkPb.Deeplink_WealthOnboardingErrorScreenOptions{
		WealthOnboardingErrorScreenOptions: &deeplinkPb.WealthOnboardingErrorScreenOptions{
			OnboardingAction: deeplinkPb.WealthOnboardingAction_WEALTH_ONBOARDING_ACTION_FAILED,
			Title:            PanInoperativeCustomerMsgTitle,
			Description:      PanInoperativeCustomerMsgDesc,
			IllustrationUrl:  helper.ManualInterventionIllustrationURL,
			Cta: &deeplinkPb.Cta{
				Text: "Okay",
				Deeplink: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_HOME,
				},
			},
		},
	}
	if isNewPanDobEditScreenEnabled {
		screenOptions.WealthOnboardingErrorScreenOptions.Cta = &deeplinkPb.Cta{
			Text:     "Retry",
			Deeplink: v.genPanDobScreenDeeplink(details, "", "", "", false),
		}
	}
	res.Deeplink = &deeplinkPb.Deeplink{
		Screen:        deeplinkPb.Screen_WEALTH_ONBOARDING_ERROR_SCREEN,
		ScreenOptions: screenOptions,
	}
	return res
}

func (v *VerifyPanStep) performNameMatch(ctx context.Context, details *woPb.OnboardingDetails, piRes *nsdlVgPb.NsdlPanInquiryResponse,
	isNewPanDobEditFlowEligible bool) (*StepExecutionResponse, error) {

	// name match with from name on pan
	customerName := details.GetMetadata().GetPersonalDetails().GetName()
	customerNameOnPan := piRes.GetName()
	matchRes, matchErr := gammanames.NameMatchAllCriteria(ctx, customerName, customerNameOnPan)
	if matchErr != nil {
		logger.Error(ctx, "error while performing name match", zap.Error(matchErr))
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, v.currentStep, v.nextStepOnSuccess), errors.Wrap(matchErr, "error while performing name match")
	}
	logger.Info(ctx, fmt.Sprintf("old name match score required: %v got: %v", v.conf.MinNameMatchScore, matchRes.WeightedSumScore))
	// inhouse name match check
	inhouseMatchRes, inhouseErr := v.ncClient.NameMatch(ctx, &ncPb.NameMatchRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IN_HOUSE},
		Name_1: customerNameOnPan.ToString(),
		Name_2: customerName.ToString(),
	})
	if inhouseErr != nil {
		// not returning here since we fallback to names package result in case inhouse throws error
		logger.Error(ctx, "error while performing inhouse name match", zap.Error(inhouseErr))
	}

	v.populateNameMatchInfo(matchRes, details, inhouseMatchRes)
	if v.conf.EnableNameMatch && !v.isNameMatchPass(matchRes, inhouseMatchRes) {
		logger.Error(ctx, "name match score below threshold")
		if isNewPanDobEditFlowEligible {
			if v.getPanDobAttemptCount(details) >= v.conf.MaxPanDobSubmitAttempts {
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED, v.currentStep, v.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_NAME_MATCH_FAILED), errors.Wrap(matchErr, "name match score below threshold")
			} else {
				dl := v.panNameMismatchFailureDeeplink(details, false)
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS,
					v.currentStep, v.nextStepOnSuccess).WithDeeplink(dl), nil
			}
		}
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED, v.currentStep, v.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_NAME_MATCH_FAILED), errors.Wrap(matchErr, "name match score below threshold")
	}
	return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, v.currentStep, v.nextStepOnSuccess), nil
}

func hasUserSubmittedPanDob(details *woPb.OnboardingDetails) bool {
	return details.GetMetadata().GetCustomerProvidedData().GetUserSubmittedPan() != "" &&
		details.GetMetadata().GetCustomerProvidedData().GetUserSubmittedDob() != nil
}

func (v *VerifyPanStep) genPanDobScreenDeeplink(onboardingDetails *woPb.OnboardingDetails, panFailureMsg string, dobFailureMsg string,
	nameFailureMsg string, isNameChangeEnabled bool) *deeplinkPb.Deeplink {
	panNumber := onboardingDetails.GetMetadata().GetPanDetails().GetId()
	dob := onboardingDetails.GetMetadata().GetPersonalDetails().GetDob()
	name := onboardingDetails.GetMetadata().GetPersonalDetails().GetName()
	panDobScreenOptions := &wealthonboarding.WealthOnboardingPanDobScreenOptions{
		Title:                  commontypes.GetPlainStringText("CONFIRM YOUR PAN & DATE OF BIRTH").WithFontColor(colors.ColorNight).WithFontStyle(commontypes.FontStyle_HEADLINE_2),
		PanNumber:              commontypes.GetPlainStringText(panNumber).WithFontColor(colors.ColorNight),
		Dob:                    dob,
		BottomInfoText:         commontypes.GetPlainStringText(PanDobScreenBottomInfoText).WithFontColor(colors.ColorSlate).WithFontStyle(commontypes.FontStyle_BODY_3_PARA),
		IsFullNameInputEnabled: false,
		IsPanEditable:          false,
		IsDobEditable:          false,
		IsNameEditable:         false,
	}
	if isNameChangeEnabled {
		panDobScreenOptions.FullName = commontypes.GetPlainStringText(name.ToString()).WithFontColor(colors.ColorNight)
		panDobScreenOptions.IsFullNameInputEnabled = true
		panDobScreenOptions.IsNameEditable = true
	} else {
		panDobScreenOptions.IsPanEditable = true
		panDobScreenOptions.IsDobEditable = true
	}
	if panFailureMsg != "" {
		panDobScreenOptions.PanFailureMsg = commontypes.GetPlainStringText(panFailureMsg).WithFontColor(colors.ColorErrorRed).WithFontStyle(commontypes.FontStyle_BODY_4)
	}
	if dobFailureMsg != "" {
		panDobScreenOptions.DobFailureMsg = commontypes.GetPlainStringText(dobFailureMsg).WithFontColor(colors.ColorErrorRed).WithFontStyle(commontypes.FontStyle_BODY_4)
	}
	if nameFailureMsg != "" {
		panDobScreenOptions.FullNameFailureMsg = commontypes.GetPlainStringText(nameFailureMsg).WithFontColor(colors.ColorErrorRed).WithFontStyle(commontypes.FontStyle_BODY_4)
	}
	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_WEALTH_ONBOARDING_PAN_DOB_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(panDobScreenOptions),
	}
}

func (v *VerifyPanStep) handleNewPanDobEditFlow(onboardingDetails *woPb.OnboardingDetails, isNameChangeEnabled bool) (*StepExecutionResponse, error) {
	dl := v.genPanDobScreenDeeplink(onboardingDetails, "", "", "", isNameChangeEnabled)
	return GetStepExecutionResponse(onboardingDetails, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, v.currentStep, v.nextStepOnSuccess).
		WithDeeplink(dl), nil
}

func (v *VerifyPanStep) updateUserSubmittedPanDobInMetadata(ctx context.Context, onboardingDetails *woPb.OnboardingDetails, isNameEnabled bool) {
	existingPan := onboardingDetails.GetMetadata().GetPanDetails().GetId()
	userSubmittedPan := onboardingDetails.GetMetadata().GetCustomerProvidedData().GetUserSubmittedPan()

	existingDob := onboardingDetails.GetMetadata().GetPersonalDetails().GetDob()
	userSubmittedDob := onboardingDetails.GetMetadata().GetCustomerProvidedData().GetUserSubmittedDob()

	existingName := onboardingDetails.GetMetadata().GetPersonalDetails().GetName().ToString()
	userSubmittedName := onboardingDetails.GetMetadata().GetCustomerProvidedData().GetUserSubmittedName()

	// user has entered a different pan from what is present in onb details
	if userSubmittedPan != existingPan {
		logger.SecureInfo(ctx, commonvgpb.Vendor_VENDOR_UNSPECIFIED, fmt.Sprintf("user exising pan is %s, user entered new pan %s", existingPan, userSubmittedPan),
			zap.String(logger.ACTOR_ID_V2, onboardingDetails.GetActorId()))
		onboardingDetails.GetMetadata().GetPanDetails().Id = userSubmittedPan
	}

	// user has entered a different dob from what is present in onb details
	if userSubmittedDob != onboardingDetails.GetMetadata().GetPersonalDetails().GetDob() {
		logger.SecureInfo(ctx, commonvgpb.Vendor_VENDOR_UNSPECIFIED, fmt.Sprintf("user exising dob is %s, user entered new dob %s", existingDob.String(), userSubmittedDob.String()),
			zap.String(logger.ACTOR_ID_V2, onboardingDetails.GetActorId()))
		onboardingDetails.GetMetadata().GetPersonalDetails().Dob = userSubmittedDob
	}

	if isNameEnabled && existingName != userSubmittedName {
		logger.SecureInfo(ctx, commonvgpb.Vendor_VENDOR_UNSPECIFIED, fmt.Sprintf("user exising name is %s, user entered new name %s", existingName, userSubmittedName),
			zap.String(logger.ACTOR_ID_V2, onboardingDetails.GetActorId()))
		name := &commontypes.Name{}
		onboardingDetails.GetMetadata().GetPersonalDetails().Name = name.Parse(userSubmittedName)
	}
}

func (v *VerifyPanStep) getPanDobAttemptCount(details *woPb.OnboardingDetails) int {
	return int(details.GetMetadata().GetPanValidateAttemptsCount())
}

func (v *VerifyPanStep) getPanDobValidateBeFailureDeeplink(details *woPb.OnboardingDetails, isNewPanDobEditScreenEnabled bool, isNameEnabled bool) *deeplinkPb.Deeplink {
	screenOptions := &deeplinkPb.Deeplink_WealthOnboardingErrorScreenOptions{
		WealthOnboardingErrorScreenOptions: &deeplinkPb.WealthOnboardingErrorScreenOptions{
			OnboardingAction: deeplinkPb.WealthOnboardingAction_WEALTH_ONBOARDING_ACTION_FAILED,
			Title:            SomethingWentWrongTitle,
			Description:      SomethingWentWrongDescription,
			IllustrationUrl:  helper.SomethingWentWrongIllustrationURL,
			Cta: &deeplinkPb.Cta{
				Text: "Okay",
				Deeplink: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_HOME,
				},
			},
		},
	}
	if isNewPanDobEditScreenEnabled {
		screenOptions.WealthOnboardingErrorScreenOptions.Cta = &deeplinkPb.Cta{
			Text:     "Retry",
			Deeplink: v.genPanDobScreenDeeplink(details, "", "", "", isNameEnabled),
		}
	}
	return &deeplinkPb.Deeplink{
		Screen:        deeplinkPb.Screen_WEALTH_ONBOARDING_ERROR_SCREEN,
		ScreenOptions: screenOptions,
	}
}

func (v *VerifyPanStep) panNameMismatchFailureDeeplink(details *woPb.OnboardingDetails, isNameEnabled bool) *deeplinkPb.Deeplink {
	return v.genPanDobScreenDeeplink(details, PanNameMismatchText, "", "", isNameEnabled)
}
func (v *VerifyPanStep) invalidPanFailureDeeplink(details *woPb.OnboardingDetails, isNameEnabled bool) *deeplinkPb.Deeplink {
	return v.genPanDobScreenDeeplink(details, InvalidPanText, "", "", isNameEnabled)
}

func (v *VerifyPanStep) newFlowPostNsdlApiChange(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	if !hasUserSubmittedPanDobAndNameData(details) {
		// trigger pan dob screen for user
		return v.handleNewPanDobEditFlow(details, true)
	} else {
		// update user submitted pan, dob and name in onb details metadata
		v.updateUserSubmittedPanDobInMetadata(ctx, details, true)
	}
	piRes, piResErr := v.nsdlVgClient.PerformPanInquiry(ctx, &nsdlVgPb.PerformPanInquiryRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_NSDL},
		InquiriesData: []*nsdlVgPb.PanInquiryData{
			{
				Pan:  details.GetMetadata().GetPanDetails().GetId(),
				Name: details.GetMetadata().GetPersonalDetails().GetName(),
				Dob:  details.GetMetadata().GetPersonalDetails().GetDob(),
			},
		},
	})
	logger.Debug(ctx, "got response in nsdl PerformPanInquiry", zap.Any("resp", piRes), zap.Any("err", piResErr))
	if rpcErr := epifigrpc.IsRPCErrorWithDowntime(piRes, piResErr); rpcErr != nil {
		if errors.Is(rpcErr, epifierrors.ErrDowntimeExpected) {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, v.currentStep, v.nextStepOnSuccess).
				WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).
				WithDowntimeDeeplink(rpcErr), errors.Wrap(rpcErr, "downtime failure in PerformPanInquiry")
		}
		dl := v.getPanDobValidateBeFailureDeeplink(details, true, true)
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, v.currentStep, v.nextStepOnSuccess).
			WithDeeplink(dl), errors.Wrap(rpcErr, "error in PerformPanInquiry")
	}
	if len(piRes.GetInquiryResults()) != 1 {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, v.currentStep, v.nextStepOnSuccess),
			fmt.Errorf("expected 1 entry in nsdl pan inquiry got %d", len(piRes.GetInquiryResults()))
	}
	panInquiryResp := piRes.GetInquiryResults()[0]
	logger.Info(ctx, "nsdl PerformPanInquiry call successful", zap.String("status", panInquiryResp.GetPanStatus().String()), zap.String("aadhaar_status", panInquiryResp.GetAadhaarStatus().String()))
	v.updateNsdlDataWithNewNsdlResponse(details, panInquiryResp)

	if !panInquiryResp.GetNameMatched() {
		dl := v.genPanDobScreenDeeplink(details, "", "", nameDoesNotMatchPanFailureMsg, true)
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS,
			v.currentStep, v.nextStepOnSuccess).WithDeeplink(dl), nil
	}
	if !panInquiryResp.GetDobMatched() {
		dl := v.genPanDobScreenDeeplink(details, "", dobDoesNotMatchPanFailureMsg, "", true)
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS,
			v.currentStep, v.nextStepOnSuccess).WithDeeplink(dl), nil
	}

	if panInquiryResp.GetPanStatus() == wealthVendorPb.NsdlPanStatus_NSDL_PAN_STATUS_TYPE_EXISTING_AND_VALID {
		// as perincome tax department, pan which is not linked with aadhaar will become inoperative after 31 march 2023
		if panInquiryResp.GetAadhaarStatus() != wealthVendorPb.NsdlAadhaarStatus_NSDL_AADHAAR_STATUS_TYPE_SUCCESSFUL {
			if v.getPanDobAttemptCount(details) >= v.conf.MaxPanDobSubmitAttempts {
				logger.Error(ctx, fmt.Sprintf("user has exceeded the max pan-dob submit attempts : %d", v.getPanDobAttemptCount(details)))
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, v.currentStep,
					v.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_NSDL_PAN_VERIFICATION_FAILED), nil
			} else {
				return v.getAadhaarSeedingIssueResponse(details, true), nil
			}
		}
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, v.currentStep, v.nextStepOnSuccess), nil
	}
	logger.Info(ctx, "invalid pan as per nsdl", zap.String("status", panInquiryResp.GetPanStatus().String()))

	if v.getPanDobAttemptCount(details) >= v.conf.MaxPanDobSubmitAttempts {
		logger.Error(ctx, fmt.Sprintf("user has exceeded the max pan-dob submit attempts : %d", v.getPanDobAttemptCount(details)))
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, v.currentStep,
			v.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_NSDL_PAN_VERIFICATION_FAILED), nil
	} else {
		dl := v.invalidPanFailureDeeplink(details, true)
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, v.currentStep, v.nextStepOnSuccess).
			WithDeeplink(dl), nil
	}
}

func hasUserSubmittedPanDobAndNameData(details *woPb.OnboardingDetails) bool {
	return details.GetMetadata().GetCustomerProvidedData().GetUserSubmittedPan() != "" &&
		details.GetMetadata().GetCustomerProvidedData().GetUserSubmittedDob() != nil &&
		details.GetMetadata().GetCustomerProvidedData().GetUserSubmittedName() != ""
}

func (v *VerifyPanStep) updateNsdlDataWithNewNsdlResponse(details *woPb.OnboardingDetails, res *nsdlVgPb.PanInquiryResult) {
	if details.GetMetadata().GetNsdlData().GetPanDetails() == nil {
		if details.GetMetadata().GetNsdlData() == nil {
			if details.GetMetadata() == nil {
				details.Metadata = &woPb.OnboardingMetadata{}
			}
			details.GetMetadata().NsdlData = &woPb.NsdlData{}
		}
		details.GetMetadata().GetNsdlData().PanDetails = &woPb.NsdlData_NsdlPanDetails{}
	}
	pd := details.GetMetadata().GetNsdlData().GetPanDetails()
	pd.PanStatus = res.GetPanStatus()
	pd.AadhaarStatus = res.GetAadhaarStatus()

	pd.NameMatched = res.GetNameMatched()
	pd.DobMatched = res.GetDobMatched()
	pd.FatherNameMatched = res.GetFatherNameMatched()
}
