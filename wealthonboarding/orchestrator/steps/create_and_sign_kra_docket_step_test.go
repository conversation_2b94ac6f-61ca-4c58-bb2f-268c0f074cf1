package steps

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/epifi/gamma/api/typesv2"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/config"
	mocks "github.com/epifi/gamma/wealthonboarding/test/mocks/esign"
	ckycHelperMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/helpers"
	releaseMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/release"
)

func TestIsDocketValid(t *testing.T) {
	type want struct {
		validDocket bool
		err         error
	}
	tests := []struct {
		name      string
		docketUrl string
		want      *want
		wantErr   bool
	}{
		{
			name:      "docket url does not exist",
			docketUrl: "",
			want: &want{
				validDocket: false,
				err:         nil,
			},
			wantErr: false,
		},
		{
			name:      "docket url exists, but expired 1 hour ago",
			docketUrl: getDocketUrl(-time.Hour * 1),
			want: &want{
				validDocket: false,
				err:         nil,
			},
			wantErr: false,
		},
		{
			name:      "docket url exists, but expired 5 minutes ago",
			docketUrl: getDocketUrl(-time.Minute * 5),
			want: &want{
				validDocket: false,
				err:         nil,
			},
			wantErr: false,
		},
		{
			name:      "docket url exists and is valid for 1 more hour",
			docketUrl: getDocketUrl(time.Hour * 1),
			want: &want{
				validDocket: true,
				err:         nil,
			},
			wantErr: false,
		},
		{
			name:      "docket url exists and is valid for 10 more hours",
			docketUrl: getDocketUrl(time.Hour * 10),
			want: &want{
				validDocket: true,
				err:         nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		valid, err := isDocketUrlValid(tt.docketUrl)
		if (err != nil) != (tt.wantErr) {
			t.Errorf("isDocketUrlValid() error = %v, wantErr %v", err, tt.wantErr)
			return
		}
		if tt.wantErr {
			if tt.want.err.Error() != err.Error() {
				t.Errorf("isDocketUrlValid() error = %v, want_err %v", err, tt.want.err)
			}
		}
		if valid != tt.want.validDocket {
			t.Errorf("Unexpected result. Got: valid = %v. Want: valid = %v.", valid, tt.want.validDocket)
			return
		}
	}
}

// getDocketUrl returns an s3 presigned url based on the time duration passed
// it adds or subtracts the time duration from current time
func getDocketUrl(dur time.Duration) string {
	return fmt.Sprintf(`https://s3.ap-south-1.amazonaws.com/epifi-prod-wealth-onboarding/converted_doc_proof_image/AC2109179s2egkOeQlC2AkaHVq0a/b87b4071-c9f7-4d24-91fd-9ddc53bd85de/DOCUMENT_PROOF_TYPE_OTHERS/0.PDF?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIA4NVUGWDZHO7LRTXT20220512ap-south-1s3aws4_request&X-Amz-Date=%v&X-Amz-Expires=300&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEGcaCmFwLXNvdXRoLTEiSDBGAiEA0oIKx6xETUy6yEKUY4YT1IqvqWCJGthNElamKewh88CIQCxEJsa6x8x4kCoWo8gN19xQnQRXGs0TzoaBeQQHb5FirWBAhAEAIaDDg1NDAwMjY3NTk1NCIMSnzaNQs2ma7zpFr1KrME6NZ90Top8UAIiT8yHgTBoJMrAeQqIadqcMSmOBkRpJd6x45Xv7cFMT4YcaFfQJqMZg1W7OD5dMzJl1DpsgJrcolTw85Rk22ImEiqHi77bukt9cUoOFkXbX4w03OUvFHbz2DHqN1MX4zJjp2zMRNXDta1fX0cLUQtjqgGbmNuRCw4NLO5wWJRX8PeLFiuioXxTnqOE3Rdtlus9tAcv9UxN76fLe71j6QNcEVwyLVbFzdwbz8lblLdqT6gLtZoTAUNazXLt3xb0JuLnrR3TdG5Sd13DQsk24cjaVwgwvLFaUqAxDgTxOi0QGdy7epkZ8BexsDoMayVXyam725v0FKIdpNhF2f8VBvmgGE9QWPDnY1TsGS9hhfksFsV1q7rk8GmQNzoYOctyyXSUj1j5JXRV4VgaLYoFjpZQqJ0XdHWaWwYsWOH2tb9B521cm4zT9N2gb5WM7iw64DzAa076RFJcgtxMPgdkxXjnVGJu444dLG9AB2OzmTcJfYW1F9JBZ40TyFpDUv7sepdL6nBZHQ9Vkd9R3TN88Oz5Bs3c8dkbKB4FovnSHucNnNh7bV3jjkqbYL4AYDl3RMDIfPFikwMSUo0jXNcoUhq80YgSqJVCZF7jLvBHz0IGe1j4LyO9PWyN1RqHoMUiEqSesaEwPbqwitQlX5TvzTVA9NlHact9Rk0VdN8jqCX2l2VoBrtNd83e3omworjI3pgWH3qaQbcEOKSDoQ4woNTykwY6qAGSvqqtXhGbqtQcLNlPNBiSVodncMvkEUVNFV5RI5mFrgP8N3A3RVXOAZpOanat3WF8OEeW6yWeGvIv2otaqcmyze321QH3pxS1jeQ5OH0jW6MVel8ZCFjOptQy8DWgmNTvDbjJjU0Y9krXgFE98Gu5VGXy1MKIQctyIQtw4FosygeJab12Ba8cMfQoOz1x7gqiWAN91mSW0ZkmOkH3PkDR2Z4&X-Amz-SignedHeaders=host&X-Amz-Signature=52f4935617b9d866416577d7a0ee5473061ad66586169b19ae1eaff18599644e`,
		time.Now().UTC().Add(dur).Format(XAmzDateLayout))
}

func TestCreateAndSignKraDocketStep_Perform(t *testing.T) {
	t.Skip()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockCkycHelper := ckycHelperMocks.NewMockICkycHelper(ctrl)
	mockEsign := mocks.NewMockESign(ctrl)
	mockEvaluator := releaseMocks.NewMockIEvaluator(ctrl)

	type args struct {
		ctx     context.Context
		details *woPb.OnboardingDetails
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func()
		want       *StepExecutionResponse
		wantErr    bool
	}{
		{
			name: "ckyc disabled - skip ckyc data",
			args: args{
				ctx: context.Background(),
				details: &woPb.OnboardingDetails{
					ActorId: "actor-1",
					Metadata: &woPb.OnboardingMetadata{
						PanDetails: &typesv2.DocumentProof{
							Id: "pan123",
						},
					},
				},
			},
			setupMocks: func() {
				mockEsign.EXPECT().GetLatestESignDetails(gomock.Any(), gomock.Any()).Return(&woPb.ESignDetails{
					TxnState: woPb.TxnState_TXN_STATE_COMPLETED,
				}, nil)
				mockEsign.EXPECT().IsValidEsign(gomock.Any(), gomock.Any()).Return(true, nil)
				mockEsign.EXPECT().GetSignUrl(gomock.Any(), gomock.Any()).Return("url", nil)
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					Step:   woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET,
					Status: woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
			},
			wantErr: false,
		},
		{
			name: "ckyc enabled - proceed with ckyc data",
			args: args{
				ctx: context.Background(),
				details: &woPb.OnboardingDetails{
					ActorId: "actor-1",
					Metadata: &woPb.OnboardingMetadata{
						PanDetails: &typesv2.DocumentProof{
							Id: "pan123",
						},
					},
				},
			},
			setupMocks: func() {
				mockCkycHelper.EXPECT().FetchPopulateValidateCkycDownloadData(gomock.Any(), gomock.Any()).Return(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSPECIFIED, nil)
				mockEsign.EXPECT().GetLatestESignDetails(gomock.Any(), gomock.Any()).Return(&woPb.ESignDetails{
					TxnState: woPb.TxnState_TXN_STATE_COMPLETED,
				}, nil)
				mockEsign.EXPECT().IsValidEsign(gomock.Any(), gomock.Any()).Return(true, nil)
				mockEsign.EXPECT().GetSignUrl(gomock.Any(), gomock.Any()).Return("url", nil)
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					Step:   woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET,
					Status: woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			conf := &config.Config{
				DisableCKYC: tt.name == "ckyc disabled - skip ckyc data",
			}

			s := NewCreateAndSignKraDocketStep(conf, nil, nil, mockCkycHelper, mockEsign, mockEvaluator, nil)

			tt.setupMocks()
			got, err := s.Perform(tt.args.ctx, tt.args.details)
			if (err != nil) != tt.wantErr {
				t.Errorf("Perform() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			opts := cmp.Options{
				protocmp.Transform(),
				protocmp.IgnoreFields(&woPb.OnboardingStepDetails{}, "completed_at"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("Perform() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}
