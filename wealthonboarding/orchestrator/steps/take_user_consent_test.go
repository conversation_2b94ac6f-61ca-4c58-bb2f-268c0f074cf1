package steps

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/datetime"

	"github.com/epifi/gamma/api/consent"
	mockConsent "github.com/epifi/gamma/api/consent/mocks"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/release"
	mock_evaluator "github.com/epifi/gamma/wealthonboarding/test/mocks/release"
)

type TakeUserConsentFields struct {
	consentClient    *mockConsent.MockConsentClient
	releaseEvaluator *mock_evaluator.MockIEvaluator
}

func initTakeUserConsentFields(ctrl *gomock.Controller) *TakeUserConsentFields {
	return &TakeUserConsentFields{
		consentClient:    mockConsent.NewMockConsentClient(ctrl),
		releaseEvaluator: mock_evaluator.NewMockIEvaluator(ctrl),
	}
}

var (
	time1 = time.Date(2023, 12, 12, 0, 0, 0, 0, datetime.IST)
)

func TestTakeUserConsent_Perform(t1 *testing.T) {
	type args struct {
		ctx     context.Context
		details *woPb.OnboardingDetails
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *TakeUserConsentFields)
		want       *StepExecutionResponse
		wantErr    bool
	}{
		{
			name: "user is not eligible for consent tnc step, skip to next step",
			args: args{
				ctx: context.Background(),
				details: &woPb.OnboardingDetails{
					ActorId: "actor-1",
				},
			},
			setupMocks: func(f *TakeUserConsentFields) {
				f.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), &release.CommonConstraintData{
					ActorId: "actor-1",
					Feature: woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_TNC_V2,
				}).Return(false, nil, nil)
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					Step:   woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
					Status: woPb.OnboardingStepStatus_STEP_STATUS_SKIPPED,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_NoMetadata{},
					},
					CompletedAt: timestamppb.New(time1),
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
				IsLastStep:       false,
			},
			wantErr: false,
		},
		{
			name: "failed to check if consent is present for eligible user",
			args: args{
				ctx: context.Background(),
				details: &woPb.OnboardingDetails{
					ActorId: "actor-1",
				},
			},
			setupMocks: func(f *TakeUserConsentFields) {
				f.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), &release.CommonConstraintData{
					ActorId: "actor-1",
					Feature: woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_TNC_V2,
				}).Return(false, nil, fmt.Errorf("err"))
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					Step:      woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
					Status:    woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE,
					SubStatus: woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_FEATURE_EVALUATION_FAILED,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_NoMetadata{},
					},
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
				IsLastStep:       false,
			},
			wantErr: true,
		},
		{
			name: "failed to check if consent is present for user",
			args: args{
				ctx: context.Background(),
				details: &woPb.OnboardingDetails{
					ActorId: "actor-1",
				},
			},
			setupMocks: func(f *TakeUserConsentFields) {
				f.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), &release.CommonConstraintData{
					ActorId: "actor-1",
					Feature: woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_TNC_V2,
				}).Return(true, nil, nil)
				f.consentClient.EXPECT().FetchConsent(gomock.Any(), &consent.FetchConsentRequest{
					ConsentType: consent.ConsentType_FI_WEALTH_TNC,
					ActorId:     "actor-1",
					Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.FetchConsentResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					Step:      woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
					Status:    woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE,
					SubStatus: woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CONSENT_FETCH_FAILED,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_NoMetadata{},
					},
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
				IsLastStep:       false,
			},
			wantErr: true,
		},
		{
			name: "consent is already present, move to next step  (eligible user with wealth consent already submitted before)",
			args: args{
				ctx: context.Background(),
				details: &woPb.OnboardingDetails{
					ActorId: "actor-1",
				},
			},
			setupMocks: func(f *TakeUserConsentFields) {
				f.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), &release.CommonConstraintData{
					ActorId: "actor-1",
					Feature: woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_TNC_V2,
				}).Return(true, nil, nil)
				f.consentClient.EXPECT().FetchConsent(gomock.Any(), &consent.FetchConsentRequest{
					ConsentType: consent.ConsentType_FI_WEALTH_TNC,
					ActorId:     "actor-1",
					Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.FetchConsentResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					Step:   woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
					Status: woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_NoMetadata{},
					},
					CompletedAt: timestamppb.New(time1),
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
				IsLastStep:       false,
			},
			wantErr: false,
		},
		{
			name: "show consent tnc page (new eligible user with no wealth consent before)",
			args: args{
				ctx: context.Background(),
				details: &woPb.OnboardingDetails{
					ActorId: "actor-1",
				},
			},
			setupMocks: func(f *TakeUserConsentFields) {
				f.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), &release.CommonConstraintData{
					ActorId: "actor-1",
					Feature: woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_TNC_V2,
				}).Return(true, nil, nil)
				f.consentClient.EXPECT().FetchConsent(gomock.Any(), &consent.FetchConsentRequest{
					ConsentType: consent.ConsentType_FI_WEALTH_TNC,
					ActorId:     "actor-1",
					Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.FetchConsentResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					Step:   woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
					Status: woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_NoMetadata{},
					},
				},
				Deeplink: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_AGREEMENT_CONFIRMATION_SCREEN,
					ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingSignAgreementScreenOptions{
						WealthOnboardingSignAgreementScreenOptions: &deeplinkPb.WealthOnboardingSignAgreementScreenOptions{
							Title:           consentScreenTitle,
							Description:     consentScreenDescription,
							IllustrationUrl: startInvestingImgUrl,
							Consents: []*widget.CheckboxItem{
								{
									Id:          consent.ConsentType_FI_WEALTH_TNC.String(),
									DisplayText: commontypes.GetHtmlText(tncHtmlText).WithFontStyle(commontypes.FontStyle_BODY_4).WithFontColor(colors.ColorSlate),
									IsChecked:   false,
								},
							},
						},
					},
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
				IsLastStep:       false,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			ctrl := gomock.NewController(t1)
			defer ctrl.Finish()
			f := initTakeUserConsentFields(ctrl)
			t := NewTakeUserConsent(f.consentClient, f.releaseEvaluator)
			tt.setupMocks(f)
			got, err := t.Perform(tt.args.ctx, tt.args.details)
			if (err != nil) != tt.wantErr {
				t1.Errorf("Perform() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.CurrentStepDetails.GetCompletedAt() != nil {
				got.CurrentStepDetails.CompletedAt = timestamppb.New(time1)
			}
			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t1.Errorf("Perform (-want +got):\n%s", diff)
			}
		})
	}
}
