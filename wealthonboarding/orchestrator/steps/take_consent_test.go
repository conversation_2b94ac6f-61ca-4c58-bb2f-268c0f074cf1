package steps

import (
	"context"
	"reflect"
	"testing"

	"github.com/epifi/be-common/api/rpc"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/vendorgateway/wealth/cvl"
	"github.com/epifi/gamma/api/vendors/wealth"

	"github.com/golang/mock/gomock"

	"github.com/epifi/gamma/api/consent"
	consentMocks "github.com/epifi/gamma/api/consent/mocks"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/user/obfuscator"
	obfuscatorMocks "github.com/epifi/gamma/api/user/obfuscator/mocks"
	cvlVgMocks "github.com/epifi/gamma/api/vendorgateway/wealth/cvl/mocks"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/helper"
	"github.com/epifi/gamma/wealthonboarding/kra_data"
	"github.com/epifi/gamma/wealthonboarding/release"
	mockDao "github.com/epifi/gamma/wealthonboarding/test/mocks/dao"
	mockHelpers "github.com/epifi/gamma/wealthonboarding/test/mocks/helpers"
	mockKraData "github.com/epifi/gamma/wealthonboarding/test/mocks/kra_data"
	releaseEvaluatorMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/release"
)

func TestTakeConfirmationOnPepAndCitizenship_storeBankDetails(t *testing.T) {
	ctr := gomock.NewController(t)
	type fields struct {
		consentClient            consent.ConsentClient
		docHelper                helper.DocumentHelper
		cvlKraDataSvc            kra_data.KraData
		commonHelper             helper.ICommonHelper
		onboardingStepDetailsDao dao.OnboardingStepDetailsDao
		releaseEvaluator         release.IEvaluator
		obfuscatorClient         obfuscator.ObfuscatorClient
	}
	type args struct {
		ctx     context.Context
		details *woPb.OnboardingDetails
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "happy case",
			fields: fields{
				consentClient: func() consent.ConsentClient {
					a := consentMocks.NewMockConsentClient(ctr)
					return a
				}(),
				docHelper: func() helper.DocumentHelper {
					a := mockHelpers.NewMockDocumentHelper(ctr)
					return a
				}(),
				cvlKraDataSvc: func() kra_data.KraData {
					a := mockKraData.NewMockKraData(ctr)
					return a
				}(),
				commonHelper: func() helper.ICommonHelper {
					a := mockHelpers.NewMockICommonHelper(ctr)
					a.EXPECT().FetchBankAccountDetails(gomock.Any(), gomock.Any()).Return(&woPb.BankDetails{
						AccountNumber: "AccountNumber",
						IfscCode:      "IfscCode",
						AccountType:   "AccountType",
						BankName:      "BankName",
						BranchName:    "BranchName",
						BankCity:      "BankCity",
					}, nil)
					return a
				}(),
				onboardingStepDetailsDao: func() dao.OnboardingStepDetailsDao {
					a := mockDao.NewMockOnboardingStepDetailsDao(ctr)
					return a
				}(),
				releaseEvaluator: func() release.IEvaluator {
					a := releaseEvaluatorMocks.NewMockIEvaluator(ctr)
					return a
				}(),
				obfuscatorClient: func() obfuscator.ObfuscatorClient {
					a := obfuscatorMocks.NewMockObfuscatorClient(ctr)
					return a
				}(),
			},
			args: args{
				ctx: context.Background(),
				details: &woPb.OnboardingDetails{
					Metadata: &woPb.OnboardingMetadata{},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t1 *testing.T) {
			s := &TakeConfirmationOnPepAndCitizenship{
				currentStep:              0,
				nextStepOnSuccess:        0,
				conf:                     conf,
				consentClient:            tt.fields.consentClient,
				docHelper:                tt.fields.docHelper,
				cvlKraDataSvc:            tt.fields.cvlKraDataSvc,
				commonHelper:             tt.fields.commonHelper,
				onboardingStepDetailsDao: tt.fields.onboardingStepDetailsDao,
				releaseEvaluator:         tt.fields.releaseEvaluator,
				obfuscatorClient:         tt.fields.obfuscatorClient,
				cvlVgClient:              nil,
			}
			if err := s.storeBankDetails(tt.args.ctx, tt.args.details); (err != nil) != tt.wantErr {
				t1.Errorf("storeBankDetails() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_getTnCDeeplink(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockReleaseEvaluator := releaseEvaluatorMocks.NewMockIEvaluator(ctrl)
	mockCvlVgClient := cvlVgMocks.NewMockCvlClient(ctrl)
	type args struct {
		isConsentTaken bool
	}
	tests := []struct {
		name       string
		args       args
		want       *deeplinkPb.Deeplink
		wantErr    bool
		setupMocks func()
	}{
		{
			name: "new dl for consent not taken",
			args: args{
				isConsentTaken: false,
			},
			want: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_AGREEMENT_CONFIRMATION_SCREEN,
				ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingSignAgreementScreenOptions{
					WealthOnboardingSignAgreementScreenOptions: &deeplinkPb.WealthOnboardingSignAgreementScreenOptions{
						TncUrl:          TncURL,
						IsConsentTaken:  false,
						Title:           TnCScreenTitle,
						Description:     TncScreenDescrDownloadKRAConsentNotTaken,
						IllustrationUrl: helper.FinishLineFlagIllustrationURL,
					},
				},
			},
			setupMocks: func() {
				mockCvlVgClient.EXPECT().GetPanStatus(gomock.Any(), gomock.Any()).Return(&cvl.GetPanStatusResponse{
					Status:     rpc.StatusOk(),
					PanEnquiry: &wealth.PanEnquiry{Status: wealth.KraStatus_KRA_STATUS_KRA_VERIFIED},
				}, nil)
			},
		},
		{
			name: "new dl for consent taken",
			args: args{
				isConsentTaken: true,
			},
			want: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_AGREEMENT_CONFIRMATION_SCREEN,
				ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingSignAgreementScreenOptions{
					WealthOnboardingSignAgreementScreenOptions: &deeplinkPb.WealthOnboardingSignAgreementScreenOptions{
						TncUrl:                    TncURL,
						IsConsentTaken:            true,
						Title:                     TnCScreenTitle,
						Description:               TncScreenDescrDownloadKRAConsentTaken,
						IllustrationUrl:           helper.FinishLineFlagIllustrationURL,
						ProgressBarDurationInSecs: TnCConsentTakenProgressBarDurationInSecs,
					},
				},
			},
			setupMocks: func() {
				mockCvlVgClient.EXPECT().GetPanStatus(gomock.Any(), gomock.Any()).Return(&cvl.GetPanStatusResponse{
					Status:     rpc.StatusOk(),
					PanEnquiry: &wealth.PanEnquiry{Status: wealth.KraStatus_KRA_STATUS_KRA_VERIFIED},
				}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &TakeConfirmationOnPepAndCitizenship{
				releaseEvaluator: mockReleaseEvaluator,
				cvlVgClient:      mockCvlVgClient,
			}
			tt.setupMocks()
			got, err := s.getTnCDeeplink(context.Background(), &woPb.OnboardingDetails{
				ActorId: "randomActorId",
				Metadata: &woPb.OnboardingMetadata{
					IsFreshKra: false,
					PanDetails: &types.DocumentProof{Id: "randomPAN"},
				},
			}, tt.args.isConsentTaken)
			if (err != nil) != tt.wantErr {
				t.Errorf("getTnCDeeplink() error = %v, wantErr %v", err, tt.wantErr)
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getTnCDeeplink() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_isIpValid(t *testing.T) {
	type args struct {
		ip string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "local ip",
			args: args{
				ip: "127.0.0.1",
			},
			want: false,
		},
		{
			name: "ip other than local host ip",
			args: args{
				ip: "***********",
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isIpValid(tt.args.ip); got != tt.want {
				t.Errorf("isIpValid() = %v, want %v", got, tt.want)
			}
		})
	}
}
