//nolint:depguard
package steps

import (
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/ptypes/timestamp"
	"golang.org/x/net/context"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	types "github.com/epifi/gamma/api/typesv2"
	cvlVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/cvl"
	"github.com/epifi/gamma/api/vendorgateway/wealth/cvl/mocks"
	"github.com/epifi/gamma/api/vendors/wealth"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	mock_evaluator "github.com/epifi/gamma/wealthonboarding/test/mocks/release"
)

var (
	oneDayAgo    *timestamppb.Timestamp
	twentyDayAgo *timestamppb.Timestamp
)

func TestFetchKycKraInfoStep_Perform(t *testing.T) {
	ctr := gomock.NewController(t)

	cvlVgClient := mocks.NewMockCvlClient(ctr)
	releaseEval := mock_evaluator.NewMockIEvaluator(ctr)
	type args struct {
		ctx   context.Context
		od    *woPb.OnboardingDetails
		mocks []interface{}
	}
	tests := []struct {
		name    string
		args    *args
		want    *StepExecutionResponse
		wantErr bool
	}{
		{
			name: "skip ckyc flow due to empty ckyc data and mark fresh kra due to submitted date being more 15 days ago with digilocker flow enabled",
			args: &args{
				ctx: context.Background(),
				od: &woPb.OnboardingDetails{
					ActorId: "random_actor_id",
					Metadata: &woPb.OnboardingMetadata{
						PanDetails: &types.DocumentProof{Id: "random_pan_id"},
					},
				},
				mocks: []interface{}{
					cvlVgClient.EXPECT().GetPanStatus(gomock.Any(), gomock.Any()).Return(&cvlVgPb.GetPanStatusResponse{
						Status: rpcPb.StatusOk(),
						PanEnquiry: &wealth.PanEnquiry{
							Status:         wealth.KraStatus_KRA_STATUS_SUBMITTED,
							PanNumber:      "random_pan_id",
							LastUpdateDate: getTwentyDayAgoTime(),
							IpvFlag:        wealth.KraIpvFlag_KRA_IPV_FLAG_TYPE_Y,
						},
					}, nil).Times(1),
				},
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_FROM_DIGILOCKER,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					SubStatus: woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_FRESH_KRA_NEEDED,
					Step:      woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_FetchKycInfoFromKraStepMetadata{
							FetchKycInfoFromKraStepMetadata: &woPb.FetchKycInfoFromKraStepMetadata{
								StatusData: &woPb.KraStatusData{PanStatusDetails: &woPb.KraStatusData_PanStatusDetails{
									PanNumber:      "random_pan_id",
									LastUpdateDate: getTwentyDayAgoTime(),
									IpvFlag:        wealth.KraIpvFlag_KRA_IPV_FLAG_TYPE_Y,
									Status:         wealth.KraStatus_KRA_STATUS_SUBMITTED,
								}},
								IsFreshKra: true,
							},
						},
					},
					Status: woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
			},
			wantErr: false,
		},
		{
			name: "skip ckyc flow due to empty ckyc data and fresh kra status, digilocker enabled",
			args: &args{
				ctx: context.Background(),
				od: &woPb.OnboardingDetails{
					ActorId: "random_actor_id",
					Metadata: &woPb.OnboardingMetadata{
						PanDetails: &types.DocumentProof{Id: "random_pan_id"},
					},
				},
				mocks: []interface{}{
					cvlVgClient.EXPECT().GetPanStatus(gomock.Any(), gomock.Any()).Return(&cvlVgPb.GetPanStatusResponse{
						Status: rpcPb.StatusOk(),
						PanEnquiry: &wealth.PanEnquiry{
							Status:    wealth.KraStatus_KRA_STATUS_NOT_AVAILABLE,
							PanNumber: "random_pan_id",
						},
					}, nil).Times(1),
				},
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_FROM_DIGILOCKER,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					Step:      woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
					SubStatus: woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_FRESH_KRA_NEEDED,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_FetchKycInfoFromKraStepMetadata{
							FetchKycInfoFromKraStepMetadata: &woPb.FetchKycInfoFromKraStepMetadata{
								StatusData: &woPb.KraStatusData{PanStatusDetails: &woPb.KraStatusData_PanStatusDetails{
									PanNumber: "random_pan_id",
									Status:    wealth.KraStatus_KRA_STATUS_NOT_AVAILABLE,
								}},
								IsFreshKra: true,
							},
						},
					},
					Status: woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
			},
			wantErr: false,
		},
		{
			name: "skip ckyc flow due to empty ckyc data and invalid pan format",
			args: &args{
				ctx: context.Background(),
				od: &woPb.OnboardingDetails{
					ActorId: "random_actor_id",
					Metadata: &woPb.OnboardingMetadata{
						PanDetails: &types.DocumentProof{Id: "random_pan_id"},
					},
				},
				mocks: []interface{}{
					cvlVgClient.EXPECT().GetPanStatus(gomock.Any(), gomock.Any()).Return(&cvlVgPb.GetPanStatusResponse{
						Status: rpcPb.StatusOk(),
						PanEnquiry: &wealth.PanEnquiry{
							Status:    wealth.KraStatus_KRA_STATUS_INVALID_PAN_NO_FORMAT,
							PanNumber: "random_pan_id",
						},
					}, nil).Times(1),
				},
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_UNSPECIFIED,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					Step:      woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
					SubStatus: woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_INVALID_PAN_NO_FORMAT,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_FetchKycInfoFromKraStepMetadata{
							FetchKycInfoFromKraStepMetadata: &woPb.FetchKycInfoFromKraStepMetadata{
								StatusData: &woPb.KraStatusData{PanStatusDetails: &woPb.KraStatusData_PanStatusDetails{
									PanNumber: "random_pan_id",
									Status:    wealth.KraStatus_KRA_STATUS_INVALID_PAN_NO_FORMAT,
								}},
							},
						},
					},
					Status: woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE,
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_FUTURE_SCOPE,
			},
			wantErr: true,
		},
		{
			name: "skip ckyc flow due to empty ckyc data and kra status hold",
			args: &args{
				ctx: context.Background(),
				od: &woPb.OnboardingDetails{
					ActorId: "random_actor_id",
					Metadata: &woPb.OnboardingMetadata{
						PanDetails: &types.DocumentProof{Id: "random_pan_id"},
					},
				},
				mocks: []interface{}{
					cvlVgClient.EXPECT().GetPanStatus(gomock.Any(), gomock.Any()).Return(&cvlVgPb.GetPanStatusResponse{
						Status: rpcPb.StatusOk(),
						PanEnquiry: &wealth.PanEnquiry{
							Status:    wealth.KraStatus_KRA_STATUS_HOLD,
							PanNumber: "random_pan_id",
						},
					}, nil).Times(1),
				},
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_UNSPECIFIED,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					Step:      woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
					SubStatus: woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_KRA_STATUS_HOLD,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_FetchKycInfoFromKraStepMetadata{
							FetchKycInfoFromKraStepMetadata: &woPb.FetchKycInfoFromKraStepMetadata{
								StatusData: &woPb.KraStatusData{PanStatusDetails: &woPb.KraStatusData_PanStatusDetails{
									PanNumber: "random_pan_id",
									Status:    wealth.KraStatus_KRA_STATUS_HOLD,
								}},
							},
						},
					},
					Status: woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE,
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_FUTURE_SCOPE,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			f := &FetchKycInfoStep{
				cvlVgClient:            cvlVgClient,
				currentStep:            woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
				livenessStep:           woPb.OnboardingStep_ONBOARDING_STEP_LIVENESS,
				collectMissingDataStep: woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO,
				downloadFromDigilocker: woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_FROM_DIGILOCKER,
				commonHelper:           nil,
				conf:                   nil,
				releaseEvaluator:       releaseEval,
			}
			got, err := f.Perform(tt.args.ctx, tt.args.od)
			if (err != nil) != tt.wantErr {
				t.Errorf("Perform() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isEqualStepExecutionResponse(got, tt.want, t) {
				t.Errorf("Perform() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func getTwentyDayAgoTime() *timestamp.Timestamp {
	if twentyDayAgo != nil {
		return twentyDayAgo
	}
	currTime := time.Now()
	// subtract 1 day
	yesterday := currTime.Add(-time.Hour * 24 * 20)
	twentyDayAgo = timestamppb.New(yesterday)
	return twentyDayAgo
}
