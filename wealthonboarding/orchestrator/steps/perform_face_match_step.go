package steps

import (
	"context"

	woTypes "github.com/epifi/gamma/wealthonboarding/types"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/auth/liveness"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woConsumerPb "github.com/epifi/gamma/api/wealthonboarding/consumer"
	"github.com/epifi/gamma/wealthonboarding/ckyc_helper"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/dao"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
)

type PerformFaceMatchStep struct {
	conf                     *config.Config
	livenessClient           liveness.LivenessClient
	pub                      woTypes.RefreshFaceMatchStatusSqsPublisher
	onboardingStepDetailsDao dao.OnboardingStepDetailsDao
	ckycHelper               ckyc_helper.ICkycHelper
	currentStep              woPb.OnboardingStep
	nextStepOnSuccess        woPb.OnboardingStep
}

func NewPerformFaceMatchStep(
	conf *config.Config,
	livenessClient liveness.LivenessClient,
	pub woTypes.RefreshFaceMatchStatusSqsPublisher,
	onboardingStepDetailsDao dao.OnboardingStepDetailsDao,
	ckycHelper ckyc_helper.ICkycHelper) *PerformFaceMatchStep {
	return &PerformFaceMatchStep{
		conf:                     conf,
		livenessClient:           livenessClient,
		pub:                      pub,
		onboardingStepDetailsDao: onboardingStepDetailsDao,
		ckycHelper:               ckycHelper,
		currentStep:              woPb.OnboardingStep_ONBOARDING_STEP_FACE_MATCH,
		nextStepOnSuccess:        woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO,
	}
}

func (p *PerformFaceMatchStep) Perform(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	osd, err := p.onboardingStepDetailsDao.GetByOnboardingDetailsIdAndStep(ctx, details.GetId(), woPb.OnboardingStep_ONBOARDING_STEP_FACE_MATCH)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, p.currentStep, p.nextStepOnSuccess), errors.Wrap(err, "failed to fetch liveness step details")
	}
	if osd != nil && woPb.IsTerminalStatus(osd.GetStatus()) {
		return GetStepExecutionResponse(details, osd.GetStatus(), p.currentStep, p.nextStepOnSuccess), nil
	}

	if !p.conf.DisableCKYC {
		ckycErr := p.ckycHelper.FetchAndPopulateCkycSearchData(ctx, details)
		if ckycErr != nil {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, p.currentStep, p.nextStepOnSuccess), errors.Wrap(ckycErr, "failed to fetch and populate ckyc search data")
		}
	}

	ser, fmErr := p.InitiateFM(ctx, details)
	if fmErr != nil {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, p.currentStep, p.nextStepOnSuccess), errors.Wrap(fmErr, "failed to initiate face match")
	}
	return ser, nil
}

// nolint:funlen
func (p *PerformFaceMatchStep) InitiateFM(ctx context.Context, od *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	if od.GetMetadata().GetFaceMatchData() == nil {
		od.GetMetadata().FaceMatchData = &woPb.FaceMatchData{}
	}
	fmData := od.GetMetadata().GetFaceMatchData()
	var fmAttempt *woPb.FaceMatchData_FaceMatchAttempt
	if len(fmData.GetFaceMatchAttempts()) == 0 {
		fmAttempt = &woPb.FaceMatchData_FaceMatchAttempt{
			RefImage:     od.GetMetadata().GetPersonalDetails().GetPhoto().GetPhoto()[0],
			ImageToMatch: od.GetMetadata().GetCkycData().GetSearchData().GetPhoto().GetPhoto()[0],
			AttemptId:    uuid.New().String(),
		}
		fmData.FaceMatchAttempts = append(fmData.FaceMatchAttempts, fmAttempt)
	} else {
		fmAttempt = fmData.GetFaceMatchAttempts()[len(fmData.GetFaceMatchAttempts())-1]
	}

	var (
		zapReqId = zap.String(logger.REQUEST_ID, fmAttempt.GetAttemptId())
	)

	fmStatus := fmAttempt.GetFaceMatchStatus()
	if fmStatus == woPb.FaceMatchData_FaceMatchAttempt_FACE_MATCH_STATUS_PENDING || fmStatus == woPb.FaceMatchData_FaceMatchAttempt_FACE_MATCH_STATUS_UNSPECIFIED {
		fmsRes, errRes := p.livenessClient.GetFaceMatchStatus(ctx, &liveness.GetFaceMatchStatusRequest{
			ActorId:   od.GetActorId(),
			AttemptId: fmAttempt.GetAttemptId(),
		})
		te := epifigrpc.RPCError(fmsRes, errRes)
		if te != nil && fmsRes.GetStatus().Code != rpc.StatusRecordNotFound().GetCode() {
			logger.Error(ctx, "error in GetFaceMatchStatus", zapReqId, zap.Error(te))
			return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, p.currentStep, p.nextStepOnSuccess), errors.Wrap(te, "fm status check failed")
		}
		fmStatus = p.getWealthFmStatus(fmsRes.GetFaceMatchStatus())
		fmAttempt.FaceMatchStatus = fmStatus
	}

	switch fmStatus {
	case woPb.FaceMatchData_FaceMatchAttempt_FACE_MATCH_STATUS_UNSPECIFIED:
		logger.Info(ctx, "initiating face match")
	case woPb.FaceMatchData_FaceMatchAttempt_FACE_MATCH_STATUS_PENDING:
		return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, p.currentStep, p.nextStepOnSuccess), nil
	case woPb.FaceMatchData_FaceMatchAttempt_FACE_MATCH_STATUS_PASSED:
		return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, p.currentStep, p.nextStepOnSuccess), nil
	case woPb.FaceMatchData_FaceMatchAttempt_FACE_MATCH_STATUS_FAILED_RETRY:
		logger.Info(ctx, "re-initiating face match")
		fmAttempt = &woPb.FaceMatchData_FaceMatchAttempt{
			RefImage:     od.GetMetadata().GetPersonalDetails().GetPhoto().GetPhoto()[0],
			ImageToMatch: od.GetMetadata().GetCkycData().GetSearchData().GetPhoto().GetPhoto()[0],
			AttemptId:    uuid.New().String(),
		}
		fmData.FaceMatchAttempts = append(fmData.FaceMatchAttempts, fmAttempt)
	case woPb.FaceMatchData_FaceMatchAttempt_FACE_MATCH_STATUS_FAILED:
		return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED, p.currentStep, p.nextStepOnSuccess), nil
	}

	respFM, errRes := p.livenessClient.FaceMatch(ctx, &liveness.FaceMatchRequest{
		ActorId:    od.GetActorId(),
		AttemptId:  fmAttempt.GetAttemptId(),
		Image:      fmAttempt.GetRefImage(),
		ImageFrame: fmAttempt.GetImageToMatch(),
	})
	if err := epifigrpc.RPCError(respFM, errRes); err != nil {
		logger.Error(ctx, "error while initiating face match", zapReqId, zap.Error(err))
		return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, p.currentStep, p.nextStepOnSuccess), errors.Wrap(err, "error while initiating face match")
	}
	logger.Info(ctx, "face match initiated", zapReqId)

	if _, err := p.pub.PublishWithDelay(ctx, &woConsumerPb.RefreshFaceMatchStatusRequest{
		OnboardingDetails: od,
	}, p.conf.FetchFMStatusInitialDelayDuration); err != nil {
		return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, p.currentStep, p.nextStepOnSuccess), errors.Wrap(err, "failed to publish event to face match status queue")
	}
	fmAttempt.FaceMatchStatus = woPb.FaceMatchData_FaceMatchAttempt_FACE_MATCH_STATUS_PENDING
	return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, p.currentStep, p.nextStepOnSuccess), nil
}

func (p *PerformFaceMatchStep) getWealthFmStatus(status liveness.FaceMatchStatus) woPb.FaceMatchData_FaceMatchAttempt_FaceMatchStatus {
	switch status {
	default:
		return woPb.FaceMatchData_FaceMatchAttempt_FACE_MATCH_STATUS_UNSPECIFIED
	case liveness.FaceMatchStatus_FACE_MATCH_PENDING:
		return woPb.FaceMatchData_FaceMatchAttempt_FACE_MATCH_STATUS_PENDING
	case liveness.FaceMatchStatus_FACE_MATCH_PASSED:
		return woPb.FaceMatchData_FaceMatchAttempt_FACE_MATCH_STATUS_PASSED
	case liveness.FaceMatchStatus_FACE_MATCH_FAILED_RETRY:
		return woPb.FaceMatchData_FaceMatchAttempt_FACE_MATCH_STATUS_FAILED_RETRY
	case liveness.FaceMatchStatus_FACE_MATCH_FAILED:
		return woPb.FaceMatchData_FaceMatchAttempt_FACE_MATCH_STATUS_FAILED
	}
}
