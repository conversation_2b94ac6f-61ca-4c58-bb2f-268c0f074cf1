package steps

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"path/filepath"

	"github.com/google/uuid"
	"github.com/imdario/mergo"

	types "github.com/epifi/gamma/api/typesv2"
	woHelper "github.com/epifi/gamma/wealthonboarding/helper"
	kraDataSvc "github.com/epifi/gamma/wealthonboarding/kra_data"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	digilockerVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/digilocker"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/config"
)

type DownloadFromDigilocker struct {
	conf               *config.Config
	currentStep        woPb.OnboardingStep
	nextStepOnSuccess  woPb.OnboardingStep
	digilockerVgClient digilockerVgPb.DigilockerClient
	docHelper          woHelper.DocumentHelper
	kraData            kraDataSvc.KraData
}

func NewDownloadFromDigilocker(
	conf *config.Config,
	digilockerVgClient digilockerVgPb.DigilockerClient,
	docHelper woHelper.DocumentHelper,
	kraData kraDataSvc.KraData,
) *DownloadFromDigilocker {
	return &DownloadFromDigilocker{
		conf:               conf,
		currentStep:        woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_FROM_DIGILOCKER,
		nextStepOnSuccess:  woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO,
		digilockerVgClient: digilockerVgClient,
		docHelper:          docHelper,
		kraData:            kraData,
	}
}

// nolint:funlen,dupl
func (d *DownloadFromDigilocker) Perform(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	hasDigilockerAccount := details.GetMetadata().GetCustomerProvidedData().GetHasDigilockerAccount()
	//nolint:exhaustive
	switch hasDigilockerAccount {
	case commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED:
		logger.Debug(ctx, "digilocker account availability unknown for user")
		res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_PENDING, d.currentStep, d.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_DIGILOCKER_ACCOUNT_AVAILABILITY_UNKNOWN)
		res.Deeplink = woHelper.GetDigiLockerDeeplink(d.conf.DigilockerConfig)
		return res, nil
	case commontypes.BooleanEnum_FALSE:
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, d.currentStep, d.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_NO_DIGILOCKER_ACCOUNT), nil
	}

	// when user has DigiLocker account, fetch documents from there and store them
	logger.Info(ctx, "user has digilocker account, fetching details from digilocker", zap.String(logger.ACTOR_ID_V2, details.GetActorId()))
	atRes, atErr := d.digilockerVgClient.GetAccessToken(ctx, &digilockerVgPb.GetAccessTokenRequest{
		Header:      &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_DIGILOCKER},
		ActorId:     details.GetActorId(),
		AccessRoute: &digilockerVgPb.GetAccessTokenRequest_RefreshToken{RefreshToken: details.GetMetadata().GetDigilockerData().GetRefreshToken()},
	})
	if err := epifigrpc.IsRPCErrorWithDowntime(atRes, atErr); err != nil {
		if atRes.GetStatus().IsUnauthenticated() {
			res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, d.currentStep, d.nextStepOnSuccess)
			res.Deeplink = woHelper.GetDigiLockerDeeplink(d.conf.DigilockerConfig)
			return res, nil
		}
		logger.Error(ctx, "failed to get digilocker access token", zap.Error(err))
		if errors.Is(err, epifierrors.ErrDowntimeExpected) {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, d.currentStep, d.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(err), errors.Wrap(err, "downtime expected")
		}
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, d.currentStep, d.nextStepOnSuccess), errors.Wrap(err, "error getting DigiLocker access token")
	}
	// always update refresh tokens with the newest ones received from vendor
	details.GetMetadata().GetDigilockerData().RefreshToken = atRes.GetTokenResponse().GetRefreshToken()

	digiLockerDocsData, digiLockerDocsErr := d.getDigiLockerDocuments(ctx, details.GetActorId(), atRes.GetTokenResponse().GetAccessToken())
	if digiLockerDocsErr != nil {
		if rpc.StatusFromError(digiLockerDocsErr).IsRecordNotFound() {
			res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, d.currentStep, d.nextStepOnSuccess)
			res.Deeplink = woHelper.GetRefreshAadhaarDeeplink(details)
			return res, nil
		}
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, d.currentStep, d.nextStepOnSuccess), errors.Wrap(digiLockerDocsErr, "error getting DigiLocker documents")
	}
	mergeErr := mergo.Merge(details, &woPb.OnboardingDetails{Metadata: &woPb.OnboardingMetadata{DigilockerData: digiLockerDocsData}}, mergo.WithOverride)
	if mergeErr != nil {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, d.currentStep, d.nextStepOnSuccess), errors.Wrap(mergeErr, "error merging digilocker data")
	}
	err := populateDigilockerDataInMetadata(details)
	if err != nil {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, d.currentStep, d.nextStepOnSuccess), errors.Wrap(mergeErr, "error populating DigiLocker data on onboarding metadata")
	}
	if woHelper.IsAadhaarCollectedFromDigilocker(details) {
		// remove any pending POA and liveness review attempts because it is not needed if digilocker aadhaar is collected
		removeExistingReviewAttempts(details.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping())
	}
	return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, d.currentStep, d.nextStepOnSuccess), nil
}

// Fetches DigiLocker documents and modifies onboarding details accordingly to add those document related data
// nolint:funlen
func (d *DownloadFromDigilocker) getDigiLockerDocuments(ctx context.Context, actorId string, accessToken string) (*woPb.DigilockerData, error) {
	issuedRes, err := d.digilockerVgClient.GetListOfIssuedDocuments(ctx, &digilockerVgPb.GetListOfIssuedDocumentsRequest{
		Header:      &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_DIGILOCKER},
		AccessToken: accessToken,
	})
	if err = epifigrpc.RPCError(issuedRes, err); err != nil {
		return nil, errors.Wrap(err, "failed to Get Issued Document Response")
	}

	if len(issuedRes.GetDocList()) == 0 {
		return nil, rpc.StatusAsError(rpc.StatusRecordNotFoundWithDebugMsg("No DigiLocker documents found"))
	}

	digilockerData := &woPb.DigilockerData{}
	for _, document := range issuedRes.GetDocList() {
		switch {
		case document.GetDocumentName() == "Aadhaar Card":
			fetchErr := d.getDigiLockerAadhaar(ctx, accessToken, actorId, digilockerData)
			if fetchErr != nil {
				return nil, errors.Wrap(fetchErr, "error getting aadhaar from DigiLocker")
			}
		case document.GetDocumentName() == "PAN Verification Record":
			fetchErr := d.getDigiLockerPAN(ctx, accessToken, document, actorId, digilockerData)
			if fetchErr != nil {
				return nil, errors.Wrap(fetchErr, "error getting PAN from DigiLocker")
			}
		default:
			logger.Debug(ctx, fmt.Sprintf("skipping document: %v", document.GetDocumentName()))
		}
	}
	return digilockerData, nil
}

func (d *DownloadFromDigilocker) getDigiLockerAadhaar(ctx context.Context, accessToken string, actorId string, digilockerData *woPb.DigilockerData) error {
	aadhaarRes, aadhaarErr := d.digilockerVgClient.GetAadhaarInXml(ctx, &digilockerVgPb.GetAadhaarInXmlRequest{
		Header:      &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_DIGILOCKER},
		AccessToken: accessToken,
	})

	if err := epifigrpc.RPCError(aadhaarRes, aadhaarErr); err != nil {
		return errors.Wrap(err, "failed to Get AadhaarInXml from VG")
	}
	fileName := fmt.Sprintf("%v/%v.%v", types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR.String(), "0", "XML")
	s3AadhaarXmlFilePath := filepath.Join(woHelper.AadhaarXMLDir, actorId, uuid.New().String(), fileName)
	if uploadErr := d.docHelper.UploadRawData(ctx, s3AadhaarXmlFilePath, aadhaarRes.GetAadhaarXmlData()); uploadErr != nil {
		return errors.Wrap(uploadErr, "error while uploading bytes array of aadhaar xml data")
	}
	userImage, upErr := d.docHelper.UploadDoc(ctx, actorId, &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PHOTOGRAPH,
		Photo:     []*commontypes.Image{aadhaarRes.GetUserImage()},
	})
	if upErr != nil {
		return errors.Wrap(upErr, "error in uploading user image to s3")
	}
	digilockerData.DigilockerAadhaarData = &woPb.DigilockerAadhaarData{
		Name:                aadhaarRes.GetName(),
		Dob:                 aadhaarRes.GetDob(),
		Gender:              aadhaarRes.GetGender(),
		Address:             aadhaarRes.GetAddress(),
		Ttl:                 aadhaarRes.GetTtl(),
		MaskedAadhaarNumber: aadhaarRes.GetMaskedAadhaarNumber(),
		Ts:                  aadhaarRes.GetTs(),
		UserImageDocument:   userImage,
		AadhaarXmlFilePath:  s3AadhaarXmlFilePath,
		CareOf:              aadhaarRes.GetCareOf(),
		Landmark:            aadhaarRes.GetLandmark(),
	}

	aadhaarPdf, pdfErr := d.kraData.GenerateAadhaarPdf(ctx, actorId, aadhaarRes)
	if pdfErr != nil {
		return errors.Wrap(pdfErr, "failed to GenerateAadhaarPdf")
	}
	digilockerData.AadhaarPdf = aadhaarPdf
	return nil
}

func (d *DownloadFromDigilocker) getDigiLockerPAN(ctx context.Context, accessToken string, document *digilockerVgPb.Documents, actorId string, digilockerData *woPb.DigilockerData) error {
	fileRes, fileErr := d.digilockerVgClient.GetFileFromUri(ctx, &digilockerVgPb.GetFileFromUriRequest{
		Header:      &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_DIGILOCKER},
		AccessToken: accessToken,
		Uri:         document.GetUri(),
	})
	if err := epifigrpc.RPCError(fileRes, fileErr); err != nil {
		return errors.Wrap(err, "failed to Get File from Uri")
	}
	panDoc, upErr := d.docHelper.UploadDoc(ctx, actorId, &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
		Photo:     []*commontypes.Image{fileRes.GetDocumentFile()},
	})
	if upErr != nil {
		return errors.Wrap(upErr, "error in uploading pan to s3")
	}
	digilockerData.PanDocument = panDoc
	return nil
}

// Fields outside DigiLocker data that need modification
func populateDigilockerDataInMetadata(od *woPb.OnboardingDetails) error {
	if od.GetMetadata().GetDigilockerData().GetPanDocument() != nil {
		err := mergo.Merge(od,
			&woPb.OnboardingDetails{
				Metadata: &woPb.OnboardingMetadata{
					PanDetails: &types.DocumentProof{
						S3Paths: od.GetMetadata().GetDigilockerData().GetPanDocument().GetS3Paths(),
					},
				},
			}, mergo.WithOverride)
		if err != nil {
			return errors.Wrap(err, "error merging DigiLocker pan S3 path with existing onboarding details")
		}
	}
	if od.GetMetadata().GetDigilockerData().GetAadhaarPdf() != nil {
		err := mergo.Merge(od, &woPb.OnboardingDetails{
			Metadata: &woPb.OnboardingMetadata{
				PoaDetails: od.GetMetadata().GetDigilockerData().GetAadhaarPdf(),
				PersonalDetails: &woPb.PersonalDetails{
					Gender: od.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData().GetGender(),
				},
			},
		}, mergo.WithOverride)
		if err != nil {
			return errors.Wrap(err, "error merging DigiLocker Aadhaar S3 path with existing onboarding details")
		}
	}
	return nil
}

func removeExistingReviewAttempts(reviewMapping map[string]*woPb.ManualReviewAttempts_ReviewAttemptList) {
	if v, ok := reviewMapping[woPb.ItemType_ITEM_TYPE_AADHAAR_REDACTION.String()]; ok {
		if len(v.GetReviewAttempts()) != 0 {
			if v.GetReviewAttempts()[len(v.GetReviewAttempts())-1].GetStatus() == woPb.ReviewStatus_REVIEW_STATUS_PENDING {
				v.ReviewAttempts = v.GetReviewAttempts()[:len(v.GetReviewAttempts())-1]
			}
		}
	}
	if v, ok := reviewMapping[woPb.ItemType_ITEM_TYPE_DOCUMENT_EXPIRY.String()]; ok {
		if len(v.GetReviewAttempts()) != 0 {
			if v.GetReviewAttempts()[len(v.GetReviewAttempts())-1].GetStatus() == woPb.ReviewStatus_REVIEW_STATUS_PENDING {
				v.ReviewAttempts = v.GetReviewAttempts()[:len(v.GetReviewAttempts())-1]
			}
		}
	}
	if v, ok := reviewMapping[woPb.ItemType_ITEM_TYPE_LIVENESS.String()]; ok {
		if len(v.GetReviewAttempts()) != 0 {
			if v.GetReviewAttempts()[len(v.GetReviewAttempts())-1].GetStatus() == woPb.ReviewStatus_REVIEW_STATUS_PENDING {
				v.ReviewAttempts = v.GetReviewAttempts()[:len(v.GetReviewAttempts())-1]
			}
		}
	}
}
