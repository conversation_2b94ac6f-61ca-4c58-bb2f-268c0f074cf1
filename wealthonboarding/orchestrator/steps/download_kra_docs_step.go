package steps

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"strings"
	"time"

	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/types/known/timestamppb"

	employmentPb "github.com/epifi/gamma/api/employment"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/gamma/wealthonboarding/helper"
	"github.com/epifi/gamma/wealthonboarding/release"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	cvlVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/cvl"
	"github.com/epifi/gamma/api/vendors/wealth"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/kra_data"
)

const (
	expiryAttemptTime     = 7 * 24 * time.Hour // 7 days
	maxDocketReadyTime    = 5 * 24 * time.Hour // 5 days
	invalidDobError       = "invalid date of birth"
	dobMisMatchStatusCode = "05"
)

type DownloadKraDocsStep struct {
	kraDataSvc               kra_data.KraData
	currentStep              woPb.OnboardingStep
	nextStep                 woPb.OnboardingStep
	onboardingStepDetailsDao dao.OnboardingStepDetailsDao
	cvlVgClient              cvlVgPb.CvlClient
	s3Client                 s3.S3Client
	conf                     *config.Config
	commonHelper             *helper.CommonHelper
	releaseEvaluator         release.IEvaluator
}

func NewDownloadKraDocketStep(
	kraDataSvc kra_data.KraData,
	stepDetailsDao dao.OnboardingStepDetailsDao,
	cvlVgClient cvlVgPb.CvlClient,
	s3Client s3.S3Client,
	conf *config.Config,
	commonHelper *helper.CommonHelper,
	releaseEvaluator release.IEvaluator) *DownloadKraDocsStep {
	return &DownloadKraDocsStep{
		kraDataSvc:               kraDataSvc,
		currentStep:              woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC,
		onboardingStepDetailsDao: stepDetailsDao,
		cvlVgClient:              cvlVgClient,
		s3Client:                 s3Client,
		conf:                     conf,
		commonHelper:             commonHelper,
		releaseEvaluator:         releaseEvaluator,
	}
}

//nolint:dupl
func (d *DownloadKraDocsStep) Perform(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	osd, err := d.onboardingStepDetailsDao.GetByOnboardingDetailsIdAndStep(ctx, details.GetId(), woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, d.currentStep, d.nextStep), errors.Wrap(err, "failed to fetch liveness step details")
	}
	if osd != nil && woPb.IsTerminalStatus(osd.GetStatus()) {
		return GetStepExecutionResponse(details, osd.GetStatus(), d.currentStep, d.nextStep).WithIsLastStep(true), nil
	}
	return d.downloadKraDoc(ctx, details)
}

//nolint:funlen
func (d *DownloadKraDocsStep) downloadKraDoc(ctx context.Context, od *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	if od.GetMetadata().GetDownloadedKraDocData() == nil {
		od.GetMetadata().DownloadedKraDocData = &woPb.DownloadedKraDocData{}
	}
	dwnData := od.GetMetadata().GetDownloadedKraDocData()
	var dwnAttempt *woPb.DownloadedKraDocData_DownloadAttempt
	if createNewAttempt(dwnData.GetDownloadAttempts()) {
		dwnAttempt = &woPb.DownloadedKraDocData_DownloadAttempt{
			AttemptId: uuid.New().String(),
			CreatedAt: timestamppb.Now(),
		}
		dwnData.DownloadAttempts = append(dwnData.DownloadAttempts, dwnAttempt)
	} else {
		dwnAttempt = dwnData.GetDownloadAttempts()[len(dwnData.GetDownloadAttempts())-1]
	}

	zapReqId := zap.String(logger.ATTEMPT_ID, dwnAttempt.GetAttemptId())
	dwnStatus := dwnAttempt.GetStatus()

	switch dwnStatus {
	case woPb.DownloadedKraDocData_DownloadAttempt_DOWNLOAD_STATUS_UNSPECIFIED:
		logger.Info(ctx, "initiating fetch api call for preparing download docs", zapReqId)
		panNo := od.GetMetadata().GetPanDetails().GetId()
		dob := getDobFromCpdOrMetadata(od)
		kraCode := od.GetMetadata().GetKraData().GetStatusData().GetPanStatusDetails().GetKraCode()
		if kraCode == wealth.KraCode_KRA_CODE_TYPE_UNSPECIFIED {
			// need to use pan status api and populate KRA_CODE
			pkcErr := d.populateKraCode(ctx, od)
			if pkcErr != nil {
				if errors.Is(pkcErr, epifierrors.ErrDowntimeExpected) {
					return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, d.currentStep, d.nextStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(pkcErr), errors.Wrap(pkcErr, "pan status fetch failed")
				}
				return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, d.currentStep, d.nextStep), errors.Wrap(pkcErr, "pan status fetch failed")
			}
			// assign the recently updated kra_code
			kraCode = od.GetMetadata().GetKraData().GetStatusData().GetPanStatusDetails().GetKraCode()
		}
		fetchRes, fetchErr := d.cvlVgClient.PanDetailsFetch(ctx, &cvlVgPb.PanDetailsFetchRequest{
			Header:    &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_CVLKRA},
			PanNumber: panNo,
			DobIncorp: datetime.DateToTime(dob, nil).Format(kra_data.TimeFormat),
			KraCode:   kraCode,
			FetchType: wealth.KraFetchType_KRA_FETCH_TYPE_IMAGE_AND_DATA,
		})
		if isInvalidDobError(fetchRes) {
			return d.getPanUploadFlowResponse(ctx, od)
		}
		if te := epifigrpc.IsRPCErrorWithDowntime(fetchRes, fetchErr); te != nil {
			if errors.Is(te, epifierrors.ErrDowntimeExpected) {
				return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, d.currentStep, d.nextStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(te), errors.Wrap(fetchErr, "fetch api failed with downtime")
			}
			return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, d.currentStep, d.nextStep), errors.Wrap(te, "fetch api failed")
		}
		kraDownloadData := helper.GetKraDownloadData(fetchRes.GetPanDetails())
		dwnAttempt.KraDownloadedData = kraDownloadData
		od.GetMetadata().GetKraData().DownloadedData = kraDownloadData
		dwnAttempt.FolderDate = fetchRes.GetFolderDate()
		dwnAttempt.FolderTime = fetchRes.GetFolderTime()
		dwnAttempt.TotalRecords = fetchRes.GetTotalNoOfRecords()
		dwnAttempt.Status = woPb.DownloadedKraDocData_DownloadAttempt_DOWNLOAD_STATUS_PENDING
		// check if employment data is present either from kra or data_collection_step
		if !isValidEmploymentData(od, kraDownloadData) {
			logger.Info(ctx, "no valid employment data found for user, assigning it as others", zapReqId)
			kraDownloadData.GetPanDetails().Occ = wealth.KraOccupation_KRA_OCCUPATION_OTHERS
		}
		return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, d.currentStep, d.nextStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_KRA_DOCKET_DOWNLOAD_NOT_READY_YET), nil
	case woPb.DownloadedKraDocData_DownloadAttempt_DOWNLOAD_STATUS_PENDING:
		logger.Info(ctx, "download initiated, trying to download", zapReqId)
		kraType := fetchKraInfoFromDetails(od)
		dwnRes, dwnErr := d.kraDataSvc.DownloadKraData(ctx, od, dwnAttempt.GetCreatedAt(), kraType)
		if dwnErr != nil {
			if errors.Is(dwnErr, epifierrors.ErrRecordNotFound) {
				if time.Since(dwnAttempt.GetCreatedAt().AsTime()) > maxDocketReadyTime {
					logger.Info(ctx, "docket not ready, follow up with cvl", zap.String("created_at", dwnAttempt.GetCreatedAt().String()))
				}
				return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, d.currentStep, d.nextStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_KRA_DOCKET_DOWNLOAD_NOT_READY_YET), nil
			}
			return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, d.currentStep, d.nextStep), errors.Wrap(dwnErr, "sftp download failed")
		}
		dwnAttempt.Status = woPb.DownloadedKraDocData_DownloadAttempt_DOWNLOAD_STATUS_COMPLETED
		dwnAttempt.DocAsProof = dwnRes
		if !helper.IsIndianAsPerKraPanDetails(dwnAttempt.GetKraDownloadedData().GetPanDetails()) {
			logger.Info(ctx, "marking onboarding failed for non-indian user", zap.String(logger.ACTOR_ID_V2, od.GetActorId()), zap.String(logger.ATTEMPT_ID, dwnAttempt.GetAttemptId()))
			return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_FAILED, d.currentStep, d.nextStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSUPPORTED_NATIONALITY), nil
		}
		return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, d.currentStep, d.nextStep).WithIsLastStep(true), nil
	case woPb.DownloadedKraDocData_DownloadAttempt_DOWNLOAD_STATUS_COMPLETED:
		return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, d.currentStep, d.nextStep).WithIsLastStep(true), nil
	case woPb.DownloadedKraDocData_DownloadAttempt_DOWNLOAD_STATUS_FAILED:
		logger.Info(ctx, "re-initiating download")
		dwnAttempt = &woPb.DownloadedKraDocData_DownloadAttempt{
			AttemptId: uuid.New().String(),
			CreatedAt: timestamppb.Now(),
		}
		dwnData.DownloadAttempts = append(dwnData.DownloadAttempts, dwnAttempt)
	}
	logger.Info(ctx, "new download attempt created", zap.String(logger.ATTEMPT_ID, dwnAttempt.GetAttemptId()))
	return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, d.currentStep, d.nextStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_KRA_DOCKET_DOWNLOAD_NOT_READY_YET), nil
}

// getDobFromCpdOrMetadata returns the dob of the customer based on the dob present in customer provided data or not
func getDobFromCpdOrMetadata(od *woPb.OnboardingDetails) *date.Date {
	cpd := od.GetMetadata().GetCustomerProvidedData()
	if len(cpd.GetPanOcrAttempts()) != 0 {
		latestOcrPan := cpd.GetPanOcrAttempts()[len(cpd.GetPanOcrAttempts())-1]
		if latestOcrPan.GetOcrDocumentProof().GetDoc().GetDob() != nil {
			return latestOcrPan.GetOcrDocumentProof().GetDoc().GetDob()
		}
	}
	// get dob from pan if already exists in pan details
	if od.GetMetadata().GetPanDetails().GetDob() != nil {
		return od.GetMetadata().GetPanDetails().GetDob()
	}
	// no customer provided pan attempt is present
	return od.GetMetadata().GetPersonalDetails().GetDob()
}

func (d *DownloadKraDocsStep) getPanUploadDeepLink(od *woPb.OnboardingDetails) *deeplinkPb.Deeplink {
	var mdl []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus
	// The below function does not return pan upload deeplink if pan image already exists in db (from digilocker or ckyc)
	// in case we could not extract DOB from existing pan image and dob from personal detail mismatches with KRA records, we have to get DOB from user uploaded pan image
	mdl = collectPanDetails(od, od.GetMetadata().GetCustomerProvidedData(), mdl, d.conf, true)
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_CAPTURE_MISSING_DATA_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingCaptureMissingDataScreenOptions{
			WealthOnboardingCaptureMissingDataScreenOptions: &deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions{
				MissingData: mdl,
			},
		},
	}
}

// fetchKraInfoFromDetails fetches the kra code of the user either from download api data or status api data
func fetchKraInfoFromDetails(od *woPb.OnboardingDetails) string {
	downloadKraCode := od.GetMetadata().GetKraData().GetDownloadedData().GetPanDetails().GetKraInfo()
	statusKraCode := od.GetMetadata().GetKraData().GetStatusData().GetPanStatusDetails().GetKraCode()
	if downloadKraCode != wealth.KraCode_KRA_CODE_TYPE_UNSPECIFIED {
		return getKraType(downloadKraCode)
	}
	return getKraType(statusKraCode)
}

func (d *DownloadKraDocsStep) populateKraCode(ctx context.Context, od *woPb.OnboardingDetails) error {
	logger.Info(ctx, "populating kraCode using pan status api")
	psRes, psErr := d.cvlVgClient.GetPanStatus(ctx, &cvlVgPb.GetPanStatusRequest{
		Header:    &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_CVLKRA},
		PanNumber: od.GetMetadata().GetPanDetails().GetId(),
	})
	if te := epifigrpc.IsRPCErrorWithDowntime(psRes, psErr); te != nil {
		return errors.Wrap(te, "fail to fetch pan status from vendor")
	}
	// ideally pan status data should be populated by this point, we only need to update the KraCode here
	if od.GetMetadata().GetKraData().GetStatusData().GetPanStatusDetails() == nil {
		logger.Error(ctx, "unable to get pan status details from kra status data, skipping populating kra code", zap.String(logger.ACTOR_ID_V2, od.GetActorId()))
		return fmt.Errorf("unable to get pan status details from kra status data, skipping populating kra code")
	}
	od.GetMetadata().GetKraData().GetStatusData().GetPanStatusDetails().KraCode = psRes.GetPanEnquiry().GetKraCode()
	return nil
}

func (d *DownloadKraDocsStep) isCpdPanBelowThreshold(cpd *woPb.CustomerProvidedData) (bool, error) {
	if len(cpd.GetPanOcrAttempts()) == 0 {
		return false, nil
	}
	latestOcrPan := cpd.GetPanOcrAttempts()[len(cpd.GetPanOcrAttempts())-1]
	panThreshold, ok := d.conf.OCRThresholdScoreConfig[types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN.String()]
	if !ok {
		return false, errors.New("no pan ocr threshold present in config")
	}
	return latestOcrPan.GetOcrDocumentProof().GetConfidenceScore() < panThreshold, nil
}

func (d *DownloadKraDocsStep) getPanUploadFlowResponse(_ context.Context, od *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	// check if no of pan attempts are exhausted
	cpd := od.GetMetadata().GetCustomerProvidedData()
	if len(cpd.GetPanOcrAttempts()) >= d.conf.MaxPanUploadAttempts {
		return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, d.currentStep, d.nextStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_PAN_UPLOAD_ATTEMPTS_EXHAUSTED),
			errors.New("fetch api failed and pan attempts are exhausted")
	}
	isBelowThreshold, thresholdErr := d.isCpdPanBelowThreshold(cpd)
	if thresholdErr != nil {
		return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, d.currentStep, d.nextStep), errors.Wrap(thresholdErr, "failed to determine below threshold flag")
	}
	// check if we need to re-capture pan based on the threshold score
	if len(cpd.GetPanOcrAttempts()) == 0 || isBelowThreshold {
		res := GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, d.currentStep, d.nextStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_EMPTY_DOWNLOAD_API_RESPONSE)
		res.Deeplink = d.getPanUploadDeepLink(od)
		return res, nil
	}
	return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, d.currentStep, d.nextStep), errors.New("fetch api failed even after pan capture from user")
}

// isValidEmploymentData returns whether we have a valid employment data for a user
func isValidEmploymentData(od *woPb.OnboardingDetails, kraData *woPb.KraDownloadedData) bool {
	if od.GetMetadata().GetEmploymentData().GetEmploymentType() != employmentPb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED {
		return true
	}
	// occupation should not be unspecified in case of no employment data from data collection step
	return kraData.GetPanDetails().GetOcc() != wealth.KraOccupation_KRA_OCCUPATION_UNSPECIFIED
}

func getKraType(kraCode wealth.KraCode) string {
	switch kraCode {
	case wealth.KraCode_KRA_CODE_CVLKRA:
		return "CVLKRA"
	case wealth.KraCode_KRA_CODE_NDML:
		return "NDML"
	case wealth.KraCode_KRA_CODE_DOTEX:
		return "DOTEX"
	case wealth.KraCode_KRA_CODE_CAMS:
		return "CAMS"
	case wealth.KraCode_KRA_CODE_KARVY:
		return "KARVY"
	default:
		return "VENDOR_CODE_UNSPECIFIED"
	}
}

// createNewAttempt returns a bool of whether a new attempt for the download should be created or not
func createNewAttempt(attempts []*woPb.DownloadedKraDocData_DownloadAttempt) bool {
	if len(attempts) == 0 {
		return true
	}
	latestAttempt := attempts[len(attempts)-1]
	// no created at date present in the attempt
	if latestAttempt.CreatedAt == nil {
		return true
	}
	ct := latestAttempt.CreatedAt.AsTime()
	return time.Since(ct) > expiryAttemptTime
}

func isInvalidDobError(res *cvlVgPb.PanDetailsFetchResponse) bool {
	// in case the request is sent with invalid dob, CVL API response can be empty or have error message in remarks
	return res.GetStatus().IsInvalidArgument() ||
		strings.Contains(strings.ToLower(res.GetPanDetails().GetAppRemarks()), invalidDobError) ||
		res.GetPanDetails().GetStatus() == dobMisMatchStatusCode ||
		strings.Contains(strings.ToLower(res.GetPanDetails().GetErrorDesc()), invalidDobError)
}
