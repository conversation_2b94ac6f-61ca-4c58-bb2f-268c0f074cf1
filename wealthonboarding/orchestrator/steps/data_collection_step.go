package steps

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	types "github.com/epifi/gamma/api/typesv2"

	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	userPb "github.com/epifi/gamma/api/user"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woCkyc "github.com/epifi/gamma/wealthonboarding/ckyc"
	"github.com/epifi/gamma/wealthonboarding/config"
	woErr "github.com/epifi/gamma/wealthonboarding/errors"
)

type DataCollectionStep struct {
	conf              *config.Config
	userClient        userPb.UsersClient
	actorClient       actorPb.ActorClient
	currentStep       woPb.OnboardingStep
	nextStepOnSuccess woPb.OnboardingStep
	ckycService       *woCkyc.Service
}

func NewDataCollectionStep(
	conf *config.Config,
	userClient userPb.UsersClient,
	actorClient actorPb.ActorClient,
	ckycService *woCkyc.Service,
) *DataCollectionStep {
	return &DataCollectionStep{
		conf:              conf,
		userClient:        userClient,
		actorClient:       actorClient,
		ckycService:       ckycService,
		currentStep:       woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
		nextStepOnSuccess: woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
	}
}

func (d *DataCollectionStep) Perform(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	if d.validateMandatoryDetails(details) {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, d.currentStep, d.nextStepOnSuccess), nil
	}
	err := d.populatePreCollectedPersonalDetails(ctx, details)
	if err != nil {
		logger.Error(ctx, "error populating personal details", zap.Error(err))
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, d.currentStep, d.nextStepOnSuccess), err
	}
	if !d.validateMandatoryDetails(details) {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, d.currentStep, d.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_MANDATORY_DETAILS_MISSING_IN_FI), nil
	}

	if d.conf.DisableCKYC {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, d.currentStep, d.nextStepOnSuccess), nil
	}

	sd, sdErr := d.ckycService.CreateOrGetSearchData(ctx, details.GetActorId(), details.GetMetadata().GetPanDetails().GetId())
	if sdErr != nil {
		switch {
		case errors.Is(sdErr, epifierrors.ErrRecordNotFound):
			logger.Info(ctx, "ckyc record not found")
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, d.currentStep, d.nextStepOnSuccess), nil
		case errors.Is(sdErr, epifierrors.ErrDowntimeExpected):
			logger.Info(ctx, "downtime expected for vendor")
			// temporarily completing the step so as to not block verified users
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, d.currentStep, d.nextStepOnSuccess), nil
			// return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, d.currentStep, d.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(sdErr), nil
		default:
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, d.currentStep, d.nextStepOnSuccess), errors.Wrap(sdErr, "error while fetching ckyc search data")
		}
	}
	if sd.GetConstitutionType() != "" {
		logger.Info(ctx, "ckyc entity type is non individual")
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, d.currentStep, d.nextStepOnSuccess), nil
	}
	_, ddErr := d.ckycService.CreateOrGetDownloadData(ctx, details.GetActorId(), details.GetMetadata().GetPersonalDetails().GetDob())
	if ddErr != nil {
		switch {
		case errors.Is(ddErr, woErr.ErrCkycDobMismatch):
			logger.Info(ctx, "dob mismatch error in ckyc download response")
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, d.currentStep, d.nextStepOnSuccess), nil
		case errors.Is(ddErr, epifierrors.ErrDowntimeExpected):
			logger.Info(ctx, "downtime expected for vendor")
			// temporarily completing the step so as to not block verified users
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, d.currentStep, d.nextStepOnSuccess), nil
			// return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, d.currentStep, d.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(ddErr), nil
		default:
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, d.currentStep, d.nextStepOnSuccess), errors.Wrap(ddErr, "error while fetching ckyc download data")
		}
	}
	return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, d.currentStep, d.nextStepOnSuccess), nil
}

func (d *DataCollectionStep) populatePreCollectedPersonalDetails(ctx context.Context, details *woPb.OnboardingDetails) error {
	actorRes, err := d.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: details.GetActorId()})
	if te := epifigrpc.RPCError(actorRes, err); te != nil {
		return fmt.Errorf("failed to get actor for actor-id: %s, err: %v, %w", details.GetActorId(), te.Error(), epifierrors.ErrTransient)
	}

	userRes, err := d.userClient.GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_Id{Id: actorRes.GetActor().GetEntityId()}})
	if te := epifigrpc.RPCError(userRes, err); te != nil {
		return fmt.Errorf("failed to fetch user for user-id: %s, err: %v, %w", actorRes.GetActor().GetEntityId(), te.Error(), epifierrors.ErrTransient)
	}

	if details.GetMetadata() == nil {
		details.Metadata = &woPb.OnboardingMetadata{}
	}
	if details.GetMetadata().GetPersonalDetails() == nil {
		details.GetMetadata().PersonalDetails = &woPb.PersonalDetails{}
	}
	pd := details.GetMetadata().GetPersonalDetails()
	pf := userRes.GetUser().GetProfile()
	pd.Name = pf.GetPanName()
	pd.Email = pf.GetEmail()
	pd.PhoneNumber = pf.GetPhoneNumber()
	pd.Dob = pf.GetDateOfBirth()
	pd.FatherName = pf.GetFatherName()
	pd.MotherName = pf.GetMotherName()
	pd.Photo = &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PHOTOGRAPH,
		Photo: []*commontypes.Image{
			pf.GetPhoto(),
		},
	}
	if details.GetMetadata().GetPanDetails() == nil {
		details.GetMetadata().PanDetails = &types.DocumentProof{}
	}
	panDetails := details.GetMetadata().GetPanDetails()
	panDetails.Id = pf.GetPAN()
	panDetails.ProofType = types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN

	// populate salary slab
	pd.IncomeSlab = d.getIncomeSlabBySalaryRange(pf.GetSalaryRange())
	return nil
}

func (d *DataCollectionStep) getIncomeSlabBySalaryRange(salaryRange *userPb.SalaryRange) types.IncomeSlab {
	if salaryRange == nil {
		return types.IncomeSlab_INCOME_SLAB_UNSPECIFIED
	}
	switch {
	default:
		return types.IncomeSlab_INCOME_SLAB_UNSPECIFIED
	case salaryRange.MaxValue <= 100000:
		return types.IncomeSlab_INCOME_SLAB_BELOW_1_LAC
	case salaryRange.MinValue >= 100000 && salaryRange.MaxValue <= 500000:
		return types.IncomeSlab_INCOME_SLAB_1_TO_5_LAC
	case salaryRange.MinValue >= 500000 && salaryRange.MaxValue <= 1000000:
		return types.IncomeSlab_INCOME_SLAB_5_TO_10_LAC
	case salaryRange.MinValue >= 1000000 && salaryRange.MaxValue <= 2500000:
		return types.IncomeSlab_INCOME_SLAB_10_TO_25_LAC
	case salaryRange.MinValue >= 2500000 && salaryRange.MaxValue <= 10000000:
		return types.IncomeSlab_INCOME_SLAB_25_LAC_TO_1_CRORE
	case salaryRange.MinValue >= 10000000:
		return types.IncomeSlab_INCOME_SLAB_ABOVE_1_CRORE
	}
}

func (d *DataCollectionStep) validateMandatoryDetails(details *woPb.OnboardingDetails) bool {
	pd := details.GetMetadata().GetPersonalDetails()
	if pd == nil || pd.GetName() == nil || pd.GetDob() == nil || pd.GetFatherName() == nil || pd.GetMotherName() == nil {
		return false
	}
	return true
}
