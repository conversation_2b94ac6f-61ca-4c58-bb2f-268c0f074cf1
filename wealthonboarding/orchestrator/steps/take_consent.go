package steps

import (
	"github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/names"

	"github.com/epifi/gamma/api/consent"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	types "github.com/epifi/gamma/api/typesv2"
	obfuscatorPb "github.com/epifi/gamma/api/user/obfuscator"
	cvlVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/cvl"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/helper"
	"github.com/epifi/gamma/wealthonboarding/kra_data"
	"github.com/epifi/gamma/wealthonboarding/release"

	"github.com/pkg/errors"
)

var (
	TncURL                                   = "https://fi.money/wealth/TnC"
	TnCScreenTitle                           = "Welcome onboard!"
	TnCConsentTakenProgressBarDurationInSecs = int32(8)
	TncScreenDescrDownloadKRAConsentTaken    = "Your investment account is almost ready.\nYou'll receive an email from CVL KRA stating Epifi Wealth Pvt Ltd has fetched your details"
	TncScreenDescrDownloadKRAConsentNotTaken = "Your investment account is getting ready.\nYou’ll be receiving a confirmation mail from CVL KRA stating Epifi Wealth Pvt Ltd has fetched your details "
	TncScreenDescrUploadKRAConsentTaken      = "Your investment account is almost ready.\nYou'll receive an email from CVL KRA stating Epifi Wealth Pvt Ltd  has created your KYC record"
	TncScreenDescrUploadKRAConsentNotTaken   = "Your investment account is getting ready.\nYou’ll be receiving a confirmation mail from CVL KRA stating Epifi Wealth Pvt Ltd has created your KYC record"
)

type TakeConfirmationOnPepAndCitizenship struct {
	currentStep              woPb.OnboardingStep
	nextStepOnSuccess        woPb.OnboardingStep
	conf                     *config.Config
	consentClient            consent.ConsentClient
	docHelper                helper.DocumentHelper
	cvlKraDataSvc            kra_data.KraData
	commonHelper             helper.ICommonHelper
	onboardingStepDetailsDao dao.OnboardingStepDetailsDao
	releaseEvaluator         release.IEvaluator
	obfuscatorClient         obfuscatorPb.ObfuscatorClient
	cvlVgClient              cvlVgPb.CvlClient
}

func NewTakeConfirmationOnPepAndCitizenship(
	conf *config.Config,
	consentClient consent.ConsentClient,
	cvlKraDataSvc kra_data.KraData,
	docHelper helper.DocumentHelper,
	commonHelper helper.ICommonHelper,
	onboardingStepDetailsDao dao.OnboardingStepDetailsDao,
	releaseEvaluator release.IEvaluator,
	obfuscatorClient obfuscatorPb.ObfuscatorClient,
	cvlVgClient cvlVgPb.CvlClient) *TakeConfirmationOnPepAndCitizenship {
	return &TakeConfirmationOnPepAndCitizenship{
		currentStep:              woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
		conf:                     conf,
		consentClient:            consentClient,
		docHelper:                docHelper,
		cvlKraDataSvc:            cvlKraDataSvc,
		commonHelper:             commonHelper,
		onboardingStepDetailsDao: onboardingStepDetailsDao,
		releaseEvaluator:         releaseEvaluator,
		obfuscatorClient:         obfuscatorClient,
		cvlVgClient:              cvlVgClient,
		nextStepOnSuccess:        woPb.OnboardingStep_ONBOARDING_STEP_USER_MITC_CONSENT,
	}
}

func (t *TakeConfirmationOnPepAndCitizenship) Perform(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	osd, err := t.onboardingStepDetailsDao.GetByOnboardingDetailsIdAndStep(ctx, details.GetId(), woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error getting onboarding step details", zap.Error(err))
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, t.currentStep, t.nextStepOnSuccess), errors.Wrap(err, "failed to fetch pep step details")
	}
	if osd != nil && woPb.IsTerminalStatus(osd.GetStatus()) {
		return GetStepExecutionResponse(details, osd.GetStatus(), t.currentStep, t.nextStepOnSuccess).WithIsLastStep(isTnCLastStep(details)), nil
	}

	fcRes, fcErr := t.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ConsentType: consent.ConsentType_FI_WEALTH_TNC,
		ActorId:     details.GetActorId(),
		Owner:       common.Owner_OWNER_EPIFI_TECH,
	})
	if te := epifigrpc.RPCError(fcRes, fcErr); te != nil {
		logger.Error(ctx, "error getting consent", zap.Error(te))
		if fcRes.GetStatus().IsRecordNotFound() {
			res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, t.currentStep, t.nextStepOnSuccess)
			dl, dlErr := t.getTnCDeeplink(ctx, details, false)
			if dlErr != nil {
				if errors.Is(dlErr, epifierrors.ErrDowntimeExpected) {
					return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, t.currentStep, t.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(dlErr), dlErr
				}
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, t.currentStep, t.nextStepOnSuccess), dlErr
			}
			res.Deeplink = dl
			return res, nil
		}
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, t.currentStep, t.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CONSENT_FETCH_FAILED), errors.Wrap(te, "error while fetching consent")
	}

	// client is expected to call ConfirmComplianceData RPC at least once regardless of whether wealth consent was taken during bank onboarding or not
	// below condition is entered only when client has not called ConfirmComplianceData RPC and thus not shown the TnC screen, hence the TnC deeplink is returned inside the condition
	// once ConfirmComplianceData RPC is called, nationality would have been set and thus condition would not be entered into
	if details.GetMetadata().GetPersonalDetails().GetNationality() == types.Nationality_NATIONALITY_UNSPECIFIED {
		res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, t.currentStep, t.nextStepOnSuccess)
		dl, dlErr := t.getTnCDeeplink(ctx, details, true)
		if dlErr != nil {
			if errors.Is(dlErr, epifierrors.ErrDowntimeExpected) {
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, t.currentStep, t.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(dlErr), dlErr
			}
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, t.currentStep, t.nextStepOnSuccess), dlErr
		}
		res.Deeplink = dl
		return res, nil
	}

	processErr := t.processConsentTaken(ctx, details, fcRes.GetIpAddressToken())
	if processErr != nil {
		logger.Error(ctx, "error processing consent taken", zap.Error(processErr))
		if errors.Is(processErr, epifierrors.ErrDowntimeExpected) {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, t.currentStep, t.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(processErr), processErr
		}
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, t.currentStep, t.nextStepOnSuccess), errors.Wrap(processErr, "failed to execute step")
	}
	return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, t.currentStep, t.nextStepOnSuccess).WithIsLastStep(isTnCLastStep(details)), nil
}

func (t *TakeConfirmationOnPepAndCitizenship) processConsentTaken(ctx context.Context, details *woPb.OnboardingDetails, ipAddressToken string) error {
	recordTnCDefaultValue(details)

	// capture customer's ip at the time of consent and store
	ip, ipErr := t.fetchCustomerIpWhenConsentTaken(ctx, ipAddressToken)
	if ipErr != nil {
		return ipErr
	}
	details.GetMetadata().CustomerIpAddress = ip

	logger.Info(ctx, "signing agreement")
	signErr := t.signAgreement(ctx, details)
	if signErr != nil {
		return signErr
	}
	storeBdErr := t.storeBankDetails(ctx, details)
	if storeBdErr != nil {
		return storeBdErr
	}
	logger.Info(ctx, "signing agreement done")
	updIncomeErr := t.commonHelper.UpdateIncomeSlabForUser(ctx, details)
	if updIncomeErr != nil {
		return updIncomeErr
	}
	return nil
}

// recordTnCDefaultValue stores the default fields like nationality, politically exposed status and residential status
func recordTnCDefaultValue(details *woPb.OnboardingDetails) {
	details.GetMetadata().GetPersonalDetails().Nationality = types.Nationality_NATIONALITY_INDIAN
	details.GetMetadata().GetPersonalDetails().PoliticallyExposedStatus = types.PoliticallyExposedStatus_POLITICALLY_EXPOSED_STATUS_NOT_APPLICABLE
	details.GetMetadata().GetPersonalDetails().ResidentialStatus = types.ResidentialStatus_RESIDENT_INDIVIDUAL
}

func (t *TakeConfirmationOnPepAndCitizenship) signAgreement(ctx context.Context, details *woPb.OnboardingDetails) error {
	if details.GetMetadata().GetAgreementDocketInfo() == nil {
		details.GetMetadata().AgreementDocketInfo = &woPb.DocketInfo{}
	}
	// either one of the following would be passed for pdf generation
	signData, customerFullName := "", ""
	if details.GetMetadata().GetCustomerProvidedData().GetSignature() != nil {
		// signature is stored in s3, fetch the base64 version of it
		sign, err := t.docHelper.DownloadDoc(ctx, details.GetMetadata().GetCustomerProvidedData().GetSignature())
		if err != nil {
			return errors.Wrap(err, "error while downloading signed doc using helper svc")
		}
		// check if there are any images present of the signature
		if len(sign.GetPhoto()) == 0 {
			return errors.New("no signature found")
		}
		signData = sign.GetPhoto()[0].GetImageDataBase64()
	} else {
		customerFullName = names.ToString(details.GetMetadata().GetPersonalDetails().GetName())
	}
	// create the agreement using the docs service
	agreementPdf, err := t.cvlKraDataSvc.GenerateAgreementPdf(ctx, details.GetActorId(), signData, customerFullName)
	if err != nil {
		return errors.Wrap(err, "error in generating agreement pdf using docs service")
	}
	details.GetMetadata().GetAgreementDocketInfo().DocAsProof = agreementPdf
	return nil
}

// storeBankDetails calls the savings service for fetching the account number and ifsc code of a user's bank account
// current it is called in the OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP step, but can be segregated into another new step
func (t *TakeConfirmationOnPepAndCitizenship) storeBankDetails(ctx context.Context, details *woPb.OnboardingDetails) error {
	if details.GetMetadata().GetBankDetails() == nil {
		details.GetMetadata().BankDetails = &woPb.BankDetails{}
	}
	logger.Info(ctx, "populating bank account details for the user")
	bankDetails, err := t.commonHelper.FetchBankAccountDetails(ctx, details.GetActorId())
	if err != nil {
		return errors.Wrap(err, "error while fetching bank details in wonb")
	}
	// store bank details
	details.GetMetadata().BankDetails = bankDetails
	return nil
}

func isIpValid(ip string) bool {
	return ip != "127.0.0.1"
}

// Note: Title and description are overridden outside orchestrator in certain cases to maintain backward compatibility, ref: wealthonboarding.enrichTnCScreenDeeplink
// Vendor API call made to check the next step can result in epifierrors.ErrDowntimeExpected, callers are expected to handle this separately if needed
func (t *TakeConfirmationOnPepAndCitizenship) getTnCDeeplink(ctx context.Context, details *woPb.OnboardingDetails, isConsentTaken bool) (*deeplinkPb.Deeplink, error) {
	dl := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_AGREEMENT_CONFIRMATION_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingSignAgreementScreenOptions{
			WealthOnboardingSignAgreementScreenOptions: &deeplinkPb.WealthOnboardingSignAgreementScreenOptions{
				TncUrl:         TncURL, // keeping got backward compatibility. Client going forward uses consents field instead
				IsConsentTaken: isConsentTaken,
			},
		},
	}

	// since isNextStepUploadKRA is used only to change title in deeplink we need not get the latest pan status to get next step
	// we can use IsFreshKra to determine this
	isNextStepUploadKRA, checkNextStepErr := t.checkIfNextStepIsUploadKRA(ctx, details)
	if checkNextStepErr != nil {
		logger.Error(ctx, "error checking next step", zap.Error(checkNextStepErr))
		return nil, errors.Wrap(checkNextStepErr, "error checking next step")
	}

	so := dl.GetWealthOnboardingSignAgreementScreenOptions()
	so.Title = TnCScreenTitle
	so.IllustrationUrl = helper.FinishLineFlagIllustrationURL
	if isNextStepUploadKRA {
		if isConsentTaken {
			so.Description = TncScreenDescrUploadKRAConsentTaken
			so.ProgressBarDurationInSecs = TnCConsentTakenProgressBarDurationInSecs
		} else {
			so.Description = TncScreenDescrUploadKRAConsentNotTaken
		}
	} else {
		if isConsentTaken {
			so.Description = TncScreenDescrDownloadKRAConsentTaken
			so.ProgressBarDurationInSecs = TnCConsentTakenProgressBarDurationInSecs
		} else {
			so.Description = TncScreenDescrDownloadKRAConsentNotTaken
		}
	}
	return dl, nil
}

func (t *TakeConfirmationOnPepAndCitizenship) fetchCustomerIpWhenConsentTaken(ctx context.Context, ipAddressToken string) (string, error) {
	ipRes, err := t.obfuscatorClient.GetPIIFromToken(ctx, &obfuscatorPb.GetPIIFromTokenRequest{
		Token: ipAddressToken,
	})
	if te := epifigrpc.RPCError(ipRes, err); te != nil {
		// logging the error and falling back to previous logic of ip fetch from context
		logger.Error(ctx, "error while fetching ip from obfuscator client", zap.Error(te))
	}
	if ipRes.GetPii().GetIdValue().GetIpAddress().GetIpAddress() == "" {
		// fallback to earlier logic to fetch ip from context
		logger.Info(ctx, "falling back to older logic for capturing ip address")
		ip := helper.ParseUsersIp(epificontext.XFwdForAddrFromContext(ctx))
		if isIpValid(ip) {
			return ip, nil
		}
		return "", fmt.Errorf("invalid ip: %v", ip)
	}
	return ipRes.GetPii().GetIdValue().GetIpAddress().GetIpAddress(), nil
}

func isTnCLastStep(od *woPb.OnboardingDetails) bool {
	if od.GetMetadata().GetIsFreshKra() && len(od.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping()) > 0 {
		for _, v := range od.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping() {
			if v != nil && v.GetReviewAttempts() != nil && len(v.GetReviewAttempts()) > 0 {
				// get the latest attempt for each key
				attempt := v.GetReviewAttempts()[len(v.GetReviewAttempts())-1]
				// if any of the review attempt status is not approved and edited, manual review step is not complete
				if attempt.GetStatus() != woPb.ReviewStatus_REVIEW_STATUS_APPROVED && attempt.GetStatus() != woPb.ReviewStatus_REVIEW_STATUS_EDITED {
					return false
				}
			}
		}
	}
	// if user is not fresh KRA or review items are approved or edited, manual review step is executed and TnC step is last
	return true
}

func (t *TakeConfirmationOnPepAndCitizenship) checkIfNextStepIsUploadKRA(ctx context.Context, details *woPb.OnboardingDetails) (bool, error) {
	psRes, psResErr := t.cvlVgClient.GetPanStatus(ctx, &cvlVgPb.GetPanStatusRequest{
		Header:    &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_CVLKRA},
		PanNumber: details.GetMetadata().GetPanDetails().GetId(),
	})
	if te := epifigrpc.RPCError(psRes, psResErr); te != nil {
		logger.Info(ctx, "error getting pan status", zap.Error(te))
		// we are not returning error here since we fall back to existing KRA status
		return details.GetMetadata().GetIsFreshKra(), nil
	}
	nextStep, nextStepErr := getNextStepBasedOnKraData(ctx, psRes)
	if nextStepErr != nil {
		logger.Info(ctx, "error getting next step based on KRA KYC status, falling back to existing status", zap.Error(nextStepErr))
		// we are not returning error here since we fall back to existing KRA status
		return details.GetMetadata().GetIsFreshKra(), nil
	}
	switch nextStep {
	case woPb.OnboardingStep_ONBOARDING_STEP_UPLOAD_DOCKET:
		return true, nil
	case woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC:
		return false, nil
	default:
		return false, errors.New("unexpected next step")
	}
}
