//nolint:unused
package steps

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifierrors"
	clientstate1 "github.com/epifi/gamma/api/frontend/wealthonboarding/clientstate"
	woErr "github.com/epifi/gamma/wealthonboarding/errors"

	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"

	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	types "github.com/epifi/gamma/api/typesv2"
	wVendorPb "github.com/epifi/gamma/api/vendors/wealth"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/ckyc_helper"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/event"
	"github.com/epifi/gamma/wealthonboarding/helper"
	manualreview "github.com/epifi/gamma/wealthonboarding/manual_review"
	"github.com/epifi/gamma/wealthonboarding/ocr"
	"github.com/epifi/gamma/wealthonboarding/release"

	"github.com/pkg/errors"
	"google.golang.org/genproto/googleapis/type/date"
)

const missingDataBottomInfoText = "On continuing you’ll be redirected to a secure screen to update your KYC with Aadhaar eSign"

type CollectMissingDataStep struct {
	currentStep                  woPb.OnboardingStep
	manualReviewStep             woPb.OnboardingStep
	confirmPepAndCitizenshipStep woPb.OnboardingStep
	ckycHelper                   ckyc_helper.ICkycHelper
	livenessStep                 woPb.OnboardingStep
	review                       manualreview.IManualReview
	conf                         *config.Config
	lvClient                     livenessPb.LivenessClient
	stepDetailsDao               dao.OnboardingStepDetailsDao
	releaseEvaluator             release.IEvaluator
	ocrHelper                    ocr.IOcrHelper
	eventBroker                  events.Broker
	kraDocketStep                woPb.OnboardingStep
}

func NewCollectMissingDataStep(
	ckycHelper ckyc_helper.ICkycHelper,
	review manualreview.IManualReview,
	conf *config.Config,
	lvClient livenessPb.LivenessClient,
	stepDetailsDao dao.OnboardingStepDetailsDao,
	releaseEvaluator release.IEvaluator,
	ocrHelper ocr.IOcrHelper,
	eventBroker events.Broker) *CollectMissingDataStep {
	return &CollectMissingDataStep{
		currentStep:                  woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO,
		manualReviewStep:             woPb.OnboardingStep_ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW,
		confirmPepAndCitizenshipStep: woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
		ckycHelper:                   ckycHelper,
		livenessStep:                 woPb.OnboardingStep_ONBOARDING_STEP_LIVENESS,
		review:                       review,
		conf:                         conf,
		lvClient:                     lvClient,
		stepDetailsDao:               stepDetailsDao,
		releaseEvaluator:             releaseEvaluator,
		ocrHelper:                    ocrHelper,
		eventBroker:                  eventBroker,
		kraDocketStep:                woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET,
	}
}

//nolint:funlen
func (c *CollectMissingDataStep) Perform(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	if !details.GetMetadata().GetIsFreshKra() {
		return c.performMissingDataStepForExistingKRAUsers(ctx, details)
	}

	isAadhaarCollectedFromDigilocker := helper.IsAadhaarCollectedFromDigilocker(details)

	if !c.conf.DisableCKYC {
		stepSubStatus, ckycErr := c.ckycHelper.FetchPopulateValidateCkycDownloadData(ctx, details)
		if ckycErr != nil {
			switch {
			case errors.Is(ckycErr, epifierrors.ErrRecordNotFound):
				logger.Info(ctx, "ckyc record not found")
				// check if poa data is present in digilocker data, else mark future scope
				if !isAadhaarCollectedFromDigilocker {
					return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, c.currentStep, c.livenessStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CKYC_RECORD_NOT_FOUND), nil
				}
			case errors.Is(ckycErr, epifierrors.ErrDowntimeExpected):
				logger.Info(ctx, "downtime expected for vendor")
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, c.currentStep, c.livenessStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(ckycErr), nil
			default:
				logger.Error(ctx, "failed to fetchPopulateValidateCkycDownloadData")
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, c.currentStep, c.livenessStep), errors.Wrap(ckycErr, "failed to fetch and populate ckyc download data")
			}
		}
		if stepSubStatus != woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSPECIFIED && !isAadhaarCollectedFromDigilocker {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, c.currentStep, c.livenessStep).WithStepSubStatus(stepSubStatus), nil
		}
		if !isAadhaarCollectedFromDigilocker {
			poaOcrErr := c.ocrHelper.PopulatePoaWithOcr(ctx, details)
			if poaOcrErr != nil {
				switch {
				case errors.Is(poaOcrErr, woErr.ErrOCRLowConfidence):
					mrErr := c.pushToManualReviewQueue(ctx, details)
					if mrErr != nil {
						return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, c.currentStep, c.livenessStep), errors.Wrap(mrErr, "failed to push data to manual review")
					}
				case errors.Is(poaOcrErr, woErr.ErrPoaNotAllowed):
					return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, c.currentStep, c.livenessStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSUPPORTED_AADHAAR_TYPE), nil
				default:
					logger.Error(ctx, "failed to PopulatePoaWithOcr")
					return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, c.currentStep, c.livenessStep), errors.Wrap(poaOcrErr, "failed to populate poa with ocr")
				}
			}
		}
		// fetch pan from ckyc and then pass it through ocr
		ckycPan := getPanFromCkycData(ctx, details)
		if ckycPan != nil {
			panWithOcr, panWithOcrErr := c.ocrHelper.FetchPanWithOcr(ctx, details, ckycPan)
			if panWithOcrErr != nil && !errors.Is(panWithOcrErr, woErr.ErrOCRLowConfidence) && !errors.Is(panWithOcrErr, woErr.ErrOCRPanNumberMismatch) {
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, c.currentStep, c.livenessStep), errors.Wrap(panWithOcrErr, "failed to fetch pan using ocr")
			}
			if panWithOcrErr != nil {
				// error occurred due to low confidence or pan number mismatch from ocr, leaving pan details empty so that it can be captured from user
			} else {
				// only update the s3 paths and dob of the document
				details.GetMetadata().GetPanDetails().S3Paths = panWithOcr.GetDoc().GetS3Paths()
				details.GetMetadata().GetPanDetails().Dob = panWithOcr.GetDoc().GetDob()
			}
		}
		personalData := details.GetMetadata().GetPersonalDetails()
		ckycDD := details.GetMetadata().GetCkycData().GetDownloadData()
		if ckycDD != nil && personalData.GetGender() == types.Gender_GENDER_UNSPECIFIED {
			personalData.Gender = ckycDD.GetPersonalDetails().GetGender()
		}

		// if POA is rejected in manual review, PoaWithOcr will become nil. This case should go to future scope
		if details.GetMetadata().GetPoaDetails() == nil && details.GetMetadata().GetPoaWithOcr() == nil {
			logger.Info(ctx, "POA rejected in manual review")
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, c.currentStep, c.livenessStep), nil
		}
	} else {
		digilockerData := details.GetMetadata().GetDigilockerData()
		if digilockerData == nil {
			logger.Info(ctx, "Digilocker Data not found in metadata")
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, c.currentStep, c.livenessStep), nil
		}
		digilockerPan := digilockerData.GetPanDocument()
		if digilockerPan != nil {
			// only update the s3 paths and dob of the document
			details.GetMetadata().GetPanDetails().S3Paths = digilockerPan.GetS3Paths()
			details.GetMetadata().GetPanDetails().Dob = digilockerPan.GetDob()
		}

		personalData := details.GetMetadata().GetPersonalDetails()
		if gender := digilockerData.GetDigilockerAadhaarData().GetGender(); gender != types.Gender_GENDER_UNSPECIFIED {
			personalData.Gender = gender
		}
	}

	// check if the number attempts to upload pan from user is exhausted or not
	cpd := details.GetMetadata().GetCustomerProvidedData()
	isPanReuploadNeeded := false
	if cpd.GetPan() == nil {
		switch {
		case len(cpd.GetPanOcrAttempts()) >= c.conf.MaxPanUploadAttempts:
			logger.Info(ctx, "pan upload attempt exhausted from user, marking manual intervention")
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED, c.currentStep, c.livenessStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_PAN_UPLOAD_ATTEMPTS_EXHAUSTED), nil
		case len(cpd.GetPanOcrAttempts()) > 0:
			logger.Debug(ctx, "pan re-upload needed")
			isPanReuploadNeeded = true
		}
	}

	var isLivenessValid bool
	isMandatoryLivenessEnabled, _, mandatoryLivenessErr := c.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_MANDATORY_LIVENESS).WithActorId(details.GetActorId()))
	if mandatoryLivenessErr != nil {
		return nil, errors.Wrap(mandatoryLivenessErr, "error checking if fresh wealth onboarding liveness step is enabled")
	}
	if isMandatoryLivenessEnabled {
		isLivenessValid = c.checkLivenessAttemptInfoIfValid(ctx, details)
	} else {
		isLivenessValid = addLvAttemptInfoIfValid(ctx, details.GetActorId(), &woPb.LivenessData_LivenessAttempt{}, c.lvClient, c.conf)
	}

	isRiskProfilingSupported, updateAppDeeplink, err := c.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_RISK_PROFILING).WithActorId(details.GetActorId()))
	if err != nil {
		return nil, errors.Wrap(err, "error checking if risk profile is enabled")
	}
	if updateAppDeeplink != nil {
		res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, c.currentStep, c.livenessStep)
		res.Deeplink = updateAppDeeplink
		return res, nil
	}

	dl := getMissingDataDeeplink(details, nil, nil, c.conf, isLivenessValid, nil, isRiskProfilingSupported, nil)
	platform, version := epificontext.AppPlatformAndVersion(ctx)
	if dl != nil {
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			c.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), event.NewMissingDataEvent(details.GetActorId(), details.GetCurrentStep(), details.GetMetadata().GetIsFreshKra(), time.Now(), details.GetCurrentWealthFlow(), version, platform, dl))
		})

		// if user has already made an unsuccessful attempt at uploading PAN image, return a PAN re-upload specific sub-status for setting up user-targeted push notification in orchestrator
		var res *StepExecutionResponse
		if isPanReuploadNeeded {
			res = GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, c.currentStep, c.livenessStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_USER_INPUT_INCL_PAN_REUPLOAD_NEEDED)
		} else {
			res = GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, c.currentStep, c.livenessStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_USER_INPUT_NEEDED)
		}
		res.Deeplink = dl
		return res, nil
	}
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		c.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), event.NewMissingDataEvent(details.GetActorId(), details.GetCurrentStep(), details.GetMetadata().GetIsFreshKra(), time.Now(), details.GetCurrentWealthFlow(), version, platform, dl))
	})

	return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, c.currentStep, getNextStepForFreshKycUsers(isAadhaarCollectedFromDigilocker, isRiskProfilingSupported)), nil
}

func getNextStepForFreshKycUsers(isAadhaarCollectedFromDigilocker bool, isRiskProfileSupported bool) woPb.OnboardingStep {
	if isAadhaarCollectedFromDigilocker {
		switch {
		case isRiskProfileSupported:
			return woPb.OnboardingStep_ONBOARDING_STEP_INVESTMENT_RISK_PROFILING
		default:
			return woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT
		}
	} else {
		return woPb.OnboardingStep_ONBOARDING_STEP_LIVENESS
	}
}

func (c *CollectMissingDataStep) performMissingDataStepForExistingKRAUsers(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	missingDataInKra, asdErr := getMissingDetailsByAppStatusDelta(details.GetMetadata().GetKraData().GetStatusData().GetPanStatusDetails().GetAppStatusDelta())
	if asdErr != nil {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, c.currentStep, c.confirmPepAndCitizenshipStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_UNHANDLED_KRA_APP_STATUS_DELTA), errors.Wrap(asdErr, "error while getting missing data from app status delta")
	}
	if missingDataInKra == deeplinkPb.WealthOnboardingMissingData_WEALTH_ONBOARDING_MISSING_DATA_UNSPECIFIED {
		var mdl []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus
		mdl = c.addNomineeDeeplinkIfRequired(ctx, details, mdl)

		// risk profiling line item is added to missing data screen with no deeplink (if client supports this flow)
		// the actual collection of risk profiling data will happen at a later stage where missing_data deeplink will be generated again
		isRiskProfilingSupported, mdl, updateAppDl, err := c.addRiskProfilingDeeplinkIfRequired(ctx, details, mdl)
		if err != nil {
			return nil, errors.Wrap(err, "error in adding risk profiling deeplink")
		}
		if updateAppDl != nil {
			res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO, woPb.OnboardingStep_ONBOARDING_STEP_LIVENESS)
			res.Deeplink = updateAppDl
			return res, nil
		}

		mdl = c.addAdvisoryAgreementDeeplinkIfRequired(ctx, details, mdl)

		// return missing data deeplink if at least one of the missing data status is enabled
		// if the missing data data status is disabled, we assume that it will be collected in the next steps
		for _, md := range mdl {
			if md.GetStatus() == deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_ENABLED {
				stepExecRes := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, c.currentStep, c.confirmPepAndCitizenshipStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_USER_INPUT_NEEDED)
				stepExecRes.Deeplink = &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_CAPTURE_MISSING_DATA_SCREEN,
					ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingCaptureMissingDataScreenOptions{
						WealthOnboardingCaptureMissingDataScreenOptions: &deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions{
							MissingData: mdl,
						},
					},
				}
				return stepExecRes, nil
			}
		}
		var nextStep woPb.OnboardingStep
		switch {
		case isRiskProfilingSupported:
			nextStep = woPb.OnboardingStep_ONBOARDING_STEP_INVESTMENT_RISK_PROFILING
		default:
			nextStep = woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT
		}
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, c.currentStep, nextStep), nil
	}
	logger.Info(ctx, "user details found missing in KRA, setting to future scope")
	return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, c.currentStep, c.confirmPepAndCitizenshipStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_KRA_PARTIAL_DATA_UPLOAD_NEEDED), nil
	// TODO(vikas): enable through controlled feature release
	/**
	  if missingDataInKra != deeplinkPb.WealthOnboardingMissingData_WEALTH_ONBOARDING_MISSING_DATA_POA {
	  	var mdl1 []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus
	  	mdl1 = collectMissingData(missingDataInKra, details, details.GetMetadata().GetCustomerProvidedData(), mdl1, c.conf)
	  	dl := getMissingDataDeeplinkByMissingDataList(mdl1)
	  	if dl != nil {
	  		res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, c.currentStep, c.livenessStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_KRA_PARTIAL_DATA_UPLOAD_NEEDED)
	  		res.Deeplink = dl
	  		return res, nil
	  	}
	  }
	  logger.Info(ctx, "POA missing in KRA. Going through ckyc flow")
	*/
}

func (c *CollectMissingDataStep) addAdvisoryAgreementDeeplinkIfRequired(_ context.Context, details *woPb.OnboardingDetails, mdl []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus) []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus {
	// add advisory agreement deeplink if supported
	if details.GetMetadata().GetPersonalDetails().GetAdvisoryAgreementDetails().GetSignedAgreementS3Path() == "" {
		mdl = append(mdl, &deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus{
			MissingDataDeeplink: nil,
			Status:              deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_DISABLED,
			DisplayText:         "Advisory agreement",
		})
	}
	return mdl
}

func (c *CollectMissingDataStep) addRiskProfilingDeeplinkIfRequired(ctx context.Context, details *woPb.OnboardingDetails, mdl []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus) (bool, []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus, *deeplinkPb.Deeplink, error) {
	var (
		updateAppDeeplink        *deeplinkPb.Deeplink
		err                      error
		isRiskProfilingSupported bool
	)
	if details.GetMetadata().GetCustomerProvidedData().GetRiskProfilingConsentId() == "" {
		isRiskProfilingSupported, updateAppDeeplink, err = c.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_RISK_PROFILING).WithActorId(details.GetActorId()))
		if err != nil {
			return false, nil, nil, errors.Wrap(err, "error checking if advisory agreement is enabled")
		}
		if updateAppDeeplink != nil {
			return false, nil, updateAppDeeplink, nil
		}
		if isRiskProfilingSupported {
			mdl = append(mdl, &deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus{
				MissingDataDeeplink: nil,
				Status:              deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_DISABLED,
				DisplayText:         "Investment Profile",
			})
		}
	}

	return isRiskProfilingSupported, mdl, nil, nil
}

func (c *CollectMissingDataStep) addNomineeDeeplinkIfRequired(_ context.Context, details *woPb.OnboardingDetails, mdl []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus) []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus {
	// if user has not opted for nominee in wealth onboarding, will show wealth flow nominee screen
	if details.GetMetadata().GetPersonalDetails().GetNomineeDeclarationDetails().GetChoice() == commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
		mdl = addDeeplinkForNomineeCollection(details.GetMetadata().GetCustomerProvidedData(), mdl)
	}
	return mdl
}

// nolint: funlen
func getMissingDataDeeplink(
	details *woPb.OnboardingDetails,
	livenessDl *deeplinkPb.Deeplink,
	signDocketDl *deeplinkPb.Deeplink,
	conf *config.Config,
	isExistingLvValid bool,
	advisoryAgreementDl *deeplinkPb.Deeplink,
	isRiskProfilingSupported bool,
	riskProfilingDl *deeplinkPb.Deeplink,
) *deeplinkPb.Deeplink {
	personalData := details.GetMetadata().GetPersonalDetails()
	cpd := details.GetMetadata().GetCustomerProvidedData()
	var mdl []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus
	// only if the user is FreshKra we need to collect the following data
	if details.GetMetadata().GetIsFreshKra() {
		mdl = collectGender(personalData, cpd, mdl)
		mdl = collectMaritalStatus(personalData, cpd, mdl)
		mdl = collectIncomeSlab(personalData, cpd, mdl)
		mdl = collectSignature(personalData, cpd, mdl)
		mdl = collectPanDetails(details, cpd, mdl, conf, false)
		if details.GetMetadata().GetPersonalDetails().GetNomineeDeclarationDetails().GetChoice() == commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
			mdl = addDeeplinkForNomineeCollection(cpd, mdl)
		}
		// mdl = collectPoaDetails(details, cpd, mdl, conf)
	}

	isAllDataCollected := true
	for _, md := range mdl {
		if md.GetStatus() != deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_UPDATED &&
			md.GetStatus() != deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_EDITABLE {
			isAllDataCollected = false
		}
	}

	livenessSo := &deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus{
		MissingDataDeeplink: nil,
		Status:              deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_DISABLED,
		DisplayText:         "Video Verification",
	}

	advisoryAgreementStatus := deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_DISABLED
	advisoryAgreementFreshInputText := ""

	riskProfilingStatus := deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_DISABLED

	so := &deeplinkPb.Deeplink_WealthOnboardingCaptureMissingDataScreenOptions{
		WealthOnboardingCaptureMissingDataScreenOptions: &deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions{},
	}

	showLiveness := false
	switch {
	case livenessDl == nil && signDocketDl == nil && advisoryAgreementDl == nil && riskProfilingDl == nil: // called from collect_missing_data_step
		if isAllDataCollected {
			return nil
		}
		showLiveness = !isExistingLvValid
	case livenessDl != nil: // called from perform_liveness_step
		livenessSo.MissingDataDeeplink = livenessDl
		livenessSo.Status = deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_ENABLED
		showLiveness = true
	case signDocketDl != nil: // called from create_and_sign_kra_docket_step
		// for all the collected data, the status is editable, but once the docket is generated, user cannot change the data already collected
		// this is why status is overridden to updated here
		for i := range mdl {
			mdl[i].Status = deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_UPDATED
		}
		lvAttempts := details.GetMetadata().GetLivenessData().GetLivenessAttempts()
		if len(lvAttempts) > 0 {
			showLiveness = lvAttempts[len(lvAttempts)-1].GetIsInitiatedFromWealth()
			livenessSo.Status = deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_UPDATED
		}
		advisoryAgreementStatus = deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_UPDATED
		so.WealthOnboardingCaptureMissingDataScreenOptions.SignDocketDeeplink = signDocketDl
		so.WealthOnboardingCaptureMissingDataScreenOptions.IsSignDocketCtaEnabled = true
	case advisoryAgreementDl != nil:
		// called from sign_advisory_agreement step
		// for all the collected data, the status is editable, but once the user is past collect missing data step, we need not take input for the other fields
		// this is why status is overridden to updated here
		for i := range mdl {
			mdl[i].Status = deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_UPDATED
		}
		if details.GetMetadata().GetPersonalDetails().GetAdvisoryAgreementDetails().GetEsignTxnDetails().GetStatus() == woPb.TxnState_TXN_STATE_COMPLETED {
			advisoryAgreementStatus = deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_UPDATED
		} else {
			advisoryAgreementStatus = deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_ENABLED
			advisoryAgreementFreshInputText = "ADD"
		}
	case riskProfilingDl != nil:
		riskProfilingStatus = deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_ENABLED
	}
	if showLiveness {
		mdl = append(mdl, livenessSo)
	}

	// a boolean flag to represent if the risk profiling is supported by client, if not the line item is not shown in missing data sceen
	// the deeplink here could be nil as this data will be collected at a later stage where this deeplink will be populated
	if isRiskProfilingSupported {
		mdl = append(mdl, &deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus{
			MissingDataDeeplink: riskProfilingDl,
			Status:              riskProfilingStatus,
			DisplayText:         "Investment Profile",
		})
	}
	mdl = append(mdl, &deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus{
		MissingDataDeeplink: advisoryAgreementDl,
		Status:              advisoryAgreementStatus,
		DisplayText:         "Advisory agreement",
		FreshInputText:      advisoryAgreementFreshInputText,
	})

	so.WealthOnboardingCaptureMissingDataScreenOptions.MissingData = mdl
	// do not show sign docket CTA if we are collecting data post wealth onboarding is completed
	if details.GetStatus() != woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED {
		so.WealthOnboardingCaptureMissingDataScreenOptions.IsSignDocketCtaVisible = true
		so.WealthOnboardingCaptureMissingDataScreenOptions.BottomInfoText = commontypes.GetTextFromStringFontColourFontStyle(missingDataBottomInfoText, "#8D8D8D", commontypes.FontStyle_BODY_4)
	}
	dl := &deeplinkPb.Deeplink{
		Screen:        deeplinkPb.Screen_WEALTH_ONBOARDING_CAPTURE_MISSING_DATA_SCREEN,
		ScreenOptions: so,
	}
	return dl
}

func getMissingDetailsByAppStatusDelta(asd wVendorPb.KraAppStatusDelta) (deeplinkPb.WealthOnboardingMissingData, error) {
	switch asd {
	case wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_GENDER_NOT_AVAILABLE:
		return deeplinkPb.WealthOnboardingMissingData_WEALTH_ONBOARDING_MISSING_DATA_GENDER, nil
	case wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_MARITAL_STATUS_NOT_AVAILABLE:
		return deeplinkPb.WealthOnboardingMissingData_WEALTH_ONBOARDING_MISSING_DATA_MARITAL_STATUS, nil
	case wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_PROOF_OF_CORRESPONDENCE_ADDRESS_NOT_AVAILABLE:
		return deeplinkPb.WealthOnboardingMissingData_WEALTH_ONBOARDING_MISSING_DATA_POA, nil
	case wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_PROOF_OF_PERMANENT_ADDRESS_NOT_AVAILABLE:
		return deeplinkPb.WealthOnboardingMissingData_WEALTH_ONBOARDING_MISSING_DATA_POA, nil
	case wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_APPLICANTS_SIGNATURE_NOT_AVAILABLE:
		return deeplinkPb.WealthOnboardingMissingData_WEALTH_ONBOARDING_MISSING_DATA_SIGNATURE, nil
	case wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_UNSPECIFIED,
		wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_ALL_MANDATORY_FIELDS_AVAILABLE,
		wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_NAME_OF_THE_APPLICANT_NOT_AVAILABLE,
		wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_FATHERS_OR_SPOUSE_NAME_NOT_AVAILABLE,
		wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_DOB_NOT_AVAILABLE,
		wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_NATIONALITY_NOT_AVAILABLE,
		wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_RESIDENTIAL_STATUS_NOT_AVAILABLE,
		wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_PROOF_OF_IDENTITY_NOT_AVAILABLE,
		wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_CORRESPONDENCE_ADDRESS_NOT_AVAILABLE,
		wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_PERMANENT_ADDRESS_NOT_AVAILABLE,
		wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_IPV_DETAILS_NOT_AVAILABLE,
		wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_DATE_OF_INCORPORATION_NOT_AVAILABLE,
		wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_PLACE_OF_INCORPORATION_NOT_AVAILABLE,
		wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_DATE_OF_COMMENCEMENT_OF_BUSINESS_NOT_AVAILABLE,
		wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_STATUS_NOT_AVAILABLE,
		wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_NUMBER_OF_PROMOTERS_OR_PARTNERS_OR_KARTA_OR_TRUSTEES_AND,
		wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_NAME_OF_RELATED_PERSON_NOT_AVAILABLE,
		wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_RELATION_WITH_APPLICANT_NOT_AVAILABLE,
		wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_PERMANENT_ADDRESS_OF_RELATED_PERSON_NOT_AVAILABLE,
		wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_KYC_IMAGE_IS_INCOMPLETE,
		wVendorPb.KraAppStatusDelta_KRA_APP_STATUS_DELTA_CONTACT_DETAILS_ARE_NOT_AVAILABLE:
		return deeplinkPb.WealthOnboardingMissingData_WEALTH_ONBOARDING_MISSING_DATA_UNSPECIFIED, nil
	default:
		return deeplinkPb.WealthOnboardingMissingData_WEALTH_ONBOARDING_MISSING_DATA_UNSPECIFIED, errors.New("unknown kra app status delta")
	}
}

func collectGender(personalData *woPb.PersonalDetails, cpd *woPb.CustomerProvidedData, mdl []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus) []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus {
	if (personalData.GetGender() == types.Gender_GENDER_UNSPECIFIED && cpd.GetGender() == types.Gender_GENDER_UNSPECIFIED) || cpd.GetGender() != types.Gender_GENDER_UNSPECIFIED {
		status := deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_ENABLED
		if cpd.GetGender() != types.Gender_GENDER_UNSPECIFIED {
			status = deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_EDITABLE
		}

		selectGenderBottomSheet := &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_COLLECT_GENDER_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_CollectGenderScreenOptions{
				CollectGenderScreenOptions: &deeplinkPb.CollectGenderScreenOptions{
					GenderOptions: []*deeplinkPb.CollectGenderScreenOptions_GenderOption{
						{
							Gender:      types.Gender_MALE,
							DisplayText: "Male",
						}, {
							Gender:      types.Gender_MALE,
							DisplayText: "Female",
						}, {
							Gender:      types.Gender_TRANSGENDER,
							DisplayText: "Transgender",
						}, {
							Gender:      types.Gender_OTHER,
							DisplayText: "Other",
						},
					},
					Title: "Select your gender",
				},
			},
		}
		collectGenderListItem := &deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus{
			MissingDataDeeplink: selectGenderBottomSheet,
			Status:              status,
			DisplayText:         "Gender",
			// TODO(Brijesh): Check if fresh input text is needed for this
		}
		mdl = append(mdl, collectGenderListItem)
	}
	return mdl
}

func collectSignature(personalData *woPb.PersonalDetails, cpd *woPb.CustomerProvidedData, mdl []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus) []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus {
	if (personalData.GetSignature() == nil && cpd.GetSignature() == nil) || cpd.GetSignature() != nil {
		status := deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_ENABLED
		if cpd.GetSignature() != nil {
			status = deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_EDITABLE
		}

		addSignatureBottomSheet := &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_COLLECT_SIGNATURE_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_CollectSignatureScreenOptions{
				CollectSignatureScreenOptions: &deeplinkPb.CollectSignatureScreenOptions{
					Title:             "Add your signature",
					CameraCaptureHint: "Make sure the signature is in the frame and is clear to read",
				},
			},
		}
		collectSignatureListItem := &deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus{
			MissingDataDeeplink: addSignatureBottomSheet,
			Status:              status,
			DisplayText:         "Signature",
			FreshInputText:      "SIGN",
		}
		mdl = append(mdl, collectSignatureListItem)
	}
	return mdl
}

func collectPanDetails(details *woPb.OnboardingDetails, cpd *woPb.CustomerProvidedData, mdl []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus, conf *config.Config, forcePanUpload bool) []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus {
	isMetadataPanValid := isDocProofValid(details.GetMetadata().GetPanDetails(), conf)
	isCpdPanValid := hasDocImage(cpd.GetPan())
	panUploadCnt := len(details.GetMetadata().GetCustomerProvidedData().GetPanOcrAttempts())
	var status deeplinkPb.WealthOnboardingDataStatus
	switch {
	case isMetadataPanValid && !isCpdPanValid && !forcePanUpload:
		// no need to append PAN to missing data list as it is populated by the CKYC response
		return mdl
	case isCpdPanValid:
		// both are valid, meaning we have successfully captured the PAN from user
		status = deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_EDITABLE
	case panUploadCnt > 0:
		// capture from user failed
		status = deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_FAILED
	default:
		// !isMetadataPanValid && !isCpdPanValid (no data for pan found)
		status = deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_ENABLED
	}

	uploadPANBottomSheet := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_COLLECT_PAN_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_CollectPanScreenOptions{
			CollectPanScreenOptions: &deeplinkPb.CollectPanScreenOptions{
				Title: "Add a PAN Card Image",
			},
		},
	}
	collectPANListItem := &deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus{
		MissingDataDeeplink: uploadPANBottomSheet,
		Status:              status,
		DisplayText:         "PAN Card Image",
		FreshInputText:      "UPLOAD",
	}
	mdl = append(mdl, collectPANListItem)
	return mdl
}

// Creates a deeplink for the screen where nominee declaration details are collected from user
// and adds it to the list for the screen where user is where user input is needed
func addDeeplinkForNomineeCollection(cpd *woPb.CustomerProvidedData,
	missingDataInputScreen []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus) []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus {
	status := deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_ENABLED
	if cpd.GetNomineeDeclarationDetails().GetChoice() != commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
		status = deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_UPDATED
	}

	chooseNomineeBottomSheet := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_COLLECT_NOMINEE_DETAILS_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_CollectNomineeDetailsScreenOptions{
			CollectNomineeDetailsScreenOptions: &deeplinkPb.CollectNomineeDetailsScreenOptions{
				Title:       "Select a nominee",
				MaxNominees: 1, // UI supports single nominee selection only
				OptOutText:  "I want to continue without a nominee",
				Flow:        deeplinkPb.DataCollectionFlow_DATA_COLLECTION_FLOW_WEALTH_ONBOARDING,
				FlowData: &deeplinkPb.DataCollectionFlowData{
					Data: &deeplinkPb.DataCollectionFlowData_WealthFlow{
						WealthFlow: clientstate1.WealthFlow_WEALTH_FLOW_INVESTMENT,
					},
				},
				AddNomineeDeeplink: GetAddNomineeDeeplink(),
			}},
	}
	collectNomineeListItem := &deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus{
		Status:              status,
		DisplayText:         "Nominee details",
		MissingDataDeeplink: chooseNomineeBottomSheet,
		FreshInputText:      "ADD",
	}
	missingDataInputScreen = append(missingDataInputScreen, collectNomineeListItem)
	return missingDataInputScreen
}

// show marital status deeplink
func collectMaritalStatus(personalData *woPb.PersonalDetails, cpd *woPb.CustomerProvidedData, mdl []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus) []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus {
	if (personalData.GetMaritalStatus() == types.MaritalStatus_UNSPECIFIED && cpd.GetMaritalStatus() == types.MaritalStatus_UNSPECIFIED) || cpd.GetMaritalStatus() != types.MaritalStatus_UNSPECIFIED {
		status := deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_ENABLED
		if cpd.GetMaritalStatus() != types.MaritalStatus_UNSPECIFIED {
			status = deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_EDITABLE
		}
		mdl = append(mdl, &deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus{
			MissingDataDeeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_COLLECT_MARITAL_STATUS_SCREEN,
				ScreenOptions: &deeplinkPb.Deeplink_CollectMaritalStatusScreenOptions{
					CollectMaritalStatusScreenOptions: &deeplinkPb.CollectMaritalStatusScreenOptions{
						MaritalStatusOptions: []*deeplinkPb.CollectMaritalStatusScreenOptions_MaritalStatusOption{
							{
								MaritalStatus: types.MaritalStatus_MARRIED,
								DisplayText:   "Married",
							}, {
								MaritalStatus: types.MaritalStatus_UNMARRIED,
								DisplayText:   "Unmarried",
							},
						},
						Title: "What is your marital status?",
					},
				},
			},
			Status:      status,
			DisplayText: "Marital Status",
		})
	}
	return mdl
}

func collectMaritalStatusV2(personalData *woPb.PersonalDetails, cpd *woPb.CustomerProvidedData, mdl []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus) []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus {
	// set marital status of user to unmarried by default if we don't have it
	// this is done to reduce user input friction, users can always edit their marital status via deeplink
	if personalData.GetMaritalStatus() == types.MaritalStatus_UNSPECIFIED && cpd.GetMaritalStatus() == types.MaritalStatus_UNSPECIFIED {
		cpd.MaritalStatus = types.MaritalStatus_UNMARRIED
		personalData.MaritalStatus = types.MaritalStatus_UNMARRIED
	}

	collectMaritalStatusDeeplink := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_COLLECT_MARITAL_STATUS_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_CollectMaritalStatusScreenOptions{
			CollectMaritalStatusScreenOptions: &deeplinkPb.CollectMaritalStatusScreenOptions{
				MaritalStatusOptions: []*deeplinkPb.CollectMaritalStatusScreenOptions_MaritalStatusOption{
					{
						MaritalStatus: types.MaritalStatus_MARRIED,
						DisplayText:   "Married",
					}, {
						MaritalStatus: types.MaritalStatus_UNMARRIED,
						DisplayText:   "Unmarried",
					},
				},
				Title: "What is your marital status?",
			},
		},
	}

	displayVal := ""
	switch cpd.GetMaritalStatus() {
	case types.MaritalStatus_MARRIED:
		displayVal = "Married"
	case types.MaritalStatus_UNMARRIED:
		displayVal = "Unmarried"
	default:
	}
	mdl = append(mdl, &deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus{
		MissingDataDeeplink: collectMaritalStatusDeeplink,
		Status:              deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_EDITABLE,
		DisplayText:         "Marital Status",
		DisplayVal:          displayVal,
	})
	return mdl
}

// nolint
func collectIncomeSlab(personalData *woPb.PersonalDetails, cpd *woPb.CustomerProvidedData, mdl []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus) []*deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus {
	if (personalData.GetIncomeSlab() == types.IncomeSlab_INCOME_SLAB_UNSPECIFIED && cpd.GetIncomeSlab() == types.IncomeSlab_INCOME_SLAB_UNSPECIFIED) || cpd.GetIncomeSlab() != types.IncomeSlab_INCOME_SLAB_UNSPECIFIED {
		status := deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_ENABLED
		if cpd.GetIncomeSlab() != types.IncomeSlab_INCOME_SLAB_UNSPECIFIED {
			status = deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_EDITABLE
		}
		mdl = append(mdl, &deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus{
			MissingDataDeeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_COLLECT_INCOME_SLAB_SCREEN,
				ScreenOptions: &deeplinkPb.Deeplink_CollectIncomeSlabScreenOptions{
					CollectIncomeSlabScreenOptions: &deeplinkPb.CollectIncomeSlabScreenOptions{
						IncomeSlabOptions: []*deeplinkPb.CollectIncomeSlabScreenOptions_IncomeSlabOption{
							{
								IncomeSlab:  types.IncomeSlab_INCOME_SLAB_BELOW_1_LAC,
								DisplayText: "Less than 1 Lakh",
							}, {
								IncomeSlab:  types.IncomeSlab_INCOME_SLAB_1_TO_5_LAC,
								DisplayText: "1 - 5 Lakhs",
							}, {
								IncomeSlab:  types.IncomeSlab_INCOME_SLAB_5_TO_10_LAC,
								DisplayText: "5 - 10 Lakhs",
							}, {
								IncomeSlab:  types.IncomeSlab_INCOME_SLAB_10_TO_25_LAC,
								DisplayText: "10 - 25 Lakhs",
							}, {
								IncomeSlab:  types.IncomeSlab_INCOME_SLAB_ABOVE_25_LAC,
								DisplayText: "25 Lakhs+",
							}, {
								IncomeSlab:  types.IncomeSlab_INCOME_SLAB_ABOVE_1_CRORE,
								DisplayText: "1 Crore+",
							},
						},
						Title: "What is your annual income?",
					},
				},
			},
			Status:      status,
			DisplayText: "Income Slab",
		})
	}
	return mdl
}

func isDocProofValid(doc *types.DocumentProof, conf *config.Config) bool {
	if doc.GetId() != "" && hasDocImage(doc) {
		if doc.GetProofType() == types.DocumentProofType_DOCUMENT_PROOF_TYPE_PASSPORT || doc.GetProofType() == types.DocumentProofType_DOCUMENT_PROOF_TYPE_DRIVING_LICENSE {
			if doc.GetExpiry() != nil && isExpiryDateValid(doc.GetExpiry(), conf) {
				return true
			}
		} else {
			return true
		}
	}
	return false
}

func hasDocImage(doc *types.DocumentProof) bool {
	hasPhoto := doc.GetPhoto() != nil && len(doc.GetPhoto()) > 0 && (doc.GetPhoto()[0].GetImageUrl() != "" || doc.GetPhoto()[0].GetImageDataBase64() != "")
	isInS3 := doc.GetS3Paths() != nil && len(doc.GetS3Paths()) > 0 && doc.GetS3Paths()[0] != ""
	if doc != nil && (hasPhoto || isInS3) {
		return true
	}
	return false
}

func (c *CollectMissingDataStep) pushToManualReviewQueue(ctx context.Context, od *woPb.OnboardingDetails) error {
	if od.GetMetadata().GetPoaWithOcr().GetDoc() == nil || od.GetMetadata().GetCkycData().GetDownloadData().GetPersonalDetails().GetDocumentsList() == nil {
		return errors.New("OCR document not found")
	}
	proofType := od.GetMetadata().GetPoaWithOcr().GetDoc().GetProofType()
	originalDocument := helper.GetDocumentFromDocumentLists(od.GetMetadata().GetCkycData().GetDownloadData().GetPersonalDetails().GetDocumentsList(), proofType)
	var payload *woPb.ReviewPayload
	var itemType woPb.ItemType
	switch proofType {
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR:
		payload = &woPb.ReviewPayload{
			Payload: &woPb.ReviewPayload_RedactionReview{
				RedactionReview: &woPb.ReviewPayload_OcrReview{
					ActorId:           od.GetActorId(),
					OriginalDocument:  originalDocument,
					ProcessedDocument: od.GetMetadata().GetPoaWithOcr(),
				},
			},
		}
		itemType = woPb.ItemType_ITEM_TYPE_AADHAAR_REDACTION
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_DRIVING_LICENSE, types.DocumentProofType_DOCUMENT_PROOF_TYPE_PASSPORT:
		payload = &woPb.ReviewPayload{
			Payload: &woPb.ReviewPayload_ExpiryReview{
				ExpiryReview: &woPb.ReviewPayload_OcrReview{
					ActorId:           od.GetActorId(),
					OriginalDocument:  originalDocument,
					ProcessedDocument: od.GetMetadata().GetPoaWithOcr(),
				},
			},
		}
		itemType = woPb.ItemType_ITEM_TYPE_DOCUMENT_EXPIRY
	default:
		return errors.New(fmt.Sprintf("invalid OCR document proof type: %v", proofType))
	}
	id, err := c.review.AddToReview(ctx, payload, itemType)
	if err != nil {
		return errors.Wrap(err, "Failed to add item to review")
	}
	if od.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping() == nil {
		if od.GetMetadata().GetManualReviewAttempts() == nil {
			od.GetMetadata().ManualReviewAttempts = &woPb.ManualReviewAttempts{}
		}
		od.GetMetadata().GetManualReviewAttempts().ReviewAttemptMapping = make(map[string]*woPb.ManualReviewAttempts_ReviewAttemptList)
	}
	_, ok := od.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping()[itemType.String()]
	if !ok {
		od.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping()[itemType.String()] = &woPb.ManualReviewAttempts_ReviewAttemptList{
			ReviewAttempts: make([]*woPb.ManualReviewAttempts_ReviewAttempt, 0),
		}
	}
	reviewAttemptList := od.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping()[itemType.String()]
	reviewAttemptList.ReviewAttempts = append(reviewAttemptList.ReviewAttempts, &woPb.ManualReviewAttempts_ReviewAttempt{
		Id:     id,
		Status: woPb.ReviewStatus_REVIEW_STATUS_PENDING,
	})
	return nil
}

func isExpiryDateValid(expiry *date.Date, conf *config.Config) bool {
	if expiry == nil {
		return false
	}
	expiryTime := time.Date(int(expiry.GetYear()), time.Month(expiry.GetMonth()), int(expiry.GetDay()), 0, 0, 0, 0, datetime.IST)
	newExpiryTime := expiryTime.AddDate(0, 0, -conf.DaysToExpiry)
	newExpiryDate := &date.Date{
		Year:  int32(newExpiryTime.Year()),
		Month: int32(newExpiryTime.Month()),
		Day:   int32(newExpiryTime.Day()),
	}
	return !datetime.IsDateBeforeTodayInLoc(newExpiryDate, datetime.IST)
}

func getPanFromCkycData(ctx context.Context, details *woPb.OnboardingDetails) *types.DocumentProof {
	docs := details.GetMetadata().GetCkycData().GetDownloadData().GetPersonalDetails().GetDocumentsList()
	if len(details.GetMetadata().GetPanDetails().GetPhoto()) == 0 && len(details.GetMetadata().GetPanDetails().GetS3Paths()) == 0 {
		pan := helper.GetDocumentFromDocumentLists(docs, types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN)
		if pan == nil || (len(pan.GetPhoto()) == 0 && len(pan.GetS3Paths()) == 0) {
			logger.Info(ctx, "pan not present")
			return nil
		}
		return pan
	}
	return nil
}

func (c *CollectMissingDataStep) checkLivenessAttemptInfoIfValid(ctx context.Context, onboardingDetails *woPb.OnboardingDetails) bool {
	res, err := c.lvClient.GetLivenessAttempts(ctx, &livenessPb.GetLivenessAttemptsRequest{
		ActorId:      onboardingDetails.GetActorId(),
		Limit:        maxLvAttemptLimit,
		LivenessFlow: livenessPb.LivenessFlow_WEALTH_ONBOARDING,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching liveness attempts, not updating liveness attempt", zap.String(logger.ACTOR_ID_V2, onboardingDetails.GetActorId()), zap.Error(rpcErr))
		return false
	}
	for _, attempt := range res.GetLivenessAttempts() {
		// we can use passed liveness attempt with wealth onboarding as liveness flow
		if (attempt.GetLivenessFlow() == livenessPb.LivenessFlow_WEALTH_ONBOARDING) && (attempt.GetStatus() == livenessPb.LivenessStatus_LIVENESS_PASSED || attempt.GetStatus() == livenessPb.LivenessStatus_LIVENESS_MANUALLY_PASSED) {
			if attempt.GetLivenessScore() >= c.conf.LivenessConfig.LivenessThreshold && attempt.GetOtpScore() >= c.conf.LivenessConfig.OTPThreshold {
				return true
			}
		}
	}
	return false
}
