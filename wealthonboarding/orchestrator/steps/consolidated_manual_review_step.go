package steps

import (
	"context"
	"fmt"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	wealthCommsPb "github.com/epifi/gamma/api/wealthonboarding/comms"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	woComms "github.com/epifi/gamma/wealthonboarding/comms"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/event"
	"github.com/epifi/gamma/wealthonboarding/helper"
	manualreview "github.com/epifi/gamma/wealthonboarding/manual_review"

	"github.com/pkg/errors"
)

const ManualVerificationTitle = "We’ve submitted your documents for review"
const ManualVerificationDescription = "This should not take more than 3 business days. We’ll notify you when this is done⏳"

type ConsolidatedManualReviewStep struct {
	manualReview      manualreview.IManualReview
	currentStep       woPb.OnboardingStep
	nextStepOnSuccess woPb.OnboardingStep
	conf              *config.Config
	commsService      woComms.IComms
	eventBroker       events.Broker
}

func NewConsolidatedManualReviewStep(
	manualReview manualreview.IManualReview,
	conf *config.Config,
	commsService woComms.IComms,
	eventBroker events.Broker,
) *ConsolidatedManualReviewStep {
	return &ConsolidatedManualReviewStep{
		manualReview:      manualReview,
		currentStep:       woPb.OnboardingStep_ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW,
		nextStepOnSuccess: woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
		conf:              conf,
		commsService:      commsService,
		eventBroker:       eventBroker,
	}
}

// nolint: dupl, funlen
func (s *ConsolidatedManualReviewStep) Perform(ctx context.Context, od *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	if od.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping() == nil {
		logger.Info(ctx, "no items for manual review")
		return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, s.currentStep, s.nextStepOnSuccess).WithIsLastStep(isManualReviewLastStep(od)), nil
	}

	var pendingIds []string
	var rejectedSteps []woPb.OnboardingStep
	var rejectedItems []woPb.ItemType
	for k, v := range od.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping() {
		if v != nil && v.GetReviewAttempts() != nil && len(v.GetReviewAttempts()) > 0 {
			// get the latest attempt for each key
			attempt := v.GetReviewAttempts()[len(v.GetReviewAttempts())-1]
			var statusErr error
			// check if existing status is in progress or pending
			if attempt.GetStatus() == woPb.ReviewStatus_REVIEW_STATUS_IN_PROGRESS || attempt.GetStatus() == woPb.ReviewStatus_REVIEW_STATUS_PENDING {
				// update status in metadata
				attempt.Status, statusErr = s.manualReview.CheckStatus(ctx, attempt.GetId())
				if statusErr != nil {
					logger.Error(ctx, fmt.Sprintf("failed to get attempt status actorId: %v, itemType: %v, attemptId: %v", od.GetActorId(), k, attempt.GetId()))
					return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, s.currentStep, s.nextStepOnSuccess), errors.Wrap(statusErr, "failed to check attempt status")
				}
			} else {
				// no need to check already reviewed items again
				continue
			}
			switch attempt.GetStatus() {
			case woPb.ReviewStatus_REVIEW_STATUS_PENDING:
				// collect all the pending ids for pushing to queue
				pendingIds = append(pendingIds, attempt.GetId())
			case woPb.ReviewStatus_REVIEW_STATUS_REJECTED:
				err := s.handleRejectedStatus(ctx, k, attempt.GetId(), od)
				if err != nil {
					logger.Error(ctx, fmt.Sprintf("failed to update metadata actorId: %v, itemType: %v, attemptId: %v", od.GetActorId(), k, attempt.GetId()))
					return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, s.currentStep, s.nextStepOnSuccess), errors.Wrap(err, "failed to update metadata")
				}
				rejectedItems = append(rejectedItems, woPb.ItemType(woPb.ItemType_value[k]))
				// collect all the steps for retry to invalidate them
				// kra docket needs to be created again since poa or liveness date changes
				rejectedSteps = append(rejectedSteps, getStepFromItemType(k), woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET)
			case woPb.ReviewStatus_REVIEW_STATUS_PERMANENTLY_REJECTED:
				s.SendManualReviewEvent(ctx, od, woPb.ReviewStatus_REVIEW_STATUS_PERMANENTLY_REJECTED)
				return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, s.currentStep, s.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_PERMANENTLY_REJECTED), nil
			case woPb.ReviewStatus_REVIEW_STATUS_APPROVED:
				isExpired, err := s.handleApprovedStatus(ctx, k, attempt.GetId(), od)
				if err != nil {
					if errors.Is(err, epifierrors.ErrRecordNotFound) {
						logger.Info(ctx, fmt.Sprintf("approved review item not found for actorId: %v, itemType: %v, attemptId: %v", od.GetActorId(), k, attempt.GetId()))
						return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, s.currentStep, s.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_REVIEW_ITEM_NOT_FOUND), nil
					} else {
						logger.Error(ctx, fmt.Sprintf("failed to update metadata actorId: %v, itemType: %v, attemptId: %v", od.GetActorId(), k, attempt.GetId()))
						return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, s.currentStep, s.nextStepOnSuccess), errors.Wrap(err, "failed to update metadata")
					}
				}
				if isExpired {
					rejectedItems = append(rejectedItems, woPb.ItemType(woPb.ItemType_value[k]))
					// kra docket needs to be created again since poa changes
					rejectedSteps = append(rejectedSteps, getStepFromItemType(k), woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET)
				}
			case woPb.ReviewStatus_REVIEW_STATUS_EDITED:
				isExpired, err := s.handleEditedStatus(ctx, k, attempt.GetId(), od)
				if err != nil {
					if errors.Is(err, epifierrors.ErrRecordNotFound) {
						logger.Info(ctx, fmt.Sprintf("edited review item not found for actorId: %v, itemType: %v, attemptId: %v", od.GetActorId(), k, attempt.GetId()))
						return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, s.currentStep, s.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_REVIEW_ITEM_NOT_FOUND), nil
					} else {
						logger.Error(ctx, fmt.Sprintf("failed to update metadata actorId: %v, itemType: %v, attemptId: %v", od.GetActorId(), k, attempt.GetId()))
						return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, s.currentStep, s.nextStepOnSuccess), errors.Wrap(err, "failed to update metadata")
					}
				}
				if isExpired {
					rejectedSteps = append(rejectedSteps, getStepFromItemType(k))
				}
				// kra docket needs to be created again since poa changes
				rejectedSteps = append(rejectedSteps, woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET)
			case woPb.ReviewStatus_REVIEW_STATUS_IN_PROGRESS:
				return s.getInProgressResponse(od)
			case woPb.ReviewStatus_REVIEW_STATUS_UNSPECIFIED:
				return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED, s.currentStep, s.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_INVALID_REVIEW_STATUS), errors.New("invalid manual review status")
			}
		}
	}

	if len(pendingIds) > 0 {
		// submit all the pending items for review
		err := s.manualReview.SubmitForReview(ctx, od.GetActorId(), pendingIds)
		if err != nil {
			return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, s.currentStep, s.nextStepOnSuccess), errors.Wrap(err, fmt.Sprintf("failed to submit for review for actorID: %v", od.GetActorId()))
		}
		s.SendManualReviewEvent(ctx, od, woPb.ReviewStatus_REVIEW_STATUS_PENDING)
		return s.getInProgressResponse(od)
	}

	if len(rejectedItems) == 1 && rejectedItems[0] == woPb.ItemType_ITEM_TYPE_LIVENESS {
		// send notification to user for the rejected review items
		logger.Info(ctx, "sending liveness rejected notification")
		s.commsService.SendNotificationAsync(ctx, od, wealthCommsPb.WealthCommunicationType_WEALTH_COMMUNICATION_TYPE_LIVENESS_MANUALLY_REJECTED)
	}

	if len(rejectedSteps) > 0 {
		// in case where only esign is needed again
		if len(rejectedSteps) == 1 && rejectedSteps[0] == woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET {
			logger.Info(ctx, "sending aadhaar esign needed notification")
			s.commsService.SendNotificationAsync(ctx, od, wealthCommsPb.WealthCommunicationType_WEALTH_COMMUNICATION_TYPE_ESIGN_NEEDED_AGAIN)
		}
		// need to invalidate the manual review step also for it to be executed again
		rejectedSteps = append(rejectedSteps, s.currentStep)
		// invalidate the rejected steps and return
		s.SendManualReviewEvent(ctx, od, woPb.ReviewStatus_REVIEW_STATUS_REJECTED)
		return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, s.currentStep, s.nextStepOnSuccess).WithStepsToInvalidate(rejectedSteps...), nil
	} else {
		// send notification to user to continue onboarding flow
		logger.Info(ctx, "sending manual review approved notification")
		s.commsService.SendNotificationAsync(ctx, od, wealthCommsPb.WealthCommunicationType_WEALTH_COMMUNICATION_TYPE_MANUAL_REVIEW_APPROVED)
		s.SendManualReviewEvent(ctx, od, woPb.ReviewStatus_REVIEW_STATUS_APPROVED)
		return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, s.currentStep, s.nextStepOnSuccess).WithIsLastStep(isManualReviewLastStep(od)), nil
	}
}

func (s *ConsolidatedManualReviewStep) handleApprovedStatus(ctx context.Context, itemType string, attemptId string, od *woPb.OnboardingDetails) (bool, error) {
	reviewItem, err := s.manualReview.GetReviewItem(ctx, attemptId)
	if err != nil {
		return false, err
	}
	switch itemType {
	case woPb.ItemType_ITEM_TYPE_AADHAAR_REDACTION.String():
		if od.GetMetadata().PoaDetails != nil {
			return false, nil
		}
		if reviewItem.GetReviewPayload().GetRedactionReview().GetProcessedDocument().GetDoc() != nil {
			od.GetMetadata().PoaDetails = reviewItem.GetReviewPayload().GetRedactionReview().GetProcessedDocument().GetDoc()
		} else {
			return false, errors.New("redacted document not found")
		}
	case woPb.ItemType_ITEM_TYPE_DOCUMENT_EXPIRY.String():
		if od.GetMetadata().PoaDetails != nil {
			return false, nil
		}
		doc := reviewItem.GetReviewPayload().GetExpiryReview().GetProcessedDocument().GetDoc()
		if doc == nil {
			return false, errors.New("document with expiry date not found")
		}
		if isExpiryDateValid(doc.GetExpiry(), s.conf) {
			od.GetMetadata().PoaDetails = doc
		} else {
			// invalidate metadata for step retrial
			od.GetMetadata().PoaWithOcr = nil
			od.GetMetadata().KraDocketInfo = nil
			return true, nil
		}
	case woPb.ItemType_ITEM_TYPE_LIVENESS.String():
	// do nothing
	default:
		return false, errors.New(fmt.Sprintf("invalid itemType: %v", itemType))
	}
	return false, nil
}

// noline: dupl
func (s *ConsolidatedManualReviewStep) handleEditedStatus(ctx context.Context, itemType string, attemptId string, od *woPb.OnboardingDetails) (bool, error) {
	reviewItem, err := s.manualReview.GetReviewItem(ctx, attemptId)
	if err != nil {
		return false, err
	}
	switch itemType {
	case woPb.ItemType_ITEM_TYPE_AADHAAR_REDACTION.String():
		if od.GetMetadata().PoaDetails != nil {
			return false, nil
		}
		if reviewItem.GetReviewPayload().GetRedactionReview().GetEditedDocument() != nil {
			od.GetMetadata().PoaDetails = reviewItem.GetReviewPayload().GetRedactionReview().GetEditedDocument()
		} else {
			return false, errors.New("redacted document not found")
		}
		od.GetMetadata().KraDocketInfo = nil
	case woPb.ItemType_ITEM_TYPE_DOCUMENT_EXPIRY.String():
		if od.GetMetadata().PoaDetails != nil {
			return false, nil
		}
		doc := reviewItem.GetReviewPayload().GetExpiryReview().GetEditedDocument()
		if doc == nil {
			return false, errors.New("document with expiry date not found")
		}
		if isExpiryDateValid(doc.GetExpiry(), s.conf) {
			od.GetMetadata().PoaDetails = doc
		} else {
			// invalidate metadata for step retrial
			od.GetMetadata().PoaWithOcr = nil
			return true, nil
		}
		od.GetMetadata().KraDocketInfo = nil
	case woPb.ItemType_ITEM_TYPE_LIVENESS.String():
	// do nothing
	default:
		return false, errors.New(fmt.Sprintf("invalid itemType: %v", itemType))
	}
	return false, nil
}

func (s *ConsolidatedManualReviewStep) handleRejectedStatus(ctx context.Context, itemType string, attemptId string, od *woPb.OnboardingDetails) error {
	reviewItem, err := s.manualReview.GetReviewItem(ctx, attemptId)
	if err != nil {
		return errors.Wrap(err, "error while fetching manual review item")
	}
	switch itemType {
	case woPb.ItemType_ITEM_TYPE_AADHAAR_REDACTION.String(),
		woPb.ItemType_ITEM_TYPE_DOCUMENT_EXPIRY.String():
		// invalidate metadata for step retrial
		od.GetMetadata().PoaWithOcr = nil
		od.GetMetadata().KraDocketInfo = nil
	case woPb.ItemType_ITEM_TYPE_LIVENESS.String():
		if od.GetMetadata().GetLivenessData().GetLivenessAttempts() == nil || len(od.GetMetadata().GetLivenessData().GetLivenessAttempts()) == 0 {
			return errors.New("liveness data not found")
		}
		attempt := od.GetMetadata().GetLivenessData().GetLivenessAttempts()[len(od.GetMetadata().GetLivenessData().GetLivenessAttempts())-1]
		// invalidate metadata for step retrial
		attempt.Status = woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_FAILED_RETRY
		// store liveness reject reason
		attempt.LivenessRejectReason = reviewItem.GetReviewPayload().GetLivenessRejectReason()
		od.GetMetadata().KraDocketInfo = nil
	default:
		return errors.New(fmt.Sprintf("invalid itemType: %v", itemType))
	}
	return nil
}

func getStepFromItemType(itemType string) woPb.OnboardingStep {
	switch itemType {
	case woPb.ItemType_ITEM_TYPE_LIVENESS.String():
		return woPb.OnboardingStep_ONBOARDING_STEP_LIVENESS
	case woPb.ItemType_ITEM_TYPE_AADHAAR_REDACTION.String(),
		woPb.ItemType_ITEM_TYPE_DOCUMENT_EXPIRY.String():
		return woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO
	default:
		return woPb.OnboardingStep_ONBOARDING_STEP_UNSPECIFIED
	}
}

func (s *ConsolidatedManualReviewStep) getInProgressResponse(od *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	res := GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, s.currentStep, s.nextStepOnSuccess)
	res.Deeplink = &deeplinkPb.Deeplink{
		// TODO(ismail): currently only error supports an illustration url, we might need to return a status screen with illustration url in it
		Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_ERROR_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingErrorScreenOptions{
			WealthOnboardingErrorScreenOptions: &deeplinkPb.WealthOnboardingErrorScreenOptions{
				IllustrationUrl: helper.ManualVerificationIllustrationURL,
				Title:           ManualVerificationTitle,
				Description:     ManualVerificationDescription,
				Cta: &deeplinkPb.Cta{
					Text: "Awesome",
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_HOME,
					},
				},
			},
		},
	}
	return res, nil
}

func isManualReviewLastStep(od *woPb.OnboardingDetails) bool {
	// if agreement docket is created, TnC step is executed and manual review is last step
	return od.GetMetadata().GetAgreementDocketInfo().GetDocAsProof() != nil
}

func (s *ConsolidatedManualReviewStep) SendManualReviewEvent(ctx context.Context, od *woPb.OnboardingDetails, status woPb.ReviewStatus) {
	// collate item list based on review_status
	var itemList []string
	for k, v := range od.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping() {
		if v != nil && v.GetReviewAttempts() != nil && len(v.GetReviewAttempts()) > 0 {
			attemptStatus := v.GetReviewAttempts()[len(v.GetReviewAttempts())-1].GetStatus()
			if attemptStatus == status {
				itemList = append(itemList, woPb.ItemType(woPb.ItemType_value[k]).String())
			}
		}
	}
	platform, version := epificontext.AppPlatformAndVersion(ctx)
	go s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), event.NewManualReviewEvent(od.GetActorId(), od.GetCurrentWealthFlow(), version, platform, status, itemList, od.GetMetadata().GetIsFreshKra()))
}
