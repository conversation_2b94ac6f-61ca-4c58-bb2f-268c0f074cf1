package steps

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/wealthonboarding/helper"

	"github.com/epifi/gamma/api/frontend/deeplink"
	woPb "github.com/epifi/gamma/api/wealthonboarding"

	"google.golang.org/protobuf/types/known/timestamppb"
)

type StepExecutionResponse struct {
	// Note: never modify manually outside step, always use GetStepExecutionResponse to modify this
	NextOnboardingStep woPb.OnboardingStep

	// Note: never modify manually outside step, always use GetStepExecutionResponse to modify this
	CurrentStepDetails *woPb.OnboardingStepDetails
	Deeplink           *deeplink.Deeplink

	// Note: never modify manually outside step, always use GetStepExecutionResponse to modify this
	OnboardingStatus  woPb.OnboardingStatus
	StepsToInvalidate []woPb.OnboardingStep
	IsLastStep        bool
}

func (s *StepExecutionResponse) WithStepSubStatus(subStatus woPb.OnboardingStepSubStatus) *StepExecutionResponse {
	if s != nil && s.CurrentStepDetails != nil {
		s.CurrentStepDetails.SubStatus = subStatus
	}
	return s
}

func (s *StepExecutionResponse) WithStepsToInvalidate(steps ...woPb.OnboardingStep) *StepExecutionResponse {
	s.StepsToInvalidate = append(s.StepsToInvalidate, steps...)
	return s
}

// WithIsLastStep marks the onboarding status as completed if the current step is completed and is the last step in onboarding sequence
func (s *StepExecutionResponse) WithIsLastStep(isLastStep bool) *StepExecutionResponse {
	if s != nil {
		s.IsLastStep = isLastStep
		if isLastStep && s.CurrentStepDetails.Status == woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED {
			s.OnboardingStatus = woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED
		}
	}
	return s
}

// WithDowntimeDeeplink finds out the message wrapped with the custom downtime error
// and returns a deeplink with the message found
//
//nolint:errorlint
func (s *StepExecutionResponse) WithDowntimeDeeplink(err error) *StepExecutionResponse {
	// returning nil deeplink if error is nil
	if err == nil {
		return nil
	}
	s.Deeplink = helper.GetDowntimeDeeplink(helper.GetDowntimeDeeplinkDescription(err))
	return s
}

func (s *StepExecutionResponse) WithDeeplink(deeplink *deeplink.Deeplink) *StepExecutionResponse {
	s.Deeplink = deeplink
	return s
}

// nolint: funlen
func getStepMetadata(currentStep woPb.OnboardingStep, obd *woPb.OnboardingDetails) *woPb.OnboardingStepMetadata {
	res := &woPb.OnboardingStepMetadata{
		StepMetaData: &woPb.OnboardingStepMetadata_NoMetadata{
			NoMetadata: &woPb.NoMetadata{},
		},
	}
	onboardingMetadata := obd.GetMetadata()
	switch currentStep {
	case woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION:
		res.StepMetaData = &woPb.OnboardingStepMetadata_DataCollectionStepMetadata{
			DataCollectionStepMetadata: &woPb.DataCollectionStepMetadata{
				PanDetails:      onboardingMetadata.GetPanDetails(),
				PersonalDetails: onboardingMetadata.GetPersonalDetails(),
			},
		}
	case woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION:
		res.StepMetaData = &woPb.OnboardingStepMetadata_PanVerificationStepMetadata{
			PanVerificationStepMetadata: &woPb.PanVerificationStepMetadata{
				NsdlData:      onboardingMetadata.GetNsdlData(),
				NameMatchInfo: onboardingMetadata.GetNameMatchInfo(),
			},
		}
	case woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA:
		res.StepMetaData = &woPb.OnboardingStepMetadata_FetchKycInfoFromKraStepMetadata{
			FetchKycInfoFromKraStepMetadata: &woPb.FetchKycInfoFromKraStepMetadata{
				StatusData: onboardingMetadata.GetKraData().GetStatusData(),
				IsFreshKra: onboardingMetadata.GetIsFreshKra(),
			},
		}
	case woPb.OnboardingStep_ONBOARDING_STEP_LIVENESS:
		res.StepMetaData = &woPb.OnboardingStepMetadata_LivenessStepMetadata{
			LivenessStepMetadata: &woPb.LivenessStepMetadata{
				LivenessData: onboardingMetadata.GetLivenessData(),
			},
		}
	case woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO:
		res.StepMetaData = &woPb.OnboardingStepMetadata_CollectMissingInfoStepMetadata{
			CollectMissingInfoStepMetadata: &woPb.CollectMissingInfoStepMetadata{
				CkycData:             onboardingMetadata.GetCkycData(),
				CustomerProvidedData: onboardingMetadata.GetCustomerProvidedData(),
				PoaDetails:           onboardingMetadata.GetPoaDetails(),
			},
		}
	case woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET:
		res.StepMetaData = &woPb.OnboardingStepMetadata_CreateAndSignKraDocketStepMetadata{
			CreateAndSignKraDocketStepMetadata: &woPb.CreateAndSignKraDocketStepMetadata{
				KraDocketInfo: onboardingMetadata.GetKraDocketInfo(),
			},
		}
	case woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP:
		res.StepMetaData = &woPb.OnboardingStepMetadata_FinalConfirmationStepMetadata{
			FinalConfirmationStepMetadata: &woPb.FinalConfirmationStepMetadata{
				CustomerIpAddress: onboardingMetadata.GetCustomerIpAddress(),
			},
		}
	case woPb.OnboardingStep_ONBOARDING_STEP_SIGN_AGREEMENT:
		res.StepMetaData = &woPb.OnboardingStepMetadata_SignAgreementStepMetadata{
			SignAgreementStepMetadata: &woPb.SignAgreementStepMetadata{
				AgreementDocketInfo: onboardingMetadata.GetAgreementDocketInfo(),
			},
		}
	case woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION_FOR_INVESTMENT:
		res.StepMetaData = &woPb.OnboardingStepMetadata_DataCollectionForInvestmentStepMetadata{
			DataCollectionForInvestmentStepMetadata: &woPb.DataCollectionForInvestmentStepMetadata{
				EmploymentData: onboardingMetadata.GetEmploymentData(),
			},
		}
	case woPb.OnboardingStep_ONBOARDING_STEP_UPLOAD_DOCKET:
		res.StepMetaData = &woPb.OnboardingStepMetadata_UploadDocketStepMetadata{
			UploadDocketStepMetadata: &woPb.UploadDocketStepMetadata{
				UploadKraDocData: onboardingMetadata.GetUploadKraDocData(),
			},
		}
	case woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC:
		res.StepMetaData = &woPb.OnboardingStepMetadata_DownloadKraDocketStepMetadata{
			DownloadKraDocketStepMetadata: &woPb.DownloadKraDocketStepMetadata{
				DownloadedKraDocData: onboardingMetadata.GetDownloadedKraDocData(),
			},
		}
	default:
		logger.InfoNoCtx(fmt.Sprintf("no metadata for step %v", currentStep))
	}
	return res
}

type IStep interface {
	// Perform executes the step and modifies onboarding details as necessary along with returning StepExecutionResponse
	// Note: steps are expected to always use GetStepExecutionResponse to return StepExecutionResponse
	// Note: NextOnboardingStep, OnboardingStatus or CurrentStepDetails.Status fields are tightly coupled with the orchestrator logic
	// Hence if changing any of these fields, always use GetStepExecutionResponse and update all these fields together
	// TODO(Brijesh): Check if it is possible to pass only metadata insteead of onboarding details. Ideally a special request for step execution should be passed.
	Perform(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error)
}

// GetStepExecutionResponse serves the following purposes
// 1. Initialize and populate the essential details for the current onboarding step
// 2. Keep the onboarding status, step status and next onboarding step in sync
func GetStepExecutionResponse(obd *woPb.OnboardingDetails, status woPb.OnboardingStepStatus,
	currentStep woPb.OnboardingStep, nextStepOnSuccess woPb.OnboardingStep) *StepExecutionResponse {

	// Fields like ID, ExpectedRetryAt and NumAttempts are populated at orchestrator level
	// Helper methods like WithStepSubStatus, WithIsLastStep, etc. are to be used to populate other fields
	osd := &woPb.OnboardingStepDetails{
		OnboardingDetailsId: obd.GetId(),
		Status:              status,
		Step:                currentStep,
		Metadata:            getStepMetadata(currentStep, obd),
	}
	// move to the next step if the status is completed or skipped
	if status == woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED || status == woPb.OnboardingStepStatus_STEP_STATUS_SKIPPED {
		osd.CompletedAt = timestamppb.Now()
		return &StepExecutionResponse{
			NextOnboardingStep: nextStepOnSuccess,
			CurrentStepDetails: osd,
			// onboarding status is kept in progress because current step might not be the last step in sequence
			OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
		}
	}

	// in case of terminal failures, next step is kept unspecified so that orchestrator can be exited
	nextStep := currentStep
	if isTerminalStepFailure(status) {
		nextStep = woPb.OnboardingStep_ONBOARDING_STEP_UNSPECIFIED
	}

	return &StepExecutionResponse{
		NextOnboardingStep: nextStep,
		CurrentStepDetails: osd,
		OnboardingStatus:   getOnboardingStatusForStepStatus(status),
	}
}

// Keeps onboarding status and step status in sync for terminal cases (except when the step is complete)
// When the step is complete, onboarding is not marked complete since the step might not be the last step in sequence
// Default value of onboarding status is set to in progress so that orchestrator knows to continue for non-terminal cases
func getOnboardingStatusForStepStatus(status woPb.OnboardingStepStatus) woPb.OnboardingStatus {
	switch status {
	case woPb.OnboardingStepStatus_STEP_STATUS_FAILED:
		return woPb.OnboardingStatus_ONBOARDING_STATUS_FAILED
	case woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED:
		return woPb.OnboardingStatus_ONBOARDING_STATUS_MANUAL_INTERVENTION_NEEDED
	case woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE:
		return woPb.OnboardingStatus_ONBOARDING_STATUS_FUTURE_SCOPE
	default:
		return woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS
	}
}

func isTerminalStepFailure(status woPb.OnboardingStepStatus) bool {
	if status == woPb.OnboardingStepStatus_STEP_STATUS_FAILED ||
		status == woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED ||
		status == woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE {
		return true
	}
	return false
}
