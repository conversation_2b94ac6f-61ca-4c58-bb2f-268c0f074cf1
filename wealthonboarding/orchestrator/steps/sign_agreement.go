package steps

import (
	"context"

	"github.com/pkg/errors"

	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/helper"
	"github.com/epifi/gamma/wealthonboarding/kra_data"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
)

type SignAgreementStep struct {
	conf                     *config.Config
	onboardingStepDetailsDao dao.OnboardingStepDetailsDao
	currentStep              woPb.OnboardingStep
	nextStepOnSuccess        woPb.OnboardingStep
	cvlKraDataSvc            kra_data.KraData
	docHelper                helper.DocumentHelper
}

func NewSignAgreementStep(
	conf *config.Config,
	onboardingStepDetailsDao dao.OnboardingStepDetailsDao,
	cvlKraDataSvc kra_data.KraData,
	documentHelper helper.DocumentHelper) *SignAgreementStep {
	return &SignAgreementStep{
		conf:                     conf,
		onboardingStepDetailsDao: onboardingStepDetailsDao,
		currentStep:              woPb.OnboardingStep_ONBOARDING_STEP_SIGN_AGREEMENT,
		cvlKraDataSvc:            cvlKraDataSvc,
		docHelper:                documentHelper,
	}
}

// nolint:funlen,dupl
func (s *SignAgreementStep) Perform(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	if details.GetMetadata().GetAgreementDocketInfo() == nil {
		details.GetMetadata().AgreementDocketInfo = &woPb.DocketInfo{}
	}
	// signature is stored in s3, fetch the base64 version of it
	sign, err := s.docHelper.DownloadDoc(ctx, details.GetMetadata().GetCustomerProvidedData().GetSignature())
	if err != nil {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, s.currentStep, s.nextStepOnSuccess), errors.Wrap(err, "error while downloading signed doc using helper svc")
	}
	// check if there are any images present of the signature
	if len(sign.GetPhoto()) == 0 {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, s.currentStep, s.nextStepOnSuccess), errors.New("no signature found")
	}
	signData := sign.GetPhoto()[0].GetImageDataBase64()
	// create the agreement using the docs service
	agreementPdf, err := s.cvlKraDataSvc.GenerateAgreementPdf(ctx, details.GetActorId(), signData, "")
	if err != nil {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, s.currentStep, s.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_AGREEMENT_PDF_GENERATION_FAILED), errors.Wrap(err, "error in generating agreement pdf using docs service")
	}
	details.GetMetadata().GetAgreementDocketInfo().DocAsProof = agreementPdf
	return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, s.currentStep, s.nextStepOnSuccess), nil
}
