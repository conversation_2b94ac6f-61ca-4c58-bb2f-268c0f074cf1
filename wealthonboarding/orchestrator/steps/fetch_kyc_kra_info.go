package steps

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"time"

	cvlVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/cvl"
	wVendorPb "github.com/epifi/gamma/api/vendors/wealth"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/helper"
	"github.com/epifi/gamma/wealthonboarding/release"

	"github.com/pkg/errors"
	"go.uber.org/zap"
)

const StatusSubmittedAllowedDuration = 15 * 24 * time.Hour

type FetchKycInfoStep struct {
	cvlVgClient            cvlVgPb.CvlClient
	currentStep            woPb.OnboardingStep
	livenessStep           woPb.OnboardingStep
	collectMissingDataStep woPb.OnboardingStep
	downloadFromDigilocker woPb.OnboardingStep
	commonHelper           *helper.CommonHelper
	conf                   *config.Config
	releaseEvaluator       release.IEvaluator
}

func NewFetchKycInfoStep(
	cvlVgClient cvlVgPb.CvlClient,
	commonHelper *helper.CommonHelper,
	conf *config.Config,
	releaseEvaluator release.IEvaluator) *FetchKycInfoStep {
	return &FetchKycInfoStep{
		cvlVgClient:            cvlVgClient,
		currentStep:            woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
		livenessStep:           woPb.OnboardingStep_ONBOARDING_STEP_LIVENESS,
		collectMissingDataStep: woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO,
		downloadFromDigilocker: woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_FROM_DIGILOCKER,
		commonHelper:           commonHelper,
		conf:                   conf,
		releaseEvaluator:       releaseEvaluator,
	}
}

//nolint:funlen
func (f *FetchKycInfoStep) Perform(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	pan := details.GetMetadata().GetPanDetails().GetId()
	psRes, psResErr := f.cvlVgClient.GetPanStatus(ctx, &cvlVgPb.GetPanStatusRequest{
		Header:    &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_CVLKRA},
		PanNumber: pan,
	})
	if te := epifigrpc.IsRPCErrorWithDowntime(psRes, psResErr); te != nil {
		logger.Error(ctx, "error while calling GetPanStatus", zap.Error(te))
		if errors.Is(te, epifierrors.ErrDowntimeExpected) {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, f.currentStep, f.livenessStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(te), errors.Wrap(te, "error while calling GetPanStatus, downtime expected")
		}
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, f.currentStep, f.livenessStep), errors.Wrap(te, "error while calling GetPanStatus")
	}

	logger.Info(ctx, "updating kra pan status in meta")
	f.updateKraPanStatus(details, psRes)

	logger.Info(ctx, "kra GetPanEnquiry status", zap.String("status", psRes.GetPanEnquiry().GetStatus().String()))
	logger.Info(ctx, "ipv flag status", zap.String("status", psRes.GetPanEnquiry().GetIpvFlag().String()))

	// resetting the fresh kra flag so that step re execution works
	details.GetMetadata().IsFreshKra = false

	return f.checkIfFreshKYCOrModificationNeeded(ctx, details, psRes)
}

// nolint:funlen
func (f *FetchKycInfoStep) checkIfFreshKYCOrModificationNeeded(ctx context.Context, details *woPb.OnboardingDetails,
	psRes *cvlVgPb.GetPanStatusResponse) (*StepExecutionResponse, error) {

	kycStatus := psRes.GetPanEnquiry().GetStatus()

	// we want users with Aadhar as their proof of address to go forward.
	// for the rest based on kyc status we would either redirect them to KYC modification flow or move them to future scope
	// _ = psRes.GetPanEnquiry().GetCorAddProof() == wVendorPb.KraAddressProof_KRA_ADDRESS_PROOF_AADHAAR ||
	// 	psRes.GetPanEnquiry().GetPerAddProof() == wVendorPb.KraAddressProof_KRA_ADDRESS_PROOF_AADHAAR
	logger.Info(ctx, fmt.Sprintf("Corr. add. proof type = %s, Perm. add. proof type = %s, KYC status = %s", psRes.GetPanEnquiry().GetCorAddProof(), psRes.GetPanEnquiry().GetPerAddProof(), kycStatus.String()))

	switch kycStatus {
	case wVendorPb.KraStatus_KRA_STATUS_VALIDATED:
		logger.Info(ctx, "kyc status is validated")
		ipvFlag := psRes.GetPanEnquiry().GetIpvFlag()
		// IPV_NO or IPV_UNSPECIFIED should go to future scope
		if ipvFlag == wVendorPb.KraIpvFlag_KRA_IPV_FLAG_TYPE_N || ipvFlag == wVendorPb.KraIpvFlag_KRA_IPV_FLAG_TYPE_UNSPECIFIED {
			logger.Info(ctx, fmt.Sprintf("user IPV flag is %v, marking future scope", ipvFlag.String()))
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, f.currentStep, f.livenessStep), nil
		}
		// we need to pass both i.e IPV_YES and IPV_EXEMPT cases to the next step
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, f.currentStep, f.collectMissingDataStep), nil
	case wVendorPb.KraStatus_KRA_STATUS_KRA_VERIFIED:
		// Note: If KYC status is not available with any KRA, created at timestamp might not be valid, hence this check should only be done when KRA has some records for user
		if !psRes.GetPanEnquiry().GetCreatedDate().IsValid() {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, f.currentStep, f.livenessStep), errors.New("invalid kyc created timestamp in kyc record")
		}
		// TODO(Ayush): Add support for modification flow for non-aadhar POA
		res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, f.currentStep, f.livenessStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_KYC_NOT_VALIDATED_YET)
		res.Deeplink = helper.GetKycNotValidatedDeeplink(details, f.conf.InvestmentsFaqCategoryId)
		return res, nil
	case wVendorPb.KraStatus_KRA_STATUS_SUBMITTED:
		// TODO(Brijesh): Check if we should actually use CreatedDate.Refer process note.
		lastUpdateDate := psRes.GetPanEnquiry().GetLastUpdateDate()
		if lastUpdateDate.AsTime().Add(StatusSubmittedAllowedDuration).After(time.Now()) {
			// TODO(Ayush): Check and add support for modification flow for non-aadhar POA
			logger.Info(ctx, fmt.Sprintf("KYC submitted less than %s ago, waiting for it to be processed", StatusSubmittedAllowedDuration.String()))
			res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, f.currentStep, f.livenessStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_KYC_NOT_VALIDATED_YET)
			res.Deeplink = helper.GetKycNotValidatedDeeplink(details, f.conf.InvestmentsFaqCategoryId)
			return res, nil
		} else {
			logger.Info(ctx, "kra submitted is past 15 days, new kyc needed")
			details.GetMetadata().IsFreshKra = true
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, f.currentStep, f.downloadFromDigilocker).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_FRESH_KRA_NEEDED), nil
		}
	case wVendorPb.KraStatus_KRA_STATUS_NOT_AVAILABLE,
		wVendorPb.KraStatus_KRA_STATUS_EXISTING_KYC_SUBMITTED,
		wVendorPb.KraStatus_KRA_STATUS_EXISTING_KYC_VERIFIED,
		wVendorPb.KraStatus_KRA_STATUS_EXISTING_KYC_HOLD,
		wVendorPb.KraStatus_KRA_STATUS_KYC_REGISTERED_WITH_CVLMF,
		wVendorPb.KraStatus_KRA_STATUS_DEACTIVATED,
		wVendorPb.KraStatus_KRA_STATUS_REJECTED,
		wVendorPb.KraStatus_KRA_STATUS_EXISTING_KYC_REJECTED:
		logger.Info(ctx, "data upload in KRA needed. setting up isFreshKRA to true")
		details.GetMetadata().IsFreshKra = true
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, f.currentStep, f.downloadFromDigilocker).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_FRESH_KRA_NEEDED), nil
	case wVendorPb.KraStatus_KRA_STATUS_INVALID_PAN_NO_FORMAT:
		logger.Error(ctx, fmt.Sprintf("onboarding not permitted for status: %v", kycStatus))
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, f.currentStep, f.livenessStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_INVALID_PAN_NO_FORMAT), errors.New("invalid pan format")
	case wVendorPb.KraStatus_KRA_STATUS_HOLD:
		logger.Error(ctx, fmt.Sprintf("onboarding not permitted for status: %v", kycStatus))
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, f.currentStep, f.livenessStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_KRA_STATUS_HOLD), errors.New("KYC status on hold")
	case wVendorPb.KraStatus_KRA_STATUS_NOT_CHECKED_WITH_MULTIPLE_KRA,
		wVendorPb.KraStatus_KRA_STATUS_NOT_CHECKED_WITH_RESPECTIVE_KRA:
		// this is a transient status received from vendor when other KRA systems are down
		// we need to fetch the status after some time in such cases
		logger.Error(ctx, "transient invalid KRA status from vendor")
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, f.currentStep, f.livenessStep), errors.New("transient invalid KRA status from vendor")
	default:
		logger.Error(ctx, fmt.Sprintf("unhandled pan status: %v", kycStatus))
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, f.currentStep, f.livenessStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_UNHANDLED_KRA_STATUS), errors.New("invalid pan status")
	}
}

func (f *FetchKycInfoStep) updateKraPanStatus(details *woPb.OnboardingDetails, res *cvlVgPb.GetPanStatusResponse) {
	if details.GetMetadata().GetKraData().GetStatusData().GetPanStatusDetails() == nil {
		if details.GetMetadata().GetKraData().GetStatusData() == nil {
			if details.GetMetadata().GetKraData() == nil {
				if details.GetMetadata() == nil {
					details.Metadata = &woPb.OnboardingMetadata{}
				}
				details.GetMetadata().KraData = &woPb.KraData{}
			}
			details.GetMetadata().GetKraData().StatusData = &woPb.KraStatusData{}
		}
		details.GetMetadata().GetKraData().GetStatusData().PanStatusDetails = &woPb.KraStatusData_PanStatusDetails{}
	}
	psd := details.GetMetadata().GetKraData().GetStatusData().GetPanStatusDetails()
	psd.PanNumber = res.GetPanEnquiry().GetPanNumber()
	psd.Name = res.GetPanEnquiry().GetName()
	psd.Status = res.GetPanEnquiry().GetStatus()
	psd.LastUpdateDate = res.GetPanEnquiry().GetLastUpdateDate()
	psd.CreatedDate = res.GetPanEnquiry().GetCreatedDate()
	psd.ModifiedDate = res.GetPanEnquiry().GetModifiedDate()
	psd.AppStatusDelta = res.GetPanEnquiry().GetAppStatusDelta()
	psd.UpdateStatus = res.GetPanEnquiry().GetUpdateStatus()
	psd.HoldDeactivateRemarks = res.GetPanEnquiry().GetHoldDeactivateRemarks()
	psd.UpdateRemarks = res.GetPanEnquiry().UpdateRemarks
	psd.KycMode = res.GetPanEnquiry().GetKycMode()
	psd.IpvFlag = res.GetPanEnquiry().GetIpvFlag()
	psd.UboFlag = res.GetPanEnquiry().GetUboFlag()
	psd.KraCode = res.GetPanEnquiry().GetKraCode()
	psd.CorAddProof = res.GetPanEnquiry().GetCorAddProof()
	psd.PerAddProof = res.GetPanEnquiry().GetPerAddProof()
}
