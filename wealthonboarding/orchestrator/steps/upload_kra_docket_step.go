//nolint:depguard
package steps

import (
	"github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"path/filepath"
	"time"

	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"

	"github.com/epifi/gamma/api/consent"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	types "github.com/epifi/gamma/api/typesv2"
	cvlVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/cvl"
	digilockerVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/digilocker"
	wVendor "github.com/epifi/gamma/api/vendors/wealth"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	wealthCommsPb "github.com/epifi/gamma/api/wealthonboarding/comms"
	woComms "github.com/epifi/gamma/wealthonboarding/comms"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/helper"
	"github.com/epifi/gamma/wealthonboarding/kra_data"
	manualreview "github.com/epifi/gamma/wealthonboarding/manual_review"
)

type UploadKraDocketStep struct {
	kraDataSvc               kra_data.KraData
	s3Client                 s3.S3Client
	onboardingStepDetailsDao dao.OnboardingStepDetailsDao
	cvlVgClient              cvlVgPb.CvlClient
	currentStep              woPb.OnboardingStep
	nextStepOnSuccess        woPb.OnboardingStep
	manualReview             manualreview.IManualReview
	conf                     *config.Config
	digilockerVgClient       digilockerVgPb.DigilockerClient
	docHelper                helper.DocumentHelper
	woComms                  woComms.IComms
	consentClient            consent.ConsentClient
}

func NewUploadKraDocketStep(
	kraDataSvc kra_data.KraData,
	s3Client s3.S3Client,
	onboardingStepDetailsDao dao.OnboardingStepDetailsDao,
	cvlVgClient cvlVgPb.CvlClient,
	manualReview manualreview.IManualReview,
	conf *config.Config,
	digilockerVgClient digilockerVgPb.DigilockerClient,
	docHelper helper.DocumentHelper,
	woComms woComms.IComms,
	consentClient consent.ConsentClient,
) *UploadKraDocketStep {
	return &UploadKraDocketStep{
		kraDataSvc:               kraDataSvc,
		s3Client:                 s3Client,
		onboardingStepDetailsDao: onboardingStepDetailsDao,
		cvlVgClient:              cvlVgClient,
		currentStep:              woPb.OnboardingStep_ONBOARDING_STEP_UPLOAD_DOCKET,
		manualReview:             manualReview,
		conf:                     conf,
		digilockerVgClient:       digilockerVgClient,
		docHelper:                docHelper,
		woComms:                  woComms,
		consentClient:            consentClient,
	}
}

//nolint:dupl
func (u *UploadKraDocketStep) Perform(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	osd, err := u.onboardingStepDetailsDao.GetByOnboardingDetailsIdAndStep(ctx, details.GetId(), woPb.OnboardingStep_ONBOARDING_STEP_UPLOAD_DOCKET)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, u.currentStep, u.nextStepOnSuccess), errors.Wrap(err, "failed to fetch liveness step details")
	}
	if osd != nil && woPb.IsTerminalStatus(osd.GetStatus()) {
		return GetStepExecutionResponse(details, osd.GetStatus(), u.currentStep, u.nextStepOnSuccess).WithIsLastStep(true), nil
	}
	return u.executeUploadStep(ctx, details)
}

//nolint:funlen
func (u *UploadKraDocketStep) executeUploadStep(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	actorId := details.GetActorId()
	if details.GetMetadata().GetUploadKraDocData() == nil {
		details.GetMetadata().UploadKraDocData = &woPb.UploadKraDocData{}
	}
	upData := details.GetMetadata().GetUploadKraDocData()
	var upAttempt *woPb.UploadKraDocData_UploadAttempt
	if len(upData.GetUploadAttempts()) == 0 {
		upAttempt = &woPb.UploadKraDocData_UploadAttempt{
			AttemptId: uuid.New().String(),
		}
		upData.UploadAttempts = append(upData.UploadAttempts, upAttempt)
	} else {
		upAttempt = upData.GetUploadAttempts()[len(upData.GetUploadAttempts())-1]
	}

	if upAttempt.GetStatus() == woPb.UploadKraDocData_UploadAttempt_STATUS_FAILED {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED, u.currentStep, u.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_UPLOAD_ATTEMPT_FAILED), errors.New(fmt.Sprintf("attempt in failed state, marking manual intevention, actorId: %v", actorId))
	}

	// TODO:(ismail) handle case for failed retry
	if upAttempt.GetStatus() == woPb.UploadKraDocData_UploadAttempt_STATUS_UNSPECIFIED {
		logger.Info(ctx, "calling update kra api", zap.String(logger.ACTOR_ID_V2, actorId))

		// get the current kra status of user
		psRes, psResErr := u.cvlVgClient.GetPanStatus(ctx, &cvlVgPb.GetPanStatusRequest{
			Header:    &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_CVLKRA},
			PanNumber: details.GetMetadata().GetPanDetails().GetId(),
		})
		if te := epifigrpc.IsRPCErrorWithDowntime(psRes, psResErr); te != nil {
			if errors.Is(te, epifierrors.ErrDowntimeExpected) {
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, u.currentStep, u.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(te), errors.Wrap(te, "get pan status api failed")
			}
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, u.currentStep, u.nextStepOnSuccess), errors.Wrap(te, "get pan status api failed")
		}
		upAttempt.PanEnquiry = psRes.GetPanEnquiry()
		// update the latest kra status in metadata
		if details.GetMetadata().GetKraData().GetStatusData().GetPanStatusDetails() == nil {
			logger.Error(ctx, "unable to get pan status details from kra status data, skipping updating status", zap.String(logger.ACTOR_ID_V2, details.GetActorId()))
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, u.currentStep, u.nextStepOnSuccess), fmt.Errorf("unable to get pan status details from kra status data, skipping updating status")
		}
		details.GetMetadata().GetKraData().GetStatusData().GetPanStatusDetails().Status = psRes.GetPanEnquiry().GetStatus()
		updateFlag, stepExecRes, stepExecErr := u.setKYCUpdateFlag(ctx, details, psRes)
		if stepExecRes != nil {
			return stepExecRes, stepExecErr
		}
		logger.Info(ctx, fmt.Sprintf("allowing creation / modification of KYC records, update flag: %s", updateFlag.String()))
		if helper.IsAadhaarCollectedFromDigilocker(details) {
			digilockerAadhaarData := details.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData()
			if !digilockerAadhaarData.GetTs().IsValid() {
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, u.currentStep, u.nextStepOnSuccess), errors.New("digilocker aadhaar timestamp value is not valid")
			}
			// here we check that is aadhaar xml download date less than 3 day or not
			aadhaarXMLValidTill := digilockerAadhaarData.GetTs().AsTime().Add(3 * 24 * time.Hour)

			if time.Until(aadhaarXMLValidTill) < 0 || digilockerAadhaarData.GetAadhaarXmlFilePath() == "" {
				tokenRes, err := u.digilockerVgClient.GetAccessToken(ctx, &digilockerVgPb.GetAccessTokenRequest{
					Header:      &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_DIGILOCKER},
					ActorId:     details.GetActorId(),
					AccessRoute: &digilockerVgPb.GetAccessTokenRequest_RefreshToken{RefreshToken: details.GetMetadata().GetDigilockerData().GetRefreshToken()},
				})
				if te := epifigrpc.RPCError(tokenRes, err); te != nil {
					if errors.Is(te, epifierrors.ErrDowntimeExpected) {
						return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, u.currentStep, u.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(te), nil
					}
					if tokenRes.GetStatus().IsUnauthenticated() {
						res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, u.currentStep, u.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_DIGILOCKER_AUTH_NEEDED)
						res.Deeplink = helper.GetDigiLockerDeeplink(u.conf.DigilockerConfig)
						return res, nil
					}
					return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, u.currentStep, u.nextStepOnSuccess), errors.Wrap(te, "failed to GetAccessTokenResponse")
				}
				// always update refresh tokens with the newest ones received from vendor
				details.GetMetadata().GetDigilockerData().RefreshToken = tokenRes.GetTokenResponse().GetRefreshToken()
				aadhaarRes, aadhaarErr := u.digilockerVgClient.GetAadhaarInXml(ctx, &digilockerVgPb.GetAadhaarInXmlRequest{
					Header:      &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_DIGILOCKER},
					AccessToken: tokenRes.GetTokenResponse().GetAccessToken(),
				})
				if te := epifigrpc.RPCError(aadhaarRes, aadhaarErr); te != nil {
					// TODO(Brijesh): Send downtime specific deeplink when facing downtime
					return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, u.currentStep, u.nextStepOnSuccess), errors.Wrap(te, "failed to Get AadhaarInXml from VG")
				}
				fileName := fmt.Sprintf("%v/%v.%v", types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR.String(), "0", "XML")
				aadhaarXmlFilePath := filepath.Join(helper.AadhaarXMLDir, actorId, uuid.New().String(), fileName)
				if uploadErr := u.docHelper.UploadRawData(ctx, aadhaarXmlFilePath, aadhaarRes.GetAadhaarXmlData()); uploadErr != nil {
					// TODO(Brijesh): Send downtime specific deeplink when facing downtime
					return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, u.currentStep, u.nextStepOnSuccess), errors.Wrap(uploadErr, "error while upload bytes array of aadhaar xml data")
				}
				details.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData().AadhaarXmlFilePath = aadhaarXmlFilePath
				details.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData().Ts = aadhaarRes.GetTs()
				details.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData().Ttl = aadhaarRes.GetTtl()
				logger.Info(ctx, fmt.Sprint("ts time of aadhaar xml is- ", aadhaarRes.GetTs()), zap.String(logger.ACTOR_ID_V2, actorId))
			}
		}
		updateErr := u.updateKraInfo(ctx, details, updateFlag)
		if updateErr != nil {
			if errors.Is(updateErr, epifierrors.ErrDowntimeExpected) {
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, u.currentStep, u.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(updateErr), errors.Wrap(updateErr, "update kra info api failed")
			}
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, u.currentStep, u.nextStepOnSuccess), errors.Wrap(updateErr, "update kra info api failed")
		}
		upAttempt.CreatedAt = timestampPb.Now()
		upAttempt.Status = woPb.UploadKraDocData_UploadAttempt_STATUS_UPDATED_KRA_INFO
	}

	if upAttempt.GetStatus() == woPb.UploadKraDocData_UploadAttempt_STATUS_UPDATED_KRA_INFO {
		logger.Info(ctx, "initiating upload of docket to kra server", zap.String(logger.ACTOR_ID_V2, actorId))
		if helper.IsAadhaarCollectedFromDigilocker(details) {
			uploadAadhaarErr := u.uploadAadhaarXMLFileToKRASFTP(ctx, details)
			if uploadAadhaarErr != nil {
				// TODO(Brijesh): Send downtime specific deeplink when facing downtime
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, u.currentStep, u.nextStepOnSuccess), errors.Wrap(uploadAadhaarErr, "upload aadhaar xml data failed")
			}
		}
		uploadErr := u.uploadKraDocket(ctx, details)
		if uploadErr != nil {
			// TODO(Brijesh): Send downtime specific deeplink when facing downtime
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, u.currentStep, u.nextStepOnSuccess), errors.Wrap(uploadErr, "upload kra docket failed")
		}
		upAttempt.Status = woPb.UploadKraDocData_UploadAttempt_STATUS_UPLOADED_KRA_DOCKET
	}

	if upAttempt.GetStatus() == woPb.UploadKraDocData_UploadAttempt_STATUS_UPLOADED_KRA_DOCKET {
		logger.Info(ctx, "calling get pan status api for acknowledging update", zap.String(logger.ACTOR_ID_V2, actorId))
		panStatusRes, panStatusErr := u.cvlVgClient.GetPanStatus(ctx, &cvlVgPb.GetPanStatusRequest{
			Header:    &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_CVLKRA},
			PanNumber: details.GetMetadata().GetPanDetails().GetId(),
		})
		if te := epifigrpc.IsRPCErrorWithDowntime(panStatusRes, panStatusErr); te != nil {
			if errors.Is(te, epifierrors.ErrDowntimeExpected) {
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, u.currentStep, u.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(te), errors.Wrap(panStatusErr, "pan status check api failed")
			}
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, u.currentStep, u.nextStepOnSuccess), errors.Wrap(te, "pan status check api failed")
		}
		upAttempt.PanEnquiry = panStatusRes.GetPanEnquiry()
		// TODO(ismail): move to completed step based on API response
		// TODO(Brijesh): Understand why above TODO is still there, and if it can be removed
		kycRecordCreatedAt := panStatusRes.GetPanEnquiry().GetCreatedDate()
		// Note: If KYC status is not available with any KRA, created at timestamp might not be valid, hence this check should only be done when KRA has some records for user
		if !kycRecordCreatedAt.IsValid() {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, u.currentStep, u.nextStepOnSuccess), errors.New("invalid kyc created timestamp in kyc record")
		}
		nov1IST := time.Date(2022, 11, 1, 0, 0, 0, 0, datetime.IST)
		logger.Info(ctx, fmt.Sprintf("Corr. add. proof type = %s, Perm. add. proof type = %s, KYC status = %s", panStatusRes.GetPanEnquiry().GetCorAddProof(), panStatusRes.GetPanEnquiry().GetPerAddProof(), panStatusRes.GetPanEnquiry().GetStatus().String()))
		isAddressProofAadhaar := helper.IsCollectedAddressProofAadhaar(details)
		isKYCComplete := u.checkIfKYCComplete(kycRecordCreatedAt, nov1IST, isAddressProofAadhaar, panStatusRes.GetPanEnquiry().GetStatus())
		if isKYCComplete {
			upAttempt.Status = woPb.UploadKraDocData_UploadAttempt_STATUS_COMPLETED
			if isAddressProofAadhaar {
				u.woComms.SendNotificationAsync(ctx, details, wealthCommsPb.WealthCommunicationType_WEALTH_COMMUNICATION_TYPE_AADHAAR_VALIDATED_BY_KRA)
			}
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, u.currentStep, u.nextStepOnSuccess).WithIsLastStep(true), nil
		}
		// add to queue if not verified after required time
		if time.Now().After(upAttempt.GetCreatedAt().AsTime().Add(time.Duration(u.conf.CvlKra.VerificationTimeInHours) * time.Hour)) {
			_, ok := details.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping()[woPb.ItemType_ITEM_TYPE_KYC_DOCKET.String()]
			// push to queue if not already pushed
			if !ok {
				mErr := u.pushToManualReviewQueue(ctx, details)
				if mErr != nil {
					return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, u.currentStep, u.nextStepOnSuccess), mErr
				}
			}
		}
		if panStatusRes.GetPanEnquiry().GetStatus() != wVendor.KraStatus_KRA_STATUS_SUBMITTED && panStatusRes.GetPanEnquiry().GetStatus() != wVendor.KraStatus_KRA_STATUS_EXISTING_KYC_SUBMITTED {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED, u.currentStep, u.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_UPDATED_KRA_STATUS_NOT_SUPPORTED),
				errors.Errorf("undesired kra status in polling during upload flow : %v", panStatusRes.GetPanEnquiry().GetStatus())
		}

		stepExecRes := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, u.currentStep, u.nextStepOnSuccess)
		if kycRecordCreatedAt.AsTime().After(nov1IST) && isAddressProofAadhaar {
			stepExecRes.Deeplink = &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_ERROR_SCREEN,
				ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingErrorScreenOptions{
					WealthOnboardingErrorScreenOptions: &deeplinkPb.WealthOnboardingErrorScreenOptions{
						IllustrationUrl: helper.ManualVerificationIllustrationURL,
						Title:           "CVL KRA is verifying your KYC documents",
						Description:     "It takes 2 working days. Expect an SMS/email to validate your Aadhaar",
						Cta:             &deeplinkPb.Cta{Text: "Ok", Deeplink: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_HOME}},
					},
				},
			}
		}
		return stepExecRes, nil
	}
	return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, u.currentStep, u.nextStepOnSuccess), errors.New(fmt.Sprintf("unable to map status: %v", upAttempt.Status))
}

func (u *UploadKraDocketStep) checkIfKYCComplete(kycRecordCreatedAt *timestamp.Timestamp, nov1IST time.Time, isAadhaarPOA bool, kycStatus wVendor.KraStatus) bool {
	if kycRecordCreatedAt.AsTime().After(nov1IST) {
		if isAadhaarPOA && kycStatus == wVendor.KraStatus_KRA_STATUS_VALIDATED {
			return true
		}
		if !isAadhaarPOA && kycStatus == wVendor.KraStatus_KRA_STATUS_KRA_VERIFIED {
			return true
		}
	} else if kycStatus == wVendor.KraStatus_KRA_STATUS_KRA_VERIFIED || kycStatus == wVendor.KraStatus_KRA_STATUS_VALIDATED {
		return true
	}
	return false
}

// nolint:unparam,exhaustive
func (u *UploadKraDocketStep) setKYCUpdateFlag(ctx context.Context, details *woPb.OnboardingDetails, psRes *cvlVgPb.GetPanStatusResponse) (wVendor.KraAppUpdtFlg, *StepExecutionResponse, error) {
	kraStatus := details.GetMetadata().GetKraData().GetStatusData().GetPanStatusDetails().GetStatus()
	poa := psRes.GetPanEnquiry().GetCorAddProof()
	if poa == wVendor.KraAddressProof_KRA_ADDRESS_PROOF_UNSPECIFIED {
		poa = psRes.GetPanEnquiry().GetPerAddProof()
	}
	logger.Info(ctx, fmt.Sprintf("KYC status = %s, POA = %s", kraStatus.String(), poa.String()))
	updateFlag := wVendor.KraAppUpdtFlg_KRA_APP_UPDTFLG_NEW
	switch kraStatus {
	// TODO(sharath): need to move such users to download step flow
	case wVendor.KraStatus_KRA_STATUS_KRA_VERIFIED, wVendor.KraStatus_KRA_STATUS_HOLD, wVendor.KraStatus_KRA_STATUS_INVALID_PAN_NO_FORMAT:
		return 0, GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, u.currentStep, u.nextStepOnSuccess), nil
	case wVendor.KraStatus_KRA_STATUS_SUBMITTED:
		lastUpdateDate := psRes.GetPanEnquiry().GetLastUpdateDate()
		if lastUpdateDate.AsTime().Add(StatusSubmittedAllowedDuration).After(time.Now()) {
			// TODO(sharath): KRA submitted is below 15 days, need to move such users to download step flow
			return 0, GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, u.currentStep, u.nextStepOnSuccess), nil
		}
	}
	return updateFlag, nil, nil
}

func (u *UploadKraDocketStep) pushToManualReviewQueue(ctx context.Context, od *woPb.OnboardingDetails) error {
	id, err := u.manualReview.AddToReview(ctx, &woPb.ReviewPayload{
		Payload: &woPb.ReviewPayload_KycReview_{
			KycReview: &woPb.ReviewPayload_KycReview{ActorId: od.GetActorId()},
		},
	}, woPb.ItemType_ITEM_TYPE_KYC_DOCKET)
	if err != nil {
		return errors.Wrap(err, "Failed to add item to review")
	}
	sErr := u.manualReview.SubmitForReview(ctx, od.GetActorId(), []string{id})
	if sErr != nil {
		return errors.Wrap(sErr, "failed to submit for review")
	}
	logger.Info(ctx, "submitted docket for review")
	if od.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping() == nil {
		if od.GetMetadata().GetManualReviewAttempts() == nil {
			od.GetMetadata().ManualReviewAttempts = &woPb.ManualReviewAttempts{}
		}
		od.GetMetadata().GetManualReviewAttempts().ReviewAttemptMapping = make(map[string]*woPb.ManualReviewAttempts_ReviewAttemptList)
	}
	_, ok := od.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping()[woPb.ItemType_ITEM_TYPE_KYC_DOCKET.String()]
	if !ok {
		od.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping()[woPb.ItemType_ITEM_TYPE_KYC_DOCKET.String()] = &woPb.ManualReviewAttempts_ReviewAttemptList{
			ReviewAttempts: make([]*woPb.ManualReviewAttempts_ReviewAttempt, 0),
		}
	}
	reviewAttemptList := od.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping()[woPb.ItemType_ITEM_TYPE_KYC_DOCKET.String()]
	reviewAttemptList.ReviewAttempts = append(reviewAttemptList.ReviewAttempts, &woPb.ManualReviewAttempts_ReviewAttempt{
		Id:     id,
		Status: woPb.ReviewStatus_REVIEW_STATUS_PENDING,
	})
	return nil
}

func (u *UploadKraDocketStep) uploadKraDocket(ctx context.Context, details *woPb.OnboardingDetails) error {
	// get path of docket pdf
	if len(details.GetMetadata().GetKraDocketInfo().GetDocAsProof().GetS3Paths()) == 0 {
		return errors.New("no docket present in onboarding details")
	}
	docketPath := details.GetMetadata().GetKraDocketInfo().GetDocAsProof().GetS3Paths()[0]
	// fetch docket from s3
	docketData, err := u.s3Client.Read(ctx, docketPath)
	if err != nil {
		return fmt.Errorf("failed to fetch docket from s3 for actor-id: %s, err: %v, %w",
			details.GetActorId(), err.Error(), queue.ErrTransient)
	}
	// upload docket to sftp server of CVL
	if len(details.GetMetadata().GetUploadKraDocData().GetUploadAttempts()) == 0 {
		return errors.New("no attempt present to upload")
	}
	latestUploadAttempt := details.GetMetadata().GetUploadKraDocData().GetUploadAttempts()[len(details.GetMetadata().GetUploadKraDocData().GetUploadAttempts())-1]
	if latestUploadAttempt.GetCreatedAt() == nil {
		return errors.New("created at not present in latest attempt")
	}
	// upload docket to sftp server of CVL
	uploadErr := u.kraDataSvc.UploadDocketToKra(ctx, details.GetMetadata().GetPanDetails().GetId(), latestUploadAttempt.GetCreatedAt(), false, docketData)
	if uploadErr != nil {
		return fmt.Errorf("failed to upload docket to sftp server for actor-id: %s, err: %v, %w",
			details.GetActorId(), uploadErr.Error(), queue.ErrTransient)
	}
	return nil
}

func (u *UploadKraDocketStep) uploadAadhaarXMLFileToKRASFTP(ctx context.Context, details *woPb.OnboardingDetails) error {
	// get path of digilocker aadhaar xml data
	aadhaarXmlFilePath := details.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData().GetAadhaarXmlFilePath()
	if aadhaarXmlFilePath == "" {
		return errors.New("no aadhaar xml data in onboarding details")
	}
	// fetch docket from s3
	aadhaarData, err := u.s3Client.Read(ctx, aadhaarXmlFilePath)
	if err != nil {
		return errors.Wrap(err, "failed to fetch docket from s3")
	}
	// upload aadhaar xml data to sftp server of CVL
	if len(details.GetMetadata().GetUploadKraDocData().GetUploadAttempts()) == 0 {
		return errors.New("no attempt present to upload")
	}
	// here we are passing a false value for isKRAStatusOnHold in the parameter, we will pass a true value only for those cases whose status is on hold.
	// timestamp is passed as now since CVL checks present day's folder for aadhaar XML
	uploadErr := u.kraDataSvc.UploadAadhaarXmlToKra(ctx, details.GetMetadata().GetPanDetails().GetId(), timestampPb.Now(), false, aadhaarData)
	if uploadErr != nil {
		return errors.Wrap(uploadErr, fmt.Sprint("failed to upload docket to sftp server for given xml path - ", aadhaarXmlFilePath))
	}
	return nil
}

func (u *UploadKraDocketStep) updateKraInfo(ctx context.Context, details *woPb.OnboardingDetails, updateFlag wVendor.KraAppUpdtFlg) error {
	kycMode := wVendor.KraKycMode_KRA_KYC_MODE_ONLINE_DATA_ENTRY_AND_IPV
	documentSubmissionDetails := wVendor.DocumentSubmissionDetail_DOCUMENT_SUBMISSION_DETAIL_TRUE_COPIES_RECEIVED
	if helper.IsAadhaarCollectedFromDigilocker(details) {
		kycMode = wVendor.KraKycMode_KRA_KYC_MODE_DIGI_LOCKER
		// document verification is exempted for digilocker mode
		documentSubmissionDetails = wVendor.DocumentSubmissionDetail_DOCUMENT_SUBMISSION_DETAIL_EXEMPTED
	}
	var (
		ipvDate *timestampPb.Timestamp
		ipvFlag wVendor.KraIpvFlag
	)
	// ipv is exempt for digilocker flow
	if kycMode == wVendor.KraKycMode_KRA_KYC_MODE_DIGI_LOCKER {
		ipvFlag = wVendor.KraIpvFlag_KRA_IPV_FLAG_TYPE_E
	} else {
		if len(details.GetMetadata().GetLivenessData().GetLivenessAttempts()) == 0 {
			return errors.New(fmt.Sprintf("unable to update kra info, no liveness attempts are made yet, actorId: %v", details.GetActorId()))
		}
		ipvFlag = wVendor.KraIpvFlag_KRA_IPV_FLAG_TYPE_Y
		ipvDate = details.GetMetadata().GetLivenessData().GetLivenessAttempts()[len(details.GetMetadata().GetLivenessData().GetLivenessAttempts())-1].GetCompletedAt()
	}
	address := helper.GetPermanentAddressFromDetails(details)
	if address == nil {
		return errors.New("permanent address is empty")
	}

	// use KRA code received from CVL if user already has records with CVL, else use KRA CODE = CVL
	kraCode := details.GetMetadata().GetKraData().GetStatusData().GetPanStatusDetails().GetKraCode()
	if kraCode == wVendor.KraCode_KRA_CODE_TYPE_UNSPECIFIED {
		kraCode = wVendor.KraCode_KRA_CODE_CVLKRA
	}

	consentRes, err := u.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ConsentType: consent.ConsentType_FI_WEALTH_TNC,
		ActorId:     details.GetActorId(),
		Owner:       common.Owner_OWNER_EPIFI_TECH,
	})
	if err = epifigrpc.RPCError(consentRes, err); err != nil {
		return errors.Wrap(err, "error getting epifi wealth tnc consent")
	}

	// call the InsertUpdate api of CVL to update the kra details of user
	rpcRes, rpcErr := u.cvlVgClient.InsertUpdateKycRecord(ctx, &cvlVgPb.InsertUpdateKycRecordRequest{
		Header:                   &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_CVLKRA},
		AppUpdateFlag:            updateFlag,
		AppType:                  wVendor.KraAppType_KRA_APP_TYPE_INDIVIDUAL,
		PanNo:                    details.GetMetadata().GetPanDetails().GetId(),
		PanCopy:                  wVendor.KraPanCopyFlag_KRA_PAN_COPY_FLAG_YES,
		Exmt:                     wVendor.KraAppExmt_KRA_APP_EXMT_NO,
		ExmtProof:                wVendor.KraIdProof_KRA_ID_PROOF_PAN,
		IpvFlag:                  ipvFlag,
		AppIpvDate:               ipvDate,
		Gender:                   details.GetMetadata().GetPersonalDetails().GetGender(),
		Name:                     helper.GetNameForDocket(details), // this should be the same name as present in the docket pdf
		FatherName:               details.GetMetadata().GetPersonalDetails().GetFatherName(),
		DobIncorp:                datetime.DateToTimestamp(details.GetMetadata().GetPersonalDetails().GetDob(), nil),
		Nationality:              types.Nationality_NATIONALITY_INDIAN,
		ResidentialStatus:        types.ResidentialStatus_RESIDENT_INDIVIDUAL,
		CurrentAddress:           address,
		CorrespondenceAddressRef: kra_data.GetIdNumberFromPoa(details.GetMetadata().GetPoaDetails()),
		PermanentAddress:         address,
		PermanentAddressRef:      kra_data.GetIdNumberFromPoa(details.GetMetadata().GetPoaDetails()),
		MobileNumber:             helper.GetMobileNumberFromDetails(details),
		EmailId:                  helper.GetEmailIdFromDetails(details),
		CorAddProof:              helper.GetKraAddressProofType(details),
		DocumentSubmissionDetail: documentSubmissionDetails,
		MaritalStatus:            details.GetMetadata().GetPersonalDetails().GetMaritalStatus(),
		KycMode:                  kycMode,
		PerAddFlag:               wVendor.KraPanCopyFlag_KRA_PAN_COPY_FLAG_YES,
		NoOfKycRecords:           1,
		KraCode:                  kraCode,
		FatcaDeclaration: &cvlVgPb.FatcaDeclaration{
			PlaceOfBirth: "India",
			DeclaredAt:   consentRes.GetCreatedAt(),
			Pan:          details.GetMetadata().GetPanDetails().GetId(),
		},
	})
	if te := epifigrpc.IsRPCErrorWithDowntime(rpcRes, rpcErr); te != nil {
		return errors.Wrap(te, fmt.Sprintf("failed to update kra info for actor-id: %s, err: %v, %v",
			details.GetActorId(), te.Error(), queue.ErrTransient.Error()))
	}
	// check if there is any reject reason in the API response
	if rpcRes.GetKraRejectionReason() != wVendor.KraRejectionReason_KRA_REJECTION_REASON_UNSPECIFIED {
		return errors.Errorf("insert update api call rejected due to : %v", rpcRes.GetKraRejectionReason())
	}
	return nil
}
