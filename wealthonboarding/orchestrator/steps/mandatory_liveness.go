package steps

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

var (
	noLivenessAttemptFoundErr = errors.New("no liveness attempt found")

	livenessNotWealthInitiatedErr = errors.New("liveness attempt not initiated from wealth")

	prevAttemptStaleErr = errors.New("liveness attempt is stale")

	prevAttemptRejectedErr = errors.New("failed retry liveness attempt")
)

type ExistingLivenessStatus int

const (
	existingLivenessStatusUnspecified ExistingLivenessStatus = iota
	existingLivenessStatusContinue
	existingLivenessStatusPassed
	existingLivenessStatusFailed
)

type AttemptResult struct {
	// Attempt represents a valid and existing liveness attempt that has been initiated from Wealth.
	// if this attempt is not nil then it is ensured that this lv attempt is within liveness freshness threshold
	Attempt *woPb.LivenessData_LivenessAttempt
	// Status represents the Attempt result status after checking that existing lv attempt is valid or not
	// It describes the further status of the process after retrieving the existing liveness attempt.
	// - existingLivenessStatusContinue: The existing liveness attempt is pending or in progress,
	// - existingLivenessStatusPassed: The existing liveness attempt has passed,
	// - existingLivenessStatusFailed: The existing liveness attempt has failed after checking validity.
	Status ExistingLivenessStatus
}

func (p *PerformLivenessStep) PerformLivenessCheck(ctx context.Context, onboardingDetails *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	if !onboardingDetails.GetMetadata().GetIsFreshKra() {
		return nil, errors.New("liveness check not expected for user already registered with KRA")
	}

	if onboardingDetails.GetMetadata().GetLivenessData() == nil {
		onboardingDetails.GetMetadata().LivenessData = &woPb.LivenessData{}
	}

	existingLivenessResult, existingValidationError := p.getExistingValidLivenessResult(ctx, onboardingDetails)
	if existingValidationError != nil {
		logger.Info(ctx, "Failed to get existing valid liveness result", zap.Error(existingValidationError))
		freshLivenessAttempt, lvCreationErr := p.createNewLivenessWithReason(ctx, onboardingDetails.GetActorId(), existingValidationError)
		if lvCreationErr != nil {
			return GetStepExecutionResponse(onboardingDetails, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, p.currentStep, p.riskProfilingStep), errors.Wrap(lvCreationErr, "error creating liveness attempt")
		}
		livenessData := onboardingDetails.GetMetadata().GetLivenessData()
		var errReason deeplinkPb.ErrorLivenessFailure
		if len(livenessData.GetLivenessAttempts()) != 0 {
			prevAttempt := livenessData.GetLivenessAttempts()[len(livenessData.GetLivenessAttempts())-1]
			errReason = getErrorReasonFromStatus(prevAttempt.GetLivenessRejectReason())
		}
		livenessData.LivenessAttempts = append(livenessData.LivenessAttempts, freshLivenessAttempt)
		res := GetStepExecutionResponse(onboardingDetails, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, p.currentStep, p.riskProfilingStep)
		res.Deeplink = getMissingDataDeeplink(onboardingDetails, getLivenessDeeplink(freshLivenessAttempt, errReason), nil, p.conf, false, nil, true, nil)
		return res, nil
	}

	switch existingLivenessResult.Status {
	case existingLivenessStatusContinue:
		logger.Info(ctx, "existing liveness attempt is pending or in progress")
		dl := getMissingDataDeeplink(onboardingDetails, getLivenessDeeplink(existingLivenessResult.Attempt, deeplinkPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_UNSPECIFIED), nil, p.conf, false, nil, true, nil)
		return GetStepExecutionResponse(onboardingDetails, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, p.currentStep, p.riskProfilingStep).WithDeeplink(dl), nil
	case existingLivenessStatusPassed:
		existingLivenessResult.Attempt.CompletedAt = timestamppb.Now()
		// pushing video for manual review queue and marking step as manual intervention
		err := p.pushToManualReviewQueue(ctx, onboardingDetails, existingLivenessResult.Attempt)
		if err != nil {
			logger.Error(ctx, "error while pushing liveness video to manual review queue", zap.Error(err))
			return GetStepExecutionResponse(onboardingDetails, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, p.currentStep, p.riskProfilingStep), err
		}
		return GetStepExecutionResponse(onboardingDetails, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, p.currentStep, p.riskProfilingStep), nil
	case existingLivenessStatusFailed:
		return GetStepExecutionResponse(onboardingDetails, woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED, p.currentStep, p.riskProfilingStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_LIVENESS_FAILED), nil
	default:
		return GetStepExecutionResponse(onboardingDetails, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, p.currentStep, p.riskProfilingStep), errors.Errorf("unknown liveness attempt status: %d", existingLivenessResult.Status)
	}
}

func (p *PerformLivenessStep) createNewLivenessWithReason(ctx context.Context, actorId string, existingValidationError error) (*woPb.LivenessData_LivenessAttempt, error) {
	var lvCreationReason woPb.LivenessCreationReason
	switch {
	case errors.Is(existingValidationError, noLivenessAttemptFoundErr):
		lvCreationReason = woPb.LivenessCreationReason_LIVENESS_CREATION_REASON_FIRST_ATTEMPT
	case errors.Is(existingValidationError, livenessNotWealthInitiatedErr):
		lvCreationReason = woPb.LivenessCreationReason_LIVENESS_CREATION_REASON_PREV_ATTEMPT_NOT_WEALTH_INITIATED
	case errors.Is(existingValidationError, prevAttemptStaleErr):
		lvCreationReason = woPb.LivenessCreationReason_LIVENESS_CREATION_REASON_PREV_ATTEMPT_STALE
	case errors.Is(existingValidationError, prevAttemptRejectedErr):
		lvCreationReason = woPb.LivenessCreationReason_LIVENESS_CREATION_REASON_PREV_ATTEMPT_REJECTED
	default:
		return nil, errors.Wrap(existingValidationError, "unhandled existing liveness validation error")
	}

	freshLivenessAttempt, err := p.createNewLivenessAttempt(ctx, actorId, lvCreationReason)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create new liveness attempt")
	}
	return freshLivenessAttempt, nil
}

func (p *PerformLivenessStep) getExistingValidLivenessResult(ctx context.Context, onboardingDetails *woPb.OnboardingDetails) (*AttemptResult, error) {
	livenessData := onboardingDetails.GetMetadata().GetLivenessData()
	// check if liveness attempt already exists in the wealth liveness metadata
	// if exists, get the existing attempt or else create a new attempt
	if len(livenessData.GetLivenessAttempts()) == 0 {
		return nil, noLivenessAttemptFoundErr
	}

	existingLivenessAttempt := livenessData.GetLivenessAttempts()[len(livenessData.GetLivenessAttempts())-1]
	livenessValidationErr := p.checkLivenessValidity(ctx, existingLivenessAttempt.GetAttemptId())
	if livenessValidationErr != nil {
		return nil, errors.Wrap(livenessValidationErr, "error checking liveness validity")
	}

	switch existingLivenessAttempt.GetStatus() {
	case woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_IN_PROGRESS,
		woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_PENDING:
		// Refresh data of non-terminal attempts in wealth using liveness service's latest response
		lvRes, err := p.livenessClient.GetLivenessStatus(ctx, &livenessPb.GetLivenessStatusRequest{
			ActorId:   onboardingDetails.GetActorId(),
			AttemptId: existingLivenessAttempt.GetAttemptId(),
		})
		if err = epifigrpc.RPCError(lvRes, err); err != nil {
			return nil, errors.Wrap(err, "error getting liveness status")
		}

		latestLvStatus := p.getWealthLivenessStatus(ctx, lvRes.GetLivenessStatus(), lvRes.GetOtpScore(), lvRes.GetLivenessScore())
		existingLivenessAttempt.Status = latestLvStatus
		existingLivenessAttempt.LivenessRejectReason = getLivenessRejectReason(lvRes.GetLivenessStatus())
		existingLivenessAttempt.VideoLocation = lvRes.GetVideoLocation()
		switch latestLvStatus {
		case woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_PENDING, woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_IN_PROGRESS:
			return &AttemptResult{Attempt: existingLivenessAttempt, Status: existingLivenessStatusContinue}, nil
		case woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_PASSED:
			return &AttemptResult{Attempt: existingLivenessAttempt, Status: existingLivenessStatusPassed}, nil
		case woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_FAILED_RETRY:
			return nil, prevAttemptRejectedErr
		case woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_FAILED:
			return &AttemptResult{Attempt: existingLivenessAttempt, Status: existingLivenessStatusFailed}, nil
		default:
			return nil, errors.New(fmt.Sprintf("unhandled latest wealth liveness attempt status %s", latestLvStatus))
		}
	case woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_FAILED_RETRY:
		return nil, prevAttemptRejectedErr
	case woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_PASSED:
		return &AttemptResult{Attempt: existingLivenessAttempt, Status: existingLivenessStatusPassed}, nil
	case woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_FAILED:
		return &AttemptResult{Attempt: existingLivenessAttempt, Status: existingLivenessStatusFailed}, nil
	default:
		return nil, errors.New(fmt.Sprintf("unhandled existing wealth liveness attempt status %s", existingLivenessAttempt.GetStatus()))
	}
}

func (p *PerformLivenessStep) createNewLivenessAttempt(ctx context.Context, actorId string, reason woPb.LivenessCreationReason) (*woPb.LivenessData_LivenessAttempt, error) {
	attemptId := uuid.New().String()
	lvRes, err := p.livenessClient.GenerateOTP(ctx, &livenessPb.GenerateOTPRequest{
		LivenessOptions: &livenessPb.LivenessOptions{
			ActorId:      actorId,
			AttemptId:    attemptId,
			LivenessFlow: livenessPb.LivenessFlow_WEALTH_ONBOARDING,
		},
	})
	if err = epifigrpc.RPCError(lvRes, err); err != nil {
		return nil, errors.Wrap(err, "error while generating otp for wealth onboarding liveness check")
	}
	livenessAttempt := &woPb.LivenessData_LivenessAttempt{
		AttemptId:              attemptId,
		Otp:                    lvRes.GetOtp(),
		Status:                 woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_IN_PROGRESS,
		IsInitiatedFromWealth:  true,
		LivenessCreationReason: reason,
		LivenessRejectReason:   woPb.LivenessRejectReason_LIVENESS_REJECT_REASON_UNSPECIFIED,
	}
	return livenessAttempt, nil
}

func (p *PerformLivenessStep) checkLivenessValidity(ctx context.Context, attemptId string) error {
	res, err := p.livenessClient.GetLivenessAttempt(ctx, &livenessPb.GetLivenessAttemptRequest{
		LivenessReqId: attemptId,
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		// here we do not handle record not found case because this scenario is not possible
		// because wealth liveness will have attempt id if and only if liveness is initiated by auth service any time in past
		return errors.Wrap(err, "error getting liveness attempt")
	}
	if res.GetLivenessAttempt().GetLivenessFlow() != livenessPb.LivenessFlow_WEALTH_ONBOARDING {
		return errors.Wrap(livenessNotWealthInitiatedErr, fmt.Sprintf("liveness flow initiated from %s", res.GetLivenessAttempt().GetLivenessFlow()))
	}

	// check if the liveness attempt that we have is stale or not, if it is stale then we need to create new attempt
	if p.isStale(res.GetLivenessAttempt()) {
		return errors.Wrap(prevAttemptStaleErr, fmt.Sprintf("lv completed at: %v, lv staleness threshold: %v", res.GetLivenessAttempt().GetUpdatedAt().AsTime(), p.conf.LivenessConfig.LivenessStalenessDuration))
	}
	return nil
}

func getLivenessDeeplink(livenessAttempt *woPb.LivenessData_LivenessAttempt, errReason deeplinkPb.ErrorLivenessFailure) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_CHECK_LIVENESS,
		ScreenOptions: &deeplinkPb.Deeplink_CheckLivenessScreenOptions{
			CheckLivenessScreenOptions: &deeplinkPb.CheckLivenessScreenOptions{
				AttemptId:                  livenessAttempt.GetAttemptId(),
				Otp:                        livenessAttempt.GetOtp(),
				LivenessFlow:               deeplinkPb.LivenessFlow_WEALTH_ONBOARDING,
				DisableFaceTrackingAndroid: true,
				ErrorLastLivenessFailure:   errReason,
			},
		},
	}
}

func (p *PerformLivenessStep) isStale(livenessAttempt *livenessPb.LivenessAttempt) bool {
	if !livenessAttempt.GetUpdatedAt().IsValid() {
		return true
	}
	livenessCompletedAt := livenessAttempt.GetUpdatedAt().AsTime()
	// calculate the threshold time for liveness by subtracting the configured staleness duration from the current time.
	// any event with a timestamp older than this threshold from current time is considered stale and not live.
	livenessFreshnessThreshold := time.Now().Add(-p.conf.LivenessConfig.LivenessStalenessDuration)
	if livenessCompletedAt.Before(livenessFreshnessThreshold) {
		return true
	}
	return false
}
