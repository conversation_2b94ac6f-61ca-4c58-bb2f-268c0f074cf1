package steps

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"testing"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	s3Mocks "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	"github.com/epifi/gamma/wealthonboarding/config"
	mocks "github.com/epifi/gamma/wealthonboarding/test/mocks/esign"
	mock_evaluator "github.com/epifi/gamma/wealthonboarding/test/mocks/release"

	"github.com/golang/mock/gomock"
	"github.com/jinzhu/copier"
)

func TestAdvisoryAgreementStep_Perform(t *testing.T) {
	ctr := gomock.NewController(t)
	mockEvaluator := mock_evaluator.NewMockIEvaluator(ctr)
	mockEsign := mocks.NewMockESign(ctr)
	mockS3Client := s3Mocks.NewMockS3Client(ctr)
	type args struct {
		details *woPb.OnboardingDetails
	}
	type fields struct {
		conf *config.Config
	}
	mandatoryConf := &config.Config{}
	err := copier.Copy(mandatoryConf, conf)
	if err != nil {
		t.Errorf("error in copying config")
	}
	tests := []struct {
		name       string
		args       args
		fields     fields
		setupMocks func()
		want       *StepExecutionResponse
		wantErr    bool
	}{
		{
			name: "user wants to skip the step",
			args: args{
				details: &woPb.OnboardingDetails{
					Metadata: &woPb.OnboardingMetadata{
						CustomerProvidedData: &woPb.CustomerProvidedData{
							SkipSigningAdvisoryAgreement: commontypes.BooleanEnum_TRUE,
						},
						IsFreshKra: true,
					},
				},
			},
			fields: fields{
				conf: conf,
			},
			setupMocks: func() {},
			want: GetStepExecutionResponse(
				&woPb.OnboardingDetails{
					Metadata: &woPb.OnboardingMetadata{
						CustomerProvidedData: &woPb.CustomerProvidedData{
							SkipSigningAdvisoryAgreement: commontypes.BooleanEnum_TRUE,
						},
						IsFreshKra: true,
					},
				},
				woPb.OnboardingStepStatus_STEP_STATUS_SKIPPED,
				woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT,
				woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET,
			),
			wantErr: false,
		},
		{
			name: "esign state signed",
			args: args{
				details: &woPb.OnboardingDetails{
					Metadata: &woPb.OnboardingMetadata{
						IsFreshKra: true,
						PersonalDetails: &woPb.PersonalDetails{
							AdvisoryAgreementDetails: &woPb.AdvisoryAgreementDetails{
								EsignTxnDetails: &woPb.EsignInfo{
									EsignId:  "1",
									EsignUrl: "url",
								},
							},
						},
					},
				},
			},
			fields: fields{
				conf: conf,
			},
			setupMocks: func() {
				mockEsign.EXPECT().GetLatestESignDetails(gomock.Any(), "1").Return(&woPb.ESignDetails{
					CreatedAt: timestamp.Now(),
					TxnState:  woPb.TxnState_TXN_STATE_SIGNED,
				}, nil).Times(1)
				mockEsign.EXPECT().IsValidEsign(gomock.Any(), "1").Return(true, nil).Times(1)
				mockEsign.EXPECT().GetSignUrl(gomock.Any(), "1").Return("url", nil).Times(1)
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					Status:    woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS,
					Step:      woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT,
					Metadata:  getStepMetadata(woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, nil),
					SubStatus: woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CUSTOMER_SIGNED,
				},
				Deeplink: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_LANDING_SCREEN,
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
			},
			wantErr: false,
		},
		{
			name: "esign state completed and document not available",
			args: args{
				details: &woPb.OnboardingDetails{
					Metadata: &woPb.OnboardingMetadata{
						IsFreshKra: true,
						PersonalDetails: &woPb.PersonalDetails{
							AdvisoryAgreementDetails: &woPb.AdvisoryAgreementDetails{
								EsignTxnDetails: &woPb.EsignInfo{
									EsignId:  "1",
									EsignUrl: "url",
									Status:   woPb.TxnState_TXN_STATE_COMPLETED,
								},
							},
						},
					},
				},
			},
			fields: fields{
				conf: conf,
			},
			setupMocks: func() {
				mockEsign.EXPECT().GetLatestESignDetails(gomock.Any(), "1").Return(&woPb.ESignDetails{
					TxnState:  woPb.TxnState_TXN_STATE_COMPLETED,
					CreatedAt: timestamp.Now(),
				}, nil).Times(1)
				mockEsign.EXPECT().IsValidEsign(gomock.Any(), "1").Return(true, nil).Times(1)
				mockEsign.EXPECT().GetSignUrl(gomock.Any(), "1").Return("url", nil).Times(1)
				mockEsign.EXPECT().GetSignedDocS3Path(gomock.Any(), "1").Return("s3path", nil).Times(1)
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					Status:   woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					Step:     woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT,
					Metadata: getStepMetadata(woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, nil),
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
			},
			wantErr: false,
		},
		{
			name: "esign state completed and document available",
			args: args{
				details: &woPb.OnboardingDetails{
					Metadata: &woPb.OnboardingMetadata{
						IsFreshKra: true,
						PersonalDetails: &woPb.PersonalDetails{
							AdvisoryAgreementDetails: &woPb.AdvisoryAgreementDetails{
								SignedAgreementS3Path: "s3path",
								EsignTxnDetails: &woPb.EsignInfo{
									EsignId:  "1",
									EsignUrl: "url",
									Status:   woPb.TxnState_TXN_STATE_COMPLETED,
								},
							},
						},
					},
				},
			},
			fields: fields{
				conf: conf,
			},
			setupMocks: func() {},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					Status:   woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					Step:     woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT,
					Metadata: getStepMetadata(woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, nil),
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &AdvisoryAgreementStep{
				eSign:            mockEsign,
				conf:             tt.fields.conf,
				releaseEvaluator: mockEvaluator,
				s3Client:         mockS3Client,
			}
			tt.setupMocks()
			got, err := c.Perform(context.Background(), tt.args.details)
			if (err != nil) != tt.wantErr {
				t.Errorf("Perform() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := cmp.Options{
				protocmp.Transform(),
				protocmp.IgnoreFields(&woPb.OnboardingStepDetails{}, "completed_at"),
				protocmp.IgnoreFields(&deeplinkPb.Deeplink{}, "wealth_onboarding_capture_missing_data_screen_options"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("Mismatch (-want +got):\n%s", diff)
			}
		})
	}
}
