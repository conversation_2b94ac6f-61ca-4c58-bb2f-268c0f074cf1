// nolint:goimports
package steps

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/api/typesv2/common"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/investment/profile"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/release"
)

type RiskProfilingStep struct {
	conf                    *config.Config
	releaseEvaluator        release.IEvaluator
	currentStep             woPb.OnboardingStep
	nextStep                woPb.OnboardingStep
	investmentProfileClient profile.InvestmentProfileServiceClient
	consentClient           consent.ConsentClient
}

func NewRiskProfilingStep(conf *config.Config, releaseEvaluator release.IEvaluator, investmentProfileClient profile.InvestmentProfileServiceClient,
	consentClient consent.ConsentClient) *RiskProfilingStep {
	return &RiskProfilingStep{
		conf:                    conf,
		releaseEvaluator:        releaseEvaluator,
		currentStep:             woPb.OnboardingStep_ONBOARDING_STEP_INVESTMENT_RISK_PROFILING,
		nextStep:                woPb.OnboardingStep_ONBOARDING_STEP_UPDATE_NOMINEE_DETAILS,
		investmentProfileClient: investmentProfileClient,
		consentClient:           consentClient,
	}
}

func (r *RiskProfilingStep) Perform(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	isRiskProfilingEnabled, updateAppDl, err := r.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_RISK_PROFILING).WithActorId(details.GetActorId()))
	if err != nil {
		logger.Error(ctx, "failed to check if riskProfiling is enabled for user", zap.Error(err))
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, r.currentStep, r.nextStep), nil
	}
	if updateAppDl != nil {
		updateAppDl.GetWealthOnboardingStatusScreenOptions().Title = "Update Fi app to start investing"
		updateAppDl.GetWealthOnboardingStatusScreenOptions().Description = "For a smoother and better investment experience on Fi, please update the app"
		res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, r.currentStep, r.nextStep)
		res.Deeplink = updateAppDl
		return res, nil
	}
	if !isRiskProfilingEnabled {
		logger.Info(ctx, "risk profiling step is not enabled for user")
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_SKIPPED, r.currentStep, r.nextStep), nil
	}
	investmentProfileResp, investmentProfileErr := r.investmentProfileClient.GetInvestmentRiskProfileForWealthOnboarding(ctx, &profile.GetInvestmentRiskProfileForWealthOnboardingRequest{
		ActorId: details.GetActorId(),
	})
	if rpcErr := epifigrpc.RPCError(investmentProfileResp, investmentProfileErr); rpcErr != nil {
		logger.Error(ctx, "failed to get investment profile for actor", zap.Error(rpcErr))
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, r.currentStep, r.nextStep), nil
	}
	if investmentProfileResp.GetRiskProfileStatus() == profile.GetInvestmentRiskProfileForWealthOnboardingResponse_RISK_PROFILE_CONSENT_PROVIDED {
		// handle consent_id not in onboarding details (case when consent is saved in consents table but not in onb details)
		if details.GetMetadata().GetCustomerProvidedData().GetRiskProfilingConsentId() == "" {
			consentErr := r.fetchAndPersistRiskConsentIdInOnbDetails(ctx, details)
			if consentErr != nil {
				logger.Error(ctx, "failed to fetchAndPersist consent in onb details", zap.Error(consentErr))
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, r.currentStep, r.nextStep), nil
			}
		}
		// this flag will be used to determine if the risk survey is complete for the user
		details.GetMetadata().IsInvestmentRiskSurveyComplete = true
		logger.Info(ctx, "risk profile step completed for actor (both survey and consent)")
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, r.currentStep, r.nextStep), nil
	}
	if investmentProfileResp.GetNextAction() == nil {
		logger.Error(ctx, "expected next action in investmentRiskProfile response", zap.String(logger.STATUS, investmentProfileResp.GetRiskProfileStatus().String()))
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, r.currentStep, r.nextStep), nil
	}
	return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, r.currentStep, r.nextStep).
		WithDeeplink(investmentProfileResp.GetNextAction()), nil
}

func (r *RiskProfilingStep) fetchAndPersistRiskConsentIdInOnbDetails(ctx context.Context, details *woPb.OnboardingDetails) error {
	consentResp, err := r.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ConsentType: consent.ConsentType_EPIFI_WEALTH_INVESTMENT_RISK_PROFILE,
		ActorId:     details.GetActorId(),
		Owner:       common.Owner_OWNER_EPIFI_TECH,
	})
	if rpcErr := epifigrpc.RPCError(consentResp, err); rpcErr != nil {
		return fmt.Errorf("failed to get investment_risK_profile consent for actor : %w", rpcErr)
	}
	if details.GetMetadata().GetCustomerProvidedData() == nil {
		return fmt.Errorf("customer provided data object is not initialised")
	}
	details.GetMetadata().GetCustomerProvidedData().RiskProfilingConsentId = consentResp.GetConsentId()
	return nil
}
