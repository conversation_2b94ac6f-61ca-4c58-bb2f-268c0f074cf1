package steps

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/epifi/gamma/api/consent"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/release"
)

var (
	consentScreenTitle       = "Start investing in mutual funds on Fi"
	tncHtmlText              = "I accept the <a style='color: #00B899; text-decoration: none;' href=\"https://fi.money/wealth/TnC\">epiFi Wealth T&C</a>, and allow epiFi Tech to share PAN, DOB, and other details with epiFi Wealth, and give consent to epiFi Wealth to download my CKYC and KRA records to complete KYC."
	consentScreenDescription = "We've made investments effortless! Choose a fund, set the amount and frequency at which to invest, and voila! Watch your money grow."
	startInvestingImgUrl     = "https://epifi-icons.pointz.in/Wealth/wo-start-investing-v2.png"
)

type TakeUserConsent struct {
	consentClient     consent.ConsentClient
	currentStep       woPb.OnboardingStep
	nextStepOnSuccess woPb.OnboardingStep
	releaseEvaluator  release.IEvaluator
}

func NewTakeUserConsent(consentClient consent.ConsentClient, releaseEvaluator release.IEvaluator) *TakeUserConsent {
	return &TakeUserConsent{
		consentClient:     consentClient,
		currentStep:       woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
		nextStepOnSuccess: woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
		releaseEvaluator:  releaseEvaluator,
	}
}

func (t *TakeUserConsent) Perform(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	isReleased, _, err := t.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_TNC_V2).WithActorId(details.GetActorId()))
	if err != nil {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, t.currentStep, t.nextStepOnSuccess).
				WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_FEATURE_EVALUATION_FAILED),
			fmt.Errorf("failed to evaluate if user is eligible for new tnc page : %w", err)
	}
	if !isReleased {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_SKIPPED, t.currentStep, t.nextStepOnSuccess), nil
	}
	consentResp, consentErr := t.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ConsentType: consent.ConsentType_FI_WEALTH_TNC,
		ActorId:     details.GetActorId(),
		Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
	})
	if rpcErr := epifigrpc.RPCError(consentResp, consentErr); rpcErr != nil && !consentResp.GetStatus().IsRecordNotFound() {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, t.currentStep, t.nextStepOnSuccess).
				WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CONSENT_FETCH_FAILED),
			fmt.Errorf("failed to fetch fi wealth tnc consent for actor : %w", rpcErr)
	}
	// no consent found for actor
	if consentResp.GetStatus().IsRecordNotFound() {
		res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, t.currentStep, t.nextStepOnSuccess)
		res.Deeplink = t.consentScreenDeeplink()
		return res, nil
	}
	return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, t.currentStep, t.nextStepOnSuccess), nil
}

func (t *TakeUserConsent) consentScreenDeeplink() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_AGREEMENT_CONFIRMATION_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingSignAgreementScreenOptions{
			WealthOnboardingSignAgreementScreenOptions: &deeplinkPb.WealthOnboardingSignAgreementScreenOptions{
				Title:           consentScreenTitle,
				Description:     consentScreenDescription,
				IllustrationUrl: startInvestingImgUrl,
				Consents: []*widget.CheckboxItem{
					{
						Id:          consent.ConsentType_FI_WEALTH_TNC.String(),
						DisplayText: commontypes.GetHtmlText(tncHtmlText).WithFontStyle(commontypes.FontStyle_BODY_4).WithFontColor(colors.ColorSlate),
						IsChecked:   false,
					},
				},
			},
		},
	}
}
