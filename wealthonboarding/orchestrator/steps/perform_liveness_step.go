//nolint:exhaustive
package steps

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/dao"
	manualreview "github.com/epifi/gamma/wealthonboarding/manual_review"
	"github.com/epifi/gamma/wealthonboarding/release"
)

// maxLvAttemptLimit defines the maximum amount of liveness attempts returned by the liveness service
const maxLvAttemptLimit = 100

type PerformLivenessStep struct {
	conf                       *config.Config
	livenessClient             livenessPb.LivenessClient
	onboardingStepDetailsDao   dao.OnboardingStepDetailsDao
	currentStep                woPb.OnboardingStep
	createAndSignKraDocketStep woPb.OnboardingStep
	riskProfilingStep          woPb.OnboardingStep
	manualReviewStep           woPb.OnboardingStep
	manualReview               manualreview.IManualReview
	releaseEvaluator           release.IEvaluator
}

func NewPerformLivenessStep(
	conf *config.Config,
	livenessClient livenessPb.LivenessClient,
	onboardingStepDetailsDao dao.OnboardingStepDetailsDao,
	manualReview manualreview.IManualReview,
	releaseEvaluator release.IEvaluator) *PerformLivenessStep {
	return &PerformLivenessStep{
		conf:                       conf,
		livenessClient:             livenessClient,
		onboardingStepDetailsDao:   onboardingStepDetailsDao,
		currentStep:                woPb.OnboardingStep_ONBOARDING_STEP_LIVENESS,
		createAndSignKraDocketStep: woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET,
		riskProfilingStep:          woPb.OnboardingStep_ONBOARDING_STEP_INVESTMENT_RISK_PROFILING,
		manualReviewStep:           woPb.OnboardingStep_ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW,
		manualReview:               manualReview,
		releaseEvaluator:           releaseEvaluator,
	}
}

func (p *PerformLivenessStep) Perform(ctx context.Context, onboardingDetails *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	onboardingStepDetails, err := p.onboardingStepDetailsDao.GetByOnboardingDetailsIdAndStep(ctx, onboardingDetails.GetId(), woPb.OnboardingStep_ONBOARDING_STEP_LIVENESS)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return GetStepExecutionResponse(onboardingDetails, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, p.currentStep, p.createAndSignKraDocketStep), errors.Wrap(err, "failed to fetch liveness step details")
	}
	var (
		isRiskProfilingSupported bool
		updateAppDeeplink        *deeplinkPb.Deeplink
	)

	if onboardingDetails.GetMetadata().GetCustomerProvidedData().GetRiskProfilingConsentId() == "" {
		isRiskProfilingSupported, updateAppDeeplink, err = p.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_RISK_PROFILING).WithActorId(onboardingDetails.GetActorId()))
		if err != nil {
			return nil, errors.Wrap(err, "error checking if risk profiling step is enabled")
		}
		if updateAppDeeplink != nil {
			res := GetStepExecutionResponse(onboardingDetails, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, p.currentStep, woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT)
			res.Deeplink = updateAppDeeplink
			return res, nil
		}
	}

	var nextStep woPb.OnboardingStep
	if onboardingStepDetails != nil && woPb.IsTerminalStatus(onboardingStepDetails.GetStatus()) {
		switch {
		case isRiskProfilingSupported:
			nextStep = woPb.OnboardingStep_ONBOARDING_STEP_INVESTMENT_RISK_PROFILING
		case onboardingDetails.GetMetadata().GetPersonalDetails().GetAdvisoryAgreementDetails().GetSignedAgreementS3Path() == "":
			nextStep = woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT
		default:
			nextStep = woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET
		}
		return GetStepExecutionResponse(onboardingDetails, onboardingStepDetails.GetStatus(), p.currentStep, nextStep), nil
	}

	isMandatoryLivenessEnabled, _, mandatoryLivenessErr := p.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_MANDATORY_LIVENESS).WithActorId(onboardingDetails.GetActorId()))
	if mandatoryLivenessErr != nil {
		return nil, errors.Wrap(mandatoryLivenessErr, "error checking if mandatory liveness is enabled")
	}
	if isMandatoryLivenessEnabled {
		return p.PerformLivenessCheck(ctx, onboardingDetails)
	}
	return p.VerifyLiveness(ctx, onboardingDetails)
}

//nolint:funlen
func (p *PerformLivenessStep) VerifyLiveness(ctx context.Context, od *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	if !od.GetMetadata().GetIsFreshKra() {
		return nil, errors.New("cannot perform liveness step for existing kra users")
	}
	if od.GetMetadata().GetLivenessData() == nil {
		od.GetMetadata().LivenessData = &woPb.LivenessData{}
	}
	lvData := od.GetMetadata().GetLivenessData()
	isFirstAttempt := len(lvData.GetLivenessAttempts()) == 0
	var lvAttempt *woPb.LivenessData_LivenessAttempt
	if len(lvData.GetLivenessAttempts()) == 0 {
		lvAttempt = &woPb.LivenessData_LivenessAttempt{
			AttemptId: uuid.New().String(),
		}
		// check if the existing liveness done by the user during onboarding is valid or not
		addLvAttemptInfoIfValid(ctx, od.GetActorId(), lvAttempt, p.livenessClient, p.conf)
		lvData.LivenessAttempts = append(lvData.LivenessAttempts, lvAttempt)
	} else {
		lvAttempt = lvData.GetLivenessAttempts()[len(lvData.GetLivenessAttempts())-1]
	}

	var (
		zapReqId = zap.String(logger.REQUEST_ID, lvAttempt.GetAttemptId())
	)

	lvStatus := lvAttempt.GetStatus()
	lvRejectReason := lvAttempt.GetLivenessRejectReason()

	var isRiskProfilingSupported bool
	isRiskProfilingSupported, updateAppDeeplink, err := p.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_RISK_PROFILING).WithActorId(od.GetActorId()))
	if err != nil {
		return nil, errors.Wrap(err, "error checking if risk profile is enabled")
	}
	if updateAppDeeplink != nil {
		res := GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, p.currentStep, p.createAndSignKraDocketStep)
		res.Deeplink = updateAppDeeplink
		return res, nil
	}

	var nextStep woPb.OnboardingStep
	switch {
	case isRiskProfilingSupported:
		nextStep = woPb.OnboardingStep_ONBOARDING_STEP_INVESTMENT_RISK_PROFILING
	default:
		nextStep = woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT
	}

	if lvStatus == woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_UNSPECIFIED || lvStatus == woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_IN_PROGRESS ||
		lvStatus == woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_PENDING {
		lvRes, lvErr := p.livenessClient.GetLivenessStatus(ctx, &livenessPb.GetLivenessStatusRequest{ActorId: od.GetActorId(), AttemptId: lvAttempt.GetAttemptId()})
		te := epifigrpc.RPCError(lvRes, lvErr)
		if lvRes.GetStatus().GetCode() != rpc.StatusRecordNotFound().GetCode() && te != nil {
			logger.Error(ctx, "error in GetLivenessStatus", zapReqId, zap.Error(te))
			return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, p.currentStep, p.createAndSignKraDocketStep), errors.Wrap(te, "liveness status check failed")
		}
		lvStatus = p.getWealthLivenessStatus(ctx, lvRes.GetLivenessStatus(), lvRes.GetOtpScore(), lvRes.GetLivenessScore())
		lvRejectReason = getLivenessRejectReason(lvRes.GetLivenessStatus())
		lvAttempt.Status = lvStatus
		lvAttempt.VideoLocation = lvRes.GetVideoLocation()
		lvAttempt.LivenessRejectReason = lvRejectReason
	}
	errReason := getErrorReasonFromStatus(lvRejectReason)
	switch lvStatus {
	case woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_UNSPECIFIED:
		logger.Info(ctx, "initiating liveness")
	case woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_IN_PROGRESS:
		logger.Info(ctx, "liveness is in in_progress state")
		res := GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, p.currentStep, p.createAndSignKraDocketStep)
		livenessDl := &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_CHECK_LIVENESS,
			ScreenOptions: &deeplinkPb.Deeplink_CheckLivenessScreenOptions{
				CheckLivenessScreenOptions: &deeplinkPb.CheckLivenessScreenOptions{
					AttemptId:                  lvAttempt.GetAttemptId(),
					Otp:                        lvAttempt.GetOtp(),
					LivenessFlow:               deeplinkPb.LivenessFlow_WEALTH_ONBOARDING,
					DisableFaceTrackingAndroid: true,
				},
			},
		}
		res.Deeplink = getMissingDataDeeplink(od, livenessDl, nil, p.conf, false, nil, isRiskProfilingSupported, nil)
		return res, nil
	case woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_PENDING:
		logger.Info(ctx, "liveness is in in_progress state")
		res := GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, p.currentStep, p.createAndSignKraDocketStep)
		livenessDl := &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_CHECK_LIVENESS,
			ScreenOptions: &deeplinkPb.Deeplink_CheckLivenessScreenOptions{
				CheckLivenessScreenOptions: &deeplinkPb.CheckLivenessScreenOptions{
					AttemptId:                  lvAttempt.GetAttemptId(),
					Otp:                        lvAttempt.GetOtp(),
					LivenessFlow:               deeplinkPb.LivenessFlow_WEALTH_ONBOARDING,
					DisableFaceTrackingAndroid: true,
				},
			},
		}
		res.Deeplink = getMissingDataDeeplink(od, livenessDl, nil, p.conf, false, nil, isRiskProfilingSupported, nil)
		return res, nil
	case woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_PASSED:
		lvAttempt.CompletedAt = timestamppb.Now()
		// pushing video for manual review queue and marking step as manual intervention
		mrErr := p.pushToManualReviewQueue(ctx, od, lvAttempt)
		if mrErr != nil {
			logger.Error(ctx, "error while pushing liveness video to manual review queue", zap.Error(mrErr))
			return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, p.currentStep, p.createAndSignKraDocketStep), mrErr
		}
		return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, p.currentStep, nextStep), nil
	case woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_FAILED_RETRY:
		logger.Info(ctx, "re-initiating liveness")
		lvAttempt = &woPb.LivenessData_LivenessAttempt{
			AttemptId: uuid.New().String(),
		}
		lvData.LivenessAttempts = append(lvData.LivenessAttempts, lvAttempt)
	case woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_FAILED:
		return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED, p.currentStep, p.createAndSignKraDocketStep).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_LIVENESS_FAILED), nil
	}
	if lvAttempt.GetOtp() == "" {
		respLv, errLv := p.livenessClient.GenerateOTP(ctx, &livenessPb.GenerateOTPRequest{
			LivenessOptions: &livenessPb.LivenessOptions{
				ActorId:      od.GetActorId(),
				AttemptId:    lvAttempt.GetAttemptId(),
				LivenessFlow: livenessPb.LivenessFlow_WEALTH_ONBOARDING,
			},
		})
		if err := epifigrpc.RPCError(respLv, errLv); err != nil {
			logger.Error(ctx, "error while initiating liveness match", zapReqId, zap.Error(err))
			return GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, p.currentStep, p.createAndSignKraDocketStep), errors.Wrap(err, "error while initiating liveness")
		}
		lvAttempt.Otp = respLv.GetOtp()
		lvAttempt.Status = woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_PENDING
		lvAttempt.IsInitiatedFromWealth = true
	}
	logger.Info(ctx, "liveness initiated", zapReqId)
	res := GetStepExecutionResponse(od, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, p.currentStep, p.createAndSignKraDocketStep)
	livenessDl := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_CHECK_LIVENESS,
		ScreenOptions: &deeplinkPb.Deeplink_CheckLivenessScreenOptions{
			CheckLivenessScreenOptions: &deeplinkPb.CheckLivenessScreenOptions{
				AttemptId:                  lvAttempt.GetAttemptId(),
				Otp:                        lvAttempt.GetOtp(),
				LivenessFlow:               deeplinkPb.LivenessFlow_WEALTH_ONBOARDING,
				ErrorLastLivenessFailure:   errReason,
				DisableFaceTrackingAndroid: true,
			},
		},
	}
	if isFirstAttempt {
		// this is the very first attempt for the user, show data capture screen with missing data deep link including liveness video
		res.Deeplink = getMissingDataDeeplink(od, livenessDl, nil, p.conf, false, nil, isRiskProfilingSupported, nil)
	} else {
		// this is a retry attempt for a liveness video, we need show the CHECK_LIVENESS_SCREEN with last liveness failure
		res.Deeplink = livenessDl
	}
	if lvStatus == woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_FAILED_RETRY {
		res = res.WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_LIVENESS_FAILED_WITH_RETRY)
	}
	return res, nil
}

func (p *PerformLivenessStep) getWealthLivenessStatus(ctx context.Context, livenessStatus livenessPb.LivenessStatus, otpScore, lvScore float32) woPb.LivenessData_LivenessAttempt_LivenessStatus {
	switch livenessStatus {
	case livenessPb.LivenessStatus_LIVENESS_STATUS_UNSPECIFIED:
		return woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_UNSPECIFIED
	// cases of PENDING indicate that we are waiting for the vendor response to assign scores and status
	case livenessPb.LivenessStatus_LIVENESS_OTP_RECEIVED,
		livenessPb.LivenessStatus_LIVENESS_STREAMING_STARTED:
		return woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_PENDING
	// cases in which get a score from the liveness service
	case livenessPb.LivenessStatus_LIVENESS_PASSED,
		livenessPb.LivenessStatus_LIVENESS_MANUAL_RETRY,
		livenessPb.LivenessStatus_LIVENESS_FAILED_RETRY,
		livenessPb.LivenessStatus_LIVENESS_MANUALLY_PASSED:
		return p.wealthLivenessStatusFromScore(ctx, livenessStatus, otpScore, lvScore)
	// following are the case of terminal status for liveness, we need to initiate a new one for such cases
	case livenessPb.LivenessStatus_LIVENESS_FACE_NOT_DETECTED,
		livenessPb.LivenessStatus_LIVENESS_MULTIPLE_FACES_DETECTED,
		livenessPb.LivenessStatus_LIVENESS_INVALID_VIDEO,
		livenessPb.LivenessStatus_LIVENESS_FAILED,
		livenessPb.LivenessStatus_LIVENESS_FACE_POORLY_DETECTED,
		livenessPb.LivenessStatus_LIVENESS_FACE_TOO_FAR,
		livenessPb.LivenessStatus_LIVENESS_FACE_TOO_CLOSE,
		livenessPb.LivenessStatus_LIVENESS_FACE_TOO_DARK,
		livenessPb.LivenessStatus_LIVENESS_FACE_TOO_BRIGHT,
		livenessPb.LivenessStatus_LIVENESS_NO_FACE_DETECTED,
		livenessPb.LivenessStatus_LIVENESS_MANUALLY_FAILED,
		livenessPb.LivenessStatus_LIVENESS_QUEUE_MAX_RETRIES:
		return woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_FAILED_RETRY
	default:
		return woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_IN_PROGRESS
	}
}

func (p *PerformLivenessStep) wealthLivenessStatusFromScore(ctx context.Context, livenessStatus livenessPb.LivenessStatus, otpScore, lvScore float32) woPb.LivenessData_LivenessAttempt_LivenessStatus {
	// We are checking for OTP score here because it is mandated by SEBI to use random question based liveness check
	// bank onboarding liveness is not required to do that, they use blink liveness model and pass based on that
	logger.Info(ctx, "evaluating wealth liveness status from score",
		zap.Float32("otp_score", otpScore), zap.Float32("liveness_score", lvScore), zap.String("liveness_status", livenessStatus.String()))
	if otpScore >= p.conf.LivenessConfig.OTPThreshold && lvScore >= p.conf.LivenessConfig.LivenessThreshold {
		return woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_PASSED
	}
	return woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_FAILED_RETRY
}

func addLvAttemptInfoIfValid(ctx context.Context, actorId string, lvAttempt *woPb.LivenessData_LivenessAttempt, lvClient livenessPb.LivenessClient, conf *config.Config) bool {
	res, err := lvClient.GetLivenessAttempts(ctx, &livenessPb.GetLivenessAttemptsRequest{
		ActorId: actorId,
		Limit:   maxLvAttemptLimit,
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		logger.Error(ctx, "error while fetching liveness attempts, not updating liveness attempt", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(te))
		return false
	}
	var bankOnbLvAttempt *livenessPb.LivenessAttempt
	for _, lva := range res.GetLivenessAttempts() {
		// we can use passed liveness attempt with onboarding and auth factor update as liveness flow
		if (lva.GetLivenessFlow() == livenessPb.LivenessFlow_ONBOARDING || lva.GetLivenessFlow() == livenessPb.LivenessFlow_AUTH_FACTOR_UPDATE) &&
			(lva.GetStatus() == livenessPb.LivenessStatus_LIVENESS_PASSED || lva.GetStatus() == livenessPb.LivenessStatus_LIVENESS_MANUALLY_PASSED) {
			bankOnbLvAttempt = lva
			break
		}
	}
	// if not attempt is found for successful liveness then return
	if bankOnbLvAttempt == nil {
		logger.Info(ctx, "could not find a successful onb liveness attempt for onboarding", zap.String(logger.ACTOR_ID_V2, actorId))
		return false
	}
	logger.Info(ctx, "successful onb liveness attempt found", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("onb_lv_attempt", bankOnbLvAttempt.GetAttemptId()))
	// check the successful liveness attempt score as per the wealth config
	if bankOnbLvAttempt.LivenessScore < conf.LivenessConfig.LivenessThreshold || bankOnbLvAttempt.OtpScore < conf.LivenessConfig.OTPThreshold {
		logger.Info(ctx, "liveness score below threshold", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("onb_lv_attempt", bankOnbLvAttempt.GetAttemptId()))
		return false
	}
	// score is greater than threshold, mark the existing attempt as success
	lvAttempt.AttemptId = bankOnbLvAttempt.GetRequestId()
	lvAttempt.Status = woPb.LivenessData_LivenessAttempt_LIVENESS_STATUS_PASSED
	lvAttempt.VideoLocation = bankOnbLvAttempt.GetVideoLocation()
	lvAttempt.Otp = bankOnbLvAttempt.GetOtp()
	lvAttempt.OtpScore = bankOnbLvAttempt.GetOtpScore()
	lvAttempt.LivenessScore = bankOnbLvAttempt.GetLivenessScore()
	return true
}

func (p *PerformLivenessStep) pushToManualReviewQueue(ctx context.Context, od *woPb.OnboardingDetails, lvAttempt *woPb.LivenessData_LivenessAttempt) error {
	var kycImage *commontypes.Image
	if od.GetMetadata().GetPersonalDetails().GetPhoto().GetPhoto() != nil && len(od.GetMetadata().GetPersonalDetails().GetPhoto().GetPhoto()) > 0 && od.GetMetadata().GetPersonalDetails().GetPhoto().GetPhoto()[0] != nil {
		kycImage = od.GetMetadata().GetPersonalDetails().GetPhoto().GetPhoto()[0]
	} else {
		return errors.New("Kyc image not found")
	}
	id, err := p.manualReview.AddToReview(ctx, &woPb.ReviewPayload{
		Payload: &woPb.ReviewPayload_LivenessReview_{
			LivenessReview: &woPb.ReviewPayload_LivenessReview{
				ActorId:       od.GetActorId(),
				VideoLocation: lvAttempt.GetVideoLocation(),
				AttemptId:     lvAttempt.GetAttemptId(),
				Otp:           lvAttempt.GetOtp(),
				OtpScore:      lvAttempt.GetOtpScore(),
				LivenessScore: lvAttempt.GetLivenessScore(),
				KycImage:      kycImage,
			},
		},
	}, woPb.ItemType_ITEM_TYPE_LIVENESS)
	if err != nil {
		return err
	}
	// create review attempts map if not exists
	if od.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping() == nil {
		if od.GetMetadata().GetManualReviewAttempts() == nil {
			od.GetMetadata().ManualReviewAttempts = &woPb.ManualReviewAttempts{}
		}
		od.GetMetadata().GetManualReviewAttempts().ReviewAttemptMapping = make(map[string]*woPb.ManualReviewAttempts_ReviewAttemptList)
	}
	// create review attempt list for liveness item if not exists
	_, ok := od.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping()[woPb.ItemType_ITEM_TYPE_LIVENESS.String()]
	if !ok {
		od.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping()[woPb.ItemType_ITEM_TYPE_LIVENESS.String()] = &woPb.ManualReviewAttempts_ReviewAttemptList{
			ReviewAttempts: make([]*woPb.ManualReviewAttempts_ReviewAttempt, 0),
		}
	}
	// append review item with id and status to liveness item map in metadata
	reviewAttemptList := od.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping()[woPb.ItemType_ITEM_TYPE_LIVENESS.String()]
	reviewAttemptList.ReviewAttempts = append(reviewAttemptList.ReviewAttempts, &woPb.ManualReviewAttempts_ReviewAttempt{
		Id:     id,
		Status: woPb.ReviewStatus_REVIEW_STATUS_PENDING,
	})
	return nil
}

func getErrorReasonFromStatus(status woPb.LivenessRejectReason) deeplinkPb.ErrorLivenessFailure {
	// TODO(ismail): map reject reasons from CX end as well for error deep link
	switch status {
	case woPb.LivenessRejectReason_LIVENESS_REJECT_REASON_FACE_NOT_DETECTED:
		return deeplinkPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_FACE_NOT_DETECTED
	case woPb.LivenessRejectReason_LIVENESS_REJECT_REASON_MULTIPLE_FACES:
		return deeplinkPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_MULTIPLE_FACES_DETECTED
	case woPb.LivenessRejectReason_LIVENESS_REJECT_REASON_FACE_POORLY_DETECTED:
		return deeplinkPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_FACE_POORLY_DETECTED
	case woPb.LivenessRejectReason_LIVENESS_REJECT_REASON_FACE_TOO_FAR:
		return deeplinkPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_FACE_TOO_FAR
	case woPb.LivenessRejectReason_LIVENESS_REJECT_REASON_FACE_TOO_CLOSE:
		return deeplinkPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_FACE_TOO_CLOSE
	case woPb.LivenessRejectReason_LIVENESS_REJECT_REASON_FACE_TOO_DARK:
		return deeplinkPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_FACE_TOO_DARK
	case woPb.LivenessRejectReason_LIVENESS_REJECT_REASON_FACE_TOO_BRIGHT:
		return deeplinkPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_FACE_TOO_BRIGHT
	case woPb.LivenessRejectReason_LIVENESS_REJECT_REASON_NO_FACE_DETECTED:
		return deeplinkPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_NO_FACE_DETECTED
	}
	return deeplinkPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_UNSPECIFIED
}

func getLivenessRejectReason(status livenessPb.LivenessStatus) woPb.LivenessRejectReason {
	switch status {
	case livenessPb.LivenessStatus_LIVENESS_FACE_NOT_DETECTED:
		return woPb.LivenessRejectReason_LIVENESS_REJECT_REASON_FACE_NOT_DETECTED
	case livenessPb.LivenessStatus_LIVENESS_MULTIPLE_FACES_DETECTED:
		return woPb.LivenessRejectReason_LIVENESS_REJECT_REASON_MULTIPLE_FACES
	case livenessPb.LivenessStatus_LIVENESS_FACE_POORLY_DETECTED:
		return woPb.LivenessRejectReason_LIVENESS_REJECT_REASON_FACE_POORLY_DETECTED
	case livenessPb.LivenessStatus_LIVENESS_FACE_TOO_FAR:
		return woPb.LivenessRejectReason_LIVENESS_REJECT_REASON_FACE_TOO_FAR
	case livenessPb.LivenessStatus_LIVENESS_FACE_TOO_CLOSE:
		return woPb.LivenessRejectReason_LIVENESS_REJECT_REASON_FACE_TOO_CLOSE
	case livenessPb.LivenessStatus_LIVENESS_FACE_TOO_DARK:
		return woPb.LivenessRejectReason_LIVENESS_REJECT_REASON_FACE_TOO_DARK
	case livenessPb.LivenessStatus_LIVENESS_FACE_TOO_BRIGHT:
		return woPb.LivenessRejectReason_LIVENESS_REJECT_REASON_FACE_TOO_BRIGHT
	case livenessPb.LivenessStatus_LIVENESS_NO_FACE_DETECTED:
		return woPb.LivenessRejectReason_LIVENESS_REJECT_REASON_FACE_NOT_DETECTED
	}
	return woPb.LivenessRejectReason_LIVENESS_REJECT_REASON_UNSPECIFIED
}
