package steps

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"net/url"
	"time"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/epifierrors"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/esign"
	"github.com/epifi/gamma/wealthonboarding/helper"
	"github.com/epifi/gamma/wealthonboarding/release"
)

type AdvisoryAgreementStep struct {
	eSign            esign.ESign
	conf             *config.Config
	releaseEvaluator release.IEvaluator
	s3Client         s3.S3Client
}

func NewAdvisoryAgreementStep(
	eSign esign.ESign,
	conf *config.Config,
	releaseEvaluator release.IEvaluator,
	s3Client s3.S3Client,
) *AdvisoryAgreementStep {
	return &AdvisoryAgreementStep{
		eSign:            eSign,
		conf:             conf,
		releaseEvaluator: releaseEvaluator,
		s3Client:         s3Client,
	}
}

func (c *AdvisoryAgreementStep) Perform(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	// if user wants to skip the step now, return step status as skipped
	if details.GetMetadata().GetCustomerProvidedData().GetSkipSigningAdvisoryAgreement() == commontypes.BooleanEnum_TRUE {
		// change the skipStep to false so that when user comes next time, agreement deeplink will be served again
		details.GetMetadata().GetCustomerProvidedData().SkipSigningAdvisoryAgreement = commontypes.BooleanEnum_FALSE
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_SKIPPED, woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, getNextStepForAdvisoryAgreement(details.GetMetadata().GetIsFreshKra())), nil
	}
	// if the agreement is already collected, return with completed status
	if details.GetMetadata().GetPersonalDetails().GetAdvisoryAgreementDetails().GetSignedAgreementS3Path() != "" {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, getNextStepForAdvisoryAgreement(details.GetMetadata().GetIsFreshKra())), nil
	}
	// refresh the state of user's esign and get corresponding step execution response
	return c.processAdvisoryAgreementSign(ctx, details)
}

// returns next step based on fresh kra or not
func getNextStepForAdvisoryAgreement(isFreshKra bool) woPb.OnboardingStep {
	if isFreshKra {
		return woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET
	} else {
		return woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP
	}
}

// nolint:funlen
func (c *AdvisoryAgreementStep) processAdvisoryAgreementSign(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	pd := details.GetMetadata().GetPersonalDetails()
	if pd.GetAdvisoryAgreementDetails().GetEsignTxnDetails() == nil {
		if pd.GetAdvisoryAgreementDetails() == nil {
			pd.AdvisoryAgreementDetails = &woPb.AdvisoryAgreementDetails{}
		}
		pd.GetAdvisoryAgreementDetails().EsignTxnDetails = &woPb.EsignInfo{}
	}
	esignDetails := pd.GetAdvisoryAgreementDetails().GetEsignTxnDetails()

	// esign request not raised with vendor yet
	// initiate document sign request and get esign id
	// TODO(sharath): change this if condition to enum value
	isValidEsign, err := c.eSign.IsValidEsign(ctx, esignDetails.GetEsignId())
	if err != nil {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, getNextStepForAdvisoryAgreement(details.GetMetadata().GetIsFreshKra())), errors.Wrap(err, "error in checking valid presigned url")
	}
	if !isValidEsign {
		// TODO(sharath): move pdf expiry time out of cvl kra config
		preSignedAgreementUrl, pErr := c.s3Client.GetPreSignedUrl(ctx, c.conf.AdvisoryAgreement.UnsignedDocS3Path, time.Second*time.Duration(c.conf.CvlKra.PdfExpiryTime))
		if pErr != nil {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, getNextStepForAdvisoryAgreement(details.GetMetadata().GetIsFreshKra())), errors.Wrap(pErr, "error in getting presigned url")
		}

		esignId, initDocSignErr := c.eSign.InitiateDocSign(ctx, &woPb.InitiateDocSignRequest{
			Params: &woPb.InitiateDocSignRequest_AadhaarESignParams{
				AadhaarESignParams: &woPb.AadhaarESignParams{Name: helper.GetNameForDocket(details)},
			},
			DocumentUrl: preSignedAgreementUrl,
			TemplateKey: c.conf.Manch.AdvisoryAgreementTemplateKey,
		})
		if initDocSignErr != nil {
			if errors.Is(initDocSignErr, epifierrors.ErrDowntimeExpected) {
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(initDocSignErr), errors.Wrap(initDocSignErr, "failed to InitiateDocSign")
			}
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, getNextStepForAdvisoryAgreement(details.GetMetadata().GetIsFreshKra())), errors.Wrap(initDocSignErr, "error in initiate doc sign")
		}
		esignDetails.EsignId = esignId
	}

	signUrl, suErr := c.eSign.GetSignUrl(ctx, esignDetails.GetEsignId())
	if suErr != nil {
		if errors.Is(suErr, epifierrors.ErrDowntimeExpected) {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(suErr), errors.Wrap(suErr, "failed to get sign url")
		}
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, getNextStepForAdvisoryAgreement(details.GetMetadata().GetIsFreshKra())), errors.Wrap(suErr, "error in getting sign url")
	}

	esignDetails.EsignUrl = signUrl

	return c.updateEsignStatusAndGetStepExecutionResponse(ctx, details)
}

// nolint:funlen
func (c *AdvisoryAgreementStep) updateEsignStatusAndGetStepExecutionResponse(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	esignDetails := details.GetMetadata().GetPersonalDetails().GetAdvisoryAgreementDetails().GetEsignTxnDetails()
	eSignRes, err := c.eSign.GetLatestESignDetails(ctx, esignDetails.GetEsignId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrDowntimeExpected) {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(err), errors.Wrap(err, "failed to get txn status")
		}
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, getNextStepForAdvisoryAgreement(details.GetMetadata().GetIsFreshKra())), errors.Wrap(err, "error in getting sign txn state")
	}
	if esignDetails.GetStatus() != eSignRes.GetTxnState() {
		esignDetails.Status = eSignRes.GetTxnState()
	}

	switch esignDetails.GetStatus() {
	case woPb.TxnState_TXN_STATE_PENDING:
		res := GetStepExecutionResponse(details,
			woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS,
			woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT,
			getNextStepForAdvisoryAgreement(details.GetMetadata().GetIsFreshKra())).
			WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CUSTOMER_SIGN_PENDING)
		advisoryDeeplink, advErr := c.getAdvisoryAgreementDeeplink(ctx, esignDetails.GetEsignUrl(), details.GetActorId())
		if advErr != nil {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, getNextStepForAdvisoryAgreement(details.GetMetadata().GetIsFreshKra())), errors.Wrap(advErr, "error in getting advisory agreement deeplink")
		}

		var isRiskProfilingSupported bool
		isRiskProfilingSupported, updateAppDeeplink, err := c.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_RISK_PROFILING).WithActorId(details.GetActorId()))
		if err != nil {
			return nil, errors.Wrap(err, "error checking if risk profile is enabled")
		}
		if updateAppDeeplink != nil {
			res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, getNextStepForAdvisoryAgreement(details.GetMetadata().GetIsFreshKra()))
			res.Deeplink = updateAppDeeplink
			return res, nil
		}

		res.Deeplink = getMissingDataDeeplink(details, nil, nil, c.conf, false, advisoryDeeplink, isRiskProfilingSupported, nil)
		return res, nil
	case woPb.TxnState_TXN_STATE_SIGNED:
		res := GetStepExecutionResponse(details,
			woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS,
			woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT,
			getNextStepForAdvisoryAgreement(details.GetMetadata().GetIsFreshKra())).
			WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CUSTOMER_SIGNED)
		res.Deeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_LANDING_SCREEN,
		}
		return res, nil
	case woPb.TxnState_TXN_STATE_COMPLETED:
		if details.GetMetadata().GetPersonalDetails().GetAdvisoryAgreementDetails().GetSignedAgreementS3Path() == "" {
			// download and get signed agreement s3 path
			signedDocS3Path, docPathErr := c.eSign.GetSignedDocS3Path(ctx, esignDetails.GetEsignId())
			if docPathErr != nil {
				if errors.Is(docPathErr, epifierrors.ErrDowntimeExpected) {
					return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(docPathErr), errors.Wrap(docPathErr, "failed to get signed document")
				}
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, getNextStepForAdvisoryAgreement(details.GetMetadata().GetIsFreshKra())), errors.Wrap(docPathErr, "error in getting signed document")
			}
			details.GetMetadata().GetPersonalDetails().GetAdvisoryAgreementDetails().SignedAgreementS3Path = signedDocS3Path
			esignDetails.CompletedAt = eSignRes.GetSignedAt()
		}
		return GetStepExecutionResponse(details,
			woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
			woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT,
			getNextStepForAdvisoryAgreement(details.GetMetadata().GetIsFreshKra())), nil
	default:
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, getNextStepForAdvisoryAgreement(details.GetMetadata().GetIsFreshKra())), fmt.Errorf("unhandled sign status: %v", esignDetails.GetStatus())
	}
}

// nolint:funlen
func (c *AdvisoryAgreementStep) getAdvisoryAgreementDeeplink(ctx context.Context, esignUrl string, actorId string) (*deeplinkPb.Deeplink, error) {
	var skipCta *commontypes.Text
	// TODO(sharath): move pdf expiry time out of cvl kra config
	preSignedAdvisoryAgreeementURL, pErr := c.s3Client.GetPreSignedUrl(ctx, c.conf.AdvisoryAgreement.UnsignedDocS3Path, time.Second*time.Duration(c.conf.CvlKra.PdfExpiryTime))
	if pErr != nil {
		return nil, errors.Wrap(pErr, "error in getting presigned url")
	}
	encodedAgreementUrl := "https://docs.google.com/gview?embedded=true&" + url.Values{"url": []string{preSignedAdvisoryAgreeementURL}}.Encode()
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_IA_AGREEMENT_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingIaAgreementScreenOptions{
			WealthOnboardingIaAgreementScreenOptions: &deeplinkPb.WealthOnboardingIAAgreementScreenOptions{
				Title: &commontypes.Text{
					FontColor: "#333333",
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "Investment Advisory Agreement",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_L,
					},
				},
				Description: &commontypes.Text{
					FontColor: "#646464",
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "This is required by Epifi Wealth (SEBI registered Investment Advisor) to onboard you as an investment advisory client",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_BODY_3,
					},
				},
				WaitInfo: &commontypes.Text{
					FontColor: "#5D7D4C",
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "Takes less than 2 mins",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS,
					},
					BgColor: "#D9F2CC",
				},
				WaitIcon: &commontypes.Image{
					ImageType: commontypes.ImageType_PNG,
					ImageUrl:  "https://epifi-icons.pointz.in/Wealth/high-voltage_26a1+1.png",
					Height:    16,
					Width:     16,
				},
				Benefits: []*deeplinkPb.WealthInfoBox{
					{
						Info: &commontypes.Text{
							FontColor: "#37383A",
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "Fund suggestions based on your risk profile",
							},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_HEADLINE_M,
							},
						},
						InfoImage: &commontypes.Image{
							ImageType: commontypes.ImageType_PNG,
							ImageUrl:  "https://epifi-icons.pointz.in/Wealth/Pad.png",
							Height:    32,
							Width:     32,
						},
					},
					{
						Info: &commontypes.Text{
							FontColor: "#37383A",
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "Personalised collections",
							},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_HEADLINE_M,
							},
						},
						InfoImage: &commontypes.Image{
							ImageType: commontypes.ImageType_PNG,
							ImageUrl:  "https://epifi-icons.pointz.in/Wealth/Basket.png",
							Height:    32,
							Width:     32,
						},
					},
					{
						Info: &commontypes.Text{
							FontColor: "#37383A",
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "Insights on your portfolio",
							},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_HEADLINE_M,
							},
						},
						InfoImage: &commontypes.Image{
							ImageType: commontypes.ImageType_PNG,
							ImageUrl:  "https://epifi-icons.pointz.in/Wealth/Pie-Chart.png",
							Height:    32,
							Width:     32,
						},
					},
				},
				BottomText: &commontypes.Text{
					FontColor: "#9DA1A4",
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "On proceeding, you’ll be redirected to Aadhaar e-sign to confirm the ^^" + encodedAgreementUrl + "^^IA agreement^^",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_BODY_XS,
					},
				},
				SignCta: &deeplinkPb.Cta{
					Type: deeplinkPb.Cta_CUSTOM,
					Text: "Agree & Proceed to e-sign",
					// weblink is handled in ios but not on android
					Weblink: esignUrl,
					// deeplink is also populated since weblink is not handled on android
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_DOCUMENT_AADHAAR_ESIGN_SCREEN,
						ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingDocumentAadhaarEsignScreenOptions{
							WealthOnboardingDocumentAadhaarEsignScreenOptions: &deeplinkPb.WealthOnboardingDocumentAadhaarEsignScreenOptions{
								EsignUrl:    esignUrl,
								CallbackUrl: c.conf.KraDocketSignConfig.CallBackUrl,
								// flow is populated at top-level based on client's entry point
							},
						},
					},
				},
				CallbackUrl: c.conf.KraDocketSignConfig.CallBackUrl,
				SkipCta:     skipCta,
				ComplianceElement: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: "https://epifi-icons.pointz.in/Wealth/epifi_wealth_investment_logo.png",
							},
							ImageType: commontypes.ImageType_PNG,
							Properties: &commontypes.VisualElementProperties{
								Width:  121,
								Height: 30,
							},
						},
					},
				},
			},
		},
	}, nil
}
