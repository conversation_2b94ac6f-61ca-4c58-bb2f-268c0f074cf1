package steps

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	types "github.com/epifi/gamma/api/typesv2"
	wealthVgPb "github.com/epifi/gamma/api/vendors/wealth"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/ckyc_helper"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/dao"
	woErr "github.com/epifi/gamma/wealthonboarding/errors"
	"github.com/epifi/gamma/wealthonboarding/esign"
	"github.com/epifi/gamma/wealthonboarding/helper"
	"github.com/epifi/gamma/wealthonboarding/kra_data"
	"github.com/epifi/gamma/wealthonboarding/release"

	"github.com/pkg/errors"
)

var (
	XAmzDateLayout = "20060102T150405Z"
)

type CreateAndSignKraDocketStep struct {
	conf                     *config.Config
	onboardingStepDetailsDao dao.OnboardingStepDetailsDao
	currentStep              woPb.OnboardingStep
	nextStepOnSuccess        woPb.OnboardingStep
	kraData                  kra_data.KraData
	ckycHelper               ckyc_helper.ICkycHelper
	eSign                    esign.ESign
	releaseEvaluator         release.IEvaluator
	s3Client                 s3.S3Client
}

func NewCreateAndSignKraDocketStep(
	conf *config.Config,
	onboardingStepDetailsDao dao.OnboardingStepDetailsDao,
	kraData kra_data.KraData,
	ckycHelper ckyc_helper.ICkycHelper,
	eSign esign.ESign,
	releaseEvaluator release.IEvaluator,
	s3Client s3.S3Client) *CreateAndSignKraDocketStep {
	return &CreateAndSignKraDocketStep{
		conf:                     conf,
		onboardingStepDetailsDao: onboardingStepDetailsDao,
		currentStep:              woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET,
		nextStepOnSuccess:        woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
		kraData:                  kraData,
		ckycHelper:               ckycHelper,
		eSign:                    eSign,
		releaseEvaluator:         releaseEvaluator,
		s3Client:                 s3Client,
	}
}

// nolint:funlen,dupl
func (c *CreateAndSignKraDocketStep) Perform(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	isAadhaarCollectedFromDigilocker := helper.IsAadhaarCollectedFromDigilocker(details)
	if !c.conf.DisableCKYC {
		stepSubStatus, ckycErr := c.ckycHelper.FetchPopulateValidateCkycDownloadData(ctx, details)
		if ckycErr != nil {
			switch {
			case errors.Is(ckycErr, woErr.ErrOCRLowConfidence):
			case errors.Is(ckycErr, epifierrors.ErrRecordNotFound):
				logger.Info(ctx, "ckyc record not found")
				if !isAadhaarCollectedFromDigilocker {
					return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, c.currentStep, c.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CKYC_RECORD_NOT_FOUND), nil
				}
			case errors.Is(ckycErr, epifierrors.ErrDowntimeExpected):
				logger.Info(ctx, "downtime expected for vendor")
				res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, c.currentStep, c.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(ckycErr)
				return res, errors.Wrap(ckycErr, "expected downtime for vendor")
			default:
				logger.Error(ctx, "failed to fetchPopulateValidateCkycDownloadData")
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, c.currentStep, c.nextStepOnSuccess), errors.Wrap(ckycErr, "failed to fetch and populate ckyc download data")
			}
		}
		if stepSubStatus != woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSPECIFIED && !isAadhaarCollectedFromDigilocker {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, c.currentStep, c.nextStepOnSuccess).WithStepSubStatus(stepSubStatus), nil
		}
	}

	if details.GetMetadata().GetKraDocketInfo() == nil {
		details.GetMetadata().KraDocketInfo = &woPb.DocketInfo{}
	}

	if isValidDocketUrl, uErr := isDocketUrlValid(details.GetMetadata().GetKraDocketInfo().GetUnsignedDocUrl()); uErr != nil {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, c.currentStep, c.nextStepOnSuccess), errors.Wrap(uErr, "failed to determine pre-signed url expiry")
	} else if !isValidDocketUrl {
		res, pdfErr := c.kraData.GenerateKraFormPdf(ctx, details)
		if pdfErr != nil {
			if errors.Is(pdfErr, woErr.ErrImageConversionFailed) {
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED, c.currentStep, c.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_IMAGE_CONVERSION_FAILED), errors.Wrap(pdfErr, "failed to GenerateKraFormPdf")
			}
			if errors.Is(pdfErr, woErr.ErrPdfMergeFailed) {
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED, c.currentStep, c.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_PDF_MERGING_FAILED), errors.Wrap(pdfErr, "failed to GenerateKraFormPdf")
			}
			if errors.Is(pdfErr, woErr.ErrPoaIdMissing) {
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE, c.currentStep, c.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_POA_ID_MISSING), nil
			}
			if errors.Is(pdfErr, woErr.ErrIncompleteKraFormDetails) {
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED, c.currentStep, c.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_KRA_FORM_DETAILS_MISSING), pdfErr
			}
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, c.currentStep, c.nextStepOnSuccess), errors.Wrap(pdfErr, "failed to GenerateKraFormPdf")
		}
		details.GetMetadata().GetKraDocketInfo().UnsignedDocUrl = res
	}
	var latestEsignInfo *woPb.EsignInfo
	noOfEsignInfos := len(details.GetMetadata().GetKraDocketInfo().GetEsignInfos())
	if noOfEsignInfos > 0 {
		latestEsignInfo = details.GetMetadata().GetKraDocketInfo().GetEsignInfos()[noOfEsignInfos-1]
	}
	if latestEsignInfo == nil {
		latestEsignInfo = &woPb.EsignInfo{}
		details.GetMetadata().GetKraDocketInfo().EsignInfos = append(details.GetMetadata().GetKraDocketInfo().EsignInfos, latestEsignInfo)
	}

	isValidEsign, err := c.eSign.IsValidEsign(ctx, latestEsignInfo.GetEsignId())
	if err != nil {
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, c.currentStep, c.nextStepOnSuccess), errors.Wrap(err, "failed to validate esign")
	}
	if !isValidEsign {
		esignId, initDocSignErr := c.eSign.InitiateDocSign(ctx, &woPb.InitiateDocSignRequest{
			Params: &woPb.InitiateDocSignRequest_AadhaarESignParams{
				AadhaarESignParams: &woPb.AadhaarESignParams{Name: helper.GetNameForDocket(details)},
			},
			DocumentUrl: details.GetMetadata().GetKraDocketInfo().GetUnsignedDocUrl(),
			TemplateKey: c.conf.Manch.DocketTemplateKey,
		})
		if initDocSignErr != nil {
			if errors.Is(initDocSignErr, epifierrors.ErrDowntimeExpected) {
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, c.currentStep, c.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(initDocSignErr), errors.Wrap(initDocSignErr, "failed to InitiateDocSign")
			}
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, c.currentStep, c.nextStepOnSuccess), errors.Wrap(initDocSignErr, "failed to InitiateDocSign")
		}
		latestEsignInfo.EsignId = esignId
	}

	signUrl, err := c.eSign.GetSignUrl(ctx, latestEsignInfo.GetEsignId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrDowntimeExpected) {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(err), errors.Wrap(err, "failed to get sign url")
		}
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, getNextStepForAdvisoryAgreement(details.GetMetadata().GetIsFreshKra())), errors.Wrap(err, "error in getting sign url")
	}
	latestEsignInfo.EsignUrl = signUrl
	esignDetailsRes, signStatusErr := c.eSign.GetLatestESignDetails(ctx, latestEsignInfo.GetEsignId())
	if signStatusErr != nil {
		if errors.Is(signStatusErr, epifierrors.ErrDowntimeExpected) {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, c.currentStep, c.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME).WithDowntimeDeeplink(signStatusErr), errors.Wrap(signStatusErr, "failed to GetSignTxnState")
		}
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, c.currentStep, c.nextStepOnSuccess), errors.Wrap(signStatusErr, "failed to GetSignTxnState")
	}
	if latestEsignInfo.GetStatus() != esignDetailsRes.GetTxnState() {
		latestEsignInfo.Status = esignDetailsRes.GetTxnState()
	}

	switch latestEsignInfo.GetStatus() {
	case woPb.TxnState_TXN_STATE_PENDING:
		var isRiskProfilingSupported bool
		isRiskProfilingSupported, updateAppDeeplink, err := c.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_RISK_PROFILING).WithActorId(details.GetActorId()))
		if err != nil {
			return nil, errors.Wrap(err, "error checking if risk profile is enabled")
		}
		if updateAppDeeplink != nil {
			res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, c.currentStep, c.nextStepOnSuccess)
			res.Deeplink = updateAppDeeplink
			return res, nil
		}
		res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, c.currentStep, c.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CUSTOMER_SIGN_PENDING)
		if details.GetAgentProvidedData().GetAddress() != nil {
			res.CurrentStepDetails.SubStatus = woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CUSTOMER_SIGN_PENDING_HOLD_CASES
		}
		res.Deeplink = c.getDeeplink(details, latestEsignInfo.GetEsignId(), latestEsignInfo.GetEsignUrl(), c.conf, isRiskProfilingSupported)
		return res, nil
	case woPb.TxnState_TXN_STATE_SIGNED:
		res := GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, c.currentStep, c.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CUSTOMER_SIGNED)
		if details.GetAgentProvidedData().GetAddress() != nil {
			res.CurrentStepDetails.SubStatus = woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CUSTOMER_SIGNED_HOLD_CASES
		}
		res.Deeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_LANDING_SCREEN,
		}
		return res, nil
	case woPb.TxnState_TXN_STATE_COMPLETED:
		// get signed docket as doc proof
		docProofS3Path, docPathErr := c.eSign.GetSignedDocS3Path(ctx, latestEsignInfo.GetEsignId())
		if docPathErr != nil {
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, c.currentStep, c.nextStepOnSuccess), errors.Wrap(docPathErr, "failed to get signed docket as doc proof")
		}
		details.GetMetadata().GetKraDocketInfo().DocAsProof = &types.DocumentProof{
			ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_OTHERS,
			S3Paths:   []string{docProofS3Path},
		}
		latestEsignInfo.CompletedAt = esignDetailsRes.GetSignedAt()
		if details.GetAgentProvidedData().GetKycStatus() != wealthVgPb.KraStatus_KRA_STATUS_TYPE_UNSPECIFIED {
			if ukdErr := c.uploadKraDocket(ctx, details); ukdErr != nil {
				return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, c.currentStep, c.nextStepOnSuccess), errors.Wrap(ukdErr, "failed to upload docket to kra")
			}
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, c.currentStep, c.nextStepOnSuccess).WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_COMPLETED_HOLD_CASES), nil
		}
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, c.currentStep, c.nextStepOnSuccess), nil
	default:
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, c.currentStep, c.nextStepOnSuccess), errors.Wrap(signStatusErr, fmt.Sprintf("esign status %s not handled", latestEsignInfo.GetStatus()))
	}
}

func (c *CreateAndSignKraDocketStep) getDeeplink(details *woPb.OnboardingDetails, esignId string, esignUrl string,
	conf *config.Config, isRiskProfilingSupported bool) *deeplinkPb.Deeplink {
	return getMissingDataDeeplink(details, nil, &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_DOCUMENT_AADHAAR_ESIGN_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingDocumentAadhaarEsignScreenOptions{
			WealthOnboardingDocumentAadhaarEsignScreenOptions: &deeplinkPb.WealthOnboardingDocumentAadhaarEsignScreenOptions{
				SignId:      esignId,
				EsignUrl:    esignUrl,
				CallbackUrl: c.conf.KraDocketSignConfig.CallBackUrl,
				// flow is populated at top-level based on client's entry point
			},
		},
	}, conf, false, nil, isRiskProfilingSupported, nil)
}

// isDocketUrlValid returns whether the docket url is valid or not based on if pre-signed url is expired
func isDocketUrlValid(preSignedUrl string) (bool, error) {
	// if pre-signed url is empty, it means that no docket is created yet for the actor
	if preSignedUrl == "" {
		return false, nil
	}
	currentTime := time.Now().UTC()
	// parse the presigned URL
	u, err := url.Parse(preSignedUrl)
	if err != nil {
		return false, errors.Wrap(err, "error while parsing presigned url")
	}
	createdDate := u.Query().Get("X-Amz-Date")
	expiresInTime := u.Query().Get("X-Amz-Expires")
	tm, err := time.Parse(XAmzDateLayout, createdDate)
	if err != nil {
		return false, errors.Wrap(err, "error while parsing time")
	}
	expiryTime, err := strconv.ParseInt(expiresInTime, 10, 64)
	if err != nil {
		return false, errors.Wrap(err, "error while parsing expiry time of url")
	}
	tm = tm.Add(time.Second * time.Duration(expiryTime))
	return tm.After(currentTime), nil
}

func (u *CreateAndSignKraDocketStep) uploadKraDocket(ctx context.Context, details *woPb.OnboardingDetails) error {
	// get path of docket pdf
	if len(details.GetMetadata().GetKraDocketInfo().GetDocAsProof().GetS3Paths()) == 0 {
		return errors.New("no docket present in onboarding details")
	}
	docketPath := details.GetMetadata().GetKraDocketInfo().GetDocAsProof().GetS3Paths()[0]
	// fetch docket from s3
	docketData, err := u.s3Client.Read(ctx, docketPath)
	if err != nil {
		return fmt.Errorf("failed to fetch docket from s3 for actor-id: %s, err: %v, %w",
			details.GetActorId(), err.Error(), epifierrors.ErrTransient)
	}
	isKycApplicationOnHold := details.GetAgentProvidedData().GetKycStatus() == wealthVgPb.KraStatus_KRA_STATUS_HOLD
	// upload docket to sftp server of CVL
	uploadErr := u.kraData.UploadDocketToKra(ctx, details.GetMetadata().GetPanDetails().GetId(), timestampPb.Now(), isKycApplicationOnHold, docketData)
	if uploadErr != nil {
		return fmt.Errorf("failed to upload docket to sftp server for actor-id: %s, err: %v, %w",
			details.GetActorId(), uploadErr.Error(), epifierrors.ErrTransient)
	}
	return nil
}
