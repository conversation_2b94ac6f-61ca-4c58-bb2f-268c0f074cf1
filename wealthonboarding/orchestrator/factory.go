package orchestrator

import (
	"context"
	"fmt"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/orchestrator/steps"
)

type IHandlerFactory interface {
	GetStepHandler(ctx context.Context, step woPb.OnboardingStep) (steps.IStep, error)
}

type HandlerFactory struct {
	collectMissingDataStep              *steps.CollectMissingDataStep
	dataCollectionStep                  *steps.DataCollectionStep
	fetchKycInfoStep                    *steps.FetchKycInfoStep
	performFaceMatchStep                *steps.PerformFaceMatchStep
	takeConfirmationOnPepAndCitizenship *steps.TakeConfirmationOnPepAndCitizenship
	verifyPanStep                       *steps.VerifyPanStep
	createAndSignKraDocketStep          *steps.CreateAndSignKraDocketStep
	signAgreementStep                   *steps.SignAgreementStep
	performLivenessStep                 *steps.PerformLivenessStep
	uploadKraDocketStep                 *steps.UploadKraDocketStep
	downloadKraDocsStep                 *steps.DownloadKraDocsStep
	investmentDataCollectionStep        *steps.InvestmentDataCollectionStep
	consolidatedManualReviewStep        *steps.ConsolidatedManualReviewStep
	downloadFromDigilocker              *steps.DownloadFromDigilocker
	advisoryAgreementStep               *steps.AdvisoryAgreementStep
	takeUserConsentStep                 *steps.TakeUserConsent
	riskProfilingStep                   *steps.RiskProfilingStep
	nomineeModificationStep             *steps.NomineeModificationStep
	recordWealthMITCConsent             *steps.RecordWealthMITCConsent
}

func NewHandlerFactory(
	collectMissingDataStep *steps.CollectMissingDataStep,
	dataCollectionStep *steps.DataCollectionStep,
	fetchKycInfoStep *steps.FetchKycInfoStep,
	performFaceMatchStep *steps.PerformFaceMatchStep,
	takeConfirmationOnPepAndCitizenship *steps.TakeConfirmationOnPepAndCitizenship,
	verifyPanStep *steps.VerifyPanStep,
	createAndSignKraDocketStep *steps.CreateAndSignKraDocketStep,
	signAgreementStep *steps.SignAgreementStep,
	performLivenessStep *steps.PerformLivenessStep,
	uploadKraDocketStep *steps.UploadKraDocketStep,
	downloadKraDocsStep *steps.DownloadKraDocsStep,
	preInvestmentDataCollectionStep *steps.InvestmentDataCollectionStep,
	consolidatedManualReviewStep *steps.ConsolidatedManualReviewStep,
	downloadFromDigilocker *steps.DownloadFromDigilocker,
	advisoryAgreementStep *steps.AdvisoryAgreementStep,
	takeUserConsentStep *steps.TakeUserConsent,
	riskProfilingStep *steps.RiskProfilingStep,
	nomineeModificationStep *steps.NomineeModificationStep,
	recordWealthMITCConsent *steps.RecordWealthMITCConsent,
) *HandlerFactory {
	return &HandlerFactory{
		collectMissingDataStep:              collectMissingDataStep,
		dataCollectionStep:                  dataCollectionStep,
		fetchKycInfoStep:                    fetchKycInfoStep,
		performFaceMatchStep:                performFaceMatchStep,
		takeConfirmationOnPepAndCitizenship: takeConfirmationOnPepAndCitizenship,
		verifyPanStep:                       verifyPanStep,
		signAgreementStep:                   signAgreementStep,
		createAndSignKraDocketStep:          createAndSignKraDocketStep,
		performLivenessStep:                 performLivenessStep,
		uploadKraDocketStep:                 uploadKraDocketStep,
		downloadKraDocsStep:                 downloadKraDocsStep,
		investmentDataCollectionStep:        preInvestmentDataCollectionStep,
		consolidatedManualReviewStep:        consolidatedManualReviewStep,
		downloadFromDigilocker:              downloadFromDigilocker,
		advisoryAgreementStep:               advisoryAgreementStep,
		takeUserConsentStep:                 takeUserConsentStep,
		riskProfilingStep:                   riskProfilingStep,
		nomineeModificationStep:             nomineeModificationStep,
		recordWealthMITCConsent:             recordWealthMITCConsent,
	}
}

func (h *HandlerFactory) GetStepHandler(_ context.Context, step woPb.OnboardingStep) (steps.IStep, error) {
	switch step {
	case woPb.OnboardingStep_ONBOARDING_STEP_UNSPECIFIED:
		return nil, fmt.Errorf("no handler found for the state %d", step)
	case woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION:
		return h.dataCollectionStep, nil
	case woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION:
		return h.verifyPanStep, nil
	case woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA:
		return h.fetchKycInfoStep, nil
	case woPb.OnboardingStep_ONBOARDING_STEP_FACE_MATCH:
		return h.performFaceMatchStep, nil
	case woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO:
		return h.collectMissingDataStep, nil
	case woPb.OnboardingStep_ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW:
		return h.consolidatedManualReviewStep, nil
	case woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP:
		return h.takeConfirmationOnPepAndCitizenship, nil
	case woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET:
		return h.createAndSignKraDocketStep, nil
	case woPb.OnboardingStep_ONBOARDING_STEP_SIGN_AGREEMENT:
		return h.signAgreementStep, nil
	case woPb.OnboardingStep_ONBOARDING_STEP_LIVENESS:
		return h.performLivenessStep, nil
	case woPb.OnboardingStep_ONBOARDING_STEP_UPLOAD_DOCKET:
		return h.uploadKraDocketStep, nil
	case woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC:
		return h.downloadKraDocsStep, nil
	case woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION_FOR_INVESTMENT:
		return h.investmentDataCollectionStep, nil
	case woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_FROM_DIGILOCKER:
		return h.downloadFromDigilocker, nil
	case woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT:
		return h.advisoryAgreementStep, nil
	case woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT:
		return h.takeUserConsentStep, nil
	case woPb.OnboardingStep_ONBOARDING_STEP_INVESTMENT_RISK_PROFILING:
		return h.riskProfilingStep, nil
	case woPb.OnboardingStep_ONBOARDING_STEP_UPDATE_NOMINEE_DETAILS:
		return h.nomineeModificationStep, nil
	case woPb.OnboardingStep_ONBOARDING_STEP_USER_MITC_CONSENT:
		return h.recordWealthMITCConsent, nil
	default:
		return nil, fmt.Errorf("no handler found for the state %d", step)
	}
}
