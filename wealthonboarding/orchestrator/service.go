package orchestrator

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	oncev2 "github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/retry"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	commPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/frontend/deeplink"
	rmsPb "github.com/epifi/gamma/api/rms/manager"
	userPb "github.com/epifi/gamma/api/user"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/api/wealthonboarding/consumer"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/event"
	"github.com/epifi/gamma/wealthonboarding/helper"
	"github.com/epifi/gamma/wealthonboarding/metrics"
	"github.com/epifi/gamma/wealthonboarding/orchestrator/orchestrator_model"
	"github.com/epifi/gamma/wealthonboarding/orchestrator/steps"
	"github.com/epifi/gamma/wealthonboarding/release"
	"github.com/epifi/gamma/wealthonboarding/user"
)

//go:generate mockgen -source=service.go -destination=../test/mocks/orchestrator/mock_orchestrator.go -package=mock_orchestrator
type IService interface {
	Orchestrate(ctx context.Context, req *orchestrator_model.OrchestrateRequest) (*woPb.OnboardingDetails, *deeplink.Deeplink, error)
}

// aliases for wiring dependencies of similar types
type WealthOnboardingStepsRetrySqsCustomDelayPublisher queue.DelayPublisher
type UserCommsDelayPublisher queue.DelayPublisher

type Service struct {
	onboardingDetailsDao     dao.OnboardingDetailsDao
	onboardingStepDetailsDao dao.OnboardingStepDetailsDao
	handlerFactory           IHandlerFactory
	stepsRetryDelayPublisher WealthOnboardingStepsRetrySqsCustomDelayPublisher
	conf                     *config.Config
	userService              user.IService
	eventBroker              events.Broker
	idempotentTxnExecutor    storagev2.IdempotentTxnExecutor
	userCommsDelayPublisher  UserCommsDelayPublisher
	doOnce                   oncev2.DoOnce
	distributedLockManager   lock.ILockManager
	releaseEvaluator         release.IEvaluator
	rmsRuleManagerClient     rmsPb.RuleManagerClient
	userClient               userPb.UsersClient
}

func NewService(
	onboardingDetailsDao dao.OnboardingDetailsDao,
	onboardingStepDetailsDao dao.OnboardingStepDetailsDao,
	handlerFactory IHandlerFactory,
	stepsRetryDelayPublisher WealthOnboardingStepsRetrySqsCustomDelayPublisher,
	conf *config.Config,
	userService user.IService,
	eventBroker events.Broker,
	idempotentTxnExecutor storagev2.IdempotentTxnExecutor,
	userCommsDelayPublisher UserCommsDelayPublisher,
	doOnce oncev2.DoOnce,
	distributedLockManager lock.ILockManager,
	releaseEvaluator release.IEvaluator,
	rmsRuleManagerClient rmsPb.RuleManagerClient,
	userClient userPb.UsersClient,
) *Service {
	return &Service{
		onboardingDetailsDao:     onboardingDetailsDao,
		onboardingStepDetailsDao: onboardingStepDetailsDao,
		handlerFactory:           handlerFactory,
		stepsRetryDelayPublisher: stepsRetryDelayPublisher,
		conf:                     conf,
		userService:              userService,
		eventBroker:              eventBroker,
		idempotentTxnExecutor:    idempotentTxnExecutor,
		userCommsDelayPublisher:  userCommsDelayPublisher,
		doOnce:                   doOnce,
		distributedLockManager:   distributedLockManager,
		releaseEvaluator:         releaseEvaluator,
		rmsRuleManagerClient:     rmsRuleManagerClient,
		userClient:               userClient,
	}
}

const (
	OnboardingDetailsLockKeyPrefix = "wealth:onboarding_details"
)

// nolint:funlen
func (s *Service) Orchestrate(ctx context.Context, req *orchestrator_model.OrchestrateRequest) (*woPb.OnboardingDetails, *deeplink.Deeplink, error) {
	ctx = epificontext.CtxWithActorId(ctx, req.ActorId)

	// create entry in onboarding details if already not present
	od, odGetOrCreateErr := s.getOrCreateOnbDetails(ctx, req.ActorId, req.GetOnbType())
	if odGetOrCreateErr != nil {
		return nil, nil, errors.Wrap(odGetOrCreateErr, "error getting or creating onboarding details")
	}

	// acquiring distributed lock to prevent parallel calls to orchestrator
	orcLock, err := s.distributedLockManager.GetLock(ctx, OnboardingDetailsLockKeyPrefix+od.GetId(), 5*time.Minute)
	if err != nil {
		logger.Error(ctx, "error acquiring lock", zap.Error(err))
		dl := &deeplink.Deeplink{Screen: deeplink.Screen_WEALTH_ONBOARDING_LANDING_SCREEN}
		return od, dl, nil
	}
	defer func() {
		// cloning ctx before releasing lock, to avoid lock release failures in case of cancelled ctx
		if err = orcLock.Release(epificontext.CloneCtx(ctx)); err != nil {
			logger.Error(ctx, "error releasing lock", zap.Error(err), zap.String(logger.WEALTH_ONB_ID, od.GetId()), zap.String(logger.ACTOR_ID_V2, req.ActorId))
		}
	}()
	logger.Info(ctx, "acquired distributed lock in orchestrator", zap.String(logger.WEALTH_ONB_ID, od.GetId()), zap.String(logger.ACTOR_ID_V2, req.ActorId))

	// this check is added to return only mandatory step deeplink as per the entrypoint of flow
	// e.g. in mf invest summary page we only need to show MITC consent screen if not completed and not other onboarding steps
	// TODO: refactor this logic such that orchestrator handles steps to be completed as per entrypoint of flow
	if req.GetEntryPoint() == woPb.OnboardingStepEntrypoint_ONBOARDING_STEP_ENTRYPOINT_INVEST_SUMMARY {
		return s.checkIfWealthMITCConsentStepNeeded(ctx, od)
	}

	// Note: Please ensure onboarding status RPC impl. is in sync with any changes to logic which are part of this if condition
	if od.GetStatus() == woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED {
		// isAdvisoryAgreementNeeded is passed as true here to collect esign for already onboarded users
		return s.askOnboardedUsersForMissingData(ctx, od, true)
	}

	currStep := od.CurrentStep
	currStatus := od.GetStatus()

	for !IsTerminalStatus(currStatus) {
		if od.GetCurrentStep() != currStep {
			od.CurrentStep = currStep
			od.Status = woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS
			uErr := s.onboardingDetailsDao.Update(ctx, od, []woPb.OnboardingDetailsFieldMask{
				woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_CURRENT_STEP,
				woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_STATUS,
			})
			if uErr != nil {
				return nil, nil, errors.Wrap(uErr, "onboardingDetails update failed")
			}
		}
		// create entry in step details if already not present
		newOnbStepDetails := &woPb.OnboardingStepDetails{OnboardingDetailsId: od.GetId(), Step: currStep, Status: woPb.OnboardingStepStatus_STEP_STATUS_PENDING}
		stepData, isCreated, dbErr := s.onboardingStepDetailsDao.GetOrCreate(ctx, od.GetId(), currStep, newOnbStepDetails)
		if dbErr != nil {
			logger.Error(ctx, "error getting or creating onboarding step details", zap.Error(dbErr))
			return nil, nil, errors.Wrap(dbErr, "error getting or creating onboarding step details")
		}
		if isCreated {
			//nocustomlint:goroutine
			go s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), event.NewWealthonboardingStepEvent(time.Now(), epificontext.AppVersionFromContext(ctx), epificontext.AppPlatformFromContext(ctx), od, stepData, nil))
		}

		logger.Info(ctx, "performing step", zap.String(logger.ACTOR_ID_V2, od.GetActorId()), zap.String(logger.ONBOARDING_STAGE, currStep.String()))
		handler, hfErr := s.handlerFactory.GetStepHandler(ctx, currStep)
		if hfErr != nil {
			return nil, nil, errors.Wrap(hfErr, "unable to get step handler")
		}

		seRes, perfErr := handler.Perform(ctx, od)
		if perfErr != nil {
			logger.Error(ctx, "failed to execute step", zap.Error(perfErr), zap.String(logger.ACTOR_ID_V2, od.GetActorId()))
			// Note: This log is used a central place for easier debugging, monitoring and alerting, hence the error is not returned
		}
		if seRes == nil {
			logger.Error(ctx, "failed to execute step: nil step execution response", zap.String(logger.ACTOR_ID_V2, od.GetActorId()), zap.String(logger.ONBOARDING_STAGE, currStep.String()))
			return nil, nil, errors.New("failed to execute step: nil step execution response")
		}
		logger.Info(ctx, "performed step result", zap.String(logger.ACTOR_ID_V2, od.GetActorId()),
			zap.String(logger.ONBOARDING_STAGE, currStep.String()),
			zap.String("currentStepStatus", seRes.CurrentStepDetails.GetStatus().String()),
			zap.String(logger.REASON, seRes.CurrentStepDetails.GetSubStatus().String()),
			zap.String(logger.SCREEN, seRes.Deeplink.GetScreen().String()))
		metrics.RecordStepStatus(currStep, seRes.CurrentStepDetails.GetStatus(), seRes.CurrentStepDetails.GetSubStatus())
		uErr := s.updateDbAndSetupRetry(ctx, seRes, stepData, od, currStep, perfErr, true)
		if uErr != nil {
			return nil, nil, errors.Wrap(uErr, "error in updating db entry")
		}

		// set up a notification for later in case user gets stuck at this step + status + sub status
		clonedCtxWithIncreasedDeadline, cancelCtx := context.WithDeadline(epificontext.CloneCtx(ctx), time.Now().Add(5*time.Minute))
		//nocustomlint:goroutine
		go func() {
			defer cancelCtx()
			notificationErr := s.setupUserNotifications(clonedCtxWithIncreasedDeadline, od, seRes.CurrentStepDetails)
			if notificationErr != nil {
				logger.Error(clonedCtxWithIncreasedDeadline, "error setting up user notifications", zap.Error(notificationErr))
			}
		}()

		if len(seRes.StepsToInvalidate) != 0 {
			updatedOd, invErr := s.markStepsStale(ctx, req.GetActorId(), req.GetOnbType(), seRes.StepsToInvalidate)
			if invErr != nil {
				return nil, nil, errors.Wrap(invErr, "invalidating steps failed")
			}
			currStep = updatedOd.GetCurrentStep()
			currStatus = woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS
			continue
		}
		if seRes.Deeplink != nil {
			return od, seRes.Deeplink, nil
		}

		// TODO(Brijesh): Handle unspecified next onboarding step in a more elegant way
		if seRes.NextOnboardingStep == currStep {
			logger.Debug(ctx, "next onb step same as curr step, exiting orchestrator loop", zap.String(logger.WEALTH_ONB_STEP_NAME, currStep.String()))
			break
		}
		logger.Info(ctx, fmt.Sprintf("peformed wonb step. actor_id: %v, current_step: %v, next_step: %v", od.GetActorId(), currStep, seRes.NextOnboardingStep))
		currStep = seRes.NextOnboardingStep
		currStatus = seRes.OnboardingStatus
	}
	// record onboarding status
	metrics.RecordOnboardingStatus(od.GetStatus())

	// if status is terminal status the update the status
	if IsTerminalStatus(currStatus) {
		od.Status = currStatus
		txnErr := s.idempotentTxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
			return s.terminateOnboarding(txnCtx, od)
		})
		if txnErr != nil {
			logger.Error(ctx, "error terminating onboarding in txn", zap.Error(txnErr))
			return nil, nil, errors.Wrap(txnErr, "error terminating onboarding in txn")
		}
	}

	// Note: Please ensure onboarding status RPC impl. is in sync with any changes to logic which are part of this if condition
	if od.GetStatus() == woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED {
		// isAdvisoryAgreementNeeded is passed as false here because for freshly onboarding user,
		// if they skipped it already during onboarding, it should not come again at the end of onboarding
		// for already onboarded users it will appear from the first call to this function before the orchestration for loop
		return s.askOnboardedUsersForMissingData(ctx, od, false)
	}

	return od, nil, nil
}

func (s *Service) updateDbAndSetupRetry(ctx context.Context, seRes *steps.StepExecutionResponse, stepData *woPb.OnboardingStepDetails, od *woPb.OnboardingDetails, currStep woPb.OnboardingStep, perfErr error, isRetryNeeded bool) error {
	if seRes.CurrentStepDetails != nil {
		seRes.CurrentStepDetails.Id = stepData.GetId()
		seRes.CurrentStepDetails.OnboardingDetailsId = od.GetId()
		seRes.CurrentStepDetails.Step = currStep
		fm := []woPb.OnboardingStepDetailsFieldMask{
			woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_METADATA,
			woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_STATUS,
			woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_SUB_STATUS,
		}
		if seRes.CurrentStepDetails.GetStatus() == woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED {
			seRes.CurrentStepDetails.CompletedAt = timestamppb.Now()
			fm = append(fm, woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_COMPLETED_AT)
			metrics.RecordStepCompletionTime(currStep, time.Since(seRes.CurrentStepDetails.CreatedAt.AsTime()))
		}
		if isRetryNeeded {
			rs := s.getRetryStrategyForStep(ctx, seRes.CurrentStepDetails.GetStep(), seRes.CurrentStepDetails.GetStatus(), seRes.CurrentStepDetails.GetSubStatus())
			if rs != nil {
				fm = s.setupRetry(ctx, rs, seRes, stepData, fm, od)
			} else {
				logger.Info(ctx, "no retry strategy found", zap.String("step", seRes.CurrentStepDetails.GetStep().String()),
					zap.String("status", seRes.CurrentStepDetails.GetStatus().String()), zap.String("sub_status", seRes.CurrentStepDetails.GetSubStatus().String()))
			}
		}
		upSErr := s.onboardingStepDetailsDao.UpdateById(ctx, seRes.CurrentStepDetails, fm)
		if upSErr != nil {
			return errors.Wrap(upSErr, "onboardingStepDetails update failed by onboarding id and step")
		}
		//nocustomlint:goroutine
		go s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), event.NewWealthonboardingStepEvent(time.Now(), epificontext.AppVersionFromContext(ctx), epificontext.AppPlatformFromContext(ctx), od, seRes.CurrentStepDetails, perfErr))
		upErr := s.onboardingDetailsDao.Update(ctx, od, []woPb.OnboardingDetailsFieldMask{
			woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_METADATA,
		})
		if upErr != nil {
			return errors.Wrap(upErr, "onboardingDetails update failed")
		}
	}
	return nil
}

func (s *Service) getOrCreateOnbDetails(ctx context.Context, actorId string, onbType woPb.OnboardingType) (*woPb.OnboardingDetails, error) {
	var od *woPb.OnboardingDetails
	switch onbType {
	case woPb.OnboardingType_ONBOARDING_TYPE_WEALTH:
		od = &woPb.OnboardingDetails{
			ActorId:        actorId,
			Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_PENDING,
			CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
			OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
		}
	case woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT:
		od = &woPb.OnboardingDetails{
			ActorId:        actorId,
			Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_PENDING,
			CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION_FOR_INVESTMENT,
			OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT,
		}
	default:
		return nil, errors.New(fmt.Sprintf("invalid onboarding type: %v, exiting orchestrator", onbType))
	}

	var dbErr error
	od, _, dbErr = s.onboardingDetailsDao.GetOrCreate(ctx, actorId, onbType, od)
	if dbErr != nil {
		logger.Error(ctx, "error getting or creating onboarding details",
			zap.Error(dbErr), zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.WEALTH_ONB_TYPE, onbType.String()))
		return nil, errors.Wrap(dbErr, "error getting or creating onboarding details")
	}
	return od, nil
}

func (s *Service) askOnboardedUsersForMissingData(ctx context.Context, od *woPb.OnboardingDetails, isAdvisoryAgreementNeeded bool) (*woPb.OnboardingDetails, *deeplink.Deeplink, error) {
	if od.GetOnboardingType() != woPb.OnboardingType_ONBOARDING_TYPE_WEALTH {
		return od, nil, nil
	}
	if od.GetMetadata().GetCustomerProvidedData().GetNomineeDeclarationDetails().GetChoice() == commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
		collectUserInputStep, hfErr := s.handlerFactory.GetStepHandler(ctx, woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO)
		if hfErr != nil {
			return nil, nil, errors.Wrap(hfErr, "unable to get collect missing data step handler")
		}
		stepRes, stepErr := collectUserInputStep.Perform(ctx, od)
		if stepErr != nil {
			logger.Error(ctx, "failed to execute step for nominee collection", zap.Error(stepErr))
			return nil, nil, errors.Wrap(stepErr, "failed to execute step for nominee collection")
		}
		if stepRes != nil && stepRes.Deeplink != nil {
			return od, stepRes.Deeplink, nil
		}
	}

	isRiskProfilingEnabled, updateAppDl, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_RISK_PROFILING).WithActorId(od.GetActorId()))
	if err != nil {
		return nil, nil, fmt.Errorf("failed to check if user is eligible for risk profiling step : %w", err)
	}
	if updateAppDl != nil {
		return od, updateAppDl, nil
	}
	if isRiskProfilingEnabled && !od.GetMetadata().GetIsInvestmentRiskSurveyComplete() {
		riskProfilingStepHandler, handlerErr := s.handlerFactory.GetStepHandler(ctx, woPb.OnboardingStep_ONBOARDING_STEP_INVESTMENT_RISK_PROFILING)
		if handlerErr != nil {
			return nil, nil, fmt.Errorf("failed to get risk profiling step handler : %w", handlerErr)
		}
		// create entry in step details if already not present
		newOnbStepDetails := &woPb.OnboardingStepDetails{OnboardingDetailsId: od.GetId(), Step: woPb.OnboardingStep_ONBOARDING_STEP_INVESTMENT_RISK_PROFILING, Status: woPb.OnboardingStepStatus_STEP_STATUS_PENDING}
		stepData, _, dbErr := s.onboardingStepDetailsDao.GetOrCreate(ctx, od.GetId(), woPb.OnboardingStep_ONBOARDING_STEP_INVESTMENT_RISK_PROFILING, newOnbStepDetails)
		if dbErr != nil {
			logger.Error(ctx, "failed to getOrCreate onboarding step for risk profiling", zap.Error(dbErr))
			return nil, nil, errors.Wrap(dbErr, "failed to getOrCreate onboarding step for risk profiling")
		}
		stepResp, stepErr := riskProfilingStepHandler.Perform(ctx, od)
		if stepErr != nil {
			logger.Error(ctx, "failed to execute step for risk profiling", zap.Error(stepErr))
		}
		if stepResp == nil {
			logger.Error(ctx, "failed to execute step: nil step execution response", zap.String(logger.ACTOR_ID_V2, od.GetActorId()), zap.String(logger.ONBOARDING_STAGE, woPb.OnboardingStep_ONBOARDING_STEP_INVESTMENT_RISK_PROFILING.String()))
			return nil, nil, errors.New("failed to execute step: nil step execution response")
		}
		uErr := s.updateDbAndSetupRetry(ctx, stepResp, stepData, od, woPb.OnboardingStep_ONBOARDING_STEP_INVESTMENT_RISK_PROFILING, stepErr, false)
		if uErr != nil {
			return nil, nil, errors.Wrap(uErr, "error in updating db entry")
		}
		if stepResp.Deeplink != nil {
			return od, stepResp.Deeplink, nil
		}
	}

	onboardingDetails, stepDeeplink, err := s.checkIfWealthMITCConsentStepNeeded(ctx, od)
	if err != nil {
		logger.Error(ctx, "failed to check if mitc consent step is needed for user", zap.Error(err))
		return nil, nil, errors.Wrap(err, "failed to check if mitc consent step is needed for user")
	}
	if stepDeeplink != nil {
		return onboardingDetails, stepDeeplink, nil
	}

	onboardingDetails, stepDeeplink, err = s.checkIfNomineeModificationStepNeeded(ctx, od)
	if err != nil {
		logger.Error(ctx, "failed to if nominee modification step is needed for user", zap.Error(err))
		return nil, nil, errors.Wrap(err, "failed to if nominee modification step is needed for use")
	}
	if stepDeeplink != nil {
		return onboardingDetails, stepDeeplink, nil
	}

	if isAdvisoryAgreementNeeded && od.GetMetadata().GetPersonalDetails().GetAdvisoryAgreementDetails().GetSignedAgreementS3Path() == "" {
		advisoryAgreementStep, hfErr := s.handlerFactory.GetStepHandler(ctx, woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT)
		if hfErr != nil {
			return nil, nil, errors.Wrap(hfErr, "unable to get advisory agreement step handler")
		}
		// create entry in step details if already not present
		newOnbStepDetails := &woPb.OnboardingStepDetails{OnboardingDetailsId: od.GetId(), Step: woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, Status: woPb.OnboardingStepStatus_STEP_STATUS_PENDING}
		stepData, _, dbErr := s.onboardingStepDetailsDao.GetOrCreate(ctx, od.GetId(), woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, newOnbStepDetails)
		if dbErr != nil {
			logger.Error(ctx, "error getting or creating onboarding step details", zap.Error(dbErr))
			return nil, nil, errors.Wrap(dbErr, "error getting or creating onboarding step details")
		}
		stepRes, stepErr := advisoryAgreementStep.Perform(ctx, od)
		if stepErr != nil {
			logger.Error(ctx, "failed to execute step for advisory agreement sign", zap.Error(stepErr))
		}
		if stepRes == nil {
			logger.Error(ctx, "failed to execute step: nil step execution response", zap.String(logger.ACTOR_ID_V2, od.GetActorId()), zap.String(logger.ONBOARDING_STAGE, woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT.String()))
			return nil, nil, errors.New("failed to execute step: nil step execution response")
		}
		uErr := s.updateDbAndSetupRetry(ctx, stepRes, stepData, od, woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT, stepErr, false)
		if uErr != nil {
			return nil, nil, errors.Wrap(uErr, "error in updating db entry")
		}
		if stepRes != nil && stepRes.Deeplink != nil {
			return od, stepRes.Deeplink, nil
		}
	}

	return od, nil, nil
}

func (s *Service) checkIfWealthMITCConsentStepNeeded(ctx context.Context, onboardingDetails *woPb.OnboardingDetails) (*woPb.OnboardingDetails, *deeplink.Deeplink, error) {
	// create entry in step details if already not present
	newOnbStepDetails := &woPb.OnboardingStepDetails{
		OnboardingDetailsId: onboardingDetails.GetId(),
		Step:                woPb.OnboardingStep_ONBOARDING_STEP_USER_MITC_CONSENT,
		Status:              woPb.OnboardingStepStatus_STEP_STATUS_PENDING,
		SubStatus:           woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_USER_INPUT_NEEDED,
	}
	onboardingStepDetails, _, err := s.onboardingStepDetailsDao.GetOrCreate(ctx, onboardingDetails.GetId(), woPb.OnboardingStep_ONBOARDING_STEP_USER_MITC_CONSENT, newOnbStepDetails)
	if err != nil {
		logger.Error(ctx, "failed to getOrCreate onboarding step for mitc consent", zap.Error(err))
		return nil, nil, errors.Wrap(err, "failed to getOrCreate onboarding step for mitc consent")
	}
	if onboardingStepDetails.GetStep() == woPb.OnboardingStep_ONBOARDING_STEP_USER_MITC_CONSENT &&
		(onboardingStepDetails.GetStatus() == woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED || onboardingStepDetails.GetStatus() == woPb.OnboardingStepStatus_STEP_STATUS_SKIPPED) {
		return onboardingDetails, nil, nil
	}

	newConsentHandler, handlerErr := s.handlerFactory.GetStepHandler(ctx, woPb.OnboardingStep_ONBOARDING_STEP_USER_MITC_CONSENT)
	if handlerErr != nil {
		return nil, nil, fmt.Errorf("failed to get mitc consent step handler : %w", handlerErr)
	}
	stepResp, stepErr := newConsentHandler.Perform(ctx, onboardingDetails)
	if stepErr != nil || stepResp == nil {
		return nil, nil, errors.Wrap(stepErr, "failed to execute step for mitc consent")
	}
	err = s.updateDbAndSetupRetry(ctx, stepResp, onboardingStepDetails, onboardingDetails, woPb.OnboardingStep_ONBOARDING_STEP_USER_MITC_CONSENT, stepErr, false)
	if err != nil {
		return nil, nil, errors.Wrap(err, "error in updating db entry for mitc consent")
	}
	if stepResp.Deeplink != nil {
		return onboardingDetails, stepResp.Deeplink, nil
	}
	return onboardingDetails, nil, nil
}

func (s *Service) checkIfNomineeModificationStepNeeded(ctx context.Context, onboardingDetails *woPb.OnboardingDetails) (*woPb.OnboardingDetails, *deeplink.Deeplink, error) {
	// create entry in step details if already not present
	newOnbStepDetails := &woPb.OnboardingStepDetails{
		OnboardingDetailsId: onboardingDetails.GetId(),
		Step:                woPb.OnboardingStep_ONBOARDING_STEP_UPDATE_NOMINEE_DETAILS,
		Status:              woPb.OnboardingStepStatus_STEP_STATUS_PENDING,
		SubStatus:           woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_USER_INPUT_NEEDED,
	}
	onboardingStepDetails, _, err := s.onboardingStepDetailsDao.GetOrCreate(ctx, onboardingDetails.GetId(), woPb.OnboardingStep_ONBOARDING_STEP_UPDATE_NOMINEE_DETAILS, newOnbStepDetails)
	if err != nil {
		logger.Error(ctx, "failed to getOrCreate onboarding step for nominee modification", zap.Error(err))
		return nil, nil, errors.Wrap(err, "failed to getOrCreate onboarding step for nominee modification")
	}
	if onboardingStepDetails.GetStep() == woPb.OnboardingStep_ONBOARDING_STEP_UPDATE_NOMINEE_DETAILS &&
		onboardingStepDetails.GetStatus() == woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED {
		return onboardingDetails, nil, nil
	}

	nomineeModificationHandler, handlerErr := s.handlerFactory.GetStepHandler(ctx, woPb.OnboardingStep_ONBOARDING_STEP_UPDATE_NOMINEE_DETAILS)
	if handlerErr != nil {
		return nil, nil, fmt.Errorf("failed to get risk profiling step handler : %w", handlerErr)
	}
	stepResp, stepErr := nomineeModificationHandler.Perform(ctx, onboardingDetails)
	if stepErr != nil || stepResp == nil {
		return nil, nil, errors.Wrap(stepErr, "failed to execute step for nominee modification")
	}
	err = s.updateDbAndSetupRetry(ctx, stepResp, onboardingStepDetails, onboardingDetails, woPb.OnboardingStep_ONBOARDING_STEP_UPDATE_NOMINEE_DETAILS, stepErr, false)
	if err != nil {
		return nil, nil, errors.Wrap(err, "error in updating db entry for nominee modification")
	}
	if stepResp.Deeplink != nil {
		return onboardingDetails, stepResp.Deeplink, nil
	}
	return onboardingDetails, nil, nil
}

// setupRetry pushes packet which when consumed will retry wealth onboarding (unless all retries are exhausted)
func (s *Service) setupRetry(ctx context.Context, rs retry.RetryStrategy, seRes *steps.StepExecutionResponse,
	stepData *woPb.OnboardingStepDetails, fm []woPb.OnboardingStepDetailsFieldMask, od *woPb.OnboardingDetails) []woPb.OnboardingStepDetailsFieldMask {
	var nextExpectedRetryAt time.Time
	nextInterval := rs.GetNextRetryIntervalDuration(uint(seRes.CurrentStepDetails.GetNumAttempts()))
	if stepData.GetExpectedRetryAt().IsValid() {
		logger.Info(ctx, fmt.Sprintf("got expected retry time: %v", stepData.GetExpectedRetryAt().AsTime().In(datetime.IST).String()))
	} else {
		logger.Info(ctx, "got empty or invalid retry time")
	}

	switch {
	case stepData.GetExpectedRetryAt() == nil || (stepData.GetExpectedRetryAt().IsValid() && stepData.GetExpectedRetryAt().AsTime().IsZero()):
		nextExpectedRetryAt = time.Now().Add(nextInterval).Round(time.Second)
	case stepData.GetExpectedRetryAt().IsValid() && time.Now().After(stepData.GetExpectedRetryAt().AsTime()):
		// NOTE: prev retry timestamp is used, instead of time.Now(), so that next retry timestamp is same across multiple invocations
		nextExpectedRetryAt = stepData.GetExpectedRetryAt().AsTime().Add(nextInterval).Round(time.Second)

		// users for whom wealth onboarding was stuck for long time due to some reason / bug, would have a very old expected retry at timestamp
		// for them next expected retry at would be in the past too after adding the next retry interval duration, hence defaulting to time.Now()
		if time.Now().After(nextExpectedRetryAt) {
			nextExpectedRetryAt = time.Now().Add(nextInterval).Round(time.Second)
		}
	default:
		// if curr time is before expected retry time, no need to push (duplicate) packet
		return fm
	}
	seRes.CurrentStepDetails.NumAttempts = stepData.GetNumAttempts() + 1
	fm = append(fm, woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_NUM_ATTEMPTS)
	if rs.IsMaxRetryMet(uint(seRes.CurrentStepDetails.GetNumAttempts())) {
		logger.Info(ctx, "retries exhausted")
		seRes.CurrentStepDetails.SubStatus = woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_RETRY_EXHAUSTED

		stepStatus := woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED
		if stepData.GetSubStatus() == woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_VENDOR_DOWNTIME {
			// do not change status to manual intervention because downtime might extend our retry time
			stepStatus = stepData.GetStatus()
		}
		// calling GetStepExecutionResponse since step status is to be modified
		// in below func call, nextStepOnSuccess is sent as unspecified because it does not matter, since the current step status is sent as manual intervention
		res := steps.GetStepExecutionResponse(od, stepStatus, seRes.CurrentStepDetails.GetStep(), woPb.OnboardingStep_ONBOARDING_STEP_UNSPECIFIED)
		seRes.CurrentStepDetails.Status = res.CurrentStepDetails.GetStatus() // should be woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED
		seRes.OnboardingStatus = res.OnboardingStatus                        // should be woPb.OnboardingStatus_ONBOARDING_STATUS_MANUAL_INTERVENTION_NEEDED
		seRes.NextOnboardingStep = res.NextOnboardingStep                    // should be woPb.OnboardingStep_ONBOARDING_STEP_UNSPECIFIED
	} else {
		seRes.CurrentStepDetails.ExpectedRetryAt = timestamppb.New(nextExpectedRetryAt)
		fm = append(fm, woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_EXPECTED_RETRY_AT)
		// Delay queue can send us packets before the exact expected timestamp (observed +/- 1-2 sec variability at least)
		// Hence adding 10-sec extra delay ensuring packets are consumed after the expected timestamp
		roundedNextRetryAt := nextExpectedRetryAt.Add(10 * time.Second).Round(time.Second)
		_, pubErr := s.stepsRetryDelayPublisher.PublishWithDelay(ctx, &consumer.RetryOnboardingEvent{OnboardingId: od.GetId()}, time.Until(roundedNextRetryAt).Round(time.Second))
		if pubErr != nil {
			logger.Error(ctx, "retry onboarding event push failed", zap.Error(pubErr))
		} else {
			logger.Info(ctx, fmt.Sprintf("pushed packet to retry onboarding after: %v", roundedNextRetryAt.In(datetime.IST).String()))
		}
	}
	return fm
}

func (s *Service) createOnboardingDetailsInDb(ctx context.Context, actorId string, onbType woPb.OnboardingType) (*woPb.OnboardingDetails, error) {
	switch onbType {
	case woPb.OnboardingType_ONBOARDING_TYPE_WEALTH:
		return s.onboardingDetailsDao.Create(ctx, &woPb.OnboardingDetails{
			ActorId:        actorId,
			Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_PENDING,
			CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
			OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
		})
	case woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT:
		return s.onboardingDetailsDao.Create(ctx, &woPb.OnboardingDetails{
			ActorId:        actorId,
			Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_PENDING,
			CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION_FOR_INVESTMENT,
			OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT,
		})
	default:
		return nil, errors.New(fmt.Sprintf("unable to map onbType: %v", onbType))
	}
}

func (s *Service) markStepsStale(ctx context.Context, actorId string, onbType woPb.OnboardingType, steps []woPb.OnboardingStep) (*woPb.OnboardingDetails, error) {
	obd, err := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, actorId, onbType)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get onboarding details by actor-id and onboarding type")
	}
	stepDetails, err := s.onboardingStepDetailsDao.GetByOnboardingDetailsId(ctx, obd.GetId())
	if err != nil {
		return nil, errors.Wrap(err, "failed to get onboarding step details by onboarding id")
	}
	stepDetailsList, err := getStepDetailsBasedOnOrderOfExecution(steps, stepDetails)
	if err != nil {
		return nil, errors.Wrap(err, "failed to getStepDetailsBasedOnOrderOfExecution")
	}
	if txnErr := s.idempotentTxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		// mark all steps staled
		isFirstStepToInvalidate := true
		for _, sd := range stepDetailsList {
			sd.StaledAt = timestamppb.Now()
			stepUpdateErr := s.onboardingStepDetailsDao.UpdateById(txnCtx, sd, []woPb.OnboardingStepDetailsFieldMask{woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_STALED_AT})
			if stepUpdateErr != nil {
				return errors.Wrap(stepUpdateErr, "failed to update step by id")
			}
			// updating first step to invalidate as current step and marking onboarding status in-progress
			if isFirstStepToInvalidate {
				obd.CurrentStep = sd.Step
				obd.Status = woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS
				obdUpdateErr := s.onboardingDetailsDao.Update(txnCtx, obd, []woPb.OnboardingDetailsFieldMask{woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_CURRENT_STEP, woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_STATUS})
				if obdUpdateErr != nil {
					return errors.Wrap(obdUpdateErr, "failed to update onboarding details")
				}
				isFirstStepToInvalidate = false
			}
		}
		return nil
	}); txnErr != nil {
		return nil, errors.Wrap(txnErr, "failed to invalidate steps")
	}
	return obd, nil
}

func areValidSteps(steps []woPb.OnboardingStep, stepDetails []*woPb.OnboardingStepDetails) bool {
	for _, step := range steps {
		if getStepDetails(step, stepDetails) == nil {
			return false
		}
	}
	return true
}

func getStepDetailsBasedOnOrderOfExecution(steps []woPb.OnboardingStep, stepDetails []*woPb.OnboardingStepDetails) ([]*woPb.OnboardingStepDetails, error) {
	if !areValidSteps(steps, stepDetails) {
		return nil, errors.New("can't mark un-executed steps stale")
	}
	var stepsToReExecuteDetails []*woPb.OnboardingStepDetails
	for _, sd := range stepDetails {
		for _, step := range steps {
			if step == sd.GetStep() {
				stepsToReExecuteDetails = append(stepsToReExecuteDetails, sd)
			}
		}
	}
	return stepsToReExecuteDetails, nil
}

func getStepDetails(step woPb.OnboardingStep, stepDetails []*woPb.OnboardingStepDetails) *woPb.OnboardingStepDetails {
	for _, stepDetails := range stepDetails {
		if step == stepDetails.GetStep() {
			return stepDetails
		}
	}
	return nil
}

func (s *Service) getRetryStrategyForStep(ctx context.Context, step woPb.OnboardingStep, status woPb.OnboardingStepStatus, subStatus woPb.OnboardingStepSubStatus) retry.RetryStrategy {
	if s.conf.StepsRetryStrategy == nil {
		return nil
	}
	srp, ok := s.conf.StepsRetryStrategy[step.String()]
	if !ok {
		return s.getGenericRetryStrategy(ctx, status, subStatus)
	}
	strp, ok := srp[status.String()]
	if !ok {
		return s.getGenericRetryStrategy(ctx, status, subStatus)
	}
	sbrp, ok := strp[subStatus.String()]
	if !ok {
		return s.getGenericRetryStrategy(ctx, status, subStatus)
	}
	srs, dErr := retry.NewStrategyFromConfig(sbrp)
	if dErr != nil {
		logger.Error(ctx, "failed to create RetryStrategy fro  retry params", zap.Error(dErr))
		return nil
	}
	return srs
}

func (s *Service) getGenericRetryStrategy(ctx context.Context, status woPb.OnboardingStepStatus, subStatus woPb.OnboardingStepSubStatus) retry.RetryStrategy {
	drp, ok1 := s.conf.StepsRetryStrategy["ALLSTEPS"]
	if !ok1 {
		return nil
	}
	dsrp, ok1 := drp[status.String()]
	if !ok1 {
		return nil
	}

	// special handling for transient failures
	// treating all transient failures as same and having a common retry strategy
	var subStatusStr string
	if status == woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE {
		subStatusStr = "ALLSUBSTATUS"
	} else {
		subStatusStr = subStatus.String()
	}

	dsbrp, ok1 := dsrp[subStatusStr]
	if !ok1 {
		return nil
	}
	drs, dErr := retry.NewStrategyFromConfig(dsbrp)
	if dErr != nil {
		logger.Error(ctx, "failed to create RetryStrategy from retry params", zap.Error(dErr))
		return nil
	}
	return drs
}

func (s *Service) setupUserNotifications(ctx context.Context, onbDetails *woPb.OnboardingDetails, onbStepDetails *woPb.OnboardingStepDetails) error {
	notificationConfigs := GetNotificationConfigsForCurrentUserState(s.conf, onbDetails, onbStepDetails)
	if len(notificationConfigs) == 0 {
		logger.Debug(ctx, "no notification config found for current user state", zap.String(logger.WEALTH_ONB_STEP_NAME, onbDetails.GetCurrentStep().String()),
			zap.String(logger.WEALTH_ONB_STEP_STATUS, onbStepDetails.GetStatus().String()), zap.String(logger.WEALTH_ONB_STEP_SUB_STATUS, onbStepDetails.GetSubStatus().String()))
		return nil
	}
	for _, notificationConfig := range notificationConfigs {
		campaignName := commPb.CampaignName(commPb.CampaignName_value[notificationConfig.Campaign])
		if campaignName == commPb.CampaignName_CAMPAIGN_NAME_UNSPECIFIED {
			logger.Error(ctx, "error casting campaign value from config", zap.String(logger.CAMPAIGN_ID, notificationConfig.Campaign))
			continue
		}

		// Checks if a notification has already been set up; sets it up if it hasn't been and marks it as done in db
		// A common do-once-tasks table is used to check if notification has been set up or not
		// step id is used in task id so that notification will be sent if a step is staled and executed again
		taskId := fmt.Sprintf("SETUP WONB PN FOR ACTORID: %s, CAMPAIGN: %s, STEP ID: %s", onbDetails.GetActorId(), campaignName.String(), onbStepDetails.GetId())
		isDone, isDoneDbErr := s.doOnce.IsDone(ctx, taskId)
		if isDoneDbErr != nil {
			logger.Error(ctx, "error in getting do once task", zap.Error(isDoneDbErr), zap.String(logger.TASK_ID, taskId))
			return isDoneDbErr
		}
		if isDone {
			logger.Debug(ctx, fmt.Sprintf("task already done: %s", taskId))
			continue
		}
		logger.Debug(ctx, "setting up notification for later", zap.String(logger.CAMPAIGN_ID, campaignName.String()))
		_, publishErr := s.userCommsDelayPublisher.PublishWithDelay(ctx, &consumer.SendUserNotificationsRequest{
			ActorId:        onbDetails.GetActorId(),
			OnboardingType: onbDetails.GetOnboardingType(),
			CampaignName:   campaignName,
		}, notificationConfig.Delay)
		if publishErr != nil {
			logger.Error(ctx, "unable to send message to queue", zap.String(logger.CAMPAIGN_TYPE, campaignName.String()), zap.Error(publishErr))
			return publishErr
		}
		if doDbErr := s.doOnce.Do(ctx, taskId); doDbErr != nil {
			logger.Error(ctx, "unable to mark task as done", zap.Error(doDbErr), zap.String(logger.TASK_ID, taskId))
			return doDbErr
		}
		logger.Info(ctx, fmt.Sprintf("one time user notification setup successfully with delay %s: %s", notificationConfig.Delay.String(), taskId))
	}
	return nil
}

func GetNotificationConfigsForCurrentUserState(cfg *config.Config, onbDetails *woPb.OnboardingDetails, onbStepDetails *woPb.OnboardingStepDetails) []*config.DelayedNotification {
	var notifications []*config.DelayedNotification
	for _, userCommCfg := range cfg.UserComms {
		if onbDetails.GetCurrentStep().String() == userCommCfg.Step &&
			onbStepDetails.GetStatus().String() == userCommCfg.Status &&
			onbStepDetails.GetSubStatus().String() == userCommCfg.SubStatus {
			notifications = append(notifications, userCommCfg.Notifications...)
		}
	}
	return notifications
}

// Actions performed when a terminal state is reached:,
//  1. update onboarding status
//  2. if onboarding is successfully completed,
//     a. store completion timestamp
//     b. create/update wealth user record
//     c. if aadhaar is validated by KRA, unpause any paused FIT rules
func (s *Service) terminateOnboarding(ctx context.Context, od *woPb.OnboardingDetails) error {
	fm := []woPb.OnboardingDetailsFieldMask{
		woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_STATUS,
	}
	if od.Status == woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED {
		od.CompletedAt = timestamppb.Now()
		fm = append(fm, woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_COMPLETED_AT)
		metrics.RecordOnboardingTime(time.Since(od.CreatedAt.AsTime()))
	}
	upErr := s.onboardingDetailsDao.Update(ctx, od, fm)
	if upErr != nil {
		return errors.Wrap(upErr, "failed to mark onboarding details to terminal status")
	}
	if od.GetStatus() == woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED {
		// Note: we don't force update any fields here. In case user's phone number is already populated via user re-onboarding
		// with different phone number or email, those fields are not replaced from here
		userErr := s.userService.CreateOrUpdateUser(ctx, getWealthUser(od), nil)
		if userErr != nil {
			return errors.Wrap(userErr, "failed to create or update user data")
		}

		if od.GetOnboardingType() == woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT &&
			helper.IsCollectedAddressProofAadhaar(od) {
			return helper.UnblockRMSRuleExecutions(ctx, od, s.rmsRuleManagerClient,
				rmsPb.SubscriptionExecutionState_BLOCKED_DUE_TO_AADHAAR_POA_NOT_VALIDATED_BY_KRA, rmsPb.SubscriptionStateChangeReason_AADHAAR_POA_VALIDATED_BY_KRA)
		}
	}
	return nil
}
