package orchestrator_model

import woPb "github.com/epifi/gamma/api/wealthonboarding"

type OrchestrateRequest struct {
	ActorId    string
	OnbType    woPb.OnboardingType
	EntryPoint woPb.OnboardingStepEntrypoint
}

func (o *OrchestrateRequest) GetActorId() string {
	if o != nil {
		return o.ActorId
	}
	return ""
}

func (o *OrchestrateRequest) GetOnbType() woPb.OnboardingType {
	if o != nil {
		return o.OnbType
	}
	return woPb.OnboardingType_ONBOARDING_TYPE_UNSPECIFIED
}

func (o *OrchestrateRequest) GetEntryPoint() woPb.OnboardingStepEntrypoint {
	if o != nil {
		return o.EntryPoint
	}
	return woPb.OnboardingStepEntrypoint_ONBOARDING_STEP_ENTRYPOINT_UNSPECIFIED
}
