package orchestrator

import (
	"flag"
	"os"
	"sync"
	"testing"

	cmdtypes "github.com/epifi/be-common/pkg/cmd/types"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/test"
)

type DaoTestSuite struct {
	db     cmdtypes.EpifiWealthCRDB
	conf   *config.Config
	dbName string
}

var (
	AffectedTestTables    = []string{"onboarding_details,onboarding_step_details"}
	daoTestSuite          DaoTestSuite
	initialiseSameDbOnce  sync.Once
	conf                  *config.Config
	idempotentTxnExecutor storagev2.IdempotentTxnExecutor
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()

	var (
		db       cmdtypes.EpifiWealthCRDB
		teardown func()
		dbName   string
	)
	dbName, conf, _, db, teardown = test.InitTestServer()
	idempotentTxnExecutor = storagev2.NewCRDBIdempotentTxnExecutor(db)

	daoTestSuite = DaoTestSuite{
		db:     db,
		conf:   conf,
		dbName: dbName,
	}

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
