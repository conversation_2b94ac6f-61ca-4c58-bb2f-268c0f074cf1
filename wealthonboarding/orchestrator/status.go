package orchestrator

import (
	woPb "github.com/epifi/gamma/api/wealthonboarding"
)

func IsTerminalStatus(status woPb.OnboardingStatus) bool {
	switch status {
	case
		woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED,
		woPb.OnboardingStatus_ONBOARDING_STATUS_FAILED,
		woPb.OnboardingStatus_ONBOARDING_STATUS_MANUAL_INTERVENTION_NEEDED,
		woPb.OnboardingStatus_ONBOARDING_STATUS_FUTURE_SCOPE:
		return true
	default:
		return false
	}
}
