package consumer

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	woTypes "github.com/epifi/gamma/wealthonboarding/types"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth/afu"
	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	"github.com/epifi/gamma/api/auth/notification"
	commsPb "github.com/epifi/gamma/api/comms"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/fcm"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woConsumerPb "github.com/epifi/gamma/api/wealthonboarding/consumer"
	wealthUserPb "github.com/epifi/gamma/api/wealthonboarding/user"
	"github.com/epifi/gamma/wealthonboarding/ckyc"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/dao"
	woErr "github.com/epifi/gamma/wealthonboarding/errors"
	"github.com/epifi/gamma/wealthonboarding/orchestrator"
	"github.com/epifi/gamma/wealthonboarding/user"
)

type Service struct {
	woConsumerPb.UnimplementedWealthOnboardingConsumerServer
	livenessClient           livenessPb.LivenessClient
	onboardingStepDetailsDao dao.OnboardingStepDetailsDao
	wealthOnbClient          woPb.WealthOnboardingClient
	onboardingDetailsDao     dao.OnboardingDetailsDao
	userSvc                  user.IService
	userClient               userPb.UsersClient
	actorClient              actorPb.ActorClient
	ckycService              *ckyc.Service
	afuWealthPublisher       queue.Publisher
	idempotentTxnExecutor    storagev2.IdempotentTxnExecutor
	conf                     *config.Config
	commsClient              commsPb.CommsClient
}

func NewService(
	livenessClient livenessPb.LivenessClient,
	onboardingStepDetailsDao dao.OnboardingStepDetailsDao,
	wealthOnbClient woPb.WealthOnboardingClient,
	onboardingDetailsDao dao.OnboardingDetailsDao,
	userSvc user.IService,
	userClient userPb.UsersClient,
	actorClient actorPb.ActorClient,
	ckycService *ckyc.Service,
	afuWealthPublisher woTypes.AuthFactorUpdateWealthPublisher,
	idempotentTxnExecutor storagev2.IdempotentTxnExecutor,
	conf *config.Config,
	commsClient commsPb.CommsClient) *Service {
	return &Service{
		livenessClient:           livenessClient,
		onboardingStepDetailsDao: onboardingStepDetailsDao,
		wealthOnbClient:          wealthOnbClient,
		onboardingDetailsDao:     onboardingDetailsDao,
		userSvc:                  userSvc,
		userClient:               userClient,
		actorClient:              actorClient,
		ckycService:              ckycService,
		afuWealthPublisher:       afuWealthPublisher,
		idempotentTxnExecutor:    idempotentTxnExecutor,
		conf:                     conf,
		commsClient:              commsClient,
	}
}

var (
	_ woConsumerPb.WealthOnboardingConsumerServer = &Service{}

	successResp = func() *woConsumerPb.RefreshFaceMatchStatusResponse {
		return &woConsumerPb.RefreshFaceMatchStatusResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_SUCCESS,
			},
		}
	}
	permFailureResp = func() *woConsumerPb.RefreshFaceMatchStatusResponse {
		return &woConsumerPb.RefreshFaceMatchStatusResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
			},
		}
	}
	transFailureResp = func() *woConsumerPb.RefreshFaceMatchStatusResponse {
		return &woConsumerPb.RefreshFaceMatchStatusResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
			},
		}
	}
)

func (s *Service) RefreshFaceMatchStatus(ctx context.Context, req *woConsumerPb.RefreshFaceMatchStatusRequest) (*woConsumerPb.RefreshFaceMatchStatusResponse, error) {
	ctx = epificontext.CtxWithActorId(ctx, req.GetOnboardingDetails().GetActorId())
	od := req.GetOnboardingDetails()
	if od.GetId() == "" {
		logger.Error(ctx, "onboarding details id can't be blank")
		return permFailureResp(), nil
	}
	osd, err := s.onboardingStepDetailsDao.GetByOnboardingDetailsIdAndStep(ctx, od.GetId(), woPb.OnboardingStep_ONBOARDING_STEP_FACE_MATCH)
	if err != nil {
		logger.Error(ctx, "error while fetching onboarding liveness step", zap.Error(err))
		return transFailureResp(), nil
	}

	latestFmAttempt := od.GetMetadata().GetFaceMatchData().GetFaceMatchAttempts()[len(od.GetMetadata().GetFaceMatchData().GetFaceMatchAttempts())-1]

	if latestFmAttempt.GetAttemptId() == "" {
		logger.Info(ctx, "liveness not yet initiated")
		return successResp(), nil
	}
	if osd.GetStatus() == woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED {
		logger.Info(ctx, "liveness status already updated")
		return successResp(), nil
	}
	fmRes, fmrErr := s.livenessClient.GetFaceMatchStatus(ctx, &livenessPb.GetFaceMatchStatusRequest{
		ActorId:   od.GetActorId(),
		AttemptId: latestFmAttempt.GetAttemptId(),
	})
	if fmErr := epifigrpc.RPCError(fmRes, fmrErr); fmErr != nil {
		logger.Error(ctx, "fetching fm status failed", zap.Error(fmErr))
		s.updateFailures(ctx, osd, req)
		return transFailureResp(), nil
	}
	if fmRes.GetFaceMatchStatus() == livenessPb.FaceMatchStatus_FACE_MATCH_PASSED {
		osd.Status = woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED
		osd.SubStatus = woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSPECIFIED
		osd.CompletedAt = timestamppb.Now()
		uErr := s.onboardingStepDetailsDao.UpdateById(ctx, osd, []woPb.OnboardingStepDetailsFieldMask{
			woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_STATUS,
			woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_SUB_STATUS,
			woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_COMPLETED_AT,
		})
		if uErr != nil {
			logger.Error(ctx, "failed to update liveness step", zap.Error(uErr))
			s.updateFailures(ctx, osd, req)
			return transFailureResp(), nil
		}
		return successResp(), nil
	}
	logger.Info(ctx, "face match still pending", zap.String("status", fmRes.GetFaceMatchStatus().String()))
	s.updateFailures(ctx, osd, req)
	return transFailureResp(), nil
}

// not returning error is intentional as update to manual intervention is best effort
func (s *Service) updateFailures(ctx context.Context, osd *woPb.OnboardingStepDetails, req *woConsumerPb.RefreshFaceMatchStatusRequest) {
	if req.GetRequestHeader().GetIsLastAttempt() {
		osd.Status = woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED
		osd.SubStatus = woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSPECIFIED
		uErr := s.onboardingStepDetailsDao.UpdateById(ctx, osd, []woPb.OnboardingStepDetailsFieldMask{
			woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_STATUS,
			woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_SUB_STATUS,
		})
		if uErr != nil {
			logger.Error(ctx, "failed to update liveness step to manual intervention needed", zap.Error(uErr))
		}
	}
}

//nolint:funlen
func (s *Service) InitWealthOnboarding(ctx context.Context, req *onbPb.OnboardingStageUpdate) (*woConsumerPb.InitWealthOnboardingResponse, error) {
	actorId := req.GetActorId()
	ctx = epificontext.CtxWithActorId(ctx, actorId)
	logger.Info(ctx, "processing wealth ckyc data pull for actor", zap.String(logger.ACTOR_ID_V2, actorId))
	if req.GetState() != onbPb.OnboardingStageUpdate_SUCCESS || req.GetStage() != onbPb.OnboardingStage_ONBOARDING_COMPLETE {
		logger.Error(ctx, fmt.Sprintf("dropping packet, user is not succesfully onboarded, State: %v and Stage %v", req.GetState(), req.GetStage()))
		return &woConsumerPb.InitWealthOnboardingResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_SUCCESS,
			},
		}, nil
	}
	// fetch user details for actor
	actorRes, actorErr := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if te := epifigrpc.RPCError(actorRes, actorErr); te != nil {
		logger.Error(ctx, "error while fetching actor", zap.Error(te))
		return &woConsumerPb.InitWealthOnboardingResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
			},
		}, nil
	}
	userRes, userErr := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_Id{
			Id: actorRes.GetActor().GetEntityId(),
		},
	})
	if te := epifigrpc.RPCError(userRes, userErr); te != nil {
		logger.Error(ctx, "error while fetching user", zap.Error(te))
		return &woConsumerPb.InitWealthOnboardingResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
			},
		}, nil
	}
	// create basic onboarding details so that it can be used to pull ckyc data
	od := &woPb.OnboardingDetails{
		ActorId: actorId,
		Metadata: &woPb.OnboardingMetadata{
			PanDetails: &types.DocumentProof{
				Id: userRes.GetUser().GetProfile().GetPAN(),
			},
			PersonalDetails: &woPb.PersonalDetails{
				Dob: userRes.GetUser().GetProfile().GetDateOfBirth(),
			},
			CkycData: &woPb.CkycData{},
		},
	}

	if s.conf.DisableCKYC {
		logger.Error(ctx, "returning from consumer because ckyc is disabled", zap.String("state", req.GetState().String()), zap.String("stage", req.GetStage().String()))
		return &woConsumerPb.InitWealthOnboardingResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_SUCCESS,
			},
		}, nil
	}

	sdRes, sdErr := s.ckycService.CreateOrGetSearchData(ctx, od.GetActorId(), od.GetMetadata().GetPanDetails().GetId())
	if sdErr != nil {
		if errors.Is(sdErr, epifierrors.ErrRecordNotFound) {
			// no record found in ckyc, marking packet as success
			logger.Info(ctx, "no ckyc record found during consumer pull")
			return &woConsumerPb.InitWealthOnboardingResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			}, nil
		}
		logger.Error(ctx, "error occurred while getting ckyc search data", zap.Error(sdErr))
		return &woConsumerPb.InitWealthOnboardingResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
			},
		}, nil
	}
	// assign the search data response
	od.GetMetadata().GetCkycData().SearchData = sdRes
	if sdRes.GetConstitutionType() != "" {
		logger.Info(ctx, "ckyc entity type non individual")
		return &woConsumerPb.InitWealthOnboardingResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_SUCCESS,
			},
		}, nil
	}
	_, ddErr := s.ckycService.CreateOrGetDownloadData(ctx, od.GetActorId(), od.GetMetadata().GetPersonalDetails().GetDob())
	if ddErr != nil {
		if errors.Is(ddErr, woErr.ErrCkycDobMismatch) {
			// dob mismatch error in ckyc download response, marking packet as success
			logger.Info(ctx, "dob mismatch error in ckyc download response during consumer pull")
			return &woConsumerPb.InitWealthOnboardingResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			}, nil
		}
		logger.Error(ctx, "error occurred while getting ckyc download data", zap.Error(ddErr))
		return &woConsumerPb.InitWealthOnboardingResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
			},
		}, nil
	}
	return &woConsumerPb.InitWealthOnboardingResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_SUCCESS,
		},
	}, nil
}

func (s *Service) RetryOnboarding(ctx context.Context, req *woConsumerPb.RetryOnboardingEvent) (*woConsumerPb.RetryOnboardingResponse, error) {
	logger.Debug(ctx, "retrying onboarding...")

	onboardingId := req.GetOnboardingId()
	// TODO(Brijesh): Remove this after 2 weeks of deploying this change, once all packets using old field are consumed for sure
	if onboardingId == "" {
		onboardingId = req.GetOnboardingDetails().GetId()
	}
	onbDetails, onbDetailsErr := s.onboardingDetailsDao.GetById(ctx, onboardingId)
	if onbDetailsErr != nil {
		logger.Error(ctx, "error getting onboarding details by id", zap.Error(onbDetailsErr), zap.String(logger.WEALTH_ONB_ID, onboardingId))
		return &woConsumerPb.RetryOnboardingResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}
	ctx = epificontext.CtxWithActorId(ctx, onbDetails.GetActorId())
	res, err := s.wealthOnbClient.GetNextOnboardingStep(ctx, &woPb.GetNextOnboardingStatusRequest{
		ActorId:        onbDetails.GetActorId(),
		OnboardingType: onbDetails.GetOnboardingType(),
		WealthFlow:     onbDetails.GetCurrentWealthFlow(),
	})

	if te := epifigrpc.RPCError(res, err); te != nil {
		logger.Error(ctx, "error getting next onboarding step", zap.Error(te))
		// TODO(Brijesh): Manual intervention marking should be done inside orchestrator
		if req.GetRequestHeader().GetIsLastAttempt() {
			upErr := s.markAsManualInterventionNeeded(ctx, onbDetails)
			if upErr != nil {
				logger.Error(ctx, "error marking onboarding as manual intervention needed", zap.Error(upErr))
			}
			return &woConsumerPb.RetryOnboardingResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE}}, nil
		}
		return &woConsumerPb.RetryOnboardingResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}
	return &woConsumerPb.RetryOnboardingResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS}}, nil
}

func (s *Service) markAsManualInterventionNeeded(ctx context.Context, od *woPb.OnboardingDetails) error {
	// get current step details
	osd, osdErr := s.onboardingStepDetailsDao.GetByOnboardingDetailsIdAndStep(ctx, od.GetId(), od.GetCurrentStep())
	if osdErr != nil {
		return errors.Wrap(osdErr, "error getting onboarding step details")
	}
	txnErr := s.idempotentTxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		osdMask := []woPb.OnboardingStepDetailsFieldMask{
			woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_STATUS,
			woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_SUB_STATUS,
		}
		osd.Status = woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED
		osd.SubStatus = woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_FAILED_DUE_TO_ORCHESTRATION_FAILURE
		updErr := s.onboardingStepDetailsDao.UpdateById(txnCtx, osd, osdMask)
		if updErr != nil {
			return errors.Wrap(updErr, "error updating onboarding step details ")
		}
		od.Status = woPb.OnboardingStatus_ONBOARDING_STATUS_MANUAL_INTERVENTION_NEEDED
		updErr = s.onboardingDetailsDao.Update(txnCtx, od, []woPb.OnboardingDetailsFieldMask{
			woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_STATUS,
		})
		if updErr != nil {
			return errors.Wrap(updErr, "error updating onboarding details")
		}
		return nil
	})
	return txnErr
}

//nolint:funlen
func (s *Service) ProcessAuthFactorUpdateEvent(ctx context.Context, afuEvent *notification.AuthFactorUpdateEvent) (*woConsumerPb.ProcessAuthFactorUpdateEventResponse, error) {
	// check if the update type is phone or email
	ctx = epificontext.CtxWithActorId(ctx, afuEvent.GetActorId())
	var (
		phoneNumber *commontypes.PhoneNumber
		email       = ""
	)
	for _, authFactor := range afuEvent.GetAuthFactors() {
		switch authFactor {
		case afu.AuthFactor_PHONE_NUM:
			phoneNumber = afuEvent.GetNewAuthFactors().GetPhoneNumber()
		case afu.AuthFactor_EMAIL:
			email = afuEvent.GetNewAuthFactors().GetEmail()
		default:
			// do nothing
		}
	}
	// no updates in phone number or email
	if phoneNumber == nil && email == "" {
		logger.Info(ctx, "no phone number or email id to update")
		return &woConsumerPb.ProcessAuthFactorUpdateEventResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_SUCCESS,
		}}, nil
	}
	userData, gErr := s.userSvc.GetByActorId(ctx, afuEvent.GetActorId())
	if gErr != nil {
		if errors.Is(gErr, epifierrors.ErrRecordNotFound) {
			// when user has not started or completed their wealth onboarding journey, we create a new record with just the phone number and email
			logger.Info(ctx, "no record of user in wealth db, creating record", zap.String(logger.ACTOR_ID_V2, afuEvent.GetActorId()))
			userData = &wealthUserPb.User{ActorId: afuEvent.GetActorId()}
		} else {
			logger.Info(ctx, "error while fetching user data for actor", zap.Error(gErr), zap.String(logger.ACTOR_ID_V2, afuEvent.GetActorId()))
			return &woConsumerPb.ProcessAuthFactorUpdateEventResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
		}
	}
	// updating the phone number or email and adding them to force update field list
	updErr := s.updateUserPhoneEmail(ctx, userData, phoneNumber, email)
	if updErr != nil {
		logger.Info(ctx, "error while updating user data", zap.Error(updErr), zap.String(logger.ACTOR_ID_V2, userData.GetActorId()))
		return &woConsumerPb.ProcessAuthFactorUpdateEventResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}

	// Publish to wealth SNS topic for MF to consume and update on their side if needed
	msgId, pubErr := s.afuWealthPublisher.Publish(ctx, &woConsumerPb.AuthFactorUpdateWealthEvent{
		ActorId:        afuEvent.GetActorId(),
		AuthFactors:    afuEvent.GetAuthFactors(),
		OldAuthFactors: afuEvent.GetOldAuthFactors(),
		NewAuthFactors: afuEvent.GetNewAuthFactors(),
	})
	if pubErr != nil {
		logger.Error(ctx, "error while publishing afu wealth event", zap.Error(pubErr))
		return &woConsumerPb.ProcessAuthFactorUpdateEventResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
		}}, nil
	}
	logger.Info(ctx, "successfully published afu wealth event", zap.String("msgId", msgId))
	return &woConsumerPb.ProcessAuthFactorUpdateEventResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{
		Status: queuePb.MessageConsumptionStatus_SUCCESS,
	}}, nil
}

func (s *Service) updateUserPhoneEmail(ctx context.Context, userData *wealthUserPb.User, phoneNumber *commontypes.PhoneNumber, email string) error {
	var forceUpdateFields []user.ForceUpdateField
	if userData.GetPersonalDetails() == nil {
		userData.PersonalDetails = &wealthUserPb.PersonalDetails{}
	}
	if phoneNumber != nil {
		userData.GetPersonalDetails().PhoneNumber = phoneNumber
		forceUpdateFields = append(forceUpdateFields, user.PhoneNumber)
	}
	if email != "" {
		userData.GetPersonalDetails().Email = email
		forceUpdateFields = append(forceUpdateFields, user.EmailId)
	}
	updErr := s.userSvc.CreateOrUpdateUser(ctx, userData, forceUpdateFields)
	if updErr != nil {
		return errors.Wrap(updErr, "error updating user data")
	}
	return nil
}

// SendUserNotifications gets the latest onboarding state of the user first.
// If they are still at the same state as when the notification request was set up, we send a notification to nudge user (if not already sent)
// Campaign name is used to decide which notification to send in case of multiple notifications for an onboarding state
//
//nolint:funlen
func (s *Service) SendUserNotifications(ctx context.Context, req *woConsumerPb.SendUserNotificationsRequest) (*woConsumerPb.SendUserNotificationsResponse, error) {
	ctx = epificontext.CtxWithActorId(ctx, req.GetActorId())
	// Re-triggering orchestrator so that user's onboarding journey gets updated if needed
	// This is done to check if there is no need to send the PN after the update to user's onboarding journey
	// For example, in case the notification was scheduled because of a transient failure which probably has gotten resolved by now, then we don't want to send the PN
	nextOnbStepRes, nextOnbStepErr := s.wealthOnbClient.GetNextOnboardingStep(ctx, &woPb.GetNextOnboardingStatusRequest{
		ActorId:        req.GetActorId(),
		WealthFlow:     woPb.WealthFlow_WEALTH_FLOW_INVESTMENT,
		OnboardingType: req.GetOnboardingType(),
	})
	if err := epifigrpc.RPCError(nextOnbStepRes, nextOnbStepErr); err != nil {
		logger.Error(ctx, "error getting next onboarding step", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()),
			zap.String(logger.WEALTH_ONB_FLOW, woPb.WealthFlow_WEALTH_FLOW_INVESTMENT.String()), zap.String(logger.WEALTH_ONB_TYPE, req.GetOnboardingType().String()))
		return &woConsumerPb.SendUserNotificationsResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}
	onbDetails, onbDetailsErr := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, req.GetActorId(), req.GetOnboardingType())
	if onbDetailsErr != nil {
		logger.Error(ctx, "unable to get wonb details from db", zap.Error(onbDetailsErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.WEALTH_ONB_TYPE, req.GetOnboardingType().String()))
		return &woConsumerPb.SendUserNotificationsResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}
	onbStepDetails, onbStepDetailsErr := s.onboardingStepDetailsDao.GetByOnboardingDetailsIdAndStep(ctx, onbDetails.GetId(), onbDetails.GetCurrentStep())
	if onbStepDetailsErr != nil {
		logger.Error(ctx, "unable to get wonb details from db", zap.Error(onbStepDetailsErr), zap.String(logger.WEALTH_ONB_ID, onbDetails.GetId()), zap.String(logger.WEALTH_ONB_STEP_NAME, onbDetails.GetCurrentStep().String()))
		return &woConsumerPb.SendUserNotificationsResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}

	notificationConfigs := orchestrator.GetNotificationConfigsForCurrentUserState(s.conf, onbDetails, onbStepDetails)
	var notificationConfigToSend *config.DelayedNotification
	for _, notificationConfig := range notificationConfigs {
		campaignName := commsPb.CampaignName(commsPb.CampaignName_value[notificationConfig.Campaign])
		if campaignName == commsPb.CampaignName_CAMPAIGN_NAME_UNSPECIFIED {
			logger.Error(ctx, "error casting campaign value from config", zap.String(logger.CAMPAIGN_ID, notificationConfig.Campaign))
			return &woConsumerPb.SendUserNotificationsResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE}}, nil
		}
		if campaignName == req.CampaignName {
			notificationConfigToSend = notificationConfig
			break
		}
	}
	if notificationConfigToSend == nil {
		logger.Error(ctx, "no notification config matched, skipping notification",
			zap.String(logger.WEALTH_ONB_STEP_NAME, onbDetails.GetCurrentStep().String()), zap.String(logger.WEALTH_ONB_STEP_STATUS, onbStepDetails.GetStatus().String()), zap.String(logger.WEALTH_ONB_STEP_SUB_STATUS, onbStepDetails.GetSubStatus().String()))
		return &woConsumerPb.SendUserNotificationsResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS}}, nil
	}
	actorRes, actorErr := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: req.GetActorId()})
	if err := epifigrpc.RPCError(actorRes, actorErr); err != nil {
		logger.Error(ctx, "unable to get actor by ID", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return &woConsumerPb.SendUserNotificationsResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}

	if nextOnbStepRes.GetNextStep() == nil {
		logger.Info(ctx, "no deeplink to be sent in PN", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.CAMPAIGN_ID, req.GetCampaignName().String()))
	}
	err := s.createAndSendNotificationMsg(ctx, actorRes.GetActor().GetEntityId(), req.GetCampaignName(), notificationConfigToSend, nextOnbStepRes.GetNextStep())
	if err != nil {
		return &woConsumerPb.SendUserNotificationsResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}
	return &woConsumerPb.SendUserNotificationsResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS}}, nil
}

func (s *Service) createAndSendNotificationMsg(ctx context.Context, userId string,
	campaignName commsPb.CampaignName, notificationConfigToSend *config.DelayedNotification, deeplink *deeplinkPb.Deeplink) error {
	notificationMsg := &commsPb.SendMessageRequest{
		Type:           commsPb.QoS_BEST_EFFORT,
		Medium:         commsPb.Medium_NOTIFICATION,
		UserIdentifier: &commsPb.SendMessageRequest_UserId{UserId: userId},
		CampaignName:   campaignName,
		Message: &commsPb.SendMessageRequest_Notification{Notification: &commsPb.NotificationMessage{
			Notification: &fcm.Notification{
				NotificationType: fcm.NotificationType_SYSTEM_TRAY,
				NotificationTemplates: &fcm.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcm.SystemTrayTemplate{
					CommonTemplateFields: &fcm.CommonTemplateFields{
						Title:          notificationConfigToSend.NotificationData.Title,
						Body:           notificationConfigToSend.NotificationData.Body,
						IconAttributes: &fcm.IconAttributes{IconUrl: notificationConfigToSend.NotificationData.IconUrl},
						Deeplink:       deeplink,
					},
				}},
			},
		}},
	}
	res, err := s.commsClient.SendMessage(ctx, notificationMsg)
	if err = epifigrpc.RPCError(res, err); err != nil {
		if res.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "notification not sent to user due to app not found")
			return nil
		}
		logger.Error(ctx, "error sending notification", zap.Error(err))
		return err
	}
	logger.Info(ctx, "notification triggered successfully", zap.String(logger.QUEUE_MESSAGE_ID, res.GetMessageId()))
	return nil
}
