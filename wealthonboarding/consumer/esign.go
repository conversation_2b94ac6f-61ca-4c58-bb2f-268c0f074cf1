//nolint:funlen
package consumer

import (
	"context"

	queuePb "github.com/epifi/be-common/api/queue"
	woConsumerPb "github.com/epifi/gamma/api/wealthonboarding/consumer"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/esign"

	"go.uber.org/zap"
)

var (
	_ woConsumerPb.ESignConsumerServer = &ESignConsumerService{}
)

type ESignConsumerService struct {
	detailsDao           dao.ESignDetailsDao
	s3Client             *s3.Client
	eSignService         *esign.ESignService
	eSignProviderService *esign.ESignProviderService
}

func NewESignConsumerService(
	detailsDao dao.ESignDetailsDao,
	s3Client *s3.Client,
	eSignService *esign.ESignService,
	eSignProviderService *esign.ESignProviderService) *ESignConsumerService {
	return &ESignConsumerService{
		detailsDao:           detailsDao,
		s3Client:             s3Client,
		eSignService:         eSignService,
		eSignProviderService: eSignProviderService,
	}
}

func (e *ESignConsumerService) UpdateTxnState(ctx context.Context, request *woConsumerPb.UpdateTxnStateRequest) (*woConsumerPb.UpdateTxnStateResponse, error) {
	isPermanentFailure, dsuErr := e.eSignService.DownloadSignedDocAndUpdateTxnState(ctx, &woConsumerPb.UpdateTxnStateRequest{
		ESignId: request.GetESignId(),
	})
	if dsuErr != nil {
		logger.Error(ctx, "failed to DownloadSignedDocAndUpdateTxnState", zap.Error(dsuErr), zap.String("ID", request.GetESignId()), zap.Bool("isPermanentFailure", isPermanentFailure))
		if isPermanentFailure {
			return sendPacketResp(queuePb.MessageConsumptionStatus_PERMANENT_FAILURE)
		}
		return sendPacketResp(queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE)
	}
	return sendPacketResp(queuePb.MessageConsumptionStatus_SUCCESS)
}

func sendPacketResp(status queuePb.MessageConsumptionStatus) (*woConsumerPb.UpdateTxnStateResponse, error) {
	return &woConsumerPb.UpdateTxnStateResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status: status,
		},
	}, nil
}
