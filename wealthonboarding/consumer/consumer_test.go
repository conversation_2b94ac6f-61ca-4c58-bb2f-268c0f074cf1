package consumer

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	mockQueue "github.com/epifi/be-common/pkg/queue/mocks"
	actorPb "github.com/epifi/gamma/api/actor"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	"github.com/epifi/gamma/api/auth/afu"
	"github.com/epifi/gamma/api/auth/notification"
	commsPb "github.com/epifi/gamma/api/comms"
	commsMocks "github.com/epifi/gamma/api/comms/mocks"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woConsumerPb "github.com/epifi/gamma/api/wealthonboarding/consumer"
	wealthOnbMocks "github.com/epifi/gamma/api/wealthonboarding/mocks"
	wealthUserPb "github.com/epifi/gamma/api/wealthonboarding/user"
	mockDao "github.com/epifi/gamma/wealthonboarding/test/mocks/dao"
	wealthUser "github.com/epifi/gamma/wealthonboarding/user"
)

// TODO: Fix Tests
func TestService_SendUserNotifications(t *testing.T) {
	t.Skip()
	ctrl := gomock.NewController(t)
	mockOnbDetailsDao := mockDao.NewMockOnboardingDetailsDao(ctrl)
	mockOnbStepDetailsDao := mockDao.NewMockOnboardingStepDetailsDao(ctrl)
	mockActorClient := actorMocks.NewMockActorClient(ctrl)
	mockCommsClient := commsMocks.NewMockCommsClient(ctrl)
	mockWealthOnbClient := wealthOnbMocks.NewMockWealthOnboardingClient(ctrl)
	type args struct {
		ctx context.Context
		req *woConsumerPb.SendUserNotificationsRequest
	}
	tests := []struct {
		name       string
		setupMocks func()
		args       args
		want       *woConsumerPb.SendUserNotificationsResponse
		wantErr    bool
	}{
		{
			name: "send notification if user is stuck in same state",
			setupMocks: func() {
				mockWealthOnbClient.EXPECT().GetNextOnboardingStep(gomock.Any(), gomock.Any()).Return(&woPb.GetNextOnboardingStatusResponse{
					Status: rpc.StatusOk(),
					NextStep: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_AGREEMENT_CONFIRMATION_SCREEN,
						ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingSignAgreementScreenOptions{
							WealthOnboardingSignAgreementScreenOptions: &deeplinkPb.WealthOnboardingSignAgreementScreenOptions{
								TncUrl:         "dummyTnCURL",
								IsConsentTaken: true,
							},
						},
					},
				}, nil)
				mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(&woPb.OnboardingDetails{CurrentStep: woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC}, nil)
				mockOnbStepDetailsDao.EXPECT().GetByOnboardingDetailsIdAndStep(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(&woPb.OnboardingStepDetails{
						Status:    woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS,
						SubStatus: woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_EMPTY_DOWNLOAD_API_RESPONSE,
					}, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{Status: rpc.StatusOk()}, nil)
				mockCommsClient.EXPECT().SendMessage(gomock.Any(), gomock.Any()).Return(&commsPb.SendMessageResponse{Status: rpc.StatusOk()}, nil)
			},
			args: args{ctx: ctx, req: &woConsumerPb.SendUserNotificationsRequest{CampaignName: commsPb.CampaignName_CAMPAIGN_NAME_WONB_DOB_MISMATCH_PAN_NEEDED}},
			want: &woConsumerPb.SendUserNotificationsResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS},
			},
		},
		{
			name: "skip notification if user is not stuck anymore",
			setupMocks: func() {
				mockWealthOnbClient.EXPECT().GetNextOnboardingStep(gomock.Any(), gomock.Any()).Return(&woPb.GetNextOnboardingStatusResponse{Status: rpc.StatusOk()}, nil)
				mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(&woPb.OnboardingDetails{CurrentStep: woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC}, nil)
				mockOnbStepDetailsDao.EXPECT().GetByOnboardingDetailsIdAndStep(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(&woPb.OnboardingStepDetails{Status: woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED}, nil)
			},
			args: args{ctx: ctx, req: &woConsumerPb.SendUserNotificationsRequest{CampaignName: commsPb.CampaignName_CAMPAIGN_NAME_WONB_DOB_MISMATCH_PAN_NEEDED}},
			want: &woConsumerPb.SendUserNotificationsResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				onboardingStepDetailsDao: mockOnbStepDetailsDao,
				onboardingDetailsDao:     mockOnbDetailsDao,
				actorClient:              mockActorClient,
				conf:                     conf,
				commsClient:              mockCommsClient,
				wealthOnbClient:          mockWealthOnbClient,
			}
			tt.setupMocks()

			got, err := s.SendUserNotifications(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("SendUserNotifications() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SendUserNotifications() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_ProcessAuthFactorUpdateEvent(t *testing.T) {
	t.Skip()
	ctrl := gomock.NewController(t)
	mockAfuWealthPublisher := mockQueue.NewMockPublisher(ctrl)
	mockWealthUserDao := mockDao.NewMockUserDao(ctrl)
	type args struct {
		ctx      context.Context
		afuEvent *notification.AuthFactorUpdateEvent
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func()
		want       *woConsumerPb.ProcessAuthFactorUpdateEventResponse
		wantErr    bool
	}{
		{
			name: "create a new wealth user record if none is present",
			args: args{
				ctx: context.Background(),
				afuEvent: &notification.AuthFactorUpdateEvent{
					ActorId:        "randomActorId",
					AuthFactors:    []afu.AuthFactor{afu.AuthFactor_PHONE_NUM},
					NewAuthFactors: &afu.AuthFactorValues{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 8800278580}},
				},
			},
			setupMocks: func() {
				mockWealthUserDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound).Times(2)
				mockWealthUserDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(&wealthUserPb.User{}, nil)
				mockAfuWealthPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("randomMsgId", nil)
			},
			want:    &woConsumerPb.ProcessAuthFactorUpdateEventResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS}},
			wantErr: false,
		},
		{
			name: "update wealth user record if already present",
			args: args{
				ctx: context.Background(),
				afuEvent: &notification.AuthFactorUpdateEvent{
					ActorId:        "randomActorId",
					AuthFactors:    []afu.AuthFactor{afu.AuthFactor_PHONE_NUM},
					NewAuthFactors: &afu.AuthFactorValues{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 8800278580}},
				},
			},
			setupMocks: func() {
				mockWealthUserDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(&wealthUserPb.User{ActorId: "randomActorId"}, nil).Times(2)
				mockWealthUserDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				mockAfuWealthPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("randomMsgId", nil)
			},
			want:    &woConsumerPb.ProcessAuthFactorUpdateEventResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				afuWealthPublisher: mockAfuWealthPublisher,
				userSvc:            &wealthUser.Service{UserDao: mockWealthUserDao},
			}
			tt.setupMocks()
			got, err := s.ProcessAuthFactorUpdateEvent(tt.args.ctx, tt.args.afuEvent)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessAuthFactorUpdateEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessAuthFactorUpdateEvent() got = %v, want %v", got, tt.want)
			}
		})
	}
}
