package ckyc_helper

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	types "github.com/epifi/gamma/api/typesv2"
	ckycPb "github.com/epifi/gamma/api/vendorgateway/wealth/ckyc"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	woCkyc "github.com/epifi/gamma/wealthonboarding/ckyc"
	"github.com/epifi/gamma/wealthonboarding/dao"
	woErr "github.com/epifi/gamma/wealthonboarding/errors"
	"github.com/epifi/gamma/wealthonboarding/event"
	"github.com/epifi/gamma/wealthonboarding/helper"
	"github.com/epifi/gamma/wealthonboarding/ocr"

	"github.com/pkg/errors"
	"go.uber.org/zap"
)

const (
	ckycNoLength         = 14
	ckycDobMismatchError = "date of birth entered does not match with the date of birth stated in the ckyc number"
)

type CkycHelperImpl struct {
	ckycClient       ckycPb.CkycClient
	docHelper        helper.DocumentHelper
	vendorRequestDao dao.VendorRequestDao
	ocr              ocr.Ocr
	commonHelper     *helper.CommonHelper
	ckycService      *woCkyc.Service
	eventBroker      events.Broker
}

func NewCkycHelper(
	ckycClient ckycPb.CkycClient,
	docHelper helper.DocumentHelper,
	vendorRequestDao dao.VendorRequestDao,
	ocr ocr.Ocr,
	commonHelper *helper.CommonHelper,
	ckycService *woCkyc.Service,
	eventBroker events.Broker) *CkycHelperImpl {
	return &CkycHelperImpl{
		ckycClient:       ckycClient,
		docHelper:        docHelper,
		vendorRequestDao: vendorRequestDao,
		ocr:              ocr,
		commonHelper:     commonHelper,
		ckycService:      ckycService,
		eventBroker:      eventBroker,
	}
}

func (c *CkycHelperImpl) FetchAndPopulateCkycSearchData(ctx context.Context, details *woPb.OnboardingDetails) error {
	if details.GetMetadata().GetCkycData().GetSearchData() != nil {
		logger.Info(ctx, "ckyc search already done")
		return nil
	}
	// fetch and populate data in ckyc service
	searchData, searchDataErr := c.ckycService.CreateOrGetSearchData(ctx, details.GetActorId(), details.GetMetadata().GetPanDetails().GetId())
	if searchDataErr != nil {
		return errors.Wrap(searchDataErr, "error while getting ckyc search data from ckyc service")
	}

	logger.Info(ctx, "populating ckyc search data")
	if details.GetMetadata().GetCkycData().GetSearchData() == nil {
		if details.GetMetadata().GetCkycData() == nil {
			if details.GetMetadata() == nil {
				details.Metadata = &woPb.OnboardingMetadata{}
			}
			details.GetMetadata().CkycData = &woPb.CkycData{}
		}
		details.GetMetadata().GetCkycData().SearchData = &woPb.CkycSearchData{
			CkycNumber:  searchData.GetCkycNumber(),
			Name:        searchData.GetName(),
			FathersName: searchData.GetFathersName(),
			Age:         searchData.GetAge(),
			Photo:       searchData.GetPhoto(),
			KycDate:     searchData.GetKycDate(),
			UpdatedDate: searchData.GetUpdatedDate(),
		}
	}
	sd := details.GetMetadata().GetCkycData().GetSearchData()
	sd.CkycNumber = searchData.GetCkycNumber()
	sd.Name = searchData.GetName()
	sd.FathersName = searchData.GetFathersName()
	sd.Age = searchData.GetAge()
	sd.Photo = searchData.GetPhoto()
	sd.KycDate = searchData.GetKycDate()
	sd.UpdatedDate = searchData.GetUpdatedDate()
	sd.CkycAccountType = GetCKYCAccountTypeByAccountNumber(ctx, searchData.GetCkycNumber())
	sd.ConstitutionType = searchData.GetConstitutionType()
	logger.Info(ctx, "populated ckyc search data")
	return nil
}

func (c *CkycHelperImpl) FetchPopulateValidateCkycDownloadData(ctx context.Context, details *woPb.OnboardingDetails) (woPb.OnboardingStepSubStatus, error) {
	err := c.fetchAndPopulateCkycDownloadData(ctx, details)
	var stepSubStatus woPb.OnboardingStepSubStatus
	isNonIndividual := IsNonIndividual(details.GetMetadata().GetCkycData().GetSearchData())
	if isNonIndividual {
		stepSubStatus = woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CKYC_ENTITY_TYPE_NON_INDIVIDUAL
	} else {
		stepSubStatus = GetStepSubStatusByCKYCAccountType(details.GetMetadata().GetCkycData().GetSearchData().GetCkycAccountType())
	}
	if err != nil {
		if errors.Is(err, woErr.ErrCkycDobMismatch) {
			return woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_DOB_MISMATCH_ERROR_IN_CKYC_DOWNLOAD_RESPONSE, nil
		}
		return stepSubStatus, errors.Wrap(err, "failed to fetchAndPopulateCkycDownloadData")
	}
	return stepSubStatus, nil
}

//nolint:funlen
func (c *CkycHelperImpl) fetchAndPopulateCkycDownloadData(ctx context.Context, details *woPb.OnboardingDetails) error {
	platform, version := epificontext.AppPlatformAndVersion(ctx)
	var eventErr error
	defer func() {
		go c.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), event.NewCkycCallEvent(details.GetActorId(), time.Now(), details.GetCurrentWealthFlow(), version, platform, details.GetMetadata(), eventErr))
	}()

	if details.GetMetadata().GetCkycData().GetSearchData() == nil {
		err := c.FetchAndPopulateCkycSearchData(ctx, details)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				eventErr = epifierrors.ErrRecordNotFound
				return errors.Wrap(err, "unable to populate ckyc search data")
			}
			eventErr = errors.New("failed to FetchAndPopulateCkycSearchData before CkycDownload call")
			return errors.Wrap(err, "failed to FetchAndPopulateCkycSearchData before CkycDownload call")
		}
	}

	if details.GetMetadata().GetCkycData().GetDownloadData() == nil {
		ckycData, ckycDataErr := c.ckycService.CreateOrGetDownloadData(ctx, details.GetActorId(), details.GetMetadata().GetPersonalDetails().GetDob())
		if ckycDataErr != nil {
			eventErr = ckycDataErr
			return ckycDataErr
		}
		logger.Info(ctx, "populating ckyc download data")
		if details.GetMetadata().GetCkycData().GetDownloadData() == nil {
			if details.GetMetadata().GetCkycData() == nil {
				if details.GetMetadata() == nil {
					details.Metadata = &woPb.OnboardingMetadata{}
				}
				details.GetMetadata().CkycData = &woPb.CkycData{}
			}
			details.GetMetadata().GetCkycData().DownloadData = &woPb.CkycDownloadData{}
		}
		if details.GetMetadata().GetCkycData().GetDownloadData().GetIdentityDetails() == nil || len(details.GetMetadata().GetCkycData().GetDownloadData().GetIdentityDetails()) == 0 {
			var ids []*woPb.CkycIdentityDetail
			for _, id := range ckycData.GetIdentityDetails() {
				ids = append(ids, &woPb.CkycIdentityDetail{
					IdentityType:       id.GetIdentityType(),
					IdentityNumber:     id.GetIdentityNumber(),
					VerificationStatus: id.GetVerificationStatus(),
				})
			}
			details.GetMetadata().GetCkycData().GetDownloadData().IdentityDetails = ids
		}
		if details.GetMetadata().GetCkycData().GetDownloadData().GetPersonalDetails() == nil {
			details.GetMetadata().GetCkycData().GetDownloadData().PersonalDetails = &woPb.CkycPersonalDetails{
				AccountType:      ckycData.GetPersonalDetails().GetAccountType(),
				Name:             ckycData.GetPersonalDetails().GetName(),
				FathersName:      ckycData.GetPersonalDetails().GetFathersName(),
				MothersName:      ckycData.GetPersonalDetails().GetMothersName(),
				Gender:           ckycData.GetPersonalDetails().GetGender(),
				Dob:              ckycData.GetPersonalDetails().GetDob(),
				Nationality:      ckycData.GetPersonalDetails().GetNationality(),
				PermanentAddress: ckycData.GetPersonalDetails().GetPermanentAddress(),
				CurrentAddress:   ckycData.GetPersonalDetails().GetCurrentAddress(),
				TelOffice:        ckycData.GetPersonalDetails().GetTelOffice(),
				TelResidential:   ckycData.GetPersonalDetails().GetTelResidential(),
				MobileNumber:     ckycData.GetPersonalDetails().GetMobileNumber(),
				EmailId:          ckycData.GetPersonalDetails().GetEmailId(),
				DocumentsList:    ckycData.GetPersonalDetails().GetDocumentsList(),
				Pan:              ckycData.GetPersonalDetails().GetPan(),
				ProofOfAddress:   ckycData.GetPersonalDetails().GetProofOfAddress(),
				Remarks:          ckycData.GetPersonalDetails().GetRemarks(),
				MaidenName:       ckycData.GetPersonalDetails().GetMaidenName(),
				CkycNo:           ckycData.GetPersonalDetails().GetCkycNo(),
			}
		}
		logger.Info(ctx, "populated ckyc download data")
	}
	logger.Info(ctx, "updating metadata with ckyc download data")
	c.updateMetadataWithCkycData(ctx, details)
	logger.Info(ctx, "updated metadat with ckyc download data")
	return nil
}

func (c *CkycHelperImpl) updateMetadataWithCkycData(ctx context.Context, od *woPb.OnboardingDetails) {
	populateSignature(ctx, od)
}

//nolint:deadcode,unused
func parseCkycDownloadError(err string) error {
	switch {
	case err == "":
		return nil
	case strings.Contains(strings.ToLower(err), ckycDobMismatchError):
		return woErr.ErrCkycDobMismatch
	default:
		return errors.New(err)
	}
}

//nolint:deadcode,unused
func getCorrectedCkycNo(ckycNo string) (string, error) {
	if len(ckycNo) < ckycNoLength {
		return "", errors.New(fmt.Sprintf("invalid ckyc number: %v", ckycNo))
	}
	return ckycNo[len(ckycNo)-ckycNoLength:], nil
}

func GetCKYCAccountTypeByAccountNumber(ctx context.Context, ckycNumber string) woPb.CkycAccountType {
	ckycNum := strings.ToUpper(ckycNumber)
	switch {
	case strings.HasPrefix(ckycNum, "L"):
		logger.Info(ctx, "CKYC Number contains L")
		return woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_SIMPLIFIED
	case strings.HasPrefix(ckycNum, "S"):
		logger.Info(ctx, "CKYC Number contains S")
		return woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_SMALL
	case strings.HasPrefix(ckycNum, "O"):
		logger.Info(ctx, "CKYC Number contains O")
		return woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_OTP_EKYC
	default:
		return woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_NORMAL
	}
}

func IsNonIndividual(data *woPb.CkycSearchData) bool {
	return data.GetConstitutionType() != ""
}

func GetStepSubStatusByCKYCAccountType(accountType woPb.CkycAccountType) woPb.OnboardingStepSubStatus {
	switch accountType {
	case woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_SIMPLIFIED:
		return woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CKYC_ACCOUNT_TYPE_L
	case woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_SMALL:
		return woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CKYC_ACCOUNT_TYPE_S
	case woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_OTP_EKYC:
		return woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CKYC_ACCOUNT_TYPE_O
	default:
		return woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSPECIFIED
	}
}

//nolint:funlen,unused
func (c *CkycHelperImpl) populateCkycDownloadData(ctx context.Context, details *woPb.OnboardingDetails, res *ckycPb.CkycDownloadResponse) error {
	if details.GetMetadata().GetCkycData().GetDownloadData() == nil {
		if details.GetMetadata().GetCkycData() == nil {
			if details.GetMetadata() == nil {
				details.Metadata = &woPb.OnboardingMetadata{}
			}
			details.GetMetadata().CkycData = &woPb.CkycData{}
		}
		details.GetMetadata().GetCkycData().DownloadData = &woPb.CkycDownloadData{}
	}
	if details.GetMetadata().GetCkycData().GetDownloadData().GetIdentityDetails() == nil || len(details.GetMetadata().GetCkycData().GetDownloadData().GetIdentityDetails()) == 0 {
		var ids []*woPb.CkycIdentityDetail
		for _, id := range res.GetIdentityDetails() {
			ids = append(ids, &woPb.CkycIdentityDetail{
				IdentityType:       getCkycIdentityType(id.GetIdentityType()),
				IdentityNumber:     id.GetIdentityNumber(),
				VerificationStatus: getCkycVerificationStatus(id.GetVerificationStatus()),
			})
		}
		details.GetMetadata().GetCkycData().GetDownloadData().IdentityDetails = ids
	}
	if details.GetMetadata().GetCkycData().GetDownloadData().GetPersonalDetails() == nil {
		var docs []*types.DocumentProof
		idList := details.GetMetadata().GetCkycData().GetDownloadData().GetIdentityDetails()
		for _, im := range res.GetPersonalDetails().GetImageDetailsList() {
			// redacting documents we are not allowed to store
			if helper.IsDocumentStoreAllowed(im.GetCode()) {
				if im.GetData() == "" {
					continue
				}
				docImg, sErr := c.commonHelper.SanitizeImage(ctx, &commontypes.Image{
					ImageType:       im.GetType(),
					ImageDataBase64: im.GetData(),
				})
				if sErr != nil {
					logger.Error(ctx, "error while sanitizing img from tiff format", zap.Error(sErr))
				}
				doc := &types.DocumentProof{
					ProofType: im.GetCode(),
					Photo: []*commontypes.Image{
						docImg,
					},
				}
				for _, id := range idList {
					if getDocumentProofTypeFromIdentityType(id.GetIdentityType()) == im.GetCode() {
						doc.Id = id.GetIdentityNumber()
						break
					}
				}
				// uploading raw data with base64 data to s3 and getting doc with S3 urls
				docWithS3Paths, err := c.docHelper.UploadDoc(ctx, details.GetActorId(), doc)
				if err != nil {
					details.GetMetadata().GetCkycData().DownloadData = nil
					return errors.Wrap(err, "failed to get docWithS3Paths")
				}
				docs = append(docs, docWithS3Paths)
			} else {
				logger.Info(ctx, fmt.Sprintf("not allowed to store document proof type: %v", im.GetCode().String()))
			}
		}
		details.GetMetadata().GetCkycData().GetDownloadData().PersonalDetails = &woPb.CkycPersonalDetails{
			AccountType:      getCkycAccountType(res.GetPersonalDetails().GetAccountType()),
			Name:             res.GetPersonalDetails().GetName(),
			FathersName:      res.GetPersonalDetails().GetFathersName(),
			MothersName:      res.GetPersonalDetails().GetMothersName(),
			Gender:           res.GetPersonalDetails().GetGender(),
			Dob:              res.GetPersonalDetails().GetDob(),
			Nationality:      res.GetPersonalDetails().GetNationality(),
			PermanentAddress: res.GetPersonalDetails().GetPermanentAddress(),
			CurrentAddress:   res.GetPersonalDetails().GetCurrentAddress(),
			TelOffice:        res.GetPersonalDetails().GetTelOffice(),
			TelResidential:   res.GetPersonalDetails().GetTelResidential(),
			MobileNumber:     res.GetPersonalDetails().GetMobileNumber(),
			EmailId:          res.GetPersonalDetails().GetEmailId(),
			DocumentsList:    docs,
			Pan:              res.GetPersonalDetails().GetPan(),
			ProofOfAddress:   res.GetPersonalDetails().GetProofOfAddress(),
			Remarks:          res.GetPersonalDetails().GetRemarks(),
			MaidenName:       res.GetPersonalDetails().GetMaidenName(),
			CkycNo:           res.GetPersonalDetails().GetCkycNo(),
		}
	}
	return nil
}

//nolint:unused
func getCkycAccountType(accountType ckycPb.AccountType) woPb.CkycAccountType {
	switch accountType {
	default:
		return woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_TYPE_UNSPECIFIED
	case ckycPb.AccountType_ACCOUNT_TYPE_NORMAL:
		return woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_NORMAL
	case ckycPb.AccountType_ACCOUNT_TYPE_SMALL:
		return woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_SMALL
	case ckycPb.AccountType_ACCOUNT_TYPE_SIMPLIFIED:
		return woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_SIMPLIFIED
	case ckycPb.AccountType_ACCOUNT_TYPE_OTP_EKYC:
		return woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_OTP_EKYC
	}
}

//nolint:unused
func getCkycIdentityType(identityType ckycPb.IdentityType) woPb.CkycIdentityType {
	switch identityType {
	default:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_TYPE_UNSPECIFIED
	case ckycPb.IdentityType_IDENTITY_TYPE_PASSPORT:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_PASSPORT
	case ckycPb.IdentityType_IDENTITY_TYPE_VOTER_ID:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_VOTER_ID
	case ckycPb.IdentityType_IDENTITY_TYPE_PAN:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_PAN
	case ckycPb.IdentityType_IDENTITY_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR
	case ckycPb.IdentityType_IDENTITY_TYPE_DRIVING_LICENSE:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_DRIVING_LICENSE
	case ckycPb.IdentityType_IDENTITY_TYPE_NREGA:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_NREGA
	case ckycPb.IdentityType_IDENTITY_TYPE_NATIONAL_POPULATION_REGISTRY_LETTER:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_NATIONAL_POPULATION_REGISTRY_LETTER
	case ckycPb.IdentityType_IDENTITY_TYPE_EKYC_AUTHENTICATION:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_EKYC_AUTHENTICATION
	case ckycPb.IdentityType_IDENTITY_TYPE_OFFLINE_AADHAAR_VERIFICATION:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_OFFLINE_AADHAAR_VERIFICATION
	case ckycPb.IdentityType_IDENTITY_TYPE_OFFLINE_OTHERS:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_OFFLINE_OTHERS
	case ckycPb.IdentityType_IDENTITY_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GOVERNMENT:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GOVERNMENT
	case ckycPb.IdentityType_IDENTITY_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GAZETTED:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GAZETTED
	}
}

//nolint:unused
func getCkycVerificationStatus(verificationStatus ckycPb.VerificationStatus) woPb.CkycVerificationStatus {
	switch verificationStatus {
	default:
		return woPb.CkycVerificationStatus_CKYC_VERIFICATION_STATUS_TYPE_UNSPECIFIED
	case ckycPb.VerificationStatus_VERIFICATION_STATUS_YES:
		return woPb.CkycVerificationStatus_CKYC_VERIFICATION_STATUS_YES
	case ckycPb.VerificationStatus_VERIFICATION_STATUS_NO:
		return woPb.CkycVerificationStatus_CKYC_VERIFICATION_STATUS_NO
	}
}

//nolint:unused
func getDocumentProofTypeFromIdentityType(idType woPb.CkycIdentityType) types.DocumentProofType {
	switch idType {
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_PASSPORT:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_PASSPORT
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_VOTER_ID:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_VOTER_ID
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_PAN:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_DRIVING_LICENSE:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_DRIVING_LICENSE
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_NREGA:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_NREGA_JOB_CARD
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_NATIONAL_POPULATION_REGISTRY_LETTER:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_NATIONAL_POPULATION_REGISTRY_LETTER
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_EKYC_AUTHENTICATION:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_EKYC_AUTHENTICATION
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_OFFLINE_AADHAAR_VERIFICATION:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_OFFLINE_AADHAAR_VERIFICATION
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_OFFLINE_OTHERS:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_OTHERS
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GOVERNMENT:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GOVERNMENT
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GAZETTED:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GAZETTED
	default:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_UNSPECIFIED
	}
}

func populateSignature(ctx context.Context, details *woPb.OnboardingDetails) {
	docs := details.GetMetadata().GetCkycData().GetDownloadData().GetPersonalDetails().GetDocumentsList()
	if details.GetMetadata().GetPersonalDetails().GetSignature() == nil || len(details.GetMetadata().GetPersonalDetails().GetSignature().GetS3Paths()) == 0 {
		docType := types.DocumentProofType_DOCUMENT_PROOF_TYPE_SIGNATURE
		if helper.IsDocumentStoreAllowed(docType) {
			sig := helper.GetDocumentFromDocumentLists(docs, docType)
			if sig == nil || (len(sig.GetPhoto()) == 0 && len(sig.GetS3Paths()) == 0) {
				logger.Info(ctx, "signature not present")
			} else {
				details.GetMetadata().GetPersonalDetails().Signature = sig
			}
		}
	}
}

//nolint:gosec,deadcode,unused
func generateReqId() string {
	now := time.Now()
	nsOfTheDay := time.Hour*time.Duration(now.Hour()) + time.Minute*time.Duration(now.Minute()) + time.Second*time.Duration(now.Second()) + time.Nanosecond*time.Duration(now.Nanosecond())
	microsecondOfTheDay := int(nsOfTheDay) / int(time.Millisecond)
	// adding a random number to microsecondOfTheDay to avoid collision
	rand.Seed(time.Now().UnixNano())
	microsecondOfTheDay += (rand.Intn(100)) + 1
	return strconv.Itoa(microsecondOfTheDay)
}
