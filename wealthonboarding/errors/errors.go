package errors

import "errors"

var (
	ErrOCRLowConfidence                = errors.New("low OCR confidence score")
	ErrOCRPanNumberMismatch            = errors.New("pan number does not match with ocr res")
	ErrPoaNotAllowed                   = errors.New("poa document type is not allowed")
	ErrImageConversionFailed           = errors.New("error while conversion of image to different format")
	ErrCkycDobMismatch                 = errors.New("dob mismatch error in ckyc download response")
	ErrPoaIdMissing                    = errors.New("poa id is missing")
	ErrIncompleteKraFormDetails        = errors.New("missing mandatory details for KRA form")
	ErrKycNotValidatedYetForAadhaarPoa = errors.New("kyc not validated yet for aadhaar poa")
	ErrKycNotValidatedYet              = errors.New("kyc not validated yet")
	ErrPdfMergeFailed                  = errors.New("error in pdf merging lib")
	ErrKYCOnHoldByKRA                  = errors.New("KYC on hold by KRA")
)
