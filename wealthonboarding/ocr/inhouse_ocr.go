//nolint:dupl
package ocr

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"io"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"

	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/vendorgateway/wealth/inhouseocr"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/integer"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/wealthonboarding/helper"
)

type InhouseOcr struct {
	ocrClient inhouseocr.OcrClient
}

var InhouseOCRWireSet = wire.NewSet(NewInhouseOcr, wire.Bind(new(Ocr), new(*InhouseOcr)))

func NewInhouseOcr(ocrClient inhouseocr.OcrClient) *InhouseOcr {
	return &InhouseOcr{
		ocrClient: ocrClient,
	}
}

func (o *InhouseOcr) getMaskedDocStream(ctx context.Context, doc *types.DocumentProof) (*inhouseocr.GetMaskedDocResponse, error) {
	stream, err := o.ocrClient.GetMaskedDocWithStream(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "error while invoking rpc GetMaskedDocWithStream")
	}

	getMaskedDocRequestObj := &inhouseocr.GetMaskedDocRequest{
		Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_INHOUSE_OCR},
		DocumentProof: doc,
	}

	getMaskedDocRequestObjBytes, err := protojson.Marshal(getMaskedDocRequestObj)
	if err != nil {
		return nil, errors.Wrap(err, "error while marshalling getMaskedDocRequestObj")
	}

	const packetSize = 50 * 1024
	for i := 0; i < len(getMaskedDocRequestObjBytes); i += packetSize {
		sendErr := stream.Send(&inhouseocr.GetMaskedDocWithStreamRequest{
			FileChunk: getMaskedDocRequestObjBytes[i:integer.Min(i+packetSize, len(getMaskedDocRequestObjBytes))],
		})
		if sendErr != nil {
			return nil, errors.Wrap(sendErr, "error while sending bytes in GetMaskedDocWithStream")
		}
	}

	err = stream.CloseSend()
	if err != nil {
		return nil, errors.Wrap(err, "error while closing send request in GetMaskedDocWithStream")
	}

	var maskedDocWithStreamResponseObjByte []byte

	for {
		// Get response and possible error message from the stream
		maskedDocWithStreamRequestObj, recvErr := stream.Recv()

		// Break for loop if there are no more response messages
		if errors.Is(recvErr, io.EOF) {
			logger.Debug(ctx, fmt.Sprint("len of bytes received", len(maskedDocWithStreamResponseObjByte)))
			break
		}

		// Handle a possible error
		if te := epifigrpc.RPCError(maskedDocWithStreamRequestObj, recvErr); te != nil {
			return nil, errors.Wrap(te, "Error when receiving response in GetMaskedDocWithStream")
		}

		maskedDocWithStreamResponseObjByte = append(maskedDocWithStreamResponseObjByte, maskedDocWithStreamRequestObj.GetFileChunk()...)
	}

	getMaskedDocResponseObj := &inhouseocr.GetMaskedDocResponse{}
	err = protojson.Unmarshal(maskedDocWithStreamResponseObjByte, getMaskedDocResponseObj)
	if err != nil {
		return nil, errors.Wrap(err, "error while redacting doc")
	}
	return getMaskedDocResponseObj, nil
}

func (o *InhouseOcr) getExpiryDocStream(ctx context.Context, doc *types.DocumentProof) (*inhouseocr.GetExpiryDocResponse, error) {
	stream, err := o.ocrClient.GetExpiryDocWithStream(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "error while invoking rpc getExpiryDocStream")
	}

	getExpiryDocRequestObj := &inhouseocr.GetExpiryDocRequest{
		Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_INHOUSE_OCR},
		DocumentProof: doc,
	}

	getExpiryDocRequestObjBytes, err := protojson.Marshal(getExpiryDocRequestObj)
	if err != nil {
		return nil, errors.Wrap(err, "error while marshalling getExpiryDocRequestObj")
	}

	const packetSize = 50 * 1024
	for i := 0; i < len(getExpiryDocRequestObjBytes); i += packetSize {
		sendErr := stream.Send(&inhouseocr.GetExpiryDocWithStreamRequest{
			FileChunk: getExpiryDocRequestObjBytes[i:integer.Min(i+packetSize, len(getExpiryDocRequestObjBytes))],
		})
		if sendErr != nil {
			return nil, errors.Wrap(sendErr, "error while sending bytes in GetExpiryDocWithStream")
		}
	}

	err = stream.CloseSend()
	if err != nil {
		return nil, errors.Wrap(err, "error while closing send request in GetExpiryDocWithStream")
	}

	var expiryDocWithStreamResponseObjByte []byte

	for {
		// Get response and possible error message from the stream
		expiryDocWithStreamRequestObj, recvErr := stream.Recv()

		// Break for loop if there are no more response messages
		if errors.Is(recvErr, io.EOF) {
			logger.Debug(ctx, fmt.Sprint("len of bytes received ", len(expiryDocWithStreamResponseObjByte)))
			break
		}

		// Handle a possible error
		if te := epifigrpc.RPCError(expiryDocWithStreamRequestObj, recvErr); te != nil {
			return nil, errors.Wrap(te, "Error when receiving response in GetExpiryDocWithStream")
		}

		expiryDocWithStreamResponseObjByte = append(expiryDocWithStreamResponseObjByte, expiryDocWithStreamRequestObj.GetFileChunk()...)
	}

	getExpiryDocResponseObj := &inhouseocr.GetExpiryDocResponse{}
	err = protojson.Unmarshal(expiryDocWithStreamResponseObjByte, getExpiryDocResponseObj)
	if err != nil {
		return nil, errors.Wrap(err, "error while redacting doc")
	}
	return getExpiryDocResponseObj, nil
}

func (o *InhouseOcr) GetOcrDoc(ctx context.Context, doc *types.DocumentProof, confidenceThresholds map[string]float64) (*woPb.OcrDocumentProof, error) {
	if !helper.IsImagePresent(doc) {
		return nil, errors.New("image not present")
	}
	ocrResDoc, confidenceScore, manualReviewNeeded, err := o.getOCRRes(ctx, doc)
	if err != nil {
		return nil, fmt.Errorf("error getting ocr res: %w", err)
	}
	thresholdScore, ok := confidenceThresholds[doc.GetProofType().String()]
	if !ok {
		return nil, fmt.Errorf("could not find threshold score for document type: %v", doc.GetProofType().String())
	}
	return getOcrDocProof(ocrResDoc, confidenceScore, thresholdScore, manualReviewNeeded), nil
}

func (o *InhouseOcr) GetDocumentWithOCRResults(ctx context.Context, doc *types.DocumentProof) (*woPb.OcrDocumentProof, error) {
	if !helper.IsImagePresent(doc) {
		return nil, errors.New("image not present")
	}
	ocrResDoc, confidenceScore, manualReviewNeeded, err := o.getOCRRes(ctx, doc)
	if err != nil {
		return nil, fmt.Errorf("error getting ocr res: %w", err)
	}
	return getOCRDocProofWithoutThreshold(ocrResDoc, confidenceScore, manualReviewNeeded), nil
}

func (o *InhouseOcr) getOCRRes(ctx context.Context, doc *types.DocumentProof) (*types.DocumentProof, float64, bool, error) {
	var (
		ocrResDoc          *types.DocumentProof
		confidenceScore    float64
		manualReviewNeeded bool
	)
	switch doc.GetProofType() {
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR:
		res, err := o.getMaskedDocStream(ctx, doc)
		if err != nil {
			return nil, 0, false, fmt.Errorf("error getting masked doc stream: %w", err)
		}
		ocrResDoc = res.GetMaskedDocumentProof()
		confidenceScore = res.GetConfidenceScore()
		manualReviewNeeded = res.GetReview()
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_PASSPORT,
		types.DocumentProofType_DOCUMENT_PROOF_TYPE_DRIVING_LICENSE,
		types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN:
		res, err := o.getExpiryDocStream(ctx, doc)
		if err != nil {
			return nil, 0, false, errors.Wrap(err, "error while extracting expiry from doc")
		}
		ocrResDoc = res.GetMaskedDocumentProof()
		confidenceScore = res.GetConfidenceScore()
		manualReviewNeeded = res.GetReview()
	default:
		return nil, 0, false, fmt.Errorf("unhandled document type: %s", doc.GetProofType().String())
	}
	return ocrResDoc, confidenceScore, manualReviewNeeded, nil
}

func getOcrDocProof(docProof *types.DocumentProof, score float64, threshold float64, review bool) *woPb.OcrDocumentProof {
	ocrDocProof := &woPb.OcrDocumentProof{
		Doc:              docProof,
		ConfidenceScore:  score,
		ThresholdScore:   threshold,
		VendorReviewFlag: review,
	}
	switch {
	case score < threshold || review:
		ocrDocProof.Result = woPb.OcrResult_OCR_RESULT_MANUAL_REVIEW
	default:
		ocrDocProof.Result = woPb.OcrResult_OCR_RESULT_PASSED
	}
	return ocrDocProof
}

func getOCRDocProofWithoutThreshold(docProof *types.DocumentProof, score float64, review bool) *woPb.OcrDocumentProof {
	return &woPb.OcrDocumentProof{
		Doc:              docProof,
		ConfidenceScore:  score,
		VendorReviewFlag: review,
	}
}
