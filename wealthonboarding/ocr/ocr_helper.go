package ocr

import (
	"golang.org/x/net/context"

	types "github.com/epifi/gamma/api/typesv2"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
)

//go:generate mockgen -source=./ocr_helper.go -destination=./../test/mocks/ocr/mock_ocr_helper.go -package=mocks
type IOcrHelper interface {
	PopulatePoaWithOcr(ctx context.Context, details *woPb.OnboardingDetails) error

	// FetchPanWithOcr performs OCR on the PAN image
	// Returns errors.ErrOCRLowConfidence if PAN wasn't detected properly in image
	// Returns errors.ErrOCRPanNumberMismatch if PAN number detected by OCR doesn't match with PAN in args
	FetchPanWithOcr(ctx context.Context, details *woPb.OnboardingDetails, pan *types.DocumentProof) (*woPb.OcrDocumentProof, error)
}
