package ocr

import (
	"context"

	types "github.com/epifi/gamma/api/typesv2"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
)

// Ocr interface is used to mask sensitive documents like <PERSON>adhaar
//
//go:generate mockgen -source=./ocr.go -destination=./../test/mocks/ocr/mock_ocr.go -package=mocks
type Ocr interface {
	// GetOcrDoc is used to perform multiple character recognition related work
	// For Aadhaar it returns a masked document
	// For DL and Passport it returns document validity (based on expiry date)
	// For PAN card image it returns DOB and the permanent account number extracted from image
	GetOcrDoc(ctx context.Context, doc *types.DocumentProof, confidenceThresholds map[string]float64) (*woPb.OcrDocumentProof, error)

	// GetDocumentWithOCRResults is similar to GetOcrDoc except it doesn't populate thresholds and results
	// Callers are expected to make decisions on thresholds based on the confidence score returned
	GetDocumentWithOCRResults(ctx context.Context, doc *types.DocumentProof) (*woPb.OcrDocumentProof, error)
}
