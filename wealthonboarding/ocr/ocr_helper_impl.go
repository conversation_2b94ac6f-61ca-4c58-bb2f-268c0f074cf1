package ocr

import (
	"context"
	"fmt"

	types "github.com/epifi/gamma/api/typesv2"

	"github.com/pkg/errors"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/wealthonboarding/config"
	woErr "github.com/epifi/gamma/wealthonboarding/errors"
	"github.com/epifi/gamma/wealthonboarding/helper"
)

type OcrHelperImpl struct {
	docHelper helper.DocumentHelper
	ocr       Ocr
	conf      *config.Config
}

var _ IOcrHelper = &OcrHelperImpl{}

func NewOcrHelper(documentHelper helper.DocumentHelper, ocr Ocr, conf *config.Config) *OcrHelperImpl {
	return &OcrHelperImpl{
		docHelper: documentHelper,
		ocr:       ocr,
		conf:      conf,
	}
}

var OcrDocumentProofTypes = []types.DocumentProofType{
	types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR,
	types.DocumentProofType_DOCUMENT_PROOF_TYPE_DRIVING_LICENSE,
	types.DocumentProofType_DOCUMENT_PROOF_TYPE_PASSPORT,
}

func IsOcrRequired(dpt types.DocumentProofType) bool {
	for _, odt := range OcrDocumentProofTypes {
		if odt == dpt {
			return true
		}
	}
	return false
}

func (o *OcrHelperImpl) PopulatePoaWithOcr(ctx context.Context, details *woPb.OnboardingDetails) error {
	docs := details.GetMetadata().GetCkycData().GetDownloadData().GetPersonalDetails().GetDocumentsList()
	// ConsolidateManualReview step empties the poaWithOcr, hence we need to check whether there is a review attempt created or not
	if details.GetMetadata().PoaDetails != nil || details.GetMetadata().GetPoaWithOcr() != nil || IsReviewAttemptCreated(details) {
		return nil
	}
	poaType := details.GetMetadata().GetCkycData().GetDownloadData().GetPersonalDetails().GetProofOfAddress()
	if !helper.IsDocumentStoreAllowed(poaType) {
		return woErr.ErrPoaNotAllowed
	}
	poa := helper.GetDocumentFromDocumentLists(docs, poaType)
	if poa == nil {
		logger.Info(ctx, fmt.Sprintf("POA not present. Doc type: %v", poaType))
		return nil
	}
	// collect poa without ocr based on proof type
	if !IsOcrRequired(poaType) {
		details.GetMetadata().PoaDetails = poa
		return nil
	}

	// mask or get expiry from doc
	poaB64, err := o.docHelper.DownloadDoc(ctx, poa)
	if err != nil {
		return errors.Wrap(err, "error in downloading doc for ocr")
	}
	ocrDoc, err := o.ocr.GetOcrDoc(ctx, poaB64, o.conf.OCRThresholdScoreConfig)
	if err != nil {
		return errors.Wrap(err, "error in getting OCR doc")
	}
	// upload to s3
	if helper.IsImagePresent(ocrDoc.GetDoc()) {
		docRes, docErr := o.docHelper.UploadDoc(ctx, details.GetActorId(), ocrDoc.GetDoc())
		if docErr != nil {
			return errors.Wrap(docErr, "error while uploading doc to s3")
		}
		ocrDoc.Doc = docRes
	} else {
		ocrDoc.Doc = poa
	}

	switch ocrDoc.Result {
	case woPb.OcrResult_OCR_RESULT_MANUAL_REVIEW:
		logger.Info(ctx, fmt.Sprintf("low confidence in OCR for doc: %v, confidence score: %v, threshold: %v", poaType, ocrDoc.GetConfidenceScore(), ocrDoc.GetThresholdScore()))
		details.GetMetadata().PoaWithOcr = ocrDoc
		return woErr.ErrOCRLowConfidence
	case woPb.OcrResult_OCR_RESULT_PASSED:
		details.GetMetadata().PoaDetails = ocrDoc.GetDoc()
		details.GetMetadata().PoaWithOcr = ocrDoc
	default:
		// do nothing
	}
	return nil
}

func (o *OcrHelperImpl) FetchPanWithOcr(ctx context.Context, details *woPb.OnboardingDetails, pan *types.DocumentProof) (*woPb.OcrDocumentProof, error) {
	// fetch document from s3
	panB64, err := o.docHelper.DownloadDoc(ctx, pan)
	if err != nil {
		return nil, errors.Wrap(err, "error in downloading pan doc for ocr")
	}
	ocrDoc, err := o.ocr.GetOcrDoc(ctx, panB64, o.conf.OCRThresholdScoreConfig)
	if err != nil {
		return nil, errors.Wrap(err, "error in getting pan OCR doc")
	}
	if !helper.IsImagePresent(ocrDoc.GetDoc()) {
		// assumption is that in case of a successful review from OCR service, it will always return an image
		return nil, errors.New("image not present in the PAN OCR doc")
	}
	docRes, docErr := o.docHelper.UploadDoc(ctx, details.GetActorId(), ocrDoc.GetDoc())
	if docErr != nil {
		return nil, errors.Wrap(docErr, "error while uploading doc to s3")
	}
	ocrDoc.Doc = docRes
	if ocrDoc.GetResult() == woPb.OcrResult_OCR_RESULT_MANUAL_REVIEW {
		// ocr service returned with a review status, need to return from here as the PAN will be capture from user now
		logger.Info(ctx, "low confidence as per ocr service response, user collection for pan needs to be done")
		return ocrDoc, woErr.ErrOCRLowConfidence
	}
	if cfg.IsProdEnv(o.conf.Application.Environment) && details.GetMetadata().GetPanDetails().GetId() != ocrDoc.GetDoc().GetId() {
		logger.Info(ctx, "pan number mismatch")
		return ocrDoc, woErr.ErrOCRPanNumberMismatch
	}
	return ocrDoc, nil
}

// IsReviewAttemptCreated returns whether a manual review attempt was created by the CollectMissingDataStep
func IsReviewAttemptCreated(od *woPb.OnboardingDetails) bool {
	rvAttempts := od.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping()
	return len(rvAttempts[woPb.ItemType_ITEM_TYPE_AADHAAR_REDACTION.String()].GetReviewAttempts()) > 0 ||
		len(rvAttempts[woPb.ItemType_ITEM_TYPE_DOCUMENT_EXPIRY.String()].GetReviewAttempts()) > 0
}
