package ocr

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"io"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/vendorgateway/wealth/inhouseocr"
	"github.com/epifi/gamma/api/vendorgateway/wealth/inhouseocr/mocks"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/integer"
)

func TestInhouseOcr_GetOcrDoc(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	streamAadhaarData := []byte(streamAadhaarResp)

	mockOcrClient := mocks.NewMockOcrClient(ctrl)
	c := &InhouseOcr{
		ocrClient: mockOcrClient,
	}

	maskedDocStream := mocks.NewMockOcr_GetMaskedDocWithStreamClient(ctrl)
	maskedDocStream.EXPECT().Send(gomock.Any()).Return(nil).AnyTimes()
	mockOcrClient.EXPECT().GetMaskedDocWithStream(gomock.Any()).Return(maskedDocStream, nil)
	maskedDocStream.EXPECT().CloseSend().Return(nil)

	const packetSize = 50 * 1024
	for i := 0; i < len(streamAadhaarData); i += packetSize {
		maskedDocStream.EXPECT().Recv().Return(&inhouseocr.GetMaskedDocWithStreamResponse{
			Status:    rpc.StatusOk(),
			FileChunk: streamAadhaarData[i:integer.Min(i+packetSize, len(streamAadhaarData))],
		}, nil)
	}
	maskedDocStream.EXPECT().Recv().Return(nil, io.EOF)

	streamPanData := []byte(streamPanResp)
	ExpiryDocStream := mocks.NewMockOcr_GetExpiryDocWithStreamClient(ctrl)
	ExpiryDocStream.EXPECT().Send(gomock.Any()).Return(nil).AnyTimes()
	mockOcrClient.EXPECT().GetExpiryDocWithStream(gomock.Any()).Return(ExpiryDocStream, nil)
	ExpiryDocStream.EXPECT().CloseSend().Return(nil)

	for i := 0; i < len(streamPanData); i += packetSize {
		ExpiryDocStream.EXPECT().Recv().Return(&inhouseocr.GetExpiryDocWithStreamResponse{
			Status:    rpc.StatusOk(),
			FileChunk: streamPanData[i:integer.Min(i+packetSize, len(streamPanData))],
		}, nil)
	}
	ExpiryDocStream.EXPECT().Recv().Return(nil, io.EOF)
	tests := []struct {
		name    string
		args    *types.DocumentProof
		want    *woPb.OcrDocumentProof
		wantErr bool
	}{
		{
			name: "when document type is aadhaar, performed getMaskedDocStream",
			args: &types.DocumentProof{
				ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR,
				Photo: []*commontypes.Image{
					{
						ImageType:       commontypes.ImageType_JPEG,
						ImageDataBase64: base64AadhaarReq,
					},
				},
			},
			want: &woPb.OcrDocumentProof{
				Doc: &types.DocumentProof{
					ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR,
					Photo: []*commontypes.Image{
						{
							ImageType:       commontypes.ImageType_PDF,
							ImageDataBase64: base64AadhaarResp,
						},
					},
				},
				ConfidenceScore:  0,
				ThresholdScore:   100,
				Result:           woPb.OcrResult_OCR_RESULT_MANUAL_REVIEW,
				VendorReviewFlag: true,
			},
			wantErr: false,
		},
		{
			name: "when document type is pan, performed ",
			args: &types.DocumentProof{
				ProofType: 3,
				Photo: []*commontypes.Image{
					{
						ImageType:       commontypes.ImageType_JPEG,
						ImageDataBase64: panDocBase6Req,
					},
				},
			},
			want: &woPb.OcrDocumentProof{
				Doc: &types.DocumentProof{
					ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
					Id:        "**********",
					Photo: []*commontypes.Image{
						{
							ImageType:       commontypes.ImageType_JPEG,
							ImageDataBase64: base64PanResp,
						},
					},
					Dob: &date.Date{
						Year:  1976,
						Month: 6,
						Day:   2,
					},
				},
				ConfidenceScore:  64.48445717493692,
				ThresholdScore:   40,
				Result:           woPb.OcrResult_OCR_RESULT_PASSED,
				VendorReviewFlag: false,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, respErr := c.GetOcrDoc(context.Background(), tt.args, conf.OCRThresholdScoreConfig)
			if (respErr != nil) != tt.wantErr {
				t.Errorf("GetOcrDoc() error = %v, wantErr %v", respErr, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetOcrDoc() got = %v, want %v", got, tt.want)
			}
		})
	}
}
