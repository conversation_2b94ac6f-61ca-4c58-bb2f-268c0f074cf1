package ocr

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	types "github.com/epifi/gamma/api/typesv2"

	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"google.golang.org/genproto/googleapis/type/date"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/logger"
	woErr "github.com/epifi/gamma/wealthonboarding/errors"
	"github.com/epifi/gamma/wealthonboarding/helper"
	mock_helpers "github.com/epifi/gamma/wealthonboarding/test/mocks/helpers"
	mocks "github.com/epifi/gamma/wealthonboarding/test/mocks/ocr"
)

var (
	onboardingDetailsReq = &woPb.OnboardingDetails{
		Id:      "12345",
		ActorId: "AC220525h9NnvYRgQbWSBIl5vqKL9Q==",
		Metadata: &woPb.OnboardingMetadata{
			CkycData: &woPb.CkycData{
				DownloadData: &woPb.CkycDownloadData{
					PersonalDetails: &woPb.CkycPersonalDetails{
						DocumentsList: []*types.DocumentProof{
							{
								ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR,
								Id:        "",
								Photo: []*commontypes.Image{
									{
										ImageType: commontypes.ImageType_PDF,
										ImageUrl:  "http://www.africau.edu/images/default/sample.pdf",
									},
								},
								Expiry: nil,
								S3Paths: []string{
									"converted_doc_proof_image/AC220525h9NnvYRgQbWSBIl5vqKL9Q==/0609f237-bbd3-43a5-93eb-2e91d0a0a772/DOCUMENT_PROOF_TYPE_OTHERS/0.PDF",
								},
							},
						},
						ProofOfAddress: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR,
					},
					IdentityDetails: nil,
				},
			},
			ManualReviewAttempts: &woPb.ManualReviewAttempts{ReviewAttemptMapping: nil},
			PanDetails: &types.DocumentProof{
				ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
				Id:        "1234",
				Photo: []*commontypes.Image{
					{
						ImageType:       commontypes.ImageType_PDF,
						ImageDataBase64: "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",
						ImageUrl:        "",
					},
				},
				Expiry:  nil,
				S3Paths: nil,
			},
		},
	}
	onboardingDetailsReq2 = &woPb.OnboardingDetails{
		Id:      "12345",
		ActorId: "AC220525h9NnvYRgQbWSBIl5vqKL9Q==",
		Metadata: &woPb.OnboardingMetadata{
			CkycData: &woPb.CkycData{
				DownloadData: &woPb.CkycDownloadData{
					PersonalDetails: &woPb.CkycPersonalDetails{
						DocumentsList: []*types.DocumentProof{
							{
								ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR,
								Id:        "",
								Photo: []*commontypes.Image{
									{
										ImageType: commontypes.ImageType_PDF,
										ImageUrl:  "http://www.africau.edu/images/default/sample.pdf",
									},
								},
								Expiry: nil,
								S3Paths: []string{
									"converted_doc_proof_image/AC220525h9NnvYRgQbWSBIl5vqKL9Q==/0609f237-bbd3-43a5-93eb-2e91d0a0a772/DOCUMENT_PROOF_TYPE_OTHERS/0.PDF",
								},
							},
						},
						ProofOfAddress: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR,
					},
					IdentityDetails: nil,
				},
			},
			ManualReviewAttempts: &woPb.ManualReviewAttempts{ReviewAttemptMapping: nil},
		},
	}
	documentProofObj = &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR,
		Id:        "",
		Photo: []*commontypes.Image{
			{
				ImageType:       commontypes.ImageType_PDF,
				ImageDataBase64: "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",
			},
		},
	}
	documentProofForPan = &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
		Id:        "",
		Photo: []*commontypes.Image{
			{
				ImageType:       commontypes.ImageType_JPEG,
				ImageDataBase64: "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",
			},
		},
		Expiry: &date.Date{
			Year:  2025,
			Month: 12,
			Day:   30,
		},
		S3Paths: []string{
			"converted_doc_proof_image/AC220525h9NnvYRgQbWSBIl5vqKL9Q==/0609f237-bbd3-43a5-93eb-2e91d0a0a772/DOCUMENT_PROOF_TYPE_OTHERS/0.PDF",
		},
	}
	ocrDocumentProofObj = &woPb.OcrDocumentProof{
		Doc: &types.DocumentProof{
			ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR,
			Id:        "",
			Photo:     nil,
			Expiry:    nil,
			S3Paths: []string{
				"converted_aadhaar_doc_image/AC220525h9NnvYRgQbWSBIl5vqKL9Q==/6b548193-4537-4720-9e1c-e1a83e70b215/DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR/0.PDF",
			},
		},
		ConfidenceScore: 0,
		ThresholdScore:  100,
		Result:          woPb.OcrResult_OCR_RESULT_PASSED,
	}
	ocrDocumentProofObj2 = &woPb.OcrDocumentProof{
		Doc: &types.DocumentProof{
			ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR,
			Id:        "",
			Photo:     nil,
			Expiry:    nil,
			S3Paths: []string{
				"converted_aadhaar_doc_image/AC220525h9NnvYRgQbWSBIl5vqKL9Q==/6b548193-4537-4720-9e1c-e1a83e70b215/DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR/0.PDF",
			},
		},
		ConfidenceScore: 0,
		ThresholdScore:  100,
		Result:          woPb.OcrResult_OCR_RESULT_MANUAL_REVIEW,
	}
	documentProofObj2 = &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
		Id:        "",
		Photo: []*commontypes.Image{
			{
				ImageType:       commontypes.ImageType_PDF,
				ImageDataBase64: "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",
			},
		},
		Expiry: &date.Date{
			Year:  2025,
			Month: 12,
			Day:   30,
		},
		S3Paths: nil,
	}
	ocrDocumentProofObj3 = &woPb.OcrDocumentProof{
		Doc: &types.DocumentProof{
			ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
			Id:        "1234",
			Photo: []*commontypes.Image{
				{
					ImageType:       commontypes.ImageType_PDF,
					ImageDataBase64: "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",
					ImageUrl:        "",
				},
			},
			Expiry:  nil,
			S3Paths: nil,
		},
		ConfidenceScore: 0,
		ThresholdScore:  65,
		Result:          woPb.OcrResult_OCR_RESULT_PASSED,
	}
	documentProofForPan2 = &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
		Id:        "",
		Photo:     nil,
		Expiry:    nil,
		S3Paths: []string{
			"converted_aadhaar_doc_image/AC220525h9NnvYRgQbWSBIl5vqKL9Q==/6b548193-4537-4720-9e1c-e1a83e70b215/DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR/0.PDF",
		},
	}
	ocrDocumentProofObj4 = &woPb.OcrDocumentProof{
		Doc: &types.DocumentProof{
			ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
			Id:        "",
			Photo: []*commontypes.Image{
				{
					ImageType:       commontypes.ImageType_PDF,
					ImageDataBase64: "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",
					ImageUrl:        "",
				},
			},
			Expiry:  nil,
			S3Paths: nil,
		},
		ConfidenceScore: 0,
		ThresholdScore:  65,
		Result:          woPb.OcrResult_OCR_RESULT_MANUAL_REVIEW,
	}
)

func TestOcrHelperImpl_PopulatePoaWithOcr(t *testing.T) {
	ctr := gomock.NewController(t)

	ocrMock := mocks.NewMockOcr(ctr)
	docHelperMock := mock_helpers.NewMockDocumentHelper(ctr)

	type fields struct {
		docHelper helper.DocumentHelper
		ocr       Ocr
	}
	type args struct {
		ctx     context.Context
		details *woPb.OnboardingDetails
		mock    []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
		err     error
	}{
		{
			name: "TestCase for valid user info and document",
			fields: fields{
				docHelper: docHelperMock,
				ocr:       ocrMock,
			},
			args: args{
				ctx:     context.Background(),
				details: onboardingDetailsReq,
				mock: []interface{}{
					docHelperMock.EXPECT().DownloadDoc(gomock.Any(), gomock.Any()).Return(documentProofObj, nil),
					ocrMock.EXPECT().GetOcrDoc(gomock.Any(), gomock.Any(), gomock.Any()).Return(ocrDocumentProofObj, nil),
				},
			},
			wantErr: false,
		},
		{
			name: "TestCase for when ocr result is OcrResult_OCR_RESULT_MANUAL_REVIEW",
			fields: fields{
				docHelper: docHelperMock,
				ocr:       ocrMock,
			},
			args: args{
				ctx:     context.Background(),
				details: onboardingDetailsReq2,
				mock: []interface{}{
					docHelperMock.EXPECT().DownloadDoc(gomock.Any(), gomock.Any()).Return(documentProofObj, nil),
					ocrMock.EXPECT().GetOcrDoc(gomock.Any(), gomock.Any(), gomock.Any()).Return(ocrDocumentProofObj2, nil),
				},
			},
			wantErr: true,
			err:     woErr.ErrOCRLowConfidence,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup logger
			logger.Init(conf.Application.Environment)
			o := &OcrHelperImpl{
				docHelper: tt.fields.docHelper,
				ocr:       tt.fields.ocr,
				conf:      conf,
			}

			err := o.PopulatePoaWithOcr(tt.args.ctx, tt.args.details)
			if (err != nil) != tt.wantErr {
				t.Errorf("PopulatePoaWithOcr() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err != nil && err.Error() != tt.err.Error() {
				t.Errorf("Error don't match, got = %v, want %v", err, tt.err)
				return
			}
		})
	}
}

func TestOcrHelperImpl_FetchPanWithOcr(t *testing.T) {
	ctr := gomock.NewController(t)

	ocrMock := mocks.NewMockOcr(ctr)
	docHelperMock := mock_helpers.NewMockDocumentHelper(ctr)

	type fields struct {
		docHelper helper.DocumentHelper
		ocr       Ocr
	}
	type args struct {
		ctx     context.Context
		details *woPb.OnboardingDetails
		pan     *types.DocumentProof
		mock    []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *woPb.OcrDocumentProof
		wantErr bool
		err     error
	}{
		{
			name: "TestCase for valid pan details and s3Path",
			fields: fields{
				docHelper: docHelperMock,
				ocr:       ocrMock,
			},
			args: args{
				ctx:     context.Background(),
				details: onboardingDetailsReq,
				pan:     documentProofForPan,
				mock: []interface{}{
					docHelperMock.EXPECT().DownloadDoc(gomock.Any(), gomock.Any()).Return(documentProofObj2, nil),
					ocrMock.EXPECT().GetOcrDoc(gomock.Any(), gomock.Any(), gomock.Any()).Return(ocrDocumentProofObj3, nil),
					docHelperMock.EXPECT().UploadDoc(gomock.Any(), gomock.Any(), gomock.Any()).Return(documentProofForPan2, nil),
				},
			},
			want: &woPb.OcrDocumentProof{
				Doc: &types.DocumentProof{
					ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
					Id:        "",
					Photo:     nil,
					Expiry:    nil,
					S3Paths: []string{
						"converted_aadhaar_doc_image/AC220525h9NnvYRgQbWSBIl5vqKL9Q==/6b548193-4537-4720-9e1c-e1a83e70b215/DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR/0.PDF",
					},
				},
				ConfidenceScore: 0,
				ThresholdScore:  65,
				Result:          woPb.OcrResult_OCR_RESULT_PASSED,
			},
			wantErr: false,
		},
		// temporarily disabling until simulator data is found for a positive result
		/*
			{
				name: "TestCase for low OCR confidence score",
				fields: fields{
					docHelper: docHelperMock,
					ocr:       ocrMock,
				},
				args: args{
					ctx:     context.Background(),
					details: onboardingDetailsReq,
					pan:     documentProofForPan,
					mock: []interface{}{
						docHelperMock.EXPECT().DownloadDoc(gomock.Any(), gomock.Any()).Return(documentProofObj2, nil),
						ocrMock.EXPECT().GetOcrDoc(gomock.Any(), gomock.Any()).Return(ocrDocumentProofObj4, nil),
						docHelperMock.EXPECT().UploadDoc(gomock.Any(), gomock.Any(), gomock.Any()).Return(documentProofForPan2, nil),
					},
				},
				want: &woPb.OcrDocumentProof{
					Doc: &types.DocumentProof{
						ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
						Id:        "",
						Photo:     nil,
						Expiry:    nil,
						S3Paths: []string{
							"converted_aadhaar_doc_image/AC220525h9NnvYRgQbWSBIl5vqKL9Q==/6b548193-4537-4720-9e1c-e1a83e70b215/DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR/0.PDF",
						},
					},
					ConfidenceScore: 0,
					ThresholdScore:  65,
					Result:          woPb.OcrResult_OCR_RESULT_PASSED,
				},
				wantErr: true,
				err:     woErr.ErrOCRLowConfidence,
			},
		*/
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OcrHelperImpl{
				docHelper: tt.fields.docHelper,
				ocr:       tt.fields.ocr,
				conf:      conf,
			}
			got, err := o.FetchPanWithOcr(tt.args.ctx, tt.args.details, tt.args.pan)
			if (err != nil) != tt.wantErr {
				t.Errorf("FetchPanWithOcr() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
				if !reflect.DeepEqual(tt.want, got) {
					t.Errorf("FetchPanWithOcr() got = %v, want %v", got, tt.want)
				}
			} else if err.Error() != tt.err.Error() {
				t.Errorf("Error don't match, got = %v, want %v", err, tt.err)
				return
			}
		})
	}
}
