//go:generate mockgen -source=./esign.go -destination=../test/mocks/esign/mock_esign.go -package=mocks
package esign

import (
	"context"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
)

type ESign interface {
	// InitiateDocSign starts the eSign flow for a document, it returns an internal id (eSignId) for the doc which is to be signed
	InitiateDocSign(ctx context.Context, req *woPb.InitiateDocSignRequest) (string, error)
	// GetSignUrl returns the url using which the user will sign the document
	GetSignUrl(ctx context.Context, eSignId string) (string, error)
	// GetLatestESignDetails returns the current txn state of the signing of document, it could be either PENDING, SIGNED or COMPLETED
	GetLatestESignDetails(ctx context.Context, eSignId string) (*woPb.ESignDetails, error)
	// GetSignedDocS3Path GetSignedDoc returns the signed document as document proof
	GetSignedDocS3Path(ctx context.Context, eSignId string) (string, error)
	// IsvalidEsign checks whether the esign is blank or is older than 365 days
	IsValidEsign(ctx context.Context, esignId string) (bool, error)
}

type ESignProvider interface {
	// CreateTransaction initiates a transaction at the vendor end
	CreateTransaction(ctx context.Context, request *woPb.CreateTransactionRequest) (string, string, error)
	// GetSignUrl returns the url using which the user will sign the document
	GetSignUrl(ctx context.Context, documentId, identifier string) (string, error)
	// GetTxnStatus returns the current txn state of the signing of document
	GetTxnStatus(ctx context.Context, transactionId, documentId string) (woPb.TxnState, error)
	// DownloadDocument returns the signed document in byte array format
	DownloadDocument(ctx context.Context, transactionId, documentId string) ([]byte, error)
}
