package esign

import (
	"fmt"

	"github.com/pkg/errors"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
)

type ESignProviderService struct {
	manchService ESignManch
	digioService ESignDigio
}

func NewESignProviderService(manchService ESignManch, digioService ESignDigio) *ESignProviderService {
	return &ESignProviderService{
		digioService: digioService,
		manchService: manchService,
	}
}

func (e *ESignProviderService) GetProviderImpl(vendor woPb.Vendor) (ESignProvider, error) {
	switch vendor {
	case woPb.Vendor_VENDOR_MANCH:
		return e.manchService, nil
	case woPb.Vendor_VENDOR_DIGIO:
		return e.digioService, nil
	default:
		return nil, errors.New(fmt.Sprintf("eSignProvider not supported %v", vendor.String()))
	}
}
