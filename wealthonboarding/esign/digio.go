package esign

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/pkg/errors"

	digioVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/digio"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/gamma/wealthonboarding/config"
)

type ESignDigio ESignProvider

var _ ESignDigio = &DigioService{}

type DigioService struct {
	digioVgClient digioVgPb.DigioClient
	conf          *config.Config
}

func NewDigioService(digioVgClient digioVgPb.DigioClient, conf *config.Config) *DigioService {
	return &DigioService{
		digioVgClient: digioVgClient,
		conf:          conf,
	}
}

var _ ESignProvider = &DigioService{}

func (d *DigioService) CreateTransaction(ctx context.Context, request *woPb.CreateTransactionRequest) (string, string, error) {
	cTxnRes, cTxnErr := d.digioVgClient.CreateTransaction(ctx, &digioVgPb.CreateTransactionRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_DIGIO,
		},
		Identifier:   request.GetIdentifier(),
		DocumentData: request.GetDocBase_64(),
	})
	if te := epifigrpc.RPCError(cTxnRes, cTxnErr); te != nil {
		return "", "", errors.Wrap(te, "error while calling digio vendor for creating transaction")
	}
	// since digio does not generated a txn_id for a create transaction request, we are creating a dummy id
	txnId := uuid.New().String()
	docId := cTxnRes.GetDocumentId()
	return txnId, docId, nil
}

func (d *DigioService) GetSignUrl(_ context.Context, documentId, identifier string) (string, error) {
	baseUrl := d.conf.Digio.BaseSignPath
	// random string as req_id
	reqId := uuid.New().String()
	return fmt.Sprintf("%v/%v/%v/%v", baseUrl, documentId, reqId, identifier), nil
}

func (d *DigioService) GetTxnStatus(ctx context.Context, _, documentId string) (woPb.TxnState, error) {
	stateVgRes, stateVgErr := d.digioVgClient.GetDocumentDetails(ctx, &digioVgPb.GetDocumentDetailsRequest{
		Header:     &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_DIGIO},
		DocumentId: documentId,
	})
	if te := epifigrpc.RPCError(stateVgRes, stateVgErr); te != nil {
		return woPb.TxnState_TXN_STATE_TYPE_UNSPECIFIED, errors.Wrap(te, "error getting txn state from manch vendor")
	}
	currTxnState := d.convertToBeTxnState(stateVgRes.GetTxnState())
	return currTxnState, nil
}

func (d *DigioService) DownloadDocument(ctx context.Context, _, documentId string) ([]byte, error) {
	docId := documentId
	// fetch signed document content from docUrl
	downloadRes, downloadErr := d.digioVgClient.DownloadDocument(ctx, &digioVgPb.DownloadDocumentRequest{
		Header:     &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_DIGIO},
		DocumentId: docId,
	})
	if te := epifigrpc.RPCError(downloadRes, downloadErr); te != nil {
		return nil, te
	}
	return downloadRes.GetSignedPdf(), nil
}

func (d *DigioService) convertToBeTxnState(txnState digioVgPb.TxnState) woPb.TxnState {
	switch txnState {
	case digioVgPb.TxnState_TXN_STATE_PENDING:
		return woPb.TxnState_TXN_STATE_PENDING
	case digioVgPb.TxnState_TXN_STATE_COMPLETED:
		return woPb.TxnState_TXN_STATE_SIGNED
	default:
		return woPb.TxnState_TXN_STATE_TYPE_UNSPECIFIED
	}
}
