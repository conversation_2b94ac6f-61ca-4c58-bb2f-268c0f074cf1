package esign

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"strconv"
	"strings"

	"github.com/pkg/errors"

	manchVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/manch"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

const (
	SignMethod   = "OTP"
	DocumentType = "Agreement"
)

type ManchService struct {
	manchVgClient manchVgPb.ManchClient
}

type ESignManch ESignProvider

var _ ESignManch = &ManchService{}

func NewManchService(manchVgClient manchVgPb.ManchClient) *ManchService {
	return &ManchService{
		manchVgClient: manchVgClient,
	}
}

var _ ESignProvider = &ManchService{}

func (m *ManchService) CreateTransaction(ctx context.Context, request *woPb.CreateTransactionRequest) (string, string, error) {
	if request.GetName().GetFirstName() == "" && request.GetName().GetLastName() == "" {
		return "", "", errors.New("first name and last name are empty")
	}
	name := request.GetName()
	// first name is a mandatory parameter for manch request
	if request.GetName().GetFirstName() == "" {
		name = &commontypes.Name{
			FirstName: request.GetName().GetLastName(),
		}
	}
	cTxnRes, cTxnErr := m.manchVgClient.CreateTransaction(ctx, &manchVgPb.CreateTransactionRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_MANCH,
		},
		FirstName: strings.Title(strings.ToLower(name.GetFirstName())),
		LastName:  strings.Title(strings.ToLower(name.GetLastName())),
		Documents: []*manchVgPb.Document{
			{
				DocumentType:  DocumentType,
				DocumentUrl:   request.GetDocUrl(),
				DocumentBytes: request.GetDocBase_64(),
			},
		},
		ESignMethod: SignMethod,
		TemplateKey: request.GetTemplateKey(),
	})
	if te := epifigrpc.IsRPCErrorWithDowntime(cTxnRes, cTxnErr); te != nil {
		return "", "", errors.Wrap(te, "error while calling manch vendor for creating transaction")
	}

	// check if the documents list returned by manch is not empty
	if len(cTxnRes.GetDocuments()) == 0 {
		return "", "", errors.New("vg response has no documents present")
	}

	txnId := strconv.FormatInt(cTxnRes.GetTransaction().GetTransactionId(), 10)
	docId := strconv.FormatInt(cTxnRes.GetDocuments()[0].GetDocumentId(), 10)
	return txnId, docId, nil
}

func (m *ManchService) GetSignUrl(ctx context.Context, documentId, _ string) (string, error) {
	// fetch sign url from manch vendor
	docId, docIdErr := strconv.ParseInt(documentId, 10, 64)
	if docIdErr != nil {
		return "", errors.Wrap(docIdErr, fmt.Sprintf("error parsing docId %v", documentId))
	}

	signVgRes, signVgErr := m.manchVgClient.GetSignUrl(ctx, &manchVgPb.GetSignUrlRequest{
		Header:     &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_MANCH},
		DocumentId: docId,
	})
	if te := epifigrpc.IsRPCErrorWithDowntime(signVgRes, signVgErr); te != nil {
		return "", errors.Wrap(te, "error while getting sign url from manch vg")
	}
	return signVgRes.GetSignUrl(), nil
}

func (m *ManchService) GetTxnStatus(ctx context.Context, transactionId, _ string) (woPb.TxnState, error) {
	txnId, txnIdErr := strconv.ParseInt(transactionId, 10, 64)
	if txnIdErr != nil {
		return woPb.TxnState_TXN_STATE_TYPE_UNSPECIFIED, errors.Wrap(txnIdErr, fmt.Sprintf("error parsing txnId %v", transactionId))
	}

	stateVgRes, stateVgErr := m.manchVgClient.GetTxnStatus(ctx, &manchVgPb.GetTxnStatusRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_MANCH},
		TxnId:  txnId,
	})
	if te := epifigrpc.IsRPCErrorWithDowntime(stateVgRes, stateVgErr); te != nil {
		return woPb.TxnState_TXN_STATE_TYPE_UNSPECIFIED, errors.Wrap(te, "error getting txn state from manch vendor")
	}
	currTxnState := m.convertToBeTxnState(stateVgRes.GetTxnState())
	return currTxnState, nil
}

func (m *ManchService) DownloadDocument(ctx context.Context, transactionId, _ string) ([]byte, error) {
	// fetch docs for the corresponding transaction created
	txnId, txnIdErr := strconv.ParseInt(transactionId, 10, 64)
	if txnIdErr != nil {
		return nil, txnIdErr
	}
	allDocsRes, allDocsErr := m.manchVgClient.GetAllDocsOfTxn(ctx, &manchVgPb.GetAllDocsOfTxnRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_MANCH},
		TxnId:  txnId,
	})
	if te := epifigrpc.IsRPCErrorWithDowntime(allDocsRes, allDocsErr); te != nil {
		return nil, te
	}
	if len(allDocsRes.GetDocuments()) == 0 {
		return nil, errors.New(fmt.Sprintf("no documents found for the txn_id %v", txnId))
	}
	docId := allDocsRes.GetDocuments()[0].GetDocumentId()
	// fetch signed document content from docUrl
	downloadRes, downloadErr := m.manchVgClient.DownloadDocument(ctx, &manchVgPb.DownloadDocumentRequest{
		Header:     &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_MANCH},
		DocumentId: docId,
	})
	if te := epifigrpc.IsRPCErrorWithDowntime(downloadRes, downloadErr); te != nil {
		return nil, te
	}
	return downloadRes.GetSignedPdf(), nil
}

func (m *ManchService) convertToBeTxnState(txnState manchVgPb.TxnState) woPb.TxnState {
	switch txnState {
	case manchVgPb.TxnState_TXN_STATE_PENDING:
		return woPb.TxnState_TXN_STATE_PENDING
	case manchVgPb.TxnState_TXN_STATE_COMPLETED:
		return woPb.TxnState_TXN_STATE_SIGNED
	default:
		return woPb.TxnState_TXN_STATE_TYPE_UNSPECIFIED
	}
}
