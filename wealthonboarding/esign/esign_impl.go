package esign

import (
	"context"
	"fmt"
	"path/filepath"
	"time"

	"github.com/epifi/be-common/pkg/async/goroutine"

	woTypes "github.com/epifi/gamma/wealthonboarding/types"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	s3types "github.com/aws/aws-sdk-go-v2/service/s3/types"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/logger"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woConsumerPb "github.com/epifi/gamma/api/wealthonboarding/consumer"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/dao"

	"github.com/pkg/errors"
	"go.uber.org/zap"
)

const (
	s3PathPrefix = "e_signed_documents/"
)

type ESignService struct {
	eSignDetailsDao      dao.ESignDetailsDao
	eSignProviderService *ESignProviderService
	updateTxnPublisher   woTypes.RefreshFaceMatchStatusSqsPublisher
	conf                 *config.Config
	s3Client             s3.S3Client
}

func NewESignService(
	eSignProviderService *ESignProviderService,
	detailsDao dao.ESignDetailsDao,
	updateTxnPublisher woTypes.RefreshFaceMatchStatusSqsPublisher,
	conf *config.Config, s3Client s3.S3Client) *ESignService {
	return &ESignService{
		eSignProviderService: eSignProviderService,
		eSignDetailsDao:      detailsDao,
		updateTxnPublisher:   updateTxnPublisher,
		conf:                 conf,
		s3Client:             s3Client,
	}
}

var _ ESign = &ESignService{}

func (e *ESignService) InitiateDocSign(ctx context.Context, req *woPb.InitiateDocSignRequest) (string, error) {
	if req.GetDocumentBytes() == "" && req.GetDocumentUrl() == "" {
		return "", errors.New("invalid request, document data missing")
	}
	vendor := getVendorProvider(req)
	provider, err := e.eSignProviderService.GetProviderImpl(vendor)
	if err != nil {
		return "", errors.Wrap(err, "could not map provider")
	}

	txnId, docId, err := provider.CreateTransaction(ctx, &woPb.CreateTransactionRequest{
		DocBase_64:  req.GetDocumentBytes(),
		DocUrl:      req.GetDocumentUrl(),
		Identifier:  req.GetESignParams().GetIdentifier(),
		Name:        req.GetAadhaarESignParams().GetName(),
		TemplateKey: req.GetTemplateKey(),
	})
	if err != nil {
		return "", errors.Wrap(err, "error while creating transaction")
	}

	eSignDetails := &woPb.ESignDetails{
		TxnId:    txnId,
		DocId:    docId,
		TxnState: woPb.TxnState_TXN_STATE_PENDING,
		Vendor:   vendor,
		Metadata: &woPb.Metadata{Identifier: req.GetESignParams().GetIdentifier()},
	}
	// store transaction_id in db
	dbRes, dbErr := e.eSignDetailsDao.Create(ctx, eSignDetails)
	if dbErr != nil {
		return "", errors.Wrap(dbErr, "error while creating e-sign details for document in db")
	}

	// return primary id
	return dbRes.GetId(), nil
}

func (e *ESignService) GetSignUrl(ctx context.Context, eSignId string) (string, error) {
	eSignDetailRes, eSignDetailErr := e.eSignDetailsDao.Get(ctx, eSignId)
	if eSignDetailErr != nil {
		return "", errors.Wrap(eSignDetailErr, "error querying esign details using id")
	}
	// check if we have sign URL already for the eSignId
	if eSignDetailRes.GetSignUrl() != "" {
		return eSignDetailRes.GetSignUrl(), nil
	}
	provider, err := e.eSignProviderService.GetProviderImpl(eSignDetailRes.GetVendor())
	if err != nil {
		return "", errors.Wrap(err, "could not map provider")
	}
	signUrl, err := provider.GetSignUrl(ctx, eSignDetailRes.GetDocId(), eSignDetailRes.GetMetadata().GetIdentifier())
	if err != nil {
		return "", errors.Wrap(err, "could not fetch sign url from vendor")
	}
	// update the current txn state in db
	eSignDetailRes.SignUrl = signUrl
	updateErr := e.eSignDetailsDao.UpdateById(ctx, eSignDetailRes, []woPb.ESignDetailsFieldMask{woPb.ESignDetailsFieldMask_E_SIGN_DETAILS_FIELD_MASK_SIGN_URL})
	if updateErr != nil {
		return "", errors.Wrap(updateErr, "error while updating sign url in db")
	}
	return eSignDetailRes.GetSignUrl(), nil
}

func (e *ESignService) GetLatestESignDetails(ctx context.Context, eSignId string) (*woPb.ESignDetails, error) {
	txnRes, err := e.refreshTxnState(ctx, eSignId)
	if err != nil {
		return txnRes, err
	}
	return txnRes, nil
}

func (e *ESignService) GetSignedDocS3Path(ctx context.Context, eSignId string) (string, error) {
	eSignDetailRes, eSignDetailErr := e.eSignDetailsDao.Get(ctx, eSignId)
	if eSignDetailErr != nil {
		return "", errors.Wrap(eSignDetailErr, "error querying esign details using id")
	}
	if eSignDetailRes.GetTxnState() != woPb.TxnState_TXN_STATE_COMPLETED {
		return "", errors.New(fmt.Sprintf("state is not completed %v", eSignDetailRes.GetTxnState()))
	}
	return eSignDetailRes.GetStoredS3Path(), nil
}

func (e *ESignService) refreshTxnState(ctx context.Context, eSignId string) (*woPb.ESignDetails, error) {

	res, err := e.eSignDetailsDao.Get(ctx, eSignId)
	if err != nil {
		res.TxnState = woPb.TxnState_TXN_STATE_TYPE_UNSPECIFIED
		return res, errors.Wrap(err, "error querying esign details using id")
	}
	// check if current state is terminal
	if res.GetTxnState() == woPb.TxnState_TXN_STATE_COMPLETED {
		return res, nil
	}

	provider, err := e.eSignProviderService.GetProviderImpl(res.GetVendor())
	if err != nil {
		return res, errors.Wrap(err, "could not map provider")
	}

	currTxnState, err := provider.GetTxnStatus(ctx, res.GetTxnId(), res.GetDocId())
	if err != nil {
		res.TxnState = woPb.TxnState_TXN_STATE_TYPE_UNSPECIFIED
		return res, errors.Wrap(err, "could not fetch status from vendor")
	}

	// if the current vendor state is the same as in db, no need to update
	if currTxnState == res.GetTxnState() && res.GetStoredS3Path() != "" {
		return res, nil
	}
	res.TxnState = currTxnState

	// download the document async
	if currTxnState == woPb.TxnState_TXN_STATE_SIGNED {
		goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
			newCtx, cancelFunc := context.WithTimeout(context.Background(), time.Second*100)
			defer cancelFunc()
			isPermanentFailure, dsuErr := e.DownloadSignedDocAndUpdateTxnState(newCtx, &woConsumerPb.UpdateTxnStateRequest{
				ESignId: res.GetId(),
			})
			if dsuErr != nil {
				logger.Error(ctx, "failed to DownloadSignedDocAndUpdateTxnState", zap.Error(dsuErr), zap.String("ID", res.GetId()), zap.Bool("isPermanentFailure", isPermanentFailure))
			}
			logger.Info(ctx, "DownloadSignedDocAndUpdateTxnState done", zap.String("ID", res.GetId()))
		})
	}
	updateErr := e.eSignDetailsDao.UpdateById(ctx, res, []woPb.ESignDetailsFieldMask{woPb.ESignDetailsFieldMask_E_SIGN_DETAILS_FIELD_MASK_TXN_STATE})
	if updateErr != nil {
		res.TxnState = woPb.TxnState_TXN_STATE_TYPE_UNSPECIFIED
		return res, errors.Wrap(updateErr, "error updating txn state in db")
	}
	return res, nil
}

func getVendorProvider(req *woPb.InitiateDocSignRequest) woPb.Vendor {
	switch req.Params.(type) {
	case *woPb.InitiateDocSignRequest_AadhaarESignParams:
		return woPb.Vendor_VENDOR_MANCH
	case *woPb.InitiateDocSignRequest_ESignParams:
		return woPb.Vendor_VENDOR_DIGIO
	default:
		return woPb.Vendor_VENDOR_TYPE_UNSPECIFIED
	}
}

func (e *ESignService) DownloadSignedDocAndUpdateTxnState(ctx context.Context, request *woConsumerPb.UpdateTxnStateRequest) (bool, error) {
	time.Sleep(time.Second * 2)
	eSignId := request.GetESignId()
	if eSignId == "" {
		return true, errors.New("empty eSignId in consumer")
	}
	// user has signed the document, fetch the details from db
	eSignDetails, err := e.eSignDetailsDao.Get(ctx, eSignId)
	if err != nil {
		return false, errors.Wrap(err, "error while fetching esign details from db")
	}
	// if current state is COMPLETED, return success for packet
	if eSignDetails.GetTxnState() == woPb.TxnState_TXN_STATE_COMPLETED {
		return false, nil
	}
	// get provider for current e-sign details
	provider, err := e.eSignProviderService.GetProviderImpl(eSignDetails.GetVendor())
	if err != nil {
		return true, errors.Wrap(err, "failed to get esign provider")
	}
	// download document from provider
	signedPdf, err := provider.DownloadDocument(ctx, eSignDetails.GetTxnId(), eSignDetails.GetDocId())
	if err != nil {
		return false, errors.Wrap(err, "failed to download signed content")
	}
	// store the document response from vendor in s3
	fileName := fmt.Sprintf("%v.pdf", eSignDetails.GetId())
	dst := filepath.Join(s3PathPrefix, fileName)
	// TODO(ismail): confirm on aclString with Vikas
	s3Err := e.s3Client.Write(ctx, dst, signedPdf, string(s3types.ObjectCannedACLBucketOwnerFullControl))
	if s3Err != nil {
		return false, errors.Wrap(s3Err, "error while storing to s3")
	}
	// update the destination path of document stored in db and mark it as completed state
	eSignDetails.StoredS3Path = dst
	eSignDetails.TxnState = woPb.TxnState_TXN_STATE_COMPLETED
	eSignDetails.SignedAt = timestampPb.Now()
	err = e.eSignDetailsDao.UpdateById(ctx, eSignDetails, []woPb.ESignDetailsFieldMask{
		woPb.ESignDetailsFieldMask_E_SIGN_DETAILS_FIELD_MASK_STORED_S3_PATH,
		woPb.ESignDetailsFieldMask_E_SIGN_DETAILS_FIELD_MASK_TXN_STATE,
		woPb.ESignDetailsFieldMask_E_SIGN_DETAILS_FIELD_MASK_SIGNED_AT,
	})
	if err != nil {
		return false, errors.Wrap(err, "error while updating storedS3 path")
	}
	return false, nil
}

func (e *ESignService) IsValidEsign(ctx context.Context, esignId string) (bool, error) {
	if esignId == "" {
		return false, nil
	}
	res, err := e.GetLatestESignDetails(ctx, esignId)
	if err != nil {
		return false, errors.Wrap(err, "error in getting esign transaction state")
	}
	// esign url is valid till 365 days, if its older than 365 days we return fail
	if res.GetCreatedAt().AsTime().Before(time.Now().Add(-24 * 365 * time.Hour)) {
		return false, nil
	}
	return true, nil
}
