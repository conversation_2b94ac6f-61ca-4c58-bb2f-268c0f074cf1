package esign

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	mocks2 "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"

	"github.com/epifi/gamma/api/vendorgateway/wealth/manch"
	mocks3 "github.com/epifi/gamma/api/vendorgateway/wealth/manch/mocks"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/config"
	dao2 "github.com/epifi/gamma/wealthonboarding/dao"
	mockdao "github.com/epifi/gamma/wealthonboarding/test/mocks/dao"
	mocks "github.com/epifi/gamma/wealthonboarding/test/mocks/esign"
)

func Test_InitiateDocSign(t *testing.T) {
	ctr := gomock.NewController(t)

	conf, _ := config.Load()
	type fields struct {
		eSignDetailsDaoMock dao2.ESignDetailsDao
	}
	esigndetailsdaomock := mockdao.NewMockESignDetailsDao(ctr)
	manchMock := mocks.NewMockESignProvider(ctr)
	digioMock := mocks.NewMockESignProvider(ctr)

	s := &ESignService{
		eSignDetailsDao:      esigndetailsdaomock,
		eSignProviderService: NewESignProviderService(manchMock, digioMock),
		conf:                 conf,
		s3Client:             nil,
	}

	req1 := &woPb.InitiateDocSignRequest{
		Params: &woPb.InitiateDocSignRequest_AadhaarESignParams{
			AadhaarESignParams: &woPb.AadhaarESignParams{Name: &commontypes.Name{
				FirstName: "ram",
				LastName:  "gupta",
			}},
		},
		DocumentUrl: "http://www.africau.edu/images/default/sample.pdf",
	}
	req2 := &woPb.InitiateDocSignRequest{
		Params: &woPb.InitiateDocSignRequest_AadhaarESignParams{
			AadhaarESignParams: &woPb.AadhaarESignParams{Name: &commontypes.Name{
				FirstName: "ram",
				LastName:  "gupta",
			}},
		},
		DocumentUrl: "http://www.africau.edu/images.pdf",
	}
	eSignDetailRes := &woPb.ESignDetails{
		Id:       "12345",
		TxnId:    "141415",
		DocId:    "262133",
		TxnState: woPb.TxnState_TXN_STATE_PENDING,
		Vendor:   woPb.Vendor_VENDOR_MANCH,
		Metadata: &woPb.Metadata{Identifier: ""},
	}
	type args struct {
		ctx                    context.Context
		initiateDocSignRequest *woPb.InitiateDocSignRequest
		mocks                  []interface{}
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
		err     error
	}{
		{
			name: "successfully InitiateDocSign",
			fields: fields{
				eSignDetailsDaoMock: esigndetailsdaomock,
			},
			args: args{
				ctx:                    context.Background(),
				initiateDocSignRequest: req1,
				mocks: []interface{}{
					esigndetailsdaomock.EXPECT().Create(gomock.Any(), gomock.Any()).Return(eSignDetailRes, nil),
					manchMock.EXPECT().CreateTransaction(gomock.Any(), &woPb.CreateTransactionRequest{
						DocBase_64: req1.GetDocumentBytes(),
						DocUrl:     req1.GetDocumentUrl(),
						Identifier: req1.GetESignParams().GetIdentifier(),
						Name:       req1.GetAadhaarESignParams().GetName(),
					}).Return(eSignDetailRes.TxnId, eSignDetailRes.DocId, nil),
				},
			},
			want:    "12345",
			wantErr: false,
		},
		{
			name: "TestCast-2",
			fields: fields{
				eSignDetailsDaoMock: esigndetailsdaomock,
			},
			args: args{
				ctx:                    context.Background(),
				initiateDocSignRequest: req2,
				mocks: []interface{}{
					manchMock.EXPECT().CreateTransaction(gomock.Any(), &woPb.CreateTransactionRequest{
						DocBase_64: req2.GetDocumentBytes(),
						DocUrl:     req2.GetDocumentUrl(),
						Identifier: req2.GetESignParams().GetIdentifier(),
						Name:       req2.GetAadhaarESignParams().GetName(),
					}).Return("", "", errors.New("error while calling manch vendor for creating transaction")),
				},
			},
			wantErr: true,
			err:     errors.Wrap(errors.New("error while calling manch vendor for creating transaction"), "error while creating transaction"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := s.InitiateDocSign(tt.args.ctx, tt.args.initiateDocSignRequest)
			if (err != nil) != tt.wantErr {
				t.Errorf("InitiateDocSign() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
				if !assert.Equal(t, got, tt.want) {
					t.Errorf("InitiateDocSign() mis match = got %v, want %v", got, tt.want)
				}
			} else if err.Error() != tt.err.Error() {
				t.Errorf("Error don't match, got = %v, want %v", err, tt.err)
				return
			}
		})
	}
}

func Test_GetSignUrl(t *testing.T) {
	ctr := gomock.NewController(t)

	conf, _ := config.Load()
	type fields struct {
		eSignDetailsDaoMock dao2.ESignDetailsDao
	}
	esigndetailsdaomock := mockdao.NewMockESignDetailsDao(ctr)
	manchMock := mocks.NewMockESignProvider(ctr)
	digioMock := mocks.NewMockESignProvider(ctr)

	s := &ESignService{
		eSignDetailsDao:      esigndetailsdaomock,
		eSignProviderService: NewESignProviderService(manchMock, digioMock),
		conf:                 conf,
		s3Client:             nil,
	}

	type args struct {
		ctx     context.Context
		eSignId string
		mocks   []interface{}
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
		err     error
	}{
		{
			name: "successfully InitiateDocSign",
			fields: fields{
				eSignDetailsDaoMock: esigndetailsdaomock,
			},
			args: args{
				ctx:     context.Background(),
				eSignId: "123456",
				mocks: []interface{}{
					esigndetailsdaomock.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&woPb.ESignDetails{
						Id:       "12345",
						TxnId:    "141415",
						DocId:    "262133",
						TxnState: woPb.TxnState_TXN_STATE_PENDING,
						Vendor:   woPb.Vendor_VENDOR_MANCH,
						Metadata: nil,
					}, nil),
					manchMock.EXPECT().GetSignUrl(gomock.Any(), gomock.Any(), gomock.Any()).Return("https://uat.manchtech.com/esign.html?authToken=m5E3PlLGCCKSwm4jHb7FVElffp5KA0LYs9B&redir=https%3A%2F%2Fuat.manchtech.com%2Fesign-authentication.html", nil),
					esigndetailsdaomock.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			want:    "https://uat.manchtech.com/esign.html?authToken=m5E3PlLGCCKSwm4jHb7FVElffp5KA0LYs9B&redir=https%3A%2F%2Fuat.manchtech.com%2Fesign-authentication.html",
			wantErr: false,
		},
		{
			name: "test-2",
			fields: fields{
				eSignDetailsDaoMock: esigndetailsdaomock,
			},
			args: args{
				ctx:     context.Background(),
				eSignId: "123456",
				mocks: []interface{}{
					esigndetailsdaomock.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&woPb.ESignDetails{
						Id:       "12345",
						TxnId:    "141415",
						DocId:    "262133",
						TxnState: woPb.TxnState_TXN_STATE_PENDING,
						Vendor:   woPb.Vendor_VENDOR_MANCH,
						Metadata: nil,
					}, nil),
					manchMock.EXPECT().GetSignUrl(gomock.Any(), gomock.Any(), gomock.Any()).Return("", errors.New("error while getting sign url from manch vg")),
				},
			},
			wantErr: true,
			err:     errors.Wrap(errors.New("error while getting sign url from manch vg"), "could not fetch sign url from vendor"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := s.GetSignUrl(tt.args.ctx, tt.args.eSignId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSignUrl() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
				if !assert.Equal(t, got, tt.want) {
					t.Errorf("GetSignUrl() mis match = got %v, want %v", got, tt.want)
				}
			} else if err.Error() != tt.err.Error() {
				t.Errorf("Error don't match, got = %v, want %v", err, tt.err)
				return
			}
		})
	}
}

func Test_GetSignTxnState(t *testing.T) {
	ctr := gomock.NewController(t)

	conf, _ := config.Load()

	pdfBase64 := "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"
	pdfBytes := []byte(pdfBase64)

	type args struct {
		ctx             context.Context
		eSignId         string
		eSignDetailsDao dao2.ESignDetailsDao
		s3Client        s3.S3Client
		manchMock       ESignManch
		digioMock       ESignDigio
	}

	tests := []struct {
		name    string
		args    args
		want    woPb.TxnState
		wantErr bool
		err     error
	}{
		{
			name: "successfully InitiateDocSign",
			args: args{
				ctx:     context.Background(),
				eSignId: "123456",
				eSignDetailsDao: func() dao2.ESignDetailsDao {
					esigndetailsdaomock := mockdao.NewMockESignDetailsDao(ctr)
					esigndetailsdaomock.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&woPb.ESignDetails{
						Id:       "123456",
						TxnId:    "141415",
						DocId:    "262133",
						TxnState: woPb.TxnState_TXN_STATE_PENDING,
						Vendor:   woPb.Vendor_VENDOR_MANCH,
						Metadata: nil,
					}, nil).AnyTimes()

					esigndetailsdaomock.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
					return esigndetailsdaomock
				}(),
				s3Client: func() s3.S3Client {
					s3ClientMock := mocks2.NewMockS3Client(ctr)
					s3ClientMock.EXPECT().Write(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
					return s3ClientMock
				}(),
				manchMock: func() ESignManch {
					manchMock := mocks.NewMockESignProvider(ctr)
					manchMock.EXPECT().GetTxnStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(woPb.TxnState_TXN_STATE_SIGNED, nil).AnyTimes()
					manchMock.EXPECT().DownloadDocument(gomock.Any(), gomock.Any(), gomock.Any()).Return(pdfBytes, nil).AnyTimes()
					return manchMock
				}(),
				digioMock: func() ESignDigio {
					return mocks.NewMockESignProvider(ctr)
				}(),
			},
			want:    woPb.TxnState_TXN_STATE_SIGNED,
			wantErr: false,
		},
		{
			name: "TestCase-2",
			args: args{
				ctx:     context.Background(),
				eSignId: "123456",
				eSignDetailsDao: func() dao2.ESignDetailsDao {
					esigndetailsdaomock := mockdao.NewMockESignDetailsDao(ctr)
					esigndetailsdaomock.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&woPb.ESignDetails{
						Id:       "123456",
						TxnId:    "141415",
						DocId:    "262133",
						TxnState: woPb.TxnState_TXN_STATE_PENDING,
						Vendor:   woPb.Vendor_VENDOR_MANCH,
						Metadata: nil,
					}, nil).AnyTimes()

					esigndetailsdaomock.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("error while updating storedS3 path")).AnyTimes()
					return esigndetailsdaomock
				}(),
				s3Client: func() s3.S3Client {
					s3ClientMock := mocks2.NewMockS3Client(ctr)
					s3ClientMock.EXPECT().Write(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
					return s3ClientMock
				}(),
				manchMock: func() ESignManch {
					manchMock := mocks.NewMockESignProvider(ctr)
					manchMock.EXPECT().GetTxnStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(woPb.TxnState_TXN_STATE_SIGNED, nil).AnyTimes()
					manchMock.EXPECT().DownloadDocument(gomock.Any(), gomock.Any(), gomock.Any()).Return(pdfBytes, nil).AnyTimes()
					return manchMock
				}(),
				digioMock: func() ESignDigio {
					return mocks.NewMockESignProvider(ctr)
				}(),
			},
			wantErr: true,
			err:     errors.Wrap(errors.New("error while updating storedS3 path"), "error updating txn state in db"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ESignService{
				eSignDetailsDao:      tt.args.eSignDetailsDao,
				eSignProviderService: NewESignProviderService(tt.args.manchMock, tt.args.digioMock),
				conf:                 conf,
				s3Client:             tt.args.s3Client,
			}

			esignDetailsRes, err := s.GetLatestESignDetails(tt.args.ctx, tt.args.eSignId)
			got := esignDetailsRes.GetTxnState()
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSignTxnState() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
				if !assert.Equal(t, got, tt.want) {
					t.Errorf("GetSignTxnState() mis match = got %v, want %v", got, tt.want)
				}
			} else if err.Error() != tt.err.Error() {
				t.Errorf("Error don't match, got = %v, want %v", err, tt.err)
				return
			}
		})
	}
}

func Test_GetSignedDoc(t *testing.T) {
	ctr := gomock.NewController(t)

	conf, _ := config.Load()
	type fields struct {
		eSignDetailsDaoMock dao2.ESignDetailsDao
	}
	esigndetailsdaomock := mockdao.NewMockESignDetailsDao(ctr)
	manchMock := mocks.NewMockESignProvider(ctr)
	digioMock := mocks.NewMockESignProvider(ctr)

	s := &ESignService{
		eSignDetailsDao:      esigndetailsdaomock,
		eSignProviderService: NewESignProviderService(manchMock, digioMock),
		conf:                 conf,
		s3Client:             nil,
	}

	type args struct {
		ctx     context.Context
		eSignId string
		mocks   []interface{}
	}

	eSignDetailRes1 := &woPb.ESignDetails{
		Id:           "123456",
		TxnId:        "141415",
		DocId:        "262133",
		TxnState:     woPb.TxnState_TXN_STATE_COMPLETED,
		Vendor:       woPb.Vendor_VENDOR_MANCH,
		Metadata:     nil,
		StoredS3Path: "e_signed_documents/123456.pdf",
	}
	eSignDetailRes2 := &woPb.ESignDetails{
		Id:           "123479",
		TxnId:        "141415",
		DocId:        "262133",
		TxnState:     woPb.TxnState_TXN_STATE_SIGNED,
		Vendor:       woPb.Vendor_VENDOR_MANCH,
		Metadata:     nil,
		StoredS3Path: "e_signed_documents/123456.pdf",
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
		err     error
	}{
		{
			name: "successfully InitiateDocSign",
			fields: fields{
				eSignDetailsDaoMock: esigndetailsdaomock,
			},
			args: args{
				ctx:     context.Background(),
				eSignId: "123456",
				mocks: []interface{}{
					esigndetailsdaomock.EXPECT().Get(gomock.Any(), eSignDetailRes1.GetId()).Return(eSignDetailRes1, nil),
				},
			},
			want:    eSignDetailRes1.GetStoredS3Path(),
			wantErr: false,
		},
		{
			name: "TestCase-2",
			fields: fields{
				eSignDetailsDaoMock: esigndetailsdaomock,
			},
			args: args{
				ctx:     context.Background(),
				eSignId: "123479",
				mocks: []interface{}{
					esigndetailsdaomock.EXPECT().Get(gomock.Any(), eSignDetailRes2.GetId()).Return(eSignDetailRes2, nil),
				},
			},
			wantErr: true,
			err:     errors.New(fmt.Sprintf("state is not completed %v", eSignDetailRes2.GetTxnState())),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := s.GetSignedDocS3Path(tt.args.ctx, tt.args.eSignId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSignedDoc() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
				if !assert.Equal(t, got, tt.want) {
					t.Errorf("GetSignedDoc() mis match = got %v, want %v", got, tt.want)
				}
			} else if err.Error() != tt.err.Error() {
				t.Errorf("Error don't match, got = %v, want %v", err, tt.err)
				return
			}
		})
	}
}

func Test_CreateTransaction(t *testing.T) {
	ctr := gomock.NewController(t)

	manchMock := mocks3.NewMockManchClient(ctr)
	s := &ManchService{
		manchVgClient: manchMock,
	}

	req1 := &woPb.InitiateDocSignRequest{
		Params: &woPb.InitiateDocSignRequest_AadhaarESignParams{
			AadhaarESignParams: &woPb.AadhaarESignParams{Name: &commontypes.Name{
				FirstName: "ram",
				LastName:  "gupta",
			}},
		},
		DocumentUrl: "http://www.africau.edu/images/default/sample.pdf",
	}

	createTransactionResp := &manch.CreateTransactionResponse{
		Status:       rpc.StatusOk(),
		RequestId:    "21187584",
		Message:      " transaction is successful",
		ResponseCode: "1",
		Transaction: &manch.Transaction{
			TransactionId:   141632,
			TransactionLink: "https://uat.manchtech.com/app/api/transactions/141632",
		},
		Documents: []*manch.Document{
			{
				DocumentTypeId:    0,
				DocumentType:      "agreement",
				DocumentId:        262631,
				DocumentUrl:       "",
				DocumentBytes:     "",
				DocumentLink:      "https://uat.manchtech.com/app/api/documents/262631",
				Signed:            false,
				DocumentStorageId: "",
			},
		},
		ESignMethod: "OTP",
	}

	type args struct {
		ctx                  context.Context
		createTransactionReq *woPb.CreateTransactionRequest
		mocks                func()
	}

	tests := []struct {
		name    string
		args    args
		want    []string
		wantErr bool
		err     error
	}{
		{
			name: "Happy TestCase",
			args: args{
				ctx: context.Background(),
				createTransactionReq: &woPb.CreateTransactionRequest{
					DocBase_64: req1.GetDocumentBytes(),
					DocUrl:     req1.GetDocumentUrl(),
					Identifier: req1.GetESignParams().GetIdentifier(),
					Name:       req1.GetAadhaarESignParams().GetName(),
				},
				mocks: func() {
					manchMock.EXPECT().CreateTransaction(gomock.Any(), gomock.Any()).Return(createTransactionResp, nil)
				},
			},
			want: []string{
				"141632",
				"262631",
			},
			wantErr: false,
			err:     nil,
		},
		{
			name: "TestCase-2",
			args: args{
				ctx: context.Background(),
				createTransactionReq: &woPb.CreateTransactionRequest{
					DocBase_64: req1.GetDocumentBytes(),
					DocUrl:     req1.GetDocumentUrl(),
					Identifier: req1.GetESignParams().GetIdentifier(),
					Name:       req1.GetAadhaarESignParams().GetName(),
				},
				mocks: func() {
					manchMock.EXPECT().CreateTransaction(gomock.Any(), gomock.Any()).Return(nil, errors.New("error while creating transaction"))
				},
			},
			wantErr: true,
			err:     errors.Wrap(errors.New("error while creating transaction"), "error while calling manch vendor for creating transaction"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.args.mocks()
			got1, got2, err := s.CreateTransaction(tt.args.ctx, tt.args.createTransactionReq)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateTransaction() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				if !assert.Equal(t, got1, tt.want[0]) || !assert.Equal(t, got2, tt.want[1]) {
					t.Errorf("CreateTransaction() mis match = got txnId %v and docId %v, want txnId %v and docId %v", got1, got2, tt.want[0], tt.want[1])
				}
			} else if err.Error() != tt.err.Error() {
				t.Errorf("Error don't match, got = %v, want %v", err, tt.err)
				return
			}
		})
	}
}

func Test_GetSignUrlESignProvider(t *testing.T) {
	ctr := gomock.NewController(t)

	manchMock := mocks3.NewMockManchClient(ctr)
	s := &ManchService{
		manchVgClient: manchMock,
	}

	type args struct {
		ctx   context.Context
		docId string
		mocks func()
	}

	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
		err     error
	}{
		{
			name: "Happy TestCase",
			args: args{
				ctx:   context.Background(),
				docId: "262631",
				mocks: func() {
					manchMock.EXPECT().GetSignUrl(gomock.Any(), gomock.Any()).Return(&manch.GetSignUrlResponse{
						Status:  rpc.StatusOk(),
						SignUrl: "https://uat.manchtech.com/esign.html?authToken=GJ9K0xaWm7W4cO8Tz1nVgsReWlJASiTIa1n&redir=https%3A%2F%2Fuat.manchtech.com%2Fesign-authentication.html",
						EspName: "NSDL_2_1",
					}, nil)
				},
			},
			want:    "https://uat.manchtech.com/esign.html?authToken=GJ9K0xaWm7W4cO8Tz1nVgsReWlJASiTIa1n&redir=https%3A%2F%2Fuat.manchtech.com%2Fesign-authentication.html",
			wantErr: false,
			err:     nil,
		},
		{
			name: "TestCase-2",
			args: args{
				ctx:   context.Background(),
				docId: "262631",
				mocks: func() {
					manchMock.EXPECT().GetSignUrl(gomock.Any(), gomock.Any()).Return(nil, errors.New("error while getSignUrl"))
				},
			},
			wantErr: true,
			err:     errors.Wrap(errors.New("error while getSignUrl"), "error while getting sign url from manch vg"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.args.mocks()
			got, err := s.GetSignUrl(tt.args.ctx, tt.args.docId, "")
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSignUrlESignProvider() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				if !assert.Equal(t, got, tt.want) {
					t.Errorf("GetSignUrlESignProvider() mis match = got %v, want %v", got, tt.want)
				}
			} else if err.Error() != tt.err.Error() {
				t.Errorf("Error don't match, got = %v, want %v", err, tt.err)
				return
			}
		})
	}
}

func Test_GetTxnStatus(t *testing.T) {
	ctr := gomock.NewController(t)

	manchMock := mocks3.NewMockManchClient(ctr)
	s := &ManchService{
		manchVgClient: manchMock,
	}

	type args struct {
		ctx   context.Context
		txnId string
		mocks func()
	}

	tests := []struct {
		name    string
		args    args
		want    woPb.TxnState
		wantErr bool
		err     error
	}{
		{
			name: "Happy TestCase",
			args: args{
				ctx:   context.Background(),
				txnId: "141632",
				mocks: func() {
					manchMock.EXPECT().GetTxnStatus(gomock.Any(), gomock.Any()).Return(&manch.GetTxnStatusResponse{
						Status:   rpc.StatusOk(),
						TxnId:    141632,
						TxnState: manch.TxnState_TXN_STATE_PENDING,
						Documents: []*manch.Document{
							{
								DocumentTypeId:    0,
								DocumentType:      "agreement",
								DocumentId:        0,
								DocumentUrl:       "https://uat.manchtech.com/app/api/documents/262631/content",
								DocumentBytes:     "",
								DocumentLink:      "https://uat.manchtech.com/app/api/documents/262631",
								Signed:            false,
								DocumentStorageId: "629d964405b3ab3d09696a94",
							},
						},
					}, nil)
				},
			},
			want:    woPb.TxnState_TXN_STATE_PENDING,
			wantErr: false,
			err:     nil,
		},
		{
			name: "TestCase-2",
			args: args{
				ctx:   context.Background(),
				txnId: "141632",
				mocks: func() {
					manchMock.EXPECT().GetTxnStatus(gomock.Any(), gomock.Any()).Return(nil, errors.New("error while GetTxnStatus"))
				},
			},
			wantErr: true,
			err:     errors.Wrap(errors.New("error while GetTxnStatus"), "error getting txn state from manch vendor"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.args.mocks()
			got, err := s.GetTxnStatus(tt.args.ctx, tt.args.txnId, "")
			if (err != nil) != tt.wantErr {
				t.Errorf("GetTxnStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				if !assert.Equal(t, got, tt.want) {
					t.Errorf("GetTxnStatus() mis match = got %v, want %v", got, tt.want)
				}
			} else if err.Error() != tt.err.Error() {
				t.Errorf("Error don't match, got = %v, want %v", err, tt.err)
				return
			}
		})
	}
}

func Test_DownloadDocument(t *testing.T) {
	ctr := gomock.NewController(t)

	manchMock := mocks3.NewMockManchClient(ctr)

	pdfBase64 := "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"
	pdfBytes := []byte(pdfBase64)

	s := &ManchService{
		manchVgClient: manchMock,
	}

	type args struct {
		ctx   context.Context
		txnId string
		mocks func()
	}

	tests := []struct {
		name    string
		args    args
		want    []byte
		wantErr bool
		err     error
	}{
		{
			name: "Happy TestCase",
			args: args{
				ctx:   context.Background(),
				txnId: "141658",
				mocks: func() {
					manchMock.EXPECT().GetAllDocsOfTxn(gomock.Any(), gomock.Any()).Return(&manch.GetAllDocsOfTxnResponse{
						Status: rpc.StatusOk(),
						Documents: []*manch.Document{
							{
								DocumentTypeId:    2666,
								DocumentType:      "agreement",
								DocumentId:        262755,
								DocumentUrl:       "https://uat.manchtech.com/app/api/documents/262755/content",
								DocumentBytes:     "",
								DocumentLink:      "https://uat.manchtech.com/app/api/documents/262755",
								Signed:            false,
								DocumentStorageId: "629db0a705b3ab3d09696bda",
							},
						},
					}, nil)
					manchMock.EXPECT().DownloadDocument(gomock.Any(), gomock.Any()).Return(&manch.DownloadDocumentResponse{
						Status:    rpc.StatusOk(),
						SignedPdf: pdfBytes,
					}, nil)
				},
			},
			want:    pdfBytes,
			wantErr: false,
			err:     nil,
		},
		{
			name: "TestCase-2",
			args: args{
				ctx:   context.Background(),
				txnId: "141658",
				mocks: func() {
					manchMock.EXPECT().GetAllDocsOfTxn(gomock.Any(), gomock.Any()).Return(&manch.GetAllDocsOfTxnResponse{
						Status: rpc.StatusOk(),
						Documents: []*manch.Document{
							{
								DocumentTypeId:    2666,
								DocumentType:      "agreement",
								DocumentId:        262755,
								DocumentUrl:       "https://uat.manchtech.com/app/api/documents/262755/content",
								DocumentBytes:     "",
								DocumentLink:      "https://uat.manchtech.com/app/api/documents/262755",
								Signed:            false,
								DocumentStorageId: "629db0a705b3ab3d09696bda",
							},
						},
					}, nil)
					manchMock.EXPECT().DownloadDocument(gomock.Any(), gomock.Any()).Return(nil, errors.New("error while DownloadDocument"))
				},
			},
			wantErr: true,
			err:     errors.New("error while DownloadDocument"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.args.mocks()
			got, err := s.DownloadDocument(tt.args.ctx, tt.args.txnId, "")
			if (err != nil) != tt.wantErr {
				t.Errorf("DownloadDocument() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				if !assert.Equal(t, got, tt.want) {
					t.Errorf("DownloadDocument() mis match = got %v, want %v", got, tt.want)
				}
			} else if err.Error() != tt.err.Error() {
				t.Errorf("Error don't match, got = %v, want %v", err, tt.err)
				return
			}
		})
	}
}
