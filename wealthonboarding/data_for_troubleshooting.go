package wealthonboarding

import (
	"context"
	"sort"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

// nolint: funlen
func (s *Service) GetOnboardingTroubleshootDetails(ctx context.Context, req *woPb.GetOnboardingTroubleshootDetailsRequest) (*woPb.GetOnboardingTroubleshootDetailsResponse, error) {
	od, err := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, req.GetActorId(), req.GetOnboardingType())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &woPb.GetOnboardingTroubleshootDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		} else {
			logger.Error(ctx, "failed to fetch onboarding details", zap.Error(err))
			return &woPb.GetOnboardingTroubleshootDetailsResponse{
				Status: rpc.StatusInternal(),
			}, nil
		}
	}

	osd, osdErr := s.onboardingStepDetailsDao.GetByOnboardingDetailsId(ctx, od.GetId())
	if osdErr != nil {
		if errors.Is(osdErr, epifierrors.ErrRecordNotFound) {
			return &woPb.GetOnboardingTroubleshootDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		} else {
			logger.Error(ctx, "failed to fetch onboarding step details", zap.Error(osdErr))
			return &woPb.GetOnboardingTroubleshootDetailsResponse{
				Status: rpc.StatusInternal(),
			}, nil
		}
	}

	sort.Slice(osd, func(i, j int) bool {
		return osd[i].GetUpdatedAt().AsTime().Before(osd[j].GetUpdatedAt().AsTime())
	})
	onbTroubleshootDetails := woPb.OnboardingTroubleshootDetails{
		Status:              od.GetStatus(),
		StageDetailsMapping: make(map[string]*woPb.StageDetails),
	}
	var currentStageDetails *woPb.OnboardingStepDetails
	order := uint32(0)
	// Populate stage details
	for _, step := range osd {
		order++
		if od.CurrentStep == step.Step {
			currentStageDetails = step
		}
		handler, hErr := s.troubleshootHandler.GetTroubleshootHandler(step.Step)
		if hErr != nil {
			logger.Error(ctx, "error getting wealth onboarding step handler", zap.Error(hErr))
			continue
		}
		onbTroubleshootDetails.StageDetailsMapping[step.Step.String()] = handler.GetOnboardingStageDetails(ctx, od, step)
		onbTroubleshootDetails.StageDetailsMapping[step.Step.String()].Order = order
	}

	currentStageHandler, hErr := s.troubleshootHandler.GetTroubleshootHandler(currentStageDetails.GetStep())
	if hErr != nil {
		logger.Error(ctx, "error getting wealth onboarding step handler", zap.Error(hErr))
		return &woPb.GetOnboardingTroubleshootDetailsResponse{
			Status:                        rpc.StatusOk(),
			OnboardingTroubleshootDetails: &onbTroubleshootDetails,
		}, nil
	}
	onbTroubleshootDetails.CurrentStageTroubleShootingDetails = currentStageHandler.GetStageTroubleShootingDetails(ctx, od, currentStageDetails)

	return &woPb.GetOnboardingTroubleshootDetailsResponse{
		Status:                        rpc.StatusOk(),
		OnboardingTroubleshootDetails: &onbTroubleshootDetails,
	}, nil
}
