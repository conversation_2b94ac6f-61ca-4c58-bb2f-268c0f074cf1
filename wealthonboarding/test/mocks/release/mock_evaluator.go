// Code generated by MockGen. DO NOT EDIT.
// Source: wealthonboarding/release/evaluator.go

// Package mock_evaluator is a generated GoMock package.
package mock_evaluator

import (
	context "context"
	reflect "reflect"

	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	release "github.com/epifi/gamma/wealthonboarding/release"
	gomock "github.com/golang/mock/gomock"
)

// MockIEvaluator is a mock of IEvaluator interface.
type MockIEvaluator struct {
	ctrl     *gomock.Controller
	recorder *MockIEvaluatorMockRecorder
}

// MockIEvaluatorMockRecorder is the mock recorder for MockIEvaluator.
type MockIEvaluatorMockRecorder struct {
	mock *MockIEvaluator
}

// NewMockIEvaluator creates a new mock instance.
func NewMockIEvaluator(ctrl *gomock.Controller) *MockIEvaluator {
	mock := &MockIEvaluator{ctrl: ctrl}
	mock.recorder = &MockIEvaluatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIEvaluator) EXPECT() *MockIEvaluatorMockRecorder {
	return m.recorder
}

// Evaluate mocks base method.
func (m *MockIEvaluator) Evaluate(ctx context.Context, commonConstraintData *release.CommonConstraintData) (bool, *deeplink.Deeplink, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Evaluate", ctx, commonConstraintData)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(*deeplink.Deeplink)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Evaluate indicates an expected call of Evaluate.
func (mr *MockIEvaluatorMockRecorder) Evaluate(ctx, commonConstraintData interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Evaluate", reflect.TypeOf((*MockIEvaluator)(nil).Evaluate), ctx, commonConstraintData)
}
