// Code generated by MockGen. DO NOT EDIT.
// Source: ./esign.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	wealthonboarding "github.com/epifi/gamma/api/wealthonboarding"
	gomock "github.com/golang/mock/gomock"
)

// MockESign is a mock of ESign interface.
type MockESign struct {
	ctrl     *gomock.Controller
	recorder *MockESignMockRecorder
}

// MockESignMockRecorder is the mock recorder for MockESign.
type MockESignMockRecorder struct {
	mock *MockESign
}

// NewMockESign creates a new mock instance.
func NewMockESign(ctrl *gomock.Controller) *MockESign {
	mock := &MockESign{ctrl: ctrl}
	mock.recorder = &MockESignMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockESign) EXPECT() *MockESignMockRecorder {
	return m.recorder
}

// GetLatestESignDetails mocks base method.
func (m *MockESign) GetLatestESignDetails(ctx context.Context, eSignId string) (*wealthonboarding.ESignDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestESignDetails", ctx, eSignId)
	ret0, _ := ret[0].(*wealthonboarding.ESignDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestESignDetails indicates an expected call of GetLatestESignDetails.
func (mr *MockESignMockRecorder) GetLatestESignDetails(ctx, eSignId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestESignDetails", reflect.TypeOf((*MockESign)(nil).GetLatestESignDetails), ctx, eSignId)
}

// GetSignUrl mocks base method.
func (m *MockESign) GetSignUrl(ctx context.Context, eSignId string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSignUrl", ctx, eSignId)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSignUrl indicates an expected call of GetSignUrl.
func (mr *MockESignMockRecorder) GetSignUrl(ctx, eSignId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSignUrl", reflect.TypeOf((*MockESign)(nil).GetSignUrl), ctx, eSignId)
}

// GetSignedDocS3Path mocks base method.
func (m *MockESign) GetSignedDocS3Path(ctx context.Context, eSignId string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSignedDocS3Path", ctx, eSignId)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSignedDocS3Path indicates an expected call of GetSignedDocS3Path.
func (mr *MockESignMockRecorder) GetSignedDocS3Path(ctx, eSignId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSignedDocS3Path", reflect.TypeOf((*MockESign)(nil).GetSignedDocS3Path), ctx, eSignId)
}

// InitiateDocSign mocks base method.
func (m *MockESign) InitiateDocSign(ctx context.Context, req *wealthonboarding.InitiateDocSignRequest) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateDocSign", ctx, req)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateDocSign indicates an expected call of InitiateDocSign.
func (mr *MockESignMockRecorder) InitiateDocSign(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateDocSign", reflect.TypeOf((*MockESign)(nil).InitiateDocSign), ctx, req)
}

// IsValidEsign mocks base method.
func (m *MockESign) IsValidEsign(ctx context.Context, esignId string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsValidEsign", ctx, esignId)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsValidEsign indicates an expected call of IsValidEsign.
func (mr *MockESignMockRecorder) IsValidEsign(ctx, esignId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsValidEsign", reflect.TypeOf((*MockESign)(nil).IsValidEsign), ctx, esignId)
}

// MockESignProvider is a mock of ESignProvider interface.
type MockESignProvider struct {
	ctrl     *gomock.Controller
	recorder *MockESignProviderMockRecorder
}

// MockESignProviderMockRecorder is the mock recorder for MockESignProvider.
type MockESignProviderMockRecorder struct {
	mock *MockESignProvider
}

// NewMockESignProvider creates a new mock instance.
func NewMockESignProvider(ctrl *gomock.Controller) *MockESignProvider {
	mock := &MockESignProvider{ctrl: ctrl}
	mock.recorder = &MockESignProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockESignProvider) EXPECT() *MockESignProviderMockRecorder {
	return m.recorder
}

// CreateTransaction mocks base method.
func (m *MockESignProvider) CreateTransaction(ctx context.Context, request *wealthonboarding.CreateTransactionRequest) (string, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTransaction", ctx, request)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// CreateTransaction indicates an expected call of CreateTransaction.
func (mr *MockESignProviderMockRecorder) CreateTransaction(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTransaction", reflect.TypeOf((*MockESignProvider)(nil).CreateTransaction), ctx, request)
}

// DownloadDocument mocks base method.
func (m *MockESignProvider) DownloadDocument(ctx context.Context, transactionId, documentId string) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DownloadDocument", ctx, transactionId, documentId)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DownloadDocument indicates an expected call of DownloadDocument.
func (mr *MockESignProviderMockRecorder) DownloadDocument(ctx, transactionId, documentId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadDocument", reflect.TypeOf((*MockESignProvider)(nil).DownloadDocument), ctx, transactionId, documentId)
}

// GetSignUrl mocks base method.
func (m *MockESignProvider) GetSignUrl(ctx context.Context, documentId, identifier string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSignUrl", ctx, documentId, identifier)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSignUrl indicates an expected call of GetSignUrl.
func (mr *MockESignProviderMockRecorder) GetSignUrl(ctx, documentId, identifier interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSignUrl", reflect.TypeOf((*MockESignProvider)(nil).GetSignUrl), ctx, documentId, identifier)
}

// GetTxnStatus mocks base method.
func (m *MockESignProvider) GetTxnStatus(ctx context.Context, transactionId, documentId string) (wealthonboarding.TxnState, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTxnStatus", ctx, transactionId, documentId)
	ret0, _ := ret[0].(wealthonboarding.TxnState)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTxnStatus indicates an expected call of GetTxnStatus.
func (mr *MockESignProviderMockRecorder) GetTxnStatus(ctx, transactionId, documentId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTxnStatus", reflect.TypeOf((*MockESignProvider)(nil).GetTxnStatus), ctx, transactionId, documentId)
}
