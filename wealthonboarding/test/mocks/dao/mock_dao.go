// Code generated by MockGen. DO NOT EDIT.
// Source: ./dao.go

// Package mock_dao is a generated GoMock package.
package mock_dao

import (
	context "context"
	reflect "reflect"

	rpc "github.com/epifi/be-common/api/rpc"
	pagination "github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	wealthonboarding "github.com/epifi/gamma/api/wealthonboarding"
	ckyc "github.com/epifi/gamma/api/wealthonboarding/ckyc"
	user "github.com/epifi/gamma/api/wealthonboarding/user"
	gomock "github.com/golang/mock/gomock"
)

// MockOnboardingDetailsDao is a mock of OnboardingDetailsDao interface.
type MockOnboardingDetailsDao struct {
	ctrl     *gomock.Controller
	recorder *MockOnboardingDetailsDaoMockRecorder
}

// MockOnboardingDetailsDaoMockRecorder is the mock recorder for MockOnboardingDetailsDao.
type MockOnboardingDetailsDaoMockRecorder struct {
	mock *MockOnboardingDetailsDao
}

// NewMockOnboardingDetailsDao creates a new mock instance.
func NewMockOnboardingDetailsDao(ctrl *gomock.Controller) *MockOnboardingDetailsDao {
	mock := &MockOnboardingDetailsDao{ctrl: ctrl}
	mock.recorder = &MockOnboardingDetailsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOnboardingDetailsDao) EXPECT() *MockOnboardingDetailsDaoMockRecorder {
	return m.recorder
}

// BatchGetByActorIdsAndOnbType mocks base method.
func (m *MockOnboardingDetailsDao) BatchGetByActorIdsAndOnbType(ctx context.Context, actorIds []string, onbType wealthonboarding.OnboardingType) ([]*wealthonboarding.OnboardingDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetByActorIdsAndOnbType", ctx, actorIds, onbType)
	ret0, _ := ret[0].([]*wealthonboarding.OnboardingDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetByActorIdsAndOnbType indicates an expected call of BatchGetByActorIdsAndOnbType.
func (mr *MockOnboardingDetailsDaoMockRecorder) BatchGetByActorIdsAndOnbType(ctx, actorIds, onbType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetByActorIdsAndOnbType", reflect.TypeOf((*MockOnboardingDetailsDao)(nil).BatchGetByActorIdsAndOnbType), ctx, actorIds, onbType)
}

// Create mocks base method.
func (m *MockOnboardingDetailsDao) Create(ctx context.Context, onboardingDetails *wealthonboarding.OnboardingDetails) (*wealthonboarding.OnboardingDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, onboardingDetails)
	ret0, _ := ret[0].(*wealthonboarding.OnboardingDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockOnboardingDetailsDaoMockRecorder) Create(ctx, onboardingDetails interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockOnboardingDetailsDao)(nil).Create), ctx, onboardingDetails)
}

// GetByActorIdAndOnbType mocks base method.
func (m *MockOnboardingDetailsDao) GetByActorIdAndOnbType(ctx context.Context, actorId string, onbType wealthonboarding.OnboardingType) (*wealthonboarding.OnboardingDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorIdAndOnbType", ctx, actorId, onbType)
	ret0, _ := ret[0].(*wealthonboarding.OnboardingDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorIdAndOnbType indicates an expected call of GetByActorIdAndOnbType.
func (mr *MockOnboardingDetailsDaoMockRecorder) GetByActorIdAndOnbType(ctx, actorId, onbType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorIdAndOnbType", reflect.TypeOf((*MockOnboardingDetailsDao)(nil).GetByActorIdAndOnbType), ctx, actorId, onbType)
}

// GetById mocks base method.
func (m *MockOnboardingDetailsDao) GetById(ctx context.Context, id string) (*wealthonboarding.OnboardingDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*wealthonboarding.OnboardingDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockOnboardingDetailsDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockOnboardingDetailsDao)(nil).GetById), ctx, id)
}

// GetByIds mocks base method.
func (m *MockOnboardingDetailsDao) GetByIds(ctx context.Context, ids []string) ([]*wealthonboarding.OnboardingDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByIds", ctx, ids)
	ret0, _ := ret[0].([]*wealthonboarding.OnboardingDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByIds indicates an expected call of GetByIds.
func (mr *MockOnboardingDetailsDaoMockRecorder) GetByIds(ctx, ids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByIds", reflect.TypeOf((*MockOnboardingDetailsDao)(nil).GetByIds), ctx, ids)
}

// GetOrCreate mocks base method.
func (m *MockOnboardingDetailsDao) GetOrCreate(ctx context.Context, actorId string, onbType wealthonboarding.OnboardingType, newOnbDetails *wealthonboarding.OnboardingDetails) (*wealthonboarding.OnboardingDetails, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrCreate", ctx, actorId, onbType, newOnbDetails)
	ret0, _ := ret[0].(*wealthonboarding.OnboardingDetails)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetOrCreate indicates an expected call of GetOrCreate.
func (mr *MockOnboardingDetailsDaoMockRecorder) GetOrCreate(ctx, actorId, onbType, newOnbDetails interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrCreate", reflect.TypeOf((*MockOnboardingDetailsDao)(nil).GetOrCreate), ctx, actorId, onbType, newOnbDetails)
}

// Update mocks base method.
func (m *MockOnboardingDetailsDao) Update(ctx context.Context, onboardingDetails *wealthonboarding.OnboardingDetails, updateMasks []wealthonboarding.OnboardingDetailsFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, onboardingDetails, updateMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockOnboardingDetailsDaoMockRecorder) Update(ctx, onboardingDetails, updateMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockOnboardingDetailsDao)(nil).Update), ctx, onboardingDetails, updateMasks)
}

// MockOnboardingStepDetailsDao is a mock of OnboardingStepDetailsDao interface.
type MockOnboardingStepDetailsDao struct {
	ctrl     *gomock.Controller
	recorder *MockOnboardingStepDetailsDaoMockRecorder
}

// MockOnboardingStepDetailsDaoMockRecorder is the mock recorder for MockOnboardingStepDetailsDao.
type MockOnboardingStepDetailsDaoMockRecorder struct {
	mock *MockOnboardingStepDetailsDao
}

// NewMockOnboardingStepDetailsDao creates a new mock instance.
func NewMockOnboardingStepDetailsDao(ctrl *gomock.Controller) *MockOnboardingStepDetailsDao {
	mock := &MockOnboardingStepDetailsDao{ctrl: ctrl}
	mock.recorder = &MockOnboardingStepDetailsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOnboardingStepDetailsDao) EXPECT() *MockOnboardingStepDetailsDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockOnboardingStepDetailsDao) Create(ctx context.Context, onboardingStepDetails *wealthonboarding.OnboardingStepDetails) (*wealthonboarding.OnboardingStepDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, onboardingStepDetails)
	ret0, _ := ret[0].(*wealthonboarding.OnboardingStepDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockOnboardingStepDetailsDaoMockRecorder) Create(ctx, onboardingStepDetails interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockOnboardingStepDetailsDao)(nil).Create), ctx, onboardingStepDetails)
}

// GetById mocks base method.
func (m *MockOnboardingStepDetailsDao) GetById(ctx context.Context, id string) (*wealthonboarding.OnboardingStepDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*wealthonboarding.OnboardingStepDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockOnboardingStepDetailsDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockOnboardingStepDetailsDao)(nil).GetById), ctx, id)
}

// GetByOnboardingDetailsId mocks base method.
func (m *MockOnboardingStepDetailsDao) GetByOnboardingDetailsId(ctx context.Context, onboardingDetailsId string) ([]*wealthonboarding.OnboardingStepDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByOnboardingDetailsId", ctx, onboardingDetailsId)
	ret0, _ := ret[0].([]*wealthonboarding.OnboardingStepDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByOnboardingDetailsId indicates an expected call of GetByOnboardingDetailsId.
func (mr *MockOnboardingStepDetailsDaoMockRecorder) GetByOnboardingDetailsId(ctx, onboardingDetailsId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByOnboardingDetailsId", reflect.TypeOf((*MockOnboardingStepDetailsDao)(nil).GetByOnboardingDetailsId), ctx, onboardingDetailsId)
}

// GetByOnboardingDetailsIdAndStep mocks base method.
func (m *MockOnboardingStepDetailsDao) GetByOnboardingDetailsIdAndStep(ctx context.Context, onboardingDetailsId string, step wealthonboarding.OnboardingStep) (*wealthonboarding.OnboardingStepDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByOnboardingDetailsIdAndStep", ctx, onboardingDetailsId, step)
	ret0, _ := ret[0].(*wealthonboarding.OnboardingStepDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByOnboardingDetailsIdAndStep indicates an expected call of GetByOnboardingDetailsIdAndStep.
func (mr *MockOnboardingStepDetailsDaoMockRecorder) GetByOnboardingDetailsIdAndStep(ctx, onboardingDetailsId, step interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByOnboardingDetailsIdAndStep", reflect.TypeOf((*MockOnboardingStepDetailsDao)(nil).GetByOnboardingDetailsIdAndStep), ctx, onboardingDetailsId, step)
}

// GetOrCreate mocks base method.
func (m *MockOnboardingStepDetailsDao) GetOrCreate(ctx context.Context, onbDetailsId string, step wealthonboarding.OnboardingStep, newOnbStepDetails *wealthonboarding.OnboardingStepDetails) (*wealthonboarding.OnboardingStepDetails, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrCreate", ctx, onbDetailsId, step, newOnbStepDetails)
	ret0, _ := ret[0].(*wealthonboarding.OnboardingStepDetails)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetOrCreate indicates an expected call of GetOrCreate.
func (mr *MockOnboardingStepDetailsDaoMockRecorder) GetOrCreate(ctx, onbDetailsId, step, newOnbStepDetails interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrCreate", reflect.TypeOf((*MockOnboardingStepDetailsDao)(nil).GetOrCreate), ctx, onbDetailsId, step, newOnbStepDetails)
}

// UpdateById mocks base method.
func (m *MockOnboardingStepDetailsDao) UpdateById(ctx context.Context, onboardingStepDetails *wealthonboarding.OnboardingStepDetails, updateMasks []wealthonboarding.OnboardingStepDetailsFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateById", ctx, onboardingStepDetails, updateMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateById indicates an expected call of UpdateById.
func (mr *MockOnboardingStepDetailsDaoMockRecorder) UpdateById(ctx, onboardingStepDetails, updateMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateById", reflect.TypeOf((*MockOnboardingStepDetailsDao)(nil).UpdateById), ctx, onboardingStepDetails, updateMasks)
}

// MockESignDetailsDao is a mock of ESignDetailsDao interface.
type MockESignDetailsDao struct {
	ctrl     *gomock.Controller
	recorder *MockESignDetailsDaoMockRecorder
}

// MockESignDetailsDaoMockRecorder is the mock recorder for MockESignDetailsDao.
type MockESignDetailsDaoMockRecorder struct {
	mock *MockESignDetailsDao
}

// NewMockESignDetailsDao creates a new mock instance.
func NewMockESignDetailsDao(ctrl *gomock.Controller) *MockESignDetailsDao {
	mock := &MockESignDetailsDao{ctrl: ctrl}
	mock.recorder = &MockESignDetailsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockESignDetailsDao) EXPECT() *MockESignDetailsDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockESignDetailsDao) Create(ctx context.Context, eSignDetails *wealthonboarding.ESignDetails) (*wealthonboarding.ESignDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, eSignDetails)
	ret0, _ := ret[0].(*wealthonboarding.ESignDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockESignDetailsDaoMockRecorder) Create(ctx, eSignDetails interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockESignDetailsDao)(nil).Create), ctx, eSignDetails)
}

// Get mocks base method.
func (m *MockESignDetailsDao) Get(ctx context.Context, eSignId string) (*wealthonboarding.ESignDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, eSignId)
	ret0, _ := ret[0].(*wealthonboarding.ESignDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockESignDetailsDaoMockRecorder) Get(ctx, eSignId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockESignDetailsDao)(nil).Get), ctx, eSignId)
}

// UpdateById mocks base method.
func (m *MockESignDetailsDao) UpdateById(ctx context.Context, details *wealthonboarding.ESignDetails, updateMasks []wealthonboarding.ESignDetailsFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateById", ctx, details, updateMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateById indicates an expected call of UpdateById.
func (mr *MockESignDetailsDaoMockRecorder) UpdateById(ctx, details, updateMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateById", reflect.TypeOf((*MockESignDetailsDao)(nil).UpdateById), ctx, details, updateMasks)
}

// MockVendorRequestDao is a mock of VendorRequestDao interface.
type MockVendorRequestDao struct {
	ctrl     *gomock.Controller
	recorder *MockVendorRequestDaoMockRecorder
}

// MockVendorRequestDaoMockRecorder is the mock recorder for MockVendorRequestDao.
type MockVendorRequestDaoMockRecorder struct {
	mock *MockVendorRequestDao
}

// NewMockVendorRequestDao creates a new mock instance.
func NewMockVendorRequestDao(ctrl *gomock.Controller) *MockVendorRequestDao {
	mock := &MockVendorRequestDao{ctrl: ctrl}
	mock.recorder = &MockVendorRequestDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockVendorRequestDao) EXPECT() *MockVendorRequestDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockVendorRequestDao) Create(ctx context.Context, vendorRequest *wealthonboarding.VendorRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, vendorRequest)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockVendorRequestDaoMockRecorder) Create(ctx, vendorRequest interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockVendorRequestDao)(nil).Create), ctx, vendorRequest)
}

// GetByActorId mocks base method.
func (m *MockVendorRequestDao) GetByActorId(ctx context.Context, actorId string) ([]*wealthonboarding.VendorRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorId", ctx, actorId)
	ret0, _ := ret[0].([]*wealthonboarding.VendorRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockVendorRequestDaoMockRecorder) GetByActorId(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockVendorRequestDao)(nil).GetByActorId), ctx, actorId)
}

// MockManualReviewDao is a mock of ManualReviewDao interface.
type MockManualReviewDao struct {
	ctrl     *gomock.Controller
	recorder *MockManualReviewDaoMockRecorder
}

// MockManualReviewDaoMockRecorder is the mock recorder for MockManualReviewDao.
type MockManualReviewDaoMockRecorder struct {
	mock *MockManualReviewDao
}

// NewMockManualReviewDao creates a new mock instance.
func NewMockManualReviewDao(ctrl *gomock.Controller) *MockManualReviewDao {
	mock := &MockManualReviewDao{ctrl: ctrl}
	mock.recorder = &MockManualReviewDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockManualReviewDao) EXPECT() *MockManualReviewDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockManualReviewDao) Create(ctx context.Context, review *wealthonboarding.ManualReview) (*wealthonboarding.ManualReview, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, review)
	ret0, _ := ret[0].(*wealthonboarding.ManualReview)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockManualReviewDaoMockRecorder) Create(ctx, review interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockManualReviewDao)(nil).Create), ctx, review)
}

// GetById mocks base method.
func (m *MockManualReviewDao) GetById(ctx context.Context, id string) (*wealthonboarding.ManualReview, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*wealthonboarding.ManualReview)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockManualReviewDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockManualReviewDao)(nil).GetById), ctx, id)
}

// UpdateById mocks base method.
func (m *MockManualReviewDao) UpdateById(ctx context.Context, review *wealthonboarding.ManualReview, mask []wealthonboarding.ManualReviewFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateById", ctx, review, mask)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateById indicates an expected call of UpdateById.
func (mr *MockManualReviewDaoMockRecorder) UpdateById(ctx, review, mask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateById", reflect.TypeOf((*MockManualReviewDao)(nil).UpdateById), ctx, review, mask)
}

// MockCkycDataDao is a mock of CkycDataDao interface.
type MockCkycDataDao struct {
	ctrl     *gomock.Controller
	recorder *MockCkycDataDaoMockRecorder
}

// MockCkycDataDaoMockRecorder is the mock recorder for MockCkycDataDao.
type MockCkycDataDaoMockRecorder struct {
	mock *MockCkycDataDao
}

// NewMockCkycDataDao creates a new mock instance.
func NewMockCkycDataDao(ctrl *gomock.Controller) *MockCkycDataDao {
	mock := &MockCkycDataDao{ctrl: ctrl}
	mock.recorder = &MockCkycDataDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCkycDataDao) EXPECT() *MockCkycDataDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockCkycDataDao) Create(ctx context.Context, ckycData *ckyc.CkycData) (*ckyc.CkycData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, ckycData)
	ret0, _ := ret[0].(*ckyc.CkycData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockCkycDataDaoMockRecorder) Create(ctx, ckycData interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockCkycDataDao)(nil).Create), ctx, ckycData)
}

// GetByActorIdAndType mocks base method.
func (m *MockCkycDataDao) GetByActorIdAndType(ctx context.Context, actorId string, dataType ckyc.CkycDataType) (*ckyc.CkycData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorIdAndType", ctx, actorId, dataType)
	ret0, _ := ret[0].(*ckyc.CkycData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorIdAndType indicates an expected call of GetByActorIdAndType.
func (mr *MockCkycDataDaoMockRecorder) GetByActorIdAndType(ctx, actorId, dataType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorIdAndType", reflect.TypeOf((*MockCkycDataDao)(nil).GetByActorIdAndType), ctx, actorId, dataType)
}

// MockUserDao is a mock of UserDao interface.
type MockUserDao struct {
	ctrl     *gomock.Controller
	recorder *MockUserDaoMockRecorder
}

// MockUserDaoMockRecorder is the mock recorder for MockUserDao.
type MockUserDaoMockRecorder struct {
	mock *MockUserDao
}

// NewMockUserDao creates a new mock instance.
func NewMockUserDao(ctrl *gomock.Controller) *MockUserDao {
	mock := &MockUserDao{ctrl: ctrl}
	mock.recorder = &MockUserDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserDao) EXPECT() *MockUserDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockUserDao) Create(ctx context.Context, wealthUser *user.User) (*user.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, wealthUser)
	ret0, _ := ret[0].(*user.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockUserDaoMockRecorder) Create(ctx, wealthUser interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockUserDao)(nil).Create), ctx, wealthUser)
}

// GetByActorId mocks base method.
func (m *MockUserDao) GetByActorId(ctx context.Context, actorId string) (*user.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorId", ctx, actorId)
	ret0, _ := ret[0].(*user.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockUserDaoMockRecorder) GetByActorId(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockUserDao)(nil).GetByActorId), ctx, actorId)
}

// GetById mocks base method.
func (m *MockUserDao) GetById(ctx context.Context, id string) (*user.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*user.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockUserDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockUserDao)(nil).GetById), ctx, id)
}

// Update mocks base method.
func (m *MockUserDao) Update(ctx context.Context, wealthUser *user.User, updateMasks []user.UserFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, wealthUser, updateMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockUserDaoMockRecorder) Update(ctx, wealthUser, updateMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockUserDao)(nil).Update), ctx, wealthUser, updateMasks)
}

// MockOnboardingSummary is a mock of OnboardingSummary interface.
type MockOnboardingSummary struct {
	ctrl     *gomock.Controller
	recorder *MockOnboardingSummaryMockRecorder
}

// MockOnboardingSummaryMockRecorder is the mock recorder for MockOnboardingSummary.
type MockOnboardingSummaryMockRecorder struct {
	mock *MockOnboardingSummary
}

// NewMockOnboardingSummary creates a new mock instance.
func NewMockOnboardingSummary(ctrl *gomock.Controller) *MockOnboardingSummary {
	mock := &MockOnboardingSummary{ctrl: ctrl}
	mock.recorder = &MockOnboardingSummaryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOnboardingSummary) EXPECT() *MockOnboardingSummaryMockRecorder {
	return m.recorder
}

// GetByFilters mocks base method.
func (m *MockOnboardingSummary) GetByFilters(ctx context.Context, pageToken *pagination.PageToken, filters ...storagev2.FilterOption) ([]*wealthonboarding.OnboardingSummary, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, pageToken}
	for _, a := range filters {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByFilters", varargs...)
	ret0, _ := ret[0].([]*wealthonboarding.OnboardingSummary)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetByFilters indicates an expected call of GetByFilters.
func (mr *MockOnboardingSummaryMockRecorder) GetByFilters(ctx, pageToken interface{}, filters ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, pageToken}, filters...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByFilters", reflect.TypeOf((*MockOnboardingSummary)(nil).GetByFilters), varargs...)
}
