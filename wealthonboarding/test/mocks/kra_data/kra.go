// Code generated by MockGen. DO NOT EDIT.
// Source: kra.go

// Package mock_kra_data is a generated GoMock package.
package mock_kra_data

import (
	context "context"
	reflect "reflect"

	types "github.com/epifi/gamma/api/typesv2"
	digilocker "github.com/epifi/gamma/api/vendorgateway/wealth/digilocker"
	wealthonboarding "github.com/epifi/gamma/api/wealthonboarding"
	gomock "github.com/golang/mock/gomock"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

// MockKraData is a mock of KraData interface.
type MockKraData struct {
	ctrl     *gomock.Controller
	recorder *MockKraDataMockRecorder
}

// MockKraDataMockRecorder is the mock recorder for MockKraData.
type MockKraDataMockRecorder struct {
	mock *MockKraData
}

// NewMockKraData creates a new mock instance.
func NewMockKraData(ctrl *gomock.Controller) *MockKraData {
	mock := &MockKraData{ctrl: ctrl}
	mock.recorder = &MockKraDataMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockKraData) EXPECT() *MockKraDataMockRecorder {
	return m.recorder
}

// DownloadKraData mocks base method.
func (m *MockKraData) DownloadKraData(ctx context.Context, od *wealthonboarding.OnboardingDetails, createdAt *timestamppb.Timestamp, kraVendor string) (*types.DocumentProof, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DownloadKraData", ctx, od, createdAt, kraVendor)
	ret0, _ := ret[0].(*types.DocumentProof)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DownloadKraData indicates an expected call of DownloadKraData.
func (mr *MockKraDataMockRecorder) DownloadKraData(ctx, od, createdAt, kraVendor interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadKraData", reflect.TypeOf((*MockKraData)(nil).DownloadKraData), ctx, od, createdAt, kraVendor)
}

// GenerateAadhaarPdf mocks base method.
func (m *MockKraData) GenerateAadhaarPdf(ctx context.Context, actorId string, aadhaarRes *digilocker.GetAadhaarInXmlResponse) (*types.DocumentProof, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateAadhaarPdf", ctx, actorId, aadhaarRes)
	ret0, _ := ret[0].(*types.DocumentProof)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateAadhaarPdf indicates an expected call of GenerateAadhaarPdf.
func (mr *MockKraDataMockRecorder) GenerateAadhaarPdf(ctx, actorId, aadhaarRes interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateAadhaarPdf", reflect.TypeOf((*MockKraData)(nil).GenerateAadhaarPdf), ctx, actorId, aadhaarRes)
}

// GenerateAgreementPdf mocks base method.
func (m *MockKraData) GenerateAgreementPdf(ctx context.Context, actorId, b64SignatureData, customerFullName string) (*types.DocumentProof, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateAgreementPdf", ctx, actorId, b64SignatureData, customerFullName)
	ret0, _ := ret[0].(*types.DocumentProof)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateAgreementPdf indicates an expected call of GenerateAgreementPdf.
func (mr *MockKraDataMockRecorder) GenerateAgreementPdf(ctx, actorId, b64SignatureData, customerFullName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateAgreementPdf", reflect.TypeOf((*MockKraData)(nil).GenerateAgreementPdf), ctx, actorId, b64SignatureData, customerFullName)
}

// GenerateKraFormPdf mocks base method.
func (m *MockKraData) GenerateKraFormPdf(ctx context.Context, od *wealthonboarding.OnboardingDetails) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateKraFormPdf", ctx, od)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateKraFormPdf indicates an expected call of GenerateKraFormPdf.
func (mr *MockKraDataMockRecorder) GenerateKraFormPdf(ctx, od interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateKraFormPdf", reflect.TypeOf((*MockKraData)(nil).GenerateKraFormPdf), ctx, od)
}

// UploadAadhaarXmlToKra mocks base method.
func (m *MockKraData) UploadAadhaarXmlToKra(ctx context.Context, panNumber string, latestUploadAttemptMadeAt *timestamppb.Timestamp, isKRAStatusOnHold bool, aadhaarXmlData []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadAadhaarXmlToKra", ctx, panNumber, latestUploadAttemptMadeAt, isKRAStatusOnHold, aadhaarXmlData)
	ret0, _ := ret[0].(error)
	return ret0
}

// UploadAadhaarXmlToKra indicates an expected call of UploadAadhaarXmlToKra.
func (mr *MockKraDataMockRecorder) UploadAadhaarXmlToKra(ctx, panNumber, latestUploadAttemptMadeAt, isKRAStatusOnHold, aadhaarXmlData interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadAadhaarXmlToKra", reflect.TypeOf((*MockKraData)(nil).UploadAadhaarXmlToKra), ctx, panNumber, latestUploadAttemptMadeAt, isKRAStatusOnHold, aadhaarXmlData)
}

// UploadDocketToKra mocks base method.
func (m *MockKraData) UploadDocketToKra(ctx context.Context, panNumber string, latestUploadAttemptMadeAt *timestamppb.Timestamp, isKRAStatusOnHold bool, docketData []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadDocketToKra", ctx, panNumber, latestUploadAttemptMadeAt, isKRAStatusOnHold, docketData)
	ret0, _ := ret[0].(error)
	return ret0
}

// UploadDocketToKra indicates an expected call of UploadDocketToKra.
func (mr *MockKraDataMockRecorder) UploadDocketToKra(ctx, panNumber, latestUploadAttemptMadeAt, isKRAStatusOnHold, docketData interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadDocketToKra", reflect.TypeOf((*MockKraData)(nil).UploadDocketToKra), ctx, panNumber, latestUploadAttemptMadeAt, isKRAStatusOnHold, docketData)
}
