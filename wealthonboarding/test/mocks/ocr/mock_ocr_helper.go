// Code generated by MockGen. DO NOT EDIT.
// Source: ./ocr_helper.go

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	types "github.com/epifi/gamma/api/typesv2"
	wealthonboarding "github.com/epifi/gamma/api/wealthonboarding"
	gomock "github.com/golang/mock/gomock"
	context "golang.org/x/net/context"
)

// MockIOcrHelper is a mock of IOcrHelper interface.
type MockIOcrHelper struct {
	ctrl     *gomock.Controller
	recorder *MockIOcrHelperMockRecorder
}

// MockIOcrHelperMockRecorder is the mock recorder for MockIOcrHelper.
type MockIOcrHelperMockRecorder struct {
	mock *MockIOcrHelper
}

// NewMockIOcrHelper creates a new mock instance.
func NewMockIOcrHelper(ctrl *gomock.Controller) *MockIOcrHelper {
	mock := &MockIOcrHelper{ctrl: ctrl}
	mock.recorder = &MockIOcrHelperMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIOcrHelper) EXPECT() *MockIOcrHelperMockRecorder {
	return m.recorder
}

// FetchPanWithOcr mocks base method.
func (m *MockIOcrHelper) FetchPanWithOcr(ctx context.Context, details *wealthonboarding.OnboardingDetails, pan *types.DocumentProof) (*wealthonboarding.OcrDocumentProof, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchPanWithOcr", ctx, details, pan)
	ret0, _ := ret[0].(*wealthonboarding.OcrDocumentProof)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchPanWithOcr indicates an expected call of FetchPanWithOcr.
func (mr *MockIOcrHelperMockRecorder) FetchPanWithOcr(ctx, details, pan interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchPanWithOcr", reflect.TypeOf((*MockIOcrHelper)(nil).FetchPanWithOcr), ctx, details, pan)
}

// PopulatePoaWithOcr mocks base method.
func (m *MockIOcrHelper) PopulatePoaWithOcr(ctx context.Context, details *wealthonboarding.OnboardingDetails) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PopulatePoaWithOcr", ctx, details)
	ret0, _ := ret[0].(error)
	return ret0
}

// PopulatePoaWithOcr indicates an expected call of PopulatePoaWithOcr.
func (mr *MockIOcrHelperMockRecorder) PopulatePoaWithOcr(ctx, details interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PopulatePoaWithOcr", reflect.TypeOf((*MockIOcrHelper)(nil).PopulatePoaWithOcr), ctx, details)
}
