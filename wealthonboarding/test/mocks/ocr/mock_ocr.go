// Code generated by MockGen. DO NOT EDIT.
// Source: ./ocr.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	types "github.com/epifi/gamma/api/typesv2"
	wealthonboarding "github.com/epifi/gamma/api/wealthonboarding"
	gomock "github.com/golang/mock/gomock"
)

// MockOcr is a mock of Ocr interface.
type MockOcr struct {
	ctrl     *gomock.Controller
	recorder *MockOcrMockRecorder
}

// MockOcrMockRecorder is the mock recorder for MockOcr.
type MockOcrMockRecorder struct {
	mock *MockOcr
}

// NewMockOcr creates a new mock instance.
func NewMockOcr(ctrl *gomock.Controller) *MockOcr {
	mock := &MockOcr{ctrl: ctrl}
	mock.recorder = &MockOcrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOcr) EXPECT() *MockOcrMockRecorder {
	return m.recorder
}

// GetDocumentWithOCRResults mocks base method.
func (m *MockOcr) GetDocumentWithOCRResults(ctx context.Context, doc *types.DocumentProof) (*wealthonboarding.OcrDocumentProof, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDocumentWithOCRResults", ctx, doc)
	ret0, _ := ret[0].(*wealthonboarding.OcrDocumentProof)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDocumentWithOCRResults indicates an expected call of GetDocumentWithOCRResults.
func (mr *MockOcrMockRecorder) GetDocumentWithOCRResults(ctx, doc interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDocumentWithOCRResults", reflect.TypeOf((*MockOcr)(nil).GetDocumentWithOCRResults), ctx, doc)
}

// GetOcrDoc mocks base method.
func (m *MockOcr) GetOcrDoc(ctx context.Context, doc *types.DocumentProof, confidenceThresholds map[string]float64) (*wealthonboarding.OcrDocumentProof, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOcrDoc", ctx, doc, confidenceThresholds)
	ret0, _ := ret[0].(*wealthonboarding.OcrDocumentProof)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOcrDoc indicates an expected call of GetOcrDoc.
func (mr *MockOcrMockRecorder) GetOcrDoc(ctx, doc, confidenceThresholds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOcrDoc", reflect.TypeOf((*MockOcr)(nil).GetOcrDoc), ctx, doc, confidenceThresholds)
}
