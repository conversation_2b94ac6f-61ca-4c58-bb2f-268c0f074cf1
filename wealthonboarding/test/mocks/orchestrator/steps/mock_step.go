// Code generated by MockGen. DO NOT EDIT.
// Source: steps/step.go

// Package mock_steps is a generated GoMock package.
package mock_steps

import (
	context "context"
	reflect "reflect"

	wealthonboarding "github.com/epifi/gamma/api/wealthonboarding"
	steps "github.com/epifi/gamma/wealthonboarding/orchestrator/steps"
	gomock "github.com/golang/mock/gomock"
)

// MockIStep is a mock of IStep interface
type MockIStep struct {
	ctrl     *gomock.Controller
	recorder *MockIStepMockRecorder
}

// MockIStepMockRecorder is the mock recorder for MockIStep
type MockIStepMockRecorder struct {
	mock *MockIStep
}

// NewMockIStep creates a new mock instance
func NewMockIStep(ctrl *gomock.Controller) *MockIStep {
	mock := &MockIStep{ctrl: ctrl}
	mock.recorder = &MockIStepMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockIStep) EXPECT() *MockIStepMockRecorder {
	return m.recorder
}

// Perform mocks base method
func (m *MockIStep) Perform(ctx context.Context, details *wealthonboarding.OnboardingDetails) (*steps.StepExecutionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Perform", ctx, details)
	ret0, _ := ret[0].(*steps.StepExecutionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Perform indicates an expected call of Perform
func (mr *MockIStepMockRecorder) Perform(ctx, details interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Perform", reflect.TypeOf((*MockIStep)(nil).Perform), ctx, details)
}
