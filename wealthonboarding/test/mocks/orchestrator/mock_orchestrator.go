// Code generated by MockGen. DO NOT EDIT.
// Source: service.go

// Package mock_orchestrator is a generated GoMock package.
package mock_orchestrator

import (
	context "context"
	reflect "reflect"

	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	wealthonboarding "github.com/epifi/gamma/api/wealthonboarding"
	orchestrator_model "github.com/epifi/gamma/wealthonboarding/orchestrator/orchestrator_model"
	gomock "github.com/golang/mock/gomock"
)

// MockIService is a mock of IService interface.
type MockIService struct {
	ctrl     *gomock.Controller
	recorder *MockIServiceMockRecorder
}

// MockIServiceMockRecorder is the mock recorder for MockIService.
type MockIServiceMockRecorder struct {
	mock *MockIService
}

// NewMockIService creates a new mock instance.
func NewMockIService(ctrl *gomock.Controller) *MockIService {
	mock := &MockIService{ctrl: ctrl}
	mock.recorder = &MockIServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIService) EXPECT() *MockIServiceMockRecorder {
	return m.recorder
}

// Orchestrate mocks base method.
func (m *MockIService) Orchestrate(ctx context.Context, req *orchestrator_model.OrchestrateRequest) (*wealthonboarding.OnboardingDetails, *deeplink.Deeplink, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Orchestrate", ctx, req)
	ret0, _ := ret[0].(*wealthonboarding.OnboardingDetails)
	ret1, _ := ret[1].(*deeplink.Deeplink)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Orchestrate indicates an expected call of Orchestrate.
func (mr *MockIServiceMockRecorder) Orchestrate(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Orchestrate", reflect.TypeOf((*MockIService)(nil).Orchestrate), ctx, req)
}
