// Code generated by MockGen. DO NOT EDIT.
// Source: factory.go

// Package mock_orchestrator is a generated GoMock package.
package mock_orchestrator

import (
	context "context"
	reflect "reflect"

	wealthonboarding "github.com/epifi/gamma/api/wealthonboarding"
	steps "github.com/epifi/gamma/wealthonboarding/orchestrator/steps"
	gomock "github.com/golang/mock/gomock"
)

// MockIHandlerFactory is a mock of IHandlerFactory interface
type MockIHandlerFactory struct {
	ctrl     *gomock.Controller
	recorder *MockIHandlerFactoryMockRecorder
}

// MockIHandlerFactoryMockRecorder is the mock recorder for MockIHandlerFactory
type MockIHandlerFactoryMockRecorder struct {
	mock *MockIHandlerFactory
}

// NewMockIHandlerFactory creates a new mock instance
func NewMockIHandlerFactory(ctrl *gomock.Controller) *MockIHandlerFactory {
	mock := &MockIHandlerFactory{ctrl: ctrl}
	mock.recorder = &MockIHandlerFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockIHandlerFactory) EXPECT() *MockIHandlerFactoryMockRecorder {
	return m.recorder
}

// GetStepHandler mocks base method
func (m *MockIHandlerFactory) GetStepHandler(ctx context.Context, step wealthonboarding.OnboardingStep) (steps.IStep, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStepHandler", ctx, step)
	ret0, _ := ret[0].(steps.IStep)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStepHandler indicates an expected call of GetStepHandler
func (mr *MockIHandlerFactoryMockRecorder) GetStepHandler(ctx, step interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStepHandler", reflect.TypeOf((*MockIHandlerFactory)(nil).GetStepHandler), ctx, step)
}
