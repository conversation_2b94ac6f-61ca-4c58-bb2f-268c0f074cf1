// Code generated by MockGen. DO NOT EDIT.
// Source: ./service.go

// Package mock_comms is a generated GoMock package.
package mock_comms

import (
	context "context"
	reflect "reflect"

	wealthonboarding "github.com/epifi/gamma/api/wealthonboarding"
	comms "github.com/epifi/gamma/api/wealthonboarding/comms"
	comms0 "github.com/epifi/gamma/wealthonboarding/comms"
	gomock "github.com/golang/mock/gomock"
)

// MockIComms is a mock of IComms interface.
type MockIComms struct {
	ctrl     *gomock.Controller
	recorder *MockICommsMockRecorder
}

// MockICommsMockRecorder is the mock recorder for MockIComms.
type MockICommsMockRecorder struct {
	mock *MockIComms
}

// NewMockIComms creates a new mock instance.
func NewMockIComms(ctrl *gomock.Controller) *MockIComms {
	mock := &MockIComms{ctrl: ctrl}
	mock.recorder = &MockICommsMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIComms) EXPECT() *MockICommsMockRecorder {
	return m.recorder
}

// SendCommunication mocks base method.
func (m *MockIComms) SendCommunication(ctx context.Context, sendCommunicationReq *comms0.SendCommunicationRequest) *comms0.SendCommunicationResponse {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendCommunication", ctx, sendCommunicationReq)
	ret0, _ := ret[0].(*comms0.SendCommunicationResponse)
	return ret0
}

// SendCommunication indicates an expected call of SendCommunication.
func (mr *MockICommsMockRecorder) SendCommunication(ctx, sendCommunicationReq interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendCommunication", reflect.TypeOf((*MockIComms)(nil).SendCommunication), ctx, sendCommunicationReq)
}

// SendNotificationAsync mocks base method.
func (m *MockIComms) SendNotificationAsync(ctx context.Context, od *wealthonboarding.OnboardingDetails, notificationType comms.WealthCommunicationType) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendNotificationAsync", ctx, od, notificationType)
}

// SendNotificationAsync indicates an expected call of SendNotificationAsync.
func (mr *MockICommsMockRecorder) SendNotificationAsync(ctx, od, notificationType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendNotificationAsync", reflect.TypeOf((*MockIComms)(nil).SendNotificationAsync), ctx, od, notificationType)
}
