// Code generated by MockGen. DO NOT EDIT.
// Source: ./doc_helper.go

// Package mock_helpers is a generated GoMock package.
package mock_helpers

import (
	context "context"
	reflect "reflect"

	typesv2 "github.com/epifi/gamma/api/typesv2"
	gomock "github.com/golang/mock/gomock"
)

// MockDocumentHelper is a mock of DocumentHelper interface.
type MockDocumentHelper struct {
	ctrl     *gomock.Controller
	recorder *MockDocumentHelperMockRecorder
}

// MockDocumentHelperMockRecorder is the mock recorder for MockDocumentHelper.
type MockDocumentHelperMockRecorder struct {
	mock *MockDocumentHelper
}

// NewMockDocumentHelper creates a new mock instance.
func NewMockDocumentHelper(ctrl *gomock.Controller) *MockDocumentHelper {
	mock := &MockDocumentHelper{ctrl: ctrl}
	mock.recorder = &MockDocumentHelperMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDocumentHelper) EXPECT() *MockDocumentHelperMockRecorder {
	return m.recorder
}

// DownloadDoc mocks base method.
func (m *MockDocumentHelper) DownloadDoc(ctx context.Context, doc *typesv2.DocumentProof) (*typesv2.DocumentProof, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DownloadDoc", ctx, doc)
	ret0, _ := ret[0].(*typesv2.DocumentProof)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DownloadDoc indicates an expected call of DownloadDoc.
func (mr *MockDocumentHelperMockRecorder) DownloadDoc(ctx, doc interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadDoc", reflect.TypeOf((*MockDocumentHelper)(nil).DownloadDoc), ctx, doc)
}

// GetRawS3Document mocks base method.
func (m *MockDocumentHelper) GetRawS3Document(ctx context.Context, actorId string, doc *typesv2.DocumentProof) (*typesv2.DocumentProof, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRawS3Document", ctx, actorId, doc)
	ret0, _ := ret[0].(*typesv2.DocumentProof)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRawS3Document indicates an expected call of GetRawS3Document.
func (mr *MockDocumentHelperMockRecorder) GetRawS3Document(ctx, actorId, doc interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRawS3Document", reflect.TypeOf((*MockDocumentHelper)(nil).GetRawS3Document), ctx, actorId, doc)
}

// GetSignedUrl mocks base method.
func (m *MockDocumentHelper) GetSignedUrl(ctx context.Context, dst string, isUiCompatible bool, bucket string, expiryTime int64) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSignedUrl", ctx, dst, isUiCompatible, bucket, expiryTime)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSignedUrl indicates an expected call of GetSignedUrl.
func (mr *MockDocumentHelperMockRecorder) GetSignedUrl(ctx, dst, isUiCompatible, bucket, expiryTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSignedUrl", reflect.TypeOf((*MockDocumentHelper)(nil).GetSignedUrl), ctx, dst, isUiCompatible, bucket, expiryTime)
}

// UploadDoc mocks base method.
func (m *MockDocumentHelper) UploadDoc(ctx context.Context, actorId string, doc *typesv2.DocumentProof) (*typesv2.DocumentProof, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadDoc", ctx, actorId, doc)
	ret0, _ := ret[0].(*typesv2.DocumentProof)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadDoc indicates an expected call of UploadDoc.
func (mr *MockDocumentHelperMockRecorder) UploadDoc(ctx, actorId, doc interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadDoc", reflect.TypeOf((*MockDocumentHelper)(nil).UploadDoc), ctx, actorId, doc)
}

// UploadRawData mocks base method.
func (m *MockDocumentHelper) UploadRawData(ctx context.Context, s3Path string, rawData []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadRawData", ctx, s3Path, rawData)
	ret0, _ := ret[0].(error)
	return ret0
}

// UploadRawData indicates an expected call of UploadRawData.
func (mr *MockDocumentHelperMockRecorder) UploadRawData(ctx, s3Path, rawData interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadRawData", reflect.TypeOf((*MockDocumentHelper)(nil).UploadRawData), ctx, s3Path, rawData)
}

// UploadRawDoc mocks base method.
func (m *MockDocumentHelper) UploadRawDoc(ctx context.Context, actorId string, doc *typesv2.DocumentProof) (*typesv2.DocumentProof, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadRawDoc", ctx, actorId, doc)
	ret0, _ := ret[0].(*typesv2.DocumentProof)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadRawDoc indicates an expected call of UploadRawDoc.
func (mr *MockDocumentHelperMockRecorder) UploadRawDoc(ctx, actorId, doc interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadRawDoc", reflect.TypeOf((*MockDocumentHelper)(nil).UploadRawDoc), ctx, actorId, doc)
}
