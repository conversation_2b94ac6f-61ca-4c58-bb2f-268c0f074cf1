// Code generated by MockGen. DO NOT EDIT.
// Source: common.go

// Package mock_helpers is a generated GoMock package.
package mock_helpers

import (
	context "context"
	reflect "reflect"

	common "github.com/epifi/be-common/api/typesv2/common"
	wealthonboarding "github.com/epifi/gamma/api/wealthonboarding"
	user "github.com/epifi/gamma/api/wealthonboarding/user"
	gomock "github.com/golang/mock/gomock"
)

// MockICommonHelper is a mock of ICommonHelper interface.
type MockICommonHelper struct {
	ctrl     *gomock.Controller
	recorder *MockICommonHelperMockRecorder
}

// MockICommonHelperMockRecorder is the mock recorder for MockICommonHelper.
type MockICommonHelperMockRecorder struct {
	mock *MockICommonHelper
}

// NewMockICommonHelper creates a new mock instance.
func NewMockICommonHelper(ctrl *gomock.Controller) *MockICommonHelper {
	mock := &MockICommonHelper{ctrl: ctrl}
	mock.recorder = &MockICommonHelperMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICommonHelper) EXPECT() *MockICommonHelperMockRecorder {
	return m.recorder
}

// CreateAndPopulateUserPersonalDetails mocks base method.
func (m *MockICommonHelper) CreateAndPopulateUserPersonalDetails(ctx context.Context, actorId string) (*wealthonboarding.GetOrCreateUserResponse_UserDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAndPopulateUserPersonalDetails", ctx, actorId)
	ret0, _ := ret[0].(*wealthonboarding.GetOrCreateUserResponse_UserDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAndPopulateUserPersonalDetails indicates an expected call of CreateAndPopulateUserPersonalDetails.
func (mr *MockICommonHelperMockRecorder) CreateAndPopulateUserPersonalDetails(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAndPopulateUserPersonalDetails", reflect.TypeOf((*MockICommonHelper)(nil).CreateAndPopulateUserPersonalDetails), ctx, actorId)
}

// FetchBankAccountDetails mocks base method.
func (m *MockICommonHelper) FetchBankAccountDetails(ctx context.Context, actorId string) (*wealthonboarding.BankDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchBankAccountDetails", ctx, actorId)
	ret0, _ := ret[0].(*wealthonboarding.BankDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchBankAccountDetails indicates an expected call of FetchBankAccountDetails.
func (mr *MockICommonHelperMockRecorder) FetchBankAccountDetails(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchBankAccountDetails", reflect.TypeOf((*MockICommonHelper)(nil).FetchBankAccountDetails), ctx, actorId)
}

// IsWealthInternalUser mocks base method.
func (m *MockICommonHelper) IsWealthInternalUser(ctx context.Context, details *wealthonboarding.OnboardingDetails) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsWealthInternalUser", ctx, details)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsWealthInternalUser indicates an expected call of IsWealthInternalUser.
func (mr *MockICommonHelperMockRecorder) IsWealthInternalUser(ctx, details interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsWealthInternalUser", reflect.TypeOf((*MockICommonHelper)(nil).IsWealthInternalUser), ctx, details)
}

// SanitizeImage mocks base method.
func (m *MockICommonHelper) SanitizeImage(ctx context.Context, im *common.Image) (*common.Image, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SanitizeImage", ctx, im)
	ret0, _ := ret[0].(*common.Image)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SanitizeImage indicates an expected call of SanitizeImage.
func (mr *MockICommonHelperMockRecorder) SanitizeImage(ctx, im interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SanitizeImage", reflect.TypeOf((*MockICommonHelper)(nil).SanitizeImage), ctx, im)
}

// UpdateIncomeSlabForUser mocks base method.
func (m *MockICommonHelper) UpdateIncomeSlabForUser(ctx context.Context, details *wealthonboarding.OnboardingDetails) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateIncomeSlabForUser", ctx, details)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateIncomeSlabForUser indicates an expected call of UpdateIncomeSlabForUser.
func (mr *MockICommonHelperMockRecorder) UpdateIncomeSlabForUser(ctx, details interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateIncomeSlabForUser", reflect.TypeOf((*MockICommonHelper)(nil).UpdateIncomeSlabForUser), ctx, details)
}

// ValidateAndUpdateUserPersonalDetails mocks base method.
func (m *MockICommonHelper) ValidateAndUpdateUserPersonalDetails(ctx context.Context, usr *user.User) (*wealthonboarding.GetOrCreateUserResponse_UserDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateAndUpdateUserPersonalDetails", ctx, usr)
	ret0, _ := ret[0].(*wealthonboarding.GetOrCreateUserResponse_UserDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateAndUpdateUserPersonalDetails indicates an expected call of ValidateAndUpdateUserPersonalDetails.
func (mr *MockICommonHelperMockRecorder) ValidateAndUpdateUserPersonalDetails(ctx, usr interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateAndUpdateUserPersonalDetails", reflect.TypeOf((*MockICommonHelper)(nil).ValidateAndUpdateUserPersonalDetails), ctx, usr)
}
