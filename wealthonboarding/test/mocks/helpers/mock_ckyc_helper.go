// Code generated by MockGen. DO NOT EDIT.
// Source: wealthonboarding/helper/ckyc_helper.go

// Package mock_helpers is a generated GoMock package.
package mock_helpers

import (
	reflect "reflect"

	wealthonboarding "github.com/epifi/gamma/api/wealthonboarding"
	gomock "github.com/golang/mock/gomock"
	context "golang.org/x/net/context"
)

// MockICkycHelper is a mock of ICkycHelper interface.
type MockICkycHelper struct {
	ctrl     *gomock.Controller
	recorder *MockICkycHelperMockRecorder
}

// MockICkycHelperMockRecorder is the mock recorder for MockICkycHelper.
type MockICkycHelperMockRecorder struct {
	mock *MockICkycHelper
}

// NewMockICkycHelper creates a new mock instance.
func NewMockICkycHelper(ctrl *gomock.Controller) *MockICkycHelper {
	mock := &MockICkycHelper{ctrl: ctrl}
	mock.recorder = &MockICkycHelperMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICkycHelper) EXPECT() *MockICkycHelperMockRecorder {
	return m.recorder
}

// FetchAndPopulateCkycSearchData mocks base method.
func (m *MockICkycHelper) FetchAndPopulateCkycSearchData(ctx context.Context, details *wealthonboarding.OnboardingDetails) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchAndPopulateCkycSearchData", ctx, details)
	ret0, _ := ret[0].(error)
	return ret0
}

// FetchAndPopulateCkycSearchData indicates an expected call of FetchAndPopulateCkycSearchData.
func (mr *MockICkycHelperMockRecorder) FetchAndPopulateCkycSearchData(ctx, details interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchAndPopulateCkycSearchData", reflect.TypeOf((*MockICkycHelper)(nil).FetchAndPopulateCkycSearchData), ctx, details)
}

// FetchPopulateValidateCkycDownloadData mocks base method.
func (m *MockICkycHelper) FetchPopulateValidateCkycDownloadData(ctx context.Context, details *wealthonboarding.OnboardingDetails) (wealthonboarding.OnboardingStepSubStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchPopulateValidateCkycDownloadData", ctx, details)
	ret0, _ := ret[0].(wealthonboarding.OnboardingStepSubStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchPopulateValidateCkycDownloadData indicates an expected call of FetchPopulateValidateCkycDownloadData.
func (mr *MockICkycHelperMockRecorder) FetchPopulateValidateCkycDownloadData(ctx, details interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchPopulateValidateCkycDownloadData", reflect.TypeOf((*MockICkycHelper)(nil).FetchPopulateValidateCkycDownloadData), ctx, details)
}
