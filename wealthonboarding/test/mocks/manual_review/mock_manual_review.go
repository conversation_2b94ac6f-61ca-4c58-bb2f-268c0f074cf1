// Code generated by MockGen. DO NOT EDIT.
// Source: wealthonboarding/manual_review/manual_review.go

// Package mock_manualreview is a generated GoMock package.
package mock_manualreview

import (
	context "context"
	reflect "reflect"

	wealthonboarding "github.com/epifi/gamma/api/wealthonboarding"
	gomock "github.com/golang/mock/gomock"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

// MockIManualReview is a mock of IManualReview interface.
type MockIManualReview struct {
	ctrl     *gomock.Controller
	recorder *MockIManualReviewMockRecorder
}

// MockIManualReviewMockRecorder is the mock recorder for MockIManualReview.
type MockIManualReviewMockRecorder struct {
	mock *MockIManualReview
}

// NewMockIManualReview creates a new mock instance.
func NewMockIManualReview(ctrl *gomock.Controller) *MockIManualReview {
	mock := &MockIManualReview{ctrl: ctrl}
	mock.recorder = &MockIManualReviewMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIManualReview) EXPECT() *MockIManualReviewMockRecorder {
	return m.recorder
}

// AddToReview mocks base method.
func (m *MockIManualReview) AddToReview(ctx context.Context, reviewPayload *wealthonboarding.ReviewPayload, itemType wealthonboarding.ItemType) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddToReview", ctx, reviewPayload, itemType)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddToReview indicates an expected call of AddToReview.
func (mr *MockIManualReviewMockRecorder) AddToReview(ctx, reviewPayload, itemType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddToReview", reflect.TypeOf((*MockIManualReview)(nil).AddToReview), ctx, reviewPayload, itemType)
}

// CheckStatus mocks base method.
func (m *MockIManualReview) CheckStatus(ctx context.Context, id string) (wealthonboarding.ReviewStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckStatus", ctx, id)
	ret0, _ := ret[0].(wealthonboarding.ReviewStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckStatus indicates an expected call of CheckStatus.
func (mr *MockIManualReviewMockRecorder) CheckStatus(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckStatus", reflect.TypeOf((*MockIManualReview)(nil).CheckStatus), ctx, id)
}

// GetReviewItem mocks base method.
func (m *MockIManualReview) GetReviewItem(ctx context.Context, id string) (*wealthonboarding.ManualReview, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReviewItem", ctx, id)
	ret0, _ := ret[0].(*wealthonboarding.ManualReview)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReviewItem indicates an expected call of GetReviewItem.
func (mr *MockIManualReviewMockRecorder) GetReviewItem(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReviewItem", reflect.TypeOf((*MockIManualReview)(nil).GetReviewItem), ctx, id)
}

// MarkReviewCompleted mocks base method.
func (m *MockIManualReview) MarkReviewCompleted(ctx context.Context, id string, reviewStatus wealthonboarding.ReviewStatus, reviewPayload *wealthonboarding.ReviewPayload, reviewedAt *timestamppb.Timestamp) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkReviewCompleted", ctx, id, reviewStatus, reviewPayload, reviewedAt)
	ret0, _ := ret[0].(error)
	return ret0
}

// MarkReviewCompleted indicates an expected call of MarkReviewCompleted.
func (mr *MockIManualReviewMockRecorder) MarkReviewCompleted(ctx, id, reviewStatus, reviewPayload, reviewedAt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkReviewCompleted", reflect.TypeOf((*MockIManualReview)(nil).MarkReviewCompleted), ctx, id, reviewStatus, reviewPayload, reviewedAt)
}

// SubmitForReview mocks base method.
func (m *MockIManualReview) SubmitForReview(ctx context.Context, actorId string, ids []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitForReview", ctx, actorId, ids)
	ret0, _ := ret[0].(error)
	return ret0
}

// SubmitForReview indicates an expected call of SubmitForReview.
func (mr *MockIManualReviewMockRecorder) SubmitForReview(ctx, actorId, ids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitForReview", reflect.TypeOf((*MockIManualReview)(nil).SubmitForReview), ctx, actorId, ids)
}

// UpdateStatus mocks base method.
func (m *MockIManualReview) UpdateStatus(ctx context.Context, id string, reviewStatus wealthonboarding.ReviewStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatus", ctx, id, reviewStatus)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStatus indicates an expected call of UpdateStatus.
func (mr *MockIManualReviewMockRecorder) UpdateStatus(ctx, id, reviewStatus interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatus", reflect.TypeOf((*MockIManualReview)(nil).UpdateStatus), ctx, id, reviewStatus)
}
