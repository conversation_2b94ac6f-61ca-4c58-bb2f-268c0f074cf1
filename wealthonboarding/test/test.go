package test

import (
	"log"

	"go.uber.org/zap"

	cmdtypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	testv2 "github.com/epifi/be-common/pkg/test/v2"

	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/config/worker"
	workerConf "github.com/epifi/gamma/wealthonboarding/config/worker"
)

// InitTestServer initiates components needed for tests
// Will be invoked from TestMain but can be called from individual tests for *special* cases only
func InitTestServer() (string, *config.Config, *worker.Config, cmdtypes.EpifiWealthCRDB, func()) {
	// Init config
	conf, err := config.Load()
	if err != nil {
		log.Fatal("failed to load config", err)
	}

	workerConf, err := worker.Load()
	if err != nil {
		log.Fatal("failed to load worker config", err)
	}

	// Setup logger
	logger.Init(conf.Application.Environment)

	// Init db connection
	db, dbName, err := testv2.NewRandomScopedCrdbWithConfig(conf.EpifiDb, false)
	if err != nil {
		logger.Panic("failed to connect to db", zap.Error(err))
	}
	storagev2.InitDefaultCRDBTransactionExecutor(db)

	err = testv2.DropTestDatabase(db, conf.EpifiDb.GetName())
	if err != nil {
		logger.Fatal("failed to drop DB", zap.Error(err))
	}

	sqlDb, _ := db.DB()
	return dbName, conf, workerConf, db, func() {
		_ = logger.Log.Sync()
		_ = sqlDb.Close()
	}
}

func InitTestWorker() (*workerConf.Config, func()) {
	conf, err := workerConf.Load()
	if err != nil {
		log.Fatal("failed to load worker config", err)
	}
	logger.Init(conf.Application.Environment)
	err = epifitemporal.InitWorkflowParams(conf.WorkflowParamsList.GetWorkflowParamsMap())
	if err != nil {
		logger.Panic("failed to load workflow params", zap.Error(err))
	}

	err = epifitemporal.InitDefaultActivityParams(conf.DefaultActivityParamsList.GetActivityParamsMap())
	if err != nil {
		logger.Panic("failed to load default activity params", zap.Error(err))
	}

	return conf, func() {
		_ = logger.Log.Sync()
	}
}
