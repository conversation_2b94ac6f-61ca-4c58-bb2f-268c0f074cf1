package workflow

import (
	"fmt"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	woActivityPb "github.com/epifi/gamma/api/wealthonboarding/activity"
	workflow2 "github.com/epifi/gamma/api/wealthonboarding/workflow"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	woNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/wealthonboarding"
	"github.com/epifi/be-common/pkg/logger"
)

/*
WealthOnboardingDocumentExtraction defines the workflow to orchestrate document extraction from kra docket
*/
func WealthOnboardingDocumentExtraction(ctx workflow.Context, _ *workflowPb.Request) error {
	wfReqID := workflow.GetInfo(ctx).WorkflowExecution.ID
	lg := workflow.GetLogger(ctx)
	var workflowStageStatus stagePb.Status

	// getting unmarshalled workflow processing params and workflow processing request
	docExtractionReq, wfProcessingParams, err := getWorkflowProcessingReqParams(ctx)
	if err != nil {
		lg.Error("error in getting workflow processing req params", zap.Error(err))
		return err
	}

	clientReqId := wfProcessingParams.GetClientReqId().GetId()

	// stage 1: extract document from docket
	workflowStageStatus, err = extractDocumentFromDocket(ctx, docExtractionReq, wfReqID, clientReqId)
	if err != nil {
		lg.Error("error in extracting document", zap.String(logger.CLIENT_REQUEST_ID, clientReqId), zap.Error(err))
		return err
	}
	if workflowStageStatus == stagePb.Status_SUCCESSFUL {
		lg.Info("document successfully extracted from docket")
		// PAN extraction from docket was successful, no need to proceed to rest of the stages
		return nil
	}

	// stage 2
	_, err = extractPANFromCKYC(ctx, docExtractionReq.GetActorId(), wfReqID, clientReqId)
	if err != nil {
		lg.Error("error extracting PAN from CKYC", zap.String(logger.CLIENT_REQUEST_ID, clientReqId), zap.Error(err))
		return err
	}
	// Note: Even in case of unsuccessful pan extraction, this workflow is marked complete,
	// so that user can proceed to manual PAN upload flow in US stocks broker account onboarding

	return nil
}

func extractDocumentFromDocket(ctx workflow.Context, docExtractionReq *workflow2.DocumentExtractionRequest, wfReqID string, clientReqId string) (stagePb.Status, error) {
	stage := workflowPb.Stage_WEALTH_ONBOARDING_DOCUMENT_EXTRACTION_EXTRACT
	// Step 1: Initiate workflow stage extraction
	if err := initiateWorkflowStage(ctx, stage, stagePb.Status_INITIATED, clientReqId, wfReqID); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("InitiateWorkflowStageV2 activity failure: %w", err)
	}

	resp := &woActivityPb.ExtractDocumentResponse{}
	// Step 2: execute activity to send order to vendor
	err := activityPkg.Execute(ctx, woNs.ExtractDocument, resp, &woActivityPb.ExtractDocumentRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   OWNERSHIP,
		},
		DocumentType: docExtractionReq.GetDocumentType(),
		ActorId:      docExtractionReq.GetActorId(),
	})
	workflowStageStatus := stagePb.GetWorkflowStageStatusForErr(err)
	if err = updateWorkflowStage(ctx, stage, workflowStageStatus, clientReqId, wfReqID); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, err
	}

	return workflowStageStatus, nil
}

func extractPANFromCKYC(ctx workflow.Context, actorId, wfReqID, clientReqId string) (stagePb.Status, error) {
	stage := workflowPb.Stage_WEALTH_ONBOARDING_EXTRACT_DOCUMENT_FROM_CKYC
	if err := initiateWorkflowStage(ctx, stage, stagePb.Status_INITIATED, clientReqId, wfReqID); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("activity failure: %w", err)
	}
	resp := &woActivityPb.ExtractDocumentResponse{}
	err := activityPkg.Execute(ctx, woNs.ExtractDocumentFromCKYC, resp, &woActivityPb.VerifyDocumentFromCKYCRequest{
		RequestHeader: &activityPb.RequestHeader{ClientReqId: clientReqId, Ownership: OWNERSHIP},
		ActorId:       actorId,
	})
	workflowStageStatus := stagePb.GetWorkflowStageStatusForErr(err)
	if err = updateWorkflowStage(ctx, stage, workflowStageStatus, clientReqId, wfReqID); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, err
	}
	return workflowStageStatus, nil
}
