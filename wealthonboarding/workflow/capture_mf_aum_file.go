package workflow

import (
	"fmt"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	mfNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/mutualfund"

	mfActivityPb "github.com/epifi/gamma/api/investment/mutualfund/activity"
	workflow2 "github.com/epifi/gamma/api/wealthonboarding/workflow"
)

func CaptureMfAumFile(ctx workflow.Context, _ *workflowPb.Request) (*emptyPb.Empty, error) {
	logger := workflow.GetLogger(ctx)

	captureMfAumFileReq, wfProcessingParams, err := getCaptureMfAumFileRequest(ctx)
	if err != nil {
		logger.Error("error in getting mf aum reconciliation request", zap.Error(err))
	}

	clientReqId := wfProcessingParams.GetClientReqId().GetId()

	// stage 1: process mf aum recon file from s3 and upload map to s3
	parseAndGroupAumFileResponse, wfStageStatus, err := parseAndGroupAumFile(ctx, captureMfAumFileReq, clientReqId)
	if err != nil {
		logger.Error("error in processing mf aum reconciliation request", zap.Error(err))
		return nil, err
	}
	if wfStageStatus != stagePb.Status_SUCCESSFUL {
		return &emptyPb.Empty{}, nil
	}

	// stage 2: correct folio mismatch
	correctFolioMismatchResponse, wfStageStatus, err := correctFolioMismatch(ctx, parseAndGroupAumFileResponse, clientReqId)
	if err != nil {
		logger.Error("error in processing mf aum reconciliation request", zap.Error(err))
		return nil, err
	}
	if wfStageStatus != stagePb.Status_SUCCESSFUL {
		return &emptyPb.Empty{}, nil
	}

	// stage 3: send Slack message containing list of folios and their errors
	_, err = publishFileCaptureReport(ctx, correctFolioMismatchResponse, clientReqId)
	if err != nil {
		logger.Error("error in processing mf aum reconciliation request", zap.Error(err))
		return nil, err
	}

	return &emptyPb.Empty{}, nil
}

//nolint:dupl
func parseAndGroupAumFile(ctx workflow.Context, captureMfAumFileReq *workflow2.CaptureMfAumFileRequest, clientReqId string) (*mfActivityPb.ParseAndGroupAumFileResponse, stagePb.Status, error) {
	stage := mfNs.ParseAndGroupAumFileStage
	if err := celestialPkg.InitiateWorkflowStage(ctx, stage, stagePb.Status_INITIATED); err != nil {
		return nil, stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("InitiateWorkflowStageV2 activity failure: %w", err)
	}

	resp := &mfActivityPb.ParseAndGroupAumFileResponse{}

	err := activityPkg.Execute(ctx, mfNs.ParseAndGroupAumFile, resp, &mfActivityPb.ParseAndGroupAumFileRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   OWNERSHIP,
		},
		AumFileUrl: captureMfAumFileReq.GetAumFileUrl(),
		RtaType:    captureMfAumFileReq.GetRtaType(),
	})
	wfStageStatus := celestialPkg.GetWorkflowStageStatusForErr(err)
	if err = celestialPkg.UpdateWorkflowStage(ctx, stage, wfStageStatus); err != nil {
		return nil, stagePb.Status_STATUS_UNSPECIFIED, err
	}

	return resp, wfStageStatus, nil
}

//nolint:dupl
func correctFolioMismatch(ctx workflow.Context, parseAndGroupAumFileResponse *mfActivityPb.ParseAndGroupAumFileResponse, clientReqId string) (*mfActivityPb.CorrectFolioMismatchResponse, stagePb.Status, error) {
	stage := mfNs.CorrectFolioMismatchStage
	if err := celestialPkg.InitiateWorkflowStage(ctx, stage, stagePb.Status_INITIATED); err != nil {
		return nil, stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("InitiateWorkflowStageV2 activity failure: %w", err)
	}

	resp := &mfActivityPb.CorrectFolioMismatchResponse{}

	err := activityPkg.Execute(ctx, mfNs.CorrectFolioMismatch, resp, &mfActivityPb.CorrectFolioMismatchRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   OWNERSHIP,
		},
		GroupedFolioMapUrl: parseAndGroupAumFileResponse.GetGroupedFolioMapUrl(),
		FolioStatusUrl:     parseAndGroupAumFileResponse.GetFolioStatusUrl(),
	})
	wfStageStatus := stagePb.GetWorkflowStageStatusForErr(err)
	if err = celestialPkg.UpdateWorkflowStage(ctx, stage, wfStageStatus); err != nil {
		return nil, stagePb.Status_STATUS_UNSPECIFIED, err
	}

	return resp, wfStageStatus, nil
}

func publishFileCaptureReport(ctx workflow.Context, correctFolioMismatchResp *mfActivityPb.CorrectFolioMismatchResponse, clientReqId string) (stagePb.Status, error) {
	stage := mfNs.PublishFileCaptureReportStage
	if err := celestialPkg.InitiateWorkflowStage(ctx, stage, stagePb.Status_INITIATED); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("InitiateWorkflowStageV2 activity failure: %w", err)
	}

	resp := &mfActivityPb.PublishFileCaptureReportResponse{}

	err := activityPkg.Execute(ctx, mfNs.PublishFileCaptureReport, resp, &mfActivityPb.PublishFileCaptureReportRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   OWNERSHIP,
		},
		FolioStatusUrl: correctFolioMismatchResp.GetFolioStatusUrl(),
	})
	wfStageStatus := stagePb.GetWorkflowStageStatusForErr(err)
	if err = celestialPkg.UpdateWorkflowStage(ctx, stage, wfStageStatus); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, err
	}

	return wfStageStatus, nil
}
