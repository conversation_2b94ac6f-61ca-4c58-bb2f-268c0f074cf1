# Mutual Fund Workflow Documentation

## Overview
The workflows in the `investment.mutualfund` folder are currently under the `wealthonboarding` namespace and will be hosted there. However, if a `mutualfund` namespace is created, these workflows should be migrated accordingly.

## CaptureMfAumFile Workflow
### Description
The `CaptureMfAumFile` workflow is responsible for running the reconciliation process for a given Mutual Fund AUM (Assets Under Management) recon file. The workflow includes the following steps:
1. **Parse and group the AUM recon file** - Extracting relevant data from the input file based on the RTA(CAMS/KARVY).
2. **Correct any folio mismatches** - Validating and matching data as per business rules. Capturing any errors encountered during parsing or processing
3. **Publish file capture report** - Any failure messages will be collated and sent to a designated Slack channel for further debugging.

## Error Management
- Errors encountered during parsing or processing are stored in a designated location.
- Notifications with relevant error details are sent to a specified Slack channel for prompt debugging.
