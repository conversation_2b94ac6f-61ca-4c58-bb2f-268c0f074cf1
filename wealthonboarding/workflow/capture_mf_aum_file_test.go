package workflow

import (
	"os"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/protobuf/encoding/protojson"

	celestialPb "github.com/epifi/be-common/api/celestial/activity"
	celestialWorkflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	lambdaMocks "github.com/epifi/be-common/pkg/aws/lambda/mocks"
	mockS3 "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	dateTimeMock "github.com/epifi/be-common/pkg/datetime/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	epifitemporalLogging "github.com/epifi/be-common/pkg/epifitemporal/logging"
	mfNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/mutualfund"
	epifiTemporalTest "github.com/epifi/be-common/pkg/epifitemporal/test"
	"github.com/epifi/be-common/pkg/logger"
	storagev2Mocks "github.com/epifi/be-common/pkg/storage/v2/mocks"

	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	mfActivityPb "github.com/epifi/gamma/api/investment/mutualfund/activity"
	catalogMocks "github.com/epifi/gamma/api/investment/mutualfund/catalog/mocks"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	mockOcr "github.com/epifi/gamma/api/vendorgateway/wealth/inhouseocr/mocks"
	woWorkflowPb "github.com/epifi/gamma/api/wealthonboarding/workflow"
	celestialActivity "github.com/epifi/gamma/celestial/activity/v2"
	mfActivity "github.com/epifi/gamma/investment/mutualfund/activity"
	mfDaoMocks "github.com/epifi/gamma/investment/mutualfund/dao/mocks"
	"github.com/epifi/gamma/wealthonboarding/config/worker"
	"github.com/epifi/gamma/wealthonboarding/test"
	woHelperMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/helpers"
	woOcrMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/ocr"
)

type mockActivity struct {
	enable   bool
	activity epifitemporal.Activity
	req      interface{}
	res      []interface{}
}

const (
	aumFileUrl        = "aum-file-url"
	defaultWorkflowID = "default-test-workflow-id"
	folioStatusUrl    = "folio-status-url"
)

var (
	clientReqId = uuid.New().String()
	wts         epifiTemporalTest.WorkflowTestSuite
	conf        *worker.Config
)

func TestMfAumReconciliation(t *testing.T) {
	wfReqPayload, _ := protojson.Marshal(&woWorkflowPb.CaptureMfAumFileRequest{
		AumFileUrl: aumFileUrl,
		RtaType:    commonvgpb.Vendor_CAMS,
	})

	tests := []struct {
		name           string
		mockActivities []*mockActivity
		wantErr        bool
	}{
		{
			name: "Successful mf aum recon",
			mockActivities: func() []*mockActivity {
				activities := make([]*mockActivity, 0)
				activities = append(activities, []*mockActivity{
					{
						enable:   true,
						activity: epifitemporal.GetWorkflowProcessingParamsV2,
						req: &celestialPb.GetWorkflowProcessingParamsV2Request{
							RequestHeader: &celestialPb.RequestHeader{
								Ownership: OWNERSHIP,
							},
							WfReqId: defaultWorkflowID,
						},
						res: []interface{}{
							&celestialPb.GetWorkflowProcessingParamsV2Response{
								WfReqParams: &celestialWorkflowPb.ProcessingParams{
									Payload: wfReqPayload,
									ClientReqId: &celestialWorkflowPb.ClientReqId{
										Id:     clientReqId,
										Client: celestialWorkflowPb.Client_WEALTH_ONBOARDING,
									},
								},
							}, nil,
						},
					},
					mockInitiateWorkflowStageV2(celestialPkg.GetStageEnumFromStage(mfNs.ParseAndGroupAumFileStage)),
					{
						enable:   true,
						activity: mfNs.ParseAndGroupAumFile,
						req: &mfActivityPb.ParseAndGroupAumFileRequest{
							RequestHeader: &celestialPb.RequestHeader{
								ClientReqId: clientReqId,
								Ownership:   OWNERSHIP,
							},
							AumFileUrl: aumFileUrl,
							RtaType:    commonvgpb.Vendor_CAMS,
						},
						res: []interface{}{&mfActivityPb.ParseAndGroupAumFileResponse{
							GroupedFolioMapUrl: aumFileUrl,
							FolioStatusUrl:     folioStatusUrl,
						}, nil},
					},
					mockInitiateWorkflowStageV2(celestialPkg.GetStageEnumFromStage(mfNs.CorrectFolioMismatchStage)),
					{
						enable:   true,
						activity: mfNs.CorrectFolioMismatch,
						req: &mfActivityPb.CorrectFolioMismatchRequest{
							RequestHeader: &celestialPb.RequestHeader{
								ClientReqId: clientReqId,
								Ownership:   OWNERSHIP,
							},
							GroupedFolioMapUrl: aumFileUrl,
							FolioStatusUrl:     folioStatusUrl,
						},
						res: []interface{}{&mfActivityPb.CorrectFolioMismatchResponse{
							FolioStatusUrl: folioStatusUrl,
						}, nil},
					},
					mockInitiateWorkflowStageV2(celestialPkg.GetStageEnumFromStage(mfNs.PublishFileCaptureReportStage)),
					{
						enable:   true,
						activity: mfNs.PublishFileCaptureReport,
						req: &mfActivityPb.PublishFileCaptureReportRequest{
							RequestHeader: &celestialPb.RequestHeader{
								ClientReqId: clientReqId,
								Ownership:   OWNERSHIP,
							},
							FolioStatusUrl: folioStatusUrl,
						},
						res: []interface{}{&mfActivityPb.PublishFileCaptureReportResponse{}, nil},
					},
				}...)
				activities = append(activities, mockUpdateWfStageV2(stagePb.Status_SUCCESSFUL, celestialPkg.GetStageEnumFromStage(mfNs.ParseAndGroupAumFileStage))...)
				activities = append(activities, mockUpdateWfStageV2(stagePb.Status_SUCCESSFUL, celestialPkg.GetStageEnumFromStage(mfNs.CorrectFolioMismatchStage))...)
				activities = append(activities, mockUpdateWfStageV2(stagePb.Status_SUCCESSFUL, celestialPkg.GetStageEnumFromStage(mfNs.PublishFileCaptureReportStage))...)
				return activities
			}(),
			wantErr: false,
		},
		{
			name: "Error when send folio errors slack message fails",
			mockActivities: func() []*mockActivity {
				activities := make([]*mockActivity, 0)
				activities = append(activities, []*mockActivity{
					{
						enable:   true,
						activity: epifitemporal.GetWorkflowProcessingParamsV2,
						req: &celestialPb.GetWorkflowProcessingParamsV2Request{
							RequestHeader: &celestialPb.RequestHeader{
								Ownership: OWNERSHIP,
							},
							WfReqId: defaultWorkflowID,
						},
						res: []interface{}{
							&celestialPb.GetWorkflowProcessingParamsV2Response{
								WfReqParams: &celestialWorkflowPb.ProcessingParams{
									Payload: wfReqPayload,
									ClientReqId: &celestialWorkflowPb.ClientReqId{
										Id:     clientReqId,
										Client: celestialWorkflowPb.Client_WEALTH_ONBOARDING,
									},
								},
							}, nil,
						},
					},
					mockInitiateWorkflowStageV2(celestialPkg.GetStageEnumFromStage(mfNs.ParseAndGroupAumFileStage)),
					{
						enable:   true,
						activity: mfNs.ParseAndGroupAumFile,
						req: &mfActivityPb.ParseAndGroupAumFileRequest{
							RequestHeader: &celestialPb.RequestHeader{
								ClientReqId: clientReqId,
								Ownership:   OWNERSHIP,
							},
							AumFileUrl: aumFileUrl,
							RtaType:    commonvgpb.Vendor_CAMS,
						},
						res: []interface{}{&mfActivityPb.ParseAndGroupAumFileResponse{
							GroupedFolioMapUrl: aumFileUrl,
							FolioStatusUrl:     folioStatusUrl,
						}, nil},
					},
					mockInitiateWorkflowStageV2(celestialPkg.GetStageEnumFromStage(mfNs.CorrectFolioMismatchStage)),
					{
						enable:   true,
						activity: mfNs.CorrectFolioMismatch,
						req: &mfActivityPb.CorrectFolioMismatchRequest{
							RequestHeader: &celestialPb.RequestHeader{
								ClientReqId: clientReqId,
								Ownership:   OWNERSHIP,
							},
							GroupedFolioMapUrl: aumFileUrl,
							FolioStatusUrl:     folioStatusUrl,
						},
						res: []interface{}{&mfActivityPb.CorrectFolioMismatchResponse{
							FolioStatusUrl: folioStatusUrl,
						}, nil},
					},
					mockInitiateWorkflowStageV2(celestialPkg.GetStageEnumFromStage(mfNs.PublishFileCaptureReportStage)),
					{
						enable:   true,
						activity: mfNs.PublishFileCaptureReport,
						req: &mfActivityPb.PublishFileCaptureReportRequest{
							RequestHeader: &celestialPb.RequestHeader{
								ClientReqId: clientReqId,
								Ownership:   OWNERSHIP,
							},
							FolioStatusUrl: folioStatusUrl,
						},
						res: []interface{}{nil, epifierrors.ErrPermanent},
					},
				}...)
				activities = append(activities, mockUpdateWfStageV2(stagePb.Status_SUCCESSFUL, celestialPkg.GetStageEnumFromStage(mfNs.ParseAndGroupAumFileStage))...)
				activities = append(activities, mockUpdateWfStageV2(stagePb.Status_SUCCESSFUL, celestialPkg.GetStageEnumFromStage(mfNs.CorrectFolioMismatchStage))...)
				activities = append(activities, mockUpdateWfStageV2(stagePb.Status_FAILED, celestialPkg.GetStageEnumFromStage(mfNs.PublishFileCaptureReportStage))...)
				return activities
			}(),
			wantErr: false,
		},
		{
			name: "Error when correct folio mismatch fails",
			mockActivities: func() []*mockActivity {
				activities := make([]*mockActivity, 0)
				activities = append(activities, []*mockActivity{
					{
						enable:   true,
						activity: epifitemporal.GetWorkflowProcessingParamsV2,
						req: &celestialPb.GetWorkflowProcessingParamsV2Request{
							RequestHeader: &celestialPb.RequestHeader{
								Ownership: OWNERSHIP,
							},
							WfReqId: defaultWorkflowID,
						},
						res: []interface{}{
							&celestialPb.GetWorkflowProcessingParamsV2Response{
								WfReqParams: &celestialWorkflowPb.ProcessingParams{
									Payload: wfReqPayload,
									ClientReqId: &celestialWorkflowPb.ClientReqId{
										Id:     clientReqId,
										Client: celestialWorkflowPb.Client_WEALTH_ONBOARDING,
									},
								},
							}, nil,
						},
					},
					mockInitiateWorkflowStageV2(celestialPkg.GetStageEnumFromStage(mfNs.ParseAndGroupAumFileStage)),
					{
						enable:   true,
						activity: mfNs.ParseAndGroupAumFile,
						req: &mfActivityPb.ParseAndGroupAumFileRequest{
							RequestHeader: &celestialPb.RequestHeader{
								ClientReqId: clientReqId,
								Ownership:   OWNERSHIP,
							},
							AumFileUrl: aumFileUrl,
							RtaType:    commonvgpb.Vendor_CAMS,
						},
						res: []interface{}{&mfActivityPb.ParseAndGroupAumFileResponse{
							GroupedFolioMapUrl: aumFileUrl,
							FolioStatusUrl:     folioStatusUrl,
						}, nil},
					},
					mockInitiateWorkflowStageV2(celestialPkg.GetStageEnumFromStage(mfNs.CorrectFolioMismatchStage)),
					{
						enable:   true,
						activity: mfNs.CorrectFolioMismatch,
						req: &mfActivityPb.CorrectFolioMismatchRequest{
							RequestHeader: &celestialPb.RequestHeader{
								ClientReqId: clientReqId,
								Ownership:   OWNERSHIP,
							},
							GroupedFolioMapUrl: aumFileUrl,
							FolioStatusUrl:     folioStatusUrl,
						},
						res: []interface{}{nil, epifierrors.ErrPermanent},
					},
				}...)
				activities = append(activities, mockUpdateWfStageV2(stagePb.Status_SUCCESSFUL, celestialPkg.GetStageEnumFromStage(mfNs.ParseAndGroupAumFileStage))...)
				activities = append(activities, mockUpdateWfStageV2(stagePb.Status_FAILED, celestialPkg.GetStageEnumFromStage(mfNs.CorrectFolioMismatchStage))...)
				return activities
			}(),
			wantErr: false,
		},
		{
			name: "Error when process aum recon file fails",
			mockActivities: func() []*mockActivity {
				activities := make([]*mockActivity, 0)
				activities = append(activities, []*mockActivity{
					{
						enable:   true,
						activity: epifitemporal.GetWorkflowProcessingParamsV2,
						req: &celestialPb.GetWorkflowProcessingParamsV2Request{
							RequestHeader: &celestialPb.RequestHeader{
								Ownership: OWNERSHIP,
							},
							WfReqId: defaultWorkflowID,
						},
						res: []interface{}{
							&celestialPb.GetWorkflowProcessingParamsV2Response{
								WfReqParams: &celestialWorkflowPb.ProcessingParams{
									Payload: wfReqPayload,
									ClientReqId: &celestialWorkflowPb.ClientReqId{
										Id:     clientReqId,
										Client: celestialWorkflowPb.Client_WEALTH_ONBOARDING,
									},
								},
							}, nil,
						},
					},
					mockInitiateWorkflowStageV2(celestialPkg.GetStageEnumFromStage(mfNs.ParseAndGroupAumFileStage)),
					{
						enable:   true,
						activity: mfNs.ParseAndGroupAumFile,
						req: &mfActivityPb.ParseAndGroupAumFileRequest{
							RequestHeader: &celestialPb.RequestHeader{
								ClientReqId: clientReqId,
								Ownership:   OWNERSHIP,
							},
							AumFileUrl: aumFileUrl,
							RtaType:    commonvgpb.Vendor_CAMS,
						},
						res: []interface{}{nil, epifierrors.ErrPermanent},
					},
				}...)
				activities = append(activities, mockUpdateWfStageV2(stagePb.Status_FAILED, celestialPkg.GetStageEnumFromStage(mfNs.ParseAndGroupAumFileStage))...)
				return activities
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			env := wts.NewTestWorkflowEnvironment()
			env.RegisterWorkflow(CaptureMfAumFile)
			woProcessor, _, closeFunc := GetProcessorWithMocks(t)
			defer closeFunc()
			env.RegisterActivity(woProcessor)
			env.RegisterActivity(&celestialActivity.Processor{})
			for _, activity := range tt.mockActivities {
				env.OnActivity(string(activity.activity), mock.Anything, activity.req).Return(activity.res...).Times(1)
			}

			env.ExecuteWorkflow(CaptureMfAumFile, &celestialWorkflowPb.Request{})
			assert.True(t, env.IsWorkflowCompleted())

			if (env.GetWorkflowError() != nil) != tt.wantErr {
				t.Errorf("MfAumReconciliation() error = %v, wantErr %v", env.GetWorkflowError(), tt.wantErr)
			}
			env.AssertExpectations(t)
		})
	}
}

func mockInitiateWorkflowStageV2(stageEnum *celestialWorkflowPb.StageEnum) *mockActivity {
	return &mockActivity{
		enable:   true,
		activity: epifitemporal.InitiateWorkflowStageV2,
		req: &celestialPb.InitiateWorkflowStageV2Request{
			RequestHeader: &celestialPb.RequestHeader{},
			WfReqId:       defaultWorkflowID,
			Status:        stagePb.Status_INITIATED,
			StageEnum:     stageEnum,
		},
		res: []interface{}{nil},
	}
}

func mockUpdateWfStageV2(status stagePb.Status, stageEnum *celestialWorkflowPb.StageEnum) []*mockActivity {
	updateStage := &mockActivity{
		enable:   true,
		activity: epifitemporal.UpdateWorkflowStage,
		req: &celestialPb.UpdateWorkflowStageRequest{
			RequestHeader: &celestialPb.RequestHeader{},
			WfReqId:       defaultWorkflowID,
			Status:        status,
			StageEnum:     stageEnum,
		},
		res: []interface{}{
			nil,
		},
	}
	if celestialPkg.IsTerminalStatus(status) {
		return []*mockActivity{
			updateStage,
			{
				enable:   true,
				activity: epifitemporal.PublishWorkflowUpdateEventV2,
				req: &celestialPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &celestialPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
				},
				res: []interface{}{nil},
			},
		}
	}
	return []*mockActivity{updateStage}
}

type mockDependencies struct {
	s3Client              *mockS3.MockS3Client
	ocrClient             *mockOcr.MockOcrClient
	conf                  *worker.Config
	docHelper             *woHelperMocks.MockDocumentHelper
	woOcr                 *woOcrMocks.MockOcr
	lambdaClient          *lambdaMocks.MockLambdaClient
	userClient            *userMocks.MockUsersClient
	actorClient           *actorMocks.MockActorClient
	catalogManagerClient  *catalogMocks.MockCatalogManagerClient
	mutualFundDao         *mfDaoMocks.MockMutualFundDao
	folioLedgerDao        *mfDaoMocks.MockFolioLedgerDao
	orderDao              *mfDaoMocks.MockOrderDao
	orderConfirmationDao  *mfDaoMocks.MockOrderConfirmationInfoDao
	idempotentTxnExecutor *storagev2Mocks.MockIdempotentTxnExecutor
	timeClient            *dateTimeMock.MockTime
}

func GetProcessorWithMocks(t *testing.T) (*mfActivity.Processor, *mockDependencies, func()) {
	ctr := gomock.NewController(t)

	s3Client := mockS3.NewMockS3Client(ctr)
	ocrClient := mockOcr.NewMockOcrClient(ctr)
	docHelper := woHelperMocks.NewMockDocumentHelper(ctr)
	woOcr := woOcrMocks.NewMockOcr(ctr)
	lambdaClient := lambdaMocks.NewMockLambdaClient(ctr)
	userClient := userMocks.NewMockUsersClient(ctr)
	actorClient := actorMocks.NewMockActorClient(ctr)
	catalogManagerClient := catalogMocks.NewMockCatalogManagerClient(ctr)
	mutualFundDao := mfDaoMocks.NewMockMutualFundDao(ctr)
	folioLedgerDao := mfDaoMocks.NewMockFolioLedgerDao(ctr)
	orderDao := mfDaoMocks.NewMockOrderDao(ctr)
	orderConfirmationDao := mfDaoMocks.NewMockOrderConfirmationInfoDao(ctr)
	idempotentTxnExecutor := storagev2Mocks.NewMockIdempotentTxnExecutor(ctr)
	timeClient := dateTimeMock.NewMockTime(ctr)
	md := &mockDependencies{
		s3Client:              s3Client,
		ocrClient:             ocrClient,
		conf:                  conf,
		docHelper:             docHelper,
		woOcr:                 woOcr,
		lambdaClient:          lambdaClient,
		userClient:            userClient,
		actorClient:           actorClient,
		catalogManagerClient:  catalogManagerClient,
		mutualFundDao:         mutualFundDao,
		folioLedgerDao:        folioLedgerDao,
		orderDao:              orderDao,
		orderConfirmationDao:  orderConfirmationDao,
		idempotentTxnExecutor: idempotentTxnExecutor,
		timeClient:            timeClient,
	}

	p := mfActivity.NewProcessor(s3Client, ocrClient, conf, docHelper, woOcr, lambdaClient, userClient, actorClient,
		nil, catalogManagerClient, mutualFundDao, folioLedgerDao, orderDao, orderConfirmationDao, idempotentTxnExecutor, timeClient)

	return p, md, func() { ctr.Finish() }
}

func TestMain(m *testing.M) {
	_, teardown := test.InitTestWorker()
	wts.SetLogger(epifitemporalLogging.NewZapAdapter(logger.Log))
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
