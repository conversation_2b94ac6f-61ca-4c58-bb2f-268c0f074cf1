package workflow

import (
	"fmt"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"go.temporal.io/sdk/workflow"
	"google.golang.org/protobuf/encoding/protojson"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"

	workflow2 "github.com/epifi/gamma/api/wealthonboarding/workflow"
)

const (
	OWNERSHIP = commontypes.Ownership_EPIFI_WEALTH
)

func getWorkflowProcessingReqParams(ctx workflow.Context) (*workflow2.DocumentExtractionRequest, *workflowPb.ProcessingParams, error) {
	wfReqID := workflow.GetInfo(ctx).WorkflowExecution.ID
	wfProcessingParams := &activityPb.GetWorkflowProcessingParamsV2Response{}
	if err := activityPkg.Execute(ctx, epifitemporal.GetWorkflowProcessingParamsV2, wfProcessingParams, &activityPb.GetWorkflowProcessingParamsV2Request{
		RequestHeader: &activityPb.RequestHeader{
			Ownership: OWNERSHIP,
		},
		WfReqId: wfReqID,
	}); err != nil {
		return nil, nil, err
	}

	workflowReq := &workflow2.DocumentExtractionRequest{}
	if err := protojson.Unmarshal(wfProcessingParams.GetWfReqParams().GetPayload(), workflowReq); err != nil {
		return nil, nil, fmt.Errorf("failed to unmarshal payload %w", err)
	}

	return workflowReq, wfProcessingParams.GetWfReqParams(), nil
}

func getCaptureMfAumFileRequest(ctx workflow.Context) (*workflow2.CaptureMfAumFileRequest, *workflowPb.ProcessingParams, error) {
	wfReqID := workflow.GetInfo(ctx).WorkflowExecution.ID
	wfProcessingParams := &activityPb.GetWorkflowProcessingParamsV2Response{}
	if err := activityPkg.Execute(ctx, epifitemporal.GetWorkflowProcessingParamsV2, wfProcessingParams, &activityPb.GetWorkflowProcessingParamsV2Request{
		RequestHeader: &activityPb.RequestHeader{
			Ownership: OWNERSHIP,
		},
		WfReqId: wfReqID,
	}); err != nil {
		return nil, nil, err
	}

	workflowReq := &workflow2.CaptureMfAumFileRequest{}
	marshaller := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	if err := marshaller.Unmarshal(wfProcessingParams.GetWfReqParams().GetPayload(), workflowReq); err != nil {
		return nil, nil, fmt.Errorf("failed to unmarshal payload %w", err)
	}

	return workflowReq, wfProcessingParams.GetWfReqParams(), nil
}

func initiateWorkflowStage(ctx workflow.Context, stage workflowPb.Stage, status stagePb.Status, clientReqId, wfReqID string) error {
	if err := activityPkg.Execute(ctx, epifitemporal.InitiateWorkflowStageV2, &activityPb.InitiateWorkflowStageV2Response{}, &activityPb.InitiateWorkflowStageV2Request{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   OWNERSHIP,
		},
		WfReqId: wfReqID,
		Stage:   stage,
		Status:  status,
	}); err != nil {
		return fmt.Errorf("InitiateWorkflowStageV2 activity failure: %w", err)
	}
	return nil
}

func updateWorkflowStage(ctx workflow.Context, stage workflowPb.Stage, workflowStageStatus stagePb.Status, clientReqId, wfReqID string) error {
	// Step 3: Update workflow stage status
	if err2 := activityPkg.Execute(ctx, epifitemporal.UpdateWorkflowStage, &activityPb.UpdateWorkflowStageResponse{}, &activityPb.UpdateWorkflowStageRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   OWNERSHIP,
		},
		WfReqId: wfReqID,
		Stage:   stage,
		Status:  workflowStageStatus,
	}); err2 != nil {
		return fmt.Errorf("UpdateWorkflowStage activity failed: %w", err2)
	}

	if stagePb.IsTerminalStatus(workflowStageStatus) {
		if err := activityPkg.Execute(ctx, epifitemporal.PublishWorkflowUpdateEventV2, &activityPb.PublishWorkflowUpdateEventV2Response{}, &activityPb.PublishWorkflowUpdateEventV2Request{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
				Ownership:   OWNERSHIP,
			},
			WfReqId: wfReqID,
		}); err != nil {
			return fmt.Errorf("PublishWorkflowUpdateEvent activity failed: %w", err)
		}
	}
	return nil
}
