package event

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"errors"
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	types "github.com/epifi/gamma/api/typesv2"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/gamma/wealthonboarding/helper"
)

const (
	WEALTH  = "wealth_onboarding"
	SUCCESS = "success"
	FAIL    = "fail"
)

type PersonalData int32

const (
	Unspecified PersonalData = iota
	PanDoc
	SignatureData
	MaritalStatusData
	IncomeSlabData
	GenderData
	ProofOfAddressData
	DigilockerAccountAvailable
	DigilockerAccountUnAvailable
)

var PersonalDataName = map[PersonalData]string{
	Unspecified:                  "unspecified_data",
	PanDoc:                       "pan_document",
	SignatureData:                "signature_data",
	MaritalStatusData:            "marital_status_data",
	IncomeSlabData:               "income_slab_data",
	GenderData:                   "gender_data",
	ProofOfAddressData:           "proof_of_address_data",
	DigilockerAccountAvailable:   "digilocker_account_available",
	DigilockerAccountUnAvailable: "digilocker_account_unavailable",
}

type WealthonboardingStepEvent struct {
	ActorId       string
	ProspectId    string
	CurrentStep   string
	SessionId     string
	EventId       string
	EventType     string
	Timestamp     time.Time
	Flow          string
	Category      string
	AppVersion    int
	AppOs         string
	Status        string // equivalent to StepStatus
	FailureReason string
	IsFreshKra    bool
	SubStatus     string
}

func NewWealthonboardingStepEvent(timestamp time.Time, appVersion int, appOs commontypes.Platform, od *woPb.OnboardingDetails, osd *woPb.OnboardingStepDetails, failureReason error) *WealthonboardingStepEvent {
	errMsg := ""
	if failureReason != nil {
		errMsg = failureReason.Error()
	}
	actorId := od.GetActorId()
	flow := od.GetCurrentWealthFlow()
	step := od.GetCurrentStep().String()
	kraState := od.GetMetadata().GetIsFreshKra()
	stepStatus := osd.GetStatus().String()
	subStatus := osd.GetSubStatus().String()

	return &WealthonboardingStepEvent{
		ActorId:       actorId,
		CurrentStep:   step,
		EventId:       uuid.New().String(),
		EventType:     events.EventTrack,
		Flow:          flow.String(),
		Category:      WEALTH,
		AppOs:         appOs.String(),
		AppVersion:    appVersion,
		Timestamp:     timestamp,
		Status:        stepStatus,
		FailureReason: errMsg,
		IsFreshKra:    kraState,
		SubStatus:     subStatus,
	}
}

func (n *WealthonboardingStepEvent) GetEventId() string {
	return n.EventId
}

func (n *WealthonboardingStepEvent) GetUserId() string {
	return n.ActorId
}

func (n *WealthonboardingStepEvent) GetProspectId() string {
	return n.ProspectId
}

func (n *WealthonboardingStepEvent) GetEventName() string {
	switch n.CurrentStep {
	case woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION.String():
		return "data_collection_from_fi_tech"
	case woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION.String():
		return "nsdl_pan_verification"
	case woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA.String():
		return "kra_status_check"
	case woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET.String():
		return "e_sign"
	default:
		return n.CurrentStep
	}
}

func (n *WealthonboardingStepEvent) GetEventType() string {
	return n.EventType
}

func (n *WealthonboardingStepEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(n, properties)
	return properties
}

func (n *WealthonboardingStepEvent) GetEventTraits() map[string]interface{} {
	return nil
}

type CkycCallEvent struct {
	ActorId             string
	ProspectId          string
	SessionId           string
	EventId             string
	EventType           string
	Timestamp           time.Time
	Flow                string
	Category            string
	AppOs               string
	AppVersion          int
	IsPoiPresent        bool
	IsPoaPresent        bool
	IsCkycRecordPresent bool
	Error               string
}

func NewCkycCallEvent(actorId string, timestamp time.Time, flow woPb.WealthFlow, appVersion int, appOs commontypes.Platform, metaData *woPb.OnboardingMetadata, err error) *CkycCallEvent {
	poiStatus, poaStatus, ckycStatus := false, false, false
	errMsg := ""
	if err == nil {
		docs := metaData.GetCkycData().GetDownloadData().GetPersonalDetails().GetDocumentsList()
		poaType := metaData.GetCkycData().GetDownloadData().GetPersonalDetails().GetProofOfAddress()
		poa := helper.GetDocumentFromDocumentLists(docs, poaType)
		poaStatus = poa != nil && helper.IsDocumentStoreAllowed(poaType)
		pan := helper.GetDocumentFromDocumentLists(docs, types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN)
		poiStatus = pan != nil
		ckycStatus = true
	} else if !errors.Is(err, epifierrors.ErrRecordNotFound) {
		errMsg = err.Error()
	}

	return &CkycCallEvent{
		ActorId:             actorId,
		EventId:             uuid.New().String(),
		EventType:           events.EventTrack,
		Timestamp:           timestamp,
		Flow:                flow.String(),
		Category:            WEALTH,
		AppOs:               appOs.String(),
		AppVersion:          appVersion,
		IsPoiPresent:        poiStatus,
		IsPoaPresent:        poaStatus,
		IsCkycRecordPresent: ckycStatus,
		Error:               errMsg,
	}
}

func (c *CkycCallEvent) GetEventId() string {
	return c.EventId
}

func (c *CkycCallEvent) GetUserId() string {
	return c.ActorId
}

func (c *CkycCallEvent) GetProspectId() string {
	return c.ProspectId
}

func (c *CkycCallEvent) GetEventName() string {
	return "ckyc_call"
}

func (c *CkycCallEvent) GetEventType() string {
	return events.EventTrack
}

func (c *CkycCallEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *CkycCallEvent) GetEventTraits() map[string]interface{} {
	return nil
}

type MissingDataEvent struct {
	ActorId           string
	ProspectId        string
	SessionId         string
	EventId           string
	EventType         string
	Timestamp         time.Time
	CurrentStep       string
	Flow              string
	Category          string
	AppOs             string
	IsFreshKra        bool
	AppVersion        int
	PanState          bool
	IncomeSlab        bool
	MaritalStatus     bool
	Signature         bool
	VideoVerification bool
}

func NewMissingDataEvent(actorId string, step woPb.OnboardingStep, kraState bool, timestamp time.Time, flow woPb.WealthFlow,
	appVersion int, appOs commontypes.Platform, dl *deeplinkPb.Deeplink) *MissingDataEvent {
	panState, incomeSlab, maritalStatus, signature, videoVerification := false, false, false, false, false

	// when user will upload all the data then deeplink for missing data would be nil so by default all the doc value is set to false
	if dl != nil {
		if resp, ok := dl.GetScreenOptions().(*deeplinkPb.Deeplink_WealthOnboardingCaptureMissingDataScreenOptions); ok {
			missingData := resp.WealthOnboardingCaptureMissingDataScreenOptions.GetMissingData()

			for _, md := range missingData {
				if md.GetStatus() == deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_ENABLED ||
					md.GetStatus() == deeplinkPb.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_FAILED {
					switch md.GetMissingDataDeeplink().GetScreen() {
					case deeplinkPb.Screen_COLLECT_INCOME_SLAB_SCREEN:
						incomeSlab = true
					case deeplinkPb.Screen_COLLECT_MARITAL_STATUS_SCREEN:
						maritalStatus = true
					case deeplinkPb.Screen_COLLECT_PAN_SCREEN:
						panState = true
					case deeplinkPb.Screen_COLLECT_SIGNATURE_SCREEN:
						signature = true
					case deeplinkPb.Screen_CHECK_LIVENESS:
						videoVerification = true
					default:
						// for now, we are collecting data only for above screens
					}
				}
			}
		}
	}

	return &MissingDataEvent{
		ActorId:           actorId,
		EventId:           uuid.New().String(),
		EventType:         events.EventTrack,
		Timestamp:         timestamp,
		Flow:              flow.String(),
		Category:          WEALTH,
		AppOs:             appOs.String(),
		AppVersion:        appVersion,
		IsFreshKra:        kraState,
		PanState:          panState,
		IncomeSlab:        incomeSlab,
		MaritalStatus:     maritalStatus,
		Signature:         signature,
		VideoVerification: videoVerification,
		CurrentStep:       step.String(),
	}
}

func (c *MissingDataEvent) GetEventId() string {
	return c.EventId
}

func (c *MissingDataEvent) GetUserId() string {
	return c.ActorId
}

func (c *MissingDataEvent) GetProspectId() string {
	return c.ProspectId
}

func (c *MissingDataEvent) GetEventName() string {
	return "missing_data_screen_generated"
}

func (c *MissingDataEvent) GetEventType() string {
	return events.EventTrack
}

func (c *MissingDataEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *MissingDataEvent) GetEventTraits() map[string]interface{} {
	return nil
}

type DataCollectedEvent struct {
	ActorId           string
	ProspectId        string
	SessionId         string
	EventId           string
	EventType         string
	Timestamp         time.Time
	Flow              string
	Category          string
	AppOs             string
	DataCollectedType string
	AppVersion        int
	Status            string
	FailureReason     string
}

func NewDataCollectedEvent(actorId string, timestamp time.Time, flow woPb.WealthFlow, appVersion int, appOs commontypes.Platform, docType PersonalData, status, failureReason string) *DataCollectedEvent {
	return &DataCollectedEvent{
		ActorId:           actorId,
		EventId:           uuid.New().String(),
		EventType:         events.EventTrack,
		Flow:              flow.String(),
		Category:          WEALTH,
		AppOs:             appOs.String(),
		AppVersion:        appVersion,
		Timestamp:         timestamp,
		DataCollectedType: PersonalDataName[docType],
		Status:            status,
		FailureReason:     failureReason,
	}
}

func (u *DataCollectedEvent) GetEventId() string {
	return u.EventId
}

func (u *DataCollectedEvent) GetUserId() string {
	return u.ActorId
}

func (u *DataCollectedEvent) GetProspectId() string {
	return u.ProspectId
}

func (u *DataCollectedEvent) GetEventName() string {
	return "upload_event"
}

func (u *DataCollectedEvent) GetEventType() string {
	return events.EventTrack
}

func (u *DataCollectedEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(u, properties)
	return properties
}

func (u *DataCollectedEvent) GetEventTraits() map[string]interface{} {
	return nil
}

type ManualReviewEvent struct {
	ActorId    string
	ProspectId string
	SessionId  string
	EventId    string
	EventType  string
	Timestamp  time.Time
	Flow       string
	Category   string
	AppOs      string
	AppVersion int
	ItemList   []string
	Status     string
	IsFreshKra bool
}

func NewManualReviewEvent(actorId string, flow woPb.WealthFlow, appVersion int, appOs commontypes.Platform, status woPb.ReviewStatus, itemList []string, kraStatus bool) *ManualReviewEvent {
	return &ManualReviewEvent{
		ActorId:    actorId,
		EventId:    uuid.New().String(),
		EventType:  events.EventTrack,
		Flow:       flow.String(),
		Category:   WEALTH,
		AppOs:      appOs.String(),
		AppVersion: appVersion,
		Timestamp:  time.Now(),
		ItemList:   itemList,
		Status:     status.String(),
		IsFreshKra: kraStatus,
	}
}

func (m *ManualReviewEvent) GetEventId() string {
	return m.EventId
}

func (m *ManualReviewEvent) GetUserId() string {
	return m.ActorId
}

func (m *ManualReviewEvent) GetProspectId() string {
	return m.ProspectId
}

func (m *ManualReviewEvent) GetEventName() string {
	return "manual_review_event"
}

func (m *ManualReviewEvent) GetEventType() string {
	return events.EventTrack
}

func (m *ManualReviewEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(m, properties)
	return properties
}

func (m *ManualReviewEvent) GetEventTraits() map[string]interface{} {
	return nil
}

type WealthonboardingTriggerEvent struct {
	ActorId          string
	ProspectId       string
	SessionId        string
	EventId          string
	EventType        string
	Timestamp        time.Time
	Flow             string
	Category         string
	AppVersion       int
	AppOs            string
	Status           string
	FailureReason    string
	NextStepDeeplink string
}

func (w *WealthonboardingTriggerEvent) GetEventId() string {
	return w.EventId
}

func (w *WealthonboardingTriggerEvent) GetUserId() string {
	return w.ActorId
}

func (w *WealthonboardingTriggerEvent) GetProspectId() string {
	return w.ProspectId
}

func (w *WealthonboardingTriggerEvent) GetEventName() string {
	return "wealth_onboarding_trigger_event"
}

func (w *WealthonboardingTriggerEvent) GetEventType() string {
	return events.EventTrack
}

func (w *WealthonboardingTriggerEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(w, properties)
	return properties
}

func (w *WealthonboardingTriggerEvent) GetEventTraits() map[string]interface{} {
	return nil
}

func NewWealthonboardingTriggerEvent(timestamp time.Time, appVersion int, appOs commontypes.Platform, actorId string, status woPb.OnboardingStatus, failureReason error, deeplink *deeplinkPb.Deeplink, flow woPb.WealthFlow) *WealthonboardingTriggerEvent {
	errMsg := ""
	if failureReason != nil {
		errMsg = failureReason.Error()
	}

	return &WealthonboardingTriggerEvent{
		ActorId:          actorId,
		EventId:          uuid.New().String(),
		EventType:        events.EventTrack,
		Category:         WEALTH,
		AppOs:            appOs.String(),
		AppVersion:       appVersion,
		Timestamp:        timestamp,
		Status:           status.String(),
		FailureReason:    errMsg,
		NextStepDeeplink: deeplink.GetScreen().String(),
		Flow:             flow.String(),
	}
}
