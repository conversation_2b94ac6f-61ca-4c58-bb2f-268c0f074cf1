package wealthonboarding

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	userPb "github.com/epifi/gamma/api/wealthonboarding/user"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/gamma/wealthonboarding/dao"
	mockDao "github.com/epifi/gamma/wealthonboarding/test/mocks/dao"
	mock_helpers "github.com/epifi/gamma/wealthonboarding/test/mocks/helpers"
	"github.com/epifi/gamma/wealthonboarding/user"
)

func TestService_UpdateOnbStatus(t *testing.T) {
	ctr := gomock.NewController(t)

	mockOnbDetailsDao := mockDao.NewMockOnboardingDetailsDao(ctr)
	mockOnbStepDetailsDao := mockDao.NewMockOnboardingStepDetailsDao(ctr)
	onbDetailsManualInterv := &woPb.OnboardingDetails{Id: "1", ActorId: "1", Status: woPb.OnboardingStatus_ONBOARDING_STATUS_MANUAL_INTERVENTION_NEEDED, CurrentStep: woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION}
	onbStepDetailsManualInterv := &woPb.OnboardingStepDetails{Step: woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION, Status: woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED}
	onbDetailsFutureScope := &woPb.OnboardingDetails{Id: "1", ActorId: "1", Status: woPb.OnboardingStatus_ONBOARDING_STATUS_FUTURE_SCOPE, CurrentStep: woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION}
	onbStepDetailsFutureScope := &woPb.OnboardingStepDetails{Step: woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION, Status: woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE}
	onbDetailsUpdateErr := &woPb.OnboardingDetails{Id: "1", ActorId: "1", Status: woPb.OnboardingStatus_ONBOARDING_STATUS_MANUAL_INTERVENTION_NEEDED, CurrentStep: woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION}
	onbDetailsStepUpdateErr := &woPb.OnboardingDetails{Id: "1", ActorId: "1", Status: woPb.OnboardingStatus_ONBOARDING_STATUS_MANUAL_INTERVENTION_NEEDED, CurrentStep: woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION}
	onbStepDetailsStepUpdateErr := &woPb.OnboardingStepDetails{Step: woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION, Status: woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED}
	onbDetailsFetchStepDetailsErr := &woPb.OnboardingDetails{Id: "1", ActorId: "1", Status: woPb.OnboardingStatus_ONBOARDING_STATUS_MANUAL_INTERVENTION_NEEDED, CurrentStep: woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION}
	type fields struct {
		onboardingDetailsDao     dao.OnboardingDetailsDao
		onboardingStepDetailsDao dao.OnboardingStepDetailsDao
	}
	type args struct {
		ctx   context.Context
		req   *woPb.UpdateOnbStatusRequest
		mocks []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *woPb.UpdateOnbStatusResponse
		wantErr bool
	}{
		{
			name: "actor not found",
			fields: fields{
				onboardingDetailsDao:     mockOnbDetailsDao,
				onboardingStepDetailsDao: mockOnbStepDetailsDao,
			},
			args: args{
				ctx: context.Background(),
				req: &woPb.UpdateOnbStatusRequest{
					ActorId:          "1",
					OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
					OnboardingType:   woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
				},
				mocks: []interface{}{
					mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).
						Return(nil, errors.New("error")),
				},
			},
			want: &woPb.UpdateOnbStatusResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "invalid status update request",
			fields: fields{
				onboardingDetailsDao:     mockOnbDetailsDao,
				onboardingStepDetailsDao: mockOnbStepDetailsDao,
			},
			args: args{
				ctx: context.Background(),
				req: &woPb.UpdateOnbStatusRequest{
					ActorId:          "1",
					OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED,
					OnboardingType:   woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
				},
				mocks: []interface{}{
					mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).
						Return(&woPb.OnboardingDetails{ActorId: "1", Status: woPb.OnboardingStatus_ONBOARDING_STATUS_FUTURE_SCOPE}, nil),
				},
			},
			want: &woPb.UpdateOnbStatusResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("Onboarding status update request is not valid"),
			},
			wantErr: false,
		},
		{
			name: "successful update for manual intervention",
			fields: fields{
				onboardingDetailsDao:     mockOnbDetailsDao,
				onboardingStepDetailsDao: mockOnbStepDetailsDao,
			},
			args: args{
				ctx: context.Background(),
				req: &woPb.UpdateOnbStatusRequest{
					ActorId:          "1",
					OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
					OnboardingType:   woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
				},
				mocks: []interface{}{
					mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(onbDetailsManualInterv, nil),
					mockOnbDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					mockOnbStepDetailsDao.EXPECT().GetByOnboardingDetailsIdAndStep(gomock.Any(), gomock.Any(), gomock.Any()).Return(onbStepDetailsManualInterv, nil),
					mockOnbStepDetailsDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			want: &woPb.UpdateOnbStatusResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "successful update for future scope",
			fields: fields{
				onboardingDetailsDao:     mockOnbDetailsDao,
				onboardingStepDetailsDao: mockOnbStepDetailsDao,
			},
			args: args{
				ctx: context.Background(),
				req: &woPb.UpdateOnbStatusRequest{
					ActorId:          "1",
					OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
					OnboardingType:   woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
				},
				mocks: []interface{}{
					mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(onbDetailsFutureScope, nil),
					mockOnbDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					mockOnbStepDetailsDao.EXPECT().GetByOnboardingDetailsIdAndStep(gomock.Any(), gomock.Any(), gomock.Any()).Return(onbStepDetailsFutureScope, nil),
					mockOnbStepDetailsDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			want: &woPb.UpdateOnbStatusResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "error in onbboardingDetailsDao update",
			fields: fields{
				onboardingDetailsDao:     mockOnbDetailsDao,
				onboardingStepDetailsDao: mockOnbStepDetailsDao,
			},
			args: args{
				ctx: context.Background(),
				req: &woPb.UpdateOnbStatusRequest{
					ActorId:          "1",
					OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
					OnboardingType:   woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
				},
				mocks: []interface{}{
					mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(onbDetailsUpdateErr, nil),
					mockOnbDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("error in updating db")),
				},
			},
			want: &woPb.UpdateOnbStatusResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "error in onbboardingStepDetailsDao update",
			fields: fields{
				onboardingDetailsDao:     mockOnbDetailsDao,
				onboardingStepDetailsDao: mockOnbStepDetailsDao,
			},
			args: args{
				ctx: context.Background(),
				req: &woPb.UpdateOnbStatusRequest{
					ActorId:          "1",
					OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
					OnboardingType:   woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
				},
				mocks: []interface{}{
					mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(onbDetailsStepUpdateErr, nil),
					mockOnbDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					mockOnbStepDetailsDao.EXPECT().GetByOnboardingDetailsIdAndStep(gomock.Any(), gomock.Any(), gomock.Any()).Return(onbStepDetailsStepUpdateErr, nil),
					mockOnbStepDetailsDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("error")),
				},
			},
			want: &woPb.UpdateOnbStatusResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "error in fetching onboarding step details",
			fields: fields{
				onboardingDetailsDao:     mockOnbDetailsDao,
				onboardingStepDetailsDao: mockOnbStepDetailsDao,
			},
			args: args{
				ctx: context.Background(),
				req: &woPb.UpdateOnbStatusRequest{
					ActorId:          "1",
					OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
					OnboardingType:   woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
				},
				mocks: []interface{}{
					mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(onbDetailsFetchStepDetailsErr, nil),
					mockOnbDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					mockOnbStepDetailsDao.EXPECT().GetByOnboardingDetailsIdAndStep(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error")),
				},
			},
			want: &woPb.UpdateOnbStatusResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				onboardingDetailsDao:     tt.fields.onboardingDetailsDao,
				onboardingStepDetailsDao: tt.fields.onboardingStepDetailsDao,
				idempotentTxnExecutor:    idempotentTxnExecutor,
				conf:                     conf,
			}
			got, err := s.UpdateOnbStatus(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateOnbStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdateOnbStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetOrCreateUser(t *testing.T) {
	var (
		randomErr = fmt.Errorf("random error")
	)
	type mockStruct struct {
		userDao      *mockDao.MockUserDao
		commonHelper *mock_helpers.MockICommonHelper
	}
	type args struct {
		req *woPb.GetOrCreateUserRequest
	}
	tests := map[string]struct {
		args    args
		want    *woPb.GetOrCreateUserResponse
		mocks   func(args args, mocks mockStruct)
		wantErr bool
	}{
		"random error in GetByActorId": {
			args: args{
				req: &woPb.GetOrCreateUserRequest{
					ActorId: "actor-1",
				},
			},
			mocks: func(args args, mocks mockStruct) {
				mocks.userDao.EXPECT().GetByActorId(gomock.Any(), args.req.GetActorId()).Return(nil, randomErr)
			},
			want: &woPb.GetOrCreateUserResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		"did not find user by actor id": {
			args: args{
				req: &woPb.GetOrCreateUserRequest{
					ActorId: "actor-1",
				},
			},
			want: &woPb.GetOrCreateUserResponse{
				Status: rpc.StatusOk(),
				UserDetails: &woPb.GetOrCreateUserResponse_UserDetails{
					Id: "user-id",
				},
			},
			mocks: func(args args, mocks mockStruct) {
				mocks.userDao.EXPECT().GetByActorId(gomock.Any(), args.req.GetActorId()).Return(nil, epifierrors.ErrRecordNotFound)
				mocks.commonHelper.EXPECT().CreateAndPopulateUserPersonalDetails(gomock.Any(), args.req.GetActorId()).Return(&woPb.GetOrCreateUserResponse_UserDetails{
					Id: "user-id",
				}, nil)
			},
			wantErr: false,
		},
		"found user in DB": {
			args: args{
				req: &woPb.GetOrCreateUserRequest{
					ActorId: "actor-1",
				},
			},
			want: &woPb.GetOrCreateUserResponse{
				Status: rpc.StatusOk(),
				UserDetails: &woPb.GetOrCreateUserResponse_UserDetails{
					Id: "user-id",
				},
			},
			mocks: func(args args, mocks mockStruct) {
				mocks.userDao.EXPECT().GetByActorId(gomock.Any(), args.req.GetActorId()).Return(&userPb.User{
					Id:      "user-id",
					ActorId: args.req.GetActorId(),
				}, nil)
				mocks.commonHelper.EXPECT().ValidateAndUpdateUserPersonalDetails(gomock.Any(), gomock.Any()).Return(&woPb.GetOrCreateUserResponse_UserDetails{
					Id: "user-id",
				}, nil)
			},
			wantErr: false,
		},
	}
	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockUserDao := mockDao.NewMockUserDao(ctrl)
			mockCommonHelper := mock_helpers.NewMockICommonHelper(ctrl)
			s := &Service{
				userService: &user.Service{
					UserDao: mockUserDao,
				},
				commonHelper: mockCommonHelper,
			}
			tt.mocks(tt.args, mockStruct{
				userDao:      mockUserDao,
				commonHelper: mockCommonHelper,
			})
			got, err := s.GetOrCreateUser(context.Background(), tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOrCreateUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetOrCreateUser() got = %v, want %v", got, tt.want)
			}
		})
	}
}
