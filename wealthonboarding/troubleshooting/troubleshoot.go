// nolint: gocritic, exhaustive
package troubleshooting

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	types "github.com/epifi/gamma/api/typesv2"
	wealthVgPb "github.com/epifi/gamma/api/vendors/wealth"
	"github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/wealthonboarding/config"

	"go.uber.org/zap"
)

const (
	adviceTransientError   = "User should see a retry in app itself"
	adviceContactDeveloper = "Please escalate internally, someone from dev team will investigate"
	adviceFutureScopeError = "User cannot onboard now. Will be handled in future releases"
)

var (
	stepStatusStringMap = map[wealthonboarding.OnboardingStepStatus]string{
		wealthonboarding.OnboardingStepStatus_STEP_STATUS_UNSPECIFIED:                "Unspecified",
		wealthonboarding.OnboardingStepStatus_STEP_STATUS_PENDING:                    "Pending",
		wealthonboarding.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS:                "In progress",
		wealthonboarding.OnboardingStepStatus_STEP_STATUS_COMPLETED:                  "Completed",
		wealthonboarding.OnboardingStepStatus_STEP_STATUS_FAILED:                     "Failed",
		wealthonboarding.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED: "Manual intervention needed",
		wealthonboarding.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE:               "To be addressed in future",
		wealthonboarding.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE:          "Temporary error",
	}

	livenessAdviceMap = map[wealthonboarding.LivenessRejectReason]string{
		wealthonboarding.LivenessRejectReason_LIVENESS_REJECT_REASON_FACE_NOT_DETECTED:    "User's face not detected during liveness. Please ask user to be in front of plain background and well lit area.",
		wealthonboarding.LivenessRejectReason_LIVENESS_REJECT_REASON_MULTIPLE_FACES:       "Multiple face detected during liveness. Please ask user to be in front of plain background",
		wealthonboarding.LivenessRejectReason_LIVENESS_REJECT_REASON_FACE_POORLY_DETECTED: "User's face not detected properly during liveness. Please ask user to be in well lit room and face to be align to oval",
		wealthonboarding.LivenessRejectReason_LIVENESS_REJECT_REASON_FACE_TOO_FAR:         "User's face too far during liveness. Please ask user to make sure his face is closer to the camera and aligned in the circle during recording.",
		wealthonboarding.LivenessRejectReason_LIVENESS_REJECT_REASON_FACE_TOO_CLOSE:       "User's face too close during liveness.Please ask user to make usre his face is further from the camera and aligned in the circle during recording",
		wealthonboarding.LivenessRejectReason_LIVENESS_REJECT_REASON_FACE_TOO_DARK:        "Users face is too dark during liveness. Please ask user to find a well lit area.",
		wealthonboarding.LivenessRejectReason_LIVENESS_REJECT_REASON_FACE_TOO_BRIGHT:      "Users face is too Bright during liveness. Please ask user to find a well lit area with plain background",
		wealthonboarding.LivenessRejectReason_LIVENESS_REJECT_REASON_NO_FACE_DETECTED:     "User's face not detected during liveness. Please ask user to be in front of plain background and well lit area.",
	}
)

type IHandlerFactory interface {
	GetTroubleshootHandler(step wealthonboarding.OnboardingStep) (IHandler, error)
}

type IHandler interface {
	GetOnboardingStageDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.StageDetails
	GetStageTroubleShootingDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.CurrentStageTroubleShootingDetails
}

type HandlerFactory struct {
	dataCollectionHand             *DataCollectionHandler
	panVerificationHand            *PanVerificationHandler
	fetchKycInfoFromKraHand        *FetchKycInfoFromKraHandler
	collectMissingPersonalInfoHand *CollectMissingPersonalInfoHandler
	livenessHand                   *LivenessHandler
	faceMatchHand                  *FaceMatchHandler
	createAndSignKraDocketHand     *CreateAndSignKraDocketHandler
	confirmPepAndCitizenshipHand   *ConfirmPepAndCitizenshipHandler
	investmentDataCollectionHand   *InvestmentDataCollectionHandler
	downloadKraDocsHand            *DownloadKraDocsHandler
	uploadKraDocketHand            *UploadKraDocketHandler
	ConsolidatedManualReviewHand   *ConsolidatedManualReviewHandler
}

func NewHandlerFactory(
	dataCollectionHand *DataCollectionHandler,
	panVerificationHand *PanVerificationHandler,
	fetchKycInfoFromKraHand *FetchKycInfoFromKraHandler,
	collectMissingPersonalInfoHand *CollectMissingPersonalInfoHandler,
	livenessHand *LivenessHandler,
	faceMatchHand *FaceMatchHandler,
	createAndSignKraDocketHand *CreateAndSignKraDocketHandler,
	confirmPepAndCitizenshipHand *ConfirmPepAndCitizenshipHandler,
	investmentDataCollectionHand *InvestmentDataCollectionHandler,
	downloadKraDocsHand *DownloadKraDocsHandler,
	uploadKraDocketHand *UploadKraDocketHandler,
	ConsolidatedManualReviewHand *ConsolidatedManualReviewHandler) *HandlerFactory {
	return &HandlerFactory{
		dataCollectionHand:             dataCollectionHand,
		panVerificationHand:            panVerificationHand,
		fetchKycInfoFromKraHand:        fetchKycInfoFromKraHand,
		collectMissingPersonalInfoHand: collectMissingPersonalInfoHand,
		livenessHand:                   livenessHand,
		faceMatchHand:                  faceMatchHand,
		createAndSignKraDocketHand:     createAndSignKraDocketHand,
		confirmPepAndCitizenshipHand:   confirmPepAndCitizenshipHand,
		investmentDataCollectionHand:   investmentDataCollectionHand,
		downloadKraDocsHand:            downloadKraDocsHand,
		uploadKraDocketHand:            uploadKraDocketHand,
		ConsolidatedManualReviewHand:   ConsolidatedManualReviewHand,
	}
}

func (h *HandlerFactory) GetTroubleshootHandler(step wealthonboarding.OnboardingStep) (IHandler, error) {
	switch step {
	case wealthonboarding.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION:
		return h.dataCollectionHand, nil
	case wealthonboarding.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION:
		return h.panVerificationHand, nil
	case wealthonboarding.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA:
		return h.fetchKycInfoFromKraHand, nil
	case wealthonboarding.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO:
		return h.collectMissingPersonalInfoHand, nil
	case wealthonboarding.OnboardingStep_ONBOARDING_STEP_LIVENESS:
		return h.livenessHand, nil
	case wealthonboarding.OnboardingStep_ONBOARDING_STEP_FACE_MATCH:
		return h.faceMatchHand, nil
	case wealthonboarding.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET:
		return h.createAndSignKraDocketHand, nil
	case wealthonboarding.OnboardingStep_ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW:
		return h.ConsolidatedManualReviewHand, nil
	case wealthonboarding.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP:
		return h.confirmPepAndCitizenshipHand, nil
	case wealthonboarding.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION_FOR_INVESTMENT:
		return h.investmentDataCollectionHand, nil
	case wealthonboarding.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC:
		return h.downloadKraDocsHand, nil
	case wealthonboarding.OnboardingStep_ONBOARDING_STEP_UPLOAD_DOCKET:
		return h.uploadKraDocketHand, nil
	default:
		return nil, errors.New("step handler not found")
	}
}

type DataCollectionHandler struct {
	conf *config.Config
}

func NewDataCollectionHandler(conf *config.Config) *DataCollectionHandler {
	return &DataCollectionHandler{
		conf: conf,
	}
}

// nolint: dupl
func (h *DataCollectionHandler) GetOnboardingStageDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.StageDetails {
	return &wealthonboarding.StageDetails{
		Status:      stepStatusStringMap[osd.GetStatus()],
		Description: h.conf.StageDescriptionMapping[osd.GetStep().String()],
		UpdatedAt:   osd.GetUpdatedAt(),
	}
}

// nolint: dupl
func (h *DataCollectionHandler) GetStageTroubleShootingDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.CurrentStageTroubleShootingDetails {
	advice, l1, l2 := "", "", ""
	if osd.GetStatus() == wealthonboarding.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE {
		advice = adviceTransientError
	} else if osd.GetStatus() == wealthonboarding.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE {
		if osd.GetSubStatus() == wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_MANDATORY_DETAILS_MISSING_IN_FI {
			l1 = osd.GetSubStatus().String() + ": Some of the following details are missing: name, dob, father name, mother name"
			l2 = "Missing details: "
			if od.GetMetadata().GetPersonalDetails().GetName() == nil {
				l2 = "Name"
			}
			if od.GetMetadata().GetPersonalDetails().GetDob() == nil {
				if l2 != "" {
					l2 += ", "
				}
				l2 += "DOB"
			}
			if od.GetMetadata().GetPersonalDetails().GetFatherName() == nil {
				if l2 != "" {
					l2 += ", "
				}
				l2 += "Father name"
			}
			if od.GetMetadata().GetPersonalDetails().GetMotherName() == nil {
				if l2 != "" {
					l2 += ", "
				}
				l2 += "Mother name"
			}
		}
		advice = adviceContactDeveloper
	} else if osd.GetSubStatus() != wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSPECIFIED {
		// any unhandled sub status, capture and show in l1
		l1 = osd.GetSubStatus().String()
		advice = adviceContactDeveloper
	}
	return &wealthonboarding.CurrentStageTroubleShootingDetails{
		Stage:     wealthonboarding.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION.String(),
		Status:    stepStatusStringMap[osd.GetStatus()],
		UpdatedAt: osd.GetUpdatedAt(),
		L1:        l1,
		L2:        l2,
		Advice:    advice,
	}
}

type PanVerificationHandler struct {
	conf *config.Config
}

func NewPanVerificationHandler(conf *config.Config) *PanVerificationHandler {
	return &PanVerificationHandler{
		conf: conf,
	}
}

// nolint: dupl
func (h *PanVerificationHandler) GetOnboardingStageDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.StageDetails {
	return &wealthonboarding.StageDetails{
		Status:      stepStatusStringMap[osd.GetStatus()],
		Description: h.conf.StageDescriptionMapping[osd.GetStep().String()],
		UpdatedAt:   osd.GetUpdatedAt(),
	}
}

// nolint: gocritic, dupl
func (h *PanVerificationHandler) GetStageTroubleShootingDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.CurrentStageTroubleShootingDetails {
	l1, l2, advice := "", "", ""
	if osd.GetSubStatus() == wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_NAME_MATCH_FAILED {
		advice = "Please escalate internally, someone from dev team will have to investigate"
		l1 = osd.GetSubStatus().String() + ": User given name not matching with PAN name"
		l2 = fmt.Sprintf("Name Match Score: %f, User Name: %v, User PAN Name: %v", od.GetMetadata().GetNameMatchInfo().GetWeightedSumScore(), od.GetMetadata().GetPersonalDetails().GetName().String(), od.GetMetadata().GetNsdlData().GetPanDetails().GetPanCardName())
	} else if osd.GetSubStatus() == wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_NSDL_PAN_AADHAAR_SEEDING_PENDING {
		l1 = osd.GetSubStatus().String() + ": Aadhaar is not seeded in PAN"
		advice = "User is stuck as Aadhaar is not seeded in PAN, we can ask user to link their Aadhaar with the PAN from here(https://eportal.incometax.gov.in/iec/foservices/#/pre-login/bl-link-aadhaar) and retry after 48 hours"
	} else if osd.GetSubStatus() == wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_NSDL_PAN_VERIFICATION_FAILED {
		l1 = osd.GetSubStatus().String() + ": Invalid PAN number"
		l2 = "PAN status of the user: " + od.GetMetadata().GetNsdlData().GetPanDetails().GetPanStatus().String()
		advice = "user is blocked due to invalid PAN, we would not be able to proceed"
	} else if osd.GetStatus() == wealthonboarding.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE {
		advice = adviceTransientError
	} else if osd.GetSubStatus() != wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSPECIFIED {
		// any unhandled sub status, capture and show in l1
		l1 = osd.GetSubStatus().String()
		advice = adviceContactDeveloper
	}
	return &wealthonboarding.CurrentStageTroubleShootingDetails{
		Stage:     osd.GetStep().String(),
		Status:    stepStatusStringMap[osd.GetStatus()],
		UpdatedAt: osd.GetUpdatedAt(),
		L1:        l1,
		L2:        l2,
		Advice:    advice,
	}
}

type FetchKycInfoFromKraHandler struct {
	conf *config.Config
}

func NewFetchKycInfoFromKraHandler(conf *config.Config) *FetchKycInfoFromKraHandler {
	return &FetchKycInfoFromKraHandler{
		conf: conf,
	}
}

// nolint: dupl
func (h *FetchKycInfoFromKraHandler) GetOnboardingStageDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.StageDetails {
	metadata := struct {
		IsFreshKRA     bool
		KRAStatus      string
		IPVFlag        string
		AppStatusDelta string
	}{
		IsFreshKRA:     od.GetMetadata().GetIsFreshKra(),
		KRAStatus:      od.GetMetadata().GetKraData().GetStatusData().GetPanStatusDetails().GetStatus().String(),
		IPVFlag:        od.GetMetadata().GetKraData().GetStatusData().GetPanStatusDetails().GetIpvFlag().String(),
		AppStatusDelta: od.GetMetadata().GetKraData().GetStatusData().GetPanStatusDetails().GetAppStatusDelta().String(),
	}
	metadataStr := ""
	metadataJson, err := json.Marshal(metadata)
	if err != nil {
		logger.Error(ctx, "failed to marshal additionalDetails", zap.Error(err))
	} else {
		metadataStr = string(metadataJson)
	}
	return &wealthonboarding.StageDetails{
		Status:      stepStatusStringMap[osd.GetStatus()],
		Description: h.conf.StageDescriptionMapping[osd.GetStep().String()],
		UpdatedAt:   osd.GetUpdatedAt(),
		MetaData:    metadataStr,
	}
}

// nolint: dupl
func (h *FetchKycInfoFromKraHandler) GetStageTroubleShootingDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.CurrentStageTroubleShootingDetails {
	advice, l2, l1 := "", "", ""
	if osd.GetStatus() == wealthonboarding.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE {
		advice = adviceTransientError
	} else if osd.GetSubStatus() == wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_INVALID_PAN_NO_FORMAT {
		l1 = osd.GetSubStatus().String() + ": Invalid PAN format passed as per KRA"
		advice = adviceFutureScopeError
	} else if osd.GetSubStatus() == wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_KRA_STATUS_HOLD {
		l1 = osd.GetSubStatus().String() + ": KYC status is on hold at KRA"
		advice = "Ask user to wait for few days"
	} else if osd.GetSubStatus() == wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_UNHANDLED_KRA_STATUS {
		l1 = osd.GetSubStatus().String() + ": We have not handled this status in our system"
		advice = adviceContactDeveloper
	} else if osd.GetSubStatus() != wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSPECIFIED {
		l1 = osd.GetSubStatus().String()
		advice = adviceContactDeveloper
	}
	additionalDetails := struct {
		IsFreshKRA     bool
		KRAStatus      string
		IPVFlag        string
		AppStatusDelta string
	}{
		IsFreshKRA:     od.GetMetadata().GetIsFreshKra(),
		KRAStatus:      od.GetMetadata().GetKraData().GetStatusData().GetPanStatusDetails().GetStatus().String(),
		IPVFlag:        od.GetMetadata().GetKraData().GetStatusData().GetPanStatusDetails().GetIpvFlag().String(),
		AppStatusDelta: od.GetMetadata().GetKraData().GetStatusData().GetPanStatusDetails().GetAppStatusDelta().String(),
	}
	additionalDetailsStr := ""
	additionalDetailsJson, err := json.Marshal(additionalDetails)
	if err != nil {
		logger.Error(ctx, "failed to marshal additionalDetails", zap.Error(err))
	} else {
		additionalDetailsStr = string(additionalDetailsJson)
	}
	return &wealthonboarding.CurrentStageTroubleShootingDetails{
		Stage:             osd.GetStep().String(),
		Status:            stepStatusStringMap[osd.GetStatus()],
		UpdatedAt:         osd.GetUpdatedAt(),
		L1:                l1,
		L2:                l2,
		Advice:            advice,
		AdditionalDetails: additionalDetailsStr,
	}
}

type CollectMissingPersonalInfoHandler struct {
	conf *config.Config
}

func NewCollectMissingPersonalInfoHandler(conf *config.Config) *CollectMissingPersonalInfoHandler {
	return &CollectMissingPersonalInfoHandler{
		conf: conf,
	}
}

// nolint: dupl
func (h *CollectMissingPersonalInfoHandler) GetOnboardingStageDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.StageDetails {
	return &wealthonboarding.StageDetails{
		Status:      stepStatusStringMap[osd.GetStatus()],
		Description: h.conf.StageDescriptionMapping[osd.GetStep().String()],
		UpdatedAt:   osd.GetUpdatedAt(),
	}
}

// nolint: dupl
func (h *CollectMissingPersonalInfoHandler) GetStageTroubleShootingDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.CurrentStageTroubleShootingDetails {
	advice, l1, l2 := "", "", ""
	switch osd.GetStatus() {
	case wealthonboarding.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE:
		advice = adviceTransientError
		if osd.GetSubStatus() == wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_UNHANDLED_KRA_APP_STATUS_DELTA {
			l1 = osd.GetSubStatus().String() + ": In case any data is missing in KRA and we have not handled capturing it from user"
			advice = adviceContactDeveloper
		}
	case wealthonboarding.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS:
		l1 = "User should see missing data screen"
		l2 = "Data needed from user:"
		if od.GetMetadata().GetPersonalDetails().GetGender() == types.Gender_GENDER_UNSPECIFIED {
			l2 += " Gender,"
		}
		if len(od.GetMetadata().GetPanDetails().GetS3Paths()) == 0 {
			l2 += " PAN photo,"
		}
		if od.GetMetadata().GetPersonalDetails().GetIncomeSlab() == types.IncomeSlab_INCOME_SLAB_UNSPECIFIED {
			l2 += " Income Slab,"
		}
		if od.GetMetadata().GetPersonalDetails().GetMaritalStatus() == types.MaritalStatus_UNSPECIFIED {
			l2 += " Marital Status,"
		}
		if len(od.GetMetadata().GetPersonalDetails().GetSignature().GetS3Paths()) == 0 {
			l2 += " Signature"
		}
	case wealthonboarding.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE:
		switch osd.GetSubStatus() {
		case wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_CKYC_ACCOUNT_TYPE_L, wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_CKYC_ACCOUNT_TYPE_S, wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_CKYC_ACCOUNT_TYPE_O:
			l1 = osd.GetSubStatus().String() + ": These types of accounts are not allowed to onboard"
		case wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_CKYC_RECORD_NOT_FOUND:
			l1 = osd.GetSubStatus().String() + ": Unable to fetch required mandatory details from CKYC"
		case wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSUPPORTED_AADHAAR_TYPE:
			l1 = osd.GetSubStatus().String() + ": These type of Aadhaar proof is not allowed to onboard"
		case wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_DOB_MISMATCH_ERROR_IN_CKYC_DOWNLOAD_RESPONSE:
			l1 = osd.GetSubStatus().String() + ": DOB provided by us does not match with ckyc data"
		}
		advice = "User cannot proceed"
	}
	if l1 == "" && osd.GetSubStatus() != wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSPECIFIED {
		l1 = osd.GetSubStatus().String()
		advice = adviceContactDeveloper
	}
	additionalDetails := struct {
		IsFreshKRA     bool
		AppStatusDelta string
	}{
		IsFreshKRA:     od.GetMetadata().GetIsFreshKra(),
		AppStatusDelta: od.GetMetadata().GetKraData().GetStatusData().GetPanStatusDetails().GetAppStatusDelta().String(),
	}
	additionalDetailsStr := ""
	additionalDetailsJson, err := json.Marshal(additionalDetails)
	if err != nil {
		logger.Error(ctx, "failed to marshal additionalDetails", zap.Error(err))
	} else {
		additionalDetailsStr = string(additionalDetailsJson)
	}
	return &wealthonboarding.CurrentStageTroubleShootingDetails{
		Stage:             osd.GetStep().String(),
		Status:            stepStatusStringMap[osd.GetStatus()],
		UpdatedAt:         osd.GetUpdatedAt(),
		L1:                l1,
		L2:                l2,
		Advice:            advice,
		AdditionalDetails: additionalDetailsStr,
	}
}

type LivenessHandler struct {
	conf *config.Config
}

func NewLivenessHandler(conf *config.Config) *LivenessHandler {
	return &LivenessHandler{
		conf: conf,
	}
}

// nolint: dupl
func (h *LivenessHandler) GetOnboardingStageDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.StageDetails {
	metadata, err := json.Marshal(od.GetMetadata().GetLivenessData())
	metadataStr := ""
	if err != nil {
		logger.Error(ctx, "failed to marshal LivenessData", zap.Error(err))
	} else {
		metadataStr = string(metadata)
	}
	return &wealthonboarding.StageDetails{
		Status:      stepStatusStringMap[osd.GetStatus()],
		Description: h.conf.StageDescriptionMapping[osd.GetStep().String()],
		UpdatedAt:   osd.GetUpdatedAt(),
		MetaData:    metadataStr,
	}
}

// nolint: dupl
func (h *LivenessHandler) GetStageTroubleShootingDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.CurrentStageTroubleShootingDetails {
	advice, l1 := "", ""
	type lvAttempt struct {
		AttemptCount        int
		LatestAttemptStatus wealthonboarding.LivenessData_LivenessAttempt_LivenessStatus
		LivenessScore       float32
		OtpScore            float32
		LatestAttemptId     string
	}
	var lvAttemptDetails lvAttempt
	lvAttemptDetails.AttemptCount = len(od.GetMetadata().GetLivenessData().GetLivenessAttempts())
	if lvAttemptDetails.AttemptCount > 0 {
		lvAttemptDetails.LatestAttemptStatus = od.GetMetadata().GetLivenessData().GetLivenessAttempts()[lvAttemptDetails.AttemptCount-1].GetStatus()
		lvAttemptDetails.LivenessScore = od.GetMetadata().GetLivenessData().GetLivenessAttempts()[lvAttemptDetails.AttemptCount-1].GetLivenessScore()
		lvAttemptDetails.OtpScore = od.GetMetadata().GetLivenessData().GetLivenessAttempts()[lvAttemptDetails.AttemptCount-1].GetOtpScore()
		lvAttemptDetails.LatestAttemptId = od.GetMetadata().GetLivenessData().GetLivenessAttempts()[lvAttemptDetails.AttemptCount-1].GetAttemptId()
	}
	addnDetails, err := json.Marshal(&lvAttemptDetails)
	if osd.GetStatus() == wealthonboarding.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE {
		advice = adviceTransientError
	} else if osd.GetStatus() == wealthonboarding.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED {
		l1 = osd.GetSubStatus().String() + ": liveness failed without retry option"
		advice = "Please escalate internally, we can reset the liveness attempt for the user here on a case by case basis"
	} else if osd.GetStatus() == wealthonboarding.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS {
		if osd.GetSubStatus() == wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_LIVENESS_FAILED_WITH_RETRY {
			l1 = osd.GetSubStatus().String() + ": this is not the first attempt of liveness, user will see check liveness screen"
			if lvAttemptDetails.AttemptCount > 0 {
				advice = livenessAdviceMap[od.GetMetadata().GetLivenessData().GetLivenessAttempts()[lvAttemptDetails.AttemptCount-1].GetLivenessRejectReason()]
			}
		} else {
			l1 = "this is first attempt, user will see missing data screen along with liveness option"
		}
	} else if osd.GetSubStatus() != wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSPECIFIED {
		l1 = osd.GetSubStatus().String()
		advice = adviceContactDeveloper
	}
	addnDetailsStr := ""
	if err != nil {
		logger.Error(ctx, "failed to marshal lvAttempt", zap.Error(err))
	} else {
		addnDetailsStr = string(addnDetails)
	}
	return &wealthonboarding.CurrentStageTroubleShootingDetails{
		Stage:             osd.GetStep().String(),
		Status:            stepStatusStringMap[osd.GetStatus()],
		UpdatedAt:         osd.GetUpdatedAt(),
		L1:                l1,
		Advice:            advice,
		AdditionalDetails: addnDetailsStr,
	}
}

type FaceMatchHandler struct {
	conf *config.Config
}

func NewFaceMatchHandler(conf *config.Config) *FaceMatchHandler {
	return &FaceMatchHandler{
		conf: conf,
	}
}

// nolint: dupl
func (h *FaceMatchHandler) GetOnboardingStageDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.StageDetails {
	metadata, err := json.Marshal(od.GetMetadata().GetFaceMatchData())
	metadataStr := ""
	if err != nil {
		logger.Error(ctx, "failed to marshal FaceMatchData", zap.Error(err))
	} else {
		metadataStr = string(metadata)
	}
	return &wealthonboarding.StageDetails{
		Status:      stepStatusStringMap[osd.GetStatus()],
		Description: h.conf.StageDescriptionMapping[osd.GetStep().String()],
		UpdatedAt:   osd.GetUpdatedAt(),
		MetaData:    metadataStr,
	}
}

// nolint: dupl
func (h *FaceMatchHandler) GetStageTroubleShootingDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.CurrentStageTroubleShootingDetails {
	advice, l2 := "", ""
	if osd.GetStatus() == wealthonboarding.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE {
		advice = adviceTransientError
	} else if osd.GetStatus() == wealthonboarding.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED {
		fmAttempts := od.GetMetadata().GetFaceMatchData().GetFaceMatchAttempts()
		if len(fmAttempts) > 0 {
			fmScore := fmAttempts[len(fmAttempts)-1].GetFaceMatchScore()
			fmThreshold := fmAttempts[len(fmAttempts)-1].GetFmScoreThreshold()
			l2 = fmt.Sprintf("FaceMatch Score: %f\nFaceMatch Threshold: %f", fmScore, fmThreshold)
			advice = "If account has been put on hold due to low face match cases, escalate internally, devs would allow user to complete again"
		}
	}
	l1 := ""
	if osd.GetSubStatus() != wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSPECIFIED {
		l1 = osd.GetSubStatus().String()
	}
	type fmAttempt struct {
		AttemptCount        int
		LatestAttemptStatus wealthonboarding.FaceMatchData_FaceMatchAttempt_FaceMatchStatus
		LatestAttemptScore  float32
		ThresholdScore      float32
	}
	var fmAttemptDetails fmAttempt
	fmAttemptDetails.AttemptCount = len(od.GetMetadata().GetFaceMatchData().GetFaceMatchAttempts())
	if fmAttemptDetails.AttemptCount > 0 {
		latestAttempt := od.GetMetadata().GetFaceMatchData().GetFaceMatchAttempts()[fmAttemptDetails.AttemptCount-1]
		fmAttemptDetails.LatestAttemptStatus = latestAttempt.GetFaceMatchStatus()
		fmAttemptDetails.LatestAttemptScore = latestAttempt.GetFaceMatchScore()
		fmAttemptDetails.ThresholdScore = latestAttempt.GetFmScoreThreshold()
	}
	addnDetails, err := json.Marshal(&fmAttemptDetails)
	addnDetailsStr := ""
	if err != nil {
		logger.Error(ctx, "failed to marshal fmAttempt", zap.Error(err))
	} else {
		addnDetailsStr = string(addnDetails)
	}
	return &wealthonboarding.CurrentStageTroubleShootingDetails{
		Stage:             osd.GetStep().String(),
		Status:            stepStatusStringMap[osd.GetStatus()],
		UpdatedAt:         osd.GetUpdatedAt(),
		L1:                l1,
		L2:                l2,
		Advice:            advice,
		AdditionalDetails: addnDetailsStr,
	}
}

type CreateAndSignKraDocketHandler struct {
	conf *config.Config
}

func NewCreateAndSignKraDocketHandler(conf *config.Config) *CreateAndSignKraDocketHandler {
	return &CreateAndSignKraDocketHandler{
		conf: conf,
	}
}

// nolint: dupl
func (h *CreateAndSignKraDocketHandler) GetOnboardingStageDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.StageDetails {
	type kraDocketMetadata struct {
		DocSignStatus []string
	}

	var esignStatus []string
	for _, esignInfo := range od.GetMetadata().GetKraDocketInfo().GetEsignInfos() {
		switch esignInfo.Status {
		case wealthonboarding.TxnState_TXN_STATE_TYPE_UNSPECIFIED:
			esignStatus = append(esignStatus, "")
		case wealthonboarding.TxnState_TXN_STATE_PENDING:
			esignStatus = append(esignStatus, "E_SIGN_PENDING")
		case wealthonboarding.TxnState_TXN_STATE_SIGNED:
			esignStatus = append(esignStatus, "E_SIGN_SIGNED")
		case wealthonboarding.TxnState_TXN_STATE_COMPLETED:
			esignStatus = append(esignStatus, "E_SIGN_SUCCESS")
		}
	}
	metadata, err := json.Marshal(&kraDocketMetadata{DocSignStatus: esignStatus})
	metadataStr := ""
	if err != nil {
		logger.Error(ctx, "failed to marshal fetchKycMetadata", zap.Error(err))
	} else {
		metadataStr = string(metadata)
	}
	return &wealthonboarding.StageDetails{
		Status:      stepStatusStringMap[osd.GetStatus()],
		Description: h.conf.StageDescriptionMapping[osd.GetStep().String()],
		UpdatedAt:   osd.GetUpdatedAt(),
		MetaData:    metadataStr,
	}
}

// nolint: dupl
func (h *CreateAndSignKraDocketHandler) GetStageTroubleShootingDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.CurrentStageTroubleShootingDetails {
	advice, l1 := "", ""
	if osd.GetStatus() == wealthonboarding.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE {
		advice = adviceTransientError
	} else {
		switch osd.GetSubStatus() {
		case wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_CUSTOMER_SIGN_PENDING:
			l1 = osd.GetSubStatus().String() + ": user is yet to sign, should see aadhaar esgin screen"
			advice = "Ask user to enter aadhaar number and OTP"
		case wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_CUSTOMER_SIGNED:
			l1 = osd.GetSubStatus().String() + ": user signed, should see wealth onboarding landing screen"
			advice = "User should automatically go to next step"
		case wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_CKYC_RECORD_NOT_FOUND, wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_CKYC_ACCOUNT_TYPE_L,
			wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_CKYC_ACCOUNT_TYPE_O, wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_CKYC_ACCOUNT_TYPE_S,
			wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSUPPORTED_AADHAAR_TYPE, wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_DOB_MISMATCH_ERROR_IN_CKYC_DOWNLOAD_RESPONSE:
			l1 = osd.GetSubStatus().String() + ": this should have been caught in collect missing data step"
			advice = adviceContactDeveloper
		case wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_POA_ID_MISSING:
			l1 = osd.GetSubStatus().String() + ": POA id number is not present, kyc form cannot be created in this case"
			advice = "User cannot proceed"
		default:
			l1 = osd.GetSubStatus().String()
			advice = adviceContactDeveloper
		}
	}
	esignCount := len(od.GetMetadata().GetKraDocketInfo().GetEsignInfos())
	additionalDetailsStr := ""
	if esignCount > 0 {
		additionalDetails := struct {
			LatestESignStatus string
		}{
			LatestESignStatus: od.GetMetadata().GetKraDocketInfo().GetEsignInfos()[esignCount-1].GetStatus().String(),
		}
		additionalDetailsJson, err := json.Marshal(additionalDetails)
		if err != nil {
			logger.Error(ctx, "failed to marshal additionalDetails", zap.Error(err))
		} else {
			additionalDetailsStr = string(additionalDetailsJson)
		}
	}
	return &wealthonboarding.CurrentStageTroubleShootingDetails{
		Stage:             osd.GetStep().String(),
		Status:            stepStatusStringMap[osd.GetStatus()],
		UpdatedAt:         osd.GetUpdatedAt(),
		L1:                l1,
		Advice:            advice,
		AdditionalDetails: additionalDetailsStr,
	}
}

type ConsolidatedManualReviewHandler struct {
	conf *config.Config
}

func NewConsolidatedManualReviewHandler(conf *config.Config) *ConsolidatedManualReviewHandler {
	return &ConsolidatedManualReviewHandler{
		conf: conf,
	}
}

// nolint: dupl
func (h *ConsolidatedManualReviewHandler) GetOnboardingStageDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.StageDetails {
	metadata, err := json.Marshal(od.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping())
	metadataStr := ""
	if err != nil {
		logger.Error(ctx, "failed to marshal reviewAttemptMapping", zap.Error(err))
	} else {
		metadataStr = string(metadata)
	}
	return &wealthonboarding.StageDetails{
		Status:      stepStatusStringMap[osd.GetStatus()],
		Description: h.conf.StageDescriptionMapping[osd.GetStep().String()],
		UpdatedAt:   osd.GetUpdatedAt(),
		MetaData:    metadataStr,
	}
}

// nolint: dupl
func (h *ConsolidatedManualReviewHandler) GetStageTroubleShootingDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.CurrentStageTroubleShootingDetails {
	advice, l1 := "", ""
	if osd.GetStatus() == wealthonboarding.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE {
		advice = adviceTransientError
	} else if osd.GetStatus() == wealthonboarding.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS {
		l1 = "manual review is in progress"
		advice = "ask user to wait for 3 days for the review to be done"
	} else if osd.GetSubStatus() == wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_INVALID_REVIEW_STATUS {
		l1 = osd.GetSubStatus().String() + ": this is not a valid review status"
		advice = adviceContactDeveloper
	} else if osd.GetSubStatus() == wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_PERMANENTLY_REJECTED {
		l1 = osd.GetSubStatus().String() + ": permanently rejected in review"
		advice = "user cannot proceed"
	} else if osd.GetSubStatus() == wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSPECIFIED {
		l1 = osd.GetSubStatus().String()
		advice = adviceContactDeveloper
	}
	additionalDetailsStr := ""
	additionalDetailsJson, err := json.Marshal(od.GetMetadata().GetManualReviewAttempts().GetReviewAttemptMapping())
	if err != nil {
		logger.Error(ctx, "failed to marshal additionalDetails", zap.Error(err))
	} else {
		additionalDetailsStr = string(additionalDetailsJson)
	}
	return &wealthonboarding.CurrentStageTroubleShootingDetails{
		Stage:             osd.GetStep().String(),
		Status:            stepStatusStringMap[osd.GetStatus()],
		UpdatedAt:         osd.GetUpdatedAt(),
		L1:                l1,
		Advice:            advice,
		AdditionalDetails: additionalDetailsStr,
	}
}

type ConfirmPepAndCitizenshipHandler struct {
	conf *config.Config
}

func NewConfirmPepAndCitizenshipHandler(conf *config.Config) *ConfirmPepAndCitizenshipHandler {
	return &ConfirmPepAndCitizenshipHandler{
		conf: conf,
	}
}

// nolint: dupl
func (h *ConfirmPepAndCitizenshipHandler) GetOnboardingStageDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.StageDetails {
	return &wealthonboarding.StageDetails{
		Status:      stepStatusStringMap[osd.GetStatus()],
		Description: h.conf.StageDescriptionMapping[osd.GetStep().String()],
		UpdatedAt:   osd.GetUpdatedAt(),
	}
}

// nolint: dupl
func (h *ConfirmPepAndCitizenshipHandler) GetStageTroubleShootingDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.CurrentStageTroubleShootingDetails {
	advice, l1 := "", ""
	if osd.GetStatus() == wealthonboarding.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE {
		advice = adviceTransientError
	} else if osd.GetStatus() == wealthonboarding.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS {
		l1 = "consent fetch in progress, user should see wealth onboarding agreement screen"
	} else if osd.GetSubStatus() == wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_CONSENT_FETCH_FAILED {
		l1 = osd.GetSubStatus().String() + ": unable to get consent data"
		advice = adviceContactDeveloper
	} else if osd.GetSubStatus() != wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSPECIFIED {
		l1 = osd.GetSubStatus().String()
		advice = adviceContactDeveloper
	}
	return &wealthonboarding.CurrentStageTroubleShootingDetails{
		Stage:     osd.GetStep().String(),
		Status:    stepStatusStringMap[osd.GetStatus()],
		UpdatedAt: osd.GetUpdatedAt(),
		L1:        l1,
		Advice:    advice,
	}
}

type InvestmentDataCollectionHandler struct {
	conf *config.Config
}

func NewInvestmentDataCollectionHandler(conf *config.Config) *InvestmentDataCollectionHandler {
	return &InvestmentDataCollectionHandler{
		conf: conf,
	}
}

// nolint: dupl
func (h *InvestmentDataCollectionHandler) GetOnboardingStageDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.StageDetails {
	return &wealthonboarding.StageDetails{
		Status:      stepStatusStringMap[osd.GetStatus()],
		Description: h.conf.StageDescriptionMapping[osd.GetStep().String()],
		UpdatedAt:   osd.GetUpdatedAt(),
	}
}

// nolint: dupl
func (h *InvestmentDataCollectionHandler) GetStageTroubleShootingDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.CurrentStageTroubleShootingDetails {
	advice, l1, l2 := "", "", ""
	if osd.GetStatus() == wealthonboarding.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE {
		advice = adviceTransientError
	} else if osd.GetSubStatus() == wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_MANDATORY_DETAILS_MISSING_IN_FI {
		l1 = osd.GetSubStatus().String() + ": Some details mandatory to start investing were not collected"
		l2 = "Missing details:"
		if od.GetMetadata().GetBankDetails() == nil {
			l2 += " Bank details"
		}
		advice = adviceContactDeveloper
	} else if osd.GetSubStatus() != wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSPECIFIED {
		l1 = osd.GetSubStatus().String()
		advice = adviceContactDeveloper
	}
	return &wealthonboarding.CurrentStageTroubleShootingDetails{
		Stage:     osd.GetStep().String(),
		Status:    stepStatusStringMap[osd.GetStatus()],
		UpdatedAt: osd.GetUpdatedAt(),
		L1:        l1,
		L2:        l2,
		Advice:    advice,
	}
}

type DownloadKraDocsHandler struct {
	conf *config.Config
}

func NewDownloadKraDocsHandler(conf *config.Config) *DownloadKraDocsHandler {
	return &DownloadKraDocsHandler{
		conf: conf,
	}
}

// nolint: dupl
func (h *DownloadKraDocsHandler) GetOnboardingStageDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.StageDetails {
	return &wealthonboarding.StageDetails{
		Status:      stepStatusStringMap[osd.GetStatus()],
		Description: h.conf.StageDescriptionMapping[osd.GetStep().String()],
		UpdatedAt:   osd.GetUpdatedAt(),
	}
}

// nolint: dupl
func (h *DownloadKraDocsHandler) GetStageTroubleShootingDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.CurrentStageTroubleShootingDetails {
	advice, l1 := "", ""
	if osd.GetStatus() == wealthonboarding.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE {
		advice = adviceTransientError
	} else if osd.GetSubStatus() == wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_EMPTY_DOWNLOAD_API_RESPONSE {
		l1 = osd.GetSubStatus().String() + ": DOB mismatch with PAN card"
		advice = "ask user to upload PAN card photo by using one time invest option"
	} else {
		l1 = "downloading docket is pending"
		advice = "this is not a blocker for investment, user should not face any issues with onboarding"
	}
	return &wealthonboarding.CurrentStageTroubleShootingDetails{
		Stage:     osd.GetStep().String(),
		Status:    stepStatusStringMap[osd.GetStatus()],
		UpdatedAt: osd.GetUpdatedAt(),
		L1:        l1,
		Advice:    advice,
	}
}

type UploadKraDocketHandler struct {
	conf *config.Config
}

func NewUploadKraDocketHandler(conf *config.Config) *UploadKraDocketHandler {
	return &UploadKraDocketHandler{
		conf: conf,
	}
}

// nolint: dupl
func (h *UploadKraDocketHandler) GetOnboardingStageDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.StageDetails {
	return &wealthonboarding.StageDetails{
		Status:      stepStatusStringMap[osd.GetStatus()],
		Description: h.conf.StageDescriptionMapping[osd.GetStep().String()],
		UpdatedAt:   osd.GetUpdatedAt(),
	}
}

// nolint: dupl
func (h *UploadKraDocketHandler) GetStageTroubleShootingDetails(ctx context.Context, od *wealthonboarding.OnboardingDetails, osd *wealthonboarding.OnboardingStepDetails) *wealthonboarding.CurrentStageTroubleShootingDetails {
	advice, l1, l2 := "", "", ""
	if osd.GetStatus() == wealthonboarding.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE {
		advice = adviceTransientError
	} else if osd.GetStatus() == wealthonboarding.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE {
		advice = adviceFutureScopeError
	} else if osd.GetStatus() == wealthonboarding.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED {
		switch osd.GetSubStatus() {
		case wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_UPLOAD_ATTEMPT_FAILED:
			l1 = osd.GetSubStatus().String() + ": To complete KYC, we need to upload the docket to KRA. That failed, so user's KYC is not completed and user cannot proceed with investment"
			advice = adviceContactDeveloper
		case wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_UPDATED_KRA_STATUS_NOT_SUPPORTED:
			l1 = osd.GetSubStatus().String() + ": this status is not handled yet"
			advice = adviceContactDeveloper
			uploadAttempts := od.GetMetadata().GetUploadKraDocData().GetUploadAttempts()
			if len(uploadAttempts) != 0 {
				if uploadAttempts[len(uploadAttempts)-1].GetPanEnquiry().GetStatus() == wealthVgPb.KraStatus_KRA_STATUS_HOLD {
					l2 = uploadAttempts[len(uploadAttempts)-1].GetPanEnquiry().GetHoldDeactivateRemarks()
				}
			}
		}
	}
	if l1 == "" && osd.GetSubStatus() != wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSPECIFIED {
		l1 = osd.GetSubStatus().String()
		advice = adviceContactDeveloper
	}
	if osd.GetSubStatus() != wealthonboarding.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSPECIFIED {
		l1 = osd.GetSubStatus().String()
	}
	return &wealthonboarding.CurrentStageTroubleShootingDetails{
		Stage:     osd.GetStep().String(),
		Status:    stepStatusStringMap[osd.GetStatus()],
		UpdatedAt: osd.GetUpdatedAt(),
		L1:        l1,
		Advice:    advice,
		L2:        l2,
	}
}
