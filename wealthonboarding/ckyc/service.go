//nolint:funlen
package ckyc

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	types "github.com/epifi/gamma/api/typesv2"
	vgCkycPb "github.com/epifi/gamma/api/vendorgateway/wealth/ckyc"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woCkycPb "github.com/epifi/gamma/api/wealthonboarding/ckyc"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/dao"
	woErr "github.com/epifi/gamma/wealthonboarding/errors"
	"github.com/epifi/gamma/wealthonboarding/helper"
)

const (
	ckycNoLength         = 14
	ckycDobMismatchError = "date of birth entered does not match with the date of birth stated in the ckyc number"
)

type Service struct {
	ckycDataDao      dao.CkycDataDao
	vgCkycClient     vgCkycPb.CkycClient
	commonHelper     helper.ICommonHelper
	docHelper        helper.DocumentHelper
	vendorRequestDao dao.VendorRequestDao
	conf             *config.Config
}

func NewService(
	ckycDataDao dao.CkycDataDao,
	vgCkycClient vgCkycPb.CkycClient,
	commonHelper helper.ICommonHelper,
	documentHelper helper.DocumentHelper,
	vendorRequestDao dao.VendorRequestDao,
	conf *config.Config) *Service {
	return &Service{
		ckycDataDao:      ckycDataDao,
		vendorRequestDao: vendorRequestDao,
		vgCkycClient:     vgCkycClient,
		commonHelper:     commonHelper,
		docHelper:        documentHelper,
		conf:             conf,
	}
}

// CreateOrGetSearchData returns the KYC verification(search) details of user from CKYC
// Returns error if no KYC record for the user is present with CKYC
// Note: If KYC verification(search) details are already stored in our tables, we do not re-fetch and store any updated KYC details for the user
func (s *Service) CreateOrGetSearchData(ctx context.Context, actorId, panNumber string) (*woPb.CkycSearchData, error) {
	// check if search data already exists for user
	getRes, getErr := s.ckycDataDao.GetByActorIdAndType(ctx, actorId, woCkycPb.CkycDataType_CKYC_DATA_TYPE_SEARCH)
	if getErr != nil && !errors.Is(getErr, epifierrors.ErrRecordNotFound) {
		return nil, errors.Wrap(getErr, "error while fetching ckyc data from db")
	}
	if getRes != nil {
		return getRes.GetCkycPayload().GetCkycSearchData(), nil
	}
	// we do not have the ckyc search data of the user, fetch from Vg and populate in ckyc db
	reqId := generateReqId()
	req := vgCkycPb.GetCkycSearchRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_CKYC},
		Pan:    panNumber,
		ReqId:  reqId,
	}
	csRes, csResErr := s.vgCkycClient.GetCkycSearch(ctx, &req)
	if err := epifigrpc.IsRPCErrorWithDowntime(csRes, csResErr); err != nil {
		if csRes.GetStatus().GetCode() == rpcPb.StatusRecordNotFound().GetCode() {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrap(err, "error while calling GetCkycSearchRequest")
	}
	if cfg.IsProdEnv(s.conf.Application.Environment) || cfg.IsTestEnv(s.conf.Application.Environment) {
		// make an entry in vendor request dao
		// TODO(ismail): add go routine for the following create
		vrdErr := s.vendorRequestDao.Create(ctx, &woPb.VendorRequest{
			Id:            uuid.New().String(),
			ActorId:       actorId,
			Vendor:        commonvgpb.Vendor_CKYC,
			ResponseCode:  csRes.GetVendorStatus().GetCode(),
			RpcStatusCode: csRes.GetStatus().Code,
			RawResponse:   csRes.GetVendorStatus().GetDescription(),
			RequestId:     reqId,
			TraceId:       epificontext.TraceIdFromContext(ctx),
			RawRequest:    req.String(),
			Api:           woPb.Api_CKYC_SEARCH,
			CreatedAt:     timestamppb.Now(),
			UpdatedAt:     timestamppb.Now(),
		})
		if vrdErr != nil {
			return nil, errors.Wrap(vrdErr, "error while creating entry in vendor request dao")
		}
	}

	// map the ckyc vg response to wealth onboarding ckyc search data
	ckycSearchData, ckycSearchDataErr := s.convertToCkycSearchData(ctx, actorId, csRes)
	if ckycSearchDataErr != nil {
		return nil, errors.Wrap(ckycSearchDataErr, "error while fetching ckyc search data from vg response")
	}
	_, err := s.ckycDataDao.Create(ctx, &woCkycPb.CkycData{
		KycDate:     ckycSearchData.GetKycDate(),
		UpdatedDate: ckycSearchData.GetUpdatedDate(),
		AccountType: ckycSearchData.GetCkycAccountType(),
		CkycPayload: &woCkycPb.CkycPayload{
			CkycData: &woCkycPb.CkycPayload_CkycSearchData{
				CkycSearchData: ckycSearchData,
			},
		},
		ActorId:      actorId,
		CkycDataType: woCkycPb.CkycDataType_CKYC_DATA_TYPE_SEARCH,
	})
	if err != nil {
		return nil, errors.Wrap(err, "error while creating entry in ckyc db")
	}
	return ckycSearchData, nil
}

func (s *Service) GetCkycData(ctx context.Context, req *woCkycPb.GetCkycDataRequest) (*woCkycPb.GetCkycDataResponse, error) {
	if req.GetActorId() == "" || req.GetDataType() == woCkycPb.CkycDataType_CKYC_DATA_TYPE_UNSPECIFIED {
		return &woCkycPb.GetCkycDataResponse{Status: rpcPb.StatusInvalidArgument()}, nil
	}
	res, err := s.ckycDataDao.GetByActorIdAndType(ctx, req.GetActorId(), req.GetDataType())
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		return &woCkycPb.GetCkycDataResponse{Status: rpcPb.StatusRecordNotFound()}, nil
	}
	return &woCkycPb.GetCkycDataResponse{Status: rpcPb.StatusOk(), CkycData: res}, nil
}

func (s *Service) convertToCkycSearchData(ctx context.Context, actorId string, res *vgCkycPb.GetCkycSearchResponse) (*woPb.CkycSearchData, error) {
	var docWithS3Paths *types.DocumentProof
	if res.GetPhoto() != nil {
		photo, sErr := s.commonHelper.SanitizeImage(ctx, res.GetPhoto())
		if sErr != nil {
			logger.Error(ctx, "error while sanitizing img from tiff format", zap.Error(sErr))
		}
		doc := &types.DocumentProof{
			ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PHOTOGRAPH,
			Photo: []*commontypes.Image{
				photo,
			},
		}
		// uploading raw data with base64 data to s3 and getting doc with S3 urls
		var err error
		docWithS3Paths, err = s.docHelper.UploadDoc(ctx, actorId, doc)
		if err != nil {
			return nil, errors.Wrap(err, "failed to get docWithS3Paths")
		}
	}
	ckycSearchRes := &woPb.CkycSearchData{
		MaskedCkycNo:    res.GetCkycNumber(),
		CkycReferenceId: res.GetCkycReferenceId(),
		Name:            res.GetName(),
		FathersName:     res.GetFathersName(),
		Age:             res.GetAge(),
		Photo:           docWithS3Paths,
		KycDate:         res.GetKycDate(),
		UpdatedDate:     res.GetUpdatedDate(),
	}
	ckycSearchRes.Name = res.GetName()
	ckycSearchRes.FathersName = res.GetFathersName()
	ckycSearchRes.Age = res.GetAge()
	ckycSearchRes.Photo = docWithS3Paths
	ckycSearchRes.KycDate = res.GetKycDate()
	ckycSearchRes.UpdatedDate = res.GetUpdatedDate()
	ckycSearchRes.CkycAccountType = GetCKYCAccountTypeByAccountNumber(ctx, res.GetCkycNumber())
	ckycSearchRes.ConstitutionType = res.GetConstitutionType()
	return ckycSearchRes, nil
}

// CreateOrGetDownloadData returns the KYC details including KYC proofs, documents, etc. of user from CKYC
// This should only be called after we have stored the KYC verification details of user using CreateOrGetSearchData
// CKYC identification number received in CreateOrGetSearchData response is sent in request of CreateOrGetDownloadData to download the KYC details of user
// Note: If KYC details are already stored in our tables, we do not re-fetch and store any updated KYC details for the user
func (s *Service) CreateOrGetDownloadData(ctx context.Context, actorId string, dob *date.Date) (*woPb.CkycDownloadData, error) {
	// check if download data already exists for user
	getRes, getErr := s.ckycDataDao.GetByActorIdAndType(ctx, actorId, woCkycPb.CkycDataType_CKYC_DATA_TYPE_DOWNLOAD)
	if getErr != nil && !errors.Is(getErr, epifierrors.ErrRecordNotFound) {
		return nil, errors.Wrap(getErr, "error while fetching ckyc data from db")
	}
	if getRes != nil {
		return getRes.GetCkycPayload().GetCkycDownloadData(), nil
	}
	// fetch the user's ckyc search data
	sdRes, sdErr := s.ckycDataDao.GetByActorIdAndType(ctx, actorId, woCkycPb.CkycDataType_CKYC_DATA_TYPE_SEARCH)
	if sdErr != nil {
		return nil, errors.Wrap(sdErr, "error while fetching ckyc search data from db")
	}
	// we do not have the ckyc download data of the user, fetch from Vg and populate in ckyc db
	dobString := datetime.DateToString(dob, "02-01-2006", datetime.IST)
	ckycReferenceId, cErr := getCorrectedCkycRefId(sdRes.GetCkycPayload().GetCkycSearchData().GetCkycReferenceId())
	if cErr != nil {
		return nil, cErr
	}
	reqId := generateReqId()
	req := vgCkycPb.CkycDownloadRequest{
		Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_CKYC},
		CkycReferenceId: ckycReferenceId,
		Dob:             dobString,
		ReqId:           reqId,
	}
	cdRes, cdResErr := s.vgCkycClient.CkycDownload(ctx, &req)
	if err := epifigrpc.IsRPCErrorWithDowntime(cdRes, cdResErr); err != nil {
		return nil, errors.Wrap(err, "error while calling CkycDownload")
	}
	if pErr := parseCkycDownloadError(cdRes.GetErrorMessage()); pErr != nil {
		return nil, pErr
	}
	// TODO(ismail): add go routine for the following create
	dbres := s.vendorRequestDao.Create(ctx, &woPb.VendorRequest{
		Id:            uuid.New().String(),
		ActorId:       actorId,
		Vendor:        commonvgpb.Vendor_CKYC,
		ResponseCode:  cdRes.GetVendorStatus().Code,
		RpcStatusCode: cdRes.GetStatus().Code,
		RawResponse:   cdRes.GetVendorStatus().Description,
		RequestId:     reqId,
		TraceId:       epificontext.TraceIdFromContext(ctx),
		RawRequest:    req.String(),
		Api:           woPb.Api_CKYC_DOWNLOAD,
		CreatedAt:     timestamppb.Now(),
		UpdatedAt:     timestamppb.Now(),
	})
	if dbres != nil {
		logger.Error(ctx, "failed to create entry in vendor_req_resps", zap.Error(dbres))
	}
	ckycDownloadData, ckycDownloadErr := s.fetchCkycDownloadData(ctx, actorId, cdRes)
	if ckycDownloadErr != nil {
		return nil, errors.Wrap(ckycDownloadErr, "error while fetching ckyc download data")
	}
	_, err := s.ckycDataDao.Create(ctx, &woCkycPb.CkycData{
		CkycNo:      ckycDownloadData.GetPersonalDetails().GetCkycNo(),
		KycDate:     nil,
		UpdatedDate: nil,
		AccountType: ckycDownloadData.GetPersonalDetails().GetAccountType(),
		CkycPayload: &woCkycPb.CkycPayload{
			CkycData: &woCkycPb.CkycPayload_CkycDownloadData{
				CkycDownloadData: ckycDownloadData,
			},
		},
		ActorId:      actorId,
		CkycDataType: woCkycPb.CkycDataType_CKYC_DATA_TYPE_DOWNLOAD,
	})
	if err != nil {
		return nil, errors.Wrap(err, "error while creating entry in ckyc db")
	}
	return ckycDownloadData, nil
}

func (s *Service) fetchCkycDownloadData(ctx context.Context, actorId string, res *vgCkycPb.CkycDownloadResponse) (*woPb.CkycDownloadData, error) {
	var ids []*woPb.CkycIdentityDetail
	for _, id := range res.GetIdentityDetails() {
		ids = append(ids, &woPb.CkycIdentityDetail{
			IdentityType:       getCkycIdentityType(id.GetIdentityType()),
			IdentityNumber:     id.GetIdentityNumber(),
			VerificationStatus: getCkycVerificationStatus(id.GetVerificationStatus()),
		})
	}
	var docs []*types.DocumentProof
	idList := ids
	for _, im := range res.GetPersonalDetails().GetImageDetailsList() {
		// redacting documents we are not allowed to store
		if helper.IsDocumentStoreAllowed(im.GetCode()) {
			if im.GetData() == "" {
				continue
			}
			docImg, sErr := s.commonHelper.SanitizeImage(ctx, &commontypes.Image{
				ImageType:       im.GetType(),
				ImageDataBase64: im.GetData(),
			})
			if sErr != nil {
				logger.Error(ctx, "error while sanitizing img from tiff format", zap.Error(sErr))
			}
			doc := &types.DocumentProof{
				ProofType: im.GetCode(),
				Photo: []*commontypes.Image{
					docImg,
				},
			}
			for _, id := range idList {
				if getDocumentProofTypeFromIdentityType(id.GetIdentityType()) == im.GetCode() {
					doc.Id = id.GetIdentityNumber()
					break
				}
			}
			// uploading raw data with base64 data to s3 and getting doc with S3 urls
			docWithS3Paths, err := s.docHelper.UploadDoc(ctx, actorId, doc)
			if err != nil {
				return nil, errors.Wrap(err, "failed to get docWithS3Paths")
			}
			docs = append(docs, docWithS3Paths)
		} else {
			logger.Info(ctx, fmt.Sprintf("not allowed to store document proof type: %v", im.GetCode().String()))
		}
	}
	pd := &woPb.CkycPersonalDetails{
		AccountType:      getCkycAccountType(res.GetPersonalDetails().GetAccountType()),
		Name:             res.GetPersonalDetails().GetName(),
		FathersName:      res.GetPersonalDetails().GetFathersName(),
		MothersName:      res.GetPersonalDetails().GetMothersName(),
		Gender:           res.GetPersonalDetails().GetGender(),
		Dob:              res.GetPersonalDetails().GetDob(),
		Nationality:      res.GetPersonalDetails().GetNationality(),
		PermanentAddress: res.GetPersonalDetails().GetPermanentAddress(),
		CurrentAddress:   res.GetPersonalDetails().GetCurrentAddress(),
		TelOffice:        res.GetPersonalDetails().GetTelOffice(),
		TelResidential:   res.GetPersonalDetails().GetTelResidential(),
		MobileNumber:     res.GetPersonalDetails().GetMobileNumber(),
		EmailId:          res.GetPersonalDetails().GetEmailId(),
		DocumentsList:    docs,
		Pan:              res.GetPersonalDetails().GetPan(),
		ProofOfAddress:   res.GetPersonalDetails().GetProofOfAddress(),
		Remarks:          res.GetPersonalDetails().GetRemarks(),
		MaidenName:       res.GetPersonalDetails().GetMaidenName(),
		CkycNo:           res.GetPersonalDetails().GetCkycNo(),
	}
	return &woPb.CkycDownloadData{
		PersonalDetails: pd,
		IdentityDetails: ids,
	}, nil
}

// nolint: gosec
// CKYC download API takes in a "Request ID" which should unique throughout a day
// For more ref CKYC API: https://drive.google.com/drive/u/0/folders/1k_Ozchu8l_qoWHXdYmaucyB6Gia7L8od
func generateReqId() string {
	now := time.Now()
	nsOfTheDay := time.Hour*time.Duration(now.Hour()) + time.Minute*time.Duration(now.Minute()) + time.Second*time.Duration(now.Second()) + time.Nanosecond*time.Duration(now.Nanosecond())
	microsecondOfTheDay := int(nsOfTheDay) / int(time.Millisecond)
	// adding a random number to microsecondOfTheDay to avoid collision
	rand.Seed(time.Now().UnixNano())
	microsecondOfTheDay += (rand.Intn(100)) + 1
	return strconv.Itoa(microsecondOfTheDay)
}

func GetCKYCAccountTypeByAccountNumber(ctx context.Context, ckycNumber string) woPb.CkycAccountType {
	ckycNum := strings.ToUpper(ckycNumber)
	switch {
	case strings.HasPrefix(ckycNum, "L"):
		logger.Info(ctx, "CKYC Number contains L")
		return woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_SIMPLIFIED
	case strings.HasPrefix(ckycNum, "S"):
		logger.Info(ctx, "CKYC Number contains S")
		return woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_SMALL
	case strings.HasPrefix(ckycNum, "O"):
		logger.Info(ctx, "CKYC Number contains O")
		return woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_OTP_EKYC
	default:
		return woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_NORMAL
	}
}

func parseCkycDownloadError(err string) error {
	switch {
	case err == "":
		return nil
	case strings.Contains(strings.ToLower(err), ckycDobMismatchError):
		return woErr.ErrCkycDobMismatch
	default:
		return errors.New(err)
	}
}

func getCorrectedCkycRefId(ckycRefNo string) (string, error) {
	if len(ckycRefNo) < ckycNoLength {
		return "", errors.New(fmt.Sprintf("invalid ckyc number: %v", ckycRefNo))
	}
	return ckycRefNo[len(ckycRefNo)-ckycNoLength:], nil
}
func getCkycIdentityType(identityType vgCkycPb.IdentityType) woPb.CkycIdentityType {
	switch identityType {
	default:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_TYPE_UNSPECIFIED
	case vgCkycPb.IdentityType_IDENTITY_TYPE_PASSPORT:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_PASSPORT
	case vgCkycPb.IdentityType_IDENTITY_TYPE_VOTER_ID:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_VOTER_ID
	case vgCkycPb.IdentityType_IDENTITY_TYPE_PAN:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_PAN
	case vgCkycPb.IdentityType_IDENTITY_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR
	case vgCkycPb.IdentityType_IDENTITY_TYPE_DRIVING_LICENSE:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_DRIVING_LICENSE
	case vgCkycPb.IdentityType_IDENTITY_TYPE_NREGA:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_NREGA
	case vgCkycPb.IdentityType_IDENTITY_TYPE_NATIONAL_POPULATION_REGISTRY_LETTER:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_NATIONAL_POPULATION_REGISTRY_LETTER
	case vgCkycPb.IdentityType_IDENTITY_TYPE_EKYC_AUTHENTICATION:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_EKYC_AUTHENTICATION
	case vgCkycPb.IdentityType_IDENTITY_TYPE_OFFLINE_AADHAAR_VERIFICATION:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_OFFLINE_AADHAAR_VERIFICATION
	case vgCkycPb.IdentityType_IDENTITY_TYPE_OFFLINE_OTHERS:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_OFFLINE_OTHERS
	case vgCkycPb.IdentityType_IDENTITY_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GOVERNMENT:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GOVERNMENT
	case vgCkycPb.IdentityType_IDENTITY_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GAZETTED:
		return woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GAZETTED
	}
}

func getCkycVerificationStatus(verificationStatus vgCkycPb.VerificationStatus) woPb.CkycVerificationStatus {
	switch verificationStatus {
	default:
		return woPb.CkycVerificationStatus_CKYC_VERIFICATION_STATUS_TYPE_UNSPECIFIED
	case vgCkycPb.VerificationStatus_VERIFICATION_STATUS_YES:
		return woPb.CkycVerificationStatus_CKYC_VERIFICATION_STATUS_YES
	case vgCkycPb.VerificationStatus_VERIFICATION_STATUS_NO:
		return woPb.CkycVerificationStatus_CKYC_VERIFICATION_STATUS_NO
	}
}

func getCkycAccountType(accountType vgCkycPb.AccountType) woPb.CkycAccountType {
	switch accountType {
	default:
		return woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_TYPE_UNSPECIFIED
	case vgCkycPb.AccountType_ACCOUNT_TYPE_NORMAL:
		return woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_NORMAL
	case vgCkycPb.AccountType_ACCOUNT_TYPE_SMALL:
		return woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_SMALL
	case vgCkycPb.AccountType_ACCOUNT_TYPE_SIMPLIFIED:
		return woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_SIMPLIFIED
	case vgCkycPb.AccountType_ACCOUNT_TYPE_OTP_EKYC:
		return woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_OTP_EKYC
	}
}

func getDocumentProofTypeFromIdentityType(idType woPb.CkycIdentityType) types.DocumentProofType {
	switch idType {
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_PASSPORT:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_PASSPORT
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_VOTER_ID:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_VOTER_ID
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_PAN:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_DRIVING_LICENSE:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_DRIVING_LICENSE
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_NREGA:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_NREGA_JOB_CARD
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_NATIONAL_POPULATION_REGISTRY_LETTER:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_NATIONAL_POPULATION_REGISTRY_LETTER
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_EKYC_AUTHENTICATION:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_EKYC_AUTHENTICATION
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_OFFLINE_AADHAAR_VERIFICATION:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_OFFLINE_AADHAAR_VERIFICATION
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_OFFLINE_OTHERS:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_OTHERS
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GOVERNMENT:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GOVERNMENT
	case woPb.CkycIdentityType_CKYC_IDENTITY_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GAZETTED:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GAZETTED
	default:
		return types.DocumentProofType_DOCUMENT_PROOF_TYPE_UNSPECIFIED
	}
}
