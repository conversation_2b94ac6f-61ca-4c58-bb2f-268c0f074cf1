//nolint:gocritic
package ckyc

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	ckycVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/ckyc"
	mocks2 "github.com/epifi/gamma/api/vendorgateway/wealth/ckyc/mocks"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woCkycPb "github.com/epifi/gamma/api/wealthonboarding/ckyc"
	dao2 "github.com/epifi/gamma/wealthonboarding/dao"
	mocks "github.com/epifi/gamma/wealthonboarding/test/mocks/dao"
)

func TestCkycService_Create(t *testing.T) {
	ctr := gomock.NewController(t)
	mockCkycDataDao := mocks.NewMockCkycDataDao(ctr)
	mockVendorRequestDataDao := mocks.NewMockVendorRequestDao(ctr)
	mockCkycClient := mocks2.NewMockCkycClient(ctr)

	type fields struct {
		ckycDataDao dao2.CkycDataDao
	}
	type args struct {
		ctx                   context.Context
		createCkycDataRequest *woCkycPb.CkycData
		mocks                 []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *woPb.CkycSearchData
		wantErr bool
		err     error
	}{
		{
			name: "successfully created ckyc data",
			fields: fields{
				ckycDataDao: mockCkycDataDao,
			},
			args: args{
				ctx: context.Background(),
				createCkycDataRequest: &woCkycPb.CkycData{
					AccountType:  woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_NORMAL,
					CkycPayload:  &woCkycPb.CkycPayload{CkycData: &woCkycPb.CkycPayload_CkycSearchData{CkycSearchData: &woPb.CkycSearchData{}}},
					ActorId:      "random_actor_id",
					CkycDataType: woCkycPb.CkycDataType_CKYC_DATA_TYPE_SEARCH,
				},
				mocks: []interface{}{
					mockCkycDataDao.EXPECT().GetByActorIdAndType(gomock.Any(), gomock.Any(), woCkycPb.CkycDataType_CKYC_DATA_TYPE_SEARCH).
						Return(nil, epifierrors.ErrRecordNotFound).Times(1),
					mockCkycDataDao.EXPECT().Create(gomock.Any(), gomock.Any()).
						Return(&woCkycPb.CkycData{
							AccountType:  woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_NORMAL,
							CkycPayload:  &woCkycPb.CkycPayload{CkycData: &woCkycPb.CkycPayload_CkycSearchData{CkycSearchData: &woPb.CkycSearchData{}}},
							ActorId:      "random_actor_id",
							CkycDataType: woCkycPb.CkycDataType_CKYC_DATA_TYPE_SEARCH,
						}, nil),
					mockCkycClient.EXPECT().GetCkycSearch(gomock.Any(), gomock.Any()).Return(&ckycVgPb.GetCkycSearchResponse{
						Status:          rpcPb.StatusOk(),
						CkycReferenceId: "random_ckyc_number",
					}, nil),
					mockVendorRequestDataDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			want: &woPb.CkycSearchData{
				CkycReferenceId: "random_ckyc_number",
				CkycAccountType: woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_NORMAL,
			},
			wantErr: false,
		},
		{
			name: "failed to create ckyc data - duplicate entry",
			fields: fields{
				ckycDataDao: mockCkycDataDao,
			},
			args: args{
				ctx: context.Background(),
				createCkycDataRequest: &woCkycPb.CkycData{
					AccountType:  woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_NORMAL,
					CkycPayload:  &woCkycPb.CkycPayload{CkycData: &woCkycPb.CkycPayload_CkycSearchData{CkycSearchData: &woPb.CkycSearchData{}}},
					ActorId:      "random_actor_id",
					CkycDataType: woCkycPb.CkycDataType_CKYC_DATA_TYPE_SEARCH,
				},
				mocks: []interface{}{
					mockCkycDataDao.EXPECT().GetByActorIdAndType(gomock.Any(), gomock.Any(), woCkycPb.CkycDataType_CKYC_DATA_TYPE_SEARCH).
						Return(nil, epifierrors.ErrRecordNotFound).Times(1),
					mockCkycDataDao.EXPECT().Create(gomock.Any(), gomock.Any()).
						Return(nil, epifierrors.ErrDuplicateEntry),
					mockCkycClient.EXPECT().GetCkycSearch(gomock.Any(), gomock.Any()).Return(&ckycVgPb.GetCkycSearchResponse{
						Status:     rpcPb.StatusOk(),
						CkycNumber: "random_ckyc_number",
					}, nil),
					mockVendorRequestDataDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrDuplicateEntry,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Service{
				conf:             conf,
				ckycDataDao:      tt.fields.ckycDataDao,
				vgCkycClient:     mockCkycClient,
				vendorRequestDao: mockVendorRequestDataDao,
			}
			got, err := c.CreateOrGetSearchData(tt.args.ctx, "random_actor_id", "random_pan_number")
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateOrGetSearchData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
				if !assert.Equal(t, got, tt.want) {
					t.Errorf("Create ckyc data mis match = got %v, want %v", got, tt.want)
				}
			} else {
				if !errors.Is(err, tt.err) {
					t.Errorf("Error don't match, got = %v, want %v", err, tt.err)
					return
				}
			}
		})
	}
}

func TestCkycService_GetCkycData(t *testing.T) {
	ctr := gomock.NewController(t)
	mockCkycDataDao := mocks.NewMockCkycDataDao(ctr)

	type fields struct {
		ckycDataDao dao2.CkycDataDao
	}
	type args struct {
		ctx                context.Context
		getCkycDataRequest *woCkycPb.GetCkycDataRequest
		mocks              []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *woCkycPb.GetCkycDataResponse
		wantErr bool
		err     error
	}{
		{
			name: "successfully get ckyc data",
			fields: fields{
				ckycDataDao: mockCkycDataDao,
			},
			args: args{
				ctx: context.Background(),
				getCkycDataRequest: &woCkycPb.GetCkycDataRequest{
					ActorId:  "random_actor_id",
					DataType: woCkycPb.CkycDataType_CKYC_DATA_TYPE_SEARCH,
				},
				mocks: []interface{}{
					mockCkycDataDao.EXPECT().GetByActorIdAndType(gomock.Any(), "random_actor_id", woCkycPb.CkycDataType_CKYC_DATA_TYPE_SEARCH).
						Return(&woCkycPb.CkycData{
							CkycNo:       "random_ckyc_number",
							AccountType:  woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_NORMAL,
							CkycPayload:  &woCkycPb.CkycPayload{CkycData: &woCkycPb.CkycPayload_CkycSearchData{CkycSearchData: &woPb.CkycSearchData{}}},
							ActorId:      "random_actor_id",
							CkycDataType: woCkycPb.CkycDataType_CKYC_DATA_TYPE_SEARCH,
						}, nil),
				},
			},
			want: &woCkycPb.GetCkycDataResponse{
				Status: rpcPb.StatusOk(),
				CkycData: &woCkycPb.CkycData{
					CkycNo:       "random_ckyc_number",
					AccountType:  woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_NORMAL,
					CkycPayload:  &woCkycPb.CkycPayload{CkycData: &woCkycPb.CkycPayload_CkycSearchData{CkycSearchData: &woPb.CkycSearchData{}}},
					ActorId:      "random_actor_id",
					CkycDataType: woCkycPb.CkycDataType_CKYC_DATA_TYPE_SEARCH,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to get ckyc data - record not found",
			fields: fields{
				ckycDataDao: mockCkycDataDao,
			},
			args: args{
				ctx: context.Background(),
				getCkycDataRequest: &woCkycPb.GetCkycDataRequest{
					ActorId:  "random_actor_id",
					DataType: woCkycPb.CkycDataType_CKYC_DATA_TYPE_DOWNLOAD,
				},
				mocks: []interface{}{
					mockCkycDataDao.EXPECT().GetByActorIdAndType(gomock.Any(), "random_actor_id", woCkycPb.CkycDataType_CKYC_DATA_TYPE_DOWNLOAD).
						Return(nil, epifierrors.ErrRecordNotFound),
				},
			},
			want: &woCkycPb.GetCkycDataResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
		},
		{
			name: "failed to get ckyc data - invalid argument",
			fields: fields{
				ckycDataDao: mockCkycDataDao,
			},
			args: args{
				ctx: context.Background(),
				getCkycDataRequest: &woCkycPb.GetCkycDataRequest{
					ActorId:  "random_actor_id",
					DataType: woCkycPb.CkycDataType_CKYC_DATA_TYPE_UNSPECIFIED,
				},
				mocks: []interface{}{},
			},
			want: &woCkycPb.GetCkycDataResponse{
				Status: rpcPb.StatusInvalidArgument(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Service{
				ckycDataDao: tt.fields.ckycDataDao,
			}
			got, err := c.GetCkycData(tt.args.ctx, tt.args.getCkycDataRequest)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCkycData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
				if !assert.Equal(t, got, tt.want) {
					t.Errorf("GetCkycData mis match = got %v, want %v", got, tt.want)
				}
			}
		})
	}

}
