package docket

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	b64 "encoding/base64"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"sort"
	"strings"

	"github.com/imdario/mergo"
	"github.com/jinzhu/copier"
	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/aws/v2/lambda"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/file"

	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/vendorgateway/wealth/inhouseocr"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/dao"
)

type ExtractionService struct {
	s3Client      s3.S3Client
	onbDetailsDao dao.OnboardingDetailsDao
	lambdaClient  lambda.LambdaClient
}

func NewExtractionService(
	s3Client s3.S3Client,
	onbDetailsDao dao.OnboardingDetailsDao,
	lambdaClient lambda.LambdaClient,
) *ExtractionService {
	return &ExtractionService{
		s3Client:      s3Client,
		onbDetailsDao: onbDetailsDao,
		lambdaClient:  lambdaClient,
	}
}

// Unzip downloads the docket from S3 and unzips its contents into a temporary directory
// Callers must clean up the directory after usage to avoid resource leaks.
func (s *ExtractionService) Unzip(ctx context.Context, preInvOnbDetails *woPb.OnboardingDetails) (*file.TempDir, error) {
	if preInvOnbDetails.GetCurrentStep() != woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC ||
		preInvOnbDetails.GetStatus() != woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED {
		return nil, errors.Wrap(epifierrors.ErrFailedPrecondition, fmt.Sprintf("docket is not available for the user as user's current step is: %v, with status: %v", preInvOnbDetails.GetCurrentStep(), preInvOnbDetails.GetStatus()))
	}
	docketS3Path, err := GetDocketS3Path(preInvOnbDetails.GetMetadata().GetPanDetails().GetId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting docket s3 path by PAN")
	}
	docket, err := s.s3Client.Read(ctx, docketS3Path)
	if err != nil {
		return nil, errors.Wrapf(err, "error reading docket s3 path: %s", docketS3Path)
	}
	unzippedDir, err := file.Unzip(docket, 3)
	if err != nil {
		return nil, errors.Wrap(err, "error unzipping docket")
	}
	return unzippedDir, nil
}

type ExtractedImage struct {
	// Data stores contents of an image
	Data []byte

	// Type identifies the encoding of the image
	Type commontypes.ImageType
}

// ExtractImageOnPage extracts the contents of a page in input image file.
// TIF files are converted to PNGs.
func (s *ExtractionService) ExtractImageOnPage(ctx context.Context, f []byte, pageNum int) (*ExtractedImage, error) {
	ext, err := file.GetFileExtension(f)
	if err != nil {
		return nil, errors.Wrap(err, "error getting file extension")
	}
	imgType := commontypes.StringToImageType(ext)
	if imgType == commontypes.ImageType_IMAGE_TYPE_UNSPECIFIED {
		return nil, errors.Errorf("image type unspecified for ext: %s", ext)
	}
	switch imgType {
	case commontypes.ImageType_PDF:
		b, err := file.ExtractPDFPage(f, pageNum)
		if err != nil {
			return nil, errors.Wrapf(err, "error extracting pdf page num: %d", pageNum)
		}
		return &ExtractedImage{Data: b, Type: imgType}, nil
	case commontypes.ImageType_TIFF:
		b, err := s.extractTifPageAsPng(ctx, f, pageNum)
		if err != nil {
			return nil, errors.Wrapf(err, "error extracting TIF page num: %d", pageNum)
		}
		return &ExtractedImage{Data: b, Type: commontypes.ImageType_PNG}, nil
	case commontypes.ImageType_PNG,
		commontypes.ImageType_JPEG:
		// JPEGs and PNGs do not contain multiple pages
		return &ExtractedImage{Data: f, Type: imgType}, nil
	default:
		return nil, errors.Errorf("image extraction unsupported for img type: %s", imgType.String())
	}
}

type ImageConversionRes struct {
	// Data stores converted PNG image returned by the image converter lambda function
	Data string `json:"data,omitempty"`

	// ErrorMessage provides more info if image was not converted by lambda function
	ErrorMessage string `json:"errorMessage,omitempty"`
}

// extractTifPageAsPng takes a TIF image and page number and returns a PNG image of the requested page.
func (s *ExtractionService) extractTifPageAsPng(ctx context.Context, tif []byte, pageNum int) ([]byte, error) {
	lambdaFuncPayload := fmt.Sprintf(`{"data":"%v", "selectedPage":%v}`, b64.StdEncoding.EncodeToString(tif), pageNum)
	convResB, err := s.lambdaClient.Execute(ctx, "ImageConverterFunction", []byte(lambdaFuncPayload))
	if err != nil {
		return nil, errors.Wrapf(err, "error converting TIF image page num: %d", pageNum)
	}
	res := &ImageConversionRes{}
	err = json.Unmarshal(convResB, res)
	if err != nil {
		return nil, err
	}
	if res.ErrorMessage != "" {
		return nil, errors.Errorf("error converting TIF to PNG: %s", res.ErrorMessage)
	}
	if res.Data == "" {
		return nil, errors.New("empty PNG data returned by lambda func")
	}
	b, err := b64.StdEncoding.DecodeString(getRepairedBase64String(res.Data))
	if err != nil {
		return nil, errors.Wrap(err, "error decoding base-64 encoded PNG image")
	}
	return b, nil
}

// TODO(Brijesh): Understand why this is needed and replace all occurrences with one.
func getRepairedBase64String(b64String string) string {
	return strings.ReplaceAll(b64String, "\n", "")
}

type PanLocation struct {
	// Name of the file inside docket that contains the PAN image
	FileName string

	// Page number inside the file that contains the PAN image
	PageNum int
}

// ExtractAndStoreKRADocketPAN extracts the page containing PAN image of a user via the PAN location inside docket.
func (s *ExtractionService) ExtractAndStoreKRADocketPAN(ctx context.Context, actorId string, panLocation *PanLocation) (err error) {
	preInvOnbDetails, err := s.onbDetailsDao.GetByActorIdAndOnbType(ctx, actorId, woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT)
	if err != nil {
		return errors.Wrap(err, "error getting pre-investment onboarding details for actor")
	}
	pan := preInvOnbDetails.GetMetadata().GetPanDetails().GetId()
	if len(pan) == 0 {
		return errors.New("no PAN found in pre-investment onboarding details")
	}
	panLocation.FileName = strings.TrimSpace(panLocation.FileName)
	if panLocation.FileName == "" {
		return errors.New("empty pan location filename")
	}
	if panLocation.PageNum <= 0 || panLocation.PageNum > 100 {
		return errors.Errorf("pan location page num can be from 1 to 100: %d", panLocation.PageNum)
	}
	unzippedDocketDir, err := s.Unzip(ctx, preInvOnbDetails)
	if err != nil {
		return errors.Wrapf(err, "error unzipping docket: %s", actorId)
	}
	defer func() {
		if rmErr := unzippedDocketDir.Remove(); rmErr != nil {
			err = errors.Wrap(rmErr, "error removing unzipped docket dir")
		}
	}()
	matchedFileName, err := verifyAndGetPanFileName(unzippedDocketDir.Path, panLocation.FileName)
	if err != nil {
		return errors.Wrapf(err, "error getting PAN file name in unzipped docket dir: %s", panLocation.FileName)
	}
	f, err := os.ReadFile(filepath.Join(unzippedDocketDir.Path, filepath.Clean(matchedFileName)))
	if err != nil {
		return errors.Wrapf(err, "error reading PAN file from unzipped docket dir: %s", matchedFileName)
	}
	panImage, err := s.ExtractImageOnPage(ctx, f, panLocation.PageNum)
	if err != nil {
		return errors.Wrapf(err, "error extracting PAN image from page num: %d in file: %s", panLocation.PageNum, matchedFileName)
	}
	err = s.StorePANExtractedFromDocketWithOCRResults(ctx, &StorePANImageWithOCRResultsRequest{
		PreInvestmentOnboardingDetails: preInvOnbDetails,
		PanImage:                       panImage,
	})
	if err != nil {
		return errors.Wrap(err, "error storing PAN extracted from docket with OCR results")
	}
	return nil
}

// verifyAndGetPanFileName checks and returns the name of the file in docket that
// matches the filename provided in PAN location.
func verifyAndGetPanFileName(unzippedDocketDirPath, panFileName string) (string, error) {
	fileInfos, err := ioutil.ReadDir(unzippedDocketDirPath)
	if err != nil {
		return "", errors.Wrapf(err, "error reading unzipped docket dir path: %s", unzippedDocketDirPath)
	}
	if len(fileInfos) == 0 {
		return "", errors.Errorf("no file present in unzipped dir path: %s", unzippedDocketDirPath)
	}
	var matchedFileName string
	for _, fi := range fileInfos {
		if strings.EqualFold(panFileName, fi.Name()) {
			matchedFileName = fi.Name()
			break
		}
	}
	if matchedFileName == "" {
		return "", errors.Errorf("no file name in unzipped docket dir matched with PAN location filename: %s", matchedFileName)
	}
	return matchedFileName, nil
}

// GetFileNamesInDir returns the names of all unzipped docket files in temporary directory.
func GetFileNamesInDir(dirPath string) ([]string, error) {
	fileInfos, err := ioutil.ReadDir(dirPath)
	if err != nil {
		return nil, errors.Wrapf(err, "error reading unzipped docket dir path: %s", dirPath)
	}
	if len(fileInfos) == 0 {
		return nil, errors.Errorf("no file present in unzipped dir path: %s", dirPath)
	}
	fileNames := make([]string, len(fileInfos))
	for i, info := range fileInfos {
		fileNames[i] = info.Name()
	}
	sort.Strings(fileNames)
	return fileNames, nil
}

type StorePANImageWithOCRResultsRequest struct {
	PreInvestmentOnboardingDetails *woPb.OnboardingDetails

	PanImage *ExtractedImage

	// Optional when PAN is located manually
	OcrRes *inhouseocr.GetExpiryDocResponse
}

func (s *ExtractionService) StorePANExtractedFromDocketWithOCRResults(ctx context.Context, req *StorePANImageWithOCRResultsRequest) error {
	panImageFileName := fmt.Sprintf("%v.%v", 0, req.PanImage.Type)
	panS3Path := filepath.Join("converted_doc_proof_image/", req.PreInvestmentOnboardingDetails.GetActorId(), types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN.String(), panImageFileName)
	err := s.s3Client.Write(ctx, panS3Path, req.PanImage.Data, "bucket-owner-full-control")
	if err != nil {
		return errors.Wrapf(err, "error storing PAN image in s3 path: %s", panS3Path)
	}
	var panWithOCRResults *woPb.OcrDocumentProof
	if req.OcrRes != nil {
		// reachable only from workflow call
		ocrResCopy := &inhouseocr.GetExpiryDocResponse{}
		copyErr := copier.Copy(ocrResCopy, req.OcrRes)
		if copyErr != nil {
			return errors.Wrap(copyErr, "error copying ocr res")
		}
		ocrResCopy.MaskedDocumentProof.S3Paths = append(ocrResCopy.GetMaskedDocumentProof().GetS3Paths(), panS3Path)
		ocrResCopy.MaskedDocumentProof.Photo = nil
		panWithOCRResults = &woPb.OcrDocumentProof{
			Doc:              ocrResCopy.GetMaskedDocumentProof(),
			ConfidenceScore:  ocrResCopy.GetConfidenceScore(),
			VendorReviewFlag: ocrResCopy.GetReview(),
		}
	} else {
		panWithOCRResults = &woPb.OcrDocumentProof{
			Doc: &types.DocumentProof{
				ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
				Id:        req.PreInvestmentOnboardingDetails.GetMetadata().GetPanDetails().GetId(),
				S3Paths:   []string{panS3Path},
			},
			// PANs detected manually are assigned 100% confidence and marked as manually-verified
			ConfidenceScore:  100,
			VerificationMode: woPb.VerificationMode_VERIFICATION_MODE_MANUAL,
		}
	}
	err = mergo.Merge(req.PreInvestmentOnboardingDetails, &woPb.OnboardingDetails{Metadata: &woPb.OnboardingMetadata{
		PersonalDetails: &woPb.PersonalDetails{KraDocketPan: panWithOCRResults},
	}}, mergo.WithOverride)
	if err != nil {
		return errors.Wrap(err, "error merging ocr pan with pre-investment onboarding details")
	}
	err = s.onbDetailsDao.Update(ctx, req.PreInvestmentOnboardingDetails, []woPb.OnboardingDetailsFieldMask{
		woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_METADATA,
	})
	if err != nil {
		return errors.Wrap(err, "error updating pre-investment onboarding details")
	}
	return nil
}
