package docket

import (
	"path/filepath"
	"strings"

	"github.com/pkg/errors"
)

const (
	KraDocketPath = "kra_docket"
)

// GetDocketS3Path uses the PAN to return a unique S3 file path where a docket should be stored
// PAN extraction depends on docket being stored in this path.
// DO NOT CHANGE THE PATH.
// All KRAs use PAN as the unique identifier of an investor.
// At any point of time, no two actively investing users on Fi should have the same PAN.
func GetDocketS3Path(pan string) (string, error) {
	if strings.TrimSpace(pan) == "" {
		return "", errors.New("PAN can not be empty for getting docket S3 path")
	}
	return filepath.Join(KraDocketPath, pan, "docket.zip"), nil
}
