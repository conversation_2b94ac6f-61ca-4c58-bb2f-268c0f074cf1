package wealthonboarding

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"strconv"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	types "github.com/epifi/gamma/api/typesv2"
	userpb "github.com/epifi/gamma/api/user"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

// getCtxWithDevicePlatformVersion returns a ctx with device platform and version added, if not already present in ctx
// platform and version are crucial for feature flag evaluations and deeplink customizations
// in practical scenarios' device details are only fetched in cases where request didn't originate from device
// Note: In case of errors, original ctx in request is returned as it is
func getCtxWithDevicePlatformVersion(ctx context.Context, userClient userpb.UsersClient, actorId string) (context.Context, error) {
	if epificontext.AppPlatformFromContext(ctx) != commontypes.Platform_PLATFORM_UNSPECIFIED {
		return ctx, nil
	}
	getUserDevicePropertiesRes, err := userClient.GetUserDeviceProperties(ctx, &userpb.GetUserDevicePropertiesRequest{
		ActorId:       actorId,
		PropertyTypes: []types.DeviceProperty{types.DeviceProperty_DEVICE_PROP_APP_VERSION_INFO},
	})
	if err = epifigrpc.RPCError(getUserDevicePropertiesRes, err); err != nil {
		return nil, errors.Wrap(err, "error while GetUserDeviceProperties")
	}

	for _, userDeviceProperty := range getUserDevicePropertiesRes.GetUserDevicePropertyList() {
		if userDeviceProperty.GetDeviceProperty() == types.DeviceProperty_DEVICE_PROP_APP_VERSION_INFO {
			ctx = epificontext.CtxWithAppPlatform(ctx, userDeviceProperty.GetPropertyValue().GetAppVersionInfo().GetPlatform())
			ctx = epificontext.CtxWithAppVersionCode(ctx, strconv.Itoa(int(userDeviceProperty.GetPropertyValue().GetAppVersionInfo().GetAppVersionCode())))
			return ctx, nil
		}
	}
	return nil, errors.New("unable to get app version for actor")
}

// repairDataWithCondition trys to fix the data inconsistency in the onboarding details of a user
// it does not return any error and only logs it, as it is based on best effort basis
// it checks for some conditions before executing the repair data handler method
func (s *Service) repairDataWithCondition(ctx context.Context, od *woPb.OnboardingDetails) {
	if od.GetStatus() == woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED ||
		(od.GetOnboardingType() == woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT && (od.GetCurrentStep() == woPb.OnboardingStep_ONBOARDING_STEP_UPLOAD_DOCKET || od.GetCurrentStep() == woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC)) {
		handlers := s.repairProvider.GetRepairHandlers()
		for _, handle := range handlers {
			// calling repair implementation to the fix the onboarding data
			_, rpErr := handle.Repair(ctx, od)
			if rpErr != nil {
				logger.Error(ctx, "error while executing repair data", zap.Error(rpErr))
			}
		}
	}
}
