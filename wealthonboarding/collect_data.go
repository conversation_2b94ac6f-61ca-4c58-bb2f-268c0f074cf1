package wealthonboarding

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"encoding/base64"
	"fmt"
	"io"
	"time"

	"github.com/epifi/be-common/pkg/async/goroutine"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/names"

	rmsPb "github.com/epifi/gamma/api/rms/manager"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	wealthPkg "github.com/epifi/gamma/pkg/wealthonboarding"
	woErr "github.com/epifi/gamma/wealthonboarding/errors"
	"github.com/epifi/gamma/wealthonboarding/event"
	"github.com/epifi/gamma/wealthonboarding/helper"
)

// CollectDataFromCustomer stores the data collected from user inputs
// There's a limit on the number of times a user is allowed to upload PAN image
// In case the limit is reached, user is put into manual intervention and no deeplink is returned by the orchestrator
// Until the limit hasn't been reached, this RPC returns a rpc.StatusFailedPrecondition status,
// which is used in the frontend layer to show deeplink.Screen_WEALTH_ONBOARDING_DATA_COLLECTION_ERROR_SCREEN with appropriate deeplink params
func (s *Service) CollectDataFromCustomer(ctx context.Context, req *woPb.CollectDataFromCustomerRequest) (*woPb.CollectDataFromCustomerResponse, error) {
	isRetroActiveDataCollection := req.GetNomineeDeclarationDetails().GetChoice() != commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED ||
		req.GetSkipSigningAdvisoryAgreement() != commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED ||
		req.GetRiskProfilingConsentId() != "" || req.GetInvestmentRiskProfileId() != ""

	od, odErr := s.getOnbDetails(ctx, req.GetActorId(), req.GetOnboardingType(), isRetroActiveDataCollection)
	if odErr != nil {
		return &woPb.CollectDataFromCustomerResponse{Status: rpc.StatusInternal()}, nil
	}
	// TODO(Vikas): handle race condition(maybe use select for update)
	// populate collected data to onboarding details
	mapErr := s.mapCollectedFieldsToMetadata(ctx, od, req)
	if mapErr != nil {
		logger.Error(ctx, "error mapping collected fields", zap.Error(mapErr))
		return &woPb.CollectDataFromCustomerResponse{Status: rpc.StatusInternal()}, nil
	}
	mapDocsErr := s.mapPANAddressSign(ctx, od, req)
	if mapDocsErr != nil && !errors.Is(mapDocsErr, woErr.ErrOCRLowConfidence) {
		logger.Error(ctx, "error mapping pan address and sign", zap.Error(mapDocsErr))
		return &woPb.CollectDataFromCustomerResponse{Status: rpc.StatusInternal()}, nil
	}

	if req.GetNomineeDeclarationDetails().GetChoice() != commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
		txnErr := s.idempotentTxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
			return s.storeNomineeAndUnpauseSubscriptions(txnCtx, od)
		})
		if txnErr != nil {
			logger.Error(ctx, "error storing nominee details and un-pausing subscriptions in txn", zap.Error(txnErr))
			return &woPb.CollectDataFromCustomerResponse{Status: rpc.StatusInternal()}, nil
		}
	} else {
		uErr := s.onboardingDetailsDao.Update(ctx, od, []woPb.OnboardingDetailsFieldMask{
			woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_METADATA,
		})
		if uErr != nil {
			logger.Error(ctx, "error while updating onboarding details", zap.Error(uErr))
			return &woPb.CollectDataFromCustomerResponse{Status: rpc.StatusInternal()}, nil
		}
	}
	status := rpc.StatusOk()
	if errors.Is(mapDocsErr, woErr.ErrOCRLowConfidence) && len(od.GetMetadata().GetCustomerProvidedData().GetPanOcrAttempts()) < s.conf.MaxPanUploadAttempts {
		status = rpc.StatusFailedPrecondition()
	}
	return &woPb.CollectDataFromCustomerResponse{Status: status}, nil
}

func (s *Service) CollectDataFromCustomerWithStream(stream woPb.WealthOnboarding_CollectDataFromCustomerWithStreamServer) error {
	ctx := stream.Context()
	var (
		customerInfo *woPb.BasicCustomerInfo
		panImgData   []byte
	)

	for {
		// Get response and possible error message from the stream
		beStreamPkt, pktRecvErr := stream.Recv()

		// Break for loop if there are no more stream packets
		if errors.Is(pktRecvErr, io.EOF) {
			logger.Info(ctx, "stream EOF signal received, processing data")
			beStreamRes := s.updateMetadataOnEndOfStream(ctx, customerInfo, panImgData)
			sendErr := stream.SendAndClose(beStreamRes)
			if sendErr != nil {
				return errors.Wrap(sendErr, "error closing stream")
			}
			logger.Info(ctx, "processed and closed stream")
			return nil
		}

		// Handle a possible error
		if pktRecvErr != nil {
			logger.Error(ctx, "error receiving stream packet", zap.Error(pktRecvErr))
			return pktRecvErr
		}

		// No error.
		switch req := beStreamPkt.GetCustomerData().(type) {
		case *woPb.CollectDataFromCustomerWithStreamRequest_BasicInfo:
			logger.Info(ctx, "received basic customer info chunk")
			customerInfo = req.BasicInfo
		case *woPb.CollectDataFromCustomerWithStreamRequest_PanImgChunk:
			logger.Info(ctx, "received pan img chunk")
			panImgData = append(panImgData, req.PanImgChunk...)
		}
	}
}

func (s *Service) updateMetadataOnEndOfStream(ctx context.Context,
	basicInfo *woPb.BasicCustomerInfo, panImgData []byte) *woPb.CollectDataFromCustomerWithStreamResponse {
	// TODO(Brijesh): handle race condition(maybe use select for update)
	od, odErr := s.getOnbDetails(ctx, basicInfo.GetActorId(), basicInfo.GetOnboardingType(), false)
	if odErr != nil {
		logger.Error(ctx, "error getting onboarding details", zap.Error(odErr))
		return &woPb.CollectDataFromCustomerWithStreamResponse{Status: rpc.StatusInternal()}
	}

	base64EncodedPanImg := base64.StdEncoding.EncodeToString(panImgData)
	pan := &types.DocumentProof{
		ProofType: basicInfo.GetProofType(),
		Photo:     []*commontypes.Image{{ImageType: basicInfo.GetImgType(), ImageDataBase64: base64EncodedPanImg}},
	}
	mapDocsErr := s.mapPANAddressSign(ctx, od, &woPb.CollectDataFromCustomerRequest{Pan: pan})
	if mapDocsErr != nil && !errors.Is(mapDocsErr, woErr.ErrOCRLowConfidence) {
		logger.Error(ctx, "error mapping pan", zap.Error(mapDocsErr))
		return &woPb.CollectDataFromCustomerWithStreamResponse{Status: rpc.StatusInternal()}
	}

	uErr := s.onboardingDetailsDao.Update(ctx, od, []woPb.OnboardingDetailsFieldMask{
		woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_METADATA,
	})
	if uErr != nil {
		logger.Error(ctx, "error while updating onboarding details", zap.Error(uErr))
		return &woPb.CollectDataFromCustomerWithStreamResponse{Status: rpc.StatusInternal()}
	}
	status := rpc.StatusOk()
	if errors.Is(mapDocsErr, woErr.ErrOCRLowConfidence) && len(od.GetMetadata().GetCustomerProvidedData().GetPanOcrAttempts()) < s.conf.MaxPanUploadAttempts {
		status = rpc.StatusFailedPrecondition()
	}
	return &woPb.CollectDataFromCustomerWithStreamResponse{Status: status}
}

func (s *Service) getOnbDetails(ctx context.Context, actorId string, onbType woPb.OnboardingType, isRetroActiveDataCollection bool) (*woPb.OnboardingDetails, error) {
	if onbType == woPb.OnboardingType_ONBOARDING_TYPE_UNSPECIFIED {
		logger.Error(ctx, "onboarding type is unspecified")
		return nil, errors.New("onboarding type is unspecified")
	}
	wealthOnbDetails, err := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, actorId, woPb.OnboardingType_ONBOARDING_TYPE_WEALTH)
	if err != nil {
		logger.Error(ctx, "error while fetching onboarding details for actor", zap.Error(err))
		return nil, err
	}
	if isRetroActiveDataCollection {
		// nominee is stored as part of wealth onboarding (not pre-investment onboarding), even for users whose onboarding journey is complete
		return wealthOnbDetails, nil
	}
	if wealthOnbDetails.GetStatus() == woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED && onbType == woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT {
		preInvOnbDetails, err2 := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, actorId, onbType)
		if err2 != nil {
			logger.Error(ctx, "error while fetching onboarding details for actor", zap.Error(err2))
			return nil, err2
		}
		return preInvOnbDetails, nil
	}
	return wealthOnbDetails, nil
}

//nolint:funlen
func (s *Service) mapCollectedFieldsToMetadata(ctx context.Context, od *woPb.OnboardingDetails, req *woPb.CollectDataFromCustomerRequest) error {
	platform, version := epificontext.AppPlatformAndVersion(ctx)

	if od.GetMetadata() == nil {
		od.Metadata = &woPb.OnboardingMetadata{}
	}
	if od.GetMetadata().GetCustomerProvidedData() == nil {
		od.GetMetadata().CustomerProvidedData = &woPb.CustomerProvidedData{}
	}
	if od.GetMetadata().GetPersonalDetails() == nil {
		od.GetMetadata().PersonalDetails = &woPb.PersonalDetails{}
	}
	metadata := od.GetMetadata()
	cpd := metadata.GetCustomerProvidedData()
	if req.GetMaritalStatus() != types.MaritalStatus_UNSPECIFIED {
		metadata.GetPersonalDetails().MaritalStatus = req.MaritalStatus
		cpd.MaritalStatus = req.GetMaritalStatus()
		goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
			s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), event.NewDataCollectedEvent(od.GetActorId(), time.Now(), od.GetCurrentWealthFlow(), version, platform, event.MaritalStatusData, event.SUCCESS, ""))
		})
	}
	if req.GetGender() != types.Gender_GENDER_UNSPECIFIED {
		metadata.GetPersonalDetails().Gender = req.GetGender()
		cpd.Gender = req.GetGender()
		goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
			s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), event.NewDataCollectedEvent(od.GetActorId(), time.Now(), od.GetCurrentWealthFlow(), version, platform, event.GenderData, event.SUCCESS, ""))
		})
	}
	if req.GetIncomeSlab() != types.IncomeSlab_INCOME_SLAB_UNSPECIFIED {
		metadata.GetPersonalDetails().IncomeSlab = req.GetIncomeSlab()
		cpd.IncomeSlab = req.GetIncomeSlab()
		goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
			s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), event.NewDataCollectedEvent(od.GetActorId(), time.Now(), od.GetCurrentWealthFlow(), version, platform, event.IncomeSlabData, event.SUCCESS, ""))
		})
	}
	if req.GetNationality() != types.Nationality_NATIONALITY_UNSPECIFIED {
		if req.GetNationality() != types.Nationality_NATIONALITY_INDIAN {
			logger.Error(ctx, "nationality other than Indian is not supported")
			return errors.New("nationality other than Indian is not supported")
		}
		metadata.GetPersonalDetails().Nationality = req.GetNationality()
	}
	if req.GetPoliticallyExposedStatus() != types.PoliticallyExposedStatus_POLITICALLY_EXPOSED_STATUS_UNSPECIFIED {
		if req.GetPoliticallyExposedStatus() != types.PoliticallyExposedStatus_POLITICALLY_EXPOSED_STATUS_NOT_APPLICABLE {
			logger.Error(ctx, "politically exposed status other than NOT_APPLICABLE is not supported")
			return errors.New("politically exposed status other than NOT_APPLICABLE is not supported")
		}
		metadata.GetPersonalDetails().PoliticallyExposedStatus = req.GetPoliticallyExposedStatus()
	}
	if req.GetResidentialStatus() != types.ResidentialStatus_RESIDENTIAL_STATUS_UNSPECIFIED {
		if req.GetResidentialStatus() != types.ResidentialStatus_RESIDENT_INDIVIDUAL {
			logger.Error(ctx, "residential status other than RESIDENT_INDIVIDUAL is not supported")
			return errors.New("residential status other than RESIDENT_INDIVIDUAL is not supported")
		}
		metadata.GetPersonalDetails().ResidentialStatus = req.GetResidentialStatus()
	}
	if req.GetHasDigilockerAccount() != commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
		cpd.HasDigilockerAccount = req.GetHasDigilockerAccount()
		if req.GetHasDigilockerAccount() == commontypes.BooleanEnum_TRUE {
			goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
				s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), event.NewDataCollectedEvent(od.GetActorId(), time.Now(), od.GetCurrentWealthFlow(), version, platform, event.DigilockerAccountAvailable, event.SUCCESS, ""))
			})
		} else {
			goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
				s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), event.NewDataCollectedEvent(od.GetActorId(), time.Now(), od.GetCurrentWealthFlow(), version, platform, event.DigilockerAccountUnAvailable, event.SUCCESS, ""))
			})
		}
	}
	if req.GetNomineeDeclarationDetails().GetChoice() != commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
		// Taking customerName to check that the Nominee name should not be equal to the Investor Name
		customerName := od.GetMetadata().GetPersonalDetails().GetName()
		allUserNomineesRes, userErr := s.userClient.GetNominees(ctx, &userPb.GetNomineesRequest{ActorId: req.GetActorId()})
		validationErr := wealthPkg.ValidateNomineeDeclarationDetails(ctx, req.GetNomineeDeclarationDetails(), names.ToString(customerName), allUserNomineesRes, userErr)
		if validationErr != nil {
			// TODO(Brijesh): Figure out how client can show the validation error in a proper way, maybe build a wrapper around CreateNominee
			return validationErr
		}
		metadata.GetPersonalDetails().NomineeDeclarationDetails = req.GetNomineeDeclarationDetails()
		cpd.NomineeDeclarationDetails = req.GetNomineeDeclarationDetails()
	}
	if req.GetSkipSigningAdvisoryAgreement() != commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
		cpd.SkipSigningAdvisoryAgreement = req.GetSkipSigningAdvisoryAgreement()
	}
	if req.GetUserSubmittedPan() != "" && req.GetUserSubmittedDob() != nil {
		s.increasePanDobSubmitAttempt(od)
	}
	if req.GetUserSubmittedPan() != "" {
		cpd.UserSubmittedPan = req.GetUserSubmittedPan()
	}
	if req.GetUserSubmittedDob() != nil {
		cpd.UserSubmittedDob = req.GetUserSubmittedDob()
	}
	if req.GetUserSubmittedName() != "" {
		cpd.UserSubmittedName = req.GetUserSubmittedName()
	}
	if req.GetRiskProfilingConsentId() != "" {
		cpd.RiskProfilingConsentId = req.GetRiskProfilingConsentId()
	}
	if req.GetInvestmentRiskProfileId() != "" {
		cpd.InvestmentRiskProfileId = req.GetInvestmentRiskProfileId()
	}
	return nil
}

func (s *Service) increasePanDobSubmitAttempt(onbDetails *woPb.OnboardingDetails) {
	onbDetails.GetMetadata().PanValidateAttemptsCount += 1
}

// PAN, address and signature are file data and are stored separately with links to the store updated in DB
// OCR is performed on PAN to verify DOB, etc.
func (s *Service) mapPANAddressSign(ctx context.Context, od *woPb.OnboardingDetails, req *woPb.CollectDataFromCustomerRequest) error {
	platform, version := epificontext.AppPlatformAndVersion(ctx)
	// nolint: dupl
	if req.GetPan() != nil {
		panUpdateErr, failureReason := s.updatePANWithOCR(ctx, od, req.GetPan())
		if failureReason != "" {
			goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
				s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), event.NewDataCollectedEvent(od.GetActorId(), time.Now(), od.GetCurrentWealthFlow(), version, platform, event.PanDoc, event.FAIL, failureReason))
			})
		} else {
			goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
				s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), event.NewDataCollectedEvent(od.GetActorId(), time.Now(), od.GetCurrentWealthFlow(), version, platform, event.PanDoc, event.SUCCESS, ""))
			})
		}
		if panUpdateErr != nil {
			return panUpdateErr
		}
	}
	// nolint: dupl
	if req.GetPoa() != nil {
		poaUpdateErr, failureReason := s.updatePOA(ctx, od, req.GetPoa())
		if failureReason != "" {
			goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
				s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), event.NewDataCollectedEvent(od.GetActorId(), time.Now(), od.GetCurrentWealthFlow(), version, platform, event.ProofOfAddressData, event.FAIL, failureReason))
			})
		} else {
			goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
				s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), event.NewDataCollectedEvent(od.GetActorId(), time.Now(), od.GetCurrentWealthFlow(), version, platform, event.ProofOfAddressData, event.SUCCESS, ""))
			})
		}
		if poaUpdateErr != nil {
			return poaUpdateErr
		}
	}
	// nolint: dupl
	if req.GetSignature() != nil {
		signUpdateErr, failureReason := s.updateSignature(ctx, od, req.GetSignature())
		if failureReason != "" {
			goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
				s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), event.NewDataCollectedEvent(od.GetActorId(), time.Now(), od.GetCurrentWealthFlow(), version, platform, event.SignatureData, event.FAIL, failureReason))
			})
		} else {
			goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
				s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), event.NewDataCollectedEvent(od.GetActorId(), time.Now(), od.GetCurrentWealthFlow(), version, platform, event.SignatureData, event.SUCCESS, ""))
			})
		}
		if signUpdateErr != nil {
			return signUpdateErr
		}
	}
	return nil
}

func (s *Service) updatePANWithOCR(ctx context.Context, od *woPb.OnboardingDetails, pan *types.DocumentProof) (error, string) {
	if pan.GetProofType() != types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN {
		failureReason := fmt.Sprintf("please provide pan instead of %v", pan.GetProofType())
		return errors.New(failureReason), failureReason
	}
	if pan.GetPhoto() == nil || len(pan.GetPhoto()) == 0 || pan.GetPhoto()[0].GetImageDataBase64() == "" || pan.GetPhoto()[0].GetImageType() == commontypes.ImageType_IMAGE_TYPE_UNSPECIFIED {
		failureReason := "please provide pan image"
		return errors.New(failureReason), failureReason
	}
	// uploading raw data with base64 data to s3 and getting doc with S3 urls
	panWithS3Paths, err := s.docHelper.UploadDoc(ctx, od.GetActorId(), pan)
	if err != nil {
		failureReason := "failed to get panDocWithS3Paths"
		return errors.Wrap(err, failureReason), failureReason
	}
	// populate PAN using OCR service
	curTimestamp := timestamppb.Now()
	panOcrDoc, pErr := s.ocrHelper.FetchPanWithOcr(ctx, od, panWithS3Paths)
	if od.GetMetadata() == nil {
		od.Metadata = &woPb.OnboardingMetadata{}
	}
	if od.GetMetadata().GetCustomerProvidedData() == nil {
		od.GetMetadata().CustomerProvidedData = &woPb.CustomerProvidedData{}
	}
	if panOcrDoc != nil {
		od.GetMetadata().GetCustomerProvidedData().PanOcrAttempts = append(od.GetMetadata().GetCustomerProvidedData().PanOcrAttempts, &woPb.OcrAttempt{
			OcrDocumentProof: panOcrDoc,
			CreatedAt:        curTimestamp,
		})
	}
	if pErr != nil {
		// TODO(ismail): handle low confidence cases, how should we let user know that they need to upload another picture because of low confidence?
		failureReason := "error while populating pan using ocr"
		return errors.Wrap(pErr, failureReason), failureReason
	}
	if od.GetMetadata().GetPanDetails() == nil {
		od.GetMetadata().PanDetails = &types.DocumentProof{}
	}
	od.GetMetadata().GetPanDetails().S3Paths = panOcrDoc.GetDoc().GetS3Paths()
	od.GetMetadata().GetPanDetails().Dob = panOcrDoc.GetDoc().GetDob()
	od.GetMetadata().GetCustomerProvidedData().Pan = panOcrDoc.GetDoc()
	return nil, ""
}

func (s *Service) updatePOA(ctx context.Context, od *woPb.OnboardingDetails, poa *types.DocumentProof) (error, string) {
	if poa.GetPhoto() == nil || len(poa.GetPhoto()) == 0 || poa.GetPhoto()[0].GetImageDataBase64() == "" || poa.GetPhoto()[0].GetImageType() == commontypes.ImageType_IMAGE_TYPE_UNSPECIFIED {
		failureReason := "please provide proof of address image"
		return errors.New(failureReason), failureReason
	}
	// uploading raw data with base64 data to s3 and getting doc with S3 urls
	poaWithS3Paths, err := s.docHelper.UploadDoc(ctx, od.GetActorId(), poa)
	if err != nil {
		failureReason := "failed to get poaDocWithS3Paths"
		return errors.Wrap(err, failureReason), failureReason
	}
	if od.GetMetadata() == nil {
		od.Metadata = &woPb.OnboardingMetadata{}
	}
	if od.GetMetadata().GetPoaDetails() == nil {
		od.GetMetadata().PoaDetails = &types.DocumentProof{}
	}
	od.GetMetadata().PoaDetails = poaWithS3Paths
	if od.GetMetadata().GetCustomerProvidedData() == nil {
		od.GetMetadata().CustomerProvidedData = &woPb.CustomerProvidedData{}
	}
	od.GetMetadata().GetCustomerProvidedData().Poa = poaWithS3Paths
	return nil, ""
}

func (s *Service) updateSignature(ctx context.Context, od *woPb.OnboardingDetails, sign *commontypes.Image) (error, string) {
	if sign.GetImageType() == commontypes.ImageType_IMAGE_TYPE_UNSPECIFIED || sign.GetImageDataBase64() == "" {
		failureReason := "please provide signature image"
		return errors.New("please provide signature image"), failureReason
	}
	sigDoc := &types.DocumentProof{ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_SIGNATURE, Photo: []*commontypes.Image{sign}}
	// uploading raw data with base64 data to s3 and getting doc with S3 urls
	sigDocWithS3Paths, err := s.docHelper.UploadDoc(ctx, od.GetActorId(), sigDoc)
	if err != nil {
		failureReason := "failed to get sigDocWithS3Paths"
		return errors.Wrap(err, "failed to get sigDocWithS3Paths"), failureReason
	}
	if od.GetMetadata() == nil {
		od.Metadata = &woPb.OnboardingMetadata{}
	}
	if od.GetMetadata().GetPersonalDetails() == nil {
		od.GetMetadata().PersonalDetails = &woPb.PersonalDetails{}
	}
	od.GetMetadata().GetPersonalDetails().Signature = sigDocWithS3Paths
	if od.GetMetadata().GetCustomerProvidedData() == nil {
		od.GetMetadata().CustomerProvidedData = &woPb.CustomerProvidedData{}
	}
	od.GetMetadata().GetCustomerProvidedData().Signature = sigDocWithS3Paths
	return nil, ""
}

// store the updated nominee details in DB and unpause any FIT rules that might have been paused due to nominee not being declared earlier
func (s *Service) storeNomineeAndUnpauseSubscriptions(ctx context.Context, od *woPb.OnboardingDetails) error {
	updOnbErr := s.onboardingDetailsDao.Update(ctx, od, []woPb.OnboardingDetailsFieldMask{woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_METADATA})
	if updOnbErr != nil {
		return errors.Wrap(updOnbErr, "error while updating onboarding details")
	}
	return helper.UnblockRMSRuleExecutions(ctx, od, s.rmsRuleManagerClient,
		rmsPb.SubscriptionExecutionState_BLOCKED_DUE_TO_WEALTH_ACCOUNT_NOMINEE_NOT_DECLARED, rmsPb.SubscriptionStateChangeReason_WEALTH_ACCOUNT_NOMINEE_DECLARED)
}
