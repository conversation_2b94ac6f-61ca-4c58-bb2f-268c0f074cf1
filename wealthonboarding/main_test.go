package wealthonboarding

import (
	"flag"
	"os"
	"testing"

	cmdtypes "github.com/epifi/be-common/pkg/cmd/types"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/test"
)

var (
	idempotentTxnExecutor storagev2.IdempotentTxnExecutor
	conf                  *config.Config
)

func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	var db cmdtypes.EpifiWealthCRDB
	_, conf, _, db, teardown = test.InitTestServer()
	idempotentTxnExecutor = storagev2.NewCRDBIdempotentTxnExecutor(db)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
