package dao

import (
	timestamp "google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
)

func WithOnboardingDetailsUpdatedFrom(fromTs *timestamp.Timestamp) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gormv2.DB) *gormv2.DB {
		return db.Where("onboarding_details.updated_at >= ?", fromTs.AsTime())
	})
}

func WithOnboardingDetailsUpdatedTill(toTs *timestamp.Timestamp) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gormv2.DB) *gormv2.DB {
		return db.Where("onboarding_details.updated_at <= ?", toTs.AsTime())
	})
}

func WithOnboardingStatuses(statuses []woPb.OnboardingStatus) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gormv2.DB) *gormv2.DB {
		return db.Where("onboarding_details.status IN (?)", statuses)
	})
}
