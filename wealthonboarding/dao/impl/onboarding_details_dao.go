//nolint:dupl
package impl

import (
	"context"
	"fmt"
	"time"

	"github.com/google/wire"
	"gorm.io/gorm/clause"

	cmdtypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/dao/model"

	"github.com/pkg/errors"
	gormv2 "gorm.io/gorm"
)

type CrdbOnboardingDetailsDao struct {
	db cmdtypes.EpifiWealthCRDB
}

var _ dao.OnboardingDetailsDao = &CrdbOnboardingDetailsDao{}
var OnboardingDetailsDaoWireSet = wire.NewSet(NewCrdbOnboardingDetailsDao, wire.Bind(new(dao.OnboardingDetailsDao), new(*CrdbOnboardingDetailsDao)))

var onboardingDetailsColumnNames = map[woPb.OnboardingDetailsFieldMask]string{
	woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_METADATA:            "metadata",
	woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_STATUS:              "status",
	woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_CURRENT_STEP:        "current_step",
	woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_COMPLETED_AT:        "completed_at",
	woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_CURRENT_WEALTH_FLOW: "current_wealth_flow",
	woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_AGENT_PROVIDED_DATA: "agent_provided_data",
}

var splittedColumnNames = []string{"personal_details", "nsdl_data", "kra_data", "ckyc_data", "customer_provided_data", "match_data", "digilocker_data"}

func NewCrdbOnboardingDetailsDao(db cmdtypes.EpifiWealthCRDB) *CrdbOnboardingDetailsDao {
	return &CrdbOnboardingDetailsDao{
		db: db,
	}
}

func (c *CrdbOnboardingDetailsDao) Create(ctx context.Context, onboardingDetails *woPb.OnboardingDetails) (*woPb.OnboardingDetails, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbOnboardingDetailsDao", "Create", time.Now())
	onboardingDetailsModel := model.NewOnboardingDetails(onboardingDetails)
	handle := gormctxv2.FromContextOrDefault(ctx, c.db)
	res := handle.Clauses(clause.Returning{}).Create(onboardingDetailsModel)
	if res.Error != nil {
		// if trying to create more than one entry for an actor_id, return duplicate entry error
		if storagev2.IsDuplicateRowError(res.Error) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		return nil, res.Error
	}
	return onboardingDetailsModel.GetProto(), nil
}

func (c *CrdbOnboardingDetailsDao) Update(ctx context.Context, od *woPb.OnboardingDetails, updateMasks []woPb.OnboardingDetailsFieldMask) error {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbOnboardingDetailsDao", "Update", time.Now())
	if od.GetId() == "" {
		return fmt.Errorf("primary identifier can't be empty for an update operation")
	}
	if len(updateMasks) == 0 {
		return fmt.Errorf("update mask can't be empty")
	}

	onboardingDetailsModel := model.NewOnboardingDetails(od)
	updateColumns := c.selectedColumnsForUpdate(updateMasks)
	handle := gormctxv2.FromContextOrDefault(ctx, c.db)
	whereClause := &model.OnboardingDetails{
		Id:             od.GetId(),
		OnboardingType: od.GetOnboardingType(),
	}
	if err := handle.Model(onboardingDetailsModel).Where(whereClause).Select(updateColumns).Updates(onboardingDetailsModel).Error; err != nil {
		return err
	}
	return nil
}

func (c *CrdbOnboardingDetailsDao) GetById(ctx context.Context, id string) (*woPb.OnboardingDetails, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbOnboardingDetailsDao", "GetById", time.Now())
	if id == "" {
		return nil, errors.New("id can't be blank")
	}
	var onboardingDetailsModel model.OnboardingDetails
	d := gormctxv2.FromContextOrDefault(ctx, c.db)
	if err := d.Where("id = ?", id).Take(&onboardingDetailsModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return onboardingDetailsModel.GetProto(), nil
}

func (c *CrdbOnboardingDetailsDao) GetByIds(ctx context.Context, ids []string) ([]*woPb.OnboardingDetails, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbOnboardingDetailsDao", "GetByIds", time.Now())
	if len(ids) == 0 {
		return nil, errors.New("ids cannot be empty")
	}
	var onboardingDetailsModel []model.OnboardingDetails
	d := gormctxv2.FromContextOrDefault(ctx, c.db)
	if err := d.Where("id IN (?)", ids).Find(&onboardingDetailsModel).Error; err != nil {
		return nil, err
	}
	var ret []*woPb.OnboardingDetails
	for _, odModel := range onboardingDetailsModel {
		ret = append(ret, odModel.GetProto())
	}
	return ret, nil
}

func (c *CrdbOnboardingDetailsDao) GetByActorIdAndOnbType(ctx context.Context, actorId string, onbType woPb.OnboardingType) (*woPb.OnboardingDetails, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbOnboardingDetailsDao", "GetByActorIdAndOnbType", time.Now())
	if actorId == "" {
		return nil, errors.New("actorId can't be blank")
	}
	var onboardingDetailsModel model.OnboardingDetails
	d := gormctxv2.FromContextOrDefault(ctx, c.db)
	if err := d.Where("actor_id = ? and onboarding_type = ?", actorId, onbType).Take(&onboardingDetailsModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return onboardingDetailsModel.GetProto(), nil
}

func (c *CrdbOnboardingDetailsDao) BatchGetByActorIdsAndOnbType(ctx context.Context, actorIds []string, onbType woPb.OnboardingType) ([]*woPb.OnboardingDetails, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbOnboardingDetailsDao", "BatchGetByActorIdsAndOnbType", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	if len(actorIds) > 100 {
		return nil, epifierrors.ErrInvalidArgument
	}
	var models []*model.OnboardingDetails
	if err := db.Where("actor_id IN (?) and onboarding_type = ?", actorIds, onbType).Find(&models).Error; err != nil {
		return nil, err
	}
	var details []*woPb.OnboardingDetails
	for _, m := range models {
		details = append(details, m.GetProto())
	}
	return details, nil
}

// GetOrCreate finds the onboarding details if already present, else creates a new record
func (c *CrdbOnboardingDetailsDao) GetOrCreate(ctx context.Context, actorId string, onbType woPb.OnboardingType, newOnbDetails *woPb.OnboardingDetails) (*woPb.OnboardingDetails, bool, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbOnboardingDetailsDao", "GetOrCreate", time.Now())
	od, err := c.GetByActorIdAndOnbType(ctx, actorId, onbType)
	if err != nil {
		if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, false, errors.Wrap(err, "unable to get onboarding details")
		}
		od, err = c.Create(ctx, newOnbDetails)
		if err != nil {
			if !errors.Is(err, epifierrors.ErrDuplicateEntry) {
				return nil, false, errors.Wrap(err, "unable to create onboarding details")
			}
			od, err = c.GetByActorIdAndOnbType(ctx, actorId, onbType)
			if err != nil {
				return nil, false, errors.Wrap(err, "unable to get onboarding details by actor id")
			}
			return od, false, nil
		}
		return od, true, nil
	}
	return od, false, nil
}

func (c *CrdbOnboardingDetailsDao) selectedColumnsForUpdate(updateMasks []woPb.OnboardingDetailsFieldMask) []string {
	var selectColumns []string
	updateMetadata := false
	for _, field := range updateMasks {
		selectColumns = append(selectColumns, onboardingDetailsColumnNames[field])
		if field == woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_METADATA {
			updateMetadata = true
		}
	}
	// we need to add splitted columns in case of updates for Metadata
	if updateMetadata {
		selectColumns = append(selectColumns, splittedColumnNames...)
	}
	return selectColumns
}
