package impl

import (
	"time"

	"golang.org/x/net/context"

	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/dao"
)

type VendorRequestDaoAndProducerImpl struct {
	vendorRequestDaoImpl      *VendorRequestDaoImpl
	vendorRequestProducerImpl *VendorRequestProducerImpl
}

var _ dao.VendorRequestDao = &VendorRequestDaoAndProducerImpl{}

func NewVendorRequestDaoAndProducer(vendorRequestDaoImpl *VendorRequestDaoImpl, vendorRequestProducerImpl *VendorRequestProducerImpl) *VendorRequestDaoAndProducerImpl {
	return &VendorRequestDaoAndProducerImpl{
		vendorRequestDaoImpl:      vendorRequestDaoImpl,
		vendorRequestProducerImpl: vendorRequestProducerImpl,
	}
}

func (v *VendorRequestDaoAndProducerImpl) Create(ctx context.Context, vendorRequest *wealthonboarding.VendorRequest) error {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "VendorRequestDaoAndProducerImpl", "Create", time.Now())
	err := v.vendorRequestDaoImpl.Create(ctx, vendorRequest)
	if err != nil {
		return err
	}
	err = v.vendorRequestProducerImpl.Create(ctx, vendorRequest)
	return err
}

func (v *VendorRequestDaoAndProducerImpl) GetByActorId(ctx context.Context, actorId string) ([]*wealthonboarding.VendorRequest, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "VendorRequestDaoAndProducerImpl", "GetByActorId", time.Now())
	vendorReqProtos, err := v.vendorRequestDaoImpl.GetByActorId(ctx, actorId)
	return vendorReqProtos, err
}
