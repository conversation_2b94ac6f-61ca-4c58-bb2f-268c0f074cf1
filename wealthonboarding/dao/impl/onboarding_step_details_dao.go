//nolint:dupl
package impl

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	gormv2 "gorm.io/gorm"
	"gorm.io/gorm/clause"

	cmdtypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/dao/model"
)

type CrdbOnboardingStepDetailsDao struct {
	db cmdtypes.EpifiWealthCRDB
}

var _ dao.OnboardingStepDetailsDao = &CrdbOnboardingStepDetailsDao{}

var onboardingStepDetailsColumnNames = map[woPb.OnboardingStepDetailsFieldMask]string{
	woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_METADATA:          "metadata",
	woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_STATUS:            "status",
	woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_SUB_STATUS:        "sub_status",
	woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_COMPLETED_AT:      "completed_at",
	woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_STALED_AT:         "staled_at",
	woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_NUM_ATTEMPTS:      "num_attempts",
	woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_EXPECTED_RETRY_AT: "expected_retry_at",
}

func NewCrdbOnboardingStepDetailsDao(db cmdtypes.EpifiWealthCRDB) *CrdbOnboardingStepDetailsDao {
	return &CrdbOnboardingStepDetailsDao{
		db: db,
	}
}

func (c *CrdbOnboardingStepDetailsDao) Create(ctx context.Context, onboardingStepDetails *woPb.OnboardingStepDetails) (*woPb.OnboardingStepDetails, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbOnboardingStepDetailsDao", "Create", time.Now())
	onboardingStepDetailsModel := model.NewOnboardingStepDetails(onboardingStepDetails)
	handle := gormctxv2.FromContextOrDefault(ctx, c.db)
	res := handle.Clauses(clause.Returning{}).Create(onboardingStepDetailsModel)
	if res.Error != nil {
		// if trying to create more than one entry for an onboarding_id and step, return duplicate entry error
		if storagev2.IsDuplicateRowError(res.Error) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		return nil, res.Error
	}
	return onboardingStepDetailsModel.GetProto(), nil
}

func (c *CrdbOnboardingStepDetailsDao) UpdateById(ctx context.Context, onboardingStepDetails *woPb.OnboardingStepDetails, updateMasks []woPb.OnboardingStepDetailsFieldMask) error {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbOnboardingStepDetailsDao", "UpdateById", time.Now())
	if onboardingStepDetails.GetId() == "" {
		return fmt.Errorf("id can't be empty for an update operation")
	}
	if onboardingStepDetails.GetStep() == woPb.OnboardingStep_ONBOARDING_STEP_UNSPECIFIED {
		return fmt.Errorf("step can't be OnboardingStep_ONBOARDING_STEP_UNSPECIFIED for an update operation")
	}
	if len(updateMasks) == 0 {
		return fmt.Errorf("update mask can't be empty")
	}

	onboardingStepDetailsModel := model.NewOnboardingStepDetails(onboardingStepDetails)
	updateColumns := c.selectedColumnsForUpdate(updateMasks)
	handle := gormctxv2.FromContextOrDefault(ctx, c.db)
	res := handle.Model(onboardingStepDetailsModel).Where("id = ?", onboardingStepDetails.GetId()).Select(updateColumns).Updates(onboardingStepDetailsModel)
	if res.Error != nil {
		return res.Error
	}
	return nil
}

func (c *CrdbOnboardingStepDetailsDao) GetById(ctx context.Context, id string) (*woPb.OnboardingStepDetails, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbOnboardingStepDetailsDao", "GetById", time.Now())
	if id == "" {
		return nil, errors.New("id can't be blank")
	}
	var onboardingStepDetailsModel model.OnboardingStepDetails
	d := gormctxv2.FromContextOrDefault(ctx, c.db)
	if err := d.Where("id = ?", id).Take(&onboardingStepDetailsModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return onboardingStepDetailsModel.GetProto(), nil
}

func (c *CrdbOnboardingStepDetailsDao) GetByOnboardingDetailsIdAndStep(ctx context.Context, onboardingDetailsId string, step woPb.OnboardingStep) (*woPb.OnboardingStepDetails, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbOnboardingStepDetailsDao", "GetByOnboardingDetailsIdAndStep", time.Now())
	if onboardingDetailsId == "" {
		return nil, errors.New("onboardingDetailsId can't be blank")
	}
	var onboardingStepDetailsModel model.OnboardingStepDetails
	d := gormctxv2.FromContextOrDefault(ctx, c.db)
	if err := d.Where("onboarding_details_id = ? and step = ? and staled_at is NULL", onboardingDetailsId, step).Order("created_at desc").Take(&onboardingStepDetailsModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return onboardingStepDetailsModel.GetProto(), nil
}

func (c *CrdbOnboardingStepDetailsDao) GetByOnboardingDetailsId(ctx context.Context, onboardingDetailsId string) ([]*woPb.OnboardingStepDetails, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbOnboardingStepDetailsDao", "GetByOnboardingDetailsId", time.Now())
	if onboardingDetailsId == "" {
		return nil, errors.New("onboardingDetailsId can't be blank")
	}
	var onboardingStepDetailsModelList []*model.OnboardingStepDetails
	d := gormctxv2.FromContextOrDefault(ctx, c.db)
	if err := d.Where("onboarding_details_id = ?", onboardingDetailsId).Order("created_at asc").Find(&onboardingStepDetailsModelList).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	if len(onboardingStepDetailsModelList) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	var onboardingStepDetailProto []*woPb.OnboardingStepDetails
	for _, od := range onboardingStepDetailsModelList {
		onboardingStepDetailProto = append(onboardingStepDetailProto, od.GetProto())
	}
	return onboardingStepDetailProto, nil
}

// GetOrCreate finds the onboarding step details if already present, else creates a new record
// in case a new record is created, we return a bool flag set to true
func (c *CrdbOnboardingStepDetailsDao) GetOrCreate(ctx context.Context, onbDetailsId string, step woPb.OnboardingStep, newOnbStepDetails *woPb.OnboardingStepDetails) (*woPb.OnboardingStepDetails, bool, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbOnboardingStepDetailsDao", "GetOrCreate", time.Now())
	stepData, err := c.GetByOnboardingDetailsIdAndStep(ctx, onbDetailsId, step)
	if err != nil {
		if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, false, errors.Wrap(err, "unable to get onboarding step details ")
		}
		stepData, err = c.Create(ctx, newOnbStepDetails)
		if err != nil {
			if !errors.Is(err, epifierrors.ErrDuplicateEntry) {
				return nil, false, errors.Wrap(err, "unable to create onboarding step details")
			}
			stepData, err = c.GetByOnboardingDetailsIdAndStep(ctx, onbDetailsId, step)
			if err != nil {
				return nil, false, errors.Wrap(err, "unable to get onboarding step details")
			}
			return stepData, false, nil
		}
		return stepData, true, nil
	}
	return stepData, false, nil
}

func (c *CrdbOnboardingStepDetailsDao) selectedColumnsForUpdate(updateMasks []woPb.OnboardingStepDetailsFieldMask) []string {
	var selectColumns []string
	for _, field := range updateMasks {
		selectColumns = append(selectColumns, onboardingStepDetailsColumnNames[field])
	}
	return selectColumns
}
