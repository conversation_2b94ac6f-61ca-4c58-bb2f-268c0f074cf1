package impl

import (
	"context"
	"testing"

	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/google/go-cmp/cmp"
	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/testing/protocmp"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/epifierrors"
	testv2 "github.com/epifi/be-common/pkg/test/v2"
)

var (
	sampleEsignTxnDetails = &woPb.ESignDetails{
		Id:           "089fd478-5d31-4a13-91c1-a6e152191b0e",
		TxnId:        "141355",
		DocId:        "261885",
		SignUrl:      "https://uat.manchtech.com/esign.html?authToken=SopmGHwJBAuGu4QeFhVnHyJ3fJtsl1br0XQ&redir=https%3A%2F%2Fuat.manchtech.com%2Fesign-authentication.html",
		TxnState:     woPb.TxnState_TXN_STATE_PENDING,
		StoredS3Path: "e_signed_documents/123456.pdf",
		Vendor:       woPb.Vendor_VENDOR_MANCH,
		Metadata:     nil,
	}
)

func TestCrdbESignDetailsDao_Create(t *testing.T) {
	type args struct {
		ctx          context.Context
		eSignDetails *woPb.ESignDetails
	}
	tests := []struct {
		name    string
		args    args
		want    *woPb.ESignDetails
		wantErr bool
		err     error
	}{
		{
			name: "successfully creation",
			args: args{
				ctx: context.Background(),
				eSignDetails: &woPb.ESignDetails{
					Id:           "",
					TxnId:        "12345670",
					DocId:        "1112",
					SignUrl:      "abcde",
					TxnState:     woPb.TxnState_TXN_STATE_SIGNED,
					StoredS3Path: "e_signed_documents/1234567.pdf",
					Vendor:       woPb.Vendor_VENDOR_MANCH,
					Metadata:     &woPb.Metadata{Identifier: ""},
					SignedAt: &timestamp.Timestamp{
						Seconds: 1655283740,
						Nanos:   0,
					},
				},
			},
			want: &woPb.ESignDetails{
				Id:           "",
				TxnId:        "12345670",
				DocId:        "1112",
				SignUrl:      "abcde",
				TxnState:     woPb.TxnState_TXN_STATE_SIGNED,
				StoredS3Path: "e_signed_documents/1234567.pdf",
				Vendor:       woPb.Vendor_VENDOR_MANCH,
				Metadata:     &woPb.Metadata{Identifier: ""},
				SignedAt: &timestamp.Timestamp{
					Seconds: 1655283740,
					Nanos:   0,
				},
			},
			wantErr: false,
		},
		{
			name: "create duplicate entry for same actor error",
			args: args{
				ctx: context.Background(),
				eSignDetails: &woPb.ESignDetails{
					Id:           "",
					TxnId:        "12345670",
					DocId:        "1111",
					SignUrl:      "abcd",
					TxnState:     woPb.TxnState_TXN_STATE_SIGNED,
					StoredS3Path: "e_signed_documents/123456.pdf",
					Vendor:       woPb.Vendor_VENDOR_MANCH,
					Metadata:     &woPb.Metadata{Identifier: ""},
					SignedAt: &timestamp.Timestamp{
						Seconds: 1655283740,
						Nanos:   0,
					},
				},
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrDuplicateEntry,
		},
	}
	testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CrdbESignDetailsDao{
				db: daoTestSuite.db,
			}
			got, err := c.Create(tt.args.ctx, tt.args.eSignDetails)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&woPb.ESignDetails{}, "created_at", "updated_at", "id"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("Create() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestCrdbESignDetailsDao_Get(t *testing.T) {
	type args struct {
		ctx     context.Context
		eSignId string
	}
	tests := []struct {
		name    string
		args    args
		want    *woPb.ESignDetails
		wantErr bool
		err     error
	}{
		{
			name: "successful get by id",
			args: args{
				ctx:     context.Background(),
				eSignId: "089fd478-5d31-4a13-91c1-a6e152191b0e",
			},
			want:    sampleEsignTxnDetails,
			wantErr: false,
		},
		{
			name: "record not found",
			args: args{
				ctx:     context.Background(),
				eSignId: "129fd478-5d31-4a13-91c1-a6e152191b0e",
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
		{
			name: "eSignId is empty",
			args: args{
				ctx:     context.Background(),
				eSignId: "",
			},
			want:    nil,
			wantErr: true,
			err:     errors.New("eSignId cannot be empty"),
		},
	}
	testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CrdbESignDetailsDao{
				db: daoTestSuite.db,
			}
			got, err := c.Get(tt.args.ctx, tt.args.eSignId)
			if (err != nil) != tt.wantErr {
				t.Errorf("Get() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if err.Error() != tt.err.Error() {
					t.Errorf("Get() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&woPb.ESignDetails{}, "created_at", "updated_at", "id"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("Create() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestCrdbESignDetailsDao_UpdateById(t *testing.T) {
	esignTxnDetailsWithUpdatedStatus := &woPb.ESignDetails{}
	err := copier.Copy(esignTxnDetailsWithUpdatedStatus, sampleEsignTxnDetails)
	if err != nil {
		t.Errorf("error copying esign txn details: %v", err)
		return
	}
	esignTxnDetailsWithUpdatedStatus.TxnState = woPb.TxnState_TXN_STATE_COMPLETED
	esignTxnDetailsWithUpdatedStatus.StoredS3Path = "a-new-s3-path"

	type args struct {
		ctx          context.Context
		eSignDetails *woPb.ESignDetails
		updateMasks  []woPb.ESignDetailsFieldMask
	}
	tests := []struct {
		name    string
		args    args
		want    *woPb.ESignDetails
		wantErr bool
		err     error
	}{
		{
			name: "update successfully",
			args: args{
				ctx: context.Background(),
				eSignDetails: &woPb.ESignDetails{
					Id:           "089fd478-5d31-4a13-91c1-a6e152191b0e",
					TxnState:     woPb.TxnState_TXN_STATE_COMPLETED,
					StoredS3Path: "a-new-s3-path",
					Vendor:       woPb.Vendor_VENDOR_MANCH,
				},
				updateMasks: []woPb.ESignDetailsFieldMask{
					woPb.ESignDetailsFieldMask_E_SIGN_DETAILS_FIELD_MASK_TXN_STATE,
					woPb.ESignDetailsFieldMask_E_SIGN_DETAILS_FIELD_MASK_STORED_S3_PATH,
				},
			},
			want:    esignTxnDetailsWithUpdatedStatus,
			wantErr: false,
		},
		{
			name: "error when id is empty",
			args: args{
				ctx: context.Background(),
				eSignDetails: &woPb.ESignDetails{
					Id:       "",
					TxnState: woPb.TxnState_TXN_STATE_COMPLETED,
				},
				updateMasks: []woPb.ESignDetailsFieldMask{
					woPb.ESignDetailsFieldMask_E_SIGN_DETAILS_FIELD_MASK_TXN_STATE,
				},
			},
			wantErr: true,
			err:     errors.New("primary id cannot be empty"),
		},
		{
			name: "error when e-sign txn state is unspecified",
			args: args{
				ctx: context.Background(),
				eSignDetails: &woPb.ESignDetails{
					Id:       "089fd478-5d31-4a13-91c1-a6e152191b0e",
					TxnState: woPb.TxnState_TXN_STATE_TYPE_UNSPECIFIED,
				},
				updateMasks: []woPb.ESignDetailsFieldMask{
					woPb.ESignDetailsFieldMask_E_SIGN_DETAILS_FIELD_MASK_TXN_STATE,
				},
			},
			wantErr: true,
			err:     errors.New("txn_state cannot be unspecified"),
		},
		{
			name: "error when no update field mask provided",
			args: args{
				ctx: context.Background(),
				eSignDetails: &woPb.ESignDetails{
					Id:       "089fd478-5d31-4a13-91c1-a6e152191b0e",
					TxnState: woPb.TxnState_TXN_STATE_COMPLETED,
				},
				updateMasks: []woPb.ESignDetailsFieldMask{},
			},
			wantErr: true,
			err:     errors.New("update mask can't be empty"),
		},
		{
			name: "error when no such e-sign txn record found",
			args: args{
				ctx: context.Background(),
				eSignDetails: &woPb.ESignDetails{
					Id:       "269fd478-5d31-4a13-91c1-a6e152191b0e",
					TxnState: woPb.TxnState_TXN_STATE_COMPLETED,
				},
				updateMasks: []woPb.ESignDetailsFieldMask{
					woPb.ESignDetailsFieldMask_E_SIGN_DETAILS_FIELD_MASK_TXN_STATE,
				},
			},
			wantErr: true,
			err:     epifierrors.ErrRowNotUpdated,
		},
	}
	testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CrdbESignDetailsDao{
				db: daoTestSuite.db,
			}
			err := c.UpdateById(tt.args.ctx, tt.args.eSignDetails, tt.args.updateMasks)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if err.Error() != tt.err.Error() {
					t.Errorf("UpdateById() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				return
			}
			updatedEsignDetails, err := c.Get(tt.args.ctx, tt.args.eSignDetails.GetId())
			if err != nil {
				t.Errorf("error getting updated e-sign details: %v", err)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&woPb.ESignDetails{}, "created_at", "updated_at", "id"),
			}
			if diff := cmp.Diff(tt.want, updatedEsignDetails, opts...); diff != "" {
				t.Errorf("UpdateById() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}
