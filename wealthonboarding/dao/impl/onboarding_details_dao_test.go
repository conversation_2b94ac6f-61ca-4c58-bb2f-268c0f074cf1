package impl

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"testing"

	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/testing/protocmp"

	types "github.com/epifi/gamma/api/typesv2"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/epifierrors"
	testv2 "github.com/epifi/be-common/pkg/test/v2"

	"google.golang.org/protobuf/types/known/timestamppb"
)

var (
	onboardingDetailsSample = &woPb.OnboardingDetails{
		ActorId: "act-1",
		Metadata: &woPb.OnboardingMetadata{
			PanDetails: &types.DocumentProof{
				Id:        "**********",
				ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
				Photo:     nil,
			},
			PoiDetails: &types.DocumentProof{
				Id:        "DL",
				ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_DRIVING_LICENSE,
				Photo:     nil,
			},
			PoaDetails: &types.DocumentProof{
				Id:        "DL",
				ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_DRIVING_LICENSE,
				Photo:     nil,
			},
			PersonalDetails: &woPb.PersonalDetails{
				Name: &commontypes.Name{
					FirstName:  "FN",
					MiddleName: "MN",
					LastName:   "LN",
					Honorific:  "",
				},
				Gender: types.Gender_MALE,
			},
		},
		Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_PENDING,
		CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
		OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
	}
	onboardingDetailsSample2 = &woPb.OnboardingDetails{
		ActorId: "dummy-actor",
		Metadata: &woPb.OnboardingMetadata{
			PanDetails: &types.DocumentProof{
				Id:        "**********",
				ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
				Photo:     nil,
			},
			PoiDetails: &types.DocumentProof{
				Id:        "DL",
				ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_DRIVING_LICENSE,
				Photo:     nil,
			},
			PoaDetails: &types.DocumentProof{
				Id:        "DL",
				ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_DRIVING_LICENSE,
				Photo:     nil,
			},
			PersonalDetails: &woPb.PersonalDetails{
				Name: &commontypes.Name{
					FirstName:  "FN",
					MiddleName: "MN",
					LastName:   "LN",
					Honorific:  "",
				},
				Gender: types.Gender_MALE,
			},
		},
		Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_PENDING,
		CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
		OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
	}
	onboardingDetailsSample3 = &woPb.OnboardingDetails{
		Id:      "b05815c4-35ba-4e25-902b-f73ebe1ae528",
		ActorId: "dummy-actor",
		Metadata: &woPb.OnboardingMetadata{
			PanDetails: &types.DocumentProof{
				Id:        "**********",
				ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
				Photo:     nil,
			},
			PoiDetails: &types.DocumentProof{
				Id:        "DL",
				ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_DRIVING_LICENSE,
				Photo:     nil,
			},
			PoaDetails: &types.DocumentProof{
				Id:        "DL",
				ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_DRIVING_LICENSE,
				Photo:     nil,
			},
			PersonalDetails: &woPb.PersonalDetails{
				Name: &commontypes.Name{
					FirstName:  "FN",
					MiddleName: "MN",
					LastName:   "LN",
					Honorific:  "",
				},
				Gender: types.Gender_MALE,
			},
		},
		Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED,
		CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
		OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
		CompletedAt:    timestamppb.Now(),
	}
	onboardingDetailsSample4 = &woPb.OnboardingDetails{
		ActorId: "dummy-actor10",
		Metadata: &woPb.OnboardingMetadata{
			PanDetails: &types.DocumentProof{
				ProofType: 0,
				Id:        "Id",
				Photo:     nil,
				Expiry: &date.Date{
					Year:  0,
					Month: 0,
					Day:   0,
				},
				S3Paths: nil,
			},
			PoiDetails: &types.DocumentProof{
				ProofType: 0,
				Id:        "Id",
				Photo:     nil,
				Expiry: &date.Date{
					Year:  0,
					Month: 0,
					Day:   0,
				},
				S3Paths: nil,
			},
			PoaDetails: &types.DocumentProof{
				ProofType: 0,
				Id:        "Id",
				Photo:     nil,
				Expiry: &date.Date{
					Year:  0,
					Month: 0,
					Day:   0,
				},
				S3Paths: nil,
			},
			PersonalDetails: &woPb.PersonalDetails{
				Name: &commontypes.Name{
					FirstName:  "FirstName",
					MiddleName: "MiddleName",
					LastName:   "LastName",
					Honorific:  "Honorific",
				},
				Gender: 0,
				PhoneNumber: &commontypes.PhoneNumber{
					CountryCode:    0,
					NationalNumber: 0,
				},
				Email: "Email",
				Dob: &date.Date{
					Year:  0,
					Month: 0,
					Day:   0,
				},
				Photo: &types.DocumentProof{
					ProofType: 0,
					Id:        "Id",
					Photo:     nil,
					Expiry: &date.Date{
						Year:  0,
						Month: 0,
						Day:   0,
					},
					S3Paths: nil,
				},
				MaritalStatus: 0,
				FatherName: &commontypes.Name{
					FirstName:  "fn",
					MiddleName: "mn",
					LastName:   "ln",
					Honorific:  "h",
				},
				MotherName: &commontypes.Name{
					FirstName:  "fn1",
					MiddleName: "mn1",
					LastName:   "ln1",
					Honorific:  "h1",
				},
				Nominees:                 nil,
				Nationality:              0,
				ResidentialStatus:        0,
				PoliticallyExposedStatus: 0,
				Signature: &types.DocumentProof{
					ProofType: 0,
					Id:        "Id",
					Photo:     nil,
					Expiry: &date.Date{
						Year:  0,
						Month: 0,
						Day:   0,
					},
					S3Paths: nil,
				},
				Occupation: "Occupation",
				IncomeSlab: 0,
			},
			FaceMatchData: &woPb.FaceMatchData{
				FaceMatchAttempts: nil,
			},
			NsdlData: &woPb.NsdlData{
				PanDetails: &woPb.NsdlData_NsdlPanDetails{
					PanStatus:     0,
					AadhaarStatus: 0,
					Name: &commontypes.Name{
						FirstName:  "fn2",
						MiddleName: "mn2",
						LastName:   "ln2",
						Honorific:  "h2",
					},
					LastUpdatedDate: "LastUpdatedDate",
					PanCardName:     "PanCardName",
					ErrMsg:          "ErrMsg",
				},
			},
			KraData: &woPb.KraData{
				StatusData: &woPb.KraStatusData{
					PanStatusDetails: &woPb.KraStatusData_PanStatusDetails{
						PanNumber: "PanNumber",
						Name:      "Name",
						Status:    0,
						LastUpdateDate: &timestamp.Timestamp{
							Seconds: 0,
							Nanos:   0,
						},
						CreatedDate: &timestamp.Timestamp{
							Seconds: 0,
							Nanos:   0,
						},
						ModifiedDate: &timestamp.Timestamp{
							Seconds: 0,
							Nanos:   0,
						},
						AppStatusDelta:        0,
						UpdateStatus:          0,
						HoldDeactivateRemarks: "HoldDeactivateRemarks",
						UpdateRemarks:         "UpdateRemarks",
						KycMode:               0,
						IpvFlag:               0,
						UboFlag:               0,
					},
				},
				DownloadedData: &woPb.KraDownloadedData{
					PanDetails: &woPb.KraDownloadedData_DownloadedPanDetails{
						UpdateFlag: 0,
						Type:       0,
						No:         "No",
						Date:       "Date",
						Exmt:       0,
						ExmtCat:    0,
						Name: &commontypes.Name{
							FirstName:  "FirstName",
							MiddleName: "MiddleName",
							LastName:   "LastName",
							Honorific:  "Honorific",
						},
						IdProof: 0,
						IpvFlag: 0,
						IpvDate: "IpvDate",
						Gen:     0,
						PanNo:   "PanNo",
						PanexNo: "PanexNo",
						PanCopy: 0,
						FName: &commontypes.Name{
							FirstName:  "FirstName",
							MiddleName: "MiddleName",
							LastName:   "LastName",
							Honorific:  "Honorific",
						},
						RegNo:          "RegNo",
						DobDt:          "DobDt",
						DoiDt:          "DoiDt",
						CommenceDt:     "CommenceDt",
						Nationality:    0,
						OthNationality: "OthNationality",
						CompStatus:     0,
						OthCompStatus:  "OthCompStatus",
						ResStatus:      0,
						ResStatusProof: "ResStatusProof",
						UidNo:          "UidNo",
						CorAdd1:        "CorAdd1",
						CorAdd2:        "CorAdd2",
						CorAdd3:        "CorAdd3",
						CorCity:        "CorCity",
						CorPincd:       "CorPincd",
						CorState:       "CorState",
						CorCtry:        "CorCtry",
						OffNo:          "OffNo",
						ResNo:          "ResNo",
						MobNo:          "MobNo",
						FaxNo:          "FaxNo",
						Email:          "Email",
						CorAddProof:    0,
						CorAddRef:      "CorAddRef",
						CorAddDt:       "CorAddDt",
						PerAdd1:        "PerAdd1",
						PerAdd2:        "PerAdd2",
						PerAdd3:        "PerAdd3",
						PerCity:        "PerCity",
						PerPincd:       "PerPincd",
						PerState:       "PerState",
						PerCtry:        "PerCtry",
						PerAddProof:    0,
						PerAddRef:      "PerAddRef",
						PerAddDt:       "PerAddDt",
						Income:         "Income",
						Occ:            0,
						OthOcc:         "OthOcc",
						PolConn:        "PolConn",
						DocProof:       "DocProof",
						InternalRef:    "InternalRef",
						BranchCode:     "BranchCode",
						MarStatus:      0,
						Netwrth:        0,
						NetworthDt:     "NetworthDt",
						IncorpPlc:      "IncorpPlc",
						Otherinfo:      "Otherinfo",
						Filler1:        "Filler1",
						Filler2:        "Filler2",
						Filler3:        "Filler3",
						Status:         "Status",
						Statusdt:       "Statusdt",
						ErrorDesc:      "ErrorDesc",
						DumpType:       "DumpType",
						Dnlddt:         "Dnlddt",
						KraInfo:        0,
						Signature:      "Signature",
						IopFlg:         "IopFlg",
						PosCode:        "PosCode",
						SummRec: &woPb.KraDownloadedData_DownloadedPanDetails_SummaryRec{
							OthkraCode:   "OthkraCode",
							OthkraBatch:  "OthkraBatch",
							ReqDate:      "ReqDate",
							TotalRec:     0,
							ResponseDate: "ResponseDate",
						},
						KycMode:    0,
						AppRemarks: "AppRemarks",
						AddlData:   nil,
						KycStatus:  0,
					},
				},
			},
			CkycData: &woPb.CkycData{
				SearchData: &woPb.CkycSearchData{
					CkycNumber: "CkycNumber",
					Name: &commontypes.Name{
						FirstName:  "FirstName",
						MiddleName: "MiddleName",
						LastName:   "LastName",
						Honorific:  "Honorific",
					},
					FathersName: &commontypes.Name{
						FirstName:  "FirstName",
						MiddleName: "MiddleName",
						LastName:   "LastName",
						Honorific:  "Honorific",
					},
					Age: 0,
					Photo: &types.DocumentProof{
						ProofType: 0,
						Id:        "Id",
						Photo:     nil,
						Expiry: &date.Date{
							Year:  0,
							Month: 0,
							Day:   0,
						},
						S3Paths: nil,
					},
					KycDate: &date.Date{
						Year:  0,
						Month: 0,
						Day:   0,
					},
					UpdatedDate: &date.Date{
						Year:  0,
						Month: 0,
						Day:   0,
					},
				},
				DownloadData: &woPb.CkycDownloadData{
					PersonalDetails: &woPb.CkycPersonalDetails{
						AccountType: 0,
						Name: &commontypes.Name{
							FirstName:  "FirstName",
							MiddleName: "MiddleName",
							LastName:   "LastName",
							Honorific:  "Honorific",
						},
						FathersName: &commontypes.Name{
							FirstName:  "FirstName",
							MiddleName: "MiddleName",
							LastName:   "LastName",
							Honorific:  "Honorific",
						},
						MothersName: &commontypes.Name{
							FirstName:  "FirstName",
							MiddleName: "MiddleName",
							LastName:   "LastName",
							Honorific:  "Honorific",
						},
						Gender: 0,
						Dob: &date.Date{
							Year:  0,
							Month: 0,
							Day:   0,
						},
						Nationality: 0,
						PermanentAddress: &types.PostalAddress{
							Revision:           0,
							RegionCode:         "RegionCode",
							LanguageCode:       "LanguageCode",
							PostalCode:         "PostalCode",
							SortingCode:        "SortingCode",
							AdministrativeArea: "AdministrativeArea",
							Locality:           "Locality",
							Sublocality:        "Sublocality",
							AddressLines:       nil,
							Recipients:         nil,
							Organization:       "Organization",
						},
						CurrentAddress: &types.PostalAddress{
							Revision:           0,
							RegionCode:         "RegionCode",
							LanguageCode:       "LanguageCode",
							PostalCode:         "PostalCode",
							SortingCode:        "SortingCode",
							AdministrativeArea: "AdministrativeArea",
							Locality:           "Locality",
							Sublocality:        "Sublocality",
							AddressLines:       nil,
							Recipients:         nil,
							Organization:       "Organization",
						},
						TelOffice: &commontypes.Landline{
							StdCode: "StdCode",
							Number:  "Number",
						},
						TelResidential: &commontypes.Landline{
							StdCode: "StdCode",
							Number:  "Number",
						},
						MobileNumber: &commontypes.PhoneNumber{
							CountryCode:    0,
							NationalNumber: 0,
						},
						EmailId:        "EmailId",
						DocumentsList:  nil,
						Pan:            "Pan",
						ProofOfAddress: 0,
						Remarks:        "Remarks",
						MaidenName: &commontypes.Name{
							FirstName:  "FirstName",
							MiddleName: "MiddleName",
							LastName:   "LastName",
							Honorific:  "Honorific",
						},
						CkycNo: "CkycNo",
					},
					IdentityDetails: nil,
				},
			},
			NameMatchInfo: &woPb.NameMatchInfo{
				WeightedSumScore: 0,
				Scores:           nil,
			},
			KraDocketInfo: &woPb.DocketInfo{
				UnsignedDocUrl: "UnsignedDocUrl",
				SignedDocUrl:   "SignedDocUrl",
				EsignInfos:     nil,
				DocAsProof: &types.DocumentProof{
					ProofType: 0,
					Id:        "Id",
					Photo:     nil,
					Expiry: &date.Date{
						Year:  0,
						Month: 0,
						Day:   0,
					},
					S3Paths: nil,
				},
			},
			AgreementDocketInfo: &woPb.DocketInfo{
				UnsignedDocUrl: "UnsignedDocUrl",
				SignedDocUrl:   "SignedDocUrl",
				EsignInfos:     nil,
				DocAsProof: &types.DocumentProof{
					ProofType: 0,
					Id:        "Id",
					Photo:     nil,
					Expiry: &date.Date{
						Year:  0,
						Month: 0,
						Day:   0,
					},
					S3Paths: nil,
				},
			},
			CustomerProvidedData: &woPb.CustomerProvidedData{
				Gender:        0,
				MaritalStatus: 0,
				Signature: &types.DocumentProof{
					ProofType: 0,
					Id:        "Id",
					Photo:     nil,
					Expiry: &date.Date{
						Year:  0,
						Month: 0,
						Day:   0,
					},
					S3Paths: nil,
				},
				Pan: &types.DocumentProof{
					ProofType: 0,
					Id:        "Id",
					Photo:     nil,
					Expiry: &date.Date{
						Year:  0,
						Month: 0,
						Day:   0,
					},
					S3Paths: nil,
				},
				Poa: &types.DocumentProof{
					ProofType: 0,
					Id:        "Id",
					Photo:     nil,
					Expiry: &date.Date{
						Year:  0,
						Month: 0,
						Day:   0,
					},
					S3Paths: nil,
				},
			},
			LivenessData: &woPb.LivenessData{
				LivenessAttempts: nil,
			},
			IsFreshKra:        false,
			CustomerIpAddress: "CustomerIpAddress",
			EmploymentData: &woPb.EmploymentData{
				EmploymentType:        0,
				EnteredEmploymentText: "EnteredEmploymentText",
			},
			UploadKraDocData: &woPb.UploadKraDocData{
				UploadAttempts: []*woPb.UploadKraDocData_UploadAttempt{
					{
						AttemptId: "12312312",
						Status:    1,
					},
				},
			},
			DownloadedKraDocData: &woPb.DownloadedKraDocData{
				DownloadAttempts: []*woPb.DownloadedKraDocData_DownloadAttempt{
					{
						AttemptId:    "AttemptId",
						Status:       2,
						FolderDate:   "FolderDate",
						FolderTime:   "FolderTime",
						TotalRecords: 4,
						DocAsProof: &types.DocumentProof{
							ProofType: 3,
							Id:        "id",
							Photo:     nil,
							Expiry: &date.Date{
								Year:  1235,
								Month: 1,
								Day:   12,
							},
							S3Paths: []string{"s3path"},
						},
					},
				},
			},
			DigilockerData: &woPb.DigilockerData{
				PersonalData: &woPb.DigilockerPersonalData{
					Name: &commontypes.Name{
						FirstName:  "",
						MiddleName: "",
						LastName:   "",
						Honorific:  "",
					},
					Dob: &types.Date{
						Year:  0,
						Month: 0,
						Day:   0,
					},
					Gender:   0,
					EAadhaar: 0,
					Mobile: &commontypes.PhoneNumber{
						CountryCode:    0,
						NationalNumber: 0,
					},
				},
				Pan: &commontypes.Image{
					ImageType: commontypes.ImageType_PDF,
				},
				DrivingLicense: &commontypes.Image{
					ImageType: commontypes.ImageType_PDF,
				},
				DigilockerAadhaarData: &woPb.DigilockerAadhaarData{
					UserImage: &commontypes.Image{
						ImageType: commontypes.ImageType_PDF,
					},
					Name: &commontypes.Name{
						FirstName:  "FirstName",
						MiddleName: "MiddleName",
						LastName:   "LastName",
						Honorific:  "Honorific",
					},
					Dob: &types.Date{
						Year:  2000,
						Month: 10,
						Day:   10,
					},
					Gender: types.Gender_MALE,
					Address: &types.PostalAddress{
						Revision:           1,
						RegionCode:         "RegionCode",
						PostalCode:         "PostalCode",
						AdministrativeArea: "AdministrativeArea",
						Locality:           "loc",
						Sublocality:        "sl",
						AddressLines:       []string{"s2", "s2"},
						Organization:       "Organization",
					},
					Ttl: &timestamp.Timestamp{
						Seconds: 128379128312,
						Nanos:   0,
					},
					MaskedAadhaarNumber: "1234",
					Ts: &timestamp.Timestamp{
						Seconds: 120378190238,
						Nanos:   0,
					},
				},
			},
		},
		Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED,
		CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
		OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
		CompletedAt:    timestamppb.Now(),
	}
	onboardingDetailsSample5 = &woPb.OnboardingDetails{
		ActorId: "dummy-actor11",
		Metadata: &woPb.OnboardingMetadata{
			PanDetails: &types.DocumentProof{
				ProofType: 0,
				Id:        "Id",
				Photo:     nil,
				Expiry: &date.Date{
					Year:  0,
					Month: 0,
					Day:   0,
				},
				S3Paths: nil,
			},
			PoiDetails: &types.DocumentProof{
				ProofType: 0,
				Id:        "Id",
				Photo:     nil,
				Expiry: &date.Date{
					Year:  0,
					Month: 0,
					Day:   0,
				},
				S3Paths: nil,
			},
			PoaDetails: &types.DocumentProof{
				ProofType: 0,
				Id:        "Id",
				Photo:     nil,
				Expiry: &date.Date{
					Year:  0,
					Month: 0,
					Day:   0,
				},
				S3Paths: nil,
			},
			PersonalDetails: &woPb.PersonalDetails{
				Name: &commontypes.Name{
					FirstName:  "FirstName",
					MiddleName: "MiddleName",
					LastName:   "LastName",
					Honorific:  "Honorific",
				},
				Gender: 0,
				PhoneNumber: &commontypes.PhoneNumber{
					CountryCode:    0,
					NationalNumber: 0,
				},
				Email: "Email",
				Dob: &date.Date{
					Year:  0,
					Month: 0,
					Day:   0,
				},
				Photo: &types.DocumentProof{
					ProofType: 0,
					Id:        "Id",
					Photo:     nil,
					Expiry: &date.Date{
						Year:  0,
						Month: 0,
						Day:   0,
					},
					S3Paths: nil,
				},
				MaritalStatus: 0,
				FatherName: &commontypes.Name{
					FirstName:  "fn",
					MiddleName: "mn",
					LastName:   "ln",
					Honorific:  "h",
				},
				MotherName: &commontypes.Name{
					FirstName:  "fn1",
					MiddleName: "mn1",
					LastName:   "ln1",
					Honorific:  "h1",
				},
				Nominees:                 nil,
				Nationality:              0,
				ResidentialStatus:        0,
				PoliticallyExposedStatus: 0,
				Signature: &types.DocumentProof{
					ProofType: 0,
					Id:        "Id",
					Photo:     nil,
					Expiry: &date.Date{
						Year:  0,
						Month: 0,
						Day:   0,
					},
					S3Paths: nil,
				},
				Occupation: "Occupation",
				IncomeSlab: 0,
			},
			FaceMatchData: &woPb.FaceMatchData{
				FaceMatchAttempts: nil,
			},
			NsdlData: &woPb.NsdlData{
				PanDetails: &woPb.NsdlData_NsdlPanDetails{
					PanStatus:     0,
					AadhaarStatus: 0,
					Name: &commontypes.Name{
						FirstName:  "fn2",
						MiddleName: "mn2",
						LastName:   "ln2",
						Honorific:  "h2",
					},
					LastUpdatedDate: "LastUpdatedDate",
					PanCardName:     "PanCardName",
					ErrMsg:          "ErrMsg",
				},
			},
			KraData: &woPb.KraData{
				StatusData: &woPb.KraStatusData{
					PanStatusDetails: &woPb.KraStatusData_PanStatusDetails{
						PanNumber: "PanNumber",
						Name:      "Name",
						Status:    0,
						LastUpdateDate: &timestamp.Timestamp{
							Seconds: 0,
							Nanos:   0,
						},
						CreatedDate: &timestamp.Timestamp{
							Seconds: 0,
							Nanos:   0,
						},
						ModifiedDate: &timestamp.Timestamp{
							Seconds: 0,
							Nanos:   0,
						},
						AppStatusDelta:        0,
						UpdateStatus:          0,
						HoldDeactivateRemarks: "HoldDeactivateRemarks",
						UpdateRemarks:         "UpdateRemarks",
						KycMode:               0,
						IpvFlag:               0,
						UboFlag:               0,
					},
				},
				DownloadedData: &woPb.KraDownloadedData{
					PanDetails: &woPb.KraDownloadedData_DownloadedPanDetails{
						UpdateFlag: 0,
						Type:       0,
						No:         "No",
						Date:       "Date",
						Exmt:       0,
						ExmtCat:    0,
						Name: &commontypes.Name{
							FirstName:  "FirstName",
							MiddleName: "MiddleName",
							LastName:   "LastName",
							Honorific:  "Honorific",
						},
						IdProof: 0,
						IpvFlag: 0,
						IpvDate: "IpvDate",
						Gen:     0,
						PanNo:   "PanNo",
						PanexNo: "PanexNo",
						PanCopy: 0,
						FName: &commontypes.Name{
							FirstName:  "FirstName",
							MiddleName: "MiddleName",
							LastName:   "LastName",
							Honorific:  "Honorific",
						},
						RegNo:          "RegNo",
						DobDt:          "DobDt",
						DoiDt:          "DoiDt",
						CommenceDt:     "CommenceDt",
						Nationality:    0,
						OthNationality: "OthNationality",
						CompStatus:     0,
						OthCompStatus:  "OthCompStatus",
						ResStatus:      0,
						ResStatusProof: "ResStatusProof",
						UidNo:          "UidNo",
						CorAdd1:        "CorAdd1",
						CorAdd2:        "CorAdd2",
						CorAdd3:        "CorAdd3",
						CorCity:        "CorCity",
						CorPincd:       "CorPincd",
						CorState:       "CorState",
						CorCtry:        "CorCtry",
						OffNo:          "OffNo",
						ResNo:          "ResNo",
						MobNo:          "MobNo",
						FaxNo:          "FaxNo",
						Email:          "Email",
						CorAddProof:    0,
						CorAddRef:      "CorAddRef",
						CorAddDt:       "CorAddDt",
						PerAdd1:        "PerAdd1",
						PerAdd2:        "PerAdd2",
						PerAdd3:        "PerAdd3",
						PerCity:        "PerCity",
						PerPincd:       "PerPincd",
						PerState:       "PerState",
						PerCtry:        "PerCtry",
						PerAddProof:    0,
						PerAddRef:      "PerAddRef",
						PerAddDt:       "PerAddDt",
						Income:         "Income",
						Occ:            0,
						OthOcc:         "OthOcc",
						PolConn:        "PolConn",
						DocProof:       "DocProof",
						InternalRef:    "InternalRef",
						BranchCode:     "BranchCode",
						MarStatus:      0,
						Netwrth:        0,
						NetworthDt:     "NetworthDt",
						IncorpPlc:      "IncorpPlc",
						Otherinfo:      "Otherinfo",
						Filler1:        "Filler1",
						Filler2:        "Filler2",
						Filler3:        "Filler3",
						Status:         "Status",
						Statusdt:       "Statusdt",
						ErrorDesc:      "ErrorDesc",
						DumpType:       "DumpType",
						Dnlddt:         "Dnlddt",
						KraInfo:        0,
						Signature:      "Signature",
						IopFlg:         "IopFlg",
						PosCode:        "PosCode",
						SummRec: &woPb.KraDownloadedData_DownloadedPanDetails_SummaryRec{
							OthkraCode:   "OthkraCode",
							OthkraBatch:  "OthkraBatch",
							ReqDate:      "ReqDate",
							TotalRec:     0,
							ResponseDate: "ResponseDate",
						},
						KycMode:    0,
						AppRemarks: "AppRemarks",
						AddlData:   nil,
						KycStatus:  0,
					},
				},
			},
			CkycData: &woPb.CkycData{
				SearchData: &woPb.CkycSearchData{
					CkycNumber: "CkycNumber",
					Name: &commontypes.Name{
						FirstName:  "FirstName",
						MiddleName: "MiddleName",
						LastName:   "LastName",
						Honorific:  "Honorific",
					},
					FathersName: &commontypes.Name{
						FirstName:  "FirstName",
						MiddleName: "MiddleName",
						LastName:   "LastName",
						Honorific:  "Honorific",
					},
					Age: 0,
					Photo: &types.DocumentProof{
						ProofType: 0,
						Id:        "Id",
						Photo:     nil,
						Expiry: &date.Date{
							Year:  0,
							Month: 0,
							Day:   0,
						},
						S3Paths: nil,
					},
					KycDate: &date.Date{
						Year:  0,
						Month: 0,
						Day:   0,
					},
					UpdatedDate: &date.Date{
						Year:  0,
						Month: 0,
						Day:   0,
					},
				},
				DownloadData: &woPb.CkycDownloadData{
					PersonalDetails: &woPb.CkycPersonalDetails{
						AccountType: 0,
						Name: &commontypes.Name{
							FirstName:  "FirstName",
							MiddleName: "MiddleName",
							LastName:   "LastName",
							Honorific:  "Honorific",
						},
						FathersName: &commontypes.Name{
							FirstName:  "FirstName",
							MiddleName: "MiddleName",
							LastName:   "LastName",
							Honorific:  "Honorific",
						},
						MothersName: &commontypes.Name{
							FirstName:  "FirstName",
							MiddleName: "MiddleName",
							LastName:   "LastName",
							Honorific:  "Honorific",
						},
						Gender: 0,
						Dob: &date.Date{
							Year:  0,
							Month: 0,
							Day:   0,
						},
						Nationality: 0,
						PermanentAddress: &types.PostalAddress{
							Revision:           0,
							RegionCode:         "RegionCode",
							LanguageCode:       "LanguageCode",
							PostalCode:         "PostalCode",
							SortingCode:        "SortingCode",
							AdministrativeArea: "AdministrativeArea",
							Locality:           "Locality",
							Sublocality:        "Sublocality",
							AddressLines:       nil,
							Recipients:         nil,
							Organization:       "Organization",
						},
						CurrentAddress: &types.PostalAddress{
							Revision:           0,
							RegionCode:         "RegionCode",
							LanguageCode:       "LanguageCode",
							PostalCode:         "PostalCode",
							SortingCode:        "SortingCode",
							AdministrativeArea: "AdministrativeArea",
							Locality:           "Locality",
							Sublocality:        "Sublocality",
							AddressLines:       nil,
							Recipients:         nil,
							Organization:       "Organization",
						},
						TelOffice: &commontypes.Landline{
							StdCode: "StdCode",
							Number:  "Number",
						},
						TelResidential: &commontypes.Landline{
							StdCode: "StdCode",
							Number:  "Number",
						},
						MobileNumber: &commontypes.PhoneNumber{
							CountryCode:    0,
							NationalNumber: 0,
						},
						EmailId:        "EmailId",
						DocumentsList:  nil,
						Pan:            "Pan",
						ProofOfAddress: 0,
						Remarks:        "Remarks",
						MaidenName: &commontypes.Name{
							FirstName:  "FirstName",
							MiddleName: "MiddleName",
							LastName:   "LastName",
							Honorific:  "Honorific",
						},
						CkycNo: "CkycNo",
					},
					IdentityDetails: nil,
				},
			},
			NameMatchInfo: &woPb.NameMatchInfo{
				WeightedSumScore: 0,
				Scores:           nil,
			},
			KraDocketInfo: &woPb.DocketInfo{
				UnsignedDocUrl: "UnsignedDocUrl",
				SignedDocUrl:   "SignedDocUrl",
				EsignInfos:     nil,
				DocAsProof: &types.DocumentProof{
					ProofType: 0,
					Id:        "Id",
					Photo:     nil,
					Expiry: &date.Date{
						Year:  0,
						Month: 0,
						Day:   0,
					},
					S3Paths: nil,
				},
			},
			AgreementDocketInfo: &woPb.DocketInfo{
				UnsignedDocUrl: "UnsignedDocUrl",
				SignedDocUrl:   "SignedDocUrl",
				EsignInfos:     nil,
				DocAsProof: &types.DocumentProof{
					ProofType: 0,
					Id:        "Id",
					Photo:     nil,
					Expiry: &date.Date{
						Year:  0,
						Month: 0,
						Day:   0,
					},
					S3Paths: nil,
				},
			},
			CustomerProvidedData: &woPb.CustomerProvidedData{
				Gender:        0,
				MaritalStatus: 0,
				Signature: &types.DocumentProof{
					ProofType: 0,
					Id:        "Id",
					Photo:     nil,
					Expiry: &date.Date{
						Year:  0,
						Month: 0,
						Day:   0,
					},
					S3Paths: nil,
				},
				Pan: &types.DocumentProof{
					ProofType: 0,
					Id:        "Id",
					Photo:     nil,
					Expiry: &date.Date{
						Year:  0,
						Month: 0,
						Day:   0,
					},
					S3Paths: nil,
				},
				Poa: &types.DocumentProof{
					ProofType: 0,
					Id:        "Id",
					Photo:     nil,
					Expiry: &date.Date{
						Year:  0,
						Month: 0,
						Day:   0,
					},
					S3Paths: nil,
				},
			},
			LivenessData: &woPb.LivenessData{
				LivenessAttempts: nil,
			},
			IsFreshKra:        false,
			CustomerIpAddress: "CustomerIpAddress",
			EmploymentData: &woPb.EmploymentData{
				EmploymentType:        0,
				EnteredEmploymentText: "EnteredEmploymentText",
			},
			UploadKraDocData: &woPb.UploadKraDocData{
				UploadAttempts: []*woPb.UploadKraDocData_UploadAttempt{
					{
						AttemptId: "12312312",
						Status:    1,
					},
				},
			},
			DownloadedKraDocData: &woPb.DownloadedKraDocData{
				DownloadAttempts: []*woPb.DownloadedKraDocData_DownloadAttempt{
					{
						AttemptId:    "AttemptId",
						Status:       2,
						FolderDate:   "FolderDate",
						FolderTime:   "FolderTime",
						TotalRecords: 4,
						DocAsProof: &types.DocumentProof{
							ProofType: 3,
							Id:        "id",
							Photo:     nil,
							Expiry: &date.Date{
								Year:  1235,
								Month: 1,
								Day:   12,
							},
							S3Paths: []string{"s3path"},
						},
					},
				},
			},
		},
		Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED,
		CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
		OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
	}

	onboardingDetailsSample6 = &woPb.OnboardingDetails{
		Id:             "3a9e4ffe-50e5-4ed6-9e3b-0f1693b41188",
		ActorId:        "actor-b1",
		OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
		Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_PENDING,
		CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
		Metadata:       &woPb.OnboardingMetadata{KraData: &woPb.KraData{}},
	}
)

func TestCrdbOnboardingDetailsDao_Create(t *testing.T) {
	type args struct {
		ctx               context.Context
		onboardingDetails *woPb.OnboardingDetails
	}
	tests := []struct {
		name    string
		args    args
		want    *woPb.OnboardingDetails
		wantErr bool
		err     error
	}{
		{
			name: "successful creation flow",
			args: args{
				ctx:               context.Background(),
				onboardingDetails: onboardingDetailsSample,
			},
			want:    onboardingDetailsSample,
			wantErr: false,
		},
		{
			name: "duplicate actor create",
			args: args{
				ctx:               context.Background(),
				onboardingDetails: onboardingDetailsSample2,
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrDuplicateEntry,
		},
		{
			name: "successful actor create with exhaustive meta data old",
			args: args{
				ctx:               context.Background(),
				onboardingDetails: onboardingDetailsSample4,
			},
			want:    onboardingDetailsSample4,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			c := &CrdbOnboardingDetailsDao{
				db: daoTestSuite.db,
			}
			got, err := c.Create(tt.args.ctx, tt.args.onboardingDetails)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&woPb.OnboardingDetails{}, "created_at", "updated_at", "completed_at", "id"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("Create() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestCrdbOnboardingDetailsDao_GetByActorIdAndOnbType(t *testing.T) {
	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name    string
		args    args
		want    *woPb.OnboardingDetails
		wantErr bool
		err     error
	}{
		{
			name: "successfully get by actor id",
			args: args{
				ctx:     context.Background(),
				actorId: "dummy-actor11",
			},
			want:    onboardingDetailsSample5,
			wantErr: false,
		},
		{
			name: "record not found",
			args: args{
				ctx:     context.Background(),
				actorId: "dummy-actor1",
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			c := &CrdbOnboardingDetailsDao{
				db: daoTestSuite.db,
			}
			got, err := c.GetByActorIdAndOnbType(tt.args.ctx, tt.args.actorId, woPb.OnboardingType_ONBOARDING_TYPE_WEALTH)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorIdAndOnbType() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetByActorIdAndOnbType() error = %v, wantErr %v", err, tt.wantErr)
				}
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&woPb.OnboardingDetails{}, "created_at", "updated_at", "completed_at", "id"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("GetByActorIdAndOnbType() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestCrdbOnboardingDetailsDao_GetById(t *testing.T) {
	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		args    args
		want    *woPb.OnboardingDetails
		wantErr bool
		err     error
	}{
		{
			name: "successfully get by id",
			args: args{
				ctx: context.Background(),
				id:  "35541389-bb07-443a-a5cf-2b4d150f3e1f",
			},
			want:    onboardingDetailsSample5,
			wantErr: false,
		},
		{
			name: "record not found",
			args: args{
				ctx: context.Background(),
				id:  "b05815c4-35ba-4e25-902b-f73ebe1ae527",
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
		{
			name: "adding null defaults is backward compatible",
			args: args{
				ctx: context.Background(),
				id:  "3a9e4ffe-50e5-4ed6-9e3b-0f1693b41188",
			},
			want: onboardingDetailsSample6,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			c := &CrdbOnboardingDetailsDao{
				db: daoTestSuite.db,
			}
			got, err := c.GetById(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				}
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&woPb.OnboardingDetails{}, "created_at", "updated_at", "completed_at", "id"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("GetById() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestCrdbOnboardingDetailsDao_Update(t *testing.T) {
	type args struct {
		ctx         context.Context
		od          *woPb.OnboardingDetails
		updateMasks []woPb.OnboardingDetailsFieldMask
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "successful fields update",
			args: args{
				ctx: context.Background(),
				od:  onboardingDetailsSample3,
				updateMasks: []woPb.OnboardingDetailsFieldMask{
					woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_METADATA,
					woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_STATUS,
					woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_CURRENT_STEP,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			c := &CrdbOnboardingDetailsDao{
				db: daoTestSuite.db,
			}
			if err := c.Update(tt.args.ctx, tt.args.od, tt.args.updateMasks); (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCrdbOnboardingDetailsDao_GetOrCreate(t *testing.T) {
	type args struct {
		ctx           context.Context
		actorId       string
		onbType       woPb.OnboardingType
		newOnbDetails *woPb.OnboardingDetails
	}
	tests := []struct {
		name           string
		args           args
		wantOnbDetails *woPb.OnboardingDetails
		wantIsCreated  bool
		wantErr        bool
	}{
		{
			name: "create if not present",
			args: args{
				ctx:           context.Background(),
				actorId:       onboardingDetailsSample.GetActorId(),
				onbType:       onboardingDetailsSample.GetOnboardingType(),
				newOnbDetails: onboardingDetailsSample,
			},
			wantOnbDetails: onboardingDetailsSample,
			wantIsCreated:  true,
		},
		{
			name: "get if present",
			args: args{
				ctx:           context.Background(),
				actorId:       onboardingDetailsSample.GetActorId(),
				onbType:       onboardingDetailsSample.GetOnboardingType(),
				newOnbDetails: onboardingDetailsSample,
			},
			wantOnbDetails: onboardingDetailsSample,
			wantIsCreated:  false,
		},
	}
	testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CrdbOnboardingDetailsDao{
				db: daoTestSuite.db,
			}
			got, got1, err := c.GetOrCreate(tt.args.ctx, tt.args.actorId, tt.args.onbType, tt.args.newOnbDetails)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOrCreate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&woPb.OnboardingDetails{}, "created_at", "updated_at", "completed_at", "id"),
			}
			if diff := cmp.Diff(tt.wantOnbDetails, got, opts...); diff != "" {
				t.Errorf("GetOrCreate() mismatch (-want +got):\n%s", diff)
			}
			if got1 != tt.wantIsCreated {
				t.Errorf("GetOrCreate() got1 = %v, want %v", got1, tt.wantIsCreated)
			}
		})
	}
}
