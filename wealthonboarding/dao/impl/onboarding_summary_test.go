package impl

import (
	"context"
	"reflect"
	"testing"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	testv2 "github.com/epifi/be-common/pkg/test/v2"
	"github.com/epifi/gamma/wealthonboarding/dao"
)

func TestOnboardingSummaryCrdb_GetByFilters(t *testing.T) {
	ctx := context.Background()
	testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
	onbDetails, cErr := daoTestSuite.onbDetailsDao.Create(ctx, &woPb.OnboardingDetails{
		ActorId:     "test-actor-id",
		CurrentStep: woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
		Status:      woPb.OnboardingStatus_ONBOARDING_STATUS_MANUAL_INTERVENTION_NEEDED,
	})
	if cErr != nil {
		t.Errorf("error creating onb details: %v", cErr)
		return
	}
	_, cErr = daoTestSuite.onbStepDetailsDao.Create(ctx, &woPb.OnboardingStepDetails{
		OnboardingDetailsId: onbDetails.GetId(),
		Step:                woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
		Status:              woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED,
		SubStatus:           woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_RETRY_EXHAUSTED,
	})
	if cErr != nil {
		t.Errorf("error creating onb step details: %v", cErr)
		return
	}

	type args struct {
		ctx       context.Context
		pageToken *pagination.PageToken
		filters   []storagev2.FilterOption
	}
	tests := []struct {
		name           string
		args           args
		wantSummaries  []*woPb.OnboardingSummary
		wantPageCtxRes *rpc.PageContextResponse
		wantErr        bool
	}{
		{
			name: "get existing summaries",
			args: args{
				ctx: ctx,
				filters: []storagev2.FilterOption{
					dao.WithOnboardingStatuses([]woPb.OnboardingStatus{woPb.OnboardingStatus_ONBOARDING_STATUS_MANUAL_INTERVENTION_NEEDED}),
				},
			},
			wantSummaries: []*woPb.OnboardingSummary{
				{
					OnboardingDetails: &woPb.OnboardingDetails{
						ActorId:     "test-actor-id",
						Id:          onbDetails.GetId(),
						CurrentStep: woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
						Status:      woPb.OnboardingStatus_ONBOARDING_STATUS_MANUAL_INTERVENTION_NEEDED,
					},
					CurrentStepDetails: &woPb.OnboardingStepDetails{
						Status:    woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED,
						SubStatus: woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_RETRY_EXHAUSTED,
					},
				},
			},
			wantPageCtxRes: &rpc.PageContextResponse{HasAfter: false},
			wantErr:        false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &OnboardingSummaryCrdb{
				db: daoTestSuite.db,
			}
			gotSummaries, gotPageCtxRes, err := d.GetByFilters(tt.args.ctx, nil, tt.args.filters...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByFilters() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if len(gotSummaries) != len(tt.wantSummaries) {
				t.Errorf("GetByFilters() len = %v, wantlen %v", len(gotSummaries), len(tt.wantSummaries))
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&woPb.OnboardingDetails{}, "created_at", "updated_at"),
				protocmp.IgnoreFields(&woPb.OnboardingStepDetails{}, "id", "created_at"),
			}
			for i := range gotSummaries {
				if diff := cmp.Diff(tt.wantSummaries[i], gotSummaries[i], opts...); diff != "" {
					t.Errorf("GetByFilters() mismatch (-want +got):\n%s", diff)
					return
				}
			}
			if !reflect.DeepEqual(gotPageCtxRes, tt.wantPageCtxRes) {
				t.Errorf("GetByFilters() gotPageCtxRes = %v, want %v", gotPageCtxRes, tt.wantPageCtxRes)
				return
			}
		})
	}
}
