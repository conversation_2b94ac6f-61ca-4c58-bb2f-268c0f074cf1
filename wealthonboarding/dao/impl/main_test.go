package impl

import (
	"flag"
	"os"
	"sync"
	"testing"

	cmdtypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/test"
)

type DaoTestSuite struct {
	db                cmdtypes.EpifiWealthCRDB
	conf              *config.Config
	dbName            string
	onbDetailsDao     dao.OnboardingDetailsDao
	onbStepDetailsDao dao.OnboardingStepDetailsDao
}

var (
	AffectedTestTables   = []string{"onboarding_details,onboarding_step_details", "manual_reviews", "ckyc_data", "users"}
	daoTestSuite         DaoTestSuite
	initialiseSameDbOnce sync.Once
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()

	var teardown func()
	dbName, conf, _, db, teardown := test.InitTestServer()

	daoTestSuite = DaoTestSuite{
		db:                db,
		conf:              conf,
		dbName:            dbName,
		onbDetailsDao:     NewCrdbOnboardingDetailsDao(db),
		onbStepDetailsDao: NewCrdbOnboardingStepDetailsDao(db),
	}

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
