package impl

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"
	"testing"

	"github.com/pkg/errors"
	"golang.org/x/net/context"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woCkycPb "github.com/epifi/gamma/api/wealthonboarding/ckyc"
	"github.com/epifi/be-common/pkg/epifierrors"
	testv2 "github.com/epifi/be-common/pkg/test/v2"
)

var ckycDataSample = &woCkycPb.CkycData{
	CkycNo:      "random_ckyc_no_1",
	AccountType: woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_NORMAL,
	CkycPayload: &woCkycPb.CkycPayload{CkycData: &woCkycPb.CkycPayload_CkycSearchData{
		CkycSearchData: &woPb.CkycSearchData{
			CkycNumber: "random_ckyc_no_1",
			Name: &commontypes.Name{
				FirstName:  "first_name",
				MiddleName: "middle_name",
				LastName:   "last_name",
			},
			Age:             32,
			CkycAccountType: woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_NORMAL,
		},
	}},
	ActorId:      "random_actor_1",
	CkycDataType: 1,
}
var ckycDataSample2 = &woCkycPb.CkycData{
	CkycNo:      "random_ckyc_no",
	AccountType: woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_NORMAL,
	CkycPayload: &woCkycPb.CkycPayload{CkycData: &woCkycPb.CkycPayload_CkycSearchData{
		CkycSearchData: &woPb.CkycSearchData{
			CkycNumber: "random_ckyc_no",
			Name: &commontypes.Name{
				FirstName:  "first_name",
				MiddleName: "middle_name",
				LastName:   "last_name",
			},
			Age:             32,
			CkycAccountType: woPb.CkycAccountType_CKYC_ACCOUNT_TYPE_NORMAL,
		},
	}},
	ActorId:      "random_actor",
	CkycDataType: 1,
}

func TestCrdbCkycDataDao_Create(t *testing.T) {
	type args struct {
		ctx      context.Context
		ckycData *woCkycPb.CkycData
	}
	tests := []struct {
		name    string
		args    args
		want    *woCkycPb.CkycData
		wantErr bool
		err     error
	}{
		{
			name: "successful creation",
			args: args{
				ctx:      context.Background(),
				ckycData: ckycDataSample,
			},
			want:    ckycDataSample,
			wantErr: false,
			err:     nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			c := &CrdbCkycDataDao{
				db: daoTestSuite.db,
			}
			got, err := c.Create(tt.args.ctx, tt.args.ckycData)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				}
			}
			if err == nil {
				if !proto.Equal(got, tt.want) {
					j1, _ := protojson.Marshal(got)
					j2, _ := protojson.Marshal(tt.want)
					fmt.Println(string(j1))
					fmt.Println(string(j2))
					t.Errorf("Create() got = %v, want %v", got, tt.want)
				}
			}
		})
	}
}

func TestCrdbCkycDataDao_GetByActorIdAndType(t *testing.T) {
	tests := []struct {
		name     string
		want     *woCkycPb.CkycData
		actorId  string
		dataType woCkycPb.CkycDataType
		wantErr  bool
		err      error
	}{
		{
			name:     "successful get by id",
			want:     ckycDataSample2,
			wantErr:  false,
			err:      nil,
			actorId:  "random_actor",
			dataType: woCkycPb.CkycDataType_CKYC_DATA_TYPE_SEARCH,
		},
		{
			name:     "record not found",
			want:     nil,
			wantErr:  true,
			err:      epifierrors.ErrRecordNotFound,
			actorId:  "random_actor",
			dataType: woCkycPb.CkycDataType_CKYC_DATA_TYPE_DOWNLOAD,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			c := &CrdbCkycDataDao{
				db: daoTestSuite.db,
			}
			got, err := c.GetByActorIdAndType(context.Background(), tt.actorId, tt.dataType)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				}
			}
			if err == nil {
				if got == nil {
					t.Errorf("generated id is empty")
				}
				if !proto.Equal(got, tt.want) {
					j1, _ := protojson.Marshal(got)
					j2, _ := protojson.Marshal(tt.want)
					fmt.Println(string(j1))
					fmt.Println(string(j2))
					t.Errorf("Create() got = %v, want %v", got, tt.want)
				}
			}
		})
	}
}
