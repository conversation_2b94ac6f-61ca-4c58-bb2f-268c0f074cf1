package impl

import (
	"context"
	"time"

	timestamp "google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	cmdtypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/dao/model"
)

type OnboardingSummaryCrdb struct {
	db cmdtypes.EpifiWealthCRDB
}

func NewOnboardingSummaryCrdb(db cmdtypes.EpifiWealthCRDB) *OnboardingSummaryCrdb {
	return &OnboardingSummaryCrdb{db: db}
}

const onboardingSummarySelection = `onboarding_details.actor_id as actor_id,
									onboarding_details.id as onboarding_details_id,
									onboarding_details.current_step as current_step,
									onboarding_details.status as status,
									onboarding_details.onboarding_type as onboarding_type,
									onboarding_details.created_at as created_at,
									onboarding_details.updated_at as updated_at,

									onboarding_step_details.id as step_details_id,
									onboarding_step_details.status as step_status,
									onboarding_step_details.sub_status as step_sub_status,
									onboarding_step_details.created_at as step_created_at`

func (d *OnboardingSummaryCrdb) GetByFilters(ctx context.Context, pageToken *pagination.PageToken,
	filters ...storagev2.FilterOption) ([]*woPb.OnboardingSummary, *rpc.PageContextResponse, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "OnboardingSummaryCrdb", "GetByFilters", time.Now())
	pageSize := 100
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	models := make([]*model.OnboardingSummary, 0)

	db = db.
		Table("onboarding_details").
		Joins("INNER JOIN onboarding_step_details" +
			" ON onboarding_details.id = onboarding_step_details.onboarding_details_id" +
			" AND onboarding_details.current_step = onboarding_step_details.step").
		Where("onboarding_step_details.staled_at IS NULL").
		Select(onboardingSummarySelection)
	db = paginateByUpdatedAtDesc(db, pageSize, pageToken)
	for _, filter := range filters {
		db = filter.ApplyInGorm(db)
	}
	if err := db.Scan(&models).Error; err != nil {
		return nil, nil, err
	}
	rows, pageCtxResp, err := pagination.NewPageCtxResp(pageToken, pageSize, model.OnboardingSummaries(models))
	if err != nil {
		return nil, nil, err
	}
	var onboardingSummaries []*woPb.OnboardingSummary
	for _, s := range rows.(model.OnboardingSummaries) {
		onbSummary := &woPb.OnboardingSummary{
			OnboardingDetails: &woPb.OnboardingDetails{
				Id:             s.OnboardingDetailsId,
				ActorId:        s.ActorId,
				Status:         s.Status,
				CurrentStep:    s.CurrentStep,
				CreatedAt:      timestamp.New(s.CreatedAt),
				UpdatedAt:      timestamp.New(s.UpdatedAt),
				OnboardingType: s.OnboardingType,
			},
			CurrentStepDetails: &woPb.OnboardingStepDetails{
				Id:        s.StepDetailsId,
				SubStatus: s.StepSubStatus,
				Status:    s.StepStatus,
				CreatedAt: timestamp.New(s.StepCreatedAt),
			},
		}
		onboardingSummaries = append(onboardingSummaries, onbSummary)
	}
	return onboardingSummaries, pageCtxResp, nil
}

func paginateByUpdatedAtDesc(db *gormv2.DB, pageSize int, pageToken *pagination.PageToken) *gormv2.DB {
	if pageToken == nil || pageToken.Timestamp == nil {
		return db.Order("onboarding_details.updated_at DESC").Limit(pageSize + 1)
	}
	return db.
		Where("onboarding_details.updated_at <= ?", pageToken.Timestamp.AsTime()).
		Order("onboarding_details.updated_at DESC").
		Offset(int(pageToken.Offset)).
		Limit(pageSize + 1)
}
