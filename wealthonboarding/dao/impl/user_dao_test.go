package impl

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"testing"

	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/proto"

	types "github.com/epifi/gamma/api/typesv2"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	userPb "github.com/epifi/gamma/api/wealthonboarding/user"
	"github.com/epifi/be-common/pkg/epifierrors"
	testv2 "github.com/epifi/be-common/pkg/test/v2"
)

var (
	userSample1 = &userPb.User{
		ActorId:    "act-1",
		SchemeCode: userPb.SchemeCode_SCHEME_CODE_CONNECTED_ACCOUNT,
		PersonalDetails: &userPb.PersonalDetails{
			Name: &commontypes.Name{
				FirstName:  "fn",
				MiddleName: "mn",
				LastName:   "ln",
				Honorific:  "h",
			},
			Gender: types.Gender_MALE,
			Dob: &date.Date{
				Year:  1990,
				Month: 7,
				Day:   11,
			},
			Photo: &types.DocumentProof{
				Photo: []*commontypes.Image{
					{
						ImageType: commontypes.ImageType_JPEG,
						ImageUrl:  "image-url",
					},
				},
			},
			MaritalStatus: types.MaritalStatus_UNMARRIED,
			FatherName: &commontypes.Name{
				FirstName:  "fn1",
				MiddleName: "mn1",
				LastName:   "ln1",
				Honorific:  "h1",
			},
			MotherName: &commontypes.Name{
				FirstName:  "fn2",
				MiddleName: "mn2",
				LastName:   "ln2",
				Honorific:  "h2",
			},
			Nationality:              types.Nationality_NATIONALITY_INDIAN,
			ResidentialStatus:        types.ResidentialStatus_RESIDENT_INDIVIDUAL,
			PoliticallyExposedStatus: types.PoliticallyExposedStatus_POLITICALLY_EXPOSED_STATUS_NOT_APPLICABLE,
			Occupation:               "NA",
		},
	}
	userSample2 = &userPb.User{
		ActorId:    "dummy-actor",
		SchemeCode: userPb.SchemeCode_SCHEME_CODE_CONNECTED_ACCOUNT,
		PersonalDetails: &userPb.PersonalDetails{
			Name: &commontypes.Name{
				FirstName:  "fn",
				MiddleName: "mn",
				LastName:   "ln",
				Honorific:  "h",
			},
			Gender: types.Gender_MALE,
			Dob: &date.Date{
				Year:  1990,
				Month: 7,
				Day:   11,
			},
			Photo: &types.DocumentProof{
				Photo: []*commontypes.Image{
					{ImageType: commontypes.ImageType_JPEG,
						ImageUrl: "image-url",
					},
				},
			},
			MaritalStatus: types.MaritalStatus_UNMARRIED,
			FatherName: &commontypes.Name{
				FirstName:  "fn1",
				MiddleName: "mn1",
				LastName:   "ln1",
				Honorific:  "h1",
			},
			MotherName: &commontypes.Name{
				FirstName:  "fn2",
				MiddleName: "mn2",
				LastName:   "ln2",
				Honorific:  "h2",
			},
			Nationality:              types.Nationality_NATIONALITY_INDIAN,
			ResidentialStatus:        types.ResidentialStatus_RESIDENT_INDIVIDUAL,
			PoliticallyExposedStatus: types.PoliticallyExposedStatus_POLITICALLY_EXPOSED_STATUS_NOT_APPLICABLE,
			Occupation:               "NA",
		},
		Metadata: &userPb.UserMetadata{CustomerIpAddress: "dummy-ip"},
		KycData: &userPb.KycData{
			StatusData: &woPb.KraStatusData{PanStatusDetails: &woPb.KraStatusData_PanStatusDetails{
				PanNumber: "PanNumber",
				Name:      "Name",
			}},
		},
	}
	userSample3 = &userPb.User{
		Id:         "b05815c4-35ba-4e25-902b-f73ebe1ae528",
		ActorId:    "dummy-actor",
		SchemeCode: userPb.SchemeCode_SCHEME_CODE_CONNECTED_ACCOUNT,
		PersonalDetails: &userPb.PersonalDetails{
			Name: &commontypes.Name{
				FirstName:  "fn",
				MiddleName: "mn",
				LastName:   "ln",
				Honorific:  "h",
			},
			Gender: types.Gender_MALE,
			Dob: &date.Date{
				Year:  1990,
				Month: 7,
				Day:   11,
			},
			Photo: &types.DocumentProof{
				Photo: []*commontypes.Image{
					{ImageType: commontypes.ImageType_JPEG,
						ImageUrl: "image-url",
					},
				},
			},
			MaritalStatus: types.MaritalStatus_UNMARRIED,
			FatherName: &commontypes.Name{
				FirstName:  "fn1",
				MiddleName: "mn1",
				LastName:   "ln1",
				Honorific:  "h1",
			},
			MotherName: &commontypes.Name{
				FirstName:  "fn2",
				MiddleName: "mn2",
				LastName:   "ln2",
				Honorific:  "h2",
			},
			Nationality:              types.Nationality_NATIONALITY_INDIAN,
			ResidentialStatus:        types.ResidentialStatus_RESIDENT_INDIVIDUAL,
			PoliticallyExposedStatus: types.PoliticallyExposedStatus_POLITICALLY_EXPOSED_STATUS_NOT_APPLICABLE,
			Occupation:               "NA",
		},
		Metadata: &userPb.UserMetadata{CustomerIpAddress: "dummy-ip1"},
		KycData: &userPb.KycData{
			StatusData: &woPb.KraStatusData{PanStatusDetails: &woPb.KraStatusData_PanStatusDetails{
				PanNumber: "PanNumber1",
				Name:      "Name1",
			}},
		},
	}
)

func TestCrdbUserDao_Create(t *testing.T) {
	type args struct {
		ctx  context.Context
		user *userPb.User
	}
	tests := []struct {
		name    string
		args    args
		want    *userPb.User
		wantErr bool
		err     error
	}{
		{
			name: "successful creation flow",
			args: args{
				ctx:  context.Background(),
				user: userSample1,
			},
			want:    userSample1,
			wantErr: false,
		},
		{
			name: "duplicate actor create",
			args: args{
				ctx:  context.Background(),
				user: userSample2,
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrDuplicateEntry,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			c := &CrdbUserDao{
				db: daoTestSuite.db,
			}
			got, err := c.Create(tt.args.ctx, tt.args.user)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				}
			}
			if err == nil {
				if got == nil || got.Id == "" {
					t.Errorf("generated id is empty")
				}
				if got != nil {
					got.Id = ""
					got.CreatedAt = nil
					got.UpdatedAt = nil
				}
				if !proto.Equal(got, tt.want) {
					t.Errorf("Create() got = %v, want %v", got, tt.want)
				}
			}
		})
	}
}

func TestCrdbUserDao_GetByActorId(t *testing.T) {
	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name    string
		args    args
		want    *userPb.User
		wantErr bool
		err     error
	}{
		{
			name: "successfully get by actor id",
			args: args{
				ctx:     context.Background(),
				actorId: "dummy-actor",
			},
			want:    userSample2,
			wantErr: false,
		},
		{
			name: "record not found",
			args: args{
				ctx:     context.Background(),
				actorId: "dummy-actor1",
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			c := &CrdbUserDao{
				db: daoTestSuite.db,
			}
			got, err := c.GetByActorId(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.wantErr)
				}
			}
			if got != nil {
				got.Id = ""
				got.CreatedAt = nil
				got.UpdatedAt = nil
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetByActorId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCrdbUserDao_GetById(t *testing.T) {
	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		args    args
		want    *userPb.User
		wantErr bool
		err     error
	}{
		{
			name: "successfully get by id",
			args: args{
				ctx: context.Background(),
				id:  "b05815c4-35ba-4e25-902b-f73ebe1ae528",
			},
			want:    userSample2,
			wantErr: false,
		},
		{
			name: "record not found",
			args: args{
				ctx: context.Background(),
				id:  "b05815c4-35ba-4e25-902b-f73ebe1ae527",
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			c := &CrdbUserDao{
				db: daoTestSuite.db,
			}
			got, err := c.GetById(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				}
			}
			if got != nil {
				got.Id = ""
				got.CreatedAt = nil
				got.UpdatedAt = nil
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCrdbUserDao_Update(t *testing.T) {
	type args struct {
		ctx         context.Context
		od          *userPb.User
		updateMasks []userPb.UserFieldMask
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "successful fields update",
			args: args{
				ctx: context.Background(),
				od:  userSample3,
				updateMasks: []userPb.UserFieldMask{
					userPb.UserFieldMask_USER_FIELD_MASK_SCHEME_CODE,
					userPb.UserFieldMask_USER_FIELD_MASK_METADATA,
					userPb.UserFieldMask_USER_FIELD_MASK_PERSONAL_DETAILS,
					userPb.UserFieldMask_USER_FIELD_MASK_KYC_DATA,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			c := &CrdbUserDao{
				db: daoTestSuite.db,
			}
			if err := c.Update(tt.args.ctx, tt.args.od, tt.args.updateMasks); (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
