package impl

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"reflect"
	"testing"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/wealthonboarding"
	testv2 "github.com/epifi/be-common/pkg/test/v2"
)

var createdAt, _ = time.Parse(time.RFC3339, "2022-05-05T21:06:37.409484Z")
var updatedAt, _ = time.Parse(time.RFC3339, "2022-05-05T21:06:37.409484Z")

var vendorRequestSample = &wealthonboarding.VendorRequest{
	ActorId:       "actor1",
	Vendor:        commonvgpb.Vendor_CKYC,
	ResponseCode:  "ok",
	RpcStatusCode: 0,
	RawResponse:   "raw response",
	Api:           wealthonboarding.Api_CKYC_SEARCH,
	RequestId:     "1",
	TraceId:       "1",
	RawRequest:    "raw request",
	Id:            "3bf9225e-dffe-11ec-9d64-0242ac120002",
	CreatedAt:     timestamppb.New(createdAt),
	UpdatedAt:     timestamppb.New(updatedAt),
}

func TestVendorRequestDaoImpl_Create(t *testing.T) {
	t.Skip()
	type args struct {
		ctx       context.Context
		vendorReq *wealthonboarding.VendorRequest
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "happy flow",
			args: args{
				ctx: context.Background(),
				vendorReq: &wealthonboarding.VendorRequest{
					ActorId:       "actor2",
					Vendor:        commonvgpb.Vendor_CKYC,
					ResponseCode:  "ok",
					RpcStatusCode: 0,
					RawResponse:   "raw response",
					Api:           wealthonboarding.Api_CKYC_SEARCH,
					RequestId:     "1",
					TraceId:       "1",
					RawRequest:    "1",
				}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			v := &VendorRequestDaoImpl{
				db: daoTestSuite.db,
			}
			if err := v.Create(tt.args.ctx, tt.args.vendorReq); (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestVendorRequestDaoImpl_GetByActorId(t *testing.T) {
	t.Skip()
	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name    string
		args    args
		want    []*wealthonboarding.VendorRequest
		wantErr bool
		err     error
	}{
		{
			name: "happy flow",
			args: args{
				ctx:     context.Background(),
				actorId: "actor1",
			},
			want:    []*wealthonboarding.VendorRequest{vendorRequestSample},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			v := &VendorRequestDaoImpl{
				db: daoTestSuite.db,
			}
			got, err := v.GetByActorId(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.wantErr)
				}
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetByActorId() got = %v, want %v", got, tt.want)
			}
		})
	}
}
