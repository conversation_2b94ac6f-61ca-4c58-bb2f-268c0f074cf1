package impl

import (
	"context"
	"fmt"
	"path/filepath"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/types"
)

type VendorRequestProducerImpl struct {
	producer types.VendorRequestProducer
	s3Client types.VendorRequestS3Client
	conf     *config.Config
}

var _ dao.VendorRequestDao = &VendorRequestProducerImpl{}

func NewVendorRequestProducer(producer types.VendorRequestProducer, s3Client types.VendorRequestS3Client, conf *config.Config) *VendorRequestProducerImpl {
	return &VendorRequestProducerImpl{
		producer: producer,
		s3Client: s3Client,
		conf:     conf,
	}
}

// Create publishes vendor request to Kinesis stream.
// Kinesis stream has a restriction to publish data with size greater than 1Mib, Create handles data larger than 1Mib by writing directly to s3 bucket
func (v *VendorRequestProducerImpl) Create(ctx context.Context, vendorReq *wealthonboarding.VendorRequest) error {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "VendorRequestProducerImpl", "Create", time.Now())
	_, publishErr := v.producer.PublishMessage(ctx, vendorReq)
	if publishErr == nil {
		return nil
	}
	buf, marshalErr := protojson.Marshal(vendorReq)
	if marshalErr != nil {
		return errors.Wrap(publishErr, "failed to publish vendor request to kinesis, failed to marshal data")
	}
	if len(buf) > 950000 { // greater than ~ 0.9 Mib since we can't write data greater than 1 Mib to kinesis stream.
		logger.InfoNoCtx("Vendor request data greater than 1Mib. Writing directly to s3.")
		t := vendorReq.CreatedAt.AsTime()
		bucketName := v.conf.VendorRequestS3Conf.Bucket
		fileName := fmt.Sprintf("%s-wealthonboarding-vendor-request-%s", v.conf.Application.Environment, vendorReq.Id)
		dst := filepath.Join(bucketName, v.conf.Application.Environment, "data", "vendor", "vendor_request", strconv.Itoa(t.Year()), intToStringWith2Digits(int(t.Month())), intToStringWith2Digits(t.Day()), intToStringWith2Digits(t.Hour()), fileName)
		err := v.s3Client.Write(ctx, dst, buf, "bucket-owner-full-control")
		if err != nil {
			logger.ErrorNoCtx("failed to write vendor request to s3 directly", zap.Error(err))
			return err
		}
		return nil
	}
	return errors.Wrap(publishErr, "failed to publish vendor request to kinesis")
}

func (v *VendorRequestProducerImpl) GetByActorId(ctx context.Context, actorId string) ([]*wealthonboarding.VendorRequest, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "VendorRequestProducerImpl", "GetByActorId", time.Now())
	return nil, nil
}

func intToStringWith2Digits(val int) string {
	return fmt.Sprintf("%02v", val)
}
