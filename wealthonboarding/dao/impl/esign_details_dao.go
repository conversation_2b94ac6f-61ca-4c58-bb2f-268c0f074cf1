package impl

import (
	"context"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/pkg/errors"
	gormv2 "gorm.io/gorm"

	cmdtypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epifierrors"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/dao/model"
)

type CrdbESignDetailsDao struct {
	db cmdtypes.EpifiWealthCRDB
}

var eSignDetailsColumnNames = map[woPb.ESignDetailsFieldMask]string{
	woPb.ESignDetailsFieldMask_E_SIGN_DETAILS_FIELD_MASK_SIGN_URL:       "sign_url",
	woPb.ESignDetailsFieldMask_E_SIGN_DETAILS_FIELD_MASK_TXN_STATE:      "txn_state",
	woPb.ESignDetailsFieldMask_E_SIGN_DETAILS_FIELD_MASK_STORED_S3_PATH: "stored_s3_path",
	woPb.ESignDetailsFieldMask_E_SIGN_DETAILS_FIELD_MASK_SIGNED_AT:      "signed_at",
}

var _ dao.ESignDetailsDao = &CrdbESignDetailsDao{}

func NewESignDetailsDao(db cmdtypes.EpifiWealthCRDB) *CrdbESignDetailsDao {
	return &CrdbESignDetailsDao{db: db}
}

func (c *CrdbESignDetailsDao) Create(ctx context.Context, eSignDetails *woPb.ESignDetails) (*woPb.ESignDetails, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbESignDetailsDao", "Create", time.Now())
	eSignDetailsModel := model.NewESignDetails(eSignDetails)
	if eSignDetails.GetTxnState() == woPb.TxnState_TXN_STATE_TYPE_UNSPECIFIED {
		return nil, errors.New("txn_state cannot be unspecified")
	}
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	res := db.Create(eSignDetailsModel)
	if res.Error != nil {
		// if trying to create more than one entry for an actor_id, return duplicate entry error
		if storagev2.IsDuplicateRowError(res.Error) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		return nil, res.Error
	}
	return eSignDetailsModel.GetProto(), nil
}

func (c *CrdbESignDetailsDao) Get(ctx context.Context, eSignId string) (*woPb.ESignDetails, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbESignDetailsDao", "Get", time.Now())
	if eSignId == "" {
		return nil, errors.New("eSignId cannot be empty")
	}
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	var eSignModel model.ESignDetails
	if err := db.Where("id = ?", eSignId).Take(&eSignModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return eSignModel.GetProto(), nil
}

func (c *CrdbESignDetailsDao) UpdateById(ctx context.Context, eSignDetails *woPb.ESignDetails, updateMasks []woPb.ESignDetailsFieldMask) error {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbESignDetailsDao", "UpdateById", time.Now())
	if eSignDetails.GetId() == "" {
		return errors.New("primary id cannot be empty")
	}
	if eSignDetails.GetTxnState() == woPb.TxnState_TXN_STATE_TYPE_UNSPECIFIED {
		return errors.New("txn_state cannot be unspecified")
	}
	if len(updateMasks) == 0 {
		return fmt.Errorf("update mask can't be empty")
	}
	eSignModel := model.NewESignDetails(eSignDetails)
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	updateColumns := c.selectedColumnsForUpdate(updateMasks)
	res := db.Model(eSignModel).Where("id = ?", eSignDetails.GetId()).Select(updateColumns).Updates(eSignModel)
	if res.Error != nil {
		return res.Error
	}
	if res.RowsAffected == 0 {
		return epifierrors.ErrRowNotUpdated
	}
	return nil
}

func (c *CrdbESignDetailsDao) selectedColumnsForUpdate(updateMasks []woPb.ESignDetailsFieldMask) []string {
	var selectColumns []string
	for _, field := range updateMasks {
		selectColumns = append(selectColumns, eSignDetailsColumnNames[field])
	}
	return selectColumns
}
