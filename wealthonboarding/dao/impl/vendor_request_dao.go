package impl

import (
	"context"
	"fmt"
	"time"

	cmdtypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/dao/model"
)

type VendorRequestDaoImpl struct {
	db cmdtypes.EpifiWealthCRDB
}

var _ dao.VendorRequestDao = &VendorRequestDaoImpl{}

func NewVendorRequestDao(db cmdtypes.EpifiWealthCRDB) *VendorRequestDaoImpl {
	return &VendorRequestDaoImpl{db: db}
}

func (v *VendorRequestDaoImpl) Create(ctx context.Context, vendorReq *wealthonboarding.VendorRequest) error {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "VendorRequestDaoImpl", "Create", time.Now())
	vendorReqRespModel := model.NewVendorRequest(vendorReq)
	db := gormctxv2.FromContextOrDefault(ctx, v.db)
	res := db.Create(vendorReqRespModel)
	switch {
	case res.Error == nil:
		return nil
	case storagev2.IsDuplicateRowError(res.Error):
		return epifierrors.ErrDuplicateEntry
	default:
		return fmt.Errorf("unknown error in creating entry in vendor_req_resps: %w", res.Error)
	}
}

func (v *VendorRequestDaoImpl) GetByActorId(ctx context.Context, actorId string) ([]*wealthonboarding.VendorRequest, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "VendorRequestDaoImpl", "GetByActorId", time.Now())
	if actorId == "" {
		return nil, fmt.Errorf("actor id required to get dao: %w", epifierrors.ErrInvalidArgument)
	}
	var m []*model.VendorRequest
	db := gormctxv2.FromContextOrDefault(ctx, v.db)
	res := db.Where("actor_id = ?", actorId).Order("created_at DESC").Find(&m)
	switch {
	case res.Error == nil:
		var vendorReqProtos []*wealthonboarding.VendorRequest
		for _, request := range m {
			vendorReqProto, err := request.GetProto()
			if err != nil {
				return nil, err
			}
			vendorReqProtos = append(vendorReqProtos, vendorReqProto)
		}
		return vendorReqProtos, nil
	case storagev2.IsRecordNotFoundError(res.Error):
		return nil, epifierrors.ErrRecordNotFound
	default:
		return nil, fmt.Errorf("unknown error in getting vendor response by actorId: %w", res.Error)
	}
}
