package impl

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"fmt"
	"testing"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/epifierrors"
	testv2 "github.com/epifi/be-common/pkg/test/v2"
)

var manualReviewSample = &woPb.ManualReview{
	ReviewStatus: woPb.ReviewStatus_REVIEW_STATUS_PENDING,
	ReviewPayload: &woPb.ReviewPayload{
		Payload: &woPb.ReviewPayload_LivenessReview_{
			LivenessReview: &woPb.ReviewPayload_LivenessReview{
				ActorId:       "random_actor_id",
				VideoLocation: "random/location",
				AttemptId:     "random_attempt",
				Otp:           "123456",
				OtpScore:      11,
				LivenessScore: 11,
				KycImage: &commontypes.Image{
					ImageType:       commontypes.ImageType_JPEG,
					ImageDataBase64: "random_base_64",
				},
				CreatedAt: nil,
			},
		},
	},
	ItemType: woPb.ItemType_ITEM_TYPE_LIVENESS,
}
var manualReviewSample2 = &woPb.ManualReview{
	Id:           "b05815c4-35ba-4e25-902b-f73ebe1ae528",
	ReviewStatus: woPb.ReviewStatus_REVIEW_STATUS_REJECTED,
	ReviewPayload: &woPb.ReviewPayload{
		Payload: &woPb.ReviewPayload_LivenessReview_{
			LivenessReview: &woPb.ReviewPayload_LivenessReview{
				ActorId:       "random_actor_id",
				VideoLocation: "random/location",
				AttemptId:     "random_attempt",
				Otp:           "123456",
				OtpScore:      11,
				LivenessScore: 11,
				KycImage: &commontypes.Image{
					ImageType:       commontypes.ImageType_JPEG,
					ImageDataBase64: "random_base_64",
				},
				CreatedAt: nil,
			},
		},
	},
	ItemType: woPb.ItemType_ITEM_TYPE_LIVENESS,
}

func TestCrdbManualReviewDao_Create(t *testing.T) {
	type args struct {
		ctx          context.Context
		manualReview *woPb.ManualReview
	}
	tests := []struct {
		name    string
		args    args
		want    *woPb.ManualReview
		wantErr bool
		err     error
	}{
		{
			name: "successful creation",
			args: args{
				ctx:          context.Background(),
				manualReview: manualReviewSample,
			},
			want:    manualReviewSample,
			wantErr: false,
			err:     nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			c := &CrdbManualReviewDao{
				db: daoTestSuite.db,
			}
			got, err := c.Create(tt.args.ctx, tt.args.manualReview)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				}
			}
			if err == nil {
				if got == nil || got.Id == "" {
					t.Errorf("generated id is empty")
				}
				if got != nil {
					got.Id = ""
				}
				if !proto.Equal(got, tt.want) {
					j1, _ := protojson.Marshal(got)
					j2, _ := protojson.Marshal(tt.want)
					fmt.Println(string(j1))
					fmt.Println(string(j2))
					t.Errorf("Create() got = %v, want %v", got, tt.want)
				}
			}
		})
	}
}

func TestCrdbManualReviewDao_GetById(t *testing.T) {
	tests := []struct {
		name    string
		want    *woPb.ManualReview
		id      string
		wantErr bool
		err     error
	}{
		{
			name:    "successful get by id",
			want:    manualReviewSample,
			wantErr: false,
			err:     nil,
			id:      "b05815c4-35ba-4e25-902b-f73ebe1ae528",
		},
		{
			name:    "record not found",
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
			id:      "a05815c4-35ba-4e25-902b-f73ebe1ae528",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			c := &CrdbManualReviewDao{
				db: daoTestSuite.db,
			}
			got, err := c.GetById(context.Background(), tt.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				}
			}
			if err == nil {
				if got == nil || got.Id == "" {
					t.Errorf("generated id is empty")
				}
				if got != nil {
					got.Id = ""
					got.ReviewedAt = nil
				}
				if !proto.Equal(got, tt.want) {
					j1, _ := protojson.Marshal(got)
					j2, _ := protojson.Marshal(tt.want)
					fmt.Println(string(j1))
					fmt.Println(string(j2))
					t.Errorf("Create() got = %v, want %v", got, tt.want)
				}
			}
		})
	}
}

func TestCrdbManualReviewDao_UpdateById(t *testing.T) {
	type args struct {
		review *woPb.ManualReview
	}
	tests := []struct {
		name    string
		args    *args
		want    *woPb.ManualReview
		wantErr bool
		err     error
	}{
		{
			name:    "successful update by id",
			args:    &args{review: manualReviewSample2},
			want:    manualReviewSample2,
			wantErr: false,
			err:     nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			c := &CrdbManualReviewDao{
				db: daoTestSuite.db,
			}
			err := c.UpdateById(context.Background(), tt.args.review, []woPb.ManualReviewFieldMask{woPb.ManualReviewFieldMask_MANUAL_REVIEW_FIELD_MASK_STATUS})
			if (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			}

			result, err := c.GetById(context.Background(), tt.args.review.GetId())
			if err != nil {
				t.Errorf("error while fetching manual review item %v", err)
				return
			}
			if result != nil {
				tt.args.review.ReviewedAt = result.ReviewedAt
			}
			if !proto.Equal(result, tt.args.review) {
				j1, _ := protojson.Marshal(result)
				j2, _ := protojson.Marshal(tt.args.review)
				fmt.Println(string(j1))
				fmt.Println(string(j2))
				t.Errorf("Update() got = %v, want %v", result, tt.args.review)
			}
		})
	}
}
