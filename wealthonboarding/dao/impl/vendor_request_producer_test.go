package impl

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"strings"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/types/known/timestamppb"

	s3Mock "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	kinesisMock "github.com/epifi/be-common/pkg/stream/mock"

	"github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/config"
)

func TestVendorRequestProducerImpl_Create(t *testing.T) {
	bigRequestStr := strings.Repeat("#", 1048576)
	type fields struct {
		conf *config.Config
	}
	type args struct {
		ctx       context.Context
		vendorReq *wealthonboarding.VendorRequest
	}
	tests := []struct {
		name           string
		fields         fields
		args           args
		wantErr        bool
		setupMockCalls func(mockProducer *kinesisMock.MockProducer, mockS3Client *s3Mock.MockS3Client)
	}{
		{
			name:   "happy flow",
			fields: fields{conf: &config.Config{}},
			args: args{
				ctx: context.Background(),
				vendorReq: &wealthonboarding.VendorRequest{
					ActorId:       "actor2",
					Vendor:        commonvgpb.Vendor_CKYC,
					ResponseCode:  "ok",
					RpcStatusCode: 0,
					RawResponse:   "raw response",
					Api:           wealthonboarding.Api_CKYC_SEARCH,
					RequestId:     "1",
					TraceId:       "1",
					RawRequest:    "1",
				}},
			wantErr: false,
			setupMockCalls: func(mockProducer *kinesisMock.MockProducer, mockS3Client *s3Mock.MockS3Client) {
				mockProducer.EXPECT().PublishMessage(gomock.Any(), gomock.Any()).Return("", nil).AnyTimes()
			},
		},
		{
			name: "data greater than 1Mib. Use s3",
			fields: fields{conf: &config.Config{
				Application:         &config.Application{Environment: "prod"},
				VendorRequestS3Conf: &config.S3Conf{Bucket: "test-bucket"},
			}},
			args: args{
				ctx: context.Background(),
				vendorReq: &wealthonboarding.VendorRequest{
					RawRequest: bigRequestStr,
					Id:         "id",
					CreatedAt:  timestamppb.Now(),
				},
			},
			wantErr: false,
			setupMockCalls: func(mockProducer *kinesisMock.MockProducer, mockS3Client *s3Mock.MockS3Client) {
				mockProducer.EXPECT().PublishMessage(gomock.Any(), gomock.Any()).Return("", errors.New("test-error")).Times(1)
				mockS3Client.EXPECT().Write(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()
			mockProducer := kinesisMock.NewMockProducer(ctr)
			mockS3Client := s3Mock.NewMockS3Client(ctr)
			tt.setupMockCalls(mockProducer, mockS3Client)
			v := &VendorRequestProducerImpl{
				producer: mockProducer,
				s3Client: mockS3Client,
				conf:     tt.fields.conf,
			}
			if err := v.Create(tt.args.ctx, tt.args.vendorReq); (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
