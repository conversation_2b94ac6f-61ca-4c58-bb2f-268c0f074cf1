package impl

import (
	"time"

	"github.com/pkg/errors"
	"golang.org/x/net/context"
	gormv2 "gorm.io/gorm"

	cmdtypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	woCkycPb "github.com/epifi/gamma/api/wealthonboarding/ckyc"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/dao/model"
)

var _ dao.CkycDataDao = &CrdbCkycDataDao{}

type CrdbCkycDataDao struct {
	db cmdtypes.EpifiWealthCRDB
}

func NewCrdbCkycDataDao(db cmdtypes.EpifiWealthCRDB) *CrdbCkycDataDao {
	return &CrdbCkycDataDao{db: db}
}
func (c *CrdbCkycDataDao) Create(ctx context.Context, ckycData *woCkycPb.CkycData) (*woCkycPb.CkycData, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbCkycDataDao", "Create", time.Now())
	ckycDataModel := model.NewCkycData(ckycData)
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	res := db.Create(ckycDataModel)
	if res.Error != nil {
		// if trying to create more than one entry for an actor_id, return duplicate entry error
		if storagev2.IsDuplicateRowError(res.Error) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		return nil, res.Error
	}
	return ckycDataModel.GetProto(), nil
}

func (c *CrdbCkycDataDao) GetByActorIdAndType(ctx context.Context, actorId string, dataType woCkycPb.CkycDataType) (*woCkycPb.CkycData, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbCkycDataDao", "GetByActorIdAndType", time.Now())
	if actorId == "" {
		return nil, errors.New("eSignId cannot be empty")
	}
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	var ckycDataModel model.CkycData
	if err := db.Where("actor_id = ? AND type = ?", actorId, dataType).Take(&ckycDataModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return ckycDataModel.GetProto(), nil
}
