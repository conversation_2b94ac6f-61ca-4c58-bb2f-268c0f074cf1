//nolint:dupl
package impl

import (
	"context"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/epifi/gamma/wealthonboarding/dao/model"

	"github.com/pkg/errors"
	gormv2 "gorm.io/gorm"

	cmdtypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epifierrors"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	userPb "github.com/epifi/gamma/api/wealthonboarding/user"
	"github.com/epifi/gamma/wealthonboarding/dao"
)

type CrdbUserDao struct {
	db cmdtypes.EpifiWealthCRDB
}

var _ dao.UserDao = &CrdbUserDao{}

var userColumnNames = map[userPb.UserFieldMask]string{
	userPb.UserFieldMask_USER_FIELD_MASK_SCHEME_CODE:      "scheme_code",
	userPb.UserFieldMask_USER_FIELD_MASK_METADATA:         "metadata",
	userPb.UserFieldMask_USER_FIELD_MASK_PERSONAL_DETAILS: "personal_details",
	userPb.UserFieldMask_USER_FIELD_MASK_KYC_DATA:         "kyc_data",
}

func NewCrdbUserDao(db cmdtypes.EpifiWealthCRDB) *CrdbUserDao {
	return &CrdbUserDao{
		db: db,
	}
}

func (c *CrdbUserDao) Create(ctx context.Context, user *userPb.User) (*userPb.User, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbUserDao", "Create", time.Now())
	userModel := model.NewUser(user)
	handle := gormctxv2.FromContextOrDefault(ctx, c.db)
	res := handle.Create(userModel)
	if res.Error != nil {
		// if trying to create more than one entry for an actor_id, return duplicate entry error
		if storagev2.IsDuplicateRowError(res.Error) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		return nil, res.Error
	}
	return userModel.GetProto(), nil
}

func (c *CrdbUserDao) Update(ctx context.Context, usr *userPb.User, updateMasks []userPb.UserFieldMask) error {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbUserDao", "Update", time.Now())
	if usr.GetId() == "" {
		return fmt.Errorf("primary identifier can't be empty for an update operation")
	}
	if len(updateMasks) == 0 {
		return fmt.Errorf("update mask can't be empty")
	}

	userModel := model.NewUser(usr)
	updateColumns := c.selectedColumnsForUpdate(updateMasks)
	handle := gormctxv2.FromContextOrDefault(ctx, c.db)
	whereClause := &model.User{
		Id: usr.GetId(),
	}
	if err := handle.Model(userModel).Where(whereClause).Select(updateColumns).Updates(userModel).Error; err != nil {
		return err
	}
	return nil
}

func (c *CrdbUserDao) GetById(ctx context.Context, id string) (*userPb.User, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbUserDao", "GetById", time.Now())
	if id == "" {
		return nil, errors.New("id can't be blank")
	}
	var userModel model.User
	d := gormctxv2.FromContextOrDefault(ctx, c.db)
	if err := d.Where("id = ?", id).Take(&userModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return userModel.GetProto(), nil
}

func (c *CrdbUserDao) GetByActorId(ctx context.Context, actorId string) (*userPb.User, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbUserDao", "GetByActorId", time.Now())
	if actorId == "" {
		return nil, errors.New("actorId can't be blank")
	}
	var userModel model.User
	d := gormctxv2.FromContextOrDefault(ctx, c.db)
	if err := d.Where("actor_id = ?", actorId).Take(&userModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return userModel.GetProto(), nil
}

func (c *CrdbUserDao) selectedColumnsForUpdate(updateMasks []userPb.UserFieldMask) []string {
	var selectColumns []string
	for _, field := range updateMasks {
		selectColumns = append(selectColumns, userColumnNames[field])
	}
	return selectColumns
}
