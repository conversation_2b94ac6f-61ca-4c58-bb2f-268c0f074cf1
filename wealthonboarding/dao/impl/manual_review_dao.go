package impl

import (
	"context"
	"errors"
	"fmt"
	"time"

	gormv2 "gorm.io/gorm"

	cmdtypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/dao/model"
)

var manualReviewColumnNames = map[woPb.ManualReviewFieldMask]string{
	woPb.ManualReviewFieldMask_MANUAL_REVIEW_FIELD_MASK_STATUS:      "status",
	woPb.ManualReviewFieldMask_MANUAL_REVIEW_FIELD_MASK_PAYLOAD:     "payload",
	woPb.ManualReviewFieldMask_MANUAL_REVIEW_FIELD_MASK_REVIEWED_AT: "reviewed_at",
}

type CrdbManualReviewDao struct {
	db cmdtypes.EpifiWealthCRDB
}

var _ dao.ManualReviewDao = &CrdbManualReviewDao{}

func NewCrdbManualReviewDao(db cmdtypes.EpifiWealthCRDB) *CrdbManualReviewDao {
	return &CrdbManualReviewDao{db: db}
}

func (c *CrdbManualReviewDao) Create(ctx context.Context, review *woPb.ManualReview) (*woPb.ManualReview, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbManualReviewDao", "Create", time.Now())
	manualReviewModel := model.NewManualReview(review)
	if review.GetReviewStatus() == woPb.ReviewStatus_REVIEW_STATUS_UNSPECIFIED {
		return nil, errors.New("status cannot be unspecified")
	}
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	res := db.Create(manualReviewModel)
	if res.Error != nil {
		return nil, res.Error
	}
	return manualReviewModel.GetProto(), nil
}

func (c *CrdbManualReviewDao) GetById(ctx context.Context, id string) (*woPb.ManualReview, error) {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbManualReviewDao", "GetById", time.Now())
	if id == "" {
		return nil, errors.New("id cannot be empty")
	}
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	manualReviewModel := &model.ManualReview{}
	if err := db.Where("id = ?", id).Take(manualReviewModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return manualReviewModel.GetProto(), nil
}

func (c *CrdbManualReviewDao) UpdateById(ctx context.Context, review *woPb.ManualReview, updateMasks []woPb.ManualReviewFieldMask) error {
	defer metric_util.TrackDuration("wealthonboarding/dao/impl", "CrdbManualReviewDao", "UpdateById", time.Now())
	if review.GetId() == "" {
		return fmt.Errorf("primary identifier can't be empty for an update operation")
	}
	if len(updateMasks) == 0 {
		return fmt.Errorf("update mask can't be empty")
	}

	manualReviewModel := model.NewManualReview(review)
	updateColumns := c.selectedColumnsForUpdate(updateMasks)
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	whereClause := &model.ManualReview{
		Id: review.GetId(),
	}
	if err := db.Model(manualReviewModel).Where(whereClause).Select(updateColumns).Updates(manualReviewModel).Error; err != nil {
		return err
	}
	return nil
}

func (c *CrdbManualReviewDao) selectedColumnsForUpdate(updateMasks []woPb.ManualReviewFieldMask) []string {
	var selectColumns []string
	for _, field := range updateMasks {
		selectColumns = append(selectColumns, manualReviewColumnNames[field])
	}
	return selectColumns
}
