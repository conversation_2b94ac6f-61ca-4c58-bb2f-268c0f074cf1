package impl

import (
	"context"
	"errors"
	"testing"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/epifierrors"
	testv2 "github.com/epifi/be-common/pkg/test/v2"

	"google.golang.org/protobuf/types/known/timestamppb"
)

var (
	onboardingStepDetailsSample1 = &woPb.OnboardingStepDetails{
		OnboardingDetailsId: "b05815c4-35ba-4e25-902b-f73ebe1ae528",
		Step:                woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
		Status:              woPb.OnboardingStepStatus_STEP_STATUS_PENDING,
	}
	onboardingStepDetailsSample2 = &woPb.OnboardingStepDetails{
		OnboardingDetailsId: "c1a9e432-414f-4cec-856b-b2fb76ab2057",
		Step:                woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
		Status:              woPb.OnboardingStepStatus_STEP_STATUS_PENDING,
	}
	onboardingStepDetailsSample3 = &woPb.OnboardingStepDetails{
		Id:                  "2e965494-c753-46ce-892a-98a852f69528",
		OnboardingDetailsId: "c1a9e432-414f-4cec-856b-b2fb76ab2057",
		Step:                woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
		Status:              woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
		CompletedAt:         timestamppb.Now(),
	}
)

func TestCrdbOnboardingStepDetailsDao_Create(t *testing.T) {
	type args struct {
		ctx                   context.Context
		onboardingStepDetails *woPb.OnboardingStepDetails
	}
	tests := []struct {
		name    string
		args    args
		want    *woPb.OnboardingStepDetails
		wantErr bool
	}{
		{
			name: "successful creation flow",
			args: args{
				ctx:                   context.Background(),
				onboardingStepDetails: onboardingStepDetailsSample1,
			},
			want:    onboardingStepDetailsSample1,
			wantErr: false,
		},
	}
	testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CrdbOnboardingStepDetailsDao{
				db: daoTestSuite.db,
			}
			got, err := c.Create(tt.args.ctx, tt.args.onboardingStepDetails)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&woPb.OnboardingStepDetails{}, "created_at", "updated_at", "completed_at", "id", "staled_at", "expected_retry_at"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("Create() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestCrdbOnboardingStepDetailsDao_GetById(t *testing.T) {
	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		args    args
		want    *woPb.OnboardingStepDetails
		wantErr bool
		err     error
	}{
		{
			name: "successfully get by id",
			args: args{
				ctx: context.Background(),
				id:  "2e965494-c753-46ce-892a-98a852f69528",
			},
			want:    onboardingStepDetailsSample2,
			wantErr: false,
		},
		{
			name: "record not found",
			args: args{
				ctx: context.Background(),
				id:  "3e965494-c753-46ce-892a-98a852f69528",
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			c := &CrdbOnboardingStepDetailsDao{
				db: daoTestSuite.db,
			}
			got, err := c.GetById(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				}
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&woPb.OnboardingStepDetails{}, "created_at", "updated_at", "completed_at", "id", "staled_at", "expected_retry_at"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("GetById() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestCrdbOnboardingStepDetailsDao_UpdateById(t *testing.T) {
	type args struct {
		ctx                   context.Context
		onboardingStepDetails *woPb.OnboardingStepDetails
		updateMasks           []woPb.OnboardingStepDetailsFieldMask
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "successful fields update",
			args: args{
				ctx:                   context.Background(),
				onboardingStepDetails: onboardingStepDetailsSample3,
				updateMasks: []woPb.OnboardingStepDetailsFieldMask{
					woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_STATUS,
					woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_METADATA,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			c := &CrdbOnboardingStepDetailsDao{
				db: daoTestSuite.db,
			}
			if err := c.UpdateById(tt.args.ctx, tt.args.onboardingStepDetails, tt.args.updateMasks); (err != nil) != tt.wantErr {
				t.Errorf("UpdateById() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCrdbOnboardingStepDetailsDao_GetByOnboardingDetailsIdAndStep(t *testing.T) {
	type args struct {
		ctx                 context.Context
		onboardingDetailsId string
		step                woPb.OnboardingStep
	}
	tests := []struct {
		name    string
		args    args
		want    *woPb.OnboardingStepDetails
		wantErr bool
		err     error
	}{
		{
			name: "successfully get by id",
			args: args{
				ctx:                 context.Background(),
				onboardingDetailsId: "c1a9e432-414f-4cec-856b-b2fb76ab2057",
				step:                woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
			},
			want:    onboardingStepDetailsSample2,
			wantErr: false,
		},
		{
			name: "record not found-1",
			args: args{
				ctx:                 context.Background(),
				onboardingDetailsId: "c1a9e432-414f-4cec-856b-b2fb76ab2057",
				step:                woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
		{
			name: "record not found-2",
			args: args{
				ctx:                 context.Background(),
				onboardingDetailsId: "d1a9e432-414f-4cec-856b-b2fb76ab2058",
				step:                woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			c := &CrdbOnboardingStepDetailsDao{
				db: daoTestSuite.db,
			}
			got, err := c.GetByOnboardingDetailsIdAndStep(tt.args.ctx, tt.args.onboardingDetailsId, tt.args.step)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByOnboardingDetailsIdAndStep() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetByOnboardingDetailsIdAndStep() error = %v, wantErr %v", err, tt.wantErr)
				}
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&woPb.OnboardingStepDetails{}, "created_at", "updated_at", "completed_at", "id", "staled_at", "expected_retry_at"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("GetByOnboardingDetailsIdAndStep() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestCrdbOnboardingStepDetailsDao_GetOrCreate(t *testing.T) {
	type args struct {
		ctx               context.Context
		onbDetailsId      string
		step              woPb.OnboardingStep
		newOnbStepDetails *woPb.OnboardingStepDetails
	}
	tests := []struct {
		name            string
		args            args
		wantStepDetails *woPb.OnboardingStepDetails
		wantIsCreated   bool
		wantErr         bool
	}{
		{
			name: "create if not present",
			args: args{
				ctx:               context.Background(),
				onbDetailsId:      onboardingStepDetailsSample1.GetOnboardingDetailsId(),
				step:              onboardingStepDetailsSample1.GetStep(),
				newOnbStepDetails: onboardingStepDetailsSample1,
			},
			wantStepDetails: onboardingStepDetailsSample1,
			wantIsCreated:   true,
		},
		{
			name: "get if present",
			args: args{
				ctx:               context.Background(),
				onbDetailsId:      onboardingStepDetailsSample1.GetOnboardingDetailsId(),
				step:              onboardingStepDetailsSample1.GetStep(),
				newOnbStepDetails: onboardingStepDetailsSample1,
			},
			wantStepDetails: onboardingStepDetailsSample1,
			wantIsCreated:   false,
		},
	}
	testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CrdbOnboardingStepDetailsDao{
				db: daoTestSuite.db,
			}
			got, got1, err := c.GetOrCreate(tt.args.ctx, tt.args.onbDetailsId, tt.args.step, tt.args.newOnbStepDetails)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOrCreate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&woPb.OnboardingStepDetails{}, "created_at", "updated_at", "completed_at", "id", "staled_at", "expected_retry_at"),
			}
			if diff := cmp.Diff(tt.wantStepDetails, got, opts...); diff != "" {
				t.Errorf("GetOrCreate() mismatch (-want +got):\n%s", diff)
			}
			if got1 != tt.wantIsCreated {
				t.Errorf("GetOrCreate() got1 = %v, want %v", got1, tt.wantIsCreated)
			}
		})
	}
}
