package model

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"strconv"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/nulltypes"
)

type VendorRequest struct {

	// Primary identifier to vendor_responses table.
	Id string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`

	// actor_id associated with the request
	ActorId string

	// denotes the vendor serving the api call
	Vendor commonvgpb.Vendor

	// denotes vendor response code
	ResponseCode nulltypes.NullString

	// denotes received rpc status code(not applicable to callback)
	RpcStatusCode string

	// denotes received response description from vendor
	RawResponse nulltypes.NullString

	// denotes what the API does(eg customer creation, account creation)
	Api woPb.Api

	// requestId of the api call used to identify a request
	RequestId nulltypes.NullString

	// traceId of the request
	TraceId nulltypes.NullString

	// request description
	RawRequest string

	// Deleted time
	DeletedAt *time.Time

	// created at
	CreatedAt time.Time

	// updated at
	UpdatedAt time.Time
}

func NewVendorRequest(req *woPb.VendorRequest) *VendorRequest {
	res := &VendorRequest{
		ActorId:       req.ActorId,
		Vendor:        req.Vendor,
		ResponseCode:  nulltypes.NewNullString(req.ResponseCode),
		RpcStatusCode: strconv.Itoa(int(req.RpcStatusCode)),
		RawResponse:   nulltypes.NewNullString(req.RawResponse),
		Api:           req.Api,
		RequestId:     nulltypes.NewNullString(req.RequestId),
		TraceId:       nulltypes.NewNullString(req.TraceId),
		RawRequest:    req.RawRequest,
		Id:            req.GetId(),
		CreatedAt:     req.CreatedAt.AsTime(),
		UpdatedAt:     req.UpdatedAt.AsTime(),
	}
	return res
}

func (v *VendorRequest) GetProto() (*woPb.VendorRequest, error) {
	rpcCodeInt, err := strconv.Atoi(v.RpcStatusCode)
	if err != nil {
		return nil, err
	}
	res := &woPb.VendorRequest{
		ActorId:       v.ActorId,
		Vendor:        v.Vendor,
		ResponseCode:  v.ResponseCode.GetValue(),
		RpcStatusCode: uint32(rpcCodeInt),
		RawResponse:   v.RawResponse.GetValue(),
		Api:           v.Api,
		RequestId:     v.RequestId.GetValue(),
		TraceId:       v.TraceId.GetValue(),
		RawRequest:    v.RawRequest,
		Id:            v.Id,
		CreatedAt:     timestamppb.New(v.CreatedAt),
		UpdatedAt:     timestamppb.New(v.UpdatedAt),
	}
	return res, nil
}
