package model

import (
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	userPb "github.com/epifi/gamma/api/wealthonboarding/user"
)

type User struct {
	Id              string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`
	ActorId         string
	SchemeCode      userPb.SchemeCode
	Metadata        *userPb.UserMetadata
	PersonalDetails *userPb.PersonalDetails
	KycData         *userPb.KycData
	CreatedAt       *time.Time
	UpdatedAt       *time.Time
}

func NewUser(user *userPb.User) *User {
	return &User{
		Id:              user.GetId(),
		ActorId:         user.GetActorId(),
		SchemeCode:      user.GetSchemeCode(),
		Metadata:        user.GetMetadata(),
		PersonalDetails: user.GetPersonalDetails(),
		KycData:         user.GetKycData(),
	}
}

func (u *User) GetProto() *userPb.User {
	return &userPb.User{
		Id:              u.Id,
		ActorId:         u.ActorId,
		SchemeCode:      u.SchemeCode,
		Metadata:        u.Metadata,
		PersonalDetails: u.PersonalDetails,
		KycData:         u.KycData,
		CreatedAt:       timestampPb.New(*u.CreatedAt),
		UpdatedAt:       timestampPb.New(*u.UpdatedAt),
	}
}
