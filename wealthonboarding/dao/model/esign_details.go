package model

import (
	"database/sql"
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/nulltypes"
)

type ESignDetails struct {
	// An internal unique identifier of a transaction started with e-sign vendor to sign a document
	Id string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`

	// Vendor-provided unique identifier of a transaction started to e-sign a document.
	// This can be later used to query the latest status from the vendor or download the signed document.
	// Note: This field is currently used for Aadhaar-OTP based e-sign vendors (MANCH) only.
	TxnId string

	// Vendor-provided unique identifier of a transaction started to e-sign a document.
	// This can be later used to query the latest status from the vendor or download the signed document.
	// Note: This field is currently configured for one of the e-sign vendors (DIGIO) only and not used at all.
	DocId nulltypes.NullString

	// URL generated by the vendor to e-sign a document.
	// A client/user has to follow the instructions provided on the corresponding web page, e.g.,
	// entering <PERSON><PERSON><PERSON><PERSON> number and verifying the number via OTP received on the phone to complete the e-sign process.
	SignUrl nulltypes.NullString

	// Status of an e-sign transaction
	TxnState wealthonboarding.TxnState

	// S3 path of the signed document provided by e-sign vendor after a user has completed the e-sign process
	// by visiting vendor-provided signing URL.
	StoredS3Path nulltypes.NullString

	// Vendor used for e-signing document
	Vendor wealthonboarding.Vendor

	// Additional metadata info as per vendor
	Metadata *wealthonboarding.Metadata

	// Time at which the document was e-signed
	SignedAt sql.NullTime

	CreatedAt time.Time
	UpdatedAt time.Time
}

func NewESignDetails(eSignDetails *wealthonboarding.ESignDetails) *ESignDetails {
	eSignModel := &ESignDetails{
		TxnId:        eSignDetails.GetTxnId(),
		SignUrl:      nulltypes.NewNullString(eSignDetails.GetSignUrl()),
		DocId:        nulltypes.NewNullString(eSignDetails.GetDocId()),
		TxnState:     eSignDetails.GetTxnState(),
		StoredS3Path: nulltypes.NewNullString(eSignDetails.GetStoredS3Path()),
		Vendor:       eSignDetails.GetVendor(),
		Metadata:     eSignDetails.GetMetadata(),
	}
	if eSignDetails.GetCreatedAt().IsValid() {
		eSignModel.CreatedAt = eSignDetails.GetCreatedAt().AsTime()
	}
	if eSignDetails.GetUpdatedAt().IsValid() {
		eSignModel.UpdatedAt = eSignDetails.GetUpdatedAt().AsTime()
	}
	if eSignDetails.GetSignedAt().IsValid() {
		eSignModel.SignedAt = sql.NullTime{Valid: true, Time: eSignDetails.GetSignedAt().AsTime()}
	}
	return eSignModel
}

func (e *ESignDetails) GetProto() *wealthonboarding.ESignDetails {
	eSignDetails := &wealthonboarding.ESignDetails{
		Id:           e.Id,
		TxnId:        e.TxnId,
		SignUrl:      e.SignUrl.GetValue(),
		DocId:        e.DocId.GetValue(),
		TxnState:     e.TxnState,
		StoredS3Path: e.StoredS3Path.GetValue(),
		Vendor:       e.Vendor,
		Metadata:     e.Metadata,
		CreatedAt:    timestampPb.New(e.CreatedAt),
		UpdatedAt:    timestampPb.New(e.UpdatedAt),
	}
	if e.SignedAt.Valid {
		eSignDetails.SignedAt = timestampPb.New(e.SignedAt.Time)
	}
	return eSignDetails
}
