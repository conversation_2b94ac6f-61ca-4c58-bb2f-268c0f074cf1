package model

import (
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woDbPb "github.com/epifi/gamma/api/wealthonboarding/db"
)

type OnboardingDetails struct {
	Id                   string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`
	ActorId              string
	PersonalDetails      *woDbPb.PersonalDetails    `gorm:"type:jsonb;default:'null'"`
	NsdlData             *woPb.NsdlData             `gorm:"type:jsonb;default:'null'"`
	KraData              *woDbPb.KraData            `gorm:"type:jsonb;default:'null'"`
	CkycData             *woPb.CkycData             `gorm:"type:jsonb;default:'null'"`
	CustomerProvidedData *woPb.CustomerProvidedData `gorm:"type:jsonb;default:'null'"`
	MatchData            *woDbPb.MatchData          `gorm:"type:jsonb;default:'null'"`
	Metadata             *woDbPb.OnboardingMetadata `gorm:"type:jsonb;default:'null'"`
	DigilockerData       *woPb.DigilockerData       `gorm:"type:jsonb;default:'null'"`
	Status               woPb.OnboardingStatus
	CurrentStep          woPb.OnboardingStep
	OnboardingType       woPb.OnboardingType
	CreatedAt            *time.Time
	UpdatedAt            *time.Time
	CompletedAt          *time.Time
	CurrentWealthFlow    woPb.WealthFlow
	AgentProvidedData    *woPb.AgentProvidedData
}

//nolint:funlen
func NewOnboardingDetails(details *woPb.OnboardingDetails) *OnboardingDetails {
	var completedAtTime *time.Time
	if details.GetCompletedAt() != nil {
		v := details.GetCompletedAt().AsTime()
		completedAtTime = &v
	}
	res := &OnboardingDetails{
		Id:                   details.GetId(),
		ActorId:              details.GetActorId(),
		NsdlData:             details.GetMetadata().GetNsdlData(),
		CkycData:             details.GetMetadata().GetCkycData(),
		CustomerProvidedData: details.GetMetadata().GetCustomerProvidedData(),
		MatchData: &woDbPb.MatchData{
			FaceMatchData:        details.GetMetadata().GetFaceMatchData(),
			NameMatchInfo:        details.GetMetadata().GetNameMatchInfo(),
			LivenessData:         details.GetMetadata().GetLivenessData(),
			ManualReviewAttempts: details.GetMetadata().GetManualReviewAttempts(),
		},
		Metadata: &woDbPb.OnboardingMetadata{
			AgreementDocketInfo:            details.GetMetadata().GetAgreementDocketInfo(),
			IsFreshKra:                     details.GetMetadata().GetIsFreshKra(),
			CustomerIpAddress:              details.GetMetadata().GetCustomerIpAddress(),
			PanValidateAttemptsCount:       details.GetMetadata().GetPanValidateAttemptsCount(),
			IsInvestmentRiskSurveyComplete: details.GetMetadata().GetIsInvestmentRiskSurveyComplete(),
		},
		DigilockerData:    details.GetMetadata().GetDigilockerData(),
		Status:            details.GetStatus(),
		CurrentStep:       details.GetCurrentStep(),
		OnboardingType:    details.GetOnboardingType(),
		CompletedAt:       completedAtTime,
		CurrentWealthFlow: details.GetCurrentWealthFlow(),
		AgentProvidedData: details.GetAgentProvidedData(),
	}
	if details.GetMetadata().GetPersonalDetails() != nil {
		if res.PersonalDetails == nil {
			res.PersonalDetails = &woDbPb.PersonalDetails{}
		}
		res.PersonalDetails.Name = details.GetMetadata().GetPersonalDetails().GetName()
		res.PersonalDetails.Gender = details.GetMetadata().GetPersonalDetails().GetGender()
		res.PersonalDetails.PhoneNumber = details.GetMetadata().GetPersonalDetails().GetPhoneNumber()
		res.PersonalDetails.Email = details.GetMetadata().GetPersonalDetails().GetEmail()
		res.PersonalDetails.Dob = details.GetMetadata().GetPersonalDetails().GetDob()
		res.PersonalDetails.Photo = details.GetMetadata().GetPersonalDetails().GetPhoto()
		res.PersonalDetails.MaritalStatus = details.GetMetadata().GetPersonalDetails().GetMaritalStatus()
		res.PersonalDetails.FatherName = details.GetMetadata().GetPersonalDetails().GetFatherName()
		res.PersonalDetails.MotherName = details.GetMetadata().GetPersonalDetails().GetMotherName()
		res.PersonalDetails.Nominees = details.GetMetadata().GetPersonalDetails().GetNominees()
		res.PersonalDetails.Nationality = details.GetMetadata().GetPersonalDetails().GetNationality()
		res.PersonalDetails.ResidentialStatus = details.GetMetadata().GetPersonalDetails().GetResidentialStatus()
		res.PersonalDetails.PoliticallyExposedStatus = details.GetMetadata().GetPersonalDetails().GetPoliticallyExposedStatus()
		res.PersonalDetails.Signature = details.GetMetadata().GetPersonalDetails().GetSignature()
		res.PersonalDetails.Occupation = details.GetMetadata().GetPersonalDetails().GetOccupation()
		res.PersonalDetails.IncomeSlab = details.GetMetadata().GetPersonalDetails().GetIncomeSlab()
		res.PersonalDetails.PanDetails = details.GetMetadata().GetPanDetails()
		res.PersonalDetails.PoiDetails = details.GetMetadata().GetPoiDetails()
		res.PersonalDetails.PoaDetails = details.GetMetadata().GetPoaDetails()
		res.PersonalDetails.EmploymentData = details.GetMetadata().GetEmploymentData()
		res.PersonalDetails.BankDetails = details.GetMetadata().GetBankDetails()
		res.PersonalDetails.PoaWithOcr = details.GetMetadata().GetPoaWithOcr()
		res.PersonalDetails.NomineeDeclarationDetails = details.GetMetadata().GetPersonalDetails().GetNomineeDeclarationDetails()
		res.PersonalDetails.KraDocketPan = details.GetMetadata().GetPersonalDetails().GetKraDocketPan()
		res.PersonalDetails.CkycPan = details.GetMetadata().GetPersonalDetails().GetCkycPan()
		res.PersonalDetails.AdvisoryAgreementDetails = details.GetMetadata().GetPersonalDetails().GetAdvisoryAgreementDetails()
	}
	if details.GetMetadata().GetKraData() != nil {
		if res.KraData == nil {
			res.KraData = &woDbPb.KraData{}
		}
		res.KraData.StatusData = details.GetMetadata().GetKraData().GetStatusData()
		res.KraData.DownloadedData = details.GetMetadata().GetKraData().GetDownloadedData()
		res.KraData.UploadKraDocData = details.GetMetadata().GetUploadKraDocData()
		res.KraData.DownloadedKraDocData = details.GetMetadata().GetDownloadedKraDocData()
		res.KraData.KraDocketInfo = details.GetMetadata().GetKraDocketInfo()
	}
	return res
}

//nolint:funlen
func (o *OnboardingDetails) GetProto() *woPb.OnboardingDetails {
	od := &woPb.OnboardingDetails{
		Id:      o.Id,
		ActorId: o.ActorId,
		Metadata: &woPb.OnboardingMetadata{
			PanDetails:                     o.PersonalDetails.GetPanDetails(),
			PoiDetails:                     o.PersonalDetails.GetPoiDetails(),
			PoaDetails:                     o.PersonalDetails.GetPoaDetails(),
			FaceMatchData:                  o.MatchData.GetFaceMatchData(),
			NsdlData:                       o.NsdlData,
			CkycData:                       o.CkycData,
			NameMatchInfo:                  o.MatchData.GetNameMatchInfo(),
			KraDocketInfo:                  o.KraData.GetKraDocketInfo(),
			AgreementDocketInfo:            o.Metadata.GetAgreementDocketInfo(),
			CustomerProvidedData:           o.CustomerProvidedData,
			LivenessData:                   o.MatchData.GetLivenessData(),
			IsFreshKra:                     o.Metadata.GetIsFreshKra(),
			CustomerIpAddress:              o.Metadata.GetCustomerIpAddress(),
			EmploymentData:                 o.PersonalDetails.GetEmploymentData(),
			UploadKraDocData:               o.KraData.GetUploadKraDocData(),
			DownloadedKraDocData:           o.KraData.GetDownloadedKraDocData(),
			PoaWithOcr:                     o.PersonalDetails.GetPoaWithOcr(),
			ManualReviewAttempts:           o.MatchData.GetManualReviewAttempts(),
			DigilockerData:                 o.DigilockerData,
			PanValidateAttemptsCount:       o.Metadata.GetPanValidateAttemptsCount(),
			IsInvestmentRiskSurveyComplete: o.Metadata.GetIsInvestmentRiskSurveyComplete(),
		},
		Status:            o.Status,
		CurrentStep:       o.CurrentStep,
		OnboardingType:    o.OnboardingType,
		CreatedAt:         timestampPb.New(*o.CreatedAt),
		UpdatedAt:         timestampPb.New(*o.UpdatedAt),
		CurrentWealthFlow: o.CurrentWealthFlow,
		AgentProvidedData: o.AgentProvidedData,
	}
	if o.KraData != nil {
		od.GetMetadata().KraData = &woPb.KraData{
			StatusData:     o.KraData.GetStatusData(),
			DownloadedData: o.KraData.GetDownloadedData(),
		}
	}
	if o.PersonalDetails != nil {
		od.GetMetadata().PersonalDetails = &woPb.PersonalDetails{
			Name:                      o.PersonalDetails.GetName(),
			Gender:                    o.PersonalDetails.GetGender(),
			PhoneNumber:               o.PersonalDetails.GetPhoneNumber(),
			Email:                     o.PersonalDetails.GetEmail(),
			Dob:                       o.PersonalDetails.GetDob(),
			Photo:                     o.PersonalDetails.GetPhoto(),
			MaritalStatus:             o.PersonalDetails.GetMaritalStatus(),
			FatherName:                o.PersonalDetails.GetFatherName(),
			MotherName:                o.PersonalDetails.GetMotherName(),
			Nominees:                  o.PersonalDetails.GetNominees(),
			Nationality:               o.PersonalDetails.GetNationality(),
			ResidentialStatus:         o.PersonalDetails.GetResidentialStatus(),
			PoliticallyExposedStatus:  o.PersonalDetails.GetPoliticallyExposedStatus(),
			Signature:                 o.PersonalDetails.GetSignature(),
			Occupation:                o.PersonalDetails.GetOccupation(),
			IncomeSlab:                o.PersonalDetails.GetIncomeSlab(),
			NomineeDeclarationDetails: o.PersonalDetails.GetNomineeDeclarationDetails(),
			KraDocketPan:              o.PersonalDetails.GetKraDocketPan(),
			CkycPan:                   o.PersonalDetails.GetCkycPan(),
			AdvisoryAgreementDetails:  o.PersonalDetails.GetAdvisoryAgreementDetails(),
		}
		od.GetMetadata().BankDetails = o.PersonalDetails.BankDetails
	}
	if o.CompletedAt != nil {
		od.CompletedAt = timestampPb.New(*o.CompletedAt)
	}
	return od
}
