package model

import (
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
)

type OnboardingStepDetails struct {
	Id                  string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`
	OnboardingDetailsId string `gorm:"type:uuid"`
	Step                woPb.OnboardingStep
	Status              woPb.OnboardingStepStatus
	SubStatus           woPb.OnboardingStepSubStatus
	Metadata            *woPb.OnboardingStepMetadata `gorm:"type:jsonb;default:'null'"`
	CreatedAt           *time.Time
	UpdatedAt           *time.Time
	CompletedAt         *time.Time
	StaledAt            *time.Time
	ExpectedRetryAt     *time.Time
	NumAttempts         int32
}

func NewOnboardingStepDetails(details *woPb.OnboardingStepDetails) *OnboardingStepDetails {
	var completedAtTime *time.Time
	if details.CompletedAt != nil {
		v := details.CompletedAt.AsTime()
		completedAtTime = &v
	}
	var slatedAtTime *time.Time
	if details.GetStaledAt() != nil {
		v := details.GetStaledAt().AsTime()
		slatedAtTime = &v
	}
	var expectedRetryAt *time.Time
	if details.GetExpectedRetryAt() != nil {
		v := details.GetExpectedRetryAt().AsTime()
		expectedRetryAt = &v
	}
	return &OnboardingStepDetails{
		Id:                  details.GetId(),
		OnboardingDetailsId: details.GetOnboardingDetailsId(),
		Status:              details.GetStatus(),
		SubStatus:           details.GetSubStatus(),
		Step:                details.GetStep(),
		Metadata:            details.GetMetadata(),
		CompletedAt:         completedAtTime,
		StaledAt:            slatedAtTime,
		ExpectedRetryAt:     expectedRetryAt,
		NumAttempts:         details.GetNumAttempts(),
	}
}

func (o *OnboardingStepDetails) GetProto() *woPb.OnboardingStepDetails {
	osd := &woPb.OnboardingStepDetails{
		Id:                  o.Id,
		OnboardingDetailsId: o.OnboardingDetailsId,
		Metadata:            o.Metadata,
		Status:              o.Status,
		SubStatus:           o.SubStatus,
		Step:                o.Step,
		CreatedAt:           timestampPb.New(*o.CreatedAt),
		UpdatedAt:           timestampPb.New(*o.UpdatedAt),
		NumAttempts:         o.NumAttempts,
	}
	if o.CompletedAt != nil {
		osd.CompletedAt = timestampPb.New(*o.CompletedAt)
	}
	if o.StaledAt != nil {
		osd.StaledAt = timestampPb.New(*o.StaledAt)
	}
	if o.ExpectedRetryAt != nil {
		osd.ExpectedRetryAt = timestampPb.New(*o.ExpectedRetryAt)
	}
	return osd
}
