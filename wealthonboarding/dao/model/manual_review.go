package model

import (
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
)

type ManualReview struct {
	Id         string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`
	Status     woPb.ReviewStatus
	Payload    *woPb.ReviewPayload
	ItemType   woPb.ItemType
	ReviewedAt *time.Time
}

func NewManualReview(review *woPb.ManualReview) *ManualReview {
	reviewAt := review.GetReviewedAt().AsTime()
	mReview := &ManualReview{
		Status:   review.GetReviewStatus(),
		Payload:  review.GetReviewPayload(),
		ItemType: review.GetItemType(),
	}
	if review.GetReviewedAt() != nil {
		mReview.ReviewedAt = &reviewAt
	}
	return mReview
}

func (m *ManualReview) GetProto() *woPb.ManualReview {
	mr := &woPb.ManualReview{
		Id:            m.Id,
		ReviewStatus:  m.Status,
		ReviewPayload: m.Payload,
		ItemType:      m.ItemType,
	}
	if m.ReviewedAt != nil {
		mr.ReviewedAt = timestampPb.New(*m.ReviewedAt)
	}
	return mr
}
