package model

import (
	"time"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/pagination"
)

type OnboardingSummary struct {
	ActorId string

	OnboardingDetailsId string
	CurrentStep         woPb.OnboardingStep

	Status         woPb.OnboardingStatus
	OnboardingType woPb.OnboardingType
	CreatedAt      time.Time
	UpdatedAt      time.Time

	// Current step's details
	StepDetailsId string
	StepStatus    woPb.OnboardingStepStatus
	StepSubStatus woPb.OnboardingStepSubStatus
	StepCreatedAt time.Time
}

type OnboardingSummaries []*OnboardingSummary

func (s OnboardingSummaries) Slice(start, end int) pagination.Rows { return s[start:end] }

func (s OnboardingSummaries) GetTimestamp(index int) time.Time {
	return s[index].UpdatedAt
}

func (s OnboardingSummaries) Size() int { return len(s) }
