package model

import (
	"time"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woCkycPb "github.com/epifi/gamma/api/wealthonboarding/ckyc"
	"github.com/epifi/be-common/pkg/datetime"
)

type CkycData struct {
	Id            string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`
	ActorId       string
	CkycNo        string
	KycDate       *time.Time
	LastUpdatedAt *time.Time
	AccountType   woPb.CkycAccountType
	Type          woCkycPb.CkycDataType
	RawPayload    *woCkycPb.CkycPayload
	CreatedAt     *time.Time
	UpdatedAt     *time.Time
}

func NewCkycData(ckycData *woCkycPb.CkycData) *CkycData {
	ckycModel := &CkycData{
		ActorId:     ckycData.GetActorId(),
		CkycNo:      ckycData.GetCkycNo(),
		AccountType: ckycData.GetAccountType(),
		Type:        ckycData.GetCkycDataType(),
		RawPayload:  ckycData.GetCkycPayload(),
	}
	if ckycData.GetKycDate() != nil {
		ckycModel.KycDate = datetime.DateToTime(ckycData.GetKycDate(), datetime.IST)
	}
	if ckycData.GetUpdatedDate() != nil {
		ckycModel.LastUpdatedAt = datetime.DateToTime(ckycData.GetUpdatedDate(), datetime.IST)
	}
	return ckycModel
}

func (c *CkycData) GetProto() *woCkycPb.CkycData {
	ckycData := &woCkycPb.CkycData{
		CkycNo:       c.CkycNo,
		AccountType:  c.AccountType,
		ActorId:      c.ActorId,
		CkycDataType: c.Type,
		CkycPayload:  c.RawPayload,
	}
	if c.KycDate != nil {
		ckycData.KycDate = datetime.TimeToDateInLoc(*c.KycDate, datetime.IST)
	}
	if c.LastUpdatedAt != nil {
		ckycData.UpdatedDate = datetime.TimeToDateInLoc(*c.LastUpdatedAt, datetime.IST)
	}
	return ckycData
}
