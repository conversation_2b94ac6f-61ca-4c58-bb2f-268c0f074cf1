package model

import (
	"fmt"
	"reflect"
	"testing"

	"github.com/stretchr/testify/require"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
)

// make sure to add a new field in both VendorRequest proto and VendorRequest gorm model,
// since vendor requests are written in S3 using proto and crdb using gorm model
func TestVendorRequestModelFields(t *testing.T) {
	a := require.New(t)

	model := VendorRequest{}
	pb := woPb.VendorRequest{}

	typeOfModel := reflect.TypeOf(model) // nolint:govet
	typeOfPb := reflect.TypeOf(pb)       // nolint:govet
	for fieldIdx := 0; fieldIdx < typeOfModel.NumField(); fieldIdx++ {
		fieldName := typeOfModel.Field(fieldIdx).Name
		_, isFound := typeOfPb.FieldByName(fieldName)
		a.True(isFound, fmt.Sprintf("%s field found in VendorRequst model but not in VendorRequest proto. Add new fields in both proto and model", fieldName))
	}
}
