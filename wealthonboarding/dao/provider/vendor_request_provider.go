package provider

import (
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/dao/impl"
)

func ProvideVendorRequestStorageImpl(vendorRequestDaoImpl *impl.VendorRequestDaoImpl, vendorRequestProducerImpl *impl.VendorRequestProducerImpl, vendorRequestDaoAndProducerImpl *impl.VendorRequestDaoAndProducerImpl, conf *config.Config) dao.VendorRequestDao {
	if conf.Flags.VendorRequestStore == "kinesis" {
		return vendorRequestProducerImpl
	} else if conf.Flags.VendorRequestStore == "crdb" {
		return vendorRequestDaoImpl
	}
	return vendorRequestDaoAndProducerImpl
}
