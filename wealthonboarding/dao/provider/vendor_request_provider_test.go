package provider

import (
	"reflect"
	"testing"

	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/dao/impl"
)

func TestProvideVendorRequestStorageImpl(t *testing.T) {
	type args struct {
		vendorRequestDaoImpl            *impl.VendorRequestDaoImpl
		vendorRequestProducerImpl       *impl.VendorRequestProducerImpl
		vendorRequestDaoAndProducerImpl *impl.VendorRequestDaoAndProducerImpl
		conf                            *config.Config
	}
	tests := []struct {
		name string
		args args
		want dao.VendorRequestDao
	}{
		{
			name: "store in crdb",
			args: args{
				vendorRequestDaoImpl: &impl.VendorRequestDaoImpl{},
				conf: &config.Config{Flags: &config.Flags{
					VendorRequestStore: "crdb",
				}},
			},
			want: &impl.VendorRequestDaoImpl{},
		},
		{
			name: "store in kinesis",
			args: args{
				vendorRequestProducerImpl: &impl.VendorRequestProducerImpl{},
				conf: &config.Config{Flags: &config.Flags{
					VendorRequestStore: "kinesis",
				}},
			},
			want: &impl.VendorRequestProducerImpl{},
		},
		{
			name: "store in both crdb and kinesis",
			args: args{
				vendorRequestDaoAndProducerImpl: &impl.VendorRequestDaoAndProducerImpl{},
				conf: &config.Config{Flags: &config.Flags{
					VendorRequestStore: "crdb_and_kinesis",
				}},
			},
			want: &impl.VendorRequestDaoAndProducerImpl{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ProvideVendorRequestStorageImpl(tt.args.vendorRequestDaoImpl, tt.args.vendorRequestProducerImpl, tt.args.vendorRequestDaoAndProducerImpl, tt.args.conf); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProvideVendorRequestStorageImpl() = %v, want %v", got, tt.want)
			}
		})
	}
}
