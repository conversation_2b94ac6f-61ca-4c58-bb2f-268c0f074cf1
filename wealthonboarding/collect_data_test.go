package wealthonboarding

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"encoding/base64"
	"fmt"
	"io"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	rmsManagerPb "github.com/epifi/gamma/api/rms/manager"
	rmsManagerMocks "github.com/epifi/gamma/api/rms/manager/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	eventMock "github.com/epifi/be-common/pkg/events/mocks"
	woErr "github.com/epifi/gamma/wealthonboarding/errors"
	mockDao "github.com/epifi/gamma/wealthonboarding/test/mocks/dao"
	mockHelper "github.com/epifi/gamma/wealthonboarding/test/mocks/helpers"
	mocks "github.com/epifi/gamma/wealthonboarding/test/mocks/ocr"
)

func TestService_CollectDataFromCustomerWithStream(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockOnbDetailsDao := mockDao.NewMockOnboardingDetailsDao(ctrl)
	mockDocHelper := mockHelper.NewMockDocumentHelper(ctrl)
	mockOCRHelper := mocks.NewMockIOcrHelper(ctrl)
	mockEventBroker := eventMock.NewMockBroker(ctrl)
	mockEventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()

	samplePan := &types.DocumentProof{
		ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
		Photo:     []*commontypes.Image{{ImageType: commontypes.ImageType_JPEG, ImageDataBase64: string(base64.StdEncoding.EncodeToString([]byte{1, 2, 3, 100, 101, 102}))}}}
	sampleBasicCustomerInfo := &woPb.BasicCustomerInfo{
		ActorId:        "randomActorId",
		OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
		ProofType:      samplePan.GetProofType(),
		ImgType:        samplePan.GetPhoto()[0].GetImageType(),
	}
	samplePanImgChunks := [][]byte{{1, 2, 3}, {100, 101, 102}}
	sampleSuccessRes := &woPb.CollectDataFromCustomerWithStreamResponse{Status: rpc.StatusOk()}
	sampleBadReqRes := &woPb.CollectDataFromCustomerWithStreamResponse{Status: rpc.StatusFailedPrecondition()}

	tests := []struct {
		name       string
		stream     woPb.WealthOnboarding_CollectDataFromCustomerWithStreamServer
		setupMocks func()
		wantErr    bool
	}{
		{
			name:   "receive minimum info for pan update in db",
			stream: newMockCollectDataStreamServer(context.Background(), sampleBasicCustomerInfo, samplePanImgChunks, sampleSuccessRes),
			setupMocks: func() {
				mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(&woPb.OnboardingDetails{}, nil)
				mockDocHelper.EXPECT().UploadDoc(gomock.Any(), gomock.Any(), samplePan).Return(nil, nil)
				mockOCRHelper.EXPECT().FetchPanWithOcr(gomock.Any(), gomock.Any(), gomock.Any()).Return(&woPb.OcrDocumentProof{Doc: &types.DocumentProof{}}, nil)
				mockOnbDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
			wantErr: false,
		},
		{
			name:   "send error response and close stream if error in processing data",
			stream: newMockCollectDataStreamServer(context.Background(), sampleBasicCustomerInfo, samplePanImgChunks, sampleBadReqRes),
			setupMocks: func() {
				mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(&woPb.OnboardingDetails{}, nil)
				mockDocHelper.EXPECT().UploadDoc(gomock.Any(), gomock.Any(), samplePan).Return(nil, nil)
				mockOCRHelper.EXPECT().FetchPanWithOcr(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, woErr.ErrOCRLowConfidence)
				mockOnbDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				onboardingDetailsDao: mockOnbDetailsDao,
				eventBroker:          mockEventBroker,
				docHelper:            mockDocHelper,
				ocrHelper:            mockOCRHelper,
				conf:                 conf,
			}
			tt.setupMocks()
			if err := s.CollectDataFromCustomerWithStream(tt.stream); (err != nil) != tt.wantErr {
				t.Errorf("CollectDataFromCustomerWithStream() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

type mockCollectDataStreamServer struct {
	grpc.ServerStream
	ctx                        context.Context
	basicInfo                  *woPb.BasicCustomerInfo
	panImgChunks               [][]byte
	currentBasicInfoChunkCount int
	currentPanImgChunkCount    int
	wantStreamCloseRes         *woPb.CollectDataFromCustomerWithStreamResponse
}

func newMockCollectDataStreamServer(
	ctx context.Context,
	basicInfo *woPb.BasicCustomerInfo,
	panImgChunks [][]byte,
	wantStreamCloseRes *woPb.CollectDataFromCustomerWithStreamResponse,
) *mockCollectDataStreamServer {
	return &mockCollectDataStreamServer{
		ctx:                ctx,
		basicInfo:          basicInfo,
		panImgChunks:       panImgChunks,
		wantStreamCloseRes: wantStreamCloseRes,
	}
}

func (s *mockCollectDataStreamServer) Context() context.Context {
	return s.ctx
}

func (s *mockCollectDataStreamServer) SendAndClose(res *woPb.CollectDataFromCustomerWithStreamResponse) error {
	if !reflect.DeepEqual(res, s.wantStreamCloseRes) {
		return fmt.Errorf("SendAndClose() got = %v, want %v", res, s.wantStreamCloseRes)
	}
	return nil
}

func (s *mockCollectDataStreamServer) Recv() (*woPb.CollectDataFromCustomerWithStreamRequest, error) {
	m := &woPb.CollectDataFromCustomerWithStreamRequest{}
	totalBasicInfoChunks := 0
	if s.basicInfo != nil {
		totalBasicInfoChunks++
	}
	currentChunkCount := s.currentBasicInfoChunkCount + s.currentPanImgChunkCount

	cumPanChunks := totalBasicInfoChunks + len(s.panImgChunks)
	// nolint: gocritic
	if currentChunkCount < totalBasicInfoChunks {
		m.CustomerData = &woPb.CollectDataFromCustomerWithStreamRequest_BasicInfo{BasicInfo: s.basicInfo}
		s.currentBasicInfoChunkCount++
	} else if currentChunkCount >= totalBasicInfoChunks && currentChunkCount < cumPanChunks {
		m.CustomerData = &woPb.CollectDataFromCustomerWithStreamRequest_PanImgChunk{PanImgChunk: s.panImgChunks[s.currentPanImgChunkCount]}
		s.currentPanImgChunkCount++
	} else {
		return nil, io.EOF
	}
	return m, nil
}

func TestService_CollectDataFromCustomer(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockOnbDetailsDao := mockDao.NewMockOnboardingDetailsDao(ctrl)
	mockDocHelper := mockHelper.NewMockDocumentHelper(ctrl)
	mockOCRHelper := mocks.NewMockIOcrHelper(ctrl)
	mockEventBroker := eventMock.NewMockBroker(ctrl)
	mockEventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUser := userMocks.NewMockUsersClient(ctrl)
	mockActor := actorMocks.NewMockActorClient(ctrl)
	mockRmsClient := rmsManagerMocks.NewMockRuleManagerClient(ctrl)

	type args struct {
		ctx context.Context
		req *woPb.CollectDataFromCustomerRequest
	}
	tests := []struct {
		name       string
		args       args
		want       *woPb.CollectDataFromCustomerResponse
		setupMocks func()
		wantErr    bool
	}{
		{
			name: "receive minimum info for pan update in db",
			args: args{
				ctx: context.Background(),
				req: &woPb.CollectDataFromCustomerRequest{
					ActorId:        "randomActorId",
					OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
					Pan: &types.DocumentProof{
						ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN,
						Photo:     []*commontypes.Image{{ImageType: commontypes.ImageType_JPEG, ImageDataBase64: string([]byte{1, 2, 3, 100, 101, 102})}},
					},
				},
			},
			setupMocks: func() {
				mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(&woPb.OnboardingDetails{}, nil)
				mockDocHelper.EXPECT().UploadDoc(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
				mockOCRHelper.EXPECT().FetchPanWithOcr(gomock.Any(), gomock.Any(), gomock.Any()).Return(&woPb.OcrDocumentProof{Doc: &types.DocumentProof{}}, nil)
				mockOnbDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				mockUser.EXPECT().GetNominees(gomock.Any(), gomock.Any()).Return(&userPb.GetNomineesResponse{
					Status:   rpc.StatusOk(),
					Nominees: []*types.Nominee{{Id: "randomNomineeId", Dob: timestamppb.New(time.Now().AddDate(-10, 0, 0))}},
				}, nil)
			},
			want: &woPb.CollectDataFromCustomerResponse{Status: rpc.StatusOk()},
		},
		{
			name: "collect nominee details when valid",
			args: args{
				ctx: context.Background(),
				req: &woPb.CollectDataFromCustomerRequest{
					ActorId:                   "randomActorId",
					OnboardingType:            woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
					NomineeDeclarationDetails: &woPb.NomineeDeclarationDetails{Choice: commontypes.BooleanEnum_FALSE},
				},
			},
			setupMocks: func() {
				mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(&woPb.OnboardingDetails{
					Metadata: &woPb.OnboardingMetadata{PersonalDetails: &woPb.PersonalDetails{Name: &commontypes.Name{FirstName: "Brijesh", LastName: "Sahoo"}}},
				}, nil)
				mockOnbDetailsDao.EXPECT().Update(gomock.Any(), &woPb.OnboardingDetails{Metadata: &woPb.OnboardingMetadata{
					PersonalDetails: &woPb.PersonalDetails{
						Name:                      &commontypes.Name{FirstName: "Brijesh", LastName: "Sahoo"},
						NomineeDeclarationDetails: &woPb.NomineeDeclarationDetails{Choice: commontypes.BooleanEnum_FALSE},
					},
					CustomerProvidedData: &woPb.CustomerProvidedData{NomineeDeclarationDetails: &woPb.NomineeDeclarationDetails{Choice: commontypes.BooleanEnum_FALSE}},
				}}, gomock.Any()).Return(nil)
				mockRmsClient.EXPECT().GetSubscriptionsByExecutionState(gomock.Any(), gomock.Any()).Return(&rmsManagerPb.GetSubscriptionsByExecutionStateResponse{Status: rpc.StatusOk()}, nil)
				mockUser.EXPECT().GetNominees(gomock.Any(), gomock.Any()).Return(&userPb.GetNomineesResponse{
					Status:   rpc.StatusOk(),
					Nominees: []*types.Nominee{{Id: "randomNomineeId", Dob: timestamppb.New(time.Now().AddDate(-30, 0, 0))}},
				}, nil)
			},
			want: &woPb.CollectDataFromCustomerResponse{Status: rpc.StatusOk()},
		},
		{
			name: "throw error when nominee share sum not 100",
			args: args{
				ctx: context.Background(),
				req: &woPb.CollectDataFromCustomerRequest{
					ActorId:        "randomActorId",
					OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
					NomineeDeclarationDetails: &woPb.NomineeDeclarationDetails{
						Choice:                commontypes.BooleanEnum_TRUE,
						WealthAccountNominees: []*woPb.WealthAccountNomineeInfo{{NomineeId: "randomNomineeId", PercentageShare: 50}}, // percentage sum not 100
					},
				},
			},
			setupMocks: func() {
				mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(&woPb.OnboardingDetails{
					Metadata: &woPb.OnboardingMetadata{PersonalDetails: &woPb.PersonalDetails{Name: &commontypes.Name{FirstName: "Brijesh", LastName: "Sahoo"}}},
				}, nil)
			},
			want: &woPb.CollectDataFromCustomerResponse{Status: rpc.StatusInternal()},
		},
		{
			name: "throw error when nominee is over 80 years old",
			args: args{
				ctx: context.Background(),
				req: &woPb.CollectDataFromCustomerRequest{
					ActorId:        "randomActorId",
					OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
					NomineeDeclarationDetails: &woPb.NomineeDeclarationDetails{
						Choice:                commontypes.BooleanEnum_TRUE,
						WealthAccountNominees: []*woPb.WealthAccountNomineeInfo{{NomineeId: "randomNomineeId", PercentageShare: 100}},
					},
				},
			},
			setupMocks: func() {
				mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(&woPb.OnboardingDetails{
					Metadata: &woPb.OnboardingMetadata{PersonalDetails: &woPb.PersonalDetails{Name: &commontypes.Name{FirstName: "Brijesh", LastName: "Sahoo"}}},
				}, nil)
				mockUser.EXPECT().GetNominees(gomock.Any(), gomock.Any()).Return(&userPb.GetNomineesResponse{
					Status:   rpc.StatusOk(),
					Nominees: []*types.Nominee{{Id: "randomNomineeId", Dob: timestamppb.New(time.Now().AddDate(-85, 0, 0))}},
				}, nil)
			},
			want: &woPb.CollectDataFromCustomerResponse{Status: rpc.StatusInternal()},
		},
		{
			name: "throw error when nominee name is junk",
			args: args{
				ctx: context.Background(),
				req: &woPb.CollectDataFromCustomerRequest{
					ActorId:        "randomActorId",
					OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
					NomineeDeclarationDetails: &woPb.NomineeDeclarationDetails{
						Choice:                commontypes.BooleanEnum_TRUE,
						WealthAccountNominees: []*woPb.WealthAccountNomineeInfo{{NomineeId: "randomNomineeId", PercentageShare: 100}},
					},
				},
			},
			setupMocks: func() {
				mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(&woPb.OnboardingDetails{
					Metadata: &woPb.OnboardingMetadata{PersonalDetails: &woPb.PersonalDetails{Name: &commontypes.Name{FirstName: "Brijesh", LastName: "Sahoo"}}},
				}, nil)
				mockUser.EXPECT().GetNominees(gomock.Any(), gomock.Any()).Return(&userPb.GetNomineesResponse{
					Status: rpc.StatusOk(),
					Nominees: []*types.Nominee{{
						Id:   "randomNomineeId",
						Name: "Amma",
						Dob:  timestamppb.New(time.Now().AddDate(-25, 0, 0)),
					}},
				}, nil)
			},
			want: &woPb.CollectDataFromCustomerResponse{Status: rpc.StatusInternal()},
		},
		{
			name: "throw error when nominee age under 18",
			args: args{
				ctx: context.Background(),
				req: &woPb.CollectDataFromCustomerRequest{
					ActorId:        "randomActorId",
					OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
					NomineeDeclarationDetails: &woPb.NomineeDeclarationDetails{
						Choice:                commontypes.BooleanEnum_TRUE,
						WealthAccountNominees: []*woPb.WealthAccountNomineeInfo{{NomineeId: "randomNomineeId", PercentageShare: 100}},
					},
				},
			},
			setupMocks: func() {
				mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(&woPb.OnboardingDetails{
					Metadata: &woPb.OnboardingMetadata{PersonalDetails: &woPb.PersonalDetails{Name: &commontypes.Name{FirstName: "Brijesh", LastName: "Sahoo"}}},
				}, nil)
				mockUser.EXPECT().GetNominees(gomock.Any(), gomock.Any()).Return(&userPb.GetNomineesResponse{
					Status:   rpc.StatusOk(),
					Nominees: []*types.Nominee{{Id: "randomNomineeId", Dob: timestamppb.New(time.Now().AddDate(-10, 0, 0))}},
				}, nil)
			},
			want: &woPb.CollectDataFromCustomerResponse{Status: rpc.StatusInternal()},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				onboardingDetailsDao:  mockOnbDetailsDao,
				eventBroker:           mockEventBroker,
				docHelper:             mockDocHelper,
				ocrHelper:             mockOCRHelper,
				userClient:            mockUser,
				actorClient:           mockActor,
				rmsRuleManagerClient:  mockRmsClient,
				idempotentTxnExecutor: idempotentTxnExecutor,
			}
			tt.setupMocks()
			got, err := s.CollectDataFromCustomer(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CollectDataFromCustomer() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CollectDataFromCustomer() got = %v, want %v", got, tt.want)
			}
		})
	}
}
