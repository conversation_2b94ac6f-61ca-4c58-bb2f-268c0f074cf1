{"StatusCodes": [{"Vendor": "FEDERAL", "RawStatusCode": "OBE0022", "StatusCode": "FI100", "StatusCodeDescriptionPayer": "Socket Exception", "StatusCodeDescriptionPayee": "Socket Exception", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F008", "StatusCode": "FI101", "StatusCodeDescriptionPayer": "Internal Service Down", "StatusCodeDescriptionPayee": "Internal Service Down", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F003", "StatusCode": "FI102", "StatusCodeDescriptionPayer": "This Service is not enabled for this Sender", "StatusCodeDescriptionPayee": "This Service is not enabled for this Sender", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F004", "StatusCode": "FI103", "StatusCodeDescriptionPayer": "Invalid username or password", "StatusCodeDescriptionPayee": "Invalid username or password", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F007", "StatusCode": "FI104", "StatusCodeDescriptionPayer": "Reference Id is required", "StatusCodeDescriptionPayee": "Reference Id is required", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F001", "StatusCode": "FI105", "StatusCodeDescriptionPayer": "Duplicate Transaction", "StatusCodeDescriptionPayee": "Duplicate Transaction", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F002", "StatusCode": "FI106", "StatusCodeDescriptionPayer": "Reference Id required", "StatusCodeDescriptionPayee": "Reference Id required", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F006", "StatusCode": "FI107", "StatusCodeDescriptionPayer": "Insufficient Previleges", "StatusCodeDescriptionPayee": "Insufficient Previleges", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F101", "StatusCode": "FI109", "StatusCodeDescriptionPayer": "Invalid Sender Credentials", "StatusCodeDescriptionPayee": "Invalid Sender Credentials", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F102", "StatusCode": "FI110", "StatusCodeDescriptionPayer": "<PERSON><PERSON><PERSON>", "StatusCodeDescriptionPayee": "<PERSON><PERSON><PERSON>", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F103", "StatusCode": "FI111", "StatusCodeDescriptionPayer": "Sender is not enabled for this request", "StatusCodeDescriptionPayee": "Sender is not enabled for this request", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F104", "StatusCode": "FI112", "StatusCodeDescriptionPayer": "Please enter valid amount", "StatusCodeDescriptionPayee": "Please enter valid amount", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F105", "StatusCode": "FI113", "StatusCodeDescriptionPayer": "Please enter valid values", "StatusCodeDescriptionPayee": "Please enter valid values", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F106", "StatusCode": "FI114", "StatusCodeDescriptionPayer": "Duplicate Request", "StatusCodeDescriptionPayee": "Duplicate Request", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F300", "StatusCode": "FI115", "StatusCodeDescriptionPayer": "CBS Connection Failure", "StatusCodeDescriptionPayee": "CBS Connection Failure", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F301", "StatusCode": "FI116", "StatusCodeDescriptionPayer": "CBS Response timeout", "StatusCodeDescriptionPayee": "CBS Response timeout", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F302", "StatusCode": "FI117", "StatusCodeDescriptionPayer": "CBS Response Invalid or Connection Failed after sending request", "StatusCodeDescriptionPayee": "CBS Response Invalid or Connection Failed after sending request", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F303", "StatusCode": "FI118", "StatusCodeDescriptionPayer": "CBS Response Got Mismatched", "StatusCodeDescriptionPayee": "CBS Response Got Mismatched", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "184", "StatusCode": "FI119", "StatusCodeDescriptionPayer": "Requested Block operation failed since Account is closed", "StatusCodeDescriptionPayee": "Requested Block operation failed since Account is closed", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "114", "StatusCode": "FI120", "StatusCodeDescriptionPayer": "Invalid account number", "StatusCodeDescriptionPayee": "Invalid account number", "DeclineType": "Business", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "115", "StatusCode": "FI121", "StatusCodeDescriptionPayer": "Function not supported", "StatusCodeDescriptionPayee": "Function not supported", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "111", "StatusCode": "FI122", "StatusCodeDescriptionPayer": "Invalid Scheme Type", "StatusCodeDescriptionPayee": "Invalid Scheme Type", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "116", "StatusCode": "FI123", "StatusCodeDescriptionPayer": "Insufficient funds in account", "StatusCodeDescriptionPayee": "Insufficient funds in account", "DeclineType": "Business", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "119", "StatusCode": "FI124", "StatusCodeDescriptionPayer": "Transaction not allowed on account (Remit<PERSON> or Beneficiary)", "StatusCodeDescriptionPayee": "Transaction not allowed on account (Remit<PERSON> or Beneficiary)", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "180", "StatusCode": "FI126", "StatusCodeDescriptionPayer": "Maximum Transaction Amount Limit Exceeded", "StatusCodeDescriptionPayee": "Maximum Transaction Amount Limit Exceeded", "DeclineType": "Business", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "185", "StatusCode": "FI127", "StatusCodeDescriptionPayer": "Transfer Amount Invalid", "StatusCodeDescriptionPayee": "Transfer Amount Invalid", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "902", "StatusCode": "FI128", "StatusCodeDescriptionPayer": "Please contact Admin", "StatusCodeDescriptionPayee": "Please contact Admin", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "904", "StatusCode": "FI129", "StatusCodeDescriptionPayer": "Please contact Admin", "StatusCodeDescriptionPayee": "Please contact Admin", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "906", "StatusCode": "FI130", "StatusCodeDescriptionPayer": "Cutover in progress", "StatusCodeDescriptionPayee": "Cutover in progress", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "907", "StatusCode": "FI131", "StatusCodeDescriptionPayer": "Host not available", "StatusCodeDescriptionPayee": "Host not available", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "908", "StatusCode": "FI132", "StatusCodeDescriptionPayer": "Host not available", "StatusCodeDescriptionPayee": "Host not available", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "909", "StatusCode": "FI133", "StatusCodeDescriptionPayer": "System Malfunction", "StatusCodeDescriptionPayee": "System Malfunction", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "911", "StatusCode": "FI134", "StatusCodeDescriptionPayer": "Host not available", "StatusCodeDescriptionPayee": "Host not available", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "913", "StatusCode": "FI135", "StatusCodeDescriptionPayer": "Duplicate transaction", "StatusCodeDescriptionPayee": "Duplicate transaction", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "186", "StatusCode": "FI136", "StatusCodeDescriptionPayer": "Block Amount not available", "StatusCodeDescriptionPayee": "Block Amount not available", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F", "StatusCode": "FI137", "StatusCodeDescriptionPayer": "Beneficiary account is blocked or frozen", "StatusCodeDescriptionPayee": "Beneficiary account is blocked or frozen", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "C", "StatusCode": "FI138", "StatusCodeDescriptionPayer": "Account Closed (Remitter or beneficiary)", "StatusCodeDescriptionPayee": "Account Closed (Remitter or beneficiary)", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "D", "StatusCode": "FI139", "StatusCodeDescriptionPayer": "Debit Frozen", "StatusCodeDescriptionPayee": "Debit Frozen", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "R", "StatusCode": "FI140", "StatusCodeDescriptionPayer": "Credit Frozen", "StatusCodeDescriptionPayee": "Credit Frozen", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "M", "StatusCode": "FI141", "StatusCodeDescriptionPayer": "<PERSON><PERSON><PERSON> Account", "StatusCodeDescriptionPayee": "<PERSON><PERSON><PERSON> Account", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "I", "StatusCode": "FI142", "StatusCodeDescriptionPayer": "Account Inactive", "StatusCodeDescriptionPayee": "Account Inactive", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "F108", "StatusCode": "FI143", "StatusCodeDescriptionPayer": "We are unable to process your transaction. Please try after some time.", "StatusCodeDescriptionPayee": "We are unable to process your transaction. Please try after some time.", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F109", "StatusCode": "FI144", "StatusCodeDescriptionPayer": "Server is down please try after some time", "StatusCodeDescriptionPayee": "Server is down please try after some time", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F107", "StatusCode": "FI145", "StatusCodeDescriptionPayer": "We are unable to process your transaction. Please try after some time.", "StatusCodeDescriptionPayee": "We are unable to process your transaction. Please try after some time.", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F110", "StatusCode": "FI146", "StatusCodeDescriptionPayer": "Server is down please try after some time", "StatusCodeDescriptionPayee": "Server is down please try after some time", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F304", "StatusCode": "FI147", "StatusCodeDescriptionPayer": "Your request is under processing , Please wait for some time", "StatusCodeDescriptionPayee": "Your request is under processing , Please wait for some time", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F999", "StatusCode": "FI148", "StatusCodeDescriptionPayer": "No Transaction Found", "StatusCodeDescriptionPayee": "No Transaction Found", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F121", "StatusCode": "FI150", "StatusCodeDescriptionPayer": "Original Transaction not found", "StatusCodeDescriptionPayee": "Original Transaction not found", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F310", "StatusCode": "FI151", "StatusCodeDescriptionPayer": "Reversal Fund Transfer CBS Connection Failure", "StatusCodeDescriptionPayee": "Reversal Fund Transfer CBS Connection Failure", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F311", "StatusCode": "FI152", "StatusCodeDescriptionPayer": "Reversal Fund Transfer CBS Response timeout", "StatusCodeDescriptionPayee": "Reversal Fund Transfer CBS Response timeout", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F312", "StatusCode": "FI153", "StatusCodeDescriptionPayer": "Reversal Fund Transfer CBS Response Invalid or Connection Failed after sending request", "StatusCodeDescriptionPayee": "Reversal Fund Transfer CBS Response Invalid or Connection Failed after sending request", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F313", "StatusCode": "FI154", "StatusCodeDescriptionPayer": "Reversal Fund Transfer CBS Response Got Mismatched", "StatusCodeDescriptionPayee": "Reversal Fund Transfer CBS Response Got Mismatched", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F314", "StatusCode": "FI155", "StatusCodeDescriptionPayer": "Reversal Fund Transfer CBS Response Invalid", "StatusCodeDescriptionPayee": "Reversal Fund Transfer CBS Response Invalid", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F017", "StatusCode": "FI156", "StatusCodeDescriptionPayer": "Account Number not registered for sender", "StatusCodeDescriptionPayee": "Account Number not registered for sender", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F018", "StatusCode": "FI157", "StatusCodeDescriptionPayer": "Maximum Transaction Amount Limit Exceeded", "StatusCodeDescriptionPayee": "Maximum Transaction Amount Limit Exceeded", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F021", "StatusCode": "FI158", "StatusCodeDescriptionPayer": "Invalid PurposeCode", "StatusCodeDescriptionPayee": "Invalid PurposeCode", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F005", "StatusCode": "FI159", "StatusCodeDescriptionPayer": "Database Error", "StatusCodeDescriptionPayee": "Database Error", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F1", "StatusCode": "FI161", "StatusCodeDescriptionPayer": "FAILURE", "StatusCodeDescriptionPayee": "FAILURE", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F9", "StatusCode": "FI162", "StatusCodeDescriptionPayer": "FAILURE", "StatusCodeDescriptionPayee": "FAILURE", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "XN", "StatusCode": "FI163", "StatusCodeDescriptionPayer": "Account not found (Remitter or beneficiary)", "StatusCodeDescriptionPayee": "Account not found (Remitter or beneficiary)", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "XC", "StatusCode": "FI164", "StatusCodeDescriptionPayer": "Account Closed (Remitter or beneficiary)", "StatusCodeDescriptionPayee": "Account Closed (Remitter or beneficiary)", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "XF", "StatusCode": "FI165", "StatusCodeDescriptionPayer": "Account Credit or total Freeze (Remitter or beneficiary)", "StatusCodeDescriptionPayee": "Account Credit or total Freeze (Remitter or beneficiary)", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "XD", "StatusCode": "FI166", "StatusCodeDescriptionPayer": "Account <PERSON> (Remitter or beneficiary)", "StatusCodeDescriptionPayee": "Account <PERSON> (Remitter or beneficiary)", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "XI", "StatusCode": "FI167", "StatusCodeDescriptionPayer": "Account Inactive (Remitter or beneficiary)", "StatusCodeDescriptionPayee": "Account Inactive (Remitter or beneficiary)", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "XL", "StatusCode": "FI168", "StatusCodeDescriptionPayer": "Account <PERSON><PERSON> (Remitter or beneficiary)", "StatusCodeDescriptionPayee": "Account <PERSON><PERSON> (Remitter or beneficiary)", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "XR", "StatusCode": "FI169", "StatusCodeDescriptionPayer": "Account NRE (Remitter or beneficiary)", "StatusCodeDescriptionPayee": "Account NRE (Remitter or beneficiary)", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "XG", "StatusCode": "FI170", "StatusCodeDescriptionPayer": "Account Normal (Remitter or beneficiary)", "StatusCodeDescriptionPayee": "Account Normal (Remitter or beneficiary)", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "XH", "StatusCode": "FI171", "StatusCodeDescriptionPayer": "Account Invalid Scheme (Remitter or beneficiary)", "StatusCodeDescriptionPayee": "Account Invalid Scheme (Remitter or beneficiary)", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "XRT", "StatusCode": "FI172", "StatusCodeDescriptionPayer": "Payment Processing Dont Retry", "StatusCodeDescriptionPayee": "Payment Processing Dont Retry", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X38", "StatusCode": "FI175", "StatusCodeDescriptionPayer": "NEFT Returned", "StatusCodeDescriptionPayee": "NEFT Returned", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F508", "StatusCode": "FI179", "StatusCodeDescriptionPayer": "Remitter Account number Invalid", "StatusCodeDescriptionPayee": "Remitter Account number Invalid", "DeclineType": "Business", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F115", "StatusCode": "FI180", "StatusCodeDescriptionPayer": "Remitter Name is Invalid", "StatusCodeDescriptionPayee": "Remitter Name is Invalid", "DeclineType": "Business", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F301", "StatusCode": "FI181", "StatusCodeDescriptionPayer": "CBS Transaction Timeout", "StatusCodeDescriptionPayee": "CBS Transaction Timeout", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F204", "StatusCode": "FI183", "StatusCodeDescriptionPayer": "Gateway Id not found", "StatusCodeDescriptionPayee": "Gateway Id not found", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F501", "StatusCode": "FI184", "StatusCodeDescriptionPayer": "Gateway received duplicate request", "StatusCodeDescriptionPayee": "Gateway received duplicate request", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F502", "StatusCode": "FI185", "StatusCodeDescriptionPayer": "Input data is invalid", "StatusCodeDescriptionPayee": "Input data is invalid", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F406", "StatusCode": "FI186", "StatusCodeDescriptionPayer": "Transaction amount limit failure. Please change the amount and try again", "StatusCodeDescriptionPayee": "Transaction amount limit failure. Please change the amount and try again", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "M1", "StatusCode": "FI187", "StatusCodeDescriptionPayer": "Invalid Beneficiary mobile number or MAS", "StatusCodeDescriptionPayee": "Invalid Beneficiary mobile number or MAS", "DeclineType": "Business", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "M4", "StatusCode": "FI189", "StatusCodeDescriptionPayer": "Your account is marked NRE. Please connect with our support team for next steps", "StatusCodeDescriptionPayee": "Your account is marked NRE. Please connect with our support team for next steps", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "M3", "StatusCode": "FI190", "StatusCodeDescriptionPayer": "Beneficiary account is blocked or frozen", "StatusCodeDescriptionPayee": "Beneficiary account is blocked or frozen", "DeclineType": "Business", "BankServerDown": "BENEFICIARY"}, {"Vendor": "FEDERAL", "RawStatusCode": "F112", "StatusCode": "FI191", "StatusCodeDescriptionPayer": "Authorization Failed", "StatusCodeDescriptionPayee": "Authorization Failed", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F205", "StatusCode": "FI192", "StatusCodeDescriptionPayer": "Destination Queue details are not found", "StatusCodeDescriptionPayee": "Destination Queue details are not found", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "1", "StatusCode": "FI193", "StatusCodeDescriptionPayer": "Invalid Message Format", "StatusCodeDescriptionPayee": "Invalid Message Format", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X02", "StatusCode": "FI194", "StatusCodeDescriptionPayer": "Invalid or Inactive Corporate Id", "StatusCodeDescriptionPayee": "Invalid or Inactive Corporate Id", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X03", "StatusCode": "FI195", "StatusCodeDescriptionPayer": "Corporate is not allowed for selected Message", "StatusCodeDescriptionPayee": "Corporate is not allowed for selected Message", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "181", "StatusCode": "FI196", "StatusCodeDescriptionPayer": "All cheque not in same book", "StatusCodeDescriptionPayee": "All cheque not in same book", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "182", "StatusCode": "FI197", "StatusCodeDescriptionPayer": "Could not stop all cheque", "StatusCodeDescriptionPayee": "Could not stop all cheque", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "183", "StatusCode": "FI198", "StatusCodeDescriptionPayer": "All cheque not of same account", "StatusCodeDescriptionPayee": "All cheque not of same account", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X05", "StatusCode": "FI199", "StatusCodeDescriptionPayer": "Invalid Length (Response ISO message)", "StatusCodeDescriptionPayee": "Invalid Length (Response ISO message)", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "999", "StatusCode": "FI200", "StatusCodeDescriptionPayer": "Failed", "StatusCodeDescriptionPayee": "Failed", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X09", "StatusCode": "FI201", "StatusCodeDescriptionPayer": "Invalid Fund Transfer Enquiry", "StatusCodeDescriptionPayee": "Invalid Fund Transfer Enquiry", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X11", "StatusCode": "FI202", "StatusCodeDescriptionPayer": "Invalid Length (Input Corporate message)", "StatusCodeDescriptionPayee": "Invalid Length (Input Corporate message)", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X01", "StatusCode": "FI203", "StatusCodeDescriptionPayer": "Invalid Signature or certificate details", "StatusCodeDescriptionPayee": "Invalid Signature or certificate details", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X12", "StatusCode": "FI204", "StatusCodeDescriptionPayer": "Invalid Debit Account No.", "StatusCodeDescriptionPayee": "Invalid Debit Account No.", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X13", "StatusCode": "FI205", "StatusCodeDescriptionPayer": "Transaction ID already used", "StatusCodeDescriptionPayee": "Transaction ID already used", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X14", "StatusCode": "FI206", "StatusCodeDescriptionPayer": "Remark field is empty", "StatusCodeDescriptionPayee": "Remark field is empty", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X15", "StatusCode": "FI207", "StatusCodeDescriptionPayer": "Invalid IFSC Code", "StatusCodeDescriptionPayee": "Invalid IFSC Code", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X16", "StatusCode": "FI208", "StatusCodeDescriptionPayer": "Missing RTGS Corporate setup details", "StatusCodeDescriptionPayee": "Missing RTGS Corporate setup details", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X17", "StatusCode": "FI209", "StatusCodeDescriptionPayer": "Invalid RTGS Message Characters", "StatusCodeDescriptionPayee": "Invalid RTGS Message Characters", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X18", "StatusCode": "FI210", "StatusCodeDescriptionPayer": "RTGS Rejection - QPH", "StatusCodeDescriptionPayee": "RTGS Rejection - QPH", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X19", "StatusCode": "FI211", "StatusCodeDescriptionPayer": "RTGS Rejection - PI", "StatusCodeDescriptionPayee": "RTGS Rejection - PI", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X20", "StatusCode": "FI212", "StatusCodeDescriptionPayer": "RTGS Rejection -Other bank", "StatusCodeDescriptionPayee": "RTGS Rejection -Other bank", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X21", "StatusCode": "FI213", "StatusCodeDescriptionPayer": "RTGS message generation -Syntax error", "StatusCodeDescriptionPayee": "RTGS message generation -Syntax error", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "X22", "StatusCode": "FI214", "StatusCodeDescriptionPayer": "Action message - Mandatory field missing", "StatusCodeDescriptionPayee": "Action message - Mandatory field missing", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X23", "StatusCode": "FI215", "StatusCodeDescriptionPayer": "Action message - Invalid Beneficiary Account", "StatusCodeDescriptionPayee": "Action message - Invalid Beneficiary Account", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X24", "StatusCode": "FI216", "StatusCodeDescriptionPayer": "DD Message - Invalid DD Payable City", "StatusCodeDescriptionPayee": "DD Message - Invalid DD Payable City", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "X25", "StatusCode": "FI217", "StatusCodeDescriptionPayer": "Message Invalid Values", "StatusCodeDescriptionPayee": "Message Invalid Values", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "X26", "StatusCode": "FI218", "StatusCodeDescriptionPayer": "Transaction does not exist", "StatusCodeDescriptionPayee": "Transaction does not exist", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X27", "StatusCode": "FI219", "StatusCodeDescriptionPayer": "DP Error BackOffice Description", "StatusCodeDescriptionPayee": "DP Error BackOffice Description", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X28", "StatusCode": "FI220", "StatusCodeDescriptionPayer": "NEFT - Senders Customer ID cannot be blank", "StatusCodeDescriptionPayee": "NEFT - Senders Customer ID cannot be blank", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "M5", "StatusCode": "FI221", "StatusCodeDescriptionPayer": "Account Closed (Remitter or beneficiary)", "StatusCodeDescriptionPayee": "Account Closed (Remitter or beneficiary)", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "8", "StatusCode": "FI222", "StatusCodeDescriptionPayer": "HOST/CBS Offline", "StatusCodeDescriptionPayee": "HOST/CBS Offline", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "M0", "StatusCode": "FI223", "StatusCodeDescriptionPayer": "Declined the transaction", "StatusCodeDescriptionPayee": "Declined the transaction", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F400", "StatusCode": "FI224", "StatusCodeDescriptionPayer": "Amount limit per transaction is exceeded. Please change amount and retry", "StatusCodeDescriptionPayee": "Amount limit per transaction is exceeded. Please change amount and retry", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "F401", "StatusCode": "FI225", "StatusCodeDescriptionPayer": "Amount limit per period is exceeded. Please try again after sometime", "StatusCodeDescriptionPayee": "Amount limit per period is exceeded. Please try again after sometime", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "F402", "StatusCode": "FI226", "StatusCodeDescriptionPayer": "RET Response Invalid", "StatusCodeDescriptionPayee": "RET Response Invalid", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F403", "StatusCode": "FI227", "StatusCodeDescriptionPayer": "REV Connection failure", "StatusCodeDescriptionPayee": "REV Connection failure", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F404", "StatusCode": "FI228", "StatusCodeDescriptionPayer": "REV Response timeout", "StatusCodeDescriptionPayee": "REV Response timeout", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F405", "StatusCode": "FI229", "StatusCodeDescriptionPayer": "REV Response Invalid", "StatusCodeDescriptionPayee": "REV Response Invalid", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F206", "StatusCode": "FI230", "StatusCodeDescriptionPayer": "Sender Reference Invalid", "StatusCodeDescriptionPayee": "Sender Reference Invalid", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F207", "StatusCode": "FI231", "StatusCodeDescriptionPayer": "Remitter Account type Invalid", "StatusCodeDescriptionPayee": "Remitter Account type Invalid", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F500", "StatusCode": "FI233", "StatusCodeDescriptionPayer": "Gateway coremodule Response not received", "StatusCodeDescriptionPayee": "Gateway coremodule Response not received", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F114", "StatusCode": "FI234", "StatusCodeDescriptionPayer": "<PERSON><PERSON><PERSON>", "StatusCodeDescriptionPayee": "<PERSON><PERSON><PERSON>", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F503", "StatusCode": "FI235", "StatusCodeDescriptionPayer": "Benifitiary Account number not found with the given MMID", "StatusCodeDescriptionPayee": "Benifitiary Account number not found with the given MMID", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F504", "StatusCode": "FI236", "StatusCodeDescriptionPayer": "Beneficiary MMID or Mobile Number Invalid", "StatusCodeDescriptionPayee": "Beneficiary MMID or Mobile Number Invalid", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F505", "StatusCode": "FI237", "StatusCodeDescriptionPayer": "Remitter Account number Invalid", "StatusCodeDescriptionPayee": "Remitter Account number Invalid", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F506", "StatusCode": "FI238", "StatusCodeDescriptionPayer": "Remitter Account number OR Benificiary MMID are Invalid", "StatusCodeDescriptionPayee": "Remitter Account number OR Benificiary MMID are Invalid", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F507", "StatusCode": "FI239", "StatusCodeDescriptionPayer": "Remitter or Benificiary Account Numbers are invalid", "StatusCodeDescriptionPayee": "Remitter or Benificiary Account Numbers are invalid", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F117", "StatusCode": "FI245", "StatusCodeDescriptionPayer": "Beneficiary <PERSON><PERSON><PERSON><PERSON> is Invalid.", "StatusCodeDescriptionPayee": "Beneficiary <PERSON><PERSON><PERSON><PERSON> is Invalid.", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F511", "StatusCode": "FI246", "StatusCodeDescriptionPayer": "Invalid account number", "StatusCodeDescriptionPayee": "Invalid account number", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F111", "StatusCode": "FI247", "StatusCodeDescriptionPayer": "Remitter Account Number Invalid", "StatusCodeDescriptionPayee": "Remitter Account Number Invalid", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F200", "StatusCode": "FI248", "StatusCodeDescriptionPayer": "Global Cache not loaded", "StatusCodeDescriptionPayee": "Global Cache not loaded", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F201", "StatusCode": "FI249", "StatusCodeDescriptionPayer": "Shared <PERSON>ache not loaded", "StatusCodeDescriptionPayee": "Shared <PERSON>ache not loaded", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F202", "StatusCode": "FI250", "StatusCodeDescriptionPayer": "Global Cache Invalid", "StatusCodeDescriptionPayee": "Global Cache Invalid", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F203", "StatusCode": "FI251", "StatusCodeDescriptionPayer": "Shared <PERSON><PERSON>", "StatusCodeDescriptionPayee": "Shared <PERSON><PERSON>", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F305", "StatusCode": "FI252", "StatusCodeDescriptionPayer": "NPCI Response Invalid", "StatusCodeDescriptionPayee": "NPCI Response Invalid", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F306", "StatusCode": "FI253", "StatusCodeDescriptionPayer": "VER Connection failure", "StatusCodeDescriptionPayee": "VER Connection failure", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F307", "StatusCode": "FI254", "StatusCodeDescriptionPayer": "VER Response timeout", "StatusCodeDescriptionPayee": "VER Response timeout", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F308", "StatusCode": "FI255", "StatusCodeDescriptionPayer": "VER Response Invalid", "StatusCodeDescriptionPayee": "VER Response Invalid", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F309", "StatusCode": "FI256", "StatusCodeDescriptionPayer": "RET Connection failure", "StatusCodeDescriptionPayee": "RET Connection failure", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "M6", "StatusCode": "FI257", "StatusCodeDescriptionPayer": "Limit exceeded for member bank", "StatusCodeDescriptionPayee": "Limit exceeded for member bank", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "M7", "StatusCode": "FI258", "StatusCodeDescriptionPayer": "Based on remitting and beneficiary account types. Please try again after 30mins.", "StatusCodeDescriptionPayee": "Based on remitting and beneficiary account types. Please try again after 30mins.", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "M8", "StatusCode": "FI259", "StatusCodeDescriptionPayer": "Transaction is declined based on transaction limit imposed on the FROM account type and TO account type", "StatusCodeDescriptionPayee": "Transaction is declined based on transaction limit imposed on the FROM account type and TO account type", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "MM", "StatusCode": "FI260", "StatusCodeDescriptionPayer": "Transaction not allowed as this is non-reloadable card", "StatusCodeDescriptionPayee": "Transaction not allowed as this is non-reloadable card", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "MP", "StatusCode": "FI261", "StatusCodeDescriptionPayer": "Transaction not allowed as Bank not enabled yet for P2A functionality", "StatusCodeDescriptionPayee": "Transaction not allowed as Bank not enabled yet for P2A functionality", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "MC", "StatusCode": "FI262", "StatusCodeDescriptionPayer": "Functionality not yet available for merchant through the payee bank", "StatusCodeDescriptionPayee": "Functionality not yet available for merchant through the payee bank", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "MV", "StatusCode": "FI263", "StatusCodeDescriptionPayer": "Transaction not allowed as Bank is not enabled for IMPS P2U", "StatusCodeDescriptionPayee": "Transaction not allowed as Bank is not enabled for IMPS P2U", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "MU", "StatusCode": "FI264", "StatusCodeDescriptionPayer": "Transaction not allowed as <PERSON><PERSON><PERSON> number is not in NPCI database", "StatusCodeDescriptionPayee": "Transaction not allowed as <PERSON><PERSON><PERSON> number is not in NPCI database", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "4", "StatusCode": "FI265", "StatusCodeDescriptionPayer": "Transaction not allowed as Amount is greater than 2 Lakhs (Applicable for Bank where NDC is greater than 2 Lakhs)", "StatusCodeDescriptionPayee": "Transaction not allowed as Amount is greater than 2 Lakhs (Applicable for Bank where NDC is greater than 2 Lakhs)", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "M2", "StatusCode": "FI266", "StatusCodeDescriptionPayer": "Amount limit as per payment mode exceeded. Please change amount and try again", "StatusCodeDescriptionPayee": "Amount limit as per payment mode exceeded. Please change amount and try again", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "MN", "StatusCode": "FI267", "StatusCodeDescriptionPayer": "Transaction not allowed as Beneficiary bank is not enable for Foreign Inward Remittance. To be Decline by NPCI", "StatusCodeDescriptionPayee": "Transaction not allowed as Beneficiary bank is not enable for Foreign Inward Remittance. To be Decline by NPCI", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "MQ", "StatusCode": "FI268", "StatusCodeDescriptionPayer": "Transaction not allowed as invalid payment reference", "StatusCodeDescriptionPayee": "Transaction not allowed as invalid payment reference", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "MR", "StatusCode": "FI269", "StatusCodeDescriptionPayer": "Transaction not allowed as invalid amount", "StatusCodeDescriptionPayee": "Transaction not allowed as invalid amount", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "MS", "StatusCode": "FI270", "StatusCodeDescriptionPayer": "Transaction not allowed as invalid remitter account number", "StatusCodeDescriptionPayee": "Transaction not allowed as invalid remitter account number", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "MT", "StatusCode": "FI271", "StatusCodeDescriptionPayer": "Transaction is declined as invalid account number", "StatusCodeDescriptionPayee": "Transaction is declined as invalid account number", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "92", "StatusCode": "FI272", "StatusCodeDescriptionPayer": "Invalid NBIN", "StatusCodeDescriptionPayee": "Invalid NBIN", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "12", "StatusCode": "FI273", "StatusCodeDescriptionPayer": "Invalid transaction", "StatusCodeDescriptionPayee": "Invalid transaction", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "20", "StatusCode": "FI274", "StatusCodeDescriptionPayer": "Invalid response code", "StatusCodeDescriptionPayee": "Invalid response code", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "96", "StatusCode": "FI275", "StatusCodeDescriptionPayer": "Unable to process", "StatusCodeDescriptionPayee": "Unable to process", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "51", "StatusCode": "FI276", "StatusCodeDescriptionPayer": "Insufficient balance in pool A/c", "StatusCodeDescriptionPayee": "Insufficient balance in pool A/c", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "33", "StatusCode": "FI278", "StatusCodeDescriptionPayer": "Expired card", "StatusCodeDescriptionPayee": "Expired card", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F122", "StatusCode": "FI279", "StatusCodeDescriptionPayer": "Invalid Name of the Foreign Institution/Exchange", "StatusCodeDescriptionPayee": "Invalid Name of the Foreign Institution/Exchange", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F123", "StatusCode": "FI280", "StatusCodeDescriptionPayer": "Invalid Originators Name", "StatusCodeDescriptionPayee": "Invalid Originators Name", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F124", "StatusCode": "FI281", "StatusCodeDescriptionPayer": "Invalid Originators Bank Account No.", "StatusCodeDescriptionPayee": "Invalid Originators Bank Account No.", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F125", "StatusCode": "FI282", "StatusCodeDescriptionPayer": "Invalid Originators Address", "StatusCodeDescriptionPayee": "Invalid Originators Address", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F126", "StatusCode": "FI283", "StatusCodeDescriptionPayer": "Invalid Beneficiary Customers Name", "StatusCodeDescriptionPayee": "Invalid Beneficiary Customers Name", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F127", "StatusCode": "FI284", "StatusCodeDescriptionPayer": "Invalid Purpose code", "StatusCodeDescriptionPayee": "Invalid Purpose code", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F128", "StatusCode": "FI285", "StatusCodeDescriptionPayer": "Invalid Remarks", "StatusCodeDescriptionPayee": "Invalid Remarks", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F509", "StatusCode": "FI286", "StatusCodeDescriptionPayer": "Invalid Customer Details", "StatusCodeDescriptionPayee": "Invalid Customer Details", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F510", "StatusCode": "FI287", "StatusCodeDescriptionPayer": "Remitter MMID or Mobile Number Invalid", "StatusCodeDescriptionPayee": "Remitter MMID or Mobile Number Invalid", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F700", "StatusCode": "FI288", "StatusCodeDescriptionPayer": "Invalid / Incorrect OTP. Please re-check OTP and try again", "StatusCodeDescriptionPayee": "Invalid / Incorrect OTP. Please re-check OTP and try again", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "F100", "StatusCode": "FI289", "StatusCodeDescriptionPayer": "SOAP Request Invaid", "StatusCodeDescriptionPayee": "SOAP Request Invaid", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F600", "StatusCode": "FI290", "StatusCodeDescriptionPayer": "Please wait for SMS confirmation. Please do not transfer funds to same beneficiary till you receive confirmation.", "StatusCodeDescriptionPayee": "Please wait for SMS confirmation. Please do not transfer funds to same beneficiary till you receive confirmation.", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "F407", "StatusCode": "FI291", "StatusCodeDescriptionPayer": "CBS Response got mismatch Validated through STAN", "StatusCodeDescriptionPayee": "CBS Response got mismatch Validated through STAN", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F211", "StatusCode": "FI292", "StatusCodeDescriptionPayer": "Beneficiary bank servers are down", "StatusCodeDescriptionPayee": "Beneficiary bank servers are down", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F212", "StatusCode": "FI293", "StatusCodeDescriptionPayer": "Invalid NBIN", "StatusCodeDescriptionPayee": "Invalid NBIN", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "91", "StatusCode": "FI294", "StatusCodeDescriptionPayer": "NPCI Response Time Out", "StatusCodeDescriptionPayee": "NPCI Response Time Out", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F213", "StatusCode": "FI295", "StatusCodeDescriptionPayer": "Beneficiary bank not enabled for IMPS", "StatusCodeDescriptionPayee": "Beneficiary bank not enabled for IMPS", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X93", "StatusCode": "FI296", "StatusCodeDescriptionPayer": "Unable to connect to TCP Host", "StatusCodeDescriptionPayee": "Unable to connect to TCP Host", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X94", "StatusCode": "FI297", "StatusCodeDescriptionPayer": "TCP Host timed out", "StatusCodeDescriptionPayee": "TCP Host timed out", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X32", "StatusCode": "FI298", "StatusCodeDescriptionPayer": "Invalid Currency Code", "StatusCodeDescriptionPayee": "Invalid Currency Code", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "X33", "StatusCode": "FI299", "StatusCodeDescriptionPayer": "Transaction amount not permissible. Please change the amount and try again.", "StatusCodeDescriptionPayee": "Transaction amount not permissible. Please change the amount and try again.", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F113", "StatusCode": "FI300", "StatusCodeDescriptionPayer": "Currency code Invalid", "StatusCodeDescriptionPayee": "Currency code Invalid", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F010", "StatusCode": "FI301", "StatusCodeDescriptionPayer": "Exception Occurred", "StatusCodeDescriptionPayee": "Exception Occurred", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F20", "StatusCode": "FI302", "StatusCodeDescriptionPayer": "Acknowledgment Message", "StatusCodeDescriptionPayee": "Acknowledgment Message", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F22", "StatusCode": "FI303", "StatusCodeDescriptionPayer": "Non-Delivery Message", "StatusCodeDescriptionPayee": "Non-Delivery Message", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "F23", "StatusCode": "FI304", "StatusCodeDescriptionPayer": "Delivery Notification Message", "StatusCodeDescriptionPayee": "Delivery Notification Message", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F24", "StatusCode": "FI305", "StatusCodeDescriptionPayer": "Open Notification Message", "StatusCodeDescriptionPayee": "Open Notification Message", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F25", "StatusCode": "FI306", "StatusCodeDescriptionPayer": "Negative Acknowledgment", "StatusCodeDescriptionPayee": "Negative Acknowledgment", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F26", "StatusCode": "FI307", "StatusCodeDescriptionPayer": "User Negative Acknowledgment", "StatusCodeDescriptionPayee": "User Negative Acknowledgment", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "F011", "StatusCode": "FI308", "StatusCodeDescriptionPayer": "Invalid Priority Flag.(Default should be 00)", "StatusCodeDescriptionPayee": "Invalid Priority Flag.(Default should be 00)", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F029", "StatusCode": "FI309", "StatusCodeDescriptionPayer": "Beneficiary IFSC is invalid ,please check beneficiary IFSC", "StatusCodeDescriptionPayee": "Beneficiary IFSC is invalid ,please check beneficiary IFSC", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "F024", "StatusCode": "FI311", "StatusCodeDescriptionPayer": "Invalid date", "StatusCodeDescriptionPayee": "Invalid date", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F023", "StatusCode": "FI312", "StatusCodeDescriptionPayer": "Future payment schedule", "StatusCodeDescriptionPayee": "Future payment schedule", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F019", "StatusCode": "FI313", "StatusCodeDescriptionPayer": "Your Account Number is Expired", "StatusCodeDescriptionPayee": "Your Account Number is Expired", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F022", "StatusCode": "FI314", "StatusCodeDescriptionPayer": "Transaction amount not permissible. Please change the amount and try again.", "StatusCodeDescriptionPayee": "Transaction amount not permissible. Please change the amount and try again.", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "R03", "StatusCode": "FI317", "StatusCodeDescriptionPayer": "Beneficiary account does not exist. Refund Initiated", "StatusCodeDescriptionPayee": "Beneficiary account does not exist. Refund Initiated", "DeclineType": "Business", "BankServerDown": "BENEFICIARY"}, {"Vendor": "FEDERAL", "RawStatusCode": "F012", "StatusCode": "FI319", "StatusCodeDescriptionPayer": "Invalid Delivery Notification Flag", "StatusCodeDescriptionPayee": "Invalid Delivery Notification Flag", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F02", "StatusCode": "FI320", "StatusCodeDescriptionPayer": "FAILURE", "StatusCodeDescriptionPayee": "FAILURE", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F027", "StatusCode": "FI321", "StatusCodeDescriptionPayer": "Remmiter Details acctype tag is mandatory for this sender", "StatusCodeDescriptionPayee": "Remmiter Details acctype tag is mandatory for this sender", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F028", "StatusCode": "FI322", "StatusCodeDescriptionPayer": "Beneficiary IFSC is invalid. Please re-check beneficiary IFSC and try again", "StatusCodeDescriptionPayee": "Beneficiary IFSC is invalid. Please re-check beneficiary IFSC and try again", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "RTGS_004", "StatusCode": "FI324", "StatusCodeDescriptionPayer": "RTGS returned by the corresponding bank", "StatusCodeDescriptionPayee": "RTGS returned by the corresponding bank", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "RTGS_002", "StatusCode": "FI326", "StatusCodeDescriptionPayer": "Message Failed At RBI End", "StatusCodeDescriptionPayee": "Message Failed At RBI End", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "F009", "StatusCode": "FI327", "StatusCodeDescriptionPayer": "Processing failed at RBI end", "StatusCodeDescriptionPayee": "Processing failed at RBI end", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "LAE0014", "StatusCode": "FI328", "StatusCodeDescriptionPayer": "Invalid deposit account", "StatusCodeDescriptionPayee": "Invalid deposit account", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "UP7", "StatusCode": "FI329", "StatusCodeDescriptionPayer": "Payer PSP scheduled offline", "StatusCodeDescriptionPayee": "Payer PSP scheduled offline", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"Vendor": "FEDERAL", "RawStatusCode": "OBE0062", "StatusCode": "FI330", "StatusCodeDescriptionPayer": "Invalid Transaction PIN", "StatusCodeDescriptionPayee": "Invalid Transaction PIN", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "OBE0064", "StatusCode": "FI331", "StatusCodeDescriptionPayer": "Number of PIN tries exceeded", "StatusCodeDescriptionPayee": "Number of PIN tries exceeded", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "OBE0170", "StatusCode": "FI332", "StatusCodeDescriptionPayer": "Device is deactivated temporary by bank", "StatusCodeDescriptionPayee": "Device is deactivated temporary by bank", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "OBE0061", "StatusCode": "FI333", "StatusCodeDescriptionPayer": "Transaction PIN is not SET", "StatusCodeDescriptionPayee": "Transaction PIN is not SET", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "OBE0066", "StatusCode": "FI334", "StatusCodeDescriptionPayer": "Invalid <PERSON>", "StatusCodeDescriptionPayee": "Invalid <PERSON>", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "U17", "StatusCode": "FI335", "StatusCodeDescriptionPayer": "Payee PSP not registered purpose = 00", "StatusCodeDescriptionPayee": "Payee PSP not registered purpose = 00", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "F0", "StatusCode": "SUCCESS", "StatusCodeDescriptionPayer": "SUCCESS", "StatusCodeDescriptionPayee": "SUCCESS", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "OBE0000", "StatusCode": "SUCCESS", "StatusCodeDescriptionPayer": "SUCCESS", "StatusCodeDescriptionPayee": "SUCCESS", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "000", "StatusCode": "SUCCESS", "StatusCodeDescriptionPayer": "SUCCESS", "StatusCodeDescriptionPayee": "SUCCESS", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "0", "StatusCode": "SUCCESS", "StatusCodeDescriptionPayer": "SUCCESS", "StatusCodeDescriptionPayee": "SUCCESS", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "OBE0172", "StatusCode": "FI336", "StatusCodeDescriptionPayer": "Customer Name Missmatch", "StatusCodeDescriptionPayee": "Customer Name Missmatch", "DeclineType": "Business"}, {"Vendor": "FEDERAL", "RawStatusCode": "OBE0067", "StatusCode": "FI337", "StatusCodeDescriptionPayer": "No Data found with given Details", "StatusCodeDescriptionPayee": "No Data found with given Details", "DeclineType": "Business"}]}