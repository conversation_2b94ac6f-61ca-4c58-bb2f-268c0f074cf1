{"ResponseCodes": [{"Vendor": "FEDERAL_BANK", "ApiType": "DEPOSIT_ENQUIRY", "RawStatusCode": "000", "StatusCode": "EP_DEP_ENQ_RESP_SUCCESS", "StatusDescription": "SUCCESS"}, {"Vendor": "FEDERAL_BANK", "ApiType": "DEPOSIT_ENQUIRY", "RawStatusCode": "OBE0000", "StatusCode": "EP_DEP_ENQ_RESP_INPROGRESS", "StatusDescription": "INPROGRESS"}, {"Vendor": "FEDERAL_BANK", "ApiType": "DEPOSIT_ENQUIRY", "RawStatusCode": "OBE0001", "StatusCode": "EP_DEP_ENQ_RESP_100", "StatusDescription": "Something went wrong EP_DEP_ENQ_RESP_100."}, {"Vendor": "FEDERAL_BANK", "ApiType": "DEPOSIT_ENQUIRY", "RawStatusCode": "OBE0002", "StatusCode": "EP_DEP_ENQ_RESP_101", "StatusDescription": "Something went wrong EP_DEP_ENQ_RESP_101."}, {"Vendor": "FEDERAL_BANK", "ApiType": "DEPOSIT_ENQUIRY", "RawStatusCode": "OBE0003", "StatusCode": "EP_DEP_ENQ_RESP_102", "StatusDescription": "Something went wrong EP_DEP_ENQ_RESP_102."}, {"Vendor": "FEDERAL_BANK", "ApiType": "DEPOSIT_ENQUIRY", "RawStatusCode": "OBE0004", "StatusCode": "EP_DEP_ENQ_RESP_103", "StatusDescription": "Something went wrong EP_DEP_ENQ_RESP_103."}, {"Vendor": "FEDERAL_BANK", "ApiType": "DEPOSIT_ENQUIRY", "RawStatusCode": "OBE0007", "StatusCode": "EP_DEP_ENQ_RESP_104", "StatusDescription": "Something went wrong EP_DEP_ENQ_RESP_104."}, {"Vendor": "FEDERAL_BANK", "ApiType": "DEPOSIT_ENQUIRY", "RawStatusCode": "OBE0011", "StatusCode": "EP_DEP_ENQ_RESP_105", "StatusDescription": "Something went wrong EP_DEP_ENQ_RESP_105."}, {"Vendor": "FEDERAL_BANK", "ApiType": "DEPOSIT_ENQUIRY", "RawStatusCode": "OBE0059", "StatusCode": "EP_DEP_ENQ_RESP_106", "StatusDescription": "Something went wrong EP_DEP_ENQ_RESP_106."}, {"Vendor": "FEDERAL_BANK", "ApiType": "DEPOSIT_ENQUIRY", "RawStatusCode": "OBE0060", "StatusCode": "EP_DEP_ENQ_RESP_107", "StatusDescription": "Something went wrong EP_DEP_ENQ_RESP_107."}, {"Vendor": "FEDERAL_BANK", "ApiType": "DEPOSIT_ENQUIRY", "RawStatusCode": "LAE9999", "StatusCode": "EP_DEP_ENQ_RESP_108", "StatusDescription": "Something went wrong EP_DEP_ENQ_RESP_108."}, {"Vendor": "FEDERAL_BANK", "ApiType": "DEPOSIT_ENQUIRY", "RawStatusCode": "OBE8888", "StatusCode": "EP_DEP_ENQ_RESP_109", "StatusDescription": "Something went wrong EP_DEP_ENQ_RESP_109."}, {"Vendor": "FEDERAL_BANK", "ApiType": "DEPOSIT_ENQUIRY", "RawStatusCode": "OBE0070", "StatusCode": "EP_DEP_ENQ_RESP_110", "StatusDescription": "Something went wrong EP_DEP_ENQ_RESP_110."}, {"Vendor": "FEDERAL_BANK", "ApiType": "DEPOSIT_ENQUIRY", "RawStatusCode": "OBE0073", "StatusCode": "EP_DEP_ENQ_RESP_111", "StatusDescription": "Something went wrong EP_DEP_ENQ_RESP_111."}, {"Vendor": "FEDERAL_BANK", "ApiType": "DEPOSIT_ENQUIRY", "RawStatusCode": "OBE0022", "StatusCode": "EP_DEP_ENQ_RESP_112", "StatusDescription": "Something went wrong EP_DEP_ENQ_RESP_112."}, {"Vendor": "FEDERAL_BANK", "ApiType": "DEPOSIT_ENQUIRY", "RawStatusCode": "LAE0012", "StatusCode": "EP_DEP_ENQ_RESP_113", "StatusDescription": "Something went wrong EP_DEP_ENQ_RESP_113."}, {"Vendor": "FEDERAL_BANK", "ApiType": "DEPOSIT_ENQUIRY", "RawStatusCode": "OBE0092", "StatusCode": "EP_DEP_ENQ_RESP_114", "StatusDescription": "Something went wrong EP_DEP_ENQ_RESP_114."}, {"Vendor": "FEDERAL_BANK", "ApiType": "DEPOSIT_ENQUIRY", "RawStatusCode": "LAE1111", "StatusCode": "EP_DEP_ENQ_RESP_115", "StatusDescription": "Something went wrong EP_DEP_ENQ_RESP_115."}, {"Vendor": "FEDERAL_BANK", "ApiType": "DEPOSIT_ENQUIRY", "RawStatusCode": "LAE0035", "StatusCode": "EP_DEP_ENQ_RESP_116", "StatusDescription": "Something went wrong EP_DEP_ENQ_RESP_116."}, {"Vendor": "FEDERAL_BANK", "ApiType": "DEPOSIT_ENQUIRY", "RawStatusCode": "LAE0036", "StatusCode": "EP_DEP_ENQ_RESP_117", "StatusDescription": "Something went wrong EP_DEP_ENQ_RESP_117."}, {"Vendor": "FEDERAL_BANK", "ApiType": "CREATE_DEPOSIT", "RawStatusCode": "000", "StatusCode": "EP_DEP_CD_RESP_SUCCESS", "StatusDescription": "SUCCESS"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CREATE_DEPOSIT", "RawStatusCode": "LAE0035", "StatusCode": "EP_DEP_CD_RESP_100", "StatusDescription": "Something went wrong EP_DEP_CD_RESP_100."}, {"Vendor": "FEDERAL_BANK", "ApiType": "CREATE_DEPOSIT", "RawStatusCode": "LAE0036", "StatusCode": "EP_DEP_CD_RESP_101", "StatusDescription": "Something went wrong EP_DEP_CD_RESP_101."}, {"Vendor": "FEDERAL_BANK", "ApiType": "CREATE_DEPOSIT", "RawStatusCode": "LAE1111", "StatusCode": "EP_DEP_CD_RESP_102", "StatusDescription": "Something went wrong EP_DEP_CD_RESP_102."}, {"Vendor": "FEDERAL_BANK", "ApiType": "PRECLOSE_DEPOSIT", "RawStatusCode": "000", "StatusCode": "EP_DEP_PD_RESP_SUCCESS", "StatusDescription": "SUCCESS"}, {"Vendor": "FEDERAL_BANK", "ApiType": "PRECLOSE_DEPOSIT", "RawStatusCode": "LAE1111", "StatusCode": "EP_DEP_PD_RESP_100", "StatusDescription": "Something went wrong EP_DEP_PD_RESP_100."}]}