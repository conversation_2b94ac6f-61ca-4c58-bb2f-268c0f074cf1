Application:
  Environment: "demo"
  Name: "vendornotification"

UpdateTransactionEventsPublisher:
  QueueName: "demo-payment-callback-update-queue"

KarzaEPANWhitelist:
  EnableWhitelist: false

KarzaVkycWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "***********"
  SoftBlock: false

AclSmsWhitelist:
  EnableWhitelist: false

KaleyraSmsWhitelist:
  EnableWhitelist: false

AclWhatsappWhitelist:
  EnableWhitelist: false

GupshupWhitelist:
  EnableWhitelist: false

NetCoreWhitelist:
  EnableWhitelist: false

DPandaWhitelist:
  EnableWhitelist: false

PoshVineWhitelist:
  EnableWhitelist: false

OzonetelWhitelist:
  EnableWhitelist: false

M2PWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "*************,************,************"
  SoftBlock: true

FederalWhitelist:
  EnableWhitelist: false

InboundTxnPublisher:
  QueueName: "demo-inbound-txn-queue"

InboundUpiTxnPublisher:
  QueueName: "demo-inbound-upi-txn-queue"

InboundLoanTxnPublisher:
  QueueName: "demo-loan-inbound-transaction-queue"

CreateCardCallbackPublisher:
  QueueName: "demo-card-creation-callback-queue"

DispatchPhysicalCardCallbackPublisher:
  QueueName: "demo-card-dispatch-request-callback-queue"

CheckLivenessCallbackPublisher:
  QueueName: "demo-check-liveness-callback-queue"

UPIReqAuthEventPublisher:
  QueueName: "demo-upi-req-auth-processing-queue"

UPIReqAuthMandateEventPublisher:
  QueueName: "demo-upi-req-auth-mandate-processing-queue"

UPIReqAuthValCustEventPublisher:
  QueueName: "demo-upi-req-auth-val-cust-processing-queue"

UPIReqMandateConfirmationEventPublisher:
  QueueName: "demo-upi-req-mandate-confirmation-processing-queue"

UPIRespMandateEventPublisher:
  QueueName: "demo-upi-resp-mandate-processing-queue"

UPIRespPayEventPublisher:
  QueueName: "demo-upi-resp-pay-processing-queue"

UPIReqTxnConfirmationEventPublisher:
  QueueName: "demo-upi-req-txn-confirmation-processing-queue"

UPIReqValAddressEventPublisher:
  QueueName: "demo-upi-req-val-address-processing-queue"

UPIListPspKeysEventPublisher:
  QueueName: "demo-list-psp-keys-processing-queue"

UPIListVaePublisher:
  QueueName: "demo-list-vae-processing-queue"
  BucketName: "epifi-dev-extended-sqs"

CreateDepositCallbackPublisher:
  QueueName: "demo-create-deposit-callback-queue"

PreCloseDepositCallbackPublisher:
  QueueName: "demo-preclose-deposit-callback-queue"

FdAutoRenewCallbackPublisher:
  QueueName: "demo-deposit-maturity-action-callback-queue"

AclSmsCallbackPublisher:
  QueueName: "demo-vn-acl-sms-callback-queue"

KaleyraSmsCallbackPublisher:
  QueueName: "demo-vn-kaleyra-sms-callback-queue"

AclWhatsappCallbackPublisher:
  QueueName: "demo-vn-acl-whatsapp-callback-queue"

AclWhatsappReplyPublisher:
  QueueName: "demo-vn-acl-whatsapp-reply-queue"

DeviceReRegCallbackPublisher:
  QueueName: "demo-device-rereg-callback-queue"

DeviceRegSMSAckPublisher:
  QueueName: "demo-device-reg-sms-ack-queue"

CustomerCreationCallbackPublisher:
  QueueName: "demo-customer-creation-callback-queue"

BankCustCallbackPublisher:
  QueueName: "demo-bankcust-customer-creation-callback-queue"

AccountCreationCallbackPublisher:
  QueueName: "demo-savings-creation-callback-queue"

FederalVkycUpdatePublisher:
  QueueName: "demo-vn-federal-vkyc-update-queue"

UpdateShippingAddressCallbackPublisher:
  QueueName: "demo-shipping-address-update-callback-queue"

KarzaVkycAgentResponsePublisher:
  QueueName: "demo-vn-karza-vkyc-agent-response-queue"

KarzaVkycAuditorResponsePublisher:
  QueueName: "demo-vn-karza-vkyc-auditor-response-queue"

KarzaVkycCallEventPublisher:
  QueueName: "demo-vn-karza-vkyc-call-event-queue"

EmailCallbackPublisher:
  QueueName: "demo-vn-email-callback-queue"

ConsentCallbackPublisher:
  QueueName: "demo-vn-aa-consent-callback-queue"

FICallbackPublisher:
  QueueName: "demo-vn-aa-fi-callback-queue"

CardTrackingCallbackPublisher:
  QueueName: "demo-card-tracking-callback-queue"

AccountLinkStatusCallbackPublisher:
  QueueName: "demo-vn-aa-account-link-status-callback-queue"

UPIReqTxnConfirmationComplaintEventPublisher:
  QueueName: "demo-upi-req-txn-confirmation-complaint-processing-queue"

OzonetelCallDetailsPublisher:
  QueueName: "demo-vn-ozonetel-call-details-queue"

UPIRespComplaintEventPublisher:
  QueueName: "demo-upi-resp-complaint-queue"

FreshchatActionCallbackPublisher:
  QueueName: "demo-vn-freshchat-action-callback-queue"

TssWebhookCallBackPublisher:
  QueueName: "demo-tss-webhook-callback-queue"

HealthInsurancePolicyIssuanceEventPublisher:
  QueueName: "demo-salaryprogram-healthinsurance-policy-issuance-completion-queue"

SignalWorkflowPublisher:
  QueueName: "demo-celestial-signal-workflow-queue"

UPIReqMapperConfirmationEventPublisher:
  QueueName: "demo-upi-req-mapper-confirmation-processing-queue"

LoansFiftyfinCallbackPublisher:
  QueueName: "demo-vn-loans-fiftyfin-callback-queue"

ProcrastinatorWorkflowPublisher:
  QueueName: "demo-celestial-initiate-procrastinator-workflow-queue"

FederalEscalationUpdateEventPublisher:
  QueueName : "demo-cx-escalation-update-queue"

Server:
  Ports:
    GrpcPort: 8088
    GrpcSecurePort: 9524
    HttpPort: 9999
    HttpPProfPort: 9990

Aws:
  Region: "ap-south-1"

#json file path
PayFundTransferStatusCodeJson: "./mappingJson/fundTransferStatusCodes.json"
PayUpiStatusCodeJson: "./mappingJson/upiStatusCodes.json"
DepositResponseStatusCodeFilePath: "./mappingJson/depositResponseStatusCodes.json"
CardResponseStatusCodeFilePath: "./mappingJson/cardResponseStatusCodes.json"

SyncRespHandler:
  SyncPublisher:
    Publisher:
      QueueName: "demo-vn-sync-wrapper-queue"


Flags:
  TrimDebugMessageFromStatus: true

Secrets:
  Ids:
    #Federal
    SenderCode: "demo/vg-vn-simulator-vgpci/federal-auth-sender-code"
    SenderCodeLoans: "demo/vg-vn-simulator-vgpci/federal-auth-sender-code"
    ServiceAccessId: "demo/vg-vn-vgpci/federal-auth-service-access-id"
    ServiceAccessCode: "demo/vg-vn-vgpci/federal-auth-service-access-code"
    AaVgVnSecretsV1: "demo/vg-vn/aa-secrets-v1"
    SahamatiPublicKeyJwk: "demo/vn/sahamati-public-key"
    SenseforthAuthApiKey: "demo/vn/senseforth-auth-api-key"
    SprinklrAuthApiKey: "demo/vg-vn/sprinklr-auth-api-key"

SecureLogging:
  EnableSecureLog: true
  SecureLogPath: "/var/log/vendornotification/secure.log"
  MaxSizeInMBs: 50
  MaxBackups: 20

FeatureFlags:
  AllowCustomerCallbackProcessing: true
  AllowAccountCallbackProcessing: true

AA:
  AaSecretsVersionToUse: "V1"
  TokenIssuer: "https://uattokens.sahamati.org.in/auth/realms/sahamati"
  VerifyApiKeyAndJws: true
  IPWhiteListing:
    EnableWhitelist: false
    SoftBlock: false
  OneMoneyCrId: "onemoney-aa"
  FinvuCrId: "<EMAIL>"
  EpifiAaKid: "654024c8-29c8-11e8-8868-0289437bf331"

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

CCTransactionNotificationPublisher:
  QueueName: "demo-cc-txn-notification-queue"

CCAcsNotificationPublisher:
  QueueName: "demo-cc-acs-notification-queue"

CCStatementNotificationPublisher:
  QueueName: "demo-cc-statement-notification-queue"

SmallcaseProcessMFHoldingsWebhookPublisher:
  QueueName: "demo-vn-process-mf-holdings-webhook-extended-queue"
  BucketName: "epifi-wealth-demo-extended-sqs"

CardSwitchFinancialNotificationPublisher:
  QueueName: "demo-card-switch-financial-notification-queue"

CardSwitchNonFinancialNotificationPublisher:
  QueueName: "demo-card-switch-non-financial-notification-queue"

AccountStatusCallbackPublisher:
  QueueName: "demo-account-status-callback-queue"

EnachRegistrationAuthorisationCallbackPublisher:
  QueueName: "demo-recurringpayment-creation-auth-vendor-callback-queue"

KycStatusUpdatePublisher:
  QueueName: "demo-kyc-v2-update-status-queue"

CcSwitchNotificationsBucketName: "epifi-demo-cc-switch-notifications"
CcRawSwitchNotificationsBucketName: "epifi-raw-dev"
EpanCallbackBucketName: "epifi-demo-pan"
M2pFederalSwitchNotificationFilePath: "m2p/federal/%s/%s-switchTxnNotifications.csv"
RawBucketM2pFederalSwitchNotificationFilePath: "demo/data/vendor/federal_cc_reports/switch_transaction_notifications/%s/%s-switchTxnNotifications.csv"

CredgenicsCallbackStreamProducer:
  EmailStream:
    StreamName: "demo-credgenics-webhook-generic-event-publish-stream"
  SmsStream:
    StreamName: "demo-credgenics-webhook-generic-event-publish-stream"
  WhatsappStream:
    StreamName: "demo-credgenics-webhook-generic-event-publish-stream"
  CallingStream:
    StreamName: "demo-credgenics-webhook-calling-event-publish-stream"
  VoiceMessageStream:
    StreamName: "demo-credgenics-webhook-generic-event-publish-stream"

PgRazorpayInboundEventPublisher:
  QueueName: "demo-pg-razorpay-inbound-event-queue"
