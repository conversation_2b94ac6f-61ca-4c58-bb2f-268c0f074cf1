// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"context"
	"fmt"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"

	pkgweb "github.com/epifi/be-common/api/pkg/web"
	questtypes "github.com/epifi/be-common/api/quest/types"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	response "github.com/epifi/be-common/pkg/syncwrapper/response"
	questsdk "github.com/epifi/be-common/quest/sdk"
	genconfig "github.com/epifi/be-common/quest/sdk/config/genconf"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	config "github.com/epifi/gamma/vendornotification/config"
)

type Config struct {
	callbacks                     *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk                      questsdk.Client
	questFieldPath                string
	_EnableIndianStocksContentApi uint32
	// adding this since we don't have any other quest enabled config.
	_DummyQuestVariable                              string
	_DummyQuestVariableMutex                         *sync.RWMutex
	_Flags                                           *Flags
	_FeatureFlags                                    *FeatureFlags
	_AA                                              *AA
	_QuestSdk                                        *genconfig.Config
	_Reward                                          *Reward
	_Nugget                                          *Nugget
	_Application                                     *config.Application
	_Server                                          *config.Server
	_AWS                                             *config.Aws
	_UpdateTransactionEventsPublisher                *cfg.SqsPublisher
	_InboundTxnPublisher                             *cfg.SqsPublisher
	_InboundUpiTxnPublisher                          *cfg.SqsPublisher
	_InboundLoanTxnPublisher                         *cfg.SqsPublisher
	_GrpcRateLimiterParams                           *cfg.GrpcRateLimiterParams
	_SyncWrapperPublisher                            *cfg.SqsPublisher
	_CreateCardCallbackPublisher                     *cfg.SqsPublisher
	_DispatchPhysicalCardCallbackPublisher           *cfg.SqsPublisher
	_CheckLivenessCallbackPublisher                  *cfg.SqsPublisher
	_UpdateShippingAddressCallbackPublisher          *cfg.SqsPublisher
	_UPIReqAuthEventPublisher                        *cfg.SqsPublisher
	_UPIReqAuthMandateEventPublisher                 *cfg.SqsPublisher
	_UPIReqAuthValCustEventPublisher                 *cfg.SqsPublisher
	_UPIReqMandateConfirmationEventPublisher         *cfg.SqsPublisher
	_UPIRespPayEventPublisher                        *cfg.SqsPublisher
	_UPIRespMandateEventPublisher                    *cfg.SqsPublisher
	_UPIReqTxnConfirmationEventPublisher             *cfg.SqsPublisher
	_UPIReqValAddressEventPublisher                  *cfg.SqsPublisher
	_UPIListPspKeysEventPublisher                    *cfg.SqsPublisher
	_UPIListVaePublisher                             *cfg.ExtendedSqsPublisher
	_CreateDepositCallbackPublisher                  *cfg.SqsPublisher
	_PreCloseDepositCallbackPublisher                *cfg.SqsPublisher
	_FdAutoRenewCallbackPublisher                    *cfg.SqsPublisher
	_AclSmsCallbackPublisher                         *cfg.SqsPublisher
	_KaleyraSmsCallbackPublisher                     *cfg.SqsPublisher
	_AclWhatsappCallbackPublisher                    *cfg.SqsPublisher
	_AclWhatsappReplyPublisher                       *cfg.SqsPublisher
	_GupshupWhatsappCallbackPublisher                *cfg.SqsPublisher
	_GupshupRcsCallbackPublisher                     *cfg.SqsPublisher
	_NetCoreSmsCallbackPublisher                     *cfg.SqsPublisher
	_AirtelSmsCallbackPublisher                      *cfg.SqsPublisher
	_DeviceReRegCallbackPublisher                    *cfg.SqsPublisher
	_DeviceRegSMSAckPublisher                        *cfg.SqsPublisher
	_CustomerCreationCallbackPublisher               *cfg.SqsPublisher
	_BankCustCallbackPublisher                       *cfg.SqsPublisher
	_AccountCreationCallbackPublisher                *cfg.SqsPublisher
	_FederalVkycUpdatePublisher                      *cfg.SqsPublisher
	_KarzaVkycCallEventPublisher                     *cfg.SqsPublisher
	_KarzaVkycAgentResponsePublisher                 *cfg.SqsPublisher
	_KarzaVkycAuditorResponsePublisher               *cfg.SqsPublisher
	_EmailCallbackPublisher                          *cfg.SqsPublisher
	_ConsentCallbackPublisher                        *cfg.SqsPublisher
	_FICallbackPublisher                             *cfg.SqsPublisher
	_AccountLinkStatusCallbackPublisher              *cfg.SqsPublisher
	_CardTrackingCallbackPublisher                   *cfg.SqsPublisher
	_UPIReqTxnConfirmationComplaintEventPublisher    *cfg.SqsPublisher
	_OzonetelCallDetailsPublisher                    *cfg.SqsPublisher
	_UPIRespComplaintEventPublisher                  *cfg.SqsPublisher
	_FreshchatActionCallbackPublisher                *cfg.SqsPublisher
	_CCTransactionNotificationPublisher              *cfg.SqsPublisher
	_CCStatementNotificationPublisher                *cfg.SqsPublisher
	_CCAcsNotificationPublisher                      *cfg.SqsPublisher
	_TssWebhookCallBackPublisher                     *cfg.SqsPublisher
	_HealthInsurancePolicyIssuanceEventPublisher     *cfg.SqsPublisher
	_CCNonFinancialNotificationPublisher             *cfg.SqsPublisher
	_SmallcaseProcessMFHoldingsWebhookPublisher      *cfg.ExtendedSqsPublisher
	_SignalWorkflowPublisher                         *cfg.SqsPublisher
	_LoansFiftyfinCallbackPublisher                  *cfg.SqsPublisher
	_AirtelWhatsappCallbackPublisher                 *cfg.SqsPublisher
	_Secrets                                         *cfg.Secrets
	_FederalUPISignatureVerifier                     *cfg.XMLDigitalSignatureVerifier
	_PayFundTransferStatusCodeJson                   string
	_PayUpiStatusCodeJson                            string
	_EnachTransactionStatusCodeJson                  string
	_DepositResponseStatusCodeFilePath               string
	_CardResponseStatusCodeFilePath                  string
	_SecureLogging                                   *config.SecureLogging
	_Logging                                         *cfg.Logging
	_SyncRespHandler                                 *response.SyncRespHandler
	_KarzaVkycWhitelist                              *config.IPWhiteListing
	_AclSmsWhitelist                                 *config.IPWhiteListing
	_KaleyraSmsWhitelist                             *config.IPWhiteListing
	_AclWhatsappWhitelist                            *config.IPWhiteListing
	_NetCoreWhitelist                                *config.IPWhiteListing
	_AirtelWhitelist                                 *config.IPWhiteListing
	_UPIWhitelist                                    *config.IPWhiteListing
	_OzonetelWhitelist                               *config.IPWhiteListing
	_FreshchatWhitelist                              *config.IPWhiteListing
	_SenseforthWhitelist                             *config.IPWhiteListing
	_TssWhitelist                                    *config.IPWhiteListing
	_RiskcovryWhitelist                              *config.IPWhiteListing
	_SmallcaseWhitelist                              *config.IPWhiteListing
	_SprinklrWhitelist                               *config.IPWhiteListing
	_KarzaEPANWhitelist                              *config.IPWhiteListing
	_M2PWhitelist                                    *config.IPWhiteListing
	_FiftyfinWhitelist                               *config.IPWhiteListing
	_MoneyviewWhiteList                              *config.IPWhiteListing
	_IrisWhitelist                                   *config.IPWhiteListing
	_AbflWhitelist                                   *config.IPWhiteListing
	_CredgenicsWhitelist                             *config.IPWhiteListing
	_DPandaWhitelist                                 *config.IPWhiteListing
	_PoshVineWhitelist                               *config.IPWhiteListing
	_GupshupWhitelist                                *config.IPWhiteListing
	_PaisabazaarWhitelist                            *config.IPWhiteListing
	_SetuWhiteList                                   *config.IPWhiteListing
	_FederalWhiteList                                *config.IPWhiteListing
	_SavenWhiteList                                  *config.IPWhiteListing
	_LeadsWhiteList                                  *config.IPWhiteListing
	_AuthWhiteList                                   *config.IPWhiteListing
	_NuggetWhiteList                                 *config.IPWhiteListing
	_NumberOfHopsThatAddXForwardedFor                int
	_VpcCidrIPPrefix                                 string
	_Ozonetel                                        *config.Ozonetel
	_Tracing                                         *cfg.Tracing
	_Profiling                                       *cfg.Profiling
	_Chatbot                                         *config.Chatbot
	_RateLimitConfig                                 *cfg.RateLimitConfig
	_Freshdesk                                       *cfg.Freshdesk
	_CardSwitchFinancialNotificationPublisher        *cfg.SqsPublisher
	_CardSwitchNonFinancialNotificationPublisher     *cfg.SqsPublisher
	_AccountStatusCallBackPublisher                  *cfg.SqsPublisher
	_UPIReqMapperConfirmationEventPublisher          *cfg.SqsPublisher
	_EnachRegistrationAuthorisationCallbackPublisher *cfg.SqsPublisher
	_ProcrastinatorWorkflowPublisher                 *cfg.SqsPublisher
	_QuestRedisOptions                               *cfg.RedisOptions
	_KycStatusUpdatePublisher                        *cfg.SqsPublisher
	_CcSwitchNotificationsBucketName                 string
	_CcRawSwitchNotificationsBucketName              string
	_EpanCallbackBucketName                          string
	_M2pFederalSwitchNotificationFilePath            string
	_RawBucketM2pFederalSwitchNotificationFilePath   string
	_DpandaVnSecrets                                 *config.FiStoreVendorSecrets
	_PoshvineVnSecrets                               *config.FiStoreVendorSecrets
	_RazorpayVnSecrets                               *config.FiStoreVendorSecrets
	_CredgenicsCallbackStreamProducer                *config.CredgenicsCallbackStreamProducer
	_FederalBankCustKycStateChangePublisher          *cfg.SqsPublisher
	_FederalResidentialStatusUpdatePublisher         *cfg.SqsPublisher
	_FederalMobileNumberUpdatePublisher              *cfg.SqsPublisher
	_PgRazorpayInboundEventPublisher                 *cfg.SqsPublisher
	_FederalEscalationUpdateEventPublisher           *cfg.SqsPublisher
	_CcOnboardingStateUpdateEventPublisher           *cfg.SqsPublisher
	_Auth                                            *config.Auth
	_VendorRewardFulfillmentPublisher                *cfg.SqsPublisher
	_SavenRewardVnSecrets                            *config.SavenRewardVnSecrets
}

func (obj *Config) EnableIndianStocksContentApi() bool {
	if atomic.LoadUint32(&obj._EnableIndianStocksContentApi) == 0 {
		return false
	} else {
		return true
	}
}

// adding this since we don't have any other quest enabled config.
func (obj *Config) dummyQuestVariable() string {
	obj._DummyQuestVariableMutex.RLock()
	defer obj._DummyQuestVariableMutex.RUnlock()
	return obj._DummyQuestVariable
}

// adding this since we don't have any other quest enabled config.
func (obj *Config) DummyQuestVariable(ctx context.Context) string {
	defVal := obj.dummyQuestVariable()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "DummyQuestVariable"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *Config) Flags() *Flags {
	return obj._Flags
}
func (obj *Config) FeatureFlags() *FeatureFlags {
	return obj._FeatureFlags
}
func (obj *Config) AA() *AA {
	return obj._AA
}
func (obj *Config) QuestSdk() *genconfig.Config {
	return obj._QuestSdk
}
func (obj *Config) Reward() *Reward {
	return obj._Reward
}
func (obj *Config) Nugget() *Nugget {
	return obj._Nugget
}
func (obj *Config) Application() *config.Application {
	return obj._Application
}
func (obj *Config) Server() *config.Server {
	return obj._Server
}
func (obj *Config) AWS() *config.Aws {
	return obj._AWS
}
func (obj *Config) UpdateTransactionEventsPublisher() *cfg.SqsPublisher {
	return obj._UpdateTransactionEventsPublisher
}
func (obj *Config) InboundTxnPublisher() *cfg.SqsPublisher {
	return obj._InboundTxnPublisher
}
func (obj *Config) InboundUpiTxnPublisher() *cfg.SqsPublisher {
	return obj._InboundUpiTxnPublisher
}
func (obj *Config) InboundLoanTxnPublisher() *cfg.SqsPublisher {
	return obj._InboundLoanTxnPublisher
}
func (obj *Config) GrpcRateLimiterParams() *cfg.GrpcRateLimiterParams {
	return obj._GrpcRateLimiterParams
}
func (obj *Config) SyncWrapperPublisher() *cfg.SqsPublisher {
	return obj._SyncWrapperPublisher
}
func (obj *Config) CreateCardCallbackPublisher() *cfg.SqsPublisher {
	return obj._CreateCardCallbackPublisher
}
func (obj *Config) DispatchPhysicalCardCallbackPublisher() *cfg.SqsPublisher {
	return obj._DispatchPhysicalCardCallbackPublisher
}
func (obj *Config) CheckLivenessCallbackPublisher() *cfg.SqsPublisher {
	return obj._CheckLivenessCallbackPublisher
}
func (obj *Config) UpdateShippingAddressCallbackPublisher() *cfg.SqsPublisher {
	return obj._UpdateShippingAddressCallbackPublisher
}
func (obj *Config) UPIReqAuthEventPublisher() *cfg.SqsPublisher {
	return obj._UPIReqAuthEventPublisher
}
func (obj *Config) UPIReqAuthMandateEventPublisher() *cfg.SqsPublisher {
	return obj._UPIReqAuthMandateEventPublisher
}
func (obj *Config) UPIReqAuthValCustEventPublisher() *cfg.SqsPublisher {
	return obj._UPIReqAuthValCustEventPublisher
}
func (obj *Config) UPIReqMandateConfirmationEventPublisher() *cfg.SqsPublisher {
	return obj._UPIReqMandateConfirmationEventPublisher
}
func (obj *Config) UPIRespPayEventPublisher() *cfg.SqsPublisher {
	return obj._UPIRespPayEventPublisher
}
func (obj *Config) UPIRespMandateEventPublisher() *cfg.SqsPublisher {
	return obj._UPIRespMandateEventPublisher
}
func (obj *Config) UPIReqTxnConfirmationEventPublisher() *cfg.SqsPublisher {
	return obj._UPIReqTxnConfirmationEventPublisher
}
func (obj *Config) UPIReqValAddressEventPublisher() *cfg.SqsPublisher {
	return obj._UPIReqValAddressEventPublisher
}
func (obj *Config) UPIListPspKeysEventPublisher() *cfg.SqsPublisher {
	return obj._UPIListPspKeysEventPublisher
}
func (obj *Config) UPIListVaePublisher() *cfg.ExtendedSqsPublisher {
	return obj._UPIListVaePublisher
}
func (obj *Config) CreateDepositCallbackPublisher() *cfg.SqsPublisher {
	return obj._CreateDepositCallbackPublisher
}
func (obj *Config) PreCloseDepositCallbackPublisher() *cfg.SqsPublisher {
	return obj._PreCloseDepositCallbackPublisher
}
func (obj *Config) FdAutoRenewCallbackPublisher() *cfg.SqsPublisher {
	return obj._FdAutoRenewCallbackPublisher
}
func (obj *Config) AclSmsCallbackPublisher() *cfg.SqsPublisher {
	return obj._AclSmsCallbackPublisher
}
func (obj *Config) KaleyraSmsCallbackPublisher() *cfg.SqsPublisher {
	return obj._KaleyraSmsCallbackPublisher
}
func (obj *Config) AclWhatsappCallbackPublisher() *cfg.SqsPublisher {
	return obj._AclWhatsappCallbackPublisher
}
func (obj *Config) AclWhatsappReplyPublisher() *cfg.SqsPublisher {
	return obj._AclWhatsappReplyPublisher
}
func (obj *Config) GupshupWhatsappCallbackPublisher() *cfg.SqsPublisher {
	return obj._GupshupWhatsappCallbackPublisher
}
func (obj *Config) GupshupRcsCallbackPublisher() *cfg.SqsPublisher {
	return obj._GupshupRcsCallbackPublisher
}
func (obj *Config) NetCoreSmsCallbackPublisher() *cfg.SqsPublisher {
	return obj._NetCoreSmsCallbackPublisher
}
func (obj *Config) AirtelSmsCallbackPublisher() *cfg.SqsPublisher {
	return obj._AirtelSmsCallbackPublisher
}
func (obj *Config) DeviceReRegCallbackPublisher() *cfg.SqsPublisher {
	return obj._DeviceReRegCallbackPublisher
}
func (obj *Config) DeviceRegSMSAckPublisher() *cfg.SqsPublisher {
	return obj._DeviceRegSMSAckPublisher
}
func (obj *Config) CustomerCreationCallbackPublisher() *cfg.SqsPublisher {
	return obj._CustomerCreationCallbackPublisher
}
func (obj *Config) BankCustCallbackPublisher() *cfg.SqsPublisher {
	return obj._BankCustCallbackPublisher
}
func (obj *Config) AccountCreationCallbackPublisher() *cfg.SqsPublisher {
	return obj._AccountCreationCallbackPublisher
}
func (obj *Config) FederalVkycUpdatePublisher() *cfg.SqsPublisher {
	return obj._FederalVkycUpdatePublisher
}
func (obj *Config) KarzaVkycCallEventPublisher() *cfg.SqsPublisher {
	return obj._KarzaVkycCallEventPublisher
}
func (obj *Config) KarzaVkycAgentResponsePublisher() *cfg.SqsPublisher {
	return obj._KarzaVkycAgentResponsePublisher
}
func (obj *Config) KarzaVkycAuditorResponsePublisher() *cfg.SqsPublisher {
	return obj._KarzaVkycAuditorResponsePublisher
}
func (obj *Config) EmailCallbackPublisher() *cfg.SqsPublisher {
	return obj._EmailCallbackPublisher
}
func (obj *Config) ConsentCallbackPublisher() *cfg.SqsPublisher {
	return obj._ConsentCallbackPublisher
}
func (obj *Config) FICallbackPublisher() *cfg.SqsPublisher {
	return obj._FICallbackPublisher
}
func (obj *Config) AccountLinkStatusCallbackPublisher() *cfg.SqsPublisher {
	return obj._AccountLinkStatusCallbackPublisher
}
func (obj *Config) CardTrackingCallbackPublisher() *cfg.SqsPublisher {
	return obj._CardTrackingCallbackPublisher
}
func (obj *Config) UPIReqTxnConfirmationComplaintEventPublisher() *cfg.SqsPublisher {
	return obj._UPIReqTxnConfirmationComplaintEventPublisher
}
func (obj *Config) OzonetelCallDetailsPublisher() *cfg.SqsPublisher {
	return obj._OzonetelCallDetailsPublisher
}
func (obj *Config) UPIRespComplaintEventPublisher() *cfg.SqsPublisher {
	return obj._UPIRespComplaintEventPublisher
}
func (obj *Config) FreshchatActionCallbackPublisher() *cfg.SqsPublisher {
	return obj._FreshchatActionCallbackPublisher
}
func (obj *Config) CCTransactionNotificationPublisher() *cfg.SqsPublisher {
	return obj._CCTransactionNotificationPublisher
}
func (obj *Config) CCStatementNotificationPublisher() *cfg.SqsPublisher {
	return obj._CCStatementNotificationPublisher
}
func (obj *Config) CCAcsNotificationPublisher() *cfg.SqsPublisher {
	return obj._CCAcsNotificationPublisher
}
func (obj *Config) TssWebhookCallBackPublisher() *cfg.SqsPublisher {
	return obj._TssWebhookCallBackPublisher
}
func (obj *Config) HealthInsurancePolicyIssuanceEventPublisher() *cfg.SqsPublisher {
	return obj._HealthInsurancePolicyIssuanceEventPublisher
}
func (obj *Config) CCNonFinancialNotificationPublisher() *cfg.SqsPublisher {
	return obj._CCNonFinancialNotificationPublisher
}
func (obj *Config) SmallcaseProcessMFHoldingsWebhookPublisher() *cfg.ExtendedSqsPublisher {
	return obj._SmallcaseProcessMFHoldingsWebhookPublisher
}
func (obj *Config) SignalWorkflowPublisher() *cfg.SqsPublisher {
	return obj._SignalWorkflowPublisher
}
func (obj *Config) LoansFiftyfinCallbackPublisher() *cfg.SqsPublisher {
	return obj._LoansFiftyfinCallbackPublisher
}
func (obj *Config) AirtelWhatsappCallbackPublisher() *cfg.SqsPublisher {
	return obj._AirtelWhatsappCallbackPublisher
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}
func (obj *Config) FederalUPISignatureVerifier() *cfg.XMLDigitalSignatureVerifier {
	return obj._FederalUPISignatureVerifier
}
func (obj *Config) PayFundTransferStatusCodeJson() string {
	return obj._PayFundTransferStatusCodeJson
}
func (obj *Config) PayUpiStatusCodeJson() string {
	return obj._PayUpiStatusCodeJson
}
func (obj *Config) EnachTransactionStatusCodeJson() string {
	return obj._EnachTransactionStatusCodeJson
}
func (obj *Config) DepositResponseStatusCodeFilePath() string {
	return obj._DepositResponseStatusCodeFilePath
}
func (obj *Config) CardResponseStatusCodeFilePath() string {
	return obj._CardResponseStatusCodeFilePath
}
func (obj *Config) SecureLogging() *config.SecureLogging {
	return obj._SecureLogging
}
func (obj *Config) Logging() *cfg.Logging {
	return obj._Logging
}
func (obj *Config) SyncRespHandler() *response.SyncRespHandler {
	return obj._SyncRespHandler
}
func (obj *Config) KarzaVkycWhitelist() *config.IPWhiteListing {
	return obj._KarzaVkycWhitelist
}
func (obj *Config) AclSmsWhitelist() *config.IPWhiteListing {
	return obj._AclSmsWhitelist
}
func (obj *Config) KaleyraSmsWhitelist() *config.IPWhiteListing {
	return obj._KaleyraSmsWhitelist
}
func (obj *Config) AclWhatsappWhitelist() *config.IPWhiteListing {
	return obj._AclWhatsappWhitelist
}
func (obj *Config) NetCoreWhitelist() *config.IPWhiteListing {
	return obj._NetCoreWhitelist
}
func (obj *Config) AirtelWhitelist() *config.IPWhiteListing {
	return obj._AirtelWhitelist
}
func (obj *Config) UPIWhitelist() *config.IPWhiteListing {
	return obj._UPIWhitelist
}
func (obj *Config) OzonetelWhitelist() *config.IPWhiteListing {
	return obj._OzonetelWhitelist
}
func (obj *Config) FreshchatWhitelist() *config.IPWhiteListing {
	return obj._FreshchatWhitelist
}
func (obj *Config) SenseforthWhitelist() *config.IPWhiteListing {
	return obj._SenseforthWhitelist
}
func (obj *Config) TssWhitelist() *config.IPWhiteListing {
	return obj._TssWhitelist
}
func (obj *Config) RiskcovryWhitelist() *config.IPWhiteListing {
	return obj._RiskcovryWhitelist
}
func (obj *Config) SmallcaseWhitelist() *config.IPWhiteListing {
	return obj._SmallcaseWhitelist
}
func (obj *Config) SprinklrWhitelist() *config.IPWhiteListing {
	return obj._SprinklrWhitelist
}
func (obj *Config) KarzaEPANWhitelist() *config.IPWhiteListing {
	return obj._KarzaEPANWhitelist
}
func (obj *Config) M2PWhitelist() *config.IPWhiteListing {
	return obj._M2PWhitelist
}
func (obj *Config) FiftyfinWhitelist() *config.IPWhiteListing {
	return obj._FiftyfinWhitelist
}
func (obj *Config) MoneyviewWhiteList() *config.IPWhiteListing {
	return obj._MoneyviewWhiteList
}
func (obj *Config) IrisWhitelist() *config.IPWhiteListing {
	return obj._IrisWhitelist
}
func (obj *Config) AbflWhitelist() *config.IPWhiteListing {
	return obj._AbflWhitelist
}
func (obj *Config) CredgenicsWhitelist() *config.IPWhiteListing {
	return obj._CredgenicsWhitelist
}
func (obj *Config) DPandaWhitelist() *config.IPWhiteListing {
	return obj._DPandaWhitelist
}
func (obj *Config) PoshVineWhitelist() *config.IPWhiteListing {
	return obj._PoshVineWhitelist
}
func (obj *Config) GupshupWhitelist() *config.IPWhiteListing {
	return obj._GupshupWhitelist
}
func (obj *Config) PaisabazaarWhitelist() *config.IPWhiteListing {
	return obj._PaisabazaarWhitelist
}
func (obj *Config) SetuWhiteList() *config.IPWhiteListing {
	return obj._SetuWhiteList
}
func (obj *Config) FederalWhiteList() *config.IPWhiteListing {
	return obj._FederalWhiteList
}
func (obj *Config) SavenWhiteList() *config.IPWhiteListing {
	return obj._SavenWhiteList
}
func (obj *Config) LeadsWhiteList() *config.IPWhiteListing {
	return obj._LeadsWhiteList
}
func (obj *Config) AuthWhiteList() *config.IPWhiteListing {
	return obj._AuthWhiteList
}
func (obj *Config) NuggetWhiteList() *config.IPWhiteListing {
	return obj._NuggetWhiteList
}
func (obj *Config) NumberOfHopsThatAddXForwardedFor() int {
	return obj._NumberOfHopsThatAddXForwardedFor
}
func (obj *Config) VpcCidrIPPrefix() string {
	return obj._VpcCidrIPPrefix
}
func (obj *Config) Ozonetel() *config.Ozonetel {
	return obj._Ozonetel
}
func (obj *Config) Tracing() *cfg.Tracing {
	return obj._Tracing
}
func (obj *Config) Profiling() *cfg.Profiling {
	return obj._Profiling
}
func (obj *Config) Chatbot() *config.Chatbot {
	return obj._Chatbot
}
func (obj *Config) RateLimitConfig() *cfg.RateLimitConfig {
	return obj._RateLimitConfig
}
func (obj *Config) Freshdesk() *cfg.Freshdesk {
	return obj._Freshdesk
}
func (obj *Config) CardSwitchFinancialNotificationPublisher() *cfg.SqsPublisher {
	return obj._CardSwitchFinancialNotificationPublisher
}
func (obj *Config) CardSwitchNonFinancialNotificationPublisher() *cfg.SqsPublisher {
	return obj._CardSwitchNonFinancialNotificationPublisher
}
func (obj *Config) AccountStatusCallBackPublisher() *cfg.SqsPublisher {
	return obj._AccountStatusCallBackPublisher
}
func (obj *Config) UPIReqMapperConfirmationEventPublisher() *cfg.SqsPublisher {
	return obj._UPIReqMapperConfirmationEventPublisher
}
func (obj *Config) EnachRegistrationAuthorisationCallbackPublisher() *cfg.SqsPublisher {
	return obj._EnachRegistrationAuthorisationCallbackPublisher
}
func (obj *Config) ProcrastinatorWorkflowPublisher() *cfg.SqsPublisher {
	return obj._ProcrastinatorWorkflowPublisher
}
func (obj *Config) QuestRedisOptions() *cfg.RedisOptions {
	return obj._QuestRedisOptions
}
func (obj *Config) KycStatusUpdatePublisher() *cfg.SqsPublisher {
	return obj._KycStatusUpdatePublisher
}
func (obj *Config) CcSwitchNotificationsBucketName() string {
	return obj._CcSwitchNotificationsBucketName
}
func (obj *Config) CcRawSwitchNotificationsBucketName() string {
	return obj._CcRawSwitchNotificationsBucketName
}
func (obj *Config) EpanCallbackBucketName() string {
	return obj._EpanCallbackBucketName
}
func (obj *Config) M2pFederalSwitchNotificationFilePath() string {
	return obj._M2pFederalSwitchNotificationFilePath
}
func (obj *Config) RawBucketM2pFederalSwitchNotificationFilePath() string {
	return obj._RawBucketM2pFederalSwitchNotificationFilePath
}
func (obj *Config) DpandaVnSecrets() *config.FiStoreVendorSecrets {
	return obj._DpandaVnSecrets
}
func (obj *Config) PoshvineVnSecrets() *config.FiStoreVendorSecrets {
	return obj._PoshvineVnSecrets
}
func (obj *Config) RazorpayVnSecrets() *config.FiStoreVendorSecrets {
	return obj._RazorpayVnSecrets
}
func (obj *Config) CredgenicsCallbackStreamProducer() *config.CredgenicsCallbackStreamProducer {
	return obj._CredgenicsCallbackStreamProducer
}
func (obj *Config) FederalBankCustKycStateChangePublisher() *cfg.SqsPublisher {
	return obj._FederalBankCustKycStateChangePublisher
}
func (obj *Config) FederalResidentialStatusUpdatePublisher() *cfg.SqsPublisher {
	return obj._FederalResidentialStatusUpdatePublisher
}
func (obj *Config) FederalMobileNumberUpdatePublisher() *cfg.SqsPublisher {
	return obj._FederalMobileNumberUpdatePublisher
}
func (obj *Config) PgRazorpayInboundEventPublisher() *cfg.SqsPublisher {
	return obj._PgRazorpayInboundEventPublisher
}
func (obj *Config) FederalEscalationUpdateEventPublisher() *cfg.SqsPublisher {
	return obj._FederalEscalationUpdateEventPublisher
}
func (obj *Config) CcOnboardingStateUpdateEventPublisher() *cfg.SqsPublisher {
	return obj._CcOnboardingStateUpdateEventPublisher
}
func (obj *Config) Auth() *config.Auth {
	return obj._Auth
}
func (obj *Config) VendorRewardFulfillmentPublisher() *cfg.SqsPublisher {
	return obj._VendorRewardFulfillmentPublisher
}
func (obj *Config) SavenRewardVnSecrets() *config.SavenRewardVnSecrets {
	return obj._SavenRewardVnSecrets
}

type Flags struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Flag to determine if cc transaction notification will be processed via temporal
	// If enabled we will use the faas executor to execute the transaction notification activity
	_EnableCCTransactionProcessingViaTemporal uint32
	// EnableNewEndpointInboundNotification flag to enable the inbound notification from new endpoint.
	// True means notification from new endpoint will be routed and old endpoint notification will be disabled.
	// False means old will be enabled and new end point will not be routed.
	_EnableNewEndpointInboundNotification uint32
	_TrimDebugMessageFromStatus           bool
}

// Flag to determine if cc transaction notification will be processed via temporal
// If enabled we will use the faas executor to execute the transaction notification activity
func (obj *Flags) EnableCCTransactionProcessingViaTemporal() bool {
	if atomic.LoadUint32(&obj._EnableCCTransactionProcessingViaTemporal) == 0 {
		return false
	} else {
		return true
	}
}

// EnableNewEndpointInboundNotification flag to enable the inbound notification from new endpoint.
// True means notification from new endpoint will be routed and old endpoint notification will be disabled.
// False means old will be enabled and new end point will not be routed.
func (obj *Flags) EnableNewEndpointInboundNotification() bool {
	if atomic.LoadUint32(&obj._EnableNewEndpointInboundNotification) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) TrimDebugMessageFromStatus() bool {
	return obj._TrimDebugMessageFromStatus
}

type FeatureFlags struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_AllowCustomerCallbackProcessing uint32
	_AllowAccountCallbackProcessing  bool
}

func (obj *FeatureFlags) AllowCustomerCallbackProcessing() bool {
	if atomic.LoadUint32(&obj._AllowCustomerCallbackProcessing) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *FeatureFlags) AllowAccountCallbackProcessing() bool {
	return obj._AllowAccountCallbackProcessing
}

type AA struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_VerifyApiKeyAndJws         uint32
	_TokenIssuer                string
	_TokenIssuerMutex           *sync.RWMutex
	_OneMoneyCrId               string
	_OneMoneyCrIdMutex          *sync.RWMutex
	_FinvuCrId                  string
	_FinvuCrIdMutex             *sync.RWMutex
	_EpifiAaKid                 string
	_EpifiAaKidMutex            *sync.RWMutex
	_AaSecretsVersionToUse      string
	_AaSecretsVersionToUseMutex *sync.RWMutex
	_SahamatiPublicKey          string
	_IPWhiteListing             *config.IPWhiteListing
	_AaVgVnSecretsV1            *config.AaVgVnSecrets
	_AaVgVnSecretsV2            *config.AaVgVnSecrets
}

func (obj *AA) VerifyApiKeyAndJws() bool {
	if atomic.LoadUint32(&obj._VerifyApiKeyAndJws) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *AA) TokenIssuer() string {
	obj._TokenIssuerMutex.RLock()
	defer obj._TokenIssuerMutex.RUnlock()
	return obj._TokenIssuer
}
func (obj *AA) OneMoneyCrId() string {
	obj._OneMoneyCrIdMutex.RLock()
	defer obj._OneMoneyCrIdMutex.RUnlock()
	return obj._OneMoneyCrId
}
func (obj *AA) FinvuCrId() string {
	obj._FinvuCrIdMutex.RLock()
	defer obj._FinvuCrIdMutex.RUnlock()
	return obj._FinvuCrId
}
func (obj *AA) EpifiAaKid() string {
	obj._EpifiAaKidMutex.RLock()
	defer obj._EpifiAaKidMutex.RUnlock()
	return obj._EpifiAaKid
}
func (obj *AA) AaSecretsVersionToUse() string {
	obj._AaSecretsVersionToUseMutex.RLock()
	defer obj._AaSecretsVersionToUseMutex.RUnlock()
	return obj._AaSecretsVersionToUse
}
func (obj *AA) SahamatiPublicKey() string {
	return obj._SahamatiPublicKey
}
func (obj *AA) IPWhiteListing() *config.IPWhiteListing {
	return obj._IPWhiteListing
}
func (obj *AA) AaVgVnSecretsV1() *config.AaVgVnSecrets {
	return obj._AaVgVnSecretsV1
}
func (obj *AA) AaVgVnSecretsV2() *config.AaVgVnSecrets {
	return obj._AaVgVnSecretsV2
}

type Reward struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_DisableDummyApiResponseFlow      uint32
	_RewardTypeToMaxRewardValueMap    *syncmap.Map[string, float64]
	_DummyRewardProcessingStatus      string
	_DummyRewardProcessingStatusMutex *sync.RWMutex
}

func (obj *Reward) DisableDummyApiResponseFlow() bool {
	if atomic.LoadUint32(&obj._DisableDummyApiResponseFlow) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Reward) RewardTypeToMaxRewardValueMap() *syncmap.Map[string, float64] {
	return obj._RewardTypeToMaxRewardValueMap
}
func (obj *Reward) DummyRewardProcessingStatus() string {
	obj._DummyRewardProcessingStatusMutex.RLock()
	defer obj._DummyRewardProcessingStatusMutex.RUnlock()
	return obj._DummyRewardProcessingStatus
}

type Nugget struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_NuggetAccountFreezeDummyDetails *syncmap.Map[string, *NuggetAccountFreezeDummyDetails]
	_NuggetTransactionDummyDetails   *syncmap.Map[string, *NuggetTransactionDummyDetails]
	_NuggetUserMockAPIProtoJsonResp  *syncmap.Map[string, string]
}

func (obj *Nugget) NuggetAccountFreezeDummyDetails() *syncmap.Map[string, *NuggetAccountFreezeDummyDetails] {
	return obj._NuggetAccountFreezeDummyDetails
}
func (obj *Nugget) NuggetTransactionDummyDetails() *syncmap.Map[string, *NuggetTransactionDummyDetails] {
	return obj._NuggetTransactionDummyDetails
}
func (obj *Nugget) NuggetUserMockAPIProtoJsonResp() *syncmap.Map[string, string] {
	return obj._NuggetUserMockAPIProtoJsonResp
}

type NuggetAccountFreezeDummyDetails struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_AccountStatus              string
	_AccountStatusMutex         *sync.RWMutex
	_ProcessedFreezeReason      string
	_ProcessedFreezeReasonMutex *sync.RWMutex
	_FreezeType                 string
	_FreezeTypeMutex            *sync.RWMutex
	_FormId                     string
	_FormIdMutex                *sync.RWMutex
	_FormStatus                 string
	_FormStatusMutex            *sync.RWMutex
	_FormExpiryDate             string
	_FormExpiryDateMutex        *sync.RWMutex
	_LeaComplaintDetails        string
	_LeaComplaintDetailsMutex   *sync.RWMutex
}

func (obj *NuggetAccountFreezeDummyDetails) AccountStatus() string {
	obj._AccountStatusMutex.RLock()
	defer obj._AccountStatusMutex.RUnlock()
	return obj._AccountStatus
}
func (obj *NuggetAccountFreezeDummyDetails) ProcessedFreezeReason() string {
	obj._ProcessedFreezeReasonMutex.RLock()
	defer obj._ProcessedFreezeReasonMutex.RUnlock()
	return obj._ProcessedFreezeReason
}
func (obj *NuggetAccountFreezeDummyDetails) FreezeType() string {
	obj._FreezeTypeMutex.RLock()
	defer obj._FreezeTypeMutex.RUnlock()
	return obj._FreezeType
}
func (obj *NuggetAccountFreezeDummyDetails) FormId() string {
	obj._FormIdMutex.RLock()
	defer obj._FormIdMutex.RUnlock()
	return obj._FormId
}
func (obj *NuggetAccountFreezeDummyDetails) FormStatus() string {
	obj._FormStatusMutex.RLock()
	defer obj._FormStatusMutex.RUnlock()
	return obj._FormStatus
}
func (obj *NuggetAccountFreezeDummyDetails) FormExpiryDate() string {
	obj._FormExpiryDateMutex.RLock()
	defer obj._FormExpiryDateMutex.RUnlock()
	return obj._FormExpiryDate
}
func (obj *NuggetAccountFreezeDummyDetails) LeaComplaintDetails() string {
	obj._LeaComplaintDetailsMutex.RLock()
	defer obj._LeaComplaintDetailsMutex.RUnlock()
	return obj._LeaComplaintDetails
}

type NuggetTransactionDummyDetails struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_CreatedAt                  string
	_CreatedAtMutex             *sync.RWMutex
	_ErrorCode                  string
	_ErrorCodeMutex             *sync.RWMutex
	_ExecutedAt                 string
	_ExecutedAtMutex            *sync.RWMutex
	_P2P_P2M                    string
	_P2P_P2MMutex               *sync.RWMutex
	_PaymentProtocol            string
	_PaymentProtocolMutex       *sync.RWMutex
	_Provenance                 string
	_ProvenanceMutex            *sync.RWMutex
	_Tags                       string
	_TagsMutex                  *sync.RWMutex
	_TransactionAmount          string
	_TransactionAmountMutex     *sync.RWMutex
	_TransactionStatus          string
	_TransactionStatusMutex     *sync.RWMutex
	_CreatedAtReadableTime      string
	_CreatedAtReadableTimeMutex *sync.RWMutex
}

func (obj *NuggetTransactionDummyDetails) CreatedAt() string {
	obj._CreatedAtMutex.RLock()
	defer obj._CreatedAtMutex.RUnlock()
	return obj._CreatedAt
}
func (obj *NuggetTransactionDummyDetails) ErrorCode() string {
	obj._ErrorCodeMutex.RLock()
	defer obj._ErrorCodeMutex.RUnlock()
	return obj._ErrorCode
}
func (obj *NuggetTransactionDummyDetails) ExecutedAt() string {
	obj._ExecutedAtMutex.RLock()
	defer obj._ExecutedAtMutex.RUnlock()
	return obj._ExecutedAt
}
func (obj *NuggetTransactionDummyDetails) P2P_P2M() string {
	obj._P2P_P2MMutex.RLock()
	defer obj._P2P_P2MMutex.RUnlock()
	return obj._P2P_P2M
}
func (obj *NuggetTransactionDummyDetails) PaymentProtocol() string {
	obj._PaymentProtocolMutex.RLock()
	defer obj._PaymentProtocolMutex.RUnlock()
	return obj._PaymentProtocol
}
func (obj *NuggetTransactionDummyDetails) Provenance() string {
	obj._ProvenanceMutex.RLock()
	defer obj._ProvenanceMutex.RUnlock()
	return obj._Provenance
}
func (obj *NuggetTransactionDummyDetails) Tags() string {
	obj._TagsMutex.RLock()
	defer obj._TagsMutex.RUnlock()
	return obj._Tags
}
func (obj *NuggetTransactionDummyDetails) TransactionAmount() string {
	obj._TransactionAmountMutex.RLock()
	defer obj._TransactionAmountMutex.RUnlock()
	return obj._TransactionAmount
}
func (obj *NuggetTransactionDummyDetails) TransactionStatus() string {
	obj._TransactionStatusMutex.RLock()
	defer obj._TransactionStatusMutex.RUnlock()
	return obj._TransactionStatus
}
func (obj *NuggetTransactionDummyDetails) CreatedAtReadableTime() string {
	obj._CreatedAtReadableTimeMutex.RLock()
	defer obj._CreatedAtReadableTimeMutex.RUnlock()
	return obj._CreatedAtReadableTime
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enableindianstockscontentapi"] = _obj.SetEnableIndianStocksContentApi
	_setters["dummyquestvariable"] = _obj.SetDummyQuestVariable
	_obj._DummyQuestVariableMutex = &sync.RWMutex{}
	_Flags, _fieldSetters := NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_FeatureFlags, _fieldSetters := NewFeatureFlags()
	_obj._FeatureFlags = _FeatureFlags
	helper.AddFieldSetters("featureflags", _fieldSetters, _setters)
	_AA, _fieldSetters := NewAA()
	_obj._AA = _AA
	helper.AddFieldSetters("aa", _fieldSetters, _setters)
	_QuestSdk, _fieldSetters := genconfig.NewConfig()
	_obj._QuestSdk = _QuestSdk
	helper.AddFieldSetters("questsdk", _fieldSetters, _setters)
	_Reward, _fieldSetters := NewReward()
	_obj._Reward = _Reward
	helper.AddFieldSetters("reward", _fieldSetters, _setters)
	_Nugget, _fieldSetters := NewNugget()
	_obj._Nugget = _Nugget
	helper.AddFieldSetters("nugget", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enableindianstockscontentapi"] = _obj.SetEnableIndianStocksContentApi
	_setters["dummyquestvariable"] = _obj.SetDummyQuestVariable
	_obj._DummyQuestVariableMutex = &sync.RWMutex{}
	_Flags, _fieldSetters := NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_FeatureFlags, _fieldSetters := NewFeatureFlags()
	_obj._FeatureFlags = _FeatureFlags
	helper.AddFieldSetters("featureflags", _fieldSetters, _setters)
	_AA, _fieldSetters := NewAA()
	_obj._AA = _AA
	helper.AddFieldSetters("aa", _fieldSetters, _setters)
	_QuestSdk, _fieldSetters := genconfig.NewConfig()
	_obj._QuestSdk = _QuestSdk
	helper.AddFieldSetters("questsdk", _fieldSetters, _setters)
	_Reward, _fieldSetters := NewReward()
	_obj._Reward = _Reward
	helper.AddFieldSetters("reward", _fieldSetters, _setters)
	_Nugget, _fieldSetters := NewNugget()
	_obj._Nugget = _Nugget
	helper.AddFieldSetters("nugget", _fieldSetters, _setters)
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *Config) Init(questFieldPath string) {
	newObj, _ := NewConfig()
	*obj = *newObj
}
func (obj *Config) InitWithQuest(questFieldPath string) {
	newObj, _ := NewConfigWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "DummyQuestVariable",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *config.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enableindianstockscontentapi":
		return obj.SetEnableIndianStocksContentApi(v.EnableIndianStocksContentApi, true, nil)
	case "dummyquestvariable":
		return obj.SetDummyQuestVariable(v.DummyQuestVariable, true, nil)
	case "flags":
		return obj._Flags.Set(v.Flags, true, path)
	case "featureflags":
		return obj._FeatureFlags.Set(v.FeatureFlags, true, path)
	case "aa":
		return obj._AA.Set(v.AA, true, path)
	case "questsdk":
		return obj._QuestSdk.Set(v.QuestSdk, true, path)
	case "reward":
		return obj._Reward.Set(v.Reward, true, path)
	case "nugget":
		return obj._Nugget.Set(v.Nugget, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *config.Config, dynamic bool, path []string) (err error) {

	err = obj.SetEnableIndianStocksContentApi(v.EnableIndianStocksContentApi, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDummyQuestVariable(v.DummyQuestVariable, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._Flags.Set(v.Flags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FeatureFlags.Set(v.FeatureFlags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AA.Set(v.AA, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._QuestSdk.Set(v.QuestSdk, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Reward.Set(v.Reward, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Nugget.Set(v.Nugget, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *config.Config) error {

	obj._Application = v.Application
	obj._Server = v.Server
	obj._AWS = v.AWS
	obj._UpdateTransactionEventsPublisher = v.UpdateTransactionEventsPublisher
	obj._InboundTxnPublisher = v.InboundTxnPublisher
	obj._InboundUpiTxnPublisher = v.InboundUpiTxnPublisher
	obj._InboundLoanTxnPublisher = v.InboundLoanTxnPublisher
	obj._GrpcRateLimiterParams = v.GrpcRateLimiterParams
	obj._SyncWrapperPublisher = v.SyncWrapperPublisher
	obj._CreateCardCallbackPublisher = v.CreateCardCallbackPublisher
	obj._DispatchPhysicalCardCallbackPublisher = v.DispatchPhysicalCardCallbackPublisher
	obj._CheckLivenessCallbackPublisher = v.CheckLivenessCallbackPublisher
	obj._UpdateShippingAddressCallbackPublisher = v.UpdateShippingAddressCallbackPublisher
	obj._UPIReqAuthEventPublisher = v.UPIReqAuthEventPublisher
	obj._UPIReqAuthMandateEventPublisher = v.UPIReqAuthMandateEventPublisher
	obj._UPIReqAuthValCustEventPublisher = v.UPIReqAuthValCustEventPublisher
	obj._UPIReqMandateConfirmationEventPublisher = v.UPIReqMandateConfirmationEventPublisher
	obj._UPIRespPayEventPublisher = v.UPIRespPayEventPublisher
	obj._UPIRespMandateEventPublisher = v.UPIRespMandateEventPublisher
	obj._UPIReqTxnConfirmationEventPublisher = v.UPIReqTxnConfirmationEventPublisher
	obj._UPIReqValAddressEventPublisher = v.UPIReqValAddressEventPublisher
	obj._UPIListPspKeysEventPublisher = v.UPIListPspKeysEventPublisher
	obj._UPIListVaePublisher = v.UPIListVaePublisher
	obj._CreateDepositCallbackPublisher = v.CreateDepositCallbackPublisher
	obj._PreCloseDepositCallbackPublisher = v.PreCloseDepositCallbackPublisher
	obj._FdAutoRenewCallbackPublisher = v.FdAutoRenewCallbackPublisher
	obj._AclSmsCallbackPublisher = v.AclSmsCallbackPublisher
	obj._KaleyraSmsCallbackPublisher = v.KaleyraSmsCallbackPublisher
	obj._AclWhatsappCallbackPublisher = v.AclWhatsappCallbackPublisher
	obj._AclWhatsappReplyPublisher = v.AclWhatsappReplyPublisher
	obj._GupshupWhatsappCallbackPublisher = v.GupshupWhatsappCallbackPublisher
	obj._GupshupRcsCallbackPublisher = v.GupshupRcsCallbackPublisher
	obj._NetCoreSmsCallbackPublisher = v.NetCoreSmsCallbackPublisher
	obj._AirtelSmsCallbackPublisher = v.AirtelSmsCallbackPublisher
	obj._DeviceReRegCallbackPublisher = v.DeviceReRegCallbackPublisher
	obj._DeviceRegSMSAckPublisher = v.DeviceRegSMSAckPublisher
	obj._CustomerCreationCallbackPublisher = v.CustomerCreationCallbackPublisher
	obj._BankCustCallbackPublisher = v.BankCustCallbackPublisher
	obj._AccountCreationCallbackPublisher = v.AccountCreationCallbackPublisher
	obj._FederalVkycUpdatePublisher = v.FederalVkycUpdatePublisher
	obj._KarzaVkycCallEventPublisher = v.KarzaVkycCallEventPublisher
	obj._KarzaVkycAgentResponsePublisher = v.KarzaVkycAgentResponsePublisher
	obj._KarzaVkycAuditorResponsePublisher = v.KarzaVkycAuditorResponsePublisher
	obj._EmailCallbackPublisher = v.EmailCallbackPublisher
	obj._ConsentCallbackPublisher = v.ConsentCallbackPublisher
	obj._FICallbackPublisher = v.FICallbackPublisher
	obj._AccountLinkStatusCallbackPublisher = v.AccountLinkStatusCallbackPublisher
	obj._CardTrackingCallbackPublisher = v.CardTrackingCallbackPublisher
	obj._UPIReqTxnConfirmationComplaintEventPublisher = v.UPIReqTxnConfirmationComplaintEventPublisher
	obj._OzonetelCallDetailsPublisher = v.OzonetelCallDetailsPublisher
	obj._UPIRespComplaintEventPublisher = v.UPIRespComplaintEventPublisher
	obj._FreshchatActionCallbackPublisher = v.FreshchatActionCallbackPublisher
	obj._CCTransactionNotificationPublisher = v.CCTransactionNotificationPublisher
	obj._CCStatementNotificationPublisher = v.CCStatementNotificationPublisher
	obj._CCAcsNotificationPublisher = v.CCAcsNotificationPublisher
	obj._TssWebhookCallBackPublisher = v.TssWebhookCallBackPublisher
	obj._HealthInsurancePolicyIssuanceEventPublisher = v.HealthInsurancePolicyIssuanceEventPublisher
	obj._CCNonFinancialNotificationPublisher = v.CCNonFinancialNotificationPublisher
	obj._SmallcaseProcessMFHoldingsWebhookPublisher = v.SmallcaseProcessMFHoldingsWebhookPublisher
	obj._SignalWorkflowPublisher = v.SignalWorkflowPublisher
	obj._LoansFiftyfinCallbackPublisher = v.LoansFiftyfinCallbackPublisher
	obj._AirtelWhatsappCallbackPublisher = v.AirtelWhatsappCallbackPublisher
	obj._Secrets = v.Secrets
	obj._FederalUPISignatureVerifier = v.FederalUPISignatureVerifier
	obj._PayFundTransferStatusCodeJson = v.PayFundTransferStatusCodeJson
	obj._PayUpiStatusCodeJson = v.PayUpiStatusCodeJson
	obj._EnachTransactionStatusCodeJson = v.EnachTransactionStatusCodeJson
	obj._DepositResponseStatusCodeFilePath = v.DepositResponseStatusCodeFilePath
	obj._CardResponseStatusCodeFilePath = v.CardResponseStatusCodeFilePath
	obj._SecureLogging = v.SecureLogging
	obj._Logging = v.Logging
	obj._SyncRespHandler = v.SyncRespHandler
	obj._KarzaVkycWhitelist = v.KarzaVkycWhitelist
	obj._AclSmsWhitelist = v.AclSmsWhitelist
	obj._KaleyraSmsWhitelist = v.KaleyraSmsWhitelist
	obj._AclWhatsappWhitelist = v.AclWhatsappWhitelist
	obj._NetCoreWhitelist = v.NetCoreWhitelist
	obj._AirtelWhitelist = v.AirtelWhitelist
	obj._UPIWhitelist = v.UPIWhitelist
	obj._OzonetelWhitelist = v.OzonetelWhitelist
	obj._FreshchatWhitelist = v.FreshchatWhitelist
	obj._SenseforthWhitelist = v.SenseforthWhitelist
	obj._TssWhitelist = v.TssWhitelist
	obj._RiskcovryWhitelist = v.RiskcovryWhitelist
	obj._SmallcaseWhitelist = v.SmallcaseWhitelist
	obj._SprinklrWhitelist = v.SprinklrWhitelist
	obj._KarzaEPANWhitelist = v.KarzaEPANWhitelist
	obj._M2PWhitelist = v.M2PWhitelist
	obj._FiftyfinWhitelist = v.FiftyfinWhitelist
	obj._MoneyviewWhiteList = v.MoneyviewWhiteList
	obj._IrisWhitelist = v.IrisWhitelist
	obj._AbflWhitelist = v.AbflWhitelist
	obj._CredgenicsWhitelist = v.CredgenicsWhitelist
	obj._DPandaWhitelist = v.DPandaWhitelist
	obj._PoshVineWhitelist = v.PoshVineWhitelist
	obj._GupshupWhitelist = v.GupshupWhitelist
	obj._PaisabazaarWhitelist = v.PaisabazaarWhitelist
	obj._SetuWhiteList = v.SetuWhiteList
	obj._FederalWhiteList = v.FederalWhiteList
	obj._SavenWhiteList = v.SavenWhiteList
	obj._LeadsWhiteList = v.LeadsWhiteList
	obj._AuthWhiteList = v.AuthWhiteList
	obj._NuggetWhiteList = v.NuggetWhiteList
	obj._NumberOfHopsThatAddXForwardedFor = v.NumberOfHopsThatAddXForwardedFor
	obj._VpcCidrIPPrefix = v.VpcCidrIPPrefix
	obj._Ozonetel = v.Ozonetel
	obj._Tracing = v.Tracing
	obj._Profiling = v.Profiling
	obj._Chatbot = v.Chatbot
	obj._RateLimitConfig = v.RateLimitConfig
	obj._Freshdesk = v.Freshdesk
	obj._CardSwitchFinancialNotificationPublisher = v.CardSwitchFinancialNotificationPublisher
	obj._CardSwitchNonFinancialNotificationPublisher = v.CardSwitchNonFinancialNotificationPublisher
	obj._AccountStatusCallBackPublisher = v.AccountStatusCallBackPublisher
	obj._UPIReqMapperConfirmationEventPublisher = v.UPIReqMapperConfirmationEventPublisher
	obj._EnachRegistrationAuthorisationCallbackPublisher = v.EnachRegistrationAuthorisationCallbackPublisher
	obj._ProcrastinatorWorkflowPublisher = v.ProcrastinatorWorkflowPublisher
	obj._QuestRedisOptions = v.QuestRedisOptions
	obj._KycStatusUpdatePublisher = v.KycStatusUpdatePublisher
	obj._CcSwitchNotificationsBucketName = v.CcSwitchNotificationsBucketName
	obj._CcRawSwitchNotificationsBucketName = v.CcRawSwitchNotificationsBucketName
	obj._EpanCallbackBucketName = v.EpanCallbackBucketName
	obj._M2pFederalSwitchNotificationFilePath = v.M2pFederalSwitchNotificationFilePath
	obj._RawBucketM2pFederalSwitchNotificationFilePath = v.RawBucketM2pFederalSwitchNotificationFilePath
	obj._DpandaVnSecrets = v.DpandaVnSecrets
	obj._PoshvineVnSecrets = v.PoshvineVnSecrets
	obj._RazorpayVnSecrets = v.RazorpayVnSecrets
	obj._CredgenicsCallbackStreamProducer = v.CredgenicsCallbackStreamProducer
	obj._FederalBankCustKycStateChangePublisher = v.FederalBankCustKycStateChangePublisher
	obj._FederalResidentialStatusUpdatePublisher = v.FederalResidentialStatusUpdatePublisher
	obj._FederalMobileNumberUpdatePublisher = v.FederalMobileNumberUpdatePublisher
	obj._PgRazorpayInboundEventPublisher = v.PgRazorpayInboundEventPublisher
	obj._FederalEscalationUpdateEventPublisher = v.FederalEscalationUpdateEventPublisher
	obj._CcOnboardingStateUpdateEventPublisher = v.CcOnboardingStateUpdateEventPublisher
	obj._Auth = v.Auth
	obj._VendorRewardFulfillmentPublisher = v.VendorRewardFulfillmentPublisher
	obj._SavenRewardVnSecrets = v.SavenRewardVnSecrets
	return nil
}

func (obj *Config) SetEnableIndianStocksContentApi(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableIndianStocksContentApi", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableIndianStocksContentApi, 1)
	} else {
		atomic.StoreUint32(&obj._EnableIndianStocksContentApi, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableIndianStocksContentApi")
	}
	return nil
}
func (obj *Config) SetDummyQuestVariable(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.DummyQuestVariable", reflect.TypeOf(val))
	}
	obj._DummyQuestVariableMutex.Lock()
	defer obj._DummyQuestVariableMutex.Unlock()
	obj._DummyQuestVariable = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "DummyQuestVariable")
	}
	return nil
}

func NewFlags() (_obj *Flags, _setters map[string]dynconf.SetFunc) {
	_obj = &Flags{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enablecctransactionprocessingviatemporal"] = _obj.SetEnableCCTransactionProcessingViaTemporal
	_setters["enablenewendpointinboundnotification"] = _obj.SetEnableNewEndpointInboundNotification
	return _obj, _setters
}

func (obj *Flags) Init() {
	newObj, _ := NewFlags()
	*obj = *newObj
}

func (obj *Flags) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Flags) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Flags)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Flags) setDynamicField(v *config.Flags, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enablecctransactionprocessingviatemporal":
		return obj.SetEnableCCTransactionProcessingViaTemporal(v.EnableCCTransactionProcessingViaTemporal, true, nil)
	case "enablenewendpointinboundnotification":
		return obj.SetEnableNewEndpointInboundNotification(v.EnableNewEndpointInboundNotification, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Flags) setDynamicFields(v *config.Flags, dynamic bool, path []string) (err error) {

	err = obj.SetEnableCCTransactionProcessingViaTemporal(v.EnableCCTransactionProcessingViaTemporal, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableNewEndpointInboundNotification(v.EnableNewEndpointInboundNotification, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Flags) setStaticFields(v *config.Flags) error {

	obj._TrimDebugMessageFromStatus = v.TrimDebugMessageFromStatus
	return nil
}

func (obj *Flags) SetEnableCCTransactionProcessingViaTemporal(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableCCTransactionProcessingViaTemporal", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableCCTransactionProcessingViaTemporal, 1)
	} else {
		atomic.StoreUint32(&obj._EnableCCTransactionProcessingViaTemporal, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableCCTransactionProcessingViaTemporal")
	}
	return nil
}
func (obj *Flags) SetEnableNewEndpointInboundNotification(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableNewEndpointInboundNotification", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableNewEndpointInboundNotification, 1)
	} else {
		atomic.StoreUint32(&obj._EnableNewEndpointInboundNotification, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableNewEndpointInboundNotification")
	}
	return nil
}

func NewFeatureFlags() (_obj *FeatureFlags, _setters map[string]dynconf.SetFunc) {
	_obj = &FeatureFlags{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["allowcustomercallbackprocessing"] = _obj.SetAllowCustomerCallbackProcessing
	return _obj, _setters
}

func (obj *FeatureFlags) Init() {
	newObj, _ := NewFeatureFlags()
	*obj = *newObj
}

func (obj *FeatureFlags) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *FeatureFlags) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.FeatureFlags)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *FeatureFlags) setDynamicField(v *config.FeatureFlags, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "allowcustomercallbackprocessing":
		return obj.SetAllowCustomerCallbackProcessing(v.AllowCustomerCallbackProcessing, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *FeatureFlags) setDynamicFields(v *config.FeatureFlags, dynamic bool, path []string) (err error) {

	err = obj.SetAllowCustomerCallbackProcessing(v.AllowCustomerCallbackProcessing, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *FeatureFlags) setStaticFields(v *config.FeatureFlags) error {

	obj._AllowAccountCallbackProcessing = v.AllowAccountCallbackProcessing
	return nil
}

func (obj *FeatureFlags) SetAllowCustomerCallbackProcessing(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags.AllowCustomerCallbackProcessing", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._AllowCustomerCallbackProcessing, 1)
	} else {
		atomic.StoreUint32(&obj._AllowCustomerCallbackProcessing, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "AllowCustomerCallbackProcessing")
	}
	return nil
}

func NewAA() (_obj *AA, _setters map[string]dynconf.SetFunc) {
	_obj = &AA{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["verifyapikeyandjws"] = _obj.SetVerifyApiKeyAndJws
	_setters["tokenissuer"] = _obj.SetTokenIssuer
	_obj._TokenIssuerMutex = &sync.RWMutex{}
	_setters["onemoneycrid"] = _obj.SetOneMoneyCrId
	_obj._OneMoneyCrIdMutex = &sync.RWMutex{}
	_setters["finvucrid"] = _obj.SetFinvuCrId
	_obj._FinvuCrIdMutex = &sync.RWMutex{}
	_setters["epifiaakid"] = _obj.SetEpifiAaKid
	_obj._EpifiAaKidMutex = &sync.RWMutex{}
	_setters["aasecretsversiontouse"] = _obj.SetAaSecretsVersionToUse
	_obj._AaSecretsVersionToUseMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *AA) Init() {
	newObj, _ := NewAA()
	*obj = *newObj
}

func (obj *AA) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AA) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AA)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AA) setDynamicField(v *config.AA, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "verifyapikeyandjws":
		return obj.SetVerifyApiKeyAndJws(v.VerifyApiKeyAndJws, true, nil)
	case "tokenissuer":
		return obj.SetTokenIssuer(v.TokenIssuer, true, nil)
	case "onemoneycrid":
		return obj.SetOneMoneyCrId(v.OneMoneyCrId, true, nil)
	case "finvucrid":
		return obj.SetFinvuCrId(v.FinvuCrId, true, nil)
	case "epifiaakid":
		return obj.SetEpifiAaKid(v.EpifiAaKid, true, nil)
	case "aasecretsversiontouse":
		return obj.SetAaSecretsVersionToUse(v.AaSecretsVersionToUse, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AA) setDynamicFields(v *config.AA, dynamic bool, path []string) (err error) {

	err = obj.SetVerifyApiKeyAndJws(v.VerifyApiKeyAndJws, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTokenIssuer(v.TokenIssuer, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetOneMoneyCrId(v.OneMoneyCrId, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFinvuCrId(v.FinvuCrId, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEpifiAaKid(v.EpifiAaKid, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAaSecretsVersionToUse(v.AaSecretsVersionToUse, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AA) setStaticFields(v *config.AA) error {

	obj._SahamatiPublicKey = v.SahamatiPublicKey
	obj._IPWhiteListing = v.IPWhiteListing
	obj._AaVgVnSecretsV1 = v.AaVgVnSecretsV1
	obj._AaVgVnSecretsV2 = v.AaVgVnSecretsV2
	return nil
}

func (obj *AA) SetVerifyApiKeyAndJws(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.VerifyApiKeyAndJws", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._VerifyApiKeyAndJws, 1)
	} else {
		atomic.StoreUint32(&obj._VerifyApiKeyAndJws, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "VerifyApiKeyAndJws")
	}
	return nil
}
func (obj *AA) SetTokenIssuer(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.TokenIssuer", reflect.TypeOf(val))
	}
	obj._TokenIssuerMutex.Lock()
	defer obj._TokenIssuerMutex.Unlock()
	obj._TokenIssuer = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "TokenIssuer")
	}
	return nil
}
func (obj *AA) SetOneMoneyCrId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.OneMoneyCrId", reflect.TypeOf(val))
	}
	obj._OneMoneyCrIdMutex.Lock()
	defer obj._OneMoneyCrIdMutex.Unlock()
	obj._OneMoneyCrId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "OneMoneyCrId")
	}
	return nil
}
func (obj *AA) SetFinvuCrId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.FinvuCrId", reflect.TypeOf(val))
	}
	obj._FinvuCrIdMutex.Lock()
	defer obj._FinvuCrIdMutex.Unlock()
	obj._FinvuCrId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FinvuCrId")
	}
	return nil
}
func (obj *AA) SetEpifiAaKid(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.EpifiAaKid", reflect.TypeOf(val))
	}
	obj._EpifiAaKidMutex.Lock()
	defer obj._EpifiAaKidMutex.Unlock()
	obj._EpifiAaKid = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "EpifiAaKid")
	}
	return nil
}
func (obj *AA) SetAaSecretsVersionToUse(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.AaSecretsVersionToUse", reflect.TypeOf(val))
	}
	obj._AaSecretsVersionToUseMutex.Lock()
	defer obj._AaSecretsVersionToUseMutex.Unlock()
	obj._AaSecretsVersionToUse = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "AaSecretsVersionToUse")
	}
	return nil
}

func NewReward() (_obj *Reward, _setters map[string]dynconf.SetFunc) {
	_obj = &Reward{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["disabledummyapiresponseflow"] = _obj.SetDisableDummyApiResponseFlow

	_obj._RewardTypeToMaxRewardValueMap = &syncmap.Map[string, float64]{}
	_setters["rewardtypetomaxrewardvaluemap"] = _obj.SetRewardTypeToMaxRewardValueMap
	_setters["dummyrewardprocessingstatus"] = _obj.SetDummyRewardProcessingStatus
	_obj._DummyRewardProcessingStatusMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *Reward) Init() {
	newObj, _ := NewReward()
	*obj = *newObj
}

func (obj *Reward) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Reward) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Reward)
	if !ok {
		return fmt.Errorf("invalid data type %v *Reward", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Reward) setDynamicField(v *config.Reward, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "disabledummyapiresponseflow":
		return obj.SetDisableDummyApiResponseFlow(v.DisableDummyApiResponseFlow, true, nil)
	case "rewardtypetomaxrewardvaluemap":
		return obj.SetRewardTypeToMaxRewardValueMap(v.RewardTypeToMaxRewardValueMap, true, path)
	case "dummyrewardprocessingstatus":
		return obj.SetDummyRewardProcessingStatus(v.DummyRewardProcessingStatus, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Reward) setDynamicFields(v *config.Reward, dynamic bool, path []string) (err error) {

	err = obj.SetDisableDummyApiResponseFlow(v.DisableDummyApiResponseFlow, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetRewardTypeToMaxRewardValueMap(v.RewardTypeToMaxRewardValueMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetDummyRewardProcessingStatus(v.DummyRewardProcessingStatus, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Reward) setStaticFields(v *config.Reward) error {

	return nil
}

func (obj *Reward) SetDisableDummyApiResponseFlow(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Reward.DisableDummyApiResponseFlow", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableDummyApiResponseFlow, 1)
	} else {
		atomic.StoreUint32(&obj._DisableDummyApiResponseFlow, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableDummyApiResponseFlow")
	}
	return nil
}
func (obj *Reward) SetRewardTypeToMaxRewardValueMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]float64)
	if !ok {
		return fmt.Errorf("invalid data type %v *Reward.RewardTypeToMaxRewardValueMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._RewardTypeToMaxRewardValueMap, v, path)
}
func (obj *Reward) SetDummyRewardProcessingStatus(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Reward.DummyRewardProcessingStatus", reflect.TypeOf(val))
	}
	obj._DummyRewardProcessingStatusMutex.Lock()
	defer obj._DummyRewardProcessingStatusMutex.Unlock()
	obj._DummyRewardProcessingStatus = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "DummyRewardProcessingStatus")
	}
	return nil
}

func NewNugget() (_obj *Nugget, _setters map[string]dynconf.SetFunc) {
	_obj = &Nugget{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._NuggetAccountFreezeDummyDetails = &syncmap.Map[string, *NuggetAccountFreezeDummyDetails]{}
	_setters["nuggetaccountfreezedummydetails"] = _obj.SetNuggetAccountFreezeDummyDetails

	_obj._NuggetTransactionDummyDetails = &syncmap.Map[string, *NuggetTransactionDummyDetails]{}
	_setters["nuggettransactiondummydetails"] = _obj.SetNuggetTransactionDummyDetails

	_obj._NuggetUserMockAPIProtoJsonResp = &syncmap.Map[string, string]{}
	_setters["nuggetusermockapiprotojsonresp"] = _obj.SetNuggetUserMockAPIProtoJsonResp
	return _obj, _setters
}

func (obj *Nugget) Init() {
	newObj, _ := NewNugget()
	*obj = *newObj
}

func (obj *Nugget) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Nugget) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Nugget)
	if !ok {
		return fmt.Errorf("invalid data type %v *Nugget", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Nugget) setDynamicField(v *config.Nugget, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "nuggetaccountfreezedummydetails":
		return obj.SetNuggetAccountFreezeDummyDetails(v.NuggetAccountFreezeDummyDetails, true, path)
	case "nuggettransactiondummydetails":
		return obj.SetNuggetTransactionDummyDetails(v.NuggetTransactionDummyDetails, true, path)
	case "nuggetusermockapiprotojsonresp":
		return obj.SetNuggetUserMockAPIProtoJsonResp(v.NuggetUserMockAPIProtoJsonResp, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Nugget) setDynamicFields(v *config.Nugget, dynamic bool, path []string) (err error) {

	err = obj.SetNuggetAccountFreezeDummyDetails(v.NuggetAccountFreezeDummyDetails, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetNuggetTransactionDummyDetails(v.NuggetTransactionDummyDetails, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetNuggetUserMockAPIProtoJsonResp(v.NuggetUserMockAPIProtoJsonResp, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Nugget) setStaticFields(v *config.Nugget) error {

	return nil
}

func (obj *Nugget) SetNuggetAccountFreezeDummyDetails(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.NuggetAccountFreezeDummyDetails)
	if !ok {
		return fmt.Errorf("invalid data type %v *Nugget.NuggetAccountFreezeDummyDetails", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._NuggetAccountFreezeDummyDetails, v, dynamic, path)

}
func (obj *Nugget) SetNuggetTransactionDummyDetails(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.NuggetTransactionDummyDetails)
	if !ok {
		return fmt.Errorf("invalid data type %v *Nugget.NuggetTransactionDummyDetails", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._NuggetTransactionDummyDetails, v, dynamic, path)

}
func (obj *Nugget) SetNuggetUserMockAPIProtoJsonResp(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Nugget.NuggetUserMockAPIProtoJsonResp", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._NuggetUserMockAPIProtoJsonResp, v, path)
}

func NewNuggetAccountFreezeDummyDetails() (_obj *NuggetAccountFreezeDummyDetails, _setters map[string]dynconf.SetFunc) {
	_obj = &NuggetAccountFreezeDummyDetails{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["accountstatus"] = _obj.SetAccountStatus
	_obj._AccountStatusMutex = &sync.RWMutex{}
	_setters["processedfreezereason"] = _obj.SetProcessedFreezeReason
	_obj._ProcessedFreezeReasonMutex = &sync.RWMutex{}
	_setters["freezetype"] = _obj.SetFreezeType
	_obj._FreezeTypeMutex = &sync.RWMutex{}
	_setters["formid"] = _obj.SetFormId
	_obj._FormIdMutex = &sync.RWMutex{}
	_setters["formstatus"] = _obj.SetFormStatus
	_obj._FormStatusMutex = &sync.RWMutex{}
	_setters["formexpirydate"] = _obj.SetFormExpiryDate
	_obj._FormExpiryDateMutex = &sync.RWMutex{}
	_setters["leacomplaintdetails"] = _obj.SetLeaComplaintDetails
	_obj._LeaComplaintDetailsMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *NuggetAccountFreezeDummyDetails) Init() {
	newObj, _ := NewNuggetAccountFreezeDummyDetails()
	*obj = *newObj
}

func (obj *NuggetAccountFreezeDummyDetails) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *NuggetAccountFreezeDummyDetails) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.NuggetAccountFreezeDummyDetails)
	if !ok {
		return fmt.Errorf("invalid data type %v *NuggetAccountFreezeDummyDetails", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *NuggetAccountFreezeDummyDetails) setDynamicField(v *config.NuggetAccountFreezeDummyDetails, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "accountstatus":
		return obj.SetAccountStatus(v.AccountStatus, true, nil)
	case "processedfreezereason":
		return obj.SetProcessedFreezeReason(v.ProcessedFreezeReason, true, nil)
	case "freezetype":
		return obj.SetFreezeType(v.FreezeType, true, nil)
	case "formid":
		return obj.SetFormId(v.FormId, true, nil)
	case "formstatus":
		return obj.SetFormStatus(v.FormStatus, true, nil)
	case "formexpirydate":
		return obj.SetFormExpiryDate(v.FormExpiryDate, true, nil)
	case "leacomplaintdetails":
		return obj.SetLeaComplaintDetails(v.LeaComplaintDetails, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *NuggetAccountFreezeDummyDetails) setDynamicFields(v *config.NuggetAccountFreezeDummyDetails, dynamic bool, path []string) (err error) {

	err = obj.SetAccountStatus(v.AccountStatus, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetProcessedFreezeReason(v.ProcessedFreezeReason, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFreezeType(v.FreezeType, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFormId(v.FormId, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFormStatus(v.FormStatus, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFormExpiryDate(v.FormExpiryDate, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetLeaComplaintDetails(v.LeaComplaintDetails, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *NuggetAccountFreezeDummyDetails) setStaticFields(v *config.NuggetAccountFreezeDummyDetails) error {

	return nil
}

func (obj *NuggetAccountFreezeDummyDetails) SetAccountStatus(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NuggetAccountFreezeDummyDetails.AccountStatus", reflect.TypeOf(val))
	}
	obj._AccountStatusMutex.Lock()
	defer obj._AccountStatusMutex.Unlock()
	obj._AccountStatus = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "AccountStatus")
	}
	return nil
}
func (obj *NuggetAccountFreezeDummyDetails) SetProcessedFreezeReason(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NuggetAccountFreezeDummyDetails.ProcessedFreezeReason", reflect.TypeOf(val))
	}
	obj._ProcessedFreezeReasonMutex.Lock()
	defer obj._ProcessedFreezeReasonMutex.Unlock()
	obj._ProcessedFreezeReason = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ProcessedFreezeReason")
	}
	return nil
}
func (obj *NuggetAccountFreezeDummyDetails) SetFreezeType(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NuggetAccountFreezeDummyDetails.FreezeType", reflect.TypeOf(val))
	}
	obj._FreezeTypeMutex.Lock()
	defer obj._FreezeTypeMutex.Unlock()
	obj._FreezeType = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FreezeType")
	}
	return nil
}
func (obj *NuggetAccountFreezeDummyDetails) SetFormId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NuggetAccountFreezeDummyDetails.FormId", reflect.TypeOf(val))
	}
	obj._FormIdMutex.Lock()
	defer obj._FormIdMutex.Unlock()
	obj._FormId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FormId")
	}
	return nil
}
func (obj *NuggetAccountFreezeDummyDetails) SetFormStatus(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NuggetAccountFreezeDummyDetails.FormStatus", reflect.TypeOf(val))
	}
	obj._FormStatusMutex.Lock()
	defer obj._FormStatusMutex.Unlock()
	obj._FormStatus = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FormStatus")
	}
	return nil
}
func (obj *NuggetAccountFreezeDummyDetails) SetFormExpiryDate(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NuggetAccountFreezeDummyDetails.FormExpiryDate", reflect.TypeOf(val))
	}
	obj._FormExpiryDateMutex.Lock()
	defer obj._FormExpiryDateMutex.Unlock()
	obj._FormExpiryDate = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FormExpiryDate")
	}
	return nil
}
func (obj *NuggetAccountFreezeDummyDetails) SetLeaComplaintDetails(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NuggetAccountFreezeDummyDetails.LeaComplaintDetails", reflect.TypeOf(val))
	}
	obj._LeaComplaintDetailsMutex.Lock()
	defer obj._LeaComplaintDetailsMutex.Unlock()
	obj._LeaComplaintDetails = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "LeaComplaintDetails")
	}
	return nil
}

func NewNuggetTransactionDummyDetails() (_obj *NuggetTransactionDummyDetails, _setters map[string]dynconf.SetFunc) {
	_obj = &NuggetTransactionDummyDetails{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["createdat"] = _obj.SetCreatedAt
	_obj._CreatedAtMutex = &sync.RWMutex{}
	_setters["errorcode"] = _obj.SetErrorCode
	_obj._ErrorCodeMutex = &sync.RWMutex{}
	_setters["executedat"] = _obj.SetExecutedAt
	_obj._ExecutedAtMutex = &sync.RWMutex{}
	_setters["p2p_p2m"] = _obj.SetP2P_P2M
	_obj._P2P_P2MMutex = &sync.RWMutex{}
	_setters["paymentprotocol"] = _obj.SetPaymentProtocol
	_obj._PaymentProtocolMutex = &sync.RWMutex{}
	_setters["provenance"] = _obj.SetProvenance
	_obj._ProvenanceMutex = &sync.RWMutex{}
	_setters["tags"] = _obj.SetTags
	_obj._TagsMutex = &sync.RWMutex{}
	_setters["transactionamount"] = _obj.SetTransactionAmount
	_obj._TransactionAmountMutex = &sync.RWMutex{}
	_setters["transactionstatus"] = _obj.SetTransactionStatus
	_obj._TransactionStatusMutex = &sync.RWMutex{}
	_setters["createdatreadabletime"] = _obj.SetCreatedAtReadableTime
	_obj._CreatedAtReadableTimeMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *NuggetTransactionDummyDetails) Init() {
	newObj, _ := NewNuggetTransactionDummyDetails()
	*obj = *newObj
}

func (obj *NuggetTransactionDummyDetails) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *NuggetTransactionDummyDetails) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.NuggetTransactionDummyDetails)
	if !ok {
		return fmt.Errorf("invalid data type %v *NuggetTransactionDummyDetails", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *NuggetTransactionDummyDetails) setDynamicField(v *config.NuggetTransactionDummyDetails, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "createdat":
		return obj.SetCreatedAt(v.CreatedAt, true, nil)
	case "errorcode":
		return obj.SetErrorCode(v.ErrorCode, true, nil)
	case "executedat":
		return obj.SetExecutedAt(v.ExecutedAt, true, nil)
	case "p2p_p2m":
		return obj.SetP2P_P2M(v.P2P_P2M, true, nil)
	case "paymentprotocol":
		return obj.SetPaymentProtocol(v.PaymentProtocol, true, nil)
	case "provenance":
		return obj.SetProvenance(v.Provenance, true, nil)
	case "tags":
		return obj.SetTags(v.Tags, true, nil)
	case "transactionamount":
		return obj.SetTransactionAmount(v.TransactionAmount, true, nil)
	case "transactionstatus":
		return obj.SetTransactionStatus(v.TransactionStatus, true, nil)
	case "createdatreadabletime":
		return obj.SetCreatedAtReadableTime(v.CreatedAtReadableTime, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *NuggetTransactionDummyDetails) setDynamicFields(v *config.NuggetTransactionDummyDetails, dynamic bool, path []string) (err error) {

	err = obj.SetCreatedAt(v.CreatedAt, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetErrorCode(v.ErrorCode, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetExecutedAt(v.ExecutedAt, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetP2P_P2M(v.P2P_P2M, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPaymentProtocol(v.PaymentProtocol, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetProvenance(v.Provenance, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTags(v.Tags, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTransactionAmount(v.TransactionAmount, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTransactionStatus(v.TransactionStatus, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCreatedAtReadableTime(v.CreatedAtReadableTime, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *NuggetTransactionDummyDetails) setStaticFields(v *config.NuggetTransactionDummyDetails) error {

	return nil
}

func (obj *NuggetTransactionDummyDetails) SetCreatedAt(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NuggetTransactionDummyDetails.CreatedAt", reflect.TypeOf(val))
	}
	obj._CreatedAtMutex.Lock()
	defer obj._CreatedAtMutex.Unlock()
	obj._CreatedAt = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CreatedAt")
	}
	return nil
}
func (obj *NuggetTransactionDummyDetails) SetErrorCode(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NuggetTransactionDummyDetails.ErrorCode", reflect.TypeOf(val))
	}
	obj._ErrorCodeMutex.Lock()
	defer obj._ErrorCodeMutex.Unlock()
	obj._ErrorCode = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ErrorCode")
	}
	return nil
}
func (obj *NuggetTransactionDummyDetails) SetExecutedAt(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NuggetTransactionDummyDetails.ExecutedAt", reflect.TypeOf(val))
	}
	obj._ExecutedAtMutex.Lock()
	defer obj._ExecutedAtMutex.Unlock()
	obj._ExecutedAt = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ExecutedAt")
	}
	return nil
}
func (obj *NuggetTransactionDummyDetails) SetP2P_P2M(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NuggetTransactionDummyDetails.P2P_P2M", reflect.TypeOf(val))
	}
	obj._P2P_P2MMutex.Lock()
	defer obj._P2P_P2MMutex.Unlock()
	obj._P2P_P2M = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "P2P_P2M")
	}
	return nil
}
func (obj *NuggetTransactionDummyDetails) SetPaymentProtocol(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NuggetTransactionDummyDetails.PaymentProtocol", reflect.TypeOf(val))
	}
	obj._PaymentProtocolMutex.Lock()
	defer obj._PaymentProtocolMutex.Unlock()
	obj._PaymentProtocol = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "PaymentProtocol")
	}
	return nil
}
func (obj *NuggetTransactionDummyDetails) SetProvenance(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NuggetTransactionDummyDetails.Provenance", reflect.TypeOf(val))
	}
	obj._ProvenanceMutex.Lock()
	defer obj._ProvenanceMutex.Unlock()
	obj._Provenance = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Provenance")
	}
	return nil
}
func (obj *NuggetTransactionDummyDetails) SetTags(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NuggetTransactionDummyDetails.Tags", reflect.TypeOf(val))
	}
	obj._TagsMutex.Lock()
	defer obj._TagsMutex.Unlock()
	obj._Tags = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Tags")
	}
	return nil
}
func (obj *NuggetTransactionDummyDetails) SetTransactionAmount(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NuggetTransactionDummyDetails.TransactionAmount", reflect.TypeOf(val))
	}
	obj._TransactionAmountMutex.Lock()
	defer obj._TransactionAmountMutex.Unlock()
	obj._TransactionAmount = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "TransactionAmount")
	}
	return nil
}
func (obj *NuggetTransactionDummyDetails) SetTransactionStatus(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NuggetTransactionDummyDetails.TransactionStatus", reflect.TypeOf(val))
	}
	obj._TransactionStatusMutex.Lock()
	defer obj._TransactionStatusMutex.Unlock()
	obj._TransactionStatus = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "TransactionStatus")
	}
	return nil
}
func (obj *NuggetTransactionDummyDetails) SetCreatedAtReadableTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NuggetTransactionDummyDetails.CreatedAtReadableTime", reflect.TypeOf(val))
	}
	obj._CreatedAtReadableTimeMutex.Lock()
	defer obj._CreatedAtReadableTimeMutex.Unlock()
	obj._CreatedAtReadableTime = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CreatedAtReadableTime")
	}
	return nil
}
