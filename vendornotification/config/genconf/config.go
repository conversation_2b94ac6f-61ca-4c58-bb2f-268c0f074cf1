package genconf

import (
	"fmt"
	"runtime"
	"sync"

	"github.com/mohae/deepcopy"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cfg/dynconf"
	config "github.com/epifi/gamma/vendornotification/config"
)

var (
	once       sync.Once
	genConf    *Config
	err        error
	_, _, _, _ = runtime.Caller(0)
)

// <PERSON><PERSON> initializes the object of the generated conf struct by copying the values from the Static conf.
// Then it starts watcher to update remote config.
func Load() (*Config, error) {
	once.Do(func() {
		genConf, err = loadConfig()
	})
	if err != nil {
		return nil, err
	}
	return genConf, err
}

func loadConfig() (*Config, error) {
	staticConf, err := config.Load()
	if err != nil {
		return nil, err
	}
	// clone staticConf to avoid any potential write on the object when the remote config changes are applied.
	// Since static conf is not concurrent safe, these writes may lead to race condition.
	staticConfClone := deepcopy.Copy(staticConf).(*config.Config)
	gconf, setters := NewConfig()
	err = gconf.Set(staticConfClone, false, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to set static staticConf in dynamic config: %w", err)
	}

	sc := dynconf.NewSafeConfigV2(staticConfClone, setters)
	envName, err := cfg.GetEnvironment()
	if err != nil {
		return nil, fmt.Errorf("failed to get environment: %w", err)
	}

	err = dynconf.LoadAndUpdateFromRemoteStoreV2(envName, cfg.VENDOR_NOTIFI_SERVICE, sc)
	if err != nil {
		return nil, err
	}
	return gconf, nil
}
