package test

import (
	"log"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/vendornotification/config"
)

// InitTestServer initiates components needed for tests
// Will be invoked from TestMain but can be called from individual tests for *special* cases only
func InitTestServer() (*config.Config, func()) {
	// Init config
	conf, err := config.Load()
	if err != nil {
		log.Fatal("failed to load config", err)
	}

	// Setup logger
	logger.Init(conf.Application.Environment)

	return conf, func() {
		_ = logger.Log.Sync()
	}
}
