// Code generated by MockGen. DO NOT EDIT.
// Source: vendornotification/cx/chatbot/helper/auth_helper.go

// Package mock_helper is a generated GoMock package.
package mock_helper

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIAuthHelper is a mock of IAuthHelper interface.
type MockIAuthHelper struct {
	ctrl     *gomock.Controller
	recorder *MockIAuthHelperMockRecorder
}

// MockIAuthHelperMockRecorder is the mock recorder for MockIAuthHelper.
type MockIAuthHelperMockRecorder struct {
	mock *MockIAuthHelper
}

// NewMockIAuthHelper creates a new mock instance.
func NewMockIAuthHelper(ctrl *gomock.Controller) *MockIAuthHelper {
	mock := &MockIAuthHelper{ctrl: ctrl}
	mock.recorder = &MockIAuthHelperMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIAuthHelper) EXPECT() *MockIAuthHelperMockRecorder {
	return m.recorder
}

// ValidateApiKey mocks base method.
func (m *MockIAuthHelper) ValidateApiKey(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateApiKey", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateApiKey indicates an expected call of ValidateApiKey.
func (mr *MockIAuthHelperMockRecorder) ValidateApiKey(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateApiKey", reflect.TypeOf((*MockIAuthHelper)(nil).ValidateApiKey), ctx)
}

// ValidateShortTokenAndGetActorId mocks base method.
func (m *MockIAuthHelper) ValidateShortTokenAndGetActorId(ctx context.Context) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateShortTokenAndGetActorId", ctx)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateShortTokenAndGetActorId indicates an expected call of ValidateShortTokenAndGetActorId.
func (mr *MockIAuthHelperMockRecorder) ValidateShortTokenAndGetActorId(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateShortTokenAndGetActorId", reflect.TypeOf((*MockIAuthHelper)(nil).ValidateShortTokenAndGetActorId), ctx)
}
