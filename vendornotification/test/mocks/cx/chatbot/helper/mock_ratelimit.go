// Code generated by MockGen. DO NOT EDIT.
// Source: vendornotification/cx/chatbot/helper/ratelimit.go

// Package mock_helper is a generated GoMock package.
package mock_helper

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIRateLimitHelper is a mock of IRateLimitHelper interface.
type MockIRateLimitHelper struct {
	ctrl     *gomock.Controller
	recorder *MockIRateLimitHelperMockRecorder
}

// MockIRateLimitHelperMockRecorder is the mock recorder for MockIRateLimitHelper.
type MockIRateLimitHelperMockRecorder struct {
	mock *MockIRateLimitHelper
}

// NewMockIRateLimitHelper creates a new mock instance.
func NewMockIRateLimitHelper(ctrl *gomock.Controller) *MockIRateLimitHelper {
	mock := &MockIRateLimitHelper{ctrl: ctrl}
	mock.recorder = &MockIRateLimitHelperMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIRateLimitHelper) EXPECT() *MockIRateLimitHelperMockRecorder {
	return m.recorder
}

// IsRateLimitExceeded mocks base method.
func (m *MockIRateLimitHelper) IsRateLimitExceeded(ctx context.Context, actorId, apiName string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsRateLimitExceeded", ctx, actorId, apiName)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsRateLimitExceeded indicates an expected call of IsRateLimitExceeded.
func (mr *MockIRateLimitHelperMockRecorder) IsRateLimitExceeded(ctx, actorId, apiName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsRateLimitExceeded", reflect.TypeOf((*MockIRateLimitHelper)(nil).IsRateLimitExceeded), ctx, actorId, apiName)
}
