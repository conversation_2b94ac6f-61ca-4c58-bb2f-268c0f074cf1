// Code generated by MockGen. DO NOT EDIT.
// Source: vendornotification/notifications/moengage/userattributesfetcher/userattributesfetcher.go

// Package mock_userattributesfetcher is a generated GoMock package.
package mock_userattributesfetcher

import (
	context "context"
	reflect "reflect"

	userattributesfetcher "github.com/epifi/gamma/vendornotification/notifications/moengage/userattributesfetcher"
	gomock "github.com/golang/mock/gomock"
)

// MockIUserAttributesFetcher is a mock of IUserAttributesFetcher interface.
type MockIUserAttributesFetcher struct {
	ctrl     *gomock.Controller
	recorder *MockIUserAttributesFetcherMockRecorder
}

// MockIUserAttributesFetcherMockRecorder is the mock recorder for MockIUserAttributesFetcher.
type MockIUserAttributesFetcherMockRecorder struct {
	mock *MockIUserAttributesFetcher
}

// NewMockIUserAttributesFetcher creates a new mock instance.
func NewMockIUserAttributesFetcher(ctrl *gomock.Controller) *MockIUserAttributesFetcher {
	mock := &MockIUserAttributesFetcher{ctrl: ctrl}
	mock.recorder = &MockIUserAttributesFetcherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIUserAttributesFetcher) EXPECT() *MockIUserAttributesFetcherMockRecorder {
	return m.recorder
}

// GetAttributes mocks base method.
func (m *MockIUserAttributesFetcher) GetAttributes(ctx context.Context, req *userattributesfetcher.GetAttributesRequest) (*userattributesfetcher.GetAttributesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAttributes", ctx, req)
	ret0, _ := ret[0].(*userattributesfetcher.GetAttributesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAttributes indicates an expected call of GetAttributes.
func (mr *MockIUserAttributesFetcherMockRecorder) GetAttributes(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAttributes", reflect.TypeOf((*MockIUserAttributesFetcher)(nil).GetAttributes), ctx, req)
}
