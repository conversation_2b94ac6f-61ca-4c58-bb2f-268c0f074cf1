// Code generated by MockGen. DO NOT EDIT.
// Source: vendornotification/notifications/moengage/userattributesfetcher/factory.go

// Package mock_userattributesfetcher is a generated GoMock package.
package mock_userattributesfetcher

import (
	reflect "reflect"

	moengage "github.com/epifi/gamma/api/vendornotification/notifications/moengage"
	userattributesfetcher "github.com/epifi/gamma/vendornotification/notifications/moengage/userattributesfetcher"
	gomock "github.com/golang/mock/gomock"
)

// MockIUserAttributesFetcherFactory is a mock of IUserAttributesFetcherFactory interface.
type MockIUserAttributesFetcherFactory struct {
	ctrl     *gomock.Controller
	recorder *MockIUserAttributesFetcherFactoryMockRecorder
}

// MockIUserAttributesFetcherFactoryMockRecorder is the mock recorder for MockIUserAttributesFetcherFactory.
type MockIUserAttributesFetcherFactoryMockRecorder struct {
	mock *MockIUserAttributesFetcherFactory
}

// NewMockIUserAttributesFetcherFactory creates a new mock instance.
func NewMockIUserAttributesFetcherFactory(ctrl *gomock.Controller) *MockIUserAttributesFetcherFactory {
	mock := &MockIUserAttributesFetcherFactory{ctrl: ctrl}
	mock.recorder = &MockIUserAttributesFetcherFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIUserAttributesFetcherFactory) EXPECT() *MockIUserAttributesFetcherFactoryMockRecorder {
	return m.recorder
}

// GetUserAttributesFetcher mocks base method.
func (m *MockIUserAttributesFetcherFactory) GetUserAttributesFetcher(area moengage.Area) (userattributesfetcher.IUserAttributesFetcher, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAttributesFetcher", area)
	ret0, _ := ret[0].(userattributesfetcher.IUserAttributesFetcher)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAttributesFetcher indicates an expected call of GetUserAttributesFetcher.
func (mr *MockIUserAttributesFetcherFactoryMockRecorder) GetUserAttributesFetcher(area interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAttributesFetcher", reflect.TypeOf((*MockIUserAttributesFetcherFactory)(nil).GetUserAttributesFetcher), area)
}
