package jwt

import (
	"context"
	"encoding/base64"
	"fmt"

	"github.com/golang-jwt/jwt/v5"
	"github.com/pkg/errors"

	rsaPkg "github.com/epifi/be-common/pkg/crypto/rsa"
	"github.com/epifi/be-common/pkg/logger"
)

// Supported JWT algorithms
const (
	AlgorithmRS256 = "RS256"
)

// Verifier defines the interface for JWT token verification
type Verifier interface {
	VerifyTokenAndGetClaims(ctx context.Context, tokenString string) (jwt.MapClaims, error)
}

// JwtVerifierConfig represents the configuration for JWT verification
type JwtVerifierConfig struct {
	Algorithm      string   // e.g., "RS256", "HS256", etc.
	PublicKeys     []string // Base64 encoded public keys
	AllowedIssuers []string // Optional: restrict allowed issuers
}

// verifier is the internal implementation of the Verifier interface
type verifier struct {
	algorithm      jwt.SigningMethod
	publicKeyPool  []interface{} // Can hold RSA public keys, HMAC secrets, etc.
	allowedIssuers map[string]bool
}

// NewVerifier creates a new JWT verifier with the specified algorithm and keys
func NewVerifier(algorithm string, publicKeys []string, allowedIssuers ...string) (Verifier, error) {
	config := &JwtVerifierConfig{
		Algorithm:      algorithm,
		PublicKeys:     publicKeys,
		AllowedIssuers: allowedIssuers,
	}

	return newVerifierWithConfig(config)
}

// newVerifierWithConfig creates a new JWT verifier with explicit configuration
// This allows clients to specify different algorithms and keys
func newVerifierWithConfig(config *JwtVerifierConfig) (Verifier, error) {
	signingMethod := jwt.GetSigningMethod(config.Algorithm)
	if signingMethod == nil {
		return nil, errors.Errorf("unsupported signing algorithm: %s", config.Algorithm)
	}

	publicKeys, err := loadPublicKeysForAlgorithm(config.Algorithm, config.PublicKeys)
	if err != nil {
		return nil, errors.Wrap(err, "failed to load keys")
	}

	allowedIssuers := make(map[string]bool)
	for _, issuer := range config.AllowedIssuers {
		allowedIssuers[issuer] = true
	}

	return &verifier{
		algorithm:      signingMethod,
		publicKeyPool:  publicKeys,
		allowedIssuers: allowedIssuers,
	}, nil
}

// VerifyTokenAndGetClaims verifies the JWT token and returns the claims
func (v *verifier) VerifyTokenAndGetClaims(ctx context.Context, tokenString string) (jwt.MapClaims, error) {
	var (
		token *jwt.Token
		err   error
	)

	// Try verification with each key in the pool until one succeeds
	for _, key := range v.publicKeyPool {
		token, err = jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			// Verify the signing method matches what we expect
			if token.Method != v.algorithm {
				return nil, errors.Errorf("unexpected signing method: %v", token.Header["alg"])
			}
			return key, nil
		})
		if err == nil {
			break
		}
	}

	if err != nil {
		return nil, errors.Wrap(err, "failed to verify and parse jwt token string")
	}

	if token == nil {
		return nil, errors.New("failed to parse jwt token string to get token with claims")
	}

	if !token.Valid {
		return nil, errors.New("invalid jwt token")
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errors.New("failed to destructure token to get token claims")
	}

	// Validate issuer if restrictions are configured
	if len(v.allowedIssuers) > 0 {
		issuer, err := claims.GetIssuer()
		if err != nil || issuer == "" {
			return nil, jwt.ErrTokenInvalidIssuer
		}
		if !v.allowedIssuers[issuer] {
			return nil, errors.Errorf("issuer not allowed: %s", issuer)
		}
	}

	// Log issuer for debugging
	if issuer, err := claims.GetIssuer(); err == nil && issuer != "" {
		logger.Info(ctx, fmt.Sprintf("issuer for vendor callback request JWT token: %s", issuer))
	}

	return claims, nil
}

// loadPublicKeysForAlgorithm loads keys based on the signing algorithm
func loadPublicKeysForAlgorithm(algorithm string, keyStrings []string) ([]interface{}, error) {
	var keys []interface{}

	switch algorithm {
	case AlgorithmRS256:
		// RSA algorithms
		for _, keyString := range keyStrings {
			decodedKey, err := base64.StdEncoding.DecodeString(keyString)
			if err != nil {
				return nil, errors.Wrap(err, "error while decoding RSA public key")
			}

			publicKey, err := rsaPkg.ParsePKIXPublicKey(decodedKey)
			if err != nil {
				return nil, errors.Wrap(err, "failed to parse RSA public key")
			}

			keys = append(keys, publicKey)
		}
	default:
		return nil, errors.Errorf("unsupported algorithm for key loading: %s", algorithm)
	}

	return keys, nil
}
