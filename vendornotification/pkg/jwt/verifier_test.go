package jwt

import (
	"context"
	"testing"

	"github.com/epifi/be-common/pkg/logger"
)

const (
	// Test RSA public key (base64 encoded PEM)
	testRSAPublicKeyBase64 = "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUF1MVNVMUxmVkxQSENvek14SDJNbwo0bGdPRWVQek5tMHRSZ2VMZXpWNmZmQXQwZ3VuVlRMdzdvbkxSbnJxMC9Jelc3eVdSN1Frcm1CTDdqVEtFbjV1CitxS2hid0tmQnN0SXMrYk1ZMlprcDE4Z25UeEtMeG9TMnRGY3pHa1BMUGdpenNrdWVtTWdoUm5pV2FvTGN5ZWgKa2QzcXFHRWx2Vy9WREw1QWFXVGcwbkxWa2pSbzl6KzQwUlF6dVZhRThBa0FGbXhaem93M3grVkpZS2RqeWtrSgowaVQ5d0NTMERSVFh1MjY5VjI2NFZmLzNqdnJlZFppS1JrZ3dsTDl4TkF3eFhGZzB4L1hGdzAwNVVXVlJJa2RnCmNLV1RqcEJQMmRQd1ZaNFdXQys5YUdWZCtHeW4xbzBDTGVsZjRyRWpHb1hiQUFFZ0FxZUdVeHJjSWxialhmYmMKbXdJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0t"

	// Test JWT tokens (these would be provided by you with real tokens from Saven)
	validRS256Token  = "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
	wrongIssuerToken = "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
)

func TestVerifier_VerifyToken(t *testing.T) {
	logger.Init("test")
	tests := []struct {
		name           string
		algorithm      string
		publicKeys     []string
		allowedIssuers []string
		token          string
		subClaim       string
		wantErr        bool
		wantClaims     bool
	}{
		{
			name:           "should verify valid RS256 token successfully",
			algorithm:      "RS256",
			publicKeys:     []string{testRSAPublicKeyBase64},
			allowedIssuers: []string{"SAVEN"},
			token:          validRS256Token,
			subClaim:       "1234567890",
			wantErr:        false,
			wantClaims:     true,
		},
		{
			name:           "should reject token with wrong issuer",
			algorithm:      "RS256",
			publicKeys:     []string{testRSAPublicKeyBase64},
			allowedIssuers: []string{"SAVER"},
			token:          wrongIssuerToken,
			wantErr:        true,
			wantClaims:     false,
		},
		{
			name:           "should reject malformed token",
			algorithm:      "RS256",
			publicKeys:     []string{testRSAPublicKeyBase64},
			allowedIssuers: []string{"SAVEN"},
			token:          "invalid.token.format",
			wantErr:        true,
			wantClaims:     false,
		},
		{
			name:           "should accept token when no issuer restriction",
			algorithm:      "RS256",
			publicKeys:     []string{testRSAPublicKeyBase64},
			allowedIssuers: nil, // no issuer restriction
			token:          wrongIssuerToken,
			subClaim:       "1234567890",
			wantErr:        false,
			wantClaims:     true,
		},
		{
			name:           "should reject empty token",
			algorithm:      "RS256",
			publicKeys:     []string{testRSAPublicKeyBase64},
			allowedIssuers: []string{"SAVEN"},
			token:          "",
			wantErr:        true,
			wantClaims:     false,
		},
		{
			name:           "should reject token with invalid signature",
			algorithm:      "RS256",
			publicKeys:     []string{testRSAPublicKeyBase64},
			allowedIssuers: []string{"SAVEN"},
			token:          "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJ0ZXN0LWlzc3VlciIsInN1YiI6InRlc3Qtc3ViamVjdCIsImV4cCI6OTk5OTk5OTk5OX0.invalid_signature",
			wantErr:        true,
			wantClaims:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			verifier, err := NewVerifier(tt.algorithm, tt.publicKeys, tt.allowedIssuers...)
			if err != nil {
				t.Fatalf("Failed to create verifier: %v", err)
			}

			ctx := context.Background()
			claims, err := verifier.VerifyTokenAndGetClaims(ctx, tt.token)

			if tt.wantErr {
				if err == nil {
					t.Errorf("VerifyToken() error = nil, wantErr %v", tt.wantErr)
					return
				}
			} else {
				if err != nil {
					t.Errorf("VerifyToken() unexpected error = %v", err)
					return
				}
			}

			if tt.wantClaims && claims == nil {
				t.Error("VerifyToken() expected claims but got nil")
			}

			if !tt.wantClaims && claims != nil {
				t.Error("VerifyToken() expected no claims but got claims")
			}

			// Verify claims content for successful cases
			if tt.wantClaims && claims != nil {
				if subject, ok := claims["sub"].(string); !ok || subject != tt.subClaim {
					t.Errorf("VerifyToken() expected subject 'test-subject', got %v", claims["sub"])
				}
			}
		})
	}
}
