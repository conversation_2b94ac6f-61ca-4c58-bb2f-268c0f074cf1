package airtel

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	commspb "github.com/epifi/gamma/api/comms"
	vgSmsPb "github.com/epifi/gamma/api/vendorgateway/sms"
	airtelPb "github.com/epifi/gamma/api/vendornotification/sms/airtel"
	"github.com/epifi/gamma/api/vendors/airtel"
	federalRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/metrics"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"

	"google.golang.org/protobuf/types/known/emptypb"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"
)

type Service struct {
	conf                       *config.Config
	airtelSmsCallbackPublisher queue.Publisher
	smsClient                  vgSmsPb.SMSClient
}

type AirtelSmsCallbackPublisher queue.Publisher

const AirtelSmsDLRRequest = "AirtelSmsDLRRequest"

func NewService(conf *config.Config, airtelSmsCallbackPublisher AirtelSmsCallbackPublisher, smsClient vgSmsPb.SMSClient) *Service {
	return &Service{
		conf:                       conf,
		airtelSmsCallbackPublisher: airtelSmsCallbackPublisher,
		smsClient:                  smsClient,
	}
}

var _ airtelPb.AirtelSMSCallbackServer = &Service{}

func (s *Service) AirtelSmsDLR(ctx context.Context, req *airtel.AirtelSmsDLRRequest) (*emptyPb.Empty, error) {
	if err := security.CheckWhiteList(ctx, s.conf.AirtelWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor,
		s.conf.VpcCidrIPPrefix); err != nil {
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, req, AirtelSmsDLRRequest, req.GetMessageRequestId(), federalRedactor.Config)

	// validate and redirect the callback to the correct environment if required.
	isRedirecting, err := s.validateAndRedirectCallbacks(ctx, req)
	if err != nil {
		return nil, err
	}

	// if the callback is redirected then return without processing it.
	if isRedirecting {
		return &emptypb.Empty{}, nil
	}

	var (
		statusToUse = req.GetMessageStatus()
		smsOrigin   = commspb.SmsOrigin_SMS_ORIGIN_EPIFI
	)

	// If message status is not present in the request , then use the status from the request.
	if statusToUse == "" {
		statusToUse = req.GetStatus()
	}

	// Only for MoEngage callbacks, we receive status in req.GetStatus()
	if req.GetStatus() != "" {
		smsOrigin = commspb.SmsOrigin_SMS_ORIGIN_MOENGAGE
	}

	event := &commspb.ProcessAirtelSmsCallbackRequest{
		MessageId:          req.GetMessageId(),
		MessageRequestId:   req.GetMessageRequestId(),
		DestinationAddress: req.GetDestinationAddress(),
		MessageStatus:      statusToUse,
		ErrorCode:          req.GetErrorCode(),
		ErrorDescription:   req.GetErrorDescription(),
		RequestDate:        req.GetRequestDate(),
		DeliveredTime:      req.GetDeliveredTime(),
		Units:              req.GetUnits(),
		EventTimestamp:     timestampPb.Now(),
		SmsOrigin:          smsOrigin,
	}
	msgId, err := s.airtelSmsCallbackPublisher.Publish(ctx, event)
	if err != nil {
		logger.Error(ctx, "error while publishing airtel sms callback event to queue", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "internal server error")
	}

	metrics.IncrementSMSCallbackCount(commonvgpb.Vendor_AIRTEL.String())

	logger.Debug(ctx, "airtel sms dlr request processed successfully", zap.String("message_id", msgId))

	return &emptypb.Empty{}, nil
}

func (s *Service) validateAndRedirectCallbacks(ctx context.Context, req *airtel.AirtelSmsDLRRequest) (bool, error) {
	switch req.GetMetaData().GetEnv() {
	case s.conf.Application.Environment:
		// if the callback is for the same environment then process it.
		return false, nil
	case "":
		logger.Error(ctx, "dlr_env is empty in airtel sms dlr request", zap.String("message_id", req.GetMessageRequestId()))
		return false, status.Errorf(codes.InvalidArgument, "dlr_env is empty in airtel sms dlr request")
	default:
		logger.Debug(ctx, "ignoring airtel sms dlr request as it is not for the same environment",
			zap.String("message_id", req.GetMessageId()), zap.String("dlr_env", req.GetMetaData().GetEnv()))
		res, err := s.smsClient.RedirectSMSDlr(ctx, &vgSmsPb.RedirectSMSDlrRequest{
			Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_AIRTEL},
			DlrRequest: &vgSmsPb.RedirectSMSDlrRequest_AirtelSmsDlrRequest{
				AirtelSmsDlrRequest: req,
			},
		})
		if grpcErr := epifigrpc.RPCError(res, err); grpcErr != nil {
			logger.Error(ctx, "error while redirecting airtel sms dlr request to another environment", zap.String("message_id", req.GetMessageRequestId()), zap.Error(grpcErr))
			// We try to redirect callback to another environment with the best effort, so we should not return error here.
		}
		return true, nil
	}
}
