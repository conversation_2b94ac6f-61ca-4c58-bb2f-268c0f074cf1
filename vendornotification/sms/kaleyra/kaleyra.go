package kaleyra

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"strconv"

	"github.com/epifi/gamma/api/vendors/kaleyra"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	commsPb "github.com/epifi/gamma/api/comms"
	kaleyraPb "github.com/epifi/gamma/api/vendornotification/sms/kaleyra"
	federalRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/metrics"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"
)

type Service struct {
	conf                  *config.Config
	kaleyraSmsCallbackPub queue.Publisher
}

type KaleyraSmsCallbackPublisher queue.Publisher

func NewService(kaleyraSmsCallbackPub KaleyraSmsCallbackPublisher, conf *config.Config) *Service {
	return &Service{
		kaleyraSmsCallbackPub: kaleyraSmsCallbackPub,
		conf:                  conf,
	}
}

var _ kaleyraPb.KaleyraCallbackServer = &Service{}

const (
	KaleyraSmsDLRRequest   = "KaleyraSmsDLRRequest"
	KaleyraIOSmsDLRRequest = "KaleyraIOSmsDLRRequest"
)

// KaleyraSmsDLR - DLR is delivery report, a standard term used by all sms vendors
func (s *Service) KaleyraSmsDLR(ctx context.Context, req *kaleyraPb.KaleyraSmsDLRRequest) (*emptypb.Empty, error) {
	// NOTE: Whitelist IPs disabled now as the vendor did not give us specific IPs.
	if err := security.CheckWhiteList(ctx, s.conf.KaleyraSmsWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor,
		s.conf.VpcCidrIPPrefix); err != nil {
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, req, KaleyraSmsDLRRequest, req.GetMessageId(), federalRedactor.Config)

	// due to an update in grpc gateway, repetition of keys is not allowed if the
	// json field is not repeated. kaleyra callbacks are having the `status` field
	// repeated (with the same value) in the API calls that they are making. hence,
	// `status` field has been change to a string array (from string) to handle this.
	// can be reverted once they start sending a single status key in query params.
	if len(req.GetStatus()) == 0 {
		return nil, status.Errorf(codes.InvalidArgument, "status field can't be empty")
	}
	dlrStatus := req.GetStatus()[0]

	var dlrStatusTrace string
	if len(req.GetStatusTrace()) > 0 {
		dlrStatusTrace = req.GetStatusTrace()[0]
	}

	event := &commsPb.ProcessKaleyraSmsCallBackRequest{
		ReferenceId:    req.GetMessageId(),
		MessageId:      req.GetMessageId(),
		Status:         dlrStatus,
		StatusTrace:    dlrStatusTrace,
		SubmitTime:     req.GetSubmitTime(),
		SentTime:       req.GetSentTime(),
		DeliveredTime:  req.GetDeliveredTime(),
		MobileNumber:   req.GetMobileNumber(),
		Custom:         req.GetCustom(),
		EventTimestamp: timestamppb.Now(),
		SmsCount:       req.GetSmsCount(),
		SmsOrigin:      commsPb.SmsOrigin_SMS_ORIGIN_EPIFI,
	}
	sqsMsgId, err := s.kaleyraSmsCallbackPub.Publish(ctx, event)
	if err != nil {
		logger.Error(ctx, "error publishing kaleyra sms callback event to queue", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "internal server error")
	}
	metrics.IncrementSMSCallbackCount(commonvgpb.Vendor_KALEYRA.String())
	logger.Debug(ctx, "message published in kaleyra sms callback queue successfully", zap.String(logger.QUEUE_MESSAGE_ID, sqsMsgId), zap.String(logger.STATUS, dlrStatus))
	return &emptypb.Empty{}, nil
}

// KaleyraIOSmsDLR DLR callback for Kaleyra IO SMS
func (s *Service) KaleyraIOSmsDLR(ctx context.Context, request *kaleyra.KaleyraIOSmsDLRRequest) (*emptypb.Empty, error) {
	// TODO: Need to add the IP Whitelisting for Kaleyra

	redactor.LogCallbackRequestData(ctx, request, KaleyraIOSmsDLRRequest, request.GetMessageId(), federalRedactor.Config)

	event := &commsPb.ProcessKaleyraSmsCallBackRequest{
		ReferenceId:    request.GetMessageId(),
		MessageId:      request.GetId(),
		Status:         request.GetStatusTrace(),
		SubmitTime:     request.GetStatusTime(),
		SentTime:       request.GetSentTime(),
		DeliveredTime:  request.GetStatusTime(),
		MobileNumber:   request.GetRecipient(),
		Custom:         request.GetSource(),
		EventTimestamp: timestamppb.Now(),
		SmsCount:       strconv.Itoa(int(request.GetUnits())),
		SmsOrigin:      commsPb.SmsOrigin_SMS_ORIGIN_EPIFI,
	}
	sqsMsgId, err := s.kaleyraSmsCallbackPub.Publish(ctx, event)
	if err != nil {
		logger.Error(ctx, "error publishing kaleyra sms callback event to queue", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "internal server error")
	}
	metrics.IncrementSMSCallbackCount(commonvgpb.Vendor_KALEYRA.String())
	logger.Debug(ctx, "message published in kaleyra sms callback queue successfully", zap.String(logger.QUEUE_MESSAGE_ID, sqsMsgId), zap.String(logger.STATUS, request.GetStatus()))
	return &emptypb.Empty{}, nil
}
