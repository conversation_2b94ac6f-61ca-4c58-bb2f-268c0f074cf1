package acl

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"

	federalRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/metrics"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"

	commsPb "github.com/epifi/gamma/api/comms"
	aclPb "github.com/epifi/gamma/api/vendornotification/sms/acl"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
)

type Service struct {
	conf              *config.Config
	aclSmsCallbackPub queue.Publisher
}

type AclSmsCallbackPublisher queue.Publisher

func NewService(aclSmsCallbackPub AclSmsCallbackPublisher, conf *config.Config) *Service {
	return &Service{
		aclSmsCallbackPub: aclSmsCallbackPub,
		conf:              conf,
	}
}

var _ aclPb.SmsCallbackServer = &Service{}

const (
	AclSmsDLRRequest = "AclSmsDLRRequest"
)

// DLR is delivery report, a standard term used by all sms vendors
func (s *Service) AclSmsDLR(ctx context.Context, req *aclPb.AclSmsDLRRequest) (*emptypb.Empty, error) {
	if err := security.CheckWhiteList(
		ctx, s.conf.AclSmsWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor,
		s.conf.VpcCidrIPPrefix,
	); err != nil {
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, req, AclSmsDLRRequest, req.GetResponseId(), federalRedactor.Config)
	switch {
	case req == nil:
		logger.Error(ctx, "AclSmsDLRRequest cannot be empty", zap.String("AclSmsDLRRequest", req.String()))
		return nil, status.Errorf(codes.InvalidArgument, "delivery report cannot be empty")
	case req.GetResponseId() == "":
		return nil, status.Errorf(codes.InvalidArgument, "response id should not be empty")
	}

	event := &commsPb.ProcessAclSmsCallBackRequest{
		ResponseId:         req.GetResponseId(),
		VendorReceivedTime: req.GetVendorReceivedTime(),
		SmsDeliveryStatus:  req.GetSmsDeliveryStatus(),
		SmsDeliveryTime:    req.GetSmsDeliveryTime(),
		VendorUpdatedTime:  req.GetVendorUpdatedTime(),
		FailureErrorCode:   req.GetFailureErrorCode(),
		VendorName:         req.GetVendorName(),
		ChannelName:        req.GetChannelName(),
		FailureDescription: req.GetFailureDescription(),
		PhoneNumber:        req.GetPhoneNumber(),
		EventTimestamp:     timestamppb.Now(),
		SmsCount:           req.GetSmsCount(),
		SmsOrigin:          commsPb.SmsOrigin_SMS_ORIGIN_EPIFI,
	}
	sqsMsgId, err := s.aclSmsCallbackPub.Publish(ctx, event)
	if err != nil {
		logger.Error(ctx, "error publishing sms callback event to queue", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "internal server error")
	}
	metrics.IncrementSMSCallbackCount(commonvgpb.Vendor_ACL.String())
	logger.Info(ctx, "message published in acl callback queue successfully", zap.String(logger.QUEUE_MESSAGE_ID, sqsMsgId), zap.String(logger.ERROR_CODE, req.GetFailureErrorCode()))
	return &emptypb.Empty{}, nil
}
