package netcore

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"

	commsPb "github.com/epifi/gamma/api/comms"
	netcorePb "github.com/epifi/gamma/api/vendornotification/sms/netcore"
	"github.com/epifi/gamma/api/vendors/netcore"
	federalRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/metrics"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"
)

type Service struct {
	conf                        *config.Config
	netCoreSmsCallbackPublisher queue.Publisher
}

type NetCoreSmsCallbackPublisher queue.Publisher

const NetCoreSmsDLRRequest = "NetCoreSmsDLRRequest"

func NewService(netCoreSmsCallbackPublisher NetCoreSmsCallbackPublisher, conf *config.Config) *Service {
	return &Service{
		netCoreSmsCallbackPublisher: netCoreSmsCallbackPublisher,
		conf:                        conf,
	}
}

var _ netcorePb.NetCoreCallbackServer = &Service{}

func (s Service) NetCoreSmsDLR(ctx context.Context, req *netcore.NetCoreSmsDLRRequest) (*emptypb.Empty, error) {
	if err := security.CheckWhiteList(ctx, s.conf.NetCoreWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor,
		s.conf.VpcCidrIPPrefix); err != nil {
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, req, NetCoreSmsDLRRequest, req.GetRequestId(), federalRedactor.Config)

	event := &commsPb.ProcessNetCoreSmsCallbackRequest{
		Mobile:         req.GetMobile(),
		PublishDate:    req.GetPublishDate(),
		DeliveryDate:   req.GetDeliveryDate(),
		DeliveryStatus: req.GetDeliveryStatus(),
		FeedId:         req.GetFeedId(),
		RequestId:      req.GetRequestId(),
		TransactionId:  req.GetTransactionId(),
		DltTemplateId:  req.GetDltTemplateId(),
		JobName:        req.GetJobName(),
		NoOfSms:        req.GetNoOfSms(),
		Operator:       req.GetOperator(),
		SenderId:       req.GetSenderId(),
		Circle:         req.GetCircle(),
	}
	msgId, err := s.netCoreSmsCallbackPublisher.Publish(ctx, event)
	if err != nil {
		logger.Error(ctx, "error while publishing netcore sms callback event to queue", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "internal server error")
	}

	metrics.IncrementSMSCallbackCount(commonvgpb.Vendor_NETCORE.String())

	logger.Debug(ctx, "netcore sms callback event published to queue", zap.String("msgId", msgId))

	return &emptypb.Empty{}, nil
}
