package unsubscribe

import (
	"context"
	"fmt"
	"strings"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/comms"
	commspb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/user"
	vendormappingPb "github.com/epifi/gamma/api/vendormapping"
	unsubscribeVnPb "github.com/epifi/gamma/api/vendornotification/comms/unsubscribe"
	"github.com/epifi/gamma/api/vendors/unsubscribe"
	"github.com/epifi/gamma/vendornotification/config/genconf"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/emptypb"
)

type RequestSource string

const (
	RequestSource_Moengage RequestSource = "MOENGAGE"
	DefaultTestUserID                    = "{{UserAttribute[\\'uid\\']}}"
)

type Service struct {
	conf                *genconf.Config
	userClient          user.UsersClient
	vendorMappingClient vendormappingPb.VendorMappingServiceClient
}

func NewService(conf *genconf.Config, userClient user.UsersClient, vendorMappingClient vendormappingPb.VendorMappingServiceClient) *Service {
	return &Service{
		conf:                conf,
		userClient:          userClient,
		vendorMappingClient: vendorMappingClient,
	}
}

var _ unsubscribeVnPb.UnsubscribeServer = &Service{}

// GetCommsAreaSubscriptionStatusForUser returns area wise user preference for promotional email subscription.
func (s *Service) GetCommsAreaSubscriptionStatusForUser(ctx context.Context, request *unsubscribe.GetCommsAreaSubscriptionStatusForUserRequest) (*unsubscribe.GetCommsAreaSubscriptionStatusForUserResponse, error) {
	if request.GetUserId() == "" {
		logger.Error(ctx, "user id missing in request", zap.String("userId", request.GetUserId()))
		return nil, fmt.Errorf("user id missing in request")
	}
	if !strings.EqualFold(strings.ToUpper(request.GetSource()), string(RequestSource_Moengage)) {
		logger.Error(ctx, "source should be from moengage", zap.String("userId", request.GetUserId()), zap.String("source", request.GetSource()))
		return nil, fmt.Errorf("source should be from moengage, requested source: %v", request.GetSource())
	}

	actorId, err := s.getActorIdFromVendorId(ctx, request.GetUserId())
	if err != nil {
		logger.Error(ctx, "error while fetching ActorId mapped to vendor user ID", zap.String(logger.USER_ID, request.GetUserId()), zap.Error(err))
		return nil, fmt.Errorf("error while fetching user id")
	}

	userCommsPrefResp, err := s.userClient.GetUserPreferences(ctx, &user.GetUserPreferencesRequest{
		ActorId: actorId,
		PreferenceTypes: []user.PreferenceType{
			user.PreferenceType_PREFERENCE_TYPE_APP_COMMS,
		},
	})
	if rpcErr := epifigrpc.RPCError(userCommsPrefResp, err); rpcErr != nil && !userCommsPrefResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error while fetching user preferences", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return nil, fmt.Errorf("error while fetching user preferences")
	}

	return &unsubscribe.GetCommsAreaSubscriptionStatusForUserResponse{
		UserId: request.GetUserId(),
		Areas:  getSubscriptionStatusByAreaForUser(userCommsPrefResp.GetUserPreferences()),
	}, nil
}

// getSubscriptionStatusByAreaForUser will take user preferences value and
// returns Area wise preference list with details of whether user has subscribed/unsubscribe from the area.
func getSubscriptionStatusByAreaForUser(userPreferences []*user.UserPreference) []*unsubscribe.Areas {
	// map to store if user has unsubscribed from given area or not.
	// only if user unsubscribed from given area we add this to the Map.
	areasUnSubMap := make(map[string]bool, 0)
	for _, userPref := range userPreferences {
		if userPref.GetPreferenceType() == user.PreferenceType_PREFERENCE_TYPE_APP_COMMS {
			for _, commsPref := range userPref.GetPreferenceValue().GetAppCommsPreference().GetCommsPreferenceInfos() {
				if commsPref.GetMedium() == commspb.Medium_EMAIL {
					switch {
					case commsPref.GetSignal() == user.CommsSignal_OFF && commsPref.GetArea() == commspb.Area_ALL:
						return getAllSupportedAreasList(false)
					case commsPref.GetSignal() == user.CommsSignal_ON && commsPref.GetArea() == commspb.Area_ALL:
						return getAllSupportedAreasList(true)
					case commsPref.GetSignal() == user.CommsSignal_OFF:
						areasUnSubMap[commsPref.GetArea().String()] = true
					}
				}
			}
		}
	}
	areas := make([]*unsubscribe.Areas, 0)
	for _, areaName := range commspb.Area_name {
		if areaName == commspb.Area_AREA_UNSPECIFIED.String() || areaName == commspb.Area_ALL.String() {
			continue
		}
		// if the map contains the given area then user has unsubscribed to this area.
		_, ok := areasUnSubMap[areaName]
		areas = append(areas, &unsubscribe.Areas{
			Area:       areaName,
			Subscribed: !ok,
		})
	}
	return areas
}

// getAllSupportedAreasList returns all supported area wise preference list.
func getAllSupportedAreasList(isSubscribed bool) []*unsubscribe.Areas {
	areas := make([]*unsubscribe.Areas, 0)
	for _, areaName := range commspb.Area_name {
		// we are skipping UNSPECIFIED and ALL areas to not show on client.
		if areaName == commspb.Area_AREA_UNSPECIFIED.String() || areaName == commspb.Area_ALL.String() {
			continue
		}
		areas = append(areas, &unsubscribe.Areas{
			Area:       areaName,
			Subscribed: isSubscribed,
		})
	}
	return areas
}

// EmailUnsubscribe updates the user email preferences for requested areas.
func (s *Service) EmailUnsubscribe(ctx context.Context, request *unsubscribe.EmailUnsubscribeRequest) (*emptypb.Empty, error) {
	if request.GetUserId() == "" {
		logger.Error(ctx, "user id missing in request", zap.String("userId", request.GetUserId()))
		return nil, fmt.Errorf("user id missing in request")
	}

	if !strings.EqualFold(strings.ToUpper(request.GetSource()), string(RequestSource_Moengage)) {
		logger.Error(ctx, "source should be from moengage", zap.String("userId", request.GetUserId()), zap.String("source", request.GetSource()))
		return nil, fmt.Errorf("source should be from moengage, requested source: %v", request.GetSource())
	}

	// if user id is default test user id, then return dummy response.
	// this is to avoid unnecessary false alerts due to testing done by pmm team.
	if request.GetUserId() == DefaultTestUserID {
		logger.Info(ctx, "user id is default test user id, returning dummy response", zap.String("userId", request.GetUserId()))
		return &emptypb.Empty{}, nil
	}

	actorId, err := s.getActorIdFromVendorId(ctx, request.GetUserId())
	if err != nil {
		logger.Error(ctx, "error while fetching ActorId mapped to vendor user ID", zap.String(logger.USER_ID, request.GetUserId()), zap.Error(err))
		return nil, fmt.Errorf("error while fetching user id")
	}

	areas := getAreasEnum(request.GetAreas())
	commsPreference := buildCommsPreference(areas)
	resp, err := s.userClient.SetUserPreferences(ctx, &user.SetUserPreferencesRequest{
		ActorId: actorId,
		Preferences: []*user.PreferenceTypeValuePair{
			{
				PreferenceType: user.PreferenceType_PREFERENCE_TYPE_APP_COMMS,
				PreferenceValue: &user.PreferenceValue{
					PrefVal: &user.PreferenceValue_AppCommsPreference{
						AppCommsPreference: commsPreference,
					},
				},
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "error while set user preferences", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		if resp.GetStatus().IsAlreadyExists() {
			return &emptypb.Empty{}, nil
		}
		return nil, fmt.Errorf("error while set user preferences")
	}
	return &emptypb.Empty{}, nil
}

func buildCommsPreference(areas []commspb.Area) *user.CommsPreference {
	commsPreferenceInfos := make([]*user.CommsPreferenceInfo, 0)
	if len(areas) == 0 {
		return &user.CommsPreference{
			CommsPreferenceInfos: []*user.CommsPreferenceInfo{
				{
					Area:     commspb.Area_ALL,
					Medium:   commspb.Medium_EMAIL,
					Feature:  commspb.Feature_PROMOTIONS,
					Signal:   user.CommsSignal_ON,
					Category: comms.Category_CATEGORY_PROMOTIONAL,
				},
			},
		}
	}
	for _, area := range areas {
		// check if area ALL exist in areas list
		// if exists, then simply return Area_ALL preference.
		if area == commspb.Area_ALL {
			return &user.CommsPreference{
				CommsPreferenceInfos: []*user.CommsPreferenceInfo{
					{
						Area:     commspb.Area_ALL,
						Medium:   commspb.Medium_EMAIL,
						Feature:  commspb.Feature_PROMOTIONS,
						Signal:   user.CommsSignal_OFF,
						Category: comms.Category_CATEGORY_PROMOTIONAL,
					},
				},
			}
		}
		commsPreferenceInfos = append(commsPreferenceInfos, &user.CommsPreferenceInfo{
			Area:     area,
			Medium:   commspb.Medium_EMAIL,
			Feature:  commspb.Feature_PROMOTIONS,
			Signal:   user.CommsSignal_OFF,
			Category: comms.Category_CATEGORY_PROMOTIONAL,
		})
	}

	return &user.CommsPreference{
		CommsPreferenceInfos: commsPreferenceInfos,
	}
}

// getAreas returns the enum areas list
func getAreasEnum(areas []string) []commspb.Area {
	areasPb := make([]commspb.Area, 0)
	for _, area := range areas {
		val, ok := commspb.Area_value[strings.ToUpper(area)]
		if ok {
			areasPb = append(areasPb, commspb.Area(val))
		}
	}
	return areasPb
}

// getActorIdFromVendorId get actor id from given vendor's id
func (s *Service) getActorIdFromVendorId(ctx context.Context, userId string) (string, error) {
	actorIdResp, err := s.vendorMappingClient.GetActorIdByVendorId(ctx, &vendormappingPb.GetActorIdByVendorIdRequest{
		VendorId: &vendormappingPb.GetActorIdByVendorIdRequest_MoengageId{
			MoengageId: userId,
		},
	})
	if rpcErr := epifigrpc.RPCError(actorIdResp, err); rpcErr != nil {
		return "", fmt.Errorf("error while fetching ActorId mapped to vendor user ID, err: %w", rpcErr)
	}
	return actorIdResp.GetActorId(), nil
}
