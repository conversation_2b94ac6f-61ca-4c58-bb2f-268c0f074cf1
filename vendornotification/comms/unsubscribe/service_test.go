package unsubscribe

import (
	"context"
	"reflect"
	"sort"
	"testing"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/user"
	mockUser "github.com/epifi/gamma/api/user/mocks"
	vendormappingPb "github.com/epifi/gamma/api/vendormapping"
	mockVendorMapping "github.com/epifi/gamma/api/vendormapping/mocks"
	"github.com/epifi/gamma/api/vendors/unsubscribe"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/golang/mock/gomock"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"
)

// nolint: govet
func TestService_AreasSupportedForUnsubscribe(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	type args struct {
		ctx context.Context
		req *unsubscribe.GetCommsAreaSubscriptionStatusForUserRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient)
		want           *unsubscribe.GetCommsAreaSubscriptionStatusForUserResponse
		wantErr        bool
	}{
		{
			name: "empty user id",
			args: args{
				ctx: context.Background(),
				req: &unsubscribe.GetCommsAreaSubscriptionStatusForUserRequest{
					UserId: "",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "unsupported source",
			args: args{
				ctx: context.Background(),
				req: &unsubscribe.GetCommsAreaSubscriptionStatusForUserRequest{
					UserId: "user-id",
					Source: "source-invalid",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "error while fetching actor id from user id",
			args: args{
				ctx: context.Background(),
				req: &unsubscribe.GetCommsAreaSubscriptionStatusForUserRequest{
					UserId: "user-id",
					Source: "Moengage",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockVendorMappingClient.EXPECT().GetActorIdByVendorId(gomock.Any(), &vendormappingPb.GetActorIdByVendorIdRequest{
					VendorId: &vendormappingPb.GetActorIdByVendorIdRequest_MoengageId{
						MoengageId: "user-id",
					},
				}).Return(&vendormappingPb.GetActorIdByVendorIdResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "error in GetUserPreferences rpc",
			args: args{
				ctx: context.Background(),
				req: &unsubscribe.GetCommsAreaSubscriptionStatusForUserRequest{
					UserId: "user-id",
					Source: "Moengage",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockVendorMappingClient.EXPECT().GetActorIdByVendorId(gomock.Any(), &vendormappingPb.GetActorIdByVendorIdRequest{
					VendorId: &vendormappingPb.GetActorIdByVendorIdRequest_MoengageId{
						MoengageId: "user-id",
					},
				}).Return(&vendormappingPb.GetActorIdByVendorIdResponse{
					Status:  rpc.StatusOk(),
					ActorId: "actor-id",
				}, nil)
				mockUserClient.EXPECT().GetUserPreferences(gomock.Any(), &user.GetUserPreferencesRequest{
					ActorId: "actor-id",
					PreferenceTypes: []user.PreferenceType{
						user.PreferenceType_PREFERENCE_TYPE_APP_COMMS,
					},
				}).Return(&user.GetUserPreferencesResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "success, get area ALL with signal OFF",
			args: args{
				ctx: context.Background(),
				req: &unsubscribe.GetCommsAreaSubscriptionStatusForUserRequest{
					UserId: "user-id",
					Source: "Moengage",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockVendorMappingClient.EXPECT().GetActorIdByVendorId(gomock.Any(), &vendormappingPb.GetActorIdByVendorIdRequest{
					VendorId: &vendormappingPb.GetActorIdByVendorIdRequest_MoengageId{
						MoengageId: "user-id",
					},
				}).Return(&vendormappingPb.GetActorIdByVendorIdResponse{
					Status:  rpc.StatusOk(),
					ActorId: "actor-id",
				}, nil)
				mockUserClient.EXPECT().GetUserPreferences(gomock.Any(), &user.GetUserPreferencesRequest{
					ActorId: "actor-id",
					PreferenceTypes: []user.PreferenceType{
						user.PreferenceType_PREFERENCE_TYPE_APP_COMMS,
					},
				}).Return(&user.GetUserPreferencesResponse{
					Status: rpc.StatusOk(),
					UserPreferences: []*user.UserPreference{
						{
							ActorId:        "actor-id",
							PreferenceType: user.PreferenceType_PREFERENCE_TYPE_APP_COMMS,
							PreferenceValue: &user.PreferenceValue{
								PrefVal: &user.PreferenceValue_AppCommsPreference{
									AppCommsPreference: &user.CommsPreference{
										CommsPreferenceInfos: []*user.CommsPreferenceInfo{
											{
												Area:     comms.Area_ALL,
												Medium:   comms.Medium_EMAIL,
												Feature:  comms.Feature_PROMOTIONS,
												Signal:   user.CommsSignal_OFF,
												Category: comms.Category_CATEGORY_PROMOTIONAL,
											},
										},
									},
								},
							},
						},
					},
				}, nil)
			},
			want: &unsubscribe.GetCommsAreaSubscriptionStatusForUserResponse{
				UserId: "user-id",
				Areas: []*unsubscribe.Areas{
					{
						Area:       comms.Area_CREDIT_CARD.String(),
						Subscribed: false,
					},
					{
						Area:       comms.Area_LOANS.String(),
						Subscribed: false,
					},
					{
						Area:       comms.Area_INVEST.String(),
						Subscribed: false,
					},
					{
						Area:       comms.Area_INSIGHTS.String(),
						Subscribed: false,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "success, get area ALL with signal ON",
			args: args{
				ctx: context.Background(),
				req: &unsubscribe.GetCommsAreaSubscriptionStatusForUserRequest{
					UserId: "user-id",
					Source: "Moengage",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockVendorMappingClient.EXPECT().GetActorIdByVendorId(gomock.Any(), &vendormappingPb.GetActorIdByVendorIdRequest{
					VendorId: &vendormappingPb.GetActorIdByVendorIdRequest_MoengageId{
						MoengageId: "user-id",
					},
				}).Return(&vendormappingPb.GetActorIdByVendorIdResponse{
					Status:  rpc.StatusOk(),
					ActorId: "actor-id",
				}, nil)
				mockUserClient.EXPECT().GetUserPreferences(gomock.Any(), &user.GetUserPreferencesRequest{
					ActorId: "actor-id",
					PreferenceTypes: []user.PreferenceType{
						user.PreferenceType_PREFERENCE_TYPE_APP_COMMS,
					},
				}).Return(&user.GetUserPreferencesResponse{
					Status: rpc.StatusOk(),
					UserPreferences: []*user.UserPreference{
						{
							ActorId:        "actor-id",
							PreferenceType: user.PreferenceType_PREFERENCE_TYPE_APP_COMMS,
							PreferenceValue: &user.PreferenceValue{
								PrefVal: &user.PreferenceValue_AppCommsPreference{
									AppCommsPreference: &user.CommsPreference{
										CommsPreferenceInfos: []*user.CommsPreferenceInfo{
											{
												Area:     comms.Area_ALL,
												Medium:   comms.Medium_EMAIL,
												Feature:  comms.Feature_PROMOTIONS,
												Signal:   user.CommsSignal_ON,
												Category: comms.Category_CATEGORY_PROMOTIONAL,
											},
										},
									},
								},
							},
						},
					},
				}, nil)
			},
			want: &unsubscribe.GetCommsAreaSubscriptionStatusForUserResponse{
				UserId: "user-id",
				Areas: []*unsubscribe.Areas{
					{
						Area:       comms.Area_CREDIT_CARD.String(),
						Subscribed: true,
					},
					{
						Area:       comms.Area_LOANS.String(),
						Subscribed: true,
					},
					{
						Area:       comms.Area_INVEST.String(),
						Subscribed: true,
					},
					{
						Area:       comms.Area_INSIGHTS.String(),
						Subscribed: true,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "success, get area loans, credit card OFF and invest as ON",
			args: args{
				ctx: context.Background(),
				req: &unsubscribe.GetCommsAreaSubscriptionStatusForUserRequest{
					UserId: "user-id",
					Source: "Moengage",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockVendorMappingClient.EXPECT().GetActorIdByVendorId(gomock.Any(), &vendormappingPb.GetActorIdByVendorIdRequest{
					VendorId: &vendormappingPb.GetActorIdByVendorIdRequest_MoengageId{
						MoengageId: "user-id",
					},
				}).Return(&vendormappingPb.GetActorIdByVendorIdResponse{
					Status:  rpc.StatusOk(),
					ActorId: "actor-id",
				}, nil)
				mockUserClient.EXPECT().GetUserPreferences(gomock.Any(), &user.GetUserPreferencesRequest{
					ActorId: "actor-id",
					PreferenceTypes: []user.PreferenceType{
						user.PreferenceType_PREFERENCE_TYPE_APP_COMMS,
					},
				}).Return(&user.GetUserPreferencesResponse{
					Status: rpc.StatusOk(),
					UserPreferences: []*user.UserPreference{
						{
							ActorId:        "actor-id",
							PreferenceType: user.PreferenceType_PREFERENCE_TYPE_APP_COMMS,
							PreferenceValue: &user.PreferenceValue{
								PrefVal: &user.PreferenceValue_AppCommsPreference{
									AppCommsPreference: &user.CommsPreference{
										CommsPreferenceInfos: []*user.CommsPreferenceInfo{
											{
												Area:     comms.Area_CREDIT_CARD,
												Medium:   comms.Medium_EMAIL,
												Feature:  comms.Feature_PROMOTIONS,
												Signal:   user.CommsSignal_OFF,
												Category: comms.Category_CATEGORY_PROMOTIONAL,
											},
											{
												Area:     comms.Area_LOANS,
												Medium:   comms.Medium_EMAIL,
												Feature:  comms.Feature_PROMOTIONS,
												Signal:   user.CommsSignal_OFF,
												Category: comms.Category_CATEGORY_PROMOTIONAL,
											},
											{
												Area:     comms.Area_INVEST,
												Medium:   comms.Medium_EMAIL,
												Feature:  comms.Feature_PROMOTIONS,
												Signal:   user.CommsSignal_ON,
												Category: comms.Category_CATEGORY_PROMOTIONAL,
											},
										},
									},
								},
							},
						},
					},
				}, nil)
			},
			want: &unsubscribe.GetCommsAreaSubscriptionStatusForUserResponse{
				UserId: "user-id",
				Areas: []*unsubscribe.Areas{
					{
						Area:       comms.Area_CREDIT_CARD.String(),
						Subscribed: false,
					},
					{
						Area:       comms.Area_LOANS.String(),
						Subscribed: false,
					},
					{
						Area:       comms.Area_INVEST.String(),
						Subscribed: true,
					},
					{
						Area:       comms.Area_INSIGHTS.String(),
						Subscribed: true,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockVendorMappingClient := mockVendorMapping.NewMockVendorMappingServiceClient(ctr)
			mockUserClient := mockUser.NewMockUsersClient(ctr)
			s := &Service{
				vendorMappingClient: mockVendorMappingClient,
				userClient:          mockUserClient,
			}
			tt.setupMockCalls(mockUserClient, mockVendorMappingClient)
			got, err := s.GetCommsAreaSubscriptionStatusForUser(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCommsAreaSubscriptionStatusForUser error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isDeepEqualResponse(got, tt.want) {
				t.Errorf("GetCommsAreaSubscriptionStatusForUser() got = %v, want %v", got, tt.want)
			}
		})
	}
}

type ByAreaAndSubscribed []*unsubscribe.Areas

// Len returns the length of the slice
func (a ByAreaAndSubscribed) Len() int {
	return len(a)
}

// Swap swaps the elements at indices i and j
func (a ByAreaAndSubscribed) Swap(i, j int) {
	a[i], a[j] = a[j], a[i]
}

func (a ByAreaAndSubscribed) Less(i, j int) bool {
	// First, compare by Area
	if a[i].Area != a[j].Area {
		return a[i].Area < a[j].Area
	}
	// If Area is the same, compare by Subscribed
	return !a[i].Subscribed && a[j].Subscribed
}
func isDeepEqualResponse(got, want *unsubscribe.GetCommsAreaSubscriptionStatusForUserResponse) bool {
	if got.GetUserId() != want.GetUserId() || len(got.GetAreas()) != len(want.GetAreas()) {
		return false
	}
	sort.Sort(ByAreaAndSubscribed(got.GetAreas()))
	sort.Sort(ByAreaAndSubscribed(want.GetAreas()))
	if !reflect.DeepEqual(got, want) {
		return false
	}
	return true
}

// nolint: govet
func TestService_EmailUnsubscribe(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	type args struct {
		ctx context.Context
		req *unsubscribe.EmailUnsubscribeRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient)
		want           *emptyPb.Empty
		wantErr        bool
	}{
		{
			name: "empty user id",
			args: args{
				ctx: context.Background(),
				req: &unsubscribe.EmailUnsubscribeRequest{
					UserId: "",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "empty areas",
			args: args{
				ctx: context.Background(),
				req: &unsubscribe.EmailUnsubscribeRequest{
					UserId: "user-id",
					Areas:  nil,
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "unsupported source",
			args: args{
				ctx: context.Background(),
				req: &unsubscribe.EmailUnsubscribeRequest{
					UserId: "user-id",
					Areas:  []string{"ALL"},
					Source: "invalid-source",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "error in getActorIdFromVendorId rpc",
			args: args{
				ctx: context.Background(),
				req: &unsubscribe.EmailUnsubscribeRequest{
					UserId: "user-id",
					Areas:  []string{"ALL"},
					Source: "Moengage",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockVendorMappingClient.EXPECT().GetActorIdByVendorId(gomock.Any(), &vendormappingPb.GetActorIdByVendorIdRequest{
					VendorId: &vendormappingPb.GetActorIdByVendorIdRequest_MoengageId{
						MoengageId: "user-id",
					},
				}).Return(&vendormappingPb.GetActorIdByVendorIdResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "error in SetUserPreferences rpc",
			args: args{
				ctx: context.Background(),
				req: &unsubscribe.EmailUnsubscribeRequest{
					UserId: "user-id",
					Areas:  []string{"ALL"},
					Source: "Moengage",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockVendorMappingClient.EXPECT().GetActorIdByVendorId(gomock.Any(), &vendormappingPb.GetActorIdByVendorIdRequest{
					VendorId: &vendormappingPb.GetActorIdByVendorIdRequest_MoengageId{
						MoengageId: "user-id",
					},
				}).Return(&vendormappingPb.GetActorIdByVendorIdResponse{
					Status:  rpc.StatusOk(),
					ActorId: "actor-id",
				}, nil)
				mockUserClient.EXPECT().SetUserPreferences(gomock.Any(), &user.SetUserPreferencesRequest{
					ActorId: "actor-id",
					Preferences: []*user.PreferenceTypeValuePair{
						{
							PreferenceType: user.PreferenceType_PREFERENCE_TYPE_APP_COMMS,
							PreferenceValue: &user.PreferenceValue{
								PrefVal: &user.PreferenceValue_AppCommsPreference{
									AppCommsPreference: &user.CommsPreference{
										CommsPreferenceInfos: []*user.CommsPreferenceInfo{
											{
												Area:     comms.Area_ALL,
												Medium:   comms.Medium_EMAIL,
												Feature:  comms.Feature_PROMOTIONS,
												Signal:   user.CommsSignal_OFF,
												Category: comms.Category_CATEGORY_PROMOTIONAL,
											},
										},
									},
								},
							},
						},
					},
				}).Return(&user.SetUserPreferencesResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "successful",
			args: args{
				ctx: context.Background(),
				req: &unsubscribe.EmailUnsubscribeRequest{
					UserId: "user-id",
					Areas:  []string{"ALL"},
					Source: "Moengage",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockVendorMappingClient.EXPECT().GetActorIdByVendorId(gomock.Any(), &vendormappingPb.GetActorIdByVendorIdRequest{
					VendorId: &vendormappingPb.GetActorIdByVendorIdRequest_MoengageId{
						MoengageId: "user-id",
					},
				}).Return(&vendormappingPb.GetActorIdByVendorIdResponse{
					Status:  rpc.StatusOk(),
					ActorId: "actor-id",
				}, nil)
				mockUserClient.EXPECT().SetUserPreferences(gomock.Any(), &user.SetUserPreferencesRequest{
					ActorId: "actor-id",
					Preferences: []*user.PreferenceTypeValuePair{
						{
							PreferenceType: user.PreferenceType_PREFERENCE_TYPE_APP_COMMS,
							PreferenceValue: &user.PreferenceValue{
								PrefVal: &user.PreferenceValue_AppCommsPreference{
									AppCommsPreference: &user.CommsPreference{
										CommsPreferenceInfos: []*user.CommsPreferenceInfo{
											{
												Area:     comms.Area_ALL,
												Medium:   comms.Medium_EMAIL,
												Feature:  comms.Feature_PROMOTIONS,
												Signal:   user.CommsSignal_OFF,
												Category: comms.Category_CATEGORY_PROMOTIONAL,
											},
										},
									},
								},
							},
						},
					},
				}).Return(&user.SetUserPreferencesResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want:    &emptyPb.Empty{},
			wantErr: false,
		},
		{
			name: "successful, for empty areas",
			args: args{
				ctx: context.Background(),
				req: &unsubscribe.EmailUnsubscribeRequest{
					UserId: "user-id",
					Areas:  nil,
					Source: "Moengage",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockVendorMappingClient.EXPECT().GetActorIdByVendorId(gomock.Any(), &vendormappingPb.GetActorIdByVendorIdRequest{
					VendorId: &vendormappingPb.GetActorIdByVendorIdRequest_MoengageId{
						MoengageId: "user-id",
					},
				}).Return(&vendormappingPb.GetActorIdByVendorIdResponse{
					Status:  rpc.StatusOk(),
					ActorId: "actor-id",
				}, nil)
				mockUserClient.EXPECT().SetUserPreferences(gomock.Any(), &user.SetUserPreferencesRequest{
					ActorId: "actor-id",
					Preferences: []*user.PreferenceTypeValuePair{
						{
							PreferenceType: user.PreferenceType_PREFERENCE_TYPE_APP_COMMS,
							PreferenceValue: &user.PreferenceValue{
								PrefVal: &user.PreferenceValue_AppCommsPreference{
									AppCommsPreference: &user.CommsPreference{
										CommsPreferenceInfos: []*user.CommsPreferenceInfo{
											{
												Area:     comms.Area_ALL,
												Medium:   comms.Medium_EMAIL,
												Feature:  comms.Feature_PROMOTIONS,
												Signal:   user.CommsSignal_ON,
												Category: comms.Category_CATEGORY_PROMOTIONAL,
											},
										},
									},
								},
							},
						},
					},
				}).Return(&user.SetUserPreferencesResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want:    &emptyPb.Empty{},
			wantErr: false,
		},
		{
			name: "successful, for areas loans, invest",
			args: args{
				ctx: context.Background(),
				req: &unsubscribe.EmailUnsubscribeRequest{
					UserId: "user-id",
					Areas:  []string{"LOANS", "INVEST"},
					Source: "Moengage",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockVendorMappingClient.EXPECT().GetActorIdByVendorId(gomock.Any(), &vendormappingPb.GetActorIdByVendorIdRequest{
					VendorId: &vendormappingPb.GetActorIdByVendorIdRequest_MoengageId{
						MoengageId: "user-id",
					},
				}).Return(&vendormappingPb.GetActorIdByVendorIdResponse{
					Status:  rpc.StatusOk(),
					ActorId: "actor-id",
				}, nil)
				mockUserClient.EXPECT().SetUserPreferences(gomock.Any(), &user.SetUserPreferencesRequest{
					ActorId: "actor-id",
					Preferences: []*user.PreferenceTypeValuePair{
						{
							PreferenceType: user.PreferenceType_PREFERENCE_TYPE_APP_COMMS,
							PreferenceValue: &user.PreferenceValue{
								PrefVal: &user.PreferenceValue_AppCommsPreference{
									AppCommsPreference: &user.CommsPreference{
										CommsPreferenceInfos: []*user.CommsPreferenceInfo{
											{
												Area:     comms.Area_LOANS,
												Medium:   comms.Medium_EMAIL,
												Feature:  comms.Feature_PROMOTIONS,
												Signal:   user.CommsSignal_OFF,
												Category: comms.Category_CATEGORY_PROMOTIONAL,
											},
											{
												Area:     comms.Area_INVEST,
												Medium:   comms.Medium_EMAIL,
												Feature:  comms.Feature_PROMOTIONS,
												Signal:   user.CommsSignal_OFF,
												Category: comms.Category_CATEGORY_PROMOTIONAL,
											},
										},
									},
								},
							},
						},
					},
				}).Return(&user.SetUserPreferencesResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want:    &emptyPb.Empty{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockVendorMappingClient := mockVendorMapping.NewMockVendorMappingServiceClient(ctr)
			mockUserClient := mockUser.NewMockUsersClient(ctr)
			s := &Service{
				vendorMappingClient: mockVendorMappingClient,
				userClient:          mockUserClient,
			}
			tt.setupMockCalls(mockUserClient, mockVendorMappingClient)
			got, err := s.EmailUnsubscribe(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("EmailUnsubscribe error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("EmailUnsubscribe() got = %v, want %v", got, tt.want)
			}
		})
	}
}
