package ignosis

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	aaAnalyticsPb "github.com/epifi/gamma/api/connected_account/analytics"
	igVnPb "github.com/epifi/gamma/api/vendornotification/aa/analytics/ignosis"
	igVendorPb "github.com/epifi/gamma/api/vendors/aa/analytics/ignosis"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/config/genconf"
	"github.com/epifi/gamma/vendornotification/redactor"
)

type IgnosisAaService struct {
	*igVnPb.UnimplementedIgnosisAaAnalyticsNotificationServer
	conf             *genconf.Config
	aaAnalysisClient aaAnalyticsPb.AnalyticsClient
}

func NewIgnosisAaService(
	conf *genconf.Config,
	aaAnalysisClient aaAnalyticsPb.AnalyticsClient,
) *IgnosisAaService {
	return &IgnosisAaService{
		conf:             conf,
		aaAnalysisClient: aaAnalysisClient,
	}
}

func (i *IgnosisAaService) ProcessAnalysisStatusCallback(ctx context.Context, req *igVendorPb.DetailedAnalysisStatusResponse) (*igVendorPb.CallbackResponse, error) {
	redactor.LogCallbackRequestData(ctx, req, "ProcessAnalysisStatusCallback", req.GetReferenceId(), vendorsRedactor.Config)
	aaRes, aaErr := i.aaAnalysisClient.ProcessAnalysisStatusCallback(ctx, &aaAnalyticsPb.ProcessAnalysisStatusCallbackRequest{
		VendorActorId:   req.GetTrackingId(),
		VendorRequestId: req.GetReferenceId(),
		AnalysisStatus:  req.GetJobStatus(),
	})

	if te := epifigrpc.RPCError(aaRes, aaErr); te != nil {
		if aaRes.GetStatus().GetCode() == rpcPb.StatusRecordNotFound().GetCode() {
			logger.Error(ctx, "analysis request not found", zap.String("tracking_id", req.GetTrackingId()), zap.String("reference_id", req.GetReferenceId()))
			return nil, status.Errorf(codes.Internal, "internal server error")
		}
		logger.Error(ctx, "error in receiving vendor callback", zap.Error(te))
		return nil, status.Errorf(codes.Internal, "internal server error")
	}

	return &igVendorPb.CallbackResponse{
		Status: codes.OK.String(),
	}, nil
}
