package aa

type ResponseObj struct {
	HttpStatusCode string
	ErrorCode      string
	ErrorMessage   string
	Response       string
}

var (
	JwsError = &ResponseObj{
		HttpStatusCode: "400",
		ErrorCode:      "SignatureDoesNotMatch",
		ErrorMessage:   "x-jws-signature is not valid",
	}
	ApiKeyError = &ResponseObj{
		HttpStatusCode: "401",
		ErrorCode:      "InvalidRequest",
		ErrorMessage:   "api key is not valid",
	}
	NotifierIdError = &ResponseObj{
		HttpStatusCode: "400",
		ErrorCode:      "InvalidNotifier",
		ErrorMessage:   "Invalid AA id",
	}
	NotifierTypeError = &ResponseObj{
		HttpStatusCode: "400",
		ErrorCode:      "InvalidNotifier",
		ErrorMessage:   "Invalid notifier type",
	}
	ConsentHandleError = &ResponseObj{
		HttpStatusCode: "400",
		ErrorCode:      "InvalidRequest",
		ErrorMessage:   "Invalid consent handle in request",
	}
	ConsentIdError = &ResponseObj{
		HttpStatusCode: "400",
		ErrorCode:      "InvalidConsentId",
		ErrorMessage:   "consent id is not valid",
	}
	SchematicError = &ResponseObj{
		HttpStatusCode: "400",
		ErrorCode:      "InvalidRequest",
		ErrorMessage:   "schematic error in request",
	}
	InvalidRequestError = &ResponseObj{
		HttpStatusCode: "400",
		ErrorCode:      "InvalidRequest",
		ErrorMessage:   "Request is not valid",
	}
	TimestampVariationError = &ResponseObj{
		HttpStatusCode: "400",
		ErrorCode:      "InvalidRequest",
		ErrorMessage:   "Consent Notification API response timestamp time should be between current time -15 minutes and current time +15 minutes",
	}
	TimestampInvalidError = &ResponseObj{
		HttpStatusCode: "400",
		ErrorCode:      "InvalidRequest",
		ErrorMessage:   "Timestamp format Validation for Consent Notification Request",
	}
	VersionError = &ResponseObj{
		HttpStatusCode: "404",
		ErrorCode:      "NoSuchVersion",
		ErrorMessage:   "Validation for valid version received in response",
	}
	SessionIdError = &ResponseObj{
		HttpStatusCode: "400",
		ErrorCode:      "InvalidSessionId",
		ErrorMessage:   "session id is not valid",
	}
	ServerError = &ResponseObj{
		HttpStatusCode: "500",
		ErrorCode:      "InternalServerError",
		ErrorMessage:   "Internal Server Error",
	}
	Success = &ResponseObj{
		HttpStatusCode: "200",
		Response:       "OK",
	}
	IpError = &ResponseObj{
		HttpStatusCode: "403",
		ErrorCode:      "IpWhitelistError",
		ErrorMessage:   "IP not whitelisted for receiving callbacks",
	}
)
