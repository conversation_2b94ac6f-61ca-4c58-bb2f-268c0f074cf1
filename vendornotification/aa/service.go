//nolint:dupl
package aa

import (
	"bytes"
	"context"
	"crypto/rsa"
	"encoding/json"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"github.com/golang/protobuf/jsonpb"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	jwsParser "github.com/lestrrat-go/jwx/jws"

	caPb "github.com/epifi/gamma/api/connected_account"
	beCaEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	vgAaPb "github.com/epifi/gamma/api/vendorgateway/aa"
	aaPb "github.com/epifi/gamma/api/vendornotification/aa"
	venAaPb "github.com/epifi/gamma/api/vendors/aa"
	"github.com/epifi/gamma/api/vendors/aa/onemoney"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	caPkg "github.com/epifi/gamma/pkg/connectedaccount"
	"github.com/epifi/be-common/pkg/crypto/jws"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/gamma/vendornotification/config/genconf"
	"github.com/epifi/gamma/vendornotification/metrics"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"
)

type NotificationService struct {
	consentPub queue.Publisher             // consent notification publisher
	fiPub      queue.Publisher             // FI notification publisher
	beCaClient caPb.ConnectedAccountClient // backend connected account client for validations
	crCache    caPkg.InMemoryAaCache       // sahamati token server and cr cache
	conf       *genconf.Config
	vgAaClient vgAaPb.AccountAggregatorClient
	accLinkPub queue.Publisher
}

func NewNotificationService(
	consentPub ConsentCallbackPublisher, fiPub FICallbackPublisher, beCaClient caPb.ConnectedAccountClient,
	crCache caPkg.InMemoryAaCache, conf *genconf.Config,
	vgAaClient vgAaPb.AccountAggregatorClient,
	accLinkPub AccountLinkStatusCallbackPublisher,
) *NotificationService {
	return &NotificationService{
		consentPub: consentPub,
		fiPub:      fiPub,
		beCaClient: beCaClient,
		crCache:    crCache,
		conf:       conf,
		vgAaClient: vgAaClient,
		accLinkPub: accLinkPub,
	}
}

type ConsentCallbackPublisher queue.Publisher
type FICallbackPublisher queue.Publisher
type AccountLinkStatusCallbackPublisher queue.Publisher

var (
	ConsentNotificationRequest                               = "ConsentNotification"
	FINotificationRequest                                    = "FINotification"
	AccountLinkNotificationRequest                           = "AccountLinkNotification"
	_                              aaPb.AANotificationServer = &NotificationService{}
)

func (an *NotificationService) getJwsSigner() (*jws.RsaJwsSigner, error) {
	confEpifiAaPrivateKey, confErr := caPkg.GetEpifiAaPrivateKeyVn(an.conf.AA())
	if confErr != nil {
		return nil, confErr
	}
	epifiAaPrivateKey, privateKeyErr := jwt.ParseRSAPrivateKeyFromPEM([]byte(confEpifiAaPrivateKey))
	if privateKeyErr != nil {
		return nil, errors.Wrap(privateKeyErr, "error loading epifi AA private key")
	}
	signer, signerErr := jws.NewRsaJwsSigner(an.conf.AA().EpifiAaKid(), epifiAaPrivateKey)
	if signerErr != nil {
		return nil, errors.Wrap(signerErr, "error loading signer for AA requests")
	}
	return signer, nil
}

func (an *NotificationService) ConsentNotification(
	ctx context.Context, req *venAaPb.ConsentNotification,
) (*venAaPb.AANotificationResponse, error) {
	redactor.LogCallbackRequestData(ctx, req, ConsentNotificationRequest, "", vendorsRedactor.Config)
	metrics.RecordAaConsentNotification(req.GetConsentStatusNotification().GetConsentStatus(), req.GetNotifier().GetId())
	ret := &venAaPb.AANotificationResponse{
		Ver: req.GetVer(), Timestamp: time.Now().UTC().Format(caPkg.TimestampFormat), TxnId: req.GetTxnId(),
	}
	if an.conf.AA().VerifyApiKeyAndJws() {
		if ok, errObj := validateConsentNotificationSchema(req); !ok {
			logger.Error(
				ctx, "error validating consent schema in callback", zap.Bool("ok", ok), zap.Any("errorObj", errObj),
			)
			return an.getResponse(ctx, errObj, ret)
		}
		if ok, errObj := an.validateNotifierForConsent(ctx, req.GetNotifier(), req.GetVer()); !ok {
			logger.Error(
				ctx, "error validating notifier for consent", zap.Bool("ok", ok), zap.Any("errorObj", errObj),
			)
			return an.getResponse(ctx, errObj, ret)
		}
		if ok, errObj, err := an.validate(ctx, req, req.GetNotifier(), req.GetVer(), req.GetTimestamp()); !ok {
			logger.Error(
				ctx, "error validating callback", zap.Bool("ok", ok), zap.Any("errorObj", errObj), zap.Error(err),
			)
			return an.getResponse(ctx, errObj, ret)
		}
		// for consent notifications other than active, check if consent handle is empty
		// if yes, return error if consent id is also empty
		// otherwise fetch the consent handle using the consent id and set it in the request
		if req.GetConsentStatusNotification().GetConsentStatus() != caPkg.ConsentActive && req.GetConsentStatusNotification().GetConsentHandle() == "" {
			consentId := req.GetConsentStatusNotification().GetConsentId()
			if consentId == "" {
				logger.Error(ctx, "error validating callback, consent handle and consent id both cannot be empty")
				return an.getResponse(ctx, ConsentIdError, ret)
			}
			resp, err := an.beCaClient.GetConsent(ctx, &caPb.GetConsentRequest{ConsentId: consentId})
			if err != nil {
				logger.Error(ctx, "error validating consent id in callback", zap.String(logger.CONSENT_ID, consentId), zap.Error(err))
				return an.getResponse(ctx, ConsentIdError, ret)
			}
			req.ConsentStatusNotification.ConsentHandle = resp.GetConsentHandle()
		}
		if ok, errObj := an.validateConsentHandle(
			ctx, req.GetConsentStatusNotification().GetConsentHandle(), req.GetNotifier(),
		); !ok {
			logger.Error(
				ctx, "error validating consent handle in callback", zap.Bool("ok", ok), zap.Any("errorObj", errObj),
			)
			return an.getResponse(ctx, errObj, ret)
		}
		if req.GetConsentStatusNotification().GetConsentId() != "" {
			if ok, errObj := an.validateConsentId(
				ctx, req.GetConsentStatusNotification().GetConsentHandle(),
				req.GetConsentStatusNotification().GetConsentId(),
			); !ok {
				logger.Error(
					ctx, "error validating consent id in callback", zap.Bool("ok", ok), zap.Any("errorObj", errObj),
				)
				return an.getResponse(ctx, errObj, ret)
			}
		}
	}
	// publish event to queue
	sqsMsgId, pubErr := an.consentPub.Publish(
		ctx, &aaPb.ConsentEvent{
			ConsentNotification: req,
			EventTimestamp:      timestamppb.Now(),
		},
	)
	if pubErr != nil {
		logger.Error(ctx, "error publishing consent callback to queue", zap.Error(pubErr))
		return an.getResponse(ctx, ServerError, ret)
	}
	logger.Info(ctx, "successfully pushed consent notification to queue", zap.String(logger.QUEUE_MESSAGE_ID, sqsMsgId))
	return an.getResponse(ctx, Success, ret)
}

func (an *NotificationService) FINotification(
	ctx context.Context, req *venAaPb.FINotification,
) (*venAaPb.AANotificationResponse, error) {
	logger.Info(ctx, "Received FI Notification with payload", zap.Any("payload", req),
		zap.String(logger.SESSION_ID, req.GetFiStatusNotification().GetSessionId()))
	for _, ss := range req.GetFiStatusNotification().GetFiStatusResponse() {
		for _, acc := range ss.GetAccounts() {
			metrics.RecordAaFiNotification(req.GetFiStatusNotification().GetSessionStatus(), acc.GetFi_Status(),
				ss.GetFipId(), req.GetNotifier().GetId())
		}
	}
	ret := &venAaPb.AANotificationResponse{
		Ver: req.GetVer(), Timestamp: time.Now().UTC().Format(caPkg.TimestampFormat), TxnId: req.GetTxnId(),
	}
	if an.conf.AA().VerifyApiKeyAndJws() {
		if ok, errObj := validateFINotificationSchema(req); !ok {
			logger.Error(ctx, "error validating FI schema in callback", zap.Bool("ok", ok), zap.Any("errorObj", errObj))
			return an.getResponse(ctx, errObj, ret)
		}
		if ok, errObj := an.validateNotifierForFI(ctx, req.GetNotifier(), req.GetVer()); !ok {
			logger.Error(
				ctx, "error validating notifier for FI", zap.Bool("ok", ok), zap.Any("errorObj", errObj),
			)
			return an.getResponse(ctx, errObj, ret)
		}
		if ok, errObj, err := an.validate(ctx, req, req.GetNotifier(), req.GetVer(), req.GetTimestamp()); !ok {
			logger.Error(
				ctx, "error validating callback", zap.Bool("ok", ok), zap.Any("errorObj", errObj), zap.Error(err),
			)
			return an.getResponse(ctx, errObj, ret)
		}
		if ok, errObj := an.validateSessionIdAndAccounts(ctx, req.GetFiStatusNotification().GetSessionId(), req); !ok {
			logger.Error(
				ctx, "error validating session id in callback", zap.Bool("ok", ok), zap.Any("errorObj", errObj),
			)
			return an.getResponse(ctx, errObj, ret)
		}
	}
	// publish event to queue
	_, pubErr := an.fiPub.Publish(
		ctx, &aaPb.FIEvent{
			FiNotification: req,
			EventTimestamp: timestamppb.Now(),
		},
	)
	if pubErr != nil {
		logger.Error(ctx, "error publishing FI callback to queue", zap.Error(pubErr))
		return an.getResponse(ctx, ServerError, ret)
	}
	return an.getResponse(ctx, Success, ret)
}

func (an *NotificationService) AccountLinkNotification(
	ctx context.Context, req *venAaPb.AccountLinkNotification,
) (*venAaPb.AANotificationResponse, error) {
	redactor.LogCallbackRequestData(ctx, req, AccountLinkNotificationRequest, "", vendorsRedactor.Config)
	metrics.RecordAaAccountNotification(req.GetAccountLinkStatusNotification().GetLinkStatus(), req.GetNotifier().GetId())
	ret := &venAaPb.AANotificationResponse{
		Ver: req.GetVer(), Timestamp: time.Now().UTC().Format(caPkg.TimestampFormat), TxnId: req.GetTxnId(),
	}
	if an.conf.AA().VerifyApiKeyAndJws() {
		if ok, errObj := validateAccountLinkNotificationSchema(req); !ok {
			logger.Error(
				ctx, "error validating account link schema in callback", zap.Bool("ok", ok), zap.Any("errorObj", errObj),
			)
			return an.getResponse(ctx, errObj, ret)
		}
		if ok, errObj, err := an.validate(ctx, req, req.GetNotifier(), req.GetVer(), req.GetTimestamp()); !ok {
			logger.Error(
				ctx, "error validating callback", zap.Bool("ok", ok), zap.Any("errorObj", errObj), zap.Error(err),
			)
			return an.getResponse(ctx, errObj, ret)
		}
	}
	// publish event to queue
	sqsMsgId, pubErr := an.accLinkPub.Publish(
		ctx, &aaPb.AccountLinkEvent{
			AccountLinkNotification: req,
			EventTimestamp:          timestamppb.Now(),
		},
	)
	if pubErr != nil {
		logger.Error(ctx, "error publishing account link notification to queue", zap.Error(pubErr))
		return an.getResponse(ctx, ServerError, ret)
	}
	logger.Info(
		ctx, "successfully pushed account link notification to queue", zap.String(logger.QUEUE_MESSAGE_ID, sqsMsgId),
	)
	return an.getResponse(ctx, Success, ret)
}

func validateAccountLinkNotificationSchema(req *venAaPb.AccountLinkNotification) (bool, *ResponseObj) {
	errObj := SchematicError
	if req.GetVer() == "" {
		errObj.ErrorMessage = "version not present in account link notification"
		return false, SchematicError
	}
	if req.GetTxnId() == "" {
		errObj.ErrorMessage = "txn id not present in account link notification"
		return false, SchematicError
	}
	if req.GetTimestamp() == "" {
		errObj.ErrorMessage = "timestamp not present in account link notification"
		return false, SchematicError
	}
	if req.GetAccountLinkStatusNotification() == nil {
		errObj.ErrorMessage = "GetAccountLinkStatusNotification object not present"
		return false, SchematicError
	}
	if req.GetNotifier() == nil {
		errObj.ErrorMessage = "notifier not present in account link notification"
		return false, SchematicError
	}
	if req.GetNotifier().GetId() == "" {
		errObj.ErrorMessage = "notifier id not present in account link notification"
		return false, SchematicError
	}
	if req.GetNotifier().GetType() == "" {
		errObj.ErrorMessage = "notifier type not present in account link notification"
		return false, SchematicError
	}
	if req.GetAccountLinkStatusNotification().GetLinkRefNumber() == "" {
		errObj.ErrorMessage = "link ref number not present in account link notification"
		return false, SchematicError
	}
	if req.GetAccountLinkStatusNotification().GetCustomerAddress() == "" {
		errObj.ErrorMessage = "customer address not present in account link notification"
		return false, SchematicError
	}
	if req.GetAccountLinkStatusNotification().GetLinkStatus() == "" {
		errObj.ErrorMessage = "link status not present in account link notification"
		return false, SchematicError
	}
	return true, nil
}

func getApiKeyAndSignature(ctx context.Context) (string, string) {
	var jwsSignature, aaApiKey string
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if sig, ok := md[caPkg.JwsSignatureGrpcHeaderKey]; ok {
			jwsSignature = strings.Join(sig, ",")
		}
		if key, ok := md[caPkg.AaApiKeyGrpcHeaderKey]; ok {
			aaApiKey = strings.Join(key, ",")
		}
	}
	return jwsSignature, aaApiKey
}

func (an *NotificationService) getResponse(
	ctx context.Context, errObj *ResponseObj, resp *venAaPb.AANotificationResponse,
) (*venAaPb.AANotificationResponse, error) {
	resp.ErrorCode = errObj.ErrorCode
	resp.ErrorMessage = errObj.ErrorMessage
	resp.Response = errObj.Response
	signature, err := an.generateResponseSignature(resp)
	if err != nil {
		logger.Error(ctx, "error generating response jws signature", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "error generating response jws signature")
	}
	if headerErr := grpc.SendHeader(
		ctx, metadata.New(
			map[string]string{
				caPkg.JwsSignatureGrpcHeaderKey: signature,
				caPkg.HttpStatusCodeGrpcHeader:  errObj.HttpStatusCode,
			},
		),
	); headerErr != nil {
		logger.Error(ctx, "error setting grpc headers", zap.Error(headerErr))
		return nil, status.Errorf(codes.Internal, "error setting grpc headers")
	}
	return resp, nil
}

func (an *NotificationService) validate(
	ctx context.Context, not proto.Message, notifier *venAaPb.Notifier, ver string,
	ts string,
) (bool, *ResponseObj, error) {
	if an.conf.AA().VerifyApiKeyAndJws() {
		entityInfo, err := an.getCrEntityDetails(ctx, notifier, ver)
		if err != nil {
			return false, ServerError, errors.Wrap(err, "error getting AA CR details")
		}
		an.conf.AA().IPWhiteListing().WhitelistedIPs = strings.Join(entityInfo.GetIps(), ",")
		if err := security.CheckWhiteList(
			ctx, an.conf.AA().IPWhiteListing(), an.conf.NumberOfHopsThatAddXForwardedFor(),
			an.conf.VpcCidrIPPrefix(),
		); err != nil {
			return false, IpError, errors.Wrap(err, "error checking for IP whitelisting")
		}
		jwsSignature, aaApiKey := getApiKeyAndSignature(ctx)
		if ok, errObj, err := an.validateApiKey(aaApiKey); !ok {
			return ok, errObj, errors.Wrap(err, "error validating API key")
		}
		if ok, errObj, err := an.validateJwsSignature(jwsSignature, not, entityInfo); !ok {
			return ok, errObj, errors.Wrap(err, "error validating JWS signature of request")
		}
	}
	if ok, errObj := validateVersionAndTs(ver, ts); !ok {
		return ok, errObj, errors.New("error validating version and timestamp")
	}
	return true, Success, nil
}

// Validate that consent id received in incoming request is the one present in DB
// which was stored when Get /Consent/handle/{id} call was made to the AA. In case consent id was not found in DB
// return false with nil error. In case of error fetching handle from backend, return error
// Verify that on making valid POST /Consent/Notification with Invalid consent id error response is received
func (n *NotificationService) validateConsentId(ctx context.Context, consentHandle, consentId string) (
	bool, *ResponseObj,
) {
	// Add UUID validation on consent id and check if query by consent id gives different consent handle to handle test case 1007
	if _, err := uuid.Parse(consentId); err != nil {
		return false, ConsentIdError
	}
	cResp, cErr := n.beCaClient.GetConsent(ctx, &caPb.GetConsentRequest{ConsentId: consentId})
	if cErr = epifigrpc.RPCError(cResp, cErr); cErr != nil {
		if cResp.GetStatus().IsRecordNotFound() {
			return true, nil
		}
		logger.Error(ctx, "error verifying consent id in consent notification", zap.Error(cErr))
		return false, ConsentIdError
	}
	if cResp.GetConsentHandle() != consentHandle {
		return false, ConsentIdError
	}
	return true, nil
}

// Validate that consent handle received in incoming request is the one present in DB
// which was stored when POST /Consent call was made to the AA. In case consent handle was not found in DB
// return false with nil error. In case of error fetching handle from backend, return error
// Test1 : Verify that on making valid POST /Consent/Notification with Invalid consent handle error response is received
func (n *NotificationService) validateConsentHandle(
	ctx context.Context, consentHandle string, notifier *venAaPb.Notifier,
) (bool, *ResponseObj) {
	resp, err := n.beCaClient.GetConsentRequestDetails(
		ctx, &caPb.GetConsentRequestDetailsRequest{ConsentHandle: consentHandle},
	)
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if resp.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "consent handle not found in epifi DB, discarding callback", zap.Error(err))
			return false, ConsentHandleError
		}
		logger.Error(ctx, "error verifying consent handle in consent notification", zap.Error(err))
		return false, ServerError
	}
	if !n.validateAaEntityId(resp.GetConsentRequest().GetAaEntity(), notifier) {
		return false, ConsentHandleError
	}
	if resp.GetConsentRequest().GetConsentHandleStatus() == beCaEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_FAILED {
		return false, ConsentHandleError
	}
	return true, nil
}

func (n *NotificationService) validateAaEntityId(aaEntity beCaEnumPb.AaEntity, notifier *venAaPb.Notifier) bool {
	switch aaEntity {
	case beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY:
		if n.conf.AA().OneMoneyCrId() != notifier.GetId() {
			return false
		}
	case beCaEnumPb.AaEntity_AA_ENTITY_AA_FINVU:
		if n.conf.AA().FinvuCrId() != notifier.GetId() {
			return false
		}
	case beCaEnumPb.AaEntity_AA_ENTITY_AA_AUJUS_DEFAULT:
		if caPkg.AujasDefaultAaId != notifier.GetId() {
			return false
		}
	case beCaEnumPb.AaEntity_AA_ENTITY_AA_AUJUS_ALTERNATE:
		if caPkg.AujasAlternateAaId != notifier.GetId() {
			return false
		}
	default:
		return false
	}
	return true
}

// Notifier type should be AA
// Notifier ID should be that of one money, if we expect callbacks from one money
// Notifier ID of one money should be taken from CR
// Test1 : Verify that on making valid POST /Consent/Notification with alternate AA id  error response is received
// Test2 : Verify that on making valid POST /Consent/Notification with FIP in notifier type error response is received
func (n *NotificationService) validateNotifierForConsent(ctx context.Context, notifier *venAaPb.Notifier, ver string) (
	bool, *ResponseObj,
) {
	_, aaApiKey := getApiKeyAndSignature(ctx)
	if aaApiKey == "" {
		return false, ApiKeyError
	}
	token, err := n.getJwtTokenFromApiKey(aaApiKey)
	if err != nil {
		return false, ApiKeyError
	}
	claims := token.Claims.(jwt.MapClaims)
	azpVal := claims["azp"]
	role := claims["roles"]
	entityInfo, err := n.getCrEntityDetails(ctx, notifier, ver)
	if err != nil {
		return false, ServerError
	}
	// If API key is of alternate AA and notifier is of default AA we return InvalidRequest
	if azpVal == caPkg.AujasAlternateAaId && notifier.GetId() == caPkg.AujasDefaultAaId {
		return false, InvalidRequestError
	}
	// If API key is if default AA and notifier is of alternate AA we return InvalidNotifier
	if azpVal == caPkg.AujasDefaultAaId && notifier.GetId() == caPkg.AujasAlternateAaId {
		return false, NotifierIdError
	}
	if notifier.GetType() != "AA" || role != "AA" {
		return false, NotifierTypeError
	}
	if notifier.GetId() != entityInfo.GetId() {
		return false, NotifierIdError
	}
	return true, nil
}

func (n *NotificationService) validateNotifierForFI(ctx context.Context, notifier *venAaPb.Notifier, ver string) (
	bool, *ResponseObj,
) {
	_, aaApiKey := getApiKeyAndSignature(ctx)
	if aaApiKey == "" {
		return false, ApiKeyError
	}
	token, err := n.getJwtTokenFromApiKey(aaApiKey)
	if err != nil {
		return false, ApiKeyError
	}
	claims := token.Claims.(jwt.MapClaims)
	azpVal := claims["azp"]
	role := claims["roles"]
	entityInfo, err := n.getCrEntityDetails(ctx, notifier, ver)
	if err != nil {
		return false, ServerError
	}
	if notifier.GetType() != "AA" || role != "AA" {
		return false, InvalidRequestError
	}
	if notifier.GetId() != entityInfo.GetId() || azpVal != notifier.GetId() {
		return false, InvalidRequestError
	}
	return true, nil
}

// If any fields in request are not populated, we treat it as schematic error
// T1 : Verify on making valid request to POST /Consent/Notification API  with schematic error for one field
// each subcase error response is received.
// NOTE : consent id is only present in case consent was approved not in other cases
func validateConsentNotificationSchema(req *venAaPb.ConsentNotification) (bool, *ResponseObj) {
	errObj := SchematicError
	if req.GetVer() == "" {
		errObj.ErrorMessage = "Validation for valid version received in response"
		return false, SchematicError
	}
	if req.GetTxnId() == "" {
		errObj.ErrorMessage = "txn id not present in consent notification"
		return false, SchematicError
	}
	if req.GetTimestamp() == "" {
		errObj.ErrorMessage = "Timestamp format Validation for Consent Notification Request"
		return false, SchematicError
	}
	if req.GetConsentStatusNotification() == nil {
		errObj.ErrorMessage = "consent status notification object not present in consent notification"
		return false, SchematicError
	}
	if req.GetNotifier() == nil {
		errObj.ErrorMessage = "notifier not present in consent notification"
		return false, SchematicError
	}
	if req.GetNotifier().GetId() == "" {
		errObj.ErrorMessage = "notifier id not present in consent notification"
		return false, SchematicError
	}
	if req.GetNotifier().GetType() == "" {
		errObj.ErrorMessage = "notifier type not present in consent notification"
		return false, SchematicError
	}
	if req.GetConsentStatusNotification().GetConsentStatus() == "" {
		errObj.ErrorMessage = "consent status not present in consent notification"
		return false, SchematicError
	}
	if _, err := uuid.Parse(req.GetTxnId()); err != nil {
		return false, SchematicError
	}
	cs := req.GetConsentStatusNotification().GetConsentStatus()
	if cs != caPkg.ConsentActive && cs != caPkg.ConsentRevoked && cs != caPkg.ConsentPaused && cs != caPkg.ConsentExpired && cs != caPkg.ConsentStatusRejected {
		errObj.ErrorMessage = "invalid consent status present in consent notification"
		return false, SchematicError
	}
	return true, nil
}

func validateVersionAndTs(ver string, ts string) (bool, *ResponseObj) {
	// validate API version
	if ver != venAaPb.ApiVersionV1 && ver != venAaPb.ApiVersionV2 {
		return false, VersionError
	}
	// validate timestamp format and value in UTC format since AA follows UTC
	tsPb, err := datetime.ParseIso8601DateTimeString(ts)
	if err != nil {
		return false, TimestampInvalidError
	}
	nowTime := time.Now().UTC()
	if tsPb.AsTime().Before(nowTime.Add(-15*time.Minute)) || tsPb.AsTime().After(nowTime.Add(15*time.Minute)) {
		return false, TimestampVariationError
	}
	return true, nil
}

// If any fields in request are not populated, we treat it as schematic error
// T1 : Verify on making valid request to POST /FI/Notification API  with schematic error for one field each subcase
// error response is received.
// nolint:funlen
func validateFINotificationSchema(req *venAaPb.FINotification) (bool, *ResponseObj) {
	errObj := SchematicError
	if req.GetVer() == "" {
		errObj.ErrorMessage = "version not present in consent notification"
		return false, SchematicError
	}
	if req.GetTxnId() == "" {
		errObj.ErrorMessage = "txn id not present in FI notification"
		return false, SchematicError
	}
	if req.GetTimestamp() == "" {
		errObj.ErrorMessage = "timestamp not present in FI notification"
		return false, SchematicError
	}
	if req.GetFiStatusNotification() == nil {
		errObj.ErrorMessage = "FI status notification object not present in FI notification"
		return false, SchematicError
	}
	if req.GetNotifier() == nil {
		errObj.ErrorMessage = "notifier not present in FI notification"
		return false, SchematicError
	}
	if req.GetNotifier().GetId() == "" {
		errObj.ErrorMessage = "notifier id not present in FI notification"
		return false, SchematicError
	}
	if req.GetNotifier().GetType() == "" {
		errObj.ErrorMessage = "notifier type not present in FI notification"
		return false, SchematicError
	}
	if req.GetFiStatusNotification().GetSessionId() == "" {
		errObj.ErrorMessage = "session id not present in FI notification"
		return false, SchematicError
	}
	if req.GetFiStatusNotification().GetSessionStatus() == "" {
		errObj.ErrorMessage = "session status not present in FI notification"
		return false, SchematicError
	}
	if req.GetFiStatusNotification().GetSessionStatus() != caPkg.SessionStatusActive &&
		req.GetFiStatusNotification().GetSessionStatus() != caPkg.SessionStatusCompleted &&
		req.GetFiStatusNotification().GetSessionStatus() != caPkg.SessionStatusFailed &&
		req.GetFiStatusNotification().GetSessionStatus() != caPkg.SessionStatusExpired {
		errObj.ErrorMessage = "invalid session status present in FI notification"
		return false, SchematicError
	}
	if _, err := uuid.Parse(req.GetTxnId()); err != nil {
		return false, SchematicError
	}
	if req.GetFiStatusNotification().GetFiStatusResponse() == nil || len(req.GetFiStatusNotification().GetFiStatusResponse()) == 0 {
		return false, SchematicError
	}
	for _, obj := range req.GetFiStatusNotification().GetFiStatusResponse() {
		if obj.GetFipId() == "" || obj.GetAccounts() == nil || len(obj.GetAccounts()) == 0 {
			return false, SchematicError
		}
		for _, acc := range obj.GetAccounts() {
			if acc.GetLinkRefNumber() == "" || acc.GetDescription() == "" || acc.GetFi_Status() == "" {
				return false, SchematicError
			}
		}
	}
	return true, nil
}

// getConsentDetails returns the consent details decoded and parsed from signed consent
func getConsentDetails(signedConsent string) (*onemoney.ConsentDetail, error) {
	if signedConsent == "" {
		return nil, errors.New("empty signed consent")
	}
	var consDetail onemoney.ConsentDetail
	msg, signErr := jwsParser.ParseString(signedConsent)
	if signErr != nil {
		return nil, errors.Wrap(signErr, "error parsing signed consent")
	}
	umAllowUnknownFields := jsonpb.Unmarshaler{AllowUnknownFields: false}
	umErr := umAllowUnknownFields.Unmarshal(bytes.NewBuffer(msg.Payload()), &consDetail)
	if umErr != nil {
		return nil, errors.Wrap(umErr, "error unmarshalling signed consent")
	}

	return &consDetail, nil
}

// Validate that session id received in incoming request is the one present in DB
// which was stored when POST /FI/Request call was made to the AA. In case session id was not found in DB
// return false with nil error. In case of error fetching handle from backend, return error
// Test1 : Verify that on making valid POST /FI/Notification with Invalid session id error response is received
func (n *NotificationService) validateSessionIdAndAccounts(
	ctx context.Context, sessionId string, req *venAaPb.FINotification,
) (bool, *ResponseObj) {
	resp, err := n.beCaClient.GetDataFetchAttempt(ctx, &caPb.GetDataFetchAttemptRequest{SessionId: sessionId})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if resp.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "session id not found in epifi DB, discarding callback", zap.Error(err))
			return false, SessionIdError
		}
		logger.Error(ctx, "error verifying session id in FI notification", zap.Error(err))
		return false, ServerError
	}
	if !n.validateAaEntityId(resp.GetAaEntity(), req.GetNotifier()) {
		return false, SessionIdError
	}

	consDetail, consDetailErr := getConsentDetails(resp.GetConsent().GetSignature())
	if consDetailErr != nil || consDetail.GetAccounts() == nil {
		logger.Error(ctx, "error getting consent detail", zap.Error(consDetailErr))
		return false, ServerError
	}

	// check if accounts in notification belong to consent or not
	for _, st := range req.GetFiStatusNotification().GetFiStatusResponse() {
		for _, acc := range st.GetAccounts() {
			found := false
			// this acc should be linked with consent for which data was requested
			for _, consentAccount := range consDetail.GetAccounts() {
				if consentAccount.GetLinkRefNumber() == acc.GetLinkRefNumber() {
					found = true
				}
			}
			if !found {
				return false, InvalidRequestError
			}
		}
	}
	return true, nil
}

func (n *NotificationService) getJwtTokenFromApiKey(apiKey string) (*jwt.Token, error) {
	sahamatiPublicKey, err := jws.LoadRSAPublicKeyFromJWS(n.conf.AA().SahamatiPublicKey())
	if err != nil {
		return nil, errors.Wrap(err, "error loading RSA public key from JWS for sahamati")
	}
	token, err := jwt.Parse(
		apiKey, func(token *jwt.Token) (interface{}, error) {
			return sahamatiPublicKey, nil
		},
	)
	if err != nil {
		return nil, errors.Wrap(err, "error parsing JWT token")
	}
	return token, nil
}

// Validate API key in request using CR and token server
func (n *NotificationService) validateApiKey(apiKey string) (bool, *ResponseObj, error) {
	token, err := n.getJwtTokenFromApiKey(apiKey)
	if err != nil {
		return false, ApiKeyError, errors.Wrap(err, "error getting JWT token")
	}
	if !token.Valid {
		return false, ApiKeyError, nil
	}
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return false, ApiKeyError, nil
	}
	// check whether issuer of the token is sahamati or not and owner of the token is AA one money or not
	issuer, ok := claims["iss"]
	if !ok {
		return false, ApiKeyError, nil
	}
	iss := issuer.(string)
	if iss != n.conf.AA().TokenIssuer() {
		return false, ApiKeyError, nil
	}
	_, ok = claims["azp"]
	if !ok {
		return false, ApiKeyError, nil
	}
	_, ok = claims["roles"]
	if !ok {
		return false, ApiKeyError, nil
	}
	return true, nil, nil
}

// Validate signature key in request using CR and token server
func (n *NotificationService) validateJwsSignature(
	signature string, req proto.Message, entityInfo *vgAaPb.EntityInfo,
) (bool, *ResponseObj, error) {
	p, ok := req.(venAaPb.IAaNotification)
	if !ok {
		return false, ServerError, errors.New("request does not implement IAaNotification")
	}
	// get om public key from CR or cache
	pk, pkErr := n.getAaPublicKey(entityInfo)
	if pkErr != nil {
		return false, ServerError, errors.Wrap(pkErr, "error getting one money public key")
	}
	signer, err := n.getJwsSigner()
	if err != nil {
		return false, nil, err
	}
	if verifyErr := signer.VerifyDetachedJwsSignature(signature, p.GetRawBytes(), pk); verifyErr != nil {
		return false, JwsError, errors.Wrap(verifyErr, "error verifying detached signature in inbound request")
	}
	return true, nil, nil
}

func (n *NotificationService) getAaPublicKey(entityInfo *vgAaPb.EntityInfo) (*rsa.PublicKey, error) {
	publicKeyJwk := entityInfo.GetCertificate()
	by, marshalErr := json.Marshal(publicKeyJwk)
	if marshalErr != nil {
		return nil, errors.Wrap(marshalErr, "error marshalling one money public key cert")
	}
	pk, loadKeyErr := jws.LoadRSAPublicKeyFromJWS(string(by))
	if loadKeyErr != nil {
		return nil, errors.Wrap(loadKeyErr, "error loading one money public key from jws")
	}
	return pk, nil
}

func (n *NotificationService) getCrEntityDetails(ctx context.Context, notifier *venAaPb.Notifier, ver string) (
	*vgAaPb.EntityInfo, error,
) {
	accessToken, getTokenErr := n.crCache.GetAccessToken(ctx, venAaPb.RebitVersionStringToEnum[ver])
	if getTokenErr != nil {
		return nil, errors.Wrap(getTokenErr, "error getting access token")
	}
	entityInfo, getCrErr := n.crCache.GetAaCrDetails(ctx, accessToken, notifier.GetId(), venAaPb.RebitVersionStringToEnum[ver])
	if getCrErr != nil {
		return nil, errors.Wrap(getCrErr, "error getting entity info from CR")
	}
	return entityInfo, nil
}

func (n *NotificationService) generateResponseSignature(resp *venAaPb.AANotificationResponse) (string, error) {
	m := jsonpb.Marshaler{} // same marshaller is used in vngw for unmarshalling bytes to proto
	b := &bytes.Buffer{}
	if err := m.Marshal(b, resp); err != nil {
		return "", errors.Wrap(err, "error marshalling response")
	}
	signer, err := n.getJwsSigner()
	if err != nil {
		return "", err
	}
	signature, err := signer.GenerateDetachedJwsSignature(b.Bytes())
	if err != nil {
		return "", errors.Wrap(err, "error generating detached jws signature")
	}
	return signature, nil
}
