package reward

import (
	"crypto/rsa"
	"encoding/base64"
	"fmt"

	rsaPkg "github.com/epifi/be-common/pkg/crypto/rsa"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/golang-jwt/jwt/v5"
	"github.com/pkg/errors"

	"github.com/epifi/gamma/vendornotification/config"
)

type JwtRsaVerifier struct {
	publicKeyPool []*rsa.PublicKey
}

func NewJWTVerifier(conf *config.Config) (*JwtRsaVerifier, error) {
	pubKeyList, err := loadPublicKey([]string{conf.SavenRewardVnSecrets.JwtVerificationKeyBase64})
	if err != nil {
		return nil, errors.Wrap(err, "failed to load public key")
	}
	return &JwtRsaVerifier{
		publicKeyPool: pubKeyList,
	}, nil
}

func (v *JwtRsaVerifier) VerifyTokenAndGetClaims(tokenString string) (jwt.MapClaims, error) {
	var (
		token *jwt.Token
		err   error
	)
	for _, pubKey := range v.publicKeyPool {
		token, err = jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
				return nil, errors.New("unexpected signing method")
			}
			return pubKey, nil
		})
	}

	if err != nil {
		return nil, errors.Wrap(err, "failed to verify and parse jwt token string")
	}

	if token == nil {
		return nil, errors.New("failed to parse jwt token string to get token with claims")
	}

	if !token.Valid {
		return nil, errors.New("invalid jwt token")
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errors.New("failed to get destructure token to get token claims")
	}

	issuer, err := claims.GetIssuer()
	if err != nil || issuer == "" {
		return nil, jwt.ErrTokenInvalidIssuer
	}
	// logging issue for token
	logger.InfoNoCtx(fmt.Sprintf("issuer for rewards callback request JWT token: %s", issuer))

	return claims, nil
}

// loadPublicKey converts a PEM-encoded public key string to an RSA private key
func loadPublicKey(base64PubKeyList []string) ([]*rsa.PublicKey, error) {
	rsaPubKeyPool := make([]*rsa.PublicKey, 0)
	for _, base64PubKey := range base64PubKeyList {
		decodePubKey, decodeErr := base64.StdEncoding.DecodeString(base64PubKey)
		if decodeErr != nil {
			return nil, errors.Wrap(decodeErr, "error while decoding base64 encoded rsa public key")
		}
		rsaPubKey, err := rsaPkg.ParsePKIXPublicKey(decodePubKey)
		if err != nil {
			return nil, errors.Wrap(err, "failed to parse public key")
		}
		rsaPubKeyPool = append(rsaPubKeyPool, rsaPubKey)
	}
	return rsaPubKeyPool, nil
}
