package types

import (
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/stream"
)

type EmailCallbackStreamProducer stream.Producer
type SmsCallbackStreamProducer stream.Producer
type VoiceMessageCallbackStreamProducer stream.Producer
type CallingCallbackStreamProducer stream.Producer
type WhatsappCallbackStreamProducer stream.Producer
type FederalEscalationUpdateEventPublisher queue.Publisher

type USStocksCacheStorage cache.CacheStorage

func USStocksCacheStorageProvider(cs USStocksCacheStorage) cache.CacheStorage { return cs }
