package externalredemptions

import (
	"context"
	"encoding/base64"
	"encoding/hex"
	"testing"

	"github.com/epifi/be-common/api/rpc"
	evrPb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	"github.com/epifi/gamma/api/casper/external_vendor_redemption/mocks"
	vendormappingPb "github.com/epifi/gamma/api/vendormapping"
	mockVendorMapping "github.com/epifi/gamma/api/vendormapping/mocks"
	"github.com/epifi/gamma/api/vendors/fistore"
	"github.com/epifi/be-common/pkg/crypto/aes"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/vendornotification/config/genconf"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/testing/protocmp"
)

func TestService_CreateFiStoreRedemption(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	a := require.New(t)
	gconf, err := genconf.Load()
	a.NoError(err)
	type args struct {
		ctx     context.Context
		request *fistore.FiStoreRedemptionRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient)
		want           *fistore.FiStoreRedemptionResponse
		wantErr        bool
	}{
		{
			name: "invalid vendor in request",
			args: args{
				ctx: context.Background(),
				request: &fistore.FiStoreRedemptionRequest{
					Data: GetEncryptedPayload(&fistore.CreateFiStoreRedemptionPayload{
						UserId:                      "user-id",
						VendorRefId:                 "ref-id",
						PaymentInstrumentIdentifier: "09090",
						Category:                    "ECOM",
						RedemptionDetails: []*fistore.RedemptionDetails{
							{
								ProductId:        "product-id-1",
								ProductName:      "product-name-1",
								ProductPrice:     1000,
								SpentCashUnits:   800,
								SpentFiCoinUnits: 2000,
								BrandName:        "brand-1",
								SubCategory:      "sub-category-1",
								OrderStatus:      "CONFIRMED",
								Quantity:         2,
							},
						},
						OrderTimestamp: "2023-11-11T20:42:00Z",
					}, gconf.DpandaVnSecrets().SecretKey, gconf.DpandaVnSecrets().Iv),
					Vendor: "DPANDAA",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "invalid encrypted payload in request",
			args: args{
				ctx: context.Background(),
				request: &fistore.FiStoreRedemptionRequest{
					Data:   "abhbwdjdw",
					Vendor: "DPANDA",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "mandatory fields are empty: user id",
			args: args{
				ctx: context.Background(),
				request: &fistore.FiStoreRedemptionRequest{
					Data: GetEncryptedPayload(&fistore.CreateFiStoreRedemptionPayload{
						UserId:                      "",
						VendorRefId:                 "ref-id",
						PaymentInstrumentIdentifier: "09090",
						Category:                    "ECOM",
						RedemptionDetails: []*fistore.RedemptionDetails{
							{
								ProductId:        "product-id-1",
								ProductName:      "product-name-1",
								ProductPrice:     1000,
								SpentCashUnits:   800,
								SpentFiCoinUnits: 2000,
								BrandName:        "brand-1",
								SubCategory:      "sub-category-1",
								OrderStatus:      "CONFIRMED",
								Quantity:         2,
							},
						},
						OrderTimestamp: "2023-11-11T20:42:00Z",
					}, gconf.DpandaVnSecrets().SecretKey, gconf.DpandaVnSecrets().Iv),
					Vendor: "DPANDA",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "mandatory fields are empty: vendor ref id",
			args: args{
				ctx: context.Background(),
				request: &fistore.FiStoreRedemptionRequest{
					Data: GetEncryptedPayload(&fistore.CreateFiStoreRedemptionPayload{
						UserId:                      "actor-id",
						VendorRefId:                 "",
						PaymentInstrumentIdentifier: "09090",
						Category:                    "ECOM",
						RedemptionDetails: []*fistore.RedemptionDetails{
							{
								ProductId:        "product-id-1",
								ProductName:      "product-name-1",
								ProductPrice:     1000,
								SpentCashUnits:   800,
								SpentFiCoinUnits: 2000,
								BrandName:        "brand-1",
								SubCategory:      "sub-category-1",
								OrderStatus:      "CONFIRMED",
								Quantity:         2,
							},
						},
						OrderTimestamp: "2023-11-11T20:42:00Z",
					}, gconf.DpandaVnSecrets().SecretKey, gconf.DpandaVnSecrets().Iv),
					Vendor: "DPANDA",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "mandatory fields are empty: redemption details",
			args: args{
				ctx: context.Background(),
				request: &fistore.FiStoreRedemptionRequest{
					Data: GetEncryptedPayload(&fistore.CreateFiStoreRedemptionPayload{
						UserId:                      "actor-id",
						VendorRefId:                 "",
						PaymentInstrumentIdentifier: "09090",
						Category:                    "ECOM",
						OrderTimestamp:              "2023-11-11T20:42:00Z",
					}, gconf.DpandaVnSecrets().SecretKey, gconf.DpandaVnSecrets().Iv),
					Vendor: "DPANDA",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "invalid category in request",
			args: args{
				ctx: context.Background(),
				request: &fistore.FiStoreRedemptionRequest{
					Data: GetEncryptedPayload(&fistore.CreateFiStoreRedemptionPayload{
						UserId:                      "actor-id",
						VendorRefId:                 "ref-id",
						PaymentInstrumentIdentifier: "09090",
						Category:                    "ECOMMERCE",
						RedemptionDetails: []*fistore.RedemptionDetails{
							{
								ProductId:        "product-id-1",
								ProductName:      "product-name-1",
								ProductPrice:     1000,
								SpentCashUnits:   800,
								SpentFiCoinUnits: 2000,
								BrandName:        "brand-1",
								SubCategory:      "sub-category-1",
								OrderStatus:      "CONFIRMED",
								Quantity:         2,
							},
						},
						OrderTimestamp: "2023-11-11T20:42:00Z",
					}, gconf.DpandaVnSecrets().SecretKey, gconf.DpandaVnSecrets().Iv),
					Vendor: "DPANDA",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "invalid order timestamp",
			args: args{
				ctx: context.Background(),
				request: &fistore.FiStoreRedemptionRequest{
					Data: GetEncryptedPayload(&fistore.CreateFiStoreRedemptionPayload{
						UserId:                      "actor-id",
						VendorRefId:                 "ref-id",
						PaymentInstrumentIdentifier: "09090",
						Category:                    "ECOM",
						RedemptionDetails: []*fistore.RedemptionDetails{
							{
								ProductId:        "product-id-1",
								ProductName:      "product-name-1",
								ProductPrice:     1000,
								SpentCashUnits:   800,
								SpentFiCoinUnits: 2000,
								BrandName:        "brand-1",
								SubCategory:      "sub-category-1",
								OrderStatus:      "CONFIRMED",
								Quantity:         2,
							},
						},
						OrderTimestamp: "2023-11-11",
					}, gconf.DpandaVnSecrets().SecretKey, gconf.DpandaVnSecrets().Iv),
					Vendor: "DPANDA",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "invalid order status in request",
			args: args{
				ctx: context.Background(),
				request: &fistore.FiStoreRedemptionRequest{
					Data: GetEncryptedPayload(&fistore.CreateFiStoreRedemptionPayload{
						UserId:                      "user-id",
						VendorRefId:                 "ref-id",
						PaymentInstrumentIdentifier: "09090",
						Category:                    "ECOM",
						RedemptionDetails: []*fistore.RedemptionDetails{
							{
								ProductId:        "product-id-1",
								ProductName:      "product-name-1",
								ProductPrice:     1000,
								SpentCashUnits:   800,
								SpentFiCoinUnits: 2000,
								BrandName:        "brand-1",
								SubCategory:      "sub-category-1",
								OrderStatus:      "CONFIRMEDdd",
								Quantity:         2,
							},
						},
						OrderTimestamp: "2023-11-11T20:42:00Z",
					}, gconf.DpandaVnSecrets().SecretKey, gconf.DpandaVnSecrets().Iv),
					Vendor: "DPANDA",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "error while fetching actor id from vendor id",
			args: args{
				ctx: context.Background(),
				request: &fistore.FiStoreRedemptionRequest{
					Data: GetEncryptedPayload(&fistore.CreateFiStoreRedemptionPayload{
						UserId:                      "user-id",
						VendorRefId:                 "ref-id",
						PaymentInstrumentIdentifier: "09090",
						Category:                    "ECOM",
						RedemptionDetails: []*fistore.RedemptionDetails{
							{
								ProductId:        "product-id-1",
								ProductName:      "product-name-1",
								ProductPrice:     1000,
								SpentCashUnits:   800,
								SpentFiCoinUnits: 2000,
								BrandName:        "brand-1",
								SubCategory:      "sub-category-1",
								OrderStatus:      "CONFIRMED",
								Quantity:         2,
							},
						},
						OrderTimestamp: "2023-11-11T20:42:00Z",
					}, gconf.DpandaVnSecrets().SecretKey, gconf.DpandaVnSecrets().Iv),
					Vendor: "DPANDA",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockVendorMappingClient.EXPECT().GetInputIdByVendor(gomock.Any(), gomock.Any()).Return(&vendormappingPb.GetInputIdByVendorResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "error while calling upsert RPC",
			args: args{
				ctx: context.Background(),
				request: &fistore.FiStoreRedemptionRequest{
					Data: GetEncryptedPayload(&fistore.CreateFiStoreRedemptionPayload{
						UserId:                      "user-id",
						VendorRefId:                 "ref-id",
						PaymentInstrumentIdentifier: "09090",
						Category:                    "ECOM",
						RedemptionDetails: []*fistore.RedemptionDetails{
							{
								ProductId:        "product-id-1",
								ProductName:      "product-name-1",
								ProductPrice:     1000,
								SpentCashUnits:   800,
								SpentFiCoinUnits: 2000,
								BrandName:        "brand-1",
								SubCategory:      "sub-category-1",
								OrderStatus:      "CONFIRMED",
								Quantity:         2,
							},
						},
						OrderTimestamp: "2023-11-11T20:42:00Z",
					}, gconf.DpandaVnSecrets().SecretKey, gconf.DpandaVnSecrets().Iv),
					Vendor: "DPANDA",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockVendorMappingClient.EXPECT().GetInputIdByVendor(gomock.Any(), gomock.Any()).Return(&vendormappingPb.GetInputIdByVendorResponse{
					Status:  rpc.StatusOk(),
					InputId: "ACnyH99au1SruMfst3S65fwQ230608==",
				}, nil)
				externalVendorRedemptionClient.EXPECT().CreateFiStoreRedemptions(gomock.Any(), gomock.Any()).Return(&evrPb.CreateFiStoreRedemptionsResponse{Status: rpc.StatusInternal()}, nil)
			},
			wantErr: true,
		},
		{
			name: "successfully create the data in db",
			args: args{
				ctx: context.Background(),
				request: &fistore.FiStoreRedemptionRequest{
					Data: GetEncryptedPayload(&fistore.CreateFiStoreRedemptionPayload{
						UserId:                      "user-id",
						VendorRefId:                 "ref-id",
						PaymentInstrumentIdentifier: "09090",
						Category:                    "ECOM",
						RedemptionDetails: []*fistore.RedemptionDetails{
							{
								ProductId:        "product-id-1",
								ProductName:      "product-name-1",
								ProductPrice:     1000,
								SpentCashUnits:   800,
								SpentFiCoinUnits: 2000,
								BrandName:        "brand-1",
								SubCategory:      "sub-category-1",
								OrderStatus:      "CONFIRMED",
								Quantity:         2,
							},
						},
						OrderTimestamp: "2023-11-11T20:42:00Z",
					}, gconf.DpandaVnSecrets().SecretKey, gconf.DpandaVnSecrets().Iv),
					Vendor: "DPANDA",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockVendorMappingClient.EXPECT().GetInputIdByVendor(gomock.Any(), gomock.Any()).Return(&vendormappingPb.GetInputIdByVendorResponse{
					Status:  rpc.StatusOk(),
					InputId: "ACnyH99au1SruMfst3S65fwQ230608==",
				}, nil)
				externalVendorRedemptionClient.EXPECT().CreateFiStoreRedemptions(gomock.Any(), gomock.Any()).Return(&evrPb.CreateFiStoreRedemptionsResponse{Status: rpc.StatusOk()}, nil)
			},
			want:    &fistore.FiStoreRedemptionResponse{Status: rpc.StatusOk().GetShortMessage()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockVendorMappingClient := mockVendorMapping.NewMockVendorMappingServiceClient(ctr)
			mockExternalVendorRedemptionClient := mocks.NewMockExternalVendorRedemptionServiceClient(ctr)
			s := &Service{
				conf:                           gconf,
				vendorMappingClient:            mockVendorMappingClient,
				externalVendorRedemptionClient: mockExternalVendorRedemptionClient,
			}
			tt.setupMockCalls(mockExternalVendorRedemptionClient, mockVendorMappingClient)
			got, err := s.CreateFiStoreRedemption(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateFiStoreRedemption error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("CreateFiStoreRedemption() diff = %s", diff)
			}
		})
	}
}

func GetEncryptedPayload(vendorReqPayload *fistore.CreateFiStoreRedemptionPayload, secretKey, iv string) string {
	vendorReqPayloadStr := protojson.Format(vendorReqPayload)
	hexEncodedKey := hex.EncodeToString([]byte(secretKey))
	aesCbcCryptor := aes.NewAesCbcCryptor(hexEncodedKey)
	cipherText, err := aesCbcCryptor.Encrypt(context.Background(), []byte(vendorReqPayloadStr), iv)
	if err != nil {
		logger.ErrorNoCtx("error while encrypting payload", zap.Error(err))
		return ""
	}
	base64EncodedData := base64.StdEncoding.EncodeToString(cipherText)
	return base64EncodedData
}

func TestService_UpdateFiStoreRedemption(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	a := require.New(t)
	gconf, err := genconf.Load()
	a.NoError(err)
	type args struct {
		ctx     context.Context
		request *fistore.FiStoreRedemptionRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient)
		want           *fistore.FiStoreRedemptionResponse
		wantErr        bool
	}{
		{
			name: "invalid vendor in request",
			args: args{
				ctx: context.Background(),
				request: &fistore.FiStoreRedemptionRequest{
					Data: GetUpdateEncryptedPayload(&fistore.UpdateFiStoreRedemptionPayload{
						VendorRefId:       "ref-id",
						ProductId:         "product-id-1",
						OrderStatus:       "CONFIRMED",
						OrderTrackingLink: "",
					}, gconf.DpandaVnSecrets().SecretKey, gconf.DpandaVnSecrets().Iv),
					Vendor: "DPANDAA",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "invalid encrypted payload in request",
			args: args{
				ctx: context.Background(),
				request: &fistore.FiStoreRedemptionRequest{
					Data:   "abhbwdjdw",
					Vendor: "DPANDA",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "mandatory fields are empty: vendor ref id",
			args: args{
				ctx: context.Background(),
				request: &fistore.FiStoreRedemptionRequest{
					Data: GetUpdateEncryptedPayload(&fistore.UpdateFiStoreRedemptionPayload{
						VendorRefId:       "",
						ProductId:         "product-id-1",
						OrderStatus:       "CONFIRMED",
						OrderTrackingLink: "",
					}, gconf.DpandaVnSecrets().SecretKey, gconf.DpandaVnSecrets().Iv),
					Vendor: "DPANDA",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "invalid order status in request",
			args: args{
				ctx: context.Background(),
				request: &fistore.FiStoreRedemptionRequest{
					Data: GetUpdateEncryptedPayload(&fistore.UpdateFiStoreRedemptionPayload{
						VendorRefId:       "ref-id",
						ProductId:         "product-id-1",
						OrderStatus:       "pending",
						OrderTrackingLink: "",
					}, gconf.DpandaVnSecrets().SecretKey, gconf.DpandaVnSecrets().Iv),
					Vendor: "DPANDA",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "error while calling upsert RPC",
			args: args{
				ctx: context.Background(),
				request: &fistore.FiStoreRedemptionRequest{
					Data: GetUpdateEncryptedPayload(&fistore.UpdateFiStoreRedemptionPayload{
						VendorRefId:       "ref-id",
						ProductId:         "product-id-1",
						OrderStatus:       "CONFIRMED",
						OrderTrackingLink: "",
					}, gconf.DpandaVnSecrets().SecretKey, gconf.DpandaVnSecrets().Iv),
					Vendor: "DPANDA",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				externalVendorRedemptionClient.EXPECT().UpdateFiStoreRedemptions(gomock.Any(), gomock.Any()).Return(&evrPb.UpdateFiStoreRedemptionsResponse{Status: rpc.StatusInternal()}, nil)
			},
			wantErr: true,
		},
		{
			name: "successfully create the data in db",
			args: args{
				ctx: context.Background(),
				request: &fistore.FiStoreRedemptionRequest{
					Data: GetUpdateEncryptedPayload(&fistore.UpdateFiStoreRedemptionPayload{
						VendorRefId:       "ref-id",
						ProductId:         "product-id-1",
						OrderStatus:       "CONFIRMED",
						OrderTrackingLink: "",
					}, gconf.DpandaVnSecrets().SecretKey, gconf.DpandaVnSecrets().Iv),
					Vendor: "DPANDA",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				externalVendorRedemptionClient.EXPECT().UpdateFiStoreRedemptions(gomock.Any(), gomock.Any()).Return(&evrPb.UpdateFiStoreRedemptionsResponse{Status: rpc.StatusOk()}, nil)
			},
			want:    &fistore.FiStoreRedemptionResponse{Status: rpc.StatusOk().GetShortMessage()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockVendorMappingClient := mockVendorMapping.NewMockVendorMappingServiceClient(ctr)
			mockExternalVendorRedemptionClient := mocks.NewMockExternalVendorRedemptionServiceClient(ctr)
			s := &Service{
				conf:                           gconf,
				vendorMappingClient:            mockVendorMappingClient,
				externalVendorRedemptionClient: mockExternalVendorRedemptionClient,
			}
			tt.setupMockCalls(mockExternalVendorRedemptionClient, mockVendorMappingClient)
			got, err := s.UpdateFiStoreRedemption(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateFiStoreRedemption error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("UpdateFiStoreRedemption() diff = %s", diff)
			}
		})
	}
}

func GetUpdateEncryptedPayload(vendorReqPayload *fistore.UpdateFiStoreRedemptionPayload, secretKey, iv string) string {
	vendorReqPayloadStr := protojson.Format(vendorReqPayload)
	hexEncodedKey := hex.EncodeToString([]byte(secretKey))
	aesCbcCryptor := aes.NewAesCbcCryptor(hexEncodedKey)
	cipherText, err := aesCbcCryptor.Encrypt(context.Background(), []byte(vendorReqPayloadStr), iv)
	if err != nil {
		logger.ErrorNoCtx("error while encrypting payload", zap.Error(err))
		return ""
	}
	base64EncodedData := base64.StdEncoding.EncodeToString(cipherText)
	return base64EncodedData
}
