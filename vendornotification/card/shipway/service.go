package shipway

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/datetime"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/golang/protobuf/ptypes/empty"

	"github.com/epifi/be-common/pkg/queue"

	cardProvisioningPb "github.com/epifi/gamma/api/card/provisioning"
	shipwayPb "github.com/epifi/gamma/api/vendornotification/card/shipway"
	pb "github.com/epifi/gamma/api/vendors/shipway"
)

const (
	timeStampFormat = "2006-01-02 15:04:05"
)

var (
	// TODO(priyansh) : Add more states here as per the ones in webhooks
	codeToDeliveryStateMap = map[string]cardProvisioningPb.CardTrackingDeliveryState{
		"INT": cardProvisioningPb.CardTrackingDeliveryState_IN_TRANSIT,
		"OOD": cardProvisioningPb.CardTrackingDeliveryState_OUT_FOR_DELIVERY,
		"DEL": cardProvisioningPb.CardTrackingDeliveryState_DELIVERED,
		"RTO": cardProvisioningPb.CardTrackingDeliveryState_RETURNED_TO_ORIGIN,
	}
	statusToDeliveryStateMap = map[string]cardProvisioningPb.CardTrackingDeliveryState{
		"Shipment Recieved at Origin Center": cardProvisioningPb.CardTrackingDeliveryState_SHIPPED,
		"Out for delivery":                   cardProvisioningPb.CardTrackingDeliveryState_OUT_FOR_DELIVERY,
		"Delivered to consignee":             cardProvisioningPb.CardTrackingDeliveryState_DELIVERED,
	}
)

type Service struct {
	cardTrackingCallbackPublisher queue.Publisher
	shipwayPb.UnimplementedShipwayServer
}

type CardTrackingCallbackPublisher queue.Publisher

func NewService(cardTrackingCallbackPublisher CardTrackingCallbackPublisher) *Service {
	return &Service{cardTrackingCallbackPublisher: cardTrackingCallbackPublisher}
}

func (s *Service) CardTrackingCallbacks(ctx context.Context, req *pb.CardTrackingCallbacksRequest) (*empty.Empty, error) {
	event := &cardProvisioningPb.ProcessCardTrackingCallbackRequest{}

	logger.Info(ctx, "Received card tracking callback from Shipway", zap.Any("callback", req.GetTrackingStatusList()))

	constructEventRequest(ctx, req, event)
	// publish the packet to the queue to be consumed by the card BE service. The retries are also handled using the
	// same queue.
	if _, err := s.cardTrackingCallbackPublisher.Publish(ctx, event); err != nil {
		logger.Error(ctx, fmt.Sprintf("Unable to publish card tracking call back to the queue %v", event), zap.Error(err))
		return nil, err
	}

	return &empty.Empty{}, nil
}

func constructEventRequest(ctx context.Context, req *pb.CardTrackingCallbacksRequest, event *cardProvisioningPb.ProcessCardTrackingCallbackRequest) {
	var eventTrackingDetails []*cardProvisioningPb.TrackingDetails

	for _, trackingStatus := range req.GetTrackingStatusList() {
		logger.Info(ctx, "received tracking callback", zap.String(logger.ORDER_ID, trackingStatus.GetOrderId()))
		beTrackingDetails := &cardProvisioningPb.TrackingDetails{}
		beTrackingDetails.OrderId = trackingStatus.GetOrderId()
		deliveryState, ok := codeToDeliveryStateMap[trackingStatus.GetCurrentStatus()]
		if !ok {
			// We will not send any packet if unknown status is received
			logger.Info(ctx, fmt.Sprintf("delivery state does not exist for the status %s", trackingStatus.GetCurrentStatus()))
			continue
		}
		beTrackingDetails.DeliveryState = deliveryState
		beTrackingDetails.Scans = processDeliveryScans(ctx, trackingStatus.GetScans(), trackingStatus.GetOrderId())
		pickupDateTimeStamp, err := datetime.ParseStringTimestampProtoInLocation(timeStampFormat,
			trackingStatus.GetPickupDate(), datetime.IST)
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("error in converting pickup date time %v", trackingStatus.GetPickupDate()), zap.Error(err),
				zap.String(logger.ORDER_ID, trackingStatus.GetOrderId()))
		} else {
			beTrackingDetails.PickupDate = pickupDateTimeStamp
		}
		eventTrackingDetails = append(eventTrackingDetails, beTrackingDetails)
	}
	event.TrackingDetailsList = eventTrackingDetails
}

func processDeliveryScans(ctx context.Context, callbackScans []*pb.CallbackScan, orderId string) []*cardProvisioningPb.Scan {
	var beScans []*cardProvisioningPb.Scan
	currentState := cardProvisioningPb.CardTrackingDeliveryState_CARD_TRACKING_DELIVERY_STATE_UNSPECIFIED
	// We will traverse through the scans list in reverse order.
	// As we get the scans details from last to first
	for i := len(callbackScans) - 1; i >= 0; i-- {
		// As per current product requirement If the last scan was in SHIPPED state, all the scan post that will be in IN_TRANSIT state until
		// we find out for delivery or delivered scan.
		if currentState == cardProvisioningPb.CardTrackingDeliveryState_SHIPPED {
			currentState = cardProvisioningPb.CardTrackingDeliveryState_IN_TRANSIT
		}
		scan := callbackScans[i]
		scanTimeStamp, err := datetime.ParseStringTimestampProtoInLocation(timeStampFormat,
			scan.GetTime(), datetime.IST)
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("error in converting scan time %v", scan.GetTime()), zap.Error(err),
				zap.String("shipmentOrderId", orderId))
			continue
		}
		deliveryState, ok := statusToDeliveryStateMap[scan.GetStatus()]
		if !ok {
			logger.Debug(ctx, fmt.Sprintf("delivery state not found for %s status", scan.GetStatus()))
		} else {
			currentState = deliveryState
		}
		beScans = append(beScans, &cardProvisioningPb.Scan{
			Location:          scan.GetLocation(),
			UpdatedAt:         scanTimeStamp,
			StatusDescription: scan.GetStatus(),
			DeliveryState:     currentState,
		})
	}
	return beScans
}
