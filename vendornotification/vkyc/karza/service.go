package karza

import (
	"context"
	"strings"

	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/security"

	"github.com/golang/protobuf/ptypes/empty"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/vendornotification/vkyc/karza"
	vendorsKarza "github.com/epifi/gamma/api/vendors/karza"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/gamma/vendornotification/redactor"
)

type Service struct {
	karzaVkycCallEventPublisher       queue.Publisher
	karzaVkycAgentResponsePublisher   queue.Publisher
	karzaVkycAuditorResponsePublisher queue.Publisher
	conf                              *config.Config
}

func NewService(karzaVkycCallEventPublisher KarzaVkycCallEventPublisher, karzaVkycAgentResponsePublisher KarzaVkycAgentResponsePublisher,
	karzaVkycAuditorResponsePublisher KarzaVkycAuditorResponsePublisher, conf *config.Config) *Service {
	return &Service{
		karzaVkycCallEventPublisher:       karzaVkycCallEventPublisher,
		karzaVkycAgentResponsePublisher:   karzaVkycAgentResponsePublisher,
		karzaVkycAuditorResponsePublisher: karzaVkycAuditorResponsePublisher,
		conf:                              conf,
	}
}

type KarzaVkycCallEventPublisher queue.Publisher
type KarzaVkycAgentResponsePublisher queue.Publisher
type KarzaVkycAuditorResponsePublisher queue.Publisher

var _ karza.VKYCKarzaServer = &Service{}

const (
	AgentCallbackResponse   = "AgentCallbackResponse"
	AuditorCallbackResponse = "AuditorCallbackResponse"
	CallEventResponse       = "CallEventResponse"
)

// TODO (hardik) : Implement when needed and actionable
func (s *Service) AgentCallback(ctx context.Context, agentCallbackResponse *vendorsKarza.AgentCallbackResponse) (*empty.Empty, error) {
	redactor.LogCallbackRequestData(
		ctx, agentCallbackResponse, AgentCallbackResponse, agentCallbackResponse.GetTransactionId(),
		vendorsRedactor.Config,
	)

	// checking if request is coming for whitelisted IPs
	if err := security.CheckWhiteList(
		ctx, s.conf.KarzaVkycWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor,
		s.conf.VpcCidrIPPrefix,
	); err != nil {
		return nil, err
	}

	agentCallbackEvent := &karza.AgentCallbackEvent{
		ConsumerRequestHeader: nil,
		CallSuccess:           agentCallbackResponse.GetCallSuccess(),
		Error:                 agentCallbackResponse.GetError(),
		FailureReason:         agentCallbackResponse.GetFailureReason(),
		ApplicationId:         agentCallbackResponse.GetApplicationId(),
		CustomerId:            agentCallbackResponse.GetCustomerId(),
		TransactionId:         agentCallbackResponse.GetTransactionId(),
		AgentEmployeeId:       agentCallbackResponse.GetAgentEmployeeId(),
		CallStartTime:         agentCallbackResponse.GetCallStartTime(),
		CallEndTime:           agentCallbackResponse.GetCallEndTime(),
		CallDuration:          agentCallbackResponse.GetCallDuration(),
		ApprovedByAgent:       agentCallbackResponse.GetApprovedByAgent(),
		EventTimestamp:        timestamppb.Now(),
	}
	sqsMsgId, err := s.karzaVkycAgentResponsePublisher.Publish(ctx, agentCallbackEvent)
	if err != nil {
		logger.Error(ctx, "error publishing vkyc karza agent callback event to queue", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "internal server error")
	}
	logger.Info(ctx, "vkyc karza agent callback event published to queue successfully", zap.String(logger.QUEUE_MESSAGE_ID, sqsMsgId))
	return &empty.Empty{}, nil
}

func (s *Service) AuditorCallback(ctx context.Context, auditorCallbackResponse *vendorsKarza.AuditorCallbackResponse) (*empty.Empty, error) {
	redactor.LogCallbackRequestData(
		ctx, auditorCallbackResponse, AuditorCallbackResponse, auditorCallbackResponse.GetTransactionId(),
		vendorsRedactor.Config,
	)

	// checking if request is coming for whitelisted IPs
	if err := security.CheckWhiteList(
		ctx, s.conf.KarzaVkycWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor,
		s.conf.VpcCidrIPPrefix,
	); err != nil {
		return nil, err
	}

	auditorCallbackEvent := &karza.AuditorCallbackEvent{
		ApplicationId:     auditorCallbackResponse.GetApplicationId(),
		TransactionId:     auditorCallbackResponse.GetTransactionId(),
		AuditorEmployeeId: auditorCallbackResponse.GetAuditorEmployeeId(),
		ApprovedByAuditor: auditorCallbackResponse.GetApprovedByAuditor(),
		EventTimestamp:    timestamppb.Now(),
	}
	sqsMsgId, err := s.karzaVkycAuditorResponsePublisher.Publish(ctx, auditorCallbackEvent)
	if err != nil {
		logger.Error(ctx, "error publishing vkyc karza auditor callback event to queue", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "internal server error")
	}
	logger.Info(ctx, "vkyc karza auditor callback event published to queue successfully", zap.String(logger.QUEUE_MESSAGE_ID, sqsMsgId))
	return &empty.Empty{}, nil
}

func (s *Service) CallEvent(ctx context.Context, callEventResponse *vendorsKarza.CallEventResponse) (*empty.Empty, error) {
	redactor.LogCallbackRequestData(
		ctx, callEventResponse, CallEventResponse, callEventResponse.GetTransactionId(), vendorsRedactor.Config,
	)

	// checking if request is coming for whitelisted IPs
	if err := security.CheckWhiteList(
		ctx, s.conf.KarzaVkycWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor,
		s.conf.VpcCidrIPPrefix,
	); err != nil {
		return nil, err
	}

	// ignore older event
	if !strings.EqualFold(callEventResponse.GetEventVersion(), "v2") {
		return &empty.Empty{}, nil
	}
	// in user_login, user_logout events we get userId while at other places its agentId
	// they both represent agentId
	agentId := strings.TrimSpace(callEventResponse.GetData().GetAgentId())
	if strings.EqualFold(agentId, "") {
		agentId = strings.TrimSpace(callEventResponse.GetData().GetUserId())
	}
	// in user_login, user_logout events we get userName while at other places its agentUserName
	// they both represent agentUserName
	userName := strings.TrimSpace(callEventResponse.GetData().GetUsername())
	if strings.EqualFold(userName, "") {
		userName = strings.TrimSpace(callEventResponse.GetData().GetAgentUserName())
	}
	timeStamp := callEventResponse.GetNewTimestamp()
	failureReason := getEventV2Reason(callEventResponse)
	event := callEventResponse.GetEvent()

	callEvent := &karza.CallEvent{
		TransactionId: callEventResponse.GetTransactionId(),
		Data: &karza.CallEvent_Data{
			Timestamp:            timeStamp,
			Username:             userName,
			StartDate:            callEventResponse.GetData().GetStartDate(),
			EndDate:              callEventResponse.GetData().GetEndDate(),
			AgentId:              agentId,
			SessionId:            callEventResponse.GetData().GetSessionId(),
			UserAgentString:      callEventResponse.GetData().GetUserAgentString(),
			AgentEmployeeId:      callEventResponse.GetData().GetAgentEmployeeId(),
			ExpectedWaitTime:     callEventResponse.GetData().GetExpectedWaitTime(),
			ApplicationId:        callEventResponse.GetData().GetApplicationId(),
			CustomerArrivalTime:  callEventResponse.GetData().GetCustomerArrivalTime(),
			CustomerId:           callEventResponse.GetData().GetCustomerId(),
			LastStageCompleted:   callEventResponse.GetData().GetLastStageCompleted(),
			IsCustomerBlocked:    callEventResponse.GetData().GetBlockedDetails().GetIsCustomerBlocked(),
			BlockedReason:        callEventResponse.GetData().GetBlockedDetails().GetReason(),
			CallConnectionTime:   callEventResponse.GetData().GetCallConnectionTime(),
			CallEndTime:          callEventResponse.GetData().GetCallEndTime(),
			ApprovedByAgent:      callEventResponse.GetData().GetApprovedByAgent(),
			AgentRemark:          callEventResponse.GetData().GetAgentRemark(),
			AuditorFailureReason: callEventResponse.GetData().GetAuditorFailureReason(),
			AuditorStatus:        callEventResponse.GetData().GetAuditorStatus(),
			AuditorRemark:        callEventResponse.GetData().GetAuditorRemark(),
		},
		Event:          event,
		RequestId:      callEventResponse.GetRequestId(),
		EventTimestamp: timestamppb.Now(),
		Status:         callEventResponse.GetStatus(),
		FailureReason:  failureReason,
		EventVersion:   callEventResponse.GetEventVersion(),
		EventType:      callEventResponse.GetEventType(),
	}
	sqsMsgId, err := s.karzaVkycCallEventPublisher.Publish(ctx, callEvent)
	if err != nil {
		logger.Error(ctx, "error publishing vkyc karza call event to queue", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "internal server error")
	}
	logger.Info(ctx, "vkyc karza call event published to queue successfully", zap.String(logger.QUEUE_MESSAGE_ID, sqsMsgId))
	return &empty.Empty{}, nil
}

func getEventV2Reason(data *vendorsKarza.CallEventResponse) string {
	if !data.GetTerminalState() {
		return ""
	}
	if data.GetData().GetReason() != "" {
		return data.GetData().GetReason()
	}
	return data.GetFailureReason()
}
