package federal

import (
	"context"
	"fmt"
	"html"
	"net/http"
	"strconv"
	"strings"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	consumerPb "github.com/epifi/gamma/api/cx/consumer"
	"github.com/epifi/gamma/api/vendornotification/cx/federal"
	redactorConf "github.com/epifi/gamma/api/vendors/redactor"
	genConf "github.com/epifi/gamma/vendornotification/config/genconf"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"
	types "github.com/epifi/gamma/vendornotification/types"
)

const (
	FintechSRNO       = "FintechSRNO"
	StatusCd          = "StatusCd"
	IOComments        = "IOComments"
	IODisposalRemarks = "IODisposalRemarks"
	IODisposalStatus  = "IODisposalStatus"
	maxVarCharLength  = 1000
)

type FederalEventsHandlingService struct {
	federalEscalationUpdateEventPublisher types.FederalEscalationUpdateEventPublisher
	genConf                               *genConf.Config
}

func NewFederalEventsHandlingService(genConf *genConf.Config,
	federalEscalationUpdateEventPublisher types.FederalEscalationUpdateEventPublisher) *FederalEventsHandlingService {
	return &FederalEventsHandlingService{
		genConf:                               genConf,
		federalEscalationUpdateEventPublisher: federalEscalationUpdateEventPublisher,
	}
}

var _ federal.FederalEscalationHandlingServer = &FederalEventsHandlingService{}

const (
	ProcessFederalEscalationEventRequest = "ProcessFederalEscalationEventRequest"
)

// TODO(Amitkumar): configure metrics
func (e *FederalEventsHandlingService) ProcessFederalEscalationEvent(ctx context.Context, req *federal.ProcessFederalEscalationEventRequest) (*federal.ProcessFederalEscalationEventResponse, error) {
	redactor.LogCallbackRequestData(ctx, req, ProcessFederalEscalationEventRequest, "", redactorConf.Config)

	if err := security.CheckWhiteList(ctx, e.genConf.FederalWhiteList(), e.genConf.NumberOfHopsThatAddXForwardedFor(), e.genConf.VpcCidrIPPrefix()); err != nil {
		logger.Error(ctx, "error while checking IP whitelisting", zap.Error(err))
		return nil, err
	}

	if isValid := isRequestValid(req); isValid != nil {
		return &federal.ProcessFederalEscalationEventResponse{
			Message: isValid.Error(),
			Status:  http.StatusBadRequest,
		}, nil
	}

	updateEvent := &consumerPb.ProcessFederalEscalationEventRequest{
		EscalationUpdatePayload: &consumerPb.EscalationUpdatePayload{
			UpdateTicketRequestType: consumerPb.EscalationRequestType_ESCALATION_REQUEST_TYPE_UPDATE,
			IODisposalStatus:        req.GetIODisposalStatus(),
			IOComments:              req.GetIOComments(),
			IODisposalRemarks:       req.GetIODisposalRemarks(),
			StatusCd:                req.GetStatusCd(),
			FintechSRNO:             req.GetFintechSRNO(),
		},
	}

	if _, publishErr := e.federalEscalationUpdateEventPublisher.Publish(ctx, updateEvent); publishErr != nil {
		logger.Error(ctx, "error publishing update event", zap.Error(publishErr), zap.String("Fintech Sr No", updateEvent.GetEscalationUpdatePayload().GetFintechSRNO()))
		status := http.StatusInternalServerError
		return &federal.ProcessFederalEscalationEventResponse{
			Message: http.StatusText(status),
			Status:  int64(status),
		}, nil
	}

	return &federal.ProcessFederalEscalationEventResponse{
		Message: http.StatusText(http.StatusOK),
		Status:  http.StatusOK,
	}, nil
}

// isRequestValid validates the request parameters and sanitizes free text fields
func isRequestValid(req *federal.ProcessFederalEscalationEventRequest) error {
	var missingFields []string
	var validationErrors []string

	if req.GetFintechSRNO() == "" {
		missingFields = append(missingFields, "FintechSRNO")
	} else {
		if _, err := strconv.ParseInt(req.GetFintechSRNO(), 10, 64); err != nil {
			validationErrors = append(validationErrors, "FintechSRNO must be a valid numeric string")
		}
	}

	if req.GetStatusCd() == "" {
		missingFields = append(missingFields, "StatusCd")
	}

	if len(req.GetIOComments()) > maxVarCharLength {
		validationErrors = append(validationErrors, fmt.Sprintf("IOComments must be less than %d characters", maxVarCharLength))
	}

	if len(req.GetIODisposalRemarks()) > maxVarCharLength {
		validationErrors = append(validationErrors, fmt.Sprintf("IODisposalRemarks must be less than %d characters", maxVarCharLength))
	}

	if len(req.GetStatusCd()) > maxVarCharLength {
		validationErrors = append(validationErrors, fmt.Sprintf("StatusCd must be less than %d characters", maxVarCharLength))
	}

	if len(req.GetIODisposalStatus()) > maxVarCharLength {
		validationErrors = append(validationErrors, fmt.Sprintf("IODisposalStatus must be less than %d characters", maxVarCharLength))
	}

	if len(missingFields) > 0 {
		return fmt.Errorf("mandatory parameters missing: %s", strings.Join(missingFields, ", "))
	}

	if len(validationErrors) > 0 {
		return fmt.Errorf("validation errors: %s", strings.Join(validationErrors, ", "))
	}

	// HTML encode free text fields
	fields := map[string]*string{
		FintechSRNO:       &req.FintechSRNO,
		StatusCd:          &req.StatusCd,
		IOComments:        &req.IOComments,
		IODisposalRemarks: &req.IODisposalRemarks,
		IODisposalStatus:  &req.IODisposalStatus,
	}

	for _, ptr := range fields {
		if *ptr != "" {
			*ptr = *stringPtr(html.EscapeString(*ptr))
		}
	}

	return nil
}

func stringPtr(s string) *string {
	return &s
}
