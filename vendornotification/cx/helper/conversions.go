package helper

import (
	chatPb "github.com/epifi/gamma/api/cx/chat"
	freshchatPb "github.com/epifi/gamma/api/vendornotification/cx/freshchat"
	vendorsPb "github.com/epifi/gamma/api/vendors/freshchat"
)

var (
	msgTypeStrToEnumMap = map[string]chatPb.FreshchatMessageType{
		"normal":  chatPb.FreshchatMessageType_FRESHCHAT_MESSAGE_TYPE_NORMAL,
		"private": chatPb.FreshchatMessageType_FRESHCHAT_MESSAGE_TYPE_PRIVATE,
	}
	ActorTypeStrToEnumMap = map[string]chatPb.FreshchatActorType{
		"user":   chatPb.FreshchatActorType_FRESHCHAT_ACTOR_TYPE_USER,
		"agent":  chatPb.FreshchatActorType_FRESHCHAT_ACTOR_TYPE_AGENT,
		"system": chatPb.FreshchatActorType_FRESHCHAT_ACTOR_TYPE_SYSTEM,
	}
	ActionStrToEnumMap = map[string]chatPb.FreshchatCallbackAction{
		"message_create":          chatPb.FreshchatCallbackAction_FRESHCHAT_CALLBACK_ACTION_MESSAGE_CREATE,
		"conversation_reopen":     chatPb.FreshchatCallbackAction_FRESHCHAT_CALLBACK_ACTION_CONVERSATION_REOPEN,
		"conversation_resolution": chatPb.FreshchatCallbackAction_FRESHCHAT_CALLBACK_ACTION_CONVERSATION_RESOLUTION,
		"conversation_assignment": chatPb.FreshchatCallbackAction_FRESHCHAT_CALLBACK_ACTION_CONVERSATION_ASSIGNMENT,
		// agent_availability action
	}
	ConversationStatusStrToEnumMap = map[string]chatPb.FreshchatConversationStatus{
		"new":      chatPb.FreshchatConversationStatus_FRESHCHAT_CONVERSATION_STATUS_NEW,
		"assigned": chatPb.FreshchatConversationStatus_FRESHCHAT_CONVERSATION_STATUS_ASSIGNED,
		"resolved": chatPb.FreshchatConversationStatus_FRESHCHAT_CONVERSATION_STATUS_RESOLVED,
		"reopened": chatPb.FreshchatConversationStatus_FRESHCHAT_CONVERSATION_STATUS_REOPENED,
	}
)

func VendorsToCxFcMessage(vendorMsg *vendorsPb.Message) *chatPb.Message {
	cxMsg := &chatPb.Message{
		AppId:          vendorMsg.GetAppId(),
		ActorId:        vendorMsg.GetActorId(),
		Id:             vendorMsg.GetId(),
		ChannelId:      vendorMsg.GetChannelId(),
		ConversationId: vendorMsg.GetConversationId(),
		MessageType:    msgTypeStrToEnumMap[vendorMsg.GetMessageType()],
		ActorType:      ActorTypeStrToEnumMap[vendorMsg.GetActorType()],
		CreatedTime:    vendorMsg.GetCreatedTime(),
	}
	for _, msgPart := range vendorMsg.GetMessageParts() {
		cxMsg.MessageParts = append(cxMsg.MessageParts, VendorsToCxFcMessagePart(msgPart))
	}
	for _, replyPart := range vendorMsg.GetReplyParts() {
		cxMsg.ReplyParts = append(cxMsg.ReplyParts, VendorsToCxFcMessagePart(replyPart))
	}
	return cxMsg
}

func VendorsToCxFcMessagePart(vendorMsgPart *vendorsPb.MessagePart) *chatPb.MessagePart {
	cxMsgPart := &chatPb.MessagePart{}
	switch {
	case vendorMsgPart.GetImage() != nil:
		cxMsgPart.Image = &chatPb.ImageMessagePart{
			Url: vendorMsgPart.GetImage().GetUrl(),
		}
	case vendorMsgPart.GetText() != nil:
		cxMsgPart.Text = &chatPb.TextMessagePart{
			Content: vendorMsgPart.GetText().GetContent(),
		}
	case vendorMsgPart.GetUrlButton() != nil:
		cxMsgPart.UrlButton = &chatPb.UrlButtonMessagePart{
			Url:    vendorMsgPart.GetUrlButton().GetUrl(),
			Label:  vendorMsgPart.GetUrlButton().GetLabel(),
			Target: vendorMsgPart.GetUrlButton().GetTarget(),
		}
	case vendorMsgPart.GetCollection() != nil:
		cxMsgPart.Collection = &chatPb.CollectionMessagePart{}
		for _, subPart := range vendorMsgPart.GetCollection().GetSubParts() {
			cxMsgPart.Collection.SubParts = append(cxMsgPart.Collection.SubParts, VendorsToCxFcMessagePart(subPart))
		}
	case vendorMsgPart.GetQuickReplyButton() != nil:
		cxMsgPart.QuickReplyButton = &chatPb.QuickReplyButtonMessagePart{
			CustomReplyText: vendorMsgPart.GetQuickReplyButton().GetCustomReplyText(),
			Label:           vendorMsgPart.GetQuickReplyButton().GetLabel(),
			Payload:         vendorMsgPart.GetQuickReplyButton().GetPayload(),
		}
	case vendorMsgPart.GetTemplateContent() != nil:
		cxMsgPart.TemplateContent = &chatPb.TemplateContentMessagePart{
			Type: vendorMsgPart.GetTemplateContent().GetType(),
		}
		for _, section := range vendorMsgPart.GetTemplateContent().GetSections() {
			cxSec := &chatPb.Section{Name: section.GetName()}
			for _, part := range section.GetParts() {
				cxSec.Parts = append(cxSec.Parts, VendorsToCxFcMessagePart(part))
			}
			cxMsgPart.TemplateContent.Sections = append(cxMsgPart.TemplateContent.Sections, cxSec)
		}
	case vendorMsgPart.GetCallback() != nil:
		cxMsgPart.Callback = &chatPb.CallbackMessagePart{
			Payload: vendorMsgPart.GetCallback().GetPayload(),
			Label:   vendorMsgPart.GetCallback().GetLabel(),
		}
	default:
		return nil
	}
	return cxMsgPart
}

func VendorsToCxFcConversation(vendorConversation *vendorsPb.Conversation) *chatPb.Conversation {
	cxFcConversation := &chatPb.Conversation{
		ConversationId: vendorConversation.GetConversationId(),
		AppId:          vendorConversation.GetAppId(),
		ChannelId:      vendorConversation.GetChannelId(),
		Status:         ConversationStatusStrToEnumMap[vendorConversation.GetStatus()],
	}

	for _, msg := range vendorConversation.GetMessages() {
		cxFcConversation.Messages = append(cxFcConversation.Messages, VendorsToCxFcMessage(msg))
	}
	for _, user := range vendorConversation.GetUsers() {
		cxFcConversation.Users = append(cxFcConversation.Users, VendorsToCxFcUser(user))
	}
	for _, agent := range vendorConversation.GetAgents() {
		cxFcConversation.Agents = append(cxFcConversation.Agents, VendorsToCxFcUser(agent))
	}
	return cxFcConversation
}

func VendorsToCxFcAssignment(vnAssignment *freshchatPb.ConversationAssignmentCallbackData) *chatPb.ConversationAssignmentCallbackData {
	cxFcAssignment := &chatPb.ConversationAssignmentCallbackData{
		Assignor:      ActorTypeStrToEnumMap[vnAssignment.GetAssignor()],
		AssignorId:    vnAssignment.GetAssignorId(),
		ToAgentId:     vnAssignment.GetToAgentId(),
		ToGroupId:     vnAssignment.GetToGroupId(),
		FromAgentId:   vnAssignment.GetFromAgentId(),
		FromGroupId:   vnAssignment.GetFromGroupId(),
		Conversation:  VendorsToCxFcConversation(vnAssignment.GetConversation()),
		InteractionId: vnAssignment.GetInteractionId(),
	}
	return cxFcAssignment
}

func VendorsToCxFcResolve(vnResolve *freshchatPb.ConversationResolutionCallbackData) *chatPb.ConversationResolutionCallbackData {
	cxFcResolve := &chatPb.ConversationResolutionCallbackData{
		Resolver:     ActorTypeStrToEnumMap[vnResolve.GetResolver()],
		ResolverId:   vnResolve.GetResolverId(),
		Conversation: VendorsToCxFcConversation(vnResolve.GetConversation()),
	}
	return cxFcResolve
}

func VendorsToCxFcReopen(vnReopen *freshchatPb.ConversationReopenCallbackData) *chatPb.ConversationReopenCallbackData {
	cxFcReopen := &chatPb.ConversationReopenCallbackData{
		Reopener:     ActorTypeStrToEnumMap[vnReopen.GetReopener()],
		ReopenerId:   vnReopen.GetReopenerId(),
		Conversation: VendorsToCxFcConversation(vnReopen.GetConversation()),
	}
	return cxFcReopen
}

func VendorsToCxFcUser(vendorUser *vendorsPb.User) *chatPb.User {
	cxFcUser := &chatPb.User{
		Email:       vendorUser.GetEmail(),
		CreatedTime: vendorUser.GetCreatedTime(),
		UpdatedTime: vendorUser.GetUpdatedTime(),
		Id:          vendorUser.GetId(),
		Phone:       vendorUser.GetPhone(),
		FirstName:   vendorUser.GetFirstName(),
		LastName:    vendorUser.GetLastName(),
		ReferenceId: vendorUser.GetReferenceId(),
		RestoreId:   vendorUser.GetRestoreId(),
	}
	if vendorUser.GetAvatar() != nil {
		cxFcUser.Avatar = &chatPb.Image{
			Url: vendorUser.GetAvatar().GetUrl(),
		}
	}
	for _, prop := range vendorUser.GetProperties() {
		cxFcUser.Properties = append(cxFcUser.Properties, &chatPb.Property{
			Name:  prop.GetName(),
			Value: prop.GetValue(),
		})
	}
	return cxFcUser
}
