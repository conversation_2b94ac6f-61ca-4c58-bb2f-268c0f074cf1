package helper

import (
	"context"
	"strings"

	"github.com/epifi/be-common/pkg/ratelimiter"
	"github.com/epifi/gamma/vendornotification/config"

	"github.com/pkg/errors"
)

const apiNameDefault = "default"

// IRateLimitHelper is an abstraction for defining rate limits on chatbot APIs
type IRateLimitHelper interface {
	// IsRateLimitExceeded check whether rate limit has exceeded for a given actorId for the given API
	// The resource name for rate limit check will be "chatbot_user_api_apiName". If this resource doesn't exist in config,
	// we use the default one chatbot_user_api_default. We can still differentiate the APIs using default resource using the key.
	// The key used for rate limit check will be of the form "actorId_apiName".
	// Here actorId and apiName are mandatory params
	IsRateLimitExceeded(ctx context.Context, actorId string, apiName string) (bool, error)
}

type RatelimitHelper struct {
	rlClient ratelimiter.RateLimiter
	conf     *config.Config
}

func NewRatelimitHelper(rlClient ratelimiter.RateLimiter, conf *config.Config) *RatelimitHelper {
	return &RatelimitHelper{
		rlClient: rlClient,
		conf:     conf,
	}
}

func (r *RatelimitHelper) IsRateLimitExceeded(ctx context.Context, actorId string, apiName string) (bool, error) {
	if actorId == "" || apiName == "" {
		return false, errors.New("actorId and apiName are mandatory to apply vn chat api rate limit")
	}
	resource := getUserApiResourceName(apiName)
	if _, isResourceConfigFound := r.conf.RateLimitConfig.ResourceMap[resource]; !isResourceConfigFound {
		resource = getUserApiResourceName(apiNameDefault)
	}
	userApiKey := getUserApiRateLimitKey(actorId, apiName)
	userApiAllowed, userApiError := r.rlClient.Acquire(ctx, resource, userApiKey)
	if userApiError != nil {
		return false, errors.Wrap(userApiError, "failed to check vn chat api rate limit")
	}
	if userApiAllowed {
		return false, nil
	}
	return true, nil
}

func getUserApiResourceName(apiName string) string {
	return "chatbot_user_api_" + strings.ToLower(apiName)
}

func getUserApiRateLimitKey(actorId string, apiName string) string {
	return actorId + "_" + apiName
}
