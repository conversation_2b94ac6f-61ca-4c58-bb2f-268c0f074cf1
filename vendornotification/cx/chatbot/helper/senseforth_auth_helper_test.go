package helper

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/epifi/be-common/api/rpc"
	authPb "github.com/epifi/gamma/api/auth"
	cxPkg "github.com/epifi/gamma/pkg/cx"
	"google.golang.org/grpc/metadata"

	authMock "github.com/epifi/gamma/api/auth/mocks"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/golang/mock/gomock"
)

type SenseforthLiveChatFallbackServiceTestSuite struct {
	conf *config.Config
}

const shortToken1 = "short-token-1"

var (
	slcsTs    SenseforthLiveChatFallbackServiceTestSuite
	metaData1 = metadata.New(map[string]string{
		cxPkg.ChatBotShortTokenHttpHeaderKey: shortToken1,
		cxPkg.ChatBotApiKeyHeaderKey:         cxPkg.ApiKeyPrefix + " XYZ",
	})

	testError = errors.New("test error")
)

func TestService_ValidateApiKey(t *testing.T) {
	ctr := gomock.NewController(t)
	mockAuthClient := authMock.NewMockAuthClient(ctr)
	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks []interface{}
		ctx   context.Context
	}
	tests := []struct {
		name string
		args args
		want error
	}{
		{
			name: "no metadata found",
			args: args{
				ctx: context.Background(),
			},
			want: testError,
		},
		{
			name: "api key not found",
			args: args{
				ctx: metadata.NewIncomingContext(context.Background(), metadata.New(map[string]string{})),
			},
			want: ErrApiKeyNotFound,
		},
		{
			name: "api key prefix format invalid",
			args: args{
				ctx: metadata.NewIncomingContext(context.Background(), metadata.New(map[string]string{cxPkg.ChatBotApiKeyHeaderKey: ""})),
			},
			want: ErrApiKeyInvalid,
		},
		{
			name: "api key value invalid",
			args: args{
				ctx: metadata.NewIncomingContext(context.Background(), metadata.New(map[string]string{cxPkg.ChatBotApiKeyHeaderKey: cxPkg.ApiKeyPrefix + ""})),
			},
			want: ErrApiKeyInvalid,
		},
		{
			name: "success",
			args: args{
				ctx: metadata.NewIncomingContext(context.Background(), metaData1),
			},
			want: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewSenseforthAuthHelper(mockAuthClient, slcsTs.conf)
			got := s.ValidateApiKey(tt.args.ctx)
			if !errors.Is(tt.want, testError) && !errors.Is(got, tt.want) {
				t.Errorf("ValidateApiKey() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_ValidateShortTokenAndGetActorId(t *testing.T) {
	ctr := gomock.NewController(t)
	mockAuthClient := authMock.NewMockAuthClient(ctr)
	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks []interface{}
		ctx   context.Context
	}
	tests := []struct {
		name    string
		args    args
		wantStr string
		wantErr error
	}{
		{
			name: "no metadata found",
			args: args{
				ctx: context.Background(),
			},
			wantStr: "",
			wantErr: testError,
		},
		{
			name: "short token not found",
			args: args{
				ctx: metadata.NewIncomingContext(context.Background(), metadata.New(map[string]string{})),
			},
			wantErr: ErrShortTokenNotFound,
		},
		{
			name: "short token invalid",
			args: args{
				mocks: []interface{}{
					mockAuthClient.EXPECT().ValidateToken(metadata.NewIncomingContext(context.Background(), metaData1), &authPb.ValidateTokenRequest{
						Token:     shortToken1,
						TokenType: authPb.TokenType_CHATBOT_ACCESS_TOKEN,
					}).Return(&authPb.ValidateTokenResponse{
						Status: rpc.NewStatus(uint32(authPb.ValidateTokenResponse_TOKEN_INVALID), "invalid", ""),
					}, nil),
				},
				ctx: metadata.NewIncomingContext(context.Background(), metaData1),
			},
			wantErr: ErrShortTokenInvalid,
		},
		{
			name: "short token expired",
			args: args{
				mocks: []interface{}{
					mockAuthClient.EXPECT().ValidateToken(metadata.NewIncomingContext(context.Background(), metaData1), &authPb.ValidateTokenRequest{
						Token:     shortToken1,
						TokenType: authPb.TokenType_CHATBOT_ACCESS_TOKEN,
					}).Return(&authPb.ValidateTokenResponse{
						Status: rpc.NewStatus(uint32(authPb.ValidateTokenResponse_TOKEN_EXPIRY), "invalid", ""),
					}, nil),
				},
				ctx: metadata.NewIncomingContext(context.Background(), metaData1),
			},
			wantErr: ErrShortTokenExpired,
		},
		{
			name: "success",
			args: args{
				mocks: []interface{}{
					mockAuthClient.EXPECT().ValidateToken(metadata.NewIncomingContext(context.Background(), metaData1), &authPb.ValidateTokenRequest{
						Token:     shortToken1,
						TokenType: authPb.TokenType_CHATBOT_ACCESS_TOKEN,
					}).Return(&authPb.ValidateTokenResponse{
						Status: rpc.StatusOk(),
					}, nil),
				},
				ctx: metadata.NewIncomingContext(context.Background(), metaData1),
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewSenseforthAuthHelper(mockAuthClient, slcsTs.conf)
			gotStr, gotErr := s.ValidateShortTokenAndGetActorId(tt.args.ctx)
			if errors.Is(tt.wantErr, testError) {
				return
			}
			if !errors.Is(gotErr, tt.wantErr) {
				t.Errorf("ValidateShortTokenAndGetActorId() error = %v, wantErr %v", gotErr, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotStr, tt.wantStr) {
				t.Errorf("ValidateShortTokenAndGetActorId() gotStr = %v, wantStr %v", gotStr, tt.wantStr)
			}
		})
	}
}
