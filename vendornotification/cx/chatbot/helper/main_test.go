package helper

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/vendornotification/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	conf, teardown := test.InitTestServer()
	slcsTs = SenseforthLiveChatFallbackServiceTestSuite{
		conf: conf,
	}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
