package helper

import (
	"errors"

	"github.com/epifi/be-common/api/rpc"
	chatBotPb "github.com/epifi/gamma/api/vendornotification/cx/chatbot"
)

func GetStatusCodeFromError(err error) chatBotPb.Status {
	switch {
	case errors.Is(err, ErrApiKeyNotFound) || errors.Is(err, ErrApiKeyInvalid):
		return chatBotPb.Status_STATUS_API_KEY_AUTHORIZATION_FAILED
	case errors.Is(err, ErrShortTokenNotFound):
		return chatBotPb.Status_STATUS_SHORT_TOKEN_NOT_FOUND_IN_HEADER
	case errors.Is(err, ErrShortTokenInvalid) || errors.Is(err, ErrShortTokenExpired):
		return chatBotPb.Status_STATUS_TOKEN_EXPIRED_OR_INVALID
	case errors.Is(err, InvalidEntityErr) || errors.Is(err, InvalidFetchEntityParamErr):
		return chatBotPb.Status_STATUS_INVALID_ARGUMENT
	default:
	}
	return chatBotPb.Status_STATUS_INTERNAL_ERROR
}

func GetStatusCodeFromRpcStatus(rpcStatus *rpc.Status) chatBotPb.Status {
	switch {
	case rpcStatus.IsRecordNotFound():
		return chatBotPb.Status_STATUS_RECORD_NOT_FOUND
	case rpcStatus.IsInvalidArgument():
		return chatBotPb.Status_STATUS_INVALID_ARGUMENT
	default:
		return chatBotPb.Status_STATUS_INTERNAL_ERROR
	}
}
