package helper

import (
	"context"

	"github.com/pkg/errors"
)

var (
	ErrApiKeyNotFound            = errors.New("Api Key not found in header")
	ErrApiKeyInvalid             = errors.New("Api key is invalid")
	ErrShortTokenNotFound        = errors.New("Short token not found")
	ErrShortTokenInvalid         = errors.New("Short token is invalid")
	ErrShortTokenExpired         = errors.New("Short token expired")
	InvalidEntityErr             = errors.New("invalid workflow entity in request")
	InvalidFetchEntityParamErr   = errors.New("invalid workflow entity params in request")
	InvalidActionErr             = errors.New("invalid workflow action in request")
	InvalidExecuteActionParamErr = errors.New("invalid workflow action params in request")
)

// IAuthHelper is an abstraction to validate the authorization of a request.
// vendor specific implementations for this interface can be defined
type IAuthHelper interface {
	ValidateApiKey(ctx context.Context) error
	ValidateShortTokenAndGetActorId(ctx context.Context) (string, error)
}
