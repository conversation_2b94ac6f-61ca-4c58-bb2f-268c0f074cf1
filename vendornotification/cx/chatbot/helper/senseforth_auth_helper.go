package helper

import (
	"context"
	"strings"

	authPb "github.com/epifi/gamma/api/auth"
	cxPkg "github.com/epifi/gamma/pkg/cx"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/vendornotification/config"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
)

type SenseforthAuthHelper struct {
	authClient authPb.AuthClient
	conf       *config.Config
}

func NewSenseforthAuthHelper(authClient authPb.AuthClient, conf *config.Config) *SenseforthAuthHelper {
	return &SenseforthAuthHelper{
		authClient: authClient,
		conf:       conf,
	}
}

func (s *SenseforthAuthHelper) ValidateApiKey(ctx context.Context) error {
	md, isMetaDataAppendedInContext := metadata.FromIncomingContext(ctx)
	if !isMetaDataAppendedInContext {
		return errors.New("no metadata found in context")
	}
	authorization, found := md[strings.ToLower(cxPkg.ChatBotApiKeyHeaderKey)]
	if !found {
		return ErrApiKeyNotFound
	}
	authParts := strings.Split(authorization[0], " ")
	if len(authParts) != 2 {
		return errors.Wrap(ErrApiKeyInvalid, "api key format invalid")
	}
	if authParts[0] != cxPkg.ApiKeyPrefix {
		return errors.Wrap(ErrApiKeyInvalid, "api key prefix doesn't match")
	}
	apiKey, ok := s.conf.Secrets.Ids[cxPkg.SenseforthAuthApiKey]
	if !ok {
		return errors.New("could not read senseforth API key from config")
	}
	if authParts[1] != apiKey {
		return errors.Wrap(ErrApiKeyInvalid, "api key comparison failed")
	}
	return nil
}

func (s *SenseforthAuthHelper) ValidateShortTokenAndGetActorId(ctx context.Context) (string, error) {
	md, isMetaDataAppendedInContext := metadata.FromIncomingContext(ctx)
	if !isMetaDataAppendedInContext {
		return "", errors.New("no metadata found in context")
	}
	shortToken, isShortTokenKeyFound := md[strings.ToLower(cxPkg.ChatBotShortTokenHttpHeaderKey)]
	if !isShortTokenKeyFound {
		return "", ErrShortTokenNotFound
	}
	// validate short token extracted from header
	authResp, authErr := s.authClient.ValidateToken(ctx, &authPb.ValidateTokenRequest{
		Token:     shortToken[0],
		TokenType: authPb.TokenType_CHATBOT_ACCESS_TOKEN,
	})
	// check for rpc error
	if rpcErr := epifigrpc.RPCError(authResp, authErr); rpcErr != nil {
		switch authResp.GetStatus().GetCode() {
		case uint32(authPb.ValidateTokenResponse_NOT_FOUND):
			return "", ErrShortTokenInvalid
		case uint32(authPb.ValidateTokenResponse_TOKEN_INVALID):
			return "", ErrShortTokenInvalid
		case uint32(authPb.ValidateTokenResponse_TOKEN_EXPIRY):
			return "", ErrShortTokenExpired
		default:
		}
		logger.Error(ctx, "error while verifying short token", zap.Error(rpcErr))
		return "", errors.New("error while invoking auth service for short token validation")
	}
	logger.Info(ctx, "chatbot access token validation successful", zap.String(logger.ACTOR_ID, authResp.GetActorId()))
	return authResp.GetActorId(), nil
}
