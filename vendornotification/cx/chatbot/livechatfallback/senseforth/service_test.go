package senseforth

import (
	"context"
	"flag"
	"os"
	"reflect"
	"testing"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/gamma/vendornotification/metrics"
	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/rpc"
	cxPb "github.com/epifi/gamma/api/cx/chat/bot/livechatfallback"
	mocks2 "github.com/epifi/gamma/api/cx/chat/bot/livechatfallback/mocks"
	chatBotPb "github.com/epifi/gamma/api/vendornotification/cx/chatbot"
	liveChatFallbackPb "github.com/epifi/gamma/api/vendornotification/cx/chatbot/livechatfallback"
	senseforthPb "github.com/epifi/gamma/api/vendornotification/cx/chatbot/livechatfallback/senseforth"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/helper"
	"github.com/epifi/gamma/vendornotification/test"
	mockHelper "github.com/epifi/gamma/vendornotification/test/mocks/cx/chatbot/helper"

	"github.com/golang/mock/gomock"
)

type SenseforthLiveChatFallbackServiceTestSuite struct {
	conf *config.Config
}

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	conf, teardown := test.InitTestServer()
	slcsTs = SenseforthLiveChatFallbackServiceTestSuite{
		conf: conf,
	}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

var (
	slcsTs          SenseforthLiveChatFallbackServiceTestSuite
	ctxWithActorId1 = epificontext.CtxWithActorId(context.Background(), actorId1)
)

const (
	actorId1 = "actorId1"
)

func TestService_InitiateConversation(t *testing.T) {
	ctr := gomock.NewController(t)
	mockAuthHelper := mockHelper.NewMockIAuthHelper(ctr)
	mockCxLiveChatFallbackClient := mocks2.NewMockLiveChatFallbackClient(ctr)
	mockRateLimiHelper := mockHelper.NewMockIRateLimitHelper(ctr)
	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *senseforthPb.InitiateConversationRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *senseforthPb.InitiateConversationResponse
		wantErr bool
	}{
		{
			name: "api key validation failed",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(helper.ErrApiKeyInvalid),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &senseforthPb.InitiateConversationResponse{
				Status: int64(chatBotPb.Status_STATUS_API_KEY_AUTHORIZATION_FAILED),
			},
			wantErr: false,
		},
		{
			name: "short token validation failed: token not found",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return("", helper.ErrShortTokenNotFound),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &senseforthPb.InitiateConversationResponse{
				Status: int64(chatBotPb.Status_STATUS_SHORT_TOKEN_NOT_FOUND_IN_HEADER),
			},
			wantErr: false,
		},
		{
			name: "short token validation failed: token expired",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return("", helper.ErrShortTokenExpired),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &senseforthPb.InitiateConversationResponse{
				Status: int64(chatBotPb.Status_STATUS_TOKEN_EXPIRED_OR_INVALID),
			},
			wantErr: false,
		},
		{
			name: "rate limit check failed",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return(actorId1, nil),
					mockRateLimiHelper.EXPECT().IsRateLimitExceeded(ctxWithActorId1, actorId1, metrics.ChatBotApiNameInitiateConversation).Return(false, errors.New("test err")),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &senseforthPb.InitiateConversationResponse{
				Status: int64(chatBotPb.Status_STATUS_INTERNAL_ERROR),
			},
			wantErr: false,
		}, {
			name: "rate limit exceeded",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return(actorId1, nil),
					mockRateLimiHelper.EXPECT().IsRateLimitExceeded(ctxWithActorId1, actorId1, metrics.ChatBotApiNameInitiateConversation).Return(true, nil),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &senseforthPb.InitiateConversationResponse{
				Status: int64(chatBotPb.Status_STATUS_RATE_LIMIT_EXCEEDED),
			},
			wantErr: false,
		},
		{
			name: "vm internal error",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return(actorId1, nil),
					mockRateLimiHelper.EXPECT().IsRateLimitExceeded(ctxWithActorId1, actorId1, metrics.ChatBotApiNameInitiateConversation).Return(false, nil),
					mockCxLiveChatFallbackClient.EXPECT().InitiateConversation(gomock.Any(), gomock.Any()).Return(&cxPb.InitiateConversationResponse{
						Status: rpc.StatusInternal(),
					}, nil),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &senseforthPb.InitiateConversationResponse{
				Status: int64(chatBotPb.Status_STATUS_INTERNAL_ERROR),
			},
			wantErr: false,
		},
		{
			name: "successful",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return(actorId1, nil),
					mockRateLimiHelper.EXPECT().IsRateLimitExceeded(ctxWithActorId1, actorId1, metrics.ChatBotApiNameInitiateConversation).Return(false, nil),
					mockCxLiveChatFallbackClient.EXPECT().InitiateConversation(gomock.Any(), gomock.Any()).Return(&cxPb.InitiateConversationResponse{
						Status:         rpc.StatusOk(),
						ConversationId: "conv-id-1",
					}, nil),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &senseforthPb.InitiateConversationResponse{
				Status:         int64(chatBotPb.Status_STATUS_OK),
				ConversationId: "conv-id-1",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(slcsTs.conf, mockCxLiveChatFallbackClient, mockAuthHelper, mockRateLimiHelper)
			got, err := s.InitiateConversation(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("InitiateConversation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("InitiateConversation() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_PushUserMessage(t *testing.T) {
	ctr := gomock.NewController(t)
	mockAuthHelper := mockHelper.NewMockIAuthHelper(ctr)
	mockCxLiveChatFallbackClient := mocks2.NewMockLiveChatFallbackClient(ctr)
	mockRateLimiHelper := mockHelper.NewMockIRateLimitHelper(ctr)
	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *senseforthPb.PushUserMessageRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *senseforthPb.PushUserMessageResponse
		wantErr bool
	}{
		{
			name: "api key validation failed",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(helper.ErrApiKeyInvalid),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &senseforthPb.PushUserMessageResponse{
				Status: int64(chatBotPb.Status_STATUS_API_KEY_AUTHORIZATION_FAILED),
			},
			wantErr: false,
		},
		{
			name: "short token validation failed: token not found",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return("", helper.ErrShortTokenNotFound),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &senseforthPb.PushUserMessageResponse{
				Status: int64(chatBotPb.Status_STATUS_SHORT_TOKEN_NOT_FOUND_IN_HEADER),
			},
			wantErr: false,
		},
		{
			name: "short token validation failed: token expired",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return("", helper.ErrShortTokenExpired),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &senseforthPb.PushUserMessageResponse{
				Status: int64(chatBotPb.Status_STATUS_TOKEN_EXPIRED_OR_INVALID),
			},
			wantErr: false,
		},
		{
			name: "rate limit check failed",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return(actorId1, nil),
					mockRateLimiHelper.EXPECT().IsRateLimitExceeded(ctxWithActorId1, actorId1, metrics.ChatBotApiNamePushUserMessage).Return(false, errors.New("test err")),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &senseforthPb.PushUserMessageResponse{
				Status: int64(chatBotPb.Status_STATUS_INTERNAL_ERROR),
			},
			wantErr: false,
		},
		{
			name: "rate limit exceeded",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return(actorId1, nil),
					mockRateLimiHelper.EXPECT().IsRateLimitExceeded(ctxWithActorId1, actorId1, metrics.ChatBotApiNamePushUserMessage).Return(true, nil),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &senseforthPb.PushUserMessageResponse{
				Status: int64(chatBotPb.Status_STATUS_RATE_LIMIT_EXCEEDED),
			},
			wantErr: false,
		},
		{
			name: "vm internal error",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return(actorId1, nil),
					mockRateLimiHelper.EXPECT().IsRateLimitExceeded(ctxWithActorId1, actorId1, metrics.ChatBotApiNamePushUserMessage).Return(false, nil),
					mockCxLiveChatFallbackClient.EXPECT().PushUserMessage(gomock.Any(), gomock.Any()).Return(&cxPb.PushUserMessageResponse{
						Status: rpc.StatusInternal(),
					}, nil),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &senseforthPb.PushUserMessageResponse{
				Status: int64(chatBotPb.Status_STATUS_INTERNAL_ERROR),
			},
			wantErr: false,
		},
		{
			name: "successful",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return(actorId1, nil),
					mockRateLimiHelper.EXPECT().IsRateLimitExceeded(ctxWithActorId1, actorId1, metrics.ChatBotApiNamePushUserMessage).Return(false, nil),
					mockCxLiveChatFallbackClient.EXPECT().PushUserMessage(gomock.Any(), gomock.Any()).Return(&cxPb.PushUserMessageResponse{
						Status:         rpc.StatusOk(),
						ConversationId: "conv-id-1",
					}, nil),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &senseforthPb.PushUserMessageResponse{
				Status:         int64(chatBotPb.Status_STATUS_OK),
				ConversationId: "conv-id-1",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(slcsTs.conf, mockCxLiveChatFallbackClient, mockAuthHelper, mockRateLimiHelper)
			got, err := s.PushUserMessage(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("PushUserMessage() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PushUserMessage() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_NotifyUser(t *testing.T) {
	ctr := gomock.NewController(t)
	mockAuthHelper := mockHelper.NewMockIAuthHelper(ctr)
	mockCxLiveChatFallbackClient := mocks2.NewMockLiveChatFallbackClient(ctr)
	mockRateLimiHelper := mockHelper.NewMockIRateLimitHelper(ctr)
	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *senseforthPb.NotifyUserRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *senseforthPb.NotifyUserResponse
		wantErr bool
	}{
		{
			name: "api key validation failed",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(helper.ErrApiKeyInvalid),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &senseforthPb.NotifyUserResponse{
				Status: int64(chatBotPb.Status_STATUS_API_KEY_AUTHORIZATION_FAILED),
			},
			wantErr: false,
		},
		{
			name: "short token validation failed: token not found",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return("", helper.ErrShortTokenNotFound),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &senseforthPb.NotifyUserResponse{
				Status: int64(chatBotPb.Status_STATUS_SHORT_TOKEN_NOT_FOUND_IN_HEADER),
			},
			wantErr: false,
		},
		{
			name: "short token validation failed: token expired",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return("", helper.ErrShortTokenExpired),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &senseforthPb.NotifyUserResponse{
				Status: int64(chatBotPb.Status_STATUS_TOKEN_EXPIRED_OR_INVALID),
			},
			wantErr: false,
		},
		{
			name: "rate limit check error",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return(actorId1, nil),
					mockRateLimiHelper.EXPECT().IsRateLimitExceeded(ctxWithActorId1, actorId1, metrics.ChatBotApiNameNotifyUser).Return(false, errors.New("test err")),
				},
				ctx: context.Background(),
				req: &senseforthPb.NotifyUserRequest{
					EventType: "agent_reply",
					Data: &liveChatFallbackPb.EventData{
						AgentReply: &liveChatFallbackPb.AgentReplyEventData{
							AgentName: "agent-1",
							Message:   &liveChatFallbackPb.Message{},
						},
					},
				},
			},
			want: &senseforthPb.NotifyUserResponse{
				Status: int64(chatBotPb.Status_STATUS_INTERNAL_ERROR),
			},
			wantErr: false,
		},
		{
			name: "rate limit exceeded",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return(actorId1, nil),
					mockRateLimiHelper.EXPECT().IsRateLimitExceeded(ctxWithActorId1, actorId1, metrics.ChatBotApiNameNotifyUser).Return(true, nil),
				},
				ctx: context.Background(),
				req: &senseforthPb.NotifyUserRequest{
					EventType: "agent_reply",
					Data: &liveChatFallbackPb.EventData{
						AgentReply: &liveChatFallbackPb.AgentReplyEventData{
							AgentName: "agent-1",
							Message:   &liveChatFallbackPb.Message{},
						},
					},
				},
			},
			want: &senseforthPb.NotifyUserResponse{
				Status: int64(chatBotPb.Status_STATUS_RATE_LIMIT_EXCEEDED),
			},
			wantErr: false,
		},
		{
			name: "notify req validation error",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return(actorId1, nil),
					mockRateLimiHelper.EXPECT().IsRateLimitExceeded(ctxWithActorId1, actorId1, metrics.ChatBotApiNameNotifyUser).Return(false, nil),
				},
				ctx: context.Background(),
				req: &senseforthPb.NotifyUserRequest{
					EventType: "agent_reply",
					Data:      &liveChatFallbackPb.EventData{},
				},
			},
			want: &senseforthPb.NotifyUserResponse{
				Status: int64(chatBotPb.Status_STATUS_INVALID_ARGUMENT),
			},
			wantErr: false,
		},
		{
			name: "cx internal error",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return(actorId1, nil),
					mockRateLimiHelper.EXPECT().IsRateLimitExceeded(ctxWithActorId1, actorId1, metrics.ChatBotApiNameNotifyUser).Return(false, nil),
					mockCxLiveChatFallbackClient.EXPECT().NotifyUser(gomock.Any(), gomock.Any()).Return(&cxPb.NotifyUserResponse{
						Status: rpc.StatusInternal(),
					}, nil),
				},
				ctx: context.Background(),
				req: &senseforthPb.NotifyUserRequest{
					EventType: "agent_reply",
					Data: &liveChatFallbackPb.EventData{
						AgentReply: &liveChatFallbackPb.AgentReplyEventData{
							AgentName: "agent-1",
							Message:   &liveChatFallbackPb.Message{},
						},
					},
				},
			},
			want: &senseforthPb.NotifyUserResponse{
				Status: int64(chatBotPb.Status_STATUS_INTERNAL_ERROR),
			},
			wantErr: false,
		},
		{
			name: "successful",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return(actorId1, nil),
					mockRateLimiHelper.EXPECT().IsRateLimitExceeded(ctxWithActorId1, actorId1, metrics.ChatBotApiNameNotifyUser).Return(false, nil),
					mockCxLiveChatFallbackClient.EXPECT().NotifyUser(gomock.Any(), gomock.Any()).Return(&cxPb.NotifyUserResponse{
						Status: rpc.StatusOk(),
					}, nil),
				},
				ctx: context.Background(),
				req: &senseforthPb.NotifyUserRequest{
					EventType: "agent_reply",
					Data: &liveChatFallbackPb.EventData{
						AgentReply: &liveChatFallbackPb.AgentReplyEventData{
							AgentName: "agent-1",
							Message:   &liveChatFallbackPb.Message{},
						},
					},
				},
			},
			want: &senseforthPb.NotifyUserResponse{
				Status: int64(chatBotPb.Status_STATUS_OK),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(slcsTs.conf, mockCxLiveChatFallbackClient, mockAuthHelper, mockRateLimiHelper)
			got, err := s.NotifyUser(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("NotifyUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NotifyUser() got = %v, want %v", got, tt.want)
			}
		})
	}
}
