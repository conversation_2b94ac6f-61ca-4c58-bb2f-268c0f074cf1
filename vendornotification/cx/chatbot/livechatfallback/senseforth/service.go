package senseforth

import (
	"context"
	"time"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	cxLiveChatFbkPb "github.com/epifi/gamma/api/cx/chat/bot/livechatfallback"
	chatBotPb "github.com/epifi/gamma/api/vendornotification/cx/chatbot"
	vnLiveChatFbkPb "github.com/epifi/gamma/api/vendornotification/cx/chatbot/livechatfallback"
	senseforthPb "github.com/epifi/gamma/api/vendornotification/cx/chatbot/livechatfallback/senseforth"
	redactorConf "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/config"
	chatbotHelper "github.com/epifi/gamma/vendornotification/cx/chatbot/helper"
	"github.com/epifi/gamma/vendornotification/cx/helper"
	"github.com/epifi/gamma/vendornotification/metrics"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type Service struct {
	conf                     *config.Config
	cxLiveChatFallbackClient cxLiveChatFbkPb.LiveChatFallbackClient
	authHelper               chatbotHelper.IAuthHelper
	rateLimitHelper          chatbotHelper.IRateLimitHelper
}

func NewService(conf *config.Config, cxLiveChatFallbackClient cxLiveChatFbkPb.LiveChatFallbackClient, authHelper chatbotHelper.IAuthHelper,
	rateLimitHelper chatbotHelper.IRateLimitHelper) *Service {
	return &Service{
		conf:                     conf,
		cxLiveChatFallbackClient: cxLiveChatFallbackClient,
		authHelper:               authHelper,
		rateLimitHelper:          rateLimitHelper,
	}
}

var _ senseforthPb.LiveChatFallbackServer = &Service{}

const (
	InitiateConversationRequest = "InitiateConversationRequest"
	PushUserMessageRequest      = "PushUserMessageRequest"
	NotifyUserRequest           = "NotifyUserRequest"
)

var (
	msgTypeStrToEnumMap = map[string]cxLiveChatFbkPb.MessageType{
		"normal":  cxLiveChatFbkPb.MessageType_MESSAGE_TYPE_NORMAL,
		"private": cxLiveChatFbkPb.MessageType_MESSAGE_TYPE_PRIVATE,
	}
	msgTypeEnumToStrMap = map[cxLiveChatFbkPb.MessageType]string{
		cxLiveChatFbkPb.MessageType_MESSAGE_TYPE_NORMAL:  "normal",
		cxLiveChatFbkPb.MessageType_MESSAGE_TYPE_PRIVATE: "private",
	}
	eventTypeStrToEnumMap = map[string]cxLiveChatFbkPb.LiveChatEventType{
		"conversation_assignment": cxLiveChatFbkPb.LiveChatEventType_LIVE_CHAT_EVENT_TYPE_CONVERSATION_ASSIGNMENT,
		"agent_reply":             cxLiveChatFbkPb.LiveChatEventType_LIVE_CHAT_EVENT_TYPE_AGENT_REPLY,
		"conversation_resolution": cxLiveChatFbkPb.LiveChatEventType_LIVE_CHAT_EVENT_TYPE_CONVERSATION_RESOLUTION,
	}
)

func (s *Service) InitiateConversation(ctx context.Context, req *senseforthPb.InitiateConversationRequest) (*senseforthPb.InitiateConversationResponse, error) {
	if err := security.CheckWhiteList(ctx, s.conf.SenseforthWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor, s.conf.VpcCidrIPPrefix); err != nil {
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, req, InitiateConversationRequest, "", redactorConf.Config)

	if err := s.authHelper.ValidateApiKey(ctx); err != nil {
		logger.Error(ctx, "error while validating senseforth api key", zap.Error(err))
		statusCode := chatbotHelper.GetStatusCodeFromError(err)
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameInitiateConversation, statusCode.String())
		return &senseforthPb.InitiateConversationResponse{
			Status: int64(statusCode),
		}, nil
	}
	actorId, err := s.authHelper.ValidateShortTokenAndGetActorId(ctx)
	if err != nil {
		logger.Error(ctx, "error while validating short token", zap.Error(err), zap.String(logger.ACTOR_ID, actorId))
		statusCode := chatbotHelper.GetStatusCodeFromError(err)
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameInitiateConversation, statusCode.String())
		return &senseforthPb.InitiateConversationResponse{
			Status: int64(statusCode),
		}, nil
	}
	// inject ctx with actor Id for logging purpose
	ctx = epificontext.CtxWithActorId(ctx, actorId)
	isRateLimitExceeded, rlErr := s.rateLimitHelper.IsRateLimitExceeded(ctx, actorId, metrics.ChatBotApiNameInitiateConversation)
	if rlErr != nil {
		logger.Error(ctx, "error while evaluating rate limits", zap.Error(rlErr), zap.String(logger.ACTOR_ID, actorId))
		statusCode := chatBotPb.Status_STATUS_INTERNAL_ERROR
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameInitiateConversation, statusCode.String())
		return &senseforthPb.InitiateConversationResponse{
			Status: int64(statusCode),
		}, nil
	}
	if isRateLimitExceeded {
		logger.Error(ctx, "Rate limit has exceeded for this user for InitiateConversation API call", zap.String(logger.ACTOR_ID, actorId))
		statusCode := chatBotPb.Status_STATUS_RATE_LIMIT_EXCEEDED
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameInitiateConversation, statusCode.String())
		return &senseforthPb.InitiateConversationResponse{
			Status: int64(statusCode),
		}, nil
	}

	beResp, beErr := s.cxLiveChatFallbackClient.InitiateConversation(ctx, &cxLiveChatFbkPb.InitiateConversationRequest{
		ActorId:  actorId,
		Messages: convertToCxMessagesList(req.GetMessages()),
	})
	if rpcErr := epifigrpc.RPCError(beResp, beErr); rpcErr != nil {
		logger.Error(ctx, "error while sending initiate conversation req to cx backend", zap.Error(rpcErr), zap.String(logger.ACTOR_ID, actorId))
		statusCode := chatBotPb.Status_STATUS_INTERNAL_ERROR
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameInitiateConversation, statusCode.String())
		return &senseforthPb.InitiateConversationResponse{
			Status: int64(statusCode),
		}, nil
	}
	statusCode := chatBotPb.Status_STATUS_OK
	metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameInitiateConversation, statusCode.String())
	return &senseforthPb.InitiateConversationResponse{
		Status:         int64(statusCode),
		ConversationId: beResp.GetConversationId(),
		Messages:       convertToVnMessagesList(beResp.GetMessages()),
	}, nil
}

func (s *Service) PushUserMessage(ctx context.Context, req *senseforthPb.PushUserMessageRequest) (*senseforthPb.PushUserMessageResponse, error) {
	if err := security.CheckWhiteList(ctx, s.conf.SenseforthWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor, s.conf.VpcCidrIPPrefix); err != nil {
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, req, PushUserMessageRequest, "", redactorConf.Config)
	if err := s.authHelper.ValidateApiKey(ctx); err != nil {
		statusCode := chatbotHelper.GetStatusCodeFromError(err)
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNamePushUserMessage, statusCode.String())
		return &senseforthPb.PushUserMessageResponse{
			Status: int64(statusCode),
		}, nil
	}
	actorId, err := s.authHelper.ValidateShortTokenAndGetActorId(ctx)
	if err != nil {
		logger.Error(ctx, "error validating short token to get actor ID", zap.Error(err), zap.String(logger.ACTOR_ID, actorId))
		statusCode := chatbotHelper.GetStatusCodeFromError(err)
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNamePushUserMessage, statusCode.String())
		return &senseforthPb.PushUserMessageResponse{
			Status: int64(statusCode),
		}, nil
	}
	// inject ctx with actor Id for logging purpose
	ctx = epificontext.CtxWithActorId(ctx, actorId)
	isRateLimitExceeded, rlErr := s.rateLimitHelper.IsRateLimitExceeded(ctx, actorId, metrics.ChatBotApiNamePushUserMessage)
	if rlErr != nil {
		logger.Error(ctx, "error while evaluating rate limits", zap.Error(rlErr), zap.String(logger.ACTOR_ID, actorId))
		statusCode := chatBotPb.Status_STATUS_INTERNAL_ERROR
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNamePushUserMessage, statusCode.String())
		return &senseforthPb.PushUserMessageResponse{
			Status: int64(statusCode),
		}, nil
	}
	if isRateLimitExceeded {
		logger.Error(ctx, "Rate limit has exceeded for this user for PushUserMessage API call", zap.String(logger.ACTOR_ID, actorId))
		statusCode := chatBotPb.Status_STATUS_RATE_LIMIT_EXCEEDED
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNamePushUserMessage, statusCode.String())
		return &senseforthPb.PushUserMessageResponse{
			Status: int64(statusCode),
		}, nil
	}

	beResp, beErr := s.cxLiveChatFallbackClient.PushUserMessage(ctx, &cxLiveChatFbkPb.PushUserMessageRequest{
		ActorId:        actorId,
		ConversationId: req.GetConversationId(),
		Message:        convertToCxMessage(req.GetMessage()),
	})
	if rpcErr := epifigrpc.RPCError(beResp, beErr); rpcErr != nil {
		logger.Error(ctx, "error while sending user message to cx backend", zap.Error(rpcErr), zap.String(logger.ACTOR_ID, actorId))
		statusCode := chatBotPb.Status_STATUS_INTERNAL_ERROR
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNamePushUserMessage, statusCode.String())
		return &senseforthPb.PushUserMessageResponse{
			Status: int64(statusCode),
		}, nil
	}
	statusCode := chatBotPb.Status_STATUS_OK
	metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNamePushUserMessage, statusCode.String())
	return &senseforthPb.PushUserMessageResponse{
		Status:         int64(chatBotPb.Status_STATUS_OK),
		ConversationId: beResp.GetConversationId(),
		Message:        convertToVnMessage(beResp.GetMessage()),
	}, nil
}

func (s *Service) NotifyUser(ctx context.Context, req *senseforthPb.NotifyUserRequest) (*senseforthPb.NotifyUserResponse, error) {
	if err := security.CheckWhiteList(ctx, s.conf.SenseforthWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor, s.conf.VpcCidrIPPrefix); err != nil {
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, req, NotifyUserRequest, "", redactorConf.Config)
	if err := s.authHelper.ValidateApiKey(ctx); err != nil {
		statusCode := chatbotHelper.GetStatusCodeFromError(err)
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameNotifyUser, statusCode.String())
		return &senseforthPb.NotifyUserResponse{
			Status: int64(statusCode),
		}, nil
	}
	actorId, err := s.authHelper.ValidateShortTokenAndGetActorId(ctx)
	if err != nil {
		logger.Error(ctx, "error validating short token to get actor ID", zap.Error(err), zap.String(logger.ACTOR_ID, actorId))
		statusCode := chatbotHelper.GetStatusCodeFromError(err)
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameNotifyUser, statusCode.String())
		return &senseforthPb.NotifyUserResponse{
			Status: int64(statusCode),
		}, nil
	}
	// inject ctx with actor Id for logging purpose
	ctx = epificontext.CtxWithActorId(ctx, actorId)
	isRateLimitExceeded, rlErr := s.rateLimitHelper.IsRateLimitExceeded(ctx, actorId, metrics.ChatBotApiNameNotifyUser)
	if rlErr != nil {
		logger.Error(ctx, "error while evaluating rate limits", zap.Error(rlErr), zap.String(logger.ACTOR_ID, actorId))
		statusCode := chatBotPb.Status_STATUS_INTERNAL_ERROR
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameNotifyUser, statusCode.String())
		return &senseforthPb.NotifyUserResponse{
			Status: int64(statusCode),
		}, nil
	}
	if isRateLimitExceeded {
		logger.Error(ctx, "Rate limit has exceeded for this user for NotifyUser API call", zap.String(logger.ACTOR_ID, actorId))
		statusCode := chatBotPb.Status_STATUS_RATE_LIMIT_EXCEEDED
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameNotifyUser, statusCode.String())
		return &senseforthPb.NotifyUserResponse{
			Status: int64(statusCode),
		}, nil
	}

	eventTime, err := time.Parse(time.RFC3339Nano, req.GetEventTime())
	if err != nil {
		logger.Error(ctx, "error parsing action time string", zap.Error(err), zap.String("eventTime", req.GetEventTime()), zap.String(logger.ACTOR_ID, actorId))
		// ignoring error as this is not critical, and we would still want to publish the remaining data to queue
	}
	notifyReq := &cxLiveChatFbkPb.NotifyUserRequest{
		ActorId:   actorId,
		EventType: eventTypeStrToEnumMap[req.GetEventType()],
		EventTime: timestamppb.New(eventTime),
	}
	err = validateAndPopulateEventData(notifyReq, req)
	if err != nil {
		logger.Error(ctx, "error validating action", zap.Error(err), zap.String("eventType", req.GetEventType()), zap.String(logger.ACTOR_ID, actorId))
		statusCode := chatBotPb.Status_STATUS_INVALID_ARGUMENT
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameNotifyUser, statusCode.String())
		// Send error if the request is invalid
		return &senseforthPb.NotifyUserResponse{
			Status: int64(statusCode),
		}, nil
	}
	beResp, beErr := s.cxLiveChatFallbackClient.NotifyUser(ctx, notifyReq)
	if rpcErr := epifigrpc.RPCError(beResp, beErr); rpcErr != nil {
		statusCode := chatBotPb.Status_STATUS_INTERNAL_ERROR
		// record not found is ignored,
		// because this is due to app registration token missing due to which we can't send notification to user
		if beResp.GetStatus().IsRecordNotFound() {
			statusCode = chatBotPb.Status_STATUS_OK
		} else {
			logger.Error(ctx, "error while sending notification request to cx backend", zap.Error(rpcErr), zap.String(logger.ACTOR_ID, actorId))
		}

		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameNotifyUser, statusCode.String())
		return &senseforthPb.NotifyUserResponse{
			Status: int64(statusCode),
		}, nil
	}
	statusCode := chatBotPb.Status_STATUS_OK
	metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameNotifyUser, statusCode.String())
	return &senseforthPb.NotifyUserResponse{
		Status: int64(statusCode),
	}, nil
}

// validateActionAndPopulateEventData validates whether action type is recognized and load data
// the event data must be populated for cxFreshchat struct as per the type of the action
func validateAndPopulateEventData(cxReq *cxLiveChatFbkPb.NotifyUserRequest, req *senseforthPb.NotifyUserRequest) error {
	switch cxReq.GetEventType() {
	case cxLiveChatFbkPb.LiveChatEventType_LIVE_CHAT_EVENT_TYPE_CONVERSATION_ASSIGNMENT:
		if req.GetData().GetConversationAssignment() == nil {
			return errors.New("conversation assignment data is nil for LIVE_CHAT_EVENT_TYPE_CONVERSATION_ASSIGNMENT")
		}
		cxReq.Data = &cxLiveChatFbkPb.NotifyUserRequest_ConversationAssignment{
			ConversationAssignment: &cxLiveChatFbkPb.ConversationAssignmentEventData{
				ConversationId: req.GetData().GetConversationAssignment().GetConversationId(),
				AgentName:      req.GetData().GetConversationAssignment().GetAgentName(),
			},
		}
	case cxLiveChatFbkPb.LiveChatEventType_LIVE_CHAT_EVENT_TYPE_AGENT_REPLY:
		if req.GetData().GetAgentReply() == nil {
			return errors.New("agent reply data is nil for LIVE_CHAT_EVENT_TYPE_AGENT_REPLY")
		}
		cxReq.Data = &cxLiveChatFbkPb.NotifyUserRequest_AgentReply{
			AgentReply: &cxLiveChatFbkPb.AgentReplyEventData{
				Message:   convertToCxMessage(req.GetData().GetAgentReply().GetMessage()),
				AgentName: req.GetData().GetAgentReply().GetAgentName(),
			},
		}
	case cxLiveChatFbkPb.LiveChatEventType_LIVE_CHAT_EVENT_TYPE_CONVERSATION_RESOLUTION:
		if req.GetData().GetConversationResolution() == nil {
			return errors.New("conversation resolution data is nil for LIVE_CHAT_EVENT_TYPE_CONVERSATION_RESOLUTION")
		}
		cxReq.Data = &cxLiveChatFbkPb.NotifyUserRequest_ConversationResolution{
			ConversationResolution: &cxLiveChatFbkPb.ConversationResolutionEventData{
				ConversationId: req.GetData().GetConversationResolution().GetConversationId(),
				AgentName:      req.GetData().GetConversationResolution().GetAgentName(),
			},
		}
	default:
		return errors.New("unspecified or unknown event type")
	}
	return nil
}

func convertToCxMessagesList(vendorMessages []*vnLiveChatFbkPb.Message) []*cxLiveChatFbkPb.Message {
	var cxMessages []*cxLiveChatFbkPb.Message
	for _, msg := range vendorMessages {
		cxMessages = append(cxMessages, convertToCxMessage(msg))
	}
	return cxMessages
}

func convertToCxMessage(vnMessage *vnLiveChatFbkPb.Message) *cxLiveChatFbkPb.Message {
	if vnMessage == nil {
		return nil
	}
	cxMsg := &cxLiveChatFbkPb.Message{
		MessageId:   vnMessage.GetMessageId(),
		MessageType: msgTypeStrToEnumMap[vnMessage.GetMessageType()],
		CreatedTime: vnMessage.GetCreatedTime(),
	}
	for _, part := range vnMessage.GetMessageParts() {
		cxMsg.MessageParts = append(cxMsg.MessageParts, helper.VendorsToCxFcMessagePart(part))
	}
	return cxMsg
}

func convertToVnMessagesList(cxMessages []*cxLiveChatFbkPb.Message) []*vnLiveChatFbkPb.Message {
	var vendorMessages []*vnLiveChatFbkPb.Message
	for _, msg := range cxMessages {
		vendorMessages = append(vendorMessages, convertToVnMessage(msg))
	}
	return vendorMessages
}

func convertToVnMessage(cxMessage *cxLiveChatFbkPb.Message) *vnLiveChatFbkPb.Message {
	if cxMessage == nil {
		return nil
	}
	vnMsg := &vnLiveChatFbkPb.Message{
		MessageType: msgTypeEnumToStrMap[cxMessage.GetMessageType()],
	}
	for _, part := range cxMessage.GetMessageParts() {
		vnMsg.MessageParts = append(vnMsg.MessageParts, part.ToVendorMessagePart())
	}
	return vnMsg
}
