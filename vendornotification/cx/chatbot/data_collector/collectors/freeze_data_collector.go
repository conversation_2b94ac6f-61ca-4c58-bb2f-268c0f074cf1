package collectors

import (
	"context"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	accountsPb "github.com/epifi/gamma/api/accounts"
	formPb "github.com/epifi/gamma/api/risk/case_management/form"
	riskProfilePb "github.com/epifi/gamma/api/risk/profile"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
	cxEvents "github.com/epifi/gamma/vendornotification/cx/events"
)

type FreezeBotDataCollector struct {
	riskProfileClient riskProfilePb.ProfileClient
	eventBroker       events.Broker
}

func NewFreezeDataCollector(
	riskProfileClient riskProfilePb.ProfileClient,
	eventBroker events.Broker,
) *FreezeBotDataCollector {
	return &FreezeBotDataCollector{
		riskProfileClient: riskProfileClient,
		eventBroker:       eventBroker,
	}
}

func (c *FreezeBotDataCollector) CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error) {
	actorId := inputParams.GetActorId()
	if actorId == "" {
		return nil, errors.New("actorId is required")
	}

	// Zomato ID refers to the user ID extracted from the request, which is passed during SDK initialization.
	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		c.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx),
			cxEvents.NewAccountFreezeBotFetchMetadataRequestReceived(actorId))
	})

	resp, err := c.riskProfileClient.GetDetailedUserProfile(ctx, &riskProfilePb.GetDetailedUserProfileRequest{
		ActorId: actorId,
	})

	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error calling backend service", zap.String(logger.ACTOR_ID, actorId), zap.Error(te))
		return nil, te
	}

	var savingsAccountInfo *riskProfilePb.DetailedAccountInfo
	for _, accountInfo := range resp.GetAccountsInfo() {
		if accountInfo.GetAccountType() == accountsPb.Type_SAVINGS {
			savingsAccountInfo = accountInfo
			break
		}
	}

	if savingsAccountInfo == nil {
		return nil, errors.New("savings account information not found in user profile")
	}

	processedFreezeReason := strings.TrimPrefix(savingsAccountInfo.GetProcessedFreezeReason().String(), "PROCESSED_FREEZE_REASON_")
	accountStatus := strings.TrimPrefix(savingsAccountInfo.GetAccountOperationalStatus().String(), "ACCOUNT_")

	freezeBotDataResp := &FreezeBotData{
		ProcessedFreezeReason: processedFreezeReason,
		AccountStatus:         accountStatus,
		FreezeType:            savingsAccountInfo.GetAccountStatus().GetPresentStatus().String(),
	}

	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		c.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx),
			cxEvents.NewAccountFreezeBotInterpretStatusRequestReceived(actorId, freezeBotDataResp.ProcessedFreezeReason, freezeBotDataResp.AccountStatus))
	})

	var leaComplaintDetails []*UnifiedLeaComplaint

	for _, complaint := range savingsAccountInfo.GetUnifiedLeaComplaints() {
		currentComplaintDates := complaint.GetDates()
		leaComplaintDetails = append(leaComplaintDetails, &UnifiedLeaComplaint{
			ComplaintId: complaint.GetComplaintId(),
			Dates: &Dates{
				DetailsReceivedDate: GetTimeInHumanReadableFormatForFreezeDetails(currentComplaintDates.GetDetailsReceivedDate()),
				ComplaintDate:       GetTimeInHumanReadableFormatForFreezeDetails(currentComplaintDates.GetComplaintDate()),
				AuditDate:           GetTimeInHumanReadableFormatForFreezeDetails(currentComplaintDates.GetAuditDate()),
				AccountClosureDate:  GetTimeInHumanReadableFormatForFreezeDetails(currentComplaintDates.GetAccountClosureDate()),
			},
			ReporterContactDetails:  complaint.GetReporterContactDetails(),
			OriginGeographicalState: complaint.GetOriginGeographicalState(),
		})
	}

	if len(leaComplaintDetails) > 0 {
		freezeBotDataResp.LeaComplaintDetails = leaComplaintDetails
	}

	if len(savingsAccountInfo.GetForms()) > 0 {
		for _, form := range savingsAccountInfo.GetForms() {
			if form.GetOrigin() == formPb.FormOrigin_FORM_ORIGIN_ACCOUNT_OPERATIONAL_STATUS_UPDATE {
				freezeBotDataResp.FormId = form.GetFormLink()
				freezeBotDataResp.FormStatus = form.GetStatus().String()
				freezeBotDataResp.FormExpiryDate = GetTimeInHumanReadableFormatForFreezeDetails(form.GetExpireAt())
				break
			}
		}
	}
	return freezeBotDataResp, nil
}
