package options

type DataCollectorParams struct {
	ActorId       string
	TransactionId string
	ZomatoId      string
}

func (s *DataCollectorParams) GetTransactionId() string {
	if s != nil {
		return s.TransactionId
	}
	return ""
}

func (s *DataCollectorParams) GetActorId() string {
	if s != nil {
		return s.ActorId
	}
	return ""
}

func (s *DataCollectorParams) GetZomatoId() string {
	if s != nil {
		return s.ZomatoId
	}
	return ""
}
