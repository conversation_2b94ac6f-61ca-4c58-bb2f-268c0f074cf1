//go:generate mockgen -source=factory.go -destination=../test/mocks/mock_data_collector_factory.go -package=mocks
package data_collector

import (
	"fmt"

	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/collectors"
)

type DataCollectorFactory interface {
	GetImpl(qcType typesv2.ChatbotRequestedDataField) (DataCollector, error)
}

type DataCollectorFactorySvc struct {
	freezeDataCollector      *collectors.FreezeBotDataCollector
	transactionDataCollector *collectors.TransactionDataCollector
}

func NewDataCollectorFactorySvc(
	freezeDataCollector *collectors.FreezeBotDataCollector,
	transactionDataCollector *collectors.TransactionDataCollector,
) *DataCollectorFactorySvc {
	return &DataCollectorFactorySvc{
		freezeDataCollector:      freezeDataCollector,
		transactionDataCollector: transactionDataCollector,
	}
}

func (d *DataCollectorFactorySvc) GetImpl(qcType typesv2.ChatbotRequestedDataField) (DataCollector, error) {
	switch qcType {
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_SAVINGS_ACCOUNT_FREEZE_DETAILS:
		return d.freezeDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_TRANSACTION_DETAILS:
		return d.transactionDataCollector, nil
	default:
		return nil, fmt.Errorf("data collector for %s is not implemented yet", qcType.String())
	}
}
