package senseforth

import (
	"context"
	"errors"
	"flag"
	"os"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	chatBotPb "github.com/epifi/gamma/api/vendornotification/cx/chatbot"
	chatBotAuthPb "github.com/epifi/gamma/api/vendornotification/cx/chatbot/auth/senseforth"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/helper"
	"github.com/epifi/gamma/vendornotification/metrics"
	"github.com/epifi/gamma/vendornotification/test"
	mockHelper "github.com/epifi/gamma/vendornotification/test/mocks/cx/chatbot/helper"
)

type SenseforthChatBotAuthServiceTestSuite struct {
	conf *config.Config
}

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	conf, teardown := test.InitTestServer()
	scsTs = SenseforthChatBotAuthServiceTestSuite{
		conf: conf,
	}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

var (
	scsTs SenseforthChatBotAuthServiceTestSuite

	ctxWithActorId1 = epificontext.CtxWithActorId(context.Background(), actorId1)
)

const (
	actorId1 = "actorId1"
)

func TestService_ValidateToken(t *testing.T) {
	ctr := gomock.NewController(t)
	mockAuthHelper := mockHelper.NewMockIAuthHelper(ctr)
	mockRateLimiHelper := mockHelper.NewMockIRateLimitHelper(ctr)
	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *chatBotAuthPb.ValidateTokenRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *chatBotAuthPb.ValidateTokenResponse
		wantErr bool
	}{
		{
			name: "api key validation failed",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(helper.ErrApiKeyInvalid),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &chatBotAuthPb.ValidateTokenResponse{
				Status: int64(chatBotPb.Status_STATUS_API_KEY_AUTHORIZATION_FAILED),
			},
			wantErr: false,
		},
		{
			name: "short token validation failed: token not found",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return("", helper.ErrShortTokenNotFound),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &chatBotAuthPb.ValidateTokenResponse{
				Status: int64(chatBotPb.Status_STATUS_SHORT_TOKEN_NOT_FOUND_IN_HEADER),
			},
			wantErr: false,
		},
		{
			name: "short token validation failed: token expired",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return("", helper.ErrShortTokenExpired),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &chatBotAuthPb.ValidateTokenResponse{
				Status: int64(chatBotPb.Status_STATUS_TOKEN_EXPIRED_OR_INVALID),
			},
			wantErr: false,
		},
		{
			name: "rate limit check failed",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return(actorId1, nil),
					mockRateLimiHelper.EXPECT().IsRateLimitExceeded(ctxWithActorId1, actorId1, metrics.ChatBotApiNameValidateToken).Return(false, errors.New("test err")),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &chatBotAuthPb.ValidateTokenResponse{
				Status: int64(chatBotPb.Status_STATUS_INTERNAL_ERROR),
			},
			wantErr: false,
		},
		{
			name: "rate limit exceeded",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return(actorId1, nil),
					mockRateLimiHelper.EXPECT().IsRateLimitExceeded(ctxWithActorId1, actorId1, metrics.ChatBotApiNameValidateToken).Return(true, nil),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &chatBotAuthPb.ValidateTokenResponse{
				Status: int64(chatBotPb.Status_STATUS_RATE_LIMIT_EXCEEDED),
			},
			wantErr: false,
		},
		{
			name: "successful",
			args: args{
				mocks: []interface{}{
					mockAuthHelper.EXPECT().ValidateApiKey(context.Background()).Return(nil),
					mockAuthHelper.EXPECT().ValidateShortTokenAndGetActorId(context.Background()).Return(actorId1, nil),
					mockRateLimiHelper.EXPECT().IsRateLimitExceeded(ctxWithActorId1, actorId1, metrics.ChatBotApiNameValidateToken).Return(false, nil),
				},
				ctx: context.Background(),
				req: nil,
			},
			want: &chatBotAuthPb.ValidateTokenResponse{
				Status:    int64(chatBotPb.Status_STATUS_OK),
				LongToken: "actorId1",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(scsTs.conf, mockAuthHelper, mockRateLimiHelper)
			got, err := s.ValidateToken(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateToken() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ValidateToken() got = %v, want %v", got, tt.want)
			}
		})
	}
}
