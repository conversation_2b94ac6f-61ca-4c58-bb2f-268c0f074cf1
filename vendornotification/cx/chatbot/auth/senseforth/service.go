package senseforth

import (
	"context"

	chatBotPb "github.com/epifi/gamma/api/vendornotification/cx/chatbot"
	chatBotAuthPb "github.com/epifi/gamma/api/vendornotification/cx/chatbot/auth/senseforth"
	redactorConf "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/vendornotification/config"
	chatbotHelper "github.com/epifi/gamma/vendornotification/cx/chatbot/helper"
	"github.com/epifi/gamma/vendornotification/metrics"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"

	"go.uber.org/zap"
)

type Service struct {
	conf            *config.Config
	authHelper      chatbotHelper.IAuthHelper
	rateLimitHelper chatbotHelper.IRateLimitHelper
}

func NewService(conf *config.Config, authHelper chatbotHelper.IAuthHelper, rateLimitHelper chatbotHelper.IRateLimitHelper) *Service {
	return &Service{
		conf:            conf,
		authHelper:      authHelper,
		rateLimitHelper: rateLimitHelper,
	}
}

const (
	ValidateTokenRequest = "ValidateTokenRequest"
)

func (s *Service) ValidateToken(ctx context.Context, req *chatBotAuthPb.ValidateTokenRequest) (*chatBotAuthPb.ValidateTokenResponse, error) {
	if err := security.CheckWhiteList(ctx, s.conf.SenseforthWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor, s.conf.VpcCidrIPPrefix); err != nil {
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, req, ValidateTokenRequest, "", redactorConf.Config)

	if err := s.authHelper.ValidateApiKey(ctx); err != nil {
		logger.Error(ctx, "error while validating senseforth api key", zap.Error(err))
		statusCode := chatbotHelper.GetStatusCodeFromError(err)
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameValidateToken, statusCode.String())
		return &chatBotAuthPb.ValidateTokenResponse{
			Status: int64(statusCode),
		}, nil
	}
	actorId, err := s.authHelper.ValidateShortTokenAndGetActorId(ctx)
	if err != nil {
		logger.Error(ctx, "error while validating short token", zap.Error(err))
		statusCode := chatbotHelper.GetStatusCodeFromError(err)
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameValidateToken, statusCode.String())
		return &chatBotAuthPb.ValidateTokenResponse{
			Status: int64(statusCode),
		}, nil
	}
	// inject ctx with actor Id for logging purpose
	ctx = epificontext.CtxWithActorId(ctx, actorId)
	isRateLimitExceeded, rlErr := s.rateLimitHelper.IsRateLimitExceeded(ctx, actorId, metrics.ChatBotApiNameValidateToken)
	if rlErr != nil {
		logger.Error(ctx, "error while evaluating rate limits", zap.Error(rlErr), zap.Any(logger.ACTOR_ID, actorId))
		statusCode := chatBotPb.Status_STATUS_INTERNAL_ERROR
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameValidateToken, statusCode.String())
		return &chatBotAuthPb.ValidateTokenResponse{
			Status: int64(statusCode),
		}, nil
	}
	if isRateLimitExceeded {
		logger.Error(ctx, "Rate limit has exceeded for this user for ValidateToken API call", zap.Any(logger.ACTOR_ID, actorId))
		statusCode := chatBotPb.Status_STATUS_RATE_LIMIT_EXCEEDED
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameValidateToken, statusCode.String())
		return &chatBotAuthPb.ValidateTokenResponse{
			Status: int64(statusCode),
		}, nil
	}

	// return actorId in response
	statusCode := chatBotPb.Status_STATUS_OK
	metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameValidateToken, statusCode.String())
	return &chatBotAuthPb.ValidateTokenResponse{
		Status:    int64(statusCode),
		LongToken: actorId,
	}, nil
}
