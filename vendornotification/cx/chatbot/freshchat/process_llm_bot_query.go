package freshchat

import (
	"context"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"go.uber.org/zap"

	commonVgPb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/inapphelp/issue_reporting"
	"github.com/epifi/gamma/api/user"
	vendormappingPb "github.com/epifi/gamma/api/vendormapping"
	"github.com/epifi/gamma/api/vendornotification/cx/chatbot/inapphelp/freshchat"
	redactorConf "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/config/genconf"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"
)

type Service struct {
	freshchat.UnimplementedFreshchatAIBotServiceServer
	conf       *genconf.Config
	irClient   issue_reporting.ServiceClient
	userClient user.UsersClient
	vmClient   vendormappingPb.VendorMappingServiceClient
}

func NewService(conf *genconf.Config,
	irClient issue_reporting.ServiceClient,
	userClient user.UsersClient,
	vmClient vendormappingPb.VendorMappingServiceClient) *Service {
	return &Service{
		conf:       conf,
		irClient:   irClient,
		userClient: userClient,
		vmClient:   vmClient,
	}
}

var _ freshchat.FreshchatAIBotServiceServer = &Service{}

const (
	processLLMBotQueryRequest = "ProcessLLMBotQueryRequest"
	errorQueryResponse        = "Sorry, not able to process your request right now. Please try again later."
)

func (s *Service) ProcessLLMBotQuery(ctx context.Context, req *freshchat.FreshchatAiBotQueryRequest) (*freshchat.FreshchatAiBotQueryResponse, error) {
	if err := security.CheckWhiteList(ctx, s.conf.FreshchatWhitelist(), s.conf.NumberOfHopsThatAddXForwardedFor(), s.conf.VpcCidrIPPrefix()); err != nil {
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, req, processLLMBotQueryRequest, "", redactorConf.Config)
	if err := s.validateQuery(req); err != nil {
		logger.Error(ctx, "invalid query", zap.Error(err))
		return &freshchat.FreshchatAiBotQueryResponse{
			QueryResponse: errorQueryResponse,
		}, nil
	}

	// email is of the format - <EMAIL>, where refId is the freshdesk id of the user
	// ref - cx/chat/service.go/getChatInitInformationForFreshChat + FreshChatConfig().FreshChatCustomUserEmailFormat
	refId := strings.Split(req.GetEmail(), "@")[0]
	vmResp, err := s.vmClient.GetInputIdByVendor(ctx, &vendormappingPb.GetInputIdByVendorRequest{
		Id:     refId,
		Vendor: commonVgPb.Vendor_FRESHDESK,
	})

	if rpcErr := epifigrpc.RPCError(vmResp, err); rpcErr != nil {
		logger.Error(ctx, "invalid ref id", zap.Error(rpcErr))
		return &freshchat.FreshchatAiBotQueryResponse{
			QueryResponse: errorQueryResponse,
		}, nil
	}

	actorId := vmResp.GetInputId()
	ctx = epificontext.CtxWithActorId(ctx, actorId)

	queryId := uuid.NewString()
	resp, err := s.irClient.RespondUserQuery(ctx, &issue_reporting.RespondUserQueryRequest{
		ActorId:                      actorId,
		UserQuery:                    req.GetUserQuery(),
		SessionId:                    req.GetSessionId(),
		QueryId:                      queryId,
		IsFreshchatExperimentEnabled: true,
		ConversationHistory:          req.GetQueryContext(),
	})
	if grpcErr := epifigrpc.RPCError(resp, err); grpcErr != nil {
		logger.Error(ctx, "error while responding to user query", zap.String("query_id", queryId), zap.Error(grpcErr))
		return &freshchat.FreshchatAiBotQueryResponse{
			QueryResponse: errorQueryResponse,
		}, nil
	}

	// TODO(sayan): check if context reference score handling is required
	logger.Info(ctx, "user query responded", zap.String(logger.ID, queryId),
		zap.Int64(logger.SCORE, resp.GetContextReferenceScore()))

	return &freshchat.FreshchatAiBotQueryResponse{
		QueryResponse: resp.GetResponse(),
	}, nil
}

func (s *Service) validateQuery(req *freshchat.FreshchatAiBotQueryRequest) error {
	if req.GetEmail() == "" {
		return fmt.Errorf("empty email")
	}
	if len(strings.Split(req.GetEmail(), "@")) < 2 {
		return fmt.Errorf("incorrect email format")
	}
	if req.GetUserQuery() == "" {
		return fmt.Errorf("empty user query")
	}
	if req.GetSessionId() == "" {
		return fmt.Errorf("empty session id")
	}
	if req.GetQueryRaisedAt() == "" {
		return fmt.Errorf("empty query raised at")
	}
	if req.GetQueryContext() == "" {
		return fmt.Errorf("empty query context")
	}

	return nil
}
