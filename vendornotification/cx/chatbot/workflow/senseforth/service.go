package senseforth

import (
	"context"

	"go.uber.org/zap"

	cxChatbotWorkflowPb "github.com/epifi/gamma/api/cx/chat/bot/workflow"
	chatBotPb "github.com/epifi/gamma/api/vendornotification/cx/chatbot"
	vnChatbotWorkflowPb "github.com/epifi/gamma/api/vendornotification/cx/chatbot/workflow/senseforth"
	redactorConf "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/vendornotification/config"
	chatbotHelper "github.com/epifi/gamma/vendornotification/cx/chatbot/helper"
	"github.com/epifi/gamma/vendornotification/metrics"
	"github.com/epifi/gamma/vendornotification/redactor"
)

type Service struct {
	conf             *config.Config
	authHelper       chatbotHelper.IAuthHelper
	cxWorkflowClient cxChatbotWorkflowPb.WorkflowClient
}

func NewService(conf *config.Config, authHelper chatbotHelper.IAuthHelper, cxWorkflowClient cxChatbotWorkflowPb.WorkflowClient) *Service {
	return &Service{
		conf:             conf,
		authHelper:       authHelper,
		cxWorkflowClient: cxWorkflowClient,
	}
}

var _ vnChatbotWorkflowPb.ChatBotWorkflowServer = &Service{}

const (
	FetchDataRequest     = "FetchDataRequest"
	ExecuteActionRequest = "ExecuteActionRequest"
)

func (s *Service) FetchData(ctx context.Context, req *vnChatbotWorkflowPb.FetchDataRequest) (*vnChatbotWorkflowPb.FetchDataResponse, error) {
	redactor.LogCallbackRequestData(ctx, req, FetchDataRequest, "", redactorConf.Config)
	// validate and get cx equivalent request
	cxReq, reqErr := s.validateAndConvertToCxFetchDataRequest(ctx, req)
	actorId := cxReq.GetActorId()
	if reqErr != nil {
		logger.Error(ctx, "failed to convert to cx fetch data request", zap.Error(reqErr), zap.String(logger.ACTOR_ID, actorId))
		statusCode := chatbotHelper.GetStatusCodeFromError(reqErr)
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameFetchData, statusCode.String())
		return &vnChatbotWorkflowPb.FetchDataResponse{
			Status: int64(statusCode),
		}, nil
	}
	ctx = epificontext.CtxWithActorId(ctx, actorId)

	// call cx service to fetch data and handle error if any
	resp, err := s.cxWorkflowClient.FetchData(ctx, cxReq)
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "failed invoking cx fetch data request", zap.Error(rpcErr))
		statusCode := chatbotHelper.GetStatusCodeFromRpcStatus(resp.GetStatus())
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameFetchData, statusCode.String())
		return &vnChatbotWorkflowPb.FetchDataResponse{
			Status: int64(statusCode),
		}, nil
	}

	// build vn equivalent response for same
	statusCode := chatBotPb.Status_STATUS_OK
	workflowData := convertToVnResponse(resp)
	logger.Debug(ctx, "VN FetchData response", zap.Any(logger.RESPONSE, workflowData))

	return &vnChatbotWorkflowPb.FetchDataResponse{
		Status:       int64(statusCode),
		WorkflowData: workflowData,
	}, nil
}

func (s *Service) ExecuteAction(ctx context.Context, req *vnChatbotWorkflowPb.ExecuteActionRequest) (*vnChatbotWorkflowPb.ExecuteActionResponse, error) {
	redactor.LogCallbackRequestData(ctx, req, ExecuteActionRequest, "", redactorConf.Config)
	// validate and get cx equivalent request
	cxReq, reqErr := s.validateAndConvertToCxExecuteActionRequest(ctx, req)
	if reqErr != nil {
		logger.Error(ctx, "failed to convert to cx execute action request", zap.Error(reqErr))
		statusCode := chatbotHelper.GetStatusCodeFromError(reqErr)
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameExecuteAction, statusCode.String())
		return &vnChatbotWorkflowPb.ExecuteActionResponse{
			Status: int64(statusCode),
		}, nil
	}

	// call cx service to execute action and handle error if any
	resp, err := s.cxWorkflowClient.ExecuteAction(ctx, cxReq)
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "Execute action CX api failed", zap.Error(rpcErr))
		statusCode := chatbotHelper.GetStatusCodeFromRpcStatus(resp.GetStatus())
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameExecuteAction, statusCode.String())
		return &vnChatbotWorkflowPb.ExecuteActionResponse{
			Status: int64(statusCode),
		}, nil
	}

	// build vn equivalent response for same
	actionResult, convErr := s.convertToVnExecuteActionResult(ctx, resp.GetActionResult())
	if convErr != nil {
		logger.Error(ctx, "failed to convert to vn execute action response", zap.Error(convErr))
		statusCode := chatBotPb.Status_STATUS_INTERNAL_ERROR
		metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameExecuteAction, statusCode.String())
		return &vnChatbotWorkflowPb.ExecuteActionResponse{
			Status: int64(statusCode),
		}, nil
	}
	statusCode := chatBotPb.Status_STATUS_OK
	metrics.RecordChatbotApiStatusCode(metrics.ChatBotApiNameExecuteAction, statusCode.String())
	return &vnChatbotWorkflowPb.ExecuteActionResponse{
		Status:       int64(statusCode),
		ActionResult: actionResult,
	}, nil
}
