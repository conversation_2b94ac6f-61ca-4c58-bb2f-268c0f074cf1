package senseforth

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"

	cxChatbotWorkflowPb "github.com/epifi/gamma/api/cx/chat/bot/workflow"
	"github.com/epifi/gamma/api/cx/data_collector/transaction"
	cxTicketPb "github.com/epifi/gamma/api/cx/ticket"
	vnChatbotWorkflowPb "github.com/epifi/gamma/api/vendornotification/cx/chatbot/workflow/senseforth"
	vendorFD "github.com/epifi/gamma/api/vendors/freshdesk"
	cxPkg "github.com/epifi/gamma/pkg/cx"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	pkgMoney "github.com/epifi/be-common/pkg/money"
	chatbotHelper "github.com/epifi/gamma/vendornotification/cx/chatbot/helper"
)

const (
	queryParams  = "query_params"
	headerParams = "header_params"
)

func (s *Service) validateAndConvertToCxFetchDataRequest(ctx context.Context, req *vnChatbotWorkflowPb.FetchDataRequest) (*cxChatbotWorkflowPb.FetchDataRequest, error) {
	// check if req params are to be read from http header or not
	// ideally req params should not be read from http header
	// this is a temporary workaround to read params from http header
	// until senseforth support sending us pre-known and configured request params in API task flows
	// once that functionality is exposed by senseforth, we should read req params from vn request
	switch strings.ToLower(s.conf.Chatbot.ReadWorkflowReqParamsFrom) {
	case headerParams:
		return s.getCxReqFromHeader(ctx)
	case queryParams:
		return s.getCxReqFromVnReq(ctx, req)
		// by default read from cx query params/ body params
	default:
		return s.getCxReqFromVnReq(ctx, req)
	}
}

// convert from cx to vn response
func convertToVnResponse(resp *cxChatbotWorkflowPb.FetchDataResponse) *vnChatbotWorkflowPb.WorkflowData {
	switch resp.GetWorkflowData().GetData().(type) {
	case *cxChatbotWorkflowPb.WorkflowData_FaqData:
		return getVnFaqData(resp.GetWorkflowData().GetFaqData())
	case *cxChatbotWorkflowPb.WorkflowData_UserDetailsData:
		return getVnUserDetailsData(resp.GetWorkflowData().GetUserDetailsData())
	case *cxChatbotWorkflowPb.WorkflowData_TxnListData:
		return getVnTxnListData(resp.GetWorkflowData().GetTxnListData())
	case *cxChatbotWorkflowPb.WorkflowData_TxnDetailsData:
		return getVnTxnDetailsData(resp.GetWorkflowData().GetTxnDetailsData())
	case *cxChatbotWorkflowPb.WorkflowData_DebitCardTrackingData:
		return getVnDebitCardTrackingData(resp.GetWorkflowData().GetDebitCardTrackingData())
	case *cxChatbotWorkflowPb.WorkflowData_CreditCardTxnListData:
		return getVnCreditCardTxnListData(resp.GetWorkflowData().GetCreditCardTxnListData())
	case *cxChatbotWorkflowPb.WorkflowData_CreditCardStateData:
		return getVnCreditCardStateData(resp.GetWorkflowData().GetCreditCardStateData())
	case *cxChatbotWorkflowPb.WorkflowData_EmploymentData:
		return getVnEmploymentData(resp.GetWorkflowData().GetEmploymentData())
	case *cxChatbotWorkflowPb.WorkflowData_FetchDisputeData:
		return getVnFetchDisputeData(resp.GetWorkflowData().GetFetchDisputeData())
	case *cxChatbotWorkflowPb.WorkflowData_LivenessData:
		return getVnLivenessData(resp.GetWorkflowData().GetLivenessData())
	case *cxChatbotWorkflowPb.WorkflowData_ChargeList:
		return getVnChargeListData(resp.GetWorkflowData().GetChargeList())
	case *cxChatbotWorkflowPb.WorkflowData_DisplayTxnReasonData:
		return getVnDisplayTxnReasonData(resp.GetWorkflowData().GetDisplayTxnReasonData())
	case *cxChatbotWorkflowPb.WorkflowData_FailedTxnsData:
		return getVnFailedTxnsListData(resp.GetWorkflowData().GetFailedTxnsData())
	case *cxChatbotWorkflowPb.WorkflowData_FetchSalaryProgramRegistrationDetailsData:
		return getVnFetchSalaryProgramRegistrationDetailsData(resp.GetWorkflowData().GetFetchSalaryProgramRegistrationDetailsData())
	case *cxChatbotWorkflowPb.WorkflowData_CheckSalaryProgramAmazonVoucherEligibilityData:
		return getVnCheckSalaryProgramAmazonVoucherEligibilityData(resp.GetWorkflowData().GetCheckSalaryProgramAmazonVoucherEligibilityData())
	case *cxChatbotWorkflowPb.WorkflowData_FetchRewardOffersForUserData:
		return getVnFetchRewardOffersForUserData(resp.GetWorkflowData().GetFetchRewardOffersForUserData())
	case *cxChatbotWorkflowPb.WorkflowData_FetchRewardsEventDetailsData:
		return getVnFetchRewardsEventDetailsData(resp.GetWorkflowData().GetFetchRewardsEventDetailsData())
	case *cxChatbotWorkflowPb.WorkflowData_FetchRewardForEventData:
		return getVnFetchRewardForEventData(resp.GetWorkflowData().GetFetchRewardForEventData())
	case *cxChatbotWorkflowPb.WorkflowData_BalanceRefreshData:
		return getVnBalanceRefreshData(resp.GetWorkflowData().GetBalanceRefreshData())
	case *cxChatbotWorkflowPb.WorkflowData_PredefinedMessageTemplateData:
		return getVnPredefinedMessageTemplateData(resp.GetWorkflowData().GetPredefinedMessageTemplateData())
	case *cxChatbotWorkflowPb.WorkflowData_FetchUserTransactionsData:
		return getVnFetchUserTransactionsData(resp.GetWorkflowData().GetFetchUserTransactionsData())
	default:
		return nil
	}
}

func getVnLivenessData(livData *cxChatbotWorkflowPb.LivenessData) *vnChatbotWorkflowPb.WorkflowData {
	return &vnChatbotWorkflowPb.WorkflowData{
		Data: &vnChatbotWorkflowPb.WorkflowData_LivenessData{
			LivenessData: &vnChatbotWorkflowPb.LivenessData{
				LivenessStatus: livData.GetLivenessStatus().String(),
			},
		},
	}
}

func getVnEmploymentData(empData *cxChatbotWorkflowPb.EmploymentData) *vnChatbotWorkflowPb.WorkflowData {
	return &vnChatbotWorkflowPb.WorkflowData{
		Data: &vnChatbotWorkflowPb.WorkflowData_EmploymentData{
			EmploymentData: &vnChatbotWorkflowPb.EmploymentData{
				EmploymentType: empData.GetEmploymentType().String(),
			},
		},
	}
}

func getVnFetchDisputeData(data *cxChatbotWorkflowPb.FetchDisputeData) *vnChatbotWorkflowPb.WorkflowData {
	resp := &vnChatbotWorkflowPb.FetchDisputeData{
		IsDisputeRaised: data.GetIsDisputeRaised().String(),
	}
	// only populate dispute details if its created
	if data.GetIsDisputeRaised() == commontypes.BooleanEnum_TRUE {
		resp.TicketId = data.GetTicketId()
		resp.CreatedAt = data.GetCreatedAt().AsTime().In(datetime.IST).Format(time.RFC822)
		resp.UpdatedAt = data.GetUpdatedAt().AsTime().In(datetime.IST).Format(time.RFC822)
		resp.DisputeState = data.GetDisputeState().String()
	}
	vnResp := &vnChatbotWorkflowPb.WorkflowData{
		Data: &vnChatbotWorkflowPb.WorkflowData_FetchDisputeData{
			FetchDisputeData: resp,
		},
	}
	return vnResp
}

func getVnDebitCardTrackingData(cxData *cxChatbotWorkflowPb.DebitCardTrackingData) *vnChatbotWorkflowPb.WorkflowData {
	return &vnChatbotWorkflowPb.WorkflowData{
		Data: &vnChatbotWorkflowPb.WorkflowData_DebitCardTrackingData{
			DebitCardTrackingData: &vnChatbotWorkflowPb.DebitCardTrackingData{
				CardTrackingDeliveryState: cxData.GetCardTrackingDeliveryState().String(),
				CardActivationState:       cxData.GetCardActivationState().String(),
				CardCreatedAt:             cxData.GetCardCreatedAt().AsTime().In(datetime.IST).Format(time.RFC822),
				AwbNumber:                 cxData.GetAwbNumber(),
				CourierPartner:            cxData.GetCourierPartner(),
			},
		},
	}
}

func getVnCreditCardTxnListData(cxData *cxChatbotWorkflowPb.CreditCardTxnListData) *vnChatbotWorkflowPb.WorkflowData {
	return &vnChatbotWorkflowPb.WorkflowData{
		Data: &vnChatbotWorkflowPb.WorkflowData_CreditCardTxnListData{
			CreditCardTxnListData: &vnChatbotWorkflowPb.CreditCardTxnListData{
				CreditCardTransactions: convertToVnCcTxnList(cxData.GetCreditCardTransactions()),
			},
		},
	}
}

func convertToVnCcTxnList(cxTxnList []*cxChatbotWorkflowPb.CreditCardTransaction) []*vnChatbotWorkflowPb.CreditCardTransaction {
	var vnCcTxnList []*vnChatbotWorkflowPb.CreditCardTransaction
	for _, cxTxnInfo := range cxTxnList {
		vnCcTxnList = append(vnCcTxnList, convertToVnCcTxn(cxTxnInfo))
	}
	return vnCcTxnList
}

func convertToVnCcTxn(cxTxn *cxChatbotWorkflowPb.CreditCardTransaction) *vnChatbotWorkflowPb.CreditCardTransaction {
	return &vnChatbotWorkflowPb.CreditCardTransaction{
		TxnId:                   cxTxn.GetTxnId(),
		Amount:                  pkgMoney.ToDisplayStringWithINRSymbol(cxTxn.GetAmount()),
		TxnTime:                 cxTxn.GetTxnTime().AsTime().In(datetime.IST).Format(time.RFC822),
		TxnStatus:               cxTxn.GetTxnStatus(),
		TxnCategory:             cxTxn.GetTxnCategory(),
		TxnType:                 cxTxn.GetTxnType(),
		Description:             cxTxn.GetDescription(),
		ExternalTxnId:           cxTxn.GetExternalTxnId(),
		PartnerBank:             cxTxn.GetPartnerBank(),
		MerchantNameFromNetwork: cxTxn.GetMerchantNameFromNetwork(),
		MerchantNameByFi:        cxTxn.GetMerchantNameByFi(),
		SettlementStatus:        cxTxn.GetSettlementStatus(),
		FailureReason:           cxTxn.GetFailureReason(),
	}
}

func getVnCreditCardStateData(cxData *cxChatbotWorkflowPb.CreditCardStateData) *vnChatbotWorkflowPb.WorkflowData {
	res := &vnChatbotWorkflowPb.WorkflowData{
		Data: &vnChatbotWorkflowPb.WorkflowData_CreditCardStateData{
			CreditCardStateData: &vnChatbotWorkflowPb.CreditCardStateData{
				CreditCardState: cxData.GetCreditCardState().String(),
			},
		},
	}

	switch cxData.GetCreditCardState() {
	case cxChatbotWorkflowPb.CreditCardStateData_CREDIT_CARD_STATE_ONBOARDED:
		res.GetCreditCardStateData().Payload = &vnChatbotWorkflowPb.CreditCardStateData_CreditCardStatus{
			CreditCardStatus: &vnChatbotWorkflowPb.CreditCardStatus{
				IsCardInTransit: cxData.GetCreditCardStatus().GetIsCardInTransit().String(),
				Status:          cxData.GetCreditCardStatus().GetStatus(),
				CreditCardId:    cxData.GetCreditCardStatus().GetCreditCardId(),
			},
		}

	case cxChatbotWorkflowPb.CreditCardStateData_CREDIT_CARD_STATE_APPLICATION_IN_PROGRESS:
		res.GetCreditCardStateData().Payload = &vnChatbotWorkflowPb.CreditCardStateData_ApplicationStage{
			ApplicationStage: &vnChatbotWorkflowPb.ApplicationStage{
				CurrentOnboardingStageName: cxData.GetApplicationStage().GetCurrentOnboardingStageName(),
				Status:                     cxData.GetApplicationStage().GetStatus(),
			},
		}

	default:
	}

	return res
}

func getVnTxnDetailsData(cxData *cxChatbotWorkflowPb.TxnDetailsData) *vnChatbotWorkflowPb.WorkflowData {
	return &vnChatbotWorkflowPb.WorkflowData{
		Data: &vnChatbotWorkflowPb.WorkflowData_TxnDetailsData{
			TxnDetailsData: &vnChatbotWorkflowPb.TxnDetailsData{
				TxnInfo: convertToVnTxnInfo(cxData.GetTxnInfo()),
			},
		},
	}
}

func getVnTxnListData(cxData *cxChatbotWorkflowPb.TxnListData) *vnChatbotWorkflowPb.WorkflowData {
	return &vnChatbotWorkflowPb.WorkflowData{
		Data: &vnChatbotWorkflowPb.WorkflowData_TxnListData{
			TxnListData: &vnChatbotWorkflowPb.TxnListData{
				TxnList: convertToVnTxnList(cxData.GetTxnList()),
			},
		},
	}
}

func convertToVnTxnList(cxTxnList []*cxChatbotWorkflowPb.TransactionInfo) []*vnChatbotWorkflowPb.TransactionInfo {
	var vnTxnList []*vnChatbotWorkflowPb.TransactionInfo
	for _, cxTxnInfo := range cxTxnList {
		vnTxnList = append(vnTxnList, convertToVnTxnInfo(cxTxnInfo))
	}
	return vnTxnList
}

func convertToVnTxnInfo(cxTxnInfo *cxChatbotWorkflowPb.TransactionInfo) *vnChatbotWorkflowPb.TransactionInfo {
	return &vnChatbotWorkflowPb.TransactionInfo{
		TxnId:           cxTxnInfo.GetTxnId(),
		Utr:             cxTxnInfo.GetUtr(),
		TxnType:         cxTxnInfo.GetTxnType().String(),
		Amount:          pkgMoney.ToDisplayStringWithINRSymbol(cxTxnInfo.GetAmount()),
		TxnStatus:       cxTxnInfo.GetTxnStatus(),
		Provenance:      cxTxnInfo.GetProvenance(),
		PaymentProtocol: cxTxnInfo.GetPaymentProtocol(),
		FromPiDetails:   convertToVnPiDetails(cxTxnInfo.GetFromPiDetails()),
		ToPiDetails:     convertToVnPiDetails(cxTxnInfo.GetToPiDetails()),
		MerchantName:    cxTxnInfo.GetMerchantName(),
		CreatedAt:       cxTxnInfo.GetCreatedAt().AsTime().In(datetime.IST).Format(time.RFC822),
		IsWithinTat:     cxTxnInfo.GetIsWithinTat().String(),
		DisputedAt:      cxTxnInfo.GetDisputedAt().AsTime().In(datetime.IST).Format(time.RFC822),
		// removing trailing spaces while sending api response, as sense-forth is not able to handle it
		DisplayString:        strings.TrimSpace(cxTxnInfo.GetDisplayString()),
		RaiseDisputeDeeplink: cxTxnInfo.GetRaiseDisputeDeeplink(),
		OrderType:            cxTxnInfo.GetOrderType().String(),
	}
}

func convertToVnPiDetails(cxPiDetails *transaction.PIDetails) *vnChatbotWorkflowPb.PIDetails {
	return &vnChatbotWorkflowPb.PIDetails{
		CustomerName: cxPiDetails.GetCustomerName(),
		PiType:       cxPiDetails.GetPiType(),
		PiValue:      cxPiDetails.GetPiValue(),
		PiState:      cxPiDetails.GetPiState(),
	}
}

func getVnUserDetailsData(userDetailsData *cxChatbotWorkflowPb.UserDetailsData) *vnChatbotWorkflowPb.WorkflowData {
	return &vnChatbotWorkflowPb.WorkflowData{
		Data: &vnChatbotWorkflowPb.WorkflowData_UserDetailsData{
			UserDetailsData: &vnChatbotWorkflowPb.UserDetailsData{
				KycLevel:                   userDetailsData.GetKycLevel().String(),
				VkycStatus:                 userDetailsData.GetVkycStatus().String(),
				DaysLeftToCompleteVkyc:     userDetailsData.GetDaysLeftToCompleteVkyc(),
				VkycCompletionDeadlineDate: userDetailsData.GetVkycCompletionDeadlineDate(),
				AccountClosureDate:         userDetailsData.GetAccountClosureDate(),
				VkycReviewDurationInDays:   userDetailsData.GetVkycReviewDurationInDays(),
			},
		},
	}
}

// get vn equivalent of faq data object
func getVnFaqData(faqData *cxChatbotWorkflowPb.FaqData) *vnChatbotWorkflowPb.WorkflowData {
	return &vnChatbotWorkflowPb.WorkflowData{
		Data: &vnChatbotWorkflowPb.WorkflowData_FaqData{
			FaqData: &vnChatbotWorkflowPb.FaqData{
				ArticleList: convertToVnArticleList(faqData.GetArticleList()),
			},
		},
	}
}

// convert from cx to vn article list
func convertToVnArticleList(cxArticleList []*cxChatbotWorkflowPb.Article) []*vnChatbotWorkflowPb.Article {
	var vnArticleList []*vnChatbotWorkflowPb.Article
	for _, article := range cxArticleList {
		vnArticleList = append(vnArticleList, &vnChatbotWorkflowPb.Article{
			Title:       article.GetTitle(),
			Description: article.GetDescription(),
		})
	}
	return vnArticleList
}

// nolint:funlen, unparam
func (s *Service) constructCxFetchDataRequest(ctx context.Context, actorId string, workflowEntity cxChatbotWorkflowPb.WorkflowEntity, fetchDataParams *vnChatbotWorkflowPb.FetchDataParameters) (*cxChatbotWorkflowPb.FetchDataRequest, error) {
	cxReq := &cxChatbotWorkflowPb.FetchDataRequest{
		ActorId: actorId,
	}
	cxReq.WorkflowEntity = workflowEntity
	switch workflowEntity {
	case cxChatbotWorkflowPb.WorkflowEntity_WORK_FLOW_ENTITY_FAQ:
		// entity found is faq then validate article id list
		// article list cannot be empty
		articleIdList := fetchDataParams.GetFaqParameters().GetArticleIdList()
		if len(articleIdList) == 0 {
			return nil, errors.Wrap(chatbotHelper.InvalidEntityErr, "article id list is empty")
		}
		cxReq.FetchDataParameters = &cxChatbotWorkflowPb.FetchDataParameters{
			EntityParameters: &cxChatbotWorkflowPb.FetchDataParameters_FaqParameters{
				FaqParameters: &cxChatbotWorkflowPb.FaqParameters{
					ArticleIdList: articleIdList,
				},
			},
		}
	case cxChatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_USER_DETAILS:
		cxReq.FetchDataParameters = &cxChatbotWorkflowPb.FetchDataParameters{
			EntityParameters: &cxChatbotWorkflowPb.FetchDataParameters_UserDetailsParameters{
				UserDetailsParameters: &cxChatbotWorkflowPb.UserDetailsParameters{
					ActorId: actorId,
				},
			},
		}
	case cxChatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_TXN_LIST:
		txnTypeEnum, err := s.parseTransactionTypeEnum(strings.ToLower(fetchDataParams.GetTxnListParameters().GetTxnType()))
		if err != nil {
			return nil, errors.Wrap(err, "error while parsing transaction type enum")
		}
		cxReq.FetchDataParameters = &cxChatbotWorkflowPb.FetchDataParameters{
			EntityParameters: &cxChatbotWorkflowPb.FetchDataParameters_TxnListParameters{
				TxnListParameters: &cxChatbotWorkflowPb.TxnListParameters{
					ActorId:          actorId,
					Provenances:      fetchDataParams.GetTxnListParameters().GetProvenances(),
					PaymentProtocols: fetchDataParams.GetTxnListParameters().GetPaymentProtocols(),
					TxnType:          txnTypeEnum,
				},
			},
		}
	case cxChatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_TXN_DETAILS:
		cxReq.FetchDataParameters = &cxChatbotWorkflowPb.FetchDataParameters{
			EntityParameters: &cxChatbotWorkflowPb.FetchDataParameters_TxnDetailsParameters{
				TxnDetailsParameters: &cxChatbotWorkflowPb.TxnDetailsParameters{
					ActorId:          actorId,
					TxnDisplayString: fetchDataParams.GetTxnDetailsParameters().GetTxnDisplayString(),
				},
			},
		}
	case cxChatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_DEBIT_CARD_TRACKING:
		cxReq.FetchDataParameters = &cxChatbotWorkflowPb.FetchDataParameters{
			EntityParameters: &cxChatbotWorkflowPb.FetchDataParameters_DebitCardTrackingParameters{
				DebitCardTrackingParameters: &cxChatbotWorkflowPb.DebitCardTrackingParameters{
					ActorId: actorId,
				},
			},
		}
	case cxChatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_CREDIT_CARD_STATE:
		cxReq.FetchDataParameters = &cxChatbotWorkflowPb.FetchDataParameters{
			EntityParameters: &cxChatbotWorkflowPb.FetchDataParameters_CreditCardStateParameters{
				CreditCardStateParameters: &cxChatbotWorkflowPb.CreditCardStateParameters{
					ActorId: actorId,
				},
			},
		}
	case cxChatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_CREDIT_CARD_TXN_LIST:
		cxReq.FetchDataParameters = &cxChatbotWorkflowPb.FetchDataParameters{
			EntityParameters: &cxChatbotWorkflowPb.FetchDataParameters_CreditCardTxnListParameters{
				CreditCardTxnListParameters: &cxChatbotWorkflowPb.CreditCardTxnListParameters{
					ActorId: actorId,
				},
			},
		}
	case cxChatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_EMPLOYMENT_DATA:
		cxReq.FetchDataParameters = &cxChatbotWorkflowPb.FetchDataParameters{
			EntityParameters: &cxChatbotWorkflowPb.FetchDataParameters_EmploymentDataParameters{
				EmploymentDataParameters: &cxChatbotWorkflowPb.EmploymentDataParameters{
					ActorId: actorId,
				},
			},
		}
	case cxChatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_DISPUTE_DETAILS:
		cxReq.FetchDataParameters = &cxChatbotWorkflowPb.FetchDataParameters{
			EntityParameters: &cxChatbotWorkflowPb.FetchDataParameters_DisputeParameters{
				DisputeParameters: &cxChatbotWorkflowPb.DisputeParameters{
					ActorId:       actorId,
					DisplayString: fetchDataParams.GetDisputeParameters().GetDisplayString(),
				},
			},
		}
	case cxChatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_LIVENESS:
		cxReq.FetchDataParameters = &cxChatbotWorkflowPb.FetchDataParameters{
			EntityParameters: &cxChatbotWorkflowPb.FetchDataParameters_LivenessDataParams{
				LivenessDataParams: &cxChatbotWorkflowPb.LivenessDataParams{
					ActorId: actorId,
				},
			},
		}
	case cxChatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_FETCH_CHARGES_FOR_ACTOR:
		cxReq.FetchDataParameters = &cxChatbotWorkflowPb.FetchDataParameters{
			EntityParameters: &cxChatbotWorkflowPb.FetchDataParameters_ChargesListForActorParams{
				ChargesListForActorParams: &cxChatbotWorkflowPb.ChargesListForActorParams{
					SessionActorId: actorId,
				},
			},
		}
	case cxChatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_DISPLAY_TXN_REASON_FOR_ACTOR:
		cxReq.FetchDataParameters = &cxChatbotWorkflowPb.FetchDataParameters{
			EntityParameters: &cxChatbotWorkflowPb.FetchDataParameters_DisplayTxnReasonParams{
				DisplayTxnReasonParams: &cxChatbotWorkflowPb.DisplayTxnReasonParameters{
					SessionActorId:   actorId,
					TxnDisplayString: fetchDataParams.GetDisplayTxnReasonsParameters().GetTxnDisplayString(),
				},
			},
		}
	case cxChatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_FETCH_FAILED_TRANSACTIONS_FOR_ACTOR:
		cxReq.FetchDataParameters = &cxChatbotWorkflowPb.FetchDataParameters{
			EntityParameters: &cxChatbotWorkflowPb.FetchDataParameters_FailedTxnsDataParams{
				FailedTxnsDataParams: &cxChatbotWorkflowPb.FailedTxnsDataParameters{
					SessionActorId: actorId,
				},
			},
		}
	case cxChatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_CHECK_SALARY_PROGRAM_AMAZON_VOUCHER_ELIGIBILITY:
		cxReq.FetchDataParameters = &cxChatbotWorkflowPb.FetchDataParameters{
			EntityParameters: &cxChatbotWorkflowPb.FetchDataParameters_CheckSalaryProgramAmazonVoucherEligibilityParams{
				CheckSalaryProgramAmazonVoucherEligibilityParams: &cxChatbotWorkflowPb.CheckSalaryProgramAmazonVoucherEligibilityParams{
					ActorId: actorId,
				},
			},
		}
	case cxChatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_FETCH_SALARY_PROGRAM_REGISTRATION_DETAILS:
		cxReq.FetchDataParameters = &cxChatbotWorkflowPb.FetchDataParameters{
			EntityParameters: &cxChatbotWorkflowPb.FetchDataParameters_FetchSalaryProgramRegistrationDetailsParameters{
				FetchSalaryProgramRegistrationDetailsParameters: &cxChatbotWorkflowPb.FetchSalaryProgramRegistrationDetailsParameters{
					ActorId: actorId,
				},
			},
		}
	case cxChatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_FETCH_REWARD_OFFERS_FOR_USER:
		cxReq.FetchDataParameters = &cxChatbotWorkflowPb.FetchDataParameters{
			EntityParameters: &cxChatbotWorkflowPb.FetchDataParameters_FetchRewardOffersForUserParameters{
				FetchRewardOffersForUserParameters: &cxChatbotWorkflowPb.FetchRewardOffersForUserParameters{
					ActorId: actorId,
				},
			},
		}
	case cxChatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_FETCH_REWARDS_EVENT_DETAILS:
		cxReq.FetchDataParameters = &cxChatbotWorkflowPb.FetchDataParameters{
			EntityParameters: &cxChatbotWorkflowPb.FetchDataParameters_FetchRewardsEventDetailsParameters{
				FetchRewardsEventDetailsParameters: &cxChatbotWorkflowPb.FetchRewardsEventDetailsParameters{
					ActorId:       actorId,
					DisplayString: fetchDataParams.GetFetchRewardsEventDetailsParameters().GetDisplayString(),
				},
			},
		}
	case cxChatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_FETCH_REWARD_FOR_EVENT:
		fetchRewardForEventParameters := &cxChatbotWorkflowPb.FetchRewardForEventParameters{}
		switch fetchDataParams.GetFetchRewardForEventParameters().GetEventDetails().(type) {
		case *vnChatbotWorkflowPb.FetchRewardForEventParameters_OrderDetails:
			fetchRewardForEventParameters.EventDetails = &cxChatbotWorkflowPb.FetchRewardForEventParameters_OrderDetails{
				OrderDetails: &cxChatbotWorkflowPb.OrderDetails{
					OrderDisplayString: fetchDataParams.GetFetchRewardForEventParameters().GetOrderDetails().GetOrderDisplayString(),
				},
			}
		case *vnChatbotWorkflowPb.FetchRewardForEventParameters_CreditCardTransactionDetails:
			fetchRewardForEventParameters.EventDetails = &cxChatbotWorkflowPb.FetchRewardForEventParameters_CreditCardTransactionDetails{
				CreditCardTransactionDetails: &cxChatbotWorkflowPb.CreditCardTransactionDetails{
					CcDisplayString: fetchDataParams.GetFetchRewardForEventParameters().GetCreditCardTransactionDetails().GetCcDisplayString(),
				},
			}
		case *vnChatbotWorkflowPb.FetchRewardForEventParameters_ReferralDetails:
			fetchRewardForEventParameters.EventDetails = &cxChatbotWorkflowPb.FetchRewardForEventParameters_ReferralDetails{
				ReferralDetails: &cxChatbotWorkflowPb.ReferralDetail{
					ReferralDisplayString: fetchDataParams.GetFetchRewardForEventParameters().GetReferralDetails().GetReferralDisplayString(),
				},
			}
		}
		fetchRewardForEventParameters.RewardOfferDisplayString = fetchDataParams.GetFetchRewardForEventParameters().GetRewardOfferDisplayString()
		fetchRewardForEventParameters.ActorId = actorId
		cxReq.FetchDataParameters = &cxChatbotWorkflowPb.FetchDataParameters{
			EntityParameters: &cxChatbotWorkflowPb.FetchDataParameters_FetchRewardForEventParameters{
				FetchRewardForEventParameters: fetchRewardForEventParameters,
			},
		}
	case cxChatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_REFRESH_BALANCE:
		cxReq.FetchDataParameters = &cxChatbotWorkflowPb.FetchDataParameters{
			EntityParameters: &cxChatbotWorkflowPb.FetchDataParameters_BalanceRefreshParams{
				BalanceRefreshParams: &cxChatbotWorkflowPb.BalanceRefreshParams{
					SessionActorId: actorId,
				},
			},
		}
	case cxChatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_PREDEFINED_MESSAGE_TEMPLATE:
		cxReq.FetchDataParameters = &cxChatbotWorkflowPb.FetchDataParameters{
			EntityParameters: &cxChatbotWorkflowPb.FetchDataParameters_PredefinedMessageTemplateParameters{
				PredefinedMessageTemplateParameters: &cxChatbotWorkflowPb.PredefinedMessageTemplateDataParameters{
					ActorId: actorId,
				},
			},
		}
	case cxChatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_FETCH_USER_TRANSACTIONS:
		cxReq.FetchDataParameters = &cxChatbotWorkflowPb.FetchDataParameters{
			EntityParameters: &cxChatbotWorkflowPb.FetchDataParameters_FetchUserTransactionsParameters{
				FetchUserTransactionsParameters: &cxChatbotWorkflowPb.FetchUserTransactionsParameters{
					ActorId:                   actorId,
					RewardTransactionCategory: cxChatbotWorkflowPb.RewardTransactionCategory(cxChatbotWorkflowPb.RewardTransactionCategory_value[fetchDataParams.GetFetchUserTransactionsParameters().GetRewardTransactionCategory().String()]),
				},
			},
		}
	default:
		// handling for entities which are not configured on backend
		return nil, errors.Wrap(chatbotHelper.InvalidEntityErr, "entity not configured")
	}
	return cxReq, nil
}

func (s *Service) getCxReqFromHeader(ctx context.Context) (*cxChatbotWorkflowPb.FetchDataRequest, error) {
	// extract http header metadata from context
	// return error if not found
	headerMetaData, isMetaDataAppendedInContext := metadata.FromIncomingContext(ctx)
	if !isMetaDataAppendedInContext {
		return nil, errors.Wrap(chatbotHelper.InvalidEntityErr, "no metadata found in context")
	}

	// obtain entity from http header K,V store
	// return error if we do not find fetch data entity key in header K,V
	entityStr, isEntityStrPresent := headerMetaData[strings.ToLower(cxPkg.FetchDataEntityKey)]
	if !isEntityStrPresent {
		return nil, errors.Wrap(chatbotHelper.InvalidEntityErr, "entity not found in http header")
	}
	// obtain article id list from http header K,V store
	// return error if we do not find article id list key in header K,V
	articleIdList, isArticleIdListPresent := headerMetaData[strings.ToLower(cxPkg.ArticleIdListKey)]
	if !isArticleIdListPresent {
		return nil, errors.Wrap(chatbotHelper.InvalidEntityErr, "article id list not found in http header")
	}

	// parse obtained string entity to enum entity
	// using 0th index here since metadata K, V structure is <string, []string>
	workflowEntity, entityErr := s.getCxEntity(entityStr[0])
	if entityErr != nil {
		return nil, errors.Wrap(chatbotHelper.InvalidEntityErr, "invalid entity found")
	}

	// obtain integer list of article id from give string
	// input string in header will look like this: "1, 2, 3" all ids stuffed into single string
	// here delimiter between two data is ","
	// we should transform it to integer it looks like this: [1, 2, 3]
	// using 0th index here since metadata K, V structure is <string, []string>
	intArticleIdList, err := getIntArticleIdList(articleIdList[0])
	if err != nil {
		return nil, errors.Wrap(chatbotHelper.InvalidEntityErr, "invalid article id list")
	}
	actorId, err := s.authHelper.ValidateShortTokenAndGetActorId(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch actor Id")
	}
	return s.constructCxFetchDataRequest(ctx, actorId, workflowEntity, &vnChatbotWorkflowPb.FetchDataParameters{
		EntityParameters: &vnChatbotWorkflowPb.FetchDataParameters_FaqParameters{
			FaqParameters: &vnChatbotWorkflowPb.FaqParameters{
				ArticleIdList: intArticleIdList,
			},
		},
	})
}

func (s *Service) getCxEntity(entity string) (cxChatbotWorkflowPb.WorkflowEntity, error) {
	// get enum string for given entity from config
	cxEnumStr, isKeyFound := s.conf.Chatbot.VnToCxEntityMap[strings.ToUpper(entity)]
	if !isKeyFound {
		return cxChatbotWorkflowPb.WorkflowEntity_WORK_FLOW_ENTITY_UNSPECIFIED, errors.Wrap(chatbotHelper.InvalidEntityErr, "vn request entity key not found in config map")
	}
	// get enum equivalent from str
	val, isValidWorkflowEntity := cxChatbotWorkflowPb.WorkflowEntity_value[strings.ToUpper(cxEnumStr)]
	if !isValidWorkflowEntity {
		return cxChatbotWorkflowPb.WorkflowEntity_WORK_FLOW_ENTITY_UNSPECIFIED, errors.Wrap(chatbotHelper.InvalidEntityErr, "invalid cx enum str value for workflow entity")
	}
	return cxChatbotWorkflowPb.WorkflowEntity(val), nil
}

func getIntArticleIdList(articleIdStr string) ([]int64, error) {
	var intIdList []int64
	// split give article id string by ","
	// and loop over it to obtain integer equivalent for the same
	for _, idStr := range strings.Split(articleIdStr, ",") {
		// trim space in string id, to avoid any space addition done during API task flow configuration
		intId, err := strconv.ParseInt(strings.TrimSpace(idStr), 10, 64)
		if err != nil {
			return nil, errors.New("invalid article id")
		}
		intIdList = append(intIdList, intId)
	}
	return intIdList, nil
}

func (s *Service) getCxReqFromVnReq(ctx context.Context, req *vnChatbotWorkflowPb.FetchDataRequest) (*cxChatbotWorkflowPb.FetchDataRequest, error) {
	// get cx equivalent entity enum str
	// key is vn entity
	// value is cx enum as string
	workflowEntity, entityErr := s.getCxEntity(req.GetEntity())
	if entityErr != nil {
		return nil, errors.Wrap(chatbotHelper.InvalidEntityErr, entityErr.Error())
	}
	actorId, err := s.authHelper.ValidateShortTokenAndGetActorId(getCtxWithShortToken(ctx, req.GetShortToken()))
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch actor Id")
	}
	return s.constructCxFetchDataRequest(ctx, actorId, workflowEntity, req.GetFetchDataParameters())
}

func (s *Service) getCxAction(action string) (cxChatbotWorkflowPb.WorkflowAction, error) {
	// get enum string for given entity from config
	cxEnumStr, isKeyFound := s.conf.Chatbot.VnToCxActionMap[strings.ToUpper(action)]
	if !isKeyFound {
		return cxChatbotWorkflowPb.WorkflowAction_WORKFLOW_ACTION_UNSPECIFIED, errors.Wrap(chatbotHelper.InvalidActionErr, "vn request action key not found in config map")
	}
	// get enum equivalent from str
	val, isValidWorkflowAction := cxChatbotWorkflowPb.WorkflowAction_value[strings.ToUpper(cxEnumStr)]
	if !isValidWorkflowAction {
		return cxChatbotWorkflowPb.WorkflowAction_WORKFLOW_ACTION_UNSPECIFIED, errors.Wrap(chatbotHelper.InvalidActionErr, "invalid cx enum str value for workflow action")
	}
	return cxChatbotWorkflowPb.WorkflowAction(val), nil
}

func (s *Service) validateAndConvertToCxExecuteActionRequest(ctx context.Context, req *vnChatbotWorkflowPb.ExecuteActionRequest) (*cxChatbotWorkflowPb.ExecuteActionRequest, error) {
	workflowAction, actionErr := s.getCxAction(req.GetAction())
	if actionErr != nil {
		return nil, errors.Wrap(chatbotHelper.InvalidActionErr, "invalid action found")
	}
	actorId, err := s.authHelper.ValidateShortTokenAndGetActorId(getCtxWithShortToken(ctx, req.GetShortToken()))
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch actor Id")
	}
	cxReq := &cxChatbotWorkflowPb.ExecuteActionRequest{
		ActorId: actorId,
	}
	cxReq.WorkflowAction = workflowAction
	switch workflowAction {
	case cxChatbotWorkflowPb.WorkflowAction_WORKFLOW_ACTION_CREATE_TICKET:
		ticket, ticketErr := getTicketFromHeaderOrReqParams(ctx, req.GetExecuteActionParams().GetCreateTicketParams())
		if ticketErr != nil {
			return nil, errors.Wrap(ticketErr, "failed to get ticket data for create ticket action")
		}
		logger.Debug(ctx, "vendor ticket input", zap.Any("ticket", ticket))
		cxTicket := cxTicketPb.FromFreshdeskVendorTicketAndRequesterMessage(ticket, ticket.GetRequester(), s.conf.Freshdesk, s.conf.Application.Environment)
		cxReq.ExecuteActionParams = &cxChatbotWorkflowPb.ExecuteActionParams{
			ActionParams: &cxChatbotWorkflowPb.ExecuteActionParams_CreateTicketParams{
				CreateTicketParams: &cxChatbotWorkflowPb.CreateTicketParams{
					Ticket: cxTicket,
				},
			},
		}
	default:
		// handling for actions which are not configured on backend
		return nil, errors.Wrap(chatbotHelper.InvalidActionErr, "action not configured")
	}
	logger.Debug(ctx, "execute action cx request", zap.Any("cxExecuteActionRequest", cxReq))
	return cxReq, nil
}

func getCtxWithShortToken(ctx context.Context, shortTokenFromReqParams string) context.Context {
	// if short token is not explicitly present in the request params, we assume that it is present in header.
	// So return ctx which is expected to contain the short token
	if shortTokenFromReqParams == "" {
		return ctx
	}
	// if short token is explicitly mentioned in request, add it to ctx
	return metadata.NewIncomingContext(ctx, metadata.New(map[string]string{
		cxPkg.ChatBotShortTokenHttpHeaderKey: shortTokenFromReqParams,
	}))
}

// nolint: funlen
func getTicketFromHeaderOrReqParams(ctx context.Context, reqParams *vnChatbotWorkflowPb.CreateTicketParams) (*vendorFD.Ticket, error) {
	var (
		groupId, agentId                    int64
		err                                 error
		productCategoryDetails, subcategory string
	)
	if reqParams.GetTicket() != nil {
		return reqParams.GetTicket(), nil
	}
	// if the ticket is not provided in request params, then try reading from header
	// We are providing this option to make input easy for PE team. [Because a.ware platform doesn't support easy input for body params]
	md, isMetaDataAppendedInContext := metadata.FromIncomingContext(ctx)
	if !isMetaDataAppendedInContext {
		return nil, errors.New("no metadata found in context")
	}
	subject, isSubjectFound := md[strings.ToLower(cxPkg.TicketSubjectKey)]
	if !isSubjectFound {
		return nil, errors.New("ticket subject not found in header")
	}
	description, isDescriptionFound := md[strings.ToLower(cxPkg.TicketDescriptionKey)]
	if !isDescriptionFound {
		return nil, errors.New("ticket description not found in header")
	}
	groupIdStr, isGroupIdStrFound := md[strings.ToLower(cxPkg.TicketGroupId)]
	if isGroupIdStrFound {
		groupId, err = strconv.ParseInt(groupIdStr[0], 10, 64)
		if err != nil {
			return nil, errors.Wrap(err, "failed to parse group Id from header")
		}
	}

	agentIdStr, isAgentIdStrFound := md[strings.ToLower(cxPkg.TicketAgentId)]
	if isAgentIdStrFound {
		agentId, err = strconv.ParseInt(agentIdStr[0], 10, 64)
		if err != nil {
			return nil, errors.Wrap(err, "failed to parse group Id from header")
		}
	}

	l1, isL1Found := md[strings.ToLower(cxPkg.TicketL1)]
	if !isL1Found {
		return nil, errors.New("Product category not found in header")
	}
	// L2 and L3 are not mandatory. So no failure checks
	l2, isL2Found := md[strings.ToLower(cxPkg.TicketL2)]
	if isL2Found {
		productCategoryDetails = l2[0]
	}
	l3, isL3Found := md[strings.ToLower(cxPkg.TicketL3)]
	if isL3Found {
		subcategory = l3[0]
	}
	return &vendorFD.Ticket{
		Subject:     subject[0],
		Description: description[0],
		GroupId:     groupId,
		ResponderId: agentId,
		CustomFields: &vendorFD.CustomFields{
			CfProductCategory:        l1[0],
			CfProductCategoryDetails: productCategoryDetails,
			CfSubcategory:            subcategory,
		},
	}, nil
}

func (s *Service) convertToVnExecuteActionResult(_ context.Context, cxActionResult *cxChatbotWorkflowPb.ActionResult) (*vnChatbotWorkflowPb.ActionResult, error) {
	switch cxActionResult.GetResult().(type) {
	case *cxChatbotWorkflowPb.ActionResult_CreateTicketResult:
		return &vnChatbotWorkflowPb.ActionResult{
			Result: &vnChatbotWorkflowPb.ActionResult_CreateTicketResult{
				CreateTicketResult: &vnChatbotWorkflowPb.CreateTicketResult{
					Ticket: convertToVendorFreshdeskTicket(cxActionResult.GetCreateTicketResult().GetTicket()),
				},
			},
		}, nil
	default:
		// handling for actions which are not configured on backend
		return nil, errors.Wrap(chatbotHelper.InvalidActionErr, "action not configured")
	}
}

func convertToVendorFreshdeskTicket(ticket *cxTicketPb.Ticket) *vendorFD.Ticket {
	// TODO: For now return only Id
	return &vendorFD.Ticket{
		Id: ticket.GetId(),
	}
}

func (s *Service) parseTransactionTypeEnum(txnType string) (cxChatbotWorkflowPb.TransactionType, error) {
	// get enum string for given entity from config
	cxEnumStr, isKeyFound := s.conf.Chatbot.VnToCxTransactionType[strings.ToUpper(txnType)]
	if !isKeyFound {
		return cxChatbotWorkflowPb.TransactionType_TRANSACTION_TYPE_UNSPECIFIED, errors.New(fmt.Sprintf("vn request transaction type key not found in config map: %s", txnType))
	}
	// get enum equivalent from str
	val, isValidTxnType := cxChatbotWorkflowPb.TransactionType_value[strings.ToUpper(cxEnumStr)]
	if !isValidTxnType {
		return cxChatbotWorkflowPb.TransactionType_TRANSACTION_TYPE_UNSPECIFIED, errors.New(fmt.Sprintf("invalid cx enum str value for transaction type: %s", cxEnumStr))
	}
	return cxChatbotWorkflowPb.TransactionType(val), nil
}

func getVnChargeListData(cxData *cxChatbotWorkflowPb.ChargesListForActor) *vnChatbotWorkflowPb.WorkflowData {
	return &vnChatbotWorkflowPb.WorkflowData{
		Data: &vnChatbotWorkflowPb.WorkflowData_ChargeListForActor{
			ChargeListForActor: &vnChatbotWorkflowPb.ChargesListForActor{
				ChargesInfoList: convertToVnTxnList(cxData.GetChargesInfoList()),
			},
		},
	}
}

func getVnDisplayTxnReasonData(cxData *cxChatbotWorkflowPb.DisplayTxnReasonData) *vnChatbotWorkflowPb.WorkflowData {
	return &vnChatbotWorkflowPb.WorkflowData{
		Data: &vnChatbotWorkflowPb.WorkflowData_DisplayTxnReasonData{
			DisplayTxnReasonData: &vnChatbotWorkflowPb.DisplayTxnReasonData{
				ReasonDisplayString: cxData.GetReasonDisplayString(),
			},
		},
	}
}

func getVnFailedTxnsListData(cxData *cxChatbotWorkflowPb.FailedTxnsData) *vnChatbotWorkflowPb.WorkflowData {
	return &vnChatbotWorkflowPb.WorkflowData{
		Data: &vnChatbotWorkflowPb.WorkflowData_FailedTxnsData{
			FailedTxnsData: &vnChatbotWorkflowPb.FailedTxnsData{
				FailedTxnsList: convertToVnTxnList(cxData.GetFailedTxnsList()),
			},
		},
	}
}

func getVnFetchSalaryProgramRegistrationDetailsData(cxData *cxChatbotWorkflowPb.FetchSalaryProgramRegistrationDetailsData) *vnChatbotWorkflowPb.WorkflowData {

	return &vnChatbotWorkflowPb.WorkflowData{
		Data: &vnChatbotWorkflowPb.WorkflowData_FetchSalaryProgramRegistrationDetailsData{
			FetchSalaryProgramRegistrationDetailsData: &vnChatbotWorkflowPb.FetchSalaryProgramRegistrationDetailsData{
				HasUserCompletedEmploymentConfirmation: cxData.GetHasUserCompletedEmploymentConfirmation(),
				HasUserCompletedFullKyc:                cxData.GetHasUserCompletedFullKyc(),
			},
		},
	}

}

func getVnCheckSalaryProgramAmazonVoucherEligibilityData(cxData *cxChatbotWorkflowPb.CheckSalaryProgramAmazonVoucherEligibilityData) *vnChatbotWorkflowPb.WorkflowData {
	return &vnChatbotWorkflowPb.WorkflowData{
		Data: &vnChatbotWorkflowPb.WorkflowData_CheckSalaryProgramAmazonVoucherEligibilityData{
			CheckSalaryProgramAmazonVoucherEligibilityData: &vnChatbotWorkflowPb.CheckSalaryProgramAmazonVoucherEligibilityData{
				IsEligibleForVoucher1: cxData.GetIsEligibleForVoucher1(),
				IsEligibleForVoucher2: cxData.GetIsEligibleForVoucher2(),
				Message:               cxData.GetMessage(),
			},
		},
	}
}

func getVnFetchRewardOffersForUserData(cxData *cxChatbotWorkflowPb.FetchRewardOffersForUserData) *vnChatbotWorkflowPb.WorkflowData {
	var RewardOffersList []*vnChatbotWorkflowPb.RewardOffer
	for _, cxRewardOffer := range cxData.GetRewardOffers() {
		RewardOffersList = append(RewardOffersList, &vnChatbotWorkflowPb.RewardOffer{
			Id:            cxRewardOffer.GetId(),
			Title:         cxRewardOffer.GetTitle(),
			DisplayString: cxRewardOffer.GetDisplayString(),
			ComponentType: cxRewardOffer.GetComponentType(),
			TextField:     cxRewardOffer.GetTextField(),
		})
	}
	return &vnChatbotWorkflowPb.WorkflowData{
		Data: &vnChatbotWorkflowPb.WorkflowData_FetchRewardOffersForUserData{
			FetchRewardOffersForUserData: &vnChatbotWorkflowPb.FetchRewardOffersForUserData{
				RewardOffers: RewardOffersList,
			},
		},
	}
}

func getVnFetchRewardsEventDetailsData(cxData *cxChatbotWorkflowPb.FetchRewardsEventDetailsData) *vnChatbotWorkflowPb.WorkflowData {
	fetchRewardsEventDetailsData := &vnChatbotWorkflowPb.FetchRewardsEventDetailsData{}
	var transactionDetails []*vnChatbotWorkflowPb.Transaction
	var creditCardTransactionDetails []*vnChatbotWorkflowPb.Transaction
	var refereeDetails []*vnChatbotWorkflowPb.RefereeDetail

	switch cxData.GetEventDetails().(type) {
	case *cxChatbotWorkflowPb.FetchRewardsEventDetailsData_Transactions:
		for _, cxTransaction := range cxData.GetTransactions().GetTransactions() {
			transactionDetails = append(transactionDetails, &vnChatbotWorkflowPb.Transaction{Amount: cxTransaction.GetAmount(),
				Timestamp:     cxTransaction.GetTimestamp(),
				ExternalId:    cxTransaction.GetExternalId(),
				DisplayString: cxTransaction.GetDisplayString(),
				ComponentType: cxTransaction.GetComponentType(),
				Type:          cxTransaction.GetType(),
				Mode:          cxTransaction.GetMode(),
				Status:        cxTransaction.GetStatus(),
			})
		}
		fetchRewardsEventDetailsData.EventDetails = &vnChatbotWorkflowPb.FetchRewardsEventDetailsData_Transactions{
			Transactions: &vnChatbotWorkflowPb.Transactions{
				Transactions: transactionDetails,
			},
		}
	case *cxChatbotWorkflowPb.FetchRewardsEventDetailsData_CreditCardTransactions:
		for _, cxCCTransaction := range cxData.GetCreditCardTransactions().GetTransactions() {
			creditCardTransactionDetails = append(creditCardTransactionDetails, &vnChatbotWorkflowPb.Transaction{Amount: cxCCTransaction.GetAmount(),
				Timestamp:     cxCCTransaction.GetTimestamp(),
				ExternalId:    cxCCTransaction.GetExternalId(),
				DisplayString: cxCCTransaction.GetDisplayString(),
				ComponentType: cxCCTransaction.GetComponentType(),
				Type:          cxCCTransaction.GetType(),
				Mode:          cxCCTransaction.GetMode(),
				Status:        cxCCTransaction.GetStatus(),
			})
		}
		fetchRewardsEventDetailsData.EventDetails = &vnChatbotWorkflowPb.FetchRewardsEventDetailsData_CreditCardTransactions{
			CreditCardTransactions: &vnChatbotWorkflowPb.Transactions{
				Transactions: creditCardTransactionDetails,
			},
		}
	case *cxChatbotWorkflowPb.FetchRewardsEventDetailsData_RefereeDetails:
		for _, refereeDetail := range cxData.GetRefereeDetails().GetRefereeDetails() {
			var onboardingStatus vnChatbotWorkflowPb.RefereeOnbStage
			switch refereeDetail.GetOnboardingStatus() {
			case cxChatbotWorkflowPb.RefereeOnbStage_REFEREE_ONB_STAGE_UNSPECIFIED:
				onboardingStatus = vnChatbotWorkflowPb.RefereeOnbStage_REFEREE_ONB_STAGE_UNSPECIFIED
			case cxChatbotWorkflowPb.RefereeOnbStage_REFEREE_ONB_STAGE_INSTALLED:
				onboardingStatus = vnChatbotWorkflowPb.RefereeOnbStage_REFEREE_ONB_STAGE_INSTALLED
			case cxChatbotWorkflowPb.RefereeOnbStage_REFEREE_ONB_STAGE_ACCOUNT_CREATED:
				onboardingStatus = vnChatbotWorkflowPb.RefereeOnbStage_REFEREE_ONB_STAGE_ACCOUNT_CREATED
			case cxChatbotWorkflowPb.RefereeOnbStage_REFEREE_ONB_STAGE_REWARD_GRANTED:
				onboardingStatus = vnChatbotWorkflowPb.RefereeOnbStage_REFEREE_ONB_STAGE_REWARD_GRANTED

			}
			refereeDetails = append(refereeDetails, &vnChatbotWorkflowPb.RefereeDetail{Name: refereeDetail.GetName(),
				OnboardingStatus:  onboardingStatus,
				ActorId:           refereeDetail.GetActorId(),
				FiniteCode:        refereeDetail.GetFiniteCode(),
				FiniteCodeClaimId: refereeDetail.GetFiniteCodeClaimId(),
				DisplayString:     refereeDetail.GetDisplayString(),
				ComponentType:     refereeDetail.GetComponentType(),
				TextField:         refereeDetail.GetTextField(),
			})
		}
		fetchRewardsEventDetailsData.EventDetails = &vnChatbotWorkflowPb.FetchRewardsEventDetailsData_RefereeDetails{
			RefereeDetails: &vnChatbotWorkflowPb.RefereeDetails{
				RefereeDetails: refereeDetails,
			},
		}
	}
	fetchRewardsEventDetailsData.EventType = cxData.GetEventType()
	return &vnChatbotWorkflowPb.WorkflowData{
		Data: &vnChatbotWorkflowPb.WorkflowData_FetchRewardsEventDetailsData{
			FetchRewardsEventDetailsData: fetchRewardsEventDetailsData,
		},
	}
}
func getVnFetchRewardForEventData(cxData *cxChatbotWorkflowPb.FetchRewardForEventData) *vnChatbotWorkflowPb.WorkflowData {
	return &vnChatbotWorkflowPb.WorkflowData{
		Data: &vnChatbotWorkflowPb.WorkflowData_FetchRewardForEventData{
			FetchRewardForEventData: &vnChatbotWorkflowPb.FetchRewardForEventData{
				HasUserGotReward: cxData.GetHasUserGotReward(),
				TermsAndConditions: &vnChatbotWorkflowPb.TermsAndConditions{
					Tncs:  cxData.GetTermsAndConditions().GetTncs(),
					Steps: cxData.GetTermsAndConditions().GetSteps(),
				},
				Message: cxData.GetMessage(),
			},
		},
	}
}

func getVnBalanceRefreshData(cxData *cxChatbotWorkflowPb.BalanceRefreshData) *vnChatbotWorkflowPb.WorkflowData {
	return &vnChatbotWorkflowPb.WorkflowData{
		Data: &vnChatbotWorkflowPb.WorkflowData_BalanceRefreshData{
			BalanceRefreshData: &vnChatbotWorkflowPb.BalanceRefreshData{
				RefreshedBalance: cxData.GetRefreshedBalance(),
			},
		},
	}
}

func getVnPredefinedMessageTemplateData(cxData *cxChatbotWorkflowPb.PredefinedMessageTemplateData) *vnChatbotWorkflowPb.WorkflowData {
	return &vnChatbotWorkflowPb.WorkflowData{
		Data: &vnChatbotWorkflowPb.WorkflowData_PredefinedMessageTemplateData{
			PredefinedMessageTemplateData: &vnChatbotWorkflowPb.PredefinedMessageTemplateData{
				PredefinedMessageTemplateType: cxData.GetPredefinedMessageTemplateType(),
				PredefinedMessageText:         cxData.GetPredefinedMessageText(),
			},
		},
	}
}

func getVnFetchUserTransactionsData(cxData *cxChatbotWorkflowPb.FetchUserTransactionsData) *vnChatbotWorkflowPb.WorkflowData {
	var transactions []*vnChatbotWorkflowPb.Transaction
	for _, transaction := range cxData.GetTransactions().GetTransactions() {
		transactions = append(transactions, &vnChatbotWorkflowPb.Transaction{
			Amount:        transaction.GetAmount(),
			Timestamp:     transaction.GetTimestamp(),
			ExternalId:    transaction.GetExternalId(),
			DisplayString: transaction.GetDisplayString(),
			ComponentType: transaction.GetComponentType(),
			Type:          transaction.GetType(),
			Mode:          transaction.GetMode(),
			Status:        transaction.GetStatus(),
		})
	}

	return &vnChatbotWorkflowPb.WorkflowData{
		Data: &vnChatbotWorkflowPb.WorkflowData_FetchUserTransactionsData{
			FetchUserTransactionsData: &vnChatbotWorkflowPb.FetchUserTransactionsData{
				Transactions: &vnChatbotWorkflowPb.Transactions{
					Transactions: transactions,
				},
			},
		},
	}
}
