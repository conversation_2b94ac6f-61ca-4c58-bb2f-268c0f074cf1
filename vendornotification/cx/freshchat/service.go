package freshchat

import (
	"context"
	"time"

	"github.com/epifi/gamma/vendornotification/metrics"

	chatPb "github.com/epifi/gamma/api/cx/chat"
	chatConsumerPb "github.com/epifi/gamma/api/cx/chat/consumer"
	freshchatPb "github.com/epifi/gamma/api/vendornotification/cx/freshchat"
	redactorConf "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/cx/helper"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type Service struct {
	conf          *config.Config
	fcCallbackPub queue.Publisher
}

type FreshchatActionCallbackPublisher queue.Publisher

func NewService(conf *config.Config,
	fcCallbackPub FreshchatActionCallbackPublisher) *Service {
	return &Service{
		conf:          conf,
		fcCallbackPub: fcCallbackPub,
	}
}

var _ freshchatPb.FreshchatCallbackServer = &Service{}

const (
	FreshchatActionCallbackRequest = "FreshchatActionCallbackRequest"
)

func (s *Service) FreshchatActionCallback(ctx context.Context, req *freshchatPb.FreshchatActionCallbackRequest) (*empty.Empty, error) {
	if err := security.CheckWhiteList(
		ctx, s.conf.FreshchatWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor,
		s.conf.VpcCidrIPPrefix,
	); err != nil {
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, req, FreshchatActionCallbackRequest, req.GetActor().GetActorId(), redactorConf.Config)
	// Freshchat responses use time.RFC3339Nano time format
	actionTime, err := time.Parse(time.RFC3339Nano, req.GetActionTime())
	if err != nil {
		logger.Error(ctx, "error parsing action time string", zap.Error(err), zap.String("actionTime", req.GetActionTime()))
		// ignoring error as this is not critical, and we would still want to publish the remaining data to queue
	}
	actionType := helper.ActionStrToEnumMap[req.GetAction()]
	event := &chatConsumerPb.ProcessFreshchatEventRequest{
		FreshchatActorType: helper.ActorTypeStrToEnumMap[req.GetActor().GetActorType()],
		FreshchatActorId:   req.GetActor().GetActorId(),
		Action:             actionType,
		ActionTime:         timestamppb.New(actionTime),
	}
	err = validateActionAndPopulateEventData(event, req)
	if err != nil {
		logger.Error(ctx, "error validating freshchat action", zap.Error(err), zap.String("action", req.GetAction()))
		// Not publishing to queue because the consumer would anyways reject unhandled action types
		// Not returning error to avoid false alerts from freshchat for these unhandled action types
		metrics.RecordFreshchatActionCallback(actionType.String(), err.Error())
		return &empty.Empty{}, nil
	}
	sqsMsgId, err := s.fcCallbackPub.Publish(ctx, event)
	if err != nil {
		logger.Error(ctx, "error publishing sms callback event to queue", zap.Error(err))
		metrics.RecordFreshchatActionCallback(actionType.String(), err.Error())
		return nil, status.Errorf(codes.Internal, "internal server error")
	}
	logger.Info(ctx, "message published in queue successfully", zap.String(logger.QUEUE_MESSAGE_ID, sqsMsgId))
	metrics.RecordFreshchatActionCallback(actionType.String(), metrics.NoError)
	return &empty.Empty{}, nil
}

// validateActionAndPopulateEventData validates whether action type is recognized and load data
// the event data must be populated for cxFreshchat struct as per the type of the action
func validateActionAndPopulateEventData(event *chatConsumerPb.ProcessFreshchatEventRequest, req *freshchatPb.FreshchatActionCallbackRequest) error {
	switch event.GetAction() {
	case chatPb.FreshchatCallbackAction_FRESHCHAT_CALLBACK_ACTION_MESSAGE_CREATE:
		if req.GetData().GetMessage() == nil {
			return errors.New("Message data is nil for FRESHCHAT_CALLBACK_ACTION_MESSAGE_CREATE")
		}
		event.Data = &chatConsumerPb.ProcessFreshchatEventRequest_Message{
			Message: helper.VendorsToCxFcMessage(req.GetData().GetMessage()),
		}
	case chatPb.FreshchatCallbackAction_FRESHCHAT_CALLBACK_ACTION_CONVERSATION_ASSIGNMENT:
		if req.GetData().GetAssignment() == nil {
			return errors.New("Assignment data is nil for FRESHCHAT_CALLBACK_ACTION_CONVERSATION_ASSIGNMENT")
		}
		event.Data = &chatConsumerPb.ProcessFreshchatEventRequest_Assignment{
			Assignment: helper.VendorsToCxFcAssignment(req.GetData().GetAssignment()),
		}
	case chatPb.FreshchatCallbackAction_FRESHCHAT_CALLBACK_ACTION_CONVERSATION_RESOLUTION:
		if req.GetData().GetResolve() == nil {
			return errors.New("Resolution data is nil for FRESHCHAT_CALLBACK_ACTION_CONVERSATION_RESOLUTION")
		}
		event.Data = &chatConsumerPb.ProcessFreshchatEventRequest_Resolve{
			Resolve: helper.VendorsToCxFcResolve(req.GetData().GetResolve()),
		}
	// NOTE: conversation reopen case is not required for now and we are not handling in consumer
	// case chatPb.FreshchatCallbackAction_FRESHCHAT_CALLBACK_ACTION_CONVERSATION_REOPEN:
	//	if req.GetData().GetReopen() != nil {
	//		return errors.New("Resolution data is nil for FRESHCHAT_CALLBACK_ACTION_CONVERSATION_RESOLUTION")
	//	}
	//	event.Data = &chatConsumerPb.ProcessFreshchatEventRequest_Reopen{
	//		Reopen: helper.VendorsToCxFcReopen(req.GetData().GetReopen()),
	//	}
	default:
		return errors.New("unspecified or unknown action type")
	}
	return nil
}
