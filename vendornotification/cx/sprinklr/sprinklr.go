package sprinklr

import (
	"context"
	"strings"

	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"

	"github.com/pkg/errors"

	sprinklrPb "github.com/epifi/gamma/api/cx/sprinklr"
	"github.com/epifi/gamma/api/vendornotification/cx/sprinklr"
	redactorConf "github.com/epifi/gamma/api/vendors/redactor"
	cxPkg "github.com/epifi/gamma/pkg/cx"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"
)

type EventsHandlingService struct {
	sprinklrEventProcessingClient sprinklrPb.SprinklrClient
	conf                          *config.Config
}

func NewEventsHandlingService(sprinklrEventProcessingClient sprinklrPb.SprinklrClient, conf *config.Config) *EventsHandlingService {
	return &EventsHandlingService{
		sprinklrEventProcessingClient: sprinklrEventProcessingClient,
		conf:                          conf,
	}
}

var (
	_                 sprinklr.SprinklrEventHandlingServer = &EventsHandlingService{}
	ErrApiKeyNotFound                                      = errors.New("Api Key not found in header")
	ErrApiKeyInvalid                                       = errors.New("Api key is invalid")
)

const (
	ProcessSprinklrEventRequest = "ProcessSprinklrEventRequest"
)

func (e *EventsHandlingService) ValidateApiKey(ctx context.Context) error {
	md, isMetaDataAppendedInContext := metadata.FromIncomingContext(ctx)
	if !isMetaDataAppendedInContext {
		return errors.New("no metadata found in context")
	}
	authorization, found := md[strings.ToLower(cxPkg.SprinklrAuthorizationHeaderKey)]
	if !found {
		return ErrApiKeyNotFound
	}
	authParts := strings.Split(authorization[0], " ")
	if len(authParts) != 2 {
		return errors.Wrap(ErrApiKeyInvalid, "api key format invalid")
	}
	if authParts[0] != cxPkg.SprinklrAuthApiKeyPrefix {
		return errors.Wrap(ErrApiKeyInvalid, "api key prefix doesn't match")
	}
	apiKey, ok := e.conf.Secrets.Ids[cxPkg.SprinklrAuthApiKey]
	if !ok {
		return errors.New("could not read sprinklr API key from config")
	}
	if authParts[1] != apiKey {
		return errors.Wrap(ErrApiKeyInvalid, "api key comparison failed")
	}
	return nil
}

func (e *EventsHandlingService) ProcessSprinklrEvent(ctx context.Context, req *sprinklr.ProcessSprinklrEventRequest) (*emptyPb.Empty, error) {
	redactor.LogCallbackRequestData(ctx, req, ProcessSprinklrEventRequest, req.GetId(), redactorConf.Config)
	if err := security.CheckWhiteList(ctx, e.conf.SprinklrWhitelist, e.conf.NumberOfHopsThatAddXForwardedFor, e.conf.VpcCidrIPPrefix); err != nil {
		logger.Error(ctx, "error while checking IP whitelisting", zap.Error(err))
		return nil, err
	}
	if err := e.ValidateApiKey(ctx); err != nil {
		logger.Error(ctx, "error while validating sprinklr api key", zap.Error(err))
		return &emptyPb.Empty{}, nil
	}
	// convert the received event to cx proto and call cx service for further processing
	resp, err := e.sprinklrEventProcessingClient.ProcessTicketFromSprinklrEvent(ctx, &sprinklrPb.ProcessTicketFromSprinklrEventRequest{
		SprinklrPayload: sprinklrPb.ConvertToSprinklrPayload(ctx, req),
	})
	if grpcErr := epifigrpc.RPCError(resp, err); grpcErr != nil {
		logger.Error(ctx, "Error calling cx service in vendor notification: ", zap.Error(grpcErr))
		return &emptyPb.Empty{}, nil
	}
	return &emptyPb.Empty{}, nil
}
