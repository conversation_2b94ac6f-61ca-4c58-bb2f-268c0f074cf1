package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

type AccountFreezeBotFetchMetadataRequestReceived struct {
	ActorId     string
	SessionId   string
	ProspectId  string
	Timestamp   time.Time
	EventId     string
	EventType   string
	EventName   string
	ServiceName string
}

func NewAccountFreezeBotFetchMetadataRequestReceived(actorId string) *AccountFreezeBotFetchMetadataRequestReceived {
	return &AccountFreezeBotFetchMetadataRequestReceived{
		ActorId:     actorId,
		Timestamp:   time.Now(),
		ProspectId:  uuid.New().String(),
		EventId:     uuid.New().String(),
		EventType:   events.EventTrack,
		EventName:   AccountFreezeBotFetchMetadataRequestReceivedEventName,
		ServiceName: CxServiceName,
	}
}

func (s *AccountFreezeBotFetchMetadataRequestReceived) GetEventType() string {
	return s.EventType
}

func (s *AccountFreezeBotFetchMetadataRequestReceived) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(s, properties)
	return properties
}

func (s *AccountFreezeBotFetchMetadataRequestReceived) GetEventTraits() map[string]interface{} {
	return nil
}

func (s *AccountFreezeBotFetchMetadataRequestReceived) GetEventId() string {
	return s.EventId
}

func (s *AccountFreezeBotFetchMetadataRequestReceived) GetUserId() string {
	return s.ActorId
}

func (s *AccountFreezeBotFetchMetadataRequestReceived) GetProspectId() string {
	return s.ProspectId
}

func (s *AccountFreezeBotFetchMetadataRequestReceived) GetEventName() string {
	return s.EventName
}
