package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

type AccountFreezeBotInterpretStatusRequestReceived struct {
	ActorId     string
	SessionId   string
	ProspectId  string
	Timestamp   time.Time
	EventId     string
	EventType   string
	EventName   string
	ServiceName string

	// custom properties
	FreezeReason string
	FreezeStatus string
}

func NewAccountFreezeBotInterpretStatusRequestReceived(actorId, freezeReason, freezeStatus string) *AccountFreezeBotInterpretStatusRequestReceived {
	return &AccountFreezeBotInterpretStatusRequestReceived{
		ActorId:      actorId,
		Timestamp:    time.Now(),
		ProspectId:   uuid.New().String(),
		EventId:      uuid.New().String(),
		EventType:    events.EventTrack,
		EventName:    AccountFreezeBotInterpretStatusRequestReceivedEventName,
		ServiceName:  CxServiceName,
		FreezeReason: freezeReason,
		FreezeStatus: freezeStatus,
	}
}

func (s *AccountFreezeBotInterpretStatusRequestReceived) GetEventType() string {
	return s.EventType
}

func (s *AccountFreezeBotInterpretStatusRequestReceived) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(s, properties)
	return properties
}

func (s *AccountFreezeBotInterpretStatusRequestReceived) GetEventTraits() map[string]interface{} {
	return nil
}

func (s *AccountFreezeBotInterpretStatusRequestReceived) GetEventId() string {
	return s.EventId
}

func (s *AccountFreezeBotInterpretStatusRequestReceived) GetUserId() string {
	return s.ActorId
}

func (s *AccountFreezeBotInterpretStatusRequestReceived) GetProspectId() string {
	return s.ProspectId
}

func (s *AccountFreezeBotInterpretStatusRequestReceived) GetEventName() string {
	return s.EventName
}
