package ozonetel

import (
	"context"
	"fmt"
	"net/http"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/vendornotification/cx/ozonetel"
	caPkg "github.com/epifi/gamma/pkg/connectedaccount"
)

var (
	initiateIvrInvalidArgument = &ozonetel.InitiateIvrResponse{
		Status: http.StatusBadRequest,
	}
	initiateIvrInternal = &ozonetel.InitiateIvrResponse{
		Status: http.StatusInternalServerError,
	}
	processIvrUserInputInvalidArgument = &ozonetel.ProcessIvrUserInputResponse{
		Status: http.StatusBadRequest,
	}
	processIvrUserInputInternal = &ozonetel.ProcessIvrUserInputResponse{
		Status: http.StatusInternalServerError,
	}
	processIvrUserInputNotFound = &ozonetel.ProcessIvrUserInputResponse{
		Status: http.StatusNotFound,
	}
	getIvrStateInvalidArgument = &ozonetel.GetIvrStateResponse{
		Status: http.StatusBadRequest,
	}
	getIvrStateNotFound = &ozonetel.GetIvrStateResponse{
		Status: http.StatusNotFound,
	}
	getIvrStateInternal = &ozonetel.GetIvrStateResponse{
		Status: http.StatusInternalServerError,
	}
)

func (c *CallRoutingService) getInitiateIvrResponse(
	ctx context.Context, httpStatusCode int, resp *ozonetel.InitiateIvrResponse,
) (*ozonetel.InitiateIvrResponse, error) {
	if headerErr := grpc.SendHeader(
		ctx, metadata.New(
			map[string]string{
				caPkg.HttpStatusCodeGrpcHeader: fmt.Sprintf("%v", httpStatusCode),
			},
		),
	); headerErr != nil {
		logger.Error(ctx, "error setting grpc headers", zap.Error(headerErr))
		return nil, status.Errorf(codes.Internal, "error setting grpc headers")
	}
	return resp, nil
}

func (c *CallRoutingService) getProcessIvrUserInputResponse(
	ctx context.Context, httpStatusCode int, resp *ozonetel.ProcessIvrUserInputResponse,
) (*ozonetel.ProcessIvrUserInputResponse, error) {
	if headerErr := grpc.SendHeader(
		ctx, metadata.New(
			map[string]string{
				caPkg.HttpStatusCodeGrpcHeader: fmt.Sprintf("%v", httpStatusCode),
			},
		),
	); headerErr != nil {
		logger.Error(ctx, "error setting grpc headers", zap.Error(headerErr))
		return nil, status.Errorf(codes.Internal, "error setting grpc headers")
	}
	return resp, nil
}

func (c *CallRoutingService) getGetIvrStateResponse(
	ctx context.Context, httpStatusCode int, resp *ozonetel.GetIvrStateResponse,
) (*ozonetel.GetIvrStateResponse, error) {
	if headerErr := grpc.SendHeader(
		ctx, metadata.New(
			map[string]string{
				caPkg.HttpStatusCodeGrpcHeader: fmt.Sprintf("%v", httpStatusCode),
			},
		),
	); headerErr != nil {
		logger.Error(ctx, "error setting grpc headers", zap.Error(headerErr))
		return nil, status.Errorf(codes.Internal, "error setting grpc headers")
	}
	return resp, nil
}
