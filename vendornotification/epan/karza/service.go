package karza

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"strings"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/names"
	"github.com/epifi/be-common/pkg/queue"

	celestialPb "github.com/epifi/be-common/api/celestial"
	celestialConsumerPb "github.com/epifi/be-common/api/celestial/consumer"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	panNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/pan"

	panPb "github.com/epifi/gamma/api/pan"
	"github.com/epifi/gamma/api/pan/epan"
	panPayloadPb "github.com/epifi/gamma/api/pan/payload"
	types "github.com/epifi/gamma/api/typesv2"
	vnPb "github.com/epifi/gamma/api/vendornotification/epan/karza"
	karzaPb "github.com/epifi/gamma/api/vendors/karza"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/pkg/image"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"
)

type EpanCallbackS3Client s3.S3Client

type Service struct {
	signalWorkflowPublisher queue.Publisher
	conf                    *config.Config
	panClient               panPb.PanClient
	celestialClient         celestialPb.CelestialClient
	s3Client                s3.S3Client
}

type SignalWorkflowPublisher queue.Publisher

func NewService(conf *config.Config, signalWorkflowPublisher SignalWorkflowPublisher, panClient panPb.PanClient,
	celestialClient celestialPb.CelestialClient, s3Client EpanCallbackS3Client) *Service {
	return &Service{
		conf:                    conf,
		signalWorkflowPublisher: signalWorkflowPublisher,
		panClient:               panClient,
		celestialClient:         celestialClient,
		s3Client:                s3Client,
	}
}

const (
	ProcessEPANEventCallback = "ProcessEPANEventCallback"
)

var _ vnPb.EPANServer = &Service{}

func (s *Service) ProcessEPANEventCallback(ctx context.Context, resp *karzaPb.ProcessEPANEventCallbackResponse) (*empty.Empty, error) {
	logger.Info(ctx, fmt.Sprintf("received the callback for epan data %v", resp.GetClientReqId()))
	logProcessEPANEventCallback(ctx, resp)
	logEmptyFile(ctx, resp)
	if err := security.CheckWhiteList(ctx, s.conf.KarzaEPANWhitelist,
		s.conf.NumberOfHopsThatAddXForwardedFor, s.conf.VpcCidrIPPrefix,
	); err != nil {
		logger.Error(ctx, "error while checking whitelisting of epan api", zap.Error(err))
		// failing current attempt
		_ = s.sendSignal(ctx, resp.GetClientReqId(), string(panNs.EPANDataSyncSignal), epan.EPANState_EPAN_STATE_FAILED, nil)
		return nil, err
	}
	epanAttemptRes, epanAttemptErr := s.panClient.GetEPANAttempt(ctx, &panPb.GetEPANAttemptRequest{
		ClientReqId: resp.GetClientReqId(),
	})
	if te := epifigrpc.RPCError(epanAttemptRes, epanAttemptErr); te != nil {
		logger.Error(ctx, "error getting epan attempt by client req id", zap.Error(te))
		// failing current attempt
		_ = s.sendSignal(ctx, resp.GetClientReqId(), string(panNs.EPANDataSyncSignal), epan.EPANState_EPAN_STATE_FAILED, nil)
		return &empty.Empty{}, nil
	}
	payloadBytes, payloadErr := protojson.Marshal(&panPayloadPb.EpanInfoSyncPayload{
		ActorId: epanAttemptRes.GetEPanAttempt().GetActorId(),
	})
	if payloadErr != nil {
		logger.Error(ctx, "error in marshalling the payload", zap.Error(payloadErr))
		// failing current attempt
		_ = s.sendSignal(ctx, resp.GetClientReqId(), string(panNs.EPANDataSyncSignal), epan.EPANState_EPAN_STATE_FAILED, nil)
		return &empty.Empty{}, nil
	}

	initiateResp, initiateErr := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: epanAttemptRes.GetEPanAttempt().GetActorId(),
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(panNs.EPANInfoSync),
			ClientReqId: &workflowPb.ClientReqId{
				Id:     resp.GetClientReqId(),
				Client: workflowPb.Client_EPAN,
			},
			Ownership:        commontypes.Ownership_EPIFI_TECH,
			Payload:          payloadBytes,
			QualityOfService: celestialPb.QoS_BEST_EFFORT,
		},
	})
	if te := epifigrpc.RPCError(initiateResp, initiateErr); te != nil && !rpc.StatusFromError(te).IsAlreadyExists() {
		logger.Error(ctx, "failed to initiate workflow", zap.Error(te))
		// failing current attempt
		_ = s.sendSignal(ctx, resp.GetClientReqId(), string(panNs.EPANDataSyncSignal), epan.EPANState_EPAN_STATE_FAILED, nil)
		return &empty.Empty{}, nil
	}

	if !resp.GetSuccess() {
		logger.Error(ctx, "epan download failed")
		_ = s.sendSignal(ctx, resp.GetClientReqId(), string(panNs.EPANDataSyncSignal), epan.EPANState_EPAN_STATE_FAILED, nil)
		return &empty.Empty{}, nil
	}
	epanData, epanErr := parseEPanData(ctx, resp.GetCallbackData())
	if epanErr != nil {
		logger.Error(ctx, "error while converting to epan details", zap.Error(epanErr))
		_ = s.sendSignal(ctx, resp.GetClientReqId(), string(panNs.EPANDataSyncSignal), epan.EPANState_EPAN_STATE_FAILED, nil)
		return &empty.Empty{}, nil
	}
	uploadErr := s.uploadImagesToS3bucket(ctx, epanData, resp.GetClientReqId(), epanAttemptRes.GetEPanAttempt().GetActorId())
	if uploadErr != nil {
		logger.Error(ctx, "error while uploading images", zap.Error(uploadErr))
		_ = s.sendSignal(ctx, resp.GetClientReqId(), string(panNs.EPANDataSyncSignal), epan.EPANState_EPAN_STATE_FAILED, nil)
		return &empty.Empty{}, nil
	}
	_ = s.sendSignal(ctx, resp.GetClientReqId(), string(panNs.EPANDataSyncSignal), epan.EPANState_EPAN_STATE_SUCCESS, epanData)
	logger.Info(ctx, "signal is sent to workflow with epan data")
	return &empty.Empty{}, nil
}

func logEmptyFile(ctx context.Context, resp *karzaPb.ProcessEPANEventCallbackResponse) {
	files := resp.GetCallbackData().GetParsedData()
	if strings.TrimSpace(files.GetPhoto()) == "" {
		logger.Error(ctx, "photo image is empty in callback data")
	}
	if strings.TrimSpace(files.GetSignatureImage()) == "" {
		logger.Error(ctx, "signature image is empty in callback data")
	}
	if strings.TrimSpace(resp.GetCallbackData().GetPdf().GetPdfContent()) == "" {
		logger.Error(ctx, "pdf content is empty in callback data")
	}
	if strings.TrimSpace(resp.GetCallbackData().GetXml().GetXmlContent()) == "" {
		logger.Error(ctx, "xml content is empty in callback data")
	}
}

// We set the Base64 fields to nil because the response was not being logged in Kibana due to the large message size.
func logProcessEPANEventCallback(ctx context.Context, resp *karzaPb.ProcessEPANEventCallbackResponse) {
	b, err := protojson.Marshal(resp)
	tempResp := &karzaPb.ProcessEPANEventCallbackResponse{}
	err = protojson.Unmarshal(b, tempResp)
	if err != nil {
		logger.Error(ctx, "logging the error", zap.Error(err))
	}
	tempResp.GetCallbackData().GetParsedData().SignatureImage = ""
	tempResp.GetCallbackData().GetParsedData().Photo = ""
	tempResp.GetCallbackData().Xml = nil
	tempResp.GetCallbackData().Pdf = nil
	redactor.LogCallbackRequestData(ctx, tempResp, ProcessEPANEventCallback, resp.GetClientReqId(), vendorsRedactor.Config)
}

func parseEPanData(ctx context.Context, data *karzaPb.CallbackData) (*epan.EPANData, error) {
	mobileNo, err := parseMobileNumber(data.GetMobile())
	if err != nil {
		return nil, err
	}
	photoUrl, err := parseFile(ctx, data.GetParsedData().GetPhoto())
	if err != nil {
		return nil, err
	}
	// ignoring err since we can forward dummy file also
	signatureUrl, _ := parseFile(ctx, data.GetParsedData().GetSignatureImage())
	epanData := &epan.EPANData{
		PanNumber:         data.GetParsedData().GetPan(),
		Name:              names.ParseStringV2(data.GetParsedData().GetName()),
		FatherName:        names.ParseStringV2(data.GetParsedData().GetFatherName()),
		Dob:               datetime.DateFromString(data.GetParsedData().GetDob()),
		Mobile:            mobileNo,
		Email:             data.GetEmail(),
		Gender:            types.ParseGender(data.GetParsedData().GetGender()),
		PhotoUrl:          photoUrl,
		SignatureImageUrl: signatureUrl,
		XmlContentUrl:     data.GetXml().GetXmlContent(),
		PdfContentUrl:     data.GetPdf().GetPdfContent(),
	}
	return epanData, nil
}

func parseMobileNumber(number string) (*commontypes.PhoneNumber, error) {
	num, err := commontypes.ParsePhoneNumber(number)
	if err != nil {
		logger.InfoNoCtx("error while parsing mobile number from epan data", zap.Error(err))
		return nil, nil
	}
	if num.GetCountryCode() == 0 {
		num.CountryCode = 91
	}
	return num, nil
}

// TODO (Rishu Sahu) : move the code pkg after verifying
func parseFile(ctx context.Context, data string) (*commontypes.Image, error) {
	photo, errPhoto := image.EnsureImageInBase64(ctx, &commontypes.Image{
		ImageType:       commontypes.ImageType_JPEG,
		ImageDataBase64: data,
	})
	if errPhoto != nil {
		logger.ErrorNoCtx(fmt.Sprintf("Failed to ensure image in base64: %v", errPhoto))
		return nil, fmt.Errorf("unable to parse file")
	}
	return photo, nil
}

func (s *Service) sendSignal(ctx context.Context, clientReqId string, signalName string, epanState epan.EPANState, epanData *epan.EPANData) error {
	payload := &panPayloadPb.EpanDataSyncSignalPayload{
		EPanState: epanState,
		EPanData:  epanData,
	}
	payloadByte, err := protojson.Marshal(payload)
	if err != nil {
		logger.Error(ctx, "error marshalling signal payload", zap.Error(err))
		return err
	}
	_, sigErr := s.signalWorkflowPublisher.Publish(ctx, &celestialConsumerPb.SignalWorkflowRequest{
		Identifier: &celestialConsumerPb.SignalWorkflowRequest_ClientReqId{
			ClientReqId: &celestialPb.ClientReqId{
				Id:     clientReqId,
				Client: workflowPb.Client_EPAN,
			},
		},
		Payload:   payloadByte,
		SignalId:  signalName,
		Ownership: commontypes.Ownership_EPIFI_TECH,
	})
	if sigErr != nil {
		logger.Error(ctx, "error in sending signal to epan workflow", zap.String(logger.CLIENT_REQUEST_ID, clientReqId), zap.Error(sigErr))
		return errors.Wrap(err, "error sending epan callback status signal to workflow")
	}
	return nil
}
