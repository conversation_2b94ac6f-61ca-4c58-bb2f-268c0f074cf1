package karza

import (
	"context"
	"encoding/base64"
	"fmt"
	"path/filepath"
	"strings"

	s3types "github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/pan/epan"
	img "github.com/epifi/gamma/pkg/image"
)

func (s *Service) uploadImagesToS3bucket(ctx context.Context, epanData *epan.EPANData, clientReqId, actorId string) error {
	const (
		imagePathPrefix = "epan_jpeg"
		imageFileExt    = ".jpeg"
		pdfPathPrefix   = "epan_pdf"
		pdfFileExt      = ".pdf"
		xmlPathPrefix   = "epan_xml"
		xmlFileExt      = ".xml"
	)
	chErrGroup, gCtx := errgroup.WithContext(ctx)
	gCtx = epificontext.CtxWithActorId(ctx, actorId)
	// assuming we have all the images as we already checked in vendor notification while parsing
	chErrGroup.Go(func() error {
		// upload image base64 encoded string
		filePath, _, err := s.uploadBase64ImageToS3(gCtx, epanData.GetPhotoUrl().GetImageDataBase64(), clientReqId, imagePathPrefix, imageFileExt)
		if err != nil {
			return err
		}
		epanData.GetPhotoUrl().ImageUrl = filePath
		epanData.GetPhotoUrl().ImageDataBase64 = ""
		return nil
	})

	chErrGroup.Go(func() error {
		if epanData.GetSignatureImageUrl().GetImageDataBase64() == "" {
			return nil
		}
		// upload signature64 encoded string
		filePath, _, err := s.uploadBase64ImageToS3(ctx, epanData.GetSignatureImageUrl().GetImageDataBase64(), clientReqId, imagePathPrefix, imageFileExt)
		if err != nil {
			return err
		}
		epanData.GetSignatureImageUrl().ImageUrl = filePath
		// we won't be storing base 64 data as it will consume resource in db
		epanData.GetSignatureImageUrl().ImageDataBase64 = ""
		return nil
	})

	chErrGroup.Go(func() error {
		// upload xml content encoded string
		filePath, _, err := s.uploadBase64ImageToS3(ctx, epanData.XmlContentUrl, clientReqId, xmlPathPrefix, xmlFileExt)
		if err != nil {
			return err
		}
		epanData.XmlContentUrl = filePath
		return nil
	})

	chErrGroup.Go(func() error {
		// upload pdf content encoded string
		filePath, _, err := s.uploadBase64ImageToS3(ctx, epanData.PdfContentUrl, clientReqId, pdfPathPrefix, pdfFileExt)
		if err != nil {
			return err
		}
		epanData.PdfContentUrl = filePath
		return nil
	})
	errGroup := chErrGroup.Wait()
	if errGroup != nil {
		logger.Error(ctx, "error while uploading images to s3 bucket")
		return fmt.Errorf("error while uploading images to s3 bucket")
	}
	return nil
}

func (s *Service) uploadBase64ImageToS3(ctx context.Context, imageBase64 string, clientReqId, pathPrefix, fileExt string) (string, string, error) {
	if strings.TrimSpace(imageBase64) == "" {
		logger.Info(ctx, "received empty image base 64 data")
		return "", "", fmt.Errorf("received empty image base 64 data")
	}
	randSuffix := clientReqId + "/" + uuid.New().String()
	filePath := filepath.Join(pathPrefix, randSuffix+fileExt)
	logger.Info(ctx, fmt.Sprintf("writing to bucket... %v", s.conf.AWS.S3.BucketNames["panbucketname"]))

	// decoding image before uploading
	image, err := base64.StdEncoding.DecodeString(imageBase64)
	if err != nil {
		logger.Error(ctx, "failed to decode image base64 string, ", zap.Error(err))
		return "", "", err
	}

	if err = s.s3Client.Write(ctx, filePath, image, string(s3types.ObjectCannedACLBucketOwnerFullControl)); err != nil {
		logger.Error(ctx, "failed to store image in S3", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		return "", "", err
	}

	httpUrl := img.GetHttpUrl(s.conf.AWS.S3.BucketNames["panbucketname"], s.conf.AWS.Region, filePath)
	logger.Debug(ctx, fmt.Sprintf("BucketName: %v and Region name: %v", s.conf.AWS.S3.BucketNames["Panbucketname"], s.conf.AWS.Region))
	return filePath, httpUrl, err
}
