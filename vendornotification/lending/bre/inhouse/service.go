package inhouse

import (
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	structPb "google.golang.org/protobuf/types/known/structpb"

	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	credit_report "github.com/epifi/gamma/api/creditreportv2"
	pb "github.com/epifi/gamma/api/vendornotification/lending/bre/inhouse"
	"github.com/epifi/gamma/featurestore"
	fsModel "github.com/epifi/gamma/featurestore/model"
	"github.com/epifi/gamma/pkg/creditreport"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/security"
)

const (
	workflow                    = "InhouseBre"
	CreditReportFeatureV3Prefix = "CreditReportsLendingRawV3"
)

type Service struct {
	pb.UnimplementedBreServer
	featureStoreFactory       featurestore.IFactory
	conf                      *config.Config
	creditReportManagerClient credit_report.CreditReportManagerClient
}

func NewService(featureStoreFactory featurestore.IFactory, conf *config.Config, creditReportManagerClient credit_report.CreditReportManagerClient) *Service {
	return &Service{
		featureStoreFactory:       featureStoreFactory,
		conf:                      conf,
		creditReportManagerClient: creditReportManagerClient,
	}
}

func (s *Service) GetFeaturesData(ctx context.Context, req *pb.GetFeaturesDataRequest) (*pb.GetFeaturesDataResponse, error) {
	var (
		res = &pb.GetFeaturesDataResponse{}
	)
	logger.Info(ctx, "received request for GetFeaturesData")

	err := s.whitelistNotifications(ctx)
	if err != nil {
		logger.Error(ctx, "error in whitelisting get features data request", zap.Error(err))
		return nil, err
	}

	identifierList, identifierTypeList, err := s.getIdentifiersList(ctx, req.GetRequestIdentifiersList(), req.GetFeatureNameList())
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid request sent for fetching features data")
	}

	featureStoreClient, err := s.featureStoreFactory.GetFeatureStoreClient(vendorgateway.Vendor_FENNEL_FEATURE_STORE)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "error in getting feature store client")
	}

	featuresDataRes, err := featureStoreClient.ExtractFeature(ctx, &fsModel.ExtractFeatureRequest{
		FeatureNameList:    req.GetFeatureNameList(),
		IdentifierList:     identifierList,
		IdentifierTypeList: identifierTypeList,
		Workflow:           workflow,
	})
	if err != nil {
		logger.Error(ctx, "error in extracting features data", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "error in extracting features data")
	}
	for _, featuresData := range featuresDataRes.FeaturesSetList {
		res.FeaturesResponseDataList = append(res.FeaturesResponseDataList, &pb.FeaturesResponseData{
			Identifiers:     getResponseIdentifier(featuresData.Identifier),
			FeatureValueMap: getResponseFeatureValueMap(featuresData.FeatureList),
		})
	}
	return res, nil
}

func (s *Service) getIdentifiersList(ctx context.Context, reqIdentifierList []*pb.RequestIdentifiers, featureNameList []string) ([]fsModel.Identifier, []fsModel.IdentifierType, error) {
	var (
		identifierList      []fsModel.Identifier
		identifierTypeList  []fsModel.IdentifierType
		creditReportDataStr string
		err                 error
		actorId             = reqIdentifierList[0].GetIdentifiers().GetActorId()
	)
	if len(reqIdentifierList) == 0 {
		return nil, nil, fmt.Errorf("empty request sent for fetching data")
	}

	if len(featureNameList) > 0 && strings.Contains(featureNameList[0], CreditReportFeatureV3Prefix) {
		creditReportDataStr, err = s.getCreditReportRawString(ctx, actorId)
		if err != nil {
			return nil, nil, err
		}
		identifierTypeList = append(identifierTypeList, fsModel.CREDIT_REPORT_DATA_RAW)
		identifierList = append(identifierList, fsModel.Identifier{
			CreditReportDataRaw: creditReportDataStr,
		})
		return identifierList, identifierTypeList, nil
	}
	if actorId != "" {
		identifierTypeList = append(identifierTypeList, fsModel.ACTOR_ID)
	}
	if reqIdentifierList[0].GetIdentifiers().GetAccountId() != "" {
		identifierTypeList = append(identifierTypeList, fsModel.ACCOUNT_ID)
	}
	if reqIdentifierList[0].GetIdentifiers().GetModelName() != "" {
		identifierTypeList = append(identifierTypeList, fsModel.MODEL_NAME)
	}

	for _, reqIdentifier := range reqIdentifierList {
		identifierList = append(identifierList, fsModel.Identifier{
			ActorId:             reqIdentifier.GetIdentifiers().GetActorId(),
			AccountId:           reqIdentifier.GetIdentifiers().GetAccountId(),
			ModelName:           reqIdentifier.GetIdentifiers().GetModelName(),
			CreditReportDataRaw: creditReportDataStr,
		})
	}
	return identifierList, identifierTypeList, nil
}

func (s *Service) getCreditReportRawString(ctx context.Context, actorId string) (string, error) {
	crResp, err := s.creditReportManagerClient.GetCreditReport(ctx, &credit_report.GetCreditReportRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(crResp, err); grpcErr != nil {
		logger.Error(ctx, "error in fetching credit report", zap.Error(grpcErr))
		return "", grpcErr
	}

	return creditreport.StringifyRawReport(ctx, crResp.GetCreditReport().GetCreditReportDataRaw()), nil
}

func getResponseIdentifier(identifier fsModel.Identifier) *pb.Identifiers {
	return &pb.Identifiers{
		ActorId:   identifier.ActorId,
		AccountId: identifier.AccountId,
		ModelName: identifier.ModelName,
	}
}

func getResponseFeatureValueMap(featureList []fsModel.Feature) map[string]*structPb.Value {
	featureValueMap := make(map[string]*structPb.Value)
	for _, feature := range featureList {
		featureValueMap[feature.Name] = feature.Value
	}
	return featureValueMap
}

func (s *Service) whitelistNotifications(ctx context.Context) error {
	if err := security.CheckWhiteList(
		ctx, s.conf.IrisWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor,
		s.conf.VpcCidrIPPrefix,
	); err != nil {
		return err
	}
	return nil
}
