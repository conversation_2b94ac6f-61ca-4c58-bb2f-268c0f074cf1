// Provides clients that can talk to the vendor notification server over https.
// Used by simulator for call backs and notifications

package client

import (
	"crypto/tls"
	"net/http"
	"time"

	"github.com/epifi/be-common/pkg/cfg"
)

// NewHttpClient creates a http client for the vendor notification server
func NewHttpClient() *http.Client {
	if cfg.IsTestTenantEnabled() {
		// since the client will connect to localhost http port in TEST_TENANT setup, return simple client
		return &http.Client{}
	}
	tr := &http.Transport{
		MaxIdleConns:    10,
		IdleConnTimeout: 30 * time.Second,
		TLSClientConfig: &tls.Config{MinVersion: tls.VersionTLS12},
	}
	return &http.Client{
		Transport: tr,
		Timeout:   15 * time.Second,
	}
}
