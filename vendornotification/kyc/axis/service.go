package axis

import (
	"context"
	"fmt"

	"google.golang.org/protobuf/types/known/emptypb"

	"github.com/epifi/gamma/api/vendors/axis"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/vendornotification/config"
)

type Service struct {
	config *config.Config
}

func NewService(conf *config.Config) *Service {
	return &Service{config: conf}
}

func (s *Service) ProcessEkycCallBack(ctx context.Context, req *axis.EkycCallBackResponse) (*emptypb.Empty, error) {
	logger.Info(ctx, fmt.Sprintf("Received ekyc callback response : %s",
		req.String()))

	return &emptypb.Empty{}, nil
}
