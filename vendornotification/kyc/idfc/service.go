package idfc

import (
	"context"
	"time"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	kycV2Pb "github.com/epifi/gamma/api/kyc/v2/kyc"
	vnIdfcPb "github.com/epifi/gamma/api/vendornotification/kyc/idfc"
	vendorIdfcVkycPb "github.com/epifi/gamma/api/vendors/idfc/vkyc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
)

type Service struct {
	vnIdfcPb.UnimplementedIdfcServer
	kycStatusPublisher queue.Publisher
}

func NewService(kycStatusPublisher KycStatusUpdatePublisher) *Service {
	return &Service{
		kycStatusPublisher: kycStatusPublisher,
	}
}

type KycStatusUpdatePublisher queue.Publisher

func (s *Service) VKYCProfileStatusCallback(ctx context.Context, req *vendorIdfcVkycPb.VKYCProfileStatusCallbackRequest) (*vendorIdfcVkycPb.VKYCProfileStatusCallbackResponse, error) {
	createdAtTime, err := time.Parse(time.RFC3339, req.GetVkycProfileStatusUpdateCallback().GetCreatedAt())
	if err != nil {
		logger.Info(ctx, "failed to parse time in RFC3339 format. Using default current time", zap.String("created time", createdAtTime.String()))
		createdAtTime = time.Now()
	}

	completedAtTime, err := time.Parse(time.RFC3339, req.GetVkycProfileStatusUpdateCallback().GetCompletedAt())
	if err != nil {
		logger.Info(ctx, "failed to parse time in RFC3339 format. Using default current time", zap.String("completed time", completedAtTime.String()))
		completedAtTime = time.Now()
	}

	reviewerActionEnum := getReviewerAction(req.GetVkycProfileStatusUpdateCallback().GetReviewerAction())
	vkycProfileStatusEnum := getVkycProfileStatus(req.GetVkycProfileStatusUpdateCallback().GetStatus())

	event := &kycV2Pb.VKYCProfileStatusCallbackEvent{
		ReqId:             req.GetVkycProfileStatusUpdateCallback().GetReqId(),
		ReferenceId:       req.GetVkycProfileStatusUpdateCallback().GetReferenceId(),
		ReviewerAction:    reviewerActionEnum,
		VkycProfileStatus: vkycProfileStatusEnum,
		StatusDetail:      req.GetVkycProfileStatusUpdateCallback().GetStatusDetail(),
		CreatedAt:         timestampPb.New(createdAtTime),
		CompletedAt:       timestampPb.New(completedAtTime),
	}

	_, pubErr := s.kycStatusPublisher.Publish(ctx, &kycV2Pb.KycStatusUpdateEvent{
		// client request Id for IDFC VKYC
		Identifier:        req.GetVkycProfileStatusUpdateCallback().GetReqId(),
		StatusUpdateEvent: &kycV2Pb.KycStatusUpdateEvent_IdfcVkycStatus{IdfcVkycStatus: event},
	})
	if pubErr != nil {
		logger.Error(ctx, "failed to publish IDFC VKYC status update callback", zap.Any(logger.PAYLOAD, event), zap.Error(pubErr))
	}

	resp := &vendorIdfcVkycPb.VKYCProfileStatusCallbackResponse{
		ReqId:  req.GetVkycProfileStatusUpdateCallback().GetReqId(),
		Status: req.GetVkycProfileStatusUpdateCallback().GetStatus(),
	}
	return resp, nil
}

func getReviewerAction(status string) kycV2Pb.ReviewerAction {
	return kycV2Pb.ReviewerAction_REVIEWER_ACTION_UNSPECIFIED
}

func getVkycProfileStatus(status string) kycV2Pb.VkycProfileCallbackStatus {
	return kycV2Pb.VkycProfileCallbackStatus_VKYC_PROFILE_CALLBACK_STATUS_UNSPECIFIED
}
