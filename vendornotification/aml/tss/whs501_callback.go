package tss

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/vendors/tss"
	"github.com/epifi/gamma/vendornotification/redactor"
)

func (s *Service) ProcessWHS501Callback(ctx context.Context, req *tss.ProcessWHS501CallbackRequest) (*tss.ProcessWHS501CallbackResponse, error) {
	redactor.LogCallbackRequestData(ctx, req, ProcessWHS501Callback, req.GetRequestData().GetRequestId(), nil)
	if req.GetEventType() == "" {
		logger.Info(ctx, "event type is empty")
		return &tss.ProcessWHS501CallbackResponse{Status: "OK"}, nil
	}

	// Validate event type
	if req.GetEventType() != "WHS501" && req.GetEventType() != "ValidateEndPoint" {
		logger.Error(ctx, "invalid event type", zap.String("event_type", req.GetEventType()))
		return nil, status.Errorf(codes.InvalidArgument, "invalid event type: %s", req.GetEventType())
	}

	// Handle endpoint validation
	if req.GetEventType() == "ValidateEndPoint" {
		logger.Info(ctx, "endpoint validation request received")
		return &tss.ProcessWHS501CallbackResponse{Status: "OK"}, nil
	}

	// Process WHS501 webhook data
	requestData := req.GetRequestData()
	if requestData == nil {
		logger.Error(ctx, "request data is nil")
		return nil, status.Errorf(codes.InvalidArgument, "request data is required")
	}

	logger.Info(ctx, "processing WHS501 webhook", zap.Any("request", requestData))

	// TODO(Brijesh): Add business logic here to process the webhook data
	// This could include:
	// - Publishing to a queue for further processing
	// - Updating case status in database
	// - Triggering downstream workflows based on onboarding decision

	return &tss.ProcessWHS501CallbackResponse{Status: "OK"}, nil
}
