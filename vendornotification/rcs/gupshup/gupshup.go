package gupshup

import (
	"context"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/comms"
	gupshupRcsPb "github.com/epifi/gamma/api/vendornotification/rcs/gupshup"
	"github.com/epifi/gamma/api/vendors/gupshup"
	federalRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/redactor"

	"google.golang.org/protobuf/types/known/emptypb"
)

type Service struct {
	conf                 *config.Config
	rcsCallbackPublisher queue.Publisher
}

type GupshupRcsCallbackPublisher queue.Publisher

func NewService(conf *config.Config, rcsCallbackPublisher GupshupRcsCallbackPublisher) *Service {
	return &Service{
		conf:                 conf,
		rcsCallbackPublisher: rcsCallbackPublisher,
	}
}

var _ gupshupRcsPb.GupshupRcsCallbackServer = &Service{}

const GupshupRcsDLRRequest = "GupshupRcsDLRRequest" //nolint:gosec

// GupshupRcsDLR RPC to receive delivery report for RCS messages sent via gupshup vendor.
func (s Service) GupshupRcsDLR(ctx context.Context, req *gupshup.GupshupRcsDLRRequest) (*emptypb.Empty, error) {
	// TODO:
	//  1. Whitelist Gupshup IPs
	//  2. Configure alerts for this RPC

	redactor.LogCallbackRequestData(ctx, req, GupshupRcsDLRRequest, req.GetMessagePayload().GetRcsId(), federalRedactor.Config)

	unixMilliseconds := req.GetTimestamp()
	// Convert milliseconds to seconds
	unixSeconds := unixMilliseconds / 1000
	errorCode := strconv.Itoa(int(req.GetMessagePayload().GetDeliveryPayload().GetCode()))
	event := &comms.ProcessGupshupRcsCallbackRequest{
		ResponseId:         req.GetMessagePayload().GetGupshupId(),
		VendorReceivedTime: timestamppb.New(time.Unix(unixSeconds, 0)).AsTime().Format(time.RFC3339),
		DeliveryStatus:     strings.ToUpper(req.GetMessagePayload().GetStatusType()),
		DeliveryTime:       timestamppb.New(time.Unix(req.GetMessagePayload().GetDeliveryPayload().GetDeliveredTs(), 0)).AsTime().Format(time.RFC3339),
		FailureErrorCode:   errorCode,
		FailureDescription: req.GetMessagePayload().GetDeliveryPayload().GetReason(),
		PhoneNumber:        req.GetMessagePayload().GetDestination(),
		EventTimestamp:     timestamppb.Now(),
		SmsOrigin:          comms.SmsOrigin_SMS_ORIGIN_MOENGAGE,
		TemplateId:         req.GetMessagePayload().GetDeliveryPayload().GetTemplateId(),
	}

	msgId, err := s.rcsCallbackPublisher.Publish(ctx, event)
	if err != nil {
		logger.Error(ctx, "error publishing rcs gupshup callback event to queue", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "internal server error")
	}

	logger.Info(ctx, "message published to rcs gupshup queue successfully", zap.String(logger.QUEUE_MESSAGE_ID, msgId))

	return &emptypb.Empty{}, nil
}
