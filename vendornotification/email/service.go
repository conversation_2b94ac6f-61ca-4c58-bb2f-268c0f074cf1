package email

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"time"

	emptyPb "github.com/golang/protobuf/ptypes/empty"
	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	emailPb "github.com/epifi/gamma/api/vendornotification/email"
	"github.com/epifi/gamma/api/vendors/email/sendgrid"
	"github.com/epifi/gamma/api/vendors/email/ses"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/redactor"
)

type Service struct {
	conf      *config.Config
	publisher queue.Publisher
}

type EmailCallbackPublisher queue.Publisher

func NewService(publisher EmailCallbackPublisher, conf *config.Config) *Service {
	return &Service{
		publisher: publisher,
		conf:      conf,
	}
}

var _ emailPb.EmailCallbackServer = &Service{}

const (
	SesCallbackRequest      = "AwsSesCallback"
	SendGridCallbackRequest = "SendGridCallbackList"
)

func (s *Service) SesCallback(ctx context.Context, req *ses.AwsSesCallback) (*emptyPb.Empty, error) {
	redactor.LogCallbackRequestData(ctx, req, SesCallbackRequest, "", vendorsRedactor.Config)
	// processing data from ses email callback and populating event
	event := &emailPb.EmailCallbackEvent{
		EventType: req.GetEventType(),
		MessageId: req.GetMail().GetMessageId(),
		Vendor:    commonvgpb.Vendor_AWS_SES,
		EmailCallbackMeta: &emailPb.EmailCallbackMeta{
			Destination:   req.GetMail().GetDestination(),
			Reason:        getReason(req),
			ErrorMessages: getErrorResponse(req),
			UserAgent:     getUseragent(req),
		},
	}
	var timestampErr error
	event.EventTimestamp, timestampErr = convertTimestamp(req.GetMail().GetTimestamp())
	if timestampErr != nil {
		logger.Error(ctx, timestampErr.Error())
	}

	// publishing event to queue
	id, err := s.publisher.Publish(ctx, event)
	if err != nil {
		logger.Error(ctx, "error publishing email callback event to queue", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "internal server error")
	}
	logger.Info(ctx, "email callback event published in queue successfully", zap.String(logger.QUEUE_MESSAGE_ID, id))

	return &emptyPb.Empty{}, nil
}

// useragent field is only present in compliant, open, click events in ses callback
func getUseragent(req *ses.AwsSesCallback) string {
	switch req.GetEventType() {
	case "Complaint":
		return req.GetComplaint().GetUserAgent()
	case "Open":
		return req.GetOpen().GetUserAgent()
	case "Click":
		return req.GetClick().GetUserAgent()
	default:
		return ""
	}
}

// error responses are present for each email address in bounce and delivery delay event types
// In case of Failure only a single error message is present in the ses callback
func getErrorResponse(req *ses.AwsSesCallback) []string {
	var ErrorResponses []string
	switch req.GetEventType() {
	case "Bounce":
		for _, errMsg := range req.GetBounce().GetBouncedRecipients() {
			ErrorResponses = append(ErrorResponses, errMsg.GetDiagnosticCode())
		}
	case "DeliveryDelay":
		for _, errMsg := range req.GetDeliveryDelay().GetDelayedRecipients() {
			ErrorResponses = append(ErrorResponses, errMsg.GetDiagnosticCode())
		}
	case "Rendering Failure":
		ErrorResponses = append(ErrorResponses, req.GetFailure().GetErrorMessage())
	default:
		ErrorResponses = nil
	}
	return ErrorResponses
}

// Reason field is filled with type and subtype of the ses callback events, which provide
// information about why the event might have occurred
func getReason(req *ses.AwsSesCallback) string {
	switch req.GetEventType() {
	case "Bounce":
		return req.GetBounce().GetBounceType() + ", " + req.GetBounce().GetBounceSubType() + " - bounce"
	case "Complaint":
		return req.GetComplaint().GetComplaintFeedbackType() + " - complaint"
	case "Reject":
		return req.GetReject().GetReason() + " - reject"
	case "DeliveryDelay":
		return req.GetDeliveryDelay().GetDelayType() + " - delivery delay"
	default:
		return ""
	}
}

// timestamp is converted from ISO8601 format to unix format
func convertTimestamp(isoTimestamp string) (*timestamp.Timestamp, error) {
	t, err := time.Parse("2006-01-02T15:04:05.999Z", isoTimestamp)
	if err != nil {
		return nil, errors.Wrap(err, "error while parsing timestamp in ses email callback event")
	}
	unixTime := timestampPb.New(t)
	return unixTime, nil
}

func (s *Service) SendgridCallback(ctx context.Context, req *sendgrid.SendGridCallbackList) (*emptyPb.Empty, error) {
	redactor.LogCallbackRequestData(ctx, req, SendGridCallbackRequest, "", vendorsRedactor.Config)
	// request contains a list of sendgrid email callback events
	// process each event and publish it to queue
	for _, callback := range req.GetSendGridCallback() {
		event := processSendGridCallback(callback)
		id, err := s.publisher.Publish(ctx, event)
		if err != nil {
			logger.Error(ctx, "error publishing email callback event to queue", zap.Error(err))
			continue
		}
		logger.Info(ctx, "email callback event published in queue successfully", zap.String(logger.QUEUE_MESSAGE_ID, id))
	}
	return &emptyPb.Empty{}, nil
}

func processSendGridCallback(callback *sendgrid.SendGridCallback) *emailPb.EmailCallbackEvent {
	event := &emailPb.EmailCallbackEvent{
		EventType:      callback.GetEvent(),
		EventTimestamp: &timestamp.Timestamp{Seconds: int64(callback.GetTimestamp())},
		MessageId:      callback.GetSgMessageId(),
		Vendor:         commonvgpb.Vendor_SEND_GRID,
		EmailCallbackMeta: &emailPb.EmailCallbackMeta{
			Destination:   []string{callback.GetEmail()},
			Reason:        callback.GetReason(),
			ErrorMessages: []string{callback.GetResponse(), callback.GetStatus()},
			UserAgent:     callback.GetUseragent(),
			Attempt:       callback.GetAttempt(),
		},
	}
	return event
}
