package connectedaccount

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/connectedaccount/data/consentorchestrator"
	"github.com/epifi/gamma/connectedaccount/property"
	"github.com/epifi/gamma/pkg/connectedaccount"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"reflect"
	"sort"
	"strings"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	idgenMocks "github.com/epifi/be-common/pkg/idgen/mocks"
	"github.com/epifi/be-common/pkg/pagination"
	mocks2 "github.com/epifi/be-common/pkg/queue/mocks"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	caPb "github.com/epifi/gamma/api/connected_account"
	caCoPb "github.com/epifi/gamma/api/connected_account/consumer"
	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	caExtPb "github.com/epifi/gamma/api/connected_account/external"
	"github.com/epifi/gamma/api/screener"
	mockScreener "github.com/epifi/gamma/api/screener/mocks"
	"github.com/epifi/gamma/api/segment"
	mocks3 "github.com/epifi/gamma/api/segment/mocks"
	typespb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/userintel"
	mockUIIntel "github.com/epifi/gamma/api/userintel/mocks"
	mockIncEst "github.com/epifi/gamma/api/vendorgateway/incomeestimator/mocks"
	ncPb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	ncMocks "github.com/epifi/gamma/api/vendorgateway/namecheck/mocks"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	wealthOnbMocks "github.com/epifi/gamma/api/wealthonboarding/mocks"
	"github.com/epifi/gamma/connectedaccount/config/genconf"
	"github.com/epifi/gamma/connectedaccount/dao"
	"github.com/epifi/gamma/connectedaccount/test/mocks/mock_account_consent_orchestrator"
	account_processor "github.com/epifi/gamma/connectedaccount/test/mocks/mock_account_processor"
	"github.com/epifi/gamma/connectedaccount/test/mocks/mock_dao"
	mock_user "github.com/epifi/gamma/connectedaccount/test/mocks/mock_user"
	mock_in_memory_store "github.com/epifi/gamma/pkg/connectedaccount/test/mocks"
	mock_release "github.com/epifi/gamma/pkg/feature/release/mocks"
)

var (
	dynconf  *genconf.Config
	pgdb     *gormv2.DB
	accList1 = &caPb.Accounts{AccountList: []*caPb.Account{
		{
			FiType:          caEnumPb.FIType_FI_TYPE_DEPOSIT,
			FipId:           "HDFC-FIP",
			AccType:         "SAVINGS",
			LinkRefNumber:   "test-link-ref-number",
			MaskedAccNumber: "XXXXXXX989",
		},
	}}
	mockTxnExecutor storagev2.TxnExecutor
)

func TestCaService_enrichAccountMeta(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx      context.Context
		accounts []*caExtPb.AccountDetails
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(m *mockStruct)
		want    []*caExtPb.AccountDetails
		wantErr bool
	}{
		{
			name: "error fetching account from DB",
			args: args{
				ctx: context.Background(),
				accounts: []*caExtPb.AccountDetails{
					{
						ActorId:          "actor-1",
						LinkedAccountRef: "test-1",
					},
					{
						ActorId:          "actor-1",
						LinkedAccountRef: "test-2",
					},
					{
						ActorId:          "actor-1",
						LinkedAccountRef: "test-3",
					},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetByActorIdAndLinkedAccountRef(gomock.Any(), "actor-1", "test-1").
					Return(&caPb.AaAccount{Id: "test-id-1"}, nil)
				m.mockAccountDao.EXPECT().GetByActorIdAndLinkedAccountRef(gomock.Any(), "actor-1", "test-2").
					Return(&caPb.AaAccount{Id: "test-id-2"}, nil)
				m.mockAccountDao.EXPECT().GetByActorIdAndLinkedAccountRef(gomock.Any(), "actor-1", "test-3").
					Return(nil, errors.New("some error"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Successfully enrich account meta using go routines",
			args: args{
				ctx: context.Background(),
				accounts: []*caExtPb.AccountDetails{
					{
						ActorId:          "actor-1",
						LinkedAccountRef: "test-1",
					},
					{
						ActorId:          "actor-1",
						LinkedAccountRef: "test-2",
					},
					{
						ActorId:          "actor-1",
						LinkedAccountRef: "test-3",
					},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetByActorIdAndLinkedAccountRef(gomock.Any(), "actor-1", "test-1").
					Return(&caPb.AaAccount{Id: "test-id-1"}, nil)
				m.mockAccountDao.EXPECT().GetByActorIdAndLinkedAccountRef(gomock.Any(), "actor-1", "test-2").
					Return(&caPb.AaAccount{Id: "test-id-2"}, nil)
				m.mockAccountDao.EXPECT().GetByActorIdAndLinkedAccountRef(gomock.Any(), "actor-1", "test-3").
					Return(&caPb.AaAccount{Id: "test-id-3"}, nil)
			},
			want: []*caExtPb.AccountDetails{
				{
					ActorId:          "actor-1",
					LinkedAccountRef: "test-1",
					AccountId:        "test-id-1",
				},
				{
					ActorId:          "actor-1",
					LinkedAccountRef: "test-2",
					AccountId:        "test-id-2",
				},
				{
					ActorId:          "actor-1",
					LinkedAccountRef: "test-3",
					AccountId:        "test-id-3",
				},
			},
			wantErr: false,
		},
		{
			name: "account not found for one account, no error",
			args: args{
				ctx: context.Background(),
				accounts: []*caExtPb.AccountDetails{
					{
						ActorId:          "actor-1",
						LinkedAccountRef: "test-1",
					},
					{
						ActorId:          "actor-1",
						LinkedAccountRef: "test-2",
					},
					{
						ActorId:          "actor-1",
						LinkedAccountRef: "test-3",
					},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetByActorIdAndLinkedAccountRef(gomock.Any(), "actor-1", "test-1").
					Return(&caPb.AaAccount{Id: "test-id-1"}, nil)
				m.mockAccountDao.EXPECT().GetByActorIdAndLinkedAccountRef(gomock.Any(), "actor-1", "test-2").
					Return(&caPb.AaAccount{Id: "test-id-2"}, nil)
				m.mockAccountDao.EXPECT().GetByActorIdAndLinkedAccountRef(gomock.Any(), "actor-1", "test-3").
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: []*caExtPb.AccountDetails{
				{
					ActorId:          "actor-1",
					LinkedAccountRef: "test-1",
					AccountId:        "test-id-1",
				},
				{
					ActorId:          "actor-1",
					LinkedAccountRef: "test-2",
					AccountId:        "test-id-2",
				},
				{
					ActorId:          "actor-1",
					LinkedAccountRef: "test-3",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := getMocks(t)
			c := &CaService{
				accountDao:  m.mockAccountDao,
				txnExecutor: mockTxnExecutor,
			}
			tt.mocks(m)
			got, err := c.enrichAccountMeta(tt.args.ctx, tt.args.accounts)
			if (err != nil) != tt.wantErr {
				t.Errorf("enrichAccountMeta() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				sort.Slice(got, func(i, j int) bool {
					return got[i].LinkedAccountRef < got[j].LinkedAccountRef
				})
				sort.Slice(tt.want, func(i, j int) bool {
					return got[i].LinkedAccountRef < got[j].LinkedAccountRef
				})
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("enrichAccountMeta() got = %v, want %v", got, tt.want)
				}
			}
		})
	}
}

func TestCaService_GetConsent(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx     context.Context
		request *caPb.GetConsentRequest
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(m *mockStruct)
		want    *caPb.GetConsentResponse
		wantErr bool
	}{
		{
			name: "successfully got consent",
			args: args{
				ctx: context.Background(),
				request: &caPb.GetConsentRequest{
					ConsentId: "consent-id-1",
				},
			},
			mocks: func(m *mockStruct) {
				m.mockConsentDao.EXPECT().GetByConsentId(gomock.Any(), "consent-id-1").
					Return(&caPb.Consent{ConsentRequestId: "consent-request-id-1"}, nil)
				m.mockConsentRequestDao.EXPECT().Get(gomock.Any(), "consent-request-id-1").
					Return(&caPb.ConsentRequest{ConsentId: "consent-id-1"}, nil)
			},
			want: &caPb.GetConsentResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "failed to get consent (GetByConsentId())",
			args: args{
				ctx: context.Background(),
				request: &caPb.GetConsentRequest{
					ConsentId: "non-existent-consent-id",
				},
			},
			mocks: func(m *mockStruct) {
				m.mockConsentDao.EXPECT().GetByConsentId(gomock.Any(), "non-existent-consent-id").
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &caPb.GetConsentResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "failed to get consent (Get())",
			args: args{
				ctx: context.Background(),
				request: &caPb.GetConsentRequest{
					ConsentId: "consent-id-2",
				},
			},
			mocks: func(m *mockStruct) {
				m.mockConsentDao.EXPECT().GetByConsentId(gomock.Any(), "consent-id-2").
					Return(&caPb.Consent{ConsentRequestId: "consent-request-id-2"}, nil)
				m.mockConsentRequestDao.EXPECT().Get(gomock.Any(), "consent-request-id-2").
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &caPb.GetConsentResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "failure: error other than record not found",
			args: args{
				ctx: context.Background(),
				request: &caPb.GetConsentRequest{
					ConsentId: "consent-id-1",
				},
			},
			mocks: func(m *mockStruct) {
				m.mockConsentDao.EXPECT().GetByConsentId(gomock.Any(), "consent-id-1").
					Return(nil, errors.New("some random error"))
			},
			want: &caPb.GetConsentResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error fetching consent from db"),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := getMocks(t)
			c := &CaService{
				crDao:       m.mockConsentRequestDao,
				cDao:        m.mockConsentDao,
				txnExecutor: mockTxnExecutor,
			}
			tt.mocks(m)
			got, err := c.GetConsent(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetConsent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if err == nil {
				assert.NotNil(t, got)
				if !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
					t.Errorf("Internal Error, GetConsent() got = %v, want %v", got, tt.want)
					return
				}
			}
		})
	}
}

func TestCaService_GetConsentRequestDetails(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx     context.Context
		request *caPb.GetConsentRequestDetailsRequest
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(m *mockStruct)
		want    *caPb.GetConsentRequestDetailsResponse
		wantErr bool
	}{
		{
			name: "successfully got consent request details",
			args: args{
				ctx: context.Background(),
				request: &caPb.GetConsentRequestDetailsRequest{
					ConsentHandle: "consent-handle-1",
				},
			},
			mocks: func(m *mockStruct) {
				m.mockConsentRequestDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-1").
					Return(&caPb.ConsentRequest{
						ConsentId:     "consent-id-1",
						ConsentHandle: "consent-handle-1",
						ActorId:       "actor-id-1",
					}, nil)
			},
			want: &caPb.GetConsentRequestDetailsResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "failed to get consent request details",
			args: args{
				ctx: context.Background(),
				request: &caPb.GetConsentRequestDetailsRequest{
					ConsentHandle: "non-existent-consent-handle",
				},
			},
			mocks: func(m *mockStruct) {
				m.mockConsentRequestDao.EXPECT().GetByConsentHandle(gomock.Any(), "non-existent-consent-handle").
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &caPb.GetConsentRequestDetailsResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "failure: error other than record not found",
			args: args{
				ctx: context.Background(),
				request: &caPb.GetConsentRequestDetailsRequest{
					ConsentHandle: "consent-handle-1",
				},
			},
			mocks: func(m *mockStruct) {
				m.mockConsentRequestDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-1").
					Return(nil, errors.New("some random error"))
			},
			want: &caPb.GetConsentRequestDetailsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error fetching consent request"),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := getMocks(t)
			c := &CaService{
				crDao:       m.mockConsentRequestDao,
				txnExecutor: mockTxnExecutor,
			}
			tt.mocks(m)
			got, err := c.GetConsentRequestDetails(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetConsentRequestDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
				if !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
					t.Errorf("Internal Error, GetConsentRequestDetails() got = %v, want %v", got, tt.want)
					return
				}
			}
		})
	}
}

func TestCaService_GetDataFetchAttempt(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx     context.Context
		request *caPb.GetDataFetchAttemptRequest
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(m *mockStruct)
		want    *caPb.GetDataFetchAttemptResponse
		wantErr bool
	}{
		{
			name: "successfully got data fetch attempt",
			args: args{
				ctx: context.Background(),
				request: &caPb.GetDataFetchAttemptRequest{
					SessionId: "session-id-1",
				},
			},
			mocks: func(m *mockStruct) {
				m.mockDfaDao.EXPECT().GetBySessionId(gomock.Any(), "session-id-1", nil).
					Return(&caPb.DataFetchAttempt{ConsentReferenceId: "consent-ref-id-1"}, nil)
				m.mockConsentDao.EXPECT().Get(gomock.Any(), "consent-ref-id-1").
					Return(&caPb.Consent{ConsentRequestId: "consent-request-id-1"}, nil)
				m.mockConsentRequestDao.EXPECT().Get(gomock.Any(), "consent-request-id-1").
					Return(&caPb.ConsentRequest{ConsentId: "consent-id-1"}, nil)
			},
			want: &caPb.GetDataFetchAttemptResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "failed to get data fetch attempt - record not found",
			args: args{
				ctx: context.Background(),
				request: &caPb.GetDataFetchAttemptRequest{
					SessionId: "random-session-id",
				},
			},
			mocks: func(m *mockStruct) {
				m.mockDfaDao.EXPECT().GetBySessionId(gomock.Any(), "random-session-id", nil).
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &caPb.GetDataFetchAttemptResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "failed to get data fetch attempt - internal error",
			args: args{
				ctx: context.Background(),
				request: &caPb.GetDataFetchAttemptRequest{
					SessionId: "session-id-2",
				},
			},
			mocks: func(m *mockStruct) {
				m.mockDfaDao.EXPECT().GetBySessionId(gomock.Any(), "session-id-2", nil).
					Return(&caPb.DataFetchAttempt{ConsentReferenceId: "consent-ref-id-2"}, nil)
				m.mockConsentDao.EXPECT().Get(gomock.Any(), "consent-ref-id-2").
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &caPb.GetDataFetchAttemptResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "successfully got data fetch attempt with missing consent request",
			args: args{
				ctx: context.Background(),
				request: &caPb.GetDataFetchAttemptRequest{
					SessionId: "session-id-3",
				},
			},
			mocks: func(m *mockStruct) {
				m.mockDfaDao.EXPECT().GetBySessionId(gomock.Any(), "session-id-3", nil).
					Return(&caPb.DataFetchAttempt{ConsentReferenceId: "consent-ref-id-3"}, nil)
				m.mockConsentDao.EXPECT().Get(gomock.Any(), "consent-ref-id-3").
					Return(&caPb.Consent{ConsentRequestId: "consent-request-id-3"}, nil)
				m.mockConsentRequestDao.EXPECT().Get(gomock.Any(), "consent-request-id-3").
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &caPb.GetDataFetchAttemptResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "failure: error other than record not found",
			args: args{
				ctx: context.Background(),
				request: &caPb.GetDataFetchAttemptRequest{
					SessionId: "random-session-id",
				},
			},
			mocks: func(m *mockStruct) {
				m.mockDfaDao.EXPECT().GetBySessionId(gomock.Any(), "random-session-id", nil).
					Return(nil, errors.New("some random error"))
			},
			want: &caPb.GetDataFetchAttemptResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error fetching data attempt"),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := getMocks(t)
			c := &CaService{
				cDao:        m.mockConsentDao,
				crDao:       m.mockConsentRequestDao,
				dfaDao:      m.mockDfaDao,
				txnExecutor: mockTxnExecutor,
			}
			if tt.mocks != nil {
				tt.mocks(m)
			}
			got, err := c.GetDataFetchAttempt(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDataFetchAttempt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
				if !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
					t.Errorf("Internal Error, GetConsent() got = %v, want %v", got, tt.want)
					return
				}
			}
		})
	}
}

func TestCaService_GetAccounts(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx     context.Context
		request *caPb.GetAccountsRequest
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(m *mockStruct)
		want    *caPb.GetAccountsResponse
		wantErr bool
	}{
		{
			name: "successfully got accounts",
			args: args{
				ctx: context.Background(),
				request: &caPb.GetAccountsRequest{
					ActorId:           "actor-id-1",
					AccountFilterList: []caExtPb.AccountFilter{caExtPb.AccountFilter_ACCOUNT_FILTER_DELETED},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetByActorIdAndStatus(gomock.Any(), "actor-id-1",
					[]caEnumPb.AccountStatus{caEnumPb.AccountStatus_ACCOUNT_STATUS_DELETED}, gomock.Any()).
					Return([]*caPb.AaAccount{
						{FipId: "HDFC-FIP", ActorId: "actor-id-1"},
					}, nil)
			},
			want:    &caPb.GetAccountsResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
		{
			name: "successfully got accounts - failed to get summary details",
			args: args{
				ctx: context.Background(),
				request: &caPb.GetAccountsRequest{
					ActorId:           "actor-id-1",
					AccountFilterList: []caExtPb.AccountFilter{caExtPb.AccountFilter_ACCOUNT_FILTER_DELETED},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetByActorIdAndStatus(gomock.Any(), "actor-id-1",
					[]caEnumPb.AccountStatus{caEnumPb.AccountStatus_ACCOUNT_STATUS_DELETED}, gomock.Any()).
					Return([]*caPb.AaAccount{
						{FipId: "FDRLFIPPROD", ActorId: "actor-id-1"},
					}, nil)
				m.mockProcessor.EXPECT().GetAccountDetails(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil, nil, errors.New("not found"))
			},
			want:    &caPb.GetAccountsResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
		{
			name: "successfully got accounts - successfully got summary details - not jupiter",
			args: args{
				ctx: context.Background(),
				request: &caPb.GetAccountsRequest{
					ActorId:           "actor-id-1",
					AccountFilterList: []caExtPb.AccountFilter{caExtPb.AccountFilter_ACCOUNT_FILTER_DELETED},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetByActorIdAndStatus(gomock.Any(), "actor-id-1",
					[]caEnumPb.AccountStatus{caEnumPb.AccountStatus_ACCOUNT_STATUS_DELETED}, gomock.Any()).
					Return([]*caPb.AaAccount{
						{FipId: "FDRLFIPPROD", ActorId: "actor-id-1"},
					}, nil)
				m.mockProcessor.EXPECT().GetAccountDetails(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil, &caExtPb.DepositSummary{IfscCode: "NOTJUPITER"}, nil)
			},
			want:    &caPb.GetAccountsResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
		{
			name: "successfully got accounts - successfully got summary details - jupiter",
			args: args{
				ctx: context.Background(),
				request: &caPb.GetAccountsRequest{
					ActorId:           "actor-id-1",
					AccountFilterList: []caExtPb.AccountFilter{caExtPb.AccountFilter_ACCOUNT_FILTER_DELETED},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetByActorIdAndStatus(gomock.Any(), "actor-id-1",
					[]caEnumPb.AccountStatus{caEnumPb.AccountStatus_ACCOUNT_STATUS_DELETED}, gomock.Any()).
					Return([]*caPb.AaAccount{
						{FipId: "FDRLFIPPROD", ActorId: "actor-id-1"},
					}, nil)
				m.mockProcessor.EXPECT().GetAccountDetails(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil, &caExtPb.DepositSummary{IfscCode: "FDRL0007777"}, nil)
			},
			want:    &caPb.GetAccountsResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
		{
			name: "failed to get accounts",
			args: args{
				ctx: context.Background(),
				request: &caPb.GetAccountsRequest{
					ActorId:           "missing-actor-id",
					AccountFilterList: []caExtPb.AccountFilter{caExtPb.AccountFilter_ACCOUNT_FILTER_DELETED},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetByActorIdAndStatus(gomock.Any(), "missing-actor-id",
					[]caEnumPb.AccountStatus{caEnumPb.AccountStatus_ACCOUNT_STATUS_DELETED}, gomock.Any()).
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:    &caPb.GetAccountsResponse{Status: rpcPb.StatusRecordNotFound()},
			wantErr: false,
		},
		{
			name: "failure: error other than record not found",
			args: args{
				ctx: context.Background(),
				request: &caPb.GetAccountsRequest{
					ActorId:           "missing-actor-id",
					AccountFilterList: []caExtPb.AccountFilter{caExtPb.AccountFilter_ACCOUNT_FILTER_DELETED},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetByActorIdAndStatus(gomock.Any(), "missing-actor-id",
					[]caEnumPb.AccountStatus{caEnumPb.AccountStatus_ACCOUNT_STATUS_DELETED}, gomock.Any()).
					Return(nil, errors.New("some random error"))
			},
			want:    &caPb.GetAccountsResponse{Status: rpcPb.StatusInternalWithDebugMsg("error while fetching linked accounts")},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := getMocks(t)
			c := &CaService{
				accountDao:    m.mockAccountDao,
				dataProcessor: m.mockProcessor,
				conf:          dynconf,
				txnExecutor:   mockTxnExecutor,
			}
			tt.mocks(m)
			got, err := c.GetAccounts(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAccounts() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
				if !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
					t.Errorf("Internal Error, GetAccounts() got = %v, want %v", got, tt.want)
					return
				}
			}
		})
	}
}

func TestCaService_GetAvailableFips(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx                     context.Context
		getAvailableFipsRequest *caPb.GetAvailableFipsRequest
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(m *mockStruct)
		want    *caPb.GetAvailableFipsResponse
		wantErr bool
	}{
		{
			name: "successfully received FIP details",
			args: args{
				ctx:                     context.Background(),
				getAvailableFipsRequest: &caPb.GetAvailableFipsRequest{},
			},
			mocks: func(m *mockStruct) {
				m.mockCaProp.EXPECT().GetAllowedFipAndFiForActor(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*caExtPb.FipConfig{
					{
						Bank:        typespb.Bank_HDFC,
						FiTypesList: []caEnumPb.FIType{caEnumPb.FIType_FI_TYPE_DEPOSIT},
						FipId:       "HDFC-FIP",
					},
					{
						Bank:        typespb.Bank_ICICI,
						FiTypesList: []caEnumPb.FIType{caEnumPb.FIType_FI_TYPE_DEPOSIT},
						FipId:       "ICICI-FIP",
					},
				}, nil, nil)
			},
			want:    &caPb.GetAvailableFipsResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
		{
			name: "failure: error getting allowed fip for actor",
			args: args{
				ctx:                     context.Background(),
				getAvailableFipsRequest: &caPb.GetAvailableFipsRequest{},
			},
			mocks: func(m *mockStruct) {
				m.mockCaProp.EXPECT().GetAllowedFipAndFiForActor(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil, errors.New("some random error"))
			},
			want:    &caPb.GetAvailableFipsResponse{Status: rpcPb.StatusInternalWithDebugMsg("error fetching fips and fi for actor")},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := getMocks(t)
			tt.mocks(m)
			c := &CaService{
				conf:        dynconf,
				caProp:      m.mockCaProp,
				txnExecutor: mockTxnExecutor,
			}
			got, err := c.GetAvailableFips(tt.args.ctx, tt.args.getAvailableFipsRequest)
			fmt.Println(got)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAvailableFips() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
				if !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
					t.Errorf("Internal Error, GetAvailableFips() got = %v, want %v", got, tt.want)
					return
				}
			}
		})
	}
}

func TestCaService_CreateBankPreference(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx                         context.Context
		createBankPreferenceRequest *caPb.CreateBankPreferenceRequest
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(m *mockStruct)
		want    *caPb.CreateBankPreferenceResponse
		wantErr bool
	}{
		{
			name: "successfully created bank preference",
			args: args{
				ctx: context.Background(),
				createBankPreferenceRequest: &caPb.CreateBankPreferenceRequest{
					ActorId:  "actor-id-1",
					BankList: []typespb.Bank{typespb.Bank_YES, typespb.Bank_IDFC},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockPrefDao.EXPECT().GetByActorIdAndBank(gomock.Any(), "actor-id-1", typespb.Bank_YES).
					Return(nil, epifierrors.ErrRecordNotFound)
				m.mockPrefDao.EXPECT().GetByActorIdAndBank(gomock.Any(), "actor-id-1", typespb.Bank_IDFC).
					Return(nil, epifierrors.ErrRecordNotFound)
				m.mockPrefDao.EXPECT().Create(gomock.Any(), &caPb.AaUserBankPreference{
					ActorId: "actor-id-1",
					Bank:    typespb.Bank_YES,
				}).Return(&caPb.AaUserBankPreference{ActorId: "actor-id-1"}, nil)
				m.mockPrefDao.EXPECT().Create(gomock.Any(), &caPb.AaUserBankPreference{
					ActorId: "actor-id-1",
					Bank:    typespb.Bank_IDFC,
				}).Return(&caPb.AaUserBankPreference{ActorId: "actor-id-1"}, nil)
				m.preferenceDao = m.mockPrefDao
			},
			want:    &caPb.CreateBankPreferenceResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
		{
			name: "failed to created bank preference - not eligible",
			args: args{
				ctx: context.Background(),
				createBankPreferenceRequest: &caPb.CreateBankPreferenceRequest{
					ActorId:  "actor-id-1",
					BankList: []typespb.Bank{typespb.Bank_UNION},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockPrefDao.EXPECT().GetByActorIdAndBank(gomock.Any(), "actor-id-1", typespb.Bank_UNION).
					Return(&caPb.AaUserBankPreference{ActorId: "actor-id"}, nil)
				m.preferenceDao = m.mockPrefDao
			},
			want:    &caPb.CreateBankPreferenceResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
		{
			name: "failure: GetByActorIdAndBank error other than record not found",
			args: args{
				ctx: context.Background(),
				createBankPreferenceRequest: &caPb.CreateBankPreferenceRequest{
					ActorId:  "actor-id-1",
					BankList: []typespb.Bank{typespb.Bank_YES, typespb.Bank_IDFC},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockPrefDao.EXPECT().GetByActorIdAndBank(gomock.Any(), "actor-id-1", typespb.Bank_YES).
					Return(nil, errors.New("some random error"))
				m.preferenceDao = m.mockPrefDao
			},
			want:    &caPb.CreateBankPreferenceResponse{Status: rpcPb.StatusInternal()},
			wantErr: false,
		},
		{
			name: "failure: pref dao create error",
			args: args{
				ctx: context.Background(),
				createBankPreferenceRequest: &caPb.CreateBankPreferenceRequest{
					ActorId:  "actor-id-1",
					BankList: []typespb.Bank{typespb.Bank_YES, typespb.Bank_IDFC},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockPrefDao.EXPECT().GetByActorIdAndBank(gomock.Any(), "actor-id-1", typespb.Bank_YES).
					Return(nil, epifierrors.ErrRecordNotFound)
				m.mockPrefDao.EXPECT().GetByActorIdAndBank(gomock.Any(), "actor-id-1", typespb.Bank_IDFC).
					Return(nil, epifierrors.ErrRecordNotFound)
				m.mockPrefDao.EXPECT().Create(gomock.Any(), &caPb.AaUserBankPreference{
					ActorId: "actor-id-1",
					Bank:    typespb.Bank_YES,
				}).Return(nil, errors.New("some random error"))
				m.preferenceDao = m.mockPrefDao
			},
			want:    &caPb.CreateBankPreferenceResponse{Status: rpcPb.StatusInternal()},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := getMocks(t)
			tt.mocks(m)
			c := &CaService{
				preferenceDao: m.preferenceDao,
				txnExecutor:   mockTxnExecutor,
				conf:          dynconf,
			}
			got, err := c.CreateBankPreference(tt.args.ctx, tt.args.createBankPreferenceRequest)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateBankPreference() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
				if !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
					t.Errorf("Internal Error, CreateBankPreference() got = %v, want %v", got, tt.want)
					return
				}
			}
		})
	}
}

func TestCaService_GetLinkedAaAccounts(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockConsentDao := mock_dao.NewMockConsentDao(ctr)
	mockAccountDao := mock_dao.NewMockAaAccountDao(ctr)
	mockAco := mock_account_consent_orchestrator.NewMockAccountConsentOrchestrator(ctr)
	defer ctr.Finish()

	testAccounts := &caPb.Accounts{
		AccountList: []*caPb.Account{
			{
				FipId:           "HDFC_FIP",
				AccType:         "SAVINGS",
				FiType:          caEnumPb.FIType_FI_TYPE_DEPOSIT,
				LinkRefNumber:   "link-ref-num-1",
				MaskedAccNumber: "masked-acc-num-1",
			},
		},
	}
	type fields struct {
		cDao                  dao.ConsentDao
		accountDao            dao.AaAccountDao
		accountConsentWrapper consentorchestrator.AccountConsentOrchestrator
	}
	type args struct {
		ctx     context.Context
		request *caPb.GetLinkedAaAccountsRequest
		mocks   []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *caPb.GetLinkedAaAccountsResponse
		wantErr bool
	}{
		{
			name: "successfully got linked accounts",
			fields: fields{
				cDao:                  mockConsentDao,
				accountDao:            mockAccountDao,
				accountConsentWrapper: mockAco,
			},
			args: args{
				ctx: context.Background(),
				request: &caPb.GetLinkedAaAccountsRequest{
					ActorId: "actor-id-1",
				},
				mocks: []interface{}{
					mockConsentDao.EXPECT().GetByActorIdWithStatus(gomock.Any(), "actor-id-1", []caEnumPb.ConsentStatus{caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE, caEnumPb.ConsentStatus_CONSENT_STATUS_PAUSED}).
						Return([]*caPb.Consent{
							{
								Id:               "id-1",
								ConsentRequestId: "cr-id-1",
								Accounts:         testAccounts,
							},
						}, nil,
						),
					mockAco.EXPECT().RefreshByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).
						Return(&caPb.Consent{
							Id:               "id-1",
							ActorId:          "actor-id-1",
							ConsentRequestId: "cr-id-1",
							ConsentStatus:    caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE,
							Accounts:         testAccounts,
						}, nil,
						),
					mockAccountDao.EXPECT().GetByActorIdAndLinkedAccountRef(gomock.Any(), "actor-id-1", "link-ref-num-1").
						Return(&caPb.AaAccount{Id: "acc-id-1"}, nil),
				},
			},
			want: &caPb.GetLinkedAaAccountsResponse{
				Status: rpcPb.StatusOk(),
				Accounts: []*caExtPb.AccountDetails{
					{
						ActorId:          "actor-id-1",
						LinkedAccountRef: "link-ref-num-1",
						AccountId:        "acc-id-1",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "failed to link accounts",
			fields: fields{
				cDao:                  mockConsentDao,
				accountDao:            mockAccountDao,
				accountConsentWrapper: mockAco,
			},
			args: args{
				ctx: context.Background(),
				request: &caPb.GetLinkedAaAccountsRequest{
					ActorId: "random-actor-id",
				},
				mocks: []interface{}{
					mockConsentDao.EXPECT().GetByActorIdWithStatus(gomock.Any(), "random-actor-id", []caEnumPb.ConsentStatus{caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE, caEnumPb.ConsentStatus_CONSENT_STATUS_PAUSED}).
						Return(nil, epifierrors.ErrRecordNotFound),
				},
			},
			want: &caPb.GetLinkedAaAccountsResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "failure: error other than record not found",
			fields: fields{
				cDao:                  mockConsentDao,
				accountDao:            mockAccountDao,
				accountConsentWrapper: mockAco,
			},
			args: args{
				ctx: context.Background(),
				request: &caPb.GetLinkedAaAccountsRequest{
					ActorId: "random-actor-id",
				},
				mocks: []interface{}{
					mockConsentDao.EXPECT().GetByActorIdWithStatus(gomock.Any(), "random-actor-id", []caEnumPb.ConsentStatus{caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE, caEnumPb.ConsentStatus_CONSENT_STATUS_PAUSED}).
						Return(nil, errors.New("some random error")),
				},
			},
			want: &caPb.GetLinkedAaAccountsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error while fetching consents using actor_id"),
			},
			wantErr: false,
		},
		{
			name: "failure: error refreshing consents, refresh error",
			fields: fields{
				cDao:                  mockConsentDao,
				accountDao:            mockAccountDao,
				accountConsentWrapper: mockAco,
			},
			args: args{
				ctx: context.Background(),
				request: &caPb.GetLinkedAaAccountsRequest{
					ActorId: "actor-id-1",
				},
				mocks: []interface{}{
					mockConsentDao.EXPECT().GetByActorIdWithStatus(gomock.Any(), "actor-id-1", []caEnumPb.ConsentStatus{caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE, caEnumPb.ConsentStatus_CONSENT_STATUS_PAUSED}).
						Return([]*caPb.Consent{
							{
								Id:               "id-1",
								ConsentRequestId: "cr-id-1",
								Accounts:         testAccounts,
							},
						}, nil,
						),
					mockAco.EXPECT().RefreshByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("some random error")),
				},
			},
			want: &caPb.GetLinkedAaAccountsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error while refreshing consent"),
			},
			wantErr: false,
		},
		{
			name: "failure: error refreshing consents, enrich error",
			fields: fields{
				cDao:                  mockConsentDao,
				accountDao:            mockAccountDao,
				accountConsentWrapper: mockAco,
			},
			args: args{
				ctx: context.Background(),
				request: &caPb.GetLinkedAaAccountsRequest{
					ActorId: "actor-id-1",
				},
				mocks: []interface{}{
					mockConsentDao.EXPECT().GetByActorIdWithStatus(gomock.Any(), "actor-id-1", []caEnumPb.ConsentStatus{caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE, caEnumPb.ConsentStatus_CONSENT_STATUS_PAUSED}).
						Return([]*caPb.Consent{
							{
								Id:               "id-1",
								ConsentRequestId: "cr-id-1",
								Accounts:         testAccounts,
							},
						}, nil,
						),
					mockAccountDao.EXPECT().GetByActorIdAndLinkedAccountRef(gomock.Any(), "actor-id-1", "link-ref-num-1").
						Return(nil, errors.New("some random error")),
				},
			},
			want: &caPb.GetLinkedAaAccountsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error while enriching account meta"),
			},
			wantErr: false,
		},
		{
			name: "failure: error refreshing consents, merge error",
			fields: fields{
				cDao:                  mockConsentDao,
				accountDao:            mockAccountDao,
				accountConsentWrapper: mockAco,
			},
			args: args{
				ctx: context.Background(),
				request: &caPb.GetLinkedAaAccountsRequest{
					ActorId: "actor-id-1",
				},
				mocks: []interface{}{
					mockConsentDao.EXPECT().GetByActorIdWithStatus(gomock.Any(), "actor-id-1", []caEnumPb.ConsentStatus{caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE, caEnumPb.ConsentStatus_CONSENT_STATUS_PAUSED}).
						Return([]*caPb.Consent{}, nil),
					mockAco.EXPECT().RefreshByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).
						Return(&caPb.Consent{
							Id:               "id-1",
							ActorId:          "actor-id-1",
							ConsentRequestId: "cr-id-1",
							ConsentStatus:    caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE,
							Accounts:         testAccounts,
						}, nil,
						),
				},
			},
			want: &caPb.GetLinkedAaAccountsResponse{
				Status: rpcPb.StatusRecordNotFoundWithDebugMsg("no accounts found after merging and deduping"),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CaService{
				cDao:                  tt.fields.cDao,
				accountDao:            tt.fields.accountDao,
				accountConsentWrapper: tt.fields.accountConsentWrapper,
				txnExecutor:           mockTxnExecutor,
			}
			got, err := c.GetLinkedAaAccounts(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLinkedAaAccounts() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
				t.Errorf("Internal Error, GetConsent() got = %v, want %v", got, tt.want)
				return
			}

			if got.GetStatus().IsEqualTo(rpcPb.StatusOk()) {
				if len(got.GetAccounts()) != len(tt.want.GetAccounts()) {
					t.Errorf("Internal Error, GetConsent() got = %v, want %v", got, tt.want)
					return
				}

				for idx := range got.GetAccounts() {
					if got.GetAccounts()[idx].GetActorId() != tt.want.GetAccounts()[idx].GetActorId() {
						t.Errorf("Internal Error, GetConsent() got = %v, want %v", got, tt.want)
						return
					}
				}
			}
		})
	}
}

func TestCaService_GetConsentParams(t *testing.T) {
	t.Parallel()
	type fields struct {
		conf *genconf.Config
	}
	type args struct {
		ctx     context.Context
		request *caPb.GetConsentParamsRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *caPb.GetConsentParamsResponse
		wantErr bool
	}{
		{
			name:    "successfully got consent params",
			fields:  fields{conf: dynconf},
			args:    args{ctx: context.Background(), request: &caPb.GetConsentParamsRequest{}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CaService{
				conf:        tt.fields.conf,
				txnExecutor: mockTxnExecutor,
			}
			got, err := c.GetConsentParams(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetConsentParams() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestCaService_GetConsentsForAccount(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *caPb.GetConsentsForAccountRequest
	}
	tests := []struct {
		name    string
		mocks   func(m *mockStruct)
		args    args
		want    *caPb.GetConsentsForAccountResponse
		wantErr bool
	}{
		{
			name: "successfully got consents",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetConsentsForAccountRequest{
					AccountId: "account-id-1",
					ConsentStatusList: []caEnumPb.ConsentStatus{
						caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE,
					},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAco.EXPECT().GetConsentsForAccount(gomock.Any(), "account-id-1").
					Return([]*caPb.Consent{{
						ActorId:       "actor-id-1",
						ConsentId:     "consent-id-1",
						ConsentStatus: caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE,
					}}, nil)
				m.mockConsentRequestDao.EXPECT().GetBulk(gomock.Any(), gomock.Any()).Return([]*caPb.ConsentRequest{{ActorId: "actor-id-1", ConsentHandle: "test-consent-handle-1"}}, nil)
			},
			want: &caPb.GetConsentsForAccountResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "failed to get consents (consent mismatch)",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetConsentsForAccountRequest{
					AccountId: "account-id-2",
					ConsentStatusList: []caEnumPb.ConsentStatus{
						caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE,
					},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAco.EXPECT().GetConsentsForAccount(gomock.Any(), "account-id-2").
					Return([]*caPb.Consent{{
						ActorId:       "actor-id-2",
						ConsentId:     "consent-id-2",
						ConsentStatus: caEnumPb.ConsentStatus_CONSENT_STATUS_PAUSED,
					}}, nil)
			},
			want: &caPb.GetConsentsForAccountResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "failure: error in GetConsentsForAccount",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetConsentsForAccountRequest{
					AccountId: "account-id-1",
					ConsentStatusList: []caEnumPb.ConsentStatus{
						caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE,
					},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAco.EXPECT().GetConsentsForAccount(gomock.Any(), "account-id-1").
					Return(nil, errors.New("some random error"))
			},
			want: &caPb.GetConsentsForAccountResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error fetching consents for acc"),
			},
			wantErr: false,
		},
		{
			name: "failure: got empty list",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetConsentsForAccountRequest{
					AccountId: "account-id-1",
					ConsentStatusList: []caEnumPb.ConsentStatus{
						caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE,
					},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAco.EXPECT().GetConsentsForAccount(gomock.Any(), "account-id-1").
					Return([]*caPb.Consent{{}}, nil)
			},
			want: &caPb.GetConsentsForAccountResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "failure: error in GetBulk",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetConsentsForAccountRequest{
					AccountId: "account-id-1",
					ConsentStatusList: []caEnumPb.ConsentStatus{
						caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE,
					},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAco.EXPECT().GetConsentsForAccount(gomock.Any(), "account-id-1").
					Return([]*caPb.Consent{{
						ActorId:       "actor-id-1",
						ConsentId:     "consent-id-1",
						ConsentStatus: caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE,
					}}, nil)
				m.mockConsentRequestDao.EXPECT().GetBulk(gomock.Any(), gomock.Any()).Return(nil, errors.New("some random error"))
			},
			want: &caPb.GetConsentsForAccountResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error fetching consent requests"),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := getMocks(t)
			if tt.mocks != nil {
				tt.mocks(m)
			}
			c := &CaService{
				accountConsentWrapper: m.mockAco,
				crDao:                 m.mockConsentRequestDao,
				txnExecutor:           mockTxnExecutor,
			}
			got, err := c.GetConsentsForAccount(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetConsentsForAccount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
				if !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
					t.Errorf("Internal Error, GetConsentsForAccount() got = %v, want %v", got, tt.want)
					return
				}
			}
		})
	}
}

func TestCaService_GetDataFetchAttempts(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *caPb.GetDataFetchAttemptsRequest
	}

	tests := []struct {
		name    string
		mocks   func(m *mockStruct)
		args    args
		want    *caPb.GetDataFetchAttemptsResponse
		wantErr bool
	}{
		{
			name: "successfully got consents",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetDataFetchAttemptsRequest{
					PageContext: &rpcPb.PageContextRequest{PageSize: 1},
					Filter:      &caPb.GetDataFetchAttemptsRequest_ConsentHandle{ConsentHandle: "consent-handle-1"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockConsentRequestDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-1").
					Return(&caPb.ConsentRequest{ActorId: "actor-id-1", Id: "aa-consent-request-id-1"}, nil)
				m.mockConsentDao.EXPECT().GetByConsentRequestId(gomock.Any(), "aa-consent-request-id-1").
					Return(&caPb.Consent{ActorId: "actor-id-1", Id: "aa-consents-id-1", ConsentId: "consent-id-1"}, nil)
				m.mockDfaDao.EXPECT().GetDataFetchAttemptsPaginatedAndPurpose(gomock.Any(), "aa-consents-id-1", nil, uint32(1), gomock.Any()).
					Return([]*caPb.DataFetchAttempt{{ActorId: "actor-id-1", ConsentReferenceId: "aa-consents-id-1"}}, &rpcPb.PageContextResponse{HasAfter: false, HasBefore: false}, nil)
			},
			want:    &caPb.GetDataFetchAttemptsResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
		{
			name: "failed to get consents - 1",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetDataFetchAttemptsRequest{
					PageContext: &rpcPb.PageContextRequest{PageSize: 1},
					Filter:      &caPb.GetDataFetchAttemptsRequest_ConsentHandle{ConsentHandle: "random-consent-handle"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockConsentRequestDao.EXPECT().GetByConsentHandle(gomock.Any(), "random-consent-handle").
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &caPb.GetDataFetchAttemptsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("encountered internal error"),
			},
			wantErr: false,
		},
		{
			name: "failed to get consents - 2",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetDataFetchAttemptsRequest{
					PageContext: &rpcPb.PageContextRequest{PageSize: 1},
					Filter:      &caPb.GetDataFetchAttemptsRequest_ConsentHandle{ConsentHandle: "consent-handle-2"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockConsentRequestDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-2").
					Return(&caPb.ConsentRequest{ActorId: "actor-id-2", Id: "aa-consent-request-id-2"}, nil)
				m.mockConsentDao.EXPECT().GetByConsentRequestId(gomock.Any(), "aa-consent-request-id-2").
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &caPb.GetDataFetchAttemptsResponse{
				Status: rpcPb.StatusOk(),
				ConsentRequest: &caPb.ConsentRequest{
					ActorId: "actor-id-2",
					Id:      "aa-consent-request-id-2",
				},
			},
			wantErr: false,
		},
		{
			name: "failed to get consents - 3",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetDataFetchAttemptsRequest{
					PageContext: &rpcPb.PageContextRequest{PageSize: 1},
					Filter:      &caPb.GetDataFetchAttemptsRequest_ConsentHandle{ConsentHandle: "consent-handle-3"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockConsentRequestDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-3").
					Return(&caPb.ConsentRequest{ActorId: "actor-id-3", Id: "aa-consent-request-id-3"}, nil)
				m.mockConsentDao.EXPECT().GetByConsentRequestId(gomock.Any(), "aa-consent-request-id-3").
					Return(&caPb.Consent{ActorId: "actor-id-3", Id: "aa-consents-id-3", ConsentId: "consent-id-3"}, nil)
				m.mockDfaDao.EXPECT().GetDataFetchAttemptsPaginatedAndPurpose(gomock.Any(), "aa-consents-id-3", nil, uint32(1), gomock.Any()).
					Return(nil, nil, epifierrors.ErrRecordNotFound)
			},
			want: &caPb.GetDataFetchAttemptsResponse{
				Status: rpcPb.StatusOk(),
				ConsentRequest: &caPb.ConsentRequest{
					ActorId: "actor-id-3",
					Id:      "aa-consent-request-id-3",
				},
			},
			wantErr: false,
		},
		{
			name: "failure: GetByConsentHandle dao error other than record not found",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetDataFetchAttemptsRequest{
					PageContext: &rpcPb.PageContextRequest{PageSize: 1},
					Filter:      &caPb.GetDataFetchAttemptsRequest_ConsentHandle{ConsentHandle: "consent-handle-1"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockConsentRequestDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-1").
					Return(nil, errors.New("some random error"))
			},
			want: &caPb.GetDataFetchAttemptsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error getting page tokens"),
			},
			wantErr: false,
		},
		{
			name: "failure: GetByConsentRequestId error other than record not found",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetDataFetchAttemptsRequest{
					PageContext: &rpcPb.PageContextRequest{PageSize: 1},
					Filter:      &caPb.GetDataFetchAttemptsRequest_ConsentHandle{ConsentHandle: "consent-handle-1"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockConsentRequestDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-1").
					Return(&caPb.ConsentRequest{ActorId: "actor-id-1", Id: "aa-consent-request-id-1"}, nil)
				m.mockConsentDao.EXPECT().GetByConsentRequestId(gomock.Any(), "aa-consent-request-id-1").
					Return(&caPb.Consent{ActorId: "actor-id-1", Id: "aa-consents-id-1", ConsentId: "consent-id-1"}, errors.New("some random error"))
			},
			want: &caPb.GetDataFetchAttemptsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error getting consent request"),
			},
			wantErr: false,
		},
		{
			name: "failure: GetDataFetchAttemptsPaginated error other than record not found",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetDataFetchAttemptsRequest{
					PageContext: &rpcPb.PageContextRequest{PageSize: 1},
					Filter:      &caPb.GetDataFetchAttemptsRequest_ConsentHandle{ConsentHandle: "consent-handle-1"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockConsentRequestDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-1").
					Return(&caPb.ConsentRequest{ActorId: "actor-id-1", Id: "aa-consent-request-id-1"}, nil)
				m.mockConsentDao.EXPECT().GetByConsentRequestId(gomock.Any(), "aa-consent-request-id-1").
					Return(&caPb.Consent{ActorId: "actor-id-1", Id: "aa-consents-id-1", ConsentId: "consent-id-1"}, nil)
				m.mockDfaDao.EXPECT().GetDataFetchAttemptsPaginatedAndPurpose(gomock.Any(), "aa-consents-id-1", nil, uint32(1), gomock.Any()).
					Return(nil, nil, errors.New("some random error"))
			},
			want: &caPb.GetDataFetchAttemptsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error getting data fetch attempt"),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := getMocks(t)
			if tt.mocks != nil {
				tt.mocks(m)
			}
			c := &CaService{
				crDao:       m.mockConsentRequestDao,
				cDao:        m.mockConsentDao,
				dfaDao:      m.mockDfaDao,
				txnExecutor: mockTxnExecutor,
			}
			got, err := c.GetDataFetchAttempts(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDataFetchAttempts() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
				if !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
					t.Errorf("Internal Error, GetDataFetchAttempt() got = %v, want %v", got, tt.want)
					return
				}
			}
		})
	}
}

func TestCaService_CheckAccountSync(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx                 context.Context
		checkAccountSyncReq *caPb.CheckAccountSyncRequest
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(m *mockStruct)
		want    *caPb.CheckAccountSyncResponse
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "failure: GetById record not found",
			args: args{
				ctx:                 context.Background(),
				checkAccountSyncReq: &caPb.CheckAccountSyncRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:    &caPb.CheckAccountSyncResponse{Status: rpcPb.StatusRecordNotFound()},
			wantErr: assert.NoError,
		},
		{
			name: "error getting account by id",
			args: args{
				ctx:                 context.Background(),
				checkAccountSyncReq: &caPb.CheckAccountSyncRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error"))
			},
			want:    &caPb.CheckAccountSyncResponse{Status: rpcPb.StatusInternal()},
			wantErr: assert.NoError,
		},
		{
			name: "account sync not allowed - account data sync off paused",
			args: args{
				ctx:                 context.Background(),
				checkAccountSyncReq: &caPb.CheckAccountSyncRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{
					Id:               "780dc61a-a93e-4126-aefc-c0b2cd403da6",
					AccountStatus:    caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_OFF_PAUSED,
					AccountSubStatus: caEnumPb.AccountSubStatus_ACCOUNT_SUB_STATUS_CONSENT_PAUSED,
				}, nil)
				m.mockTxnDao.EXPECT().GetCountForAccount(gomock.Any(), gomock.Any()).Return(int32(10), nil)
			},
			want: &caPb.CheckAccountSyncResponse{Status: rpcPb.StatusOk(),
				AccountSyncStatus:       caEnumPb.AccountSyncStatus_ACCOUNT_SYNC_STATUS_INELIGIBLE,
				AccountSyncActionReason: caEnumPb.AccountSyncActionReason_ACCOUNT_SYNC_ACTION_REASON_DATA_SYNC_OFF_PAUSED,
				AccountSyncAction:       caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_DENIED,
				TransactionCount:        int32(10),
			},
			wantErr: assert.NoError,
		},
		{
			name: "account sync not allowed - account data sync off expired",
			args: args{
				ctx:                 context.Background(),
				checkAccountSyncReq: &caPb.CheckAccountSyncRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{
					Id:               "780dc61a-a93e-4126-aefc-c0b2cd403da6",
					AccountStatus:    caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_OFF_STOPPED,
					AccountSubStatus: caEnumPb.AccountSubStatus_ACCOUNT_SUB_STATUS_CONSENT_EXPIRED,
				}, nil)
				m.mockTxnDao.EXPECT().GetCountForAccount(gomock.Any(), gomock.Any()).Return(int32(10), nil)
			},
			want: &caPb.CheckAccountSyncResponse{Status: rpcPb.StatusOk(),
				AccountSyncStatus:       caEnumPb.AccountSyncStatus_ACCOUNT_SYNC_STATUS_INELIGIBLE,
				AccountSyncActionReason: caEnumPb.AccountSyncActionReason_ACCOUNT_SYNC_ACTION_REASON_DATA_SYNC_OFF_STOPPED,
				AccountSyncAction:       caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_DENIED,
				TransactionCount:        int32(10),
			},
			wantErr: assert.NoError,
		},
		{
			name: "account sync not allowed - account disconnected",
			args: args{
				ctx:                 context.Background(),
				checkAccountSyncReq: &caPb.CheckAccountSyncRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{
					Id:               "780dc61a-a93e-4126-aefc-c0b2cd403da6",
					AccountStatus:    caEnumPb.AccountStatus_ACCOUNT_STATUS_DISCONNECTED,
					AccountSubStatus: caEnumPb.AccountSubStatus_ACCOUNT_SUB_STATUS_CONSENT_REVOKED,
				}, nil)
				m.mockTxnDao.EXPECT().GetCountForAccount(gomock.Any(), gomock.Any()).Return(int32(10), nil)
			},
			want: &caPb.CheckAccountSyncResponse{Status: rpcPb.StatusOk(),
				AccountSyncStatus:       caEnumPb.AccountSyncStatus_ACCOUNT_SYNC_STATUS_INELIGIBLE,
				AccountSyncActionReason: caEnumPb.AccountSyncActionReason_ACCOUNT_SYNC_ACTION_REASON_DATA_SYNC_OFF_DISCONNECTED,
				AccountSyncAction:       caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_DENIED,
				TransactionCount:        int32(10),
			},
			wantErr: assert.NoError,
		},
		{
			name: "account sync not allowed - account deleted",
			args: args{
				ctx:                 context.Background(),
				checkAccountSyncReq: &caPb.CheckAccountSyncRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{
					Id:               "780dc61a-a93e-4126-aefc-c0b2cd403da6",
					AccountStatus:    caEnumPb.AccountStatus_ACCOUNT_STATUS_DELETED,
					AccountSubStatus: caEnumPb.AccountSubStatus_ACCOUNT_SUB_STATUS_AA_DELINK,
				}, nil)
				m.mockTxnDao.EXPECT().GetCountForAccount(gomock.Any(), gomock.Any()).Return(int32(10), nil)
			},
			want: &caPb.CheckAccountSyncResponse{Status: rpcPb.StatusOk(),
				AccountSyncStatus:       caEnumPb.AccountSyncStatus_ACCOUNT_SYNC_STATUS_INELIGIBLE,
				AccountSyncActionReason: caEnumPb.AccountSyncActionReason_ACCOUNT_SYNC_ACTION_REASON_DATA_SYNC_OFF_DELETED,
				AccountSyncAction:       caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_DENIED,
				TransactionCount:        int32(10),
			},
			wantErr: assert.NoError,
		},
		{
			name: "error getting consents for account",
			args: args{
				ctx:                 context.Background(),
				checkAccountSyncReq: &caPb.CheckAccountSyncRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{
					Id:               "780dc61a-a93e-4126-aefc-c0b2cd403da6",
					AccountStatus:    caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
					AccountSubStatus: caEnumPb.AccountSubStatus_ACCOUNT_SUB_STATUS_CONSENT_ACTIVE,
				}, nil)
				m.mockAco.EXPECT().GetConsentsForAccount(gomock.Any(), gomock.Any()).Return(nil, errors.New("err"))
			},
			want:    &caPb.CheckAccountSyncResponse{Status: rpcPb.StatusInternal()},
			wantErr: assert.NoError,
		},
		{
			name: "error getting latest attempt for consents",
			args: args{
				ctx:                 context.Background(),
				checkAccountSyncReq: &caPb.CheckAccountSyncRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{
					Id:               "780dc61a-a93e-4126-aefc-c0b2cd403da6",
					AccountStatus:    caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
					AccountSubStatus: caEnumPb.AccountSubStatus_ACCOUNT_SUB_STATUS_CONSENT_ACTIVE,
				}, nil)
				m.mockAco.EXPECT().GetConsentsForAccount(gomock.Any(), gomock.Any()).Return([]*caPb.Consent{
					{Id: "test-id"},
				}, nil)
				m.mockDfaDao.EXPECT().GetLatestAttemptForConsentsAndPurpose(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("err"))
			},
			want:    &caPb.CheckAccountSyncResponse{Status: rpcPb.StatusInternal()},
			wantErr: assert.NoError,
		},
		{
			name: "account sync not allowed - error getting transaction count",
			args: args{
				ctx:                 context.Background(),
				checkAccountSyncReq: &caPb.CheckAccountSyncRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{
					Id:               "780dc61a-a93e-4126-aefc-c0b2cd403da6",
					AccountStatus:    caEnumPb.AccountStatus_ACCOUNT_STATUS_DELETED,
					AccountSubStatus: caEnumPb.AccountSubStatus_ACCOUNT_SUB_STATUS_AA_DELINK,
				}, nil)
				m.mockTxnDao.EXPECT().GetCountForAccount(gomock.Any(), gomock.Any()).Return(int32(0), errors.New("err"))
			},
			want:    &caPb.CheckAccountSyncResponse{Status: rpcPb.StatusInternal()},
			wantErr: assert.NoError,
		},
		{
			name: "account sync not allowed - sync already in progress (FETCHED)",
			args: args{
				ctx:                 context.Background(),
				checkAccountSyncReq: &caPb.CheckAccountSyncRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{
					Id:               "780dc61a-a93e-4126-aefc-c0b2cd403da6",
					AccountStatus:    caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
					AccountSubStatus: caEnumPb.AccountSubStatus_ACCOUNT_SUB_STATUS_CONSENT_ACTIVE,
				}, nil)
				m.mockDfaDao.EXPECT().GetLatestAttemptForConsentsAndPurpose(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&caPb.DataFetchAttempt{FetchStatus: caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_FETCHED, ConsentReferenceId: "test-id"}, nil)
				m.mockTxnDao.EXPECT().GetCountForAccount(gomock.Any(), gomock.Any()).Return(int32(10), nil)
				m.mockAco.EXPECT().GetConsentsForAccount(gomock.Any(), gomock.Any()).Return([]*caPb.Consent{
					{Id: "test-id", Accounts: accList1},
				}, nil)
			},
			want: &caPb.CheckAccountSyncResponse{Status: rpcPb.StatusOk(),
				AccountSyncStatus:       caEnumPb.AccountSyncStatus_ACCOUNT_SYNC_STATUS_IN_PROGRESS,
				AccountSyncActionReason: caEnumPb.AccountSyncActionReason_ACCOUNT_SYNC_ACTION_REASON_DATA_SYNC_IN_PROGRESS,
				AccountSyncAction:       caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_DENIED,
				TransactionCount:        int32(10),
			},
			wantErr: assert.NoError,
		},
		{
			name: "account sync not allowed - sync already in progress (FAILED)",
			args: args{
				ctx:                 context.Background(),
				checkAccountSyncReq: &caPb.CheckAccountSyncRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{
					Id:               "780dc61a-a93e-4126-aefc-c0b2cd403da6",
					AccountStatus:    caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
					AccountSubStatus: caEnumPb.AccountSubStatus_ACCOUNT_SUB_STATUS_CONSENT_ACTIVE,
				}, nil)
				m.mockDfaDao.EXPECT().GetLatestAttemptForConsentsAndPurpose(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&caPb.DataFetchAttempt{FetchStatus: caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_FAILED, ConsentReferenceId: "test-id"}, nil)
				m.mockTxnDao.EXPECT().GetCountForAccount(gomock.Any(), gomock.Any()).Return(int32(10), nil)
				m.mockAco.EXPECT().GetConsentsForAccount(gomock.Any(), gomock.Any()).Return([]*caPb.Consent{
					{Id: "test-id", Accounts: accList1},
				}, nil)
			},
			want: &caPb.CheckAccountSyncResponse{Status: rpcPb.StatusOk(),
				AccountSyncStatus:       caEnumPb.AccountSyncStatus_ACCOUNT_SYNC_STATUS_IN_PROGRESS,
				AccountSyncActionReason: caEnumPb.AccountSyncActionReason_ACCOUNT_SYNC_ACTION_REASON_DATA_SYNC_IN_PROGRESS,
				AccountSyncAction:       caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_DENIED,
				TransactionCount:        int32(10),
			},
			wantErr: assert.NoError,
		},
		{
			name: "account sync not allowed - sync already in progress (DECRYPTED)",
			args: args{
				ctx:                 context.Background(),
				checkAccountSyncReq: &caPb.CheckAccountSyncRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{
					Id:               "780dc61a-a93e-4126-aefc-c0b2cd403da6",
					AccountStatus:    caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
					AccountSubStatus: caEnumPb.AccountSubStatus_ACCOUNT_SUB_STATUS_CONSENT_ACTIVE,
				}, nil)
				m.mockDfaDao.EXPECT().GetLatestAttemptForConsentsAndPurpose(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&caPb.DataFetchAttempt{FetchStatus: caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_DECRYPTED, ConsentReferenceId: "test-id"}, nil)
				m.mockTxnDao.EXPECT().GetCountForAccount(gomock.Any(), gomock.Any()).Return(int32(10), nil)
				m.mockAco.EXPECT().GetConsentsForAccount(gomock.Any(), gomock.Any()).Return([]*caPb.Consent{
					{Id: "test-id", Accounts: accList1},
				}, nil)
			},
			want: &caPb.CheckAccountSyncResponse{Status: rpcPb.StatusOk(),
				AccountSyncStatus:       caEnumPb.AccountSyncStatus_ACCOUNT_SYNC_STATUS_IN_PROGRESS,
				AccountSyncActionReason: caEnumPb.AccountSyncActionReason_ACCOUNT_SYNC_ACTION_REASON_DATA_SYNC_IN_PROGRESS,
				AccountSyncAction:       caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_DENIED,
				TransactionCount:        int32(10),
			},
			wantErr: assert.NoError,
		},
		{
			name: "account sync not allowed - limit exhausted (COMPLETED)",
			args: args{
				ctx:                 context.Background(),
				checkAccountSyncReq: &caPb.CheckAccountSyncRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{
					Id:               "780dc61a-a93e-4126-aefc-c0b2cd403da6",
					AccountStatus:    caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
					AccountSubStatus: caEnumPb.AccountSubStatus_ACCOUNT_SUB_STATUS_CONSENT_ACTIVE,
				}, nil)
				m.mockDfaDao.EXPECT().GetLatestAttemptForConsentsAndPurpose(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&caPb.DataFetchAttempt{FetchStatus: caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_COMPLETED, CreatedAt: timestampPb.Now(), ConsentReferenceId: "test-id"}, nil)
				m.mockTxnDao.EXPECT().GetCountForAccount(gomock.Any(), gomock.Any()).Return(int32(10), nil)
				m.mockAco.EXPECT().GetConsentsForAccount(gomock.Any(), gomock.Any()).Return([]*caPb.Consent{
					{Id: "test-id", Accounts: accList1},
				}, nil)
			},
			want: &caPb.CheckAccountSyncResponse{Status: rpcPb.StatusOk(),
				AccountSyncStatus:       caEnumPb.AccountSyncStatus_ACCOUNT_SYNC_STATUS_SUCCESSFUL,
				AccountSyncActionReason: caEnumPb.AccountSyncActionReason_ACCOUNT_SYNC_ACTION_REASON_LIMIT_EXHAUSTED,
				AccountSyncAction:       caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_DENIED,
				TransactionCount:        int32(10),
			},
			wantErr: assert.NoError,
		},
		{
			name: "account sync not allowed - limit exhausted (MANUAL_INTERVENTION)",
			args: args{
				ctx:                 context.Background(),
				checkAccountSyncReq: &caPb.CheckAccountSyncRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{
					Id:               "780dc61a-a93e-4126-aefc-c0b2cd403da6",
					AccountStatus:    caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
					AccountSubStatus: caEnumPb.AccountSubStatus_ACCOUNT_SUB_STATUS_CONSENT_ACTIVE,
				}, nil)
				m.mockDfaDao.EXPECT().GetLatestAttemptForConsentsAndPurpose(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&caPb.DataFetchAttempt{FetchStatus: caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_MANUAL_INTERVENTION, CreatedAt: timestampPb.Now(), ConsentReferenceId: "test-id"}, nil)
				m.mockTxnDao.EXPECT().GetCountForAccount(gomock.Any(), gomock.Any()).Return(int32(10), nil)
				m.mockAco.EXPECT().GetConsentsForAccount(gomock.Any(), gomock.Any()).Return([]*caPb.Consent{
					{Id: "test-id", Accounts: accList1},
				}, nil)
			},
			want: &caPb.CheckAccountSyncResponse{Status: rpcPb.StatusOk(),
				AccountSyncStatus:       caEnumPb.AccountSyncStatus_ACCOUNT_SYNC_STATUS_FAILED,
				AccountSyncActionReason: caEnumPb.AccountSyncActionReason_ACCOUNT_SYNC_ACTION_REASON_LIMIT_EXHAUSTED,
				AccountSyncAction:       caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_DENIED,
				TransactionCount:        int32(10),
			},
			wantErr: assert.NoError,
		},
		{
			name: "account sync allowed",
			args: args{
				ctx:                 context.Background(),
				checkAccountSyncReq: &caPb.CheckAccountSyncRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{
					Id:               "780dc61a-a93e-4126-aefc-c0b2cd403da6",
					AccountStatus:    caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
					AccountSubStatus: caEnumPb.AccountSubStatus_ACCOUNT_SUB_STATUS_CONSENT_ACTIVE,
				}, nil)
				m.mockDfaDao.EXPECT().GetLatestAttemptForConsentsAndPurpose(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&caPb.DataFetchAttempt{FetchStatus: caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_MANUAL_INTERVENTION,
						CreatedAt: timestampPb.New(time.Now().AddDate(0, 0, -2)), ConsentReferenceId: "test-id"}, nil)
				m.mockAco.EXPECT().GetConsentsForAccount(gomock.Any(), gomock.Any()).Return([]*caPb.Consent{
					{Id: "test-id", Accounts: accList1},
				}, nil)
				m.mockTxnDao.EXPECT().GetCountForAccount(gomock.Any(), gomock.Any()).Return(int32(10), nil)
			},
			want: &caPb.CheckAccountSyncResponse{Status: rpcPb.StatusOk(),
				AccountSyncStatus:       caEnumPb.AccountSyncStatus_ACCOUNT_SYNC_STATUS_FAILED,
				AccountSyncActionReason: caEnumPb.AccountSyncActionReason_ACCOUNT_SYNC_ACTION_REASON_DATA_SYNC_ON,
				AccountSyncAction:       caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_ALLOWED,
				TransactionCount:        int32(10),
			},
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := getMocks(t)
			if tt.mocks != nil {
				tt.mocks(m)
			}
			c := &CaService{
				accountDao:            m.mockAccountDao,
				txnDao:                m.mockTxnDao,
				accountConsentWrapper: m.mockAco,
				dfaDao:                m.mockDfaDao,
				conf:                  dynconf,
				txnExecutor:           mockTxnExecutor,
			}
			got, err := c.CheckAccountSync(tt.args.ctx, tt.args.checkAccountSyncReq)
			if !tt.wantErr(t, err, fmt.Sprintf("CheckAccountSync(%v, %v)", tt.args.ctx, tt.args.checkAccountSyncReq)) {
				return
			}
			assert.Equalf(t, tt.want, got, "CheckAccountSync(%v, %v)", tt.args.ctx, tt.args.checkAccountSyncReq)
		})
	}
}

func TestCaService_SyncAccount(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx        context.Context
		syncAccReq *caPb.SyncAccountRequest
	}

	tests := []struct {
		name    string
		args    args
		mocks   func(m *mockStruct)
		want    *caPb.SyncAccountResponse
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "account sync not allowed",
			args: args{
				ctx:        context.Background(),
				syncAccReq: &caPb.SyncAccountRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{
					Id:               "780dc61a-a93e-4126-aefc-c0b2cd403da6",
					AccountStatus:    caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
					AccountSubStatus: caEnumPb.AccountSubStatus_ACCOUNT_SUB_STATUS_CONSENT_ACTIVE,
				}, nil)
				m.mockDfaDao.EXPECT().GetLatestAttemptForConsentsAndPurpose(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&caPb.DataFetchAttempt{FetchStatus: caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_MANUAL_INTERVENTION,
						CreatedAt: timestampPb.Now(), ConsentReferenceId: "test-id"}, nil)
				m.mockAco.EXPECT().GetConsentsForAccount(gomock.Any(), gomock.Any()).Return([]*caPb.Consent{
					{Id: "test-id", Accounts: &caPb.Accounts{AccountList: []*caPb.Account{{
						FiType:          caEnumPb.FIType_FI_TYPE_DEPOSIT,
						FipId:           "HDFC-FIP",
						AccType:         "SAVINGS",
						LinkRefNumber:   "test-link-ref-number",
						MaskedAccNumber: "XXXXXXX989",
					}}}},
				}, nil)
			},
			want: &caPb.SyncAccountResponse{Status: rpcPb.StatusOk(),
				AccountSyncStatus:       caEnumPb.AccountSyncStatus_ACCOUNT_SYNC_STATUS_FAILED,
				AccountSyncActionReason: caEnumPb.AccountSyncActionReason_ACCOUNT_SYNC_ACTION_REASON_LIMIT_EXHAUSTED,
				AccountSyncAction:       caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_DENIED,
			},
			wantErr: assert.NoError,
		},
		{
			name: "account sync allowed, attempt in progress",
			args: args{
				ctx:        context.Background(),
				syncAccReq: &caPb.SyncAccountRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{
					Id:               "780dc61a-a93e-4126-aefc-c0b2cd403da6",
					AccountStatus:    caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
					AccountSubStatus: caEnumPb.AccountSubStatus_ACCOUNT_SUB_STATUS_CONSENT_ACTIVE,
				}, nil)
				m.mockDfaDao.EXPECT().GetLatestAttemptForConsentsAndPurpose(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&caPb.DataFetchAttempt{FetchStatus: caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_MANUAL_INTERVENTION,
						CreatedAt: timestampPb.New(time.Now().AddDate(0, 0, -2)), ConsentReferenceId: "test-id"}, nil)
				m.mockProcessor.EXPECT().CreateAttempt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), false).Return(&caPb.DataFetchAttempt{Id: "id"}, nil)
				m.mockDfaDao.EXPECT().GetByAttemptId(gomock.Any(), gomock.Any()).Return(&caPb.DataFetchAttempt{
					Id: "id", FetchStatus: caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_FETCHED}, nil).Times(4)
				m.mockTxnDao.EXPECT().GetCountForAccount(gomock.Any(), gomock.Any()).Return(int32(10), nil)
				m.mockAco.EXPECT().GetConsentsForAccount(gomock.Any(), gomock.Any()).Return([]*caPb.Consent{
					{Id: "test-id", Accounts: accList1},
				}, nil)
			},
			want: &caPb.SyncAccountResponse{Status: rpcPb.StatusOk(),
				AccountSyncStatus:       caEnumPb.AccountSyncStatus_ACCOUNT_SYNC_STATUS_IN_PROGRESS,
				AccountSyncAction:       caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_DENIED,
				AccountSyncActionReason: caEnumPb.AccountSyncActionReason_ACCOUNT_SYNC_ACTION_REASON_DATA_SYNC_IN_PROGRESS,
				TransactionCount:        int32(10),
			},
			wantErr: assert.NoError,
		},
		{
			name: "account sync allowed, create attempt failure",
			args: args{
				ctx:        context.Background(),
				syncAccReq: &caPb.SyncAccountRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{
					Id:               "780dc61a-a93e-4126-aefc-c0b2cd403da6",
					AccountStatus:    caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
					AccountSubStatus: caEnumPb.AccountSubStatus_ACCOUNT_SUB_STATUS_CONSENT_ACTIVE,
				}, nil)
				m.mockAco.EXPECT().GetConsentsForAccount(gomock.Any(), gomock.Any()).Return([]*caPb.Consent{
					{Id: "test-id", Accounts: accList1},
				}, nil)
				m.mockDfaDao.EXPECT().GetLatestAttemptForConsentsAndPurpose(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&caPb.DataFetchAttempt{FetchStatus: caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_MANUAL_INTERVENTION,
						CreatedAt: timestampPb.New(time.Now().AddDate(0, 0, -2)), ConsentReferenceId: "test-id"}, nil)
				m.mockProcessor.EXPECT().CreateAttempt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), false).Return(nil, errors.New("err"))
			},
			want:    &caPb.SyncAccountResponse{Status: rpcPb.StatusInternal()},
			wantErr: assert.NoError,
		},
		{
			name: "account sync allowed, error in polling",
			args: args{
				ctx:        context.Background(),
				syncAccReq: &caPb.SyncAccountRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{
					Id:               "780dc61a-a93e-4126-aefc-c0b2cd403da6",
					AccountStatus:    caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
					AccountSubStatus: caEnumPb.AccountSubStatus_ACCOUNT_SUB_STATUS_CONSENT_ACTIVE,
				}, nil)
				m.mockAco.EXPECT().GetConsentsForAccount(gomock.Any(), gomock.Any()).Return([]*caPb.Consent{
					{Id: "test-id", Accounts: accList1},
				}, nil)
				m.mockDfaDao.EXPECT().GetLatestAttemptForConsentsAndPurpose(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&caPb.DataFetchAttempt{FetchStatus: caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_MANUAL_INTERVENTION,
						CreatedAt: timestampPb.New(time.Now().AddDate(0, 0, -2)), ConsentReferenceId: "test-id"}, nil)
				m.mockProcessor.EXPECT().CreateAttempt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), false).Return(&caPb.DataFetchAttempt{Id: "id"}, nil)
				m.mockDfaDao.EXPECT().GetByAttemptId(gomock.Any(), gomock.Any()).Return(&caPb.DataFetchAttempt{
					Id: "id", FetchStatus: caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_FETCHED}, nil)
				m.mockDfaDao.EXPECT().GetByAttemptId(gomock.Any(), gomock.Any()).Return(nil, errors.New("err"))
			},
			want:    &caPb.SyncAccountResponse{Status: rpcPb.StatusInternal()},
			wantErr: assert.NoError,
		},
		{
			name: "failure: GetById record not found",
			args: args{
				ctx:        context.Background(),
				syncAccReq: &caPb.SyncAccountRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:    &caPb.SyncAccountResponse{Status: rpcPb.StatusRecordNotFound()},
			wantErr: assert.NoError,
		},
		{
			name: "failure: GetById error other than record not found",
			args: args{
				ctx:        context.Background(),
				syncAccReq: &caPb.SyncAccountRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("some random error"))
			},
			want:    &caPb.SyncAccountResponse{Status: rpcPb.StatusInternal()},
			wantErr: assert.NoError,
		},
		{
			name: "failure: GetCountForAccount error",
			args: args{
				ctx:        context.Background(),
				syncAccReq: &caPb.SyncAccountRequest{AccountId: "780dc61a-a93e-4126-aefc-c0b2cd403da6"},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.AaAccount{
					Id:               "780dc61a-a93e-4126-aefc-c0b2cd403da6",
					AccountStatus:    caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
					AccountSubStatus: caEnumPb.AccountSubStatus_ACCOUNT_SUB_STATUS_CONSENT_ACTIVE,
				}, nil)
				m.mockAco.EXPECT().GetConsentsForAccount(gomock.Any(), gomock.Any()).Return([]*caPb.Consent{
					{Id: "test-id", Accounts: accList1},
				}, nil)
				m.mockDfaDao.EXPECT().GetLatestAttemptForConsentsAndPurpose(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&caPb.DataFetchAttempt{FetchStatus: caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_MANUAL_INTERVENTION,
						CreatedAt: timestampPb.New(time.Now().AddDate(0, 0, -2)), ConsentReferenceId: "test-id"}, nil)
				m.mockProcessor.EXPECT().CreateAttempt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), false).Return(&caPb.DataFetchAttempt{Id: "id"}, nil)
				m.mockDfaDao.EXPECT().GetByAttemptId(gomock.Any(), gomock.Any()).Return(&caPb.DataFetchAttempt{
					Id: "id", FetchStatus: caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_FETCHED}, nil).Times(4)
				m.mockTxnDao.EXPECT().GetCountForAccount(gomock.Any(), gomock.Any()).Return(int32(0), errors.New("some random error"))
			},
			want:    &caPb.SyncAccountResponse{Status: rpcPb.StatusInternal()},
			wantErr: assert.NoError,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			m := getMocks(t)
			if tt.mocks != nil {
				tt.mocks(m)
			}
			c := &CaService{
				accountDao:            m.mockAccountDao,
				txnDao:                m.mockTxnDao,
				accountConsentWrapper: m.mockAco,
				dfaDao:                m.mockDfaDao,
				dataProcessor:         m.mockProcessor,
				conf:                  dynconf,
				txnExecutor:           mockTxnExecutor,
			}
			got, err := c.SyncAccount(tt.args.ctx, tt.args.syncAccReq)
			if !tt.wantErr(t, err, fmt.Sprintf("SyncAccount(%v, %v)", tt.args.ctx, tt.args.syncAccReq)) {
				return
			}
			assert.Equalf(t, tt.want, got, "SyncAccount(%v, %v)", tt.args.ctx, tt.args.syncAccReq)
		})
	}
}

func TestCaService_ReplayAccountEvent(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx     context.Context
		request *caPb.ReplayAccountEventRequest
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(m *mockStruct)
		want    *caPb.ReplayAccountEventResponse
		wantErr bool
	}{
		{
			name: "successfully published",
			args: args{
				ctx: context.Background(),
				request: &caPb.ReplayAccountEventRequest{
					AccountIdList: []string{"account-id-1"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetBulkById(gomock.Any(), []string{"account-id-1"}).Return(
					[]*caPb.AaAccount{
						{Id: "account-id-1"},
					}, nil,
				)
				m.mockExtAccProc.EXPECT().PublishExternalAccountEvents(gomock.Any(), gomock.Any()).Return(nil)
			},
			want: &caPb.ReplayAccountEventResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "error in publishing",
			args: args{
				ctx: context.Background(),
				request: &caPb.ReplayAccountEventRequest{
					AccountIdList: []string{"account-id-2"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetBulkById(gomock.Any(), []string{"account-id-2"}).Return(
					[]*caPb.AaAccount{
						{Id: "account-id-2"},
					}, nil,
				)
				m.mockExtAccProc.EXPECT().PublishExternalAccountEvents(gomock.Any(), gomock.Any()).Return(errors.New("pubErr"))
			},
			want: &caPb.ReplayAccountEventResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "error - not found",
			args: args{
				ctx: context.Background(),
				request: &caPb.ReplayAccountEventRequest{
					AccountIdList: []string{"account-id-3"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetBulkById(gomock.Any(), []string{"account-id-3"}).Return(
					nil, epifierrors.ErrRecordNotFound,
				)
			},
			want: &caPb.ReplayAccountEventResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "error - dao internal error",
			args: args{
				ctx: context.Background(),
				request: &caPb.ReplayAccountEventRequest{
					AccountIdList: []string{"account-id-4"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetBulkById(gomock.Any(), []string{"account-id-4"}).Return(
					nil, epifierrors.ErrContextCanceled,
				)
			},
			want: &caPb.ReplayAccountEventResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "successful - record not found for one or more",
			args: args{
				ctx: context.Background(),
				request: &caPb.ReplayAccountEventRequest{
					AccountIdList: []string{"account-id-5", "account-id-6"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetBulkById(gomock.Any(), []string{"account-id-5", "account-id-6"}).Return(
					[]*caPb.AaAccount{
						{Id: "account-id-5"},
					}, nil,
				)
				m.mockExtAccProc.EXPECT().PublishExternalAccountEvents(gomock.Any(), gomock.Any()).Return(nil)
			},
			want: &caPb.ReplayAccountEventResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := getMocks(t)
			if tt.mocks != nil {
				tt.mocks(m)
			}
			c := &CaService{
				accountDao:  m.mockAccountDao,
				accExtProc:  m.mockExtAccProc,
				txnExecutor: mockTxnExecutor,
			}
			got, err := c.ReplayAccountEvent(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("ReplayAccountEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
				if !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
					t.Errorf("Internal Error, ReplayAccountEvent() got = %v, want %v", got, tt.want)
					return
				}
			}
		})
	}
}

func TestCaService_ReplayTxnEvent(t *testing.T) {
	t.Parallel()
	txn1 := &caPb.AaTransaction{
		Id:        "id-1",
		AccountId: "account-1",
		TxnId:     "txn-1",
		ActorId:   "actor-id-1",
	}
	type args struct {
		ctx     context.Context
		request *caPb.ReplayTxnEventRequest
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(m *mockStruct)
		want    *caPb.ReplayTxnEventResponse
		wantErr bool
	}{
		{
			name: "no need to publish",
			args: args{
				ctx: context.Background(),
				request: &caPb.ReplayTxnEventRequest{
					ActorIdList:   []string{"actor-id-1"},
					CreatedAfter:  timestampPb.New(time.Now()),
					CreatedBefore: timestampPb.New(time.Now()),
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetByActorIdAndDateRange(gomock.Any(), "actor-id-1", gomock.Any(), gomock.Any()).Return(
					nil, epifierrors.ErrRecordNotFound,
				)
			},
			want: &caPb.ReplayTxnEventResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "failure: GetByActorIdAndDateRange error other than record not found",
			args: args{
				ctx: context.Background(),
				request: &caPb.ReplayTxnEventRequest{
					ActorIdList:   []string{"actor-id-1"},
					CreatedAfter:  timestampPb.New(time.Now()),
					CreatedBefore: timestampPb.New(time.Now()),
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetByActorIdAndDateRange(gomock.Any(), "actor-id-1", gomock.Any(), gomock.Any()).Return(
					nil, errors.New("some random error"),
				)
			},
			want: &caPb.ReplayTxnEventResponse{
				Status:             rpcPb.StatusOk(),
				ActorIdFailureList: []string{"actor-id-1"},
			},
			wantErr: false,
		},
		{
			name: "successfully published",
			args: args{
				ctx: context.Background(),
				request: &caPb.ReplayTxnEventRequest{
					ActorIdList:   []string{"actor-id-1"},
					CreatedAfter:  timestampPb.New(time.Now()),
					CreatedBefore: timestampPb.New(time.Now()),
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetByActorIdAndDateRange(gomock.Any(), "actor-id-1", gomock.Any(), gomock.Any()).Return(
					[]*caPb.AaTransaction{
						txn1,
					}, nil)
				m.mockFiFactory.EXPECT().GetTxnDetailsImpl(gomock.Any()).Return(m.mockFiProc, nil)
				m.mockFiProc.EXPECT().GetTxnDetails(gomock.Any(), gomock.Any()).Return(nil, nil)
				m.mockFiExtProc.EXPECT().PublishExternalTransactionEvents(gomock.Any(), gomock.Any(),
					caEnumPb.TransactionEventTypeInitiatedBy_TRANSACTION_EVENT_TYPE_INITIATED_BY_PERIODIC_DATA_PULL_TXNS).Return(nil)
			},
			want: &caPb.ReplayTxnEventResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "failure: error in factory",
			args: args{
				ctx: context.Background(),
				request: &caPb.ReplayTxnEventRequest{
					ActorIdList:   []string{"actor-id-1"},
					CreatedAfter:  timestampPb.New(time.Now()),
					CreatedBefore: timestampPb.New(time.Now()),
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetByActorIdAndDateRange(gomock.Any(), "actor-id-1", gomock.Any(), gomock.Any()).Return(
					[]*caPb.AaTransaction{
						txn1,
					}, nil)
				m.mockFiFactory.EXPECT().GetTxnDetailsImpl(gomock.Any()).Return(m.mockFiProc, errors.New("some random error"))
			},
			want: &caPb.ReplayTxnEventResponse{
				Status:             rpcPb.StatusOk(),
				ActorIdFailureList: []string{"actor-id-1"},
			},
			wantErr: false,
		},
		{
			name: "failure: error in get transaction details",
			args: args{
				ctx: context.Background(),
				request: &caPb.ReplayTxnEventRequest{
					ActorIdList:   []string{"actor-id-1"},
					CreatedAfter:  timestampPb.New(time.Now()),
					CreatedBefore: timestampPb.New(time.Now()),
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetByActorIdAndDateRange(gomock.Any(), "actor-id-1", gomock.Any(), gomock.Any()).Return(
					[]*caPb.AaTransaction{
						txn1,
					}, nil)
				m.mockFiFactory.EXPECT().GetTxnDetailsImpl(gomock.Any()).Return(m.mockFiProc, nil)
				m.mockFiProc.EXPECT().GetTxnDetails(gomock.Any(), gomock.Any()).Return(nil, errors.New("some random error"))
			},
			want: &caPb.ReplayTxnEventResponse{
				Status:             rpcPb.StatusOk(),
				ActorIdFailureList: []string{"actor-id-1"},
			},
			wantErr: false,
		},
		{
			name: "failure: error in publishing",
			args: args{
				ctx: context.Background(),
				request: &caPb.ReplayTxnEventRequest{
					ActorIdList:   []string{"actor-id-1"},
					CreatedAfter:  timestampPb.New(time.Now()),
					CreatedBefore: timestampPb.New(time.Now()),
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetByActorIdAndDateRange(gomock.Any(), "actor-id-1", gomock.Any(), gomock.Any()).Return(
					[]*caPb.AaTransaction{
						txn1,
					}, nil)
				m.mockFiFactory.EXPECT().GetTxnDetailsImpl(gomock.Any()).Return(m.mockFiProc, nil)
				m.mockFiProc.EXPECT().GetTxnDetails(gomock.Any(), gomock.Any()).Return(nil, nil)
				m.mockFiExtProc.EXPECT().PublishExternalTransactionEvents(gomock.Any(), gomock.Any(),
					caEnumPb.TransactionEventTypeInitiatedBy_TRANSACTION_EVENT_TYPE_INITIATED_BY_PERIODIC_DATA_PULL_TXNS).Return(errors.New("some random error"))
			},
			want: &caPb.ReplayTxnEventResponse{
				Status:             rpcPb.StatusOk(),
				ActorIdFailureList: []string{"actor-id-1"},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := getMocks(t)
			if tt.mocks != nil {
				tt.mocks(m)
			}
			c := &CaService{
				txnDao:      m.mockTxnDao,
				fiFactory:   m.mockFiFactory,
				fiTypeProc:  m.mockFiExtProc,
				txnExecutor: mockTxnExecutor,
			}
			got, err := c.ReplayTxnEvent(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("ReplayTxnEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
				if !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
					t.Errorf("Internal Error, ReplayAccountEvent() got = %v, want %v", got, tt.want)
					return
				}
			}
		})
	}
}

func TestCaService_StartConsentFlow(t *testing.T) {
	t.Parallel()
	var (
		authFlow    = caEnumPb.ConsentRequestPurpose_CONSENT_REQUEST_PURPOSE_AA_AUTH_FLOW
		consentFlow = caEnumPb.ConsentRequestPurpose_CONSENT_REQUEST_PURPOSE_CONSENT_FLOW
		onemoney    = caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY
		finvu       = caEnumPb.AaEntity_AA_ENTITY_AA_FINVU
	)
	const testDelay = 10
	type args struct {
		ctx                     context.Context
		startConsentFlowRequest *caPb.StartConsentFlowRequest
		mocks                   []interface{}
	}
	tests := []struct {
		name    string
		args    args
		want    *caPb.StartConsentFlowResponse
		wantErr bool
		mock    func(md *mockStruct)
	}{
		{
			name: "1 - success, finvu, auth handle",
			args: args{
				ctx: context.Background(),
				startConsentFlowRequest: &caPb.StartConsentFlowRequest{
					ActorId:                       "actor-id-1",
					Vua:                           "vua-1",
					AaEntity:                      finvu,
					ConsentRequestPurpose:         authFlow,
					CaFlowName:                    caEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT,
					NumOfConsentHandlesToGenerate: 1,
				},
			},
			mock: func(md *mockStruct) {
				md.mockConsentManager.EXPECT().GetConsentRequestForAuth(gomock.Any(), "actor-id-1", gomock.Any(), finvu, caEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT).
					Return(&caPb.ConsentRequest{
						ActorId:       "actor-id-1",
						ConsentHandle: "consent-handle-auth-1",
					}, nil)
			},
			want:    &caPb.StartConsentFlowResponse{Status: rpcPb.StatusOk(), ConsentHandle: "consent-handle-auth-1", ConsentHandleList: []string{"consent-handle-auth-1"}},
			wantErr: false,
		},
		{
			name: "2 - failed, onemoney auth handle",
			args: args{
				ctx: context.Background(),
				startConsentFlowRequest: &caPb.StartConsentFlowRequest{
					ActorId:                       "actor-id-2",
					Vua:                           "vua-2",
					AaEntity:                      onemoney,
					ConsentRequestPurpose:         authFlow,
					NumOfConsentHandlesToGenerate: 1,
				},
			},
			want:    &caPb.StartConsentFlowResponse{Status: rpcPb.StatusInternalWithDebugMsg("entity-purpose pair is invalid")},
			wantErr: false,
		},
		{
			name: "3 - failed, unspecified entity auth handle",
			args: args{
				ctx: context.Background(),
				startConsentFlowRequest: &caPb.StartConsentFlowRequest{
					AaEntity:                      caEnumPb.AaEntity_AA_ENTITY_UNSPECIFIED,
					ConsentRequestPurpose:         authFlow,
					NumOfConsentHandlesToGenerate: 1,
				},
			},
			want:    &caPb.StartConsentFlowResponse{Status: rpcPb.StatusInternalWithDebugMsg("entity-purpose pair is invalid")},
			wantErr: false,
		},
		{
			name: "failure. NumOfConsentHandlesToGenerate is less than or equal to 0",
			args: args{
				ctx: context.Background(),
				startConsentFlowRequest: &caPb.StartConsentFlowRequest{
					ActorId:               "actor-id-4",
					Vua:                   "vua-4",
					AaEntity:              onemoney,
					ConsentRequestPurpose: consentFlow,
				},
			},
			want:    &caPb.StartConsentFlowResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("NumOfConsentHandlesToGenerate in StartConsentFlowRequest cannot be less than or equal to 0")},
			wantErr: false,
		},
		{
			name: "4.1 - success, multiple onemoney consent handles",
			args: args{
				ctx: context.Background(),
				startConsentFlowRequest: &caPb.StartConsentFlowRequest{
					ActorId:                       "actor-id-4",
					Vua:                           "vua-4",
					AaEntity:                      onemoney,
					ConsentRequestPurpose:         consentFlow,
					NumOfConsentHandlesToGenerate: 3,
				},
			},
			mock: func(md *mockStruct) {
				md.mockConsentManager.EXPECT().RequestConsent(gomock.Any(), "actor-id-4", gomock.Any(), onemoney, consentFlow, gomock.Any()).
					Return(&caPb.ConsentRequest{
						ActorId:       "actor-id-4",
						ConsentHandle: "consent-handle-default-4",
						Id:            "id-4",
					}, nil).Times(3)
				md.mockEventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
				md.mockConsentProcessDelayPub.EXPECT().PublishWithDelay(gomock.Any(), &caCoPb.ProcessConsentRequest{ConsentRequestId: "id-4"}, gomock.Any()).
					Return("", nil).Times(3)
			},
			want:    &caPb.StartConsentFlowResponse{Status: rpcPb.StatusOk(), ConsentHandle: "consent-handle-default-4", ConsentHandleList: []string{"consent-handle-default-4", "consent-handle-default-4", "consent-handle-default-4"}},
			wantErr: false,
		},
		{
			name: "4 - success, onemoney consent handle",
			args: args{
				ctx: context.Background(),
				startConsentFlowRequest: &caPb.StartConsentFlowRequest{
					ActorId:                       "actor-id-4",
					Vua:                           "vua-4",
					AaEntity:                      onemoney,
					ConsentRequestPurpose:         consentFlow,
					NumOfConsentHandlesToGenerate: 1,
				},
			},
			mock: func(md *mockStruct) {
				md.mockConsentManager.EXPECT().RequestConsent(gomock.Any(), "actor-id-4", gomock.Any(), onemoney, consentFlow, gomock.Any()).
					Return(&caPb.ConsentRequest{
						ActorId:       "actor-id-4",
						ConsentHandle: "consent-handle-default-4",
						Id:            "id-4",
					}, nil)
				md.mockEventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
				md.mockConsentProcessDelayPub.EXPECT().PublishWithDelay(gomock.Any(), &caCoPb.ProcessConsentRequest{ConsentRequestId: "id-4"}, gomock.Any()).
					Return("", nil)
			},
			want:    &caPb.StartConsentFlowResponse{Status: rpcPb.StatusOk(), ConsentHandle: "consent-handle-default-4", ConsentHandleList: []string{"consent-handle-default-4"}},
			wantErr: false,
		},
		{
			name: "failure: finvu, auth handle error",
			args: args{
				ctx: context.Background(),
				startConsentFlowRequest: &caPb.StartConsentFlowRequest{
					ActorId:                       "actor-id-1",
					Vua:                           "vua-1",
					AaEntity:                      finvu,
					ConsentRequestPurpose:         authFlow,
					NumOfConsentHandlesToGenerate: 1,
				},
			},
			mock: func(md *mockStruct) {
				md.mockConsentManager.EXPECT().GetConsentRequestForAuth(gomock.Any(), "actor-id-1", gomock.Any(), finvu, gomock.Any()).
					Return(nil, errors.New("some random error"))
			},
			want:    &caPb.StartConsentFlowResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to generate consent handle (auth)")},
			wantErr: false,
		},
		{
			name: "failure, onemoney consent handle, request consent error",
			args: args{
				ctx: context.Background(),
				startConsentFlowRequest: &caPb.StartConsentFlowRequest{
					ActorId:                       "actor-id-4",
					Vua:                           "vua-4",
					AaEntity:                      onemoney,
					ConsentRequestPurpose:         consentFlow,
					NumOfConsentHandlesToGenerate: 1,
				},
			},
			mock: func(md *mockStruct) {
				md.mockConsentManager.EXPECT().RequestConsent(gomock.Any(), "actor-id-4", gomock.Any(), onemoney, consentFlow, gomock.Any()).
					Return(nil, errors.New("some random error"))
			},
			want:    &caPb.StartConsentFlowResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to generate consent handle (default)")},
			wantErr: false,
		},
		{
			name: "failure: onemoney consent handle, publish error",
			args: args{
				ctx: context.Background(),
				startConsentFlowRequest: &caPb.StartConsentFlowRequest{
					ActorId:                       "actor-id-4",
					Vua:                           "vua-4",
					AaEntity:                      onemoney,
					ConsentRequestPurpose:         consentFlow,
					NumOfConsentHandlesToGenerate: 1,
				},
			},
			mock: func(md *mockStruct) {
				md.mockConsentManager.EXPECT().RequestConsent(gomock.Any(), "actor-id-4", gomock.Any(), onemoney, consentFlow, gomock.Any()).
					Return(&caPb.ConsentRequest{
						ActorId:       "actor-id-4",
						ConsentHandle: "consent-handle-default-4",
						Id:            "id-4",
					}, nil)
				md.mockEventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
				md.mockConsentProcessDelayPub.EXPECT().PublishWithDelay(gomock.Any(), &caCoPb.ProcessConsentRequest{ConsentRequestId: "id-4"}, gomock.Any()).
					Return("", errors.New("some random error"))
			},
			want:    &caPb.StartConsentFlowResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to generate consent handle (default)")},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := getMocks(t)
			if tt.mock != nil {
				tt.mock(&mockStruct{
					mockConsentManager:         m.mockConsentManager,
					mockConsentRequestDao:      m.mockConsentRequestDao,
					mockConsentDao:             m.mockConsentDao,
					mockConsentProcessDelayPub: m.mockConsentProcessDelayPub,
					mockEventBroker:            m.mockEventBroker,
				})
			}
			c := &CaService{
				consentManager:         m.mockConsentManager,
				crDao:                  m.mockConsentRequestDao,
				cDao:                   m.mockConsentDao,
				consentProcessDelayPub: m.mockConsentProcessDelayPub,
				eventBroker:            m.mockEventBroker,
				conf:                   dynconf,
				txnExecutor:            mockTxnExecutor,
			}
			got, err := c.StartConsentFlow(tt.args.ctx, tt.args.startConsentFlowRequest)
			if (err != nil) != tt.wantErr {
				t.Errorf("StartConsentFlow() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("StartConsentFlow() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCaService_GetRelatedAccountsForDelete(t *testing.T) {
	t.Parallel()
	acc1 := &caPb.AaAccount{
		Id:            "b05815c4-35ba-4e25-902b-f73ebe1ae528",
		AccountStatus: caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
	}
	con1 := &caPb.Consent{Id: "b05815c4-35ba-4e25-902b-f73ebe1ae528", ConsentStatus: caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE}
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAco := mock_account_consent_orchestrator.NewMockAccountConsentOrchestrator(ctrl)

	type fields struct {
		UnimplementedConnectedAccountServer caPb.UnimplementedConnectedAccountServer
		accountConsentWrapper               consentorchestrator.AccountConsentOrchestrator
	}
	type args struct {
		ctx   context.Context
		req   *caPb.GetRelatedAccountsForDeleteRequest
		mocks []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *caPb.GetRelatedAccountsForDeleteResponse
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "Error getting consents and accounts for account id",
			fields: fields{
				accountConsentWrapper: mockAco,
			},
			args: args{
				ctx: context.Background(),
				req: &caPb.GetRelatedAccountsForDeleteRequest{AccountId: "b05815c4-35ba-4e25-902b-f73ebe1ae528"},
				mocks: []interface{}{
					mockAco.EXPECT().GetRelatedAccountsAndConsents(gomock.Any(), gomock.Any()).Return(nil, nil, errors.New("err")),
				},
			},
			want: &caPb.GetRelatedAccountsForDeleteResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: nil,
		},
		{
			name: "success getting consents and accounts for account id",
			fields: fields{
				accountConsentWrapper: mockAco,
			},
			args: args{
				ctx: context.Background(),
				req: &caPb.GetRelatedAccountsForDeleteRequest{AccountId: "b05815c4-35ba-4e25-902b-f73ebe1ae528"},
				mocks: []interface{}{
					mockAco.EXPECT().GetRelatedAccountsAndConsents(
						gomock.Any(), gomock.Any()).Return([]*caPb.AaAccount{acc1}, []*caPb.Consent{con1}, nil),
				},
			},
			want: &caPb.GetRelatedAccountsForDeleteResponse{Status: rpcPb.StatusOk(),
				AccountDetailList:  []*caExtPb.AccountDetails{acc1.ConvertToExternalAccountDetail(dynconf)},
				ConfirmBottomSheet: getDeleteResponseConfirmBottomSheetDeeplink([]*caExtPb.AccountDetails{acc1.ConvertToExternalAccountDetail(dynconf)}),
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CaService{
				accountConsentWrapper: tt.fields.accountConsentWrapper,
				conf:                  dynconf,
				txnExecutor:           mockTxnExecutor,
			}
			got, err := c.GetRelatedAccountsForDelete(tt.args.ctx, tt.args.req)
			if tt.wantErr != nil {
				assert.NotNil(t, err)
			}
			assert.NotNil(t, got)
		})
	}
}

func TestCaService_GetRelatedAccountsForDisconnect(t *testing.T) {
	t.Parallel()
	acc1 := &caPb.AaAccount{
		Id:            "b05815c4-35ba-4e25-902b-f73ebe1ae528",
		AccountStatus: caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
		FipId:         "sbi-fip",
	}
	type args struct {
		ctx context.Context
		req *caPb.GetRelatedAccountsForDisconnectRequest
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(m *mockStruct)
		want    *caPb.GetRelatedAccountsForDisconnectResponse
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "Error getting consents and accounts for account id",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetRelatedAccountsForDisconnectRequest{AccountId: "b05815c4-35ba-4e25-902b-f73ebe1ae528"},
			},
			mocks: func(m *mockStruct) {
				m.mockAco.EXPECT().GetRelatedDataForDisconnect(gomock.Any(), gomock.Any()).Return(nil, nil, nil, errors.New("err"))
			},
			want: &caPb.GetRelatedAccountsForDisconnectResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: nil,
		},
		{
			name: "success getting consents and accounts for account id",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetRelatedAccountsForDisconnectRequest{AccountId: "b05815c4-35ba-4e25-902b-f73ebe1ae528"},
			},
			mocks: func(m *mockStruct) {
				m.mockAco.EXPECT().GetRelatedDataForDisconnect(
					gomock.Any(), gomock.Any()).Return([]*caPb.AaAccount{acc1}, []string{"as"}, []string{"as"}, nil)
			},
			want: &caPb.GetRelatedAccountsForDisconnectResponse{
				Status:             rpcPb.StatusOk(),
				AccountDetailList:  []*caExtPb.AccountDetails{acc1.ConvertToExternalAccountDetail(dynconf)},
				ConsentHandleList:  []string{"as"},
				ConsentIdList:      []string{"as"},
				ConfirmBottomSheet: getDisconnectResponseConfirmBottomSheetDeeplink([]*caExtPb.AccountDetails{acc1.ConvertToExternalAccountDetail(dynconf)}, []string{"as"}, []string{"as"}),
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := getMocks(t)
			if tt.mocks != nil {
				tt.mocks(m)
			}
			c := &CaService{
				accountConsentWrapper: m.mockAco,
				conf:                  dynconf,
				txnExecutor:           mockTxnExecutor,
			}
			got, err := c.GetRelatedAccountsForDisconnect(tt.args.ctx, tt.args.req)
			if tt.wantErr != nil {
				assert.NotNil(t, err)
			}
			assert.NotNil(t, got)
			if tt.want != nil && got != nil {
				assert.True(t, got.GetStatus().IsEqualTo(tt.want.GetStatus()))
			}
		})
	}
}

// func TestCaService_StartDataFlowV2(t *testing.T) {
//	ctrl := gomock.NewController(t)
//	mockConsentDao := mock_dao.NewMockConsentDao(ctrl)
//	mockConsentDataRefreshPub := mocks2.NewMockPublisher(ctrl)
//	consentIdList2 := []*caPb.Consent{
//		{
//			Id: "consent-id-2-1",
//			Accounts: &caPb.Accounts{
//				AccountList: []*caPb.Account{{FipId: "HDFC-FIP"}},
//			},
//		},
//		{
//			Id: "consent-id-2-2",
//			Accounts: &caPb.Accounts{
//				AccountList: []*caPb.Account{{FipId: "HDFC-FIP"}},
//			},
//		},
//		{
//			Id: "consent-id-2-3",
//			Accounts: &caPb.Accounts{
//				AccountList: []*caPb.Account{{FipId: "HDFC-FIP"}},
//			},
//		},
//	}
//	consentIdList3 := []*caPb.Consent{
//		{
//			Id: "consent-id-3-1",
//			Accounts: &caPb.Accounts{
//				AccountList: []*caPb.Account{{FipId: "HDFC-FIP"}},
//			},
//		},
//		{
//			Id: "consent-id-3-2",
//			Accounts: &caPb.Accounts{
//				AccountList: []*caPb.Account{{FipId: "HDFC-FIP"}},
//			},
//		},
//		{
//			Id: "consent-id-3-3",
//			Accounts: &caPb.Accounts{
//				AccountList: []*caPb.Account{{FipId: "HDFC-FIP"}},
//			},
//		},
//	}
//	defer ctrl.Finish()
//	fieldMaskArgs := []caPb.ConsentFieldMask{caPb.ConsentFieldMask_CONSENT_FIELD_MASK_ID, caPb.ConsentFieldMask_CONSENT_FIELD_MASK_ACCOUNTS}
//
//	type fields struct {
//		cDao                  dao.ConsentDao
//		conf                  *genconf.Config
//		consentDataRefreshPub queue.Publisher
//	}
//	type args struct {
//		ctx   context.Context
//		req   *caPb.StartDataFlowV2Request
//		mocks []interface{}
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    *caPb.StartDataFlowV2Response
//		wantErr assert.ErrorAssertionFunc
//	}{
//		{
//			name: "1 - active ids not found in db",
//			fields: fields{
//				cDao:                  mockConsentDao,
//				consentDataRefreshPub: mockConsentDataRefreshPub,
//				conf:                  dynconf,
//			},
//			args: args{
//				ctx: context.Background(),
//				req: &caPb.StartDataFlowV2Request{},
//				mocks: []interface{}{
//					mockConsentDao.EXPECT().GetActiveConsentList(gomock.Any(), dynconf.ConsentDataRequestCountLimit(), fieldMaskArgs).Return(nil, epifierrors.ErrRecordNotFound),
//				},
//			},
//			want:    &caPb.StartDataFlowV2Response{Status: rpcPb.StatusRecordNotFound()},
//			wantErr: assert.NoError,
//		},
//		{
//			name: "2 - error publishing consent id to queue",
//			fields: fields{
//				cDao:                  mockConsentDao,
//				consentDataRefreshPub: mockConsentDataRefreshPub,
//				conf:                  dynconf,
//			},
//			args: args{
//				ctx: context.Background(),
//				req: &caPb.StartDataFlowV2Request{},
//				mocks: []interface{}{
//					mockConsentDao.EXPECT().GetActiveConsentList(gomock.Any(), dynconf.ConsentDataRequestCountLimit(), fieldMaskArgs).Return(consentIdList2, nil),
//					mockConsentDataRefreshPub.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", errors.New("err")).Times(len(consentIdList2)),
//				},
//			},
//			want:    &caPb.StartDataFlowV2Response{Status: rpcPb.StatusInternal()},
//			wantErr: assert.NoError,
//		},
//		{
//			name: "3 - successfully published consent ids to queue",
//			fields: fields{
//				cDao:                  mockConsentDao,
//				consentDataRefreshPub: mockConsentDataRefreshPub,
//				conf:                  dynconf,
//			},
//			args: args{
//				ctx: context.Background(),
//				req: &caPb.StartDataFlowV2Request{},
//				mocks: []interface{}{
//					mockConsentDao.EXPECT().GetActiveConsentList(gomock.Any(), dynconf.ConsentDataRequestCountLimit(), fieldMaskArgs).Return(consentIdList3, nil),
//					mockConsentDataRefreshPub.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("sqs-id-3-1", nil).Times(len(consentIdList3)),
//				},
//			},
//			want:    &caPb.StartDataFlowV2Response{Status: rpcPb.StatusOk()},
//			wantErr: assert.NoError,
//		},
//		{
//			name: "failure: GetActiveConsentIdList error other than record not found",
//			fields: fields{
//				cDao:                  mockConsentDao,
//				consentDataRefreshPub: mockConsentDataRefreshPub,
//				conf:                  dynconf,
//			},
//			args: args{
//				ctx: context.Background(),
//				req: &caPb.StartDataFlowV2Request{},
//				mocks: []interface{}{
//					mockConsentDao.EXPECT().GetActiveConsentList(gomock.Any(), dynconf.ConsentDataRequestCountLimit(), fieldMaskArgs).Return(nil, errors.New("some random error")),
//				},
//			},
//			want:    &caPb.StartDataFlowV2Response{Status: rpcPb.StatusInternalWithDebugMsg("error while fetching active consent id list")},
//			wantErr: assert.NoError,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			c := &CaService{
//				cDao:                  tt.fields.cDao,
//				consentDataRefreshPub: tt.fields.consentDataRefreshPub,
//				conf:                  tt.fields.conf,
//				txnExecutor:           mockTxnExecutor,
//			}
//			got, err := c.StartDataFlowV2(tt.args.ctx, tt.args.req)
//			if !tt.wantErr(t, err, fmt.Sprintf("StartDataFlowV2(%v, %v)", tt.args.ctx, tt.args.req)) {
//				return
//			}
//			if err == nil {
//				assert.NotNil(t, got)
//				if !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
//					t.Errorf("Internal Error, StartDataFlowV2() got = %v, want %v", got, tt.want)
//					return
//				}
//			}
//		})
//	}
// }

func TestCaService_GetAllowedConfig(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx     context.Context
		request *caPb.GetAllowedConfigRequest
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(m *mockStruct)
		want    *caPb.GetAllowedConfigResponse
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "1 - failed to get user properties",
			args: args{
				ctx:     context.Background(),
				request: &caPb.GetAllowedConfigRequest{ActorId: "actor-id-1"},
			},
			mocks: func(m *mockStruct) {
				m.mockCaProp.EXPECT().GetAllowedFipAndFiForActor(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil, errors.New("err"))
			},
			want:    &caPb.GetAllowedConfigResponse{Status: rpcPb.StatusInternal()},
			wantErr: assert.NoError,
		},
		// Uncomment and refactor the next test case if needed
		// {
		// 	name:   "2 - successfully got user properties",
		// 	fields: fields{caProp: nil},
		// 	args: args{
		// 		ctx:     context.Background(),
		// 		request: &caPb.GetAllowedConfigRequest{ActorId: "actor-id-2"},
		// 	},
		// 	mocks: func(m *mockStruct) {
		// 		m.mockCaProp.EXPECT().GetAllowedFipAndFiForActor(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*caExtPb.FipConfig{}, &commontypes.PhoneNumber{}, nil)
		// 	},
		// 	want: &caPb.GetAllowedConfigResponse{Status: rpcPb.StatusOk()},
		// 	wantErr: assert.NoError,
		// },
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := getMocks(t)
			if tt.mocks != nil {
				tt.mocks(m)
			}
			c := &CaService{
				caProp:      m.mockCaProp,
				txnExecutor: mockTxnExecutor,
			}
			got, err := c.GetAllowedConfig(tt.args.ctx, tt.args.request)
			if !tt.wantErr(t, err, fmt.Sprintf("GetAllowedConfig(%v, %v)", tt.args.ctx, tt.args.request)) {
				return
			}
			if err == nil {
				assert.NotNil(t, got)
				if !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
					t.Errorf("Internal Error, GetAllowedConfig() got = %v, want %v", got, tt.want)
					return
				}
			}
		})
	}
}

func TestCaService_GetAllAccounts(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *caPb.GetAllAccountsRequest
	}

	tests := []struct {
		name    string
		args    args
		mocks   func(m *mockStruct)
		want    *caPb.GetAllAccountsResponse
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "1-successful",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAllAccountsRequest{
					PageContext: &rpcPb.PageContextRequest{PageSize: 10},
					ActorId:     "actor-id-1",
					AccountFilterList: []caExtPb.AccountFilter{
						caExtPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
					},
					CreatedBefore: timestampPb.Now(),
					CreatedAfter:  timestampPb.New(time.Now().AddDate(0, 0, -2)),
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetAccountsPaginated(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*caPb.AaAccount{{
						Id:               "id-1",
						ActorId:          "actor-id-1",
						LinkedAccountRef: "linked-acc-ref-1",
						FipId:            "fiplive@indusind",
					}}, nil, nil)
			},
			want:    &caPb.GetAllAccountsResponse{Status: rpcPb.StatusOk()},
			wantErr: assert.NoError,
		},
		{
			name: "2 - no record found",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAllAccountsRequest{
					PageContext: &rpcPb.PageContextRequest{PageSize: 10},
					ActorId:     "actor-id-2",
					AccountFilterList: []caExtPb.AccountFilter{
						caExtPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
					},
					CreatedBefore: timestampPb.Now(),
					CreatedAfter:  timestampPb.New(time.Now().AddDate(0, 0, -2)),
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetAccountsPaginated(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, nil, epifierrors.ErrRecordNotFound,
				)
			},
			want:    &caPb.GetAllAccountsResponse{Status: rpcPb.StatusRecordNotFound()},
			wantErr: assert.NoError,
		},
		{
			name: "error: other than record not found",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAllAccountsRequest{
					PageContext: &rpcPb.PageContextRequest{PageSize: 10},
					ActorId:     "actor-id-1",
					AccountFilterList: []caExtPb.AccountFilter{
						caExtPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
					},
					CreatedBefore: timestampPb.Now(),
					CreatedAfter:  timestampPb.New(time.Now().AddDate(0, 0, -2)),
				},
			},
			mocks: func(m *mockStruct) {
				m.mockAccountDao.EXPECT().GetAccountsPaginated(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, nil, errors.New("some random error"),
				)
			},
			want:    &caPb.GetAllAccountsResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to get page token")},
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := getMocks(t)
			tt.mocks(m)
			c := &CaService{
				accountDao:  m.mockAccountDao,
				conf:        dynconf,
				txnExecutor: mockTxnExecutor,
			}
			got, err := c.GetAllAccounts(tt.args.ctx, tt.args.req)
			if !tt.wantErr(t, err, fmt.Sprintf("GetAllAccounts(%v, %v)", tt.args.ctx, tt.args.req)) {
				return
			}
			if err == nil {
				assert.NotNil(t, got)
				if !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
					t.Errorf("Internal Error, GetAllAccounts() got = %v, want %v", got, tt.want)
					return
				}
			}
		})
	}
}

func TestCaService_GetAaEntityForConnect(t *testing.T) {
	t.Parallel()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockCaProp := mock_user.NewMockCaProperties(ctrl)
	type fields struct {
		caProp property.CaProperties
	}
	type args struct {
		ctx   context.Context
		req   *caPb.GetAaEntityForConnectRequest
		mocks []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *caPb.GetAaEntityForConnectResponse
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "#1 actor empty",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAaEntityForConnectRequest{},
			},
			want: &caPb.GetAaEntityForConnectResponse{
				Status: rpcPb.StatusInvalidArgument(),
			},
			wantErr: assert.NoError,
		},
		{
			name: "#2 app platform unspecified",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAaEntityForConnectRequest{
					ActorId: "actor-id-1",
				},
			},
			want: &caPb.GetAaEntityForConnectResponse{
				Status: rpcPb.StatusInvalidArgument(),
			},
			wantErr: assert.NoError,
		},
		{
			name: "#3 android < min version",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAaEntityForConnectRequest{
					ActorId:     "actor-id-1",
					AppPlatform: commontypes.Platform_ANDROID,
					AppVersion:  1,
				},
			},
			want: &caPb.GetAaEntityForConnectResponse{
				Status:   rpcPb.StatusOk(),
				AaEntity: caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
			},
			wantErr: assert.NoError,
		},
		{
			name: "#4 ios < min version",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAaEntityForConnectRequest{
					ActorId:     "actor-id-1",
					AppPlatform: commontypes.Platform_IOS,
					AppVersion:  1,
				},
			},
			want: &caPb.GetAaEntityForConnectResponse{
				Status:   rpcPb.StatusOk(),
				AaEntity: caEnumPb.AaEntity_AA_ENTITY_AA_FINVU,
			},
			wantErr: assert.NoError,
		},
		{
			name: "#5 error in getting aa entity",
			fields: fields{
				caProp: mockCaProp,
			},
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAaEntityForConnectRequest{
					ActorId:     "actor-id-1",
					AppPlatform: commontypes.Platform_ANDROID,
					AppVersion:  200,
				},
				mocks: []interface{}{
					mockCaProp.EXPECT().GetAaEntityForActor(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(caEnumPb.AaEntity_AA_ENTITY_UNSPECIFIED, errors.New("error")),
				},
			},
			want: &caPb.GetAaEntityForConnectResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: assert.NoError,
		},
		{
			name: "#6 successfully got aa entity",
			fields: fields{
				caProp: mockCaProp,
			},
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAaEntityForConnectRequest{
					ActorId:     "actor-id-1",
					AppPlatform: commontypes.Platform_ANDROID,
					AppVersion:  200,
				},
				mocks: []interface{}{
					mockCaProp.EXPECT().GetAaEntityForActor(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY, nil),
				},
			},
			want: &caPb.GetAaEntityForConnectResponse{
				Status:   rpcPb.StatusOk(),
				AaEntity: caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
			},
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CaService{
				conf:        dynconf,
				caProp:      tt.fields.caProp,
				txnExecutor: mockTxnExecutor,
			}
			got, err := c.GetAaEntityForConnect(tt.args.ctx, tt.args.req)
			if !tt.wantErr(t, err, fmt.Sprintf("GetAaEntityForConnect(%v, %v)", tt.args.ctx, tt.args.req)) {
				return
			}
			assert.Equalf(t, tt.want.GetStatus(), got.GetStatus(), "GetAaEntityForConnect(%v, %v)", tt.args.ctx, tt.args.req)
			if tt.want.GetStatus().GetCode() == rpcPb.StatusOk().GetCode() {
				assert.Equalf(t, tt.want.GetAaEntity(), got.GetAaEntity(), "GetAaEntityForConnect(%v, %v)", tt.args.ctx, tt.args.req)
			}
		})
	}
}

// func TestCaService_CheckAaHeartbeatStatus(t *testing.T) {
// 	ctrl := gomock.NewController(t)
// 	defer ctrl.Finish()
// 	mockHbDao := mock_dao.NewMockAaUserHeartbeatDao(ctrl)
// 	mockHbClient := mocks.NewMockHeartbeatClient(ctrl)
// 	mockCaptureHeartbeatAndSendNotificationPub := mocks2.NewMockPublisher(ctrl)
// 	type fields struct {
// 		heartbeatDao                           dao.AaUserHeartbeatDao
// 		captureHeartbeatAndSendNotificationPub queue.Publisher
// 	}
// 	type args struct {
// 		ctx   context.Context
// 		req   *caPb.CheckAaHeartbeatStatusRequest
// 		mocks []interface{}
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		want    *caPb.CheckAaHeartbeatStatusResponse
// 		wantErr bool
// 	}{
// 		{
// 			name: "success: heartbeat up, actor exists, status DONE",
// 			fields: fields{
// 				heartbeatDao:                           mockHbDao,
// 				captureHeartbeatAndSendNotificationPub: mockCaptureHeartbeatAndSendNotificationPub,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				req: &caPb.CheckAaHeartbeatStatusRequest{
// 					ActorId:  "actor-id-1",
// 					AaEntity: caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
// 				},
// 				mocks: []interface{}{
// 					mockHbDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(&caPb.AaUserHeartbeat{
// 						ActorId:            "actor-id-1",
// 						NotificationStatus: caEnumPb.AaUserHeartbeatNotificationStatus_AA_USER_HEARTBEAT_NOTIFICATION_STATUS_DONE,
// 					}, nil),
// 					mockHbClient.EXPECT().GetHeartbeatStatus(gomock.Any(), gomock.Any()).Return(&heartbeat.GetHeartbeatStatusResponse{
// 						Status: rpcPb.StatusOk(),
// 						HeartbeatStat: &heartbeat.HeartbeatStat{
// 							HeartbeatStatus: heartbeat.HeartbeatStatus_HEARTBEAT_STATUS_UP,
// 						},
// 					}, nil),
// 				},
// 			},
// 			want: &caPb.CheckAaHeartbeatStatusResponse{
// 				Status:            rpcPb.StatusOk(),
// 				AaHeartbeatStatus: caEnumPb.AaHeartbeatStatus_AA_HEARTBEAT_STATUS_UP,
// 			},
// 			wantErr: false,
// 		},
// 		{
// 			name: "success: heartbeat up, actor exists, status IN_PROGRESS",
// 			fields: fields{
// 				heartbeatDao:                           mockHbDao,
// 				captureHeartbeatAndSendNotificationPub: mockCaptureHeartbeatAndSendNotificationPub,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				req: &caPb.CheckAaHeartbeatStatusRequest{
// 					ActorId:  "actor-id-1",
// 					AaEntity: caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
// 				},
// 				mocks: []interface{}{
// 					mockHbDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(&caPb.AaUserHeartbeat{
// 						ActorId:            "actor-id-1",
// 						NotificationStatus: caEnumPb.AaUserHeartbeatNotificationStatus_AA_USER_HEARTBEAT_NOTIFICATION_STATUS_IN_PROGRESS,
// 					}, nil),
// 					mockHbClient.EXPECT().GetHeartbeatStatus(gomock.Any(), gomock.Any()).Return(&heartbeat.GetHeartbeatStatusResponse{
// 						Status: rpcPb.StatusOk(),
// 						HeartbeatStat: &heartbeat.HeartbeatStat{
// 							HeartbeatStatus: heartbeat.HeartbeatStatus_HEARTBEAT_STATUS_UP,
// 						},
// 					}, nil),
// 					mockHbDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
// 				},
// 			},
// 			want: &caPb.CheckAaHeartbeatStatusResponse{
// 				Status:            rpcPb.StatusOk(),
// 				AaHeartbeatStatus: caEnumPb.AaHeartbeatStatus_AA_HEARTBEAT_STATUS_UP,
// 			},
// 			wantErr: false,
// 		},
// 		{
// 			name: "success: heartbeat up, actor doesn't exist",
// 			fields: fields{
// 				heartbeatDao:                           mockHbDao,
// 				captureHeartbeatAndSendNotificationPub: mockCaptureHeartbeatAndSendNotificationPub,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				req: &caPb.CheckAaHeartbeatStatusRequest{
// 					ActorId:  "actor-id-1",
// 					AaEntity: caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
// 				},
// 				mocks: []interface{}{
// 					mockHbDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
// 					mockHbClient.EXPECT().GetHeartbeatStatus(gomock.Any(), gomock.Any()).Return(&heartbeat.GetHeartbeatStatusResponse{
// 						Status: rpcPb.StatusOk(),
// 						HeartbeatStat: &heartbeat.HeartbeatStat{
// 							HeartbeatStatus: heartbeat.HeartbeatStatus_HEARTBEAT_STATUS_UP,
// 						},
// 					}, nil),
// 				},
// 			},
// 			want: &caPb.CheckAaHeartbeatStatusResponse{
// 				Status:            rpcPb.StatusOk(),
// 				AaHeartbeatStatus: caEnumPb.AaHeartbeatStatus_AA_HEARTBEAT_STATUS_UP,
// 			},
// 			wantErr: false,
// 		},
// 		{
// 			name: "failure: heartbeat up, random db error",
// 			fields: fields{
// 				heartbeatDao:                           mockHbDao,
// 				captureHeartbeatAndSendNotificationPub: mockCaptureHeartbeatAndSendNotificationPub,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				req: &caPb.CheckAaHeartbeatStatusRequest{
// 					ActorId:  "actor-id-1",
// 					AaEntity: caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
// 				},
// 				mocks: []interface{}{
// 					mockHbDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(nil, errors.New("some random error")),
// 					mockHbClient.EXPECT().GetHeartbeatStatus(gomock.Any(), gomock.Any()).Return(&heartbeat.GetHeartbeatStatusResponse{
// 						Status: rpcPb.StatusInternal(),
// 						HeartbeatStat: &heartbeat.HeartbeatStat{
// 							HeartbeatStatus: heartbeat.HeartbeatStatus_HEARTBEAT_STATUS_UP,
// 						},
// 					}, nil),
// 				},
// 			},
// 			want: &caPb.CheckAaHeartbeatStatusResponse{
// 				Status: rpcPb.StatusInternalWithDebugMsg("error in getting user from db or getting heartbeat status"),
// 			},
// 			wantErr: false,
// 		},
// 		{
// 			name: "success: heartbeat down, actor exists, status DONE",
// 			fields: fields{
// 				heartbeatDao:                           mockHbDao,
// 				captureHeartbeatAndSendNotificationPub: mockCaptureHeartbeatAndSendNotificationPub,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				req: &caPb.CheckAaHeartbeatStatusRequest{
// 					ActorId:  "actor-id-1",
// 					AaEntity: caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
// 				},
// 				mocks: []interface{}{
// 					mockHbDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(&caPb.AaUserHeartbeat{
// 						ActorId:            "actor-id-1",
// 						NotificationStatus: caEnumPb.AaUserHeartbeatNotificationStatus_AA_USER_HEARTBEAT_NOTIFICATION_STATUS_DONE,
// 					}, nil),
// 					mockHbClient.EXPECT().GetHeartbeatStatus(gomock.Any(), gomock.Any()).Return(&heartbeat.GetHeartbeatStatusResponse{
// 						Status: rpcPb.StatusOk(),
// 						HeartbeatStat: &heartbeat.HeartbeatStat{
// 							HeartbeatStatus: heartbeat.HeartbeatStatus_HEARTBEAT_STATUS_DOWN,
// 						},
// 					}, nil),
// 					mockHbDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
// 					mockCaptureHeartbeatAndSendNotificationPub.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", nil),
// 				},
// 			},
// 			want: &caPb.CheckAaHeartbeatStatusResponse{
// 				Status:            rpcPb.StatusOk(),
// 				AaHeartbeatStatus: caEnumPb.AaHeartbeatStatus_AA_HEARTBEAT_STATUS_DOWN,
// 			},
// 			wantErr: false,
// 		},
// 		{
// 			name: "success: heartbeat down, actor exists, status IN_PROGRESS",
// 			fields: fields{
// 				heartbeatDao:                           mockHbDao,
// 				captureHeartbeatAndSendNotificationPub: mockCaptureHeartbeatAndSendNotificationPub,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				req: &caPb.CheckAaHeartbeatStatusRequest{
// 					ActorId:  "actor-id-1",
// 					AaEntity: caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
// 				},
// 				mocks: []interface{}{
// 					mockHbDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(&caPb.AaUserHeartbeat{
// 						ActorId:            "actor-id-1",
// 						NotificationStatus: caEnumPb.AaUserHeartbeatNotificationStatus_AA_USER_HEARTBEAT_NOTIFICATION_STATUS_IN_PROGRESS,
// 					}, nil),
// 					mockHbClient.EXPECT().GetHeartbeatStatus(gomock.Any(), gomock.Any()).Return(&heartbeat.GetHeartbeatStatusResponse{
// 						Status: rpcPb.StatusOk(),
// 						HeartbeatStat: &heartbeat.HeartbeatStat{
// 							HeartbeatStatus: heartbeat.HeartbeatStatus_HEARTBEAT_STATUS_DOWN,
// 						},
// 					}, nil),
// 				},
// 			},
// 			want: &caPb.CheckAaHeartbeatStatusResponse{
// 				Status:            rpcPb.StatusOk(),
// 				AaHeartbeatStatus: caEnumPb.AaHeartbeatStatus_AA_HEARTBEAT_STATUS_DOWN,
// 			},
// 			wantErr: false,
// 		},
// 		{
// 			name: "success: heartbeat down, actor doesn't exists",
// 			fields: fields{
// 				heartbeatDao:                           mockHbDao,
// 				captureHeartbeatAndSendNotificationPub: mockCaptureHeartbeatAndSendNotificationPub,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				req: &caPb.CheckAaHeartbeatStatusRequest{
// 					ActorId:  "actor-id-1",
// 					AaEntity: caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
// 				},
// 				mocks: []interface{}{
// 					mockHbDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
// 					mockHbClient.EXPECT().GetHeartbeatStatus(gomock.Any(), gomock.Any()).Return(&heartbeat.GetHeartbeatStatusResponse{
// 						Status: rpcPb.StatusOk(),
// 						HeartbeatStat: &heartbeat.HeartbeatStat{
// 							HeartbeatStatus: heartbeat.HeartbeatStatus_HEARTBEAT_STATUS_DOWN,
// 						},
// 					}, nil),
// 					mockHbDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(&caPb.AaUserHeartbeat{
// 						ActorId:            "actor-id-1",
// 						NotificationStatus: caEnumPb.AaUserHeartbeatNotificationStatus_AA_USER_HEARTBEAT_NOTIFICATION_STATUS_IN_PROGRESS,
// 					}, nil),
// 					mockCaptureHeartbeatAndSendNotificationPub.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", nil),
// 				},
// 			},
// 			want: &caPb.CheckAaHeartbeatStatusResponse{
// 				Status:            rpcPb.StatusOk(),
// 				AaHeartbeatStatus: caEnumPb.AaHeartbeatStatus_AA_HEARTBEAT_STATUS_DOWN,
// 			},
// 			wantErr: false,
// 		},
// 		{
// 			name: "failure: heartbeat server error, actor exists",
// 			fields: fields{
// 				heartbeatDao:                           mockHbDao,
// 				captureHeartbeatAndSendNotificationPub: mockCaptureHeartbeatAndSendNotificationPub,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				req: &caPb.CheckAaHeartbeatStatusRequest{
// 					ActorId:  "actor-id-1",
// 					AaEntity: caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
// 				},
// 				mocks: []interface{}{
// 					mockHbDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(&caPb.AaUserHeartbeat{
// 						ActorId:            "actor-id-1",
// 						NotificationStatus: caEnumPb.AaUserHeartbeatNotificationStatus_AA_USER_HEARTBEAT_NOTIFICATION_STATUS_IN_PROGRESS,
// 					}, nil),
// 					mockHbClient.EXPECT().GetHeartbeatStatus(gomock.Any(), gomock.Any()).Return(&heartbeat.GetHeartbeatStatusResponse{
// 						Status: rpcPb.StatusInternal(),
// 					}, nil),
// 				},
// 			},
// 			want: &caPb.CheckAaHeartbeatStatusResponse{
// 				Status: rpcPb.StatusInternalWithDebugMsg("error in getting user from db or getting heartbeat status"),
// 			},
// 			wantErr: false,
// 		},
// 		{
// 			name: "failure: unspeicified aa entity",
// 			fields: fields{
// 				heartbeatDao:                           mockHbDao,
// 				captureHeartbeatAndSendNotificationPub: mockCaptureHeartbeatAndSendNotificationPub,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				req: &caPb.CheckAaHeartbeatStatusRequest{
// 					ActorId: "actor-id-1",
// 				},
// 				mocks: []interface{}{},
// 			},
// 			want: &caPb.CheckAaHeartbeatStatusResponse{
// 				Status: rpcPb.StatusInternalWithDebugMsg("aa entity is mandatory"),
// 			},
// 			wantErr: false,
// 		},
// 		{
// 			name: "failure: heartbeat down, actor doesn't exists",
// 			fields: fields{
// 				heartbeatDao:                           mockHbDao,
// 				captureHeartbeatAndSendNotificationPub: mockCaptureHeartbeatAndSendNotificationPub,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				req: &caPb.CheckAaHeartbeatStatusRequest{
// 					ActorId:  "actor-id-1",
// 					AaEntity: caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
// 				},
// 				mocks: []interface{}{
// 					mockHbDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
// 					mockHbClient.EXPECT().GetHeartbeatStatus(gomock.Any(), gomock.Any()).Return(&heartbeat.GetHeartbeatStatusResponse{
// 						Status: rpcPb.StatusOk(),
// 						HeartbeatStat: &heartbeat.HeartbeatStat{
// 							HeartbeatStatus: heartbeat.HeartbeatStatus_HEARTBEAT_STATUS_DOWN,
// 						},
// 					}, nil),
// 					mockHbDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, errors.New("random db error")),
// 				},
// 			},
// 			want: &caPb.CheckAaHeartbeatStatusResponse{
// 				Status: rpcPb.StatusInternalWithDebugMsg("error in handling aa status down"),
// 			},
// 			wantErr: false,
// 		},
// 		{
// 			name: "failure: heartbeat up, actor exists, status IN_PROGRESS",
// 			fields: fields{
// 				heartbeatDao:                           mockHbDao,
// 				captureHeartbeatAndSendNotificationPub: mockCaptureHeartbeatAndSendNotificationPub,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				req: &caPb.CheckAaHeartbeatStatusRequest{
// 					ActorId:  "actor-id-1",
// 					AaEntity: caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
// 				},
// 				mocks: []interface{}{
// 					mockHbDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(&caPb.AaUserHeartbeat{
// 						ActorId:            "actor-id-1",
// 						NotificationStatus: caEnumPb.AaUserHeartbeatNotificationStatus_AA_USER_HEARTBEAT_NOTIFICATION_STATUS_IN_PROGRESS,
// 					}, nil),
// 					mockHbClient.EXPECT().GetHeartbeatStatus(gomock.Any(), gomock.Any()).Return(&heartbeat.GetHeartbeatStatusResponse{
// 						Status: rpcPb.StatusOk(),
// 						HeartbeatStat: &heartbeat.HeartbeatStat{
// 							HeartbeatStatus: heartbeat.HeartbeatStatus_HEARTBEAT_STATUS_UP,
// 						},
// 					}, nil),
// 					mockHbDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("some random error")),
// 				},
// 			},
// 			want: &caPb.CheckAaHeartbeatStatusResponse{
// 				Status: rpcPb.StatusInternalWithDebugMsg("error in handling aa status up"),
// 			},
// 			wantErr: false,
// 		},
// 		{
// 			name: "failure: heartbeat down, actor exists, status DONE",
// 			fields: fields{
// 				heartbeatDao:                           mockHbDao,
// 				captureHeartbeatAndSendNotificationPub: mockCaptureHeartbeatAndSendNotificationPub,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				req: &caPb.CheckAaHeartbeatStatusRequest{
// 					ActorId:  "actor-id-1",
// 					AaEntity: caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
// 				},
// 				mocks: []interface{}{
// 					mockHbDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(&caPb.AaUserHeartbeat{
// 						ActorId:            "actor-id-1",
// 						NotificationStatus: caEnumPb.AaUserHeartbeatNotificationStatus_AA_USER_HEARTBEAT_NOTIFICATION_STATUS_DONE,
// 					}, nil),
// 					mockHbClient.EXPECT().GetHeartbeatStatus(gomock.Any(), gomock.Any()).Return(&heartbeat.GetHeartbeatStatusResponse{
// 						Status: rpcPb.StatusOk(),
// 						HeartbeatStat: &heartbeat.HeartbeatStat{
// 							HeartbeatStatus: heartbeat.HeartbeatStatus_HEARTBEAT_STATUS_DOWN,
// 						},
// 					}, nil),
// 					mockHbDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("some random error")),
// 				},
// 			},
// 			want: &caPb.CheckAaHeartbeatStatusResponse{
// 				Status: rpcPb.StatusInternalWithDebugMsg("error in handling aa status down"),
// 			},
// 			wantErr: false,
// 		},
// 		{
// 			name: "failure: heartbeat down, actor exists, status DONE, publish error",
// 			fields: fields{
// 				heartbeatDao:                           mockHbDao,
// 				captureHeartbeatAndSendNotificationPub: mockCaptureHeartbeatAndSendNotificationPub,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				req: &caPb.CheckAaHeartbeatStatusRequest{
// 					ActorId:  "actor-id-1",
// 					AaEntity: caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
// 				},
// 				mocks: []interface{}{
// 					mockHbDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(&caPb.AaUserHeartbeat{
// 						ActorId:            "actor-id-1",
// 						NotificationStatus: caEnumPb.AaUserHeartbeatNotificationStatus_AA_USER_HEARTBEAT_NOTIFICATION_STATUS_DONE,
// 					}, nil),
// 					mockHbClient.EXPECT().GetHeartbeatStatus(gomock.Any(), gomock.Any()).Return(&heartbeat.GetHeartbeatStatusResponse{
// 						Status: rpcPb.StatusOk(),
// 						HeartbeatStat: &heartbeat.HeartbeatStat{
// 							HeartbeatStatus: heartbeat.HeartbeatStatus_HEARTBEAT_STATUS_DOWN,
// 						},
// 					}, nil),
// 					mockHbDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
// 					mockCaptureHeartbeatAndSendNotificationPub.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", errors.New("some random error")),
// 				},
// 			},
// 			want: &caPb.CheckAaHeartbeatStatusResponse{
// 				Status: rpcPb.StatusInternalWithDebugMsg("error in handling aa status down"),
// 			},
// 			wantErr: false,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			c := &CaService{
// 				heartbeatDao:                           tt.fields.heartbeatDao,
// 				captureHeartbeatAndSendNotificationPub: tt.fields.captureHeartbeatAndSendNotificationPub,
// 				txnExecutor:                            mockTxnExecutor,
// 			}
// 			got, err := c.CheckAaHeartbeatStatus(tt.args.ctx, tt.args.req)
// 			if (err != nil) != tt.wantErr {
// 				t.Errorf("CheckAaHeartbeatStatus() error = %v, wantErr %v", err, tt.wantErr)
// 				return
// 			}
// 			if !reflect.DeepEqual(got, tt.want) {
// 				t.Errorf("CheckAaHeartbeatStatus() got = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }

func TestCaService_GetAccountDetailsBulk(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *caPb.GetAccountDetailsBulkRequest
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(m *mockStruct)
		want    *caPb.GetAccountDetailsBulkResponse
		wantErr bool
	}{
		{
			name: "#1 successful",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAccountDetailsBulkRequest{
					AccountIdList:          []string{"acc-id-1", "acc-id-2"},
					AccountDetailsMaskList: []caExtPb.AccountDetailsMask{caExtPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_SUMMARY},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockProcessor.EXPECT().GetAccountDetailsBulk(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[string]*caPb.AccountProfileSummaryDetails{
					"acc-id-1": {
						AccountDetails: &caExtPb.AccountDetails{AccountId: "acc-id-1"},
						Summary:        &caPb.AccountProfileSummaryDetails_DepositSummary{DepositSummary: &caExtPb.DepositSummary{AccountId: "acc-id-1"}},
					},
				}, nil)
			},
			want: &caPb.GetAccountDetailsBulkResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "#2 error in data factory",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAccountDetailsBulkRequest{
					AccountIdList:          []string{"acc-id-1", "acc-id-2"},
					AccountDetailsMaskList: []caExtPb.AccountDetailsMask{caExtPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_SUMMARY},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockProcessor.EXPECT().GetAccountDetailsBulk(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error in data factory"))
			},
			want: &caPb.GetAccountDetailsBulkResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := getMocks(t)
			tt.mocks(m)
			c := &CaService{
				dataProcessor: m.mockProcessor,
				txnExecutor:   mockTxnExecutor,
			}
			got, err := c.GetAccountDetailsBulk(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAccountDetailsBulk(%v, %v), got : %v, want : %v", tt.args.ctx, tt.args.req, got, tt.want)
			}
			if err == nil && !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
				t.Errorf("GetAccountDetailsBulk(%v, %v), got : %v, want : %v", tt.args.ctx, tt.args.req, got, tt.want)
			}
		})
	}
}

func TestCaService_PublishTxnEventsByTxnIds(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *caPb.PublishTxnEventsByTxnIdsRequest
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(m *mockStruct)
		want    *caPb.PublishTxnEventsByTxnIdsResponse
		wantErr bool
	}{
		{
			name: "success, 1 deposit, 1 recurring deposit and 1 term deposit",
			args: args{
				ctx: context.Background(),
				req: &caPb.PublishTxnEventsByTxnIdsRequest{
					TxnIds: []string{"txn-id-1", "txn-id-2", "txn-id-3"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetBulkByIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaTransaction{
					{
						Id:                "txn-id-1",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_DEPOSIT,
						Amount:            "420",
					},
					{
						Id:                "txn-id-2",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_RECURRING_DEPOSIT,
						Amount:            "420",
					},
					{
						Id:                "txn-id-3",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_TERM_DEPOSIT,
						Amount:            "420",
					},
				}, nil)
				m.mockDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaDepositTransaction{{
					Id:               "dep-txn-1",
					TransactionRefId: "txn-id-1",
					DepositTxnMeta:   &caPb.DepositTransactionMeta{CurrentBalance: "420"},
				}}, nil)
				m.mockRecurringDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaRecurringDepositTransaction{{
					Id:                      "recurring-dep-txn-1",
					TransactionRefId:        "txn-id-2",
					RecurringDepositTxnMeta: &caPb.RecurringDepositTransactionMeta{Balance: "420"},
				}}, nil)
				m.mockTermDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaTermDepositTransaction{{
					Id:                 "term-dep-txn-1",
					TransactionRefId:   "txn-id-3",
					TermDepositTxnMeta: &caPb.TermDepositTransactionMeta{Balance: "420"},
				}}, nil)
				m.mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", nil).Times(3)
			},
			want:    &caPb.PublishTxnEventsByTxnIdsResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
		{
			name: "error, aa txns record not found",
			args: args{
				ctx: context.Background(),
				req: &caPb.PublishTxnEventsByTxnIdsRequest{
					TxnIds: []string{"txn-id-1", "txn-id-2", "txn-id-3"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetBulkByIds(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:    &caPb.PublishTxnEventsByTxnIdsResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("no transactions found with given txn Ids")},
			wantErr: false,
		},
		{
			name: "error, aa txns other than record not found",
			args: args{
				ctx: context.Background(),
				req: &caPb.PublishTxnEventsByTxnIdsRequest{
					TxnIds: []string{"txn-id-1", "txn-id-2", "txn-id-3"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetBulkByIds(gomock.Any(), gomock.Any()).Return(nil, errors.New("some random error"))
			},
			want:    &caPb.PublishTxnEventsByTxnIdsResponse{Status: rpcPb.StatusInternalWithDebugMsg("error fetching bulk aa txns from dao")},
			wantErr: false,
		},
		{
			name: "error, missing transactions",
			args: args{
				ctx: context.Background(),
				req: &caPb.PublishTxnEventsByTxnIdsRequest{
					TxnIds: []string{"txn-id-1", "txn-id-2", "txn-id-3"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetBulkByIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaTransaction{{
					Id:                "txn-id-1",
					TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_DEPOSIT,
					Amount:            "420",
				}}, nil)
			},
			want:    &caPb.PublishTxnEventsByTxnIdsResponse{Status: rpcPb.StatusInternalWithDebugMsg("some transactions are not found in db")},
			wantErr: false,
		},
		{
			name: "error, invalid txn instrument type",
			args: args{
				ctx: context.Background(),
				req: &caPb.PublishTxnEventsByTxnIdsRequest{
					TxnIds: []string{"txn-id-1", "txn-id-2", "txn-id-3"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetBulkByIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaTransaction{
					{
						Id:                "txn-id-1",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_TYPE_UNSPECIFIED,
						Amount:            "420",
					},
					{
						Id:                "txn-id-2",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_TYPE_UNSPECIFIED,
						Amount:            "420",
					},
					{
						Id:                "txn-id-3",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_TYPE_UNSPECIFIED,
						Amount:            "420",
					},
				}, nil)
			},
			want:    &caPb.PublishTxnEventsByTxnIdsResponse{Status: rpcPb.StatusInternalWithDebugMsg("invalid txn instrument type")},
			wantErr: false,
		},
		{
			name: "error, deposit transaction dao record not found",
			args: args{
				ctx: context.Background(),
				req: &caPb.PublishTxnEventsByTxnIdsRequest{
					TxnIds: []string{"txn-id-1", "txn-id-2", "txn-id-3"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetBulkByIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaTransaction{
					{
						Id:                "txn-id-1",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_DEPOSIT,
						Amount:            "420",
					},
					{
						Id:                "txn-id-2",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_RECURRING_DEPOSIT,
						Amount:            "420",
					},
					{
						Id:                "txn-id-3",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_TERM_DEPOSIT,
						Amount:            "420",
					},
				}, nil)
				m.mockDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				m.mockRecurringDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaRecurringDepositTransaction{{
					Id:                      "recurring-dep-txn-1",
					TransactionRefId:        "txn-id-2",
					RecurringDepositTxnMeta: &caPb.RecurringDepositTransactionMeta{Balance: "420"},
				}}, nil)
				m.mockTermDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaTermDepositTransaction{{
					Id:                 "term-dep-txn-1",
					TransactionRefId:   "txn-id-3",
					TermDepositTxnMeta: &caPb.TermDepositTransactionMeta{Balance: "420"},
				}}, nil)
				m.mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", nil).Times(2)
			},
			want:    &caPb.PublishTxnEventsByTxnIdsResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
		{
			name: "error, recurring deposit dao record not found",
			args: args{
				ctx: context.Background(),
				req: &caPb.PublishTxnEventsByTxnIdsRequest{
					TxnIds: []string{"txn-id-1", "txn-id-2", "txn-id-3"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetBulkByIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaTransaction{
					{
						Id:                "txn-id-1",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_DEPOSIT,
						Amount:            "420",
					},
					{
						Id:                "txn-id-2",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_RECURRING_DEPOSIT,
						Amount:            "420",
					},
					{
						Id:                "txn-id-3",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_TERM_DEPOSIT,
						Amount:            "420",
					},
				}, nil)
				m.mockDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaDepositTransaction{{
					Id:               "dep-txn-1",
					TransactionRefId: "txn-id-1",
					DepositTxnMeta:   &caPb.DepositTransactionMeta{CurrentBalance: "420"},
				}}, nil)
				m.mockRecurringDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				m.mockTermDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaTermDepositTransaction{{
					Id:                 "term-dep-txn-1",
					TransactionRefId:   "txn-id-3",
					TermDepositTxnMeta: &caPb.TermDepositTransactionMeta{Balance: "420"},
				}}, nil)
				m.mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", nil).Times(2)
			},
			want:    &caPb.PublishTxnEventsByTxnIdsResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
		{
			name: "error, term deposit dao record not found",
			args: args{
				ctx: context.Background(),
				req: &caPb.PublishTxnEventsByTxnIdsRequest{
					TxnIds: []string{"txn-id-1", "txn-id-2", "txn-id-3"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetBulkByIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaTransaction{
					{
						Id:                "txn-id-1",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_DEPOSIT,
						Amount:            "420",
					},
					{
						Id:                "txn-id-2",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_RECURRING_DEPOSIT,
						Amount:            "420",
					},
					{
						Id:                "txn-id-3",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_TERM_DEPOSIT,
						Amount:            "420",
					},
				}, nil)
				m.mockDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaDepositTransaction{{
					Id:               "dep-txn-1",
					TransactionRefId: "txn-id-1",
					DepositTxnMeta:   &caPb.DepositTransactionMeta{CurrentBalance: "420"},
				}}, nil).Times(1)
				m.mockRecurringDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaRecurringDepositTransaction{{
					Id:                      "recurring-dep-txn-1",
					TransactionRefId:        "txn-id-2",
					RecurringDepositTxnMeta: &caPb.RecurringDepositTransactionMeta{Balance: "420"},
				}}, nil).Times(1)
				m.mockTermDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				m.mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", nil).Times(2)
			},
			want:    &caPb.PublishTxnEventsByTxnIdsResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
		{
			name: "error, deposit dao other than record not found",
			args: args{
				ctx: context.Background(),
				req: &caPb.PublishTxnEventsByTxnIdsRequest{
					TxnIds: []string{"txn-id-1", "txn-id-2", "txn-id-3"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetBulkByIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaTransaction{
					{
						Id:                "txn-id-1",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_DEPOSIT,
						Amount:            "420",
					},
					{
						Id:                "txn-id-2",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_RECURRING_DEPOSIT,
						Amount:            "420",
					},
					{
						Id:                "txn-id-3",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_TERM_DEPOSIT,
						Amount:            "420",
					},
				}, nil)
				m.mockDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return(nil, errors.New("some random error"))
				m.mockRecurringDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaRecurringDepositTransaction{{
					Id:                      "recurring-dep-txn-1",
					TransactionRefId:        "txn-id-2",
					RecurringDepositTxnMeta: &caPb.RecurringDepositTransactionMeta{Balance: "420"},
				}}, nil).Times(1)
				m.mockTermDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaTermDepositTransaction{{
					Id:                 "term-dep-txn-1",
					TransactionRefId:   "txn-id-3",
					TermDepositTxnMeta: &caPb.TermDepositTransactionMeta{Balance: "420"},
				}}, nil).Times(1)
				m.mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", nil).Times(2)
			},
			want:    &caPb.PublishTxnEventsByTxnIdsResponse{Status: rpcPb.StatusInternalWithDebugMsg("error fetching txns from dao or publish err")},
			wantErr: false,
		},
		{
			name: "error, recurring deposit dao other than record not found",
			args: args{
				ctx: context.Background(),
				req: &caPb.PublishTxnEventsByTxnIdsRequest{
					TxnIds: []string{"txn-id-1", "txn-id-2", "txn-id-3"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetBulkByIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaTransaction{
					{
						Id:                "txn-id-1",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_DEPOSIT,
						Amount:            "420",
					},
					{
						Id:                "txn-id-2",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_RECURRING_DEPOSIT,
						Amount:            "420",
					},
					{
						Id:                "txn-id-3",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_TERM_DEPOSIT,
						Amount:            "420",
					},
				}, nil)
				m.mockDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaDepositTransaction{{
					Id:               "dep-txn-1",
					TransactionRefId: "txn-id-1",
					DepositTxnMeta:   &caPb.DepositTransactionMeta{CurrentBalance: "420"},
				}}, nil).Times(1)
				m.mockRecurringDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return(nil, errors.New("some random error"))
				m.mockTermDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaTermDepositTransaction{{
					Id:                 "term-dep-txn-1",
					TransactionRefId:   "txn-id-3",
					TermDepositTxnMeta: &caPb.TermDepositTransactionMeta{Balance: "420"},
				}}, nil)
				m.mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", nil).Times(2)
			},
			want:    &caPb.PublishTxnEventsByTxnIdsResponse{Status: rpcPb.StatusInternalWithDebugMsg("error fetching txns from dao or publish err")},
			wantErr: false,
		},
		{
			name: "error, term deposit dao other than record not found",
			args: args{
				ctx: context.Background(),
				req: &caPb.PublishTxnEventsByTxnIdsRequest{
					TxnIds: []string{"txn-id-1", "txn-id-2", "txn-id-3"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetBulkByIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaTransaction{
					{
						Id:                "txn-id-1",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_DEPOSIT,
						Amount:            "420",
					},
					{
						Id:                "txn-id-2",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_RECURRING_DEPOSIT,
						Amount:            "420",
					},
					{
						Id:                "txn-id-3",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_TERM_DEPOSIT,
						Amount:            "420",
					},
				}, nil)
				m.mockDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaDepositTransaction{{
					Id:               "dep-txn-1",
					TransactionRefId: "txn-id-1",
					DepositTxnMeta:   &caPb.DepositTransactionMeta{CurrentBalance: "420"},
				}}, nil).Times(1)
				m.mockRecurringDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaRecurringDepositTransaction{{
					Id:                      "recurring-dep-txn-1",
					TransactionRefId:        "txn-id-2",
					RecurringDepositTxnMeta: &caPb.RecurringDepositTransactionMeta{Balance: "420"},
				}}, nil).Times(1)
				m.mockTermDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return(nil, errors.New("some random error"))
				m.mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", nil).Times(2)
			},
			want:    &caPb.PublishTxnEventsByTxnIdsResponse{Status: rpcPb.StatusInternalWithDebugMsg("error fetching txns from dao or publish err")},
			wantErr: false,
		},
		{
			name: "error, publish",
			args: args{
				ctx: context.Background(),
				req: &caPb.PublishTxnEventsByTxnIdsRequest{
					TxnIds: []string{"txn-id-1", "txn-id-2", "txn-id-3"},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetBulkByIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaTransaction{
					{
						Id:                "txn-id-1",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_DEPOSIT,
						Amount:            "420",
					},
					{
						Id:                "txn-id-2",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_RECURRING_DEPOSIT,
						Amount:            "420",
					},
					{
						Id:                "txn-id-3",
						TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_TERM_DEPOSIT,
						Amount:            "420",
					},
				}, nil)
				m.mockDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaDepositTransaction{{
					Id:               "dep-txn-1",
					TransactionRefId: "txn-id-1",
					DepositTxnMeta:   &caPb.DepositTransactionMeta{CurrentBalance: "420"},
				}}, nil)
				m.mockRecurringDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaRecurringDepositTransaction{{
					Id:                      "recurring-dep-txn-1",
					TransactionRefId:        "txn-id-2",
					RecurringDepositTxnMeta: &caPb.RecurringDepositTransactionMeta{Balance: "420"},
				}}, nil)
				m.mockTermDepTxnDao.EXPECT().GetBulkTransactionsByRefIds(gomock.Any(), gomock.Any()).Return([]*caPb.AaTermDepositTransaction{{
					Id:                 "term-dep-txn-1",
					TransactionRefId:   "txn-id-3",
					TermDepositTxnMeta: &caPb.TermDepositTransactionMeta{Balance: "420"},
				}}, nil)
				m.mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", errors.New("some random error")).Times(3)
			},
			want:    &caPb.PublishTxnEventsByTxnIdsResponse{Status: rpcPb.StatusInternalWithDebugMsg("error fetching txns from dao or publish err")},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			mockTxnDao := mock_dao.NewMockAaTransactionDao(ctrl)
			mockDepTxnDao := mock_dao.NewMockAaDepositFiDao(ctrl)
			mockRecurringDepTxnDao := mock_dao.NewMockAaRecurringDepositFiDao(ctrl)
			mockTermDepTxnDao := mock_dao.NewMockAaTermDepositFiDao(ctrl)
			mockPublisher := mocks2.NewMockPublisher(ctrl)
			m := &mockStruct{
				mockTxnDao:             mockTxnDao,
				mockDepTxnDao:          mockDepTxnDao,
				mockRecurringDepTxnDao: mockRecurringDepTxnDao,
				mockTermDepTxnDao:      mockTermDepTxnDao,
				mockPublisher:          mockPublisher,
			}
			if tt.mocks != nil {
				tt.mocks(m)
			}
			c := &CaService{
				txnDao:             m.mockTxnDao,
				depositTxnDao:      m.mockDepTxnDao,
				recurringDepTxnDao: m.mockRecurringDepTxnDao,
				termDepositTxnDao:  m.mockTermDepTxnDao,
				txnExternalSnsPub:  m.mockPublisher,
				txnExecutor:        mockTxnExecutor,
			}
			got, err := c.PublishTxnEventsByTxnIds(tt.args.ctx, tt.args.req)
			time.Sleep(time.Second * 5)
			if (err != nil) != tt.wantErr {
				t.Errorf("PublishTxnEventsByTxnIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PublishTxnEventsByTxnIds() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCaService_GetAuthToken(t *testing.T) {
	t.Parallel()
	ctrl := gomock.NewController(t)
	mockCaCache := mock_in_memory_store.NewMockInMemoryAaCache(ctrl)
	defer ctrl.Finish()
	type fields struct {
		caCache connectedaccount.InMemoryAaCache
		conf    *genconf.Config
	}
	type args struct {
		ctx   context.Context
		req   *caPb.GetAuthTokenRequest
		mocks []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *caPb.GetAuthTokenResponse
		wantErr bool
	}{
		{
			name: "#1 successful",
			fields: fields{
				caCache: mockCaCache,
				conf:    dynconf,
			},
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAuthTokenRequest{AaEntity: caEnumPb.AaEntity_AA_ENTITY_AA_FINVU},
				mocks: []interface{}{
					mockCaCache.EXPECT().GetAuthToken(gomock.Any(), gomock.Any(), gomock.Any()).Return("jwt-token", nil),
				},
			},
			want:    &caPb.GetAuthTokenResponse{Token: "jwt-token", Status: rpcPb.StatusOk()},
			wantErr: false,
		},
		{
			name: "#2 aa entity unsupported",
			fields: fields{
				caCache: mockCaCache,
				conf:    dynconf,
			},
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAuthTokenRequest{AaEntity: caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY},
				mocks: []interface{}{
					mockCaCache.EXPECT().GetAuthToken(gomock.Any(), gomock.Any(), gomock.Any()).Return("", epifierrors.ErrInvalidArgument),
				},
			},
			want:    &caPb.GetAuthTokenResponse{Status: rpcPb.StatusInvalidArgument()},
			wantErr: false,
		},
		{
			name: "#3 aa entity unsupported",
			fields: fields{
				caCache: mockCaCache,
				conf:    dynconf,
			},
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAuthTokenRequest{AaEntity: caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY},
				mocks: []interface{}{
					mockCaCache.EXPECT().GetAuthToken(gomock.Any(), gomock.Any(), gomock.Any()).Return("", errors.New("error getting jwt token")),
				},
			},
			want:    &caPb.GetAuthTokenResponse{Status: rpcPb.StatusInternal()},
			wantErr: false,
		},
		{
			name: "#4 unspecified aa entity",
			fields: fields{
				caCache: mockCaCache,
				conf:    dynconf,
			},
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAuthTokenRequest{AaEntity: caEnumPb.AaEntity_AA_ENTITY_UNSPECIFIED},
			},
			want:    &caPb.GetAuthTokenResponse{Status: rpcPb.StatusInvalidArgument()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CaService{
				caCache:     tt.fields.caCache,
				conf:        tt.fields.conf,
				txnExecutor: mockTxnExecutor,
			}
			got, err := c.GetAuthToken(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetJwtToken() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
				t.Errorf("GetJwtToken() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCaService_GetTxnDetails(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *caPb.GetTxnDetailsRequest
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(m *mockStruct)
		want    *caPb.GetTxnDetailsResponse
		wantErr bool
	}{
		{
			name: "#1 not found",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetTxnDetailsRequest{Id: "random-id"},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:    &caPb.GetTxnDetailsResponse{Status: rpcPb.StatusRecordNotFound()},
			wantErr: false,
		},
		{
			name: "#2 error in txn dao",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetTxnDetailsRequest{Id: "id-1"},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(nil, errors.New("error in txn dao"))
			},
			want:    &caPb.GetTxnDetailsResponse{Status: rpcPb.StatusInternal()},
			wantErr: false,
		},
		{
			name: "#3 successful",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetTxnDetailsRequest{Id: "id-1"},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&caPb.AaTransaction{Id: "id-1"}, nil)
			},
			want:    &caPb.GetTxnDetailsResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := getMocks(t)
			tt.mocks(m)
			c := &CaService{
				txnDao:      m.mockTxnDao,
				txnExecutor: mockTxnExecutor,
			}
			got, err := c.GetTxnDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetTxnDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
				t.Errorf("GetTxnDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCaService_GetTotalTransactionsCount(t *testing.T) {
	t.Parallel()
	totalTxnResp1 := []*caPb.AccountIdAndTotalTransactions{
		{
			AccountReferenceId: "account_ref_id",
			TotalTransactions:  20,
		},
	}
	totalTxnResp2 := []*caPb.AccountIdAndTotalTransactions{
		{
			AccountReferenceId: "account_ref_id",
			TotalTransactions:  0,
		},
	}
	type args struct {
		ctx context.Context
		req *caPb.GetTotalTransactionsCountRequest
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(m *mockStruct)
		want    *caPb.GetTotalTransactionsCountResponse
		wantErr bool
	}{
		{
			name: "#1 actorId or account_reference id is empty",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetTotalTransactionsCountRequest{
					ActorId: "",
				},
			},
			mocks:   func(m *mockStruct) {},
			want:    &caPb.GetTotalTransactionsCountResponse{Status: rpcPb.StatusInvalidArgument()},
			wantErr: false,
		},
		{
			name: "#2 error in txn dao",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetTotalTransactionsCountRequest{
					ActorId:            "actor_id",
					AccountReferenceId: []string{"account_ref_id"},
					StartTime: &timestampPb.Timestamp{
						Seconds: **********,
						Nanos:   0,
					},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetTotalTxnByActorIdAndAccRefId(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error in txn dao"))
			},
			want:    &caPb.GetTotalTransactionsCountResponse{Status: rpcPb.StatusInternal()},
			wantErr: false,
		},
		{
			name: "#3 successful",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetTotalTransactionsCountRequest{
					ActorId:            "actor_id",
					AccountReferenceId: []string{"account_ref_id"},
					StartTime: &timestampPb.Timestamp{
						Seconds: **********,
						Nanos:   0,
					},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetTotalTxnByActorIdAndAccRefId(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(totalTxnResp1, nil)
			},
			want: &caPb.GetTotalTransactionsCountResponse{
				Status:                        rpcPb.StatusOk(),
				AccountIdAndTotalTransactions: totalTxnResp1,
			},
			wantErr: false,
		},
		{
			name: "#4 invalid actor id - not record is present",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetTotalTransactionsCountRequest{
					ActorId:            "random_actor_id",
					AccountReferenceId: []string{"random_account_ref_id"},
					StartTime: &timestampPb.Timestamp{
						Seconds: **********,
						Nanos:   0,
					},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetTotalTxnByActorIdAndAccRefId(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(totalTxnResp2, nil)
			},
			want:    &caPb.GetTotalTransactionsCountResponse{Status: rpcPb.StatusOk(), AccountIdAndTotalTransactions: totalTxnResp2},
			wantErr: false,
		},
		{
			name: "#5 when account_reference_id is empty",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetTotalTransactionsCountRequest{
					ActorId:            "actor_id",
					AccountReferenceId: []string{},
					StartTime: &timestampPb.Timestamp{
						Seconds: **********,
						Nanos:   0,
					},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockTxnDao.EXPECT().GetTotalTxnByActorIdAndAccRefId(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(totalTxnResp1, nil)
			},
			want: &caPb.GetTotalTransactionsCountResponse{
				Status:                        rpcPb.StatusOk(),
				AccountIdAndTotalTransactions: totalTxnResp1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := getMocks(t)
			if tt.mocks != nil {
				tt.mocks(m)
			}
			s := &CaService{
				txnDao:      m.mockTxnDao,
				txnExecutor: mockTxnExecutor,
			}
			got, err := s.GetTotalTransactionsCount(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetTotalTransactionsCount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				opts := []cmp.Option{
					protocmp.Transform(),
					cmpopts.IgnoreFields(caPb.AaTransaction{}, "CreatedAt", "UpdatedAt"),
				}
				if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
					t.Errorf("GetTotalTransactionsCount() got = %v,\n want %v", got, tt.want)
				}
			}
		})
	}
}

func TestCaService_GetScreenerDecision(t *testing.T) {
	t.Parallel()
	ctx := epificontext.CtxWithAppPlatform(context.Background(), commontypes.Platform_ANDROID)
	ctx = epificontext.CtxWithAppVersionCode(ctx, "277")
	actorId := "actor-id"
	pageCtx := &rpcPb.PageContextRequest{
		PageSize: 1,
	}
	token, _ := pagination.GetPageToken(pageCtx)

	aaProfile := &caPb.Profile{
		Holders: &caPb.Holders{
			HolderType: caEnumPb.HolderType_HOLDER_TYPE_SINGLE,
			Holder: []*caPb.Holder{
				{
					CkycCompliance: true,
					Name:           "first last",
					Pan:            "APAN",
				}},
		},
	}
	accountStatuses := []caEnumPb.AccountStatus{
		caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
		caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_PENDING, +caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_OFF_PAUSED,
		caEnumPb.AccountStatus_ACCOUNT_STATUS_DISCONNECTED,
		caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_OFF_STOPPED,
	}
	aaAcc1 := &caPb.AaAccount{
		Id:            "aa1",
		ActorId:       actorId,
		AccountStatus: caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_PENDING,
		LastSyncedAt:  nil,
		Profile:       aaProfile,
	}
	aaAcc2 := &caPb.AaAccount{
		Id:            "aa2",
		ActorId:       actorId,
		AccountStatus: caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
		LastSyncedAt:  timestampPb.Now(),
		Profile:       aaProfile,
	}
	aaAcc3 := &caPb.AaAccount{
		Id:            "aa3",
		ActorId:       actorId,
		AccountStatus: caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
		LastSyncedAt:  timestampPb.Now(),
		Profile:       aaProfile,
	}
	aaAcc4 := &caPb.AaAccount{
		Id:            "aa4",
		ActorId:       actorId,
		AccountStatus: caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
		LastSyncedAt:  timestampPb.Now(),
		Profile:       aaProfile,
	}
	type args struct {
		ctx   context.Context
		req   *caPb.GetScreenerDecisionRequest
		mocks func(mockStruct *mockStruct)
	}
	tests := []struct {
		name    string
		args    args
		want    *caPb.GetScreenerDecisionResponse
		wantErr error
	}{
		{
			name: "data sync in progress",
			args: args{
				ctx: ctx,
				req: &caPb.GetScreenerDecisionRequest{
					ActorId: actorId,
				},
				mocks: func(m *mockStruct) {
					m.mockAccountDao.EXPECT().GetAccountsPaginated(gomock.Any(), &dao.GetAccountsPaginatedReq{
						ActorId:            actorId,
						AccountStatuses:    accountStatuses,
						PageToken:          token,
						PageSize:           pageCtx.GetPageSize(),
						IsFiFedAccIncluded: false,
					}, nil).
						Return([]*caPb.AaAccount{aaAcc1}, nil, nil)
				},
			},
			want: &caPb.GetScreenerDecisionResponse{
				Status: rpcPb.ExtendedStatusInProgress(),
			},
			wantErr: nil,
		},
		{
			name: "no accounts found for actor",
			args: args{
				ctx: ctx,
				req: &caPb.GetScreenerDecisionRequest{
					ActorId: actorId,
				},
				mocks: func(m *mockStruct) {
					m.mockAccountDao.EXPECT().GetAccountsPaginated(gomock.Any(), &dao.GetAccountsPaginatedReq{
						ActorId:            actorId,
						AccountStatuses:    accountStatuses,
						PageToken:          token,
						PageSize:           pageCtx.GetPageSize(),
						IsFiFedAccIncluded: false,
					}, nil).
						Return(nil, nil, epifierrors.ErrRecordNotFound)
				},
			},
			want: &caPb.GetScreenerDecisionResponse{
				Status: rpcPb.StatusRecordNotFoundWithDebugMsg("could not find connected accounts for user"),
			},
			wantErr: nil,
		},
		{
			name: "error while getting accounts",
			args: args{
				ctx: ctx,
				req: &caPb.GetScreenerDecisionRequest{
					ActorId: actorId,
				},
				mocks: func(m *mockStruct) {
					m.mockAccountDao.EXPECT().GetAccountsPaginated(gomock.Any(), &dao.GetAccountsPaginatedReq{
						ActorId:            actorId,
						AccountStatuses:    accountStatuses,
						PageToken:          token,
						PageSize:           pageCtx.GetPageSize(),
						IsFiFedAccIncluded: false,
					}, nil).
						Return(nil, nil, epifierrors.ErrInvalidSQL)
				},
			},
			want: &caPb.GetScreenerDecisionResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error while getting accounts for actor"),
			},
			wantErr: nil,
		},
		{
			name: "error while getting deposit summary",
			args: args{
				ctx: ctx,
				req: &caPb.GetScreenerDecisionRequest{
					ActorId: actorId,
				},
				mocks: func(m *mockStruct) {
					m.mockAccountDao.EXPECT().GetAccountsPaginated(gomock.Any(), &dao.GetAccountsPaginatedReq{
						ActorId:            actorId,
						AccountStatuses:    accountStatuses,
						PageToken:          token,
						PageSize:           pageCtx.GetPageSize(),
						IsFiFedAccIncluded: false,
					}, nil).
						Return([]*caPb.AaAccount{aaAcc2, aaAcc3, aaAcc4}, nil, nil)
					m.mockUIClient.EXPECT().GetUserIntel(gomock.Any(), &userintel.GetUserIntelRequest{
						ActorId:     actorId,
						IntelType:   userintel.IntelType_INTEL_TYPE_AA_INCOME_ESTIMATE,
						AccessType:  userintel.GetUserIntelAccessType_ACCESS_TYPE_FORCE_CACHE,
						ClientReqId: "check-attempt-id",
					}).Return(&userintel.GetUserIntelResponse{
						Status: rpcPb.StatusOk(),
						UserIntel: &userintel.UserIntel{
							IntelData: &userintel.IntelData{
								IntelDataValue: &userintel.IntelData_AAIncomeEstimateData{
									AAIncomeEstimateData: &userintel.AAIncomeEstimateData{
										PredictedIncome: &typespb.Money{
											CurrencyCode: "INR",
											Units:        25000,
										},
									},
								},
							},
							Status: userintel.UserIntelStatus_USER_INTEL_STATUS_AVAILABLE,
						},
					}, nil)
					m.mockScreenerClient.EXPECT().GetScreenerAttemptsByActorId(gomock.Any(), &screener.GetScreenerAttemptsByActorIdRequest{
						ActorId:    actorId,
						CachedData: true,
					}).Return(&screener.GetScreenerAttemptsByActorIdResponse{
						Status: rpcPb.StatusOk(),
						ChecksMap: []*screener.CheckDetails{{
							CheckType:      screener.CheckType_CHECK_TYPE_CONNECTED_ACCOUNTS,
							CheckAttemptId: "check-attempt-id",
							CheckResult:    screener.CheckResult_CHECK_RESULT_INITIATED,
						}},
					}, nil)
				},
			},
			want: &caPb.GetScreenerDecisionResponse{
				Status:       rpcPb.StatusOk(),
				ScreenerPass: commontypes.BooleanEnum_TRUE,
			},
			wantErr: nil,
		},
		{
			name: "screener pass",
			args: args{
				ctx: ctx,
				req: &caPb.GetScreenerDecisionRequest{
					ActorId: actorId,
				},
				mocks: func(m *mockStruct) {
					m.mockScreenerClient.EXPECT().GetScreenerAttemptsByActorId(gomock.Any(), &screener.GetScreenerAttemptsByActorIdRequest{
						ActorId:    actorId,
						CachedData: true,
					}).Return(&screener.GetScreenerAttemptsByActorIdResponse{
						Status: rpcPb.StatusOk(),
						ChecksMap: []*screener.CheckDetails{{
							CheckType:      screener.CheckType_CHECK_TYPE_CONNECTED_ACCOUNTS,
							CheckAttemptId: "check-attempt-id",
							CheckResult:    screener.CheckResult_CHECK_RESULT_IN_PROGRESS,
						}},
					}, nil)
					m.mockAccountDao.EXPECT().GetAccountsPaginated(gomock.Any(), &dao.GetAccountsPaginatedReq{
						ActorId:            actorId,
						AccountStatuses:    accountStatuses,
						PageToken:          token,
						PageSize:           pageCtx.GetPageSize(),
						IsFiFedAccIncluded: false,
					}, nil).
						Return([]*caPb.AaAccount{aaAcc2, aaAcc3, aaAcc4}, nil, nil)
					m.mockUIClient.EXPECT().GetUserIntel(gomock.Any(), &userintel.GetUserIntelRequest{
						ActorId:     actorId,
						IntelType:   userintel.IntelType_INTEL_TYPE_AA_INCOME_ESTIMATE,
						AccessType:  userintel.GetUserIntelAccessType_ACCESS_TYPE_FORCE_CACHE,
						ClientReqId: "check-attempt-id",
					}).Return(&userintel.GetUserIntelResponse{
						Status: rpcPb.StatusOk(),
						UserIntel: &userintel.UserIntel{
							IntelData: &userintel.IntelData{
								IntelDataValue: &userintel.IntelData_AAIncomeEstimateData{
									AAIncomeEstimateData: &userintel.AAIncomeEstimateData{
										PredictedIncome: &typespb.Money{
											CurrencyCode: "INR",
											Units:        25000,
										},
									},
								},
							},
							Status: userintel.UserIntelStatus_USER_INTEL_STATUS_AVAILABLE,
						},
					}, nil)
				},
			},
			want: &caPb.GetScreenerDecisionResponse{
				Status:       rpcPb.StatusOk(),
				ScreenerPass: commontypes.BooleanEnum_TRUE,
			},
			wantErr: nil,
		},
		{
			name: "actor id not present in request",
			args: args{
				ctx:   ctx,
				req:   &caPb.GetScreenerDecisionRequest{},
				mocks: func(m *mockStruct) {},
			},
			want: &caPb.GetScreenerDecisionResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("actor id not present"),
			},
			wantErr: nil,
		},
		{
			name: "income estimation in progress",
			args: args{
				ctx: ctx,
				req: &caPb.GetScreenerDecisionRequest{
					ActorId: actorId,
				},
				mocks: func(m *mockStruct) {
					m.mockScreenerClient.EXPECT().GetScreenerAttemptsByActorId(gomock.Any(), &screener.GetScreenerAttemptsByActorIdRequest{
						ActorId:    actorId,
						CachedData: true,
					}).Return(&screener.GetScreenerAttemptsByActorIdResponse{
						Status: rpcPb.StatusOk(),
						ChecksMap: []*screener.CheckDetails{{
							CheckType:      screener.CheckType_CHECK_TYPE_CONNECTED_ACCOUNTS,
							CheckAttemptId: "check-attempt-id",
							CheckResult:    screener.CheckResult_CHECK_RESULT_IN_PROGRESS,
						}},
					}, nil)
					m.mockAccountDao.EXPECT().GetAccountsPaginated(gomock.Any(), &dao.GetAccountsPaginatedReq{
						ActorId:            actorId,
						AccountStatuses:    accountStatuses,
						PageToken:          token,
						PageSize:           pageCtx.GetPageSize(),
						IsFiFedAccIncluded: false,
					}, nil).
						Return([]*caPb.AaAccount{aaAcc2, aaAcc3, aaAcc4}, nil, nil)
					m.mockUIClient.EXPECT().GetUserIntel(gomock.Any(), &userintel.GetUserIntelRequest{
						ActorId:     actorId,
						IntelType:   userintel.IntelType_INTEL_TYPE_AA_INCOME_ESTIMATE,
						ClientReqId: "check-attempt-id",
						AccessType:  userintel.GetUserIntelAccessType_ACCESS_TYPE_FORCE_CACHE,
					}).Return(&userintel.GetUserIntelResponse{
						Status: rpcPb.StatusOk(),
						UserIntel: &userintel.UserIntel{
							Status: userintel.UserIntelStatus_USER_INTEL_STATUS_IN_PROGRESS,
						},
					}, nil)
				},
			},
			want: &caPb.GetScreenerDecisionResponse{
				Status: rpcPb.ExtendedStatusInProgress(),
			},
			wantErr: nil,
		},
		{
			name: "screener pass by income estimator",
			args: args{
				ctx: ctx,
				req: &caPb.GetScreenerDecisionRequest{
					ActorId: actorId,
				},
				mocks: func(m *mockStruct) {
					m.mockScreenerClient.EXPECT().GetScreenerAttemptsByActorId(gomock.Any(), &screener.GetScreenerAttemptsByActorIdRequest{
						ActorId:    actorId,
						CachedData: true,
					}).Return(&screener.GetScreenerAttemptsByActorIdResponse{
						Status: rpcPb.StatusOk(),
						ChecksMap: []*screener.CheckDetails{{
							CheckType:      screener.CheckType_CHECK_TYPE_CONNECTED_ACCOUNTS,
							CheckAttemptId: "check-attempt-id",
							CheckResult:    screener.CheckResult_CHECK_RESULT_INITIATED,
						}},
					}, nil)
					m.mockAccountDao.EXPECT().GetAccountsPaginated(gomock.Any(), &dao.GetAccountsPaginatedReq{
						ActorId:            actorId,
						AccountStatuses:    accountStatuses,
						PageToken:          token,
						PageSize:           pageCtx.GetPageSize(),
						IsFiFedAccIncluded: false,
					}, nil).
						Return([]*caPb.AaAccount{aaAcc2, aaAcc3, aaAcc4}, nil, nil)
					m.mockUIClient.EXPECT().GetUserIntel(gomock.Any(), &userintel.GetUserIntelRequest{
						ActorId:     actorId,
						IntelType:   userintel.IntelType_INTEL_TYPE_AA_INCOME_ESTIMATE,
						AccessType:  userintel.GetUserIntelAccessType_ACCESS_TYPE_FORCE_CACHE,
						ClientReqId: "check-attempt-id",
					}).Return(&userintel.GetUserIntelResponse{
						Status: rpcPb.StatusOk(),
						UserIntel: &userintel.UserIntel{
							IntelData: &userintel.IntelData{
								IntelDataValue: &userintel.IntelData_AAIncomeEstimateData{
									AAIncomeEstimateData: &userintel.AAIncomeEstimateData{
										PredictedIncome: &typespb.Money{
											CurrencyCode: "INR",
											Units:        100000,
											Decimals:     0,
										},
										Confidence: 1,
									},
								},
							},
							Status: userintel.UserIntelStatus_USER_INTEL_STATUS_AVAILABLE,
						},
					}, nil)
				},
			},
			want: &caPb.GetScreenerDecisionResponse{
				Status:       rpcPb.StatusOk(),
				ScreenerPass: commontypes.BooleanEnum_TRUE,
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockAccountDao := mock_dao.NewMockAaAccountDao(ctrl)
			mockProcessor := account_processor.NewMockProcessor(ctrl)
			mockNcClient := ncMocks.NewMockUNNameCheckClient(ctrl)
			wealthOnbClient := wealthOnbMocks.NewMockWealthOnboardingClient(ctrl)
			mockIncomeEstimatorClient := mockIncEst.NewMockIncomeEstimatorClient(ctrl)
			mockIdgen := idgenMocks.NewMockIdGenerator(ctrl)
			mockUserIntelClient := mockUIIntel.NewMockUserIntelServiceClient(ctrl)
			mockScreenerClient := mockScreener.NewMockScreenerClient(ctrl)

			s := &CaService{
				dataProcessor:         mockProcessor,
				conf:                  dynconf,
				accountDao:            mockAccountDao,
				nameCheckClient:       mockNcClient,
				wealthOnbClient:       wealthOnbClient,
				txnExecutor:           mockTxnExecutor,
				incomeEstimatorClient: mockIncomeEstimatorClient,
				idgen:                 mockIdgen,
				uiClient:              mockUserIntelClient,
				screenerClient:        mockScreenerClient,
			}
			tt.args.mocks(&mockStruct{
				mockProcessor:      mockProcessor,
				mockAccountDao:     mockAccountDao,
				mockNcClient:       mockNcClient,
				mockWoClient:       wealthOnbClient,
				mockIncEstClient:   mockIncomeEstimatorClient,
				mockIdgen:          mockIdgen,
				mockUIClient:       mockUserIntelClient,
				mockScreenerClient: mockScreenerClient,
			})

			got, err := s.GetScreenerDecision(tt.args.ctx, tt.args.req)
			assert.Equal(t, tt.wantErr, err)
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestCaService_doesAaBelongToUser(t *testing.T) {
	t.Parallel()
	type args struct {
		aaHolder    *caPb.Holder
		userProfile *woPb.GetOrCreateUserResponse_UserDetails
		mocks       func(mockStruct *mockStruct)
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr error
	}{
		{
			name: "pan found and matches",
			args: args{
				aaHolder: &caPb.Holder{
					Name: "Mike Hawk",
					Pan:  "**********",
				},
				userProfile: &woPb.GetOrCreateUserResponse_UserDetails{
					Name: &commontypes.Name{
						FirstName: "Mike",
						LastName:  "Hawk",
					},
					PanDetails: &typespb.DocumentProof{
						Id: "**********",
					},
				},
				mocks: func(m *mockStruct) {},
			},
			want:    true,
			wantErr: nil,
		},
		{
			name: "pan found and not match",
			args: args{
				aaHolder: &caPb.Holder{
					Name: "Mike Hawk",
					Pan:  "**********",
				},
				userProfile: &woPb.GetOrCreateUserResponse_UserDetails{
					Name: &commontypes.Name{
						FirstName: "Mike",
						LastName:  "Hawk",
					},
					PanDetails: &typespb.DocumentProof{
						Id: "randomPan",
					},
				},
				mocks: func(m *mockStruct) {},
			},
			want:    false,
			wantErr: nil,
		},
		{
			name: "pan not found, name matches",
			args: args{
				aaHolder: &caPb.Holder{
					Name: "Mike Hawk",
				},
				userProfile: &woPb.GetOrCreateUserResponse_UserDetails{
					Name: &commontypes.Name{
						FirstName: "Mike",
						LastName:  "Hawk",
					},
					PanDetails: &typespb.DocumentProof{
						Id: "**********",
					},
				},
				mocks: func(m *mockStruct) {
					m.mockNcClient.EXPECT().NameMatch(gomock.Any(), &ncPb.NameMatchRequest{
						Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IN_HOUSE},
						Name_1: "Mike Hawk",
						Name_2: "Mike Hawk",
					}).Return(&ncPb.NameMatchRespone{
						Status:   rpcPb.StatusOk(),
						Decision: ncPb.NameMatchDecision_NAMEMATCH_PASS,
					}, nil)
				},
			},
			want:    true,
			wantErr: nil,
		},
		{
			name: "invalid PAN but name matches",
			args: args{
				aaHolder: &caPb.Holder{
					Name: "Mike Hawk",
				},
				userProfile: &woPb.GetOrCreateUserResponse_UserDetails{
					Name: &commontypes.Name{
						FirstName: "Mike",
						LastName:  "Hawk",
					},
					PanDetails: &typespb.DocumentProof{
						Id: "FOXX60",
					},
				},
				mocks: func(m *mockStruct) {
					m.mockNcClient.EXPECT().NameMatch(gomock.Any(), &ncPb.NameMatchRequest{
						Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IN_HOUSE},
						Name_1: "Mike Hawk",
						Name_2: "Mike Hawk",
					}).Return(&ncPb.NameMatchRespone{
						Status:   rpcPb.StatusOk(),
						Decision: ncPb.NameMatchDecision_NAMEMATCH_PASS,
					}, nil)
				},
			},
			want:    true,
			wantErr: nil,
		},
		{
			name: "pan not found, name match fail",
			args: args{
				aaHolder: &caPb.Holder{
					Name: "Mike Hawk",
				},
				userProfile: &woPb.GetOrCreateUserResponse_UserDetails{
					Name: &commontypes.Name{
						FirstName: "Hugh",
						LastName:  "Jass",
					},
					PanDetails: &typespb.DocumentProof{
						Id: "**********",
					},
				},
				mocks: func(m *mockStruct) {
					m.mockNcClient.EXPECT().NameMatch(gomock.Any(), &ncPb.NameMatchRequest{
						Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IN_HOUSE},
						Name_1: "Hugh Jass",
						Name_2: "Mike Hawk",
					}).Return(&ncPb.NameMatchRespone{
						Status:   rpcPb.StatusOk(),
						Decision: ncPb.NameMatchDecision_NAMEMATCH_FAIL,
					}, nil)
				},
			},
			want:    false,
			wantErr: nil,
		},
		{
			name: "pan not found, error during name match",
			args: args{
				aaHolder: &caPb.Holder{
					Name: "Mike Hawk",
				},
				userProfile: &woPb.GetOrCreateUserResponse_UserDetails{
					Name: &commontypes.Name{
						FirstName: "Hugh",
						LastName:  "Jass",
					},
					PanDetails: &typespb.DocumentProof{
						Id: "**********",
					},
				},
				mocks: func(m *mockStruct) {
					m.mockNcClient.EXPECT().NameMatch(gomock.Any(), &ncPb.NameMatchRequest{
						Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IN_HOUSE},
						Name_1: "Hugh Jass",
						Name_2: "Mike Hawk",
					}).Return(nil, epifierrors.ErrPermissionDenied)
				},
			},
			want:    false,
			wantErr: errors.Wrap(epifierrors.ErrPermissionDenied, "error while performing aa-user name match"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := getMocks(t)
			c := &CaService{
				nameCheckClient: m.mockNcClient,
				txnExecutor:     mockTxnExecutor,
			}
			tt.args.mocks(&mockStruct{
				mockNcClient: m.mockNcClient,
			})
			got, err := c.doesAaBelongToUser(context.Background(), tt.args.aaHolder, tt.args.userProfile)
			if tt.wantErr != nil && !strings.EqualFold(tt.wantErr.Error(), err.Error()) {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
			}
			assert.Equalf(t, tt.want, got, "doesAaBelongToUser(%v, %v)", tt.args.aaHolder, tt.args.userProfile)
		})
	}
}

func TestCaService_GetAccountsForRenewal(t *testing.T) {
	t.Parallel()
	// test setup
	acc1 := &caPb.AaAccount{
		Id:            "b05815c4-35ba-4e25-902b-f73ebe1ae528",
		AccountStatus: caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
		FipId:         "sbi-fip",
	}
	acc1Expiry := timestampPb.Now()
	accountList1 := map[*caPb.AaAccount]*timestampPb.Timestamp{
		acc1: acc1Expiry,
	}

	// test init
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockAco := mock_account_consent_orchestrator.NewMockAccountConsentOrchestrator(ctrl)
	mockReleaseEvaluator := mock_release.NewMockIEvaluator(ctrl)
	mockSegmentationService := mocks3.NewMockSegmentationServiceClient(ctrl)

	caService := &CaService{
		accountConsentWrapper: mockAco,
		conf:                  dynconf,
		releaseEvaluator:      mockReleaseEvaluator,
		beSegmentationClient:  mockSegmentationService,
	}
	externalAcc1, _ := caService.getExternalAccountDetail(context.Background(), acc1)

	type args struct {
		ctx context.Context
		req *caPb.GetAccountsForRenewalRequest
	}
	tests := []struct {
		name  string
		args  args
		mocks []interface{}
		want  *caPb.GetAccountsForRenewalResponse
	}{
		{
			name: "invalid request empty actor id",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAccountsForRenewalRequest{
					ActorId: "",
				},
			},
			mocks: []interface{}{
				mockAco.EXPECT().GetRelatedDataForRenewal(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("invalid actor id")),
			},
			want: &caPb.GetAccountsForRenewalResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
		{
			name: "actor id not found",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAccountsForRenewalRequest{
					ActorId: "randomId==",
				},
			},
			mocks: []interface{}{
				mockAco.EXPECT().GetRelatedDataForRenewal(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("failed to fetch account, no actor Id found")),
			},
			want: &caPb.GetAccountsForRenewalResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
		{
			name: "valid actor id",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAccountsForRenewalRequest{
					ActorId: "AC",
				},
			},
			mocks: []interface{}{
				mockAco.EXPECT().GetRelatedDataForRenewal(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(accountList1, nil),
			},
			want: &caPb.GetAccountsForRenewalResponse{
				Status: rpcPb.StatusOk(),
				RenewalAccountDetailsList: []*caPb.RenewalAccountDetails{
					{
						AccountDetails: externalAcc1,
						ConsentExpiry:  acc1Expiry,
					},
				},
			},
		},
		{
			name: "invalid request, empty actor id and account id",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAccountsForRenewalRequest{
					ActorId:    "actor-1",
					AccountIds: []string{"account-1"},
				},
			},
			mocks: []interface{}{
				mockAco.EXPECT().GetRelatedDataForRenewal(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("actorId and accountId is empty")),
			},
			want: &caPb.GetAccountsForRenewalResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
		{
			name: "check eligibility true, eligible",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAccountsForRenewalRequest{
					ActorId:                    "AC",
					CheckSegmentForEligibility: true,
				},
			},
			mocks: []interface{}{
				mockAco.EXPECT().GetRelatedDataForRenewal(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(accountList1, nil),
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil),
				mockSegmentationService.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segment.IsMemberResponse{
					Status: rpcPb.StatusOk(),
					SegmentMembershipMap: map[string]*segment.SegmentMembership{
						"test-segment-42069": {
							IsActorMember: true,
						},
					},
				}, nil),
			},
			want: &caPb.GetAccountsForRenewalResponse{
				Status: rpcPb.StatusOk(),
				RenewalAccountDetailsList: []*caPb.RenewalAccountDetails{
					{
						AccountDetails: externalAcc1,
						ConsentExpiry:  acc1Expiry,
					},
				},
			},
		},
		{
			name: "check eligibility true, not eligible",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAccountsForRenewalRequest{
					ActorId:                    "AC",
					CheckSegmentForEligibility: true,
				},
			},
			mocks: []interface{}{
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil),
			},
			want: &caPb.GetAccountsForRenewalResponse{
				Status:                    rpcPb.StatusOk(),
				RenewalAccountDetailsList: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := caService.GetAccountsForRenewal(tt.args.ctx, tt.args.req)
			assert.Equal(t, got, tt.want)
			assert.Nil(t, err)
		})
	}
}

func TestCaService_CheckEligibilityForConsentRenewal(t *testing.T) {
	t.Parallel()
	testActorId := "actor-01"
	type args struct {
		ctx context.Context
		req *caPb.CheckEligibilityForConsentRenewalRequest
	}
	type releaseEvalMocks struct {
		enable     bool
		isEligible bool
		wantErr    error
	}
	type segmentMemberMocks struct {
		enable       bool
		isMemberResp *segment.IsMemberResponse
		isMemberErr  error
	}
	tests := []struct {
		name               string
		args               args
		releaseEvalMocks   *releaseEvalMocks
		segmentMemberMocks *segmentMemberMocks
		want               *caPb.CheckEligibilityForConsentRenewalResponse
		wantErr            error
	}{
		{
			name: "user NOT eligible for consent renewal due to release constrains",
			args: args{
				ctx: context.Background(),
				req: &caPb.CheckEligibilityForConsentRenewalRequest{ActorId: testActorId},
			},
			releaseEvalMocks: &releaseEvalMocks{
				enable:     true,
				isEligible: false,
				wantErr:    nil,
			},
			segmentMemberMocks: &segmentMemberMocks{
				enable: false,
			},
			want: &caPb.CheckEligibilityForConsentRenewalResponse{
				Status:   rpcPb.StatusOk(),
				Eligible: false,
			},
			wantErr: nil,
		},
		{
			name: "error while evaluating using release evaluator",
			args: args{
				ctx: context.Background(),
				req: &caPb.CheckEligibilityForConsentRenewalRequest{ActorId: testActorId},
			},
			releaseEvalMocks: &releaseEvalMocks{
				enable:  true,
				wantErr: errors.New("evaluation failed"),
			},
			segmentMemberMocks: &segmentMemberMocks{
				enable: false,
			},
			want: &caPb.CheckEligibilityForConsentRenewalResponse{
				Status:   rpcPb.StatusInternal(),
				Eligible: false,
			},
			wantErr: nil,
		},
		{
			name: "user present in consent renewal segment",
			args: args{
				ctx: context.Background(),
				req: &caPb.CheckEligibilityForConsentRenewalRequest{ActorId: testActorId},
			},
			releaseEvalMocks: &releaseEvalMocks{
				enable:     true,
				isEligible: true,
				wantErr:    nil,
			},
			segmentMemberMocks: &segmentMemberMocks{
				enable: true,
				isMemberResp: &segment.IsMemberResponse{
					Status: rpcPb.StatusOk(),
					SegmentMembershipMap: map[string]*segment.SegmentMembership{
						"test-segment-42069": {
							IsActorMember: true,
						},
					},
				},
				isMemberErr: nil,
			},
			want: &caPb.CheckEligibilityForConsentRenewalResponse{
				Status:   rpcPb.StatusOk(),
				Eligible: true,
			},
			wantErr: nil,
		},
		{
			name: "user NOT present in consent renewal segment",
			args: args{
				ctx: context.Background(),
				req: &caPb.CheckEligibilityForConsentRenewalRequest{ActorId: testActorId},
			},
			releaseEvalMocks: &releaseEvalMocks{
				enable:     true,
				isEligible: true,
				wantErr:    nil,
			},
			segmentMemberMocks: &segmentMemberMocks{
				enable: true,
				isMemberResp: &segment.IsMemberResponse{
					Status: rpcPb.StatusOk(),
					SegmentMembershipMap: map[string]*segment.SegmentMembership{
						"test-segment-42069": {
							IsActorMember: false,
						},
					},
				},
				isMemberErr: nil,
			},
			want: &caPb.CheckEligibilityForConsentRenewalResponse{
				Status:   rpcPb.StatusOk(),
				Eligible: false,
			},
			wantErr: nil,
		},
		{
			name: "failed to check eligibility from segmentation service",
			args: args{
				ctx: context.Background(),
				req: &caPb.CheckEligibilityForConsentRenewalRequest{ActorId: testActorId},
			},
			releaseEvalMocks: &releaseEvalMocks{
				enable:     true,
				isEligible: true,
				wantErr:    nil,
			},
			segmentMemberMocks: &segmentMemberMocks{
				enable: true,
				isMemberResp: &segment.IsMemberResponse{
					Status: rpcPb.StatusInternal(),
				},
			},
			want: &caPb.CheckEligibilityForConsentRenewalResponse{
				Status:   rpcPb.StatusInternal(),
				Eligible: false,
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockReleaseEvaluator := mock_release.NewMockIEvaluator(ctr)
			mockSegmentationService := mocks3.NewMockSegmentationServiceClient(ctr)
			c := &CaService{
				releaseEvaluator:     mockReleaseEvaluator,
				beSegmentationClient: mockSegmentationService,
				conf:                 dynconf,
			}

			if tt.releaseEvalMocks.enable {
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(tt.releaseEvalMocks.isEligible, tt.releaseEvalMocks.wantErr)
			}

			if tt.segmentMemberMocks.enable {
				mockSegmentationService.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(tt.segmentMemberMocks.isMemberResp, tt.segmentMemberMocks.isMemberErr)
			}

			got, err := c.CheckEligibilityForConsentRenewal(tt.args.ctx, tt.args.req)
			if tt.wantErr != nil && !strings.EqualFold(tt.wantErr.Error(), err.Error()) {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
			}

			assert.Equalf(t, tt.want, got, "CheckEligibilityForConsentRenewal(%v, %v)", tt.args.ctx, tt.args.req)
		})
	}
}

func TestCaService_GetFipMeta(t *testing.T) {
	t.Parallel()
	fipMeta1 := &caExtPb.FipMeta{
		FipId:                 "fiplive@indusind",
		Name:                  "IndusInd Bank Ltd.",
		DisplayName:           "IndusInd",
		Bank:                  typespb.Bank_INDUSIND,
		LogoUrl:               "https://epifi-icons.pointz.in/connectedaccounts/banks/IndusInd.png",
		OtpLength:             6,
		IsPopular:             true,
		OtpPattern:            []string{"OTP generated is", "OTP is", "one time password is", "is your one time password", "is the OTP", "is SECRET OTP", "Your one time password"},
		BankSmsHeader:         []string{" "},
		FipAccountType:        "BANK_ACCOUNT",
		ShowSkipButtonTimeout: 5,
	}
	// todo: BankSmsHeader data needs to be added below when we have the data
	fipMeta2 := &caExtPb.FipMeta{
		FipId:                 "bajaj-finserv-fip",
		Name:                  "BAJAJ FINSERV",
		DisplayName:           "Bajaj Finserv",
		Bank:                  typespb.Bank_BAJAJ_FINSERV,
		LogoUrl:               "https://www.onemoney.in/docs/img/bank_logos/bajajbank_logo.png",
		OtpLength:             6,
		OtpPattern:            []string{"OTP generated is", "OTP is", "one time password is", "is your one time password", "is the OTP", "is SECRET OTP"},
		BankSmsHeader:         []string{" "},
		FipAccountType:        "BANK_ACCOUNT",
		ShowSkipButtonTimeout: 5,
	}

	type args struct {
		ctx context.Context
		req *caPb.GetFipMetaRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *caPb.GetFipMetaResponse
		wantErr bool
	}{
		{
			name: "identifier, fip id in request",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetFipMetaRequest{
					Identifier: &caPb.GetFipMetaRequest_FipId{
						FipId: fipMeta1.GetFipId(),
					},
					Identifiers: nil,
				},
			},
			want: &caPb.GetFipMetaResponse{
				Status:  rpcPb.StatusOk(),
				FipMeta: fipMeta1,
			},
		},
		{
			name: "identifier, bank in request",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetFipMetaRequest{
					Identifier: &caPb.GetFipMetaRequest_Bank{
						Bank: fipMeta1.GetBank(),
					},
				},
			},
			want: &caPb.GetFipMetaResponse{
				Status:  rpcPb.StatusOk(),
				FipMeta: fipMeta1,
			},
		},
		{
			name: "bank meta not found",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetFipMetaRequest{
					Identifier: &caPb.GetFipMetaRequest_FipId{
						FipId: "unknown@fipid",
					},
				},
			},
			want: &caPb.GetFipMetaResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
		},
		{
			name: "bulk fetch, identifiers list, fip id in request,",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetFipMetaRequest{
					Identifiers: []*caPb.FipMetaIdentifier{
						{
							Identifier: &caPb.FipMetaIdentifier_FipId{
								FipId: fipMeta1.GetFipId(),
							},
						},
						{
							Identifier: &caPb.FipMetaIdentifier_FipId{
								FipId: fipMeta2.GetFipId(),
							},
						},
					},
				},
			},
			want: &caPb.GetFipMetaResponse{
				Status: rpcPb.StatusOk(),
				FipMetaList: []*caExtPb.FipMeta{
					fipMeta1,
					fipMeta2,
				},
			},
		},
		{
			name: "bulk fetch, identifiers list, bank in request,",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetFipMetaRequest{
					Identifiers: []*caPb.FipMetaIdentifier{
						{
							Identifier: &caPb.FipMetaIdentifier_Bank{
								Bank: fipMeta1.GetBank(),
							},
						},
						{
							Identifier: &caPb.FipMetaIdentifier_Bank{
								Bank: fipMeta2.GetBank(),
							},
						},
					},
				},
			},
			want: &caPb.GetFipMetaResponse{
				Status: rpcPb.StatusOk(),
				FipMetaList: []*caExtPb.FipMeta{
					fipMeta1,
					fipMeta2,
				},
			},
		},
		{
			name: "when both identifier and identifier list is passed",
			args: args{
				ctx: context.Background(),
				req: &caPb.GetFipMetaRequest{
					Identifier: &caPb.GetFipMetaRequest_FipId{FipId: fipMeta1.GetFipId()},
					Identifiers: []*caPb.FipMetaIdentifier{
						{
							Identifier: &caPb.FipMetaIdentifier_FipId{
								FipId: fipMeta1.GetFipId(),
							},
						},
						{
							Identifier: &caPb.FipMetaIdentifier_FipId{
								FipId: fipMeta2.GetFipId(),
							},
						},
					},
				},
			},
			want: &caPb.GetFipMetaResponse{
				Status:  rpcPb.StatusOk(),
				FipMeta: fipMeta1,
				FipMetaList: []*caExtPb.FipMeta{
					fipMeta1,
					fipMeta2,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CaService{
				conf: dynconf,
			}
			got, err := c.GetFipMeta(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFipMeta() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("GetFipMeta() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestCaService_GetAccountIdsByConsentHandleListResponse(t *testing.T) {
	t.Parallel()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	type fields struct {
		crDao                dao.ConsentRequestDao
		cDao                 dao.ConsentDao
		accountDao           dao.AaAccountDao
		consentAccMappingDao dao.ConsentAccountMappingDao
	}
	type args struct {
		ctx context.Context
		req *caPb.GetAccountIdsByConsentHandleListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		mocks   func(m *mockStruct)
		want    *caPb.GetAccountIdsByConsentHandleListResponse
		wantErr bool
	}{
		{
			name:   "2 valid consent handles",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				req: &caPb.GetAccountIdsByConsentHandleListRequest{
					ConsentHandleList: []string{
						"consent-handle-1",
						"consent-handle-2",
					},
				},
			},
			mocks: func(m *mockStruct) {
				m.mockConsentRequestDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-1", gomock.Any(), gomock.Any()).Return(&caPb.ConsentRequest{
					Id:     "consent-request-1",
					Status: caEnumPb.ConsentRequestStatus_CONSENT_REQUEST_STATUS_SUCCESS,
				}, nil)
				m.mockConsentDao.EXPECT().GetByConsentRequestId(gomock.Any(), "consent-request-1", gomock.Any()).Return(&caPb.Consent{
					Id: "consent-1",
				}, nil)
				m.mockConAccMapDao.EXPECT().GetByConsentReferenceId(gomock.Any(), "consent-1", gomock.Any()).Return([]*caPb.ConsentAccountMapping{
					{
						AccountReferenceId: "acc-1",
					},
					{
						AccountReferenceId: "acc-2",
					},
				}, nil)
				m.mockConsentRequestDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-2", gomock.Any(), gomock.Any()).Return(&caPb.ConsentRequest{
					Id:     "consent-request-2",
					Status: caEnumPb.ConsentRequestStatus_CONSENT_REQUEST_STATUS_SUCCESS,
				}, nil)
				m.mockConsentDao.EXPECT().GetByConsentRequestId(gomock.Any(), "consent-request-2", gomock.Any()).Return(&caPb.Consent{
					Id: "consent-2",
				}, nil)
				m.mockConAccMapDao.EXPECT().GetByConsentReferenceId(gomock.Any(), "consent-2", gomock.Any()).Return([]*caPb.ConsentAccountMapping{
					{
						AccountReferenceId: "acc-1",
					},
				}, nil)
			},
			want: &caPb.GetAccountIdsByConsentHandleListResponse{
				Status: rpcPb.StatusOk(),
				ConsentHandleToAccountIdsMap: map[string]*caPb.GetAccountIdsByConsentHandleListResponse_AccountIdList{
					"consent-handle-1": {
						AccountIdList: []string{
							"acc-1",
							"acc-2",
						},
					},
					"consent-handle-2": {
						AccountIdList: []string{
							"acc-1",
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := getMocks(t)
			c := &CaService{
				crDao:                m.mockConsentRequestDao,
				cDao:                 m.mockConsentDao,
				accountDao:           m.mockAccountDao,
				consentAccMappingDao: m.mockConAccMapDao,
			}
			tt.mocks(m)
			got, err := c.GetAccountIdsByConsentHandleList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAccountIdsByConsentHandleList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equalf(t, tt.want, got, "GetAccountIdsByConsentHandleList(%v, %v)", tt.args.ctx, tt.args.req)
		})
	}
}
