package connectedaccount

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/waitgroup"

	"github.com/epifi/gamma/api/frontend/deeplinkv2"
	networthBeFePb "github.com/epifi/gamma/api/insights/networth/frontend"
	insightsPkg "github.com/epifi/gamma/insights/pkg"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	events2 "github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/hash"
	idGen "github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
	"github.com/epifi/be-common/pkg/queue"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/connected_account"
	caPb "github.com/epifi/gamma/api/connected_account"
	caCoPb "github.com/epifi/gamma/api/connected_account/consumer"
	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	caExtPb "github.com/epifi/gamma/api/connected_account/external"
	"github.com/epifi/gamma/api/docs"
	"github.com/epifi/gamma/api/frontend/account/signup"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	aaOrderPb "github.com/epifi/gamma/api/order/aa"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/screener"
	"github.com/epifi/gamma/api/segment"
	segmentPb "github.com/epifi/gamma/api/segment"
	types "github.com/epifi/gamma/api/typesv2"
	typesPb "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	userIntelPb "github.com/epifi/gamma/api/userintel"
	"github.com/epifi/gamma/api/vendorgateway/incomeestimator"
	ncPb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/connectedaccount/config/genconf"
	"github.com/epifi/gamma/connectedaccount/consent"
	"github.com/epifi/gamma/connectedaccount/dao"
	"github.com/epifi/gamma/connectedaccount/data"
	consentorchestrator "github.com/epifi/gamma/connectedaccount/data/consentorchestrator"
	caFiType "github.com/epifi/gamma/connectedaccount/data/fi_types"
	caError "github.com/epifi/gamma/connectedaccount/error"
	"github.com/epifi/gamma/connectedaccount/events"
	"github.com/epifi/gamma/connectedaccount/external"
	caHelper "github.com/epifi/gamma/connectedaccount/helper"
	"github.com/epifi/gamma/connectedaccount/metrics"
	"github.com/epifi/gamma/connectedaccount/property"
	"github.com/epifi/gamma/connectedaccount/transaction"
	"github.com/epifi/gamma/connectedaccount/typedef"
	caTypes "github.com/epifi/gamma/connectedaccount/wire/types"
	caPkg "github.com/epifi/gamma/pkg/connectedaccount"
	"github.com/epifi/gamma/pkg/decisionmodel"
	"github.com/epifi/gamma/pkg/feature/release"
	onboardingPkg "github.com/epifi/gamma/pkg/onboarding"
	panPkg "github.com/epifi/gamma/pkg/pan"
)

type CaService struct {
	caPb.UnimplementedConnectedAccountServer
	consentManager                         consent.Manager
	dataProcessor                          data.Processor
	consentProcessPublisher                queue.Publisher
	crDao                                  dao.ConsentRequestDao
	cDao                                   dao.ConsentDao
	accountDao                             dao.AaAccountDao
	dfaDao                                 dao.DataFetchAttemptDao
	txnDao                                 dao.AaTransactionDao
	heartbeatDao                           dao.AaUserHeartbeatDao
	depositTxnDao                          dao.AaDepositFiDao
	recurringDepTxnDao                     dao.AaRecurringDepositFiDao
	termDepositTxnDao                      dao.AaTermDepositFiDao
	consentAccMappingDao                   dao.ConsentAccountMappingDao
	fetchDataDelayPub                      queue.DelayPublisher
	conf                                   *genconf.Config
	accountConsentWrapper                  consentorchestrator.AccountConsentOrchestrator
	consentProcessDelayPub                 queue.DelayPublisher
	preferenceDao                          dao.AaUserBankPreferenceDao
	eventBroker                            events2.Broker
	caProp                                 property.CaProperties
	accExtProc                             external.AccountExtProcessor
	fiTypeProc                             caFiType.FITransactionExternalProcessor
	fiFactory                              data.IFIFactory
	consentDataRefreshPub                  queue.Publisher
	captureColumnUpdatePub                 queue.Publisher
	captureHeartbeatAndSendNotificationPub queue.Publisher
	txnExternalSnsPub                      typedef.TransactionEventExternalPublisher
	caCache                                caPkg.InMemoryAaCache
	docsClient                             docs.DocsClient
	releaseEvaluator                       release.IEvaluator
	userClient                             userPb.UsersClient
	actorClient                            actorPb.ActorClient
	nameCheckClient                        ncPb.UNNameCheckClient
	wealthOnbClient                        woPb.WealthOnboardingClient
	txnExecutor                            storagev2.TxnExecutor
	beSegmentationClient                   segment.SegmentationServiceClient
	incomeEstimatorClient                  incomeestimator.IncomeEstimatorClient
	idgen                                  idGen.IdGenerator
	transactionProcessor                   transaction.Processor
	piClient                               piPb.PiClient
	accountPIRelationClient                accountPiPb.AccountPIRelationClient
	aaOrderClient                          aaOrderPb.AccountAggregatorClient
	uiClient                               userIntelPb.UserIntelServiceClient
	screenerClient                         screener.ScreenerClient
	docsS3Client                           caTypes.DocsS3Client
	connFlowDao                            dao.ConnectionFlowDao
	preApprovedLoanClient                  preapprovedloan.PreApprovedLoanClient
	panProcessor                           insightsPkg.IPanProcessor
}

func NewCaService(consentManager consent.Manager, consentProcessPublisher typedef.ProcessConsentSqsPublisher, crDao dao.ConsentRequestDao,
	cDao dao.ConsentDao, dfaDao dao.DataFetchAttemptDao, accountDao dao.AaAccountDao, dataProcessor data.Processor,
	fetchDataDelayPub typedef.FetchDataSqsDelayPublisher, conf *genconf.Config, accountConsentWrapper consentorchestrator.AccountConsentOrchestrator,
	consentProcessDelayPub typedef.ProcessConsentSqsDelayPublisher, preferenceDao dao.AaUserBankPreferenceDao, eventBroker events2.Broker,
	caProp property.CaProperties, accExtProc external.AccountExtProcessor, txnDao dao.AaTransactionDao,
	fiTypeProc caFiType.FITransactionExternalProcessor, fiFactory data.IFIFactory, consentDataRefreshPub typedef.ProcessConsentDataRefreshSqsPublisher,
	captureColumnUpdatePub typedef.CaptureColumnUpdateSqsPublisher, captureHeartbeatAndSendNotificationPub typedef.CaptureHeartbeatAndSendNotificationSqsPublisher,
	heartbeatDao dao.AaUserHeartbeatDao, depositTxnDao dao.AaDepositFiDao, recurringDepTxnDao dao.AaRecurringDepositFiDao,
	termDepositTxnDao dao.AaTermDepositFiDao, txnExternalSnsPub typedef.TransactionEventExternalPublisher, caCache caPkg.InMemoryAaCache,
	docsClient docs.DocsClient, releaseEvaluator release.IEvaluator, userClient userPb.UsersClient, actorClient actorPb.ActorClient,
	nameCheckClient ncPb.UNNameCheckClient, wealthOnbClient woPb.WealthOnboardingClient,
	txnExecutor storagev2.TxnExecutor, beSegmentationClient segment.SegmentationServiceClient,
	incomeEstClient incomeestimator.IncomeEstimatorClient, idgen idGen.IdGenerator, transactionProcessor transaction.Processor, piClient piPb.PiClient, accountPIRelationClient accountPiPb.AccountPIRelationClient, aaOrderClient aaOrderPb.AccountAggregatorClient, consentAccMappingDao dao.ConsentAccountMappingDao,
	uiClient userIntelPb.UserIntelServiceClient, screenerClient screener.ScreenerClient, docsS3Client caTypes.DocsS3Client,
	connFlowDao dao.ConnectionFlowDao, preApprovedLoanClient preapprovedloan.PreApprovedLoanClient,
	panProcessor insightsPkg.IPanProcessor) *CaService {
	return &CaService{
		consentManager:                         consentManager,
		consentProcessPublisher:                consentProcessPublisher,
		dataProcessor:                          dataProcessor,
		crDao:                                  crDao,
		cDao:                                   cDao,
		dfaDao:                                 dfaDao,
		fetchDataDelayPub:                      fetchDataDelayPub,
		conf:                                   conf,
		accountDao:                             accountDao,
		accountConsentWrapper:                  accountConsentWrapper,
		consentProcessDelayPub:                 consentProcessDelayPub,
		preferenceDao:                          preferenceDao,
		eventBroker:                            eventBroker,
		caProp:                                 caProp,
		accExtProc:                             accExtProc,
		txnDao:                                 txnDao,
		fiTypeProc:                             fiTypeProc,
		fiFactory:                              fiFactory,
		consentDataRefreshPub:                  consentDataRefreshPub,
		captureColumnUpdatePub:                 captureColumnUpdatePub,
		captureHeartbeatAndSendNotificationPub: captureHeartbeatAndSendNotificationPub,
		heartbeatDao:                           heartbeatDao,
		depositTxnDao:                          depositTxnDao,
		recurringDepTxnDao:                     recurringDepTxnDao,
		termDepositTxnDao:                      termDepositTxnDao,
		txnExternalSnsPub:                      txnExternalSnsPub,
		caCache:                                caCache,
		docsClient:                             docsClient,
		releaseEvaluator:                       releaseEvaluator,
		userClient:                             userClient,
		actorClient:                            actorClient,
		nameCheckClient:                        nameCheckClient,
		wealthOnbClient:                        wealthOnbClient,
		txnExecutor:                            txnExecutor,
		beSegmentationClient:                   beSegmentationClient,
		incomeEstimatorClient:                  incomeEstClient,
		idgen:                                  idgen,
		transactionProcessor:                   transactionProcessor,
		piClient:                               piClient,
		accountPIRelationClient:                accountPIRelationClient,
		aaOrderClient:                          aaOrderClient,
		consentAccMappingDao:                   consentAccMappingDao,
		uiClient:                               uiClient,
		screenerClient:                         screenerClient,
		docsS3Client:                           docsS3Client,
		connFlowDao:                            connFlowDao,
		preApprovedLoanClient:                  preApprovedLoanClient,
		panProcessor:                           panProcessor,
	}
}

var _ caPb.ConnectedAccountServer = &CaService{}

// Proto validations are added to request so explicit check not needed here
func (c *CaService) StartConsentFlow(ctx context.Context, startConsentFlowRequest *caPb.StartConsentFlowRequest) (*caPb.StartConsentFlowResponse, error) {
	numOfConsentHandlesToGenerate := startConsentFlowRequest.GetNumOfConsentHandlesToGenerate()
	if numOfConsentHandlesToGenerate <= 0 {
		return &caPb.StartConsentFlowResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("NumOfConsentHandlesToGenerate in StartConsentFlowRequest cannot be less than or equal to 0"),
		}, nil
	}
	// validate the purpose of the request
	purpose := startConsentFlowRequest.GetConsentRequestPurpose()
	valid := validateEntityPurposePair(startConsentFlowRequest.GetAaEntity(), purpose)
	caFlowName := startConsentFlowRequest.GetCaFlowName()
	if !valid {
		logger.Error(ctx, "entity-purpose pair is invalid",
			zap.Any("AA_ENTITY", startConsentFlowRequest.GetAaEntity()), zap.Any("PURPOSE", purpose))
		return &caPb.StartConsentFlowResponse{Status: rpcPb.StatusInternalWithDebugMsg("entity-purpose pair is invalid")}, nil
	}

	switch purpose {
	case caEnumPb.ConsentRequestPurpose_CONSENT_REQUEST_PURPOSE_AA_AUTH_FLOW:
		var consentHandleList []string
		for consentHandleCount := int32(0); consentHandleCount < numOfConsentHandlesToGenerate; consentHandleCount++ {
			consentRequest, crErr := c.consentManager.GetConsentRequestForAuth(ctx, startConsentFlowRequest.GetActorId(), startConsentFlowRequest.GetVua(), startConsentFlowRequest.GetAaEntity(), caFlowName)
			if crErr != nil {
				logger.Error(ctx, "failed to generate consent handle (auth)", zap.Any("AA_ENTITY", startConsentFlowRequest.GetAaEntity()), zap.Error(crErr))
				return &caPb.StartConsentFlowResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to generate consent handle (auth)")}, nil
			}
			consentHandleList = append(consentHandleList, consentRequest.GetConsentHandle())
		}
		return &caPb.StartConsentFlowResponse{Status: rpcPb.StatusOk(), ConsentHandle: consentHandleList[0], ConsentHandleList: consentHandleList}, nil
	default:
		// for all other cases - default consent and consent renewal flow
		var consentHandleList []string
		for consentHandleCount := int32(0); consentHandleCount < numOfConsentHandlesToGenerate; consentHandleCount++ {
			consentRequest, crErr := c.handleConsentFlow(ctx, startConsentFlowRequest)
			if crErr != nil {
				logger.Error(ctx, "failed to generate consent handle", zap.Any("AA_ENTITY", startConsentFlowRequest.GetAaEntity()), zap.Error(crErr))
				return &caPb.StartConsentFlowResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to generate consent handle (default)")}, nil
			}
			consentHandleList = append(consentHandleList, consentRequest.GetConsentHandle())
		}
		return &caPb.StartConsentFlowResponse{Status: rpcPb.StatusOk(), ConsentHandle: consentHandleList[0], ConsentHandleList: consentHandleList}, nil
	}
}

func (c *CaService) handleConsentFlow(ctx context.Context, startConsentFlowRequest *caPb.StartConsentFlowRequest) (*caPb.ConsentRequest, error) {
	consentRequest, err := c.consentManager.RequestConsent(ctx, startConsentFlowRequest.GetActorId(),
		startConsentFlowRequest.GetVua(), startConsentFlowRequest.GetAaEntity(),
		startConsentFlowRequest.GetConsentRequestPurpose(), startConsentFlowRequest.GetCaFlowName())
	if err != nil {
		logger.Error(ctx, "error in requesting consent", zap.Any(logger.CONSENT_REQUEST_PURPOSE, startConsentFlowRequest.GetConsentRequestPurpose()), zap.Error(err))
		return nil, errors.Wrap(err, "error requesting consent")
	}
	goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
		c.publishStartConsentFlowEvent(epificontext.WithEventAttributes(ctx), consentRequest)
	})
	// Publish event to queue to process consent request and store consent artefact in DB
	_, sqsErr := c.consentProcessDelayPub.PublishWithDelay(ctx, &caCoPb.ProcessConsentRequest{
		ConsentRequestId: consentRequest.GetId(),
	}, c.conf.ConsentProcessDelay())
	if sqsErr != nil {
		logger.Error(ctx, "Failed to publish consent process message in queue",
			zap.String(logger.CONSENT_REQUEST_ID, consentRequest.GetId()), zap.String(logger.CONSENT_HANDLE,
				consentRequest.GetConsentHandle()), zap.Error(sqsErr))
		return nil, errors.Wrap(sqsErr, "error publishing consent process message in queue")
	}
	return consentRequest, nil
}

func (c *CaService) publishStartConsentFlowEvent(ctx context.Context, cr *caPb.ConsentRequest) {
	c.eventBroker.AddToBatch(ctx, events.NewGaveConsentAAServer(
		cr.GetActorId(),
		time.Now(),
		(int)(c.conf.Consent().ConsentLifeDuration().Hours()/24)))
}

//nolint:funlen
func (c *CaService) StartDataFlow(ctx context.Context, req *caPb.StartDataFlowRequest) (*caPb.StartDataFlowResponse, error) {
	return nil, fmt.Errorf("Unimplemented. Removed since dao indexes are removed")
	// // Get all active consents from DB, vua is optional and can be empty
	// activeConsentList, err := c.accountConsentWrapper.GetActiveConsents(ctx, req.GetVua())
	// if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
	//	logger.Info(ctx, "no consents found to fetch")
	//	return &caPb.StartDataFlowResponse{Status: rpcPb.StatusRecordNotFoundWithDebugMsg("no consents to fetch")}, nil
	// }
	// if err != nil {
	//	logger.Error(ctx, "error fetching active consents from DB", zap.Error(err))
	//	return &caPb.StartDataFlowResponse{Status: rpcPb.StatusInternalWithDebugMsg("error getting active consents")}, nil
	// }
	// logger.Info(ctx, "number of active consents from DB", zap.Int("consents", len(activeConsentList)))
	//
	// isErr := false
	// errorMsg := ""
	// cnt := 0
	//
	// var wg sync.WaitGroup
	// for _, con := range activeConsentList {
	//	gcons := con
	//	wg.Add(1)
	//	goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
	//		defer logger.RecoverPanicAndError(ctx)
	//		defer wg.Done()
	//		if updErr := c.updateConsentDataRefreshStatus(ctx, gcons, caEnumPb.ConsentDataRefreshStatus_CONSENT_DATA_REFRESH_STATUS_IN_PROGRESS); updErr != nil {
	//			logger.Error(ctx, "error updating consent data refresh status", zap.String(logger.CONSENT_ID, gcons.GetConsentId()), zap.Error(updErr))
	//			isErr = true
	//		}
	//		attemptErr := c.handleAttempt(ctx, gcons, &cnt)
	//		if attemptErr != nil {
	//			if updErr := c.updateConsentDataRefreshStatus(ctx, gcons, caEnumPb.ConsentDataRefreshStatus_CONSENT_DATA_REFRESH_STATUS_COMPLETED); updErr != nil {
	//				logger.Error(ctx, "error updating consent data refresh status", zap.String(logger.CONSENT_ID, gcons.GetConsentId()), zap.Error(updErr))
	//				isErr = true
	//			}
	//			if !errors.Is(attemptErr, caError.ErrAttemptAlreadyExists) {
	//				isErr = true
	//				errorMsg = attemptErr.Error()
	//				logger.Error(ctx, "failed to create/handle attempt for consent", zap.String(logger.CONSENT_ID, gcons.GetConsentId()),
	//					zap.String(logger.PRIMARY_CONSENT_ID, gcons.GetId()), zap.Error(attemptErr))
	//			}
	//		}
	//	})
	// }
	//
	// wg.Wait()
	// if isErr {
	//	return &caPb.StartDataFlowResponse{Status: rpcPb.StatusInternalWithDebugMsg(errorMsg)}, nil
	// }
	// if cnt == len(activeConsentList) {
	//	return &caPb.StartDataFlowResponse{Status: rpcPb.StatusRecordNotFoundWithDebugMsg("no consents to fetch")}, nil
	// }
	// return &caPb.StartDataFlowResponse{Status: rpcPb.StatusOk()}, nil
}

// returns error if attempt was not created/handled successfully, nil otherwise
func (c *CaService) handleAttempt(ctx context.Context, con *caPb.Consent, cnt *int) error {
	attempt, err := c.dataProcessor.CreateAttempt(ctx, con, caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_JOB, caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_PERIODIC, false)
	if err != nil && !errors.Is(err, caError.ErrAttemptAlreadyExists) {
		// if create attempt returns invalid argument, consent might be revoked/paused : refresh consent
		if errors.Is(err, caError.ErrInvalidArgument) {
			_, refreshErr := c.accountConsentWrapper.RefreshByConsentId(ctx, con, true)
			if refreshErr != nil {
				logger.Error(ctx, "error refreshing consent", zap.String(logger.CONSENT_ID, con.GetConsentId()),
					zap.String(logger.PRIMARY_CONSENT_ID, con.GetId()), zap.Error(refreshErr))
			}
		}
		return errors.Wrap(err, "failed to create attempt for consent")
	}
	if err != nil && errors.Is(err, caError.ErrAttemptAlreadyExists) {
		*cnt++
		return errors.Wrap(err, "not creating new attempt for consent")
	}
	// publish events to process this attempt in queue
	if _, pubErr := c.fetchDataDelayPub.PublishWithDelay(ctx, &caCoPb.FetchDataRequest{AttemptId: attempt.GetId()},
		c.conf.DataFetchDelayMinutes()); pubErr != nil {
		// mark attempt as failed so that it is created again in next RPC call
		attempt.FetchStatus = caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_FAILED_START
		if updErr := c.dfaDao.UpdateById(ctx, attempt.GetId(), attempt, []caPb.DataFetchAttemptFieldMask{
			caPb.DataFetchAttemptFieldMask_DATA_FETCH_ATTEMPT_FIELD_MASK_FETCH_STATUS}); updErr != nil {
			return errors.Wrap(updErr, fmt.Sprintf("error updating attempt on failed data publishing to delay queue, attemptId: %s, pubErr: %v", attempt.GetId(), pubErr))
		}
		return errors.Wrap(pubErr, fmt.Sprintf("cannot publish event for data fetch, attemptId : %s", attempt.GetId()))
	}
	logger.Info(ctx, "successfully published data fetch attempt", zap.String(logger.CONSENT_ID, con.GetConsentId()),
		zap.String(logger.ATTEMPT_ID, attempt.GetId()))
	return nil
}

// nolint:dupl
func (c *CaService) updateConsentDataRefreshStatus(ctx context.Context, con *caPb.Consent, status caEnumPb.ConsentDataRefreshStatus) error {
	con.ConsentDataRefreshStatus = status
	_, err := c.cDao.UpdateByConsentId(ctx, con, []caPb.ConsentFieldMask{caPb.ConsentFieldMask_CONSENT_FIELD_MASK_DATA_REFRESH_STATUS})
	if err != nil {
		return err
	}
	return nil
}

// Proto validations are added to request so explicit check not needed here
func (c *CaService) GetConsent(ctx context.Context, request *caPb.GetConsentRequest) (*caPb.GetConsentResponse, error) {
	cons, err := c.cDao.GetByConsentId(ctx, request.GetConsentId())
	if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Info(ctx, "consent not found in DB using consent id", zap.Error(err))
		return &caPb.GetConsentResponse{Status: rpcPb.StatusRecordNotFoundWithDebugMsg("consent not found in DB")}, nil
	}
	if err != nil {
		logger.Error(ctx, "error fetching consent from db using consent id", zap.Error(err))
		return &caPb.GetConsentResponse{Status: rpcPb.StatusInternalWithDebugMsg("error fetching consent from db")}, nil
	}
	cr, err := c.crDao.Get(ctx, cons.GetConsentRequestId())
	if err != nil {
		logger.Error(ctx, "error fetching consent request from db using consent id", zap.Error(err))
		return &caPb.GetConsentResponse{Status: rpcPb.StatusInternalWithDebugMsg("error fetching consent request from db")}, nil
	}
	return &caPb.GetConsentResponse{Status: rpcPb.StatusOk(), Consent: cons, AaEntity: cr.GetAaEntity(), ConsentHandle: cr.GetConsentHandle()}, nil
}

// Proto validations are added to request so explicit check not needed here
func (c *CaService) GetConsentRequestDetails(ctx context.Context, request *caPb.GetConsentRequestDetailsRequest) (*caPb.GetConsentRequestDetailsResponse, error) {
	cr, err := c.crDao.GetByConsentHandle(ctx, request.GetConsentHandle())
	if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Info(ctx, "consent request not found in DB using consent handle", zap.Error(err))
		return &caPb.GetConsentRequestDetailsResponse{Status: rpcPb.StatusRecordNotFoundWithDebugMsg("consent request not found")}, nil
	}
	if err != nil {
		logger.Error(ctx, "error fetching consent request from db using consent handle", zap.Error(err))
		return &caPb.GetConsentRequestDetailsResponse{Status: rpcPb.StatusInternalWithDebugMsg("error fetching consent request")}, nil
	}
	return &caPb.GetConsentRequestDetailsResponse{Status: rpcPb.StatusOk(), ConsentRequest: cr}, nil
}

// Proto validations are added to request so explicit check not needed here
func (c *CaService) GetDataFetchAttempt(ctx context.Context, request *caPb.GetDataFetchAttemptRequest) (*caPb.GetDataFetchAttemptResponse, error) {
	at, err := c.dfaDao.GetBySessionId(ctx, request.GetSessionId(), nil)
	if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Info(ctx, "data fetch attempt not found in DB using session id", zap.Error(err))
		return &caPb.GetDataFetchAttemptResponse{Status: rpcPb.StatusRecordNotFoundWithDebugMsg("data fetch attempt not found")}, nil
	}
	if err != nil {
		logger.Error(ctx, "error fetching data attempt from db using session id", zap.Error(err))
		return &caPb.GetDataFetchAttemptResponse{Status: rpcPb.StatusInternalWithDebugMsg("error fetching data attempt")}, nil
	}
	cons, err := c.cDao.Get(ctx, at.GetConsentReferenceId())
	if err != nil {
		logger.Error(ctx, "error fetching consent from db using consent id", zap.Error(err))
		return &caPb.GetDataFetchAttemptResponse{Status: rpcPb.StatusInternalWithDebugMsg("error fetching consent from db")}, nil
	}
	cr, err := c.crDao.Get(ctx, cons.GetConsentRequestId())
	if err != nil {
		logger.Error(ctx, "error fetching consent request from db using consent id", zap.Error(err))
		return &caPb.GetDataFetchAttemptResponse{Status: rpcPb.StatusInternalWithDebugMsg("error fetching consent request from db")}, nil
	}
	return &caPb.GetDataFetchAttemptResponse{Status: rpcPb.StatusOk(), DataFetchAttempt: at, AaEntity: cr.GetAaEntity(), Consent: cons}, nil
}

func (c *CaService) GetConsentParams(_ context.Context, _ *caPb.GetConsentParamsRequest) (*caPb.GetConsentParamsResponse, error) {
	now := time.Now()
	dataRangeFrom := now.AddDate(c.conf.Consent().DataRangeStartYears(), 0, 0)
	dataRangeTo := now.Add(c.conf.Consent().ConsentLifeDuration())
	return &caPb.GetConsentParamsResponse{
		Status:        rpcPb.StatusOk(),
		DataRangeFrom: timestampPb.New(dataRangeFrom),
		DataRangeTo:   timestampPb.New(dataRangeTo),
		Purpose:       c.conf.Consent().Purpose(),
		ConsentExpiry: timestampPb.New(time.Now().Add(c.conf.Consent().ConsentLifeDuration())),
		ConsentTypes:  convertToBeConsentTypes(c.conf.Consent().ConsentTypes()),
		Frequency:     convertToBeFrequency(c.conf.Consent().Frequency()),
		DataLife:      convertDataLifeToBeFrequency(c.conf.Consent().DataLife()),
	}, nil
}

// Proto validations are added to request so explicit check not needed here
func (c *CaService) GetLinkedAaAccounts(ctx context.Context, request *caPb.GetLinkedAaAccountsRequest) (*caPb.GetLinkedAaAccountsResponse, error) {
	actorId := request.GetActorId()
	consents, consentsErr := c.cDao.GetByActorIdWithStatus(ctx, actorId,
		[]caEnumPb.ConsentStatus{caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE, caEnumPb.ConsentStatus_CONSENT_STATUS_PAUSED},
	)
	if consentsErr != nil {
		if errors.Is(consentsErr, epifierrors.ErrRecordNotFound) {
			logger.Info(ctx, "no linked accounts were found for actor")
			return &caPb.GetLinkedAaAccountsResponse{
				Status: rpcPb.StatusRecordNotFoundWithDebugMsg("no linked accounts were found for actor"),
			}, nil
		}
		logger.Error(ctx, "error while fetching consents using actor_id", zap.Error(consentsErr))
		return &caPb.GetLinkedAaAccountsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching consents using actor_id"),
		}, nil
	}
	refreshedList, err := c.refreshConsents(ctx, consents)
	if err != nil {
		return &caPb.GetLinkedAaAccountsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while refreshing consent"),
		}, nil
	}
	mergedAccounts := mergeAndDedupeAccounts(refreshedList)
	if len(mergedAccounts) == 0 {
		logger.Info(ctx, "no accounts found after merging and deduping")
		return &caPb.GetLinkedAaAccountsResponse{
			Status: rpcPb.StatusRecordNotFoundWithDebugMsg("no accounts found after merging and deduping"),
		}, nil
	}
	// enrich account ID if found
	enrichedAccounts, err := c.enrichAccountMeta(ctx, mergedAccounts)
	if err != nil {
		return &caPb.GetLinkedAaAccountsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while enriching account meta"),
		}, nil
	}
	// Get account ID for all accounts if it exists
	return &caPb.GetLinkedAaAccountsResponse{
		Status:   rpcPb.StatusOk(),
		Accounts: enrichedAccounts,
	}, nil
}

// Proto validations are added to request so explicit check not needed here
func (c *CaService) GetAccounts(ctx context.Context, request *caPb.GetAccountsRequest) (*caPb.GetAccountsResponse, error) {
	actorId, accFilters, accInstTypeFilters := request.GetActorId(), request.GetAccountFilterList(), request.GetAccInstrumentTypeList()
	var resAccDetailsList []*caExtPb.AccountDetails
	accountStatuses := getInternalAccountStatusList(accFilters)
	accList, accErr := c.accountDao.GetByActorIdAndStatus(ctx, actorId, accountStatuses, accInstTypeFilters)
	if accErr != nil {
		if errors.Is(accErr, epifierrors.ErrRecordNotFound) {
			logger.Debug(ctx, "no accounts linked to actor were found", zap.String(logger.ACTOR_ID_V2, actorId))
			return &caPb.GetAccountsResponse{
				Status: rpcPb.StatusRecordNotFoundWithDebugMsg("no accounts linked to actor were found"),
			}, nil
		}
		logger.Error(ctx, "error while fetching linked accounts for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(accErr))
		return &caPb.GetAccountsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching linked accounts"),
		}, nil
	}
	for _, acc := range accList {
		extAcc, extAccErr := c.getExternalAccountDetail(ctx, acc)
		if extAccErr != nil {
			logger.Error(ctx, "error getting external account detail", zap.Error(extAccErr), zap.String(logger.ACCOUNT_ID, acc.GetId()), zap.String(logger.FIP_ID, acc.GetFipId()))
			return &caPb.GetAccountsResponse{Status: rpcPb.StatusInternal()}, nil
		}
		resAccDetailsList = append(resAccDetailsList, extAcc)
	}
	return &caPb.GetAccountsResponse{
		Status:             rpcPb.StatusOk(),
		AccountDetailsList: resAccDetailsList,
	}, nil
}

func isJupiterAccount(ctx context.Context, summaryDetails interface{}, jupiterIfsc string) bool {
	var ifscCode string
	switch summary := summaryDetails.(type) {
	case *caExtPb.DepositSummary:
		ifscCode = summary.GetIfscCode()
	case *caExtPb.TermDepositSummary:
		ifscCode = summary.GetIfscCode()
	case *caExtPb.RecurringDepositSummary:
		ifscCode = summary.GetIfscCode()
	default:
		logger.Error(ctx, "error getting ifsc code from summary")
		return false
	}

	return ifscCode == jupiterIfsc
}

func getInternalAccountStatusList(accFilters []caExtPb.AccountFilter) []caEnumPb.AccountStatus {
	var accountStatuses []caEnumPb.AccountStatus
	accountStatus := map[caEnumPb.AccountStatus]bool{}
	for _, filterType := range accFilters {
		statusList := accountFilterToStatusMap[filterType]
		for _, status := range statusList {
			if !accountStatus[status] {
				accountStatuses = append(accountStatuses, status)
				accountStatus[status] = true
			}
		}
	}
	return accountStatuses
}

// Proto validations are added to request so explicit check not needed here
func (c *CaService) GetAccountDetails(ctx context.Context, request *caPb.GetAccountDetailsRequest) (*caPb.GetAccountDetailsResponse, error) {
	accountId, accountDetailsMask := request.GetAccountId(), request.GetAccountDetailsMaskList()
	// fetch account details, profile details, summary details
	accountDetails, profileDetails, summaryDetails, err := c.dataProcessor.GetAccountDetails(ctx, accountId, accountDetailsMask)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Info(ctx, "no account details found", zap.String(logger.ACCOUNT_ID, accountId), zap.Any("mask", accountDetailsMask))
			return &caPb.GetAccountDetailsResponse{Status: rpcPb.StatusRecordNotFoundWithDebugMsg("no account details found")}, nil
		}
		logger.Error(ctx, "error occurred while fetching account details", zap.Error(err), zap.String(logger.ACCOUNT_ID, accountId), zap.Any(logger.FIELD_MASKS, accountDetailsMask))
		return &caPb.GetAccountDetailsResponse{Status: rpcPb.StatusInternalWithDebugMsg("error fetching account details")}, nil
	}
	// TODO(sainath): Remove empty check once the field is made mandatory
	if request.GetActorId() != "" && request.GetActorId() != accountDetails.GetActorId() {
		logger.Error(ctx, "actorId validation error", zap.String("req_actor_id", request.GetActorId()),
			zap.String("res_actor_id", accountDetails.GetActorId()))
		return &caPb.GetAccountDetailsResponse{Status: rpcPb.StatusPermissionDeniedWithDebugMsg("actorId validation error")}, nil
	}
	resp := &caPb.GetAccountDetailsResponse{Status: rpcPb.StatusOk(), AccountDetails: accountDetails}
	if profileDetails != nil {
		// assign profile details to response if present
		switch accountDetails.GetAccInstrumentType() {
		case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT:
			resp.Profile = &caPb.GetAccountDetailsResponse_DepositProfile{DepositProfile: profileDetails.(*caExtPb.HoldersDetails)}
			resp.ProfileDetails = &caExtPb.ProfileDetails{
				HoldersDetails: profileDetails.(*caExtPb.HoldersDetails),
			}
		case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_RECURRING_DEPOSIT:
			resp.Profile = &caPb.GetAccountDetailsResponse_RecurringDepositProfile{RecurringDepositProfile: profileDetails.(*caExtPb.HoldersDetails)}
			resp.ProfileDetails = &caExtPb.ProfileDetails{
				HoldersDetails: profileDetails.(*caExtPb.HoldersDetails),
			}
		case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT:
			resp.Profile = &caPb.GetAccountDetailsResponse_TermDepositProfile{TermDepositProfile: profileDetails.(*caExtPb.HoldersDetails)}
			resp.ProfileDetails = &caExtPb.ProfileDetails{
				HoldersDetails: profileDetails.(*caExtPb.HoldersDetails),
			}
		case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES:
			resp.Profile = &caPb.GetAccountDetailsResponse_EquityProfile{EquityProfile: profileDetails.(*caExtPb.EquityProfileDetails)}
		case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_ETF:
			resp.Profile = &caPb.GetAccountDetailsResponse_EtfProfile{EtfProfile: profileDetails.(*caExtPb.EtfProfileDetails)}
		case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_REIT:
			resp.Profile = &caPb.GetAccountDetailsResponse_ReitProfile{ReitProfile: profileDetails.(*caExtPb.ReitProfileDetails)}
		case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_INVIT:
			resp.Profile = &caPb.GetAccountDetailsResponse_InvitProfile{InvitProfile: profileDetails.(*caExtPb.InvitProfileDetails)}
		case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_NPS:
			resp.Profile = &caPb.GetAccountDetailsResponse_NpsProfileDetails{NpsProfileDetails: profileDetails.(*caExtPb.NpsProfileDetails)}
		default:
			logger.Error(ctx, "GetAccountDetails: invalid accInstType or profile details are null or profile mask is not present in request",
				zap.String(logger.ACCOUNT_ID, accountId), zap.Any("accDetailsMask", accountDetailsMask), zap.Any("accInstType", accountDetails.GetAccInstrumentType()))
			return &caPb.GetAccountDetailsResponse{Status: rpcPb.StatusInternal()}, nil
		}
	}
	if summaryDetails != nil {
		// assign summary details to response if present
		switch summary := summaryDetails.(type) {
		case *caExtPb.DepositSummary:
			resp.Summary = &caPb.GetAccountDetailsResponse_DepositSummary{DepositSummary: summary}
			resp.AccountDetails.IfscCode = summary.GetIfscCode()
		case *caExtPb.RecurringDepositSummary:
			resp.Summary = &caPb.GetAccountDetailsResponse_RecurringDepositSummary{RecurringDepositSummary: summary}
			resp.AccountDetails.IfscCode = summary.GetIfscCode()
		case *caExtPb.TermDepositSummary:
			resp.Summary = &caPb.GetAccountDetailsResponse_TermDepositSummary{TermDepositSummary: summary}
			resp.AccountDetails.IfscCode = summary.GetIfscCode()
		case *caExtPb.EquitySummary:
			resp.Summary = &caPb.GetAccountDetailsResponse_EquitySummary{EquitySummary: summary}
		case *caExtPb.EtfSummary:
			resp.Summary = &caPb.GetAccountDetailsResponse_EtfSummary{EtfSummary: summary}
		case *caExtPb.ReitSummary:
			resp.Summary = &caPb.GetAccountDetailsResponse_ReitSummary{ReitSummary: summary}
		case *caExtPb.InvitSummary:
			resp.Summary = &caPb.GetAccountDetailsResponse_InvitSummary{InvitSummary: summary}
		case *caExtPb.NpsSummary:
			resp.Summary = &caPb.GetAccountDetailsResponse_NpsSummary{NpsSummary: summary}
		default:
			logger.Info(ctx, "summary is not required in request", zap.String(logger.ACCOUNT_ID, accountId), zap.Any("accDetailsMask", accountDetailsMask))
		}
	}
	// fill fip meta
	fipMeta, err := c.conf.GetFipMetaByFipId(accountDetails.GetFipId())
	if err != nil {
		logger.Error(ctx, "error fetching fip meta", zap.Error(err))
		return &caPb.GetAccountDetailsResponse{Status: rpcPb.StatusInternalWithDebugMsg("error fetching fip meta")}, nil
	}
	logoUrl, displayName := fipMeta.LogoUrl, fipMeta.DisplayName
	if resp.GetAccountDetails().GetIfscCode() == c.conf.JupiterMetaData().IfscCode() {
		logoUrl, displayName = c.conf.JupiterMetaData().LogoUrl(), c.conf.JupiterMetaData().DisplayName()
	}
	resp.GetAccountDetails().FipMeta = &caExtPb.FipMeta{
		FipId:       fipMeta.GetFipId(),
		Bank:        fipMeta.GetBank(),
		Name:        fipMeta.GetName(),
		LogoUrl:     logoUrl,
		DisplayName: displayName,
	}
	return resp, nil
}

func (c *CaService) GetAccountDetailsBulk(ctx context.Context, req *caPb.GetAccountDetailsBulkRequest) (*caPb.GetAccountDetailsBulkResponse, error) {
	// get the bulk account, profile and summary details
	bulkApsDetails, bulkApsDetailsErr := c.dataProcessor.GetAccountDetailsBulk(ctx, req.GetAccountIdList(), req.GetAccountDetailsMaskList())
	if bulkApsDetailsErr != nil {
		logger.Error(ctx, "error getting bulk account details", zap.Error(bulkApsDetailsErr))
		if errors.Is(epifierrors.ErrRecordNotFound, bulkApsDetailsErr) {
			return &caPb.GetAccountDetailsBulkResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		return &caPb.GetAccountDetailsBulkResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &caPb.GetAccountDetailsBulkResponse{Status: rpcPb.StatusOk(), AccountDetailsMap: bulkApsDetails}, nil
}

func (c *CaService) refreshConsents(ctx context.Context, consents []*caPb.Consent) ([]*caPb.Consent, error) {
	errFlag := false
	var wg sync.WaitGroup
	// refresh the current consent list fetched from db
	var eligibleConsentList []*caPb.Consent
	for _, con := range consents {
		cons := con
		wg.Add(1)
		goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
			defer logger.RecoverPanicAndError(ctx)
			defer wg.Done()
			refreshedConsent, refreshErr := c.accountConsentWrapper.RefreshByConsentId(ctx, cons, true)
			if refreshErr != nil {
				logger.Error(ctx, "error while refreshing consent", zap.Error(refreshErr), zap.String(logger.CONSENT_ID, cons.GetConsentId()))
				errFlag = true
			} else if refreshedConsent.GetConsentStatus() == caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE ||
				refreshedConsent.GetConsentStatus() == caEnumPb.ConsentStatus_CONSENT_STATUS_PAUSED {
				eligibleConsentList = append(eligibleConsentList, refreshedConsent)
			}
		})
	}
	// Wait blocks until all function calls from the Go method have returned
	waitgroup.SafeWaitCtx(ctx, &wg)
	if errFlag {
		return nil, errors.New("error refreshing consent")
	}
	return eligibleConsentList, nil
}

// In case we get record not found for link reference number, it means that data pull has not started for account
// hence account id does not exist yet
func (c *CaService) enrichAccountMeta(ctx context.Context, accounts []*caExtPb.AccountDetails) ([]*caExtPb.AccountDetails, error) {
	errFlag := false
	var wg sync.WaitGroup
	accChannel := make(chan *caExtPb.AccountDetails, len(accounts))
	for _, acc := range accounts {
		acnt := acc
		wg.Add(1)
		goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
			defer logger.RecoverPanicAndError(ctx)
			defer wg.Done()
			fetchedAcc, getErr := c.accountDao.GetByActorIdAndLinkedAccountRef(ctx, acnt.GetActorId(), acnt.GetLinkedAccountRef())
			if getErr != nil {
				if !errors.Is(getErr, epifierrors.ErrRecordNotFound) {
					logger.Error(ctx, "error getting account from DB", zap.String(logger.LINK_REF_NUM, acnt.GetLinkedAccountRef()), zap.Error(getErr))
					errFlag = true
				}
			} else {
				acnt.AccountId = fetchedAcc.GetId()
			}
			accChannel <- acnt
		})
	}
	// Wait blocks until all function calls from the Go method have returned
	waitgroup.SafeWaitCtx(ctx, &wg)
	close(accChannel)
	var newAccountList []*caExtPb.AccountDetails
	for acc := range accChannel {
		newAccountList = append(newAccountList, acc)
	}
	if errFlag {
		return nil, errors.New("error enriching account meta information")
	}
	return newAccountList, nil
}

// TODO (hardik) : Add delete event for hybrid model for eventual consistency
func (c *CaService) DeleteAccount(ctx context.Context, request *caPb.DeleteAccountRequest) (*caPb.DeleteAccountResponse, error) {
	accList, conList, err := c.accountConsentWrapper.GetRelatedAccountsAndConsents(ctx, request.GetAccountId())
	if err != nil {
		logger.Error(ctx, "error getting related accounts and consents for delete operation", zap.Error(err))
		return &caPb.DeleteAccountResponse{Status: rpcPb.StatusInternal()}, nil
	}
	for _, acc := range accList {
		if request.GetActorId() != acc.GetActorId() {
			logger.Error(ctx, "actorId validation error", zap.String("req_actor_id", request.GetActorId()),
				zap.String("res_actor_id", acc.GetActorId()))
			return &caPb.DeleteAccountResponse{Status: rpcPb.StatusPermissionDeniedWithDebugMsg("actorId validation error")}, nil
		}
	}
	// Filter deleted items
	accList, conList = GetNonDeletedAccountsAndConsentsForDelete(accList, conList)
	if delErr := c.accountConsentWrapper.LocalDeleteAccountAndConsents(ctx, accList, conList); delErr != nil {
		logger.Error(ctx, "error local delete accounts and consents", zap.Error(delErr))
		return &caPb.DeleteAccountResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &caPb.DeleteAccountResponse{Status: rpcPb.StatusOk()}, nil
}

func (c *CaService) GetRelatedAccountsForDisconnect(ctx context.Context, request *caPb.GetRelatedAccountsForDisconnectRequest) (*caPb.GetRelatedAccountsForDisconnectResponse, error) {
	accs, chlist, consentIdList, err := c.accountConsentWrapper.GetRelatedDataForDisconnect(ctx, request.GetAccountId())
	if err != nil {
		logger.Error(ctx, "error getting related accounts for disconnect", zap.String(logger.ACCOUNT_ID,
			request.GetAccountId()), zap.Error(err))
		return &caPb.GetRelatedAccountsForDisconnectResponse{Status: rpcPb.StatusInternal()}, nil
	}
	if accs == nil || chlist == nil {
		logger.Error(ctx, "empty accounts and consent handle list, hence no active/paused consent", zap.String(logger.ACCOUNT_ID,
			request.GetAccountId()))
		return &caPb.GetRelatedAccountsForDisconnectResponse{Status: rpcPb.StatusRecordNotFound()}, nil
	}
	var accList []*caExtPb.AccountDetails
	for _, acc := range accs {
		if request.GetActorId() != acc.GetActorId() {
			logger.Error(ctx, "actorId validation error", zap.String("req_actor_id", request.GetActorId()),
				zap.String("res_actor_id", acc.GetActorId()))
			return &caPb.GetRelatedAccountsForDisconnectResponse{Status: rpcPb.StatusPermissionDeniedWithDebugMsg("actorId validation error")}, nil
		}
		extAcc, extAccErr := c.getExternalAccountDetail(ctx, acc)
		if extAccErr != nil {
			logger.Error(ctx, "error getting external account detail", zap.Error(extAccErr), zap.String(logger.ACCOUNT_ID, acc.GetId()), zap.String(logger.FIP_ID, acc.GetFipId()))
			return &caPb.GetRelatedAccountsForDisconnectResponse{Status: rpcPb.StatusInternal()}, nil
		}
		accList = append(accList, extAcc)
	}
	// From the list of all related accounts, keep the requested account details on top of the list.
	// This is required by the client to show the primary account to be disconnected on top
	requestAccountIndex := -1
	for i, acc := range accList {
		if request.GetAccountId() == acc.GetAccountId() {
			requestAccountIndex = i
			break
		}
	}
	if len(accList) > 0 && requestAccountIndex != -1 {
		accList[0], accList[requestAccountIndex] = accList[requestAccountIndex], accList[0]
	}
	confirmBottomSheetDeeplink := getDisconnectResponseConfirmBottomSheetDeeplink(accList, consentIdList, chlist)
	return &caPb.GetRelatedAccountsForDisconnectResponse{
		Status:             rpcPb.StatusOk(),
		AccountDetailList:  accList,
		ConsentHandleList:  chlist,
		ConsentIdList:      consentIdList,
		ConfirmBottomSheet: confirmBottomSheetDeeplink,
	}, nil
}

func getDisconnectResponseConfirmBottomSheetDeeplink(accountList []*caExtPb.AccountDetails, consentIdList []string, consentHandleList []string) *deeplinkPb.Deeplink {
	accountMetadataList := make([]*deeplinkPb.DisconnectAccountConfirmScreenOptions_AccountDetail, 0, len(accountList))
	for _, account := range accountList {
		accountObj := &deeplinkPb.DisconnectAccountConfirmScreenOptions_AccountDetail{
			AccountId:           account.GetAccountId(),
			MaskedAccountNumber: account.GetMaskedAccountNumber(),
			AccountType:         convertAccountTypeToExternalDisplayText(account.GetAccountType()),
			AccountDisplayName:  account.GetFipMeta().GetDisplayName(),
			FipLogoUrl:          account.GetFipMeta().GetLogoUrl(),
			AaEntity:            convertAaEntityCaEnumToDisconnectConfirmScreenOptionsType(account.GetAaEntity()),
		}
		accountMetadataList = append(accountMetadataList, accountObj)
	}
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_DISCONNECT_ACCOUNT_CONFIRM_BOTTOM_SHEET,
		ScreenOptions: &deeplinkPb.Deeplink_DisconnectAccountConfirmScreenOptions{
			DisconnectAccountConfirmScreenOptions: &deeplinkPb.DisconnectAccountConfirmScreenOptions{
				Title: &commontypes.Text{
					FontColor:        DisconnectConfirmBottomSheetTitleFontColor,
					BgColor:          DisconnectConfirmBottomSheetTitleBgColor,
					DisplayValue:     &commontypes.Text_PlainString{PlainString: DisconnectConfirmBottomSheetTitleText},
					FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: DisconnectConfirmBottomSheetTitleFontStyle},
					FontColorOpacity: int32(DisconnectConfirmBottomSheetTitleFontColorOpacity),
				},
				HintText: &commontypes.Text{
					FontColor:        DisconnectConfirmBottomSheetHintTextFontColor,
					BgColor:          DisconnectConfirmBottomSheetHintTextBgColor,
					DisplayValue:     &commontypes.Text_PlainString{PlainString: DisconnectConfirmBottomSheetHintTextTitleText},
					FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: DisconnectConfirmBottomSheetHintTextFontStyle},
					FontColorOpacity: int32(DisconnectConfirmBottomSheetHintTextFontColorOpacity),
				},
				Confirm: &deeplinkPb.Cta{
					Type:         deeplinkPb.Cta_DONE,
					Text:         DisconnectConfirmBottomSheetCtaText,
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
				},
				ConsentIdList:     consentIdList,
				ConsentHandleList: consentHandleList,
				AccountDetailList: accountMetadataList,
			},
		},
	}
}

func getDeleteResponseConfirmBottomSheetDeeplink(accountList []*caExtPb.AccountDetails) *deeplinkPb.Deeplink {
	accountMetadataList := make([]*deeplinkPb.DisconnectAccountConfirmScreenOptions_AccountDetail, 0, len(accountList))
	for _, account := range accountList {
		accountObj := &deeplinkPb.DisconnectAccountConfirmScreenOptions_AccountDetail{
			AccountId:           account.GetAccountId(),
			MaskedAccountNumber: account.GetMaskedAccountNumber(),
			AccountType:         convertAccountTypeToExternalDisplayText(account.GetAccountType()),
			AccountDisplayName:  account.GetFipMeta().GetDisplayName(),
			FipLogoUrl:          account.GetFipMeta().GetLogoUrl(),
			AaEntity:            convertAaEntityCaEnumToDisconnectConfirmScreenOptionsType(account.GetAaEntity()),
		}
		accountMetadataList = append(accountMetadataList, accountObj)
	}
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_DISCONNECT_ACCOUNT_CONFIRM_BOTTOM_SHEET,
		ScreenOptions: &deeplinkPb.Deeplink_DisconnectAccountConfirmScreenOptions{
			DisconnectAccountConfirmScreenOptions: &deeplinkPb.DisconnectAccountConfirmScreenOptions{
				Title: &commontypes.Text{
					FontColor:        DeleteConfirmBottomSheetTitleFontColor,
					BgColor:          DeleteConfirmBottomSheetTitleBgColor,
					DisplayValue:     &commontypes.Text_PlainString{PlainString: DeleteConfirmBottomSheetTitleText},
					FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: DeleteConfirmBottomSheetTitleFontStyle},
					FontColorOpacity: int32(DeleteConfirmBottomSheetTitleFontColorOpacity),
				},
				HintText: &commontypes.Text{
					FontColor:        DeleteConfirmBottomSheetHintTextFontColor,
					BgColor:          DeleteConfirmBottomSheetHintTextBgColor,
					DisplayValue:     &commontypes.Text_PlainString{PlainString: DeleteConfirmBottomSheetHintTextTitleText},
					FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: DeleteConfirmBottomSheetHintTextFontStyle},
					FontColorOpacity: int32(DeleteConfirmBottomSheetHintTextFontColorOpacity),
				},
				Confirm: &deeplinkPb.Cta{
					Type: deeplinkPb.Cta_CUSTOM,
					Text: DeleteConfirmBottomSheetCtaText,
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_API_DELETE_ACCOUNT,
					},
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
				},
				AccountDetailList: accountMetadataList,
			},
		},
	}
}

func (c *CaService) getExternalAccountDetail(ctx context.Context, account *caPb.AaAccount) (*caExtPb.AccountDetails, error) {
	fipMeta, fipMetaErr := c.conf.GetFipMetaByFipId(account.GetFipId())
	if fipMetaErr != nil {
		return nil, errors.Wrap(fipMetaErr, fmt.Sprintf("error fetching fip meta by id : %v", account.GetFipId()))
	}
	logoUrl, displayName := fipMeta.LogoUrl, fipMeta.DisplayName
	// fetching summary is necessary for federal accounts because we want to be able to detect whether it is a jupiter account
	// this detection is based on ifsc code which is present only in the summary table
	if account.GetFipId() == caPkg.FederalFipId {
		// make dao call to account summary, if not present exhibit default behaviour
		_, _, summaryDetails, accountDetailsErr := c.dataProcessor.GetAccountDetails(ctx, account.GetId(), []caExtPb.AccountDetailsMask{caExtPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_SUMMARY})
		if summaryDetails == nil || accountDetailsErr != nil {
			logger.Error(ctx, "account summary not found for federal account", zap.Error(accountDetailsErr), zap.String(logger.ACCOUNT_ID, account.GetId()))
		} else if isJupiterAccount(ctx, summaryDetails, c.conf.JupiterMetaData().IfscCode()) {
			logoUrl, displayName = c.conf.JupiterMetaData().LogoUrl(), c.conf.JupiterMetaData().DisplayName()
		}
	}
	extAcc := account.ConvertToExternalAccountDetail(c.conf)
	extAcc.FipMeta = &caExtPb.FipMeta{
		FipId:       fipMeta.GetFipId(),
		DisplayName: displayName,
		LogoUrl:     logoUrl,
		Name:        fipMeta.GetName(),
		Bank:        fipMeta.GetBank(),
	}

	return extAcc, nil
}

func (c *CaService) CheckReoobe(ctx context.Context, checkReoobeRequest *caPb.CheckReoobeRequest) (*caPb.CheckReoobeResponse, error) {
	_, accList, oldVua, err := c.accountConsentWrapper.CheckReoobe(ctx, checkReoobeRequest.GetActorId(), checkReoobeRequest.GetVua())
	if err != nil {
		logger.Error(ctx, "error checking reoobe for user", zap.Error(err))
		return &caPb.CheckReoobeResponse{Status: rpcPb.StatusInternal()}, nil
	}
	var extAccList []*caExtPb.AccountDetails
	for _, acc := range accList {
		extAccList = append(extAccList, acc.ConvertToExternalAccountDetail(c.conf))
	}
	return &caPb.CheckReoobeResponse{Status: rpcPb.StatusOk(), AccountDetailList: extAccList, OldVua: oldVua}, nil
}

func (c *CaService) HandleReoobe(ctx context.Context, handleReoobeRequest *caPb.HandleReoobeRequest) (*caPb.HandleReoobeResponse, error) {
	err := c.accountConsentWrapper.HandleReoobe(ctx, handleReoobeRequest.GetActorId(), handleReoobeRequest.GetVua(), handleReoobeRequest.GetAccountIdList())
	if err != nil {
		logger.Error(ctx, "error handling reoobe for user", zap.Error(err))
		return &caPb.HandleReoobeResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &caPb.HandleReoobeResponse{Status: rpcPb.StatusOk()}, nil
}

func (c *CaService) GetAvailableFips(ctx context.Context, getAvailableFipsRequest *caPb.GetAvailableFipsRequest) (*caPb.GetAvailableFipsResponse, error) {
	fipMetaList, err := caPkg.GetFipMetaList()
	if err != nil {
		logger.Error(ctx, "error fetching list of available fips", zap.Error(err))
		return &caPb.GetAvailableFipsResponse{Status: rpcPb.StatusInternalWithDebugMsg("error fetching list of available fips")}, nil
	}

	fipFiListForActor, _, err := c.caProp.GetAllowedFipAndFiForActor(ctx, getAvailableFipsRequest.GetActorId(), getAvailableFipsRequest.GetAppPlatform(), getAvailableFipsRequest.GetAppVersion(), caEnumPb.CAFlowName_CA_FLOW_NAME_UNSPECIFIED)
	if err != nil {
		logger.Error(ctx, "error fetching fips and fi for actor", zap.Error(err))
		return &caPb.GetAvailableFipsResponse{Status: rpcPb.StatusInternalWithDebugMsg("error fetching fips and fi for actor")}, nil
	}
	var availableFips []typesPb.Bank

	// create a string:bool map of permitted FIPs
	// this will help while comparing it with the ones in pkg
	// any FIPs already permitted will have a value true
	permittedFipsMap := map[string]bool{}
	for _, fip := range fipFiListForActor {
		permittedFipsMap[fip.FipId] = true
	}

	for _, fipMeta := range fipMetaList {
		// if FIP from pkg is not already present in the list of permitted FIPs
		// add it to the list of available FIPs
		if !permittedFipsMap[fipMeta.Id] {
			availableFips = append(availableFips, fipMeta.Bank)
		}
	}

	if len(availableFips) == 0 {
		logger.Info(ctx, "No FIPs found to return after removing permitted fips")
		return &caPb.GetAvailableFipsResponse{Status: rpcPb.StatusRecordNotFound(), BankList: nil}, nil
	}

	return &caPb.GetAvailableFipsResponse{Status: rpcPb.StatusOk(), BankList: availableFips}, nil
}

// Note : PAN Filtering will not work for account types other than Deposit, Recurring Deposit and Term Deposit.
// this is due to inconsistency in the way we store Profile Details ( which contains PAN ) for Deposit, RD, TD vs Equity and ETF
// TODO : Make above behaviour consistent

func (c *CaService) GetAllAccounts(ctx context.Context, req *caPb.GetAllAccountsRequest) (*caPb.GetAllAccountsResponse, error) {
	token, err := pagination.GetPageToken(req.GetPageContext())
	if err != nil {
		logger.Error(ctx, "failed to get page token", zap.Error(err))
		return &caPb.GetAllAccountsResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to get page token")}, nil
	}
	accountStatuses := getInternalAccountStatusList(req.GetAccountFilterList())
	includeFiFedAccounts := caHelper.Contains(req.GetAccountFilterList(), caExtPb.AccountFilter_ACCOUNT_FILTER_INCL_FI_FEDERAL)
	accList, pageResp, err := c.accountDao.GetAccountsPaginated(ctx, &dao.GetAccountsPaginatedReq{
		ActorId:            req.GetActorId(),
		FipIds:             req.GetFipIdList(),
		AccInstrumentTypes: req.GetAccInstrumentTypeList(),
		CreatedAfter:       req.GetCreatedAfter(),
		CreatedBefore:      req.GetCreatedBefore(),
		AccountStatuses:    accountStatuses,
		PageToken:          token,
		PageSize:           req.GetPageContext().GetPageSize(),
		IsFiFedAccIncluded: includeFiFedAccounts,
	}, nil)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &caPb.GetAllAccountsResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		return &caPb.GetAllAccountsResponse{Status: rpcPb.StatusInternalWithDebugMsg(fmt.Sprintf("error getting account from db: %v", err.Error()))}, nil
	}
	var resAccDetailsList []*caExtPb.AccountDetails
	for _, resAcc := range accList {
		isMatch, isMatchErr := IsAccountSubTypeAndPanNumMatch(req.GetAccountSubTypeList(), req.GetPanNumber(), resAcc)
		if isMatchErr != nil && errors.Is(isMatchErr, epifierrors.ErrInvalidArgument) {
			logger.Error(ctx, "Invalid/Not-supported AccountSubType in req AccountSubTypeList", zap.Error(isMatchErr))
			return &caPb.GetAllAccountsResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg(isMatchErr.Error())}, nil
		}
		if isMatchErr != nil {
			logger.Error(ctx, "Error validating AccountSubType and Pan", zap.Any(logger.ACCOUNT_ID, resAcc.GetId()), zap.Error(isMatchErr))
			return &caPb.GetAllAccountsResponse{Status: rpcPb.StatusInternalWithDebugMsg(isMatchErr.Error())}, nil
		}
		if !isMatch {
			continue
		}
		fipMeta, fipMetaErr := c.conf.GetFipMetaByFipId(resAcc.GetFipId())
		if fipMetaErr != nil {
			logger.Error(ctx, "error fetching fip meta by fip id", zap.Any(logger.FIP_ID, resAcc.GetFipId()), zap.Error(fipMetaErr))
			return &caPb.GetAllAccountsResponse{Status: rpcPb.StatusInternalWithDebugMsg(fipMetaErr.Error())}, nil
		}
		externalAccountDetail := resAcc.ConvertToExternalAccountDetail(c.conf)
		externalAccountDetail.FipMeta = fipMeta
		resAccDetailsList = append(resAccDetailsList, externalAccountDetail)
	}
	return &caPb.GetAllAccountsResponse{Status: rpcPb.StatusOk(), PageContext: pageResp, AccountDetailsList: resAccDetailsList}, nil
}

func (c *CaService) CreateBankPreference(ctx context.Context, createBankPreferenceRequest *caPb.CreateBankPreferenceRequest) (*caPb.CreateBankPreferenceResponse, error) {
	// eliminate banks for which (actor_id,bank) entries are already recorded in the table
	finalBankList, err := c.eliminateRecordedBanks(ctx, createBankPreferenceRequest)

	if err != nil {
		logger.Error(ctx, "error while calling preferenceDao.GetByActorIdAndBank()", zap.Error(err))
		return &caPb.CreateBankPreferenceResponse{Status: rpcPb.StatusInternal()}, nil
	}

	// if length of the final bank list is zero there is nothing to be inserted
	if len(finalBankList) == 0 {
		return &caPb.CreateBankPreferenceResponse{Status: rpcPb.StatusOk()}, nil
	}

	actorId := createBankPreferenceRequest.GetActorId()

	// every other actor_id x bank preference is legitimate, insert them
	txnErr := c.processTxn(ctx, func(txnCtx context.Context) error {
		for _, bank := range finalBankList {
			_, err := c.preferenceDao.Create(ctx, &caPb.AaUserBankPreference{ActorId: actorId, Bank: bank})
			if err != nil {
				return errors.New("error while creating new preference entries")
			}
		}

		return nil
	})

	// if error is returned while executing the transaction block, return StatusInternal()
	// log the error
	if txnErr != nil {
		logger.Error(ctx, "error while creating new preference entries", zap.Error(txnErr))
		return &caPb.CreateBankPreferenceResponse{Status: rpcPb.StatusInternalWithDebugMsg("error while creating preferences")}, nil
	}

	return &caPb.CreateBankPreferenceResponse{Status: rpcPb.StatusOk()}, nil
}

func (c *CaService) GetConsentsForAccount(ctx context.Context, req *caPb.GetConsentsForAccountRequest) (*caPb.GetConsentsForAccountResponse, error) {
	consentRequestDetailsList, err := c.getConsents(ctx, &getConsentsRequest{
		accountId:         req.GetAccountId(),
		consentStatusList: req.GetConsentStatusList(),
	})
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &caPb.GetConsentsForAccountResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		logger.Error(ctx, "error getting consents for account", zap.Error(err))
		return &caPb.GetConsentsForAccountResponse{Status: rpcPb.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	var caExtPbList []*caExtPb.ConsentDetails
	for _, conReqDetails := range consentRequestDetailsList {
		caExtPbList = append(caExtPbList, conReqDetails.consentDetails)
	}
	return &caPb.GetConsentsForAccountResponse{
		Status:             rpcPb.StatusOk(),
		ConsentDetailsList: caExtPbList,
	}, nil
}

type getConsentsRequest struct {
	accountId         string
	consentStatusList []caEnumPb.ConsentStatus
}

type consentRequestDetails struct {
	consent        *caPb.Consent
	consentRequest *caPb.ConsentRequest
	consentDetails *caExtPb.ConsentDetails
}

func (c *CaService) getConsents(ctx context.Context, req *getConsentsRequest) ([]*consentRequestDetails, error) {
	cList, err := c.accountConsentWrapper.GetConsentsForAccount(ctx, req.accountId)
	if err != nil {
		return nil, errors.Wrapf(err, "error getting consents from account id: %s", req.accountId)
	}
	// Get all consent requests for consent request ids in lis
	var crIdList []string
	var filteredList []*caPb.Consent
	if len(req.consentStatusList) != 0 {
		for _, con := range cList {
			// check if status of consent is present in list
			for _, st := range req.consentStatusList {
				if st == con.GetConsentStatus() {
					filteredList = append(filteredList, con)
					break
				}
			}
		}
	} else {
		filteredList = cList
	}
	if len(filteredList) == 0 {
		return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "no consents found for account after filtering on status")
	}
	for _, con := range filteredList {
		crIdList = append(crIdList, con.GetConsentRequestId())
	}
	crList, getErr := c.crDao.GetBulk(ctx, crIdList)
	if getErr != nil {
		return nil, errors.Wrap(getErr, "error getting consent requests by id in bulk")
	}
	var conReqDetailsList []*consentRequestDetails
	for _, con := range filteredList {
		for _, cr := range crList {
			if cr.GetId() == con.GetConsentRequestId() {
				conReqDetailsList = append(conReqDetailsList, &consentRequestDetails{
					consent:        con,
					consentRequest: cr,
					consentDetails: con.ConvertToExternalConsentDetails(cr),
				})
			}
		}
	}
	return conReqDetailsList, nil
}

// eliminateRecordedBanks : eliminate banks for which (actor_id,bank) entries are already recorded in the table
func (c *CaService) eliminateRecordedBanks(ctx context.Context, cbpr *caPb.CreateBankPreferenceRequest) ([]typesPb.Bank, error) {

	actorId := cbpr.GetActorId()
	var finalBankList []typesPb.Bank

	// elimination of banks done here
	// if there is any unspecified bank entry (0), skip
	// if while using GetByActorIdAndBank() any other error -
	// - except ErrRecordNotFound occurs, return nil,err -
	// - else append
	for _, bank := range cbpr.GetBankList() {
		if bank == typesPb.Bank_BANK_UNSPECIFIED {
			continue
		}
		_, err := c.preferenceDao.GetByActorIdAndBank(ctx, actorId, bank)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				finalBankList = append(finalBankList, bank)
			} else {
				return nil, err
			}
		}
	}

	return finalBankList, nil
}

// get data fetch attempts
func (c *CaService) GetDataFetchAttempts(ctx context.Context, req *caPb.GetDataFetchAttemptsRequest) (*caPb.GetDataFetchAttemptsResponse, error) {
	token, err := pagination.GetPageToken(req.GetPageContext())
	if err != nil {
		logger.Error(ctx, "failed to get page token", zap.String(logger.CONSENT_HANDLE, req.GetConsentHandle()), zap.Error(err))
		return &caPb.GetDataFetchAttemptsResponse{Status: rpcPb.StatusInternalWithDebugMsg("error getting page tokens")}, nil
	}

	var dfaList []*caPb.DataFetchAttempt
	var pageResp *rpcPb.PageContextResponse
	var fetchErr error
	var crErr error
	var consentRequest *connected_account.ConsentRequest

	switch filter := req.GetFilter().(type) {

	case *caPb.GetDataFetchAttemptsRequest_ConsentHandle:
		consentHandle := req.GetConsentHandle()
		consentRequest, crErr = c.crDao.GetByConsentHandle(ctx, consentHandle)
		if crErr != nil {
			logger.Error(ctx, "failed to get consent request by consent handle from db", zap.String(logger.CONSENT_HANDLE, req.GetConsentHandle()), zap.Error(crErr))
			return &caPb.GetDataFetchAttemptsResponse{Status: rpcPb.StatusInternalWithDebugMsg("error getting consent request")}, nil
		}
		cons, cErr := c.cDao.GetByConsentRequestId(ctx, consentRequest.GetId())
		if cErr != nil {
			if errors.Is(cErr, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "consent by consent request id not found in db", zap.String(logger.CONSENT_REQUEST_ID, consentRequest.GetId()), zap.Error(cErr))
				return &caPb.GetDataFetchAttemptsResponse{Status: rpcPb.StatusOk(), ConsentRequest: consentRequest}, nil
			}
			logger.Error(ctx, "failed to get consent by consent request id from db", zap.String(logger.CONSENT_REQUEST_ID, consentRequest.GetId()), zap.Error(cErr))
			return &caPb.GetDataFetchAttemptsResponse{Status: rpcPb.StatusInternalWithDebugMsg("error getting consent")}, nil
		}
		dfaList, pageResp, fetchErr = c.dfaDao.GetDataFetchAttemptsPaginatedAndPurpose(ctx, cons.GetId(), token, req.GetPageContext().GetPageSize(),
			[]caEnumPb.DataFetchAttemptPurpose{caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_PERIODIC,
				caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_UNSPECIFIED,
			})
		if fetchErr != nil {
			if errors.Is(fetchErr, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "data fetch attempt not found in db", zap.String(logger.CONSENT_HANDLE, req.GetConsentHandle()), zap.Error(fetchErr))
				return &caPb.GetDataFetchAttemptsResponse{Status: rpcPb.StatusOk(), ConsentRequest: consentRequest}, nil
			}
			logger.Error(ctx, "failed to get data fetch attempt from db", zap.String(logger.CONSENT_HANDLE, req.GetConsentHandle()), zap.Error(fetchErr))
			return &caPb.GetDataFetchAttemptsResponse{Status: rpcPb.StatusInternalWithDebugMsg("error getting data fetch attempt")}, nil
		}
	case *caPb.GetDataFetchAttemptsRequest_AccountId:
		dfaList, pageResp, err = c.getDataFetchAttemptsForAccount(ctx, req.GetAccountId(), token, req.GetPageContext().GetPageSize())
		if err != nil {
			logger.Error(ctx, "error getting data fetch attempts for account", zap.Error(err))
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return &caPb.GetDataFetchAttemptsResponse{Status: rpcPb.StatusRecordNotFound()}, nil
			}
			return &caPb.GetDataFetchAttemptsResponse{Status: rpcPb.StatusInternalWithDebugMsg("error getting data fetch attempts for account")}, nil
		}
	default:
		logger.Error(ctx, "wrong search parameter", zap.Any("request", req), zap.Error(fmt.Errorf("wrong search parameter : %T", filter)))
		return &caPb.GetDataFetchAttemptsResponse{Status: rpcPb.StatusInternalWithDebugMsg("wrong search parameter")}, nil
	}
	var dfaDetailList []*caExtPb.DataFetchAttemptDetails
	for _, dfa := range dfaList {
		dfaDetailList = append(dfaDetailList, dfa.ConvertToExternalDfaDetail())
	}
	return &caPb.GetDataFetchAttemptsResponse{Status: rpcPb.StatusOk(), PageContext: pageResp, DataFetchAttemptDetailsList: dfaDetailList, ConsentRequest: consentRequest}, nil
}

func (c *CaService) getDataFetchAttemptsForAccount(ctx context.Context, accountId string, pageToken *pagination.PageToken, pageSize uint32) ([]*caPb.DataFetchAttempt, *rpcPb.PageContextResponse, error) {
	consentRequestDetailsList, err := c.getConsents(ctx, &getConsentsRequest{accountId: accountId})
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, nil, errors.Wrap(err, "no consents found")
		}
		return nil, nil, errors.Wrap(err, "error getting consents for account")
	}
	if len(consentRequestDetailsList) == 0 {
		return nil, nil, errors.Errorf("no consent requests found for account id: %s", accountId)
	}
	var consentRefIds []string
	for _, consentReq := range consentRequestDetailsList {
		consentRefIds = append(consentRefIds, consentReq.consent.GetId())
	}
	dataFetchAttempts, pageCtxRes, err := c.dfaDao.GetByActorIdAndFilters(ctx, consentRequestDetailsList[0].consentDetails.GetActorId(),
		[]storagev2.FilterOption{
			dao.WithDataFetchStatuses([]caEnumPb.DataFetchStatus{caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_COMPLETED}),
			dao.WithConsentRefIds(consentRefIds),
		},
		pageToken, pageSize,
	)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, nil, errors.Wrap(err, "no next page of data fetch attempts found")
		}
		return nil, nil, errors.Wrap(err, "error getting next page of data fetch attempts for consent ")
	}
	return dataFetchAttempts, pageCtxRes, nil
}

func (c *CaService) GetAllowedConfig(ctx context.Context, request *caPb.GetAllowedConfigRequest) (*caPb.GetAllowedConfigResponse, error) {
	fipFiList, userProfile, err := c.caProp.GetAllowedFipAndFiForActor(ctx, request.GetActorId(), request.GetAppPlatform(), request.GetAppVersion(), request.GetCaFlowName())
	if err != nil {
		logger.Error(ctx, "error getting allowed config using actor id", zap.Error(err))
		return &caPb.GetAllowedConfigResponse{Status: rpcPb.StatusInternal()}, nil
	}
	entity, entityErr := c.GetAaEntityForConnect(ctx, &caPb.GetAaEntityForConnectRequest{ActorId: request.GetActorId(), AppVersion: request.GetAppVersion(), AppPlatform: request.GetAppPlatform()})
	if rpcErr := epifigrpc.RPCError(entity, entityErr); rpcErr != nil {
		logger.Error(ctx, "error getting aa entity for connect", zap.Error(rpcErr))
		return &caPb.GetAllowedConfigResponse{Status: rpcPb.StatusInternal()}, nil
	}

	var allowedFipFiList []*caExtPb.FipConfig
	for _, fipFi := range fipFiList {
		for _, fip := range c.conf.AaToFipMapping()[entity.GetAaEntity().String()] {
			if fip == fipFi.GetFipId() {
				allowedFipFiList = append(allowedFipFiList, fipFi)
				break
			}
		}
	}

	return &caPb.GetAllowedConfigResponse{
		Status:             rpcPb.StatusOk(),
		AllowedFipList:     allowedFipFiList,
		PhoneNumber:        userProfile.GetPhoneNumber(),
		UserProfileDetails: userProfile,
	}, nil
}

func (c *CaService) ReplayAccountEvent(ctx context.Context, request *caPb.ReplayAccountEventRequest) (*caPb.ReplayAccountEventResponse, error) {
	accountList, err := c.accountDao.GetBulkById(ctx, request.GetAccountIdList())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "account id(s) not found in db", zap.Any(logger.ACCOUNT_ID, request.GetAccountIdList()), zap.Error(err))
			return &caPb.ReplayAccountEventResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error getting account by id - GetBulkById", zap.Any(logger.ACCOUNT_ID,
			request.GetAccountIdList()), zap.Error(err))
		return &caPb.ReplayAccountEventResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	var failureList []string
	for _, id := range request.GetAccountIdList() {
		if !idInIdList(id, accountList) {
			failureList = append(failureList, id)
		}
	}

	chErrGroup, _ := errgroup.WithContext(ctx)
	for _, account := range accountList {
		acc := account
		chErrGroup.Go(func() error {
			pubErr := c.accExtProc.PublishExternalAccountEvents(ctx, acc)
			if pubErr != nil {
				failureList = append(failureList, acc.GetId())
			}
			return pubErr
		})
	}

	pubErr := chErrGroup.Wait()
	if pubErr != nil {
		logger.Error(ctx, "error publishing one or more external account events", zap.Error(pubErr))
	}

	return &caPb.ReplayAccountEventResponse{
		Status:               rpcPb.StatusOk(),
		AccountIdFailureList: failureList,
	}, nil
}

func idInIdList(a string, list []*caPb.AaAccount) bool {
	for _, b := range list {
		if b.GetId() == a {
			return true
		}
	}
	return false
}

func (c *CaService) ReplayTxnEvent(ctx context.Context, request *caPb.ReplayTxnEventRequest) (*caPb.ReplayTxnEventResponse, error) {
	var failureList []string
	chErrGroup, _ := errgroup.WithContext(ctx)
	// nolint:scopelint
	for _, actorId := range request.GetActorIdList() {
		// get the transaction list for actorId and date Range
		txnList, txnFetchErr := c.txnDao.GetByActorIdAndDateRange(ctx, actorId, request.GetCreatedAfter(), request.GetCreatedBefore())
		if txnFetchErr != nil {
			if errors.Is(txnFetchErr, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "txns not found in db", zap.Any(logger.ACTOR_ID_V2, actorId), zap.Error(txnFetchErr))
				// no txns for this actorId, continue
				continue
			}

			logger.Error(ctx, "internal error while getting txns by actor_id", zap.Any(logger.ACTOR_ID_V2,
				actorId), zap.Error(txnFetchErr))
			// error getting txns for this actorId, append & continue
			failureList = append(failureList, actorId)
			continue
		}

		txnPublishList, failedForActorId := c.getTxnPublishList(ctx, txnList)

		if len(txnPublishList) > 0 {
			chErrGroup.Go(func() error {
				pubErr := c.fiTypeProc.PublishExternalTransactionEvents(ctx, txnPublishList, caEnumPb.TransactionEventTypeInitiatedBy_TRANSACTION_EVENT_TYPE_INITIATED_BY_PERIODIC_DATA_PULL_TXNS)
				if pubErr != nil {
					failureList = append(failureList, actorId)
				}
				return pubErr
			})
		}

		// if even a single txn failed append to failureList
		if failedForActorId {
			failureList = append(failureList, actorId)
		}
	}

	if chErrGroup != nil {
		pubErr := chErrGroup.Wait()
		if pubErr != nil {
			logger.Error(ctx, "error publishing one or more external txn events for actor", zap.Error(pubErr))
		}
	}

	return &caPb.ReplayTxnEventResponse{
		Status:             rpcPb.StatusOk(),
		ActorIdFailureList: failureList,
	}, nil
}

func (c *CaService) getTxnPublishList(ctx context.Context, txnList []*caPb.AaTransaction) ([]*caPb.TransactionEventInternal, bool) {
	failedForActorId := false
	var txnPublishList []*caPb.TransactionEventInternal

	for _, txn := range txnList {
		txnDetailsImpl, txnDetailsImplErr := c.fiFactory.GetTxnDetailsImpl(txn.GetTxnInstrumentType())
		if txnDetailsImplErr != nil {
			logger.Error(ctx, "error getting txn details processor", zap.Any(logger.TXN_TYPE, txn.GetTxnInstrumentType()),
				zap.Error(txnDetailsImplErr))
			failedForActorId = true
			continue
		}
		depTxn, depErr := txnDetailsImpl.GetTxnDetails(ctx, txn.GetId())
		if depErr != nil {
			logger.Error(ctx, "error getting txn details", zap.Any(logger.TXN_ID, txn.GetId()),
				zap.Error(depErr))
			failedForActorId = true
			continue
		}
		txnPublishList = append(txnPublishList, &caPb.TransactionEventInternal{
			AaTransaction: txn,
			FITransaction: depTxn,
		})
	}

	return txnPublishList, failedForActorId
}

func (c *CaService) CheckAccountSync(ctx context.Context, checkAccountSyncReq *caPb.CheckAccountSyncRequest) (*caPb.CheckAccountSyncResponse, error) {
	accSyncStatus, accSyncAction, accSyncActionReason, _, _, getErr := c.GetAccSyncStatusActionReason(ctx, checkAccountSyncReq.GetAccountId())
	if getErr != nil {
		logger.Error(ctx, "error getting account status, action and reason by id", zap.String(
			logger.ACCOUNT_ID, checkAccountSyncReq.GetAccountId()), zap.Error(getErr))
		if errors.Is(getErr, epifierrors.ErrRecordNotFound) {
			return &caPb.CheckAccountSyncResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		return &caPb.CheckAccountSyncResponse{Status: rpcPb.StatusInternal()}, nil
	}
	// Fetch number of transactions for this account from DB
	cnt, cntErr := c.txnDao.GetCountForAccount(ctx, checkAccountSyncReq.GetAccountId())
	if cntErr != nil {
		logger.Error(ctx, "error getting txn count for account",
			zap.String(logger.ACCOUNT_ID, checkAccountSyncReq.GetAccountId()), zap.Error(cntErr))
		return &caPb.CheckAccountSyncResponse{Status: rpcPb.StatusInternal()}, nil
	}
	logger.Info(ctx, "check account sync response", zap.String(logger.ACCOUNT_ID, checkAccountSyncReq.GetAccountId()),
		zap.String("status", accSyncStatus.String()), zap.String("action", accSyncAction.String()),
		zap.String("reason", accSyncActionReason.String()), zap.Int32("txnCnt", cnt))
	return &caPb.CheckAccountSyncResponse{Status: rpcPb.StatusOk(), AccountSyncStatus: accSyncStatus, AccountSyncAction: accSyncAction,
		AccountSyncActionReason: accSyncActionReason, TransactionCount: cnt}, nil
}

func (c *CaService) SyncAccount(ctx context.Context, syncAccReq *caPb.SyncAccountRequest) (*caPb.SyncAccountResponse, error) {
	accSyncStatus, accSyncAction, accSyncActionReason, _, chosenConsent, getErr := c.GetAccSyncStatusActionReason(ctx, syncAccReq.GetAccountId())
	if getErr != nil {
		logger.Error(ctx, "error getting account status, action and reason by id", zap.String(
			logger.ACCOUNT_ID, syncAccReq.GetAccountId()), zap.Error(getErr))
		if errors.Is(getErr, epifierrors.ErrRecordNotFound) {
			return &caPb.SyncAccountResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		return &caPb.SyncAccountResponse{Status: rpcPb.StatusInternal()}, nil
	}
	// If not allowed
	if accSyncAction != caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_ALLOWED {
		logger.Info(ctx, "get account sync response", zap.String(logger.ACCOUNT_ID, syncAccReq.GetAccountId()),
			zap.String("status", accSyncStatus.String()), zap.String("action", accSyncAction.String()),
			zap.String("reason", accSyncActionReason.String()))
		return &caPb.SyncAccountResponse{Status: rpcPb.StatusOk(), AccountSyncStatus: accSyncStatus, AccountSyncAction: accSyncAction,
			AccountSyncActionReason: accSyncActionReason}, nil
	}
	// Create data request for consent
	createdAttempt, createErr := c.dataProcessor.CreateAttempt(ctx, chosenConsent, caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_USER, caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_PERIODIC, false)
	if createErr != nil {
		logger.Error(ctx, "error creating attempt for chosen consent", zap.String(
			logger.ACCOUNT_ID, syncAccReq.GetAccountId()), zap.String(logger.PRIMARY_CONSENT_ID, chosenConsent.GetId()), zap.Error(createErr))
		return &caPb.SyncAccountResponse{Status: rpcPb.StatusInternal()}, nil
	}
	logger.Info(ctx, "created attempt for chosen consent", zap.String(logger.ACCOUNT_ID, syncAccReq.GetAccountId()),
		zap.String(logger.PRIMARY_CONSENT_ID, chosenConsent.GetId()))

	if chosenConsent == nil || chosenConsent.GetAccounts() == nil || chosenConsent.GetAccounts().GetAccountList() == nil {
		logger.Error(ctx, "No accounts present for data refresh against the given consent", zap.String(logger.CONSENT_ID, chosenConsent.GetConsentId()))
		return &caPb.SyncAccountResponse{Status: rpcPb.StatusInternal()}, nil
	}

	// Poll the attempt status for a defined threshold
	now := time.Now()
	for time.Now().Before(now.Add(c.conf.AccountSyncCompletionThreshold())) {
		att, getAttErr := c.dfaDao.GetByAttemptId(ctx, createdAttempt.GetId())
		if getAttErr != nil {
			logger.Error(ctx, "error polling attempt for status in account sync", zap.String(
				logger.ACCOUNT_ID, syncAccReq.GetAccountId()), zap.String(logger.ATTEMPT_ID, createdAttempt.GetId()), zap.Error(getAttErr))
			return &caPb.SyncAccountResponse{Status: rpcPb.StatusInternal()}, nil
		}
		if caHelper.IsAttemptInTerminalStatus(att.GetFetchStatus()) {
			createdAttempt = att
			break
		}
		time.Sleep(c.conf.AccountSyncCompletionPollFrequency())
	}
	// Fetch number of transactions for this account from DB
	cnt, cntErr := c.txnDao.GetCountForAccount(ctx, syncAccReq.GetAccountId())
	if cntErr != nil {
		logger.Error(ctx, "error getting txn count for account",
			zap.String(logger.ACCOUNT_ID, syncAccReq.GetAccountId()), zap.Error(cntErr))
		return &caPb.SyncAccountResponse{Status: rpcPb.StatusInternal()}, nil
	}
	accSyncStatus, accSyncAction, accSyncActionReason = getAccSyncActionAndReasonFromLatestAttemptStatus(createdAttempt,
		c.conf, accSyncAction, accSyncActionReason, chosenConsent.GetAccounts().GetAccountList())
	return &caPb.SyncAccountResponse{Status: rpcPb.StatusOk(), AccountSyncStatus: accSyncStatus,
		AccountSyncAction: accSyncAction, AccountSyncActionReason: accSyncActionReason, TransactionCount: cnt}, nil
}

func (c *CaService) GetAccSyncStatusActionReason(ctx context.Context, accId string) (
	caEnumPb.AccountSyncStatus, caEnumPb.AccountSyncAction, caEnumPb.AccountSyncActionReason, *caPb.DataFetchAttempt, *caPb.Consent, error) {
	var accSyncStatus caEnumPb.AccountSyncStatus
	acc, getAccErr := c.accountDao.GetById(ctx, accId, nil)
	if getAccErr != nil {
		return 0, 0, 0, nil, nil, errors.Wrap(getAccErr, "error fetching account by id")
	}
	accSyncAction, accSyncActionReason := getAccSyncActionAndReasonFromAccStatus(acc.GetAccountStatus())
	// If account status is such that it's data cannot be pulled anymore
	if accSyncAction != caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_ALLOWED {
		accSyncStatus = caEnumPb.AccountSyncStatus_ACCOUNT_SYNC_STATUS_INELIGIBLE
		return accSyncStatus, accSyncAction, accSyncActionReason, nil, nil, nil
	}
	// get all consents for the account
	cList, getConErr := c.accountConsentWrapper.GetConsentsForAccount(ctx, acc.GetId())
	if getConErr != nil {
		return 0, 0, 0, nil, nil, errors.Wrap(getConErr, "error getting consents for account")
	}
	var consentRefIdList []string
	for _, con := range cList {
		consentRefIdList = append(consentRefIdList, con.GetId())
	}
	attempt, getAttErr := c.dfaDao.GetLatestAttemptForConsentsAndPurpose(ctx, acc.GetActorId(), consentRefIdList,
		[]caEnumPb.DataFetchAttemptPurpose{caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_PERIODIC,
			caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_UNSPECIFIED,
		})
	if getAttErr != nil {
		return 0, 0, 0, nil, nil, errors.Wrap(getAttErr, "error getting latest attempt for consent")
	}
	var chosenConsent *caPb.Consent
	for _, con := range cList {
		if attempt.GetConsentReferenceId() == con.GetId() {
			chosenConsent = con
			break
		}
	}
	if chosenConsent == nil || chosenConsent.GetAccounts() == nil || chosenConsent.GetAccounts().GetAccountList() == nil {
		return 0, 0, 0, nil, nil, errors.Wrap(errors.New("AccountList is nil in the consent"), fmt.Sprintf("No accounts present for data refresh against the chosen consent %s", chosenConsent.GetConsentId()))
	}
	accSyncStatus, accSyncAction, accSyncActionReason = getAccSyncActionAndReasonFromLatestAttemptStatus(
		attempt, c.conf, accSyncAction, accSyncActionReason, chosenConsent.GetAccounts().GetAccountList())
	return accSyncStatus, accSyncAction, accSyncActionReason, attempt, chosenConsent, nil
}

func getAccSyncActionAndReasonFromAccStatus(accStatus caEnumPb.AccountStatus) (caEnumPb.AccountSyncAction, caEnumPb.AccountSyncActionReason) {
	var accSyncAction caEnumPb.AccountSyncAction
	var accSyncActionReason caEnumPb.AccountSyncActionReason
	// based on account status decide
	switch accStatus {
	case caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_PENDING:
		accSyncAction = caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_DENIED
		accSyncActionReason = caEnumPb.AccountSyncActionReason_ACCOUNT_SYNC_ACTION_REASON_DATA_SYNC_IN_PROGRESS
	case caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_OFF_PAUSED:
		accSyncAction = caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_DENIED
		accSyncActionReason = caEnumPb.AccountSyncActionReason_ACCOUNT_SYNC_ACTION_REASON_DATA_SYNC_OFF_PAUSED
	case caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_OFF_STOPPED:
		accSyncAction = caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_DENIED
		accSyncActionReason = caEnumPb.AccountSyncActionReason_ACCOUNT_SYNC_ACTION_REASON_DATA_SYNC_OFF_STOPPED
	case caEnumPb.AccountStatus_ACCOUNT_STATUS_DISCONNECTED:
		accSyncAction = caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_DENIED
		accSyncActionReason = caEnumPb.AccountSyncActionReason_ACCOUNT_SYNC_ACTION_REASON_DATA_SYNC_OFF_DISCONNECTED
	case caEnumPb.AccountStatus_ACCOUNT_STATUS_DELETED:
		accSyncAction = caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_DENIED
		accSyncActionReason = caEnumPb.AccountSyncActionReason_ACCOUNT_SYNC_ACTION_REASON_DATA_SYNC_OFF_DELETED
	default:
		accSyncAction = caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_ALLOWED
		accSyncActionReason = caEnumPb.AccountSyncActionReason_ACCOUNT_SYNC_ACTION_REASON_DATA_SYNC_ON
	}
	return accSyncAction, accSyncActionReason
}

func getAccSyncActionAndReasonFromLatestAttemptStatus(attempt *caPb.DataFetchAttempt, conf *genconf.Config,
	accSyncAction caEnumPb.AccountSyncAction, accSyncActionReason caEnumPb.AccountSyncActionReason, accountList []*connected_account.Account) (
	caEnumPb.AccountSyncStatus, caEnumPb.AccountSyncAction, caEnumPb.AccountSyncActionReason) {
	var accSyncStatus caEnumPb.AccountSyncStatus
	FIPListFromConsent := getFIPIdListFromAccountList(accountList)
	switch attempt.GetFetchStatus() {
	case caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_MANUAL_INTERVENTION,
		caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_FAILED_START,
		caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_INITIAL:
		accSyncStatus = caEnumPb.AccountSyncStatus_ACCOUNT_SYNC_STATUS_FAILED
		// In case previous attempt failed allow user after failure duration has passed
		if !attempt.GetCreatedAt().AsTime().Add(conf.GetNextFetchIntervalFailureCaseForFIP(FIPListFromConsent)).Before(time.Now()) {
			accSyncAction = caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_DENIED
			accSyncActionReason = caEnumPb.AccountSyncActionReason_ACCOUNT_SYNC_ACTION_REASON_LIMIT_EXHAUSTED
		}
	case caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_COMPLETED:
		accSyncStatus = caEnumPb.AccountSyncStatus_ACCOUNT_SYNC_STATUS_SUCCESSFUL
		if !attempt.GetCreatedAt().AsTime().Add(conf.GetNextFetchIntervalFailureCaseForFIP(FIPListFromConsent)).Before(time.Now()) {
			accSyncAction = caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_DENIED
			accSyncActionReason = caEnumPb.AccountSyncActionReason_ACCOUNT_SYNC_ACTION_REASON_LIMIT_EXHAUSTED
		}
	default:
		accSyncStatus = caEnumPb.AccountSyncStatus_ACCOUNT_SYNC_STATUS_IN_PROGRESS
		accSyncAction = caEnumPb.AccountSyncAction_ACCOUNT_SYNC_ACTION_DENIED
		accSyncActionReason = caEnumPb.AccountSyncActionReason_ACCOUNT_SYNC_ACTION_REASON_DATA_SYNC_IN_PROGRESS
	}
	return accSyncStatus, accSyncAction, accSyncActionReason
}

func (c *CaService) StartDataFlowV2(ctx context.Context, _ *caPb.StartDataFlowV2Request) (*caPb.StartDataFlowV2Response, error) {
	return nil, fmt.Errorf("Unimplemented. Removed since dao indexes are removed")
	// consentIdList, err := c.cDao.GetActiveConsentList(ctx, c.conf.ConsentDataRequestCountLimit(), caPb.ConsentFieldMask_CONSENT_FIELD_MASK_ID, caPb.ConsentFieldMask_CONSENT_FIELD_MASK_ACCOUNTS)
	// if err != nil {
	//	if errors.Is(err, epifierrors.ErrRecordNotFound) {
	//		logger.Error(ctx, "no active consent id found")
	//		return &caPb.StartDataFlowV2Response{Status: rpcPb.StatusRecordNotFound()}, nil
	//	}
	//	logger.Error(ctx, "error while fetching active consent id list", zap.Error(err))
	//	return &caPb.StartDataFlowV2Response{Status: rpcPb.StatusInternalWithDebugMsg("error while fetching active consent id list")}, nil
	// }
	// permittedConsentIdList := c.getPermittedConsents(ctx, consentIdList)
	// batchCnt := 0
	// cIdBatches := make([][]string, int(math.Ceil(float64(len(permittedConsentIdList))/float64(parallelConsentBatchLimit))))
	// var curBatchList []string
	// for idx, cid := range permittedConsentIdList {
	//	curBatchList = append(curBatchList, cid)
	//	if (batchCnt+1)*parallelConsentBatchLimit <= idx+1 {
	//		cIdBatches[batchCnt] = curBatchList
	//		curBatchList = nil
	//		batchCnt++
	//	}
	// }
	// if curBatchList != nil {
	//	cIdBatches[batchCnt] = curBatchList
	// }
	// isError := false
	// for _, batch := range cIdBatches {
	//	// Process this batch in parallel
	//	var wg sync.WaitGroup
	//	for _, cId := range batch {
	//		conId := cId
	//		wg.Add(1)
	//		goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
	//			defer logger.RecoverPanicAndError(ctx)
	//			defer wg.Done()
	//			if _, pubErr := c.consentDataRefreshPub.Publish(ctx, &caCoPb.ProcessConsentDataRefreshRequest{
	//				PrimaryConsentId: conId, Purpose: caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_PERIODIC}); pubErr != nil {
	//				isError = true
	//				logger.Error(ctx, "error publishing to consent data refresh queue", zap.Error(pubErr),
	//					zap.String(logger.PRIMARY_CONSENT_ID, conId))
	//			}
	//		})
	//	}
	//	wg.Wait()
	// }
	// if isError {
	//	return &caPb.StartDataFlowV2Response{Status: rpcPb.StatusInternalWithDebugMsg("error publishing one or more consents to data refresh queue ")}, nil
	// }
	//
	// logger.Info(ctx, "successfully published active consents for data refresh")
	// return &caPb.StartDataFlowV2Response{Status: rpcPb.StatusOk()}, nil
}

func (c *CaService) getPermittedConsents(_ context.Context, consentIdList []*caPb.Consent) []string {
	allowedFipsForDataFetch := c.conf.GetAllowedFipsForDataFetch()
	var allowedConsents []string
	for _, con := range consentIdList {
		for _, acc := range con.GetAccounts().GetAccountList() {
			if allowedFipsForDataFetch[acc.GetFipId()] {
				allowedConsents = append(allowedConsents, con.GetId())
				break
			}
		}
	}
	return allowedConsents
}

func (c *CaService) GetRelatedAccountsForDelete(ctx context.Context, req *caPb.GetRelatedAccountsForDeleteRequest) (
	*caPb.GetRelatedAccountsForDeleteResponse, error) {
	accList, conList, err := c.accountConsentWrapper.GetRelatedAccountsAndConsents(ctx, req.GetAccountId())
	if err != nil {
		logger.Error(ctx, "error getting related accounts and consents for delete operation", zap.Error(err))
		return &caPb.GetRelatedAccountsForDeleteResponse{Status: rpcPb.StatusInternal()}, nil
	}
	for _, acc := range accList {
		if req.GetActorId() != acc.GetActorId() {
			logger.Error(ctx, "actorId validation error", zap.String("req_actor_id", req.GetActorId()),
				zap.String("res_actor_id", acc.GetActorId()))
			return &caPb.GetRelatedAccountsForDeleteResponse{Status: rpcPb.StatusPermissionDeniedWithDebugMsg("actorId validation error")}, nil
		}
	}
	// Filter deleted items
	accList, _ = GetNonDeletedAccountsAndConsentsForDelete(accList, conList)
	var accExtList []*caExtPb.AccountDetails
	for _, acc := range accList {
		extAcc, extAccErr := c.getExternalAccountDetail(ctx, acc)
		if extAccErr != nil {
			logger.Error(ctx, "error getting external account detail", zap.Error(extAccErr), zap.String(logger.ACCOUNT_ID, acc.GetId()), zap.String(logger.FIP_ID, acc.GetFipId()))
			return &caPb.GetRelatedAccountsForDeleteResponse{Status: rpcPb.StatusInternal()}, nil
		}
		accExtList = append(accExtList, extAcc)
	}
	// From the list of all related accounts, keep the requested account details on top of the list.
	// This is required by the client to show the primary account to be deleted on top
	requestAccountIndex := -1
	for i, acc := range accExtList {
		if req.GetAccountId() == acc.GetAccountId() {
			requestAccountIndex = i
			break
		}
	}
	if len(accExtList) > 0 && requestAccountIndex != -1 {
		accExtList[0], accExtList[requestAccountIndex] = accExtList[requestAccountIndex], accExtList[0]
	}
	confirmBottomSheet := getDeleteResponseConfirmBottomSheetDeeplink(accExtList)
	return &caPb.GetRelatedAccountsForDeleteResponse{Status: rpcPb.StatusOk(), AccountDetailList: accExtList, ConfirmBottomSheet: confirmBottomSheet}, nil
}

func GetNonDeletedAccountsAndConsentsForDelete(accList []*caPb.AaAccount, conList []*caPb.Consent) ([]*caPb.AaAccount, []*caPb.Consent) {
	var filteredAccList []*caPb.AaAccount
	for _, acc := range accList {
		if acc.GetDeletedAtUnix() != 0 || acc.GetAccountStatus() == caEnumPb.AccountStatus_ACCOUNT_STATUS_DELETED {
			continue
		}
		filteredAccList = append(filteredAccList, acc)
	}
	var filteredConList []*caPb.Consent
	for _, con := range conList {
		if con.GetConsentStatus() == caEnumPb.ConsentStatus_CONSENT_STATUS_DELETED_EPIFI {
			continue
		}
		filteredConList = append(filteredConList, con)
	}
	return filteredAccList, filteredConList
}

func (c *CaService) GetAaEntityForConnect(ctx context.Context, req *caPb.GetAaEntityForConnectRequest) (*caPb.GetAaEntityForConnectResponse, error) {
	if req.GetAppPlatform() == commontypes.Platform_PLATFORM_UNSPECIFIED || req.GetActorId() == "" {
		logger.Error(ctx, "platform and actor cannot be left unspecified")
		return &caPb.GetAaEntityForConnectResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}
	// nolint:exhaustive
	switch req.GetAppPlatform() {
	case commontypes.Platform_IOS:
		if req.GetAppVersion() < c.conf.MinIosVersionEligibleForAaRouting() {
			return &caPb.GetAaEntityForConnectResponse{
				AaEntity: caEnumPb.AaEntity_AA_ENTITY_AA_FINVU,
				Status:   rpcPb.StatusOk(),
			}, nil
		}
	case commontypes.Platform_ANDROID:
		if req.GetAppVersion() < c.conf.MinAndroidVersionEligibleForAaRouting() {
			return &caPb.GetAaEntityForConnectResponse{
				AaEntity: caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
				Status:   rpcPb.StatusOk(),
			}, nil
		}
	}
	preferredEntity := c.conf.GetPreferredAaEntityForFipId(req.GetFipId(), req.GetAppPlatform())
	if preferredEntity != caEnumPb.AaEntity_AA_ENTITY_UNSPECIFIED {
		logger.Info(ctx, fmt.Sprintf("got preferred aa entity %s for fip %s", preferredEntity.String(), req.GetFipId()))
		return &caPb.GetAaEntityForConnectResponse{
			AaEntity: preferredEntity,
			Status:   rpcPb.StatusOk(),
		}, nil
	}
	// if preferred entity is not provided/eligible, fallback to user-group, hash-bucket logic to find the entity

	// hash bucket(of size 1) number is used as deterministic probability
	// ex: for an actor id probability will always be the same(same hash bucket)
	hashBucketForActorId := hash.Hash(req.GetActorId(), 100) + 1
	aaEntity, err := c.caProp.GetAaEntityForActor(ctx, req.GetAppPlatform(), hashBucketForActorId, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error getting aa entity for actor", zap.Error(err))
		return &caPb.GetAaEntityForConnectResponse{Status: rpcPb.StatusInternal()}, nil
	}

	logger.Info(ctx, fmt.Sprintf("successfully mapped hash bucket %v to entity %v", hashBucketForActorId, aaEntity.String()))
	return &caPb.GetAaEntityForConnectResponse{
		AaEntity: aaEntity,
		Status:   rpcPb.StatusOk(),
	}, nil
}

func (c *CaService) CheckAaHeartbeatStatus(ctx context.Context, req *caPb.CheckAaHeartbeatStatusRequest) (*caPb.CheckAaHeartbeatStatusResponse, error) {
	return &caPb.CheckAaHeartbeatStatusResponse{
		Status: rpcPb.StatusUnimplemented(),
	}, nil
	// if req.GetAaEntity() == caEnumPb.AaEntity_AA_ENTITY_UNSPECIFIED {
	// 	logger.Error(ctx, "aa entity is mandatory")
	// 	return &caPb.CheckAaHeartbeatStatusResponse{
	// 		Status: rpcPb.StatusInternalWithDebugMsg("aa entity is mandatory"),
	// 	}, nil
	// }
	// userHeartbeatGet, doesActorExists, heartbeatStatusResp, daoGetOrHbErr := c.handleDaoGetAndHeartbeatGet(ctx, req.GetActorId(), req.GetAaEntity())
	// if daoGetOrHbErr != nil {
	// 	logger.Error(ctx, "error in getting user from db or getting heartbeat status", zap.Any(logger.ACTOR_ID_V2, req.GetActorId()), zap.Any("aa_entity", req.GetAaEntity()))
	// 	return &caPb.CheckAaHeartbeatStatusResponse{
	// 		Status: rpcPb.StatusInternalWithDebugMsg("error in getting user from db or getting heartbeat status"),
	// 	}, nil
	// }
	// if heartbeatStatusResp.GetHeartbeatStat().GetHeartbeatStatus() == heartbeat.HeartbeatStatus_HEARTBEAT_STATUS_DOWN {
	// 	aaDownErr := c.handleAaHeartbeatDown(ctx, req.GetActorId(), req.GetAaEntity(), userHeartbeatGet, doesActorExists)
	// 	if aaDownErr != nil {
	// 		logger.Error(ctx, "error in handling aa status down", zap.Error(aaDownErr))
	// 		return &caPb.CheckAaHeartbeatStatusResponse{Status: rpcPb.StatusInternalWithDebugMsg("error in handling aa status down")}, nil
	// 	}
	// 	// return down status
	// 	return &caPb.CheckAaHeartbeatStatusResponse{
	// 		Status:            rpcPb.StatusOk(),
	// 		AaHeartbeatStatus: caEnumPb.AaHeartbeatStatus_AA_HEARTBEAT_STATUS_DOWN,
	// 	}, nil
	// }
	// aaUpErr := c.handleAaHeartbeatUp(ctx, req.GetActorId(), userHeartbeatGet, doesActorExists)
	// if aaUpErr != nil {
	// 	logger.Error(ctx, "error in handling aa status up", zap.Error(aaUpErr))
	// 	return &caPb.CheckAaHeartbeatStatusResponse{Status: rpcPb.StatusInternalWithDebugMsg("error in handling aa status up")}, nil
	// }
	// // return up status
	// return &caPb.CheckAaHeartbeatStatusResponse{
	// 	Status:            rpcPb.StatusOk(),
	// 	AaHeartbeatStatus: caEnumPb.AaHeartbeatStatus_AA_HEARTBEAT_STATUS_UP,
	// }, nil
}

// func (c *CaService) handleDaoGetAndHeartbeatGet(ctx context.Context, actorId string, aaEntity caEnumPb.AaEntity) (*caPb.AaUserHeartbeat, bool, *heartbeat.GetHeartbeatStatusResponse, error) {
// 	// check if actor exists
// 	var userHeartbeatGet *caPb.AaUserHeartbeat
// 	var userHeartbeatDaoGetErr error
// 	doesActorExists := true
// 	daoGetErrGroup, _ := errgroup.WithContext(ctx)
// 	daoGetErrGroup.Go(func() error {
// 		userHeartbeatGet, userHeartbeatDaoGetErr = c.heartbeatDao.GetByActorId(ctx, actorId)
// 		if userHeartbeatDaoGetErr != nil {
// 			if !errors.Is(userHeartbeatDaoGetErr, epifierrors.ErrRecordNotFound) {
// 				return userHeartbeatDaoGetErr
// 			}
// 			doesActorExists = false
// 		}
// 		return nil
// 	})
//
// 	// heartbeat status check from heartbeat service
// 	var heartbeatStatusResp *heartbeat.GetHeartbeatStatusResponse
// 	var heartbeatStatusErr error
// 	hbErrGroup, _ := errgroup.WithContext(ctx)
// 	hbErrGroup.Go(func() error {
// 		heartbeatStatusResp, heartbeatStatusErr = c.hbClient.GetHeartbeatStatus(ctx, &heartbeat.GetHeartbeatStatusRequest{
// 			HeartbeatTrigger: caHelper.ConvertToHeartbeatTrigger(aaEntity),
// 		})
// 		if err := epifigrpc.RPCError(heartbeatStatusResp, heartbeatStatusErr); err != nil {
// 			return err
// 		}
// 		return nil
// 	})
//
// 	daoGetErr := daoGetErrGroup.Wait()
// 	if daoGetErr != nil {
// 		return nil, false, nil, errors.Wrap(daoGetErr, "error while fetching actor from dao")
// 	}
//
// 	hbErr := hbErrGroup.Wait()
// 	if hbErr != nil {
// 		return nil, false, nil, errors.Wrap(hbErr, "error while fetching aa status from heartbeat server")
// 	}
// 	return userHeartbeatGet, doesActorExists, heartbeatStatusResp, nil
// }
//
// func (c *CaService) handleAaHeartbeatDown(ctx context.Context, actorId string, aaEntity caEnumPb.AaEntity, userHeartbeat *caPb.AaUserHeartbeat, doesActorExists bool) error {
// 	switch {
// 	case !doesActorExists:
// 		// create entry in db if not exists
// 		_, userHeartbeatDaoCreateErr := c.heartbeatDao.Create(ctx, &caPb.AaUserHeartbeat{
// 			ActorId:            actorId,
// 			NotificationStatus: caEnumPb.AaUserHeartbeatNotificationStatus_AA_USER_HEARTBEAT_NOTIFICATION_STATUS_IN_PROGRESS,
// 		})
// 		if userHeartbeatDaoCreateErr != nil {
// 			return errors.Wrap(userHeartbeatDaoCreateErr, "error creating entry in user heartbeat dao")
// 		}
// 	case userHeartbeat.GetNotificationStatus() != caEnumPb.AaUserHeartbeatNotificationStatus_AA_USER_HEARTBEAT_NOTIFICATION_STATUS_IN_PROGRESS:
// 		userHeartbeatDaoUpdateErr := c.heartbeatDao.Update(ctx, &caPb.AaUserHeartbeat{
// 			ActorId:            actorId,
// 			NotificationStatus: caEnumPb.AaUserHeartbeatNotificationStatus_AA_USER_HEARTBEAT_NOTIFICATION_STATUS_IN_PROGRESS,
// 		}, []caPb.AaUserHeartbeatFieldMask{caPb.AaUserHeartbeatFieldMask_AA_USER_HEARTBEAT_FIELD_MASK_NOTIFICATION_STATUS})
// 		if userHeartbeatDaoUpdateErr != nil {
// 			return errors.Wrap(userHeartbeatDaoUpdateErr, "error updating status in user heartbeat dao")
// 		}
// 	case userHeartbeat.GetNotificationStatus() == caEnumPb.AaUserHeartbeatNotificationStatus_AA_USER_HEARTBEAT_NOTIFICATION_STATUS_IN_PROGRESS:
// 		return nil
// 	}
// 	// publish
// 	_, publishErr := c.captureHeartbeatAndSendNotificationPub.Publish(ctx, &caCoPb.CaptureHeartbeatAndSendNotificationRequest{
// 		ActorId:            actorId,
// 		PublishedTimestamp: timestampPb.Now(),
// 		AaEntity:           aaEntity,
// 	})
// 	if publishErr != nil {
// 		return errors.Wrap(publishErr, "error publishing event")
// 	}
// 	return nil
// }
//
// func (c *CaService) handleAaHeartbeatUp(ctx context.Context, actorId string, userHeartbeat *caPb.AaUserHeartbeat, doesActorExists bool) error {
// 	if doesActorExists && userHeartbeat.GetNotificationStatus() == caEnumPb.AaUserHeartbeatNotificationStatus_AA_USER_HEARTBEAT_NOTIFICATION_STATUS_IN_PROGRESS {
// 		// update status to DONE
// 		userHeartbeatDaoUpdateErr := c.heartbeatDao.Update(ctx, &caPb.AaUserHeartbeat{
// 			ActorId:            actorId,
// 			NotificationStatus: caEnumPb.AaUserHeartbeatNotificationStatus_AA_USER_HEARTBEAT_NOTIFICATION_STATUS_DONE,
// 		}, []caPb.AaUserHeartbeatFieldMask{caPb.AaUserHeartbeatFieldMask_AA_USER_HEARTBEAT_FIELD_MASK_NOTIFICATION_STATUS})
// 		if userHeartbeatDaoUpdateErr != nil {
// 			return errors.Wrap(userHeartbeatDaoUpdateErr, "error updating status in user heartbeat dao")
// 		}
// 	}
// 	return nil
// }

func (c *CaService) GetFipMeta(ctx context.Context, req *caPb.GetFipMetaRequest) (*caPb.GetFipMetaResponse, error) {
	if req.GetIdentifier() != nil {
		if req.GetFipId() != "" {
			req.Identifiers = append(req.Identifiers, &caPb.FipMetaIdentifier{Identifier: &caPb.FipMetaIdentifier_FipId{FipId: req.GetFipId()}})
		}
		if req.GetBank() != typesPb.Bank_BANK_UNSPECIFIED {
			req.Identifiers = append(req.Identifiers, &caPb.FipMetaIdentifier{Identifier: &caPb.FipMetaIdentifier_Bank{Bank: req.GetBank()}})
		}
	}

	var fipMetaList []*caExtPb.FipMeta
	for _, identifier := range req.GetIdentifiers() {
		var fipMeta *caExtPb.FipMeta
		var err error
		switch identifier.GetIdentifier().(type) {
		case *caPb.FipMetaIdentifier_FipId:
			fipMeta, err = c.conf.GetFipMetaByFipId(identifier.GetFipId())
		case *caPb.FipMetaIdentifier_Bank:
			fipMeta, err = c.conf.GetFipMetaByBank(identifier.GetBank())
		default:
			logger.Error(ctx, "unable to recognise identifier")
			return &caPb.GetFipMetaResponse{Status: rpcPb.StatusInternal()}, nil
		}
		if err != nil {
			logger.Error(ctx, "bank meta not found", zap.Error(err))
			return &caPb.GetFipMetaResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}

		fipMetaList = append(fipMetaList, fipMeta)
	}

	resp := &caPb.GetFipMetaResponse{Status: rpcPb.StatusOk(), FipMetaList: fipMetaList}
	if req.GetIdentifier() != nil && len(fipMetaList) > 0 {
		resp.FipMeta = fipMetaList[len(fipMetaList)-1]
		fipMetaList = fipMetaList[:len(fipMetaList)-1]
	}

	resp.FipMetaList = fipMetaList

	return resp, nil
}

func (c *CaService) GetAllFipMetas(ctx context.Context, req *caPb.GetAllFipMetasRequest) (*caPb.GetAllFipMetasResponse, error) {
	metaList, err := c.conf.GetAllFipMetas(req.GetCaFlowName(), req.GetAppPlatform())
	if err != nil {
		logger.Error(ctx, "no banks found in fip meta map", zap.Error(err))
		return &caPb.GetAllFipMetasResponse{Status: rpcPb.StatusRecordNotFound()}, nil
	}

	return &caPb.GetAllFipMetasResponse{
		Status:      rpcPb.StatusOk(),
		FipMetaList: metaList,
	}, nil
}

func (c *CaService) PublishTxnEventsByTxnIds(ctx context.Context, req *caPb.PublishTxnEventsByTxnIdsRequest) (*caPb.PublishTxnEventsByTxnIdsResponse, error) {
	txnIds := req.GetTxnIds()
	aaTxns, aaTxnsFetchErr := c.txnDao.GetBulkByIds(ctx, txnIds)
	if aaTxnsFetchErr != nil {
		if errors.Is(aaTxnsFetchErr, epifierrors.ErrRecordNotFound) {
			return &caPb.PublishTxnEventsByTxnIdsResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("no transactions found with given txn Ids"),
			}, nil
		}
		logger.Error(ctx, "error fetching bulk aa txns from dao", zap.Error(aaTxnsFetchErr))
		return &caPb.PublishTxnEventsByTxnIdsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error fetching bulk aa txns from dao"),
		}, nil
	}
	if len(aaTxns) != len(txnIds) {
		logger.Error(ctx, "aborting...not all transactions were returned from dao")
		return &caPb.PublishTxnEventsByTxnIdsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("some transactions are not found in db"),
		}, nil
	}
	var depositTxnIds, recurringDepositTxnIds, termDepositTxnIds []string
	txnIdsToTxnsMap := map[string]*caPb.AaTransaction{}
	for _, txn := range aaTxns {
		txnId := txn.GetId()
		txnIdsToTxnsMap[txnId] = txn
		switch txn.GetTxnInstrumentType() {
		case caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_DEPOSIT:
			depositTxnIds = append(depositTxnIds, txnId)
		case caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_RECURRING_DEPOSIT:
			recurringDepositTxnIds = append(recurringDepositTxnIds, txnId)
		case caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_TERM_DEPOSIT:
			termDepositTxnIds = append(termDepositTxnIds, txnId)
		default:
			logger.Error(ctx, "invalid txn instrument type")
			return &caPb.PublishTxnEventsByTxnIdsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("invalid txn instrument type"),
			}, nil
		}
	}
	handleDaoFetchAndPublishErr := c.handleDaoFetchAndPublish(ctx, depositTxnIds, recurringDepositTxnIds, termDepositTxnIds, txnIdsToTxnsMap)
	if handleDaoFetchAndPublishErr != nil {
		logger.Error(ctx, "error fetching txns from dao or publish err", zap.Error(handleDaoFetchAndPublishErr))
		return &caPb.PublishTxnEventsByTxnIdsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error fetching txns from dao or publish err"),
		}, nil
	}
	return &caPb.PublishTxnEventsByTxnIdsResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

func (c *CaService) handleDaoFetchAndPublish(ctx context.Context, depositTxnIds, recurringDepositTxnIds, termDepositTxnIds []string, txnIdsToTxnsMap map[string]*caPb.AaTransaction) error {
	depTxnsErrGrp, _ := errgroup.WithContext(ctx)
	recurringDepTxnsErrGrp, _ := errgroup.WithContext(ctx)
	termDepTxnsErrGrp, _ := errgroup.WithContext(ctx)
	depTxnsErrGrp.Go(func() error {
		depTxns, depTxnsDaoErr := c.depositTxnDao.GetBulkTransactionsByRefIds(ctx, depositTxnIds)
		if errors.Is(depTxnsDaoErr, epifierrors.ErrRecordNotFound) {
			return nil
		}
		if depTxnsDaoErr != nil {
			return depTxnsDaoErr
		}
		for _, depTxn := range depTxns {
			if handlePublishingTxnEventsErr := c.handlePublishingTxnEvents(ctx, depTxn, depTxn.GetTransactionRefId(), txnIdsToTxnsMap); handlePublishingTxnEventsErr != nil {
				return handlePublishingTxnEventsErr
			}
		}
		return nil
	})
	recurringDepTxnsErrGrp.Go(func() error {
		recurringDepTxns, recurringDepTxnsDaoErr := c.recurringDepTxnDao.GetBulkTransactionsByRefIds(ctx, recurringDepositTxnIds)
		if errors.Is(recurringDepTxnsDaoErr, epifierrors.ErrRecordNotFound) {
			return nil
		}
		if recurringDepTxnsDaoErr != nil {
			return recurringDepTxnsDaoErr
		}
		for _, recurringDepTxn := range recurringDepTxns {
			if handlePublishingTxnEventsErr := c.handlePublishingTxnEvents(ctx, recurringDepTxn, recurringDepTxn.GetTransactionRefId(), txnIdsToTxnsMap); handlePublishingTxnEventsErr != nil {
				return handlePublishingTxnEventsErr
			}
		}
		return nil
	})
	termDepTxnsErrGrp.Go(func() error {
		termDepTxns, termDepTxnsDaoErr := c.termDepositTxnDao.GetBulkTransactionsByRefIds(ctx, termDepositTxnIds)
		if errors.Is(termDepTxnsDaoErr, epifierrors.ErrRecordNotFound) {
			return nil
		}
		if termDepTxnsDaoErr != nil {
			return termDepTxnsDaoErr
		}
		for _, termDepTxn := range termDepTxns {
			if handlePublishingTxnEventsErr := c.handlePublishingTxnEvents(ctx, termDepTxn, termDepTxn.GetTransactionRefId(), txnIdsToTxnsMap); handlePublishingTxnEventsErr != nil {
				return handlePublishingTxnEventsErr
			}
		}
		return nil
	})
	if depTxnsErr := depTxnsErrGrp.Wait(); depTxnsErr != nil {
		return errors.Wrap(depTxnsErr, "error fetching and publishing deposit transactions")
	}
	if recurringDepTxnsErr := recurringDepTxnsErrGrp.Wait(); recurringDepTxnsErr != nil {
		return errors.Wrap(recurringDepTxnsErr, "error fetching and publishing recurring deposit transactions")
	}
	if termDepTxnsErr := termDepTxnsErrGrp.Wait(); termDepTxnsErr != nil {
		return errors.Wrap(termDepTxnsErr, "error fetching and publishing term deposit transactions")
	}
	return nil
}

func (c *CaService) handlePublishingTxnEvents(ctx context.Context, txn caPb.Parser, txnId string, txnIdsToTxnsMap map[string]*caPb.AaTransaction) error {
	correspondingAaTxn, ok := txnIdsToTxnsMap[txnId]
	if !ok {
		return fmt.Errorf("key with txn id: %s not present in map", txnId)
	}
	txnEvent, parseErr := txn.ParseToExternalTransactionEvent(correspondingAaTxn, caEnumPb.TransactionEventTypeInitiatedBy_TRANSACTION_EVENT_TYPE_INITIATED_BY_PERIODIC_DATA_PULL_TXNS)
	if parseErr != nil {
		return fmt.Errorf("error parsing to external transaction event, txnId: %s", txnId)
	}
	_, publishErr := c.txnExternalSnsPub.Publish(ctx, txnEvent)
	if publishErr != nil {
		return fmt.Errorf("error publishing transaction event, txnId: %s", txnId)
	}
	return nil
}

func (c *CaService) GetAuthToken(ctx context.Context, req *caPb.GetAuthTokenRequest) (*caPb.GetAuthTokenResponse, error) {
	if req.GetAaEntity() == caEnumPb.AaEntity_AA_ENTITY_UNSPECIFIED {
		return &caPb.GetAuthTokenResponse{Status: rpcPb.StatusInvalidArgument()}, nil
	}
	vendor := caHelper.GetVendorFromAaEntity(req.GetAaEntity())
	vgAaEntity := caHelper.GetVgEntityFromAaEntity(req.GetAaEntity())
	// Get JWT Token from cache
	token, tokenErr := c.caCache.GetAuthToken(ctx, vendor, vgAaEntity)
	if tokenErr != nil {
		logger.Error(ctx, "error fetching jwt token", zap.Error(tokenErr))
		if errors.Is(tokenErr, epifierrors.ErrInvalidArgument) {
			return &caPb.GetAuthTokenResponse{Status: rpcPb.StatusInvalidArgument()}, nil
		}
		return &caPb.GetAuthTokenResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &caPb.GetAuthTokenResponse{Status: rpcPb.StatusOk(), Token: token,
		ExpiryDurationMinutes: int32(c.conf.FinvuTokenExpiryDuration().Minutes())}, nil
}

func (c *CaService) GetTxnDetails(ctx context.Context, req *caPb.GetTxnDetailsRequest) (*caPb.GetTxnDetailsResponse, error) {
	txn, txnErr := c.txnDao.GetById(ctx, req.GetId())
	if txnErr != nil {
		if errors.Is(txnErr, epifierrors.ErrRecordNotFound) {
			return &caPb.GetTxnDetailsResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		return &caPb.GetTxnDetailsResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &caPb.GetTxnDetailsResponse{
		Status:      rpcPb.StatusOk(),
		Transaction: convertToExternalTransaction(txn),
	}, nil
}

func (c *CaService) GetTxnListByActorIdAndAccRefId(_ context.Context, _ *caPb.GetTxnListByActorIdAndAccRefIdRequest) (*caPb.GetTxnListByActorIdAndAccRefIdResponse, error) {
	// RPC that was never used
	return &caPb.GetTxnListByActorIdAndAccRefIdResponse{Status: rpcPb.StatusUnimplemented()}, nil
}

func (c *CaService) GetTotalTransactionsCount(ctx context.Context, req *caPb.GetTotalTransactionsCountRequest) (*caPb.GetTotalTransactionsCountResponse, error) {
	if req.GetActorId() == "" {
		logger.Error(ctx, "actorId is empty ", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &caPb.GetTotalTransactionsCountResponse{Status: rpcPb.StatusInvalidArgument()}, nil
	}

	totalTrxn, err := c.txnDao.GetTotalTxnByActorIdAndAccRefId(ctx, req.GetActorId(), req.GetAccountReferenceId(), req.GetStartTime())
	if err != nil {
		logger.Error(ctx, "error while get total txn by actor_id and account_reference_id", zap.Error(err))
		return &caPb.GetTotalTransactionsCountResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &caPb.GetTotalTransactionsCountResponse{
		Status:                        rpcPb.StatusOk(),
		AccountIdAndTotalTransactions: totalTrxn,
	}, nil
}

func (c *CaService) GetScreenerDecision(ctx context.Context, req *caPb.GetScreenerDecisionRequest) (*caPb.GetScreenerDecisionResponse, error) {
	if req.GetActorId() == "" {
		return &caPb.GetScreenerDecisionResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("actor id not present"),
		}, nil
	}
	// get all connected accounts for an actor
	accounts, err := c.getConnectedAccountsForScreenerDecision(ctx, req.GetActorId(), req.GetCreatedAfter())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &caPb.GetScreenerDecisionResponse{
				Status: rpcPb.StatusRecordNotFoundWithDebugMsg("could not find connected accounts for user"),
			}, nil
		}
		logger.Error(ctx, "error while getting accounts for screener decision", zap.Error(err))
		return &caPb.GetScreenerDecisionResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while getting accounts for actor"),
		}, nil
	}

	if len(accounts) == 0 {
		return &caPb.GetScreenerDecisionResponse{
			Status: rpcPb.StatusRecordNotFoundWithDebugMsg("could not find connected accounts for user"),
		}, nil
	}

	// fetch list of accounts whose data is being synced
	getBalResp := c.getDetailsForScreenerDecision(ctx, accounts)

	// if screener conditions failed, return in progress if any account sync is still in progress
	if getBalResp.numInProgress > 0 {
		return &caPb.GetScreenerDecisionResponse{
			Status: rpcPb.ExtendedStatusInProgress(),
		}, nil
	}
	// make screener decision on income estimate
	screenerPassed, scrErr := c.isScreenerPassed(ctx, req.GetActorId(), getBalResp.balances, getBalResp.openingDates)
	if scrErr != nil {
		if errors.Is(scrErr, epifierrors.ErrInProgress) {
			return &caPb.GetScreenerDecisionResponse{
				Status: rpcPb.ExtendedStatusInProgress(),
			}, nil
		}
		logger.Error(ctx, "error while making screener pass decision", zap.Error(scrErr))
		return &caPb.GetScreenerDecisionResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while making screener decision"),
		}, nil
	}

	metrics.RecordCAIncomeEstimateComputedTime(screenerPassed, accounts[0].GetCreatedAt().AsTime())
	if screenerPassed == commontypes.BooleanEnum_TRUE {
		return &caPb.GetScreenerDecisionResponse{
			Status:       rpcPb.StatusOk(),
			ScreenerPass: commontypes.BooleanEnum_TRUE,
		}, nil
	}

	// screener conditions failed and no data is still being fetched
	return &caPb.GetScreenerDecisionResponse{
		Status:       rpcPb.StatusOk(),
		ScreenerPass: commontypes.BooleanEnum_FALSE,
	}, nil
}

func (c *CaService) getConnectedAccountsForScreenerDecision(ctx context.Context, actorId string, createdAfter *timestampPb.Timestamp) ([]*caPb.AaAccount, error) {
	pageCtx := &rpcPb.PageContextRequest{
		PageSize: 1,
	}
	token, err := pagination.GetPageToken(pageCtx)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get page token")
	}

	accountStatuses := []caEnumPb.AccountStatus{
		caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
		caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_PENDING,
		caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_OFF_PAUSED,
		caEnumPb.AccountStatus_ACCOUNT_STATUS_DISCONNECTED,
		caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_OFF_STOPPED,
	}
	accList, _, err := c.accountDao.GetAccountsPaginated(ctx, &dao.GetAccountsPaginatedReq{
		ActorId:            actorId,
		AccountStatuses:    accountStatuses,
		PageToken:          token,
		PageSize:           pageCtx.GetPageSize(),
		IsFiFedAccIncluded: false,
		CreatedAfter:       createdAfter,
	}, nil)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, err
		}
		return nil, errors.Wrap(err, "error getting account from db")
	}
	return accList, nil
}

// doesAaBelongToUser: An AA account belongs to an actor if:
//  1. If a valid PAN number exists in AA profile, and is the same as user profile PAN, OR
//  2. If PAN number does not exist in AA profile, but AA profile name matches
//     with user profile name (using name match api)
func (c *CaService) doesAaBelongToUser(ctx context.Context, aaHolder *caPb.Holder, userProfile *woPb.GetOrCreateUserResponse_UserDetails) (bool, error) {
	switch {
	case !onboardingPkg.IsValidIndividualPAN(aaHolder.GetPan()):
		ncResp, ncErr := c.nameCheckClient.NameMatch(ctx, &ncPb.NameMatchRequest{
			Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IN_HOUSE},
			Name_1: userProfile.GetName().ToString(),
			Name_2: aaHolder.GetName(),
		})
		if grpcErr := epifigrpc.RPCError(ncResp, ncErr); grpcErr != nil {
			return false, errors.Wrap(grpcErr, "error while performing aa-user name match")
		}
		if ncResp.GetDecision() != ncPb.NameMatchDecision_NAMEMATCH_PASS {
			logger.Info(ctx, "aa-user name match failed")
			return false, nil
		}
	case !strings.EqualFold(aaHolder.GetPan(), userProfile.GetPanDetails().GetId()):
		logger.Info(ctx, "aa-user pan match failed")
		return false, nil
	}
	return true, nil
}

// getDetailsForScreenerDecision fetches account balance and opening dates for a list of accounts.
// if summary was not found, then a count of accounts for which sync is in-progress
// or failed is sent back
func (c *CaService) getDetailsForScreenerDecision(ctx context.Context, accounts []*caPb.AaAccount) *detailsForScreenerDecision {
	var (
		inProgress, failed int32
		wg                 sync.WaitGroup
	)
	for _, account := range accounts {
		wg.Add(1)
		aa := account
		goroutine.Run(ctx, 3*time.Second, func(ctx context.Context) {
			defer logger.RecoverPanicAndError(ctx)
			defer wg.Done()
			resp := c.getAccountInfoForScreenerDecision(ctx, aa)
			switch {
			case resp.inProgress:
				atomic.AddInt32(&inProgress, 1)
			case resp.failed:
				atomic.AddInt32(&failed, 1)
			}
		})
	}
	waitgroup.SafeWaitCtx(ctx, &wg)
	return &detailsForScreenerDecision{
		numInProgress: inProgress,
		numFailed:     failed,
	}

}

// getAccountInfoForScreenerDecision fetches account balance and opening date for an account.
// it returns inProgress as true if summary sync is in progress or failed if we were not able to fetch
// account summary (or if balance/opening date was missing or if account is not active)
func (c *CaService) getAccountInfoForScreenerDecision(_ context.Context, account *caPb.AaAccount) *accountInfoForScreenerDecision {
	switch {
	// if account was synced (at any point in time)
	case account.GetLastSyncedAt() != nil:
		break
	// account sync is pending
	case account.GetAccountStatus() == caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_PENDING:
		return &accountInfoForScreenerDecision{
			inProgress: true,
		}
	default:
		return &accountInfoForScreenerDecision{
			failed: true,
		}
	}
	// in all other cases data fetch was failed / never synced
	return &accountInfoForScreenerDecision{}
}

// isScreenerPassed make screener decision on balance, opening dates of deposit accounts (savings/current), income estimate
func (c *CaService) isScreenerPassed(ctx context.Context, actorId string, balances []*moneyPb.Money, openingDates []*timestampPb.Timestamp) (commontypes.BooleanEnum, error) {
	screenerAttemptId, err := c.getScreenerAttemptId(ctx, actorId)
	if err != nil {
		return commontypes.BooleanEnum_FALSE, err
	}
	uiResp, uiErr := c.uiClient.GetUserIntel(ctx, &userIntelPb.GetUserIntelRequest{
		ActorId:     actorId,
		IntelType:   userIntelPb.IntelType_INTEL_TYPE_AA_INCOME_ESTIMATE,
		AccessType:  userIntelPb.GetUserIntelAccessType_ACCESS_TYPE_FORCE_CACHE,
		ClientReqId: screenerAttemptId,
	})
	if uiResp.GetStatus().IsRecordNotFound() {
		// Fetching user intel from vendor might take time during which user might cancel the operation
		// Create a separate context and use it to fetch income estimate instead
		ctxWithTimeout, cancelCtx := context.WithTimeout(ctx, 30*time.Second)
		defer cancelCtx()

		logger.Info(ctxWithTimeout, "force refreshing data from user intel")
		uiResp, uiErr = c.uiClient.GetUserIntel(ctxWithTimeout, &userIntelPb.GetUserIntelRequest{
			ActorId:     actorId,
			IntelType:   userIntelPb.IntelType_INTEL_TYPE_AA_INCOME_ESTIMATE,
			AccessType:  userIntelPb.GetUserIntelAccessType_ACCESS_TYPE_FORCE_REFRESH,
			ClientReqId: screenerAttemptId,
		})
	}
	uiErr = epifigrpc.RPCError(uiResp, uiErr)
	switch {
	case !uiResp.GetStatus().IsSuccess(),
		uiErr != nil:
		logger.Error(ctx, "Error getting user's income estimate from ds service", zap.Error(uiErr))
		return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, uiErr
	case uiResp.GetUserIntel().GetStatus() == userIntelPb.UserIntelStatus_USER_INTEL_STATUS_UNSPECIFIED:
		logger.Error(ctx, "data pull failed for user")
		return commontypes.BooleanEnum_FALSE, nil
	case uiResp.GetUserIntel().GetStatus() == userIntelPb.UserIntelStatus_USER_INTEL_STATUS_IN_PROGRESS:
		logger.Info(ctx, "Income could not be computed yet")
		return 0, epifierrors.ErrInProgress
	case uiResp.GetUserIntel().GetStatus() == userIntelPb.UserIntelStatus_USER_INTEL_STATUS_UNAVAILABLE:
		logger.Info(ctx, "couldn't estimate the income, failing the attempt")
		return commontypes.BooleanEnum_FALSE, nil
	case uiResp.GetUserIntel().GetStatus() == userIntelPb.UserIntelStatus_USER_INTEL_STATUS_AVAILABLE:
		if decisionmodel.CanPassScreenerForIncome(uiResp.GetUserIntel().GetIntelData().GetAAIncomeEstimateData().GetPredictedIncome()) {
			return commontypes.BooleanEnum_TRUE, nil
		}
		return commontypes.BooleanEnum_FALSE, nil
	default:
		logger.Error(ctx, "should not reach here")
		return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, errors.New("should not reach here")
	}
}

func (c *CaService) getScreenerAttemptId(ctx context.Context, actorId string) (string, error) {
	screenerAttemptResp, respErr := c.screenerClient.GetScreenerAttemptsByActorId(ctx, &screener.GetScreenerAttemptsByActorIdRequest{
		ActorId:    actorId,
		CachedData: true,
	})
	if rpcErr := epifigrpc.RPCError(screenerAttemptResp, respErr); rpcErr != nil {
		logger.Error(ctx, "error in getting screener attempt", zap.Error(rpcErr))
		return "", rpcErr
	}
	for _, checkAttempt := range screenerAttemptResp.GetChecksMap() {
		if checkAttempt.GetCheckType() == screener.CheckType_CHECK_TYPE_CONNECTED_ACCOUNTS &&
			(checkAttempt.GetCheckResult() == screener.CheckResult_CHECK_RESULT_INITIATED || checkAttempt.GetCheckResult() == screener.CheckResult_CHECK_RESULT_IN_PROGRESS) {
			return checkAttempt.GetCheckAttemptId(), nil
		}
	}
	return "", errors.New("check attempt id does not exist")
}

// validateGetSdkDeeplinkRequest will validate the GetSdkDeeplinkForCaFlowRequest fields and the context
// it returns appPlatform and appVersion from request, if any of these turn out to be nil, use the respective value from
// context, if the value turn out be nil in context as well, then return error
func (c *CaService) validateGetSdkDeeplinkRequest(ctx context.Context, appPlatform commontypes.Platform, appVersion uint32) (commontypes.Platform, uint32, error) {
	if appPlatform != commontypes.Platform_PLATFORM_UNSPECIFIED && appVersion != uint32(0) {
		return appPlatform, appVersion, nil
	}
	platformCtx, versionCtx := epificontext.AppPlatformAndVersion(ctx)
	if appPlatform == commontypes.Platform_PLATFORM_UNSPECIFIED {
		if platformCtx == commontypes.Platform_PLATFORM_UNSPECIFIED {
			return commontypes.Platform_PLATFORM_UNSPECIFIED, 0, errors.New("app platform neither set in request nor in context")
		} else {
			appPlatform = platformCtx
		}
	}
	if appVersion == uint32(0) {
		if versionCtx == int(0) {
			return commontypes.Platform_PLATFORM_UNSPECIFIED, 0, errors.New("app version neither set in request nor in context")
		} else {
			appVersion = uint32(versionCtx)
		}
	}
	return appPlatform, appVersion, nil
}

// GetSdkDeeplinkForCaFlow is used to fetch deeplink required for initialising the AA SDK.
// If PAN DOB details are not found, it returns the PAN DOB screen with a deeplink for initializing AA SDK upon collecting PAN DOB details
// @params : GetSdkDeeplinkForCaFlowRequest consists of
//   - Actor ID
//   - App Version : app version for user
//   - App Platform : app platform - Android, iOS etc.
//   - CA Flow Name : SDK deeplink will be fetched based on the ca_flow_name (identifier) of the service
//
// @returns : GetSdkDeeplinkForCaFlowResponse  consists of
//   - RPC Status : INTERNAL in case when AA Entity/V2 Flow or is token auth enabled can't be determined
//     OK in case of no internal error
//   - Deeplink : nil in case of INTERNAL error else returns deeplink required for initialising the AA SDK.
//
// nolint: funlen
// start the flow
func (c *CaService) GetSdkDeeplinkForCaFlow(ctx context.Context, req *caPb.GetSdkDeeplinkForCaFlowRequest) (*caPb.GetSdkDeeplinkForCaFlowResponse, error) {
	// perform validation on request and context
	appPlatform, appVersion, err := c.validateGetSdkDeeplinkRequest(ctx, req.GetAppPlatform(), req.GetAppVersion())
	if err != nil {
		logger.Error(ctx, "GetSdkDeeplinkForCaFlowRequest is not valid", zap.Error(err))
		return &caPb.GetSdkDeeplinkForCaFlowResponse{Status: rpcPb.StatusInvalidArgument()}, nil
	}
	logger.Debug(ctx, "app platform and app version is", zap.Any("AppPlatform", appPlatform), zap.Any("AppVersion", appVersion))
	ctxPlatform, ctxVersion := epificontext.AppPlatformAndVersion(ctx)
	// if platform and version are not present in ctx, then will add this in ctx from given platform and version in request params
	if ctxVersion == 0 && req.GetAppVersion() != 0 {
		ctx = context.WithValue(ctx, epificontext.CtxAppVersionCodeKey, strconv.Itoa(int(req.GetAppVersion())))
	}
	if ctxPlatform == commontypes.Platform_PLATFORM_UNSPECIFIED && req.GetAppPlatform() != commontypes.Platform_PLATFORM_UNSPECIFIED {
		ctx = context.WithValue(ctx, epificontext.CtxAppPlatformKey, req.GetAppPlatform().String())
	}
	userRes, userErr := c.getUserByActorId(ctx, req.GetActorId())
	if userErr != nil {
		logger.Error(ctx, fmt.Sprintf("error getting user by actor id: %v", req.GetActorId()), zap.Error(userErr))
		return &caPb.GetSdkDeeplinkForCaFlowResponse{Status: rpcPb.StatusInternal()}, nil
	}
	aaEntityRes, err := c.getAaEntity(ctx, appPlatform, appVersion, req.GetActorId())
	if err != nil || aaEntityRes == caEnumPb.AaEntity_AA_ENTITY_UNSPECIFIED {
		logger.Error(ctx, "error while getting aa entity due to failure in check with config",
			zap.Any(logger.ACTOR_ID_V2, req.GetActorId()), zap.Any(logger.APP_PLATFORM, appPlatform), zap.Any(logger.APP_VERSION_CODE, appVersion))
		return &caPb.GetSdkDeeplinkForCaFlowResponse{Status: rpcPb.StatusInternal()}, nil
	}
	useV2Flow, v2FlowFlagErr := c.isV2FlowEnabled(appPlatform, appVersion)
	logger.Debug(ctx, "use V2 flag", zap.Any("useV2Flow", useV2Flow))
	if v2FlowFlagErr != nil {
		logger.Error(ctx, "error determining flag for v2 flow", zap.Error(v2FlowFlagErr))
		return &caPb.GetSdkDeeplinkForCaFlowResponse{Status: rpcPb.StatusInternal()}, nil
	}
	dpAaEntity := caHelper.ConvertToDeeplinkAaEntity(caHelper.ConvertToFeAaEntity(aaEntityRes))
	useTokenAuth, useTokenAuthErr := c.isTokenAuthEnabled(ctx, req.GetActorId(), aaEntityRes)
	logger.Debug(ctx, "token auth flag", zap.Any("tokenAuthFlag", useTokenAuth))
	if useTokenAuthErr != nil {
		logger.Error(ctx, "error determining flag for using token auth", zap.Error(useTokenAuthErr))
		return &caPb.GetSdkDeeplinkForCaFlowResponse{Status: rpcPb.StatusInternal()}, nil
	}
	caFlowName := req.GetCaFlowName().String()
	if req.GetCaFlowName() == caEnumPb.CAFlowName_CA_FLOW_NAME_UNSPECIFIED {
		caFlowName = caEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT.String()
	}
	// add flow
	sdkDeeplink := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_CONNECTED_ACCOUNTS_SDK,
		ScreenOptions: &deeplinkPb.Deeplink_ConnectedAccountsOptions{
			ConnectedAccountsOptions: &deeplinkPb.ConnectedAccountsOptions{
				MobileNumber: userRes.GetProfile().GetPhoneNumber(),
				// passing pan name as profile name is deprecated
				Name:                   userRes.GetProfile().GetPanName().ToString(),
				AaEntity:               dpAaEntity,
				FiuId:                  c.conf.FiuId(),
				AaTncMessage:           c.getTncMessageFromEntity(dpAaEntity),
				UseV2Flow:              useV2Flow,
				UseTokenAuthentication: useTokenAuth,
				CaFlowName:             caFlowName,
				Version:                c.GetConnectedAccountSDKVersion(ctx, req.GetActorId(), useV2Flow, req.GetAppPlatform(), req.GetAppVersion(), req.GetCaFlowName()),
			},
		},
	}
	logger.Debug(ctx, "SDK Deeplink CaFlowName field", zap.Any(logger.CA_FLOW, caFlowName))
	finalDeeplink := sdkDeeplink

	if userRes.GetProfile().GetPAN() == "" && c.isPanRequiredFlow(ctx, req.GetActorId(), caFlowName) {
		isWealthBuilderPanFormEnabled, evalErr := c.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_WEALTH_BUILDER_PAN_COLLECTION_FORM).WithActorId(req.GetActorId()))
		if evalErr != nil {
			logger.Error(ctx, "error in evaluating wealth builder pan collection form release config", zap.Error(evalErr))
			return &caPb.GetSdkDeeplinkForCaFlowResponse{Status: rpcPb.StatusInternal()}, nil
		}

		switch {
		case isWealthBuilderPanFormEnabled:
			// pan collection form will be shown if no verified pan is available irrespective of if there is some unverified pan available
			// this allows user to update their pan if they have entered a wrong pan
			finalDeeplink, err = insightsPkg.GetWealthBuilderDataCollectionDeeplink(sdkDeeplink, networthBeFePb.ProfileDataFormIdentifierType_PROFILE_DATA_FORM_IDENTIFIER_TYPE_PAN)
			if err != nil {
				logger.Error(ctx, "error in getting wealth builder pan collection deeplink", zap.Error(err))
				return &caPb.GetSdkDeeplinkForCaFlowResponse{Status: rpcPb.StatusInternal()}, nil
			}
		default:
			finalDeeplink = &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_UPDATE_APP_SCREEN,
			}
		}
	}
	return &caPb.GetSdkDeeplinkForCaFlowResponse{Status: rpcPb.StatusOk(), SdkDeeplinkCaFlow: finalDeeplink}, nil
}

func (c *CaService) isPanRequiredFlow(ctx context.Context, actorId string, caFlowName string) bool {
	releaseConstraint := release.NewCommonConstraintData(types.Feature_FEATURE_CONNECTED_ACCOUNTS_SKIP_PAN_DOB).WithActorId(actorId)
	isEnabled, err := c.releaseEvaluator.Evaluate(ctx, releaseConstraint)
	if err != nil || !isEnabled {
		return true
	}
	logger.Info(ctx, fmt.Sprintf("checking if pan is really required for flow name %v", caFlowName))
	switch caFlowName {
	case caEnumPb.CAFlowName_CA_FLOW_NAME_NET_WORTH_IND_STOCKS.String(), caEnumPb.CAFlowName_CA_FLOW_NAME_NPS.String():
		return true
	default:
		return false
	}
}

// V2FlowParams consist of params that decides whether the V2 Flow should be enabled or not depending on either absolute flag or app version
func (c *CaService) isV2FlowEnabled(appPlatform commontypes.Platform, appVersion uint32) (bool, error) {
	// Return false if appVersion is less than the min version
	// Decide on user group otherwise
	switch appPlatform {
	case commontypes.Platform_ANDROID:
		if appVersion < c.conf.V2FlowParams().MinVersionAndroid() {
			return false, nil
		}
	case commontypes.Platform_IOS:
		if appVersion < c.conf.V2FlowParams().MinVersionIos() {
			return false, nil
		}
	default:
		return false, errors.New("unimplemented appPlatform")
	}
	// If v2 flow is already enabled for the user, send user to v2 flow
	if c.conf.V2FlowParams().UseV2Flow() {
		return true, nil
	}

	// Send user to v2 flow if platform is Android.
	if appPlatform == commontypes.Platform_ANDROID {
		return true, nil
	}
	// Return false otherwise
	return false, nil
}

func (c *CaService) isTokenAuthEnabled(ctx context.Context, actorId string, aaEntity caEnumPb.AaEntity) (commontypes.BooleanEnum, error) {
	if aaEntity != caEnumPb.AaEntity_AA_ENTITY_AA_FINVU {
		return commontypes.BooleanEnum_FALSE, nil
	}
	// check if feature is enabled for the actor id, user group and stickiness constraint or not
	releaseConstraint := release.NewCommonConstraintData(typesPb.Feature_AA_FINVU_TOKEN_AUTHENTICATION).WithActorId(actorId)
	isEnabled, err := c.releaseEvaluator.Evaluate(ctx, releaseConstraint)
	if err != nil {
		return commontypes.BooleanEnum_FALSE, errors.Wrap(err, fmt.Sprintf("error evaluating feature release : %v", releaseConstraint))
	}
	if isEnabled {
		return commontypes.BooleanEnum_TRUE, nil
	}

	return commontypes.BooleanEnum_FALSE, nil
}

func (c *CaService) getTncMessageFromEntity(aaEntity deeplinkPb.ConnectedAccountsOptions_AaEntity) string {
	switch aaEntity {
	case deeplinkPb.ConnectedAccountsOptions_AA_ENTITY_ONE_MONEY:
		return "I agree to OneMoney's <a href=\"" + c.conf.LegalDocuments().AaOnemoneyTncUrl + "\">Terms & Conditions</a>"
	case deeplinkPb.ConnectedAccountsOptions_AA_ENTITY_FINVU:
		return "I agree to Finvu's <a href=\"" + c.conf.LegalDocuments().AaFinvuTncUrl + "\">Terms & Conditions</a>"
	default:
		return ""
	}
}

// this method will fetch user details using actor id
// will return ErrorUserNotFound if we get RecordNotFound status from user service
// will return non nil error for other errors
func (c *CaService) getUserByActorId(ctx context.Context, actorId string) (*userPb.User, error) {
	if actorId == "" {
		return nil, errors.New("actor id is mandatory to get user via actor id")
	}
	// Get user by actor id
	userResp, err := c.userClient.GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: actorId}})
	if userRespRPCErr := epifigrpc.RPCError(userResp, err); userRespRPCErr != nil {
		return nil, errors.Wrap(userRespRPCErr, "error while fetching user from actor id")
	}
	return userResp.GetUser(), nil
}

// getAaEntity will return AA Entity based on the appPlatform and appVersion(taken as params) check from configs
func (c *CaService) getAaEntity(ctx context.Context, appPlatform commontypes.Platform, appVersion uint32, actorId string) (caEnumPb.AaEntity, error) {
	switch appPlatform {
	case commontypes.Platform_IOS:
		if appVersion < c.conf.MinIosVersionEligibleForAaRouting() {
			return caEnumPb.AaEntity_AA_ENTITY_AA_FINVU, nil
		}
	case commontypes.Platform_ANDROID:
		if appVersion < c.conf.MinAndroidVersionEligibleForAaRouting() {
			return caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY, nil
		}
	default:
		return caEnumPb.AaEntity_AA_ENTITY_UNSPECIFIED, errors.New("Unimplemented AppPlatform")
	}
	// hash bucket(of size 1) number is used as deterministic probability
	// ex: for an actor id probability will always be the same(same hash bucket)
	hashBucketForActorId := hash.Hash(actorId, 100) + 1
	aaEntity, err := c.caProp.GetAaEntityForActor(ctx, appPlatform, hashBucketForActorId, actorId)
	if err != nil {
		logger.Error(ctx, "error getting aa entity for actor", zap.Error(err))
		return caEnumPb.AaEntity_AA_ENTITY_UNSPECIFIED, errors.New("error getting aa entity for actor")
	}
	return aaEntity, nil
}

func (c *CaService) GetAccountsForRenewal(ctx context.Context, req *caPb.GetAccountsForRenewalRequest) (*caPb.GetAccountsForRenewalResponse, error) {
	if req.GetCheckSegmentForEligibility() {
		checkEligResp, err := c.CheckEligibilityForConsentRenewal(ctx, &caPb.CheckEligibilityForConsentRenewalRequest{
			ActorId: req.GetActorId(),
		})
		if rpcErr := epifigrpc.RPCError(checkEligResp, err); rpcErr != nil {
			logger.Error(ctx, "failed to check eligibility for consent renewal", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			return &caPb.GetAccountsForRenewalResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}

		if !checkEligResp.GetEligible() {
			logger.Debug(ctx, "user not eligible for consent renewal")
			return &caPb.GetAccountsForRenewalResponse{
				Status:                    rpcPb.StatusOk(),
				RenewalAccountDetailsList: nil,
			}, nil
		}
	}

	renewalAccountDetails, err := c.accountConsentWrapper.GetRelatedDataForRenewal(ctx, req.GetActorId(), req.GetAccountIds(), req.GetOnlyExpired(), true)
	if err != nil {
		logger.Error(ctx, "error getting accounts", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &caPb.GetAccountsForRenewalResponse{Status: rpcPb.StatusInternal()}, nil
	}

	var renewalAccountsList []*caPb.RenewalAccountDetails
	for account, consentExpiry := range renewalAccountDetails {
		extAccount, extAccountErr := c.getExternalAccountDetail(ctx, account)
		if extAccountErr != nil {
			logger.Error(ctx, "error getting external account detail", zap.Error(extAccountErr), zap.String(logger.ACCOUNT_ID, account.GetId()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			return &caPb.GetAccountsForRenewalResponse{Status: rpcPb.StatusInternal()}, nil
		}
		renewalAccount := &caPb.RenewalAccountDetails{
			AccountDetails: extAccount,
			ConsentExpiry:  consentExpiry,
		}
		renewalAccountsList = append(renewalAccountsList, renewalAccount)
	}

	if req.GetSortAccounts() {
		SortRenewalAccountDetails(renewalAccountsList)
	}

	return &caPb.GetAccountsForRenewalResponse{
		Status:                    rpcPb.StatusOk(),
		RenewalAccountDetailsList: renewalAccountsList,
	}, nil
}

func (c *CaService) processTxn(ctx context.Context, inTransactionMethodImpl storagev2.InTransaction) error {
	return c.txnExecutor.RunTxn(ctx, inTransactionMethodImpl)
}

func (c *CaService) UpdateConsentExpiry(ctx context.Context, req *caPb.UpdateConsentExpiryRequest) (*caPb.UpdateConsentExpiryResponse, error) {
	consentRequestObj, err := c.crDao.GetByConsentHandle(ctx, req.GetConsentHandleId(), caPb.ConsentRequestFieldMask_CONSENT_REQUEST_FIELD_MASK_ID)
	if err != nil {
		logger.Error(ctx, "failed to fetch consent request by consent handle id", zap.String(logger.CONSENT_HANDLE, req.GetConsentHandleId()), zap.Error(err))
		return &caPb.UpdateConsentExpiryResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch consent request by consent handle id")}, nil
	}
	consentObj, err := c.cDao.GetByConsentRequestId(ctx, consentRequestObj.GetId())
	if err != nil {
		logger.Error(ctx, "failed to fetch consent by consent request id", zap.String(logger.CONSENT_REQUEST_ID, consentRequestObj.GetId()), zap.String(logger.CONSENT_HANDLE, req.GetConsentHandleId()), zap.Error(err))
		return &caPb.UpdateConsentExpiryResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch consent by consent request id")}, nil
	}
	consentObj.Expiry = req.GetConsentExpiry()
	_, updateErr := c.cDao.UpdateByConsentId(ctx, consentObj, []caPb.ConsentFieldMask{caPb.ConsentFieldMask_CONSENT_FIELD_MASK_EXPIRY})
	if updateErr != nil {
		logger.Error(ctx, "failed to update consent with expiry date", zap.String(logger.CONSENT_ID, consentObj.GetId()), zap.String(logger.CONSENT_REQUEST_ID, consentRequestObj.GetId()), zap.String(logger.CONSENT_HANDLE, req.GetConsentHandleId()), zap.Error(err))
		return &caPb.UpdateConsentExpiryResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to update consent with expiry date")}, nil
	}
	return &caPb.UpdateConsentExpiryResponse{Status: rpcPb.StatusOk()}, nil
}

func (c *CaService) CheckEligibilityForConsentRenewal(ctx context.Context, req *caPb.CheckEligibilityForConsentRenewalRequest) (
	*caPb.CheckEligibilityForConsentRenewalResponse, error) {
	// TODO(vishal): Remove this once it is rolled out 100% external
	actorId := req.GetActorId()
	releaseConstraint := release.NewCommonConstraintData(types.Feature_AA_CONSENT_RENEWAL).WithActorId(actorId)
	isEligible, err := c.releaseEvaluator.Evaluate(ctx, releaseConstraint)
	if err != nil {
		logger.Error(ctx, "error evaluating feature: AA_CONSENT_RENEWAL", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return &caPb.CheckEligibilityForConsentRenewalResponse{
			Status:   rpcPb.StatusInternal(),
			Eligible: false,
		}, nil
	}

	if !isEligible {
		return &caPb.CheckEligibilityForConsentRenewalResponse{
			Status:   rpcPb.StatusOk(),
			Eligible: false,
		}, nil
	}

	// Just for non-prod testing: always return true for non-prod testing since segmentation is not available
	if c.conf.Flags().EnableConsentRenewalSegmentNonProdTest() {
		return &caPb.CheckEligibilityForConsentRenewalResponse{
			Status:   rpcPb.StatusOk(),
			Eligible: true,
		}, nil
	}

	// check is user is present in segment
	isMemberResp, isMemberErr := c.beSegmentationClient.IsMember(ctx, &segmentPb.IsMemberRequest{
		ActorId:    actorId,
		SegmentIds: []string{c.conf.SegmentIds().ConsentRenewal},
	})
	if rpcErr := epifigrpc.RPCError(isMemberResp, isMemberErr); rpcErr != nil {
		logger.Error(ctx, "error in determining consent renewal segment for user", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(isMemberErr))
		return &caPb.CheckEligibilityForConsentRenewalResponse{
			Status:   rpcPb.StatusInternal(),
			Eligible: false,
		}, nil
	}

	if isMemberResp.SegmentMembershipMap[c.conf.SegmentIds().ConsentRenewal].GetIsActorMember() {
		logger.Info(ctx, "user us eligible for consent renewal", zap.Any(logger.ACTOR_ID_V2, actorId))
		return &caPb.CheckEligibilityForConsentRenewalResponse{
			Status:   rpcPb.StatusOk(),
			Eligible: true,
		}, nil
	}

	return &caPb.CheckEligibilityForConsentRenewalResponse{
		Status:   rpcPb.StatusOk(),
		Eligible: false,
	}, nil
}

// nolint:funlen,dupl
func getPanDobDeeplink(sdkDlBytes []byte) *deeplinkPb.Deeplink {
	consents := []*deeplinkPb.Consent{
		{
			Text: onboardingPkg.FiWealthTncText,
			TextV2: &commontypes.Text{
				DisplayValue: &commontypes.Text_Html{
					Html: onboardingPkg.FiWealthTncText,
				},
			},
			Consent: deeplinkPb.ConsentTypeUrl_FI_WEALTH_TNC.String(),
			ConsentInfos: []*deeplinkPb.Consent_ConsentInfo{
				{
					Title:       onboardingPkg.KnowMoreTitle,
					Description: onboardingPkg.KnowMoreDescription,
					TitleV2: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: onboardingPkg.KnowMoreTitle,
						},
					},
					DescriptionV2: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: onboardingPkg.KnowMoreDescription,
						},
					},
				},
				{
					Title:       onboardingPkg.CKYCFailureTitle,
					Description: onboardingPkg.CKYCFailureDescription,
					TitleV2: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: onboardingPkg.CKYCFailureTitle,
						},
					},
					DescriptionV2: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: onboardingPkg.CKYCFailureTitle,
						},
					},
				},
			},
		},
		{
			Text: onboardingPkg.FiPrivacyPolicyText,
			TextV2: &commontypes.Text{
				DisplayValue: &commontypes.Text_Html{
					Html: onboardingPkg.FiPrivacyPolicyText,
				},
			},
			Consent: deeplinkPb.ConsentTypeUrl_FI_PRIVACY_POLICY.String(),
			ConsentInfos: []*deeplinkPb.Consent_ConsentInfo{
				{
					Title:       onboardingPkg.EligibilityChecksTitle,
					Description: onboardingPkg.EligibilityChecksDescription,
				},
			},
		},
	}

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_REGISTER_CKYC,
		ScreenOptions: &deeplinkPb.Deeplink_RegisterCkycScreenOptions{
			RegisterCkycScreenOptions: &deeplinkPb.RegisterCKYCScreenOptions{
				Title:      onboardingPkg.PanDobTitleForCa,
				Subtitle:   onboardingPkg.PanDobSubtitleForCa,
				IsEditable: commontypes.BooleanEnum_TRUE,
				EntryPoint: signup.EntryPoint_ENTRY_POINT_CONNECTED_ACCOUNTS.String(),
				Consents:   consents,
				BackAction: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_HOME,
				},
				BackActionV2: &deeplinkPb.BackAction{
					ShowButton: commontypes.BooleanEnum_TRUE,
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_HOME,
					},
				},
				Blob: sdkDlBytes,
			},
		},
	}
}

// nolint:funlen,dupl
func getPanDobDeeplinkV2(sdkDeeplink *deeplinkPb.Deeplink) *deeplinkPb.Deeplink {
	return panPkg.GetNsdlPanDobForm(&panPkg.GetPanDobFormReq{
		ShouldFetchFromServer:      false,
		IsEditable:                 true,
		ShowFiWealthTncConsent:     true,
		ShowFiPrivacyPolicyConsent: true,
		Title:                      onboardingPkg.PanDobTitleForCa,
		Subtitle:                   onboardingPkg.PanDobSubtitleForCa,
		BackAction: &deeplinkPb.BackAction{
			ShowButton: commontypes.BooleanEnum_TRUE,
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_HOME,
			},
		},
		NextAction: sdkDeeplink,
	})
	// todo[obed]: remove the commented code after supporting info popup in consent checkbox.
	//  the commented code is kept in place to use the copy

	// consents := []*deeplinkPb.Consent{
	//	{
	//		Text: onboardingPkg.FiWealthTncText,
	//		TextV2: &commontypes.Text{
	//			DisplayValue: &commontypes.Text_Html{
	//				Html: onboardingPkg.FiWealthTncText,
	//			},
	//		},
	//		Consent: deeplinkPb.ConsentTypeUrl_FI_WEALTH_TNC.String(),
	//		ConsentInfos: []*deeplinkPb.Consent_ConsentInfo{
	//			{
	//				Title:       onboardingPkg.KnowMoreTitle,
	//				Description: onboardingPkg.KnowMoreDescription,
	//				TitleV2: &commontypes.Text{
	//					DisplayValue: &commontypes.Text_PlainString{
	//						PlainString: onboardingPkg.KnowMoreTitle,
	//					},
	//				},
	//				DescriptionV2: &commontypes.Text{
	//					DisplayValue: &commontypes.Text_PlainString{
	//						PlainString: onboardingPkg.KnowMoreDescription,
	//					},
	//				},
	//			},
	//			{
	//				Title:       onboardingPkg.CKYCFailureTitle,
	//				Description: onboardingPkg.CKYCFailureDescription,
	//				TitleV2: &commontypes.Text{
	//					DisplayValue: &commontypes.Text_PlainString{
	//						PlainString: onboardingPkg.CKYCFailureTitle,
	//					},
	//				},
	//				DescriptionV2: &commontypes.Text{
	//					DisplayValue: &commontypes.Text_PlainString{
	//						PlainString: onboardingPkg.CKYCFailureTitle,
	//					},
	//				},
	//			},
	//		},
	//	},
	//	{
	//		Text: onboardingPkg.FiPrivacyPolicyText,
	//		TextV2: &commontypes.Text{
	//			DisplayValue: &commontypes.Text_Html{
	//				Html: onboardingPkg.FiPrivacyPolicyText,
	//			},
	//		},
	//		Consent: deeplinkPb.ConsentTypeUrl_FI_PRIVACY_POLICY.String(),
	//		ConsentInfos: []*deeplinkPb.Consent_ConsentInfo{
	//			{
	//				Title:       onboardingPkg.EligibilityChecksTitle,
	//				Description: onboardingPkg.EligibilityChecksDescription,
	//			},
	//		},
	//	},
	// }
}

func (c *CaService) ParseAATransaction(ctx context.Context, req *caPb.ParseAATransactionRequest) (*caPb.ParseAATransactionResponse, error) {
	parsedTxn, parseErr := c.transactionProcessor.Parse(ctx, req.GetRawTxn())
	if parseErr != nil {
		rawTxnId := req.GetRawTxn().GetId()
		actorId := req.GetRawTxn().GetActorId()
		logger.Error(ctx, "failed to parse raw AA transaction", zap.Any("RawTxnId", rawTxnId), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &caPb.ParseAATransactionResponse{Status: rpcPb.StatusInternalWithDebugMsg(fmt.Sprintf("failed to parse raw AA transaction with Id %s. ActorId %s Error %v", rawTxnId, actorId, parseErr))}, nil
	}
	return &caPb.ParseAATransactionResponse{
		Status:    rpcPb.StatusOk(),
		ParsedTxn: parsedTxn,
	}, nil
}

func (c *CaService) EnrichAATransaction(ctx context.Context, req *caPb.EnrichAATransactionRequest) (*caPb.EnrichAATransactionResponse, error) {
	parsedTxn := req.GetParsedTxn()
	enrichedTxn, err := c.transactionProcessor.Enrich(ctx, parsedTxn, req.GetTimelineResolutionSource())
	if err != nil {
		return &caPb.EnrichAATransactionResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(fmt.Sprintf("Failed to enrich transaction. Raw Txn Id %s. Actor Id %s. Error %s", parsedTxn.GetRawTransactionId(), parsedTxn.GetActorId(), err.Error())),
		}, nil
	}
	return &caPb.EnrichAATransactionResponse{
		Status:      rpcPb.StatusOk(),
		EnrichedTxn: enrichedTxn,
	}, nil
}

func (c *CaService) PublishListEnrichedAATransaction(ctx context.Context, req *caPb.PublishListEnrichedAATransactionRequest) (*caPb.PublishListEnrichedAATransactionResponse, error) {
	enrichedTxnList := req.GetEnrichedTxnList()
	err := c.transactionProcessor.PublishList(ctx, enrichedTxnList)
	if err != nil {
		logger.Error(ctx, "Failed to publish Enriched Transaction List")
		return &caPb.PublishListEnrichedAATransactionResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(fmt.Sprintf("Failed to publish enriched transactions. Error %s", err.Error())),
		}, nil
	}
	return &caPb.PublishListEnrichedAATransactionResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

func (c *CaService) BackupEnrichedTransactions(ctx context.Context, req *caPb.BackupEnrichedTransactionsRequest) (*caPb.BackupEnrichedTransactionsResponse, error) {
	enrichedTxnBatch := req.GetEnrichedTxnBatch()
	err := c.transactionProcessor.WriteToS3(ctx, req.GetPath(), enrichedTxnBatch)
	if err != nil {
		logger.Error(ctx, "Failed to backup Enriched Transaction List")
		return &caPb.BackupEnrichedTransactionsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(fmt.Sprintf("Failed to backup enriched transactions. Error %s", err.Error())),
		}, nil
	}
	return &caPb.BackupEnrichedTransactionsResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

// nolint:funlen
func (c *CaService) GetEnrichedTransactionsProgress(ctx context.Context, req *caPb.GetEnrichedTransactionsProgressRequest) (*caPb.GetEnrichedTransactionsProgressResponse, error) {
	accountId := req.GetAccountId()
	if accountId == "" {
		logger.Error(ctx, "actorId or/and accountId is empty", zap.String(logger.ACCOUNT_ID, accountId))
		return &caPb.GetEnrichedTransactionsProgressResponse{Status: rpcPb.StatusInvalidArgument()}, nil
	}

	// Get ConsentId from AccountId. Fetch any one Active ConsentId
	consentAccountMappingList, err := c.consentAccMappingDao.GetByAccountReferenceId(ctx, accountId)
	if err != nil && !(errors.Is(err, epifierrors.ErrRecordNotFound)) {
		logger.Error(ctx, "Error getting Consent Account Mapping", zap.String(logger.ACCOUNT_ID, accountId), zap.Error(err))
		return &caPb.GetEnrichedTransactionsProgressResponse{Status: rpcPb.StatusInternalWithDebugMsg(fmt.Sprintf("Error getting Consent Account Mapping for accountId %s", accountId))}, nil
	}
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "Consent Account mapping not found/created", zap.String(logger.ACCOUNT_ID, accountId))
		return &caPb.GetEnrichedTransactionsProgressResponse{Status: rpcPb.StatusInternalWithDebugMsg("Consent Account mapping not found/created for accountId")}, nil
	}

	var (
		consentId  string
		consentObj *connected_account.Consent
	)

	for _, consentAccountMapping := range consentAccountMappingList {
		consentObj, err = c.cDao.Get(ctx, consentAccountMapping.GetConsentReferenceId(), caPb.ConsentFieldMask_CONSENT_FIELD_MASK_CONSENT_ID, caPb.ConsentFieldMask_CONSENT_FIELD_MASK_STATUS)
		if err != nil {
			logger.Error(ctx, "Error getting Consent By Id", zap.String(logger.CONSENT_ID, consentAccountMapping.GetConsentReferenceId()), zap.Error(err))
			continue
		}
		consentStatus := consentObj.GetConsentStatus()
		if consentStatus != caEnumPb.ConsentStatus_CONSENT_STATUS_DELETED_EPIFI {
			consentId = consentAccountMapping.GetConsentReferenceId()
			if consentStatus == caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE {
				break
			}
		}
	}

	if consentId == "" {
		logger.Error(ctx, "Error getting Active Consent for accountId", zap.String(logger.ACCOUNT_ID, accountId))
		return &caPb.GetEnrichedTransactionsProgressResponse{Status: rpcPb.StatusInternalWithDebugMsg("Error getting Active Consent for accountId")}, nil
	}

	// Get last success attempt for a consentId
	_, err = c.dfaDao.GetLatestSuccessAttemptByConsentIdAndInitiatorAndPurpose(ctx, consentId, caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_JOB,
		[]caEnumPb.DataFetchAttemptPurpose{caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_PERIODIC}, caPb.DataFetchAttemptFieldMask_DATA_FETCH_ATTEMPT_FIELD_MASK_UPDATED_AT)

	if err != nil && !(errors.Is(err, epifierrors.ErrRecordNotFound)) {
		logger.Error(ctx, "Error getting data fetch attempt by consentId", zap.String(logger.ACCOUNT_ID, accountId), zap.String(logger.CONSENT_ID, consentId), zap.Error(err))
		return &caPb.GetEnrichedTransactionsProgressResponse{Status: rpcPb.StatusInternalWithDebugMsg("Error getting Latest Data Fetch Attempt by ConsentId")}, nil
	}

	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "Data Fetch Attempt not Created/Completed", zap.String(logger.CONSENT_ID, consentId), zap.String(logger.ACCOUNT_ID, accountId))
		return &caPb.GetEnrichedTransactionsProgressResponse{Status: rpcPb.NewStatus(uint32(caPb.GetEnrichedTransactionsProgressResponse_TRANSIENT_FAILURE), "Transient Failure", "Data Fetch Attempt not Created/Completed")}, nil
	}

	// Get raw transaction count
	rawTxnCount, err := c.txnDao.GetCountForAccount(ctx, accountId)
	if err != nil {
		logger.Error(ctx, "Error getting raw transactions count", zap.String(logger.ACCOUNT_ID, accountId), zap.Error(err))
		return &caPb.GetEnrichedTransactionsProgressResponse{Status: rpcPb.StatusInternalWithDebugMsg("Error getting raw transactions count")}, nil
	}

	if rawTxnCount == 0 {
		logger.Info(ctx, "zero raw transactions found", zap.Any(logger.ACCOUNT_ID, accountId))
		return &caPb.GetEnrichedTransactionsProgressResponse{
			Status:                rpcPb.StatusOk(),
			PersistencePercentage: 100,
			RawTxnCount:           0,
			EnrichedTxnCount:      0,
		}, nil
	}

	// Get enriched transactions count for a given piId
	getTxnCountForAccIdRes, err := c.aaOrderClient.GetTransactionCountForAccountId(ctx, &aaOrderPb.GetTransactionCountForAccountIdRequest{AccountId: accountId})
	if getTxnCountForAccIdResErr := epifigrpc.RPCError(getTxnCountForAccIdRes, err); getTxnCountForAccIdResErr != nil {
		logger.Error(ctx, "Error getting Enriched Transaction Count from PiId", zap.String(logger.ACCOUNT_ID, accountId), zap.Error(err))
		return &caPb.GetEnrichedTransactionsProgressResponse{Status: rpcPb.StatusInternalWithDebugMsg("Error getting enriched transactions count by piId")}, nil
	}

	enrichedTxnCount := getTxnCountForAccIdRes.GetCount()

	return &caPb.GetEnrichedTransactionsProgressResponse{
		Status:                rpcPb.StatusOk(),
		PersistencePercentage: (float64(enrichedTxnCount) / float64(rawTxnCount)) * 100,
		RawTxnCount:           uint32(rawTxnCount),
		EnrichedTxnCount:      uint32(enrichedTxnCount),
	}, nil
}

func (c *CaService) InitiateDataRefreshForAccounts(ctx context.Context, req *caPb.InitiateDataRefreshForAccountsRequest) (*caPb.InitiateDataRefreshForAccountsResponse, error) {
	// stale check duration before. data will be refreshed only if latest data fetch attempt (success/failed) lies before this duration from now
	// Todo(rajEpiFi) Take refresh interval in input if multiple usages of this api arises. Avoiding it currently to prevent misuse.
	var refreshCheckInterval = 3 * 24 * time.Hour
	// map to store the refresh status for each account
	accRefreshRequestStatusMap := make(map[string]caEnumPb.AccountDataRefreshRequestStatus)
	// set to store consents for which data fetch request was initiated
	consentRefreshRequestedSet := make(map[string]struct{})

	for _, accountId := range req.GetAccountIdList() {
		// Get Active consent for account
		activeConsent, activeConsentErr := c.getActiveConsentForAccount(ctx, accountId)
		// Set account status in case of error getting active consent
		if activeConsentErr != nil {
			logger.Error(ctx, "Error getting active consent for account", zap.String(logger.ACCOUNT_ID, accountId), zap.Error(activeConsentErr))
			if errors.Is(activeConsentErr, caError.ErrNoActiveConsentFoundForAccount) {
				accRefreshRequestStatusMap[accountId] = caEnumPb.AccountDataRefreshRequestStatus_ACCOUNT_DATA_REFRESH_REQUEST_STATUS_NO_ACTIVE_CONSENT_FOUND
			} else {
				accRefreshRequestStatusMap[accountId] = caEnumPb.AccountDataRefreshRequestStatus_ACCOUNT_DATA_REFRESH_REQUEST_STATUS_INTERNAL
			}
			continue
		}

		// Check if refresh of this consent is already initiated by another account
		activeConsentId := activeConsent.GetId()
		if _, ok := consentRefreshRequestedSet[activeConsentId]; ok {
			accRefreshRequestStatusMap[accountId] = caEnumPb.AccountDataRefreshRequestStatus_ACCOUNT_DATA_REFRESH_REQUEST_STATUS_INITIATED
			continue
		}

		isDfaStale, isDfaStaleErr := c.evaluateIfDfaIsStale(ctx, activeConsentId, refreshCheckInterval)
		if isDfaStaleErr != nil {
			logger.Error(ctx, "Error getting data fetch attempt by consentId", zap.String(logger.ACCOUNT_ID, accountId), zap.String(logger.CONSENT_ID, activeConsentId), zap.Error(isDfaStaleErr))
			accRefreshRequestStatusMap[accountId] = caEnumPb.AccountDataRefreshRequestStatus_ACCOUNT_DATA_REFRESH_REQUEST_STATUS_INTERNAL
			continue
		}

		// Ignore refresh request if DFA is not stale
		if !isDfaStale {
			accRefreshRequestStatusMap[accountId] = caEnumPb.AccountDataRefreshRequestStatus_ACCOUNT_DATA_REFRESH_REQUEST_STATUS_IGNORED
			continue
		}

		// Initiate refresh for the active consent
		counter := 0 // this variable is only declared since it is a compulsory argument in handleAttempt function
		handleAttemptErr := c.handleAttempt(ctx, activeConsent, &counter)
		if handleAttemptErr != nil {
			accRefreshRequestStatusMap[accountId] = caEnumPb.AccountDataRefreshRequestStatus_ACCOUNT_DATA_REFRESH_REQUEST_STATUS_INTERNAL
			continue
		}
		// add Consent to consentRefreshRequestedSet
		consentRefreshRequestedSet[activeConsentId] = struct{}{}
		accRefreshRequestStatusMap[accountId] = caEnumPb.AccountDataRefreshRequestStatus_ACCOUNT_DATA_REFRESH_REQUEST_STATUS_INITIATED
		continue
	}
	return &caPb.InitiateDataRefreshForAccountsResponse{
		Status:           rpcPb.StatusOk(),
		AccountStatusMap: accRefreshRequestStatusMap,
	}, nil
}

// Evaluates to true if latest data fetch attempt is before refreshCheckInterval. False otherwise
func (c *CaService) evaluateIfDfaIsStale(ctx context.Context, activeConsentId string, refreshCheckInterval time.Duration) (bool, error) {
	// Get latest attempt for consentId. If it is within last 3 days, ignore the refresh request
	latestDfa, latestDfaErr := c.dfaDao.GetLatestConsentAttemptByConsentId(ctx, activeConsentId, caPb.DataFetchAttemptFieldMask_DATA_FETCH_ATTEMPT_FIELD_MASK_CREATED_AT)
	if latestDfaErr != nil && !(errors.Is(latestDfaErr, epifierrors.ErrRecordNotFound)) {
		return false, errors.Wrap(latestDfaErr, "Error evaluating if data refresh is required")
	}
	// Ignore if latest data fetch is within refreshCheckInterval
	if latestDfa.GetCreatedAt().AsTime().After(time.Now().Add(-1 * refreshCheckInterval)) {
		return false, nil
	}
	return true, nil
}

// Get active consent from AccountId. Fetch any one Active Consent
func (c *CaService) getActiveConsentForAccount(ctx context.Context, accountId string) (*caPb.Consent, error) {
	consentAccountMappingList, consentAccountMappingErr := c.consentAccMappingDao.GetByAccountReferenceId(ctx, accountId)
	if consentAccountMappingErr != nil && !(errors.Is(consentAccountMappingErr, epifierrors.ErrRecordNotFound)) {
		return nil, errors.New("error getting consent account mapping")
	}
	if errors.Is(consentAccountMappingErr, epifierrors.ErrRecordNotFound) {
		return nil, caError.ErrNoActiveConsentFoundForAccount
	}
	var (
		activeConsent *caPb.Consent
	)
	for _, consentAccountMapping := range consentAccountMappingList {
		consent, consentErr := c.cDao.Get(ctx, consentAccountMapping.GetConsentReferenceId())
		if consentErr != nil {
			logger.Error(ctx, "Error getting Consent By Id", zap.String(logger.CONSENT_ID, consentAccountMapping.GetConsentReferenceId()), zap.Error(consentErr))
			continue
		}
		if consent.GetConsentStatus() == caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE {
			activeConsent = consent
			break
		}
	}
	if activeConsent == nil {
		return nil, caError.ErrNoActiveConsentFoundForAccount
	}
	return activeConsent, nil
}

func (c *CaService) GetRawTxnsForAccount(ctx context.Context, req *caPb.GetRawTxnsForAccountRequest) (*caPb.GetRawTxnsForAccountResponse, error) {
	token, err := pagination.GetPageToken(req.GetPageContext())
	if err != nil {
		logger.Error(ctx, "failed to get page token", zap.Error(err))
		return &caPb.GetRawTxnsForAccountResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to get page token")}, nil
	}
	txnList, pageResp, err := c.txnDao.GetByAccReferenceId(ctx, req.GetAccountId(), req.GetFilters().GetTransactionDateAfter(), req.GetFilters().GetTransactionDateBefore(), token, req.GetPageContext().GetPageSize())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "No raw transactions found from db", zap.Any(logger.ACCOUNT_ID, req.GetAccountId()), zap.Error(err))
			return &caPb.GetRawTxnsForAccountResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		logger.Error(ctx, "failed to get raw transactions from db", zap.Any(logger.ACCOUNT_ID, req.GetAccountId()), zap.Error(err))
		return &caPb.GetRawTxnsForAccountResponse{Status: rpcPb.StatusInternalWithDebugMsg("error getting raw transactions for account")}, nil
	}
	return &caPb.GetRawTxnsForAccountResponse{Status: rpcPb.StatusOk(), PageContext: pageResp, RawTxnList: txnList}, nil
}

func (c *CaService) GetRawTxnsForAccountV2(ctx context.Context, req *caPb.GetRawTxnsForAccountV2Request) (*caPb.GetRawTxnsForAccountV2Response, error) {
	token, err := pagination.GetPageToken(req.GetPageContext())
	if err != nil {
		logger.Error(ctx, "failed to get page token", zap.Error(err))
		return &caPb.GetRawTxnsForAccountV2Response{Status: rpcPb.StatusInternalWithDebugMsg("failed to get page token")}, nil
	}
	txnList, pageResp, err := c.txnDao.GetByAccReferenceId(ctx, req.GetAccountId(), req.GetFilters().GetTransactionDateAfter(), req.GetFilters().GetTransactionDateBefore(), token, req.GetPageContext().GetPageSize())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "No raw transactions found from db", zap.Any(logger.ACCOUNT_ID, req.GetAccountId()), zap.Error(err))
			return &caPb.GetRawTxnsForAccountV2Response{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		logger.Error(ctx, "failed to get raw transactions from db", zap.Any(logger.ACCOUNT_ID, req.GetAccountId()), zap.Error(err))
		return &caPb.GetRawTxnsForAccountV2Response{Status: rpcPb.StatusInternalWithDebugMsg("error getting raw transactions for account")}, nil
	}

	if len(txnList) == 0 {
		logger.Error(ctx, "no aa transactions found from db", zap.Any(logger.ACCOUNT_ID, req.GetAccountId()), zap.Error(err))
		return &caPb.GetRawTxnsForAccountV2Response{Status: rpcPb.StatusRecordNotFound()}, nil
	}

	rawTxnList, rawListErr := c.fillRawTxnsWithTxns(ctx, txnList, req.GetFiType())
	if rawListErr != nil {
		logger.Error(ctx, "failed to fill raw txns", zap.Error(rawListErr))
		return &caPb.GetRawTxnsForAccountV2Response{Status: rpcPb.StatusInternalWithDebugMsg("failed to fill raw transactions")}, nil
	}

	for _, txn := range rawTxnList {
		if txn.GetAaDepositTransaction().GetDepositTxnMeta().GetCurrentBalance() == "" {
			logger.Info(ctx, "empty current balance found after fetching txns",
				zap.String(logger.TXN_ID, txn.GetAaTransaction().GetId()), zap.String(logger.AA_TXN_ID, txn.GetAaDepositTransaction().GetId()))
		}
	}
	return &caPb.GetRawTxnsForAccountV2Response{Status: rpcPb.StatusOk(), PageContext: pageResp, RawTxnList: rawTxnList}, nil
}

func (c *CaService) fillRawTxnsWithTxns(ctx context.Context, txnList []*caPb.AaTransaction, fiType caEnumPb.AccInstrumentType) ([]*caPb.RawAaTransaction, error) {
	var rawTxnList []*caPb.RawAaTransaction

	var txnRefIds []string
	for _, txn := range txnList {
		txnRefIds = append(txnRefIds, txn.GetId())
		rawTxnList = append(rawTxnList, &caPb.RawAaTransaction{AaTransaction: txn})
	}

	// TODO: complete for all type of transactions
	switch fiType {
	case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT:
		transactions, transactionsErr := c.depositTxnDao.GetBulkTransactionsByRefIds(ctx, txnRefIds)
		if transactionsErr != nil {
			return nil, fmt.Errorf("failed to get deposit transactions: %v", transactionsErr)
		}
		mp := map[string]*caPb.AaDepositTransaction{}
		for _, singleTxn := range transactions {
			mp[singleTxn.GetTransactionRefId()] = singleTxn
		}
		for _, rawTxn := range rawTxnList {
			rawTxn.AdditionalAttributes = &caPb.RawAaTransaction_AaDepositTransaction{AaDepositTransaction: mp[rawTxn.GetAaTransaction().GetId()]}
		}
	case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_RECURRING_DEPOSIT:
		transactions, transactionsErr := c.recurringDepTxnDao.GetBulkTransactionsByRefIds(ctx, txnRefIds)
		if transactionsErr != nil {
			return nil, fmt.Errorf("failed to get recurring deposit transactions: %v", transactionsErr)
		}
		mp := map[string]*caPb.AaRecurringDepositTransaction{}
		for _, singleTxn := range transactions {
			mp[singleTxn.GetTransactionRefId()] = singleTxn
		}
		for _, rawTxn := range rawTxnList {
			rawTxn.AdditionalAttributes = &caPb.RawAaTransaction_AaRecurringDepositTransaction{AaRecurringDepositTransaction: mp[rawTxn.GetAaTransaction().GetId()]}
		}
	case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT:
		transactions, transactionsErr := c.termDepositTxnDao.GetBulkTransactionsByRefIds(ctx, txnRefIds)
		if transactionsErr != nil {
			return nil, fmt.Errorf("failed to get term deposit transactions: %v", transactionsErr)
		}
		mp := map[string]*caPb.AaTermDepositTransaction{}
		for _, singleTxn := range transactions {
			mp[singleTxn.GetTransactionRefId()] = singleTxn
		}
		for _, rawTxn := range rawTxnList {
			rawTxn.AdditionalAttributes = &caPb.RawAaTransaction_AaTermDepositTransaction{AaTermDepositTransaction: mp[rawTxn.GetAaTransaction().GetId()]}
		}
	default:
		return nil, fmt.Errorf("unsupported/unimplemented fiType: %s", fiType.String())
	}
	return rawTxnList, nil
}

type consentHandleToAccIds struct {
	consentHandle string
	accIds        *caPb.GetAccountIdsByConsentHandleListResponse_AccountIdList
}

func (c *CaService) GetAccountIdsByConsentHandleList(ctx context.Context, req *caPb.GetAccountIdsByConsentHandleListRequest) (*caPb.GetAccountIdsByConsentHandleListResponse, error) {
	consentHandles := req.GetConsentHandleList()
	accountIdsChan := make(chan *consentHandleToAccIds, len(consentHandles))
	accountIdErrGrp, _ := errgroup.WithContext(ctx)
	for _, conHandle := range consentHandles {
		consentHandle := conHandle
		accountIdErrGrp.Go(func() error {
			accIds, getAccIdsErr := c.getAccountIdsFromConsentHandle(ctx, consentHandle)
			if getAccIdsErr != nil {
				return fmt.Errorf("error fetching accounts ids from consent handle: %w", getAccIdsErr)
			}
			accountIdsChan <- &consentHandleToAccIds{
				consentHandle: consentHandle,
				accIds: &caPb.GetAccountIdsByConsentHandleListResponse_AccountIdList{
					AccountIdList: accIds,
				},
			}
			return nil
		})
	}
	if accountIdErr := accountIdErrGrp.Wait(); accountIdErr != nil {
		logger.Error(ctx, "error fetching account ids from consent handles", zap.Error(accountIdErr))
	}
	close(accountIdsChan)
	consentHandlesToAccIdsMap := make(map[string]*caPb.GetAccountIdsByConsentHandleListResponse_AccountIdList)
	for consentHandleAccIdList := range accountIdsChan {
		consentHandlesToAccIdsMap[consentHandleAccIdList.consentHandle] = consentHandleAccIdList.accIds
	}
	return &caPb.GetAccountIdsByConsentHandleListResponse{
		Status:                       rpcPb.StatusOk(),
		ConsentHandleToAccountIdsMap: consentHandlesToAccIdsMap,
	}, nil
}

func (c *CaService) getAccountIdsFromConsentHandle(ctx context.Context, consentHandle string) ([]string, error) {
	consentRequest, getCrErr := c.crDao.GetByConsentHandle(ctx, consentHandle,
		caPb.ConsentRequestFieldMask_CONSENT_REQUEST_FIELD_MASK_ID,
		caPb.ConsentRequestFieldMask_CONSENT_REQUEST_FIELD_MASK_STATUS)
	if getCrErr != nil {
		if errors.Is(getCrErr, epifierrors.ErrRecordNotFound) {
			// TODO(sainath): move to debug once stable
			logger.Info(ctx, "no consent request found with given consent handle", zap.String(logger.CONSENT_HANDLE, consentHandle))
			return nil, nil
		}
		return nil, fmt.Errorf("error fetching consent request from consent handle: %w", getCrErr)
	}
	if consentRequest.GetStatus() != caEnumPb.ConsentRequestStatus_CONSENT_REQUEST_STATUS_SUCCESS {
		logger.Info(ctx, "consent request is not in success state", zap.String(logger.STATUS, consentRequest.GetStatus().String()))
		return nil, nil
	}
	con, getConErr := c.cDao.GetByConsentRequestId(ctx, consentRequest.GetId(), caPb.ConsentFieldMask_CONSENT_FIELD_MASK_ID)
	if getConErr != nil {
		return nil, fmt.Errorf("error fetching consent from consent request id: %w", getConErr)
	}
	conAccMappings, getConAccMappingErr := c.consentAccMappingDao.GetByConsentReferenceId(ctx, con.GetId(),
		caPb.ConsentAccountMappingFieldMask_CONSENT_ACCOUNT_MAPPING_FIELD_MASK_ACCOUNT_REFERENCE_ID)
	if getConAccMappingErr != nil {
		return nil, fmt.Errorf("error fetching consent account mapping from consent ref id: %w", getConAccMappingErr)
	}
	accIds := make([]string, 0)
	for _, conAccMapping := range conAccMappings {
		accIds = append(accIds, conAccMapping.GetAccountReferenceId())
	}
	return accIds, nil
}

func (c *CaService) GetConnectedAccountSDKVersion(ctx context.Context, actorId string, isV2FlowEnabled bool, appPlatform commontypes.Platform, appVersion uint32, caFlowName caEnumPb.CAFlowName) deeplinkv2.Version {

	releaseConstraint := release.NewCommonConstraintData(types.Feature_FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW).WithActorId(actorId)
	isEligible, err := c.releaseEvaluator.Evaluate(ctx, releaseConstraint)
	if err != nil {
		logger.Error(ctx, "error evaluating feature: FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		isEligible = false
	}

	if isEligible && caFlowName != caEnumPb.CAFlowName_CA_FLOW_NAME_AA_SALARY {
		return deeplinkv2.Version_VERSION_V3
	}
	if isV2FlowEnabled {
		return deeplinkv2.Version_VERSION_V2
	}
	return deeplinkv2.Version_VERSION_V1
}
