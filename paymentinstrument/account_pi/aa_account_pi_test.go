package accountPi_test

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/accounts"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	accountPi "github.com/epifi/gamma/paymentinstrument/account_pi"
	daoMocks "github.com/epifi/gamma/paymentinstrument/dao/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
)

func TestService_CreateAaAccountPi(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockAaAccountPiDao := daoMocks.NewMockAAAccountPiDao(ctr)
	svc := accountPi.NewService(accountPiDao, nil, nil, mockAaAccountPiDao)

	type mockGetByPiId struct {
		enable bool
		piId   string
		want   *accountPiPb.AAAccountPI
		err    error
	}

	type mockCreateAaAccountPi struct {
		enable    bool
		accountPi *accountPiPb.AAAccountPI
		err       error
	}

	tests := []struct {
		name                  string
		req                   *accountPiPb.CreateAaAccountPiRequest
		mockGetByPiId         mockGetByPiId
		mockCreateAaAccountPi mockCreateAaAccountPi
		want                  *accountPiPb.CreateAaAccountPiResponse
		wantErr               bool
	}{
		{
			name: "Create Account Pi Successfully",
			req: &accountPiPb.CreateAaAccountPiRequest{
				ActorId:     "actor-1",
				AccountId:   "account-1",
				AccountType: accounts.Type_SAVINGS,
				PiId:        "pi-1",
			},
			mockGetByPiId: mockGetByPiId{
				enable: true,
				piId:   "pi-1",
				err:    epifierrors.ErrRecordNotFound,
			},
			mockCreateAaAccountPi: mockCreateAaAccountPi{
				enable: true,
				accountPi: &accountPiPb.AAAccountPI{
					ActorId:     "actor-1",
					AccountId:   "account-1",
					AccountType: accounts.Type_SAVINGS,
					PiId:        "pi-1",
				},
			},
			want: &accountPiPb.CreateAaAccountPiResponse{
				Status: rpc.StatusOk(),
				AccountPi: &accountPiPb.AAAccountPI{
					ActorId:     "actor-1",
					AccountId:   "account-1",
					AccountType: accounts.Type_SAVINGS,
					PiId:        "pi-1",
				},
			},
		},
		{
			name: "Return already existing pi",
			req: &accountPiPb.CreateAaAccountPiRequest{
				ActorId:     "actor-1",
				AccountId:   "account-1",
				AccountType: accounts.Type_SAVINGS,
				PiId:        "pi-1",
			},
			mockGetByPiId: mockGetByPiId{
				enable: true,
				piId:   "pi-1",
				want: &accountPiPb.AAAccountPI{
					ActorId:     "actor-1",
					AccountId:   "account-1",
					AccountType: accounts.Type_SAVINGS,
					PiId:        "pi-1",
				},
			},
			want: &accountPiPb.CreateAaAccountPiResponse{
				Status: rpc.StatusAlreadyExists(),
				AccountPi: &accountPiPb.AAAccountPI{
					ActorId:     "actor-1",
					AccountId:   "account-1",
					AccountType: accounts.Type_SAVINGS,
					PiId:        "pi-1",
				},
			},
		},
		{
			name: "pi linked to different account",
			req: &accountPiPb.CreateAaAccountPiRequest{
				ActorId:     "actor-1",
				AccountId:   "account-1",
				AccountType: accounts.Type_SAVINGS,
				PiId:        "pi-1",
			},
			mockGetByPiId: mockGetByPiId{
				enable: true,
				piId:   "pi-1",
				want: &accountPiPb.AAAccountPI{
					ActorId:     "actor-2",
					AccountId:   "account-2",
					AccountType: accounts.Type_SAVINGS,
					PiId:        "pi-1",
				},
			},
			want: &accountPiPb.CreateAaAccountPiResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockCreateAaAccountPi.enable {
				mockAaAccountPiDao.EXPECT().Create(context.Background(), tt.mockCreateAaAccountPi.accountPi).
					Return(tt.mockCreateAaAccountPi.accountPi, tt.mockCreateAaAccountPi.err)
			}
			if tt.mockGetByPiId.enable {
				mockAaAccountPiDao.EXPECT().GetByPiId(context.Background(), tt.mockGetByPiId.piId).
					Return(tt.mockGetByPiId.want, tt.mockGetByPiId.err)
			}

			got, err := svc.CreateAaAccountPi(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateAaAccountPi() got err: %v, wantErr: %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CreateAaAccountPi() got: %v, want: %v", got, tt.want)
				return
			}
		})
	}

}

func TestService_GetAaAccountPiByAccountId(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockAaAccountPiDao := daoMocks.NewMockAAAccountPiDao(ctr)
	svc := accountPi.NewService(accountPiDao, nil, nil, mockAaAccountPiDao)

	type mockGetByAccountId struct {
		enable    bool
		accountId string
		actType   accounts.Type
		want      []*accountPiPb.AAAccountPI
		err       error
	}

	tests := []struct {
		name               string
		req                *accountPiPb.GetAaAccountPiByAccountIdRequest
		mockGetByAccountId mockGetByAccountId
		want               *accountPiPb.GetAaAccountPiByAccountIdResponse
		wantErr            bool
	}{
		{
			name: "Got account pi successfully",
			req: &accountPiPb.GetAaAccountPiByAccountIdRequest{
				AccountId:   "account-1",
				AccountType: accounts.Type_SAVINGS,
			},
			mockGetByAccountId: mockGetByAccountId{
				enable:    true,
				accountId: "account-1",
				actType:   accounts.Type_SAVINGS,
				want: []*accountPiPb.AAAccountPI{
					{
						ActorId:     "actor-2",
						AccountId:   "account-2",
						AccountType: accounts.Type_SAVINGS,
						PiId:        "pi-1",
					},
				},
			},
			want: &accountPiPb.GetAaAccountPiByAccountIdResponse{
				Status: rpc.StatusOk(),
				AccountPis: []*accountPiPb.AAAccountPI{
					{
						ActorId:     "actor-2",
						AccountId:   "account-2",
						AccountType: accounts.Type_SAVINGS,
						PiId:        "pi-1",
					},
				},
			},
		},
		{
			name: "error fetching account pi",
			req: &accountPiPb.GetAaAccountPiByAccountIdRequest{
				AccountId:   "account-1",
				AccountType: accounts.Type_SAVINGS,
			},
			mockGetByAccountId: mockGetByAccountId{
				enable:    true,
				accountId: "account-1",
				actType:   accounts.Type_SAVINGS,
				err:       errors.New("error fetching account pi"),
			},
			want: &accountPiPb.GetAaAccountPiByAccountIdResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "no aa account pi present",
			req: &accountPiPb.GetAaAccountPiByAccountIdRequest{
				AccountId:   "account-1",
				AccountType: accounts.Type_SAVINGS,
			},
			mockGetByAccountId: mockGetByAccountId{
				enable:    true,
				accountId: "account-1",
				actType:   accounts.Type_SAVINGS,
				err:       epifierrors.ErrRecordNotFound,
			},
			want: &accountPiPb.GetAaAccountPiByAccountIdResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByAccountId.enable {
				mockAaAccountPiDao.EXPECT().GetByAccountId(context.Background(), tt.mockGetByAccountId.accountId,
					tt.mockGetByAccountId.actType).Return(tt.mockGetByAccountId.want, tt.mockGetByAccountId.err)
			}

			got, err := svc.GetAaAccountPiByAccountId(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAaAccountPiByAccountId() got err: %v, wantErr: %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAaAccountPiByAccountId() got: %v, want: %v", got, tt.want)
				return
			}
		})
	}
}

func TestService_GetAaAccountPiByActorId(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockAaAccountPiDao := daoMocks.NewMockAAAccountPiDao(ctr)
	svc := accountPi.NewService(accountPiDao, nil, nil, mockAaAccountPiDao)

	type mockGetByActorId struct {
		enable  bool
		actorId string
		want    []*accountPiPb.AAAccountPI
		err     error
	}

	tests := []struct {
		name             string
		req              *accountPiPb.GetAaAccountPiByActorIdRequest
		mockGetByActorId mockGetByActorId
		want             *accountPiPb.GetAaAccountPiByActorIdResponse
		wantErr          bool
	}{
		{
			name: "Got account pi successfully",
			req: &accountPiPb.GetAaAccountPiByActorIdRequest{
				ActorId: "actor-2",
			},
			mockGetByActorId: mockGetByActorId{
				enable:  true,
				actorId: "actor-2",
				want: []*accountPiPb.AAAccountPI{
					{
						ActorId:     "actor-2",
						AccountId:   "account-2",
						AccountType: accounts.Type_SAVINGS,
						PiId:        "pi-1",
					},
				},
			},
			want: &accountPiPb.GetAaAccountPiByActorIdResponse{
				Status: rpc.StatusOk(),
				AccountPis: []*accountPiPb.AAAccountPI{
					{
						ActorId:     "actor-2",
						AccountId:   "account-2",
						AccountType: accounts.Type_SAVINGS,
						PiId:        "pi-1",
					},
				},
			},
		},
		{
			name: "error fetching account pi",
			req: &accountPiPb.GetAaAccountPiByActorIdRequest{
				ActorId: "actor-2",
			},
			mockGetByActorId: mockGetByActorId{
				enable:  true,
				actorId: "actor-2",
				err:     errors.New("error fetching account pi"),
			},
			want: &accountPiPb.GetAaAccountPiByActorIdResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "no aa account pi present",
			req: &accountPiPb.GetAaAccountPiByActorIdRequest{
				ActorId: "actor-1",
			},
			mockGetByActorId: mockGetByActorId{
				enable:  true,
				actorId: "actor-1",
				err:     epifierrors.ErrRecordNotFound,
			},
			want: &accountPiPb.GetAaAccountPiByActorIdResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByActorId.enable {
				mockAaAccountPiDao.EXPECT().GetByActorId(context.Background(), tt.mockGetByActorId.actorId).
					Return(tt.mockGetByActorId.want, tt.mockGetByActorId.err)
			}

			got, err := svc.GetAaAccountPiByActorId(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAaAccountPiByActorId() got err: %v, wantErr: %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAaAccountPiByActorId() got: %v, want: %v", got, tt.want)
				return
			}
		})
	}
}
