package accountPi

import (
	"context"
	"errors"
	"fmt"

	"github.com/thoas/go-funk"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/accounts"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	account "github.com/epifi/gamma/api/typesv2/account"
	"github.com/epifi/gamma/paymentinstrument/dao"
	piWireTypes "github.com/epifi/gamma/paymentinstrument/wire/types"
)

type Service struct {
	// UnimplementedAccountPIServiceServer is embedded to have forward compatible implementations
	accountPiPb.UnimplementedAccountPIRelationServer
	dao              dao.AccountPiDao
	piEventPublisher piWireTypes.PiEventPublisher
	piClient         piPb.PiClient
	aaAccountPiDao   dao.AAAccountPiDao
}

// Factory method for creating an instance of Service.
func NewService(dao dao.AccountPiDao, piEventPublisher piWireTypes.PiEventPublisher, piClient piPb.PiClient, aaAccountPiDao dao.AAAccountPiDao) *Service {
	return &Service{dao: dao, piEventPublisher: piEventPublisher, piClient: piClient, aaAccountPiDao: aaAccountPiDao}
}

// Create creates a relationship between account <> PI
// returns existing accountPi with status statusAccountPiAlreadyExists if the accountPi already exists
func (s *Service) Create(ctx context.Context, req *accountPiPb.CreateAccountPIRequest) (*accountPiPb.CreateAccountPIResponse, error) {
	var (
		savedAccountPI *accountPiPb.AccountPI
		err            error
		res            = &accountPiPb.CreateAccountPIResponse{}
	)

	fetchedAccountPi, err := s.dao.GetByPiId(ctx, req.GetPiId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		savedAccountPI, err = s.createAccountPi(ctx, req.GetAccountType(), req.GetActorId(), req.GetAccountId(), req.GetPiId(), req.GetApo())
		if err != nil {
			logger.Error(ctx, "error creating account Pi relation", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		res.AccountPi = savedAccountPI
		res.Status = rpc.StatusOk()
	case err != nil:
		logger.Error(ctx, "error fetching accountPi", zap.String("PiId", req.GetPiId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
	case fetchedAccountPi.GetAccountId() == req.GetAccountId() &&
		fetchedAccountPi.GetAccountType() == req.GetAccountType():
		res.AccountPi = fetchedAccountPi
		res.Status = rpc.StatusAlreadyExists()
	default:
		logger.Error(ctx, "piId already linked to a different account", zap.String("PiId", req.GetPiId()))
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	if res.Status != nil && (res.Status.IsSuccess() || res.Status.IsAlreadyExists()) {
		var currPi *accountPiPb.AccountPI
		if fetchedAccountPi != nil {
			currPi = fetchedAccountPi
		} else if savedAccountPI != nil {
			currPi = savedAccountPI
		}
		if currPi != nil {
			// publish pi event
			goroutine.RunWithDefaultTimeout(context.Background(), func(gCtx context.Context) {
				s.PublishPiEvent(gCtx, currPi)
			})
		}
	}
	return res, nil
}

// GetByPiId returns actor_id who owns given pi_id
func (s *Service) GetByPiId(ctx context.Context, req *accountPiPb.GetByPiIdRequest) (*accountPiPb.GetByPiIdResponse, error) {
	var (
		fetchedAccountPi *accountPiPb.AccountPI
		err              error
		res              = &accountPiPb.GetByPiIdResponse{}
	)

	if fetchedAccountPi, err = s.dao.GetByPiId(ctx, req.GetPiId()); err != nil {
		// This should ideally be done by post process middleware
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
		} else {
			logger.Error(ctx, "failed to get PI by id=", zap.String(logger.PI_ID, req.GetPiId()), zap.Error(err))
			res.Status = rpc.StatusInternal()
		}
		return res, nil
	}

	res.Status = rpc.StatusOk()
	res.ActorId = fetchedAccountPi.ActorId
	res.AccountId = fetchedAccountPi.GetAccountId()
	res.AccountType = fetchedAccountPi.GetAccountType()
	res.Apo = fetchedAccountPi.GetApo()

	return res, nil
}

// GetByAccountId fetches PIs belonging to a given accountId
func (s *Service) GetByAccountId(ctx context.Context, req *accountPiPb.GetByAccountIdRequest) (*accountPiPb.GetByAccountIdResponse, error) {
	var (
		fetchedAccountPIs []*accountPiPb.AccountPI
		err               error
		res               = &accountPiPb.GetByAccountIdResponse{}
	)

	if fetchedAccountPIs, err = s.dao.GetByAccountId(ctx, req.GetAccountId()); err != nil {
		logger.Error(ctx, "failed to get account_pi by account id", zap.String("account_id", req.GetAccountId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.AccountPis = fetchedAccountPIs
	res.Status = rpc.StatusOk()
	return res, nil
}

// GetByActorId fetches AccountPIs for a given actor
func (s *Service) GetByActorId(ctx context.Context, req *accountPiPb.GetByActorIdRequest) (*accountPiPb.GetByActorIdResponse, error) {
	var (
		fetchedAccountPIs []*accountPiPb.AccountPI
		err               error
		res               = &accountPiPb.GetByActorIdResponse{}
	)

	if fetchedAccountPIs, err = s.dao.GetByActorId(ctx, req.ActorId); err != nil {
		logger.Error(ctx, "failed to get accountPIs by actorId", zap.String("actor_id", req.ActorId), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.AccountPis = fetchedAccountPIs
	res.Status = rpc.StatusOk()
	return res, nil
}

// createAccountPi  creates account Pi relation for given accountId , accountType, actorId, PiId
// returns savedAccountPi,nil in case of success
// returns nil, relevant error in case of failure/errors
func (s *Service) createAccountPi(ctx context.Context, accountType accounts.Type, actorId, accountId, piId string, apo account.AccountProductOffering) (*accountPiPb.AccountPI, error) {
	accountPI := &accountPiPb.AccountPI{
		ActorId:     actorId,
		AccountId:   accountId,
		AccountType: accountType,
		Apo:         apo,
		PiId:        piId,
	}

	// TODO(kunal): should verify that accountId exists and it belongs to the given actorId
	// Foreign reference makes sure that actorId and PIId exists.
	// Also PiId is unique across the table
	savedAccountPI, err := s.dao.Create(ctx, accountPI)
	if err != nil {
		return nil, fmt.Errorf("error creating accountPi for accountId: %s, PiId %s: %w", accountId, piId, err)
	}
	return savedAccountPI, nil
}

func (s *Service) PublishPiEvent(ctx context.Context, accountPi *accountPiPb.AccountPI) {
	messageId, err := s.piEventPublisher.Publish(ctx, &accountPiPb.AccountPiCreateOrUpdateEvent{
		AccountPi: accountPi,
		EventType: accountPiPb.PiEventType_CREATE,
	})
	if err != nil {
		logger.Error(ctx, "error in publishing account-pi", zap.Error(err), zap.String(logger.PI_ID, accountPi.GetPiId()))
		return
	}
	logger.Info(ctx, "successfully published account-pi event", zap.String(logger.PI_ID, accountPi.GetPiId()), zap.String(logger.QUEUE_MESSAGE_ID, messageId))
}

// GetPiByAccountId fetches PIs belonging to a given account. We will fetch all PI's if pi_types is null
func (s *Service) GetPiByAccountId(ctx context.Context, req *accountPiPb.GetPiByAccountIdRequest) (*accountPiPb.GetPiByAccountIdResponse, error) {
	var (
		res               = &accountPiPb.GetPiByAccountIdResponse{}
		fetchedPis        []*piPb.PaymentInstrument
		fetchedAccountPIs []*accountPiPb.AccountPI
		err               error
	)

	if fetchedAccountPIs, err = s.dao.GetByAccountId(ctx, req.GetAccountId()); err != nil {
		switch {
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			logger.Error(ctx, "unable to fetch account_pi's by account id", zap.String(logger.ACCOUNT_ID, req.AccountId), zap.Error(err))
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		default:
			logger.Error(ctx, "failed to get account_pi by account id", zap.String(logger.ACCOUNT_ID, req.AccountId), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}

	accountPiIds := make([]string, 0)
	for _, accountPI := range fetchedAccountPIs {
		accountPiIds = append(accountPiIds, accountPI.GetPiId())
	}

	// bulk fetch PI using accountPiIds
	pis, err := s.getPisByIds(ctx, accountPiIds)
	if err != nil {
		logger.Error(ctx, "error while fetching pi's for id's", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	switch {
	case req.GetPiStates() == nil && req.GetPiTypes() != nil:
		for _, piById := range pis {
			if funk.Contains(req.GetPiTypes(), piById.GetType()) {
				fetchedPis = append(fetchedPis, piById)
			}
		}
	case req.GetPiStates() != nil && req.GetPiTypes() == nil:
		for _, piById := range pis {
			if funk.Contains(req.GetPiStates(), piById.GetState()) {
				fetchedPis = append(fetchedPis, piById)
			}
		}
	case req.GetPiStates() != nil && req.GetPiTypes() != nil:
		for _, piById := range pis {
			if funk.Contains(req.GetPiStates(), piById.GetState()) && funk.Contains(req.GetPiTypes(), piById.GetType()) {
				fetchedPis = append(fetchedPis, piById)
			}
		}
	default:
		fetchedPis = pis
	}

	res.PaymentInstruments = fetchedPis
	res.Status = rpc.StatusOk()
	return res, nil
}

// GetPiByAccountId fetches PIs belonging to a given actor and filter by provided pi types.
// In case pi types is null all the PIs are returned
func (s *Service) GetPiByActorId(ctx context.Context, req *accountPiPb.GetPiByActorIdRequest) (*accountPiPb.GetPiByActorIdResponse, error) {
	var (
		res               = &accountPiPb.GetPiByActorIdResponse{}
		filteredPis       []*piPb.PaymentInstrument
		fetchedAccountPIs []*accountPiPb.AccountPI
		piIds             []string
		err               error
	)

	if fetchedAccountPIs, err = s.dao.GetByActorId(ctx, req.GetActorId()); err != nil {
		switch {
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			logger.Error(ctx, "unable to fetch account_pi's by actor id", zap.String(logger.ACTOR_ID, req.GetActorId()), zap.Error(err))
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		default:
			logger.Error(ctx, "failed to get account_pi by actor id", zap.String(logger.ACTOR_ID, req.GetActorId()), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}

	for _, accountPI := range fetchedAccountPIs {
		piIds = append(piIds, accountPI.GetPiId())
	}

	// bulk fetch PI using accountPiIds
	pis, err := s.getPisByIds(ctx, piIds)
	if err != nil {
		logger.Error(ctx, "error while fetching pi's for id's", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if len(req.GetPiTypes()) == 0 {
		res.PaymentInstruments = pis
		res.Status = rpc.StatusOk()
		return res, nil
	}

	piTypes := make([]int32, 0)
	for _, piType := range req.GetPiTypes() {
		piTypes = append(piTypes, int32(piType))
	}

	for _, piById := range pis {
		if funk.ContainsInt32(piTypes, int32(piById.GetType())) {
			filteredPis = append(filteredPis, piById)
		}
	}

	res.PaymentInstruments = filteredPis
	res.Status = rpc.StatusOk()

	return res, nil

}

// fetches list of payment instruments corresponding to the given id's
// if there is any processing error returns empty list with error
func (s *Service) getPisByIds(ctx context.Context, ids []string) ([]*piPb.PaymentInstrument, error) {
	piByIdsRes, err := s.piClient.GetPIsByIds(ctx, &piPb.GetPIsByIdsRequest{
		Ids: ids,
	})
	switch {
	case err != nil:
		return nil, fmt.Errorf("RPC call GetPIsByIds failed: %w", err)
	case !piByIdsRes.GetStatus().IsSuccess():
		return nil, fmt.Errorf("received non ok status while fetching Pi's %v", piByIdsRes.Status)
	}
	return piByIdsRes.GetPaymentinstruments(), nil
}
