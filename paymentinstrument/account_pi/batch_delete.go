package accountPi

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/be-common/pkg/logger"
)

// BatchHardDeleteAaAccountPi does hard delete of account pi relation for connected accounts from the database.
// The RPC works on all or none principle for all eligible entries (eligible = ids for which entry is present in the DB).
// i.e. eligible entry deletion happen in an atomic manner
// TODO(nitesh/16827): add UT
func (s *Service) BatchHardDeleteAaAccountPi(ctx context.Context, req *accountPiPb.BatchHardDeleteAaAccountPiRequest) (*accountPiPb.BatchHardDeleteAaAccountPiResponse, error) {
	res := &accountPiPb.BatchHardDeleteAaAccountPiResponse{}

	err := s.aaAccountPiDao.DeleteByAccountId(ctx, req.GetAccountId())
	if err != nil {
		logger.Error(ctx, "failed to delete wealth pis", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// TODO(nitesh/16826): add logic to publish deletion event for the stack holders to update

	res.Status = rpc.StatusOk()
	return res, nil
}
