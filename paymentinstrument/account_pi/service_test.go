package accountPi_test

import (
	"context"
	"flag"
	"fmt"
	"os"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/idgen"
	queueMock "github.com/epifi/be-common/pkg/queue/mocks"
	pkgTestv2 "github.com/epifi/be-common/pkg/test/v2"

	accountsPb "github.com/epifi/gamma/api/accounts"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	"github.com/epifi/gamma/api/typesv2/account"
	accountPi "github.com/epifi/gamma/paymentinstrument/account_pi"
	"github.com/epifi/gamma/paymentinstrument/config"
	"github.com/epifi/gamma/paymentinstrument/dao"
	daoMocks "github.com/epifi/gamma/paymentinstrument/dao/mocks"
	"github.com/epifi/gamma/paymentinstrument/test"
)

var (
	db                       *gormv2.DB
	conf                     *config.Config
	accountPiDao             dao.AccountPiDao
	accountPiRelationService *accountPi.Service
)

var (
	fixtureActorId     = "actor-user-1"
	fixtureAccountId   = "Test-Savings-1"
	fixtureAccountType = accountsPb.Type_SAVINGS
	fixtureAccountPi1  = &accountPiPb.AccountPI{
		Id:          "account-pi-1",
		ActorId:     "actor-user-1",
		AccountId:   "Test-Savings-1",
		AccountType: accountsPb.Type_SAVINGS,
		PiId:        "pi-1",
	}
	fixturePi1 = &piPb.PaymentInstrument{
		Id:   "pi-1",
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.PaymentInstrument_Account{
			Account: &piPb.Account{
				ActualAccountNumber: "***************",
				SecureAccountNumber: "xxxxxxxxxxx2939",
				IfscCode:            "FDRL0001001",
				AccountType:         accountsPb.Type_SAVINGS,
			},
		},
		VerifiedName: "Kunal",
		State:        piPb.PaymentInstrumentState_CREATED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
	}
	fixturePi2 = &piPb.PaymentInstrument{
		Id:   "pi-2",
		Type: piPb.PaymentInstrumentType_UPI,
		Identifier: &piPb.PaymentInstrument_Account{
			Account: &piPb.Account{
				ActualAccountNumber: "***************",
				SecureAccountNumber: "xxxxxxxxxxx2939",
				IfscCode:            "FDRL0001001",
				AccountType:         accountsPb.Type_SAVINGS,
			},
		},
		VerifiedName: "Kunal",
		State:        piPb.PaymentInstrumentState_CREATED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
	}
	fixturePi3 = &piPb.PaymentInstrument{
		Id:   "pi-3",
		Type: piPb.PaymentInstrumentType_DEBIT_CARD,
		Identifier: &piPb.PaymentInstrument_Account{
			Account: &piPb.Account{
				ActualAccountNumber: "***************",
				SecureAccountNumber: "xxxxxxxxxxx2939",
				IfscCode:            "FDRL0001001",
				AccountType:         accountsPb.Type_SAVINGS,
			},
		},
		VerifiedName: "Kunal",
		State:        piPb.PaymentInstrumentState_VERIFIED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
	}
	affectedTestTables = []string{"account_pis"}
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()

	var teardown func()
	conf, _, db, teardown = test.InitTestServer()

	ctrl := gomock.NewController(&testing.T{})

	accountPiIdgen := idgen.NewDomainIdGenerator(idgen.NewClock())
	accountPiDao = dao.NewAccountPiDaoPgdb(db, accountPiIdgen)
	accountPiRelationService = accountPi.NewService(accountPiDao, queueMock.NewMockPublisher(ctrl), nil, nil)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestAccountPIService_Create(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTestv2.TruncateAndPopulateRdsFixtures(t, db, conf.PaymentInstrumentDb.GetName(), affectedTestTables)
	tests := []struct {
		name    string
		req     *accountPiPb.CreateAccountPIRequest
		want    *accountPiPb.CreateAccountPIResponse
		wantErr bool
	}{
		{
			name: "Should create account <> PI relation successfully",
			req: &accountPiPb.CreateAccountPIRequest{
				ActorId:     fixtureActorId,
				AccountId:   "random-account-id",
				AccountType: fixtureAccountType,
				PiId:        "pi-2",
			},
			want: &accountPiPb.CreateAccountPIResponse{
				Status: rpc.StatusOk(),
				AccountPi: &accountPiPb.AccountPI{
					AccountId:   "random-account-id",
					ActorId:     fixtureActorId,
					PiId:        "pi-2",
					AccountType: fixtureAccountType,
					Apo:         account.AccountProductOffering_APO_REGULAR,
				},
			},
		},
		{
			name: "Account Pi relation already exists",
			req: &accountPiPb.CreateAccountPIRequest{
				ActorId:     fixtureActorId,
				AccountId:   fixtureAccountId,
				AccountType: fixtureAccountType,
				PiId:        "pi-1",
			},
			want: &accountPiPb.CreateAccountPIResponse{
				Status: rpc.StatusAlreadyExists(),
				AccountPi: &accountPiPb.AccountPI{
					ActorId:     fixtureActorId,
					AccountId:   fixtureAccountId,
					AccountType: fixtureAccountType,
					PiId:        "pi-1",
				},
			},
		},
		{
			name: "Pi already linked to a different account",
			req: &accountPiPb.CreateAccountPIRequest{
				ActorId:     fixtureActorId,
				AccountId:   "random-account",
				AccountType: fixtureAccountType,
				PiId:        "pi-1",
			},
			want: &accountPiPb.CreateAccountPIResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := accountPiRelationService.Create(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !isAccountPiDeepEqual(got.AccountPi, tt.want.AccountPi) {
				t.Errorf("Create() got = %v, want %v", got, tt.want)
			}
		})
	}

}

func TestAccountPIService_GetByAccountId(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	// Clean database, run migrations and load fixtures
	pkgTestv2.TruncateAndPopulateRdsFixtures(t, db, conf.PaymentInstrumentDb.GetName(), affectedTestTables)

	getByAccountIdResp, err := accountPiRelationService.GetByAccountId(context.Background(), &accountPiPb.GetByAccountIdRequest{
		AccountId:   fixtureAccountId,
		AccountType: fixtureAccountType,
	})
	assert.Nil(t, err)

	assert.Equal(t, rpc.StatusOk(), getByAccountIdResp.Status)
	assert.Equal(t, 1, len(getByAccountIdResp.AccountPis))
	assert.Equal(t, getByAccountIdResp.AccountPis[0].AccountId, fixtureAccountId)
	assert.Equal(t, getByAccountIdResp.AccountPis[0].AccountType, fixtureAccountType)
}

func TestService_GetByPiId(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	// Clean database, run migrations and load fixtures
	pkgTestv2.TruncateAndPopulateRdsFixtures(t, db, conf.PaymentInstrumentDb.GetName(), affectedTestTables)

	type args struct {
		ctx context.Context
		req *accountPiPb.GetByPiIdRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *accountPiPb.GetByPiIdResponse
		wantErr bool
	}{
		{
			name: "Should successfully fetch actor who owns PI",
			args: args{
				ctx: context.Background(),
				req: &accountPiPb.GetByPiIdRequest{
					PiId: fixtureAccountPi1.PiId,
				},
			},
			want: &accountPiPb.GetByPiIdResponse{
				Status:      rpc.StatusOk(),
				ActorId:     fixtureAccountPi1.ActorId,
				AccountId:   fixtureAccountPi1.AccountId,
				AccountType: accountsPb.Type_SAVINGS,
			},
			wantErr: false,
		},
		{
			name: "Should fail to fetch actor due to not found error",
			args: args{
				ctx: context.Background(),
				req: &accountPiPb.GetByPiIdRequest{
					PiId: "random-pi-1",
				},
			},
			want: &accountPiPb.GetByPiIdResponse{
				Status:  rpc.StatusRecordNotFound(),
				ActorId: "",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := accountPiRelationService.GetByPiId(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByPiId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetByPiId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetByActorId(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	// Clean database, run migrations and load fixtures
	pkgTestv2.TruncateAndPopulateRdsFixtures(t, db, conf.PaymentInstrumentDb.GetName(), affectedTestTables)

	type args struct {
		ctx context.Context
		req *accountPiPb.GetByActorIdRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *accountPiPb.GetByActorIdResponse
		wantErr bool
	}{
		{
			name: "Should successfully fetch AccountPIs",
			args: args{
				ctx: context.Background(),
				req: &accountPiPb.GetByActorIdRequest{
					ActorId: fixtureAccountPi1.ActorId,
				},
			},
			want: &accountPiPb.GetByActorIdResponse{
				Status:     rpc.StatusOk(),
				AccountPis: []*accountPiPb.AccountPI{fixtureAccountPi1},
			},
			wantErr: false,
		},
		{
			name: "Should successfully fetch 0 AccountPIs",
			args: args{
				ctx: context.Background(),
				req: &accountPiPb.GetByActorIdRequest{
					ActorId: "random-actor-id",
				},
			},
			want: &accountPiPb.GetByActorIdResponse{
				Status:     rpc.StatusOk(),
				AccountPis: []*accountPiPb.AccountPI{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := accountPiRelationService.GetByActorId(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if got != nil && got.Status.Code != tt.want.Status.Code {
				t.Errorf("timelineService.Create() status: got = %v, want %v", got.Status, tt.want.Status)
			}

			for i := range tt.want.AccountPis {
				if !isAccountPiDeepEqual(got.AccountPis[i], tt.want.AccountPis[i]) {
					t.Errorf("GetByActorId() index = %d,\ngot %v,\nwant %v", i, got.AccountPis[i], tt.want.AccountPis[i])
				}
			}
		})
	}
}

// isAccountPiDeepEqual compares 2 values of type *accountPiPb.AccountPI without the standard timestamp fields
func isAccountPiDeepEqual(actual, expected *accountPiPb.AccountPI) bool {
	if actual != nil && expected != nil {
		expected.CreatedAt = actual.CreatedAt
		expected.UpdatedAt = actual.UpdatedAt
		expected.DeletedAt = actual.DeletedAt
		expected.Id = actual.Id
	}
	return proto.Equal(actual, expected)
}

func TestService_GetPiByAccountId(t *testing.T) {

	ctr := gomock.NewController(t)
	mockAccountPiDao := daoMocks.NewMockAccountPiDao(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	accountPiService := accountPi.NewService(mockAccountPiDao, queueMock.NewMockPublisher(ctr), mockPiClient, nil)

	type mockGetByAccountIdDao struct {
		enable      bool
		accountId   string
		accountType accountsPb.Type
		res         []*accountPiPb.AccountPI
		err         error
	}

	type mockGetPisByIds struct {
		enable bool
		req    *piPb.GetPIsByIdsRequest
		res    *piPb.GetPIsByIdsResponse
		err    error
	}

	tests := []struct {
		name                  string
		req                   *accountPiPb.GetPiByAccountIdRequest
		mockGetPisByIds       mockGetPisByIds
		mockGetByAccountIdDao mockGetByAccountIdDao
		want                  *accountPiPb.GetPiByAccountIdResponse
		wantErr               bool
	}{
		{
			name: "Successfully fetched pi's of all types with null piTypes",
			req: &accountPiPb.GetPiByAccountIdRequest{
				AccountId:   "account-1",
				AccountType: accountsPb.Type_SAVINGS,
				PiTypes:     nil,
			},
			mockGetByAccountIdDao: mockGetByAccountIdDao{
				enable:      true,
				accountId:   "account-1",
				accountType: accountsPb.Type_SAVINGS,
				res: []*accountPiPb.AccountPI{
					{
						Id:          "",
						ActorId:     "",
						AccountId:   "account-1",
						AccountType: accountsPb.Type_SAVINGS,
						PiId:        fixturePi1.Id,
					},
					{
						Id:          "",
						ActorId:     "",
						AccountId:   "account-1",
						AccountType: accountsPb.Type_SAVINGS,
						PiId:        fixturePi2.Id,
					},
				},
				err: nil,
			},
			mockGetPisByIds: mockGetPisByIds{
				enable: true,
				req:    &piPb.GetPIsByIdsRequest{Ids: []string{fixturePi1.Id, fixturePi2.Id}},
				res: &piPb.GetPIsByIdsResponse{
					Status:             rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{fixturePi1, fixturePi2},
				},
				err: nil,
			},
			want: &accountPiPb.GetPiByAccountIdResponse{
				Status:             rpc.StatusOk(),
				PaymentInstruments: []*piPb.PaymentInstrument{fixturePi1, fixturePi2},
			},
		},
		{
			name: "Successfully fetched pi's of all types",
			req: &accountPiPb.GetPiByAccountIdRequest{
				AccountId:   "account-1",
				AccountType: accountsPb.Type_SAVINGS,
				PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI, piPb.PaymentInstrumentType_BANK_ACCOUNT},
			},
			mockGetByAccountIdDao: mockGetByAccountIdDao{
				enable:      true,
				accountId:   "account-1",
				accountType: accountsPb.Type_SAVINGS,
				res: []*accountPiPb.AccountPI{
					{
						Id:          "",
						ActorId:     "",
						AccountId:   "account-1",
						AccountType: accountsPb.Type_SAVINGS,
						PiId:        fixturePi1.Id,
					},
					{
						Id:          "",
						ActorId:     "",
						AccountId:   "account-1",
						AccountType: accountsPb.Type_SAVINGS,
						PiId:        fixturePi2.Id,
					},
				},
				err: nil,
			},
			mockGetPisByIds: mockGetPisByIds{
				enable: true,
				req:    &piPb.GetPIsByIdsRequest{Ids: []string{fixturePi1.Id, fixturePi2.Id}},
				res: &piPb.GetPIsByIdsResponse{
					Status:             rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{fixturePi1, fixturePi2},
				},
				err: nil,
			},
			want: &accountPiPb.GetPiByAccountIdResponse{
				Status:             rpc.StatusOk(),
				PaymentInstruments: []*piPb.PaymentInstrument{fixturePi1, fixturePi2},
			},
		},
		{
			name: "successfully fetched pi of type upi",
			req: &accountPiPb.GetPiByAccountIdRequest{
				AccountId:   "account-1",
				AccountType: accountsPb.Type_SAVINGS,
				PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI},
			},
			mockGetByAccountIdDao: mockGetByAccountIdDao{
				enable:      true,
				accountId:   "account-1",
				accountType: accountsPb.Type_SAVINGS,
				res: []*accountPiPb.AccountPI{
					{
						Id:          "",
						ActorId:     "",
						AccountId:   "account-1",
						AccountType: accountsPb.Type_SAVINGS,
						PiId:        fixturePi1.Id,
					},
					{
						Id:          "",
						ActorId:     "",
						AccountId:   "account-1",
						AccountType: accountsPb.Type_SAVINGS,
						PiId:        fixturePi2.Id,
					},
				},
				err: nil,
			},
			mockGetPisByIds: mockGetPisByIds{
				enable: true,
				req:    &piPb.GetPIsByIdsRequest{Ids: []string{fixturePi1.Id, fixturePi2.Id}},
				res: &piPb.GetPIsByIdsResponse{
					Status:             rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{fixturePi1, fixturePi2},
				},
				err: nil,
			},
			want: &accountPiPb.GetPiByAccountIdResponse{
				Status:             rpc.StatusOk(),
				PaymentInstruments: []*piPb.PaymentInstrument{fixturePi2},
			},
		},
		{
			name: "Successfully fetched pi's of type account",
			req: &accountPiPb.GetPiByAccountIdRequest{
				AccountId:   "account-1",
				AccountType: accountsPb.Type_SAVINGS,
				PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_BANK_ACCOUNT},
			},
			mockGetByAccountIdDao: mockGetByAccountIdDao{
				enable:      true,
				accountId:   "account-1",
				accountType: accountsPb.Type_SAVINGS,
				res: []*accountPiPb.AccountPI{
					{
						Id:          "",
						ActorId:     "",
						AccountId:   "account-1",
						AccountType: accountsPb.Type_SAVINGS,
						PiId:        fixturePi1.Id,
					},
					{
						Id:          "",
						ActorId:     "",
						AccountId:   "account-1",
						AccountType: accountsPb.Type_SAVINGS,
						PiId:        fixturePi2.Id,
					},
				},
				err: nil,
			},
			mockGetPisByIds: mockGetPisByIds{
				enable: true,
				req:    &piPb.GetPIsByIdsRequest{Ids: []string{fixturePi1.Id, fixturePi2.Id}},
				res: &piPb.GetPIsByIdsResponse{
					Status:             rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{fixturePi1, fixturePi2},
				},
				err: nil,
			},
			want: &accountPiPb.GetPiByAccountIdResponse{
				Status:             rpc.StatusOk(),
				PaymentInstruments: []*piPb.PaymentInstrument{fixturePi1},
			},
		},
		{
			name: "Successfully fetched pi's where filter by Pi state and Pi type",
			req: &accountPiPb.GetPiByAccountIdRequest{
				AccountId:   "account-1",
				AccountType: accountsPb.Type_SAVINGS,
				PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_DEBIT_CARD},
				PiStates:    []piPb.PaymentInstrumentState{piPb.PaymentInstrumentState_VERIFIED},
			},
			mockGetByAccountIdDao: mockGetByAccountIdDao{
				enable:      true,
				accountId:   "account-1",
				accountType: accountsPb.Type_SAVINGS,
				res: []*accountPiPb.AccountPI{
					{
						Id:          "",
						ActorId:     "",
						AccountId:   "account-1",
						AccountType: accountsPb.Type_SAVINGS,
						PiId:        fixturePi1.Id,
					},
					{
						Id:          "",
						ActorId:     "",
						AccountId:   "account-1",
						AccountType: accountsPb.Type_SAVINGS,
						PiId:        fixturePi2.Id,
					},
					{
						Id:          "",
						ActorId:     "",
						AccountId:   "account-1",
						AccountType: accountsPb.Type_SAVINGS,
						PiId:        fixturePi3.Id,
					},
				},
				err: nil,
			},
			mockGetPisByIds: mockGetPisByIds{
				enable: true,
				req:    &piPb.GetPIsByIdsRequest{Ids: []string{fixturePi1.Id, fixturePi2.Id, fixturePi3.Id}},
				res: &piPb.GetPIsByIdsResponse{
					Status:             rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{fixturePi1, fixturePi2, fixturePi3},
				},
				err: nil,
			},
			want: &accountPiPb.GetPiByAccountIdResponse{
				Status:             rpc.StatusOk(),
				PaymentInstruments: []*piPb.PaymentInstrument{fixturePi3},
			},
		},
		{
			name: "Error while fetching account pi",
			req: &accountPiPb.GetPiByAccountIdRequest{
				AccountId:   "account-1",
				AccountType: accountsPb.Type_SAVINGS,
				PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_BANK_ACCOUNT},
			},
			mockGetByAccountIdDao: mockGetByAccountIdDao{
				enable:      true,
				accountId:   "account-1",
				accountType: accountsPb.Type_SAVINGS,
				res:         nil,
				err:         fmt.Errorf("error while fetching account pi"),
			},
			want: &accountPiPb.GetPiByAccountIdResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "Error while fetching pi's by id's",
			req: &accountPiPb.GetPiByAccountIdRequest{
				AccountId:   "account-1",
				AccountType: accountsPb.Type_SAVINGS,
				PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_BANK_ACCOUNT},
			},
			mockGetByAccountIdDao: mockGetByAccountIdDao{
				enable:      true,
				accountId:   "account-1",
				accountType: accountsPb.Type_SAVINGS,
				res: []*accountPiPb.AccountPI{
					{
						Id:          "",
						ActorId:     "",
						AccountId:   "account-1",
						AccountType: accountsPb.Type_SAVINGS,
						PiId:        fixturePi1.Id,
					},
					{
						Id:          "",
						ActorId:     "",
						AccountId:   "account-1",
						AccountType: accountsPb.Type_SAVINGS,
						PiId:        fixturePi2.Id,
					},
				},
				err: nil,
			},
			mockGetPisByIds: mockGetPisByIds{
				enable: true,
				req:    &piPb.GetPIsByIdsRequest{Ids: []string{fixturePi1.Id, fixturePi2.Id}},
				res: &piPb.GetPIsByIdsResponse{
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
			want: &accountPiPb.GetPiByAccountIdResponse{
				Status: rpc.StatusInternal(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByAccountIdDao.enable {
				mockAccountPiDao.EXPECT().GetByAccountId(context.Background(), tt.mockGetByAccountIdDao.accountId).
					Return(tt.mockGetByAccountIdDao.res, tt.mockGetByAccountIdDao.err)
			}
			if tt.mockGetPisByIds.enable {
				mockPiClient.EXPECT().GetPIsByIds(context.Background(), tt.mockGetPisByIds.req).
					Return(tt.mockGetPisByIds.res, tt.mockGetPisByIds.err)
			}

			got, err := accountPiService.GetPiByAccountId(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPiByAccountId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assertPis(t, tt.want.PaymentInstruments, got.PaymentInstruments)

		})
	}
}

func TestService_GetPiByActorId(t *testing.T) {
	ctr := gomock.NewController(t)
	mockAccountPiDao := daoMocks.NewMockAccountPiDao(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	accountPiService := accountPi.NewService(mockAccountPiDao, queueMock.NewMockPublisher(ctr), mockPiClient, nil)

	type mockGetByActorId struct {
		enable  bool
		actorId string
		res     []*accountPiPb.AccountPI
		err     error
	}
	type mockGetPisByIds struct {
		enable bool
		req    *piPb.GetPIsByIdsRequest
		res    *piPb.GetPIsByIdsResponse
		err    error
	}
	tests := []struct {
		name             string
		mockGetByActorId mockGetByActorId
		mockGetPisByIds  mockGetPisByIds
		req              *accountPiPb.GetPiByActorIdRequest
		want             *accountPiPb.GetPiByActorIdResponse
		wantErr          bool
	}{
		{
			name: "Successfully fetched pi's of all types with null piTypes",
			req: &accountPiPb.GetPiByActorIdRequest{
				ActorId: "actor-1",
				PiTypes: nil,
			},
			mockGetByActorId: mockGetByActorId{
				enable:  true,
				actorId: "actor-1",
				res: []*accountPiPb.AccountPI{
					{
						Id:          "",
						ActorId:     "actor-1",
						AccountId:   "account-2",
						AccountType: accountsPb.Type_SAVINGS,
						PiId:        fixturePi1.Id,
					},
					{
						Id:          "",
						ActorId:     "actor-1",
						AccountId:   "account-2",
						AccountType: accountsPb.Type_SAVINGS,
						PiId:        fixturePi2.Id,
					},
				},
				err: nil,
			},
			mockGetPisByIds: mockGetPisByIds{
				enable: true,
				req:    &piPb.GetPIsByIdsRequest{Ids: []string{fixturePi1.Id, fixturePi2.Id}},
				res: &piPb.GetPIsByIdsResponse{
					Status:             rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{fixturePi1, fixturePi2},
				},
				err: nil,
			},
			want: &accountPiPb.GetPiByActorIdResponse{
				Status:             rpc.StatusOk(),
				PaymentInstruments: []*piPb.PaymentInstrument{fixturePi1, fixturePi2},
			},
		},
		{
			name: "Successfully fetched pi's of all types",
			req: &accountPiPb.GetPiByActorIdRequest{
				ActorId: "actor-1",
				PiTypes: []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI, piPb.PaymentInstrumentType_BANK_ACCOUNT},
			},
			mockGetByActorId: mockGetByActorId{
				enable:  true,
				actorId: "actor-1",
				res: []*accountPiPb.AccountPI{
					{
						Id:          "",
						ActorId:     "actor-1",
						AccountId:   "account-1",
						AccountType: accountsPb.Type_SAVINGS,
						PiId:        fixturePi1.Id,
					},
					{
						Id:          "",
						ActorId:     "actor-1",
						AccountId:   "account-2",
						AccountType: accountsPb.Type_SAVINGS,
						PiId:        fixturePi2.Id,
					},
				},
				err: nil,
			},
			mockGetPisByIds: mockGetPisByIds{
				enable: true,
				req:    &piPb.GetPIsByIdsRequest{Ids: []string{fixturePi1.Id, fixturePi2.Id}},
				res: &piPb.GetPIsByIdsResponse{
					Status:             rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{fixturePi1, fixturePi2},
				},
				err: nil,
			},
			want: &accountPiPb.GetPiByActorIdResponse{
				Status:             rpc.StatusOk(),
				PaymentInstruments: []*piPb.PaymentInstrument{fixturePi1, fixturePi2},
			},
		},
		{
			name: "successfully fetched pi of type upi",
			req: &accountPiPb.GetPiByActorIdRequest{
				ActorId: "actor-1",
				PiTypes: []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI},
			},
			mockGetByActorId: mockGetByActorId{
				enable:  true,
				actorId: "actor-1",
				res: []*accountPiPb.AccountPI{
					{
						Id:          "",
						ActorId:     "actor-1",
						AccountId:   "account-1",
						AccountType: accountsPb.Type_SAVINGS,
						PiId:        fixturePi1.Id,
					},
					{
						Id:          "",
						ActorId:     "actor-1",
						AccountId:   "account-2",
						AccountType: accountsPb.Type_SAVINGS,
						PiId:        fixturePi2.Id,
					},
				},
				err: nil,
			},
			mockGetPisByIds: mockGetPisByIds{
				enable: true,
				req:    &piPb.GetPIsByIdsRequest{Ids: []string{fixturePi1.Id, fixturePi2.Id}},
				res: &piPb.GetPIsByIdsResponse{
					Status:             rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{fixturePi1, fixturePi2},
				},
				err: nil,
			},
			want: &accountPiPb.GetPiByActorIdResponse{
				Status:             rpc.StatusOk(),
				PaymentInstruments: []*piPb.PaymentInstrument{fixturePi2},
			},
		},
		{
			name: "Error while fetching account pi",
			req: &accountPiPb.GetPiByActorIdRequest{
				ActorId: "actor-1",
				PiTypes: []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_BANK_ACCOUNT},
			},
			mockGetByActorId: mockGetByActorId{
				enable:  true,
				actorId: "actor-1",
				res:     nil,
				err:     fmt.Errorf("error while fetching account pi"),
			},
			want: &accountPiPb.GetPiByActorIdResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "Error while fetching pi's by id's",
			req: &accountPiPb.GetPiByActorIdRequest{
				ActorId: "actor-1",
				PiTypes: []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_BANK_ACCOUNT},
			},
			mockGetByActorId: mockGetByActorId{
				enable:  true,
				actorId: "actor-1",
				res: []*accountPiPb.AccountPI{
					{
						Id:          "",
						ActorId:     "actor-1",
						AccountId:   "account-1",
						AccountType: accountsPb.Type_SAVINGS,
						PiId:        fixturePi1.Id,
					},
					{
						Id:          "",
						ActorId:     "actor-1",
						AccountId:   "account-2",
						AccountType: accountsPb.Type_SAVINGS,
						PiId:        fixturePi2.Id,
					},
				},
				err: nil,
			},
			mockGetPisByIds: mockGetPisByIds{
				enable: true,
				req:    &piPb.GetPIsByIdsRequest{Ids: []string{fixturePi1.Id, fixturePi2.Id}},
				res: &piPb.GetPIsByIdsResponse{
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
			want: &accountPiPb.GetPiByActorIdResponse{
				Status: rpc.StatusInternal(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByActorId.enable {
				mockAccountPiDao.EXPECT().GetByActorId(context.Background(), tt.mockGetByActorId.actorId).
					Return(tt.mockGetByActorId.res, tt.mockGetByActorId.err)
			}
			if tt.mockGetPisByIds.enable {
				mockPiClient.EXPECT().GetPIsByIds(context.Background(), tt.mockGetPisByIds.req).
					Return(tt.mockGetPisByIds.res, tt.mockGetPisByIds.err)
			}

			got, err := accountPiService.GetPiByActorId(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPiByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPiByActorId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

// assertPis compares slice of type *piPb.PaymentInstrument without the standard timestamp fields for each
func assertPis(t *testing.T, expected, actual []*piPb.PaymentInstrument) {
	if expected == nil && actual == nil {
		return
	}
	if len(expected) != len(actual) {
		t.Errorf("number of pi's got = %v not equal to number of pi's want %v", len(actual), len(expected))
	}
	for i := range actual {
		assertPi(t, expected[i], actual[i])
	}
}

// assertPi compares Payment instrument without the standard timestamp fields
func assertPi(t *testing.T, expected, actual *piPb.PaymentInstrument) {
	if expected == nil && actual == nil {
		return
	}

	expected.CreatedAt = actual.CreatedAt
	expected.UpdatedAt = actual.UpdatedAt
	expected.DeletedAt = actual.DeletedAt
	assert.EqualValues(t, expected, actual)
}
