package accountPi

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/accounts"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

// CreateAaAccountPi created account pi relation for connected accounts
func (s *Service) CreateAaAccountPi(ctx context.Context, req *accountPiPb.CreateAaAccountPiRequest) (*accountPiPb.CreateAaAccountPiResponse, error) {
	var (
		savedAccountPI *accountPiPb.AAAccountPI
		err            error
		res            = &accountPiPb.CreateAaAccountPiResponse{}
	)

	fetchedAccountPi, err := s.aaAccountPiDao.GetByPiId(ctx, req.GetPiId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		savedAccountPI, err = s.createAaAccountPi(ctx, req.GetAccountType(),
			req.GetActorId(), req.GetAccountId(), req.GetPiId())
		if err != nil {
			logger.Error(ctx, "error creating account Pi relation", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		res.AccountPi = savedAccountPI
		res.Status = rpc.StatusOk()
	case err != nil:
		logger.Error(ctx, "error fetching aa-accountPi", zap.String(logger.PI_ID, req.GetPiId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
	case fetchedAccountPi.GetAccountId() == req.GetAccountId() &&
		fetchedAccountPi.GetAccountType() == req.GetAccountType():
		res.AccountPi = fetchedAccountPi
		res.Status = rpc.StatusAlreadyExists()
	default:
		logger.Error(ctx, "piId already linked to a different account", zap.String(logger.PI_ID, req.GetPiId()), zap.String(logger.ACCOUNT_ID, fetchedAccountPi.GetId()))
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}
	return res, nil
}

// GetAaAccountPiByAccountId returns account pi relation for aa account for the given account id
func (s *Service) GetAaAccountPiByAccountId(ctx context.Context, req *accountPiPb.GetAaAccountPiByAccountIdRequest) (*accountPiPb.GetAaAccountPiByAccountIdResponse, error) {
	var (
		fetchedAccountPIs []*accountPiPb.AAAccountPI
		err               error
		res               = &accountPiPb.GetAaAccountPiByAccountIdResponse{}
	)

	if fetchedAccountPIs, err = s.aaAccountPiDao.GetByAccountId(ctx, req.GetAccountId(), req.GetAccountType()); err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		logger.Error(ctx, "failed to get aa account_pi by account id", zap.String(logger.ACCOUNT_ID, req.GetAccountId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.AccountPis = fetchedAccountPIs
	res.Status = rpc.StatusOk()
	return res, nil
}

// createAaAccountPi  creates aa account Pi relation for given accountId , accountType, actorId, PiId
// returns savedAccountPi,nil in case of success
// returns nil, relevant error in case of failure/errors
func (s *Service) createAaAccountPi(ctx context.Context, accountType accounts.Type, actorId, accountId, piId string) (*accountPiPb.AAAccountPI, error) {
	accountPI := &accountPiPb.AAAccountPI{
		ActorId:     actorId,
		AccountId:   accountId,
		AccountType: accountType,
		PiId:        piId,
	}
	savedAccountPI, err := s.aaAccountPiDao.Create(ctx, accountPI)
	if err != nil {
		return nil, fmt.Errorf("error creating aa accountPi for accountId: %s, PiId %s: %w", accountId, piId, err)
	}
	return savedAccountPI, nil
}

// GetAaAccountPiByActorId returns account pi relation for aa account for the given actor id
func (s *Service) GetAaAccountPiByActorId(ctx context.Context, req *accountPiPb.GetAaAccountPiByActorIdRequest) (*accountPiPb.GetAaAccountPiByActorIdResponse, error) {
	var (
		fetchedAccountPIs []*accountPiPb.AAAccountPI
		err               error
		res               = &accountPiPb.GetAaAccountPiByActorIdResponse{}
	)

	fetchedAccountPIs, err = s.aaAccountPiDao.GetByActorId(ctx, req.GetActorId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "failed to get aa account_pi by actor id", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.AccountPis = fetchedAccountPIs
	res.Status = rpc.StatusOk()
	return res, nil
}
