package dao_test

import (
	"context"
	"testing"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/proto"
	gormV2 "gorm.io/gorm"

	"github.com/stretchr/testify/assert"

	accountsPb "github.com/epifi/gamma/api/accounts"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/typesv2/account"
	"github.com/epifi/gamma/paymentinstrument/config/genconf"
	"github.com/epifi/gamma/paymentinstrument/dao"
)

type accountPiTestSuite struct {
	db           *gormV2.DB
	conf         *genconf.Config
	accountPiDao dao.AccountPiDao
}

var (
	fixtureActorId     = "actor-user-1"
	fixturePiId        = "pi-1"
	fixtureAccountId   = "Test-Savings-1"
	fixtureAccountType = accountsPb.Type_SAVINGS
	fixtureAccountPi1  = &accountPiPb.AccountPI{
		Id:          "account-pi-1",
		ActorId:     "actor-user-1",
		AccountId:   "Test-Savings-1",
		AccountType: accountsPb.Type_SAVINGS,
		PiId:        "pi-1",
	}
	accountPiAffectedTestTables = []string{"account_pis"}
)

// isAccountPiDeepEqual compares 2 values of type *accountPiPb.AccountPI without the standard timestamp fields
func isAccountPiDeepEqual(actual, expected *accountPiPb.AccountPI, ignoreIdField bool) bool {
	if actual != nil && expected != nil {
		expected.CreatedAt = actual.GetCreatedAt()
		expected.UpdatedAt = actual.GetUpdatedAt()
		expected.DeletedAt = actual.GetDeletedAt()
		if ignoreIdField {
			expected.Id = actual.GetId()
		}
	}
	return proto.Equal(actual, expected)
}

// todo: can be removed in favour of table driven tests `TestAccountPIPgdb_Create1`
func TestAccountPIPgdb_Create(t *testing.T) {
	testSuite, testSuiteReleaseFunc := getAccountPiTestSuite(t)
	accountPiDao := testSuite.accountPiDao
	defer testSuiteReleaseFunc()

	accountId := "random-account-id"
	fixturesPIId2 := "pi-2"

	savedAccountPI, err := accountPiDao.Create(context.Background(), &accountPiPb.AccountPI{
		ActorId:     fixtureActorId,
		AccountId:   accountId,
		AccountType: fixtureAccountType,
		PiId:        fixturesPIId2,
	})
	assert.Nil(t, err)

	fetchedAccountPIs, fetchErr := accountPiDao.GetByAccountId(context.Background(), accountId)
	assert.Nil(t, fetchErr)

	assert.Equal(t, len(fetchedAccountPIs), 1)
	accountPi := fetchedAccountPIs[0]
	if !isAccountPiDeepEqual(accountPi, savedAccountPI, false) {
		t.Errorf("Create() : got %v, want %v", accountPi, savedAccountPI)
	}
}

func TestAccountPIPgdb_Create1(t *testing.T) {
	testSuite, testSuiteReleaseFunc := getAccountPiTestSuite(t)
	accountPiDao := testSuite.accountPiDao
	defer testSuiteReleaseFunc()

	type args struct {
		ctx       context.Context
		accountPi *accountPiPb.AccountPI
	}

	tests := []struct {
		name    string
		args    args
		want    *accountPiPb.AccountPI
		wantErr bool
	}{
		{
			name: "should create account_pi entry with default account product offering",
			args: args{
				ctx: context.Background(),
				accountPi: &accountPiPb.AccountPI{
					Id:          "unique-id-for-account-pi-1",
					ActorId:     "actor-user-1",
					AccountId:   "random-acc-id-1-uniq-1",
					AccountType: accountsPb.Type_SAVINGS,
					PiId:        "pi-6",
				},
			},
			want: &accountPiPb.AccountPI{
				ActorId:     "actor-user-1",
				AccountId:   "random-acc-id-1-uniq-1",
				AccountType: accountsPb.Type_SAVINGS,
				Apo:         account.AccountProductOffering_APO_REGULAR,
				PiId:        "pi-6",
			},
			wantErr: false,
		},
		{
			name: "should create account_pi entry with explicit account product offering",
			args: args{
				ctx: context.Background(),
				accountPi: &accountPiPb.AccountPI{
					Id:          "unique-id-for-account-pi-2",
					ActorId:     "actor-user-1",
					AccountId:   "random-acc-id-1-uniq-2",
					AccountType: accountsPb.Type_SAVINGS,
					Apo:         account.AccountProductOffering_APO_NRE,
					PiId:        "pi-7",
				},
			},
			want: &accountPiPb.AccountPI{
				ActorId:     "actor-user-1",
				AccountId:   "random-acc-id-1-uniq-2",
				AccountType: accountsPb.Type_SAVINGS,
				Apo:         account.AccountProductOffering_APO_NRE,
				PiId:        "pi-7",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := accountPiDao.Create(tt.args.ctx, tt.args.accountPi)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			fetchedAccountPIs, fetchErr := accountPiDao.GetByAccountId(context.Background(), tt.args.accountPi.GetAccountId())
			if fetchErr != nil {
				t.Errorf("Error fetching created account_pi entry: %v", fetchErr)
				return
			}

			accountPi := fetchedAccountPIs[0]

			if !isAccountPiDeepEqual(accountPi, tt.want, true) {
				t.Errorf("GetByAccountId() got = %v, want %v", accountPi, tt.want)
			}
		})
	}

}

func TestAccountPiDaoPgdb_GetByPiId(t *testing.T) {
	testSuite, testSuiteReleaseFunc := getAccountPiTestSuite(t)
	accountPiDao := testSuite.accountPiDao
	defer testSuiteReleaseFunc()

	type args struct {
		ctx  context.Context
		piId string
	}
	tests := []struct {
		name    string
		args    args
		want    *accountPiPb.AccountPI
		wantErr bool
	}{
		{
			name: "Should successfully fetch AccountPI",
			args: args{
				ctx:  context.Background(),
				piId: fixtureAccountPi1.GetPiId(),
			},
			want:    fixtureAccountPi1,
			wantErr: false,
		},
		{
			name: "Should successfully fetch AccountPI with Apo as UNSPECIFIED (already existing entry in db)",
			args: args{
				ctx:  context.Background(),
				piId: fixtureAccountPi1.GetPiId(),
			},
			want: &accountPiPb.AccountPI{
				Id:          "account-pi-1",
				ActorId:     "actor-user-1",
				AccountId:   "Test-Savings-1",
				AccountType: accountsPb.Type_SAVINGS,
				Apo:         account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
				PiId:        "pi-1",
			},
			wantErr: false,
		},
		{
			name: "Should fail to fetch due to not found error",
			args: args{
				ctx:  context.Background(),
				piId: "random-pi-id",
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := accountPiDao.GetByPiId(tt.args.ctx, tt.args.piId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByPiId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !isAccountPiDeepEqual(got, tt.want, false) {
				t.Errorf("GetByPiId() got = %v, want %v, diff %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestAccountPIPgdb_GetByAccountId(t *testing.T) {
	testSuite, testSuiteReleaseFunc := getAccountPiTestSuite(t)
	accountPiDao := testSuite.accountPiDao
	defer testSuiteReleaseFunc()
	fetchedAccountPIs, fetchErr := accountPiDao.GetByAccountId(context.Background(), fixtureAccountId)
	assert.Nil(t, fetchErr)

	assert.Equal(t, len(fetchedAccountPIs), 1)
	assert.Equal(t, fetchedAccountPIs[0].ActorId, fixtureActorId)
	assert.Equal(t, fetchedAccountPIs[0].AccountId, fixtureAccountId)
	assert.Equal(t, fetchedAccountPIs[0].AccountType, fixtureAccountType)
	assert.Equal(t, fetchedAccountPIs[0].PiId, fixturePiId)
}

func TestAccountPiDaoPgdb_GetByActorId(t *testing.T) {
	testSuite, testSuiteReleaseFunc := getAccountPiTestSuite(t)
	accountPiDao := testSuite.accountPiDao
	defer testSuiteReleaseFunc()
	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name    string
		args    args
		want    []*accountPiPb.AccountPI
		wantErr bool
	}{
		{
			name: "Should successfully fetch AccountPIs",
			args: args{
				ctx:     context.Background(),
				actorId: fixtureAccountPi1.GetActorId(),
			},
			want:    []*accountPiPb.AccountPI{fixtureAccountPi1},
			wantErr: false,
		},
		{
			name: "Should successfully fetch 0 AccountPIs",
			args: args{
				ctx:     context.Background(),
				actorId: "random-actor-id",
			},
			want:    []*accountPiPb.AccountPI{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := accountPiDao.GetByActorId(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			for i := range tt.want {
				if !isAccountPiDeepEqual(got[i], tt.want[i], false) {
					t.Errorf("GetByActorId() index = %d, got %v, want %v, diff %s", i, got, tt.want, cmp.Diff(got[i], tt.want[i]))
				}
			}
		})
	}
}
