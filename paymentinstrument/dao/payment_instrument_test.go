// nolint: goimports
package dao_test

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/testing/protocmp"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	"github.com/thoas/go-funk"
	"google.golang.org/protobuf/types/known/timestamppb"

	gormV2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/epifierrors"

	accountsPb "github.com/epifi/gamma/api/accounts"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/typesv2/account"
	"github.com/epifi/gamma/paymentinstrument/config"
	"github.com/epifi/gamma/paymentinstrument/config/genconf"
	"github.com/epifi/gamma/paymentinstrument/dao"
	"github.com/epifi/gamma/paymentinstrument/dao/model"
)

type piTestSuite struct {
	db      *gormV2.DB
	conf    *config.Config
	piDao   dao.PiDao
	genConf *genconf.Config
}

var (
	ctxWithLlOwnership = epificontext.WithOwnership(context.Background(), commontypes.Ownership_LIQUILOANS_PL)
	fixturePi1         = &piPb.PaymentInstrument{
		Id:   "pi-1",
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.PaymentInstrument_Account{
			Account: &piPb.Account{
				ActualAccountNumber: "***************",
				SecureAccountNumber: "xxxxxxxxxxx2939",
				IfscCode:            "FDRL0001001",
				AccountType:         accountsPb.Type_SAVINGS,
			},
		},
		VerifiedName: "Kunal",
		State:        piPb.PaymentInstrumentState_CREATED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
	}
	fixturePi1Clone = func() *piPb.PaymentInstrument {
		return proto.Clone(fixturePi1).(*piPb.PaymentInstrument)
	}
	fixturePi2Wealth = &piPb.PaymentInstrument{
		Id:   "pi-2",
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.PaymentInstrument_Account{
			Account: &piPb.Account{
				ActualAccountNumber: "***************",
				SecureAccountNumber: "xxxxxxxxxxx2938",
				IfscCode:            "FDRL0001001",
				AccountType:         accountsPb.Type_SAVINGS,
			},
		},
		VerifiedName: "Kunal",
		State:        piPb.PaymentInstrumentState_CREATED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
		Ownership:            piPb.Ownership_EPIFI_WEALTH,
	}
	updateFixturePi3 = &piPb.PaymentInstrument{
		Id:   "pi-3",
		Type: piPb.PaymentInstrumentType_UPI,
		Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
			Vpa:                    "nitesh@fede",
			AccountReferenceNumber: "testAccountRefNumber",
			IfscCode:               "test-code",
			MerchantDetails:        &piPb.Upi_MerchantDetails{},
		}},
		VerifiedName: "nitesh",
		State:        piPb.PaymentInstrumentState_CREATED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
		LastVerifiedAt:       &timestamppb.Timestamp{Seconds: 100000},
	}

	updateFixturePi3Capability = &piPb.PaymentInstrument{
		Id:   "pi-3",
		Type: piPb.PaymentInstrumentType_UPI,
		Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
			Vpa:                    "nitesh@fede",
			AccountReferenceNumber: "testAccountRefNumber",
			IfscCode:               "test-code",
			MerchantDetails: &piPb.Upi_MerchantDetails{
				Mcc:                "mcc",
				MerchantGenre:      "genre",
				MerchantId:         "merchant-id",
				MerchantStoreId:    "store-id",
				MerchantTerminalId: "terminal-id",
			},
		}},
		VerifiedName: "nitesh",
		State:        piPb.PaymentInstrumentState_CREATED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String(): false,
		},
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
		LastVerifiedAt:       &timestamppb.Timestamp{Seconds: 100000},
	}
	updatedFixturePi3Capability = &piPb.PaymentInstrument{
		Id:   "pi-3",
		Type: piPb.PaymentInstrumentType_UPI,
		Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
			Vpa:                    "nitesh@fede",
			AccountReferenceNumber: "testAccountRefNumber",
			IfscCode:               "test-code",
			AccountType:            accountsPb.Type_SAVINGS,
			Apo:                    account.AccountProductOffering_APO_NRE,
			MerchantDetails: &piPb.Upi_MerchantDetails{
				Mcc:                "mcc",
				MerchantGenre:      "genre",
				MerchantId:         "merchant-id",
				MerchantStoreId:    "store-id",
				MerchantTerminalId: "terminal-id",
			},
		}},
		VerifiedName: "nitesh",
		State:        piPb.PaymentInstrumentState_CREATED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  false,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
		LastVerifiedAt:       &timestamppb.Timestamp{Seconds: 100000},
	}

	upiLitePi = &piPb.PaymentInstrument{
		Id:   "pi-22",
		Type: piPb.PaymentInstrumentType_UPI_LITE,
		Identifier: &piPb.PaymentInstrument_UpiLite{UpiLite: &piPb.UpiLite{
			PiRefId: "pi-3",
			Lrn:     "lrn-11",
		}},
		State:                piPb.PaymentInstrumentState_CREATED,
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  false,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
	}

	upiLitePiFromFixture = &piPb.PaymentInstrument{
		Id:   "pi-23",
		Type: piPb.PaymentInstrumentType_UPI_LITE,
		Identifier: &piPb.PaymentInstrument_UpiLite{UpiLite: &piPb.UpiLite{
			PiRefId: "pi-3",
			Lrn:     "lrn-1",
		}},
		State:                piPb.PaymentInstrumentState_CREATED,
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  false,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
	}

	upiLitePiNotInDb = &piPb.PaymentInstrument{
		Id:   "pi-24",
		Type: piPb.PaymentInstrumentType_UPI_LITE,
		Identifier: &piPb.PaymentInstrument_UpiLite{UpiLite: &piPb.UpiLite{
			PiRefId: "pi-35",
			Lrn:     "lrn-12",
		}},
		State:                piPb.PaymentInstrumentState_CREATED,
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  false,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
	}

	creditCardPi = &piPb.PaymentInstrument{
		Id:   "pi-5",
		Type: piPb.PaymentInstrumentType_CREDIT_CARD,
		Identifier: &piPb.PaymentInstrument_CreditCard{
			CreditCard: &piPb.CreditCard{
				Id: "credit-card-test-id",
			},
		},
		VerifiedName: "credit-card-random-id",
		State:        piPb.PaymentInstrumentState_CREATED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
		Ownership:            piPb.Ownership_EPIFI_TECH,
	}

	fixturePiEpifiTech = &piPb.PaymentInstrument{
		Id:   "pi-bank-epifi-tech",
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.PaymentInstrument_Account{
			Account: &piPb.Account{
				ActualAccountNumber: "***************",
				SecureAccountNumber: "xxxxxxxxxxx2937",
				IfscCode:            "FDRL0001001",
				AccountType:         accountsPb.Type_SAVINGS,
			},
		},
		VerifiedName: "Ashutosh",
		State:        piPb.PaymentInstrumentState_CREATED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
		Ownership:            piPb.Ownership_EPIFI_WEALTH,
	}

	piAffectedTestTables    = []string{"payment_instruments"}
	piAffectedTestTablesMap = map[commontypes.Ownership]map[commontypes.UseCase][]string{
		commontypes.Ownership_EPIFI_TECH: {
			commontypes.UseCase_USE_CASE_PAYMENT_INSTRUMENT: piAffectedTestTables,
		}}
)

// isPiDeepEqual compares 2 values of type *piPb.PaymentInstrument without the standard timestamp fields
// nolint: protogetter
func isPiDeepEqual(actual, expected *piPb.PaymentInstrument) bool {
	if actual != nil && expected != nil {
		expected.CreatedAt = actual.GetCreatedAt()
		expected.UpdatedAt = actual.GetUpdatedAt()
		expected.DeletedAt = actual.GetDeletedAt()
	}
	return proto.Equal(actual, expected)
}

func assertPI(t *testing.T, want, got *piPb.PaymentInstrument) bool {
	opts := []cmp.Option{
		protocmp.Transform(),
		protocmp.IgnoreFields(&piPb.PaymentInstrument{}, "created_at", "updated_at", "deleted_at"),
		protocmp.IgnoreFields(&piPb.Upi_MerchantDetails{}, "mcc", "merchant_genre", "merchant_id", "merchant_store_id", "merchant_terminal_id"),
	}

	if diff := cmp.Diff(want, got, opts...); diff != "" {
		t.Errorf("mismatch (-want +got):\n%s", diff)
		return false
	}
	return true
}

func getPisFromDB(piIds []string, db *gormV2.DB) []*model.PaymentInstrument {
	var gotDataModel []*model.PaymentInstrument
	db.Unscoped().Table("payment_instruments").Where("id in (?)", piIds).Scan(&gotDataModel)
	return gotDataModel
}

func TestPiDaoPgdb_Create(t *testing.T) {
	testPi := &piPb.PaymentInstrument{
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.PaymentInstrument_Account{
			Account: &piPb.Account{
				ActualAccountNumber: "***************",
				SecureAccountNumber: "xxxxxxxxxxx2938",
				IfscCode:            "FDRL0001001",
				AccountType:         accountsPb.Type_SAVINGS,
			},
		},
		VerifiedName: "Kunal",
		State:        piPb.PaymentInstrumentState_CREATED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
		LastVerifiedAt: &timestamppb.Timestamp{
			Seconds: 100000,
		},
	}
	testPiClone := func() *piPb.PaymentInstrument {
		return proto.Clone(testPi).(*piPb.PaymentInstrument)
	}

	testPiWithApo := &piPb.PaymentInstrument{
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.PaymentInstrument_Account{
			Account: &piPb.Account{
				ActualAccountNumber: "***************",
				SecureAccountNumber: "xxxxxxxxxxx2938",
				IfscCode:            "FDRL0001001",
				AccountType:         accountsPb.Type_SAVINGS,
				Apo:                 account.AccountProductOffering_APO_NRE,
			},
		},
		VerifiedName: "Kunal",
		State:        piPb.PaymentInstrumentState_CREATED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
		LastVerifiedAt: &timestamppb.Timestamp{
			Seconds: 100000,
		},
	}
	testPiWithApoClone := func() *piPb.PaymentInstrument {
		return proto.Clone(testPiWithApo).(*piPb.PaymentInstrument)
	}

	type args struct {
		ctx context.Context
		pi  *piPb.PaymentInstrument
	}
	tests := []struct {
		name    string
		args    args
		want    *piPb.PaymentInstrument
		wantErr bool
	}{
		{
			name: "Should successfully create a PI of type BANK_ACCOUNT",
			args: args{
				ctx: context.Background(),
				pi:  testPiClone(),
			},
			want: &piPb.PaymentInstrument{
				Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
				Identifier: &piPb.PaymentInstrument_Account{
					Account: &piPb.Account{
						ActualAccountNumber: "***************",
						SecureAccountNumber: "xxxxxxxxxxx2938",
						IfscCode:            "FDRL0001001",
						AccountType:         accountsPb.Type_SAVINGS,
						Apo:                 account.AccountProductOffering_APO_REGULAR,
					},
				},
				VerifiedName: "Kunal",
				State:        piPb.PaymentInstrumentState_CREATED,
				Capabilities: map[string]bool{
					piPb.Capability_INBOUND_TXN.String():  true,
					piPb.Capability_OUTBOUND_TXN.String(): true,
				},
				IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
				LastVerifiedAt: &timestamppb.Timestamp{
					Seconds: 100000,
				},
			},
			wantErr: false,
		},
		{
			name: "Should successfully create a PI of type BANK_ACCOUNT (with explicit Account Product Offering)",
			args: args{
				ctx: context.Background(),
				pi:  testPiWithApoClone(),
			},
			want: &piPb.PaymentInstrument{
				Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
				Identifier: &piPb.PaymentInstrument_Account{
					Account: &piPb.Account{
						ActualAccountNumber: "***************",
						SecureAccountNumber: "xxxxxxxxxxx2938",
						IfscCode:            "FDRL0001001",
						AccountType:         accountsPb.Type_SAVINGS,
						Apo:                 account.AccountProductOffering_APO_NRE,
					},
				},
				VerifiedName: "Kunal",
				State:        piPb.PaymentInstrumentState_CREATED,
				Capabilities: map[string]bool{
					piPb.Capability_INBOUND_TXN.String():  true,
					piPb.Capability_OUTBOUND_TXN.String(): true,
				},
				IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
				LastVerifiedAt: &timestamppb.Timestamp{
					Seconds: 100000,
				},
			},
			wantErr: false,
		},
		{
			name: "Should fail to create a PI of type BANK_ACCOUNT due to duplication",
			args: args{
				ctx: context.Background(),
				pi:  fixturePi1Clone(),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Should successfully create a PI of type BANK_ACCOUNT with epifi_wealth ownership",
			args: args{
				ctx: context.Background(),
				pi:  fixturePi2Wealth,
			},
			want:    fixturePi2Wealth,
			wantErr: false,
		},
		{
			name: "should successfully create PI of type CREDIT_CARD",
			args: args{
				ctx: context.Background(),
				pi:  creditCardPi,
			},
			want: creditCardPi,
		},
		{
			name: "should successfully create PI of type UPI_LITE",
			args: args{
				ctx: context.Background(),
				pi:  upiLitePi,
			},
			want: upiLitePi,
		},
		// TODO(kunal): Add test cases for other types of PI
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testSuite, testSuiteFunc := getPiTestSuite(t)
			defer testSuiteFunc()
			got, err := testSuite.piDao.Create(tt.args.ctx, tt.args.pi)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// explicitly set id as it is generated by dao
			if got != nil {
				tt.want.Id = got.Id
			}
			if !isPiDeepEqual(got, tt.want) {
				t.Errorf("Create() got = %v, want %v", got, tt.want)
			}
		})
	}
	t.Run("creation and query from ll db", func(t *testing.T) {
		testSuite, testSuiteFunc := getPiTestSuite(t)
		defer testSuiteFunc()
		paymentInstrument01 := &piPb.PaymentInstrument{
			Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
			Identifier: &piPb.PaymentInstrument_Account{
				Account: &piPb.Account{
					ActualAccountNumber: "***************",
					SecureAccountNumber: "xxxxxxxxxxx2938",
					IfscCode:            "FDRL0001001",
					AccountType:         accountsPb.Type_SAVINGS,
				},
			},
			VerifiedName: "Ashutosh",
			State:        piPb.PaymentInstrumentState_CREATED,
			Capabilities: map[string]bool{
				piPb.Capability_INBOUND_TXN.String():  true,
				piPb.Capability_OUTBOUND_TXN.String(): true,
			},
		}
		createdPi, err := testSuite.piDao.Create(ctxWithLlOwnership, paymentInstrument01)
		require.NoError(t, err)
		// querying with context.Background should also be able to give data from ll db
		got, err := testSuite.piDao.GetById(context.Background(), createdPi.GetId())
		require.NoError(t, err)
		assertPI(t, createdPi, got)
	})
	t.Run("creation from multiple ownership and query", func(t *testing.T) {
		testSuite, testSuiteFunc := getPiTestSuite(t)
		defer testSuiteFunc()
		paymentInstrument01 := &piPb.PaymentInstrument{
			Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
			Identifier: &piPb.PaymentInstrument_Account{
				Account: &piPb.Account{
					ActualAccountNumber: "***************",
					SecureAccountNumber: "xxxxxxxxxxx2938",
					IfscCode:            "FDRL0001001",
					AccountType:         accountsPb.Type_SAVINGS,
				},
			},
			VerifiedName: "Ashutosh",
			State:        piPb.PaymentInstrumentState_CREATED,
			Capabilities: map[string]bool{
				piPb.Capability_INBOUND_TXN.String():  true,
				piPb.Capability_OUTBOUND_TXN.String(): true,
			},
		}
		paymentInstrument02 := &piPb.PaymentInstrument{
			Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
			Identifier: &piPb.PaymentInstrument_Account{
				Account: &piPb.Account{
					ActualAccountNumber: "***************",
					SecureAccountNumber: "xxxxxxxxxxx2937",
					IfscCode:            "FDRL0001001",
					AccountType:         accountsPb.Type_SAVINGS,
				},
			},
			VerifiedName: "Ashutosh 2",
			State:        piPb.PaymentInstrumentState_CREATED,
			Capabilities: map[string]bool{
				piPb.Capability_INBOUND_TXN.String():  true,
				piPb.Capability_OUTBOUND_TXN.String(): true,
			},
		}
		createdPi, err := testSuite.piDao.Create(ctxWithLlOwnership, paymentInstrument01)
		require.NoError(t, err)
		createdPi2, err := testSuite.piDao.Create(context.Background(), paymentInstrument02)
		require.NoError(t, err)
		expected := []*piPb.PaymentInstrument{createdPi, createdPi2}
		// querying with context.Background should also be able to give data from ll db and epifi tech db
		got, err := testSuite.piDao.GetByIds(context.Background(), []string{createdPi.GetId(), createdPi2.GetId()})
		require.NoError(t, err)
		assert.Equal(t, len(expected), len(got))
		idToPi := make(map[string]*piPb.PaymentInstrument)
		for _, pi := range got {
			idToPi[pi.GetId()] = pi
		}
		for _, exp := range expected {
			assertPI(t, exp, idToPi[exp.GetId()])
		}
	})
}

func TestPiDaoPgdb_GetById(t *testing.T) {

	testSuite, testSuiteFunc := getPiTestSuite(t)
	defer testSuiteFunc()

	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		args    args
		want    *piPb.PaymentInstrument
		wantErr bool
	}{
		{
			name: "Should successfully fetch PI of type BANK_ACCOUNT",
			args: args{
				ctx: context.Background(),
				id:  fixturePi1.GetId(),
			},
			want:    fixturePi1,
			wantErr: false,
		},
		{
			name: "Should fail to fetch PI",
			args: args{
				ctx: context.Background(),
				id:  "random-id",
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := testSuite.piDao.GetById(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isPiDeepEqual(got, tt.want) {
				t.Errorf("GetById() got = %v, want %v, diff %s", got, tt.want, cmp.Diff(got, tt.want, protocmp.Transform()))
			}
		})
	}
}

func TestPiDaoPgdb_GetByIds(t *testing.T) {
	testSuite, testSuiteFunc := getPiTestSuite(t)
	defer testSuiteFunc()
	type args struct {
		ctx context.Context
		ids []string
	}
	tests := []struct {
		name    string
		args    args
		want    []*piPb.PaymentInstrument
		wantErr bool
	}{
		{
			name: "Should successfully fetch all the PIs",
			args: args{
				ctx: context.Background(),
				ids: []string{fixturePi1.GetId()},
			},
			want:    []*piPb.PaymentInstrument{fixturePi1},
			wantErr: false,
		},
		{
			name: "Should successfully fetch all the PIs and ignore the not found ones",
			args: args{
				ctx: context.Background(),
				ids: []string{fixturePi1.GetId(), "random-pi-id"},
			},
			want:    []*piPb.PaymentInstrument{fixturePi1},
			wantErr: false,
		},
		{
			name: "Return empty list if none of the PI Id is found",
			args: args{
				ctx: context.Background(),
				ids: []string{"random-pi-id-1", "random-pi-id-2"},
			},
			want:    []*piPb.PaymentInstrument{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := testSuite.piDao.GetByIds(tt.args.ctx, tt.args.ids)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if len(tt.want) != len(got) {
				t.Errorf("GetByIds() got %v, want %v", got, tt.want)
			}
			for i := range tt.want {
				if !isPiDeepEqual(got[i], tt.want[i]) {
					t.Errorf("GetByIds() index = %d, got %v, want %v, diff %s", i, got, tt.want, cmp.Diff(got[i], tt.want[i], protocmp.Transform()))
				}
			}
		})
	}
}

func TestPiDaoPgdb_GetBankAccountPI(t *testing.T) {
	testSuite, testSuiteFunc := getPiTestSuite(t)
	defer testSuiteFunc()
	type args struct {
		ctx           context.Context
		accountNumber string
		ifscCode      string
	}
	tests := []struct {
		name    string
		args    args
		want    *piPb.PaymentInstrument
		wantErr bool
	}{
		{
			name: "Should successfully fetch PI of type BANK_ACCOUNT",
			args: args{
				ctx:           context.Background(),
				accountNumber: fixturePi1.GetAccount().ActualAccountNumber,
				ifscCode:      fixturePi1.GetAccount().IfscCode,
			},
			want:    fixturePi1,
			wantErr: false,
		},
		{
			name: "Should fail to fetch PI due to not found error",
			args: args{
				ctx:           context.Background(),
				accountNumber: fixturePi1.GetAccount().ActualAccountNumber,
				ifscCode:      "random-ifsc-code",
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := testSuite.piDao.GetBankAccountPI(tt.args.ctx, tt.args.accountNumber, tt.args.ifscCode)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBankAccountPI() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isPiDeepEqual(got, tt.want) {
				t.Errorf("GetBankAccountPI() got = %v, want %v, diff %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestPiDaoPgdb_UpdatePi(t *testing.T) {
	testSuite, testSuiteFunc := getPiTestSuite(t)
	defer testSuiteFunc()

	type args struct {
		pi        *piPb.PaymentInstrument
		fieldMask []piPb.PaymentInstrumentFieldMask
	}
	test := []struct {
		name      string
		args      args
		updatedPi *piPb.PaymentInstrument
		wantErr   bool
	}{
		{
			name: "Should update Pi successfully",
			args: args{
				pi: updateFixturePi3,
				fieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_IFSC_CODE_UPI,
					piPb.PaymentInstrumentFieldMask_ACCOUNT_REFERENCE_NUMBER_UPI, piPb.PaymentInstrumentFieldMask_LAST_VERIFIED_AT},
			},
			updatedPi: updateFixturePi3,
			wantErr:   false,
		},
		{
			name: "Should update only the values present in mask",
			args: args{
				pi: &piPb.PaymentInstrument{
					Id:   "pi-1",
					Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.PaymentInstrument_Account{
						Account: &piPb.Account{
							ActualAccountNumber: "***************",
							SecureAccountNumber: "xxxxxxxxxxx2939",
							// changes the ifs code
							IfscCode:    "FED00021",
							AccountType: accountsPb.Type_SAVINGS,
						},
					},
					VerifiedName: "Kunal",
					State:        piPb.PaymentInstrumentState_SUSPENDED,
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  true,
						piPb.Capability_OUTBOUND_TXN.String(): true,
					},
				},
				fieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_STATE},
			},
			updatedPi: &piPb.PaymentInstrument{
				Id:   "pi-1",
				Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
				Identifier: &piPb.PaymentInstrument_Account{
					Account: &piPb.Account{
						ActualAccountNumber: "***************",
						SecureAccountNumber: "xxxxxxxxxxx2939",
						IfscCode:            "FDRL0001001",
						AccountType:         accountsPb.Type_SAVINGS,
					},
				},
				VerifiedName: "Kunal",
				State:        piPb.PaymentInstrumentState_SUSPENDED,
				Capabilities: map[string]bool{
					piPb.Capability_INBOUND_TXN.String():  true,
					piPb.Capability_OUTBOUND_TXN.String(): true,
				},
				IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
			},
			wantErr: false,
		},
		{
			name: "Should update only specific values from identifier",
			args: args{
				pi: &piPb.PaymentInstrument{
					Id:   "pi-3",
					Type: piPb.PaymentInstrumentType_UPI,
					Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
						Vpa:                    "test-nitesh@fede",
						AccountReferenceNumber: "testAccountRefNumber",
						IfscCode:               "test-code",
						MerchantDetails: &piPb.Upi_MerchantDetails{
							Mcc:                "mcc",
							MerchantId:         "merchant-id",
							MerchantStoreId:    "store-id",
							MerchantTerminalId: "terminal-id",
							MerchantGenre:      "genre",
						},
						AccountType: accountsPb.Type_SAVINGS,
						Apo:         account.AccountProductOffering_APO_NRE,
					}},
					VerifiedName: "nitesh",
					State:        piPb.PaymentInstrumentState_CREATED,
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  true,
						piPb.Capability_OUTBOUND_TXN.String(): true,
					},
					LastVerifiedAt: &timestamppb.Timestamp{Seconds: 100000},
				},
				fieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_ACCOUNT_REFERENCE_NUMBER_UPI,
					piPb.PaymentInstrumentFieldMask_IFSC_CODE_UPI,
					piPb.PaymentInstrumentFieldMask_MERCHANT_DETAILS_UPI, piPb.PaymentInstrumentFieldMask_LAST_VERIFIED_AT,
					piPb.PaymentInstrumentFieldMask_ACCOUNT_TYPE_UPI, piPb.PaymentInstrumentFieldMask_APO_UPI,
				},
			},
			updatedPi: &piPb.PaymentInstrument{
				Id:   "pi-3",
				Type: piPb.PaymentInstrumentType_UPI,
				Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
					Vpa:                    "nitesh@fede",
					AccountReferenceNumber: "testAccountRefNumber",
					AccountType:            accountsPb.Type_SAVINGS,
					Apo:                    account.AccountProductOffering_APO_NRE,
					IfscCode:               "test-code",
					MerchantDetails: &piPb.Upi_MerchantDetails{
						Mcc:                "mcc",
						MerchantId:         "merchant-id",
						MerchantStoreId:    "store-id",
						MerchantTerminalId: "terminal-id",
						MerchantGenre:      "genre",
					},
				}},
				VerifiedName: "nitesh",
				State:        piPb.PaymentInstrumentState_CREATED,
				Capabilities: map[string]bool{
					piPb.Capability_INBOUND_TXN.String():  true,
					piPb.Capability_OUTBOUND_TXN.String(): true,
				},
				IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
				LastVerifiedAt:       &timestamppb.Timestamp{Seconds: 100000},
			},
			wantErr: false,
		},
		{
			name: "Should fail to update IFSC code for pi of type bank account",
			args: args{
				pi: &piPb.PaymentInstrument{
					Id:   "pi-1",
					Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.PaymentInstrument_Account{
						Account: &piPb.Account{
							ActualAccountNumber: "***************",
							SecureAccountNumber: "xxxxxxxxxxx2939",
							// changes the ifs code
							IfscCode:    "FED00021",
							AccountType: accountsPb.Type_SAVINGS,
						},
					},
					VerifiedName: "Kunal",
					State:        piPb.PaymentInstrumentState_SUSPENDED,
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  true,
						piPb.Capability_OUTBOUND_TXN.String(): true,
					},
				},
				fieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_IFSC_CODE_UPI},
			},
			wantErr: true,
		},
		{
			name: "Should update pi ownership and last verified at",
			args: args{
				pi: &piPb.PaymentInstrument{
					Id:   "pi-7",
					Type: piPb.PaymentInstrumentType_UPI,
					Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
						Vpa: "random-7@fede",
					}},
					Ownership:      piPb.Ownership_EPIFI_WEALTH,
					LastVerifiedAt: &timestamppb.Timestamp{Seconds: 200000},
				},
				fieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_OWNERSHIP, piPb.PaymentInstrumentFieldMask_LAST_VERIFIED_AT},
			},
			updatedPi: &piPb.PaymentInstrument{
				Id:   "pi-7",
				Type: piPb.PaymentInstrumentType_UPI,
				Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
					Vpa: "random-7@fede",
				}},
				VerifiedName: "random-7",
				State:        piPb.PaymentInstrumentState_CREATED,
				Capabilities: map[string]bool{
					piPb.Capability_INBOUND_TXN.String():  true,
					piPb.Capability_OUTBOUND_TXN.String(): true,
				},
				IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
				Ownership:            piPb.Ownership_EPIFI_WEALTH,
				LastVerifiedAt:       &timestamppb.Timestamp{Seconds: 200000},
			},
			wantErr: false,
		},
		{
			name: "Should update pi merchant details",
			args: args{
				pi: &piPb.PaymentInstrument{
					Id: "pi-6",
					Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
						Vpa: "random-6@fede",
						MerchantDetails: &piPb.Upi_MerchantDetails{
							MerchantId:         "merchant-id",
							MerchantStoreId:    "store-id",
							MerchantTerminalId: "terminal-id",
							MerchantGenre:      "genre",
							MerchantType:       "merchant-type",
							BrandName:          "brand",
							LegalName:          "legal-name",
							OwnershipType:      "ownership",
							OnboardingType:     "onboarding",
							SubCode:            "32",
							FranchiseName:      "epifi",
						},
					}},
					VerifiedName: "random-6",
				},
				fieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_MERCHANT_DETAILS_UPI},
			},
			updatedPi: &piPb.PaymentInstrument{
				Id:   "pi-6",
				Type: piPb.PaymentInstrumentType_UPI,
				Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
					Vpa: "random-6@fede",
					MerchantDetails: &piPb.Upi_MerchantDetails{
						Mcc:                "random-mcc",
						MerchantId:         "merchant-id",
						MerchantStoreId:    "store-id",
						MerchantTerminalId: "terminal-id",
						MerchantGenre:      "genre",
						MerchantType:       "merchant-type",
						BrandName:          "brand",
						LegalName:          "legal-name",
						OwnershipType:      "ownership",
						OnboardingType:     "onboarding",
						SubCode:            "32",
						FranchiseName:      "epifi",
					},
				}},
				VerifiedName: "random-6",
				State:        piPb.PaymentInstrumentState_CREATED,
				Capabilities: map[string]bool{
					piPb.Capability_INBOUND_TXN.String():  true,
					piPb.Capability_OUTBOUND_TXN.String(): true,
				},
				IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
			},
			wantErr: false,
		},
		{
			name: "merchant details are nil , so should update only the last verified at field",
			args: args{
				pi: &piPb.PaymentInstrument{
					Id:             "pi-8",
					LastVerifiedAt: &timestamppb.Timestamp{Seconds: 200000},
				},
				fieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_MERCHANT_DETAILS_UPI, piPb.PaymentInstrumentFieldMask_LAST_VERIFIED_AT},
			},
			updatedPi: &piPb.PaymentInstrument{
				Id:   "pi-8",
				Type: piPb.PaymentInstrumentType_UPI,
				Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
					Vpa: "random-8@fede",
					MerchantDetails: &piPb.Upi_MerchantDetails{
						Mcc: "random-mcc",
					},
				}},
				VerifiedName: "random-8",
				State:        piPb.PaymentInstrumentState_CREATED,
				Capabilities: map[string]bool{
					piPb.Capability_INBOUND_TXN.String():  true,
					piPb.Capability_OUTBOUND_TXN.String(): true,
				},
				IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
				LastVerifiedAt:       &timestamppb.Timestamp{Seconds: 200000},
			},
			wantErr: false,
		},
		{
			name: "Should update Pi capability successfully",
			args: args{
				pi: updateFixturePi3Capability,
				fieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_IFSC_CODE_UPI,
					piPb.PaymentInstrumentFieldMask_ACCOUNT_REFERENCE_NUMBER_UPI,
					piPb.PaymentInstrumentFieldMask_LAST_VERIFIED_AT, piPb.PaymentInstrumentFieldMask_CAPABILITY_INBOUND_TXN},
			},
			updatedPi: updatedFixturePi3Capability,
			wantErr:   false,
		},
		{
			name: "Update only APO and account type",
			args: args{
				pi: &piPb.PaymentInstrument{
					Id:   "apo-pi-1",
					Type: piPb.PaymentInstrumentType_UPI,
					Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
						Vpa: "random-apo@fede",
						MerchantDetails: &piPb.Upi_MerchantDetails{
							Mcc: "random-mcc",
						},
						AccountType: accountsPb.Type_SAVINGS,
						Apo:         account.AccountProductOffering_APO_NRE,
					}},
				},
				fieldMask: []piPb.PaymentInstrumentFieldMask{
					piPb.PaymentInstrumentFieldMask_APO_UPI,
					piPb.PaymentInstrumentFieldMask_ACCOUNT_TYPE_UPI,
				},
			},
			updatedPi: &piPb.PaymentInstrument{
				Id:   "apo-pi-1",
				Type: piPb.PaymentInstrumentType_UPI,
				Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
					Vpa: "random-apo@fede",
					MerchantDetails: &piPb.Upi_MerchantDetails{
						Mcc: "random-mcc",
					},
					AccountType: accountsPb.Type_SAVINGS,
					Apo:         account.AccountProductOffering_APO_NRE,
				}},
				VerifiedName: "random-6",
				State:        piPb.PaymentInstrumentState_CREATED,
				Capabilities: map[string]bool{
					piPb.Capability_INBOUND_TXN.String():  true,
					piPb.Capability_OUTBOUND_TXN.String(): true,
				},
				IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
			},
			wantErr: false,
		},
		{
			name: "Should update Pi successfully for stock guardian ownership",
			args: args{
				pi: &piPb.PaymentInstrument{
					Id:           "PI5nrkf1HyC2ND250206_MbZRPgHhafW",
					VerifiedName: "Ashutosh",
					State:        piPb.PaymentInstrumentState_VERIFIED,
				},
				fieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_STATE,
					piPb.PaymentInstrumentFieldMask_VERIFIED_NAME},
			},
			updatedPi: &piPb.PaymentInstrument{
				Id:   "PI5nrkf1HyC2ND250206_MbZRPgHhafW",
				Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
				Identifier: &piPb.PaymentInstrument_Account{
					Account: &piPb.Account{
						ActualAccountNumber: "**************",
						SecureAccountNumber: "xxxxxxxxxx6732",
						IfscCode:            "FDRL000001",
						AccountType:         accountsPb.Type_CURRENT,
						Name:                "EPIFI TECHNOLOGIES PRIVATE LIMITED",
					},
				},
				VerifiedName: "Ashutosh",
				State:        piPb.PaymentInstrumentState_VERIFIED,
				Capabilities: map[string]bool{
					piPb.Capability_INBOUND_TXN.String():  false,
					piPb.Capability_OUTBOUND_TXN.String(): false,
				},
				IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
				Ownership:            piPb.Ownership_OWNERSHIP_STOCK_GUARDIAN_TSP,
			},
			wantErr: false,
		},
		{
			name: "Should successfully update the capability of a PI in stock guardian ownership",
			args: args{
				pi: &piPb.PaymentInstrument{
					Id: "PI6nrkf1HyC2ND250206_MbZRPgHhafW",
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String(): false,
					},
				},
				fieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_CAPABILITY_INBOUND_TXN},
			},
			updatedPi: &piPb.PaymentInstrument{
				Id:   "PI6nrkf1HyC2ND250206_MbZRPgHhafW",
				Type: piPb.PaymentInstrumentType_UPI,
				Identifier: &piPb.PaymentInstrument_Upi{
					Upi: &piPb.Upi{
						Vpa: "test@fede",
					},
				},
				VerifiedName: "test",
				State:        piPb.PaymentInstrumentState_VERIFIED,
				Capabilities: map[string]bool{
					piPb.Capability_INBOUND_TXN.String():  false,
					piPb.Capability_OUTBOUND_TXN.String(): true,
				},
				IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
				Ownership:            piPb.Ownership_OWNERSHIP_STOCK_GUARDIAN_TSP,
			},
			wantErr: false,
		},
		{
			name: "Should successfully update the account reference number of a PI in stock guardian ownership",
			args: args{
				pi: &piPb.PaymentInstrument{
					Id: "PI7nrkf1HyC2ND250206_MbZRPgHhafW",
					Identifier: &piPb.PaymentInstrument_Upi{
						Upi: &piPb.Upi{
							AccountReferenceNumber: "some-number",
						},
					},
				},
				fieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_ACCOUNT_REFERENCE_NUMBER_UPI},
			},
			updatedPi: &piPb.PaymentInstrument{
				Id:   "PI7nrkf1HyC2ND250206_MbZRPgHhafW",
				Type: piPb.PaymentInstrumentType_UPI,
				Identifier: &piPb.PaymentInstrument_Upi{
					Upi: &piPb.Upi{
						AccountReferenceNumber: "some-number",
						Vpa:                    "lettercase@fede",
						MerchantDetails:        &piPb.Upi_MerchantDetails{},
					},
				},
				VerifiedName: "lettercase",
				State:        piPb.PaymentInstrumentState_VERIFIED,
				Capabilities: map[string]bool{
					piPb.Capability_INBOUND_TXN.String():  true,
					piPb.Capability_OUTBOUND_TXN.String(): true,
				},
				IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
				Ownership:            piPb.Ownership_OWNERSHIP_STOCK_GUARDIAN_TSP,
			},
			wantErr: false,
		},
	}

	for _, tt := range test {
		t.Run(tt.name, func(t *testing.T) {
			err := testSuite.piDao.UpdatePi(context.Background(), tt.args.pi, tt.args.fieldMask, piPb.Source_SOURCE_UNSPECIFIED, "")
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdatePi() got err: %v, want: %v", err, tt.wantErr)
				return
			}
			if err != nil {
				return
			}
			pi, err := testSuite.piDao.GetById(context.Background(), tt.args.pi.GetId())
			if err != nil {
				t.Errorf("Error fetching pi for id: %s, err: %v", tt.args.pi.GetId(), err)
				return
			}
			assertPI(t, pi, tt.updatedPi)
		})

	}
}

func TestPiDaoPgdb_GetDebitCardPi(t *testing.T) {
	testSuite, testSuiteFunc := getPiTestSuite(t)
	defer testSuiteFunc()
	tests := []struct {
		name       string
		cardNumber string
		expiry     string
		embossName string
		want       *piPb.PaymentInstrument
		wantErr    bool
	}{
		{
			name:       "should successfully fetched pi for card",
			cardNumber: "FI_APP:PERPETUAL:649adef1-43e9-4573-9d83-91bfbde0c4b5",
			expiry:     "0221",
			embossName: "epifi user",
			want: &piPb.PaymentInstrument{
				Id:   "pi-4",
				Type: piPb.PaymentInstrumentType_DEBIT_CARD,
				Identifier: &piPb.PaymentInstrument_Card{Card: &piPb.Card{
					ActualCardNumber:    "6652********0722",
					Name:                "epifi user",
					Expiry:              "0221",
					CardReferenceNumber: "15b78d61-03e8-4553-92b0-307a266fcc2e",
					SecureCardNumber:    "FI_APP:PERPETUAL:649adef1-43e9-4573-9d83-91bfbde0c4b5",
				}},
				VerifiedName: "epifi user",
				State:        piPb.PaymentInstrumentState_CREATED,
				Capabilities: map[string]bool{
					piPb.Capability_INBOUND_TXN.String():  true,
					piPb.Capability_OUTBOUND_TXN.String(): true,
				},
				IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
			},
		},
		{
			name:       "should failed tp fetch pi",
			cardNumber: "FI_APP:PERPETUAL:649adef1-43e9-4573-9d83-91bfbde0c4b5",
			expiry:     "",
			embossName: "",
			want:       nil,
			wantErr:    true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := testSuite.piDao.GetDebitCardPi(context.Background(), tt.cardNumber, tt.expiry, tt.embossName)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDebitCardPi() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isPiDeepEqual(got, tt.want) {
				t.Errorf("GetDebitCardPi() got = %v, want %v, diff %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestPiDaoPgdb_GetPartialBankAccountPI(t *testing.T) {
	testSuite, testSuiteFunc := getPiTestSuite(t)
	defer testSuiteFunc()
	tests := []struct {
		name          string
		accountNumber string
		userName      string
		ifscCode      string
		want          *piPb.PaymentInstrument
		wantErr       bool
	}{
		{
			name:          "got partial account pis successfully",
			accountNumber: "2342940",
			userName:      "partial",
			ifscCode:      "FDRL0001001",
			want: &piPb.PaymentInstrument{
				Id:   "pi-partial-1",
				Type: piPb.PaymentInstrumentType_PARTIAL_BANK_ACCOUNT,
				Identifier: &piPb.PaymentInstrument_Account{
					Account: &piPb.Account{
						ActualAccountNumber: "2342940",
						SecureAccountNumber: "xxxxxxxxxxx2940",
						IfscCode:            "FDRL0001001",
						AccountType:         accountsPb.Type_SAVINGS,
						Name:                "partial",
					},
				},
				State:                piPb.PaymentInstrumentState_CREATED,
				Capabilities:         map[string]bool{"INBOUND_TXN": false, "OUTBOUND_TXN": false},
				IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
			},
		},
		{
			name:          "record not found",
			accountNumber: "2342940",
			userName:      "random name",
			ifscCode:      "FDRL0001001",
			wantErr:       true,
		},
		{
			name:          "error due to empty name",
			accountNumber: "2342940",
			userName:      "",
			ifscCode:      "FDRL0001001",
			wantErr:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := testSuite.piDao.GetPartialBankAccountPI(context.Background(), tt.accountNumber, tt.ifscCode, tt.userName)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPartialBankAccountPI() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isPiDeepEqual(got, tt.want) {
				t.Errorf("GetPartialBankAccountPI() got = %v, want %v, diff %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestPiDaoPgdb_GetPartialUpiPI(t *testing.T) {
	testSuite, testSuiteFunc := getPiTestSuite(t)
	defer testSuiteFunc()
	tests := []struct {
		name       string
		partialVpa string
		userName   string
		want       *piPb.PaymentInstrument
		wantErr    bool
	}{
		{
			name:       "got partial account pis successfully",
			partialVpa: "test@fe",
			userName:   "partial",
			want: &piPb.PaymentInstrument{
				Id:   "pi-partial-2",
				Type: piPb.PaymentInstrumentType_PARTIAL_UPI,
				Identifier: &piPb.PaymentInstrument_Upi{
					Upi: &piPb.Upi{
						Vpa:  "test@fe",
						Name: "partial",
					},
				},
				VerifiedName:         "test",
				State:                piPb.PaymentInstrumentState_CREATED,
				Capabilities:         map[string]bool{"INBOUND_TXN": false, "OUTBOUND_TXN": false},
				IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
			},
		},
		{
			name:       "record not found",
			partialVpa: "2342940",
			userName:   "random name",
			wantErr:    true,
		},
		{
			name:       "error due to empty vpa",
			partialVpa: "",
			userName:   "random name",
			wantErr:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := testSuite.piDao.GetPartialUpiPI(context.Background(), tt.partialVpa, tt.userName)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPartialUpiPI() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isPiDeepEqual(got, tt.want) {
				t.Errorf("GetPartialUpiPI() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPiDaoPgdb_GetGenericPi(t *testing.T) {
	testSuite, testSuiteFunc := getPiTestSuite(t)
	defer testSuiteFunc()
	tests := []struct {
		name          string
		accountNumber string
		userName      string
		ifscCode      string
		want          *piPb.PaymentInstrument
		wantErr       bool
	}{
		{
			name:          "got generic pi successfully",
			accountNumber: "********",
			userName:      "generic",
			ifscCode:      "FDRL0001001",
			want: &piPb.PaymentInstrument{
				Id:   "pi-generic-1",
				Type: piPb.PaymentInstrumentType_GENERIC,
				Identifier: &piPb.PaymentInstrument_Account{
					Account: &piPb.Account{
						ActualAccountNumber: "********",
						SecureAccountNumber: "xxxxxxxxxxx0000",
						IfscCode:            "FDRL0001001",
						AccountType:         accountsPb.Type_SAVINGS,
						Name:                "generic",
					},
				},
				State:                piPb.PaymentInstrumentState_CREATED,
				Capabilities:         map[string]bool{"INBOUND_TXN": false, "OUTBOUND_TXN": false},
				IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
			},
		},
		{
			name:          "record not found",
			accountNumber: "********",
			userName:      "random name",
			ifscCode:      "FDRL0001001",
			wantErr:       true,
		},
		{
			name:          "error due to empty account number",
			accountNumber: "",
			userName:      "random name",
			ifscCode:      "FDRL0001001",
			wantErr:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := testSuite.piDao.GetGenericPi(context.Background(), tt.accountNumber, tt.ifscCode, tt.userName)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGenericPi() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isPiDeepEqual(got, tt.want) {
				t.Errorf("GetGenericPi() got = %v, want %v, diff %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestPiDaoPgdb_DeleteWealthPisByIds(t *testing.T) {
	testSuite, testSuiteFunc := getPiTestSuite(t)
	defer testSuiteFunc()
	tests := []struct {
		name         string
		piIds        []string
		wantErr      bool
		undeletedPis []string
	}{
		{
			name: "delete pis successfully",
			piIds: []string{
				"pi-wealth-1",
				"pi-wealth-2",
			},
			undeletedPis: []string{},
		},
		{
			name: "delete on epifi wealth pis",
			piIds: []string{
				"pi-wealth-3",
				"pi-ex-3",
			},
			undeletedPis: []string{
				"pi-ex-3",
			},
		},
		{
			name: "pis does not exist",
			piIds: []string{
				"pi-random-1",
				"pi-random-2",
			},
		},
		{
			name:    "should return error as pi ids list is empty",
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			err := testSuite.piDao.DeleteWealthPisByIds(context.Background(), tt.piIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeleteWealthPisByIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			got := getPisFromDB(tt.piIds, testSuite.db)
			assert.Equal(t, len(got), len(tt.undeletedPis))
			for _, pi := range got {
				if !funk.Contains(tt.undeletedPis, pi.Id) {
					t.Errorf("Pi not deleted: %s", pi.Id)
					return
				}
			}
		})
	}
}

func TestPiDaoPgdb_GetByVpas(t *testing.T) {
	testSuite, testSuiteFunc := getPiTestSuite(t)
	defer testSuiteFunc()
	tests := []struct {
		name    string
		vpa     []string
		want    []*piPb.PaymentInstrument
		wantErr bool
	}{
		{
			name: "got pis for vpas successfully",
			vpa:  []string{"nitesh@fede"},
			want: []*piPb.PaymentInstrument{
				{
					Id:   "pi-3",
					Type: piPb.PaymentInstrumentType_UPI,
					Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
						Vpa: "nitesh@fede",
					}},
					VerifiedName:         "nitesh",
					State:                piPb.PaymentInstrumentState_CREATED,
					Capabilities:         map[string]bool{"INBOUND_TXN": true, "OUTBOUND_TXN": true},
					IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
				},
			},
		},
		{
			name: "record not found",
			vpa:  []string{"2342940"},
		},
		{
			name:    "error due to empty vpa",
			wantErr: true,
		},
		{
			name: "should fetch the valid one and ignore others",
			vpa:  []string{"xyz", "nitesh@fede"},
			want: []*piPb.PaymentInstrument{
				{
					Id:   "pi-3",
					Type: piPb.PaymentInstrumentType_UPI,
					Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
						Vpa: "nitesh@fede",
					}},
					VerifiedName:         "nitesh",
					State:                piPb.PaymentInstrumentState_CREATED,
					Capabilities:         map[string]bool{"INBOUND_TXN": true, "OUTBOUND_TXN": true},
					IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := testSuite.piDao.GetByVpas(context.Background(), tt.vpa)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByVpas() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			for i := range tt.want {
				if !isPiDeepEqual(got[i], tt.want[i]) {
					t.Errorf("GetByVpas() index = %d, got %v, want %v", i, got, tt.want)
				}
			}
		})
	}
	t.Run("creation from multiple ownership and query from VPAs", func(t *testing.T) {
		paymentInstrument01 := &piPb.PaymentInstrument{
			Type: piPb.PaymentInstrumentType_UPI,
			Identifier: &piPb.PaymentInstrument_Upi{
				Upi: &piPb.Upi{
					Vpa: "ashutosh-1@federal",
				},
			},
			VerifiedName: "Ashutosh",
			State:        piPb.PaymentInstrumentState_CREATED,
			Capabilities: map[string]bool{
				piPb.Capability_INBOUND_TXN.String():  true,
				piPb.Capability_OUTBOUND_TXN.String(): true,
			},
		}
		paymentInstrument02 := &piPb.PaymentInstrument{
			Type: piPb.PaymentInstrumentType_UPI,
			Identifier: &piPb.PaymentInstrument_Upi{
				Upi: &piPb.Upi{
					Vpa: "ashutosh-2@federal",
				},
			},
			VerifiedName: "Ashutosh",
			State:        piPb.PaymentInstrumentState_CREATED,
			Capabilities: map[string]bool{
				piPb.Capability_INBOUND_TXN.String():  true,
				piPb.Capability_OUTBOUND_TXN.String(): true,
			},
		}
		createdPi, err := testSuite.piDao.Create(context.Background(), paymentInstrument01)
		require.NoError(t, err)
		createdPi2, err := testSuite.piDao.Create(ctxWithLlOwnership, paymentInstrument02)
		require.NoError(t, err)
		expected := []*piPb.PaymentInstrument{createdPi, createdPi2}
		// querying with context.Background should also be able to give data from ll db and epifi tech db
		got, err := testSuite.piDao.GetByVpas(context.Background(), []string{"ashutosh-1@federal", "ashutosh-2@federal", "random-vpa-dne"})
		require.NoError(t, err)
		assert.Len(t, got, 2)
		idToPi := make(map[string]*piPb.PaymentInstrument)
		for _, pi := range got {
			idToPi[pi.GetId()] = pi
		}
		for _, exp := range expected {
			assertPI(t, exp, idToPi[exp.GetId()])
		}
	})
}

func TestPiDaoPgdb_GetUpiLitePI(t *testing.T) {
	testSuite, testSuiteFunc := getPiTestSuite(t)
	defer testSuiteFunc()
	tests := []struct {
		name    string
		lrn     string
		want    *piPb.PaymentInstrument
		wantErr bool
	}{
		{
			name: "got pi for lrn successfully",
			lrn:  upiLitePiFromFixture.GetUpiLite().GetLrn(),
			want: upiLitePiFromFixture,
		},
		{
			name:    "no pi found for lrn",
			lrn:     "lrn-33",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := testSuite.piDao.GetUpiLitePI(context.Background(), tt.lrn)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUpiLitePI() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !isPiDeepEqual(got, tt.want) {
				t.Errorf("GetUpiLitePI() got %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPiDaoPgdb_GetCreditCardPi(t *testing.T) {
	testSuite, testSuiteFunc := getPiTestSuite(t)
	defer testSuiteFunc()
	tests := []struct {
		name    string
		cardId  string
		want    *piPb.PaymentInstrument
		wantErr error
	}{
		{
			name:   "should successfully fetched pi for credit card",
			cardId: "random_credit_card_id",
			want: &piPb.PaymentInstrument{
				Id:   "credit_card_pi",
				Type: piPb.PaymentInstrumentType_CREDIT_CARD,
				Identifier: &piPb.PaymentInstrument_CreditCard{
					CreditCard: &piPb.CreditCard{
						Id: "random_credit_card_id",
					},
				},
				VerifiedName: "credit-card-user",
				State:        piPb.PaymentInstrumentState_CREATED,
				Capabilities: map[string]bool{
					piPb.Capability_INBOUND_TXN.String():  true,
					piPb.Capability_OUTBOUND_TXN.String(): true,
				},
				IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
			},
		},
		{
			name:    "should return record not found error",
			cardId:  "random-id",
			wantErr: epifierrors.ErrRecordNotFound,
		},
		{
			name:    "should return invalid argument error since id is empty",
			wantErr: epifierrors.ErrInvalidArgument,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := testSuite.piDao.GetCreditCardPi(context.Background(), tt.cardId)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetCredittCardPi() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isPiDeepEqual(got, tt.want) {
				t.Errorf("GetCreditCardPi() got = %v, want %v, diff %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}
