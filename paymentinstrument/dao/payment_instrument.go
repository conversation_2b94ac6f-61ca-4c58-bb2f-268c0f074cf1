// nolint: goimports
package dao

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/golang/protobuf/ptypes"
	"github.com/imdario/mergo"
	"github.com/jinzhu/copier"
	"github.com/lib/pq"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/storage/v2/usecase"

	pkgCmdTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/nulltypes"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/typesv2/account"
	"github.com/epifi/gamma/paymentinstrument/dao/model"
	"github.com/epifi/gamma/paymentinstrument/wire/types"
	"github.com/epifi/gamma/pkg/pay"
)

// PiDaoPgdb implements PiDao using pgdb
type PiDaoPgdb struct {
	db                            *gormv2.DB
	idGen                         idgen.IdGenerator
	txnExecutor                   storageV2.TxnExecutor
	attrIdGen                     idgen.AttributedIdGen[*PaymentInstrumentAttribute]
	dbResourceProvider            *usecase.DBResourceProvider[*gormv2.DB]
	dbTxnExecutorResourceProvider *usecase.DBResourceProvider[storageV2.IdempotentTxnExecutor]
	// true: db instance is going to be fetched based on ownership which will be fetched from context
	// false: db instance will be the default PI PGDB
	useDbResourceProvider types.EnableResourceProvider
}

// Ensure PiDaoPgdb implements PiDao at compile time
var _ PiDao = &PiDaoPgdb{}

const useCase = commontypes.UseCase_USE_CASE_PAYMENT_INSTRUMENT

const (
	identifierColumnName          = "identifier"
	ownershipColumnName           = "ownership"
	computedColumnFormatThreeKeys = "%s_%s_%s"
	computedColumnForTwoKeys      = "%s_%s"
	lastVerifiedAtColumnName      = "last_verified_at"
	capabilityColumnName          = "capabilities"
)

var (
	piColumnNameMap = map[piPb.PaymentInstrumentFieldMask]string{
		piPb.PaymentInstrumentFieldMask_IFSC_CODE_UPI:                identifierColumnName,
		piPb.PaymentInstrumentFieldMask_ACCOUNT_REFERENCE_NUMBER_UPI: identifierColumnName,
		piPb.PaymentInstrumentFieldMask_MERCHANT_DETAILS_UPI:         identifierColumnName,
		piPb.PaymentInstrumentFieldMask_STATE:                        "state",
		piPb.PaymentInstrumentFieldMask_VERIFIED_NAME:                "verified_name",
		piPb.PaymentInstrumentFieldMask_OWNERSHIP:                    ownershipColumnName,
		piPb.PaymentInstrumentFieldMask_LAST_VERIFIED_AT:             lastVerifiedAtColumnName,
		piPb.PaymentInstrumentFieldMask_CAPABILITY_INBOUND_TXN:       capabilityColumnName,
		piPb.PaymentInstrumentFieldMask_ACCOUNT_TYPE_UPI:             identifierColumnName,
		piPb.PaymentInstrumentFieldMask_APO_UPI:                      identifierColumnName,
	}
	piInvalidFieldFilter = func(field piPb.PaymentInstrumentFieldMask) bool {
		return piPb.PaymentInstrumentFieldMask_PAYMENT_INSTRUMENT_FIELD_MASK_UNSPECIFIED != field
	}
	identifierMaskList = []piPb.PaymentInstrumentFieldMask{
		piPb.PaymentInstrumentFieldMask_ACCOUNT_REFERENCE_NUMBER_UPI,
		piPb.PaymentInstrumentFieldMask_IFSC_CODE_UPI,
		piPb.PaymentInstrumentFieldMask_ACCOUNT_TYPE_UPI,
		piPb.PaymentInstrumentFieldMask_MERCHANT_DETAILS_UPI,
		piPb.PaymentInstrumentFieldMask_APO_UPI,
	}
)

// Factory method for creating an instance of PI dao.
// This method will be used by the injector when providing the dependencies at initialization time.
func NewPiDaoPgdb(db pkgCmdTypes.PaymentInstrumentPGDB, idGenerator idgen.IdGenerator, txnExecutor storageV2.TxnExecutor, attrIdGen idgen.AttributedIdGen[*PaymentInstrumentAttribute],
	dbResourceProvider *usecase.DBResourceProvider[*gormv2.DB], dbTxnExecutorResourceProvider *usecase.DBResourceProvider[storageV2.IdempotentTxnExecutor], useDbResourceProvider types.EnableResourceProvider) *PiDaoPgdb {
	return &PiDaoPgdb{db: db, idGen: idGenerator, txnExecutor: txnExecutor, attrIdGen: attrIdGen, dbResourceProvider: dbResourceProvider, dbTxnExecutorResourceProvider: dbTxnExecutorResourceProvider, useDbResourceProvider: useDbResourceProvider}
}

// Create creates an entry in pi table
//
//nolint:dupl
func (pd *PiDaoPgdb) Create(ctx context.Context, pi *piPb.PaymentInstrument) (*piPb.PaymentInstrument, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiDaoPgdb", "Create", time.Now())

	ctx = epificontext.WithOwnership(ctx, pay.PiOwnershipToCommonOwnership[pi.GetOwnership()])

	db, resolveErr := getConnFromContextOrProviderWithDefaultHandling(ctx, pd.dbResourceProvider, pd.db, pd.useDbResourceProvider)
	if resolveErr != nil {
		return nil, fmt.Errorf("error [%w] while resolving db connection", resolveErr)
	}

	piModel, err := convertToPiModel(pi, true)
	if err != nil {
		return nil, fmt.Errorf("failed to convert from proto to model: %w", err)
	}
	id, err := getGeneratedId(ctx, idgen.PaymentInstrument, pd.idGen, pd.attrIdGen, pd.useDbResourceProvider)
	if err != nil {
		return nil, fmt.Errorf("payment instrument id generation failed: %w", err)
	}

	piModel.Id = id
	if err = db.Create(piModel).Error; err != nil {
		return nil, fmt.Errorf("failed to insert PI in DB: %w", err)
	}
	return convertToPiProto(piModel)
}

// GetById fetches pi by id
// nolint: dupl
func (pd *PiDaoPgdb) GetById(ctx context.Context, id string) (*piPb.PaymentInstrument, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiDaoPgdb", "GetById", time.Now())
	var piModel model.PaymentInstrument
	ctx = getCtxWithOwnershipFromAttribute(ctx, idgen.PaymentInstrument, pd.attrIdGen, id)
	db, dbErr := getConnFromContextOrProviderWithDefaultHandling(ctx, pd.dbResourceProvider, pd.db, pd.useDbResourceProvider)
	if dbErr != nil {
		return nil, fmt.Errorf("failed to get db connection: %w", dbErr)
	}
	if err := db.Where("id = ?", id).First(&piModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			err = epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to get PI in DB, id: %s: %w", id, err)
	}

	return convertToPiProto(&piModel)
}

// GetByIds returns a list of PIs for a given list of PI ids
func (pd *PiDaoPgdb) GetByIds(ctx context.Context, ids []string) ([]*piPb.PaymentInstrument, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiDaoPgdb", "GetByIds", time.Now())
	ownershipToIdsMap := getOwnershipToIdListMap(ids, idgen.PaymentInstrument, pd.attrIdGen)

	entitiesChan := make(chan []*model.PaymentInstrument, len(ownershipToIdsMap))

	grp, grpCtx := errgroup.WithContext(ctx)
	for ownership, idList := range ownershipToIdsMap {
		grp.Go(func() error {
			pis, err := pd.getByIdsUtil(epificontext.WithOwnership(grpCtx, ownership), idList)
			switch {
			case errors.Is(err, epifierrors.ErrRecordNotFound):
				return nil
			case err != nil:
				return fmt.Errorf("error in fetching payment instruments for ownership %s : %w", ownership.String(), err)
			default:
				entitiesChan <- pis
				return nil
			}
		})
	}
	if err := grp.Wait(); err != nil {
		return nil, fmt.Errorf("error in fetching payment instruments: %w", err)
	}
	close(entitiesChan)

	modelPIs := make([]*model.PaymentInstrument, 0)
	for pis := range entitiesChan {
		modelPIs = append(modelPIs, pis...)
	}

	return convertToPiProtos(modelPIs)
}

func (pd *PiDaoPgdb) getByIdsUtil(ctx context.Context, ids []string) ([]*model.PaymentInstrument, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	db, resolveErr := getConnFromContextOrProviderWithDefaultHandling(ctx, pd.dbResourceProvider, pd.db, pd.useDbResourceProvider)
	if resolveErr != nil {
		return nil, fmt.Errorf("error [%w] while resolving db connection", resolveErr)
	}
	var (
		pis []*model.PaymentInstrument
		err error
	)
	// ANY is preferred over IN to reduce the number of fingerprints tracked in the database stats.
	// But ANY is not efficient in handling single element array, so using direct comparison
	switch len(ids) {
	case 0:
		return nil, nil
	case 1:
		err = db.Where("id = ?", ids[0]).Find(&pis).Error
	default:
		err = db.Where("id = ANY(?)", pq.Array(ids)).Find(&pis).Error
	}
	if err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			err = epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to get PI in DB, ids: %v: %w", ids, err)
	}
	return pis, nil
}

// GetBankAccountPI fetches PI of type BANK_ACCOUNT by accountNumber and IfscCode
func (pd *PiDaoPgdb) GetBankAccountPI(ctx context.Context, accountNumber, ifscCode string) (*piPb.PaymentInstrument, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiDaoPgdb", "GetBankAccountPI", time.Now())
	var piModel model.PaymentInstrument
	computedAccountValue := fmt.Sprintf(computedColumnForTwoKeys, accountNumber, ifscCode)
	db, resolveErr := getConnFromContextOrProviderWithDefaultHandling(ctx, pd.dbResourceProvider, pd.db, pd.useDbResourceProvider)
	if resolveErr != nil {
		return nil, fmt.Errorf("error [%w] while resolving db connection", resolveErr)
	}
	if err := db.Where("computed_unique_account = ?", computedAccountValue).First(&piModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			err = epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to get PI in DB, accountNumber: %s, ifscCode:%s,  %w", accountNumber, ifscCode, err)
	}

	return convertToPiProto(&piModel)
}

// GetPartialBankAccountPI fetches pi using account number, ifsc , name
// fetches the pi by computed column -> name_accountNumber_ifscCode
func (pd *PiDaoPgdb) GetPartialBankAccountPI(ctx context.Context, accountNumber, ifscCode, name string) (*piPb.PaymentInstrument, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiDaoPgdb", "GetPartialBankAccountPI", time.Now())
	var piModel model.PaymentInstrument

	if accountNumber == "" || ifscCode == "" || name == "" {
		return nil, epifierrors.ErrInvalidArgument
	}
	db, resolveErr := getConnFromContextOrProviderWithDefaultHandling(ctx, pd.dbResourceProvider, pd.db, pd.useDbResourceProvider)
	if resolveErr != nil {
		return nil, fmt.Errorf("error [%w] while resolving db connection", resolveErr)
	}
	computedAccountValue := fmt.Sprintf(computedColumnFormatThreeKeys, strings.ReplaceAll(name, " ", ""), accountNumber, ifscCode)
	if err := db.Where("computed_unique_account = ?", computedAccountValue).First(&piModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			err = epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to get partial PI in DB, accountNumber: %s, ifscCode: %s, name:%s,  %w", accountNumber, ifscCode, name, err)
	}
	return convertToPiProto(&piModel)
}

// GetPartialUpiPI fetches PI of type UPI by vpa and name
// fetches the PI by computed column -> name_vpa
func (pd *PiDaoPgdb) GetPartialUpiPI(ctx context.Context, vpa, name string) (*piPb.PaymentInstrument, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiDaoPgdb", "GetPartialUpiPI", time.Now())
	var piModel model.PaymentInstrument

	if vpa == "" || name == "" {
		return nil, epifierrors.ErrInvalidArgument
	}
	db, resolveErr := getConnFromContextOrProviderWithDefaultHandling(ctx, pd.dbResourceProvider, pd.db, pd.useDbResourceProvider)
	if resolveErr != nil {
		return nil, fmt.Errorf("error [%w] while resolving db connection", resolveErr)
	}
	computedUpiValue := fmt.Sprintf(computedColumnForTwoKeys, strings.ReplaceAll(name, " ", ""), vpa)
	if err := db.Where("computed_unique_upi = ?", computedUpiValue).First(&piModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			err = epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to get partial PI in DB, vpa: %s, name:%s,  %w", vpa, name, err)
	}
	return convertToPiProto(&piModel)
}

// GetGenericPi fetches PI of type Generic
// fetches the PI by computed_column -> accountNumber_ifscCode
func (pd *PiDaoPgdb) GetGenericPi(ctx context.Context, accountNumber, ifscCode, name string) (*piPb.PaymentInstrument, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiDaoPgdb", "GetGenericPi", time.Now())
	var piModel model.PaymentInstrument

	if accountNumber == "" || ifscCode == "" || name == "" {
		return nil, epifierrors.ErrInvalidArgument
	}

	computedAccountValue := fmt.Sprintf(computedColumnFormatThreeKeys, strings.ReplaceAll(name, " ", ""), accountNumber, ifscCode)
	db, resolveErr := getConnFromContextOrProviderWithDefaultHandling(ctx, pd.dbResourceProvider, pd.db, pd.useDbResourceProvider)
	if resolveErr != nil {
		return nil, fmt.Errorf("error [%w] while resolving db connection", resolveErr)
	}
	if err := db.Where("computed_unique_account = ?", computedAccountValue).First(&piModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			err = epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to get generic PI in DB, accountNumber: %s, ifscCode: %s, name:%s,  %w", accountNumber, ifscCode, name, err)
	}
	return convertToPiProto(&piModel)
}

// GetUpiPI fetches PI of type UPI by vpa
// nolint: dupl
func (pd *PiDaoPgdb) GetUpiPI(ctx context.Context, vpa string) (*piPb.PaymentInstrument, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiDaoPgdb", "GetUpiPI", time.Now())
	var piModel model.PaymentInstrument
	db, resolveErr := getConnFromContextOrProviderWithDefaultHandling(ctx, pd.dbResourceProvider, pd.db, pd.useDbResourceProvider)
	if resolveErr != nil {
		return nil, fmt.Errorf("error [%w] while resolving db connection", resolveErr)
	}
	if err := db.Where("computed_unique_upi = ?", vpa).First(&piModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			err = epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch PI of type UPI, vpa: %s, %w", vpa, err)
	}

	return convertToPiProto(&piModel)
}

// GetUpiLitePI fetches PI of type UPI by lrn
func (pd *PiDaoPgdb) GetUpiLitePI(ctx context.Context, lrn string) (*piPb.PaymentInstrument, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiDaoPgdb", "GetUpiLitePI", time.Now())
	var piModel model.PaymentInstrument
	db, resolveErr := getConnFromContextOrProviderWithDefaultHandling(ctx, pd.dbResourceProvider, pd.db, pd.useDbResourceProvider)
	if resolveErr != nil {
		return nil, fmt.Errorf("error [%w] while resolving db connection", resolveErr)
	}
	if err := db.Where("computed_unique_lrn = ?", lrn).First(&piModel).Error; err != nil {
		if storageV2.IsRecordNotFoundError(err) {
			err = epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch PI of type UPI_LITE, lrn: %s, %w", lrn, err)
	}

	return convertToPiProto(&piModel)
}

// GetDebitCardPi fetches PI of type DEBIT_CARD by card number, card expiry and card emboss name
func (pd *PiDaoPgdb) GetDebitCardPi(ctx context.Context, cardNumber, expiry, embossName string) (*piPb.PaymentInstrument, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiDaoPgdb", "GetDebitCardPi", time.Now())
	var piModel model.PaymentInstrument
	computedCardIdentifier := fmt.Sprintf(computedColumnFormatThreeKeys, cardNumber, expiry, embossName)
	db, resolveErr := getConnFromContextOrProviderWithDefaultHandling(ctx, pd.dbResourceProvider, pd.db, pd.useDbResourceProvider)
	if resolveErr != nil {
		return nil, fmt.Errorf("error [%w] while resolving db connection", resolveErr)
	}
	if err := db.Where("computed_unique_card = ?", computedCardIdentifier).First(&piModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			err = epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch PI of type Debit Card, cardId: %s, %w", computedCardIdentifier, err)
	}
	return convertToPiProto(&piModel)
}

// UpdatePi updates the Pi fields present in the update fields mask
// verifies if the field masks is eligible to be update for the pi type
// for eg. ACCOUNT_REFERENCE_NUMBER_UPI can be only used with pi of type upi
//
// source app and reason are stored in pi state log only in case of pi state change
// nolint: funlen
func (pd *PiDaoPgdb) UpdatePi(ctx context.Context, pi *piPb.PaymentInstrument,
	fieldMask []piPb.PaymentInstrumentFieldMask, app piPb.Source, reason string) error {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiDaoPgdb", "UpdatePi", time.Now())
	var err error
	if pi.Id == "" {
		return fmt.Errorf("primary identifier can't be empty for an update operation")
	}
	ctx = getCtxWithOwnershipFromAttribute(ctx, idgen.PaymentInstrument, pd.attrIdGen, pi.GetId())
	ownershipFromCtx := epificontext.OwnershipFromContext(ctx)

	updateMask := filterPiFieldMaskSlice(fieldMask, piInvalidFieldFilter)
	if len(updateMask) == 0 {
		return fmt.Errorf("update mask can't be empty")
	}

	// explicitly specifying not to set any default values in the model since we are updating the existing record.
	modelPi, err := convertToPiModel(pi, false)
	if err != nil {
		return fmt.Errorf("error converting pi proto to model %v", err)
	}

	updateColumns := getSelectColumnsForPi(updateMask)

	piUpdateFunc := func(piCtx context.Context) error {
		db, ok := gormctxv2.FromContext(piCtx, nil)
		if !ok {
			return fmt.Errorf("update expected to be called in a DB transactional block")
		}

		if isIdentifierMaskPresent(updateMask) || isCapabilityUpdateMaskPresent(updateMask) {
			fetchedPi := &model.PaymentInstrument{}
			if err = db.Where("id = ?", pi.GetId()).First(&fetchedPi).Error; err != nil {
				if errors.Is(err, gormv2.ErrRecordNotFound) {
					err = epifierrors.ErrRecordNotFound
				}
				return fmt.Errorf("unable to get pi by id: %s : %w", pi.GetId(), err)
			}
			if isIdentifierMaskPresent(updateMask) {
				if err = verifyAndUpdateIdentifier(updateMask, fetchedPi, modelPi); err != nil {
					return err
				}
			}

			if isCapabilityUpdateMaskPresent(updateMask) {
				updateCapability(fetchedPi, modelPi, updateMask)
			}
		}

		if err := db.Model(&modelPi).Select(updateColumns).Updates(modelPi).Error; err != nil {
			return fmt.Errorf("unable to update pi: %s : %w", pi.Id, err)
		}

		// Skip piStateLog creation for OWNERSHIP_LIQUILOANS_PL & Ownership_OWNERSHIP_STOCK_GUARDIAN_TSP since the
		// pi_state_log table is not yet created for these ownerships in the corresponding DBs to quickly unblock
		// NBFC A2L flow. The entries in pi_state_log table help EPIFI_TECH use cases to keep audit logs as users keep
		// activating/deactivating their PIs (e.g. UPI accounts getting added/disconnected). But in case of SG or LL,
		// these are mostly externally issued PIs and the state change audit isn't necessarily required as the state
		// change isn't user action initiated. If the need arises then this needs to be revisited and this needs to be
		// enabled for these ownerships after adding entity-segregation in the DAO layer methods.
		// TODO(Sundeep): Evaluate and re-enable piStateLog creation for LL_PL & SG_TSP ownerships if the need arises later.
		if isPiStateChange(updateMask) &&
			ownershipFromCtx != commontypes.Ownership_LIQUILOANS_PL &&
			ownershipFromCtx != commontypes.Ownership_STOCK_GUARDIAN_TSP {
			piStateLogModel := convertPiModelToPiStateLogModel(modelPi, app, reason)
			if piStateLogErr := db.Create(piStateLogModel).Error; piStateLogErr != nil {
				return fmt.Errorf("failed to insert Pi state log in DB : %w", piStateLogErr)
			}
		}

		return nil
	}

	_, ok := gormctxv2.FromContext(ctx, nil)
	if !ok {
		txnExecutorFromProvider, execProviderErr := getTxnExecutorFromProvider(ctx, pd.dbTxnExecutorResourceProvider, pd.txnExecutor, pd.useDbResourceProvider)
		if execProviderErr != nil {
			return fmt.Errorf("error in fetching txn executor from provider : %w", execProviderErr)
		}
		err = txnExecutorFromProvider.RunTxn(ctx, piUpdateFunc)
	} else {
		err = piUpdateFunc(ctx)
	}

	if err != nil {
		return fmt.Errorf("failed to execute pi updation block: %w", err)
	}

	return nil
}

// DeleteWealthPisByIds deletes the pis for given ids and ownership = EPIFI_WEALTH.
func (pd *PiDaoPgdb) DeleteWealthPisByIds(ctx context.Context, piIds []string) error {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiDaoPgdb", "DeleteWealthPisByIds", time.Now())
	if len(piIds) == 0 {
		return fmt.Errorf("len of pi ids can't be zero: %w", epifierrors.ErrInvalidArgument)
	}

	limit := len(piIds)
	db := gormctxv2.FromContextOrDefault(ctx, pd.db)
	err := db.Unscoped().Where("id in (?) AND ownership = 'EPIFI_WEALTH'", piIds).Delete([]*model.PaymentInstrument{}).Order("id DESC").Limit(limit).Error
	if err != nil {
		return fmt.Errorf("error deleting pis: %w", err)
	}
	return nil
}

// Fetch all the PIs corresponding to the given VPAs (batch call)
func (pd *PiDaoPgdb) GetByVpas(ctx context.Context, vpas []string, options ...storageV2.FilterOption) ([]*piPb.PaymentInstrument, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiDaoPgdb", "GetByVpas", time.Now())
	if len(vpas) == 0 {
		return nil, fmt.Errorf("len of vpas can't be zero %w", epifierrors.ErrInvalidArgument)
	}

	// Define the query logic in a separate function
	queryFunc := func(ctx context.Context, db *gormv2.DB) ([]*model.PaymentInstrument, error) {
		for _, opt := range options {
			db = opt.ApplyInGorm(db)
		}
		var pis []*model.PaymentInstrument
		// ANY is preferred over IN to reduce the number of fingerprints tracked in the database stats.
		// But ANY is not efficient in handling a single element array, so using direct comparison
		switch len(vpas) {
		case 1:
			if err := db.Where("computed_unique_upi = ?", vpas[0]).Find(&pis).Error; err != nil {
				return nil, fmt.Errorf("failed to get PI in DB, VPA: %v: %w", vpas[0], err)
			}
		default:
			if err := db.Where("computed_unique_upi = ANY(?)", pq.Array(vpas)).Find(&pis).Error; err != nil {
				return nil, fmt.Errorf("failed to get PIs in DB, VPAs: %v: %w", vpas, err)
			}
		}
		if len(pis) == 0 {
			return nil, epifierrors.ErrRecordNotFound
		}
		return pis, nil
	}
	var (
		res    []*model.PaymentInstrument
		resErr error
	)

	// 1. We are relying on the context to fetch the db instance and make query
	// 2. If not found via context then we will be using the getMultiDbResponse
	db, ok := gormctxv2.FromContext(ctx, nil)
	if ok {
		res, resErr = queryFunc(ctx, db)
	} else {
		res, resErr = getMultiDbResponse[*model.PaymentInstrument](ctx, pd.dbResourceProvider, queryFunc)
	}

	if resErr != nil {
		return nil, fmt.Errorf("error fetching payment instruments from DB: %w", resErr)
	}
	return convertToPiProtos(res)
}

func (pd *PiDaoPgdb) GetCreditCardPi(ctx context.Context, cardId string) (*piPb.PaymentInstrument, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiDaoPgdb", "GetCreditCardPi", time.Now())
	if cardId == "" {
		return nil, epifierrors.ErrInvalidArgument
	}

	var piModel model.PaymentInstrument
	// prefixing id with the type (we are storing this way in db to avoid collision among microservices)
	cardId = prefixTypeToId(cardId, piPb.PaymentInstrumentType_CREDIT_CARD)
	db, resolveErr := getConnFromContextOrProviderWithDefaultHandling(ctx, pd.dbResourceProvider, pd.db, pd.useDbResourceProvider)
	if resolveErr != nil {
		return nil, fmt.Errorf("error [%w] while resolving db connection", resolveErr)
	}
	if err := db.Where("computed_unique_credit_card_id = ?", cardId).First(&piModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			err = epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch PI of type Credit Card, cardId: %s, %w", cardId, err)
	}
	return convertToPiProto(&piModel)
}

func convertToPiProtos(piModels []*model.PaymentInstrument) ([]*piPb.PaymentInstrument, error) {
	var pis = make([]*piPb.PaymentInstrument, 0)
	for _, piModel := range piModels {
		pi, err := convertToPiProto(piModel)
		if err != nil {
			return nil, err
		}
		pis = append(pis, pi)
	}

	return pis, nil
}

// convertToPiProto converts model to proto
func convertToPiProto(piModel *model.PaymentInstrument) (*piPb.PaymentInstrument, error) {
	var (
		pi  piPb.PaymentInstrument
		err error
	)

	if err = copier.Copy(&pi, &piModel); err != nil {
		return nil, fmt.Errorf("failed to copy from model to proto: %w", err)
	}
	pi.DeletedAt = nil

	switch {
	case piModel.Identifier.Account != nil:
		pi.Identifier = &piPb.PaymentInstrument_Account{Account: piModel.Identifier.Account}
	case piModel.Identifier.Card != nil:
		pi.Identifier = &piPb.PaymentInstrument_Card{Card: piModel.Identifier.Card}
	case piModel.Identifier.Upi != nil:
		pi.Identifier = &piPb.PaymentInstrument_Upi{Upi: piModel.Identifier.Upi}
	case piModel.Identifier.CreditCard != nil:
		pi.Identifier = &piPb.PaymentInstrument_CreditCard{CreditCard: piModel.Identifier.CreditCard}
	case piModel.Identifier.InternationalAccount != nil:
		pi.Identifier = &piPb.PaymentInstrument_InternationalAccount{InternationalAccount: piModel.Identifier.InternationalAccount}
	case piModel.Identifier.UpiLite != nil:
		pi.Identifier = &piPb.PaymentInstrument_UpiLite{UpiLite: piModel.Identifier.UpiLite}
	}

	if !piModel.CreatedAt.IsZero() {
		if pi.CreatedAt, err = ptypes.TimestampProto(piModel.CreatedAt); err != nil {
			return nil, fmt.Errorf("failed to map createdAt from model to proto: %w", err)
		}
	}

	if !piModel.UpdatedAt.IsZero() {
		if pi.UpdatedAt, err = ptypes.TimestampProto(piModel.UpdatedAt); err != nil {
			return nil, fmt.Errorf("failed to map updatedAt from model to proto: %w", err)
		}
	}

	if piModel.LastVerifiedAt != nil && !piModel.LastVerifiedAt.IsZero() {
		pi.LastVerifiedAt = timestamppb.New(*piModel.LastVerifiedAt)
	}

	if piModel.DeletedAt.Valid {
		pi.DeletedAt = timestamppb.New(piModel.DeletedAt.Time)
	}

	return &pi, nil
}

// convertToPiModel converts proto to model.
// It also takes a boolean setDefault which is used to set default values in the model.
// - This can help when the model is being prepared for creation of new entities in the DB.
// nolint:funlen
func convertToPiModel(pi *piPb.PaymentInstrument, setDefault bool) (*model.PaymentInstrument, error) {
	var (
		piModel = model.PaymentInstrument{}
		err     error
	)

	if err = copier.Copy(&piModel, pi); err != nil {
		return nil, fmt.Errorf("failed to copy from proto to model: %w", err)
	}
	piModel.DeletedAt.Valid = false

	switch pi.GetIdentifier().(type) {
	case *piPb.PaymentInstrument_Account:
		piModel.Identifier = &model.Identifier{Account: pi.GetAccount()}

		// Context(9th Jul, 2024): We are setting the default value as REGULAR in case none is provided.
		// This is done to keep the APO assumptions consistent in the codebase, i.e.:
		// 1. We'd store the default value as REGULAR in the account_pis table (DAO level default).
		if setDefault {
			if piModel.Identifier.Account != nil && piModel.Identifier.Account.GetApo() == account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED {
				piModel.Identifier.Account.Apo = account.AccountProductOffering_APO_REGULAR
			}
		}
	case *piPb.PaymentInstrument_Card:
		piModel.Identifier = &model.Identifier{Card: pi.GetCard()}
	case *piPb.PaymentInstrument_Upi:
		piModel.Identifier = &model.Identifier{Upi: pi.GetUpi()}

		// Context(9th Jul, 2024): We are setting the default value as REGULAR in case none is provided.
		// This is done to keep the APO assumptions consistent in the codebase, i.e.:
		// 1. We'd store the default value as REGULAR in the upi_accounts table (DAO level default).
		// 2. We'd store the default value as REGULAR in the account_pis table (DAO level default).
		if setDefault {
			if piModel.Identifier.Upi != nil && piModel.Identifier.Upi.GetApo() == account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED {
				piModel.Identifier.Upi.Apo = account.AccountProductOffering_APO_REGULAR
			}
		}
	case *piPb.PaymentInstrument_CreditCard:
		piModel.Identifier = &model.Identifier{CreditCard: pi.GetCreditCard()}
	case *piPb.PaymentInstrument_InternationalAccount:
		piModel.Identifier = &model.Identifier{InternationalAccount: pi.GetInternationalAccount()}
	case *piPb.PaymentInstrument_UpiLite:
		piModel.Identifier = &model.Identifier{UpiLite: pi.GetUpiLite()}
		// TODO(kunal): What should be the default case here if any?
	}

	if pi.CreatedAt.GetSeconds() != 0 {
		if piModel.CreatedAt, err = ptypes.Timestamp(pi.CreatedAt); err != nil {
			return nil, fmt.Errorf("failed to convert createdAt from proto to model: %w", err)
		}
	}

	if pi.UpdatedAt.GetSeconds() != 0 {
		if piModel.UpdatedAt, err = ptypes.Timestamp(pi.UpdatedAt); err != nil {
			return nil, fmt.Errorf("failed to convert updatedAt from proto to model: %w", err)
		}
	}

	if pi.GetLastVerifiedAt().IsValid() {
		verifiedTime := pi.GetLastVerifiedAt().AsTime()
		piModel.LastVerifiedAt = &verifiedTime
	}

	if pi.DeletedAt != nil {
		piModel.DeletedAt = gormv2.DeletedAt{
			Time:  pi.DeletedAt.AsTime(),
			Valid: true,
		}
	}

	if piModel.DeletedAt.Valid {
		if deletedAt, err := ptypes.Timestamp(pi.DeletedAt); err != nil {
			return nil, fmt.Errorf("failed to convert deletedAt from proto to model: %w", err)
		} else {
			piModel.DeletedAt = gormv2.DeletedAt{Time: deletedAt}
		}
	}
	return &piModel, nil
}

// filterPiFieldMaskSlice filters out elements from a slice based on the check function passed in arg
// elements are filtered out if they dont pass the check function
func filterPiFieldMaskSlice(fieldMasks []piPb.PaymentInstrumentFieldMask,
	check func(field piPb.PaymentInstrumentFieldMask) bool) []piPb.PaymentInstrumentFieldMask {
	var ret []piPb.PaymentInstrumentFieldMask
	for _, fieldMask := range fieldMasks {
		if check(fieldMask) {
			ret = append(ret, fieldMask)
		}
	}
	return ret
}

// getSelectColumnsForPi converts field mask to string slice with column name corresponding to field name enums
func getSelectColumnsForPi(fieldMasks []piPb.PaymentInstrumentFieldMask) []string {
	var selectColumns []string
	isIdentiferAdded := false
	for _, field := range fieldMasks {
		columnName := piColumnNameMap[field]
		if columnName == identifierColumnName {
			if !isIdentiferAdded {
				selectColumns = append(selectColumns, columnName)
				isIdentiferAdded = true
			}
		} else {
			selectColumns = append(selectColumns, columnName)
		}
	}
	return selectColumns
}

// verifies if the field masks is eligible to be update for the pi type
// for eg. ACCOUNT_REFERENCE_NUMBER_UPI can be only used with pi of type upi
// updates the identifier of the pi model to make sure only fields present
// in the fields mask for identifier is updated
func verifyAndUpdateIdentifier(updateMasks []piPb.PaymentInstrumentFieldMask,
	fetchedPi *model.PaymentInstrument, pi *model.PaymentInstrument) error {
	fetchedIdentifier := fetchedPi.Identifier

	// back fill required for previously saved merchants
	if fetchedPi.Type == piPb.PaymentInstrumentType_UPI &&
		fetchedPi.Identifier.Upi != nil && fetchedPi.Identifier.Upi.MerchantDetails == nil {
		fetchedIdentifier.Upi.MerchantDetails = &piPb.Upi_MerchantDetails{}
	}
	for _, updateMask := range updateMasks {
		switch updateMask {
		case piPb.PaymentInstrumentFieldMask_ACCOUNT_REFERENCE_NUMBER_UPI:
			if fetchedPi.Type != piPb.PaymentInstrumentType_UPI {
				return fmt.Errorf("account reference number cannot be updated for pi of type: %v, err: %v",
					fetchedPi.Type, epifierrors.ErrInvalidArgument)
			}
			fetchedIdentifier.Upi.AccountReferenceNumber = pi.Identifier.Upi.AccountReferenceNumber
		case piPb.PaymentInstrumentFieldMask_IFSC_CODE_UPI:
			if fetchedPi.Type != piPb.PaymentInstrumentType_UPI {
				return fmt.Errorf("ifsc code cannot be updated for pi of type: %v, err: %v",
					fetchedPi.Type, epifierrors.ErrInvalidArgument)
			}
			fetchedIdentifier.Upi.IfscCode = pi.Identifier.Upi.IfscCode
		case piPb.PaymentInstrumentFieldMask_ACCOUNT_TYPE_UPI:
			if fetchedPi.Type != piPb.PaymentInstrumentType_UPI {
				return fmt.Errorf("account type cannot be updated for pi of type: %v, err: %v",
					fetchedPi.Type, epifierrors.ErrInvalidArgument)
			}
			fetchedIdentifier.Upi.AccountType = pi.Identifier.Upi.AccountType
		case piPb.PaymentInstrumentFieldMask_APO_UPI:
			if fetchedPi.Type != piPb.PaymentInstrumentType_UPI {
				return fmt.Errorf("apo cannot be updated for pi of type: %v, err: %v",
					fetchedPi.Type, epifierrors.ErrInvalidArgument)
			}
			fetchedIdentifier.Upi.Apo = pi.Identifier.Upi.GetApo()
		case piPb.PaymentInstrumentFieldMask_MERCHANT_DETAILS_UPI:
			switch {
			case fetchedPi.Type != piPb.PaymentInstrumentType_UPI:
				return fmt.Errorf("merchant details cannot be updated for pi of type: %v, err: %v",
					fetchedPi.Type, epifierrors.ErrInvalidArgument)
			case pi.Identifier.Upi.GetMerchantDetails() == nil:
				// if merchant details are nil , we should not update the older merchant details with nil value
				continue
			}

			if err := mergo.Merge(fetchedIdentifier.Upi.MerchantDetails, pi.Identifier.Upi.MerchantDetails, mergo.WithOverride); err != nil {
				return fmt.Errorf("error in merging fetched merchant details and merchant details to update %w", err)
			}
		}
	}
	pi.Identifier = fetchedIdentifier
	return nil
}

func updateCapability(fetchedPi, modelPi *model.PaymentInstrument, fieldMasks []piPb.PaymentInstrumentFieldMask) {
	for _, mask := range fieldMasks {
		if mask == piPb.PaymentInstrumentFieldMask_CAPABILITY_INBOUND_TXN {
			(*fetchedPi.Capabilities)[piPb.Capability_INBOUND_TXN.String()] = (*modelPi.Capabilities)[piPb.
				Capability_INBOUND_TXN.String()]
		}
	}
	modelPi.Capabilities = fetchedPi.Capabilities
}

// isIdentifierMaskPresent returns true if the update mask contains Identifier related field mask
// else returns false
func isIdentifierMaskPresent(updateMasks []piPb.PaymentInstrumentFieldMask) bool {
	for _, updateMask := range updateMasks {
		if lo.Contains(identifierMaskList, updateMask) {
			return true
		}
	}
	return false
}

func isCapabilityUpdateMaskPresent(updateMasks []piPb.PaymentInstrumentFieldMask) bool {
	return lo.Contains(updateMasks, piPb.PaymentInstrumentFieldMask_CAPABILITY_INBOUND_TXN)
}

func convertPiModelToPiStateLogModel(pi *model.PaymentInstrument, source piPb.Source, reason string) *model.PiStateLog {
	return &model.PiStateLog{
		PiId:   pi.Id,
		Source: source,
		State:  pi.State,
		Reason: nulltypes.NewNullString(reason),
	}
}

func isPiStateChange(updateMasks []piPb.PaymentInstrumentFieldMask) bool {
	for _, updateMask := range updateMasks {
		if updateMask == piPb.PaymentInstrumentFieldMask_STATE {
			return true
		}
	}
	return false
}

// prefixTypeToId will add the type as prefix to the ids
func prefixTypeToId(id string, piType piPb.PaymentInstrumentType) string {
	return piType.String() + "_" + id
}

func getConnFromContextOrProviderWithDefaultHandling(ctx context.Context, dbResourceProvider *usecase.DBResourceProvider[*gormv2.DB], defaultDb *gormv2.DB, useDbResourceProvider types.EnableResourceProvider) (*gormv2.DB, error) {
	if !useDbResourceProvider {
		return gormctxv2.FromContextOrDefault(ctx, defaultDb), nil
	}
	// if we are not able to fetch ownership from context, the ownership will default to EPIFI_TECH
	ow := epificontext.OwnershipFromContext[context.Context](ctx)
	dbConn, err := dbResourceProvider.GetResource(ow, useCase)
	if err != nil {
		return nil, fmt.Errorf("error in fetching db from ownership :%s : %w", ow, err)
	}
	return gormctxv2.FromContextOrDefault(ctx, dbConn), nil
}

func getGeneratedId(ctx context.Context, d idgen.Domain, idgen idgen.IdGenerator, attributedIdGen idgen.AttributedIdGen[*PaymentInstrumentAttribute], useDbResourceProvider types.EnableResourceProvider) (string, error) {
	if !useDbResourceProvider {
		return idgen.Get(d)
	}
	ownership := epificontext.OwnershipFromContext(ctx)
	return attributedIdGen.GetIdWithAttribute(d, &PaymentInstrumentAttribute{
		Ownership: int(ownership),
	})
}

func getCtxWithOwnershipFromAttribute(ctx context.Context, domain idgen.Domain, attributedIdGen idgen.AttributedIdGen[*PaymentInstrumentAttribute], embeddedId string) context.Context {
	attribute := attributedIdGen.GetAttributeFromId(domain, embeddedId)
	if attribute == nil {
		return ctx
	}
	return epificontext.WithOwnership(ctx, commontypes.Ownership(attribute.Ownership))
}

func getOwnershipToIdListMap(ids []string, d idgen.Domain, attrIdg idgen.AttributedIdGen[*PaymentInstrumentAttribute]) map[commontypes.Ownership][]string {
	ownershipMap := make(map[commontypes.Ownership][]string)
	for _, id := range ids {
		attribute := attrIdg.GetAttributeFromId(d, id)
		if attribute == nil {
			attribute = &PaymentInstrumentAttribute{}
		}
		ow := commontypes.Ownership(attribute.Ownership)
		ownershipMap[ow] = append(ownershipMap[ow], id)
	}
	return ownershipMap
}

// getMultiDbResponse aggregates the response for a particular query function from multiple databases. This is generic in nature
// and can be used to aggregate any type of model data
func getMultiDbResponse[T any](ctx context.Context, dbResProvider *usecase.DBResourceProvider[*gormv2.DB], queryFunc func(ctx context.Context, db *gormv2.DB) ([]T, error)) ([]T, error) {
	collectedData := make([]T, 0)
	collectedDataChan := make(chan []T, len(dbResProvider.DBOwnershipMap))
	grp, grpCtx := errgroup.WithContext(ctx) // Capture the returned grpCtx
	for ownership, db := range dbResProvider.DBOwnershipMap {
		db := db // capture variable inside the loop to avoid issues with goroutines
		grp.Go(func() error {
			// Pass grpCtx instead of the original ctx
			res, err := queryFunc(grpCtx, db[useCase])
			switch {
			case errors.Is(err, epifierrors.ErrRecordNotFound):
				return nil
			case err != nil:
				return fmt.Errorf("failed to fetch record for ownership: %v, error: %w", ownership, err)
			default:
				collectedDataChan <- res
				return nil
			}
		})
	}
	if err := grp.Wait(); err != nil {
		return nil, fmt.Errorf("failed to fetch record from all databases: %w", err)
	}

	close(collectedDataChan)

	for data := range collectedDataChan {
		collectedData = append(collectedData, data...)
	}
	return collectedData, nil
}

func getTxnExecutorFromProvider(ctx context.Context, dbTxnExecutorResourceProvider *usecase.DBResourceProvider[storageV2.IdempotentTxnExecutor], defaultTxnExecutor storageV2.TxnExecutor, useDbResourceProvider types.EnableResourceProvider) (storageV2.TxnExecutor, error) {
	if !useDbResourceProvider {
		return defaultTxnExecutor, nil
	}
	ownership := epificontext.OwnershipFromContext(ctx)
	txnExecutor, err := dbTxnExecutorResourceProvider.GetResource(ownership, useCase)
	if err != nil {
		logger.Error(ctx, "error in getting resource from ownership", zap.Error(err))
		return nil, fmt.Errorf("err [%w] in GetResourceForOwnership for ownership %s", err, ownership.String())
	}
	return txnExecutor, nil
}
