package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/golang/protobuf/ptypes"
	"github.com/jinzhu/copier"
	"google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	pkgCmdTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	accountPIPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/typesv2/account"
	"github.com/epifi/gamma/paymentinstrument/dao/model"
)

// AccountPiDaoPgdb implements AccountPiDao using CRDB
type AccountPiDaoPgdb struct {
	db    *gormv2.DB
	idGen idgen.IdGenerator
}

// Ensure AccountPiDaoPgdb implements AccountPiDao at compile time
var _ AccountPiDao = &AccountPiDaoPgdb{}

// Factory method for creating an instance of accountPI dao. This method will be used by the injector
// when providing the dependencies at initialization time.
func NewAccountPiDaoPgdb(db pkgCmdTypes.PaymentInstrumentPGDB, idGen idgen.IdGenerator) *AccountPiDaoPgdb {
	return &AccountPiDaoPgdb{db: db, idGen: idGen}
}

// Create creates an relationship between account <> PI
//
//nolint:dupl
func (ap *AccountPiDaoPgdb) Create(ctx context.Context, accountPI *accountPIPb.AccountPI) (*accountPIPb.AccountPI, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "AccountPiDaoPgdb", "Create", time.Now())
	id, err := ap.idGen.Get(idgen.AccountPI)
	if err != nil {
		return nil, fmt.Errorf("id generation failed: %w", err)
	}
	db := gormctxv2.FromContextOrDefault(ctx, ap.db)
	accountPIModel, err := convertToAccountPIModel(accountPI, true)
	if err != nil {
		return nil, fmt.Errorf("failed to convert from proto to model: %w", err)
	}

	accountPIModel.Id = id
	if err := db.Create(accountPIModel).Error; err != nil {
		return nil, fmt.Errorf("failed to insert accountPI: %w", err)
	}

	return convertToAccountPIProto(accountPIModel)
}

// nolint: dupl
func (ap *AccountPiDaoPgdb) GetByPiId(ctx context.Context, piId string) (*accountPIPb.AccountPI, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "AccountPiDaoPgdb", "GetByPiId", time.Now())
	var accountPiModel model.AccountPI
	db := gormctxv2.FromContextOrDefault(ctx, ap.db)
	if err := db.Where("pi_id = ?", piId).First(&accountPiModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			err = epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to get accountPI in DB, pi_id: %s: %w", piId, err)
	}

	return convertToAccountPIProto(&accountPiModel)
}

// GetByAccountId fetches PIs belonging to a given accountId
func (ap *AccountPiDaoPgdb) GetByAccountId(ctx context.Context, accountId string) ([]*accountPIPb.AccountPI, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "AccountPiDaoPgdb", "GetByAccountId", time.Now())
	var accountPIModels []*model.AccountPI
	db := gormctxv2.FromContextOrDefault(ctx, ap.db)
	if err := db.Where("account_id = ?", accountId).Find(&accountPIModels).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch accountPIs, accountId: %s, %w", accountId, err)
	}

	return convertToAccountPIProtos(accountPIModels)
}

// GetByActorId fetches AccountPIs for a given actor
func (ap *AccountPiDaoPgdb) GetByActorId(ctx context.Context, actorId string) ([]*accountPIPb.AccountPI, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "AccountPiDaoPgdb", "GetByActorId", time.Now())
	var accountPIModels []*model.AccountPI
	db := gormctxv2.FromContextOrDefault(ctx, ap.db)
	if err := db.Where("actor_id = ?", actorId).Find(&accountPIModels).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch accountPIs, actorId: %s, %w", actorId, err)
	}

	return convertToAccountPIProtos(accountPIModels)
}

// convertToAccountPIProtos converts model struct(s) to domain proto struct(s)
func convertToAccountPIProtos(accountPIModels []*model.AccountPI) ([]*accountPIPb.AccountPI, error) {
	var accountPIs []*accountPIPb.AccountPI
	for _, accountPIModel := range accountPIModels {
		accountPI, err := convertToAccountPIProto(accountPIModel)
		if err != nil {
			return nil, err
		}
		accountPIs = append(accountPIs, accountPI)
	}

	return accountPIs, nil
}

// convertToAccountPIProto converts model struct to domain proto struct
func convertToAccountPIProto(accountPIModel *model.AccountPI) (*accountPIPb.AccountPI, error) {
	var (
		accountPI accountPIPb.AccountPI
		err       error
	)

	if err = copier.Copy(&accountPI, accountPIModel); err != nil {
		return nil, fmt.Errorf("failed to copy from proto to model: %w", err)
	}
	accountPI.DeletedAt = nil

	if !accountPIModel.CreatedAt.IsZero() {
		if accountPI.CreatedAt, err = ptypes.TimestampProto(accountPIModel.CreatedAt); err != nil {
			return nil, fmt.Errorf("failed to map createdAt from model to proto: %w", err)
		}
	}

	if !accountPIModel.UpdatedAt.IsZero() {
		if accountPI.UpdatedAt, err = ptypes.TimestampProto(accountPIModel.UpdatedAt); err != nil {
			return nil, fmt.Errorf("failed to map updatedAt from model to proto: %w", err)
		}
	}

	if accountPIModel.DeletedAt.Valid {
		accountPI.DeletedAt = timestamppb.New(accountPIModel.DeletedAt.Time)
	}

	return &accountPI, nil
}

// convertToAccountPIModel converts domain proto struct to model struct
// It also takes a boolean setDefault which is used to set default values in the model.
// - This can help when the model is being prepared for creation of new entities in the DB.
func convertToAccountPIModel(accountPI *accountPIPb.AccountPI, setDefault bool) (*model.AccountPI, error) {
	var (
		accountPIModel = model.AccountPI{}
		err            error
	)

	if err = copier.Copy(&accountPIModel, accountPI); err != nil {
		return nil, fmt.Errorf("failed to copy from proto to model: %w", err)
	}
	accountPIModel.DeletedAt.Valid = false

	if accountPI.CreatedAt.GetSeconds() != 0 {
		if accountPIModel.CreatedAt, err = ptypes.Timestamp(accountPI.CreatedAt); err != nil {
			return nil, fmt.Errorf("failed to convert createdAt from proto to model: %w", err)
		}
	}

	if accountPI.UpdatedAt.GetSeconds() != 0 {
		if accountPIModel.UpdatedAt, err = ptypes.Timestamp(accountPI.UpdatedAt); err != nil {
			return nil, fmt.Errorf("failed to convert updatedAt from proto to model: %w", err)
		}
	}

	if accountPI.DeletedAt != nil {
		accountPIModel.DeletedAt = gormv2.DeletedAt{
			Time:  accountPI.DeletedAt.AsTime(),
			Valid: true,
		}
	}

	if setDefault {
		// in case of unspecified APO, set it to APO_REGULAR
		if accountPIModel.Apo == account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED {
			accountPIModel.Apo = account.AccountProductOffering_APO_REGULAR
		}
	}

	return &accountPIModel, nil
}
