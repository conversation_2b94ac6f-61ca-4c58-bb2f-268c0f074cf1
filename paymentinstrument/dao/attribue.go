// nolint: goimports
package dao

import (
	"encoding/json"
	"fmt"

	"github.com/btcsuite/btcutil/base58"
	"github.com/epifi/be-common/pkg/idgen"
)

type PaymentInstrumentAttribute struct {
	Ownership int `json:"o"`
}

// Instead of directly converting the Ownership integer to a string, the entire struct
// is marshaled into JSON and then encoded for the following reasons:
//
// 1. **Future-Proofing**: The struct may evolve to contain additional fields in the future.
//    By serializing the entire object, we ensure that future changes are backward-compatible
//    without altering the conversion logic.
// 2. **Compact Encoding**: Base58 encoding minimizes the size of the serialized JSON output,
//    reducing storage and transmission overhead, while also avoiding special characters that
//    can cause issues in URLs or database keys.

func (o *PaymentInstrumentAttribute) ToString() (string, error) {
	if o == nil {
		return "", nil
	}
	jsonAttr, err := json.Marshal(o)
	if err != nil {
		return "", fmt.<PERSON>rrorf("error marshalling actor attributes: %w", err)
	}
	return base58.Encode(jsonAttr), nil
}

func (o *PaymentInstrumentAttribute) FromString(s string) idgen.Attribute {
	var attr PaymentInstrumentAttribute
	err := json.Unmarshal(base58.Decode(s), &attr)
	if err != nil {
		return nil
	}
	return &attr
}

var _ idgen.Attribute = &PaymentInstrumentAttribute{}
