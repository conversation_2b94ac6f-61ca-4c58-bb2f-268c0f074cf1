package dao

import (
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	gormV2 "gorm.io/gorm"
)

// WithOwnership returns storage.FilterOption that sets ownership filter in the DB.
func WithOwnership(ownerShip piPb.Ownership) storageV2.FilterOption {
	return storageV2.NewFuncFilterOption(func(db *gormV2.DB) *gormV2.DB {
		return db.Where("ownership = ?", ownerShip)
	})
}

// WithIssuer returns storage.FilterOption that sets issuer classification filter in the DB.
func WithIssuer(issuer piPb.PaymentInstrumentIssuer) storageV2.FilterOption {
	return storageV2.NewFuncFilterOption(func(db *gormV2.DB) *gormV2.DB {
		return db.Where("issuer_classification = ?", issuer)
	})
}
