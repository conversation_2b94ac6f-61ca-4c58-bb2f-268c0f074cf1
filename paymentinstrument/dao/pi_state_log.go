package dao

import (
	"context"
	"fmt"
	"time"

	gormv2 "gorm.io/gorm"

	pkgCmdTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/nulltypes"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/golang/protobuf/ptypes"

	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/paymentinstrument/dao/model"
)

type PiStateLogDaoPGDB struct {
	db *gormv2.DB
}

// NewpiStateLogDaoPGDB factory method to initialize a pi state log dao
func NewpiStateLogDaoPGDB(db pkgCmdTypes.PaymentInstrumentPGDB) *PiStateLogDaoPGDB {
	return &PiStateLogDaoPGDB{db: db}
}

// Ensure PiStatusLogDao implements PiStateLogDaoPGDB at compile time
var _ PiStatusLogDao = &PiStateLogDaoPGDB{}

// Create creates an new mapping between pi and the state log
func (b *PiStateLogDaoPGDB) Create(ctx context.Context, piStateLog *piPb.PaymentInstrumentStateLog) (*piPb.PaymentInstrumentStateLog, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiStateLogDaoPGDB", "Create", time.Now())
	piStateLogModel := convertToPiStateLogProtoModel(piStateLog)
	db := gormctxv2.FromContextOrDefault(ctx, b.db)
	if err := db.Create(&piStateLogModel).Error; err != nil {
		return nil, fmt.Errorf("failed to create entry in pi state log: %w", err)
	}

	return convertToPiStateLogProto(piStateLogModel)
}

// GetByPiId returns pi state log list for a given pi id
func (b *PiStateLogDaoPGDB) GetByPiId(ctx context.Context, piId string) ([]*piPb.PaymentInstrumentStateLog, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiStateLogDaoPGDB", "GetByPiId", time.Now())
	var piStateLogModelList []*model.PiStateLog
	db := gormctxv2.FromContextOrDefault(ctx, b.db)
	if err := db.Where("pi_id = ?", piId).Find(&piStateLogModelList).Error; err != nil {
		return nil, fmt.Errorf("error fetching pi state log: %v", err)
	}
	return covertPiStateLogModelListToProto(piStateLogModelList)
}

// converts piStateLog proto to model
func convertToPiStateLogProtoModel(piStateLog *piPb.PaymentInstrumentStateLog) *model.PiStateLog {

	piStateLogModel := &model.PiStateLog{
		IdV2:   piStateLog.GetIdV2(),
		PiId:   piStateLog.GetPiId(),
		Source: piStateLog.GetSource(),
		Reason: nulltypes.NewNullString(piStateLog.GetReason()),
		State:  piStateLog.GetState(),
	}

	return piStateLogModel
}

// converts piStateLog model to proto
func convertToPiStateLogProto(piStateLogModel *model.PiStateLog) (*piPb.PaymentInstrumentStateLog, error) {
	var err error
	piStateLogProto := &piPb.PaymentInstrumentStateLog{
		IdV2:   piStateLogModel.IdV2,
		PiId:   piStateLogModel.PiId,
		Source: piStateLogModel.Source,
		Reason: piStateLogModel.Reason.GetValue(),
		State:  piStateLogModel.State,
	}

	piStateLogProto.CreatedAt, err = ptypes.TimestampProto(piStateLogModel.CreatedAt)
	if err != nil {
		return nil, fmt.Errorf("failed to convert createdAt from model to proto: %w", err)
	}

	piStateLogProto.UpdatedAt, err = ptypes.TimestampProto(piStateLogModel.UpdatedAt)
	if err != nil {
		return nil, fmt.Errorf("failed to convert updatedAt from model to proto: %w", err)
	}

	if piStateLogModel.DeletedAt.Valid {
		deletedAt, err := ptypes.TimestampProto(piStateLogModel.DeletedAt.Time)
		if err != nil {
			return nil, fmt.Errorf("failed to convert deletedAt from model to proto: %w", err)
		}
		piStateLogProto.DeletedAt = deletedAt
	}

	return piStateLogProto, nil
}

// converts list of piStateLog model to proto
func covertPiStateLogModelListToProto(piStateLogModelList []*model.PiStateLog) ([]*piPb.PaymentInstrumentStateLog, error) {
	var piStateProtoList []*piPb.PaymentInstrumentStateLog
	for _, pisStateLogModel := range piStateLogModelList {
		piStateProto, err := convertToPiStateLogProto(pisStateLogModel)
		if err != nil {
			return nil, fmt.Errorf("failed to convert pi state log model to proto: %v", err)
		}
		piStateProtoList = append(piStateProtoList, piStateProto)
	}
	return piStateProtoList, nil
}
