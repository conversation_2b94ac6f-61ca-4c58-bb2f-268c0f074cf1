package dao_test

import (
	"context"
	"testing"

	gormv2 "gorm.io/gorm"

	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/paymentinstrument/config/genconf"
	"github.com/epifi/gamma/paymentinstrument/dao"
)

var (
	piPurgeAuditsAffectedTables = []string{"payment_instrument_purge_audits"}
)

type piPurgeAuditTestSuite struct {
	db              *gormv2.DB
	genConf         *genconf.Config
	piPurgeAuditDao *dao.PaymentInstrumentPurgeAuditPGDB
}

func Test_paymentInstrumentPurgeAuditPgdb_BatchUpsert(t *testing.T) {
	testSuite, testSuiteReleaseFunc := getPiPurgeAuditTestSuite(t)
	testSuiteReleaseFunc()
	tests := []struct {
		name         string
		auditEntries []*piPb.PaymentInstrumentPurgeAudit
		wantErr      bool
	}{
		{
			name: "upsert entry successfully",
			auditEntries: []*piPb.PaymentInstrumentPurgeAudit{
				{
					// upsert for already present entry
					PiId:    "pi-1",
					Payload: fixturePi1,
				},
				{
					// create new entry
					PiId:    "pi-2",
					Payload: fixturePi2Wealth,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := testSuite.piPurgeAuditDao.BatchUpsert(context.Background(), tt.auditEntries); (err != nil) != tt.wantErr {
				t.Errorf("BatchUpsert() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
