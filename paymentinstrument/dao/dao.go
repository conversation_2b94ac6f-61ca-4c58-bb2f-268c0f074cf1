// nolint: goimports
//
//go:generate dao_metrics_gen .
//go:generate mockgen -source=dao.go -destination=./mocks/mock_dao.go -package=mocks
package dao

import (
	"context"
	"time"

	"github.com/google/wire"
	"github.com/jonboulle/clockwork"

	"github.com/epifi/be-common/pkg/idgen"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	accountsPb "github.com/epifi/gamma/api/accounts"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/paymentinstrument/config/genconf"
	"github.com/epifi/gamma/paymentinstrument/dao/model"
)

var PiWireSet = wire.NewSet(NewPiCache, NewPiDaoPgdb, ProvidePiDao, ProvideAttributedIdGen)

func ProvidePiDao(piCache *PiCache, conf *genconf.Config, piDaoPgdb *PiDaoPgdb) PiDao {
	if conf != nil && conf.PiCacheConfig() != nil && conf.PiCacheConfig().IsCachingEnabled() {
		return piCache
	}

	return piDaoPgdb
}

func ProvideAttributedIdGen(clk clockwork.Clock) idgen.AttributedIdGen[*PaymentInstrumentAttribute] {
	return idgen.NewAttributedIdGen[*PaymentInstrumentAttribute](clk)
}

var AccountPiWireSet = wire.NewSet(NewAccountPiCache, NewAccountPiDaoPgdb, ProvideAccountPiDao)
var AaAccountPiWireSet = wire.NewSet(NewAAAccountPiDaoPgdb, wire.Bind(new(AAAccountPiDao), new(*AAAccountPiDaoPgdb)))

func ProvideAccountPiDao(cachedAccountPi *AccountPiCache,
	accountPiDaoPgdb *AccountPiDaoPgdb, genConf *genconf.Config) AccountPiDao {
	if genConf != nil && genConf.AccountPiCacheConfig() != nil && genConf.AccountPiCacheConfig().IsCachingEnabled() {
		return cachedAccountPi
	}

	return accountPiDaoPgdb
}

var PiStatusLogDaoWireSet = wire.NewSet(NewpiStateLogDaoPGDB, wire.Bind(new(PiStatusLogDao), new(*PiStateLogDaoPGDB)))
var PiPurgeAuditDaoWireSet = wire.NewSet(NewpaymentInstrumentPurgeAuditPGDB, wire.Bind(new(PiPurgeAuditDao), new(*PaymentInstrumentPurgeAuditPGDB)))
var PiLastTransactionDaoWireSet = wire.NewSet(NewPiLastTransactionPgdb, wire.Bind(new(PiLastTransactionDao), new(*PiLastTransactionPgdb)))

// PiDao defines all the DB operations that can be done on PI entity
type PiDao interface {
	Create(ctx context.Context, pi *piPb.PaymentInstrument) (*piPb.PaymentInstrument, error)
	GetById(ctx context.Context, id string) (*piPb.PaymentInstrument, error)

	// GetByIds returns a list of PIs for a given list of PI ids
	GetByIds(ctx context.Context, ids []string) ([]*piPb.PaymentInstrument, error)

	// GetBankAccountPI fetches PI of type BANK_ACCOUNT by accountNumber and IfscCode
	GetBankAccountPI(ctx context.Context, accountNumber, ifscCode string) (*piPb.PaymentInstrument, error)

	// GetUpiPI fetches PI of type UPI by vpa
	GetUpiPI(ctx context.Context, vpa string) (*piPb.PaymentInstrument, error)

	// GetDebitCardPi fetches PI of type DEBIT_CARD by card number, card expiry and card emboss name
	GetDebitCardPi(ctx context.Context, cardNumber, expiry, embossName string) (*piPb.PaymentInstrument, error)

	// UpdatePi updates the Pi fields present in the update fields mask
	// verifies if the field masks is eligible to be update for the pi type
	// for eg. ACCOUNT_REFERENCE_NUMBER_UPI can be only used with pi of type upi
	//
	// source app and reason are stored in pi state log only in case of pi state change
	UpdatePi(ctx context.Context, pi *piPb.PaymentInstrument, fieldMask []piPb.PaymentInstrumentFieldMask,
		app piPb.Source, reason string) error

	// GetPartialBankAccountPI fetches pi using account number, ifsc , name
	// fetches the pi by computed column -> name_accountNumber_ifscCode
	GetPartialBankAccountPI(ctx context.Context, accountNumber, ifscCode, name string) (*piPb.PaymentInstrument, error)

	// GetPartialUpiPI fetches PI of type UPI by vpa and name
	// fetches the PI by computed column -> name_vpa
	GetPartialUpiPI(ctx context.Context, vpa, name string) (*piPb.PaymentInstrument, error)

	// GetGenericPi fetches PI of type Generic
	// fetches the PI by computed_column -> accountNumber_ifscCode
	GetGenericPi(ctx context.Context, accountNumber, ifscCode, name string) (*piPb.PaymentInstrument, error)

	// DeleteWealthPisByIds deletes the pis for given ids and ownership = EPIFI_WEALTH.
	// NOTE - Max 100 pis can be deleted in one call
	DeleteWealthPisByIds(ctx context.Context, piIds []string) error

	// GetByVpas fetches the PIs corresponding to the give vpas (batch call)
	GetByVpas(ctx context.Context, vpas []string, options ...storagev2.FilterOption) ([]*piPb.PaymentInstrument, error)

	// GetCreditCardPi fetches PI of type CREDIT_CARD by card id
	GetCreditCardPi(ctx context.Context, cardId string) (*piPb.PaymentInstrument, error)

	// GetUpiLitePI fetches PI of type UPI_LITE by lrn (lite reference number)
	GetUpiLitePI(ctx context.Context, lrn string) (*piPb.PaymentInstrument, error)
}

// AccountPiDao defines all the DB operations that can be done on AccountPI entity
type AccountPiDao interface {
	Create(ctx context.Context, accountPI *accountPiPb.AccountPI) (*accountPiPb.AccountPI, error)
	GetByPiId(ctx context.Context, piId string) (*accountPiPb.AccountPI, error)
	GetByAccountId(ctx context.Context, accountId string) ([]*accountPiPb.AccountPI, error)

	// GetByActorId fetches AccountPIs for a given actor
	GetByActorId(ctx context.Context, actorId string) ([]*accountPiPb.AccountPI, error)
}

// Connected Account Account pi dao - defines operations that can be done on AAAccountPI entity
type AAAccountPiDao interface {
	Create(ctx context.Context, accountPI *accountPiPb.AAAccountPI) (*accountPiPb.AAAccountPI, error)
	GetByPiId(ctx context.Context, piId string) (*accountPiPb.AAAccountPI, error)
	GetByAccountId(ctx context.Context, accountId string, accountType accountsPb.Type) ([]*accountPiPb.AAAccountPI, error)
	// DeleteByAccountId deletes the account pi relation for given account id
	DeleteByAccountId(ctx context.Context, accountId string) error
	// GetById fetches account pi for given Id.
	GetById(_ context.Context, id string) (*accountPiPb.AAAccountPI, error)
	// GetByActorId fetches account pis for actor id
	GetByActorId(_ context.Context, actorId string) ([]*accountPiPb.AAAccountPI, error)
}

type PiStatusLogDao interface {
	// Create creates an new mapping between pi and the state log
	Create(ctx context.Context, piStateLog *piPb.PaymentInstrumentStateLog) (*piPb.PaymentInstrumentStateLog, error)
	// GetByPiId returns pi state log list for a given pi id
	GetByPiId(ctx context.Context, piId string) ([]*piPb.PaymentInstrumentStateLog, error)
}

type PiPurgeAuditDao interface {
	// BatchUpsert upserts all the entries to the DB
	BatchUpsert(ctx context.Context, auditEntries []*piPb.PaymentInstrumentPurgeAudit) error
}

type PiLastTransactionDao interface {
	// Get returns the last transaction time for a given pi id and action type
	Get(ctx context.Context, piId string, actionType piPb.TransactionActionType) (*model.PiLastTransaction, error)
	// Upsert upserts the last transaction time for a given pi id and action type
	Upsert(ctx context.Context, lastTransaction *model.PiLastTransaction) error

	// GetPaginatedPisInRange returns the pi ids in order of the most recent last transaction first, upto the limit,
	// for which the last transaction time is in the given range.
	// Limit by default is 1000, and can be upto 10000 i.e. [1,10000]
	GetPaginatedPisInRange(ctx context.Context, lastTransactionAtStart,
		lastTransactionAtEnd time.Time, limit uint32, token *pagination.PageToken) (pis []string,
		pageCtxResp *rpc.PageContextResponse, err error)
}
