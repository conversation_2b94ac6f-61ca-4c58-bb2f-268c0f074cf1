package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/gamma/paymentinstrument/constants"

	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	accountPIPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/paymentinstrument/config/genconf"
)

type AccountPiCache struct {
	AccountPiDao
	cacheStorage cache.CacheStorage
	cacheConfig  *genconf.AccountPiCacheConfig
}

var _ AccountPiDao = &AccountPiCache{}

func NewAccountPiCache(cacheStorage cache.CacheStorage, genConf *genconf.Config, accountPiDaoPgdb *AccountPiDaoPgdb) *AccountPiCache {
	return &AccountPiCache{
		cacheStorage: cacheStorage,
		cacheConfig:  genConf.AccountPiCacheConfig(),
		AccountPiDao: accountPiDaoPgdb,
	}
}

// Create will first delete existing accoutPis from cache and then create a new one in DB (for data consistency)
func (apc *AccountPiCache) Create(ctx context.Context, accountPI *accountPIPb.AccountPI) (*accountPIPb.AccountPI, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "AccountPiCache", "Create", time.Now())
	// first delete existing account pis in cache
	actorId := accountPI.GetActorId()
	err := apc.deleteAccountPiInCache(ctx, actorId)
	if err != nil {
		return nil, fmt.Errorf("error while deleting (actorId : accountPis) list in cache : %s : %w", actorId, err)
	}
	accountId := accountPI.GetAccountId()
	err = apc.deleteAccountPiInCache(ctx, accountId)
	if err != nil {
		return nil, fmt.Errorf("error while deleting (accountId : accountPis) list in cache : %s : %w", accountId, err)
	}
	return apc.AccountPiDao.Create(ctx, accountPI)
}

// GetByPiId will first try to fetch form cache , if cache hit , then return right away
// else , fetch from DB , set in cache and then return the result
func (ap *AccountPiCache) GetByPiId(ctx context.Context, piId string) (*accountPIPb.AccountPI, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "AccountPiCache", "GetByPiId", time.Now())

	accountPi, ok := ap.fetchAccountPiFromCache(ctx, piId)
	if ok {
		logger.Debug(ctx, "cache hit fetch from cache")
		return accountPi, nil
	}
	accountPi, err := ap.AccountPiDao.GetByPiId(ctx, piId)
	if err != nil {
		logger.Debug(ctx, "cache miss - error in fetching from database")
		return nil, err
	}
	logger.Debug(ctx, "cache miss - fetch from database")
	ap.setAccountPiInCache(ctx, accountPi)
	return accountPi, nil
}

// nolint:dupl
// GetByActorId will first try to fetch form cache , if cache hit , then return right away
// else , fetch from DB , set in cache and then return the result
func (apc *AccountPiCache) GetByActorId(ctx context.Context, actorId string) ([]*accountPIPb.AccountPI, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "AccountPiCache", "GetByActorId", time.Now())
	// fetch from cache
	var accountPis []*accountPIPb.AccountPI
	accountPis, ok := apc.fetchAccountPisFromCache(ctx, actorId)
	if ok {
		logger.Debug(ctx, fmt.Sprintf("PI cache hit fetch for actor id %v", actorId))
		return accountPis, nil
	}

	logger.Debug(ctx, fmt.Sprintf("PI cache miss for actor id %v", actorId))

	// fetch from db
	accountPis, err := apc.AccountPiDao.GetByActorId(ctx, actorId)
	if err != nil {
		logger.Debug(ctx, fmt.Sprintf("Error while fetching PIs from database for actor id %v", actorId))
		return nil, err
	}

	if len(accountPis) == 0 {
		logger.Debug(ctx, fmt.Sprintf("No PIs found in db for actor id %v, so not setting anything in cache", actorId))
		return accountPis, nil
	}
	// set in the cache
	apc.setAccountPisInCache(ctx, actorId, accountPis)
	return accountPis, nil
}

// nolint:dupl
// GetByAccountId will first try to fetch form cache , if cache hit , then return right away
// else , fetch from DB , set in cache and then return the result
func (apc *AccountPiCache) GetByAccountId(ctx context.Context, accountId string) ([]*accountPIPb.AccountPI, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "AccountPiCache", "GetByAccountId", time.Now())
	// fetch from cache
	var accountPis []*accountPIPb.AccountPI
	accountPis, ok := apc.fetchAccountPisFromCache(ctx, accountId)
	if ok && len(accountPis) != 0 {
		logger.Debug(ctx, fmt.Sprintf("PI cache hit for account id %v", accountId))
		return accountPis, nil
	}

	logger.Debug(ctx, fmt.Sprintf("PI cache miss for account id %v", accountId))
	// fetch from db
	accountPis, err := apc.AccountPiDao.GetByAccountId(ctx, accountId)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Error while fetching PI from database for account id %v", accountId), zap.Error(err))
		return nil, err
	}
	if len(accountPis) == 0 {
		logger.Debug(ctx, fmt.Sprintf("No PIs found in db for account id %v, so not setting anything in cache", accountId))
		return accountPis, nil
	}

	// set in the cache
	apc.setAccountPisInCache(ctx, accountId, accountPis)
	return accountPis, nil
}

// fetching AccountPi from cache , which is mapped correspoding to the pi ID
// "account_pi_id_" + pi id - AccountPi details
//
//nolint:dupl
func (apc *AccountPiCache) fetchAccountPiFromCache(ctx context.Context, piId string) (*accountPIPb.AccountPI, bool) {
	accountPiDetailsJson, err := apc.cacheStorage.Get(ctx, apc.getCachedKey(piId))
	if err != nil {
		if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "failed to fetch AccountPi from cache", zap.Error(err), zap.String(logger.PI_ID, piId))
		}
		return nil, false
	}
	accountPi := &accountPIPb.AccountPI{}
	err = protojson.Unmarshal([]byte(accountPiDetailsJson), accountPi)
	if err != nil {
		logger.Error(ctx, "failed to unmarshal AccountPi from cache", zap.Error(err), zap.String(logger.PI_ID, piId))
		// Failed to unmarshall value. Delete this from cache (best-effort).
		err = apc.deleteAccountPiInCache(ctx, apc.getCachedKey(piId))
		if err != nil {
			logger.Error(ctx, "error in delete cache", zap.Error(err), zap.String(logger.PI_ID, piId))
		}
		return nil, false
	}
	return accountPi, true
}

// fetchAccountPisFromCache will fetch list of Account Pis corresponding to the given Id from cache
func (apc *AccountPiCache) fetchAccountPisFromCache(ctx context.Context, id string) ([]*accountPIPb.AccountPI, bool) {
	// fetch list of account pis from cache
	stringAccountPis, err := apc.cacheStorage.GetValuesFromSet(ctx, apc.getCachedKey(id))
	if err != nil {
		// error while fetching from cache
		if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "failed to fetch account pis from cache", zap.Error(err), zap.String("cache key", id))
		}
		// cache miss - key not present in the cache
		return nil, false
	}
	// now unmarshal them
	var accountPis []*accountPIPb.AccountPI
	for _, stringAccountPi := range stringAccountPis {
		accountPi := &accountPIPb.AccountPI{}
		err := protojson.Unmarshal([]byte(stringAccountPi), accountPi)
		if err != nil {
			logger.Error(ctx, "failed to unmarshal account Pi  when fetching from cache", zap.Error(err), zap.String("cache key", id))
			// Failed to unmarshall value. Delete this from cache (best-effort).
			err = apc.deleteAccountPiInCache(ctx, id)
			if err != nil {
				logger.Error(ctx, "error while deleting account Pi in cache", zap.Error(err), zap.String("cache key", id))
			}
			return nil, false
		}
		accountPis = append(accountPis, accountPi)
	}
	return accountPis, true
}

// mapping PI Id("account_pi_id_" + pi id) - account Pi details in cache
//
//nolint:dupl
func (apc *AccountPiCache) setAccountPiInCache(ctx context.Context, accountPi *accountPIPb.AccountPI) {
	accountPiBytes, err := protojson.Marshal(accountPi)
	if err != nil {
		logger.Error(ctx, "error while marshaling pi for caching", zap.Error(err))
		return
	}
	cachedaccountPiIdKey := apc.getCachedKey(accountPi.GetPiId())
	err = apc.cacheStorage.Set(ctx, cachedaccountPiIdKey, string(accountPiBytes), apc.cacheConfig.CacheTTl())
	if err != nil {
		logger.Error(ctx, "error while setting account pi in cache", zap.Error(err), zap.String(logger.PI_ID, accountPi.GetPiId()))
		return
	}
}

// setAccountPisInCache will set account Pis (as a list) corresponding to the given Id in cache
func (apc *AccountPiCache) setAccountPisInCache(ctx context.Context, id string, accountPis []*accountPIPb.AccountPI) {
	if len(accountPis) == 0 {
		logger.Debug(ctx, fmt.Sprintf("Empty PI list requested to be cached against account id %v. Doing nothing.", id))
		return
	}
	var stringAccountPis []string
	for _, accountPi := range accountPis {
		accountPiBytes, err := protojson.Marshal(accountPi)
		if err != nil {
			logger.Error(ctx, "error while marshaling account Pi for caching", zap.Error(err), zap.String("cache key", id))
			return
		}
		stringAccountPis = append(stringAccountPis, string(accountPiBytes))
	}
	_, err := apc.cacheStorage.CreateOrAddToSet(ctx, apc.cacheConfig.CacheTTl(), apc.getCachedKey(id), stringAccountPis...)
	if err != nil {
		logger.Error(ctx, "error while setting id : account Pis in cache", zap.Error(err), zap.String("cache key", id))
		return
	}
	logger.Debug(ctx, fmt.Sprintf("Added Pis in cache successfully for id %v", id))
}

// deleteAccountPiInCache will delete the given {id - account pi list} from cache
func (apc *AccountPiCache) deleteAccountPiInCache(ctx context.Context, id string) error {
	cacheKey := apc.getCachedKey(id)
	return apc.cacheStorage.Delete(ctx, cacheKey)
}

// getCachedKey will add AccountPiIdPrefix(string) as prefix
func (apc *AccountPiCache) getCachedKey(id string) string {
	return constants.AccountPiIdPrefix + id
}
