package dao

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/epifi/be-common/api/rpc"
	pkgCmdTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/paymentinstrument/config/genconf"
	"github.com/epifi/gamma/paymentinstrument/dao/model"
)

type PiLastTransactionPgdb struct {
	db *gorm.DB
}

func NewPiLastTransactionPgdb(gconf *genconf.Config, db pkgCmdTypes.PaymentInstrumentPGDB) *PiLastTransactionPgdb {
	return &PiLastTransactionPgdb{db: db}
}

type piLastTransRows []*model.PiLastTransaction

func (o piLastTransRows) Slice(start, end int) pagination.Rows { return o[start:end] }
func (o piLastTransRows) GetTimestamp(index int) time.Time {
	return o[index].LastTransactionAt
}
func (o piLastTransRows) Size() int { return len(o) }

func (p *PiLastTransactionPgdb) Get(ctx context.Context, piId string, actionType piPb.TransactionActionType) (*model.PiLastTransaction, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiLastTransactionPgdb", "Get", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, p.db)
	piLastTrans := &model.PiLastTransaction{}
	qry := db.Model(&model.PiLastTransaction{}).Where("pi_id = ? and action_type = ?", piId, actionType).Take(piLastTrans)

	if qry.Error != nil {
		if storagev2.IsRecordNotFoundError(qry.Error) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, qry.Error
	}
	return piLastTrans, nil
}

func (p *PiLastTransactionPgdb) Upsert(ctx context.Context, lastTransaction *model.PiLastTransaction) error {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiLastTransactionPgdb", "Upsert", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, p.db)
	qry := db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "pi_id"}, {Name: "action_type"}},
		DoUpdates: clause.Assignments(map[string]interface{}{"last_transaction_at": lastTransaction.LastTransactionAt}),
	}).Create(lastTransaction)

	if err := qry.Error; err != nil {
		return fmt.Errorf("failed to upsert last transaction: %w", err)
	}
	return nil
}

func (p *PiLastTransactionPgdb) GetPaginatedPisInRange(ctx context.Context, lastTransactionAtStart,
	lastTransactionAtEnd time.Time, limit uint32, token *pagination.PageToken) (pis []string,
	pageCtxResp *rpc.PageContextResponse, err error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiLastTransactionPgdb", "GetPaginatedPisInRange", time.Now())
	var (
		piLastTrans []*model.PiLastTransaction
		rows        pagination.Rows
	)

	// keeping default value of limit as 1000
	// and keeping max value as 10000
	if limit <= 0 {
		limit = 1000
	} else if limit > 10000 {
		limit = 10000
	}

	db := gormctxv2.FromContextOrDefault(ctx, p.db)
	db = pagination.AddPaginationOnGivenColumn(db, token, limit, "pi_last_transactions", "last_transaction_at")
	qry := db.Model(&model.PiLastTransaction{}).Where("last_transaction_at <= ? and last_transaction_at >= ?",
		lastTransactionAtStart, lastTransactionAtEnd).Order("last_transaction_at desc").Find(&piLastTrans)
	if qry.Error != nil {
		return nil, nil, fmt.Errorf("failed to fetch pi last transactions: %w", qry.Error)
	}
	rows, pageCtxResp, err = pagination.NewPageCtxResp(token, int(limit), piLastTransRows(piLastTrans))
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create new page context: %w", err)
	}
	for _, piLastTran := range rows.(piLastTransRows) {
		pis = append(pis, piLastTran.PiId)
	}
	return pis, pageCtxResp, nil
}
