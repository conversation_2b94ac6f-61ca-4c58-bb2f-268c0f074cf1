package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

type capabilities map[string]bool

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x capabilities) Value() (driver.Value, error) {
	return json.Marshal(x)
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *capabilities) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}

	return json.Unmarshal(marshalledData, &x)
}
