package model

import (
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	gormv2 "gorm.io/gorm"

	piPb "github.com/epifi/gamma/api/paymentinstrument"
)

// PaymentInstrumentPurgeAudit stores audit data for pi purging requests
type PaymentInstrumentPurgeAudit struct {
	PiId    string
	Payload *piPb.PaymentInstrument

	// Standard timestamp fields
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gormv2.DeletedAt
}

func (p *PaymentInstrumentPurgeAudit) ToProto() *piPb.PaymentInstrumentPurgeAudit {
	var deletedAt *timestampPb.Timestamp
	if p.DeletedAt.Valid {
		deletedAt = timestampPb.New(p.DeletedAt.Time)
	}

	return &piPb.PaymentInstrumentPurgeAudit{
		PiId:      p.PiId,
		Payload:   p.Payload,
		CreatedAt: timestampPb.New(p.CreatedAt),
		UpdatedAt: timestampPb.New(p.UpdatedAt),
		DeletedAt: deletedAt,
	}
}

func NewPaymentInstrumentPurgeAudit(proto *piPb.PaymentInstrumentPurgeAudit) *PaymentInstrumentPurgeAudit {
	return &PaymentInstrumentPurgeAudit{
		PiId:    proto.GetPiId(),
		Payload: proto.GetPayload(),
	}
}
