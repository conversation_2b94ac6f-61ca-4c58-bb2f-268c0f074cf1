package model

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	gormv2 "gorm.io/gorm"

	accountsPb "github.com/epifi/gamma/api/accounts"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
)

// AAAccountPI is relationship table between accounts <> PI for connected accounts
type AAAccountPI struct {
	Id string `gorm:"primary_key"`

	// Reference to actor table
	ActorId string

	// Account ID the aa-accounts table
	AccountId string

	// Type of the account eg. savings
	AccountType accountsPb.Type

	// Reference to PI table
	PiId string

	// Standard timestamp fields
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gormv2.DeletedAt
}

func NewAaAccountPiModel(proto *accountPiPb.AAAccountPI) *AAAccountPI {
	return &AAAccountPI{
		ActorId:     proto.GetActorId(),
		AccountId:   proto.GetAccountId(),
		AccountType: proto.GetAccountType(),
		PiId:        proto.GetPiId(),
	}
}

func (a *AAAccountPI) ToProto() *accountPiPb.AAAccountPI {
	proto := &accountPiPb.AAAccountPI{
		Id:          a.Id,
		ActorId:     a.ActorId,
		AccountId:   a.AccountId,
		AccountType: a.AccountType,
		PiId:        a.PiId,
		CreatedAt:   timestamppb.New(a.CreatedAt),
		UpdatedAt:   timestamppb.New(a.UpdatedAt),
	}

	if a.DeletedAt.Valid {
		proto.UpdatedAt = timestamppb.New(a.DeletedAt.Time)
	}
	return proto
}

func NewAaAccountPiProtoList(models []*AAAccountPI) []*accountPiPb.AAAccountPI {
	var (
		protos []*accountPiPb.AAAccountPI
	)

	for _, model := range models {
		protos = append(protos, model.ToProto())
	}
	return protos
}
