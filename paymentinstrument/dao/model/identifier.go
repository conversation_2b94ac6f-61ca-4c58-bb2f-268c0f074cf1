package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"

	piPb "github.com/epifi/gamma/api/paymentinstrument"
)

type Identifier struct {
	Account              *piPb.Account              `json:"account,omitempty"`
	Card                 *piPb.Card                 `json:"card,omitempty"`
	Upi                  *piPb.Upi                  `json:"upi,omitempty"`
	CreditCard           *piPb.CreditCard           `json:"credit_card,omitempty"`
	InternationalAccount *piPb.InternationalAccount `json:"international_account,omitempty"`
	UpiLite              *piPb.UpiLite              `json:"upi_lite,omitempty"`
}

func (a *Identifier) Value() (driver.Value, error) {
	return json.Marshal(a)
}

func (a *Identifier) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}

	return json.Unmarshal(marshalledData, &a)
}
