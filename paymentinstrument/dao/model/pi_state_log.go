package model

import (
	"time"

	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/nulltypes"

	piPb "github.com/epifi/gamma/api/paymentinstrument"
)

// PiStateLog captures the state log for a payment instrument
// captures data like the source app which requested the state change, reason for state change ec.
type PiStateLog struct {
	// id to uniquely identify an entry in the DB
	// column name is id_v2 and not id because there was an issue while migrating table from crdb to pgdb as row_id is not supported in pgdb
	// to solve this, new column was created in crdb with name id_v2, and column with name id was dropped
	// migration job does not support the migration of columns with different names, hence id_v2 is the column name
	IdV2 string `gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`

	// id corresponding to the pi
	PiId string `gorm:"primary_key"`

	// source app which requested the state change
	Source piPb.Source

	// state of the pi when this entry was created
	State piPb.PaymentInstrumentState

	// reason for the state change to the current state
	Reason nulltypes.NullString

	// Standard timestamp fields
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gormv2.DeletedAt
}
