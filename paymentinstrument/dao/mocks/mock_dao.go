// Code generated by MockGen. DO NOT EDIT.
// Source: dao.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	rpc "github.com/epifi/be-common/api/rpc"
	pagination "github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	accounts "github.com/epifi/gamma/api/accounts"
	paymentinstrument "github.com/epifi/gamma/api/paymentinstrument"
	account_pi "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	model "github.com/epifi/gamma/paymentinstrument/dao/model"
	gomock "github.com/golang/mock/gomock"
)

// MockPiDao is a mock of PiDao interface.
type MockPiDao struct {
	ctrl     *gomock.Controller
	recorder *MockPiDaoMockRecorder
}

// MockPiDaoMockRecorder is the mock recorder for MockPiDao.
type MockPiDaoMockRecorder struct {
	mock *MockPiDao
}

// NewMockPiDao creates a new mock instance.
func NewMockPiDao(ctrl *gomock.Controller) *MockPiDao {
	mock := &MockPiDao{ctrl: ctrl}
	mock.recorder = &MockPiDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPiDao) EXPECT() *MockPiDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockPiDao) Create(ctx context.Context, pi *paymentinstrument.PaymentInstrument) (*paymentinstrument.PaymentInstrument, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, pi)
	ret0, _ := ret[0].(*paymentinstrument.PaymentInstrument)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockPiDaoMockRecorder) Create(ctx, pi interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockPiDao)(nil).Create), ctx, pi)
}

// DeleteWealthPisByIds mocks base method.
func (m *MockPiDao) DeleteWealthPisByIds(ctx context.Context, piIds []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteWealthPisByIds", ctx, piIds)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteWealthPisByIds indicates an expected call of DeleteWealthPisByIds.
func (mr *MockPiDaoMockRecorder) DeleteWealthPisByIds(ctx, piIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteWealthPisByIds", reflect.TypeOf((*MockPiDao)(nil).DeleteWealthPisByIds), ctx, piIds)
}

// GetBankAccountPI mocks base method.
func (m *MockPiDao) GetBankAccountPI(ctx context.Context, accountNumber, ifscCode string) (*paymentinstrument.PaymentInstrument, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankAccountPI", ctx, accountNumber, ifscCode)
	ret0, _ := ret[0].(*paymentinstrument.PaymentInstrument)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankAccountPI indicates an expected call of GetBankAccountPI.
func (mr *MockPiDaoMockRecorder) GetBankAccountPI(ctx, accountNumber, ifscCode interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankAccountPI", reflect.TypeOf((*MockPiDao)(nil).GetBankAccountPI), ctx, accountNumber, ifscCode)
}

// GetById mocks base method.
func (m *MockPiDao) GetById(ctx context.Context, id string) (*paymentinstrument.PaymentInstrument, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*paymentinstrument.PaymentInstrument)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockPiDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockPiDao)(nil).GetById), ctx, id)
}

// GetByIds mocks base method.
func (m *MockPiDao) GetByIds(ctx context.Context, ids []string) ([]*paymentinstrument.PaymentInstrument, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByIds", ctx, ids)
	ret0, _ := ret[0].([]*paymentinstrument.PaymentInstrument)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByIds indicates an expected call of GetByIds.
func (mr *MockPiDaoMockRecorder) GetByIds(ctx, ids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByIds", reflect.TypeOf((*MockPiDao)(nil).GetByIds), ctx, ids)
}

// GetByVpas mocks base method.
func (m *MockPiDao) GetByVpas(ctx context.Context, vpas []string, options ...storagev2.FilterOption) ([]*paymentinstrument.PaymentInstrument, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, vpas}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByVpas", varargs...)
	ret0, _ := ret[0].([]*paymentinstrument.PaymentInstrument)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByVpas indicates an expected call of GetByVpas.
func (mr *MockPiDaoMockRecorder) GetByVpas(ctx, vpas interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, vpas}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByVpas", reflect.TypeOf((*MockPiDao)(nil).GetByVpas), varargs...)
}

// GetCreditCardPi mocks base method.
func (m *MockPiDao) GetCreditCardPi(ctx context.Context, cardId string) (*paymentinstrument.PaymentInstrument, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCreditCardPi", ctx, cardId)
	ret0, _ := ret[0].(*paymentinstrument.PaymentInstrument)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCreditCardPi indicates an expected call of GetCreditCardPi.
func (mr *MockPiDaoMockRecorder) GetCreditCardPi(ctx, cardId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCreditCardPi", reflect.TypeOf((*MockPiDao)(nil).GetCreditCardPi), ctx, cardId)
}

// GetDebitCardPi mocks base method.
func (m *MockPiDao) GetDebitCardPi(ctx context.Context, cardNumber, expiry, embossName string) (*paymentinstrument.PaymentInstrument, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDebitCardPi", ctx, cardNumber, expiry, embossName)
	ret0, _ := ret[0].(*paymentinstrument.PaymentInstrument)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDebitCardPi indicates an expected call of GetDebitCardPi.
func (mr *MockPiDaoMockRecorder) GetDebitCardPi(ctx, cardNumber, expiry, embossName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDebitCardPi", reflect.TypeOf((*MockPiDao)(nil).GetDebitCardPi), ctx, cardNumber, expiry, embossName)
}

// GetGenericPi mocks base method.
func (m *MockPiDao) GetGenericPi(ctx context.Context, accountNumber, ifscCode, name string) (*paymentinstrument.PaymentInstrument, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGenericPi", ctx, accountNumber, ifscCode, name)
	ret0, _ := ret[0].(*paymentinstrument.PaymentInstrument)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGenericPi indicates an expected call of GetGenericPi.
func (mr *MockPiDaoMockRecorder) GetGenericPi(ctx, accountNumber, ifscCode, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGenericPi", reflect.TypeOf((*MockPiDao)(nil).GetGenericPi), ctx, accountNumber, ifscCode, name)
}

// GetPartialBankAccountPI mocks base method.
func (m *MockPiDao) GetPartialBankAccountPI(ctx context.Context, accountNumber, ifscCode, name string) (*paymentinstrument.PaymentInstrument, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPartialBankAccountPI", ctx, accountNumber, ifscCode, name)
	ret0, _ := ret[0].(*paymentinstrument.PaymentInstrument)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPartialBankAccountPI indicates an expected call of GetPartialBankAccountPI.
func (mr *MockPiDaoMockRecorder) GetPartialBankAccountPI(ctx, accountNumber, ifscCode, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPartialBankAccountPI", reflect.TypeOf((*MockPiDao)(nil).GetPartialBankAccountPI), ctx, accountNumber, ifscCode, name)
}

// GetPartialUpiPI mocks base method.
func (m *MockPiDao) GetPartialUpiPI(ctx context.Context, vpa, name string) (*paymentinstrument.PaymentInstrument, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPartialUpiPI", ctx, vpa, name)
	ret0, _ := ret[0].(*paymentinstrument.PaymentInstrument)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPartialUpiPI indicates an expected call of GetPartialUpiPI.
func (mr *MockPiDaoMockRecorder) GetPartialUpiPI(ctx, vpa, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPartialUpiPI", reflect.TypeOf((*MockPiDao)(nil).GetPartialUpiPI), ctx, vpa, name)
}

// GetUpiLitePI mocks base method.
func (m *MockPiDao) GetUpiLitePI(ctx context.Context, lrn string) (*paymentinstrument.PaymentInstrument, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUpiLitePI", ctx, lrn)
	ret0, _ := ret[0].(*paymentinstrument.PaymentInstrument)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUpiLitePI indicates an expected call of GetUpiLitePI.
func (mr *MockPiDaoMockRecorder) GetUpiLitePI(ctx, lrn interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpiLitePI", reflect.TypeOf((*MockPiDao)(nil).GetUpiLitePI), ctx, lrn)
}

// GetUpiPI mocks base method.
func (m *MockPiDao) GetUpiPI(ctx context.Context, vpa string) (*paymentinstrument.PaymentInstrument, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUpiPI", ctx, vpa)
	ret0, _ := ret[0].(*paymentinstrument.PaymentInstrument)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUpiPI indicates an expected call of GetUpiPI.
func (mr *MockPiDaoMockRecorder) GetUpiPI(ctx, vpa interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpiPI", reflect.TypeOf((*MockPiDao)(nil).GetUpiPI), ctx, vpa)
}

// UpdatePi mocks base method.
func (m *MockPiDao) UpdatePi(ctx context.Context, pi *paymentinstrument.PaymentInstrument, fieldMask []paymentinstrument.PaymentInstrumentFieldMask, app paymentinstrument.Source, reason string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePi", ctx, pi, fieldMask, app, reason)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePi indicates an expected call of UpdatePi.
func (mr *MockPiDaoMockRecorder) UpdatePi(ctx, pi, fieldMask, app, reason interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePi", reflect.TypeOf((*MockPiDao)(nil).UpdatePi), ctx, pi, fieldMask, app, reason)
}

// MockAccountPiDao is a mock of AccountPiDao interface.
type MockAccountPiDao struct {
	ctrl     *gomock.Controller
	recorder *MockAccountPiDaoMockRecorder
}

// MockAccountPiDaoMockRecorder is the mock recorder for MockAccountPiDao.
type MockAccountPiDaoMockRecorder struct {
	mock *MockAccountPiDao
}

// NewMockAccountPiDao creates a new mock instance.
func NewMockAccountPiDao(ctrl *gomock.Controller) *MockAccountPiDao {
	mock := &MockAccountPiDao{ctrl: ctrl}
	mock.recorder = &MockAccountPiDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccountPiDao) EXPECT() *MockAccountPiDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockAccountPiDao) Create(ctx context.Context, accountPI *account_pi.AccountPI) (*account_pi.AccountPI, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, accountPI)
	ret0, _ := ret[0].(*account_pi.AccountPI)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockAccountPiDaoMockRecorder) Create(ctx, accountPI interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockAccountPiDao)(nil).Create), ctx, accountPI)
}

// GetByAccountId mocks base method.
func (m *MockAccountPiDao) GetByAccountId(ctx context.Context, accountId string) ([]*account_pi.AccountPI, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByAccountId", ctx, accountId)
	ret0, _ := ret[0].([]*account_pi.AccountPI)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByAccountId indicates an expected call of GetByAccountId.
func (mr *MockAccountPiDaoMockRecorder) GetByAccountId(ctx, accountId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByAccountId", reflect.TypeOf((*MockAccountPiDao)(nil).GetByAccountId), ctx, accountId)
}

// GetByActorId mocks base method.
func (m *MockAccountPiDao) GetByActorId(ctx context.Context, actorId string) ([]*account_pi.AccountPI, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorId", ctx, actorId)
	ret0, _ := ret[0].([]*account_pi.AccountPI)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockAccountPiDaoMockRecorder) GetByActorId(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockAccountPiDao)(nil).GetByActorId), ctx, actorId)
}

// GetByPiId mocks base method.
func (m *MockAccountPiDao) GetByPiId(ctx context.Context, piId string) (*account_pi.AccountPI, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByPiId", ctx, piId)
	ret0, _ := ret[0].(*account_pi.AccountPI)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByPiId indicates an expected call of GetByPiId.
func (mr *MockAccountPiDaoMockRecorder) GetByPiId(ctx, piId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByPiId", reflect.TypeOf((*MockAccountPiDao)(nil).GetByPiId), ctx, piId)
}

// MockAAAccountPiDao is a mock of AAAccountPiDao interface.
type MockAAAccountPiDao struct {
	ctrl     *gomock.Controller
	recorder *MockAAAccountPiDaoMockRecorder
}

// MockAAAccountPiDaoMockRecorder is the mock recorder for MockAAAccountPiDao.
type MockAAAccountPiDaoMockRecorder struct {
	mock *MockAAAccountPiDao
}

// NewMockAAAccountPiDao creates a new mock instance.
func NewMockAAAccountPiDao(ctrl *gomock.Controller) *MockAAAccountPiDao {
	mock := &MockAAAccountPiDao{ctrl: ctrl}
	mock.recorder = &MockAAAccountPiDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAAAccountPiDao) EXPECT() *MockAAAccountPiDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockAAAccountPiDao) Create(ctx context.Context, accountPI *account_pi.AAAccountPI) (*account_pi.AAAccountPI, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, accountPI)
	ret0, _ := ret[0].(*account_pi.AAAccountPI)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockAAAccountPiDaoMockRecorder) Create(ctx, accountPI interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockAAAccountPiDao)(nil).Create), ctx, accountPI)
}

// DeleteByAccountId mocks base method.
func (m *MockAAAccountPiDao) DeleteByAccountId(ctx context.Context, accountId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByAccountId", ctx, accountId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByAccountId indicates an expected call of DeleteByAccountId.
func (mr *MockAAAccountPiDaoMockRecorder) DeleteByAccountId(ctx, accountId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByAccountId", reflect.TypeOf((*MockAAAccountPiDao)(nil).DeleteByAccountId), ctx, accountId)
}

// GetByAccountId mocks base method.
func (m *MockAAAccountPiDao) GetByAccountId(ctx context.Context, accountId string, accountType accounts.Type) ([]*account_pi.AAAccountPI, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByAccountId", ctx, accountId, accountType)
	ret0, _ := ret[0].([]*account_pi.AAAccountPI)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByAccountId indicates an expected call of GetByAccountId.
func (mr *MockAAAccountPiDaoMockRecorder) GetByAccountId(ctx, accountId, accountType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByAccountId", reflect.TypeOf((*MockAAAccountPiDao)(nil).GetByAccountId), ctx, accountId, accountType)
}

// GetByActorId mocks base method.
func (m *MockAAAccountPiDao) GetByActorId(arg0 context.Context, actorId string) ([]*account_pi.AAAccountPI, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorId", arg0, actorId)
	ret0, _ := ret[0].([]*account_pi.AAAccountPI)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockAAAccountPiDaoMockRecorder) GetByActorId(arg0, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockAAAccountPiDao)(nil).GetByActorId), arg0, actorId)
}

// GetById mocks base method.
func (m *MockAAAccountPiDao) GetById(arg0 context.Context, id string) (*account_pi.AAAccountPI, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", arg0, id)
	ret0, _ := ret[0].(*account_pi.AAAccountPI)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockAAAccountPiDaoMockRecorder) GetById(arg0, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockAAAccountPiDao)(nil).GetById), arg0, id)
}

// GetByPiId mocks base method.
func (m *MockAAAccountPiDao) GetByPiId(ctx context.Context, piId string) (*account_pi.AAAccountPI, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByPiId", ctx, piId)
	ret0, _ := ret[0].(*account_pi.AAAccountPI)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByPiId indicates an expected call of GetByPiId.
func (mr *MockAAAccountPiDaoMockRecorder) GetByPiId(ctx, piId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByPiId", reflect.TypeOf((*MockAAAccountPiDao)(nil).GetByPiId), ctx, piId)
}

// MockPiStatusLogDao is a mock of PiStatusLogDao interface.
type MockPiStatusLogDao struct {
	ctrl     *gomock.Controller
	recorder *MockPiStatusLogDaoMockRecorder
}

// MockPiStatusLogDaoMockRecorder is the mock recorder for MockPiStatusLogDao.
type MockPiStatusLogDaoMockRecorder struct {
	mock *MockPiStatusLogDao
}

// NewMockPiStatusLogDao creates a new mock instance.
func NewMockPiStatusLogDao(ctrl *gomock.Controller) *MockPiStatusLogDao {
	mock := &MockPiStatusLogDao{ctrl: ctrl}
	mock.recorder = &MockPiStatusLogDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPiStatusLogDao) EXPECT() *MockPiStatusLogDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockPiStatusLogDao) Create(ctx context.Context, piStateLog *paymentinstrument.PaymentInstrumentStateLog) (*paymentinstrument.PaymentInstrumentStateLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, piStateLog)
	ret0, _ := ret[0].(*paymentinstrument.PaymentInstrumentStateLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockPiStatusLogDaoMockRecorder) Create(ctx, piStateLog interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockPiStatusLogDao)(nil).Create), ctx, piStateLog)
}

// GetByPiId mocks base method.
func (m *MockPiStatusLogDao) GetByPiId(ctx context.Context, piId string) ([]*paymentinstrument.PaymentInstrumentStateLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByPiId", ctx, piId)
	ret0, _ := ret[0].([]*paymentinstrument.PaymentInstrumentStateLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByPiId indicates an expected call of GetByPiId.
func (mr *MockPiStatusLogDaoMockRecorder) GetByPiId(ctx, piId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByPiId", reflect.TypeOf((*MockPiStatusLogDao)(nil).GetByPiId), ctx, piId)
}

// MockPiPurgeAuditDao is a mock of PiPurgeAuditDao interface.
type MockPiPurgeAuditDao struct {
	ctrl     *gomock.Controller
	recorder *MockPiPurgeAuditDaoMockRecorder
}

// MockPiPurgeAuditDaoMockRecorder is the mock recorder for MockPiPurgeAuditDao.
type MockPiPurgeAuditDaoMockRecorder struct {
	mock *MockPiPurgeAuditDao
}

// NewMockPiPurgeAuditDao creates a new mock instance.
func NewMockPiPurgeAuditDao(ctrl *gomock.Controller) *MockPiPurgeAuditDao {
	mock := &MockPiPurgeAuditDao{ctrl: ctrl}
	mock.recorder = &MockPiPurgeAuditDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPiPurgeAuditDao) EXPECT() *MockPiPurgeAuditDaoMockRecorder {
	return m.recorder
}

// BatchUpsert mocks base method.
func (m *MockPiPurgeAuditDao) BatchUpsert(ctx context.Context, auditEntries []*paymentinstrument.PaymentInstrumentPurgeAudit) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpsert", ctx, auditEntries)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpsert indicates an expected call of BatchUpsert.
func (mr *MockPiPurgeAuditDaoMockRecorder) BatchUpsert(ctx, auditEntries interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpsert", reflect.TypeOf((*MockPiPurgeAuditDao)(nil).BatchUpsert), ctx, auditEntries)
}

// MockPiLastTransactionDao is a mock of PiLastTransactionDao interface.
type MockPiLastTransactionDao struct {
	ctrl     *gomock.Controller
	recorder *MockPiLastTransactionDaoMockRecorder
}

// MockPiLastTransactionDaoMockRecorder is the mock recorder for MockPiLastTransactionDao.
type MockPiLastTransactionDaoMockRecorder struct {
	mock *MockPiLastTransactionDao
}

// NewMockPiLastTransactionDao creates a new mock instance.
func NewMockPiLastTransactionDao(ctrl *gomock.Controller) *MockPiLastTransactionDao {
	mock := &MockPiLastTransactionDao{ctrl: ctrl}
	mock.recorder = &MockPiLastTransactionDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPiLastTransactionDao) EXPECT() *MockPiLastTransactionDaoMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockPiLastTransactionDao) Get(ctx context.Context, piId string, actionType paymentinstrument.TransactionActionType) (*model.PiLastTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, piId, actionType)
	ret0, _ := ret[0].(*model.PiLastTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockPiLastTransactionDaoMockRecorder) Get(ctx, piId, actionType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockPiLastTransactionDao)(nil).Get), ctx, piId, actionType)
}

// GetPaginatedPisInRange mocks base method.
func (m *MockPiLastTransactionDao) GetPaginatedPisInRange(ctx context.Context, lastTransactionAtStart, lastTransactionAtEnd time.Time, limit uint32, token *pagination.PageToken) ([]string, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaginatedPisInRange", ctx, lastTransactionAtStart, lastTransactionAtEnd, limit, token)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetPaginatedPisInRange indicates an expected call of GetPaginatedPisInRange.
func (mr *MockPiLastTransactionDaoMockRecorder) GetPaginatedPisInRange(ctx, lastTransactionAtStart, lastTransactionAtEnd, limit, token interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaginatedPisInRange", reflect.TypeOf((*MockPiLastTransactionDao)(nil).GetPaginatedPisInRange), ctx, lastTransactionAtStart, lastTransactionAtEnd, limit, token)
}

// Upsert mocks base method.
func (m *MockPiLastTransactionDao) Upsert(ctx context.Context, lastTransaction *model.PiLastTransaction) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upsert", ctx, lastTransaction)
	ret0, _ := ret[0].(error)
	return ret0
}

// Upsert indicates an expected call of Upsert.
func (mr *MockPiLastTransactionDaoMockRecorder) Upsert(ctx, lastTransaction interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockPiLastTransactionDao)(nil).Upsert), ctx, lastTransaction)
}
