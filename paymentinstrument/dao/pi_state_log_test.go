package dao_test

import (
	"context"
	"reflect"
	"testing"

	gormv2 "gorm.io/gorm"

	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/paymentinstrument/config/genconf"
	"github.com/epifi/gamma/paymentinstrument/dao"
)

type piStateLogTestSuite struct {
	db             *gormv2.DB
	conf           *genconf.Config
	piStateLogPgdb *dao.PiStateLogDaoPGDB
}

var (
	piStateLogsAffectedTables = []string{"payment_instruments", "pi_state_logs"}
)

func assertPiStateLog(got, want *piPb.PaymentInstrumentStateLog) bool {
	if (got == nil) != (want == nil) {
		return false
	}
	if got == nil {
		return true
	}
	want.CreatedAt = got.CreatedAt
	want.UpdatedAt = got.UpdatedAt
	want.DeletedAt = got.DeletedAt
	want.IdV2 = got.IdV2
	return reflect.DeepEqual(got, want)
}

func assertPiStateLogList(got, want []*piPb.PaymentInstrumentStateLog) bool {
	if len(got) != len(want) {
		return false
	}
	for index := range got {
		if !assertPiStateLog(got[index], want[index]) {
			return false
		}
	}
	return true
}

func TestPiStateLogPgdb_Create(t *testing.T) {
	testSuite, testSuiteReleaseFunc := getPiStateLogTestSuite(t)
	testSuiteReleaseFunc()
	tests := []struct {
		name    string
		req     *piPb.PaymentInstrumentStateLog
		want    *piPb.PaymentInstrumentStateLog
		wantErr bool
	}{
		{
			name: "State log creation failed because of foreign key constraints",
			req: &piPb.PaymentInstrumentStateLog{
				PiId: "pi-random",
			},
			wantErr: true,
		},
		{
			name: "State log creation successful",
			req: &piPb.PaymentInstrumentStateLog{
				PiId:   "pi-1",
				Source: piPb.Source_SYSTEM,
				Reason: "Test reason",
				State:  piPb.PaymentInstrumentState_CREATED,
			},
			want: &piPb.PaymentInstrumentStateLog{
				PiId:   "pi-1",
				Source: piPb.Source_SYSTEM,
				Reason: "Test reason",
				State:  piPb.PaymentInstrumentState_CREATED,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := testSuite.piStateLogPgdb.Create(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() got err: %v, want: %v", err, tt.wantErr)
				return
			}
			if !assertPiStateLog(got, tt.want) {
				t.Errorf("Create() got: %v, want: %v", got, tt.want)
				return
			}
		})
	}
}

func TestPiStateLogPgdb_GetByPiId(t *testing.T) {
	testSuite, testSuiteReleaseFunc := getPiStateLogTestSuite(t)
	testSuiteReleaseFunc()
	tests := []struct {
		name    string
		req     string
		want    []*piPb.PaymentInstrumentStateLog
		wantErr bool
	}{
		{
			name: "Got a list of payment instrument state log successfully",
			req:  "pi-1",
			want: []*piPb.PaymentInstrumentStateLog{
				{
					PiId:   "pi-1",
					Source: piPb.Source_SYSTEM,
					State:  piPb.PaymentInstrumentState_CREATED,
				},
				{
					PiId:   "pi-1",
					Source: piPb.Source_EPIFI_USER,
					State:  piPb.PaymentInstrumentState_SUSPENDED,
					Reason: "Suspended by user",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := testSuite.piStateLogPgdb.GetByPiId(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByPiId() got err: %v, want: %v", err, tt.wantErr)
				return
			}
			if !assertPiStateLogList(got, tt.want) {
				t.Errorf("GetByPiId() got: %v, want: %v", got, tt.want)
				return
			}
		})
	}
}
