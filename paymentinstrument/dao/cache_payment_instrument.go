package dao

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/paymentinstrument/config/genconf"
	"github.com/epifi/gamma/paymentinstrument/constants"
)

// PaymentInstrumentCache embedding PiDaoCrdb
type PiCache struct {
	PiDao
	cacheStorage cache.CacheStorage
	cacheConfig  *genconf.PiCacheConfig
}

// to make sure piCache implements piDao at compile time
var _ PiDao = &PiCache{}

func NewPiCache(cacheStorage cache.CacheStorage, conf *genconf.Config, piDoaPgdb *PiDaoPgdb) *PiCache {
	return &PiCache{
		cacheStorage: cacheStorage,
		cacheConfig:  conf.PiCacheConfig(),
		PiDao:        piDoaPgdb,
	}
}

// PI id is primary key which will store PI details in cache.
// All other ids are secondary and will store Pi Id in cache.
// We store PI details against PI id in cache. Cache key will be  {"payment_instrument_id_" + PI id}.
// GetById will first try to fetch from cache, if no entry found in cache then it will fetch data from crdb and  set the value in cache before returning to caller function.
func (piC *PiCache) GetById(ctx context.Context, id string) (*piPb.PaymentInstrument, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiCache", "GetById", time.Now())
	// fetch from cache
	pi, ok := piC.fetchPiFromCache(ctx, id)
	if ok {
		return pi, nil
	}
	// fetch PI from crdb
	pi, err := piC.PiDao.GetById(ctx, id)
	if err != nil {
		return nil, err
	}
	// set cache with ("payment_instrument_id_" + PI id) to Pi details
	piC.setPiInCache(ctx, pi)
	return pi, nil
}

// GetByIds returns an UNORDERED list of PIs for a given list of PI ids

func (piC *PiCache) GetByIds(ctx context.Context, ids []string) ([]*piPb.PaymentInstrument, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiCache", "GetByIds", time.Now())
	if len(ids) == 0 {
		logger.Debug(ctx, "Got empty piIDList. Not looking up the cache.")
		return make([]*piPb.PaymentInstrument, 0, 0), nil
	}

	// fetch from cache
	piListFromCache, piIdsToBeFetchedFromDB := piC.fetchPiListFromCache(ctx, ids)

	if len(piIdsToBeFetchedFromDB) == 0 {
		return piListFromCache, nil
	}

	// fetch PI not found in cache from Crdb
	piListFromDB, err := piC.PiDao.GetByIds(ctx, piIdsToBeFetchedFromDB)
	if err != nil {
		return nil, err
	}

	piList := append(piListFromCache, piListFromDB...)

	// set not found PIs in cache
	if len(piListFromDB) > 0 {
		piC.setPiListInCache(ctx, piListFromDB)
	}

	return piList, nil
}

// GetBankAccountPI will first try to fetch PI ID from cache against secondary key {prefix + accountNo + ifsc}.
// If PI ID is found then it will try to fetch PI using GetById.
// If no cache present against secondary key, then it will fetch PI details from CRDB and set secondary cache key with PI ID and primary cache key {prefix + PI id} with PI details.
// Note: secondary cache key here is different from other DAOs.
func (piC *PiCache) GetBankAccountPI(ctx context.Context, accountNumber, ifscCode string) (*piPb.PaymentInstrument, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiCache", "GetBankAccountPI", time.Now())

	// fetch Pi Id from cache
	piId, ok := piC.fetchPiIdFromCache(ctx, piC.getBankAccountPICacheKey(accountNumber, ifscCode))
	if ok {
		return piC.GetById(ctx, piId)
	}
	// fetch PI from crdb
	pi, err := piC.PiDao.GetBankAccountPI(ctx, accountNumber, ifscCode)
	if err != nil {
		return nil, err
	}
	// set PiId to Pi details in cache
	piC.setPiInCache(ctx, pi)

	piC.setSecondaryKeyToPiIdMappingInCache(ctx, piC.getBankAccountPICacheKey(accountNumber, ifscCode), pi.GetId())
	return pi, nil
}

// GetPartialBankAccountPI will first try to fetch PI ID from cache against secondary key {prefix + accountNo + ifsc + name}.
// If PI ID is found then it will try to fetch PI using GetById.
// If no cache present against secondary key, then it will fetch PI details from CRDB and set secondary cache key with PI ID and primary cache key {prefix + PI id} with PI details.
// Note: secondary cache key here is different from other DAOs.
func (piC *PiCache) GetPartialBankAccountPI(ctx context.Context, accountNumber, ifscCode, name string) (*piPb.PaymentInstrument, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiCache", "GetPartialBankAccountPI", time.Now())
	if accountNumber == "" || ifscCode == "" || name == "" {
		return nil, epifierrors.ErrInvalidArgument
	}

	// fetch Pi Id from cache
	piId, ok := piC.fetchPiIdFromCache(ctx, piC.getPartialBankAccountPICacheKey(accountNumber, ifscCode, name))
	if ok {
		return piC.GetById(ctx, piId)
	}
	// fetch Pi from crdb
	pi, err := piC.PiDao.GetPartialBankAccountPI(ctx, accountNumber, ifscCode, name)
	if err != nil {
		return nil, err
	}
	// set PiId to Pi details in cache
	piC.setPiInCache(ctx, pi)

	piC.setSecondaryKeyToPiIdMappingInCache(ctx, piC.getPartialBankAccountPICacheKey(accountNumber, ifscCode, name), pi.GetId())
	return pi, nil
}

// GetPartialUpiPI will first try to fetch PI ID from cache against secondary key {prefix + vpa + name}.
// If PI ID is found then it will try to fetch PI using GetById.
// If no cache present against secondary key, then it will fetch PI details from CRDB and set secondary cache key with PI ID and primary cache key {prefix + PI id} with PI details.
// Note: secondary cache key here is different from other DAOs.
func (piC *PiCache) GetPartialUpiPI(ctx context.Context, vpa, name string) (*piPb.PaymentInstrument, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiCache", "GetPartialUpiPI", time.Now())
	if vpa == "" || name == "" {
		return nil, epifierrors.ErrInvalidArgument
	}
	// fetch Pi Id from cache
	piId, ok := piC.fetchPiIdFromCache(ctx, piC.getPartialUpiPICacheKey(vpa, name))
	if ok {
		return piC.GetById(ctx, piId)
	}
	// fetch PI from crdb
	pi, err := piC.PiDao.GetPartialUpiPI(ctx, vpa, name)
	if err != nil {
		return nil, err
	}
	// set PiId to Pi details in cache
	piC.setPiInCache(ctx, pi)

	piC.setSecondaryKeyToPiIdMappingInCache(ctx, piC.getPartialUpiPICacheKey(vpa, name), pi.GetId())
	return pi, nil
}

// GetGenericPi will first try to fetch PI ID from cache against secondary key {prefix + accountNo + ifsc + name}.
// If PI ID is found then it will try to fetch PI using GetById.
// If no cache present against secondary key, then it will fetch PI details from CRDB and set secondary cache key with PI ID and primary cache key {prefix + PI id} with PI details.
// Note: secondary cache key here is different from other DAOs.
func (piC *PiCache) GetGenericPi(ctx context.Context, accountNumber, ifscCode, name string) (*piPb.PaymentInstrument, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiCache", "GetGenericPi", time.Now())
	if accountNumber == "" || ifscCode == "" || name == "" {
		return nil, epifierrors.ErrInvalidArgument
	}
	// fetch PiId from cache
	piId, ok := piC.fetchPiIdFromCache(ctx, piC.getGenericPiCacheKey(accountNumber, ifscCode, name))
	if ok {
		return piC.GetById(ctx, piId)
	}
	// fetch PI from crdb
	pi, err := piC.PiDao.GetGenericPi(ctx, accountNumber, ifscCode, name)
	if err != nil {
		return nil, err
	}
	// set PiId to Pi details in cache
	piC.setPiInCache(ctx, pi)

	piC.setSecondaryKeyToPiIdMappingInCache(ctx, piC.getGenericPiCacheKey(accountNumber, ifscCode, name), pi.GetId())
	return pi, nil
}

// GetUpiPI will first try to fetch PI ID from cache against secondary key {prefix + vpa}.
// If PI ID is found then it will try to fetch PI using GetById.
// If no cache present against secondary key, then it will fetch PI details from CRDB and set secondary cache key with PI ID and primary cache key {prefix + PI id} with PI details.
// Note: secondary cache key here is different from other DAOs.
func (piC *PiCache) GetUpiPI(ctx context.Context, vpa string) (*piPb.PaymentInstrument, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiCache", "GetUpiPI", time.Now())

	// fetch PiId from cache
	piId, ok := piC.fetchPiIdFromCache(ctx, piC.getUpiPICacheKey(vpa))
	if ok {
		return piC.GetById(ctx, piId)
	}
	// fetch PI from crdb
	pi, err := piC.PiDao.GetUpiPI(ctx, vpa)
	if err != nil {
		return nil, err
	}
	// set PiId - Pi in cache
	piC.setPiInCache(ctx, pi)

	piC.setSecondaryKeyToPiIdMappingInCache(ctx, piC.getUpiPICacheKey(vpa), pi.GetId())
	return pi, nil
}

// GetUpiLitePI will first try to fetch PI ID from cache against secondary key {prefix + lrn}.
// If PI ID is found then it will try to fetch PI using GetById.
// If no cache present against secondary key, then it will fetch PI details from CRDB and set secondary cache key with PI ID and primary cache key {prefix + PI id} with PI details.
// Note: secondary cache key here is different from other DAOs.
func (piC *PiCache) GetUpiLitePI(ctx context.Context, lrn string) (*piPb.PaymentInstrument, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiCache", "GetUpiLitePI", time.Now())

	// lrn should not be empty
	if lrn == "" {
		return nil, epifierrors.ErrInvalidArgument
	}

	// fetch PiId from cache
	piId, ok := piC.fetchPiIdFromCache(ctx, piC.getUpiLitePICacheKey(lrn))
	if ok {
		return piC.GetById(ctx, piId)
	}
	// fetch PI from crdb
	pi, err := piC.PiDao.GetUpiLitePI(ctx, lrn)
	if err != nil {
		return nil, err
	}
	// set PiId - Pi in cache
	piC.setPiInCache(ctx, pi)

	piC.setSecondaryKeyToPiIdMappingInCache(ctx, piC.getUpiLitePICacheKey(lrn), pi.GetId())
	return pi, nil
}

// in all the write methods, first deleting from cache and then from db to have data consistency
// returning the error to service layer if deletion in cache is failed
// db is only updated when deletion from cache is successful
func (piC *PiCache) UpdatePi(ctx context.Context, pi *piPb.PaymentInstrument,
	fieldMask []piPb.PaymentInstrumentFieldMask, app piPb.Source, reason string) error {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiCache", "UpdatePi", time.Now())
	piId := pi.GetId()
	if piId == "" {
		return fmt.Errorf("primary identifier can't be empty for an update operation")
	}
	// evict from cache
	err := piC.deletePiInCache(ctx, piC.getPiCacheKey(pi.GetId()))
	if err != nil {
		return fmt.Errorf("error while deleting pi in cache: %s , %w", piId, err)
	}
	return piC.PiDao.UpdatePi(ctx, pi, fieldMask, app, reason)
}

// first deleting from cache , then in crdb for data consistency
func (piC *PiCache) DeleteWealthPisByIds(ctx context.Context, piIds []string) error {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PiCache", "DeleteWealthPisByIds", time.Now())
	if len(piIds) == 0 {
		return fmt.Errorf("len of pi ids can't be zero: %w", epifierrors.ErrInvalidArgument)
	}
	// append the prefix to the piIds
	cachePiIds := make([]string, len(piIds))
	for ind := range piIds {
		cachePiIds[ind] = piC.getPiCacheKey(piIds[ind])
	}
	// delete the piIds from cache
	err := piC.deletePiInCache(ctx, cachePiIds...)
	if err != nil {
		return fmt.Errorf("error while deleting pi in cache: %w", err)
	}
	// delete piIds from crdb
	return piC.PiDao.DeleteWealthPisByIds(ctx, piIds)
}

// fetching PI from cache , which is mapped correspoding to the PI ID
// "payment_instrument_id_" + PI id - Pi details
//
//nolint:dupl
func (piC *PiCache) fetchPiFromCache(ctx context.Context, piId string) (*piPb.PaymentInstrument, bool) {
	pIDetailsJson, err := piC.cacheStorage.Get(ctx, piC.getPiCacheKey(piId))
	if err != nil {
		if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "failed to fetch PI from cache", zap.Error(err), zap.String(logger.PI_ID, piId))
		}
		return nil, false
	}
	pi := &piPb.PaymentInstrument{}
	err = proto.Unmarshal([]byte(pIDetailsJson), pi)
	if err != nil {
		logger.Error(ctx, "failed to unmarshal PI from cache", zap.Error(err), zap.String(logger.PI_ID, piId))
		// Failed to unmarshall value. Delete this from cache (best-effort).
		err = piC.deletePiInCache(ctx, piC.getPiCacheKey(piId))
		if err != nil {
			logger.Error(ctx, "error in delete cache", zap.Error(err), zap.String(logger.PI_ID, piId))
		}
		return nil, false
	}
	return pi, true
}

// fetching List of PI from cache given the List of PI Ids
func (piC *PiCache) fetchPiListFromCache(ctx context.Context, piIdList []string) ([]*piPb.PaymentInstrument, []string) {

	piFromCacheList := make([]*piPb.PaymentInstrument, 0, len(piIdList))
	piIdNotInCacheList := make([]string, 0, len(piIdList))

	if len(piIdList) == 0 {
		logger.Debug(ctx, "Got empty piIDList. Not calling cache storage.")
		return piFromCacheList, piIdNotInCacheList
	}

	// create list of cache keys
	piCacheKeyList := make([]string, 0, len(piIdList))

	for _, piId := range piIdList {
		piCacheKeyList = append(piCacheKeyList, piC.getPiCacheKey(piId))
	}

	// get values for all keys
	marshalledPiList, err := piC.cacheStorage.MultiGet(ctx, piCacheKeyList)
	if err != nil {
		if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "failed to fetch PI from cache", zap.Error(err), zap.String(logger.PI_ID_LIST, strings.Join(piIdList, ", ")))
		}
		return []*piPb.PaymentInstrument{}, piIdList
	}

	// deserialize strings to PaymentInstrument objects
	for i, marshalledPi := range marshalledPiList {
		if marshalledPi == "" {
			// if a key is cache miss, appending it to piIdNotInCacheList so that they can be bulk fetched from database later
			piIdNotInCacheList = append(piIdNotInCacheList, piIdList[i])
			continue
		}

		pi := &piPb.PaymentInstrument{}
		unmarshalErr := proto.Unmarshal([]byte(marshalledPi), pi)
		if unmarshalErr != nil {
			logger.Error(ctx, "error in unmarshalling PI in cache", zap.Error(unmarshalErr), zap.String(logger.PI_ID, piIdList[i]))
			// deleting PI from cache
			deleteInCacheError := piC.cacheStorage.Delete(ctx, piCacheKeyList[i])
			if deleteInCacheError != nil {
				logger.Error(ctx, "error in deleting PI in cache", zap.Error(deleteInCacheError), zap.String(logger.PI_ID, piIdList[i]))
			}
			piIdNotInCacheList = append(piIdNotInCacheList, piIdList[i])
			continue
		}

		piFromCacheList = append(piFromCacheList, pi)
	}
	return piFromCacheList, piIdNotInCacheList
}

// mapping PI Id("payment_instrument_id_" + PI id) - PI details in cache
//
//nolint:dupl
func (piC *PiCache) setPiInCache(ctx context.Context, instrument *piPb.PaymentInstrument) {
	piBytes, err := proto.Marshal(instrument)
	if err != nil {
		logger.Error(ctx, "error while marshaling pi for caching", zap.Error(err))
		return
	}
	cachedPiIdKey := piC.getPiCacheKey(instrument.GetId())
	err = piC.cacheStorage.Set(ctx, cachedPiIdKey, string(piBytes), piC.cacheConfig.CacheTTl())
	if err != nil {
		logger.Error(ctx, "error while setting pi in cache", zap.Error(err), zap.String(logger.PI_ID, instrument.GetId()))
		return
	}
}

func (piC *PiCache) setPiListInCache(ctx context.Context, piList []*piPb.PaymentInstrument) {

	for _, pi := range piList {
		piC.setPiInCache(ctx, pi)
	}
}

// deleting PI Id("payment_instrument_id_" + PI id) - PI details(value) mapping in cache
func (piC *PiCache) deletePiInCache(ctx context.Context, piIds ...string) error {
	return piC.cacheStorage.Delete(ctx, piIds...)
}

// adding the constant string as prefix("payment_instrument_id_") to the PI Id
func (piC *PiCache) getPiCacheKey(id string) string {
	return constants.PiIdPrefix + id
}

// fetching the PI id from cache, which is mapped corresponding to secondary Ids
// secondary key(key) : Pi Id(value)
func (piC *PiCache) fetchPiIdFromCache(ctx context.Context, secondaryKey string) (string, bool) {
	id, err := piC.cacheStorage.Get(ctx, secondaryKey)
	if err != nil {
		if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "failed to fetch id from cache", zap.Error(err))
		}
		return "", false
	}
	return id, true
}

// setSecondaryKeyToPiIdMappingInCache maps secondary key(cacheKey) : PI Id(value) in cache
func (piC *PiCache) setSecondaryKeyToPiIdMappingInCache(ctx context.Context, secondaryKey, id string) {
	err := piC.cacheStorage.Set(ctx, secondaryKey, id, piC.cacheConfig.CacheTTl())
	if err != nil {
		logger.Error(ctx, "error while mapping secondary key to instrument id in Cache", zap.Error(err))
	}
}

func (piC *PiCache) getBankAccountPICacheKey(cacheKeyElements ...string) string {
	return constants.BankAccountPiPrefix + strings.Join(cacheKeyElements, "")
}

func (piC *PiCache) getPartialBankAccountPICacheKey(cacheKeyElements ...string) string {
	return constants.PartialBankAccountPiPrefix + strings.Join(cacheKeyElements, "")
}

func (piC *PiCache) getPartialUpiPICacheKey(cacheKeyElements ...string) string {
	return constants.PartialUpiPiPrefix + strings.Join(cacheKeyElements, "")
}

func (piC *PiCache) getGenericPiCacheKey(cacheKeyElements ...string) string {
	return constants.GenericPiPrefix + strings.Join(cacheKeyElements, "")
}

func (piC *PiCache) getUpiPICacheKey(cacheKeyElements ...string) string {
	return constants.UpiPiPrefix + strings.Join(cacheKeyElements, "")
}

func (piC *PiCache) getUpiLitePICacheKey(cacheKeyElements ...string) string {
	return constants.UpiLitePiPrefix + strings.Join(cacheKeyElements, "")
}
