package dao_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/paymentinstrument/config/genconf"
	"github.com/epifi/gamma/paymentinstrument/dao"
	"github.com/epifi/gamma/paymentinstrument/dao/model"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
)

type piLastTransTS struct {
	db      *gorm.DB
	dao     *dao.PiLastTransactionPgdb
	genconf *genconf.Config
}

var (
	piLastTransAffectedTable = []string{}
)

func TestPiLastTransDao_Upsert(t *testing.T) {
	var (
		piIdFix                  = "piIdFix"
		actionTypeFix            = piPb.TransactionActionType_TRANSACTION_ACTION_TYPE_CREDIT
		lastTransactionAtFix     = time.Now().Add(-1 * time.Hour)
		lastTransactionUpdateFix = time.Now()
		piLastTransFix           = &model.PiLastTransaction{
			PiId:              piIdFix,
			ActionType:        actionTypeFix,
			LastTransactionAt: lastTransactionAtFix,
		}
		piLastTransUpdateFix = &model.PiLastTransaction{
			PiId:              piIdFix,
			ActionType:        actionTypeFix,
			LastTransactionAt: lastTransactionUpdateFix,
		}
	)
	testSuite, testSuiteReleaseFunc := getPiLastTransTS(t)
	defer testSuiteReleaseFunc()
	tests := []struct {
		name       string
		piLastTran *model.PiLastTransaction
		wantErr    assert.ErrorAssertionFunc
	}{
		{
			name:       "new insert",
			piLastTran: piLastTransFix,
			wantErr:    assert.NoError,
		},
		{
			name:       "update",
			piLastTran: piLastTransUpdateFix,
			wantErr:    assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := testSuite.dao
			err := p.Upsert(context.Background(), tt.piLastTran)
			tt.wantErr(t, err, fmt.Sprintf("Upsert(%v, %v)", "ctx", tt.piLastTran))
			lastTrans, getErr := p.Get(context.Background(), tt.piLastTran.PiId, tt.piLastTran.ActionType)
			assert.NoError(t, getErr)
			assert.Equal(t, lastTrans.LastTransactionAt.Unix(), tt.piLastTran.LastTransactionAt.Unix())
		})
	}
}

func getOrderedPiLT(n int) *model.PiLastTransaction {
	piIdPrefix := "piId"
	randPiId := fmt.Sprintf("%s%d", piIdPrefix, n)
	timeNow := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	randLastTransactionAt := timeNow.Add(time.Duration(n) * time.Second)
	return &model.PiLastTransaction{
		PiId:              randPiId,
		ActionType:        piPb.TransactionActionType_TRANSACTION_ACTION_TYPE_DEBIT,
		LastTransactionAt: randLastTransactionAt,
	}
}

func populateOrderedPis(piltsDao dao.PiLastTransactionDao, n int) {
	for i := 0; i < n; i++ {
		pilt := getOrderedPiLT(i)
		err := piltsDao.Upsert(context.Background(), pilt)
		logger.InfoNoCtx(fmt.Sprintf("upserting %v, %v, %v", pilt.PiId, pilt.ActionType, pilt.LastTransactionAt))
		if err != nil {
			logger.ErrorNoCtx("error while populating pi last transactions fixtures", zap.Error(err))
		}
	}
}

func TestPiLastTransDao_GetPaginatedPisInRange(t *testing.T) {
	var (
		timeNow        = time.Date(2024, 1, 2, 0, 0, 0, 0, time.UTC)
		time1DayAgoFix = time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	)
	testSuite, testSuiteReleaseFunc := getPiLastTransTS(t)
	defer testSuiteReleaseFunc()
	populateOrderedPis(testSuite.dao, 5)
	type args struct {
		lastTransactionAtStart time.Time
		lastTransactionAtEnd   time.Time
		limit                  uint32
		token                  *pagination.PageToken
	}
	tests := []struct {
		name            string
		args            args
		wantPis         []string
		wantPageCtxResp func() *rpc.PageContextResponse
		wantErr         assert.ErrorAssertionFunc
	}{
		{
			name: "get all pis in range",
			args: args{
				lastTransactionAtStart: timeNow,
				lastTransactionAtEnd:   time1DayAgoFix,
				limit:                  5,
				token:                  nil,
			},
			wantPis: []string{
				"piId4",
				"piId3",
				"piId2",
				"piId1",
				"piId0",
			},
			wantPageCtxResp: func() *rpc.PageContextResponse { return &rpc.PageContextResponse{} },
			wantErr:         assert.NoError,
		},
		{
			name: "get fewer pis to continue in next page",
			args: args{
				lastTransactionAtStart: timeNow,
				lastTransactionAtEnd:   time1DayAgoFix,
				limit:                  2,
				token:                  nil,
			},
			wantPis: []string{
				"piId4",
				"piId3",
			},
			wantPageCtxResp: func() *rpc.PageContextResponse {
				afterToken := &pagination.PageToken{
					Timestamp: timestampPb.New(time1DayAgoFix.Add(3 * time.Second)),
					Offset:    1,
					IsReverse: false,
				}
				afterTokenStr, _ := afterToken.Marshal()
				return &rpc.PageContextResponse{
					AfterToken: afterTokenStr,
					HasAfter:   true,
				}
			},
			wantErr: assert.NoError,
		},
		{
			name: "get pis with page token",
			args: args{
				lastTransactionAtStart: timeNow,
				lastTransactionAtEnd:   time1DayAgoFix,
				limit:                  2,
				token: &pagination.PageToken{
					Timestamp: timestampPb.New(time1DayAgoFix.Add(3 * time.Second)),
					Offset:    1,
					IsReverse: false},
			},
			wantPis: []string{
				"piId2",
				"piId1",
			},
			wantPageCtxResp: func() *rpc.PageContextResponse {
				afterToken := &pagination.PageToken{
					Timestamp: timestampPb.New(time1DayAgoFix.Add(1 * time.Second)),
					Offset:    1,
					IsReverse: false,
				}
				afterTokenStr, _ := afterToken.Marshal()
				beforeToken := &pagination.PageToken{
					Timestamp: timestampPb.New(time1DayAgoFix.Add(2 * time.Second)),
					Offset:    1,
					IsReverse: true,
				}
				beforeTokenStr, _ := beforeToken.Marshal()
				return &rpc.PageContextResponse{
					AfterToken:  afterTokenStr,
					HasAfter:    true,
					BeforeToken: beforeTokenStr,
					HasBefore:   true,
				}
			},
			wantErr: assert.NoError,
		},
		{
			name: "get no pis in range than limit",
			args: args{
				lastTransactionAtStart: timeNow.Add(2 * time.Hour),
				lastTransactionAtEnd:   timeNow.Add(1 * time.Hour),
				limit:                  2,
				token:                  nil,
			},
			wantPis:         nil,
			wantPageCtxResp: func() *rpc.PageContextResponse { return &rpc.PageContextResponse{} },
			wantErr:         assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			gotPis, gotPageCtx, err := testSuite.dao.GetPaginatedPisInRange(ctx,
				tt.args.lastTransactionAtStart, tt.args.lastTransactionAtEnd, tt.args.limit, tt.args.token)
			if !tt.wantErr(t, err, fmt.Sprintf("GetPIsInRange(%v, %v, %v)", "ctx",
				tt.args.lastTransactionAtStart, tt.args.lastTransactionAtEnd)) {
				return
			}
			if diff := cmp.Diff(tt.wantPis, gotPis); diff != "" {
				t.Errorf("GetPIsInRange() mismatch (-want +got):\n%s", diff)
			}
			if diff := cmp.Diff(tt.wantPageCtxResp(), gotPageCtx, protocmp.Transform()); diff != "" {
				t.Errorf("GetPaginatedTransactions() got %v, want %v, diff %v", gotPageCtx, tt.wantPageCtxResp(), diff)
			}
		})
	}
}
