// nolint: goimports
package dao_test

import (
	"flag"
	"log"
	"os"
	"testing"

	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/cfg"
	serverCfg "github.com/epifi/be-common/pkg/cmd/config"
	genconf2 "github.com/epifi/be-common/pkg/cmd/config/genconf"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/pkg/storage/v2/usecase"
	testPkg "github.com/epifi/be-common/pkg/test/v2"

	"github.com/epifi/gamma/paymentinstrument/config"
	"github.com/epifi/gamma/paymentinstrument/config/genconf"
	"github.com/epifi/gamma/paymentinstrument/dao"
	"github.com/epifi/gamma/paymentinstrument/test"
)

var (
	dbNameToDbInstancePoolMap = make(map[string]testPkg.DbInstancePool)
	conf                      *config.Config
	genConf                   *genconf.Config
	gnconf                    *genconf2.Config
	teardown                  func()
	idg                       = idgen.NewDomainIdGenerator(idgen.NewClock())
	dbResourceProviderPool    testPkg.DbResourceProviderInstancePool
	cleanup                   func()
	ownershipToDbConfigMap    map[commontypes.Ownership]*cfg.DB
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var err error
	conf, _, _, teardown = test.InitTestServer()
	gnconf, err = genconf2.Load(cfg.ACTOR_SERVER)
	if err != nil {
		log.Fatal("failed to load config", err)
	}
	genConf, err = genconf.Load()
	if err != nil {
		log.Fatal("failed to load config", err)
	}
	dbResourceProviderPool, cleanup = InitConfigAndDBResourceProviderInstancePool()
	defer cleanup()

	dbInstancePool := testPkg.NewPgdbDbInstancePool(testPkg.NewZapLogger(logger.Log), conf.PaymentInstrumentDb, 1)

	dbNameToDbInstancePoolMap[conf.PaymentInstrumentDb.GetName()] = dbInstancePool
	exitCode := m.Run()
	dbInstancePool.Cleanup(testPkg.NewZapLogger(logger.Log))
	teardown()
	os.Exit(exitCode)
}

func getAaAccountPiDaoTestSuite(t *testing.T) (*aaAccountPiTestSuite, func()) {
	dbInstancePool := dbNameToDbInstancePoolMap[conf.PaymentInstrumentDb.GetName()]
	dbInstance, dbInstanceReleaseFn := dbInstancePool.GetDbInstance(t)
	aaAccountPiDaoPgdb := dao.NewAAAccountPiDaoPgdb(dbInstance.GetConnection(), idg)
	return &aaAccountPiTestSuite{
			db:              dbInstance.GetConnection(),
			aaAccountPiPgdb: aaAccountPiDaoPgdb,
		}, func() {
			dbInstanceReleaseFn(aaAccountPiAffectedTestTables)
		}
}

func getAccountPiTestSuite(t *testing.T) (*accountPiTestSuite, func()) {
	dbInstancePool := dbNameToDbInstancePoolMap[conf.PaymentInstrumentDb.GetName()]
	dbInstance, dbInstanceReleaseFn := dbInstancePool.GetDbInstance(t)
	accountPiDaoPgdb := dao.NewAccountPiDaoPgdb(dbInstance.GetConnection(), idg)
	return &accountPiTestSuite{
			db:           dbInstance.GetConnection(),
			conf:         genConf,
			accountPiDao: accountPiDaoPgdb,
		}, func() {
			dbInstanceReleaseFn(accountPiAffectedTestTables)
		}
}

func getCacheAccountPiTestSuite(t *testing.T) (*cacheAccountPiTestSuite, func()) {
	dbInstancePool := dbNameToDbInstancePoolMap[conf.PaymentInstrumentDb.GetName()]
	dbInstance, dbInstanceReleaseFn := dbInstancePool.GetDbInstance(t)
	accountPiDaoPgdb := dao.NewAccountPiDaoPgdb(dbInstance.GetConnection(), idg)
	return &cacheAccountPiTestSuite{
			db:               dbInstance.GetConnection(),
			conf:             genConf,
			accountPiDaoPgdb: accountPiDaoPgdb,
		}, func() {
			dbInstanceReleaseFn(cacheAccountPiAffectedTestTables)
		}
}

func getCachePiTestSuite(t *testing.T) (*cachePiTestSuite, func()) {
	dbInstancePool := dbNameToDbInstancePoolMap[conf.PaymentInstrumentDb.GetName()]
	dbInstance, dbInstanceReleaseFn := dbInstancePool.GetDbInstance(t)
	txnExecutor := storageV2.NewGormTxnExecutor(dbInstance.GetConnection())
	piDaoPgdb := dao.NewPiDaoPgdb(dbInstance.GetConnection(), idg, txnExecutor, nil, nil, nil, false)
	return &cachePiTestSuite{
			db:        dbInstance.GetConnection(),
			piDaoPgdb: piDaoPgdb,
			conf:      genConf,
		}, func() {
			dbInstanceReleaseFn(piAffectedTestTables)
		}
}

func getPiTestSuite(t *testing.T) (*piTestSuite, func()) {
	dbInstancePool := dbNameToDbInstancePoolMap[conf.PaymentInstrumentDb.GetName()]
	dbInstance, dbInstanceReleaseFn := dbInstancePool.GetDbInstance(t)
	txnExecutor := storageV2.NewGormTxnExecutor(dbInstance.GetConnection())
	dbResourceProvider, release := dbResourceProviderPool.GetDbConnProviderWithUseCase(t)
	attrIdGen := idgen.NewAttributedIdGen[*dao.PaymentInstrumentAttribute](idgen.NewClock())
	txnExecResProvider, err := getTxnExecProviderFromDbResourceProvider(dbResourceProvider)
	require.NoError(t, err)
	piDaoPgdb := dao.NewPiDaoPgdb(dbInstance.GetConnection(), idg, txnExecutor, attrIdGen, dbResourceProvider, txnExecResProvider, true)
	return &piTestSuite{
			db:    dbInstance.GetConnection(),
			piDao: piDaoPgdb,
		}, func() {
			release(piAffectedTestTablesMap)
			dbInstanceReleaseFn(piAffectedTestTables)
		}
}

func getPiLastTransTS(t *testing.T) (*piLastTransTS, func()) {
	dbInstancePool := dbNameToDbInstancePoolMap[conf.PaymentInstrumentDb.GetName()]
	dbInstance, dbInstanceReleaseFn := dbInstancePool.GetDbInstance(t)
	piLastTransDaoPgdb := dao.NewPiLastTransactionPgdb(genConf, dbInstance.GetConnection())
	return &piLastTransTS{
			db:      dbInstance.GetConnection(),
			dao:     piLastTransDaoPgdb,
			genconf: genConf,
		}, func() {
			dbInstanceReleaseFn(piLastTransAffectedTable)
		}
}

func getPiPurgeAuditTestSuite(t *testing.T) (*piPurgeAuditTestSuite, func()) {
	dbInstancePool := dbNameToDbInstancePoolMap[conf.PaymentInstrumentDb.GetName()]
	dbInstance, dbInstanceReleaseFn := dbInstancePool.GetDbInstance(t)
	piPurgeAuditDao := dao.NewpaymentInstrumentPurgeAuditPGDB(dbInstance.GetConnection())
	return &piPurgeAuditTestSuite{
			db:              dbInstance.GetConnection(),
			genConf:         genConf,
			piPurgeAuditDao: piPurgeAuditDao,
		}, func() {
			dbInstanceReleaseFn(piPurgeAuditsAffectedTables)
		}
}

func getPiStateLogTestSuite(t *testing.T) (*piStateLogTestSuite, func()) {
	dbInstancePool := dbNameToDbInstancePoolMap[conf.PaymentInstrumentDb.GetName()]
	dbInstance, dbInstanceReleaseFn := dbInstancePool.GetDbInstance(t)
	piStateLogDaoPgdb := dao.NewpiStateLogDaoPGDB(dbInstance.GetConnection())
	return &piStateLogTestSuite{
			db:             dbInstance.GetConnection(),
			conf:           genConf,
			piStateLogPgdb: piStateLogDaoPgdb,
		}, func() {
			dbInstanceReleaseFn(piStateLogsAffectedTables)
		}
}

func InitConfigAndDBResourceProviderInstancePool() (testPkg.DbResourceProviderInstancePool, func()) {
	// Init config
	serverConf, err := serverCfg.Load(cfg.ACTOR_SERVER)
	if err != nil {
		log.Fatal("failed to load config", err)
	}
	// Setup logger
	logger.Init(serverConf.Environment)
	// Currently set pool size to 1 since we don't support concurrency for DBResourceProviderInstancePool.
	dbResourceProviderInstancePool := testPkg.NewDbResourceProviderInstancePoolWithUseCase(testPkg.NewZapLogger(logger.Log), serverConf.UseCaseDBConfigMap, 1)
	// Initializing dbUtilsProvider to get db connections and txn executors
	return dbResourceProviderInstancePool, func() {
		dbResourceProviderInstancePool.Cleanup(testPkg.NewZapLogger(logger.Log))
	}
}

// nolint:unparam
func getTxnExecProviderFromDbResourceProvider(
	dbResProvider *usecase.DBResourceProvider[*gorm.DB]) (*usecase.DBResourceProvider[storageV2.IdempotentTxnExecutor], error) {

	ownershipUsecaseTxnExecutorsMap := make(map[commontypes.Ownership]map[commontypes.UseCase]storageV2.IdempotentTxnExecutor)
	for ow, usecaseToDbConn := range dbResProvider.DBOwnershipMap {
		// Ensure the inner map is initialized before assigning to it
		if ownershipUsecaseTxnExecutorsMap[ow] == nil {
			ownershipUsecaseTxnExecutorsMap[ow] = make(map[commontypes.UseCase]storageV2.IdempotentTxnExecutor)
		}
		for useCase, dbConn := range usecaseToDbConn {
			txnExec := storageV2.IdempotentTxnExecutor(storageV2.NewGormTxnExecutor(dbConn))
			ownershipUsecaseTxnExecutorsMap[ow][useCase] = txnExec
		}
	}
	return &usecase.DBResourceProvider[storageV2.IdempotentTxnExecutor]{
		DBOwnershipMap: ownershipUsecaseTxnExecutorsMap,
	}, nil
}
