package dao

import (
	"context"
	"fmt"
	"time"

	"google.golang.org/protobuf/encoding/protojson"
	gormv2 "gorm.io/gorm"

	pkgCmdTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	piPb "github.com/epifi/gamma/api/paymentinstrument"
)

type PaymentInstrumentPurgeAuditPGDB struct {
	db *gormv2.DB
}

func NewpaymentInstrumentPurgeAuditPGDB(db pkgCmdTypes.PaymentInstrumentPGDB) *PaymentInstrumentPurgeAuditPGDB {
	return &PaymentInstrumentPurgeAuditPGDB{db: db}
}

var _ PiPurgeAuditDao = &PaymentInstrumentPurgeAuditPGDB{}

func (p *PaymentInstrumentPurgeAuditPGDB) BatchUpsert(ctx context.Context, auditEntries []*piPb.PaymentInstrumentPurgeAudit) error {
	defer metric_util.TrackDuration("paymentinstrument/dao", "PaymentInstrumentPurgeAuditPGDB", "BatchUpsert", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, p.db)
	var query string
	for _, e := range auditEntries {
		marshalledPayload, err := protojson.Marshal(e.GetPayload())
		if err != nil {
			return fmt.Errorf("failed to marshal pi: %w", err)
		}

		query += fmt.Sprintf("INSERT INTO payment_instrument_purge_audits (pi_id, payload) VALUES ('%s', '%s') ON CONFLICT (pi_id) DO UPDATE set payload = '%s', updated_at = '%s';", e.GetPiId(), marshalledPayload, marshalledPayload, time.Now().UTC().Format("2006-01-02 15:04:05.000000 +00:00"))
	}

	err := db.Exec(query).Error
	if err != nil {
		return fmt.Errorf("error upserting entry in payment instrument audit entries: %w", err)
	}

	return nil
}
