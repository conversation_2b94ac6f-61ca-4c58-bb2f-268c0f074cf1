package dao_test

import (
	"context"
	"reflect"
	"testing"

	"github.com/google/go-cmp/cmp"
	gormv2 "gorm.io/gorm"

	accountsPb "github.com/epifi/gamma/api/accounts"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/paymentinstrument/dao"
	"github.com/epifi/gamma/paymentinstrument/dao/model"
)

type aaAccountPiTestSuite struct {
	db              *gormv2.DB
	aaAccountPiPgdb dao.AAAccountPiDao
}

var (
	fixtureAaActorId    = "actor-user-11"
	fixtureAaAccountPi1 = &accountPiPb.AAAccountPI{
		Id:          "account-aa-pi-1",
		ActorId:     "actor-user-11",
		AccountId:   "Test-Savings-aa-1",
		AccountType: accountsPb.Type_SAVINGS,
		PiId:        "pi-aa-1",
	}
	fixtureAaAccountPi4 = &accountPiPb.AAAccountPI{
		Id:          "account-aa-pi-4",
		ActorId:     "actor-user-12",
		AccountId:   "Test-Savings-aa-4",
		AccountType: accountsPb.Type_SAVINGS,
		PiId:        "pi-aa-4",
	}
	fixtureAaAccountPi5 = &accountPiPb.AAAccountPI{
		Id:          "account-aa-pi-5",
		ActorId:     "actor-user-12",
		AccountId:   "Test-Savings-aa-5",
		AccountType: accountsPb.Type_SAVINGS,
		PiId:        "pi-aa-5",
	}
	aaAccountPiAffectedTestTables = []string{"aa_account_pis"}
)

// isAaAccountPiDeepEqual compares 2 values of type *accountPiPb.AccountPI without the standard timestamp fields and id
func assertAaAccountPi(actual, expected *accountPiPb.AAAccountPI) bool {
	if actual != nil && expected != nil {
		expected.Id = actual.Id
		expected.CreatedAt = actual.CreatedAt
		expected.UpdatedAt = actual.UpdatedAt
		expected.DeletedAt = actual.DeletedAt
	}
	return reflect.DeepEqual(actual, expected)
}

func assertAaAccountPiList(got, want []*accountPiPb.AAAccountPI) bool {
	if len(got) != len(want) {
		return false
	}
	for index := range got {
		if !assertAaAccountPi(got[index], want[index]) {
			return false
		}
	}
	return true
}

func getAaAccountPiRelationFromDB(accountId string, db *gormv2.DB) []*model.AAAccountPI {
	var gotDataModel []*model.AAAccountPI
	db.Unscoped().Table("aa_account_pis").Where("account_id = ?", accountId).Scan(&gotDataModel)
	return gotDataModel
}

func TestAAAccountPiDaoPgdb_Create(t *testing.T) {
	tests := []struct {
		name      string
		accountPi *accountPiPb.AAAccountPI
		wantErr   bool
	}{
		{
			name: "created entry successfully",
			accountPi: &accountPiPb.AAAccountPI{
				ActorId:     fixtureAaActorId,
				AccountId:   "random-account-id",
				AccountType: fixtureAccountType,
				PiId:        "pi-aa-2",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testSuite, testSuiteRelease := getAaAccountPiDaoTestSuite(t)
			defer testSuiteRelease()
			got, err := testSuite.aaAccountPiPgdb.Create(context.Background(), tt.accountPi)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() got err: %v wantErr: %v", err, tt.wantErr)
				return
			}
			if !assertAaAccountPi(got, tt.accountPi) {
				t.Errorf("Create() got :%v, want: %v", got, tt.accountPi)
				return
			}
		})

	}
}

func TestAAAccountPiDaoPgdb_GetByAccountId(t *testing.T) {
	testSuite, testSuiteRelease := getAaAccountPiDaoTestSuite(t)
	defer testSuiteRelease()

	tests := []struct {
		name        string
		accountId   string
		accountType accountsPb.Type
		want        []*accountPiPb.AAAccountPI
		wantErr     bool
	}{
		{
			name:        "Fetched account pi successfully",
			accountId:   "Test-Savings-aa-1",
			accountType: accountsPb.Type_SAVINGS,
			want: []*accountPiPb.AAAccountPI{
				fixtureAaAccountPi1,
			},
		},
		{
			name:        "record not found",
			accountId:   "Test-Savings-aa-random",
			accountType: accountsPb.Type_SAVINGS,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := testSuite.aaAccountPiPgdb.GetByAccountId(context.Background(), tt.accountId, tt.accountType)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByAccountId() got err: %v wantErr: %v", err, tt.wantErr)
				return
			}
			if !assertAaAccountPiList(got, tt.want) {
				t.Errorf("Create() got :%v, want: %v", got, tt.want)
				return
			}
		})
	}
}

func TestAAAccountPiDaoPgdb_GetByPiId(t *testing.T) {
	testSuite, testSuiteRelease := getAaAccountPiDaoTestSuite(t)
	defer testSuiteRelease()

	type args struct {
		ctx  context.Context
		piId string
	}
	tests := []struct {
		name    string
		args    args
		want    *accountPiPb.AAAccountPI
		wantErr bool
	}{
		{
			name: "Should successfully fetch AaAccountPI",
			args: args{
				ctx:  context.Background(),
				piId: fixtureAaAccountPi1.PiId,
			},
			want:    fixtureAaAccountPi1,
			wantErr: false,
		},
		{
			name: "Should fail to fetch due to not found error",
			args: args{
				ctx:  context.Background(),
				piId: "random-pi-id",
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := testSuite.aaAccountPiPgdb.GetByPiId(tt.args.ctx, tt.args.piId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByPiId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !assertAaAccountPi(got, tt.want) {
				t.Errorf("GetByPiId() got = %v, want %v, diff %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestAAAccountPiDaoPgdb_DeleteByAccountId(t *testing.T) {
	testSuite, testSuiteRelease := getAaAccountPiDaoTestSuite(t)
	defer testSuiteRelease()

	tests := []struct {
		name      string
		accountId string
		wantErr   bool
	}{
		{
			name:      "delete account pi relation successfully",
			accountId: "Test-Savings-aa-1",
		},
		{
			name:      "account pi does not exist",
			accountId: "account-random",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := testSuite.aaAccountPiPgdb.DeleteByAccountId(context.Background(), tt.accountId)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeleteByAccountId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if err != nil {
				got := getAaAccountPiRelationFromDB(tt.accountId, testSuite.db)
				if len(got) != 0 {
					t.Errorf("account pi relation not detleted: %v", got)
					return
				}
			}
		})
	}
}

func TestAAAccountPiDaoPgdb_GetById(t *testing.T) {
	testSuite, testSuiteRelease := getAaAccountPiDaoTestSuite(t)
	defer testSuiteRelease()

	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		args    args
		want    *accountPiPb.AAAccountPI
		wantErr bool
	}{
		{
			name: "Should successfully fetch AaAccountPI",
			args: args{
				ctx: context.Background(),
				id:  fixtureAaAccountPi1.Id,
			},
			want:    fixtureAaAccountPi1,
			wantErr: false,
		},
		{
			name: "Should fail to fetch due to not found error",
			args: args{
				ctx: context.Background(),
				id:  "random-pi-id",
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := testSuite.aaAccountPiPgdb.GetById(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !assertAaAccountPi(got, tt.want) {
				t.Errorf("GetById() got = %v, want %v, diff %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestAAAccountPiDaoPgdb_GetByActorId(t *testing.T) {
	testSuite, testSuiteRelease := getAaAccountPiDaoTestSuite(t)
	defer testSuiteRelease()

	tests := []struct {
		name    string
		actorId string
		want    []*accountPiPb.AAAccountPI
		wantErr bool
	}{
		{
			name:    "Fetched account pi successfully",
			actorId: "actor-user-11",
			want: []*accountPiPb.AAAccountPI{
				fixtureAaAccountPi1,
			},
		},
		{
			name:    "Fetched list of account pis successfully",
			actorId: "actor-user-12",
			want: []*accountPiPb.AAAccountPI{
				fixtureAaAccountPi4,
				fixtureAaAccountPi5,
			},
		},
		{
			name:    "record not found",
			actorId: "random-actor-id",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := testSuite.aaAccountPiPgdb.GetByActorId(context.Background(), tt.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorId() got err: %v wantErr: %v", err, tt.wantErr)
				return
			}
			if !assertAaAccountPiList(got, tt.want) {
				t.Errorf("GetByActorId() got :%v, want: %v", got, tt.want)
				return
			}
		})
	}
}
