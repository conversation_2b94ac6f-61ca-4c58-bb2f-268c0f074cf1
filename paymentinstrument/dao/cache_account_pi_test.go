package dao_test

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/encoding/protojson"
	gormV2 "gorm.io/gorm"

	mock_cache "github.com/epifi/be-common/pkg/cache/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"

	accountsPb "github.com/epifi/gamma/api/accounts"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/paymentinstrument/config/genconf"
	"github.com/epifi/gamma/paymentinstrument/constants"
	"github.com/epifi/gamma/paymentinstrument/dao"
)

type cacheAccountPiTestSuite struct {
	accountPiDaoPgdb *dao.AccountPiDaoPgdb
	conf             *genconf.Config
	db               *gormV2.DB
}

var (
	cacheFixtureActorId     = "actor-user-1"
	cacheFixtureAccountType = accountsPb.Type_SAVINGS
	cacheFixtureAccountId   = "Test-Savings-1"
	cacheFixtureAccountPi1  = &accountPiPb.AccountPI{
		Id:          "account-pi-1",
		ActorId:     "actor-user-1",
		AccountId:   "Test-Savings-1",
		AccountType: accountsPb.Type_SAVINGS,
		PiId:        "pi-1",
	}
	cacheFixtureAccountPi1Bytes, _   = protojson.Marshal(cacheFixtureAccountPi1)
	cacheAccountPiAffectedTestTables = []string{"account_pis"}
	AccountPiIdPrefix                = constants.AccountPiIdPrefix
)

func TestAccountPiCachePgdb_Create(t *testing.T) {
	// t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	testSuite, testSuiteReleaseFunc := getCacheAccountPiTestSuite(t)
	defer testSuiteReleaseFunc()
	accountPiDaoPgdb := testSuite.accountPiDaoPgdb

	ctr := gomock.NewController(t)
	mockCache := mock_cache.NewMockCacheStorage(ctr)

	accountPiCache := dao.NewAccountPiCache(mockCache, testSuite.conf, accountPiDaoPgdb)
	type mockDelete struct {
		keyId     string
		returnErr error
	}
	cacheFixturesPIId2 := "pi-2"
	cacheAccountPI := &accountPiPb.AccountPI{
		ActorId:     cacheFixtureActorId,
		AccountId:   cacheFixtureAccountId,
		AccountType: cacheFixtureAccountType,
		PiId:        cacheFixturesPIId2,
	}
	tests := []struct {
		context    context.Context
		name       string
		accountPi  *accountPiPb.AccountPI
		mockDelete []mockDelete
		wantErr    bool
	}{
		{
			context:   context.Background(),
			name:      "Account Pi actor Id successfully deleted from cache",
			accountPi: cacheAccountPI,
			mockDelete: []mockDelete{
				{
					keyId:     AccountPiIdPrefix + cacheFixtureActorId,
					returnErr: nil,
				},
				{
					keyId:     AccountPiIdPrefix + cacheFixtureAccountId,
					returnErr: nil,
				},
			},
		},
		{
			context:   context.Background(),
			name:      "Error in deleting Account Pi actor Id in cache hence no creation of account Pi in db",
			accountPi: cacheAccountPI,
			mockDelete: []mockDelete{
				{
					keyId:     AccountPiIdPrefix + cacheFixtureActorId,
					returnErr: errors.New("error while deleting actor Id in cache"),
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			for _, mocks := range tt.mockDelete {
				mockCache.EXPECT().Delete(tt.context, mocks.keyId).Return(mocks.returnErr)
			}
			resultAccountPi, err := accountPiCache.Create(tt.context, tt.accountPi)
			if tt.wantErr {
				assert.NotNil(t, err)
				assert.Nil(t, resultAccountPi)
				return
			}
			assert.Nil(t, err)
			accountPi, fetchErr := accountPiDaoPgdb.GetByPiId(tt.context, cacheFixturesPIId2)
			assert.Nil(t, fetchErr)
			if !isAccountPiDeepEqual(accountPi, resultAccountPi, false) {
				t.Errorf("Create() : got %v, want %v", accountPi, resultAccountPi)
			}
		})
	}
}

func TestAccountPiCachePgdb_GetByActorId(t *testing.T) {
	testSuite, testSuiteReleaseFunc := getCacheAccountPiTestSuite(t)
	defer testSuiteReleaseFunc()
	accountPiDaoPgdb := testSuite.accountPiDaoPgdb
	ctr := gomock.NewController(t)
	mockCache := mock_cache.NewMockCacheStorage(ctr)

	accountPiCache := dao.NewAccountPiCache(mockCache, testSuite.conf, accountPiDaoPgdb)
	type mockGetSet struct {
		enable     bool
		keyId      string
		returnErr  error
		returnList []string
	}
	type mockCreateOrAppendSet struct {
		enable    bool
		keyId     string
		returnErr error
	}
	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name                  string
		args                  args
		want                  []*accountPiPb.AccountPI
		mockGetSet            mockGetSet
		mockCreateOrAppendSet mockCreateOrAppendSet
		wantErr               bool
	}{
		{
			name: "ActorId - account Pi List hit in cache",
			args: args{
				ctx:     context.Background(),
				actorId: cacheFixtureAccountPi1.ActorId,
			},
			want: []*accountPiPb.AccountPI{cacheFixtureAccountPi1},
			mockGetSet: mockGetSet{
				enable:     true,
				keyId:      AccountPiIdPrefix + cacheFixtureAccountPi1.GetActorId(),
				returnErr:  nil,
				returnList: []string{string(cacheFixtureAccountPi1Bytes)},
			},
		},
		{
			name: "Actor Id - account Pi List miss in cache",
			args: args{
				ctx:     context.Background(),
				actorId: cacheFixtureAccountPi1.ActorId,
			},
			want: []*accountPiPb.AccountPI{cacheFixtureAccountPi1},
			mockGetSet: mockGetSet{
				enable:     true,
				keyId:      AccountPiIdPrefix + cacheFixtureAccountPi1.GetActorId(),
				returnErr:  epifierrors.ErrRecordNotFound,
				returnList: nil,
			},
			mockCreateOrAppendSet: mockCreateOrAppendSet{
				enable:    true,
				keyId:     AccountPiIdPrefix + cacheFixtureAccountPi1.GetActorId(),
				returnErr: nil,
			},
		},
		{
			name: "Should successfully fetch 0 AccountPIs",
			args: args{
				ctx:     context.Background(),
				actorId: "random-actor-id",
			},
			want:    nil,
			wantErr: false,
			mockGetSet: mockGetSet{
				enable:     true,
				keyId:      AccountPiIdPrefix + "random-actor-id",
				returnErr:  epifierrors.ErrRecordNotFound,
				returnList: nil,
			},
			mockCreateOrAppendSet: mockCreateOrAppendSet{
				enable: false,
			},
		},
		{
			name: "Suppressing Errors from cache read methods",
			args: args{
				ctx:     context.Background(),
				actorId: cacheFixtureAccountPi1.ActorId,
			},
			want: []*accountPiPb.AccountPI{cacheFixtureAccountPi1},
			mockGetSet: mockGetSet{
				enable:     true,
				keyId:      AccountPiIdPrefix + cacheFixtureAccountPi1.GetActorId(),
				returnErr:  errors.New("Error while fetching from cache"),
				returnList: nil,
			},
			mockCreateOrAppendSet: mockCreateOrAppendSet{
				enable:    true,
				keyId:     AccountPiIdPrefix + cacheFixtureAccountPi1.GetActorId(),
				returnErr: errors.New("Error while creating new list in cache "),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetSet.enable {
				mockCache.EXPECT().GetValuesFromSet(tt.args.ctx, tt.mockGetSet.keyId).Return(tt.mockGetSet.returnList, tt.mockGetSet.returnErr)
			}
			if tt.mockCreateOrAppendSet.enable {
				mockCache.EXPECT().CreateOrAddToSet(tt.args.ctx, testSuite.conf.AccountPiCacheConfig().CacheTTl(), tt.mockCreateOrAppendSet.keyId, gomock.Any()).Return(1, tt.mockCreateOrAppendSet.returnErr)
			}
			got, err := accountPiCache.GetByActorId(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			for i := range tt.want {
				if !isAccountPiDeepEqual(got[i], tt.want[i], false) {
					t.Errorf("GetByActorId() index = %d, got %v, want %v, diff %s", i, got, tt.want, cmp.Diff(got[i], tt.want[i]))
				}
			}
		})
	}

}

func TestAccountPiCachePgdb_GetByPiId(t *testing.T) {
	testSuite, testSuiteReleaseFunc := getCacheAccountPiTestSuite(t)
	defer testSuiteReleaseFunc()
	accountPiDaoPgdb := testSuite.accountPiDaoPgdb
	ctr := gomock.NewController(t)
	mockCache := mock_cache.NewMockCacheStorage(ctr)

	accountPiCache := dao.NewAccountPiCache(mockCache, testSuite.conf, accountPiDaoPgdb)

	type mockSet struct {
		enable    bool
		keyId     string
		returnErr error
	}
	type mockGet struct {
		enable       bool
		keyId        string
		returnString string
		returnErr    error
	}
	tests := []struct {
		name    string
		ctx     context.Context
		piId    string
		want    *accountPiPb.AccountPI
		wantErr bool
		mockSet mockSet
		mockGet mockGet
	}{
		{
			name:    "Id exists and Id - AccountPi cache hit",
			ctx:     context.Background(),
			piId:    cacheFixtureAccountPi1.GetPiId(),
			want:    cacheFixtureAccountPi1,
			wantErr: false,
			mockSet: mockSet{
				enable: false,
			},
			mockGet: mockGet{
				enable:       true,
				keyId:        AccountPiIdPrefix + cacheFixtureAccountPi1.GetPiId(),
				returnString: string(cacheFixtureAccountPi1Bytes),
				returnErr:    nil,
			},
		},
		{
			name:    "Id exsists and Id - AccountPi cache miss ",
			ctx:     context.Background(),
			piId:    cacheFixtureAccountPi1.GetPiId(),
			want:    cacheFixtureAccountPi1,
			wantErr: false,
			mockSet: mockSet{
				enable:    true,
				keyId:     AccountPiIdPrefix + cacheFixtureAccountPi1.GetPiId(),
				returnErr: nil,
			},
			mockGet: mockGet{
				enable:       true,
				keyId:        AccountPiIdPrefix + cacheFixtureAccountPi1.GetPiId(),
				returnString: "",
				returnErr:    epifierrors.ErrRecordNotFound,
			},
		},
		{
			name:    "should fail to fetch AccountPi",
			ctx:     context.Background(),
			piId:    "random-id",
			want:    nil,
			wantErr: true,
			mockGet: mockGet{
				enable:       true,
				keyId:        AccountPiIdPrefix + "random-id",
				returnString: "",
				returnErr:    epifierrors.ErrRecordNotFound,
			},
		},
		{
			name:    "suppressing errors returned by cache methods",
			ctx:     context.Background(),
			piId:    cacheFixtureAccountPi1.GetPiId(),
			want:    cacheFixtureAccountPi1,
			wantErr: false,
			mockGet: mockGet{
				enable:       true,
				keyId:        AccountPiIdPrefix + cacheFixtureAccountPi1.GetPiId(),
				returnString: "",
				returnErr:    epifierrors.ErrKeyNotDeletedFromCache,
			},
			mockSet: mockSet{
				enable:    true,
				keyId:     AccountPiIdPrefix + cacheFixtureAccountPi1.GetPiId(),
				returnErr: epifierrors.ErrKeyNotCreatedInCache,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockSet.enable {
				mockCache.EXPECT().Set(tt.ctx, tt.mockSet.keyId, gomock.Any(), gomock.Any()).Return(tt.mockSet.returnErr)
			}
			if tt.mockGet.enable {
				mockCache.EXPECT().Get(tt.ctx, tt.mockGet.keyId).Return(tt.mockGet.returnString, tt.mockGet.returnErr)
			}
			got, err := accountPiCache.GetByPiId(tt.ctx, tt.piId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByPiId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isAccountPiDeepEqual(got, tt.want, false) {
				t.Errorf("GetByPiId() got = %v, want %v, diff %s", got, tt.want, cmp.Diff(got, tt.want))
			}

		})
	}
}

func TestAccountPiCachePgdb_GetByAccountId(t *testing.T) {
	testSuite, testSuiteReleaseFunc := getCacheAccountPiTestSuite(t)
	defer testSuiteReleaseFunc()
	accountPiDaoPgdb := testSuite.accountPiDaoPgdb
	ctr := gomock.NewController(t)
	mockCache := mock_cache.NewMockCacheStorage(ctr)

	accountPiCache := dao.NewAccountPiCache(mockCache, testSuite.conf, accountPiDaoPgdb)

	type mockGetSet struct {
		enable     bool
		keyId      string
		returnErr  error
		returnList []string
	}
	type mockCreateOrAppendSet struct {
		enable    bool
		keyId     string
		returnErr error
	}
	type args struct {
		ctx         context.Context
		accountId   string
		accountType accountsPb.Type
	}
	tests := []struct {
		name                  string
		args                  args
		want                  []*accountPiPb.AccountPI
		wantErr               bool
		mockGetSet            mockGetSet
		mockCreateOrAppendSet mockCreateOrAppendSet
	}{
		{
			name: "AccountId - account Pi List hit in cache",
			args: args{
				ctx:         context.Background(),
				accountId:   cacheFixtureAccountPi1.GetAccountId(),
				accountType: cacheFixtureAccountPi1.GetAccountType(),
			},
			want: []*accountPiPb.AccountPI{cacheFixtureAccountPi1},
			mockGetSet: mockGetSet{
				enable:     true,
				keyId:      AccountPiIdPrefix + cacheFixtureAccountPi1.GetAccountId(),
				returnErr:  nil,
				returnList: []string{string(cacheFixtureAccountPi1Bytes)},
			},
		},
		{
			name: "Account Id - account Pi List miss in cache",
			args: args{
				ctx:         context.Background(),
				accountId:   cacheFixtureAccountPi1.GetAccountId(),
				accountType: cacheFixtureAccountPi1.GetAccountType(),
			},
			want: []*accountPiPb.AccountPI{cacheFixtureAccountPi1},
			mockGetSet: mockGetSet{
				enable:     true,
				keyId:      AccountPiIdPrefix + cacheFixtureAccountPi1.GetAccountId(),
				returnErr:  epifierrors.ErrRecordNotFound,
				returnList: nil,
			},
			mockCreateOrAppendSet: mockCreateOrAppendSet{
				enable:    true,
				keyId:     AccountPiIdPrefix + cacheFixtureAccountPi1.GetAccountId(),
				returnErr: nil,
			},
		},
		{
			name: "Should successfully fetch 0 AccountPIs",
			args: args{
				ctx:         context.Background(),
				accountId:   "random-account-id",
				accountType: accountsPb.Type_TYPE_UNSPECIFIED,
			},
			want:    nil,
			wantErr: false,
			mockGetSet: mockGetSet{
				enable:     true,
				keyId:      AccountPiIdPrefix + "random-account-id",
				returnErr:  epifierrors.ErrRecordNotFound,
				returnList: nil,
			},
			mockCreateOrAppendSet: mockCreateOrAppendSet{
				enable: false,
			},
		},
		{
			name: "Suppressing Errors from cache read methods",
			args: args{
				ctx:         context.Background(),
				accountId:   cacheFixtureAccountPi1.GetAccountId(),
				accountType: cacheFixtureAccountPi1.GetAccountType(),
			},
			want: []*accountPiPb.AccountPI{cacheFixtureAccountPi1},
			mockGetSet: mockGetSet{
				enable:     true,
				keyId:      AccountPiIdPrefix + cacheFixtureAccountPi1.GetAccountId(),
				returnErr:  errors.New("Error while fetching from cache"),
				returnList: nil,
			},
			mockCreateOrAppendSet: mockCreateOrAppendSet{
				enable:    true,
				keyId:     AccountPiIdPrefix + cacheFixtureAccountPi1.GetAccountId(),
				returnErr: errors.New("Error while creating new list in cache "),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			if tt.mockGetSet.enable {
				mockCache.EXPECT().GetValuesFromSet(tt.args.ctx, tt.mockGetSet.keyId).Return(tt.mockGetSet.returnList, tt.mockGetSet.returnErr)
			}
			if tt.mockCreateOrAppendSet.enable {
				mockCache.EXPECT().CreateOrAddToSet(tt.args.ctx, testSuite.conf.AccountPiCacheConfig().CacheTTl(), tt.mockCreateOrAppendSet.keyId, gomock.Any()).Return(1, tt.mockCreateOrAppendSet.returnErr)
			}
			got, err := accountPiCache.GetByAccountId(tt.args.ctx, tt.args.accountId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByAccountId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			for i := range tt.want {
				if !isAccountPiDeepEqual(got[i], tt.want[i], false) {
					t.Errorf("GetByAccountId() index = %d, got %v, want %v, diff %s", i, got, tt.want, cmp.Diff(got[i], tt.want[i]))
				}
			}
		})
	}
}
