package paymentinstrument_test

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/cfg"
	serverCfg "github.com/epifi/be-common/pkg/cmd/config/genconf"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/be-common/pkg/epifierrors"
	queueMock "github.com/epifi/be-common/pkg/queue/mocks"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTestv2 "github.com/epifi/be-common/pkg/test/v2"

	"google.golang.org/protobuf/testing/protocmp"

	accountPIPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	savingsPb "github.com/epifi/gamma/api/savings"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	"github.com/epifi/gamma/api/typesv2/account"
	daoMocks "github.com/epifi/gamma/paymentinstrument/dao/mocks"

	"github.com/google/go-cmp/cmp"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/idgen"

	accountsPb "github.com/epifi/gamma/api/accounts"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/paymentinstrument"
	"github.com/epifi/gamma/paymentinstrument/config"
	"github.com/epifi/gamma/paymentinstrument/dao"
	"github.com/epifi/gamma/paymentinstrument/test"
)

var (
	db                     *gormv2.DB
	conf                   *config.Config
	piService              *paymentinstrument.Service
	piDao                  dao.PiDao
	dbResourceProviderPool pkgTestv2.DbResourceProviderInstancePool
	cleanup                func()
)

var (
	fixturePi1 = &piPb.PaymentInstrument{
		Id:   "pi-1",
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.PaymentInstrument_Account{
			Account: &piPb.Account{
				ActualAccountNumber: "***************",
				SecureAccountNumber: "xxxxxxxxxxx2939",
				IfscCode:            "FDRL0001001",
				AccountType:         accountsPb.Type_SAVINGS,
			},
		},
		VerifiedName: "Kunal",
		State:        piPb.PaymentInstrumentState_CREATED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
	}

	fixturePiWealthOwnership = &piPb.PaymentInstrument{
		Id:   "pi-wealth-1",
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.PaymentInstrument_Account{
			Account: &piPb.Account{
				ActualAccountNumber: "***************",
				SecureAccountNumber: "xxxxxxxxxxx2935",
				IfscCode:            "FDRL0001001",
				AccountType:         accountsPb.Type_SAVINGS,
			},
		},
		State: piPb.PaymentInstrumentState_CREATED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
		Ownership:            piPb.Ownership_EPIFI_WEALTH,
	}

	creditCardPi = &piPb.PaymentInstrument{
		Id:    "credit-card-pi",
		Type:  piPb.PaymentInstrumentType_CREDIT_CARD,
		State: piPb.PaymentInstrumentState_CREATED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		Identifier: &piPb.PaymentInstrument_CreditCard{
			CreditCard: &piPb.CreditCard{
				Id: "credit-card-id",
			},
		},
		IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
		Ownership:            piPb.Ownership_EPIFI_TECH,
	}

	upiLitePi = &piPb.PaymentInstrument{
		Id:   "pi-50",
		Type: piPb.PaymentInstrumentType_UPI_LITE,
		Identifier: &piPb.PaymentInstrument_UpiLite{UpiLite: &piPb.UpiLite{
			PiRefId: "pi-49",
			Lrn:     "lrn-81",
		}},
		State:                piPb.PaymentInstrumentState_CREATED,
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  false,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
	}

	upiLitePiFixture = &piPb.PaymentInstrument{
		Id:   "pi-23",
		Type: piPb.PaymentInstrumentType_UPI_LITE,
		Identifier: &piPb.PaymentInstrument_UpiLite{UpiLite: &piPb.UpiLite{
			PiRefId: "pi-3",
			Lrn:     "lrn-1",
		}},
		State:                piPb.PaymentInstrumentState_CREATED,
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  false,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
	}

	affectedTestTables = []string{"payment_instruments", "account_pis"}
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()

	var teardown func()
	conf, _, db, teardown = test.InitTestServer()

	ctrl := gomock.NewController(&testing.T{})
	// Create pi test suite
	piIdgen := idgen.NewDomainIdGenerator(idgen.NewClock())
	txnExecutor := storageV2.NewCRDBIdempotentTxnExecutor(db)
	attrIdGen := idgen.NewAttributedIdGen[*dao.PaymentInstrumentAttribute](idgen.NewClock())
	dbResourceProviderPool, cleanup = InitConfigAndDBResourceProviderInstancePool()
	defer cleanup()
	piDao = dao.NewPiDaoPgdb(db, piIdgen, txnExecutor, attrIdGen, nil, nil, false)
	piService = paymentinstrument.NewService(conf, piDao, nil, nil, queueMock.NewMockPublisher(ctrl), nil, nil, nil)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func InitConfigAndDBResourceProviderInstancePool() (pkgTestv2.DbResourceProviderInstancePool, func()) {
	// Init config
	serverConf, err := serverCfg.Load(cfg.ACTOR_SERVER)
	if err != nil {
		log.Fatal("failed to load config", err)
	}
	// Setup logger
	logger.Init(serverConf.Environment())

	// Currently set pool size to 1 since we don't support concurrency for DBResourceProviderInstancePool.
	dbResourceProviderInstancePool := pkgTestv2.NewDbResourceProviderInstancePoolWithUseCase(pkgTestv2.NewZapLogger(logger.Log), serverConf.UseCaseDBConfigMap(), 1)
	// Initializing dbUtilsProvider to get db connections and txn executors
	return dbResourceProviderInstancePool, func() {
		dbResourceProviderInstancePool.Cleanup(pkgTestv2.NewZapLogger(logger.Log))
	}
}

func TestService_CreatePi(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctrl)
	defer ctrl.Finish()

	ctr := gomock.NewController(&testing.T{})
	piService = paymentinstrument.NewService(conf, piDao, nil, nil, queueMock.NewMockPublisher(ctr), mockSavingsClient,
		nil, nil)

	var testAccountPi = &piPb.PaymentInstrument{
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.PaymentInstrument_Account{
			Account: &piPb.Account{
				ActualAccountNumber: "***************",
				SecureAccountNumber: "xxxxxxxxxxx2938",
				IfscCode:            "FED0001",
				AccountType:         accountsPb.Type_SAVINGS,
			},
		},
		State: piPb.PaymentInstrumentState_CREATED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
		Ownership:            piPb.Ownership_EPIFI_TECH,
	}

	var testKnownIFSCAccountPi = &piPb.PaymentInstrument{
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.PaymentInstrument_Account{
			Account: &piPb.Account{
				ActualAccountNumber: "***************",
				SecureAccountNumber: "xxxxxxxxxxx2938",
				IfscCode:            "FDRL00000001",
				AccountType:         accountsPb.Type_SAVINGS,
			},
		},
		State: piPb.PaymentInstrumentState_CREATED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
		Ownership:            piPb.Ownership_EPIFI_TECH,
	}

	var testUpiPi = &piPb.PaymentInstrument{
		Type: piPb.PaymentInstrumentType_UPI,
		Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
			Vpa:                    "TEST1@fede",
			AccountReferenceNumber: "random-acc-ref-num",
			MaskedAccountNumber:    "random-masked-acc-num",
			IfscCode:               "FI000001",
			AccountType:            accountsPb.Type_SAVINGS,
		}},
		VerifiedName: "Test verified name",
		State:        piPb.PaymentInstrumentState_VERIFIED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
		Ownership:            piPb.Ownership_EPIFI_TECH,
	}
	testUpiPiClone := func() *piPb.PaymentInstrument {
		return proto.Clone(testUpiPi).(*piPb.PaymentInstrument)
	}

	var testUpiPiWithApo = &piPb.PaymentInstrument{
		Type: piPb.PaymentInstrumentType_UPI,
		Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
			Vpa:                    "TEST1@fede",
			AccountReferenceNumber: "random-acc-ref-num",
			MaskedAccountNumber:    "random-masked-acc-num",
			IfscCode:               "FI000001",
			AccountType:            accountsPb.Type_SAVINGS,
			Apo:                    account.AccountProductOffering_APO_NRE,
		}},
		VerifiedName: "Test verified name",
		State:        piPb.PaymentInstrumentState_VERIFIED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
		Ownership:            piPb.Ownership_EPIFI_TECH,
	}
	var testUpiPiWithNumericName = &piPb.PaymentInstrument{
		Type: piPb.PaymentInstrumentType_UPI,
		Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
			Vpa:                    "TEST1@fede",
			AccountReferenceNumber: "random-acc-ref-num",
			MaskedAccountNumber:    "random-masked-acc-num",
			IfscCode:               "FI000001",
			AccountType:            accountsPb.Type_SAVINGS,
			Apo:                    account.AccountProductOffering_APO_NRE,
		}},
		VerifiedName: "************",
		State:        piPb.PaymentInstrumentState_VERIFIED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
		Ownership:            piPb.Ownership_EPIFI_TECH,
	}
	testUpiPiWithApoClone := func() *piPb.PaymentInstrument {
		return proto.Clone(testUpiPiWithApo).(*piPb.PaymentInstrument)
	}

	var testWealthOwnershipPi = &piPb.PaymentInstrument{
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.PaymentInstrument_Account{
			Account: &piPb.Account{
				ActualAccountNumber: "***************",
				SecureAccountNumber: "xxxxxxxxxxx2938",
				IfscCode:            "FDRL00000001",
				AccountType:         accountsPb.Type_SAVINGS,
				Apo:                 account.AccountProductOffering_APO_REGULAR,
			},
		},
		State: piPb.PaymentInstrumentState_CREATED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
		Ownership:            piPb.Ownership_EPIFI_WEALTH,
	}

	var testTechOwnershipPi = &piPb.PaymentInstrument{
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.PaymentInstrument_Account{
			Account: &piPb.Account{
				ActualAccountNumber: "***************",
				SecureAccountNumber: "xxxxxxxxxxx2938",
				IfscCode:            "FDRL00000001",
				AccountType:         accountsPb.Type_SAVINGS,
				Apo:                 account.AccountProductOffering_APO_REGULAR,
			},
		},
		State: piPb.PaymentInstrumentState_CREATED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
		Ownership:            piPb.Ownership_EPIFI_TECH,
	}

	var testOwnershipPi = &piPb.PaymentInstrument{
		Id:   "pi-1",
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.PaymentInstrument_Account{
			Account: &piPb.Account{
				ActualAccountNumber: "***************",
				SecureAccountNumber: "xxxxxxxxxxx2939",
				IfscCode:            "FDRL0001001",
				AccountType:         accountsPb.Type_SAVINGS,
			},
		},
		VerifiedName: "Kunal",
		State:        piPb.PaymentInstrumentState_CREATED,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
		Ownership:            piPb.Ownership_EPIFI_TECH,
	}

	var testCardPi = &piPb.PaymentInstrument{
		Id:   "debit-card-pi",
		Type: piPb.PaymentInstrumentType_DEBIT_CARD,
		Identifier: &piPb.PaymentInstrument_Card{
			Card: &piPb.Card{
				ActualCardNumber: "***************6",
				SecureCardNumber: "xxxxxxxxxxxx9396",
				Name:             "Kunal",
				Expiry:           "10/25",
			},
		},
		State:                piPb.PaymentInstrumentState_CREATED,
		IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
		Ownership:            piPb.Ownership_EPIFI_TECH,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
	}

	type mockGetAccount struct {
		enable bool
		req    *savingsPb.GetAccountRequest
		res    *savingsPb.GetAccountResponse
		err    error
	}
	type mockGetSavingsEssentials struct {
		enable bool
		req    *savingsPb.GetSavingsAccountEssentialsRequest
		res    *savingsPb.GetSavingsAccountEssentialsResponse
		err    error
	}
	type args struct {
		ctx context.Context
		req *piPb.CreatePiRequest
	}
	tests := []struct {
		name                     string
		args                     args
		mockGetAccount           mockGetAccount
		mockGetSavingsEssentials mockGetSavingsEssentials
		want                     *piPb.CreatePiResponse
		wantErr                  bool
	}{
		{
			name: "should successfully create a PI of type DEBIT_CARD",
			args: args{
				ctx: context.Background(),
				req: &piPb.CreatePiRequest{
					Type: piPb.PaymentInstrumentType_DEBIT_CARD,
					Identifier: &piPb.CreatePiRequest_Card{
						Card: testCardPi.GetCard(),
					},
				},
			},
			want: &piPb.CreatePiResponse{
				Status:            rpc.StatusOk(),
				PaymentInstrument: testCardPi,
			},
			wantErr: false,
		},
		{
			name: "Should successfully create a PI of type BANK_ACCOUNT",
			args: args{
				ctx: context.Background(),
				req: &piPb.CreatePiRequest{
					Type:       piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.CreatePiRequest_Account{Account: testAccountPi.GetAccount()},
				},
			},
			want: &piPb.CreatePiResponse{
				Status:            rpc.StatusOk(),
				PaymentInstrument: testAccountPi,
			},
			wantErr: false,
		},
		{
			name: "Should successfully create a internal verified PI of type UPI",
			args: args{
				ctx: context.Background(),
				req: &piPb.CreatePiRequest{
					Type:                 piPb.PaymentInstrumentType_UPI,
					Identifier:           &piPb.CreatePiRequest_Upi{Upi: testUpiPiClone().GetUpi()},
					VerifiedName:         testUpiPi.GetVerifiedName(),
					IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
				},
			},
			want: &piPb.CreatePiResponse{
				Status: rpc.StatusOk(),
				PaymentInstrument: &piPb.PaymentInstrument{
					Type: piPb.PaymentInstrumentType_UPI,
					Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
						Vpa:                    "test1@fede",
						AccountReferenceNumber: "random-acc-ref-num",
						MaskedAccountNumber:    "random-masked-acc-num",
						IfscCode:               "FI000001",
						AccountType:            accountsPb.Type_SAVINGS,
						Apo:                    account.AccountProductOffering_APO_REGULAR,
					}},
					VerifiedName: "Test verified name",
					State:        piPb.PaymentInstrumentState_VERIFIED,
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  true,
						piPb.Capability_OUTBOUND_TXN.String(): true,
					},
					IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
					Ownership:            piPb.Ownership_EPIFI_TECH,
				},
			},
			wantErr: false,
		},
		{
			name: "Should successfully create a PI of type UPI_LITE",
			args: args{
				ctx: context.Background(),
				req: &piPb.CreatePiRequest{
					Type:                 piPb.PaymentInstrumentType_UPI_LITE,
					Identifier:           &piPb.CreatePiRequest_UpiLite{UpiLite: upiLitePi.GetUpiLite()},
					IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  false,
						piPb.Capability_OUTBOUND_TXN.String(): true,
					},
				},
			},
			want: &piPb.CreatePiResponse{
				Status:            rpc.StatusOk(),
				PaymentInstrument: upiLitePi,
			},
			wantErr: false,
		},
		{
			name: "Should successfully return an existing PI of type UPI_LITE",
			args: args{
				ctx: context.Background(),
				req: &piPb.CreatePiRequest{
					Type:                 piPb.PaymentInstrumentType_UPI_LITE,
					Identifier:           &piPb.CreatePiRequest_UpiLite{UpiLite: upiLitePiFixture.GetUpiLite()},
					IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  false,
						piPb.Capability_OUTBOUND_TXN.String(): true,
					},
				},
			},
			want: &piPb.CreatePiResponse{
				Status:            rpc.StatusOk(),
				PaymentInstrument: upiLitePiFixture,
			},
			wantErr: false,
		},
		{
			name: "Should successfully return an existing PI",
			args: args{
				ctx: context.Background(),
				req: &piPb.CreatePiRequest{
					Type:       piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.CreatePiRequest_Account{Account: fixturePi1.GetAccount()},
				},
			},
			mockGetSavingsEssentials: mockGetSavingsEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_AccountNumBankFilter{
						AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
							AccountNumber: fixturePi1.GetAccount().GetActualAccountNumber(),
							PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				res: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						IfscCode: fixturePi1.GetAccount().GetIfscCode(),
					},
				},
			},
			want: &piPb.CreatePiResponse{
				Status:            rpc.StatusOk(),
				PaymentInstrument: fixturePi1,
			},
			wantErr: false,
		},
		{
			name: "Should successfully return an existing PI with corrected IFSC",
			args: args{
				ctx: context.Background(),
				req: &piPb.CreatePiRequest{
					Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.CreatePiRequest_Account{
						Account: &piPb.Account{
							ActualAccountNumber: fixturePi1.GetAccount().GetActualAccountNumber(),
							SecureAccountNumber: fixturePi1.GetAccount().GetSecureAccountNumber(),
							IfscCode:            "FDRL0000001",
							AccountType:         fixturePi1.GetAccount().GetAccountType(),
							Name:                fixturePi1.GetAccount().GetName(),
						},
					},
				},
			},
			mockGetSavingsEssentials: mockGetSavingsEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_AccountNumBankFilter{
						AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
							AccountNumber: fixturePi1.GetAccount().GetActualAccountNumber(),
							PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				res: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						IfscCode: fixturePi1.GetAccount().GetIfscCode(),
					},
				},
			},
			want: &piPb.CreatePiResponse{
				Status:            rpc.NewStatus(uint32(piPb.CreatePiResponse_INCORRECT_IFSC), "", ""),
				PaymentInstrument: fixturePi1,
			},
			wantErr: false,
		},
		{
			name: "Should successfully create entry in case record not found for a known IFSC",
			args: args{
				ctx: context.Background(),
				req: &piPb.CreatePiRequest{
					Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.CreatePiRequest_Account{
						Account: testKnownIFSCAccountPi.GetAccount(),
					},
				},
			},
			mockGetSavingsEssentials: mockGetSavingsEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_AccountNumBankFilter{
						AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
							AccountNumber: testKnownIFSCAccountPi.GetAccount().GetActualAccountNumber(),
							PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				res: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusRecordNotFound(),
				},
			},
			want: &piPb.CreatePiResponse{
				Status:            rpc.StatusOk(),
				PaymentInstrument: testKnownIFSCAccountPi,
			},
			wantErr: false,
		},
		{
			name: "Should successfully create entry in pi with epifi_wealth ownership",
			args: args{
				ctx: context.Background(),
				req: &piPb.CreatePiRequest{
					Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.CreatePiRequest_Account{
						Account: testKnownIFSCAccountPi.GetAccount(),
					},
					Ownership: piPb.Ownership_EPIFI_WEALTH,
				},
			},
			mockGetSavingsEssentials: mockGetSavingsEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_AccountNumBankFilter{
						AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
							AccountNumber: testKnownIFSCAccountPi.GetAccount().GetActualAccountNumber(),
							PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				res: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusRecordNotFound(),
				},
			},
			want: &piPb.CreatePiResponse{
				Status:            rpc.StatusOk(),
				PaymentInstrument: testWealthOwnershipPi,
			},
			wantErr: false,
		},
		{
			name: "Should successfully create entry in pi with epifi_tech ownership",
			args: args{
				ctx: context.Background(),
				req: &piPb.CreatePiRequest{
					Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.CreatePiRequest_Account{
						Account: testKnownIFSCAccountPi.GetAccount(),
					},
					Ownership: piPb.Ownership_EPIFI_TECH,
				},
			},
			mockGetSavingsEssentials: mockGetSavingsEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_AccountNumBankFilter{
						AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
							AccountNumber: testKnownIFSCAccountPi.GetAccount().GetActualAccountNumber(),
							PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				res: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusRecordNotFound(),
				},
			},
			want: &piPb.CreatePiResponse{
				Status:            rpc.StatusOk(),
				PaymentInstrument: testTechOwnershipPi,
			},
			wantErr: false,
		},
		{
			name: "Should successfully create entry in pi and not update from epifi_tech to epifi_wealth",
			args: args{
				ctx: context.Background(),
				req: &piPb.CreatePiRequest{
					Type:       piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.CreatePiRequest_Account{Account: testOwnershipPi.GetAccount()},
					Ownership:  piPb.Ownership_EPIFI_WEALTH,
				},
			},
			mockGetSavingsEssentials: mockGetSavingsEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_AccountNumBankFilter{
						AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
							AccountNumber: testOwnershipPi.GetAccount().GetActualAccountNumber(),
							PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				res: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						IfscCode: fixturePi1.GetAccount().GetIfscCode(),
					},
				},
			},
			want: &piPb.CreatePiResponse{
				Status:            rpc.StatusOk(),
				PaymentInstrument: testOwnershipPi,
			},
			wantErr: false,
		},
		{
			name: "Should Update fixture pi with ownership epifi_wealth to ownership epifi_tech",
			args: args{
				ctx: context.Background(),
				req: &piPb.CreatePiRequest{
					Type:       piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.CreatePiRequest_Account{Account: fixturePiWealthOwnership.GetAccount()},
					Ownership:  piPb.Ownership_EPIFI_TECH,
				},
			},
			mockGetSavingsEssentials: mockGetSavingsEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_AccountNumBankFilter{
						AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
							AccountNumber: fixturePiWealthOwnership.GetAccount().GetActualAccountNumber(),
							PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				res: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						IfscCode: fixturePiWealthOwnership.GetAccount().GetIfscCode(),
					},
				},
			},
			want: &piPb.CreatePiResponse{
				Status: rpc.StatusOk(),
				PaymentInstrument: &piPb.PaymentInstrument{
					Id:   "pi-wealth-1",
					Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.PaymentInstrument_Account{
						Account: &piPb.Account{
							ActualAccountNumber: "***************",
							SecureAccountNumber: "xxxxxxxxxxx2935",
							IfscCode:            "FDRL0001001",
							AccountType:         accountsPb.Type_SAVINGS,
						},
					},
					State: piPb.PaymentInstrumentState_CREATED,
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  true,
						piPb.Capability_OUTBOUND_TXN.String(): true,
					},
					IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
					Ownership:            piPb.Ownership_EPIFI_TECH,
				},
			},
			wantErr: false,
		},
		{
			name: "Should successfully create a UPI pi in CREATED state",
			args: args{
				ctx: context.Background(),
				req: &piPb.CreatePiRequest{
					Type:                 piPb.PaymentInstrumentType_UPI,
					Identifier:           &piPb.CreatePiRequest_Upi{Upi: testUpiPi.GetUpi()},
					VerifiedName:         testUpiPi.VerifiedName,
					IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
					State:                piPb.PaymentInstrumentState_CREATED,
				},
			},
			want: &piPb.CreatePiResponse{
				Status: rpc.StatusOk(),
				PaymentInstrument: &piPb.PaymentInstrument{
					Type: piPb.PaymentInstrumentType_UPI,
					Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
						Vpa:                    "test1@fede",
						AccountReferenceNumber: "random-acc-ref-num",
						MaskedAccountNumber:    "random-masked-acc-num",
						IfscCode:               "FI000001",
						AccountType:            accountsPb.Type_SAVINGS,
						Apo:                    account.AccountProductOffering_APO_REGULAR,
					}},
					VerifiedName: "Test verified name",
					State:        piPb.PaymentInstrumentState_CREATED,
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  true,
						piPb.Capability_OUTBOUND_TXN.String(): true,
					},
					IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
					Ownership:            piPb.Ownership_EPIFI_TECH,
				},
			},
			wantErr: false,
		},
		{
			name: "Should successfully create a UPI pi in CREATED state (with APO)",
			args: args{
				ctx: context.Background(),
				req: &piPb.CreatePiRequest{
					Type:                 piPb.PaymentInstrumentType_UPI,
					Identifier:           &piPb.CreatePiRequest_Upi{Upi: testUpiPiWithApoClone().GetUpi()},
					VerifiedName:         testUpiPiWithApo.GetVerifiedName(),
					IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
					State:                piPb.PaymentInstrumentState_CREATED,
				},
			},
			want: &piPb.CreatePiResponse{
				Status: rpc.StatusOk(),
				PaymentInstrument: &piPb.PaymentInstrument{
					Type: piPb.PaymentInstrumentType_UPI,
					Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
						Vpa:                    "test1@fede",
						AccountReferenceNumber: "random-acc-ref-num",
						MaskedAccountNumber:    "random-masked-acc-num",
						IfscCode:               "FI000001",
						AccountType:            accountsPb.Type_SAVINGS,
						Apo:                    account.AccountProductOffering_APO_NRE,
					}},
					VerifiedName: "Test verified name",
					State:        piPb.PaymentInstrumentState_CREATED,
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  true,
						piPb.Capability_OUTBOUND_TXN.String(): true,
					},
					IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
					Ownership:            piPb.Ownership_EPIFI_TECH,
				},
			},
			wantErr: false,
		},
		{
			name: "Should successfully create a UPI pi with numeric name",
			args: args{
				ctx: context.Background(),
				req: &piPb.CreatePiRequest{
					Type:                 piPb.PaymentInstrumentType_UPI,
					Identifier:           &piPb.CreatePiRequest_Upi{Upi: testUpiPiWithNumericName.GetUpi()},
					VerifiedName:         testUpiPiWithNumericName.GetVerifiedName(),
					IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
					State:                piPb.PaymentInstrumentState_CREATED,
				},
			},
			want: &piPb.CreatePiResponse{
				Status: rpc.StatusOk(),
				PaymentInstrument: &piPb.PaymentInstrument{
					Type: piPb.PaymentInstrumentType_UPI,
					Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
						Vpa:                    "test1@fede",
						AccountReferenceNumber: "random-acc-ref-num",
						MaskedAccountNumber:    "random-masked-acc-num",
						IfscCode:               "FI000001",
						AccountType:            accountsPb.Type_SAVINGS,
						Apo:                    account.AccountProductOffering_APO_NRE,
					}},
					VerifiedName: testUpiPiWithNumericName.GetVerifiedName(),
					State:        piPb.PaymentInstrumentState_CREATED,
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  true,
						piPb.Capability_OUTBOUND_TXN.String(): true,
					},
					IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
					Ownership:            piPb.Ownership_EPIFI_TECH,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean database, run migrations and load fixtures
			pkgTestv2.TruncateAndPopulateRdsFixtures(t, db, conf.PaymentInstrumentDb.GetName(), affectedTestTables)

			if tt.mockGetAccount.enable {
				mockSavingsClient.EXPECT().GetAccount(gomock.Any(), tt.mockGetAccount.req).
					Return(tt.mockGetAccount.res, tt.mockGetAccount.err)
			}

			if tt.mockGetSavingsEssentials.enable {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), tt.mockGetSavingsEssentials.req).
					Return(tt.mockGetSavingsEssentials.res, tt.mockGetSavingsEssentials.err)
			}

			got, err := piService.CreatePi(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreatePi() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// explicitly set id as it is generated by dao
			if got != nil && got.PaymentInstrument != nil {
				tt.want.PaymentInstrument.Id = got.PaymentInstrument.Id
			}

			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("piService.CreatePi() status: got = %v, want %v", got.Status, tt.want.Status)
				return
			}

			if !isDeepEqual(got.PaymentInstrument, tt.want.PaymentInstrument) {
				t.Errorf("CreatePi() got = %v, \nwant %v, \ndiff: %s", got, tt.want, cmp.Diff(got, tt.want, protocmp.Transform()))
				return
			}
		})
	}
}

func TestService_GetPi(t *testing.T) {

	ctr := gomock.NewController(t)
	mockPiDao := daoMocks.NewMockPiDao(ctr)

	piService = paymentinstrument.NewService(conf, mockPiDao, nil, nil, queueMock.NewMockPublisher(ctr), nil, nil, nil)

	type mockGetBankAccountPi struct {
		enable        bool
		accountNumber string
		ifscCode      string
		want          *piPb.PaymentInstrument
		err           error
	}

	type mockGetUpiPi struct {
		enable bool
		vpa    string
		want   *piPb.PaymentInstrument
		err    error
	}

	type mockGetPartialBankAccountPi struct {
		enable        bool
		accountNumber string
		name          string
		ifscCode      string
		want          *piPb.PaymentInstrument
		err           error
	}

	type mockGetGenericPi struct {
		enable        bool
		accountNumber string
		name          string
		ifscCode      string
		want          *piPb.PaymentInstrument
		err           error
	}

	type mockGetPartialUpiPi struct {
		enable bool
		vpa    string
		name   string
		want   *piPb.PaymentInstrument
		err    error
	}

	type args struct {
		ctx                         context.Context
		req                         *piPb.GetPiRequest
		mockGetBankAccountPi        mockGetBankAccountPi
		mockGetUpiPi                mockGetUpiPi
		mockGetPartialBankAccountPi mockGetPartialBankAccountPi
		mockGetGenericPi            mockGetGenericPi
		mockGetPartialUpiPi         mockGetPartialUpiPi
	}
	tests := []struct {
		name    string
		args    args
		want    *piPb.GetPiResponse
		wantErr bool
	}{
		{
			name: "Should successfully fetch PI of type BANK_ACCOUNT",
			args: args{
				ctx: context.Background(),
				mockGetBankAccountPi: mockGetBankAccountPi{
					enable:        true,
					accountNumber: fixturePi1.GetAccount().GetActualAccountNumber(),
					ifscCode:      fixturePi1.GetAccount().GetIfscCode(),
					want: &piPb.PaymentInstrument{
						Id:   fixturePi1.GetId(),
						Type: fixturePi1.GetType(),
					},
					err: nil,
				},
				req: &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.GetPiRequest_AccountRequestParams_{
						AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
							ActualAccountNumber: fixturePi1.GetAccount().ActualAccountNumber,
							IfscCode:            fixturePi1.GetAccount().IfscCode,
						},
					},
				},
			},
			want: &piPb.GetPiResponse{
				Status: rpc.StatusOk(),
				PaymentInstrument: &piPb.PaymentInstrument{
					Id:   fixturePi1.GetId(),
					Type: fixturePi1.GetType(),
				},
			},
			wantErr: false,
		},
		{
			name: "Should fail to fetch PI of type BANK_ACCOUNT due to not found error",
			args: args{
				ctx: context.Background(),
				mockGetBankAccountPi: mockGetBankAccountPi{
					enable:        true,
					accountNumber: fixturePi1.GetAccount().GetActualAccountNumber(),
					ifscCode:      "random-ifsc-code",
					err:           epifierrors.ErrRecordNotFound,
				},
				req: &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.GetPiRequest_AccountRequestParams_{
						AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
							ActualAccountNumber: fixturePi1.GetAccount().ActualAccountNumber,
							IfscCode:            "random-ifsc-code",
						},
					},
				},
			},
			want: &piPb.GetPiResponse{
				Status:            rpc.StatusRecordNotFound(),
				PaymentInstrument: nil,
			},
			wantErr: false,
		},
		{
			name: "Should successfully fetch PI of type PARTIAL_BANK_ACCOUNT",

			args: args{
				ctx: context.Background(),
				mockGetPartialBankAccountPi: mockGetPartialBankAccountPi{
					enable:        true,
					accountNumber: "account-1",
					ifscCode:      "FDRL0001001",
					name:          "test name",
					want: &piPb.PaymentInstrument{
						Id:   "pi-1",
						Type: piPb.PaymentInstrumentType_PARTIAL_BANK_ACCOUNT,
					},
					err: nil,
				},
				req: &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_PARTIAL_BANK_ACCOUNT,
					Identifier: &piPb.GetPiRequest_PartialAccountRequestParams_{
						PartialAccountRequestParams: &piPb.GetPiRequest_PartialAccountRequestParams{
							PartialAccountNumber: "account-1",
							IfscCode:             "FDRL0001001",
							Name:                 "test name",
						},
					},
				},
			},
			want: &piPb.GetPiResponse{
				Status: rpc.StatusOk(),
				PaymentInstrument: &piPb.PaymentInstrument{
					Id:   "pi-1",
					Type: piPb.PaymentInstrumentType_PARTIAL_BANK_ACCOUNT,
				},
			},
			wantErr: false,
		},
		{
			name: "Should successfully fetch PI of type PARTIAL_UPI",

			args: args{
				ctx: context.Background(),
				mockGetPartialUpiPi: mockGetPartialUpiPi{
					enable: true,
					vpa:    "vpa@f",
					name:   "test name",
					want: &piPb.PaymentInstrument{
						Id:   "pi-1",
						Type: piPb.PaymentInstrumentType_PARTIAL_UPI,
					},
					err: nil,
				},
				req: &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_PARTIAL_UPI,
					Identifier: &piPb.GetPiRequest_PartialUpiRequestParams_{
						PartialUpiRequestParams: &piPb.GetPiRequest_PartialUpiRequestParams{
							PartialVpa: "vpa@f",
							Name:       "test name",
						},
					},
				},
			},
			want: &piPb.GetPiResponse{
				Status: rpc.StatusOk(),
				PaymentInstrument: &piPb.PaymentInstrument{
					Id:   "pi-1",
					Type: piPb.PaymentInstrumentType_PARTIAL_UPI,
				},
			},
			wantErr: false,
		},
		{
			name: "Should successfully fetch PI of type GENERIC",

			args: args{
				ctx: context.Background(),
				mockGetGenericPi: mockGetGenericPi{
					enable:        true,
					accountNumber: "********",
					ifscCode:      "FDRL0001001",
					name:          "test name",
					want: &piPb.PaymentInstrument{
						Id:   "pi-1",
						Type: piPb.PaymentInstrumentType_GENERIC,
					},
					err: nil,
				},
				req: &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_GENERIC,
					Identifier: &piPb.GetPiRequest_GenericAccountRequestParams_{
						GenericAccountRequestParams: &piPb.GetPiRequest_GenericAccountRequestParams{
							AccountNumber: "********",
							IfscCode:      "FDRL0001001",
							Name:          "test name",
						},
					},
				},
			},
			want: &piPb.GetPiResponse{
				Status: rpc.StatusOk(),
				PaymentInstrument: &piPb.PaymentInstrument{
					Id:   "pi-1",
					Type: piPb.PaymentInstrumentType_GENERIC,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			if tt.args.mockGetBankAccountPi.enable {
				mockPiDao.EXPECT().GetBankAccountPI(tt.args.ctx, tt.args.mockGetBankAccountPi.accountNumber, tt.args.mockGetBankAccountPi.ifscCode).
					Return(tt.args.mockGetBankAccountPi.want, tt.args.mockGetBankAccountPi.err)
			}
			if tt.args.mockGetGenericPi.enable {
				mockPiDao.EXPECT().GetGenericPi(tt.args.ctx, tt.args.mockGetGenericPi.accountNumber, tt.args.mockGetGenericPi.ifscCode, tt.args.mockGetGenericPi.name).
					Return(tt.args.mockGetGenericPi.want, tt.args.mockGetGenericPi.err)
			}

			if tt.args.mockGetUpiPi.enable {
				mockPiDao.EXPECT().GetUpiPI(tt.args.ctx, tt.args.mockGetUpiPi.vpa).
					Return(tt.args.mockGetUpiPi.want, tt.args.mockGetUpiPi.err)
			}

			if tt.args.mockGetPartialBankAccountPi.enable {
				mockPiDao.EXPECT().GetPartialBankAccountPI(tt.args.ctx, tt.args.mockGetPartialBankAccountPi.accountNumber,
					tt.args.mockGetPartialBankAccountPi.ifscCode, tt.args.mockGetPartialBankAccountPi.name).
					Return(tt.args.mockGetPartialBankAccountPi.want, tt.args.mockGetPartialBankAccountPi.err)
			}

			if tt.args.mockGetPartialUpiPi.enable {
				mockPiDao.EXPECT().GetPartialUpiPI(tt.args.ctx, tt.args.mockGetPartialUpiPi.vpa, tt.args.mockGetPartialUpiPi.name).
					Return(tt.args.mockGetPartialUpiPi.want, tt.args.mockGetPartialUpiPi.err)
			}

			got, err := piService.GetPi(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBankAccountPi() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("GetBankAccountPi() status: got = %v, want %v", got.Status, tt.want.Status)
			}

			if !isDeepEqual(got.PaymentInstrument, tt.want.PaymentInstrument) {
				t.Errorf("GetBankAccountPi() got = %v, \nwant %v, \ndiff %s",
					got.PaymentInstrument, tt.want.PaymentInstrument, cmp.Diff(got.PaymentInstrument, tt.want.PaymentInstrument, protocmp.Transform()))
			}
		})
	}
}

func TestService_GetPIsByIds(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTestv2.TruncateAndPopulateRdsFixtures(t, db, conf.PaymentInstrumentDb.GetName(), affectedTestTables)

	piService = paymentinstrument.NewService(conf, piDao, nil, nil, nil, nil, nil, nil)

	type args struct {
		ctx context.Context
		req *piPb.GetPIsByIdsRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *piPb.GetPIsByIdsResponse
		wantErr bool
	}{
		{
			name: "Should successfully fetch all the PIs",
			args: args{
				ctx: context.Background(),
				req: &piPb.GetPIsByIdsRequest{
					Ids: []string{fixturePi1.Id},
				},
			},
			want: &piPb.GetPIsByIdsResponse{
				Status:             rpc.StatusOk(),
				Paymentinstruments: []*piPb.PaymentInstrument{fixturePi1},
			},
			wantErr: false,
		},
		{
			name: "Should successfully fetch all the PIs and ignore the not found ones",
			args: args{
				ctx: context.Background(),
				req: &piPb.GetPIsByIdsRequest{
					Ids: []string{fixturePi1.Id, "random-pi-id"},
				},
			},
			want: &piPb.GetPIsByIdsResponse{
				Status:             rpc.StatusOk(),
				Paymentinstruments: []*piPb.PaymentInstrument{fixturePi1},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := piService.GetPIsByIds(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPIsByIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("GetPIsByIds() status: got = %v, want %v", got.Status, tt.want.Status)
			}

			if len(got.Paymentinstruments) != len(tt.want.Paymentinstruments) {
				t.Errorf("GetPIsByIds() status: got = %v, want %v", got.Status, tt.want.Status)
				return
			}

			for i := range tt.want.Paymentinstruments {
				if !isDeepEqual(got.Paymentinstruments[i], tt.want.Paymentinstruments[i]) {
					t.Errorf("GetPIsByIds() index = %d, got %v, \nwant %v, \ndiff %s", i, got.Paymentinstruments[i],
						tt.want.Paymentinstruments[i], cmp.Diff(got.Paymentinstruments[i], tt.want.Paymentinstruments[i], protocmp.Transform()))
				}
			}
		})
	}
}

func TestService_UpdatePi(t *testing.T) {
	ctr := gomock.NewController(t)
	mockPiDao := daoMocks.NewMockPiDao(ctr)

	ctrl := gomock.NewController(&testing.T{})

	piService = paymentinstrument.NewService(conf, mockPiDao, nil, nil, queueMock.NewMockPublisher(ctrl), nil, nil, nil)

	type mockUpdatePi struct {
		enable    bool
		pi        *piPb.PaymentInstrument
		fieldMask []piPb.PaymentInstrumentFieldMask
		err       error
	}

	type mockGetById struct {
		enable bool
		id     string
		piData *piPb.PaymentInstrument
		err    error
	}

	tests := []struct {
		name         string
		req          *piPb.UpdatePiRequest
		mockUpdatePi mockUpdatePi
		mockGetById  mockGetById
		want         *piPb.UpdatePiResponse
		wantErr      bool
	}{
		{
			name: "error while updating the PI",
			req: &piPb.UpdatePiRequest{
				PaymentInstrument: &piPb.PaymentInstrument{
					Id:    "pi-1",
					Type:  piPb.PaymentInstrumentType_UPI,
					State: piPb.PaymentInstrumentState_CREATED,
				},
				UpdateFieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_STATE},
			},
			mockUpdatePi: mockUpdatePi{
				enable: true,
				pi: &piPb.PaymentInstrument{
					Id:    "pi-1",
					Type:  piPb.PaymentInstrumentType_UPI,
					State: piPb.PaymentInstrumentState_CREATED,
				},
				fieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_STATE},
				err:       fmt.Errorf("error updating pi"),
			},
			want:    &piPb.UpdatePiResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "should return successfully without updating PI if there is no field-mask remaining to be updated",
			req: &piPb.UpdatePiRequest{
				PaymentInstrument: &piPb.PaymentInstrument{
					Id:           "pi-1",
					VerifiedName: "*&",
				},
				UpdateFieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_VERIFIED_NAME},
			},
			mockGetById: mockGetById{
				enable: true,
				id:     "pi-1",
				piData: &piPb.PaymentInstrument{
					Id:           "pi-1",
					VerifiedName: "21",
				},
				err: nil,
			},
			want:    &piPb.UpdatePiResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
		{
			name: "should update the verified name successfully after checking whether its valid or not",
			req: &piPb.UpdatePiRequest{
				PaymentInstrument: &piPb.PaymentInstrument{
					Id:           "pi-1",
					VerifiedName: "John_Doe21!",
				},
				UpdateFieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_VERIFIED_NAME},
			},
			mockGetById: mockGetById{
				enable: true,
				id:     "pi-1",
				piData: &piPb.PaymentInstrument{
					Id:           "pi-1",
					VerifiedName: "John_Doe21!",
				},
				err: nil,
			},
			mockUpdatePi: mockUpdatePi{
				enable: true,
				pi: &piPb.PaymentInstrument{
					Id:           "pi-1",
					VerifiedName: "John_Doe21!",
				},
				fieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_VERIFIED_NAME},
				err:       nil,
			},
			want:    &piPb.UpdatePiResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
		{
			name: "updated Pi successfully",
			req: &piPb.UpdatePiRequest{
				PaymentInstrument: &piPb.PaymentInstrument{
					Id:    "pi-1",
					Type:  piPb.PaymentInstrumentType_UPI,
					State: piPb.PaymentInstrumentState_SUSPENDED,
				},
				UpdateFieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_STATE},
			},
			mockUpdatePi: mockUpdatePi{
				enable: true,
				pi: &piPb.PaymentInstrument{
					Id:    "pi-1",
					Type:  piPb.PaymentInstrumentType_UPI,
					State: piPb.PaymentInstrumentState_SUSPENDED,
				},
				fieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_STATE},
				err:       nil,
			},
			want:    &piPb.UpdatePiResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockUpdatePi.enable {
				mockPiDao.EXPECT().UpdatePi(context.Background(), tt.mockUpdatePi.pi,
					tt.mockUpdatePi.fieldMask, piPb.Source_SOURCE_UNSPECIFIED, "").
					Return(tt.mockUpdatePi.err)
			}
			if tt.mockGetById.enable {
				mockPiDao.EXPECT().GetById(context.Background(), tt.mockGetById.id).
					Return(tt.mockGetById.piData, tt.mockGetById.err)
			}
			got, err := piService.UpdatePi(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdatePi() got err: %v, want %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdatePi() got: %v, want: %v", got, tt.want)
				return
			}
		})
	}
}

func TestService_GetPiStateLog(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockPiStateLogDao := daoMocks.NewMockPiStatusLogDao(ctr)

	piService = paymentinstrument.NewService(conf, nil, mockPiStateLogDao, nil, queueMock.NewMockPublisher(ctr), nil,
		nil, nil)

	type mockGetByPiId struct {
		enable bool
		piId   string
		want   []*piPb.PaymentInstrumentStateLog
		err    error
	}

	tests := []struct {
		name          string
		req           *piPb.GetPiStateLogRequest
		want          *piPb.GetPiStateLogResponse
		mockGetByPiId mockGetByPiId
		wantErr       bool
	}{
		{
			name: "Go pi logs successfully",
			req:  &piPb.GetPiStateLogRequest{PiId: "pi-1"},
			mockGetByPiId: mockGetByPiId{
				enable: true,
				piId:   "pi-1",
				want: []*piPb.PaymentInstrumentStateLog{
					{
						PiId:   "pi-1",
						Source: piPb.Source_EPIFI_USER,
						Reason: "Disabled by user",
						State:  piPb.PaymentInstrumentState_SUSPENDED,
					},
				},
				err: nil,
			},
			want: &piPb.GetPiStateLogResponse{
				Status: rpc.StatusOk(),
				PiStateLogs: []*piPb.PaymentInstrumentStateLog{
					{
						PiId:   "pi-1",
						Source: piPb.Source_EPIFI_USER,
						Reason: "Disabled by user",
						State:  piPb.PaymentInstrumentState_SUSPENDED,
					},
				},
			},
		},
		{
			name: "Record not found",
			req:  &piPb.GetPiStateLogRequest{PiId: "pi-random"},
			mockGetByPiId: mockGetByPiId{
				enable: true,
				piId:   "pi-random",
				err:    nil,
			},
			want: &piPb.GetPiStateLogResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByPiId.enable {
				mockPiStateLogDao.EXPECT().GetByPiId(context.Background(), tt.mockGetByPiId.piId).
					Return(tt.mockGetByPiId.want, tt.mockGetByPiId.err)
			}

			got, err := piService.GetPiStateLog(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPiStateLog got err: %v, want: %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPiStateLog got: %v, want: %v", got, tt.want)
				return
			}
		})
	}
}

func TestService_GetPisByVpas(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTestv2.TruncateAndPopulateRdsFixtures(t, db, conf.PaymentInstrumentDb.GetName(), affectedTestTables)
	piIdgen := idgen.NewDomainIdGenerator(idgen.NewClock())
	txnExecutor := storageV2.NewCRDBIdempotentTxnExecutor(db)
	attrIdGen := idgen.NewAttributedIdGen[*dao.PaymentInstrumentAttribute](idgen.NewClock())
	dbResourceProviderPool, cleanup = InitConfigAndDBResourceProviderInstancePool()
	defer cleanup()
	dbResourceProvider, _ := dbResourceProviderPool.GetDbConnProviderWithUseCase(t)
	piDao2 := dao.NewPiDaoPgdb(db, piIdgen, txnExecutor, attrIdGen, dbResourceProvider, nil, false)
	piService = paymentinstrument.NewService(conf, piDao2, nil, nil, nil, nil, nil, nil)

	type args struct {
		ctx context.Context
		req *piPb.GetPisByVpasRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *piPb.GetPisByVpasResponse
		wantErr bool
	}{
		{
			name: "Should successfully fetch all the PIs",
			args: args{
				ctx: context.Background(),
				req: &piPb.GetPisByVpasRequest{
					Vpas: []string{"nitesh@fede"},
				},
			},
			want: &piPb.GetPisByVpasResponse{
				Status: rpc.StatusOk(),
				Paymentinstruments: []*piPb.PaymentInstrument{
					{
						Id:   "pi-3",
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
							Vpa: "nitesh@fede",
						}},
						VerifiedName:         "nitesh",
						State:                piPb.PaymentInstrumentState_CREATED,
						Capabilities:         map[string]bool{"INBOUND_TXN": true, "OUTBOUND_TXN": true},
						IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "Should successfully fetch all the PIs and ignore the not found ones",
			args: args{
				ctx: context.Background(),
				req: &piPb.GetPisByVpasRequest{
					Vpas: []string{"nitesh@fede", "random@fede"},
				},
			},
			want: &piPb.GetPisByVpasResponse{
				Status: rpc.StatusOk(),
				Paymentinstruments: []*piPb.PaymentInstrument{
					{
						Id:   "pi-3",
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
							Vpa: "nitesh@fede",
						}},
						VerifiedName:         "nitesh",
						State:                piPb.PaymentInstrumentState_CREATED,
						Capabilities:         map[string]bool{"INBOUND_TXN": true, "OUTBOUND_TXN": true},
						IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "no PI is found",
			args: args{
				ctx: context.Background(),
				req: &piPb.GetPisByVpasRequest{
					Vpas: []string{"1234@fede", "random@fede"},
				},
			},
			want: &piPb.GetPisByVpasResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := piService.GetPisByVpas(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPisByVpas() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("GetPisByVpas() status: got = %v, want %v", got.Status, tt.want.Status)
			}

			if len(got.Paymentinstruments) != len(tt.want.Paymentinstruments) {
				t.Errorf("GetPisByVpas() status: got = %v, want %v", got.Status, tt.want.Status)
				return
			}

			for i := range tt.want.Paymentinstruments {
				if !isDeepEqual(got.Paymentinstruments[i], tt.want.Paymentinstruments[i]) {
					t.Errorf("GetPisByVpas() index = %d, got %v, \nwant %v, \ndiff %s", i, got.Paymentinstruments[i],
						tt.want.Paymentinstruments[i], cmp.Diff(got.Paymentinstruments[i], tt.want.Paymentinstruments[i], protocmp.Transform()))
				}
			}
		})
	}
}

func TestService_CreatePiAndAccountPi(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockPaymentInstrumentDao := daoMocks.NewMockPiDao(ctrl)
	mockAccountPiRelationClient := mocks.NewMockAccountPIRelationClient(ctrl)
	defer ctrl.Finish()

	ctr := gomock.NewController(&testing.T{})
	piService = paymentinstrument.NewService(conf, mockPaymentInstrumentDao, nil, nil, queueMock.NewMockPublisher(ctr),
		nil, nil, mockAccountPiRelationClient)

	type mockCreatePiDao struct {
		enable  bool
		req     *piPb.PaymentInstrument
		res     *piPb.PaymentInstrument
		wantErr error
	}
	type mockGetPi struct {
		enable  bool
		cardId  string
		res     *piPb.PaymentInstrument
		wantErr error
	}
	type mockCreateAccountPiRelation struct {
		enable  bool
		req     *accountPIPb.CreateAccountPIRequest
		res     *accountPIPb.CreateAccountPIResponse
		wantErr error
	}

	type args struct {
		ctx context.Context
		req *piPb.CreatePiAndAccountPiRequest
	}
	tests := []struct {
		name                        string
		args                        args
		mockCreatePiDao             mockCreatePiDao
		mockGetPi                   mockGetPi
		mockCreateAccountPiRelation mockCreateAccountPiRelation
		want                        *piPb.CreatePiAndAccountPiResponse
		wantErr                     bool
	}{
		{
			name: "Should successfully create pi and account-pi relation for given pi details",
			args: args{
				ctx: context.Background(),
				req: &piPb.CreatePiAndAccountPiRequest{
					ActorId: "actor-1",
					Type:    piPb.PaymentInstrumentType_CREDIT_CARD,
					Identifier: &piPb.CreatePiAndAccountPiRequest_CreditCard{
						CreditCard: &piPb.CreditCard{
							Id: "credit-card-id",
						},
					},
					Ownership:            piPb.Ownership_EPIFI_TECH,
					IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
					AccountType:          accountsPb.Type_CREDIT_CARD_ACCOUNT,
					AccountId:            "credit-account-id",
				},
			},
			mockGetPi: mockGetPi{
				enable:  true,
				cardId:  "credit-card-id",
				wantErr: epifierrors.ErrRecordNotFound,
			},
			mockCreatePiDao: mockCreatePiDao{
				enable: true,
				req: &piPb.PaymentInstrument{
					Type:  piPb.PaymentInstrumentType_CREDIT_CARD,
					State: piPb.PaymentInstrumentState_CREATED,
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  true,
						piPb.Capability_OUTBOUND_TXN.String(): true,
					},
					Identifier: &piPb.PaymentInstrument_CreditCard{
						CreditCard: &piPb.CreditCard{
							Id: "credit-card-id",
						},
					},
					IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
					Ownership:            piPb.Ownership_EPIFI_TECH,
					LastVerifiedAt:       timestamppb.Now(),
				},
				res: creditCardPi,
			},
			mockCreateAccountPiRelation: mockCreateAccountPiRelation{
				enable: true,
				req: &accountPIPb.CreateAccountPIRequest{
					ActorId:     "actor-1",
					AccountId:   "credit-account-id",
					AccountType: accountsPb.Type_CREDIT_CARD_ACCOUNT,
					PiId:        "credit-card-pi",
				},
				res: &accountPIPb.CreateAccountPIResponse{
					Status: rpc.StatusOk(),
					AccountPi: &accountPIPb.AccountPI{
						Id:          "credit-card-account-pi-relation-id",
						ActorId:     "actor-1",
						AccountId:   "credit-account-id",
						AccountType: accountsPb.Type_CREDIT_CARD_ACCOUNT,
						PiId:        "credit-card-pi",
					},
				},
			},
			want: &piPb.CreatePiAndAccountPiResponse{
				Status: rpc.StatusOk(),
				Pi:     creditCardPi,
			},
		},
		{
			name: "Should successfully create pi and account-pi relation for given pi details (With Apo)",
			args: args{
				ctx: context.Background(),
				req: &piPb.CreatePiAndAccountPiRequest{
					ActorId: "actor-1",
					Type:    piPb.PaymentInstrumentType_CREDIT_CARD,
					Identifier: &piPb.CreatePiAndAccountPiRequest_CreditCard{
						CreditCard: &piPb.CreditCard{
							Id: "credit-card-id",
						},
					},
					Ownership:            piPb.Ownership_EPIFI_TECH,
					IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
					AccountType:          accountsPb.Type_CREDIT_CARD_ACCOUNT,
					Apo:                  account.AccountProductOffering_APO_NRE,
					AccountId:            "credit-account-id",
				},
			},
			mockGetPi: mockGetPi{
				enable:  true,
				cardId:  "credit-card-id",
				wantErr: epifierrors.ErrRecordNotFound,
			},
			mockCreatePiDao: mockCreatePiDao{
				enable: true,
				req: &piPb.PaymentInstrument{
					Type:  piPb.PaymentInstrumentType_CREDIT_CARD,
					State: piPb.PaymentInstrumentState_CREATED,
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  true,
						piPb.Capability_OUTBOUND_TXN.String(): true,
					},
					Identifier: &piPb.PaymentInstrument_CreditCard{
						CreditCard: &piPb.CreditCard{
							Id: "credit-card-id",
						},
					},
					IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
					Ownership:            piPb.Ownership_EPIFI_TECH,
					LastVerifiedAt:       timestamppb.Now(),
				},
				res: creditCardPi,
			},
			mockCreateAccountPiRelation: mockCreateAccountPiRelation{
				enable: true,
				req: &accountPIPb.CreateAccountPIRequest{
					ActorId:     "actor-1",
					AccountId:   "credit-account-id",
					AccountType: accountsPb.Type_CREDIT_CARD_ACCOUNT,
					Apo:         account.AccountProductOffering_APO_NRE,
					PiId:        "credit-card-pi",
				},
				res: &accountPIPb.CreateAccountPIResponse{
					Status: rpc.StatusOk(),
					AccountPi: &accountPIPb.AccountPI{
						Id:          "credit-card-account-pi-relation-id",
						ActorId:     "actor-1",
						AccountId:   "credit-account-id",
						AccountType: accountsPb.Type_CREDIT_CARD_ACCOUNT,
						Apo:         account.AccountProductOffering_APO_NRE,
						PiId:        "credit-card-pi",
					},
				},
			},
			want: &piPb.CreatePiAndAccountPiResponse{
				Status: rpc.StatusOk(),
				Pi:     creditCardPi,
			},
		},
		{
			name: "should return non-ok status since the create pi rpc failed",
			args: args{
				ctx: context.Background(),
				req: &piPb.CreatePiAndAccountPiRequest{
					ActorId: "actor-1",
					Type:    piPb.PaymentInstrumentType_CREDIT_CARD,
					Identifier: &piPb.CreatePiAndAccountPiRequest_CreditCard{
						CreditCard: &piPb.CreditCard{
							Id: "credit-card-id",
						},
					},
				},
			},
			mockGetPi: mockGetPi{
				enable:  true,
				cardId:  "credit-card-id",
				wantErr: epifierrors.ErrInvalidArgument,
			},
			want: &piPb.CreatePiAndAccountPiResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "should return non-ok status since the create account-pi relation rpc failed",
			args: args{
				ctx: context.Background(),
				req: &piPb.CreatePiAndAccountPiRequest{
					ActorId: "actor-1",
					Type:    piPb.PaymentInstrumentType_CREDIT_CARD,
					Identifier: &piPb.CreatePiAndAccountPiRequest_CreditCard{
						CreditCard: &piPb.CreditCard{
							Id: "credit-card-id",
						},
					},
					Ownership:            piPb.Ownership_EPIFI_TECH,
					IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
					AccountType:          accountsPb.Type_CREDIT_CARD_ACCOUNT,
					AccountId:            "credit-account-id",
				},
			},
			mockGetPi: mockGetPi{
				enable: true,
				cardId: "credit-card-id",
				res:    creditCardPi,
			},
			mockCreateAccountPiRelation: mockCreateAccountPiRelation{
				enable: true,
				req: &accountPIPb.CreateAccountPIRequest{
					ActorId:     "actor-1",
					AccountId:   "credit-account-id",
					AccountType: accountsPb.Type_CREDIT_CARD_ACCOUNT,
					PiId:        "credit-card-pi",
				},
				res: &accountPIPb.CreateAccountPIResponse{
					Status: rpc.StatusInternal(),
				},
			},
			want: &piPb.CreatePiAndAccountPiResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "rpc failed because no pi identifier is passed",
			args: args{
				ctx: context.Background(),
				req: &piPb.CreatePiAndAccountPiRequest{
					ActorId:              "actor-1",
					Type:                 piPb.PaymentInstrumentType_CREDIT_CARD,
					Ownership:            piPb.Ownership_EPIFI_TECH,
					IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
					AccountType:          accountsPb.Type_CREDIT_CARD_ACCOUNT,
					AccountId:            "credit-account-id",
				},
			},
			want: &piPb.CreatePiAndAccountPiResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetPi.enable {
				mockPaymentInstrumentDao.EXPECT().GetCreditCardPi(gomock.Any(), tt.mockGetPi.cardId).Return(tt.mockGetPi.res, tt.mockGetPi.wantErr)
			}

			if tt.mockCreatePiDao.enable {
				mockPaymentInstrumentDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(tt.mockCreatePiDao.res, tt.mockCreatePiDao.wantErr)
			}

			if tt.mockCreateAccountPiRelation.enable {
				mockAccountPiRelationClient.EXPECT().Create(gomock.Any(), tt.mockCreateAccountPiRelation.req).Return(tt.mockCreateAccountPiRelation.res, tt.mockCreateAccountPiRelation.wantErr)
			}

			got, err := piService.CreatePiAndAccountPi(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreatePiAndAccountPi() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("CreatePiAndAccountPi() status: got = %v, want %v", got.Status, tt.want.Status)
				return
			}

			if !isDeepEqual(got.Pi, tt.want.Pi) {
				t.Errorf("CreatePiAndAccountPi() got = %v, \nwant %v, \ndiff: %s", got, tt.want, cmp.Diff(got, tt.want, protocmp.Transform()))
				return
			}
		})
	}
}

// isDeepEqual compares 2 values of type *piPb.PaymentInstrument without the standard timestamp fields
// TODO(kunal): Had to duplicate this method for dao and service test. Is `helper_test.go` right way?
func isDeepEqual(actual, expected *piPb.PaymentInstrument) bool {
	if actual != nil && expected != nil {
		expected.CreatedAt = actual.GetCreatedAt()
		expected.UpdatedAt = actual.GetUpdatedAt()
		expected.DeletedAt = actual.GetDeletedAt()
		expected.LastVerifiedAt = actual.GetLastVerifiedAt()
	}
	return reflect.DeepEqual(actual, expected)
}
