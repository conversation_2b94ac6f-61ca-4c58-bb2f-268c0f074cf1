package constants

// cache prefixes
const (
	AccountPiIdPrefix          = "pi:accPiId:"   // AccountPiIdPrefix is used to fetch AccountPi details from piId
	PiIdPrefix                 = "pi:piId:"      // PiIdPrefix is used to get pi details from pi id
	BankAccountPiPrefix        = "pi:bAccPi:"    // BankAccountPiPrefix is used to get pi id from account number and ifsc code
	PartialBankAccountPiPrefix = "pi:pBAccPi:"   // PartialBankAccountPiPrefix is used to get pi id from account number, ifsc code and name
	PartialUpiPiPrefix         = "pi:pUpiPi:"    // PartialUpiPiPrefix is used to get pi id from vpa and name
	GenericPiPrefix            = "pi:genericPi"  // GenericPiPrefix is used to get pi id from account number, ifsc code and name
	UpiPiPrefix                = "pi:upiPi:"     // UpiPiPrefix is used to get pi id from vpa
	UpiLitePiPrefix            = "pi:upiLitePi:" // UpiLitePiPrefix is used to get pi id from lrn
)
