package paymentinstrument

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/epifi/gamma/pkg/pay"

	"context"
	"errors"
	"fmt"
	"regexp"
	"strings"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"

	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/paymentinstrument/config"
	"github.com/epifi/gamma/paymentinstrument/dao"
	piWireTypes "github.com/epifi/gamma/paymentinstrument/wire/types"

	"github.com/samber/lo"
	"go.uber.org/zap"
)

var (
	bankStrToEnum = map[string]commonvgpb.Vendor{
		"FEDERAL_BANK": commonvgpb.Vendor_FEDERAL_BANK,
	}

	statusIncorrectIFSC rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatus(uint32(piPb.CreatePiResponse_INCORRECT_IFSC), "incorrect ifsc code", "")
	}

	piTypeList = []piPb.PaymentInstrumentType{
		piPb.PaymentInstrumentType_BANK_ACCOUNT, piPb.PaymentInstrumentType_UPI, piPb.PaymentInstrumentType_PARTIAL_BANK_ACCOUNT,
		piPb.PaymentInstrumentType_PARTIAL_UPI, piPb.PaymentInstrumentType_GENERIC, piPb.PaymentInstrumentType_CREDIT_CARD,
		piPb.PaymentInstrumentType_DEBIT_CARD, piPb.PaymentInstrumentType_UPI_LITE}
)

// Service is server implementation of Pi Service
type Service struct {
	// UnimplementedPiServer is embedded to have forward compatible implementations
	piPb.UnimplementedPiServer
	piDao                   dao.PiDao
	piStatusLogDao          dao.PiStatusLogDao
	piPurgeAuditDao         dao.PiPurgeAuditDao
	piLastTransDao          dao.PiLastTransactionDao
	piEventPublisher        piWireTypes.PiEventPublisher
	savingClient            savingsPb.SavingsClient
	knownBanksIFSC          []*config.KnownIFSC
	accountPiRelationClient accountPiPb.AccountPIRelationClient
}

func NewService(
	conf *config.Config,
	piDao dao.PiDao,
	piStatusLogDao dao.PiStatusLogDao,
	piLastTransactionDao dao.PiLastTransactionDao,
	piEventPublisher piWireTypes.PiEventPublisher,
	savingsClient savingsPb.SavingsClient,
	piPurgeAuditDao dao.PiPurgeAuditDao,
	accountPiRelationClient accountPiPb.AccountPIRelationClient,
) *Service {
	return &Service{
		piDao:                   piDao,
		piStatusLogDao:          piStatusLogDao,
		piPurgeAuditDao:         piPurgeAuditDao,
		piLastTransDao:          piLastTransactionDao,
		piEventPublisher:        piEventPublisher,
		knownBanksIFSC:          conf.KnownBanksIFSC,
		savingClient:            savingsClient,
		accountPiRelationClient: accountPiRelationClient,
	}
}

// CreatePi creates a PI
// If the PI is already present, it returns the existing PI
//
//nolint:funlen
func (s *Service) CreatePi(ctx context.Context, req *piPb.CreatePiRequest) (*piPb.CreatePiResponse, error) {
	var (
		savedPi         *piPb.PaymentInstrument
		err             error
		res             = &piPb.CreatePiResponse{}
		isIfscIncorrect bool
		piOwnership     = req.GetOwnership()
	)

	// updating the ctx with ownership we got from request
	ctx = epificontext.WithOwnership(ctx, pay.PiOwnershipToCommonOwnership[piOwnership])

	var getResStatus = func(isIfscIncorrect bool) *rpc.Status {
		if !isIfscIncorrect {
			return rpc.StatusOk()
		}

		return statusIncorrectIFSC()
	}

	pi := piPb.PaymentInstrument{
		Type:  req.Type,
		State: piPb.PaymentInstrumentState_CREATED,

		// create with all possible capabilities
		// expose this field later to create PI with different capabilities
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		// ownership of pi, if incoming ownership is nil, set to default `EPIFI_TECH` ownership
		Ownership: piOwnership,
	}

	if capability, ok := req.GetCapabilities()[piPb.Capability_INBOUND_TXN.String()]; ok {
		pi.Capabilities[piPb.Capability_INBOUND_TXN.String()] = capability
	}
	if capability, ok := req.GetCapabilities()[piPb.Capability_OUTBOUND_TXN.String()]; ok {
		pi.Capabilities[piPb.Capability_OUTBOUND_TXN.String()] = capability
	}

	switch req.GetIdentifier().(type) {
	case *piPb.CreatePiRequest_Account:
		pi.Identifier = &piPb.PaymentInstrument_Account{Account: req.GetAccount()}
	case *piPb.CreatePiRequest_Card:
		pi.Identifier = &piPb.PaymentInstrument_Card{Card: req.GetCard()}
	case *piPb.CreatePiRequest_Upi:
		pi.Identifier = &piPb.PaymentInstrument_Upi{Upi: req.GetUpi()}
		req.GetUpi().Vpa = strings.ToLower(req.GetUpi().GetVpa())
		if req.GetVerifiedName() == "" {
			logger.Error(ctx, fmt.Sprintf("Verified name is empty for PI of type UPI. Identifier: %v", req.GetIdentifier()))
			res.Status = rpc.StatusInvalidArgument()
			return res, nil
		}
	case *piPb.CreatePiRequest_CreditCard:
		pi.Identifier = &piPb.PaymentInstrument_CreditCard{CreditCard: req.GetCreditCard()}
	case *piPb.CreatePiRequest_UpiLite:
		pi.Identifier = &piPb.PaymentInstrument_UpiLite{UpiLite: req.GetUpiLite()}
	default:
		logger.Error(ctx, fmt.Sprintf("invalid identifier: %s", req.GetIdentifier()), zap.Error(err))
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	pi.VerifiedName = req.GetVerifiedName()
	trimmedVerifiedName := parseAndTrimVerifiedName(pi.GetVerifiedName())
	if len(trimmedVerifiedName) == 0 {
		logger.WarnWithCtx(ctx, "got invalid verified name to create PI", zap.String(logger.VERIFIED_NAME, pi.GetVerifiedName()))
		pi.VerifiedName = trimmedVerifiedName
	}

	pi.IssuerClassification = req.GetIssuerClassification()

	if pi.IssuerClassification == piPb.PaymentInstrumentIssuer_PAYMENT_INSTRUMENT_ISSUER_UNSPECIFIED {
		pi.IssuerClassification = piPb.PaymentInstrumentIssuer_EXTERNAL
	}

	// in case pi is created with a verified name, it means the verified name of the PI is already known
	// Hence, create the PI in verified state
	if pi.GetVerifiedName() != "" {
		pi.State = piPb.PaymentInstrumentState_VERIFIED
	}
	// if state is passed explicitly in the request we will use it
	if req.GetState() != piPb.PaymentInstrumentState_PAYMENT_INSTRUMENT_STATE_UNSPECIFIED {
		pi.State = req.GetState()
	}

	// Check if the PI already exists if PI type is BANK_ACCOUNT , UPI , CREDIT_CARD OR DEBIT_CARD
	if lo.Contains[piPb.PaymentInstrumentType](piTypeList, pi.Type) {
		var (
			fetchedPi *piPb.PaymentInstrument
			fetchErr  error
		)
		switch pi.Type {
		case piPb.PaymentInstrumentType_BANK_ACCOUNT:

			// validate if known IFSC code and swap with IFSC with correct value in case
			// ifsc is wrong
			if pi.IssuerClassification != piPb.PaymentInstrumentIssuer_INTERNAL {
				var correctIFSC string
				correctIFSC, err = s.validateIfKnownIfscCode(ctx, req.GetAccount().GetActualAccountNumber(), req.GetAccount().GetIfscCode())
				if err != nil {
					logger.Error(ctx, "failed to validate IFSC code", zap.Error(err))
					res.Status = rpc.StatusInternal()
					return res, nil
				}

				if correctIFSC != "" {
					isIfscIncorrect = true
					req.GetAccount().IfscCode = correctIFSC
				}
			}

			fetchedPi, fetchErr = s.piDao.GetBankAccountPI(ctx,
				req.GetAccount().GetActualAccountNumber(),
				req.GetAccount().GetIfscCode())
		case piPb.PaymentInstrumentType_UPI:
			fetchedPi, fetchErr = s.piDao.GetUpiPI(ctx, req.GetUpi().GetVpa())
		case piPb.PaymentInstrumentType_PARTIAL_BANK_ACCOUNT:
			fetchedPi, fetchErr = s.piDao.GetPartialBankAccountPI(ctx,
				req.GetAccount().GetActualAccountNumber(), req.GetAccount().GetIfscCode(),
				req.GetAccount().GetName())
		case piPb.PaymentInstrumentType_PARTIAL_UPI:
			fetchedPi, fetchErr = s.piDao.GetPartialUpiPI(ctx, req.GetUpi().GetVpa(), req.GetUpi().GetName())
		case piPb.PaymentInstrumentType_GENERIC:
			fetchedPi, fetchErr = s.piDao.GetGenericPi(ctx, req.GetAccount().GetActualAccountNumber(), req.GetAccount().GetIfscCode(), req.GetAccount().GetName())
		case piPb.PaymentInstrumentType_CREDIT_CARD:
			fetchedPi, fetchErr = s.piDao.GetCreditCardPi(ctx, req.GetCreditCard().GetId())
		case piPb.PaymentInstrumentType_DEBIT_CARD:
			fetchedPi, fetchErr = s.piDao.GetDebitCardPi(ctx, req.GetCard().GetSecureCardNumber(), req.GetCard().GetExpiry(), req.GetCard().GetName())
		case piPb.PaymentInstrumentType_UPI_LITE:
			fetchedPi, fetchErr = s.piDao.GetUpiLitePI(ctx, req.GetUpiLite().GetLrn())

			// If Pi for the upi lite already exists then update the pi state to created
			if fetchErr == nil && fetchedPi != nil {
				fetchedPi.State = piPb.PaymentInstrumentState_CREATED
				if updateErr := s.piDao.UpdatePi(ctx, fetchedPi, []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_STATE},
					piPb.Source_SYSTEM, "Get Lite Request from user"); updateErr != nil {

					logger.Error(ctx, "error while updating Pi for the upi lite PI", zap.String(logger.PI_ID, fetchedPi.GetId()),
						zap.Error(err))
					res.Status = rpc.StatusInternal()
					return res, nil
				}

				res.PaymentInstrument = fetchedPi
				res.Status = rpc.StatusOk()
				return res, nil
			}
		}

		// if error is present and is not of type epifierrors.ErrRecordNotFound then return error
		if fetchErr != nil && !errors.Is(fetchErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "failed to fetch PI", zap.Error(fetchErr))
			res.Status = rpc.StatusInternal()
			return res, nil
		} else if fetchedPi != nil {
			if shouldUpdateOwnership(fetchedPi, &pi) {
				ownershipUpdateErr := s.updatePiOwnershipAndPublishEvent(ctx, fetchedPi)
				if ownershipUpdateErr != nil {
					logger.Error(ctx, "error in updating pi-ownership from wealth->tech", zap.String(logger.PI_ID, fetchedPi.GetId()), zap.Error(err))
					res.Status = rpc.StatusInternal()
					return res, nil
				}
			}
			res.Status = getResStatus(isIfscIncorrect)
			res.PaymentInstrument = fetchedPi
			return res, nil
		}
	}
	if pi.GetState() == piPb.PaymentInstrumentState_VERIFIED || pi.IsIssuedInternally() {
		pi.LastVerifiedAt = timestampPb.Now()
	}

	if savedPi, err = s.piDao.Create(ctx, &pi); err != nil {
		logger.Error(ctx, "failed to create PI", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.Status = getResStatus(isIfscIncorrect)
	res.PaymentInstrument = savedPi
	return res, nil
}

func (s *Service) GetPi(ctx context.Context, req *piPb.GetPiRequest) (*piPb.GetPiResponse, error) {
	var (
		fetchedPi *piPb.PaymentInstrument
		err       error
		res       = &piPb.GetPiResponse{}
	)

	switch req.Type {
	case piPb.PaymentInstrumentType_BANK_ACCOUNT:
		fetchedPi, err = s.piDao.GetBankAccountPI(ctx,
			req.GetAccountRequestParams().GetActualAccountNumber(),
			req.GetAccountRequestParams().GetIfscCode())
	case piPb.PaymentInstrumentType_UPI:
		vpa := strings.ToLower(req.GetUpiRequestParams().GetVpa())
		fetchedPi, err = s.piDao.GetUpiPI(ctx, vpa)
	case piPb.PaymentInstrumentType_DEBIT_CARD:
		fetchedPi, err = s.piDao.GetDebitCardPi(ctx, req.GetDebitCardRequestParams().GetTokenizedCardNumber(),
			req.GetDebitCardRequestParams().GetExpiry(),
			req.GetDebitCardRequestParams().GetName())
	case piPb.PaymentInstrumentType_PARTIAL_BANK_ACCOUNT:
		fetchedPi, err = s.piDao.GetPartialBankAccountPI(ctx, req.GetPartialAccountRequestParams().GetPartialAccountNumber(),
			req.GetPartialAccountRequestParams().GetIfscCode(), req.GetPartialAccountRequestParams().GetName())
	case piPb.PaymentInstrumentType_PARTIAL_UPI:
		fetchedPi, err = s.piDao.GetPartialUpiPI(ctx, req.GetPartialUpiRequestParams().GetPartialVpa(),
			req.GetPartialUpiRequestParams().GetName())
	case piPb.PaymentInstrumentType_GENERIC:
		fetchedPi, err = s.piDao.GetGenericPi(ctx, req.GetGenericAccountRequestParams().GetAccountNumber(),
			req.GetGenericAccountRequestParams().GetIfscCode(), req.GetGenericAccountRequestParams().GetName())
	case piPb.PaymentInstrumentType_UPI_LITE:
		fetchedPi, err = s.piDao.GetUpiLitePI(ctx, req.GetUpiLiteRequestParams().GetLrn())
	default:
		fetchedPi, err = nil, fmt.Errorf("invalid type: %s", req.Type)
	}

	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
		} else {
			logger.Error(ctx, "failed to get PI", zap.Error(err))
			res.Status = rpc.StatusInternal()
		}
		return res, nil
	}

	// update pi ownership if fethced-pi's ownership is `wealth` and incoming pi's ownership is `tech`

	res.Status = rpc.StatusOk()
	res.PaymentInstrument = fetchedPi
	return res, nil
}

func (s *Service) GetPiById(ctx context.Context, req *piPb.GetPiByIdRequest) (*piPb.GetPiByIdResponse, error) {
	var (
		fetchedPi *piPb.PaymentInstrument
		err       error
		res       = &piPb.GetPiByIdResponse{}
	)

	if fetchedPi, err = s.piDao.GetById(ctx, req.Id); err != nil {
		logger.Error(ctx, "failed to get PI by id", zap.String("pi_id", req.Id), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.Status = rpc.StatusOk()
	res.PaymentInstrument = fetchedPi
	return res, nil
}

// Get PIs by id
// If any of the PI Id is not found, remaining PIs are returned.
// In this case it is upto the caller to decide if this is an error or not.
func (s *Service) GetPIsByIds(ctx context.Context, req *piPb.GetPIsByIdsRequest) (*piPb.GetPIsByIdsResponse, error) {
	var (
		fetchedPIs []*piPb.PaymentInstrument
		err        error
		res        = &piPb.GetPIsByIdsResponse{}
	)

	if fetchedPIs, err = s.piDao.GetByIds(ctx, req.Ids); err != nil {
		logger.Error(ctx, fmt.Sprintf("failed to get PIs by ids: %v", req.Ids), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.Status = rpc.StatusOk()
	res.Paymentinstruments = fetchedPIs
	return res, nil
}

// UpdatePi updates the Pi fields present in the update fields mask
// verifies if the field masks is eligible to be update for the pi type
// for eg. ACCOUNT_REFERENCE_NUMBER_UPI can be only used with pi of type upi
func (s *Service) UpdatePi(ctx context.Context, req *piPb.UpdatePiRequest) (*piPb.UpdatePiResponse, error) {
	var (
		err error
		res = &piPb.UpdatePiResponse{}
		pi  *piPb.PaymentInstrument
	)

	// logging any changes around verified name + checking if the new verified name is valid or not post sanitization
	if checkIfVerifiedNameIsPresentInFieldMask(req.GetUpdateFieldMask()) {
		piData, getPiByIdErr := s.piDao.GetById(ctx, req.GetPaymentInstrument().GetId())
		if getPiByIdErr != nil {
			logger.Error(ctx, "error in getting Pi details from DB", zap.String(logger.PI_ID, piData.GetId()), zap.Error(getPiByIdErr))
		} else if piData.GetType() == piPb.PaymentInstrumentType_BANK_ACCOUNT {
			// logging the diff in case of verified name change for bank account PI to check for PI recycling cases
			checkAndLogForVerifyNameChange(ctx, req.GetPaymentInstrument().GetVerifiedName(), piData.GetVerifiedName(), req.GetPaymentInstrument().GetId())
		}

		// Note: we are NOT using the trimmed name as the new verified-name. It is used just to validate whether the
		// original name passed is a good fit for the verified-name or not.
		if len(parseAndTrimVerifiedName(req.GetPaymentInstrument().GetVerifiedName())) == 0 {
			logger.WarnWithCtx(ctx, "got invalid verified name to update PI", zap.String(logger.VERIFIED_NAME, req.GetPaymentInstrument().GetVerifiedName()),
				zap.String(logger.PI_ID, req.GetPaymentInstrument().GetId()),
			)
			req.UpdateFieldMask = removeVerifiedNameFromFieldMask(req.GetUpdateFieldMask())
		}
		// todo(rohan): should we implicitly add the PI State field-mask if we are getting a valid name? i.e. mark the PI as verified?
	}

	// early return if there is nothing to be updated
	if len(lo.Without(req.GetUpdateFieldMask(), piPb.PaymentInstrumentFieldMask_PAYMENT_INSTRUMENT_FIELD_MASK_UNSPECIFIED)) == 0 {
		logger.WarnWithCtx(ctx, "not updating PI as no field-mask is left", zap.String(logger.PI_ID, pi.GetId()))
		res.Status = rpc.StatusOk()
		return res, nil
	}

	pi = req.GetPaymentInstrument()

	err = s.piDao.UpdatePi(ctx, pi, req.GetUpdateFieldMask(), req.GetSource(), req.GetUpdateReason())

	switch {
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		logger.Error(ctx, "invalid argument passed for updating pi",
			zap.String(logger.PI_ID, pi.GetId()), zap.Error(err))
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error updating pi", zap.String(logger.PI_ID, pi.GetId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	// publish pi event
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		s.PublishPiEvent(ctx, pi)
	})

	res.Status = rpc.StatusOk()
	return res, nil
}

// rpc to get the list of pis state log for a given pi id
// returns status record not found if no entry is present for the pi id
func (s *Service) GetPiStateLog(ctx context.Context, req *piPb.GetPiStateLogRequest) (*piPb.GetPiStateLogResponse, error) {
	var (
		res = &piPb.GetPiStateLogResponse{}
	)
	piStateLogs, err := s.piStatusLogDao.GetByPiId(ctx, req.GetPiId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "no record found", zap.String(logger.PI_ID, req.GetPiId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error fetching pi state logs", zap.String(logger.PI_ID, req.GetPiId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	case len(piStateLogs) == 0:
		logger.Info(ctx, "no record found", zap.String(logger.PI_ID, req.GetPiId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	}
	res.Status = rpc.StatusOk()
	res.PiStateLogs = piStateLogs
	return res, nil
}

func (s *Service) PublishPiEvent(ctx context.Context, pi *piPb.PaymentInstrument) {
	messageId, err := s.piEventPublisher.Publish(ctx, &accountPiPb.AccountPiCreateOrUpdateEvent{
		Pi:        pi,
		EventType: accountPiPb.PiEventType_UPDATE,
	})
	if err != nil {
		logger.Error(ctx, "error in publishing pi-update-event", zap.Error(err), zap.String(logger.PI_ID, pi.GetId()))
		return
	}
	logger.Info(ctx, "successfully published pi-update-event", zap.String(logger.PI_ID, pi.GetId()), zap.String(logger.QUEUE_MESSAGE_ID, messageId))
}

// validateIfKnownIfscCode checks if the ifsc code sent while creating PI is one of known IFSCs.
// in case yes, then entry in the internal database is enquired to check if any account exists after
// replacing entered IFSC code with the correct one.
// This helps in avoiding creation of multiple PIs for same account number but with different IFSC codes.
// The IFSC code can vary due to numerous reasons. e.g. user entered wrong IFSC on app while making payment
// or some external user makes a payment to epiFi issued bank PI and entered wrong IFSC (since, bank payment
// systems are agnostic to ifsc codes)
func (s *Service) validateIfKnownIfscCode(ctx context.Context, accountNo, ifscCode string) (string, error) {
	var (
		match  bool
		vendor commonvgpb.Vendor
	)

	for _, kb := range s.knownBanksIFSC {
		match = strings.HasPrefix(ifscCode, kb.Prefix)

		if match {
			vendor = bankStrToEnum[kb.Bank]
			break
		}
	}

	if !match {
		return "", nil
	}

	// right now we just check savings account, but this logic can be further extended to deposit account
	// as well as connected accounts.
	// This is crucial if we map an internal account to same PI as it further impacts user's visibility
	// of transactions on the app
	savingsEssentials, savingsEssentialserr := s.savingClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
		Filter: &savingsPb.GetSavingsAccountEssentialsRequest_AccountNumBankFilter{
			AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
				AccountNumber: accountNo,
				PartnerBank:   vendor,
			},
		},
	})
	switch {
	case savingsEssentialserr != nil:
		return "", fmt.Errorf("failed to fetch the savings account essentials %w", savingsEssentialserr)
	case savingsEssentials.GetStatus().IsRecordNotFound():
		return "", nil
	case !savingsEssentials.GetStatus().IsSuccess() || savingsEssentialserr != nil:
		return "", fmt.Errorf("failed to fetch the savings account essentials %w", savingsEssentialserr)
	case savingsEssentials.GetAccount().GetIfscCode() != ifscCode:
		return savingsEssentials.GetAccount().GetIfscCode(), nil
	default:
		return "", nil
	}
}

// update ownership of pi from epifi_wealth to epifi_tech.
// This is needed for aa txns. In case a fi-user uses epifi_wealth pi to make fi-txn, Or fi-user makes a txn to epifi_wealth pi
func (s *Service) updatePiOwnershipAndPublishEvent(ctx context.Context, pi *piPb.PaymentInstrument) error {
	var (
		err error
	)

	pi.Ownership = piPb.Ownership_EPIFI_TECH
	err = s.piDao.UpdatePi(
		ctx,
		pi,
		[]piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_OWNERSHIP},
		piPb.Source_SYSTEM,
		"ownership change to tech")

	switch {
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		logger.Error(ctx, "invalid argument passed for updating pi",
			zap.String(logger.PI_ID, pi.GetId()), zap.Error(err))
		return fmt.Errorf("invalid argument passed for updating pi: %w", err)
	case err != nil:
		logger.Error(ctx, "error updating pi", zap.String(logger.PI_ID, pi.GetId()), zap.Error(err))
		return fmt.Errorf("error updating pi: %w", err)
	}
	// publish pi event
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		s.PublishPiEvent(ctx, pi)
	})
	return nil
}

func (s *Service) GetPisByVpas(ctx context.Context, req *piPb.GetPisByVpasRequest) (*piPb.GetPisByVpasResponse, error) {
	var (
		fetchedPIs []*piPb.PaymentInstrument
		err        error
		res        = &piPb.GetPisByVpasResponse{}
	)
	if fetchedPIs, err = s.piDao.GetByVpas(ctx, req.Vpas); err != nil {
		switch {
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			res.Status = rpc.StatusRecordNotFound()
		default:
			logger.Error(ctx, fmt.Sprintf("failed to get PIs by vpas; number of VPAs: %d", len(req.GetVpas())), zap.Error(err))
			res.Status = rpc.StatusInternal()
		}
		return res, nil
	}
	if len(fetchedPIs) == 0 {
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	}
	res.Status = rpc.StatusOk()
	res.Paymentinstruments = fetchedPIs
	return res, nil
}

func (s *Service) CreatePiAndAccountPi(ctx context.Context, req *piPb.CreatePiAndAccountPiRequest) (*piPb.CreatePiAndAccountPiResponse, error) {
	var res = &piPb.CreatePiAndAccountPiResponse{}
	createPiReq := &piPb.CreatePiRequest{
		Type:                 req.GetType(),
		VerifiedName:         req.GetVerifiedName(),
		Capabilities:         req.GetCapabilities(),
		IssuerClassification: req.GetIssuerClassification(),
		Ownership:            req.GetOwnership(),
		State:                req.GetState(),
	}
	switch req.Identifier.(type) {
	case *piPb.CreatePiAndAccountPiRequest_Account:
		createPiReq.Identifier = &piPb.CreatePiRequest_Account{
			Account: req.GetAccount(),
		}
	case *piPb.CreatePiAndAccountPiRequest_Card:
		createPiReq.Identifier = &piPb.CreatePiRequest_Card{
			Card: req.GetCard(),
		}
	case *piPb.CreatePiAndAccountPiRequest_Upi:
		createPiReq.Identifier = &piPb.CreatePiRequest_Upi{
			Upi: req.GetUpi(),
		}
	case *piPb.CreatePiAndAccountPiRequest_CreditCard:
		createPiReq.Identifier = &piPb.CreatePiRequest_CreditCard{
			CreditCard: req.GetCreditCard(),
		}
	default:
		logger.Error(ctx, fmt.Sprintf("invalid pi identifier: %s", req.GetIdentifier()))
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}
	// create pi (fetch if already exists)
	createPiRes, createPiErr := s.CreatePi(ctx, createPiReq)

	switch {
	case createPiErr != nil:
		logger.Error(ctx, "Create pi rpc failed", zap.Error(createPiErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	case !createPiRes.GetStatus().IsSuccess() && !createPiRes.GetStatus().IsAlreadyExists():
		logger.Error(ctx, "Create pi rpc failed", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	createAccountPiReq := &accountPiPb.CreateAccountPIRequest{
		ActorId:     req.GetActorId(),
		AccountType: req.GetAccountType(),
		Apo:         req.GetApo(),
		AccountId:   req.GetAccountId(),
		PiId:        createPiRes.GetPaymentInstrument().GetId(),
	}
	// create account pi relation (fetch if already exists)
	createAccountPiRes, CreateAccountPiErr := s.accountPiRelationClient.Create(ctx, createAccountPiReq)

	switch {
	case CreateAccountPiErr != nil:
		logger.Error(ctx, "Create account pi rpc failed", zap.Error(CreateAccountPiErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	case !createAccountPiRes.GetStatus().IsSuccess() && !createAccountPiRes.GetStatus().IsAlreadyExists():
		logger.Error(ctx, "Create account pi rpc failed", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	res.Pi = createPiRes.GetPaymentInstrument()
	res.Status = rpc.StatusOk()
	return res, nil
}

// pi ownership should only be updated from epifi_wealth to epifi_tech and not vice versa
func shouldUpdateOwnership(fetchedPi, incomingPi *piPb.PaymentInstrument) bool {
	return incomingPi.Ownership == piPb.Ownership_EPIFI_TECH && fetchedPi.Ownership == piPb.Ownership_EPIFI_WEALTH
}

func checkIfVerifiedNameIsPresentInFieldMask(updateMask []piPb.PaymentInstrumentFieldMask) bool {
	if lo.Contains(updateMask, piPb.PaymentInstrumentFieldMask_VERIFIED_NAME) {
		return true
	}

	return false
}

func removeVerifiedNameFromFieldMask(updateMask []piPb.PaymentInstrumentFieldMask) []piPb.PaymentInstrumentFieldMask {
	return lo.ReplaceAll(updateMask, piPb.PaymentInstrumentFieldMask_VERIFIED_NAME, piPb.PaymentInstrumentFieldMask_PAYMENT_INSTRUMENT_FIELD_MASK_UNSPECIFIED)
}

func checkAndLogForVerifyNameChange(ctx context.Context, nameFromReq, nameFromDB, piId string) {
	verifiedNameFromDB := parseAndTrimVerifiedName(nameFromDB)
	verifiedNameFromReq := parseAndTrimVerifiedName(nameFromReq)

	if verifiedNameFromDB != "" && verifiedNameFromDB != verifiedNameFromReq {
		logger.Error(ctx, "verified name changed for pi", zap.String(logger.DB_NAME, mask.GetMaskedString(mask.DontMaskFirstTwoAndLastTwoChars, verifiedNameFromDB)),
			zap.String(logger.VERIFIED_NAME, mask.GetMaskedString(mask.DontMaskFirstTwoAndLastTwoChars, verifiedNameFromReq)), zap.String(logger.PI_ID, piId),
		)
	}
}

// parseAndTrimVerifiedName : performs validations and returns the validated verified-name.
//
// How to use this value?
// - In case this returns an empty string, it means the name originally passed is not eligible to be used as a verified name (i.e. for verified state PI)
func parseAndTrimVerifiedName(verifiedName string) string {
	return regexp.MustCompile(`[^a-zA-Z0-9]+`).ReplaceAllString(verifiedName, "")
}
