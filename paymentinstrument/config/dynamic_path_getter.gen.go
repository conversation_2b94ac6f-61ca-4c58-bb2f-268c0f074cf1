// Code generated by tools/conf_gen/dynamic_conf_gen.go
package config

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enableentitysegregation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableEntitySegregation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableEntitySegregation, nil
	case "aaaccountpipurgesubscriber":
		return obj.AaAccountPiPurgeSubscriber.Get(dynamicFieldPath[1:])
	case "pipurgesubscriber":
		return obj.PiPurgeSubscriber.Get(dynamicFieldPath[1:])
	case "accountpicacheconfig":
		return obj.AccountPiCacheConfig.Get(dynamicFieldPath[1:])
	case "picacheconfig":
		return obj.PiCacheConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AccountPiCacheConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iscachingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCachingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCachingEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AccountPiCacheConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *PiCacheConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iscachingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCachingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCachingEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for PiCacheConfig", strings.Join(dynamicFieldPath, "."))
	}
}
