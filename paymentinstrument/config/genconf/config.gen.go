// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"fmt"
	"reflect"
	"strings"
	"sync/atomic"

	time "time"

	questtypes "github.com/epifi/be-common/api/quest/types"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	config "github.com/epifi/gamma/paymentinstrument/config"
)

type Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_EnableEntitySegregation    uint32
	_AaAccountPiPurgeSubscriber *gencfg.SqsSubscriber
	_PiPurgeSubscriber          *gencfg.SqsSubscriber
	_AccountPiCacheConfig       *AccountPiCacheConfig
	_PiCacheConfig              *PiCacheConfig
	_Application                *config.Application
	_Server                     *config.Server
	_Logging                    *cfg.Logging
	_PaymentInstrumentDb        *cfg.DB
	_AWS                        *cfg.AWS
	_PiEventPublisher           *cfg.SnsPublisher
	_Flags                      *config.Flags
	_RedisOptions               *cfg.RedisOptions
	_KnownBanksIFSC             []*config.KnownIFSC
	_AaTxnPurgePublisher        *cfg.SqsPublisher
	_Tracing                    *cfg.Tracing
	_Secrets                    *cfg.Secrets
}

func (obj *Config) EnableEntitySegregation() bool {
	if atomic.LoadUint32(&obj._EnableEntitySegregation) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) AaAccountPiPurgeSubscriber() *gencfg.SqsSubscriber {
	return obj._AaAccountPiPurgeSubscriber
}
func (obj *Config) PiPurgeSubscriber() *gencfg.SqsSubscriber {
	return obj._PiPurgeSubscriber
}
func (obj *Config) AccountPiCacheConfig() *AccountPiCacheConfig {
	return obj._AccountPiCacheConfig
}
func (obj *Config) PiCacheConfig() *PiCacheConfig {
	return obj._PiCacheConfig
}
func (obj *Config) Application() *config.Application {
	return obj._Application
}
func (obj *Config) Server() *config.Server {
	return obj._Server
}
func (obj *Config) Logging() *cfg.Logging {
	return obj._Logging
}
func (obj *Config) PaymentInstrumentDb() *cfg.DB {
	return obj._PaymentInstrumentDb
}
func (obj *Config) AWS() *cfg.AWS {
	return obj._AWS
}
func (obj *Config) PiEventPublisher() *cfg.SnsPublisher {
	return obj._PiEventPublisher
}
func (obj *Config) Flags() *config.Flags {
	return obj._Flags
}
func (obj *Config) RedisOptions() *cfg.RedisOptions {
	return obj._RedisOptions
}
func (obj *Config) KnownBanksIFSC() []*config.KnownIFSC {
	return obj._KnownBanksIFSC
}
func (obj *Config) AaTxnPurgePublisher() *cfg.SqsPublisher {
	return obj._AaTxnPurgePublisher
}
func (obj *Config) Tracing() *cfg.Tracing {
	return obj._Tracing
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}

type AccountPiCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsCachingEnabled uint32
	_CacheTTl         time.Duration
}

func (obj *AccountPiCacheConfig) IsCachingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCachingEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *AccountPiCacheConfig) CacheTTl() time.Duration {
	return obj._CacheTTl
}

type PiCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsCachingEnabled uint32
	_CacheTTl         time.Duration
}

func (obj *PiCacheConfig) IsCachingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCachingEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *PiCacheConfig) CacheTTl() time.Duration {
	return obj._CacheTTl
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enableentitysegregation"] = _obj.SetEnableEntitySegregation
	_AaAccountPiPurgeSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._AaAccountPiPurgeSubscriber = _AaAccountPiPurgeSubscriber
	helper.AddFieldSetters("aaaccountpipurgesubscriber", _fieldSetters, _setters)
	_PiPurgeSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._PiPurgeSubscriber = _PiPurgeSubscriber
	helper.AddFieldSetters("pipurgesubscriber", _fieldSetters, _setters)
	_AccountPiCacheConfig, _fieldSetters := NewAccountPiCacheConfig()
	_obj._AccountPiCacheConfig = _AccountPiCacheConfig
	helper.AddFieldSetters("accountpicacheconfig", _fieldSetters, _setters)
	_PiCacheConfig, _fieldSetters := NewPiCacheConfig()
	_obj._PiCacheConfig = _PiCacheConfig
	helper.AddFieldSetters("picacheconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enableentitysegregation"] = _obj.SetEnableEntitySegregation
	_AaAccountPiPurgeSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._AaAccountPiPurgeSubscriber = _AaAccountPiPurgeSubscriber
	helper.AddFieldSetters("aaaccountpipurgesubscriber", _fieldSetters, _setters)
	_PiPurgeSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._PiPurgeSubscriber = _PiPurgeSubscriber
	helper.AddFieldSetters("pipurgesubscriber", _fieldSetters, _setters)
	_AccountPiCacheConfig, _fieldSetters := NewAccountPiCacheConfig()
	_obj._AccountPiCacheConfig = _AccountPiCacheConfig
	helper.AddFieldSetters("accountpicacheconfig", _fieldSetters, _setters)
	_PiCacheConfig, _fieldSetters := NewPiCacheConfig()
	_obj._PiCacheConfig = _PiCacheConfig
	helper.AddFieldSetters("picacheconfig", _fieldSetters, _setters)

	return _obj, _setters
}

func (obj *Config) Init() {
	newObj, _ := NewConfig()
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *config.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enableentitysegregation":
		return obj.SetEnableEntitySegregation(v.EnableEntitySegregation, true, nil)
	case "aaaccountpipurgesubscriber":
		return obj._AaAccountPiPurgeSubscriber.Set(v.AaAccountPiPurgeSubscriber, true, path)
	case "pipurgesubscriber":
		return obj._PiPurgeSubscriber.Set(v.PiPurgeSubscriber, true, path)
	case "accountpicacheconfig":
		return obj._AccountPiCacheConfig.Set(v.AccountPiCacheConfig, true, path)
	case "picacheconfig":
		return obj._PiCacheConfig.Set(v.PiCacheConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *config.Config, dynamic bool, path []string) (err error) {

	err = obj.SetEnableEntitySegregation(v.EnableEntitySegregation, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._AaAccountPiPurgeSubscriber.Set(v.AaAccountPiPurgeSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PiPurgeSubscriber.Set(v.PiPurgeSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AccountPiCacheConfig.Set(v.AccountPiCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PiCacheConfig.Set(v.PiCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *config.Config) error {

	obj._Application = v.Application
	obj._Server = v.Server
	obj._Logging = v.Logging
	obj._PaymentInstrumentDb = v.PaymentInstrumentDb
	obj._AWS = v.AWS
	obj._PiEventPublisher = v.PiEventPublisher
	obj._Flags = v.Flags
	obj._RedisOptions = v.RedisOptions
	obj._KnownBanksIFSC = v.KnownBanksIFSC
	obj._AaTxnPurgePublisher = v.AaTxnPurgePublisher
	obj._Tracing = v.Tracing
	obj._Secrets = v.Secrets
	return nil
}

func (obj *Config) SetEnableEntitySegregation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableEntitySegregation", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableEntitySegregation, 1)
	} else {
		atomic.StoreUint32(&obj._EnableEntitySegregation, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableEntitySegregation")
	}
	return nil
}

func NewAccountPiCacheConfig() (_obj *AccountPiCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &AccountPiCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscachingenabled"] = _obj.SetIsCachingEnabled
	return _obj, _setters
}

func (obj *AccountPiCacheConfig) Init() {
	newObj, _ := NewAccountPiCacheConfig()
	*obj = *newObj
}

func (obj *AccountPiCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AccountPiCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AccountPiCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *AccountPiCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AccountPiCacheConfig) setDynamicField(v *config.AccountPiCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscachingenabled":
		return obj.SetIsCachingEnabled(v.IsCachingEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AccountPiCacheConfig) setDynamicFields(v *config.AccountPiCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCachingEnabled(v.IsCachingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AccountPiCacheConfig) setStaticFields(v *config.AccountPiCacheConfig) error {

	obj._CacheTTl = v.CacheTTl
	return nil
}

func (obj *AccountPiCacheConfig) SetIsCachingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AccountPiCacheConfig.IsCachingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCachingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCachingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCachingEnabled")
	}
	return nil
}

func NewPiCacheConfig() (_obj *PiCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &PiCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscachingenabled"] = _obj.SetIsCachingEnabled
	return _obj, _setters
}

func (obj *PiCacheConfig) Init() {
	newObj, _ := NewPiCacheConfig()
	*obj = *newObj
}

func (obj *PiCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PiCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.PiCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *PiCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PiCacheConfig) setDynamicField(v *config.PiCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscachingenabled":
		return obj.SetIsCachingEnabled(v.IsCachingEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PiCacheConfig) setDynamicFields(v *config.PiCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCachingEnabled(v.IsCachingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *PiCacheConfig) setStaticFields(v *config.PiCacheConfig) error {

	obj._CacheTTl = v.CacheTTl
	return nil
}

func (obj *PiCacheConfig) SetIsCachingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *PiCacheConfig.IsCachingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCachingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCachingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCachingEnabled")
	}
	return nil
}
