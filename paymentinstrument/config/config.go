package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/epifi/be-common/pkg/cfg"
)

var (
	once   sync.Once
	config *Config
	err    error
)

var (
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	conf := &Config{}
	// will be used only for test environment
	configDirPath := testEnvConfigDir()
	// loads config from file
	k, _, err := cfg.LoadConfigUsingKoanf(configDirPath, cfg.PAYMENT_INSTRUMENT_SERVICE)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	err = readAndSetEnv(conf)
	if err != nil {
		return nil, err
	}

	_, err = cfg.LoadSecretsAndPrepareDBConfig(conf.Secrets, conf.Application.Environment, conf.AWS.Region, conf.PaymentInstrumentDb)
	if err != nil {
		return nil, err
	}

	return conf, nil

}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Ports.GrpcPort = intVal
	}
	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

// TODO(kunal): Add validate tags later and verify config struct post unmarshalling
//
//go:generate conf_gen github.com/epifi/gamma/paymentinstrument/config Config
type Config struct {
	Application         *Application
	Server              *Server
	Logging             *cfg.Logging
	PaymentInstrumentDb *cfg.DB
	AWS                 *cfg.AWS
	PiEventPublisher    *cfg.SnsPublisher
	Flags               *Flags
	RedisOptions        *cfg.RedisOptions
	// Known ifsc codes contains ifsc code for a bucket of banks for which
	// we might know the account number.
	// Since a bank account payment instrument is unique based on account number
	// and ifsc, this helps in avoiding creating duplicate PIs for same account.
	// Specially, for internal account as this can lead to wrong mapping of transactions.

	// Hence while creating a bank account PI, known ifsc code prefixes matches then
	// then we cross check the account number from the database and in case we realise
	// wrong ifsc code is passed by the caller we return error
	KnownBanksIFSC             []*KnownIFSC
	AaTxnPurgePublisher        *cfg.SqsPublisher
	AaAccountPiPurgeSubscriber *cfg.SqsSubscriber    `dynamic:"true"`
	PiPurgeSubscriber          *cfg.SqsSubscriber    `dynamic:"true"`
	AccountPiCacheConfig       *AccountPiCacheConfig `dynamic:"true"`
	PiCacheConfig              *PiCacheConfig        `dynamic:"true"`
	Tracing                    *cfg.Tracing
	Secrets                    *cfg.Secrets
	EnableEntitySegregation    bool `dynamic:"true"`
}

type Application struct {
	Environment   string
	Name          string
	IsSecureRedis bool
}

type Server struct {
	Ports *cfg.ServerPorts
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool
}

type KnownIFSC struct {
	// IFSC code prefix for a bank
	Prefix string
	// Bank name
	Bank string
}
type AccountPiCacheConfig struct {
	IsCachingEnabled bool `dynamic:"true"`
	// duration for which actor data should be cached
	CacheTTl time.Duration
}

type PiCacheConfig struct {
	IsCachingEnabled bool `dynamic:"true"`
	CacheTTl         time.Duration
}
