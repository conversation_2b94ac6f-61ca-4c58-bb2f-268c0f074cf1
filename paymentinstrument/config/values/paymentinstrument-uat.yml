Application:
  Environment: "uat"
  Name: "paymentinstrument"
  IsSecureRedis: true

Aws:
  Region: "ap-south-1"

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 12

Server:
  Ports:
    GrpcPort: 8087
    GrpcSecurePort: 9514
    HttpPort: 9999
    HttpPProfPort: 9990

PaymentInstrumentDb:
  Name: "payment_instrument"
  DbType: "PGDB"
  DbServerAlias: "PLUTUS_RDS"
  EnableDebug: false
  SSLMode: "disable"
  AppName: "payment_instrument"
  SecretName: "uat/rds/epifiplutus/payment_instrument_dev_user"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

PiEventPublisher:
  TopicName: "uat-pi-topic"

Flags:
  TrimDebugMessageFromStatus: false

AaTxnPurgePublisher:
    QueueName: "uat-aa-txn-purge-queue"

AaAccountPiPurgeSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-aa-account-pi-purge-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

PiPurgeSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-pi-purge-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

AccountPiCacheConfig:
  IsCachingEnabled: false
  CacheTTl: "2m"

PiCacheConfig :
  IsCachingEnabled : false
  CacheTTl         : "2m"

Tracing:
  Enable: true


