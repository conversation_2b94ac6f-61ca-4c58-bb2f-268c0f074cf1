Application:
  Environment: "staging"
  Name: "paymentinstrument"
  IsSecureRedis: true

Aws:
  Region: "ap-south-1"

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 12

Server:
  Ports:
    GrpcPort: 8087
    GrpcSecurePort: 9514
    HttpPort: 9999
    HttpPProfPort: 9990

PaymentInstrumentDb:
  Name: "payment_instrument"
  DbType: "PGDB"
  DbServerAlias: "PLUTUS_RDS"
  EnableDebug: false
  SSLMode: "disable"
  AppName: "payment_instrument"
  SecretName: "staging/rds/epifiplutus/payment_instrument_dev_user"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

PiEventPublisher:
  TopicName: "staging-pi-topic"

Flags:
  TrimDebugMessageFromStatus: false

AaTxnPurgePublisher:
  QueueName: "staging-aa-txn-purge-queue"

AaAccountPiPurgeSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-aa-account-pi-purge-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

PiPurgeSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-pi-purge-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

AccountPiCacheConfig:
  IsCachingEnabled: true
  CacheTTl: "2m"

PiCacheConfig :
  IsCachingEnabled : true
  CacheTTl         : "2m"

Tracing:
  Enable: true


