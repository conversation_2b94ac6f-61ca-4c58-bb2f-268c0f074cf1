Application:
  Environment: "test"
  Name: "paymentinstrument"
  ServerName: "paymentinstrument"

Aws:
  Region: "ap-south-1"

RedisOptions:
  IsSecureRedis: false
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 3
  ClientName: paymentinstrument

Server:
  Ports:
    GrpcPort: 8087
    GrpcSecurePort: 9514
    HttpPort: 9882

PaymentInstrumentDb:
  Host: "localhost"
  Port: 5432
  Name: "payment_instrument_test"
  DbType: "PGDB"
  SSLMode: "disable"
  AppName: "payment_instrument"
  SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

PiEventPublisher:
  TopicName: "pi-topic"

Flags:
  TrimDebugMessageFromStatus: false

AaTxnPurgePublisher:
  QueueName: "aa-txn-purge-queue"

AaAccountPiPurgeSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "aa-account-pi-purge-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

PiPurgeSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "pi-purge-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

AccountPiCacheConfig:
  IsCachingEnabled: false
  CacheTTl: "2m"

PiCacheConfig :
  IsCachingEnabled : false
  CacheTTl         : "2m"

