Application:
  Environment: "prod"
  Name: "paymentinstrument"
  IsSecureRedis: true

Aws:
  Region: "ap-south-1"

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "redis-15404.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15404"
  AuthDetails:
    SecretPath: "prod/redis/pay/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"
  ClientName: paymentinstrument

Server:
  Ports:
    GrpcPort: 8087
    GrpcSecurePort: 9514
    HttpPort: 9999
    HttpPProfPort: 9990

PaymentInstrumentDb:
  Name: "payment_instrument"
  DbType: "PGDB"
  DbServerAlias: "PLUTUS_RDS"
  EnableDebug: false
  SSLMode: "verify-full"
  AppName: "payment_instrument"
  SecretName: "prod/rds/prod-plutus/payment_instrument_dev_user"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

PiEventPublisher:
  TopicName: "prod-pi-topic"

Flags:
  TrimDebugMessageFromStatus: false

AaTxnPurgePublisher:
  QueueName: "prod-aa-txn-purge-queue"

AaAccountPiPurgeSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-aa-account-pi-purge-queue"
  # Exponential backoff till 2.5 hrs
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 12
      TimeUnit: "Second"

PiPurgeSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-pi-purge-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

AccountPiCacheConfig:
  IsCachingEnabled: true
  CacheTTl: "3h"

PiCacheConfig :
  IsCachingEnabled : true
  CacheTTl         : "1h"

Tracing:
  Enable: true

EnableEntitySegregation: true
