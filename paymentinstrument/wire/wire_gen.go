// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"fmt"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/pkg/storage/v2/usecase"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/order/aa"
	paymentinstrument2 "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/paymentinstrument"
	"github.com/epifi/gamma/paymentinstrument/account_pi"
	"github.com/epifi/gamma/paymentinstrument/config"
	"github.com/epifi/gamma/paymentinstrument/config/genconf"
	"github.com/epifi/gamma/paymentinstrument/consumer"
	"github.com/epifi/gamma/paymentinstrument/dao"
	"github.com/epifi/gamma/paymentinstrument/developer"
	"github.com/epifi/gamma/paymentinstrument/developer/processor"
	types2 "github.com/epifi/gamma/paymentinstrument/wire/types"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitializePiService(conf *config.Config, db types.PaymentInstrumentPGDB, piEventPublisher types2.PiEventPublisher, svClient savings.SavingsClient, redisClient types2.VendormappingPiRedisStore, accountPiRelationClient account_pi.AccountPIRelationClient, genConf *genconf.Config, provider *usecase.DBResourceProvider[*gorm.DB], txnExecutorProvider *usecase.DBResourceProvider[storagev2.IdempotentTxnExecutor], actorClient actor.ActorClient, userClient user.UsersClient, userGroupClient group.GroupClient) *paymentinstrument.Service {
	client := redisClientProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	gormDB := types.PaymentInstrumentPGDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	enableResourceProvider := ProvideDbResourceProviderFlag(genConf)
	piDaoPgdb := dao.NewPiDaoPgdb(db, domainIdGenerator, gormTxnExecutor, attributedIdGen, provider, txnExecutorProvider, enableResourceProvider)
	piCache := dao.NewPiCache(redisCacheStorage, genConf, piDaoPgdb)
	piDao := dao.ProvidePiDao(piCache, genConf, piDaoPgdb)
	piStateLogDaoPGDB := dao.NewpiStateLogDaoPGDB(db)
	piLastTransactionPgdb := dao.NewPiLastTransactionPgdb(genConf, db)
	paymentInstrumentPurgeAuditPGDB := dao.NewpaymentInstrumentPurgeAuditPGDB(db)
	service := paymentinstrument.NewService(conf, piDao, piStateLogDaoPGDB, piLastTransactionPgdb, piEventPublisher, svClient, paymentInstrumentPurgeAuditPGDB, accountPiRelationClient)
	return service
}

func InitializeAccountPiService(db types.PaymentInstrumentPGDB, piEventPublisher types2.PiEventPublisher, piClient paymentinstrument2.PiClient, redisClient types2.VendormappingPiRedisStore, conf *config.Config, genConf *genconf.Config) *accountPi.Service {
	client := redisClientProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	accountPiDaoPgdb := dao.NewAccountPiDaoPgdb(db, domainIdGenerator)
	accountPiCache := dao.NewAccountPiCache(redisCacheStorage, genConf, accountPiDaoPgdb)
	accountPiDao := dao.ProvideAccountPiDao(accountPiCache, accountPiDaoPgdb, genConf)
	aaAccountPiDaoPgdb := dao.NewAAAccountPiDaoPgdb(db, domainIdGenerator)
	service := accountPi.NewService(accountPiDao, piEventPublisher, piClient, aaAccountPiDaoPgdb)
	return service
}

func InitializeDbStatesPiService(db types.PaymentInstrumentPGDB, redisClient types2.VendormappingPiRedisStore, conf *config.Config, genConf *genconf.Config, provider *usecase.DBResourceProvider[*gorm.DB], txnExecutorProvider *usecase.DBResourceProvider[storagev2.IdempotentTxnExecutor]) *developer.PIDevService {
	client := redisClientProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	accountPiDaoPgdb := dao.NewAccountPiDaoPgdb(db, domainIdGenerator)
	accountPiCache := dao.NewAccountPiCache(redisCacheStorage, genConf, accountPiDaoPgdb)
	accountPiDao := dao.ProvideAccountPiDao(accountPiCache, accountPiDaoPgdb, genConf)
	gormDB := types.PaymentInstrumentPGDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	enableResourceProvider := ProvideDbResourceProviderFlag(genConf)
	piDaoPgdb := dao.NewPiDaoPgdb(db, domainIdGenerator, gormTxnExecutor, attributedIdGen, provider, txnExecutorProvider, enableResourceProvider)
	piCache := dao.NewPiCache(redisCacheStorage, genConf, piDaoPgdb)
	piDao := dao.ProvidePiDao(piCache, genConf, piDaoPgdb)
	devPIEntity := processor.NewDevPIEntity(accountPiDao, piDao)
	aaAccountPiDaoPgdb := dao.NewAAAccountPiDaoPgdb(db, domainIdGenerator)
	devAaAccountPi := processor.NewDevAaAccountPi(aaAccountPiDaoPgdb)
	devFactory := developer.NewDevFactory(devPIEntity, devAaAccountPi)
	piDevService := developer.NewPIDevService(devFactory)
	return piDevService
}

func InitializePiConsumerService(db types.PaymentInstrumentPGDB, purgeAaTxnPublisher types2.AaTxnPurgePublisher, aaClient aa.AccountAggregatorClient, redisClient types2.VendormappingPiRedisStore, conf *config.Config, genConf *genconf.Config, provider *usecase.DBResourceProvider[*gorm.DB], txnExecutorProvider *usecase.DBResourceProvider[storagev2.IdempotentTxnExecutor]) *consumer.Service {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	aaAccountPiDaoPgdb := dao.NewAAAccountPiDaoPgdb(db, domainIdGenerator)
	client := redisClientProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	gormDB := types.PaymentInstrumentPGDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	enableResourceProvider := ProvideDbResourceProviderFlag(genConf)
	piDaoPgdb := dao.NewPiDaoPgdb(db, domainIdGenerator, gormTxnExecutor, attributedIdGen, provider, txnExecutorProvider, enableResourceProvider)
	piCache := dao.NewPiCache(redisCacheStorage, genConf, piDaoPgdb)
	piDao := dao.ProvidePiDao(piCache, genConf, piDaoPgdb)
	service := consumer.NewService(aaAccountPiDaoPgdb, purgeAaTxnPublisher, piDao, aaClient, gormTxnExecutor)
	return service
}

// wire.go:

func redisClientProvider(VendormappingPiRedisStore types2.VendormappingPiRedisStore) *redis.Client {
	return VendormappingPiRedisStore
}

func ProvideDbResourceProviderFlag(conf *genconf.Config) types2.EnableResourceProvider {
	logger.InfoNoCtx(fmt.Sprintf("Entity segregation flag in pi config : %v", conf.EnableEntitySegregation()))
	return types2.EnableResourceProvider(conf.EnableEntitySegregation())
}
