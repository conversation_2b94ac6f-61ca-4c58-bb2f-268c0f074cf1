//go:build wireinject
// +build wireinject

//go:generate wire
package wire

import (
	"fmt"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/storage/v2/usecase"
	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cache"
	pkgCmdTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/idgen"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	actorPb "github.com/epifi/gamma/api/actor"
	aaOrderPb "github.com/epifi/gamma/api/order/aa"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	savingsPb "github.com/epifi/gamma/api/savings"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/paymentinstrument"
	accountPi "github.com/epifi/gamma/paymentinstrument/account_pi"
	"github.com/epifi/gamma/paymentinstrument/config"
	"github.com/epifi/gamma/paymentinstrument/config/genconf"
	"github.com/epifi/gamma/paymentinstrument/consumer"
	"github.com/epifi/gamma/paymentinstrument/dao"
	"github.com/epifi/gamma/paymentinstrument/developer"
	"github.com/epifi/gamma/paymentinstrument/developer/processor"
	"github.com/epifi/gamma/paymentinstrument/wire/types"
	piWireTypes "github.com/epifi/gamma/paymentinstrument/wire/types"
)

func InitializePiService(conf *config.Config, db pkgCmdTypes.PaymentInstrumentPGDB, piEventPublisher piWireTypes.PiEventPublisher,
	svClient savingsPb.SavingsClient, redisClient piWireTypes.VendormappingPiRedisStore, accountPiRelationClient accountPiPb.AccountPIRelationClient, genConf *genconf.Config,
	provider *usecase.DBResourceProvider[*gorm.DB], txnExecutorProvider *usecase.DBResourceProvider[storagev2.IdempotentTxnExecutor],
	actorClient actorPb.ActorClient, userClient userPb.UsersClient, userGroupClient userGroupPb.GroupClient) *paymentinstrument.Service {
	wire.Build(
		dao.PiWireSet,
		dao.PiLastTransactionDaoWireSet,
		dao.PiStatusLogDaoWireSet,
		dao.PiPurgeAuditDaoWireSet,
		pkgCmdTypes.PaymentInstrumentPGDBProvider,
		storagev2.TxnExecutorWireSet,
		idgen.NewClock,
		idgen.WireSet,
		paymentinstrument.NewService,
		redisClientProvider,
		ProvideDbResourceProviderFlag,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
	)
	return &paymentinstrument.Service{} // These return values are ignored.
}

func InitializeAccountPiService(db pkgCmdTypes.PaymentInstrumentPGDB, piEventPublisher piWireTypes.PiEventPublisher, piClient piPb.PiClient, redisClient piWireTypes.VendormappingPiRedisStore, conf *config.Config, genConf *genconf.Config) *accountPi.Service {
	wire.Build(
		dao.AccountPiWireSet,
		dao.AaAccountPiWireSet,
		idgen.NewClock,
		idgen.WireSet,
		accountPi.NewService,
		redisClientProvider,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
	)
	return &accountPi.Service{} // These return values are ignored.
}

func InitializeDbStatesPiService(db pkgCmdTypes.PaymentInstrumentPGDB, redisClient piWireTypes.VendormappingPiRedisStore,
	conf *config.Config, genConf *genconf.Config, provider *usecase.DBResourceProvider[*gorm.DB], txnExecutorProvider *usecase.DBResourceProvider[storagev2.IdempotentTxnExecutor]) *developer.PIDevService {
	wire.Build(
		dao.PiWireSet,
		dao.AaAccountPiWireSet,
		dao.AccountPiWireSet,
		pkgCmdTypes.PaymentInstrumentPGDBProvider,
		storagev2.TxnExecutorWireSet,
		idgen.NewClock,
		idgen.WireSet,
		processor.NewDevPIEntity,
		processor.NewDevAaAccountPi,
		developer.NewDevFactory,
		developer.NewPIDevService,
		redisClientProvider,
		ProvideDbResourceProviderFlag,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
	)
	return &developer.PIDevService{} // These return values are ignored.
}

func InitializePiConsumerService(db pkgCmdTypes.PaymentInstrumentPGDB, purgeAaTxnPublisher piWireTypes.AaTxnPurgePublisher, aaClient aaOrderPb.AccountAggregatorClient, redisClient piWireTypes.VendormappingPiRedisStore,
	conf *config.Config, genConf *genconf.Config, provider *usecase.DBResourceProvider[*gorm.DB], txnExecutorProvider *usecase.DBResourceProvider[storagev2.IdempotentTxnExecutor]) *consumer.Service {
	wire.Build(
		dao.AaAccountPiWireSet,
		dao.PiWireSet,
		pkgCmdTypes.PaymentInstrumentPGDBProvider,
		storagev2.TxnExecutorWireSet,
		idgen.NewClock,
		idgen.WireSet,
		consumer.NewService,
		redisClientProvider,
		ProvideDbResourceProviderFlag,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
	)
	return &consumer.Service{}
}

func redisClientProvider(VendormappingPiRedisStore piWireTypes.VendormappingPiRedisStore) *redis.Client {
	return VendormappingPiRedisStore
}

func ProvideDbResourceProviderFlag(conf *genconf.Config) types.EnableResourceProvider {
	logger.InfoNoCtx(fmt.Sprintf("Entity segregation flag in pi config : %v", conf.EnableEntitySegregation()))
	return types.EnableResourceProvider(conf.EnableEntitySegregation())
}
