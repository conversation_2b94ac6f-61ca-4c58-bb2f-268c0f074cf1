package test

import (
	"log"

	"go.uber.org/zap"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/gamma/paymentinstrument/config"
	"github.com/epifi/gamma/paymentinstrument/config/genconf"
	"github.com/epifi/be-common/pkg/logger"
	pkgTestv2 "github.com/epifi/be-common/pkg/test/v2"
)

// InitTestServer initiates components needed for tests
// Will be invoked from TestMain but can be called from individual tests for *special* cases only
func InitTestServer() (*config.Config, *genconf.Config, *gormv2.DB, func()) {
	// Init config
	conf, err := config.Load()
	if err != nil {
		log.Fatal("failed to load config", err)
	}

	genConf, err := genconf.Load()
	if err != nil {
		log.Fatal("failed to load dynamic config", err)
	}

	// Setup logger
	logger.Init(conf.Application.Environment)

	// Init db connection
	db, _, dbCloseFun, dbErr := pkgTestv2.PrepareRandomScopedRdsTestDb(conf.PaymentInstrumentDb, false)
	if dbErr != nil {
		log.Fatal("failed to connect to db", dbErr)
	}

	sqlDb, err := db.DB()
	if err != nil {
		logger.Panic("failed to get sql DB", zap.Error(err))
	}

	// TODO(kunal): Init test gRPC server
	return conf, genConf, db, func() {
		dbCloseFun()
		_ = logger.Log.Sync()
		_ = sqlDb.Close()
	}
}
