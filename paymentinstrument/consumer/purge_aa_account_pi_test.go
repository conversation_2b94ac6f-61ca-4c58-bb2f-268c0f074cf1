package consumer_test

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	queuePb "github.com/epifi/be-common/api/queue"
	aaPb "github.com/epifi/gamma/api/order/aa"
	piConsumerPb "github.com/epifi/gamma/api/paymentinstrument/consumer"
	piConsumer "github.com/epifi/gamma/paymentinstrument/consumer"
	daoMocks "github.com/epifi/gamma/paymentinstrument/dao/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
	mockQueue "github.com/epifi/be-common/pkg/queue/mocks"
	storageV2Mock "github.com/epifi/be-common/pkg/storage/v2/mocks"
)

func TestService_PurgeAaAccountPiRelation(t *testing.T) {
	t.Skipf("FixMe: Issue 67429")
	ctr := gomock.NewController(t)
	mockAccountPiDao := daoMocks.NewMockAAAccountPiDao(ctr)
	mockAaTxnPublisher := mockQueue.NewMockPublisher(ctr)
	mockTxnExecutorClient := storageV2Mock.NewMockTxnExecutor(ctr)
	svc := piConsumer.NewService(mockAccountPiDao, mockAaTxnPublisher, nil, nil, mockTxnExecutorClient)

	type mockDeleteByAccountId struct {
		enable    bool
		accountId string
		err       error
	}
	type mockPublish struct {
		enable bool
		req    *aaPb.PurgeAaTransactionRequest
		res    string
		err    error
	}

	type mockTxnExecutor struct {
		enable bool
		err    error
	}

	tests := []struct {
		name                  string
		req                   *piConsumerPb.PurgeAaAccountPiRelationRequest
		res                   *piConsumerPb.PurgeAaAccountPiRelationResponse
		mockDeleteByAccountId mockDeleteByAccountId
		mockPublish           mockPublish
		mockTxnExecutor       mockTxnExecutor
		wantErr               bool
	}{
		{
			name: "deleted aa account pi successfully",
			req: &piConsumerPb.PurgeAaAccountPiRelationRequest{
				AccountId: "account-1",
			},
			mockDeleteByAccountId: mockDeleteByAccountId{
				enable:    true,
				accountId: "account-1",
			},
			mockPublish: mockPublish{
				enable: true,
				req: &aaPb.PurgeAaTransactionRequest{
					AccountId: "account-1",
				},
			},
			mockTxnExecutor: mockTxnExecutor{
				enable: true,
				err:    nil,
			},
			res: &piConsumerPb.PurgeAaAccountPiRelationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
		},
		{
			name: "error deleting account pi relation",
			req: &piConsumerPb.PurgeAaAccountPiRelationRequest{
				AccountId: "account-1",
			},
			mockDeleteByAccountId: mockDeleteByAccountId{
				enable:    true,
				accountId: "account-1",
				err:       errors.New("error deleting account pi relation"),
			},
			mockTxnExecutor: mockTxnExecutor{
				enable: true,
				err:    epifierrors.ErrTransient,
			},
			res: &piConsumerPb.PurgeAaAccountPiRelationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockDeleteByAccountId.enable {
				mockAccountPiDao.EXPECT().DeleteByAccountId(gomock.Any(), tt.mockDeleteByAccountId.accountId).
					Return(tt.mockDeleteByAccountId.err)
			}
			if tt.mockTxnExecutor.enable {
				mockTxnExecutorClient.EXPECT().RunTxn(gomock.Any(), gomock.Any()).Return(tt.mockTxnExecutor.err)
			}
			if tt.mockPublish.enable {
				mockAaTxnPublisher.EXPECT().Publish(gomock.Any(), tt.mockPublish.req).
					Return(tt.mockPublish.res, tt.mockPublish.err)
			}
			got, err := svc.PurgeAaAccountPiRelation(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("PurgeAaAccountPiRelation() gotErr :%v wantErr: %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.res) {
				t.Errorf("PurgeAaAccountPiRelation() got :%v want: %v", got, tt.res)
				return
			}
		})
	}

}
