package consumer

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	queuePb "github.com/epifi/be-common/api/queue"
	aaOrderPb "github.com/epifi/gamma/api/order/aa"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	piConsumerPb "github.com/epifi/gamma/api/paymentinstrument/consumer"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
)

// PurgeWealthPis purges the pi for given piIds if the ownership is wealth and the pi is not getting used in any other trasnaction
func (s *Service) PurgeWealthPis(ctx context.Context, req *piConsumerPb.PurgeWealthPisRequest) (*piConsumerPb.PurgeWealthPisResponse, error) {
	var (
		res           = &piConsumerPb.PurgeWealthPisResponse{}
		header        = &queuePb.ConsumerResponseHeader{}
		filteredPiIds []string
	)
	res.ResponseHeader = header

	logger.Info(ctx, "received event for deletion of wealth pis", zap.Any("pi-ids", req.GetPiIds()))

	txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		pis, err := s.piDao.GetByIds(txnCtx, req.GetPiIds())
		if err != nil {
			if !errors.Is(err, epifierrors.ErrRecordNotFound) {
				return fmt.Errorf("error fetching pis: %v :%w", err, epifierrors.ErrTransient)
			}
		}
		filteredPiIds, err = s.getFilteredPisToDelete(txnCtx, pis)
		if err != nil {
			return fmt.Errorf("error fetching filtered pis: %w", err)
		}
		if len(filteredPiIds) == 0 {
			return nil
		}
		err = s.piDao.DeleteWealthPisByIds(txnCtx, filteredPiIds)
		if err != nil {
			return fmt.Errorf("error deleting wealth pis: %v :%w", err, epifierrors.ErrTransient)
		}
		return nil
	})

	if txnErr != nil {
		logger.Error(ctx, "error while deleting pis", zap.Error(txnErr))
		header.Status = queue.GetStatusFrom(txnErr)
		return res, nil
	}

	logger.Info(ctx, "successfully processed event for deletion of pis", zap.Any("pi-ids", req.GetPiIds()))
	header.Status = queuePb.MessageConsumptionStatus_SUCCESS
	return res, nil
}

func (s *Service) getFilteredPisToDelete(ctx context.Context, pis []*piPb.PaymentInstrument) ([]string, error) {
	var (
		filteredPiIds         []string
		finalPiIdsToBeDeleted []string
	)

	for _, pi := range pis {
		if !pi.IsMerchantPI() && pi.GetOwnership() == piPb.Ownership_EPIFI_WEALTH {
			filteredPiIds = append(filteredPiIds, pi.GetId())
		}
	}
	if len(filteredPiIds) == 0 {
		return nil, nil
	}
	res, err := s.aaOrderClient.GetTransactionCountForPis(ctx, &aaOrderPb.GetTransactionCountForPisRequest{
		PiIds: filteredPiIds,
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		return nil, fmt.Errorf("error deleting pis: %v :%w", err, epifierrors.ErrTransient)
	}
	for _, piId := range filteredPiIds {
		txnCount, ok := res.GetPiIdToTxnCountMap()[piId]
		if !ok || txnCount == 0 {
			finalPiIdsToBeDeleted = append(finalPiIdsToBeDeleted, piId)
		}
	}
	return finalPiIdsToBeDeleted, nil
}
