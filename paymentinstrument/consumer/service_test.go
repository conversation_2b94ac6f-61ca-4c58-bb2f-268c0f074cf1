package consumer_test

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/paymentinstrument/config"
	"github.com/epifi/gamma/paymentinstrument/test"
)

var conf *config.Config

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, _, _, teardown = test.InitTestServer()

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
