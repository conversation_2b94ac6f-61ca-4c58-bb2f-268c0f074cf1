package consumer

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	queuePb "github.com/epifi/be-common/api/queue"
	aaPb "github.com/epifi/gamma/api/order/aa"
	piConsumerPb "github.com/epifi/gamma/api/paymentinstrument/consumer"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
)

// PurgeAaAccountPiRelation purges the aa account pi relation for the given account id
// and publishes the packet to purge aa txn publisher
func (s *Service) PurgeAaAccountPiRelation(ctx context.Context, req *piConsumerPb.PurgeAaAccountPiRelationRequest) (*piConsumerPb.PurgeAaAccountPiRelationResponse, error) {
	var (
		res    = &piConsumerPb.PurgeAaAccountPiRelationResponse{}
		header = &queuePb.ConsumerResponseHeader{}
	)
	res.ResponseHeader = header

	logger.Info(ctx, "received event for deletion of account pi relation", zap.String(logger.ACCOUNT_ID, req.GetAccountId()))

	txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		err := s.aaAccountPiDao.DeleteByAccountId(txnCtx, req.GetAccountId())
		if err != nil {
			return fmt.Errorf("error deleting account pi relation for account id :%s :%v :%w", req.GetAccountId(), err, epifierrors.ErrTransient)
		}

		_, err = s.purgeAaTxnPublisher.Publish(txnCtx, &aaPb.PurgeAaTransactionRequest{
			AccountId: req.GetAccountId(),
		})
		if err != nil {
			return fmt.Errorf("error publishing packet to purge aa txn :%s :%v :%w", req.GetAccountId(), err, epifierrors.ErrTransient)
		}
		return nil
	})

	if txnErr != nil {
		logger.Error(ctx, "error in transactional block", zap.Error(txnErr), zap.String(logger.ACCOUNT_ID, req.GetAccountId()))
		header.Status = queue.GetStatusFrom(txnErr)
		return res, nil
	}

	logger.Info(ctx, "successfully processed event for account pi deletion", zap.String(logger.ACCOUNT_ID, req.GetAccountId()))

	header.Status = queuePb.MessageConsumptionStatus_SUCCESS
	return res, nil
}
