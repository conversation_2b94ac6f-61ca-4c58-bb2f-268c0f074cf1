package consumer

import (
	aaOrderPb "github.com/epifi/gamma/api/order/aa"
	pb "github.com/epifi/gamma/api/paymentinstrument/consumer"
	"github.com/epifi/gamma/paymentinstrument/dao"
	piWireTypes "github.com/epifi/gamma/paymentinstrument/wire/types"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
)

type Service struct {
	pb.UnimplementedConsumerServer
	aaAccountPiDao      dao.AAAccountPiDao
	purgeAaTxnPublisher piWireTypes.AaTxnPurgePublisher
	piDao               dao.PiDao
	aaOrderClient       aaOrderPb.AccountAggregatorClient
	txnExecutor         storageV2.TxnExecutor
}

func NewService(
	aaAccountPiDao dao.AAAccountPiDao,
	purgeAaTxnPublisher piWireTypes.AaTxnPurgePublisher,
	piDao dao.PiDao,
	aaOrderClient aaOrderPb.AccountAggregatorClient,
	txnExecutor storageV2.TxnExecutor,
) *Service {
	return &Service{
		aaAccountPiDao:      aaAccountPiDao,
		purgeAaTxnPublisher: purgeAaTxnPublisher,
		piDao:               piDao,
		aaOrderClient:       aaOrderClient,
		txnExecutor:         txnExecutor,
	}
}
