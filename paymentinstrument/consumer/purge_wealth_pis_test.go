package consumer_test

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/api/rpc"
	aaOrderPb "github.com/epifi/gamma/api/order/aa"
	mocks2 "github.com/epifi/gamma/api/order/aa/mocks"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	piConsumerPb "github.com/epifi/gamma/api/paymentinstrument/consumer"
	piConsumer "github.com/epifi/gamma/paymentinstrument/consumer"
	daoMocks "github.com/epifi/gamma/paymentinstrument/dao/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
	storageV2Mock "github.com/epifi/be-common/pkg/storage/v2/mocks"
)

func TestService_PurgeWealthPis(t *testing.T) {
	t.Skipf("FixMe: Issue 67429")
	ctr := gomock.NewController(t)
	mockPiDao := daoMocks.NewMockPiDao(ctr)
	mockAaOrderClient := mocks2.NewMockAccountAggregatorClient(ctr)
	mockTxnExecutorClient := storageV2Mock.NewMockTxnExecutor(ctr)
	svc := piConsumer.NewService(nil, nil, mockPiDao, mockAaOrderClient, mockTxnExecutorClient)

	type mockGetByIds struct {
		enable bool
		piIds  []string
		want   []*piPb.PaymentInstrument
		err    error
	}

	type mockGetTransactionCountForPis struct {
		enable bool
		req    *aaOrderPb.GetTransactionCountForPisRequest
		res    *aaOrderPb.GetTransactionCountForPisResponse
		err    error
	}

	type mockDeleteWealthPisByIds struct {
		enable bool
		piIds  []string
		err    error
	}

	type mockTxnExecutor struct {
		enable bool
		err    error
	}

	tests := []struct {
		name                          string
		req                           *piConsumerPb.PurgeWealthPisRequest
		res                           *piConsumerPb.PurgeWealthPisResponse
		mockGetByIds                  mockGetByIds
		mockGetTransactionCountForPis mockGetTransactionCountForPis
		mockDeleteWealthPisByIds      mockDeleteWealthPisByIds
		mockTxnExecutor               mockTxnExecutor
		wantErr                       bool
	}{
		{
			name: "deleted wealth pis successfully",
			req: &piConsumerPb.PurgeWealthPisRequest{
				PiIds: []string{
					"pi-1",
					"pi-2",
					"pi-3",
				},
			},
			mockGetByIds: mockGetByIds{
				enable: true,
				piIds: []string{
					"pi-1",
					"pi-2",
					"pi-3",
				},
				want: []*piPb.PaymentInstrument{
					{
						Id:        "pi-1",
						Ownership: piPb.Ownership_EPIFI_TECH,
					},
					{
						Id:        "pi-2",
						Ownership: piPb.Ownership_EPIFI_WEALTH,
					},
					{
						Id:        "pi-3",
						Ownership: piPb.Ownership_EPIFI_WEALTH,
					},
				},
				err: nil,
			},
			mockGetTransactionCountForPis: mockGetTransactionCountForPis{
				enable: true,
				req: &aaOrderPb.GetTransactionCountForPisRequest{
					PiIds: []string{"pi-2", "pi-3"},
				},
				res: &aaOrderPb.GetTransactionCountForPisResponse{
					Status: rpc.StatusOk(),
					PiIdToTxnCountMap: map[string]int32{
						"pi-2": 5,
						"pi-3": 0,
					},
				},
			},
			mockDeleteWealthPisByIds: mockDeleteWealthPisByIds{
				enable: true,
				piIds:  []string{"pi-3"},
			},
			mockTxnExecutor: mockTxnExecutor{
				enable: true,
				err:    nil,
			},
			res: &piConsumerPb.PurgeWealthPisResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
		},
		{
			name: "no pis present",
			req: &piConsumerPb.PurgeWealthPisRequest{
				PiIds: []string{
					"pi-1",
					"pi-2",
					"pi-3",
				},
			},
			mockGetByIds: mockGetByIds{
				enable: true,
				piIds: []string{
					"pi-1",
					"pi-2",
					"pi-3",
				},
				err: epifierrors.ErrRecordNotFound,
			},
			mockTxnExecutor: mockTxnExecutor{
				enable: true,
				err:    nil,
			},
			res: &piConsumerPb.PurgeWealthPisResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
		},
		{
			name: "error while fetching pis",
			req: &piConsumerPb.PurgeWealthPisRequest{
				PiIds: []string{
					"pi-1",
					"pi-2",
					"pi-3",
				},
			},
			mockGetByIds: mockGetByIds{
				enable: true,
				piIds: []string{
					"pi-1",
					"pi-2",
					"pi-3",
				},
				err: errors.New("error while fetching pi"),
			},
			mockTxnExecutor: mockTxnExecutor{
				enable: true,
				err:    epifierrors.ErrTransient,
			},
			res: &piConsumerPb.PurgeWealthPisResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
		},
		{
			name: "error while fetchin pi count",
			req: &piConsumerPb.PurgeWealthPisRequest{
				PiIds: []string{
					"pi-1",
					"pi-2",
					"pi-3",
				},
			},
			mockGetByIds: mockGetByIds{
				enable: true,
				piIds: []string{
					"pi-1",
					"pi-2",
					"pi-3",
				},
				want: []*piPb.PaymentInstrument{
					{
						Id:        "pi-1",
						Ownership: piPb.Ownership_EPIFI_TECH,
					},
					{
						Id:        "pi-2",
						Ownership: piPb.Ownership_EPIFI_WEALTH,
					},
					{
						Id:        "pi-3",
						Ownership: piPb.Ownership_EPIFI_WEALTH,
					},
				},
				err: nil,
			},
			mockGetTransactionCountForPis: mockGetTransactionCountForPis{
				enable: true,
				req: &aaOrderPb.GetTransactionCountForPisRequest{
					PiIds: []string{"pi-2", "pi-3"},
				},
				res: &aaOrderPb.GetTransactionCountForPisResponse{
					Status: rpc.StatusInternal(),
				},
			},
			res: &piConsumerPb.PurgeWealthPisResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
		},
		{
			name: "error while deleting wealth pis",
			req: &piConsumerPb.PurgeWealthPisRequest{
				PiIds: []string{
					"pi-1",
					"pi-2",
					"pi-3",
				},
			},
			mockGetByIds: mockGetByIds{
				enable: true,
				piIds: []string{
					"pi-1",
					"pi-2",
					"pi-3",
				},
				want: []*piPb.PaymentInstrument{
					{
						Id:        "pi-1",
						Ownership: piPb.Ownership_EPIFI_TECH,
					},
					{
						Id:        "pi-2",
						Ownership: piPb.Ownership_EPIFI_WEALTH,
					},
					{
						Id:        "pi-3",
						Ownership: piPb.Ownership_EPIFI_WEALTH,
					},
				},
				err: nil,
			},
			mockGetTransactionCountForPis: mockGetTransactionCountForPis{
				enable: true,
				req: &aaOrderPb.GetTransactionCountForPisRequest{
					PiIds: []string{"pi-2", "pi-3"},
				},
				res: &aaOrderPb.GetTransactionCountForPisResponse{
					Status: rpc.StatusOk(),
					PiIdToTxnCountMap: map[string]int32{
						"pi-2": 5,
						"pi-3": 0,
					},
				},
			},
			mockDeleteWealthPisByIds: mockDeleteWealthPisByIds{
				enable: true,
				piIds:  []string{"pi-3"},
				err:    errors.New("error while deleting wealth pis"),
			},
			res: &piConsumerPb.PurgeWealthPisResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByIds.enable {
				mockPiDao.EXPECT().GetByIds(gomock.Any(), tt.mockGetByIds.piIds).
					Return(tt.mockGetByIds.want, tt.mockGetByIds.err)
			}
			if tt.mockGetTransactionCountForPis.enable {
				mockAaOrderClient.EXPECT().GetTransactionCountForPis(gomock.Any(), tt.mockGetTransactionCountForPis.req).
					Return(tt.mockGetTransactionCountForPis.res, tt.mockGetTransactionCountForPis.err)
			}
			if tt.mockDeleteWealthPisByIds.enable {
				mockPiDao.EXPECT().DeleteWealthPisByIds(gomock.Any(), tt.mockDeleteWealthPisByIds.piIds).
					Return(tt.mockDeleteWealthPisByIds.err)
			}

			if tt.mockTxnExecutor.enable {
				mockTxnExecutorClient.EXPECT().RunTxn(gomock.Any(), gomock.Any()).Return(tt.mockTxnExecutor.err)
			}

			got, err := svc.PurgeWealthPis(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("PurgeWealthPis() gotErr: %v wantErr: %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.res) {
				t.Errorf("PurgeWealthPis() got: %v want: %v", err, tt.wantErr)
				return
			}
		})
	}
}
