package developer

import (
	"context"
	"errors"

	"github.com/epifi/be-common/pkg/epifierrors"

	rpcPb "github.com/epifi/be-common/api/rpc"
	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/paymentinstrument/developer"
	"github.com/epifi/be-common/pkg/logger"
)

type PIDevService struct {
	fac *DevFactory
}

func NewPIDevService(fac *DevFactory) *PIDevService {
	return &PIDevService{
		fac: fac,
	}
}

func (c *PIDevService) GetEntityList(ctx context.Context, req *cxDsPb.GetEntityListRequest) (*cxDsPb.GetEntityListResponse, error) {
	return &cxDsPb.GetEntityListResponse{
		Status:     rpcPb.StatusOk(),
		EntityList: []string{developer.PIEntity_PAYMENT_INSTRUMENT.String(), developer.PIEntity_AA_ACCOUNT_PI.String()},
	}, nil
}

func (c *PIDevService) GetParameterList(ctx context.Context, req *cxDsPb.GetParameterListRequest) (*cxDsPb.GetParameterListResponse, error) {
	ent, ok := developer.PIEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetParameterListResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available"),
		}, nil
	}
	paramFetcher, err := c.fac.getParameterListImpl(developer.PIEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available")
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	paramList, err := paramFetcher.FetchParamList(ctx, developer.PIEntity(ent))
	if err != nil {
		logger.Error(ctx, "unable to fetch parameter list")
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxDsPb.GetParameterListResponse{
		Status:        rpcPb.StatusOk(),
		ParameterList: paramList,
	}, nil
}

func (c *PIDevService) GetData(ctx context.Context, req *cxDsPb.GetDataRequest) (*cxDsPb.GetDataResponse, error) {
	ent, ok := developer.PIEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetDataResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available"),
		}, nil
	}
	dataFetcher, err := c.fac.getDataImpl(developer.PIEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available")
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	jsonResp, err := dataFetcher.FetchData(ctx, developer.PIEntity(ent), req.GetFilters())
	if err != nil {
		logger.Error(ctx, "unable to get data")
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &cxDsPb.GetDataResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxDsPb.GetDataResponse{
		Status:       rpcPb.StatusOk(),
		JsonResponse: jsonResp,
	}, nil
}
