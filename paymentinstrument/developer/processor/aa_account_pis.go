package processor

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/proto/json"
	accountsPb "github.com/epifi/gamma/api/accounts"

	"github.com/epifi/gamma/paymentinstrument/dao"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/paymentinstrument/developer"
)

type DevAaAccountPi struct {
	aaAccountPiDao dao.AAAccountPiDao
}

func NewDevAaAccountPi(aaAccountPiDao dao.AAAccountPiDao) *DevAaAccountPi {
	return &DevAaAccountPi{
		aaAccountPiDao: aaAccountPiDao,
	}
}

func (d *DevAaAccountPi) FetchParamList(ctx context.Context, entity developer.PIEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            Id,
			Label:           "ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            ActorId,
			Label:           "Actor Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            AccountId,
			Label:           "Account Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:    AccountType,
			Label:   "Account Type",
			Type:    db_state.ParameterDataType_DROPDOWN,
			Options: []string{accountsPb.Type_SAVINGS.String(), accountsPb.Type_CURRENT.String(), accountsPb.Type_FIXED_DEPOSIT.String(), accountsPb.Type_RECURRING_DEPOSIT.String(), accountsPb.Type_SMART_DEPOSIT.String()},
		},
		{
			Name:            PiId,
			Label:           "Payment Instrument ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

// nolint:funlen
func (d *DevAaAccountPi) FetchData(ctx context.Context, entity developer.PIEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}
	var (
		e           []byte
		accountType accountsPb.Type
		accountId   string
	)
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case Id:
			aaAccountPi, err := d.aaAccountPiDao.GetById(ctx, filter.GetStringValue())
			if err != nil {
				logger.Error(ctx, "could not fetch aa account pi details for given id", zap.String(logger.ID, filter.GetStringValue()), zap.Error(err))
				return "", err
			}
			e, err = json.Marshal(aaAccountPi)
			if err != nil {
				logger.Error(ctx, "cannot marshal struct to json", zap.Error(err))
				return "", err
			}
			return string(e), nil
		case ActorId:
			aaAccountPis, err := d.aaAccountPiDao.GetByActorId(ctx, filter.GetStringValue())
			if err != nil {
				logger.Error(ctx, "could not fetch aa account pi details for given actor id", zap.String(logger.ACTOR_ID, filter.GetStringValue()), zap.Error(err))
				return "", err
			}
			e, err = json.Marshal(aaAccountPis)
			if err != nil {
				logger.Error(ctx, "cannot marshal struct to json", zap.Error(err))
				return "", err
			}
			return string(e), nil
		case PiId:
			aaAccountPi, err := d.aaAccountPiDao.GetByPiId(ctx, filter.GetStringValue())
			if err != nil {
				logger.Error(ctx, "could not fetch aa account pi details for given pi id", zap.String(logger.PI_ID, filter.GetStringValue()), zap.Error(err))
				return "", err
			}
			e, err = json.Marshal(aaAccountPi)
			if err != nil {
				logger.Error(ctx, "cannot marshal struct to json", zap.Error(err))
				return "", err
			}
			return string(e), nil
		case AccountType:
			accountType = accountsPb.Type(accountsPb.Type_value[filter.GetDropdownValue()])
		case AccountId:
			accountId = filter.GetStringValue()
		default:
			return "", fmt.Errorf("unknown parameter name %s", filter.GetParameterName())
		}
	}

	if accountType == accountsPb.Type_TYPE_UNSPECIFIED {
		logger.Error(ctx, "accountType cannot be unspecified for fetching aa account pi details for given account id and account type")
		return "", fmt.Errorf("account type is unspecified")
	}

	if accountId == "" {
		logger.Error(ctx, "accountId cannot be empty for fetching aa account pi details for given account id and account type")
		return "", fmt.Errorf("accountId is empty")
	}

	aaAccountPis, err := d.aaAccountPiDao.GetByAccountId(ctx, accountId, accountType)
	if err != nil {
		logger.Error(ctx, "could not fetch aa account pi details for given account id and account type", zap.String(logger.ACCOUNT_ID, accountId), zap.String(logger.ACCOUNT_TYPE, accountType.String()), zap.Error(err))
		return "", err
	}

	e, err = json.Marshal(aaAccountPis)
	if err != nil {
		logger.Error(ctx, "cannot marshal struct to json", zap.Error(err))
		return "", err
	}

	return string(e), nil
}
