package processor

import (
	"context"
	"fmt"
	"strings"

	"github.com/golang/protobuf/ptypes/timestamp"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/proto/json"
	piPb "github.com/epifi/gamma/api/paymentinstrument"

	"github.com/epifi/gamma/paymentinstrument/dao"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/paymentinstrument/developer"
)

type DevPIEntity struct {
	accountPiDao dao.AccountPiDao
	piDao        dao.PiDao
}

func NewDevPIEntity(accountPiDao dao.AccountPiDao, piDao dao.PiDao) *DevPIEntity {
	return &DevPIEntity{
		accountPiDao: accountPiDao,
		piDao:        piDao,
	}
}

func (d *DevPIEntity) FetchParamList(ctx context.Context, entity developer.PIEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            ActorId,
			Label:           "Actor ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            VPA,
			Label:           "VPA",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            AccountNoIfsc,
			Label:           "AccountNo-IFSC",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            PiId,
			Label:           "Payment Instrument ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (d *DevPIEntity) FetchData(ctx context.Context, entity developer.PIEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}
	var data []byte
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case ActorId:
			accountPiList, err := d.accountPiDao.GetByActorId(ctx, filter.GetStringValue())
			if err != nil {
				logger.Error(ctx, "could not fetch account pi details", zap.String(logger.ACTOR_ID, filter.GetStringValue()), zap.Error(err))
				return "", err
			}
			var piIdList []string
			for _, accountPi := range accountPiList {
				piIdList = append(piIdList, accountPi.PiId)
			}
			piDetailList, err := d.piDao.GetByIds(ctx, piIdList)
			if err != nil {
				logger.Error(ctx, "could not fetch pi details", zap.Error(err))
				return "", err
			}
			resp := getPIResponseList(piDetailList)
			data, err = json.Marshal(resp)
			if err != nil {
				logger.Error(ctx, "cannot marshal struct to json")
				return "", err
			}
		case VPA:
			piDetail, err := d.piDao.GetUpiPI(ctx, filter.GetStringValue())
			if err != nil {
				logger.Error(ctx, "could not fetch UPI pi details", zap.String(logger.VPA, filter.GetStringValue()), zap.Error(err))
				return "", err
			}
			resp := getPIResponse(piDetail)
			data, err = json.Marshal(resp)
			if err != nil {
				logger.Error(ctx, "cannot marshal struct to json")
				return "", err
			}
		case AccountNoIfsc:
			accountNoIfsc := filter.GetStringValue()
			splitAccNoIfsc := strings.Split(accountNoIfsc, "-")
			if len(splitAccNoIfsc) != 2 {
				logger.Error(ctx, "Invalid Format. Enter #account_number-#IFSC.")
				return "", errors.New("Invalid Format. Enter #account_number-#IFSC.")
			}
			accountNo := splitAccNoIfsc[0]
			ifscCode := splitAccNoIfsc[1]
			piDetail, err := d.piDao.GetBankAccountPI(ctx, accountNo, ifscCode)
			if err != nil {
				logger.Error(ctx, "could not fetch BANK_ACCOUNT pi details", zap.String(AccountNoIfsc, accountNoIfsc), zap.Error(err))
				return "", err
			}
			resp := getPIResponse(piDetail)
			data, err = json.Marshal(resp)
			if err != nil {
				logger.Error(ctx, "cannot marshal struct to json")
				return "", err
			}
		case PiId:
			piDetail, err := d.piDao.GetById(ctx, filter.GetStringValue())
			if err != nil {
				logger.Error(ctx, "could not fetch pi details", zap.Error(err))
				return "", err
			}
			resp := getPIResponse(piDetail)
			data, err = json.Marshal(resp)
			if err != nil {
				logger.Error(ctx, "cannot marshal struct to json")
				return "", err
			}
		default:
			return "", fmt.Errorf("unknown parameter name %s", filter.GetParameterName())
		}
	}
	return string(data), nil
}

type PIResponse struct {
	Id string

	Type string

	State string

	Capabilities map[string]bool

	CreatedAt *timestamp.Timestamp
	UpdatedAt *timestamp.Timestamp
	// Defaults to null which signifies active instrument and to timestamp if deleted
	DeletedAt *timestamp.Timestamp
	// PI issuer classification
	IssuerClassification string
	Identifier           interface{}
	VerifiedName         string
}

func getPIResponseList(piDetailList []*piPb.PaymentInstrument) []*PIResponse {
	var resp []*PIResponse
	for _, piDetail := range piDetailList {
		piResponse := getPIResponse(piDetail)
		resp = append(resp, piResponse)
	}
	return resp
}

func getPIResponse(piDetail *piPb.PaymentInstrument) *PIResponse {
	var identifier interface{}
	switch piDetail.GetType() { //nolint:exhaustive
	case piPb.PaymentInstrumentType_BANK_ACCOUNT:
		account := piDetail.GetAccount()
		if account != nil {
			if account.GetSecureAccountNumber() != "" {
				account.SecureAccountNumber = mask.GetMaskedAccountNumber(account.GetSecureAccountNumber(), "X")
			}
			if account.GetActualAccountNumber() != "" {
				account.ActualAccountNumber = mask.GetMaskedAccountNumber(account.GetActualAccountNumber(), "X")
			}
		}
		identifier = account
	case piPb.PaymentInstrumentType_CREDIT_CARD, piPb.PaymentInstrumentType_DEBIT_CARD:
		card := piDetail.GetCard()
		if card != nil {
			if card.GetSecureCardNumber() != "" {
				card.SecureCardNumber = mask.GetMaskedCardNumber(card.GetSecureCardNumber(), "X")
			}
			if card.GetActualCardNumber() != "" {
				card.ActualCardNumber = mask.GetMaskedDebitCardNumber(card.GetActualCardNumber(), "X")
			}
		}
		identifier = card
	case piPb.PaymentInstrumentType_UPI:
		upi := piDetail.GetUpi()
		if upi != nil && upi.GetMaskedAccountNumber() != "" {
			// Ensure masked_account_number is properly masked
			upi.MaskedAccountNumber = mask.GetMaskedAccountNumber(upi.GetMaskedAccountNumber(), "X")
		}
		identifier = upi
	case piPb.PaymentInstrumentType_GENERIC, piPb.PaymentInstrumentType_PARTIAL_BANK_ACCOUNT:
		account := piDetail.GetAccount()
		if account != nil && account.GetSecureAccountNumber() != "" {
			account.SecureAccountNumber = mask.GetMaskedAccountNumber(account.GetSecureAccountNumber(), "X")
		}
		identifier = account
	}
	return &PIResponse{
		Id:                   piDetail.GetId(),
		Type:                 piDetail.GetType().String(),
		State:                piDetail.GetState().String(),
		Capabilities:         piDetail.GetCapabilities(),
		CreatedAt:            piDetail.GetCreatedAt(),
		UpdatedAt:            piDetail.GetUpdatedAt(),
		DeletedAt:            piDetail.GetDeletedAt(),
		IssuerClassification: piDetail.GetIssuerClassification().String(),
		Identifier:           identifier,
		VerifiedName:         piDetail.GetVerifiedName(),
	}
}
