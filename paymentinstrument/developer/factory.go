package developer

import (
	"fmt"

	"github.com/epifi/gamma/api/paymentinstrument/developer"
	"github.com/epifi/gamma/paymentinstrument/developer/processor"
)

type DevFactory struct {
	devPIEntity    *processor.DevPIEntity
	devAaAccountPi *processor.DevAaAccountPi
}

func NewDevFactory(devPIEntity *processor.DevPIEntity, devAaAccountPi *processor.DevAaAccountPi) *DevFactory {
	return &DevFactory{
		devPIEntity:    devPIEntity,
		devAaAccountPi: devAaAccountPi,
	}
}

func (d *DevFactory) getParameterListImpl(entity developer.PIEntity) (IParameterFetcher, error) {
	switch entity {
	case developer.PIEntity_PAYMENT_INSTRUMENT:
		return d.devPIEntity, nil
	case developer.PIEntity_AA_ACCOUNT_PI:
		return d.devAaAccountPi, nil
	}
	return nil, fmt.Errorf("no valid implementation found")
}

func (d *DevFactory) getDataImpl(entity developer.PIEntity) (IDataFetcher, error) {
	switch entity {
	case developer.PIEntity_PAYMENT_INSTRUMENT:
		return d.devPIEntity, nil
	case developer.PIEntity_AA_ACCOUNT_PI:
		return d.devAaAccountPi, nil
	}
	return nil, fmt.Errorf("no valid implementation found")
}
