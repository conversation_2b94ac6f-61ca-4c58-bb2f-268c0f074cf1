package paymentinstrument

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

// BatchHardDeletePi does hard delete of PI from the database and publishes deletion event to
// intimate respective stack holders.
// The RPC works on all or none principle for all eligible entries (eligible = ids for which entry is present in the DB).
// i.e. PI deletion and deletion event publishing is done in an atomic block
// **NOTE**
// The method only supports deletion on epiFi wealth related PIs at the moment.
// TODO(nitesh/16827): add UT
func (s *Service) BatchHardDeletePi(ctx context.Context, req *piPb.BatchHardDeletePiRequest) (*piPb.BatchHardDeletePiResponse, error) {
	res := &piPb.BatchHardDeletePiResponse{}

	eligiblePis, err := s.getEligiblePiIDsToBeDeleted(ctx, req.GetPiIds())
	if err != nil {
		logger.Error(ctx, "failed to get list of eligible pi ids", zap.Error(err))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	}

	if len(eligiblePis) == 0 {
		logger.Info(ctx, "no pi eligible for deletion")
		res.Status = rpc.StatusOk()
		return res, nil
	}

	err = s.piPurgeAuditDao.BatchUpsert(ctx, getPiPurgeAuditEntriesForPis(eligiblePis))
	if err != nil {
		logger.Error(ctx, "failed to upsert pi purge audit data", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// TODO(nitesh/16826): add logic to publish deletion event for the stack holders to update

	res.Status = rpc.StatusOk()
	return res, nil
}

// getEligiblePiIDsToBeDeleted get list of eligible pi ids to be deleted
func (s *Service) getEligiblePiIDsToBeDeleted(ctx context.Context, piIDs []string) ([]*piPb.PaymentInstrument, error) {
	pis, err := s.piDao.GetByIds(ctx, piIDs)
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, fmt.Errorf("no eligible pi found: %w", rpc.StatusAsError(rpc.ExtendedStatusAlreadyProcessed()))
	}

	if err != nil {
		return nil, fmt.Errorf("failed to fetch pis: %v: %w", err, rpc.StatusAsError(rpc.StatusInternal()))
	}

	// filter PIs where PI Ownership is EpiFi Wealth and Issuer is External
	var eligiblePis []*piPb.PaymentInstrument
	for _, pi := range pis {
		if pi.Ownership == piPb.Ownership_EPIFI_WEALTH && pi.IssuerClassification == piPb.PaymentInstrumentIssuer_EXTERNAL {
			eligiblePis = append(eligiblePis, pi)
		}
	}

	if len(eligiblePis) == 0 {
		return nil, fmt.Errorf("no eligible pi found: %w", rpc.StatusAsError(rpc.ExtendedStatusAlreadyProcessed()))
	}

	return eligiblePis, nil

}

func getPiPurgeAuditEntriesForPis(pis []*piPb.PaymentInstrument) []*piPb.PaymentInstrumentPurgeAudit {
	var res []*piPb.PaymentInstrumentPurgeAudit

	for _, pi := range pis {
		res = append(res, &piPb.PaymentInstrumentPurgeAudit{
			PiId:    pi.GetId(),
			Payload: pi,
		})
	}

	return res
}
