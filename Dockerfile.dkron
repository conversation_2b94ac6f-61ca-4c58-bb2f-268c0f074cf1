# Ref: https://weberc2.bitbucket.io/posts/golang-docker-scratch-app.html

# This is the first stage, for building things that will be required by the
# final stage (notably the binary)
FROM golang:1.24 AS builder

# Accept service_name as build-arg
ARG job_name
ARG target

# set GOPROXY for fetching Go Modules to default value
ARG goproxy=https://proxy.golang.org,direct

WORKDIR /go/src/github.com/epifi/gamma/

# Downloading dependencies first to cache them
COPY go.sum .
COPY go.mod .
ENV GOPROXY=$goproxy
RUN go mod download -x

# Create a "nobody" non-root user for the next image by crafting an /etc/passwd
# file that the next image can copy in. This is necessary since the next image
# is based on scratch, which doesn't have adduser, cat, echo, or even sh.
RUN echo "nobody:x:65534:65534:Nobody:/:" > /etc_passwd

# Copy the source code
COPY . /go/src/github.com/epifi/gamma

# Build the Go app with CGO_ENABLED=0 so we use the pure-Go implementations for
# things like DNS resolution (so we don't build a binary that depends on system
# libraries)
RUN make build-cron-script target=${target} dkronJobName=${job_name}

# The second and final stage
FROM scratch

# Accept service_name as build-arg
ARG job_name

# Copy the binary and config from the builder stage
COPY --from=builder /go/src/github.com/epifi/gamma/output/${job_name}/${job_name}_bin /my_bin
COPY --from=builder /go/src/github.com/epifi/gamma/output/${job_name}/config /config

# Copy the certs from the builder stage,
# this is needed if applications makes HTTPS requests
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# Copy the /etc_passwd file we created in the builder stage into /etc/passwd in
# the target stage. This creates a new non-root user as a security best
# practice.
COPY --from=builder /etc_passwd /etc/passwd

# Run as the new non-root by default
USER nobody

CMD ["/my_bin"]
