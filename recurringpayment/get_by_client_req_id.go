package recurringpayment

import (
	"context"
	"errors"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	recurringPaymentPb "github.com/epifi/gamma/api/recurringpayment"
)

// GetRecurringPaymentDetailsByClientReqId returns the recurring payment details for given client req id
func (s *Service) GetRecurringPaymentDetailsByClientReqId(ctx context.Context, req *recurringPaymentPb.GetRecurringPaymentDetailsByClientReqIdRequest) (*recurringPaymentPb.GetRecurringPaymentDetailsByClientReqIdResponse, error) {
	var (
		res = &recurringPaymentPb.GetRecurringPaymentDetailsByClientReqIdResponse{}
	)

	recurringPaymentAction, err := s.recurringPaymentActionsDao.GetByClientRequestId(ctx, req.GetClientReqId(), true)
	if err != nil {
		logger.Error(ctx, "error fetching recurring payment action for client req id",
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) || errors.Is(err, gorm.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.RecurringPaymentId = recurringPaymentAction.GetRecurringPaymentId()
	res.Status = rpc.StatusOk()
	return res, nil
}
