package recurringpayment

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/logger"

	recurringPaymentsPb "github.com/epifi/gamma/api/recurringpayment"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpPbPayload "github.com/epifi/gamma/api/recurringpayment/payload"
	"github.com/epifi/gamma/pkg/recurringpayment"
)

func (s *Service) RevokeRecurringPaymentV1(ctx context.Context, req *recurringPaymentsPb.RevokeRecurringPaymentV1Request) (*recurringPaymentsPb.RevokeRecurringPaymentV1Response, error) {
	fetchedAction, fetchedActionErr := s.recurringPaymentActionsDao.GetByClientRequestId(ctx, req.GetClientRequestId(), true)
	switch {
	case fetchedActionErr != nil && !errors.Is(fetchedActionErr, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "error while fetching recurring payment action", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(fetchedActionErr))
		return &rpPb.RevokeRecurringPaymentV1Response{
			Status: rpc.StatusInternalWithDebugMsg(fetchedActionErr.Error()),
		}, nil
	case fetchedAction != nil:
		logger.Info(ctx, "recurring payment action already exists for given client request id", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()))
		return &rpPb.RevokeRecurringPaymentV1Response{
			Status: rpc.StatusAlreadyExists(),
		}, nil
	}

	fetchedRecurringPayment, fetchedRecurringPaymentErr := s.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	if fetchedRecurringPaymentErr != nil {
		logger.Error(ctx, "error while fetching recurring payment by id", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(fetchedRecurringPaymentErr))
		return &rpPb.RevokeRecurringPaymentV1Response{
			Status: rpc.StatusInternalWithDebugMsg(fetchedRecurringPaymentErr.Error()),
		}, nil
	}
	if fetchedRecurringPayment.GetState().IsAnyActionInProgress() {
		logger.Error(ctx, "cannot initiate revoke recurring payment flow as there are pending actions", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		return &rpPb.RevokeRecurringPaymentV1Response{
			Status: rpc.StatusFailedPrecondition(),
		}, nil
	}
	if fetchedRecurringPayment.GetState() == recurringPaymentsPb.RecurringPaymentState_REVOKED {
		logger.Info(ctx, "recurring payment is already revoked", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		return &rpPb.RevokeRecurringPaymentV1Response{
			Status: rpc.StatusFailedPrecondition(),
		}, nil
	}
	initialRecurringPaymentStatus := fetchedRecurringPayment.GetState()

	ctx = epificontext.WithOwnership(ctx, fetchedRecurringPayment.GetEntityOwnership())

	txnProvider, txnProviderErr := s.txnExecutorProvider.GetResourceForOwnership(fetchedRecurringPayment.GetEntityOwnership())
	if txnProviderErr != nil {
		logger.Error(
			ctx,
			"error in fetching transaction executor provider for given ownership",
			zap.String(logger.OWNERSHIP, fetchedRecurringPayment.GetEntityOwnership().String()),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()),
		)
		return &rpPb.RevokeRecurringPaymentV1Response{
			Status: rpc.StatusInternal(),
		}, nil
	}

	txnErr := txnProvider.RunTxn(ctx, func(txnCtx context.Context) error {
		updRpErr := s.updateRecurringPayment(txnCtx, fetchedRecurringPayment, nil,
			fetchedRecurringPayment.GetState(), recurringPaymentsPb.RecurringPaymentState_REVOKE_QUEUED)
		if updRpErr != nil {
			return fmt.Errorf("error in updating recurring payment state %w", updRpErr)
		}

		// create the recurring payment action for revoke
		rpa, createRpaErr := s.createRecurringPaymentAction(
			txnCtx,
			req.GetRecurringPaymentId(),
			req.GetClientRequestId(),
			"",
			recurringPaymentsPb.Action_REVOKE,
			nil,
			recurringPaymentsPb.ActionState_ACTION_CREATED,
			"",
			nil,
			nil,
			nil,
		)
		if createRpaErr != nil {
			return fmt.Errorf("error in creating recurring payment action for revoke: %w", createRpaErr)
		}

		// This must be the last call in the transaction block since we want to initiate the workflow execution only if
		// all the DB entities are updated to their correct initial state for revoke. Not initiating the workflow, should
		// roll back all the changes made to the DB.
		fetchedRecurringPayment.State = recurringPaymentsPb.RecurringPaymentState_REVOKE_QUEUED
		initiateWorkflowErr := s.initiateRevokeV1Workflow(txnCtx, fetchedRecurringPayment, rpa, initialRecurringPaymentStatus, fetchedRecurringPayment.GetEntityOwnership())
		if initiateWorkflowErr != nil {
			return fmt.Errorf("error while initiating workflow: %w", initiateWorkflowErr)
		}
		return nil
	})
	if txnErr != nil {
		logger.Error(
			ctx,
			"error in in invoking transaction block to initiate revoke recurring payment",
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()),
			zap.Error(txnErr),
		)
		return &rpPb.RevokeRecurringPaymentV1Response{
			Status: rpc.StatusInternalWithDebugMsg("error in initiating recurring payment revoke flow"),
		}, nil
	}
	return &rpPb.RevokeRecurringPaymentV1Response{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) initiateRevokeV1Workflow(ctx context.Context, recurringPayment *rpPb.RecurringPayment, recurringPaymentAction *rpPb.RecurringPaymentsAction, initialRecurringPaymentStatus recurringPaymentsPb.RecurringPaymentState, entityOwnership commontypes.Ownership) error {
	workflowPayload, marshalErr := protojson.Marshal(&rpPbPayload.RevokeRecurringPaymentV1WorkflowPayload{
		RecurringPaymentId:            recurringPayment.GetId(),
		OriginalRecurringPaymentState: initialRecurringPaymentStatus.ToNewRecurringPaymentState(),
	})
	if marshalErr != nil {
		return fmt.Errorf("error while marshalling recurring payment without auth execution workflow payload,err: %w", marshalErr)
	}
	initiateWorkflowRes, initiateWorkflowErr := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ClientReqId: &workflowPb.ClientReqId{
				// Using the recurring payment action ID as the workflow client-req-id since in
				// recurring_payments_actions table unique index is added only on the ID column
				// and not on the client_req_id column.
				Id:     recurringPaymentAction.GetId(),
				Client: workflowPb.Client_RECURRING_PAYMENT,
			},
			Payload:          workflowPayload,
			Ownership:        entityOwnership,
			QualityOfService: celestialPb.QoS_GUARANTEED,
			Version:          workflowPb.Version_V0,
			Type:             celestial.GetTypeEnumFromWorkflowType(rpNs.RevokeRecurringPaymentV1),
			UseCase: recurringpayment.GetUseCaseForOwnership(&recurringpayment.GetUseCaseForOwnershipReq{
				PaymentRoute: recurringPayment.GetPaymentRoute(),
				Ownership:    entityOwnership,
			}),
		},
	})
	if err := epifigrpc.RPCError(initiateWorkflowRes, initiateWorkflowErr); err != nil {
		return fmt.Errorf("error while initiating recurring payment without auth execution v1 workflow, err: %w", err)
	}
	return nil
}
