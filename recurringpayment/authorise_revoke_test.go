package recurringpayment

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/money"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	actorPb "github.com/epifi/gamma/api/actor"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	orderPb "github.com/epifi/gamma/api/order"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	pb "github.com/epifi/gamma/api/recurringpayment"
	payloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	"github.com/epifi/gamma/api/recurringpayment/standinginstruction/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	mocks2 "github.com/epifi/gamma/recurringpayment/internal/mocks"
)

func TestService_AuthoriseRecurringPaymentRevoke(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockOrderClient := orderMocks.NewMockOrderServiceClient(ctr)
	mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
	mockRecurringPaymentsActionDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
	mockSIClient := mocks.NewMockStandingInstructionServiceClient(ctr)
	mockRecurringPaymentProcessor := mocks2.NewMockRecurringPaymentProcessor(ctr)
	mockActor := actorMocks.NewMockActorClient(ctr)
	mockCelestialProcessor := mocks2.NewMockCelestialProcessor(ctr)
	mockRecurringPaymentVendorDetailsDao := daoMocks.NewMockRecurringPaymentsVendorDetailsDao(ctr)

	svc := NewService(mockRecurringPaymentDao, mockOrderClient, mockSIClient, mockRecurringPaymentsActionDao, nil, mockActor, nil, nil, nil, conf, mockRecurringPaymentProcessor, nil, nil, nil, nil, nil, nil, nil, dynamicConf.FeatureFlags(), mockCelestialProcessor, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, mockRecurringPaymentVendorDetailsDao, nil, nil, nil, nil)

	startDate := timestampPb.New(time.Now())
	endDate := timestampPb.New(time.Now().Add(48 * time.Hour))

	recurringPayment1 := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns: 10,
		PartnerBank:        commonvgpb.Vendor_FEDERAL_BANK,
		State:              pb.RecurringPaymentState_REVOKE_QUEUED,
		Ownership:          pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:       pb.UIEntryPoint_FIT,
		InitiatedBy:        pb.InitiatedBy_PAYER,
	}
	updatedRecurringPayment1 := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns: 10,
		PartnerBank:        commonvgpb.Vendor_FEDERAL_BANK,
		State:              pb.RecurringPaymentState_REVOKE_AUTHORISED,
		Ownership:          pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:       pb.UIEntryPoint_FIT,
		InitiatedBy:        pb.InitiatedBy_PAYER,
	}
	recurringPayment2 := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		State:       pb.RecurringPaymentState_REVOKE_QUEUED,
	}
	updatedRecurringPayment2 := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		State:       pb.RecurringPaymentState_REVOKE_AUTHORISED,
	}
	recurringPayment3 := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		State:       pb.RecurringPaymentState_ACTIVATED,
	}
	// sending signal to workflow
	signalPayload, _ := protojson.Marshal(&payloadPb.RevokeRecurringPaymentAuthSignal{})

	tests := []struct {
		name           string
		req            *pb.AuthoriseRecurringPaymentRevokeRequest
		setupMockCalls func()
		want           *pb.AuthoriseRecurringPaymentRevokeResponse
		wantErr        bool
	}{
		{
			name: "authorised revoke attempt successfully using oms flow",
			req: &pb.AuthoriseRecurringPaymentRevokeRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "pin"}},
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentRevokeViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment1, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_REVOKE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}, nil)
				mockActor.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  &types.Actor{Type: types.Actor_USER},
				}, nil)
				mockOrderClient.EXPECT().InitiateOrderProcessing(gomock.Any(), &orderPb.InitiateOrderProcessingRequest{Identifier: &orderPb.InitiateOrderProcessingRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.InitiateOrderProcessingResponse{Status: rpc.StatusOk()}, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment1,
					nil, pb.RecurringPaymentState_REVOKE_QUEUED, pb.RecurringPaymentState_REVOKE_AUTHORISED).Return(nil)
				mockSIClient.EXPECT().AuthoriseRevoke(gomock.Any(), &siPb.AuthoriseRevokeRequest{
					RecurringPaymentId:  "rp-1",
					PartnerSdkCredBlock: "pin",
				}).Return(&siPb.AuthoriseRevokeResponse{Status: rpc.StatusOk()}, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), updatedRecurringPayment1,
					nil, pb.RecurringPaymentState_REVOKE_AUTHORISED, pb.RecurringPaymentState_REVOKED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_REVOKE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}, nil, pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_SUCCESS).Return(nil)
			},
			want:    &pb.AuthoriseRecurringPaymentRevokeResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
		{
			name: "authorised revoke attempt successfully using celestial flow",
			req: &pb.AuthoriseRecurringPaymentRevokeRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "pin"}},
				ClientRequestId:    "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				CurrentActorId: "actor-1",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentRevokeViaCelestial(true, true, nil)
				if err != nil {
					return
				}
				recurringPayment1.State = pb.RecurringPaymentState_REVOKE_QUEUED
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment1, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_REVOKE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}, nil)
				mockActor.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  &types.Actor{Type: types.Actor_USER},
				}, nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).Return(&celestialPb.WorkflowRequest{
					Id:        "workflow_req_id",
					CreatedAt: timestampPb.New(time.Now().Add(-9 * time.Minute)),
				}, nil)
				mockCelestialProcessor.EXPECT().SignalWorkflow(gomock.Any(), "client-req-1",
					string(rpNs.RevokeRecurringPaymentAuthSignal), workflowPb.Client_RECURRING_PAYMENT, signalPayload, gomock.Any(), gomock.Any()).Return(nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment1,
					nil, pb.RecurringPaymentState_REVOKE_QUEUED, pb.RecurringPaymentState_REVOKE_AUTHORISED).Return(nil)
				mockSIClient.EXPECT().AuthoriseRevoke(gomock.Any(), &siPb.AuthoriseRevokeRequest{
					RecurringPaymentId:  "rp-1",
					PartnerSdkCredBlock: "pin",
				}).Return(&siPb.AuthoriseRevokeResponse{Status: rpc.StatusOk()}, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), updatedRecurringPayment1,
					nil, pb.RecurringPaymentState_REVOKE_AUTHORISED, pb.RecurringPaymentState_REVOKED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_REVOKE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}, nil, pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_SUCCESS).Return(nil)
			},
			want:    &pb.AuthoriseRecurringPaymentRevokeResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
		{
			name: "authorisation failed at domain service using oms flow",
			req: &pb.AuthoriseRecurringPaymentRevokeRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "pin"}},
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentRevokeViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment2, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_REVOKE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}, nil)
				mockActor.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  &types.Actor{Type: types.Actor_USER},
				}, nil)
				mockOrderClient.EXPECT().InitiateOrderProcessing(gomock.Any(), &orderPb.InitiateOrderProcessingRequest{Identifier: &orderPb.InitiateOrderProcessingRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.InitiateOrderProcessingResponse{Status: rpc.StatusOk()}, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment2,
					nil, pb.RecurringPaymentState_REVOKE_QUEUED, pb.RecurringPaymentState_REVOKE_AUTHORISED).Return(nil)
				mockSIClient.EXPECT().AuthoriseRevoke(gomock.Any(), &siPb.AuthoriseRevokeRequest{
					RecurringPaymentId:  "rp-1",
					PartnerSdkCredBlock: "pin",
				}).Return(&siPb.AuthoriseRevokeResponse{Status: rpc.StatusInternal()}, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), updatedRecurringPayment2,
					nil, pb.RecurringPaymentState_REVOKE_AUTHORISED, pb.RecurringPaymentState_ACTIVATED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_REVOKE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}, nil, pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_FAILURE).Return(nil)
			},
			want:    &pb.AuthoriseRecurringPaymentRevokeResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "authorisation failed at domain service using celestial flow",
			req: &pb.AuthoriseRecurringPaymentRevokeRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "pin"}},
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				recurringPayment2.State = pb.RecurringPaymentState_REVOKE_QUEUED
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentRevokeViaCelestial(true, true, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment2, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_REVOKE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}, nil)
				mockActor.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  &types.Actor{Type: types.Actor_USER},
				}, nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).Return(&celestialPb.WorkflowRequest{
					Id:        "workflow_req_id",
					CreatedAt: timestampPb.New(time.Now().Add(-9 * time.Minute)),
				}, nil)
				mockCelestialProcessor.EXPECT().SignalWorkflow(gomock.Any(), "client-req-1",
					string(rpNs.RevokeRecurringPaymentAuthSignal), workflowPb.Client_RECURRING_PAYMENT, signalPayload, gomock.Any(), gomock.Any()).Return(nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment2,
					nil, pb.RecurringPaymentState_REVOKE_QUEUED, pb.RecurringPaymentState_REVOKE_AUTHORISED).Return(nil)
				mockSIClient.EXPECT().AuthoriseRevoke(gomock.Any(), &siPb.AuthoriseRevokeRequest{
					RecurringPaymentId:  "rp-1",
					PartnerSdkCredBlock: "pin",
				}).Return(&siPb.AuthoriseRevokeResponse{Status: rpc.StatusInternal()}, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), updatedRecurringPayment2,
					nil, pb.RecurringPaymentState_REVOKE_AUTHORISED, pb.RecurringPaymentState_ACTIVATED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_REVOKE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}, nil, pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_FAILURE).Return(nil)
			},
			want:    &pb.AuthoriseRecurringPaymentRevokeResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "failed to revoke, failed to fetch workflow for given client req ID",
			req: &pb.AuthoriseRecurringPaymentRevokeRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "pin"}},
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentRevokeViaCelestial(true, true, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment2, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_REVOKE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}, nil)
				mockActor.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  &types.Actor{Type: types.Actor_USER},
				}, nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).Return(
					nil, fmt.Errorf("record not found %v %w", epifierrors.ErrRecordNotFound, rpc.StatusAsError(rpc.StatusRecordNotFound())))
			},
			want:    &pb.AuthoriseRecurringPaymentRevokeResponse{Status: rpc.StatusRecordNotFound()},
			wantErr: false,
		},
		{
			name: "failed to revoke, workflow timed out after 10 mins",
			req: &pb.AuthoriseRecurringPaymentRevokeRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "pin"}},
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentRevokeViaCelestial(true, true, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment2, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_REVOKE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}, nil)
				mockActor.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  &types.Actor{Type: types.Actor_USER},
				}, nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).Return(&celestialPb.WorkflowRequest{
					Id:        "workflow_req_id",
					CreatedAt: timestampPb.New(time.Now().Add(-15 * time.Minute)),
				}, nil)
			},
			want:    &pb.AuthoriseRecurringPaymentRevokeResponse{Status: rpc.StatusPermissionDenied()},
			wantErr: false,
		},
		{
			name: "failed to revoke, failed to signal workflow from celestial",
			req: &pb.AuthoriseRecurringPaymentRevokeRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "pin"}},
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentRevokeViaCelestial(true, true, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment2, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_REVOKE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}, nil)
				mockActor.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  &types.Actor{Type: types.Actor_USER},
				}, nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).Return(&celestialPb.WorkflowRequest{
					Id:        "workflow_req_id",
					CreatedAt: timestampPb.New(time.Now().Add(-9 * time.Minute)),
				}, nil)
				mockCelestialProcessor.EXPECT().SignalWorkflow(gomock.Any(), "client-req-1",
					string(rpNs.RevokeRecurringPaymentAuthSignal), workflowPb.Client_RECURRING_PAYMENT, signalPayload, gomock.Any(), gomock.Any()).Return(
					rpc.StatusAsError(rpc.StatusInternal()))
			},
			want:    &pb.AuthoriseRecurringPaymentRevokeResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "state validations failed",
			req: &pb.AuthoriseRecurringPaymentRevokeRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "pin"}},
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
			},
			setupMockCalls: func() {
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment3, nil)
			},
			want:    &pb.AuthoriseRecurringPaymentRevokeResponse{Status: rpc.StatusFailedPrecondition()},
			wantErr: false,
		},
		{
			name: "failed to fetch action",
			req: &pb.AuthoriseRecurringPaymentRevokeRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "pin"}},
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
			},
			setupMockCalls: func() {
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment2, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(nil, fmt.Errorf("error"))
			},
			want:    &pb.AuthoriseRecurringPaymentRevokeResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.AuthoriseRecurringPaymentRevoke(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AuthoriseRecurringPaymentRevoke() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("AuthoriseRecurringPaymentRevoke() got = %v, want %v", got, tt.want)
			}
		})
	}
}
