package developer

import (
	"context"

	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/recurringpayment/developer"
)

type IParameterFetcher interface {
	FetchParamList(ctx context.Context, entity developer.RecurringPaymentEntity) ([]*cxDsPb.ParameterMeta, error)
}

type IDataFetcher interface {
	FetchData(ctx context.Context, entity developer.RecurringPaymentEntity, filters []*cxDsPb.Filter) (string, error)
}
