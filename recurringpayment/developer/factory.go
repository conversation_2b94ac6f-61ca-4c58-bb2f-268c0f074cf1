package developer

import (
	"fmt"

	"github.com/epifi/gamma/api/recurringpayment/developer"
	"github.com/epifi/gamma/recurringpayment/developer/processor"
)

type DevFactory struct {
	devRecurringPaymentEntity               *processor.DevRecurringPaymentEntity
	devRecurringPaymentActionEntity         *processor.DevRecurringPaymentActionEntity
	devRecurringPaymentsVendorDetailsEntity *processor.DevRecurringPaymentsVendorDetailsEntity
	devStandingInstructionEntity            *processor.DevSIEntity
	devStandingInstructionRequestEntity     *processor.DevSIRequestEntity
}

func NewDevFactory(devRecurringPaymentEntity *processor.DevRecurringPaymentEntity,
	devRecurringPaymentActionEntity *processor.DevRecurringPaymentActionEntity,
	devRecurringPaymentsVendorDetailsEntity *processor.DevRecurringPaymentsVendorDetailsEntity,
	devStandingInstructionEntity *processor.DevSIEntity, devStandingInstructionRequestEntity *processor.DevSIRequestEntity) *DevFactory {
	return &DevFactory{
		devRecurringPaymentEntity:               devRecurringPaymentEntity,
		devRecurringPaymentActionEntity:         devRecurringPaymentActionEntity,
		devRecurringPaymentsVendorDetailsEntity: devRecurringPaymentsVendorDetailsEntity,
		devStandingInstructionEntity:            devStandingInstructionEntity,
		devStandingInstructionRequestEntity:     devStandingInstructionRequestEntity,
	}
}

func (d *DevFactory) getParameterListImpl(entity developer.RecurringPaymentEntity) (IParameterFetcher, error) {
	switch entity {
	case developer.RecurringPaymentEntity_RECURRING_PAYMENTS:
		return d.devRecurringPaymentEntity, nil
	case developer.RecurringPaymentEntity_RECURRING_PAYMENT_ACTIONS:
		return d.devRecurringPaymentActionEntity, nil
	case developer.RecurringPaymentEntity_RECURRING_PAYMENT_VENDOR_DETAILS:
		return d.devRecurringPaymentsVendorDetailsEntity, nil
	case developer.RecurringPaymentEntity_STANDING_INSTRUCTIONS:
		return d.devStandingInstructionEntity, nil
	case developer.RecurringPaymentEntity_STANDING_INSTRUCTION_REQUESTS:
		return d.devStandingInstructionRequestEntity, nil
	default:
		return nil, fmt.Errorf("no valid implementation found")
	}
}

func (d *DevFactory) getDataImpl(entity developer.RecurringPaymentEntity) (IDataFetcher, error) {
	switch entity {
	case developer.RecurringPaymentEntity_RECURRING_PAYMENTS:
		return d.devRecurringPaymentEntity, nil
	case developer.RecurringPaymentEntity_RECURRING_PAYMENT_ACTIONS:
		return d.devRecurringPaymentActionEntity, nil
	case developer.RecurringPaymentEntity_RECURRING_PAYMENT_VENDOR_DETAILS:
		return d.devRecurringPaymentsVendorDetailsEntity, nil
	case developer.RecurringPaymentEntity_STANDING_INSTRUCTIONS:
		return d.devStandingInstructionEntity, nil
	case developer.RecurringPaymentEntity_STANDING_INSTRUCTION_REQUESTS:
		return d.devStandingInstructionRequestEntity, nil
	default:
		return nil, fmt.Errorf("no valid implementation found")
	}
}
