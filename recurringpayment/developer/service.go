package developer

import (
	"context"
	"errors"

	"go.uber.org/zap"
	"gorm.io/gorm"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/recurringpayment/developer"
)

type RecurringPaymentDevService struct {
	fac *DevFactory
}

func NewRecurringPaymentDevService(fac *DevFactory) *RecurringPaymentDevService {
	return &RecurringPaymentDevService{
		fac: fac,
	}
}

func (c *RecurringPaymentDevService) GetEntityList(ctx context.Context, req *cxDsPb.GetEntityListRequest) (*cxDsPb.GetEntityListResponse, error) {
	return &cxDsPb.GetEntityListResponse{
		Status: rpcPb.StatusOk(),
		EntityList: []string{developer.RecurringPaymentEntity_RECURRING_PAYMENTS.String(), developer.RecurringPaymentEntity_RECURRING_PAYMENT_ACTIONS.String(),
			developer.RecurringPaymentEntity_STANDING_INSTRUCTIONS.String(), developer.RecurringPaymentEntity_STANDING_INSTRUCTION_REQUESTS.String(), developer.RecurringPaymentEntity_RECURRING_PAYMENT_VENDOR_DETAILS.String()},
	}, nil
}

func (c *RecurringPaymentDevService) GetParameterList(ctx context.Context, req *cxDsPb.GetParameterListRequest) (*cxDsPb.GetParameterListResponse, error) {
	ent, ok := developer.RecurringPaymentEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetParameterListResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available"),
		}, nil
	}
	paramFetcher, err := c.fac.getParameterListImpl(developer.RecurringPaymentEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available", zap.Error(err))
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	paramList, err := paramFetcher.FetchParamList(ctx, developer.RecurringPaymentEntity(ent))
	if err != nil {
		logger.Error(ctx, "unable to fetch parameter list", zap.Error(err))
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxDsPb.GetParameterListResponse{
		Status:        rpcPb.StatusOk(),
		ParameterList: paramList,
	}, nil
}

func (c *RecurringPaymentDevService) GetData(ctx context.Context, req *cxDsPb.GetDataRequest) (*cxDsPb.GetDataResponse, error) {
	ent, ok := developer.RecurringPaymentEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetDataResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available"),
		}, nil
	}
	dataFetcher, err := c.fac.getDataImpl(developer.RecurringPaymentEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available", zap.Error(err))
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	jsonResp, err := dataFetcher.FetchData(ctx, developer.RecurringPaymentEntity(ent), req.GetFilters())
	if err != nil {
		logger.Error(ctx, "unable to get data", zap.Error(err))
		if errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &cxDsPb.GetDataResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxDsPb.GetDataResponse{
		Status:       rpcPb.StatusOk(),
		JsonResponse: jsonResp,
	}, nil
}
