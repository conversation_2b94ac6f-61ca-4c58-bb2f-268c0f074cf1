// nolint
package processor

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/proto/json"

	"github.com/epifi/be-common/pkg/logger"

	pb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/recurringpayment/dao"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/recurringpayment/developer"
)

type DevRecurringPaymentActionEntity struct {
	recurringPaymentsActionDao dao.RecurringPaymentsActionDao
}

func NewDevRecurringPaymentActionEntity(recurringPaymentsActionDao dao.RecurringPaymentsActionDao) *DevRecurringPaymentActionEntity {
	return &DevRecurringPaymentActionEntity{
		recurringPaymentsActionDao: recurringPaymentsActionDao,
	}
}

func (d *DevRecurringPaymentActionEntity) FetchParamList(ctx context.Context, entity developer.RecurringPaymentEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            ID,
			Label:           "ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            ClientRequestId,
			Label:           "Client Request ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            VendorRequestId,
			Label:           "Vendor Request Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            RecurringPaymentID,
			Label:           "Recurring Payment Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            Action,
			Label:           "Action Type",
			Type:            db_state.ParameterDataType_DROPDOWN,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
			Options:         getActions(),
		},
		{
			Name:            ownership,
			Label:           "Ownership",
			Type:            db_state.ParameterDataType_DROPDOWN,
			Options:         getOwnershipList(),
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (d *DevRecurringPaymentActionEntity) FetchData(ctx context.Context, entity developer.RecurringPaymentEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", ErrNilFilter
	}
	var (
		id                      string
		vendorRequestId         string
		clientRequestId         string
		recurringPaymentID      string
		actionType              string
		data                    []byte
		err                     error
		recurringPaymentAction  *pb.RecurringPaymentsAction
		recurringPaymentActions []*pb.RecurringPaymentsAction
		inputOwnership          commontypes.Ownership
	)
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case ID:
			id = filter.GetStringValue()
		case ClientRequestId:
			clientRequestId = filter.GetStringValue()
		case VendorRequestId:
			vendorRequestId = filter.GetStringValue()
		case RecurringPaymentID:
			recurringPaymentID = filter.GetStringValue()
		case Action:
			actionType = filter.GetDropdownValue()
		case ownership:
			inputOwnership = commontypes.Ownership(commontypes.Ownership_value[filter.GetDropdownValue()])
		default:
			return "", fmt.Errorf("unknown parameter name %s", filter.GetParameterName())
		}
	}
	ctx = epificontext.WithOwnership(ctx, inputOwnership)

	switch {
	case id != "":
		recurringPaymentAction, err = d.recurringPaymentsActionDao.GetById(ctx, id)
		if err != nil {
			logger.Error(ctx, "could not fetch recurring payment action details by id filter", zap.String(logger.ID, id), zap.Error(err))
			return "", err
		}
		data, err = json.Marshal(recurringPaymentAction)
	case clientRequestId != "":
		recurringPaymentAction, err = d.recurringPaymentsActionDao.GetByClientRequestId(ctx, clientRequestId, false)
		if err != nil {
			logger.Error(ctx, "could not fetch recurring payment action details by client request id filter", zap.String(logger.CLIENT_REQUEST_ID, clientRequestId), zap.Error(err))
			return "", err
		}
		data, err = json.Marshal(recurringPaymentAction)
	case vendorRequestId != "":
		recurringPaymentAction, err = d.recurringPaymentsActionDao.GetByVendorRequestId(ctx, vendorRequestId)
		if err != nil {
			logger.Error(ctx, "could not fetch recurring payment action details by vendor request id filter", zap.String(logger.VENDOR_REQUEST, vendorRequestId), zap.Error(err))
			return "", err
		}
		data, err = json.Marshal(recurringPaymentAction)
	case recurringPaymentID != "":
		if actionType != "" && actionType != pb.Action_ACTION_UNSPECIFIED.String() {
			action := pb.Action(pb.Action_value[actionType])
			recurringPaymentActions, err = d.recurringPaymentsActionDao.GetByRecurringPaymentId(ctx, recurringPaymentID, dao.WithActionsFilter([]pb.Action{action}))
		} else {
			recurringPaymentActions, err = d.recurringPaymentsActionDao.GetByRecurringPaymentId(ctx, recurringPaymentID)
		}
		if err != nil {
			logger.Error(ctx, "could not fetch recurring payment action details by recurring payment id filter", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentID), zap.Error(err))
			return "", err
		}
		data, err = json.Marshal(recurringPaymentActions)
	default:
		logger.Error(ctx, "could not fetch recurring payment action details for given parameters", zap.String(logger.ACTION_TYPE, actionType), zap.Error(err))
		return "", err
	}
	if err != nil {
		return "", fmt.Errorf("cannot marshal recurring payment actions struct to json: %w", err)
	}
	return string(data), nil
}

func getActions() []string {
	var actions []string
	for _, action := range pb.Action_name {
		if action != pb.Action_ACTION_UNSPECIFIED.String() {
			actions = append(actions, action)
		}
	}
	return actions
}

func getOwnershipList() []string {
	ownershipList := make([]string, 0)
	for _, ownership := range commontypes.Ownership_name {
		ownershipList = append(ownershipList, ownership)
	}
	return ownershipList
}
