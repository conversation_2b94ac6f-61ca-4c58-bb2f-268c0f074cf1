package processor

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/proto/json"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"

	"github.com/epifi/gamma/recurringpayment/dao"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/recurringpayment/developer"
)

type DevSIEntity struct {
	siDao dao.StandingInstructionDao
}

func NewDevSIEntity(siDao dao.StandingInstructionDao) *DevSIEntity {
	return &DevSIEntity{
		siDao: siDao,
	}
}

func (d *DevSIEntity) FetchParamList(ctx context.Context, entity developer.RecurringPaymentEntity) (
	[]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            RecurringPaymentID,
			Label:           "Recurring Payment ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (d *DevSIEntity) FetchData(ctx context.Context, entity developer.RecurringPaymentEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", ErrNilFilter
	}
	var (
		recurringPaymentID  string
		data                []byte
		standingInstruction *siPb.StandingInstruction
		err                 error
	)
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case RecurringPaymentID:
			recurringPaymentID = filter.GetStringValue()
		default:
			return "", fmt.Errorf("unknown parameter name %s", filter.GetParameterName())
		}
	}

	standingInstruction, err = d.siDao.GetByRecurringPaymentId(ctx, recurringPaymentID)
	if err != nil {
		logger.Error(ctx, "could not fetch standing instruction details for given recurring payment id", zap.String(
			logger.RECURRING_PAYMENT_ID, recurringPaymentID), zap.Error(err))
		return "", err
	}
	data, err = json.Marshal(standingInstruction)
	if err != nil {
		return "", fmt.Errorf("cannot marshal standing instruction struct to json: %w", err)
	}
	return string(data), nil
}
