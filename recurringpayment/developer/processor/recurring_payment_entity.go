// nolint
package processor

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/proto/json"

	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/recurringpayment/dao"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/recurringpayment/developer"
)

type DevRecurringPaymentEntity struct {
	recurringPaymentsDao dao.RecurringPaymentDao
}

func NewDevRecurringPaymentEntity(recurringPaymentsDao dao.RecurringPaymentDao) *DevRecurringPaymentEntity {
	return &DevRecurringPaymentEntity{
		recurringPaymentsDao: recurringPaymentsDao,
	}
}

func (d *DevRecurringPaymentEntity) FetchParamList(ctx context.Context, entity developer.RecurringPaymentEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            ID,
			Label:           "Recurring Payment ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            ExternalId,
			Label:           "External Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            ownership,
			Label:           "Ownership",
			Type:            db_state.ParameterDataType_DROPDOWN,
			Options:         getOwnershipList(),
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (d *DevRecurringPaymentEntity) FetchData(ctx context.Context, entity developer.RecurringPaymentEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", ErrNilFilter
	}
	var (
		data             []byte
		id               string
		externalId       string
		recurringPayment *recurringpayment.RecurringPayment
		inputOwnership   commontypes.Ownership
		err              error
	)
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case ID:
			id = filter.GetStringValue()
		case ExternalId:
			externalId = filter.GetStringValue()
		case ownership:
			inputOwnership = commontypes.Ownership(commontypes.Ownership_value[filter.GetDropdownValue()])
		default:
			return "", fmt.Errorf("unknown parameter name %s", filter.GetParameterName())
		}
	}
	ctx = epificontext.WithOwnership(ctx, inputOwnership)

	switch {
	case id != "":
		recurringPayment, err = d.recurringPaymentsDao.GetById(ctx, id)
		if err != nil {
			logger.Error(ctx, "could not fetch recurring payment details for given id", zap.String(logger.ID, id), zap.Error(err))
			return "", err
		}
	case externalId != "":
		recurringPayment, err = d.recurringPaymentsDao.GetByExternalId(ctx, externalId)
		if err != nil {
			logger.Error(ctx, "could not fetch recurring payment details for given external id", zap.String(logger.EXTERNAL_ID, externalId), zap.Error(err))
			return "", err
		}
	}
	recurringPayment.Amount = nil
	data, err = json.Marshal(recurringPayment)
	if err != nil {
		return "", fmt.Errorf("cannot marshal recurring payment struct to json: %w", err)
	}
	return string(data), nil
}
