package processor

import (
	"context"
	"fmt"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/proto/json"
	"github.com/epifi/gamma/api/recurringpayment"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/recurringpayment/dao"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/recurringpayment/developer"
)

type DevRecurringPaymentsVendorDetailsEntity struct {
	recurringPaymentsVendorDetailsDao dao.RecurringPaymentsVendorDetailsDao
}

func NewDevRecurringPaymentsVendorDetailsEntity(recurringPaymentsVendorDetailsDao dao.RecurringPaymentsVendorDetailsDao) *DevRecurringPaymentsVendorDetailsEntity {
	return &DevRecurringPaymentsVendorDetailsEntity{
		recurringPaymentsVendorDetailsDao: recurringPaymentsVendorDetailsDao,
	}
}

func (d *DevRecurringPaymentsVendorDetailsEntity) FetchParamList(ctx context.Context, entity developer.RecurringPaymentEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            ID,
			Label:           "ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            ActorId,
			Label:           "Actor ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            RecurringPaymentID,
			Label:           "Recurring Payment Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            VendorCustomerId,
			Label:           "Vendor Customer ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            VendorPaymentId,
			Label:           "Vendor Payment ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            ownership,
			Label:           "Ownership",
			Type:            db_state.ParameterDataType_DROPDOWN,
			Options:         getOwnershipList(),
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (d *DevRecurringPaymentsVendorDetailsEntity) FetchData(ctx context.Context, entity developer.RecurringPaymentEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", ErrNilFilter
	}
	var (
		id                                 string
		actorId                            string
		recurringPaymentId                 string
		vendorCustomerId                   string
		vendorPaymentId                    string
		data                               []byte
		err                                error
		recurringPaymentsVendorDetails     *recurringpayment.RecurringPaymentsVendorDetails
		recurringPaymentsVendorDetailsList []*recurringpayment.RecurringPaymentsVendorDetails
		inputOwnership                     commontypes.Ownership
	)
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case ID:

			id = filter.GetStringValue()
		case ActorId:
			actorId = filter.GetStringValue()
		case RecurringPaymentID:

			recurringPaymentId = filter.GetStringValue()
		case VendorCustomerId:

			vendorCustomerId = filter.GetStringValue()
		case VendorPaymentId:

			vendorPaymentId = filter.GetStringValue()
		case ownership:
			inputOwnership = commontypes.Ownership(commontypes.Ownership_value[filter.GetDropdownValue()])
		default:
			return "", fmt.Errorf("unknown parameter name %s", filter.GetParameterName())
		}
	}
	ctx = epificontext.WithOwnership(ctx, inputOwnership)

	switch {
	case id != "":
		recurringPaymentsVendorDetails, err = d.recurringPaymentsVendorDetailsDao.GetById(ctx, id)
		if err != nil {
			logger.Error(ctx, "could not fetch recurring payment vendor details by id ", zap.String(logger.ID, id), zap.Error(err))
			return "", err
		}
		data, err = json.Marshal(recurringPaymentsVendorDetails)
	case actorId != "":
		recurringPaymentsVendorDetailsList, err = d.recurringPaymentsVendorDetailsDao.GetByActorId(ctx, actorId)
		if err != nil {
			logger.Error(ctx, "could not fetch recurring payment vendor details by actor id ", zap.String(logger.ACTOR_ID, actorId), zap.Error(err))
			return "", err
		}
		data, err = json.Marshal(recurringPaymentsVendorDetailsList)
	case recurringPaymentId != "":
		recurringPaymentsVendorDetails, err = d.recurringPaymentsVendorDetailsDao.GetByRecurringPaymentId(ctx, recurringPaymentId)
		if err != nil {
			logger.Error(ctx, "could not fetch recurring payment vendor details by recurring payment id ", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId), zap.Error(err))
			return "", err
		}
		data, err = json.Marshal(recurringPaymentsVendorDetails)
	case vendorCustomerId != "":
		recurringPaymentsVendorDetails, err = d.recurringPaymentsVendorDetailsDao.GetByVendorCustomerId(ctx, vendorCustomerId)
		if err != nil {
			logger.Error(ctx, "could not fetch recurring payment vendor details by vendor customer id ", zap.String(logger.VENDOR_CUSTOMER_ID, vendorCustomerId), zap.Error(err))
			return "", err
		}
		data, err = json.Marshal(recurringPaymentsVendorDetails)
	case vendorPaymentId != "":
		recurringPaymentsVendorDetails, err = d.recurringPaymentsVendorDetailsDao.GetByVendorPaymentId(ctx, vendorPaymentId)
		if err != nil {
			logger.Error(ctx, "could not fetch recurring payment vendor details by vendor payment id ", zap.String(logger.VENDOR_PAYMENT_ID, vendorPaymentId), zap.Error(err))
			return "", err
		}
		data, err = json.Marshal(recurringPaymentsVendorDetails)
	default:
		logger.Error(ctx, "invalid parameters passed for fetching recurring_payment_vendor_details", zap.Error(err))
		return "", fmt.Errorf("invalid parameters passed for fetching recurring payment vendor details: %w", epifierrors.ErrInvalidArgument)
	}
	if err != nil {
		return "", fmt.Errorf("cannot marshal recurring payment struct to json: %w", err)
	}
	return string(data), nil
}
