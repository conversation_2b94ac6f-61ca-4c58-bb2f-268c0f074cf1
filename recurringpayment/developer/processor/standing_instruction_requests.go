package processor

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/proto/json"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"

	"github.com/epifi/gamma/recurringpayment/dao"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/recurringpayment/developer"
)

type DevSIRequestEntity struct {
	siRequestDao dao.StandingInstructionRequestDao
}

func NewDevSIRequestEntity(siRequestDao dao.StandingInstructionRequestDao) *DevSIRequestEntity {
	return &DevSIRequestEntity{
		siRequestDao: siRequestDao,
	}
}

func (d *DevSIRequestEntity) FetchParamList(ctx context.Context, entity developer.RecurringPaymentEntity) (
	[]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            VendorRequestId,
			Label:           "Vendor Request Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            StandingInstructionId,
			Label:           "Standing Instruction Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:    RequestType,
			Label:   "Request Type",
			Type:    db_state.ParameterDataType_DROPDOWN,
			Options: getRequestsTypes(),
		},
	}
	return paramList, nil
}

func (d *DevSIRequestEntity) FetchData(ctx context.Context, entity developer.RecurringPaymentEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", ErrNilFilter
	}

	var (
		vendorRequestId       string
		standingInstructionId string
		requestType           siPb.RequestType
		data                  []byte
		siRequest             *siPb.StandingInstructionRequest
		err                   error
	)

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case VendorRequestId:
			vendorRequestId = filter.GetStringValue()
		case StandingInstructionId:
			standingInstructionId = filter.GetStringValue()
		case RequestType:
			reqType := filter.GetDropdownValue()
			requestType = siPb.RequestType(siPb.RequestType_value[reqType])
		default:
			return "", fmt.Errorf("unknown parameter name %s", filter.GetParameterName())
		}
	}

	switch {
	case vendorRequestId != "":
		siRequest, err = d.siRequestDao.GetByRequestId(ctx, vendorRequestId)
		if err != nil {
			logger.Error(ctx, "could not fetch standing instruction request details for given vendor request id", zap.String(
				logger.VENDOR_REUQEST, vendorRequestId), zap.Error(err))
			return "", err
		}
	case standingInstructionId != "" && requestType != siPb.RequestType_REQUEST_TYPE_UNSPECIFIED:
		siRequest, err = d.siRequestDao.GetByStandingInstructionIdAndRequestType(ctx, standingInstructionId, requestType)
		if err != nil {
			logger.Error(ctx, "could not fetch standing instruction request details for given standing instruction id and request type", zap.String(
				logger.STANDING_INSTRUCTION_ID, standingInstructionId), zap.String(logger.REQUEST_TYPE, RequestType), zap.Error(err))
			return "", err
		}
	}
	data, err = json.Marshal(siRequest)
	if err != nil {
		return "", fmt.Errorf("cannot marshal standing instruction request struct to json: %w", err)
	}
	return string(data), nil
}

func getRequestsTypes() []string {
	var requestsTypes []string
	for _, requestsType := range siPb.RequestType_name {
		if requestsType != siPb.RequestType_REQUEST_TYPE_UNSPECIFIED.String() {
			requestsTypes = append(requestsTypes, requestsType)
		}
	}
	return requestsTypes
}
