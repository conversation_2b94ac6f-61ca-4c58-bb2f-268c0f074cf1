// nolint: testifylint
package paymentgateway

import (
	"flag"
	"os"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/jonboulle/clockwork"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	mockOrder "github.com/epifi/gamma/api/order/mocks"
	mocks3 "github.com/epifi/gamma/api/pay/mocks"
	mocks6 "github.com/epifi/gamma/api/paymentinstrument/mocks"
	enachMocks "github.com/epifi/gamma/api/recurringpayment/enach/mocks"
	mocks5 "github.com/epifi/gamma/api/recurringpayment/mocks"
	mocks4 "github.com/epifi/gamma/api/user/mocks"
	mocks2 "github.com/epifi/gamma/api/vendorgateway/pg/mocks"
	"github.com/epifi/gamma/pay/config/server"
	"github.com/epifi/gamma/pkg/pay/pgauthkeys"
	rpServerConfig "github.com/epifi/gamma/recurringpayment/config/server"
	rpServerGenConf "github.com/epifi/gamma/recurringpayment/config/server/genconf"
	"github.com/epifi/gamma/recurringpayment/dao/mocks"
	mock_internal "github.com/epifi/gamma/recurringpayment/internal/mocks"
	mocks7 "github.com/epifi/gamma/recurringpayment/internal/user/mocks"
	"github.com/epifi/gamma/recurringpayment/test"
)

var (
	db          *gorm.DB
	conf        *rpServerConfig.Config
	dynamicConf *rpServerGenConf.Config
)

var seedTime = time.Date(2024, 10, 25, 16, 0, 0, 0, time.Local)

type mockDependencies struct {
	vgPgClient             *mocks2.MockPaymentGatewayClient
	enachClient            *enachMocks.MockEnachServiceClient
	rpDao                  *mocks.MockRecurringPaymentDao
	rpaDao                 *mocks.MockRecurringPaymentsActionDao
	rpvdDao                *mocks.MockRecurringPaymentsVendorDetailsDao
	orderClient            *mockOrder.MockOrderServiceClient
	payClient              *mocks3.MockPayClient
	userClient             *mocks4.MockUsersClient
	recurringPaymentClient *mocks5.MockRecurringPaymentServiceClient
	piClient               *mocks6.MockPiClient
	celestialProcessor     *mock_internal.MockCelestialProcessor
	userProcessorFactory   *mocks7.MockIUserProcessorFactory
	defaultUserProcessor   *mocks7.MockIUserProcessor
}

// nolint:dogsled
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, conf, dynamicConf, db, teardown = test.InitTestServerV2()

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func getServiceWithMocks(t *testing.T) (*Service, *mockDependencies) {
	ctr := gomock.NewController(t)
	vgPgClient := mocks2.NewMockPaymentGatewayClient(ctr)
	rpMockDao := mocks.NewMockRecurringPaymentDao(ctr)
	rpaMockDao := mocks.NewMockRecurringPaymentsActionDao(ctr)
	rpvdDao := mocks.NewMockRecurringPaymentsVendorDetailsDao(ctr)
	orderClient := mockOrder.NewMockOrderServiceClient(ctr)
	payClient := mocks3.NewMockPayClient(ctr)
	userClient := mocks4.NewMockUsersClient(ctr)
	enachClient := enachMocks.NewMockEnachServiceClient(ctr)
	recurringPaymentClient := mocks5.NewMockRecurringPaymentServiceClient(ctr)
	piClient := mocks6.NewMockPiClient(ctr)
	celestialProcessor := mock_internal.NewMockCelestialProcessor(ctr)
	userProcessorFactory := mocks7.NewMockIUserProcessorFactory(ctr)
	userProcessor := mocks7.NewMockIUserProcessor(ctr)
	pgProgToAuthSecret := map[string]*server.PgProgramToAuthSecret{}
	for k, v := range conf.PgProgramToAuthSecretMap {
		pgProgToAuthSecret[k] = &server.PgProgramToAuthSecret{
			AuthParam:      v.AuthParam,
			AuthParamValue: v.AuthParamValue,
		}
	}
	err := pgauthkeys.InitialisePgProgramToAuthParams(pgProgToAuthSecret)
	assert.NoError(t, err)
	s := NewService(conf, dynamicConf, vgPgClient, payClient, enachClient, rpMockDao, rpaMockDao, orderClient, userClient, rpvdDao, celestialProcessor, recurringPaymentClient, piClient, userProcessorFactory, clockwork.NewFakeClockAt(seedTime))
	return s, &mockDependencies{
		vgPgClient:             vgPgClient,
		enachClient:            enachClient,
		rpDao:                  rpMockDao,
		rpaDao:                 rpaMockDao,
		rpvdDao:                rpvdDao,
		orderClient:            orderClient,
		payClient:              payClient,
		userClient:             userClient,
		recurringPaymentClient: recurringPaymentClient,
		piClient:               piClient,
		celestialProcessor:     celestialProcessor,
		userProcessorFactory:   userProcessorFactory,
		defaultUserProcessor:   userProcessor,
	}
}
