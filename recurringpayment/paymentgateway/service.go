// nolint: funlen
package paymentgateway

import (
	"context"
	fmt "fmt"

	"github.com/jonboulle/clockwork"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/durationpb"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/pay/pgerrorcodes"

	"github.com/epifi/gamma/api/accounts"
	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/enums"
	"github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	rpEnumsPb "github.com/epifi/gamma/api/recurringpayment/enums"
	"github.com/epifi/gamma/api/recurringpayment/paymentgateway"
	typesPb "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/pg"
	payPkg "github.com/epifi/gamma/pkg/pay"
	"github.com/epifi/gamma/pkg/pay/pgauthkeys"
	rpConf "github.com/epifi/gamma/recurringpayment/config/server"
	rpGenconf "github.com/epifi/gamma/recurringpayment/config/server/genconf"
	"github.com/epifi/gamma/recurringpayment/dao"
	"github.com/epifi/gamma/recurringpayment/internal"
	"github.com/epifi/gamma/recurringpayment/internal/user"
)

var (
	vendorOrderStatusToRpOrderStatus = map[pg.VendorOrderStatus]rpEnumsPb.OrderStatus{
		pg.VendorOrderStatus_VENDOR_ORDER_STATUS_UNSPECIFIED: rpEnumsPb.OrderStatus_ORDER_STATUS_UNSPECIFIED,
		pg.VendorOrderStatus_VENDOR_ORDER_STATUS_CREATED:     rpEnumsPb.OrderStatus_ORDER_STATUS_IN_PROGRESS,
		pg.VendorOrderStatus_VENDOR_ORDER_STATUS_PAID:        rpEnumsPb.OrderStatus_ORDER_STATUS_PAID,
	}
	rpPaymentTypeToVgAuthMethod = map[rpPb.RecurringPaymentType]pg.AuthorisationMethod{
		rpPb.RecurringPaymentType_UPI_MANDATES:   pg.AuthorisationMethod_AUTHORISATION_METHOD_UPI,
		rpPb.RecurringPaymentType_ENACH_MANDATES: pg.AuthorisationMethod_AUTHORISATION_METHOD_EMANDATE,
	}
	vgMandateStatusToRespMandateStatus = map[pg.MandateStatus]paymentgateway.GetMandateStatusResponse_MandateStatus{
		pg.MandateStatus_MANDATE_STATUS_INITIATED: paymentgateway.GetMandateStatusResponse_MANDATE_STATUS_INITIATED,
		pg.MandateStatus_MANDATE_STATUS_CONFIRMED: paymentgateway.GetMandateStatusResponse_MANDATE_STATUS_CONFIRMED,
		pg.MandateStatus_MANDATE_STATUS_REJECTED:  paymentgateway.GetMandateStatusResponse_MANDATE_STATUS_REJECTED,
		pg.MandateStatus_MANDATE_STATUS_CANCELLED: paymentgateway.GetMandateStatusResponse_MANDATE_STATUS_CANCELLED,
		pg.MandateStatus_MANDATE_STATUS_PAUSED:    paymentgateway.GetMandateStatusResponse_MANDATE_STATUS_PAUSED,
	}
)

type Service struct {
	conf                   *rpConf.Config
	gconf                  *rpGenconf.Config
	vgPgClient             pg.PaymentGatewayClient
	enachClient            enachPb.EnachServiceClient
	payClient              pay.PayClient
	rpDao                  dao.RecurringPaymentDao
	rpaDao                 dao.RecurringPaymentsActionDao
	orderClient            orderPb.OrderServiceClient
	userClient             userPb.UsersClient
	rpVdDao                dao.RecurringPaymentsVendorDetailsDao
	celestialProcessor     internal.CelestialProcessor
	recurringPaymentClient rpPb.RecurringPaymentServiceClient
	piClient               piPb.PiClient
	userProcessorFactory   user.IUserProcessorFactory
	clock                  clockwork.Clock
}

func NewService(
	conf *rpConf.Config,
	gconf *rpGenconf.Config,
	vgPgClient pg.PaymentGatewayClient,
	payClient pay.PayClient,
	enachClient enachPb.EnachServiceClient,
	rpDao dao.RecurringPaymentDao,
	rpaDao dao.RecurringPaymentsActionDao,
	orderClient orderPb.OrderServiceClient,
	userClient userPb.UsersClient,
	rpVdDao dao.RecurringPaymentsVendorDetailsDao,
	celestialProcessor internal.CelestialProcessor,
	recurringPaymentClient rpPb.RecurringPaymentServiceClient,
	piClient piPb.PiClient,
	userProcessorFactory user.IUserProcessorFactory,
	clock clockwork.Clock) *Service {
	return &Service{
		conf:                   conf,
		gconf:                  gconf,
		vgPgClient:             vgPgClient,
		payClient:              payClient,
		enachClient:            enachClient,
		rpDao:                  rpDao,
		rpaDao:                 rpaDao,
		orderClient:            orderClient,
		userClient:             userClient,
		rpVdDao:                rpVdDao,
		celestialProcessor:     celestialProcessor,
		recurringPaymentClient: recurringPaymentClient,
		piClient:               piClient,
		userProcessorFactory:   userProcessorFactory,
		clock:                  clock,
	}
}

func (s *Service) CreateOrder(ctx context.Context, req *paymentgateway.CreateOrderRequest) (*paymentgateway.CreateOrderResponse, error) {
	var (
		res = &paymentgateway.CreateOrderResponse{}
		err error
	)
	recurringPayment, err := s.rpDao.GetById(ctx, req.GetRecurringPaymentId())
	if err != nil {
		logger.Error(ctx,
			"error in fetching recurring payment",
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()),
			zap.Error(err))
		return &paymentgateway.CreateOrderResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	ctxWithOwnership := epificontext.WithOwnership(ctx, recurringPayment.GetEntityOwnership())
	switch req.GetRecurringPaymentStage() {
	case rpEnumsPb.RecurringPaymentStage_RECURRING_PAYMENT_STAGE_EXECUTION:
		res, err = s.createOrderForExecution(ctxWithOwnership, req, recurringPayment)
	case rpEnumsPb.RecurringPaymentStage_RECURRING_PAYMENT_STAGE_REGISTRATION:
		res, err = s.createOrderForRegistration(ctxWithOwnership, req, recurringPayment)
	default:
		logger.Error(ctx, "invalid stage of recurring payment",
			zap.String(logger.STAGE, req.GetRecurringPaymentStage().String()),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()),
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	if err != nil {
		logger.Error(ctx, "error in creating order",
			zap.String(logger.STAGE, req.GetRecurringPaymentStage().String()),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()),
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()),
			zap.Error(err))
		return &paymentgateway.CreateOrderResponse{Status: rpc.StatusInternal()}, nil
	}
	return res, nil
}

func (s *Service) createOrderForRegistration(ctx context.Context, req *paymentgateway.CreateOrderRequest, rp *rpPb.RecurringPayment) (*paymentgateway.CreateOrderResponse, error) {
	var (
		res            = &paymentgateway.CreateOrderResponse{}
		externalVendor commonvgpb.Vendor
	)
	// Check for existence of recurring payment action in non-terminal state so that the existing order can be
	// authorised, when the user re-tries the flow. Or else, we fail the flow so that the user can restart the
	// journey with a different recurring payment ID.
	rpa, rpaErr := s.rpaDao.GetByClientRequestId(ctx, req.GetClientRequestId(), false)
	if rpaErr != nil {
		return nil, fmt.Errorf("error in fetching recurring payments action by client request id : %s : %w", req.GetClientRequestId(), rpaErr)
	}
	if rpa.GetState().IsTerminal() || rpa.GetState() == rpPb.ActionState_ACTION_MANUAL_INTERVENTION {
		logger.Error(ctx,
			"recurring payments action for given client request id is in terminal state",
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()),
			zap.String(logger.RECURRING_PAYMENT_ID, rp.GetId()),
			zap.String(logger.RECURRING_PAYMENT_ACTION_ID, rpa.GetId()),
			zap.String(logger.STATE, rpa.GetState().String()),
		)
		return nil, fmt.Errorf("recurring payments action for given client request id is in terminal state")
	}

	rpvd, err := s.rpVdDao.GetByRecurringPaymentId(ctx, req.GetRecurringPaymentId())
	if err != nil {
		return nil, fmt.Errorf("error in fetching recurring payment vendor details for rp id : %s : %w", req.GetRecurringPaymentId(), err)
	}
	externalVendor = rpvd.GetVendor()

	authParams, err := pgauthkeys.GetAuthParamsForPgProgram(&typesPb.PaymentGatewayProgram{
		PgVendor:        externalVendor,
		PiId:            rp.GetPiTo(),
		EntityOwnership: rp.GetEntityOwnership(),
	})
	if err != nil {
		return nil, fmt.Errorf("error in fetching authentication params for the pg program : %w", err)
	}

	userProcessor := s.userProcessorFactory.GetUserProcessor(ctx, rp.GetFromActorId(), rp.GetEntityOwnership())

	userDetails, err := userProcessor.GetUserFromActorId(ctx, rp.GetFromActorId(), rp.GetEntityOwnership())
	if err != nil {
		return nil, fmt.Errorf("error in fetching user details : %w", err)
	}

	customerRes, err := s.vgPgClient.CreateCustomer(ctx, &pg.CreateCustomerRequest{
		Header:               &commonvgpb.RequestHeader{Vendor: externalVendor},
		Name:                 userDetails.Name,
		Contact:              userDetails.PhoneNumber,
		Email:                userDetails.Email,
		AuthenticationParams: authParams,
	})
	if te := epifigrpc.RPCError(customerRes, err); te != nil {
		logger.Error(ctx, "error in creating customer on vendor's end", zap.Error(te), zap.String(logger.ACTOR_ID_V2, rp.GetFromActorId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	createVendorOrderRes, err := s.checkAndCreateVendorOrderForRegistration(ctx, &checkAndCreateVendorOrderForRegistrationRequest{
		createOrderReq:   req,
		recurringPayment: rp,
		externalVendor:   externalVendor,
		customerRes:      customerRes,
		authParams:       authParams,
	})
	if err != nil {
		logger.Error(ctx,
			"error in creating vendor order for registration",
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()),
			zap.String(logger.RECURRING_PAYMENT_ID, rp.GetId()),
			zap.Error(err),
		)
		return nil, fmt.Errorf("error in checking and creating vendor order : %w", err)
	}

	rpvd.VendorCustomerId = customerRes.GetVendorCustomerId()
	updErr := s.rpVdDao.Update(ctx, rpvd, []rpPb.RecurringPaymentVendorDetailsFieldMask{
		rpPb.RecurringPaymentVendorDetailsFieldMask_RECURRING_PAYMENT_VENDOR_DETAILS_FIELD_MASK_VENDOR_CUSTOMER_ID,
	})
	if updErr != nil {
		return nil, fmt.Errorf("error in updating RecurringPaymentVendorDetails")
	}

	res.VendorCustomerId = customerRes.GetVendorCustomerId()
	res.InternalOrderId = rp.GetId()
	res.VendorOrderId = createVendorOrderRes.vendorOrderId
	res.Status = rpc.StatusOk()
	authKey, authKeyErr := pgauthkeys.GetAuthKeyForAuthParams(authParams)
	if authKeyErr != nil {
		return nil, fmt.Errorf("error in fetching auth key for auth params : %w", authKeyErr)
	}
	res.VendorAuthKey = authKey
	return res, nil
}

type checkAndCreateVendorOrderForRegistrationRequest struct {
	createOrderReq   *paymentgateway.CreateOrderRequest
	recurringPayment *rpPb.RecurringPayment
	externalVendor   commonvgpb.Vendor
	customerRes      *pg.CreateCustomerResponse
	authParams       *pg.AuthenticationParams
}

type checkAndCreateVendorOrderForRegistrationResponse struct {
	vendorOrderId string
}

// checkAndCreateVendorOrderForRegistration checks for existing ovom entry for the given recurring payment ID so that we only make
// vendor API calls when necessary. If the ovom entry exists, then it returns the existing entry. Or else it creates a
// new order at vendor side and creates a corresponding entry in OVOM.
func (s *Service) checkAndCreateVendorOrderForRegistration(ctx context.Context, req *checkAndCreateVendorOrderForRegistrationRequest) (*checkAndCreateVendorOrderForRegistrationResponse, error) {
	res := &checkAndCreateVendorOrderForRegistrationResponse{}

	getOvomRes, getOvomErr := s.payClient.GetOrderVendorOrderMap(ctx, &pay.GetOrderVendorOrderMapRequest{
		OrderId: req.recurringPayment.GetId(),
		Vendor:  req.externalVendor,
	})
	ovomTe := epifigrpc.RPCError(getOvomRes, getOvomErr)
	switch {
	case getOvomRes.GetStatus().IsRecordNotFound():
		// no existing record found for OVOM, so creating a new order at vendor side and adding it in ovom.
		// no pay side orders are going to be created for registration stage. Hence, direcly creating order on
		// vendor's end.
		vgOrderRequest := &pg.CreateOrderRequest{
			Header:      &commonvgpb.RequestHeader{Vendor: req.externalVendor},
			Amount:      typesPb.GetFromBeMoney(req.createOrderReq.GetOrderAmount()),
			ReferenceId: req.recurringPayment.GetExternalId(),
			RecurringPaymentParameters: &pg.RecurringPaymentParameters{
				PaymentCapture:      true,
				CustomerId:          req.customerRes.GetVendorCustomerId(),
				AuthorisationMethod: rpPaymentTypeToVgAuthMethod[req.recurringPayment.GetType()],
				Token: &pg.Token{
					MaxAmount: typesPb.GetFromBeMoney(req.recurringPayment.GetAmount()),
					ExpireAt:  req.recurringPayment.GetInterval().GetEndTime(),
				},
			},
			AuthenticationParams: req.authParams,
		}
		if req.recurringPayment.GetPiFrom() != "" {
			vgOrderRequest.RequireAccountValidationForPayment = true
			piRes, piErr := s.piClient.GetPiById(ctx, &piPb.GetPiByIdRequest{
				Id: req.recurringPayment.GetPiFrom(),
			})
			if te := epifigrpc.RPCError(piRes, piErr); te != nil {
				return nil, fmt.Errorf("error in fetching pi for id : %s : %w", req.recurringPayment.GetPiFrom(), te)
			}
			vgOrderRequest.BankAccount = &pg.AccountDetails{
				AccountNumber:     piRes.GetPaymentInstrument().GetAccount().GetActualAccountNumber(),
				IfscCode:          piRes.GetPaymentInstrument().GetAccount().GetIfscCode(),
				AccountHolderName: piRes.GetPaymentInstrument().GetAccount().GetName(),
				AccountType:       piRes.GetPaymentInstrument().GetAccount().GetAccountType().String(),
			}
		}
		orderRes, err := s.vgPgClient.CreateOrder(ctx, vgOrderRequest)
		if te := epifigrpc.RPCError(orderRes, err); te != nil {
			return nil, fmt.Errorf("error in creating order on vendor's end : %w", te)
		}
		// Since no internal order is created, the internal order id in OVOM will be recurring payment id
		// This call to CreateOrderVendorOrderMap is idempotent wrt OrderId parameter, and returns an existing
		// ovom entry if it already exists for the given OrderId.
		ovomRes, ovomErr := s.payClient.CreateOrderVendorOrderMap(ctx, &pay.CreateOrderVendorOrderMapRequest{
			OrderId:           req.recurringPayment.GetId(),
			VendorOrderId:     orderRes.GetVendorOrderId(),
			Vendor:            req.externalVendor,
			EntityOwnership:   req.recurringPayment.GetEntityOwnership(),
			DomainReferenceId: req.createOrderReq.GetClientRequestId(),
		})
		if te := epifigrpc.RPCError(ovomRes, ovomErr); te != nil {
			return nil, fmt.Errorf("error in creating ovom : %w", te)
		}
		res.vendorOrderId = orderRes.GetVendorOrderId()
	case ovomTe != nil:
		return nil, fmt.Errorf("error in checking for existence of OVOM entry : %w", ovomTe)
	default:
		res.vendorOrderId = getOvomRes.GetVendorOrderMaps()[0].GetVendorOrderId()
	}
	return res, nil
}

func (s *Service) fetchSuccessfulVendorPaymentForOrder(ctx context.Context, vendorOrderId string, externalVendor commonvgpb.Vendor, authParams *pg.AuthenticationParams) (*pg.Payment, error) {
	paymentRes, err := s.vgPgClient.FetchPaymentsForOrderId(ctx, &pg.FetchPaymentsForOrderIdRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: externalVendor,
		},
		VendorOrderId:        vendorOrderId,
		AuthenticationParams: authParams,
	})
	if te := epifigrpc.RPCError(paymentRes, err); te != nil {
		return nil, fmt.Errorf("error in fetching payments for this order id : %s : %w", vendorOrderId, te)
	}
	for _, pmnt := range paymentRes.GetPayments() {
		if pmnt.GetStatus() == pg.VendorPaymentStatus_VENDOR_PAYMENT_STATUS_CAPTURED {
			return pmnt, nil
		}
	}
	return nil, fmt.Errorf("no successful payments for the given vendor order id : %s", vendorOrderId)
}

// createPi creates the payment instrument for the given recurring payment based on the vendor payments. This has to be done
// as a part of order status fetch since the user is moved to the PG vendor's stage and we dont have visiibility of the bank account/vpa, etc
// of the user. The PI will be created in a tokenized form. For every payment method, the parameters used for creating hash will differ.
func (s *Service) createPi(ctx context.Context, rp *rpPb.RecurringPayment, vendorPayment *pg.Payment) (string, error) {
	var (
		createPiRequest *piPb.CreatePiRequest
		hashedAccNumber string
	)
	ownership := epificontext.OwnershipFromContext(ctx)
	userProcessor := s.userProcessorFactory.GetUserProcessor(ctx, rp.GetFromActorId(), rp.GetEntityOwnership())
	userDetails, err := userProcessor.GetUserFromActorId(ctx, rp.GetFromActorId(), rp.GetEntityOwnership())
	if err != nil {
		return "", fmt.Errorf("error in fetching user details : %w", err)
	}
	actorName := userDetails.Name.ToFirstNameLastNameString()
	switch vendorPayment.GetMethod() {
	case pg.PaymentMethod_PAYMENT_METHOD_CARD:
		// in case of card, the parameter to be hashed is the card id on vendor's end. We are hashing this so that
		// its not present in the raw format and can be stored without compliance consequences
		hashedAccNumber, err = constructUniqueHashedAccountNumber(vendorPayment.GetCardPaymentDetails().GetCardId())
		if err != nil {
			return "", fmt.Errorf("unable to construct hashed account number for card method: %w", err)
		}
		// as we are creating a new pi here, and we are not sure whether the actorName is actually verified or not
		// so we are not setting verified name here
		createPiRequest = &piPb.CreatePiRequest{
			Type: piPb.PaymentInstrumentType_GENERIC,
			Identifier: &piPb.CreatePiRequest_Account{Account: &piPb.Account{
				ActualAccountNumber: hashedAccNumber,
				SecureAccountNumber: mask.MaskLastNDigits(hashedAccNumber, 3, ""),
				IfscCode:            payPkg.DefaultIfscCardTxn,
				AccountType:         accounts.Type_SAVINGS,
				Name:                actorName,
			}},
			IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
			Capabilities: map[string]bool{
				piPb.Capability_INBOUND_TXN.String():  true,
				piPb.Capability_OUTBOUND_TXN.String(): true,
			},
			Ownership: payPkg.CommonOwnershipToPiOwnership[ownership],
			State:     piPb.PaymentInstrumentState_CREATED,
		}
	case pg.PaymentMethod_PAYMENT_METHOD_NETBANKING, pg.PaymentMethod_PAYMENT_METHOD_EMANDATE:
		// in case of netbanking, the parameter to be hashed is the payer's email and phone number. This is done so that
		// the PI can be created in a tokenized form. The hashed account number will be unique for every user and hence
		// todo:(Rahul/Vineet) hashedAccNumber could be non-unique if the email/ph details are missing from vendor
		hashedAccNumber, err = constructUniqueHashedAccountNumber(vendorPayment.GetPayerContactInfo().GetEmail(),
			vendorPayment.GetPayerContactInfo().GetNumber())
		if err != nil {
			return "", fmt.Errorf("unable to construct hashed account number for netbanking: %w", err)
		}
		// as we are creating a new pi here, and we are not sure whether the actorName is actually verified or not
		// so we are not setting verified name here
		createPiRequest = &piPb.CreatePiRequest{
			Type: piPb.PaymentInstrumentType_GENERIC,
			Identifier: &piPb.CreatePiRequest_Account{Account: &piPb.Account{
				ActualAccountNumber: hashedAccNumber,
				SecureAccountNumber: mask.MaskLastNDigits(hashedAccNumber, 3, ""),
				IfscCode:            payPkg.DefaultIfscCardTxn,
				AccountType:         accounts.Type_SAVINGS,
				Name:                actorName,
			}},
			IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
			Capabilities: map[string]bool{
				piPb.Capability_INBOUND_TXN.String():  true,
				piPb.Capability_OUTBOUND_TXN.String(): true,
			},
			Ownership: payPkg.CommonOwnershipToPiOwnership[ownership],
			State:     piPb.PaymentInstrumentState_CREATED,
		}
	case pg.PaymentMethod_PAYMENT_METHOD_UPI:
		// in case of upi, the parameter to be hashed is the vpa of the user. This is done so that the PI can be created
		// in a tokenized form. The hashed account number will be unique for every user and hence
		hashedAccNumber, err = constructUniqueHashedAccountNumber(vendorPayment.GetUpiPaymentDetails().GetVpa())
		if err != nil {
			return "", fmt.Errorf("unable to construct hashed account number for upi: %w", err)
		}
		// as we are creating a new pi here, and we are not sure whether the actorName is actually verified or not
		// we should ideally not set verified name here, but while creating new pi for upi we need verified name as a mandatory parameter hence sending it as actorName
		createPiRequest = &piPb.CreatePiRequest{
			Type: piPb.PaymentInstrumentType_UPI,
			Identifier: &piPb.CreatePiRequest_Upi{Upi: &piPb.Upi{
				Vpa:                    vendorPayment.GetUpiPaymentDetails().GetVpa(),
				AccountReferenceNumber: hashedAccNumber,
				MaskedAccountNumber:    mask.MaskLastNDigits(hashedAccNumber, 3, ""),
				IfscCode:               payPkg.DefaultIfscCardTxn,
				AccountType:            accounts.Type_SAVINGS,
				MerchantDetails:        nil,
				Name:                   actorName,
				VpaType:                piPb.Upi_VPA_TYPE_UNSPECIFIED,
			}},
			VerifiedName:         actorName,
			IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
			Capabilities: map[string]bool{
				piPb.Capability_INBOUND_TXN.String():  true,
				piPb.Capability_OUTBOUND_TXN.String(): true,
			},
			Ownership: payPkg.CommonOwnershipToPiOwnership[ownership],
			State:     piPb.PaymentInstrumentState_CREATED,
		}
	default:
		return "", fmt.Errorf("unsupported payment method: %v", vendorPayment.GetMethod().String())
	}
	piResp, piErr := s.piClient.CreatePi(ctx, createPiRequest)
	if piErr = epifigrpc.RPCError(piResp, piErr); piErr != nil {
		return "", fmt.Errorf("error creating pi for method %v: %w", vendorPayment.GetMethod(), piErr)
	}
	return piResp.GetPaymentInstrument().GetId(), nil
}

// TODO(Sundeep): revisit the idempotency & error handling of this function so that the caller knows when it is safe to
// retry this function, so that funds are not deducted twice in case of failures.
func (s *Service) createOrderForExecution(ctx context.Context, req *paymentgateway.CreateOrderRequest, recurringPayment *rpPb.RecurringPayment) (*paymentgateway.CreateOrderResponse, error) {
	var (
		res                    = &paymentgateway.CreateOrderResponse{}
		customerId             string
		authorisationPaymentId string
	)
	orderAmountFloat, _ := moneyPkg.ToDecimal(req.GetOrderAmount()).Float64()
	recurringPaymentTypeStr := recurringPayment.GetType().String()
	executionLimit, ok := s.conf.PgParams.RecurringPaymentTypeToExecutionLimit[recurringPaymentTypeStr]
	if ok {
		if orderAmountFloat > executionLimit {
			return nil, epifierrors.ErrInvalidArgument
		}
	}
	// Fetch vendor details associated with the recurring payment.
	rpVd, err := s.rpVdDao.GetByRecurringPaymentId(ctx, req.GetRecurringPaymentId())
	if err != nil {
		return nil, fmt.Errorf("error in fetching recurring payments vendor details : %w", err)
	}

	customerId = rpVd.GetVendorCustomerId()
	authorisationPaymentId = rpVd.GetVendorPaymentId()

	autoCaptureTimeoutOverride := s.gconf.PgParams().RpExecutionPaymentAutoCaptureTimeout().Get(recurringPaymentTypeStr)

	// Create an internal order with the payment client.
	orderRes, err := s.payClient.CreateOrder(ctx, &pay.CreateOrderRequest{
		Order: &orderPb.Order{
			FromActorId: recurringPayment.GetFromActorId(),
			ToActorId:   recurringPayment.GetToActorId(),
			Status:      orderPb.OrderStatus_CREATED,
			Provenance:  orderPb.OrderProvenance_INTERNAL,
			Amount:      req.GetOrderAmount(),
			ClientReqId: req.GetClientRequestId(),
		},
		Vendor: req.GetVendor(),
		DomainOrderData: &pay.DomainOrderData{
			Amount:                req.GetOrderAmount(),
			ClientRequestIdExpiry: nil,
			ToPi:                  recurringPayment.GetPiTo(),
			ToActor:               recurringPayment.GetToActorId(),
		},
		Ownership: recurringPayment.GetEntityOwnership(),
		RecurringPaymentDetails: &pay.RecurringPaymentDetails{
			RecurringPaymentId: recurringPayment.GetId(),
			VendorCustomerId:   rpVd.GetVendorCustomerId(),
		},
		AutoCaptureTimeoutOverride: durationpb.New(autoCaptureTimeoutOverride),
	})
	if te := epifigrpc.RPCError(orderRes, err); te != nil {
		return nil, fmt.Errorf("error in creating order : %w", te)
	}
	// once the internal order has been created, we need to create order and payment
	// on the vendor's end.
	authParams, err := pgauthkeys.GetAuthParamsForPgProgram(&typesPb.PaymentGatewayProgram{
		PgVendor:        req.GetVendor(),
		PiId:            recurringPayment.GetPiTo(),
		EntityOwnership: recurringPayment.GetEntityOwnership(),
	})
	if err != nil {
		return nil, fmt.Errorf("error in fetching auth params for pg program : %w", err)
	}

	// Fetch a token for the payment using the vendor's API
	tokenRes, err := s.vgPgClient.FetchToken(ctx, &pg.FetchTokenRequest{
		Header:               &commonvgpb.RequestHeader{Vendor: req.GetVendor()},
		PaymentId:            authorisationPaymentId,
		AuthenticationParams: authParams,
	})
	if te := epifigrpc.RPCError(tokenRes, err); te != nil {
		return nil, fmt.Errorf("error in fetching token for payment id : %s  : %w", authorisationPaymentId, te)
	}
	userProcessor := s.userProcessorFactory.GetUserProcessor(ctx, recurringPayment.GetFromActorId(), recurringPayment.GetEntityOwnership())

	userDetails, err := userProcessor.GetUserFromActorId(ctx, recurringPayment.GetFromActorId(), recurringPayment.GetEntityOwnership())
	if err != nil {
		return nil, fmt.Errorf("error in fetching user details : %w", err)
	}
	paymentRes, err := s.vgPgClient.CreateSubsequentPayment(ctx, &pg.CreateSubsequentPaymentRequest{
		Header:               &commonvgpb.RequestHeader{Vendor: req.GetVendor()},
		Email:                userDetails.Email,
		Contact:              userDetails.PhoneNumber,
		Amount:               typesPb.GetFromBeMoney(req.GetOrderAmount()),
		OrderId:              orderRes.GetVendorOrderId(),
		CustomerId:           customerId,
		TokenId:              tokenRes.GetTokenId(),
		AuthenticationParams: authParams,
	})
	if te := epifigrpc.RPCError(paymentRes, err); te != nil {
		// populate detailed status here so that it can be propagated to the caller.
		res.Status = rpc.StatusInternal()
		razorpayStatusMapping, statusMpErr := pgerrorcodes.GetErrorMappingFromPgErrorReason(commonvgpb.Vendor_RAZORPAY, pgerrorcodes.PgStagePayment, &pgerrorcodes.PgErrorReason{
			ErrorReason: paymentRes.GetVendorStatus().GetErrorReason(),
			ErrorCode:   paymentRes.GetVendorStatus().GetCode(),
		})
		if statusMpErr != nil {
			logger.Error(ctx, "error in getting razorpay status mapping",
				zap.String(logger.VENDOR_ORDER_ID, orderRes.GetVendorOrderId()),
				zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()),
				zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()),
				zap.Error(statusMpErr))
			return nil, fmt.Errorf("error getting razorpay status mapping: %w", statusMpErr)
		}
		res.DetailedStatus = &rpPb.ActionDetailedStatusInfo{
			FiStatusCode:     razorpayStatusMapping.GetFiStatusCode(),
			ErrorDescription: razorpayStatusMapping.GetFiStatusDescription(),
		}
		logger.Error(ctx, "error in creating subsequent payment for order",
			zap.String(logger.VENDOR_ORDER_ID, orderRes.GetVendorOrderId()),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()),
			zap.Error(te))
		return res, nil
	}
	res.Status = rpc.StatusOk()
	res.InternalOrderId = orderRes.GetOrder().GetId()
	res.VendorOrderId = orderRes.GetVendorOrderId()
	return res, nil
}

func (s *Service) GetOrderStatus(ctx context.Context, req *paymentgateway.GetOrderStatusRequest) (*paymentgateway.GetOrderStatusResponse, error) {
	var (
		res = &paymentgateway.GetOrderStatusResponse{}
		err error
	)
	recurringPayment, rpErr := s.rpDao.GetById(ctx, req.GetRecurringPaymentId())
	if rpErr != nil {
		logger.Error(ctx, "error in fetching recurring payment",
			zap.Error(err),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	ctxWithOwnership := epificontext.WithOwnership(ctx, recurringPayment.GetEntityOwnership())
	recurringPaymentAction, err := s.rpaDao.GetByClientRequestId(ctx, req.GetClientRequestId(), false)
	if err != nil {
		logger.Error(ctx, "error in fetching recurring payment action for given client request id",
			zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()),
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	var internalOrder *orderPb.Order
	res, internalOrder = s.getOrderAndStatusFromInternalDb(ctxWithOwnership, req, recurringPaymentAction, recurringPayment)
	switch {
	case req.GetDataFreshness() == paymentgateway.GetOrderStatusRequest_REAL_TIME &&
		(err != nil || !res.GetOrderStatus().IsTerminal()):

		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error in fetching order status. falling back to fetching status from vendor API",
				zap.Error(err),
				zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()),
				zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		}

		// since caller is explicitly asking for real-time status, and the status is not in
		// terminal state in internal status or there is error in fetching internal status,
		// we make a call to the vendor API.
		res, err = s.getVendorOrderStatus(ctxWithOwnership, recurringPaymentAction, recurringPayment, internalOrder)
		switch {
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			logger.Error(ctx, "no order found for the given recurring payment id",
				zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()),
				zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
			res = &paymentgateway.GetOrderStatusResponse{Status: rpc.StatusRecordNotFound()}
			return res, nil

		case err != nil:
			logger.Error(ctx, "error in fetching vendor order status",
				zap.Error(err),
				zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()),
				zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
			res = &paymentgateway.GetOrderStatusResponse{Status: rpc.StatusInternal()}
			return res, nil
		}
		res.Status = rpc.StatusOk()
		return res, nil

	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "no record found for the specified input in internal DB, and vendor data not requested",
			zap.Error(err),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res = &paymentgateway.GetOrderStatusResponse{Status: rpc.StatusRecordNotFound()}
		return res, nil

	case err != nil:
		logger.Error(ctx, "error in fetching order status",
			zap.Error(err),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res = &paymentgateway.GetOrderStatusResponse{Status: rpc.StatusInternal()}
		return res, nil

	default:
		res.Status = rpc.StatusOk()
		return res, nil
	}
}

func (s *Service) getVendorOrderStatus(ctx context.Context, recurringPaymentAction *rpPb.RecurringPaymentsAction, recurringPayment *rpPb.RecurringPayment, internalOrder *orderPb.Order) (*paymentgateway.GetOrderStatusResponse, error) {
	var (
		res           = &paymentgateway.GetOrderStatusResponse{}
		vendorOrderId string
		pgVendor      commonvgpb.Vendor
	)

	rpVd, err := s.rpVdDao.GetByRecurringPaymentId(ctx, recurringPayment.GetId())
	if err != nil {
		return nil, fmt.Errorf("error in fetching recurring payment vendor details : %w", err)
	}

	pgVendor = rpVd.GetVendor()

	authParams, err := pgauthkeys.GetAuthParamsForPgProgram(&typesPb.PaymentGatewayProgram{
		PgVendor:        pgVendor,
		PiId:            recurringPayment.GetPiTo(),
		EntityOwnership: recurringPayment.GetEntityOwnership(),
	})
	if err != nil {
		return nil, fmt.Errorf("error in fetching authentication params for the pg program : %w", err)
	}

	ovom, ovomErr := s.checkAndGetOvomForRecurringPaymentAction(ctx, rpVd, recurringPaymentAction, recurringPayment, internalOrder)
	if ovomErr != nil {
		logger.Error(
			ctx,
			"error in fetching ovom for recurring payment",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()),
			zap.String(logger.CLIENT_REQUEST_ID, recurringPaymentAction.GetClientRequestId()),
			zap.String(logger.VENDOR, rpVd.GetVendor().String()),
			zap.String(logger.RECURRING_PAYMENT_ACTION_ID, recurringPaymentAction.GetId()),
			zap.Error(ovomErr))
		if errors.Is(ovomErr, epifierrors.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("error in fetching ovom: %w", ovomErr)
	}
	vendorOrderId = ovom.GetVendorOrderId()

	orderRes, err := s.vgPgClient.FetchOrderStatus(ctx, &pg.FetchOrderStatusRequest{
		Header:               &commonvgpb.RequestHeader{Vendor: pgVendor},
		VendorOrderId:        vendorOrderId,
		AuthenticationParams: authParams,
	})
	if te := epifigrpc.RPCError(orderRes, err); te != nil {
		return nil, fmt.Errorf("error in fetching order status : %w", te)
	}

	if orderRes.GetOrderStatus() == pg.VendorOrderStatus_VENDOR_ORDER_STATUS_CREATED {
		// Check if order creation duration is within the expiration limit. This check is needed to ensure that we fail
		// the order after it has expired, even if the vendor does not update the order status as failed at their end.
		if expirationDuration, ok := s.conf.PgParams.VendorOrderExpirationDuration[pgVendor.String()]; ok {
			if orderRes.GetCreatedAt().AsTime().Add(expirationDuration).Before(s.clock.Now()) {
				res.Status = rpc.StatusOk()
				res.OrderStatus = rpEnumsPb.OrderStatus_ORDER_STATUS_FAILED
				return res, nil
			}
		} else {
			// TODO(Sundeep): Remove this log once all testing is done.
			logger.Info(ctx, "No order expiration duration configured for payment gateway vendor. Skip checks for order expiration", zap.String(logger.VENDOR, pgVendor.String()))
		}
	}

	if orderRes.GetOrderStatus() != pg.VendorOrderStatus_VENDOR_ORDER_STATUS_PAID {
		res.Status = rpc.StatusOk()
		res.OrderStatus = vendorOrderStatusToRpOrderStatus[orderRes.GetOrderStatus()]
		return res, nil
	}
	vendorPayment, vendorPaymentIdErr := s.fetchSuccessfulVendorPaymentForOrder(ctx, vendorOrderId, pgVendor, authParams)
	if vendorPaymentIdErr != nil {
		return nil, fmt.Errorf("error in fetching vendor payment for order : %w", vendorPaymentIdErr)
	}

	detailedStatusInfo, updErr := s.updateEntitiesStateForAuthorisationPayment(ctx, recurringPayment, recurringPaymentAction, rpVd, vendorPayment, authParams)
	if updErr != nil {
		logger.Error(
			ctx,
			"error in updating entity state during authorisation payment",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()),
			zap.String(logger.CLIENT_REQUEST_ID, recurringPaymentAction.GetClientRequestId()),
			zap.String(logger.VENDOR, rpVd.GetVendor().String()),
			zap.String(logger.RECURRING_PAYMENT_ACTION_ID, recurringPaymentAction.GetId()),
			zap.Error(updErr))
		return nil, updErr
	}

	res.Status = rpc.StatusOk()
	res.DetailedStatus = detailedStatusInfo
	res.OrderStatus = rpEnumsPb.OrderStatus_ORDER_STATUS_PAID
	return res, nil
}

// updateEntitiesStateForAuthorisationPayment updates the state of the recurring payment vendor details entity with the
// vendor payment id, creates the PIs, and associates the PI with the recurring payment when invoked for a recurring
// payment authorisation flow.
func (s *Service) updateEntitiesStateForAuthorisationPayment(ctx context.Context,
	recurringPayment *rpPb.RecurringPayment,
	recurringPaymentAction *rpPb.RecurringPaymentsAction,
	rpVd *rpPb.RecurringPaymentsVendorDetails,
	vendorPayment *pg.Payment, authParams *pg.AuthenticationParams) (*rpPb.ActionDetailedStatusInfo, error) {
	if recurringPaymentAction.GetAction() != rpPb.Action_CREATE {
		return nil, nil
	}

	// If the vendor payment id of the rpvd is found, then it means that a successful payment is being made against an
	// already successfully activated recurring payment. This case is an invalid scenario and is not expected to happen.
	// Adding a log line to ensure that if someone ends up calling this function by mistake, we can determine such cases.
	if rpVd.GetVendorPaymentId() != "" {
		logger.Info(ctx,
			"recurring payment vendor details already contains non-empty payment ID. Updating with latest received payment id",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()),
			zap.String(logger.CLIENT_REQUEST_ID, recurringPaymentAction.GetClientRequestId()),
			zap.String(logger.VENDOR, rpVd.GetVendor().String()),
			zap.String(logger.RECURRING_PAYMENT_ACTION_ID, recurringPaymentAction.GetId()),
		)
	}
	// updating vendor payment id for authorisation payment
	rpVd.VendorPaymentId = vendorPayment.GetVendorPaymentId()
	if updErr := s.rpVdDao.Update(ctx, rpVd, []rpPb.RecurringPaymentVendorDetailsFieldMask{
		rpPb.RecurringPaymentVendorDetailsFieldMask_RECURRING_PAYMENT_VENDOR_DETAILS_FIELD_MASK_VENDOR_PAYMENT_ID,
	}); updErr != nil {
		return nil, fmt.Errorf("error in updating payment id to RecurringPaymentVendorDetails : %w", updErr)
	}

	// creating PI for recurring payment authorisation since no internal order/txn has been created leading to
	// no pi created. If pi from already exists, it would mean that the PI has already been created and does
	// not have to be created again
	piIdRes, piErr := s.createPi(ctx, recurringPayment, vendorPayment)
	if piErr != nil {
		return nil, fmt.Errorf("error in creating PI for the successful payment : %w", piErr)
	}
	recurringPayment.PiFrom = piIdRes
	if rpUpdErr := s.rpDao.UpdateAndChangeStatus(ctx, recurringPayment, []rpPb.RecurringPaymentFieldMask{
		rpPb.RecurringPaymentFieldMask_PI_FROM,
	}, recurringPayment.GetState(), recurringPayment.GetState()); rpUpdErr != nil {
		return nil, fmt.Errorf("error in updating PI FROM in recurring payment : %w", rpUpdErr)
	}

	pgVendor := rpVd.GetVendor()
	fetchTokenRes, fetchTokenErr := s.vgPgClient.FetchToken(ctx, &pg.FetchTokenRequest{
		Header:               &commonvgpb.RequestHeader{Vendor: pgVendor},
		PaymentId:            rpVd.GetVendorPaymentId(),
		AuthenticationParams: authParams,
	})
	if te := epifigrpc.RPCError(fetchTokenRes, fetchTokenErr); te != nil {
		return nil, fmt.Errorf("error in fetching token status for payment id : %s : %w", rpVd.GetVendorPaymentId(), te)
	}
	detailedStatusInfo, detailedStatusInfoErr := s.getActionDetailedStatusInfoFromMandateStatus(pgVendor, fetchTokenRes, vendorPayment.GetStatus())
	if detailedStatusInfoErr != nil {
		logger.Error(ctx,
			"error in fetching action detailed status info mapping",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()),
			zap.String(logger.CLIENT_REQUEST_ID, recurringPaymentAction.GetClientRequestId()),
			zap.String(logger.VENDOR, rpVd.GetVendor().String()),
			zap.String(logger.RECURRING_PAYMENT_ACTION_ID, recurringPaymentAction.GetId()),
			zap.String(logger.MANDATE_STATUS, fetchTokenRes.GetTokenDetails().GetRecurringDetails().GetStatus().String()),
			zap.Error(detailedStatusInfoErr))
		// We return generic failure here to gracefully handle the error and mark the overall mandate status as failed
		// on our side (even though the actual state is not known), so that the registration can be re-tried.
		return &rpPb.ActionDetailedStatusInfo{
			FiStatusCode:     pgerrorcodes.PaymentGatewayGenericFailureFiStatusCode,
			ErrorDescription: "",
		}, nil
	}

	// Create the domain specific recurring payment entities (ENACH Mandate, UPI mandate, etc.) for successful mandate creation.
	if detailedStatusInfo.GetFiStatusCode() == pgerrorcodes.PaymentGatewaySuccessFiStatusCode && s.gconf.PgParams().EnableOffAppMandateCreation() {
		err := s.createDomainEntityForRecurringPayment(ctx, pgVendor, recurringPayment, fetchTokenRes)
		if err != nil {
			return nil, fmt.Errorf("error in creating domain entities for recurring payment: %w", err)
		}
	}

	return detailedStatusInfo, nil
}

func (s *Service) createDomainEntityForRecurringPayment(
	ctx context.Context,
	pgVendor commonvgpb.Vendor,
	recurringPayment *rpPb.RecurringPayment,
	fetchTokenRes *pg.FetchTokenResponse,
) error {
	switch fetchTokenRes.GetTokenDetails().GetAuthTypeV2() {
	case pg.AuthType_AUTH_TYPE_DEBIT_CARD:
		res, err := s.enachClient.CreateOffAppEnachMandate(ctx, &enachPb.CreateOffAppEnachMandateRequest{
			Umrn:                 fetchTokenRes.GetTokenDetails().GetMrn(),
			RecurringPaymentId:   recurringPayment.GetId(),
			Provenance:           enachEnumsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
			Vendor:               pgVendor,
			Ownership:            recurringPayment.GetEntityOwnership(),
			RegistrationAuthMode: enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_DEBIT_CARD,
		})
		if te := epifigrpc.RPCError(res, err); te != nil {
			return fmt.Errorf("error in creating ENACH mandate entity for PG recurrrinpayment: %w", te)
		}
	case pg.AuthType_AUTH_TYPE_NET_BANKING:
		res, err := s.enachClient.CreateOffAppEnachMandate(ctx, &enachPb.CreateOffAppEnachMandateRequest{
			Umrn:                 fetchTokenRes.GetTokenDetails().GetMrn(),
			RecurringPaymentId:   recurringPayment.GetId(),
			Provenance:           enachEnumsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
			Vendor:               pgVendor,
			Ownership:            recurringPayment.GetEntityOwnership(),
			RegistrationAuthMode: enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
		})
		if te := epifigrpc.RPCError(res, err); te != nil {
			return fmt.Errorf("error in creating ENACH mandate entity for PG recurrrinpayment: %w", te)
		}
	// TODO(Sundeep): Call UPI mandate entity creation code here.
	default:
		return fmt.Errorf("unsupported auth type: %v", fetchTokenRes.GetTokenDetails().GetAuthTypeV2())
	}
	return nil
}

// checkAndGetOvomForRecurringPaymentAction returns the appropriate order-vendor-order map for the given recurringpayment
// or the internal order by taking into account whether an internal order is created for the given recurringpayment action.
func (s *Service) checkAndGetOvomForRecurringPaymentAction(
	ctx context.Context,
	rpVd *rpPb.RecurringPaymentsVendorDetails,
	recurringPaymentAction *rpPb.RecurringPaymentsAction,
	recurringPayment *rpPb.RecurringPayment,
	internalOrder *orderPb.Order) (*orderPb.OrderVendorOrderMap, error) {
	var ovomRes *pay.GetOrderVendorOrderMapResponse
	var ovomErr error
	switch {
	case recurringPaymentAction.GetAction() == rpPb.Action_CREATE:
		// Fetch ovom using the recurring payment id for creation recurring payment action since in that case no
		// internal order is created.
		ovomRes, ovomErr = s.payClient.GetOrderVendorOrderMap(ctx, &pay.GetOrderVendorOrderMapRequest{
			OrderId: recurringPayment.GetId(),
			Vendor:  rpVd.GetVendor(),
		})
	case internalOrder.GetId() != "":
		// For rest of the recurringpayment action types fetch the ovom entry using internal order id
		ovomRes, ovomErr = s.payClient.GetOrderVendorOrderMap(ctx, &pay.GetOrderVendorOrderMapRequest{
			OrderId: internalOrder.GetId(),
			Vendor:  rpVd.GetVendor(),
		})
	default:
		logger.Error(
			ctx,
			"no internal order exists for the given recurring payment action nor it is an Action_CREATE flow",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()),
			zap.String(logger.CLIENT_REQUEST_ID, recurringPaymentAction.GetClientRequestId()),
			zap.String(logger.VENDOR, rpVd.GetVendor().String()),
			zap.String(logger.RECURRING_PAYMENT_ACTION_ID, recurringPaymentAction.GetId()),
		)
		return nil, fmt.Errorf("no appropriate input found for fetching the order vendor order map")
	}
	if te := epifigrpc.RPCError(ovomRes, ovomErr); te != nil {
		if ovomRes.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("error in fetching ovom for the given recurring payment id : %s : %w", recurringPayment.GetId(), te)
	}
	for _, ovom := range ovomRes.GetVendorOrderMaps() {
		if ovom.GetOrderDirection() != enums.OrderDirection_ORDER_DIRECTION_REVERSE {
			return ovom, nil
		}
	}
	return nil, fmt.Errorf("error in fetching ovom with forward order direction: %w", epifierrors.ErrRecordNotFound)
}

// getActionDetailedStatusInfoFromMandateStatus returns the ActionDetailedStatusInfo
// from the mandate registration detail status returned in the vendor response.
func (s *Service) getActionDetailedStatusInfoFromMandateStatus(vendor commonvgpb.Vendor, tokenRes *pg.FetchTokenResponse, paymentStatus pg.VendorPaymentStatus) (*rpPb.ActionDetailedStatusInfo, error) {
	errMapping, err := pgerrorcodes.GetErrorMappingFromPgErrorReason(vendor, pgerrorcodes.PgStageMandateRegistration, &pgerrorcodes.PgErrorReason{
		ErrorReason:   tokenRes.GetTokenDetails().GetRecurringDetails().GetFailureReason(),
		PaymentStatus: paymentStatus.ToPGPaymentStatus(),
		MandateStatus: tokenRes.GetTokenDetails().GetRecurringDetails().GetStatus().ToPGMandateStatus(),
	})
	if err != nil {
		return nil, fmt.Errorf("error in getting error mapping for mandate registration : %w", err)
	}
	return &rpPb.ActionDetailedStatusInfo{
		FiStatusCode:     errMapping.GetFiStatusCode(),
		ErrorDescription: errMapping.GetFiStatusDescription(),
	}, nil
}

// constructUniqueHashedAccountNumber constructs a unique hashed account number based on the parameters passed to it.
func constructUniqueHashedAccountNumber(params ...string) (string, error) {
	var concatenatedParameters string
	for _, param := range params {
		concatenatedParameters += param
	}
	if concatenatedParameters == "" {
		return "", fmt.Errorf("empty params passed to constructUniqueHashedAccountNumber")
	}
	return crypto.GetSHA1InBase32(concatenatedParameters), nil
}

// getOrderAndStatusFromInternalDb returns the order status based on the state stored in internal DB.
// First it tries to query the internal order entity's status. If that is not present then it tries to infer the order
// status from recurringpayment action. This is because in the overall flow, we update the order status first, and based
// on the order status the recurringpayment action status & the recurringpayment state is updated.
// It also returns the internal order if it exists in the DB.
func (s *Service) getOrderAndStatusFromInternalDb(ctx context.Context, req *paymentgateway.GetOrderStatusRequest, recurringPaymentAction *rpPb.RecurringPaymentsAction, recurringPayment *rpPb.RecurringPayment) (*paymentgateway.GetOrderStatusResponse, *orderPb.Order) {
	ctxWithOwnership := epificontext.WithOwnership(ctx, recurringPayment.GetEntityOwnership())
	orderWithTxnRes, err := s.orderClient.GetOrdersWithTransactions(ctxWithOwnership, &orderPb.GetOrdersWithTransactionsRequest{
		OrderIdentifiers: []*orderPb.OrderIdentifier{
			{
				Identifier: &orderPb.OrderIdentifier_ClientReqId{
					ClientReqId: req.GetClientRequestId(),
				},
			},
		},
	})

	orderWithTxnTe := epifigrpc.RPCError(orderWithTxnRes, err)

	if orderWithTxnTe == nil {
		orderWithTxn := orderWithTxnRes.GetOrderWithTransactions()[0]
		txns := orderWithTxn.GetTransactions()

		// If no transactions exist for the given order id then, the user may not have initiated the payment yet
		// and the transaction may or may not be within the timeout.
		if len(txns) == 0 {
			return &paymentgateway.GetOrderStatusResponse{
				Status:      rpc.StatusOk(),
				OrderStatus: getRpOrderStatusFromOrderStatus(orderWithTxn.GetOrder().GetStatus()),
			}, orderWithTxn.GetOrder()
		}
		latestTxn := txns[len(txns)-1]

		latestTxnDetailedStatus := latestTxn.GetLatestTxnDetailedStatus()

		return &paymentgateway.GetOrderStatusResponse{
			Status: rpc.StatusOk(),
			DetailedStatus: &rpPb.ActionDetailedStatusInfo{
				FiStatusCode:     latestTxnDetailedStatus.GetStatusCodePayer(),
				ErrorDescription: latestTxnDetailedStatus.GetStatusDescriptionPayer(),
			},
			OrderStatus: getRpOrderStatusFromOrderStatus(orderWithTxn.GetOrder().GetStatus()),
		}, orderWithTxn.GetOrder()
	}

	if orderWithTxnRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "no internal order record found for given inputs. trying to fetch status from recurring payment action",
			zap.Error(orderWithTxnTe),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		// not returning here, since we can infer the status from recurringpayment action
	} else {
		// orderWithTxnTe != nil if control reaches here
		logger.Error(ctx, "error in fetching internal order status. trying to fetch status from recurring payment action",
			zap.Error(orderWithTxnTe),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		// not returning here, since we can infer the status from recurringpayment action
	}

	// Try to infer the order status from the recurringpayment action state
	// since internal order could not be found
	return &paymentgateway.GetOrderStatusResponse{
		Status:         rpc.StatusOk(),
		OrderStatus:    getRpOrderStatusFromRpaState(recurringPaymentAction.GetState()),
		DetailedStatus: recurringPaymentAction.GetActionDetailedStatusInfo(),
	}, nil
}

func getRpOrderStatusFromOrderStatus(status orderPb.OrderStatus) rpEnumsPb.OrderStatus {
	switch status {
	case orderPb.OrderStatus_PAID:
		return rpEnumsPb.OrderStatus_ORDER_STATUS_PAID
	case orderPb.OrderStatus_PAYMENT_FAILED:
		return rpEnumsPb.OrderStatus_ORDER_STATUS_FAILED
	default:
		return rpEnumsPb.OrderStatus_ORDER_STATUS_IN_PROGRESS
	}
}

func getRpOrderStatusFromRpaState(state rpPb.ActionState) rpEnumsPb.OrderStatus {
	switch state {
	case rpPb.ActionState_ACTION_FAILURE, rpPb.ActionState_ACTION_EXPIRED, rpPb.ActionState_ACTION_REJECT:
		return rpEnumsPb.OrderStatus_ORDER_STATUS_FAILED
	case rpPb.ActionState_ACTION_SUCCESS:
		return rpEnumsPb.OrderStatus_ORDER_STATUS_PAID
	case rpPb.ActionState_ACTION_STATE_UNSPECIFIED:
		return rpEnumsPb.OrderStatus_ORDER_STATUS_UNSPECIFIED
	default:
		return rpEnumsPb.OrderStatus_ORDER_STATUS_IN_PROGRESS
	}
}

func (s *Service) GetMandateStatus(ctx context.Context, req *paymentgateway.GetMandateStatusRequest) (*paymentgateway.GetMandateStatusResponse, error) {
	var (
		res = &paymentgateway.GetMandateStatusResponse{}
		err error
	)

	recurringPayment, rpErr := s.rpDao.GetById(ctx, req.GetRecurringPaymentId())
	if rpErr != nil {
		logger.Error(ctx, "error in fetching recurring payment",
			zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	rpvd, rpvdErr := s.rpVdDao.GetByRecurringPaymentId(ctx, req.GetRecurringPaymentId())
	if rpvdErr != nil {
		logger.Error(ctx, "error in fetching recurring payment vendor details by recurring payment id", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(rpvdErr))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	pgVendor := rpvd.GetVendor()

	authParams, err := pgauthkeys.GetAuthParamsForPgProgram(&typesPb.PaymentGatewayProgram{
		PgVendor:        pgVendor,
		PiId:            recurringPayment.GetPiTo(),
		EntityOwnership: recurringPayment.GetEntityOwnership(),
	})
	if err != nil {
		logger.Error(ctx, "error in fetching authentication params for the pg program", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	fetchTokenRes, fetchTokenErr := s.vgPgClient.FetchToken(ctx, &pg.FetchTokenRequest{
		Header:               &commonvgpb.RequestHeader{Vendor: pgVendor},
		PaymentId:            rpvd.GetVendorPaymentId(),
		AuthenticationParams: authParams,
	})
	if te := epifigrpc.RPCError(fetchTokenRes, fetchTokenErr); te != nil {
		logger.Error(ctx, "error in fetching token status for vendor payment id", zap.Error(te), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.String(logger.VENDOR_ORDER_ID, rpvd.GetVendorPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	res.Status = rpc.StatusOk()
	res.TokenId = fetchTokenRes.GetTokenDetails().GetId()
	res.Mrn = fetchTokenRes.GetTokenDetails().GetMrn()
	res.MandateStatus = s.vgMandateStatusToBeMandateStatus(fetchTokenRes.GetTokenDetails().GetRecurringDetails().GetStatus())
	return res, nil
}

func (s *Service) vgMandateStatusToBeMandateStatus(mandateStatus pg.MandateStatus) paymentgateway.GetMandateStatusResponse_MandateStatus {
	if status, exists := vgMandateStatusToRespMandateStatus[mandateStatus]; exists {
		return status
	}
	return paymentgateway.GetMandateStatusResponse_MANDATE_STATUS_UNSPECIFIED
}
