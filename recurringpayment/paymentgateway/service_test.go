package paymentgateway

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/durationpb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/pay/pgerrorcodes"

	"github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/order"
	orderEnumsPb "github.com/epifi/gamma/api/order/enums"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/recurringpayment"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	"github.com/epifi/gamma/api/recurringpayment/enums"
	"github.com/epifi/gamma/api/recurringpayment/paymentgateway"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/vendorgateway/pg"
	payPkg "github.com/epifi/gamma/pkg/pay"
	"github.com/epifi/gamma/recurringpayment/internal/user"
)

var (
	rp1 = &recurringpayment.RecurringPayment{
		Id:          "rp-id-1",
		FromActorId: "actor-id-123",
		ToActorId:   "actor-id-456",
		Amount: &money.Money{
			CurrencyCode: "INR",
			Units:        100000, // Representing 1000.00 INR assuming the smallest unit is a paisa
		},
		Interval: &types.Interval{
			StartTime: timestampPb.Now(),
			EndTime:   timestampPb.New(timestampPb.Now().AsTime().AddDate(0, 3, 0)), // 3 months from now
		},
		State:           recurringpayment.RecurringPaymentState_ACTIVATED, // Assuming an Active state enum
		EntityOwnership: commontypes.Ownership_EPIFI_TECH,
		ExternalId:      "external-id-123",
		PiTo:            "paymentinstrument-creditcard-federal-pool-account-1",
		Type:            recurringpayment.RecurringPaymentType_UPI_MANDATES,
	}
	rp2 = &recurringpayment.RecurringPayment{
		Id:          "rp-id-2",
		FromActorId: "actor-id-123",
		ToActorId:   "actor-id-456",
		Amount: &money.Money{
			CurrencyCode: "INR",
			Units:        100000, // Representing 1000.00 INR assuming the smallest unit is a paisa
		},
		Interval: &types.Interval{
			StartTime: timestampPb.Now(),
			EndTime:   timestampPb.New(timestampPb.Now().AsTime().AddDate(0, 3, 0)), // 3 months from now
		},
		State:           recurringpayment.RecurringPaymentState_ACTIVATED, // Assuming an Active state enum
		EntityOwnership: commontypes.Ownership_EPIFI_TECH,
		ExternalId:      "external-id-123",
		PiTo:            "paymentinstrument-creditcard-federal-pool-account-1",
		Type:            recurringpayment.RecurringPaymentType_UPI_MANDATES,
	}
	rpa1 = &recurringpayment.RecurringPaymentsAction{
		Id:                 "rpa-id-1",
		RecurringPaymentId: "rp-id-1",
		ClientRequestId:    "client-req-id-1",
		Action:             recurringpayment.Action_CREATE,
		State:              recurringpayment.ActionState_ACTION_IN_PROGRESS,
	}
	rpvd = &recurringpayment.RecurringPaymentsVendorDetails{
		Id:                 "rpvd-id-1",
		RecurringPaymentId: "rp-id-1",
		Vendor:             commonvgpb.Vendor_RAZORPAY, // Example vendor, adjust according to your enum
		VendorCustomerId:   "vendor-customer-id-123",
		VendorPaymentId:    "vendor-payment-id-456",
	}
	rpvd2 = &recurringpayment.RecurringPaymentsVendorDetails{
		Id:                 "rpvd-id-2",
		RecurringPaymentId: "rp-id-2",
		Vendor:             commonvgpb.Vendor_RAZORPAY, // Example vendor, adjust according to your enum
		VendorCustomerId:   "vendor-customer-id-123",
	}
	authParams = &pg.AuthenticationParams{AuthParams: &pg.AuthenticationParams_RazorpayAuthenticationParams{RazorpayAuthenticationParams: &pg.RazorpayAuthenticationParams{
		AuthKey:    "rzp_test_J8bVSzs01Aaq7V",
		AuthSecret: "fjdeWi73ZX71TyZOjYzeB0jh",
	}}}
	vgHeader    = &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_RAZORPAY}
	userDetails = &user.UserDetails{
		PhoneNumber: &commontypes.PhoneNumber{
			CountryCode:    91,
			NationalNumber: 1234567890,
		},
		Email: "<EMAIL>",
		Name:  &commontypes.Name{FirstName: "Lorem", LastName: "Ipsum"},
	}
)

func TestService_CreateOrder(t *testing.T) {
	type args struct {
		ctx context.Context
		req *paymentgateway.CreateOrderRequest
	}
	tests := []struct {
		name       string
		args       args
		want       *paymentgateway.CreateOrderResponse
		wantErr    bool
		setupMocks func(md *mockDependencies)
	}{
		{
			name: "1. Successful creation of execution order",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.CreateOrderRequest{
					ClientRequestId:    "client-req-id-1",
					RecurringPaymentId: "rp-id-1",
					OrderAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        15000,
					},
					RecurringPaymentStage: enums.RecurringPaymentStage_RECURRING_PAYMENT_STAGE_EXECUTION,
					Vendor:                commonvgpb.Vendor_RAZORPAY,
				},
			},
			want: &paymentgateway.CreateOrderResponse{
				Status:          rpc.StatusOk(),
				VendorOrderId:   "order_123124124",
				InternalOrderId: "",
			},
			wantErr: false,
			setupMocks: func(md *mockDependencies) {
				md.userProcessorFactory.EXPECT().GetUserProcessor(gomock.Any(), "actor-id-123", commontypes.Ownership_EPIFI_TECH).Return(md.defaultUserProcessor)
				md.defaultUserProcessor.EXPECT().GetUserFromActorId(gomock.Any(), "actor-id-123", commontypes.Ownership_EPIFI_TECH).Return(userDetails, nil)
				md.payClient.EXPECT().CreateOrder(gomock.Any(), &pay.CreateOrderRequest{
					Order: &order.Order{
						FromActorId: rp1.GetFromActorId(),
						ToActorId:   rp1.GetToActorId(),
						Status:      order.OrderStatus_CREATED,
						Provenance:  order.OrderProvenance_INTERNAL,
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        15000,
						},
						ClientReqId: "client-req-id-1",
					},
					Vendor: commonvgpb.Vendor_RAZORPAY,
					DomainOrderData: &pay.DomainOrderData{
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        15000,
						},
						ClientRequestIdExpiry: nil,
						ToPi:                  rp1.GetPiTo(),
						ToActor:               rp1.GetToActorId(),
					},
					Ownership: rp1.GetEntityOwnership(),
					RecurringPaymentDetails: &pay.RecurringPaymentDetails{
						RecurringPaymentId: rp1.GetId(),
						VendorCustomerId:   rpvd.GetVendorCustomerId(),
					},
					AutoCaptureTimeoutOverride: durationpb.New(dynamicConf.PgParams().RpExecutionPaymentAutoCaptureTimeout().Get(rp1.GetType().String())),
				}).Return(&pay.CreateOrderResponse{
					Status:        rpc.StatusOk(),
					Order:         nil,
					VendorOrderId: "order_123124124",
				}, nil)
				md.rpDao.EXPECT().GetById(gomock.Any(), "rp-id-1").Return(rp1, nil)
				md.rpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id-1").Return(rpvd, nil)
				md.vgPgClient.EXPECT().FetchToken(gomock.Any(), gomock.Any()).Return(&pg.FetchTokenResponse{Status: rpc.StatusOk(), TokenId: "token_12345"}, nil)
				md.vgPgClient.EXPECT().CreateSubsequentPayment(gomock.Any(), gomock.Any()).Return(&pg.CreateSubsequentPaymentResponse{
					Status:    rpc.StatusOk(),
					PaymentId: "payment_12345",
				}, nil)
			},
		},
		{
			name: "2. Successful creation of registration creation",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.CreateOrderRequest{
					ClientRequestId:    "client-req-id-1",
					RecurringPaymentId: "rp-id-1",
					OrderAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        15000,
					},
					RecurringPaymentStage: enums.RecurringPaymentStage_RECURRING_PAYMENT_STAGE_REGISTRATION,
					Vendor:                commonvgpb.Vendor_RAZORPAY,
				},
			},
			want: &paymentgateway.CreateOrderResponse{
				Status:           rpc.StatusOk(),
				VendorOrderId:    "order_123124124",
				InternalOrderId:  "rp-id-1",
				VendorCustomerId: "vendor-customer-id-123",
				VendorAuthKey:    "rzp_test_J8bVSzs01Aaq7V",
			},
			wantErr: false,
			setupMocks: func(md *mockDependencies) {
				md.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-id-1", false).Return(&recurringpayment.RecurringPaymentsAction{State: recurringpayment.ActionState_ACTION_CREATED}, nil)
				md.userProcessorFactory.EXPECT().GetUserProcessor(gomock.Any(), "actor-id-123", commontypes.Ownership_EPIFI_TECH).Return(md.defaultUserProcessor)
				md.defaultUserProcessor.EXPECT().GetUserFromActorId(gomock.Any(), "actor-id-123", commontypes.Ownership_EPIFI_TECH).Return(userDetails, nil)
				md.rpDao.EXPECT().GetById(gomock.Any(), "rp-id-1").Return(rp1, nil)
				md.rpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id-1").Return(rpvd, nil)
				md.vgPgClient.EXPECT().CreateCustomer(gomock.Any(), gomock.Any()).Return(&pg.CreateCustomerResponse{
					Status:           rpc.StatusOk(),
					VendorCustomerId: "vendor-customer-id-123",
				}, nil)
				md.payClient.EXPECT().GetOrderVendorOrderMap(gomock.Any(), &pay.GetOrderVendorOrderMapRequest{
					OrderId: rp1.GetId(),
					Vendor:  commonvgpb.Vendor_RAZORPAY,
				}).Return(&pay.GetOrderVendorOrderMapResponse{Status: rpc.StatusRecordNotFound()}, nil)
				md.payClient.EXPECT().CreateOrderVendorOrderMap(gomock.Any(), &pay.CreateOrderVendorOrderMapRequest{
					OrderId:           rp1.GetId(),
					VendorOrderId:     "order_123124124",
					Vendor:            commonvgpb.Vendor_RAZORPAY,
					EntityOwnership:   commontypes.Ownership_EPIFI_TECH,
					DomainReferenceId: "client-req-id-1",
				}).Return(&pay.CreateOrderVendorOrderMapResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.vgPgClient.EXPECT().CreateOrder(gomock.Any(), gomock.Any()).Return(&pg.CreateOrderResponse{
					Status:        rpc.StatusOk(),
					VendorOrderId: "order_123124124",
					ReferenceId:   "external-id-123",
					OrderStatus:   pg.VendorOrderStatus_VENDOR_ORDER_STATUS_CREATED,
					OrderMetadata: nil,
					CreatedAt:     nil,
					RecurringPaymentDetails: &pg.RecurringPaymentDetails{
						FirstPaymentAmount: &types.Money{
							CurrencyCode: "INR",
							Units:        1,
						},
					},
				}, nil)
				md.rpvdDao.EXPECT().Update(gomock.Any(), rpvd, []recurringpayment.RecurringPaymentVendorDetailsFieldMask{
					recurringpayment.RecurringPaymentVendorDetailsFieldMask_RECURRING_PAYMENT_VENDOR_DETAILS_FIELD_MASK_VENDOR_CUSTOMER_ID,
				}).Return(nil)
			},
		},
		{
			name: "3. Order execution failure because of decline in payment",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.CreateOrderRequest{
					ClientRequestId:    "client-req-id-1",
					RecurringPaymentId: "rp-id-1",
					OrderAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        15000,
					},
					RecurringPaymentStage: enums.RecurringPaymentStage_RECURRING_PAYMENT_STAGE_EXECUTION,
					Vendor:                commonvgpb.Vendor_RAZORPAY,
				},
			},
			want: &paymentgateway.CreateOrderResponse{
				Status: rpc.StatusInternal(),
				DetailedStatus: &recurringpayment.ActionDetailedStatusInfo{
					FiStatusCode:     "RZP146",
					ErrorDescription: "Payment failed due to wrong request or input sent in the payment request. This is also seen while creating a payment with incorrect parameter values on the Dashboard.",
				},
			},
			wantErr: false,
			setupMocks: func(md *mockDependencies) {
				md.userProcessorFactory.EXPECT().GetUserProcessor(gomock.Any(), "actor-id-123", commontypes.Ownership_EPIFI_TECH).Return(md.defaultUserProcessor)
				md.defaultUserProcessor.EXPECT().GetUserFromActorId(gomock.Any(), "actor-id-123", commontypes.Ownership_EPIFI_TECH).Return(userDetails, nil)
				md.payClient.EXPECT().CreateOrder(gomock.Any(), &pay.CreateOrderRequest{
					Order: &order.Order{
						FromActorId: rp1.GetFromActorId(),
						ToActorId:   rp1.GetToActorId(),
						Status:      order.OrderStatus_CREATED,
						Provenance:  order.OrderProvenance_INTERNAL,
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        15000,
						},
						ClientReqId: "client-req-id-1",
					},
					Vendor: commonvgpb.Vendor_RAZORPAY,
					DomainOrderData: &pay.DomainOrderData{
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        15000,
						},
						ClientRequestIdExpiry: nil,
						ToPi:                  rp1.GetPiTo(),
						ToActor:               rp1.GetToActorId(),
					},
					Ownership: rp1.GetEntityOwnership(),
					RecurringPaymentDetails: &pay.RecurringPaymentDetails{
						RecurringPaymentId: rp1.GetId(),
						VendorCustomerId:   rpvd.GetVendorCustomerId(),
					},
					AutoCaptureTimeoutOverride: durationpb.New(dynamicConf.PgParams().RpExecutionPaymentAutoCaptureTimeout().Get(rp1.GetType().String())),
				}).Return(&pay.CreateOrderResponse{
					Status:        rpc.StatusOk(),
					Order:         nil,
					VendorOrderId: "order_123124124",
				}, nil)
				md.rpDao.EXPECT().GetById(gomock.Any(), "rp-id-1").Return(rp1, nil)
				md.rpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id-1").Return(rpvd, nil)
				md.vgPgClient.EXPECT().FetchToken(gomock.Any(), gomock.Any()).Return(&pg.FetchTokenResponse{Status: rpc.StatusOk(), TokenId: "token_12345"}, nil)
				md.vgPgClient.EXPECT().CreateSubsequentPayment(gomock.Any(), gomock.Any()).Return(&pg.CreateSubsequentPaymentResponse{
					Status: rpc.StatusInternal(),
					VendorStatus: &commonvgpb.VendorStatus{
						Code:        "BAD_REQUEST_ERROR",
						Description: "src: business, step: payment_initiation, reason: input_validation_failed, field: amount, desc: Your payment amount is different from your order amount. To pay successfully, please try using right amount.",
						ErrorReason: "input_validation_failed",
					},
				}, nil)
			},
		},
		{
			name: "4. Fail registration creation early if recurring payment action is already in terminal state",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.CreateOrderRequest{
					ClientRequestId:    "client-req-id-1",
					RecurringPaymentId: "rp-id-1",
					OrderAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        15000,
					},
					RecurringPaymentStage: enums.RecurringPaymentStage_RECURRING_PAYMENT_STAGE_REGISTRATION,
					Vendor:                commonvgpb.Vendor_RAZORPAY,
				},
			},
			want: &paymentgateway.CreateOrderResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
			setupMocks: func(md *mockDependencies) {
				md.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-id-1", false).Return(&recurringpayment.RecurringPaymentsAction{State: recurringpayment.ActionState_ACTION_FAILURE}, nil)
				md.rpDao.EXPECT().GetById(gomock.Any(), "rp-id-1").Return(rp1, nil)
			},
		},
		{
			name: "5. Don't create a new order at vendor side if OVOM entry already exists (idempotency case)",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.CreateOrderRequest{
					ClientRequestId:    "client-req-id-1",
					RecurringPaymentId: "rp-id-1",
					OrderAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        15000,
					},
					RecurringPaymentStage: enums.RecurringPaymentStage_RECURRING_PAYMENT_STAGE_REGISTRATION,
					Vendor:                commonvgpb.Vendor_RAZORPAY,
				},
			},
			want: &paymentgateway.CreateOrderResponse{
				Status:           rpc.StatusOk(),
				VendorOrderId:    "order_123124124",
				InternalOrderId:  "rp-id-1",
				VendorCustomerId: "vendor-customer-id-123",
				VendorAuthKey:    "rzp_test_J8bVSzs01Aaq7V",
			},
			wantErr: false,
			setupMocks: func(md *mockDependencies) {
				md.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-id-1", false).Return(&recurringpayment.RecurringPaymentsAction{State: recurringpayment.ActionState_ACTION_CREATED}, nil)
				md.userProcessorFactory.EXPECT().GetUserProcessor(gomock.Any(), "actor-id-123", commontypes.Ownership_EPIFI_TECH).Return(md.defaultUserProcessor)
				md.defaultUserProcessor.EXPECT().GetUserFromActorId(gomock.Any(), "actor-id-123", commontypes.Ownership_EPIFI_TECH).Return(userDetails, nil)
				md.rpDao.EXPECT().GetById(gomock.Any(), "rp-id-1").Return(rp1, nil)
				md.rpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id-1").Return(rpvd, nil)
				md.vgPgClient.EXPECT().CreateCustomer(gomock.Any(), gomock.Any()).Return(&pg.CreateCustomerResponse{
					Status:           rpc.StatusOk(),
					VendorCustomerId: "vendor-customer-id-123",
				}, nil)
				md.payClient.EXPECT().GetOrderVendorOrderMap(gomock.Any(), &pay.GetOrderVendorOrderMapRequest{
					OrderId: rp1.GetId(),
					Vendor:  commonvgpb.Vendor_RAZORPAY,
				}).Return(&pay.GetOrderVendorOrderMapResponse{
					Status: rpc.StatusOk(),
					VendorOrderMaps: []*order.OrderVendorOrderMap{
						{
							Id:                "ovom-1",
							OrderId:           rp1.GetId(),
							VendorOrderId:     "order_123124124",
							Vendor:            commonvgpb.Vendor_RAZORPAY,
							EntityOwnership:   commontypes.Ownership_EPIFI_TECH,
							DomainReferenceId: "client-req-id-1",
						},
					},
				}, nil)
				md.rpvdDao.EXPECT().Update(gomock.Any(), rpvd, []recurringpayment.RecurringPaymentVendorDetailsFieldMask{
					recurringpayment.RecurringPaymentVendorDetailsFieldMask_RECURRING_PAYMENT_VENDOR_DETAILS_FIELD_MASK_VENDOR_CUSTOMER_ID,
				}).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md := getServiceWithMocks(t)
			tt.setupMocks(md)
			got, err := s.CreateOrder(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateOrder() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("CreateOrder() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetOrderStatus(t *testing.T) {
	type args struct {
		ctx        context.Context
		req        *paymentgateway.GetOrderStatusRequest
		setupMocks func(md *mockDependencies)
	}
	tests := []struct {
		name    string
		args    args
		want    *paymentgateway.GetOrderStatusResponse
		wantErr bool
	}{
		{
			name: "1. Successful order status retrieval from vendor",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.GetOrderStatusRequest{
					RecurringPaymentId: rp1.GetId(),
					ClientRequestId:    "client-req-id-1",
					Vendor:             commonvgpb.Vendor_RAZORPAY,
					DataFreshness:      paymentgateway.GetOrderStatusRequest_REAL_TIME,
				},
				setupMocks: func(md *mockDependencies) {
					md.orderClient.EXPECT().GetOrdersWithTransactions(gomock.Any(), &order.GetOrdersWithTransactionsRequest{
						OrderIdentifiers: []*order.OrderIdentifier{
							{
								Identifier: &order.OrderIdentifier_ClientReqId{
									ClientReqId: "client-req-id-1",
								},
							},
						},
					}).Return(&order.GetOrdersWithTransactionsResponse{Status: rpc.StatusRecordNotFound()}, nil)
					md.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-id-1", false).Return(rpa1, nil)
					md.rpDao.EXPECT().GetById(gomock.Any(), "rp-id-1").Return(rp1, nil)
					md.rpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id-1").Return(rpvd, nil)
					md.payClient.EXPECT().GetOrderVendorOrderMap(gomock.Any(), &pay.GetOrderVendorOrderMapRequest{
						OrderId: "rp-id-1",
						Vendor:  commonvgpb.Vendor_RAZORPAY,
					}).Return(&pay.GetOrderVendorOrderMapResponse{
						Status: rpc.StatusOk(),
						VendorOrderMaps: []*order.OrderVendorOrderMap{
							{
								Id:             "ovom-id-1",
								OrderId:        "rp-id-1",
								VendorOrderId:  "order_123124124",
								Vendor:         commonvgpb.Vendor_RAZORPAY,
								OrderDirection: orderEnumsPb.OrderDirection_ORDER_DIRECTION_FORWARD,
							},
						},
					}, nil)
					md.vgPgClient.EXPECT().FetchOrderStatus(gomock.Any(), gomock.Any()).Return(&pg.FetchOrderStatusResponse{
						Status:      rpc.StatusOk(),
						OrderStatus: pg.VendorOrderStatus_VENDOR_ORDER_STATUS_CREATED,
						CreatedAt:   timestampPb.New(seedTime),
					}, nil)
				},
			},
			want:    &paymentgateway.GetOrderStatusResponse{Status: rpc.StatusOk(), OrderStatus: enums.OrderStatus_ORDER_STATUS_IN_PROGRESS},
			wantErr: false,
		},
		{
			name: "2. Successful order status retrieval from internal & freshness is not realtime, but no transaction is created against the order",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.GetOrderStatusRequest{RecurringPaymentId: rp1.GetId(), ClientRequestId: "client-req-id-1", Vendor: commonvgpb.Vendor_RAZORPAY},
				setupMocks: func(md *mockDependencies) {
					md.rpDao.EXPECT().GetById(gomock.Any(), "rp-id-1").Return(rp1, nil)
					md.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-id-1", false).Return(rpa1, nil)
					md.orderClient.EXPECT().GetOrdersWithTransactions(gomock.Any(), &order.GetOrdersWithTransactionsRequest{
						OrderIdentifiers: []*order.OrderIdentifier{
							{
								Identifier: &order.OrderIdentifier_ClientReqId{
									ClientReqId: "client-req-id-1",
								},
							},
						},
					}).Return(&order.GetOrdersWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: []*order.OrderWithTransactions{
							{
								Order: &order.Order{
									Status: order.OrderStatus_CREATED,
								},
							},
						},
					}, nil)

				},
			},
			want:    &paymentgateway.GetOrderStatusResponse{Status: rpc.StatusOk(), OrderStatus: enums.OrderStatus_ORDER_STATUS_IN_PROGRESS},
			wantErr: false,
		},
		{
			name: "3. order status not found from internal DB and data requested from vendor",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.GetOrderStatusRequest{RecurringPaymentId: rp1.GetId(), ClientRequestId: "client-req-id-1", Vendor: commonvgpb.Vendor_RAZORPAY, DataFreshness: paymentgateway.GetOrderStatusRequest_REAL_TIME},
				setupMocks: func(md *mockDependencies) {
					md.orderClient.EXPECT().GetOrdersWithTransactions(gomock.Any(), &order.GetOrdersWithTransactionsRequest{
						OrderIdentifiers: []*order.OrderIdentifier{
							{
								Identifier: &order.OrderIdentifier_ClientReqId{
									ClientReqId: "client-req-id-1",
								},
							},
						},
					}).Return(&order.GetOrdersWithTransactionsResponse{Status: rpc.StatusRecordNotFound()}, nil)
					md.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-id-1", false).Return(rpa1, nil)
					md.rpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id-1").Return(rpvd, nil)
					md.rpDao.EXPECT().GetById(gomock.Any(), "rp-id-1").Return(rp1, nil)
					md.payClient.EXPECT().GetOrderVendorOrderMap(gomock.Any(), &pay.GetOrderVendorOrderMapRequest{
						OrderId: "rp-id-1",
						Vendor:  commonvgpb.Vendor_RAZORPAY,
					}).Return(&pay.GetOrderVendorOrderMapResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)
				},
			},
			want:    &paymentgateway.GetOrderStatusResponse{Status: rpc.StatusRecordNotFound()},
			wantErr: false,
		},
		{
			name: "4. Handling when order status is PAID, mandate registration success and status is fetched from vendor",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.GetOrderStatusRequest{RecurringPaymentId: "rp-id-2", ClientRequestId: "client-req-id-1", Vendor: commonvgpb.Vendor_RAZORPAY, DataFreshness: paymentgateway.GetOrderStatusRequest_REAL_TIME},
				setupMocks: func(md *mockDependencies) {
					rpa2 := &recurringpayment.RecurringPaymentsAction{
						Id:                 "rpa-id-2",
						RecurringPaymentId: "rp-id-2",
						ClientRequestId:    "client-req-id-1",
						Action:             recurringpayment.Action_CREATE,
					}
					rp3 := &recurringpayment.RecurringPayment{
						Id:          "rp-id-2",
						FromActorId: "actor-id-123",
						ToActorId:   "actor-id-456",
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        100000, // Representing 1000.00 INR assuming the smallest unit is a paisa
						},
						Interval: &types.Interval{
							StartTime: timestampPb.Now(),
							EndTime:   timestampPb.New(timestampPb.Now().AsTime().AddDate(0, 3, 0)), // 3 months from now
						},
						State:           recurringpayment.RecurringPaymentState_ACTIVATED, // Assuming an Active state enum
						EntityOwnership: commontypes.Ownership_EPIFI_TECH,
						ExternalId:      "external-id-123",
						PiTo:            "paymentinstrument-creditcard-federal-pool-account-1",
						Type:            recurringpayment.RecurringPaymentType_ENACH_MANDATES,
					}
					md.orderClient.EXPECT().GetOrdersWithTransactions(gomock.Any(), &order.GetOrdersWithTransactionsRequest{
						OrderIdentifiers: []*order.OrderIdentifier{
							{
								Identifier: &order.OrderIdentifier_ClientReqId{
									ClientReqId: "client-req-id-1",
								},
							},
						},
					}).Return(&order.GetOrdersWithTransactionsResponse{Status: rpc.StatusRecordNotFound()}, nil)
					md.rpDao.EXPECT().GetById(gomock.Any(), "rp-id-2").Return(rp3, nil)
					md.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-id-1", false).Return(rpa2, nil)
					md.rpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id-2").Return(rpvd2, nil)
					md.payClient.EXPECT().GetOrderVendorOrderMap(gomock.Any(), &pay.GetOrderVendorOrderMapRequest{
						OrderId: "rp-id-2",
						Vendor:  commonvgpb.Vendor_RAZORPAY,
					}).Return(&pay.GetOrderVendorOrderMapResponse{
						Status: rpc.StatusOk(),
						VendorOrderMaps: []*order.OrderVendorOrderMap{
							{
								Id:             "ovom-id-1",
								OrderId:        "rp-id-1",
								VendorOrderId:  "order_123124124",
								Vendor:         commonvgpb.Vendor_RAZORPAY,
								OrderDirection: orderEnumsPb.OrderDirection_ORDER_DIRECTION_FORWARD,
							},
						},
					}, nil)
					md.vgPgClient.EXPECT().FetchOrderStatus(gomock.Any(), gomock.Any()).Return(&pg.FetchOrderStatusResponse{
						Status:      rpc.StatusOk(),
						OrderStatus: pg.VendorOrderStatus_VENDOR_ORDER_STATUS_PAID,
						CreatedAt:   timestampPb.New(seedTime),
					}, nil)
					md.vgPgClient.EXPECT().FetchPaymentsForOrderId(gomock.Any(), gomock.Any()).Return(&pg.FetchPaymentsForOrderIdResponse{
						Status:        rpc.StatusOk(),
						PaymentsCount: 1,
						Payments: []*pg.Payment{{
							VendorPaymentId:           "payment_12345",
							Method:                    pg.PaymentMethod_PAYMENT_METHOD_CARD,
							Status:                    pg.VendorPaymentStatus_VENDOR_PAYMENT_STATUS_CAPTURED,
							IsPaymentCapturedByVendor: true,
							CardPaymentDetails: &pg.CardPaymentDetails{
								CardId: "card-id-1",
							},
							AcquirerDetails: &pg.AcquirerDetails{
								BankTransactionId: "bank-txn-id-1",
							},
						}},
					}, nil)
					md.vgPgClient.EXPECT().FetchToken(gomock.Any(), gomock.Any()).Return(&pg.FetchTokenResponse{
						Status:  rpc.StatusOk(),
						TokenId: "token-id-1",
						TokenDetails: &pg.TokenDetails{
							Id:        "token-id-1",
							Entity:    "token",
							Recurring: true,
							RecurringDetails: &pg.MandateDetails{
								Status:        pg.MandateStatus_MANDATE_STATUS_CONFIRMED,
								FailureReason: "",
							},
							AuthTypeV2: pg.AuthType_AUTH_TYPE_DEBIT_CARD,
							Mrn:        "mrn-1",
						},
					}, nil)
					md.userProcessorFactory.EXPECT().GetUserProcessor(gomock.Any(), "actor-id-123", commontypes.Ownership_EPIFI_TECH).Return(md.defaultUserProcessor)
					md.defaultUserProcessor.EXPECT().GetUserFromActorId(gomock.Any(), "actor-id-123", commontypes.Ownership_EPIFI_TECH).Return(userDetails, nil)
					hash, _ := constructUniqueHashedAccountNumber("card-id-1")
					md.piClient.EXPECT().CreatePi(gomock.Any(), &paymentinstrument.CreatePiRequest{
						Type:  paymentinstrument.PaymentInstrumentType_GENERIC,
						State: paymentinstrument.PaymentInstrumentState_CREATED,
						Identifier: &paymentinstrument.CreatePiRequest_Account{Account: &paymentinstrument.Account{
							ActualAccountNumber: hash,
							SecureAccountNumber: mask.MaskLastNDigits(hash, 3, ""),
							IfscCode:            payPkg.DefaultIfscCardTxn,
							AccountType:         accounts.Type_SAVINGS,
							Name:                "Lorem Ipsum",
						}},
						IssuerClassification: paymentinstrument.PaymentInstrumentIssuer_EXTERNAL,
						Capabilities: map[string]bool{
							paymentinstrument.Capability_INBOUND_TXN.String():  true,
							paymentinstrument.Capability_OUTBOUND_TXN.String(): true,
						}}).Return(&paymentinstrument.CreatePiResponse{
						PaymentInstrument: &paymentinstrument.PaymentInstrument{
							Id: "paymentinstrument-creditcard-federal-pool-account-1",
						},
						Status: rpc.StatusOk(),
					}, nil)
					md.rpDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), rp3, []recurringpayment.RecurringPaymentFieldMask{
						recurringpayment.RecurringPaymentFieldMask_PI_FROM,
					}, recurringpayment.RecurringPaymentState_ACTIVATED, recurringpayment.RecurringPaymentState_ACTIVATED).Return(nil)
					md.rpvdDao.EXPECT().Update(gomock.Any(), rpvd2, []recurringpayment.RecurringPaymentVendorDetailsFieldMask{
						recurringpayment.RecurringPaymentVendorDetailsFieldMask_RECURRING_PAYMENT_VENDOR_DETAILS_FIELD_MASK_VENDOR_PAYMENT_ID,
					}).Return(nil)
					md.enachClient.EXPECT().CreateOffAppEnachMandate(gomock.Any(), &enachPb.CreateOffAppEnachMandateRequest{
						Umrn:                 "mrn-1",
						RecurringPaymentId:   "rp-id-2",
						Provenance:           enachEnumsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
						Vendor:               commonvgpb.Vendor_RAZORPAY,
						Ownership:            commontypes.Ownership_EPIFI_TECH,
						RegistrationAuthMode: enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_DEBIT_CARD,
					}).Return(&enachPb.CreateOffAppEnachMandateResponse{Status: rpc.StatusOk()}, nil)
				},
			},
			want:    &paymentgateway.GetOrderStatusResponse{Status: rpc.StatusOk(), OrderStatus: enums.OrderStatus_ORDER_STATUS_PAID, DetailedStatus: &recurringpayment.ActionDetailedStatusInfo{FiStatusCode: pgerrorcodes.PaymentGatewaySuccessFiStatusCode}},
			wantErr: false,
		},
		{
			name: "5. Return recurringpayment order status as failed if order is expired from vendor",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.GetOrderStatusRequest{RecurringPaymentId: "rp-id-2", ClientRequestId: "client-req-id-1", Vendor: commonvgpb.Vendor_RAZORPAY, DataFreshness: paymentgateway.GetOrderStatusRequest_REAL_TIME},
				setupMocks: func(md *mockDependencies) {
					rpa2 := &recurringpayment.RecurringPaymentsAction{
						Id:                 "rpa-id-2",
						RecurringPaymentId: "rp-id-2",
						ClientRequestId:    "client-req-id-1",
						Action:             recurringpayment.Action_CREATE,
					}
					rpvd3 := &recurringpayment.RecurringPaymentsVendorDetails{
						Id:                 "rpvd-id-2",
						RecurringPaymentId: "rp-id-2",
						Vendor:             commonvgpb.Vendor_RAZORPAY, // Example vendor, adjust according to your enum
						VendorCustomerId:   "vendor-customer-id-123",
					}
					rp3 := &recurringpayment.RecurringPayment{
						Id:          "rp-id-2",
						FromActorId: "actor-id-123",
						ToActorId:   "actor-id-456",
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        100000, // Representing 1000.00 INR assuming the smallest unit is a paisa
						},
						Interval: &types.Interval{
							StartTime: timestampPb.Now(),
							EndTime:   timestampPb.New(timestampPb.Now().AsTime().AddDate(0, 3, 0)), // 3 months from now
						},
						State:           recurringpayment.RecurringPaymentState_ACTIVATED, // Assuming an Active state enum
						EntityOwnership: commontypes.Ownership_EPIFI_TECH,
						ExternalId:      "external-id-123",
						PiTo:            "paymentinstrument-creditcard-federal-pool-account-1",
						Type:            recurringpayment.RecurringPaymentType_UPI_MANDATES,
					}
					md.orderClient.EXPECT().GetOrdersWithTransactions(gomock.Any(), &order.GetOrdersWithTransactionsRequest{
						OrderIdentifiers: []*order.OrderIdentifier{
							{
								Identifier: &order.OrderIdentifier_ClientReqId{
									ClientReqId: "client-req-id-1",
								},
							},
						},
					}).Return(&order.GetOrdersWithTransactionsResponse{Status: rpc.StatusRecordNotFound()}, nil)
					md.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-id-1", false).Return(rpa2, nil)
					md.rpDao.EXPECT().GetById(gomock.Any(), "rp-id-2").Return(rp3, nil)
					md.rpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id-2").Return(rpvd3, nil)
					md.payClient.EXPECT().GetOrderVendorOrderMap(gomock.Any(), &pay.GetOrderVendorOrderMapRequest{
						OrderId: "rp-id-2",
						Vendor:  commonvgpb.Vendor_RAZORPAY,
					}).Return(&pay.GetOrderVendorOrderMapResponse{
						Status: rpc.StatusOk(),
						VendorOrderMaps: []*order.OrderVendorOrderMap{
							{
								Id:             "ovom-id-1",
								OrderId:        "rp-id-1",
								VendorOrderId:  "order_123124124",
								Vendor:         commonvgpb.Vendor_RAZORPAY,
								OrderDirection: orderEnumsPb.OrderDirection_ORDER_DIRECTION_FORWARD,
							},
						},
					}, nil)
					md.vgPgClient.EXPECT().FetchOrderStatus(gomock.Any(), gomock.Any()).Return(&pg.FetchOrderStatusResponse{
						Status:      rpc.StatusOk(),
						OrderStatus: pg.VendorOrderStatus_VENDOR_ORDER_STATUS_CREATED,
						CreatedAt:   timestampPb.New(seedTime.Add(-(time.Minute * 16))),
					}, nil)
				},
			},
			want:    &paymentgateway.GetOrderStatusResponse{Status: rpc.StatusOk(), OrderStatus: enums.OrderStatus_ORDER_STATUS_FAILED},
			wantErr: false,
		},
		{
			name: "6. Successful order status retrieval from internal, and the order has succeeded, only internal order is fetched even though real-time freshness is requested",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.GetOrderStatusRequest{RecurringPaymentId: rp1.GetId(), ClientRequestId: "client-req-id-1", Vendor: commonvgpb.Vendor_RAZORPAY, DataFreshness: paymentgateway.GetOrderStatusRequest_REAL_TIME},
				setupMocks: func(md *mockDependencies) {
					md.rpDao.EXPECT().GetById(gomock.Any(), "rp-id-1").Return(rp1, nil)
					md.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-id-1", false).Return(rpa1, nil)
					md.orderClient.EXPECT().GetOrdersWithTransactions(gomock.Any(), &order.GetOrdersWithTransactionsRequest{
						OrderIdentifiers: []*order.OrderIdentifier{
							{
								Identifier: &order.OrderIdentifier_ClientReqId{
									ClientReqId: "client-req-id-1",
								},
							},
						},
					}).Return(&order.GetOrdersWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: []*order.OrderWithTransactions{
							{
								Order: &order.Order{
									Status: order.OrderStatus_PAID,
								},
								Transactions: []*payment.Transaction{
									{
										Id:     "txn-1",
										Amount: nil,
										Status: payment.TransactionStatus_SUCCESS,
										DetailedStatus: &payment.TransactionDetailedStatus{
											DetailedStatusList: []*payment.TransactionDetailedStatus_DetailedStatus{
												{
													StatusCodePayer: pgerrorcodes.PaymentGatewaySuccessFiStatusCode,
													ErrorCategory:   payment.TransactionDetailedStatus_DetailedStatus_ERROR_CATEGORY_UNSPECIFIED,
													CreatedAt:       timestampPb.Now(),
													State:           payment.TransactionDetailedStatus_DetailedStatus_SUCCESS,
													Api:             payment.TransactionDetailedStatus_DetailedStatus_PAYMENT_GATEWAY_PAYMENTS,
												},
											},
										},
										PaymentProtocol: payment.PaymentProtocol_CARD,
									},
								},
							},
						},
					}, nil)

				},
			},
			want: &paymentgateway.GetOrderStatusResponse{
				Status:      rpc.StatusOk(),
				OrderStatus: enums.OrderStatus_ORDER_STATUS_PAID,
				DetailedStatus: &recurringpayment.ActionDetailedStatusInfo{
					FiStatusCode:     pgerrorcodes.PaymentGatewaySuccessFiStatusCode,
					ErrorDescription: "",
				},
			},
			wantErr: false,
		},
		{
			name: "7. Successful order status retrieval from internal, and the order has failed",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.GetOrderStatusRequest{RecurringPaymentId: rp1.GetId(), ClientRequestId: "client-req-id-1", Vendor: commonvgpb.Vendor_RAZORPAY},
				setupMocks: func(md *mockDependencies) {
					md.rpDao.EXPECT().GetById(gomock.Any(), "rp-id-1").Return(rp1, nil)
					md.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-id-1", false).Return(rpa1, nil)
					md.orderClient.EXPECT().GetOrdersWithTransactions(gomock.Any(), &order.GetOrdersWithTransactionsRequest{
						OrderIdentifiers: []*order.OrderIdentifier{
							{
								Identifier: &order.OrderIdentifier_ClientReqId{
									ClientReqId: "client-req-id-1",
								},
							},
						},
					}).Return(&order.GetOrdersWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: []*order.OrderWithTransactions{
							{
								Order: &order.Order{
									Status: order.OrderStatus_PAYMENT_FAILED,
								},
								Transactions: []*payment.Transaction{
									{
										Id:     "txn-1",
										Amount: nil,
										Status: payment.TransactionStatus_FAILED,
										DetailedStatus: &payment.TransactionDetailedStatus{
											DetailedStatusList: []*payment.TransactionDetailedStatus_DetailedStatus{
												{
													StatusCodePayer:        "RZP100",
													StatusDescriptionPayer: "Error in validating inputs",
													ErrorCategory:          payment.TransactionDetailedStatus_DetailedStatus_USER,
													CreatedAt:              timestampPb.Now(),
													State:                  payment.TransactionDetailedStatus_DetailedStatus_FAILURE,
													Api:                    payment.TransactionDetailedStatus_DetailedStatus_PAYMENT_GATEWAY_PAYMENTS,
												},
											},
										},
										PaymentProtocol: payment.PaymentProtocol_CARD,
									},
								},
							},
						},
					}, nil)

				},
			},
			want: &paymentgateway.GetOrderStatusResponse{
				Status:      rpc.StatusOk(),
				OrderStatus: enums.OrderStatus_ORDER_STATUS_FAILED,
				DetailedStatus: &recurringpayment.ActionDetailedStatusInfo{
					FiStatusCode:     "RZP100",
					ErrorDescription: "Error in validating inputs",
				},
			},
			wantErr: false,
		},
		{
			name: "8. order status found from recurring payment action",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.GetOrderStatusRequest{RecurringPaymentId: rp1.GetId(), ClientRequestId: "client-req-id-1", Vendor: commonvgpb.Vendor_RAZORPAY},
				setupMocks: func(md *mockDependencies) {
					md.orderClient.EXPECT().GetOrdersWithTransactions(gomock.Any(), &order.GetOrdersWithTransactionsRequest{
						OrderIdentifiers: []*order.OrderIdentifier{
							{
								Identifier: &order.OrderIdentifier_ClientReqId{
									ClientReqId: "client-req-id-1",
								},
							},
						},
					}).Return(&order.GetOrdersWithTransactionsResponse{Status: rpc.StatusRecordNotFound()}, nil)
					md.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-id-1", false).Return(&recurringpayment.RecurringPaymentsAction{
						State:  recurringpayment.ActionState_ACTION_FAILURE,
						Action: recurringpayment.Action_CREATE,
						ActionDetailedStatusInfo: &recurringpayment.ActionDetailedStatusInfo{
							FiStatusCode:     "RZP100",
							ErrorDescription: "Error in validating inputs",
						},
					}, nil)
					md.rpDao.EXPECT().GetById(gomock.Any(), "rp-id-1").Return(rp1, nil)
				},
			},
			want: &paymentgateway.GetOrderStatusResponse{
				Status:      rpc.StatusOk(),
				OrderStatus: enums.OrderStatus_ORDER_STATUS_FAILED,
				DetailedStatus: &recurringpayment.ActionDetailedStatusInfo{
					FiStatusCode:     "RZP100",
					ErrorDescription: "Error in validating inputs",
				},
			},
			wantErr: false,
		},
		{
			name: "9. return internal error if no recurring payment action exists",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.GetOrderStatusRequest{RecurringPaymentId: rp1.GetId(), ClientRequestId: "client-req-id-1", Vendor: commonvgpb.Vendor_RAZORPAY},
				setupMocks: func(md *mockDependencies) {
					md.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-id-1", false).Return(nil, epifierrors.ErrRecordNotFound)
					md.rpDao.EXPECT().GetById(gomock.Any(), "rp-id-1").Return(rp1, nil)
				},
			},
			want:    &paymentgateway.GetOrderStatusResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "10. GetOrderStatus queried for recurringpayment execution action, internal order & RPA are in progress, and status is queried from vendor",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.GetOrderStatusRequest{RecurringPaymentId: "rp-id-2", ClientRequestId: "client-req-id-1", Vendor: commonvgpb.Vendor_RAZORPAY, DataFreshness: paymentgateway.GetOrderStatusRequest_REAL_TIME},
				setupMocks: func(md *mockDependencies) {
					rpa2 := &recurringpayment.RecurringPaymentsAction{
						Id:                 "rpa-id-2",
						RecurringPaymentId: "rp-id-2",
						ClientRequestId:    "client-req-id-1",
						Action:             recurringpayment.Action_EXECUTE,
					}
					md.orderClient.EXPECT().GetOrdersWithTransactions(gomock.Any(), &order.GetOrdersWithTransactionsRequest{
						OrderIdentifiers: []*order.OrderIdentifier{
							{
								Identifier: &order.OrderIdentifier_ClientReqId{
									ClientReqId: "client-req-id-1",
								},
							},
						},
					}).Return(&order.GetOrdersWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: []*order.OrderWithTransactions{
							{
								Order: &order.Order{
									Id:     "order-id-2",
									Status: order.OrderStatus_COLLECT_IN_PROGRESS,
								},
							},
						},
					}, nil)
					md.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-id-1", false).Return(rpa2, nil)
					md.rpDao.EXPECT().GetById(gomock.Any(), "rp-id-2").Return(rp2, nil)
					md.rpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id-2").Return(rpvd2, nil)
					md.payClient.EXPECT().GetOrderVendorOrderMap(gomock.Any(), &pay.GetOrderVendorOrderMapRequest{
						OrderId: "order-id-2",
						Vendor:  commonvgpb.Vendor_RAZORPAY,
					}).Return(&pay.GetOrderVendorOrderMapResponse{
						Status: rpc.StatusOk(),
						VendorOrderMaps: []*order.OrderVendorOrderMap{
							{
								Id:             "ovom-id-1",
								OrderId:        "rp-id-1",
								VendorOrderId:  "order_123124124",
								Vendor:         commonvgpb.Vendor_RAZORPAY,
								OrderDirection: orderEnumsPb.OrderDirection_ORDER_DIRECTION_FORWARD,
							},
						},
					}, nil)
					md.vgPgClient.EXPECT().FetchOrderStatus(gomock.Any(), gomock.Any()).Return(&pg.FetchOrderStatusResponse{
						Status:      rpc.StatusOk(),
						OrderStatus: pg.VendorOrderStatus_VENDOR_ORDER_STATUS_CREATED,
						CreatedAt:   timestampPb.New(seedTime.Add(-(time.Minute * 16))),
					}, nil)
				},
			},
			want:    &paymentgateway.GetOrderStatusResponse{Status: rpc.StatusOk(), OrderStatus: enums.OrderStatus_ORDER_STATUS_FAILED},
			wantErr: false,
		},
		{
			name: "11. Handling when order status is PAID, mandate registration is in initiated state and status is fetched from vendor",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.GetOrderStatusRequest{RecurringPaymentId: "rp-id-2", ClientRequestId: "client-req-id-1", Vendor: commonvgpb.Vendor_RAZORPAY, DataFreshness: paymentgateway.GetOrderStatusRequest_REAL_TIME},
				setupMocks: func(md *mockDependencies) {
					rpvd2 := &recurringpayment.RecurringPaymentsVendorDetails{
						Id:                 "rpvd-id-2",
						RecurringPaymentId: "rp-id-2",
						Vendor:             commonvgpb.Vendor_RAZORPAY, // Example vendor, adjust according to your enum
						VendorCustomerId:   "vendor-customer-id-123",
					}
					rpa2 := &recurringpayment.RecurringPaymentsAction{
						Id:                 "rpa-id-2",
						RecurringPaymentId: "rp-id-2",
						ClientRequestId:    "client-req-id-1",
						Action:             recurringpayment.Action_CREATE,
					}
					rp2 := &recurringpayment.RecurringPayment{
						Id:          "rp-id-2",
						FromActorId: "actor-id-123",
						ToActorId:   "actor-id-456",
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        100000, // Representing 1000.00 INR assuming the smallest unit is a paisa
						},
						Interval: &types.Interval{
							StartTime: timestampPb.Now(),
							EndTime:   timestampPb.New(timestampPb.Now().AsTime().AddDate(0, 3, 0)), // 3 months from now
						},
						State:           recurringpayment.RecurringPaymentState_ACTIVATED, // Assuming an Active state enum
						EntityOwnership: commontypes.Ownership_EPIFI_TECH,
						ExternalId:      "external-id-123",
						PiTo:            "paymentinstrument-creditcard-federal-pool-account-1",
						Type:            recurringpayment.RecurringPaymentType_UPI_MANDATES,
					}
					md.orderClient.EXPECT().GetOrdersWithTransactions(gomock.Any(), &order.GetOrdersWithTransactionsRequest{
						OrderIdentifiers: []*order.OrderIdentifier{
							{
								Identifier: &order.OrderIdentifier_ClientReqId{
									ClientReqId: "client-req-id-1",
								},
							},
						},
					}).Return(&order.GetOrdersWithTransactionsResponse{Status: rpc.StatusRecordNotFound()}, nil)
					md.rpDao.EXPECT().GetById(gomock.Any(), "rp-id-2").Return(rp2, nil)
					md.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-id-1", false).Return(rpa2, nil)
					md.rpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id-2").Return(rpvd2, nil)
					md.payClient.EXPECT().GetOrderVendorOrderMap(gomock.Any(), &pay.GetOrderVendorOrderMapRequest{
						OrderId: "rp-id-2",
						Vendor:  commonvgpb.Vendor_RAZORPAY,
					}).Return(&pay.GetOrderVendorOrderMapResponse{
						Status: rpc.StatusOk(),
						VendorOrderMaps: []*order.OrderVendorOrderMap{
							{
								Id:             "ovom-id-1",
								OrderId:        "rp-id-1",
								VendorOrderId:  "order_123124124",
								Vendor:         commonvgpb.Vendor_RAZORPAY,
								OrderDirection: orderEnumsPb.OrderDirection_ORDER_DIRECTION_FORWARD,
							},
						},
					}, nil)
					md.vgPgClient.EXPECT().FetchOrderStatus(gomock.Any(), gomock.Any()).Return(&pg.FetchOrderStatusResponse{
						Status:      rpc.StatusOk(),
						OrderStatus: pg.VendorOrderStatus_VENDOR_ORDER_STATUS_PAID,
						CreatedAt:   timestampPb.New(seedTime),
					}, nil)
					md.vgPgClient.EXPECT().FetchPaymentsForOrderId(gomock.Any(), gomock.Any()).Return(&pg.FetchPaymentsForOrderIdResponse{
						Status:        rpc.StatusOk(),
						PaymentsCount: 1,
						Payments: []*pg.Payment{{
							VendorPaymentId:           "payment_12345",
							Method:                    pg.PaymentMethod_PAYMENT_METHOD_UPI,
							Status:                    pg.VendorPaymentStatus_VENDOR_PAYMENT_STATUS_CAPTURED,
							IsPaymentCapturedByVendor: true,
							UpiPaymentDetails: &pg.UpiPaymentDetails{
								UpiPaymentType: pg.UPIPaymentType_UPI_PAYMENT_TYPE_ACCOUNT,
								Vpa:            "abc@upi",
							},
						}},
					}, nil)
					md.vgPgClient.EXPECT().FetchToken(gomock.Any(), gomock.Any()).Return(&pg.FetchTokenResponse{
						Status:  rpc.StatusOk(),
						TokenId: "token-id-1",
						TokenDetails: &pg.TokenDetails{
							Id:        "token-id-1",
							Entity:    "token",
							Recurring: true,
							RecurringDetails: &pg.MandateDetails{
								Status:        pg.MandateStatus_MANDATE_STATUS_INITIATED,
								FailureReason: "",
							},
						},
					}, nil)
					md.userProcessorFactory.EXPECT().GetUserProcessor(gomock.Any(), "actor-id-123", commontypes.Ownership_EPIFI_TECH).Return(md.defaultUserProcessor)
					md.defaultUserProcessor.EXPECT().GetUserFromActorId(gomock.Any(), "actor-id-123", commontypes.Ownership_EPIFI_TECH).Return(userDetails, nil)
					hash, _ := constructUniqueHashedAccountNumber("abc@upi")
					md.piClient.EXPECT().CreatePi(gomock.Any(), &paymentinstrument.CreatePiRequest{
						Type:  paymentinstrument.PaymentInstrumentType_UPI,
						State: paymentinstrument.PaymentInstrumentState_CREATED,
						Identifier: &paymentinstrument.CreatePiRequest_Upi{Upi: &paymentinstrument.Upi{
							Vpa:                    "abc@upi",
							AccountReferenceNumber: hash,
							MaskedAccountNumber:    mask.MaskLastNDigits(hash, 3, ""),
							IfscCode:               payPkg.DefaultIfscCardTxn,
							AccountType:            accounts.Type_SAVINGS,
							MerchantDetails:        nil,
							Name:                   "Lorem Ipsum",
							VpaType:                paymentinstrument.Upi_VPA_TYPE_UNSPECIFIED,
						}},
						VerifiedName:         "Lorem Ipsum",
						IssuerClassification: paymentinstrument.PaymentInstrumentIssuer_EXTERNAL,
						Capabilities: map[string]bool{
							paymentinstrument.Capability_INBOUND_TXN.String():  true,
							paymentinstrument.Capability_OUTBOUND_TXN.String(): true,
						}}).Return(&paymentinstrument.CreatePiResponse{
						PaymentInstrument: &paymentinstrument.PaymentInstrument{
							Id: "paymentinstrument-creditcard-federal-pool-account-1",
						},
						Status: rpc.StatusOk(),
					}, nil)
					md.rpDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), rp2, []recurringpayment.RecurringPaymentFieldMask{
						recurringpayment.RecurringPaymentFieldMask_PI_FROM,
					}, recurringpayment.RecurringPaymentState_ACTIVATED, recurringpayment.RecurringPaymentState_ACTIVATED).Return(nil)
					md.rpvdDao.EXPECT().Update(gomock.Any(), rpvd2, []recurringpayment.RecurringPaymentVendorDetailsFieldMask{
						recurringpayment.RecurringPaymentVendorDetailsFieldMask_RECURRING_PAYMENT_VENDOR_DETAILS_FIELD_MASK_VENDOR_PAYMENT_ID,
					}).Return(nil)
				},
			},
			want:    &paymentgateway.GetOrderStatusResponse{Status: rpc.StatusOk(), OrderStatus: enums.OrderStatus_ORDER_STATUS_PAID, DetailedStatus: &recurringpayment.ActionDetailedStatusInfo{FiStatusCode: pgerrorcodes.PaymentGatewayInProgressFiStatusCode}},
			wantErr: false,
		},
		{
			name: "12. Handling when order status is PAID, mandate registration is failed and status is fetched from vendor",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.GetOrderStatusRequest{RecurringPaymentId: "rp-id-2", ClientRequestId: "client-req-id-1", Vendor: commonvgpb.Vendor_RAZORPAY, DataFreshness: paymentgateway.GetOrderStatusRequest_REAL_TIME},
				setupMocks: func(md *mockDependencies) {
					rpvd2 := &recurringpayment.RecurringPaymentsVendorDetails{
						Id:                 "rpvd-id-2",
						RecurringPaymentId: "rp-id-2",
						Vendor:             commonvgpb.Vendor_RAZORPAY, // Example vendor, adjust according to your enum
						VendorCustomerId:   "vendor-customer-id-123",
					}
					rpa2 := &recurringpayment.RecurringPaymentsAction{
						Id:                 "rpa-id-2",
						RecurringPaymentId: "rp-id-2",
						ClientRequestId:    "client-req-id-1",
						Action:             recurringpayment.Action_CREATE,
					}
					rp2 := &recurringpayment.RecurringPayment{
						Id:          "rp-id-2",
						FromActorId: "actor-id-123",
						ToActorId:   "actor-id-456",
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        100000, // Representing 1000.00 INR assuming the smallest unit is a paisa
						},
						Interval: &types.Interval{
							StartTime: timestampPb.Now(),
							EndTime:   timestampPb.New(timestampPb.Now().AsTime().AddDate(0, 3, 0)), // 3 months from now
						},
						State:           recurringpayment.RecurringPaymentState_ACTIVATED, // Assuming an Active state enum
						EntityOwnership: commontypes.Ownership_EPIFI_TECH,
						ExternalId:      "external-id-123",
						PiTo:            "paymentinstrument-creditcard-federal-pool-account-1",
						Type:            recurringpayment.RecurringPaymentType_UPI_MANDATES,
					}
					md.orderClient.EXPECT().GetOrdersWithTransactions(gomock.Any(), &order.GetOrdersWithTransactionsRequest{
						OrderIdentifiers: []*order.OrderIdentifier{
							{
								Identifier: &order.OrderIdentifier_ClientReqId{
									ClientReqId: "client-req-id-1",
								},
							},
						},
					}).Return(&order.GetOrdersWithTransactionsResponse{Status: rpc.StatusRecordNotFound()}, nil)
					md.rpDao.EXPECT().GetById(gomock.Any(), "rp-id-2").Return(rp2, nil)
					md.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-id-1", false).Return(rpa2, nil)
					md.rpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id-2").Return(rpvd2, nil)
					md.payClient.EXPECT().GetOrderVendorOrderMap(gomock.Any(), &pay.GetOrderVendorOrderMapRequest{
						OrderId: "rp-id-2",
						Vendor:  commonvgpb.Vendor_RAZORPAY,
					}).Return(&pay.GetOrderVendorOrderMapResponse{
						Status: rpc.StatusOk(),
						VendorOrderMaps: []*order.OrderVendorOrderMap{
							{
								Id:             "ovom-id-1",
								OrderId:        "rp-id-1",
								VendorOrderId:  "order_123124124",
								Vendor:         commonvgpb.Vendor_RAZORPAY,
								OrderDirection: orderEnumsPb.OrderDirection_ORDER_DIRECTION_FORWARD,
							},
						},
					}, nil)
					md.vgPgClient.EXPECT().FetchOrderStatus(gomock.Any(), gomock.Any()).Return(&pg.FetchOrderStatusResponse{
						Status:      rpc.StatusOk(),
						OrderStatus: pg.VendorOrderStatus_VENDOR_ORDER_STATUS_PAID,
						CreatedAt:   timestampPb.New(seedTime),
					}, nil)
					md.vgPgClient.EXPECT().FetchPaymentsForOrderId(gomock.Any(), gomock.Any()).Return(&pg.FetchPaymentsForOrderIdResponse{
						Status:        rpc.StatusOk(),
						PaymentsCount: 1,
						Payments: []*pg.Payment{{
							VendorPaymentId:           "payment_12345",
							Method:                    pg.PaymentMethod_PAYMENT_METHOD_UPI,
							Status:                    pg.VendorPaymentStatus_VENDOR_PAYMENT_STATUS_CAPTURED,
							IsPaymentCapturedByVendor: true,
							UpiPaymentDetails: &pg.UpiPaymentDetails{
								UpiPaymentType: pg.UPIPaymentType_UPI_PAYMENT_TYPE_ACCOUNT,
								Vpa:            "abc@upi",
							},
						}},
					}, nil)
					md.vgPgClient.EXPECT().FetchToken(gomock.Any(), gomock.Any()).Return(&pg.FetchTokenResponse{
						Status:  rpc.StatusOk(),
						TokenId: "token-id-1",
						TokenDetails: &pg.TokenDetails{
							Id:        "token-id-1",
							Entity:    "token",
							Recurring: true,
							RecurringDetails: &pg.MandateDetails{
								Status:        pg.MandateStatus_MANDATE_STATUS_REJECTED,
								FailureReason: "rejected_by_bank",
							},
						},
					}, nil)
					md.userProcessorFactory.EXPECT().GetUserProcessor(gomock.Any(), "actor-id-123", commontypes.Ownership_EPIFI_TECH).Return(md.defaultUserProcessor)
					md.defaultUserProcessor.EXPECT().GetUserFromActorId(gomock.Any(), "actor-id-123", commontypes.Ownership_EPIFI_TECH).Return(userDetails, nil)
					hash, _ := constructUniqueHashedAccountNumber("abc@upi")
					md.piClient.EXPECT().CreatePi(gomock.Any(), &paymentinstrument.CreatePiRequest{
						Type:  paymentinstrument.PaymentInstrumentType_UPI,
						State: paymentinstrument.PaymentInstrumentState_CREATED,
						Identifier: &paymentinstrument.CreatePiRequest_Upi{Upi: &paymentinstrument.Upi{
							Vpa:                    "abc@upi",
							AccountReferenceNumber: hash,
							MaskedAccountNumber:    mask.MaskLastNDigits(hash, 3, ""),
							IfscCode:               payPkg.DefaultIfscCardTxn,
							AccountType:            accounts.Type_SAVINGS,
							MerchantDetails:        nil,
							Name:                   "Lorem Ipsum",
							VpaType:                paymentinstrument.Upi_VPA_TYPE_UNSPECIFIED,
						}},
						VerifiedName:         "Lorem Ipsum",
						IssuerClassification: paymentinstrument.PaymentInstrumentIssuer_EXTERNAL,
						Capabilities: map[string]bool{
							paymentinstrument.Capability_INBOUND_TXN.String():  true,
							paymentinstrument.Capability_OUTBOUND_TXN.String(): true,
						}}).Return(&paymentinstrument.CreatePiResponse{
						PaymentInstrument: &paymentinstrument.PaymentInstrument{
							Id: "paymentinstrument-creditcard-federal-pool-account-1",
						},
						Status: rpc.StatusOk(),
					}, nil)
					md.rpDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), rp2, []recurringpayment.RecurringPaymentFieldMask{
						recurringpayment.RecurringPaymentFieldMask_PI_FROM,
					}, recurringpayment.RecurringPaymentState_ACTIVATED, recurringpayment.RecurringPaymentState_ACTIVATED).Return(nil)
					md.rpvdDao.EXPECT().Update(gomock.Any(), rpvd2, []recurringpayment.RecurringPaymentVendorDetailsFieldMask{
						recurringpayment.RecurringPaymentVendorDetailsFieldMask_RECURRING_PAYMENT_VENDOR_DETAILS_FIELD_MASK_VENDOR_PAYMENT_ID,
					}).Return(nil)
				},
			},
			want:    &paymentgateway.GetOrderStatusResponse{Status: rpc.StatusOk(), OrderStatus: enums.OrderStatus_ORDER_STATUS_PAID, DetailedStatus: &recurringpayment.ActionDetailedStatusInfo{FiStatusCode: "RZP1000", ErrorDescription: "Mandate registration failed due to rejection by bank"}},
			wantErr: false,
		},
		{
			name: "13. Handling when order status is PAID, mandate registration is in-progress and status fetching is being re-tried, status should be fetched from vendor",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.GetOrderStatusRequest{RecurringPaymentId: "rp-id-2", ClientRequestId: "client-req-id-1", Vendor: commonvgpb.Vendor_RAZORPAY, DataFreshness: paymentgateway.GetOrderStatusRequest_REAL_TIME},
				setupMocks: func(md *mockDependencies) {
					rpvd2 := &recurringpayment.RecurringPaymentsVendorDetails{
						Id:                 "rpvd-id-2",
						RecurringPaymentId: "rp-id-2",
						Vendor:             commonvgpb.Vendor_RAZORPAY, // Example vendor, adjust according to your enum
						VendorCustomerId:   "vendor-customer-id-123",
						// Populate vendor payment Id, to indicate that we are re-trying to fetch the status on the same Id.
						VendorPaymentId: "payment_12345",
					}
					rpa2 := &recurringpayment.RecurringPaymentsAction{
						Id:                 "rpa-id-2",
						RecurringPaymentId: "rp-id-2",
						ClientRequestId:    "client-req-id-1",
						Action:             recurringpayment.Action_CREATE,
					}
					rp2 := &recurringpayment.RecurringPayment{
						Id:          "rp-id-2",
						FromActorId: "actor-id-123",
						ToActorId:   "actor-id-456",
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        100000, // Representing 1000.00 INR assuming the smallest unit is a paisa
						},
						Interval: &types.Interval{
							StartTime: timestampPb.Now(),
							EndTime:   timestampPb.New(timestampPb.Now().AsTime().AddDate(0, 3, 0)), // 3 months from now
						},
						State:           recurringpayment.RecurringPaymentState_ACTIVATED, // Assuming an Active state enum
						EntityOwnership: commontypes.Ownership_EPIFI_TECH,
						ExternalId:      "external-id-123",
						PiTo:            "paymentinstrument-creditcard-federal-pool-account-1",
						Type:            recurringpayment.RecurringPaymentType_UPI_MANDATES,
					}
					md.orderClient.EXPECT().GetOrdersWithTransactions(gomock.Any(), &order.GetOrdersWithTransactionsRequest{
						OrderIdentifiers: []*order.OrderIdentifier{
							{
								Identifier: &order.OrderIdentifier_ClientReqId{
									ClientReqId: "client-req-id-1",
								},
							},
						},
					}).Return(&order.GetOrdersWithTransactionsResponse{Status: rpc.StatusRecordNotFound()}, nil)
					md.rpDao.EXPECT().GetById(gomock.Any(), "rp-id-2").Return(rp2, nil)
					md.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-id-1", false).Return(rpa2, nil)
					md.rpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id-2").Return(rpvd2, nil)
					md.payClient.EXPECT().GetOrderVendorOrderMap(gomock.Any(), &pay.GetOrderVendorOrderMapRequest{
						OrderId: "rp-id-2",
						Vendor:  commonvgpb.Vendor_RAZORPAY,
					}).Return(&pay.GetOrderVendorOrderMapResponse{
						Status: rpc.StatusOk(),
						VendorOrderMaps: []*order.OrderVendorOrderMap{
							{
								Id:             "ovom-id-1",
								OrderId:        "rp-id-1",
								VendorOrderId:  "order_123124124",
								Vendor:         commonvgpb.Vendor_RAZORPAY,
								OrderDirection: orderEnumsPb.OrderDirection_ORDER_DIRECTION_FORWARD,
							},
						},
					}, nil)
					md.vgPgClient.EXPECT().FetchOrderStatus(gomock.Any(), gomock.Any()).Return(&pg.FetchOrderStatusResponse{
						Status:      rpc.StatusOk(),
						OrderStatus: pg.VendorOrderStatus_VENDOR_ORDER_STATUS_PAID,
						CreatedAt:   timestampPb.New(seedTime),
					}, nil)
					md.vgPgClient.EXPECT().FetchPaymentsForOrderId(gomock.Any(), gomock.Any()).Return(&pg.FetchPaymentsForOrderIdResponse{
						Status:        rpc.StatusOk(),
						PaymentsCount: 1,
						Payments: []*pg.Payment{{
							VendorPaymentId:           "payment_12345",
							Method:                    pg.PaymentMethod_PAYMENT_METHOD_UPI,
							Status:                    pg.VendorPaymentStatus_VENDOR_PAYMENT_STATUS_CAPTURED,
							IsPaymentCapturedByVendor: true,
							UpiPaymentDetails: &pg.UpiPaymentDetails{
								UpiPaymentType: pg.UPIPaymentType_UPI_PAYMENT_TYPE_ACCOUNT,
								Vpa:            "abc@upi",
							},
						}},
					}, nil)
					md.vgPgClient.EXPECT().FetchToken(gomock.Any(), gomock.Any()).Return(&pg.FetchTokenResponse{
						Status:  rpc.StatusOk(),
						TokenId: "token-id-1",
						TokenDetails: &pg.TokenDetails{
							Id:        "token-id-1",
							Entity:    "token",
							Recurring: true,
							RecurringDetails: &pg.MandateDetails{
								Status:        pg.MandateStatus_MANDATE_STATUS_INITIATED,
								FailureReason: "",
							},
						},
					}, nil)
					md.userProcessorFactory.EXPECT().GetUserProcessor(gomock.Any(), "actor-id-123", commontypes.Ownership_EPIFI_TECH).Return(md.defaultUserProcessor)
					md.defaultUserProcessor.EXPECT().GetUserFromActorId(gomock.Any(), "actor-id-123", commontypes.Ownership_EPIFI_TECH).Return(userDetails, nil)
					hash, _ := constructUniqueHashedAccountNumber("abc@upi")
					md.piClient.EXPECT().CreatePi(gomock.Any(), &paymentinstrument.CreatePiRequest{
						Type:  paymentinstrument.PaymentInstrumentType_UPI,
						State: paymentinstrument.PaymentInstrumentState_CREATED,
						Identifier: &paymentinstrument.CreatePiRequest_Upi{Upi: &paymentinstrument.Upi{
							Vpa:                    "abc@upi",
							AccountReferenceNumber: hash,
							MaskedAccountNumber:    mask.MaskLastNDigits(hash, 3, ""),
							IfscCode:               payPkg.DefaultIfscCardTxn,
							AccountType:            accounts.Type_SAVINGS,
							MerchantDetails:        nil,
							Name:                   "Lorem Ipsum",
							VpaType:                paymentinstrument.Upi_VPA_TYPE_UNSPECIFIED,
						}},
						VerifiedName:         "Lorem Ipsum",
						IssuerClassification: paymentinstrument.PaymentInstrumentIssuer_EXTERNAL,
						Capabilities: map[string]bool{
							paymentinstrument.Capability_INBOUND_TXN.String():  true,
							paymentinstrument.Capability_OUTBOUND_TXN.String(): true,
						}}).Return(&paymentinstrument.CreatePiResponse{
						PaymentInstrument: &paymentinstrument.PaymentInstrument{
							Id: "paymentinstrument-creditcard-federal-pool-account-1",
						},
						Status: rpc.StatusOk(),
					}, nil)
					md.rpDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), rp2, []recurringpayment.RecurringPaymentFieldMask{
						recurringpayment.RecurringPaymentFieldMask_PI_FROM,
					}, recurringpayment.RecurringPaymentState_ACTIVATED, recurringpayment.RecurringPaymentState_ACTIVATED).Return(nil)
					md.rpvdDao.EXPECT().Update(gomock.Any(), rpvd2, []recurringpayment.RecurringPaymentVendorDetailsFieldMask{
						recurringpayment.RecurringPaymentVendorDetailsFieldMask_RECURRING_PAYMENT_VENDOR_DETAILS_FIELD_MASK_VENDOR_PAYMENT_ID,
					}).Return(nil)
				},
			},
			want:    &paymentgateway.GetOrderStatusResponse{Status: rpc.StatusOk(), OrderStatus: enums.OrderStatus_ORDER_STATUS_PAID, DetailedStatus: &recurringpayment.ActionDetailedStatusInfo{FiStatusCode: pgerrorcodes.PaymentGatewayInProgressFiStatusCode}},
			wantErr: false,
		},
		{
			name: "14. It should return internal error when domain entity creation fails for the given recurring payment",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.GetOrderStatusRequest{RecurringPaymentId: "rp-id-2", ClientRequestId: "client-req-id-1", Vendor: commonvgpb.Vendor_RAZORPAY, DataFreshness: paymentgateway.GetOrderStatusRequest_REAL_TIME},
				setupMocks: func(md *mockDependencies) {
					rpa2 := &recurringpayment.RecurringPaymentsAction{
						Id:                 "rpa-id-2",
						RecurringPaymentId: "rp-id-2",
						ClientRequestId:    "client-req-id-1",
						Action:             recurringpayment.Action_CREATE,
					}
					rp3 := &recurringpayment.RecurringPayment{
						Id:          "rp-id-2",
						FromActorId: "actor-id-123",
						ToActorId:   "actor-id-456",
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        100000, // Representing 1000.00 INR assuming the smallest unit is a paisa
						},
						Interval: &types.Interval{
							StartTime: timestampPb.Now(),
							EndTime:   timestampPb.New(timestampPb.Now().AsTime().AddDate(0, 3, 0)), // 3 months from now
						},
						State:           recurringpayment.RecurringPaymentState_ACTIVATED, // Assuming an Active state enum
						EntityOwnership: commontypes.Ownership_EPIFI_TECH,
						ExternalId:      "external-id-123",
						PiTo:            "paymentinstrument-creditcard-federal-pool-account-1",
						Type:            recurringpayment.RecurringPaymentType_ENACH_MANDATES,
					}
					md.orderClient.EXPECT().GetOrdersWithTransactions(gomock.Any(), &order.GetOrdersWithTransactionsRequest{
						OrderIdentifiers: []*order.OrderIdentifier{
							{
								Identifier: &order.OrderIdentifier_ClientReqId{
									ClientReqId: "client-req-id-1",
								},
							},
						},
					}).Return(&order.GetOrdersWithTransactionsResponse{Status: rpc.StatusRecordNotFound()}, nil)
					md.rpDao.EXPECT().GetById(gomock.Any(), "rp-id-2").Return(rp3, nil)
					md.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-id-1", false).Return(rpa2, nil)
					md.rpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id-2").Return(rpvd2, nil)
					md.payClient.EXPECT().GetOrderVendorOrderMap(gomock.Any(), &pay.GetOrderVendorOrderMapRequest{
						OrderId: "rp-id-2",
						Vendor:  commonvgpb.Vendor_RAZORPAY,
					}).Return(&pay.GetOrderVendorOrderMapResponse{
						Status: rpc.StatusOk(),
						VendorOrderMaps: []*order.OrderVendorOrderMap{
							{
								Id:             "ovom-id-1",
								OrderId:        "rp-id-1",
								VendorOrderId:  "order_123124124",
								Vendor:         commonvgpb.Vendor_RAZORPAY,
								OrderDirection: orderEnumsPb.OrderDirection_ORDER_DIRECTION_FORWARD,
							},
						},
					}, nil)
					md.vgPgClient.EXPECT().FetchOrderStatus(gomock.Any(), gomock.Any()).Return(&pg.FetchOrderStatusResponse{
						Status:      rpc.StatusOk(),
						OrderStatus: pg.VendorOrderStatus_VENDOR_ORDER_STATUS_PAID,
						CreatedAt:   timestampPb.New(seedTime),
					}, nil)
					md.vgPgClient.EXPECT().FetchPaymentsForOrderId(gomock.Any(), gomock.Any()).Return(&pg.FetchPaymentsForOrderIdResponse{
						Status:        rpc.StatusOk(),
						PaymentsCount: 1,
						Payments: []*pg.Payment{{
							VendorPaymentId:           "payment_12345",
							Method:                    pg.PaymentMethod_PAYMENT_METHOD_NETBANKING,
							Status:                    pg.VendorPaymentStatus_VENDOR_PAYMENT_STATUS_CAPTURED,
							IsPaymentCapturedByVendor: true,
							PayerContactInfo: &pg.PayerContactInfo{
								Email:  "<EMAIL>",
								Number: "123",
							},
							AcquirerDetails: &pg.AcquirerDetails{
								BankTransactionId: "bank-txn-id-1",
							},
						}},
					}, nil)
					md.vgPgClient.EXPECT().FetchToken(gomock.Any(), gomock.Any()).Return(&pg.FetchTokenResponse{
						Status:  rpc.StatusOk(),
						TokenId: "token-id-1",
						TokenDetails: &pg.TokenDetails{
							Id:        "token-id-1",
							Entity:    "token",
							Recurring: true,
							RecurringDetails: &pg.MandateDetails{
								Status:        pg.MandateStatus_MANDATE_STATUS_CONFIRMED,
								FailureReason: "",
							},
							AuthTypeV2: pg.AuthType_AUTH_TYPE_NET_BANKING,
							Mrn:        "mrn-1",
						},
					}, nil)
					md.userProcessorFactory.EXPECT().GetUserProcessor(gomock.Any(), "actor-id-123", commontypes.Ownership_EPIFI_TECH).Return(md.defaultUserProcessor)
					md.defaultUserProcessor.EXPECT().GetUserFromActorId(gomock.Any(), "actor-id-123", commontypes.Ownership_EPIFI_TECH).Return(userDetails, nil)
					hash, _ := constructUniqueHashedAccountNumber("<EMAIL>", "123")
					md.piClient.EXPECT().CreatePi(gomock.Any(), &paymentinstrument.CreatePiRequest{
						Type:  paymentinstrument.PaymentInstrumentType_GENERIC,
						State: paymentinstrument.PaymentInstrumentState_CREATED,
						Identifier: &paymentinstrument.CreatePiRequest_Account{Account: &paymentinstrument.Account{
							ActualAccountNumber: hash,
							SecureAccountNumber: mask.MaskLastNDigits(hash, 3, ""),
							IfscCode:            payPkg.DefaultIfscCardTxn,
							AccountType:         accounts.Type_SAVINGS,
							Name:                "Lorem Ipsum",
						}},
						IssuerClassification: paymentinstrument.PaymentInstrumentIssuer_EXTERNAL,
						Capabilities: map[string]bool{
							paymentinstrument.Capability_INBOUND_TXN.String():  true,
							paymentinstrument.Capability_OUTBOUND_TXN.String(): true,
						}}).Return(&paymentinstrument.CreatePiResponse{
						PaymentInstrument: &paymentinstrument.PaymentInstrument{
							Id: "paymentinstrument-creditcard-federal-pool-account-1",
						},
						Status: rpc.StatusOk(),
					}, nil)
					md.rpDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), rp3, []recurringpayment.RecurringPaymentFieldMask{
						recurringpayment.RecurringPaymentFieldMask_PI_FROM,
					}, recurringpayment.RecurringPaymentState_ACTIVATED, recurringpayment.RecurringPaymentState_ACTIVATED).Return(nil)
					md.rpvdDao.EXPECT().Update(gomock.Any(), rpvd2, []recurringpayment.RecurringPaymentVendorDetailsFieldMask{
						recurringpayment.RecurringPaymentVendorDetailsFieldMask_RECURRING_PAYMENT_VENDOR_DETAILS_FIELD_MASK_VENDOR_PAYMENT_ID,
					}).Return(nil)
					md.enachClient.EXPECT().CreateOffAppEnachMandate(gomock.Any(), &enachPb.CreateOffAppEnachMandateRequest{
						Umrn:                 "mrn-1",
						RecurringPaymentId:   "rp-id-2",
						Provenance:           enachEnumsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
						Vendor:               commonvgpb.Vendor_RAZORPAY,
						Ownership:            commontypes.Ownership_EPIFI_TECH,
						RegistrationAuthMode: enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
					}).Return(&enachPb.CreateOffAppEnachMandateResponse{Status: rpc.StatusInternal()}, nil)
				},
			},
			want:    &paymentgateway.GetOrderStatusResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md := getServiceWithMocks(t)
			tt.args.setupMocks(md)
			got, err := s.GetOrderStatus(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOrderStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetOrderStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetMandateStatus(t *testing.T) {
	type args struct {
		ctx        context.Context
		req        *paymentgateway.GetMandateStatusRequest
		setupMocks func(md *mockDependencies)
	}
	tests := []struct {
		name    string
		args    args
		want    *paymentgateway.GetMandateStatusResponse
		wantErr bool
	}{
		{
			name: "should return confirmed mandate status when the mandate is confirmed on vendor side",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.GetMandateStatusRequest{
					RecurringPaymentId: rp1.GetId(),
				},
				setupMocks: func(md *mockDependencies) {
					md.rpDao.EXPECT().GetById(gomock.Any(), rp1.GetId()).Return(rp1, nil)
					md.rpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), rp1.GetId()).Return(rpvd, nil)
					md.vgPgClient.EXPECT().FetchToken(gomock.Any(), gomock.Any()).Return(&pg.FetchTokenResponse{
						Status:  rpc.StatusOk(),
						TokenId: "token-id-1",
						TokenDetails: &pg.TokenDetails{
							Id:        "token-id-1",
							Entity:    "token",
							Recurring: true,
							RecurringDetails: &pg.MandateDetails{
								Status:        pg.MandateStatus_MANDATE_STATUS_CONFIRMED,
								FailureReason: "",
							},
							AuthTypeV2: pg.AuthType_AUTH_TYPE_DEBIT_CARD,
							Mrn:        "mrn-1",
						},
					}, nil)
				},
			},
			want: &paymentgateway.GetMandateStatusResponse{
				Status:        rpc.StatusOk(),
				TokenId:       "token-id-1",
				Mrn:           "mrn-1",
				MandateStatus: paymentgateway.GetMandateStatusResponse_MANDATE_STATUS_CONFIRMED,
			},
			wantErr: false,
		},
		{
			name: "should return rejected mandate status when the mandate is rejected on vendor side",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.GetMandateStatusRequest{
					RecurringPaymentId: rp1.GetId(),
				},
				setupMocks: func(md *mockDependencies) {
					md.rpDao.EXPECT().GetById(gomock.Any(), rp1.GetId()).Return(rp1, nil)
					md.rpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), rp1.GetId()).Return(rpvd, nil)
					md.vgPgClient.EXPECT().FetchToken(gomock.Any(), gomock.Any()).Return(&pg.FetchTokenResponse{
						Status:  rpc.StatusOk(),
						TokenId: "token-id-1",
						TokenDetails: &pg.TokenDetails{
							Id:        "token-id-1",
							Entity:    "token",
							Recurring: true,
							RecurringDetails: &pg.MandateDetails{
								Status:        pg.MandateStatus_MANDATE_STATUS_REJECTED,
								FailureReason: "",
							},
							AuthTypeV2: pg.AuthType_AUTH_TYPE_DEBIT_CARD,
							Mrn:        "mrn-1",
						},
					}, nil)
				},
			},
			want: &paymentgateway.GetMandateStatusResponse{
				Status:        rpc.StatusOk(),
				TokenId:       "token-id-1",
				Mrn:           "mrn-1",
				MandateStatus: paymentgateway.GetMandateStatusResponse_MANDATE_STATUS_REJECTED,
			},
			wantErr: false,
		},
		{
			name: "should return unspecified mandate status when the mandate status is returned as unspecified from vendor",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.GetMandateStatusRequest{
					RecurringPaymentId: rp1.GetId(),
				},
				setupMocks: func(md *mockDependencies) {
					md.rpDao.EXPECT().GetById(gomock.Any(), rp1.GetId()).Return(rp1, nil)
					md.rpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), rp1.GetId()).Return(rpvd, nil)
					md.vgPgClient.EXPECT().FetchToken(gomock.Any(), gomock.Any()).Return(&pg.FetchTokenResponse{
						Status:  rpc.StatusOk(),
						TokenId: "token-id-1",
						TokenDetails: &pg.TokenDetails{
							Id:        "token-id-1",
							Entity:    "token",
							Recurring: true,
							RecurringDetails: &pg.MandateDetails{
								Status:        pg.MandateStatus_MANDATE_STATUS_UNSPECIFIED,
								FailureReason: "",
							},
							AuthTypeV2: pg.AuthType_AUTH_TYPE_DEBIT_CARD,
							Mrn:        "mrn-1",
						},
					}, nil)
				},
			},
			want: &paymentgateway.GetMandateStatusResponse{
				Status:        rpc.StatusOk(),
				TokenId:       "token-id-1",
				Mrn:           "mrn-1",
				MandateStatus: paymentgateway.GetMandateStatusResponse_MANDATE_STATUS_UNSPECIFIED,
			},
			wantErr: false,
		},
		{
			name: "should return internal status code when vendor RPC call fails",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.GetMandateStatusRequest{
					RecurringPaymentId: rp1.GetId(),
				},
				setupMocks: func(md *mockDependencies) {
					md.rpDao.EXPECT().GetById(gomock.Any(), rp1.GetId()).Return(rp1, nil)
					md.rpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), rp1.GetId()).Return(rpvd, nil)
					md.vgPgClient.EXPECT().FetchToken(gomock.Any(), gomock.Any()).Return(&pg.FetchTokenResponse{
						Status: rpc.StatusInternal(),
					}, nil)
				},
			},
			want: &paymentgateway.GetMandateStatusResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "should return internal status code when no mandate is found for the given recurring payment id",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.GetMandateStatusRequest{
					RecurringPaymentId: rp1.GetId(),
				},
				setupMocks: func(md *mockDependencies) {
					md.rpDao.EXPECT().GetById(gomock.Any(), rp1.GetId()).Return(nil, epifierrors.ErrRecordNotFound)
				},
			},
			want: &paymentgateway.GetMandateStatusResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "should return internal status code when recurring payment vendor details don't exist for the given recurring payment ID",
			args: args{
				ctx: context.Background(),
				req: &paymentgateway.GetMandateStatusRequest{
					RecurringPaymentId: rp1.GetId(),
				},
				setupMocks: func(md *mockDependencies) {
					md.rpDao.EXPECT().GetById(gomock.Any(), rp1.GetId()).Return(rp1, nil)
					md.rpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), rp1.GetId()).Return(nil, epifierrors.ErrRecordNotFound)
				},
			},
			want: &paymentgateway.GetMandateStatusResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			s, md := getServiceWithMocks(t)
			tc.args.setupMocks(md)
			got, err := s.GetMandateStatus(tc.args.ctx, tc.args.req)
			if (err != nil) != tc.wantErr {
				t.Errorf("GetMandateStatus() error = %v, wantErr %v", err, tc.wantErr)
				return
			}
			if !proto.Equal(got, tc.want) {
				t.Errorf("GetMandateStatus() got = %v, want %v", got, tc.want)
			}
		})
	}
}
