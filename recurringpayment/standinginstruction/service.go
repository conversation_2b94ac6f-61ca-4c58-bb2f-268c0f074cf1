package standinginstruction

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"time"

	bankCustPb "github.com/epifi/gamma/api/bankcust"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	accountPb "github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	domainPb "github.com/epifi/gamma/api/order/domain"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	pb "github.com/epifi/gamma/api/recurringpayment"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	savingsPb "github.com/epifi/gamma/api/savings"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	vgPaymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	siVg "github.com/epifi/gamma/api/vendorgateway/openbanking/standinginstruction"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/pkg/recurringpayment"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/recurringpayment/dao"
	"github.com/epifi/gamma/recurringpayment/internal"
)

var (
	allowedFrequencyToRecurrencePatternMap = map[pb.AllowedFrequency]siVg.RecurrencePattern{
		pb.AllowedFrequency_DAILY:        siVg.RecurrencePattern_DAILY,
		pb.AllowedFrequency_MONTHLY:      siVg.RecurrencePattern_MONTHLY,
		pb.AllowedFrequency_WEEKLY:       siVg.RecurrencePattern_WEEKLY,
		pb.AllowedFrequency_AS_PRESENTED: siVg.RecurrencePattern_AS_PRESENTED,
	}
)

type Service struct {
	siPb.UnimplementedStandingInstructionServiceServer
	standingInstructionDao        dao.StandingInstructionDao
	standingInstructionRequestDao dao.StandingInstructionRequestDao
	siVGClient                    siVg.StandingInstructionClient
	piClient                      piPb.PiClient
	savingsClient                 savingsPb.SavingsClient
	authClient                    authPb.AuthClient
	recurringPaymentDao           dao.RecurringPaymentDao
	userClient                    userPb.UsersClient
	actorClient                   actorPb.ActorClient
	vgPaymentClient               vgPaymentPb.PaymentClient
	bankCustClient                bankCustPb.BankCustomerServiceClient
}

func NewService(standingInstructionDao dao.StandingInstructionDao, siVGClient siVg.StandingInstructionClient, piClient piPb.PiClient,
	savingsClient savingsPb.SavingsClient, authClient authPb.AuthClient, recurringPaymentDao dao.RecurringPaymentDao,
	userClient userPb.UsersClient, actorClient actorPb.ActorClient, standingInstructionRequestDao dao.StandingInstructionRequestDao,
	vgPaymentClient vgPaymentPb.PaymentClient, bcClient bankCustPb.BankCustomerServiceClient) *Service {
	return &Service{
		standingInstructionDao:        standingInstructionDao,
		standingInstructionRequestDao: standingInstructionRequestDao,
		siVGClient:                    siVGClient,
		piClient:                      piClient,
		savingsClient:                 savingsClient,
		authClient:                    authClient,
		recurringPaymentDao:           recurringPaymentDao,
		userClient:                    userClient,
		actorClient:                   actorClient,
		vgPaymentClient:               vgPaymentClient,
		bankCustClient:                bcClient,
	}
}

func (s *Service) Create(ctx context.Context, req *siPb.CreateRequest) (*siPb.CreateResponse, error) {
	var (
		res = &siPb.CreateResponse{}
	)
	_, err := s.standingInstructionDao.GetByRecurringPaymentId(ctx, req.GetRecurringPaymentId())
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrRecordNotFound):
		txnErr := storageV2.RunCRDBTxn(ctx, func(txnCtx context.Context) error {
			standingInstruction, createErr := s.standingInstructionDao.Create(txnCtx, &siPb.StandingInstruction{
				RecurringPaymentId: req.GetRecurringPaymentId(),
			})
			if createErr != nil {
				return fmt.Errorf("error while creaating standing instruction %w", err)
			}
			_, createErr = s.standingInstructionRequestDao.Create(txnCtx, &siPb.StandingInstructionRequest{
				StandingInstructionId: standingInstruction.GetId(),
				VendorRequestId:       req.GetRequestId(),
				RequestType:           siPb.RequestType_CREATE,
				State:                 siPb.State_QUEUED,
			})
			if createErr != nil {
				return fmt.Errorf("error while creaating standing instruction request %w", err)
			}
			return nil
		})
		if txnErr != nil {
			logger.Error(ctx, "failed to commit transaction for creating standing instruction",
				zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(txnErr))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		res.Status = rpc.StatusOk()
	case err != nil:
		logger.Error(ctx, "error in fetching standing instruction", zap.String(logger.RECURRING_PAYMENT_ID,
			req.GetRecurringPaymentId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
	default:
		res.Status = rpc.StatusAlreadyExists()
	}
	return res, nil
}

// nolint : funlen
func (s *Service) AuthoriseCreation(ctx context.Context,
	req *siPb.AuthoriseCreationRequest) (*siPb.AuthoriseCreationResponse, error) {
	var (
		res                  = &siPb.AuthoriseCreationResponse{}
		detailState          pb.ActionDetailedStatus_DetailedStatus_State
		actionDetailedStatus *pb.ActionDetailedStatus_DetailedStatus
	)
	standingInstruction, err := s.standingInstructionDao.GetByRecurringPaymentId(ctx, req.GetRecurringPaymentId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "record not found for recurring payment id", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error while fetching recurring payment", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		logger.Debug(ctx, "standing instruction record fetched successfully")
	}
	standingInstructionRequest, err := s.standingInstructionRequestDao.GetByStandingInstructionIdAndRequestType(ctx,
		standingInstruction.GetId(), siPb.RequestType_CREATE)
	if err != nil {
		logger.Error(ctx, "error while fetching create request",
			zap.String(logger.STANDING_INSTRUCTION_ID, standingInstruction.GetId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	if standingInstructionRequest.GetState() != siPb.State_QUEUED {
		logger.Error(ctx, "standing instruction request not in expected state for initiating creation at vendor",
			zap.String(logger.STATE, standingInstructionRequest.GetState().String()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	vgReq, err := s.constructVGRequest(ctx, req, standingInstructionRequest.GetVendorRequestId())
	if err != nil {
		logger.Error(ctx, "error while constructing standing instruction vg request", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	vgRes, err := s.siVGClient.Create(ctx, vgReq)
	switch {
	case err != nil:
		logger.Error(ctx, "failed to create SI", zap.Error(err))
		actionDetailedStatus = recurringpayment.GetActionDetailedStatusForSystemErr(err, pb.ActionDetailedStatus_DetailedStatus_CREATE_SI)

	case vgRes.GetStatus().IsUnknown():
		logger.Error(ctx, "got unexpected response while calling create SI")
		detailState = pb.ActionDetailedStatus_DetailedStatus_UNKNOWN

	case !vgRes.GetStatus().IsSuccess():
		logger.Error(ctx, "non-success state while creating SI", zap.Error(err))
		detailState = pb.ActionDetailedStatus_DetailedStatus_FAILURE

	default:
		detailState = pb.ActionDetailedStatus_DetailedStatus_SUCCESS
	}

	if actionDetailedStatus == nil {
		actionDetailedStatus = recurringpayment.GetActionDetailedStatusForAck(detailState, vgRes.GetRawStatusCode(), vgRes.GetRawStatusDescription(),
			vgRes.GetInternalStatusCode(), "", "", pb.ActionDetailedStatus_DetailedStatus_CREATE_SI)
	}
	res.ActionDetailedStatus = &pb.ActionDetailedStatus{
		DetailedStatusList: []*pb.ActionDetailedStatus_DetailedStatus{
			actionDetailedStatus,
		},
	}

	if te := epifigrpc.RPCError(vgRes, err); te != nil {
		logger.Error(ctx, "error in vendor gateway request to create standing instruction", zap.Error(te))
		err = s.updateStandingInstructionFailure(ctx, standingInstructionRequest)
		if err != nil {
			logger.Error(ctx, "error in updating standing instruction", zap.Error(err))
		}
		res.Status = rpc.StatusFromError(te)
		return res, nil
	}
	err = s.updateStandingInstructionCreationSuccess(ctx, standingInstruction, standingInstructionRequest, vgRes.GetTokenizedSiToken())
	if err != nil {
		logger.Error(ctx, "error while updating secure token", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()),
			zap.String(logger.STANDING_INSTRUCTION_ID, standingInstruction.GetId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) updateStandingInstructionCreationSuccess(ctx context.Context, standingInstruction *siPb.StandingInstruction,
	standingInstructionRequest *siPb.StandingInstructionRequest, secureToken string) error {
	standingInstruction.SecureToken = secureToken
	txnErr := storageV2.RunCRDBTxn(ctx, func(txnCtx context.Context) error {
		err := s.standingInstructionDao.Update(txnCtx, standingInstruction, []siPb.StandingInstructionFieldMask{siPb.StandingInstructionFieldMask_SECURE_TOKEN})
		if err != nil {
			return fmt.Errorf("error while updating standing instruction %w", err)
		}
		err = s.standingInstructionRequestDao.UpdateAndChangeStatus(txnCtx, standingInstructionRequest, nil,
			standingInstructionRequest.GetState(), siPb.State_SUCCESS)
		if err != nil {
			return fmt.Errorf("error while updating standing instruction request %w", err)
		}
		return nil
	})
	if txnErr != nil {
		return txnErr
	}
	return nil
}

func (s *Service) updateStandingInstructionFailure(ctx context.Context, standingInstructionRequest *siPb.StandingInstructionRequest) error {
	err := s.standingInstructionRequestDao.UpdateAndChangeStatus(ctx, standingInstructionRequest, nil,
		standingInstructionRequest.GetState(), siPb.State_FAILED)
	if err != nil {
		return fmt.Errorf("error while updating standing instruction request %w", err)
	}
	return nil
}

func (s *Service) constructVGRequest(ctx context.Context, req *siPb.AuthoriseCreationRequest, requestId string) (*siVg.CreateRequest, error) {
	remitterAccount, err := s.getRemitterAccountDetailsFromPi(ctx, req.GetPiFrom())
	if err != nil {
		return nil, fmt.Errorf("failed to get pi details for remitter: %s: %w", req.GetPiFrom(), err)
	}

	beneficiaryAccount, err := s.getAccountDetailsFromPi(ctx, req.GetPiTo())
	if err != nil {
		return nil, fmt.Errorf("account details couldn't be fetched for PI: %s : %w", req.GetPiTo(), err)
	}

	recurrencePattern, ok := allowedFrequencyToRecurrencePatternMap[req.GetAllowedFrequency()]
	if !ok {
		return nil, fmt.Errorf("invalid allowed frequency %s", req.GetAllowedFrequency().String())
	}

	deviceId, deviceToken, userProfileId, err := internal.GetDeviceAuthDetails(ctx, req.GetFromActorId(), s.authClient)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch device details %w", err)
	}
	vgSiCreateReq := &siVg.CreateRequest{
		Header: &commonvgpb.RequestHeader{Vendor: req.GetPartnerBank()},
		Auth: &header.Auth{
			DeviceId:      deviceId,
			DeviceToken:   deviceToken,
			UserProfileId: userProfileId,
			EncryptedPin:  req.GetPartnerSdkCredBlock(),
		},
		RequestId: requestId,
		TransTime: timestampPb.New(time.Now()),
		Protocol:  req.GetPreferredPaymentProtocol(),
		Remitter: &siVg.CreateRequest_AccountDetails{
			AccountDetails: remitterAccount,
		},
		Beneficiary: &siVg.CreateRequest_AccountDetails{
			AccountDetails: beneficiaryAccount,
		},
		SiParams: &siVg.SIParams{
			AmountMaxLimit:      req.GetMaximumAmountLimit(),
			ValidityStartDate:   datetime.TimeToDateInLoc(req.GetInterval().GetStartTime().AsTime(), datetime.IST),
			ValidityEndDate:     datetime.TimeToDateInLoc(req.GetInterval().GetEndTime().AsTime(), datetime.IST),
			RecurrencePattern:   recurrencePattern,
			NumberOfOccurrences: req.GetMaximumAllowedTxns(),
		},
	}
	return vgSiCreateReq, nil
}

// getAccountDetailsFromPi fetches account number, ifsc code and type of account given a PI.
// returns error in case PI is not of account type of rpc call to PI service fails
func (s *Service) getAccountDetailsFromPi(ctx context.Context, piId string) (*vgPaymentPb.Account, error) {
	pi, err := s.getPIDetails(ctx, piId)
	if err != nil {
		return nil, err
	}

	if pi.GetType() != piPb.PaymentInstrumentType_BANK_ACCOUNT {
		return nil, fmt.Errorf("payment instrument is of type: %s expected %s", pi.Type.String(),
			piPb.PaymentInstrumentType_BANK_ACCOUNT.String())
	}
	account := pi.GetAccount()
	res := &vgPaymentPb.Account{
		AccountNumber:     account.ActualAccountNumber,
		IfscCode:          account.IfscCode,
		AccountHolderName: pi.GetName(),
		AccountType:       account.AccountType,
		NotificationFlag:  vgPaymentPb.Account_NONE,
	}

	return res, nil
}

// getRemitterAccountDetailsFromPi fetches account number, ifsc code, type of account,
// phone number, email id given a PI. Returns error in case PI is not of account type
func (s *Service) getRemitterAccountDetailsFromPi(ctx context.Context, piId string) (*vgPaymentPb.Account, error) {
	pi, err := s.getPIDetails(ctx, piId)
	if err != nil {
		return nil, err
	}

	if pi.Type != piPb.PaymentInstrumentType_BANK_ACCOUNT {
		return nil, fmt.Errorf("payment instrument is of type: %s expected %s, id %s", pi.Type.String(),
			piPb.PaymentInstrumentType_BANK_ACCOUNT.String(), piId)
	}
	account := pi.GetAccount()
	res := &vgPaymentPb.Account{
		AccountNumber:     account.ActualAccountNumber,
		IfscCode:          account.IfscCode,
		AccountHolderName: pi.GetName(),
		AccountType:       account.AccountType,
		NotificationFlag:  vgPaymentPb.Account_NONE,
	}
	switch account.GetAccountType() {
	case accountPb.Type_SAVINGS:
		acctReq := &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_ExternalId{
				ExternalId: &savingsPb.BankAccountIdentifier{
					AccountNo: account.GetActualAccountNumber(),
					IfscCode:  account.GetIfscCode(),
				}},
		}
		acctRes, err := s.savingsClient.GetAccount(ctx, acctReq)
		if err != nil {
			return nil, fmt.Errorf("get account rpc call failed: %v: %w", err, domainPb.ErrRPC)
		}
		res.PhoneNumber = acctRes.GetAccount().GetPhoneNumber()
		res.Email = acctRes.GetAccount().GetEmailId()
	default:
		return nil, fmt.Errorf("invalid account type: %s : %w",
			res.AccountType.String(), domainPb.ErrPermanent)
	}
	return res, nil
}

// getPIDetails calls pi service's `GetPiByID` RPC and returns payment-instrument
func (s *Service) getPIDetails(ctx context.Context, piId string) (*piPb.PaymentInstrument, error) {
	piReq := &piPb.GetPiByIdRequest{
		Id: piId,
	}
	piRes, err := s.piClient.GetPiById(ctx, piReq)
	if te := epifigrpc.RPCError(piRes, err); te != nil {
		return nil, fmt.Errorf("error while fetching pi from id %w", te)
	}
	return piRes.PaymentInstrument, nil
}

// nolint: funlen
func (s *Service) Execute(ctx context.Context, req *siPb.ExecuteRequest) (*siPb.ExecuteResponse, error) {
	var (
		res = &siPb.ExecuteResponse{}
	)
	recurringPayment, err := s.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "record not found for recurring payment", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error while fetching recurring payment", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		logger.Debug(ctx, "recurring payment fetched successfully")
	}
	standingInstruction, err := s.standingInstructionDao.GetByRecurringPaymentId(ctx, req.GetRecurringPaymentId())
	if err != nil {
		logger.Error(ctx, "error while fetching standing instruction", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	// Get device auth details for current actor id
	deviceId, deviceToken, userProfileId, err := internal.GetDeviceAuthDetails(ctx, recurringPayment.GetFromActorId(), s.authClient)
	if err != nil {
		logger.Error(ctx, "error in fetching device details", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	entityId, phoneNumber, err := internal.GetEntityDetails(ctx, recurringPayment.GetFromActorId(), s.actorClient)
	if err != nil {
		logger.Error(ctx, "error in fetching entity details", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	customerId, err := internal.GetCustomerId(ctx, entityId, recurringPayment.GetPartnerBank(), s.bankCustClient)
	if err != nil {
		logger.Error(ctx, "error in fetching customer id", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	executeSIRes, err := s.siVGClient.Execute(ctx, &siVg.ExecuteRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: recurringPayment.GetPartnerBank(),
		},
		Auth: &header.Auth{
			DeviceId:      deviceId,
			DeviceToken:   deviceToken,
			UserProfileId: userProfileId,
			CustomerId:    customerId,
		},
		SiToken:     standingInstruction.GetSecureToken(),
		Amount:      req.GetAmount(),
		Protocol:    req.GetProtocol(),
		RequestId:   req.GetRequestId(),
		PhoneNumber: phoneNumber,
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error in calling vendor api for standing instruction execution", zap.Error(err))
		res.Status = rpc.StatusInternal()
	case executeSIRes.GetStatus().GetCode() == uint32(siVg.ExecuteResponse_AMOUNT_LIMIT_EXCEEDED):
		res.Status = rpc.NewStatusWithoutDebug(uint32(siPb.ExecuteResponse_AMOUNT_LIMIT_EXCEEDED), executeSIRes.GetStatus().GetShortMessage())
	case executeSIRes.GetStatus().GetCode() == uint32(siVg.ExecuteResponse_VALIDATION_DATE_EXPIRED):
		res.Status = rpc.NewStatusWithoutDebug(uint32(siPb.ExecuteResponse_VALIDATION_DATE_EXPIRED), executeSIRes.GetStatus().GetShortMessage())
	case executeSIRes.GetStatus().GetCode() == uint32(siVg.ExecuteResponse_TRANSACTIONS_LIMIT_EXCEEDED):
		res.Status = rpc.NewStatusWithoutDebug(uint32(siPb.ExecuteResponse_TRANSACTIONS_LIMIT_EXCEEDED), executeSIRes.GetStatus().GetShortMessage())
	case executeSIRes.GetStatus().GetCode() == uint32(siVg.ExecuteResponse_DEVICE_TEMPORARILY_DEACTIVATED):
		res.Status = rpc.NewStatusWithoutDebug(uint32(siPb.ExecuteResponse_DEVICE_TEMPORARILY_DEACTIVATED), executeSIRes.GetStatus().GetShortMessage())
	case !executeSIRes.GetStatus().IsSuccess():
		logger.Error(ctx, "non success state for standing instruction execution",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()), zap.String(logger.STATUS_CODE, executeSIRes.GetStatus().String()))
		res.Status = executeSIRes.GetStatus()
	default:
		res.Status = rpc.StatusOk()
	}
	return res, nil
}

// nolint: funlen
func (s *Service) GetExecutionStatus(ctx context.Context, req *siPb.GetExecutionStatusRequest) (*siPb.GetExecutionStatusResponse, error) {
	var (
		res = &siPb.GetExecutionStatusResponse{}
	)
	deviceId, deviceToken, userProfileId, err := internal.GetDeviceAuthDetails(ctx, req.GetActorId(), s.authClient)
	if err != nil {
		logger.Error(ctx, "error in fetching device details", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	entityId, phoneNumber, err := internal.GetEntityDetails(ctx, req.GetActorId(), s.actorClient)
	if err != nil {
		logger.Error(ctx, "error in fetching entity details", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	customerId, err := internal.GetCustomerId(ctx, entityId, req.GetPartnerBank(), s.bankCustClient)
	if err != nil {
		logger.Error(ctx, "error in fetching customer id", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	statusCheckReq := &vgPaymentPb.GetStatusRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: req.GetPartnerBank(),
		},
		Auth: &header.Auth{
			DeviceId:      deviceId,
			DeviceToken:   deviceToken,
			UserProfileId: userProfileId,
			CustomerId:    customerId,
		},
		RequestId:         idgen.FederalRandomSequence("NEOENQRQ", 5),
		Protocol:          req.GetPaymentProtocol(),
		OriginalRequestId: req.GetOriginalRequestId(),
		PhoneNumber:       phoneNumber.ToString(),
	}

	statusCheckRes, err := s.vgPaymentClient.GetStatus(ctx, statusCheckReq)
	switch {
	case err != nil:
		logger.Error(ctx, "error in fetching transaction status", zap.Error(err), zap.String(logger.REQUEST_ID, req.GetOriginalRequestId()))
		res.Status = rpc.StatusInternal()
		return res, err
	case statusCheckRes.GetStatus().IsSuccess():
		res.Utr = statusCheckRes.GetUtr()
	default:
		if statusCheckRes.GetUtr() != "" {
			res.Utr = statusCheckRes.GetUtr()
		}
		res.RawResponseCode = statusCheckRes.GetRawResponseCode()
		res.RawResponseDescription = statusCheckRes.GetRawResponseDescription()
		res.StatusCode = statusCheckRes.GetStatusCode()
		res.StatusDescriptionPayer = statusCheckRes.GetStatusDescriptionPayer()
		res.TransactionTimestamp = statusCheckRes.GetTransactionTimestamp()
		res.Status = statusCheckRes.GetStatus()
	}
	res.Status = statusCheckRes.GetStatus()
	return res, nil
}

// nolint: dupl
func (s *Service) CreateModifyAttempt(ctx context.Context, req *siPb.CreateModifyAttemptRequest) (*siPb.CreateModifyAttemptResponse, error) {
	var (
		res = &siPb.CreateModifyAttemptResponse{}
	)
	standingInstruction, err := s.standingInstructionDao.GetByRecurringPaymentId(ctx, req.GetRecurringPaymentId())
	if err != nil {
		logger.Error(ctx, "error in fetching standing instruction", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	_, err = s.standingInstructionRequestDao.GetByRequestId(ctx, req.GetTransactionId())
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrRecordNotFound):
		_, err = s.standingInstructionRequestDao.Create(ctx, &siPb.StandingInstructionRequest{
			StandingInstructionId: standingInstruction.GetId(),
			VendorRequestId:       req.GetTransactionId(),
			RequestType:           siPb.RequestType_MODIFY,
			State:                 siPb.State_QUEUED,
		})
		if err != nil {
			logger.Error(ctx, "error in creating modify request", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()),
				zap.String(logger.STANDING_INSTRUCTION_ID, standingInstruction.GetId()), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		res.Status = rpc.StatusOk()
	case err != nil:
		logger.Error(ctx, "error in fetching standing instruction request for modify",
			zap.String(logger.REQUEST_ID, req.GetTransactionId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
	default:
		res.Status = rpc.StatusAlreadyExists()
	}
	return res, nil
}

// nolint: funlen
func (s *Service) AuthoriseModify(ctx context.Context, req *siPb.AuthoriseModifyRequest) (*siPb.AuthoriseModifyResponse, error) {
	var (
		res                  = &siPb.AuthoriseModifyResponse{}
		requestStateToUpdate siPb.State
	)
	recurringPayment, err := s.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "record not found for recurring payment", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error while fetching recurring payment", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		logger.Debug(ctx, "recurring payment fetched successfully")
	}

	standingInstruction, err := s.standingInstructionDao.GetByRecurringPaymentId(ctx, req.GetRecurringPaymentId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "record not found for recurring payment id", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error while fetching recurring payment", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		logger.Debug(ctx, "standing instruction record fetched successfully")
	}
	standingInstructionRequest, err := s.standingInstructionRequestDao.GetByStandingInstructionIdAndRequestType(ctx,
		standingInstruction.GetId(), siPb.RequestType_MODIFY)
	if err != nil {
		logger.Error(ctx, "error while fetching create request",
			zap.String(logger.STANDING_INSTRUCTION_ID, standingInstruction.GetId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	if standingInstructionRequest.GetState() != siPb.State_QUEUED {
		logger.Error(ctx, "standing instruction request not in expected state for initiating modify at vendor",
			zap.String(logger.STATE, standingInstructionRequest.GetState().String()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	vgReq, err := s.constructModifyVGRequest(ctx, recurringPayment, standingInstructionRequest.GetVendorRequestId(),
		standingInstruction.GetSecureToken(), req)
	if err != nil {
		logger.Error(ctx, "error in contructing vendor gateway request for modify", zap.Error(err),
			zap.String(logger.STANDING_INSTRUCTION_ID, standingInstruction.GetId()), zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	modifySIRes, err := s.siVGClient.Modify(ctx, vgReq)
	switch {
	case err != nil:
		logger.Error(ctx, "error in calling vendor api for standing instruction modify", zap.Error(err))
		requestStateToUpdate = siPb.State_FAILED
	case !modifySIRes.GetStatus().IsSuccess():
		logger.Error(ctx, "non success state for standing instruction modify",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()), zap.String(logger.STATUS_CODE, modifySIRes.GetStatus().String()))
		requestStateToUpdate = siPb.State_FAILED
	default:
		requestStateToUpdate = siPb.State_SUCCESS
	}
	err = s.standingInstructionRequestDao.UpdateAndChangeStatus(ctx, standingInstructionRequest, nil,
		standingInstructionRequest.GetState(), requestStateToUpdate)
	if err != nil {
		logger.Error(ctx, "error while updating standing instruction request state", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()), zap.String(logger.STANDING_INSTRUCTION_ID, standingInstructionRequest.GetStandingInstructionId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	if requestStateToUpdate == siPb.State_SUCCESS {
		res.Status = rpc.StatusOk()
	} else {
		res.Status = rpc.StatusInternal()
	}
	return res, nil
}

func (s *Service) constructModifyVGRequest(ctx context.Context, recurringPayment *pb.RecurringPayment, vendorRequestId,
	secureToken string, req *siPb.AuthoriseModifyRequest) (*siVg.ModifyRequest, error) {
	var (
		recurrencePattern = siVg.RecurrencePattern_RECURRENCE_PATTERN_UNSPECIFIED
		ok                bool
	)
	if req.GetAllowedFrequency() != pb.AllowedFrequency_ALLOWED_FREQUENCY_UNSPECIFIED {
		recurrencePattern, ok = allowedFrequencyToRecurrencePatternMap[req.GetAllowedFrequency()]
		if !ok {
			return nil, fmt.Errorf("invalid allowed frequency %s", recurringPayment.GetRecurrenceRule().GetAllowedFrequency().String())
		}
	}
	// Get device auth details for current actor id
	deviceId, deviceToken, userProfileId, err := internal.GetDeviceAuthDetails(ctx, recurringPayment.GetFromActorId(), s.authClient)
	if err != nil {
		return nil, fmt.Errorf("error in fetching device details %w", err)
	}
	entityId, phoneNumber, err := internal.GetEntityDetails(ctx, recurringPayment.GetFromActorId(), s.actorClient)
	if err != nil {
		return nil, fmt.Errorf("error in fetching entity details %w", err)
	}

	customerId, err := internal.GetCustomerId(ctx, entityId, recurringPayment.GetPartnerBank(), s.bankCustClient)
	if err != nil {
		return nil, fmt.Errorf("error in fetching customer id %w", err)
	}
	request := &siVg.ModifyRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: recurringPayment.GetPartnerBank(),
		},
		Auth: &header.Auth{
			DeviceId:      deviceId,
			DeviceToken:   deviceToken,
			UserProfileId: userProfileId,
			CustomerId:    customerId,
			EncryptedPin:  req.GetPartnerSdkCredBlock(),
		},
		RequestId:   vendorRequestId,
		PhoneNumber: phoneNumber,
		SiToken:     secureToken,
		SiParams: &siVg.SIParams{
			AmountMaxLimit:      req.GetMaximumAmountLimit(),
			RecurrencePattern:   recurrencePattern,
			NumberOfOccurrences: req.GetMaximumAllowedTxns(),
		},
	}
	if req.GetStartDate() != nil {
		request.SiParams.ValidityStartDate = datetime.TimeToDateInLoc(req.GetStartDate().AsTime(), datetime.IST)
	}
	if req.GetEndDate() != nil {
		request.SiParams.ValidityEndDate = datetime.TimeToDateInLoc(req.GetEndDate().AsTime(), datetime.IST)
	}
	return request, nil
}

func (s *Service) GetActionStatus(ctx context.Context, req *siPb.GetActionStatusRequest) (*siPb.GetActionStatusResponse, error) {
	var (
		res = &siPb.GetActionStatusResponse{}
	)
	standingInstructionRequest, err := s.standingInstructionRequestDao.GetByRequestId(ctx, req.GetRequestId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "record not found for modify request", zap.String(logger.REQUEST_ID, req.GetRequestId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error while fetching modify request for request Id",
			zap.String(logger.REQUEST_ID, req.GetRequestId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		if standingInstructionRequest.GetRequestType() != req.GetAction() {
			logger.Error(ctx, "invalid action sent in request", zap.String(logger.REQUEST_TYPE,
				standingInstructionRequest.GetRequestType().String()), zap.String(logger.ACTION_TYPE, req.GetAction().String()))
			res.Status = rpc.StatusInvalidArgument()
			return res, nil
		}
		res.Status = rpc.StatusOk()
		res.State = standingInstructionRequest.GetState()
		return res, nil
	}
}

// nolint: dupl
func (s *Service) CreateRevokeAttempt(ctx context.Context, req *siPb.CreateRevokeAttemptRequest) (*siPb.CreateRevokeAttemptResponse, error) {
	var (
		res = &siPb.CreateRevokeAttemptResponse{}
	)
	standingInstruction, err := s.standingInstructionDao.GetByRecurringPaymentId(ctx, req.GetRecurringPaymentId())
	if err != nil {
		logger.Error(ctx, "error in fetching standing instruction", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	_, err = s.standingInstructionRequestDao.GetByRequestId(ctx, req.GetTransactionId())
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrRecordNotFound):
		_, err = s.standingInstructionRequestDao.Create(ctx, &siPb.StandingInstructionRequest{
			StandingInstructionId: standingInstruction.GetId(),
			VendorRequestId:       req.GetTransactionId(),
			RequestType:           siPb.RequestType_REVOKE,
			State:                 siPb.State_QUEUED,
		})
		if err != nil {
			logger.Error(ctx, "error in creating revoke request", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()),
				zap.String(logger.STANDING_INSTRUCTION_ID, standingInstruction.GetId()), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		res.Status = rpc.StatusOk()
	case err != nil:
		logger.Error(ctx, "error in fetching standing instruction request for revoke",
			zap.String(logger.REQUEST_ID, req.GetTransactionId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
	default:
		res.Status = rpc.StatusAlreadyExists()
	}
	return res, nil
}

// nolint: funlen
func (s *Service) AuthoriseRevoke(ctx context.Context, req *siPb.AuthoriseRevokeRequest) (*siPb.AuthoriseRevokeResponse, error) {
	var (
		res                  = &siPb.AuthoriseRevokeResponse{}
		requestStateToUpdate siPb.State
	)
	recurringPayment, err := s.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "record not found for recurring payment", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error while fetching recurring payment", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		logger.Debug(ctx, "recurring payment fetched successfully")
	}

	standingInstruction, err := s.standingInstructionDao.GetByRecurringPaymentId(ctx, req.GetRecurringPaymentId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "record not found for recurring payment id", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error while fetching recurring payment", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		logger.Debug(ctx, "standing instruction record fetched successfully")
	}
	standingInstructionRequest, err := s.standingInstructionRequestDao.GetByStandingInstructionIdAndRequestType(ctx,
		standingInstruction.GetId(), siPb.RequestType_REVOKE)
	if err != nil {
		logger.Error(ctx, "error while fetching revoke request",
			zap.String(logger.STANDING_INSTRUCTION_ID, standingInstruction.GetId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	if standingInstructionRequest.GetState() != siPb.State_QUEUED {
		logger.Error(ctx, "standing instruction request not in expected state for initiating revoke at vendor",
			zap.String(logger.STATE, standingInstructionRequest.GetState().String()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	vgReq, err := s.constructRevokeVGRequest(ctx, recurringPayment, standingInstructionRequest.GetVendorRequestId(),
		standingInstruction.GetSecureToken(), req.GetPartnerSdkCredBlock())
	if err != nil {
		logger.Error(ctx, "error in constructing vendor gateway request for revoke", zap.Error(err),
			zap.String(logger.STANDING_INSTRUCTION_ID, standingInstruction.GetId()), zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	revokeSIRes, err := s.siVGClient.Revoke(ctx, vgReq)
	switch {
	case err != nil:
		logger.Error(ctx, "error in calling vendor api for standing instruction revoke", zap.Error(err))
		requestStateToUpdate = siPb.State_FAILED
	case !revokeSIRes.GetStatus().IsSuccess():
		logger.Error(ctx, "non success state for standing instruction revoke",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()), zap.String(logger.STATUS_CODE, revokeSIRes.GetStatus().String()))
		requestStateToUpdate = siPb.State_FAILED
	default:
		requestStateToUpdate = siPb.State_SUCCESS
	}
	err = s.standingInstructionRequestDao.UpdateAndChangeStatus(ctx, standingInstructionRequest, nil,
		standingInstructionRequest.GetState(), requestStateToUpdate)
	if err != nil {
		logger.Error(ctx, "error while updating standing instruction request state", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()), zap.String(logger.STANDING_INSTRUCTION_ID, standingInstructionRequest.GetStandingInstructionId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	if standingInstructionRequest.GetState() == siPb.State_SUCCESS {
		res.Status = rpc.StatusOk()
	} else {
		res.Status = rpc.StatusInternal()
	}
	return res, nil
}

func (s *Service) constructRevokeVGRequest(ctx context.Context, recurringPayment *pb.RecurringPayment, vendorRequestId,
	secureToken, credBlock string) (*siVg.RevokeRequest, error) {
	// Get device auth details for current actor id
	deviceId, deviceToken, userProfileId, err := internal.GetDeviceAuthDetails(ctx, recurringPayment.GetFromActorId(), s.authClient)
	if err != nil {
		return nil, fmt.Errorf("error in fetching device details %w", err)
	}
	entityId, phoneNumber, err := internal.GetEntityDetails(ctx, recurringPayment.GetFromActorId(), s.actorClient)
	if err != nil {
		return nil, fmt.Errorf("error in fetching entity details %w", err)
	}

	customerId, err := internal.GetCustomerId(ctx, entityId, recurringPayment.GetPartnerBank(), s.bankCustClient)
	if err != nil {
		return nil, fmt.Errorf("error in fetching customer id %w", err)
	}
	request := &siVg.RevokeRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: recurringPayment.GetPartnerBank(),
		},
		Auth: &header.Auth{
			DeviceId:      deviceId,
			DeviceToken:   deviceToken,
			UserProfileId: userProfileId,
			CustomerId:    customerId,
			EncryptedPin:  credBlock,
		},
		RequestId:   vendorRequestId,
		PhoneNumber: phoneNumber,
		SiToken:     secureToken,
	}
	return request, nil
}

func (s *Service) DeclineAction(ctx context.Context, req *siPb.DeclineActionRequest) (*siPb.DeclineActionResponse, error) {
	var (
		res = &siPb.DeclineActionResponse{}
	)
	standingInstruction, err := s.standingInstructionDao.GetByRecurringPaymentId(ctx, req.GetRecurringPaymentId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "record not found for recurring payment id", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error while fetching recurring payment", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		logger.Debug(ctx, "standing instruction record fetched successfully")
	}
	standingInstructionRequest, err := s.standingInstructionRequestDao.GetByStandingInstructionIdAndRequestType(ctx,
		standingInstruction.GetId(), req.GetRequestType())
	if err != nil {
		logger.Error(ctx, "error while fetching create request",
			zap.String(logger.STANDING_INSTRUCTION_ID, standingInstruction.GetId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	if standingInstructionRequest.GetState() != siPb.State_QUEUED {
		logger.Error(ctx, "request not in expected state", zap.String(logger.STANDING_INSTRUCTION_ID, standingInstruction.GetId()),
			zap.String(logger.STATE, standingInstructionRequest.GetState().String()))
		res.Status = rpc.StatusFailedPrecondition()
		return res, nil
	}
	standingInstructionRequest.State = siPb.State_FAILED
	// TODO(priyansh) : Use UpdateAndChangeStatus here after this is merged https://github.com/epiFi/gamma/pull/17812
	err = s.standingInstructionRequestDao.Update(ctx, standingInstructionRequest,
		[]siPb.StandingInstructionRequestFieldMask{siPb.StandingInstructionRequestFieldMask_STATE})
	if err != nil {
		logger.Error(ctx, "error in updating standing instruction request to Failed", zap.String(logger.RECURRING_PAYMENT_ID,
			req.GetRecurringPaymentId()), zap.String(logger.STANDING_INSTRUCTION_ID, standingInstruction.GetId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	res.Status = rpc.StatusInternal()
	return res, nil
}
