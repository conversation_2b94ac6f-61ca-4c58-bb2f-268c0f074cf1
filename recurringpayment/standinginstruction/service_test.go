package standinginstruction_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/accounts"
	authPb "github.com/epifi/gamma/api/auth"
	authMocks "github.com/epifi/gamma/api/auth/mocks"
	"github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	pb "github.com/epifi/gamma/api/recurringpayment"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	savingsPb "github.com/epifi/gamma/api/savings"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	vgPaymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	siVg "github.com/epifi/gamma/api/vendorgateway/openbanking/standinginstruction"
	siVGMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/standinginstruction/mocks"
	"github.com/epifi/be-common/pkg/money"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	"github.com/epifi/gamma/recurringpayment/standinginstruction"
)

func TestService_CreateStandingInstruction(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockStandingInstructionDao := daoMocks.NewMockStandingInstructionDao(ctr)
	mockStandingInstructionRequestDao := daoMocks.NewMockStandingInstructionRequestDao(ctr)

	svc := standinginstruction.NewService(mockStandingInstructionDao, nil,
		nil, nil, nil, nil, nil,
		nil, mockStandingInstructionRequestDao, nil, nil)

	tests := []struct {
		name           string
		req            *siPb.CreateRequest
		setupMockCalls func()
		want           *siPb.CreateResponse
		wantErr        bool
	}{
		{
			name: "created standing instruction successfully",
			req: &siPb.CreateRequest{
				RecurringPaymentId: "rp-1",
				Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
				RequestId:          "request-id-1",
			},
			setupMockCalls: func() {
				mockStandingInstructionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1").Return(nil,
					epifierrors.ErrRecordNotFound)
				mockStandingInstructionDao.EXPECT().Create(gomock.Any(), &siPb.StandingInstruction{
					RecurringPaymentId: "rp-1",
				}).Return(&siPb.StandingInstruction{
					Id:                 "si-id-1",
					RecurringPaymentId: "rp-1",
				}, nil)
				mockStandingInstructionRequestDao.EXPECT().Create(gomock.Any(), &siPb.StandingInstructionRequest{
					StandingInstructionId: "si-id-1",
					VendorRequestId:       "request-id-1",
					RequestType:           siPb.RequestType_CREATE,
					State:                 siPb.State_QUEUED,
				}).Return(&siPb.StandingInstructionRequest{
					StandingInstructionId: "si-id-1",
					VendorRequestId:       "request-id-1",
					RequestType:           siPb.RequestType_CREATE,
					State:                 siPb.State_QUEUED,
				}, nil)
			},
			want: &siPb.CreateResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "fetched standing instruction successfully",
			req: &siPb.CreateRequest{
				RecurringPaymentId: "rp-1",
				Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
				RequestId:          "request-id-1",
			},
			setupMockCalls: func() {
				mockStandingInstructionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(),
					"rp-1").Return(&siPb.StandingInstruction{}, nil)
			},
			want: &siPb.CreateResponse{
				Status: rpc.StatusAlreadyExists(),
			},
			wantErr: false,
		},
		{
			name: "error in creating standing instruction",
			req: &siPb.CreateRequest{
				RecurringPaymentId: "rp-1",
				Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
			},
			setupMockCalls: func() {
				mockStandingInstructionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(),
					"rp-1").Return(nil, epifierrors.ErrRecordNotFound)
				mockStandingInstructionDao.EXPECT().Create(gomock.Any(), &siPb.StandingInstruction{
					RecurringPaymentId: "rp-1",
				}).Return(nil, fmt.Errorf("error"))
			},
			want: &siPb.CreateResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.Create(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateStandingInstruction() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("createStandingInstructionResponse() diff %v", diff)
			}
		})
	}
}

func assertCreatedAt(got *siPb.AuthoriseCreationResponse, want *siPb.AuthoriseCreationResponse) {
	want.ActionDetailedStatus.DetailedStatusList[0].CreatedAt = got.ActionDetailedStatus.DetailedStatusList[0].CreatedAt
}

func TestService_AuthoriseStandingInstructionCreation(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockStandingInstructionDao := daoMocks.NewMockStandingInstructionDao(ctr)
	mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
	mockSiVGClient := siVGMocks.NewMockStandingInstructionClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockAuthClient := authMocks.NewMockAuthClient(ctr)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctr)
	mockStandingInstructionRequestDao := daoMocks.NewMockStandingInstructionRequestDao(ctr)

	amount := money.ZeroINR().Pb
	svc := standinginstruction.NewService(mockStandingInstructionDao, mockSiVGClient, mockPiClient, mockSavingsClient,
		mockAuthClient, mockRecurringPaymentDao, nil, nil, mockStandingInstructionRequestDao, nil, nil)

	startDate := timestampPb.New(time.Now())
	endDate := timestampPb.New(time.Now().Add(48 * time.Hour))
	tests := []struct {
		name            string
		req             *siPb.AuthoriseCreationRequest
		setupMocksCalls func()
		want            *siPb.AuthoriseCreationResponse
		wantErr         bool
	}{
		{
			name: "standing instruction creation at vendor successfully",
			req: &siPb.AuthoriseCreationRequest{
				RecurringPaymentId: "rp-1",
				FromActorId:        "actor-1",
				PiFrom:             "pi-1",
				PiTo:               "pi-2",
				MaximumAmountLimit: amount,
				Interval: &types.Interval{
					StartTime: startDate,
					EndTime:   endDate,
				},
				AllowedFrequency:         pb.AllowedFrequency_DAILY,
				MaximumAllowedTxns:       10,
				PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				PreferredPaymentProtocol: payment.PaymentProtocol_IMPS,
				PartnerSdkCredBlock:      "abcd",
			},
			setupMocksCalls: func() {
				mockStandingInstructionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1").Return(&siPb.StandingInstruction{
					Id:                 "si-id-1",
					RecurringPaymentId: "rp-1",
				}, nil)
				mockStandingInstructionRequestDao.EXPECT().GetByStandingInstructionIdAndRequestType(gomock.Any(),
					"si-id-1", siPb.RequestType_CREATE).Return(&siPb.StandingInstructionRequest{
					StandingInstructionId: "si-id-1",
					VendorRequestId:       "vendor-request-id",
					RequestType:           siPb.RequestType_CREATE,
					State:                 siPb.State_QUEUED,
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:   "pi-1",
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{Account: &piPb.Account{
							ActualAccountNumber: "**********",
							IfscCode:            "FDRL0001",
							AccountType:         accounts.Type_SAVINGS,
							Name:                "epifi user 1",
						}},
					},
				}, nil)
				mockSavingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_ExternalId{ExternalId: &savingsPb.BankAccountIdentifier{
					AccountNo: "**********",
					IfscCode:  "FDRL0001",
				}}}).Return(&savingsPb.GetAccountResponse{
					Account: &savingsPb.Account{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: **********,
						},
						EmailId: "email-id",
					},
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-2"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:   "pi-2",
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{Account: &piPb.Account{
							ActualAccountNumber: "**********",
							IfscCode:            "FDRL0001",
							AccountType:         accounts.Type_SAVINGS,
							Name:                "epifi user 2",
						}},
					},
				}, nil)
				mockAuthClient.EXPECT().GetDeviceAuth(gomock.Any(), &authPb.GetDeviceAuthRequest{
					ActorId: "actor-1",
				}).Return(&authPb.GetDeviceAuthResponse{
					Status:        rpc.StatusOk(),
					UserProfileId: "user-profile-id",
					DeviceToken:   "device-token",
					Device:        &commontypes.Device{DeviceId: "device-id"},
				}, nil)
				mockSiVGClient.EXPECT().Create(gomock.Any(), newSICreateVGMatch(&siVg.CreateRequest{
					Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					Auth: &header.Auth{
						DeviceId:      "device-id",
						DeviceToken:   "device-token",
						UserProfileId: "user-profile-id",
						EncryptedPin:  "abcd",
					},
					RequestId: "vendor-request-id",
					TransTime: timestampPb.New(time.Now()),
					Protocol:  payment.PaymentProtocol_IMPS,
					Remitter: &siVg.CreateRequest_AccountDetails{
						AccountDetails: &vgPaymentPb.Account{
							AccountNumber:     "**********",
							IfscCode:          "FDRL0001",
							AccountHolderName: "epifi user 1",
							AccountType:       accounts.Type_SAVINGS,
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: **********,
							},
							Email:            "email-id",
							NotificationFlag: vgPaymentPb.Account_NONE,
						}},
					Beneficiary: &siVg.CreateRequest_AccountDetails{
						AccountDetails: &vgPaymentPb.Account{
							AccountNumber:     "**********",
							IfscCode:          "FDRL0001",
							AccountHolderName: "epifi user 2",
							AccountType:       accounts.Type_SAVINGS,
							NotificationFlag:  vgPaymentPb.Account_NONE,
						},
					},
					SiParams: &siVg.SIParams{
						AmountMaxLimit:      amount,
						ValidityStartDate:   datetime.TimeToDateInLoc(startDate.AsTime(), datetime.IST),
						ValidityEndDate:     datetime.TimeToDateInLoc(endDate.AsTime(), datetime.IST),
						RecurrencePattern:   siVg.RecurrencePattern_DAILY,
						NumberOfOccurrences: 10,
					},
				})).Return(&siVg.CreateResponse{
					TokenizedSiToken: "si-token",
					Status:           rpc.StatusOk(),
				}, nil)
				mockStandingInstructionDao.EXPECT().Update(gomock.Any(), &siPb.StandingInstruction{
					SecureToken:        "si-token",
					Id:                 "si-id-1",
					RecurringPaymentId: "rp-1",
				}, []siPb.StandingInstructionFieldMask{siPb.StandingInstructionFieldMask_SECURE_TOKEN}).Return(nil)
				mockStandingInstructionRequestDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &siPb.StandingInstructionRequest{
					StandingInstructionId: "si-id-1",
					VendorRequestId:       "vendor-request-id",
					RequestType:           siPb.RequestType_CREATE,
					State:                 siPb.State_QUEUED,
				}, nil, siPb.State_QUEUED, siPb.State_SUCCESS).Return(nil)
			},
			want: &siPb.AuthoriseCreationResponse{Status: rpc.StatusOk(),
				ActionDetailedStatus: &pb.ActionDetailedStatus{
					DetailedStatusList: []*pb.ActionDetailedStatus_DetailedStatus{
						{
							Api:   pb.ActionDetailedStatus_DetailedStatus_CREATE_SI,
							State: pb.ActionDetailedStatus_DetailedStatus_SUCCESS,
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocksCalls()
			got, err := svc.AuthoriseCreation(context.Background(), tt.req)
			assertCreatedAt(got, tt.want)
			if (err != nil) != tt.wantErr {
				t.Errorf("AuthoriseStandingInstructionCreation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("AuthoriseStandingInstructionCreation() got = %v, want %v", got, tt.want)
			}
		})
	}
}

type SICreateVGMatch struct {
	want *siVg.CreateRequest
}

func newSICreateVGMatch(want *siVg.CreateRequest) *SICreateVGMatch {
	return &SICreateVGMatch{
		want: want,
	}
}

func (ce *SICreateVGMatch) Matches(x interface{}) bool {
	got, ok := x.(*siVg.CreateRequest)
	if !ok {
		return false
	}

	ce.want.TransTime = got.TransTime
	return reflect.DeepEqual(ce.want, got)
}

func (ce *SICreateVGMatch) String() string {
	return fmt.Sprintf("want: %v", ce.want)
}
