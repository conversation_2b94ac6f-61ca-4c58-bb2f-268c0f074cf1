// nolint: goimports
package recurringpayment

import (
	"context"
	"errors"
	"fmt"

	"github.com/epifi/be-common/pkg/epificontext"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	pb "github.com/epifi/gamma/api/recurringpayment"
	rpWorkflowPb "github.com/epifi/gamma/api/recurringpayment/workflow"
	"github.com/epifi/gamma/pkg/recurringpayment"
	"github.com/epifi/gamma/recurringpayment/dao"
	"github.com/epifi/gamma/recurringpayment/internal/actionstatusfetcher"
	"github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor"
)

// AuthorizeCreationV1 rpc is called once the user performs the recurring payment creation authorization.
// for off app authorization by user like authorization on bank portal, this rpc will be called once we receive authorization callback from the vendor.
func (s *Service) AuthorizeCreationV1(ctx context.Context, req *pb.AuthorizeCreationV1Request) (*pb.AuthorizeCreationV1Response, error) {
	// get recurring payment by id
	recurringPayment, err := s.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "recurring payment not found with given id", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(err))
		return &pb.AuthorizeCreationV1Response{Status: rpc.StatusInvalidArgumentWithDebugMsg("recurring payment not found with given id")}, nil
	case err != nil:
		logger.Error(ctx, "error fetching recurring payment from db using id", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(err))
		return &pb.AuthorizeCreationV1Response{Status: rpc.StatusInternalWithDebugMsg("error fetching recurring payment by id from db")}, nil
	}

	ctx = epificontext.WithOwnership(ctx, recurringPayment.GetEntityOwnership())

	// get recurring payment create action
	actions, err := s.recurringPaymentActionsDao.GetByRecurringPaymentId(ctx, req.GetRecurringPaymentId(), dao.WithActionsFilter([]pb.Action{pb.Action_CREATE}))
	if err != nil {
		logger.Error(ctx, "error fetching recurring payment action from db by recurring payment id and action type", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(err))
		return &pb.AuthorizeCreationV1Response{Status: rpc.StatusInternalWithDebugMsg("error fetching recurring payment action from db")}, nil
	}
	if len(actions) != 1 {
		logger.Error(ctx, "multiple create actions exist for given recurring payment id and action type", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(err))
		return &pb.AuthorizeCreationV1Response{Status: rpc.StatusInvalidArgumentWithDebugMsg("multiple create actions exist for given recurring payment id and action type")}, nil
	}
	action := actions[0]

	// get current action status from action status fetcher to validate if authorization is allowed or not
	actionStatusFetcher := s.actionStatusFetcherFactory.GetActionStatusFetcher(ctx, action.GetAction(), recurringPayment.GetType(), recurringPayment.GetPaymentRoute())
	if actionStatusFetcher == nil {
		logger.Error(ctx, "no action status fetched exists for action and recurring payment type", zap.String(logger.ACTION_TYPE, action.GetAction().String()), zap.String(logger.RECURRING_PAYMENT_TYPE, recurringPayment.GetType().String()))
		return &pb.AuthorizeCreationV1Response{Status: rpc.StatusInternalWithDebugMsg("no action status fetched exists for action and recurring payment type")}, nil
	}
	actionStatus, actionSubStatus, _, _, err := actionStatusFetcher.GetActionStatusAndNextStepDeeplink(ctx, recurringPayment, action, &actionstatusfetcher.Metadata{})
	if err != nil {
		logger.Error(ctx, "error fetching status for action", zap.String(logger.ACTION_ID, action.GetId()), zap.Error(err))
		return &pb.AuthorizeCreationV1Response{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	if actionStatus != pb.ActionState_ACTION_IN_PROGRESS || actionSubStatus != pb.ActionSubState_ACTION_SUB_STATE_AUTHORIZATION_INITIATED {
		logger.Error(ctx, "recurring payment is not in a state to be authorized", zap.String(logger.ACTION_ID, action.GetId()), zap.String("actionStatus", actionStatus.String()), zap.String("actionSubStatus", actionSubStatus.String()))
		return &pb.AuthorizeCreationV1Response{Status: rpc.StatusFailedPreconditionWithDebugMsg("recurring payment is not in a state to be authorized")}, nil
	}

	if authorizeErr := s.authorizeCreationOnDomain(ctx, req.GetRecurringPaymentId(), recurringPayment.GetType(), recurringPayment.GetPaymentRoute(), req.GetAuthCredential(), req.GetAuthMetadata()); authorizeErr != nil {
		logger.Error(ctx, "error authorizing domain creation", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(err))
		return &pb.AuthorizeCreationV1Response{Status: rpc.StatusInternalWithDebugMsg("error authorizing domain creation")}, nil
	}

	workflowClientReqId := action.GetClientRequestId()
	if sendSignalErr := s.sendAuthorizationCompletedSignalToCreationV1Workflow(ctx, workflowClientReqId, rpWorkflowPb.CreateRecurringPaymentAuthorisationSignal_AUTHORISATION_STATUS_SUCCESS, recurringPayment); sendSignalErr != nil {
		logger.Error(ctx, "error sending authorization completed signal to creation workflow", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(sendSignalErr))
		return &pb.AuthorizeCreationV1Response{Status: rpc.StatusInternalWithDebugMsg("error sending authorization completed signal to creation workflow")}, nil
	}

	return &pb.AuthorizeCreationV1Response{Status: rpc.StatusOk()}, nil
}

// authorizeCreationOnDomain propagates authorization to domain service.
func (s *Service) authorizeCreationOnDomain(ctx context.Context, recurringPaymentId string, recurringPaymentType pb.RecurringPaymentType, recurringPaymentRoute pb.RecurringPaymentRoute, authCredential *pb.Credential, authMetadata *pb.RecurringPaymentCreationAuthMetadata) error {
	// get domain processor for recurring payment type
	domainProcessor, err := s.domainCreationProcessorFactory.GetProcessor(recurringPaymentType, recurringPaymentRoute)
	if err != nil {
		return fmt.Errorf("error fetching domain creation processor for recurring payment type, %w", err)
	}

	if authorizationErr := domainProcessor.AuthorizeCreation(ctx, &domaincreationprocessor.AuthorizeCreationReq{
		RecurringPaymentId: recurringPaymentId,
		AuthCredential:     authCredential,
		AuthMetadata:       authMetadata,
	}); authorizationErr != nil {
		// todo (utkarsh) : based on error type we can decide if its a permanent failure at domain's end and can send a auth failed signal to workflow in such cases
		return fmt.Errorf("error authorizing recurring payment creation, %w", err)
	}
	return nil
}

func (s *Service) sendAuthorizationCompletedSignalToCreationV1Workflow(ctx context.Context, workflowReqId string, authStatus rpWorkflowPb.CreateRecurringPaymentAuthorisationSignal_AuthorisationStatus, recurringPayment *pb.RecurringPayment) error {
	// send authorization completed signal to recurring payment creation workflow
	authorizationCompletedSignalPayload, marshalErr := protojson.Marshal(&rpWorkflowPb.CreateRecurringPaymentAuthorisationSignal{
		AuthorisationStatus: authStatus,
	})
	if marshalErr != nil {
		return fmt.Errorf("error marshalling authorization completed signal payload, %w", marshalErr)
	}
	if sendSignalErr := s.celestialProcessor.SignalWorkflow(ctx, workflowReqId, string(rpNs.CreateRecurringPaymentV1AuthorisationCompletedSignal), workflowPb.Client_RECURRING_PAYMENT, authorizationCompletedSignalPayload, recurringPayment.GetEntityOwnership(), recurringpayment.OwnershipToUseCaseForRecPay[recurringPayment.GetEntityOwnership()]); sendSignalErr != nil {
		return fmt.Errorf("error sending authorization completed signal to creation workflow, %w", sendSignalErr)
	}
	return nil
}
