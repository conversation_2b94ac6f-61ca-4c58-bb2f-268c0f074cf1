package recurringpayment

import (
	"context"
	"reflect"
	"testing"
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	"github.com/epifi/gamma/api/typesv2"

	"github.com/epifi/be-common/pkg/money"

	pb "github.com/epifi/gamma/api/recurringpayment"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/encoding/protojson"

	actorPb "github.com/epifi/gamma/api/actor"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	"github.com/epifi/gamma/api/recurringpayment/standinginstruction/mocks"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
	mocks3 "github.com/epifi/gamma/api/upi/mandate/mocks"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	mocks2 "github.com/epifi/gamma/recurringpayment/internal/mocks"

	"github.com/epifi/gamma/api/order/domain"
)

func TestService_ProcessRecurringPaymentModify(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockOrderClient := orderMocks.NewMockOrderServiceClient(ctr)
	mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
	mockRecurringPaymentsActionDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
	mockSIClient := mocks.NewMockStandingInstructionServiceClient(ctr)
	mockRecurringPaymentProcessor := mocks2.NewMockRecurringPaymentProcessor(ctr)
	mockActorClient := actorMocks.NewMockActorClient(ctr)
	mockMandateClient := mocks3.NewMockMandateServiceClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)

	svc := NewService(mockRecurringPaymentDao, mockOrderClient, mockSIClient, mockRecurringPaymentsActionDao, nil, mockActorClient, nil, nil, nil, conf, mockRecurringPaymentProcessor, mockMandateClient, nil, mockPiClient, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	payload := &pb.RecurringPaymentModifyInfo{
		RecurringPaymentId: "rp-1",
		RequestId:          "request-id-1",
		ClientRequestId:    "client-req-1",
	}
	marshalledPayload, _ := protojson.Marshal(payload)

	recurringPayment1 := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns: 10,
		PartnerBank:        commonvgpb.Vendor_FEDERAL_BANK,
		State:              pb.RecurringPaymentState_MODIFY_QUEUED,
		Ownership:          pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:       pb.UIEntryPoint_FIT,
		InitiatedBy:        pb.InitiatedBy_PAYER,
	}
	recurringPayment2 := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns: 10,
		PartnerBank:        commonvgpb.Vendor_FEDERAL_BANK,
		State:              pb.RecurringPaymentState_MODIFY_INITIATED,
		Ownership:          pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:       pb.UIEntryPoint_FIT,
		InitiatedBy:        pb.InitiatedBy_PAYER,
	}
	recurringPayment3 := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns: 10,
		PartnerBank:        commonvgpb.Vendor_FEDERAL_BANK,
		State:              pb.RecurringPaymentState_MODIFY_AUTHORISED,
		Ownership:          pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:       pb.UIEntryPoint_FIT,
		InitiatedBy:        pb.InitiatedBy_PAYER,
	}

	recurringPaymentAuthorisedMandate := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns: 10,
		PartnerBank:        commonvgpb.Vendor_FEDERAL_BANK,
		State:              pb.RecurringPaymentState_MODIFY_AUTHORISED,
		Ownership:          pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:       pb.UIEntryPoint_FIT,
		InitiatedBy:        pb.InitiatedBy_PAYER,
	}

	authorisedOneTimeMandate := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_ONE_TIME,
		},
		MaximumAllowedTxns: 10,
		PartnerBank:        commonvgpb.Vendor_FEDERAL_BANK,
		State:              pb.RecurringPaymentState_MODIFY_AUTHORISED,
		Ownership:          pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:       pb.UIEntryPoint_FIT,
		InitiatedBy:        pb.InitiatedBy_PAYER,
		ShareToPayee:       true,
	}

	tests := []struct {
		name           string
		req            *domain.ProcessFulfilmentRequest
		setupMockCalls func()
		want           *domain.ProcessFulfilmentResponse
		wantErr        bool
	}{
		{
			name: "modify queued",
			req: &domain.ProcessFulfilmentRequest{
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment1, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_MODIFY,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
					CreatedAt:          timestampPb.New(time.Now()),
				}, nil)
			},
			want:    &domain.ProcessFulfilmentResponse{ResponseHeader: &domain.DomainResponseHeader{Status: domain.DomainProcessingStatus_NO_OP}},
			wantErr: false,
		},
		{
			name: "modify initiated",
			req: &domain.ProcessFulfilmentRequest{
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment2, nil)
			},
			want:    &domain.ProcessFulfilmentResponse{ResponseHeader: &domain.DomainResponseHeader{Status: domain.DomainProcessingStatus_NO_OP}},
			wantErr: false,
		},
		{
			name: "modify authorised state failure status from domain service",
			req: &domain.ProcessFulfilmentRequest{
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment3, nil)
				mockSIClient.EXPECT().GetActionStatus(gomock.Any(), &siPb.GetActionStatusRequest{
					RecurringPaymentId: "rp-1",
					RequestId:          "request-id-1",
					Action:             siPb.RequestType_MODIFY,
				}).Return(&siPb.GetActionStatusResponse{
					Status: rpc.StatusOk(),
					State:  siPb.State_FAILED,
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_MODIFY,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment3, nil,
					pb.RecurringPaymentState_MODIFY_AUTHORISED, pb.RecurringPaymentState_ACTIVATED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_MODIFY,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}, nil, pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_FAILURE).Return(nil)
			},
			want:    &domain.ProcessFulfilmentResponse{ResponseHeader: &domain.DomainResponseHeader{Status: domain.DomainProcessingStatus_PERMANENT_FAILURE}},
			wantErr: false,
		},
		{
			name: "modify authorised state in progress status from domain service",
			req: &domain.ProcessFulfilmentRequest{
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment3, nil)
				mockSIClient.EXPECT().GetActionStatus(gomock.Any(), &siPb.GetActionStatusRequest{
					RecurringPaymentId: "rp-1",
					RequestId:          "request-id-1",
					Action:             siPb.RequestType_MODIFY,
				}).Return(&siPb.GetActionStatusResponse{
					Status: rpc.StatusOk(),
					State:  siPb.State_INITIATED,
				}, nil)
			},
			want:    &domain.ProcessFulfilmentResponse{ResponseHeader: &domain.DomainResponseHeader{Status: domain.DomainProcessingStatus_TRANSIENT_FAILURE}},
			wantErr: false,
		},
		{
			name: "modify authorised state failure status from domain service for mandate",
			req: &domain.ProcessFulfilmentRequest{
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPaymentAuthorisedMandate, nil)
				mockMandateClient.EXPECT().FetchAndUpdateRequestStatus(gomock.Any(), &upiMandatePb.FetchAndUpdateRequestStatusRequest{
					ReqId:           "request-id-1",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					EpifiCustomerPi: "pi-1",
				}).
					Return(&upiMandatePb.FetchAndUpdateRequestStatusResponse{
						Status: rpc.NewStatus(uint32(upiMandatePb.FetchAndUpdateRequestStatusResponse_PERMANENT_FAILURE), "", ""),
					}, nil)
				mockPiClient.EXPECT().GetPIsByIds(gomock.Any(), &piPb.GetPIsByIdsRequest{Ids: []string{
					"pi-1",
					"pi-2",
				}}).Return(&piPb.GetPIsByIdsResponse{
					Status: rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{
						{
							Id:                   "pi-1",
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
						},
					},
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_MODIFY,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPaymentAuthorisedMandate, nil,
					pb.RecurringPaymentState_MODIFY_AUTHORISED, pb.RecurringPaymentState_ACTIVATED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_MODIFY,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}, nil, pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_FAILURE).Return(nil)
			},
			want:    &domain.ProcessFulfilmentResponse{ResponseHeader: &domain.DomainResponseHeader{Status: domain.DomainProcessingStatus_PERMANENT_FAILURE}},
			wantErr: false,
		},
		{
			name: "modify authorised state in progress status from domain service for mandate",
			req: &domain.ProcessFulfilmentRequest{
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPaymentAuthorisedMandate, nil)
				mockMandateClient.EXPECT().FetchAndUpdateRequestStatus(gomock.Any(), &upiMandatePb.FetchAndUpdateRequestStatusRequest{
					ReqId:           "request-id-1",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					EpifiCustomerPi: "pi-1",
				}).
					Return(&upiMandatePb.FetchAndUpdateRequestStatusResponse{
						Status: rpc.NewStatus(uint32(upiMandatePb.FetchAndUpdateRequestStatusResponse_TRANSIENT_FAILURE), "", ""),
					}, nil)
				mockPiClient.EXPECT().GetPIsByIds(gomock.Any(), &piPb.GetPIsByIdsRequest{Ids: []string{
					"pi-1",
					"pi-2",
				}}).Return(&piPb.GetPIsByIdsResponse{
					Status: rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{
						{
							Id:                   "pi-1",
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
						},
					},
				}, nil)
			},
			want:    &domain.ProcessFulfilmentResponse{ResponseHeader: &domain.DomainResponseHeader{Status: domain.DomainProcessingStatus_TRANSIENT_FAILURE}},
			wantErr: false,
		},
		{
			name: "modify authorised state success status from domain service for mandate",
			req: &domain.ProcessFulfilmentRequest{
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPaymentAuthorisedMandate, nil)
				mockMandateClient.EXPECT().FetchAndUpdateRequestStatus(gomock.Any(), &upiMandatePb.FetchAndUpdateRequestStatusRequest{
					ReqId:           "request-id-1",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					EpifiCustomerPi: "pi-1",
				}).Return(&upiMandatePb.FetchAndUpdateRequestStatusResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockPiClient.EXPECT().GetPIsByIds(gomock.Any(), &piPb.GetPIsByIdsRequest{Ids: []string{
					"pi-1",
					"pi-2",
				}}).Return(&piPb.GetPIsByIdsResponse{
					Status: rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{
						{
							Id:                   "pi-1",
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
						},
					},
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_MODIFY,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
					ActionMetadata: &pb.ActionMetadata{ModifyActionMetadata: &pb.ModifyActionMetaData{
						UpdatedParams: &pb.MutableParams{
							MaximumAmountLimit: money.AmountINR(1000).Pb,
						},
					}},
				}, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPaymentAuthorisedMandate,
					[]pb.RecurringPaymentFieldMask{pb.RecurringPaymentFieldMask_MAXIMUM_AMOUNT_LIMIT},
					pb.RecurringPaymentState_MODIFY_AUTHORISED, pb.RecurringPaymentState_ACTIVATED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_MODIFY,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
					ActionMetadata: &pb.ActionMetadata{ModifyActionMetadata: &pb.ModifyActionMetaData{
						UpdatedParams: &pb.MutableParams{
							MaximumAmountLimit: money.AmountINR(1000).Pb,
						},
					}},
				}, nil, pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_SUCCESS).Return(nil)
			},
			want:    &domain.ProcessFulfilmentResponse{ResponseHeader: &domain.DomainResponseHeader{Status: domain.DomainProcessingStatus_SUCCESS}},
			wantErr: false,
		},
		{
			name: "execution already present for successfully modified mandate",
			req: &domain.ProcessFulfilmentRequest{
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(authorisedOneTimeMandate, nil)
				mockMandateClient.EXPECT().FetchAndUpdateRequestStatus(gomock.Any(), &upiMandatePb.FetchAndUpdateRequestStatusRequest{
					ReqId:           "request-id-1",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					EpifiCustomerPi: "pi-1",
				}).Return(&upiMandatePb.FetchAndUpdateRequestStatusResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockPiClient.EXPECT().GetPIsByIds(gomock.Any(), &piPb.GetPIsByIdsRequest{Ids: []string{
					"pi-1",
					"pi-2",
				}}).Return(&piPb.GetPIsByIdsResponse{
					Status: rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{
						{
							Id:                   "pi-1",
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
						},
					},
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_MODIFY,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
					ActionMetadata: &pb.ActionMetadata{ModifyActionMetadata: &pb.ModifyActionMetaData{
						UpdatedParams: &pb.MutableParams{
							MaximumAmountLimit: money.AmountINR(1000).Pb,
						},
					}},
				}, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), authorisedOneTimeMandate,
					[]pb.RecurringPaymentFieldMask{pb.RecurringPaymentFieldMask_MAXIMUM_AMOUNT_LIMIT},
					pb.RecurringPaymentState_MODIFY_AUTHORISED, pb.RecurringPaymentState_ACTIVATED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_MODIFY,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
					ActionMetadata: &pb.ActionMetadata{ModifyActionMetadata: &pb.ModifyActionMetaData{
						UpdatedParams: &pb.MutableParams{
							MaximumAmountLimit: money.AmountINR(1000).Pb,
						},
					}},
				}, nil, pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_SUCCESS).Return(nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &typesv2.Actor{
						Type: typesv2.Actor_USER,
					},
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(), gomock.Any()).Return([]*pb.RecurringPaymentsAction{
					{
						Id: "rp-action-id-1",
					},
				}, nil)
			},
			want:    &domain.ProcessFulfilmentResponse{ResponseHeader: &domain.DomainResponseHeader{Status: domain.DomainProcessingStatus_SUCCESS}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.ProcessRecurringPaymentModify(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessRecurringPaymentModify() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessRecurringPaymentModify() got = %v, want %v", got, tt.want)
			}
		})
	}
}
