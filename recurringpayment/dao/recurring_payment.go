// nolint: goimports
package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/golang/protobuf/ptypes"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
	gormV2 "gorm.io/gorm"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/errgroup"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	dbTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/enums"
	pb "github.com/epifi/gamma/api/recurringpayment"
	types "github.com/epifi/gamma/api/typesv2"
	types2 "github.com/epifi/gamma/order/wire/types"
	"github.com/epifi/gamma/pay/dao"
	"github.com/epifi/gamma/pkg/pay"
	"github.com/epifi/gamma/recurringpayment/dao/model"
)

const (
	initiatedByPayerAndCurrentActorIsPayeeQuery = "(to_actor_id= ? AND share_to_payee = true AND initiated_by = 'PAYER' AND state != 'CREATION_INITIATED' AND type = 'UPI_MANDATES') "
	initiatedByPayeeAndCurrentActorIsPayeeQuery = "(to_actor_id= ? AND share_to_payee = true AND initiated_by = 'PAYEE')"
	stateIsNotInCreationQueued                  = "(state != 'CREATION_QUEUED') "
)

var recurringPaymentColumnNameMap = map[pb.RecurringPaymentFieldMask]string{
	pb.RecurringPaymentFieldMask_ID:                   "id",
	pb.RecurringPaymentFieldMask_STATE:                "state",
	pb.RecurringPaymentFieldMask_MAXIMUM_AMOUNT_LIMIT: "amount",
	pb.RecurringPaymentFieldMask_START_DATE:           "start_date",
	pb.RecurringPaymentFieldMask_END_DATE:             "end_date",
	pb.RecurringPaymentFieldMask_RECURRENCE_RULE:      "recurrence_rule",
	pb.RecurringPaymentFieldMask_MAXIMUM_ALLOWED_TXNS: "maximum_allowed_txns",
	pb.RecurringPaymentFieldMask_PAUSE_INTERVAL:       "pause_interval",
	pb.RecurringPaymentFieldMask_PAYMENT_ROUTE:        "payment_route",
	pb.RecurringPaymentFieldMask_ENTITY_OWNERSHIP:     "entity_ownership",
	pb.RecurringPaymentFieldMask_PI_FROM:              "pi_from",
}

var recurringPaymentUpdateFieldFilter = func(field pb.RecurringPaymentFieldMask) bool {
	return pb.RecurringPaymentFieldMask_RECURRING_PAYMENT_FIELD_MASK_UNSPECIFIED != field && pb.RecurringPaymentFieldMask_ID != field
}

type RecurringPaymentDaoCRDB struct {
	db                 *gorm.DB
	idGen              idgen.IdGenerator
	attrIdGen          idgen.AttributedIdGen[*RecurringPaymentAttributes]
	dbResourceProvider *storagev2.DBResourceProvider[*gormV2.DB]
	// true: db instance is going to be fetched based on ownership which will be fetched from context
	// false: db instance will be the default epifi CRDB
	useDbResourceProvider types2.EnableResourceProvider
}

// Ensure RecurringPaymentDaoCRDB implements RecurringPaymentDao at compile time
var _ RecurringPaymentDao = &RecurringPaymentDaoCRDB{}

// Factory method for creating an instance of recurring payment dao. This method will be used by the injector
// when providing the dependencies at initialization time.
func NewRecurringPaymentDao(db dbTypes.EpifiCRDB, idGen idgen.IdGenerator, dbResourceProvider *storagev2.DBResourceProvider[*gormV2.DB], useDbResourceProvider types2.EnableResourceProvider, attrIdGen idgen.AttributedIdGen[*RecurringPaymentAttributes]) *RecurringPaymentDaoCRDB {
	return &RecurringPaymentDaoCRDB{
		db:                    db,
		idGen:                 idGen,
		attrIdGen:             attrIdGen,
		dbResourceProvider:    dbResourceProvider,
		useDbResourceProvider: useDbResourceProvider,
	}
}

func (r *RecurringPaymentDaoCRDB) Create(ctx context.Context, recurringPayment *pb.RecurringPayment, ownership commontypes.Ownership) (*pb.RecurringPayment, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentDaoCRDB", "Create", time.Now())

	ctx = epificontext.WithOwnership(ctx, ownership)

	db, resolveErr := getConnFromContextOrProviderWithDefaultHandling(ctx, r.dbResourceProvider, r.db, r.useDbResourceProvider)
	if resolveErr != nil {
		return nil, fmt.Errorf("error [%w] while resolving db connection", resolveErr)
	}

	id, err := getGeneratedId(ctx, idgen.RecurringPayment, r.idGen, r.attrIdGen, r.useDbResourceProvider)
	if err != nil {
		return nil, fmt.Errorf("recurring payment id generation failed: %w", err)
	}

	modelRecurringPayment := convertToRecurringPaymentModel(recurringPayment)
	modelRecurringPayment.Id = id

	modelRecurringPayment.ExternalId = idgen.RandAlphaNumericString(pay.RecurringPaymentExternalIdLen)

	if err := db.Create(modelRecurringPayment).Error; err != nil {
		return nil, fmt.Errorf("unable create DB entry for recurring payment : %w", err)
	}

	return convertToRecurringPaymentProto(modelRecurringPayment)
}

// GetById fetches DB record belonging to given recurring payment id. Returns error if record not found.
// nolint dupl
func (r *RecurringPaymentDaoCRDB) GetById(ctx context.Context, recurringPaymentId string) (*pb.RecurringPayment, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentDaoCRDB", "GetById", time.Now())

	if recurringPaymentId == "" {
		return nil, fmt.Errorf("id cannot be empty for GetById()")
	}

	ctx = getCtxWithOwnershipFromAttribute(ctx, idgen.RecurringPayment, r.attrIdGen, recurringPaymentId)

	db, err := getConnFromContextOrProviderWithDefaultHandling(ctx, r.dbResourceProvider, r.db, r.useDbResourceProvider)
	if err != nil {
		return nil, fmt.Errorf("failed to get db connection: %w", err)
	}

	modelRecurringPayment := &model.RecurringPayment{}
	if err := db.Where("id = ?", recurringPaymentId).First(modelRecurringPayment).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch record for recurring payment by recurringPaymentId: %v: %w", recurringPaymentId, err)
	}

	return convertToRecurringPaymentProto(modelRecurringPayment)
}

// GetByIds fetches DB records belonging to given recurring payment ids. Returns error if record not found.
// nolint dupl
func (r *RecurringPaymentDaoCRDB) GetByIds(ctx context.Context, recurringPaymentIds []string) ([]*pb.RecurringPayment, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentDaoCRDB", "GetByIds", time.Now())

	if len(recurringPaymentIds) == 0 {
		return nil, fmt.Errorf("list of recurringPaymentIds cannot be empty %w", epifierrors.ErrInvalidArgument)
	}
	ownershipToIdsMap := getOwnershipToIdsMap(recurringPaymentIds, idgen.RecurringPayment, r.attrIdGen)

	entitiesChan := make(chan []*model.RecurringPayment, len(ownershipToIdsMap))

	grp, grpCtx := errgroup.WithContext(ctx)
	for ownership, ids := range ownershipToIdsMap {
		grp.Go(func() error {
			recurringPayments, err := r.getByIdsUtil(epificontext.WithOwnership(grpCtx, ownership), ids)
			switch {
			case errors.Is(err, epifierrors.ErrRecordNotFound):
				return nil
			case err != nil:
				return fmt.Errorf("error in fetching recurring payments for ownership %s : %w", ownership.String(), err)
			default:
				entitiesChan <- recurringPayments
				return nil
			}
		})
	}
	if err := grp.Wait(); err != nil {
		return nil, fmt.Errorf("error in fetching recurring payments: %w", err)
	}

	close(entitiesChan)

	modelRecurringPayments := make([]*model.RecurringPayment, 0, len(recurringPaymentIds))
	for recurringPayments := range entitiesChan {
		modelRecurringPayments = append(modelRecurringPayments, recurringPayments...)
	}
	if len(modelRecurringPayments) == 0 {
		return nil, fmt.Errorf("failed to fetch by recurringPaymentIds: %v: %w", recurringPaymentIds, epifierrors.ErrRecordNotFound)
	}
	return convertToRecurringPaymentsProto(modelRecurringPayments)
}

func (r *RecurringPaymentDaoCRDB) getByIdsUtil(ctx context.Context, ids []string) ([]*model.RecurringPayment, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	db, resolveErr := getConnFromContextOrProviderWithDefaultHandling(ctx, r.dbResourceProvider, r.db, r.useDbResourceProvider)
	if resolveErr != nil {
		return nil, fmt.Errorf("error [%w] while resolving db connection", resolveErr)
	}
	var rpModels []*model.RecurringPayment
	if err := db.Where("id IN (?)", ids).Find(&rpModels).Error; err != nil {
		return nil, fmt.Errorf("failed to get recurring payments in DB, ids: %v: %w", ids, err)
	}
	if len(rpModels) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return rpModels, nil
}
func convertToRecurringPaymentsProto(recurringPaymentModels []*model.RecurringPayment) ([]*pb.RecurringPayment, error) {
	var recurringPayments = make([]*pb.RecurringPayment, len(recurringPaymentModels))
	for i, recurringPaymentModel := range recurringPaymentModels {
		recurringPayment, err := convertToRecurringPaymentProto(recurringPaymentModel)
		if err != nil {
			return nil, err
		}
		recurringPayments[i] = recurringPayment
	}
	return recurringPayments, nil
}

// nolint dupl
// TODO(abhishekprakash) Add a fan-out pattern to fetch recurring payments in parallel from all the registered databases because ownership might not be clear in this
func (r *RecurringPaymentDaoCRDB) GetByExternalId(ctx context.Context, externalId string) (*pb.RecurringPayment, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentDaoCRDB", "GetByExternalId", time.Now())
	db, err := getConnFromContextOrProviderWithDefaultHandling(ctx, r.dbResourceProvider, r.db, r.useDbResourceProvider)
	if err != nil {
		return nil, fmt.Errorf("failed to get db connection: %w", err)
	}

	if externalId == "" {
		return nil, fmt.Errorf("id cannot be empty for GetByExternalId(): %w", epifierrors.ErrInvalidArgument)
	}

	modelRecurringPayment := &model.RecurringPayment{}
	if err := db.Where("external_id = ?", externalId).First(modelRecurringPayment).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch record for recurring payment by externalId: %s: %w", externalId, err)
	}

	return convertToRecurringPaymentProto(modelRecurringPayment)
}

// nolint: dupl
func (r *RecurringPaymentDaoCRDB) Update(ctx context.Context, recurringPayment *pb.RecurringPayment, updateMask []pb.RecurringPaymentFieldMask) error {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentDaoCRDB", "Update", time.Now())

	ctx = getCtxWithOwnershipFromAttribute(ctx, idgen.RecurringPayment, r.attrIdGen, recurringPayment.GetId())

	db, err := getConnFromContextOrProviderWithDefaultHandling(ctx, r.dbResourceProvider, r.db, r.useDbResourceProvider)
	if err != nil {
		return fmt.Errorf("failed to get db connection: %w", err)
	}

	if recurringPayment.Id == "" {
		return fmt.Errorf("primary identifier can't be empty for an update operation")
	}

	updateMask = filterRecurringPaymentFieldMaskSlice(updateMask, recurringPaymentUpdateFieldFilter)

	if len(updateMask) == 0 {
		return fmt.Errorf("update mask can't be empty")
	}

	modelRecurringPayment := convertToRecurringPaymentModel(recurringPayment)

	updateColumns := getSelectColumnsForRecurringPayment(updateMask)

	if err := db.Model(modelRecurringPayment).Select(updateColumns).Updates(modelRecurringPayment).Error; err != nil {
		return fmt.Errorf("unable to update recurring payment: %s : %w", recurringPayment.Id, err)
	}

	return nil
}

// UpdateAndChangeStatus updates various recurring payment fields along with recurring payment status.
// But, instead of doing a blind update, a transition from the currentStatus to nextStatus is performed
// which ensures thread safety, stale read updates with out the need to acquire explicit locks
// nolint: dupl
func (r *RecurringPaymentDaoCRDB) UpdateAndChangeStatus(ctx context.Context, recurringPayment *pb.RecurringPayment,
	updateMask []pb.RecurringPaymentFieldMask, currentState, nextState pb.RecurringPaymentState) error {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentDaoCRDB", "UpdateAndChangeStatus", time.Now())

	ctx = getCtxWithOwnershipFromAttribute(ctx, idgen.RecurringPayment, r.attrIdGen, recurringPayment.GetId())

	db, err := getConnFromContextOrProviderWithDefaultHandling(ctx, r.dbResourceProvider, r.db, r.useDbResourceProvider)
	if err != nil {
		return fmt.Errorf("failed to get db connection: %w", err)
	}

	if recurringPayment.Id == "" {
		return fmt.Errorf("primary identifier can't be empty for an update operation %w", epifierrors.ErrInvalidArgument)
	}

	updateMask = filterRecurringPaymentFieldMaskSlice(updateMask, recurringPaymentUpdateFieldFilter)

	modelRecurringPayment := convertToRecurringPaymentModel(recurringPayment)

	if nextState != pb.RecurringPaymentState_RECURRING_PAYMENT_STATE_UNSPECIFIED {
		recurringPayment.State = nextState
		modelRecurringPayment.State = nextState
		updateMask = append(updateMask, pb.RecurringPaymentFieldMask_STATE)
	}

	if len(updateMask) == 0 {
		return fmt.Errorf("update mask can't be empty %w", epifierrors.ErrInvalidArgument)
	}

	updateColumns := getSelectColumnsForRecurringPayment(updateMask)

	res := db.Model(modelRecurringPayment).Where("state = ?", currentState).Select(updateColumns).Updates(modelRecurringPayment)
	if res.Error != nil {
		return fmt.Errorf("unable to update recurring payment: %s : %w", recurringPayment.Id, res.Error)
	}

	if res.RowsAffected == 0 {
		return fmt.Errorf("unable to update recurring payment from: %s to: %s, %w", currentState.String(), nextState.String(), epifierrors.ErrNoRowsAffected)
	}

	return nil
}

// TODO(abhishekprakash) Add a fan-out pattern to fetch recurring payments in parallel from all the registered databases because ownership might not be clear in this
func (r *RecurringPaymentDaoCRDB) GetByFromActorId(ctx context.Context, fromActorId string, options ...FilterOption) ([]*pb.RecurringPayment, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentDaoCRDB", "GetByFromActorId", time.Now())
	if fromActorId == "" {
		return nil, fmt.Errorf("actor id cannot be empty")
	}

	db, err := getConnFromContextOrProviderWithDefaultHandling(ctx, r.dbResourceProvider, r.db, r.useDbResourceProvider)
	if err != nil {
		return nil, fmt.Errorf("failed to get db connection: %w", err)
	}

	for _, opt := range options {
		db = opt.applyInGorm(db)
	}
	var models []*model.RecurringPayment
	if err := db.Where("from_actor_id = ?", fromActorId).Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch record for actorId: %s: %w", fromActorId, err)
	}
	if len(models) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	var result []*pb.RecurringPayment
	for i := range models {
		proto, err := convertToRecurringPaymentProto(models[i])
		if err != nil {
			logger.Error(ctx, "failed to convert model to proto %w", zap.Error(err))
			continue
		}
		result = append(result, proto)
	}
	return result, nil
}

// getSelectColumnsForRecurringPayment converts update mask to string slice with column name corresponding to field name enums
func getSelectColumnsForRecurringPayment(fieldMasks []pb.RecurringPaymentFieldMask) []string {
	var selectColumns []string

	for _, field := range fieldMasks {
		selectColumns = append(selectColumns, recurringPaymentColumnNameMap[field])
	}
	return selectColumns
}

// filterRecurringPaymentFieldMaskSlice filters out elements from a slice based on the check function passed in arg
// elements are filtered out if they dont pass the check function
// NOTE- func returns a new copy of the slice. No changes are made to the existing slice
func filterRecurringPaymentFieldMaskSlice(fieldMasks []pb.RecurringPaymentFieldMask, check func(field pb.RecurringPaymentFieldMask) bool) []pb.RecurringPaymentFieldMask {
	var ret []pb.RecurringPaymentFieldMask
	for _, fieldMask := range fieldMasks {
		if check(fieldMask) {
			ret = append(ret, fieldMask)
		}
	}
	return ret
}

// convertToRecurringPaymentModel converts domain proto struct to model struct in recurring payment to perform gorm queries on them.
func convertToRecurringPaymentModel(recurringPayment *pb.RecurringPayment) *model.RecurringPayment {
	modelRecurringPayment := &model.RecurringPayment{
		Id:                       recurringPayment.GetId(),
		FromActorId:              recurringPayment.GetFromActorId(),
		ToActorId:                recurringPayment.GetToActorId(),
		Type:                     recurringPayment.GetType(),
		PiFrom:                   recurringPayment.GetPiFrom(),
		PiTo:                     recurringPayment.GetPiTo(),
		RecurrenceRule:           recurringPayment.GetRecurrenceRule(),
		Amount:                   money.NewMoney(recurringPayment.Amount),
		AmountType:               recurringPayment.GetAmountType(),
		MaximumAllowedTxns:       recurringPayment.GetMaximumAllowedTxns(),
		PartnerBank:              recurringPayment.GetPartnerBank(),
		PreferredPaymentProtocol: recurringPayment.GetPreferredPaymentProtocol(),
		State:                    recurringPayment.GetState(),
		Ownership:                recurringPayment.GetOwnership(),
		Provenance:               recurringPayment.GetProvenance(),
		UIEntryPoint:             recurringPayment.GetUiEntryPoint(),
		InitiatedBy:              recurringPayment.GetInitiatedBy(),
		ExternalId:               recurringPayment.GetExternalId(),
		ShareToPayee:             recurringPayment.GetShareToPayee(),
		Remarks:                  recurringPayment.GetRemarks(),
		PauseInterval:            recurringPayment.GetPauseInterval(),
		PaymentRoute:             recurringPayment.GetPaymentRoute(),
		EntityOwnership:          recurringPayment.GetEntityOwnership(),
	}

	if recurringPayment.GetInterval() != nil && recurringPayment.GetInterval().GetStartTime() != nil {
		startDate := recurringPayment.GetInterval().GetStartTime().AsTime()
		modelRecurringPayment.StartDate = &startDate
	}

	if recurringPayment.GetInterval() != nil && recurringPayment.GetInterval().GetEndTime() != nil {
		endDate := recurringPayment.GetInterval().GetEndTime().AsTime()
		modelRecurringPayment.EndDate = &endDate
	}

	return modelRecurringPayment
}

func convertToRecurringPaymentProto(recurringPaymentModel *model.RecurringPayment) (*pb.RecurringPayment, error) {
	var err error

	proto := &pb.RecurringPayment{
		Id:                       recurringPaymentModel.Id,
		FromActorId:              recurringPaymentModel.FromActorId,
		ToActorId:                recurringPaymentModel.ToActorId,
		Type:                     recurringPaymentModel.Type,
		PiFrom:                   recurringPaymentModel.PiFrom,
		PiTo:                     recurringPaymentModel.PiTo,
		Amount:                   recurringPaymentModel.Amount.GetPb(),
		AmountType:               recurringPaymentModel.AmountType,
		RecurrenceRule:           recurringPaymentModel.RecurrenceRule,
		MaximumAllowedTxns:       recurringPaymentModel.MaximumAllowedTxns,
		PartnerBank:              recurringPaymentModel.PartnerBank,
		PreferredPaymentProtocol: recurringPaymentModel.PreferredPaymentProtocol,
		State:                    recurringPaymentModel.State,
		Ownership:                recurringPaymentModel.Ownership,
		Provenance:               recurringPaymentModel.Provenance,
		UiEntryPoint:             recurringPaymentModel.UIEntryPoint,
		InitiatedBy:              recurringPaymentModel.InitiatedBy,
		ExternalId:               recurringPaymentModel.ExternalId,
		ShareToPayee:             recurringPaymentModel.ShareToPayee,
		Remarks:                  recurringPaymentModel.Remarks,
		PauseInterval:            recurringPaymentModel.PauseInterval,
		PaymentRoute:             recurringPaymentModel.PaymentRoute,
		EntityOwnership:          recurringPaymentModel.EntityOwnership,
	}

	if recurringPaymentModel.StartDate != nil {
		proto.Interval = &types.Interval{StartTime: timestampPb.New(*recurringPaymentModel.StartDate)}
	}

	if recurringPaymentModel.EndDate != nil {
		if proto.Interval != nil {
			proto.Interval.EndTime = timestampPb.New(*recurringPaymentModel.EndDate)
		} else {
			proto.Interval = &types.Interval{EndTime: timestampPb.New(*recurringPaymentModel.EndDate)}
		}
	}

	proto.CreatedAt, err = ptypes.TimestampProto(recurringPaymentModel.CreatedAt)
	if err != nil {
		return nil, fmt.Errorf("unable to parse createdAt from model: %w", err)
	}

	proto.UpdatedAt, err = ptypes.TimestampProto(recurringPaymentModel.UpdatedAt)
	if err != nil {
		return nil, fmt.Errorf("unable to parse updatedAt from model: %w", err)
	}

	if recurringPaymentModel.DeletedAt.Valid {
		proto.DeletedAt = timestampPb.New(recurringPaymentModel.DeletedAt.Time)
	}

	return proto, nil
}

// GetByActorId returns the list of recurring payments by actor id
// Note: this should only be used for fetching recurring payments for listing
// Also currently mandate request state is not taken into consideration
// Order for states is QUEUED -> INITIATED -> AUTHORIZED -> TERMINAL(EXPIRED/REVOKED)
// So <= means state before the current state and >= is vice versa
// Logic for fetching the recurring payment:
// Created by payer
// Current Actor Payer - show always except in QUEUED state
// Current Actor Payee -
// For SI - don’t show it
// FOR MANDATES - state >= AUTHORISED + request status >= REQ_AUTH_MANDATE_RECEIVED
// Created by payee
// Current Actor Payer - state >= Initiated + request status >= REQ_ATUH_RECEIVED
// Current Actor Payee -  show always except in QUEUED state
// For Modify/Revoke we show recurring payment in list regardless of state
// TODO(abhishekprakash) Add a fan-out pattern to fetch recurring payments in parallel from all the registered databases because ownership might not be clear in this
func (r *RecurringPaymentDaoCRDB) GetByActorId(ctx context.Context, actorId string, tm time.Time,
	limit, offset int32, descending bool, states []pb.RecurringPaymentState) ([]*pb.RecurringPayment, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentDaoCRDB", "GetByActorId", time.Now())

	var (
		comparisonOperator string
		sortOrder          string
	)

	if descending {
		comparisonOperator = "<="
		sortOrder = "DESC"
	} else {
		comparisonOperator = ">="
		sortOrder = "ASC"
	}

	if actorId == "" {
		return nil, fmt.Errorf("actor id cannot be empty :%w", epifierrors.ErrInvalidArgument)
	}

	db, connErr := getConnFromContextOrProviderWithDefaultHandling(ctx, r.dbResourceProvider, r.db, r.useDbResourceProvider)
	if connErr != nil {
		return nil, fmt.Errorf("failed to get db connection: %w", connErr)
	}

	var models []*model.RecurringPayment

	dbQuery := stateIsNotInCreationQueued + "AND (from_actor_id = ? OR " +
		initiatedByPayerAndCurrentActorIsPayeeQuery +
		"OR " + initiatedByPayeeAndCurrentActorIsPayeeQuery + ") " +
		"AND created_at " +
		comparisonOperator + " ? ::TIMESTAMPTZ "

	if len(states) == 0 {
		db = db.Where(dbQuery, actorId, actorId, actorId, tm)
	} else {
		db = db.Where(dbQuery+"AND state in (?) ", actorId, actorId, actorId, tm, states)
	}

	db = db.Order("created_at" + " " + sortOrder)
	db = db.Offset(int(offset)).Limit(int(limit))

	if err := db.Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch record for actorId: %s: %w", actorId, err)
	}

	if len(models) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	var result []*pb.RecurringPayment
	for i := range models {
		proto, err := convertToRecurringPaymentProto(models[i])
		if err != nil {
			logger.Error(ctx, "failed to convert model to proto %w", zap.Error(err))
			continue
		}
		result = append(result, proto)
	}
	return result, nil
}

// GetByActorIdInTimeWindow returns the list of recurring payments by actor id within the given time range
// TODO(abhishekprakash) Add a fan-out pattern to fetch recurring payments in parallel from all the registered databases because ownership might not be clear in this
func (r *RecurringPaymentDaoCRDB) GetByActorIdInTimeWindow(
	ctx context.Context,
	actorId string,
	fromTime time.Time,
	toTime time.Time,
	limit, offset int32,
	descending bool,
	states []pb.RecurringPaymentState,
) ([]*pb.RecurringPayment, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentDaoCRDB", "GetByActorIdInTimeWindow", time.Now())

	var (
		sortOrder string
	)

	if descending {
		sortOrder = "DESC"
	} else {
		sortOrder = "ASC"
	}

	if actorId == "" {
		return nil, fmt.Errorf("actor id cannot be empty :%w", epifierrors.ErrInvalidArgument)
	}

	db, connErr := getConnFromContextOrProviderWithDefaultHandling(ctx, r.dbResourceProvider, r.db, r.useDbResourceProvider)
	if connErr != nil {
		return nil, fmt.Errorf("failed to get db connection: %w", connErr)
	}

	var models []*model.RecurringPayment

	dbQuery := "(from_actor_id = ? OR to_actor_id = ? ) " +
		"AND created_at >= " + " ? ::TIMESTAMPTZ " +
		"AND created_at <= " + " ? ::TIMESTAMPTZ "

	if len(states) == 0 {
		db = db.Where(dbQuery, actorId, actorId, fromTime, toTime)
	} else {
		db = db.Where(dbQuery+"AND state in (?) ", actorId, actorId, fromTime, toTime, states)
	}

	db = db.Order("created_at" + " " + sortOrder)
	db = db.Offset(int(offset)).Limit(int(limit))

	if err := db.Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch record for actorId: %s: %w", actorId, err)
	}

	if len(models) == 0 {
		return nil, gorm.ErrRecordNotFound
	}
	var result []*pb.RecurringPayment
	for i := range models {
		proto, err := convertToRecurringPaymentProto(models[i])
		if err != nil {
			logger.Error(ctx, "failed to convert model to proto %w", zap.Error(err))
			continue
		}
		result = append(result, proto)
	}
	return result, nil
}

// TODO(abhishekprakash) Add a fan-out pattern to fetch recurring payments in parallel from all the registered databases because ownership might not be clear in this
func (r *RecurringPaymentDaoCRDB) GetStateCountByActorId(ctx context.Context, actorId string) (map[string]int32, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentDaoCRDB", "GetStateCountByActorId", time.Now())
	if actorId == "" {
		return nil, fmt.Errorf("actor id cannot be empty :%w", epifierrors.ErrInvalidArgument)
	}
	db, connErr := getConnFromContextOrProviderWithDefaultHandling(ctx, r.dbResourceProvider, r.db, r.useDbResourceProvider)
	if connErr != nil {
		return nil, fmt.Errorf("failed to get db connection: %w", connErr)
	}
	var output []*stateWithCount

	dbQuery := stateIsNotInCreationQueued + "AND (from_actor_id = ? OR " +
		initiatedByPayerAndCurrentActorIsPayeeQuery +
		"OR " + initiatedByPayeeAndCurrentActorIsPayeeQuery + ") "

	db = db.Model(&model.RecurringPayment{}).Select("state, count(*) as count").Where(dbQuery, actorId, actorId, actorId).Group("state").Scan(&output)
	if !errors.Is(db.Error, gorm.ErrRecordNotFound) && db.Error != nil {
		return nil, fmt.Errorf("error in GetStateCountByActorId : %w", db.Error)
	}
	return convertStateToCountToMap(output), nil
}

type stateWithCount struct {
	State string
	Count int32
}

func convertStateToCountToMap(output []*stateWithCount) map[string]int32 {
	stateToCountMap := make(map[string]int32)
	for i := range output {
		stateToCountMap[output[i].State] += output[i].Count
	}
	return stateToCountMap
}

func getConnFromContextOrProviderWithDefaultHandling(ctx context.Context, dbResourceProvider *storagev2.DBResourceProvider[*gormV2.DB], defaultDb *gormV2.DB, useDbResourceProvider types2.EnableResourceProvider) (*gormV2.DB, error) {
	if !useDbResourceProvider {
		return gormctxv2.FromContextOrDefault(ctx, defaultDb), nil
	}
	// if we are not able to fetch ownership from context, the ownership will default to EPIFI_TECH
	ow := epificontext.OwnershipFromContext[context.Context](ctx)
	dbConn, err := dbResourceProvider.GetResourceForOwnership(ow)
	if err != nil {
		return nil, fmt.Errorf("error in fetching db from ownership :%s : %w", ow, err)
	}
	return gormctxv2.FromContextOrDefault(ctx, dbConn), nil
}

func getGeneratedId(ctx context.Context, d idgen.Domain, idgen idgen.IdGenerator, attributedIdGen idgen.AttributedIdGen[*RecurringPaymentAttributes], useDbResourceProvider types2.EnableResourceProvider) (string, error) {
	if !useDbResourceProvider {
		// If there is no use of db res provider/entity segregation, then there is no need of creating embedded ids
		return idgen.Get(d)
	}
	ownership := epificontext.OwnershipFromContext(ctx)
	return attributedIdGen.GetIdWithAttribute(d, &RecurringPaymentAttributes{
		Ownership: int(ownership),
	})
}

// nolint: unparam
func getCtxWithOwnershipFromAttribute(ctx context.Context, domain idgen.Domain, attributedIdGen idgen.AttributedIdGen[*RecurringPaymentAttributes], embeddedId string) context.Context {
	attribute := attributedIdGen.GetAttributeFromId(domain, embeddedId)
	if attribute == nil {
		return ctx
	}
	return epificontext.WithOwnership(ctx, commontypes.Ownership(attribute.Ownership))
}

func getOwnershipFromClientRequestId(ctx context.Context, ovomDao dao.OrderVendorOrderMapDao, clientReqId string) (context.Context, error) {
	ovom, err := ovomDao.GetOrderVendorOrderMap(ctx, &orderPb.OrderVendorOrderMap{DomainReferenceId: clientReqId}, []enums.OrderVendorOrderMapFieldMask{
		enums.OrderVendorOrderMapFieldMask_ORDER_VENDOR_ORDER_MAP_FIELD_MASK_REFERENCE_ID,
	})
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return ctx, nil
	case err != nil:
		return ctx, fmt.Errorf("error in fetching order vendor order map for clientReqId: %s : %w", clientReqId, err)
	default:
		return epificontext.WithOwnership(ctx, ovom[0].GetEntityOwnership()), nil
	}
}

func getOwnershipToIdsMap(ids []string, domain idgen.Domain, attrIdg idgen.AttributedIdGen[*RecurringPaymentAttributes]) map[commontypes.Ownership][]string {
	ownershipMap := make(map[commontypes.Ownership][]string)
	for _, id := range ids {
		attribute := attrIdg.GetAttributeFromId(domain, id)
		if attribute == nil {
			attribute = &RecurringPaymentAttributes{}
		}
		ow := commontypes.Ownership(attribute.Ownership)
		if ownershipMap[ow] == nil {
			ownershipMap[ow] = make([]string, 0)
		}
		ownershipMap[ow] = append(ownershipMap[ow], id)
	}
	return ownershipMap
}
