// nolint: goimports
package dao_test

import (
	"context"
	"testing"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/vendorgateway"
	idGen "github.com/epifi/be-common/pkg/idgen"
	guuid "github.com/google/uuid"
	"github.com/samber/lo"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"
	anyPb "google.golang.org/protobuf/types/known/anypb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/money"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	orderPb "github.com/epifi/gamma/api/order"
	orderEnumsPb "github.com/epifi/gamma/api/order/enums"
	pb "github.com/epifi/gamma/api/recurringpayment"
	types "github.com/epifi/gamma/api/typesv2"
	dao2 "github.com/epifi/gamma/pay/dao"
	rpServerConfig "github.com/epifi/gamma/recurringpayment/config/server"
	"github.com/epifi/gamma/recurringpayment/dao"
)

var (
	fixtureRecurringPaymentAction1 = &pb.RecurringPaymentsAction{
		Id:                       "a5004f18-5d52-4991-82a9-2a1e3010e996",
		RecurringPaymentId:       "RP-2",
		ClientRequestId:          "Client-req-id-1",
		Action:                   pb.Action_EXECUTE,
		State:                    pb.ActionState_ACTION_SUCCESS,
		InitiatedBy:              pb.InitiatedBy_PAYEE,
		ActionDetailedStatus:     &pb.ActionDetailedStatus{},
		ActionDetailedStatusInfo: &pb.ActionDetailedStatusInfo{},
	}
)

type recurringPaymentActionTestSuiteV2 struct {
	recurringPaymentActionDao dao.RecurringPaymentsActionDao
	ovomDao                   dao2.OrderVendorOrderMapDao
	attrIdGen                 idGen.AttributedIdGen[*dao.RecurringPaymentAttributes]
}

func initialiseRpaDaoTestSuite(t *testing.T) (*recurringPaymentActionTestSuiteV2, func()) {
	dbResourceProvider, release := dbResourceProviderPool.GetDbConnProvider(t)
	attrIdGen := idGen.NewAttributedIdGen[*dao.RecurringPaymentAttributes](idGen.NewClock())
	epifiDb, err := dbResourceProvider.GetResourceForOwnership(commontypes.Ownership_EPIFI_TECH)
	require.NoError(t, err)
	ovomDao := dao2.NewOrderVendorOrderMapDao(epifiDb)
	rpts := &recurringPaymentActionTestSuiteV2{
		recurringPaymentActionDao: dao.NewRecurringPaymentsActionDao(nil, attrIdGen, dbResourceProvider, true, ovomDao),
		ovomDao:                   ovomDao,
		attrIdGen:                 idGen.NewAttributedIdGen[*dao.RecurringPaymentAttributes](idGen.NewClock()),
	}
	return rpts, func() {
		release(entitySegregatedTestTables)
	}
}

type recurringPaymentActionTestSuite struct {
	conf                      *rpServerConfig.Config
	db                        *gorm.DB
	recurringPaymentActionDao dao.RecurringPaymentsActionDao
	dbName                    string
}

func newRecurringPaymentActionTestSuite(conf *rpServerConfig.Config, db *gorm.DB, recurringPaymentActionDao dao.RecurringPaymentsActionDao, dbName string) recurringPaymentActionTestSuite {
	return recurringPaymentActionTestSuite{conf: conf, db: db, recurringPaymentActionDao: recurringPaymentActionDao, dbName: dbName}
}

var (
	ts recurringPaymentActionTestSuite
)

func TestRecurringPaymentActionDaoCRDB_Create(t *testing.T) {
	startDate := timestampPb.New(time.Now())
	endDate := timestampPb.New(time.Now().Add(48 * time.Hour))
	action := &pb.RecurringPaymentsAction{
		Id:                 guuid.NewString(),
		RecurringPaymentId: "RP-1",
		ClientRequestId:    "client-request-id-1",
		Action:             pb.Action_MODIFY,
		ActionMetadata: &pb.ActionMetadata{ModifyActionMetadata: &pb.ModifyActionMetaData{
			ExistingParams: &pb.MutableParams{
				MaximumAmountLimit: money.ZeroINR().Pb,
				Interval: &types.Interval{
					StartTime: startDate,
					EndTime:   endDate,
				},
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_DAILY,
					Day:              0,
				},
				MaximumAllowedTxns: 10,
			},
			UpdatedParams: &pb.MutableParams{
				MaximumAmountLimit: money.FromPaisa(1000),
				Interval: &types.Interval{
					StartTime: startDate,
					EndTime:   endDate,
				},
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
				},
				MaximumAllowedTxns: 20,
			},
		},
		},
		State: pb.ActionState_ACTION_CREATED,
	}
	action2 := &pb.RecurringPaymentsAction{
		Id:                 guuid.NewString(),
		RecurringPaymentId: "RP-1",
		ClientRequestId:    "client-request-id-1",
		Action:             pb.Action_MODIFY,
		ActionMetadata: &pb.ActionMetadata{ModifyActionMetadata: &pb.ModifyActionMetaData{
			ExistingParams: &pb.MutableParams{
				MaximumAmountLimit: money.ZeroINR().Pb,
				Interval: &types.Interval{
					StartTime: startDate,
					EndTime:   endDate,
				},
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_DAILY,
					Day:              0,
				},
				MaximumAllowedTxns: 10,
			},
			UpdatedParams: &pb.MutableParams{
				MaximumAmountLimit: money.FromPaisa(1000),
				Interval: &types.Interval{
					StartTime: startDate,
					EndTime:   endDate,
				},
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
				},
				MaximumAllowedTxns: 20,
			},
		},
		},
		State:              pb.ActionState_ACTION_CREATED,
		PostActionDeeplink: lo.Must(anyPb.New(&deeplinkPb.Deeplink{})),
		PostAuthDeeplink:   lo.Must(anyPb.New(&deeplinkPb.Deeplink{})),
	}

	tests := []struct {
		name                   string
		recurringPaymentAction *pb.RecurringPaymentsAction
		want                   *pb.RecurringPaymentsAction
		wantErr                bool
	}{
		{
			name:                   "created successfully",
			recurringPaymentAction: action,
			want:                   action,
			wantErr:                false,
		},
		{
			name:                   "created recurring payment action with next actions successfully",
			recurringPaymentAction: action2,
			want:                   action2,
			wantErr:                false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rpaTs, release := initialiseRpaDaoTestSuite(t)
			defer release()
			got, err := rpaTs.recurringPaymentActionDao.Create(context.Background(), tt.recurringPaymentAction)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want != nil && got != nil {
				tt.want.Id = got.Id
				tt.want.CreatedAt = got.CreatedAt
				tt.want.UpdatedAt = got.UpdatedAt
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("Create() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRecurringPaymentsActionDaoCRDB_GetById(t *testing.T) {

	tests := []struct {
		name                     string
		recurringPaymentActionId string
		want                     *pb.RecurringPaymentsAction
		wantErr                  bool
	}{
		{
			name:                     "Error if RecurringPaymentsAction Id is blank",
			recurringPaymentActionId: "",
			want:                     nil,
			wantErr:                  true,
		},
		{
			name:                     "Successfully fetched RecurringPaymentsAction",
			recurringPaymentActionId: fixtureRecurringPaymentAction1.GetId(),
			want:                     fixtureRecurringPaymentAction1,
			wantErr:                  false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rpaTs, release := initialiseRpaDaoTestSuite(t)
			defer release()
			got, err := rpaTs.recurringPaymentActionDao.GetById(context.Background(), tt.recurringPaymentActionId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want != nil && got != nil {
				tt.want.CreatedAt = got.CreatedAt
				tt.want.UpdatedAt = got.UpdatedAt
				tt.want.ActionDetailedStatus = got.ActionDetailedStatus
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRecurringPaymentsActionDaoCRDB_GetByRecurringPaymentId(t *testing.T) {

	startDate := timestampPb.New(time.Now())
	endDate := timestampPb.New(time.Now().Add(48 * time.Hour))

	action1 := &pb.RecurringPaymentsAction{
		RecurringPaymentId: "RP-6",
		ClientRequestId:    "client-request-id-1",
		Action:             pb.Action_EXECUTE,
		State:              pb.ActionState_ACTION_CREATED,
		VendorRequestId:    "req-1",
	}
	action2 := &pb.RecurringPaymentsAction{
		RecurringPaymentId: "RP-6",
		ClientRequestId:    "client-request-id-2",
		Action:             pb.Action_MODIFY,
		ActionMetadata: &pb.ActionMetadata{ModifyActionMetadata: &pb.ModifyActionMetaData{
			ExistingParams: &pb.MutableParams{
				MaximumAmountLimit: money.ZeroINR().Pb,
				Interval: &types.Interval{
					StartTime: startDate,
					EndTime:   endDate,
				},
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_MONTHLY,
					Day:              10,
				},
				MaximumAllowedTxns: 10,
			},
			UpdatedParams: &pb.MutableParams{
				MaximumAmountLimit: money.FromPaisa(1000),
				Interval: &types.Interval{
					StartTime: startDate,
					EndTime:   endDate,
				},
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
					Day:              20,
				},
				MaximumAllowedTxns: 20,
			},
		}},
		State:              pb.ActionState_ACTION_SUCCESS,
		VendorRequestId:    "req-2",
		PostActionDeeplink: lo.Must(anyPb.New(&deeplinkPb.Deeplink{})),
		PostAuthDeeplink:   lo.Must(anyPb.New(&deeplinkPb.Deeplink{})),
	}
	rpaTs, release := initialiseRpaDaoTestSuite(t)
	defer release()
	action1, _ = rpaTs.recurringPaymentActionDao.Create(context.Background(), action1)
	action2, _ = rpaTs.recurringPaymentActionDao.Create(context.Background(), action2)

	tests := []struct {
		name               string
		recurringPaymentId string
		options            []dao.FilterOption
		want               []*pb.RecurringPaymentsAction
		wantErr            bool
	}{
		{
			name:               "fetched actions successfully",
			recurringPaymentId: "RP-6",
			options:            []dao.FilterOption{dao.WithOrderByCreatedAt(true)},
			want:               []*pb.RecurringPaymentsAction{action2, action1},
			wantErr:            false,
		},
		{
			name:               "fetches modify actions successfully",
			recurringPaymentId: "RP-6",
			options:            []dao.FilterOption{dao.WithActionsFilter([]pb.Action{pb.Action_MODIFY})},
			want:               []*pb.RecurringPaymentsAction{action2},
			wantErr:            false,
		},
		{
			name:               "no record found for create action",
			recurringPaymentId: "RP-6",
			options:            []dao.FilterOption{dao.WithActionsFilter([]pb.Action{pb.Action_CREATE})},
			want:               nil,
			wantErr:            true,
		},
		{
			name:               "no record found for recurring payment id",
			recurringPaymentId: "RP-3",
			want:               nil,
			wantErr:            true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := rpaTs.recurringPaymentActionDao.GetByRecurringPaymentId(context.Background(), tt.recurringPaymentId, tt.options...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByRecurringPaymentId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want != nil {
				for i := range tt.want {
					tt.want[i].CreatedAt = got[i].CreatedAt
					tt.want[i].UpdatedAt = got[i].UpdatedAt
				}
			}
			if !compareRecurringPaymentActionList(got, tt.want) {
				t.Errorf("GetByRecurringPaymentId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRecurringPaymentsActionDaoCRDB_GetSuccessfulActionsByRecurringPaymentId(t *testing.T) {

	rpAction1 := &pb.RecurringPaymentsAction{
		Id:                       "a5004f18-5d52-4991-82a9-2a1e3010e996",
		RecurringPaymentId:       "RP-2",
		ClientRequestId:          "Client-req-id-1",
		Action:                   pb.Action_EXECUTE,
		State:                    pb.ActionState_ACTION_SUCCESS,
		InitiatedBy:              pb.InitiatedBy_PAYEE,
		ActionDetailedStatusInfo: &pb.ActionDetailedStatusInfo{},
	}
	rpAction2 := &pb.RecurringPaymentsAction{
		Id:                       "a5004f18-5d52-4991-82a9-2a1e3010e997",
		RecurringPaymentId:       "RP-2",
		ClientRequestId:          "Client-req-id-2",
		Action:                   pb.Action_EXECUTE,
		State:                    pb.ActionState_ACTION_SUCCESS,
		InitiatedBy:              pb.InitiatedBy_PAYEE,
		ActionDetailedStatusInfo: &pb.ActionDetailedStatusInfo{},
	}
	rpAction3 := &pb.RecurringPaymentsAction{
		Id:                       "a5004f18-5d52-4991-82a9-2a1e3010e998",
		RecurringPaymentId:       "RP-2",
		ClientRequestId:          "Client-req-id-3",
		Action:                   pb.Action_EXECUTE,
		State:                    pb.ActionState_ACTION_SUCCESS,
		InitiatedBy:              pb.InitiatedBy_PAYEE,
		ActionDetailedStatusInfo: &pb.ActionDetailedStatusInfo{},
	}
	rpAction4 := &pb.RecurringPaymentsAction{
		Id:                       "a5004f18-5d52-4991-82a9-2a1e3010e999",
		RecurringPaymentId:       "RP-2",
		ClientRequestId:          "Client-req-id-4",
		Action:                   pb.Action_EXECUTE,
		State:                    pb.ActionState_ACTION_SUCCESS,
		InitiatedBy:              pb.InitiatedBy_PAYEE,
		ActionDetailedStatusInfo: &pb.ActionDetailedStatusInfo{},
	}
	rpAction5 := &pb.RecurringPaymentsAction{
		Id:                       "a5004f18-5d52-4991-82a9-2a1e3010e995",
		RecurringPaymentId:       "RP-2",
		ClientRequestId:          "Client-req-id-5",
		Action:                   pb.Action_EXECUTE,
		State:                    pb.ActionState_ACTION_SUCCESS,
		InitiatedBy:              pb.InitiatedBy_PAYEE,
		PostActionDeeplink:       lo.Must(anyPb.New(&deeplinkPb.Deeplink{})),
		PostAuthDeeplink:         lo.Must(anyPb.New(&deeplinkPb.Deeplink{})),
		ActionDetailedStatusInfo: &pb.ActionDetailedStatusInfo{},
	}
	rpaTs, release := initialiseRpaDaoTestSuite(t)
	defer release()
	rpAction5, _ = rpaTs.recurringPaymentActionDao.Create(context.Background(), rpAction5)

	tests := []struct {
		name               string
		recurringPaymentId string
		time               time.Time
		limit              int32
		offset             int32
		descending         bool
		sortBy             pb.RecurringPaymentActionFieldMask
		options            []dao.FilterOption
		want               []*pb.RecurringPaymentsAction
		wantErr            bool
	}{
		{
			name:               "fetched actions after current time successfully with limit 1",
			recurringPaymentId: "RP-2",
			options:            []dao.FilterOption{dao.WithActionsFilter([]pb.Action{pb.Action_EXECUTE})},
			time:               time.Now(),
			limit:              1,
			offset:             0,
			descending:         true,
			sortBy:             pb.RecurringPaymentActionFieldMask_CREATED_AT,
			want:               []*pb.RecurringPaymentsAction{rpAction5},
			wantErr:            false,
		},
		{
			name:               "fetched actions before current time successfully with limit 1",
			recurringPaymentId: "RP-2",
			options:            []dao.FilterOption{dao.WithActionsFilter([]pb.Action{pb.Action_EXECUTE})},
			time:               time.Now(),
			limit:              1,
			offset:             0,
			descending:         false,
			sortBy:             pb.RecurringPaymentActionFieldMask_CREATED_AT,
			want:               []*pb.RecurringPaymentsAction{rpAction3},
			wantErr:            false,
		},
		{
			name:               "fetched actions after current time successfully with limit 10",
			recurringPaymentId: "RP-2",
			options:            []dao.FilterOption{dao.WithActionsFilter([]pb.Action{pb.Action_EXECUTE})},
			time:               time.Now(),
			limit:              10,
			offset:             0,
			descending:         true,
			sortBy:             pb.RecurringPaymentActionFieldMask_CREATED_AT,
			want:               []*pb.RecurringPaymentsAction{rpAction5, rpAction2, rpAction1},
			wantErr:            false,
		},
		{
			name:               "fetched actions before current time successfully with limit 10",
			recurringPaymentId: "RP-2",
			options:            []dao.FilterOption{dao.WithActionsFilter([]pb.Action{pb.Action_EXECUTE})},
			time:               time.Now(),
			limit:              10,
			offset:             0,
			descending:         false,
			sortBy:             pb.RecurringPaymentActionFieldMask_CREATED_AT,
			want:               []*pb.RecurringPaymentsAction{rpAction3, rpAction4},
			wantErr:            false,
		},
		{
			name:               "fetched actions before current time successfully with limit 10",
			recurringPaymentId: "RP-2",
			options:            []dao.FilterOption{dao.WithActionsFilter([]pb.Action{pb.Action_EXECUTE})},
			time:               time.Now(),
			limit:              10,
			offset:             0,
			descending:         false,
			sortBy:             pb.RecurringPaymentActionFieldMask_CREATED_AT,
			want:               []*pb.RecurringPaymentsAction{rpAction3, rpAction4},
			wantErr:            false,
		},
		{
			name:               "record not found for action type revoke",
			recurringPaymentId: "RP-2",
			options:            []dao.FilterOption{dao.WithActionsFilter([]pb.Action{pb.Action_REVOKE})},
			time:               time.Now(),
			limit:              10,
			offset:             0,
			descending:         true,
			sortBy:             pb.RecurringPaymentActionFieldMask_CREATED_AT,
			want:               nil,
			wantErr:            false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := rpaTs.recurringPaymentActionDao.GetSuccessfulActionsByRecurringPaymentId(context.Background(),
				tt.recurringPaymentId, tt.time, tt.limit, tt.offset, tt.descending, tt.sortBy, tt.options...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSuccessfulActionsByRecurringPaymentId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want != nil {
				for i := range tt.want {
					tt.want[i].CreatedAt = got[i].CreatedAt
					tt.want[i].UpdatedAt = got[i].UpdatedAt
					tt.want[i].ActionDetailedStatus = got[i].ActionDetailedStatus
				}
			}
			if !compareRecurringPaymentActionList(got, tt.want) {
				t.Errorf("GetSuccessfulActionsByRecurringPaymentId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRecurringPaymentsActionDaoCRDB_Update(t *testing.T) {

	action1 := &pb.RecurringPaymentsAction{
		RecurringPaymentId: "RP-1",
		ClientRequestId:    "client-request-id-1",
		Action:             pb.Action_EXECUTE,
		State:              pb.ActionState_ACTION_CREATED,
	}
	rpaTs, release := initialiseRpaDaoTestSuite(t)
	defer release()
	action1, _ = rpaTs.recurringPaymentActionDao.Create(context.Background(), action1)
	action1.State = pb.ActionState_ACTION_SUCCESS
	tests := []struct {
		name                   string
		recurringPaymentAction *pb.RecurringPaymentsAction
		updateMask             []pb.RecurringPaymentActionFieldMask
		wantErr                bool
	}{
		{
			name:                   "updated successfully",
			recurringPaymentAction: action1,
			updateMask:             []pb.RecurringPaymentActionFieldMask{pb.RecurringPaymentActionFieldMask_ACTION_STATE},
			wantErr:                false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := rpaTs.recurringPaymentActionDao.Update(context.Background(), tt.recurringPaymentAction, tt.updateMask); (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRecurringPaymentsActionDaoCRDB_UpdateAndChangeStatus(t *testing.T) {

	action1 := &pb.RecurringPaymentsAction{
		RecurringPaymentId:       "RP-1",
		ClientRequestId:          "client-request-id-1",
		Action:                   pb.Action_EXECUTE,
		State:                    pb.ActionState_ACTION_CREATED,
		ActionDetailedStatusInfo: &pb.ActionDetailedStatusInfo{FiStatusCode: "ENACH_EXEC_002"},
	}
	rpaTs, release := initialiseRpaDaoTestSuite(t)
	defer release()
	action1, _ = rpaTs.recurringPaymentActionDao.Create(context.Background(), action1)
	tests := []struct {
		name                   string
		recurringPaymentAction *pb.RecurringPaymentsAction
		updateMask             []pb.RecurringPaymentActionFieldMask
		currentState           pb.ActionState
		nextState              pb.ActionState
		wantErr                bool
	}{
		{
			name: "updated successfully",
			recurringPaymentAction: &pb.RecurringPaymentsAction{
				Id:                       action1.GetId(),
				RecurringPaymentId:       action1.GetRecurringPaymentId(),
				ClientRequestId:          action1.GetClientRequestId(),
				Action:                   action1.GetAction(),
				State:                    action1.GetState(),
				ActionDetailedStatusInfo: &pb.ActionDetailedStatusInfo{FiStatusCode: "ENACH_EXEC_001"},
			},
			updateMask:   []pb.RecurringPaymentActionFieldMask{pb.RecurringPaymentActionFieldMask_ACTION_STATE, pb.RecurringPaymentActionFieldMask_ACTION_DETAILED_STATUS_INFO},
			currentState: pb.ActionState_ACTION_CREATED,
			nextState:    pb.ActionState_ACTION_SUCCESS,
			wantErr:      false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := rpaTs.recurringPaymentActionDao.UpdateAndChangeStatus(context.Background(), tt.recurringPaymentAction,
				tt.updateMask, tt.currentState, tt.nextState); (err != nil) != tt.wantErr {
				t.Errorf("UpdateAndChangeStatus() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRecurringPaymentsActionDaoCRDB_GetByClientRequestId(t *testing.T) {

	action1 := &pb.RecurringPaymentsAction{
		RecurringPaymentId: "RP-",
		ClientRequestId:    "client-request-id-1",
		Action:             pb.Action_EXECUTE,
		State:              pb.ActionState_ACTION_CREATED,
	}
	rpaTs, release := initialiseRpaDaoTestSuite(t)
	defer release()
	action1, _ = rpaTs.recurringPaymentActionDao.Create(context.Background(), action1)

	action2 := &pb.RecurringPaymentsAction{
		RecurringPaymentId: "RP-2",
		ClientRequestId:    "client-request-id-2",
		Action:             pb.Action_EXECUTE,
		State:              pb.ActionState_ACTION_CREATED,
		VendorRequestId:    "vendor-request-id-2",
		PostActionDeeplink: lo.Must(anyPb.New(&deeplinkPb.Deeplink{})),
		PostAuthDeeplink:   lo.Must(anyPb.New(&deeplinkPb.Deeplink{})),
	}
	_, _ = rpaTs.recurringPaymentActionDao.Create(context.Background(), action2)

	action3 := &pb.RecurringPaymentsAction{
		RecurringPaymentId: "RP-2",
		ClientRequestId:    "client-request-id-2",
		Action:             pb.Action_EXECUTE,
		State:              pb.ActionState_ACTION_CREATED,
		VendorRequestId:    "vendor-request-id-3",
	}
	action3, _ = rpaTs.recurringPaymentActionDao.Create(context.Background(), action3)

	tests := []struct {
		name            string
		clientRequestId string
		want            *pb.RecurringPaymentsAction
		wantErr         bool
	}{
		{
			name:            "fetched successfully",
			clientRequestId: "client-request-id-1",
			want:            action1,
			wantErr:         false,
		},
		{
			name:            "record not found",
			clientRequestId: "cli-1",
			want:            nil,
			wantErr:         true,
		},
		{
			name:            "successfully get latest created recurring payment action in case of more than on action",
			clientRequestId: "client-request-id-2",
			want:            action3,
			wantErr:         false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := rpaTs.recurringPaymentActionDao.GetByClientRequestId(context.Background(), tt.clientRequestId, false)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByClientRequestId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want != nil {
				tt.want.CreatedAt = got.CreatedAt
				tt.want.UpdatedAt = got.UpdatedAt
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetByClientRequestId() got = %v, want %v", got, tt.want)
			}
		})
	}

	t.Run("fetching via client req id using order_vendororder_map as a lookup", func(t *testing.T) {
		id, idErr := rpaTs.attrIdGen.GetIdWithAttribute(idGen.RecurringPayment, &dao.RecurringPaymentAttributes{
			Ownership: int(commontypes.Ownership_LIQUILOANS_PL),
		})
		require.NoError(t, idErr)
		clientReqId := guuid.NewString()
		_, err := rpaTs.ovomDao.Create(context.Background(), &orderPb.OrderVendorOrderMap{
			OrderId:           id,
			VendorOrderId:     "vendor-order-id-1",
			Vendor:            vendorgateway.Vendor_LIQUILOANS,
			OrderDirection:    orderEnumsPb.OrderDirection_ORDER_DIRECTION_FORWARD,
			EntityOwnership:   commontypes.Ownership_LIQUILOANS_PL,
			DomainReferenceId: clientReqId,
		})
		require.NoError(t, err)
		rpa, rpaErr := rpaTs.recurringPaymentActionDao.Create(context.Background(), &pb.RecurringPaymentsAction{
			RecurringPaymentId: id,
			ClientRequestId:    clientReqId,
			Action:             pb.Action_EXECUTE,
			State:              pb.ActionState_ACTION_CREATED,
			VendorRequestId:    "vendor-req-1",
			InitiatedBy:        pb.InitiatedBy_PAYER,
		})
		require.NoError(t, rpaErr)
		fetchedRpa, err := rpaTs.recurringPaymentActionDao.GetByClientRequestId(context.Background(), clientReqId, false)
		require.NoError(t, err)
		require.Equal(t, rpa, fetchedRpa)
	})

	t.Run("fetching via client req id using multi db query", func(t *testing.T) {
		clientReqId := guuid.NewString()
		rpa, rpaErr := rpaTs.recurringPaymentActionDao.Create(ctxWithLlOwnership, &pb.RecurringPaymentsAction{
			RecurringPaymentId: "RP-1",
			ClientRequestId:    clientReqId,
			Action:             pb.Action_EXECUTE,
			State:              pb.ActionState_ACTION_CREATED,
			VendorRequestId:    "vendor-req-ll",
			InitiatedBy:        pb.InitiatedBy_PAYER,
		})
		require.NoError(t, rpaErr)
		fetchedRpa, err := rpaTs.recurringPaymentActionDao.GetByClientRequestId(context.Background(), clientReqId, true)
		require.NoError(t, err)
		require.Equal(t, rpa, fetchedRpa)
	})
}

func TestRecurringPaymentsActionDaoCRDB_GetByVendorRequestId(t *testing.T) {

	action1 := &pb.RecurringPaymentsAction{
		RecurringPaymentId: "RP-1",
		VendorRequestId:    "vendor-request-id-1",
		Action:             pb.Action_EXECUTE,
		State:              pb.ActionState_ACTION_CREATED,
	}
	rpaTs, release := initialiseRpaDaoTestSuite(t)
	defer release()
	action1, _ = rpaTs.recurringPaymentActionDao.Create(context.Background(), action1)

	tests := []struct {
		name            string
		vendorRequestId string
		want            *pb.RecurringPaymentsAction
		wantErr         bool
	}{
		{
			name:            "fetched successfully",
			vendorRequestId: "vendor-request-id-1",
			want:            action1,
			wantErr:         false,
		},
		{
			name:            "record not found",
			vendorRequestId: "vendor-1",
			want:            nil,
			wantErr:         true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := rpaTs.recurringPaymentActionDao.GetByVendorRequestId(context.Background(), tt.vendorRequestId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByVendorRequestId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want != nil {
				tt.want.CreatedAt = got.CreatedAt
				tt.want.UpdatedAt = got.UpdatedAt
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetByVendorRequestId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRecurringPaymentsActionDaoCRDB_GetActionsByRecurringPaymentId(t *testing.T) {

	rpAction1 := &pb.RecurringPaymentsAction{
		Id:                       "a5004f18-5d52-4991-82a9-2a1e3010e996",
		RecurringPaymentId:       "RP-2",
		ClientRequestId:          "Client-req-id-1",
		Action:                   pb.Action_EXECUTE,
		State:                    pb.ActionState_ACTION_SUCCESS,
		InitiatedBy:              pb.InitiatedBy_PAYEE,
		ActionDetailedStatusInfo: &pb.ActionDetailedStatusInfo{},
	}
	rpAction2 := &pb.RecurringPaymentsAction{
		Id:                       "a5004f18-5d52-4991-82a9-2a1e3010e997",
		RecurringPaymentId:       "RP-2",
		ClientRequestId:          "Client-req-id-2",
		Action:                   pb.Action_EXECUTE,
		State:                    pb.ActionState_ACTION_SUCCESS,
		InitiatedBy:              pb.InitiatedBy_PAYEE,
		ActionDetailedStatusInfo: &pb.ActionDetailedStatusInfo{},
	}
	rpAction3 := &pb.RecurringPaymentsAction{
		Id:                       "a5004f18-5d52-4991-82a9-2a1e3010e998",
		RecurringPaymentId:       "RP-2",
		ClientRequestId:          "Client-req-id-3",
		Action:                   pb.Action_EXECUTE,
		State:                    pb.ActionState_ACTION_SUCCESS,
		InitiatedBy:              pb.InitiatedBy_PAYEE,
		ActionDetailedStatusInfo: &pb.ActionDetailedStatusInfo{},
	}
	rpAction4 := &pb.RecurringPaymentsAction{
		Id:                       "a5004f18-5d52-4991-82a9-2a1e3010e999",
		RecurringPaymentId:       "RP-2",
		ClientRequestId:          "Client-req-id-4",
		Action:                   pb.Action_EXECUTE,
		State:                    pb.ActionState_ACTION_SUCCESS,
		InitiatedBy:              pb.InitiatedBy_PAYEE,
		ActionDetailedStatusInfo: &pb.ActionDetailedStatusInfo{},
	}

	tests := []struct {
		name               string
		recurringPaymentId string
		time               time.Time
		limit              int32
		offset             int32
		descending         bool
		sortBy             pb.RecurringPaymentActionFieldMask
		options            []dao.FilterOption
		want               []*pb.RecurringPaymentsAction
		wantErr            bool
	}{
		{
			name:               "fetched actions after current time successfully with limit 1",
			recurringPaymentId: "RP-2",
			time:               time.Now(),
			limit:              1,
			offset:             0,
			descending:         true,
			want:               []*pb.RecurringPaymentsAction{rpAction2},
			wantErr:            false,
		},
		{
			name:               "fetched actions before current time successfully with limit 1",
			recurringPaymentId: "RP-2",
			time:               time.Now(),
			limit:              1,
			offset:             0,
			descending:         false,
			want:               []*pb.RecurringPaymentsAction{rpAction3},
			wantErr:            false,
		},
		{
			name:               "fetched actions after current time successfully with limit 10",
			recurringPaymentId: "RP-2",
			time:               time.Now(),
			limit:              10,
			offset:             0,
			descending:         true,
			want:               []*pb.RecurringPaymentsAction{rpAction2, rpAction1},
			wantErr:            false,
		},
		{
			recurringPaymentId: "RP-2",
			options:            []dao.FilterOption{dao.WithActionsFilter([]pb.Action{pb.Action_EXECUTE})},
			time:               time.Now(),
			limit:              10,
			offset:             0,
			descending:         false,
			want:               []*pb.RecurringPaymentsAction{rpAction3, rpAction4},
			wantErr:            false,
		},
		{
			name:               "fetched actions before current time successfully with limit 10",
			recurringPaymentId: "RP-2",
			time:               time.Now(),
			limit:              10,
			offset:             0,
			descending:         false,
			want:               []*pb.RecurringPaymentsAction{rpAction3, rpAction4},
			wantErr:            false,
		},
		{
			name:               "record not found for action type revoke",
			recurringPaymentId: "RP-2",
			time:               time.Now().Add(-100 * time.Minute),
			limit:              10,
			offset:             0,
			descending:         true,
			want:               nil,
			wantErr:            true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rpaTs, release := initialiseRpaDaoTestSuite(t)
			defer release()
			got, err := rpaTs.recurringPaymentActionDao.GetActionsByRecurringPaymentId(context.Background(),
				tt.recurringPaymentId, tt.time, tt.limit, tt.offset, tt.descending)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetActionsByRecurringPaymentId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want != nil {
				for i := range tt.want {
					tt.want[i].CreatedAt = got[i].CreatedAt
					tt.want[i].UpdatedAt = got[i].UpdatedAt
					tt.want[i].ActionDetailedStatus = got[i].ActionDetailedStatus
				}
			}
			if !compareRecurringPaymentActionList(got, tt.want) {
				t.Errorf("GetActionsByRecurringPaymentId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func compareRecurringPaymentActionList(actual, expected []*pb.RecurringPaymentsAction) bool {
	if len(actual) != len(expected) {
		return false
	}
	for idx := range actual {
		if !compareRecurringPaymentAction(actual[idx], expected[idx]) {
			return false
		}
	}
	return true
}

func compareRecurringPaymentAction(actual, expected *pb.RecurringPaymentsAction) bool {
	if actual != nil && expected != nil {
		expected.CreatedAt = actual.CreatedAt
		expected.UpdatedAt = actual.UpdatedAt
	}
	return proto.Equal(actual, expected)
}
