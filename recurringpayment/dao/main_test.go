package dao_test

import (
	"flag"
	"log"
	"os"
	"sync"
	"testing"

	"github.com/epifi/be-common/pkg/cfg"
	serverCfg "github.com/epifi/be-common/pkg/cmd/config"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	testPkg "github.com/epifi/be-common/pkg/test/v2"

	"github.com/epifi/gamma/recurringpayment/dao"

	"github.com/epifi/gamma/recurringpayment/test"
)

var (
	initialiseSameDbOnce   sync.Once
	dbResourceProviderPool testPkg.DbResourceProviderInstancePool
	cleanup                func()
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
// nolint:dogsled
func TestMain(m *testing.M) {
	flag.Parse()

	idg := idgen.NewDomainIdGenerator(idgen.NewClock())

	dbName, conf, _, db, teardown := test.InitTestServerV2()
	dbResourceProviderPool, cleanup = InitConfigAndDBResourceProviderInstancePool()
	defer cleanup()

	ts = newRecurringPaymentActionTestSuite(conf, db, dao.NewRecurringPaymentsActionDao(db, nil, nil, false, nil), dbName)

	sits = newStandingInstructionTestSuite(conf, db, dao.NewStandingInstructionDao(db, idg))

	standingInstructionRequestTS = newStandingInstructionRequestTestSuite(conf, db, dao.NewStandingInstructionRequestDao(db))

	recurringPaymentsVendorDetailsTS = newRecurringPaymentsVendorDetailsTestSuite(conf, db, dao.NewRecurringPaymentsVendorDetailsDao(idg, nil), dbName)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func InitConfigAndDBResourceProviderInstancePool() (testPkg.DbResourceProviderInstancePool, func()) {
	// Init config
	serverConf, err := serverCfg.Load(cfg.ORDER_SERVER)
	if err != nil {
		log.Fatal("failed to load config", err)
	}
	// Setup logger
	logger.Init(serverConf.Environment)

	// Currently set pool size to 1 since we don't support concurrency for DBResourceProviderInstancePool.
	dbResourceProviderInstancePool := testPkg.NewDbResourceProviderInstancePoolWithUseCase(testPkg.NewZapLogger(logger.Log), serverConf.UseCaseDBConfigMap, 1)
	// Initializing dbUtilsProvider to get db connections and txn executors
	return dbResourceProviderInstancePool, func() {
		dbResourceProviderInstancePool.Cleanup(testPkg.NewZapLogger(logger.Log))
	}
}
