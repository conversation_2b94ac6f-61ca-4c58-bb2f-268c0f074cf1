// nolint: goimports
package dao

import (
	"encoding/json"
	"fmt"

	"github.com/btcsuite/btcutil/base58"
	"github.com/epifi/be-common/pkg/idgen"
)

type RecurringPaymentAttributes struct {
	Ownership int `json:"o"`
}

func (r *RecurringPaymentAttributes) ToString() (string, error) {
	if r == nil {
		return "", nil
	}
	jsonAttr, err := json.Marshal(r)
	if err != nil {
		return "", fmt.Errorf("error marshalling recurring payment attributes: %w", err)
	}
	return base58.Encode(jsonAttr), nil
}

func (r *RecurringPaymentAttributes) FromString(s string) idgen.Attribute {
	var attr RecurringPaymentAttributes
	err := json.Unmarshal(base58.Decode(s), &attr)
	if err != nil {
		return nil
	}
	return &attr
}

var _ idgen.Attribute = &RecurringPaymentAttributes{}
