package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/golang/protobuf/ptypes"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	"github.com/epifi/gamma/recurringpayment/dao/model"
)

var standingInstructionColumnNameMap = map[siPb.StandingInstructionFieldMask]string{
	siPb.StandingInstructionFieldMask_SECURE_TOKEN: "secure_token",
}

var standingInstructionUpdateFieldFilter = func(field siPb.StandingInstructionFieldMask) bool {
	return siPb.StandingInstructionFieldMask_STANDING_INSTRUCTION_FIELD_MASK_UNSPECIFIED != field
}

type StandingInstructionDaoCRDB struct {
	db    *gorm.DB
	idGen idgen.IdGenerator
}

// Ensure StandingInstructionDaoCRDB implements StandingInstructionDao at compile time
var _ StandingInstructionDao = &StandingInstructionDaoCRDB{}

// Factory method for creating an instance of standing instruction dao. This method will be used by the injector
// when providing the dependencies at initialization time.
func NewStandingInstructionDao(db types.EpifiCRDB, idGen idgen.IdGenerator) *StandingInstructionDaoCRDB {
	return &StandingInstructionDaoCRDB{db: db, idGen: idGen}
}

// nolint: dupl
func (s *StandingInstructionDaoCRDB) Create(ctx context.Context, standingInstruction *siPb.StandingInstruction) (*siPb.StandingInstruction, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "StandingInstructionDaoCRDB", "Create", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, s.db)

	id, err := s.idGen.Get(idgen.StandingInstruction)
	if err != nil {
		return nil, fmt.Errorf("standing instruction id generation failed: %w", err)
	}

	modelStandingInstruction := convertToSIModel(standingInstruction)
	modelStandingInstruction.Id = id

	if err := db.Create(modelStandingInstruction).Error; err != nil {
		return nil, fmt.Errorf("unable create DB entry for standing instruction : %w", err)
	}

	return convertToSIProto(modelStandingInstruction)
}

// GetByRecurringPaymentIdAndRequestType fetches the latest entry for a given request type corresponding to a
// recurring payment id.
// nolint: dupl
func (s *StandingInstructionDaoCRDB) GetByRecurringPaymentId(ctx context.Context, recurringPaymentId string) (*siPb.StandingInstruction, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "StandingInstructionDaoCRDB", "GetByRecurringPaymentId", time.Now())
	if recurringPaymentId == "" {
		return nil, fmt.Errorf("recurring payment id cannot be empty")
	}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	modelStandingInstruction := &model.StandingInstruction{}
	if err := db.Where("recurring_payment_id = ?", recurringPaymentId).First(modelStandingInstruction).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch standing instruction for recurring payment id %s, err %w",
			recurringPaymentId, err)
	}
	return convertToSIProto(modelStandingInstruction)
}

// nolint: dupl
func (s *StandingInstructionDaoCRDB) Update(ctx context.Context, standingInstruction *siPb.StandingInstruction,
	updateMask []siPb.StandingInstructionFieldMask) error {
	defer metric_util.TrackDuration("recurringpayment/dao", "StandingInstructionDaoCRDB", "Update", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, s.db)

	if standingInstruction.Id == "" {
		return fmt.Errorf("primary identifier can't be empty for an update operation")
	}
	updateMask = filterStandingInstructionFieldMaskSlice(updateMask, standingInstructionUpdateFieldFilter)
	if len(updateMask) == 0 {
		return fmt.Errorf("update mask can't be empty")
	}

	modelStandingInstruction := convertToSIModel(standingInstruction)
	updateColumns := getSelectColumnsForStandingInstruction(updateMask)
	if err := db.Model(modelStandingInstruction).Select(updateColumns).Updates(modelStandingInstruction).Error; err != nil {
		return fmt.Errorf("unable to update standing instruction: %s : %w", modelStandingInstruction.Id, err)
	}
	return nil
}

// filterStandingInstructionFieldMaskSlice filters out elements from a slice based on the check function passed in arg
// elements are filtered out if they dont pass the check function
// NOTE- func returns a new copy of the slice. No changes are made to the existing slice
func filterStandingInstructionFieldMaskSlice(fieldMasks []siPb.StandingInstructionFieldMask, check func(field siPb.StandingInstructionFieldMask) bool) []siPb.StandingInstructionFieldMask {
	var ret []siPb.StandingInstructionFieldMask
	for _, fieldMask := range fieldMasks {
		if check(fieldMask) {
			ret = append(ret, fieldMask)
		}
	}
	return ret
}

// getSelectColumnsForStandingInstruction converts update mask to string slice with column name corresponding to field name enums
func getSelectColumnsForStandingInstruction(fieldMasks []siPb.StandingInstructionFieldMask) []string {
	var selectColumns []string

	for _, field := range fieldMasks {
		selectColumns = append(selectColumns, standingInstructionColumnNameMap[field])
	}
	return selectColumns
}

func convertToSIModel(proto *siPb.StandingInstruction) *model.StandingInstruction {
	return &model.StandingInstruction{
		Id:                 proto.GetId(),
		RecurringPaymentId: proto.GetRecurringPaymentId(),
		SecureToken:        proto.GetSecureToken(),
	}
}

func convertToSIProto(model *model.StandingInstruction) (*siPb.StandingInstruction, error) {
	var err error

	proto := &siPb.StandingInstruction{
		Id:                 model.Id,
		RecurringPaymentId: model.RecurringPaymentId,
		SecureToken:        model.SecureToken,
	}
	proto.CreatedAt, err = ptypes.TimestampProto(model.CreatedAt)
	if err != nil {
		return nil, fmt.Errorf("unable to parse createdAt from model: %w", err)
	}

	proto.UpdatedAt, err = ptypes.TimestampProto(model.UpdatedAt)
	if err != nil {
		return nil, fmt.Errorf("unable to parse updatedAt from model: %w", err)
	}

	if model.DeletedAt.Valid {
		proto.DeletedAt = timestampPb.New(model.DeletedAt.Time)
	}
	return proto, nil
}
