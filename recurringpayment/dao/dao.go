// nolint: goimports
//
//go:generate mockgen -source=$PWD/dao.go -destination=$PWD/mocks/mock_dao.go -package=mocks
//go:generate dao_metrics_gen .
package dao

import (
	"context"
	"time"

	"github.com/google/wire"
	"github.com/jonboulle/clockwork"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/pagination"

	pb "github.com/epifi/gamma/api/recurringpayment"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	payDao "github.com/epifi/gamma/pay/dao"
)

var WireSet = wire.NewSet(
	NewRecurringPaymentDao, wire.Bind(new(RecurringPaymentDao), new(*RecurringPaymentDaoCRDB)),
	NewRecurringPaymentsActionDao, wire.Bind(new(RecurringPaymentsActionDao), new(*RecurringPaymentsActionDaoCRDB)),
	NewStandingInstructionDao, wire.Bind(new(StandingInstructionDao), new(*StandingInstructionDaoCRDB)),
	NewStandingInstructionRequestDao, wire.Bind(new(StandingInstructionRequestDao), new(*StandingInstructionRequestDaoCRDB)),
	NewRecurringPaymentsVendorDetailsDao, wire.Bind(new(RecurringPaymentsVendorDetailsDao), new(*RecurringPaymentsVendorDetailsDaoCRDB)),
	payDao.NewOrderVendorOrderMapDao, wire.Bind(new(payDao.OrderVendorOrderMapDao), new(*payDao.OrderVendorOrderMapDaoCRDB)),
	ProvideAttributedIdGen)

func ProvideAttributedIdGen(clk clockwork.Clock) idgen.AttributedIdGen[*RecurringPaymentAttributes] {
	return idgen.NewAttributedIdGen[*RecurringPaymentAttributes](clk)
}

type RecurringPaymentDao interface {
	Create(ctx context.Context, recurringPayment *pb.RecurringPayment, ownership commontypes.Ownership) (*pb.RecurringPayment, error)
	GetById(ctx context.Context, recurringPaymentId string) (*pb.RecurringPayment, error)
	GetByIds(ctx context.Context, recurringPaymentId []string) ([]*pb.RecurringPayment, error)
	GetByExternalId(ctx context.Context, externalId string) (*pb.RecurringPayment, error)
	// Deprecated: this method has be deprecated and it is prone to stale read updates specially when updating states.
	// use UpdateAndChangeStatus to update the status ensuring thread safety.
	Update(ctx context.Context, recurringPayment *pb.RecurringPayment, updateMask []pb.RecurringPaymentFieldMask) error
	// UpdateAndChangeStatus updates various recurring payment fields along with recurring payment status.
	// But, instead of doing a blind update, a transition from the currentState to nextState is performed
	// which ensures thread safety, stale read updates with out the need to acquire explicit locks
	UpdateAndChangeStatus(ctx context.Context, recurringPayment *pb.RecurringPayment, updateMask []pb.RecurringPaymentFieldMask,
		currentState, nextState pb.RecurringPaymentState) error
	GetByFromActorId(ctx context.Context, actorId string, options ...FilterOption) ([]*pb.RecurringPayment, error)
	// GetByActorId this method fetches lst of recurring payments by actor id
	// Note: this should only be used for fetching recurring payments for listing
	GetByActorId(ctx context.Context, actorId string, tm time.Time,
		limit, offset int32, descending bool, states []pb.RecurringPaymentState) ([]*pb.RecurringPayment, error)
	// GetStateCountByActorId return the count of recurring payment for a each state
	// Note : This should be only used for fetching counts for listing
	GetStateCountByActorId(ctx context.Context, actorId string) (map[string]int32, error)
	// GetByActorIdInTimeWindow this method fetches lst of recurring payments by actor id in the given time window
	GetByActorIdInTimeWindow(
		ctx context.Context,
		actorId string,
		fromTime time.Time,
		toTime time.Time,
		limit, offset int32,
		descending bool,
		states []pb.RecurringPaymentState,
	) ([]*pb.RecurringPayment, error)
}

type RecurringPaymentsActionDao interface {
	Create(ctx context.Context, recurringPaymentAction *pb.RecurringPaymentsAction) (*pb.RecurringPaymentsAction, error)
	GetById(ctx context.Context, recurringPaymentActionId string) (*pb.RecurringPaymentsAction, error)
	// GetByRecurringPaymentId returns actions for a given recurring payment. It also supports filtering based on actions.
	GetByRecurringPaymentId(ctx context.Context, recurringPaymentId string,
		options ...FilterOption) ([]*pb.RecurringPaymentsAction, error)
	// GetSuccessfulActionsByRecurringPaymentId fetches successful actions for a given actionType and recurring payment id
	// In case the descending flag is false -
	// the query returns orders, actions that happened after the given time
	// In case the descending flag is true -
	// the queue returns orders, actions that happened before the given time
	// limits and offset bounds the results and provides a functionality of pagination.
	GetSuccessfulActionsByRecurringPaymentId(ctx context.Context,
		recurringPaymentId string, time time.Time, limit, offset int32,
		descending bool, sortBy pb.RecurringPaymentActionFieldMask, options ...FilterOption) ([]*pb.RecurringPaymentsAction, error)
	// Deprecated: this method has be deprecated and it is prone to stale read updates specially when updating states.
	// use UpdateAndChangeStatus to update the status ensuring thread safety.
	Update(ctx context.Context, recurringPaymentAction *pb.RecurringPaymentsAction,
		updateMask []pb.RecurringPaymentActionFieldMask) error
	// GetByClientRequestId return latest RecurringPaymentAction against a client_request_id. More than one RecurringPaymentAction
	// can exist against a client_request_id (Ex. For execution if one transaction failed from vendor due to transient error, recurring payment service will
	// retry that payment against same order.
	// The flag multiDbQuery will specify if a scatter-gather search is required for this since the entity ownership is not certain. If the flag is sent
	// as true, then the query will be done on all the databases, else it will be done on the database derived from ctx/lookups, etc.
	GetByClientRequestId(ctx context.Context, clientRequestId string, multiDbQuery bool) (*pb.RecurringPaymentsAction, error)
	// UpdateAndChangeStatus updates various recurring payment action fields along with recurring payment action state.
	// But, instead of doing a blind update, a transition from the currentState to nextState is performed
	// which ensures thread safety, stale read updates with out the need to acquire explicit locks
	UpdateAndChangeStatus(ctx context.Context, recurringPaymentAction *pb.RecurringPaymentsAction, updateMask []pb.RecurringPaymentActionFieldMask,
		currentState, nextState pb.ActionState) error
	// GetByVendorRequestId fetches the recurring payment action by vendor request id
	GetByVendorRequestId(ctx context.Context, vendorRequestId string) (*pb.RecurringPaymentsAction, error)
	// GetActionsByRecurringPaymentId fetches actions for a given actionType and recurring payment id
	// In case the descending flag is false -
	// the query returns orders, actions that happened after the given time
	// In case the descending flag is true -
	// the queue returns orders, actions that happened before the given time
	// limits and offset bounds the results and provides a functionality of pagination.
	GetActionsByRecurringPaymentId(ctx context.Context,
		recurringPaymentId string, time time.Time, limit, offset int32,
		descending bool, options ...FilterOption) ([]*pb.RecurringPaymentsAction, error)
}

type StandingInstructionDao interface {
	Create(ctx context.Context, standingInstruction *siPb.StandingInstruction) (*siPb.StandingInstruction, error)
	GetByRecurringPaymentId(ctx context.Context, recurringPaymentId string) (*siPb.StandingInstruction, error)
	Update(ctx context.Context, standingInstruction *siPb.StandingInstruction, updateMask []siPb.StandingInstructionFieldMask) error
}

type StandingInstructionRequestDao interface {
	Create(ctx context.Context, standingInstructionRequest *siPb.StandingInstructionRequest) (*siPb.StandingInstructionRequest, error)
	GetByStandingInstructionIdAndRequestType(ctx context.Context, standingInstructionId string,
		requestType siPb.RequestType) (*siPb.StandingInstructionRequest, error)
	// Deprecated: this method has be deprecated and it is prone to stale read updates specially when updating states.
	// use UpdateAndChangeStatus to update the status ensuring thread safety.
	Update(ctx context.Context, standingInstructionRequest *siPb.StandingInstructionRequest,
		updateMask []siPb.StandingInstructionRequestFieldMask) error
	// UpdateAndChangeStatus updates various recurring payment action fields along with standing instruction request state.
	// But, instead of doing a blind update, a transition from the currentState to nextState is performed
	// which ensures thread safety, stale read updates with out the need to acquire explicit locks
	UpdateAndChangeStatus(ctx context.Context, standingInstructionRequest *siPb.StandingInstructionRequest,
		updateMask []siPb.StandingInstructionRequestFieldMask, currentState, nextState siPb.State) error
	GetByRequestId(ctx context.Context, requestId string) (*siPb.StandingInstructionRequest, error)
}

type RecurringPaymentsVendorDetailsDao interface {
	// Create creates an entry for all vendorDetails of a recurring payment
	Create(ctx context.Context, vendorDetails *pb.RecurringPaymentsVendorDetails) (*pb.RecurringPaymentsVendorDetails, error)
	GetById(ctx context.Context, id string) (*pb.RecurringPaymentsVendorDetails, error)
	// GetByRecurringPaymentId retrieves the vendorDetails for recurring payment associated with a specific recurring payment ID.
	GetByRecurringPaymentId(ctx context.Context, recurringPaymentId string) (*pb.RecurringPaymentsVendorDetails, error)
	// GetByActorId retrieves the vendorDetails associated for all recurring payments associated with a specific Actor ID.
	GetByActorId(ctx context.Context, actorId string) ([]*pb.RecurringPaymentsVendorDetails, error)
	// GetByVendorCustomerId retrieves the vendorDetails associated with a specific vendor customer ID.
	GetByVendorCustomerId(ctx context.Context, vendorCustomerId string) (*pb.RecurringPaymentsVendorDetails, error)
	// GetByVendorPaymentId retrieves the vendorDetails associated with a specific vendor payment ID.
	GetByVendorPaymentId(ctx context.Context, vendorPaymentId string) (*pb.RecurringPaymentsVendorDetails, error)
	// Update updates and entry for any of the vendor detail field for a recurring payment.
	Update(ctx context.Context, vendorDetails *pb.RecurringPaymentsVendorDetails, updateMask []pb.RecurringPaymentVendorDetailsFieldMask) error
	// Get fetches all the recurring_payments_vendor_details entries in the table in a paginated manner
	// to avoid a full table scan.
	Get(ctx context.Context, pageToken *pagination.PageToken, pageSize uint32, filters ...FilterOption) ([]*pb.RecurringPaymentsVendorDetails, *rpc.PageContextResponse, error)
}
