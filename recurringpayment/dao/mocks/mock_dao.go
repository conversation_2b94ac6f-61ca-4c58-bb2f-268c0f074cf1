// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/go/src/github.com/epifi/gamma/recurringpayment/dao/dao.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	pagination "github.com/epifi/be-common/pkg/pagination"
	recurringpayment "github.com/epifi/gamma/api/recurringpayment"
	standinginstruction "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	dao "github.com/epifi/gamma/recurringpayment/dao"
	gomock "github.com/golang/mock/gomock"
)

// MockRecurringPaymentDao is a mock of RecurringPaymentDao interface.
type MockRecurringPaymentDao struct {
	ctrl     *gomock.Controller
	recorder *MockRecurringPaymentDaoMockRecorder
}

// MockRecurringPaymentDaoMockRecorder is the mock recorder for MockRecurringPaymentDao.
type MockRecurringPaymentDaoMockRecorder struct {
	mock *MockRecurringPaymentDao
}

// NewMockRecurringPaymentDao creates a new mock instance.
func NewMockRecurringPaymentDao(ctrl *gomock.Controller) *MockRecurringPaymentDao {
	mock := &MockRecurringPaymentDao{ctrl: ctrl}
	mock.recorder = &MockRecurringPaymentDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRecurringPaymentDao) EXPECT() *MockRecurringPaymentDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRecurringPaymentDao) Create(ctx context.Context, recurringPayment *recurringpayment.RecurringPayment, ownership common.Ownership) (*recurringpayment.RecurringPayment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, recurringPayment, ownership)
	ret0, _ := ret[0].(*recurringpayment.RecurringPayment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRecurringPaymentDaoMockRecorder) Create(ctx, recurringPayment, ownership interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRecurringPaymentDao)(nil).Create), ctx, recurringPayment, ownership)
}

// GetByActorId mocks base method.
func (m *MockRecurringPaymentDao) GetByActorId(ctx context.Context, actorId string, tm time.Time, limit, offset int32, descending bool, states []recurringpayment.RecurringPaymentState) ([]*recurringpayment.RecurringPayment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorId", ctx, actorId, tm, limit, offset, descending, states)
	ret0, _ := ret[0].([]*recurringpayment.RecurringPayment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockRecurringPaymentDaoMockRecorder) GetByActorId(ctx, actorId, tm, limit, offset, descending, states interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockRecurringPaymentDao)(nil).GetByActorId), ctx, actorId, tm, limit, offset, descending, states)
}

// GetByActorIdInTimeWindow mocks base method.
func (m *MockRecurringPaymentDao) GetByActorIdInTimeWindow(ctx context.Context, actorId string, fromTime, toTime time.Time, limit, offset int32, descending bool, states []recurringpayment.RecurringPaymentState) ([]*recurringpayment.RecurringPayment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorIdInTimeWindow", ctx, actorId, fromTime, toTime, limit, offset, descending, states)
	ret0, _ := ret[0].([]*recurringpayment.RecurringPayment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorIdInTimeWindow indicates an expected call of GetByActorIdInTimeWindow.
func (mr *MockRecurringPaymentDaoMockRecorder) GetByActorIdInTimeWindow(ctx, actorId, fromTime, toTime, limit, offset, descending, states interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorIdInTimeWindow", reflect.TypeOf((*MockRecurringPaymentDao)(nil).GetByActorIdInTimeWindow), ctx, actorId, fromTime, toTime, limit, offset, descending, states)
}

// GetByExternalId mocks base method.
func (m *MockRecurringPaymentDao) GetByExternalId(ctx context.Context, externalId string) (*recurringpayment.RecurringPayment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByExternalId", ctx, externalId)
	ret0, _ := ret[0].(*recurringpayment.RecurringPayment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByExternalId indicates an expected call of GetByExternalId.
func (mr *MockRecurringPaymentDaoMockRecorder) GetByExternalId(ctx, externalId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByExternalId", reflect.TypeOf((*MockRecurringPaymentDao)(nil).GetByExternalId), ctx, externalId)
}

// GetByFromActorId mocks base method.
func (m *MockRecurringPaymentDao) GetByFromActorId(ctx context.Context, actorId string, options ...dao.FilterOption) ([]*recurringpayment.RecurringPayment, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByFromActorId", varargs...)
	ret0, _ := ret[0].([]*recurringpayment.RecurringPayment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByFromActorId indicates an expected call of GetByFromActorId.
func (mr *MockRecurringPaymentDaoMockRecorder) GetByFromActorId(ctx, actorId interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByFromActorId", reflect.TypeOf((*MockRecurringPaymentDao)(nil).GetByFromActorId), varargs...)
}

// GetById mocks base method.
func (m *MockRecurringPaymentDao) GetById(ctx context.Context, recurringPaymentId string) (*recurringpayment.RecurringPayment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, recurringPaymentId)
	ret0, _ := ret[0].(*recurringpayment.RecurringPayment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockRecurringPaymentDaoMockRecorder) GetById(ctx, recurringPaymentId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockRecurringPaymentDao)(nil).GetById), ctx, recurringPaymentId)
}

// GetByIds mocks base method.
func (m *MockRecurringPaymentDao) GetByIds(ctx context.Context, recurringPaymentId []string) ([]*recurringpayment.RecurringPayment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByIds", ctx, recurringPaymentId)
	ret0, _ := ret[0].([]*recurringpayment.RecurringPayment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByIds indicates an expected call of GetByIds.
func (mr *MockRecurringPaymentDaoMockRecorder) GetByIds(ctx, recurringPaymentId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByIds", reflect.TypeOf((*MockRecurringPaymentDao)(nil).GetByIds), ctx, recurringPaymentId)
}

// GetStateCountByActorId mocks base method.
func (m *MockRecurringPaymentDao) GetStateCountByActorId(ctx context.Context, actorId string) (map[string]int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStateCountByActorId", ctx, actorId)
	ret0, _ := ret[0].(map[string]int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStateCountByActorId indicates an expected call of GetStateCountByActorId.
func (mr *MockRecurringPaymentDaoMockRecorder) GetStateCountByActorId(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStateCountByActorId", reflect.TypeOf((*MockRecurringPaymentDao)(nil).GetStateCountByActorId), ctx, actorId)
}

// Update mocks base method.
func (m *MockRecurringPaymentDao) Update(ctx context.Context, recurringPayment *recurringpayment.RecurringPayment, updateMask []recurringpayment.RecurringPaymentFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, recurringPayment, updateMask)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockRecurringPaymentDaoMockRecorder) Update(ctx, recurringPayment, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRecurringPaymentDao)(nil).Update), ctx, recurringPayment, updateMask)
}

// UpdateAndChangeStatus mocks base method.
func (m *MockRecurringPaymentDao) UpdateAndChangeStatus(ctx context.Context, recurringPayment *recurringpayment.RecurringPayment, updateMask []recurringpayment.RecurringPaymentFieldMask, currentState, nextState recurringpayment.RecurringPaymentState) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAndChangeStatus", ctx, recurringPayment, updateMask, currentState, nextState)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAndChangeStatus indicates an expected call of UpdateAndChangeStatus.
func (mr *MockRecurringPaymentDaoMockRecorder) UpdateAndChangeStatus(ctx, recurringPayment, updateMask, currentState, nextState interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAndChangeStatus", reflect.TypeOf((*MockRecurringPaymentDao)(nil).UpdateAndChangeStatus), ctx, recurringPayment, updateMask, currentState, nextState)
}

// MockRecurringPaymentsActionDao is a mock of RecurringPaymentsActionDao interface.
type MockRecurringPaymentsActionDao struct {
	ctrl     *gomock.Controller
	recorder *MockRecurringPaymentsActionDaoMockRecorder
}

// MockRecurringPaymentsActionDaoMockRecorder is the mock recorder for MockRecurringPaymentsActionDao.
type MockRecurringPaymentsActionDaoMockRecorder struct {
	mock *MockRecurringPaymentsActionDao
}

// NewMockRecurringPaymentsActionDao creates a new mock instance.
func NewMockRecurringPaymentsActionDao(ctrl *gomock.Controller) *MockRecurringPaymentsActionDao {
	mock := &MockRecurringPaymentsActionDao{ctrl: ctrl}
	mock.recorder = &MockRecurringPaymentsActionDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRecurringPaymentsActionDao) EXPECT() *MockRecurringPaymentsActionDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRecurringPaymentsActionDao) Create(ctx context.Context, recurringPaymentAction *recurringpayment.RecurringPaymentsAction) (*recurringpayment.RecurringPaymentsAction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, recurringPaymentAction)
	ret0, _ := ret[0].(*recurringpayment.RecurringPaymentsAction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRecurringPaymentsActionDaoMockRecorder) Create(ctx, recurringPaymentAction interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRecurringPaymentsActionDao)(nil).Create), ctx, recurringPaymentAction)
}

// GetActionsByRecurringPaymentId mocks base method.
func (m *MockRecurringPaymentsActionDao) GetActionsByRecurringPaymentId(ctx context.Context, recurringPaymentId string, time time.Time, limit, offset int32, descending bool, options ...dao.FilterOption) ([]*recurringpayment.RecurringPaymentsAction, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, recurringPaymentId, time, limit, offset, descending}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetActionsByRecurringPaymentId", varargs...)
	ret0, _ := ret[0].([]*recurringpayment.RecurringPaymentsAction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActionsByRecurringPaymentId indicates an expected call of GetActionsByRecurringPaymentId.
func (mr *MockRecurringPaymentsActionDaoMockRecorder) GetActionsByRecurringPaymentId(ctx, recurringPaymentId, time, limit, offset, descending interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, recurringPaymentId, time, limit, offset, descending}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActionsByRecurringPaymentId", reflect.TypeOf((*MockRecurringPaymentsActionDao)(nil).GetActionsByRecurringPaymentId), varargs...)
}

// GetByClientRequestId mocks base method.
func (m *MockRecurringPaymentsActionDao) GetByClientRequestId(ctx context.Context, clientRequestId string, multiDbQuery bool) (*recurringpayment.RecurringPaymentsAction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByClientRequestId", ctx, clientRequestId, multiDbQuery)
	ret0, _ := ret[0].(*recurringpayment.RecurringPaymentsAction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByClientRequestId indicates an expected call of GetByClientRequestId.
func (mr *MockRecurringPaymentsActionDaoMockRecorder) GetByClientRequestId(ctx, clientRequestId, multiDbQuery interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByClientRequestId", reflect.TypeOf((*MockRecurringPaymentsActionDao)(nil).GetByClientRequestId), ctx, clientRequestId, multiDbQuery)
}

// GetById mocks base method.
func (m *MockRecurringPaymentsActionDao) GetById(ctx context.Context, recurringPaymentActionId string) (*recurringpayment.RecurringPaymentsAction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, recurringPaymentActionId)
	ret0, _ := ret[0].(*recurringpayment.RecurringPaymentsAction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockRecurringPaymentsActionDaoMockRecorder) GetById(ctx, recurringPaymentActionId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockRecurringPaymentsActionDao)(nil).GetById), ctx, recurringPaymentActionId)
}

// GetByRecurringPaymentId mocks base method.
func (m *MockRecurringPaymentsActionDao) GetByRecurringPaymentId(ctx context.Context, recurringPaymentId string, options ...dao.FilterOption) ([]*recurringpayment.RecurringPaymentsAction, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, recurringPaymentId}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByRecurringPaymentId", varargs...)
	ret0, _ := ret[0].([]*recurringpayment.RecurringPaymentsAction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByRecurringPaymentId indicates an expected call of GetByRecurringPaymentId.
func (mr *MockRecurringPaymentsActionDaoMockRecorder) GetByRecurringPaymentId(ctx, recurringPaymentId interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, recurringPaymentId}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByRecurringPaymentId", reflect.TypeOf((*MockRecurringPaymentsActionDao)(nil).GetByRecurringPaymentId), varargs...)
}

// GetByVendorRequestId mocks base method.
func (m *MockRecurringPaymentsActionDao) GetByVendorRequestId(ctx context.Context, vendorRequestId string) (*recurringpayment.RecurringPaymentsAction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByVendorRequestId", ctx, vendorRequestId)
	ret0, _ := ret[0].(*recurringpayment.RecurringPaymentsAction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByVendorRequestId indicates an expected call of GetByVendorRequestId.
func (mr *MockRecurringPaymentsActionDaoMockRecorder) GetByVendorRequestId(ctx, vendorRequestId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByVendorRequestId", reflect.TypeOf((*MockRecurringPaymentsActionDao)(nil).GetByVendorRequestId), ctx, vendorRequestId)
}

// GetSuccessfulActionsByRecurringPaymentId mocks base method.
func (m *MockRecurringPaymentsActionDao) GetSuccessfulActionsByRecurringPaymentId(ctx context.Context, recurringPaymentId string, time time.Time, limit, offset int32, descending bool, sortBy recurringpayment.RecurringPaymentActionFieldMask, options ...dao.FilterOption) ([]*recurringpayment.RecurringPaymentsAction, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, recurringPaymentId, time, limit, offset, descending, sortBy}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSuccessfulActionsByRecurringPaymentId", varargs...)
	ret0, _ := ret[0].([]*recurringpayment.RecurringPaymentsAction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSuccessfulActionsByRecurringPaymentId indicates an expected call of GetSuccessfulActionsByRecurringPaymentId.
func (mr *MockRecurringPaymentsActionDaoMockRecorder) GetSuccessfulActionsByRecurringPaymentId(ctx, recurringPaymentId, time, limit, offset, descending, sortBy interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, recurringPaymentId, time, limit, offset, descending, sortBy}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSuccessfulActionsByRecurringPaymentId", reflect.TypeOf((*MockRecurringPaymentsActionDao)(nil).GetSuccessfulActionsByRecurringPaymentId), varargs...)
}

// Update mocks base method.
func (m *MockRecurringPaymentsActionDao) Update(ctx context.Context, recurringPaymentAction *recurringpayment.RecurringPaymentsAction, updateMask []recurringpayment.RecurringPaymentActionFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, recurringPaymentAction, updateMask)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockRecurringPaymentsActionDaoMockRecorder) Update(ctx, recurringPaymentAction, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRecurringPaymentsActionDao)(nil).Update), ctx, recurringPaymentAction, updateMask)
}

// UpdateAndChangeStatus mocks base method.
func (m *MockRecurringPaymentsActionDao) UpdateAndChangeStatus(ctx context.Context, recurringPaymentAction *recurringpayment.RecurringPaymentsAction, updateMask []recurringpayment.RecurringPaymentActionFieldMask, currentState, nextState recurringpayment.ActionState) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAndChangeStatus", ctx, recurringPaymentAction, updateMask, currentState, nextState)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAndChangeStatus indicates an expected call of UpdateAndChangeStatus.
func (mr *MockRecurringPaymentsActionDaoMockRecorder) UpdateAndChangeStatus(ctx, recurringPaymentAction, updateMask, currentState, nextState interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAndChangeStatus", reflect.TypeOf((*MockRecurringPaymentsActionDao)(nil).UpdateAndChangeStatus), ctx, recurringPaymentAction, updateMask, currentState, nextState)
}

// MockStandingInstructionDao is a mock of StandingInstructionDao interface.
type MockStandingInstructionDao struct {
	ctrl     *gomock.Controller
	recorder *MockStandingInstructionDaoMockRecorder
}

// MockStandingInstructionDaoMockRecorder is the mock recorder for MockStandingInstructionDao.
type MockStandingInstructionDaoMockRecorder struct {
	mock *MockStandingInstructionDao
}

// NewMockStandingInstructionDao creates a new mock instance.
func NewMockStandingInstructionDao(ctrl *gomock.Controller) *MockStandingInstructionDao {
	mock := &MockStandingInstructionDao{ctrl: ctrl}
	mock.recorder = &MockStandingInstructionDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStandingInstructionDao) EXPECT() *MockStandingInstructionDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockStandingInstructionDao) Create(ctx context.Context, standingInstruction *standinginstruction.StandingInstruction) (*standinginstruction.StandingInstruction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, standingInstruction)
	ret0, _ := ret[0].(*standinginstruction.StandingInstruction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockStandingInstructionDaoMockRecorder) Create(ctx, standingInstruction interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockStandingInstructionDao)(nil).Create), ctx, standingInstruction)
}

// GetByRecurringPaymentId mocks base method.
func (m *MockStandingInstructionDao) GetByRecurringPaymentId(ctx context.Context, recurringPaymentId string) (*standinginstruction.StandingInstruction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByRecurringPaymentId", ctx, recurringPaymentId)
	ret0, _ := ret[0].(*standinginstruction.StandingInstruction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByRecurringPaymentId indicates an expected call of GetByRecurringPaymentId.
func (mr *MockStandingInstructionDaoMockRecorder) GetByRecurringPaymentId(ctx, recurringPaymentId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByRecurringPaymentId", reflect.TypeOf((*MockStandingInstructionDao)(nil).GetByRecurringPaymentId), ctx, recurringPaymentId)
}

// Update mocks base method.
func (m *MockStandingInstructionDao) Update(ctx context.Context, standingInstruction *standinginstruction.StandingInstruction, updateMask []standinginstruction.StandingInstructionFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, standingInstruction, updateMask)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockStandingInstructionDaoMockRecorder) Update(ctx, standingInstruction, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockStandingInstructionDao)(nil).Update), ctx, standingInstruction, updateMask)
}

// MockStandingInstructionRequestDao is a mock of StandingInstructionRequestDao interface.
type MockStandingInstructionRequestDao struct {
	ctrl     *gomock.Controller
	recorder *MockStandingInstructionRequestDaoMockRecorder
}

// MockStandingInstructionRequestDaoMockRecorder is the mock recorder for MockStandingInstructionRequestDao.
type MockStandingInstructionRequestDaoMockRecorder struct {
	mock *MockStandingInstructionRequestDao
}

// NewMockStandingInstructionRequestDao creates a new mock instance.
func NewMockStandingInstructionRequestDao(ctrl *gomock.Controller) *MockStandingInstructionRequestDao {
	mock := &MockStandingInstructionRequestDao{ctrl: ctrl}
	mock.recorder = &MockStandingInstructionRequestDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStandingInstructionRequestDao) EXPECT() *MockStandingInstructionRequestDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockStandingInstructionRequestDao) Create(ctx context.Context, standingInstructionRequest *standinginstruction.StandingInstructionRequest) (*standinginstruction.StandingInstructionRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, standingInstructionRequest)
	ret0, _ := ret[0].(*standinginstruction.StandingInstructionRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockStandingInstructionRequestDaoMockRecorder) Create(ctx, standingInstructionRequest interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockStandingInstructionRequestDao)(nil).Create), ctx, standingInstructionRequest)
}

// GetByRequestId mocks base method.
func (m *MockStandingInstructionRequestDao) GetByRequestId(ctx context.Context, requestId string) (*standinginstruction.StandingInstructionRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByRequestId", ctx, requestId)
	ret0, _ := ret[0].(*standinginstruction.StandingInstructionRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByRequestId indicates an expected call of GetByRequestId.
func (mr *MockStandingInstructionRequestDaoMockRecorder) GetByRequestId(ctx, requestId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByRequestId", reflect.TypeOf((*MockStandingInstructionRequestDao)(nil).GetByRequestId), ctx, requestId)
}

// GetByStandingInstructionIdAndRequestType mocks base method.
func (m *MockStandingInstructionRequestDao) GetByStandingInstructionIdAndRequestType(ctx context.Context, standingInstructionId string, requestType standinginstruction.RequestType) (*standinginstruction.StandingInstructionRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByStandingInstructionIdAndRequestType", ctx, standingInstructionId, requestType)
	ret0, _ := ret[0].(*standinginstruction.StandingInstructionRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByStandingInstructionIdAndRequestType indicates an expected call of GetByStandingInstructionIdAndRequestType.
func (mr *MockStandingInstructionRequestDaoMockRecorder) GetByStandingInstructionIdAndRequestType(ctx, standingInstructionId, requestType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByStandingInstructionIdAndRequestType", reflect.TypeOf((*MockStandingInstructionRequestDao)(nil).GetByStandingInstructionIdAndRequestType), ctx, standingInstructionId, requestType)
}

// Update mocks base method.
func (m *MockStandingInstructionRequestDao) Update(ctx context.Context, standingInstructionRequest *standinginstruction.StandingInstructionRequest, updateMask []standinginstruction.StandingInstructionRequestFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, standingInstructionRequest, updateMask)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockStandingInstructionRequestDaoMockRecorder) Update(ctx, standingInstructionRequest, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockStandingInstructionRequestDao)(nil).Update), ctx, standingInstructionRequest, updateMask)
}

// UpdateAndChangeStatus mocks base method.
func (m *MockStandingInstructionRequestDao) UpdateAndChangeStatus(ctx context.Context, standingInstructionRequest *standinginstruction.StandingInstructionRequest, updateMask []standinginstruction.StandingInstructionRequestFieldMask, currentState, nextState standinginstruction.State) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAndChangeStatus", ctx, standingInstructionRequest, updateMask, currentState, nextState)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAndChangeStatus indicates an expected call of UpdateAndChangeStatus.
func (mr *MockStandingInstructionRequestDaoMockRecorder) UpdateAndChangeStatus(ctx, standingInstructionRequest, updateMask, currentState, nextState interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAndChangeStatus", reflect.TypeOf((*MockStandingInstructionRequestDao)(nil).UpdateAndChangeStatus), ctx, standingInstructionRequest, updateMask, currentState, nextState)
}

// MockRecurringPaymentsVendorDetailsDao is a mock of RecurringPaymentsVendorDetailsDao interface.
type MockRecurringPaymentsVendorDetailsDao struct {
	ctrl     *gomock.Controller
	recorder *MockRecurringPaymentsVendorDetailsDaoMockRecorder
}

// MockRecurringPaymentsVendorDetailsDaoMockRecorder is the mock recorder for MockRecurringPaymentsVendorDetailsDao.
type MockRecurringPaymentsVendorDetailsDaoMockRecorder struct {
	mock *MockRecurringPaymentsVendorDetailsDao
}

// NewMockRecurringPaymentsVendorDetailsDao creates a new mock instance.
func NewMockRecurringPaymentsVendorDetailsDao(ctrl *gomock.Controller) *MockRecurringPaymentsVendorDetailsDao {
	mock := &MockRecurringPaymentsVendorDetailsDao{ctrl: ctrl}
	mock.recorder = &MockRecurringPaymentsVendorDetailsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRecurringPaymentsVendorDetailsDao) EXPECT() *MockRecurringPaymentsVendorDetailsDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRecurringPaymentsVendorDetailsDao) Create(ctx context.Context, vendorDetails *recurringpayment.RecurringPaymentsVendorDetails) (*recurringpayment.RecurringPaymentsVendorDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, vendorDetails)
	ret0, _ := ret[0].(*recurringpayment.RecurringPaymentsVendorDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRecurringPaymentsVendorDetailsDaoMockRecorder) Create(ctx, vendorDetails interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRecurringPaymentsVendorDetailsDao)(nil).Create), ctx, vendorDetails)
}

// Get mocks base method.
func (m *MockRecurringPaymentsVendorDetailsDao) Get(ctx context.Context, pageToken *pagination.PageToken, pageSize uint32, filters ...dao.FilterOption) ([]*recurringpayment.RecurringPaymentsVendorDetails, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, pageToken, pageSize}
	for _, a := range filters {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Get", varargs...)
	ret0, _ := ret[0].([]*recurringpayment.RecurringPaymentsVendorDetails)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Get indicates an expected call of Get.
func (mr *MockRecurringPaymentsVendorDetailsDaoMockRecorder) Get(ctx, pageToken, pageSize interface{}, filters ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, pageToken, pageSize}, filters...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockRecurringPaymentsVendorDetailsDao)(nil).Get), varargs...)
}

// GetByActorId mocks base method.
func (m *MockRecurringPaymentsVendorDetailsDao) GetByActorId(ctx context.Context, actorId string) ([]*recurringpayment.RecurringPaymentsVendorDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorId", ctx, actorId)
	ret0, _ := ret[0].([]*recurringpayment.RecurringPaymentsVendorDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockRecurringPaymentsVendorDetailsDaoMockRecorder) GetByActorId(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockRecurringPaymentsVendorDetailsDao)(nil).GetByActorId), ctx, actorId)
}

// GetById mocks base method.
func (m *MockRecurringPaymentsVendorDetailsDao) GetById(ctx context.Context, id string) (*recurringpayment.RecurringPaymentsVendorDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*recurringpayment.RecurringPaymentsVendorDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockRecurringPaymentsVendorDetailsDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockRecurringPaymentsVendorDetailsDao)(nil).GetById), ctx, id)
}

// GetByRecurringPaymentId mocks base method.
func (m *MockRecurringPaymentsVendorDetailsDao) GetByRecurringPaymentId(ctx context.Context, recurringPaymentId string) (*recurringpayment.RecurringPaymentsVendorDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByRecurringPaymentId", ctx, recurringPaymentId)
	ret0, _ := ret[0].(*recurringpayment.RecurringPaymentsVendorDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByRecurringPaymentId indicates an expected call of GetByRecurringPaymentId.
func (mr *MockRecurringPaymentsVendorDetailsDaoMockRecorder) GetByRecurringPaymentId(ctx, recurringPaymentId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByRecurringPaymentId", reflect.TypeOf((*MockRecurringPaymentsVendorDetailsDao)(nil).GetByRecurringPaymentId), ctx, recurringPaymentId)
}

// GetByVendorCustomerId mocks base method.
func (m *MockRecurringPaymentsVendorDetailsDao) GetByVendorCustomerId(ctx context.Context, vendorCustomerId string) (*recurringpayment.RecurringPaymentsVendorDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByVendorCustomerId", ctx, vendorCustomerId)
	ret0, _ := ret[0].(*recurringpayment.RecurringPaymentsVendorDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByVendorCustomerId indicates an expected call of GetByVendorCustomerId.
func (mr *MockRecurringPaymentsVendorDetailsDaoMockRecorder) GetByVendorCustomerId(ctx, vendorCustomerId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByVendorCustomerId", reflect.TypeOf((*MockRecurringPaymentsVendorDetailsDao)(nil).GetByVendorCustomerId), ctx, vendorCustomerId)
}

// GetByVendorPaymentId mocks base method.
func (m *MockRecurringPaymentsVendorDetailsDao) GetByVendorPaymentId(ctx context.Context, vendorPaymentId string) (*recurringpayment.RecurringPaymentsVendorDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByVendorPaymentId", ctx, vendorPaymentId)
	ret0, _ := ret[0].(*recurringpayment.RecurringPaymentsVendorDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByVendorPaymentId indicates an expected call of GetByVendorPaymentId.
func (mr *MockRecurringPaymentsVendorDetailsDaoMockRecorder) GetByVendorPaymentId(ctx, vendorPaymentId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByVendorPaymentId", reflect.TypeOf((*MockRecurringPaymentsVendorDetailsDao)(nil).GetByVendorPaymentId), ctx, vendorPaymentId)
}

// Update mocks base method.
func (m *MockRecurringPaymentsVendorDetailsDao) Update(ctx context.Context, vendorDetails *recurringpayment.RecurringPaymentsVendorDetails, updateMask []recurringpayment.RecurringPaymentVendorDetailsFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, vendorDetails, updateMask)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockRecurringPaymentsVendorDetailsDaoMockRecorder) Update(ctx, vendorDetails, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRecurringPaymentsVendorDetailsDao)(nil).Update), ctx, vendorDetails, updateMask)
}
