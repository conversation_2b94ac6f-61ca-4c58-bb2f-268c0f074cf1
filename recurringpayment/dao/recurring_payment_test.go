// nolint: goimports
package dao_test

import (
	"context"
	"reflect"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	gmoney "google.golang.org/genproto/googleapis/type/money"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/idgen"

	types "github.com/epifi/gamma/api/typesv2"
	rpServerConfig "github.com/epifi/gamma/recurringpayment/config/server"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/stretchr/testify/assert"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/money"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	pb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/recurringpayment/dao"
)

type recurringPaymentTestSuite struct {
	conf                *rpServerConfig.Config
	db                  *gorm.DB
	recurringPaymentDao dao.RecurringPaymentDao
	dbName              string
}

func initialiseRpDaoTestSuite(t *testing.T) (*recurringPaymentTestSuite, func()) {
	idg := idgen.NewDomainIdGenerator(idgen.NewClock())
	dbResourceProvider, release := dbResourceProviderPool.GetDbConnProvider(t)
	attrIdGen := idgen.NewAttributedIdGen[*dao.RecurringPaymentAttributes](idgen.NewClock())
	rpts := newRecurringPaymentTestSuite(nil, dao.NewRecurringPaymentDao(nil, idg, dbResourceProvider, true, attrIdGen))
	return &rpts, func() {
		release(entitySegregatedTestTables)
	}
}

func newRecurringPaymentTestSuite(db *gorm.DB, recurringPaymentDao dao.RecurringPaymentDao) recurringPaymentTestSuite {
	return recurringPaymentTestSuite{db: db, recurringPaymentDao: recurringPaymentDao}
}

var (
	affectedTestTables         = []string{"recurring_payments", "recurring_payments_actions", "standing_instructions", "standing_instruction_requests", "recurring_payments_vendor_details"}
	entitySegregatedTestTables = map[commontypes.Ownership][]string{
		commontypes.Ownership_EPIFI_TECH: {"recurring_payments_vendor_details", "recurring_payments", "recurring_payments_actions"},
	}
	affectedTestTablesMap = map[commontypes.Ownership]map[commontypes.UseCase][]string{
		commontypes.Ownership_EPIFI_TECH: {commontypes.UseCase_USE_CASE_RECURRING_PAYMENTS_VENDOR_DETAILS: affectedTestTables},
	}
	ctxWithLlOwnership = epificontext.WithOwnership(context.Background(), commontypes.Ownership_LIQUILOANS_PL)
)

func TestRecurringPaymentDaoCRDB_Create(t *testing.T) {

	// Clean database, run migrations and load fixtures

	startDate := timestampPb.New(time.Now())
	endDate := timestampPb.New(time.Now().Add(48 * time.Hour))

	recurringPayment1 := &pb.RecurringPayment{
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(**********).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_DAILY,
			Day:              10,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
		PauseInterval: &types.Interval{
			StartTime: timestampPb.Now(),
			EndTime:   timestampPb.New(time.Now().Add(10 * time.Minute)),
		},
	}

	recurringPayment2 := &pb.RecurringPayment{
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(**********).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_DAILY,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYEE,
	}

	tests := []struct {
		name             string
		recurringPayment *pb.RecurringPayment
		want             *pb.RecurringPayment
		wantErr          bool
	}{
		{
			name:             "created entry successfully",
			recurringPayment: recurringPayment1,
			want:             recurringPayment1,
			wantErr:          false,
		},
		{
			name:             "created entry with start and end date successfully",
			recurringPayment: recurringPayment2,
			want:             recurringPayment2,
			wantErr:          false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rpts, release := initialiseRpDaoTestSuite(t)
			defer release()
			got, err := rpts.recurringPaymentDao.Create(context.Background(), tt.recurringPayment, commontypes.Ownership_EPIFI_TECH)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want != nil && got != nil {
				tt.want.Id = got.Id
				tt.want.ExternalId = got.ExternalId
			}
			assertRecurringPayment(t, tt.want, got)
		})
	}
}

func TestRecurringPaymentDaoCRDB_GetById(t *testing.T) {

	// Clean database, run migrations and load fixtures

	recurringPayment1 := &pb.RecurringPayment{
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(**********).Pb,
		AmountType:  pb.AmountType_EXACT,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_DAILY,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		PauseInterval: &types.Interval{
			StartTime: timestampPb.Now(),
			EndTime:   timestampPb.New(time.Now().Add(10 * time.Minute)),
		},
	}
	rpts, release := initialiseRpDaoTestSuite(t)
	defer release()
	recurringPayment1, _ = rpts.recurringPaymentDao.Create(context.Background(), recurringPayment1, commontypes.Ownership_EPIFI_TECH)

	tests := []struct {
		name               string
		recurringPaymentId string
		want               *pb.RecurringPayment
		wantErr            bool
	}{
		{
			name:               "fetched successfully",
			recurringPaymentId: recurringPayment1.GetId(),
			want:               recurringPayment1,
			wantErr:            false,
		},
		{
			name:               "incorrect recurring payment id",
			recurringPaymentId: "random-id",
			want:               nil,
			wantErr:            true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := rpts.recurringPaymentDao.GetById(context.Background(), tt.recurringPaymentId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assertRecurringPayment(t, tt.want, got)
		})
	}
	t.Run("creation and query from ll db", func(t *testing.T) {
		recurringPayment2 := &pb.RecurringPayment{
			FromActorId: "actor-1",
			ToActorId:   "actor-2",
			Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
			PiFrom:      "pi-1",
			PiTo:        "pi-2",
			Amount:      money.AmountINR(**********).Pb,
			AmountType:  pb.AmountType_MAXIMUM,
			RecurrenceRule: &pb.RecurrenceRule{
				AllowedFrequency: pb.AllowedFrequency_DAILY,
			},
			MaximumAllowedTxns:       10,
			PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
			PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
			State:                    pb.RecurringPaymentState_ACTIVATED,
			Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
			Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
			UiEntryPoint:             pb.UIEntryPoint_FIT,
			InitiatedBy:              pb.InitiatedBy_PAYER,
		}
		createdRecurringPayment, err := rpts.recurringPaymentDao.Create(context.Background(), recurringPayment2, commontypes.Ownership_LIQUILOANS_PL)
		require.NoError(t, err)
		// querying with context.Background should also be able to give data from ll db
		got, err := rpts.recurringPaymentDao.GetById(context.Background(), createdRecurringPayment.GetId())
		require.NoError(t, err)
		assertRecurringPayment(t, createdRecurringPayment, got)
	})
}

func TestRecurringPaymentDaoCRDB_GetByIds(t *testing.T) {

	// Clean database, run migrations and load fixtures

	recurringPayment1 := &pb.RecurringPayment{
		Id:          "RP-1",
		FromActorId: "actor-3",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount: &gmoney.Money{
			CurrencyCode: "INR",
			Units:        2000,
			Nanos:        0,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_DAILY,
			Day:              10,
		},
		MaximumAllowedTxns:       10,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_IMPS,
		State:                    pb.RecurringPaymentState_CREATION_INITIATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
		AmountType:               pb.AmountType_EXACT,
		ExternalId:               "external-id-1",
		ShareToPayee:             true,
		PaymentRoute:             pb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE,
		EntityOwnership:          commontypes.Ownership_EPIFI_TECH,
	}

	tests := []struct {
		name                string
		recurringPaymentIds []string
		want                []*pb.RecurringPayment
		wantErr             bool
	}{
		{
			name:                "should fetch successfully if recurring payment exists",
			recurringPaymentIds: []string{recurringPayment1.GetId()},
			want:                []*pb.RecurringPayment{recurringPayment1},
			wantErr:             false,
		},
		{
			name:    "should fail to fetch if recurringPaymentId list is empty",
			wantErr: true,
		},
		{
			name:                "should fail to fetch recurringPaymentId list contains empty strings",
			recurringPaymentIds: []string{""},
			wantErr:             true,
		},
		{
			name:                "should fail to fetch if all recurring payment id are incorrect",
			recurringPaymentIds: []string{"random-id"},
			want:                nil,
			wantErr:             true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rpts, release := initialiseRpDaoTestSuite(t)
			defer release()
			got, err := rpts.recurringPaymentDao.GetByIds(context.Background(), tt.recurringPaymentIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !compareRecurringPaymentLists(tt.want, got) {
				t.Errorf("GetByIds() got = %v, want = %v", got, tt.want)
			}
		})
	}
	t.Run("creation and query from ll db", func(t *testing.T) {
		rpts, release := initialiseRpDaoTestSuite(t)
		defer release()
		recurringPayment2 := &pb.RecurringPayment{
			FromActorId: "actor-1",
			ToActorId:   "actor-2",
			Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
			PiFrom:      "pi-1",
			PiTo:        "pi-2",
			Amount:      money.AmountINR(**********).Pb,
			AmountType:  pb.AmountType_MAXIMUM,
			RecurrenceRule: &pb.RecurrenceRule{
				AllowedFrequency: pb.AllowedFrequency_DAILY,
			},
			MaximumAllowedTxns:       10,
			PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
			PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
			State:                    pb.RecurringPaymentState_ACTIVATED,
			Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
			Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
			UiEntryPoint:             pb.UIEntryPoint_FIT,
			InitiatedBy:              pb.InitiatedBy_PAYER,
		}
		createdRecurringPayment, err := rpts.recurringPaymentDao.Create(context.Background(), recurringPayment2, commontypes.Ownership_LIQUILOANS_PL)
		require.NoError(t, err)
		expected := []*pb.RecurringPayment{
			createdRecurringPayment,
			recurringPayment1,
		}
		got, err := rpts.recurringPaymentDao.GetByIds(context.Background(), []string{createdRecurringPayment.GetId(), recurringPayment1.GetId(), "random-id"})
		require.NoError(t, err)
		require.Len(t, got, 2)
		idToRp := make(map[string]*pb.RecurringPayment)
		for _, rp := range got {
			idToRp[rp.GetId()] = rp
		}
		for _, exp := range expected {
			assertRecurringPayment(t, exp, idToRp[exp.GetId()])
		}
	})
}
func compareRecurringPaymentLists(expected, actual []*pb.RecurringPayment) bool {
	if len(actual) != len(expected) {
		return false
	}

	for i := 0; i < len(actual); i++ {
		expected[i].CreatedAt = actual[i].CreatedAt
		expected[i].UpdatedAt = actual[i].UpdatedAt
		expected[i].Interval = actual[i].Interval
		if diff := cmp.Diff(actual, expected, protocmp.Transform()); diff != "" {
			return false
		}
	}
	return true
}

func TestRecurringPaymentDaoCRDB_GetByExternalId(t *testing.T) {

	// Clean database, run migrations and load fixtures

	recurringPayment1 := &pb.RecurringPayment{
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(**********).Pb,
		AmountType:  pb.AmountType_EXACT,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_DAILY,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
	}
	rpts, release := initialiseRpDaoTestSuite(t)
	defer release()
	recurringPayment1, _ = rpts.recurringPaymentDao.Create(context.Background(), recurringPayment1, commontypes.Ownership_EPIFI_TECH)

	tests := []struct {
		name       string
		externalId string
		want       *pb.RecurringPayment
		wantErr    bool
	}{
		{
			name:       "fetched successfully",
			externalId: recurringPayment1.GetExternalId(),
			want:       recurringPayment1,
			wantErr:    false,
		},
		{
			name:       "incorrect externalId id",
			externalId: "random-id",
			want:       nil,
			wantErr:    true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := rpts.recurringPaymentDao.GetByExternalId(context.Background(), tt.externalId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExternalId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assertRecurringPayment(t, tt.want, got)
		})
	}
}

func assertRecurringPayment(t *testing.T, expected, actual *pb.RecurringPayment) {
	if expected == nil && actual == nil {
		return
	}

	expected.CreatedAt = actual.CreatedAt
	expected.UpdatedAt = actual.UpdatedAt
	if diff := cmp.Diff(actual, expected, protocmp.Transform()); diff != "" {
		t.Errorf("unexpected difference:\n%v", diff)
	}
}

func TestRecurringPaymentDaoCRDB_Update(t *testing.T) {

	// Clean database, run migrations and load fixtures

	recurringPayment1 := &pb.RecurringPayment{
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(**********).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_DAILY,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}

	recurringPayment2 := &pb.RecurringPayment{
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(**********).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_DAILY,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}

	recurringPayment3 := &pb.RecurringPayment{
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(**********).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_DAILY,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}
	rpts, release := initialiseRpDaoTestSuite(t)
	defer release()
	recurringPayment1, _ = rpts.recurringPaymentDao.Create(context.Background(), recurringPayment1, commontypes.Ownership_EPIFI_TECH)
	recurringPayment1.RecurrenceRule.Day = 10
	recurringPayment1.RecurrenceRule.AllowedFrequency = pb.AllowedFrequency_MONTHLY
	recurringPayment1.State = pb.RecurringPaymentState_EXPIRED

	recurringPayment2, _ = rpts.recurringPaymentDao.Create(context.Background(), recurringPayment2, commontypes.Ownership_EPIFI_TECH)
	recurringPayment2.Interval = &types.Interval{StartTime: &timestampPb.Timestamp{
		Seconds: 100000,
		Nanos:   2000,
	}}

	recurringPayment3, _ = rpts.recurringPaymentDao.Create(context.Background(), recurringPayment3, commontypes.Ownership_EPIFI_TECH)
	recurringPayment3.Interval = &types.Interval{
		StartTime: &timestampPb.Timestamp{
			Seconds: 1000,
			Nanos:   20000,
		},
		EndTime: &timestampPb.Timestamp{
			Seconds: 1000,
			Nanos:   20000,
		},
	}

	tests := []struct {
		name             string
		recurringPayment *pb.RecurringPayment
		updateMask       []pb.RecurringPaymentFieldMask
		wantErr          bool
	}{
		{
			name:             "updated successfully",
			recurringPayment: recurringPayment1,
			updateMask:       []pb.RecurringPaymentFieldMask{pb.RecurringPaymentFieldMask_STATE, pb.RecurringPaymentFieldMask_RECURRENCE_RULE},
			wantErr:          false,
		},
		{
			name:             "updated start date successfully",
			recurringPayment: recurringPayment2,
			updateMask:       []pb.RecurringPaymentFieldMask{pb.RecurringPaymentFieldMask_START_DATE},
			wantErr:          false,
		},
		{
			name:             "updated start date and end date successfully",
			recurringPayment: recurringPayment3,
			updateMask:       []pb.RecurringPaymentFieldMask{pb.RecurringPaymentFieldMask_START_DATE, pb.RecurringPaymentFieldMask_END_DATE},
			wantErr:          false,
		},
		{
			name:             "failed due to empty id",
			recurringPayment: &pb.RecurringPayment{Id: ""},
			updateMask:       nil,
			wantErr:          true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := rpts.recurringPaymentDao.Update(context.Background(), tt.recurringPayment, tt.updateMask); (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
			}
			if !tt.wantErr {
				got, err := rpts.recurringPaymentDao.GetById(context.Background(), tt.recurringPayment.Id)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetByCardId() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				assertRecurringPayment(t, tt.recurringPayment, got)
			}
		})
	}
}

func TestRecurringPaymentDaoCRDB_GetByFromActorId(t *testing.T) {

	// Clean database, run migrations and load fixtures

	recurringPayment1 := &pb.RecurringPayment{
		FromActorId: "actor-10",
		ToActorId:   "actor-20",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(**********).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_DAILY,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}

	rpts, release := initialiseRpDaoTestSuite(t)
	defer release()

	recurringPayment1, _ = rpts.recurringPaymentDao.Create(context.Background(), recurringPayment1, commontypes.Ownership_EPIFI_TECH)

	tests := []struct {
		name    string
		actorId string
		options []dao.FilterOption
		want    []*pb.RecurringPayment
		wantErr bool
	}{
		{
			name:    "fetched successfully",
			actorId: "actor-10",
			want:    []*pb.RecurringPayment{recurringPayment1},
			wantErr: false,
		},
		{
			name:    "fetched successfully with pi from filter",
			actorId: "actor-10",
			options: []dao.FilterOption{dao.WithFromPIsFilter([]string{"pi-1"})},
			want:    []*pb.RecurringPayment{recurringPayment1},
			wantErr: false,
		},
		{
			name:    "fetched successfully with to_actor filter",
			actorId: "actor-10",
			options: []dao.FilterOption{dao.WithToActorFilter("actor-20")},
			want:    []*pb.RecurringPayment{recurringPayment1},
			wantErr: false,
		},
		{
			name:    "record not found with pi from filter",
			actorId: "actor-10",
			options: []dao.FilterOption{dao.WithFromPIsFilter([]string{"pi-2"})},
			want:    nil,
			wantErr: true,
		},
		{
			name:    "fetched successfully with pi from and pi_to filter",
			actorId: "actor-10",
			options: []dao.FilterOption{dao.WithFromPIsFilter([]string{"pi-1"}), dao.WithToPIsFilter([]string{"pi-2"})},
			want:    nil,
			wantErr: false,
		},
		{
			name:    "invalid actor id",
			actorId: "random",
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := rpts.recurringPaymentDao.GetByFromActorId(context.Background(), tt.actorId, tt.options...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByFromActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want != nil {
				for i := range tt.want {
					assertRecurringPayment(t, tt.want[i], got[i])
				}
			}
		})
	}
}

func TestRecurringPaymentDaoCRDB_GetByActorId(t *testing.T) {

	// Clean database, run migrations and load fixtures

	timeForRecords := time.Now().Add(7 * time.Minute)

	recurringPayment1 := &pb.RecurringPayment{
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(**********).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_DAILY,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
		ShareToPayee:             true,
	}

	recurringPayment2 := &pb.RecurringPayment{
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(**********).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_DAILY,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_REVOKED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYEE,
		ShareToPayee:             true,
	}

	recurringPayment3 := &pb.RecurringPayment{
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(**********).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_DAILY,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_REVOKED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
		ShareToPayee:             false,
	}

	rpts, release := initialiseRpDaoTestSuite(t)
	defer release()

	recurringPayment1, err := rpts.recurringPaymentDao.Create(context.Background(), recurringPayment1, commontypes.Ownership_EPIFI_TECH)
	assert.Nil(t, err)
	recurringPayment2, err = rpts.recurringPaymentDao.Create(context.Background(), recurringPayment2, commontypes.Ownership_EPIFI_TECH)
	assert.Nil(t, err)
	recurringPayment3, err = rpts.recurringPaymentDao.Create(context.Background(), recurringPayment3, commontypes.Ownership_EPIFI_TECH)
	assert.Nil(t, err)
	type args struct {
		actorId    string
		tm         time.Time
		limit      int32
		offset     int32
		descending bool
		states     []pb.RecurringPaymentState
	}

	tests := []struct {
		name    string
		args    args
		want    []*pb.RecurringPayment
		wantErr bool
	}{
		{
			name: "fetched records successfully when current actor is payee",
			args: args{actorId: "actor-2",
				descending: true,
				limit:      5,
				tm:         timeForRecords,
				offset:     0,
				states:     []pb.RecurringPaymentState{pb.RecurringPaymentState_ACTIVATED, pb.RecurringPaymentState_REVOKED}},
			want:    []*pb.RecurringPayment{recurringPayment2, recurringPayment1},
			wantErr: false,
		},
		{
			name: "fetched records successfully when current actor is payer",
			args: args{actorId: "actor-1",
				descending: true,
				limit:      5,
				tm:         timeForRecords,
				offset:     0,
				states:     []pb.RecurringPaymentState{pb.RecurringPaymentState_ACTIVATED, pb.RecurringPaymentState_REVOKED}},
			want:    []*pb.RecurringPayment{recurringPayment3, recurringPayment2, recurringPayment1},
			wantErr: false,
		},
		{
			name:    "error due to invalid actor id",
			args:    args{actorId: "a-1"},
			want:    nil,
			wantErr: true,
		},
		{
			name: "error due to no records found corresponding to the state",
			args: args{actorId: "actor-1",
				descending: true,
				limit:      5,
				tm:         timeForRecords,
				offset:     0,
				states:     []pb.RecurringPaymentState{pb.RecurringPaymentState_REVOKE_INITIATED}},
			want:    nil,
			wantErr: true,
		},
		{
			name: "fetched records successfully in all states when no state is mentioned",
			args: args{actorId: "actor-1",
				descending: true,
				limit:      5,
				tm:         timeForRecords,
				offset:     0,
			},
			want:    []*pb.RecurringPayment{recurringPayment3, recurringPayment2, recurringPayment1},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := rpts.recurringPaymentDao.GetByActorId(context.Background(), tt.args.actorId, tt.args.tm,
				tt.args.limit, tt.args.offset, tt.args.descending, tt.args.states)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.Len(t, got, len(tt.want))
			}
			if tt.want != nil {
				for i := range tt.want {
					assertRecurringPayment(t, tt.want[i], got[i])
				}
			}
		})
	}

}

func TestRecurringPaymentDaoCRDB_GetByActorIdInTimeWindow(t *testing.T) {

	// Clean database, run migrations and load fixtures

	fromTime := time.Now().Add(-7 * time.Minute)
	toTime1 := time.Now().Add(10 * time.Minute)
	toTime2 := time.Now().Add(-5 * time.Minute)

	recurringPayment1 := &pb.RecurringPayment{
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(**********).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_DAILY,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
		ShareToPayee:             true,
	}

	recurringPayment2 := &pb.RecurringPayment{
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(**********).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_DAILY,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_REVOKED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYEE,
		ShareToPayee:             true,
	}

	recurringPayment3 := &pb.RecurringPayment{
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(**********).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_DAILY,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_REVOKED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
		ShareToPayee:             false,
	}

	rpts, release := initialiseRpDaoTestSuite(t)
	defer release()

	recurringPayment1, err := rpts.recurringPaymentDao.Create(context.Background(), recurringPayment1, commontypes.Ownership_EPIFI_TECH)
	assert.Nil(t, err)
	recurringPayment2, err = rpts.recurringPaymentDao.Create(context.Background(), recurringPayment2, commontypes.Ownership_EPIFI_TECH)
	assert.Nil(t, err)
	recurringPayment3, err = rpts.recurringPaymentDao.Create(context.Background(), recurringPayment3, commontypes.Ownership_EPIFI_TECH)
	assert.Nil(t, err)
	type args struct {
		actorId    string
		fromTime   time.Time
		toTime     time.Time
		limit      int32
		offset     int32
		descending bool
		states     []pb.RecurringPaymentState
	}

	tests := []struct {
		name    string
		args    args
		want    []*pb.RecurringPayment
		wantErr bool
	}{
		{
			name: "fetched records successfully in the given tie window",
			args: args{actorId: "actor-2",
				descending: true,
				limit:      5,
				fromTime:   fromTime,
				toTime:     toTime1,
				offset:     0,
			},
			want:    []*pb.RecurringPayment{recurringPayment3, recurringPayment2, recurringPayment1},
			wantErr: false,
		},
		{
			name: "no record found in the time window",
			args: args{actorId: "actor-1",
				descending: true,
				limit:      5,
				fromTime:   fromTime,
				toTime:     toTime2,
				offset:     0,
				states:     []pb.RecurringPaymentState{pb.RecurringPaymentState_ACTIVATED, pb.RecurringPaymentState_REVOKED}},
			wantErr: true,
		},
		{
			name:    "error due to invalid actor id",
			args:    args{actorId: "a-1"},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := rpts.recurringPaymentDao.GetByActorIdInTimeWindow(
				context.Background(),
				tt.args.actorId,
				tt.args.fromTime,
				tt.args.toTime,
				tt.args.limit,
				tt.args.offset,
				tt.args.descending,
				tt.args.states,
			)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.Len(t, got, len(tt.want))
			}
			if tt.want != nil {
				for i := range tt.want {
					assertRecurringPayment(t, tt.want[i], got[i])
				}
			}
		})
	}

}

func TestRecurringPaymentDaoCRDB_UpdateAndChangeStatus(t *testing.T) {

	// Clean database, run migrations and load fixtures

	recurringPayment1 := &pb.RecurringPayment{
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(**********).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_DAILY,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}
	rpts, release := initialiseRpDaoTestSuite(t)
	defer release()
	recurringPayment1, _ = rpts.recurringPaymentDao.Create(context.Background(), recurringPayment1, commontypes.Ownership_EPIFI_TECH)
	recurringPayment1.RecurrenceRule.Day = 10
	recurringPayment1.RecurrenceRule.AllowedFrequency = pb.AllowedFrequency_MONTHLY

	recurringPayment1.State = pb.RecurringPaymentState_EXPIRED

	tests := []struct {
		name             string
		recurringPayment *pb.RecurringPayment
		updateMask       []pb.RecurringPaymentFieldMask
		currentState     pb.RecurringPaymentState
		nextState        pb.RecurringPaymentState
		wantErr          bool
	}{
		{
			name:             "updated successfully",
			recurringPayment: recurringPayment1,
			updateMask:       []pb.RecurringPaymentFieldMask{pb.RecurringPaymentFieldMask_STATE, pb.RecurringPaymentFieldMask_RECURRENCE_RULE},
			currentState:     pb.RecurringPaymentState_ACTIVATED,
			nextState:        pb.RecurringPaymentState_EXPIRED,
			wantErr:          false,
		},
		{
			name:             "failed due to empty id",
			recurringPayment: &pb.RecurringPayment{Id: ""},
			wantErr:          true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := rpts.recurringPaymentDao.UpdateAndChangeStatus(context.Background(), tt.recurringPayment,
				tt.updateMask, tt.currentState, tt.nextState); (err != nil) != tt.wantErr {
				t.Errorf("UpdateAndChangeStatus() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRecurringPaymentDaoCRDB_GetStateCountByActorId(t *testing.T) {

	// Clean database, run migrations and load fixtures

	recurringPayment1 := &pb.RecurringPayment{
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(**********).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_DAILY,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
		ShareToPayee:             true,
	}

	recurringPayment2 := &pb.RecurringPayment{
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(**********).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_DAILY,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_REVOKED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYEE,
		ShareToPayee:             true,
	}

	recurringPayment3 := &pb.RecurringPayment{
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(**********).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_DAILY,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_REVOKED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
		ShareToPayee:             false,
	}
	rpts, release := initialiseRpDaoTestSuite(t)
	defer release()
	_, _ = rpts.recurringPaymentDao.Create(context.Background(), recurringPayment1, commontypes.Ownership_EPIFI_TECH)
	_, _ = rpts.recurringPaymentDao.Create(context.Background(), recurringPayment2, commontypes.Ownership_EPIFI_TECH)
	_, _ = rpts.recurringPaymentDao.Create(context.Background(), recurringPayment3, commontypes.Ownership_EPIFI_TECH)

	tests := []struct {
		name    string
		actorId string
		want    map[string]int32
		wantErr bool
	}{
		{
			name:    "fetched successfully",
			actorId: "actor-1",
			want: map[string]int32{
				pb.RecurringPaymentState_ACTIVATED.String(): 1,
				pb.RecurringPaymentState_REVOKED.String():   2,
			},
			wantErr: false,
		},
		{
			name:    "empty actor id",
			actorId: "",
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := rpts.recurringPaymentDao.GetStateCountByActorId(context.Background(), tt.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetStateCountByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetStateCountByActorId() got = %v, want %v", got, tt.want)
			}
		})
	}
}
