//nolint:dupl
package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/golang/protobuf/ptypes"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	"github.com/epifi/gamma/recurringpayment/dao/model"
)

var standingInstructionRequestColumnNameMap = map[siPb.StandingInstructionRequestFieldMask]string{
	siPb.StandingInstructionRequestFieldMask_STATE: "state",
}

var standingInstructionRequestUpdateFieldFilter = func(field siPb.StandingInstructionRequestFieldMask) bool {
	return siPb.StandingInstructionRequestFieldMask_STANDING_INSTRUCTION_REQUEST_FIELD_MASK_UNSPECIFIED != field
}

type StandingInstructionRequestDaoCRDB struct {
	db *gorm.DB
}

func NewStandingInstructionRequestDao(db types.EpifiCRDB) *StandingInstructionRequestDaoCRDB {
	return &StandingInstructionRequestDaoCRDB{db: db}
}

// Ensure StandingInstructionRequestDaoCRDB implements StandingInstructionRequestDao at compile time
var _ StandingInstructionRequestDao = &StandingInstructionRequestDaoCRDB{}

func (s *StandingInstructionRequestDaoCRDB) Create(ctx context.Context, standingInstructionRequest *siPb.StandingInstructionRequest) (*siPb.StandingInstructionRequest, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "StandingInstructionRequestDaoCRDB", "Create", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, s.db)

	modelStandingInstructionRequest := convertToStandingInstructionRequestModel(standingInstructionRequest)

	if err := db.Create(modelStandingInstructionRequest).Error; err != nil {
		return nil, fmt.Errorf("unable create DB entry for standing instruction request: %w", err)
	}
	return convertToStandingInstructionRequestProto(modelStandingInstructionRequest)
}

func (s *StandingInstructionRequestDaoCRDB) GetByStandingInstructionIdAndRequestType(ctx context.Context, standingInstructionId string,
	requestType siPb.RequestType) (*siPb.StandingInstructionRequest, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "StandingInstructionRequestDaoCRDB", "GetByStandingInstructionIdAndRequestType", time.Now())
	if standingInstructionId == "" {
		return nil, fmt.Errorf("standing instruction id cannot be empty")
	}
	if requestType == siPb.RequestType_REQUEST_TYPE_UNSPECIFIED {
		return nil, fmt.Errorf("invalid request type")
	}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	modelStandingInstructionRequest := &model.StandingInstructionRequest{}
	if err := db.Where("standing_instruction_id = ? and request_type = ?", standingInstructionId, requestType).
		Order("created_at desc").First(modelStandingInstructionRequest).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch standing instruction request for standing instruction id %s, "+
			"requestType %s, err %w", standingInstructionId, requestType.String(), err)
	}
	return convertToStandingInstructionRequestProto(modelStandingInstructionRequest)
}

func (s *StandingInstructionRequestDaoCRDB) GetByRequestId(ctx context.Context, requestId string) (*siPb.StandingInstructionRequest, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "StandingInstructionRequestDaoCRDB", "GetByRequestId", time.Now())
	if requestId == "" {
		return nil, fmt.Errorf("request Id cannot be empty")
	}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	modelStandingInstructionRequest := &model.StandingInstructionRequest{}
	if err := db.Where("vendor_request_id = ?", requestId).Take(modelStandingInstructionRequest).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch standing instruction request for request id %s err %w", requestId, err)
	}
	return convertToStandingInstructionRequestProto(modelStandingInstructionRequest)
}

func (s *StandingInstructionRequestDaoCRDB) Update(ctx context.Context, standingInstructionRequest *siPb.StandingInstructionRequest,
	updateMask []siPb.StandingInstructionRequestFieldMask) error {
	defer metric_util.TrackDuration("recurringpayment/dao", "StandingInstructionRequestDaoCRDB", "Update", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, s.db)

	if standingInstructionRequest.Id == "" {
		return fmt.Errorf("primary identifier can't be empty for an update operation")
	}

	updateMask = filterStandingInstructionRequestFieldMaskSlice(updateMask, standingInstructionRequestUpdateFieldFilter)

	if len(updateMask) == 0 {
		return fmt.Errorf("update mask can't be empty")
	}

	modelStandingInstructionRequest := convertToStandingInstructionRequestModel(standingInstructionRequest)

	updateColumns := getSelectColumnsForStandingInstructionRequest(updateMask)

	if err := db.Model(modelStandingInstructionRequest).Select(updateColumns).Updates(modelStandingInstructionRequest).Error; err != nil {
		return fmt.Errorf("unable to update standing instruction request: %s : %w", standingInstructionRequest.Id, err)
	}
	return nil
}

// UpdateAndChangeStatus updates various recurring payment action fields along with standing instruction request state.
// But, instead of doing a blind update, a transition from the currentState to nextState is performed
// which ensures thread safety, stale read updates with out the need to acquire explicit locks
// nolint: dupl
func (s *StandingInstructionRequestDaoCRDB) UpdateAndChangeStatus(ctx context.Context, standingInstructionRequest *siPb.StandingInstructionRequest,
	updateMask []siPb.StandingInstructionRequestFieldMask, currentState, nextState siPb.State) error {
	defer metric_util.TrackDuration("recurringpayment/dao", "StandingInstructionRequestDaoCRDB", "UpdateAndChangeStatus", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, s.db)

	if standingInstructionRequest.Id == "" {
		return fmt.Errorf("primary identifier can't be empty for an update operation")
	}

	updateMask = filterStandingInstructionRequestFieldMaskSlice(updateMask, standingInstructionRequestUpdateFieldFilter)

	modelStandingInstructionRequest := convertToStandingInstructionRequestModel(standingInstructionRequest)

	if nextState != siPb.State_STATE_UNSPECIFIED {
		standingInstructionRequest.State = nextState
		modelStandingInstructionRequest.State = nextState
		updateMask = append(updateMask, siPb.StandingInstructionRequestFieldMask_STATE)
	}

	if len(updateMask) == 0 {
		return fmt.Errorf("update mask can't be empty")
	}

	updateColumns := getSelectColumnsForStandingInstructionRequest(updateMask)

	res := db.Model(modelStandingInstructionRequest).Where("state = ?", currentState).Select(updateColumns).Updates(modelStandingInstructionRequest)
	if res.Error != nil {
		return fmt.Errorf("unable to update standing instruction request: %s : %w", standingInstructionRequest.Id, res.Error)
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("unable to update standimng instruction request from: %s to: %s", currentState.String(), nextState.String())
	}
	return nil

}

// getSelectColumnsForStandingInstructionRequest converts update mask to string slice with column name corresponding to field name enums
func getSelectColumnsForStandingInstructionRequest(fieldMasks []siPb.StandingInstructionRequestFieldMask) []string {
	var selectColumns []string

	for _, field := range fieldMasks {
		selectColumns = append(selectColumns, standingInstructionRequestColumnNameMap[field])
	}
	return selectColumns
}

// filterStandingInstructionRequestFieldMaskSlice filters out elements from a slice based on the check function passed in arg
// elements are filtered out if they dont pass the check function
// NOTE- func returns a new copy of the slice. No changes are made to the existing slice
func filterStandingInstructionRequestFieldMaskSlice(fieldMasks []siPb.StandingInstructionRequestFieldMask,
	check func(field siPb.StandingInstructionRequestFieldMask) bool) []siPb.StandingInstructionRequestFieldMask {
	var ret []siPb.StandingInstructionRequestFieldMask
	for _, fieldMask := range fieldMasks {
		if check(fieldMask) {
			ret = append(ret, fieldMask)
		}
	}
	return ret
}

func convertToStandingInstructionRequestModel(standingInstructionRequest *siPb.StandingInstructionRequest) *model.StandingInstructionRequest {
	return &model.StandingInstructionRequest{
		Id:                    standingInstructionRequest.GetId(),
		StandingInstructionId: standingInstructionRequest.GetStandingInstructionId(),
		VendorRequestId:       standingInstructionRequest.GetVendorRequestId(),
		RequestType:           standingInstructionRequest.GetRequestType(),
		State:                 standingInstructionRequest.GetState(),
	}
}

// nolint: dupl
func convertToStandingInstructionRequestProto(model *model.StandingInstructionRequest) (*siPb.StandingInstructionRequest, error) {
	var err error

	proto := &siPb.StandingInstructionRequest{
		Id:                    model.Id,
		StandingInstructionId: model.StandingInstructionId,
		VendorRequestId:       model.VendorRequestId,
		RequestType:           model.RequestType,
		State:                 model.State,
	}
	proto.CreatedAt, err = ptypes.TimestampProto(model.CreatedAt)
	if err != nil {
		return nil, fmt.Errorf("unable to parse createdAt from model: %w", err)
	}

	proto.UpdatedAt, err = ptypes.TimestampProto(model.UpdatedAt)
	if err != nil {
		return nil, fmt.Errorf("unable to parse updatedAt from model: %w", err)
	}

	if model.DeletedAt.Valid {
		proto.DeletedAt = timestampPb.New(model.DeletedAt.Time)
	}

	return proto, nil
}
