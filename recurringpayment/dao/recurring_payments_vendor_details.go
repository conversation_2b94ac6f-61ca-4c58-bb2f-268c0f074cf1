// nolint:dupl
package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	pkgErrors "github.com/pkg/errors"
	"github.com/samber/lo"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	types "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/nulltypes"
	"github.com/epifi/be-common/pkg/pagination"
	_ "github.com/epifi/be-common/pkg/storage"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/pkg/storage/v2/usecase"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	recurringPaymentPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/recurringpayment/dao/model"
)

var recurringPaymentsVendorDetailsColumnNameMap = map[recurringPaymentPb.RecurringPaymentVendorDetailsFieldMask]string{
	recurringPaymentPb.RecurringPaymentVendorDetailsFieldMask_RECURRING_PAYMENT_VENDOR_DETAILS_FIELD_MASK_ID:                   "id",
	recurringPaymentPb.RecurringPaymentVendorDetailsFieldMask_RECURRING_PAYMENT_VENDOR_DETAILS_FIELD_MASK_RECURRING_PAYMENT_ID: "recurring_payment_id",
	recurringPaymentPb.RecurringPaymentVendorDetailsFieldMask_RECURRING_PAYMENT_VENDOR_DETAILS_FIELD_MASK_VENDOR_CUSTOMER_ID:   "vendor_customer_id",
	recurringPaymentPb.RecurringPaymentVendorDetailsFieldMask_RECURRING_PAYMENT_VENDOR_DETAILS_FIELD_MASK_VENDOR:               "vendor",
	recurringPaymentPb.RecurringPaymentVendorDetailsFieldMask_RECURRING_PAYMENT_VENDOR_DETAILS_FIELD_MASK_VENDOR_PAYMENT_ID:    "vendor_payment_id",
	recurringPaymentPb.RecurringPaymentVendorDetailsFieldMask_RECURRING_PAYMENT_VENDOR_DETAILS_FIELD_MASK_DELETED_AT:           "deleted_at",
	recurringPaymentPb.RecurringPaymentVendorDetailsFieldMask_RECURRING_PAYMENT_VENDOR_DETAILS_FIELD_MASK_UPDATED_AT:           "updated_at",
}

type RecurringPaymentsVendorDetailsDaoCRDB struct {
	idGen              idgen.IdGenerator
	dbResourceProvider *usecase.DBResourceProvider[*gormv2.DB]
}

func NewRecurringPaymentsVendorDetailsDao(idGen idgen.IdGenerator, dbResourceProvider *usecase.DBResourceProvider[*gormv2.DB]) *RecurringPaymentsVendorDetailsDaoCRDB {
	return &RecurringPaymentsVendorDetailsDaoCRDB{
		idGen:              idGen,
		dbResourceProvider: dbResourceProvider,
	}
}

var _ RecurringPaymentsVendorDetailsDao = &RecurringPaymentsVendorDetailsDaoCRDB{}

func (r *RecurringPaymentsVendorDetailsDaoCRDB) Create(ctx context.Context, vendorDetails *recurringPaymentPb.RecurringPaymentsVendorDetails) (*recurringPaymentPb.RecurringPaymentsVendorDetails, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentsVendorDetailsDaoCRDB", "Create", time.Now())
	db, err := getConnFromContextOrProvider(ctx, r.dbResourceProvider, types.UseCase_USE_CASE_RECURRING_PAYMENTS_VENDOR_DETAILS)
	if err != nil {
		return nil, fmt.Errorf("error in fetching db connection from ownership in context : %w", err)
	}
	id, err := r.idGen.Get(idgen.RecurringPaymentsVendorDetails)
	if err != nil {
		return nil, fmt.Errorf("recurring payment id generation failed: %w", err)
	}
	modelRecurringPaymentsVendorDetails, convertErr := convertToRecurringPaymentsVendorDetailsModel(vendorDetails)
	if convertErr != nil {
		return nil, fmt.Errorf("mandatory Field Missing: %w", convertErr)
	}
	modelRecurringPaymentsVendorDetails.ID = id

	if err = db.Create(modelRecurringPaymentsVendorDetails).Error; err != nil {
		if storageV2.IsDuplicateRowError(err) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		return nil, fmt.Errorf("unable create DB entry for recurring payments vendor_details : %w", err)
	}

	return modelRecurringPaymentsVendorDetails.GetProto(), nil
}

func (r *RecurringPaymentsVendorDetailsDaoCRDB) GetByRecurringPaymentId(ctx context.Context, recurringPaymentId string) (*recurringPaymentPb.RecurringPaymentsVendorDetails, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentsVendorDetailsDaoCRDB", "GetByRecurringPaymentId", time.Now())
	db, err := getConnFromContextOrProvider(ctx, r.dbResourceProvider, types.UseCase_USE_CASE_RECURRING_PAYMENTS_VENDOR_DETAILS)
	if err != nil {
		return nil, fmt.Errorf("error in fetching db connection from ownership in context : %w", err)
	}

	if recurringPaymentId == "" {
		return nil, fmt.Errorf("recurringPaymentId cannot be empty for GetByRecurringPaymentId()")
	}

	modelRecurringPaymentsVendorDetails := &model.RecurringPaymentsVendorDetails{}
	if err := db.Where("recurring_payment_id = ?", recurringPaymentId).First(modelRecurringPaymentsVendorDetails).Error; err != nil {
		if storageV2.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch record for recurring payment by recurringPaymentId: %v: %w", recurringPaymentId, err)
	}

	return modelRecurringPaymentsVendorDetails.GetProto(), nil

}

func (r *RecurringPaymentsVendorDetailsDaoCRDB) GetByActorId(ctx context.Context, actorId string) ([]*recurringPaymentPb.RecurringPaymentsVendorDetails, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentsVendorDetailsDaoCRDB", "GetByActorId", time.Now())
	db, err := getConnFromContextOrProvider(ctx, r.dbResourceProvider, types.UseCase_USE_CASE_RECURRING_PAYMENTS_VENDOR_DETAILS)
	if err != nil {
		return nil, fmt.Errorf("error in fetching db connection from ownership in context : %w", err)
	}

	if actorId == "" {
		return nil, fmt.Errorf("actorId cannot be empty for GetByActorId()")
	}
	var modelRecurringPaymentsVendorDetails []*model.RecurringPaymentsVendorDetails

	if err := db.Where("actor_id = ?", actorId).Find(&modelRecurringPaymentsVendorDetails).Error; err != nil {
		if storageV2.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch record for recurring payment by actorId: %v: %w", actorId, err)
	}
	if len(modelRecurringPaymentsVendorDetails) == 0 {
		return nil, fmt.Errorf("failed to fetch record for recurring payment by actorId: %v: %w", actorId, epifierrors.ErrRecordNotFound)
	}
	var protoRecurringPaymentsVendorDetails []*recurringPaymentPb.RecurringPaymentsVendorDetails
	for _, modelVendorDetails := range modelRecurringPaymentsVendorDetails {
		protoRecurringPaymentsVendorDetails = append(protoRecurringPaymentsVendorDetails, modelVendorDetails.GetProto())
	}

	return protoRecurringPaymentsVendorDetails, nil
}

func (r *RecurringPaymentsVendorDetailsDaoCRDB) Update(ctx context.Context, vendorDetails *recurringPaymentPb.RecurringPaymentsVendorDetails, updateMask []recurringPaymentPb.RecurringPaymentVendorDetailsFieldMask) error {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentsVendorDetailsDaoCRDB", "Update", time.Now())
	db, err := getConnFromContextOrProvider(ctx, r.dbResourceProvider, types.UseCase_USE_CASE_RECURRING_PAYMENTS_VENDOR_DETAILS)
	if err != nil {
		return fmt.Errorf("error in fetching db connection from ownership in context : %w", err)
	}

	if vendorDetails.GetId() == "" {
		return fmt.Errorf("primary identifier can't be empty for an update operation")
	}

	// Will filter out the fields for which are passed in this
	updateMask = lo.Without(updateMask, recurringPaymentPb.RecurringPaymentVendorDetailsFieldMask_RECURRING_PAYMENT_VENDOR_DETAILS_FIELD_MASK_UNSPECIFIED,
		recurringPaymentPb.RecurringPaymentVendorDetailsFieldMask_RECURRING_PAYMENT_VENDOR_DETAILS_FIELD_MASK_ID)

	if len(updateMask) == 0 {
		return fmt.Errorf("update mask can't be empty")
	}

	modelRecurringPaymentsVendorDetails, convertErr := convertToRecurringPaymentsVendorDetailsModel(vendorDetails)
	if convertErr != nil {
		return fmt.Errorf("mandatory Field Missing: %w", convertErr)
	}
	updateColumns := getSelectColumnsForRecurringPaymentsVendorDetails(updateMask)

	if err := db.Model(modelRecurringPaymentsVendorDetails).Select(updateColumns).Updates(modelRecurringPaymentsVendorDetails).Error; err != nil {
		return fmt.Errorf("unable to update recurring payment vendor details : %s : %w", vendorDetails.GetId(), err)
	}

	return nil
}
func (r *RecurringPaymentsVendorDetailsDaoCRDB) GetById(ctx context.Context, id string) (*recurringPaymentPb.RecurringPaymentsVendorDetails, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentsVendorDetailsDaoCRDB", "GetById", time.Now())
	if id == "" {
		return nil, fmt.Errorf("id cannot be empty for GetById()")
	}
	db, err := getConnFromContextOrProvider(ctx, r.dbResourceProvider, types.UseCase_USE_CASE_RECURRING_PAYMENTS_VENDOR_DETAILS)
	if err != nil {
		return nil, fmt.Errorf("error in fetching db connection from ownership in context : %w", err)
	}
	modelRecurringPaymentsVendorDetails := &model.RecurringPaymentsVendorDetails{}
	if err := db.Where("id = ?", id).First(modelRecurringPaymentsVendorDetails).Error; err != nil {
		if storageV2.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch record for recurring payment by id: %v: %w", id, err)
	}

	return modelRecurringPaymentsVendorDetails.GetProto(), nil
}

func (r *RecurringPaymentsVendorDetailsDaoCRDB) GetByVendorCustomerId(ctx context.Context, vendorCustomerId string) (*recurringPaymentPb.RecurringPaymentsVendorDetails, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentsVendorDetailsDaoCRDB", "GetByVendorCustomerId", time.Now())
	if vendorCustomerId == "" {
		return nil, fmt.Errorf("vendor customer id cannot be empty for GetByVendorCustomerId()")
	}
	db, err := getConnFromContextOrProvider(ctx, r.dbResourceProvider, types.UseCase_USE_CASE_RECURRING_PAYMENTS_VENDOR_DETAILS)
	if err != nil {
		return nil, fmt.Errorf("error in fetching db connection from ownership in context : %w", err)
	}
	modelRecurringPaymentsVendorDetails := &model.RecurringPaymentsVendorDetails{}
	if err := db.Where("vendor_customer_id = ?", vendorCustomerId).First(modelRecurringPaymentsVendorDetails).Error; err != nil {
		if storageV2.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch record for vendor customer id: %v: %w", vendorCustomerId, err)
	}

	return modelRecurringPaymentsVendorDetails.GetProto(), nil
}

func (r *RecurringPaymentsVendorDetailsDaoCRDB) GetByVendorPaymentId(ctx context.Context, vendorPaymentId string) (*recurringPaymentPb.RecurringPaymentsVendorDetails, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentsVendorDetailsDaoCRDB", "GetByVendorPaymentId", time.Now())
	if vendorPaymentId == "" {
		return nil, fmt.Errorf("vendor payment id cannot be empty for GetByVendorPaymentId()")
	}
	db, err := getConnFromContextOrProvider(ctx, r.dbResourceProvider, types.UseCase_USE_CASE_RECURRING_PAYMENTS_VENDOR_DETAILS)
	if err != nil {
		return nil, fmt.Errorf("error in fetching db connection from ownership in context : %w", err)
	}
	modelRecurringPaymentsVendorDetails := &model.RecurringPaymentsVendorDetails{}
	if err := db.Where("vendor_payment_id = ?", vendorPaymentId).First(modelRecurringPaymentsVendorDetails).Error; err != nil {
		if storageV2.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch record for vendor payment id: %v: %w", vendorPaymentId, err)
	}

	return modelRecurringPaymentsVendorDetails.GetProto(), nil
}

func (r *RecurringPaymentsVendorDetailsDaoCRDB) Get(ctx context.Context, pageToken *pagination.PageToken, pageSize uint32, filters ...FilterOption) ([]*recurringPaymentPb.RecurringPaymentsVendorDetails, *rpc.PageContextResponse, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentsVendorDetailsDaoCRDB", "Get", time.Now())

	db, err := getConnFromContextOrProvider(ctx, r.dbResourceProvider, types.UseCase_USE_CASE_RECURRING_PAYMENTS_VENDOR_DETAILS)
	if err != nil {
		return nil, nil, fmt.Errorf("error in fetching db connection from ownership in context : %w", err)
	}

	modelRecurringPaymentsVendorDetails := make([]*model.RecurringPaymentsVendorDetails, 0)

	db = pagination.AddPaginationOnGivenColumn(db, pageToken, pageSize, "", "created_at")

	for _, opt := range filters {
		opt.applyInGorm(db)
	}

	res := db.Find(&modelRecurringPaymentsVendorDetails)

	if res.Error != nil {
		return nil, nil, fmt.Errorf("error in querying for recurring payment vendor details: %w", res.Error)
	}

	if len(modelRecurringPaymentsVendorDetails) == 0 {
		return nil, nil, epifierrors.ErrRecordNotFound
	}

	var pageCtxResp *rpc.PageContextResponse
	rows, pageCtxResp, err := pagination.NewPageCtxResp(pageToken, int(pageSize), model.RecurringPaymentsVendorDetailsRows(modelRecurringPaymentsVendorDetails))

	if err != nil {
		return nil, nil, fmt.Errorf("error in generating recurring payment vendor details page ctx response: %w", err)
	}

	modelRecurringPaymentsVendorDetails = rows.(model.RecurringPaymentsVendorDetailsRows)

	var protoList []*recurringPaymentPb.RecurringPaymentsVendorDetails
	for _, rpVdModel := range modelRecurringPaymentsVendorDetails {
		protoList = append(protoList, rpVdModel.GetProto())
	}

	return protoList, pageCtxResp, nil
}

// getSelectColumnsForRecurringPaymentsVendorDetails converts update mask to string slice with column name corresponding to field name enums
func getSelectColumnsForRecurringPaymentsVendorDetails(fieldMasks []recurringPaymentPb.RecurringPaymentVendorDetailsFieldMask) []string {
	var selectColumns []string

	for _, field := range fieldMasks {
		selectColumns = append(selectColumns, recurringPaymentsVendorDetailsColumnNameMap[field])
	}
	return selectColumns
}

// convertToRecurringPaymentsVendorDetailsModel converts domain proto struct to model struct in recurring payment vendor details to perform gorm queries on them.
func convertToRecurringPaymentsVendorDetailsModel(recurringPaymentsVendorDetails *recurringPaymentPb.RecurringPaymentsVendorDetails) (*model.RecurringPaymentsVendorDetails, error) {
	if recurringPaymentsVendorDetails.GetActorId() == "" {
		return nil, errors.New("actor_id is required and cannot be empty")
	}
	if recurringPaymentsVendorDetails.GetRecurringPaymentId() == "" {
		return nil, errors.New("recurring_payment_id is required and cannot be empty")
	}
	modelRecurringPaymentsVendorDetails := &model.RecurringPaymentsVendorDetails{
		ID:                 recurringPaymentsVendorDetails.GetId(),
		ActorId:            recurringPaymentsVendorDetails.GetActorId(),
		RecurringPaymentId: recurringPaymentsVendorDetails.GetRecurringPaymentId(),
		VendorCustomerId:   nulltypes.NewNullString(recurringPaymentsVendorDetails.GetVendorCustomerId()),
		Vendor:             recurringPaymentsVendorDetails.GetVendor(),
		VendorPaymentId:    nulltypes.NewNullString(recurringPaymentsVendorDetails.GetVendorPaymentId()),
	}
	return modelRecurringPaymentsVendorDetails, nil
}

// TODO: Remove nolint once getConnFromContextOrProvider starts receiving other usecase types
// nolint:unparam
func getConnFromContextOrProvider(ctx context.Context, dbResourceProvider *usecase.DBResourceProvider[*gormv2.DB], useCase types.UseCase) (*gormv2.DB, error) {
	// if we are not able to fetch ownership from context, the ownership will default to EPIFI_TECH
	ow := epificontext.OwnershipFromContext[context.Context](ctx)
	dbConn, err := dbResourceProvider.GetResource(ow, useCase)
	if err != nil {
		return nil, pkgErrors.Wrap(err, "err in GetResourceForOwnership")
	}
	return gormctxv2.FromContextOrDefault(ctx, dbConn), nil
}
