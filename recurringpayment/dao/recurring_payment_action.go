// nolint:dupl,goimports
package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
	gormV2 "gorm.io/gorm"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/idgen"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/nulltypes"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	pb "github.com/epifi/gamma/api/recurringpayment"
	types2 "github.com/epifi/gamma/order/wire/types"
	payDao "github.com/epifi/gamma/pay/dao"
	model2 "github.com/epifi/gamma/recurringpayment/dao/model"
)

const (
	ASC  = "ASC"
	DESC = "DESC"
)

var recurringPaymentActionColumnNameMap = map[pb.RecurringPaymentActionFieldMask]string{
	pb.RecurringPaymentActionFieldMask_ACTION_STATE:                "state",
	pb.RecurringPaymentActionFieldMask_CREATED_AT:                  "created_at",
	pb.RecurringPaymentActionFieldMask_UPDATED_AT:                  "updated_at",
	pb.RecurringPaymentActionFieldMask_ACTION_DETAILED_STATUS:      "action_detailed_status",
	pb.RecurringPaymentActionFieldMask_ACTION_DETAILED_STATUS_INFO: "action_detailed_status_info",
}

var recurringPaymentActionUpdateFieldFilter = func(field pb.RecurringPaymentActionFieldMask) bool {
	return pb.RecurringPaymentActionFieldMask_RECURRING_PAYMENT_ACTION_FIELD_MASK_UNSPECIFIED != field
}

type RecurringPaymentsActionDaoCRDB struct {
	db                 *gorm.DB
	attrIdGen          idgen.AttributedIdGen[*RecurringPaymentAttributes]
	dbResourceProvider *storagev2.DBResourceProvider[*gormV2.DB]
	// true: db instance is going to be fetched based on ownership which will be fetched from context
	// false: db instance will be the default epifi CRDB
	useDbResourceProvider types2.EnableResourceProvider
	ovomDao               payDao.OrderVendorOrderMapDao
}

// DistinctDbKeys is the mapping of distinct ownerships that are present for entity segregation within pay.
// Note : This is not a list of distinct ownerships but the list of different DBs that are present for the ownerships.
// So if there are multiple ownerships that are using the same DB, then they will have the same key in this list.
// Keeping it as a list of string so that we can fit the use case construct in the future.
// This is a TEMPORARY solution to handle the ownerships that are using the same DB till we figure out how to dedupe
// DBs itself.
// Permanent solution ticket : https://monorail.pointz.in/p/fi-app/issues/detail?id=88210
var DistinctDbKeys = []string{
	"LIQUILOANS_PL",
	"STOCK_GUARDIAN_TSP",
	"EPIFI_TECH",
}

// Ensure RecurringPaymentsActionDaoCRDB implements RecurringPaymentsActionDao at compile time
var _ RecurringPaymentsActionDao = &RecurringPaymentsActionDaoCRDB{}

// Factory method for creating an instance of recurring payment dao. This method will be used by the injector
// when providing the dependencies at initialization time.
func NewRecurringPaymentsActionDao(db types.EpifiCRDB, attrIdGen idgen.AttributedIdGen[*RecurringPaymentAttributes], dbResourceProvider *storagev2.DBResourceProvider[*gormV2.DB], useDbResourceProvider types2.EnableResourceProvider, ovomDao payDao.OrderVendorOrderMapDao) *RecurringPaymentsActionDaoCRDB {
	return &RecurringPaymentsActionDaoCRDB{
		db:                    db,
		attrIdGen:             attrIdGen,
		dbResourceProvider:    dbResourceProvider,
		useDbResourceProvider: useDbResourceProvider,
		ovomDao:               ovomDao,
	}
}

func (r *RecurringPaymentsActionDaoCRDB) Create(ctx context.Context, recurringPaymentAction *pb.RecurringPaymentsAction) (*pb.RecurringPaymentsAction, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentsActionDaoCRDB", "Create", time.Now())

	ctx = getCtxWithOwnershipFromAttribute(ctx, idgen.RecurringPayment, r.attrIdGen, recurringPaymentAction.GetRecurringPaymentId())

	db, connErr := getConnFromContextOrProviderWithDefaultHandling(ctx, r.dbResourceProvider, r.db, r.useDbResourceProvider)
	if connErr != nil {
		return nil, fmt.Errorf("failed to get db connection: %w", connErr)
	}

	modelRecurringPaymentsAction := model2.NewRecurringPaymentsAction(recurringPaymentAction)

	if err := db.Create(modelRecurringPaymentsAction).Error; err != nil {
		return nil, fmt.Errorf("unable create DB entry for recurring payment action: %w", err)
	}

	return modelRecurringPaymentsAction.GetProto(), nil
}

// GetById fetches DB record belonging to given recurring payment id. Returns error if record not found.
// nolint dupl
func (r *RecurringPaymentsActionDaoCRDB) GetById(ctx context.Context, recurringPaymentActionId string) (*pb.RecurringPaymentsAction, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentsActionDaoCRDB", "GetById", time.Now())

	db, connErr := getConnFromContextOrProviderWithDefaultHandling(ctx, r.dbResourceProvider, r.db, r.useDbResourceProvider)
	if connErr != nil {
		return nil, fmt.Errorf("failed to get db connection: %w", connErr)
	}

	if recurringPaymentActionId == "" {
		return nil, fmt.Errorf("id cannot be empty for GetById() %w", epifierrors.ErrInvalidArgument)
	}

	modelRecurringPaymentAction := &model2.RecurringPaymentsAction{}
	if err := db.Where("id = ?", recurringPaymentActionId).First(modelRecurringPaymentAction).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch record for recurring payment action by recurringPaymentActionId: %v: %w", recurringPaymentActionId, err)
	}

	return modelRecurringPaymentAction.GetProto(), nil
}

// GetByRecurringPaymentId returns actions for a given recurring payment. It also supports filtering based on actions.
// nolint: dupl
func (r *RecurringPaymentsActionDaoCRDB) GetByRecurringPaymentId(ctx context.Context, recurringPaymentId string,
	options ...FilterOption) ([]*pb.RecurringPaymentsAction, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentsActionDaoCRDB", "GetByRecurringPaymentId", time.Now())
	if recurringPaymentId == "" {
		return nil, fmt.Errorf("recurring payment id cannot be empty")
	}

	ctx = getCtxWithOwnershipFromAttribute(ctx, idgen.RecurringPayment, r.attrIdGen, recurringPaymentId)

	db, connErr := getConnFromContextOrProviderWithDefaultHandling(ctx, r.dbResourceProvider, r.db, r.useDbResourceProvider)
	if connErr != nil {
		return nil, fmt.Errorf("failed to get db connection: %w", connErr)
	}

	for _, opt := range options {
		db = opt.applyInGorm(db)
	}
	var models []*model2.RecurringPaymentsAction
	if err := db.Where("recurring_payment_id = ?", recurringPaymentId).Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch record for recurring payment id: %s: %w", recurringPaymentId, err)
	}
	if len(models) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	var result []*pb.RecurringPaymentsAction
	for i := range models {
		proto := models[i].GetProto()
		result = append(result, proto)
	}
	return result, nil
}

// GetSuccessfulActionsByRecurringPaymentId fetches successful actions for a given actionType and recurring payment id
// In case the descending flag is false -
// the query returns orders, actions that happened after the given time
// In case the descending flag is true -
// the queue returns orders, actions that happened before the given time
// limits and offset bounds the results and provides a functionality of pagination.
func (r *RecurringPaymentsActionDaoCRDB) GetSuccessfulActionsByRecurringPaymentId(ctx context.Context,
	recurringPaymentId string, startTime time.Time, limit, offset int32,
	descending bool, sortBy pb.RecurringPaymentActionFieldMask, options ...FilterOption) ([]*pb.RecurringPaymentsAction, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentsActionDaoCRDB", "GetSuccessfulActionsByRecurringPaymentId", time.Now())
	var (
		fetchedModelActions []*model2.RecurringPaymentsAction
		comparisonOperator  string
		sortOrder           string
		sortColumn          string
	)
	ctx = getCtxWithOwnershipFromAttribute(ctx, idgen.RecurringPayment, r.attrIdGen, recurringPaymentId)

	db, connErr := getConnFromContextOrProviderWithDefaultHandling(ctx, r.dbResourceProvider, r.db, r.useDbResourceProvider)
	if connErr != nil {
		return nil, fmt.Errorf("failed to get db connection: %w", connErr)
	}

	successStates := []pb.ActionState{pb.ActionState_ACTION_SUCCESS}

	if recurringPaymentId == "" {
		return nil, fmt.Errorf("recurring payment id cannot be empty %w", epifierrors.ErrInvalidArgument)
	}

	if sortBy == pb.RecurringPaymentActionFieldMask_RECURRING_PAYMENT_ACTION_FIELD_MASK_UNSPECIFIED {
		return nil, fmt.Errorf("sortBy column can't be empty: %w", epifierrors.ErrInvalidArgument)
	}

	if sortColumn = recurringPaymentActionColumnNameMap[sortBy]; sortColumn == "" {
		return nil, fmt.Errorf("sortBy column mapping not found: %w", epifierrors.ErrInvalidArgument)
	}

	// set the table/model on which query needs to be performed
	dbWithQuery := db.Model(&model2.RecurringPaymentsAction{})

	dbWithQuery = dbWithQuery.Where("recurring_payment_id = ? AND state in (?) ",
		recurringPaymentId, successStates)

	for _, opt := range options {
		dbWithQuery = opt.applyInGorm(dbWithQuery)
	}

	if descending {
		comparisonOperator = " <= ? "
		sortOrder = DESC
	} else {
		comparisonOperator = " >= ? "
		sortOrder = ASC
	}

	dbWithQuery = dbWithQuery.Where(sortColumn+comparisonOperator, startTime)

	dbWithQuery = dbWithQuery.Order(sortColumn + " " + sortOrder)

	dbWithQuery = dbWithQuery.Offset(int(offset)).Limit(int(limit))

	if err := dbWithQuery.Find(&fetchedModelActions).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch actions: %w", err)
	}

	var result []*pb.RecurringPaymentsAction
	for i := range fetchedModelActions {
		proto := fetchedModelActions[i].GetProto()
		result = append(result, proto)
	}
	return result, nil
}

func (r *RecurringPaymentsActionDaoCRDB) Update(ctx context.Context, recurringPaymentAction *pb.RecurringPaymentsAction,
	updateMask []pb.RecurringPaymentActionFieldMask) error {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentsActionDaoCRDB", "Update", time.Now())

	if recurringPaymentAction.GetId() == "" {
		return fmt.Errorf("primary identifier can't be empty for an update operation %w", epifierrors.ErrInvalidArgument)
	}

	ctx = getCtxWithOwnershipFromAttribute(ctx, idgen.RecurringPayment, r.attrIdGen, recurringPaymentAction.GetRecurringPaymentId())

	db, connErr := getConnFromContextOrProviderWithDefaultHandling(ctx, r.dbResourceProvider, r.db, r.useDbResourceProvider)
	if connErr != nil {
		return fmt.Errorf("failed to get db connection: %w", connErr)
	}

	updateMask = filterRecurringPaymentsActionFieldMaskSlice(updateMask, recurringPaymentActionUpdateFieldFilter)
	if len(updateMask) == 0 {
		return fmt.Errorf("update mask can't be empty %w", epifierrors.ErrInvalidArgument)
	}

	modelRecurringPaymentAction := model2.NewRecurringPaymentsAction(recurringPaymentAction)

	updateColumns := getSelectColumnsForRecurringPaymentsAction(updateMask)

	if err := db.Model(modelRecurringPaymentAction).Select(updateColumns).Updates(modelRecurringPaymentAction).Error; err != nil {
		return fmt.Errorf("unable to update recurring payment action for rp id: %s : %w", recurringPaymentAction.RecurringPaymentId, err)
	}

	return nil
}

func (r *RecurringPaymentsActionDaoCRDB) GetByClientRequestId(ctx context.Context, clientRequestId string, multiDbQuery bool) (*pb.RecurringPaymentsAction, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentsActionDaoCRDB", "GetByClientRequestId", time.Now())
	if clientRequestId == "" {
		return nil, fmt.Errorf("client request id cannot be empty %w", epifierrors.ErrInvalidArgument)
	}
	queryFunc := func(db *gorm.DB) ([]*model2.RecurringPaymentsAction, error) {
		var mdl model2.RecurringPaymentsAction
		res := db.Where(&model2.RecurringPaymentsAction{ClientRequestId: clientRequestId}).Order("created_at desc").Limit(1).Take(&mdl)
		if res.Error != nil {
			if errors.Is(res.Error, gorm.ErrRecordNotFound) {
				return nil, epifierrors.ErrRecordNotFound
			}
			return nil, res.Error
		}
		return []*model2.RecurringPaymentsAction{&mdl}, nil
	}
	if multiDbQuery {
		multiDbRes, err := getMultiDbResponse[*model2.RecurringPaymentsAction](ctx, r.dbResourceProvider, queryFunc)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch record from all databases: %w", err)
		}
		if len(multiDbRes) == 0 {
			return nil, epifierrors.ErrRecordNotFound
		}
		return multiDbRes[0].GetProto(), nil
	}
	var err error
	ctx, err = getOwnershipFromClientRequestId(ctx, r.ovomDao, clientRequestId)
	if err != nil {
		return nil, fmt.Errorf("failed to get ownership from client request id: %w", err)
	}

	db, connErr := getConnFromContextOrProviderWithDefaultHandling(ctx, r.dbResourceProvider, r.db, r.useDbResourceProvider)
	if connErr != nil {
		return nil, fmt.Errorf("failed to get db connection: %w", connErr)
	}
	res, err := queryFunc(db)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch record for client request id: %s, error: %w", clientRequestId, err)
	}
	if len(res) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return res[0].GetProto(), nil
}

// getMultiDbResponse aggregates the response for a particular query function from multiple databases. This is generic in nature
// and can be used to aggregate any type of model data
func getMultiDbResponse[T any](ctx context.Context, dbResProvider *storagev2.DBResourceProvider[*gorm.DB], queryFunc func(db *gorm.DB) ([]T, error)) ([]T, error) {
	collectedData := make([]T, 0)
	collectedDataChan := make(chan []T, len(dbResProvider.DBOwnershipMap))
	grp, _ := errgroup.WithContext(ctx)
	// This is a TEMPORARY solution to handle the ownerships that are using the same DB till we figure out how to dedupe
	// DBs itself.
	// Permanent solution ticket : https://monorail.pointz.in/p/fi-app/issues/detail?id=88210
	for _, ownerString := range DistinctDbKeys {
		ownership, ok := commontypes.Ownership_value[ownerString]
		if !ok {
			continue
		}
		db, dbErr := dbResProvider.GetResourceForOwnership(commontypes.Ownership(ownership))
		if dbErr != nil {
			return nil, fmt.Errorf("failed to fetch db for ownership: %v, error: %w", ownership, dbErr)
		}
		grp.Go(func() error {
			res, err := queryFunc(db)
			switch {
			case errors.Is(err, epifierrors.ErrRecordNotFound):
				return nil
			case err != nil:
				return fmt.Errorf("failed to fetch record for ownership: %v, error: %w", ownership, err)
			default:
				collectedDataChan <- res
				return nil
			}
		})
	}
	if err := grp.Wait(); err != nil {
		return nil, fmt.Errorf("failed to fetch record from all databases: %w", err)
	}

	close(collectedDataChan)

	for data := range collectedDataChan {
		collectedData = append(collectedData, data...)
	}
	return collectedData, nil
}

// UpdateAndChangeStatus updates various recurring payment action fields along with recurring payment action state.
// But, instead of doing a blind update, a transition from the currentState to nextState is performed
// which ensures thread safety, stale read updates with out the need to acquire explicit locks
// nolint: dupl
func (r *RecurringPaymentsActionDaoCRDB) UpdateAndChangeStatus(ctx context.Context, recurringPaymentAction *pb.RecurringPaymentsAction, updateMask []pb.RecurringPaymentActionFieldMask,
	currentState, nextState pb.ActionState) error {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentsActionDaoCRDB", "UpdateAndChangeStatus", time.Now())

	if recurringPaymentAction.GetId() == "" {
		return fmt.Errorf("primary identifier can't be empty for an update operation %w", epifierrors.ErrInvalidArgument)
	}

	ctx = getCtxWithOwnershipFromAttribute(ctx, idgen.RecurringPayment, r.attrIdGen, recurringPaymentAction.GetRecurringPaymentId())

	db, connErr := getConnFromContextOrProviderWithDefaultHandling(ctx, r.dbResourceProvider, r.db, r.useDbResourceProvider)
	if connErr != nil {
		return fmt.Errorf("failed to get db connection: %w", connErr)
	}

	updateMask = filterRecurringPaymentsActionFieldMaskSlice(updateMask, recurringPaymentActionUpdateFieldFilter)

	modelRecurringPaymentAction := model2.NewRecurringPaymentsAction(recurringPaymentAction)

	if nextState != pb.ActionState_ACTION_STATE_UNSPECIFIED {
		recurringPaymentAction.State = nextState
		modelRecurringPaymentAction.State = nextState
		updateMask = append(updateMask, pb.RecurringPaymentActionFieldMask_ACTION_STATE)
	}

	if len(updateMask) == 0 {
		return fmt.Errorf("update mask can't be empty %w", epifierrors.ErrInvalidArgument)
	}

	updateColumns := getSelectColumnsForRecurringPaymentsAction(updateMask)

	res := db.Model(modelRecurringPaymentAction).Where("state = ?", currentState).Select(updateColumns).Updates(modelRecurringPaymentAction)
	if res.Error != nil {
		return fmt.Errorf("unable to update recurring payment action for rp id: %s : %w", recurringPaymentAction.RecurringPaymentId, res.Error)
	}

	if res.RowsAffected == 0 {
		return fmt.Errorf("unable to update recurring payment action from: %s to: %s, %w", currentState.String(), nextState.String(), epifierrors.ErrNoRowsAffected)
	}

	return nil
}

// getSelectColumnsForRecurringPaymentsAction converts update mask to string slice with column name corresponding to field name enums
func getSelectColumnsForRecurringPaymentsAction(fieldMasks []pb.RecurringPaymentActionFieldMask) []string {
	var selectColumns []string

	for _, field := range fieldMasks {
		selectColumns = append(selectColumns, recurringPaymentActionColumnNameMap[field])
	}
	return selectColumns
}

// filterRecurringPaymentsActionFieldMaskSlice filters out elements from a slice based on the check function passed in arg
// elements are filtered out if they dont pass the check function
// NOTE- func returns a new copy of the slice. No changes are made to the existing slice
func filterRecurringPaymentsActionFieldMaskSlice(fieldMasks []pb.RecurringPaymentActionFieldMask,
	check func(field pb.RecurringPaymentActionFieldMask) bool) []pb.RecurringPaymentActionFieldMask {
	var ret []pb.RecurringPaymentActionFieldMask
	for _, fieldMask := range fieldMasks {
		if check(fieldMask) {
			ret = append(ret, fieldMask)
		}
	}
	return ret
}

// TODO(abhishekprakash) Add a fan-out pattern to fetch recurring payments in parallel from all the registered databases because ownership might not be clear in this
func (r *RecurringPaymentsActionDaoCRDB) GetByVendorRequestId(ctx context.Context, vendorRequestId string) (*pb.RecurringPaymentsAction, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentsActionDaoCRDB", "GetByVendorRequestId", time.Now())
	if vendorRequestId == "" {
		return nil, fmt.Errorf("vendor request id cannot be empty %w", epifierrors.ErrInvalidArgument)
	}
	db, connErr := getConnFromContextOrProviderWithDefaultHandling(ctx, r.dbResourceProvider, r.db, r.useDbResourceProvider)
	if connErr != nil {
		return nil, fmt.Errorf("failed to get db connection: %w", connErr)
	}
	var mdl model2.RecurringPaymentsAction
	res := db.Where(&model2.RecurringPaymentsAction{VendorRequestId: nulltypes.NewNullString(vendorRequestId)}).Take(&mdl)
	if res.Error != nil {
		if errors.Is(res.Error, gorm.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("error fetching recurring payment action for reqid :%s %w", vendorRequestId, res.Error)
	}
	return mdl.GetProto(), nil
}

// GetActionsByRecurringPaymentId fetches actions for a given actionType and recurring payment id
// In case the descending flag is false -
// the query returns orders, actions that happened after the given time
// In case the descending flag is true -
// the queue returns orders, actions that happened before the given time
// limits and offset bounds the results and provides a functionality of pagination.
func (r *RecurringPaymentsActionDaoCRDB) GetActionsByRecurringPaymentId(ctx context.Context,
	recurringPaymentId string, tokenTime time.Time, limit, offset int32,
	descending bool, options ...FilterOption) ([]*pb.RecurringPaymentsAction, error) {
	defer metric_util.TrackDuration("recurringpayment/dao", "RecurringPaymentsActionDaoCRDB", "GetActionsByRecurringPaymentId", time.Now())
	var (
		fetchedModelActions []*model2.RecurringPaymentsAction
		comparisonOperator  string
		sortOrder           string
		sortColumn          = "created_at"
	)

	if recurringPaymentId == "" {
		return nil, fmt.Errorf("recurring payment id cannot be empty %w", epifierrors.ErrInvalidArgument)
	}

	ctx = getCtxWithOwnershipFromAttribute(ctx, idgen.RecurringPayment, r.attrIdGen, recurringPaymentId)

	db, connErr := getConnFromContextOrProviderWithDefaultHandling(ctx, r.dbResourceProvider, r.db, r.useDbResourceProvider)
	if connErr != nil {
		return nil, fmt.Errorf("failed to get db connection: %w", connErr)
	}
	// set the table/model on which query needs to be performed
	dbWithQuery := db.Model(&model2.RecurringPaymentsAction{})

	dbWithQuery = dbWithQuery.Where("recurring_payment_id = ? ", recurringPaymentId)

	for _, opt := range options {
		dbWithQuery = opt.applyInGorm(dbWithQuery)
	}

	if descending {
		comparisonOperator = " <= ? "
		sortOrder = DESC
	} else {
		comparisonOperator = " >= ? "
		sortOrder = ASC
	}

	dbWithQuery = dbWithQuery.Where(sortColumn+comparisonOperator, tokenTime)

	dbWithQuery = dbWithQuery.Order(sortColumn + " " + sortOrder)

	dbWithQuery = dbWithQuery.Offset(int(offset)).Limit(int(limit))

	if err := dbWithQuery.Find(&fetchedModelActions).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch actions: %w", err)
	}

	if len(fetchedModelActions) == 0 {
		return nil, fmt.Errorf("no actions found :%w", epifierrors.ErrRecordNotFound)
	}

	var result []*pb.RecurringPaymentsAction
	for i := range fetchedModelActions {
		proto := fetchedModelActions[i].GetProto()
		result = append(result, proto)
	}
	return result, nil
}
