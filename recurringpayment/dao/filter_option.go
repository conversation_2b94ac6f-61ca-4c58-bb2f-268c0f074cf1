package dao

// File to define various dao filter options

import (
	"time"

	"gorm.io/gorm"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	pb "github.com/epifi/gamma/api/recurringpayment"
)

// FilterOption interface to define generic dao filter options
type FilterOption interface {
	// applyInG<PERSON> applied the filter to gorm.DB
	applyInGorm(db *gorm.DB) *gorm.DB
}

// funcFilterOption wraps a function that modifies *gorm.DB into an
// implementation of the FilterOption interface.
type funcFilterOption struct {
	fn func(db *gorm.DB) *gorm.DB
}

func (fdo *funcFilterOption) applyInGorm(db *gorm.DB) *gorm.DB {
	return fdo.fn(db)
}

func newFuncFilterOption(fn func(db *gorm.DB) *gorm.DB) *funcFilterOption {
	return &funcFilterOption{fn: fn}
}

// WithFromPIsFilter returns FilterOption that sets pi_from filter in the DB query as WHERE clause.
func WithFromPIsFilter(piIDs []string) FilterOption {
	return newFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("pi_from IN (?)", piIDs)
	})
}

// WithToPIsFilter returns FilterOption that sets pi_to filter in the DB query as WHERE clause.
func WithToPIsFilter(piIDs []string) FilterOption {
	return newFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("pi_to IN (?)", piIDs)
	})
}

func WithActionsFilter(actions []pb.Action) FilterOption {
	return newFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("action IN (?)", actions)
	})
}

func WithOrderByCreatedAt(descending bool) FilterOption {
	if descending {
		return newFuncFilterOption(func(db *gorm.DB) *gorm.DB {
			return db.Order("created_at DESC")
		})
	}
	return newFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Order("created_at ASC")
	})
}

func WithActionStateFilter(states []pb.ActionState) FilterOption {
	return newFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("state IN (?)", states)
	})
}

func WithNotInActionStatesFilter(states []pb.ActionState) FilterOption {
	return newFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("state NOT IN (?)", states)
	})
}

func WithToActorFilter(toActorId string) FilterOption {
	return newFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("to_actor_id = ?", toActorId)
	})
}

func WithFromCreationTimeFilter(fromTime time.Time) FilterOption {
	return newFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("created_at >= ?", fromTime)
	})
}

func WithVendorFilter(vendor commonvgpb.Vendor) FilterOption {
	return newFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("vendor = ?", vendor.String())
	})
}
