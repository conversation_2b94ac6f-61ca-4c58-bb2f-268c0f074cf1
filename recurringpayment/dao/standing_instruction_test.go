package dao_test

import (
	"context"
	"reflect"
	"testing"

	"gorm.io/gorm"

	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	rpServerConfig "github.com/epifi/gamma/recurringpayment/config/server"
	"github.com/epifi/gamma/recurringpayment/dao"
)

type standingInstructionTestSuite struct {
	conf                   *rpServerConfig.Config
	db                     *gorm.DB
	standingInstructionDao dao.StandingInstructionDao
}

func newStandingInstructionTestSuite(conf *rpServerConfig.Config, db *gorm.DB, standingInstructionDao dao.StandingInstructionDao) standingInstructionTestSuite {
	return standingInstructionTestSuite{conf: conf, db: db, standingInstructionDao: standingInstructionDao}
}

var (
	sits standingInstructionTestSuite
)

func TestStandingInstructionDaoCRDB_Create(t *testing.T) {

	// Clean database, run migrations and load fixtures
	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb, ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	si1 := &siPb.StandingInstruction{
		RecurringPaymentId: "RP-100",
		SecureToken:        "hsvdhvsjdvja",
	}

	tests := []struct {
		name                string
		standingInstruction *siPb.StandingInstruction
		want                *siPb.StandingInstruction
		wantErr             bool
	}{
		{
			name:                "created successfully",
			standingInstruction: si1,
			want:                si1,
			wantErr:             false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := sits.standingInstructionDao.Create(context.Background(), tt.standingInstruction)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want != nil && got != nil {
				tt.want.Id = got.Id
				tt.want.CreatedAt = got.CreatedAt
				tt.want.UpdatedAt = got.UpdatedAt
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Create() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStandingInstructionDaoCRDB_GetByRecurringPaymentId(t *testing.T) {

	// Clean database, run migrations and load fixtures
	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb, ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	si1 := &siPb.StandingInstruction{
		RecurringPaymentId: "RP-18",
		SecureToken:        "hsvdhvsjdvja",
	}
	si1, _ = sits.standingInstructionDao.Create(context.Background(), si1)
	tests := []struct {
		name               string
		recurringPaymentId string
		want               *siPb.StandingInstruction
		wantErr            bool
	}{
		{
			name:               "fetched latest entry successfully",
			recurringPaymentId: "RP-18",
			want:               si1,
			wantErr:            false,
		},
		{
			name:               "failed due to empty recurring payment id",
			recurringPaymentId: "",
			want:               nil,
			wantErr:            true,
		},
		{
			name:               "failed due to invalid recurring id",
			recurringPaymentId: "random-rp",
			want:               nil,
			wantErr:            true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := sits.standingInstructionDao.GetByRecurringPaymentId(context.Background(), tt.recurringPaymentId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByRecurringPaymentId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want != nil {
				tt.want.CreatedAt = got.CreatedAt
				tt.want.UpdatedAt = got.UpdatedAt
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetByRecurringPaymentId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStandingInstructionDaoCRDB_Update(t *testing.T) {

	// Clean database, run migrations and load fixtures
	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb, ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	si1 := &siPb.StandingInstruction{
		RecurringPaymentId: "RP-1",
		SecureToken:        "hsvdhvsjdvja",
	}
	si1, _ = sits.standingInstructionDao.Create(context.Background(), si1)

	si1.SecureToken = "abcde"
	tests := []struct {
		name                string
		standingInstruction *siPb.StandingInstruction
		updateMask          []siPb.StandingInstructionFieldMask
		wantErr             bool
	}{
		{
			name:                "updated successfully",
			standingInstruction: si1,
			updateMask:          []siPb.StandingInstructionFieldMask{siPb.StandingInstructionFieldMask_SECURE_TOKEN},
			wantErr:             false,
		},
		{
			name:                "failed to update due to empty id",
			standingInstruction: &siPb.StandingInstruction{},
			updateMask:          []siPb.StandingInstructionFieldMask{siPb.StandingInstructionFieldMask_SECURE_TOKEN},
			wantErr:             true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := sits.standingInstructionDao.Update(context.Background(), tt.standingInstruction, tt.updateMask); (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
