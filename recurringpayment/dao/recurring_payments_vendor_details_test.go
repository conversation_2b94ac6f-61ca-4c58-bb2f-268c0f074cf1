package dao_test

import (
	"context"
	"reflect"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/pagination"

	recurringPaymentsPb "github.com/epifi/gamma/api/recurringpayment"
	recurringpaymentconf "github.com/epifi/gamma/recurringpayment/config/server"
	"github.com/epifi/gamma/recurringpayment/dao"
)

type RecurringPaymentsVendorDetailsTestSuite struct {
	db                                *gormv2.DB
	recurringPaymentsVendorDetailsDao dao.RecurringPaymentsVendorDetailsDao
	conf                              *recurringpaymentconf.Config
	dbName                            string
}

func newRecurringPaymentsVendorDetailsTestSuite(conf *recurringpaymentconf.Config, db *gormv2.DB, dao dao.RecurringPaymentsVendorDetailsDao, dbName string) RecurringPaymentsVendorDetailsTestSuite {
	return RecurringPaymentsVendorDetailsTestSuite{db: db, recurringPaymentsVendorDetailsDao: dao, conf: conf, dbName: dbName}
}

var (
	recurringPaymentsVendorDetailsTS        RecurringPaymentsVendorDetailsTestSuite
	duplicateRecurringPaymentsVendorDetails = &recurringPaymentsPb.RecurringPaymentsVendorDetails{
		RecurringPaymentId: "recurring-payment-1",
		ActorId:            "actor-1",
		VendorCustomerId:   "vendor-customer-1",
		Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
		VendorPaymentId:    "payment-1",
	}
	recurringPaymentsVendorDetails3 = &recurringPaymentsPb.RecurringPaymentsVendorDetails{
		Id:                 "0b364b3b-6a8f-45e8-9d3d-de718ce46ab6",
		RecurringPaymentId: "recurring-payment-3",
		ActorId:            "actor-3",
		VendorCustomerId:   "vendor-customer-3",
		Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
		VendorPaymentId:    "payment-3",
		CreatedAt:          timestampPb.New(time.Date(2024, 03, 01, 0, 0, 0, 0, datetime.IST)),
		UpdatedAt:          timestampPb.New(time.Date(2024, 03, 01, 0, 0, 0, 0, datetime.IST)),
	}
	existingRecurringPaymentsVendorDetails = &recurringPaymentsPb.RecurringPaymentsVendorDetails{
		Id:                 "a5004f18-5d52-4991-82a9-2a1e5010e901",
		RecurringPaymentId: "recurring-payment-1",
		ActorId:            "actor-1",
		VendorCustomerId:   "vendor-customer-1",
		Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
		VendorPaymentId:    "vendor-authorisation-payment-id-1",
		CreatedAt:          timestampPb.New(time.Date(2024, 07, 01, 18, 20, 0, 0, datetime.UTC)),
		UpdatedAt:          timestampPb.New(time.Date(2024, 07, 02, 00, 00, 0, 0, datetime.IST)),
	}
	existingRecurringPaymentsVendorDetails2 = &recurringPaymentsPb.RecurringPaymentsVendorDetails{
		Id:                 "a5004f18-5d52-4991-82a9-2a1e5010e902",
		RecurringPaymentId: "recurring-payment-2",
		ActorId:            "actor-1",
		VendorCustomerId:   "vendor-customer-2",
		Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
		VendorPaymentId:    "vendor-authorisation-payment-id-2",
		CreatedAt:          timestampPb.New(time.Date(2024, 07, 02, 00, 00, 0, 0, datetime.IST)),
		UpdatedAt:          timestampPb.New(time.Date(2024, 07, 02, 00, 00, 0, 0, datetime.IST)),
	}

	existingRecurringPaymentsVendorDetailsList = []*recurringPaymentsPb.RecurringPaymentsVendorDetails{
		existingRecurringPaymentsVendorDetails,
		existingRecurringPaymentsVendorDetails2,
	}
	existingRecurringPaymentsVendorDetails3 = &recurringPaymentsPb.RecurringPaymentsVendorDetails{
		Id:                 "idd-1",
		RecurringPaymentId: "recurring-payment-idd-1",
		ActorId:            "actor",
		VendorCustomerId:   "vendor-customer-idd-1",
		Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
		VendorPaymentId:    "vendor-payment-idd-1",
		CreatedAt:          timestampPb.New(time.Date(2024, 07, 02, 00, 00, 0, 0, datetime.IST)),
		UpdatedAt:          timestampPb.New(time.Date(2024, 07, 02, 00, 00, 0, 0, datetime.IST)),
	}
)

func TestRecurringPaymentsVendorDetailsDaoCRDB_Create(t *testing.T) {
	dbResProvider, release := dbResourceProviderPool.GetDbConnProviderWithUseCase(t)
	defer release(affectedTestTablesMap)
	idGen := idgen.NewDomainIdGenerator(idgen.NewClock())
	recurringPaymentsVendorDetailsTS.recurringPaymentsVendorDetailsDao = dao.NewRecurringPaymentsVendorDetailsDao(idGen, dbResProvider)
	tests := []struct {
		name          string
		vendorDetails *recurringPaymentsPb.RecurringPaymentsVendorDetails
		want          *recurringPaymentsPb.RecurringPaymentsVendorDetails
		wantErr       bool
	}{
		{
			name:          "successfully create vendor_details entry",
			vendorDetails: recurringPaymentsVendorDetails3,
			want:          recurringPaymentsVendorDetails3,
			wantErr:       false,
		},
		{
			name:          "duplicate entry exists",
			vendorDetails: duplicateRecurringPaymentsVendorDetails,
			want:          nil,
			wantErr:       true,
		},
		{
			name:          "empty recurring payments vendor_details",
			vendorDetails: nil,
			want:          nil,
			wantErr:       true,
		},
		{
			name: "should return error if Actor id is absent recurring payments vendor_details",
			vendorDetails: &recurringPaymentsPb.RecurringPaymentsVendorDetails{
				RecurringPaymentId: "rp-3",
				VendorPaymentId:    "vap-3",
				VendorCustomerId:   "vendor-customer-3",
				Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return error if recurring payment id is absent recurring payments vendor_details",
			vendorDetails: &recurringPaymentsPb.RecurringPaymentsVendorDetails{
				ActorId:          "actor-3",
				VendorPaymentId:  "vap-3",
				VendorCustomerId: "vendor-customer-3",
				Vendor:           commonvgpb.Vendor_FEDERAL_BANK,
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := recurringPaymentsVendorDetailsTS.recurringPaymentsVendorDetailsDao.Create(context.Background(), tt.vendorDetails)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != nil && tt.want != nil {
				tt.want.Id = got.GetId()
				tt.want.CreatedAt = got.GetCreatedAt()
				tt.want.UpdatedAt = got.GetUpdatedAt()
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Create() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRecurringPaymentsVendorDetailsDaoCRDB_GetByRecurringPaymentId(t *testing.T) {
	dbResProvider, release := dbResourceProviderPool.GetDbConnProviderWithUseCase(t)
	defer release(affectedTestTablesMap)
	idGen := idgen.NewDomainIdGenerator(idgen.NewClock())
	recurringPaymentsVendorDetailsTS.recurringPaymentsVendorDetailsDao = dao.NewRecurringPaymentsVendorDetailsDao(idGen, dbResProvider)
	tests := []struct {
		name               string
		recurringPaymentId string
		want               *recurringPaymentsPb.RecurringPaymentsVendorDetails
		wantErr            bool
	}{
		{
			name:               "successfully fetched details for recurring payment Id",
			recurringPaymentId: "recurring-payment-1",
			want:               existingRecurringPaymentsVendorDetails,
			wantErr:            false,
		},
		{
			name:               "empty Recurring Payment id ",
			recurringPaymentId: "",
			want:               nil,
			wantErr:            true,
		},
		{
			name:               "No records found for given recurring payment id",
			recurringPaymentId: "recurring-payment-7",
			want:               nil,
			wantErr:            true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := recurringPaymentsVendorDetailsTS.recurringPaymentsVendorDetailsDao.GetByRecurringPaymentId(context.Background(), tt.recurringPaymentId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByRecurringPaymentId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetByRecurringPaymentId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRecurringPaymentsVendorDetailsDaoCRDB_GetById(t *testing.T) {
	dbResProvider, release := dbResourceProviderPool.GetDbConnProviderWithUseCase(t)
	defer release(affectedTestTablesMap)
	idGen := idgen.NewDomainIdGenerator(idgen.NewClock())
	recurringPaymentsVendorDetailsTS.recurringPaymentsVendorDetailsDao = dao.NewRecurringPaymentsVendorDetailsDao(idGen, dbResProvider)

	tests := []struct {
		name    string
		id      string
		want    *recurringPaymentsPb.RecurringPaymentsVendorDetails
		wantErr bool
	}{
		{
			name:    "successfully fetched details for Id",
			id:      "a5004f18-5d52-4991-82a9-2a1e5010e901",
			want:    existingRecurringPaymentsVendorDetails,
			wantErr: false,
		},
		{
			name:    "empty id ",
			id:      "",
			want:    nil,
			wantErr: true,
		},
		{
			name:    "No records found for given recurring payment id",
			id:      "a5004f18-5d52-4991-82a9-2a1e5010e910",
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := recurringPaymentsVendorDetailsTS.recurringPaymentsVendorDetailsDao.GetById(context.Background(), tt.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRecurringPaymentsVendorDetailsDaoCRDB_GetByVendorCustomerId(t *testing.T) {
	dbResProvider, release := dbResourceProviderPool.GetDbConnProviderWithUseCase(t)
	defer release(affectedTestTablesMap)
	idGen := idgen.NewDomainIdGenerator(idgen.NewClock())
	recurringPaymentsVendorDetailsTS.recurringPaymentsVendorDetailsDao = dao.NewRecurringPaymentsVendorDetailsDao(idGen, dbResProvider)

	tests := []struct {
		name             string
		vendorCustomerId string
		want             *recurringPaymentsPb.RecurringPaymentsVendorDetails
		wantErr          bool
	}{
		{
			name:             "successfully fetched details for vendor customer id",
			vendorCustomerId: "vendor-customer-idd-1",
			want:             existingRecurringPaymentsVendorDetails3,
			wantErr:          false,
		},
		{
			name:             "empty id ",
			vendorCustomerId: "",
			want:             nil,
			wantErr:          true,
		},
		{
			name:             "No records found for given vendor customer id",
			vendorCustomerId: "random-idd",
			want:             nil,
			wantErr:          true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := recurringPaymentsVendorDetailsTS.recurringPaymentsVendorDetailsDao.GetByVendorCustomerId(context.Background(), tt.vendorCustomerId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByVendorCustomerId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetByVendorCustomerId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRecurringPaymentsVendorDetailsDaoCRDB_GetByVendorPaymentId(t *testing.T) {
	dbResProvider, release := dbResourceProviderPool.GetDbConnProviderWithUseCase(t)
	defer release(affectedTestTablesMap)
	idGen := idgen.NewDomainIdGenerator(idgen.NewClock())
	recurringPaymentsVendorDetailsTS.recurringPaymentsVendorDetailsDao = dao.NewRecurringPaymentsVendorDetailsDao(idGen, dbResProvider)

	tests := []struct {
		name            string
		vendorPaymentId string
		want            *recurringPaymentsPb.RecurringPaymentsVendorDetails
		wantErr         bool
	}{
		{
			name:            "successfully fetched details for vendor payment id",
			vendorPaymentId: "vendor-payment-idd-1",
			want:            existingRecurringPaymentsVendorDetails3,
			wantErr:         false,
		},
		{
			name:            "empty id ",
			vendorPaymentId: "",
			want:            nil,
			wantErr:         true,
		},
		{
			name:            "No records found for given vendor payment id",
			vendorPaymentId: "random idd",
			want:            nil,
			wantErr:         true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := recurringPaymentsVendorDetailsTS.recurringPaymentsVendorDetailsDao.GetByVendorPaymentId(context.Background(), tt.vendorPaymentId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByVendorPaymentId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetByVendorPaymentId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRecurringPaymentsVendorDetailsDaoCRDB_GetByActorId(t *testing.T) {
	dbResProvider, release := dbResourceProviderPool.GetDbConnProviderWithUseCase(t)
	defer release(affectedTestTablesMap)
	idGen := idgen.NewDomainIdGenerator(idgen.NewClock())
	recurringPaymentsVendorDetailsTS.recurringPaymentsVendorDetailsDao = dao.NewRecurringPaymentsVendorDetailsDao(idGen, dbResProvider)
	tests := []struct {
		name    string
		actorId string
		want    []*recurringPaymentsPb.RecurringPaymentsVendorDetails
		wantErr bool
	}{
		{
			name:    "successfully fetched List of details for Actor Id",
			actorId: "actor-1",
			want:    existingRecurringPaymentsVendorDetailsList,
			wantErr: false,
		},
		{
			name:    "empty Actor id ",
			actorId: "",
			want:    nil,
			wantErr: true,
		},
		{
			name:    "No records found for given actor id",
			actorId: "actor-7",
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := recurringPaymentsVendorDetailsTS.recurringPaymentsVendorDetailsDao.GetByActorId(context.Background(), tt.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetByActorId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRecurringPaymentsVendorDetailsDaoCRDB_Update(t *testing.T) {
	dbResProvider, release := dbResourceProviderPool.GetDbConnProviderWithUseCase(t)
	defer release(affectedTestTablesMap)
	idGen := idgen.NewDomainIdGenerator(idgen.NewClock())
	recurringPaymentsVendorDetailsTS.recurringPaymentsVendorDetailsDao = dao.NewRecurringPaymentsVendorDetailsDao(idGen, dbResProvider)

	recurringPaymentVendorDetails4 := &recurringPaymentsPb.RecurringPaymentsVendorDetails{
		RecurringPaymentId: "recurring-payment-4",
		ActorId:            "actor-4",
		Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
		CreatedAt:          timestampPb.New(time.Date(2024, 03, 01, 0, 0, 0, 0, datetime.IST)),
		UpdatedAt:          timestampPb.New(time.Date(2024, 03, 01, 0, 0, 0, 0, datetime.IST)),
	}

	recurringPaymentVendorDetails4, _ = recurringPaymentsVendorDetailsTS.recurringPaymentsVendorDetailsDao.Create(context.Background(), recurringPaymentVendorDetails4)
	recurringPaymentVendorDetails4.VendorCustomerId = "vendor-customer-4"
	recurringPaymentVendorDetails4.VendorPaymentId = "vendor-payment-4"

	recurringPaymentVendorDetails5 := &recurringPaymentsPb.RecurringPaymentsVendorDetails{
		RecurringPaymentId: "recurring-payment-5",
		ActorId:            "actor-5",
		VendorPaymentId:    "vendor-payment-5",
		VendorCustomerId:   "vendor-customer-5",
		Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
		CreatedAt:          timestampPb.New(time.Date(2024, 03, 01, 0, 0, 0, 0, datetime.IST)),
		UpdatedAt:          timestampPb.New(time.Date(2024, 03, 01, 0, 0, 0, 0, datetime.IST)),
	}

	recurringPaymentVendorDetails5, _ = recurringPaymentsVendorDetailsTS.recurringPaymentsVendorDetailsDao.Create(context.Background(), recurringPaymentVendorDetails5)
	recurringPaymentVendorDetails5.RecurringPaymentId = "recurring-payment-6"

	tests := []struct {
		name          string
		vendorDetails *recurringPaymentsPb.RecurringPaymentsVendorDetails
		updateMask    []recurringPaymentsPb.RecurringPaymentVendorDetailsFieldMask
		wantErr       bool
	}{
		{
			name:          "updated successfully",
			vendorDetails: recurringPaymentVendorDetails4,
			updateMask: []recurringPaymentsPb.RecurringPaymentVendorDetailsFieldMask{recurringPaymentsPb.RecurringPaymentVendorDetailsFieldMask_RECURRING_PAYMENT_VENDOR_DETAILS_FIELD_MASK_VENDOR_CUSTOMER_ID,
				recurringPaymentsPb.RecurringPaymentVendorDetailsFieldMask_RECURRING_PAYMENT_VENDOR_DETAILS_FIELD_MASK_VENDOR_PAYMENT_ID},

			wantErr: false,
		},
		{
			name:          "updated Vendor Recurring payment Id  successfully",
			vendorDetails: recurringPaymentVendorDetails5,
			updateMask:    []recurringPaymentsPb.RecurringPaymentVendorDetailsFieldMask{recurringPaymentsPb.RecurringPaymentVendorDetailsFieldMask_RECURRING_PAYMENT_VENDOR_DETAILS_FIELD_MASK_RECURRING_PAYMENT_ID},
			wantErr:       false,
		},
		{
			name:          "failed due to empty id",
			vendorDetails: &recurringPaymentsPb.RecurringPaymentsVendorDetails{Id: ""},
			updateMask:    nil,
			wantErr:       true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := recurringPaymentsVendorDetailsTS.recurringPaymentsVendorDetailsDao.Update(context.Background(), tt.vendorDetails, tt.updateMask); (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
			}
			if !tt.wantErr {
				got, err := recurringPaymentsVendorDetailsTS.recurringPaymentsVendorDetailsDao.GetById(context.Background(), tt.vendorDetails.GetId())
				if (err != nil) != tt.wantErr {
					t.Errorf("GetByCardId() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				assertRecurringPaymentsVendorDetails(t, tt.vendorDetails, got)
			}
		})
	}
}

func TestRecurringPaymentsVendorDetailsDaoCRDB_Get(t *testing.T) {
	dbResProvider, release := dbResourceProviderPool.GetDbConnProviderWithUseCase(t)
	defer release(affectedTestTablesMap)
	idGen := idgen.NewDomainIdGenerator(idgen.NewClock())
	recurringPaymentsVendorDetailsTS.recurringPaymentsVendorDetailsDao = dao.NewRecurringPaymentsVendorDetailsDao(idGen, dbResProvider)

	nextPageToken, pageTokenErr := pagination.GetPageToken(&rpc.PageContextRequest{
		Token: &rpc.PageContextRequest_AfterToken{
			AfterToken: "****************************************************************************************",
		},
	})
	require.NoError(t, pageTokenErr, "Error in generating next page token")

	reversedNextPageToken, reversePageTokenErr := pagination.GetPageToken(&rpc.PageContextRequest{
		Token: &rpc.PageContextRequest_BeforeToken{
			BeforeToken: "****************************************************************************************",
		},
	})
	require.NoError(t, reversePageTokenErr, "Error in generating reverse page token")

	type args struct {
		vendor    commonvgpb.Vendor
		pageToken *pagination.PageToken
		pageSize  uint32
	}
	type want struct {
		recurringPaymentVendorDetails []*recurringPaymentsPb.RecurringPaymentsVendorDetails
		pageCtxRes                    *rpc.PageContextResponse
	}
	tests := []struct {
		name    string
		args    *args
		want    *want
		wantErr bool
	}{
		{
			name: "should return the partial result of page size along with the next page response",
			args: &args{
				vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				pageToken: nil,
				pageSize:  1,
			},
			want: &want{
				recurringPaymentVendorDetails: []*recurringPaymentsPb.RecurringPaymentsVendorDetails{
					{
						Id:                 "a5004f18-5d52-4991-82a9-2a1e5010e902",
						ActorId:            "actor-1",
						RecurringPaymentId: "recurring-payment-2",
						VendorCustomerId:   "vendor-customer-2",
						Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
						VendorPaymentId:    "vendor-authorisation-payment-id-2",
						CreatedAt:          timestampPb.New(time.Date(2024, 7, 1, 18, 30, 0, 0, datetime.UTC)),
						UpdatedAt:          timestampPb.New(time.Date(2024, 7, 1, 18, 30, 0, 0, datetime.UTC)),
					},
				},
				pageCtxRes: &rpc.PageContextResponse{
					AfterToken: "****************************************************************************************",
					HasAfter:   true,
				},
			},
			wantErr: false,
		},
		{
			name: "should return the before token when the requested page size is greater than available entries",
			args: &args{
				vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				pageToken: nextPageToken,
				pageSize:  3,
			},
			want: &want{
				recurringPaymentVendorDetails: []*recurringPaymentsPb.RecurringPaymentsVendorDetails{
					{
						Id:                 "a5004f18-5d52-4991-82a9-2a1e5010e902",
						ActorId:            "actor-1",
						RecurringPaymentId: "recurring-payment-2",
						VendorCustomerId:   "vendor-customer-2",
						Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
						VendorPaymentId:    "vendor-authorisation-payment-id-2",
						CreatedAt:          timestampPb.New(time.Date(2024, 7, 1, 18, 30, 0, 0, datetime.UTC)),
						UpdatedAt:          timestampPb.New(time.Date(2024, 7, 1, 18, 30, 0, 0, datetime.UTC)),
					},
					{
						Id:                 "a5004f18-5d52-4991-82a9-2a1e5010e901",
						ActorId:            "actor-1",
						RecurringPaymentId: "recurring-payment-1",
						VendorCustomerId:   "vendor-customer-1",
						Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
						VendorPaymentId:    "vendor-authorisation-payment-id-1",
						CreatedAt:          timestampPb.New(time.Date(2024, 7, 1, 18, 20, 0, 0, datetime.UTC)),
						UpdatedAt:          timestampPb.New(time.Date(2024, 7, 1, 18, 30, 0, 0, datetime.UTC)),
					},
					{
						Id:                 "a5004f18-5d52-4991-82a9-2a1e5010e900",
						ActorId:            "actor-0",
						RecurringPaymentId: "recurring-payment-0",
						VendorCustomerId:   "vendor-customer-0",
						Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
						VendorPaymentId:    "vendor-authorisation-payment-id-0",
						CreatedAt:          timestampPb.New(time.Date(2024, 7, 1, 18, 19, 0, 0, datetime.UTC)),
						UpdatedAt:          timestampPb.New(time.Date(2024, 7, 1, 18, 19, 0, 0, datetime.UTC)),
					},
				},
				pageCtxRes: &rpc.PageContextResponse{
					BeforeToken: "****************************************************************************************",
					HasBefore:   true,
				},
			},
			wantErr: false,
		},
		{
			name: "should return the before and after tokens when the requested page size is less than available entries",
			args: &args{
				vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				pageToken: nextPageToken,
				pageSize:  1,
			},
			want: &want{
				recurringPaymentVendorDetails: []*recurringPaymentsPb.RecurringPaymentsVendorDetails{
					{
						Id:                 "a5004f18-5d52-4991-82a9-2a1e5010e902",
						ActorId:            "actor-1",
						RecurringPaymentId: "recurring-payment-2",
						VendorCustomerId:   "vendor-customer-2",
						Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
						VendorPaymentId:    "vendor-authorisation-payment-id-2",
						CreatedAt:          timestampPb.New(time.Date(2024, 7, 1, 18, 30, 0, 0, datetime.UTC)),
						UpdatedAt:          timestampPb.New(time.Date(2024, 7, 1, 18, 30, 0, 0, datetime.UTC)),
					},
				},
				pageCtxRes: &rpc.PageContextResponse{
					BeforeToken: "****************************************************************************************",
					HasBefore:   true,
					AfterToken:  "****************************************************************************************",
					HasAfter:    true,
				},
			},
			wantErr: false,
		},
		{
			name: "should honour offset when it is passed",
			args: &args{
				vendor: commonvgpb.Vendor_FEDERAL_BANK,
				pageToken: &pagination.PageToken{
					Offset: 1,
				},
				pageSize: 1,
			},
			want: &want{
				recurringPaymentVendorDetails: []*recurringPaymentsPb.RecurringPaymentsVendorDetails{
					{
						Id:                 "a5004f18-5d52-4991-82a9-2a1e5010e902",
						ActorId:            "actor-1",
						RecurringPaymentId: "recurring-payment-2",
						VendorCustomerId:   "vendor-customer-2",
						Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
						VendorPaymentId:    "vendor-authorisation-payment-id-2",
						CreatedAt:          timestampPb.New(time.Date(2024, 7, 1, 18, 30, 0, 0, datetime.UTC)),
						UpdatedAt:          timestampPb.New(time.Date(2024, 7, 1, 18, 30, 0, 0, datetime.UTC)),
					},
				},
				pageCtxRes: &rpc.PageContextResponse{
					BeforeToken: "****************************************************************************************",
					HasBefore:   true,
					AfterToken:  "****************************************************************************************",
					HasAfter:    true,
				},
			},
			wantErr: false,
		},
		{
			name: "should return result in reverse order when IsReverse is true",
			args: &args{
				vendor: commonvgpb.Vendor_FEDERAL_BANK,
				pageToken: &pagination.PageToken{
					IsReverse: true,
				},
				pageSize: 1,
			},
			want: &want{
				recurringPaymentVendorDetails: []*recurringPaymentsPb.RecurringPaymentsVendorDetails{
					{
						Id:                 "a5004f18-5d52-4991-82a9-2a1e5010e901",
						ActorId:            "actor-1",
						RecurringPaymentId: "recurring-payment-1",
						VendorCustomerId:   "vendor-customer-1",
						Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
						VendorPaymentId:    "vendor-authorisation-payment-id-1",
						CreatedAt:          timestampPb.New(time.Date(2024, 7, 1, 18, 20, 0, 0, datetime.UTC)),
						UpdatedAt:          timestampPb.New(time.Date(2024, 7, 1, 18, 30, 0, 0, datetime.UTC)),
					},
				},
				pageCtxRes: &rpc.PageContextResponse{
					BeforeToken: "****************************************************************************************",
					HasBefore:   true,
				},
			},
			wantErr: false,
		},
		{
			name: "should return paginated result in reverse order when IsReverse is true",
			args: &args{
				vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				pageToken: reversedNextPageToken,
				pageSize:  1,
			},
			want: &want{
				recurringPaymentVendorDetails: []*recurringPaymentsPb.RecurringPaymentsVendorDetails{
					{
						Id:                 "a5004f18-5d52-4991-82a9-2a1e5010e901",
						ActorId:            "actor-1",
						RecurringPaymentId: "recurring-payment-1",
						VendorCustomerId:   "vendor-customer-1",
						Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
						VendorPaymentId:    "vendor-authorisation-payment-id-1",
						CreatedAt:          timestampPb.New(time.Date(2024, 7, 1, 18, 20, 0, 0, datetime.UTC)),
						UpdatedAt:          timestampPb.New(time.Date(2024, 7, 1, 18, 30, 0, 0, datetime.UTC)),
					},
				},
				pageCtxRes: &rpc.PageContextResponse{
					BeforeToken: "****************************************************************************************",
					HasBefore:   true,
				},
			},
			wantErr: false,
		},
		{
			name: "should return record not found error when no records are found",
			args: &args{
				vendor: commonvgpb.Vendor_FEDERAL_BANK,
				pageToken: &pagination.PageToken{
					Offset: 5,
				},
				pageSize: 1,
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {

			got, gotRpcPageCtxResp, err := recurringPaymentsVendorDetailsTS.
				recurringPaymentsVendorDetailsDao.
				Get(context.Background(), tc.args.pageToken, tc.args.pageSize, dao.WithVendorFilter(tc.args.vendor))
			if tc.wantErr {
				require.Error(t, err, "Get() error = %v, wantErr %v", err, tc.wantErr)
				return
			}
			require.NoError(t, err)

			if !reflect.DeepEqual(got, tc.want.recurringPaymentVendorDetails) {
				t.Errorf("Get() got = %v, want %v", got, tc.want.recurringPaymentVendorDetails)
			}
			if !reflect.DeepEqual(gotRpcPageCtxResp, tc.want.pageCtxRes) {
				t.Errorf("Get() gotRpcPageCtxResp = %v, want %v", gotRpcPageCtxResp, tc.want.pageCtxRes)
			}
		})
	}
}

func assertRecurringPaymentsVendorDetails(t *testing.T, expected, actual *recurringPaymentsPb.RecurringPaymentsVendorDetails) {
	if expected == nil && actual == nil {
		return
	}

	expected.CreatedAt = actual.GetCreatedAt()
	expected.UpdatedAt = actual.GetUpdatedAt()
	if diff := cmp.Diff(actual, expected, protocmp.Transform()); diff != "" {
		t.Errorf("unexpected difference:\n%v", diff)
	}
}
