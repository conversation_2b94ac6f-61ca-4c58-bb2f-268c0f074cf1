package dao_test

import (
	"context"
	"reflect"
	"testing"

	"gorm.io/gorm"

	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	rpServerConfig "github.com/epifi/gamma/recurringpayment/config/server"
	"github.com/epifi/gamma/recurringpayment/dao"
)

type standingInstructionRequestTestSuite struct {
	conf                          *rpServerConfig.Config
	db                            *gorm.DB
	standingInstructionRequestDao dao.StandingInstructionRequestDao
}

func newStandingInstructionRequestTestSuite(conf *rpServerConfig.Config, db *gorm.DB, standingInstructionRequestDao dao.StandingInstructionRequestDao) standingInstructionRequestTestSuite {
	return standingInstructionRequestTestSuite{conf: conf, db: db, standingInstructionRequestDao: standingInstructionRequestDao}
}

var (
	standingInstructionRequestTS standingInstructionRequestTestSuite
)

func TestStandingInstructionRequestDaoCRDB_Create(t *testing.T) {
	tests := []struct {
		name                       string
		standingInstructionRequest *siPb.StandingInstructionRequest
		want                       *siPb.StandingInstructionRequest
		wantErr                    bool
	}{
		{
			name: "created successfully",
			standingInstructionRequest: &siPb.StandingInstructionRequest{
				StandingInstructionId: "si-id-1",
				VendorRequestId:       "req-1",
				RequestType:           siPb.RequestType_CREATE,
				State:                 siPb.State_SUCCESS,
			},
			want: &siPb.StandingInstructionRequest{
				StandingInstructionId: "si-id-1",
				VendorRequestId:       "req-1",
				RequestType:           siPb.RequestType_CREATE,
				State:                 siPb.State_SUCCESS,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			// Clean database, run migrations and load fixtures
			pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb, ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

			got, err := standingInstructionRequestTS.standingInstructionRequestDao.Create(context.Background(), tt.standingInstructionRequest)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			tt.want.Id = got.Id
			tt.want.CreatedAt = got.CreatedAt
			tt.want.UpdatedAt = got.UpdatedAt
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Create() got = %v, want %v", got, tt.want)
			}
		})
	}
}

// nolint
func TestStandingInstructionRequestDaoCRDB_GetByStandingInstructionIdAndRequestType(t *testing.T) {

	// Clean database, run migrations and load fixtures
	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb, ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	siReq1 := &siPb.StandingInstructionRequest{
		StandingInstructionId: "si-id-1",
		VendorRequestId:       "req-1",
		RequestType:           siPb.RequestType_MODIFY,
		State:                 siPb.State_FAILED,
	}
	siReq2 := &siPb.StandingInstructionRequest{
		StandingInstructionId: "si-id-1",
		VendorRequestId:       "req-1",
		RequestType:           siPb.RequestType_MODIFY,
		State:                 siPb.State_QUEUED,
	}
	siReq1, _ = standingInstructionRequestTS.standingInstructionRequestDao.Create(context.Background(), siReq1)
	siReq2, _ = standingInstructionRequestTS.standingInstructionRequestDao.Create(context.Background(), siReq2)

	tests := []struct {
		name                  string
		standingInstructionId string
		requestType           siPb.RequestType
		want                  *siPb.StandingInstructionRequest
		wantErr               bool
	}{
		{
			name:                  "fetched successfully",
			standingInstructionId: "si-id-1",
			requestType:           siPb.RequestType_MODIFY,
			want:                  siReq2,
			wantErr:               false,
		},
		{
			name:                  "record not found",
			standingInstructionId: "si-id-1",
			requestType:           siPb.RequestType_REVOKE,
			want:                  nil,
			wantErr:               true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := standingInstructionRequestTS.standingInstructionRequestDao.
				GetByStandingInstructionIdAndRequestType(context.Background(), tt.standingInstructionId, tt.requestType)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByStandingInstructionIdAndRequestType() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want != nil {
				tt.want.CreatedAt = got.CreatedAt
				tt.want.UpdatedAt = got.UpdatedAt
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetByStandingInstructionIdAndRequestType() got = %v, want %v", got, tt.want)
			}
		})
	}
}

// nolint
func TestStandingInstructionRequestDaoCRDB_Update(t *testing.T) {

	// Clean database, run migrations and load fixtures
	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb, ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	siReq1 := &siPb.StandingInstructionRequest{
		StandingInstructionId: "si-id-1",
		VendorRequestId:       "req-1",
		RequestType:           siPb.RequestType_MODIFY,
		State:                 siPb.State_QUEUED,
	}
	siReq1, _ = standingInstructionRequestTS.standingInstructionRequestDao.Create(context.Background(), siReq1)
	siReq1.State = siPb.State_SUCCESS

	tests := []struct {
		name                       string
		standingInstructionRequest *siPb.StandingInstructionRequest
		updateMask                 []siPb.StandingInstructionRequestFieldMask
		wantErr                    bool
	}{
		{
			name:                       "updated successfully",
			standingInstructionRequest: siReq1,
			updateMask:                 []siPb.StandingInstructionRequestFieldMask{siPb.StandingInstructionRequestFieldMask_STATE},
			wantErr:                    false,
		},
		{
			name:                       "failed due to empty id",
			standingInstructionRequest: &siPb.StandingInstructionRequest{Id: ""},
			updateMask:                 []siPb.StandingInstructionRequestFieldMask{siPb.StandingInstructionRequestFieldMask_STATE},
			wantErr:                    true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := standingInstructionRequestTS.standingInstructionRequestDao.Update(context.Background(), tt.standingInstructionRequest,
				tt.updateMask); (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStandingInstructionRequestDaoCRDB_UpdateAndChangeStatus(t *testing.T) {

	// Clean database, run migrations and load fixtures
	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb, ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	siReq1 := &siPb.StandingInstructionRequest{
		StandingInstructionId: "si-id-1",
		VendorRequestId:       "req-1",
		RequestType:           siPb.RequestType_MODIFY,
		State:                 siPb.State_QUEUED,
	}
	siReq1, _ = standingInstructionRequestTS.standingInstructionRequestDao.Create(context.Background(), siReq1)

	tests := []struct {
		name                       string
		standingInstructionRequest *siPb.StandingInstructionRequest
		updateMask                 []siPb.StandingInstructionRequestFieldMask
		currentState               siPb.State
		nextState                  siPb.State
		wantErr                    bool
	}{
		{
			name:                       "updated successfully",
			standingInstructionRequest: siReq1,
			currentState:               siPb.State_QUEUED,
			nextState:                  siPb.State_SUCCESS,
			wantErr:                    false,
		},
		{
			name:                       "failed due to empty id",
			standingInstructionRequest: &siPb.StandingInstructionRequest{Id: ""},
			updateMask:                 []siPb.StandingInstructionRequestFieldMask{siPb.StandingInstructionRequestFieldMask_STATE},
			wantErr:                    true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := standingInstructionRequestTS.standingInstructionRequestDao.UpdateAndChangeStatus(context.Background(),
				tt.standingInstructionRequest, tt.updateMask, tt.currentState, tt.nextState); (err != nil) != tt.wantErr {
				t.Errorf("UpdateAndChangeStatus() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
