package model

import (
	"database/sql/driver"
	"fmt"
	"time"

	"encoding/json"

	"github.com/samber/lo"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/be-common/pkg/nulltypes"
)

type RecurringPaymentsAction struct {
	Id string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`

	RecurringPaymentId string

	ClientRequestId string

	Action rpPb.Action

	ActionMetadata *rpPb.ActionMetadata

	CreatedAt time.Time

	UpdatedAt time.Time

	DeletedAt gorm.DeletedAt

	State rpPb.ActionState

	VendorRequestId nulltypes.NullString

	ExpireAt *time.Time

	InitiatedBy rpPb.InitiatedBy

	ActionDetailedStatus *rpPb.ActionDetailedStatus

	// stores the detailed status in form of fi status code for recurring payment actions.
	// generally useful in failure cases where we want know granular reason for failure.
	ActionDetailedStatusInfo *rpPb.ActionDetailedStatusInfo

	Remarks string

	// NextActions stores the next action deeplink associated with a payment
	NextActions *RecurringPaymentNextActions
}

type RecurringPaymentNextActions struct {
	// This deeplink is where user is redirected post authorisation of action
	PostAuthDeeplink *deeplinkPb.Deeplink

	// This deeplink is where user is redirected post action execution. Irrespective of the
	// success or failure of action.
	PostActionDeeplink *deeplinkPb.Deeplink
}

func NewRecurringPaymentsAction(protoRecurringPaymentAction *rpPb.RecurringPaymentsAction) *RecurringPaymentsAction {
	model := &RecurringPaymentsAction{
		Id:                       protoRecurringPaymentAction.GetId(),
		RecurringPaymentId:       protoRecurringPaymentAction.GetRecurringPaymentId(),
		ClientRequestId:          protoRecurringPaymentAction.GetClientRequestId(),
		Action:                   protoRecurringPaymentAction.GetAction(),
		ActionMetadata:           protoRecurringPaymentAction.GetActionMetadata(),
		State:                    protoRecurringPaymentAction.GetState(),
		VendorRequestId:          nulltypes.NewNullString(protoRecurringPaymentAction.GetVendorRequestId()),
		InitiatedBy:              protoRecurringPaymentAction.GetInitiatedBy(),
		ActionDetailedStatus:     protoRecurringPaymentAction.GetActionDetailedStatus(),
		ActionDetailedStatusInfo: protoRecurringPaymentAction.GetActionDetailedStatusInfo(),
		Remarks:                  protoRecurringPaymentAction.GetRemarks(),
		NextActions: &RecurringPaymentNextActions{
			PostAuthDeeplink:   lo.Must(unmarshalAnyToDeeplink(protoRecurringPaymentAction.GetPostAuthDeeplink())),
			PostActionDeeplink: lo.Must(unmarshalAnyToDeeplink(protoRecurringPaymentAction.GetPostActionDeeplink())),
		},
	}
	if protoRecurringPaymentAction.GetExpireAt() != nil {
		expireAt := protoRecurringPaymentAction.GetExpireAt().AsTime()
		model.ExpireAt = &expireAt
	}
	return model
}

func unmarshalAnyToDeeplink(src *anypb.Any) (*deeplinkPb.Deeplink, error) {
	if src == nil {
		return nil, nil
	}

	dl := &deeplinkPb.Deeplink{}
	err := src.UnmarshalTo(dl)
	if err != nil {
		return nil, err
	}

	return dl, nil
}

func (model *RecurringPaymentsAction) GetProto() *rpPb.RecurringPaymentsAction {
	proto := &rpPb.RecurringPaymentsAction{
		Id:                       model.Id,
		RecurringPaymentId:       model.RecurringPaymentId,
		ClientRequestId:          model.ClientRequestId,
		Action:                   model.Action,
		ActionMetadata:           model.ActionMetadata,
		State:                    model.State,
		VendorRequestId:          model.VendorRequestId.GetValue(),
		InitiatedBy:              model.InitiatedBy,
		ActionDetailedStatus:     model.ActionDetailedStatus,
		ActionDetailedStatusInfo: model.ActionDetailedStatusInfo,
		Remarks:                  model.Remarks,
	}
	proto.CreatedAt = timestamppb.New(model.CreatedAt)
	proto.UpdatedAt = timestamppb.New(model.UpdatedAt)

	if model.DeletedAt.Valid {
		proto.DeletedAt = timestamppb.New(model.DeletedAt.Time)
	}
	if model.ExpireAt != nil {
		proto.ExpireAt = timestamppb.New(*model.ExpireAt)
	}
	if model.NextActions != nil {
		if model.NextActions.PostAuthDeeplink != nil {
			proto.PostAuthDeeplink = lo.Must(anypb.New(model.NextActions.PostAuthDeeplink))
		}

		if model.NextActions.PostActionDeeplink != nil {
			proto.PostActionDeeplink = lo.Must(anypb.New(model.NextActions.PostActionDeeplink))
		}
	}
	return proto
}

func (r *RecurringPaymentNextActions) Value() (driver.Value, error) {
	return json.Marshal(r)
}

func (r *RecurringPaymentNextActions) Scan(src any) error {
	b, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("got: %T want: []bytes", src)
	}

	return json.Unmarshal(b, r)
}
