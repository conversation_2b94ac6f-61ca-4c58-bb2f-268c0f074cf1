package model

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"time"

	"gorm.io/gorm"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	pb "github.com/epifi/gamma/api/recurringpayment"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/be-common/pkg/money"
)

type RecurringPayment struct {
	Id string `gorm:"primary_key"`

	// actor who initiated the recurring payment
	FromActorId string

	// receiving entity of the recurring payment
	ToActorId string

	// type of recurring payment
	Type pb.RecurringPaymentType

	// remitter payment instrument
	PiFrom string

	// beneficiary payment instrument
	PiTo string

	// Amount defined in recurring payment can either be the maximum amount transferable over the frequency window
	// or the exact amount to be transferred. We will use combination of amount_amount type to decide those
	Amount *money.Money

	AmountType pb.AmountType

	StartDate *time.Time

	EndDate *time.Time

	RecurrenceRule *pb.RecurrenceRule

	MaximumAllowedTxns int32

	PartnerBank commonvgpb.Vendor

	PreferredPaymentProtocol paymentPb.PaymentProtocol

	// Different states of recurring payment
	State pb.RecurringPaymentState

	Ownership pb.RecurringPaymentOwnership

	Provenance pb.RecurrencePaymentProvenance
	// enum to identify if the recurring payment is being executed via an external payment
	// gateway or via the native flow (involving the fi-federal savings account)
	PaymentRoute pb.RecurringPaymentRoute
	// identify the ownership of the entities that will be created as a result
	// of this recurring payment flow
	EntityOwnership commontypes.Ownership

	UIEntryPoint pb.UIEntryPoint

	InitiatedBy pb.InitiatedBy

	CreatedAt time.Time

	UpdatedAt time.Time

	DeletedAt gorm.DeletedAt

	ExternalId string

	// if false the payee shouldn't receive notification for the recurringpayment
	ShareToPayee bool

	Remarks string

	PauseInterval *types.Interval
}
