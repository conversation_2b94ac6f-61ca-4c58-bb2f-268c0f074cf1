package model

import (
	"time"

	"gorm.io/gorm"

	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
)

type StandingInstructionRequest struct {
	Id string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`

	StandingInstructionId string

	VendorRequestId string

	RequestType siPb.RequestType

	State siPb.State

	CreatedAt time.Time

	UpdatedAt time.Time

	DeletedAt gorm.DeletedAt
}
