package model

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/pagination"

	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/nulltypes"

	recurringPaymentPb "github.com/epifi/gamma/api/recurringpayment"
)

type RecurringPaymentsVendorDetails struct {
	ID string `gorm:"primary_key"`
	// actor id
	ActorId string
	// recurring payment id
	RecurringPaymentId string
	// customer id received from vendor e.g. PG customer id  from Razorpay
	VendorCustomerId nulltypes.NullString
	// Represents a vendor or partner that epifi integrates with e.g. Razorpay
	Vendor commonvgpb.Vendor
	// authorisation payment id received from vendor e.g. from Razorpay
	VendorPaymentId nulltypes.NullString
	// time of creation of the transaction
	CreatedAt time.Time
	// last updated time of the transaction
	UpdatedAt time.Time
	// signifies the date of soft deletion of the transaction entry
	DeletedAt gorm.DeletedAt
}

func (r *RecurringPaymentsVendorDetails) GetProto() *recurringPaymentPb.RecurringPaymentsVendorDetails {
	protoRecurringPaymentsVendorDetails := &recurringPaymentPb.RecurringPaymentsVendorDetails{
		Id:                 r.ID,
		ActorId:            r.ActorId,
		RecurringPaymentId: r.RecurringPaymentId,
		VendorCustomerId:   r.VendorCustomerId.GetValue(),
		Vendor:             r.Vendor,
		VendorPaymentId:    r.VendorPaymentId.GetValue(),
		CreatedAt:          timestampPb.New(r.CreatedAt),
		UpdatedAt:          timestampPb.New(r.UpdatedAt),
	}

	if r.DeletedAt.Valid {
		protoRecurringPaymentsVendorDetails.DeletedAt = timestampPb.New(r.DeletedAt.Time)
	}

	return protoRecurringPaymentsVendorDetails
}

func (r *RecurringPaymentsVendorDetails) TableName() string {
	return "recurring_payments_vendor_details"
}

type RecurringPaymentsVendorDetailsRows []*RecurringPaymentsVendorDetails

func (rs RecurringPaymentsVendorDetailsRows) Slice(start, end int) pagination.Rows {
	return rs[start:end]
}
func (rs RecurringPaymentsVendorDetailsRows) GetTimestamp(index int) time.Time {
	return rs[index].CreatedAt
}
func (rs RecurringPaymentsVendorDetailsRows) Size() int { return len(rs) }
