package recurringpayment

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

func (s *Service) GetRecurringPaymentsByIds(ctx context.Context, req *rpPb.GetRecurringPaymentsByIdsRequest) (*rpPb.GetRecurringPaymentsByIdsResponse, error) {
	var (
		res = &rpPb.GetRecurringPaymentsByIdsResponse{}
	)

	fetchedRecurringPayments, err := s.recurringPaymentDao.GetByIds(ctx, req.GetIds())
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("failed to get recurring payments by ids: %v", req.GetIds()), zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.Status = rpc.StatusOk()
	res.RecurringPayments = fetchedRecurringPayments
	return res, nil
}
