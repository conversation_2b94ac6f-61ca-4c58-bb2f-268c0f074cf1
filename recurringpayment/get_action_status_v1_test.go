package recurringpayment

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/recurringpayment"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	"github.com/epifi/gamma/recurringpayment/internal/actionstatusfetcher"
	actionStatusFetcherMocks "github.com/epifi/gamma/recurringpayment/internal/actionstatusfetcher/mocks"
)

func TestService_GetActionStatusV1(t *testing.T) {
	recurringPayment1 := &recurringpayment.RecurringPayment{
		Id:   "recurring-payment-id-1",
		Type: recurringpayment.RecurringPaymentType_ENACH_MANDATES,
	}
	recurringPaymentAction1 := &recurringpayment.RecurringPaymentsAction{
		Id:                 "recurring-payment-action-id-1",
		RecurringPaymentId: recurringPayment1.GetId(),
		Action:             recurringpayment.Action_CREATE,
	}
	recurringPaymentAction2 := &recurringpayment.RecurringPaymentsAction{
		Id:                 "recurring-payment-action-id-2",
		RecurringPaymentId: recurringPayment1.GetId(),
		Action:             recurringpayment.Action_EXECUTE,
	}

	type args struct {
		ctx context.Context
		req *recurringpayment.GetActionStatusV1Request
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, actionStatusFetcher *actionStatusFetcherMocks.MockIActionStatusFetcher)
		want       *recurringpayment.GetActionStatusV1Response
		wantErr    bool
	}{
		{
			name: "should return ISE rpc status code when dao call to fetch recurring payment fails",
			args: args{
				ctx: context.Background(),
				req: &recurringpayment.GetActionStatusV1Request{
					ActionType:      recurringpayment.Action_CREATE,
					ClientRequestId: "client-request-id-1",
				},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, actionStatusFetcher *actionStatusFetcherMocks.MockIActionStatusFetcher) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", true).Return(nil, errors.New("error"))
			},
			want: &recurringpayment.GetActionStatusV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error fetching recurring payment action from db using client request id"),
			},
		},
		{
			name: "should return RecordNotFound rpc status code when dao call to fetch recurring payment returns record not found error",
			args: args{
				ctx: context.Background(),
				req: &recurringpayment.GetActionStatusV1Request{
					ActionType:      recurringpayment.Action_CREATE,
					ClientRequestId: "client-request-id-1",
				},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, actionStatusFetcher *actionStatusFetcherMocks.MockIActionStatusFetcher) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", true).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &recurringpayment.GetActionStatusV1Response{
				Status: rpc.StatusRecordNotFound(),
			},
		},
		{
			name: "should return ISE rpc status code when dao call to fetch recurring payment action fails",
			args: args{
				ctx: context.Background(),
				req: &recurringpayment.GetActionStatusV1Request{
					ActionType:      recurringpayment.Action_CREATE,
					ClientRequestId: "client-request-id-1",
				},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, actionStatusFetcher *actionStatusFetcherMocks.MockIActionStatusFetcher) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", true).Return(recurringPaymentAction1, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "recurring-payment-id-1").Return(nil, errors.New("error"))
			},
			want: &recurringpayment.GetActionStatusV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error fetching recurring payment from db using id"),
			},
		},
		{
			name: "should return OK rpc status code with action status and next step deeplink details in response",
			args: args{
				ctx: context.Background(),
				req: &recurringpayment.GetActionStatusV1Request{
					ActionType:      recurringpayment.Action_CREATE,
					ClientRequestId: "client-request-id-1",
					PollAttempt:     1,
				},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, actionStatusFetcher *actionStatusFetcherMocks.MockIActionStatusFetcher) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", true).Return(recurringPaymentAction1, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "recurring-payment-id-1").Return(recurringPayment1, nil)
				actionStatusFetcher.EXPECT().GetActionStatusAndNextStepDeeplink(gomock.Any(), recurringPayment1, recurringPaymentAction1, &actionstatusfetcher.Metadata{
					PollAttempt: 1,
				}).Return(recurringpayment.ActionState_ACTION_SUCCESS, recurringpayment.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, &deeplink.Deeplink{Screen: deeplink.Screen_AUTHORIZE_RECURRING_PAYMENT_SCREEN}, nil, nil)
			},
			want: &recurringpayment.GetActionStatusV1Response{
				Status:           rpc.StatusOk(),
				ActionStatus:     recurringpayment.ActionState_ACTION_SUCCESS,
				ActionSubStatus:  recurringpayment.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED,
				NextStepDeeplink: &deeplink.Deeplink{Screen: deeplink.Screen_AUTHORIZE_RECURRING_PAYMENT_SCREEN},
			},
		},
		{
			name: "should return OK rpc status code with action status and next step deeplink and action detailed status info in response",
			args: args{
				ctx: context.Background(),
				req: &recurringpayment.GetActionStatusV1Request{
					ActionType:      recurringpayment.Action_EXECUTE,
					ClientRequestId: "client-request-id-1",
				},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, actionStatusFetcher *actionStatusFetcherMocks.MockIActionStatusFetcher) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", true).Return(recurringPaymentAction2, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "recurring-payment-id-1").Return(recurringPayment1, nil)
				actionStatusFetcher.EXPECT().GetActionStatusAndNextStepDeeplink(gomock.Any(), recurringPayment1, recurringPaymentAction2, &actionstatusfetcher.Metadata{}).Return(recurringpayment.ActionState_ACTION_FAILURE, recurringpayment.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, nil, &recurringpayment.ActionDetailedStatusInfo{
					FiStatusCode: "FI000",
				}, nil)
			},
			want: &recurringpayment.GetActionStatusV1Response{
				Status:          rpc.StatusOk(),
				ActionStatus:    recurringpayment.ActionState_ACTION_FAILURE,
				ActionSubStatus: recurringpayment.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED,
				ActionDetailedStatusInfo: &recurringpayment.ActionDetailedStatusInfo{
					FiStatusCode: "FI000",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
			mockRecurringPaymentActionsDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
			mockActionStatusFetcherFactory := actionStatusFetcherMocks.NewMockIActionStatusFetcherFactory(ctr)
			mockActionStatusFetcher := actionStatusFetcherMocks.NewMockIActionStatusFetcher(ctr)
			mockActionStatusFetcherFactory.EXPECT().GetActionStatusFetcher(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockActionStatusFetcher).AnyTimes()

			tt.setupMocks(mockRecurringPaymentDao, mockRecurringPaymentActionsDao, mockActionStatusFetcher)

			s := &Service{
				recurringPaymentDao:        mockRecurringPaymentDao,
				recurringPaymentActionsDao: mockRecurringPaymentActionsDao,
				actionStatusFetcherFactory: mockActionStatusFetcherFactory,
			}
			got, err := s.GetActionStatusV1(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetActionStatusV1() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetActionStatusV1() got = %v, want %v", got, tt.want)
			}
		})
	}
}
