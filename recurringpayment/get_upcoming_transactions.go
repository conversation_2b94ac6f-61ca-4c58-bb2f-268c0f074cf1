package recurringpayment

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	balanceEnumsPb "github.com/epifi/gamma/api/accounts/balance/enums"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/investment/mutualfund"
	beSvc "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	merchantPb "github.com/epifi/gamma/api/merchant"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	rePb "github.com/epifi/gamma/api/recurringpayment"
	rePbEnums "github.com/epifi/gamma/api/recurringpayment/enums"
	savingsPb "github.com/epifi/gamma/api/savings"
	upcomingTxnsPb "github.com/epifi/gamma/api/upcomingtransactions"
	upTxnPbEnum "github.com/epifi/gamma/api/upcomingtransactions/model"
	usStocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	"github.com/epifi/gamma/pkg/investment"
)

var upcomingTransactionSourceMap = map[rePbEnums.UpcomingTransactionSource]upTxnPbEnum.TxnSource{
	rePbEnums.UpcomingTransactionSource_DS:    upTxnPbEnum.TxnSource_TXN_SOURCE_DS,
	rePbEnums.UpcomingTransactionSource_FITTT: upTxnPbEnum.TxnSource_TXN_SOURCE_FITTT,
}

var inverseUpcomingTransactionSourceMap = map[upTxnPbEnum.TxnSource]rePbEnums.UpcomingTransactionSource{
	upTxnPbEnum.TxnSource_TXN_SOURCE_DS:    rePbEnums.UpcomingTransactionSource_DS,
	upTxnPbEnum.TxnSource_TXN_SOURCE_FITTT: rePbEnums.UpcomingTransactionSource_FITTT,
}

// GetUpcomingTransactions is used to fetch the upcoming auto-pay hub transactions to the user
// It is useful when (but not limited to):
// 1. user has to be notified to add funds when the expected debit amount is more then the current balance of the user
// NOTE: upcoming transactions are fetched directly from the UpcomingTransactions service and not from the recurring payment tables
// reason being, recurring payment is a platform service which provides service to execute an auto-pay and it orchestrates that flow.
// The flexibility of execution of an auto-pay is with the calling service hence most of the time recurring payment service does not know when exactly auto-pay will be executed
// The same can be built in recurrin payment service but it would require a fuzzy logic which is already written in UpcomingTransactions service
// todo(Harleen Singh): follow up with DS team to provide filteration on auto-pay and manual-pay
// nolint: funlen
func (s *Service) GetUpcomingTransactions(ctx context.Context, req *rePb.GetUpcomingTransactionsRequest) (*rePb.GetUpcomingTransactionsResponse, error) {
	actorId := req.GetActorId()
	fromTime := req.GetFromTime()
	if !fromTime.IsValid() {
		fromTime = timestampPb.New(time.Now())
	}
	var sources []upTxnPbEnum.TxnSource
	for _, source := range req.GetSource() {
		txnSource, ok := upcomingTransactionSourceMap[source]
		if !ok {
			logger.Error(ctx, "unknown source for upcoming transaction", zap.String("source", txnSource.String()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			return &rePb.GetUpcomingTransactionsResponse{
				Status: rpc.StatusInternalWithDebugMsg("unknown source for upcoming transaction"),
			}, nil
		}
		sources = append(sources, txnSource)
	}
	upcomingTxnsRes, upcomingTxnsErr := s.upcomingTxnClient.GetUpcomingTxnsForActor(ctx, &upcomingTxnsPb.GetUpcomingTxnsForActorRequest{
		ActorId:         actorId,
		FromTime:        fromTime,
		ToTime:          req.GetToTime(),
		TxnSources:      sources,
		AccountingEntry: req.GetAccountingEntry(),
	})

	switch {
	case upcomingTxnsErr != nil:
		logger.Error(ctx, "error while fetching the upcoming transactions for the actor", zap.String(logger.ACTOR_ID, req.GetActorId()), zap.Error(upcomingTxnsErr))
		return &rePb.GetUpcomingTransactionsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while fetching the upcoming transactions for the actor"),
		}, nil
	case upcomingTxnsRes.GetStatus().IsInternal():
		logger.Error(ctx, "status internal while fetching the upcoming transactions for the actor", zap.String(logger.ACTOR_ID, req.GetActorId()))
		return &rePb.GetUpcomingTransactionsResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to fetch upcoming transactions for the actor"),
		}, nil
	case upcomingTxnsRes.GetStatus().IsRecordNotFound() || len(upcomingTxnsRes.GetTransactions()) == 0:
		return &rePb.GetUpcomingTransactionsResponse{
			Status: rpc.StatusRecordNotFoundWithDebugMsg("no upcoming transactions for the given user"),
		}, nil
	}

	upcomingTxns := upcomingTxnsRes.GetTransactions()
	enrichedUpcomingTxns, enrichedUpcomingTxnErr := s.getEnrichedTxns(ctx, upcomingTxns)
	if enrichedUpcomingTxnErr != nil {
		logger.Error(ctx, "failed to enrich the upcoming transactions", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(enrichedUpcomingTxnErr))
		return &rePb.GetUpcomingTransactionsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while enriching the upcoming transactions"),
		}, nil
	}

	var upcomingTxnRes []*rePb.UpcomingTransaction

	for _, txn := range enrichedUpcomingTxns {
		var finalUpcomingTxn = &rePb.UpcomingTransaction{}
		finalUpcomingTxn.EntityName = txn.EntityName
		finalUpcomingTxn.EntityIconUrl = txn.EntityIconUrl
		finalUpcomingTxn.EntityId = txn.EntityId
		finalUpcomingTxn.MaxAmount = txn.MaxAmount
		finalUpcomingTxn.MinAmount = txn.MinAmount
		finalUpcomingTxn.Type = txn.EntityType
		finalUpcomingTxn.MinTime = txn.MinTime
		finalUpcomingTxn.MaxTime = txn.MaxTime
		finalUpcomingTxn.EntryType = txn.EntryType
		finalUpcomingTxn.Source = txn.Source
		upcomingTxnRes = append(upcomingTxnRes, finalUpcomingTxn)
	}

	accountDetailsRes, accountDetailsErr := s.getAmountToBeAdded(ctx, actorId, upcomingTxns)
	if accountDetailsErr != nil {
		logger.Error(ctx, "error while fetching the account details and funds to be added", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(accountDetailsErr))
		return &rePb.GetUpcomingTransactionsResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to fetch account details and add funds to be added"),
		}, nil
	}

	// accountDetailsRes nil means that user has sufficent balance in the respective accounts so that all the upcoming transactions are successful
	if accountDetailsRes == nil {
		return &rePb.GetUpcomingTransactionsResponse{
			Status:       rpc.StatusOk(),
			UpcomingTxns: upcomingTxnRes,
		}, nil
	}
	var accountDetailsWithFunds []*rePb.GetUpcomingTransactionsResponse_AccountDetails
	for _, acc := range accountDetailsRes {
		accountDetailsWithFunds = append(accountDetailsWithFunds, &rePb.GetUpcomingTransactionsResponse_AccountDetails{
			AccountId:  acc.AccountId,
			FundsToAdd: acc.FundsToAdd,
		})
	}

	return &rePb.GetUpcomingTransactionsResponse{
		Status:         rpc.StatusOk(),
		UpcomingTxns:   upcomingTxnRes,
		AccountDetails: accountDetailsWithFunds,
	}, nil
}

type enrichedUpcomingTxnDetails struct {
	EntityId      string
	EntityType    rePbEnums.UpcomingTransactionEntityType
	EntityName    string
	EntityIconUrl string
	// min amount expected to be debited/credited in txn
	MinAmount *moneyPb.Money
	// max amount expected to be debited/credited in txn
	MaxAmount *moneyPb.Money
	// min_time after which the txn is expected to happen
	MinTime *timestampPb.Timestamp
	// max_time before which the txn is expected to happen
	MaxTime *timestampPb.Timestamp
	// entry type defines the flow of money wrt the requested actor
	EntryType paymentPb.AccountingEntryType
	// source of upcoming transactions e.g. fittt, ds, etc
	Source rePbEnums.UpcomingTransactionSource
}

// getEnrichedTxns enriches the upcoming transactions with details like (but not limited to):
// 1. entity name
// 2. icon color of the entity
// nolint: funlen
func (s *Service) getEnrichedTxns(ctx context.Context, upcomingTxns []*upcomingTxnsPb.UpcomingTransaction) ([]*enrichedUpcomingTxnDetails, error) {
	var (
		merchantTxns, actorTxns, mfTxns, sdTxns, usstocksTxns                                              []*upcomingTxnsPb.UpcomingTransaction
		populatedMerchantTxns, populatedActorTxns, populatedMfTxns, populatedSdTxns, populatedUsstocksTxns []*enrichedUpcomingTxnDetails
	)
	for _, txn := range upcomingTxns {
		switch txn.GetEntityType() {
		case upTxnPbEnum.EntityType_ENTITY_TYPE_MERCHANT:
			merchantTxns = append(merchantTxns, txn)
		case upTxnPbEnum.EntityType_ENTITY_TYPE_ACTOR:
			actorTxns = append(actorTxns, txn)
		case upTxnPbEnum.EntityType_ENTITY_TYPE_MUTUAL_FUND:
			mfTxns = append(mfTxns, txn)
		case upTxnPbEnum.EntityType_ENTITY_TYPE_SD_DEPOSIT_ACCOUNT:
			sdTxns = append(sdTxns, txn)
		case upTxnPbEnum.EntityType_ENTITY_TYPE_USSTOCKS:
			usstocksTxns = append(usstocksTxns, txn)
		}
	}
	errGrp, gCtx := errgroup.WithContext(ctx)
	errGrp.Go(func() error {
		var err error
		populatedMerchantTxns, err = s.populateMerchantTxnsDetails(gCtx, merchantTxns)
		if err != nil {
			return fmt.Errorf("failed to populate merchant txn details : %w", err)
		}
		return nil
	})
	errGrp.Go(func() error {
		var err error
		populatedActorTxns, err = s.populateActorTxnsDetails(gCtx, actorTxns)
		if err != nil {
			return fmt.Errorf("failed to populate actor txn details : %w", err)
		}
		return nil
	})
	errGrp.Go(func() error {
		var err error
		populatedMfTxns, err = s.populateMfTxnDetails(gCtx, mfTxns)
		if err != nil {
			return fmt.Errorf("failed to populate mf txns details : %w", err)
		}
		return nil
	})
	errGrp.Go(func() error {
		var err error
		populatedSdTxns, err = s.populateSdTxnDetails(gCtx, sdTxns)
		if err != nil {
			return fmt.Errorf("failed to populate sd txns details : %w", err)
		}
		return nil
	})
	errGrp.Go(func() error {
		var err error
		populatedUsstocksTxns, err = s.populateUsstocksTxnDetails(gCtx, usstocksTxns)
		if err != nil {
			return fmt.Errorf("failed to populate usstocks txns details : %w", err)
		}
		return nil
	})

	if dataPopulationErr := errGrp.Wait(); dataPopulationErr != nil {
		return nil, dataPopulationErr
	}

	var enrichedTxns []*enrichedUpcomingTxnDetails
	// append the resultant transactions together to get final result
	enrichedTxns = append(enrichedTxns, populatedActorTxns...)
	enrichedTxns = append(enrichedTxns, populatedMerchantTxns...)
	enrichedTxns = append(enrichedTxns, populatedMfTxns...)
	enrichedTxns = append(enrichedTxns, populatedSdTxns...)
	enrichedTxns = append(enrichedTxns, populatedUsstocksTxns...)

	return enrichedTxns, nil
}

func (s *Service) populateActorTxnsDetails(ctx context.Context, txns []*upcomingTxnsPb.UpcomingTransaction) ([]*enrichedUpcomingTxnDetails, error) {
	var (
		actorIds []string
		resTxns  []*enrichedUpcomingTxnDetails
	)
	if len(txns) == 0 {
		return nil, nil
	}
	for _, txn := range txns {
		actorIds = append(actorIds, txn.GetDerivedEntityId())
	}
	actorsResp, actorsRespErr := s.actorClient.GetEntityDetails(ctx, &actor.GetEntityDetailsRequest{
		ActorIds: actorIds,
	})
	if rpcErr := epifigrpc.RPCError(actorsResp, actorsRespErr); rpcErr != nil {
		return nil, fmt.Errorf("failed to fetch entity details by actor ids,err: %w", rpcErr)
	}
	actorIdToDataMap := make(map[string]*actor.GetEntityDetailsResponse_EntityDetail)
	for _, data := range actorsResp.GetEntityDetails() {
		actorIdToDataMap[data.GetActorId()] = data
	}
	for _, txn := range txns {
		if data, found := actorIdToDataMap[txn.GetDerivedEntityId()]; found {
			resTxns = append(resTxns, &enrichedUpcomingTxnDetails{
				EntityId:      txn.GetDerivedEntityId(),
				EntityName:    data.GetName().ToString(),
				EntityIconUrl: data.GetProfileImageUrl(),
				MinAmount:     txn.GetMinAmount(),
				MaxAmount:     txn.GetMaxAmount(),
				EntityType:    rePbEnums.UpcomingTransactionEntityType_ACTOR,
				MinTime:       txn.GetMinTime(),
				MaxTime:       txn.GetMaxTime(),
				EntryType:     txn.GetCreditDebit(),
				Source:        inverseUpcomingTransactionSourceMap[txn.GetTxnSource()],
			})
		} else {
			return nil, fmt.Errorf("actors info using GetEntityDetails not present for id : %s", txn.GetDerivedEntityId())
		}
	}
	return resTxns, nil
}

func (s *Service) populateMerchantTxnsDetails(ctx context.Context, txns []*upcomingTxnsPb.UpcomingTransaction) ([]*enrichedUpcomingTxnDetails, error) {
	var (
		merchantIds []string
		resTxns     []*enrichedUpcomingTxnDetails
	)
	if len(txns) == 0 {
		return nil, nil
	}
	for _, txn := range txns {
		merchantIds = append(merchantIds, txn.GetDerivedEntityId())
	}
	merchantsResp, merchantRespErr := s.merchantClient.GetMerchants(ctx, &merchantPb.GetMerchantsRequest{
		Identifier: &merchantPb.GetMerchantsRequest_MerchantIdentifier_{
			MerchantIdentifier: &merchantPb.GetMerchantsRequest_MerchantIdentifier{
				MerchantIds: merchantIds,
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(merchantsResp, merchantRespErr); rpcErr != nil {
		return nil, fmt.Errorf("failed to fetch merchants from ids,err: %w", rpcErr)
	}
	merchantIdToDataMap := make(map[string]*merchantPb.Merchant)
	for _, data := range merchantsResp.GetMerchants() {
		merchantIdToDataMap[data.GetId()] = data
	}
	for _, txn := range txns {
		if data, found := merchantIdToDataMap[txn.GetDerivedEntityId()]; found {
			resTxns = append(resTxns, &enrichedUpcomingTxnDetails{
				EntityId:      txn.GetDerivedEntityId(),
				EntityName:    data.GetName(),
				EntityIconUrl: data.GetLogoUrl(),
				MinAmount:     txn.GetMinAmount(),
				MaxAmount:     txn.GetMaxAmount(),
				EntityType:    rePbEnums.UpcomingTransactionEntityType_MERCHANT,
				MinTime:       txn.GetMinTime(),
				MaxTime:       txn.GetMaxTime(),
				EntryType:     txn.GetCreditDebit(),
				Source:        inverseUpcomingTransactionSourceMap[txn.GetTxnSource()],
			})
		} else {
			return nil, fmt.Errorf("merchant info using GetMerchants not present for id : %s", txn.GetDerivedEntityId())
		}
	}
	return resTxns, nil
}

func (s *Service) populateMfTxnDetails(ctx context.Context, txns []*upcomingTxnsPb.UpcomingTransaction) ([]*enrichedUpcomingTxnDetails, error) {

	if len(txns) == 0 {
		return nil, nil
	}
	var (
		mfIds   []string
		resTxns []*enrichedUpcomingTxnDetails
	)
	for _, txn := range txns {
		mfIds = append(mfIds, txn.GetDerivedEntityId())
	}
	mfIdToMfMap, err := s.getMfDetailsByIds(ctx, mfIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get mf details for mutual funds : %w", err)
	}

	for _, txn := range txns {
		mf, found := mfIdToMfMap[txn.GetDerivedEntityId()]
		if !found {
			return nil, fmt.Errorf("mf details not found for mf_id : %s", txn.GetDerivedEntityId())
		}
		iconUrl, found := investment.IconsForAmc[mf.GetAmc()]
		if !found {
			return nil, fmt.Errorf("failed to get icon url for amc : %s", mf.GetAmc().String())
		}
		resTxns = append(resTxns, &enrichedUpcomingTxnDetails{
			EntityId:      txn.GetDerivedEntityId(),
			EntityName:    mf.GetNameData().GetDisplayName(),
			EntityIconUrl: iconUrl,
			MinAmount:     txn.GetMinAmount(),
			MaxAmount:     txn.GetMaxAmount(),
			EntityType:    rePbEnums.UpcomingTransactionEntityType_MUTUAL_FUND,
			MinTime:       txn.GetMinTime(),
			MaxTime:       txn.GetMaxTime(),
			EntryType:     txn.GetCreditDebit(),
			Source:        inverseUpcomingTransactionSourceMap[txn.GetTxnSource()],
		})
	}
	return resTxns, nil
}

func (s *Service) getMfDetailsByIds(ctx context.Context, mfIds []string) (map[string]*mutualfund.MutualFund, error) {
	if len(mfIds) == 0 {
		return map[string]*mutualfund.MutualFund{}, nil
	}

	mfIds = lo.Uniq[string](mfIds)
	res, err := s.mfCatalogManagerClient.GetMutualFunds(ctx, &beSvc.GetMutualFundsRequest{
		FundIdentifier: mutualfund.MutualFundIdentifier_MUTUAL_FUND_IDENTIFIER_ID,
		Ids:            mfIds,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		return map[string]*mutualfund.MutualFund{}, fmt.Errorf("failed to fetch mutual fund details for subscription : %w", rpcErr)
	}
	mfIdToMfMap := make(map[string]*mutualfund.MutualFund)
	for _, mf := range res.GetMutualFunds() {
		mfIdToMfMap[mf.GetId()] = mf
	}
	return mfIdToMfMap, nil
}

func (s *Service) populateSdTxnDetails(ctx context.Context, txns []*upcomingTxnsPb.UpcomingTransaction) ([]*enrichedUpcomingTxnDetails, error) {
	if len(txns) == 0 {
		return nil, nil
	}
	var (
		sdIds   []string
		resTxns []*enrichedUpcomingTxnDetails
	)
	for _, txn := range txns {
		sdIds = append(sdIds, txn.GetDerivedEntityId())
	}
	sfIdToSdMap, err := s.getSdDetailsByIds(ctx, sdIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get sd details for sd accounts : %w", err)
	}

	for _, txn := range txns {
		sd, found := sfIdToSdMap[txn.GetDerivedEntityId()]
		if !found {
			return nil, fmt.Errorf("sd details not found for sd_id : %s", txn.GetDerivedEntityId())
		}
		iconUrl := sd.GetDepositIcon().GetImageUrl()
		resTxns = append(resTxns, &enrichedUpcomingTxnDetails{
			EntityId:      txn.GetDerivedEntityId(),
			EntityName:    sd.GetName(),
			EntityIconUrl: iconUrl,
			MinAmount:     txn.GetMinAmount(),
			MaxAmount:     txn.GetMaxAmount(),
			EntityType:    rePbEnums.UpcomingTransactionEntityType_SD_DEPOSIT_ACCOUNT,
			MinTime:       txn.GetMinTime(),
			MaxTime:       txn.GetMaxTime(),
			EntryType:     txn.GetCreditDebit(),
			Source:        inverseUpcomingTransactionSourceMap[txn.GetTxnSource()],
		})
	}
	return resTxns, nil
}

func (s *Service) getSdDetailsByIds(ctx context.Context, sdIds []string) (map[string]*deposit.DepositAccount, error) {
	if len(sdIds) == 0 {
		return map[string]*deposit.DepositAccount{}, nil
	}

	sdIds = lo.Uniq[string](sdIds)

	ch := make(chan *deposit.DepositAccount, len(sdIds))
	errGrp, gctx := errgroup.WithContext(ctx)
	errGrp.SetLimit(5)
	for _, id := range sdIds {
		sdId := id
		errGrp.Go(func() error {
			sdRes, err := s.depositClient.GetById(gctx, &deposit.GetByIdRequest{
				Id: sdId,
			})
			if rpcErr := epifigrpc.RPCError(sdRes, err); rpcErr != nil {
				return fmt.Errorf("failed to get sd details for acc %s : %w", sdId, rpcErr)
			}
			ch <- sdRes.GetAccount()
			return nil
		})
	}
	if gErr := errGrp.Wait(); gErr != nil {
		close(ch)
		return map[string]*deposit.DepositAccount{}, gErr
	}
	close(ch)
	sdIdToSdMap := make(map[string]*deposit.DepositAccount)
	for {
		sdAcc, found := <-ch
		if !found {
			break
		}
		sdIdToSdMap[sdAcc.GetId()] = sdAcc
	}
	return sdIdToSdMap, nil
}

func (s *Service) populateUsstocksTxnDetails(ctx context.Context, txns []*upcomingTxnsPb.UpcomingTransaction) ([]*enrichedUpcomingTxnDetails, error) {
	if len(txns) == 0 {
		return nil, nil
	}
	var (
		stockIds []string
		resTxns  []*enrichedUpcomingTxnDetails
	)
	stockIds = lo.Map(txns, func(txn *upcomingTxnsPb.UpcomingTransaction, _ int) string {
		return txn.GetDerivedEntityId()
	})
	stockIdToStockMap, err := s.getUsstockDetailsByIds(ctx, stockIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get stock details for usstock : %w", err)
	}

	for _, txn := range txns {
		stock, found := stockIdToStockMap[txn.GetDerivedEntityId()]
		if !found {
			return nil, fmt.Errorf("stock details not found for stock_id : %s", txn.GetDerivedEntityId())
		}
		resTxns = append(resTxns, &enrichedUpcomingTxnDetails{
			EntityId:      txn.GetDerivedEntityId(),
			EntityName:    stock.GetCompanyInfo().GetCompanyName().GetShortName(),
			EntityIconUrl: stock.GetCompanyInfo().GetLogoUrl(),
			MinAmount:     txn.GetMinAmount(),
			MaxAmount:     txn.GetMaxAmount(),
			EntityType:    rePbEnums.UpcomingTransactionEntityType_USSTOCKS,
			MinTime:       txn.GetMinTime(),
			MaxTime:       txn.GetMaxTime(),
			EntryType:     txn.GetCreditDebit(),
			Source:        inverseUpcomingTransactionSourceMap[txn.GetTxnSource()],
		})
	}
	return resTxns, nil
}

func (s *Service) getUsstockDetailsByIds(ctx context.Context, stockIds []string) (map[string]*usStocksCatalogPb.Stock, error) {
	if len(stockIds) == 0 {
		return map[string]*usStocksCatalogPb.Stock{}, nil
	}

	stockIds = lo.Uniq(stockIds)
	res, err := s.usStocksCatalogClient.GetStocks(ctx, &usStocksCatalogPb.GetStocksRequest{
		Identifiers: &usStocksCatalogPb.GetStocksRequest_StockIds{
			StockIds: &usStocksCatalogPb.RepeatedStrings{
				Ids: stockIds,
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		return map[string]*usStocksCatalogPb.Stock{}, fmt.Errorf("failed to fetch stock details for subscription : %w", rpcErr)
	}
	return res.GetStocks(), nil
}

// accountDetails represents funds to be added to the given account to make sure all the debit upcoming transactions are successful
type accountDetails struct {
	AccountId  string
	FundsToAdd *moneyPb.Money
}

// getAmountToBeAdded is helpful to fetch the minimum amount that has to be added in the user's account to fulfil all the upcoming transactions
// NOTE: as of now it only returns data for SAVINGS ACCOUNT
func (s *Service) getAmountToBeAdded(ctx context.Context, actorId string, upcomingTxns []*upcomingTxnsPb.UpcomingTransaction) ([]*accountDetails, error) {
	var (
		totalDebitMoney = &moneyPb.Money{
			CurrencyCode: moneyPkg.RupeeCurrencyCode,
		}
		totalDebitMoneyErr error
		accountDetailsList []*accountDetails
	)
	for _, txn := range upcomingTxns {
		if txn.GetEntityType() == upTxnPbEnum.EntityType_ENTITY_TYPE_UNSPECIFIED {
			continue
		}
		// todo(Harleen Singh): evaluate using average of min and max amount
		txnAmount := txn.GetMinAmount()
		totalDebitMoney, totalDebitMoneyErr = moneyPkg.Sum(totalDebitMoney, txnAmount)
		if totalDebitMoneyErr != nil {
			return nil, fmt.Errorf("failed to add the total debit money and the upcoming txn amount,err: %w", totalDebitMoneyErr)
		}
	}

	savingsAccountEssentialsRes, savingsAccountEssentialsResErr := s.savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
		Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
				ActorId:     actorId,
				PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})

	if err := epifigrpc.RPCError(savingsAccountEssentialsRes, savingsAccountEssentialsResErr); err != nil {
		return nil, fmt.Errorf("failed to fetch the savings accounts essential,err: %w", err)
	}

	currentUserBalanceRes, currentUserBalanseResErr := s.paySavingsBalanceClient.GetAccountBalance(ctx, &accountBalancePb.GetAccountBalanceRequest{
		Identifier: &accountBalancePb.GetAccountBalanceRequest_Id{
			Id: savingsAccountEssentialsRes.GetAccount().GetId(),
		},
		ActorId:       actorId,
		DataFreshness: balanceEnumsPb.DataFreshness_LAST_KNOWN_BALANCE_FROM_DB,
	})

	if err := epifigrpc.RPCError(currentUserBalanceRes, currentUserBalanseResErr); err != nil {
		return nil, fmt.Errorf("failed to fetch the account balance,err: %w", err)
	}
	var savingsAccount = &accountDetails{}
	savingsAccount.AccountId = savingsAccountEssentialsRes.GetAccount().GetId()
	accountDetailsList = append(accountDetailsList, savingsAccount)
	// compare the user's balance with the total debit amount
	amountDiff, amountDiffErr := moneyPkg.CompareV2(totalDebitMoney, currentUserBalanceRes.GetAvailableBalance())
	if amountDiffErr != nil {
		return nil, fmt.Errorf("failed to compare total debit money of upcoming transactions and user's current balance,err: %w", amountDiffErr)
	}
	if amountDiff != 1 {
		// return nil object if current balance >= totalDebitMoney
		return nil, nil
	}
	addFundsAmount, addFundsAmountErr := moneyPkg.Subtract(totalDebitMoney, currentUserBalanceRes.GetAvailableBalance())
	if addFundsAmountErr != nil {
		return nil, fmt.Errorf("failed to substract the total debit money of upcoming transactions and user's current balance,err: %w", addFundsAmountErr)
	}
	savingsAccount.FundsToAdd = addFundsAmount
	return accountDetailsList, nil
}
