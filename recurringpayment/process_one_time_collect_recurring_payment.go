package recurringpayment

import (
	"context"
	"errors"

	domainPb "github.com/epifi/gamma/api/order/domain"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	pb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
)

// nolint: funlen, dupl
func (s *Service) ProcessOneTimeCollectRecurringPayment(ctx context.Context, req *domainPb.ProcessPaymentRequest) (*domainPb.ProcessPaymentResponse, error) {
	var (
		txn *paymentPb.Transaction
	)
	res := &domainPb.ProcessPaymentResponse{}
	responseHeader := &domainPb.DomainResponseHeader{}
	res.ResponseHeader = responseHeader

	defer func() {
		if req.GetRequestHeader().GetIsLastAttempt() {
			s.updateTxnStateToManualInterventionOnLastAttempt(ctx, txn, responseHeader.GetStatus())
		}
	}()

	recurringPaymentExecutionInfo := pb.RecurringPaymentExecutionInfo{}
	unmarshalOptions := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	if unmarshalErr := unmarshalOptions.Unmarshal(req.GetPayload(), &recurringPaymentExecutionInfo); unmarshalErr != nil {
		logger.Error(ctx, "failed to unmarshal payload for recurring payment execution", zap.Error(unmarshalErr))
		responseHeader.Status = domainPb.DomainProcessingStatus_PERMANENT_FAILURE
		return res, nil
	}
	recurringPaymentId := recurringPaymentExecutionInfo.GetRecurringPaymentId()
	recurringPayment, err := s.recurringPaymentDao.GetById(ctx, recurringPaymentId)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "recurring payment record not found", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
		responseHeader.Status = domainPb.DomainProcessingStatus_PERMANENT_FAILURE
		return res, nil
	case err != nil:
		logger.Error(ctx, "error while fetching recurring payment", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId),
			zap.Error(err))
		responseHeader.Status = domainPb.DomainProcessingStatus_TRANSIENT_FAILURE
		return res, nil
	default:
		logger.Debug(ctx, "recurring payment entry fetched successfully")
	}

	txns, order, err := s.getTxnsOfOrder(ctx, req.GetRequestHeader().GetClientRequestId())
	if err != nil {
		logger.Error(ctx, "unable to fetch transaction",
			zap.String(logger.ORDER_ID, req.GetRequestHeader().GetClientRequestId()), zap.Error(err))
		responseHeader.Status = domainPb.GetStatusFrom(err)
		return res, nil
	}

	// We will only have a single successful transaction for each order
	// In case transient failure, service will re-initiate execution request which will create a new transaction.
	// Since order with transactions return transactions sorted by created_at, to get latest transaction
	// taking last transaction.
	lastElementIndex := len(txns) - 1
	if lastElementIndex < 0 {
		logger.Error(ctx, "no transaction found against order", zap.String(logger.ORDER_ID, order.GetId()))
		res.ResponseHeader.Status = domainPb.DomainProcessingStatus_PERMANENT_FAILURE
		return res, nil
	}
	txn = txns[lastElementIndex]

	switch {
	// transaction failed
	case txn.GetStatus() == paymentPb.TransactionStatus_FAILED:
		_, err = s.getAndUpdateRecurringPaymentActionState(ctx, order.GetClientReqId(), pb.ActionState_ACTION_FAILURE)
		if err != nil {
			logger.Error(ctx, "failed to update recurring payment execute action state to failure", zap.Error(err),
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId), zap.String(logger.ORDER_ID, order.GetId()))
			responseHeader.Status = domainPb.GetStatusFrom(err)
			return res, nil
		}
		logger.Debug(ctx, "recurring payment transaction failed", zap.String(logger.TXN_ID, txn.GetId()),
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
		responseHeader.Status = domainPb.DomainProcessingStatus_PERMANENT_FAILURE
		return res, nil
	// transaction success
	case txn.GetStatus() == paymentPb.TransactionStatus_SUCCESS:
		_, err = s.getAndUpdateRecurringPaymentActionState(ctx, order.GetClientReqId(), pb.ActionState_ACTION_SUCCESS)
		if err != nil {
			logger.Error(ctx, "failed to update recurring payment execute action state to failure", zap.Error(err),
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId), zap.String(logger.ORDER_ID, order.GetId()))
			responseHeader.Status = domainPb.GetStatusFrom(err)
			return res, nil
		}
		logger.Debug(ctx, "recurring payment executed successfully", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId),
			zap.String(logger.TXN_ID, txn.GetId()))
		responseHeader.Status = domainPb.DomainProcessingStatus_SUCCESS
		return res, nil
	// in case transaction is not in terminal state, update the transactions based on the enquiry response from the
	// vendor
	case txn.GetStatus() == paymentPb.TransactionStatus_INITIATED || txn.GetStatus() == paymentPb.TransactionStatus_IN_PROGRESS ||
		txn.GetStatus() == paymentPb.TransactionStatus_UNKNOWN:
		err = s.fetchPaymentStatusAndUpdateDB(ctx, txn, recurringPayment)
		switch {
		case errors.Is(err, errTxnFailure):
			responseHeader.Status = domainPb.DomainProcessingStatus_PERMANENT_FAILURE
		case err != nil:
			logger.Error(ctx, "failed to fetch transaction status", zap.String(logger.TXN_ID, txn.GetId()),
				zap.String(logger.TXN_STATUS, txn.Status.String()),
				zap.Error(err))
			responseHeader.Status = domainPb.GetStatusFrom(err)
		default:
			responseHeader.Status = domainPb.DomainProcessingStatus_SUCCESS
		}
		return res, nil
	default:
		logger.Error(ctx, "transaction found in unexpected state", zap.String(logger.TXN_ID, txn.GetId()),
			zap.String(logger.TXN_STATUS, txn.GetStatus().String()))
		responseHeader.Status = domainPb.DomainProcessingStatus_PERMANENT_FAILURE
		return res, nil
	}
}
