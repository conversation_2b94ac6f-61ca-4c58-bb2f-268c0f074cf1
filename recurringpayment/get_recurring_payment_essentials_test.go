package recurringpayment

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/savings"
	mockSavings "github.com/epifi/gamma/api/savings/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	enachVgPb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
	enachVgPbEnums "github.com/epifi/gamma/api/vendorgateway/openbanking/enach/enums"
	mocksEnachVg "github.com/epifi/gamma/api/vendorgateway/openbanking/enach/mocks"
)

func TestService_GetRecurringPaymentEssentials(t *testing.T) {
	var (
		defaultUmrn                       = "default-umrn"
		defaultActorId                    = "default-actor-id"
		defaultAccountNumber              = "default-account-number"
		defaultGetSavingsEssentialRequest = &savings.GetSavingsAccountEssentialsRequest{
			Filter: &savings.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
				ActorIdBankFilter: &savings.ActorIdBankFilter{
					ActorId:     defaultActorId,
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
		}
		defaultGetSavingsEssentialsResponse = &savings.GetSavingsAccountEssentialsResponse{
			Status: rpc.StatusOk(),
			Account: &savings.SavingsAccountEssentials{
				AccountNo: defaultAccountNumber,
			},
		}
		defaultVgHeader = &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		}
		defaultGetRecurringPaymentEssentialsRequest = &rpPb.GetRecurringPaymentEssentialsRequest{
			ActorId: defaultActorId,
			Vendor:  commonvgpb.Vendor_FEDERAL_BANK,
			Identifier: &rpPb.GetRecurringPaymentEssentialsRequest_EnachIdentifier{
				EnachIdentifier: &rpPb.GetRecurringPaymentEssentialsRequest_Enach{
					Umrn: defaultUmrn,
				},
			},
		}
	)
	type args struct {
		ctx context.Context
		req *rpPb.GetRecurringPaymentEssentialsRequest
	}
	tests := []struct {
		name       string
		args       args
		want       *rpPb.GetRecurringPaymentEssentialsResponse
		setupMocks func(savingsClient *mockSavings.MockSavingsClient, enachVgClient *mocksEnachVg.MockEnachClient)
		wantErr    bool
	}{
		{
			name: "should return ISE (failed to fetch savings account essentials)",
			args: args{
				ctx: context.Background(),
				req: defaultGetRecurringPaymentEssentialsRequest,
			},
			setupMocks: func(savingsClient *mockSavings.MockSavingsClient, enachVgClient *mocksEnachVg.MockEnachClient) {
				savingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsEssentialRequest).Return(&savings.GetSavingsAccountEssentialsResponse{Status: rpc.StatusInternal()}, nil)

			},
			want: &rpPb.GetRecurringPaymentEssentialsResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "should return ISE (failed to fetch data from vendor)",
			args: args{
				ctx: context.Background(),
				req: defaultGetRecurringPaymentEssentialsRequest,
			},
			setupMocks: func(savingsClient *mockSavings.MockSavingsClient, enachVgClient *mocksEnachVg.MockEnachClient) {
				savingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsEssentialRequest).Return(defaultGetSavingsEssentialsResponse, nil)
				enachVgClient.EXPECT().ListEnachMandate(context.Background(), &enachVgPb.ListEnachMandateRequest{
					Header:        defaultVgHeader,
					AccountNumber: defaultAccountNumber,
				}).Return(&enachVgPb.ListEnachMandateResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: &rpPb.GetRecurringPaymentEssentialsResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "should return record not found (no active mandates fetched from the vendor)",
			args: args{
				ctx: context.Background(),
				req: defaultGetRecurringPaymentEssentialsRequest,
			},
			setupMocks: func(savingsClient *mockSavings.MockSavingsClient, enachVgClient *mocksEnachVg.MockEnachClient) {
				savingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsEssentialRequest).Return(defaultGetSavingsEssentialsResponse, nil)
				enachVgClient.EXPECT().ListEnachMandate(context.Background(), &enachVgPb.ListEnachMandateRequest{
					Header:        defaultVgHeader,
					AccountNumber: defaultAccountNumber,
				}).Return(&enachVgPb.ListEnachMandateResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			want: &rpPb.GetRecurringPaymentEssentialsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
		{
			name: "should return record not found (no data found for given UMRN)",
			args: args{
				ctx: context.Background(),
				req: defaultGetRecurringPaymentEssentialsRequest,
			},
			setupMocks: func(savingsClient *mockSavings.MockSavingsClient, enachVgClient *mocksEnachVg.MockEnachClient) {
				savingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsEssentialRequest).Return(defaultGetSavingsEssentialsResponse, nil)
				enachVgClient.EXPECT().ListEnachMandate(context.Background(), &enachVgPb.ListEnachMandateRequest{
					Header:        defaultVgHeader,
					AccountNumber: defaultAccountNumber,
				}).Return(&enachVgPb.ListEnachMandateResponse{
					Status: rpc.StatusOk(),
					EnachMandates: []*enachVgPb.EnachMandate{
						{
							Umrn:             "different-umrn",
							MandateFrequency: enachVgPbEnums.Frequency_FREQUENCY_MONTHLY,
							OrgName:          "Groww",
						},
					},
				}, nil)
			},
			want: &rpPb.GetRecurringPaymentEssentialsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
		{
			name: "should successfully fetch recurring payment essentials for the given umrn",
			args: args{
				ctx: context.Background(),
				req: defaultGetRecurringPaymentEssentialsRequest,
			},
			setupMocks: func(savingsClient *mockSavings.MockSavingsClient, enachVgClient *mocksEnachVg.MockEnachClient) {
				savingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsEssentialRequest).Return(defaultGetSavingsEssentialsResponse, nil)
				enachVgClient.EXPECT().ListEnachMandate(context.Background(), &enachVgPb.ListEnachMandateRequest{
					Header:        defaultVgHeader,
					AccountNumber: defaultAccountNumber,
				}).Return(&enachVgPb.ListEnachMandateResponse{
					Status: rpc.StatusOk(),
					EnachMandates: []*enachVgPb.EnachMandate{
						{
							Umrn:             defaultUmrn,
							MandateFrequency: enachVgPbEnums.Frequency_FREQUENCY_MONTHLY,
							OrgName:          "Groww",
						},
					},
				}, nil)
			},
			want: &rpPb.GetRecurringPaymentEssentialsResponse{
				Status: rpc.StatusOk(),
				RecurringPaymentEssentials: &rpPb.RecurringPaymentEssentials{
					Type:                     rpPb.RecurringPaymentType_ENACH_MANDATES,
					PreferredPaymentProtocol: paymentPb.PaymentProtocol_ENACH,
					State:                    rpPb.RecurringPaymentState_ACTIVATED,
					Interval:                 &types.Interval{},
					PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
					Provenance:               rpPb.RecurrencePaymentProvenance_EXTERNAL,
					RecurrenceRule: &rpPb.RecurrenceRule{
						AllowedFrequency: rpPb.AllowedFrequency_MONTHLY,
					},
				},
				RecurringPaymentTypeSpecificData: &rpPb.GetRecurringPaymentEssentialsResponse_EnachData_{
					EnachData: &rpPb.GetRecurringPaymentEssentialsResponse_EnachData{
						Umrn:    defaultUmrn,
						OrgName: "Groww",
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockSavingsClient := mockSavings.NewMockSavingsClient(ctr)
			mockEnachVgClient := mocksEnachVg.NewMockEnachClient(ctr)
			// setup mocks
			if tt.setupMocks != nil {
				tt.setupMocks(mockSavingsClient, mockEnachVgClient)
			}
			s := &Service{
				savingsClient: mockSavingsClient,
				enachVgClient: mockEnachVgClient,
			}
			got, err := s.GetRecurringPaymentEssentials(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRecurringPaymentEssentials() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRecurringPaymentEssentials() got = %v, want %v", got, tt.want)
			}
		})
	}
}
