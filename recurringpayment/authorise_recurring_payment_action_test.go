package recurringpayment

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/money"

	actorPb "github.com/epifi/gamma/api/actor"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	orderPb "github.com/epifi/gamma/api/order"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	pb "github.com/epifi/gamma/api/recurringpayment"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/upi"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
	"github.com/epifi/gamma/api/upi/mandate/mocks"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	internalMocks "github.com/epifi/gamma/recurringpayment/internal/mocks"
	mocks2 "github.com/epifi/gamma/recurringpayment/internal/mocks"
)

func TestService_AuthorisePauseOrUnpause(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockOrderClient := orderMocks.NewMockOrderServiceClient(ctr)
	mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
	mockRecurringPaymentVendorDetailsDao := daoMocks.NewMockRecurringPaymentsVendorDetailsDao(ctr)
	mockRecurringPaymentsActionDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
	mockRecurringPaymentProcessor := mocks2.NewMockRecurringPaymentProcessor(ctr)
	mockUpiMandateClient := mocks.NewMockMandateServiceClient(ctr)
	mockActorClient := actorMocks.NewMockActorClient(ctr)
	mockCelestialProcessor := internalMocks.NewMockCelestialProcessor(ctr)

	svc := NewService(mockRecurringPaymentDao, mockOrderClient, nil, mockRecurringPaymentsActionDao, nil, mockActorClient, nil, nil, nil, conf, mockRecurringPaymentProcessor, mockUpiMandateClient, nil, nil, nil, nil, nil, nil, dynamicConf.FeatureFlags(), mockCelestialProcessor, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, mockRecurringPaymentVendorDetailsDao, nil, nil, nil, nil)

	startDate := timestampPb.New(time.Now())
	endDate := timestampPb.New(time.Now().Add(48 * time.Hour))

	rp1 := &pb.RecurringPayment{
		Id:          "id-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_EXACT,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
			RuleType:         pb.RecurrenceRule_ON,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_UPI,
		State:                    pb.RecurringPaymentState_UNPAUSE_QUEUED,
	}

	tests := []struct {
		name           string
		req            *pb.AuthoriseRecurringPaymentActionRequest
		setupMockCalls func()
		want           *pb.AuthoriseRecurringPaymentActionResponse
		wantErr        bool
	}{
		{
			name: "authorised successfully",
			req: &pb.AuthoriseRecurringPaymentActionRequest{
				RecurringPaymentId: "rp-1",
				Credential: &pb.Credential{Params: &pb.Credential_MandateHeader{MandateHeader: &pb.MandateAuthHeader{
					Device: &upi.Device{
						Id: "id-1",
					},
					NpciCredBlock: &upi.CredBlock{
						Type: upi.CredBlock_UPI_MANDATE,
					},
				}}},
				ClientRequestId: "client-req-1",
				CurrentActorId:  "actor-1",
				Action:          pb.Action_UNPAUSE,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentPauseUnpauseViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(rp1, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_UNPAUSE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "vendor-req-1",
				}, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type: types.Actor_USER,
					},
				}, nil)
				mockOrderClient.EXPECT().InitiateOrderProcessing(gomock.Any(), &orderPb.InitiateOrderProcessingRequest{Identifier: &orderPb.InitiateOrderProcessingRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.InitiateOrderProcessingResponse{Status: rpc.StatusOk()}, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), rp1, nil,
					rp1.GetState(), pb.RecurringPaymentState_UNPAUSE_AUTHORISED).Return(nil)
				mockUpiMandateClient.EXPECT().AuthoriseMandateAction(gomock.Any(), &upiMandatePb.AuthoriseMandateActionRequest{
					AuthHeader: &upiMandatePb.AuthoriseMandateActionRequest_AuthHeader{
						Device: &upi.Device{
							Id: "id-1",
						},
						NpciCredBlock: &upi.CredBlock{
							Type: upi.CredBlock_UPI_MANDATE,
						},
					},
					ReqId:          "vendor-req-1",
					CurrentActorId: "actor-1",
					FromActorId:    "actor-1",
					ToActorId:      "actor-2",
					FromPiId:       "pi-1",
					ToPiId:         "pi-2",
					MandateValidity: &upiMandatePb.Validity{
						Start: datetime.TimeToDateInLoc(startDate.AsTime(), datetime.IST),
						End:   datetime.TimeToDateInLoc(endDate.AsTime(), datetime.IST),
					},
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					Amount:      money.ZeroINR().Pb,
					AmountRule:  upiMandatePb.AmountRule_EXACT,
					Recurrence: &upiMandatePb.Recurrence{
						RecurrencePattern: upiMandatePb.RecurrencePattern_AS_PRESENTED,
					},
				}).Return(&upiMandatePb.AuthoriseMandateActionResponse{Status: rpc.StatusOk()}, nil)
			},
			want:    &pb.AuthoriseRecurringPaymentActionResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
		{
			name: "failed to fetch recurring payment",
			req: &pb.AuthoriseRecurringPaymentActionRequest{
				RecurringPaymentId: "rp-1",
				Credential: &pb.Credential{Params: &pb.Credential_MandateHeader{MandateHeader: &pb.MandateAuthHeader{
					Device: &upi.Device{
						Id: "id-1",
					},
					NpciCredBlock: &upi.CredBlock{
						Type: upi.CredBlock_UPI_MANDATE,
					},
				}}},
				ClientRequestId: "client-req-1",
				CurrentActorId:  "actor-1",
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(nil, fmt.Errorf("error"))
			},
			want:    &pb.AuthoriseRecurringPaymentActionResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "actor id validation failed",
			req: &pb.AuthoriseRecurringPaymentActionRequest{
				RecurringPaymentId: "rp-1",
				Credential: &pb.Credential{Params: &pb.Credential_MandateHeader{MandateHeader: &pb.MandateAuthHeader{
					Device: &upi.Device{
						Id: "id-1",
					},
					NpciCredBlock: &upi.CredBlock{
						Type: upi.CredBlock_UPI_MANDATE,
					},
				}}},
				ClientRequestId: "client-req-1",
				CurrentActorId:  "actor-3",
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(rp1, nil)
			},
			want:    &pb.AuthoriseRecurringPaymentActionResponse{Status: rpc.StatusPermissionDenied()},
			wantErr: false,
		},
		{
			name: "action not found",
			req: &pb.AuthoriseRecurringPaymentActionRequest{
				RecurringPaymentId: "rp-1",
				Credential: &pb.Credential{Params: &pb.Credential_MandateHeader{MandateHeader: &pb.MandateAuthHeader{
					Device: &upi.Device{
						Id: "id-1",
					},
					NpciCredBlock: &upi.CredBlock{
						Type: upi.CredBlock_UPI_MANDATE,
					},
				}}},
				ClientRequestId: "client-req-1",
				CurrentActorId:  "actor-1",
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(rp1, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:    &pb.AuthoriseRecurringPaymentActionResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "order not found",
			req: &pb.AuthoriseRecurringPaymentActionRequest{
				RecurringPaymentId: "rp-1",
				Credential: &pb.Credential{Params: &pb.Credential_MandateHeader{MandateHeader: &pb.MandateAuthHeader{
					Device: &upi.Device{
						Id: "id-1",
					},
					NpciCredBlock: &upi.CredBlock{
						Type: upi.CredBlock_UPI_MANDATE,
					},
				}}},
				ClientRequestId: "client-req-1",
				CurrentActorId:  "actor-1",
				Action:          pb.Action_UNPAUSE,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentPauseUnpauseViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(rp1, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_UNPAUSE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "vendor-req-1",
				}, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			want:    &pb.AuthoriseRecurringPaymentActionResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "recurring payment unpause action already authorised",
			req: &pb.AuthoriseRecurringPaymentActionRequest{
				RecurringPaymentId: "rp-1",
				Credential: &pb.Credential{Params: &pb.Credential_MandateHeader{MandateHeader: &pb.MandateAuthHeader{
					Device: &upi.Device{
						Id: "id-1",
					},
					NpciCredBlock: &upi.CredBlock{
						Type: upi.CredBlock_UPI_MANDATE,
					},
				}}},
				ClientRequestId: "client-req-1",
				CurrentActorId:  "actor-1",
				Action:          pb.Action_UNPAUSE,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentPauseUnpauseViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(rp1, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_UNPAUSE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "vendor-req-1",
				}, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want:    &pb.AuthoriseRecurringPaymentActionResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.AuthoriseRecurringPaymentAction(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AuthorisePauseOrUnpause() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AuthorisePauseOrUnpause() got = %v, want %v", got, tt.want)
			}
		})
	}
}
