package recurringpayment

import (
	"context"
	"errors"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	pb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/pkg/recurringpayment"
)

// UpdateRecurringPaymentActions this rpc updates the recurring payment action with the latest action detailed status
// Note: Currently only updating action detailed status is supported.
func (s *Service) UpdateRecurringPaymentActions(ctx context.Context, req *pb.UpdateRecurringPaymentActionsRequest) (*pb.UpdateRecurringPaymentActionsResponse, error) {

	var res = &pb.UpdateRecurringPaymentActionsResponse{}

	recurringPaymentAction, err := s.recurringPaymentActionsDao.GetByVendorRequestId(ctx, req.GetReqId())
	if err != nil {
		logger.Error(ctx, "failed to fetch action ", zap.String(logger.REQUEST_ID, req.GetReqId()), zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) || errors.Is(err, gorm.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	recurringpayment.AddActionDetailedStatus(recurringPaymentAction, req.GetActionDetailedStatus())

	err = s.recurringPaymentActionsDao.UpdateAndChangeStatus(ctx, recurringPaymentAction,
		[]pb.RecurringPaymentActionFieldMask{pb.RecurringPaymentActionFieldMask_ACTION_DETAILED_STATUS},
		recurringPaymentAction.GetState(), recurringPaymentAction.GetState())
	if err != nil {
		logger.Error(ctx, "failed to update action ", zap.String(logger.REQUEST_ID, req.GetReqId()), zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) || errors.Is(err, gorm.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.Status = rpc.StatusOk()
	return res, nil
}
