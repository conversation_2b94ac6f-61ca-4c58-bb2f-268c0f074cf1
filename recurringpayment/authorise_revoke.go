package recurringpayment

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	pb "github.com/epifi/gamma/api/recurringpayment"
	payloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	"github.com/epifi/gamma/pkg/recurringpayment"
)

// AuthoriseRecurringPaymentRevoke - authorises revoke request for recurring payment
// We are using the client as workflowPb.Client_RECURRING_PAYMENT across recurring payment workflows to prevent exposure of backend information on Client side
// nolint: funlen, dupl
func (s *Service) AuthoriseRecurringPaymentRevoke(ctx context.Context, req *pb.AuthoriseRecurringPaymentRevokeRequest) (*pb.AuthoriseRecurringPaymentRevokeResponse, error) {
	var (
		res = &pb.AuthoriseRecurringPaymentRevokeResponse{}
	)
	clientReqId := req.GetClientId().GetId()
	if clientReqId == "" {
		clientReqId = req.GetClientRequestId()
	}
	recurringPayment, err := s.recurringPaymentProcessor.GetRecurringPaymentById(ctx, req.GetRecurringPaymentId())
	if err != nil {
		logger.Error(ctx, "couldn't fetch recurring payment for given recurring payment id", zap.Error(err),
			zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	}

	err = validateActorIdForRequest(recurringPayment, req.GetCurrentActorId())
	if err != nil {
		logger.Error(ctx, "failed to validate actor", zap.String(logger.ACTOR_ID, req.GetCurrentActorId()))
		res.Status = rpc.StatusPermissionDenied()
		return res, nil
	}
	if !(recurringPayment.GetState() == pb.RecurringPaymentState_REVOKE_QUEUED ||
		recurringPayment.GetState() == pb.RecurringPaymentState_REVOKE_AUTHORISED) {
		logger.Error(ctx, "recurring payment not in expected state for authorising revoke request at domain service",
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.String(logger.STATE, recurringPayment.GetState().String()))
		res.Status = rpc.StatusFailedPrecondition()
		return res, nil
	}
	recurringPaymentAction, err := s.recurringPaymentActionsDao.GetByClientRequestId(ctx, clientReqId, false)
	if err != nil {
		logger.Error(ctx, "error in fetching revoke action", zap.String(logger.RECURRING_PAYMENT_ID,
			req.GetRecurringPaymentId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	status := s.validateActionExpiry(ctx, recurringPaymentAction, recurringPayment, pb.RecurringPaymentState_ACTIVATED)
	if status != nil {
		res.Status = status
		return res, nil
	}
	recurringPaymentState, err := s.getRecurringPaymentStateForInitiation(
		ctx,
		req.GetCurrentActorId(),
		recurringPayment.GetFromActorId(),
		pb.Action_REVOKE,
	)
	if err != nil {
		logger.Error(ctx, "error while fetching recurring payment state for initiation", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	err = s.initiateOrchestrationToRevokeRecurringPayment(ctx, req, recurringPaymentAction, recurringPayment)
	if err != nil {
		logger.Error(ctx, "error in initiating recurring payment orchestration for revoke", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	}

	err = s.updateRecurringPayment(ctx, recurringPayment, nil, recurringPayment.GetState(), recurringPaymentState)
	if err != nil {
		logger.Error(ctx, "error in updating recurring payment state to revoke initiated", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	recurringPayment.State = recurringPaymentState
	actionDetailedStatus, err := s.initiateRevokeWithDomainService(ctx, recurringPayment, recurringPaymentAction, req)
	if err != nil {
		logger.Error(ctx, "error in authorising recurring payment for revoke", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	}
	s.publishRecurringPaymentActionEvent(ctx, recurringPaymentAction, recurringPayment)
	res.Status = rpc.StatusOk()
	if actionDetailedStatus != nil {
		res.ActionDetailedStatus = actionDetailedStatus
	}
	return res, nil
}

// initiateRevokeWithDomainService - initiates revoke with domain service entities
func (s *Service) initiateRevokeWithDomainService(ctx context.Context, recurringPayment *pb.RecurringPayment,
	recurringPaymentAction *pb.RecurringPaymentsAction, req *pb.AuthoriseRecurringPaymentRevokeRequest) (*pb.ActionDetailedStatus_DetailedStatus,
	error) {
	var (
		actionStateToUpdate pb.ActionState
		stateToUpdate       pb.RecurringPaymentState
	)
	switch recurringPayment.GetType() {
	case pb.RecurringPaymentType_STANDING_INSTRUCTION:
		// We will update the state back to activated as revoke has reached terminal state of failure/success
		err := s.initiateRevokeWithSIService(ctx, recurringPayment.GetId(), req.GetCredential().GetPartnerSdkCredBlock())
		if err != nil {
			stateToUpdate = pb.RecurringPaymentState_ACTIVATED
			actionStateToUpdate = pb.ActionState_ACTION_FAILURE
		} else {
			stateToUpdate = pb.RecurringPaymentState_REVOKED
			actionStateToUpdate = pb.ActionState_ACTION_SUCCESS
		}
		err = s.updateRecurringPaymentAndActionInTxnBlock(ctx, recurringPaymentAction, recurringPayment,
			nil, nil, stateToUpdate, actionStateToUpdate)
		if err != nil {
			return nil, fmt.Errorf("error in updating recurring payment and action %w %s", rpc.StatusAsError(rpc.StatusInternal()),
				req.GetRecurringPaymentId())
		}
		if stateToUpdate == pb.RecurringPaymentState_REVOKED {
			return nil, nil
		} else {
			return nil, fmt.Errorf("error in initiating revoke with SI service %w %s", rpc.StatusAsError(rpc.StatusInternal()),
				req.GetRecurringPaymentId())
		}
	case pb.RecurringPaymentType_UPI_MANDATES:
		actionDetailedStatus, err := s.authoriseMandateAction(
			ctx,
			req.GetCredential(),
			recurringPayment,
			recurringPaymentAction,
			req.GetCurrentActorId(),
		)
		if err != nil {
			logger.Error(ctx, "error while authorising mandate revoke", zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()),
				zap.Error(err))
		}
		return actionDetailedStatus, nil
	default:
		return nil, fmt.Errorf("recurring payment type not supported for given recurring payment %w", rpc.StatusAsError(rpc.StatusInternal()))
	}
}

// initiateRevokeWithSIService - initiates revoke with SI service
func (s *Service) initiateRevokeWithSIService(ctx context.Context, recurringPaymentId string, credBlock string) error {
	revokeSIRes, err := s.siClient.AuthoriseRevoke(ctx, &siPb.AuthoriseRevokeRequest{
		RecurringPaymentId:  recurringPaymentId,
		PartnerSdkCredBlock: credBlock,
	})
	if te := epifigrpc.RPCError(revokeSIRes, err); te != nil {
		return fmt.Errorf("error in authorising si revoke %w", te)
	}
	return nil
}

// nolint: dupl
// initiateOrchestrationToRevokeRecurringPayment - initiates orchestration to revoke recurring payment
func (s *Service) initiateOrchestrationToRevokeRecurringPayment(ctx context.Context, req *pb.AuthoriseRecurringPaymentRevokeRequest,
	recurringPaymentAction *pb.RecurringPaymentsAction, recurringPayment *pb.RecurringPayment) error {
	clientReqId := req.GetClientRequestId()
	if clientReqId == "" {
		clientReqId = req.GetClientId().GetId()
	}
	if s.featureFlags.EnableRecurringPaymentRevokeViaCelestial() {
		workflowReq, processorErr := s.celestialProcessor.GetWorkflowByClientRequestId(ctx, clientReqId, workflowPb.Client_RECURRING_PAYMENT)
		if processorErr != nil {
			return fmt.Errorf("error fetching workflow request for given client req ID %s %w",
				clientReqId, processorErr,
			)
		}
		// workflow waits for signal only for 10 minutes after initiation, and will time out after that
		if time.Since(workflowReq.GetCreatedAt().AsTime()) > s.config.RecurringPaymentRevokeParams.AuthorisationTimeLimit {
			return fmt.Errorf("workflow can't be authorised after 10 minutes %w", rpc.StatusAsError(rpc.StatusPermissionDenied()))
		}

		// sending signal to workflow
		payload, marshalErr := protojson.Marshal(&payloadPb.RevokeRecurringPaymentAuthSignal{})
		if marshalErr != nil {
			return fmt.Errorf("error in marshalling revoke recurring payment auth signal payload %w %s", rpc.StatusAsError(rpc.StatusInternal()), clientReqId)
		}
		err := s.celestialProcessor.SignalWorkflow(ctx, clientReqId, string(rpNs.RevokeRecurringPaymentAuthSignal),
			workflowPb.Client_RECURRING_PAYMENT, payload, recurringPayment.GetEntityOwnership(), recurringpayment.OwnershipToUseCaseForRecPay[recurringPayment.GetEntityOwnership()])
		if err != nil {
			return fmt.Errorf("error while signaling workflow for revoke %w %s", rpc.StatusAsError(rpc.StatusInternal()), recurringPaymentAction.GetClientRequestId())
		}
	} else {
		err := s.initiateOrderProcessing(ctx, recurringPaymentAction.GetClientRequestId())
		if err != nil {
			return fmt.Errorf("error while triggering order processing for revoke %w %s", rpc.StatusAsError(rpc.StatusInternal()),
				recurringPaymentAction.GetClientRequestId())
		}
	}
	return nil
}
