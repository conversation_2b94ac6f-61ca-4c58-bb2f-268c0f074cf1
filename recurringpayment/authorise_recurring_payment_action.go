// nolint: goimports
package recurringpayment

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/epificontext"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	pb "github.com/epifi/gamma/api/recurringpayment"
	payloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	"github.com/epifi/gamma/pkg/recurringpayment"
)

// nolint: funlen, dupl
// We are using the client as workflowPb.Client_RECURRING_PAYMENT across recurring payment workflows to prevent exposure of backend information on Client side
func (s *Service) AuthoriseRecurringPaymentAction(ctx context.Context, req *pb.AuthoriseRecurringPaymentActionRequest) (*pb.AuthoriseRecurringPaymentActionResponse, error) {
	var (
		res                  = &pb.AuthoriseRecurringPaymentActionResponse{}
		actionDetailedStatus *pb.ActionDetailedStatus_DetailedStatus
	)
	recurringPayment, err := s.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "record not found for recurring payment id", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error while fetching recurring payment", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		logger.Debug(ctx, "recurring payment record fetched successfully", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
	}
	ctx = epificontext.WithOwnership(ctx, recurringPayment.GetEntityOwnership())

	err = validateActorIdForRequest(recurringPayment, req.GetCurrentActorId())
	if err != nil {
		logger.Error(ctx, "failed to validate actor", zap.String(logger.ACTOR_ID, req.GetCurrentActorId()), zap.Error(err))
		res.Status = rpc.StatusPermissionDenied()
		return res, nil
	}

	recurringPaymentAction, err := s.recurringPaymentActionsDao.GetByClientRequestId(ctx, req.GetClientRequestId(), false)
	if err != nil {
		logger.Error(ctx, "failed to fetch action", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	if req.GetAction() != recurringPaymentAction.GetAction() {
		logger.Error(ctx, "invalid authorisation request received", zap.String(logger.ACTION_TYPE,
			req.GetAction().String()), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()))
		res.Status = rpc.StatusFailedPrecondition()
		return res, nil
	}
	if !s.featureFlags.EnableRecurringPaymentPauseUnpauseViaCelestial() {
		_, status := s.getOrderByClientRequestId(ctx, req.GetClientRequestId())
		if !status.IsSuccess() {
			logger.Error(ctx, "error in fetching order for client request id",
				zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}
	status := validateRecurringPaymentState(ctx, recurringPayment, recurringPaymentAction)
	if status != nil {
		res.Status = status
		return res, nil
	}
	recurringPaymentState, err := s.getRecurringPaymentStateForInitiation(
		ctx,
		req.GetCurrentActorId(),
		recurringPayment.GetFromActorId(),
		recurringPaymentAction.GetAction(),
	)
	if err != nil {
		logger.Error(ctx, "error while fetching recurring payment state for initiation", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	err = s.initiateOrchestrationToPauseUnpauseRecurringPayment(ctx, req, recurringPaymentAction, recurringPayment)
	if err != nil {
		logger.Error(ctx, "error while triggering order processing", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()), zap.String(logger.CLIENT_REQUEST_ID,
				recurringPaymentAction.GetClientRequestId()), zap.String(logger.ACTION_TYPE, req.GetAction().String()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	err = s.recurringPaymentDao.UpdateAndChangeStatus(ctx, recurringPayment, nil,
		recurringPayment.GetState(), recurringPaymentState)
	if err != nil {
		logger.Error(ctx, "error in updating recurring payment state", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	recurringPayment.State = recurringPaymentState
	switch recurringPayment.GetType() {
	case pb.RecurringPaymentType_UPI_MANDATES:
		actionDetailedStatus, err = s.authoriseMandateAction(
			ctx,
			req.GetCredential(),
			recurringPayment,
			recurringPaymentAction,
			req.GetCurrentActorId(),
		)
		if err != nil {
			logger.Error(ctx, "error while authorising mandate modify", zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()),
				zap.Error(err))
		}
		res.ActionDetailedStatus = actionDetailedStatus
	default:
		logger.Error(ctx, "type not supported for initiating pause unpause", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	s.publishRecurringPaymentActionEvent(ctx, recurringPaymentAction, recurringPayment)

	res.Status = rpc.StatusOk()
	return res, nil
}

// validateRecurringPaymentState checks if recurring payment is in expected state for authorising request
// nolint: dupl
func validateRecurringPaymentState(ctx context.Context, recurringPayment *pb.RecurringPayment, recurringPaymentAction *pb.RecurringPaymentsAction) *rpc.Status {
	switch recurringPaymentAction.GetAction() {
	case pb.Action_PAUSE:
		switch recurringPayment.GetState() {
		case pb.RecurringPaymentState_PAUSE_AUTHORISED:
			logger.Info(ctx, "recurring payment authorisation already initiated",
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()), zap.String(logger.CLIENT_REQUEST_ID, recurringPaymentAction.GetClientRequestId()))
			return rpc.StatusOk()
		case pb.RecurringPaymentState_PAUSE_INITIATED, pb.RecurringPaymentState_PAUSE_QUEUED:
			return nil
		default:
			logger.Error(ctx, "recurring payment not in the right state to be authorised", zap.String(logger.STATE, recurringPayment.GetState().String()),
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()), zap.String(logger.CLIENT_REQUEST_ID, recurringPaymentAction.GetClientRequestId()))
			return rpc.StatusFailedPrecondition()
		}
	case pb.Action_UNPAUSE:
		switch recurringPayment.GetState() {
		case pb.RecurringPaymentState_UNPAUSE_AUTHORISED:
			logger.Info(ctx, "recurring payment authorisation already initiated",
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()), zap.String(logger.CLIENT_REQUEST_ID, recurringPaymentAction.GetClientRequestId()))
			return rpc.StatusOk()
		case pb.RecurringPaymentState_UNPAUSE_INITIATED, pb.RecurringPaymentState_UNPAUSE_QUEUED:
			return nil
		default:
			logger.Error(ctx, "recurring payment not in the right state to be authorised", zap.String(logger.STATE, recurringPayment.GetState().String()),
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()), zap.String(logger.CLIENT_REQUEST_ID, recurringPaymentAction.GetClientRequestId()))
			return rpc.StatusFailedPrecondition()
		}
	default:
		logger.Error(ctx, "action not supported", zap.String(logger.ACTION_TYPE,
			recurringPaymentAction.GetAction().String()), zap.String(logger.CLIENT_REQUEST_ID, recurringPaymentAction.GetClientRequestId()))
		return rpc.StatusFailedPrecondition()
	}
}

func (s *Service) initiateOrchestrationToPauseUnpauseRecurringPayment(ctx context.Context, req *pb.AuthoriseRecurringPaymentActionRequest,
	recurringPaymentAction *pb.RecurringPaymentsAction, recurringPayment *pb.RecurringPayment) error {
	clientReqId := req.GetClientRequestId()
	if clientReqId == "" {
		clientReqId = req.GetClientId().GetId()
	}
	if s.featureFlags.EnableRecurringPaymentPauseUnpauseViaCelestial() {
		workflowReq, processorErr := s.celestialProcessor.GetWorkflowByClientRequestId(ctx, clientReqId, workflowPb.Client_RECURRING_PAYMENT)
		if processorErr != nil {
			return fmt.Errorf("error fetching workflow request for given client req ID %s %w",
				clientReqId, processorErr,
			)
		}
		// workflow waits for signal only for 10 minutes after initiation, and will time out after that
		if time.Since(workflowReq.GetCreatedAt().AsTime()) > s.config.RecurringPaymentPauseUnpauseParams.AuthorisationTimeLimit {
			return fmt.Errorf("workflow can't be authorised after 10 minutes %w", rpc.StatusAsError(rpc.StatusPermissionDenied()))
		}

		// sending signal to workflow
		payload, marshalErr := protojson.Marshal(&payloadPb.PauseUnpauseRecurringPaymentAuthSignal{})
		if marshalErr != nil {
			return fmt.Errorf("error in marshalling pause unpause recurring payment auth signal payload %w %s", rpc.StatusAsError(rpc.StatusInternal()), clientReqId)
		}
		err := s.celestialProcessor.SignalWorkflow(ctx, clientReqId, string(rpNs.PauseUnpauseRecurringPaymentAuthSignal),
			workflowPb.Client_RECURRING_PAYMENT, payload, recurringPayment.GetEntityOwnership(), recurringpayment.OwnershipToUseCaseForRecPay[recurringPayment.GetEntityOwnership()])
		if err != nil {
			return fmt.Errorf("error while signaling workflow for pause/unpause %w %s", rpc.StatusAsError(rpc.StatusInternal()), recurringPaymentAction.GetClientRequestId())
		}
	} else {
		err := s.initiateOrderProcessing(ctx, recurringPaymentAction.GetClientRequestId())
		if err != nil {
			return fmt.Errorf("error while triggering order processing for pause/unpause %w %s", rpc.StatusAsError(rpc.StatusInternal()),
				recurringPaymentAction.GetClientRequestId())
		}
	}
	return nil
}
