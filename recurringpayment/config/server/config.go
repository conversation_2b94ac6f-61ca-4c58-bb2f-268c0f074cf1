package server

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/knadh/koanf"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/pay/pgerrorcodes"

	releaseConfig "github.com/epifi/gamma/pkg/feature/release/config"
	payPkg "github.com/epifi/gamma/pkg/pay"
	"github.com/epifi/gamma/recurringpayment/config/commons"
)

var (
	once   sync.Once
	config *Config
	err    error
	kConf  *koanf.Koanf
)

var (
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		config, kConf, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, nil
}

func GetLocalKConf() *koanf.Koanf {
	return kConf
}

func loadConfig() (*Config, *koanf.Koanf, error) {
	// will be used only for test environment
	configDirPath := testEnvConfigDir()

	conf := &Config{}

	// loads config from file
	k, _, err := cfg.LoadConfigUsingKoanf(configDirPath, cfg.RECURRING_PAYMENT_SERVICE)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to load dynamic config: %w", err)
	}

	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, nil, fmt.Errorf("failed to refresh dymanic config: %w", err)
	}

	_, err = cfg.LoadSecretsAndPrepareDBConfig(conf.Secrets, conf.Application.Environment, conf.AWS.Region, conf.EpifiDb, conf.RecurringPaymentDb)
	if err != nil {
		return nil, nil, err
	}

	err = cfg.LoadAllSecretsV3(conf.PgProgramToAuthSecretMap, conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		return nil, nil, err
	}

	err = pgerrorcodes.LoadPaymentGatewayErrorMappings(conf.RazorPayResponseCodesJson)
	if err != nil {
		return nil, nil, err
	}

	return conf, k, nil
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	cfg.UpdateDbEndpointInConfig(c.EpifiDb, dbServerEndpoint)

	if err := readAndSetEnv(c); err != nil {
		return fmt.Errorf("failed to read and set env var: %w", err)
	}

	cfg.UpdateSecretValues(c.EpifiDb, c.Secrets, keyToSecret)
	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Port = intVal
	}

	if val, ok := os.LookupEnv("DB_HOST"); ok {
		c.EpifiDb.Host = val
	}

	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

//go:generate conf_gen github.com/epifi/gamma/recurringpayment/config/server Config
type Config struct {
	Application                        *Application
	Server                             *Server
	EpifiDb                            *cfg.DB
	RecurringPaymentDb                 *cfg.DB
	Secrets                            *cfg.Secrets
	AWS                                *cfg.AWS
	Flags                              *Flags
	AuthenticationTimeLimitForAction   *AuthenticationTimeLimitForAction
	RecurringPaymentNotificationParams *RecurringPaymentNotificationParams
	UpiMandateUrn                      *UpiMandateUrn
	UpiMandateValidationParams         *UpiMandateValidationParams
	UpiMandateNotifDateFormat          string
	SIExecutionParams                  *SIExecutionParams `dynamic:"true"`
	Tracing                            *cfg.Tracing
	RecurringPaymentFrequencyMapping   map[string]string
	// PaymentEnquiryParams configurable parameters for payment enquiry
	PaymentEnquiryParams               *payPkg.PaymentEnquiryParams
	FeatureFlags                       *FeatureFlags `dynamic:"true"`
	RecurringPaymentCreationParams     *RecurringPaymentCreationParams
	SignalWorkflowPublisher            *cfg.SqsPublisher
	RecurringPaymentExecutionParams    *RecurringPaymentExecutionParams
	RecurringPaymentModificationParams *RecurringPaymentModificationParams
	RecurringPaymentRevokeParams       *RecurringPaymentRevokeParams
	RecurringPaymentRetryErrors        map[string][]string
	// list of mccs for which pause is blocked if the mandate is not revokeable
	NonPauseableMccsForNonRevokeableMandates []string

	// mcc specific info to be shown to the user while creating a recurring payment. E.g. For mcc - 7322
	// we will show Transaction declined due to insufficiency of funds for this UPI Autopay is a punishable
	// offence under Section 25 of the Payment & Settlement Act, 2007 (PSS Act)
	MccWarningMessageMap map[string]*TextObject
	// stores the max amount allowed for a combination of mcc and purpose code
	// key for the map is mcc_purpose and value is the max amount limit
	// For eg. for mcc 6211 and purpose 01 if the max allowed amount i 5 lac
	// key -> 6211-01, value - 500000
	MaxAmountForMCCAndPurposeCode      map[string]*money.Money
	RecurringPaymentPauseUnpauseParams *RecurringPaymentPauseUnpauseParams
	InPaymentOrderUpdatePublisher      *cfg.SqsPublisher
	// List of non Tpap psp handles
	NonTpapPspHandles                                    []string
	MaxCollectAmountLimitForNonVerifiedMerchants         *money.Money
	RecurringPaymentCreationAuthVendorCallbackSubscriber *cfg.SqsSubscriber `dynamic:"true"`
	FetchAndCreateOffAppRecurringPaymentSubscriber       *cfg.SqsSubscriber `dynamic:"true"`
	FetchAndCreateFailedEnachTransactionSubscriber       *cfg.SqsSubscriber `dynamic:"true"`
	// FeatureReleaseConfig defines the feature rollout configuration for failed enach transactions
	// This is used to control the rollout of the feature based on actorId, app version, and user groups
	FeatureReleaseConfig                          *releaseConfig.FeatureReleaseConfig `dynamic:"true"`
	FetchAndCreateFailedEnachTransactionPublisher *cfg.SqsPublisher
	FetchAndCreateOffAppRecurringPaymentPublisher *cfg.SqsPublisher
	OffAppRecurringPaymentExecutionSubscriber     *cfg.SqsSubscriber `dynamic:"true"`

	EnachFundTransferOrderUpdateSqsSubscriber *cfg.SqsSubscriber `dynamic:"true"`

	// EnachServiceConfig stores config related to enach service
	EnachServiceConfig *EnachServiceConfig `dynamic:"true"`
	// EnachCreationValidationConfig stores config related to internal validations needed during enach mandate creation.
	EnachCreationValidationConfig *commons.EnachCreationValidationConfig
	PgProgramToAuthSecretMap      map[string]*PgProgramToAuthSecret
	// parameters specific to recurring payments which are orchestrated via external payment gateways
	PgParams *PgParams `dynamic:"true"`
	// stores the max amount allowed for mandate execution where Authentication is not required.
	// currently this amount is 15,000 INR.
	MaxAmountForMandateExecutionWithoutAFA *money.Money
	// stores the max amount allowed for mcc for which Additional Factor Authentication is not required for mandate execution.
	// key for the map is mcc and value is the max amount limit
	// for eg. for mcc 5413 the max allowed amount is 1 lac
	MaxAmountLimitForMccWithoutAFA map[string]*money.Money
	// flag to indicate whether to enable resource provider based db instance provision or whether to
	// go with a static db instance. Disabling this flag doesn't mean that lookup won't happen from multi-db.
	// This is primarily during insertion only.
	// Assumption : Once the flag is turned on, it will not be turned off again. Since it will go through a
	// CUG, any occurring issues will be fixed within the CUG itself. Also, all the systems using this
	// have been made backward compatible. So the older prod entities will not see any issues. Possible consequences
	// can be that the data got written with entity segregation but fetched from the default
	// epifi db but since that scale will be low at the time of CUG, we would be able to fix it before going external
	EnableEntitySegregation bool `dynamic:"true"`
	// flag to indicate whether to enable entity segregation for ENACH mandate
	EnableEnachMandateEntitySegregation bool `dynamic:"true"`
	RazorPayResponseCodesJson           string
	// config storing parameters for controlling ENACH consumer methods
	EnachConsumerConfig *EnachConsumerConfig `dynamic:"true"`
	// Minimum duration to wait after txn creation before vendor status check for UPI mandates.
	UPIMandateExecutionVendorCallMinDelay time.Duration `dynamic:"true"`
}

// PgParams stores the parameters specific to recurring payments which are orchestrated via external payment gateways
// like Razorpay, PayU etc.
// Example :
// PgParams:
//
//	RecurringPaymentTypeToExecutionLimit:
//	  "UPI_MANDATES": 15000
type PgParams struct {
	RecurringPaymentTypeToExecutionLimit map[string]float64
	// VendorOrderExpirationDuration stores the duration for which the vendor order is valid for execution.
	// If the order status does not change from CREATED, after this duration, the recurringpayment order will
	// be marked as failed on our side irrespective of whether vendor sends us failed status or not.
	VendorOrderExpirationDuration map[string]time.Duration
	// RpExecutionPaymentAutoCaptureTimeout stores the duration within which the recurring payment
	// execution has to be auto captured. If the payment is not captured within this time period then, it can be captured
	// manually till the manual capture timeout expires, as supported by the payment gateway vendor.
	RpExecutionPaymentAutoCaptureTimeout map[string]time.Duration `dynamic:"true"`
	// Flag to enable/disable the Enach Mandate creation in recurring_payment DB for payment gateway mandates.
	EnableOffAppMandateCreation bool `dynamic:"true"`
}

type EnachServiceConfig struct {
	// SupportedBankToDetailsMap stores details of banks which support enach mandates.
	SupportedBankToDetailsMap map[string]*EnachSupportedBankDetails `dynamic:"true"`
	// VendorToActivationCooldownMap stores the vendor specific cool off details for activation once mandate created is authorized by the payer.
	VendorToActivationCooldownMap map[string]time.Duration `dynamic:"true"`
	// VendorConfig stores  the vendor specific config for enach.
	VendorConfig *EnachVendorConfig `dynamic:"true"`
}

type EnachSupportedBankDetails struct {
	BankIconUrl string `dynamic:"true"`
	// SupportedAuthModes denotes the auth modes like NET_BANKING, DEBIT_CARD authentication etc through which payer can authorize enach mandate creation.
	SupportedAuthModes []string `dynamic:"true"`
}
type EnachVendorConfig struct {
	// FederalConfig denotes the federal specific enach config.
	FederalConfig *EnachFederalConfig `dynamic:"true"`
}
type EnachFederalConfig struct {
	// MandateCreationConfig denotes federal specific enach mandate creation config.
	MandateCreationConfig  *EnachMandateCreationFederalConfig  `dynamic:"true"`
	MandateExecutionConfig *EnachMandateExecutionFederalConfig `dynamic:"true"`
}
type EnachMandateCreationFederalConfig struct {
	// RedirectionUrl denotes the federal web app url where used should be redirected to for mandate creation
	RedirectionUrl string `dynamic:"true"`
	// RedirectionExpiry denotes the time until which the user is allowed to be the on the federal mandate creation webapp redirection flow,
	// once the expiry time is reached, the app should force exit the web app..
	RedirectionExpiry time.Duration `dynamic:"true"`
	// ResponseUrl denotes the url on which callback response for mandate creation is posted by federal post mandate creation on federal webapp.
	ResponseUrl string `dynamic:"true"`
	// ExitUrl is used to support exiting the federal mandate creation web app once the mandate creation is completed, federal web app flow redirects
	// to this url once mandate creation web app flow is completed.
	ExitUrl string `dynamic:"true"`
	// EnableRsaEncryption denotes whether rsa encryption for mandate creation payload is enabled or not.
	EnableRsaEncryption bool `dynamic:"true"`
}

type EnachMandateExecutionFederalConfig struct {
	// Minimum wait duration for a returned transaction
	// As for now the minimum wait for a returned transaction is 72 hours
	MinWaitDurationForRepresentingReturnedTxn time.Duration `dynamic:"true"`
}

// RecurringPaymentNotificationParams for templating notification data
// suffix payer signifies that notification params are for payer.
type RecurringPaymentNotificationParams struct {
	MandateCreationPayer         payPkg.NotificationTemplateParams
	MandateExecutionSuccessPayer payPkg.NotificationTemplateParams
	MandateExecutionFailedPayer  payPkg.NotificationTemplateParams
	MandateApprovedPayer         payPkg.NotificationTemplateParams
	MandateDeclinedPayer         payPkg.NotificationTemplateParams
	MandateReceivedPayer         payPkg.NotificationTemplateParams
	MandateRevokedPayer          payPkg.NotificationTemplateParams
	MandateModifiedPayer         payPkg.NotificationTemplateParams
	MandateAuthorizedPayer       payPkg.NotificationTemplateParams
	MandateAcceptancePayee       payPkg.NotificationTemplateParams
	MandatePausedPayer           payPkg.NotificationTemplateParams
	MandateUnpausedPayer         payPkg.NotificationTemplateParams
	SICreationPayer              payPkg.NotificationTemplateParams
	SIDeclinedPayer              payPkg.NotificationTemplateParams
	SIExecutionSuccessPayer      payPkg.NotificationTemplateParams
	SIExecutionFailedPayer       payPkg.NotificationTemplateParams
}

type Application struct {
	Environment string
	Name        string
}

type Server struct {
	Port            int
	HealthCheckPort int
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool
}

// AuthenticationTimeLimitForAction is map between recurring payment type to initiated by to action and the maximum time
// for which we will wait for user authorisation after which we will move order status to failed.
// The value change in case of payee initiated and payer initiated
type AuthenticationTimeLimitForAction map[string]map[string]map[string]time.Duration

// UpiMandateUrn is to store info for creating mandate urn
type UpiMandateUrn struct {
	CollectMandatePrefix string
}

type UpiMandateValidationParams struct {
	AllowedHoursForModification                  int
	MaximumAllowedDurationBetweenStartAndEndDate int
}

type SIExecutionParams struct {
	// Payment protocol via which standing instruction execution will be done
	PaymentProtocol int32 `dynamic:"true"`
}

type FeatureFlags struct {
	EnableRecurringPaymentCreationViaCelestial             bool `dynamic:"true"`
	EnableRecurringPaymentExecutionWithoutAuthViaCelestial bool `dynamic:"true"`
	EnableRecurringPaymentExecutionWithAuthViaCelestial    bool `dynamic:"true"`
	EnableRecurringPaymentModificationViaCelestial         bool `dynamic:"true"`
	EnableRecurringPaymentRevokeViaCelestial               bool `dynamic:"true"`
	EnableRecurringPaymentPauseUnpauseViaCelestial         bool `dynamic:"true"`
}

type RecurringPaymentCreationParams struct {
	AuthorisationTimeLimit time.Duration
}

type RecurringPaymentExecutionParams struct {
	AuthorisationTimeLimit time.Duration

	// Prefix string for process execution lock
	ProcessRecurringPaymentExecutionLock string
	// Lease duration for process execution lock.
	ProcessRecurringPaymentExecutionLockLeaseDuration time.Duration
}

type RecurringPaymentModificationParams struct {
	AuthorisationTimeLimit time.Duration
}

type RecurringPaymentRevokeParams struct {
	AuthorisationTimeLimit time.Duration
}

type RecurringPaymentPauseUnpauseParams struct {
	AuthorisationTimeLimit time.Duration
}

// TextObject - a simple message could be represented by the following parameters
type TextObject struct {
	DisplayValue string
	FontColour   string
	BgColour     string
	FontStyle    string
	DisplayType  string
}

// GetMaxAmountForMccAndPurposeCode returns the max amount allowed for given mcc and purpose code
// returns nil in max amount cap is not applicable
func GetMaxAmountForMccAndPurposeCode(mcc, purpose string) *money.Money {
	if amt, ok := config.MaxAmountForMCCAndPurposeCode[mcc+"-"+purpose]; ok {
		return amt
	}
	return nil
}

type PgProgramToAuthSecret struct {
	AuthParam      string `iam:"sm-read"`
	AuthParamValue string `field:"AuthParam"`
}

type EnachConsumerConfig struct {
	// Maximum number of days for which FetchAndCreateFailedEnachTransactions
	// consumer method may fetch the transactions.
	MaxAllowedDaysToFetchFailedTxns int64 `dynamic:"true"`
	// Number of days for which to sync failed ENACH transactions
	NumDaysToFetchFailedTxns int64 `dynamic:"true"`
}
