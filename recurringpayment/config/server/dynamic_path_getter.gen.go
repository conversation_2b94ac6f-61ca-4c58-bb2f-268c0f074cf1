// Code generated by tools/conf_gen/dynamic_conf_gen.go
package server

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enableentitysegregation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableEntitySegregation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableEntitySegregation, nil
	case "enableenachmandateentitysegregation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableEnachMandateEntitySegregation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableEnachMandateEntitySegregation, nil
	case "upimandateexecutionvendorcallmindelay":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UPIMandateExecutionVendorCallMinDelay\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UPIMandateExecutionVendorCallMinDelay, nil
	case "siexecutionparams":
		return obj.SIExecutionParams.Get(dynamicFieldPath[1:])
	case "featureflags":
		return obj.FeatureFlags.Get(dynamicFieldPath[1:])
	case "recurringpaymentcreationauthvendorcallbacksubscriber":
		return obj.RecurringPaymentCreationAuthVendorCallbackSubscriber.Get(dynamicFieldPath[1:])
	case "fetchandcreateoffapprecurringpaymentsubscriber":
		return obj.FetchAndCreateOffAppRecurringPaymentSubscriber.Get(dynamicFieldPath[1:])
	case "fetchandcreatefailedenachtransactionsubscriber":
		return obj.FetchAndCreateFailedEnachTransactionSubscriber.Get(dynamicFieldPath[1:])
	case "featurereleaseconfig":
		return obj.FeatureReleaseConfig.Get(dynamicFieldPath[1:])
	case "offapprecurringpaymentexecutionsubscriber":
		return obj.OffAppRecurringPaymentExecutionSubscriber.Get(dynamicFieldPath[1:])
	case "enachfundtransferorderupdatesqssubscriber":
		return obj.EnachFundTransferOrderUpdateSqsSubscriber.Get(dynamicFieldPath[1:])
	case "enachserviceconfig":
		return obj.EnachServiceConfig.Get(dynamicFieldPath[1:])
	case "pgparams":
		return obj.PgParams.Get(dynamicFieldPath[1:])
	case "enachconsumerconfig":
		return obj.EnachConsumerConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *SIExecutionParams) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "paymentprotocol":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PaymentProtocol\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PaymentProtocol, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for SIExecutionParams", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *FeatureFlags) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enablerecurringpaymentcreationviacelestial":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableRecurringPaymentCreationViaCelestial\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableRecurringPaymentCreationViaCelestial, nil
	case "enablerecurringpaymentexecutionwithoutauthviacelestial":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableRecurringPaymentExecutionWithoutAuthViaCelestial\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableRecurringPaymentExecutionWithoutAuthViaCelestial, nil
	case "enablerecurringpaymentexecutionwithauthviacelestial":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableRecurringPaymentExecutionWithAuthViaCelestial\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableRecurringPaymentExecutionWithAuthViaCelestial, nil
	case "enablerecurringpaymentmodificationviacelestial":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableRecurringPaymentModificationViaCelestial\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableRecurringPaymentModificationViaCelestial, nil
	case "enablerecurringpaymentrevokeviacelestial":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableRecurringPaymentRevokeViaCelestial\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableRecurringPaymentRevokeViaCelestial, nil
	case "enablerecurringpaymentpauseunpauseviacelestial":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableRecurringPaymentPauseUnpauseViaCelestial\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableRecurringPaymentPauseUnpauseViaCelestial, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for FeatureFlags", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *EnachServiceConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "supportedbanktodetailsmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.SupportedBankToDetailsMap, nil
		case len(dynamicFieldPath) > 1:

			return obj.SupportedBankToDetailsMap[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.SupportedBankToDetailsMap, nil
	case "vendortoactivationcooldownmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.VendorToActivationCooldownMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"VendorToActivationCooldownMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.VendorToActivationCooldownMap[dynamicFieldPath[1]], nil

		}
		return obj.VendorToActivationCooldownMap, nil
	case "vendorconfig":
		return obj.VendorConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for EnachServiceConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *EnachSupportedBankDetails) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "supportedauthmodes":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SupportedAuthModes\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SupportedAuthModes, nil
	case "bankiconurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BankIconUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BankIconUrl, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for EnachSupportedBankDetails", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *EnachVendorConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "federalconfig":
		return obj.FederalConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for EnachVendorConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *EnachFederalConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "mandatecreationconfig":
		return obj.MandateCreationConfig.Get(dynamicFieldPath[1:])
	case "mandateexecutionconfig":
		return obj.MandateExecutionConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for EnachFederalConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *EnachMandateCreationFederalConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enablersaencryption":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableRsaEncryption\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableRsaEncryption, nil
	case "redirectionexpiry":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RedirectionExpiry\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RedirectionExpiry, nil
	case "redirectionurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RedirectionUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RedirectionUrl, nil
	case "responseurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ResponseUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ResponseUrl, nil
	case "exiturl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ExitUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ExitUrl, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for EnachMandateCreationFederalConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *EnachMandateExecutionFederalConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "minwaitdurationforrepresentingreturnedtxn":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinWaitDurationForRepresentingReturnedTxn\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinWaitDurationForRepresentingReturnedTxn, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for EnachMandateExecutionFederalConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *PgParams) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enableoffappmandatecreation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableOffAppMandateCreation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableOffAppMandateCreation, nil
	case "rpexecutionpaymentautocapturetimeout":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.RpExecutionPaymentAutoCaptureTimeout, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"RpExecutionPaymentAutoCaptureTimeout\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.RpExecutionPaymentAutoCaptureTimeout[dynamicFieldPath[1]], nil

		}
		return obj.RpExecutionPaymentAutoCaptureTimeout, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for PgParams", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *EnachConsumerConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "maxalloweddaystofetchfailedtxns":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxAllowedDaysToFetchFailedTxns\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxAllowedDaysToFetchFailedTxns, nil
	case "numdaystofetchfailedtxns":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NumDaysToFetchFailedTxns\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NumDaysToFetchFailedTxns, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for EnachConsumerConfig", strings.Join(dynamicFieldPath, "."))
	}
}
