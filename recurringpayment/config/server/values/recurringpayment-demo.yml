Application:
  Environment: "demo"
  Name: "recurringpayment"

Secrets:
  Ids:
    EnachSecrets: "demo/recurringpayment/enach/secrets"
    EnachFederalPublicKey: "demo/recurringpayment/enach/federal/public-key"

# Recurring payment service is actually initialized on the port defined in order-<env>.yml
# These properties are kept here from forward compatibility POV when we may want Recurring payment service to be running on a
# different port in the order server
Server:
  Port: 8091
  HealthCheckPort: 9999

# Recurring payment service uses DB connection initialized by order-<env>.yml
# These properties are kept from forward compatibility POV when we may want to have DB separate connection
# for Recurring payment service
EpifiDb:
  AppName: "recurringpayment"
  DbType: "CRDB"
  StatementTimeout: 5s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "demo/cockroach/ca.crt"
  SSLClientCert: "demo/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "demo/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

RecurringPaymentDb:
  Name: "recurring_payment"
  DbType: "PGDB"
  DbServerAlias: "PLUTUS_RDS"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "recurring_payment"
  SecretName: "demo/rds/epifiplutus/recurring_payment_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Tracing:
  Enable: true

FeatureFlags:
  EnableRecurringPaymentCreationViaCelestial: false
  EnableRecurringPaymentModificationViaCelestial: false
  EnableRecurringPaymentRevokeViaCelestial: false

SIExecutionParams:
  PaymentProtocol: 3

SignalWorkflowPublisher:
  QueueName: "demo-celestial-signal-workflow-queue"

UPIEnquiryPublisher:
  QueueName: "demo-payment-upi-enquiry-queue"

InPaymentOrderUpdatePublisher:
  QueueName: "demo-order-in-payment-order-update-queue"

FetchAndCreateOffAppRecurringPaymentPublisher:
  QueueName: "demo-fetch-recurringpayemnt-from-vendor-queue"

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

NonTpapPspHandles: ["fede"]

RecurringPaymentCreationAuthVendorCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "demo-recurringpayment-creation-auth-vendor-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Minute"

FetchAndCreateOffAppRecurringPaymentSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "demo-fetch-recurringpayemnt-from-vendor-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 20
      TimeUnit: "Minute"

EnachServiceConfig:
  VendorToActivationCooldownMap:
    "FEDERAL_BANK": 5m
  VendorConfig:
    FederalConfig:
      MandateCreationConfig:
        RedirectionUrl: "https://fedmnduat.federalbank.co.in:4012/e-mandate/merchant/"
        ResponseUrl: "https://vnotificationgw.demo.pointz.in/openbanking/recurringpayment/enach/registration/federal"
        ExitUrl: "https://vnotificationgw.demo.pointz.in/openbanking/recurringpayment/enach/registration/federal"
        RedirectionExpiry: "7m"
        EnableRsaEncryption: true
      MandateExecutionConfig:
        # as per compliance a returned transaction can be represented for execution only after 3 days since last return
        MinWaitDurationForRepresentingReturnedTxn: "5m"

EnachFundTransferOrderUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "demo-enach-fund-transfer-order-update-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Minute"

FetchAndCreateFailedEnachTransactionSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "demo-recurringpayment-failed-enach-transaction-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 3
      TimeUnit: "Minute"
