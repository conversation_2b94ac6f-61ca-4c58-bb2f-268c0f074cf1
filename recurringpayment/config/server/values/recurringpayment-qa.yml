Application:
  Environment: "qa"
  Name: "recurringpayment"

Secrets:
  Ids:
    EnachSecrets: "qa/recurringpayment/enach/secrets"
    EnachFederalPublicKey: "qa/recurringpayment/enach/federal/public-key"

# Recurring Payment service is actually initialized on the port defined in order-<env>.yml
# These properties are kept here from forward compatibility POV when we may want Recurring Payment service to be running on a
# different port in the order server
Server:
  Port: 8091
  HealthCheckPort: 9999

# Recurring Payment service uses DB connection initialized by order-<env>.yml
# These properties are kept from forward compatibility POV when we may want to have DB separate connection
# for Recurring Payment service
EpifiDb:
  AppName: "recurringpayment"
  DbType: "CRDB"
  StatementTimeout: 1s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "qa/cockroach/ca.crt"
  SSLClientCert: "qa/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "qa/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

RecurringPaymentDb:
  Name: "recurring_payment"
  DbType: "PGDB"
  DbServerAlias: "PLUTUS_RDS"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "recurring_payment"
  SecretName: "qa/rds/epifiplutus/recurring_payment_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Tracing:
  Enable: true

FeatureFlags:
  EnableRecurringPaymentCreationViaCelestial: true
  EnableRecurringPaymentExecutionWithAuthViaCelestial: true
  EnableRecurringPaymentModificationViaCelestial: true
  EnableRecurringPaymentRevokeViaCelestial: true
  EnableRecurringPaymentExecutionWithoutAuthViaCelestial: true
  EnableRecurringPaymentPauseUnpauseViaCelestial: true

SIExecutionParams:
  PaymentProtocol: 3

SignalWorkflowPublisher:
  QueueName: "qa-celestial-signal-workflow-queue"

UPIEnquiryPublisher:
  QueueName: "qa-payment-upi-enquiry-queue"

InPaymentOrderUpdatePublisher:
  QueueName: "qa-order-in-payment-order-update-queue"

FetchAndCreateOffAppRecurringPaymentPublisher:
  QueueName: "qa-fetch-recurringpayemnt-from-vendor-queue"

FetchAndCreateFailedEnachTransactionPublisher:
  QueueName: "qa-recurringpayment-failed-enach-transaction-queue"

OffAppRecurringPaymentExecutionSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-recurringpayemnt-off-app-execution-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 1
      MaxAttempts: 30
      TimeUnit: "Second"

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

NonTpapPspHandles: ["fede"]

RecurringPaymentCreationAuthVendorCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-recurringpayment-creation-auth-vendor-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Minute"

FetchAndCreateOffAppRecurringPaymentSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-fetch-recurringpayemnt-from-vendor-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 20
      TimeUnit: "Minute"

EnachServiceConfig:
  VendorToActivationCooldownMap:
    # keeping it 1 minutes as it is in used in smoke test where it wait 1 minutes and checks the workflow's status as activated
    "FEDERAL_BANK": 1m
  VendorConfig:
    FederalConfig:
      MandateCreationConfig:
        RedirectionUrl: "https://fedmnduat.federalbank.co.in:4012/e-mandate/merchant/"
        ResponseUrl: "https://epifi-redirection.s3.ap-south-1.amazonaws.com/redirected-page.html?redirectURL=https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu?code=dummyCode&redirectLatency=3000&redirectParamKey=redirectURL&contextText=Dummy_digilocker_page"
        ExitUrl: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"
        RedirectionExpiry: "3m"
        EnableRsaEncryption: true
      MandateExecutionConfig:
        # as per compliance a returned transaction can be represented for execution only after 3 days since last return
        MinWaitDurationForRepresentingReturnedTxn: "5m"
EnachFundTransferOrderUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-enach-fund-transfer-order-update-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Minute"

FetchAndCreateFailedEnachTransactionSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-recurringpayment-failed-enach-transaction-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 3
      TimeUnit: "Minute"

PgProgramToAuthSecretMap:
  "RAZORPAY:EPIFI_TECH:paymentinstrument-creditcard-federal-pool-account-2":
    AuthParam: "qa/vendorgateway/razorpay-federal-secured-cards-api-key"
  "RAZORPAY:LIQUILOANS_PL:PILEcFKdcmQFWa8BfipSET/Q230915==":
    AuthParam: "qa/vendorgateway/razorpay-liquiloans-loans-api-key"
  "RAZORPAY:STOCK_GUARDIAN_TSP:paymentinstrument-stockguardian_MbZRPgHhafW":
    AuthParam: "qa/vendorgateway/razorpay-stock-guardian-loans-api-key"

# Minimum duration to wait after txn creation before vendor status check for UPI mandates.
# In QA we are keeping this low to not affect automation
UPIMandateExecutionVendorCallMinDelay: 3s
