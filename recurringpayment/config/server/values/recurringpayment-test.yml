Application:
  Environment: "test"
  Name: "recurringpayment"

Secrets:
  Ids:
    EnachSecrets: "{\"AppId\": \"APP_ID\", \"HashKey\": \"HASH_KEY\", \"UtilityCode\": \"UTILITY_CODE\"}"
    EnachFederalPublicKey: ""

# Recurring Payment service is actually initialized on the port defined in order-<env>.yml
# These properties are kept here from forward compatibility POV when we may want Recurring Payment service to be running on a
# different port in the order server
Server:
  Port: 8091
  HealthCheckPort: 9892

# Recurring Payment service uses DB connection initialized by order-<env>.yml
# These properties are kept from forward compatibility POV when we may want to have DB separate connection
# for Recurring Payment service
EpifiDb:
  AppName: "recurringpayment"
  DbType: "CRDB"
  StatementTimeout: 5m
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "epifi_test"
  EnableDebug: false
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

RecurringPaymentDb:
  Host: "localhost"
  Port: 5432
  Name: "recurring_payment_test"
  DbType: "PGDB"
  SSLMode: "disable"
  AppName: "recurring_payment"
  SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

FeatureFlags:
  EnableRecurringPaymentCreationViaCelestial: true
  EnableRecurringPaymentExecutionWithoutAuthViaCelestial: true
  EnableRecurringPaymentExecutionWithAuthViaCelestial: true
  EnableRecurringPaymentModificationViaCelestial: true
  EnableRecurringPaymentRevokeViaCelestial: true
  EnableRecurringPaymentPauseUnpauseViaCelestial: true

SignalWorkflowPublisher:
  QueueName: "celestial-signal-workflow-queue"

UPIEnquiryPublisher:
  QueueName: "payment-upi-enquiry-queue"

InPaymentOrderUpdatePublisher:
  QueueName: "order-in-payment-order-update-queue"

FetchAndCreateOffAppRecurringPaymentPublisher:
  QueueName: "fetch-recurringpayemnt-from-vendor-queue"

FetchAndCreateFailedEnachTransactionPublisher:
  QueueName: "recurringpayment-failed-enach-transaction-queue"

Profiling:
  StackDriverProfiling:
    ProjectId: "test"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

NonTpapPspHandles: ["fede"]

RecurringPaymentCreationAuthVendorCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "recurringpayment-creation-auth-vendor-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Minute"

FetchAndCreateOffAppRecurringPaymentSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "fetch-recurringpayemnt-from-vendor-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 20
      TimeUnit: "Minute"

OffAppRecurringPaymentExecutionSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "recurringpayemnt-off-app-execution-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 20
      TimeUnit: "Minute"


# P.S: The following config is used to run GetActivationCooldown tests for enach service
# EDIT WITH CAUTION
EnachServiceConfig:
  VendorToActivationCooldownMap:
    "FEDERAL_BANK": 5m
  VendorConfig:
    FederalConfig:
      MandateCreationConfig:
        RedirectionUrl: "https://fedmnduat.federalbank.co.in:4012/e-mandate/merchant/"
        ResponseUrl: "http://localhost:9098/openbanking/recurringpayment/enach/registration/federal"
        ExitUrl: "http://localhost:9098/openbanking/recurringpayment/enach/registration/federal"
        RedirectionExpiry: "3m"
        EnableRsaEncryption: true
      MandateExecutionConfig:
        # as per compliance a returned transaction can be represented for execution only after 3 days since last return
        MinWaitDurationForRepresentingReturnedTxn: "5m"

EnachFundTransferOrderUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "enach-fund-transfer-order-update-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Minute"

FetchAndCreateFailedEnachTransactionSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "recurringpayment-failed-enach-transaction-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 3
      TimeUnit: "Minute"

RecurringPaymentNotificationParams:
  #  Overriding enach execution failure payer config to test gracefully handling when notification params not configured.
  EnachExecutionFailurePayer:
    Title: ""
    Body: ""
    IconAttr:
      IconURL: ""
    NotificationExpiry: "1h"
    NotificationType: ""

PgProgramToAuthSecretMap:
  "RAZORPAY:EPIFI_TECH:paymentinstrument-creditcard-federal-pool-account-1":
    AuthParam: "{\"KeyId\":\"rzp_test_J8bVSzs01Aaq7V\",\"KeySecret\":\"fjdeWi73ZX71TyZOjYzeB0jh\"}"
  "RAZORPAY:LIQUILOANS_PL:PILEcFKdcmQFWa8BfipSET/Q230915==":
    AuthParam: "{\"KeyId\":\"rzp_test_J8bVSzs01Aaq7V\",\"KeySecret\":\"fjdeWi73ZX71TyZOjYzeB0jh\"}"

PgParams:
  EnableOffAppMandateCreation: true
