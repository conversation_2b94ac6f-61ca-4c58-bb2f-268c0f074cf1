Application:
  Environment: "staging"
  Name: "recurringpayment"

Secrets:
  Ids:
    EnachSecrets: "staging/recurringpayment/enach/secrets"
    EnachFederalPublicKey: "staging/recurringpayment/enach/federal/public-key"

# Recurring Payment service is actually initialized on the port defined in order-<env>.yml
# These properties are kept here from forward compatibility POV when we may want Recurring Payment service to be running on a
# different port in the order server
Server:
  Port: 8091
  HealthCheckPort: 9999

# Recurring Payment service uses DB connection initialized by order-<env>.yml
# These properties are kept from forward compatibility POV when we may want to have DB separate connection
# for Recurring Payment service
EpifiDb:
  AppName: "recurringpayment"
  DbType: "CRDB"
  StatementTimeout: 1s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "staging/cockroach/ca.crt"
  SSLClientCert: "staging/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "staging/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

RecurringPaymentDb:
  Name: "recurring_payment"
  DbType: "PGDB"
  DbServerAlias: "PLUTUS_RDS"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "recurring_payment"
  SecretName: "staging/rds/epifiplutus/recurring_payment_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Tracing:
  Enable: true

FeatureFlags:
  EnableRecurringPaymentCreationViaCelestial: true
  EnableRecurringPaymentExecutionWithoutAuthViaCelestial: true
  EnableRecurringPaymentExecutionWithAuthViaCelestial: true
  EnableRecurringPaymentModificationViaCelestial: true
  EnableRecurringPaymentRevokeViaCelestial: true

SIExecutionParams:
  PaymentProtocol: 3

SignalWorkflowPublisher:
  QueueName: "staging-celestial-signal-workflow-queue"

UPIEnquiryPublisher:
  QueueName: "staging-payment-upi-enquiry-queue"

InPaymentOrderUpdatePublisher:
  QueueName: "staging-order-in-payment-order-update-queue"

FetchAndCreateOffAppRecurringPaymentPublisher:
  QueueName: "staging-fetch-recurringpayemnt-from-vendor-queue"

FetchAndCreateFailedEnachTransactionPublisher:
  QueueName: "staging-recurringpayment-failed-enach-transaction-queue"

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

NonTpapPspHandles: ["fede"]

RecurringPaymentCreationAuthVendorCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-recurringpayment-creation-auth-vendor-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Minute"

EnachServiceConfig:
  VendorToActivationCooldownMap:
    "FEDERAL_BANK": 5m
  VendorConfig:
    FederalConfig:
      MandateCreationConfig:
        RedirectionUrl: "https://fedmnduat.federalbank.co.in:4012/e-mandate/merchant/"
        ResponseUrl: "https://epifi-redirection.s3.ap-south-1.amazonaws.com/redirected-page.html?redirectURL=https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu?code=dummyCode&redirectLatency=3000&redirectParamKey=redirectURL&contextText=Dummy_digilocker_page"
        ExitUrl: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"
        RedirectionExpiry: "3m"
        EnableRsaEncryption: true
      MandateExecutionConfig:
        # as per compliance a returned transaction can be represented for execution only after 3 days since last return
        MinWaitDurationForRepresentingReturnedTxn: "5m"
EnachFundTransferOrderUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-enach-fund-transfer-order-update-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Minute"


FetchAndCreateOffAppRecurringPaymentSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-fetch-recurringpayemnt-from-vendor-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 20
      TimeUnit: "Minute"

OffAppRecurringPaymentExecutionSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-recurringpayemnt-off-app-execution-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 20
      TimeUnit: "Minute"

FetchAndCreateFailedEnachTransactionSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-recurringpayment-failed-enach-transaction-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 3
      TimeUnit: "Minute"

OrderCacheConfig:
  IsCachingEnabled: true
  CacheTTl: "48h" # 2 days
  RedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 14
      ClientName: order

PgProgramToAuthSecretMap:
  "RAZORPAY:EPIFI_TECH:paymentinstrument-creditcard-federal-pool-account-2":
    AuthParam: "staging/vendorgateway/razorpay-federal-secured-cards-api-key"
  "RAZORPAY:LIQUILOANS_PL:PILEcFKdcmQFWa8BfipSET/Q230915==":
    AuthParam: "staging/vendorgateway/razorpay-liquiloans-loans-api-key"
  "RAZORPAY:STOCK_GUARDIAN_TSP:paymentinstrument-creditcard-federal-pool-account-1":
    AuthParam: "staging/vendorgateway/razorpay-stock-guardian-loans-api-key"

# Minimum duration to wait after txn creation before vendor status check for UPI mandates.
UPIMandateExecutionVendorCallMinDelay: 3s
