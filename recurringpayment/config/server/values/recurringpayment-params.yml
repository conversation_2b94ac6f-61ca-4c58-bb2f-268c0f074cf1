Aws:
  Region: "ap-south-1"

Flags:
  TrimDebugMessageFromStatus: true

AuthenticationTimeLimitForAction:
  STANDING_INSTRUCTION:
    PAYER:
      MODIFY: 30s
      REVOKE: 30s
  UPI_MANDATES:
    PAYEE:
      MODIFY: 30s
      REVOKE: 30s
      PAUSE: 30s
      UNPAUSE: 30s
    PAYER:
      MODIFY: 30s
      REVOKE: 30s
      PAUSE: 30s
      UNPAUSE: 30s

UpiMandateUrn:
  CollectMandatePrefix: "upi://collect?"

UpiMandateNotifDateFormat: "January 2, 2006"

UpiMandateValidationParams:
  AllowedHoursForModification: 24
  MaximumAllowedDurationBetweenStartAndEndDate: 90

RecurringPaymentNotificationParams:

  MandateReceivedPayer:
    Title: "UPI auto-payment request ⚡️"
    Body: "%s wants to collect %s for UPI-AutoPay. Tap to open Fi & grant this!"
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"

  MandateApprovedPayer:
    Title: "Auto-payment authorised 🥳"
    Body: "Every %s, %s will receive %s from you."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"

  MandateDeclinedPayer:
    Title: "You've declined an auto-payment 🛑"
    Body: "%s will not receive your %s payment of %s"
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"

  MandateCreationPayer:
    Title: "Woohoo! Auto-payment registered 🎯"
    Body: "Every %s, %s will receive a UPI auto-payment of %s from you."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"

  MandateExecutionSuccessPayer:
    Title: "Auto-payment successful ✅"
    Body: "We've sent %s to %s. The UPI auto-payment occurred on %s."
    IconAttr:
        IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"

  MandateExecutionFailedPayer:
    Title: "Uh-oh! Your UPI auto-payment failed 🚧"
    Body: "An auto-payment of %s to %s did not go through. Tap here to investigate!"
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"

  MandateRevokedPayer:
    Title: "Auto-payment cancelled 🚨"
    Body: "Heads up! %s will no longer receive %s from you for UPI AutoPay."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"

  MandateModifiedPayer:
    Title: "Auto-payment details revised 🔧"
    Body: "As instructed, we've modified your UPI AutoPay towards %s for %s"
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"

  MandateAuthorizedPayer:
    Title: "Your authorisation is required 📝"
    Body: "%s has sent an AutoPay request for %s. Tap to open Fi & authorise it."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"

  MandateAcceptancePayee:
    Title: "Someone's trying to send you money 💸"
    Body: "%s wants to send you %s via UPI auto-payment. Tap to open Fi & accept it."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"

  MandatePausedPayer:
    Title: "UPI auto-payments paused ⏳"
    Body: "All your current UPI auto-payments are taking a breather. Want to reactivate them? Tap here!"
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"

  MandateUnpausedPayer:
    Title: "Auto-payments successfully resumed 🚀"
    Body: "We heard you loud & clear, %s. All your UPI auto-payments are back on track!"
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  SICreationPayer:
    Title: "We're ready to roll! 🏄🏻‍♀️"
    Body: "Your auto-payment rule has been created. %s will be sent to %s. It will occur %s."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  SIDeclinedPayer:
    Title: "⚠️ Something went wrong.️"
    Body: "Your instructions to send %s to %s could not be created. Try again in some time?"
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  SIExecutionSuccessPayer:
    Title: "3, 2, 1... Done! 💥️"
    Body: "Based on your instructions, we just successfully transferred %s to %s."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  SIExecutionFailedPayer:
    Title: "Uh-oh! We ran into a problem ⚙️"
    Body: "Your instructions to send %s to %s couldn't be executed. Tap to know more."
    IconAttr:
      IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  EnachCreationSuccessPayer:
    Title: "We're ready to roll! 🏄🏻‍♀️"
    Body: "Your auto-payment rule has been created. RECURRING_PAYMENT_AMOUNT will be sent to BENEFICIARY_NAME. It will occur every RECURRING_PAYMENT_FREQUENCY"
    IconAttr:
      IconURL: ""
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  EnachCreationFailurePayer:
    Title: "⚠️ Something went wrong.️"
    Body: "Your instructions to create enach of RECURRING_PAYMENT_AMOUNT to BENEFICIARY_NAME could not be created. Try again in some time"
    IconAttr:
      IconURL: ""
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  EnachExecutionSuccessPayer:
    Title: "3, 2, 1... Done! 💥️"
    Body: "Based on your instructions, we just successfully transferred RECURRING_PAYMENT_AMOUNT to BENEFICIARY_NAME"
    IconAttr:
      IconURL: ""
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"
  EnachExecutionFailurePayer:
    Title: "⚠️ Something went wrong.️"
    Body: "Your instructions to send RECURRING_PAYMENT_AMOUNT to BENEFICIARY_NAME couldn't be executed. Tap to know more."
    IconAttr:
      IconURL: ""
    NotificationExpiry: "1h"
    NotificationType: "SystemTray"

SIExecutionParams:
  PaymentProtocol: 2 # NEFT

Tracing:
  Enable: false

# Mapping for frequency we use in recurring payment notification
RecurringPaymentFrequencyMapping:
  ONE_TIME : "one-time"
  DAILY : "daily"
  WEEKLY : "weekly"
  FORTNIGHTLY : "fortnightly"
  MONTHLY : "monthly"
  BI_MONTHLY : "bi-monthly"
  QUARTERLY : "quarterly"
  HALF_YEARLY : "half yearly"
  YEARLY : "yearly"
  AS_PRESENTED : "as requested"

PaymentEnquiryParams:
  NotFoundMaxRetryDurationVendorMap:
    "FEDERAL_BANK":
      IntraBank: "24h"
      UPI: "24h"
      NEFT: "24h"
      RTGS: "24h"
      IMPS: "24h"
  InProgressToSuccessMap: # time duration and error codes for in progress response status will be move to success
    "FEDERAL_BANK":
      FiErrorCodes:
        - "FI304"
      PaymentProtocolToDurationMap:
        "NEFT": "120m"
        "RTGS": "120m"
      PaymentProtocolToDeemedEnquiryDurationMap:
        UPI:
          P2P: 6m
          P2M: 6m
        IMPS:
          P2P: 6m
          P2M: 6m

RecurringPaymentCreationParams:
  AuthorisationTimeLimit : 10m

RecurringPaymentExecutionParams:
  AuthorisationTimeLimit : 10m
  ProcessRecurringPaymentExecutionLock: "PROCESS_RECURRING_PAYMENT_EXECUTION_LOCK"
    # lease duration to be for new data sync lock
  ProcessRecurringPaymentExecutionLockLeaseDuration: "2s"

RecurringPaymentModificationParams:
  AuthorisationTimeLimit : 10m

RecurringPaymentRevokeParams:
  AuthorisationTimeLimit : 10m

RecurringPaymentPauseUnpauseParams:
  AuthorisationTimeLimit : 10m

CelestialParams:
  WorkflowTimeoutDuration: 10

FeatureFlags:
  EnableRecurringPaymentExecutionWithoutAuthViaCelestial: true

RecurringPaymentRetryErrors:
  "NEFT":
    - "F025"

NonPauseableMccsForNonRevokeableMandates:
  - 7322

MccWarningMessageMap:
  - "7322":
      DisplayValue: "Transaction declined due to insufficiency of funds for this UPI Autopay is a punishable offence under Section 25 of the Payment & Settlement Act, 2007 (PSS Act)"
      BgColour: "#F4E7BF"
      FontColour: "#555555"
      FontStyle: "BODY_XS"

EnachServiceConfig:
  SupportedBankToDetailsMap:
    "INDIAN":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/india_bank_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "UNION":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/union_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "INDUSIND":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/indusind_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "SBM":
        BankIconUrl: ""
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "YES":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/yes_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "KOTAK":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/kotak_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "CANARA":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/canara_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "IDBI":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/idbi_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "CATHOLIC_SYRIAN":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/csb_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "UJJIVAN_SMALL_FINANCE":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/ujjivan_small_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "AIRTEL_PAYMENTS":
        BankIconUrl: ""
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "MAHARASHTRA":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/bank_of_maharashtra_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "FEDERAL":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/federal_bank_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "KARNATAKA":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/karnataka_bank_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING" ]
    "IDFC":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/idfc_first_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
# currently there is some issue in mandate setup with CITI, ref: https://epifi.slack.com/archives/C05JSV713DH/p1710758987023189
#    "CITI":
#        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/citi_264px.png"
#        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "RBL":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/rbl_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "PAYTM_PAYMENTS":
        BankIconUrl: "https://epifi-icons.pointz.in/amc_logos/Hdfc.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "PUNJAB_NATIONAL":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/punjab_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "ICICI":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/icici_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "SOUTH_INDIAN":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/south_india_bank_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "SBI":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/sbi_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "DBS_DIGI":
        BankIconUrl: ""
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "DEUTSCHE":
        BankIconUrl: ""
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "STANDARD_CHARTERED":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/standard_chartered_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "TAMILNAD_MERCANTILE":
        BankIconUrl: ""
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING" ]
    "JANA_SMALL_FINANCE":
        BankIconUrl: ""
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "AU_SMALL_FINANCE_BANK":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/au_small_finance_bank_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "EQUITAS_SMALL_FINANCE":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/equitas_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "HDFC":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/hdfc_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "AXIS":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/axis_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "BARODA":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/baroda_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING","REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "INDIAN_OVERSEAS":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/iob_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING"]
    "HSBC":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/hsbc_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING"]
    "BANDHAN":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/bandhan_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING" ]
    "ESAF_SMALL_FINANCE":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/esaf_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING", "REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "SURYODAY_SMALL_FINANCE":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/suryoday_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING", "REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "FINCARE_SMALL_FINANCE":
        BankIconUrl: ""
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "CAPITAL_SMALL_FINANCE":
        BankIconUrl: ""
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "PUNJAB_AND_SIND":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/punjab_and_sind_bank_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "UTKARSH_SMALL_FINANCE":
        BankIconUrl: "https://epifi-icons.pointz.in/bank/logo/utkarsh_264px.png"
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_DEBIT_CARD", "REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
    "NSDL_PAYMENTS":
        BankIconUrl: ""
        SupportedAuthModes: [ "REGISTRATION_AUTH_MODE_NET_BANKING", "REGISTRATION_AUTH_MODE_DEBIT_CARD" ]
  VendorToActivationCooldownMap:
    "FEDERAL_BANK": 5m
  VendorConfig:
    FederalConfig:
      MandateCreationConfig:
        EnableRsaEncryption: false


MaxAmountForMCCAndPurposeCode:
  6211-01:
    CurrencyCode: "INR"
    Units: 500000
    Nanos: 0

EnachCreationValidationConfig:
  MinRequiredDelayForEnachStartTimeSinceCreation: 10s
  MaxAllowedAmountInRs: 500000 # 50k rupees

PgParams:
  VendorOrderExpirationDuration:
    "RAZORPAY": "15m"
  RecurringPaymentTypeToExecutionLimit:
    "UPI_MANDATES": 15000
  # Maintain the mapping of recurringpayment method and the time within which the payment should be auto captured.
  # This is needed to override the timeout configured through razorpay dashboard, since for recurringpayment executions,
  # the payments take almost T + 1 days to be authorised.
  # https://razorpay.com/docs/payments/recurring-payments/emandate/faqs/#4-for-emandates-how-long-does-it-take
  RpExecutionPaymentAutoCaptureTimeout:
    "ENACH_MANDATES": "4320m" # 3 days
    # TODO(Sundeep): Revisit this when enabling UPI mandates.
    "UPI_MANDATES": "1440m" # 1 day
  # Currently setting off-app mandate creation to false since razorpay does not return the MRN in non-prod.
  # Will enable this in prod after validating that the MRN is returned in prod.
  EnableOffAppMandateCreation: false

MaxAmountForMandateExecutionWithoutAFA:
  CurrencyCode: "INR"
  Units: 15000
  Nanos: 0

#stores the max amount allowed for mcc for which Additional Factor Authentication is not required for mandate execution.
#key for the map is mcc and value is the max amount limit
#for eg. for mcc 5413 the max allowed amount is 1 lac
MaxAmountLimitForMccWithoutAFA:
  5413:
    CurrencyCode: "INR"
    Units: 100000
    Nanos: 0
  5960:
    CurrencyCode: "INR"
    Units: 100000
    Nanos: 0
  6012:
    CurrencyCode: "INR"
    Units: 100000
    Nanos: 0
  6211:
    CurrencyCode: "INR"
    Units: 100000
    Nanos: 0
  6300:
    CurrencyCode: "INR"
    Units: 100000
    Nanos: 0
  6381:
    CurrencyCode: "INR"
    Units: 100000
    Nanos: 0
  6399:
    CurrencyCode: "INR"
    Units: 100000
    Nanos: 0
  6529:
    CurrencyCode: "INR"
    Units: 100000
    Nanos: 0

EnableEntitySegregation: true
EnableEnachMandateEntitySegregation: true

RazorPayResponseCodesJson: "pkg/pay/pgerrorcodes/razorpayErrorResponseCodes.json"

EnachConsumerConfig:
  MaxAllowedDaysToFetchFailedTxns: 75
  # Limiting to the last 3 days for now — skipping older transactions to avoid clearing full backlog
  # [To be revisited: AllTxns page sorting (by timestamp) and in-app notification recency logic]
  # Note: Once these are resolved, we can increase the range back to ~65 days
  NumDaysToFetchFailedTxns: 3

FeatureReleaseConfig:
  FeatureConstraints:
    # Feature rollout configuration for failed enach transactions
    FEATURE_FAILED_ENACH_TRANSACTIONS:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100

# Minimum duration to wait after txn creation before vendor status check for UPI mandates.
UPIMandateExecutionVendorCallMinDelay: 90s
