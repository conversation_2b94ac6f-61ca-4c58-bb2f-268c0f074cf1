// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"fmt"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"

	time "time"

	money "google.golang.org/genproto/googleapis/type/money"

	questtypes "github.com/epifi/be-common/api/quest/types"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	roarray "github.com/epifi/be-common/pkg/data_structs/roarray"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	genconfig "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	pay "github.com/epifi/gamma/pkg/pay"
	commons "github.com/epifi/gamma/recurringpayment/config/commons"
	server "github.com/epifi/gamma/recurringpayment/config/server"
)

type Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// flag to indicate whether to enable resource provider based db instance provision or whether to
	// go with a static db instance. Disabling this flag doesn't mean that lookup won't happen from multi-db.
	// This is primarily during insertion only.
	// Assumption : Once the flag is turned on, it will not be turned off again. Since it will go through a
	// CUG, any occurring issues will be fixed within the CUG itself. Also, all the systems using this
	// have been made backward compatible. So the older prod entities will not see any issues. Possible consequences
	// can be that the data got written with entity segregation but fetched from the default
	// epifi db but since that scale will be low at the time of CUG, we would be able to fix it before going external
	_EnableEntitySegregation uint32
	// flag to indicate whether to enable entity segregation for ENACH mandate
	_EnableEnachMandateEntitySegregation uint32
	// Minimum duration to wait after txn creation before vendor status check for UPI mandates.
	_UPIMandateExecutionVendorCallMinDelay                int64
	_SIExecutionParams                                    *SIExecutionParams
	_FeatureFlags                                         *FeatureFlags
	_RecurringPaymentCreationAuthVendorCallbackSubscriber *gencfg.SqsSubscriber
	_FetchAndCreateOffAppRecurringPaymentSubscriber       *gencfg.SqsSubscriber
	_FetchAndCreateFailedEnachTransactionSubscriber       *gencfg.SqsSubscriber
	_FeatureReleaseConfig                                 *genconfig.FeatureReleaseConfig
	_OffAppRecurringPaymentExecutionSubscriber            *gencfg.SqsSubscriber
	_EnachFundTransferOrderUpdateSqsSubscriber            *gencfg.SqsSubscriber
	_EnachServiceConfig                                   *EnachServiceConfig
	_PgParams                                             *PgParams
	_EnachConsumerConfig                                  *EnachConsumerConfig
	_Application                                          *server.Application
	_Server                                               *server.Server
	_EpifiDb                                              *cfg.DB
	_RecurringPaymentDb                                   *cfg.DB
	_Secrets                                              *cfg.Secrets
	_AWS                                                  *cfg.AWS
	_Flags                                                *server.Flags
	_AuthenticationTimeLimitForAction                     *server.AuthenticationTimeLimitForAction
	_RecurringPaymentNotificationParams                   *server.RecurringPaymentNotificationParams
	_UpiMandateUrn                                        *server.UpiMandateUrn
	_UpiMandateValidationParams                           *server.UpiMandateValidationParams
	_UpiMandateNotifDateFormat                            string
	_Tracing                                              *cfg.Tracing
	_RecurringPaymentFrequencyMapping                     map[string]string
	_PaymentEnquiryParams                                 *pay.PaymentEnquiryParams
	_RecurringPaymentCreationParams                       *server.RecurringPaymentCreationParams
	_SignalWorkflowPublisher                              *cfg.SqsPublisher
	_RecurringPaymentExecutionParams                      *server.RecurringPaymentExecutionParams
	_RecurringPaymentModificationParams                   *server.RecurringPaymentModificationParams
	_RecurringPaymentRevokeParams                         *server.RecurringPaymentRevokeParams
	_RecurringPaymentRetryErrors                          map[string][]string
	_NonPauseableMccsForNonRevokeableMandates             []string
	_MccWarningMessageMap                                 map[string]*server.TextObject
	_MaxAmountForMCCAndPurposeCode                        map[string]*money.Money
	_RecurringPaymentPauseUnpauseParams                   *server.RecurringPaymentPauseUnpauseParams
	_InPaymentOrderUpdatePublisher                        *cfg.SqsPublisher
	_NonTpapPspHandles                                    []string
	_MaxCollectAmountLimitForNonVerifiedMerchants         *money.Money
	_FetchAndCreateFailedEnachTransactionPublisher        *cfg.SqsPublisher
	_FetchAndCreateOffAppRecurringPaymentPublisher        *cfg.SqsPublisher
	_EnachCreationValidationConfig                        *commons.EnachCreationValidationConfig
	_PgProgramToAuthSecretMap                             map[string]*server.PgProgramToAuthSecret
	_MaxAmountForMandateExecutionWithoutAFA               *money.Money
	_MaxAmountLimitForMccWithoutAFA                       map[string]*money.Money
	_RazorPayResponseCodesJson                            string
}

// flag to indicate whether to enable resource provider based db instance provision or whether to
// go with a static db instance. Disabling this flag doesn't mean that lookup won't happen from multi-db.
// This is primarily during insertion only.
// Assumption : Once the flag is turned on, it will not be turned off again. Since it will go through a
// CUG, any occurring issues will be fixed within the CUG itself. Also, all the systems using this
// have been made backward compatible. So the older prod entities will not see any issues. Possible consequences
// can be that the data got written with entity segregation but fetched from the default
// epifi db but since that scale will be low at the time of CUG, we would be able to fix it before going external
func (obj *Config) EnableEntitySegregation() bool {
	if atomic.LoadUint32(&obj._EnableEntitySegregation) == 0 {
		return false
	} else {
		return true
	}
}

// flag to indicate whether to enable entity segregation for ENACH mandate
func (obj *Config) EnableEnachMandateEntitySegregation() bool {
	if atomic.LoadUint32(&obj._EnableEnachMandateEntitySegregation) == 0 {
		return false
	} else {
		return true
	}
}

// Minimum duration to wait after txn creation before vendor status check for UPI mandates.
func (obj *Config) UPIMandateExecutionVendorCallMinDelay() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._UPIMandateExecutionVendorCallMinDelay))
}
func (obj *Config) SIExecutionParams() *SIExecutionParams {
	return obj._SIExecutionParams
}
func (obj *Config) FeatureFlags() *FeatureFlags {
	return obj._FeatureFlags
}
func (obj *Config) RecurringPaymentCreationAuthVendorCallbackSubscriber() *gencfg.SqsSubscriber {
	return obj._RecurringPaymentCreationAuthVendorCallbackSubscriber
}
func (obj *Config) FetchAndCreateOffAppRecurringPaymentSubscriber() *gencfg.SqsSubscriber {
	return obj._FetchAndCreateOffAppRecurringPaymentSubscriber
}
func (obj *Config) FetchAndCreateFailedEnachTransactionSubscriber() *gencfg.SqsSubscriber {
	return obj._FetchAndCreateFailedEnachTransactionSubscriber
}
func (obj *Config) FeatureReleaseConfig() *genconfig.FeatureReleaseConfig {
	return obj._FeatureReleaseConfig
}
func (obj *Config) OffAppRecurringPaymentExecutionSubscriber() *gencfg.SqsSubscriber {
	return obj._OffAppRecurringPaymentExecutionSubscriber
}
func (obj *Config) EnachFundTransferOrderUpdateSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._EnachFundTransferOrderUpdateSqsSubscriber
}
func (obj *Config) EnachServiceConfig() *EnachServiceConfig {
	return obj._EnachServiceConfig
}
func (obj *Config) PgParams() *PgParams {
	return obj._PgParams
}
func (obj *Config) EnachConsumerConfig() *EnachConsumerConfig {
	return obj._EnachConsumerConfig
}
func (obj *Config) Application() *server.Application {
	return obj._Application
}
func (obj *Config) Server() *server.Server {
	return obj._Server
}
func (obj *Config) EpifiDb() *cfg.DB {
	return obj._EpifiDb
}
func (obj *Config) RecurringPaymentDb() *cfg.DB {
	return obj._RecurringPaymentDb
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}
func (obj *Config) AWS() *cfg.AWS {
	return obj._AWS
}
func (obj *Config) Flags() *server.Flags {
	return obj._Flags
}
func (obj *Config) AuthenticationTimeLimitForAction() *server.AuthenticationTimeLimitForAction {
	return obj._AuthenticationTimeLimitForAction
}
func (obj *Config) RecurringPaymentNotificationParams() *server.RecurringPaymentNotificationParams {
	return obj._RecurringPaymentNotificationParams
}
func (obj *Config) UpiMandateUrn() *server.UpiMandateUrn {
	return obj._UpiMandateUrn
}
func (obj *Config) UpiMandateValidationParams() *server.UpiMandateValidationParams {
	return obj._UpiMandateValidationParams
}
func (obj *Config) UpiMandateNotifDateFormat() string {
	return obj._UpiMandateNotifDateFormat
}
func (obj *Config) Tracing() *cfg.Tracing {
	return obj._Tracing
}
func (obj *Config) RecurringPaymentFrequencyMapping() map[string]string {
	return obj._RecurringPaymentFrequencyMapping
}
func (obj *Config) PaymentEnquiryParams() *pay.PaymentEnquiryParams {
	return obj._PaymentEnquiryParams
}
func (obj *Config) RecurringPaymentCreationParams() *server.RecurringPaymentCreationParams {
	return obj._RecurringPaymentCreationParams
}
func (obj *Config) SignalWorkflowPublisher() *cfg.SqsPublisher {
	return obj._SignalWorkflowPublisher
}
func (obj *Config) RecurringPaymentExecutionParams() *server.RecurringPaymentExecutionParams {
	return obj._RecurringPaymentExecutionParams
}
func (obj *Config) RecurringPaymentModificationParams() *server.RecurringPaymentModificationParams {
	return obj._RecurringPaymentModificationParams
}
func (obj *Config) RecurringPaymentRevokeParams() *server.RecurringPaymentRevokeParams {
	return obj._RecurringPaymentRevokeParams
}
func (obj *Config) RecurringPaymentRetryErrors() map[string][]string {
	return obj._RecurringPaymentRetryErrors
}
func (obj *Config) NonPauseableMccsForNonRevokeableMandates() []string {
	return obj._NonPauseableMccsForNonRevokeableMandates
}
func (obj *Config) MccWarningMessageMap() map[string]*server.TextObject {
	return obj._MccWarningMessageMap
}
func (obj *Config) MaxAmountForMCCAndPurposeCode() map[string]*money.Money {
	return obj._MaxAmountForMCCAndPurposeCode
}
func (obj *Config) RecurringPaymentPauseUnpauseParams() *server.RecurringPaymentPauseUnpauseParams {
	return obj._RecurringPaymentPauseUnpauseParams
}
func (obj *Config) InPaymentOrderUpdatePublisher() *cfg.SqsPublisher {
	return obj._InPaymentOrderUpdatePublisher
}
func (obj *Config) NonTpapPspHandles() []string {
	return obj._NonTpapPspHandles
}
func (obj *Config) MaxCollectAmountLimitForNonVerifiedMerchants() *money.Money {
	return obj._MaxCollectAmountLimitForNonVerifiedMerchants
}
func (obj *Config) FetchAndCreateFailedEnachTransactionPublisher() *cfg.SqsPublisher {
	return obj._FetchAndCreateFailedEnachTransactionPublisher
}
func (obj *Config) FetchAndCreateOffAppRecurringPaymentPublisher() *cfg.SqsPublisher {
	return obj._FetchAndCreateOffAppRecurringPaymentPublisher
}
func (obj *Config) EnachCreationValidationConfig() *commons.EnachCreationValidationConfig {
	return obj._EnachCreationValidationConfig
}
func (obj *Config) PgProgramToAuthSecretMap() map[string]*server.PgProgramToAuthSecret {
	return obj._PgProgramToAuthSecretMap
}
func (obj *Config) MaxAmountForMandateExecutionWithoutAFA() *money.Money {
	return obj._MaxAmountForMandateExecutionWithoutAFA
}
func (obj *Config) MaxAmountLimitForMccWithoutAFA() map[string]*money.Money {
	return obj._MaxAmountLimitForMccWithoutAFA
}
func (obj *Config) RazorPayResponseCodesJson() string {
	return obj._RazorPayResponseCodesJson
}

type SIExecutionParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Payment protocol via which standing instruction execution will be done
	_PaymentProtocol int32
}

// Payment protocol via which standing instruction execution will be done
func (obj *SIExecutionParams) PaymentProtocol() int32 {
	return int32(atomic.LoadInt32(&obj._PaymentProtocol))
}

type FeatureFlags struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_EnableRecurringPaymentCreationViaCelestial             uint32
	_EnableRecurringPaymentExecutionWithoutAuthViaCelestial uint32
	_EnableRecurringPaymentExecutionWithAuthViaCelestial    uint32
	_EnableRecurringPaymentModificationViaCelestial         uint32
	_EnableRecurringPaymentRevokeViaCelestial               uint32
	_EnableRecurringPaymentPauseUnpauseViaCelestial         uint32
}

func (obj *FeatureFlags) EnableRecurringPaymentCreationViaCelestial() bool {
	if atomic.LoadUint32(&obj._EnableRecurringPaymentCreationViaCelestial) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *FeatureFlags) EnableRecurringPaymentExecutionWithoutAuthViaCelestial() bool {
	if atomic.LoadUint32(&obj._EnableRecurringPaymentExecutionWithoutAuthViaCelestial) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *FeatureFlags) EnableRecurringPaymentExecutionWithAuthViaCelestial() bool {
	if atomic.LoadUint32(&obj._EnableRecurringPaymentExecutionWithAuthViaCelestial) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *FeatureFlags) EnableRecurringPaymentModificationViaCelestial() bool {
	if atomic.LoadUint32(&obj._EnableRecurringPaymentModificationViaCelestial) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *FeatureFlags) EnableRecurringPaymentRevokeViaCelestial() bool {
	if atomic.LoadUint32(&obj._EnableRecurringPaymentRevokeViaCelestial) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *FeatureFlags) EnableRecurringPaymentPauseUnpauseViaCelestial() bool {
	if atomic.LoadUint32(&obj._EnableRecurringPaymentPauseUnpauseViaCelestial) == 0 {
		return false
	} else {
		return true
	}
}

type EnachServiceConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// SupportedBankToDetailsMap stores details of banks which support enach mandates.
	_SupportedBankToDetailsMap *syncmap.Map[string, *EnachSupportedBankDetails]
	// VendorToActivationCooldownMap stores the vendor specific cool off details for activation once mandate created is authorized by the payer.
	_VendorToActivationCooldownMap *syncmap.Map[string, time.Duration]
	_VendorConfig                  *EnachVendorConfig
}

// SupportedBankToDetailsMap stores details of banks which support enach mandates.
func (obj *EnachServiceConfig) SupportedBankToDetailsMap() *syncmap.Map[string, *EnachSupportedBankDetails] {
	return obj._SupportedBankToDetailsMap
}

// VendorToActivationCooldownMap stores the vendor specific cool off details for activation once mandate created is authorized by the payer.
func (obj *EnachServiceConfig) VendorToActivationCooldownMap() *syncmap.Map[string, time.Duration] {
	return obj._VendorToActivationCooldownMap
}
func (obj *EnachServiceConfig) VendorConfig() *EnachVendorConfig {
	return obj._VendorConfig
}

type EnachSupportedBankDetails struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// SupportedAuthModes denotes the auth modes like NET_BANKING, DEBIT_CARD authentication etc through which payer can authorize enach mandate creation.
	_SupportedAuthModes      roarray.ROArray[string]
	_SupportedAuthModesMutex *sync.RWMutex
	_BankIconUrl             string
	_BankIconUrlMutex        *sync.RWMutex
}

// SupportedAuthModes denotes the auth modes like NET_BANKING, DEBIT_CARD authentication etc through which payer can authorize enach mandate creation.
func (obj *EnachSupportedBankDetails) SupportedAuthModes() roarray.ROArray[string] {
	obj._SupportedAuthModesMutex.RLock()
	defer obj._SupportedAuthModesMutex.RUnlock()
	return obj._SupportedAuthModes
}
func (obj *EnachSupportedBankDetails) BankIconUrl() string {
	obj._BankIconUrlMutex.RLock()
	defer obj._BankIconUrlMutex.RUnlock()
	return obj._BankIconUrl
}

type EnachVendorConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_FederalConfig *EnachFederalConfig
}

func (obj *EnachVendorConfig) FederalConfig() *EnachFederalConfig {
	return obj._FederalConfig
}

type EnachFederalConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MandateCreationConfig  *EnachMandateCreationFederalConfig
	_MandateExecutionConfig *EnachMandateExecutionFederalConfig
}

func (obj *EnachFederalConfig) MandateCreationConfig() *EnachMandateCreationFederalConfig {
	return obj._MandateCreationConfig
}
func (obj *EnachFederalConfig) MandateExecutionConfig() *EnachMandateExecutionFederalConfig {
	return obj._MandateExecutionConfig
}

type EnachMandateCreationFederalConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// EnableRsaEncryption denotes whether rsa encryption for mandate creation payload is enabled or not.
	_EnableRsaEncryption uint32
	// RedirectionExpiry denotes the time until which the user is allowed to be the on the federal mandate creation webapp redirection flow,
	// once the expiry time is reached, the app should force exit the web app..
	_RedirectionExpiry int64
	// RedirectionUrl denotes the federal web app url where used should be redirected to for mandate creation
	_RedirectionUrl      string
	_RedirectionUrlMutex *sync.RWMutex
	// ResponseUrl denotes the url on which callback response for mandate creation is posted by federal post mandate creation on federal webapp.
	_ResponseUrl      string
	_ResponseUrlMutex *sync.RWMutex
	// ExitUrl is used to support exiting the federal mandate creation web app once the mandate creation is completed, federal web app flow redirects
	// to this url once mandate creation web app flow is completed.
	_ExitUrl      string
	_ExitUrlMutex *sync.RWMutex
}

// EnableRsaEncryption denotes whether rsa encryption for mandate creation payload is enabled or not.
func (obj *EnachMandateCreationFederalConfig) EnableRsaEncryption() bool {
	if atomic.LoadUint32(&obj._EnableRsaEncryption) == 0 {
		return false
	} else {
		return true
	}
}

// RedirectionExpiry denotes the time until which the user is allowed to be the on the federal mandate creation webapp redirection flow,
// once the expiry time is reached, the app should force exit the web app..
func (obj *EnachMandateCreationFederalConfig) RedirectionExpiry() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._RedirectionExpiry))
}

// RedirectionUrl denotes the federal web app url where used should be redirected to for mandate creation
func (obj *EnachMandateCreationFederalConfig) RedirectionUrl() string {
	obj._RedirectionUrlMutex.RLock()
	defer obj._RedirectionUrlMutex.RUnlock()
	return obj._RedirectionUrl
}

// ResponseUrl denotes the url on which callback response for mandate creation is posted by federal post mandate creation on federal webapp.
func (obj *EnachMandateCreationFederalConfig) ResponseUrl() string {
	obj._ResponseUrlMutex.RLock()
	defer obj._ResponseUrlMutex.RUnlock()
	return obj._ResponseUrl
}

// ExitUrl is used to support exiting the federal mandate creation web app once the mandate creation is completed, federal web app flow redirects
// to this url once mandate creation web app flow is completed.
func (obj *EnachMandateCreationFederalConfig) ExitUrl() string {
	obj._ExitUrlMutex.RLock()
	defer obj._ExitUrlMutex.RUnlock()
	return obj._ExitUrl
}

type EnachMandateExecutionFederalConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Minimum wait duration for a returned transaction
	// As for now the minimum wait for a returned transaction is 72 hours
	_MinWaitDurationForRepresentingReturnedTxn int64
}

// Minimum wait duration for a returned transaction
// As for now the minimum wait for a returned transaction is 72 hours
func (obj *EnachMandateExecutionFederalConfig) MinWaitDurationForRepresentingReturnedTxn() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._MinWaitDurationForRepresentingReturnedTxn))
}

type PgParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Flag to enable/disable the Enach Mandate creation in recurring_payment DB for payment gateway mandates.
	_EnableOffAppMandateCreation uint32
	// RpExecutionPaymentAutoCaptureTimeout stores the duration within which the recurring payment
	// execution has to be auto captured. If the payment is not captured within this time period then, it can be captured
	// manually till the manual capture timeout expires, as supported by the payment gateway vendor.
	_RpExecutionPaymentAutoCaptureTimeout *syncmap.Map[string, time.Duration]
	_RecurringPaymentTypeToExecutionLimit map[string]float64
	_VendorOrderExpirationDuration        map[string]time.Duration
}

// Flag to enable/disable the Enach Mandate creation in recurring_payment DB for payment gateway mandates.
func (obj *PgParams) EnableOffAppMandateCreation() bool {
	if atomic.LoadUint32(&obj._EnableOffAppMandateCreation) == 0 {
		return false
	} else {
		return true
	}
}

// RpExecutionPaymentAutoCaptureTimeout stores the duration within which the recurring payment
// execution has to be auto captured. If the payment is not captured within this time period then, it can be captured
// manually till the manual capture timeout expires, as supported by the payment gateway vendor.
func (obj *PgParams) RpExecutionPaymentAutoCaptureTimeout() *syncmap.Map[string, time.Duration] {
	return obj._RpExecutionPaymentAutoCaptureTimeout
}
func (obj *PgParams) RecurringPaymentTypeToExecutionLimit() map[string]float64 {
	return obj._RecurringPaymentTypeToExecutionLimit
}
func (obj *PgParams) VendorOrderExpirationDuration() map[string]time.Duration {
	return obj._VendorOrderExpirationDuration
}

type EnachConsumerConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Maximum number of days for which FetchAndCreateFailedEnachTransactions
	// consumer method may fetch the transactions.
	_MaxAllowedDaysToFetchFailedTxns int32
	// Number of days for which to sync failed ENACH transactions
	_NumDaysToFetchFailedTxns int32
}

// Maximum number of days for which FetchAndCreateFailedEnachTransactions
// consumer method may fetch the transactions.
func (obj *EnachConsumerConfig) MaxAllowedDaysToFetchFailedTxns() int64 {
	return int64(atomic.LoadInt32(&obj._MaxAllowedDaysToFetchFailedTxns))
}

// Number of days for which to sync failed ENACH transactions
func (obj *EnachConsumerConfig) NumDaysToFetchFailedTxns() int64 {
	return int64(atomic.LoadInt32(&obj._NumDaysToFetchFailedTxns))
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enableentitysegregation"] = _obj.SetEnableEntitySegregation
	_setters["enableenachmandateentitysegregation"] = _obj.SetEnableEnachMandateEntitySegregation
	_setters["upimandateexecutionvendorcallmindelay"] = _obj.SetUPIMandateExecutionVendorCallMinDelay
	_SIExecutionParams, _fieldSetters := NewSIExecutionParams()
	_obj._SIExecutionParams = _SIExecutionParams
	helper.AddFieldSetters("siexecutionparams", _fieldSetters, _setters)
	_FeatureFlags, _fieldSetters := NewFeatureFlags()
	_obj._FeatureFlags = _FeatureFlags
	helper.AddFieldSetters("featureflags", _fieldSetters, _setters)
	_RecurringPaymentCreationAuthVendorCallbackSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RecurringPaymentCreationAuthVendorCallbackSubscriber = _RecurringPaymentCreationAuthVendorCallbackSubscriber
	helper.AddFieldSetters("recurringpaymentcreationauthvendorcallbacksubscriber", _fieldSetters, _setters)
	_FetchAndCreateOffAppRecurringPaymentSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FetchAndCreateOffAppRecurringPaymentSubscriber = _FetchAndCreateOffAppRecurringPaymentSubscriber
	helper.AddFieldSetters("fetchandcreateoffapprecurringpaymentsubscriber", _fieldSetters, _setters)
	_FetchAndCreateFailedEnachTransactionSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FetchAndCreateFailedEnachTransactionSubscriber = _FetchAndCreateFailedEnachTransactionSubscriber
	helper.AddFieldSetters("fetchandcreatefailedenachtransactionsubscriber", _fieldSetters, _setters)
	_FeatureReleaseConfig, _fieldSetters := genconfig.NewFeatureReleaseConfig()
	_obj._FeatureReleaseConfig = _FeatureReleaseConfig
	helper.AddFieldSetters("featurereleaseconfig", _fieldSetters, _setters)
	_OffAppRecurringPaymentExecutionSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OffAppRecurringPaymentExecutionSubscriber = _OffAppRecurringPaymentExecutionSubscriber
	helper.AddFieldSetters("offapprecurringpaymentexecutionsubscriber", _fieldSetters, _setters)
	_EnachFundTransferOrderUpdateSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._EnachFundTransferOrderUpdateSqsSubscriber = _EnachFundTransferOrderUpdateSqsSubscriber
	helper.AddFieldSetters("enachfundtransferorderupdatesqssubscriber", _fieldSetters, _setters)
	_EnachServiceConfig, _fieldSetters := NewEnachServiceConfig()
	_obj._EnachServiceConfig = _EnachServiceConfig
	helper.AddFieldSetters("enachserviceconfig", _fieldSetters, _setters)
	_PgParams, _fieldSetters := NewPgParams()
	_obj._PgParams = _PgParams
	helper.AddFieldSetters("pgparams", _fieldSetters, _setters)
	_EnachConsumerConfig, _fieldSetters := NewEnachConsumerConfig()
	_obj._EnachConsumerConfig = _EnachConsumerConfig
	helper.AddFieldSetters("enachconsumerconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enableentitysegregation"] = _obj.SetEnableEntitySegregation
	_setters["enableenachmandateentitysegregation"] = _obj.SetEnableEnachMandateEntitySegregation
	_setters["upimandateexecutionvendorcallmindelay"] = _obj.SetUPIMandateExecutionVendorCallMinDelay
	_SIExecutionParams, _fieldSetters := NewSIExecutionParams()
	_obj._SIExecutionParams = _SIExecutionParams
	helper.AddFieldSetters("siexecutionparams", _fieldSetters, _setters)
	_FeatureFlags, _fieldSetters := NewFeatureFlags()
	_obj._FeatureFlags = _FeatureFlags
	helper.AddFieldSetters("featureflags", _fieldSetters, _setters)
	_RecurringPaymentCreationAuthVendorCallbackSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RecurringPaymentCreationAuthVendorCallbackSubscriber = _RecurringPaymentCreationAuthVendorCallbackSubscriber
	helper.AddFieldSetters("recurringpaymentcreationauthvendorcallbacksubscriber", _fieldSetters, _setters)
	_FetchAndCreateOffAppRecurringPaymentSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FetchAndCreateOffAppRecurringPaymentSubscriber = _FetchAndCreateOffAppRecurringPaymentSubscriber
	helper.AddFieldSetters("fetchandcreateoffapprecurringpaymentsubscriber", _fieldSetters, _setters)
	_FetchAndCreateFailedEnachTransactionSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FetchAndCreateFailedEnachTransactionSubscriber = _FetchAndCreateFailedEnachTransactionSubscriber
	helper.AddFieldSetters("fetchandcreatefailedenachtransactionsubscriber", _fieldSetters, _setters)
	_FeatureReleaseConfig, _fieldSetters := genconfig.NewFeatureReleaseConfig()
	_obj._FeatureReleaseConfig = _FeatureReleaseConfig
	helper.AddFieldSetters("featurereleaseconfig", _fieldSetters, _setters)
	_OffAppRecurringPaymentExecutionSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OffAppRecurringPaymentExecutionSubscriber = _OffAppRecurringPaymentExecutionSubscriber
	helper.AddFieldSetters("offapprecurringpaymentexecutionsubscriber", _fieldSetters, _setters)
	_EnachFundTransferOrderUpdateSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._EnachFundTransferOrderUpdateSqsSubscriber = _EnachFundTransferOrderUpdateSqsSubscriber
	helper.AddFieldSetters("enachfundtransferorderupdatesqssubscriber", _fieldSetters, _setters)
	_EnachServiceConfig, _fieldSetters := NewEnachServiceConfig()
	_obj._EnachServiceConfig = _EnachServiceConfig
	helper.AddFieldSetters("enachserviceconfig", _fieldSetters, _setters)
	_PgParams, _fieldSetters := NewPgParams()
	_obj._PgParams = _PgParams
	helper.AddFieldSetters("pgparams", _fieldSetters, _setters)
	_EnachConsumerConfig, _fieldSetters := NewEnachConsumerConfig()
	_obj._EnachConsumerConfig = _EnachConsumerConfig
	helper.AddFieldSetters("enachconsumerconfig", _fieldSetters, _setters)

	return _obj, _setters
}

func (obj *Config) Init() {
	newObj, _ := NewConfig()
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *server.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enableentitysegregation":
		return obj.SetEnableEntitySegregation(v.EnableEntitySegregation, true, nil)
	case "enableenachmandateentitysegregation":
		return obj.SetEnableEnachMandateEntitySegregation(v.EnableEnachMandateEntitySegregation, true, nil)
	case "upimandateexecutionvendorcallmindelay":
		return obj.SetUPIMandateExecutionVendorCallMinDelay(v.UPIMandateExecutionVendorCallMinDelay, true, nil)
	case "siexecutionparams":
		return obj._SIExecutionParams.Set(v.SIExecutionParams, true, path)
	case "featureflags":
		return obj._FeatureFlags.Set(v.FeatureFlags, true, path)
	case "recurringpaymentcreationauthvendorcallbacksubscriber":
		return obj._RecurringPaymentCreationAuthVendorCallbackSubscriber.Set(v.RecurringPaymentCreationAuthVendorCallbackSubscriber, true, path)
	case "fetchandcreateoffapprecurringpaymentsubscriber":
		return obj._FetchAndCreateOffAppRecurringPaymentSubscriber.Set(v.FetchAndCreateOffAppRecurringPaymentSubscriber, true, path)
	case "fetchandcreatefailedenachtransactionsubscriber":
		return obj._FetchAndCreateFailedEnachTransactionSubscriber.Set(v.FetchAndCreateFailedEnachTransactionSubscriber, true, path)
	case "featurereleaseconfig":
		return obj._FeatureReleaseConfig.Set(v.FeatureReleaseConfig, true, path)
	case "offapprecurringpaymentexecutionsubscriber":
		return obj._OffAppRecurringPaymentExecutionSubscriber.Set(v.OffAppRecurringPaymentExecutionSubscriber, true, path)
	case "enachfundtransferorderupdatesqssubscriber":
		return obj._EnachFundTransferOrderUpdateSqsSubscriber.Set(v.EnachFundTransferOrderUpdateSqsSubscriber, true, path)
	case "enachserviceconfig":
		return obj._EnachServiceConfig.Set(v.EnachServiceConfig, true, path)
	case "pgparams":
		return obj._PgParams.Set(v.PgParams, true, path)
	case "enachconsumerconfig":
		return obj._EnachConsumerConfig.Set(v.EnachConsumerConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *server.Config, dynamic bool, path []string) (err error) {

	err = obj.SetEnableEntitySegregation(v.EnableEntitySegregation, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableEnachMandateEntitySegregation(v.EnableEnachMandateEntitySegregation, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUPIMandateExecutionVendorCallMinDelay(v.UPIMandateExecutionVendorCallMinDelay, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._SIExecutionParams.Set(v.SIExecutionParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FeatureFlags.Set(v.FeatureFlags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RecurringPaymentCreationAuthVendorCallbackSubscriber.Set(v.RecurringPaymentCreationAuthVendorCallbackSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FetchAndCreateOffAppRecurringPaymentSubscriber.Set(v.FetchAndCreateOffAppRecurringPaymentSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FetchAndCreateFailedEnachTransactionSubscriber.Set(v.FetchAndCreateFailedEnachTransactionSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FeatureReleaseConfig.Set(v.FeatureReleaseConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OffAppRecurringPaymentExecutionSubscriber.Set(v.OffAppRecurringPaymentExecutionSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnachFundTransferOrderUpdateSqsSubscriber.Set(v.EnachFundTransferOrderUpdateSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnachServiceConfig.Set(v.EnachServiceConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PgParams.Set(v.PgParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnachConsumerConfig.Set(v.EnachConsumerConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *server.Config) error {

	obj._Application = v.Application
	obj._Server = v.Server
	obj._EpifiDb = v.EpifiDb
	obj._RecurringPaymentDb = v.RecurringPaymentDb
	obj._Secrets = v.Secrets
	obj._AWS = v.AWS
	obj._Flags = v.Flags
	obj._AuthenticationTimeLimitForAction = v.AuthenticationTimeLimitForAction
	obj._RecurringPaymentNotificationParams = v.RecurringPaymentNotificationParams
	obj._UpiMandateUrn = v.UpiMandateUrn
	obj._UpiMandateValidationParams = v.UpiMandateValidationParams
	obj._UpiMandateNotifDateFormat = v.UpiMandateNotifDateFormat
	obj._Tracing = v.Tracing
	obj._RecurringPaymentFrequencyMapping = v.RecurringPaymentFrequencyMapping
	obj._PaymentEnquiryParams = v.PaymentEnquiryParams
	obj._RecurringPaymentCreationParams = v.RecurringPaymentCreationParams
	obj._SignalWorkflowPublisher = v.SignalWorkflowPublisher
	obj._RecurringPaymentExecutionParams = v.RecurringPaymentExecutionParams
	obj._RecurringPaymentModificationParams = v.RecurringPaymentModificationParams
	obj._RecurringPaymentRevokeParams = v.RecurringPaymentRevokeParams
	obj._RecurringPaymentRetryErrors = v.RecurringPaymentRetryErrors
	obj._NonPauseableMccsForNonRevokeableMandates = v.NonPauseableMccsForNonRevokeableMandates
	obj._MccWarningMessageMap = v.MccWarningMessageMap
	obj._MaxAmountForMCCAndPurposeCode = v.MaxAmountForMCCAndPurposeCode
	obj._RecurringPaymentPauseUnpauseParams = v.RecurringPaymentPauseUnpauseParams
	obj._InPaymentOrderUpdatePublisher = v.InPaymentOrderUpdatePublisher
	obj._NonTpapPspHandles = v.NonTpapPspHandles
	obj._MaxCollectAmountLimitForNonVerifiedMerchants = v.MaxCollectAmountLimitForNonVerifiedMerchants
	obj._FetchAndCreateFailedEnachTransactionPublisher = v.FetchAndCreateFailedEnachTransactionPublisher
	obj._FetchAndCreateOffAppRecurringPaymentPublisher = v.FetchAndCreateOffAppRecurringPaymentPublisher
	obj._EnachCreationValidationConfig = v.EnachCreationValidationConfig
	obj._PgProgramToAuthSecretMap = v.PgProgramToAuthSecretMap
	obj._MaxAmountForMandateExecutionWithoutAFA = v.MaxAmountForMandateExecutionWithoutAFA
	obj._MaxAmountLimitForMccWithoutAFA = v.MaxAmountLimitForMccWithoutAFA
	obj._RazorPayResponseCodesJson = v.RazorPayResponseCodesJson
	return nil
}

func (obj *Config) SetEnableEntitySegregation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableEntitySegregation", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableEntitySegregation, 1)
	} else {
		atomic.StoreUint32(&obj._EnableEntitySegregation, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableEntitySegregation")
	}
	return nil
}
func (obj *Config) SetEnableEnachMandateEntitySegregation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableEnachMandateEntitySegregation", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableEnachMandateEntitySegregation, 1)
	} else {
		atomic.StoreUint32(&obj._EnableEnachMandateEntitySegregation, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableEnachMandateEntitySegregation")
	}
	return nil
}
func (obj *Config) SetUPIMandateExecutionVendorCallMinDelay(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.UPIMandateExecutionVendorCallMinDelay", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._UPIMandateExecutionVendorCallMinDelay, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "UPIMandateExecutionVendorCallMinDelay")
	}
	return nil
}

func NewSIExecutionParams() (_obj *SIExecutionParams, _setters map[string]dynconf.SetFunc) {
	_obj = &SIExecutionParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["paymentprotocol"] = _obj.SetPaymentProtocol
	return _obj, _setters
}

func (obj *SIExecutionParams) Init() {
	newObj, _ := NewSIExecutionParams()
	*obj = *newObj
}

func (obj *SIExecutionParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SIExecutionParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.SIExecutionParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *SIExecutionParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SIExecutionParams) setDynamicField(v *server.SIExecutionParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "paymentprotocol":
		return obj.SetPaymentProtocol(v.PaymentProtocol, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SIExecutionParams) setDynamicFields(v *server.SIExecutionParams, dynamic bool, path []string) (err error) {

	err = obj.SetPaymentProtocol(v.PaymentProtocol, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *SIExecutionParams) setStaticFields(v *server.SIExecutionParams) error {

	return nil
}

func (obj *SIExecutionParams) SetPaymentProtocol(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *SIExecutionParams.PaymentProtocol", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._PaymentProtocol, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "PaymentProtocol")
	}
	return nil
}

func NewFeatureFlags() (_obj *FeatureFlags, _setters map[string]dynconf.SetFunc) {
	_obj = &FeatureFlags{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enablerecurringpaymentcreationviacelestial"] = _obj.SetEnableRecurringPaymentCreationViaCelestial
	_setters["enablerecurringpaymentexecutionwithoutauthviacelestial"] = _obj.SetEnableRecurringPaymentExecutionWithoutAuthViaCelestial
	_setters["enablerecurringpaymentexecutionwithauthviacelestial"] = _obj.SetEnableRecurringPaymentExecutionWithAuthViaCelestial
	_setters["enablerecurringpaymentmodificationviacelestial"] = _obj.SetEnableRecurringPaymentModificationViaCelestial
	_setters["enablerecurringpaymentrevokeviacelestial"] = _obj.SetEnableRecurringPaymentRevokeViaCelestial
	_setters["enablerecurringpaymentpauseunpauseviacelestial"] = _obj.SetEnableRecurringPaymentPauseUnpauseViaCelestial
	return _obj, _setters
}

func (obj *FeatureFlags) Init() {
	newObj, _ := NewFeatureFlags()
	*obj = *newObj
}

func (obj *FeatureFlags) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *FeatureFlags) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.FeatureFlags)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *FeatureFlags) setDynamicField(v *server.FeatureFlags, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enablerecurringpaymentcreationviacelestial":
		return obj.SetEnableRecurringPaymentCreationViaCelestial(v.EnableRecurringPaymentCreationViaCelestial, true, nil)
	case "enablerecurringpaymentexecutionwithoutauthviacelestial":
		return obj.SetEnableRecurringPaymentExecutionWithoutAuthViaCelestial(v.EnableRecurringPaymentExecutionWithoutAuthViaCelestial, true, nil)
	case "enablerecurringpaymentexecutionwithauthviacelestial":
		return obj.SetEnableRecurringPaymentExecutionWithAuthViaCelestial(v.EnableRecurringPaymentExecutionWithAuthViaCelestial, true, nil)
	case "enablerecurringpaymentmodificationviacelestial":
		return obj.SetEnableRecurringPaymentModificationViaCelestial(v.EnableRecurringPaymentModificationViaCelestial, true, nil)
	case "enablerecurringpaymentrevokeviacelestial":
		return obj.SetEnableRecurringPaymentRevokeViaCelestial(v.EnableRecurringPaymentRevokeViaCelestial, true, nil)
	case "enablerecurringpaymentpauseunpauseviacelestial":
		return obj.SetEnableRecurringPaymentPauseUnpauseViaCelestial(v.EnableRecurringPaymentPauseUnpauseViaCelestial, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *FeatureFlags) setDynamicFields(v *server.FeatureFlags, dynamic bool, path []string) (err error) {

	err = obj.SetEnableRecurringPaymentCreationViaCelestial(v.EnableRecurringPaymentCreationViaCelestial, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableRecurringPaymentExecutionWithoutAuthViaCelestial(v.EnableRecurringPaymentExecutionWithoutAuthViaCelestial, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableRecurringPaymentExecutionWithAuthViaCelestial(v.EnableRecurringPaymentExecutionWithAuthViaCelestial, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableRecurringPaymentModificationViaCelestial(v.EnableRecurringPaymentModificationViaCelestial, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableRecurringPaymentRevokeViaCelestial(v.EnableRecurringPaymentRevokeViaCelestial, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableRecurringPaymentPauseUnpauseViaCelestial(v.EnableRecurringPaymentPauseUnpauseViaCelestial, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *FeatureFlags) setStaticFields(v *server.FeatureFlags) error {

	return nil
}

func (obj *FeatureFlags) SetEnableRecurringPaymentCreationViaCelestial(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags.EnableRecurringPaymentCreationViaCelestial", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableRecurringPaymentCreationViaCelestial, 1)
	} else {
		atomic.StoreUint32(&obj._EnableRecurringPaymentCreationViaCelestial, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableRecurringPaymentCreationViaCelestial")
	}
	return nil
}
func (obj *FeatureFlags) SetEnableRecurringPaymentExecutionWithoutAuthViaCelestial(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags.EnableRecurringPaymentExecutionWithoutAuthViaCelestial", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableRecurringPaymentExecutionWithoutAuthViaCelestial, 1)
	} else {
		atomic.StoreUint32(&obj._EnableRecurringPaymentExecutionWithoutAuthViaCelestial, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableRecurringPaymentExecutionWithoutAuthViaCelestial")
	}
	return nil
}
func (obj *FeatureFlags) SetEnableRecurringPaymentExecutionWithAuthViaCelestial(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags.EnableRecurringPaymentExecutionWithAuthViaCelestial", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableRecurringPaymentExecutionWithAuthViaCelestial, 1)
	} else {
		atomic.StoreUint32(&obj._EnableRecurringPaymentExecutionWithAuthViaCelestial, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableRecurringPaymentExecutionWithAuthViaCelestial")
	}
	return nil
}
func (obj *FeatureFlags) SetEnableRecurringPaymentModificationViaCelestial(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags.EnableRecurringPaymentModificationViaCelestial", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableRecurringPaymentModificationViaCelestial, 1)
	} else {
		atomic.StoreUint32(&obj._EnableRecurringPaymentModificationViaCelestial, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableRecurringPaymentModificationViaCelestial")
	}
	return nil
}
func (obj *FeatureFlags) SetEnableRecurringPaymentRevokeViaCelestial(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags.EnableRecurringPaymentRevokeViaCelestial", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableRecurringPaymentRevokeViaCelestial, 1)
	} else {
		atomic.StoreUint32(&obj._EnableRecurringPaymentRevokeViaCelestial, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableRecurringPaymentRevokeViaCelestial")
	}
	return nil
}
func (obj *FeatureFlags) SetEnableRecurringPaymentPauseUnpauseViaCelestial(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags.EnableRecurringPaymentPauseUnpauseViaCelestial", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableRecurringPaymentPauseUnpauseViaCelestial, 1)
	} else {
		atomic.StoreUint32(&obj._EnableRecurringPaymentPauseUnpauseViaCelestial, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableRecurringPaymentPauseUnpauseViaCelestial")
	}
	return nil
}

func NewEnachServiceConfig() (_obj *EnachServiceConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &EnachServiceConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._SupportedBankToDetailsMap = &syncmap.Map[string, *EnachSupportedBankDetails]{}
	_setters["supportedbanktodetailsmap"] = _obj.SetSupportedBankToDetailsMap

	_obj._VendorToActivationCooldownMap = &syncmap.Map[string, time.Duration]{}
	_setters["vendortoactivationcooldownmap"] = _obj.SetVendorToActivationCooldownMap
	_VendorConfig, _fieldSetters := NewEnachVendorConfig()
	_obj._VendorConfig = _VendorConfig
	helper.AddFieldSetters("vendorconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *EnachServiceConfig) Init() {
	newObj, _ := NewEnachServiceConfig()
	*obj = *newObj
}

func (obj *EnachServiceConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *EnachServiceConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.EnachServiceConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnachServiceConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *EnachServiceConfig) setDynamicField(v *server.EnachServiceConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "supportedbanktodetailsmap":
		return obj.SetSupportedBankToDetailsMap(v.SupportedBankToDetailsMap, true, path)
	case "vendortoactivationcooldownmap":
		return obj.SetVendorToActivationCooldownMap(v.VendorToActivationCooldownMap, true, path)
	case "vendorconfig":
		return obj._VendorConfig.Set(v.VendorConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *EnachServiceConfig) setDynamicFields(v *server.EnachServiceConfig, dynamic bool, path []string) (err error) {

	err = obj.SetSupportedBankToDetailsMap(v.SupportedBankToDetailsMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetVendorToActivationCooldownMap(v.VendorToActivationCooldownMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._VendorConfig.Set(v.VendorConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *EnachServiceConfig) setStaticFields(v *server.EnachServiceConfig) error {

	return nil
}

func (obj *EnachServiceConfig) SetSupportedBankToDetailsMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*server.EnachSupportedBankDetails)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnachServiceConfig.SupportedBankToDetailsMap", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._SupportedBankToDetailsMap, v, dynamic, path)

}
func (obj *EnachServiceConfig) SetVendorToActivationCooldownMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnachServiceConfig.VendorToActivationCooldownMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._VendorToActivationCooldownMap, v, path)
}

func NewEnachSupportedBankDetails() (_obj *EnachSupportedBankDetails, _setters map[string]dynconf.SetFunc) {
	_obj = &EnachSupportedBankDetails{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["supportedauthmodes"] = _obj.SetSupportedAuthModes
	_obj._SupportedAuthModesMutex = &sync.RWMutex{}
	_setters["bankiconurl"] = _obj.SetBankIconUrl
	_obj._BankIconUrlMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *EnachSupportedBankDetails) Init() {
	newObj, _ := NewEnachSupportedBankDetails()
	*obj = *newObj
}

func (obj *EnachSupportedBankDetails) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *EnachSupportedBankDetails) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.EnachSupportedBankDetails)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnachSupportedBankDetails", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *EnachSupportedBankDetails) setDynamicField(v *server.EnachSupportedBankDetails, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "supportedauthmodes":
		return obj.SetSupportedAuthModes(v.SupportedAuthModes, true, path)
	case "bankiconurl":
		return obj.SetBankIconUrl(v.BankIconUrl, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *EnachSupportedBankDetails) setDynamicFields(v *server.EnachSupportedBankDetails, dynamic bool, path []string) (err error) {

	err = obj.SetSupportedAuthModes(v.SupportedAuthModes, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetBankIconUrl(v.BankIconUrl, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *EnachSupportedBankDetails) setStaticFields(v *server.EnachSupportedBankDetails) error {

	return nil
}

func (obj *EnachSupportedBankDetails) SetSupportedAuthModes(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnachSupportedBankDetails.SupportedAuthModes", reflect.TypeOf(val))
	}
	obj._SupportedAuthModesMutex.Lock()
	defer obj._SupportedAuthModesMutex.Unlock()
	obj._SupportedAuthModes = roarray.New[string](v)
	return nil
}
func (obj *EnachSupportedBankDetails) SetBankIconUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnachSupportedBankDetails.BankIconUrl", reflect.TypeOf(val))
	}
	obj._BankIconUrlMutex.Lock()
	defer obj._BankIconUrlMutex.Unlock()
	obj._BankIconUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BankIconUrl")
	}
	return nil
}

func NewEnachVendorConfig() (_obj *EnachVendorConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &EnachVendorConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_FederalConfig, _fieldSetters := NewEnachFederalConfig()
	_obj._FederalConfig = _FederalConfig
	helper.AddFieldSetters("federalconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *EnachVendorConfig) Init() {
	newObj, _ := NewEnachVendorConfig()
	*obj = *newObj
}

func (obj *EnachVendorConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *EnachVendorConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.EnachVendorConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnachVendorConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *EnachVendorConfig) setDynamicField(v *server.EnachVendorConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "federalconfig":
		return obj._FederalConfig.Set(v.FederalConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *EnachVendorConfig) setDynamicFields(v *server.EnachVendorConfig, dynamic bool, path []string) (err error) {

	err = obj._FederalConfig.Set(v.FederalConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *EnachVendorConfig) setStaticFields(v *server.EnachVendorConfig) error {

	return nil
}

func NewEnachFederalConfig() (_obj *EnachFederalConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &EnachFederalConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_MandateCreationConfig, _fieldSetters := NewEnachMandateCreationFederalConfig()
	_obj._MandateCreationConfig = _MandateCreationConfig
	helper.AddFieldSetters("mandatecreationconfig", _fieldSetters, _setters)
	_MandateExecutionConfig, _fieldSetters := NewEnachMandateExecutionFederalConfig()
	_obj._MandateExecutionConfig = _MandateExecutionConfig
	helper.AddFieldSetters("mandateexecutionconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *EnachFederalConfig) Init() {
	newObj, _ := NewEnachFederalConfig()
	*obj = *newObj
}

func (obj *EnachFederalConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *EnachFederalConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.EnachFederalConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnachFederalConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *EnachFederalConfig) setDynamicField(v *server.EnachFederalConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "mandatecreationconfig":
		return obj._MandateCreationConfig.Set(v.MandateCreationConfig, true, path)
	case "mandateexecutionconfig":
		return obj._MandateExecutionConfig.Set(v.MandateExecutionConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *EnachFederalConfig) setDynamicFields(v *server.EnachFederalConfig, dynamic bool, path []string) (err error) {

	err = obj._MandateCreationConfig.Set(v.MandateCreationConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._MandateExecutionConfig.Set(v.MandateExecutionConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *EnachFederalConfig) setStaticFields(v *server.EnachFederalConfig) error {

	return nil
}

func NewEnachMandateCreationFederalConfig() (_obj *EnachMandateCreationFederalConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &EnachMandateCreationFederalConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enablersaencryption"] = _obj.SetEnableRsaEncryption
	_setters["redirectionexpiry"] = _obj.SetRedirectionExpiry
	_setters["redirectionurl"] = _obj.SetRedirectionUrl
	_obj._RedirectionUrlMutex = &sync.RWMutex{}
	_setters["responseurl"] = _obj.SetResponseUrl
	_obj._ResponseUrlMutex = &sync.RWMutex{}
	_setters["exiturl"] = _obj.SetExitUrl
	_obj._ExitUrlMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *EnachMandateCreationFederalConfig) Init() {
	newObj, _ := NewEnachMandateCreationFederalConfig()
	*obj = *newObj
}

func (obj *EnachMandateCreationFederalConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *EnachMandateCreationFederalConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.EnachMandateCreationFederalConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnachMandateCreationFederalConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *EnachMandateCreationFederalConfig) setDynamicField(v *server.EnachMandateCreationFederalConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enablersaencryption":
		return obj.SetEnableRsaEncryption(v.EnableRsaEncryption, true, nil)
	case "redirectionexpiry":
		return obj.SetRedirectionExpiry(v.RedirectionExpiry, true, nil)
	case "redirectionurl":
		return obj.SetRedirectionUrl(v.RedirectionUrl, true, nil)
	case "responseurl":
		return obj.SetResponseUrl(v.ResponseUrl, true, nil)
	case "exiturl":
		return obj.SetExitUrl(v.ExitUrl, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *EnachMandateCreationFederalConfig) setDynamicFields(v *server.EnachMandateCreationFederalConfig, dynamic bool, path []string) (err error) {

	err = obj.SetEnableRsaEncryption(v.EnableRsaEncryption, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetRedirectionExpiry(v.RedirectionExpiry, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetRedirectionUrl(v.RedirectionUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetResponseUrl(v.ResponseUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetExitUrl(v.ExitUrl, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *EnachMandateCreationFederalConfig) setStaticFields(v *server.EnachMandateCreationFederalConfig) error {

	return nil
}

func (obj *EnachMandateCreationFederalConfig) SetEnableRsaEncryption(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnachMandateCreationFederalConfig.EnableRsaEncryption", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableRsaEncryption, 1)
	} else {
		atomic.StoreUint32(&obj._EnableRsaEncryption, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableRsaEncryption")
	}
	return nil
}
func (obj *EnachMandateCreationFederalConfig) SetRedirectionExpiry(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnachMandateCreationFederalConfig.RedirectionExpiry", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._RedirectionExpiry, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "RedirectionExpiry")
	}
	return nil
}
func (obj *EnachMandateCreationFederalConfig) SetRedirectionUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnachMandateCreationFederalConfig.RedirectionUrl", reflect.TypeOf(val))
	}
	obj._RedirectionUrlMutex.Lock()
	defer obj._RedirectionUrlMutex.Unlock()
	obj._RedirectionUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "RedirectionUrl")
	}
	return nil
}
func (obj *EnachMandateCreationFederalConfig) SetResponseUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnachMandateCreationFederalConfig.ResponseUrl", reflect.TypeOf(val))
	}
	obj._ResponseUrlMutex.Lock()
	defer obj._ResponseUrlMutex.Unlock()
	obj._ResponseUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ResponseUrl")
	}
	return nil
}
func (obj *EnachMandateCreationFederalConfig) SetExitUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnachMandateCreationFederalConfig.ExitUrl", reflect.TypeOf(val))
	}
	obj._ExitUrlMutex.Lock()
	defer obj._ExitUrlMutex.Unlock()
	obj._ExitUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ExitUrl")
	}
	return nil
}

func NewEnachMandateExecutionFederalConfig() (_obj *EnachMandateExecutionFederalConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &EnachMandateExecutionFederalConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["minwaitdurationforrepresentingreturnedtxn"] = _obj.SetMinWaitDurationForRepresentingReturnedTxn
	return _obj, _setters
}

func (obj *EnachMandateExecutionFederalConfig) Init() {
	newObj, _ := NewEnachMandateExecutionFederalConfig()
	*obj = *newObj
}

func (obj *EnachMandateExecutionFederalConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *EnachMandateExecutionFederalConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.EnachMandateExecutionFederalConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnachMandateExecutionFederalConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *EnachMandateExecutionFederalConfig) setDynamicField(v *server.EnachMandateExecutionFederalConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "minwaitdurationforrepresentingreturnedtxn":
		return obj.SetMinWaitDurationForRepresentingReturnedTxn(v.MinWaitDurationForRepresentingReturnedTxn, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *EnachMandateExecutionFederalConfig) setDynamicFields(v *server.EnachMandateExecutionFederalConfig, dynamic bool, path []string) (err error) {

	err = obj.SetMinWaitDurationForRepresentingReturnedTxn(v.MinWaitDurationForRepresentingReturnedTxn, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *EnachMandateExecutionFederalConfig) setStaticFields(v *server.EnachMandateExecutionFederalConfig) error {

	return nil
}

func (obj *EnachMandateExecutionFederalConfig) SetMinWaitDurationForRepresentingReturnedTxn(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnachMandateExecutionFederalConfig.MinWaitDurationForRepresentingReturnedTxn", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MinWaitDurationForRepresentingReturnedTxn, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinWaitDurationForRepresentingReturnedTxn")
	}
	return nil
}

func NewPgParams() (_obj *PgParams, _setters map[string]dynconf.SetFunc) {
	_obj = &PgParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enableoffappmandatecreation"] = _obj.SetEnableOffAppMandateCreation

	_obj._RpExecutionPaymentAutoCaptureTimeout = &syncmap.Map[string, time.Duration]{}
	_setters["rpexecutionpaymentautocapturetimeout"] = _obj.SetRpExecutionPaymentAutoCaptureTimeout
	return _obj, _setters
}

func (obj *PgParams) Init() {
	newObj, _ := NewPgParams()
	*obj = *newObj
}

func (obj *PgParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PgParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.PgParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *PgParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PgParams) setDynamicField(v *server.PgParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enableoffappmandatecreation":
		return obj.SetEnableOffAppMandateCreation(v.EnableOffAppMandateCreation, true, nil)
	case "rpexecutionpaymentautocapturetimeout":
		return obj.SetRpExecutionPaymentAutoCaptureTimeout(v.RpExecutionPaymentAutoCaptureTimeout, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PgParams) setDynamicFields(v *server.PgParams, dynamic bool, path []string) (err error) {

	err = obj.SetEnableOffAppMandateCreation(v.EnableOffAppMandateCreation, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetRpExecutionPaymentAutoCaptureTimeout(v.RpExecutionPaymentAutoCaptureTimeout, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *PgParams) setStaticFields(v *server.PgParams) error {

	obj._RecurringPaymentTypeToExecutionLimit = v.RecurringPaymentTypeToExecutionLimit
	obj._VendorOrderExpirationDuration = v.VendorOrderExpirationDuration
	return nil
}

func (obj *PgParams) SetEnableOffAppMandateCreation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *PgParams.EnableOffAppMandateCreation", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableOffAppMandateCreation, 1)
	} else {
		atomic.StoreUint32(&obj._EnableOffAppMandateCreation, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableOffAppMandateCreation")
	}
	return nil
}
func (obj *PgParams) SetRpExecutionPaymentAutoCaptureTimeout(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *PgParams.RpExecutionPaymentAutoCaptureTimeout", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._RpExecutionPaymentAutoCaptureTimeout, v, path)
}

func NewEnachConsumerConfig() (_obj *EnachConsumerConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &EnachConsumerConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["maxalloweddaystofetchfailedtxns"] = _obj.SetMaxAllowedDaysToFetchFailedTxns
	_setters["numdaystofetchfailedtxns"] = _obj.SetNumDaysToFetchFailedTxns
	return _obj, _setters
}

func (obj *EnachConsumerConfig) Init() {
	newObj, _ := NewEnachConsumerConfig()
	*obj = *newObj
}

func (obj *EnachConsumerConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *EnachConsumerConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.EnachConsumerConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnachConsumerConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *EnachConsumerConfig) setDynamicField(v *server.EnachConsumerConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "maxalloweddaystofetchfailedtxns":
		return obj.SetMaxAllowedDaysToFetchFailedTxns(v.MaxAllowedDaysToFetchFailedTxns, true, nil)
	case "numdaystofetchfailedtxns":
		return obj.SetNumDaysToFetchFailedTxns(v.NumDaysToFetchFailedTxns, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *EnachConsumerConfig) setDynamicFields(v *server.EnachConsumerConfig, dynamic bool, path []string) (err error) {

	err = obj.SetMaxAllowedDaysToFetchFailedTxns(v.MaxAllowedDaysToFetchFailedTxns, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetNumDaysToFetchFailedTxns(v.NumDaysToFetchFailedTxns, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *EnachConsumerConfig) setStaticFields(v *server.EnachConsumerConfig) error {

	return nil
}

func (obj *EnachConsumerConfig) SetMaxAllowedDaysToFetchFailedTxns(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnachConsumerConfig.MaxAllowedDaysToFetchFailedTxns", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MaxAllowedDaysToFetchFailedTxns, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxAllowedDaysToFetchFailedTxns")
	}
	return nil
}
func (obj *EnachConsumerConfig) SetNumDaysToFetchFailedTxns(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnachConsumerConfig.NumDaysToFetchFailedTxns", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._NumDaysToFetchFailedTxns, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "NumDaysToFetchFailedTxns")
	}
	return nil
}
