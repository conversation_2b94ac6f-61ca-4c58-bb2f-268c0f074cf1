package genconf

import (
	"fmt"
	"sync"

	"github.com/mohae/deepcopy"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cfg/dynconf"
	rpServerConfig "github.com/epifi/gamma/recurringpayment/config/server"
)

var (
	once    sync.Once
	genConf *Config
	err     error
)

// Load initializes the object of the generated conf struct by copying the values from the Static conf.
// Then it starts watcher to update remote config.
func Load() (*Config, error) {
	once.Do(func() {
		genConf, err = loadConfig()
	})
	if err != nil {
		return nil, err
	}
	return genConf, err
}

func loadConfig() (*Config, error) {
	staticConf, err := rpServerConfig.Load()
	if err != nil {
		return nil, err
	}
	// clone staticConf to avoid any potential write on the object when the remote config changes are applied.
	// Since static conf is not concurrent safe, these writes may lead to race condition.
	staticConfClone := deepcopy.Copy(staticConf).(*rpServerConfig.Config)
	gconf, setters := NewConfig()
	err = gconf.Set(staticConfClone, false, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to set statis staticConf in dynamic config: %w", err)
	}

	sc := dynconf.NewSafeConfigV2(staticConfClone, setters)
	envName, err := cfg.GetEnvironment()
	if err != nil {
		return nil, fmt.Errorf("failed to get environment: %w", err)
	}

	err = dynconf.LoadAndUpdateFromRemoteStoreV2(envName, cfg.RECURRING_PAYMENT_SERVICE, sc)
	if err != nil {
		return nil, err
	}
	return gconf, nil
}
