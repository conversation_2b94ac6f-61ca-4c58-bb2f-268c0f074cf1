package commons

import "time"

const (
	// nolint:gosec
	EnachSecretsKeyName = "EnachSecrets"
)

type EnachSecrets struct {
	AppId                        string            `json:"AppId"`
	HashKey                      string            `json:"HashKey"`
	UtilityCode                  string            `json:"UtilityCode"`
	UIEntryPointToUtilityCodeMap map[string]string `json:"UIEntryPointToUtilityCodeMap"`
}

type EnachCreationValidationConfig struct {
	// min required gap for enach start time since creation request time.
	// this is needed for enforcing validations like start date can't be same as creation date etc.
	MinRequiredDelayForEnachStartTimeSinceCreation time.Duration

	// max amount with which the enach can be setup
	MaxAllowedAmountInRs int32
}
