package worker

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/knadh/koanf"

	"github.com/epifi/be-common/pkg/cfg"
	cfgV2 "github.com/epifi/be-common/pkg/cfg/v2"

	"github.com/epifi/be-common/pkg/epifitemporal"

	"github.com/epifi/be-common/pkg/pay/pgerrorcodes"

	orderConfig "github.com/epifi/gamma/order/config"
	payPkg "github.com/epifi/gamma/pkg/pay"
)

var (
	_, b, _, _ = runtime.Caller(0)
	once       sync.Once
	config     *Config
	err        error
)

//go:generate conf_gen github.com/epifi/gamma/recurringpayment/config/worker Config
type Config struct {
	Server                                *cfg.ServerPorts
	Application                           *cfg.TemporalWorkerApplication `dynamic:"true"`
	WorkflowParamsList                    cfgV2.WorkflowParamsList
	DefaultActivityParamsList             cfgV2.ActivityParamsList
	PausedWorkflowList                    cfgV2.PausedWorkflowsList
	DbConfigMap                           cfg.DbConfigMap
	UsecaseDbConfigMap                    cfg.UseCaseDbConfigMap
	AWS                                   *cfg.AWS
	OrderUpdatePublisher                  *cfg.SnsPublisher
	WorkflowUpdatePublisher               *cfg.SnsPublisher
	RecurringPaymentActionUpdatePublisher *cfg.SnsPublisher
	Tracing                               *cfg.Tracing

	Secrets                            *cfg.Secrets
	Flags                              *Flags
	AuthenticationTimeLimitForAction   *AuthenticationTimeLimitForAction
	RecurringPaymentNotificationParams *RecurringPaymentNotificationParams
	UpiMandateUrn                      *UpiMandateUrn
	UpiMandateValidationParams         *UpiMandateValidationParams
	UpiMandateNotifDateFormat          string
	SIExecutionParams                  *SIExecutionParams `dynamic:"true"`
	RecurringPaymentFrequencyMapping   map[string]string
	// PaymentEnquiryParams configurable parameters for payment enquiry
	PaymentEnquiryParams               *payPkg.PaymentEnquiryParams
	FeatureFlags                       *FeatureFlags `dynamic:"true"`
	RecurringPaymentCreationParams     *RecurringPaymentCreationParams
	SignalWorkflowPublisher            *cfg.SqsPublisher
	RecurringPaymentExecutionParams    *RecurringPaymentExecutionParams
	RecurringPaymentModificationParams *RecurringPaymentModificationParams

	EnachConfig *EnachConfig
	// todo (utkarsh) : consider moving it to dbConfigMap
	RecurringPaymentDb                  *cfg.DB
	OrderCacheConfig                    *orderConfig.OrderCacheConfig `dynamic:"true"`
	EnableEntitySegregation             bool                          `dynamic:"true"`
	EnableEnachMandateEntitySegregation bool                          `dynamic:"true"`
	RazorPayResponseCodesJson           string
}
type EnachConfig struct {
	FileRecordsBucket *PresentationAwsBucketConfig
	// PresentationFileConfig stores config related to presentation file.
	PresentationFileConfig *EnachPresentationFileConfig
}
type EnachPresentationFileConfig struct {
	// Account details of federal enach pool account
	// this needs to be empty in the presentation file as per FEDERAL but keep it for now until the flow is stable on prod
	// todo (utkarsh): remove this if not needed
	EpifiFederalPoolAccountNumber string
	// this needs to be empty in the presentation file as per FEDERAL but keep it for now until the flow is stable on prod
	// todo (utkarsh): remove this if not needed
	EpifiFederalIFSCCode string
	// HeaderTransactionCode denotes the value which should be filled in transaction code header column in presentation file.
	// Supported values are  "12" for ACH Credit, "33" for ACH APBS Credit, "56" for ACH Debit,
	// For now, we are ONLY using value "56" as we are supporting ACH Debits only for now.
	HeaderTransactionCode string
	// EntryTransactionCode denotes the value which should be filled in transaction code field column in presentation file.
	// Supported values are "23" for ACH Credit, "77" for ACH APBS, "67" for ACH Debit,
	// For now, we are ONLY using value "67" as we are supporting ACH debits only for now.
	EntryTransactionCode string
	// EntryProductType is a 3 digit alphanumeric which is mapped to product (eg: APBS Schemes).
	// List of product types are be provided by NPCI.
	// For now, we are ONLY using value "10".
	EntryProductType string
	// AccountTypeToEntryDestinationAccountType denotes the mapping of account type (beneficiary or remitter) to corresponding string value which used in the account type column in presentation file
	// Following is the mapping as per ENACH presentation file specs:
	// SAVINGS: "10"
	// CURRENT: "11"
	// CREDIT_CARD_ACCOUNT: "13"
	AccountTypeToEntryDestinationAccountType map[string]string
	// UtilityName is mapped one to one with UtilityCode.
	UtilityName string
}
type PresentationAwsBucketConfig struct {
	// denotes the s3 bucket where presentation related files are stored.
	AwsBucket string
	// denotes the s3 directory where all presentation related files (presentation file, presentation ack file, presentation response file) are stored.
	PresentationFilesS3Directory string
}

// RecurringPaymentNotificationParams for templating notification data
// suffix payer signifies that notification params are for payer.
type RecurringPaymentNotificationParams struct {
	MandateCreationPayer         payPkg.NotificationTemplateParams
	MandateExecutionSuccessPayer payPkg.NotificationTemplateParams
	MandateExecutionFailedPayer  payPkg.NotificationTemplateParams
	MandateApprovedPayer         payPkg.NotificationTemplateParams
	MandateDeclinedPayer         payPkg.NotificationTemplateParams
	// facilitating multiple notifications for mandate received
	MandateReceivedPayer       []payPkg.NotificationTemplateParams
	MandateRevokedPayer        payPkg.NotificationTemplateParams
	MandateModifiedPayer       payPkg.NotificationTemplateParams
	MandateAuthorizedPayer     payPkg.NotificationTemplateParams
	MandateAcceptancePayee     payPkg.NotificationTemplateParams
	MandatePausedPayer         payPkg.NotificationTemplateParams
	MandateUnpausedPayer       payPkg.NotificationTemplateParams
	SICreationPayer            payPkg.NotificationTemplateParams
	SIDeclinedPayer            payPkg.NotificationTemplateParams
	SIExecutionSuccessPayer    payPkg.NotificationTemplateParams
	SIExecutionFailedPayer     payPkg.NotificationTemplateParams
	EnachCreationSuccessPayer  payPkg.NotificationTemplateParams
	EnachCreationFailurePayer  payPkg.NotificationTemplateParams
	EnachExecutionSuccessPayer payPkg.NotificationTemplateParams
	EnachExecutionFailurePayer payPkg.NotificationTemplateParams
	EnachRevokeSuccess         payPkg.NotificationTemplateParams
	EnachRevokeFailure         payPkg.NotificationTemplateParams
}

type Server struct {
	Port            int
	HealthCheckPort int
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool
}

// AuthenticationTimeLimitForAction is map between recurring payment type to initiated by to action and the maximum time
// for which we will wait for user authorisation after which we will move order status to failed.
// The value change in case of payee initiated and payer initiated
type AuthenticationTimeLimitForAction map[string]map[string]map[string]time.Duration

// UpiMandateUrn is to store info for creating mandate urn
type UpiMandateUrn struct {
	CollectMandatePrefix string
}

type UpiMandateValidationParams struct {
	AllowedHoursForModification                  int
	MaximumAllowedDurationBetweenStartAndEndDate int
}

type SIExecutionParams struct {
	// Payment protocol via which standing instruction execution will be done
	PaymentProtocol int32 `dynamic:"true"`
}

type FeatureFlags struct {
	EnableRecurringPaymentCreationViaCelestial             bool `dynamic:"true"`
	EnableRecurringPaymentExecutionWithoutAuthViaCelestial bool
	EnableRecurringPaymentExecutionWithAuthViaCelestial    bool
	EnableRecurringPaymentModificationViaCelestial         bool `dynamic:"true"`
}

type RecurringPaymentCreationParams struct {
	AuthorisationTimeLimit time.Duration
}

type RecurringPaymentExecutionParams struct {
	AuthorisationTimeLimit time.Duration
}

type RecurringPaymentModificationParams struct {
	AuthorisationTimeLimit time.Duration
}

func loadConfig() (*Config, error) {
	var (
		koanf *koanf.Koanf
	)

	conf := &Config{}

	koanf, _, err = cfg.LoadWorkerConfigUsingKoanf(testEnvConfigDir(epifitemporal.RecurringPaymentWorker), "", cfg.ServiceName(epifitemporal.RecurringPaymentWorker))
	if err != nil {
		return nil, err
	}

	if err = koanf.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf)); err != nil {
		return nil, err
	}

	dbList := make([]*cfg.DB, 0)
	for _, db := range conf.DbConfigMap {
		dbList = append(dbList, db)
	}
	dbList = append(dbList, conf.RecurringPaymentDb)
	_, err = cfg.LoadSecretsAndPrepareDBConfig(conf.Secrets, conf.Application.Environment, conf.AWS.Region, dbList...)

	if err != nil {
		return nil, err
	}

	err = pgerrorcodes.LoadPaymentGatewayErrorMappings(conf.RazorPayResponseCodesJson)
	if err != nil {
		return nil, fmt.Errorf("error in loading razorpay error mappings from json: %w", err)
	}

	return conf, nil
}

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, nil
}

func testEnvConfigDir(worker epifitemporal.Worker) string {
	configPath := filepath.Join(b, "..", "..", "..", "..", "cmd", "worker", worker.GetDirectory(), "config")
	return configPath
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config, dbOwnershipMap map[commontypes.Ownership]*cfg.DB) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Namespace = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.HttpPort = intVal
	}

	if val, ok := os.LookupEnv("DB_HOST"); ok {
		cfg.OverwriteDbHost(dbOwnershipMap, val)
	}

	return nil
}
