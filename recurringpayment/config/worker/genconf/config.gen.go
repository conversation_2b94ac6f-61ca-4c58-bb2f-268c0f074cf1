// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"fmt"
	"reflect"
	"strings"
	"sync/atomic"

	questtypes "github.com/epifi/be-common/api/quest/types"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	v "github.com/epifi/be-common/pkg/cfg/v2"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	genconfig "github.com/epifi/gamma/order/config/genconf"
	pay "github.com/epifi/gamma/pkg/pay"
	worker "github.com/epifi/gamma/recurringpayment/config/worker"
)

type Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_EnableEntitySegregation               uint32
	_EnableEnachMandateEntitySegregation   uint32
	_Application                           *gencfg.TemporalWorkerApplication
	_SIExecutionParams                     *SIExecutionParams
	_FeatureFlags                          *FeatureFlags
	_OrderCacheConfig                      *genconfig.OrderCacheConfig
	_Server                                *cfg.ServerPorts
	_WorkflowParamsList                    v.WorkflowParamsList
	_DefaultActivityParamsList             v.ActivityParamsList
	_PausedWorkflowList                    v.PausedWorkflowsList
	_DbConfigMap                           cfg.DbConfigMap
	_UsecaseDbConfigMap                    cfg.UseCaseDbConfigMap
	_AWS                                   *cfg.AWS
	_OrderUpdatePublisher                  *cfg.SnsPublisher
	_WorkflowUpdatePublisher               *cfg.SnsPublisher
	_RecurringPaymentActionUpdatePublisher *cfg.SnsPublisher
	_Tracing                               *cfg.Tracing
	_Secrets                               *cfg.Secrets
	_Flags                                 *worker.Flags
	_AuthenticationTimeLimitForAction      *worker.AuthenticationTimeLimitForAction
	_RecurringPaymentNotificationParams    *worker.RecurringPaymentNotificationParams
	_UpiMandateUrn                         *worker.UpiMandateUrn
	_UpiMandateValidationParams            *worker.UpiMandateValidationParams
	_UpiMandateNotifDateFormat             string
	_RecurringPaymentFrequencyMapping      map[string]string
	_PaymentEnquiryParams                  *pay.PaymentEnquiryParams
	_RecurringPaymentCreationParams        *worker.RecurringPaymentCreationParams
	_SignalWorkflowPublisher               *cfg.SqsPublisher
	_RecurringPaymentExecutionParams       *worker.RecurringPaymentExecutionParams
	_RecurringPaymentModificationParams    *worker.RecurringPaymentModificationParams
	_EnachConfig                           *worker.EnachConfig
	_RecurringPaymentDb                    *cfg.DB
	_RazorPayResponseCodesJson             string
}

func (obj *Config) EnableEntitySegregation() bool {
	if atomic.LoadUint32(&obj._EnableEntitySegregation) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) EnableEnachMandateEntitySegregation() bool {
	if atomic.LoadUint32(&obj._EnableEnachMandateEntitySegregation) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) Application() *gencfg.TemporalWorkerApplication {
	return obj._Application
}
func (obj *Config) SIExecutionParams() *SIExecutionParams {
	return obj._SIExecutionParams
}
func (obj *Config) FeatureFlags() *FeatureFlags {
	return obj._FeatureFlags
}
func (obj *Config) OrderCacheConfig() *genconfig.OrderCacheConfig {
	return obj._OrderCacheConfig
}
func (obj *Config) Server() *cfg.ServerPorts {
	return obj._Server
}
func (obj *Config) WorkflowParamsList() v.WorkflowParamsList {
	return obj._WorkflowParamsList
}
func (obj *Config) DefaultActivityParamsList() v.ActivityParamsList {
	return obj._DefaultActivityParamsList
}
func (obj *Config) PausedWorkflowList() v.PausedWorkflowsList {
	return obj._PausedWorkflowList
}
func (obj *Config) DbConfigMap() cfg.DbConfigMap {
	return obj._DbConfigMap
}
func (obj *Config) UsecaseDbConfigMap() cfg.UseCaseDbConfigMap {
	return obj._UsecaseDbConfigMap
}
func (obj *Config) AWS() *cfg.AWS {
	return obj._AWS
}
func (obj *Config) OrderUpdatePublisher() *cfg.SnsPublisher {
	return obj._OrderUpdatePublisher
}
func (obj *Config) WorkflowUpdatePublisher() *cfg.SnsPublisher {
	return obj._WorkflowUpdatePublisher
}
func (obj *Config) RecurringPaymentActionUpdatePublisher() *cfg.SnsPublisher {
	return obj._RecurringPaymentActionUpdatePublisher
}
func (obj *Config) Tracing() *cfg.Tracing {
	return obj._Tracing
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}
func (obj *Config) Flags() *worker.Flags {
	return obj._Flags
}
func (obj *Config) AuthenticationTimeLimitForAction() *worker.AuthenticationTimeLimitForAction {
	return obj._AuthenticationTimeLimitForAction
}
func (obj *Config) RecurringPaymentNotificationParams() *worker.RecurringPaymentNotificationParams {
	return obj._RecurringPaymentNotificationParams
}
func (obj *Config) UpiMandateUrn() *worker.UpiMandateUrn {
	return obj._UpiMandateUrn
}
func (obj *Config) UpiMandateValidationParams() *worker.UpiMandateValidationParams {
	return obj._UpiMandateValidationParams
}
func (obj *Config) UpiMandateNotifDateFormat() string {
	return obj._UpiMandateNotifDateFormat
}
func (obj *Config) RecurringPaymentFrequencyMapping() map[string]string {
	return obj._RecurringPaymentFrequencyMapping
}
func (obj *Config) PaymentEnquiryParams() *pay.PaymentEnquiryParams {
	return obj._PaymentEnquiryParams
}
func (obj *Config) RecurringPaymentCreationParams() *worker.RecurringPaymentCreationParams {
	return obj._RecurringPaymentCreationParams
}
func (obj *Config) SignalWorkflowPublisher() *cfg.SqsPublisher {
	return obj._SignalWorkflowPublisher
}
func (obj *Config) RecurringPaymentExecutionParams() *worker.RecurringPaymentExecutionParams {
	return obj._RecurringPaymentExecutionParams
}
func (obj *Config) RecurringPaymentModificationParams() *worker.RecurringPaymentModificationParams {
	return obj._RecurringPaymentModificationParams
}
func (obj *Config) EnachConfig() *worker.EnachConfig {
	return obj._EnachConfig
}
func (obj *Config) RecurringPaymentDb() *cfg.DB {
	return obj._RecurringPaymentDb
}
func (obj *Config) RazorPayResponseCodesJson() string {
	return obj._RazorPayResponseCodesJson
}

type SIExecutionParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Payment protocol via which standing instruction execution will be done
	_PaymentProtocol int32
}

// Payment protocol via which standing instruction execution will be done
func (obj *SIExecutionParams) PaymentProtocol() int32 {
	return int32(atomic.LoadInt32(&obj._PaymentProtocol))
}

type FeatureFlags struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_EnableRecurringPaymentCreationViaCelestial             uint32
	_EnableRecurringPaymentModificationViaCelestial         uint32
	_EnableRecurringPaymentExecutionWithoutAuthViaCelestial bool
	_EnableRecurringPaymentExecutionWithAuthViaCelestial    bool
}

func (obj *FeatureFlags) EnableRecurringPaymentCreationViaCelestial() bool {
	if atomic.LoadUint32(&obj._EnableRecurringPaymentCreationViaCelestial) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *FeatureFlags) EnableRecurringPaymentModificationViaCelestial() bool {
	if atomic.LoadUint32(&obj._EnableRecurringPaymentModificationViaCelestial) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *FeatureFlags) EnableRecurringPaymentExecutionWithoutAuthViaCelestial() bool {
	return obj._EnableRecurringPaymentExecutionWithoutAuthViaCelestial
}
func (obj *FeatureFlags) EnableRecurringPaymentExecutionWithAuthViaCelestial() bool {
	return obj._EnableRecurringPaymentExecutionWithAuthViaCelestial
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enableentitysegregation"] = _obj.SetEnableEntitySegregation
	_setters["enableenachmandateentitysegregation"] = _obj.SetEnableEnachMandateEntitySegregation
	_Application, _fieldSetters := gencfg.NewTemporalWorkerApplication()
	_obj._Application = _Application
	helper.AddFieldSetters("application", _fieldSetters, _setters)
	_SIExecutionParams, _fieldSetters := NewSIExecutionParams()
	_obj._SIExecutionParams = _SIExecutionParams
	helper.AddFieldSetters("siexecutionparams", _fieldSetters, _setters)
	_FeatureFlags, _fieldSetters := NewFeatureFlags()
	_obj._FeatureFlags = _FeatureFlags
	helper.AddFieldSetters("featureflags", _fieldSetters, _setters)
	_OrderCacheConfig, _fieldSetters := genconfig.NewOrderCacheConfig()
	_obj._OrderCacheConfig = _OrderCacheConfig
	helper.AddFieldSetters("ordercacheconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enableentitysegregation"] = _obj.SetEnableEntitySegregation
	_setters["enableenachmandateentitysegregation"] = _obj.SetEnableEnachMandateEntitySegregation
	_Application, _fieldSetters := gencfg.NewTemporalWorkerApplication()
	_obj._Application = _Application
	helper.AddFieldSetters("application", _fieldSetters, _setters)
	_SIExecutionParams, _fieldSetters := NewSIExecutionParams()
	_obj._SIExecutionParams = _SIExecutionParams
	helper.AddFieldSetters("siexecutionparams", _fieldSetters, _setters)
	_FeatureFlags, _fieldSetters := NewFeatureFlags()
	_obj._FeatureFlags = _FeatureFlags
	helper.AddFieldSetters("featureflags", _fieldSetters, _setters)
	_OrderCacheConfig, _fieldSetters := genconfig.NewOrderCacheConfig()
	_obj._OrderCacheConfig = _OrderCacheConfig
	helper.AddFieldSetters("ordercacheconfig", _fieldSetters, _setters)

	return _obj, _setters
}

func (obj *Config) Init() {
	newObj, _ := NewConfig()
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*worker.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *worker.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enableentitysegregation":
		return obj.SetEnableEntitySegregation(v.EnableEntitySegregation, true, nil)
	case "enableenachmandateentitysegregation":
		return obj.SetEnableEnachMandateEntitySegregation(v.EnableEnachMandateEntitySegregation, true, nil)
	case "application":
		return obj._Application.Set(v.Application, true, path)
	case "siexecutionparams":
		return obj._SIExecutionParams.Set(v.SIExecutionParams, true, path)
	case "featureflags":
		return obj._FeatureFlags.Set(v.FeatureFlags, true, path)
	case "ordercacheconfig":
		return obj._OrderCacheConfig.Set(v.OrderCacheConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *worker.Config, dynamic bool, path []string) (err error) {

	err = obj.SetEnableEntitySegregation(v.EnableEntitySegregation, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableEnachMandateEntitySegregation(v.EnableEnachMandateEntitySegregation, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._Application.Set(v.Application, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SIExecutionParams.Set(v.SIExecutionParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FeatureFlags.Set(v.FeatureFlags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OrderCacheConfig.Set(v.OrderCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *worker.Config) error {

	obj._Server = v.Server
	obj._WorkflowParamsList = v.WorkflowParamsList
	obj._DefaultActivityParamsList = v.DefaultActivityParamsList
	obj._PausedWorkflowList = v.PausedWorkflowList
	obj._DbConfigMap = v.DbConfigMap
	obj._UsecaseDbConfigMap = v.UsecaseDbConfigMap
	obj._AWS = v.AWS
	obj._OrderUpdatePublisher = v.OrderUpdatePublisher
	obj._WorkflowUpdatePublisher = v.WorkflowUpdatePublisher
	obj._RecurringPaymentActionUpdatePublisher = v.RecurringPaymentActionUpdatePublisher
	obj._Tracing = v.Tracing
	obj._Secrets = v.Secrets
	obj._Flags = v.Flags
	obj._AuthenticationTimeLimitForAction = v.AuthenticationTimeLimitForAction
	obj._RecurringPaymentNotificationParams = v.RecurringPaymentNotificationParams
	obj._UpiMandateUrn = v.UpiMandateUrn
	obj._UpiMandateValidationParams = v.UpiMandateValidationParams
	obj._UpiMandateNotifDateFormat = v.UpiMandateNotifDateFormat
	obj._RecurringPaymentFrequencyMapping = v.RecurringPaymentFrequencyMapping
	obj._PaymentEnquiryParams = v.PaymentEnquiryParams
	obj._RecurringPaymentCreationParams = v.RecurringPaymentCreationParams
	obj._SignalWorkflowPublisher = v.SignalWorkflowPublisher
	obj._RecurringPaymentExecutionParams = v.RecurringPaymentExecutionParams
	obj._RecurringPaymentModificationParams = v.RecurringPaymentModificationParams
	obj._EnachConfig = v.EnachConfig
	obj._RecurringPaymentDb = v.RecurringPaymentDb
	obj._RazorPayResponseCodesJson = v.RazorPayResponseCodesJson
	return nil
}

func (obj *Config) SetEnableEntitySegregation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableEntitySegregation", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableEntitySegregation, 1)
	} else {
		atomic.StoreUint32(&obj._EnableEntitySegregation, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableEntitySegregation")
	}
	return nil
}
func (obj *Config) SetEnableEnachMandateEntitySegregation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableEnachMandateEntitySegregation", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableEnachMandateEntitySegregation, 1)
	} else {
		atomic.StoreUint32(&obj._EnableEnachMandateEntitySegregation, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableEnachMandateEntitySegregation")
	}
	return nil
}

func NewSIExecutionParams() (_obj *SIExecutionParams, _setters map[string]dynconf.SetFunc) {
	_obj = &SIExecutionParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["paymentprotocol"] = _obj.SetPaymentProtocol
	return _obj, _setters
}

func (obj *SIExecutionParams) Init() {
	newObj, _ := NewSIExecutionParams()
	*obj = *newObj
}

func (obj *SIExecutionParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SIExecutionParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*worker.SIExecutionParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *SIExecutionParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SIExecutionParams) setDynamicField(v *worker.SIExecutionParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "paymentprotocol":
		return obj.SetPaymentProtocol(v.PaymentProtocol, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SIExecutionParams) setDynamicFields(v *worker.SIExecutionParams, dynamic bool, path []string) (err error) {

	err = obj.SetPaymentProtocol(v.PaymentProtocol, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *SIExecutionParams) setStaticFields(v *worker.SIExecutionParams) error {

	return nil
}

func (obj *SIExecutionParams) SetPaymentProtocol(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *SIExecutionParams.PaymentProtocol", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._PaymentProtocol, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "PaymentProtocol")
	}
	return nil
}

func NewFeatureFlags() (_obj *FeatureFlags, _setters map[string]dynconf.SetFunc) {
	_obj = &FeatureFlags{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enablerecurringpaymentcreationviacelestial"] = _obj.SetEnableRecurringPaymentCreationViaCelestial
	_setters["enablerecurringpaymentmodificationviacelestial"] = _obj.SetEnableRecurringPaymentModificationViaCelestial
	return _obj, _setters
}

func (obj *FeatureFlags) Init() {
	newObj, _ := NewFeatureFlags()
	*obj = *newObj
}

func (obj *FeatureFlags) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *FeatureFlags) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*worker.FeatureFlags)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *FeatureFlags) setDynamicField(v *worker.FeatureFlags, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enablerecurringpaymentcreationviacelestial":
		return obj.SetEnableRecurringPaymentCreationViaCelestial(v.EnableRecurringPaymentCreationViaCelestial, true, nil)
	case "enablerecurringpaymentmodificationviacelestial":
		return obj.SetEnableRecurringPaymentModificationViaCelestial(v.EnableRecurringPaymentModificationViaCelestial, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *FeatureFlags) setDynamicFields(v *worker.FeatureFlags, dynamic bool, path []string) (err error) {

	err = obj.SetEnableRecurringPaymentCreationViaCelestial(v.EnableRecurringPaymentCreationViaCelestial, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableRecurringPaymentModificationViaCelestial(v.EnableRecurringPaymentModificationViaCelestial, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *FeatureFlags) setStaticFields(v *worker.FeatureFlags) error {

	obj._EnableRecurringPaymentExecutionWithoutAuthViaCelestial = v.EnableRecurringPaymentExecutionWithoutAuthViaCelestial
	obj._EnableRecurringPaymentExecutionWithAuthViaCelestial = v.EnableRecurringPaymentExecutionWithAuthViaCelestial
	return nil
}

func (obj *FeatureFlags) SetEnableRecurringPaymentCreationViaCelestial(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags.EnableRecurringPaymentCreationViaCelestial", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableRecurringPaymentCreationViaCelestial, 1)
	} else {
		atomic.StoreUint32(&obj._EnableRecurringPaymentCreationViaCelestial, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableRecurringPaymentCreationViaCelestial")
	}
	return nil
}
func (obj *FeatureFlags) SetEnableRecurringPaymentModificationViaCelestial(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags.EnableRecurringPaymentModificationViaCelestial", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableRecurringPaymentModificationViaCelestial, 1)
	} else {
		atomic.StoreUint32(&obj._EnableRecurringPaymentModificationViaCelestial, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableRecurringPaymentModificationViaCelestial")
	}
	return nil
}
