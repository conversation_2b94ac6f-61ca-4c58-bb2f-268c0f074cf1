// Code generated by tools/conf_gen/dynamic_conf_gen.go
package worker

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enableentitysegregation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableEntitySegregation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableEntitySegregation, nil
	case "enableenachmandateentitysegregation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableEnachMandateEntitySegregation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableEnachMandateEntitySegregation, nil
	case "application":
		return obj.Application.Get(dynamicFieldPath[1:])
	case "siexecutionparams":
		return obj.SIExecutionParams.Get(dynamicFieldPath[1:])
	case "featureflags":
		return obj.FeatureFlags.Get(dynamicFieldPath[1:])
	case "ordercacheconfig":
		return obj.OrderCacheConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *SIExecutionParams) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "paymentprotocol":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PaymentProtocol\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PaymentProtocol, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for SIExecutionParams", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *FeatureFlags) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enablerecurringpaymentcreationviacelestial":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableRecurringPaymentCreationViaCelestial\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableRecurringPaymentCreationViaCelestial, nil
	case "enablerecurringpaymentmodificationviacelestial":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableRecurringPaymentModificationViaCelestial\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableRecurringPaymentModificationViaCelestial, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for FeatureFlags", strings.Join(dynamicFieldPath, "."))
	}
}
