package workflow

import (
	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	celestialPb "github.com/epifi/be-common/api/celestial"
	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	rpPayloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/logger"
)

// ExecuteRecurringPaymentWithoutAuth - executes recurring payment where authorisation is not required.
// e.g. in the case of standing instruction authorisation is sanctioned by the payer at the time of creation and is not required while making the payment
// nolint: dupl
func ExecuteRecurringPaymentWithoutAuth(ctx workflow.Context, _ *workflowPb.Request) error {
	wfReqID := workflow.GetInfo(ctx).WorkflowExecution.ID
	lg := workflow.GetLogger(ctx)

	wfProcessingParams := &celestialPb.WorkflowProcessingParams{}
	if err := activityPkg.ExecuteRaw(ctx, epifitemporal.GetWorkflowProcessingParams, &wfProcessingParams, wfReqID); err != nil {
		lg.Error("activity failed", zap.String(logger.ACTIVITY, string(epifitemporal.GetWorkflowProcessingParams)), zap.Error(err))
		return err
	}

	// --------------- EXECUTE PAYMENT STAGE --------------------
	if err := processRecurringPaymentExecutionWithoutAuth(ctx, wfReqID, wfProcessingParams); err != nil {
		lg.Error("payment stage processing failed", zap.Error(err))
		return err
	}

	return nil
}

// processRecurringPaymentExecutionWithoutAuth processes payment stage for a recurring payment execution without auth
func processRecurringPaymentExecutionWithoutAuth(ctx workflow.Context, wfReqID string, wfProcessingParams *celestialPb.WorkflowProcessingParams) error {
	// Step 1: initiate stage by creating appropriate entries in the DB
	if err := activityPkg.ExecuteRaw(ctx, epifitemporal.InitiateWorkflowStage, nil, wfReqID, workflowPb.Stage_PAYMENT, stagePb.Status_INITIATED); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStage, err)
	}

	if err := activityPkg.ExecuteRaw(ctx, epifitemporal.UpdateOrderWorkflowRefID, nil, wfProcessingParams.GetClientReqId().GetId(), wfReqID); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateOrderWorkflowRefID, err)
	}

	if err := activityPkg.ExecuteRaw(ctx, epifitemporal.UpdateOrder, nil, wfProcessingParams.GetClientReqId().GetId(), orderPb.OrderStatus_IN_PAYMENT); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateOrder, err)
	}
	v := workflow.GetVersion(ctx, "publishing-non-terminal-updates-in-order-update-topic", workflow.DefaultVersion, 1)
	if v == 1 {
		if err := activityPkg.Execute(ctx, epifitemporal.PublishOrderUpdate, &activityPb.Response{}, &activityPb.Request{
			ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
			Payload:     wfProcessingParams.GetPayload(),
		}); err != nil {
			return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateOrder, err)
		}
	}

	// Step 2: invoke domain activities to actually carry out the business logic specific to workflow and domain
	finalOrderStatus, finalCelestialWorkflowStatus, err := processExecutionWithoutAuth(ctx, wfProcessingParams)
	if err != nil {
		return fmt.Errorf("processExecutionNoAuth failed: %w", err)
	}

	// Step 3: update the DB entries and publish the events
	if err = activityPkg.ExecuteRaw(ctx, epifitemporal.UpdateOrder, nil, wfProcessingParams.GetClientReqId().GetId(), finalOrderStatus); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateOrder, err)
	}

	if err = activityPkg.ExecuteRaw(ctx, epifitemporal.PublishOrderUpdate, nil, &activityPb.Request{
		ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
		Payload:     wfProcessingParams.GetPayload(),
	}); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.PublishOrderUpdate, err)
	}

	if err = activityPkg.ExecuteRaw(ctx, epifitemporal.UpdateWorkflowStageStatus, nil, wfReqID, workflowPb.Stage_PAYMENT, finalCelestialWorkflowStatus); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStageStatus, err)
	}

	if err = activityPkg.ExecuteRaw(ctx, epifitemporal.PublishWorkflowUpdateEvent, nil, wfReqID); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.PublishWorkflowUpdateEvent, err)
	}

	// Wait for reversal signal for two days. If reversal happened then mark order status reversed else end the workflow.
	v = workflow.GetVersion(ctx, "fix-failed-txn-reversal", workflow.DefaultVersion, 1)
	if v == 1 {
		var paymentReversalOrderStatus orderPb.OrderStatus
		if finalOrderStatus == orderPb.OrderStatus_PAYMENT_FAILED {
			paymentReversalOrderStatus = handleReversalInCaseOfPaymentFailure(ctx)
		}
		if paymentReversalOrderStatus == orderPb.OrderStatus_PAYMENT_REVERSED {
			// update the DB entries and publish the events
			if err = activityPkg.ExecuteRaw(ctx, epifitemporal.UpdateOrder, nil, wfProcessingParams.GetClientReqId().GetId(), paymentReversalOrderStatus); err != nil {
				return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateOrder, err)
			}

			if err = activityPkg.ExecuteRaw(ctx, epifitemporal.PublishOrderUpdate, nil, &activityPb.Request{
				ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
				Payload:     wfProcessingParams.GetPayload(),
			}); err != nil {
				return fmt.Errorf("%s activity failed: %w", epifitemporal.PublishOrderUpdate, err)
			}
		}
	}

	return nil
}

// processExecutionWithoutAuth - It takes workflow processing params as input and does payment processing
// using domain activity  `ProcessRecurringPaymentExecution`. Since the request is asynchronous in nature, it fires
// the activity asynchronously and parallelly waits for the corresponding callback to return.
// The final status of the payment is decided on a first come first basis.
func processExecutionWithoutAuth(ctx workflow.Context, wfProcessingParams *celestialPb.WorkflowProcessingParams) (orderPb.OrderStatus, stagePb.Status, error) {
	var (
		finalOrderStatus             orderPb.OrderStatus
		finalCelestialWorkflowStatus stagePb.Status
	)
	processPaymentFuture, err := activityPkg.ExecuteAsyncRaw(ctx, rpNs.ProcessRecurringPaymentExecution, &activityPb.Request{
		ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
		Payload:     wfProcessingParams.GetPayload(),
	})
	if err != nil {
		return orderPb.OrderStatus_ORDER_STATUS_UNSPECIFIED, stagePb.Status_STATUS_UNSPECIFIED,
			fmt.Errorf("%s activity initiation failed: %w", rpNs.ProcessRecurringPaymentExecution, err)
	}

	for finalCelestialWorkflowStatus == stagePb.Status_STATUS_UNSPECIFIED {
		workflow.NewSelector(ctx).
			AddFuture(processPaymentFuture, func(f workflow.Future) {
				workflow.GetLogger(ctx).Info("ProcessRecurringPaymentExecution future finished")
				res := &activityPb.Response{}
				processPaymentErr := f.Get(ctx, &res)
				switch {
				case processPaymentErr != nil && !epifitemporal.IsRetryableError(processPaymentErr):
					finalOrderStatus = orderPb.OrderStatus_PAYMENT_FAILED
					finalCelestialWorkflowStatus = stagePb.Status_FAILED
				case processPaymentErr != nil:
					finalOrderStatus = orderPb.OrderStatus_MANUAL_INTERVENTION
					finalCelestialWorkflowStatus = stagePb.Status_MANUAL_INTERVENTION
				default:
					finalOrderStatus = orderPb.OrderStatus_PAID
					finalCelestialWorkflowStatus = stagePb.Status_SUCCESSFUL
				}
			}).
			AddReceive(workflow.GetSignalChannel(ctx, string(rpNs.ExecuteRecurringPaymentWithoutAuthCallbackSignal)), func(c workflow.ReceiveChannel, more bool) {
				workflow.GetLogger(ctx).Info("received callback signal")
				var payload []byte
				c.Receive(ctx, &payload)

				// On receiving signal, ProcessRecurringPaymentExecution need to be called as it require to change the ActivityActionState and transaction.
				v := workflow.GetVersion(ctx, "ProcessRecurringPaymentExecutionOnSignalFix", workflow.DefaultVersion, 1)
				if v == 1 {

					// finalOrderStatus and finalCelestialWorkflowStatus need to be the latest status from ProcessRecurringPaymentExecution
					processRecurringPayResOnSignal := &activityPb.Response{}
					err = activityPkg.Execute(ctx, rpNs.ProcessRecurringPaymentExecution, processRecurringPayResOnSignal, &activityPb.Request{
						ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
						Payload:     wfProcessingParams.GetPayload(),
					})
					switch {
					case err != nil && !epifitemporal.IsRetryableError(err):
						finalOrderStatus = orderPb.OrderStatus_PAYMENT_FAILED
						finalCelestialWorkflowStatus = stagePb.Status_FAILED
					case err != nil:
						finalOrderStatus = orderPb.OrderStatus_MANUAL_INTERVENTION
						finalCelestialWorkflowStatus = stagePb.Status_MANUAL_INTERVENTION
					default:
						finalOrderStatus = orderPb.OrderStatus_PAID
						finalCelestialWorkflowStatus = stagePb.Status_SUCCESSFUL
					}
				} else {
					sig := &rpPayloadPb.ExecuteRecurringPaymentWithoutAuthCallbackSignal{}
					// Unmarshal payload from []bytes to ExecuteRecurringPaymentCallbackSignal
					err = protojson.Unmarshal(payload, sig)
					if err != nil {
						workflow.GetLogger(ctx).Error("failed to unmarshal payload received", zap.Error(err))
						return
					}
					switch sig.GetTransactionStatus() { // nolint:exhaustive
					case paymentPb.TransactionStatus_SUCCESS:
						finalOrderStatus = orderPb.OrderStatus_PAID
						finalCelestialWorkflowStatus = stagePb.Status_SUCCESSFUL
					case paymentPb.TransactionStatus_FAILED:
						finalOrderStatus = orderPb.OrderStatus_PAYMENT_FAILED
						finalCelestialWorkflowStatus = stagePb.Status_FAILED
					case paymentPb.TransactionStatus_MANUAL_INTERVENTION:
						finalOrderStatus = orderPb.OrderStatus_MANUAL_INTERVENTION
						finalCelestialWorkflowStatus = stagePb.Status_MANUAL_INTERVENTION
					}
				}
			}).
			Select(ctx)
	}

	return finalOrderStatus, finalCelestialWorkflowStatus, nil
}

// handleReversalInCaseOfPaymentFailure will wait for 2 days for reversal signal. If received it will return order status as PAYMENT_REVERSED
// else it will return ORDER_STATUS_UNSPECIFIED.
func handleReversalInCaseOfPaymentFailure(ctx workflow.Context) orderPb.OrderStatus {
	var (
		finalOrderStatus orderPb.OrderStatus
	)

	workflow.NewSelector(ctx).AddReceive(workflow.GetSignalChannel(ctx, string(rpNs.ExecuteRecurringPaymentWithoutAuthReversalSignal)), func(channel workflow.ReceiveChannel, more bool) {
		var executeRecurringPaymentWithoutAuthPayload rpPayloadPb.ExecuteRecurringPaymentWithoutAuthReversalSignal
		channel.Receive(ctx, &executeRecurringPaymentWithoutAuthPayload)

		if executeRecurringPaymentWithoutAuthPayload.GetTransactionStatus() == paymentPb.TransactionStatus_REVERSED {
			finalOrderStatus = orderPb.OrderStatus_PAYMENT_REVERSED
		}
	}).AddFuture(workflow.NewTimer(ctx, 48*time.Hour), func(f workflow.Future) {
		finalOrderStatus = orderPb.OrderStatus_ORDER_STATUS_UNSPECIFIED
	}).Select(ctx)

	return finalOrderStatus
}
