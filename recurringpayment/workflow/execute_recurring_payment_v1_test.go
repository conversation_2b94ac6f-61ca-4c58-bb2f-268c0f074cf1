package workflow

import (
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.temporal.io/sdk/temporal"

	"go.temporal.io/sdk/testsuite"
	"google.golang.org/protobuf/encoding/protojson"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/pay/pgerrorcodes"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	rpPayloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	celestialActivityV2 "github.com/epifi/gamma/celestial/activity/v2"
	rpActivity "github.com/epifi/gamma/recurringpayment/activity"
)

func TestExecuteRecurringPaymentWithoutAuthV1(t *testing.T) {

	const (
		defaultWorkflowID               = "default-test-workflow-id"
		defaultRecurringPaymentId       = "default_test_recurring_payment_id"
		defaultRecurringPaymentActionId = "default_test_recurring_payment_action_id"
	)
	var (
		clientReqId                            = uuid.New().String()
		defaultRequestHeaderWithoutClientReqId = &activityPb.RequestHeader{
			Ownership: commontypes.Ownership_EPIFI_TECH,
		}
		defaultRequestHeader = &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   commontypes.Ownership_EPIFI_TECH,
		}
		defaultRecurringPaymentTypeSpecificPayload = &rpPayloadPb.RecurringPaymentTypeSpecificExecutionPayload{}

		workflowPayload = &rpPayloadPb.ExecuteRecurringPaymentWithoutAuthV1WorkflowPayload{
			RecurringPaymentId:                  defaultRecurringPaymentId,
			RecurringPaymentActionId:            defaultRecurringPaymentActionId,
			RecurringPaymentTypeSpecificPayload: defaultRecurringPaymentTypeSpecificPayload,
		}
		marshalledWfPayload, _               = protojson.Marshal(workflowPayload)
		defaultWorkflowProcessingParamsV2Req = &activityPb.GetWorkflowProcessingParamsV2Request{
			RequestHeader: defaultRequestHeaderWithoutClientReqId,
			WfReqId:       defaultWorkflowID,
		}
		defaultWorkflowProcessingParamsV2Res = &activityPb.GetWorkflowProcessingParamsV2Response{
			WfReqParams: &workflowPb.ProcessingParams{
				ClientReqId: &workflowPb.ClientReqId{
					Id: clientReqId,
				},
				Payload: marshalledWfPayload,
			},
		}
		defaultInitiateExecutionAtDomainReq = &rpActivityPb.InitiateExecutionAtDomainRequest{
			RequestHeader:                       defaultRequestHeader,
			RecurringPaymentId:                  defaultRecurringPaymentId,
			RecurringPaymentActionId:            defaultRecurringPaymentActionId,
			RecurringPaymentTypeSpecificPayload: defaultRecurringPaymentTypeSpecificPayload,
		}
		successfulInitiateExecutionAtDomainRes = &rpActivityPb.InitiateExecutionAtDomainResponse{
			DetailedStatus: &rpPb.ActionDetailedStatusInfo{
				FiStatusCode: pgerrorcodes.PaymentGatewaySuccessFiStatusCode,
			},
		}
		businessFailureInitiateExecutionAtDomainRes = &rpActivityPb.InitiateExecutionAtDomainResponse{
			DetailedStatus: &rpPb.ActionDetailedStatusInfo{
				FiStatusCode:     "RZP100",
				ErrorDescription: "International card not supported",
			},
		}
		defaultUpdateRecurringPaymentActionStatueReq = &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
			RecurringPaymentActionId: defaultRecurringPaymentActionId,
			CurrentStatus:            rpPb.ActionState_ACTION_CREATED,
			NextStatus:               rpPb.ActionState_ACTION_IN_PROGRESS,
			ActionDetailedStatusInfo: &rpPb.ActionDetailedStatusInfo{
				FiStatusCode: pgerrorcodes.PaymentGatewaySuccessFiStatusCode,
			},
		}
		businessFailureUpdateRecurringPaymentActionStatueReq = &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
			RecurringPaymentActionId: defaultRecurringPaymentActionId,
			CurrentStatus:            rpPb.ActionState_ACTION_CREATED,
			NextStatus:               rpPb.ActionState_ACTION_FAILURE,
			ActionDetailedStatusInfo: &rpPb.ActionDetailedStatusInfo{
				FiStatusCode:     "RZP100",
				ErrorDescription: "International card not supported",
			},
		}
		defaultEnquireExecutionStatusAtDomainReq = &rpActivityPb.EnquireExecutionStatusAtDomainRequest{
			RequestHeader:            defaultRequestHeader,
			RecurringPaymentActionId: defaultRecurringPaymentActionId,
		}
		defaultPublishRecurringPaymentActionUpdateEventReq = &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
			RecurringPaymentActionId: defaultRecurringPaymentActionId,
		}
		defaultCheckAndUpdateRecurringPaymentToCompletedStateReq = &rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateRequest{
			RequestHeader:      defaultRequestHeader,
			RecurringPaymentId: defaultRecurringPaymentId,
		}
		defaultSendStatusNotificationReq = &rpActivityPb.SendStatusNotificationRequest{
			RequestHeader:            defaultRequestHeader,
			RecurringPaymentId:       defaultRecurringPaymentId,
			RecurringPaymentActionId: defaultRecurringPaymentActionId,
		}

		temporalPermanentErr = temporal.NewNonRetryableApplicationError("Permanent Error", "", epifierrors.ErrPermanent)
	)

	tests := []struct {
		name              string
		setupMocks        func(env *testsuite.TestWorkflowEnvironment)
		setupExpectations func(t *testing.T, env *testsuite.TestWorkflowEnvironment)
		wantErr           bool
	}{
		// Testing workflow initiation
		{
			name: "should return error (failed to GetWorkflowProcessingParamsV2)",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(nil, temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 0)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 0)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 0)

				env.AssertNumberOfCalls(t, string(rpNs.InitiateExecutionAtDomain), 0)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 0)
			},
			wantErr: true,
		},
		// Testing InitiateExecutionATDomain stage
		{
			name: "should return error (failed to InitiateWorkflowStageV2)",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).
					Return(temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 0)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 0)

				env.AssertNumberOfCalls(t, string(rpNs.InitiateExecutionAtDomain), 0)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 0)
			},
			wantErr: true,
		},
		{
			name: "should return error (activity InitiateExecutionAtDomainResponse failed)",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.InitiateExecutionAtDomain), mock.Anything, defaultInitiateExecutionAtDomainReq).
					Return(nil, temporalPermanentErr)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_MANUAL_INTERVENTION,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 0)

				env.AssertNumberOfCalls(t, string(rpNs.InitiateExecutionAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 0)
			},
			wantErr: true,
		},
		{
			name: "should return error (failed to mark the workflow stage to manual intervention post InitiateExecutionAtDomainResponse is failed)",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.InitiateExecutionAtDomain), mock.Anything, defaultInitiateExecutionAtDomainReq).
					Return(nil, temporalPermanentErr)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_MANUAL_INTERVENTION,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).Return(temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 0)

				env.AssertNumberOfCalls(t, string(rpNs.InitiateExecutionAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 0)
			},
			wantErr: true,
		},
		{
			name: "should return error (activity UpdateRecurringPaymentActionStatus failed)",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.InitiateExecutionAtDomain), mock.Anything, defaultInitiateExecutionAtDomainReq).
					Return(successfulInitiateExecutionAtDomainRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, defaultUpdateRecurringPaymentActionStatueReq).
					Return(nil, temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 0)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 0)

				env.AssertNumberOfCalls(t, string(rpNs.InitiateExecutionAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
			},
			wantErr: true,
		},
		{
			name: "should return error (failed to update the workflow stage to successful)",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.InitiateExecutionAtDomain), mock.Anything, defaultInitiateExecutionAtDomainReq).
					Return(successfulInitiateExecutionAtDomainRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, defaultUpdateRecurringPaymentActionStatueReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).Return(temporalPermanentErr)

			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 0)

				env.AssertNumberOfCalls(t, string(rpNs.InitiateExecutionAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
			},
			wantErr: true,
		},
		// Testing EnquireExecutionStatusATDomain stage
		{
			name: "should return error (failed to InitiateWorkflowStageV2)",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.InitiateExecutionAtDomain), mock.Anything, defaultInitiateExecutionAtDomainReq).
					Return(successfulInitiateExecutionAtDomainRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, defaultUpdateRecurringPaymentActionStatueReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.EnquireExecutionStatusATDomain),
				}).
					Return(temporalPermanentErr)

			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 1)

				env.AssertNumberOfCalls(t, string(rpNs.InitiateExecutionAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireExecutionStatusAtDomain), 0)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 0)
			},
			wantErr: true,
		},
		{
			name: "should return error (activity EnquireExecutionStatusAtDomain failed)",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.InitiateExecutionAtDomain), mock.Anything, defaultInitiateExecutionAtDomainReq).
					Return(successfulInitiateExecutionAtDomainRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, defaultUpdateRecurringPaymentActionStatueReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.EnquireExecutionStatusATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.EnquireExecutionStatusAtDomain), mock.Anything, defaultEnquireExecutionStatusAtDomainReq).
					Return(nil, temporalPermanentErr)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_MANUAL_INTERVENTION,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.EnquireExecutionStatusATDomain),
				}).Return(nil)

			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 1)

				env.AssertNumberOfCalls(t, string(rpNs.InitiateExecutionAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireExecutionStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 0)
			},
			wantErr: true,
		},
		{
			name: "should return error (failed to mark the stage to manual intervention post EnquireExecutionStatusAtDomain is failed)",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.InitiateExecutionAtDomain), mock.Anything, defaultInitiateExecutionAtDomainReq).
					Return(successfulInitiateExecutionAtDomainRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, defaultUpdateRecurringPaymentActionStatueReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.EnquireExecutionStatusATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.EnquireExecutionStatusAtDomain), mock.Anything, defaultEnquireExecutionStatusAtDomainReq).
					Return(nil, temporalPermanentErr)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_MANUAL_INTERVENTION,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.EnquireExecutionStatusATDomain),
				}).Return(temporalPermanentErr)

			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 1)

				env.AssertNumberOfCalls(t, string(rpNs.InitiateExecutionAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireExecutionStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 0)
			},
			wantErr: true,
		},
		{
			name: "should return error (non-terminal execution status returned)",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.InitiateExecutionAtDomain), mock.Anything, defaultInitiateExecutionAtDomainReq).
					Return(successfulInitiateExecutionAtDomainRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, defaultUpdateRecurringPaymentActionStatueReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.EnquireExecutionStatusATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.EnquireExecutionStatusAtDomain), mock.Anything, defaultEnquireExecutionStatusAtDomainReq).
					Return(&rpActivityPb.EnquireExecutionStatusAtDomainResponse{
						ExecutionStatus: rpActivityPb.EnquireExecutionStatusAtDomainResponse_DOMAIN_EXECUTION_STATUS_UNSPECIFIED,
					}, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_MANUAL_INTERVENTION,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.EnquireExecutionStatusATDomain),
				}).Return(nil)

			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 1)

				env.AssertNumberOfCalls(t, string(rpNs.InitiateExecutionAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireExecutionStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 0)
			},
			wantErr: true,
		},
		{
			name: "should return error (activity UpdateRecurringPaymentActionStatus failed)",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.InitiateExecutionAtDomain), mock.Anything, defaultInitiateExecutionAtDomainReq).
					Return(successfulInitiateExecutionAtDomainRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, defaultUpdateRecurringPaymentActionStatueReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.EnquireExecutionStatusATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.EnquireExecutionStatusAtDomain), mock.Anything, defaultEnquireExecutionStatusAtDomainReq).
					Return(&rpActivityPb.EnquireExecutionStatusAtDomainResponse{
						ExecutionStatus: rpActivityPb.EnquireExecutionStatusAtDomainResponse_DOMAIN_EXECUTION_STATUS_SUCCESS,
					}, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
					CurrentStatus:            rpPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
				}).Return(nil, temporalPermanentErr)

			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 1)

				env.AssertNumberOfCalls(t, string(rpNs.InitiateExecutionAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireExecutionStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 0)
			},
			wantErr: true,
		},
		{
			name: "should return error (activity PublishRecurringPaymentActionUpdateEvent failed)",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.InitiateExecutionAtDomain), mock.Anything, defaultInitiateExecutionAtDomainReq).
					Return(successfulInitiateExecutionAtDomainRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, defaultUpdateRecurringPaymentActionStatueReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.EnquireExecutionStatusATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.EnquireExecutionStatusAtDomain), mock.Anything, defaultEnquireExecutionStatusAtDomainReq).
					Return(&rpActivityPb.EnquireExecutionStatusAtDomainResponse{
						ExecutionStatus: rpActivityPb.EnquireExecutionStatusAtDomainResponse_DOMAIN_EXECUTION_STATUS_SUCCESS,
					}, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
					CurrentStatus:            rpPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
				}).Return(nil, nil)
				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, defaultPublishRecurringPaymentActionUpdateEventReq).
					Return(nil, temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 1)

				env.AssertNumberOfCalls(t, string(rpNs.InitiateExecutionAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireExecutionStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 1)
			},
			wantErr: true,
		},
		{
			name: "should return error (failed to update the stage status to successful)",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.InitiateExecutionAtDomain), mock.Anything, defaultInitiateExecutionAtDomainReq).
					Return(successfulInitiateExecutionAtDomainRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, defaultUpdateRecurringPaymentActionStatueReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.EnquireExecutionStatusATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.EnquireExecutionStatusAtDomain), mock.Anything, defaultEnquireExecutionStatusAtDomainReq).
					Return(&rpActivityPb.EnquireExecutionStatusAtDomainResponse{
						ExecutionStatus: rpActivityPb.EnquireExecutionStatusAtDomainResponse_DOMAIN_EXECUTION_STATUS_SUCCESS,
					}, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
					CurrentStatus:            rpPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
				}).Return(nil, nil)
				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, defaultPublishRecurringPaymentActionUpdateEventReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.EnquireExecutionStatusATDomain),
				}).Return(temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 1)

				env.AssertNumberOfCalls(t, string(rpNs.InitiateExecutionAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireExecutionStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 1)
			},
			wantErr: true,
		},
		// Testing ExecuteRecurringPaymentPostSuccessProcessing stage
		{
			name: "should return error (failed to initiate the workflow stage)",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.InitiateExecutionAtDomain), mock.Anything, defaultInitiateExecutionAtDomainReq).
					Return(successfulInitiateExecutionAtDomainRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, defaultUpdateRecurringPaymentActionStatueReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.EnquireExecutionStatusATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.EnquireExecutionStatusAtDomain), mock.Anything, defaultEnquireExecutionStatusAtDomainReq).
					Return(&rpActivityPb.EnquireExecutionStatusAtDomainResponse{
						ExecutionStatus: rpActivityPb.EnquireExecutionStatusAtDomainResponse_DOMAIN_EXECUTION_STATUS_SUCCESS,
					}, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
					CurrentStatus:            rpPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
				}).Return(nil, nil)
				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, defaultPublishRecurringPaymentActionUpdateEventReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.EnquireExecutionStatusATDomain),
				}).Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ExecuteRecurringPaymentPostSuccessProcessing),
				}).
					Return(temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 2)

				env.AssertNumberOfCalls(t, string(rpNs.InitiateExecutionAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireExecutionStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 1)
				env.AssertNumberOfCalls(t, string(rpNs.CheckAndUpdateRecurringPaymentToCompletedState), 0)
			},
			wantErr: true,
		},
		{
			name: "should return error (CheckAndUpdateRecurringPaymentToCompletedState activity failed)",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.InitiateExecutionAtDomain), mock.Anything, defaultInitiateExecutionAtDomainReq).
					Return(successfulInitiateExecutionAtDomainRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, defaultUpdateRecurringPaymentActionStatueReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.EnquireExecutionStatusATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.EnquireExecutionStatusAtDomain), mock.Anything, defaultEnquireExecutionStatusAtDomainReq).
					Return(&rpActivityPb.EnquireExecutionStatusAtDomainResponse{
						ExecutionStatus: rpActivityPb.EnquireExecutionStatusAtDomainResponse_DOMAIN_EXECUTION_STATUS_SUCCESS,
					}, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
					CurrentStatus:            rpPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
				}).Return(nil, nil)
				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, defaultPublishRecurringPaymentActionUpdateEventReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.EnquireExecutionStatusATDomain),
				}).Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ExecuteRecurringPaymentPostSuccessProcessing),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.CheckAndUpdateRecurringPaymentToCompletedState), mock.Anything, defaultCheckAndUpdateRecurringPaymentToCompletedStateReq).
					Return(nil, temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 2)

				env.AssertNumberOfCalls(t, string(rpNs.InitiateExecutionAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireExecutionStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 1)
				env.AssertNumberOfCalls(t, string(rpNs.CheckAndUpdateRecurringPaymentToCompletedState), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 0)
			},
			wantErr: true,
		},
		{
			name: "should return error (SendStatusNotificationRequest activity failed)",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.InitiateExecutionAtDomain), mock.Anything, defaultInitiateExecutionAtDomainReq).
					Return(successfulInitiateExecutionAtDomainRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, defaultUpdateRecurringPaymentActionStatueReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.EnquireExecutionStatusATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.EnquireExecutionStatusAtDomain), mock.Anything, defaultEnquireExecutionStatusAtDomainReq).
					Return(&rpActivityPb.EnquireExecutionStatusAtDomainResponse{
						ExecutionStatus: rpActivityPb.EnquireExecutionStatusAtDomainResponse_DOMAIN_EXECUTION_STATUS_SUCCESS,
					}, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
					CurrentStatus:            rpPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
				}).Return(nil, nil)
				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, defaultPublishRecurringPaymentActionUpdateEventReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.EnquireExecutionStatusATDomain),
				}).Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ExecuteRecurringPaymentPostSuccessProcessing),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.CheckAndUpdateRecurringPaymentToCompletedState), mock.Anything, defaultCheckAndUpdateRecurringPaymentToCompletedStateReq).
					Return(nil, nil)
				env.OnActivity(string(rpNs.SendStatusNotification), mock.Anything, defaultSendStatusNotificationReq).
					Return(nil, temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 2)

				env.AssertNumberOfCalls(t, string(rpNs.InitiateExecutionAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireExecutionStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 1)
				env.AssertNumberOfCalls(t, string(rpNs.CheckAndUpdateRecurringPaymentToCompletedState), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 1)
			},
			wantErr: true,
		},
		{
			name: "should return error (failed to update the stage status to successful)",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.InitiateExecutionAtDomain), mock.Anything, defaultInitiateExecutionAtDomainReq).
					Return(successfulInitiateExecutionAtDomainRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, defaultUpdateRecurringPaymentActionStatueReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.EnquireExecutionStatusATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.EnquireExecutionStatusAtDomain), mock.Anything, defaultEnquireExecutionStatusAtDomainReq).
					Return(&rpActivityPb.EnquireExecutionStatusAtDomainResponse{
						ExecutionStatus: rpActivityPb.EnquireExecutionStatusAtDomainResponse_DOMAIN_EXECUTION_STATUS_SUCCESS,
					}, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
					CurrentStatus:            rpPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
				}).Return(nil, nil)
				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, defaultPublishRecurringPaymentActionUpdateEventReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.EnquireExecutionStatusATDomain),
				}).Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ExecuteRecurringPaymentPostSuccessProcessing),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.CheckAndUpdateRecurringPaymentToCompletedState), mock.Anything, defaultCheckAndUpdateRecurringPaymentToCompletedStateReq).
					Return(nil, nil)
				env.OnActivity(string(rpNs.SendStatusNotification), mock.Anything, defaultSendStatusNotificationReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ExecuteRecurringPaymentPostSuccessProcessing),
				}).Return(temporalPermanentErr)

			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 2)

				env.AssertNumberOfCalls(t, string(rpNs.InitiateExecutionAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireExecutionStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 1)
				env.AssertNumberOfCalls(t, string(rpNs.CheckAndUpdateRecurringPaymentToCompletedState), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 1)
			},
			wantErr: true,
		},
		{
			name: "Happy flow (all the stages are successful)",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.InitiateExecutionAtDomain), mock.Anything, defaultInitiateExecutionAtDomainReq).
					Return(successfulInitiateExecutionAtDomainRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, defaultUpdateRecurringPaymentActionStatueReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.EnquireExecutionStatusATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.EnquireExecutionStatusAtDomain), mock.Anything, defaultEnquireExecutionStatusAtDomainReq).
					Return(&rpActivityPb.EnquireExecutionStatusAtDomainResponse{
						ExecutionStatus: rpActivityPb.EnquireExecutionStatusAtDomainResponse_DOMAIN_EXECUTION_STATUS_SUCCESS,
					}, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
					CurrentStatus:            rpPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
				}).Return(nil, nil)
				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, defaultPublishRecurringPaymentActionUpdateEventReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.EnquireExecutionStatusATDomain),
				}).Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ExecuteRecurringPaymentPostSuccessProcessing),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.CheckAndUpdateRecurringPaymentToCompletedState), mock.Anything, defaultCheckAndUpdateRecurringPaymentToCompletedStateReq).
					Return(nil, nil)
				env.OnActivity(string(rpNs.SendStatusNotification), mock.Anything, defaultSendStatusNotificationReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ExecuteRecurringPaymentPostSuccessProcessing),
				}).Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 3)

				env.AssertNumberOfCalls(t, string(rpNs.InitiateExecutionAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireExecutionStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 1)
				env.AssertNumberOfCalls(t, string(rpNs.CheckAndUpdateRecurringPaymentToCompletedState), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 1)
			},
		},
		{
			name: "Should return error when there is business decline in InitiateExecutionAtDomain",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.InitiateExecutionAtDomain), mock.Anything, defaultInitiateExecutionAtDomainReq).
					Return(businessFailureInitiateExecutionAtDomainRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, businessFailureUpdateRecurringPaymentActionStatueReq).
					Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_FAILED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.InitiateExecutionATDomain),
				}).Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: defaultRequestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, defaultPublishRecurringPaymentActionUpdateEventReq).
					Return(nil, nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 1)

				env.AssertNumberOfCalls(t, string(rpNs.InitiateExecutionAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireExecutionStatusAtDomain), 0)
				env.AssertNumberOfCalls(t, string(rpNs.CheckAndUpdateRecurringPaymentToCompletedState), 0)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 0)
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			env := wts.NewTestWorkflowEnvironment()
			env.RegisterWorkflow(ExecuteRecurringPaymentWithoutAuthV1)
			env.RegisterActivity(&celestialActivityV2.Processor{})
			env.RegisterActivity(&rpActivity.Processor{})

			tt.setupMocks(env)
			env.ExecuteWorkflow(ExecuteRecurringPaymentWithoutAuthV1, &rpPayloadPb.ExecuteRecurringPaymentWithoutAuthV1WorkflowPayload{})
			assert.True(t, env.IsWorkflowCompleted())
			if (env.GetWorkflowError() != nil) != tt.wantErr {
				t.Errorf("ExecuteRecurringPaymentWithoutAuthV1() error = %v, wantErr %v", env.GetWorkflowError(), tt.wantErr)
			}
			tt.setupExpectations(t, env)
			env.AssertExpectations(t)
		})
	}
}
