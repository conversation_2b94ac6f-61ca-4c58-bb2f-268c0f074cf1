package workflow_test

import (
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/protobuf/encoding/protojson"

	celestialPb "github.com/epifi/be-common/api/celestial"
	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifitemporal"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/money"

	notificationPb "github.com/epifi/gamma/api/celestial/activity/notification"
	commPb "github.com/epifi/gamma/api/comms"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	rpPayloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	celestialActivityOld "github.com/epifi/gamma/celestial/activity"
	celestialActivity "github.com/epifi/gamma/celestial/activity/v2"
	rpActivity "github.com/epifi/gamma/recurringpayment/activity"
	rpWorkflow "github.com/epifi/gamma/recurringpayment/workflow"
)

func TestPauseUnpauseRecurringPayment(t *testing.T) {
	const (
		defaultWorkflowID = "default-test-workflow-id"
		ownership         = commontypes.Ownership_EPIFI_TECH
	)

	var (
		payload, _ = protojson.Marshal(&rpPb.RecurringPaymentPauseUnpauseInfo{
			RecurringPaymentId: "rp-id",
			ClientRequestId:    "client-req-id",
			Action:             rpPb.Action_PAUSE,
		})

		payload2, _ = protojson.Marshal(&rpPayloadPb.PauseUnpauseRecurringPaymentAuthSignal{})

		clientReqId = &workflowPb.ClientReqId{
			Id:     "client-req-id",
			Client: workflowPb.Client_USER_APP,
		}
	)

	type mockGetWorkflowProcessingParamsV2 struct {
		enable bool
		req    *activityPb.GetWorkflowProcessingParamsV2Request
		res    *activityPb.GetWorkflowProcessingParamsV2Response
		err    error
	}

	type mockInitiateWorkflowStageV2 struct {
		enable bool
		req    *activityPb.InitiateWorkflowStageV2Request
		err    error
	}

	type mockUpdateWorkflowStage struct {
		enable bool
		req    *activityPb.UpdateWorkflowStageRequest
		err    error
	}

	type mockPublishWorkflowUpdateEventV2 struct {
		enable bool
		req    *activityPb.PublishWorkflowUpdateEventV2Request
		res    *activityPb.PublishWorkflowUpdateEventV2Response
		err    error
	}

	type mockSignalWorkflow struct {
		enable     bool
		delay      time.Duration
		signal     []byte
		signalName epifitemporal.Signal
	}

	type mockGetNotificationTemplate struct {
		enable bool
		req    *notificationPb.GetTemplatesRequest
		res    *notificationPb.GetTemplatesResponse
		err    error
	}
	type mockSendNotification struct {
		enable bool
		req    *notificationPb.SendNotificationRequest
		res    *notificationPb.SendNotificationResponse
		err    error
	}

	type mockProcessPauseUnpauseRecurringPayment struct {
		enable bool
		req    *rpActivityPb.ProcessPauseUnpauseRecurringPaymentRequest
		res    *rpActivityPb.ProcessPauseUnpauseRecurringPaymentResponse
		err    error
	}

	tests := []struct {
		name                                    string
		req                                     *workflowPb.Request
		mockGetWorkflowProcessingParamsV2       mockGetWorkflowProcessingParamsV2
		mockInitiateWorkflowStageV2             mockInitiateWorkflowStageV2
		mockUpdateWorkflowStage                 []mockUpdateWorkflowStage
		mockPublishWorkflowUpdateEventV2        mockPublishWorkflowUpdateEventV2
		mockSignalWorkflow                      mockSignalWorkflow
		mockProcessPauseUnpauseRecurringPayment mockProcessPauseUnpauseRecurringPayment
		mockGetNotificationTemplate             mockGetNotificationTemplate
		mockSendNotification                    mockSendNotification
		wantErr                                 bool
	}{
		{
			name: "successfully paused mandate recurring payment",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						Payload:     payload,
						ClientReqId: clientReqId,
					},
				},
			},
			mockInitiateWorkflowStageV2: mockInitiateWorkflowStageV2{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPauseUnpause),
					Status:        stagePb.Status_BLOCKED,
				},
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{},
						WfReqId:       defaultWorkflowID,
						StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPauseUnpause),
						Status:        stagePb.Status_INITIATED,
					},
				},
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{},
						WfReqId:       defaultWorkflowID,
						StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPauseUnpause),
						Status:        stagePb.Status_SUCCESSFUL,
					},
				},
			},
			mockPublishWorkflowUpdateEventV2: mockPublishWorkflowUpdateEventV2{
				enable: true,
				req: &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
				},
				res: &activityPb.PublishWorkflowUpdateEventV2Response{},
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable:     true,
				delay:      10 * time.Nanosecond,
				signal:     payload2,
				signalName: rpNs.PauseUnpauseRecurringPaymentAuthSignal,
			},
			mockProcessPauseUnpauseRecurringPayment: mockProcessPauseUnpauseRecurringPayment{
				enable: true,
				req: &rpActivityPb.ProcessPauseUnpauseRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id",
						Ownership:   ownership,
						Payload:     payload,
					},
				},
				res: &rpActivityPb.ProcessPauseUnpauseRecurringPaymentResponse{},
			},
			mockGetNotificationTemplate: mockGetNotificationTemplate{
				enable: true,
				req: &notificationPb.GetTemplatesRequest{
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
				res: &notificationPb.GetTemplatesResponse{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_PhoneNumber{PhoneNumber: "0123456789"},
						CommunicationList: []*commPb.Communication{
							{
								Medium: commPb.Medium_SMS,
								Message: &commPb.Communication_Sms{
									Sms: &commPb.SMSMessage{
										SmsOption: &commPb.SmsOption{
											Option: &commPb.SmsOption_MandateRevokedSmsOption{
												MandateRevokedSmsOption: &commPb.MandateRevokedSmsOption{
													SmsType: commPb.SmsType_MANDATE_REVOKED,
													Option: &commPb.MandateRevokedSmsOption_MandateRevokedV1{
														MandateRevokedV1: &commPb.MandateRevokedSmsOptionV1{
															TemplateVersion: commPb.TemplateVersion_VERSION_V1,
															MandateAmount:   money.AmountINR(1000).GetPb(),
															PayeeName: &commontypes.Name{
																FirstName:  "first",
																MiddleName: "middle",
																LastName:   "last",
															},
														},
													},
												},
											},
										},
									},
								},
							},
						},
						QualityOfService: commPb.QoS_BEST_EFFORT,
						ClientId:         "clientId",
					},
				},
				err: nil,
			},
			mockSendNotification: mockSendNotification{
				enable: true,
				req: &notificationPb.SendNotificationRequest{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_PhoneNumber{PhoneNumber: "0123456789"},
						CommunicationList: []*commPb.Communication{
							{
								Medium: commPb.Medium_SMS,
								Message: &commPb.Communication_Sms{
									Sms: &commPb.SMSMessage{
										SmsOption: &commPb.SmsOption{
											Option: &commPb.SmsOption_MandateRevokedSmsOption{
												MandateRevokedSmsOption: &commPb.MandateRevokedSmsOption{
													SmsType: commPb.SmsType_MANDATE_REVOKED,
													Option: &commPb.MandateRevokedSmsOption_MandateRevokedV1{
														MandateRevokedV1: &commPb.MandateRevokedSmsOptionV1{
															TemplateVersion: commPb.TemplateVersion_VERSION_V1,
															MandateAmount:   money.AmountINR(1000).GetPb(),
															PayeeName: &commontypes.Name{
																FirstName:  "first",
																MiddleName: "middle",
																LastName:   "last",
															},
														},
													},
												},
											},
										},
									},
								},
							},
						},
						QualityOfService: commPb.QoS_BEST_EFFORT,
						ClientId:         "clientId",
					},
				},
				res: &notificationPb.SendNotificationResponse{
					MessageIdList: []string{
						"abc",
					},
				},
				err: nil,
			},
		},
		{
			name: "workflow failed on GetWorkflowProcessingParamsV2",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				err: epifitemporal.NewPermanentError(errors.New("test error")),
			},
			wantErr: true,
		},
		{
			name: "workflow failed for InitiateWorkflowStageV2",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						Payload:     payload,
						ClientReqId: clientReqId,
					},
				},
			},
			mockInitiateWorkflowStageV2: mockInitiateWorkflowStageV2{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPauseUnpause),
					Status:        stagePb.Status_BLOCKED,
				},
				err: epifitemporal.NewPermanentError(errors.New("test error")),
			},
			wantErr: true,
		},
		{
			name: "workflow failed for UpdateWorkflowStage",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						Payload:     payload,
						ClientReqId: clientReqId,
					},
				},
			},
			mockInitiateWorkflowStageV2: mockInitiateWorkflowStageV2{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPauseUnpause),
					Status:        stagePb.Status_BLOCKED,
				},
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable:     true,
				delay:      10 * time.Nanosecond,
				signal:     payload2,
				signalName: rpNs.PauseUnpauseRecurringPaymentAuthSignal,
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{},
						WfReqId:       defaultWorkflowID,
						StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPauseUnpause),
						Status:        stagePb.Status_INITIATED,
					},
					err: epifitemporal.NewPermanentError(errors.New("test error")),
				},
			},
			wantErr: true,
		},
		{
			name: "ProcessPauseUnpauseRecurringPayment activity failed with transient error",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						Payload:     payload,
						ClientReqId: clientReqId,
					},
				},
			},
			mockInitiateWorkflowStageV2: mockInitiateWorkflowStageV2{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPauseUnpause),
					Status:        stagePb.Status_BLOCKED,
				},
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{},
						WfReqId:       defaultWorkflowID,
						StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPauseUnpause),
						Status:        stagePb.Status_INITIATED,
					},
				},
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{},
						WfReqId:       defaultWorkflowID,
						StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPauseUnpause),
						Status:        stagePb.Status_MANUAL_INTERVENTION,
					},
				},
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable:     true,
				delay:      10 * time.Nanosecond,
				signal:     payload2,
				signalName: rpNs.PauseUnpauseRecurringPaymentAuthSignal,
			},
			mockProcessPauseUnpauseRecurringPayment: mockProcessPauseUnpauseRecurringPayment{
				enable: true,
				req: &rpActivityPb.ProcessPauseUnpauseRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id",
						Ownership:   ownership,
						Payload:     payload,
					},
				},
				err: epifitemporal.NewTransientError(errors.New("test error")),
			},
		},
		{
			name: "ProcessPauseUnpauseRecurringPayment activity failed with permanent error",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						Payload:     payload,
						ClientReqId: clientReqId,
					},
				},
			},
			mockInitiateWorkflowStageV2: mockInitiateWorkflowStageV2{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPauseUnpause),
					Status:        stagePb.Status_BLOCKED,
				},
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{},
						WfReqId:       defaultWorkflowID,
						StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPauseUnpause),
						Status:        stagePb.Status_INITIATED,
					},
				},
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{},
						WfReqId:       defaultWorkflowID,
						StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPauseUnpause),
						Status:        stagePb.Status_FAILED,
					},
				},
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable:     true,
				delay:      10 * time.Nanosecond,
				signal:     payload2,
				signalName: rpNs.PauseUnpauseRecurringPaymentAuthSignal,
			},
			mockPublishWorkflowUpdateEventV2: mockPublishWorkflowUpdateEventV2{
				enable: true,
				req: &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
				},
				res: &activityPb.PublishWorkflowUpdateEventV2Response{},
			},
			mockProcessPauseUnpauseRecurringPayment: mockProcessPauseUnpauseRecurringPayment{
				enable: true,
				req: &rpActivityPb.ProcessPauseUnpauseRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id",
						Ownership:   ownership,
						Payload:     payload,
					},
				},
				err: epifitemporal.NewPermanentError(errors.New("test error")),
			},
		},
		{
			name: "failed to pause/unpause recurring payment, because no signal was received",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						Payload:     payload,
						ClientReqId: clientReqId,
					},
				},
			},
			mockInitiateWorkflowStageV2: mockInitiateWorkflowStageV2{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPauseUnpause),
					Status:        stagePb.Status_BLOCKED,
				},
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{},
						WfReqId:       defaultWorkflowID,
						StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPauseUnpause),
						Status:        stagePb.Status_INITIATED,
					},
				},
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{},
						WfReqId:       defaultWorkflowID,
						StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPauseUnpause),
						Status:        stagePb.Status_FAILED,
					},
				},
			},
			mockPublishWorkflowUpdateEventV2: mockPublishWorkflowUpdateEventV2{
				enable: true,
				req: &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
				},
				res: &activityPb.PublishWorkflowUpdateEventV2Response{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			env := wts.NewTestWorkflowEnvironment()
			env.RegisterActivity(&celestialActivity.Processor{})
			env.RegisterActivity(&rpActivity.Processor{})
			env.RegisterActivity(&celestialActivityOld.Processor{})

			if tt.mockGetWorkflowProcessingParamsV2.enable {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, tt.mockGetWorkflowProcessingParamsV2.req).
					Return(tt.mockGetWorkflowProcessingParamsV2.res, tt.mockGetWorkflowProcessingParamsV2.err)
			}

			if tt.mockInitiateWorkflowStageV2.enable {
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, tt.mockInitiateWorkflowStageV2.req).
					Return(tt.mockInitiateWorkflowStageV2.err)
			}

			for _, mockUpdateWfStage := range tt.mockUpdateWorkflowStage {
				if mockUpdateWfStage.enable {
					env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, mockUpdateWfStage.req).
						Return(mockUpdateWfStage.err)
				}
			}

			if tt.mockPublishWorkflowUpdateEventV2.enable {
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, tt.mockPublishWorkflowUpdateEventV2.req).
					Return(tt.mockPublishWorkflowUpdateEventV2.err)
			}

			if tt.mockSignalWorkflow.enable {
				env.RegisterDelayedCallback(func() {
					err := env.SignalWorkflowByID(defaultWorkflowID, string(tt.mockSignalWorkflow.signalName), tt.mockSignalWorkflow.signal)
					if err != nil {
						t.Errorf("failed to send signal %s to workflow: %v", tt.mockSignalWorkflow.signalName, err)
					}
				}, tt.mockSignalWorkflow.delay)
			}

			if tt.mockGetNotificationTemplate.enable {
				env.OnActivity(string(rpNs.GetNotificationTemplate), mock.Anything, mock.Anything).
					Return(tt.mockGetNotificationTemplate.res, tt.mockGetNotificationTemplate.err)
			}

			if tt.mockSendNotification.enable {
				env.OnActivity(string(epifitemporal.SendNotification), mock.Anything, mock.Anything).Return(
					tt.mockSendNotification.res, tt.mockSendNotification.err)
			}

			if tt.mockProcessPauseUnpauseRecurringPayment.enable {
				env.OnActivity(string(rpNs.ProcessPauseUnpauseRecurringPayment), mock.Anything, tt.mockProcessPauseUnpauseRecurringPayment.req).
					Return(tt.mockProcessPauseUnpauseRecurringPayment.res, tt.mockProcessPauseUnpauseRecurringPayment.err)
			}

			env.ExecuteWorkflow(rpWorkflow.PauseUnpauseRecurringPayment, tt.req)
			assert.True(t, env.IsWorkflowCompleted())
			if (env.GetWorkflowError() != nil) != tt.wantErr {
				t.Errorf("PauseUnpauseRecurringPayment() error = %v, wantErr %v", env.GetWorkflowError(), tt.wantErr)
			}

			env.AssertExpectations(t)
		})
	}
}
