package workflow

import (
	"fmt"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	celestialActivityPb "github.com/epifi/be-common/api/celestial/activity"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/logger"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	rpPayloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
)

func RevokeRecurringPaymentV1(ctx workflow.Context, _ *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload) error {
	lg := workflow.GetLogger(ctx)
	// unmarshal the parameters required for the workflow
	workflowPayload := &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{}
	wfProcessingParams, wfProcessingParamsErr := celestialPkg.GetWorkflowProcessingReqParams(ctx, workflowPayload)
	if wfProcessingParamsErr != nil {
		lg.Error("failed to fetch workflow processing params", zap.Error(wfProcessingParamsErr))
		return wfProcessingParamsErr
	}
	clientReqId := wfProcessingParams.GetClientReqId().GetId()
	recurringPaymentActionId := workflowPayload.GetRecurringPaymentActionId()
	originalRecurringPaymentState := workflowPayload.GetOriginalRecurringPaymentState()

	// Initiate revoke at domain
	initiateRevokeAtDomainStageStatus, initiateExecutionAtDomainErr := processInitiateRevokeAtDomainStage(ctx, &initiateDomainRevokeStageReq{
		clientReqId:              clientReqId,
		recurringPaymentId:       workflowPayload.GetRecurringPaymentId(),
		recurringPaymentActionId: recurringPaymentActionId,
	})
	if initiateExecutionAtDomainErr != nil {
		lg.Error("error in initiating revoke at domain stage", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, recurringPaymentActionId), zap.Error(initiateExecutionAtDomainErr))
		return initiateExecutionAtDomainErr
	}

	if initiateRevokeAtDomainStageStatus != stagePb.Status_SUCCESSFUL {
		lg.Error("initiate revoke at domain stage was not successful", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, recurringPaymentActionId), zap.String(logger.STAGE_STATUS, initiateRevokeAtDomainStageStatus.String()))
		return fmt.Errorf("initiate mandate revoke at domain is not successful, stageStatus: %s", initiateRevokeAtDomainStageStatus)
	}

	// Authorization step
	revokeAuthorisationStageStatus, revokeAuthorisationStageErr := processRevokeAuthorisationStage(ctx, &revokeAuthorisationStageReq{
		clientReqId:              clientReqId,
		recurringPaymentId:       workflowPayload.GetRecurringPaymentId(),
		recurringPaymentActionId: recurringPaymentActionId,
	})
	if revokeAuthorisationStageErr != nil {
		lg.Error("error in handling revoke stage for recurring payment revoke", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, recurringPaymentActionId), zap.Error(revokeAuthorisationStageErr))
		return revokeAuthorisationStageErr
	}
	if revokeAuthorisationStageStatus != stagePb.Status_SUCCESSFUL {
		lg.Error("revoke authorisation stage returned non-success status", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, recurringPaymentActionId), zap.String(logger.STAGE_STATUS, initiateRevokeAtDomainStageStatus.String()))
		return fmt.Errorf("revoke authorisation stage not successful, stageStatus: %s", revokeAuthorisationStageStatus)
	}

	// Initiated state
	enquireRevokeStatusAtDomainStage, enquireRevokeStatusAtDomainErr := processEnquireRevokeStatusAtDomainStage(ctx, &enquireDomainRevokeStatusStageReq{
		clientReqId:              clientReqId,
		recurringPaymentActionId: recurringPaymentActionId,
		recurringPaymentId:       workflowPayload.GetRecurringPaymentId(),
	})

	if enquireRevokeStatusAtDomainErr != nil || enquireRevokeStatusAtDomainStage == stagePb.Status_MANUAL_INTERVENTION {
		lg.Error("enquire execution status at domain stage was not successful", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, recurringPaymentActionId), zap.String(logger.STAGE_STATUS, enquireRevokeStatusAtDomainStage.String()), zap.Error(enquireRevokeStatusAtDomainErr))
		return fmt.Errorf("failed to enquire status at domain,err: %w, stageStatus: %s", enquireRevokeStatusAtDomainErr, enquireRevokeStatusAtDomainStage)
	}

	// ============================	STAGE POST EXECUTION PROCESSING =============================
	var postProcessingStage epifitemporal.Stage
	var finalRecurringPaymentState rpPb.RecurringPaymentState
	var finalRecurringPaymentActionStatus rpPb.ActionState
	switch enquireRevokeStatusAtDomainStage {
	case stagePb.Status_SUCCESSFUL:
		postProcessingStage = rpNs.RevokeRecurringPaymentPostSuccessProcessing
		finalRecurringPaymentState = rpPb.RecurringPaymentState_REVOKED
		finalRecurringPaymentActionStatus = rpPb.ActionState_ACTION_SUCCESS
	case stagePb.Status_FAILED:
		postProcessingStage = rpNs.RevokeRecurringPaymentPostFailureProcessing
		finalRecurringPaymentState = rpPb.GetRecurringPaymentStateFromNewRecurringPaymentState(originalRecurringPaymentState)
		finalRecurringPaymentActionStatus = rpPb.ActionState_ACTION_FAILURE
	default:
		lg.Error("unhandled domain execution stage status", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, recurringPaymentActionId), zap.String(logger.STAGE_STATUS, enquireRevokeStatusAtDomainStage.String()))
		postProcessingStage = rpNs.RevokeRecurringPaymentPostFailureProcessing
		finalRecurringPaymentState = rpPb.RecurringPaymentState_REVOKE_INITIATED
		finalRecurringPaymentActionStatus = rpPb.ActionState_ACTION_MANUAL_INTERVENTION
	}

	// update the entities to final state
	revokeRecurringPaymentPostProcessingStageStatus, revokeRecurringPaymentPostProcessingErr := processRevokeRecurringPaymentPostProcessing(ctx, &revokeRecurringPaymentPostProcessingReq{
		clientReqId:                clientReqId,
		recurringPaymentId:         workflowPayload.GetRecurringPaymentId(),
		recurringPaymentActionId:   recurringPaymentActionId,
		stage:                      postProcessingStage,
		finalRecurringPaymentState: finalRecurringPaymentState,
		finalRpaStatus:             finalRecurringPaymentActionStatus,
	})
	if revokeRecurringPaymentPostProcessingErr != nil {
		lg.Error("error in revoke recurring payment post processing stage", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, recurringPaymentActionId), zap.String(logger.STAGE, string(rpNs.ExecuteRecurringPaymentPostSuccessProcessing)), zap.Error(revokeRecurringPaymentPostProcessingErr))
		return revokeRecurringPaymentPostProcessingErr
	}
	if revokeRecurringPaymentPostProcessingStageStatus != stagePb.Status_SUCCESSFUL {
		lg.Error("revoke recurring payment post processing stage was not successful", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, recurringPaymentActionId), zap.String(logger.STAGE_STATUS, revokeRecurringPaymentPostProcessingStageStatus.String()))
		return fmt.Errorf("failure in revoke recurring payment post processing stage. stage = %s, status = %s", postProcessingStage, revokeRecurringPaymentPostProcessingStageStatus)
	}

	return nil
}

type initiateDomainRevokeStageReq struct {
	clientReqId              string
	recurringPaymentId       string
	recurringPaymentActionId string
}

// nolint:dupl
func processInitiateRevokeAtDomainStage(ctx workflow.Context, req *initiateDomainRevokeStageReq) (stagePb.Status, error) {
	var (
		lg           = workflow.GetLogger(ctx)
		currentStage = rpNs.InitiateRevokeAtDomainStage
		stageStatus  = stagePb.Status_INITIATED
	)

	// ==================== initiate stage for the workflow ===================
	if initiateWorkflowStageErr := celestialPkg.InitiateWorkflowStage(ctx, currentStage, stageStatus); initiateWorkflowStageErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to initiate workflow stage: %s: %w", currentStage, initiateWorkflowStageErr)
	}

	// ==================== check that the recurring payment is in REVOKE_QUEUED status and fail early if that is not the case ========================
	getRecurringPaymentResponse := &rpActivityPb.GetRecurringPaymentResponse{}
	if getRecurringPaymentErr := activityPkg.Execute(ctx, rpNs.GetRecurringPayment, getRecurringPaymentResponse, &rpActivityPb.GetRecurringPaymentRequest{
		RequestHeader: &celestialActivityPb.RequestHeader{
			ClientReqId: req.clientReqId,
			Ownership:   commontypes.Ownership_EPIFI_TECH,
		},
		RecurringPaymentId: req.recurringPaymentId,
	}); getRecurringPaymentErr != nil || getRecurringPaymentResponse.GetRecurringPayment().GetState() != rpPb.RecurringPaymentState_REVOKE_QUEUED {
		if getRecurringPaymentErr != nil {
			lg.Error("failed to fetch recurring payment for status check", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(getRecurringPaymentErr))
		} else {
			lg.Error("recurring payment is not in a state to be revoked. failing workflow", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.String(logger.STATE, getRecurringPaymentResponse.GetRecurringPayment().GetState().String()))
		}
		stageStatus = stagePb.Status_FAILED

		// update the workflow stage with the stage status
		if updateWorkflowStageErr := celestialPkg.UpdateWorkflowStage(ctx, currentStage, stageStatus); updateWorkflowStageErr != nil {
			return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s to status: %s, %w", currentStage, stageStatus, updateWorkflowStageErr)
		}
		return stageStatus, nil
	}

	// ==================== initiate domain revoke ========================
	initiateDomainRevokeResponse := &rpActivityPb.InitiateRevokeAtDomainResponse{}
	if initiateDomainRevokeErr := activityPkg.Execute(ctx, rpNs.InitiateRevokeAtDomain, initiateDomainRevokeResponse, &rpActivityPb.InitiateRevokeAtDomainRequest{
		RequestHeader: &celestialActivityPb.RequestHeader{
			ClientReqId: req.clientReqId,
			Ownership:   commontypes.Ownership_EPIFI_TECH,
		},
		RecurringPaymentId:       req.recurringPaymentId,
		RecurringPaymentActionId: req.recurringPaymentActionId,
	}); initiateDomainRevokeErr != nil {
		lg.Error("failed to initiate domain revoke", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, req.recurringPaymentActionId), zap.String(logger.STAGE, string(currentStage)), zap.String(logger.ACTIVITY, string(rpNs.InitiateRevokeAtDomain)), zap.Error(initiateDomainRevokeErr))
		stageStatus = stagePb.Status_FAILED

		// update the workflow stage with the stage status
		if updateWorkflowStageErr := celestialPkg.UpdateWorkflowStage(ctx, currentStage, stageStatus); updateWorkflowStageErr != nil {
			return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s to status: %s, %w", currentStage, stageStatus, updateWorkflowStageErr)
		}
		return stageStatus, nil
	}

	// ==================== update recurring payment status to REVOKE_INITIATED ========================
	updateRecurringPaymentStatusResponse := &rpActivityPb.UpdateRecurringPaymentStatusResponse{}
	if updateRecurringPaymentStatusErr := activityPkg.Execute(ctx, rpNs.UpdateRecurringPaymentStatus, updateRecurringPaymentStatusResponse, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
		RequestHeader: &celestialActivityPb.RequestHeader{
			ClientReqId: req.clientReqId,
			Ownership:   commontypes.Ownership_EPIFI_TECH,
		},
		RecurringPaymentId: req.recurringPaymentId,
		CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_QUEUED,
		NextStatus:         rpPb.RecurringPaymentState_REVOKE_INITIATED,
	}); updateRecurringPaymentStatusErr != nil {
		lg.Error(
			"failed to update recurring payment state to revoke initiated",
			zap.String(logger.RECURRING_PAYMENT_ACTION_ID, req.recurringPaymentActionId),
			zap.String(logger.STAGE, string(currentStage)),
			zap.String(logger.ACTIVITY, string(rpNs.UpdateRecurringPaymentStatus)),
			zap.Error(updateRecurringPaymentStatusErr),
		)
		stageStatus = stagePb.Status_FAILED

		// update the workflow stage with the stage status
		if updateWorkflowStageErr := celestialPkg.UpdateWorkflowStage(ctx, currentStage, stageStatus); updateWorkflowStageErr != nil {
			return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s to status: %s, %w", currentStage, stageStatus, updateWorkflowStageErr)
		}
		return stageStatus, nil
	}

	newActionStatus := rpPb.ActionState_ACTION_IN_PROGRESS

	// ================= update recurring payment action to new status ====================
	updateErr := updateRecurringPaymentActionStatusAndPublishStatusUpdateEvent(ctx, &updateActionStatusAndPublishStatusUpdateEventReq{
		recurringPaymentActionId:    req.recurringPaymentActionId,
		expectedCurrentActionStatus: rpPb.ActionState_ACTION_CREATED,
		newActionStatus:             newActionStatus,
	})
	if updateErr != nil {
		lg.Error(
			"failed to update recurring payment action state to in progress",
			zap.String(logger.RECURRING_PAYMENT_ACTION_ID, req.recurringPaymentActionId),
			zap.String(logger.STAGE, string(currentStage)),
			zap.String(logger.ACTIVITY, string(rpNs.UpdateRecurringPaymentStatus)),
			zap.Error(updateErr),
		)
		stageStatus = stagePb.Status_FAILED

		// update the workflow stage with the stage status
		if updateWorkflowStageErr := celestialPkg.UpdateWorkflowStage(ctx, currentStage, stageStatus); updateWorkflowStageErr != nil {
			return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s to status: %s, %w", currentStage, stageStatus, updateWorkflowStageErr)
		}
		return stageStatus, nil
	}

	// all activities in the stage have been successfully executed
	stageStatus = stagePb.Status_SUCCESSFUL

	// ===================== updated workflow stage status =======================
	updateWorkflowStageErr := celestialPkg.UpdateWorkflowStage(ctx, currentStage, stageStatus)
	if updateWorkflowStageErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update the workflow stage %s to status: %s %w", currentStage, stageStatus, updateWorkflowStageErr)
	}
	return stageStatus, nil
}

type revokeAuthorisationStageReq struct {
	clientReqId              string
	recurringPaymentId       string
	recurringPaymentActionId string
}

// TODO: Remove the nolint once the function is implemented properly
// nolint:unparam,ineffassign
func processRevokeAuthorisationStage(ctx workflow.Context, req *revokeAuthorisationStageReq) (stagePb.Status, error) {
	var (
		_           = workflow.GetLogger(ctx)
		stageStatus = stagePb.Status_INITIATED
	)

	// TODO: Uncomment the code below after adding workflow versioning and add an activity/wait for signal to handle
	// for revoke authorisation, when needed. Not adding now to avoid adding an unnecessary activity call.
	// ==================== initiate stage for the worfklow ===================
	// if initiateWorkflowStageErr := celestialPkg.InitiateWorkflowStage(ctx, rpNs.RevokeRecurringPaymentAuthorisation, stageStatus); initiateWorkflowStageErr != nil {
	// 	return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow %s stage: %w", rpNs.RevokeRecurringPaymentAuthorisation, initiateWorkflowStageErr)
	// }

	stageStatus = stagePb.Status_SUCCESSFUL
	// ======================== UPDATE RECURRING PAYMENT STATUS TO REVOKE_AUTHORISED ===================

	// ======================== UPDATE WORKFLOW STAGE STATUS ===================
	// if updateWorkflowStageErr := celestialPkg.UpdateWorkflowStage(ctx, rpNs.EnquireRevokeStatusAtDomainStage, stageStatus); updateWorkflowStageErr != nil {
	// 	return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update the workflow stage : %s, status : %s, err: %w", rpNs.EnquireRevokeStatusAtDomainStage, stageStatus, updateWorkflowStageErr)
	// }
	return stageStatus, nil
}

type enquireDomainRevokeStatusStageReq struct {
	clientReqId              string
	recurringPaymentId       string
	recurringPaymentActionId string
}

// nolint:dupl
func processEnquireRevokeStatusAtDomainStage(ctx workflow.Context, req *enquireDomainRevokeStatusStageReq) (stagePb.Status, error) {
	var (
		lg          = workflow.GetLogger(ctx)
		stageStatus = stagePb.Status_INITIATED
	)
	// ==================== initiate stage for the worfklow ===================
	if initiateWorkflowStageErr := celestialPkg.InitiateWorkflowStage(ctx, rpNs.EnquireRevokeStatusAtDomainStage, stageStatus); initiateWorkflowStageErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow %s stage: %w", rpNs.EnquireRevokeStatusAtDomain, initiateWorkflowStageErr)
	}

	enquireRevokeStatusAtDomainRes := &rpActivityPb.EnquireRevokeStatusAtDomainResponse{}
	if enquireRevokeStatusAtDomainErr := activityPkg.Execute(ctx, rpNs.EnquireRevokeStatusAtDomain, enquireRevokeStatusAtDomainRes, &rpActivityPb.EnquireRevokeStatusAtDomainRequest{
		RequestHeader: &celestialActivityPb.RequestHeader{
			ClientReqId: req.clientReqId,
			Ownership:   commontypes.Ownership_EPIFI_TECH,
		},
		RecurringPaymentActionId: req.recurringPaymentActionId,
	}); enquireRevokeStatusAtDomainErr != nil {
		lg.Error("error in enquiring revoke status at domain", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, req.recurringPaymentActionId), zap.String(logger.STAGE, string(rpNs.EnquireRevokeStatusAtDomainStage)), zap.String(logger.ACTIVITY, string(rpNs.EnquireRevokeStatusAtDomain)), zap.Error(enquireRevokeStatusAtDomainErr))
		// since we do not know what is the status of revoke at domain, we have to check it manually
		stageStatus = stagePb.Status_MANUAL_INTERVENTION
		if updateWorkflowStageErr := celestialPkg.UpdateWorkflowStage(ctx, rpNs.EnquireRevokeStatusAtDomainStage, stageStatus); updateWorkflowStageErr != nil {
			return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update the workflow stage : %s, status : %s, err: %w", rpNs.EnquireRevokeStatusAtDomainStage, stageStatus, updateWorkflowStageErr)
		}
		return stageStatus, nil
	}

	// based upon the status we have got from the activity, update the recurring payment entities
	var nextRecurringPaymentActionState rpPb.ActionState
	switch {
	case enquireRevokeStatusAtDomainRes.GetRevokeStatus() == rpActivityPb.RevokeStatusAtDomain_DOMAIN_REVOKE_STATUS_SUCCESS:
		nextRecurringPaymentActionState = rpPb.ActionState_ACTION_SUCCESS
		// stage is marked as successful if the execution was successful at domain, else the stage is marked as failed
		// stage status here defines the execution action status
		stageStatus = stagePb.Status_SUCCESSFUL
	case enquireRevokeStatusAtDomainRes.GetRevokeStatus() == rpActivityPb.RevokeStatusAtDomain_DOMAIN_REVOKE_STATUS_FAILURE:
		nextRecurringPaymentActionState = rpPb.ActionState_ACTION_FAILURE
		// mark the stage as failed if the execution has failed
		stageStatus = stagePb.Status_FAILED
	default:
		lg.Error("unknown revoke status received from domain", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, req.recurringPaymentActionId), zap.String(logger.STAGE, string(rpNs.EnquireRevokeStatusAtDomainStage)), zap.String(logger.STATUS, string(enquireRevokeStatusAtDomainRes.GetRevokeStatus())))
		// we do not know the status of the domain, mark the stage to the manual intervention
		stageStatus = stagePb.Status_MANUAL_INTERVENTION
		if updateWorkflowStageErr := celestialPkg.UpdateWorkflowStage(ctx, rpNs.EnquireRevokeStatusAtDomainStage, stageStatus); updateWorkflowStageErr != nil {
			return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update the workflow stage : %s, status : %s, err: %w", rpNs.EnquireRevokeStatusAtDomainStage, stageStatus, updateWorkflowStageErr)
		}
		return stageStatus, nil
	}

	// ======================= update recurring payment action state ====================================
	if updateActionAndPublishPacketErr := updateRecurringPaymentActionStatusAndPublishStatusUpdateEvent(ctx, &updateActionStatusAndPublishStatusUpdateEventReq{
		recurringPaymentActionId:    req.recurringPaymentActionId,
		expectedCurrentActionStatus: rpPb.ActionState_ACTION_IN_PROGRESS,
		newActionStatus:             nextRecurringPaymentActionState,
		actionDetailedStatusInfo:    enquireRevokeStatusAtDomainRes.GetActionDetailedStatusInfo(),
	}); updateActionAndPublishPacketErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, updateActionAndPublishPacketErr
	}

	// ======================== UPDATE WORKFLOW STAGE STATUS ===================
	if updateWorkflowStageErr := celestialPkg.UpdateWorkflowStage(ctx, rpNs.EnquireRevokeStatusAtDomainStage, stageStatus); updateWorkflowStageErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update the workflow stage : %s, status : %s, err: %w", rpNs.EnquireRevokeStatusAtDomainStage, stageStatus, updateWorkflowStageErr)
	}
	return stageStatus, nil
}

type revokeRecurringPaymentPostProcessingReq struct {
	clientReqId                string
	recurringPaymentId         string
	recurringPaymentActionId   string
	stage                      epifitemporal.Stage
	finalRecurringPaymentState rpPb.RecurringPaymentState
	finalRpaStatus             rpPb.ActionState
}

func processRevokeRecurringPaymentPostProcessing(ctx workflow.Context, req *revokeRecurringPaymentPostProcessingReq) (stagePb.Status, error) {
	var (
		lg          = workflow.GetLogger(ctx)
		stageStatus = stagePb.Status_INITIATED
	)

	// ========================= initiate stage for workflow ============================
	if initiateWorkflowStageErr := celestialPkg.InitiateWorkflowStage(ctx, req.stage, stageStatus); initiateWorkflowStageErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update the workflow stage %s: %w", req.stage, initiateWorkflowStageErr)
	}

	// ==================== update RPA status to a terminal state ========================
	updateRecurringPaymentActionStatusRes := &rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}
	if updateRecPayStatusErr := activityPkg.Execute(ctx, rpNs.UpdateRecurringPaymentActionStatus, updateRecurringPaymentActionStatusRes, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
		RequestHeader: &celestialActivityPb.RequestHeader{
			ClientReqId: req.clientReqId,
			Ownership:   commontypes.Ownership_EPIFI_TECH,
		},
		RecurringPaymentActionId: req.recurringPaymentActionId,
		CurrentStatus:            rpPb.ActionState_ACTION_IN_PROGRESS,
		NextStatus:               req.finalRpaStatus,
		ActionDetailedStatusInfo: nil,
	}); updateRecPayStatusErr != nil {
		lg.Error("failed to update recurring payment status to revoke queued", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.String(logger.STAGE, string(req.stage)), zap.String(logger.ACTIVITY, string(rpNs.InitiateRevokeAtDomain)), zap.Error(updateRecPayStatusErr))
		stageStatus = stagePb.Status_FAILED

		// update the workflow stage with the stage status
		if updateWorkflowStageErr := celestialPkg.UpdateWorkflowStage(ctx, req.stage, stageStatus); updateWorkflowStageErr != nil {
			return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s to status: %s, %w", req.stage, stageStatus, updateWorkflowStageErr)
		}
		return stageStatus, nil
	}

	// ==================== update RP status to the final recurring payment status ========================
	updateRecurringPaymentStatusRes := &rpActivityPb.UpdateRecurringPaymentStatusResponse{}
	if updateRecPayStatusErr := activityPkg.Execute(ctx, rpNs.UpdateRecurringPaymentStatus, updateRecurringPaymentStatusRes, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
		RequestHeader: &celestialActivityPb.RequestHeader{
			ClientReqId: req.clientReqId,
			Ownership:   commontypes.Ownership_EPIFI_TECH,
		},
		RecurringPaymentId: req.recurringPaymentId,
		CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_INITIATED,
		NextStatus:         req.finalRecurringPaymentState,
	}); updateRecPayStatusErr != nil {
		lg.Error("failed to update recurring payment status to revoke queued", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.String(logger.STAGE, string(req.stage)), zap.String(logger.ACTIVITY, string(rpNs.InitiateRevokeAtDomain)), zap.Error(updateRecPayStatusErr))
		stageStatus = stagePb.Status_FAILED

		// update the workflow stage with the stage status
		if updateWorkflowStageErr := celestialPkg.UpdateWorkflowStage(ctx, req.stage, stageStatus); updateWorkflowStageErr != nil {
			return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s to status: %s, %w", req.stage, stageStatus, updateWorkflowStageErr)
		}
		return stageStatus, nil
	}

	// ======================== send notification for the revoke flow ================
	sendNotificationRes := &rpActivityPb.SendStatusNotificationResponse{}
	if sendNotificationErr := activityPkg.Execute(ctx, rpNs.SendStatusNotification, sendNotificationRes, &rpActivityPb.SendStatusNotificationRequest{
		RequestHeader: &celestialActivityPb.RequestHeader{
			ClientReqId: req.clientReqId,
			Ownership:   commontypes.Ownership_EPIFI_TECH,
		},
		RecurringPaymentId:       req.recurringPaymentId,
		RecurringPaymentActionId: req.recurringPaymentActionId,
	}); sendNotificationErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to send successful execution notification,err: %w", sendNotificationErr)
	}

	// all activities in the stage are successful
	stageStatus = stagePb.Status_SUCCESSFUL

	if updateWorkflowStageErr := celestialPkg.UpdateWorkflowStage(ctx, req.stage, stageStatus); updateWorkflowStageErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update the workflow stage : %s, status : %s, err: %w", req.stage, stageStatus, updateWorkflowStageErr)
	}
	return stageStatus, nil
}
