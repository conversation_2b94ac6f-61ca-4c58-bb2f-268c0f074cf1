package workflow

import (
	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	celestialPb "github.com/epifi/be-common/api/celestial"
	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/logger"

	notificationPb "github.com/epifi/gamma/api/celestial/activity/notification"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	rpPayloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
)

// PauseUnpauseRecurringPayment - Pause or unpause the recurring payment based on request
// Workflow first moves to BLOCKED state and waits for Auth signal
// After receiving the auth signal it starts the processing of pause unpause request
func PauseUnpauseRecurringPayment(ctx workflow.Context, _ *workflowPb.Request) error {
	var (
		lg = workflow.GetLogger(ctx)
	)
	payloadObj := &rpPb.RecurringPaymentPauseUnpauseInfo{}
	wfProcessingParams, err := celestialPkg.GetWorkflowProcessingReqParams(ctx, payloadObj)
	if err != nil {
		lg.Error("error in getting workflow processing params", zap.Error(err))
		return err
	}

	if err = processPauseUnpauseRecurringPayment(ctx, wfProcessingParams); err != nil {
		lg.Error("failed to process pause unpause recurring payment request", zap.Error(err))
		return err
	}

	return nil
}

// processPauseUnpauseRecurringPayment - process the pause/unpause stage of recurring payment
func processPauseUnpauseRecurringPayment(ctx workflow.Context, wfProcessingParams *workflowPb.ProcessingParams) error {
	lg := workflow.GetLogger(ctx)

	err := celestialPkg.InitiateWorkflowStage(ctx, rpNs.ProcessPauseUnpause, stagePb.Status_BLOCKED)
	if err != nil {
		lg.Error("InitiateWorkflowStageV2 activity failed", zap.Error(err))
		return fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStageV2, err)
	}

	// auth signal handling
	workflowStageStatus := stagePb.Status_INITIATED
	signalPayload := &rpPayloadPb.PauseUnpauseRecurringPaymentAuthSignal{}
	sigChannel := epifitemporal.NewSignalChannel(ctx, rpNs.PauseUnpauseRecurringPaymentAuthSignal, signalPayload)
	sigChannel.AddReceiverHandler(func(getErr error, payload *rpPayloadPb.PauseUnpauseRecurringPaymentAuthSignal) {
		lg.Info("received auth signal", zap.Error(getErr))
		if getErr != nil {
			workflowStageStatus = celestialPkg.GetWorkflowStageStatusForErr(err)
			lg.Error("failed while receiving signal", zap.String(logger.SIGNAL_NAME, string(rpNs.PauseUnpauseRecurringPaymentAuthSignal)), zap.Error(getErr))
			return
		}
	})

	// wait for auth signal or timeout before attempting to process
	err = epifitemporal.ReceiveSignal(ctx, sigChannel, 10*time.Minute)
	if err != nil {
		lg.Error("failed to receive auth signal", zap.Error(err))
	}

	err = celestialPkg.UpdateWorkflowStage(ctx, rpNs.ProcessPauseUnpause, workflowStageStatus)
	if err != nil {
		lg.Error("UpdateWorkflowStage activity failed", zap.Error(err))
		return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStage, err)
	}

	resp := &rpActivityPb.ProcessPauseUnpauseRecurringPaymentResponse{}
	err = activityPkg.Execute(ctx, rpNs.ProcessPauseUnpauseRecurringPayment, resp, &rpActivityPb.ProcessPauseUnpauseRecurringPaymentRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
			Payload:     wfProcessingParams.GetPayload(),
		},
	})
	workflowStageStatus = celestialPkg.GetWorkflowStageStatusForErr(err)

	if workflowStageStatus == stagePb.Status_SUCCESSFUL {
		// Step 5: get the notification template corresponding to the recurring payment type
		notificationTemplateRes := &notificationPb.GetTemplatesResponse{}
		if err = activityPkg.Execute(ctx, rpNs.GetNotificationTemplate, notificationTemplateRes, &notificationPb.GetTemplatesRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
				Ownership:   epificontext.OwnershipFromContext(ctx),
				Payload:     wfProcessingParams.GetPayload(),
			},
			ClientReqId: &celestialPb.ClientReqId{
				Id:     wfProcessingParams.GetClientReqId().GetId(),
				Client: wfProcessingParams.GetClientReqId().GetClient(),
			},
		}); err != nil {
			return fmt.Errorf("%s activity failed: %w", rpNs.GetNotificationTemplate, err)
		}

		if len(notificationTemplateRes.GetNotifications().GetCommunicationList()) != 0 {
			// Step 6: sends multiple notifications to the user simultaneously
			sendNotificationRes := &notificationPb.SendNotificationResponse{}
			if err = activityPkg.Execute(ctx, epifitemporal.SendNotification, sendNotificationRes, &notificationPb.SendNotificationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
					Ownership:   epificontext.OwnershipFromContext(ctx),
					Payload:     wfProcessingParams.GetPayload(),
				},
				Notifications: notificationTemplateRes.GetNotifications(),
			}); err != nil {
				return fmt.Errorf("%s activity failed: %w", epifitemporal.SendNotification, err)
			}
		}
	}

	err = celestialPkg.UpdateWorkflowStage(ctx, rpNs.ProcessPauseUnpause, workflowStageStatus)
	if err != nil {
		lg.Error("UpdateWorkflowStage activity failed", zap.Error(err))
		return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStage, err)
	}

	return nil
}
