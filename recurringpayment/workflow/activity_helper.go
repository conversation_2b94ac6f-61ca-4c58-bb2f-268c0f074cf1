package workflow

import (
	"fmt"

	"go.temporal.io/sdk/workflow"

	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
)

type updateActionStatusAndPublishStatusUpdateEventReq struct {
	recurringPaymentActionId    string
	expectedCurrentActionStatus rpPb.ActionState
	newActionStatus             rpPb.ActionState
	actionDetailedStatusInfo    *rpPb.ActionDetailedStatusInfo
}

// updateRecurringPaymentActionStatusAndPublishStatusUpdateEvent updates the recurring payment action status and publishes recurring payment action update event.
func updateRecurringPaymentActionStatusAndPublishStatusUpdateEvent(ctx workflow.Context, req *updateActionStatusAndPublishStatusUpdateEventReq) error {
	if updateActionStatusErr := activityPkg.Execute(ctx, rpNs.UpdateRecurringPaymentActionStatus, &rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
		RecurringPaymentActionId: req.recurringPaymentActionId,
		CurrentStatus:            req.expectedCurrentActionStatus,
		NextStatus:               req.newActionStatus,
		ActionDetailedStatusInfo: req.actionDetailedStatusInfo,
	}); updateActionStatusErr != nil {
		return fmt.Errorf("UpdateRecurringPaymentActionStatus activity failed, %w", updateActionStatusErr)
	}

	// publish only in terminal states
	// todo(pay): evaluate if publish is required in other  terminal states like manual_intervention also
	if req.newActionStatus == rpPb.ActionState_ACTION_FAILURE ||
		req.newActionStatus == rpPb.ActionState_ACTION_SUCCESS ||
		req.newActionStatus == rpPb.ActionState_ACTION_EXPIRED {
		if publishUpdateErr := activityPkg.Execute(ctx, rpNs.PublishRecurringPaymentActionUpdateEvent, &rpActivityPb.PublishRecurringPaymentActionUpdateEventResponse{}, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
			RecurringPaymentActionId: req.recurringPaymentActionId,
		}); publishUpdateErr != nil {
			return fmt.Errorf("PublishRecurringPaymentActionUpdateEvent activity failed, %w", publishUpdateErr)
		}
	}
	return nil
}
