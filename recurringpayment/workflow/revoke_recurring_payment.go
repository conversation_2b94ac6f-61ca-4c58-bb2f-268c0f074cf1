// nolint: dupl
package workflow

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	celestialPb "github.com/epifi/be-common/api/celestial"
	activityPb "github.com/epifi/be-common/api/celestial/activity"
	notificationPb "github.com/epifi/gamma/api/celestial/activity/notification"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	recurringPaymentNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/logger"
)

// RevokeRecurringPayment - revokes an existing recurring payment request
func RevokeRecurringPayment(ctx workflow.Context, _ *workflowPb.Request) error {
	ownership := epificontext.OwnershipFromContext[workflow.Context](ctx)
	wfReqID := workflow.GetInfo(ctx).WorkflowExecution.ID
	lg := workflow.GetLogger(ctx)

	wfProcessingParams := &celestialPb.WorkflowProcessingParams{}
	if err := activityPkg.ExecuteRaw(ctx, epifitemporal.GetWorkflowProcessingParams, &wfProcessingParams, wfReqID); err != nil {
		lg.Error("activity failed", zap.String(logger.ACTIVITY, string(epifitemporal.GetWorkflowProcessingParams)), zap.Error(err))
		return err
	}

	// --------------- REVOKE FULFILLMENT STAGE --------------------
	if err := processRecurringPaymentRevoke(ctx, wfReqID, wfProcessingParams, ownership); err != nil {
		lg.Error("revoke stage processing failed", zap.Error(err))
		return err
	}

	return nil
}

// processRecurringPaymentRevoke processes revoke stage for a recurring payment
// nolint:gocritic
func processRecurringPaymentRevoke(ctx workflow.Context, wfReqID string, wfProcessingParams *celestialPb.WorkflowProcessingParams, ownership commontypes.Ownership) error {
	var (
		recurringPaymentState rpPb.RecurringPaymentState
		actionStateToUpdate   rpPb.ActionState
		err                   error
		workflowReqStatus     = stagePb.Status_BLOCKED
	)
	lg := workflow.GetLogger(ctx)
	recurringPaymentRevokeInfo := &rpPb.RecurringPaymentRevokeInfo{}
	unmarshalOptions := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	if unmarshalErr := unmarshalOptions.Unmarshal(wfProcessingParams.GetPayload(), recurringPaymentRevokeInfo); unmarshalErr != nil {
		return fmt.Errorf("failed to unmarshal payload for recurring payment revoke: %w", unmarshalErr)
	}

	if !recurringPaymentRevokeInfo.GetBypassAuth() {
		// Step 1: blocking revoke stage for auth and creating appropriate entries in the DB
		if actErr := activityPkg.ExecuteRaw(ctx, epifitemporal.InitiateWorkflowStage, nil, wfReqID, workflowPb.Stage_FULFILLMENT, stagePb.Status_BLOCKED); actErr != nil {
			return fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStage, actErr)
		}

		// Step 2: blocking the workflow till we receive a signal, or it times out
		workflow.NewSelector(ctx).
			AddReceive(workflow.GetSignalChannel(ctx, string(recurringPaymentNs.RevokeRecurringPaymentAuthSignal)), func(c workflow.ReceiveChannel, more bool) {
				c.Receive(ctx, nil)
				lg.Info("received auth signal")
				workflowReqStatus = stagePb.Status_INITIATED
			}).
			AddFuture(workflow.NewTimer(ctx, 10*time.Minute), func(f workflow.Future) {
				lg.Error("timed out waiting for auth signal")
				workflowReqStatus = stagePb.Status_FAILED
			}).
			Select(ctx)

		// Step 3: updating stage and status of workflow with appropriate entries in the DB
		if actErr := activityPkg.ExecuteRaw(ctx, epifitemporal.UpdateWorkflowStageStatus, nil, wfReqID, workflowPb.Stage_FULFILLMENT, workflowReqStatus); actErr != nil {
			return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStageStatus, actErr)
		}
	} else {
		// create workflow stage with just status INITIATED since auth is not required
		if actErr := activityPkg.ExecuteRaw(ctx, epifitemporal.InitiateWorkflowStage, nil, wfReqID, workflowPb.Stage_FULFILLMENT, stagePb.Status_INITIATED); actErr != nil {
			return fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStage, actErr)
		}
	}

	// Step 4: invoking process recurring payment revoke activity if we received auth signal
	if workflowReqStatus != stagePb.Status_FAILED {
		res := &activityPb.Response{}
		err := activityPkg.ExecuteRaw(ctx, recurringPaymentNs.ProcessRecurringPaymentRevoke, res, &activityPb.Request{
			ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
			Payload:     wfProcessingParams.GetPayload(),
		})
		switch {
		case err != nil && !epifitemporal.IsRetryableError(err):
			workflowReqStatus = stagePb.Status_FAILED
		case err != nil:
			workflowReqStatus = stagePb.Status_MANUAL_INTERVENTION
		default:
			workflowReqStatus = stagePb.Status_SUCCESSFUL
		}

		if err := activityPkg.ExecuteRaw(ctx, epifitemporal.UpdateWorkflowStageStatus, nil, wfReqID, workflowPb.Stage_FULFILLMENT, workflowReqStatus); err != nil {
			return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStageStatus, err)
		}
	}

	// Update recurring payment state and action in case the signal times out and workflowReqStatus maps to failed
	v := workflow.GetVersion(ctx, "fix-recurring-payment-state-update", workflow.DefaultVersion, 1)
	if v == 1 {
		recurringPaymentState, actionStateToUpdate, err = getRecurringPaymentStateAndActionForRevoke(workflowReqStatus)
		if err != nil {
			return fmt.Errorf("no recurring payment state and action for workflow status")
		}

		updateRecurringPaymentStateAndActionRes := &rpActivityPb.UpdateRecurringPaymentStateAndActionResponse{}
		if err := activityPkg.Execute(ctx, recurringPaymentNs.UpdateRecurringPaymentStateAndAction, updateRecurringPaymentStateAndActionRes, &rpActivityPb.UpdateRecurringPaymentStateAndActionRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
				Ownership:   ownership,
			},
			StateToUpdate:       recurringPaymentState,
			ActionStateToUpdate: actionStateToUpdate,
		}); err != nil {
			return fmt.Errorf("%s activity failed: %w", recurringPaymentNs.UpdateRecurringPaymentStateAndAction, err)
		}

	}

	if workflowReqStatus == stagePb.Status_SUCCESSFUL {
		// Step 5: get the notification template corresponding to the recurring payment type
		notificationTemplateRes := &notificationPb.GetTemplatesResponse{}
		if err := activityPkg.ExecuteRaw(ctx, recurringPaymentNs.GetNotificationTemplate, notificationTemplateRes, &notificationPb.GetTemplatesRequest{
			ClientReqId: wfProcessingParams.GetClientReqId(),
		}); err != nil {
			return fmt.Errorf("%s activity failed: %w", recurringPaymentNs.GetNotificationTemplate, err)
		}

		if len(notificationTemplateRes.GetNotifications().GetCommunicationList()) != 0 {
			// Step 6: sends multiple notifications to the user simultaneously
			sendNotificationRes := &notificationPb.SendNotificationResponse{}
			if err := activityPkg.ExecuteRaw(ctx, epifitemporal.SendNotification, sendNotificationRes, &notificationPb.SendNotificationRequest{
				Notifications: notificationTemplateRes.GetNotifications(),
			}); err != nil {
				return fmt.Errorf("%s activity failed: %w", epifitemporal.SendNotification, err)
			}
		}
	}

	if err := activityPkg.ExecuteRaw(ctx, epifitemporal.PublishWorkflowUpdateEvent, nil, wfReqID); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.PublishWorkflowUpdateEvent, err)
	}

	return nil
}

// getRecurringPaymentStateAndActionForRevoke - returns recurring payment state and action wrt workflow status
func getRecurringPaymentStateAndActionForRevoke(workflowStatus stagePb.Status) (rpPb.RecurringPaymentState, rpPb.ActionState, error) {
	switch workflowStatus {
	case stagePb.Status_FAILED:
		return rpPb.RecurringPaymentState_ACTIVATED, rpPb.ActionState_ACTION_FAILURE, nil
	case stagePb.Status_MANUAL_INTERVENTION:
		return rpPb.RecurringPaymentState_MANUAL_INTERVENTION, rpPb.ActionState_ACTION_MANUAL_INTERVENTION, nil
	case stagePb.Status_SUCCESSFUL:
		return rpPb.RecurringPaymentState_REVOKED, rpPb.ActionState_ACTION_SUCCESS, nil
	default:
		return rpPb.RecurringPaymentState_RECURRING_PAYMENT_STATE_UNSPECIFIED, rpPb.ActionState_ACTION_STATE_UNSPECIFIED,
			fmt.Errorf("no recurring payment state and action for workflow status")
	}
}
