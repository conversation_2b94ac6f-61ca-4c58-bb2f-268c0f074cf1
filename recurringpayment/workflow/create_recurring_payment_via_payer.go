//nolint:gocritic
package workflow

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	celestialPb "github.com/epifi/be-common/api/celestial"
	activityPb "github.com/epifi/be-common/api/celestial/activity"
	notificationPb "github.com/epifi/gamma/api/celestial/activity/notification"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	recurringPaymentNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/logger"
)

func CreateRecurringPaymentViaPayer(ctx workflow.Context, _ *workflowPb.Request) error {
	ownership := epificontext.OwnershipFromContext[workflow.Context](ctx)
	wfReqID := workflow.GetInfo(ctx).WorkflowExecution.ID
	lg := workflow.GetLogger(ctx)

	wfProcessingParams := &celestialPb.WorkflowProcessingParams{}
	if err := activityPkg.ExecuteRaw(ctx, epifitemporal.GetWorkflowProcessingParams, &wfProcessingParams, wfReqID); err != nil {
		lg.Error("activity failed", zap.String(logger.ACTIVITY, string(epifitemporal.GetWorkflowProcessingParams)), zap.Error(err))
		return err
	}

	// --------------- CREATION STAGE --------------------
	if err := processRecurringPaymentCreation(ctx, wfReqID, wfProcessingParams, ownership); err != nil {
		lg.Error("creation stage processing failed", zap.Error(err))
		return err
	}

	return nil
}

// processRecurringPaymentCreation processes creation stage for a recurring payment
// nolint:dupl
func processRecurringPaymentCreation(ctx workflow.Context, wfReqID string, wfProcessingParams *celestialPb.WorkflowProcessingParams, ownership commontypes.Ownership) error {
	var (
		recurringPaymentState rpPb.RecurringPaymentState
		actionStateToUpdate   rpPb.ActionState
		err                   error
		workflowReqStatus     = stagePb.Status_INITIATED
		workflowStage         = workflowPb.Stage_CREATION
	)

	// Step 1: initiation of creation stage
	if err = activityPkg.ExecuteRaw(ctx, epifitemporal.InitiateWorkflowStage, nil, wfReqID, workflowStage, workflowReqStatus); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStage, err)
	}

	wfVersion := workflow.GetVersion(ctx, "support-for-upi-mandate-raise-notification", workflow.DefaultVersion, 1)
	if wfVersion == 1 {
		// Step 2: get the notification template corresponding to the recurring payment type
		// Context: We might need to send some notification before blocking the workflow execution.
		// E.g. For Upi Mandates, user needs to be informed regarding the mandate req, so that they can
		// go to the app and authorise the UPI Mandate / AutoPay request).
		notificationTemplateRes := &notificationPb.GetTemplatesResponse{}
		if err = activityPkg.ExecuteRaw(ctx, recurringPaymentNs.GetNotificationTemplate, notificationTemplateRes, &notificationPb.GetTemplatesRequest{
			ClientReqId: wfProcessingParams.GetClientReqId(),
		}); err != nil {
			return fmt.Errorf("%s activity failed: %w", recurringPaymentNs.GetNotificationTemplate, err)
		}

		// note: NotificationTemplate response will be empty at this time for all the recurring payments types
		// other than UPI Mandates
		if len(notificationTemplateRes.GetNotifications().GetCommunicationList()) != 0 {
			// Step 3: sends multiple notifications to the user simultaneously
			sendNotificationRes := &notificationPb.SendNotificationResponse{}
			if err = activityPkg.ExecuteRaw(ctx, epifitemporal.SendNotification, sendNotificationRes, &notificationPb.SendNotificationRequest{
				Notifications: notificationTemplateRes.GetNotifications(),
			}); err != nil {
				return fmt.Errorf("%s activity failed: %w", epifitemporal.SendNotification, err)
			}
		}
	}

	// Step 4: blocking creation stage for auth and creating appropriate entries in the DB
	workflowReqStatus = stagePb.Status_BLOCKED
	if err = activityPkg.ExecuteRaw(ctx, epifitemporal.UpdateWorkflowStageStatus, nil, wfReqID, workflowStage, workflowReqStatus); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStageStatus, err)
	}

	// Step 5: blocking the workflow till we receive a signal, or it times
	workflow.NewSelector(ctx).
		AddReceive(workflow.GetSignalChannel(ctx, string(recurringPaymentNs.CreateRecurringPaymentAuthSignal)), func(c workflow.ReceiveChannel, more bool) {
			c.Receive(ctx, nil)
			workflow.GetLogger(ctx).Info("received auth signal")
			workflowReqStatus = stagePb.Status_INITIATED
		}).
		AddFuture(workflow.NewTimer(ctx, 10*time.Minute), func(f workflow.Future) {
			workflow.GetLogger(ctx).Error("timed out waiting for auth signal")
			workflowReqStatus = stagePb.Status_FAILED
		}).
		Select(ctx)

	// Step 6: updating stage and status of workflow with appropriate entries in the DB
	if err = activityPkg.ExecuteRaw(ctx, epifitemporal.UpdateWorkflowStageStatus, nil, wfReqID, workflowPb.Stage_CREATION, workflowReqStatus); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStageStatus, err)
	}

	// Step 7: invoking process recurring payment creation activity if we received auth signal
	if workflowReqStatus != stagePb.Status_FAILED {
		res := &activityPb.Response{}
		err = activityPkg.ExecuteRaw(ctx, recurringPaymentNs.ProcessRecurringPaymentCreation, res, &activityPb.Request{
			ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
			Payload:     wfProcessingParams.GetPayload(),
		})
		switch {
		case err != nil && !epifitemporal.IsRetryableError(err):
			workflowReqStatus = stagePb.Status_FAILED
		case err != nil:
			workflowReqStatus = stagePb.Status_MANUAL_INTERVENTION
		default:
			workflowReqStatus = stagePb.Status_SUCCESSFUL
		}

		if err = activityPkg.ExecuteRaw(ctx, epifitemporal.UpdateWorkflowStageStatus, nil, wfReqID, workflowPb.Stage_CREATION, workflowReqStatus); err != nil {
			return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStageStatus, err)
		}
	}

	// Update recurring payment state and action corresponding to workflowReqStatus
	v := workflow.GetVersion(ctx, "fix-recurring-payment-state-update", workflow.DefaultVersion, 1)
	if v == 1 {
		recurringPaymentState, actionStateToUpdate, err = getRecurringPaymentStateAndAction(workflowReqStatus)
		if err != nil {
			return fmt.Errorf("no recurring payment state and action for workflow status")
		}

		updateRecurringPaymentStateAndActionRes := &rpActivityPb.UpdateRecurringPaymentStateAndActionResponse{}
		if err := activityPkg.Execute(ctx, recurringPaymentNs.UpdateRecurringPaymentStateAndAction, updateRecurringPaymentStateAndActionRes, &rpActivityPb.UpdateRecurringPaymentStateAndActionRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
				Ownership:   ownership,
			},
			StateToUpdate:       recurringPaymentState,
			ActionStateToUpdate: actionStateToUpdate,
		}); err != nil {
			return fmt.Errorf("%s activity failed: %w", recurringPaymentNs.UpdateRecurringPaymentStateAndAction, err)
		}
	}

	// Step 8: get the notification template corresponding to the recurring payment type
	notificationTemplateRes := &notificationPb.GetTemplatesResponse{}
	if err := activityPkg.ExecuteRaw(ctx, recurringPaymentNs.GetNotificationTemplate, notificationTemplateRes, &notificationPb.GetTemplatesRequest{
		ClientReqId: wfProcessingParams.GetClientReqId(),
	}); err != nil {
		return fmt.Errorf("%s activity failed: %w", recurringPaymentNs.GetNotificationTemplate, err)
	}

	if len(notificationTemplateRes.GetNotifications().GetCommunicationList()) != 0 {
		// Step 9: sends multiple notifications to the user simultaneously
		sendNotificationRes := &notificationPb.SendNotificationResponse{}
		if err := activityPkg.ExecuteRaw(ctx, epifitemporal.SendNotification, sendNotificationRes, &notificationPb.SendNotificationRequest{
			Notifications: notificationTemplateRes.GetNotifications(),
		}); err != nil {
			return fmt.Errorf("%s activity failed: %w", epifitemporal.SendNotification, err)
		}
	}

	if err := activityPkg.ExecuteRaw(ctx, epifitemporal.PublishWorkflowUpdateEvent, nil, wfReqID); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.PublishWorkflowUpdateEvent, err)
	}

	return nil
}

// getRecurringPaymentStateAndAction - returns recurring payment state and action wrt workflow status
func getRecurringPaymentStateAndAction(workflowStatus stagePb.Status) (rpPb.RecurringPaymentState, rpPb.ActionState, error) {
	switch workflowStatus {
	case stagePb.Status_FAILED:
		return rpPb.RecurringPaymentState_FAILED, rpPb.ActionState_ACTION_FAILURE, nil
	case stagePb.Status_MANUAL_INTERVENTION:
		return rpPb.RecurringPaymentState_MANUAL_INTERVENTION, rpPb.ActionState_ACTION_MANUAL_INTERVENTION, nil
	case stagePb.Status_SUCCESSFUL:
		return rpPb.RecurringPaymentState_ACTIVATED, rpPb.ActionState_ACTION_SUCCESS, nil
	default:
		return rpPb.RecurringPaymentState_RECURRING_PAYMENT_STATE_UNSPECIFIED, rpPb.ActionState_ACTION_STATE_UNSPECIFIED,
			fmt.Errorf("no recurring payment state and action for workflow status")
	}
}
