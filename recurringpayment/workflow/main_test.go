package workflow_test

import (
	"os"
	"testing"

	epifitemporalLogging "github.com/epifi/be-common/pkg/epifitemporal/logging"
	epifitemporalTest "github.com/epifi/be-common/pkg/epifitemporal/test"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/recurringpayment/test"
)

var wts epifitemporalTest.WorkflowTestSuite

// nolint:dogsled
func TestMain(m *testing.M) {
	_, _, teardown := test.InitTestWorker(false)
	wts.SetLogger(epifitemporalLogging.NewZapAdapter(logger.Log))
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
