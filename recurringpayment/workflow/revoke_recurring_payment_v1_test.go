package workflow

import (
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/testsuite"
	"google.golang.org/protobuf/encoding/protojson"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	celestialActivityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	recurringPaymentPb "github.com/epifi/gamma/api/recurringpayment"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	rpPayloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	celestialActivityV2 "github.com/epifi/gamma/celestial/activity/v2"
	rpActivity "github.com/epifi/gamma/recurringpayment/activity"
)

func TestRevokeRecurringPaymentV1(t *testing.T) {
	t.Parallel()

	const (
		defaultWorkflowID               = "default-test-workflow-id"
		defaultRecurringPaymentId       = "default_test_recurring_payment_id"
		defaultRecurringPaymentActionId = "default_test_recurring_payment_action_id"
	)

	var (
		temporalPermanentErr = temporal.NewNonRetryableApplicationError("Permanent Error", "", epifierrors.ErrPermanent)
	)

	tests := []struct {
		name              string
		req               *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload
		setupMocks        func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment)
		setupExpectations func(t *testing.T, env *testsuite.TestWorkflowEnvironment)
		wantErr           bool
	}{
		{
			name: "should return error for failure in GetWorkflowProcessingParamsV2",
			req: &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{
				RecurringPaymentId:            defaultRecurringPaymentId,
				RecurringPaymentActionId:      defaultRecurringPaymentActionId,
				OriginalRecurringPaymentState: recurringPaymentPb.RecurringPaymentState_ACTIVATED.ToNewRecurringPaymentState(),
			},
			setupMocks: func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil, temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 0)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 0)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 0)

				env.AssertNumberOfCalls(t, string(rpNs.InitiateRevokeAtDomain), 0)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireRevokeStatusAtDomain), 0)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 0)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 0)
			},
			wantErr: true,
		},
		{
			name: "should return error when an invalid original recurring payment state is passed in the workflow parameters",
			req: &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{
				RecurringPaymentId:            defaultRecurringPaymentId,
				RecurringPaymentActionId:      defaultRecurringPaymentActionId,
				OriginalRecurringPaymentState: recurringPaymentPb.RecurringPaymentState_RECURRING_PAYMENT_STATE_UNSPECIFIED.ToNewRecurringPaymentState(),
			},
			setupMocks: func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment) {
				marshalledWfPayload, marshalErr := protojson.Marshal(req)
				require.NoError(t, marshalErr, "error in marshalling request payload to json")

				clientReqId := uuid.New().String()

				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(&activityPb.GetWorkflowProcessingParamsV2Response{
						WfReqParams: &workflowPb.ProcessingParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id: clientReqId,
							},
							Payload: marshalledWfPayload,
						},
					}, nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 0)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 0)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 0)

				env.AssertNumberOfCalls(t, string(rpNs.InitiateRevokeAtDomain), 0)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireRevokeStatusAtDomain), 0)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 0)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 0)
			},
			wantErr: true,
		},
		{
			name: "should return error for failure in initiating revoke at domain",
			req: &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{
				RecurringPaymentId:            defaultRecurringPaymentId,
				RecurringPaymentActionId:      defaultRecurringPaymentActionId,
				OriginalRecurringPaymentState: recurringPaymentPb.RecurringPaymentState_ACTIVATED.ToNewRecurringPaymentState(),
			},
			setupMocks: func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment) {
				marshalledWfPayload, marshalErr := protojson.Marshal(req)
				require.NoError(t, marshalErr, "error in marshalling request payload to json")

				clientReqId := uuid.New().String()

				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(&activityPb.GetWorkflowProcessingParamsV2Response{
						WfReqParams: &workflowPb.ProcessingParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id: clientReqId,
							},
							Payload: marshalledWfPayload,
						},
					}, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
				}).
					Return(&rpActivityPb.GetRecurringPaymentResponse{
						RecurringPayment: &recurringPaymentPb.RecurringPayment{State: recurringPaymentPb.RecurringPaymentState_REVOKE_QUEUED},
					}, nil)

				env.OnActivity(string(rpNs.InitiateRevokeAtDomain), mock.Anything, &rpActivityPb.InitiateRevokeAtDomainRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId:       req.GetRecurringPaymentId(),
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(nil, temporalPermanentErr)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_FAILED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 1)

				env.AssertNumberOfCalls(t, string(rpNs.GetRecurringPayment), 1)
				env.AssertNumberOfCalls(t, string(rpNs.InitiateRevokeAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireRevokeStatusAtDomain), 0)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 0)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 0)
			},
			wantErr: true,
		},
		{
			name: "should fail the celestial workflow if recurring payment is not in REVOKE_QUEUED state",
			req: &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{
				RecurringPaymentId:            defaultRecurringPaymentId,
				RecurringPaymentActionId:      defaultRecurringPaymentActionId,
				OriginalRecurringPaymentState: recurringPaymentPb.RecurringPaymentState_ACTIVATED.ToNewRecurringPaymentState(),
			},
			setupMocks: func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment) {
				marshalledWfPayload, marshalErr := protojson.Marshal(req)
				require.NoError(t, marshalErr, "error in marshalling request payload to json")

				clientReqId := uuid.New().String()

				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(&activityPb.GetWorkflowProcessingParamsV2Response{
						WfReqParams: &workflowPb.ProcessingParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id: clientReqId,
							},
							Payload: marshalledWfPayload,
						},
					}, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
				}).
					Return(&rpActivityPb.GetRecurringPaymentResponse{
						RecurringPayment: &recurringPaymentPb.RecurringPayment{State: recurringPaymentPb.RecurringPaymentState_REVOKE_INITIATED},
					}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_FAILED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 1)

				env.AssertNumberOfCalls(t, string(rpNs.GetRecurringPayment), 1)
			},
			wantErr: true,
		},
		{
			name: "should fail the celestial workflow if the recurring payment status could not be checked",
			req: &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{
				RecurringPaymentId:            defaultRecurringPaymentId,
				RecurringPaymentActionId:      defaultRecurringPaymentActionId,
				OriginalRecurringPaymentState: recurringPaymentPb.RecurringPaymentState_ACTIVATED.ToNewRecurringPaymentState(),
			},
			setupMocks: func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment) {
				marshalledWfPayload, marshalErr := protojson.Marshal(req)
				require.NoError(t, marshalErr, "error in marshalling request payload to json")

				clientReqId := uuid.New().String()

				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(&activityPb.GetWorkflowProcessingParamsV2Response{
						WfReqParams: &workflowPb.ProcessingParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id: clientReqId,
							},
							Payload: marshalledWfPayload,
						},
					}, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
				}).
					Return(nil, temporalPermanentErr)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_FAILED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 1)

				env.AssertNumberOfCalls(t, string(rpNs.GetRecurringPayment), 1)
			},
			wantErr: true,
		},
		{
			name: "should return error for failure updating workflow stage after recurring payment is not in state to be revoked",
			req: &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{
				RecurringPaymentId:            defaultRecurringPaymentId,
				RecurringPaymentActionId:      defaultRecurringPaymentActionId,
				OriginalRecurringPaymentState: recurringPaymentPb.RecurringPaymentState_ACTIVATED.ToNewRecurringPaymentState(),
			},
			setupMocks: func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment) {
				marshalledWfPayload, marshalErr := protojson.Marshal(req)
				require.NoError(t, marshalErr, "error in marshalling request payload to json")

				clientReqId := uuid.New().String()

				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(&activityPb.GetWorkflowProcessingParamsV2Response{
						WfReqParams: &workflowPb.ProcessingParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id: clientReqId,
							},
							Payload: marshalledWfPayload,
						},
					}, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
				}).
					Return(nil, temporalPermanentErr)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_FAILED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 0)

				env.AssertNumberOfCalls(t, string(rpNs.GetRecurringPayment), 1)
			},
			wantErr: true,
		},
		{
			name: "should return error and reset entity state for failure in enquiring revoke status at domain",
			req: &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{
				RecurringPaymentId:            defaultRecurringPaymentId,
				RecurringPaymentActionId:      defaultRecurringPaymentActionId,
				OriginalRecurringPaymentState: recurringPaymentPb.RecurringPaymentState_ACTIVATED.ToNewRecurringPaymentState(),
			},
			setupMocks: func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment) {
				marshalledWfPayload, marshalErr := protojson.Marshal(req)
				require.NoError(t, marshalErr, "error in marshalling request payload to json")

				clientReqId := uuid.New().String()

				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(&activityPb.GetWorkflowProcessingParamsV2Response{
						WfReqParams: &workflowPb.ProcessingParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id: clientReqId,
							},
							Payload: marshalledWfPayload,
						},
					}, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
				}).
					Return(&rpActivityPb.GetRecurringPaymentResponse{
						RecurringPayment: &recurringPaymentPb.RecurringPayment{State: recurringPaymentPb.RecurringPaymentState_REVOKE_QUEUED},
					}, nil)

				env.OnActivity(string(rpNs.InitiateRevokeAtDomain), mock.Anything, &rpActivityPb.InitiateRevokeAtDomainRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId:       req.GetRecurringPaymentId(),
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.InitiateRevokeAtDomainResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
					CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_REVOKE_INITIATED,
				}).Return(&rpActivityPb.UpdateRecurringPaymentStatusResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_CREATED,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.EnquireRevokeStatusAtDomain), mock.Anything, &rpActivityPb.EnquireRevokeStatusAtDomainRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(nil, temporalPermanentErr)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_MANUAL_INTERVENTION,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 1)

				env.AssertNumberOfCalls(t, string(rpNs.GetRecurringPayment), 1)
				env.AssertNumberOfCalls(t, string(rpNs.InitiateRevokeAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireRevokeStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 1)
			},
			wantErr: true,
		},
		{
			name: "should successfully revoke recurring payment in the happy case",
			req: &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{
				RecurringPaymentId:            defaultRecurringPaymentId,
				RecurringPaymentActionId:      defaultRecurringPaymentActionId,
				OriginalRecurringPaymentState: recurringPaymentPb.RecurringPaymentState_ACTIVATED.ToNewRecurringPaymentState(),
			},
			setupMocks: func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment) {
				marshalledWfPayload, marshalErr := protojson.Marshal(req)
				require.NoError(t, marshalErr, "error in marshalling request payload to json")

				clientReqId := uuid.New().String()

				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(&activityPb.GetWorkflowProcessingParamsV2Response{
						WfReqParams: &workflowPb.ProcessingParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id: clientReqId,
							},
							Payload: marshalledWfPayload,
						},
					}, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
				}).
					Return(&rpActivityPb.GetRecurringPaymentResponse{
						RecurringPayment: &recurringPaymentPb.RecurringPayment{State: recurringPaymentPb.RecurringPaymentState_REVOKE_QUEUED},
					}, nil)

				env.OnActivity(string(rpNs.InitiateRevokeAtDomain), mock.Anything, &rpActivityPb.InitiateRevokeAtDomainRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId:       req.GetRecurringPaymentId(),
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.InitiateRevokeAtDomainResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
					CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_REVOKE_INITIATED,
				}).Return(&rpActivityPb.UpdateRecurringPaymentStatusResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_CREATED,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.EnquireRevokeStatusAtDomain), mock.Anything, &rpActivityPb.EnquireRevokeStatusAtDomainRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.EnquireRevokeStatusAtDomainResponse{
						RevokeStatus: rpActivityPb.RevokeStatusAtDomain_DOMAIN_REVOKE_STATUS_SUCCESS,
					}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_SUCCESS,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.PublishRecurringPaymentActionUpdateEventResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.RevokeRecurringPaymentPostSuccessProcessing),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            rpPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
					CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_INITIATED,
					NextStatus:         rpPb.RecurringPaymentState_REVOKED,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentStatusResponse{}, nil)

				env.OnActivity(string(rpNs.SendStatusNotification), mock.Anything, &rpActivityPb.SendStatusNotificationRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentId:       req.GetRecurringPaymentId(),
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).Return(&rpActivityPb.SendStatusNotificationResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.RevokeRecurringPaymentPostSuccessProcessing),
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 3)

				env.AssertNumberOfCalls(t, string(rpNs.GetRecurringPayment), 1)
				env.AssertNumberOfCalls(t, string(rpNs.InitiateRevokeAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireRevokeStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 3)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 1)
			},
			wantErr: false,
		},
		{
			name: "should not revoke recurring payment and revert the recurring payment state back to original status in case of failure in revoke recurring payment on domain",
			req: &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{
				RecurringPaymentId:            defaultRecurringPaymentId,
				RecurringPaymentActionId:      defaultRecurringPaymentActionId,
				OriginalRecurringPaymentState: recurringPaymentPb.RecurringPaymentState_ACTIVATED.ToNewRecurringPaymentState(),
			},
			setupMocks: func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment) {
				marshalledWfPayload, marshalErr := protojson.Marshal(req)
				require.NoError(t, marshalErr, "error in marshalling request payload to json")

				clientReqId := uuid.New().String()

				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(&activityPb.GetWorkflowProcessingParamsV2Response{
						WfReqParams: &workflowPb.ProcessingParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id: clientReqId,
							},
							Payload: marshalledWfPayload,
						},
					}, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
				}).
					Return(&rpActivityPb.GetRecurringPaymentResponse{
						RecurringPayment: &recurringPaymentPb.RecurringPayment{State: recurringPaymentPb.RecurringPaymentState_REVOKE_QUEUED},
					}, nil)

				env.OnActivity(string(rpNs.InitiateRevokeAtDomain), mock.Anything, &rpActivityPb.InitiateRevokeAtDomainRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId:       req.GetRecurringPaymentId(),
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.InitiateRevokeAtDomainResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
					CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_REVOKE_INITIATED,
				}).Return(&rpActivityPb.UpdateRecurringPaymentStatusResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_CREATED,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.EnquireRevokeStatusAtDomain), mock.Anything, &rpActivityPb.EnquireRevokeStatusAtDomainRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.EnquireRevokeStatusAtDomainResponse{
						RevokeStatus: rpActivityPb.RevokeStatusAtDomain_DOMAIN_REVOKE_STATUS_FAILURE,
					}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_FAILURE,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.PublishRecurringPaymentActionUpdateEventResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_FAILED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.RevokeRecurringPaymentPostFailureProcessing),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            rpPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               rpPb.ActionState_ACTION_FAILURE,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
					CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_INITIATED,
					NextStatus:         rpPb.RecurringPaymentState_ACTIVATED,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentStatusResponse{}, nil)

				env.OnActivity(string(rpNs.SendStatusNotification), mock.Anything, &rpActivityPb.SendStatusNotificationRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentId:       req.GetRecurringPaymentId(),
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).Return(&rpActivityPb.SendStatusNotificationResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.RevokeRecurringPaymentPostFailureProcessing),
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 3)

				env.AssertNumberOfCalls(t, string(rpNs.GetRecurringPayment), 1)
				env.AssertNumberOfCalls(t, string(rpNs.InitiateRevokeAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireRevokeStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 3)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 1)
			},
			wantErr: false,
		},
		{
			name: "should return error for failure in updating recurring payment status to REVOKE_INITIATED",
			req: &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{
				RecurringPaymentId:            defaultRecurringPaymentId,
				RecurringPaymentActionId:      defaultRecurringPaymentActionId,
				OriginalRecurringPaymentState: recurringPaymentPb.RecurringPaymentState_ACTIVATED.ToNewRecurringPaymentState(),
			},
			setupMocks: func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment) {
				marshalledWfPayload, marshalErr := protojson.Marshal(req)
				require.NoError(t, marshalErr, "error in marshalling request payload to json")

				clientReqId := uuid.New().String()

				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(&activityPb.GetWorkflowProcessingParamsV2Response{
						WfReqParams: &workflowPb.ProcessingParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id: clientReqId,
							},
							Payload: marshalledWfPayload,
						},
					}, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
				}).
					Return(&rpActivityPb.GetRecurringPaymentResponse{
						RecurringPayment: &recurringPaymentPb.RecurringPayment{State: recurringPaymentPb.RecurringPaymentState_REVOKE_QUEUED},
					}, nil)

				env.OnActivity(string(rpNs.InitiateRevokeAtDomain), mock.Anything, &rpActivityPb.InitiateRevokeAtDomainRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId:       req.GetRecurringPaymentId(),
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.InitiateRevokeAtDomainResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
					CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_REVOKE_INITIATED,
				}).
					Return(nil, temporalPermanentErr)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_FAILED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 1)

				env.AssertNumberOfCalls(t, string(rpNs.GetRecurringPayment), 1)
				env.AssertNumberOfCalls(t, string(rpNs.InitiateRevokeAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireRevokeStatusAtDomain), 0)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 0)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 0)
			},
			wantErr: true,
		},
		{
			name: "should return error for failure in updating recurring payment action status to ACTION_IN_PROGRESS",
			req: &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{
				RecurringPaymentId:            defaultRecurringPaymentId,
				RecurringPaymentActionId:      defaultRecurringPaymentActionId,
				OriginalRecurringPaymentState: recurringPaymentPb.RecurringPaymentState_ACTIVATED.ToNewRecurringPaymentState(),
			},
			setupMocks: func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment) {
				marshalledWfPayload, marshalErr := protojson.Marshal(req)
				require.NoError(t, marshalErr, "error in marshalling request payload to json")

				clientReqId := uuid.New().String()

				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(&activityPb.GetWorkflowProcessingParamsV2Response{
						WfReqParams: &workflowPb.ProcessingParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id: clientReqId,
							},
							Payload: marshalledWfPayload,
						},
					}, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
				}).
					Return(&rpActivityPb.GetRecurringPaymentResponse{
						RecurringPayment: &recurringPaymentPb.RecurringPayment{State: recurringPaymentPb.RecurringPaymentState_REVOKE_QUEUED},
					}, nil)

				env.OnActivity(string(rpNs.InitiateRevokeAtDomain), mock.Anything, &rpActivityPb.InitiateRevokeAtDomainRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId:       req.GetRecurringPaymentId(),
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.InitiateRevokeAtDomainResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
					CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_REVOKE_INITIATED,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentStatusResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_CREATED,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
				}).Return(nil, temporalPermanentErr)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_FAILED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 1)

				env.AssertNumberOfCalls(t, string(rpNs.GetRecurringPayment), 1)
				env.AssertNumberOfCalls(t, string(rpNs.InitiateRevokeAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireRevokeStatusAtDomain), 0)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 0)
			},
			wantErr: true,
		},
		{
			name: "should move the workflow state to MANUAL_INTERVENTION for unhandled status returned in the enquiry",
			req: &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{
				RecurringPaymentId:            defaultRecurringPaymentId,
				RecurringPaymentActionId:      defaultRecurringPaymentActionId,
				OriginalRecurringPaymentState: recurringPaymentPb.RecurringPaymentState_ACTIVATED.ToNewRecurringPaymentState(),
			},
			setupMocks: func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment) {
				marshalledWfPayload, marshalErr := protojson.Marshal(req)
				require.NoError(t, marshalErr, "error in marshalling request payload to json")

				clientReqId := uuid.New().String()

				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(&activityPb.GetWorkflowProcessingParamsV2Response{
						WfReqParams: &workflowPb.ProcessingParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id: clientReqId,
							},
							Payload: marshalledWfPayload,
						},
					}, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
				}).
					Return(&rpActivityPb.GetRecurringPaymentResponse{
						RecurringPayment: &recurringPaymentPb.RecurringPayment{State: recurringPaymentPb.RecurringPaymentState_REVOKE_QUEUED},
					}, nil)

				env.OnActivity(string(rpNs.InitiateRevokeAtDomain), mock.Anything, &rpActivityPb.InitiateRevokeAtDomainRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId:       req.GetRecurringPaymentId(),
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.InitiateRevokeAtDomainResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
					CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_REVOKE_INITIATED,
				}).Return(&rpActivityPb.UpdateRecurringPaymentStatusResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_CREATED,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.EnquireRevokeStatusAtDomain), mock.Anything, &rpActivityPb.EnquireRevokeStatusAtDomainRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.EnquireRevokeStatusAtDomainResponse{
						RevokeStatus: rpActivityPb.RevokeStatusAtDomain_DOMAIN_REVOKE_STATUS_UNSPECIFIED,
					}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_MANUAL_INTERVENTION,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 1)

				env.AssertNumberOfCalls(t, string(rpNs.GetRecurringPayment), 1)
				env.AssertNumberOfCalls(t, string(rpNs.InitiateRevokeAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireRevokeStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 0)
			},
			wantErr: true,
		},
		{
			name: "should return error for failure in updating workflow stage to MANUAL_INTERVENTION when unhandled status is returned from enquiry",
			req: &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{
				RecurringPaymentId:            defaultRecurringPaymentId,
				RecurringPaymentActionId:      defaultRecurringPaymentActionId,
				OriginalRecurringPaymentState: recurringPaymentPb.RecurringPaymentState_ACTIVATED.ToNewRecurringPaymentState(),
			},
			setupMocks: func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment) {
				marshalledWfPayload, marshalErr := protojson.Marshal(req)
				require.NoError(t, marshalErr, "error in marshalling request payload to json")

				clientReqId := uuid.New().String()

				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(&activityPb.GetWorkflowProcessingParamsV2Response{
						WfReqParams: &workflowPb.ProcessingParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id: clientReqId,
							},
							Payload: marshalledWfPayload,
						},
					}, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
				}).
					Return(&rpActivityPb.GetRecurringPaymentResponse{
						RecurringPayment: &recurringPaymentPb.RecurringPayment{State: recurringPaymentPb.RecurringPaymentState_REVOKE_QUEUED},
					}, nil)

				env.OnActivity(string(rpNs.InitiateRevokeAtDomain), mock.Anything, &rpActivityPb.InitiateRevokeAtDomainRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId:       req.GetRecurringPaymentId(),
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.InitiateRevokeAtDomainResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
					CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_REVOKE_INITIATED,
				}).Return(&rpActivityPb.UpdateRecurringPaymentStatusResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_CREATED,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.EnquireRevokeStatusAtDomain), mock.Anything, &rpActivityPb.EnquireRevokeStatusAtDomainRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.EnquireRevokeStatusAtDomainResponse{
						RevokeStatus: rpActivityPb.RevokeStatusAtDomain_DOMAIN_REVOKE_STATUS_UNSPECIFIED,
					}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_MANUAL_INTERVENTION,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 1)

				env.AssertNumberOfCalls(t, string(rpNs.GetRecurringPayment), 1)
				env.AssertNumberOfCalls(t, string(rpNs.InitiateRevokeAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireRevokeStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 0)
			},
			wantErr: true,
		},
		{
			name: "should return error for failure in updating recurring payment action status to failure",
			req: &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{
				RecurringPaymentId:            defaultRecurringPaymentId,
				RecurringPaymentActionId:      defaultRecurringPaymentActionId,
				OriginalRecurringPaymentState: recurringPaymentPb.RecurringPaymentState_ACTIVATED.ToNewRecurringPaymentState(),
			},
			setupMocks: func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment) {
				marshalledWfPayload, marshalErr := protojson.Marshal(req)
				require.NoError(t, marshalErr, "error in marshalling request payload to json")

				clientReqId := uuid.New().String()

				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(&activityPb.GetWorkflowProcessingParamsV2Response{
						WfReqParams: &workflowPb.ProcessingParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id: clientReqId,
							},
							Payload: marshalledWfPayload,
						},
					}, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
				}).
					Return(&rpActivityPb.GetRecurringPaymentResponse{
						RecurringPayment: &recurringPaymentPb.RecurringPayment{State: recurringPaymentPb.RecurringPaymentState_REVOKE_QUEUED},
					}, nil)

				env.OnActivity(string(rpNs.InitiateRevokeAtDomain), mock.Anything, &rpActivityPb.InitiateRevokeAtDomainRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId:       req.GetRecurringPaymentId(),
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.InitiateRevokeAtDomainResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
					CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_REVOKE_INITIATED,
				}).Return(&rpActivityPb.UpdateRecurringPaymentStatusResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_CREATED,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.EnquireRevokeStatusAtDomain), mock.Anything, &rpActivityPb.EnquireRevokeStatusAtDomainRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.EnquireRevokeStatusAtDomainResponse{
						RevokeStatus: rpActivityPb.RevokeStatusAtDomain_DOMAIN_REVOKE_STATUS_FAILURE,
					}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            rpPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               rpPb.ActionState_ACTION_FAILURE,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 1)

				env.AssertNumberOfCalls(t, string(rpNs.GetRecurringPayment), 1)
				env.AssertNumberOfCalls(t, string(rpNs.InitiateRevokeAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireRevokeStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 0)
			},
			wantErr: true,
		},
		{
			name: "should return error for failure in updating workflow stage after completing domain enquiry stage",
			req: &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{
				RecurringPaymentId:            defaultRecurringPaymentId,
				RecurringPaymentActionId:      defaultRecurringPaymentActionId,
				OriginalRecurringPaymentState: recurringPaymentPb.RecurringPaymentState_ACTIVATED.ToNewRecurringPaymentState(),
			},
			setupMocks: func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment) {
				marshalledWfPayload, marshalErr := protojson.Marshal(req)
				require.NoError(t, marshalErr, "error in marshalling request payload to json")

				clientReqId := uuid.New().String()

				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(&activityPb.GetWorkflowProcessingParamsV2Response{
						WfReqParams: &workflowPb.ProcessingParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id: clientReqId,
							},
							Payload: marshalledWfPayload,
						},
					}, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
				}).
					Return(&rpActivityPb.GetRecurringPaymentResponse{
						RecurringPayment: &recurringPaymentPb.RecurringPayment{State: recurringPaymentPb.RecurringPaymentState_REVOKE_QUEUED},
					}, nil)

				env.OnActivity(string(rpNs.InitiateRevokeAtDomain), mock.Anything, &rpActivityPb.InitiateRevokeAtDomainRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId:       req.GetRecurringPaymentId(),
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.InitiateRevokeAtDomainResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
					CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_REVOKE_INITIATED,
				}).Return(&rpActivityPb.UpdateRecurringPaymentStatusResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_CREATED,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.EnquireRevokeStatusAtDomain), mock.Anything, &rpActivityPb.EnquireRevokeStatusAtDomainRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.EnquireRevokeStatusAtDomainResponse{
						RevokeStatus: rpActivityPb.RevokeStatusAtDomain_DOMAIN_REVOKE_STATUS_SUCCESS,
					}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_SUCCESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.PublishRecurringPaymentActionUpdateEventResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 1)

				env.AssertNumberOfCalls(t, string(rpNs.GetRecurringPayment), 1)
				env.AssertNumberOfCalls(t, string(rpNs.InitiateRevokeAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireRevokeStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 0)
			},
			wantErr: true,
		},
		{
			name: "should return error for failure in updating recurring payment action status to terminal state in post processing",
			req: &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{
				RecurringPaymentId:            defaultRecurringPaymentId,
				RecurringPaymentActionId:      defaultRecurringPaymentActionId,
				OriginalRecurringPaymentState: recurringPaymentPb.RecurringPaymentState_ACTIVATED.ToNewRecurringPaymentState(),
			},
			setupMocks: func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment) {
				marshalledWfPayload, marshalErr := protojson.Marshal(req)
				require.NoError(t, marshalErr, "error in marshalling request payload to json")

				clientReqId := uuid.New().String()

				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(&activityPb.GetWorkflowProcessingParamsV2Response{
						WfReqParams: &workflowPb.ProcessingParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id: clientReqId,
							},
							Payload: marshalledWfPayload,
						},
					}, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
				}).
					Return(&rpActivityPb.GetRecurringPaymentResponse{
						RecurringPayment: &recurringPaymentPb.RecurringPayment{State: recurringPaymentPb.RecurringPaymentState_REVOKE_QUEUED},
					}, nil)

				env.OnActivity(string(rpNs.InitiateRevokeAtDomain), mock.Anything, &rpActivityPb.InitiateRevokeAtDomainRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId:       req.GetRecurringPaymentId(),
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.InitiateRevokeAtDomainResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
					CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_REVOKE_INITIATED,
				}).Return(&rpActivityPb.UpdateRecurringPaymentStatusResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_CREATED,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.EnquireRevokeStatusAtDomain), mock.Anything, &rpActivityPb.EnquireRevokeStatusAtDomainRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.EnquireRevokeStatusAtDomainResponse{
						RevokeStatus: rpActivityPb.RevokeStatusAtDomain_DOMAIN_REVOKE_STATUS_SUCCESS,
					}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_SUCCESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.PublishRecurringPaymentActionUpdateEventResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.RevokeRecurringPaymentPostSuccessProcessing),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            rpPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(nil, temporalPermanentErr)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_FAILED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.RevokeRecurringPaymentPostSuccessProcessing),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 3)

				env.AssertNumberOfCalls(t, string(rpNs.GetRecurringPayment), 1)
				env.AssertNumberOfCalls(t, string(rpNs.InitiateRevokeAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireRevokeStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 3)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 0)
			},
			wantErr: true,
		},
		{
			name: "should return error for failure in updating workflow stage status to failed after failure in updating recurring payment action status to terminal state in post processing",
			req: &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{
				RecurringPaymentId:            defaultRecurringPaymentId,
				RecurringPaymentActionId:      defaultRecurringPaymentActionId,
				OriginalRecurringPaymentState: recurringPaymentPb.RecurringPaymentState_ACTIVATED.ToNewRecurringPaymentState(),
			},
			setupMocks: func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment) {
				marshalledWfPayload, marshalErr := protojson.Marshal(req)
				require.NoError(t, marshalErr, "error in marshalling request payload to json")

				clientReqId := uuid.New().String()

				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(&activityPb.GetWorkflowProcessingParamsV2Response{
						WfReqParams: &workflowPb.ProcessingParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id: clientReqId,
							},
							Payload: marshalledWfPayload,
						},
					}, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
				}).
					Return(&rpActivityPb.GetRecurringPaymentResponse{
						RecurringPayment: &recurringPaymentPb.RecurringPayment{State: recurringPaymentPb.RecurringPaymentState_REVOKE_QUEUED},
					}, nil)

				env.OnActivity(string(rpNs.InitiateRevokeAtDomain), mock.Anything, &rpActivityPb.InitiateRevokeAtDomainRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId:       req.GetRecurringPaymentId(),
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.InitiateRevokeAtDomainResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
					CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_REVOKE_INITIATED,
				}).Return(&rpActivityPb.UpdateRecurringPaymentStatusResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_CREATED,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.EnquireRevokeStatusAtDomain), mock.Anything, &rpActivityPb.EnquireRevokeStatusAtDomainRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.EnquireRevokeStatusAtDomainResponse{
						RevokeStatus: rpActivityPb.RevokeStatusAtDomain_DOMAIN_REVOKE_STATUS_SUCCESS,
					}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_SUCCESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.PublishRecurringPaymentActionUpdateEventResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.RevokeRecurringPaymentPostSuccessProcessing),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            rpPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(nil, temporalPermanentErr)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_FAILED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.RevokeRecurringPaymentPostSuccessProcessing),
				}).
					Return(temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 2)

				env.AssertNumberOfCalls(t, string(rpNs.GetRecurringPayment), 1)
				env.AssertNumberOfCalls(t, string(rpNs.InitiateRevokeAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireRevokeStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 3)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 0)
			},
			wantErr: true,
		},
		{
			name: "should return error for failure in updating recurring payment to terminal state in post processing",
			req: &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{
				RecurringPaymentId:            defaultRecurringPaymentId,
				RecurringPaymentActionId:      defaultRecurringPaymentActionId,
				OriginalRecurringPaymentState: recurringPaymentPb.RecurringPaymentState_ACTIVATED.ToNewRecurringPaymentState(),
			},
			setupMocks: func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment) {
				marshalledWfPayload, marshalErr := protojson.Marshal(req)
				require.NoError(t, marshalErr, "error in marshalling request payload to json")

				clientReqId := uuid.New().String()

				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(&activityPb.GetWorkflowProcessingParamsV2Response{
						WfReqParams: &workflowPb.ProcessingParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id: clientReqId,
							},
							Payload: marshalledWfPayload,
						},
					}, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
				}).
					Return(&rpActivityPb.GetRecurringPaymentResponse{
						RecurringPayment: &recurringPaymentPb.RecurringPayment{State: recurringPaymentPb.RecurringPaymentState_REVOKE_QUEUED},
					}, nil)

				env.OnActivity(string(rpNs.InitiateRevokeAtDomain), mock.Anything, &rpActivityPb.InitiateRevokeAtDomainRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId:       req.GetRecurringPaymentId(),
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.InitiateRevokeAtDomainResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
					CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_REVOKE_INITIATED,
				}).Return(&rpActivityPb.UpdateRecurringPaymentStatusResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_CREATED,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.EnquireRevokeStatusAtDomain), mock.Anything, &rpActivityPb.EnquireRevokeStatusAtDomainRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.EnquireRevokeStatusAtDomainResponse{
						RevokeStatus: rpActivityPb.RevokeStatusAtDomain_DOMAIN_REVOKE_STATUS_SUCCESS,
					}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_SUCCESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.PublishRecurringPaymentActionUpdateEventResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.RevokeRecurringPaymentPostSuccessProcessing),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            rpPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
					CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_INITIATED,
					NextStatus:         rpPb.RecurringPaymentState_REVOKED,
				}).
					Return(nil, temporalPermanentErr)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_FAILED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.RevokeRecurringPaymentPostSuccessProcessing),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 3)

				env.AssertNumberOfCalls(t, string(rpNs.GetRecurringPayment), 1)
				env.AssertNumberOfCalls(t, string(rpNs.InitiateRevokeAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireRevokeStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 3)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 0)
			},
			wantErr: true,
		},
		{
			name: "should return error for failure in updating workflow stage status to failed after failure in updating recurring payment status",
			req: &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{
				RecurringPaymentId:            defaultRecurringPaymentId,
				RecurringPaymentActionId:      defaultRecurringPaymentActionId,
				OriginalRecurringPaymentState: recurringPaymentPb.RecurringPaymentState_ACTIVATED.ToNewRecurringPaymentState(),
			},
			setupMocks: func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment) {
				marshalledWfPayload, marshalErr := protojson.Marshal(req)
				require.NoError(t, marshalErr, "error in marshalling request payload to json")

				clientReqId := uuid.New().String()

				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(&activityPb.GetWorkflowProcessingParamsV2Response{
						WfReqParams: &workflowPb.ProcessingParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id: clientReqId,
							},
							Payload: marshalledWfPayload,
						},
					}, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
				}).
					Return(&rpActivityPb.GetRecurringPaymentResponse{
						RecurringPayment: &recurringPaymentPb.RecurringPayment{State: recurringPaymentPb.RecurringPaymentState_REVOKE_QUEUED},
					}, nil)

				env.OnActivity(string(rpNs.InitiateRevokeAtDomain), mock.Anything, &rpActivityPb.InitiateRevokeAtDomainRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId:       req.GetRecurringPaymentId(),
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.InitiateRevokeAtDomainResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
					CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_REVOKE_INITIATED,
				}).Return(&rpActivityPb.UpdateRecurringPaymentStatusResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_CREATED,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.EnquireRevokeStatusAtDomain), mock.Anything, &rpActivityPb.EnquireRevokeStatusAtDomainRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.EnquireRevokeStatusAtDomainResponse{
						RevokeStatus: rpActivityPb.RevokeStatusAtDomain_DOMAIN_REVOKE_STATUS_SUCCESS,
					}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_SUCCESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.PublishRecurringPaymentActionUpdateEventResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.RevokeRecurringPaymentPostSuccessProcessing),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            rpPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
					CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_INITIATED,
					NextStatus:         rpPb.RecurringPaymentState_REVOKED,
				}).
					Return(nil, temporalPermanentErr)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_FAILED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.RevokeRecurringPaymentPostSuccessProcessing),
				}).
					Return(temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 2)

				env.AssertNumberOfCalls(t, string(rpNs.GetRecurringPayment), 1)
				env.AssertNumberOfCalls(t, string(rpNs.InitiateRevokeAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireRevokeStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 3)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 0)
			},
			wantErr: true,
		},
		{
			name: "should return error for failure in failure in sending notification in post processing",
			req: &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{
				RecurringPaymentId:            defaultRecurringPaymentId,
				RecurringPaymentActionId:      defaultRecurringPaymentActionId,
				OriginalRecurringPaymentState: recurringPaymentPb.RecurringPaymentState_ACTIVATED.ToNewRecurringPaymentState(),
			},
			setupMocks: func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment) {
				marshalledWfPayload, marshalErr := protojson.Marshal(req)
				require.NoError(t, marshalErr, "error in marshalling request payload to json")

				clientReqId := uuid.New().String()

				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(&activityPb.GetWorkflowProcessingParamsV2Response{
						WfReqParams: &workflowPb.ProcessingParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id: clientReqId,
							},
							Payload: marshalledWfPayload,
						},
					}, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
				}).
					Return(&rpActivityPb.GetRecurringPaymentResponse{
						RecurringPayment: &recurringPaymentPb.RecurringPayment{State: recurringPaymentPb.RecurringPaymentState_REVOKE_QUEUED},
					}, nil)

				env.OnActivity(string(rpNs.InitiateRevokeAtDomain), mock.Anything, &rpActivityPb.InitiateRevokeAtDomainRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId:       req.GetRecurringPaymentId(),
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.InitiateRevokeAtDomainResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
					CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_REVOKE_INITIATED,
				}).Return(&rpActivityPb.UpdateRecurringPaymentStatusResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_CREATED,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.EnquireRevokeStatusAtDomain), mock.Anything, &rpActivityPb.EnquireRevokeStatusAtDomainRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.EnquireRevokeStatusAtDomainResponse{
						RevokeStatus: rpActivityPb.RevokeStatusAtDomain_DOMAIN_REVOKE_STATUS_SUCCESS,
					}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_SUCCESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.PublishRecurringPaymentActionUpdateEventResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.RevokeRecurringPaymentPostSuccessProcessing),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            rpPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
					CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_INITIATED,
					NextStatus:         rpPb.RecurringPaymentState_REVOKED,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentStatusResponse{}, nil)

				env.OnActivity(string(rpNs.SendStatusNotification), mock.Anything, &rpActivityPb.SendStatusNotificationRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentId:       req.GetRecurringPaymentId(),
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).Return(nil, temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 2)

				env.AssertNumberOfCalls(t, string(rpNs.GetRecurringPayment), 1)
				env.AssertNumberOfCalls(t, string(rpNs.InitiateRevokeAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireRevokeStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 3)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 1)
			},
			wantErr: true,
		},
		{
			name: "should return error when successfully revoke recurring payment in the happy case",
			req: &rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload{
				RecurringPaymentId:            defaultRecurringPaymentId,
				RecurringPaymentActionId:      defaultRecurringPaymentActionId,
				OriginalRecurringPaymentState: recurringPaymentPb.RecurringPaymentState_ACTIVATED.ToNewRecurringPaymentState(),
			},
			setupMocks: func(req *rpPayloadPb.RevokeRecurringPaymentV1WorkflowPayload, env *testsuite.TestWorkflowEnvironment) {
				marshalledWfPayload, marshalErr := protojson.Marshal(req)
				require.NoError(t, marshalErr, "error in marshalling request payload to json")

				clientReqId := uuid.New().String()

				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(&activityPb.GetWorkflowProcessingParamsV2Response{
						WfReqParams: &workflowPb.ProcessingParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id: clientReqId,
							},
							Payload: marshalledWfPayload,
						},
					}, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
				}).
					Return(&rpActivityPb.GetRecurringPaymentResponse{
						RecurringPayment: &recurringPaymentPb.RecurringPayment{State: recurringPaymentPb.RecurringPaymentState_REVOKE_QUEUED},
					}, nil)

				env.OnActivity(string(rpNs.InitiateRevokeAtDomain), mock.Anything, &rpActivityPb.InitiateRevokeAtDomainRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId:       req.GetRecurringPaymentId(),
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.InitiateRevokeAtDomainResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership:   commontypes.Ownership_EPIFI_TECH,
						ClientReqId: clientReqId,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
					CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_REVOKE_INITIATED,
				}).Return(&rpActivityPb.UpdateRecurringPaymentStatusResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_CREATED,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.InitiateRevokeAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.EnquireRevokeStatusAtDomain), mock.Anything, &rpActivityPb.EnquireRevokeStatusAtDomainRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.EnquireRevokeStatusAtDomainResponse{
						RevokeStatus: rpActivityPb.RevokeStatusAtDomain_DOMAIN_REVOKE_STATUS_SUCCESS,
					}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            recurringPaymentPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               recurringPaymentPb.ActionState_ACTION_SUCCESS,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.PublishRecurringPaymentActionUpdateEventResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnquireRevokeStatusAtDomainStage),
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				}).
					Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_INITIATED,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.RevokeRecurringPaymentPostSuccessProcessing),
				}).
					Return(nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
					CurrentStatus:            rpPb.ActionState_ACTION_IN_PROGRESS,
					NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
					ActionDetailedStatusInfo: nil,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentId: req.GetRecurringPaymentId(),
					CurrentStatus:      rpPb.RecurringPaymentState_REVOKE_INITIATED,
					NextStatus:         rpPb.RecurringPaymentState_REVOKED,
				}).
					Return(&rpActivityPb.UpdateRecurringPaymentStatusResponse{}, nil)

				env.OnActivity(string(rpNs.SendStatusNotification), mock.Anything, &rpActivityPb.SendStatusNotificationRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					RecurringPaymentId:       req.GetRecurringPaymentId(),
					RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
				}).
					Return(&rpActivityPb.SendStatusNotificationResponse{}, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId:   defaultWorkflowID,
					Status:    stagePb.Status_SUCCESSFUL,
					StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.RevokeRecurringPaymentPostSuccessProcessing),
				}).
					Return(temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 2)

				env.AssertNumberOfCalls(t, string(rpNs.GetRecurringPayment), 1)
				env.AssertNumberOfCalls(t, string(rpNs.InitiateRevokeAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireRevokeStatusAtDomain), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 3)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 1)
			},
			wantErr: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			env := wts.NewTestWorkflowEnvironment()
			env.RegisterWorkflow(RevokeRecurringPaymentV1)
			env.RegisterActivity(&celestialActivityV2.Processor{})
			env.RegisterActivity(&rpActivity.Processor{})

			tc.setupMocks(tc.req, env)
			env.ExecuteWorkflow(RevokeRecurringPaymentV1, tc.req)

			assert.True(t, env.IsWorkflowCompleted())

			tc.setupExpectations(t, env)
			env.AssertExpectations(t)

			if (env.GetWorkflowError() != nil) != tc.wantErr {
				t.Errorf("RevokeRecurringPaymentV1() error = %v, wantErr %v", env.GetWorkflowError(), tc.wantErr)
			}
		})
	}
}
