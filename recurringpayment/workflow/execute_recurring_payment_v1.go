package workflow

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	celestialActivityPb "github.com/epifi/be-common/api/celestial/activity"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/logger"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	rpPayloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
)

// ExecuteRecurringPaymentWithoutAuthV1 is the workflow responsible for orchestrating recurring payment execution
// todo(Harleen Singh): evaluate if there is not much difference in auth and no-auth workflow, and merge both into 1 if possible, else remove this todo
// nolint:funlen
func ExecuteRecurringPaymentWithoutAuthV1(ctx workflow.Context, _ *rpPayloadPb.ExecuteRecurringPaymentWithoutAuthV1WorkflowPayload) error {
	lg := workflow.GetLogger(ctx)
	// unmarshal the parameters required for the workflow
	workflowPayload := &rpPayloadPb.ExecuteRecurringPaymentWithoutAuthV1WorkflowPayload{}
	wfProcessingParams, wfProcessingParamsErr := celestialPkg.GetWorkflowProcessingReqParams(ctx, workflowPayload)
	if wfProcessingParamsErr != nil {
		lg.Error("failed to fetch workflow processing params", zap.Error(wfProcessingParamsErr))
		return wfProcessingParamsErr
	}
	clientReqId := wfProcessingParams.GetClientReqId().GetId()

	// =========================== STAGE EXECUTION AT DOMAIN ==================================
	initiateExecutionAtDomainStageStatus, initiateExecutionAtDomainErr := processInitiateExecutionAtDomainStage(ctx, &initiateDomainExecutionStageReq{
		clientReqId:                         clientReqId,
		recurringPaymentId:                  workflowPayload.GetRecurringPaymentId(),
		recurringPaymentActionId:            workflowPayload.GetRecurringPaymentActionId(),
		recurringPaymentTypeSpecificPayload: workflowPayload.GetRecurringPaymentTypeSpecificPayload(),
	})
	if initiateExecutionAtDomainErr != nil {
		lg.Error("error in initiated execution at domain stage", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, workflowPayload.RecurringPaymentActionId), zap.Error(initiateExecutionAtDomainErr))
		return initiateExecutionAtDomainErr
	}

	if initiateExecutionAtDomainStageStatus != stagePb.Status_SUCCESSFUL {
		lg.Error("initiate execution at domain stage was not successful", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, workflowPayload.RecurringPaymentActionId), zap.String(logger.STAGE_STATUS, initiateExecutionAtDomainStageStatus.String()))
		return fmt.Errorf("initiate mandate execution at domain is not successful, stageStatus: %s", initiateExecutionAtDomainStageStatus)
	}

	// ========================= STAGE ENQUIRE STATUS AT DOMAIN ===============================
	enquireExecutionStatusAtDomainStageStatus, enquireExecutionStatusAtDomainErr := processEnquireExecutionStatusAtDomainStage(ctx, &enquireExecutionStatusAtDomainStageReq{
		clientReqId:              clientReqId,
		recurringPaymentActionId: workflowPayload.GetRecurringPaymentActionId(),
		recurringPaymentId:       workflowPayload.GetRecurringPaymentId(),
	})

	if enquireExecutionStatusAtDomainErr != nil || enquireExecutionStatusAtDomainStageStatus == stagePb.Status_MANUAL_INTERVENTION {
		lg.Error("enquire execution status at domain stage was not successful", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, workflowPayload.RecurringPaymentActionId), zap.String(logger.STAGE_STATUS, enquireExecutionStatusAtDomainStageStatus.String()), zap.Error(enquireExecutionStatusAtDomainErr))
		return fmt.Errorf("failed to enquire status at domain,err: %w, stageStatus: %s", enquireExecutionStatusAtDomainErr, enquireExecutionStatusAtDomainStageStatus)
	}

	// ============================	STAGE POST EXECUTION PROCESSING =============================
	var postProcessingStage epifitemporal.Stage
	switch enquireExecutionStatusAtDomainStageStatus {
	case stagePb.Status_SUCCESSFUL:
		postProcessingStage = rpNs.ExecuteRecurringPaymentPostSuccessProcessing
	case stagePb.Status_FAILED:
		postProcessingStage = rpNs.ExecuteRecurringPaymentPostFailureProcessing
	default:
		lg.Error("unhandled domain execution stage status", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, workflowPayload.RecurringPaymentActionId), zap.String(logger.STAGE_STATUS, enquireExecutionStatusAtDomainStageStatus.String()))
		return fmt.Errorf("domain execution status is unknown,: stageStatus: %s", enquireExecutionStatusAtDomainStageStatus)
	}

	executeRecurringPaymentPostProcessingStageStatus, executeRecurringPaymentPostProcessingErr := processExecuteRecurringPaymentPostProcessing(ctx, &executeRecurringPaymentPostProcessingReq{
		clientReqId:              clientReqId,
		recurringPaymentId:       workflowPayload.GetRecurringPaymentId(),
		recurringPaymentActionId: workflowPayload.GetRecurringPaymentActionId(),
		stage:                    postProcessingStage,
	})
	if executeRecurringPaymentPostProcessingErr != nil {
		lg.Error("error in execute recurring payment post processing stage", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, workflowPayload.RecurringPaymentActionId), zap.String(logger.STAGE, string(rpNs.ExecuteRecurringPaymentPostSuccessProcessing)), zap.Error(executeRecurringPaymentPostProcessingErr))
		return executeRecurringPaymentPostProcessingErr
	}
	if executeRecurringPaymentPostProcessingStageStatus != stagePb.Status_SUCCESSFUL {
		lg.Error("execute recurring payment post processing stage was not successful", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, workflowPayload.RecurringPaymentActionId), zap.String(logger.STAGE_STATUS, executeRecurringPaymentPostProcessingStageStatus.String()))
		return nil
	}

	return nil
}

type initiateDomainExecutionStageReq struct {
	clientReqId                         string
	recurringPaymentId                  string
	recurringPaymentActionId            string
	recurringPaymentTypeSpecificPayload *rpPayloadPb.RecurringPaymentTypeSpecificExecutionPayload
}

// processInitiateExecutionAtDomainStage is responsbile for initiating execution at respective domain service
func processInitiateExecutionAtDomainStage(ctx workflow.Context, req *initiateDomainExecutionStageReq) (stagePb.Status, error) {
	var (
		lg           = workflow.GetLogger(ctx)
		currentStage = rpNs.InitiateExecutionATDomain
		stageStatus  = stagePb.Status_INITIATED
	)

	// ==================== initiate stage for the worfklow ===================
	if initiateWorkflowStageErr := celestialPkg.InitiateWorkflowStage(ctx, currentStage, stageStatus); initiateWorkflowStageErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to initiate workflow stage: %s: %w", currentStage, initiateWorkflowStageErr)
	}
	// ==================== initiate domain execution ========================
	initiateDomainExecutionResponse := &rpActivityPb.InitiateExecutionAtDomainResponse{}
	if initiateDomainExecutionErr := activityPkg.Execute(ctx, rpNs.InitiateExecutionAtDomain, initiateDomainExecutionResponse, &rpActivityPb.InitiateExecutionAtDomainRequest{
		RequestHeader: &celestialActivityPb.RequestHeader{
			ClientReqId: req.clientReqId,
			Ownership:   commontypes.Ownership_EPIFI_TECH,
		},
		RecurringPaymentId:                  req.recurringPaymentId,
		RecurringPaymentActionId:            req.recurringPaymentActionId,
		RecurringPaymentTypeSpecificPayload: req.recurringPaymentTypeSpecificPayload,
	}); initiateDomainExecutionErr != nil {
		lg.Error("failed to initiate domain execution", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, req.recurringPaymentActionId), zap.String(logger.STAGE, string(currentStage)), zap.String(logger.ACTIVITY, string(rpNs.InitiateExecutionAtDomain)), zap.Error(initiateDomainExecutionErr))
		stageStatus = stagePb.Status_MANUAL_INTERVENTION

		// update the workflow stage with the stage status
		if updateWorkflowStageErr := celestialPkg.UpdateWorkflowStage(ctx, currentStage, stageStatus); updateWorkflowStageErr != nil {
			return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s to status: %s, %w", currentStage, stageStatus, updateWorkflowStageErr)
		}
		return stageStatus, nil
	}

	newActionStatus := rpPb.ActionState_ACTION_IN_PROGRESS
	if initiateDomainExecutionResponse.GetDetailedStatus().IsActionFailed() {
		newActionStatus = rpPb.ActionState_ACTION_FAILURE
	}

	// ================= update recurring payment action to new status ====================
	updateErr := updateRecurringPaymentActionStatusAndPublishStatusUpdateEvent(ctx, &updateActionStatusAndPublishStatusUpdateEventReq{
		recurringPaymentActionId:    req.recurringPaymentActionId,
		expectedCurrentActionStatus: rpPb.ActionState_ACTION_CREATED,
		newActionStatus:             newActionStatus,
		actionDetailedStatusInfo:    initiateDomainExecutionResponse.GetDetailedStatus(),
	})
	if updateErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, updateErr
	}

	if initiateDomainExecutionResponse.GetDetailedStatus().IsActionFailed() {
		stageStatus = stagePb.Status_FAILED
	} else {
		// all activities in the stage have been successfully executed
		stageStatus = stagePb.Status_SUCCESSFUL
	}

	// ===================== updated workflow stage status =======================
	updateWorkflowStageErr := celestialPkg.UpdateWorkflowStage(ctx, currentStage, stageStatus)
	if updateWorkflowStageErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update the workflow stage %s to status: %s %w", currentStage, stageStatus, updateWorkflowStageErr)
	}
	return stageStatus, nil
}

type enquireExecutionStatusAtDomainStageReq struct {
	clientReqId              string
	recurringPaymentActionId string
	recurringPaymentId       string
}

// processEnquireExecutionStatusAtDomainStage enquires the status of the execution triggered at the domain. This is a polling a stage.
//
//	stage status will represent if the execution at domain was successful or failed
//
// nolint:funlen
func processEnquireExecutionStatusAtDomainStage(ctx workflow.Context, req *enquireExecutionStatusAtDomainStageReq) (stagePb.Status, error) {
	var (
		lg          = workflow.GetLogger(ctx)
		stageStatus = stagePb.Status_INITIATED
	)
	// ==================== initiate stage for the worfklow ===================
	if initiateWorkflowStageErr := celestialPkg.InitiateWorkflowStage(ctx, rpNs.EnquireExecutionStatusATDomain, stageStatus); initiateWorkflowStageErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to initiate the workflow %s: %w", rpNs.EnquireExecutionStatusATDomain, initiateWorkflowStageErr)
	}

	enquireExecutionAtDomainStatusRes := &rpActivityPb.EnquireExecutionStatusAtDomainResponse{}
	if enquireExecutionAtDomainStatusErr := activityPkg.Execute(ctx, rpNs.EnquireExecutionStatusAtDomain, enquireExecutionAtDomainStatusRes, &rpActivityPb.EnquireExecutionStatusAtDomainRequest{
		RequestHeader: &celestialActivityPb.RequestHeader{
			ClientReqId: req.clientReqId,
			Ownership:   commontypes.Ownership_EPIFI_TECH,
		},
		RecurringPaymentActionId: req.recurringPaymentActionId,
	}); enquireExecutionAtDomainStatusErr != nil {
		lg.Error("error in enquiring execution status at domain", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, req.recurringPaymentActionId), zap.String(logger.STAGE, string(rpNs.EnquireExecutionStatusATDomain)), zap.String(logger.ACTIVITY, string(rpNs.EnquireExecutionStatusAtDomain)), zap.Error(enquireExecutionAtDomainStatusErr))
		// since we do not know what is the status of exeuction at domain, we have to check it manually
		stageStatus = stagePb.Status_MANUAL_INTERVENTION
		if updateWorkflowStageErr := celestialPkg.UpdateWorkflowStage(ctx, rpNs.EnquireExecutionStatusATDomain, stageStatus); updateWorkflowStageErr != nil {
			return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed ot update the workflow stage : %s, status : %s, err: %w", rpNs.EnquireExecutionStatusATDomain, stageStatus, updateWorkflowStageErr)
		}
		return stageStatus, nil
	}

	// based upon the status we have got from the activity, update the recurring payment entities
	var nextRecurringPaymentActionState rpPb.ActionState
	switch {
	case enquireExecutionAtDomainStatusRes.GetExecutionStatus() == rpActivityPb.EnquireExecutionStatusAtDomainResponse_DOMAIN_EXECUTION_STATUS_SUCCESS:
		nextRecurringPaymentActionState = rpPb.ActionState_ACTION_SUCCESS
	case enquireExecutionAtDomainStatusRes.GetExecutionStatus() == rpActivityPb.EnquireExecutionStatusAtDomainResponse_DOMAIN_EXECUTION_STATUS_FAILURE:
		nextRecurringPaymentActionState = rpPb.ActionState_ACTION_FAILURE
	default:
		lg.Error("unknown execution status recieved from domain", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, req.recurringPaymentActionId), zap.String(logger.STAGE, string(rpNs.EnquireExecutionStatusATDomain)), zap.String(logger.STATUS, string(enquireExecutionAtDomainStatusRes.GetExecutionStatus())))
		// we do not know the status of the domain, mark the stage to the manual intervention
		stageStatus = stagePb.Status_MANUAL_INTERVENTION
		if updateWorkflowStageErr := celestialPkg.UpdateWorkflowStage(ctx, rpNs.EnquireExecutionStatusATDomain, stageStatus); updateWorkflowStageErr != nil {
			return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed ot update the workflow stage : %s, status : %s, err: %w", rpNs.EnquireExecutionStatusATDomain, stageStatus, updateWorkflowStageErr)
		}
		return stageStatus, nil
	}

	// ======================= update recurring payment action state ====================================
	if updateActionAndPublishPacketErr := updateRecurringPaymentActionStatusAndPublishStatusUpdateEvent(ctx, &updateActionStatusAndPublishStatusUpdateEventReq{
		recurringPaymentActionId:    req.recurringPaymentActionId,
		expectedCurrentActionStatus: rpPb.ActionState_ACTION_IN_PROGRESS,
		newActionStatus:             nextRecurringPaymentActionState,
		actionDetailedStatusInfo:    enquireExecutionAtDomainStatusRes.GetActionDetailedStatusInfo(),
	}); updateActionAndPublishPacketErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, updateActionAndPublishPacketErr
	}

	// stage is marked as successful if the execution was successful at domain, else the stage is marked as failed
	// stage status here defines the execution action status
	stageStatus = stagePb.Status_SUCCESSFUL
	// mark the stage as failed if the execution has failed
	if nextRecurringPaymentActionState == rpPb.ActionState_ACTION_FAILURE {
		stageStatus = stagePb.Status_FAILED
	}

	// ======================== UPDATE WORKFLOW STAGE STATUS ===================
	if updateWorkflowStageErr := celestialPkg.UpdateWorkflowStage(ctx, rpNs.EnquireExecutionStatusATDomain, stageStatus); updateWorkflowStageErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed ot update the workflow stage : %s, status : %s, err: %w", rpNs.EnquireExecutionStatusATDomain, stageStatus, updateWorkflowStageErr)
	}
	return stageStatus, nil
}

type executeRecurringPaymentPostProcessingReq struct {
	clientReqId              string
	recurringPaymentId       string
	recurringPaymentActionId string
	stage                    epifitemporal.Stage
}

// processExecuteRecurringPaymentPostProcessing checks if the recurring payment has to be updated to completed state
// and publishes packets to the recurring payment sns topic
func processExecuteRecurringPaymentPostProcessing(ctx workflow.Context, req *executeRecurringPaymentPostProcessingReq) (stagePb.Status, error) {
	var stageStatus = stagePb.Status_INITIATED

	// ========================= initiate stage for workflow ============================
	if initiateWorkflowStageErr := celestialPkg.InitiateWorkflowStage(ctx, req.stage, stageStatus); initiateWorkflowStageErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to initiate the workflow %s: %w", req.stage, initiateWorkflowStageErr)
	}

	// all the activities related to successful execution only
	if req.stage == rpNs.ExecuteRecurringPaymentPostSuccessProcessing {
		// ======================== check and update the recurring payment to completed state ============
		checkAndUpdateRecurringPaymentToCompletedStateRes := &rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateResponse{}
		if checkAndUpdateRecurringPaymentToCompletedStateErr := activityPkg.Execute(ctx, rpNs.CheckAndUpdateRecurringPaymentToCompletedState, checkAndUpdateRecurringPaymentToCompletedStateRes, &rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateRequest{
			RequestHeader: &celestialActivityPb.RequestHeader{
				ClientReqId: req.clientReqId,
				Ownership:   commontypes.Ownership_EPIFI_TECH,
			},
			RecurringPaymentId: req.recurringPaymentId,
		}); checkAndUpdateRecurringPaymentToCompletedStateErr != nil {
			return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to check and update recurring payment to completed state, err: %w", checkAndUpdateRecurringPaymentToCompletedStateErr)
		}
	}

	// all the activities related to failure exeuction only
	if req.stage == rpNs.ExecuteRecurringPaymentPostFailureProcessing {
	}

	// ======================== send notification for the execution ================
	sendNotificationRes := &rpActivityPb.SendStatusNotificationResponse{}
	// todo(Vishal): update the activity to handle the notification for execution also
	if sendNotificationErr := activityPkg.Execute(ctx, rpNs.SendStatusNotification, sendNotificationRes, &rpActivityPb.SendStatusNotificationRequest{
		RequestHeader: &celestialActivityPb.RequestHeader{
			ClientReqId: req.clientReqId,
			Ownership:   commontypes.Ownership_EPIFI_TECH,
		},
		RecurringPaymentId:       req.recurringPaymentId,
		RecurringPaymentActionId: req.recurringPaymentActionId,
	}); sendNotificationErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to send successful execution notification,err: %w", sendNotificationErr)
	}

	// all activities in the stage are successful
	stageStatus = stagePb.Status_SUCCESSFUL

	if updateWorkflowStageErr := celestialPkg.UpdateWorkflowStage(ctx, req.stage, stageStatus); updateWorkflowStageErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed ot update the workflow stage : %s, status : %s, err: %w", req.stage, stageStatus, updateWorkflowStageErr)
	}
	return stageStatus, nil
}
