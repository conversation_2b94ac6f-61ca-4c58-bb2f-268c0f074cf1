package workflow_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	celestialPb "github.com/epifi/be-common/api/celestial"
	activityPb "github.com/epifi/be-common/api/celestial/activity"
	notificationPb "github.com/epifi/gamma/api/celestial/activity/notification"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	commPb "github.com/epifi/gamma/api/comms"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/money"
	rpActivity "github.com/epifi/gamma/recurringpayment/activity"

	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	celestialActivity "github.com/epifi/gamma/celestial/activity"
	orderActivity "github.com/epifi/gamma/order/activity"
	rpWorkflow "github.com/epifi/gamma/recurringpayment/workflow"
)

func TestCreateRecurringPayment(t *testing.T) {
	const defaultWorkflowID = "default-test-workflow-id"
	type mockGetWorkflowProcessingParams struct {
		enable bool
		res    *celestialPb.WorkflowProcessingParams
		err    error
	}
	type mockInitiateWorkflowStage struct {
		enable bool
		stage  workflowPb.Stage
		status stagePb.Status
		err    error
	}
	type mockProcessRecurringPaymentCreation struct {
		enable bool
		req    *activityPb.Request
		res    *activityPb.Response
		err    error
	}
	type mockSignalWorkflow struct {
		enable     bool
		delay      time.Duration
		signal     []byte
		signalName epifitemporal.Signal
	}
	type mockUpdateRecurringPaymentStateAndAction struct {
		enable bool
		req    *rpActivityPb.UpdateRecurringPaymentStateAndActionRequest
		res    *rpActivityPb.UpdateRecurringPaymentStateAndActionResponse
		err    error
	}
	type mockUpdateWorkflowStageStatus struct {
		enable bool
		stage  workflowPb.Stage
		status stagePb.Status
		err    error
	}
	type mockGetNotificationTemplate struct {
		enable bool
		req    *notificationPb.GetTemplatesRequest
		res    *notificationPb.GetTemplatesResponse
		count  int
		err    error
	}
	type mockSendNotification struct {
		enable bool
		req    *notificationPb.SendNotificationRequest
		res    *notificationPb.SendNotificationResponse
		count  int
		err    error
	}
	type mockPublishWorkflowUpdateEvent struct {
		enable bool
		err    error
	}
	tests := []struct {
		name                                     string
		req                                      *workflowPb.Request
		mockGetWorkflowProcessingParams          mockGetWorkflowProcessingParams
		mockInitiateWorkflowStage                mockInitiateWorkflowStage
		mockProcessRecurringPaymentCreation      mockProcessRecurringPaymentCreation
		mockSignalWorkflow                       mockSignalWorkflow
		mockUpdateWorkflowStageStatuses          []mockUpdateWorkflowStageStatus
		mockGetNotificationTemplate              mockGetNotificationTemplate
		mockSendNotification                     mockSendNotification
		mockPublishWorkflowUpdateEvent           mockPublishWorkflowUpdateEvent
		mockUpdateRecurringPaymentStateAndAction mockUpdateRecurringPaymentStateAndAction
		wantErr                                  bool
	}{
		{
			name: "successfully created recurring payment",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_CREATION,
				status: stagePb.Status_INITIATED,
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable:     true,
				delay:      10 * time.Nanosecond,
				signal:     nil,
				signalName: rpNs.CreateRecurringPaymentAuthSignal,
			},
			mockUpdateWorkflowStageStatuses: []mockUpdateWorkflowStageStatus{
				{
					enable: true,
					stage:  workflowPb.Stage_CREATION,
					status: stagePb.Status_BLOCKED,
				},
				{
					enable: true,
					stage:  workflowPb.Stage_CREATION,
					status: stagePb.Status_INITIATED,
				},
				{
					enable: true,
					stage:  workflowPb.Stage_CREATION,
					status: stagePb.Status_SUCCESSFUL,
				},
			},
			mockProcessRecurringPaymentCreation: mockProcessRecurringPaymentCreation{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
				res: &activityPb.Response{},
			},
			mockUpdateRecurringPaymentStateAndAction: mockUpdateRecurringPaymentStateAndAction{
				enable: true,
				req: &rpActivityPb.UpdateRecurringPaymentStateAndActionRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id",
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					StateToUpdate:       rpPb.RecurringPaymentState_ACTIVATED,
					ActionStateToUpdate: rpPb.ActionState_ACTION_SUCCESS,
				},
				res: &rpActivityPb.UpdateRecurringPaymentStateAndActionResponse{},
				err: nil,
			},
			mockGetNotificationTemplate: mockGetNotificationTemplate{
				enable: true,
				count:  2,
				req: &notificationPb.GetTemplatesRequest{
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
				res: &notificationPb.GetTemplatesResponse{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_PhoneNumber{PhoneNumber: "0123456789"},
						CommunicationList: []*commPb.Communication{
							{
								Medium: commPb.Medium_SMS,
								Message: &commPb.Communication_Sms{
									Sms: &commPb.SMSMessage{
										SmsOption: &commPb.SmsOption{
											Option: &commPb.SmsOption_OnboardingOtpSmsOption{
												OnboardingOtpSmsOption: &commPb.OnboardingOtpSmsOption{
													SmsType: 0,
													Option: &commPb.OnboardingOtpSmsOption_OnboardingOtpSmsOptionV1{
														OnboardingOtpSmsOptionV1: &commPb.OnboardingOtpSmsOptionV1{
															Otp:                    "",
															TemplateVersion:        0,
															AndroidClientSignature: "",
														},
													},
												},
											},
										},
									},
								},
							},
						},
						QualityOfService: commPb.QoS_BEST_EFFORT,
						ClientId:         "clientId",
					},
				},
				err: nil,
			},
			mockSendNotification: mockSendNotification{
				enable: true,
				count:  2,
				req: &notificationPb.SendNotificationRequest{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_PhoneNumber{PhoneNumber: "0123456789"},
						CommunicationList: []*commPb.Communication{
							{
								Medium: commPb.Medium_SMS,
								Message: &commPb.Communication_Sms{
									Sms: &commPb.SMSMessage{
										SmsOption: &commPb.SmsOption{
											Option: &commPb.SmsOption_OnboardingOtpSmsOption{
												OnboardingOtpSmsOption: &commPb.OnboardingOtpSmsOption{
													SmsType: 0,
													Option: &commPb.OnboardingOtpSmsOption_OnboardingOtpSmsOptionV1{
														OnboardingOtpSmsOptionV1: &commPb.OnboardingOtpSmsOptionV1{
															Otp:                    "",
															TemplateVersion:        0,
															AndroidClientSignature: "",
														},
													},
												},
											},
										},
									},
								},
							},
						},
						QualityOfService: commPb.QoS_BEST_EFFORT,
						ClientId:         "clientId",
					},
				},
				res: &notificationPb.SendNotificationResponse{
					MessageIdList: []string{
						"abc",
					},
				},
				err: nil,
			},
			mockPublishWorkflowUpdateEvent: mockPublishWorkflowUpdateEvent{
				enable: true,
			},
			wantErr: false,
		},
		{
			name: "recurring payment creation failed because ProcessRecurringPaymentCreation returned failure",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_CREATION,
				status: stagePb.Status_INITIATED,
			},
			mockProcessRecurringPaymentCreation: mockProcessRecurringPaymentCreation{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
				err: epifitemporal.NewPermanentError(errors.New("test error")),
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable:     true,
				delay:      10 * time.Nanosecond,
				signal:     nil,
				signalName: rpNs.CreateRecurringPaymentAuthSignal,
			},
			mockUpdateWorkflowStageStatuses: []mockUpdateWorkflowStageStatus{
				{
					enable: true,
					stage:  workflowPb.Stage_CREATION,
					status: stagePb.Status_BLOCKED,
				}, {
					enable: true,
					stage:  workflowPb.Stage_CREATION,
					status: stagePb.Status_INITIATED,
				},
				{
					enable: true,
					stage:  workflowPb.Stage_CREATION,
					status: stagePb.Status_FAILED,
				},
			},
			mockUpdateRecurringPaymentStateAndAction: mockUpdateRecurringPaymentStateAndAction{
				enable: true,
				req: &rpActivityPb.UpdateRecurringPaymentStateAndActionRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id",
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					StateToUpdate:       rpPb.RecurringPaymentState_FAILED,
					ActionStateToUpdate: rpPb.ActionState_ACTION_FAILURE,
				},
				res: &rpActivityPb.UpdateRecurringPaymentStateAndActionResponse{},
				err: nil,
			},
			mockGetNotificationTemplate: mockGetNotificationTemplate{
				enable: true,
				count:  2,
				req: &notificationPb.GetTemplatesRequest{
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
				res: &notificationPb.GetTemplatesResponse{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_PhoneNumber{PhoneNumber: "0123456789"},
						CommunicationList: []*commPb.Communication{
							{
								Medium: commPb.Medium_SMS,
								Message: &commPb.Communication_Sms{
									Sms: &commPb.SMSMessage{
										SmsOption: &commPb.SmsOption{
											Option: &commPb.SmsOption_MandateDeclinedSmsOption{
												MandateDeclinedSmsOption: &commPb.MandateDeclinedSmsOption{
													SmsType: commPb.SmsType_MANDATE_DECLINED,
													Option: &commPb.MandateDeclinedSmsOption_MandateDeclinedV1{
														MandateDeclinedV1: &commPb.MandateDeclinedSmsOptionV1{
															MandateExecutionDate: time.Now().Format("12-02-2020"),
															TemplateVersion:      commPb.TemplateVersion_VERSION_V1,
															MandateAmount:        money.AmountINR(1000).GetPb(),
															PayeeName: &commontypes.Name{
																FirstName: "Yatin",
																LastName:  "Kwatra",
																Honorific: "Mr",
															},
															MandateFrequency: "daily",
														},
													},
												},
											},
										},
									},
								},
							},
						},
						QualityOfService: commPb.QoS_BEST_EFFORT,
						ClientId:         "clientId",
					},
				},
				err: nil,
			},
			mockSendNotification: mockSendNotification{
				enable: true,
				count:  2,
				req: &notificationPb.SendNotificationRequest{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_PhoneNumber{PhoneNumber: "0123456789"},
						CommunicationList: []*commPb.Communication{
							{
								Medium: commPb.Medium_SMS,
								Message: &commPb.Communication_Sms{
									Sms: &commPb.SMSMessage{
										SmsOption: &commPb.SmsOption{
											Option: &commPb.SmsOption_MandateDeclinedSmsOption{
												MandateDeclinedSmsOption: &commPb.MandateDeclinedSmsOption{
													SmsType: commPb.SmsType_MANDATE_DECLINED,
													Option: &commPb.MandateDeclinedSmsOption_MandateDeclinedV1{
														MandateDeclinedV1: &commPb.MandateDeclinedSmsOptionV1{
															MandateExecutionDate: time.Now().Format("12-02-2020"),
															TemplateVersion:      commPb.TemplateVersion_VERSION_V1,
															MandateAmount:        money.AmountINR(1000).GetPb(),
															PayeeName: &commontypes.Name{
																FirstName: "Yatin",
																LastName:  "Kwatra",
																Honorific: "Mr",
															},
															MandateFrequency: "daily",
														},
													},
												},
											},
										},
									},
								},
							},
						},
						QualityOfService: commPb.QoS_BEST_EFFORT,
						ClientId:         "clientId",
					},
				},
				res: &notificationPb.SendNotificationResponse{
					MessageIdList: []string{
						"abc",
					},
				},
				err: nil,
			},
			mockPublishWorkflowUpdateEvent: mockPublishWorkflowUpdateEvent{
				enable: true,
			},
			wantErr: false,
		},
		{
			name: "recurring payment got stuck in manual intervention as ProcessRecurringPaymentCreation returned retryable error",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_CREATION,
				status: stagePb.Status_INITIATED,
			},
			mockProcessRecurringPaymentCreation: mockProcessRecurringPaymentCreation{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
				err: epifitemporal.NewTransientError(errors.New("test error")),
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable:     true,
				delay:      10 * time.Nanosecond,
				signal:     nil,
				signalName: rpNs.CreateRecurringPaymentAuthSignal,
			},
			mockUpdateWorkflowStageStatuses: []mockUpdateWorkflowStageStatus{
				{
					enable: true,
					stage:  workflowPb.Stage_CREATION,
					status: stagePb.Status_BLOCKED,
				},
				{
					enable: true,
					stage:  workflowPb.Stage_CREATION,
					status: stagePb.Status_INITIATED,
				},
				{
					enable: true,
					stage:  workflowPb.Stage_CREATION,
					status: stagePb.Status_MANUAL_INTERVENTION,
				},
			},
			mockUpdateRecurringPaymentStateAndAction: mockUpdateRecurringPaymentStateAndAction{
				enable: true,
				req: &rpActivityPb.UpdateRecurringPaymentStateAndActionRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id",
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					StateToUpdate:       rpPb.RecurringPaymentState_MANUAL_INTERVENTION,
					ActionStateToUpdate: rpPb.ActionState_ACTION_MANUAL_INTERVENTION,
				},
				res: &rpActivityPb.UpdateRecurringPaymentStateAndActionResponse{},
				err: nil,
			},
			mockGetNotificationTemplate: mockGetNotificationTemplate{
				enable: true,
				count:  2,
				req: &notificationPb.GetTemplatesRequest{
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
				res: &notificationPb.GetTemplatesResponse{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_PhoneNumber{PhoneNumber: "0123456789"},
						CommunicationList: []*commPb.Communication{
							{
								Medium: commPb.Medium_SMS,
								Message: &commPb.Communication_Sms{
									Sms: &commPb.SMSMessage{
										SmsOption: &commPb.SmsOption{
											Option: &commPb.SmsOption_MandateDeclinedSmsOption{
												MandateDeclinedSmsOption: &commPb.MandateDeclinedSmsOption{
													SmsType: commPb.SmsType_MANDATE_DECLINED,
													Option: &commPb.MandateDeclinedSmsOption_MandateDeclinedV1{
														MandateDeclinedV1: &commPb.MandateDeclinedSmsOptionV1{
															MandateExecutionDate: time.Now().Format("12-02-2020"),
															TemplateVersion:      commPb.TemplateVersion_VERSION_V1,
															MandateAmount:        money.AmountINR(1000).GetPb(),
															PayeeName: &commontypes.Name{
																FirstName: "Yatin",
																LastName:  "Kwatra",
																Honorific: "Mr",
															},
															MandateFrequency: "daily",
														},
													},
												},
											},
										},
									},
								},
							},
						},
						QualityOfService: commPb.QoS_BEST_EFFORT,
						ClientId:         "clientId",
					},
				},
				err: nil,
			},
			mockSendNotification: mockSendNotification{
				enable: true,
				count:  2,
				req: &notificationPb.SendNotificationRequest{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_PhoneNumber{PhoneNumber: "0123456789"},
						CommunicationList: []*commPb.Communication{
							{
								Medium: commPb.Medium_SMS,
								Message: &commPb.Communication_Sms{
									Sms: &commPb.SMSMessage{
										SmsOption: &commPb.SmsOption{
											Option: &commPb.SmsOption_MandateDeclinedSmsOption{
												MandateDeclinedSmsOption: &commPb.MandateDeclinedSmsOption{
													SmsType: commPb.SmsType_MANDATE_DECLINED,
													Option: &commPb.MandateDeclinedSmsOption_MandateDeclinedV1{
														MandateDeclinedV1: &commPb.MandateDeclinedSmsOptionV1{
															MandateExecutionDate: time.Now().Format("12-02-2020"),
															TemplateVersion:      commPb.TemplateVersion_VERSION_V1,
															MandateAmount:        money.AmountINR(1000).GetPb(),
															PayeeName: &commontypes.Name{
																FirstName: "Yatin",
																LastName:  "Kwatra",
																Honorific: "Mr",
															},
															MandateFrequency: "daily",
														},
													},
												},
											},
										},
									},
								},
							},
						},
						QualityOfService: commPb.QoS_BEST_EFFORT,
						ClientId:         "clientId",
					},
				},
				res: &notificationPb.SendNotificationResponse{
					MessageIdList: []string{
						"abc",
					},
				},
				err: nil,
			},
			mockPublishWorkflowUpdateEvent: mockPublishWorkflowUpdateEvent{
				enable: true,
			},
			wantErr: false,
		},
		{
			name: "should return error in case GetWorkflowProcessingParams failed",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				err:    epifitemporal.NewPermanentError(errors.New("test error")),
			},
			wantErr: true,
		},
		{
			name: "should return error in case InitiateWorkflowStage failed",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_CREATION,
				status: stagePb.Status_INITIATED,
				err:    epifitemporal.NewPermanentError(errors.New("test error")),
			},
			wantErr: true,
		},
		{
			name: "should return error in case first UpdateWorkflowStageStatus failed",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_CREATION,
				status: stagePb.Status_INITIATED,
			},
			mockGetNotificationTemplate: mockGetNotificationTemplate{
				enable: true,
				count:  1,
				req: &notificationPb.GetTemplatesRequest{
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
				res: &notificationPb.GetTemplatesResponse{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_PhoneNumber{PhoneNumber: "0123456789"},
						CommunicationList: []*commPb.Communication{
							{
								Medium: commPb.Medium_SMS,
								Message: &commPb.Communication_Sms{
									Sms: &commPb.SMSMessage{
										SmsOption: &commPb.SmsOption{
											Option: &commPb.SmsOption_OnboardingOtpSmsOption{
												OnboardingOtpSmsOption: &commPb.OnboardingOtpSmsOption{
													SmsType: 0,
													Option: &commPb.OnboardingOtpSmsOption_OnboardingOtpSmsOptionV1{
														OnboardingOtpSmsOptionV1: &commPb.OnboardingOtpSmsOptionV1{
															Otp:                    "",
															TemplateVersion:        0,
															AndroidClientSignature: "",
														},
													},
												},
											},
										},
									},
								},
							},
						},
						QualityOfService: commPb.QoS_BEST_EFFORT,
						ClientId:         "clientId",
					},
				},
				err: nil,
			},
			mockSendNotification: mockSendNotification{
				enable: true,
				count:  1,
				req: &notificationPb.SendNotificationRequest{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_PhoneNumber{PhoneNumber: "0123456789"},
						CommunicationList: []*commPb.Communication{
							{
								Medium: commPb.Medium_SMS,
								Message: &commPb.Communication_Sms{
									Sms: &commPb.SMSMessage{
										SmsOption: &commPb.SmsOption{
											Option: &commPb.SmsOption_OnboardingOtpSmsOption{
												OnboardingOtpSmsOption: &commPb.OnboardingOtpSmsOption{
													SmsType: 0,
													Option: &commPb.OnboardingOtpSmsOption_OnboardingOtpSmsOptionV1{
														OnboardingOtpSmsOptionV1: &commPb.OnboardingOtpSmsOptionV1{
															Otp:                    "",
															TemplateVersion:        0,
															AndroidClientSignature: "",
														},
													},
												},
											},
										},
									},
								},
							},
						},
						QualityOfService: commPb.QoS_BEST_EFFORT,
						ClientId:         "clientId",
					},
				},
				res: &notificationPb.SendNotificationResponse{
					MessageIdList: []string{
						"abc",
					},
				},
				err: nil,
			},
			mockUpdateWorkflowStageStatuses: []mockUpdateWorkflowStageStatus{
				{
					enable: true,
					stage:  workflowPb.Stage_CREATION,
					status: stagePb.Status_BLOCKED,
					err:    epifitemporal.NewPermanentError(errors.New("test error")),
				},
			},
			wantErr: true,
		},
		{
			name: "should return error in case second UpdateWorkflowStageStatus failed",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_CREATION,
				status: stagePb.Status_INITIATED,
			},
			mockGetNotificationTemplate: mockGetNotificationTemplate{
				enable: true,
				count:  1,
				req: &notificationPb.GetTemplatesRequest{
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
				res: &notificationPb.GetTemplatesResponse{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_PhoneNumber{PhoneNumber: "0123456789"},
						CommunicationList: []*commPb.Communication{
							{
								Medium: commPb.Medium_SMS,
								Message: &commPb.Communication_Sms{
									Sms: &commPb.SMSMessage{
										SmsOption: &commPb.SmsOption{
											Option: &commPb.SmsOption_OnboardingOtpSmsOption{
												OnboardingOtpSmsOption: &commPb.OnboardingOtpSmsOption{
													SmsType: 0,
													Option: &commPb.OnboardingOtpSmsOption_OnboardingOtpSmsOptionV1{
														OnboardingOtpSmsOptionV1: &commPb.OnboardingOtpSmsOptionV1{
															Otp:                    "",
															TemplateVersion:        0,
															AndroidClientSignature: "",
														},
													},
												},
											},
										},
									},
								},
							},
						},
						QualityOfService: commPb.QoS_BEST_EFFORT,
						ClientId:         "clientId",
					},
				},
				err: nil,
			},
			mockSendNotification: mockSendNotification{
				enable: true,
				count:  1,
				req: &notificationPb.SendNotificationRequest{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_PhoneNumber{PhoneNumber: "0123456789"},
						CommunicationList: []*commPb.Communication{
							{
								Medium: commPb.Medium_SMS,
								Message: &commPb.Communication_Sms{
									Sms: &commPb.SMSMessage{
										SmsOption: &commPb.SmsOption{
											Option: &commPb.SmsOption_OnboardingOtpSmsOption{
												OnboardingOtpSmsOption: &commPb.OnboardingOtpSmsOption{
													SmsType: 0,
													Option: &commPb.OnboardingOtpSmsOption_OnboardingOtpSmsOptionV1{
														OnboardingOtpSmsOptionV1: &commPb.OnboardingOtpSmsOptionV1{
															Otp:                    "",
															TemplateVersion:        0,
															AndroidClientSignature: "",
														},
													},
												},
											},
										},
									},
								},
							},
						},
						QualityOfService: commPb.QoS_BEST_EFFORT,
						ClientId:         "clientId",
					},
				},
				res: &notificationPb.SendNotificationResponse{
					MessageIdList: []string{
						"abc",
					},
				},
				err: nil,
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable:     true,
				delay:      10 * time.Nanosecond,
				signal:     nil,
				signalName: rpNs.CreateRecurringPaymentAuthSignal,
			},
			mockUpdateWorkflowStageStatuses: []mockUpdateWorkflowStageStatus{
				{
					enable: true,
					stage:  workflowPb.Stage_CREATION,
					status: stagePb.Status_BLOCKED,
				},
				{
					enable: true,
					stage:  workflowPb.Stage_CREATION,
					status: stagePb.Status_INITIATED,
					err:    epifitemporal.NewPermanentError(errors.New("test error")),
				},
			},
			wantErr: true,
		},
		{
			name: "should return error in case third UpdateWorkflowStageStatus failed",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_CREATION,
				status: stagePb.Status_INITIATED,
			},
			mockProcessRecurringPaymentCreation: mockProcessRecurringPaymentCreation{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
				res: &activityPb.Response{},
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable:     true,
				delay:      10 * time.Nanosecond,
				signal:     nil,
				signalName: rpNs.CreateRecurringPaymentAuthSignal,
			},
			mockGetNotificationTemplate: mockGetNotificationTemplate{
				enable: true,
				count:  1,
				req: &notificationPb.GetTemplatesRequest{
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
				res: &notificationPb.GetTemplatesResponse{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_PhoneNumber{PhoneNumber: "0123456789"},
						CommunicationList: []*commPb.Communication{
							{
								Medium: commPb.Medium_SMS,
								Message: &commPb.Communication_Sms{
									Sms: &commPb.SMSMessage{
										SmsOption: &commPb.SmsOption{
											Option: &commPb.SmsOption_OnboardingOtpSmsOption{
												OnboardingOtpSmsOption: &commPb.OnboardingOtpSmsOption{
													SmsType: 0,
													Option: &commPb.OnboardingOtpSmsOption_OnboardingOtpSmsOptionV1{
														OnboardingOtpSmsOptionV1: &commPb.OnboardingOtpSmsOptionV1{
															Otp:                    "",
															TemplateVersion:        0,
															AndroidClientSignature: "",
														},
													},
												},
											},
										},
									},
								},
							},
						},
						QualityOfService: commPb.QoS_BEST_EFFORT,
						ClientId:         "clientId",
					},
				},
				err: nil,
			},
			mockSendNotification: mockSendNotification{
				enable: true,
				count:  1,
				req: &notificationPb.SendNotificationRequest{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_PhoneNumber{PhoneNumber: "0123456789"},
						CommunicationList: []*commPb.Communication{
							{
								Medium: commPb.Medium_SMS,
								Message: &commPb.Communication_Sms{
									Sms: &commPb.SMSMessage{
										SmsOption: &commPb.SmsOption{
											Option: &commPb.SmsOption_OnboardingOtpSmsOption{
												OnboardingOtpSmsOption: &commPb.OnboardingOtpSmsOption{
													SmsType: 0,
													Option: &commPb.OnboardingOtpSmsOption_OnboardingOtpSmsOptionV1{
														OnboardingOtpSmsOptionV1: &commPb.OnboardingOtpSmsOptionV1{
															Otp:                    "",
															TemplateVersion:        0,
															AndroidClientSignature: "",
														},
													},
												},
											},
										},
									},
								},
							},
						},
						QualityOfService: commPb.QoS_BEST_EFFORT,
						ClientId:         "clientId",
					},
				},
				res: &notificationPb.SendNotificationResponse{
					MessageIdList: []string{
						"abc",
					},
				},
				err: nil,
			},
			mockUpdateWorkflowStageStatuses: []mockUpdateWorkflowStageStatus{
				{
					enable: true,
					stage:  workflowPb.Stage_CREATION,
					status: stagePb.Status_BLOCKED,
				},
				{
					enable: true,
					stage:  workflowPb.Stage_CREATION,
					status: stagePb.Status_INITIATED,
				},
				{
					enable: true,
					stage:  workflowPb.Stage_CREATION,
					status: stagePb.Status_SUCCESSFUL,
					err:    epifitemporal.NewPermanentError(errors.New("test error")),
				},
			},
			wantErr: true,
		},
		{
			name: "should return error in case get notification template failed",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_CREATION,
				status: stagePb.Status_INITIATED,
			},
			mockGetNotificationTemplate: mockGetNotificationTemplate{
				enable: true,
				count:  1,
				req: &notificationPb.GetTemplatesRequest{
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
				err: epifitemporal.NewPermanentError(errors.New("test error")),
			},
			wantErr: true,
		},
		{
			name: "should return error in case send notification via comms failed",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_CREATION,
				status: stagePb.Status_INITIATED,
			},
			mockGetNotificationTemplate: mockGetNotificationTemplate{
				enable: true,
				count:  1,
				req: &notificationPb.GetTemplatesRequest{
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
				res: &notificationPb.GetTemplatesResponse{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_PhoneNumber{PhoneNumber: "0123456789"},
						CommunicationList: []*commPb.Communication{
							{
								Medium: commPb.Medium_SMS,
								Message: &commPb.Communication_Sms{
									Sms: &commPb.SMSMessage{
										SmsOption: &commPb.SmsOption{
											Option: &commPb.SmsOption_MandateDeclinedSmsOption{
												MandateDeclinedSmsOption: &commPb.MandateDeclinedSmsOption{
													SmsType: commPb.SmsType_MANDATE_DECLINED,
													Option: &commPb.MandateDeclinedSmsOption_MandateDeclinedV1{
														MandateDeclinedV1: &commPb.MandateDeclinedSmsOptionV1{
															MandateExecutionDate: time.Now().Format("12-02-2020"),
															TemplateVersion:      commPb.TemplateVersion_VERSION_V1,
															MandateAmount:        money.AmountINR(1000).GetPb(),
															PayeeName: &commontypes.Name{
																FirstName: "Yatin",
																LastName:  "Kwatra",
																Honorific: "Mr",
															},
															MandateFrequency: "daily",
														},
													},
												},
											},
										},
									},
								},
							},
						},
						QualityOfService: commPb.QoS_BEST_EFFORT,
						ClientId:         "client-req-id",
					},
				},
				err: nil,
			},
			mockSendNotification: mockSendNotification{
				enable: true,
				count:  1,
				req: &notificationPb.SendNotificationRequest{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_PhoneNumber{PhoneNumber: "0123456789"},
						CommunicationList: []*commPb.Communication{
							{
								Medium: commPb.Medium_SMS,
								Message: &commPb.Communication_Sms{
									Sms: &commPb.SMSMessage{
										SmsOption: &commPb.SmsOption{
											Option: &commPb.SmsOption_MandateDeclinedSmsOption{
												MandateDeclinedSmsOption: &commPb.MandateDeclinedSmsOption{
													SmsType: commPb.SmsType_MANDATE_DECLINED,
													Option: &commPb.MandateDeclinedSmsOption_MandateDeclinedV1{
														MandateDeclinedV1: &commPb.MandateDeclinedSmsOptionV1{
															MandateExecutionDate: time.Now().Format("12-02-2020"),
															TemplateVersion:      commPb.TemplateVersion_VERSION_V1,
															MandateAmount:        money.AmountINR(1000).GetPb(),
															PayeeName: &commontypes.Name{
																FirstName: "Yatin",
																LastName:  "Kwatra",
																Honorific: "Mr",
															},
															MandateFrequency: "daily",
														},
													},
												},
											},
										},
									},
								},
							},
						},
						QualityOfService: commPb.QoS_BEST_EFFORT,
						ClientId:         "client-req-id",
					},
				},
				res: nil,
				err: epifitemporal.NewPermanentError(errors.New("test error")),
			},
			wantErr: true,
		},
		{
			name: "should return error in case publish workflow update failed",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_CREATION,
				status: stagePb.Status_INITIATED,
			},
			mockProcessRecurringPaymentCreation: mockProcessRecurringPaymentCreation{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
				res: &activityPb.Response{},
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable:     true,
				delay:      10 * time.Nanosecond,
				signal:     nil,
				signalName: rpNs.CreateRecurringPaymentAuthSignal,
			},
			mockUpdateWorkflowStageStatuses: []mockUpdateWorkflowStageStatus{
				{
					enable: true,
					stage:  workflowPb.Stage_CREATION,
					status: stagePb.Status_BLOCKED,
				},
				{
					enable: true,
					stage:  workflowPb.Stage_CREATION,
					status: stagePb.Status_INITIATED,
				},
				{
					enable: true,
					stage:  workflowPb.Stage_CREATION,
					status: stagePb.Status_SUCCESSFUL,
				},
			},
			mockUpdateRecurringPaymentStateAndAction: mockUpdateRecurringPaymentStateAndAction{
				enable: true,
				req: &rpActivityPb.UpdateRecurringPaymentStateAndActionRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id",
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					StateToUpdate:       rpPb.RecurringPaymentState_ACTIVATED,
					ActionStateToUpdate: rpPb.ActionState_ACTION_SUCCESS,
				},
				res: &rpActivityPb.UpdateRecurringPaymentStateAndActionResponse{},
				err: nil,
			},
			mockGetNotificationTemplate: mockGetNotificationTemplate{
				enable: true,
				count:  2,
				req: &notificationPb.GetTemplatesRequest{
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
				res: &notificationPb.GetTemplatesResponse{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_PhoneNumber{PhoneNumber: "0123456789"},
						CommunicationList: []*commPb.Communication{
							{
								Medium: commPb.Medium_SMS,
								Message: &commPb.Communication_Sms{
									Sms: &commPb.SMSMessage{
										SmsOption: &commPb.SmsOption{
											Option: &commPb.SmsOption_MandateDeclinedSmsOption{
												MandateDeclinedSmsOption: &commPb.MandateDeclinedSmsOption{
													SmsType: commPb.SmsType_MANDATE_DECLINED,
													Option: &commPb.MandateDeclinedSmsOption_MandateDeclinedV1{
														MandateDeclinedV1: &commPb.MandateDeclinedSmsOptionV1{
															MandateExecutionDate: time.Now().Format("12-02-2020"),
															TemplateVersion:      commPb.TemplateVersion_VERSION_V1,
															MandateAmount:        money.AmountINR(1000).GetPb(),
															PayeeName: &commontypes.Name{
																FirstName: "Yatin",
																LastName:  "Kwatra",
																Honorific: "Mr",
															},
															MandateFrequency: "daily",
														},
													},
												},
											},
										},
									},
								},
							},
						},
						QualityOfService: commPb.QoS_BEST_EFFORT,
						ClientId:         "client-req-id",
					},
				},
				err: nil,
			},
			mockSendNotification: mockSendNotification{
				enable: true,
				count:  2,
				req: &notificationPb.SendNotificationRequest{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_PhoneNumber{PhoneNumber: "0123456789"},
						CommunicationList: []*commPb.Communication{
							{
								Medium: commPb.Medium_SMS,
								Message: &commPb.Communication_Sms{
									Sms: &commPb.SMSMessage{
										SmsOption: &commPb.SmsOption{
											Option: &commPb.SmsOption_MandateDeclinedSmsOption{
												MandateDeclinedSmsOption: &commPb.MandateDeclinedSmsOption{
													SmsType: commPb.SmsType_MANDATE_DECLINED,
													Option: &commPb.MandateDeclinedSmsOption_MandateDeclinedV1{
														MandateDeclinedV1: &commPb.MandateDeclinedSmsOptionV1{
															MandateExecutionDate: time.Now().Format("12-02-2020"),
															TemplateVersion:      commPb.TemplateVersion_VERSION_V1,
															MandateAmount:        money.AmountINR(1000).GetPb(),
															PayeeName: &commontypes.Name{
																FirstName: "Yatin",
																LastName:  "Kwatra",
																Honorific: "Mr",
															},
															MandateFrequency: "daily",
														},
													},
												},
											},
										},
									},
								},
							},
						},
						QualityOfService: commPb.QoS_BEST_EFFORT,
						ClientId:         "client-req-id",
					},
				},
				res: &notificationPb.SendNotificationResponse{
					MessageIdList: []string{
						"abc",
					},
				},
				err: nil,
			},
			mockPublishWorkflowUpdateEvent: mockPublishWorkflowUpdateEvent{
				enable: true,
				err:    epifitemporal.NewPermanentError(errors.New("test error")),
			},
			wantErr: true,
		},
		{
			name: "failed to create recurring payment, because no signal was received",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_CREATION,
				status: stagePb.Status_INITIATED,
			},
			mockUpdateWorkflowStageStatuses: []mockUpdateWorkflowStageStatus{
				{
					enable: true,
					stage:  workflowPb.Stage_CREATION,
					status: stagePb.Status_BLOCKED,
				},
				{
					enable: true,
					stage:  workflowPb.Stage_CREATION,
					status: stagePb.Status_FAILED,
				},
			},
			mockGetNotificationTemplate: mockGetNotificationTemplate{
				enable: true,
				count:  1,
				req: &notificationPb.GetTemplatesRequest{
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
				res: &notificationPb.GetTemplatesResponse{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_PhoneNumber{PhoneNumber: "0123456789"},
						CommunicationList: []*commPb.Communication{
							{
								Medium: commPb.Medium_SMS,
								Message: &commPb.Communication_Sms{
									Sms: &commPb.SMSMessage{
										SmsOption: &commPb.SmsOption{
											Option: &commPb.SmsOption_MandateDeclinedSmsOption{
												MandateDeclinedSmsOption: &commPb.MandateDeclinedSmsOption{
													SmsType: commPb.SmsType_MANDATE_DECLINED,
													Option: &commPb.MandateDeclinedSmsOption_MandateDeclinedV1{
														MandateDeclinedV1: &commPb.MandateDeclinedSmsOptionV1{
															MandateExecutionDate: time.Now().Format("12-02-2020"),
															TemplateVersion:      commPb.TemplateVersion_VERSION_V1,
															MandateAmount:        money.AmountINR(1000).GetPb(),
															PayeeName: &commontypes.Name{
																FirstName: "Yatin",
																LastName:  "Kwatra",
																Honorific: "Mr",
															},
															MandateFrequency: "daily",
														},
													},
												},
											},
										},
									},
								},
							},
						},
						QualityOfService: commPb.QoS_BEST_EFFORT,
						ClientId:         "client-req-id",
					},
				},
				err: nil,
			},
			mockSendNotification: mockSendNotification{
				enable: true,
				count:  1,
				req: &notificationPb.SendNotificationRequest{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_PhoneNumber{PhoneNumber: "0123456789"},
						CommunicationList: []*commPb.Communication{
							{
								Medium: commPb.Medium_SMS,
								Message: &commPb.Communication_Sms{
									Sms: &commPb.SMSMessage{
										SmsOption: &commPb.SmsOption{
											Option: &commPb.SmsOption_MandateDeclinedSmsOption{
												MandateDeclinedSmsOption: &commPb.MandateDeclinedSmsOption{
													SmsType: commPb.SmsType_MANDATE_DECLINED,
													Option: &commPb.MandateDeclinedSmsOption_MandateDeclinedV1{
														MandateDeclinedV1: &commPb.MandateDeclinedSmsOptionV1{
															MandateExecutionDate: time.Now().Format("12-02-2020"),
															TemplateVersion:      commPb.TemplateVersion_VERSION_V1,
															MandateAmount:        money.AmountINR(1000).GetPb(),
															PayeeName: &commontypes.Name{
																FirstName: "Yatin",
																LastName:  "Kwatra",
																Honorific: "Mr",
															},
															MandateFrequency: "daily",
														},
													},
												},
											},
										},
									},
								},
							},
						},
						QualityOfService: commPb.QoS_BEST_EFFORT,
						ClientId:         "client-req-id",
					},
				},
				res: &notificationPb.SendNotificationResponse{
					MessageIdList: []string{
						"abc",
					},
				},
				err: nil,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			env := wts.NewTestWorkflowEnvironment()
			env.RegisterActivity(&orderActivity.Processor{})
			env.RegisterActivity(&celestialActivity.Processor{})
			env.RegisterActivity(&rpActivity.Processor{})
			if tt.mockGetWorkflowProcessingParams.enable {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParams), mock.Anything, defaultWorkflowID).
					Return(tt.mockGetWorkflowProcessingParams.res, tt.mockGetWorkflowProcessingParams.err)
			}

			if tt.mockInitiateWorkflowStage.enable {
				env.OnActivity(string(epifitemporal.InitiateWorkflowStage), mock.Anything, defaultWorkflowID, tt.mockInitiateWorkflowStage.stage, tt.mockInitiateWorkflowStage.status).
					Return(tt.mockInitiateWorkflowStage.err)
			}
			if tt.mockUpdateRecurringPaymentStateAndAction.enable {
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStateAndAction), mock.Anything, tt.mockUpdateRecurringPaymentStateAndAction.req).
					Return(tt.mockUpdateRecurringPaymentStateAndAction.res, tt.mockUpdateRecurringPaymentStateAndAction.err)
			}

			for _, mockUpdateWorkflowStageStatus := range tt.mockUpdateWorkflowStageStatuses {
				if mockUpdateWorkflowStageStatus.enable {
					env.OnActivity(string(epifitemporal.UpdateWorkflowStageStatus), mock.Anything, defaultWorkflowID, mockUpdateWorkflowStageStatus.stage, mockUpdateWorkflowStageStatus.status).
						Return(mockUpdateWorkflowStageStatus.err)
				}
			}

			if tt.mockProcessRecurringPaymentCreation.enable {
				env.OnActivity(string(rpNs.ProcessRecurringPaymentCreation), mock.Anything, tt.mockProcessRecurringPaymentCreation.req).
					Return(tt.mockProcessRecurringPaymentCreation.res, tt.mockProcessRecurringPaymentCreation.err)
			}

			if tt.mockGetNotificationTemplate.enable {
				env.OnActivity(string(rpNs.GetNotificationTemplate), mock.Anything, tt.mockGetNotificationTemplate.req).
					Return(tt.mockGetNotificationTemplate.res, tt.mockGetNotificationTemplate.err).Times(tt.mockGetNotificationTemplate.count)
			}

			if tt.mockSendNotification.enable {
				env.OnActivity(string(epifitemporal.SendNotification), mock.Anything, tt.mockSendNotification.req).Return(
					tt.mockSendNotification.res, tt.mockSendNotification.err).Times(tt.mockSendNotification.count)
			}

			if tt.mockSignalWorkflow.enable {
				env.RegisterDelayedCallback(func() {
					err := env.SignalWorkflowByID(defaultWorkflowID, string(tt.mockSignalWorkflow.signalName), tt.mockSignalWorkflow.signal)
					if err != nil {
						t.Errorf("failed to send signal %s to workflow: %v", tt.mockSignalWorkflow.signalName, err)
					}
				}, tt.mockSignalWorkflow.delay)
			}

			if tt.mockPublishWorkflowUpdateEvent.enable {
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEvent), mock.Anything, defaultWorkflowID).
					Return(tt.mockPublishWorkflowUpdateEvent.err)
			}

			env.ExecuteWorkflow(rpWorkflow.CreateRecurringPaymentViaPayer, tt.req)

			assert.True(t, env.IsWorkflowCompleted())

			if (env.GetWorkflowError() != nil) != tt.wantErr {
				t.Errorf("ProcessRecurringPaymentCreation() error = %v, wantErr %v", env.GetWorkflowError(), tt.wantErr)
			}

			env.AssertExpectations(t)
		})
	}
}
