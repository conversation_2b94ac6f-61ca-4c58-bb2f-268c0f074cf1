package workflow_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/protobuf/encoding/protojson"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epifitemporal"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	orderPb "github.com/epifi/gamma/api/order"
	rpPayloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	celestialActivity "github.com/epifi/gamma/celestial/activity/v2"
	orderActivity "github.com/epifi/gamma/order/activity"
	rpActivity "github.com/epifi/gamma/recurringpayment/activity"
	rpWorkflow "github.com/epifi/gamma/recurringpayment/workflow"
)

func TestExecuteRecurringPaymentWithAuth(t *testing.T) {
	t.Skip("TODO(pay): Re-enable once the tests are fixed")
	const (
		defaultWorkflowID = "default-test-workflow-id"
		ownership         = commontypes.Ownership_EPIFI_TECH
	)

	var (
		payload, _ = protojson.Marshal(&rpPayloadPb.ExecuteRecurringPaymentWithAuth{})

		authPayload, _ = protojson.Marshal(&rpPayloadPb.ExecuteRecurringPaymentAuthSignal{})

		callbackPayload, _ = protojson.Marshal(&rpPayloadPb.ExecuteRecurringPaymentWithAuthCallbackSignal{})

		clientReqId = &workflowPb.ClientReqId{
			Id:     "client-req-id",
			Client: workflowPb.Client_USER_APP,
		}
	)

	type mockGetWorkflowProcessingParamsV2 struct {
		enable bool
		req    *activityPb.GetWorkflowProcessingParamsV2Request
		res    *activityPb.GetWorkflowProcessingParamsV2Response
		err    error
	}

	type mockInitiateWorkflowStageV2 struct {
		enable bool
		req    *activityPb.InitiateWorkflowStageV2Request
		err    error
	}

	type mockUpdateWorkflowStage struct {
		enable bool
		req    *activityPb.UpdateWorkflowStageRequest
		err    error
	}

	type mockPublishWorkflowUpdateEventV2 struct {
		enable bool
		req    *activityPb.PublishWorkflowUpdateEventV2Request
		res    *activityPb.PublishWorkflowUpdateEventV2Response
		err    error
	}

	type mockSignalWorkflow struct {
		enable     bool
		delay      time.Duration
		signal     []byte
		signalName epifitemporal.Signal
	}

	type mockUpdateOrderWorkflowRefID struct {
		enable      bool
		clientReqID string
		err         error
	}

	type mockUpdateOrder struct {
		clientReqID string
		status      orderPb.OrderStatus
		err         error
	}

	type mockPublishOrderUpdate struct {
		enable bool
		req    *activityPb.Request
		err    error
	}

	type mockProcessRecurringPaymentExecution struct {
		enable bool
		req    *activityPb.Request
		res    *activityPb.Response
		err    error
	}

	tests := []struct {
		name                                 string
		req                                  *workflowPb.Request
		mockGetWorkflowProcessingParamsV2    mockGetWorkflowProcessingParamsV2
		mockInitiateWorkflowStageV2          mockInitiateWorkflowStageV2
		mockUpdateOrderWorkflowRefID         mockUpdateOrderWorkflowRefID
		mockUpdateWorkflowStage              []mockUpdateWorkflowStage
		mockUpdateOrder                      []mockUpdateOrder
		mockPublishOrderUpdate               mockPublishOrderUpdate
		mockPublishWorkflowUpdateEventV2     mockPublishWorkflowUpdateEventV2
		mockSignalWorkflow                   []mockSignalWorkflow
		mockProcessRecurringPaymentExecution mockProcessRecurringPaymentExecution
		wantErr                              bool
	}{
		{
			name: "successfully executed mandate recurring payment with auth ",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						Payload:     payload,
						ClientReqId: clientReqId,
					},
				},
			},
			mockInitiateWorkflowStageV2: mockInitiateWorkflowStageV2{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPayment),
					Status:        stagePb.Status_BLOCKED,
				},
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{},
						WfReqId:       defaultWorkflowID,
						StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPayment),
						Status:        stagePb.Status_INITIATED,
					},
				},
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{},
						WfReqId:       defaultWorkflowID,
						StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPayment),
						Status:        stagePb.Status_SUCCESSFUL,
					},
				},
			},
			mockPublishWorkflowUpdateEventV2: mockPublishWorkflowUpdateEventV2{
				enable: true,
				req: &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
				},
				res: &activityPb.PublishWorkflowUpdateEventV2Response{},
			},
			mockSignalWorkflow: []mockSignalWorkflow{
				{
					enable:     true,
					delay:      10 * time.Nanosecond,
					signal:     authPayload,
					signalName: rpNs.ExecuteRecurringPaymentAuthSignal,
				},
			},
			mockUpdateOrderWorkflowRefID: mockUpdateOrderWorkflowRefID{
				enable:      true,
				clientReqID: "client-req-id",
			},
			mockUpdateOrder: []mockUpdateOrder{
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_IN_PAYMENT,
				},
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_PAID,
				},
			},
			mockPublishOrderUpdate: mockPublishOrderUpdate{
				enable: true,
				req: &activityPb.Request{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id",
						Ownership:   ownership,
						Payload:     payload,
					},
				},
			},
			mockProcessRecurringPaymentExecution: mockProcessRecurringPaymentExecution{
				enable: true,
				req: &activityPb.Request{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id",
						Ownership:   ownership,
						Payload:     payload,
					},
				},
				res: &activityPb.Response{},
			},
		},
		{
			name: "successfully executed mandate recurring payment with auth using callback signal",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						Payload:     payload,
						ClientReqId: clientReqId,
					},
				},
			},
			mockInitiateWorkflowStageV2: mockInitiateWorkflowStageV2{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPayment),
					Status:        stagePb.Status_BLOCKED,
				},
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{},
						WfReqId:       defaultWorkflowID,
						StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPayment),
						Status:        stagePb.Status_INITIATED,
					},
				},
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{},
						WfReqId:       defaultWorkflowID,
						StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPayment),
						Status:        stagePb.Status_SUCCESSFUL,
					},
				},
			},
			mockPublishWorkflowUpdateEventV2: mockPublishWorkflowUpdateEventV2{
				enable: true,
				req: &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
				},
				res: &activityPb.PublishWorkflowUpdateEventV2Response{},
			},
			mockSignalWorkflow: []mockSignalWorkflow{
				{
					enable:     true,
					delay:      1 * time.Nanosecond,
					signal:     callbackPayload,
					signalName: rpNs.ExecuteRecurringPaymentWithAuthCallbackSignal,
				},
				{
					enable:     true,
					delay:      10 * time.Nanosecond,
					signal:     authPayload,
					signalName: rpNs.ExecuteRecurringPaymentAuthSignal,
				},
			},
			mockUpdateOrderWorkflowRefID: mockUpdateOrderWorkflowRefID{
				enable:      true,
				clientReqID: "client-req-id",
			},
			mockUpdateOrder: []mockUpdateOrder{
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_IN_PAYMENT,
				},
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_PAID,
				},
			},
			mockPublishOrderUpdate: mockPublishOrderUpdate{
				enable: true,
				req: &activityPb.Request{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id",
						Ownership:   ownership,
						Payload:     payload,
					},
				},
			},
			mockProcessRecurringPaymentExecution: mockProcessRecurringPaymentExecution{
				enable: true,
				req: &activityPb.Request{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id",
						Ownership:   ownership,
						Payload:     payload,
					},
				},
				res: &activityPb.Response{},
			},
		},
		{
			name: "workflow failed on GetWorkflowProcessingParamsV2",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				err: epifitemporal.NewPermanentError(errors.New("test error")),
			},
			wantErr: true,
		},
		{
			name: "workflow failed for InitiateWorkflowStageV2",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						Payload:     payload,
						ClientReqId: clientReqId,
					},
				},
			},
			mockInitiateWorkflowStageV2: mockInitiateWorkflowStageV2{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPayment),
					Status:        stagePb.Status_BLOCKED,
				},
				err: epifitemporal.NewPermanentError(errors.New("test error")),
			},
			wantErr: true,
		},
		{
			name: "workflow failed for UpdateWorkflowStage",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						Payload:     payload,
						ClientReqId: clientReqId,
					},
				},
			},
			mockInitiateWorkflowStageV2: mockInitiateWorkflowStageV2{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPayment),
					Status:        stagePb.Status_BLOCKED,
				},
			},
			mockSignalWorkflow: []mockSignalWorkflow{
				{
					enable:     true,
					delay:      10 * time.Nanosecond,
					signal:     authPayload,
					signalName: rpNs.ExecuteRecurringPaymentAuthSignal,
				},
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{},
						WfReqId:       defaultWorkflowID,
						StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPayment),
						Status:        stagePb.Status_INITIATED,
					},
					err: epifitemporal.NewPermanentError(errors.New("test error")),
				},
			},
			wantErr: true,
		},
		{
			name: "UpdateOrderWorkflowRefID order activity failed",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						Payload:     payload,
						ClientReqId: clientReqId,
					},
				},
			},
			mockInitiateWorkflowStageV2: mockInitiateWorkflowStageV2{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPayment),
					Status:        stagePb.Status_BLOCKED,
				},
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{},
						WfReqId:       defaultWorkflowID,
						StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPayment),
						Status:        stagePb.Status_INITIATED,
					},
				},
			},
			mockSignalWorkflow: []mockSignalWorkflow{
				{
					enable:     true,
					delay:      10 * time.Nanosecond,
					signal:     authPayload,
					signalName: rpNs.ExecuteRecurringPaymentAuthSignal,
				},
			},
			mockUpdateOrderWorkflowRefID: mockUpdateOrderWorkflowRefID{
				enable:      true,
				clientReqID: "client-req-id",
				err:         epifitemporal.NewPermanentError(errors.New("test error")),
			},
			wantErr: true,
		},
		{
			name: "update order activity failed",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						Payload:     payload,
						ClientReqId: clientReqId,
					},
				},
			},
			mockInitiateWorkflowStageV2: mockInitiateWorkflowStageV2{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPayment),
					Status:        stagePb.Status_BLOCKED,
				},
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{},
						WfReqId:       defaultWorkflowID,
						StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPayment),
						Status:        stagePb.Status_INITIATED,
					},
				},
			},
			mockSignalWorkflow: []mockSignalWorkflow{
				{
					enable:     true,
					delay:      10 * time.Nanosecond,
					signal:     authPayload,
					signalName: rpNs.ExecuteRecurringPaymentAuthSignal,
				},
			},
			mockUpdateOrderWorkflowRefID: mockUpdateOrderWorkflowRefID{
				enable:      true,
				clientReqID: "client-req-id",
			},
			mockUpdateOrder: []mockUpdateOrder{
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_IN_PAYMENT,
					err:         epifitemporal.NewPermanentError(errors.New("test error")),
				},
			},
			wantErr: true,
		},
		{
			name: "ProcessRecurringPaymentExecution activity failed with permanent error",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						Payload:     payload,
						ClientReqId: clientReqId,
					},
				},
			},
			mockInitiateWorkflowStageV2: mockInitiateWorkflowStageV2{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPayment),
					Status:        stagePb.Status_BLOCKED,
				},
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{},
						WfReqId:       defaultWorkflowID,
						StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPayment),
						Status:        stagePb.Status_INITIATED,
					},
				},
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{},
						WfReqId:       defaultWorkflowID,
						StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPayment),
						Status:        stagePb.Status_FAILED,
					},
				},
			},
			mockPublishWorkflowUpdateEventV2: mockPublishWorkflowUpdateEventV2{
				enable: true,
				req: &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
				},
				res: &activityPb.PublishWorkflowUpdateEventV2Response{},
			},
			mockSignalWorkflow: []mockSignalWorkflow{
				{
					enable:     true,
					delay:      10 * time.Nanosecond,
					signal:     authPayload,
					signalName: rpNs.ExecuteRecurringPaymentAuthSignal,
				},
			},
			mockUpdateOrderWorkflowRefID: mockUpdateOrderWorkflowRefID{
				enable:      true,
				clientReqID: "client-req-id",
			},
			mockUpdateOrder: []mockUpdateOrder{
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_IN_PAYMENT,
				},
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_PAYMENT_FAILED,
				},
			},
			mockPublishOrderUpdate: mockPublishOrderUpdate{
				enable: true,
				req: &activityPb.Request{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id",
						Ownership:   ownership,
						Payload:     payload,
					},
				},
			},
			mockProcessRecurringPaymentExecution: mockProcessRecurringPaymentExecution{
				enable: true,
				req: &activityPb.Request{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id",
						Ownership:   ownership,
						Payload:     payload,
					},
				},
				err: epifitemporal.NewPermanentError(errors.New("test error")),
			},
		},
		{
			name: "ProcessRecurringPaymentExecution activity failed with transient error",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						Payload:     payload,
						ClientReqId: clientReqId,
					},
				},
			},
			mockInitiateWorkflowStageV2: mockInitiateWorkflowStageV2{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPayment),
					Status:        stagePb.Status_BLOCKED,
				},
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{},
						WfReqId:       defaultWorkflowID,
						StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPayment),
						Status:        stagePb.Status_INITIATED,
					},
				},
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{},
						WfReqId:       defaultWorkflowID,
						StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.ProcessPayment),
						Status:        stagePb.Status_MANUAL_INTERVENTION,
					},
				},
			},
			mockSignalWorkflow: []mockSignalWorkflow{
				{
					enable:     true,
					delay:      10 * time.Nanosecond,
					signal:     authPayload,
					signalName: rpNs.ExecuteRecurringPaymentAuthSignal,
				},
			},
			mockUpdateOrderWorkflowRefID: mockUpdateOrderWorkflowRefID{
				enable:      true,
				clientReqID: "client-req-id",
			},
			mockUpdateOrder: []mockUpdateOrder{
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_IN_PAYMENT,
				},
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_MANUAL_INTERVENTION,
				},
			},
			mockPublishOrderUpdate: mockPublishOrderUpdate{
				enable: true,
				req: &activityPb.Request{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id",
						Ownership:   ownership,
						Payload:     payload,
					},
				},
			},
			mockProcessRecurringPaymentExecution: mockProcessRecurringPaymentExecution{
				enable: true,
				req: &activityPb.Request{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id",
						Ownership:   ownership,
						Payload:     payload,
					},
				},
				err: epifitemporal.NewTransientError(errors.New("test error")),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			env := wts.NewTestWorkflowEnvironment()
			env.RegisterActivity(&celestialActivity.Processor{})
			env.RegisterActivity(&orderActivity.Processor{})
			env.RegisterActivity(&rpActivity.Processor{})

			if tt.mockGetWorkflowProcessingParamsV2.enable {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, tt.mockGetWorkflowProcessingParamsV2.req).
					Return(tt.mockGetWorkflowProcessingParamsV2.res, tt.mockGetWorkflowProcessingParamsV2.err)
			}

			if tt.mockInitiateWorkflowStageV2.enable {
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, tt.mockInitiateWorkflowStageV2.req).
					Return(tt.mockInitiateWorkflowStageV2.err)
			}

			for _, mockUpdateWfStage := range tt.mockUpdateWorkflowStage {
				if mockUpdateWfStage.enable {
					env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, mockUpdateWfStage.req).
						Return(mockUpdateWfStage.err)
				}
			}

			if tt.mockPublishOrderUpdate.enable {
				env.OnActivity(string(epifitemporal.PublishOrderUpdate), mock.Anything, tt.mockPublishOrderUpdate.req).
					Return(tt.mockPublishOrderUpdate.err)
			}

			for _, updateMock := range tt.mockUpdateOrder {
				env.OnActivity(string(epifitemporal.UpdateOrder), mock.Anything, updateMock.clientReqID, updateMock.status).
					Return(updateMock.err)
			}

			if tt.mockUpdateOrderWorkflowRefID.enable {
				env.OnActivity(string(epifitemporal.UpdateOrderWorkflowRefID), mock.Anything, tt.mockUpdateOrderWorkflowRefID.clientReqID, defaultWorkflowID).
					Return(tt.mockUpdateOrderWorkflowRefID.err)
			}

			if tt.mockPublishWorkflowUpdateEventV2.enable {
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, tt.mockPublishWorkflowUpdateEventV2.req).
					Return(tt.mockPublishWorkflowUpdateEventV2.err)
			}

			for _, mockSignal := range tt.mockSignalWorkflow {
				if mockSignal.enable {
					env.RegisterDelayedCallback(func() {
						err := env.SignalWorkflowByID(defaultWorkflowID, string(mockSignal.signalName), mockSignal.signal)
						if err != nil {
							t.Errorf("failed to send signal %s to workflow: %v", mockSignal.signalName, err)
						}
					}, mockSignal.delay)
				}
			}

			if tt.mockProcessRecurringPaymentExecution.enable {
				env.OnActivity(string(rpNs.ProcessRecurringPaymentExecution), mock.Anything, tt.mockProcessRecurringPaymentExecution.req).
					Return(tt.mockProcessRecurringPaymentExecution.res, tt.mockProcessRecurringPaymentExecution.err)
			}

			env.ExecuteWorkflow(rpWorkflow.ExecuteRecurringPaymentWithAuth, tt.req)
			assert.True(t, env.IsWorkflowCompleted())
			if (env.GetWorkflowError() != nil) != tt.wantErr {
				t.Errorf("ExecuteRecurringPaymentWithAuth() error = %v, wantErr %v", env.GetWorkflowError(), tt.wantErr)
			}

			env.AssertExpectations(t)
		})
	}
}
