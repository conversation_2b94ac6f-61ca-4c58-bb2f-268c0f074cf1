package workflow_test

import (
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/protobuf/encoding/protojson"

	celestialPb "github.com/epifi/be-common/api/celestial"
	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	rpPayloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	celestialActivity "github.com/epifi/gamma/celestial/activity"
	orderActivity "github.com/epifi/gamma/order/activity"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	rpActivity "github.com/epifi/gamma/recurringpayment/activity"
	rpWorkflow "github.com/epifi/gamma/recurringpayment/workflow"
)

func TestExecuteRecurringPaymentWithoutAuth(t *testing.T) {
	const defaultWorkflowID = "default-test-workflow-id"
	var (
		successPayload, _ = protojson.Marshal(&rpPayloadPb.ExecuteRecurringPaymentWithoutAuthCallbackSignal{
			TransactionStatus: paymentPb.TransactionStatus_SUCCESS})

		failedPayload, _ = protojson.Marshal(&rpPayloadPb.ExecuteRecurringPaymentWithoutAuthCallbackSignal{
			TransactionStatus: paymentPb.TransactionStatus_FAILED})

		inProgressPayload, _ = protojson.Marshal(&rpPayloadPb.ExecuteRecurringPaymentWithoutAuthCallbackSignal{
			TransactionStatus: paymentPb.TransactionStatus_IN_PROGRESS})

		manualInterventionPayload, _ = protojson.Marshal(&rpPayloadPb.ExecuteRecurringPaymentWithoutAuthCallbackSignal{
			TransactionStatus: paymentPb.TransactionStatus_MANUAL_INTERVENTION})
	)
	type mockGetWorkflowProcessingParams struct {
		enable bool
		res    *celestialPb.WorkflowProcessingParams
		err    error
	}
	type mockInitiateWorkflowStage struct {
		enable bool
		stage  workflowPb.Stage
		status stagePb.Status
		err    error
	}
	type mockUpdateOrderWorkflowRefID struct {
		enable      bool
		clientReqID string
		err         error
	}
	type mockUpdateOrder struct {
		clientReqID string
		status      orderPb.OrderStatus
		err         error
	}
	type mockProcessRecurringPaymentExecution struct {
		enable          bool
		req             *activityPb.Request
		res             *activityPb.Response
		processingDelay time.Duration
		err             error
	}
	type mockSignalWorkflow struct {
		enable     bool
		delay      time.Duration
		signal     []byte
		signalName epifitemporal.Signal
	}
	type mockUpdateWorkflowStageStatus struct {
		enable bool
		stage  workflowPb.Stage
		status stagePb.Status
		err    error
	}
	type mockPublishOrderUpdate struct {
		enable bool
		req    *activityPb.Request
		err    error
	}
	type mockPublishWorkflowUpdateEvent struct {
		enable bool
		err    error
	}
	tests := []struct {
		name                                 string
		req                                  *workflowPb.Request
		mockGetWorkflowProcessingParams      mockGetWorkflowProcessingParams
		mockInitiateWorkflowStage            mockInitiateWorkflowStage
		mockUpdateOrderWorkflowRefID         mockUpdateOrderWorkflowRefID
		mockUpdateOrder                      []*mockUpdateOrder
		mockProcessRecurringPaymentExecution mockProcessRecurringPaymentExecution
		mockSignalWorkflow                   mockSignalWorkflow
		mockUpdateWorkflowStageStatus        mockUpdateWorkflowStageStatus
		mockPublishOrderUpdate               mockPublishOrderUpdate
		mockPublishWorkflowUpdateEvent       mockPublishWorkflowUpdateEvent
		wantErr                              bool
	}{
		{
			name: "successfully executed recurring payment as ProcessRecurringPaymentExecution succeeded",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_INITIATED,
			},
			mockUpdateOrderWorkflowRefID: mockUpdateOrderWorkflowRefID{
				enable:      true,
				clientReqID: "client-req-id",
			},
			mockUpdateOrder: []*mockUpdateOrder{
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_IN_PAYMENT,
				},
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_PAID,
				},
			},
			mockUpdateWorkflowStageStatus: mockUpdateWorkflowStageStatus{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_SUCCESSFUL,
			},
			mockProcessRecurringPaymentExecution: mockProcessRecurringPaymentExecution{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
				res: &activityPb.Response{},
			},
			mockPublishOrderUpdate: mockPublishOrderUpdate{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
			},
			mockPublishWorkflowUpdateEvent: mockPublishWorkflowUpdateEvent{
				enable: true,
			},
			wantErr: false,
		},
		{
			name: "recurring payment execution failed because ProcessRecurringPaymentExecution returned failure",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_INITIATED,
			},
			mockUpdateOrderWorkflowRefID: mockUpdateOrderWorkflowRefID{
				enable:      true,
				clientReqID: "client-req-id",
			},
			mockUpdateOrder: []*mockUpdateOrder{
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_IN_PAYMENT,
				},
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_PAYMENT_FAILED,
				},
			},
			mockUpdateWorkflowStageStatus: mockUpdateWorkflowStageStatus{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_FAILED,
			},
			mockProcessRecurringPaymentExecution: mockProcessRecurringPaymentExecution{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
				err: epifitemporal.NewPermanentError(errors.New("test error")),
			},
			mockPublishOrderUpdate: mockPublishOrderUpdate{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
			},
			mockPublishWorkflowUpdateEvent: mockPublishWorkflowUpdateEvent{
				enable: true,
			},
			wantErr: false,
		},
		{
			name: "recurring payment execution went into manual intervention because ProcessRecurringPaymentExecution returned manual intervention",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_INITIATED,
			},
			mockUpdateOrderWorkflowRefID: mockUpdateOrderWorkflowRefID{
				enable:      true,
				clientReqID: "client-req-id",
			},
			mockUpdateOrder: []*mockUpdateOrder{
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_IN_PAYMENT,
				},
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_MANUAL_INTERVENTION,
				},
			},
			mockUpdateWorkflowStageStatus: mockUpdateWorkflowStageStatus{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_MANUAL_INTERVENTION,
			},
			mockProcessRecurringPaymentExecution: mockProcessRecurringPaymentExecution{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
				err: epifitemporal.NewTransientError(errors.New("test error")),
			},
			mockPublishOrderUpdate: mockPublishOrderUpdate{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
			},
			mockPublishWorkflowUpdateEvent: mockPublishWorkflowUpdateEvent{
				enable: true,
			},
			wantErr: false,
		},
		{
			name: "successfully executed recurring payment via callback",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_INITIATED,
			},
			mockUpdateOrderWorkflowRefID: mockUpdateOrderWorkflowRefID{
				enable:      true,
				clientReqID: "client-req-id",
			},
			mockProcessRecurringPaymentExecution: mockProcessRecurringPaymentExecution{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
				res: &activityPb.Response{},
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable:     true,
				delay:      10 * time.Nanosecond,
				signal:     successPayload,
				signalName: rpNs.ExecuteRecurringPaymentWithoutAuthCallbackSignal,
			},
			mockUpdateOrder: []*mockUpdateOrder{
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_IN_PAYMENT,
				},
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_PAID,
				},
			},
			mockUpdateWorkflowStageStatus: mockUpdateWorkflowStageStatus{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_SUCCESSFUL,
			},
			mockPublishOrderUpdate: mockPublishOrderUpdate{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
			},
			mockPublishWorkflowUpdateEvent: mockPublishWorkflowUpdateEvent{
				enable: true,
			},
			wantErr: false,
		},
		{
			name: "failed to execute recurring payment because received failed via callback",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_INITIATED,
			},
			mockUpdateOrderWorkflowRefID: mockUpdateOrderWorkflowRefID{
				enable:      true,
				clientReqID: "client-req-id",
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable:     true,
				delay:      10 * time.Nanosecond,
				signal:     failedPayload,
				signalName: rpNs.ExecuteRecurringPaymentWithoutAuthCallbackSignal,
			},
			mockUpdateOrder: []*mockUpdateOrder{
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_IN_PAYMENT,
				},
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_PAYMENT_FAILED,
				},
			},
			mockUpdateWorkflowStageStatus: mockUpdateWorkflowStageStatus{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_FAILED,
			},
			mockPublishOrderUpdate: mockPublishOrderUpdate{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
			},
			mockPublishWorkflowUpdateEvent: mockPublishWorkflowUpdateEvent{
				enable: true,
			},
			wantErr: false,
		},
		{
			name: "failed to execute recurring payment because received manual intervention via callback",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_INITIATED,
			},
			mockUpdateOrderWorkflowRefID: mockUpdateOrderWorkflowRefID{
				enable:      true,
				clientReqID: "client-req-id",
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable:     true,
				delay:      10 * time.Nanosecond,
				signal:     manualInterventionPayload,
				signalName: rpNs.ExecuteRecurringPaymentWithoutAuthCallbackSignal,
			},
			mockProcessRecurringPaymentExecution: mockProcessRecurringPaymentExecution{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
				res: &activityPb.Response{},
				err: epifitemporal.NewTransientError(errors.New("test error")),
			},
			mockUpdateOrder: []*mockUpdateOrder{
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_IN_PAYMENT,
				},
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_MANUAL_INTERVENTION,
				},
			},
			mockUpdateWorkflowStageStatus: mockUpdateWorkflowStageStatus{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_MANUAL_INTERVENTION,
			},
			mockPublishOrderUpdate: mockPublishOrderUpdate{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
			},
			mockPublishWorkflowUpdateEvent: mockPublishWorkflowUpdateEvent{
				enable: true,
			},
			wantErr: false,
		},
		{
			name: "should wait on process recurring payment execution activity handle in case workflow received unexpected signal",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_INITIATED,
			},
			mockUpdateOrderWorkflowRefID: mockUpdateOrderWorkflowRefID{
				enable:      true,
				clientReqID: "client-req-id",
			},
			mockProcessRecurringPaymentExecution: mockProcessRecurringPaymentExecution{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
				res:             &activityPb.Response{},
				processingDelay: time.Microsecond,
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable:     true,
				delay:      10 * time.Nanosecond,
				signal:     inProgressPayload,
				signalName: rpNs.ExecuteRecurringPaymentWithoutAuthCallbackSignal,
			},
			mockUpdateOrder: []*mockUpdateOrder{
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_IN_PAYMENT,
				},
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_PAID,
				},
			},
			mockUpdateWorkflowStageStatus: mockUpdateWorkflowStageStatus{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_SUCCESSFUL,
			},
			mockPublishOrderUpdate: mockPublishOrderUpdate{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
			},
			mockPublishWorkflowUpdateEvent: mockPublishWorkflowUpdateEvent{
				enable: true,
			},
			wantErr: false,
		},
		{
			name: "should return error in case GetWorkflowProcessingParams failed",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				err:    epifitemporal.NewPermanentError(errors.New("test error")),
			},
			wantErr: true,
		},
		{
			name: "should return error in case InitiateWorkflowStage failed",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_INITIATED,
				err:    epifitemporal.NewPermanentError(errors.New("test error")),
			},
			wantErr: true,
		},
		{
			name: "should return error in case UpdateOrderWorkflowRefID failed",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_INITIATED,
			},
			mockUpdateOrderWorkflowRefID: mockUpdateOrderWorkflowRefID{
				enable:      true,
				clientReqID: "client-req-id",
				err:         epifitemporal.NewPermanentError(errors.New("test error")),
			},
			wantErr: true,
		},
		{
			name: "should return error in case first UpdateOrder failed",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_INITIATED,
			},
			mockUpdateOrderWorkflowRefID: mockUpdateOrderWorkflowRefID{
				enable:      true,
				clientReqID: "client-req-id",
			},
			mockUpdateOrder: []*mockUpdateOrder{
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_IN_PAYMENT,
					err:         epifitemporal.NewPermanentError(errors.New("test error")),
				},
			},
			wantErr: true,
		},
		{
			name: "should return error in case second UpdateOrder failed",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_INITIATED,
			},
			mockUpdateOrderWorkflowRefID: mockUpdateOrderWorkflowRefID{
				enable:      true,
				clientReqID: "client-req-id",
			},
			mockUpdateOrder: []*mockUpdateOrder{
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_IN_PAYMENT,
				},
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_PAID,
					err:         epifitemporal.NewPermanentError(errors.New("test error")),
				},
			},
			mockPublishOrderUpdate: mockPublishOrderUpdate{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
			},
			mockProcessRecurringPaymentExecution: mockProcessRecurringPaymentExecution{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
				res:             &activityPb.Response{},
				processingDelay: time.Microsecond,
			},
			wantErr: true,
		},
		{
			name: "should return error in case PublishOrderUpdate failed",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_INITIATED,
			},
			mockUpdateOrderWorkflowRefID: mockUpdateOrderWorkflowRefID{
				enable:      true,
				clientReqID: "client-req-id",
			},
			mockUpdateOrder: []*mockUpdateOrder{
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_IN_PAYMENT,
				},
			},
			mockPublishOrderUpdate: mockPublishOrderUpdate{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
				err: epifitemporal.NewPermanentError(errors.New("test error")),
			},
			wantErr: true,
		},
		{
			name: "should return error in case UpdateWorkflowStageStatus failed",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_INITIATED,
			},
			mockUpdateOrderWorkflowRefID: mockUpdateOrderWorkflowRefID{
				enable:      true,
				clientReqID: "client-req-id",
			},
			mockUpdateOrder: []*mockUpdateOrder{
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_IN_PAYMENT,
				},
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_PAID,
				},
			},
			mockProcessRecurringPaymentExecution: mockProcessRecurringPaymentExecution{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
			},
			mockPublishOrderUpdate: mockPublishOrderUpdate{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
			},
			mockUpdateWorkflowStageStatus: mockUpdateWorkflowStageStatus{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_SUCCESSFUL,
				err:    epifitemporal.NewPermanentError(errors.New("test error")),
			},
			wantErr: true,
		},
		{
			name: "should return error in case PublishWorkflowUpdateEvent failed",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &celestialPb.WorkflowProcessingParams{
					Payload: []byte("test-payload"),
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
				},
			},
			mockInitiateWorkflowStage: mockInitiateWorkflowStage{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_INITIATED,
			},
			mockUpdateOrderWorkflowRefID: mockUpdateOrderWorkflowRefID{
				enable:      true,
				clientReqID: "client-req-id",
			},
			mockUpdateOrder: []*mockUpdateOrder{
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_IN_PAYMENT,
				},
				{
					clientReqID: "client-req-id",
					status:      orderPb.OrderStatus_PAID,
				},
			},
			mockUpdateWorkflowStageStatus: mockUpdateWorkflowStageStatus{
				enable: true,
				stage:  workflowPb.Stage_PAYMENT,
				status: stagePb.Status_SUCCESSFUL,
			},
			mockProcessRecurringPaymentExecution: mockProcessRecurringPaymentExecution{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
				res:             &activityPb.Response{},
				processingDelay: time.Microsecond,
			},
			mockPublishOrderUpdate: mockPublishOrderUpdate{
				enable: true,
				req: &activityPb.Request{
					ClientReqId: "client-req-id",
					Payload:     []byte("test-payload"),
				},
			},
			mockPublishWorkflowUpdateEvent: mockPublishWorkflowUpdateEvent{
				enable: true,
				err:    epifitemporal.NewPermanentError(errors.New("test error")),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			env := wts.NewTestWorkflowEnvironment()
			env.RegisterActivity(&orderActivity.Processor{})
			env.RegisterActivity(&celestialActivity.Processor{})
			env.RegisterActivity(&rpActivity.Processor{})

			if tt.mockGetWorkflowProcessingParams.enable {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParams), mock.Anything, defaultWorkflowID).
					Return(tt.mockGetWorkflowProcessingParams.res, tt.mockGetWorkflowProcessingParams.err)
			}

			if tt.mockInitiateWorkflowStage.enable {
				env.OnActivity(string(epifitemporal.InitiateWorkflowStage), mock.Anything, defaultWorkflowID, tt.mockInitiateWorkflowStage.stage, tt.mockInitiateWorkflowStage.status).
					Return(tt.mockInitiateWorkflowStage.err)
			}

			if tt.mockUpdateOrderWorkflowRefID.enable {
				env.OnActivity(string(epifitemporal.UpdateOrderWorkflowRefID), mock.Anything, tt.mockUpdateOrderWorkflowRefID.clientReqID, defaultWorkflowID).
					Return(tt.mockUpdateOrderWorkflowRefID.err)
			}

			if tt.mockSignalWorkflow.enable {
				env.RegisterDelayedCallback(func() {
					err := env.SignalWorkflowByID(defaultWorkflowID, string(tt.mockSignalWorkflow.signalName), tt.mockSignalWorkflow.signal)
					if err != nil {
						t.Errorf("failed to send signal %s to workflow: %v", tt.mockSignalWorkflow.signalName, err)
					}
				}, tt.mockSignalWorkflow.delay)
			}

			for _, updateMock := range tt.mockUpdateOrder {
				env.OnActivity(string(epifitemporal.UpdateOrder), mock.Anything, updateMock.clientReqID, updateMock.status).
					Return(updateMock.err)
			}

			if tt.mockUpdateWorkflowStageStatus.enable {
				env.OnActivity(string(epifitemporal.UpdateWorkflowStageStatus), mock.Anything, defaultWorkflowID, tt.mockUpdateWorkflowStageStatus.stage, tt.mockUpdateWorkflowStageStatus.status).
					Return(tt.mockUpdateWorkflowStageStatus.err)
			}

			if tt.mockProcessRecurringPaymentExecution.enable {
				env.OnActivity(string(rpNs.ProcessRecurringPaymentExecution), mock.Anything, tt.mockProcessRecurringPaymentExecution.req).
					Return(tt.mockProcessRecurringPaymentExecution.res, tt.mockProcessRecurringPaymentExecution.err)
			}

			if tt.mockPublishOrderUpdate.enable {
				env.OnActivity(string(epifitemporal.PublishOrderUpdate), mock.Anything, tt.mockPublishOrderUpdate.req).
					Return(tt.mockPublishOrderUpdate.err)
			}

			if tt.mockPublishWorkflowUpdateEvent.enable {
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEvent), mock.Anything, defaultWorkflowID).
					Return(tt.mockPublishWorkflowUpdateEvent.err)
			}

			env.ExecuteWorkflow(rpWorkflow.ExecuteRecurringPaymentWithoutAuth, tt.req)

			assert.True(t, env.IsWorkflowCompleted())

			if (env.GetWorkflowError() != nil) != tt.wantErr {
				t.Errorf("ExecuteRecurringPaymentWithoutAuth() error = %v, wantErr %v", env.GetWorkflowError(), tt.wantErr)
			}

			env.AssertExpectations(t)
		})
	}
}
