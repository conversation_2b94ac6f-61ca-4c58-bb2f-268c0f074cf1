package workflow

import (
	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/logger"
	orderPb "github.com/epifi/gamma/api/order"
	rpPayloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
)

// ExecuteRecurringPaymentWithAuth - executes recurring payment where authorisation is required.
// e.g. in case of mandates authorisation is required while making the payment
// nolint: dupl
func ExecuteRecurringPaymentWithAuth(ctx workflow.Context, _ *workflowPb.Request) error {
	var (
		lg      = workflow.GetLogger(ctx)
		wfReqID = workflow.GetInfo(ctx).WorkflowExecution.ID
	)
	payloadObj := &rpPayloadPb.ExecuteRecurringPaymentWithAuth{}
	wfProcessingParams, err := celestialPkg.GetWorkflowProcessingReqParams(ctx, payloadObj)
	if err != nil {
		lg.Error("error in getting workflow processing params", zap.Error(err))
		return err
	}

	if err = processRecurringPaymentExecutionWithAuth(ctx, wfProcessingParams, wfReqID); err != nil {
		lg.Error("failed to process recurring payment with auth execution request", zap.Error(err))
		return err
	}

	return nil
}

// processRecurringPaymentExecutionWithAuth processes payment stage for a recurring payment execution with auth
// nolint
func processRecurringPaymentExecutionWithAuth(ctx workflow.Context, wfProcessingParams *workflowPb.ProcessingParams, wfReqID string) error {
	lg := workflow.GetLogger(ctx)

	err := celestialPkg.InitiateWorkflowStage(ctx, rpNs.ProcessPayment, stagePb.Status_BLOCKED)
	if err != nil {
		lg.Error("InitiateWorkflowStageV2 activity failed", zap.Error(err))
		return fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStageV2, err)
	}

	// auth signal handling
	workflowStageStatus := stagePb.Status_INITIATED
	signalPayload := &rpPayloadPb.ExecuteRecurringPaymentAuthSignal{}
	sigChannel := epifitemporal.NewSignalChannel(ctx, rpNs.ExecuteRecurringPaymentAuthSignal, signalPayload)
	sigChannel.AddReceiverHandler(func(getErr error, payload *rpPayloadPb.ExecuteRecurringPaymentAuthSignal) {
		if getErr != nil {
			workflowStageStatus = celestialPkg.GetWorkflowStageStatusForErr(getErr)
			lg.Error("failed while receiving signal", zap.String(logger.SIGNAL_NAME, string(rpNs.ExecuteRecurringPaymentAuthSignal)), zap.Error(getErr))
			return
		}
	})

	err = epifitemporal.ReceiveSignal(ctx, sigChannel, 10*time.Minute)
	if err != nil {
		lg.Error("failed to receive auth signal", zap.Error(err))
		workflowStageStatus = celestialPkg.GetWorkflowStageStatusForErr(err)
	}

	err = celestialPkg.UpdateWorkflowStage(ctx, rpNs.ProcessPayment, workflowStageStatus)
	if err != nil {
		lg.Error("UpdateWorkflowStage activity failed", zap.Error(err))
		return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStage, err)
	}

	if workflowStageStatus != stagePb.Status_INITIATED {
		lg.Error("workflow was not initiated", zap.String(logger.STATE, workflowStageStatus.String()))
		return nil
	}

	if err = activityPkg.ExecuteRaw(ctx, epifitemporal.UpdateOrderWorkflowRefID, nil, wfProcessingParams.GetClientReqId().GetId(), wfReqID); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateOrderWorkflowRefID, err)
	}

	if err = activityPkg.ExecuteRaw(ctx, epifitemporal.UpdateOrder, nil, wfProcessingParams.GetClientReqId().GetId(), orderPb.OrderStatus_IN_PAYMENT); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateOrder, err)
	}
	v := workflow.GetVersion(ctx, "publishing-non-terminal-updates-in-order-update-topic", workflow.DefaultVersion, 1)
	if v == 1 {
		if err = activityPkg.Execute(ctx, epifitemporal.PublishOrderUpdate, &activityPb.Response{}, &activityPb.Request{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
				Ownership:   epificontext.OwnershipFromContext(ctx),
				Payload:     wfProcessingParams.GetPayload(),
			},
		}); err != nil {
			return fmt.Errorf("%s activity failed: %w", epifitemporal.PublishOrderUpdate, err)
		}
	}

	finalOrderStatus, workflowStageStatus, err := processExecutionWithAuth(ctx, wfProcessingParams)
	if err != nil {
		return fmt.Errorf("processExecutionNoAuth failed: %w", err)
	}

	if err = activityPkg.ExecuteRaw(ctx, epifitemporal.UpdateOrder, nil, wfProcessingParams.GetClientReqId().GetId(), finalOrderStatus); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateOrder, err)
	}

	if err = activityPkg.ExecuteRaw(ctx, epifitemporal.PublishOrderUpdate, nil, &activityPb.Request{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
			Payload:     wfProcessingParams.GetPayload(),
		},
	}); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.PublishOrderUpdate, err)
	}

	var paymentReversalOrderStatus orderPb.OrderStatus
	if finalOrderStatus == orderPb.OrderStatus_PAYMENT_FAILED {
		paymentReversalOrderStatus = handleReversalInCaseOfPaymentFailure(ctx)
	}
	if paymentReversalOrderStatus == orderPb.OrderStatus_PAYMENT_REVERSED {
		// update the DB entries and publish the events
		if err = activityPkg.ExecuteRaw(ctx, epifitemporal.UpdateOrder, nil, wfProcessingParams.GetClientReqId().GetId(), paymentReversalOrderStatus); err != nil {
			return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateOrder, err)
		}

		if err = activityPkg.ExecuteRaw(ctx, epifitemporal.PublishOrderUpdate, nil, &activityPb.Request{
			ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
			Payload:     wfProcessingParams.GetPayload(),
		}); err != nil {
			return fmt.Errorf("%s activity failed: %w", epifitemporal.PublishOrderUpdate, err)
		}
	}

	err = celestialPkg.UpdateWorkflowStage(ctx, rpNs.ProcessPayment, workflowStageStatus)
	if err != nil {
		lg.Error("UpdateWorkflowStage activity failed", zap.Error(err))
		return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStage, err)
	}

	return nil
}

// processExecutionWithAuth - It takes workflow processing params as input and does payment processing
// using domain activity `ProcessRecurringPaymentExecution`. Since the request is asynchronous, it fires
// the activity asynchronously and parallely waits for the corresponding callback to return.
// The final status of the payment is decided on a first come first basis.
func processExecutionWithAuth(ctx workflow.Context, wfProcessingParams *workflowPb.ProcessingParams) (orderPb.OrderStatus, stagePb.Status, error) {
	var (
		finalOrderStatus orderPb.OrderStatus
		finalStageStatus stagePb.Status
		res              = &activityPb.Response{}
		lg               = workflow.GetLogger(ctx)
	)

	processPaymentFuture, err := activityPkg.ExecuteAsync(ctx, rpNs.ProcessRecurringPaymentExecution, &activityPb.Request{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
			Payload:     wfProcessingParams.GetPayload(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
	}, res)
	if err != nil {
		return orderPb.OrderStatus_ORDER_STATUS_UNSPECIFIED, stagePb.Status_STATUS_UNSPECIFIED,
			fmt.Errorf("%s activity initiation failed: %w", rpNs.ProcessRecurringPaymentExecution, err)
	}

	processPaymentFuture.AddFutureHandler(func(getErr error, res *activityPb.Response) {
		lg.Debug("ProcessRecurringPaymentExecution activity returned first")

		if getErr != nil {
			lg.Error("ProcessRecurringPaymentExecution activity async processing failed", zap.Error(getErr))
			finalOrderStatus, finalStageStatus = GetOrderAndWorkflowStatusForError(getErr)
			return
		}

		finalOrderStatus, finalStageStatus = GetOrderAndWorkflowStatusForError(getErr)
	})

	signalPayload := &rpPayloadPb.ExecuteRecurringPaymentWithAuthCallbackSignal{}
	sigChannel := epifitemporal.NewSignalChannel(ctx, rpNs.ExecuteRecurringPaymentWithAuthCallbackSignal, signalPayload)
	sigChannel.AddReceiverHandler(func(getErr error, payload *rpPayloadPb.ExecuteRecurringPaymentWithAuthCallbackSignal) {
		lg.Debug("signal returned first")

		if getErr != nil {
			lg.Error("ExecuteRecurringPaymentWithAuthCallbackSignal signal processing failed", zap.Error(getErr))
			finalOrderStatus, finalStageStatus = GetOrderAndWorkflowStatusForError(getErr)
			return
		}

		// finalOrderStatus and finalCelestialWorkflowStatus need to be the latest status from ProcessRecurringPaymentExecution
		processRecurringPayResOnSignal := &activityPb.Response{}
		err = activityPkg.Execute(ctx, rpNs.ProcessRecurringPaymentExecution, processRecurringPayResOnSignal, &activityPb.Request{
			ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
			Payload:     wfProcessingParams.GetPayload(),
		})

		finalOrderStatus, finalStageStatus = GetOrderAndWorkflowStatusForError(err)

	})

	err = epifitemporal.ReceiveSignalWithFuture(ctx, processPaymentFuture, sigChannel, 1*time.Hour)
	if err != nil {
		lg.Error("ReceiveSignalWithFuture failed", zap.Error(err))
		finalStageStatus = celestialPkg.GetWorkflowStageStatusForErr(err)
	}

	return finalOrderStatus, finalStageStatus, nil
}

func GetOrderAndWorkflowStatusForError(err error) (orderPb.OrderStatus, stagePb.Status) {
	switch {
	case err != nil && !epifitemporal.IsRetryableError(err):
		return orderPb.OrderStatus_PAYMENT_FAILED, stagePb.Status_FAILED
	case err != nil:
		return orderPb.OrderStatus_MANUAL_INTERVENTION, stagePb.Status_MANUAL_INTERVENTION
	default:
		return orderPb.OrderStatus_PAID, stagePb.Status_SUCCESSFUL
	}
}
