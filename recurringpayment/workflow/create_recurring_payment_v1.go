package workflow

import (
	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/emptypb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/logger"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	rpPayloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	rpSignalPb "github.com/epifi/gamma/api/recurringpayment/workflow"
)

const (
	// authInitSignalTimeoutBufferDuration is the buffer duration that we subtract from the user passed
	// recurring payment expiry time while comparing so that some buffer time is available for the remaining
	// processing to be done.
	authInitSignalTimeoutBufferDuration = time.Second * 30
	// Min SLA within which the payload should be fetched for serving to user for authorization.
	// If the client explicitly passes an expiry time which has a greater duration than workflow.Now()
	// then we will use that.
	minCreationAuthorisationInitiationTimeout = time.Minute * 6
	// SLA within which the creation should be authorised by the user
	creationAuthorisationCompletionTimeout = time.Minute * 12
)

// nolint:funlen
func CreateRecurringPaymentV1(ctx workflow.Context, _ *rpPayloadPb.CreateRecurringPaymentV1WorkflowPayload) error {
	lg := workflow.GetLogger(ctx)

	var workflowPayload = &rpPayloadPb.CreateRecurringPaymentV1WorkflowPayload{}
	_, err := celestialPkg.GetWorkflowProcessingReqParams(ctx, workflowPayload)
	if err != nil {
		lg.Error("failed to fetch workflow processing params", zap.Error(err))
		return err
	}

	// ============ STAGE :  CREATE DOMAIN ENTITIES ==============
	stageStatus, err := processDomainEntityCreationStage(ctx, &processDomainEntityCreationStageReq{
		recurringPaymentId:                  workflowPayload.GetRecurringPaymentId(),
		recurringPaymentActionId:            workflowPayload.GetRecurringPaymentActionId(),
		recurringPaymentTypeSpecificPayload: workflowPayload.GetRecurringPaymentTypeSpecificPayload(),
	})
	if err != nil {
		lg.Error("error in domain entity creation stage", zap.String(logger.RECURRING_PAYMENT_ID, workflowPayload.GetRecurringPaymentId()), zap.Error(err))
		return err
	}
	// nolint: dupl
	if stageStatus == stagePb.Status_FAILED {
		lg.Error("domain entity creation stage failed", zap.String(logger.RECURRING_PAYMENT_ID, workflowPayload.GetRecurringPaymentId()))
		stageStatus, err = initiatePostCreationProcessingStage(ctx, &initiatePostCreationProcessingStageReq{
			recurringPaymentId:       workflowPayload.GetRecurringPaymentId(),
			recurringPaymentActionId: workflowPayload.GetRecurringPaymentActionId(),
			stage:                    rpNs.CreateRecurringPaymentInitiatePostFailureProcessing,
		})
		if err != nil || stageStatus != stagePb.Status_SUCCESSFUL {
			lg.Error("post creation stage was not successful", zap.String(logger.RECURRING_PAYMENT_ID, workflowPayload.GetRecurringPaymentId()), zap.String(logger.STAGE_STATUS, stageStatus.String()), zap.Error(err))
			// returning error as we want to trigger alerts for such cases
			return fmt.Errorf("post creation stage was not successful")
		}
		return nil
	}
	if stageStatus != stagePb.Status_SUCCESSFUL {
		lg.Error("domain entity creation stage was not successful", zap.String(logger.RECURRING_PAYMENT_ID, workflowPayload.GetRecurringPaymentId()), zap.String(logger.STAGE_STATUS, stageStatus.String()))
		// returning error as we want to trigger alerts for such cases
		return fmt.Errorf("domain entity creation stage was not successful")
	}

	// ============ STAGE:  CREATION AUTHORISATION ==============
	stageStatus, err = processCreationAuthorisationStage(ctx, &processCreationAuthorisationStageReq{
		recurringPaymentId:       workflowPayload.GetRecurringPaymentId(),
		recurringPaymentActionId: workflowPayload.GetRecurringPaymentActionId(),
		authorisationInitExpiry:  workflowPayload.GetExpiry(),
	})
	if err != nil {
		lg.Error("error in creation authorization stage", zap.String(logger.RECURRING_PAYMENT_ID, workflowPayload.GetRecurringPaymentId()), zap.Error(err))
		return err
	}
	// nolint: dupl
	if stageStatus == stagePb.Status_FAILED || stageStatus == stagePb.Status_TIMEOUT {
		lg.Error("creation authorisation stage failed", zap.String(logger.RECURRING_PAYMENT_ID, workflowPayload.GetRecurringPaymentId()))
		stageStatus, err = initiatePostCreationProcessingStage(ctx, &initiatePostCreationProcessingStageReq{
			recurringPaymentId:       workflowPayload.GetRecurringPaymentId(),
			recurringPaymentActionId: workflowPayload.GetRecurringPaymentActionId(),
			stage:                    rpNs.CreateRecurringPaymentInitiatePostFailureProcessing,
		})
		if err != nil || stageStatus != stagePb.Status_SUCCESSFUL {
			lg.Error("post creation stage was not successful", zap.String(logger.RECURRING_PAYMENT_ID, workflowPayload.GetRecurringPaymentId()), zap.String(logger.STAGE_STATUS, stageStatus.String()), zap.Error(err))
			// returning error as we want to trigger alerts for such cases
			return fmt.Errorf("post creation stage was not successful")
		}
		return nil
	}
	if stageStatus != stagePb.Status_SUCCESSFUL {
		lg.Error("creation authorisation stage not successful", zap.String(logger.RECURRING_PAYMENT_ID, workflowPayload.GetRecurringPaymentId()), zap.String(logger.STAGE_STATUS, stageStatus.String()))
		// returning error as we want to trigger alerts for such cases
		return fmt.Errorf("creation authorisation stage not successful")
	}

	// ============ STAGE : ACTIVATION COOL OFF ==============
	stageStatus, err = processActivationCoolOffStage(ctx, &processActivationCoolOffStageReq{
		recurringPaymentId:       workflowPayload.GetRecurringPaymentId(),
		recurringPaymentActionId: workflowPayload.GetRecurringPaymentActionId(),
	})
	if err != nil {
		lg.Error("error in processing activation coolOff stage", zap.String(logger.RECURRING_PAYMENT_ID, workflowPayload.GetRecurringPaymentId()), zap.Error(err))
		return err
	}
	// nolint: dupl
	if stageStatus != stagePb.Status_SUCCESSFUL {
		lg.Error("activation cool off stage was not successful", zap.String(logger.RECURRING_PAYMENT_ID, workflowPayload.GetRecurringPaymentId()), zap.String(logger.STAGE_STATUS, stageStatus.String()))
		// returning error as we want to trigger alerts for such cases
		return fmt.Errorf("activation cool off stage not successful")
	}

	// ============ STAGE : VENDOR ACTIVATION ==============
	stageStatus, err = processDomainActivationStage(ctx, &processDomainActivationStageReq{
		recurringPaymentId:       workflowPayload.GetRecurringPaymentId(),
		recurringPaymentActionId: workflowPayload.GetRecurringPaymentActionId(),
	})
	if err != nil {
		lg.Error("error in domain activation stage", zap.String(logger.RECURRING_PAYMENT_ID, workflowPayload.GetRecurringPaymentId()), zap.Error(err))
		return err
	}
	// nolint: dupl
	if stageStatus == stagePb.Status_FAILED {
		lg.Error("domain activation stage failed", zap.String(logger.RECURRING_PAYMENT_ID, workflowPayload.GetRecurringPaymentId()))
		stageStatus, err = initiatePostCreationProcessingStage(ctx, &initiatePostCreationProcessingStageReq{
			recurringPaymentId:       workflowPayload.GetRecurringPaymentId(),
			recurringPaymentActionId: workflowPayload.GetRecurringPaymentActionId(),
			stage:                    rpNs.CreateRecurringPaymentInitiatePostFailureProcessing,
		})
		if err != nil || stageStatus != stagePb.Status_SUCCESSFUL {
			lg.Error("post creation stage was not successful", zap.String(logger.RECURRING_PAYMENT_ID, workflowPayload.GetRecurringPaymentId()), zap.String(logger.STAGE_STATUS, stageStatus.String()), zap.Error(err))
			// returning error as we want to trigger alerts for such cases
			return fmt.Errorf("post creation stage was not successful")
		}
		return nil
	}
	// nolint: dupl
	if stageStatus != stagePb.Status_SUCCESSFUL {
		lg.Error("domain activation stage not successful", zap.String(logger.RECURRING_PAYMENT_ID, workflowPayload.GetRecurringPaymentId()), zap.String(logger.STAGE_STATUS, stageStatus.String()))
		// returning error as we want to trigger alerts for such cases
		return fmt.Errorf("domain activation stage not successful")
	}

	// ============ STAGE : POST SUCCESSFUL CREATION PROCESSING ==============
	stageStatus, err = initiatePostCreationProcessingStage(ctx, &initiatePostCreationProcessingStageReq{
		recurringPaymentId:       workflowPayload.GetRecurringPaymentId(),
		recurringPaymentActionId: workflowPayload.GetRecurringPaymentActionId(),
		stage:                    rpNs.CreateRecurringPaymentInitiatePostSuccessProcessing,
	})
	if err != nil || stageStatus != stagePb.Status_SUCCESSFUL {
		lg.Error("post successful creation stage was not successful", zap.String(logger.RECURRING_PAYMENT_ID, workflowPayload.GetRecurringPaymentId()), zap.String(logger.STAGE_STATUS, stageStatus.String()), zap.Error(err))
		// returning error as we want to trigger alerts for such cases
		return fmt.Errorf("post successful creation stage was not successful")
	}

	return nil
}

type processDomainEntityCreationStageReq struct {
	recurringPaymentId                  string
	recurringPaymentActionId            string
	recurringPaymentTypeSpecificPayload *rpPayloadPb.RecurringPaymentTypeSpecificCreationPayload
}

// nolint:funlen
func processDomainEntityCreationStage(ctx workflow.Context, req *processDomainEntityCreationStageReq) (stagePb.Status, error) {
	var (
		lg                 = workflow.GetLogger(ctx)
		currentStage       = rpNs.CreateRecurringPaymentDomainEntityCreation
		currentStageStatus = stagePb.Status_INITIATED
	)

	// =========== initiate stage for the workflow ============
	if err := celestialPkg.InitiateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to initiate workflow stage: %s: %w", currentStage, err)
	}

	// =========== initiate domain entity creation ============
	createDomainEntitiesRes := &rpActivityPb.CreateDomainEntitiesResponse{}
	err := activityPkg.Execute(ctx, rpNs.CreateDomainEntities, createDomainEntitiesRes, &rpActivityPb.CreateDomainEntitiesRequest{
		RecurringPaymentId:                  req.recurringPaymentId,
		RecurringPaymentActionId:            req.recurringPaymentActionId,
		RecurringPaymentTypeSpecificPayload: req.recurringPaymentTypeSpecificPayload,
	})
	if err != nil {
		lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(rpNs.CreateDomainEntities)), zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(err))
		currentStageStatus = stagePb.Status_FAILED
	} else {
		switch createDomainEntitiesRes.GetDomainCreationStatus() {
		case rpActivityPb.CreateDomainEntitiesResponse_DOMAIN_CREATION_STATUS_SUCCESS:
			lg.Info("domain entity created successfully", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId))
			currentStageStatus = stagePb.Status_SUCCESSFUL
		case rpActivityPb.CreateDomainEntitiesResponse_DOMAIN_CREATION_STATUS_FAILURE:
			lg.Error("failed to create domain entities", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId))
			currentStageStatus = stagePb.Status_FAILED
		// todo: Re-visit this logic when integrating SI or UPI mandates
		default:
			// Moving to failed instead of manual intervention since this is not critical, and we can safely abandon the entry created at domain
			lg.Error("unhandled domain creation status", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.String(logger.STATUS, createDomainEntitiesRes.GetDomainCreationStatus().String()))
			currentStageStatus = stagePb.Status_FAILED
		}
	}

	// =========== Handle Stage Failure ============
	if currentStageStatus == stagePb.Status_FAILED {
		if updateErr := updateRecurringPaymentAndAction(ctx, &updateRecurringPaymentAndActionReq{
			recurringPaymentId:                 req.recurringPaymentId,
			recurringPaymentActionId:           req.recurringPaymentActionId,
			currentRecurringPaymentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
			currentRecurringPaymentActionState: rpPb.ActionState_ACTION_CREATED,
			newRecurringPaymentStatus:          rpPb.RecurringPaymentState_FAILED,
			newRecurringPaymentActionState:     rpPb.ActionState_ACTION_FAILURE,
		}); updateErr != nil {
			lg.Error("error updating recurring payment to failed state", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(updateErr))
			// since recurring payment status update failed, moving the current stage status to MANUAL_INTERVENTION state instead of FAILED state
			currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
		}
		if err = celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
			return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage status: %s, status : %s, err : %w", currentStage, currentStageStatus, err)
		}
		return currentStageStatus, nil
	}

	// =========== update recurring payment status ============
	if err = activityPkg.Execute(ctx, rpNs.UpdateRecurringPaymentStatus, &rpActivityPb.UpdateRecurringPaymentStatusResponse{}, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
		RecurringPaymentId: req.recurringPaymentId,
		CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
		NextStatus:         rpPb.RecurringPaymentState_CREATION_INITIATED,
	}); err != nil {
		lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(rpNs.UpdateRecurringPaymentStatus)), zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(err))
		// since recurring payment status update failed, moving the current stage status to MANUAL_INTERVENTION state instead of SUCCESS state
		currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
	}

	// =========== update workflow stage status ============
	if err = celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage status: %s, status : %s, err : %w", currentStage, currentStageStatus, err)
	}

	return currentStageStatus, nil
}

type processCreationAuthorisationStageReq struct {
	recurringPaymentId       string
	recurringPaymentActionId string
	// The timestamp within which the authorisation initiation signal must be received or else the status
	// will be marked as failed. The min default value is 6 mins, so this param will be considered only if
	// it is greater than 6 min.
	authorisationInitExpiry *timestampPb.Timestamp
}

// nolint:funlen
func processCreationAuthorisationStage(ctx workflow.Context, req *processCreationAuthorisationStageReq) (stagePb.Status, error) {
	var (
		lg                 = workflow.GetLogger(ctx)
		currentStage       = rpNs.CreateRecurringPaymentAuthorisation
		currentStageStatus = stagePb.Status_BLOCKED
	)

	// =========== initiate stage for the workflow ============
	if err := celestialPkg.InitiateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to initiate workflow stage: %s: %w", currentStage, err)
	}

	// =========== waiting for the auth initiation signal ============
	sigChannel := epifitemporal.NewSignalChannel(ctx, rpNs.CreateRecurringPaymentV1AuthorisationInitiatedSignal, &emptypb.Empty{})
	sigChannel.AddReceiverHandler(func(getErr error, resp *emptypb.Empty) {
		if getErr != nil {
			lg.Error("error receiving CreateRecurringPaymentV1AuthorisationInitiatedSignal payload", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(getErr))
			currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
			return
		}
		currentStageStatus = stagePb.Status_PENDING
	})
	version := workflow.GetVersion(ctx, "allow-custom-timeout", workflow.DefaultVersion, 2)
	switch version {
	case workflow.DefaultVersion:
		if err := epifitemporal.ReceiveSignal(ctx, sigChannel, minCreationAuthorisationInitiationTimeout); err != nil {
			lg.Error("failed while receiving CreateRecurringPaymentV1AuthorisationInitiatedSignal", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(err))
			currentStageStatus = stagePb.Status_FAILED
		}
	case 1:
		durationToUse := minCreationAuthorisationInitiationTimeout
		curTime := workflow.Now(ctx)
		if req.authorisationInitExpiry != nil {
			computedDuration := req.authorisationInitExpiry.AsTime().Sub(curTime) - authInitSignalTimeoutBufferDuration
			lg.Info("Received authorisationInitExpiry", zap.Time("authorisationInitExpiry", req.authorisationInitExpiry.AsTime()), zap.Duration("computedDuration", computedDuration))
			if computedDuration > durationToUse {
				durationToUse = computedDuration
			}
		}
		if err := epifitemporal.ReceiveSignal(ctx, sigChannel, durationToUse); err != nil {
			lg.Error("failed while receiving CreateRecurringPaymentV1AuthorisationInitiatedSignal", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(err))
			currentStageStatus = stagePb.Status_FAILED
		}
	default:
		durationToUse := minCreationAuthorisationInitiationTimeout
		curTime := workflow.Now(ctx)
		if req.authorisationInitExpiry != nil {
			computedDuration := req.authorisationInitExpiry.AsTime().Sub(curTime) - authInitSignalTimeoutBufferDuration
			lg.Info("Received authorisationInitExpiry", zap.Time("authorisationInitExpiry", req.authorisationInitExpiry.AsTime()), zap.Duration("computedDuration", computedDuration))
			if computedDuration > durationToUse {
				durationToUse = computedDuration
			}
		}
		if err := epifitemporal.ReceiveSignal(ctx, sigChannel, durationToUse); err != nil {
			lg.Error("failed while receiving CreateRecurringPaymentV1AuthorisationInitiatedSignal", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(err))
			if epifitemporal.HasSignalReceivedTimedOut(err) {
				currentStageStatus = stagePb.Status_TIMEOUT
			} else {
				currentStageStatus = stagePb.Status_FAILED
			}
		}
	}

	// =========== Handle Signaling Failures ============
	if currentStageStatus == stagePb.Status_FAILED {
		if updateErr := updateRecurringPaymentAndAction(ctx, &updateRecurringPaymentAndActionReq{
			recurringPaymentId:                 req.recurringPaymentId,
			recurringPaymentActionId:           req.recurringPaymentActionId,
			currentRecurringPaymentStatus:      rpPb.RecurringPaymentState_CREATION_INITIATED,
			currentRecurringPaymentActionState: rpPb.ActionState_ACTION_CREATED,
			newRecurringPaymentStatus:          rpPb.RecurringPaymentState_FAILED,
			newRecurringPaymentActionState:     rpPb.ActionState_ACTION_FAILURE,
		}); updateErr != nil {
			lg.Error("error updating recurring payment to failed state", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(updateErr))
			// since recurring payment status update failed, moving the current stage status to MANUAL_INTERVENTION state instead of FAILED state
			currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
		}

		if err := celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
			return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage status: %s, status : %s, err : %w", currentStage, currentStageStatus, err)
		}
		return currentStageStatus, nil
	}

	expireRpVersion := workflow.GetVersion(ctx, "move-rp-to-expired", workflow.DefaultVersion, 1)
	switch expireRpVersion {
	case workflow.DefaultVersion:
	default:
		// =========== Handle Signaling Timeout ============
		// nolint:dupl
		if currentStageStatus == stagePb.Status_TIMEOUT {
			if updateErr := updateRecurringPaymentAndAction(ctx, &updateRecurringPaymentAndActionReq{
				recurringPaymentId:                 req.recurringPaymentId,
				recurringPaymentActionId:           req.recurringPaymentActionId,
				currentRecurringPaymentStatus:      rpPb.RecurringPaymentState_CREATION_INITIATED,
				currentRecurringPaymentActionState: rpPb.ActionState_ACTION_CREATED,
				newRecurringPaymentStatus:          rpPb.RecurringPaymentState_EXPIRED,
				newRecurringPaymentActionState:     rpPb.ActionState_ACTION_EXPIRED,
			}); updateErr != nil {
				lg.Error("error updating recurring payment to expired state", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(updateErr))
				// since recurring payment status update failed, moving the current stage status to MANUAL_INTERVENTION state instead of TIMEOUT state
				currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
			}

			if err := celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
				return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage status: %s, status : %s, err : %w", currentStage, currentStageStatus, err)
			}
			return currentStageStatus, nil
		}
	}

	// =========== update workflow stage status ============
	if err := celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage status: %s, status : %s, err : %w", currentStage, currentStageStatus, err)
	}

	// continue further ONLY if current stage is in PENDING state
	if currentStageStatus != stagePb.Status_PENDING {
		lg.Info("current stage is not in expected status to continue further", zap.String(logger.STAGE_STATUS, currentStageStatus.String()), zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId))
		return currentStageStatus, nil
	}

	// =========== waiting for the auth signal ============
	authSigChannel := epifitemporal.NewSignalChannel(ctx, rpNs.CreateRecurringPaymentV1AuthorisationCompletedSignal, &rpSignalPb.CreateRecurringPaymentAuthorisationSignal{})
	authSigChannel.AddReceiverHandler(func(getErr error, resp *rpSignalPb.CreateRecurringPaymentAuthorisationSignal) {
		if getErr != nil {
			lg.Error("error receiving CreateRecurringPaymentV1AuthorisationCompletedSignal payload", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(getErr))
			currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
			return
		}
		switch resp.GetAuthorisationStatus() {
		case rpSignalPb.CreateRecurringPaymentAuthorisationSignal_AUTHORISATION_STATUS_SUCCESS:
			currentStageStatus = stagePb.Status_SUCCESSFUL
		case rpSignalPb.CreateRecurringPaymentAuthorisationSignal_AUTHORISATION_STATUS_FAILURE:
			currentStageStatus = stagePb.Status_FAILED
		default:
			lg.Error("unhandled authorization status in CreateRecurringPaymentV1AuthorisationCompletedSignal payload", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.String(logger.STATUS, resp.GetAuthorisationStatus().String()), zap.Error(getErr))
			currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
		}
	})
	if err := epifitemporal.ReceiveSignal(ctx, authSigChannel, creationAuthorisationCompletionTimeout); err != nil {
		lg.Error("failed while receiving signal", zap.String(logger.SIGNAL_NAME, string(rpNs.CreateRecurringPaymentV1AuthorisationCompletedSignal)), zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(err))
		currentStageStatus = stagePb.Status_FAILED
	}

	// =========== Handle Signaling Failures ============
	if currentStageStatus == stagePb.Status_FAILED {
		if updateErr := updateRecurringPaymentAndAction(ctx, &updateRecurringPaymentAndActionReq{
			recurringPaymentId:                 req.recurringPaymentId,
			recurringPaymentActionId:           req.recurringPaymentActionId,
			currentRecurringPaymentStatus:      rpPb.RecurringPaymentState_CREATION_INITIATED,
			currentRecurringPaymentActionState: rpPb.ActionState_ACTION_CREATED,
			newRecurringPaymentStatus:          rpPb.RecurringPaymentState_FAILED,
			newRecurringPaymentActionState:     rpPb.ActionState_ACTION_FAILURE,
		}); updateErr != nil {
			lg.Error("error updating recurring payment to failed state", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(updateErr))
			// since recurring payment status update failed, moving the current stage status to MANUAL_INTERVENTION state instead of FAILED state
			currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
		}

		if err := celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
			return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage status: %s, status : %s, err : %w", currentStage, currentStageStatus, err)
		}
		return currentStageStatus, nil
	}

	// continue further ONLY if current stage is in SUCCESS state
	if currentStageStatus != stagePb.Status_SUCCESSFUL {
		if err := celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
			return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage status: %s, status : %s, err : %w", currentStage, currentStageStatus, err)
		}
		lg.Info("current stage is not in expected status to continue further", zap.String(logger.STAGE_STATUS, currentStageStatus.String()), zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId))
		return currentStageStatus, nil
	}

	// =========== update recurring payment entity ============
	updateRecurringPaymentRes := &rpActivityPb.UpdateRecurringPaymentStatusResponse{}
	if err := activityPkg.Execute(ctx, rpNs.UpdateRecurringPaymentStatus, updateRecurringPaymentRes, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
		RecurringPaymentId: req.recurringPaymentId,
		CurrentStatus:      rpPb.RecurringPaymentState_CREATION_INITIATED,
		NextStatus:         rpPb.RecurringPaymentState_CREATION_AUTHORISED,
	}); err != nil {
		lg.Error("error updating recurring payment status to CREATION_AUTHORISED state", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(err))
		currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
	}

	// =========== update workflow stage status ============
	if err := celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage status: %s, status : %s, err : %w", currentStage, currentStageStatus, err)
	}

	return currentStageStatus, nil
}

type processActivationCoolOffStageReq struct {
	recurringPaymentId       string
	recurringPaymentActionId string
}

// nolint:funlen
func processActivationCoolOffStage(ctx workflow.Context, req *processActivationCoolOffStageReq) (stagePb.Status, error) {
	var (
		lg                 = workflow.GetLogger(ctx)
		currentStage       = rpNs.CreateRecurringPaymentActivationCoolOff
		currentStageStatus = stagePb.Status_INITIATED
	)

	// =========== initiate stage for the workflow ============
	if err := celestialPkg.InitiateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to initiate workflow stage: %s: %w", currentStage, err)
	}

	// =========== fetch cool off for the activation ============
	getActivationCoolOffRes := &rpActivityPb.GetActivationCoolOffResponse{}
	err := activityPkg.Execute(ctx, rpNs.GetActivationCoolOff, getActivationCoolOffRes, &rpActivityPb.GetActivationCoolOffRequest{
		RecurringPaymentId: req.recurringPaymentId,
	})
	if err != nil {
		lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(rpNs.GetActivationCoolOff)), zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(err))
		currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
	} else {
		switch getActivationCoolOffRes.GetGetActivationCoolOffActivityStatus() {
		case rpActivityPb.GetActivationCoolOffResponse_GET_ACTIVATION_COOL_OFF_ACTIVITY_STATUS_SUCCESS:
			workflowSleepError := workflow.Sleep(ctx, getActivationCoolOffRes.GetCoolOffDuration().AsDuration())
			if workflowSleepError != nil {
				lg.Error("error while sleeping the creation workflow for activation coolOff", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(workflowSleepError))
				currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
				break
			}
			lg.Info("get activation cool off activity was successful", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId))
			currentStageStatus = stagePb.Status_SUCCESSFUL
		case rpActivityPb.GetActivationCoolOffResponse_GET_ACTIVATION_COOL_OFF_ACTIVITY_STATUS_FAILURE:
			lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(rpNs.GetActivationCoolOff)), zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(err))
			currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
		default:
			lg.Error("unhandled activation cool off activity status", zap.String(logger.STATUS, getActivationCoolOffRes.GetGetActivationCoolOffActivityStatus().String()), zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId))
			currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
		}
	}

	// =========== update workflow stage status ============
	if err = celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage status: %s, status : %s, err : %w", currentStage, currentStageStatus, err)
	}
	return currentStageStatus, nil
}

type processDomainActivationStageReq struct {
	recurringPaymentId       string
	recurringPaymentActionId string
}

// nolint:funlen
func processDomainActivationStage(ctx workflow.Context, req *processDomainActivationStageReq) (stagePb.Status, error) {
	var (
		lg                 = workflow.GetLogger(ctx)
		currentStage       = rpNs.CreateRecurringPaymentDomainActivation
		currentStageStatus = stagePb.Status_INITIATED
	)

	// =========== initiate stage for the workflow ============
	if err := celestialPkg.InitiateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to initiate workflow stage: %s: %w", currentStage, err)
	}

	// =========== Enquiry the creation status with domain ============
	enquireDomainActivationStatusRes := &rpActivityPb.EnquireDomainActivationStatusResponse{}
	err := activityPkg.Execute(ctx, rpNs.EnquireDomainActivationStatus, enquireDomainActivationStatusRes, &rpActivityPb.EnquireDomainActivationStatusRequest{
		RecurringPaymentId:       req.recurringPaymentId,
		RecurringPaymentActionId: req.recurringPaymentActionId,
	})
	if err != nil {
		lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(rpNs.EnquireDomainActivationStatus)), zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(err))
		currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
	} else {
		switch enquireDomainActivationStatusRes.GetDomainActivationStatus() {
		case rpActivityPb.EnquireDomainActivationStatusResponse_DOMAIN_ACTIVATION_SUCCESS:
			lg.Info("domain activation was successful", zap.String(logger.ACTIVITY, string(rpNs.EnquireDomainActivationStatus)), zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId))
			currentStageStatus = stagePb.Status_SUCCESSFUL
		case rpActivityPb.EnquireDomainActivationStatusResponse_DOMAIN_ACTIVATION_FAILURE:
			lg.Info("domain activation failed at vendor", zap.String(logger.ACTIVITY, string(rpNs.EnquireDomainActivationStatus)), zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId))
			currentStageStatus = stagePb.Status_FAILED
		default:
			lg.Error("unhandled domain activation status", zap.String(logger.STATUS, enquireDomainActivationStatusRes.GetDomainActivationStatus().String()), zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId))
			currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
		}
	}

	// =========== Handle Stage Failure and Manual Intervention ============
	if currentStageStatus == stagePb.Status_FAILED {
		if updateErr := updateRecurringPaymentAndAction(ctx, &updateRecurringPaymentAndActionReq{
			recurringPaymentId:                 req.recurringPaymentId,
			recurringPaymentActionId:           req.recurringPaymentActionId,
			currentRecurringPaymentStatus:      rpPb.RecurringPaymentState_CREATION_AUTHORISED,
			currentRecurringPaymentActionState: rpPb.ActionState_ACTION_CREATED,
			actionDetailedStatusInfo:           enquireDomainActivationStatusRes.GetActionDetailedStatusInfo(),
			newRecurringPaymentStatus:          rpPb.RecurringPaymentState_FAILED,
			newRecurringPaymentActionState:     rpPb.ActionState_ACTION_FAILURE,
		}); updateErr != nil {
			lg.Error("error updating recurring payment to failed state", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(updateErr))
			// since recurring payment status update failed, moving the current stage status to MANUAL_INTERVENTION state instead of FAILED state
			currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
		}

		if err = celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
			return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage status: %s, status : %s, err : %w", currentStage, currentStageStatus, err)
		}
		return currentStageStatus, nil
	}

	// we should ONLY continue further if current stage is SUCCESSFUL
	if currentStageStatus != stagePb.Status_SUCCESSFUL {
		if err = celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
			return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage status: %s, status : %s, err : %w", currentStage, currentStageStatus, err)
		}
		lg.Info("current stage is not in expected status to continue further", zap.String(logger.STAGE_STATUS, currentStageStatus.String()), zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId))
		return currentStageStatus, nil
	}

	// =========== update recurring payment status ============
	err = activityPkg.Execute(ctx, rpNs.UpdateRecurringPaymentStatus, &rpActivityPb.UpdateRecurringPaymentStatusResponse{}, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
		RecurringPaymentId: req.recurringPaymentId,
		CurrentStatus:      rpPb.RecurringPaymentState_CREATION_AUTHORISED,
		NextStatus:         rpPb.RecurringPaymentState_ACTIVATED,
	})
	if err != nil {
		lg.Error("error updating recurring payment status to ACTIVATED state", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(err))
		currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
	} else {
		if updateErr := updateRecurringPaymentActionStatusAndPublishStatusUpdateEvent(ctx, &updateActionStatusAndPublishStatusUpdateEventReq{
			recurringPaymentActionId:    req.recurringPaymentActionId,
			expectedCurrentActionStatus: rpPb.ActionState_ACTION_CREATED,
			newActionStatus:             rpPb.ActionState_ACTION_SUCCESS,
			actionDetailedStatusInfo:    enquireDomainActivationStatusRes.GetActionDetailedStatusInfo(),
		}); updateErr != nil {
			lg.Error("error updating recurring payment action status", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(updateErr))
			currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
		}
	}

	// =========== update workflow stage status ============
	if err = celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage status: %s, status : %s, err : %w", currentStage, currentStageStatus, err)
	}

	return currentStageStatus, nil
}

type initiatePostCreationProcessingStageReq struct {
	recurringPaymentId       string
	recurringPaymentActionId string
	stage                    epifitemporal.Stage
}

// nolint:funlen
// initiatePostCreationProcessingStage will handle post creation processing in cases of both success and failure.
// The name of the stage will be passed in the request.
// The reason for having different names in case of success and failure is that just by looking at the workflow stage and
// status, one will be able to get the entire context.
// This has been implemented using a single function in order to prevent code duplication
// P.S: For other activities, the general pattern is error is returned. but in this we are first logging. This is intentional.
// The reason for this is that we do not want to complicate the logic where the activity is trigerred for failure cases.
func initiatePostCreationProcessingStage(ctx workflow.Context, req *initiatePostCreationProcessingStageReq) (stagePb.Status, error) {
	var (
		lg                 = workflow.GetLogger(ctx)
		stage              = req.stage
		currentStageStatus = stagePb.Status_INITIATED
	)

	// =========== initiate stage for the workflow ============
	if err := celestialPkg.InitiateWorkflowStage(ctx, stage, currentStageStatus); err != nil {
		lg.Error("failed to initiate post creation processing stage", zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.Error(err))
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to initiate post creation processing stage, err : %w", err)
	}

	// =========== Send notification to user ============
	sendNotificationToUserRes := &rpActivityPb.SendStatusNotificationResponse{}
	err := activityPkg.Execute(ctx, rpNs.SendStatusNotification, sendNotificationToUserRes, &rpActivityPb.SendStatusNotificationRequest{
		RecurringPaymentId:       req.recurringPaymentId,
		RecurringPaymentActionId: req.recurringPaymentActionId,
	})
	if err != nil {
		lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(rpNs.SendStatusNotification)), zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.String(logger.STAGE, string(stage)), zap.Error(err))
		currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
	} else {
		switch sendNotificationToUserRes.GetActivityStatus() {
		case rpActivityPb.SendStatusNotificationResponse_SEND_STATUS_NOTIFICATION_SUCCESS:
			lg.Info("recurring payment action status notification sent successfully", zap.String(logger.ACTIVITY, string(rpNs.SendStatusNotification)), zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.String(logger.STAGE, string(stage)))
			currentStageStatus = stagePb.Status_SUCCESSFUL
		case rpActivityPb.SendStatusNotificationResponse_SEND_STATUS_NOTIFICATION_FAILURE:
			lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(rpNs.SendStatusNotification)), zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.String(logger.STAGE, string(stage)), zap.Error(err))
			currentStageStatus = stagePb.Status_FAILED
		default:
			lg.Error("unexpected response while trying to send status notification", zap.String(logger.ACTIVITY, string(rpNs.SendStatusNotification)), zap.String(logger.RECURRING_PAYMENT_ID, req.recurringPaymentId), zap.String(logger.STAGE, string(stage)))
			currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
		}
	}

	// =========== update workflow stage status ============
	if err = celestialPkg.UpdateWorkflowStage(ctx, stage, currentStageStatus); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage status: %s, status : %s, err : %w", stage, currentStageStatus, err)
	}
	return currentStageStatus, nil
}

type updateRecurringPaymentAndActionReq struct {
	recurringPaymentId                 string
	recurringPaymentActionId           string
	currentRecurringPaymentStatus      rpPb.RecurringPaymentState
	currentRecurringPaymentActionState rpPb.ActionState
	actionDetailedStatusInfo           *rpPb.ActionDetailedStatusInfo
	newRecurringPaymentStatus          rpPb.RecurringPaymentState
	newRecurringPaymentActionState     rpPb.ActionState
}

func updateRecurringPaymentAndAction(ctx workflow.Context, req *updateRecurringPaymentAndActionReq) error {
	if err := activityPkg.Execute(ctx, rpNs.UpdateRecurringPaymentStatus, &rpActivityPb.UpdateRecurringPaymentStatusResponse{}, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
		RecurringPaymentId: req.recurringPaymentId,
		CurrentStatus:      req.currentRecurringPaymentStatus,
		NextStatus:         req.newRecurringPaymentStatus,
	}); err != nil {
		return fmt.Errorf("%s activity failed to execute, err: %w", rpNs.UpdateRecurringPaymentStatus, err)
	}

	if err := updateRecurringPaymentActionStatusAndPublishStatusUpdateEvent(ctx, &updateActionStatusAndPublishStatusUpdateEventReq{
		recurringPaymentActionId:    req.recurringPaymentActionId,
		expectedCurrentActionStatus: req.currentRecurringPaymentActionState,
		newActionStatus:             req.newRecurringPaymentActionState,
		actionDetailedStatusInfo:    req.actionDetailedStatusInfo,
	}); err != nil {
		return err
	}
	return nil
}
