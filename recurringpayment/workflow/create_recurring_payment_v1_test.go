package workflow

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"google.golang.org/protobuf/types/known/timestamppb"

	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/testsuite"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/durationpb"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	epifitemporalTest "github.com/epifi/be-common/pkg/epifitemporal/test"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	rpPayloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	rpSignalPb "github.com/epifi/gamma/api/recurringpayment/workflow"
	celestialActivityV2 "github.com/epifi/gamma/celestial/activity/v2"
	rpActivity "github.com/epifi/gamma/recurringpayment/activity"
)

var wts epifitemporalTest.WorkflowTestSuite

func TestCreateRecurringPaymentV1(t *testing.T) {
	const (
		defaultWorkflowID               = "default-test-workflow-id"
		defaultRecurringPaymentId       = "default_test_recurring_payment_id"
		defaultRecurringPaymentActionId = "default_test_recurring_payment_action_id"
	)

	var (
		delayOneMinute     = time.Minute
		delayOneHour       = time.Hour
		delayFiftyNineMins = time.Minute * 59
		clientReqId        = uuid.New().String()

		requestHeaderWithoutClientReqId = &activityPb.RequestHeader{
			Ownership: commontypes.Ownership_EPIFI_TECH,
		}
		defaultRecurringPaymentTypeSpecificPayload = &rpPayloadPb.RecurringPaymentTypeSpecificCreationPayload{}
		workflowPayload                            = &rpPayloadPb.CreateRecurringPaymentV1WorkflowPayload{
			RecurringPaymentActionId:            defaultRecurringPaymentActionId,
			RecurringPaymentId:                  defaultRecurringPaymentId,
			RecurringPaymentTypeSpecificPayload: defaultRecurringPaymentTypeSpecificPayload,
		}
		marshalledWfPayload, _ = protojson.Marshal(workflowPayload)

		workflowPayloadWithOneHourExpiry = &rpPayloadPb.CreateRecurringPaymentV1WorkflowPayload{
			RecurringPaymentActionId:            defaultRecurringPaymentActionId,
			RecurringPaymentId:                  defaultRecurringPaymentId,
			RecurringPaymentTypeSpecificPayload: defaultRecurringPaymentTypeSpecificPayload,
			Expiry:                              timestamppb.New(time.Now().Add(time.Minute * 60)),
		}
		marshalledPayloadWithOneHourExpiry, _ = protojson.Marshal(workflowPayloadWithOneHourExpiry)

		defaultWorkflowProcessingParamsV2Req = &activityPb.GetWorkflowProcessingParamsV2Request{
			RequestHeader: requestHeaderWithoutClientReqId,
			WfReqId:       defaultWorkflowID,
		}
		defaultWorkflowProcessingParamsV2Res = &activityPb.GetWorkflowProcessingParamsV2Response{
			WfReqParams: &workflowPb.ProcessingParams{
				ClientReqId: &workflowPb.ClientReqId{
					Id: clientReqId,
				},
				Payload: marshalledWfPayload,
			},
		}

		workflowProcessingParamsV2ResWithOneHourExpiry = &activityPb.GetWorkflowProcessingParamsV2Response{
			WfReqParams: &workflowPb.ProcessingParams{
				ClientReqId: &workflowPb.ClientReqId{
					Id: clientReqId,
				},
				Payload: marshalledPayloadWithOneHourExpiry,
			},
		}

		defaultCreateDomainEntitiesReq = &rpActivityPb.CreateDomainEntitiesRequest{
			RecurringPaymentId:                  defaultRecurringPaymentId,
			RecurringPaymentActionId:            defaultRecurringPaymentActionId,
			RecurringPaymentTypeSpecificPayload: defaultRecurringPaymentTypeSpecificPayload,
		}
		createDomainEntitiesUnexpectedRes = &rpActivityPb.CreateDomainEntitiesResponse{
			DomainCreationStatus: rpActivityPb.CreateDomainEntitiesResponse_DOMAIN_CREATION_STATUS_UNSPECIFIED,
		}
		createDomainEntitiesFailedRes = &rpActivityPb.CreateDomainEntitiesResponse{
			DomainCreationStatus: rpActivityPb.CreateDomainEntitiesResponse_DOMAIN_CREATION_STATUS_FAILURE,
		}
		createDomainEntitiesSuccessRes = &rpActivityPb.CreateDomainEntitiesResponse{
			DomainCreationStatus: rpActivityPb.CreateDomainEntitiesResponse_DOMAIN_CREATION_STATUS_SUCCESS,
		}
		defaultUpdateRecurringPaymentStatusRes        = &rpActivityPb.UpdateRecurringPaymentStatusResponse{}
		defaultUpdateRecurringPaymentActionStatusRes  = &rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}
		defaultPublishRecurringPaymentActionUpdateRes = &rpActivityPb.PublishRecurringPaymentActionUpdateEventResponse{}
		defaultGetActivationCoolOffReq                = &rpActivityPb.GetActivationCoolOffRequest{
			RecurringPaymentId: defaultRecurringPaymentId,
		}
		getActivationCoolOffUnexpectedRes = &rpActivityPb.GetActivationCoolOffResponse{
			GetActivationCoolOffActivityStatus: rpActivityPb.GetActivationCoolOffResponse_GET_ACTIVATION_COOL_OFF_ACTIVITY_STATUS_UNSPECIFIED,
		}
		getActivationCoolOffFailedRes = &rpActivityPb.GetActivationCoolOffResponse{
			GetActivationCoolOffActivityStatus: rpActivityPb.GetActivationCoolOffResponse_GET_ACTIVATION_COOL_OFF_ACTIVITY_STATUS_FAILURE,
		}
		getActivationCoolOffSuccessRes = &rpActivityPb.GetActivationCoolOffResponse{
			GetActivationCoolOffActivityStatus: rpActivityPb.GetActivationCoolOffResponse_GET_ACTIVATION_COOL_OFF_ACTIVITY_STATUS_SUCCESS,
			CoolOffDuration:                    durationpb.New(time.Hour),
		}
		defaultEnquireDomainActivationStatusReq = &rpActivityPb.EnquireDomainActivationStatusRequest{
			RecurringPaymentId:       defaultRecurringPaymentId,
			RecurringPaymentActionId: defaultRecurringPaymentActionId,
		}
		enquireDomainActivationStatusUnexpectedRes = &rpActivityPb.EnquireDomainActivationStatusResponse{
			DomainActivationStatus: rpActivityPb.EnquireDomainActivationStatusResponse_DOMAIN_ACTIVATION_UNSPECIFIED,
		}
		enquireDomainActivationStatusFailedRes = &rpActivityPb.EnquireDomainActivationStatusResponse{
			DomainActivationStatus: rpActivityPb.EnquireDomainActivationStatusResponse_DOMAIN_ACTIVATION_FAILURE,
			ActionDetailedStatusInfo: &rpPb.ActionDetailedStatusInfo{
				FiStatusCode:     "PG_GENERIC_FAILURE",
				ErrorDescription: "Mandate registration failed due to some unknown reason",
			},
		}
		enquireDomainActivationStatusSuccessRes = &rpActivityPb.EnquireDomainActivationStatusResponse{
			DomainActivationStatus: rpActivityPb.EnquireDomainActivationStatusResponse_DOMAIN_ACTIVATION_SUCCESS,
		}

		defaultSendStatusNotificationReq = &rpActivityPb.SendStatusNotificationRequest{
			RecurringPaymentId:       defaultRecurringPaymentId,
			RecurringPaymentActionId: defaultRecurringPaymentActionId,
		}
		sendStatusNotificationUnexpectedRes = &rpActivityPb.SendStatusNotificationResponse{
			ActivityStatus: rpActivityPb.SendStatusNotificationResponse_SEND_STATUS_NOTIFICATION_UNSPECIFIED,
		}
		sendStatusNotificationSuccessRes = &rpActivityPb.SendStatusNotificationResponse{
			ActivityStatus: rpActivityPb.SendStatusNotificationResponse_SEND_STATUS_NOTIFICATION_SUCCESS,
		}

		temporalPermanentErr = temporal.NewNonRetryableApplicationError("Permanent Error", "", epifierrors.ErrPermanent)
	)

	tests := []struct {
		name              string
		skip              bool
		setupMocks        func(env *testsuite.TestWorkflowEnvironment)
		setupExpectations func(t *testing.T, env *testsuite.TestWorkflowEnvironment)
		wantErr           bool
		err               error
	}{
		// Testing workflow initiation
		{
			name: "Should return error if failed to GetWorkflowProcessingParamsV2",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(nil, temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 0)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 0)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 0)

				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 0)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 0)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 0)
			},
			wantErr: true,
		},
		// Testing DomainEntityCreation stage
		{
			name: "Should return error if error in InitiateWorkflowStageV2",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).Return(temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 0)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 0)

				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 0)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 0)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 0)
			},
			wantErr: true,
		},
		{
			name: "Should return error if error in CreateDomainEntities, UpdateRecurringPaymentStatus and UpdateWorkflowStage",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.CreateDomainEntities), mock.Anything, defaultCreateDomainEntitiesReq).
					Return(nil, temporalPermanentErr)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_FAILED,
				}).Return(nil, temporalPermanentErr)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_MANUAL_INTERVENTION,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).Return(temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 0)

				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 0)
			},
			wantErr: true,
		},
		{
			name: "Should update stage to Manual Intervention if error in CreateDomainEntities and UpdateRecurringPaymentStatus",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.CreateDomainEntities), mock.Anything, defaultCreateDomainEntitiesReq).
					Return(nil, temporalPermanentErr)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_FAILED,
				}).Return(nil, temporalPermanentErr)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_MANUAL_INTERVENTION,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 0)

				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 0)
			},
			wantErr: true,
		},
		{
			name: "Should update stage to Manual Intervention if error in CreateDomainEntities and UpdateRecurringPaymentActionStatus",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.CreateDomainEntities), mock.Anything, defaultCreateDomainEntitiesReq).
					Return(nil, temporalPermanentErr)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_FAILED,
				}).Return(defaultUpdateRecurringPaymentStatusRes, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
					CurrentStatus:            rpPb.ActionState_ACTION_CREATED,
					NextStatus:               rpPb.ActionState_ACTION_FAILURE,
				}).Return(nil, temporalPermanentErr)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_MANUAL_INTERVENTION,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 0)
				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 0)
			},
			wantErr: true,
		},
		{
			name: "Should return error if error in PublishWorkflowUpdateEventV2",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).Return(nil)

				env.OnActivity(string(rpNs.CreateDomainEntities), mock.Anything, defaultCreateDomainEntitiesReq).
					Return(nil, temporalPermanentErr)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_FAILED,
				}).Return(defaultUpdateRecurringPaymentStatusRes, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
					CurrentStatus:            rpPb.ActionState_ACTION_CREATED,
					NextStatus:               rpPb.ActionState_ACTION_FAILURE,
				}).Return(defaultUpdateRecurringPaymentActionStatusRes, nil)

				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				}).Return(defaultPublishRecurringPaymentActionUpdateRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_FAILED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 1)

				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 1)
			},
			wantErr: true,
		},
		{
			name: "Should update stage to Failed if error in CreateDomainEntities",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.CreateDomainEntities), mock.Anything, defaultCreateDomainEntitiesReq).
					Return(nil, temporalPermanentErr)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_FAILED,
				}).Return(defaultUpdateRecurringPaymentStatusRes, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
					CurrentStatus:            rpPb.ActionState_ACTION_CREATED,
					NextStatus:               rpPb.ActionState_ACTION_FAILURE,
				}).Return(defaultUpdateRecurringPaymentActionStatusRes, nil)

				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				}).Return(defaultPublishRecurringPaymentActionUpdateRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_FAILED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostFailureProcessing),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.SendStatusNotification), mock.Anything, defaultSendStatusNotificationReq).
					Return(sendStatusNotificationSuccessRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostFailureProcessing),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 2)

				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 1)
			},
			wantErr: false,
		},
		{
			name: "Should update stage to Failed if failed to CreateDomainEntities",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.CreateDomainEntities), mock.Anything, defaultCreateDomainEntitiesReq).
					Return(createDomainEntitiesFailedRes, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_FAILED,
				}).Return(defaultUpdateRecurringPaymentStatusRes, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
					CurrentStatus:            rpPb.ActionState_ACTION_CREATED,
					NextStatus:               rpPb.ActionState_ACTION_FAILURE,
				}).Return(defaultUpdateRecurringPaymentActionStatusRes, nil)

				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				}).Return(defaultPublishRecurringPaymentActionUpdateRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_FAILED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostFailureProcessing),
				}).Return(nil)

				env.OnActivity(string(rpNs.SendStatusNotification), mock.Anything, defaultSendStatusNotificationReq).
					Return(sendStatusNotificationSuccessRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostFailureProcessing),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 2)

				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 1)
			},
			wantErr: false,
		},
		{
			name: "Should update stage to Failed if unexpected response is received from CreateDomainEntities",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.CreateDomainEntities), mock.Anything, defaultCreateDomainEntitiesReq).
					Return(createDomainEntitiesUnexpectedRes, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_FAILED,
				}).Return(defaultUpdateRecurringPaymentStatusRes, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
					CurrentStatus:            rpPb.ActionState_ACTION_CREATED,
					NextStatus:               rpPb.ActionState_ACTION_FAILURE,
				}).Return(defaultUpdateRecurringPaymentActionStatusRes, nil)

				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				}).Return(defaultPublishRecurringPaymentActionUpdateRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_FAILED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostFailureProcessing),
				}).Return(nil)

				env.OnActivity(string(rpNs.SendStatusNotification), mock.Anything, defaultSendStatusNotificationReq).
					Return(sendStatusNotificationSuccessRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostFailureProcessing),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)

			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 2)

				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 1)
			},
			wantErr: false,
		},

		// Testing Authorisation stage
		{
			name: "Should update stage to Failed if Authorisation initiated signal timed out",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).Return(nil)

				env.OnActivity(string(rpNs.CreateDomainEntities), mock.Anything, defaultCreateDomainEntitiesReq).
					Return(createDomainEntitiesSuccessRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_INITIATED,
				}).Return(defaultUpdateRecurringPaymentStatusRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_BLOCKED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).Return(nil)

				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&emptyPb.Empty{})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationInitiatedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneHour)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_INITIATED,
					NextStatus:         rpPb.RecurringPaymentState_EXPIRED,
				}).Return(defaultUpdateRecurringPaymentStatusRes, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
					CurrentStatus:            rpPb.ActionState_ACTION_CREATED,
					NextStatus:               rpPb.ActionState_ACTION_EXPIRED,
				}).Return(defaultUpdateRecurringPaymentActionStatusRes, nil)

				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				}).Return(defaultPublishRecurringPaymentActionUpdateRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_TIMEOUT,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostFailureProcessing),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.SendStatusNotification), mock.Anything, defaultSendStatusNotificationReq).
					Return(sendStatusNotificationSuccessRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostFailureProcessing),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 2)

				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 1)
			},
			wantErr: false,
		},
		{
			name: "Should update stage to Failed if Authorisation completion signal returned Failed",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).Return(nil)

				env.OnActivity(string(rpNs.CreateDomainEntities), mock.Anything, defaultCreateDomainEntitiesReq).
					Return(createDomainEntitiesSuccessRes, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_INITIATED,
				}).Return(defaultUpdateRecurringPaymentStatusRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_BLOCKED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).Return(nil)

				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&emptyPb.Empty{})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationInitiatedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_PENDING,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).Return(nil)

				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&rpSignalPb.CreateRecurringPaymentAuthorisationSignal{
						AuthorisationStatus: rpSignalPb.CreateRecurringPaymentAuthorisationSignal_AUTHORISATION_STATUS_FAILURE,
					})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationCompletedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_INITIATED,
					NextStatus:         rpPb.RecurringPaymentState_FAILED,
				}).Return(defaultUpdateRecurringPaymentStatusRes, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
					CurrentStatus:            rpPb.ActionState_ACTION_CREATED,
					NextStatus:               rpPb.ActionState_ACTION_FAILURE,
				}).Return(defaultUpdateRecurringPaymentActionStatusRes, nil)

				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				}).Return(defaultPublishRecurringPaymentActionUpdateRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_FAILED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostFailureProcessing),
				}).Return(nil)

				env.OnActivity(string(rpNs.SendStatusNotification), mock.Anything, defaultSendStatusNotificationReq).
					Return(sendStatusNotificationSuccessRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostFailureProcessing),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 4)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 3)

				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 1)
			},
			wantErr: false,
		},
		{
			name: "Support for client configurable expiry duration, Should update stage to Failed if Authorisation completion signal returned Failed",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(workflowProcessingParamsV2ResWithOneHourExpiry, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).Return(nil)

				env.OnActivity(string(rpNs.CreateDomainEntities), mock.Anything, defaultCreateDomainEntitiesReq).
					Return(createDomainEntitiesSuccessRes, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_INITIATED,
				}).Return(defaultUpdateRecurringPaymentStatusRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_BLOCKED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).Return(nil)

				//
				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&emptyPb.Empty{})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationInitiatedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayFiftyNineMins)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_PENDING,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).Return(nil)

				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&rpSignalPb.CreateRecurringPaymentAuthorisationSignal{
						AuthorisationStatus: rpSignalPb.CreateRecurringPaymentAuthorisationSignal_AUTHORISATION_STATUS_FAILURE,
					})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationCompletedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_INITIATED,
					NextStatus:         rpPb.RecurringPaymentState_FAILED,
				}).Return(defaultUpdateRecurringPaymentStatusRes, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
					CurrentStatus:            rpPb.ActionState_ACTION_CREATED,
					NextStatus:               rpPb.ActionState_ACTION_FAILURE,
				}).Return(defaultUpdateRecurringPaymentActionStatusRes, nil)

				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				}).Return(defaultPublishRecurringPaymentActionUpdateRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_FAILED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostFailureProcessing),
				}).Return(nil)

				env.OnActivity(string(rpNs.SendStatusNotification), mock.Anything, defaultSendStatusNotificationReq).
					Return(sendStatusNotificationSuccessRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostFailureProcessing),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 4)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 3)

				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 1)
			},
			wantErr: false,
		},
		{
			name: "Should update stage to Manual Intervention if Authorisation completion signal returned unexpected status",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).Return(nil)

				env.OnActivity(string(rpNs.CreateDomainEntities), mock.Anything, defaultCreateDomainEntitiesReq).
					Return(createDomainEntitiesSuccessRes, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_INITIATED,
				}).Return(defaultUpdateRecurringPaymentStatusRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_BLOCKED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).Return(nil)

				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&emptyPb.Empty{})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationInitiatedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_PENDING,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).Return(nil)

				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&rpSignalPb.CreateRecurringPaymentAuthorisationSignal{
						AuthorisationStatus: rpSignalPb.CreateRecurringPaymentAuthorisationSignal_AUTHORISATION_STATUS_UNSPECIFIED,
					})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationCompletedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_MANUAL_INTERVENTION,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 2)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 1)
				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 0)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 0)
			},
			wantErr: true,
		},

		// Testing Activation Cool Off Stage
		{
			name: "Should update stage to Manual Intervention if GetActivationCoolOff activity returned err",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.CreateDomainEntities), mock.Anything, defaultCreateDomainEntitiesReq).
					Return(createDomainEntitiesSuccessRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_INITIATED,
				}).
					Return(defaultUpdateRecurringPaymentStatusRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_BLOCKED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&emptyPb.Empty{})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationInitiatedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_PENDING,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&rpSignalPb.CreateRecurringPaymentAuthorisationSignal{
						AuthorisationStatus: rpSignalPb.CreateRecurringPaymentAuthorisationSignal_AUTHORISATION_STATUS_SUCCESS,
					})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationCompletedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_INITIATED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_AUTHORISED,
				}).
					Return(defaultUpdateRecurringPaymentStatusRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.GetActivationCoolOff), mock.Anything, defaultGetActivationCoolOffReq).
					Return(nil, temporalPermanentErr)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_MANUAL_INTERVENTION,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 4)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 2)
				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.GetActivationCoolOff), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 0)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 0)
			},
			wantErr: true,
		},
		{
			name: "Should update stage to Manual Intervention if GetActivationCoolOff activity returned unexpected activity status",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.CreateDomainEntities), mock.Anything, defaultCreateDomainEntitiesReq).
					Return(createDomainEntitiesSuccessRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_INITIATED,
				}).
					Return(defaultUpdateRecurringPaymentStatusRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_BLOCKED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&emptyPb.Empty{})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationInitiatedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_PENDING,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&rpSignalPb.CreateRecurringPaymentAuthorisationSignal{
						AuthorisationStatus: rpSignalPb.CreateRecurringPaymentAuthorisationSignal_AUTHORISATION_STATUS_SUCCESS,
					})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationCompletedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_INITIATED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_AUTHORISED,
				}).
					Return(defaultUpdateRecurringPaymentStatusRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.GetActivationCoolOff), mock.Anything, defaultGetActivationCoolOffReq).
					Return(getActivationCoolOffUnexpectedRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_MANUAL_INTERVENTION,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 4)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 2)
				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.GetActivationCoolOff), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 0)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 0)
			},
			wantErr: true,
		},
		{
			name: "Should update stage to Manual Intervention if GetActivationCoolOff activity returned failed activity status",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.CreateDomainEntities), mock.Anything, defaultCreateDomainEntitiesReq).
					Return(createDomainEntitiesSuccessRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_INITIATED,
				}).
					Return(defaultUpdateRecurringPaymentStatusRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_BLOCKED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&emptyPb.Empty{})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationInitiatedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_PENDING,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&rpSignalPb.CreateRecurringPaymentAuthorisationSignal{
						AuthorisationStatus: rpSignalPb.CreateRecurringPaymentAuthorisationSignal_AUTHORISATION_STATUS_SUCCESS,
					})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationCompletedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_INITIATED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_AUTHORISED,
				}).
					Return(defaultUpdateRecurringPaymentStatusRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.GetActivationCoolOff), mock.Anything, defaultGetActivationCoolOffReq).
					Return(getActivationCoolOffFailedRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_MANUAL_INTERVENTION,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 3)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 4)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 2)
				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.GetActivationCoolOff), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 0)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 0)
			},
			wantErr: true,
		},

		// Testing Vendor Activation stage
		{
			name: "Should update stage to Manual Intervention if Enquire Domain Activation Status returned error",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.CreateDomainEntities), mock.Anything, defaultCreateDomainEntitiesReq).
					Return(createDomainEntitiesSuccessRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_INITIATED,
				}).
					Return(defaultUpdateRecurringPaymentStatusRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_BLOCKED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&emptyPb.Empty{})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationInitiatedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_PENDING,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&rpSignalPb.CreateRecurringPaymentAuthorisationSignal{
						AuthorisationStatus: rpSignalPb.CreateRecurringPaymentAuthorisationSignal_AUTHORISATION_STATUS_SUCCESS,
					})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationCompletedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_INITIATED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_AUTHORISED,
				}).
					Return(defaultUpdateRecurringPaymentStatusRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.GetActivationCoolOff), mock.Anything, defaultGetActivationCoolOffReq).
					Return(getActivationCoolOffSuccessRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainActivation),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.EnquireDomainActivationStatus), mock.Anything, defaultEnquireDomainActivationStatusReq).
					Return(nil, temporalPermanentErr)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_MANUAL_INTERVENTION,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainActivation),
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 4)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 5)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 3)
				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.GetActivationCoolOff), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireDomainActivationStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 0)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 0)

			},
			wantErr: true,
		},
		{
			name: "Should update stage to Manual Intervention if Enquire Domain Activation Status returned unexpected activation status",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.CreateDomainEntities), mock.Anything, defaultCreateDomainEntitiesReq).
					Return(createDomainEntitiesSuccessRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_INITIATED,
				}).
					Return(defaultUpdateRecurringPaymentStatusRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_BLOCKED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&emptyPb.Empty{})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationInitiatedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_PENDING,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&rpSignalPb.CreateRecurringPaymentAuthorisationSignal{
						AuthorisationStatus: rpSignalPb.CreateRecurringPaymentAuthorisationSignal_AUTHORISATION_STATUS_SUCCESS,
					})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationCompletedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_INITIATED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_AUTHORISED,
				}).
					Return(defaultUpdateRecurringPaymentStatusRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.GetActivationCoolOff), mock.Anything, defaultGetActivationCoolOffReq).
					Return(getActivationCoolOffSuccessRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainActivation),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.EnquireDomainActivationStatus), mock.Anything, defaultEnquireDomainActivationStatusReq).
					Return(enquireDomainActivationStatusUnexpectedRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_MANUAL_INTERVENTION,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainActivation),
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 4)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 5)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 3)
				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 2)
				env.AssertNumberOfCalls(t, string(rpNs.GetActivationCoolOff), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireDomainActivationStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 0)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 0)

			},
			wantErr: true,
		},
		{
			name: "Should update stage to Failed if Enquire Domain Activation Status returned Activation Status Failed",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).Return(nil)

				env.OnActivity(string(rpNs.CreateDomainEntities), mock.Anything, defaultCreateDomainEntitiesReq).
					Return(createDomainEntitiesSuccessRes, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_INITIATED,
				}).Return(defaultUpdateRecurringPaymentStatusRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_BLOCKED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).Return(nil)

				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&emptyPb.Empty{})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationInitiatedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_PENDING,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).Return(nil)

				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&rpSignalPb.CreateRecurringPaymentAuthorisationSignal{
						AuthorisationStatus: rpSignalPb.CreateRecurringPaymentAuthorisationSignal_AUTHORISATION_STATUS_SUCCESS,
					})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationCompletedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_INITIATED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_AUTHORISED,
				}).Return(defaultUpdateRecurringPaymentStatusRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
				}).Return(nil)

				env.OnActivity(string(rpNs.GetActivationCoolOff), mock.Anything, defaultGetActivationCoolOffReq).
					Return(getActivationCoolOffSuccessRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainActivation),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.EnquireDomainActivationStatus), mock.Anything, defaultEnquireDomainActivationStatusReq).
					Return(enquireDomainActivationStatusFailedRes, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_AUTHORISED,
					NextStatus:         rpPb.RecurringPaymentState_FAILED,
				}).Return(defaultUpdateRecurringPaymentStatusRes, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
					CurrentStatus:            rpPb.ActionState_ACTION_CREATED,
					NextStatus:               rpPb.ActionState_ACTION_FAILURE,
					ActionDetailedStatusInfo: enquireDomainActivationStatusFailedRes.GetActionDetailedStatusInfo(),
				}).Return(defaultUpdateRecurringPaymentActionStatusRes, nil)

				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				}).Return(defaultPublishRecurringPaymentActionUpdateRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_FAILED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainActivation),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostFailureProcessing),
				}).Return(nil)

				env.OnActivity(string(rpNs.SendStatusNotification), mock.Anything, defaultSendStatusNotificationReq).
					Return(sendStatusNotificationSuccessRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostFailureProcessing),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 5)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 6)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 5)

				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 3)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 1)
				env.AssertNumberOfCalls(t, string(rpNs.GetActivationCoolOff), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireDomainActivationStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 1)
			},
			wantErr: false,
		},

		// Testing Send Notification stage
		{
			name: "Should update stage to Failed if Send Notification status activity returned err",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).Return(nil)

				env.OnActivity(string(rpNs.CreateDomainEntities), mock.Anything, defaultCreateDomainEntitiesReq).
					Return(createDomainEntitiesSuccessRes, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_INITIATED,
				}).Return(defaultUpdateRecurringPaymentStatusRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_BLOCKED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).Return(nil)

				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&emptyPb.Empty{})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationInitiatedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_PENDING,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).Return(nil)

				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&rpSignalPb.CreateRecurringPaymentAuthorisationSignal{
						AuthorisationStatus: rpSignalPb.CreateRecurringPaymentAuthorisationSignal_AUTHORISATION_STATUS_SUCCESS,
					})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationCompletedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_INITIATED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_AUTHORISED,
				}).Return(defaultUpdateRecurringPaymentStatusRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
				}).Return(nil)

				env.OnActivity(string(rpNs.GetActivationCoolOff), mock.Anything, defaultGetActivationCoolOffReq).
					Return(getActivationCoolOffSuccessRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainActivation),
				}).Return(nil)

				env.OnActivity(string(rpNs.EnquireDomainActivationStatus), mock.Anything, defaultEnquireDomainActivationStatusReq).
					Return(enquireDomainActivationStatusSuccessRes, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_AUTHORISED,
					NextStatus:         rpPb.RecurringPaymentState_ACTIVATED,
				}).Return(defaultUpdateRecurringPaymentStatusRes, nil)

				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
					CurrentStatus:            rpPb.ActionState_ACTION_CREATED,
					NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
				}).Return(defaultUpdateRecurringPaymentActionStatusRes, nil)

				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				}).Return(defaultPublishRecurringPaymentActionUpdateRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainActivation),
				}).Return(nil)

				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).Return(nil)

				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostSuccessProcessing),
				}).Return(nil)

				env.OnActivity(string(rpNs.SendStatusNotification), mock.Anything, defaultSendStatusNotificationReq).
					Return(nil, temporalPermanentErr)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_MANUAL_INTERVENTION,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostSuccessProcessing),
				}).Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 5)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 6)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 4)

				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 3)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 1)
				env.AssertNumberOfCalls(t, string(rpNs.GetActivationCoolOff), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireDomainActivationStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 1)
			},
			wantErr: true,
		},
		{
			name: "Should update stage to Failed if Send Notification status activity returned unexpected activity status",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.CreateDomainEntities), mock.Anything, defaultCreateDomainEntitiesReq).
					Return(createDomainEntitiesSuccessRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_INITIATED,
				}).
					Return(defaultUpdateRecurringPaymentStatusRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_BLOCKED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&emptyPb.Empty{})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationInitiatedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_PENDING,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&rpSignalPb.CreateRecurringPaymentAuthorisationSignal{
						AuthorisationStatus: rpSignalPb.CreateRecurringPaymentAuthorisationSignal_AUTHORISATION_STATUS_SUCCESS,
					})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationCompletedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_INITIATED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_AUTHORISED,
				}).
					Return(defaultUpdateRecurringPaymentStatusRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.GetActivationCoolOff), mock.Anything, defaultGetActivationCoolOffReq).
					Return(getActivationCoolOffSuccessRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainActivation),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.EnquireDomainActivationStatus), mock.Anything, defaultEnquireDomainActivationStatusReq).
					Return(enquireDomainActivationStatusSuccessRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_AUTHORISED,
					NextStatus:         rpPb.RecurringPaymentState_ACTIVATED,
				}).
					Return(defaultUpdateRecurringPaymentStatusRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
					CurrentStatus:            rpPb.ActionState_ACTION_CREATED,
					NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
				}).
					Return(defaultUpdateRecurringPaymentActionStatusRes, nil)

				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				}).Return(defaultPublishRecurringPaymentActionUpdateRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainActivation),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostSuccessProcessing),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.SendStatusNotification), mock.Anything, defaultSendStatusNotificationReq).
					Return(sendStatusNotificationUnexpectedRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_MANUAL_INTERVENTION,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostSuccessProcessing),
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 5)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 6)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 4)

				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 3)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 1)
				env.AssertNumberOfCalls(t, string(rpNs.GetActivationCoolOff), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireDomainActivationStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 1)
			},
			wantErr: true,
		},
		{
			name: "Should update stage to Failed if Send Notification status activity returned unexpected activity status",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.CreateDomainEntities), mock.Anything, defaultCreateDomainEntitiesReq).
					Return(createDomainEntitiesSuccessRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_INITIATED,
				}).
					Return(defaultUpdateRecurringPaymentStatusRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_BLOCKED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&emptyPb.Empty{})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationInitiatedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_PENDING,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&rpSignalPb.CreateRecurringPaymentAuthorisationSignal{
						AuthorisationStatus: rpSignalPb.CreateRecurringPaymentAuthorisationSignal_AUTHORISATION_STATUS_SUCCESS,
					})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationCompletedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_INITIATED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_AUTHORISED,
				}).
					Return(defaultUpdateRecurringPaymentStatusRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.GetActivationCoolOff), mock.Anything, defaultGetActivationCoolOffReq).
					Return(getActivationCoolOffSuccessRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainActivation),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.EnquireDomainActivationStatus), mock.Anything, defaultEnquireDomainActivationStatusReq).
					Return(enquireDomainActivationStatusSuccessRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_AUTHORISED,
					NextStatus:         rpPb.RecurringPaymentState_ACTIVATED,
				}).
					Return(defaultUpdateRecurringPaymentStatusRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
					CurrentStatus:            rpPb.ActionState_ACTION_CREATED,
					NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
				}).
					Return(defaultUpdateRecurringPaymentActionStatusRes, nil)

				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				}).Return(defaultPublishRecurringPaymentActionUpdateRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainActivation),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostSuccessProcessing),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.SendStatusNotification), mock.Anything, defaultSendStatusNotificationReq).
					Return(sendStatusNotificationUnexpectedRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_MANUAL_INTERVENTION,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostSuccessProcessing),
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 5)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 6)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 4)
				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 3)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 1)
				env.AssertNumberOfCalls(t, string(rpNs.GetActivationCoolOff), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireDomainActivationStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 1)
			},
			wantErr: true,
		},

		// All process successful
		{
			name: "Should update stage to success if Send Notification status success",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, defaultWorkflowProcessingParamsV2Req).
					Return(defaultWorkflowProcessingParamsV2Res, nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.CreateDomainEntities), mock.Anything, defaultCreateDomainEntitiesReq).
					Return(createDomainEntitiesSuccessRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_INITIATED,
				}).
					Return(defaultUpdateRecurringPaymentStatusRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainEntityCreation),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_BLOCKED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&emptyPb.Empty{})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationInitiatedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_PENDING,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.RegisterDelayedCallback(func() {
					signalPayload, err := protojson.Marshal(&rpSignalPb.CreateRecurringPaymentAuthorisationSignal{
						AuthorisationStatus: rpSignalPb.CreateRecurringPaymentAuthorisationSignal_AUTHORISATION_STATUS_SUCCESS,
					})
					if err != nil {
						t.Errorf("error marshalling signal payload: %v", err)
					}
					err = env.SignalWorkflowByID(defaultWorkflowID, string(rpNs.CreateRecurringPaymentV1AuthorisationCompletedSignal), signalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, delayOneMinute)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_INITIATED,
					NextStatus:         rpPb.RecurringPaymentState_CREATION_AUTHORISED,
				}).
					Return(defaultUpdateRecurringPaymentStatusRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentAuthorisation),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.GetActivationCoolOff), mock.Anything, defaultGetActivationCoolOffReq).
					Return(getActivationCoolOffSuccessRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainActivation),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.EnquireDomainActivationStatus), mock.Anything, defaultEnquireDomainActivationStatusReq).
					Return(enquireDomainActivationStatusSuccessRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentStatusRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
					CurrentStatus:      rpPb.RecurringPaymentState_CREATION_AUTHORISED,
					NextStatus:         rpPb.RecurringPaymentState_ACTIVATED,
				}).
					Return(defaultUpdateRecurringPaymentStatusRes, nil)
				env.OnActivity(string(rpNs.UpdateRecurringPaymentActionStatus), mock.Anything, &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
					CurrentStatus:            rpPb.ActionState_ACTION_CREATED,
					NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
				}).
					Return(defaultUpdateRecurringPaymentActionStatusRes, nil)

				env.OnActivity(string(rpNs.PublishRecurringPaymentActionUpdateEvent), mock.Anything, &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				}).Return(defaultPublishRecurringPaymentActionUpdateRes, nil)

				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentDomainActivation),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_INITIATED,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostSuccessProcessing),
				}).
					Return(nil)
				env.OnActivity(string(rpNs.SendStatusNotification), mock.Anything, defaultSendStatusNotificationReq).
					Return(sendStatusNotificationSuccessRes, nil)
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
					Status:        stagePb.Status_SUCCESSFUL,
					StageEnum:     celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentInitiatePostSuccessProcessing),
				}).
					Return(nil)
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: requestHeaderWithoutClientReqId,
					WfReqId:       defaultWorkflowID,
				}).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.InitiateWorkflowStageV2), 5)
				env.AssertNumberOfCalls(t, string(epifitemporal.UpdateWorkflowStage), 6)
				env.AssertNumberOfCalls(t, string(epifitemporal.PublishWorkflowUpdateEventV2), 5)
				env.AssertNumberOfCalls(t, string(rpNs.CreateDomainEntities), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentStatus), 3)
				env.AssertNumberOfCalls(t, string(rpNs.UpdateRecurringPaymentActionStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.PublishRecurringPaymentActionUpdateEvent), 1)
				env.AssertNumberOfCalls(t, string(rpNs.GetActivationCoolOff), 1)
				env.AssertNumberOfCalls(t, string(rpNs.EnquireDomainActivationStatus), 1)
				env.AssertNumberOfCalls(t, string(rpNs.SendStatusNotification), 1)
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.skip {
				t.Skip()
			}
			env := wts.NewTestWorkflowEnvironment()
			env.RegisterWorkflow(CreateRecurringPaymentV1)
			env.RegisterActivity(&celestialActivityV2.Processor{})
			env.RegisterActivity(&rpActivity.Processor{})

			tt.setupMocks(env)

			env.ExecuteWorkflow(CreateRecurringPaymentV1, &rpPayloadPb.CreateRecurringPaymentV1WorkflowPayload{})

			assert.True(t, env.IsWorkflowCompleted())
			if (env.GetWorkflowError() != nil) != tt.wantErr {
				t.Errorf("CreateRecurringPaymentV1() error = %v, wantErr %v", env.GetWorkflowError(), tt.wantErr)
			}

			tt.setupExpectations(t, env)
			env.AssertExpectations(t)
		})
	}
}
