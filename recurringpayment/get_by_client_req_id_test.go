package recurringpayment

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"

	recurringPaymentPb "github.com/epifi/gamma/api/recurringpayment"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
)

func TestService_GetRecurringPaymentDetailsByClientReqId(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockRecurringPaymentActionsDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)

	mockRecurringPaymentVendorDetailsDao := daoMocks.NewMockRecurringPaymentsVendorDetailsDao(ctr)
	svc := NewService(nil, nil, nil, mockRecurringPaymentActionsDao, nil, nil, nil, nil, nil, conf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, mockRecurringPaymentVendorDetailsDao, nil, nil, nil, nil)

	type mockGetByClientRequestId struct {
		enable                 bool
		clientReqId            string
		recurringPaymentAction *recurringPaymentPb.RecurringPaymentsAction
		err                    error
	}

	tests := []struct {
		name                     string
		req                      *recurringPaymentPb.GetRecurringPaymentDetailsByClientReqIdRequest
		mockGetByClientRequestId mockGetByClientRequestId
		res                      *recurringPaymentPb.GetRecurringPaymentDetailsByClientReqIdResponse
		wantErr                  bool
	}{
		{
			name: "Got the recurring payment action successfully",
			req: &recurringPaymentPb.GetRecurringPaymentDetailsByClientReqIdRequest{
				ClientReqId: "client-req-1",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:                 true,
				clientReqId:            "client-req-1",
				recurringPaymentAction: &recurringPaymentPb.RecurringPaymentsAction{RecurringPaymentId: "rp-id-1"},
			},
			res: &recurringPaymentPb.GetRecurringPaymentDetailsByClientReqIdResponse{
				Status:             rpc.StatusOk(),
				RecurringPaymentId: "rp-id-1",
			},
		},
		{
			name: "recurring payment action not found",
			req: &recurringPaymentPb.GetRecurringPaymentDetailsByClientReqIdRequest{
				ClientReqId: "client-req-1",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			res: &recurringPaymentPb.GetRecurringPaymentDetailsByClientReqIdResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByClientRequestId.enable {
				mockRecurringPaymentActionsDao.EXPECT().GetByClientRequestId(context.Background(), tt.mockGetByClientRequestId.clientReqId, true).
					Return(tt.mockGetByClientRequestId.recurringPaymentAction, tt.mockGetByClientRequestId.err)
			}

			got, err := svc.GetRecurringPaymentDetailsByClientReqId(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRecurringPaymentDetailsByClientReqId() got err: %v, wantErr: %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.res) {
				t.Errorf("GetRecurringPaymentDetailsByClientReqId() got :%v want :%v", tt.req, tt.res)
				return
			}
		})
	}

}
