// nolint: goimports
package activity

import (
	"context"
	"errors"
	"fmt"

	"github.com/epifi/be-common/pkg/epificontext"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
)

func (p *Processor) CreateRecurringPayment(ctx context.Context, req *rpActivityPb.CreateRecurringPaymentRequest) (*rpActivityPb.CreateRecurringPaymentResponse, error) {
	res := &rpActivityPb.CreateRecurringPaymentResponse{}

	// Required for Idempotency check
	recurringPayment, err := p.recurringPaymentDao.GetByExternalId(ctx, req.GetRecurringPayment().GetExternalId())
	if err == nil {
		logger.Info(ctx, fmt.Sprintf("recurring payment with external id: %s already exists", req.GetRecurringPayment().GetExternalId()))
		res.RecurringPayment = recurringPayment
		return res, nil
	}
	if !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "failed to fetch recurring payment by externalId", zap.Error(err))
		return nil, fmt.Errorf("unable to fetch recurring payment by externalId %w", err)
	}

	// Creating recurring payment
	recurringPayment, err = p.recurringPaymentDao.Create(ctx, req.GetRecurringPayment(), epificontext.OwnershipFromContext(ctx))
	if err != nil {
		logger.Error(ctx, "recurring payment creation failed", zap.Error(err))
		return nil, fmt.Errorf("failed to create recurring payment %w", err)
	}

	res.RecurringPayment = recurringPayment
	return res, nil
}
