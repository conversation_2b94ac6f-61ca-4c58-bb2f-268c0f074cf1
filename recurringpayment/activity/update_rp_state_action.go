package activity

import (
	"context"
	"fmt"

	errorsPkg "github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
)

func (p *Processor) UpdateRecurringPaymentStateAndAction(ctx context.Context, req *rpActivityPb.UpdateRecurringPaymentStateAndActionRequest) (*rpActivityPb.UpdateRecurringPaymentStateAndActionResponse, error) {
	res := &rpActivityPb.UpdateRecurringPaymentStateAndActionResponse{}
	lg := activity.GetLogger(ctx)

	recurringPaymentAction, err := p.recurringPaymentsActionDao.GetByClientRequestId(ctx, req.GetRequestHeader().GetClientReqId(), false)
	if err != nil {
		lg.Error("error in fetching recurring payment action ", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return nil, errorsPkg.Wrap(epifierrors.ErrTransient, err.Error())
	}
	recurringPayment, err := p.recurringPaymentDao.GetById(ctx, recurringPaymentAction.GetRecurringPaymentId())
	if err != nil {
		lg.Error("error in fetching recurring payment by id", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentAction.GetRecurringPaymentId()))
		return nil, errorsPkg.Wrap(epifierrors.ErrTransient, err.Error())
	}

	err = p.idempotentTxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		if recurringPayment.GetState() != req.GetStateToUpdate() {
			err = p.recurringPaymentDao.UpdateAndChangeStatus(txnCtx, recurringPayment, nil,
				recurringPayment.GetState(), req.GetStateToUpdate())
			if err != nil {
				return fmt.Errorf("error while updating recurring payment state for recurring payment %w", err)
			}
		}
		if recurringPaymentAction.GetState() != req.GetActionStateToUpdate() {
			err = p.recurringPaymentsActionDao.UpdateAndChangeStatus(txnCtx, recurringPaymentAction, nil,
				recurringPaymentAction.GetState(), req.GetActionStateToUpdate())
			if err != nil {
				return fmt.Errorf("error in updating recurring payment action %w", err)
			}
		}
		return nil
	})
	if err != nil {
		lg.Error("error in updating recurring payment state and action ", zap.Error(err))
		return nil, errorsPkg.Wrap(epifierrors.ErrTransient, err.Error())
	}

	lg.Info("recurring payment state and action updated successfully", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentAction.GetRecurringPaymentId()),
		zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))

	return res, nil
}
