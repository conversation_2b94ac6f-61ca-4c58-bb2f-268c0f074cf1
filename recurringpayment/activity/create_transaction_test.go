package activity_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	paymentMocks "github.com/epifi/gamma/api/order/payment/mocks"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	moneyPb "github.com/epifi/be-common/pkg/money"
	testPkg "github.com/epifi/be-common/pkg/test"
)

func TestProcessor_CreateTransaction(t *testing.T) {

	const (
		transactionId = "transaction-id"
		piForm        = "pi-from"
		piTo          = "pi-to"
		utr           = "transaction-utr"
		orderId       = "orderId"
	)

	transaction := &paymentPb.Transaction{
		Id:              transactionId,
		PiFrom:          piForm,
		PiTo:            piTo,
		Utr:             utr,
		PartnerBank:     commonvgpb.Vendor_AXIS_BANK,
		Amount:          moneyPb.AmountINR(200).GetPb(),
		Status:          paymentPb.TransactionStatus_SUCCESS,
		PaymentProtocol: paymentPb.PaymentProtocol_ENACH,
		Ownership:       commontypes.Ownership_EPIFI_TECH,
		OrderId:         orderId,
	}

	getTransactionRequest := &paymentPb.GetTransactionRequest{
		Identifier: &paymentPb.GetTransactionRequest_Utr{
			Utr: utr,
		},
		GetReqInfo: false,
	}

	transactionRequestParams := &rpActivityPb.CreateTransactionRequest_TransactionRequestParams{
		PiFrom:          piForm,
		PiTo:            piTo,
		Amount:          moneyPb.AmountINR(200).GetPb(),
		OrderId:         orderId,
		PaymentProtocol: paymentPb.PaymentProtocol_ENACH,
		Status:          paymentPb.TransactionStatus_SUCCESS,
		Utr:             utr,
		Ownership:       commontypes.Ownership_EPIFI_TECH,
	}

	createTransactionRequest := &paymentPb.CreateTransactionRequest{
		PiFrom:          piForm,
		PiTo:            piTo,
		Amount:          moneyPb.AmountINR(200).GetPb(),
		OrderId:         orderId,
		PaymentProtocol: paymentPb.PaymentProtocol_ENACH,
		Status:          paymentPb.TransactionStatus_SUCCESS,
		Utr:             utr,
		Ownership:       commontypes.Ownership_EPIFI_TECH,
	}

	createTransactionResponse := &paymentPb.CreateTransactionResponse{
		Status:      rpc.StatusOk(),
		Transaction: transaction,
	}

	tests := []struct {
		name       string
		req        *rpActivityPb.CreateTransactionRequest
		setupMocks func(mockPaymentClient *paymentMocks.MockPaymentClient)
		want       *rpActivityPb.CreateTransactionResponse
		wantErr    bool
		assertErr  func(err error) bool
	}{
		{
			name: "should return transaction successfully when transaction is already created (Idempotency check)",
			req: &rpActivityPb.CreateTransactionRequest{
				TransactionRequestParams: transactionRequestParams,
			},
			setupMocks: func(mockPaymentClient *paymentMocks.MockPaymentClient) {
				mockPaymentClient.EXPECT().GetTransaction(gomock.Any(), getTransactionRequest).Return(&paymentPb.GetTransactionResponse{
					Status:      rpc.StatusOk(),
					Transaction: transaction,
				}, nil)
			},
			want: &rpActivityPb.CreateTransactionResponse{
				Transaction: transaction,
			},
			wantErr: false,
		},
		{
			name: "should return retryable error when rpc returns error while fetching the transaction",
			req: &rpActivityPb.CreateTransactionRequest{
				TransactionRequestParams: transactionRequestParams,
			},
			setupMocks: func(mockPaymentClient *paymentMocks.MockPaymentClient) {
				mockPaymentClient.EXPECT().GetTransaction(gomock.Any(), getTransactionRequest).Return(nil, epifierrors.ErrInvalidArgument)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return retryable error when transaction doesn't exists and transaction creation fails",
			req: &rpActivityPb.CreateTransactionRequest{
				TransactionRequestParams: transactionRequestParams,
			},
			setupMocks: func(mockPaymentClient *paymentMocks.MockPaymentClient) {
				mockPaymentClient.EXPECT().GetTransaction(gomock.Any(), getTransactionRequest).Return(&paymentPb.GetTransactionResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockPaymentClient.EXPECT().CreateTransaction(gomock.Any(), testPkg.NewProtoArgMatcher(createTransactionRequest)).Return(nil, epifierrors.ErrTransient)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return retryable error when transaction doesn't exists and transaction creation fails due to CreateTransaction rpc error response code",
			req: &rpActivityPb.CreateTransactionRequest{
				TransactionRequestParams: transactionRequestParams,
			},
			setupMocks: func(mockPaymentClient *paymentMocks.MockPaymentClient) {
				mockPaymentClient.EXPECT().GetTransaction(gomock.Any(), getTransactionRequest).Return(&paymentPb.GetTransactionResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockPaymentClient.EXPECT().CreateTransaction(gomock.Any(), testPkg.NewProtoArgMatcher(createTransactionRequest)).Return(&paymentPb.CreateTransactionResponse{
					Status: rpc.StatusInternalWithDebugMsg("error while creating the transaction"),
				}, nil)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return transaction successfully when transaction doesn't exists and transaction created successfully",
			req:  &rpActivityPb.CreateTransactionRequest{TransactionRequestParams: transactionRequestParams},
			setupMocks: func(mockPaymentClient *paymentMocks.MockPaymentClient) {
				mockPaymentClient.EXPECT().GetTransaction(gomock.Any(), getTransactionRequest).Return(&paymentPb.GetTransactionResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockPaymentClient.EXPECT().CreateTransaction(gomock.Any(), testPkg.NewProtoArgMatcher(createTransactionRequest)).Return(createTransactionResponse, nil)
			},
			want:    &rpActivityPb.CreateTransactionResponse{Transaction: transaction},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			act, md, assertTest := newProcessorWithMocks(t)
			defer assertTest()
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(act)

			tt.setupMocks(md.paymentClient)

			result := &rpActivityPb.CreateTransactionResponse{}
			got, err := env.ExecuteActivity(rpNs.CreateTransaction, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("CreateTransaction() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("CreateTransaction() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("CreateTransaction() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("CreateTransaction() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
