// nolint: goimports
package activity

import (
	"context"
	"errors"
	"fmt"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"go.uber.org/zap"

	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/gamma/recurringpayment/internal/domainexecutionprocessor"
)

// InitiateExecutionAtDomain is responsible for initiating execution at respective domain
func (p *Processor) InitiateExecutionAtDomain(ctx context.Context, req *rpActivityPb.InitiateExecutionAtDomainRequest) (*rpActivityPb.InitiateExecutionAtDomainResponse, error) {
	// fetch the recurring payment by id to identitfy the type
	recurringPayment, recurringPaymentErr := p.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	if recurringPaymentErr != nil {
		logger.Error(ctx, "failed to fetch recurring payment by id", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		return nil, fmt.Errorf("failed to fetch recurring payment by id, err: %s, %w", recurringPaymentErr, epifierrors.ErrTransient)
	}
	ctx = epificontext.WithOwnership(ctx, recurringPayment.GetEntityOwnership())
	// fetch the respective processor for the domain
	domainExecutionProcessor, domainExecutionProcessorErr := p.domainExecutionProcessorFactory.GetProcessor(recurringPayment.GetType(), recurringPayment.GetPaymentRoute())

	switch {
	case errors.Is(domainExecutionProcessorErr, epifierrors.ErrInvalidArgument):
		return nil, fmt.Errorf("no processor found for the given recurring payment,err: %s , %w", domainExecutionProcessorErr, epifierrors.ErrPermanent)
	case domainExecutionProcessorErr != nil:
		return nil, fmt.Errorf("failed to fetch the domain execution processor, err: %s, %w", domainExecutionProcessorErr, epifierrors.ErrTransient)
	}

	// fetch the recurring payment action by id to get execution amount
	recurringPaymentAction, recurringPaymentActionErr := p.recurringPaymentsActionDao.GetById(ctx, req.GetRecurringPaymentActionId())
	if recurringPaymentActionErr != nil {
		logger.Error(ctx, "failed to fetch recurring payment action by id", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, req.GetRecurringPaymentActionId()))
		return nil, fmt.Errorf("failed to fetch recurring payment action by id, err: %s, %w", recurringPaymentActionErr, epifierrors.ErrTransient)
	}

	// initiate execution at domain
	initiateRes, domainInitExecutionErr := domainExecutionProcessor.InitiateExecution(ctx, &domainexecutionprocessor.InitiateExecutionAtDomainReq{
		RecurringPaymentId:       req.GetRecurringPaymentId(),
		RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
		ExecutionAmount:          recurringPaymentAction.GetActionMetadata().GetExecuteActionMetadate().GetAmount(),
		Payload:                  req.GetRecurringPaymentTypeSpecificPayload(),
	})

	if domainInitExecutionErr != nil {
		return nil, fmt.Errorf("failed to initiate execution at domain, err: %s , %w", domainInitExecutionErr, epifierrors.ErrTransient)
	}
	if initiateRes.GetRecurringPaymentActionStatus().IsActionFailed() {
		logger.Error(ctx, "failure in initiating execution at domain due to business logic failure", zap.String(logger.REASON, initiateRes.GetRecurringPaymentActionStatus().GetErrorDescription()), zap.String(logger.RECURRING_PAYMENT_ACTION_ID, req.GetRecurringPaymentActionId()))
		return &rpActivityPb.InitiateExecutionAtDomainResponse{
			DetailedStatus: initiateRes.GetRecurringPaymentActionStatus(),
		}, nil
	}

	return &rpActivityPb.InitiateExecutionAtDomainResponse{
		DetailedStatus: initiateRes.GetRecurringPaymentActionStatus(),
	}, nil
}
