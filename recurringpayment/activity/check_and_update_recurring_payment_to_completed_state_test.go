package activity

import (
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	rpDao "github.com/epifi/gamma/recurringpayment/dao"
	rpDaoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
)

func TestProcessor_CheckAndUpdateRecurringPaymentToCompletedState(t *testing.T) {
	const (
		defaultRecurringPaymentId       = "default_test_recurring_payment_id"
		defaultRecurringPaymentActionId = "default_test_recurring_payment_action_id"
	)
	var (
		recurringPaymentToBeUpdated = &rpPb.RecurringPayment{
			Id: defaultRecurringPaymentId,
			Interval: &types.Interval{
				EndTime: timestamppb.New(time.Now().Add(time.Hour * 10)),
			},
			RecurrenceRule: &rpPb.RecurrenceRule{
				AllowedFrequency: rpPb.AllowedFrequency_ONE_TIME,
			},
		}
	)

	type args struct {
		req        *rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateRequest
		setupMocks func(mockRpActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockRpDao *rpDaoMocks.MockRecurringPaymentDao)
	}
	tests := []struct {
		name      string
		args      args
		want      *rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateResponse
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "should return successfully (recurring payment already in completed state)",
			args: args{
				req: &rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateRequest{
					RecurringPaymentId: "completed-recurring-payment-id",
				},
				setupMocks: func(mockRpActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockRpDao *rpDaoMocks.MockRecurringPaymentDao) {
					mockRpDao.EXPECT().GetById(gomock.Any(), "completed-recurring-payment-id").Return(&rpPb.RecurringPayment{
						Id:    "completed-recurring-payment-id",
						State: rpPb.RecurringPaymentState_COMPLETED,
					}, nil)
				},
			},
			want: &rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateResponse{},
		},
		{
			name: "should return transient error (failed to fetch recurring payment)",
			args: args{
				req: &rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
				},
				setupMocks: func(mockRpActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockRpDao *rpDaoMocks.MockRecurringPaymentDao) {
					mockRpDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(nil, fmt.Errorf("failed to fetch recurring payment by id"))
				},
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should not update recurring payment (condition not met [end date not reached and frequency not of type one time])",
			args: args{
				req: &rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
				},
				setupMocks: func(mockRpActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockRpDao *rpDaoMocks.MockRecurringPaymentDao) {
					mockRpDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(&rpPb.RecurringPayment{
						Id: defaultRecurringPaymentId,
						Interval: &types.Interval{
							EndTime: timestamppb.New(time.Now().Add(10 * time.Hour)),
						},
					}, nil)
				},
			},
			want: &rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateResponse{},
		},
		{
			name: "should return transint failure (failed to fetch the action)",
			args: args{
				req: &rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
				},
				setupMocks: func(mockRpActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockRpDao *rpDaoMocks.MockRecurringPaymentDao) {
					mockRpDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(&rpPb.RecurringPayment{
						Id: defaultRecurringPaymentId,
						Interval: &types.Interval{
							EndTime: timestamppb.New(time.Now().Add(time.Hour * 10)),
						},
						RecurrenceRule: &rpPb.RecurrenceRule{
							AllowedFrequency: rpPb.AllowedFrequency_ONE_TIME,
						},
					}, nil)
					mockRpActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), defaultRecurringPaymentId, gomock.AssignableToTypeOf(rpDao.WithActionsFilter([]rpPb.Action{rpPb.Action_EXECUTE})), gomock.AssignableToTypeOf(rpDao.WithActionStateFilter([]rpPb.ActionState{rpPb.ActionState_ACTION_SUCCESS})), gomock.AssignableToTypeOf(rpDao.WithOrderByCreatedAt(true))).
						Return(nil, errors.New("failed to fetch the action"))

				},
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return successfully withot updating (no action found)",
			args: args{
				req: &rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
				},
				setupMocks: func(mockRpActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockRpDao *rpDaoMocks.MockRecurringPaymentDao) {
					mockRpDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(recurringPaymentToBeUpdated, nil)
					mockRpActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), defaultRecurringPaymentId, gomock.AssignableToTypeOf(rpDao.WithActionsFilter([]rpPb.Action{rpPb.Action_EXECUTE})), gomock.AssignableToTypeOf(rpDao.WithActionStateFilter([]rpPb.ActionState{rpPb.ActionState_ACTION_SUCCESS})), gomock.AssignableToTypeOf(rpDao.WithOrderByCreatedAt(true))).
						Return(nil, epifierrors.ErrRecordNotFound)
				},
			},
			want: &rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateResponse{},
		},
		{
			name: "should return permanent failure (no rows effected post updation)",
			args: args{
				req: &rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
				},
				setupMocks: func(mockRpActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockRpDao *rpDaoMocks.MockRecurringPaymentDao) {
					mockRpDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(recurringPaymentToBeUpdated, nil)
					mockRpActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), defaultRecurringPaymentId, gomock.AssignableToTypeOf(rpDao.WithActionsFilter([]rpPb.Action{rpPb.Action_EXECUTE})), gomock.AssignableToTypeOf(rpDao.WithActionStateFilter([]rpPb.ActionState{rpPb.ActionState_ACTION_SUCCESS})), gomock.AssignableToTypeOf(rpDao.WithOrderByCreatedAt(true))).
						Return([]*rpPb.RecurringPaymentsAction{
							{
								Id: defaultRecurringPaymentActionId,
							},
						}, nil)
					mockRpDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPaymentToBeUpdated, []rpPb.RecurringPaymentFieldMask{rpPb.RecurringPaymentFieldMask_STATE}, rpPb.RecurringPaymentState_ACTIVATED, rpPb.RecurringPaymentState_COMPLETED).
						Return(epifierrors.ErrNoRowsAffected)
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "should return transient failure (failed to update)",
			args: args{
				req: &rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
				},
				setupMocks: func(mockRpActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockRpDao *rpDaoMocks.MockRecurringPaymentDao) {
					mockRpDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(recurringPaymentToBeUpdated, nil)
					mockRpActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), defaultRecurringPaymentId, gomock.AssignableToTypeOf(rpDao.WithActionsFilter([]rpPb.Action{rpPb.Action_EXECUTE})), gomock.AssignableToTypeOf(rpDao.WithActionStateFilter([]rpPb.ActionState{rpPb.ActionState_ACTION_SUCCESS})), gomock.AssignableToTypeOf(rpDao.WithOrderByCreatedAt(true))).
						Return([]*rpPb.RecurringPaymentsAction{
							{
								Id: defaultRecurringPaymentActionId,
							},
						}, nil)
					mockRpDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPaymentToBeUpdated, []rpPb.RecurringPaymentFieldMask{rpPb.RecurringPaymentFieldMask_STATE}, rpPb.RecurringPaymentState_ACTIVATED, rpPb.RecurringPaymentState_COMPLETED).
						Return(errors.New("failed to update the recurring payment to completed state"))
				},
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should successfully update recurring payment to completed state",
			args: args{
				req: &rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateRequest{
					RecurringPaymentId: defaultRecurringPaymentId,
				},
				setupMocks: func(mockRpActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockRpDao *rpDaoMocks.MockRecurringPaymentDao) {
					mockRpDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(recurringPaymentToBeUpdated, nil)
					mockRpActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), defaultRecurringPaymentId, gomock.AssignableToTypeOf(rpDao.WithActionsFilter([]rpPb.Action{rpPb.Action_EXECUTE})), gomock.AssignableToTypeOf(rpDao.WithActionStateFilter([]rpPb.ActionState{rpPb.ActionState_ACTION_SUCCESS})), gomock.AssignableToTypeOf(rpDao.WithOrderByCreatedAt(true))).
						Return([]*rpPb.RecurringPaymentsAction{
							{
								Id: defaultRecurringPaymentActionId,
							},
						}, nil)
					mockRpDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPaymentToBeUpdated, []rpPb.RecurringPaymentFieldMask{rpPb.RecurringPaymentFieldMask_STATE}, rpPb.RecurringPaymentState_ACTIVATED, rpPb.RecurringPaymentState_COMPLETED).
						Return(nil)
				},
			},
			want: &rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateResponse{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockRpDao := rpDaoMocks.NewMockRecurringPaymentDao(ctr)
			mockRpActionDao := rpDaoMocks.NewMockRecurringPaymentsActionDao(ctr)
			p := &Processor{
				recurringPaymentDao:        mockRpDao,
				recurringPaymentsActionDao: mockRpActionDao,
			}
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			tt.args.setupMocks(mockRpActionDao, mockRpDao)
			result := &rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateResponse{}
			got, err := env.ExecuteActivity(rpNs.CheckAndUpdateRecurringPaymentToCompletedState, tt.args.req)
			if got != nil {
				typeValueErr := got.Get(&result)
				if typeValueErr != nil {
					t.Errorf("CheckAndUpdateRecurringPaymentToCompletedState() error = %v failed to fetch type value from convertible", typeValueErr)
					return
				}
			}
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckAndUpdateRecurringPaymentToCompletedState() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && !tt.assertErr(err) {
				t.Errorf("CheckAndUpdateRecurringPaymentToCompletedState() got = %v, want %v", err, tt.want)
				return
			}
			if !tt.wantErr && !proto.Equal(result, tt.want) {
				t.Errorf("CheckAndUpdateRecurringPaymentToCompletedState() got = %v, want %v", got, tt.want)
			}
		})
	}
}
