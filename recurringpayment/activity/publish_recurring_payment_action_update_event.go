package activity

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	rpEventPb "github.com/epifi/gamma/api/recurringpayment/events"
)

func (p *Processor) PublishRecurringPaymentActionUpdateEvent(ctx context.Context, req *rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest) (*rpActivityPb.PublishRecurringPaymentActionUpdateEventResponse, error) {
	recurringPaymentAction, err := p.recurringPaymentsActionDao.GetById(ctx, req.GetRecurringPaymentActionId())
	if err != nil {
		logger.Error(ctx, "error while fetching recurring payment action by id", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ACTION_ID, req.GetRecurringPaymentActionId()))
		return nil, fmt.Errorf("error while fetching recurring payment action by id, err: %s, %w", err.Error(), epifierrors.ErrTransient)
	}

	_, err = p.recurringPaymentActionUpdatePublisher.Publish(ctx, &rpEventPb.RecurringPaymentActionUpdateEvent{
		ActionType:            recurringPaymentAction.GetAction(),
		ActionId:              recurringPaymentAction.GetId(),
		ActionClientRequestId: recurringPaymentAction.GetClientRequestId(),
		RecurringPaymentId:    recurringPaymentAction.GetRecurringPaymentId(),
		ActionState:           recurringPaymentAction.GetState(),
		UpdatedAt:             recurringPaymentAction.GetUpdatedAt(),
	})
	if err != nil {
		logger.Error(ctx, "error while publishing the event for recurring payment action", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ACTION_ID, req.GetRecurringPaymentActionId()))
		return nil, fmt.Errorf("error while publishing the event for recurring payment action, err: %s, %w", err.Error(), epifierrors.ErrTransient)
	}
	logger.Info(ctx, "successfully published recurring payment action update event", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, recurringPaymentAction.GetId()), zap.String(logger.STATUS, recurringPaymentAction.GetState().String()))

	return &rpActivityPb.PublishRecurringPaymentActionUpdateEventResponse{}, nil
}
