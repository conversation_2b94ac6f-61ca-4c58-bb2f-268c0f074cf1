package activity

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/money"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	rpDaoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	domainExecutionProcessor "github.com/epifi/gamma/recurringpayment/internal/domainexecutionprocessor"
	domainExecutionProcessorMocks "github.com/epifi/gamma/recurringpayment/internal/domainexecutionprocessor/mocks"
)

func TestProcessor_InitiateExecutionAtDomain(t *testing.T) {
	const (
		defaultRecurringPaymentId       = "default_test_recurring_payment_id"
		defaultRecurringPaymentActionId = "default_test_recurring_payment_action_id"
		defaultActorFrom                = "default_test_actor_from"
		defaultActorTo                  = "default_test_actor_to"
		defaultPiFrom                   = "default_test_pi_from"
		defaultPiTo                     = "default_test_pi_to"
	)
	var (
		defaultRecurringPayment = &rpPb.RecurringPayment{
			Id:          defaultRecurringPaymentId,
			FromActorId: defaultActorFrom,
			ToActorId:   defaultActorTo,
			Type:        rpPb.RecurringPaymentType_ENACH_MANDATES,
			PiFrom:      defaultPiFrom,
			PiTo:        defaultPiTo,
			State:       rpPb.RecurringPaymentState_CREATION_QUEUED,
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		}
		defaultRecurringPaymentAction = &rpPb.RecurringPaymentsAction{
			Id: defaultRecurringPaymentActionId,
			ActionMetadata: &rpPb.ActionMetadata{
				ExecuteActionMetadate: &rpPb.ExecuteActionMetaData{
					Amount: money.AmountINR(12000).GetPb(),
				},
			},
		}
		defaultInitiateExecutionReq = &domainExecutionProcessor.InitiateExecutionAtDomainReq{
			RecurringPaymentId:       defaultRecurringPaymentId,
			RecurringPaymentActionId: defaultRecurringPaymentActionId,
			ExecutionAmount:          defaultRecurringPaymentAction.GetActionMetadata().GetExecuteActionMetadate().GetAmount(),
		}
	)

	type args struct {
		ctx        context.Context
		req        *rpActivityPb.InitiateExecutionAtDomainRequest
		setupMocks func(mockRpDao *rpDaoMocks.MockRecurringPaymentDao, mockRecurringActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockDomainExecutionProcessorFactory *domainExecutionProcessorMocks.MockDomainExecutionProcessorFactory, mockDomainExecutionProcessor *domainExecutionProcessorMocks.MockDomainExecutionProcessor)
	}
	tests := []struct {
		name      string
		args      args
		want      *rpActivityPb.InitiateExecutionAtDomainResponse
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "should return transient failure (error in fetching recurring payment by id)",
			args: args{
				req: &rpActivityPb.InitiateExecutionAtDomainRequest{
					RecurringPaymentId:       defaultRecurringPaymentId,
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				},
				setupMocks: func(mockRpDao *rpDaoMocks.MockRecurringPaymentDao, mockRecurringActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockDomainExecutionProcessorFactory *domainExecutionProcessorMocks.MockDomainExecutionProcessorFactory, mockDomainExecutionProcessor *domainExecutionProcessorMocks.MockDomainExecutionProcessor) {
					mockRpDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(nil, errors.New("failed to fetch recurring payment"))
				},
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return permanent failure (no processor found for the recurring payment type)",
			args: args{
				req: &rpActivityPb.InitiateExecutionAtDomainRequest{
					RecurringPaymentId: "random-recurring-payment-id",
				},
				setupMocks: func(mockRpDao *rpDaoMocks.MockRecurringPaymentDao, mockRecurringActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockDomainExecutionProcessorFactory *domainExecutionProcessorMocks.MockDomainExecutionProcessorFactory, mockDomainExecutionProcessor *domainExecutionProcessorMocks.MockDomainExecutionProcessor) {
					mockRpDao.EXPECT().GetById(gomock.Any(), "random-recurring-payment-id").Return(&rpPb.RecurringPayment{}, nil)
					mockDomainExecutionProcessorFactory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_RECURRING_PAYMENT_TYPE_UNSPECIFIED, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).Return(nil, epifierrors.ErrInvalidArgument)
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "should return transient failure (error while fetching the processor)",
			args: args{
				req: &rpActivityPb.InitiateExecutionAtDomainRequest{
					RecurringPaymentId:       defaultRecurringPaymentId,
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				},
				setupMocks: func(mockRpDao *rpDaoMocks.MockRecurringPaymentDao, mockRecurringActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockDomainExecutionProcessorFactory *domainExecutionProcessorMocks.MockDomainExecutionProcessorFactory, mockDomainExecutionProcessor *domainExecutionProcessorMocks.MockDomainExecutionProcessor) {
					mockRpDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
					mockDomainExecutionProcessorFactory.EXPECT().GetProcessor(defaultRecurringPayment.GetType(), defaultRecurringPayment.GetPaymentRoute()).Return(nil, errors.New("failed to fetch the processor"))
				},
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return transient failure (get recurring payment action dao call failed)",
			args: args{
				req: &rpActivityPb.InitiateExecutionAtDomainRequest{
					RecurringPaymentId:       defaultRecurringPaymentId,
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				},
				setupMocks: func(mockRpDao *rpDaoMocks.MockRecurringPaymentDao, mockRecurringActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockDomainExecutionProcessorFactory *domainExecutionProcessorMocks.MockDomainExecutionProcessorFactory, mockDomainExecutionProcessor *domainExecutionProcessorMocks.MockDomainExecutionProcessor) {
					mockRpDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
					mockDomainExecutionProcessorFactory.EXPECT().GetProcessor(defaultRecurringPayment.GetType(), defaultRecurringPayment.GetPaymentRoute()).Return(mockDomainExecutionProcessor, nil)
					mockRecurringActionDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentActionId).Return(nil, errors.New("failed to fetch recurring payment action from bd"))
				},
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return transient failure (initate domain execution failed due to technical error)",
			args: args{
				req: &rpActivityPb.InitiateExecutionAtDomainRequest{
					RecurringPaymentId:       defaultRecurringPaymentId,
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				},
				setupMocks: func(mockRpDao *rpDaoMocks.MockRecurringPaymentDao, mockRecurringActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockDomainExecutionProcessorFactory *domainExecutionProcessorMocks.MockDomainExecutionProcessorFactory, mockDomainExecutionProcessor *domainExecutionProcessorMocks.MockDomainExecutionProcessor) {
					mockRpDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
					mockDomainExecutionProcessorFactory.EXPECT().GetProcessor(defaultRecurringPayment.GetType(), defaultRecurringPayment.GetPaymentRoute()).Return(mockDomainExecutionProcessor, nil)
					mockRecurringActionDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentActionId).Return(defaultRecurringPaymentAction, nil)
					mockDomainExecutionProcessor.EXPECT().InitiateExecution(gomock.Any(), defaultInitiateExecutionReq).Return(&domainExecutionProcessor.InitiateExecutionAtDomainRes{
						// empty fi status code in action detailed status info indicates that
						// there was no business failure in initiating the execution.
						RecurringPaymentActionStatus: &rpPb.ActionDetailedStatusInfo{},
					}, errors.New("failed to initiate execution at domain"))
				},
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "successully executed activity",
			args: args{
				req: &rpActivityPb.InitiateExecutionAtDomainRequest{
					RecurringPaymentId:       defaultRecurringPaymentId,
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				},
				setupMocks: func(mockRpDao *rpDaoMocks.MockRecurringPaymentDao, mockRecurringActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockDomainExecutionProcessorFactory *domainExecutionProcessorMocks.MockDomainExecutionProcessorFactory, mockDomainExecutionProcessor *domainExecutionProcessorMocks.MockDomainExecutionProcessor) {
					mockRpDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
					mockDomainExecutionProcessorFactory.EXPECT().GetProcessor(defaultRecurringPayment.GetType(), defaultRecurringPayment.GetPaymentRoute()).Return(mockDomainExecutionProcessor, nil)
					mockRecurringActionDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentActionId).Return(defaultRecurringPaymentAction, nil)
					mockDomainExecutionProcessor.EXPECT().InitiateExecution(gomock.Any(), defaultInitiateExecutionReq).Return(&domainExecutionProcessor.InitiateExecutionAtDomainRes{
						// nil in action detailed status info indicates that
						// there was no business failure in initiating the execution.
						RecurringPaymentActionStatus: nil,
					}, nil)
				},
			},
			want: &rpActivityPb.InitiateExecutionAtDomainResponse{},
		},
		{
			name: "should return non-success fi status code when initiate domain execution fails due to business logic error",
			args: args{
				req: &rpActivityPb.InitiateExecutionAtDomainRequest{
					RecurringPaymentId:       defaultRecurringPaymentId,
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				},
				setupMocks: func(mockRpDao *rpDaoMocks.MockRecurringPaymentDao, mockRecurringActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockDomainExecutionProcessorFactory *domainExecutionProcessorMocks.MockDomainExecutionProcessorFactory, mockDomainExecutionProcessor *domainExecutionProcessorMocks.MockDomainExecutionProcessor) {
					mockRpDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
					mockDomainExecutionProcessorFactory.EXPECT().GetProcessor(defaultRecurringPayment.GetType(), defaultRecurringPayment.GetPaymentRoute()).Return(mockDomainExecutionProcessor, nil)
					mockRecurringActionDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentActionId).Return(defaultRecurringPaymentAction, nil)
					mockDomainExecutionProcessor.EXPECT().InitiateExecution(gomock.Any(), defaultInitiateExecutionReq).Return(&domainExecutionProcessor.InitiateExecutionAtDomainRes{
						// nil in action detailed status info indicates that
						// there was no business failure in initiating the execution.
						RecurringPaymentActionStatus: &rpPb.ActionDetailedStatusInfo{
							FiStatusCode:     "RZP100",
							ErrorDescription: "src: business, step: payment_initiation, reason: input_validation_failed, field: amount, desc: Your payment amount is different from your order amount. To pay successfully, please try using right amount.",
						},
					}, nil)
				},
			},
			want: &rpActivityPb.InitiateExecutionAtDomainResponse{
				DetailedStatus: &rpPb.ActionDetailedStatusInfo{
					FiStatusCode:     "RZP100",
					ErrorDescription: "src: business, step: payment_initiation, reason: input_validation_failed, field: amount, desc: Your payment amount is different from your order amount. To pay successfully, please try using right amount.",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockRpDao := rpDaoMocks.NewMockRecurringPaymentDao(ctr)
			mockRecurringPaymentActionDao := rpDaoMocks.NewMockRecurringPaymentsActionDao(ctr)
			mockDomainExecutionProcessorFactory := domainExecutionProcessorMocks.NewMockDomainExecutionProcessorFactory(ctr)
			mockDomainExecutionProcessor := domainExecutionProcessorMocks.NewMockDomainExecutionProcessor(ctr)

			p := &Processor{
				recurringPaymentDao:             mockRpDao,
				recurringPaymentsActionDao:      mockRecurringPaymentActionDao,
				domainExecutionProcessorFactory: mockDomainExecutionProcessorFactory,
			}
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)
			tt.args.setupMocks(mockRpDao, mockRecurringPaymentActionDao, mockDomainExecutionProcessorFactory, mockDomainExecutionProcessor)

			result := &rpActivityPb.InitiateExecutionAtDomainResponse{}
			got, err := env.ExecuteActivity(rpNs.InitiateExecutionAtDomain, tt.args.req)
			if got != nil {
				typeValueErr := got.Get(&result)
				if typeValueErr != nil {
					t.Errorf("InitiateExecution() error = %v failed to fetch type value from convertible", typeValueErr)
					return
				}
			}
			if (err != nil) != tt.wantErr {
				t.Errorf("InitiateExecution() got = %v, want %v", err, tt.want)
				return
			}
			if tt.wantErr && !tt.assertErr(err) {
				t.Errorf("InitiateExecution() got = %v, want %v", err, tt.want)
				return
			}
		})
	}
}
