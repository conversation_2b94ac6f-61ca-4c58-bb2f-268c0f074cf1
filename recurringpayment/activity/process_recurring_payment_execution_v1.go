package activity

import (
	"context"

	"go.uber.org/zap"

	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/logger"
)

func (p *Processor) ProcessRecurringPaymentExecutionV1(ctx context.Context, req *rpActivityPb.ProcessRecurringPaymentExecutionV1Request) (*rpActivityPb.ProcessRecurringPaymentExecutionV1Response, error) {
	res := &rpActivityPb.ProcessRecurringPaymentExecutionV1Response{}

	recurringPaymentProcessor, err := p.executeRecurringPaymentFactory.GetProcessorImpl(req.GetRecurringPaymentType())
	if err != nil {
		logger.Error(ctx, "error while fetching processor implementation", zap.Error(err))
		return nil, err
	}
	executeRes, err := recurringPaymentProcessor.ExecutePayment(ctx, req.GetRecurringPaymentId(), req.GetTransaction())
	if err != nil {
		return nil, err
	}

	// todo: siClient is currently not populating these fields. need to fix this.
	res.VendorResponse = &rpActivityPb.VendorResponse{
		VendorResponseCode:     executeRes.VendorResponseCode,
		VendorResponseReason:   executeRes.VendorResponseReason,
		StatusCode:             executeRes.StatusCode,
		StatusDescriptionPayer: executeRes.StatusDescriptionPayer,
		StatusDescriptionPayee: executeRes.StatusDescriptionPayee,
	}

	return res, nil
}
