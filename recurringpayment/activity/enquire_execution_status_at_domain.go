package activity

import (
	"context"
	"errors"
	"fmt"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"

	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/gamma/recurringpayment/internal/domainexecutionprocessor"
)

func (p *Processor) EnquireExecutionStatusAtDomain(ctx context.Context, req *rpActivityPb.EnquireExecutionStatusAtDomainRequest) (*rpActivityPb.EnquireExecutionStatusAtDomainResponse, error) {
	// fetch the recurring payment action to fetch the recurring payment
	action, actionErr := p.recurringPaymentsActionDao.GetById(ctx, req.GetRecurringPaymentActionId())
	if actionErr != nil {
		return nil, fmt.Errorf("failed to fetch the recurring payment action by id,err: %s %w", actionErr, epifierrors.ErrTransient)
	}
	// fetch the recurring payment by action id to identify the type
	recurringPayment, recurringPaymentErr := p.recurringPaymentDao.GetById(ctx, action.GetRecurringPaymentId())
	if recurringPaymentErr != nil {
		return nil, fmt.Errorf("failed to fetch recurring payment by id,err: %s %w", recurringPaymentErr, epifierrors.ErrTransient)
	}
	ctx = epificontext.WithOwnership(ctx, recurringPayment.GetEntityOwnership())
	// fetch the respective processor for the domain
	domainExecutionProcessor, domainExecutionProcessorErr := p.domainExecutionProcessorFactory.GetProcessor(recurringPayment.GetType(), recurringPayment.GetPaymentRoute())
	switch {
	case errors.Is(domainExecutionProcessorErr, epifierrors.ErrInvalidArgument):
		return nil, fmt.Errorf("no processor found for the given recurring payment,err: %s , %w", domainExecutionProcessorErr, epifierrors.ErrPermanent)
	case domainExecutionProcessorErr != nil:
		return nil, fmt.Errorf("failed to fetch the domain execution processor, err: %s, %w", domainExecutionProcessorErr, epifierrors.ErrTransient)
	}

	domainExecutionStatusRes, domainExecutionStatusErr := domainExecutionProcessor.EnquireExecutionStatus(ctx, req.GetRecurringPaymentActionId())
	if domainExecutionStatusErr != nil {
		return nil, fmt.Errorf("failed to enquire execution status at domain,err: %s %w", domainExecutionStatusErr, epifierrors.ErrTransient)
	}
	executionStatus, executionStatusErr := convertDomainExecutionStatusToActivityStatus(domainExecutionStatusRes.ExecutionStatus)
	if executionStatusErr != nil {
		return nil, executionStatusErr
	}
	return &rpActivityPb.EnquireExecutionStatusAtDomainResponse{
		ExecutionStatus:          executionStatus,
		ActionDetailedStatusInfo: domainExecutionStatusRes.ActionDetailedStatusInfo,
	}, nil
}

func convertDomainExecutionStatusToActivityStatus(status domainexecutionprocessor.DomainExecutionStatus) (rpActivityPb.EnquireExecutionStatusAtDomainResponse_ExecutionStatusAtDomain, error) {
	switch {
	case status == domainexecutionprocessor.DOMAIN_EXECUTION_STATUS_SUCCESS:
		return rpActivityPb.EnquireExecutionStatusAtDomainResponse_DOMAIN_EXECUTION_STATUS_SUCCESS, nil
	case status == domainexecutionprocessor.DOMAIN_EXECUTION_STATUS_FAILURE:
		return rpActivityPb.EnquireExecutionStatusAtDomainResponse_DOMAIN_EXECUTION_STATUS_FAILURE, nil
	default:
		// return transient errror in case domain returns non-terminal state
		// this logic can be modified if required
		return rpActivityPb.EnquireExecutionStatusAtDomainResponse_DOMAIN_EXECUTION_STATUS_UNSPECIFIED, fmt.Errorf("non-terminal execution state at domain,err: %w", epifierrors.ErrTransient)
	}
}
