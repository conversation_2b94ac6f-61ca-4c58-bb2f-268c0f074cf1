package activity

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

// UpdateTransaction is used to update the transaction in DB and transitions the status from current to next instead of blind update
func (p *Processor) UpdateTransaction(ctx context.Context, req *rpActivityPb.UpdateTransactionRequest) (*rpActivityPb.UpdateTransactionResponse, error) {
	res := &rpActivityPb.UpdateTransactionResponse{}

	updateTxnRes, err := p.payClient.UpdateAndChangeTransactionStatus(ctx, &payPb.UpdateAndChangeTransactionStatusRequest{
		Transaction:   req.GetTransaction(),
		ReqInfo:       req.GetReqInfo(),
		UpdateMask:    req.GetUpdateMask(),
		CurrentStatus: req.GetCurrentStatus(),
		NextStatus:    req.GetNextStatus(),
	})
	if err != nil {
		logger.Error(ctx, "error while updating transaction", zap.Error(err))
		return nil, err
	}

	// Required for idempotency of activity
	if !updateTxnRes.GetStatus().IsSuccess() {
		getTxnRes, err := p.payClient.GetTransactionByDedupeId(ctx, &payPb.GetTransactionByDedupeIdRequest{
			DedupeId: req.GetTransaction().GetDedupeId(),
		})
		if err1 := epifigrpc.RPCError(getTxnRes, err); err1 != nil {
			logger.Error(ctx, "Error while trying to GetTransactionByDedupeId", zap.Error(err1))
			return nil, err1
		}

		req.Transaction.Status = req.GetNextStatus()
		if isTransactionAlreadyUpdated(req.GetTransaction(), getTxnRes.GetTransaction()) {
			return res, nil
		}
		return nil, fmt.Errorf("error while trying to Update Transaciton, Txn in DB in unexpected state %w", epifierrors.ErrPermanent)
	}

	return res, nil
}

func isTransactionAlreadyUpdated(txn1 *paymentPb.Transaction, txn2 *paymentPb.Transaction) bool {
	return txn1.GetId() == txn2.GetId() && txn1.GetStatus() == txn2.GetStatus()
}
