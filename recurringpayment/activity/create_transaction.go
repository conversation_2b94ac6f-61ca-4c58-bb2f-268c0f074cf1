package activity

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

func (p *Processor) CreateTransaction(ctx context.Context, req *rpActivityPb.CreateTransactionRequest) (*rpActivityPb.CreateTransactionResponse, error) {
	res := &rpActivityPb.CreateTransactionResponse{}

	getTransactionRes, err := p.paymentClient.GetTransaction(ctx, &paymentPb.GetTransactionRequest{
		Identifier: &paymentPb.GetTransactionRequest_Utr{
			Utr: req.GetTransactionRequestParams().GetUtr(),
		},
	})

	switch {
	case err != nil && !getTransactionRes.GetStatus().IsRecordNotFound():
		logger.Error(ctx, "failed to validate if transaction exists", zap.String(logger.ID, req.TransactionRequestParams.GetUtr()), zap.Error(err))
		return nil, fmt.Errorf("failed to validate if transaction exists, err: %s, %w", err, epifierrors.ErrTransient)
	case getTransactionRes.GetStatus().IsSuccess():
		logger.Info(ctx, "transaction already exists with the given utr", zap.String(logger.ID, req.GetTransactionRequestParams().GetUtr()))
		res.Transaction = getTransactionRes.GetTransaction()
		return res, nil
	}

	createTxnRes, err := p.paymentClient.CreateTransaction(ctx, &paymentPb.CreateTransactionRequest{
		PiFrom:          req.GetTransactionRequestParams().GetPiFrom(),
		PiTo:            req.GetTransactionRequestParams().GetPiTo(),
		Amount:          req.GetTransactionRequestParams().GetAmount(),
		Remarks:         req.GetTransactionRequestParams().GetRemarks(),
		OrderId:         req.GetTransactionRequestParams().GetOrderId(),
		PaymentProtocol: req.GetTransactionRequestParams().GetPaymentProtocol(),
		Status:          req.GetTransactionRequestParams().GetStatus(),
		Utr:             req.GetTransactionRequestParams().GetUtr(),
		Ownership:       req.GetTransactionRequestParams().GetOwnership(),
		ReqInfo:         req.GetTransactionRequestParams().GetReqInfo(),
	})
	if err = epifigrpc.RPCError(createTxnRes, err); err != nil {
		logger.Error(ctx, "error while creating transaction", zap.String(logger.ID, req.GetTransactionRequestParams().GetUtr()), zap.Error(err))
		return nil, fmt.Errorf("failed to create the transaction, err: %s, %w", err, epifierrors.ErrTransient)
	}

	res.Transaction = createTxnRes.GetTransaction()
	return res, nil
}
