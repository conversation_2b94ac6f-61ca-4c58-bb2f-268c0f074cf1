package activity

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	actorPb "github.com/epifi/gamma/api/actor"
	notificationPb "github.com/epifi/gamma/api/celestial/activity/notification"
	commspb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/fcm"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	payPkg "github.com/epifi/gamma/pkg/pay"
)

const (
	setFrequency                       = "set frequency"
	user                               = "user"
	merchant                           = "merchant"
	dismissibleNotificationRefIdSuffix = "_DISMISSIBLE"
)

var (
	mandateFrequencyMap = map[int]string{
		2:  "day",
		3:  "week",
		4:  "fortnight",
		5:  "month",
		6:  "bi month",
		7:  "quarter",
		8:  "half year",
		9:  "year",
		10: "set frequency",
	}
)

// getMandateFrequency - takes allowed frequency as input and returns the corresponding mandate frequency as string
func (p *Processor) getMandateFrequency(allowedFrequency int) string {
	if freq, ok := mandateFrequencyMap[allowedFrequency]; ok {
		return freq
	}
	return setFrequency
}

// processNotificationTemplate - wrapper function to generate notification templates for different recurring payment types
func (p *Processor) generateNotificationTemplate(ctx context.Context, recurringPayment *rpPb.RecurringPayment,
	toActorEntity *actorPb.GetEntityDetailsByActorIdResponse, fromActorEntity *actorPb.GetEntityDetailsByActorIdResponse, recurringPaymentAction *rpPb.RecurringPaymentsAction) (*notificationPb.Notification, error) {
	var communicationList []*commspb.Communication
	currTime := time.Now().In(datetime.IST)
	currDate := currTime.Format(p.config.UpiMandateNotifDateFormat)
	switch recurringPayment.GetType() {
	case rpPb.RecurringPaymentType_STANDING_INSTRUCTION:
		isMerchantActor := toActorEntity.GetType().IsMerchant()
		communicationList = p.getSINotificationTemplate(ctx, recurringPayment, toActorEntity, isMerchantActor, recurringPaymentAction.GetAction())
		communicationList = append(communicationList, p.getSISmsTemplate(ctx, recurringPayment, toActorEntity, recurringPaymentAction.GetAction())...)

	case rpPb.RecurringPaymentType_UPI_MANDATES:
		communicationList = p.getMandateNotificationTemplate(ctx, recurringPayment, fromActorEntity, toActorEntity, recurringPaymentAction.GetAction())
		mandateStartDate := recurringPayment.GetInterval().GetStartTime().AsTime().Format(p.config.UpiMandateNotifDateFormat)
		communicationList = append(communicationList, p.getMandateSmsTemplate(ctx, recurringPayment,
			fromActorEntity, toActorEntity, currDate, mandateStartDate, recurringPaymentAction.GetAction())...)

	case rpPb.RecurringPaymentType_ENACH_MANDATES:
		communicationList = p.getEnachMandateNotificationTemplate(ctx, recurringPayment, toActorEntity, recurringPaymentAction)

	default:
		return nil, fmt.Errorf("recurring payment type not supported for given recurring payment %v %w", recurringPayment.GetId(), epifierrors.ErrPermanent)
	}
	return &notificationPb.Notification{
		UserIdentifier:    &notificationPb.Notification_UserId{UserId: fromActorEntity.GetEntityId()},
		CommunicationList: communicationList,
		QualityOfService:  commspb.QoS_GUARANTEED,
	}, nil
}

// GetNotificationTemplate - generates the notification templates for different recurring payment types
func (p *Processor) GetNotificationTemplate(ctx context.Context, req *notificationPb.GetTemplatesRequest) (*notificationPb.GetTemplatesResponse, error) {
	lg := activity.GetLogger(ctx)
	recurringPaymentAction, err := p.recurringPaymentProcessor.GetRecurringPaymentActionByClientReqId(ctx, req.GetClientReqId().GetId())
	status := rpcPb.StatusFromError(err)
	switch {
	case status.IsRecordNotFound():
		lg.Error("no recurring payment action found", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId().String()))
		return nil, epifitemporal.NewPermanentError(err)
	case !status.IsSuccess():
		lg.Error("error occurred while fetching recurring payment action", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId().String()))
		return nil, epifitemporal.NewTransientError(err)
	}
	recurringPayment, err := p.recurringPaymentProcessor.GetRecurringPaymentById(ctx, recurringPaymentAction.GetRecurringPaymentId())
	if err != nil {
		lg.Error("couldn't fetch recurring payment for given recurring payment id", zap.Error(err),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId().String()))
		return nil, epifitemporal.GetError(err)
	}

	fromActorEntity, err := p.actorProcessor.GetActorDetails(ctx, recurringPayment.GetFromActorId())
	if err != nil {
		lg.Error("couldn't fetch fromActor entities from actor client!", zap.Error(err),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId().String()))
		return nil, epifitemporal.GetError(err)
	}
	toActorEntity, err := p.actorProcessor.GetActorDetails(ctx, recurringPayment.GetToActorId())
	if err != nil {
		lg.Error("couldn't fetch toActor entities from actor client!", zap.Error(err),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId().String()))
		return nil, epifitemporal.GetError(err)
	}

	notifications, err := p.generateNotificationTemplate(ctx, recurringPayment, toActorEntity, fromActorEntity, recurringPaymentAction)
	if err != nil {
		lg.Error("cannot get notification template for given client req id", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId().String()))
		return nil, epifitemporal.GetError(err)
	}
	res := &notificationPb.GetTemplatesResponse{}
	if len(notifications.GetCommunicationList()) == 0 {
		lg.Info("no notification template for this recurring payment", zap.String("recurring payment type", recurringPayment.GetType().String()))
		return res, nil
	}
	lg.Debug("successfully retrieved notification template for recurring payment", zap.String("recurring payment type", recurringPayment.GetType().String()),
		zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
	res.Notifications = notifications
	return res, nil
}

// nolint:dupl
// getMandateReceivedSmsTemplate - sms template in case mandate is received
func (p *Processor) getMandateReceivedSmsTemplate(recurringPayment *rpPb.RecurringPayment, toActorEntityName *commontypes.Name) *commspb.Communication_Sms {
	return &commspb.Communication_Sms{
		Sms: &commspb.SMSMessage{
			SmsOption: &commspb.SmsOption{
				Option: &commspb.SmsOption_MandateReceivedSmsOption{
					MandateReceivedSmsOption: &commspb.MandateReceivedSmsOption{
						SmsType: commspb.SmsType_MANDATE_RECEIVED,
						Option: &commspb.MandateReceivedSmsOption_MandateReceivedV1{
							MandateReceivedV1: &commspb.MandateReceivedSmsOptionV1{
								TemplateVersion: commspb.TemplateVersion_VERSION_V1,
								MandateAmount:   recurringPayment.GetAmount(),
								PayeeName:       toActorEntityName,
							},
						},
					},
				},
			},
		},
	}
}

// nolint:dupl
// getMandateApprovedSmsTemplate - sms template in case mandate is approved
func (p *Processor) getMandateApprovedSmsTemplate(
	recurringPayment *rpPb.RecurringPayment,
	mandateStartDate string,
	toActorEntity *actorPb.GetEntityDetailsByActorIdResponse,
) *commspb.Communication_Sms {
	return &commspb.Communication_Sms{
		Sms: &commspb.SMSMessage{
			SmsOption: &commspb.SmsOption{
				Option: &commspb.SmsOption_MandateApprovedSmsOption{
					MandateApprovedSmsOption: &commspb.MandateApprovedSmsOption{
						SmsType: commspb.SmsType_MANDATE_APPROVED,
						Option: &commspb.MandateApprovedSmsOption_MandateApprovedV1{
							MandateApprovedV1: &commspb.MandateApprovedSmsOptionV1{
								MandateStartDate: mandateStartDate,
								TemplateVersion:  commspb.TemplateVersion_VERSION_V1,
								MandateAmount:    recurringPayment.GetAmount(),
								PayeeName:        toActorEntity.GetName(),
								MandateFrequency: p.config.RecurringPaymentFrequencyMapping[recurringPayment.GetRecurrenceRule().GetAllowedFrequency().String()],
							},
						},
					},
				},
			},
		},
	}
}

// nolint:dupl
// getMandateDeclinedSmsTemplate - sms template in case mandate is declined
func (p *Processor) getMandateDeclinedSmsTemplate(
	recurringPayment *rpPb.RecurringPayment,
	currDate string,
	toActorEntity *actorPb.GetEntityDetailsByActorIdResponse,
) *commspb.Communication_Sms {
	return &commspb.Communication_Sms{
		Sms: &commspb.SMSMessage{
			SmsOption: &commspb.SmsOption{
				Option: &commspb.SmsOption_MandateDeclinedSmsOption{
					MandateDeclinedSmsOption: &commspb.MandateDeclinedSmsOption{
						SmsType: commspb.SmsType_MANDATE_DECLINED,
						Option: &commspb.MandateDeclinedSmsOption_MandateDeclinedV1{
							MandateDeclinedV1: &commspb.MandateDeclinedSmsOptionV1{
								MandateExecutionDate: currDate,
								TemplateVersion:      commspb.TemplateVersion_VERSION_V1,
								MandateAmount:        recurringPayment.GetAmount(),
								PayeeName:            toActorEntity.GetName(),
								MandateFrequency:     p.config.RecurringPaymentFrequencyMapping[recurringPayment.GetRecurrenceRule().GetAllowedFrequency().String()],
							},
						},
					},
				},
			},
		},
	}

}

// getMandateCreatedSmsTemplate - sms template in case mandate is created
func (p *Processor) getMandateCreatedSmsTemplate(recurringPayment *rpPb.RecurringPayment, toActorEntityName *commontypes.Name,
	fromActorEntityName *commontypes.Name) *commspb.Communication_Sms {
	return &commspb.Communication_Sms{
		Sms: &commspb.SMSMessage{
			SmsOption: &commspb.SmsOption{
				Option: &commspb.SmsOption_MandateCreatedSmsOption{
					MandateCreatedSmsOption: &commspb.MandateCreatedSmsOption{
						SmsType: commspb.SmsType_MANDATE_CREATED,
						Option: &commspb.MandateCreatedSmsOption_MandateCreatedV1{
							MandateCreatedV1: &commspb.MandateCreatedSmsOptionV1{
								TemplateVersion:  commspb.TemplateVersion_VERSION_V1,
								MandateAmount:    recurringPayment.GetAmount(),
								PayeeName:        toActorEntityName,
								PayerName:        fromActorEntityName,
								MandateFrequency: p.config.RecurringPaymentFrequencyMapping[recurringPayment.GetRecurrenceRule().GetAllowedFrequency().String()],
							},
						},
					},
				},
			},
		},
	}
}

// nolint:dupl
// getMandateModifiedSmsTemplate - sms template in case mandate is modified
func (p *Processor) getMandateModifiedSmsTemplate(recurringPayment *rpPb.RecurringPayment,
	toActorEntityName *commontypes.Name) *commspb.Communication_Sms {
	return &commspb.Communication_Sms{
		Sms: &commspb.SMSMessage{
			SmsOption: &commspb.SmsOption{
				Option: &commspb.SmsOption_MandateModifiedSmsOption{
					MandateModifiedSmsOption: &commspb.MandateModifiedSmsOption{
						SmsType: commspb.SmsType_MANDATE_MODIFIED,
						Option: &commspb.MandateModifiedSmsOption_MandateModifiedV1{
							MandateModifiedV1: &commspb.MandateModifiedSmsOptionV1{
								TemplateVersion: commspb.TemplateVersion_VERSION_V1,
								MandateAmount:   recurringPayment.GetAmount(),
								PayeeName:       toActorEntityName,
							},
						},
					},
				},
			},
		},
	}
}

// nolint:dupl
// getMandateRevokedSmsTemplate - sms template in case mandate is revoked
func (p *Processor) getMandateRevokedSmsTemplate(recurringPayment *rpPb.RecurringPayment,
	toActorEntityName *commontypes.Name) *commspb.Communication_Sms {
	return &commspb.Communication_Sms{
		Sms: &commspb.SMSMessage{
			SmsOption: &commspb.SmsOption{
				Option: &commspb.SmsOption_MandateRevokedSmsOption{
					MandateRevokedSmsOption: &commspb.MandateRevokedSmsOption{
						SmsType: commspb.SmsType_MANDATE_REVOKED,
						Option: &commspb.MandateRevokedSmsOption_MandateRevokedV1{
							MandateRevokedV1: &commspb.MandateRevokedSmsOptionV1{
								TemplateVersion: commspb.TemplateVersion_VERSION_V1,
								MandateAmount:   recurringPayment.GetAmount(),
								PayeeName:       toActorEntityName,
							},
						},
					},
				},
			},
		},
	}
}

// nolint:dupl
// getMandatePausedSmsTemplate - sms template in case mandate is paused
func (p *Processor) getMandatePausedSmsTemplate(fromActorEntityName, toActorEntityName *commontypes.Name) *commspb.Communication_Sms {
	return &commspb.Communication_Sms{
		Sms: &commspb.SMSMessage{
			SmsOption: &commspb.SmsOption{
				Option: &commspb.SmsOption_MandatePausedSmsOption{
					MandatePausedSmsOption: &commspb.MandatePausedSmsOption{
						SmsType: commspb.SmsType_MANDATE_PAUSED,
						Option: &commspb.MandatePausedSmsOption_MandatePausedV1{
							MandatePausedV1: &commspb.MandatePausedSmsOptionV1{
								TemplateVersion: commspb.TemplateVersion_VERSION_V1,
								PayerName:       fromActorEntityName,
								PayeeName:       toActorEntityName,
							},
						},
					},
				},
			},
		},
	}
}

// nolint:dupl
// getMandateUnausedSmsTemplate - sms template in case mandate is unpaused
func (p *Processor) getMandateUnpausedSmsTemplate(toActorEntityName *commontypes.Name) *commspb.Communication_Sms {
	return &commspb.Communication_Sms{
		Sms: &commspb.SMSMessage{
			SmsOption: &commspb.SmsOption{
				Option: &commspb.SmsOption_MandateUnpausedSmsOption{
					MandateUnpausedSmsOption: &commspb.MandateUnpausedSmsOption{
						SmsType: commspb.SmsType_MANDATE_UNPAUSED,
						Option: &commspb.MandateUnpausedSmsOption_MandateUnpausedV1{
							MandateUnpausedV1: &commspb.MandateUnpausedSmsOptionV1{
								TemplateVersion: commspb.TemplateVersion_VERSION_V1,
								PayeeName:       toActorEntityName,
							},
						},
					},
				},
			},
		},
	}
}

// getMandateSmsTemplate - generates the sms template for mandates
func (p *Processor) getMandateSmsTemplate(ctx context.Context, recurringPayment *rpPb.RecurringPayment, fromActorEntity *actorPb.GetEntityDetailsByActorIdResponse,
	toActorEntity *actorPb.GetEntityDetailsByActorIdResponse, currDate string, mandateStartDate string, action rpPb.Action) []*commspb.Communication {
	lg := activity.GetLogger(ctx)
	var (
		smsContent *commspb.Communication_Sms
	)
	switch {
	case action == rpPb.Action_CREATE:
		switch {
		// Mandate Received
		case recurringPayment.GetState() == rpPb.RecurringPaymentState_CREATION_INITIATED:
			smsContent = p.getMandateReceivedSmsTemplate(recurringPayment, toActorEntity.GetName())
		// Mandate Approved
		case recurringPayment.GetState() == rpPb.RecurringPaymentState_CREATION_AUTHORISED:
			smsContent = p.getMandateApprovedSmsTemplate(recurringPayment, mandateStartDate, toActorEntity)
		// Mandate Declined
		case recurringPayment.GetState() == rpPb.RecurringPaymentState_FAILED:
			smsContent = p.getMandateDeclinedSmsTemplate(recurringPayment, currDate, toActorEntity)
		// Mandate Created
		case recurringPayment.GetState() == rpPb.RecurringPaymentState_ACTIVATED:
			smsContent = p.getMandateCreatedSmsTemplate(recurringPayment, toActorEntity.GetName(), fromActorEntity.GetName())
		default:
			// no Notifications needs to be sent
			lg.Info("no sms template for this recurringPayment type",
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
			return nil
		}
	case action == rpPb.Action_MODIFY && recurringPayment.GetState() == rpPb.RecurringPaymentState_ACTIVATED:
		smsContent = p.getMandateModifiedSmsTemplate(recurringPayment, toActorEntity.GetName())

	case action == rpPb.Action_REVOKE:
		smsContent = p.getMandateRevokedSmsTemplate(recurringPayment, toActorEntity.GetName())

	case action == rpPb.Action_PAUSE:
		smsContent = p.getMandatePausedSmsTemplate(fromActorEntity.GetName(), toActorEntity.GetName())

	case action == rpPb.Action_UNPAUSE:
		smsContent = p.getMandateUnpausedSmsTemplate(toActorEntity.GetName())

	default:
		lg.Info("no sms template for this recurring payment action",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		return nil
	}
	return []*commspb.Communication{
		{
			Message: smsContent,
			Medium:  commspb.Medium_SMS,
		},
	}
}

// getSIDeclinedNotificationTemplate - gets notification template in case SI is declined
func (p *Processor) getSIDeclinedNotificationTemplate(recurringPayment *rpPb.RecurringPayment,
	toActorType string) (*fcm.CommonTemplateFields, fcm.NotificationType, time.Duration) {
	commonFields := &fcm.CommonTemplateFields{}
	commonFields.Body = fmt.Sprintf(p.config.RecurringPaymentNotificationParams.SIDeclinedPayer.Body, money.ToDisplayStringWithINRSymbol(recurringPayment.GetAmount()),
		toActorType)
	commonFields.Title = p.config.RecurringPaymentNotificationParams.SIDeclinedPayer.Title
	commonFields.IconAttributes = convertToFCMIconAttribute(p.config.RecurringPaymentNotificationParams.SIDeclinedPayer.IconAttr)
	commonFields.Deeplink = &deeplink.Deeplink{
		Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
			RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
				RecurringPaymentId: recurringPayment.GetId(),
			},
		},
	}
	return commonFields, p.config.RecurringPaymentNotificationParams.SIDeclinedPayer.NotificationType.ToFCMNotificationTypeEnum(),
		p.config.RecurringPaymentNotificationParams.SIDeclinedPayer.NotificationExpiry

}

// getSICreatedNotificationTemplate - gets notification template in case SI is created
func (p *Processor) getSICreatedNotificationTemplate(recurringPayment *rpPb.RecurringPayment,
	toActorEntity *actorPb.GetEntityDetailsByActorIdResponse) (*fcm.CommonTemplateFields, fcm.NotificationType, time.Duration) {
	commonFields := &fcm.CommonTemplateFields{}
	commonFields.Body = fmt.Sprintf(p.config.RecurringPaymentNotificationParams.SICreationPayer.Body,
		money.ToDisplayStringWithINRSymbol(recurringPayment.GetAmount()),
		toActorEntity.GetName().GetFirstName(),
		p.config.RecurringPaymentFrequencyMapping[recurringPayment.GetRecurrenceRule().GetAllowedFrequency().String()])
	commonFields.Title = p.config.RecurringPaymentNotificationParams.SICreationPayer.Title
	commonFields.IconAttributes = convertToFCMIconAttribute(p.config.RecurringPaymentNotificationParams.SICreationPayer.IconAttr)
	commonFields.Deeplink = &deeplink.Deeplink{
		Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
			RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
				RecurringPaymentId: recurringPayment.GetId(),
			},
		},
	}
	return commonFields, p.config.RecurringPaymentNotificationParams.SICreationPayer.NotificationType.ToFCMNotificationTypeEnum(),
		p.config.RecurringPaymentNotificationParams.SICreationPayer.NotificationExpiry
}

// getSINotificationTemplate - generates the SI notification template
func (p *Processor) getSINotificationTemplate(ctx context.Context, recurringPayment *rpPb.RecurringPayment,
	toActorEntity *actorPb.GetEntityDetailsByActorIdResponse,
	isMerchantActor bool, action rpPb.Action) []*commspb.Communication {
	lg := activity.GetLogger(ctx)
	// All the notification are being send to fromActor/PAYER.
	var (
		commonFields *fcm.CommonTemplateFields
		notifType    fcm.NotificationType
		notifTTL     time.Duration
		toActorType  = user
	)

	if isMerchantActor {
		toActorType = merchant
	}
	switch {
	// SI Decline
	case action == rpPb.Action_CREATE && recurringPayment.GetState() == rpPb.RecurringPaymentState_FAILED:
		commonFields, notifType, notifTTL = p.getSIDeclinedNotificationTemplate(recurringPayment, toActorType)
	// SI Created
	case action == rpPb.Action_CREATE && recurringPayment.GetState() == rpPb.RecurringPaymentState_ACTIVATED:
		commonFields, notifType, notifTTL = p.getSICreatedNotificationTemplate(recurringPayment, toActorEntity)
	default:
		lg.Info("no notification template for state",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		return nil
	}

	fcmNotificationWithCommonFields := initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL)
	return []*commspb.Communication{
		{
			Message: &commspb.Communication_Notification{
				Notification: &commspb.NotificationMessage{
					Notification: fcmNotificationWithCommonFields,
				},
			},
			Medium: commspb.Medium_NOTIFICATION,
		},
	}
}

// nolint:dupl
// getSIDeclinedSmsTemplate - gets sms template in case SI is declined
func (p *Processor) getSIDeclinedSmsTemplate(recurringPayment *rpPb.RecurringPayment, toActorEntityName *commontypes.Name) *commspb.Communication_Sms {
	return &commspb.Communication_Sms{
		Sms: &commspb.SMSMessage{
			SmsOption: &commspb.SmsOption{
				Option: &commspb.SmsOption_SiDeclinedSmsOption{
					SiDeclinedSmsOption: &commspb.SIDeclinedSmsOption{
						SmsType: commspb.SmsType_SI_DECLINED,
						Option: &commspb.SIDeclinedSmsOption_SiDeclinedV1{
							SiDeclinedV1: &commspb.SIDeclinedSmsOptionV1{
								TemplateVersion: commspb.TemplateVersion_VERSION_V1,
								SiAmount:        recurringPayment.GetAmount(),
								PayeeName:       toActorEntityName,
							},
						},
					},
				},
			},
		},
	}
}

// getSICreatedNotificationTemplate - gets notification template in case SI is created
func (p *Processor) getSICreatedSmsTemplate(recurringPayment *rpPb.RecurringPayment, toActorEntityName *commontypes.Name) *commspb.Communication_Sms {
	return &commspb.Communication_Sms{
		Sms: &commspb.SMSMessage{
			SmsOption: &commspb.SmsOption{
				Option: &commspb.SmsOption_SiCreatedSmsOption{
					SiCreatedSmsOption: &commspb.SICreatedSmsOption{
						SmsType: commspb.SmsType_SI_CREATED,
						Option: &commspb.SICreatedSmsOption_SiCreatedV1{
							SiCreatedV1: &commspb.SICreatedSmsOptionV1{
								TemplateVersion: commspb.TemplateVersion_VERSION_V1,
								SiAmount:        recurringPayment.GetAmount(),
								SiFrequency:     p.config.RecurringPaymentFrequencyMapping[recurringPayment.GetRecurrenceRule().GetAllowedFrequency().String()],
								PayeeName:       toActorEntityName,
							},
						},
					},
				},
			},
		},
	}
}

// getSISmsTemplate - generates SMS template for SI
func (p *Processor) getSISmsTemplate(ctx context.Context, recurringPayment *rpPb.RecurringPayment,
	toActorEntity *actorPb.GetEntityDetailsByActorIdResponse, action rpPb.Action) []*commspb.Communication {
	var (
		smsContent *commspb.Communication_Sms
	)
	lg := activity.GetLogger(ctx)
	switch {
	// SI Decline
	case action == rpPb.Action_CREATE && recurringPayment.GetState() == rpPb.RecurringPaymentState_FAILED:
		smsContent = p.getSIDeclinedSmsTemplate(recurringPayment, toActorEntity.GetName())
	// SI Created
	case action == rpPb.Action_CREATE && recurringPayment.GetState() == rpPb.RecurringPaymentState_ACTIVATED:
		smsContent = p.getSICreatedSmsTemplate(recurringPayment, toActorEntity.GetName())
	default:
		// no Notifications needs to be sent
		lg.Info("no sms template for this recurringPayment state",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		return nil
	}
	return []*commspb.Communication{
		{
			Message: smsContent,
			Medium:  commspb.Medium_SMS,
		},
	}
}

// getMandateReceivedNotifications - returns list of notification templates in case mandate is received(In-app, System Tray, Background etc.)
func (p *Processor) getMandateReceivedNotificationTemplates(recurringPayment *rpPb.RecurringPayment, toActorEntity *actorPb.GetEntityDetailsByActorIdResponse) []*commspb.Communication {
	var (
		notifications     []*commspb.Communication
		authorizeDeeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_AUTHORIZE_RECURRING_PAYMENT_SCREEN,
			ScreenOptions: &deeplink.Deeplink_AuthorizeRecurringPaymentScreenOptions{
				AuthorizeRecurringPaymentScreenOptions: &deeplink.AuthorizeRecurringPaymentScreenOptions{
					RecurringPaymentId: recurringPayment.GetId(),
				},
			},
		}
	)
	for _, notificationParams := range p.config.RecurringPaymentNotificationParams.MandateReceivedPayer {
		notificationTemplate := createMandateNotificationTemplate(notificationParams, authorizeDeeplink, recurringPayment, toActorEntity)
		if notificationParams.NotificationType.ToFCMNotificationTypeEnum() == fcm.NotificationType_SYSTEM_TRAY {
			// this PN can be dismissed by client if user has completed action, so adding suffix to make it unique
			notificationTemplate.GetNotification().GetNotification().GetSystemTrayTemplate().GetCommonTemplateFields().NotificationReferenceId = recurringPayment.GetId() + dismissibleNotificationRefIdSuffix
		}
		notifications = append(notifications, notificationTemplate)
	}
	return notifications
}

// generic function to create notification template from notification params
func createMandateNotificationTemplate(notifParams payPkg.NotificationTemplateParams, dl *deeplink.Deeplink, recurringPayment *rpPb.RecurringPayment, toActorEntity *actorPb.GetEntityDetailsByActorIdResponse) *commspb.Communication {
	var commonFields = &fcm.CommonTemplateFields{}
	commonFields.Title = notifParams.Title
	commonFields.Body = fmt.Sprintf(notifParams.Body,
		toActorEntity.GetName().GetFirstName(), money.ToDisplayStringWithINRSymbol(recurringPayment.GetAmount()))
	commonFields.Deeplink = dl
	commonFields.IconAttributes = convertToFCMIconAttribute(notifParams.IconAttr)

	fcmNotificationWithCommonFields := initFcmNotificationWithCommonFields(commonFields, notifParams.NotificationType.ToFCMNotificationTypeEnum(), notifParams.NotificationExpiry)
	return &commspb.Communication{
		Message: &commspb.Communication_Notification{
			Notification: &commspb.NotificationMessage{
				Notification: fcmNotificationWithCommonFields,
			},
		},
		Medium: commspb.Medium_NOTIFICATION,
	}
}

// getMandateApprovedNotificationTemplate - gets notification template in case mandate is approved
func (p *Processor) getMandateApprovedNotificationTemplate(
	recurringPayment *rpPb.RecurringPayment,
	toActorEntity *actorPb.GetEntityDetailsByActorIdResponse,
) (*fcm.CommonTemplateFields, fcm.NotificationType, time.Duration) {
	var commonFields = &fcm.CommonTemplateFields{}
	commonFields.Body = fmt.Sprintf(p.config.RecurringPaymentNotificationParams.MandateApprovedPayer.Body,
		p.getMandateFrequency(int(recurringPayment.GetRecurrenceRule().GetAllowedFrequency())),
		toActorEntity.GetName().ToString(), money.ToDisplayStringWithINRSymbol(recurringPayment.GetAmount()))
	commonFields.Title = p.config.RecurringPaymentNotificationParams.MandateApprovedPayer.Title
	commonFields.Deeplink = &deeplink.Deeplink{
		Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
			RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
				RecurringPaymentId: recurringPayment.GetId(),
			},
		},
	}
	commonFields.IconAttributes = convertToFCMIconAttribute(p.config.RecurringPaymentNotificationParams.MandateApprovedPayer.IconAttr)
	return commonFields, p.config.RecurringPaymentNotificationParams.MandateApprovedPayer.NotificationType.ToFCMNotificationTypeEnum(),
		p.config.RecurringPaymentNotificationParams.MandateApprovedPayer.NotificationExpiry
}

// getMandateDeclinedNotificationTemplate - gets notification template in case mandate is declined
func (p *Processor) getMandateDeclinedNotificationTemplate(recurringPayment *rpPb.RecurringPayment,
	toActorEntity *actorPb.GetEntityDetailsByActorIdResponse) (*fcm.CommonTemplateFields, fcm.NotificationType, time.Duration) {
	commonFields := &fcm.CommonTemplateFields{}
	commonFields.Body = fmt.Sprintf(p.config.RecurringPaymentNotificationParams.MandateDeclinedPayer.Body, toActorEntity.GetName().GetFirstName(),
		p.config.RecurringPaymentFrequencyMapping[recurringPayment.GetRecurrenceRule().GetAllowedFrequency().String()],
		money.ToDisplayStringWithINRSymbol(recurringPayment.GetAmount()))
	commonFields.Title = p.config.RecurringPaymentNotificationParams.MandateDeclinedPayer.Title
	commonFields.IconAttributes = convertToFCMIconAttribute(p.config.RecurringPaymentNotificationParams.MandateDeclinedPayer.IconAttr)
	commonFields.Deeplink = &deeplink.Deeplink{
		Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
			RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
				RecurringPaymentId: recurringPayment.GetId(),
			},
		},
	}
	return commonFields, p.config.RecurringPaymentNotificationParams.MandateDeclinedPayer.NotificationType.ToFCMNotificationTypeEnum(),
		p.config.RecurringPaymentNotificationParams.MandateDeclinedPayer.NotificationExpiry
}

// getMandateModifiedNotificationTemplate - gets notification template in case mandate is modified
func (p *Processor) getMandateModifiedNotificationTemplate(recurringPayment *rpPb.RecurringPayment,
	toActorEntity *actorPb.GetEntityDetailsByActorIdResponse) (*fcm.CommonTemplateFields, fcm.NotificationType, time.Duration) {
	commonFields := &fcm.CommonTemplateFields{}
	commonFields.Body = fmt.Sprintf(p.config.RecurringPaymentNotificationParams.MandateModifiedPayer.Body,
		toActorEntity.GetName().GetFirstName(), money.ToDisplayStringWithINRSymbol(recurringPayment.GetAmount()))
	commonFields.Title = p.config.RecurringPaymentNotificationParams.MandateModifiedPayer.Title
	commonFields.IconAttributes = convertToFCMIconAttribute(p.config.RecurringPaymentNotificationParams.MandateModifiedPayer.IconAttr)
	commonFields.Deeplink = &deeplink.Deeplink{
		Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
			RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
				RecurringPaymentId: recurringPayment.GetId(),
			},
		},
	}
	return commonFields, p.config.RecurringPaymentNotificationParams.MandateModifiedPayer.NotificationType.ToFCMNotificationTypeEnum(),
		p.config.RecurringPaymentNotificationParams.MandateModifiedPayer.NotificationExpiry
}

// getMandateRevokedNotificationTemplate - gets notification template in case mandate is revoked
func (p *Processor) getMandateRevokedNotificationTemplate(recurringPayment *rpPb.RecurringPayment,
	toActorEntity *actorPb.GetEntityDetailsByActorIdResponse) (*fcm.CommonTemplateFields, fcm.NotificationType, time.Duration) {
	commonFields := &fcm.CommonTemplateFields{}
	commonFields.Body = fmt.Sprintf(p.config.RecurringPaymentNotificationParams.MandateRevokedPayer.Body,
		toActorEntity.GetName().GetFirstName(), money.ToDisplayStringWithINRSymbol(recurringPayment.GetAmount()))
	commonFields.Title = p.config.RecurringPaymentNotificationParams.MandateRevokedPayer.Title
	commonFields.Deeplink = &deeplink.Deeplink{
		Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
			RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
				RecurringPaymentId: recurringPayment.GetId(),
			},
		},
	}
	commonFields.IconAttributes = convertToFCMIconAttribute(p.config.RecurringPaymentNotificationParams.MandateRevokedPayer.IconAttr)
	return commonFields, p.config.RecurringPaymentNotificationParams.MandateRevokedPayer.NotificationType.ToFCMNotificationTypeEnum(),
		p.config.RecurringPaymentNotificationParams.MandateRevokedPayer.NotificationExpiry
}

// nolint:dupl
// getMandatePausedNotificationTemplate - gets notification template in case mandate is paused
func (p *Processor) getMandatePausedNotificationTemplate(recurringPayment *rpPb.RecurringPayment) (*fcm.CommonTemplateFields, fcm.NotificationType, time.Duration) {
	commonFields := &fcm.CommonTemplateFields{}
	commonFields.Body = p.config.RecurringPaymentNotificationParams.MandatePausedPayer.Body
	commonFields.Title = p.config.RecurringPaymentNotificationParams.MandatePausedPayer.Title
	commonFields.Deeplink = &deeplink.Deeplink{
		Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
			RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
				RecurringPaymentId: recurringPayment.GetId(),
			},
		},
	}
	commonFields.IconAttributes = convertToFCMIconAttribute(p.config.RecurringPaymentNotificationParams.MandatePausedPayer.IconAttr)
	return commonFields, p.config.RecurringPaymentNotificationParams.MandatePausedPayer.NotificationType.ToFCMNotificationTypeEnum(),
		p.config.RecurringPaymentNotificationParams.MandatePausedPayer.NotificationExpiry
}

// nolint:dupl
// getMandateUnpausedNotificationTemplate - gets notification template in case mandate is unpaused
func (p *Processor) getMandateUnpausedNotificationTemplate(recurringPayment *rpPb.RecurringPayment, fromActorName *commontypes.Name) (*fcm.CommonTemplateFields, fcm.NotificationType, time.Duration) {
	commonFields := &fcm.CommonTemplateFields{}
	commonFields.Body = fmt.Sprintf(p.config.RecurringPaymentNotificationParams.MandateUnpausedPayer.Body, fromActorName.GetFirstName())
	commonFields.Title = p.config.RecurringPaymentNotificationParams.MandateUnpausedPayer.Title
	commonFields.Deeplink = &deeplink.Deeplink{
		Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
			RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
				RecurringPaymentId: recurringPayment.GetId(),
			},
		},
	}
	commonFields.IconAttributes = convertToFCMIconAttribute(p.config.RecurringPaymentNotificationParams.MandateUnpausedPayer.IconAttr)
	return commonFields, p.config.RecurringPaymentNotificationParams.MandateUnpausedPayer.NotificationType.ToFCMNotificationTypeEnum(),
		p.config.RecurringPaymentNotificationParams.MandateUnpausedPayer.NotificationExpiry
}

// getMandateCreatedNotificationTemplate - gets notification template in case mandate is created
func (p *Processor) getMandateCreatedNotificationTemplate(recurringPayment *rpPb.RecurringPayment,
	toActorEntity *actorPb.GetEntityDetailsByActorIdResponse) (*fcm.CommonTemplateFields, fcm.NotificationType, time.Duration) {
	commonFields := &fcm.CommonTemplateFields{}
	commonFields.Body = fmt.Sprintf(p.config.RecurringPaymentNotificationParams.MandateCreationPayer.Body,
		p.getMandateFrequency(int(recurringPayment.GetRecurrenceRule().GetAllowedFrequency())),
		toActorEntity.GetName().GetFirstName(), money.ToDisplayStringWithINRSymbol(recurringPayment.GetAmount()))
	commonFields.Title = p.config.RecurringPaymentNotificationParams.MandateCreationPayer.Title
	commonFields.Deeplink = &deeplink.Deeplink{
		Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
			RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
				RecurringPaymentId: recurringPayment.GetId(),
			},
		},
	}
	commonFields.IconAttributes = convertToFCMIconAttribute(p.config.RecurringPaymentNotificationParams.MandateCreationPayer.IconAttr)
	return commonFields, p.config.RecurringPaymentNotificationParams.MandateCreationPayer.NotificationType.ToFCMNotificationTypeEnum(),
		p.config.RecurringPaymentNotificationParams.MandateCreationPayer.NotificationExpiry
}

// getFcmNotificationsCommonFields accepts notification template and a template params map for template and returns
// the formatted notification template after replacing the params in the template with their actual values
func (p *Processor) getFormattedNotificationTemplate(notificationTemplate payPkg.NotificationTemplateParams, recurringPaymentId string, templateParamsMap map[string]string) *fcm.CommonTemplateFields {
	commonFields := &fcm.CommonTemplateFields{}

	if notificationTemplate.Body == "" {
		return nil
	}

	for paramName, paramValue := range templateParamsMap {
		notificationTemplate.Body = strings.ReplaceAll(notificationTemplate.Body, paramName, paramValue)
	}
	for paramName, paramValue := range templateParamsMap {
		notificationTemplate.Title = strings.ReplaceAll(notificationTemplate.Title, paramName, paramValue)
	}
	commonFields.Title = notificationTemplate.Title
	commonFields.Body = notificationTemplate.Body
	commonFields.Deeplink = &deeplink.Deeplink{
		Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
			RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
				RecurringPaymentId: recurringPaymentId,
			},
		},
	}
	commonFields.IconAttributes = convertToFCMIconAttribute(notificationTemplate.IconAttr)

	return commonFields
}

// getMandateNotificationTemplate - generates Notification template for mandates
func (p *Processor) getMandateNotificationTemplate(ctx context.Context, recurringPayment *rpPb.RecurringPayment,
	fromActorEntity, toActorEntity *actorPb.GetEntityDetailsByActorIdResponse, action rpPb.Action) []*commspb.Communication {
	lg := activity.GetLogger(ctx)
	// All the notification are being send to fromActor/PAYER.
	var (
		commonFields *fcm.CommonTemplateFields
		notifType    fcm.NotificationType
		notifTTL     time.Duration
	)
	switch {
	case action == rpPb.Action_CREATE:
		switch {
		// Mandate Received
		case recurringPayment.GetState() == rpPb.RecurringPaymentState_CREATION_INITIATED:
			return p.getMandateReceivedNotificationTemplates(recurringPayment, toActorEntity)
			// Mandate Approved
		case recurringPayment.GetState() == rpPb.RecurringPaymentState_CREATION_AUTHORISED:
			commonFields, notifType, notifTTL = p.getMandateApprovedNotificationTemplate(recurringPayment, toActorEntity)
			// Mandate Declined
		case recurringPayment.GetState() == rpPb.RecurringPaymentState_FAILED:
			commonFields, notifType, notifTTL = p.getMandateDeclinedNotificationTemplate(recurringPayment, toActorEntity)
		// Mandate Created
		case recurringPayment.GetState() == rpPb.RecurringPaymentState_ACTIVATED:
			commonFields, notifType, notifTTL = p.getMandateCreatedNotificationTemplate(recurringPayment, toActorEntity)
		default:
			// no Notifications need to be sent
			lg.Info("no notification template for this recurringPayment state",
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
			return nil
		}

	case action == rpPb.Action_MODIFY && recurringPayment.GetState() == rpPb.RecurringPaymentState_ACTIVATED:
		commonFields, notifType, notifTTL = p.getMandateModifiedNotificationTemplate(recurringPayment, toActorEntity)

	case action == rpPb.Action_REVOKE:
		commonFields, notifType, notifTTL = p.getMandateRevokedNotificationTemplate(recurringPayment, toActorEntity)

	case action == rpPb.Action_PAUSE:
		commonFields, notifType, notifTTL = p.getMandatePausedNotificationTemplate(recurringPayment)

	case action == rpPb.Action_UNPAUSE:
		commonFields, notifType, notifTTL = p.getMandateUnpausedNotificationTemplate(recurringPayment, fromActorEntity.GetName())

	default:
		lg.Info("no notification template for this recurring payment action",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		return nil
	}
	fcmNotificationWithCommonFields := initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL)
	return []*commspb.Communication{
		{
			Message: &commspb.Communication_Notification{
				Notification: &commspb.NotificationMessage{
					Notification: fcmNotificationWithCommonFields,
				},
			},
			Medium: commspb.Medium_NOTIFICATION,
		},
	}
}

func (p *Processor) getEnachMandateNotificationTemplate(ctx context.Context, recurringPayment *rpPb.RecurringPayment,
	toActorEntity *actorPb.GetEntityDetailsByActorIdResponse, recurringPaymentAction *rpPb.RecurringPaymentsAction) []*commspb.Communication {
	lg := activity.GetLogger(ctx)

	var (
		commonFields         *fcm.CommonTemplateFields
		notificationTemplate payPkg.NotificationTemplateParams
	)

	templateConfigMap := map[string]string{
		"BENEFICIARY_NAME":            toActorEntity.GetName().ToString(),
		"RECURRING_PAYMENT_AMOUNT":    money.ToDisplayStringWithINRSymbol(recurringPayment.GetAmount()),
		"RECURRING_PAYMENT_FREQUENCY": p.getMandateFrequency(int(recurringPayment.GetRecurrenceRule().GetAllowedFrequency())),
	}

	switch recurringPaymentAction.GetAction() {
	case rpPb.Action_CREATE:
		switch recurringPayment.GetState() {
		case rpPb.RecurringPaymentState_FAILED:
			notificationTemplate = p.config.RecurringPaymentNotificationParams.EnachCreationFailurePayer
		case rpPb.RecurringPaymentState_ACTIVATED:
			notificationTemplate = p.config.RecurringPaymentNotificationParams.EnachCreationSuccessPayer
		default:
			lg.Info("no notification template for this recurring payment state",
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
			return nil
		}
	case rpPb.Action_EXECUTE:
		switch recurringPaymentAction.GetState() {
		case rpPb.ActionState_ACTION_FAILURE:
			notificationTemplate = p.config.RecurringPaymentNotificationParams.EnachExecutionFailurePayer
		case rpPb.ActionState_ACTION_SUCCESS:
			notificationTemplate = p.config.RecurringPaymentNotificationParams.EnachExecutionSuccessPayer
		default:
			lg.Info("no notification template for this recurring payment action execution state", zap.String(logger.ID, recurringPaymentAction.GetId()))
			return nil
		}
	case rpPb.Action_REVOKE:
		switch recurringPaymentAction.GetState() {
		case rpPb.ActionState_ACTION_FAILURE:
			notificationTemplate = p.config.RecurringPaymentNotificationParams.EnachRevokeFailure
		case rpPb.ActionState_ACTION_SUCCESS:
			notificationTemplate = p.config.RecurringPaymentNotificationParams.EnachRevokeSuccess
		default:
			lg.Info("no notification template for this recurring payment action revoke state", zap.String(logger.ID, recurringPaymentAction.GetId()))
			return nil
		}
	default:
		lg.Info("no notification template for this recurring payment action type", zap.String(logger.ID, recurringPaymentAction.GetId()))
		return nil
	}

	commonFields = p.getFormattedNotificationTemplate(notificationTemplate, recurringPayment.GetId(), templateConfigMap)

	// Gracefully handing the case where commonFields are nil.
	if commonFields == nil {
		lg.Info("Common fields are not found for recurring payment action", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, recurringPaymentAction.GetId()))
		return nil
	}

	fcmNotificationWithCommonFields := initFcmNotificationWithCommonFields(commonFields, notificationTemplate.NotificationType.ToFCMNotificationTypeEnum(), notificationTemplate.NotificationExpiry)
	return []*commspb.Communication{
		{
			Message: &commspb.Communication_Notification{
				Notification: &commspb.NotificationMessage{
					Notification: fcmNotificationWithCommonFields,
				},
			},
			Medium: commspb.Medium_NOTIFICATION,
		},
	}
}

// convertToFCMIconAttribute - converts config based icon attributes to fcm icon attributes
func convertToFCMIconAttribute(iconAttr *payPkg.IconAttribute) *fcm.IconAttributes {
	if iconAttr == nil {
		return nil
	}
	return &fcm.IconAttributes{
		IconUrl:         iconAttr.IconURL,
		IconName:        iconAttr.IconName,
		BackgroundColor: iconAttr.ColourCode,
	}
}

// initFcmNotificationWithCommonFields initializes fcm notification with common fields based on notification type
func initFcmNotificationWithCommonFields(commonFields *fcm.CommonTemplateFields, notificationType fcm.NotificationType, notifTTL time.Duration) *fcm.Notification {
	notification := &fcm.Notification{}

	if notifTTL != 0 {
		commonFields.ExpireAt = timestamppb.New(time.Now().Add(notifTTL))
	}

	switch notificationType { //nolint:exhaustive
	case fcm.NotificationType_IN_APP:
		notification.NotificationTemplates = &fcm.Notification_InAppTemplate{
			InAppTemplate: &fcm.InAppTemplate{
				CommonTemplateFields: commonFields,
				AfterClickAction:     fcm.AfterClickAction_PERSIST,
				// TODO(89589) - move these values to config
				NotificationPriority: fcm.InAppNotificationPriority_NOTIFICATION_PRIORITY_CRITICAL,
			},
		}
	case fcm.NotificationType_FULL_SCREEN:
		notification.NotificationTemplates = &fcm.Notification_FullscreenTemplate{FullscreenTemplate: &fcm.FullScreenTemplate{CommonTemplateFields: commonFields}}
	case fcm.NotificationType_BACKGROUND:
		notification.NotificationTemplates = &fcm.Notification_BackgroundTemplate{BackgroundTemplate: &fcm.BackgroundTemplate{CommonTemplateFields: commonFields}}
	case fcm.NotificationType_SYSTEM_TRAY:
		notification.NotificationTemplates = &fcm.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcm.SystemTrayTemplate{CommonTemplateFields: commonFields}}
	}

	notification.NotificationType = notificationType
	return notification
}
