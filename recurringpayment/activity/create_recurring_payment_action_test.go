package activity_test

import (
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/pkg/epifierrors"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
)

func TestProcessor_CreateRecurringPaymentAction(t *testing.T) {
	const (
		defaultRecurringPaymentId       = "default_test_recurring_payment_id"
		defaultRecurringPaymentActionId = "default_test_recurring_payment_action_id"
		defaultClientReqId              = "default_test_client_req_id"
		defaultVendorReqId              = "default_test_vendor_req_id"
	)

	var (
		defaultRecurringPaymentAction = &rpPb.RecurringPaymentsAction{
			RecurringPaymentId: defaultRecurringPaymentId,
			ClientRequestId:    defaultClientReqId,
			Action:             rpPb.Action_CREATE,
			State:              rpPb.ActionState_ACTION_CREATED,
			VendorRequestId:    defaultVendorReqId,
			InitiatedBy:        rpPb.InitiatedBy_PAYER,
		}
		defaultCreatedRecurringPaymentAction = &rpPb.RecurringPaymentsAction{
			Id:                 defaultRecurringPaymentActionId,
			RecurringPaymentId: defaultRecurringPaymentId,
			ClientRequestId:    defaultClientReqId,
			Action:             rpPb.Action_CREATE,
			State:              rpPb.ActionState_ACTION_CREATED,
			VendorRequestId:    defaultVendorReqId,
			InitiatedBy:        rpPb.InitiatedBy_PAYER,
		}
	)

	type mockRecurringPaymentActionDaoGetByClientRequestId struct {
		enable                 bool
		clientRequestId        string
		recurringPaymentAction *rpPb.RecurringPaymentsAction
		err                    error
	}
	type mockRecurringPaymentActionDaoCreate struct {
		enable                        bool
		recurringPaymentAction        *rpPb.RecurringPaymentsAction
		createdRecurringPaymentAction *rpPb.RecurringPaymentsAction
		err                           error
	}

	act, md, assertTest := newProcessorWithMocks(t)
	defer assertTest()
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(act)

	tests := []struct {
		name                                              string
		req                                               *rpActivityPb.CreateRecurringPaymentActionRequest
		mockRecurringPaymentActionDaoGetByClientRequestId mockRecurringPaymentActionDaoGetByClientRequestId
		mockRecurringPaymentActionDaoCreate               mockRecurringPaymentActionDaoCreate
		want                                              *rpActivityPb.CreateRecurringPaymentActionResponse
		wantErr                                           bool
	}{
		{
			name: "Pass if recurring payment action already exists",
			req: &rpActivityPb.CreateRecurringPaymentActionRequest{
				RecurringPaymentAction: defaultRecurringPaymentAction,
			},
			mockRecurringPaymentActionDaoGetByClientRequestId: mockRecurringPaymentActionDaoGetByClientRequestId{
				enable:                 true,
				clientRequestId:        defaultClientReqId,
				recurringPaymentAction: defaultCreatedRecurringPaymentAction,
				err:                    nil,
			},
			want: &rpActivityPb.CreateRecurringPaymentActionResponse{
				RecurringPaymentAction: defaultCreatedRecurringPaymentAction,
			},
			wantErr: false,
		},
		{
			name: "Error if unable to fetch recurring payment by external id",
			req: &rpActivityPb.CreateRecurringPaymentActionRequest{
				RecurringPaymentAction: defaultRecurringPaymentAction,
			},
			mockRecurringPaymentActionDaoGetByClientRequestId: mockRecurringPaymentActionDaoGetByClientRequestId{
				enable:                 true,
				clientRequestId:        defaultClientReqId,
				recurringPaymentAction: nil,
				err:                    epifierrors.ErrPermanent,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Error if unable to create recurring payment",
			req: &rpActivityPb.CreateRecurringPaymentActionRequest{
				RecurringPaymentAction: defaultRecurringPaymentAction,
			},
			mockRecurringPaymentActionDaoGetByClientRequestId: mockRecurringPaymentActionDaoGetByClientRequestId{
				enable:                 true,
				clientRequestId:        defaultClientReqId,
				recurringPaymentAction: nil,
				err:                    epifierrors.ErrRecordNotFound,
			},
			mockRecurringPaymentActionDaoCreate: mockRecurringPaymentActionDaoCreate{
				enable:                        true,
				recurringPaymentAction:        defaultRecurringPaymentAction,
				createdRecurringPaymentAction: nil,
				err:                           epifierrors.ErrPermanent,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Success if recurring payment created successfully",
			req: &rpActivityPb.CreateRecurringPaymentActionRequest{
				RecurringPaymentAction: defaultRecurringPaymentAction,
			},
			mockRecurringPaymentActionDaoGetByClientRequestId: mockRecurringPaymentActionDaoGetByClientRequestId{
				enable:                 true,
				clientRequestId:        defaultClientReqId,
				recurringPaymentAction: nil,
				err:                    epifierrors.ErrRecordNotFound,
			},
			mockRecurringPaymentActionDaoCreate: mockRecurringPaymentActionDaoCreate{
				enable:                        true,
				recurringPaymentAction:        defaultRecurringPaymentAction,
				createdRecurringPaymentAction: defaultCreatedRecurringPaymentAction,
				err:                           nil,
			},
			want: &rpActivityPb.CreateRecurringPaymentActionResponse{
				RecurringPaymentAction: defaultCreatedRecurringPaymentAction,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockRecurringPaymentActionDaoGetByClientRequestId.enable {
				md.recurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), tt.mockRecurringPaymentActionDaoGetByClientRequestId.clientRequestId, false).
					Return(tt.mockRecurringPaymentActionDaoGetByClientRequestId.recurringPaymentAction, tt.mockRecurringPaymentActionDaoGetByClientRequestId.err)
			}
			if tt.mockRecurringPaymentActionDaoCreate.enable {
				md.recurringPaymentActionDao.EXPECT().Create(gomock.Any(), tt.mockRecurringPaymentActionDaoCreate.recurringPaymentAction).
					Return(tt.mockRecurringPaymentActionDaoCreate.createdRecurringPaymentAction, tt.mockRecurringPaymentActionDaoCreate.err)
			}

			result := &rpActivityPb.CreateRecurringPaymentActionResponse{}
			got, err := env.ExecuteActivity(rpNs.CreateRecurringPaymentAction, tt.req)

			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("CreateRecurringPaymentAction() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			if (err != nil) != tt.wantErr {
				t.Errorf("CreateRecurringPaymentAction() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !proto.Equal(result, tt.want) {
				t.Errorf("CreateRecurringPaymentAction() got = %v, want %v", result, tt.want)
				return
			}

			assertTest()

		})
	}
}
