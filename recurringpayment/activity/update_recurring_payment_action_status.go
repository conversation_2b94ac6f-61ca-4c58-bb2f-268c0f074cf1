// nolint:dupl
package activity

import (
	"context"
	"errors"
	"fmt"

	"github.com/epifi/be-common/pkg/epifierrors"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
)

func (p *Processor) UpdateRecurringPaymentActionStatus(ctx context.Context, req *rpActivityPb.UpdateRecurringPaymentActionStatusRequest) (*rpActivityPb.UpdateRecurringPaymentActionStatusResponse, error) {
	// todo: Add support for field mask for updating other fields if needed
	res := &rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}
	recurringPaymentActionEntry, err := p.recurringPaymentsActionDao.GetById(ctx, req.GetRecurringPaymentActionId())
	if err != nil {
		return nil, fmt.Errorf("error while trying to fetch recurring payment action by id, err: %s %w", err, epifierrors.ErrTransient)
	}
	// Checking if the Recurring Payment Action is already in updated state.
	// This is required for idempotency of activity
	if isRecurringPaymentActionStatusAlreadyUpdated(recurringPaymentActionEntry, req.GetNextStatus()) {
		return res, nil
	}

	updateMask := []rpPb.RecurringPaymentActionFieldMask{}
	// update Action detailed status info if present
	if req.GetActionDetailedStatusInfo() != nil {
		recurringPaymentActionEntry.ActionDetailedStatusInfo = req.GetActionDetailedStatusInfo()
		updateMask = append(updateMask, rpPb.RecurringPaymentActionFieldMask_ACTION_DETAILED_STATUS_INFO)
	}

	if err = p.recurringPaymentsActionDao.UpdateAndChangeStatus(ctx, recurringPaymentActionEntry, updateMask, req.GetCurrentStatus(), req.GetNextStatus()); err != nil {
		if errors.Is(err, epifierrors.ErrNoRowsAffected) {
			return nil, fmt.Errorf("error while trying to Update Recurring Payment Action Status, err: %s %w", err, epifierrors.ErrPermanent)
		}
		return nil, fmt.Errorf("error while trying to Update Recurring Payment Action Status, err: %s %w", err, epifierrors.ErrTransient)
	}
	return res, nil
}

func isRecurringPaymentActionStatusAlreadyUpdated(recurringPaymentAction *rpPb.RecurringPaymentsAction, nextState rpPb.ActionState) bool {
	return recurringPaymentAction.GetState() == nextState
}
