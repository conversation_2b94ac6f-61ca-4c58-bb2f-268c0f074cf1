package activity_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"testing"

	"github.com/golang/mock/gomock"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"

	rpcPb "github.com/epifi/be-common/api/rpc"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
)

func TestProcessor_UpdateTransaction(t *testing.T) {
	const (
		defaultTransactionId      = "default_test_transaction_id"
		defaultOrderId            = "default_test_order_id"
		defaultPiFrom             = "default_test_pi_from"
		defaultPiTo               = "default_test_pi_to"
		defaultTransactionRemarks = "default_test_transaction_remarks"
		defaultDedupeRequestId    = "default_test_dedupe_request_id"
	)

	var (
		defaultTransactionAmount = &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        510,
		}
		defaultTransaction = &paymentPb.Transaction{
			Id:              defaultTransactionId,
			PiFrom:          defaultPiFrom,
			PiTo:            defaultPiTo,
			PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
			Amount:          defaultTransactionAmount,
			Status:          paymentPb.TransactionStatus_IN_PROGRESS,
			PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
			Remarks:         defaultTransactionRemarks,
			OrderId:         defaultOrderId,
			DedupeId: &paymentPb.DedupeId{
				RequestId: defaultDedupeRequestId,
			},
			Ownership: commontypes.Ownership_EPIFI_TECH,
		}
		defaultWrongTransaction = &paymentPb.Transaction{
			Id:              defaultTransactionId,
			PiFrom:          defaultPiFrom,
			PiTo:            defaultPiTo,
			PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
			Amount:          defaultTransactionAmount,
			Status:          paymentPb.TransactionStatus_FAILED,
			PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
			Remarks:         defaultTransactionRemarks,
			OrderId:         defaultOrderId,
			DedupeId: &paymentPb.DedupeId{
				RequestId: defaultDedupeRequestId,
			},
			Ownership: commontypes.Ownership_EPIFI_TECH,
		}

		defaultUpdatedTransaction = &paymentPb.Transaction{
			Id:              defaultTransactionId,
			PiFrom:          defaultPiFrom,
			PiTo:            defaultPiTo,
			PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
			Amount:          defaultTransactionAmount,
			Status:          paymentPb.TransactionStatus_SUCCESS,
			PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
			Remarks:         defaultTransactionRemarks,
			OrderId:         defaultOrderId,
			DedupeId: &paymentPb.DedupeId{
				RequestId: defaultDedupeRequestId,
			},
			Ownership: commontypes.Ownership_EPIFI_TECH,
		}
	)

	act, md, assertTest := newProcessorWithMocks(t)
	defer assertTest()
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(act)

	type mockPayClientUpdateAndChangeTransactionStatus struct {
		enable bool
		req    *payPb.UpdateAndChangeTransactionStatusRequest
		res    *payPb.UpdateAndChangeTransactionStatusResponse
		err    error
	}
	type mockPayClientGetTransactionByDedupeId struct {
		enable bool
		req    *payPb.GetTransactionByDedupeIdRequest
		res    *payPb.GetTransactionByDedupeIdResponse
		err    error
	}

	tests := []struct {
		name                                          string
		req                                           *rpActivityPb.UpdateTransactionRequest
		want                                          *rpActivityPb.UpdateTransactionResponse
		mockPayClientUpdateAndChangeTransactionStatus mockPayClientUpdateAndChangeTransactionStatus
		mockPayClientGetTransactionByDedupeId         mockPayClientGetTransactionByDedupeId
		wantErr                                       bool
	}{
		{
			name: "Error if UpdateTransaction RPC returned internal error",
			req: &rpActivityPb.UpdateTransactionRequest{
				Transaction:   defaultTransaction,
				CurrentStatus: paymentPb.TransactionStatus_IN_PROGRESS,
				NextStatus:    paymentPb.TransactionStatus_SUCCESS,
			},
			mockPayClientUpdateAndChangeTransactionStatus: mockPayClientUpdateAndChangeTransactionStatus{
				enable: true,
				req: &payPb.UpdateAndChangeTransactionStatusRequest{
					Transaction:   defaultTransaction,
					CurrentStatus: paymentPb.TransactionStatus_IN_PROGRESS,
					NextStatus:    paymentPb.TransactionStatus_SUCCESS,
				},
				res: &payPb.UpdateAndChangeTransactionStatusResponse{},
				err: epifierrors.ErrPermanent,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Error if GetTransactionByDedupeId RPC returned err",
			req: &rpActivityPb.UpdateTransactionRequest{
				Transaction:   defaultTransaction,
				CurrentStatus: paymentPb.TransactionStatus_IN_PROGRESS,
				NextStatus:    paymentPb.TransactionStatus_SUCCESS,
			},
			mockPayClientUpdateAndChangeTransactionStatus: mockPayClientUpdateAndChangeTransactionStatus{
				enable: true,
				req: &payPb.UpdateAndChangeTransactionStatusRequest{
					Transaction:   defaultTransaction,
					CurrentStatus: paymentPb.TransactionStatus_IN_PROGRESS,
					NextStatus:    paymentPb.TransactionStatus_SUCCESS,
				},
				res: &payPb.UpdateAndChangeTransactionStatusResponse{
					Status: rpcPb.StatusInternal(),
				},
				err: nil,
			},
			mockPayClientGetTransactionByDedupeId: mockPayClientGetTransactionByDedupeId{
				enable: true,
				req: &payPb.GetTransactionByDedupeIdRequest{
					DedupeId: defaultTransaction.GetDedupeId(),
				},
				res: &payPb.GetTransactionByDedupeIdResponse{
					Status: rpcPb.StatusInternal(),
				},
				err: nil,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Error if GetTransactionByDedupeId RPC returned wrong transaction",
			req: &rpActivityPb.UpdateTransactionRequest{
				Transaction:   defaultTransaction,
				CurrentStatus: paymentPb.TransactionStatus_IN_PROGRESS,
				NextStatus:    paymentPb.TransactionStatus_SUCCESS,
			},
			mockPayClientUpdateAndChangeTransactionStatus: mockPayClientUpdateAndChangeTransactionStatus{
				enable: true,
				req: &payPb.UpdateAndChangeTransactionStatusRequest{
					Transaction:   defaultTransaction,
					CurrentStatus: paymentPb.TransactionStatus_IN_PROGRESS,
					NextStatus:    paymentPb.TransactionStatus_SUCCESS,
				},
				res: &payPb.UpdateAndChangeTransactionStatusResponse{
					Status: rpcPb.StatusInternal(),
				},
				err: nil,
			},
			mockPayClientGetTransactionByDedupeId: mockPayClientGetTransactionByDedupeId{
				enable: true,
				req: &payPb.GetTransactionByDedupeIdRequest{
					DedupeId: defaultTransaction.GetDedupeId(),
				},
				res: &payPb.GetTransactionByDedupeIdResponse{
					Status:      rpcPb.StatusOk(),
					Transaction: defaultWrongTransaction,
				},
				err: nil,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Success if GetTransactionByDedupeId RPC returned correct transaction",
			req: &rpActivityPb.UpdateTransactionRequest{
				Transaction:   defaultTransaction,
				CurrentStatus: paymentPb.TransactionStatus_IN_PROGRESS,
				NextStatus:    paymentPb.TransactionStatus_SUCCESS,
			},
			mockPayClientUpdateAndChangeTransactionStatus: mockPayClientUpdateAndChangeTransactionStatus{
				enable: true,
				req: &payPb.UpdateAndChangeTransactionStatusRequest{
					Transaction:   defaultTransaction,
					CurrentStatus: paymentPb.TransactionStatus_IN_PROGRESS,
					NextStatus:    paymentPb.TransactionStatus_SUCCESS,
				},
				res: &payPb.UpdateAndChangeTransactionStatusResponse{
					Status: rpcPb.StatusInternal(),
				},
				err: nil,
			},
			mockPayClientGetTransactionByDedupeId: mockPayClientGetTransactionByDedupeId{
				enable: true,
				req: &payPb.GetTransactionByDedupeIdRequest{
					DedupeId: defaultTransaction.GetDedupeId(),
				},
				res: &payPb.GetTransactionByDedupeIdResponse{
					Status:      rpcPb.StatusOk(),
					Transaction: defaultUpdatedTransaction,
				},
				err: nil,
			},
			want:    &rpActivityPb.UpdateTransactionResponse{},
			wantErr: false,
		},
		{
			name: "Success if UpdateTransaction RPC returned success",
			req: &rpActivityPb.UpdateTransactionRequest{
				Transaction:   defaultTransaction,
				CurrentStatus: paymentPb.TransactionStatus_IN_PROGRESS,
				NextStatus:    paymentPb.TransactionStatus_SUCCESS,
			},
			mockPayClientUpdateAndChangeTransactionStatus: mockPayClientUpdateAndChangeTransactionStatus{
				enable: true,
				req: &payPb.UpdateAndChangeTransactionStatusRequest{
					Transaction:   defaultTransaction,
					CurrentStatus: paymentPb.TransactionStatus_IN_PROGRESS,
					NextStatus:    paymentPb.TransactionStatus_SUCCESS,
				},
				res: &payPb.UpdateAndChangeTransactionStatusResponse{
					Status: rpcPb.StatusOk(),
				},
				err: nil,
			},
			want:    &rpActivityPb.UpdateTransactionResponse{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			if tt.mockPayClientUpdateAndChangeTransactionStatus.enable {
				md.payClient.EXPECT().UpdateAndChangeTransactionStatus(gomock.Any(), gomock.Any()).
					Return(tt.mockPayClientUpdateAndChangeTransactionStatus.res, tt.mockPayClientUpdateAndChangeTransactionStatus.err)
			}
			if tt.mockPayClientGetTransactionByDedupeId.enable {
				md.payClient.EXPECT().GetTransactionByDedupeId(gomock.Any(), tt.mockPayClientGetTransactionByDedupeId.req).
					Return(tt.mockPayClientGetTransactionByDedupeId.res, tt.mockPayClientGetTransactionByDedupeId.err)
			}

			result := &rpActivityPb.UpdateTransactionResponse{}
			got, err := env.ExecuteActivity(rpNs.UpdateTransaction, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("UpdateTransaction() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateTransaction() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !proto.Equal(result, tt.want) {
				t.Errorf("UpdateTransaction() got = %v, want %v", result, tt.want)
				return
			}

			assertTest()
		})
	}
}
