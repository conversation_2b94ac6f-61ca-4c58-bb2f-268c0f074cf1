package activity_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	actorPb "github.com/epifi/gamma/api/actor"
	notificationPb "github.com/epifi/gamma/api/celestial/activity/notification"
	commspb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/fcm"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	types "github.com/epifi/gamma/api/typesv2"
	payPkg "github.com/epifi/gamma/pkg/pay"
)

var (
	mandateFrequencyMap = map[int]string{
		2:  "day",
		3:  "week",
		4:  "fortnight",
		5:  "month",
		6:  "bi month",
		7:  "quarter",
		8:  "half year",
		9:  "year",
		10: "set frequency",
	}
	recurringPaymentSIActivated = &rpPb.RecurringPayment{
		Id:                       "rp_id",
		FromActorId:              "from_actor_id",
		ToActorId:                "to_actor_id",
		Type:                     rpPb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:                   "",
		PiTo:                     "",
		Amount:                   moneyPkg.ParseFloat(200.0, moneyPkg.RupeeCurrencyCode),
		Interval:                 nil,
		RecurrenceRule:           nil,
		MaximumAllowedTxns:       0,
		PartnerBank:              0,
		PreferredPaymentProtocol: 0,
		State:                    rpPb.RecurringPaymentState_ACTIVATED,
		Ownership:                rpPb.RecurringPaymentOwnership_EPIFI_TECH,
		InitiatedBy:              rpPb.InitiatedBy_PAYEE,
		AmountType:               rpPb.AmountType_EXACT,
		ShareToPayee:             false,
	}
	recurringPaymentSIFailed = &rpPb.RecurringPayment{
		Id:                       "rp_id",
		FromActorId:              "from_actor_id",
		ToActorId:                "to_actor_id",
		Type:                     rpPb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:                   "",
		PiTo:                     "",
		Amount:                   moneyPkg.ParseFloat(200.0, moneyPkg.RupeeCurrencyCode),
		Interval:                 nil,
		RecurrenceRule:           nil,
		MaximumAllowedTxns:       0,
		PartnerBank:              0,
		PreferredPaymentProtocol: 0,
		State:                    rpPb.RecurringPaymentState_FAILED,
		Ownership:                rpPb.RecurringPaymentOwnership_EPIFI_TECH,
		InitiatedBy:              rpPb.InitiatedBy_PAYEE,
		AmountType:               rpPb.AmountType_EXACT,
		ShareToPayee:             false,
	}
	recurringPaymentMandateActivated = &rpPb.RecurringPayment{
		Id:                       "rp_id",
		FromActorId:              "from_actor_id",
		ToActorId:                "to_actor_id",
		Type:                     rpPb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:                   "",
		PiTo:                     "",
		Amount:                   moneyPkg.ParseFloat(200.0, moneyPkg.RupeeCurrencyCode),
		Interval:                 nil,
		RecurrenceRule:           nil,
		MaximumAllowedTxns:       0,
		PartnerBank:              0,
		PreferredPaymentProtocol: 0,
		State:                    rpPb.RecurringPaymentState_ACTIVATED,
		Ownership:                rpPb.RecurringPaymentOwnership_EPIFI_TECH,
		InitiatedBy:              rpPb.InitiatedBy_PAYEE,
		AmountType:               rpPb.AmountType_EXACT,
		ShareToPayee:             false,
	}
	recurringPaymentMandateInitiated = &rpPb.RecurringPayment{
		Id:                       "rp_id",
		FromActorId:              "from_actor_id",
		ToActorId:                "to_actor_id",
		Type:                     rpPb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:                   "",
		PiTo:                     "",
		Amount:                   moneyPkg.ParseFloat(200.0, moneyPkg.RupeeCurrencyCode),
		Interval:                 nil,
		RecurrenceRule:           nil,
		MaximumAllowedTxns:       0,
		PartnerBank:              0,
		PreferredPaymentProtocol: 0,
		State:                    rpPb.RecurringPaymentState_CREATION_INITIATED,
		Ownership:                rpPb.RecurringPaymentOwnership_EPIFI_TECH,
		InitiatedBy:              rpPb.InitiatedBy_PAYEE,
		AmountType:               rpPb.AmountType_EXACT,
		ShareToPayee:             false,
	}
	recurringPaymentMandateFailed = &rpPb.RecurringPayment{
		Id:          "rp_id",
		FromActorId: "from_actor_id",
		ToActorId:   "to_actor_id",
		Type:        rpPb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "",
		PiTo:        "",
		Amount:      moneyPkg.ParseFloat(200.0, moneyPkg.RupeeCurrencyCode),
		Interval:    nil,
		RecurrenceRule: &rpPb.RecurrenceRule{AllowedFrequency: rpPb.AllowedFrequency_DAILY,
			RuleType: rpPb.RecurrenceRule_ON},
		MaximumAllowedTxns:       0,
		PartnerBank:              0,
		PreferredPaymentProtocol: 0,
		State:                    rpPb.RecurringPaymentState_FAILED,
		Ownership:                rpPb.RecurringPaymentOwnership_EPIFI_TECH,
		InitiatedBy:              rpPb.InitiatedBy_PAYEE,
		AmountType:               rpPb.AmountType_EXACT,
		ShareToPayee:             false,
	}
	recurringPaymentMandatePaused = &rpPb.RecurringPayment{
		Id:                       "rp_id",
		FromActorId:              "from_actor_id",
		ToActorId:                "to_actor_id",
		Type:                     rpPb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:                   "",
		PiTo:                     "",
		Amount:                   moneyPkg.ParseFloat(200.0, moneyPkg.RupeeCurrencyCode),
		Interval:                 nil,
		RecurrenceRule:           nil,
		MaximumAllowedTxns:       0,
		PartnerBank:              0,
		PreferredPaymentProtocol: 0,
		State:                    rpPb.RecurringPaymentState_PAUSED,
		Ownership:                rpPb.RecurringPaymentOwnership_EPIFI_TECH,
		InitiatedBy:              rpPb.InitiatedBy_PAYEE,
		AmountType:               rpPb.AmountType_EXACT,
		ShareToPayee:             false,
	}
	toActorEntity = &actorPb.GetEntityDetailsByActorIdResponse{
		Status:   nil,
		EntityId: "entity-id-2",
		Name: &commontypes.Name{
			FirstName:  "first-to",
			MiddleName: "",
			LastName:   "last-to",
			Honorific:  "",
		},
		MobileNumber: &commontypes.PhoneNumber{
			CountryCode:    +91,
			NationalNumber: **********,
		},
		ProfileImageUrl: "",
		EmailId:         "mailId-to",
		Entity:          nil,
		LegalName:       nil,
		Type:            types.ActorType_USER,
	}

	fromActorEntity = &actorPb.GetEntityDetailsByActorIdResponse{
		Status:   nil,
		EntityId: "entity-id-1",
		Name: &commontypes.Name{
			FirstName:  "first-from",
			MiddleName: "",
			LastName:   "last-from",
			Honorific:  "",
		},
		MobileNumber: &commontypes.PhoneNumber{
			CountryCode:    +91,
			NationalNumber: **********,
		},
		ProfileImageUrl: "",
		EmailId:         "mailId-from",
		Entity:          nil,
		LegalName:       nil,
		Type:            types.ActorType_USER,
	}
)

func getMandateFrequency(allowedFrequency int) string {
	if freq, ok := mandateFrequencyMap[allowedFrequency]; ok {
		return freq
	}
	return "set frequency"
}

func assertExpiry(result, want *notificationPb.GetTemplatesResponse) {
	for idx, comm := range result.GetNotifications().GetCommunicationList() {
		if comm.GetNotification().GetNotification().GetSystemTrayTemplate().GetCommonTemplateFields().GetExpireAt() != nil {
			comm.GetNotification().GetNotification().GetSystemTrayTemplate().GetCommonTemplateFields().ExpireAt =
				want.GetNotifications().GetCommunicationList()[idx].GetNotification().GetNotification().GetSystemTrayTemplate().GetCommonTemplateFields().GetExpireAt()
		}
		if comm.GetNotification().GetNotification().GetInAppTemplate().GetCommonTemplateFields().GetExpireAt() != nil {
			comm.GetNotification().GetNotification().GetInAppTemplate().GetCommonTemplateFields().ExpireAt =
				want.GetNotifications().GetCommunicationList()[idx].GetNotification().GetNotification().GetInAppTemplate().GetCommonTemplateFields().GetExpireAt()
		}
	}
}

// convertToFCMIconAttribute - converts config based icon attributes to fcm icon attributes
func convertToFCMIconAttribute(iconAttr *payPkg.IconAttribute) *fcm.IconAttributes {
	if iconAttr == nil {
		return nil
	}
	return &fcm.IconAttributes{
		IconUrl:         iconAttr.IconURL,
		IconName:        iconAttr.IconName,
		BackgroundColor: iconAttr.ColourCode,
	}
}

// initFcmNotificationWithCommonFields initializes fcm notification with common fields based on notification type
func initFcmNotificationWithCommonFields(commonFields *fcm.CommonTemplateFields, notificationType fcm.NotificationType, notifTTL time.Duration) *fcm.Notification {
	notification := &fcm.Notification{}

	if notifTTL != 0 {
		commonFields.ExpireAt = timestamppb.New(time.Now().Add(notifTTL))
	}

	switch notificationType { //nolint:exhaustive
	case fcm.NotificationType_IN_APP:
		notification.NotificationTemplates = &fcm.Notification_InAppTemplate{
			InAppTemplate: &fcm.InAppTemplate{
				CommonTemplateFields: commonFields,
				AfterClickAction:     fcm.AfterClickAction_PERSIST,
				NotificationPriority: fcm.InAppNotificationPriority_NOTIFICATION_PRIORITY_CRITICAL,
			},
		}
	case fcm.NotificationType_FULL_SCREEN:
		notification.NotificationTemplates = &fcm.Notification_FullscreenTemplate{FullscreenTemplate: &fcm.FullScreenTemplate{CommonTemplateFields: commonFields}}
	case fcm.NotificationType_BACKGROUND:
		notification.NotificationTemplates = &fcm.Notification_BackgroundTemplate{BackgroundTemplate: &fcm.BackgroundTemplate{CommonTemplateFields: commonFields}}
	case fcm.NotificationType_SYSTEM_TRAY:
		notification.NotificationTemplates = &fcm.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcm.SystemTrayTemplate{CommonTemplateFields: commonFields}}
	}

	notification.NotificationType = notificationType
	return notification
}

func generateSIActivatedTemplateResponse() []*commspb.Communication {
	commonFields := &fcm.CommonTemplateFields{
		Title: conf.RecurringPaymentNotificationParams.SICreationPayer.Title,
		Body: fmt.Sprintf(conf.RecurringPaymentNotificationParams.SICreationPayer.Body,
			moneyPkg.ToDisplayStringWithINRSymbol(recurringPaymentSIActivated.GetAmount()),
			toActorEntity.GetName().GetFirstName(),
			conf.RecurringPaymentFrequencyMapping[recurringPaymentSIActivated.GetRecurrenceRule().GetAllowedFrequency().String()]),
		IconAttributes: convertToFCMIconAttribute(conf.RecurringPaymentNotificationParams.SICreationPayer.IconAttr),
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
				RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
					RecurringPaymentId: recurringPaymentSIActivated.GetId(),
				},
			},
		},
	}
	notifType := conf.RecurringPaymentNotificationParams.SICreationPayer.NotificationType.ToFCMNotificationTypeEnum()
	notifTTL := conf.RecurringPaymentNotificationParams.SICreationPayer.NotificationExpiry
	fcmNotificationWithCommonFields := initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL)

	smsContent := &commspb.Communication_Sms{
		Sms: &commspb.SMSMessage{
			SmsOption: &commspb.SmsOption{
				Option: &commspb.SmsOption_SiCreatedSmsOption{
					SiCreatedSmsOption: &commspb.SICreatedSmsOption{
						SmsType: commspb.SmsType_SI_CREATED,
						Option: &commspb.SICreatedSmsOption_SiCreatedV1{
							SiCreatedV1: &commspb.SICreatedSmsOptionV1{
								TemplateVersion: commspb.TemplateVersion_VERSION_V1,
								SiAmount:        recurringPaymentSIActivated.GetAmount(),
								SiFrequency:     conf.RecurringPaymentFrequencyMapping[recurringPaymentSIActivated.GetRecurrenceRule().GetAllowedFrequency().String()],
								PayeeName:       toActorEntity.GetName(),
							},
						},
					},
				},
			},
		},
	}
	return []*commspb.Communication{
		{
			Message: &commspb.Communication_Notification{
				Notification: &commspb.NotificationMessage{
					Notification: fcmNotificationWithCommonFields,
				},
			},
			Medium: commspb.Medium_NOTIFICATION,
		},
		{
			Message: smsContent,
			Medium:  commspb.Medium_SMS,
		},
	}
}

func generateSIFailedTemplateResponse() []*commspb.Communication {
	commonFields := &fcm.CommonTemplateFields{
		Title: conf.RecurringPaymentNotificationParams.SIDeclinedPayer.Title,
		Body: fmt.Sprintf(conf.RecurringPaymentNotificationParams.SIDeclinedPayer.Body, moneyPkg.ToDisplayStringWithINRSymbol(recurringPaymentSIFailed.GetAmount()),
			"user"),
		IconAttributes: convertToFCMIconAttribute(conf.RecurringPaymentNotificationParams.SIDeclinedPayer.IconAttr),
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
				RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
					RecurringPaymentId: recurringPaymentSIFailed.GetId(),
				},
			},
		},
	}
	notifType := conf.RecurringPaymentNotificationParams.SIDeclinedPayer.NotificationType.ToFCMNotificationTypeEnum()
	notifTTL := conf.RecurringPaymentNotificationParams.SIDeclinedPayer.NotificationExpiry
	fcmNotificationWithCommonFields := initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL)

	smsContent := &commspb.Communication_Sms{
		Sms: &commspb.SMSMessage{
			SmsOption: &commspb.SmsOption{
				Option: &commspb.SmsOption_SiDeclinedSmsOption{
					SiDeclinedSmsOption: &commspb.SIDeclinedSmsOption{
						SmsType: commspb.SmsType_SI_DECLINED,
						Option: &commspb.SIDeclinedSmsOption_SiDeclinedV1{
							SiDeclinedV1: &commspb.SIDeclinedSmsOptionV1{
								TemplateVersion: commspb.TemplateVersion_VERSION_V1,
								SiAmount:        recurringPaymentSIFailed.GetAmount(),
								PayeeName:       toActorEntity.GetName(),
							},
						},
					},
				},
			},
		},
	}
	return []*commspb.Communication{
		{
			Message: &commspb.Communication_Notification{
				Notification: &commspb.NotificationMessage{
					Notification: fcmNotificationWithCommonFields,
				},
			},
			Medium: commspb.Medium_NOTIFICATION,
		},
		{
			Message: smsContent,
			Medium:  commspb.Medium_SMS,
		},
	}
}

func generateMandateActivatedTemplateResponse() []*commspb.Communication {
	commonFields := &fcm.CommonTemplateFields{
		Title: conf.RecurringPaymentNotificationParams.MandateCreationPayer.Title,
		Body: fmt.Sprintf(conf.RecurringPaymentNotificationParams.MandateCreationPayer.Body,
			getMandateFrequency(int(recurringPaymentMandateActivated.GetRecurrenceRule().GetAllowedFrequency())),
			toActorEntity.GetName().GetFirstName(), moneyPkg.ToDisplayStringWithINRSymbol(recurringPaymentMandateActivated.GetAmount())),
		IconAttributes: convertToFCMIconAttribute(conf.RecurringPaymentNotificationParams.MandateCreationPayer.IconAttr),
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
				RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
					RecurringPaymentId: recurringPaymentMandateActivated.GetId(),
				},
			},
		},
	}
	notifType := conf.RecurringPaymentNotificationParams.MandateCreationPayer.NotificationType.ToFCMNotificationTypeEnum()
	notifTTL := conf.RecurringPaymentNotificationParams.MandateCreationPayer.NotificationExpiry
	fcmNotificationWithCommonFields := initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL)

	smsContent := &commspb.Communication_Sms{
		Sms: &commspb.SMSMessage{
			Message:  "",
			SenderId: "",
			SmsOption: &commspb.SmsOption{
				Option: &commspb.SmsOption_MandateCreatedSmsOption{
					MandateCreatedSmsOption: &commspb.MandateCreatedSmsOption{
						SmsType: commspb.SmsType_MANDATE_CREATED,
						Option: &commspb.MandateCreatedSmsOption_MandateCreatedV1{
							MandateCreatedV1: &commspb.MandateCreatedSmsOptionV1{
								TemplateVersion:  commspb.TemplateVersion_VERSION_V1,
								MandateAmount:    recurringPaymentMandateActivated.GetAmount(),
								PayeeName:        toActorEntity.GetName(),
								PayerName:        fromActorEntity.GetName(),
								MandateFrequency: conf.RecurringPaymentFrequencyMapping[recurringPaymentMandateActivated.GetRecurrenceRule().GetAllowedFrequency().String()],
							},
						},
					},
				},
			},
		},
	}

	return []*commspb.Communication{
		{
			Message: &commspb.Communication_Notification{
				Notification: &commspb.NotificationMessage{
					Notification: fcmNotificationWithCommonFields,
				},
			},
			Medium: commspb.Medium_NOTIFICATION,
		},
		{
			Message: smsContent,
			Medium:  commspb.Medium_SMS,
		},
	}
}

func generateMandateRevokedTemplateResponse() []*commspb.Communication {
	commonFields := &fcm.CommonTemplateFields{
		Title: conf.RecurringPaymentNotificationParams.MandateRevokedPayer.Title,
		Body: fmt.Sprintf(conf.RecurringPaymentNotificationParams.MandateRevokedPayer.Body,
			toActorEntity.GetName().GetFirstName(), moneyPkg.ToDisplayStringWithINRSymbol(recurringPaymentMandateActivated.GetAmount())),
		IconAttributes: convertToFCMIconAttribute(conf.RecurringPaymentNotificationParams.MandateRevokedPayer.IconAttr),
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
				RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
					RecurringPaymentId: recurringPaymentMandateActivated.GetId(),
				},
			},
		},
	}
	notifType := conf.RecurringPaymentNotificationParams.MandateRevokedPayer.NotificationType.ToFCMNotificationTypeEnum()
	notifTTL := conf.RecurringPaymentNotificationParams.MandateRevokedPayer.NotificationExpiry
	fcmNotificationWithCommonFields := initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL)

	smsContent := &commspb.Communication_Sms{
		Sms: &commspb.SMSMessage{
			SmsOption: &commspb.SmsOption{
				Option: &commspb.SmsOption_MandateRevokedSmsOption{
					MandateRevokedSmsOption: &commspb.MandateRevokedSmsOption{
						SmsType: commspb.SmsType_MANDATE_REVOKED,
						Option: &commspb.MandateRevokedSmsOption_MandateRevokedV1{
							MandateRevokedV1: &commspb.MandateRevokedSmsOptionV1{
								TemplateVersion: commspb.TemplateVersion_VERSION_V1,
								MandateAmount:   recurringPaymentMandateActivated.GetAmount(),
								PayeeName:       toActorEntity.GetName(),
							},
						},
					},
				},
			},
		},
	}
	return []*commspb.Communication{
		{
			Message: &commspb.Communication_Notification{
				Notification: &commspb.NotificationMessage{
					Notification: fcmNotificationWithCommonFields,
				},
			},
			Medium: commspb.Medium_NOTIFICATION,
		},
		{
			Message: smsContent,
			Medium:  commspb.Medium_SMS,
		},
	}
}

func generateMandateReceivedTemplateResponse() []*commspb.Communication {
	var (
		comms []*commspb.Communication
	)
	for _, notif := range conf.RecurringPaymentNotificationParams.MandateReceivedPayer {
		commonFields := &fcm.CommonTemplateFields{
			Title: notif.Title,
			Body: fmt.Sprintf(notif.Body,
				toActorEntity.GetName().GetFirstName(), moneyPkg.ToDisplayStringWithINRSymbol(recurringPaymentMandateActivated.GetAmount())),
			IconAttributes: convertToFCMIconAttribute(notif.IconAttr),
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_AUTHORIZE_RECURRING_PAYMENT_SCREEN,
				ScreenOptions: &deeplink.Deeplink_AuthorizeRecurringPaymentScreenOptions{
					AuthorizeRecurringPaymentScreenOptions: &deeplink.AuthorizeRecurringPaymentScreenOptions{
						RecurringPaymentId: recurringPaymentMandateInitiated.GetId(),
					},
				},
			},
		}
		if notif.NotificationType.ToFCMNotificationTypeEnum() == fcm.NotificationType_SYSTEM_TRAY {
			commonFields.NotificationReferenceId = recurringPaymentMandateInitiated.GetId() + "_DISMISSIBLE"
		}
		notifType := notif.NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := notif.NotificationExpiry
		fcmNotificationWithCommonFields := initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL)
		comms = append(comms, &commspb.Communication{
			Message: &commspb.Communication_Notification{
				Notification: &commspb.NotificationMessage{
					Notification: fcmNotificationWithCommonFields,
				},
			},
			Medium: commspb.Medium_NOTIFICATION,
		})
	}

	smsContent := &commspb.Communication_Sms{
		Sms: &commspb.SMSMessage{
			SmsOption: &commspb.SmsOption{
				Option: &commspb.SmsOption_MandateReceivedSmsOption{
					MandateReceivedSmsOption: &commspb.MandateReceivedSmsOption{
						SmsType: commspb.SmsType_MANDATE_RECEIVED,
						Option: &commspb.MandateReceivedSmsOption_MandateReceivedV1{
							MandateReceivedV1: &commspb.MandateReceivedSmsOptionV1{
								TemplateVersion: commspb.TemplateVersion_VERSION_V1,
								MandateAmount:   recurringPaymentMandateInitiated.GetAmount(),
								PayeeName:       toActorEntity.GetName(),
							},
						},
					},
				},
			},
		},
	}
	comms = append(comms, &commspb.Communication{
		Message: smsContent,
		Medium:  commspb.Medium_SMS,
	})
	return comms
}

func generateMandateModifiedTemplateResponse() []*commspb.Communication {
	commonFields := &fcm.CommonTemplateFields{
		Title: conf.RecurringPaymentNotificationParams.MandateModifiedPayer.Title,
		Body: fmt.Sprintf(conf.RecurringPaymentNotificationParams.MandateModifiedPayer.Body,
			toActorEntity.GetName().GetFirstName(), moneyPkg.ToDisplayStringWithINRSymbol(recurringPaymentMandateActivated.GetAmount())),
		IconAttributes: convertToFCMIconAttribute(conf.RecurringPaymentNotificationParams.MandateModifiedPayer.IconAttr),
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
				RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
					RecurringPaymentId: recurringPaymentMandateActivated.GetId(),
				},
			},
		},
	}
	notifType := conf.RecurringPaymentNotificationParams.MandateModifiedPayer.NotificationType.ToFCMNotificationTypeEnum()
	notifTTL := conf.RecurringPaymentNotificationParams.MandateModifiedPayer.NotificationExpiry
	fcmNotificationWithCommonFields := initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL)

	smsContent := &commspb.Communication_Sms{
		Sms: &commspb.SMSMessage{
			SmsOption: &commspb.SmsOption{
				Option: &commspb.SmsOption_MandateModifiedSmsOption{
					MandateModifiedSmsOption: &commspb.MandateModifiedSmsOption{
						SmsType: commspb.SmsType_MANDATE_MODIFIED,
						Option: &commspb.MandateModifiedSmsOption_MandateModifiedV1{
							MandateModifiedV1: &commspb.MandateModifiedSmsOptionV1{
								TemplateVersion: commspb.TemplateVersion_VERSION_V1,
								MandateAmount:   recurringPaymentMandateActivated.GetAmount(),
								PayeeName:       toActorEntity.GetName(),
							},
						},
					},
				},
			},
		},
	}
	return []*commspb.Communication{
		{
			Message: &commspb.Communication_Notification{
				Notification: &commspb.NotificationMessage{
					Notification: fcmNotificationWithCommonFields,
				},
			},
			Medium: commspb.Medium_NOTIFICATION,
		},
		{
			Message: smsContent,
			Medium:  commspb.Medium_SMS,
		},
	}
}
func generateMandateFailedTemplateResponse() []*commspb.Communication {
	commonFields := &fcm.CommonTemplateFields{
		Title: conf.RecurringPaymentNotificationParams.MandateDeclinedPayer.Title,
		Body: fmt.Sprintf(conf.RecurringPaymentNotificationParams.MandateDeclinedPayer.Body,
			toActorEntity.GetName().GetFirstName(),
			conf.RecurringPaymentFrequencyMapping[recurringPaymentMandateFailed.GetRecurrenceRule().GetAllowedFrequency().String()],
			moneyPkg.ToDisplayStringWithINRSymbol(recurringPaymentMandateFailed.GetAmount())),
		IconAttributes: convertToFCMIconAttribute(conf.RecurringPaymentNotificationParams.MandateDeclinedPayer.IconAttr),
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
				RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
					RecurringPaymentId: recurringPaymentMandateFailed.GetId(),
				},
			},
		},
	}
	notifType := conf.RecurringPaymentNotificationParams.MandateDeclinedPayer.NotificationType.ToFCMNotificationTypeEnum()
	notifTTL := conf.RecurringPaymentNotificationParams.MandateDeclinedPayer.NotificationExpiry
	fcmNotificationWithCommonFields := initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL)

	smsContent := &commspb.Communication_Sms{
		Sms: &commspb.SMSMessage{
			SmsOption: &commspb.SmsOption{
				Option: &commspb.SmsOption_MandateDeclinedSmsOption{
					MandateDeclinedSmsOption: &commspb.MandateDeclinedSmsOption{
						SmsType: commspb.SmsType_MANDATE_DECLINED,
						Option: &commspb.MandateDeclinedSmsOption_MandateDeclinedV1{
							MandateDeclinedV1: &commspb.MandateDeclinedSmsOptionV1{
								MandateExecutionDate: time.Now().In(datetime.IST).Format(conf.UpiMandateNotifDateFormat),
								TemplateVersion:      commspb.TemplateVersion_VERSION_V1,
								MandateAmount:        recurringPaymentMandateFailed.GetAmount(),
								PayeeName:            toActorEntity.GetName(),
								MandateFrequency:     conf.RecurringPaymentFrequencyMapping[recurringPaymentMandateFailed.GetRecurrenceRule().GetAllowedFrequency().String()],
							},
						},
					},
				},
			},
		},
	}

	return []*commspb.Communication{
		{
			Message: &commspb.Communication_Notification{
				Notification: &commspb.NotificationMessage{
					Notification: fcmNotificationWithCommonFields,
				},
			},
			Medium: commspb.Medium_NOTIFICATION,
		},
		{
			Message: smsContent,
			Medium:  commspb.Medium_SMS,
		},
	}
}

func generateMandatePausedTemplateResponse() []*commspb.Communication {
	commonFields := &fcm.CommonTemplateFields{
		Title:          conf.RecurringPaymentNotificationParams.MandatePausedPayer.Title,
		Body:           conf.RecurringPaymentNotificationParams.MandatePausedPayer.Body,
		IconAttributes: convertToFCMIconAttribute(conf.RecurringPaymentNotificationParams.MandatePausedPayer.IconAttr),
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
				RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
					RecurringPaymentId: recurringPaymentMandateActivated.GetId(),
				},
			},
		},
	}
	notifType := conf.RecurringPaymentNotificationParams.MandatePausedPayer.NotificationType.ToFCMNotificationTypeEnum()
	notifTTL := conf.RecurringPaymentNotificationParams.MandatePausedPayer.NotificationExpiry
	fcmNotificationWithCommonFields := initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL)

	smsContent := &commspb.Communication_Sms{
		Sms: &commspb.SMSMessage{
			SmsOption: &commspb.SmsOption{
				Option: &commspb.SmsOption_MandatePausedSmsOption{
					MandatePausedSmsOption: &commspb.MandatePausedSmsOption{
						SmsType: commspb.SmsType_MANDATE_PAUSED,
						Option: &commspb.MandatePausedSmsOption_MandatePausedV1{
							MandatePausedV1: &commspb.MandatePausedSmsOptionV1{
								TemplateVersion: commspb.TemplateVersion_VERSION_V1,
								PayeeName:       toActorEntity.GetName(),
								PayerName:       fromActorEntity.GetName(),
							},
						},
					},
				},
			},
		},
	}
	return []*commspb.Communication{
		{
			Message: &commspb.Communication_Notification{
				Notification: &commspb.NotificationMessage{
					Notification: fcmNotificationWithCommonFields,
				},
			},
			Medium: commspb.Medium_NOTIFICATION,
		},
		{
			Message: smsContent,
			Medium:  commspb.Medium_SMS,
		},
	}
}

func generateMandateUnpausedTemplateResponse() []*commspb.Communication {
	commonFields := &fcm.CommonTemplateFields{
		Title:          conf.RecurringPaymentNotificationParams.MandateUnpausedPayer.Title,
		Body:           fmt.Sprintf(conf.RecurringPaymentNotificationParams.MandateUnpausedPayer.Body, fromActorEntity.GetName().GetFirstName()),
		IconAttributes: convertToFCMIconAttribute(conf.RecurringPaymentNotificationParams.MandateUnpausedPayer.IconAttr),
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
				RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
					RecurringPaymentId: recurringPaymentMandateActivated.GetId(),
				},
			},
		},
	}
	notifType := conf.RecurringPaymentNotificationParams.MandateUnpausedPayer.NotificationType.ToFCMNotificationTypeEnum()
	notifTTL := conf.RecurringPaymentNotificationParams.MandateUnpausedPayer.NotificationExpiry
	fcmNotificationWithCommonFields := initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL)

	smsContent := &commspb.Communication_Sms{
		Sms: &commspb.SMSMessage{
			SmsOption: &commspb.SmsOption{
				Option: &commspb.SmsOption_MandateUnpausedSmsOption{
					MandateUnpausedSmsOption: &commspb.MandateUnpausedSmsOption{
						SmsType: commspb.SmsType_MANDATE_UNPAUSED,
						Option: &commspb.MandateUnpausedSmsOption_MandateUnpausedV1{
							MandateUnpausedV1: &commspb.MandateUnpausedSmsOptionV1{
								TemplateVersion: commspb.TemplateVersion_VERSION_V1,
								PayeeName:       toActorEntity.GetName(),
							},
						},
					},
				},
			},
		},
	}
	return []*commspb.Communication{
		{
			Message: &commspb.Communication_Notification{
				Notification: &commspb.NotificationMessage{
					Notification: fcmNotificationWithCommonFields,
				},
			},
			Medium: commspb.Medium_NOTIFICATION,
		},
		{
			Message: smsContent,
			Medium:  commspb.Medium_SMS,
		},
	}
}

func TestProcessor_GetNotificationTemplate(t *testing.T) {
	type mockRpProcessorGetAction struct {
		enable bool
		id     string
		res    *rpPb.RecurringPaymentsAction
		err    error
	}
	type mockRpProcessorGetById struct {
		enable bool
		id     string
		res    *rpPb.RecurringPayment
		err    error
	}
	type mockActorProcessorGetActorDetails struct {
		enable  bool
		actorId string
		res     *actorPb.GetEntityDetailsByActorIdResponse
		err     error
	}
	tests := []struct {
		name                                  string
		req                                   *notificationPb.GetTemplatesRequest
		mockRpProcessorGetAction              mockRpProcessorGetAction
		mockRpProcessorGetById                mockRpProcessorGetById
		mockActorProcessorGetFromActorDetails mockActorProcessorGetActorDetails
		mockActorProcessorGetToActorDetails   mockActorProcessorGetActorDetails
		want                                  *notificationPb.GetTemplatesResponse
		wantErr                               bool
		assertErr                             func(err error) bool
	}{
		{
			name: "get notification template successfully for standing instructions activated case",
			req: &notificationPb.GetTemplatesRequest{
				ClientReqId: &celestialPb.ClientReqId{
					Id:     "client_req_id",
					Client: workflowPb.Client_USER_APP,
				},
			},
			mockRpProcessorGetAction: mockRpProcessorGetAction{
				enable: true,
				id:     "client_req_id",
				res: &rpPb.RecurringPaymentsAction{
					Id:                 "id",
					RecurringPaymentId: "rp_id",
					ClientRequestId:    "client_req_id",
					Action:             rpPb.Action_CREATE,
				},
				err: nil,
			},
			mockRpProcessorGetById: mockRpProcessorGetById{
				enable: true,
				id:     "rp_id",
				res:    recurringPaymentSIActivated,
				err:    nil,
			},
			mockActorProcessorGetFromActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "from_actor_id",
				res:     fromActorEntity,
				err:     nil,
			},
			mockActorProcessorGetToActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "to_actor_id",
				res:     toActorEntity,
				err:     nil,
			},
			want: &notificationPb.GetTemplatesResponse{
				Notifications: &notificationPb.Notification{
					UserIdentifier:    &notificationPb.Notification_UserId{UserId: "entity-id-1"},
					CommunicationList: generateSIActivatedTemplateResponse(),
					QualityOfService:  commspb.QoS_GUARANTEED,
				},
			},
			wantErr: false,
		},
		{
			name: "get notification template successfully for standing instructions failed case",
			req: &notificationPb.GetTemplatesRequest{
				ClientReqId: &celestialPb.ClientReqId{
					Id:     "client_req_id",
					Client: workflowPb.Client_USER_APP,
				},
			},
			mockRpProcessorGetAction: mockRpProcessorGetAction{
				enable: true,
				id:     "client_req_id",
				res: &rpPb.RecurringPaymentsAction{
					Id:                 "id",
					RecurringPaymentId: "rp_id",
					ClientRequestId:    "client_req_id",
					Action:             rpPb.Action_CREATE,
				},
				err: nil,
			},
			mockRpProcessorGetById: mockRpProcessorGetById{
				enable: true,
				id:     "rp_id",
				res:    recurringPaymentSIFailed,
				err:    nil,
			},
			mockActorProcessorGetFromActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "from_actor_id",
				res:     fromActorEntity,
				err:     nil,
			},
			mockActorProcessorGetToActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "to_actor_id",
				res:     toActorEntity,
				err:     nil,
			},
			want: &notificationPb.GetTemplatesResponse{
				Notifications: &notificationPb.Notification{
					UserIdentifier:    &notificationPb.Notification_UserId{UserId: "entity-id-1"},
					CommunicationList: generateSIFailedTemplateResponse(),
					QualityOfService:  commspb.QoS_GUARANTEED,
				},
			},
			wantErr: false,
		},
		{
			name: "get notification template successfully for mandates activated case - create action",
			req: &notificationPb.GetTemplatesRequest{
				ClientReqId: &celestialPb.ClientReqId{
					Id:     "client_req_id",
					Client: workflowPb.Client_USER_APP,
				},
			},
			mockRpProcessorGetAction: mockRpProcessorGetAction{
				enable: true,
				id:     "client_req_id",
				res: &rpPb.RecurringPaymentsAction{
					Id:                 "id",
					RecurringPaymentId: "rp_id",
					ClientRequestId:    "client_req_id",
					Action:             rpPb.Action_CREATE,
				},
				err: nil,
			},
			mockRpProcessorGetById: mockRpProcessorGetById{
				enable: true,
				id:     "rp_id",
				res:    recurringPaymentMandateActivated,
				err:    nil,
			},
			mockActorProcessorGetFromActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "from_actor_id",
				res:     fromActorEntity,
				err:     nil,
			},
			mockActorProcessorGetToActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "to_actor_id",
				res:     toActorEntity,
				err:     nil,
			},
			want: &notificationPb.GetTemplatesResponse{
				Notifications: &notificationPb.Notification{
					UserIdentifier:    &notificationPb.Notification_UserId{UserId: "entity-id-1"},
					CommunicationList: generateMandateActivatedTemplateResponse(),
					QualityOfService:  commspb.QoS_GUARANTEED,
				},
			},
			wantErr: false,
		},
		{
			name: "get notification template successfully for mandates - modify action case",
			req: &notificationPb.GetTemplatesRequest{
				ClientReqId: &celestialPb.ClientReqId{
					Id:     "client_req_id",
					Client: workflowPb.Client_USER_APP,
				},
			},
			mockRpProcessorGetAction: mockRpProcessorGetAction{
				enable: true,
				id:     "client_req_id",
				res: &rpPb.RecurringPaymentsAction{
					Id:                 "id",
					RecurringPaymentId: "rp_id",
					ClientRequestId:    "client_req_id",
					Action:             rpPb.Action_MODIFY,
				},
				err: nil,
			},
			mockRpProcessorGetById: mockRpProcessorGetById{
				enable: true,
				id:     "rp_id",
				res:    recurringPaymentMandateActivated,
				err:    nil,
			},
			mockActorProcessorGetFromActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "from_actor_id",
				res:     fromActorEntity,
				err:     nil,
			},
			mockActorProcessorGetToActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "to_actor_id",
				res:     toActorEntity,
				err:     nil,
			},
			want: &notificationPb.GetTemplatesResponse{
				Notifications: &notificationPb.Notification{
					UserIdentifier:    &notificationPb.Notification_UserId{UserId: "entity-id-1"},
					CommunicationList: generateMandateModifiedTemplateResponse(),
					QualityOfService:  commspb.QoS_GUARANTEED,
				},
			},
			wantErr: false,
		},
		{
			name: "get notification template successfully for mandates - revoke action case",
			req: &notificationPb.GetTemplatesRequest{
				ClientReqId: &celestialPb.ClientReqId{
					Id:     "client_req_id",
					Client: workflowPb.Client_USER_APP,
				},
			},
			mockRpProcessorGetAction: mockRpProcessorGetAction{
				enable: true,
				id:     "client_req_id",
				res: &rpPb.RecurringPaymentsAction{
					Id:                 "id",
					RecurringPaymentId: "rp_id",
					ClientRequestId:    "client_req_id",
					Action:             rpPb.Action_REVOKE,
				},
				err: nil,
			},
			mockRpProcessorGetById: mockRpProcessorGetById{
				enable: true,
				id:     "rp_id",
				res:    recurringPaymentMandateActivated,
				err:    nil,
			},
			mockActorProcessorGetFromActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "from_actor_id",
				res:     fromActorEntity,
				err:     nil,
			},
			mockActorProcessorGetToActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "to_actor_id",
				res:     toActorEntity,
				err:     nil,
			},
			want: &notificationPb.GetTemplatesResponse{
				Notifications: &notificationPb.Notification{
					UserIdentifier:    &notificationPb.Notification_UserId{UserId: "entity-id-1"},
					CommunicationList: generateMandateRevokedTemplateResponse(),
					QualityOfService:  commspb.QoS_GUARANTEED,
				},
			},
			wantErr: false,
		},
		{
			name: "get notification template successfully for mandates failed case - create action",
			req: &notificationPb.GetTemplatesRequest{
				ClientReqId: &celestialPb.ClientReqId{
					Id:     "client_req_id",
					Client: workflowPb.Client_USER_APP,
				},
			},
			mockRpProcessorGetAction: mockRpProcessorGetAction{
				enable: true,
				id:     "client_req_id",
				res: &rpPb.RecurringPaymentsAction{
					Id:                 "id",
					RecurringPaymentId: "rp_id",
					ClientRequestId:    "client_req_id",
					Action:             rpPb.Action_CREATE,
				},
				err: nil,
			},
			mockRpProcessorGetById: mockRpProcessorGetById{
				enable: true,
				id:     "rp_id",
				res:    recurringPaymentMandateFailed,
				err:    nil,
			},
			mockActorProcessorGetFromActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "from_actor_id",
				res:     fromActorEntity,
				err:     nil,
			},
			mockActorProcessorGetToActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "to_actor_id",
				res:     toActorEntity,
				err:     nil,
			},
			want: &notificationPb.GetTemplatesResponse{
				Notifications: &notificationPb.Notification{
					UserIdentifier:    &notificationPb.Notification_UserId{UserId: "entity-id-1"},
					CommunicationList: generateMandateFailedTemplateResponse(),
					QualityOfService:  commspb.QoS_GUARANTEED,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to fetch to-actor id due to transient error",
			req: &notificationPb.GetTemplatesRequest{
				ClientReqId: &celestialPb.ClientReqId{
					Id:     "client_req_id",
					Client: workflowPb.Client_USER_APP,
				},
			},
			mockRpProcessorGetAction: mockRpProcessorGetAction{
				enable: true,
				id:     "client_req_id",
				res: &rpPb.RecurringPaymentsAction{
					Id:                 "id",
					RecurringPaymentId: "rp_id",
					ClientRequestId:    "client_req_id",
				},
				err: nil,
			},
			mockRpProcessorGetById: mockRpProcessorGetById{
				enable: true,
				id:     "rp_id",
				res:    recurringPaymentSIActivated,
				err:    nil,
			},
			mockActorProcessorGetFromActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "from_actor_id",
				res:     fromActorEntity,
				err:     nil,
			},
			mockActorProcessorGetToActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "to_actor_id",
				res:     nil,
				err:     epifierrors.ErrTransient,
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failure to fetch to-actor id in case record not found",
			req: &notificationPb.GetTemplatesRequest{
				ClientReqId: &celestialPb.ClientReqId{
					Id:     "client_req_id",
					Client: workflowPb.Client_USER_APP,
				},
			},
			mockRpProcessorGetAction: mockRpProcessorGetAction{
				enable: true,
				id:     "client_req_id",
				res: &rpPb.RecurringPaymentsAction{
					Id:                 "id",
					RecurringPaymentId: "rp_id",
					ClientRequestId:    "client_req_id",
				},
				err: nil,
			},
			mockRpProcessorGetById: mockRpProcessorGetById{
				enable: true,
				id:     "rp_id",
				res:    recurringPaymentSIActivated,
				err:    nil,
			},
			mockActorProcessorGetFromActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "from_actor_id",
				res:     fromActorEntity,
				err:     nil,
			},
			mockActorProcessorGetToActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "to_actor_id",
				res:     nil,
				err:     epifierrors.ErrPermanent,
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "failed to fetch from-actor id due to transient error",
			req: &notificationPb.GetTemplatesRequest{
				ClientReqId: &celestialPb.ClientReqId{
					Id:     "client_req_id",
					Client: workflowPb.Client_USER_APP,
				},
			},
			mockRpProcessorGetAction: mockRpProcessorGetAction{
				enable: true,
				id:     "client_req_id",
				res: &rpPb.RecurringPaymentsAction{
					Id:                 "id",
					RecurringPaymentId: "rp_id",
					ClientRequestId:    "client_req_id",
				},
				err: nil,
			},
			mockRpProcessorGetById: mockRpProcessorGetById{
				enable: true,
				id:     "rp_id",
				res:    recurringPaymentSIActivated,
				err:    nil,
			},
			mockActorProcessorGetFromActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "from_actor_id",
				res:     nil,
				err:     epifierrors.ErrTransient,
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failure to fetch from-actor id in case record not found",
			req: &notificationPb.GetTemplatesRequest{
				ClientReqId: &celestialPb.ClientReqId{
					Id:     "client_req_id",
					Client: workflowPb.Client_USER_APP,
				},
			},
			mockRpProcessorGetAction: mockRpProcessorGetAction{
				enable: true,
				id:     "client_req_id",
				res: &rpPb.RecurringPaymentsAction{
					Id:                 "id",
					RecurringPaymentId: "rp_id",
					ClientRequestId:    "client_req_id",
				},
				err: nil,
			},
			mockRpProcessorGetById: mockRpProcessorGetById{
				enable: true,
				id:     "rp_id",
				res:    recurringPaymentSIActivated,
				err:    nil,
			},
			mockActorProcessorGetFromActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "from_actor_id",
				res:     nil,
				err:     epifierrors.ErrPermanent,
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "failed to fetch recurring payment in case record not found",
			req: &notificationPb.GetTemplatesRequest{
				ClientReqId: &celestialPb.ClientReqId{
					Id:     "client_req_id",
					Client: workflowPb.Client_USER_APP,
				},
			},
			mockRpProcessorGetAction: mockRpProcessorGetAction{
				enable: true,
				id:     "client_req_id",
				res: &rpPb.RecurringPaymentsAction{
					Id:                 "id",
					RecurringPaymentId: "rp_id",
					ClientRequestId:    "client_req_id",
				},
				err: nil,
			},
			mockRpProcessorGetById: mockRpProcessorGetById{
				enable: true,
				id:     "rp_id",
				res:    nil,
				err:    epifierrors.ErrPermanent,
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "failed to fetch recurring payment due to transient error",
			req: &notificationPb.GetTemplatesRequest{
				ClientReqId: &celestialPb.ClientReqId{
					Id:     "client_req_id",
					Client: workflowPb.Client_USER_APP,
				},
			},
			mockRpProcessorGetAction: mockRpProcessorGetAction{
				enable: true,
				id:     "client_req_id",
				res: &rpPb.RecurringPaymentsAction{
					Id:                 "id",
					RecurringPaymentId: "rp_id",
					ClientRequestId:    "client_req_id",
				},
				err: nil,
			},
			mockRpProcessorGetById: mockRpProcessorGetById{
				enable: true,
				id:     "rp_id",
				res:    nil,
				err:    epifierrors.ErrTransient,
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failed to fetch recurring payment action in case record not found",
			req: &notificationPb.GetTemplatesRequest{
				ClientReqId: &celestialPb.ClientReqId{
					Id:     "client_req_id",
					Client: workflowPb.Client_USER_APP,
				},
			},
			mockRpProcessorGetAction: mockRpProcessorGetAction{
				enable: true,
				id:     "client_req_id",
				res:    nil,
				err:    rpcPb.StatusAsError(rpcPb.StatusRecordNotFound()),
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "failed to fetch recurring payment action in case of transient error",
			req: &notificationPb.GetTemplatesRequest{
				ClientReqId: &celestialPb.ClientReqId{
					Id:     "client_req_id",
					Client: workflowPb.Client_USER_APP,
				},
			},
			mockRpProcessorGetAction: mockRpProcessorGetAction{
				enable: true,
				id:     "client_req_id",
				res:    nil,
				err:    rpcPb.StatusAsError(rpcPb.StatusInternal()),
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "get notification template successfully for mandates - create action case",
			req: &notificationPb.GetTemplatesRequest{
				ClientReqId: &celestialPb.ClientReqId{
					Id:     "client_req_id",
					Client: workflowPb.Client_USER_APP,
				},
			},
			mockRpProcessorGetAction: mockRpProcessorGetAction{
				enable: true,
				id:     "client_req_id",
				res: &rpPb.RecurringPaymentsAction{
					Id:                 "id",
					RecurringPaymentId: "rp_id",
					ClientRequestId:    "client_req_id",
					Action:             rpPb.Action_CREATE,
				},
				err: nil,
			},
			mockRpProcessorGetById: mockRpProcessorGetById{
				enable: true,
				id:     "rp_id",
				res:    recurringPaymentMandateInitiated,
				err:    nil,
			},
			mockActorProcessorGetFromActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "from_actor_id",
				res:     fromActorEntity,
				err:     nil,
			},
			mockActorProcessorGetToActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "to_actor_id",
				res:     toActorEntity,
				err:     nil,
			},
			want: &notificationPb.GetTemplatesResponse{
				Notifications: &notificationPb.Notification{
					UserIdentifier:    &notificationPb.Notification_UserId{UserId: "entity-id-1"},
					CommunicationList: generateMandateReceivedTemplateResponse(),
					QualityOfService:  commspb.QoS_GUARANTEED,
				},
			},
			wantErr: false,
		},
		{
			name: "get notification template successfully for mandates - pause action case",
			req: &notificationPb.GetTemplatesRequest{
				ClientReqId: &celestialPb.ClientReqId{
					Id:     "client_req_id",
					Client: workflowPb.Client_USER_APP,
				},
			},
			mockRpProcessorGetAction: mockRpProcessorGetAction{
				enable: true,
				id:     "client_req_id",
				res: &rpPb.RecurringPaymentsAction{
					Id:                 "id",
					RecurringPaymentId: "rp_id",
					ClientRequestId:    "client_req_id",
					Action:             rpPb.Action_PAUSE,
				},
				err: nil,
			},
			mockRpProcessorGetById: mockRpProcessorGetById{
				enable: true,
				id:     "rp_id",
				res:    recurringPaymentMandatePaused,
				err:    nil,
			},
			mockActorProcessorGetFromActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "from_actor_id",
				res:     fromActorEntity,
				err:     nil,
			},
			mockActorProcessorGetToActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "to_actor_id",
				res:     toActorEntity,
				err:     nil,
			},
			want: &notificationPb.GetTemplatesResponse{
				Notifications: &notificationPb.Notification{
					UserIdentifier:    &notificationPb.Notification_UserId{UserId: "entity-id-1"},
					CommunicationList: generateMandatePausedTemplateResponse(),
					QualityOfService:  commspb.QoS_GUARANTEED,
				},
			},
			wantErr: false,
		},
		{
			name: "get notification template successfully for mandates - pause action case",
			req: &notificationPb.GetTemplatesRequest{
				ClientReqId: &celestialPb.ClientReqId{
					Id:     "client_req_id",
					Client: workflowPb.Client_USER_APP,
				},
			},
			mockRpProcessorGetAction: mockRpProcessorGetAction{
				enable: true,
				id:     "client_req_id",
				res: &rpPb.RecurringPaymentsAction{
					Id:                 "id",
					RecurringPaymentId: "rp_id",
					ClientRequestId:    "client_req_id",
					Action:             rpPb.Action_UNPAUSE,
				},
				err: nil,
			},
			mockRpProcessorGetById: mockRpProcessorGetById{
				enable: true,
				id:     "rp_id",
				res:    recurringPaymentMandateActivated,
				err:    nil,
			},
			mockActorProcessorGetFromActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "from_actor_id",
				res:     fromActorEntity,
				err:     nil,
			},
			mockActorProcessorGetToActorDetails: mockActorProcessorGetActorDetails{
				enable:  true,
				actorId: "to_actor_id",
				res:     toActorEntity,
				err:     nil,
			},
			want: &notificationPb.GetTemplatesResponse{
				Notifications: &notificationPb.Notification{
					UserIdentifier:    &notificationPb.Notification_UserId{UserId: "entity-id-1"},
					CommunicationList: generateMandateUnpausedTemplateResponse(),
					QualityOfService:  commspb.QoS_GUARANTEED,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			act, md, assertMocks := newProcessorWithMocks(t)
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(act)
			if tt.mockRpProcessorGetAction.enable {
				md.recurringPaymentProcessor.EXPECT().GetRecurringPaymentActionByClientReqId(gomock.Any(), tt.mockRpProcessorGetAction.id).Return(
					tt.mockRpProcessorGetAction.res, tt.mockRpProcessorGetAction.err)
			}
			if tt.mockRpProcessorGetById.enable {
				md.recurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), tt.mockRpProcessorGetById.id).Return(
					tt.mockRpProcessorGetById.res, tt.mockRpProcessorGetById.err)
			}
			if tt.mockActorProcessorGetFromActorDetails.enable {
				md.actorProcessor.EXPECT().GetActorDetails(gomock.Any(), tt.mockActorProcessorGetFromActorDetails.actorId).Return(
					tt.mockActorProcessorGetFromActorDetails.res, tt.mockActorProcessorGetFromActorDetails.err)
			}
			if tt.mockActorProcessorGetToActorDetails.enable {
				md.actorProcessor.EXPECT().GetActorDetails(gomock.Any(), tt.mockActorProcessorGetToActorDetails.actorId).Return(
					tt.mockActorProcessorGetToActorDetails.res, tt.mockActorProcessorGetToActorDetails.err)
			}
			var result *notificationPb.GetTemplatesResponse
			got, err := env.ExecuteActivity(rpNs.GetNotificationTemplate, tt.req)

			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("GetNotificationTemplate() error = %v failed to fetch type value from convertible", err)
					return
				}
			}
			if tt.want != nil {
				assertExpiry(result, tt.want)
			}
			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("GetNotificationTemplate() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("GetNotificationTemplate() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("GetNotificationTemplate() \ngot = %v,\nwant %v", result.String(), tt.want.String())
				return
			}
			assertMocks()
		})
	}
}
