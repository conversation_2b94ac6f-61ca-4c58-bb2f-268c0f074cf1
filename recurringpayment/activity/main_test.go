package activity_test

import (
	"os"
	"testing"

	"github.com/golang/mock/gomock"

	epifitemporalLogging "github.com/epifi/be-common/pkg/epifitemporal/logging"
	epifitemporalTest "github.com/epifi/be-common/pkg/epifitemporal/test"
	"github.com/epifi/be-common/pkg/logger"
	queueMocks "github.com/epifi/be-common/pkg/queue/mocks"

	commsMocks "github.com/epifi/gamma/api/comms/mocks"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	paymentMocks "github.com/epifi/gamma/api/order/payment/mocks"
	payMocks "github.com/epifi/gamma/api/pay/mocks"
	rpPb "github.com/epifi/gamma/api/recurringpayment/mocks"
	rpActivity "github.com/epifi/gamma/recurringpayment/activity"
	rpWorkerConfig "github.com/epifi/gamma/recurringpayment/config/worker"
	rpDaoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	domainCreationProcessorMocks "github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor/mocks"
	domainExecutionProcessorMocks "github.com/epifi/gamma/recurringpayment/internal/domainexecutionprocessor/mocks"
	domainRevokeProcessorMocks "github.com/epifi/gamma/recurringpayment/internal/domainrevokeprocessor/mocks"
	"github.com/epifi/gamma/recurringpayment/internal/executeRPNoAuth"
	internalMocks "github.com/epifi/gamma/recurringpayment/internal/mocks"
	"github.com/epifi/gamma/recurringpayment/test"
)

var (
	wts  epifitemporalTest.WorkflowTestSuite
	conf *rpWorkerConfig.Config
)

type mockedDependencies struct {
	rpClient                                  *rpPb.MockRecurringPaymentServiceClient
	recurringPaymentDao                       *rpDaoMocks.MockRecurringPaymentDao
	recurringPaymentProcessor                 *internalMocks.MockRecurringPaymentProcessor
	actorProcessor                            *internalMocks.MockActorProcessor
	orderClient                               *orderMocks.MockOrderServiceClient
	recurringPaymentActionDao                 *rpDaoMocks.MockRecurringPaymentsActionDao
	payClient                                 *payMocks.MockPayClient
	paymentClient                             *paymentMocks.MockPaymentClient
	mockCommsClient                           *commsMocks.MockCommsClient
	StandingInstructionExecutor               *internalMocks.MockExecuteRecurringPaymentNoAuthProcessorV1
	UPIMandatesExecutor                       *internalMocks.MockExecuteRecurringPaymentNoAuthProcessorV1
	mockDomainCreationProcessor               *domainCreationProcessorMocks.MockDomainCreationProcessorFactory
	mockDomainExecutionProcessor              *domainExecutionProcessorMocks.MockDomainExecutionProcessorFactory
	mockDomainRevokeProcessorFactory          *domainRevokeProcessorMocks.MockDomainRevokeProcessorFactory
	mockRecurringPaymentActionUpdatePublisher *queueMocks.MockPublisher
}

func newProcessorWithMocks(t *testing.T) (*rpActivity.Processor, *mockedDependencies, func()) {
	ctr := gomock.NewController(t)
	mockedRpClient := rpPb.NewMockRecurringPaymentServiceClient(ctr)
	mockedRpDao := rpDaoMocks.NewMockRecurringPaymentDao(ctr)
	mockRecurringPaymentProcessor := internalMocks.NewMockRecurringPaymentProcessor(ctr)
	mockActorProcessor := internalMocks.NewMockActorProcessor(ctr)
	mockOrderClient := orderMocks.NewMockOrderServiceClient(ctr)
	mockedRpaDao := rpDaoMocks.NewMockRecurringPaymentsActionDao(ctr)
	mockPayClient := payMocks.NewMockPayClient(ctr)
	mockPaymentClient := paymentMocks.NewMockPaymentClient(ctr)
	mockCommsClient := commsMocks.NewMockCommsClient(ctr)
	mockStandingInstructionExecutor := internalMocks.NewMockExecuteRecurringPaymentNoAuthProcessorV1(ctr)
	mockUPIMandatesExecutor := internalMocks.NewMockExecuteRecurringPaymentNoAuthProcessorV1(ctr)
	mockDomainCreationProcessorFactory := domainCreationProcessorMocks.NewMockDomainCreationProcessorFactory(ctr)
	mockDomainExecutionProcessorFactory := domainExecutionProcessorMocks.NewMockDomainExecutionProcessorFactory(ctr)
	mockDomainRevokeProcessorFactory := domainRevokeProcessorMocks.NewMockDomainRevokeProcessorFactory(ctr)
	mockRecurringPaymentActionUpdatePublisher := queueMocks.NewMockPublisher(ctr)

	md := &mockedDependencies{
		rpClient:                                  mockedRpClient,
		recurringPaymentDao:                       mockedRpDao,
		recurringPaymentProcessor:                 mockRecurringPaymentProcessor,
		actorProcessor:                            mockActorProcessor,
		orderClient:                               mockOrderClient,
		recurringPaymentActionDao:                 mockedRpaDao,
		payClient:                                 mockPayClient,
		paymentClient:                             mockPaymentClient,
		mockCommsClient:                           mockCommsClient,
		StandingInstructionExecutor:               mockStandingInstructionExecutor,
		UPIMandatesExecutor:                       mockUPIMandatesExecutor,
		mockDomainCreationProcessor:               mockDomainCreationProcessorFactory,
		mockDomainExecutionProcessor:              mockDomainExecutionProcessorFactory,
		mockRecurringPaymentActionUpdatePublisher: mockRecurringPaymentActionUpdatePublisher,
	}

	act := rpActivity.NewProcessor(
		mockedRpClient,
		mockRecurringPaymentProcessor,
		mockActorProcessor,
		mockedRpDao,
		conf,
		mockOrderClient,
		nil,
		mockedRpaDao,
		&executeRPNoAuth.ExecuteRecurringPaymentNoAuthFactoryImpl{
			StandingInstructionExecutor: mockStandingInstructionExecutor,
			UPIMandatesExecutor:         mockUPIMandatesExecutor,
		},
		mockPayClient,
		mockPaymentClient,
		mockCommsClient,
		mockDomainCreationProcessorFactory,
		mockDomainExecutionProcessorFactory,
		mockDomainRevokeProcessorFactory,
		mockRecurringPaymentActionUpdatePublisher,
	)

	return act, md, func() {
		ctr.Finish()
	}
}

// nolint:dogsled
func TestMain(m *testing.M) {
	var teardown func()
	conf, _, teardown = test.InitTestWorker(false)
	wts.SetLogger(epifitemporalLogging.NewZapAdapter(logger.Log))
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
