// nolint:dupl
package activity

import (
	"context"
	"errors"
	"fmt"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
)

func (p *Processor) UpdateRecurringPaymentStatus(ctx context.Context, req *rpActivityPb.UpdateRecurringPaymentStatusRequest) (*rpActivityPb.UpdateRecurringPaymentStatusResponse, error) {
	// todo: Add support for field mask for updating other fields if needed
	res := &rpActivityPb.UpdateRecurringPaymentStatusResponse{}

	recurringPayment, err := p.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	if err != nil {
		return nil, fmt.Errorf("error while trying to fetch recurring payment by id, err: %s %w", err, epifierrors.ErrTransient)
	}
	// Checking if the Recurring Payment is already in updated state.
	// This is required for idempotency of activity
	if isRecurringPaymentStatusAlreadyUpdated(recurringPayment, req.GetNextStatus()) {
		return res, nil
	}

	if err = p.recurringPaymentDao.UpdateAndChangeStatus(ctx, recurringPayment, nil, req.GetCurrentStatus(), req.GetNextStatus()); err != nil {
		if errors.Is(err, epifierrors.ErrNoRowsAffected) {
			return nil, fmt.Errorf("error while trying to Update Recurring Payment state, err: %s %w", err, epifierrors.ErrPermanent)
		}
		return nil, fmt.Errorf("error while trying to Update Recurring Payment state, err: %s %w", err, epifierrors.ErrTransient)
	}
	return res, nil
}

func isRecurringPaymentStatusAlreadyUpdated(recurringPayment *rpPb.RecurringPayment, nextState rpPb.RecurringPaymentState) bool {
	return recurringPayment.GetState() == nextState
}
