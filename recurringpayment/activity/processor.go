package activity

import (
	"context"

	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	commPb "github.com/epifi/gamma/api/comms"
	orderPb "github.com/epifi/gamma/api/order"
	domainPb "github.com/epifi/gamma/api/order/domain"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	celestialPkg "github.com/epifi/gamma/pkg/epifitemporal/celestial"
	rpWorkerConfig "github.com/epifi/gamma/recurringpayment/config/worker"
	"github.com/epifi/gamma/recurringpayment/dao"
	"github.com/epifi/gamma/recurringpayment/internal"
	"github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor"
	"github.com/epifi/gamma/recurringpayment/internal/domainexecutionprocessor"
	"github.com/epifi/gamma/recurringpayment/internal/domainrevokeprocessor"
	"github.com/epifi/gamma/recurringpayment/internal/executeRPNoAuth"
	rpTypes "github.com/epifi/gamma/recurringpayment/types"
)

type Processor struct {
	rpClient                              rpPb.RecurringPaymentServiceClient
	actorProcessor                        internal.ActorProcessor
	recurringPaymentProcessor             internal.RecurringPaymentProcessor
	recurringPaymentDao                   dao.RecurringPaymentDao
	config                                *rpWorkerConfig.Config
	orderClient                           orderPb.OrderServiceClient
	idempotentTxnExecutor                 storagev2.IdempotentTxnExecutor
	recurringPaymentsActionDao            dao.RecurringPaymentsActionDao
	executeRecurringPaymentFactory        executeRPNoAuth.ExecuteRecurringPaymentNoAuthFactory
	payClient                             payPb.PayClient
	paymentClient                         paymentPb.PaymentClient
	commClient                            commPb.CommsClient
	domainCreationProcessorFactory        domaincreationprocessor.DomainCreationProcessorFactory
	domainExecutionProcessorFactory       domainexecutionprocessor.DomainExecutionProcessorFactory
	domainRevokeProcessorFactory          domainrevokeprocessor.DomainRevokeProcessorFactory
	recurringPaymentActionUpdatePublisher rpTypes.RecurringPaymentActionUpdateEventPublisher
}

func NewProcessor(rpClient rpPb.RecurringPaymentServiceClient,
	recurringPaymentProcessor internal.RecurringPaymentProcessor,
	actorProcessor internal.ActorProcessor,
	recurringPaymentDao dao.RecurringPaymentDao,
	config *rpWorkerConfig.Config,
	orderClient orderPb.OrderServiceClient,
	idempotentTxnExecutor storagev2.IdempotentTxnExecutor,
	recurringPaymentsActionDao dao.RecurringPaymentsActionDao,
	executeRecurringPaymentFactory executeRPNoAuth.ExecuteRecurringPaymentNoAuthFactory,
	payClient payPb.PayClient,
	paymentClient paymentPb.PaymentClient,
	commClient commPb.CommsClient,
	domainCreationProcessorFactory domaincreationprocessor.DomainCreationProcessorFactory,
	domainExecutionProcessorFactory domainexecutionprocessor.DomainExecutionProcessorFactory,
	domainRevokeProcessorFactory domainrevokeprocessor.DomainRevokeProcessorFactory,
	recurringPaymentActionUpdatePublisher rpTypes.RecurringPaymentActionUpdateEventPublisher,
) *Processor {
	return &Processor{
		rpClient:                              rpClient,
		recurringPaymentProcessor:             recurringPaymentProcessor,
		actorProcessor:                        actorProcessor,
		recurringPaymentDao:                   recurringPaymentDao,
		config:                                config,
		orderClient:                           orderClient,
		idempotentTxnExecutor:                 idempotentTxnExecutor,
		recurringPaymentsActionDao:            recurringPaymentsActionDao,
		executeRecurringPaymentFactory:        executeRecurringPaymentFactory,
		payClient:                             payClient,
		paymentClient:                         paymentClient,
		commClient:                            commClient,
		domainCreationProcessorFactory:        domainCreationProcessorFactory,
		domainExecutionProcessorFactory:       domainExecutionProcessorFactory,
		domainRevokeProcessorFactory:          domainRevokeProcessorFactory,
		recurringPaymentActionUpdatePublisher: recurringPaymentActionUpdatePublisher,
	}
}

// ProcessRecurringPaymentCreation - takes activity request as input and invokes domain RPC that processes the recurring payment creation
// This activity is executed inside celestial workflow for recurring payment creation
// nolint: dupl
func (p *Processor) ProcessRecurringPaymentCreation(ctx context.Context, req *activityPb.Request) (*activityPb.Response, error) {
	res := &activityPb.Response{}
	lg := activity.GetLogger(ctx)

	processRpCreateRes, err := p.rpClient.ProcessRecurringPaymentCreation(ctx, &domainPb.ProcessFulfilmentRequest{
		RequestHeader: &domainPb.DomainRequestHeader{
			ClientRequestId:             req.GetClientReqId(),
			IsLastAttempt:               req.GetRequestHeader().GetIsLastAttempt(),
			ShouldForceProcessOrder:     false,
			ShouldOverrideTerminalState: false,
		},
		Payload: req.GetPayload(),
	})
	if err != nil {
		lg.Error("failed to process recurring payment creation due to connection issues", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	err = celestialPkg.ConvertDomainStatusCodeToError(processRpCreateRes.GetResponseHeader().GetStatus(), req.GetRequestHeader().GetIsLastAttempt())
	if err != nil {
		lg.Error("error occurred while processing recurring payment",
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.Error(err))
		return nil, err
	}

	lg.Debug("successfully processed recurring payment creation for client req id", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
	return res, nil
}

// ProcessRecurringPaymentExecution - takes activity request as input and invokes domain RPC that processes the recurring payment execution
// This activity is executed inside celestial workflow for recurring payment execution
func (p *Processor) ProcessRecurringPaymentExecution(ctx context.Context, req *activityPb.Request) (*activityPb.Response, error) {
	res := &activityPb.Response{}
	lg := activity.GetLogger(ctx)

	clientReqId := req.GetRequestHeader().GetClientReqId()
	if clientReqId == "" {
		clientReqId = req.GetClientReqId()
	}

	resp, err := p.orderClient.GetOrder(ctx, &orderPb.GetOrderRequest{
		Identifier: &orderPb.GetOrderRequest_ClientReqId{
			ClientReqId: clientReqId,
		},
	})
	switch {
	case err != nil:
		lg.Error("failed to get order because RPC call failed", zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		return nil, epifitemporal.NewTransientError(err)
	case resp.GetStatus().IsRecordNotFound():
		lg.Error("no order found for given client request id", zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		return nil, epifitemporal.NewPermanentError(epifierrors.ErrRecordNotFound)
	case !resp.GetStatus().IsSuccess():
		lg.Error("get order RPC returned non success status", zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		return nil, epifitemporal.NewTransientError(epifierrors.ErrTransient)
	}

	processRpExecuteRes, err := p.rpClient.ProcessRecurringPaymentExecution(ctx, &domainPb.ProcessPaymentRequest{
		RequestHeader: &domainPb.DomainRequestHeader{
			ClientRequestId:             resp.GetOrder().GetId(),
			IsLastAttempt:               req.GetRequestHeader().GetIsLastAttempt(),
			ShouldForceProcessOrder:     false,
			ShouldOverrideTerminalState: false,
		},
		Payload: resp.GetOrder().GetOrderPayload(),
	})
	if err != nil {
		lg.Error("failed to process recurring payment execution due to connection issues", zap.String(logger.CLIENT_REQUEST_ID, clientReqId), zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	err = celestialPkg.ConvertDomainStatusCodeToError(processRpExecuteRes.GetResponseHeader().GetStatus(), req.GetRequestHeader().GetIsLastAttempt())
	if err != nil {
		lg.Error("error occurred while processing recurring payment execution",
			zap.String(logger.CLIENT_REQUEST_ID, clientReqId), zap.Error(err))
		return nil, err
	}

	lg.Debug("successfully processed recurring payment execution for client req id", zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
	return res, nil
}
