package activity

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/durationpb"

	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

func (p *Processor) GetActivationCoolOff(ctx context.Context, req *rpActivityPb.GetActivationCoolOffRequest) (*rpActivityPb.GetActivationCoolOffResponse, error) {
	var (
		res               = &rpActivityPb.GetActivationCoolOffResponse{}
		activationCoolOff *durationpb.Duration
	)

	recurringPayment, err := p.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	if err != nil {
		logger.Error(ctx, "error while fetching recurring payment by id", zap.Error(err))
		return nil, fmt.Errorf("error while fetching recurring payment by id, err: %s, %w", err, epifierrors.ErrTransient)
	}

	domainCreationProcessor, err := p.domainCreationProcessorFactory.GetProcessor(recurringPayment.GetType(), recurringPayment.GetPaymentRoute())
	if err != nil {
		logger.Error(ctx, "error while fetching processor implementation", zap.Error(err))
		if errors.Is(err, epifierrors.ErrInvalidArgument) {
			res.GetActivationCoolOffActivityStatus = rpActivityPb.GetActivationCoolOffResponse_GET_ACTIVATION_COOL_OFF_ACTIVITY_STATUS_FAILURE
			return res, nil
		}
		return nil, fmt.Errorf("error while fetching processor implementation, err: %s, %w", err, epifierrors.ErrTransient)
	}

	activationCoolOff, err = domainCreationProcessor.GetActivationCoolOff(ctx, recurringPayment.GetPartnerBank())
	if err != nil {
		logger.Error(ctx, "error while trying to fetch activation cooldown.", zap.Error(err))
		if errors.Is(err, epifierrors.ErrInvalidArgument) {
			res.GetActivationCoolOffActivityStatus = rpActivityPb.GetActivationCoolOffResponse_GET_ACTIVATION_COOL_OFF_ACTIVITY_STATUS_FAILURE
			return res, nil
		}
		return nil, fmt.Errorf("error while trying to activation cooldown, err: %s, %w", err, epifierrors.ErrTransient)
	}

	res.CoolOffDuration = activationCoolOff
	res.GetActivationCoolOffActivityStatus = rpActivityPb.GetActivationCoolOffResponse_GET_ACTIVATION_COOL_OFF_ACTIVITY_STATUS_SUCCESS
	return res, nil
}
