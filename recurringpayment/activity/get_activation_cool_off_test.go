package activity

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/durationpb"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	rpDaoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	domainCreationProcessorMocks "github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor/mocks"
)

func TestProcessor_GetActivationCoolOff(t *testing.T) {
	const (
		defaultRecurringPaymentId = "default_test_recurring_payment_id"
		defaultActorFrom          = "default_test_actor_from"
		defaultActorTo            = "default_test_actor_to"
		defaultPiFrom             = "default_test_pi_from"
		defaultPiTo               = "default_test_pi_to"
	)

	var (
		defaultRecurringPayment = &rpPb.RecurringPayment{
			Id:          defaultRecurringPaymentId,
			FromActorId: defaultActorFrom,
			ToActorId:   defaultActorTo,
			Type:        rpPb.RecurringPaymentType_STANDING_INSTRUCTION,
			PiFrom:      defaultPiFrom,
			PiTo:        defaultPiTo,
			State:       rpPb.RecurringPaymentState_CREATION_QUEUED,
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		}
		defaultActivationCoolOff = durationpb.New(time.Minute)
	)

	tests := []struct {
		name       string
		req        *rpActivityPb.GetActivationCoolOffRequest
		setupMocks func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1)
		want       *rpActivityPb.GetActivationCoolOffResponse
		wantErr    bool
	}{
		{
			name: "Should return error if unable to fetch recurring payment by id",
			req: &rpActivityPb.GetActivationCoolOffRequest{
				RecurringPaymentId: defaultRecurringPaymentId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(nil, epifierrors.ErrTransient)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Should return FAILURE status when domain entity creation processor returned invalid argument error",
			req: &rpActivityPb.GetActivationCoolOffRequest{
				RecurringPaymentId: defaultRecurringPaymentId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
				mockDomainCreationProcessorFactory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_STANDING_INSTRUCTION, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).Return(nil, epifierrors.ErrInvalidArgument)
			},
			want: &rpActivityPb.GetActivationCoolOffResponse{
				GetActivationCoolOffActivityStatus: rpActivityPb.GetActivationCoolOffResponse_GET_ACTIVATION_COOL_OFF_ACTIVITY_STATUS_FAILURE,
			},
			wantErr: false,
		},
		{
			name: "Should return FAILURE status when GetActivationCoolOff returned invalid argument error",
			req: &rpActivityPb.GetActivationCoolOffRequest{
				RecurringPaymentId: defaultRecurringPaymentId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
				mockDomainCreationProcessorFactory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_STANDING_INSTRUCTION, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).Return(mockDomainCreationProcessor, nil)
				mockDomainCreationProcessor.EXPECT().GetActivationCoolOff(gomock.Any(), commonvgpb.Vendor_FEDERAL_BANK).Return(nil, epifierrors.ErrInvalidArgument)
			},
			want: &rpActivityPb.GetActivationCoolOffResponse{
				GetActivationCoolOffActivityStatus: rpActivityPb.GetActivationCoolOffResponse_GET_ACTIVATION_COOL_OFF_ACTIVITY_STATUS_FAILURE,
			},
			wantErr: false,
		},
		{
			name: "Should return error when GetActivationCoolOff returned transient error",
			req: &rpActivityPb.GetActivationCoolOffRequest{
				RecurringPaymentId: defaultRecurringPaymentId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
				mockDomainCreationProcessorFactory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_STANDING_INSTRUCTION, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).Return(mockDomainCreationProcessor, nil)
				mockDomainCreationProcessor.EXPECT().GetActivationCoolOff(gomock.Any(), commonvgpb.Vendor_FEDERAL_BANK).Return(nil, epifierrors.ErrTransient)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Should return success when GetActivationCoolOff returned success with a cooldown",
			req: &rpActivityPb.GetActivationCoolOffRequest{
				RecurringPaymentId: defaultRecurringPaymentId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
				mockDomainCreationProcessorFactory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_STANDING_INSTRUCTION, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).Return(mockDomainCreationProcessor, nil)
				mockDomainCreationProcessor.EXPECT().GetActivationCoolOff(gomock.Any(), commonvgpb.Vendor_FEDERAL_BANK).Return(defaultActivationCoolOff, nil)
			},
			want: &rpActivityPb.GetActivationCoolOffResponse{
				GetActivationCoolOffActivityStatus: rpActivityPb.GetActivationCoolOffResponse_GET_ACTIVATION_COOL_OFF_ACTIVITY_STATUS_SUCCESS,
				CoolOffDuration:                    defaultActivationCoolOff,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockRPDao := rpDaoMocks.NewMockRecurringPaymentDao(ctr)
			mockDomainCreationProcessorFactory := domainCreationProcessorMocks.NewMockDomainCreationProcessorFactory(ctr)
			mockDomainCreationProcessor := domainCreationProcessorMocks.NewMockDomainCreationProcessorV1(ctr)

			p := &Processor{
				recurringPaymentDao:            mockRPDao,
				domainCreationProcessorFactory: mockDomainCreationProcessorFactory,
			}

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			tt.setupMocks(mockRPDao, mockDomainCreationProcessorFactory, mockDomainCreationProcessor)

			result := &rpActivityPb.GetActivationCoolOffResponse{}
			got, err := env.ExecuteActivity(rpNs.GetActivationCoolOff, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("GetActivationCoolOff() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			if (err != nil) != tt.wantErr {
				t.Errorf("GetActivationCoolOff() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !proto.Equal(result, tt.want) {
				t.Errorf("GetActivationCoolOff() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
