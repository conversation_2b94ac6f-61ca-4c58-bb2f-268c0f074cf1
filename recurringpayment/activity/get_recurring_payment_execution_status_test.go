package activity_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/gamma/recurringpayment/internal"
)

func TestProcessor_GetRecurringPaymentExecutionStatus(t *testing.T) {
	const (
		defaultActorFrom       = "default_test_actor_from"
		defaultDedupeRequestId = "default_test_dedupe_request_id"
		defaultUTR             = "default_test_utr"
	)

	var ()

	act, md, assertTest := newProcessorWithMocks(t)
	defer assertTest()
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(act)

	type mockRPProcessorV1EnquirePaymentStatus struct {
		enable bool
		req    *internal.EnquirePaymentStatusReq
		res    *internal.EnquirePaymentStatusRes
		err    error
	}

	tests := []struct {
		name                                  string
		req                                   *rpActivityPb.GetRecurringPaymentExecutionStatusRequest
		want                                  *rpActivityPb.GetRecurringPaymentExecutionStatusResponse
		mockRPProcessorV1EnquirePaymentStatus mockRPProcessorV1EnquirePaymentStatus
		wantErr                               bool
	}{
		{
			name: "Error if failed to GetRecurringPaymentExecutionStatus",
			req: &rpActivityPb.GetRecurringPaymentExecutionStatusRequest{
				OriginalRequestId:    defaultDedupeRequestId,
				PartnerBank:          commonvgpb.Vendor_FEDERAL_BANK,
				ActorId:              defaultActorFrom,
				PaymentProtocol:      paymentPb.PaymentProtocol_NEFT,
				RecurringPaymentType: rpPb.RecurringPaymentType_STANDING_INSTRUCTION,
			},
			mockRPProcessorV1EnquirePaymentStatus: mockRPProcessorV1EnquirePaymentStatus{
				enable: true,
				req: &internal.EnquirePaymentStatusReq{
					OriginalRequestId: defaultDedupeRequestId,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					ActorId:           defaultActorFrom,
					PaymentProtocol:   paymentPb.PaymentProtocol_NEFT,
				},
				res: nil,
				err: epifierrors.ErrPermanent,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Failed if transaction execution failed",
			req: &rpActivityPb.GetRecurringPaymentExecutionStatusRequest{
				OriginalRequestId:    defaultDedupeRequestId,
				PartnerBank:          commonvgpb.Vendor_FEDERAL_BANK,
				ActorId:              defaultActorFrom,
				PaymentProtocol:      paymentPb.PaymentProtocol_NEFT,
				RecurringPaymentType: rpPb.RecurringPaymentType_STANDING_INSTRUCTION,
			},
			mockRPProcessorV1EnquirePaymentStatus: mockRPProcessorV1EnquirePaymentStatus{
				enable: true,
				req: &internal.EnquirePaymentStatusReq{
					OriginalRequestId: defaultDedupeRequestId,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					ActorId:           defaultActorFrom,
					PaymentProtocol:   paymentPb.PaymentProtocol_NEFT,
				},
				res: &internal.EnquirePaymentStatusRes{
					TransactionStatus: paymentPb.TransactionStatus_FAILED,
				},
				err: nil,
			},
			want: &rpActivityPb.GetRecurringPaymentExecutionStatusResponse{
				TransactionStatus:          paymentPb.TransactionStatus_FAILED,
				ExecutedTransactionDetails: &rpActivityPb.ExecutedTransactionDetails{},
			},
			wantErr: false,
		},
		{
			name: "Success with utr if transaction executed successfully",
			req: &rpActivityPb.GetRecurringPaymentExecutionStatusRequest{
				OriginalRequestId:    defaultDedupeRequestId,
				PartnerBank:          commonvgpb.Vendor_FEDERAL_BANK,
				ActorId:              defaultActorFrom,
				PaymentProtocol:      paymentPb.PaymentProtocol_NEFT,
				RecurringPaymentType: rpPb.RecurringPaymentType_STANDING_INSTRUCTION,
			},
			mockRPProcessorV1EnquirePaymentStatus: mockRPProcessorV1EnquirePaymentStatus{
				enable: true,
				req: &internal.EnquirePaymentStatusReq{
					OriginalRequestId: defaultDedupeRequestId,
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					ActorId:           defaultActorFrom,
					PaymentProtocol:   paymentPb.PaymentProtocol_NEFT,
				},
				res: &internal.EnquirePaymentStatusRes{
					TransactionStatus: paymentPb.TransactionStatus_SUCCESS,
					Utr:               defaultUTR,
				},
				err: nil,
			},
			want: &rpActivityPb.GetRecurringPaymentExecutionStatusResponse{
				TransactionStatus: paymentPb.TransactionStatus_SUCCESS,
				ExecutedTransactionDetails: &rpActivityPb.ExecutedTransactionDetails{
					Utr: defaultUTR,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockRPProcessorV1EnquirePaymentStatus.enable {
				mockProcessor := getProcessorImpl(md, tt.req.GetRecurringPaymentType())
				mockProcessor.EXPECT().EnquirePaymentStatus(gomock.Any(), tt.mockRPProcessorV1EnquirePaymentStatus.req).
					Return(tt.mockRPProcessorV1EnquirePaymentStatus.res, tt.mockRPProcessorV1EnquirePaymentStatus.err)
			}

			result := &rpActivityPb.GetRecurringPaymentExecutionStatusResponse{}
			got, err := env.ExecuteActivity(rpNs.GetRecurringPaymentExecutionStatus, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("GetRecurringPaymentExecutionStatus() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			if (err != nil) != tt.wantErr {
				t.Errorf("GetRecurringPaymentExecutionStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !proto.Equal(result, tt.want) {
				t.Errorf("GetRecurringPaymentExecutionStatus() got = %v, want %v", result, tt.want)
				return
			}

			assertTest()
		})
	}
}
