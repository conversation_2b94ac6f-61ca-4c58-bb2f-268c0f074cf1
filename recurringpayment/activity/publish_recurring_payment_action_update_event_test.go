package activity

import (
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	rpEventPb "github.com/epifi/gamma/api/recurringpayment/events"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	queueMocks "github.com/epifi/be-common/pkg/queue/mocks"
	rpDaoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
)

func TestProcessor_PublishRecurringPaymentActionUpdateEvent(t *testing.T) {

	const (
		recurringPaymentId       = "recurring-payment-id-1"
		recurringPaymentActionId = "recurring-payment-action-id-1"
		clientRequestId          = "client-request-id-1"
		messageId                = "message-id-1"
	)

	currentTimestamp := timestamp.Now()

	recurringPaymentAction1 := &rpPb.RecurringPaymentsAction{
		Id:                 recurringPaymentActionId,
		Action:             rpPb.Action_CREATE,
		RecurringPaymentId: recurringPaymentId,
		ClientRequestId:    clientRequestId,
		State:              rpPb.ActionState_ACTION_CREATED,
		UpdatedAt:          currentTimestamp,
	}

	publishEvent1 := &rpEventPb.RecurringPaymentActionUpdateEvent{
		ActionId:              recurringPaymentAction1.GetId(),
		ActionType:            recurringPaymentAction1.GetAction(),
		RecurringPaymentId:    recurringPaymentAction1.GetRecurringPaymentId(),
		ActionClientRequestId: recurringPaymentAction1.GetClientRequestId(),
		ActionState:           recurringPaymentAction1.GetState(),
		UpdatedAt:             recurringPaymentAction1.GetUpdatedAt(),
	}

	tests := []struct {
		name       string
		req        *rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest
		setupMocks func(mockRPADao *rpDaoMocks.MockRecurringPaymentsActionDao, mockPublisher *queueMocks.MockPublisher)
		want       *rpActivityPb.PublishRecurringPaymentActionUpdateEventResponse
		wantErr    bool
		assertErr  func(err error) bool
	}{
		{
			name: "Should return retryable error if unable to fetch recurring payment action by recurring payment action id",
			req: &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
				RecurringPaymentActionId: recurringPaymentActionId,
			},
			setupMocks: func(mockRPADao *rpDaoMocks.MockRecurringPaymentsActionDao, mockPublisher *queueMocks.MockPublisher) {
				mockRPADao.EXPECT().GetById(gomock.Any(), recurringPaymentActionId).Return(nil, epifierrors.ErrTransient)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return retryable error if unable to publish the recurring payment action event",
			req: &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
				RecurringPaymentActionId: recurringPaymentActionId,
			},
			setupMocks: func(mockRPADao *rpDaoMocks.MockRecurringPaymentsActionDao, mockPublisher *queueMocks.MockPublisher) {
				mockRPADao.EXPECT().GetById(gomock.Any(), recurringPaymentActionId).Return(recurringPaymentAction1, nil)
				mockPublisher.EXPECT().Publish(gomock.Any(), publishEvent1).Return("", epifierrors.ErrTransient)

			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return empty response from the activity when successfully publishes recurring payment action event",
			req: &rpActivityPb.PublishRecurringPaymentActionUpdateEventRequest{
				RecurringPaymentActionId: recurringPaymentActionId,
			},
			setupMocks: func(mockRPADao *rpDaoMocks.MockRecurringPaymentsActionDao, mockPublisher *queueMocks.MockPublisher) {
				mockRPADao.EXPECT().GetById(gomock.Any(), recurringPaymentActionId).Return(recurringPaymentAction1, nil)
				mockPublisher.EXPECT().Publish(gomock.Any(), publishEvent1).Return(messageId, nil)

			},
			want:    &rpActivityPb.PublishRecurringPaymentActionUpdateEventResponse{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockRPADao := rpDaoMocks.NewMockRecurringPaymentsActionDao(ctr)
			mockPubliser := queueMocks.NewMockPublisher(ctr)

			p := &Processor{
				recurringPaymentsActionDao:            mockRPADao,
				recurringPaymentActionUpdatePublisher: mockPubliser,
			}

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			tt.setupMocks(mockRPADao, mockPubliser)

			result := &rpActivityPb.PublishRecurringPaymentActionUpdateEventResponse{}
			got, err := env.ExecuteActivity(rpNs.PublishRecurringPaymentActionUpdateEvent, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("PublishRecurringPaymentActionUpdateEvent() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("PublishRecurringPaymentActionUpdateEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("PublishRecurringPaymentActionUpdateEvent() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("PublishRecurringPaymentActionUpdateEvent() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
