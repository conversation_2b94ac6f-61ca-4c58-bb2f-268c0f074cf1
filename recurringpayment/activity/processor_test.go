package activity_test

import (
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	activityPb "github.com/epifi/be-common/api/celestial/activity"
	orderPb "github.com/epifi/gamma/api/order"
	domainPb "github.com/epifi/gamma/api/order/domain"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
)

func TestProcessor_ProcessRecurringPaymentCreation(t *testing.T) {
	type mockRpClient struct {
		enable bool
		req    *domainPb.ProcessFulfilmentRequest
		res    *domainPb.ProcessFulfilmentResponse
		err    error
	}

	tests := []struct {
		name         string
		req          *activityPb.Request
		mockRpClient mockRpClient
		want         *activityPb.Response
		wantErr      bool
		assertErr    func(err error) bool
	}{
		{
			name: "recurring payment processed successfully",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: false},
				ClientReqId:   "client-req-id",
				Payload:       []byte{102, 97, 108, 99, 111, 110},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessFulfilmentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "client-req-id",
						IsLastAttempt:               false,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessFulfilmentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_SUCCESS,
					},
				},
				err: nil,
			},
			want:    &activityPb.Response{},
			wantErr: false,
		},
		{
			name: "failed to process recurring payment creation due to connection issues",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: false},
				ClientReqId:   "client-req-id",
				Payload:       []byte{102, 97, 108, 99, 111, 110},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessFulfilmentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "client-req-id",
						IsLastAttempt:               false,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: nil,
				err: errors.New("error"),
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failure in processing recurring payment creation due to transient error",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: false},
				ClientReqId:   "client-req-id",
				Payload:       []byte{102, 97, 108, 99, 111, 110},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessFulfilmentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "client-req-id",
						IsLastAttempt:               false,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessFulfilmentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_TRANSIENT_FAILURE,
					},
				},
				err: nil,
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failure in processing recurring payment creation due to permanent error",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: false},
				ClientReqId:   "client-req-id",
				Payload:       []byte{102, 97, 108, 99, 111, 110},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessFulfilmentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "client-req-id",
						IsLastAttempt:               false,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessFulfilmentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_PERMANENT_FAILURE,
					},
				},
				err: nil,
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent error if last attempt and transient status in process recurring payment creation rpc",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: true},
				ClientReqId:   "client-req-id",
				Payload:       []byte{102, 97, 108, 99, 111, 110},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessFulfilmentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "client-req-id",
						IsLastAttempt:               true,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessFulfilmentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_TRANSIENT_FAILURE,
					},
				},
				err: nil,
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent error if last attempt and no_op status in process recurring payment creation rpc",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: true},
				ClientReqId:   "client-req-id",
				Payload:       []byte{102, 97, 108, 99, 111, 110},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessFulfilmentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "client-req-id",
						IsLastAttempt:               true,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessFulfilmentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_NO_OP,
					},
				},
				err: nil,
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "transient error if no_op status and attempt count is left for process recurring payment creation rpc",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: false},
				ClientReqId:   "client-req-id",
				Payload:       []byte{102, 97, 108, 99, 111, 110},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessFulfilmentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "client-req-id",
						IsLastAttempt:               false,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessFulfilmentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_NO_OP,
					},
				},
				err: nil,
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			act, md, assertTest := newProcessorWithMocks(t)
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(act)
			if tt.mockRpClient.enable {
				md.rpClient.
					EXPECT().
					ProcessRecurringPaymentCreation(gomock.Any(), tt.mockRpClient.req).
					Return(tt.mockRpClient.res, tt.mockRpClient.err)
			}
			var result *activityPb.Response
			got, err := env.ExecuteActivity(rpNs.ProcessRecurringPaymentCreation, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("ProcessRecurringPaymentCreation() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("ProcessRecurringPaymentCreation() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("ProcessRecurringPaymentCreation() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("ProcessRecurringPaymentCreation() got = %v, want %v", result, tt.want)
				return
			}
			assertTest()
		})
	}
}

func TestProcessor_ProcessRecurringPaymentExecution(t *testing.T) {
	type mockRpClient struct {
		enable bool
		req    *domainPb.ProcessPaymentRequest
		res    *domainPb.ProcessPaymentResponse
		err    error
	}

	type mockOrderClient struct {
		enable bool
		req    *orderPb.GetOrderRequest
		res    *orderPb.GetOrderResponse
		err    error
	}

	tests := []struct {
		name            string
		req             *activityPb.Request
		mockOrderClient mockOrderClient
		mockRpClient    mockRpClient
		want            *activityPb.Response
		wantErr         bool
		assertErr       func(err error) bool
	}{
		{
			name: "recurring payment execution processed successfully",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: false},
				ClientReqId:   "client-req-id",
			},
			mockOrderClient: mockOrderClient{
				enable: true,
				req: &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "client-req-id",
					},
				},
				res: &orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:           "order-id",
						OrderPayload: []byte{102, 97, 108, 99, 111, 110},
					},
				},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "order-id",
						IsLastAttempt:               false,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessPaymentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_SUCCESS,
					},
				},
				err: nil,
			},
			want:    &activityPb.Response{},
			wantErr: false,
		},
		{
			name: "failed to process recurring payment execution due to connection issue",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: false},
				ClientReqId:   "client-req-id",
			},
			mockOrderClient: mockOrderClient{
				enable: true,
				req: &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "client-req-id",
					},
				},
				res: nil,
				err: errors.New("connection issue"),
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failed to process recurring payment execution because order was not found for given client req id",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: false},
				ClientReqId:   "client-req-id",
			},
			mockOrderClient: mockOrderClient{
				enable: true,
				req: &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "client-req-id",
					},
				},
				res: &orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
					Order:  nil,
				},
				err: nil,
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "failed to process recurring payment execution because GetOrder returned non success status",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: false},
				ClientReqId:   "client-req-id",
			},
			mockOrderClient: mockOrderClient{
				enable: true,
				req: &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "client-req-id",
					},
				},
				res: &orderPb.GetOrderResponse{
					Status: rpc.StatusInternal(),
					Order:  nil,
				},
				err: nil,
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failed to process recurring payment execution due to connection issues",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: false},
				ClientReqId:   "client-req-id",
			},
			mockOrderClient: mockOrderClient{
				enable: true,
				req: &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "client-req-id",
					},
				},
				res: &orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:           "order-id",
						OrderPayload: []byte{102, 97, 108, 99, 111, 110},
					},
				},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "order-id",
						IsLastAttempt:               false,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: nil,
				err: errors.New("error"),
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failure in processing recurring payment execution due to transient error",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: false},
				ClientReqId:   "client-req-id",
				Payload:       []byte{102, 97, 108, 99, 111, 110},
			},
			mockOrderClient: mockOrderClient{
				enable: true,
				req: &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "client-req-id",
					},
				},
				res: &orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:           "order-id",
						OrderPayload: []byte{102, 97, 108, 99, 111, 110},
					},
				},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "order-id",
						IsLastAttempt:               false,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessPaymentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_TRANSIENT_FAILURE,
					},
				},
				err: nil,
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failure in processing recurring payment execution due to permanent error",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: false},
				ClientReqId:   "client-req-id",
				Payload:       []byte{102, 97, 108, 99, 111, 110},
			},
			mockOrderClient: mockOrderClient{
				enable: true,
				req: &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "client-req-id",
					},
				},
				res: &orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:           "order-id",
						OrderPayload: []byte{102, 97, 108, 99, 111, 110},
					},
				},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "order-id",
						IsLastAttempt:               false,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessPaymentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_PERMANENT_FAILURE,
					},
				},
				err: nil,
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent error if last attempt and transient status in process recurring payment execution rpc",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: true},
				ClientReqId:   "client-req-id",
				Payload:       []byte{102, 97, 108, 99, 111, 110},
			},
			mockOrderClient: mockOrderClient{
				enable: true,
				req: &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "client-req-id",
					},
				},
				res: &orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:           "order-id",
						OrderPayload: []byte{102, 97, 108, 99, 111, 110},
					},
				},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "order-id",
						IsLastAttempt:               true,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessPaymentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_TRANSIENT_FAILURE,
					},
				},
				err: nil,
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent error if last attempt and no_op status in process recurring payment execution rpc",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: true},
				ClientReqId:   "client-req-id",
				Payload:       []byte{102, 97, 108, 99, 111, 110},
			},
			mockOrderClient: mockOrderClient{
				enable: true,
				req: &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "client-req-id",
					},
				},
				res: &orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:           "order-id",
						OrderPayload: []byte{102, 97, 108, 99, 111, 110},
					},
				},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "order-id",
						IsLastAttempt:               true,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessPaymentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_NO_OP,
					},
				},
				err: nil,
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "transient error if no_op status and attempt count is left for process recurring payment execution rpc",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: false},
				ClientReqId:   "client-req-id",
				Payload:       []byte{102, 97, 108, 99, 111, 110},
			},
			mockOrderClient: mockOrderClient{
				enable: true,
				req: &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "client-req-id",
					},
				},
				res: &orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:           "order-id",
						OrderPayload: []byte{102, 97, 108, 99, 111, 110},
					},
				},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessPaymentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "order-id",
						IsLastAttempt:               false,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessPaymentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_NO_OP,
					},
				},
				err: nil,
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			act, md, assertTest := newProcessorWithMocks(t)
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(act)
			if tt.mockOrderClient.enable {
				md.orderClient.
					EXPECT().
					GetOrder(gomock.Any(), tt.mockOrderClient.req).
					Return(tt.mockOrderClient.res, tt.mockOrderClient.err)
			}
			if tt.mockRpClient.enable {
				md.rpClient.
					EXPECT().
					ProcessRecurringPaymentExecution(gomock.Any(), tt.mockRpClient.req).
					Return(tt.mockRpClient.res, tt.mockRpClient.err)
			}
			var result *activityPb.Response
			got, err := env.ExecuteActivity(rpNs.ProcessRecurringPaymentExecution, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("ProcessRecurringPaymentCreation() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("ProcessRecurringPaymentCreation() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("ProcessRecurringPaymentCreation() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("ProcessRecurringPaymentCreation() got = %v, want %v", result, tt.want)
				return
			}
			assertTest()
		})
	}
}
