package activity_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	moneyPb "github.com/epifi/be-common/pkg/money"

	actorPb "github.com/epifi/gamma/api/actor"
	commsPb "github.com/epifi/gamma/api/comms"
	commsMocks "github.com/epifi/gamma/api/comms/mocks"
	"github.com/epifi/gamma/api/frontend/deeplink"
	notificationPb "github.com/epifi/gamma/api/frontend/fcm"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/gamma/recurringpayment/activity"
	rpDaoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	domainCreationProcessorMocks "github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor/mocks"
	internalMocks "github.com/epifi/gamma/recurringpayment/internal/mocks"
)

type CustomProtoArgMatcher struct {
	want *commsPb.SendMessageBatchRequest
}

// NewProtoArgMatcher initializes a custom argument matcher for proto messages
// since reflect.Equal which is used by default for go mock arg matching can be unreliable and
// is not recommended
func NewCustomProtoArgMatcher(want *commsPb.SendMessageBatchRequest) *CustomProtoArgMatcher {
	return &CustomProtoArgMatcher{want: want}
}

// this CustomProtoArgMatcher function needed to set ExpireAt filed as nil. Since it is difficult to mock the current implementation
func (p *CustomProtoArgMatcher) Matches(x interface{}) bool {
	got, ok := x.(*commsPb.SendMessageBatchRequest)
	if !ok {
		return false
	}

	p.want.GetCommunicationList()[0].GetNotification().GetNotification().GetSystemTrayTemplate().GetCommonTemplateFields().ExpireAt = nil
	got.GetCommunicationList()[0].GetNotification().GetNotification().GetSystemTrayTemplate().GetCommonTemplateFields().ExpireAt = nil

	return proto.Equal(got, p.want)
}

func (p *CustomProtoArgMatcher) String() string {
	b, err := protojson.Marshal(p.want)
	if err != nil {
		return fmt.Sprintf("failed to marshal want: %s", err)
	}
	return string(b)
}

func TestProcessor_SendStatusNotification(t *testing.T) {

	const (
		recurringPaymentId       = "recurring_payment_id-1"
		recurringPaymentActionId = "recurring_payment_action_id-1"
		fromActorId              = "from_actor_id-1"
		toActorId                = "to_actor_id-1"
		piFrom                   = "pi_from-1"
		piTo                     = "pi_To-1"
		firstName1               = "first-name-1"
		firstName2               = "first-name-2"
		fromActorEntityId        = "from-actor-entity-id-1"
	)

	recurringPayment1 := &rpPb.RecurringPayment{
		Id:          recurringPaymentId,
		FromActorId: fromActorId,
		ToActorId:   toActorId,
		Type:        rpPb.RecurringPaymentType_ENACH_MANDATES,
		PiFrom:      piFrom,
		PiTo:        piTo,
		State:       rpPb.RecurringPaymentState_ACTIVATED,
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		RecurrenceRule: &rpPb.RecurrenceRule{
			AllowedFrequency: rpPb.AllowedFrequency_MONTHLY,
		},
		Amount: moneyPb.AmountINR(200).GetPb(),
	}

	recurringPaymentAction1 := &rpPb.RecurringPaymentsAction{
		Id:                 recurringPaymentActionId,
		RecurringPaymentId: recurringPaymentId,
		Action:             rpPb.Action_CREATE,
		State:              rpPb.ActionState_ACTION_CREATED,
		InitiatedBy:        rpPb.InitiatedBy_PAYER,
	}

	fromActorEntity1 := &actorPb.GetEntityDetailsByActorIdResponse{
		EntityId: fromActorEntityId,
		Name:     &commontypes.Name{FirstName: firstName1},
	}

	toActorEntity1 := &actorPb.GetEntityDetailsByActorIdResponse{
		Name: &commontypes.Name{FirstName: firstName2},
	}

	enachCreationSuccessCommunication := &commsPb.Communication{Medium: commsPb.Medium_NOTIFICATION,
		Message: &commsPb.Communication_Notification{
			Notification: &commsPb.NotificationMessage{
				Notification: &notificationPb.Notification{
					NotificationType: notificationPb.NotificationType_SYSTEM_TRAY,
					NotificationTemplates: &notificationPb.Notification_SystemTrayTemplate{
						SystemTrayTemplate: &notificationPb.SystemTrayTemplate{
							CommonTemplateFields: &notificationPb.CommonTemplateFields{
								Title:          "We're ready to roll! 🏄🏻‍♀️",
								Body:           "Your auto-payment rule has been created. INR 200.00 will be sent to first-name-2. It will occur every month",
								IconAttributes: &notificationPb.IconAttributes{},
								Deeplink: &deeplink.Deeplink{
									Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
									ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
										RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
											RecurringPaymentId: recurringPaymentId,
										},
									},
								},
								ExpireAt: nil,
							},
						},
					},
				},
			},
		},
	}
	communicationList := []*commsPb.Communication{enachCreationSuccessCommunication}
	enachCreationSuccessMessageBatchRequest := &commsPb.SendMessageBatchRequest{
		Type:              commsPb.QoS_GUARANTEED,
		UserIdentifier:    &commsPb.SendMessageBatchRequest_UserId{UserId: fromActorEntityId},
		CommunicationList: communicationList,
	}

	enachCreationFailureCommunication := &commsPb.Communication{Medium: commsPb.Medium_NOTIFICATION,
		Message: &commsPb.Communication_Notification{
			Notification: &commsPb.NotificationMessage{
				Notification: &notificationPb.Notification{
					NotificationType: notificationPb.NotificationType_SYSTEM_TRAY,
					NotificationTemplates: &notificationPb.Notification_SystemTrayTemplate{
						SystemTrayTemplate: &notificationPb.SystemTrayTemplate{
							CommonTemplateFields: &notificationPb.CommonTemplateFields{
								Title:          "⚠️ Something went wrong.️",
								Body:           "Your instructions to create enach of INR 200.00 to first-name-2 could not be created. Try again in some time",
								IconAttributes: &notificationPb.IconAttributes{},
								Deeplink: &deeplink.Deeplink{
									Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
									ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
										RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
											RecurringPaymentId: recurringPaymentId,
										},
									},
								},
								ExpireAt: nil,
							},
						},
					},
				},
			},
		},
	}
	communicationList = []*commsPb.Communication{enachCreationFailureCommunication}
	enachCreationFailureMessageBatchRequest := &commsPb.SendMessageBatchRequest{
		Type:              commsPb.QoS_GUARANTEED,
		UserIdentifier:    &commsPb.SendMessageBatchRequest_UserId{UserId: fromActorEntityId},
		CommunicationList: communicationList,
	}

	enachExecutionSuccessCommunication := &commsPb.Communication{Medium: commsPb.Medium_NOTIFICATION,
		Message: &commsPb.Communication_Notification{
			Notification: &commsPb.NotificationMessage{
				Notification: &notificationPb.Notification{
					NotificationType: notificationPb.NotificationType_SYSTEM_TRAY,
					NotificationTemplates: &notificationPb.Notification_SystemTrayTemplate{
						SystemTrayTemplate: &notificationPb.SystemTrayTemplate{
							CommonTemplateFields: &notificationPb.CommonTemplateFields{
								Title:          "3, 2, 1... Done! 💥️",
								Body:           "Based on your instructions, we just successfully transferred INR 200.00 to first-name-2",
								IconAttributes: &notificationPb.IconAttributes{},
								Deeplink: &deeplink.Deeplink{
									Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
									ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
										RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
											RecurringPaymentId: recurringPaymentId,
										},
									},
								},
								ExpireAt: nil,
							},
						},
					},
				},
			},
		},
	}
	communicationList = []*commsPb.Communication{enachExecutionSuccessCommunication}
	enachExecutionSuccessMessageBatchRequest := &commsPb.SendMessageBatchRequest{
		Type:              commsPb.QoS_GUARANTEED,
		UserIdentifier:    &commsPb.SendMessageBatchRequest_UserId{UserId: fromActorEntityId},
		CommunicationList: communicationList,
	}

	tests := []struct {
		name       string
		req        *rpActivityPb.SendStatusNotificationRequest
		setupMocks func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockRPADao *rpDaoMocks.MockRecurringPaymentsActionDao,
			mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory,
			mockActorProcessor *internalMocks.MockActorProcessor,
			mockCommsClient *commsMocks.MockCommsClient,
			mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1)
		want      *rpActivityPb.SendStatusNotificationResponse
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "should return retryable if unable to fetch recurring payment by id",
			req: &rpActivityPb.SendStatusNotificationRequest{
				RecurringPaymentId:       recurringPaymentId,
				RecurringPaymentActionId: recurringPaymentActionId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockRPADao *rpDaoMocks.MockRecurringPaymentsActionDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockActorProcessor *internalMocks.MockActorProcessor, mockCommsClient *commsMocks.MockCommsClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), recurringPaymentId).Return(nil, epifierrors.ErrTransient)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return retryable error if unable to fetch recurring payment action by recurring payment action id",
			req: &rpActivityPb.SendStatusNotificationRequest{
				RecurringPaymentId:       recurringPaymentId,
				RecurringPaymentActionId: recurringPaymentActionId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockRPADao *rpDaoMocks.MockRecurringPaymentsActionDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockActorProcessor *internalMocks.MockActorProcessor, mockCommsClient *commsMocks.MockCommsClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), recurringPaymentId).Return(recurringPayment1, nil)
				mockRPADao.EXPECT().GetById(gomock.Any(), recurringPaymentActionId).Return(nil, epifierrors.ErrTransient)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return retryable error if unable to fetch toActorEntity by to actor id",
			req: &rpActivityPb.SendStatusNotificationRequest{
				RecurringPaymentId:       recurringPaymentId,
				RecurringPaymentActionId: recurringPaymentActionId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockRPADao *rpDaoMocks.MockRecurringPaymentsActionDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockActorProcessor *internalMocks.MockActorProcessor, mockCommsClient *commsMocks.MockCommsClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), recurringPaymentId).Return(recurringPayment1, nil)
				mockRPADao.EXPECT().GetById(gomock.Any(), recurringPaymentActionId).Return(recurringPaymentAction1, nil)
				mockActorProcessor.EXPECT().GetActorDetails(gomock.Any(), fromActorId).Return(nil, epifierrors.ErrTransient)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return retryable error if unable to fetch fromActorEntity by from actor id",
			req: &rpActivityPb.SendStatusNotificationRequest{
				RecurringPaymentId:       recurringPaymentId,
				RecurringPaymentActionId: recurringPaymentActionId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockRPADao *rpDaoMocks.MockRecurringPaymentsActionDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockActorProcessor *internalMocks.MockActorProcessor, mockCommsClient *commsMocks.MockCommsClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), recurringPaymentId).Return(recurringPayment1, nil)
				mockRPADao.EXPECT().GetById(gomock.Any(), recurringPaymentActionId).Return(recurringPaymentAction1, nil)
				mockActorProcessor.EXPECT().GetActorDetails(gomock.Any(), fromActorId).Return(fromActorEntity1, nil)
				mockActorProcessor.EXPECT().GetActorDetails(gomock.Any(), toActorId).Return(nil, epifierrors.ErrTransient)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return retryable error when the activity receive some transient error from sendMessageBatch rpc (Comms Service)",
			req: &rpActivityPb.SendStatusNotificationRequest{
				RecurringPaymentId:       recurringPaymentId,
				RecurringPaymentActionId: recurringPaymentActionId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockRPADao *rpDaoMocks.MockRecurringPaymentsActionDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockActorProcessor *internalMocks.MockActorProcessor, mockCommsClient *commsMocks.MockCommsClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), recurringPaymentId).Return(recurringPayment1, nil)
				mockRPADao.EXPECT().GetById(gomock.Any(), recurringPaymentActionId).Return(recurringPaymentAction1, nil)
				mockActorProcessor.EXPECT().GetActorDetails(gomock.Any(), fromActorId).Return(fromActorEntity1, nil)
				mockActorProcessor.EXPECT().GetActorDetails(gomock.Any(), toActorId).Return(toActorEntity1, nil)
				mockCommsClient.EXPECT().SendMessageBatch(gomock.Any(), NewCustomProtoArgMatcher(enachCreationSuccessMessageBatchRequest)).Return(nil, epifierrors.ErrTransient)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return failure when the activity will receive record not found response sendMessageBatch rpc (Comms Service)",
			req: &rpActivityPb.SendStatusNotificationRequest{
				RecurringPaymentId:       recurringPaymentId,
				RecurringPaymentActionId: recurringPaymentActionId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockRPADao *rpDaoMocks.MockRecurringPaymentsActionDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockActorProcessor *internalMocks.MockActorProcessor, mockCommsClient *commsMocks.MockCommsClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), recurringPaymentId).Return(recurringPayment1, nil)
				mockRPADao.EXPECT().GetById(gomock.Any(), recurringPaymentActionId).Return(recurringPaymentAction1, nil)
				mockActorProcessor.EXPECT().GetActorDetails(gomock.Any(), fromActorId).Return(fromActorEntity1, nil)
				mockActorProcessor.EXPECT().GetActorDetails(gomock.Any(), toActorId).Return(toActorEntity1, nil)
				mockCommsClient.EXPECT().SendMessageBatch(gomock.Any(), NewCustomProtoArgMatcher(enachCreationSuccessMessageBatchRequest)).Return(&commsPb.SendMessageBatchResponse{
					Status:        rpcPb.StatusRecordNotFound(),
					MessageIdList: nil,
				}, nil)

			},
			want: &rpActivityPb.SendStatusNotificationResponse{
				ActivityStatus: rpActivityPb.SendStatusNotificationResponse_SEND_STATUS_NOTIFICATION_FAILURE,
			},
			wantErr: false,
		},
		{
			name: "should return retryable error when the activity will receive response other than success & record not found from sendMessageBatch rpc (Comms Service)",
			req: &rpActivityPb.SendStatusNotificationRequest{
				RecurringPaymentId:       recurringPaymentId,
				RecurringPaymentActionId: recurringPaymentActionId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockRPADao *rpDaoMocks.MockRecurringPaymentsActionDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockActorProcessor *internalMocks.MockActorProcessor, mockCommsClient *commsMocks.MockCommsClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), recurringPaymentId).Return(recurringPayment1, nil)
				mockRPADao.EXPECT().GetById(gomock.Any(), recurringPaymentActionId).Return(recurringPaymentAction1, nil)
				mockActorProcessor.EXPECT().GetActorDetails(gomock.Any(), fromActorId).Return(fromActorEntity1, nil)
				mockActorProcessor.EXPECT().GetActorDetails(gomock.Any(), toActorId).Return(toActorEntity1, nil)
				mockCommsClient.EXPECT().SendMessageBatch(gomock.Any(), NewCustomProtoArgMatcher(enachCreationSuccessMessageBatchRequest)).Return(&commsPb.SendMessageBatchResponse{
					Status:        rpcPb.StatusInvalidArgument(),
					MessageIdList: nil,
				}, nil)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should send enach creation failure notification if failed to create the enach mandate",
			req: &rpActivityPb.SendStatusNotificationRequest{
				RecurringPaymentId:       recurringPaymentId,
				RecurringPaymentActionId: recurringPaymentActionId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockRPADao *rpDaoMocks.MockRecurringPaymentsActionDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockActorProcessor *internalMocks.MockActorProcessor, mockCommsClient *commsMocks.MockCommsClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), recurringPaymentId).Return(&rpPb.RecurringPayment{
					Id:          recurringPaymentId,
					FromActorId: fromActorId,
					ToActorId:   toActorId,
					Type:        rpPb.RecurringPaymentType_ENACH_MANDATES,
					PiFrom:      piFrom,
					PiTo:        piTo,
					State:       rpPb.RecurringPaymentState_FAILED,
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					RecurrenceRule: &rpPb.RecurrenceRule{
						AllowedFrequency: rpPb.AllowedFrequency_MONTHLY,
					},
					Amount: moneyPb.AmountINR(200).GetPb(),
				}, nil)
				mockRPADao.EXPECT().GetById(gomock.Any(), recurringPaymentActionId).Return(recurringPaymentAction1, nil)
				mockActorProcessor.EXPECT().GetActorDetails(gomock.Any(), fromActorId).Return(fromActorEntity1, nil)
				mockActorProcessor.EXPECT().GetActorDetails(gomock.Any(), toActorId).Return(toActorEntity1, nil)
				mockCommsClient.EXPECT().SendMessageBatch(gomock.Any(), NewCustomProtoArgMatcher(enachCreationFailureMessageBatchRequest)).Return(&commsPb.SendMessageBatchResponse{
					Status:        rpcPb.StatusOk(),
					MessageIdList: nil,
				}, nil)
			},
			want: &rpActivityPb.SendStatusNotificationResponse{
				ActivityStatus: rpActivityPb.SendStatusNotificationResponse_SEND_STATUS_NOTIFICATION_SUCCESS,
			},
			wantErr: false,
		},
		{
			name: "should send enach creation success notification if successfully created the enach mandate",
			req: &rpActivityPb.SendStatusNotificationRequest{
				RecurringPaymentId:       recurringPaymentId,
				RecurringPaymentActionId: recurringPaymentActionId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockRPADao *rpDaoMocks.MockRecurringPaymentsActionDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockActorProcessor *internalMocks.MockActorProcessor, mockCommsClient *commsMocks.MockCommsClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), recurringPaymentId).Return(recurringPayment1, nil)
				mockRPADao.EXPECT().GetById(gomock.Any(), recurringPaymentActionId).Return(recurringPaymentAction1, nil)
				mockActorProcessor.EXPECT().GetActorDetails(gomock.Any(), fromActorId).Return(fromActorEntity1, nil)
				mockActorProcessor.EXPECT().GetActorDetails(gomock.Any(), toActorId).Return(toActorEntity1, nil)
				mockCommsClient.EXPECT().SendMessageBatch(gomock.Any(), NewCustomProtoArgMatcher(enachCreationSuccessMessageBatchRequest)).Return(&commsPb.SendMessageBatchResponse{
					Status:        rpcPb.StatusOk(),
					MessageIdList: nil,
				}, nil)
			},
			want: &rpActivityPb.SendStatusNotificationResponse{
				ActivityStatus: rpActivityPb.SendStatusNotificationResponse_SEND_STATUS_NOTIFICATION_SUCCESS,
			},
			wantErr: false,
		},
		{
			name: "should send enach execution success notification if successfully executes the enach mandate",
			req: &rpActivityPb.SendStatusNotificationRequest{
				RecurringPaymentId:       recurringPaymentId,
				RecurringPaymentActionId: recurringPaymentActionId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockRPADao *rpDaoMocks.MockRecurringPaymentsActionDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockActorProcessor *internalMocks.MockActorProcessor, mockCommsClient *commsMocks.MockCommsClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), recurringPaymentId).Return(&rpPb.RecurringPayment{
					Id:          recurringPaymentId,
					FromActorId: fromActorId,
					ToActorId:   toActorId,
					Type:        rpPb.RecurringPaymentType_ENACH_MANDATES,
					PiFrom:      piFrom,
					PiTo:        piTo,
					State:       rpPb.RecurringPaymentState_COMPLETED,
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					RecurrenceRule: &rpPb.RecurrenceRule{
						AllowedFrequency: rpPb.AllowedFrequency_MONTHLY,
					},
					Amount: moneyPb.AmountINR(200).GetPb(),
				}, nil)
				mockRPADao.EXPECT().GetById(gomock.Any(), recurringPaymentActionId).Return(&rpPb.RecurringPaymentsAction{
					Id:                 recurringPaymentActionId,
					RecurringPaymentId: recurringPaymentId,
					Action:             rpPb.Action_EXECUTE,
					State:              rpPb.ActionState_ACTION_SUCCESS,
					InitiatedBy:        rpPb.InitiatedBy_PAYER,
				}, nil)
				mockActorProcessor.EXPECT().GetActorDetails(gomock.Any(), fromActorId).Return(fromActorEntity1, nil)
				mockActorProcessor.EXPECT().GetActorDetails(gomock.Any(), toActorId).Return(toActorEntity1, nil)
				mockCommsClient.EXPECT().SendMessageBatch(gomock.Any(), NewCustomProtoArgMatcher(enachExecutionSuccessMessageBatchRequest)).Return(&commsPb.SendMessageBatchResponse{
					Status:        rpcPb.StatusOk(),
					MessageIdList: nil,
				}, nil)
			},
			want: &rpActivityPb.SendStatusNotificationResponse{
				ActivityStatus: rpActivityPb.SendStatusNotificationResponse_SEND_STATUS_NOTIFICATION_SUCCESS,
			},
			wantErr: false,
		},
		{
			name: "should gracefully skip the activity if no communication list found corresponding to recurring payment action",
			req: &rpActivityPb.SendStatusNotificationRequest{
				RecurringPaymentId:       recurringPaymentId,
				RecurringPaymentActionId: recurringPaymentActionId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockRPADao *rpDaoMocks.MockRecurringPaymentsActionDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockActorProcessor *internalMocks.MockActorProcessor, mockCommsClient *commsMocks.MockCommsClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), recurringPaymentId).Return(&rpPb.RecurringPayment{
					Id:          recurringPaymentId,
					FromActorId: fromActorId,
					ToActorId:   toActorId,
					Type:        rpPb.RecurringPaymentType_ENACH_MANDATES,
					PiFrom:      piFrom,
					PiTo:        piTo,
					State:       rpPb.RecurringPaymentState_FAILED,
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					RecurrenceRule: &rpPb.RecurrenceRule{
						AllowedFrequency: rpPb.AllowedFrequency_MONTHLY,
					},
					Amount: moneyPb.AmountINR(200).GetPb(),
				}, nil)
				mockRPADao.EXPECT().GetById(gomock.Any(), recurringPaymentActionId).Return(&rpPb.RecurringPaymentsAction{
					Id:                 recurringPaymentActionId,
					RecurringPaymentId: recurringPaymentId,
					Action:             rpPb.Action_EXECUTE,
					State:              rpPb.ActionState_ACTION_FAILURE,
					InitiatedBy:        rpPb.InitiatedBy_PAYER,
				}, nil)
				mockActorProcessor.EXPECT().GetActorDetails(gomock.Any(), fromActorId).Return(fromActorEntity1, nil)
				mockActorProcessor.EXPECT().GetActorDetails(gomock.Any(), toActorId).Return(toActorEntity1, nil)
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockRPDao := rpDaoMocks.NewMockRecurringPaymentDao(ctr)
			mockRPADao := rpDaoMocks.NewMockRecurringPaymentsActionDao(ctr)
			mockDomainCreationProcessorFactory := domainCreationProcessorMocks.NewMockDomainCreationProcessorFactory(ctr)
			mockDomainCreationProcessor := domainCreationProcessorMocks.NewMockDomainCreationProcessorV1(ctr)
			mockActorProcessor := internalMocks.NewMockActorProcessor(ctr)
			mockCommsClient := commsMocks.NewMockCommsClient(ctr)

			p := activity.NewProcessor(nil,
				nil,
				mockActorProcessor,
				mockRPDao,
				conf,
				nil,
				nil,
				mockRPADao,
				nil,
				nil,
				nil,
				mockCommsClient,
				mockDomainCreationProcessorFactory,
				nil,
				nil,
				nil)

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			tt.setupMocks(mockRPDao, mockRPADao, mockDomainCreationProcessorFactory, mockActorProcessor, mockCommsClient, mockDomainCreationProcessor)

			result := &rpActivityPb.SendStatusNotificationResponse{}
			got, err := env.ExecuteActivity(rpNs.SendStatusNotification, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("SendStatusNotification() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("SendStatusNotification() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("SendStatusNotification() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("SendStatusNotification() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
