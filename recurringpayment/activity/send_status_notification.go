// nolint: goimports
package activity

import (
	"context"
	"errors"
	"fmt"

	"github.com/epifi/be-common/pkg/epificontext"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	notificationPb "github.com/epifi/gamma/api/celestial/activity/notification"
	commPb "github.com/epifi/gamma/api/comms"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
)

// nolint: funlen
func (p *Processor) SendStatusNotification(ctx context.Context, req *rpActivityPb.SendStatusNotificationRequest) (*rpActivityPb.SendStatusNotificationResponse, error) {

	var (
		res = &rpActivityPb.SendStatusNotificationResponse{}
	)

	recurringPayment, err := p.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	if err != nil {
		logger.Error(ctx, "error while fetching recurring payment by id", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		return nil, fmt.Errorf("error while fetching recurring payment by id, err: %s, %w", err, epifierrors.ErrTransient)
	}
	ctx = epificontext.WithOwnership(ctx, recurringPayment.GetEntityOwnership())
	recurringPaymentAction, err := p.recurringPaymentsActionDao.GetById(ctx, req.GetRecurringPaymentActionId())
	if err != nil {
		logger.Error(ctx, "error while fetching recurring payment action by id", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ACTION_ID, req.GetRecurringPaymentActionId()))
		return nil, fmt.Errorf("error while fetching recurring payment action by id, err: %s, %w", err, epifierrors.ErrTransient)
	}

	fromActorEntity, err := p.actorProcessor.GetActorDetails(ctx, recurringPayment.GetFromActorId())
	if err != nil {
		logger.Error(ctx, "error while fetching fromActor entities from fromActor id", zap.Error(err), zap.String(logger.FROM_ACTOR_ID, recurringPayment.GetFromActorId()))
		return nil, fmt.Errorf("error while fetching fromActor entities from fromActor id, err: %s, %w", err, epifierrors.ErrTransient)
	}

	toActorEntity, err := p.actorProcessor.GetActorDetails(ctx, recurringPayment.GetToActorId())
	if err != nil {
		logger.Error(ctx, "error while fetching toActor entities from toActor id", zap.Error(err), zap.String(logger.TO_ACTOR_ID, recurringPayment.GetToActorId()))
		return nil, fmt.Errorf("error while fetching fromActor entities from fromActor id, err: %s, %w", err, epifierrors.ErrTransient)
	}

	// if entity id of from actor is empty (external actor) we are gracefully returning success
	if fromActorEntity.GetEntityId() == "" {
		logger.Info(ctx, "As this is an external actor. Gracefully skipping send notification activity", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.String(logger.RECURRING_PAYMENT_ACTION_ID, req.GetRecurringPaymentActionId()), zap.String(logger.FROM_ACTOR_ID, recurringPayment.GetFromActorId()))
		res.ActivityStatus = rpActivityPb.SendStatusNotificationResponse_SEND_STATUS_NOTIFICATION_SUCCESS
		return res, nil
	}

	notifications, err := p.generateNotificationTemplate(ctx, recurringPayment, toActorEntity, fromActorEntity, recurringPaymentAction)
	if err != nil {
		logger.Error(ctx, "error while fetching notification template for the recurring payment", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		if errors.Is(err, epifierrors.ErrInvalidArgument) {
			res.ActivityStatus = rpActivityPb.SendStatusNotificationResponse_SEND_STATUS_NOTIFICATION_FAILURE
			return res, nil
		}
		return nil, fmt.Errorf("error while fetching recurring payment notification template, err: %s, %w", err, epifierrors.ErrTransient)
	}
	// If communication list is empty gracefully return success
	if len(notifications.GetCommunicationList()) == 0 {
		logger.Info(ctx, "No communication list found. Gracefully skipping send status notification activity", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, req.GetRecurringPaymentActionId()))
		res.ActivityStatus = rpActivityPb.SendStatusNotificationResponse_SEND_STATUS_NOTIFICATION_SUCCESS
		return res, nil
	}

	messageBatchRequest, err := p.convertToCommsBatchRequest(ctx, notifications)
	if err != nil {
		logger.Error(ctx, "error while fetching message batch request for the recurring payment", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		if errors.Is(err, epifierrors.ErrInvalidArgument) {
			res.ActivityStatus = rpActivityPb.SendStatusNotificationResponse_SEND_STATUS_NOTIFICATION_FAILURE
			return res, nil
		}
		return nil, fmt.Errorf("error while fetching recurring payment message batch request, err: %s, %w", err, epifierrors.ErrTransient)
	}

	messageBatchResponse, err := p.commClient.SendMessageBatch(ctx, messageBatchRequest)
	switch {
	case err != nil:
		logger.Error(ctx, "failed to send notifications due to connection issues", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, messageBatchRequest.GetClientId()))
		return nil, epifierrors.ErrTransient
	case messageBatchResponse.GetStatus().IsRecordNotFound():
		logger.Error(ctx, "failed to send batch notification for client id", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, messageBatchRequest.GetClientId()))
		res.ActivityStatus = rpActivityPb.SendStatusNotificationResponse_SEND_STATUS_NOTIFICATION_FAILURE
		return res, nil
	case messageBatchResponse.GetStatus().IsInvalidArgument():
		logger.Error(ctx, "failed to send batch notification for client id due to Invalid Argument is passed", zap.String(logger.CLIENT_REQUEST_ID, messageBatchRequest.GetClientId()), zap.Any("UserIdentifier", messageBatchRequest.GetUserIdentifier()), zap.String("QosType", messageBatchRequest.GetType().String()), zap.String(logger.ENTITY_ID, fromActorEntity.GetEntityId()))
		return nil, epifierrors.ErrTransient
	case !messageBatchResponse.GetStatus().IsSuccess():
		logger.Error(ctx, "failed to send batch notification for client id", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, messageBatchRequest.GetClientId()))
		return nil, epifierrors.ErrTransient
	}

	res.ActivityStatus = rpActivityPb.SendStatusNotificationResponse_SEND_STATUS_NOTIFICATION_SUCCESS

	return res, nil
}

func (p *Processor) convertToCommsBatchRequest(ctx context.Context, req *notificationPb.Notification) (*commPb.SendMessageBatchRequest, error) {

	var messageBatchReq *commPb.SendMessageBatchRequest

	messageBatchReq = &commPb.SendMessageBatchRequest{
		Type:              req.GetQualityOfService(),
		CommunicationList: req.GetCommunicationList(),
		ClientId:          req.GetClientId(),
		UserIdentifier:    nil,
	}

	switch req.GetUserIdentifier().(type) {
	case *notificationPb.Notification_UserId:
		messageBatchReq.UserIdentifier = &commPb.SendMessageBatchRequest_UserId{UserId: req.GetUserId()}
	case *notificationPb.Notification_PhoneNumber:
		messageBatchReq.UserIdentifier = &commPb.SendMessageBatchRequest_PhoneNumber{PhoneNumber: req.GetPhoneNumber()}
	case *notificationPb.Notification_EmailId:
		messageBatchReq.UserIdentifier = &commPb.SendMessageBatchRequest_EmailId{EmailId: req.GetEmailId()}
	default:
		logger.Error(ctx, "unsupported identifier", zap.Error(epifierrors.ErrPermanent))
		return nil, fmt.Errorf("unsupported identifier, err: %w", epifierrors.ErrPermanent)
	}
	return messageBatchReq, nil
}
