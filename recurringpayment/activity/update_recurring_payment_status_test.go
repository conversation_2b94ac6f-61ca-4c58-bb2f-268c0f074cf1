package activity_test

import (
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
)

func TestProcessor_UpdateRecurringPaymentStatus(t *testing.T) {
	const (
		defaultRecurringPaymentId = "default_test_recurring_payment_id"
		defaultActorFrom          = "default_test_actor_from"
		defaultActorTo            = "default_test_actor_to"
		defaultPiFrom             = "default_test_pi_from"
		defaultPiTo               = "default_test_pi_to"
	)

	var (
		defaultRecurringPayment = &rpPb.RecurringPayment{
			Id:          defaultRecurringPaymentId,
			FromActorId: defaultActorFrom,
			ToActorId:   defaultActorTo,
			Type:        rpPb.RecurringPaymentType_STANDING_INSTRUCTION,
			PiFrom:      defaultPiFrom,
			PiTo:        defaultPiTo,
			State:       rpPb.RecurringPaymentState_CREATION_QUEUED,
		}
		defaultUpdatedRecurringPayment = &rpPb.RecurringPayment{
			Id:          defaultRecurringPaymentId,
			FromActorId: defaultActorFrom,
			ToActorId:   defaultActorTo,
			Type:        rpPb.RecurringPaymentType_STANDING_INSTRUCTION,
			PiFrom:      defaultPiFrom,
			PiTo:        defaultPiTo,
			State:       rpPb.RecurringPaymentState_CREATION_INITIATED,
		}
	)

	act, md, assertTest := newProcessorWithMocks(t)
	defer assertTest()
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(act)

	type mockRPDaoUpdateAndChangeStatus struct {
		enable           bool
		recurringPayment *rpPb.RecurringPayment
		updateMask       []rpPb.RecurringPaymentFieldMask
		currentStatus    rpPb.RecurringPaymentState
		nextStatus       rpPb.RecurringPaymentState
		err              error
	}
	type mockRPDaoGetById struct {
		enable             bool
		recurringPaymentId string
		recurringPayment   *rpPb.RecurringPayment
		err                error
	}
	tests := []struct {
		name                           string
		req                            *rpActivityPb.UpdateRecurringPaymentStatusRequest
		mockRPDaoUpdateAndChangeStatus mockRPDaoUpdateAndChangeStatus
		mockRPDaoGetById               mockRPDaoGetById
		want                           *rpActivityPb.UpdateRecurringPaymentStatusResponse
		wantErr                        bool
	}{
		{
			name: "Error if GetById dao returned error",
			req: &rpActivityPb.UpdateRecurringPaymentStatusRequest{
				RecurringPaymentId: defaultRecurringPaymentId,
				CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
				NextStatus:         rpPb.RecurringPaymentState_CREATION_INITIATED,
			},
			mockRPDaoGetById: mockRPDaoGetById{
				enable:             true,
				recurringPaymentId: defaultRecurringPaymentId,
				recurringPayment:   nil,
				err:                epifierrors.ErrPermanent,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Success if GetById returned transaction in already updated state",
			req: &rpActivityPb.UpdateRecurringPaymentStatusRequest{
				RecurringPaymentId: defaultRecurringPaymentId,
				CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
				NextStatus:         rpPb.RecurringPaymentState_CREATION_INITIATED,
			},
			mockRPDaoGetById: mockRPDaoGetById{
				enable:             true,
				recurringPaymentId: defaultRecurringPaymentId,
				recurringPayment:   defaultUpdatedRecurringPayment,
				err:                nil,
			},
			want:    &rpActivityPb.UpdateRecurringPaymentStatusResponse{},
			wantErr: false,
		},
		{
			name: "Error if unable to update and change status",
			req: &rpActivityPb.UpdateRecurringPaymentStatusRequest{
				RecurringPaymentId: defaultRecurringPaymentId,
				CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
				NextStatus:         rpPb.RecurringPaymentState_CREATION_INITIATED,
			},
			mockRPDaoUpdateAndChangeStatus: mockRPDaoUpdateAndChangeStatus{
				enable:           true,
				recurringPayment: defaultRecurringPayment,
				currentStatus:    rpPb.RecurringPaymentState_CREATION_QUEUED,
				nextStatus:       rpPb.RecurringPaymentState_CREATION_INITIATED,
				err:              epifierrors.ErrPermanent,
			},
			mockRPDaoGetById: mockRPDaoGetById{
				enable:             true,
				recurringPaymentId: defaultRecurringPaymentId,
				recurringPayment:   defaultRecurringPayment,
				err:                nil,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Success if UpdateAndChangeStatus dao returned success",
			req: &rpActivityPb.UpdateRecurringPaymentStatusRequest{
				RecurringPaymentId: defaultRecurringPaymentId,
				CurrentStatus:      rpPb.RecurringPaymentState_CREATION_QUEUED,
				NextStatus:         rpPb.RecurringPaymentState_CREATION_INITIATED,
			},
			mockRPDaoUpdateAndChangeStatus: mockRPDaoUpdateAndChangeStatus{
				enable:           true,
				recurringPayment: defaultRecurringPayment,
				currentStatus:    rpPb.RecurringPaymentState_CREATION_QUEUED,
				nextStatus:       rpPb.RecurringPaymentState_CREATION_INITIATED,
				err:              nil,
			},
			mockRPDaoGetById: mockRPDaoGetById{
				enable:             true,
				recurringPaymentId: defaultRecurringPaymentId,
				recurringPayment:   defaultRecurringPayment,
				err:                nil,
			},
			want:    &rpActivityPb.UpdateRecurringPaymentStatusResponse{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockRPDaoUpdateAndChangeStatus.enable {
				md.recurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), tt.mockRPDaoUpdateAndChangeStatus.recurringPayment, tt.mockRPDaoUpdateAndChangeStatus.updateMask, tt.mockRPDaoUpdateAndChangeStatus.currentStatus, tt.mockRPDaoUpdateAndChangeStatus.nextStatus).
					Return(tt.mockRPDaoUpdateAndChangeStatus.err)
			}
			if tt.mockRPDaoGetById.enable {
				md.recurringPaymentDao.EXPECT().GetById(gomock.Any(), tt.mockRPDaoGetById.recurringPaymentId).
					Return(tt.mockRPDaoGetById.recurringPayment, tt.mockRPDaoGetById.err)
			}

			result := &rpActivityPb.UpdateRecurringPaymentStatusResponse{}
			got, err := env.ExecuteActivity(rpNs.UpdateRecurringPaymentStatus, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("UpdateRecurringPayment() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateRecurringPayment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !proto.Equal(result, tt.want) {
				t.Errorf("UpdateRecurringPayment() got = %v, want %v", result, tt.want)
				return
			}

			assertTest()
		})
	}
}
