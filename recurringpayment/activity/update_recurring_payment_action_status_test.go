package activity_test

import (
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
)

func TestProcessor_UpdateRecurringPaymentActionStatus(t *testing.T) {
	const (
		defaultRecurringPaymentActionId = "default_test_recurring_payment_action_id"
		defaultRecurringPaymentId       = "default_test_recurring_payment_id"
		defaultRPAClientReqId           = "default_test_client_req_id"
		defaultVendorReqId              = "default_test_vendor_req_id"
	)

	var (
		defaultRecurringPaymentAction = &rpPb.RecurringPaymentsAction{
			Id:                 defaultRecurringPaymentActionId,
			RecurringPaymentId: defaultRecurringPaymentId,
			ClientRequestId:    defaultRPAClientReqId,
			Action:             rpPb.Action_CREATE,
			State:              rpPb.ActionState_ACTION_CREATED,
			VendorRequestId:    defaultVendorReqId,
			InitiatedBy:        rpPb.InitiatedBy_PAYER,
		}
		defaultUpdatedRecurringPaymentAction = &rpPb.RecurringPaymentsAction{
			Id:                 defaultRecurringPaymentActionId,
			RecurringPaymentId: defaultRecurringPaymentId,
			ClientRequestId:    defaultRPAClientReqId,
			Action:             rpPb.Action_CREATE,
			State:              rpPb.ActionState_ACTION_SUCCESS,
			VendorRequestId:    defaultVendorReqId,
			InitiatedBy:        rpPb.InitiatedBy_PAYER,
		}
	)

	act, md, assertTest := newProcessorWithMocks(t)
	defer assertTest()
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(act)

	type mockRPDaoUpdateAndChangeStatus struct {
		enable           bool
		recurringPayment *rpPb.RecurringPayment
		updateMask       []rpPb.RecurringPaymentFieldMask
		currentStatus    rpPb.RecurringPaymentState
		nextStatus       rpPb.RecurringPaymentState
		err              error
	}
	type mockRPADaoUpdateAndChangeStatus struct {
		enable                 bool
		recurringPaymentAction *rpPb.RecurringPaymentsAction
		updateMask             []rpPb.RecurringPaymentActionFieldMask
		currentStatus          rpPb.ActionState
		nextStatus             rpPb.ActionState
		err                    error
	}
	type mockRPADaoGetById struct {
		enable                   bool
		recurringPaymentActionId string
		recurringPaymentAction   *rpPb.RecurringPaymentsAction
		err                      error
	}
	tests := []struct {
		name                            string
		req                             *rpActivityPb.UpdateRecurringPaymentActionStatusRequest
		mockRPADaoUpdateAndChangeStatus mockRPADaoUpdateAndChangeStatus
		mockRPADaoGetById               mockRPADaoGetById
		want                            *rpActivityPb.UpdateRecurringPaymentActionStatusResponse
		wantErr                         bool
	}{
		{
			name: "Error if GetById dao returned error",
			req: &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
				RecurringPaymentActionId: defaultRecurringPaymentActionId,
				CurrentStatus:            rpPb.ActionState_ACTION_CREATED,
				NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
			},
			mockRPADaoGetById: mockRPADaoGetById{
				enable:                   true,
				recurringPaymentActionId: defaultRecurringPaymentActionId,
				recurringPaymentAction:   nil,
				err:                      epifierrors.ErrPermanent,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Success if GetById returned transaction in already updated state",
			req: &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
				RecurringPaymentActionId: defaultRecurringPaymentActionId,
				CurrentStatus:            rpPb.ActionState_ACTION_CREATED,
				NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
			},
			mockRPADaoGetById: mockRPADaoGetById{
				enable:                   true,
				recurringPaymentActionId: defaultRecurringPaymentActionId,
				recurringPaymentAction:   defaultUpdatedRecurringPaymentAction,
				err:                      nil,
			},
			want:    &rpActivityPb.UpdateRecurringPaymentActionStatusResponse{},
			wantErr: false,
		},
		{
			name: "Error if unable to update and change status",
			req: &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
				RecurringPaymentActionId: defaultRecurringPaymentActionId,
				CurrentStatus:            rpPb.ActionState_ACTION_CREATED,
				NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
			},
			mockRPADaoUpdateAndChangeStatus: mockRPADaoUpdateAndChangeStatus{
				enable:                 true,
				recurringPaymentAction: defaultRecurringPaymentAction,
				updateMask:             []rpPb.RecurringPaymentActionFieldMask{},
				currentStatus:          rpPb.ActionState_ACTION_CREATED,
				nextStatus:             rpPb.ActionState_ACTION_SUCCESS,
				err:                    epifierrors.ErrPermanent,
			},
			mockRPADaoGetById: mockRPADaoGetById{
				enable:                   true,
				recurringPaymentActionId: defaultRecurringPaymentActionId,
				recurringPaymentAction:   defaultRecurringPaymentAction,
				err:                      nil,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Success if updated Action statue successfully",
			req: &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
				RecurringPaymentActionId: defaultRecurringPaymentActionId,
				CurrentStatus:            rpPb.ActionState_ACTION_CREATED,
				NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
			},
			mockRPADaoUpdateAndChangeStatus: mockRPADaoUpdateAndChangeStatus{
				enable:                 true,
				recurringPaymentAction: defaultRecurringPaymentAction,
				updateMask:             []rpPb.RecurringPaymentActionFieldMask{},
				currentStatus:          rpPb.ActionState_ACTION_CREATED,
				nextStatus:             rpPb.ActionState_ACTION_SUCCESS,
				err:                    nil,
			},
			mockRPADaoGetById: mockRPADaoGetById{
				enable:                   true,
				recurringPaymentActionId: defaultRecurringPaymentActionId,
				recurringPaymentAction:   defaultRecurringPaymentAction,
				err:                      nil,
			},
			want:    &rpActivityPb.UpdateRecurringPaymentActionStatusResponse{},
			wantErr: false,
		},
		{
			name: "Success if updated Action statue successfully && update mask is not nil",
			req: &rpActivityPb.UpdateRecurringPaymentActionStatusRequest{
				RecurringPaymentActionId: defaultRecurringPaymentActionId,
				CurrentStatus:            rpPb.ActionState_ACTION_CREATED,
				NextStatus:               rpPb.ActionState_ACTION_SUCCESS,
				ActionDetailedStatusInfo: &rpPb.ActionDetailedStatusInfo{FiStatusCode: "ENACH_EXEC_001"},
			},
			mockRPADaoUpdateAndChangeStatus: mockRPADaoUpdateAndChangeStatus{
				enable:                 true,
				recurringPaymentAction: defaultRecurringPaymentAction,
				updateMask:             []rpPb.RecurringPaymentActionFieldMask{rpPb.RecurringPaymentActionFieldMask_ACTION_DETAILED_STATUS_INFO},
				currentStatus:          rpPb.ActionState_ACTION_CREATED,
				nextStatus:             rpPb.ActionState_ACTION_SUCCESS,
				err:                    nil,
			},
			mockRPADaoGetById: mockRPADaoGetById{
				enable:                   true,
				recurringPaymentActionId: defaultRecurringPaymentActionId,
				recurringPaymentAction:   defaultRecurringPaymentAction,
				err:                      nil,
			},
			want:    &rpActivityPb.UpdateRecurringPaymentActionStatusResponse{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockRPADaoUpdateAndChangeStatus.enable {
				md.recurringPaymentActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), tt.mockRPADaoUpdateAndChangeStatus.recurringPaymentAction, tt.mockRPADaoUpdateAndChangeStatus.updateMask, tt.mockRPADaoUpdateAndChangeStatus.currentStatus, tt.mockRPADaoUpdateAndChangeStatus.nextStatus).
					Return(tt.mockRPADaoUpdateAndChangeStatus.err)
			}
			if tt.mockRPADaoGetById.enable {
				md.recurringPaymentActionDao.EXPECT().GetById(gomock.Any(), tt.mockRPADaoGetById.recurringPaymentActionId).
					Return(tt.mockRPADaoGetById.recurringPaymentAction, tt.mockRPADaoGetById.err)
			}

			result := &rpActivityPb.UpdateRecurringPaymentActionStatusResponse{}
			got, err := env.ExecuteActivity(rpNs.UpdateRecurringPaymentActionStatus, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("UpdateRecurringPayment() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateRecurringPayment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !proto.Equal(result, tt.want) {
				t.Errorf("UpdateRecurringPayment() got = %v, want %v", result, tt.want)
				return
			}

			assertTest()
		})
	}
}
