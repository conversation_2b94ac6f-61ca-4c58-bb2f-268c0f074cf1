package activity_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"testing"

	"github.com/golang/mock/gomock"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/gamma/recurringpayment/internal"
)

func TestProcessor_ProcessRecurringPaymentExecutionV1(t *testing.T) {
	const (
		defaultRecurringPaymentId   = "default_test_recurring_payment_id"
		defaultDedupeRequestId      = "default_test_dedupe_request_id"
		defaultOrderId              = "default_test_order_id"
		defaultPiFrom               = "default_test_pi_from"
		defaultPiTo                 = "default_test_pi_to"
		defaultTransactionId        = "default_test_transaction_id"
		defaultTransactionRemarks   = "default_test_transaction_remarks"
		defaultVendorResponseCode   = "default_test_vendor_response_code"
		defaultVendorResponseReason = "default_test_vendor_response_reason"
	)

	var (
		defaultTransactionAmount = &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        510,
		}

		defaultTransaction = &paymentPb.Transaction{
			Id:              defaultTransactionId,
			PiFrom:          defaultPiFrom,
			PiTo:            defaultPiTo,
			PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
			Amount:          defaultTransactionAmount,
			Status:          paymentPb.TransactionStatus_IN_PROGRESS,
			PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
			Remarks:         defaultTransactionRemarks,
			OrderId:         defaultOrderId,
			DedupeId: &paymentPb.DedupeId{
				RequestId: defaultDedupeRequestId,
			},
			Ownership: commontypes.Ownership_EPIFI_TECH,
		}
	)

	act, md, assertTest := newProcessorWithMocks(t)
	defer assertTest()
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(act)

	type mockRPProcessorV1ExecutePayment struct {
		enable             bool
		recurringPaymentId string
		transaction        *paymentPb.Transaction
		res                *internal.ExecutePaymentRes
		err                error
	}

	tests := []struct {
		name                            string
		req                             *rpActivityPb.ProcessRecurringPaymentExecutionV1Request
		want                            *rpActivityPb.ProcessRecurringPaymentExecutionV1Response
		mockRPProcessorV1ExecutePayment mockRPProcessorV1ExecutePayment
		wantErr                         bool
	}{
		{
			name: "Error if failed to ExecutePayment",
			req: &rpActivityPb.ProcessRecurringPaymentExecutionV1Request{
				RecurringPaymentId:   defaultRecurringPaymentId,
				Transaction:          defaultTransaction,
				RecurringPaymentType: rpPb.RecurringPaymentType_STANDING_INSTRUCTION,
			},
			mockRPProcessorV1ExecutePayment: mockRPProcessorV1ExecutePayment{
				enable:             true,
				recurringPaymentId: defaultRecurringPaymentId,
				transaction:        defaultTransaction,
				//res: &internal.ExecutePaymentRes{
				//	VendorResponseCode:     defaultVendorResponseCode,
				//	VendorResponseReason:   defaultVendorResponseReason,
				//},
				res: nil,
				err: epifierrors.ErrPermanent,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success if ExecutePayment completed successfully",
			req: &rpActivityPb.ProcessRecurringPaymentExecutionV1Request{
				RecurringPaymentId:   defaultRecurringPaymentId,
				Transaction:          defaultTransaction,
				RecurringPaymentType: rpPb.RecurringPaymentType_STANDING_INSTRUCTION,
			},
			mockRPProcessorV1ExecutePayment: mockRPProcessorV1ExecutePayment{
				enable:             true,
				recurringPaymentId: defaultRecurringPaymentId,
				transaction:        defaultTransaction,
				res: &internal.ExecutePaymentRes{
					VendorResponseCode:   defaultVendorResponseCode,
					VendorResponseReason: defaultVendorResponseReason,
				},
				err: nil,
			},
			want: &rpActivityPb.ProcessRecurringPaymentExecutionV1Response{
				ResponseHeader: nil,
				VendorResponse: &rpActivityPb.VendorResponse{
					VendorResponseCode:   defaultVendorResponseCode,
					VendorResponseReason: defaultVendorResponseReason,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockRPProcessorV1ExecutePayment.enable {
				mockProcessor := getProcessorImpl(md, tt.req.GetRecurringPaymentType())
				mockProcessor.EXPECT().ExecutePayment(gomock.Any(), tt.mockRPProcessorV1ExecutePayment.recurringPaymentId, tt.mockRPProcessorV1ExecutePayment.transaction).
					Return(tt.mockRPProcessorV1ExecutePayment.res, tt.mockRPProcessorV1ExecutePayment.err)
			}

			result := &rpActivityPb.ProcessRecurringPaymentExecutionV1Response{}
			got, err := env.ExecuteActivity(rpNs.ProcessRecurringPaymentExecutionV1, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("ProcessRecurringPaymentExecutionV1() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessRecurringPaymentExecutionV1() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !proto.Equal(result, tt.want) {
				t.Errorf("ProcessRecurringPaymentExecutionV1() got = %v, want %v", result, tt.want)
				return
			}

			assertTest()
		})
	}
}
