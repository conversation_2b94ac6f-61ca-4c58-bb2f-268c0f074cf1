package activity

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	rpDaoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	domainExecutionProcessor "github.com/epifi/gamma/recurringpayment/internal/domainexecutionprocessor"
	domainExecutionProcessorMocks "github.com/epifi/gamma/recurringpayment/internal/domainexecutionprocessor/mocks"
)

func TestProcessor_EnquireExecutionStatusAtDomain(t *testing.T) {
	const (
		defaultRecurringPaymentId       = "default_test_recurring_payment_id"
		defaultRecurringPaymentActionId = "default_test_recurring_payment_action_id"
		defaultActorFrom                = "default_test_actor_from"
		defaultActorTo                  = "default_test_actor_to"
		defaultPiFrom                   = "default_test_pi_from"
		defaultPiTo                     = "default_test_pi_to"
	)
	var (
		defaultRecurringPayment = &rpPb.RecurringPayment{
			Id:          defaultRecurringPaymentId,
			FromActorId: defaultActorFrom,
			ToActorId:   defaultActorTo,
			Type:        rpPb.RecurringPaymentType_ENACH_MANDATES,
			PiFrom:      defaultPiFrom,
			PiTo:        defaultPiTo,
			State:       rpPb.RecurringPaymentState_CREATION_QUEUED,
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		}
		defaultRecurringPaymentAction = &rpPb.RecurringPaymentsAction{
			Id:                 defaultRecurringPaymentActionId,
			RecurringPaymentId: defaultRecurringPaymentId,
			Action:             rpPb.Action_EXECUTE,
		}
	)
	type args struct {
		req        *rpActivityPb.EnquireExecutionStatusAtDomainRequest
		setupMocks func(mockRpActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockRpDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainExecutionProcessorFactory *domainExecutionProcessorMocks.MockDomainExecutionProcessorFactory, mockDomainExecutionProcessor *domainExecutionProcessorMocks.MockDomainExecutionProcessor)
	}
	tests := []struct {
		name      string
		args      args
		want      *rpActivityPb.EnquireExecutionStatusAtDomainResponse
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "should return transient failure (error in fetching recurring payment action by id)",
			args: args{
				req: &rpActivityPb.EnquireExecutionStatusAtDomainRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				},
				setupMocks: func(mockRpActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockRpDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainExecutionProcessorFactory *domainExecutionProcessorMocks.MockDomainExecutionProcessorFactory, mockDomainExecutionProcessor *domainExecutionProcessorMocks.MockDomainExecutionProcessor) {
					mockRpActionDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentActionId).Return(nil, errors.New("failed to fetch recurring payment action"))
				},
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return transient failure (error in fetching recurring payment by id)",
			args: args{
				req: &rpActivityPb.EnquireExecutionStatusAtDomainRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				},
				setupMocks: func(mockRpActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockRpDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainExecutionProcessorFactory *domainExecutionProcessorMocks.MockDomainExecutionProcessorFactory, mockDomainExecutionProcessor *domainExecutionProcessorMocks.MockDomainExecutionProcessor) {
					mockRpActionDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentActionId).Return(defaultRecurringPaymentAction, nil)
					mockRpDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(nil, errors.New("failed to fetch recurring payment by id"))
				},
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return permanent failure (no processor found for the recurring payment type)",
			args: args{
				req: &rpActivityPb.EnquireExecutionStatusAtDomainRequest{
					RecurringPaymentActionId: "random-recurring-payment-action-id",
				},
				setupMocks: func(mockRpActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockRpDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainExecutionProcessorFactory *domainExecutionProcessorMocks.MockDomainExecutionProcessorFactory, mockDomainExecutionProcessor *domainExecutionProcessorMocks.MockDomainExecutionProcessor) {
					mockRpActionDao.EXPECT().GetById(gomock.Any(), "random-recurring-payment-action-id").Return(&rpPb.RecurringPaymentsAction{
						RecurringPaymentId: "random-recurring-payment-id",
					}, nil)
					mockRpDao.EXPECT().GetById(gomock.Any(), "random-recurring-payment-id").Return(&rpPb.RecurringPayment{}, nil)
					mockDomainExecutionProcessorFactory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_RECURRING_PAYMENT_TYPE_UNSPECIFIED, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).Return(nil, epifierrors.ErrInvalidArgument)
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "should return transient failure (error in fetching domain processor from factory)",
			args: args{
				req: &rpActivityPb.EnquireExecutionStatusAtDomainRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				},
				setupMocks: func(mockRpActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockRpDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainExecutionProcessorFactory *domainExecutionProcessorMocks.MockDomainExecutionProcessorFactory, mockDomainExecutionProcessor *domainExecutionProcessorMocks.MockDomainExecutionProcessor) {
					mockRpActionDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentActionId).Return(defaultRecurringPaymentAction, nil)
					mockRpDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
					mockDomainExecutionProcessorFactory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_ENACH_MANDATES, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).Return(nil, errors.New("failed to fetch the domain processor"))
				},
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return transient failure (error in fetching domain execution status)",
			args: args{
				req: &rpActivityPb.EnquireExecutionStatusAtDomainRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				},
				setupMocks: func(mockRpActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockRpDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainExecutionProcessorFactory *domainExecutionProcessorMocks.MockDomainExecutionProcessorFactory, mockDomainExecutionProcessor *domainExecutionProcessorMocks.MockDomainExecutionProcessor) {
					mockRpActionDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentActionId).Return(defaultRecurringPaymentAction, nil)
					mockRpDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
					mockDomainExecutionProcessorFactory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_ENACH_MANDATES, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).Return(mockDomainExecutionProcessor, nil)
					mockDomainExecutionProcessor.EXPECT().EnquireExecutionStatus(gomock.Any(), defaultRecurringPaymentActionId).Return(nil, errors.New("failed to enquire execution status at domain"))
				},
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return transient failure (domain execution is not in terminal statue)",
			args: args{
				req: &rpActivityPb.EnquireExecutionStatusAtDomainRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				},
				setupMocks: func(mockRpActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockRpDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainExecutionProcessorFactory *domainExecutionProcessorMocks.MockDomainExecutionProcessorFactory, mockDomainExecutionProcessor *domainExecutionProcessorMocks.MockDomainExecutionProcessor) {
					mockRpActionDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentActionId).Return(defaultRecurringPaymentAction, nil)
					mockRpDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
					mockDomainExecutionProcessorFactory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_ENACH_MANDATES, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).Return(mockDomainExecutionProcessor, nil)
					mockDomainExecutionProcessor.EXPECT().EnquireExecutionStatus(gomock.Any(), defaultRecurringPaymentActionId).Return(&domainExecutionProcessor.EnquireExecutionStatusAtDomainRes{}, nil)
				},
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "successfully enquired the execution status at domain (successful execution at domain)",
			args: args{
				req: &rpActivityPb.EnquireExecutionStatusAtDomainRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				},
				setupMocks: func(mockRpActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockRpDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainExecutionProcessorFactory *domainExecutionProcessorMocks.MockDomainExecutionProcessorFactory, mockDomainExecutionProcessor *domainExecutionProcessorMocks.MockDomainExecutionProcessor) {
					mockRpActionDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentActionId).Return(defaultRecurringPaymentAction, nil)
					mockRpDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
					mockDomainExecutionProcessorFactory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_ENACH_MANDATES, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).Return(mockDomainExecutionProcessor, nil)
					mockDomainExecutionProcessor.EXPECT().EnquireExecutionStatus(gomock.Any(), defaultRecurringPaymentActionId).Return(&domainExecutionProcessor.EnquireExecutionStatusAtDomainRes{
						ExecutionStatus: domainExecutionProcessor.DOMAIN_EXECUTION_STATUS_SUCCESS,
					}, nil)
				},
			},
			want: &rpActivityPb.EnquireExecutionStatusAtDomainResponse{
				ExecutionStatus: rpActivityPb.EnquireExecutionStatusAtDomainResponse_DOMAIN_EXECUTION_STATUS_SUCCESS,
			},
		},
		{
			name: "successfully enquired the execution status at domain (failed execution at domain)",
			args: args{
				req: &rpActivityPb.EnquireExecutionStatusAtDomainRequest{
					RecurringPaymentActionId: defaultRecurringPaymentActionId,
				},
				setupMocks: func(mockRpActionDao *rpDaoMocks.MockRecurringPaymentsActionDao, mockRpDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainExecutionProcessorFactory *domainExecutionProcessorMocks.MockDomainExecutionProcessorFactory, mockDomainExecutionProcessor *domainExecutionProcessorMocks.MockDomainExecutionProcessor) {
					mockRpActionDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentActionId).Return(defaultRecurringPaymentAction, nil)
					mockRpDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
					mockDomainExecutionProcessorFactory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_ENACH_MANDATES, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).Return(mockDomainExecutionProcessor, nil)
					mockDomainExecutionProcessor.EXPECT().EnquireExecutionStatus(gomock.Any(), defaultRecurringPaymentActionId).Return(&domainExecutionProcessor.EnquireExecutionStatusAtDomainRes{
						ExecutionStatus: domainExecutionProcessor.DOMAIN_EXECUTION_STATUS_FAILURE,
					}, nil)
				},
			},
			want: &rpActivityPb.EnquireExecutionStatusAtDomainResponse{
				ExecutionStatus: rpActivityPb.EnquireExecutionStatusAtDomainResponse_DOMAIN_EXECUTION_STATUS_FAILURE,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockRpDao := rpDaoMocks.NewMockRecurringPaymentDao(ctr)
			mockRpActionDao := rpDaoMocks.NewMockRecurringPaymentsActionDao(ctr)
			mockDomainExecutionProcessorFactory := domainExecutionProcessorMocks.NewMockDomainExecutionProcessorFactory(ctr)
			mockDomainExecutionProcessor := domainExecutionProcessorMocks.NewMockDomainExecutionProcessor(ctr)
			p := &Processor{
				recurringPaymentDao:             mockRpDao,
				recurringPaymentsActionDao:      mockRpActionDao,
				domainExecutionProcessorFactory: mockDomainExecutionProcessorFactory,
			}
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)
			tt.args.setupMocks(mockRpActionDao, mockRpDao, mockDomainExecutionProcessorFactory, mockDomainExecutionProcessor)
			result := &rpActivityPb.EnquireExecutionStatusAtDomainResponse{}
			got, err := env.ExecuteActivity(rpNs.EnquireExecutionStatusAtDomain, tt.args.req)
			if got != nil {
				typeValueErr := got.Get(&result)
				if typeValueErr != nil {
					t.Errorf("EnquireExecutionStatus() error = %v failed to fetch type value from convertible", typeValueErr)
					return
				}
			}
			if (err != nil) != tt.wantErr {
				t.Errorf("EnquireExecutionStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && !tt.assertErr(err) {
				t.Errorf("EnquireExecutionStatus() got = %v, want %v", err, tt.want)
				return
			}

			if !tt.wantErr && !proto.Equal(result, tt.want) {
				t.Errorf("EnquireExecutionStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}
