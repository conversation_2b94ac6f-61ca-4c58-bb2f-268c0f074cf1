package activity_test

import (
	"errors"
	"testing"

	"github.com/golang/mock/gomock"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	domainPb "github.com/epifi/gamma/api/order/domain"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
)

func TestProcessor_ProcessPauseUnpauseRecurringPayment(t *testing.T) {
	act, md, assertTest := newProcessorWithMocks(t)
	defer assertTest()
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(act)

	type mockRpClient struct {
		enable bool
		req    *domainPb.ProcessFulfilmentRequest
		res    *domainPb.ProcessFulfilmentResponse
		err    error
	}
	tests := []struct {
		name         string
		req          *rpActivityPb.ProcessPauseUnpauseRecurringPaymentRequest
		mockRpClient mockRpClient
		want         *rpActivityPb.ProcessPauseUnpauseRecurringPaymentResponse
		wantErr      bool
		assertErr    func(err error) bool
	}{
		{
			name: "recurring payment pause processed successfully",
			req: &rpActivityPb.ProcessPauseUnpauseRecurringPaymentRequest{
				RequestHeader: &activityPb.RequestHeader{
					IsLastAttempt: false,
					ClientReqId:   "client-req-id",
					Payload:       []byte{102, 97, 108, 99, 111, 110},
				},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessFulfilmentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "client-req-id",
						IsLastAttempt:               false,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessFulfilmentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_SUCCESS,
					},
				},
				err: nil,
			},
			want:    &rpActivityPb.ProcessPauseUnpauseRecurringPaymentResponse{},
			wantErr: false,
		},
		{
			name: "failed to process recurring payment pause/unpause request due to connection issues",
			req: &rpActivityPb.ProcessPauseUnpauseRecurringPaymentRequest{
				RequestHeader: &activityPb.RequestHeader{
					IsLastAttempt: false,
					ClientReqId:   "client-req-id",
					Payload:       []byte{102, 97, 108, 99, 111, 110},
				},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessFulfilmentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "client-req-id",
						IsLastAttempt:               false,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: nil,
				err: errors.New("error"),
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failure in processing recurring payment pause/unpause request due to transient error",
			req: &rpActivityPb.ProcessPauseUnpauseRecurringPaymentRequest{
				RequestHeader: &activityPb.RequestHeader{
					IsLastAttempt: false,
					ClientReqId:   "client-req-id",
					Payload:       []byte{102, 97, 108, 99, 111, 110},
				},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessFulfilmentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "client-req-id",
						IsLastAttempt:               false,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessFulfilmentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_TRANSIENT_FAILURE,
					},
				},
				err: nil,
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failure in processing recurring payment pause/unpause request due to permanent error",
			req: &rpActivityPb.ProcessPauseUnpauseRecurringPaymentRequest{
				RequestHeader: &activityPb.RequestHeader{
					IsLastAttempt: false,
					ClientReqId:   "client-req-id",
					Payload:       []byte{102, 97, 108, 99, 111, 110},
				},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessFulfilmentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "client-req-id",
						IsLastAttempt:               false,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessFulfilmentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_PERMANENT_FAILURE,
					},
				},
				err: nil,
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent error if last attempt and transient status in process recurring payment pause/unpause request rpc",
			req: &rpActivityPb.ProcessPauseUnpauseRecurringPaymentRequest{
				RequestHeader: &activityPb.RequestHeader{
					IsLastAttempt: true,
					ClientReqId:   "client-req-id",
					Payload:       []byte{102, 97, 108, 99, 111, 110},
				},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessFulfilmentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "client-req-id",
						IsLastAttempt:               true,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessFulfilmentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_TRANSIENT_FAILURE,
					},
				},
				err: nil,
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent error if last attempt and no_op status in process recurring payment pause/unpause request rpc",
			req: &rpActivityPb.ProcessPauseUnpauseRecurringPaymentRequest{
				RequestHeader: &activityPb.RequestHeader{
					IsLastAttempt: true,
					ClientReqId:   "client-req-id",
					Payload:       []byte{102, 97, 108, 99, 111, 110},
				},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessFulfilmentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "client-req-id",
						IsLastAttempt:               true,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessFulfilmentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_NO_OP,
					},
				},
				err: nil,
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "transient error if no_op status and attempt count is left for process recurring payment pause/unpause request rpc",
			req: &rpActivityPb.ProcessPauseUnpauseRecurringPaymentRequest{
				RequestHeader: &activityPb.RequestHeader{
					IsLastAttempt: false,
					ClientReqId:   "client-req-id",
					Payload:       []byte{102, 97, 108, 99, 111, 110},
				},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessFulfilmentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "client-req-id",
						IsLastAttempt:               false,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessFulfilmentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_NO_OP,
					},
				},
				err: nil,
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			if tt.mockRpClient.enable {
				md.rpClient.EXPECT().ProcessPauseUnpause(gomock.Any(), tt.mockRpClient.req).
					Return(tt.mockRpClient.res, tt.mockRpClient.err)
			}

			if _, err := env.ExecuteActivity(rpNs.ProcessPauseUnpauseRecurringPayment,
				tt.req); (err != nil) != tt.wantErr {
				t.Errorf("ProcessPauseUnpauseRecurringPayment() error = %v, wantErr %v", err, tt.wantErr)
			} else if tt.wantErr && !tt.assertErr(err) {
				t.Errorf("ProcessPauseUnpauseRecurringPayment() error = %v assertion failed", err)
				return
			}
		})
	}
}
