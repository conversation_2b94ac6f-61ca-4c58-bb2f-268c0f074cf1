package activity

import (
	"context"

	"go.uber.org/zap"

	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/logger"
)

func (p *Processor) GetEnrichedTransaction(ctx context.Context, req *rpActivityPb.GetEnrichedTransactionRequest) (*rpActivityPb.GetEnrichedTransactionResponse, error) {
	res := &rpActivityPb.GetEnrichedTransactionResponse{}

	recurringPaymentProcessor, err := p.executeRecurringPaymentFactory.GetProcessorImpl(req.GetRecurringPaymentType())
	if err != nil {
		logger.Error(ctx, "error while fetching processor implementation", zap.Error(err))
		return nil, err
	}

	enrichedTransaction, err := recurringPaymentProcessor.GetEnrichedTransaction(ctx, req.GetTransaction())
	if err != nil {
		logger.Error(ctx, "error while GetEnrichedTransaction", zap.Error(err))
		return nil, err
	}

	res.EnrichedTransaction = enrichedTransaction
	return res, nil
}
