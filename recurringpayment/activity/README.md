## Activity

An activity is a fault prone business logic of a workflow.
The purpose of an Activity is to execute a single, well-defined action (either short or long running), such as calling another service, transcoding a media file, or sending an email.

It is possible to implement an Activity fully asynchronously by completing it from a different process.

* An Activity can be implemented as a synchronous method or fully asynchronously involving multiple processes.
* An Activity can be retried indefinitely according to the provided exponential retry policy.
* If for any reason an Activity is not completed within the specified timeout, an error is reported to the Workflow, which decides how to handle it. The duration of an Activity has no limit.



This package contains processor implementation that takes exposes recurring payment specific activities. These activities can then 
be hosted in any namespace and called from any workflow based on the need.

