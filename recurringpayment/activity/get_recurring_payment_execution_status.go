package activity

import (
	"context"
	"time"

	"go.uber.org/zap"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/recurringpayment/internal"
)

func (p *Processor) GetRecurringPaymentExecutionStatus(ctx context.Context, req *rpActivityPb.GetRecurringPaymentExecutionStatusRequest) (*rpActivityPb.GetRecurringPaymentExecutionStatusResponse, error) {
	var (
		res              = &rpActivityPb.GetRecurringPaymentExecutionStatusResponse{}
		paymentStatusRes *internal.EnquirePaymentStatusRes
		executeStatusErr error
	)

	recurringPaymentProcessor, err := p.executeRecurringPaymentFactory.GetProcessorImpl(req.GetRecurringPaymentType())
	if err != nil {
		logger.Error(ctx, "error while fetching processor implementation", zap.Error(err))
		return nil, err
	}

	for true {
		// todo: we should replace with value from req/config
		time.Sleep(5)
		paymentStatusRes, executeStatusErr = recurringPaymentProcessor.EnquirePaymentStatus(ctx, &internal.EnquirePaymentStatusReq{
			OriginalRequestId: req.GetOriginalRequestId(),
			PartnerBank:       req.GetPartnerBank(),
			ActorId:           req.GetActorId(),
			PaymentProtocol:   req.GetPaymentProtocol(),
		})
		if executeStatusErr != nil {
			return nil, executeStatusErr
		}
		if paymentStatusRes.TransactionStatus == paymentPb.TransactionStatus_SUCCESS {
			res.TransactionStatus = paymentPb.TransactionStatus_SUCCESS
			break
		}
		if paymentStatusRes.TransactionStatus != paymentPb.TransactionStatus_IN_PROGRESS {
			res.TransactionStatus = paymentPb.TransactionStatus_FAILED
			break
		}
	}

	res.ExecutedTransactionDetails = &rpActivityPb.ExecutedTransactionDetails{
		Utr:                    paymentStatusRes.Utr,
		RawResponseCode:        paymentStatusRes.RawResponseCode,
		RawResponseDescription: paymentStatusRes.RawResponseDescription,
		StatusCode:             paymentStatusRes.StatusCode,
		StatusDescriptionPayer: paymentStatusRes.StatusDescriptionPayer,
		TransactionExecutedAt:  paymentStatusRes.TransactionTimestamp,
	}
	return res, nil
}
