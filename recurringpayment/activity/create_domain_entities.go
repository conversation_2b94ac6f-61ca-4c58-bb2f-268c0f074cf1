package activity

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	domainCreationProcessor1 "github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor"
)

func (p *Processor) CreateDomainEntities(ctx context.Context, req *rpActivityPb.CreateDomainEntitiesRequest) (*rpActivityPb.CreateDomainEntitiesResponse, error) {
	var (
		res = &rpActivityPb.CreateDomainEntitiesResponse{}
	)

	recurringPayment, err := p.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	if err != nil {
		logger.Error(ctx, "error while fetching recurring payment by id", zap.Error(err))
		return nil, fmt.Errorf("error while fetching recurring payment by id, err: %s, %w", err, epifierrors.ErrTransient)
	}

	domainCreationProcessor, err := p.domainCreationProcessorFactory.GetProcessor(recurringPayment.GetType(), recurringPayment.GetPaymentRoute())
	if err != nil {
		logger.Error(ctx, "error while fetching processor implementation", zap.Error(err))
		if errors.Is(err, epifierrors.ErrInvalidArgument) {
			res.DomainCreationStatus = rpActivityPb.CreateDomainEntitiesResponse_DOMAIN_CREATION_STATUS_FAILURE
			return res, nil
		}
		return nil, fmt.Errorf("error while fetching processor implementation, err: %s, %w", err, epifierrors.ErrTransient)
	}

	err = domainCreationProcessor.CreateDomainEntities(ctx, &domainCreationProcessor1.CreateDomainEntitiesReq{
		RecurringPayment:                    recurringPayment,
		RecurringPaymentActionId:            req.GetRecurringPaymentActionId(),
		Vendor:                              recurringPayment.GetPartnerBank(),
		RecurringPaymentTypeSpecificPayload: req.GetRecurringPaymentTypeSpecificPayload(),
	})
	if err != nil {
		logger.Error(ctx, "error while trying to create domain entities.", zap.Error(err))
		return nil, fmt.Errorf("error while trying to create domain entities, err: %s, %w", err, epifierrors.ErrTransient)
	}

	res.DomainCreationStatus = rpActivityPb.CreateDomainEntitiesResponse_DOMAIN_CREATION_STATUS_SUCCESS
	return res, nil
}
