package activity

import (
	"context"
	"errors"
	"fmt"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"

	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/gamma/recurringpayment/internal/domainrevokeprocessor"
)

func (p *Processor) EnquireRevokeStatusAtDomain(ctx context.Context, req *rpActivityPb.EnquireRevokeStatusAtDomainRequest) (*rpActivityPb.EnquireRevokeStatusAtDomainResponse, error) {
	// fetch the recurring payment action to fetch the recurring payment
	action, actionErr := p.recurringPaymentsActionDao.GetById(ctx, req.GetRecurringPaymentActionId())
	if actionErr != nil {
		return nil, fmt.Errorf("failed to fetch the recurring payment action by id,err: %s %w", actionErr, epifierrors.ErrTransient)
	}
	// fetch the recurring payment by action id to identify the type
	recurringPayment, recurringPaymentErr := p.recurringPaymentDao.GetById(ctx, action.GetRecurringPaymentId())
	if recurringPaymentErr != nil {
		return nil, fmt.Errorf("failed to fetch recurring payment by id,err: %s %w", recurringPaymentErr, epifierrors.ErrTransient)
	}
	ctx = epificontext.WithOwnership(ctx, recurringPayment.GetEntityOwnership())
	// fetch the respective processor for the domain
	domainRevokeProcessor, domainRevokeProcessorErr := p.domainRevokeProcessorFactory.GetProcessor(recurringPayment.GetType(), recurringPayment.GetPaymentRoute())
	switch {
	case errors.Is(domainRevokeProcessorErr, epifierrors.ErrInvalidArgument):
		return nil, fmt.Errorf("no processor found for the given recurring payment,err: %s , %w", domainRevokeProcessorErr, epifierrors.ErrPermanent)
	case domainRevokeProcessorErr != nil:
		return nil, fmt.Errorf("failed to fetch the domain revoke processor, err: %s, %w", domainRevokeProcessorErr, epifierrors.ErrTransient)
	}

	domainRevokeStatusRes, domainRevokeStatusErr := domainRevokeProcessor.EnquireRevokeStatus(ctx, &domainrevokeprocessor.EnquireRevokeStatusAtDomainReq{
		RecurringPaymentActionId: action.GetId(),
	})
	if domainRevokeStatusErr != nil {
		return nil, fmt.Errorf("failed to enquire revoke status at domain,err: %s %w", domainRevokeStatusErr, epifierrors.ErrTransient)
	}
	revokeStatus, revokeStatusErr := convertDomainRevokeStatusToActivityStatus(domainRevokeStatusRes.RevokeStatus)
	if revokeStatusErr != nil {
		return nil, revokeStatusErr
	}
	return &rpActivityPb.EnquireRevokeStatusAtDomainResponse{
		RevokeStatus: revokeStatus,
	}, nil
}

func convertDomainRevokeStatusToActivityStatus(status domainrevokeprocessor.DomainRevokeStatus) (rpActivityPb.RevokeStatusAtDomain, error) {
	switch {
	case status == domainrevokeprocessor.DOMAIN_REVOKE_STATUS_REVOKED:
		return rpActivityPb.RevokeStatusAtDomain_DOMAIN_REVOKE_STATUS_SUCCESS, nil
	case status == domainrevokeprocessor.DOMAIN_REVOKE_STATUS_FAILED:
		return rpActivityPb.RevokeStatusAtDomain_DOMAIN_REVOKE_STATUS_FAILURE, nil
	case status == domainrevokeprocessor.DOMAIN_REVOKE_STATUS_PENDING:
		// return transient error in case domain returns non-terminal state
		// this logic can be modified if required
		return rpActivityPb.RevokeStatusAtDomain_DOMAIN_REVOKE_STATUS_PENDING, fmt.Errorf("non-terminal revoke state at domain,err: %w", epifierrors.ErrTransient)
	default:
		// return transient error in case domain returns non-terminal state
		// this logic can be modified if required
		return rpActivityPb.RevokeStatusAtDomain_DOMAIN_REVOKE_STATUS_UNSPECIFIED, fmt.Errorf("non-terminal revoke state at domain,err: %w", epifierrors.ErrTransient)
	}
}
