// nolint: goimports
package activity

import (
	"context"
	"errors"
	"fmt"

	"github.com/epifi/be-common/pkg/epificontext"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	domainCreationProcessor1 "github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor"
)

func (p *Processor) EnquireDomainActivationStatus(ctx context.Context,
	req *rpActivityPb.EnquireDomainActivationStatusRequest) (*rpActivityPb.EnquireDomainActivationStatusResponse, error) {

	var (
		res = &rpActivityPb.EnquireDomainActivationStatusResponse{}
	)

	recurringPayment, err := p.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	if err != nil {
		logger.Error(ctx, "error while fetching recurring payment by id", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		return nil, fmt.Errorf("error while fetching recurring payment by id, err: %s, %w", err, epifierrors.ErrTransient)
	}

	ctx = epificontext.WithOwnership(ctx, recurringPayment.GetEntityOwnership())

	domainCreationProcessor, err := p.domainCreationProcessorFactory.GetProcessor(recurringPayment.GetType(), recurringPayment.GetPaymentRoute())
	if err != nil {
		logger.Error(ctx, "error while fetching processor implementation", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_TYPE, recurringPayment.GetType().String()))
		if errors.Is(err, epifierrors.ErrInvalidArgument) {
			res.DomainActivationStatus = rpActivityPb.EnquireDomainActivationStatusResponse_DOMAIN_ACTIVATION_FAILURE
			return res, nil
		}
		return nil, fmt.Errorf("error while fetching processor implementation, err: %s, %w", err, epifierrors.ErrTransient)
	}

	enquireDomainActivationStatusRes, err := domainCreationProcessor.EnquireDomainActivationStatus(ctx, &domainCreationProcessor1.EnquireDomainActivationStatusReq{
		RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
	})
	if err != nil {
		logger.Error(ctx, "error while fetching domain activation status", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ACTION_ID, req.GetRecurringPaymentActionId()))
		return nil, fmt.Errorf("error while fetching domain activation status, err: %s, %w", err, epifierrors.ErrTransient)
	}

	res.ActionDetailedStatusInfo = enquireDomainActivationStatusRes.ActionDetailedStatusInfo

	switch enquireDomainActivationStatusRes.ActivationStatus {
	// If domain creation is in Progress. we are returning transient so that activity is automatically retried
	case domainCreationProcessor1.DOMAIN_ACTIVATION_STATUS_INPROGRESS:
		logger.Info(ctx, "got domain activation status InProgress")
		return nil, fmt.Errorf("got domain activation status InProgress, %w", epifierrors.ErrInProgress)
	case domainCreationProcessor1.DOMAIN_ACTIVATION_STATUS_SUCCESS:
		res.DomainActivationStatus = rpActivityPb.EnquireDomainActivationStatusResponse_DOMAIN_ACTIVATION_SUCCESS
	case domainCreationProcessor1.DOMAIN_ACTIVATION_STATUS_FAILURE:
		res.DomainActivationStatus = rpActivityPb.EnquireDomainActivationStatusResponse_DOMAIN_ACTIVATION_FAILURE
	default:
		return nil, fmt.Errorf("domain activation status is not specified, %w", epifierrors.ErrPermanent)
	}

	res.StatusUpdatedAt = enquireDomainActivationStatusRes.StatusUpdatedAt

	return res, nil
}
