package activity

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
)

func (p *Processor) CreateRecurringPaymentAction(ctx context.Context, req *rpActivityPb.CreateRecurringPaymentActionRequest) (*rpActivityPb.CreateRecurringPaymentActionResponse, error) {
	res := &rpActivityPb.CreateRecurringPaymentActionResponse{}

	// Required for Idempotency check
	recurringPaymentAction, err := p.recurringPaymentsActionDao.GetByClientRequestId(ctx, req.GetRecurringPaymentAction().GetClientRequestId(), false)
	if err == nil {
		logger.Info(ctx, fmt.Sprintf("recurring payment action with clientReqId: %s already exists", req.GetRecurringPaymentAction().GetClientRequestId()))
		res.RecurringPaymentAction = recurringPaymentAction
		return res, nil
	}
	if !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "failed to fetch recurring payment action by clientReqId", zap.Error(err))
		return nil, fmt.Errorf("unable to fetch recurring payment action by clientReqId %w", err)
	}

	// Creating Recurring Payment Action
	recurringPaymentAction, err = p.recurringPaymentsActionDao.Create(ctx, req.GetRecurringPaymentAction())
	if err != nil {
		logger.Error(ctx, "recurring payment action creation failed", zap.Error(err))
		return nil, fmt.Errorf("failed to create recurring payment action %w", err)
	}

	res.RecurringPaymentAction = recurringPaymentAction
	return res, nil
}
