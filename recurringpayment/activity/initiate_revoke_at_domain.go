package activity

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/gamma/recurringpayment/internal/domainrevokeprocessor"
)

// InitiateRevokeAtDomain is responsible for initiating the revoke of the recurring payment with the domain
func (p *Processor) InitiateRevokeAtDomain(ctx context.Context, req *rpActivityPb.InitiateRevokeAtDomainRequest) (*rpActivityPb.InitiateRevokeAtDomainResponse, error) {
	// fetch the recurring payment by id to identify the type
	recurringPayment, recurringPaymentErr := p.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	if recurringPaymentErr != nil {
		logger.Error(ctx, "failed to fetch recurring payment by id", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		return nil, fmt.Errorf("failed to fetch recurring payment by id, err: %s, %w", recurringPaymentErr, epifierrors.ErrTransient)
	}
	ctx = epificontext.WithOwnership(ctx, recurringPayment.GetEntityOwnership())
	// fetch the respective processor for the domain
	domainRevokeProcessor, domainRevokeProcessorErr := p.domainRevokeProcessorFactory.GetProcessor(recurringPayment.GetType(), recurringPayment.GetPaymentRoute())

	switch {
	case errors.Is(domainRevokeProcessorErr, epifierrors.ErrInvalidArgument):
		return nil, fmt.Errorf("no processor found for the given recurring payment,err: %s , %w", domainRevokeProcessorErr, epifierrors.ErrPermanent)
	case domainRevokeProcessorErr != nil:
		return nil, fmt.Errorf("failed to fetch the domain revoke processor, err: %s, %w", domainRevokeProcessorErr, epifierrors.ErrTransient)
	}

	// initiate revoke at domain
	_, domainRevokeErr := domainRevokeProcessor.InitiateRevokeAtDomain(ctx, &domainrevokeprocessor.InitiateRevokeAtDomainReq{
		RecurringPaymentActionId: req.GetRecurringPaymentActionId(),
	})

	if domainRevokeErr != nil {
		return nil, fmt.Errorf("failed to initiate execution at domain, err: %s , %w", domainRevokeErr, epifierrors.ErrTransient)
	}

	return &rpActivityPb.InitiateRevokeAtDomainResponse{}, nil
}
