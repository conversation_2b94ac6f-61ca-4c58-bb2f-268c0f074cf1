package activity

import (
	"context"

	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/logger"
)

func (p *Processor) GetRecurringPayment(ctx context.Context, req *rpActivityPb.GetRecurringPaymentRequest) (*rpActivityPb.GetRecurringPaymentResponse, error) {
	res := &rpActivityPb.GetRecurringPaymentResponse{}
	lg := activity.GetLogger(ctx)

	recurringPayment, err := p.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	if err != nil {
		lg.Error("failed to trying to fetch recurringPayment By Id",
			zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(err))
		return nil, err
	}

	res.RecurringPayment = recurringPayment
	return res, nil
}
