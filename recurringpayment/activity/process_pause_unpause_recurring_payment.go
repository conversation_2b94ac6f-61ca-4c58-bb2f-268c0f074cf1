// nolint:dupl
package activity

import (
	"context"

	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"

	domainPb "github.com/epifi/gamma/api/order/domain"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	celestialPkg "github.com/epifi/gamma/pkg/epifitemporal/celestial"
)

// ProcessPauseUnpauseRecurringPayment - activity wrapper to process recurring payment pause/unpause request
func (p *Processor) ProcessPauseUnpauseRecurringPayment(ctx context.Context, req *rpActivityPb.ProcessPauseUnpauseRecurringPaymentRequest) (*rpActivityPb.ProcessPauseUnpauseRecurringPaymentResponse, error) {
	res := &rpActivityPb.ProcessPauseUnpauseRecurringPaymentResponse{}
	lg := activity.GetLogger(ctx)

	processPauseUnpauseRes, err := p.rpClient.ProcessPauseUnpause(ctx, &domainPb.ProcessFulfilmentRequest{
		RequestHeader: &domainPb.DomainRequestHeader{
			ClientRequestId:             req.GetRequestHeader().GetClientReqId(),
			IsLastAttempt:               req.GetRequestHeader().GetIsLastAttempt(),
			ShouldForceProcessOrder:     false,
			ShouldOverrideTerminalState: false,
		},
		Payload: req.GetRequestHeader().GetPayload(),
	})
	if err != nil {
		lg.Error("failed to process recurring payment pause/unpause request due to connection issues", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	err = celestialPkg.ConvertDomainStatusCodeToError(processPauseUnpauseRes.GetResponseHeader().GetStatus(), req.GetRequestHeader().GetIsLastAttempt())
	if err != nil {
		lg.Error("error occurred while processing recurring payment pause/unpause request",
			zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
		return nil, err
	}

	lg.Debug("successfully processed recurring payment pause/unpause request for client req id", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
	return res, nil
}
