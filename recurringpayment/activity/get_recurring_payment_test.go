package activity_test

import (
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
)

func TestProcessor_GetRecurringPayment(t *testing.T) {
	const (
		defaultRecurringPaymentId = "default_test_recurring_payment_id"
		defaultActorFrom          = "default_test_actor_from"
		defaultActorTo            = "default_test_actor_to"
		defaultPiFrom             = "default_test_pi_from"
		defaultPiTo               = "default_test_pi_to"
	)

	var (
		defaultRecurringPayment = &rpPb.RecurringPayment{
			Id:          defaultRecurringPaymentId,
			FromActorId: defaultActorFrom,
			ToActorId:   defaultActorTo,
			Type:        rpPb.RecurringPaymentType_STANDING_INSTRUCTION,
			PiFrom:      defaultPiFrom,
			PiTo:        defaultPiTo,
			State:       rpPb.RecurringPaymentState_ACTIVATED,
		}
	)

	act, md, assertTest := newProcessorWithMocks(t)
	defer assertTest()
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(act)

	type mockRecurringPaymentDaoGetById struct {
		enable             bool
		recurringPaymentId string
		recurringPayment   *rpPb.RecurringPayment
		err                error
	}

	tests := []struct {
		name                           string
		req                            *rpActivityPb.GetRecurringPaymentRequest
		mockRecurringPaymentDaoGetById mockRecurringPaymentDaoGetById
		want                           *rpActivityPb.GetRecurringPaymentResponse
		wantErr                        bool
	}{
		{
			name: "Error if failed to GetById",
			req: &rpActivityPb.GetRecurringPaymentRequest{
				RecurringPaymentId: defaultRecurringPaymentId,
			},
			mockRecurringPaymentDaoGetById: mockRecurringPaymentDaoGetById{
				enable:             true,
				recurringPaymentId: defaultRecurringPaymentId,
				recurringPayment:   nil,
				err:                epifierrors.ErrPermanent,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Success if fetched the Recurring payment successfully",
			req: &rpActivityPb.GetRecurringPaymentRequest{
				RecurringPaymentId: defaultRecurringPaymentId,
			},
			mockRecurringPaymentDaoGetById: mockRecurringPaymentDaoGetById{
				enable:             true,
				recurringPaymentId: defaultRecurringPaymentId,
				recurringPayment:   defaultRecurringPayment,
				err:                nil,
			},
			want: &rpActivityPb.GetRecurringPaymentResponse{
				RecurringPayment: defaultRecurringPayment,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockRecurringPaymentDaoGetById.enable {
				md.recurringPaymentDao.EXPECT().GetById(gomock.Any(), tt.mockRecurringPaymentDaoGetById.recurringPaymentId).
					Return(tt.mockRecurringPaymentDaoGetById.recurringPayment, tt.mockRecurringPaymentDaoGetById.err)
			}

			result := &rpActivityPb.GetRecurringPaymentResponse{}
			got, err := env.ExecuteActivity(rpNs.GetRecurringPayment, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("GetRecurringPayment() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			if (err != nil) != tt.wantErr {
				t.Errorf("GetRecurringPayment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !proto.Equal(result, tt.want) {
				t.Errorf("GetRecurringPayment() got = %v, want %v", got, tt.want)
				return
			}

			assertTest()
		})
	}
}
