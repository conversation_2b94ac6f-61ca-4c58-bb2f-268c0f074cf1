package activity

import (
	"context"
	"errors"
	"fmt"
	"time"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	rpDao "github.com/epifi/gamma/recurringpayment/dao"
)

// CheckAndUpdateRecurringPaymentToCompletedState is used to update the recurring payment status to completed post a successful execution
// recurring payment can go to completed state in the following scenrios:
// 1. end date has passed
// 2. one time execution is done
// todo(<PERSON><PERSON><PERSON>): confirm if we have to update the recurring payment to completed state if the max number of allowed transactions is reached, as of now we are using this field only for the validation but not updating it to the completed state.
// NOTE: while implementation, since we do not have much clarity, we have added lot of todos as this will only be used for ENACH execution. All the todos will be picked once we migrate other type of recurring payments to the V1 workflows
func (p *Processor) CheckAndUpdateRecurringPaymentToCompletedState(ctx context.Context, req *rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateRequest) (*rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateResponse, error) {
	// fetch the recurring payment by id
	recurringPayment, recurringPaymentErr := p.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	if recurringPaymentErr != nil {
		return nil, fmt.Errorf("failed to fetch recurring payment by id, err: %s %w", recurringPaymentErr, epifierrors.ErrTransient)
	}
	// check if recurring payment is already in completed state for idempotancy
	if recurringPayment.GetState() == rpPb.RecurringPaymentState_COMPLETED {
		return &rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateResponse{}, nil
	}
	isCompleted, isCompletedErr := p.isRecurringPaymentCompleted(ctx, recurringPayment)
	if isCompletedErr != nil {
		return nil, isCompletedErr
	}
	if isCompleted {
		recurringPaymentUpdateErr := p.updateRecurringPaymentToCompletedState(ctx, recurringPayment)
		if recurringPaymentUpdateErr != nil {
			return nil, recurringPaymentUpdateErr
		}
	}

	return &rpActivityPb.CheckAndUpdateRecurringPaymentToCompletedStateResponse{}, nil
}

func (p *Processor) isRecurringPaymentCompleted(ctx context.Context, recurringPayment *rpPb.RecurringPayment) (bool, error) {
	// step 1: check if end date has passed
	if time.Since(recurringPayment.GetInterval().GetEndTime().AsTime()) >= 0 {
		return true, nil
	}

	// step 2: check if only one time execution was allowed for the recurring payment
	if recurringPayment.GetRecurrenceRule().GetAllowedFrequency() == rpPb.AllowedFrequency_ONE_TIME {
		// fetch the latest execute action taken on the recurring payment
		// todo(Harleen Singh): evaluate if in_progress execution state has to be considred here
		action, actionErr := p.recurringPaymentsActionDao.GetByRecurringPaymentId(ctx, recurringPayment.GetId(), rpDao.WithActionsFilter([]rpPb.Action{rpPb.Action_EXECUTE}), rpDao.WithActionStateFilter([]rpPb.ActionState{rpPb.ActionState_ACTION_SUCCESS}), rpDao.WithOrderByCreatedAt(true))
		if actionErr != nil && !errors.Is(actionErr, epifierrors.ErrRecordNotFound) {
			return false, fmt.Errorf("failed to fetch the execute actions,err: %s %w", epifierrors.ErrRecordNotFound.Error(), epifierrors.ErrTransient)
		}
		// no successful execution has been done for the given recurring payment
		if errors.Is(actionErr, epifierrors.ErrRecordNotFound) || len(action) == 0 {
			return false, nil
		}
		return true, nil
	}
	// todo(Harleen Singh): evaluate if we have to mark recurring payment state for other actions like pause etc.
	return false, nil
}

func (p *Processor) updateRecurringPaymentToCompletedState(ctx context.Context, recurringPayment *rpPb.RecurringPayment) error {
	// todo(Harleen Singh): evaluate execution is only expected for recurring payments in activated state
	recurringPaymentUpdateErr := p.recurringPaymentDao.UpdateAndChangeStatus(ctx, recurringPayment, []rpPb.RecurringPaymentFieldMask{rpPb.RecurringPaymentFieldMask_STATE}, rpPb.RecurringPaymentState_ACTIVATED, rpPb.RecurringPaymentState_COMPLETED)
	switch {
	case errors.Is(recurringPaymentUpdateErr, epifierrors.ErrNoRowsAffected):
		return fmt.Errorf("no rows affected,err: %w", epifierrors.ErrPermanent)
	case recurringPaymentUpdateErr != nil:
		return fmt.Errorf("failed to update the recurring payment,err: %s %w", recurringPaymentUpdateErr, epifierrors.ErrTransient)
	default:
		return nil
	}
}
