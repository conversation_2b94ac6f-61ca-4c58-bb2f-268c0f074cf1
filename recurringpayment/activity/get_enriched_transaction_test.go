package activity_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"testing"

	"github.com/golang/mock/gomock"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	internalMocks "github.com/epifi/gamma/recurringpayment/internal/mocks"
)

func TestProcessor_GetEnrichedTransaction(t *testing.T) {
	const (
		defaultTransactionId      = "default_test_transaction_id"
		defaultOrderId            = "default_test_order_id"
		defaultPiFrom             = "default_test_pi_from"
		defaultPiTo               = "default_test_pi_to"
		defaultTransactionRemarks = "default_test_transaction_remarks"
		defaultDedupeRequestId    = "default_test_dedupe_request_id"
	)

	var (
		defaultTransactionAmount = &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        510,
		}
		defaultTransaction = &paymentPb.Transaction{
			PiFrom:      defaultPiFrom,
			PiTo:        defaultPiTo,
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			Amount:      defaultTransactionAmount,
			Status:      paymentPb.TransactionStatus_CREATED,
			Remarks:     defaultTransactionRemarks,
			OrderId:     defaultOrderId,
			DedupeId: &paymentPb.DedupeId{
				RequestId: defaultDedupeRequestId,
			},
			Ownership: commontypes.Ownership_EPIFI_TECH,
		}
		defaultEnrichedTransaction = &paymentPb.Transaction{
			PiFrom:          defaultPiFrom,
			PiTo:            defaultPiTo,
			PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
			Amount:          defaultTransactionAmount,
			Status:          paymentPb.TransactionStatus_CREATED,
			PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
			Remarks:         defaultTransactionRemarks,
			OrderId:         defaultOrderId,
			DedupeId: &paymentPb.DedupeId{
				RequestId: defaultDedupeRequestId,
			},
			Ownership: commontypes.Ownership_EPIFI_TECH,
		}
	)

	act, md, assertTest := newProcessorWithMocks(t)
	defer assertTest()
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(act)

	type mockRPProcessorV1GetEnrichedTxn struct {
		enable              bool
		transaction         *paymentPb.Transaction
		enrichedTransaction *paymentPb.Transaction
		err                 error
	}

	tests := []struct {
		name                            string
		req                             *rpActivityPb.GetEnrichedTransactionRequest
		want                            *rpActivityPb.GetEnrichedTransactionResponse
		mockRPProcessorV1GetEnrichedTxn mockRPProcessorV1GetEnrichedTxn
		wantErr                         bool
	}{
		{
			name: "Error if failed to getEnrichedTransaction",
			mockRPProcessorV1GetEnrichedTxn: mockRPProcessorV1GetEnrichedTxn{
				enable:              true,
				transaction:         defaultTransaction,
				enrichedTransaction: nil,
				err:                 epifierrors.ErrPermanent,
			},
			req: &rpActivityPb.GetEnrichedTransactionRequest{
				Transaction:          defaultTransaction,
				RecurringPaymentType: rpPb.RecurringPaymentType_STANDING_INSTRUCTION,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Success if getEnrichedTransaction returned the transaction",
			mockRPProcessorV1GetEnrichedTxn: mockRPProcessorV1GetEnrichedTxn{
				enable:              true,
				transaction:         defaultTransaction,
				enrichedTransaction: defaultEnrichedTransaction,
				err:                 nil,
			},
			req: &rpActivityPb.GetEnrichedTransactionRequest{
				Transaction:          defaultTransaction,
				RecurringPaymentType: rpPb.RecurringPaymentType_STANDING_INSTRUCTION,
			},
			want: &rpActivityPb.GetEnrichedTransactionResponse{
				EnrichedTransaction: defaultEnrichedTransaction,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockRPProcessorV1GetEnrichedTxn.enable {
				mockProcessor := getProcessorImpl(md, tt.req.GetRecurringPaymentType())
				mockProcessor.EXPECT().GetEnrichedTransaction(gomock.Any(), tt.mockRPProcessorV1GetEnrichedTxn.transaction).
					Return(tt.mockRPProcessorV1GetEnrichedTxn.enrichedTransaction, tt.mockRPProcessorV1GetEnrichedTxn.err)
			}

			result := &rpActivityPb.GetEnrichedTransactionResponse{}
			got, err := env.ExecuteActivity(rpNs.GetEnrichedTransaction, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("GetEnrichedTransaction() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			if (err != nil) != tt.wantErr {
				t.Errorf("GetEnrichedTransaction() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !proto.Equal(result, tt.want) {
				t.Errorf("GetEnrichedTransaction() got = %v, want %v", got, tt.want)
				return
			}

			assertTest()
		})
	}
}

func getProcessorImpl(mockDependencies *mockedDependencies, paymentType rpPb.RecurringPaymentType) *internalMocks.MockExecuteRecurringPaymentNoAuthProcessorV1 {
	switch paymentType {
	case rpPb.RecurringPaymentType_STANDING_INSTRUCTION:
		return mockDependencies.StandingInstructionExecutor
	case rpPb.RecurringPaymentType_UPI_MANDATES:
		return mockDependencies.UPIMandatesExecutor
	default:
		return nil
	}
}
