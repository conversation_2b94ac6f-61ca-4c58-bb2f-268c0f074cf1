package activity

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	epifitemporalTest "github.com/epifi/be-common/pkg/epifitemporal/test"
	rpDaoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	domainCreationProcessor1 "github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor"
	domainCreationProcessorMocks "github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor/mocks"
)

var (
	wts epifitemporalTest.WorkflowTestSuite
)

func TestProcessor_CreateDomainEntities(t *testing.T) {
	const (
		defaultRecurringPaymentId       = "default_test_recurring_payment_id"
		defaultRecurringPaymentActionId = "default_test_recurring_payment_action_id"
		defaultActorFrom                = "default_test_actor_from"
		defaultActorTo                  = "default_test_actor_to"
		defaultPiFrom                   = "default_test_pi_from"
		defaultPiTo                     = "default_test_pi_to"
	)

	var (
		defaultRecurringPayment = &rpPb.RecurringPayment{
			Id:          defaultRecurringPaymentId,
			FromActorId: defaultActorFrom,
			ToActorId:   defaultActorTo,
			Type:        rpPb.RecurringPaymentType_STANDING_INSTRUCTION,
			PiFrom:      defaultPiFrom,
			PiTo:        defaultPiTo,
			State:       rpPb.RecurringPaymentState_CREATION_QUEUED,
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		}
		defaultCreateDomainEntitiesReq = &domainCreationProcessor1.CreateDomainEntitiesReq{
			RecurringPayment:         defaultRecurringPayment,
			RecurringPaymentActionId: defaultRecurringPaymentActionId,
			Vendor:                   commonvgpb.Vendor_FEDERAL_BANK,
		}
	)

	tests := []struct {
		name       string
		req        *rpActivityPb.CreateDomainEntitiesRequest
		setupMocks func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1)
		want       *rpActivityPb.CreateDomainEntitiesResponse
		wantErr    bool
	}{
		{
			name: "Should return error if unable to fetch recurring payment by id",
			req: &rpActivityPb.CreateDomainEntitiesRequest{
				RecurringPaymentId:       defaultRecurringPaymentId,
				RecurringPaymentActionId: defaultRecurringPaymentActionId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(nil, epifierrors.ErrTransient)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Should return FAILURE status when domain entity creation processor returned invalid argument error",
			req: &rpActivityPb.CreateDomainEntitiesRequest{
				RecurringPaymentId:       defaultRecurringPaymentId,
				RecurringPaymentActionId: defaultRecurringPaymentActionId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
				mockDomainCreationProcessorFactory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_STANDING_INSTRUCTION, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).Return(nil, epifierrors.ErrInvalidArgument)
			},
			want: &rpActivityPb.CreateDomainEntitiesResponse{
				DomainCreationStatus: rpActivityPb.CreateDomainEntitiesResponse_DOMAIN_CREATION_STATUS_FAILURE,
			},
			wantErr: false,
		},
		{
			name: "Should return error if CreateDomainEntities returned transient error",
			req: &rpActivityPb.CreateDomainEntitiesRequest{
				RecurringPaymentId:       defaultRecurringPaymentId,
				RecurringPaymentActionId: defaultRecurringPaymentActionId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
				mockDomainCreationProcessorFactory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_STANDING_INSTRUCTION, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).Return(mockDomainCreationProcessor, nil)
				mockDomainCreationProcessor.EXPECT().CreateDomainEntities(gomock.Any(), defaultCreateDomainEntitiesReq).Return(epifierrors.ErrTransient)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Should return Success if CreateDomainEntities processor didn't return error",
			req: &rpActivityPb.CreateDomainEntitiesRequest{
				RecurringPaymentId:       defaultRecurringPaymentId,
				RecurringPaymentActionId: defaultRecurringPaymentActionId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
				mockDomainCreationProcessorFactory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_STANDING_INSTRUCTION, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).Return(mockDomainCreationProcessor, nil)
				mockDomainCreationProcessor.EXPECT().CreateDomainEntities(gomock.Any(), defaultCreateDomainEntitiesReq).Return(nil)
			},
			want: &rpActivityPb.CreateDomainEntitiesResponse{
				DomainCreationStatus: rpActivityPb.CreateDomainEntitiesResponse_DOMAIN_CREATION_STATUS_SUCCESS,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockRPDao := rpDaoMocks.NewMockRecurringPaymentDao(ctr)
			mockDomainCreationProcessorFactory := domainCreationProcessorMocks.NewMockDomainCreationProcessorFactory(ctr)
			mockDomainCreationProcessor := domainCreationProcessorMocks.NewMockDomainCreationProcessorV1(ctr)

			p := &Processor{
				recurringPaymentDao:            mockRPDao,
				domainCreationProcessorFactory: mockDomainCreationProcessorFactory,
			}

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			tt.setupMocks(mockRPDao, mockDomainCreationProcessorFactory, mockDomainCreationProcessor)

			result := &rpActivityPb.CreateDomainEntitiesResponse{}
			got, err := env.ExecuteActivity(rpNs.CreateDomainEntities, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("CreateDomainEntities() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			if (err != nil) != tt.wantErr {
				t.Errorf("CreateDomainEntities() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !proto.Equal(result, tt.want) {
				t.Errorf("CreateDomainEntities() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
