// nolint:dupl
package activity

import (
	"context"

	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"

	domainPb "github.com/epifi/gamma/api/order/domain"
	celestialPkg "github.com/epifi/gamma/pkg/epifitemporal/celestial"
)

// ProcessRecurringPaymentRevoke - activity wrapper to process recurring payment revoke request
func (p *Processor) ProcessRecurringPaymentRevoke(ctx context.Context, req *activityPb.Request) (*activityPb.Response, error) {
	res := &activityPb.Response{}
	lg := activity.GetLogger(ctx)

	processRpRevokeRes, err := p.rpClient.ProcessRecurringPaymentRevoke(ctx, &domainPb.ProcessFulfilmentRequest{
		RequestHeader: &domainPb.DomainRequestHeader{
			ClientRequestId:             req.GetClientReqId(),
			IsLastAttempt:               req.GetRequestHeader().GetIsLastAttempt(),
			ShouldForceProcessOrder:     false,
			ShouldOverrideTerminalState: false,
		},
		Payload: req.GetPayload(),
	})
	if err != nil {
		lg.Error("failed to process recurring payment revoke request due to connection issues", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	err = celestialPkg.ConvertDomainStatusCodeToError(processRpRevokeRes.GetResponseHeader().GetStatus(), req.GetRequestHeader().GetIsLastAttempt())
	if err != nil {
		lg.Error("error occurred while processing recurring payment revoke request",
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.Error(err))
		return nil, err
	}

	lg.Debug("successfully processed recurring payment revoke request for client req id", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
	return res, nil
}
