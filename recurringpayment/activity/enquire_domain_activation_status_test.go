package activity

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/pkg/epifierrors"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	rpDaoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	domainCreationProcessor1 "github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor"
	domainCreationProcessorMocks "github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor/mocks"
)

func TestProcessor_EnquireDomainActivationStatus(t *testing.T) {

	const (
		defaultRecurringPaymentId       = "default_test_recurring_payment_id"
		defaultRecurringPaymentActionId = "default_test_recurring_payment_action_id"
		defaultActorFrom                = "default_test_actor_from"
		defaultActorTo                  = "default_test_actor_to"
		defaultPiFrom                   = "default_test_pi_from"
		defaultPiTo                     = "default_test_pi_to"
	)

	var (
		defaultRecurringPayment = &rpPb.RecurringPayment{
			Id:          defaultRecurringPaymentId,
			FromActorId: defaultActorFrom,
			ToActorId:   defaultActorTo,
			Type:        rpPb.RecurringPaymentType_STANDING_INSTRUCTION,
			PiFrom:      defaultPiFrom,
			PiTo:        defaultPiTo,
			State:       rpPb.RecurringPaymentState_CREATION_QUEUED,
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		}
		defaultEnquireDomainActivationStatusRequest = &domainCreationProcessor1.EnquireDomainActivationStatusReq{
			RecurringPaymentActionId: defaultRecurringPaymentActionId,
		}

		defaultEnquireDomainActivationStatusSuccessResponse = &domainCreationProcessor1.EnquireDomainActivationStatusRes{
			ActivationStatus: domainCreationProcessor1.DOMAIN_ACTIVATION_STATUS_SUCCESS,
		}

		defaultEnquireDomainActivationStatusFailureResponse = &domainCreationProcessor1.EnquireDomainActivationStatusRes{
			ActivationStatus: domainCreationProcessor1.DOMAIN_ACTIVATION_STATUS_FAILURE,
			ActionDetailedStatusInfo: &rpPb.ActionDetailedStatusInfo{
				FiStatusCode:     "PG_GENERIC_FAILURE",
				ErrorDescription: "Mandate registration failed due to some unknown reason",
			},
		}

		defaultEnquireDomainActivationStatusInProgressResponse = &domainCreationProcessor1.EnquireDomainActivationStatusRes{
			ActivationStatus: domainCreationProcessor1.DOMAIN_ACTIVATION_STATUS_INPROGRESS,
		}
	)

	tests := []struct {
		name       string
		req        *rpActivityPb.EnquireDomainActivationStatusRequest
		setupMocks func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao,
			mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory,
			mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1)
		want    *rpActivityPb.EnquireDomainActivationStatusResponse
		wantErr bool
	}{
		{
			name: "Should return ERROR if unable to fetch recurring payment by id",
			req: &rpActivityPb.EnquireDomainActivationStatusRequest{
				RecurringPaymentId:       defaultRecurringPaymentId,
				RecurringPaymentActionId: defaultRecurringPaymentActionId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(nil, epifierrors.ErrTransient)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Should return Enquiry FAILURE status when enquire domain activation processor returned invalid argument error",
			req: &rpActivityPb.EnquireDomainActivationStatusRequest{
				RecurringPaymentId:       defaultRecurringPaymentId,
				RecurringPaymentActionId: defaultRecurringPaymentActionId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
				mockDomainCreationProcessorFactory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_STANDING_INSTRUCTION, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).Return(nil, epifierrors.ErrInvalidArgument)
			},
			want: &rpActivityPb.EnquireDomainActivationStatusResponse{
				DomainActivationStatus: rpActivityPb.EnquireDomainActivationStatusResponse_DOMAIN_ACTIVATION_FAILURE,
			},
			wantErr: false,
		},
		{
			name: "Should return ERROR if EnquireDomainActivationStatus returned transient error",
			req: &rpActivityPb.EnquireDomainActivationStatusRequest{
				RecurringPaymentId:       defaultRecurringPaymentId,
				RecurringPaymentActionId: defaultRecurringPaymentActionId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
				mockDomainCreationProcessorFactory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_STANDING_INSTRUCTION, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).Return(mockDomainCreationProcessor, nil)
				mockDomainCreationProcessor.EXPECT().EnquireDomainActivationStatus(gomock.Any(), defaultEnquireDomainActivationStatusRequest).Return(nil, epifierrors.ErrTransient)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Should return SUCCESS if domain activation enquiry return activation status as success",
			req: &rpActivityPb.EnquireDomainActivationStatusRequest{
				RecurringPaymentId:       defaultRecurringPaymentId,
				RecurringPaymentActionId: defaultRecurringPaymentActionId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
				mockDomainCreationProcessorFactory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_STANDING_INSTRUCTION, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).Return(mockDomainCreationProcessor, nil)
				mockDomainCreationProcessor.EXPECT().EnquireDomainActivationStatus(gomock.Any(), defaultEnquireDomainActivationStatusRequest).Return(defaultEnquireDomainActivationStatusSuccessResponse, nil)
			},
			want: &rpActivityPb.EnquireDomainActivationStatusResponse{
				DomainActivationStatus: rpActivityPb.EnquireDomainActivationStatusResponse_DOMAIN_ACTIVATION_SUCCESS,
			},
			wantErr: false,
		},
		{
			name: "Should return FAILURE if domain activation enquiry return activation status as failed",
			req: &rpActivityPb.EnquireDomainActivationStatusRequest{
				RecurringPaymentId:       defaultRecurringPaymentId,
				RecurringPaymentActionId: defaultRecurringPaymentActionId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
				mockDomainCreationProcessorFactory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_STANDING_INSTRUCTION, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).Return(mockDomainCreationProcessor, nil)
				mockDomainCreationProcessor.EXPECT().EnquireDomainActivationStatus(gomock.Any(), defaultEnquireDomainActivationStatusRequest).Return(defaultEnquireDomainActivationStatusFailureResponse, nil)
			},
			want: &rpActivityPb.EnquireDomainActivationStatusResponse{
				DomainActivationStatus:   rpActivityPb.EnquireDomainActivationStatusResponse_DOMAIN_ACTIVATION_FAILURE,
				ActionDetailedStatusInfo: defaultEnquireDomainActivationStatusFailureResponse.ActionDetailedStatusInfo,
			},
			wantErr: false,
		},
		{
			name: "Should return transient ERROR if domain activation is InProgress",
			req: &rpActivityPb.EnquireDomainActivationStatusRequest{
				RecurringPaymentId:       defaultRecurringPaymentId,
				RecurringPaymentActionId: defaultRecurringPaymentActionId,
			},
			setupMocks: func(mockRPDao *rpDaoMocks.MockRecurringPaymentDao, mockDomainCreationProcessorFactory *domainCreationProcessorMocks.MockDomainCreationProcessorFactory, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1) {
				mockRPDao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
				mockDomainCreationProcessorFactory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_STANDING_INSTRUCTION, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).Return(mockDomainCreationProcessor, nil)
				mockDomainCreationProcessor.EXPECT().EnquireDomainActivationStatus(gomock.Any(), defaultEnquireDomainActivationStatusRequest).Return(defaultEnquireDomainActivationStatusInProgressResponse, nil)
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockRPDao := rpDaoMocks.NewMockRecurringPaymentDao(ctr)
			mockDomainCreationProcessorFactory := domainCreationProcessorMocks.NewMockDomainCreationProcessorFactory(ctr)
			mockDomainCreationProcessor := domainCreationProcessorMocks.NewMockDomainCreationProcessorV1(ctr)

			p := &Processor{
				recurringPaymentDao:            mockRPDao,
				domainCreationProcessorFactory: mockDomainCreationProcessorFactory,
			}

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			tt.setupMocks(mockRPDao, mockDomainCreationProcessorFactory, mockDomainCreationProcessor)

			result := &rpActivityPb.EnquireDomainActivationStatusResponse{}
			got, err := env.ExecuteActivity(rpNs.EnquireDomainActivationStatus, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("EnquireDomainActivationStatus() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			if (err != nil) != tt.wantErr {
				t.Errorf("EnquireDomainActivationStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !proto.Equal(result, tt.want) {
				t.Errorf("EnquireDomainActivationStatus() got = %v, want %v", result, tt.want)
				return
			}
		})
	}

}
