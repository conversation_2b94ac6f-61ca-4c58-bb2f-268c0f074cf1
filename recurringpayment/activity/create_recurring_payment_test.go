package activity_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"testing"

	"github.com/golang/mock/gomock"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/pkg/epifierrors"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
)

func TestProcessor_CreateRecurringPayment(t *testing.T) {
	const (
		defaultRecurringPaymentId = "default_test_recurring_payment_id"
		defaultActorFrom          = "default_test_actor_from"
		defaultActorTo            = "default_test_actor_to"
		defaultPiFrom             = "default_test_pi_from"
		defaultPiTo               = "default_test_pi_to"
		defaultExternalId         = "default_test_external_id"
		defaultRemarks            = "default_test_remarks"
	)

	var (
		defaultAmount = &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        510,
		}
		defaultRecurrenceRule = &rpPb.RecurrenceRule{
			AllowedFrequency: rpPb.AllowedFrequency_WEEKLY,
			Day:              3,
		}
		defaultRecurringPayment = &rpPb.RecurringPayment{
			FromActorId:              defaultActorFrom,
			ToActorId:                defaultActorTo,
			Type:                     rpPb.RecurringPaymentType_STANDING_INSTRUCTION,
			PiFrom:                   defaultPiFrom,
			PiTo:                     defaultPiTo,
			Amount:                   defaultAmount,
			RecurrenceRule:           defaultRecurrenceRule,
			MaximumAllowedTxns:       20,
			PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
			PreferredPaymentProtocol: paymentPb.PaymentProtocol_NEFT,
			State:                    rpPb.RecurringPaymentState_ACTIVATED,
			AmountType:               rpPb.AmountType_MAXIMUM,
			ExternalId:               defaultExternalId,
			Remarks:                  defaultRemarks,
			ShareToPayee:             true,
		}
		defaultCreatedRecurringPayment = &rpPb.RecurringPayment{
			Id:                       defaultRecurringPaymentId,
			FromActorId:              defaultActorFrom,
			ToActorId:                defaultActorTo,
			Type:                     rpPb.RecurringPaymentType_STANDING_INSTRUCTION,
			PiFrom:                   defaultPiFrom,
			PiTo:                     defaultPiTo,
			Amount:                   defaultAmount,
			RecurrenceRule:           defaultRecurrenceRule,
			MaximumAllowedTxns:       20,
			PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
			PreferredPaymentProtocol: paymentPb.PaymentProtocol_NEFT,
			State:                    rpPb.RecurringPaymentState_ACTIVATED,
			AmountType:               rpPb.AmountType_MAXIMUM,
			ExternalId:               defaultExternalId,
			Remarks:                  defaultRemarks,
			ShareToPayee:             true,
		}
	)

	type mockRecurringPaymentDaoGetByExternalId struct {
		enable           bool
		externalId       string
		recurringPayment *rpPb.RecurringPayment
		err              error
	}
	type mockRecurringPaymentDaoCreate struct {
		enable                  bool
		recurringPayment        *rpPb.RecurringPayment
		createdRecurringPayment *rpPb.RecurringPayment
		err                     error
	}

	act, md, assertTest := newProcessorWithMocks(t)
	defer assertTest()
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(act)

	tests := []struct {
		name                                   string
		req                                    *rpActivityPb.CreateRecurringPaymentRequest
		mockRecurringPaymentDaoGetByExternalId mockRecurringPaymentDaoGetByExternalId
		mockRecurringPaymentDaoCreate          mockRecurringPaymentDaoCreate
		want                                   *rpActivityPb.CreateRecurringPaymentResponse
		wantErr                                bool
	}{
		{
			name: "Pass if recurring payment already exists",
			req: &rpActivityPb.CreateRecurringPaymentRequest{
				RecurringPayment: defaultRecurringPayment,
			},
			mockRecurringPaymentDaoGetByExternalId: mockRecurringPaymentDaoGetByExternalId{
				enable:           true,
				externalId:       defaultExternalId,
				recurringPayment: defaultCreatedRecurringPayment,
				err:              nil,
			},
			want: &rpActivityPb.CreateRecurringPaymentResponse{
				RecurringPayment: defaultCreatedRecurringPayment,
			},
			wantErr: false,
		},
		{
			name: "Error if unable to fetch recurring payment by external id",
			req: &rpActivityPb.CreateRecurringPaymentRequest{
				RecurringPayment: defaultRecurringPayment,
			},
			mockRecurringPaymentDaoGetByExternalId: mockRecurringPaymentDaoGetByExternalId{
				enable:           true,
				externalId:       defaultExternalId,
				recurringPayment: nil,
				err:              epifierrors.ErrPermanent,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Error if unable to create recurring payment",
			req: &rpActivityPb.CreateRecurringPaymentRequest{
				RecurringPayment: defaultRecurringPayment,
			},
			mockRecurringPaymentDaoGetByExternalId: mockRecurringPaymentDaoGetByExternalId{
				enable:           true,
				externalId:       defaultExternalId,
				recurringPayment: nil,
				err:              epifierrors.ErrRecordNotFound,
			},
			mockRecurringPaymentDaoCreate: mockRecurringPaymentDaoCreate{
				enable:                  true,
				recurringPayment:        defaultRecurringPayment,
				createdRecurringPayment: nil,
				err:                     epifierrors.ErrPermanent,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Success if recurring payment created successfully",
			req: &rpActivityPb.CreateRecurringPaymentRequest{
				RecurringPayment: defaultRecurringPayment,
			},
			mockRecurringPaymentDaoGetByExternalId: mockRecurringPaymentDaoGetByExternalId{
				enable:           true,
				externalId:       defaultExternalId,
				recurringPayment: nil,
				err:              epifierrors.ErrRecordNotFound,
			},
			mockRecurringPaymentDaoCreate: mockRecurringPaymentDaoCreate{
				enable:                  true,
				recurringPayment:        defaultRecurringPayment,
				createdRecurringPayment: defaultCreatedRecurringPayment,
				err:                     nil,
			},
			want: &rpActivityPb.CreateRecurringPaymentResponse{
				RecurringPayment: defaultCreatedRecurringPayment,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockRecurringPaymentDaoGetByExternalId.enable {
				md.recurringPaymentDao.EXPECT().GetByExternalId(gomock.Any(), tt.mockRecurringPaymentDaoGetByExternalId.externalId).
					Return(tt.mockRecurringPaymentDaoGetByExternalId.recurringPayment, tt.mockRecurringPaymentDaoGetByExternalId.err)
			}
			if tt.mockRecurringPaymentDaoCreate.enable {
				md.recurringPaymentDao.EXPECT().Create(gomock.Any(), tt.mockRecurringPaymentDaoCreate.recurringPayment, commontypes.Ownership_EPIFI_TECH).
					Return(tt.mockRecurringPaymentDaoCreate.createdRecurringPayment, tt.mockRecurringPaymentDaoCreate.err)
			}

			result := &rpActivityPb.CreateRecurringPaymentResponse{}
			got, err := env.ExecuteActivity(rpNs.CreateRecurringPayment, tt.req)

			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("CreateRecurringPayment() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			if (err != nil) != tt.wantErr {
				t.Errorf("CreateRecurringPayment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !proto.Equal(result, tt.want) {
				t.Errorf("CreateRecurringPayment() got = %v, want %v", result, tt.want)
				return
			}

			assertTest()

		})
	}
}
