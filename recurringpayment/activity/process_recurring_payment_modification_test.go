package activity_test

import (
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	domainPb "github.com/epifi/gamma/api/order/domain"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
)

func TestProcessor_ProcessRecurringPaymentModification(t *testing.T) {
	type mockRpClient struct {
		enable bool
		req    *domainPb.ProcessFulfilmentRequest
		res    *domainPb.ProcessFulfilmentResponse
		err    error
	}
	tests := []struct {
		name         string
		req          *activityPb.Request
		mockRpClient mockRpClient
		want         *activityPb.Response
		wantErr      bool
		assertErr    func(err error) bool
	}{
		{
			name: "recurring payment modification processed successfully",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: false},
				ClientReqId:   "client-req-id",
				Payload:       []byte{102, 97, 108, 99, 111, 110},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessFulfilmentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "client-req-id",
						IsLastAttempt:               false,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessFulfilmentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_SUCCESS,
					},
				},
				err: nil,
			},
			want:    &activityPb.Response{},
			wantErr: false,
		},
		{
			name: "failed to process recurring payment modify request due to connection issues",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: false},
				ClientReqId:   "client-req-id",
				Payload:       []byte{102, 97, 108, 99, 111, 110},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessFulfilmentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "client-req-id",
						IsLastAttempt:               false,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: nil,
				err: errors.New("error"),
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failure in processing recurring payment modify request due to transient error",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: false},
				ClientReqId:   "client-req-id",
				Payload:       []byte{102, 97, 108, 99, 111, 110},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessFulfilmentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "client-req-id",
						IsLastAttempt:               false,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessFulfilmentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_TRANSIENT_FAILURE,
					},
				},
				err: nil,
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failure in processing recurring payment modify request due to permanent error",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: false},
				ClientReqId:   "client-req-id",
				Payload:       []byte{102, 97, 108, 99, 111, 110},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessFulfilmentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "client-req-id",
						IsLastAttempt:               false,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessFulfilmentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_PERMANENT_FAILURE,
					},
				},
				err: nil,
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent error if last attempt and transient status in process recurring payment modify request rpc",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: true},
				ClientReqId:   "client-req-id",
				Payload:       []byte{102, 97, 108, 99, 111, 110},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessFulfilmentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "client-req-id",
						IsLastAttempt:               true,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessFulfilmentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_TRANSIENT_FAILURE,
					},
				},
				err: nil,
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent error if last attempt and no_op status in process recurring payment modify request rpc",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: true},
				ClientReqId:   "client-req-id",
				Payload:       []byte{102, 97, 108, 99, 111, 110},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessFulfilmentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "client-req-id",
						IsLastAttempt:               true,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessFulfilmentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_NO_OP,
					},
				},
				err: nil,
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "transient error if no_op status and attempt count is left for process recurring payment modify request rpc",
			req: &activityPb.Request{
				RequestHeader: &activityPb.RequestHeader{IsLastAttempt: false},
				ClientReqId:   "client-req-id",
				Payload:       []byte{102, 97, 108, 99, 111, 110},
			},
			mockRpClient: mockRpClient{
				enable: true,
				req: &domainPb.ProcessFulfilmentRequest{
					RequestHeader: &domainPb.DomainRequestHeader{
						ClientRequestId:             "client-req-id",
						IsLastAttempt:               false,
						ShouldForceProcessOrder:     false,
						ShouldOverrideTerminalState: false,
					},
					Payload: []byte{102, 97, 108, 99, 111, 110},
				},
				res: &domainPb.ProcessFulfilmentResponse{
					ResponseHeader: &domainPb.DomainResponseHeader{
						Status: domainPb.DomainProcessingStatus_NO_OP,
					},
				},
				err: nil,
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			act, md, assertTest := newProcessorWithMocks(t)
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(act)
			if tt.mockRpClient.enable {
				md.rpClient.
					EXPECT().
					ProcessRecurringPaymentModify(gomock.Any(), tt.mockRpClient.req).
					Return(tt.mockRpClient.res, tt.mockRpClient.err)
			}
			var result *activityPb.Response
			got, err := env.ExecuteActivity(rpNs.ProcessRecurringPaymentModification, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("ProcessRecurringPaymentModification() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("ProcessRecurringPaymentModification() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("ProcessRecurringPaymentModification() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("ProcessRecurringPaymentModification() got = %v, want %v", result, tt.want)
				return
			}
			assertTest()
		})
	}
}
