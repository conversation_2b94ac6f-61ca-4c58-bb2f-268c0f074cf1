package enach

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/durationpb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	"github.com/epifi/be-common/pkg/logger"
)

func (s *Service) GetActivationCoolOff(ctx context.Context, req *enachPb.GetActivationCoolOffRequest) (*enachPb.GetActivationCoolOffResponse, error) {
	res := &enachPb.GetActivationCoolOffResponse{}

	cooldown, ok := s.config.VendorToActivationCooldownMap[req.GetVendor().String()]
	if !ok {
		logger.Error(ctx, "Could not find a cool off config for the given vendor", zap.String(logger.VENDOR, req.GetVendor().String()))
		res.Status = rpcPb.StatusInvalidArgument()
		return res, nil
	}

	res.CoolOffDuration = durationpb.New(cooldown)
	res.Status = rpcPb.StatusOk()
	return res, nil
}
