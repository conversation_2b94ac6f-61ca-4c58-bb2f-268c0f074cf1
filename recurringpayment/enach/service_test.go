package enach

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	moneyPb "github.com/epifi/be-common/pkg/money"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	mockRecurPaymentClient "github.com/epifi/gamma/api/recurringpayment/mocks"
	daoMocks "github.com/epifi/gamma/recurringpayment/enach/dao/mocks"
	"github.com/epifi/gamma/recurringpayment/enach/enachvendor"
	vendorMocks "github.com/epifi/gamma/recurringpayment/enach/enachvendor/mocks"
)

func TestService_InitiateMandateCreation(t *testing.T) {
	defaultRecurringPaymentId := "recurring-payment-id"
	type args struct {
		ctx context.Context
		req *enachPb.InitiateMandateCreationRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockVendorProcessorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor, mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao)
		want       *enachPb.InitiateMandateCreationResponse
		wantErr    bool
	}{
		{
			name: "should return ISE rpc status when dao call to fetch mandate db fails",
			args: args{
				ctx: context.Background(),
				req: &enachPb.InitiateMandateCreationRequest{
					ClientRequestId:      "client-req-id-1",
					RecurringPaymentId:   defaultRecurringPaymentId,
					RegistrationAuthMode: enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
					Vendor:               commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
			setupMocks: func(mockVendorProcessorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor, mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), defaultRecurringPaymentId, true).Return(nil, errors.New("error while fetching mandate"))
			},
			want:    &enachPb.InitiateMandateCreationResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "should return status ok (mandate already exists)",
			args: args{
				ctx: context.Background(),
				req: &enachPb.InitiateMandateCreationRequest{
					ClientRequestId:      "client-req-id-1",
					RecurringPaymentId:   defaultRecurringPaymentId,
					RegistrationAuthMode: enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
					Vendor:               commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
			setupMocks: func(mockVendorProcessorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor, mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), defaultRecurringPaymentId, true).Return(&enachPb.EnachMandate{}, nil)
			},
			want:    &enachPb.InitiateMandateCreationResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
		{
			name: "should return ISE rpc status when no vendor processor exists for the vendor in the request",
			args: args{
				ctx: context.Background(),
				req: &enachPb.InitiateMandateCreationRequest{
					ClientRequestId:      "client-req-id-1",
					RecurringPaymentId:   defaultRecurringPaymentId,
					RegistrationAuthMode: enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
					Vendor:               commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
			setupMocks: func(mockVendorProcessorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor, mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), defaultRecurringPaymentId, true).Return(nil, epifierrors.ErrRecordNotFound)
				mockVendorProcessorFactory.EXPECT().GetVendorProcessor(context.Background(), commonvgpb.Vendor_FEDERAL_BANK).Return(nil)
			},
			want:    &enachPb.InitiateMandateCreationResponse{Status: rpc.StatusFailedPreconditionWithDebugMsg("unsupported vendor for enach")},
			wantErr: false,
		},
		{
			name: "should return ISE rpc status code when dao call to create mandate entry in db fails",
			args: args{
				ctx: context.Background(),
				req: &enachPb.InitiateMandateCreationRequest{
					ClientRequestId:      "client-req-id-1",
					RecurringPaymentId:   defaultRecurringPaymentId,
					RegistrationAuthMode: enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
					Vendor:               commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
			setupMocks: func(mockVendorProcessorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor, mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), defaultRecurringPaymentId, true).Return(nil, epifierrors.ErrRecordNotFound)
				mockVendorProcessorFactory.EXPECT().GetVendorProcessor(context.Background(), commonvgpb.Vendor_FEDERAL_BANK).Return(mockVendorProcessor)
				mockVendorProcessor.EXPECT().GenerateMandateCreationRequestId(context.Background()).Return("vendor-request-id-1")

				mockMandateDao.EXPECT().Create(gomock.Any(), &enachPb.EnachMandate{
					RecurringPaymentId:     defaultRecurringPaymentId,
					RegistrationAuthMode:   enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
					RegistrationProvenance: enachEnumsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
					Vendor:                 commonvgpb.Vendor_FEDERAL_BANK,
				}, commontypes.Ownership_EPIFI_TECH).Return(nil, errors.New("error creating mandate entry in db"))
			},
			want:    &enachPb.InitiateMandateCreationResponse{Status: rpc.StatusInternalWithDebugMsg("error creating mandate and mandate action entry in a db txn")},
			wantErr: false,
		},
		{
			name: "should return ISE rpc status code when dao call to create mandate action entry in db fails",
			args: args{
				ctx: context.Background(),
				req: &enachPb.InitiateMandateCreationRequest{
					ClientRequestId:      "client-req-id-1",
					RecurringPaymentId:   defaultRecurringPaymentId,
					RegistrationAuthMode: enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
					Vendor:               commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
			setupMocks: func(mockVendorProcessorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor, mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), defaultRecurringPaymentId, true).Return(nil, epifierrors.ErrRecordNotFound)
				mockVendorProcessorFactory.EXPECT().GetVendorProcessor(context.Background(), commonvgpb.Vendor_FEDERAL_BANK).Return(mockVendorProcessor)
				mockVendorProcessor.EXPECT().GenerateMandateCreationRequestId(context.Background()).Return("vendor-request-id-1")

				mockMandateDao.EXPECT().Create(gomock.Any(), &enachPb.EnachMandate{
					RecurringPaymentId:     defaultRecurringPaymentId,
					RegistrationAuthMode:   enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
					RegistrationProvenance: enachEnumsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
					Vendor:                 commonvgpb.Vendor_FEDERAL_BANK,
				}, commontypes.Ownership_EPIFI_TECH).Return(&enachPb.EnachMandate{Id: "enach-mandate-id-1"}, nil)

				mockActionDao.EXPECT().Create(gomock.Any(), &enachPb.EnachMandateAction{
					ClientRequestId: "client-req-id-1",
					EnachMandateId:  "enach-mandate-id-1",
					ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE,
					ActionStatus:    enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS,
					ActionSubStatus: enachEnumsPb.EnachActionSubStatus_ACTION_SUB_STATUS_AWAITING_AUTHORIZATION,
					VendorRequestId: "vendor-request-id-1",
				}).Return(nil, errors.New("error creating mandate action entry in db"))
			},
			want:    &enachPb.InitiateMandateCreationResponse{Status: rpc.StatusInternalWithDebugMsg("error creating mandate and mandate action entry in a db txn")},
			wantErr: false,
		},
		{
			name: "should return OK rpc status code",
			args: args{
				ctx: context.Background(),
				req: &enachPb.InitiateMandateCreationRequest{
					ClientRequestId:      "client-req-id-1",
					RecurringPaymentId:   defaultRecurringPaymentId,
					RegistrationAuthMode: enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
					Vendor:               commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
			setupMocks: func(mockVendorProcessorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor, mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), defaultRecurringPaymentId, true).Return(nil, epifierrors.ErrRecordNotFound)
				mockVendorProcessorFactory.EXPECT().GetVendorProcessor(context.Background(), commonvgpb.Vendor_FEDERAL_BANK).Return(mockVendorProcessor)
				mockVendorProcessor.EXPECT().GenerateMandateCreationRequestId(context.Background()).Return("vendor-request-id-1")

				mockMandateDao.EXPECT().Create(gomock.Any(), &enachPb.EnachMandate{
					RecurringPaymentId:     defaultRecurringPaymentId,
					RegistrationAuthMode:   enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
					RegistrationProvenance: enachEnumsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
					Vendor:                 commonvgpb.Vendor_FEDERAL_BANK,
				}, commontypes.Ownership_EPIFI_TECH).Return(&enachPb.EnachMandate{Id: "enach-mandate-id-1"}, nil)

				mockActionDao.EXPECT().Create(gomock.Any(), &enachPb.EnachMandateAction{
					ClientRequestId: "client-req-id-1",
					EnachMandateId:  "enach-mandate-id-1",
					ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE,
					ActionStatus:    enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS,
					ActionSubStatus: enachEnumsPb.EnachActionSubStatus_ACTION_SUB_STATUS_AWAITING_AUTHORIZATION,
					VendorRequestId: "vendor-request-id-1",
				}).Return(&enachPb.EnachMandateAction{Id: "enach-mandate-action-1"}, nil)
			},
			want:    &enachPb.InitiateMandateCreationResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockVendorProcessorFactory := vendorMocks.NewMockIFactory(ctr)
			mockVendorProcessor := vendorMocks.NewMockIVendorProcessor(ctr)
			mockMandateDao := daoMocks.NewMockEnachMandateDao(ctr)
			mockMandateActionDao := daoMocks.NewMockEnachMandateActionDao(ctr)
			mockTxnExecutor := storagev2.NewGormTxnExecutor(db)

			tt.setupMocks(mockVendorProcessorFactory, mockVendorProcessor, mockMandateDao, mockMandateActionDao)

			s := &Service{
				mandateDao:       mockMandateDao,
				mandateActionDao: mockMandateActionDao,
				vendorFactory:    mockVendorProcessorFactory,
				txnExecutor:      mockTxnExecutor,
			}
			got, err := s.InitiateMandateCreation(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("InitiateMandateCreation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("InitiateMandateCreation() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_AuthorizeMandateCreation(t *testing.T) {
	mandate1 := &enachPb.EnachMandate{
		Id:                     "mandate-id-1",
		RecurringPaymentId:     "recurring-payment-id-1",
		Umrn:                   "UMRN-1",
		RegistrationAuthMode:   enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
		RegistrationProvenance: enachEnumsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
		Vendor:                 commonvgpb.Vendor_FEDERAL_BANK,
	}
	mandate2 := &enachPb.EnachMandate{
		Id:                     "mandate-id-2",
		RecurringPaymentId:     "recurring-payment-id-2",
		Umrn:                   "UMRN-2",
		RegistrationAuthMode:   enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
		RegistrationProvenance: enachEnumsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
		Vendor:                 commonvgpb.Vendor_FEDERAL_BANK,
	}
	mandate3 := &enachPb.EnachMandate{
		Id:                     "mandate-id-3",
		RecurringPaymentId:     "recurring-payment-id-3",
		Umrn:                   "UMRN-3",
		RegistrationAuthMode:   enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
		RegistrationProvenance: enachEnumsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
		Vendor:                 commonvgpb.Vendor_FEDERAL_BANK,
	}
	awaitingAuthMandateAction := &enachPb.EnachMandateAction{
		Id:              "enach-mandate-action-1",
		ClientRequestId: "client-request-id-1",
		EnachMandateId:  mandate1.GetId(),
		ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE,
		ActionStatus:    enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS,
		ActionSubStatus: enachEnumsPb.EnachActionSubStatus_ACTION_SUB_STATUS_AWAITING_AUTHORIZATION,
		VendorRequestId: "vendor-request-id-1",
	}

	inProgressNotAwaitingAuthMandateAction := &enachPb.EnachMandateAction{
		Id:              "enach-mandate-action-2",
		ClientRequestId: "client-request-id-2",
		EnachMandateId:  mandate2.GetId(),
		ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE,
		ActionStatus:    enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS,
		ActionSubStatus: enachEnumsPb.EnachActionSubStatus_ENACH_ACTION_SUB_STATUS_UNSPECIFIED,
		VendorRequestId: "vendor-request-id-2",
	}

	successfulMandateAction := &enachPb.EnachMandateAction{
		Id:              "enach-mandate-action-3",
		ClientRequestId: "client-request-id-3",
		EnachMandateId:  mandate3.GetId(),
		ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE,
		ActionStatus:    enachEnumsPb.EnachActionStatus_ACTION_STATUS_SUCCESS,
		ActionSubStatus: enachEnumsPb.EnachActionSubStatus_ENACH_ACTION_SUB_STATUS_UNSPECIFIED,
		VendorRequestId: "vendor-request-id-3",
		ActionMetadata: &enachPb.ActionMetadata{
			ActionTypeSpecificMetadata: &enachPb.ActionMetadata_CreateMetadata{
				CreateMetadata: &enachPb.ActionMetadata_CreateActionMetadata{
					Umrn:                       mandate3.GetUmrn(),
					NpciRefId:                  "npci-ref-id-3",
					DestBankReferenceNumber:    "dest-bank-reference-number-3",
					MerchantReferenceMessageId: "merchant-reference-message-id-3",
				},
			},
		},
	}

	type args struct {
		ctx context.Context
		req *enachPb.AuthorizeMandateCreationRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao)
		want       *enachPb.AuthorizeMandateCreationResponse
		wantErr    bool
	}{
		{
			name: "should return ISE rpc status when dao call to fetch mandate fails",
			args: args{
				ctx: context.Background(),
				req: &enachPb.AuthorizeMandateCreationRequest{
					RecurringPaymentId:         "recurring-payment-id-1",
					Umrn:                       "UMRN-1",
					NpciRefId:                  "npci-ref-id-1",
					DestBankReferenceNumber:    "dest-bank-reference-number-1",
					MerchantReferenceMessageId: "merchant-reference-message-id-1",
				},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), "recurring-payment-id-1", true).Return(nil, errors.New("error fetching mandate entry from db"))
			},
			want:    &enachPb.AuthorizeMandateCreationResponse{Status: rpc.StatusInternalWithDebugMsg("error fetching mandate entry from db using recurring payment id")},
			wantErr: false,
		},
		{
			name: "should return ISE rpc status when dao call to fetch mandate action fails",
			args: args{
				ctx: context.Background(),
				req: &enachPb.AuthorizeMandateCreationRequest{
					RecurringPaymentId:         "recurring-payment-id-1",
					Umrn:                       "UMRN-1",
					NpciRefId:                  "npci-ref-id-1",
					DestBankReferenceNumber:    "dest-bank-reference-number-1",
					MerchantReferenceMessageId: "merchant-reference-message-id-1",
				},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				// using cloned objects to prevent any update on mandate1 object as this is used by other tests as well
				clonedMandate := cloneEnachMandate(mandate1)
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), "recurring-payment-id-1", true).Return(clonedMandate, nil)
				mockActionDao.EXPECT().GetByActionTypeAndEnachMandateId(context.Background(), enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE, clonedMandate.GetId()).Return(nil, errors.New("error fetching mandate action entry from db"))
			},
			want:    &enachPb.AuthorizeMandateCreationResponse{Status: rpc.StatusInternalWithDebugMsg("error fetching create mandate action entry from db using enach mandate id")},
			wantErr: false,
		},
		{
			name: "should return FailedPrecondition rpc status when mandate action is not is AWAITING_AUTHORIZATION sub-status",
			args: args{
				ctx: context.Background(),
				req: &enachPb.AuthorizeMandateCreationRequest{
					RecurringPaymentId:         "recurring-payment-id-1",
					Umrn:                       "UMRN-1",
					NpciRefId:                  "npci-ref-id-1",
					DestBankReferenceNumber:    "dest-bank-reference-number-1",
					MerchantReferenceMessageId: "merchant-reference-message-id-1",
				},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				// using cloned objects to prevent any update on mandate1 object as this is used by other tests as well
				clonedMandate := cloneEnachMandate(mandate1)
				clonedMandateAction := cloneEnachMandateAction(inProgressNotAwaitingAuthMandateAction)

				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), "recurring-payment-id-1", true).Return(clonedMandate, nil)
				mockActionDao.EXPECT().GetByActionTypeAndEnachMandateId(context.Background(), enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE, clonedMandate.GetId()).Return([]*enachPb.EnachMandateAction{clonedMandateAction}, nil)
			},
			want:    &enachPb.AuthorizeMandateCreationResponse{Status: rpc.StatusFailedPreconditionWithDebugMsg("authorization is not allowed")},
			wantErr: false,
		},
		{
			name: "should return OK rpc status when mandate action is already in SUCCESS state",
			args: args{
				ctx: context.Background(),
				req: &enachPb.AuthorizeMandateCreationRequest{
					RecurringPaymentId:         "recurring-payment-id-1",
					Umrn:                       "UMRN-1",
					NpciRefId:                  "npci-ref-id-1",
					DestBankReferenceNumber:    "dest-bank-reference-number-1",
					MerchantReferenceMessageId: "merchant-reference-message-id-1",
				},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				// using cloned objects to prevent any update on mandate1 object as this is used by other tests as well
				clonedMandate := cloneEnachMandate(mandate1)
				clonedMandateAction := cloneEnachMandateAction(successfulMandateAction)

				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), "recurring-payment-id-1", true).Return(clonedMandate, nil)
				mockActionDao.EXPECT().GetByActionTypeAndEnachMandateId(context.Background(), enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE, clonedMandate.GetId()).Return([]*enachPb.EnachMandateAction{clonedMandateAction}, nil)
			},
			want:    &enachPb.AuthorizeMandateCreationResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
		{
			name: "should return ISE rpc status code when dao call to update mandate fails",
			args: args{
				ctx: context.Background(),
				req: &enachPb.AuthorizeMandateCreationRequest{
					RecurringPaymentId:         "recurring-payment-id-1",
					Umrn:                       "UMRN-1",
					NpciRefId:                  "npci-ref-id-1",
					DestBankReferenceNumber:    "dest-bank-reference-number-1",
					MerchantReferenceMessageId: "merchant-reference-message-id-1",
				},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				// using cloned objects to prevent any update on mandate1 object as this is used by other tests as well
				clonedMandate := cloneEnachMandate(mandate1)
				clonedMandateAction := cloneEnachMandateAction(awaitingAuthMandateAction)

				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), "recurring-payment-id-1", true).Return(clonedMandate, nil)
				mockActionDao.EXPECT().GetByActionTypeAndEnachMandateId(context.Background(), enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE, clonedMandate.GetId()).Return([]*enachPb.EnachMandateAction{clonedMandateAction}, nil)

				clonedMandate2 := cloneEnachMandate(mandate1)
				// nolint: goconst
				clonedMandate2.Umrn = "UMRN-1"
				mockMandateDao.EXPECT().Update(gomock.Any(), []enachEnumsPb.EnachMandateFieldMask{enachEnumsPb.EnachMandateFieldMask_ENACH_MANDATE_FIELD_MASK_UMRN}, clonedMandate2).Return(errors.New("error updating mandate entry in db"))
			},
			want:    &enachPb.AuthorizeMandateCreationResponse{Status: rpc.StatusInternalWithDebugMsg("error updating mandate and mandate action entry in a db txn")},
			wantErr: false,
		},
		{
			name: "should return ISE rpc status code when dao call to update mandate action fails",
			args: args{
				ctx: context.Background(),
				req: &enachPb.AuthorizeMandateCreationRequest{
					RecurringPaymentId:         "recurring-payment-id-1",
					Umrn:                       "UMRN-1",
					NpciRefId:                  "npci-ref-id-1",
					DestBankReferenceNumber:    "dest-bank-reference-number-1",
					MerchantReferenceMessageId: "merchant-reference-message-id-1",
				},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				// using cloned objects to prevent any update on mandate1 object as this is used by other tests as well
				clonedMandate := cloneEnachMandate(mandate1)
				clonedMandateAction := cloneEnachMandateAction(awaitingAuthMandateAction)

				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), "recurring-payment-id-1", true).Return(clonedMandate, nil)
				mockActionDao.EXPECT().GetByActionTypeAndEnachMandateId(context.Background(), enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE, clonedMandate.GetId()).Return([]*enachPb.EnachMandateAction{clonedMandateAction}, nil)

				clonedMandate2 := cloneEnachMandate(mandate1)
				// nolint: goconst
				clonedMandate2.Umrn = "UMRN-1"
				mockMandateDao.EXPECT().Update(gomock.Any(), []enachEnumsPb.EnachMandateFieldMask{enachEnumsPb.EnachMandateFieldMask_ENACH_MANDATE_FIELD_MASK_UMRN}, clonedMandate2).Return(nil)

				clonedMandateAction2 := cloneEnachMandateAction(awaitingAuthMandateAction)
				clonedMandateAction2.ActionStatus = enachEnumsPb.EnachActionStatus_ACTION_STATUS_SUCCESS
				clonedMandateAction2.ActionSubStatus = enachEnumsPb.EnachActionSubStatus_ENACH_ACTION_SUB_STATUS_UNSPECIFIED
				clonedMandateAction2.ActionMetadata = &enachPb.ActionMetadata{
					ActionTypeSpecificMetadata: &enachPb.ActionMetadata_CreateMetadata{
						CreateMetadata: &enachPb.ActionMetadata_CreateActionMetadata{
							Umrn:                       "UMRN-1",
							NpciRefId:                  "npci-ref-id-1",
							DestBankReferenceNumber:    "dest-bank-reference-number-1",
							MerchantReferenceMessageId: "merchant-reference-message-id-1",
						}},
				}
				mockActionDao.EXPECT().UpdateWithStatusCheck(gomock.Any(), []enachEnumsPb.EnachMandateActionFieldMask{enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_STATUS, enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_SUB_STATUS, enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_METADATA}, clonedMandateAction2, enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS, enachEnumsPb.EnachActionSubStatus_ACTION_SUB_STATUS_AWAITING_AUTHORIZATION).Return(errors.New("error updating mandate action entry in db"))
			},
			want:    &enachPb.AuthorizeMandateCreationResponse{Status: rpc.StatusInternalWithDebugMsg("error updating mandate and mandate action entry in a db txn")},
			wantErr: false,
		},
		{
			name: "should return OK rpc status code when authorization is successfully completed",
			args: args{
				ctx: context.Background(),
				req: &enachPb.AuthorizeMandateCreationRequest{
					RecurringPaymentId:         "recurring-payment-id-1",
					Umrn:                       "UMRN-1",
					NpciRefId:                  "npci-ref-id-1",
					DestBankReferenceNumber:    "dest-bank-reference-number-1",
					MerchantReferenceMessageId: "merchant-reference-message-id-1",
				},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				// using cloned objects to prevent any update on mandate1 object as this is used by other tests as well
				clonedMandate := cloneEnachMandate(mandate1)
				clonedMandateAction := cloneEnachMandateAction(awaitingAuthMandateAction)

				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), "recurring-payment-id-1", true).Return(clonedMandate, nil)
				mockActionDao.EXPECT().GetByActionTypeAndEnachMandateId(context.Background(), enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE, clonedMandate.GetId()).Return([]*enachPb.EnachMandateAction{clonedMandateAction}, nil)

				// nolint: goconst
				clonedMandate2 := cloneEnachMandate(mandate1)
				clonedMandate2.Umrn = "UMRN-1"
				mockMandateDao.EXPECT().Update(gomock.Any(), []enachEnumsPb.EnachMandateFieldMask{enachEnumsPb.EnachMandateFieldMask_ENACH_MANDATE_FIELD_MASK_UMRN}, clonedMandate2).Return(nil)

				clonedMandateAction2 := cloneEnachMandateAction(awaitingAuthMandateAction)
				clonedMandateAction2.ActionStatus = enachEnumsPb.EnachActionStatus_ACTION_STATUS_SUCCESS
				clonedMandateAction2.ActionSubStatus = enachEnumsPb.EnachActionSubStatus_ENACH_ACTION_SUB_STATUS_UNSPECIFIED
				clonedMandateAction2.ActionMetadata = &enachPb.ActionMetadata{
					ActionTypeSpecificMetadata: &enachPb.ActionMetadata_CreateMetadata{
						CreateMetadata: &enachPb.ActionMetadata_CreateActionMetadata{
							Umrn:                       "UMRN-1",
							NpciRefId:                  "npci-ref-id-1",
							DestBankReferenceNumber:    "dest-bank-reference-number-1",
							MerchantReferenceMessageId: "merchant-reference-message-id-1",
						}},
				}
				mockActionDao.EXPECT().UpdateWithStatusCheck(gomock.Any(), []enachEnumsPb.EnachMandateActionFieldMask{enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_STATUS, enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_SUB_STATUS, enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_METADATA}, clonedMandateAction2, enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS, enachEnumsPb.EnachActionSubStatus_ACTION_SUB_STATUS_AWAITING_AUTHORIZATION).Return(nil)
			},
			want:    &enachPb.AuthorizeMandateCreationResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockMandateDao := daoMocks.NewMockEnachMandateDao(ctr)
			mockMandateActionDao := daoMocks.NewMockEnachMandateActionDao(ctr)
			mockTxnExecutor := storagev2.NewGormTxnExecutor(db)

			tt.setupMocks(mockMandateDao, mockMandateActionDao)

			s := &Service{
				mandateDao:       mockMandateDao,
				mandateActionDao: mockMandateActionDao,
				txnExecutor:      mockTxnExecutor,
			}
			got, err := s.AuthorizeMandateCreation(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AuthorizeMandateCreation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("AuthorizeMandateCreation() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_CreateOffAppEnachMandate(t *testing.T) {
	// todo (harleen) : add unit tests
}

func TestService_GetAuthorizationPayloadForMandateCreation(t *testing.T) {
	recurringPayment := &rpPb.RecurringPayment{
		Id: "recurring-payment-id-1",
	}
	enachMandate1 := &enachPb.EnachMandate{
		Id:                     "mandate-id-1",
		RecurringPaymentId:     "recurring-payment-id-1",
		Umrn:                   "umrn-1",
		RegistrationAuthMode:   enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
		RegistrationProvenance: enachEnumsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
		Vendor:                 commonvgpb.Vendor_FEDERAL_BANK,
	}
	enachMandateAction1 := &enachPb.EnachMandateAction{
		Id:             "mandate-action-1",
		EnachMandateId: "mandate-id-1",
		ActionType:     enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE,
	}

	type args struct {
		ctx context.Context
		req *enachPb.GetAuthorizationPayloadForMandateCreationRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockRecurringPayment *mockRecurPaymentClient.MockRecurringPaymentServiceClient, mockMandateDao *daoMocks.MockEnachMandateDao, mockMandateActionDao *daoMocks.MockEnachMandateActionDao, mockVendorProcessorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor)
		want       *enachPb.GetAuthorizationPayloadForMandateCreationResponse
		wantErr    bool
	}{
		{
			name: "should return ISE rpc status when rpc call to fetch recurring payment fails",
			args: args{
				ctx: context.Background(),
				req: &enachPb.GetAuthorizationPayloadForMandateCreationRequest{
					RecurringPaymentId: "recurring-payment-id-1",
				},
			},
			setupMocks: func(mockRecurringPayment *mockRecurPaymentClient.MockRecurringPaymentServiceClient, mockMandateDao *daoMocks.MockEnachMandateDao, mockMandateActionDao *daoMocks.MockEnachMandateActionDao, mockVendorProcessorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor) {
				mockRecurringPayment.EXPECT().GetRecurringPaymentById(context.Background(), &rpPb.GetRecurringPaymentByIdRequest{
					Id: "recurring-payment-id-1",
				}).Return(&rpPb.GetRecurringPaymentByIdResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: &enachPb.GetAuthorizationPayloadForMandateCreationResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching recurring payment for given id."),
			},
			wantErr: false,
		},
		{
			name: "should return ISE rpc status when dao call to fetch mandate fails",
			args: args{
				ctx: context.Background(),
				req: &enachPb.GetAuthorizationPayloadForMandateCreationRequest{
					RecurringPaymentId: "recurring-payment-id-1",
				},
			},
			setupMocks: func(mockRecurringPayment *mockRecurPaymentClient.MockRecurringPaymentServiceClient, mockMandateDao *daoMocks.MockEnachMandateDao, mockMandateActionDao *daoMocks.MockEnachMandateActionDao, mockVendorProcessorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor) {
				mockRecurringPayment.EXPECT().GetRecurringPaymentById(context.Background(), &rpPb.GetRecurringPaymentByIdRequest{
					Id: "recurring-payment-id-1",
				}).Return(&rpPb.GetRecurringPaymentByIdResponse{
					Status:           rpc.StatusOk(),
					RecurringPayment: recurringPayment,
				}, nil)
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), "recurring-payment-id-1", true).Return(nil, errors.New("error fetching mandate entry from db"))
			},
			want: &enachPb.GetAuthorizationPayloadForMandateCreationResponse{
				Status: rpc.StatusInternalWithDebugMsg("error fetching mandate entry from db using recurring payment id"),
			},
			wantErr: false,
		},
		{
			name: "should return ISE rpc status when dao call to fetch mandate action fail",
			args: args{
				ctx: context.Background(),
				req: &enachPb.GetAuthorizationPayloadForMandateCreationRequest{
					RecurringPaymentId: "recurring-payment-id-1",
				},
			},
			setupMocks: func(mockRecurringPayment *mockRecurPaymentClient.MockRecurringPaymentServiceClient, mockMandateDao *daoMocks.MockEnachMandateDao, mockMandateActionDao *daoMocks.MockEnachMandateActionDao, mockVendorProcessorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor) {
				mockRecurringPayment.EXPECT().GetRecurringPaymentById(context.Background(), &rpPb.GetRecurringPaymentByIdRequest{
					Id: "recurring-payment-id-1",
				}).Return(&rpPb.GetRecurringPaymentByIdResponse{
					Status:           rpc.StatusOk(),
					RecurringPayment: recurringPayment,
				}, nil)
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), "recurring-payment-id-1", true).Return(enachMandate1, nil)
				mockMandateActionDao.EXPECT().GetByActionTypeAndEnachMandateId(context.Background(), enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE, enachMandate1.GetId()).Return(nil, errors.New("error fetching enach mandate action from db"))
			},
			want: &enachPb.GetAuthorizationPayloadForMandateCreationResponse{
				Status: rpc.StatusInternalWithDebugMsg("error fetching create mandate action entry from db using mandate_id"),
			},
			wantErr: false,
		},
		{
			name: "should return ISE rpc status when auth payload generation fails",
			args: args{
				ctx: context.Background(),
				req: &enachPb.GetAuthorizationPayloadForMandateCreationRequest{
					RecurringPaymentId: "recurring-payment-id-1",
				},
			},
			setupMocks: func(mockRecurringPayment *mockRecurPaymentClient.MockRecurringPaymentServiceClient, mockMandateDao *daoMocks.MockEnachMandateDao, mockMandateActionDao *daoMocks.MockEnachMandateActionDao, mockVendorProcessorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor) {
				mockRecurringPayment.EXPECT().GetRecurringPaymentById(context.Background(), &rpPb.GetRecurringPaymentByIdRequest{
					Id: "recurring-payment-id-1",
				}).Return(&rpPb.GetRecurringPaymentByIdResponse{
					Status:           rpc.StatusOk(),
					RecurringPayment: recurringPayment,
				}, nil)
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), "recurring-payment-id-1", true).Return(enachMandate1, nil)
				mockMandateActionDao.EXPECT().GetByActionTypeAndEnachMandateId(context.Background(), enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE, enachMandate1.GetId()).Return([]*enachPb.EnachMandateAction{enachMandateAction1}, nil)
				mockVendorProcessorFactory.EXPECT().GetVendorProcessor(context.Background(), enachMandate1.GetVendor()).Return(mockVendorProcessor)
				mockVendorProcessor.EXPECT().GenerateAuthPayloadForMandateCreation(context.Background(), &enachvendor.AuthPayloadForMandateCreationRequest{
					RecurringPayment: recurringPayment,
					Mandate:          enachMandate1,
					MandateAction:    enachMandateAction1,
				}).Return(nil, errors.New("error generating auth payload"))
			},
			want: &enachPb.GetAuthorizationPayloadForMandateCreationResponse{
				Status: rpc.StatusInternalWithDebugMsg("error generating mandate creation auth payload"),
			},
			wantErr: false,
		},
		{
			name: "should return Ok rpc status code with auth payload",
			args: args{
				ctx: context.Background(),
				req: &enachPb.GetAuthorizationPayloadForMandateCreationRequest{
					RecurringPaymentId: "recurring-payment-id-1",
				},
			},
			setupMocks: func(mockRecurringPayment *mockRecurPaymentClient.MockRecurringPaymentServiceClient, mockMandateDao *daoMocks.MockEnachMandateDao, mockMandateActionDao *daoMocks.MockEnachMandateActionDao, mockVendorProcessorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor) {
				mockRecurringPayment.EXPECT().GetRecurringPaymentById(context.Background(), &rpPb.GetRecurringPaymentByIdRequest{
					Id: "recurring-payment-id-1",
				}).Return(&rpPb.GetRecurringPaymentByIdResponse{
					Status:           rpc.StatusOk(),
					RecurringPayment: recurringPayment,
				}, nil)
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), "recurring-payment-id-1", true).Return(enachMandate1, nil)
				mockMandateActionDao.EXPECT().GetByActionTypeAndEnachMandateId(context.Background(), enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE, enachMandate1.GetId()).Return([]*enachPb.EnachMandateAction{enachMandateAction1}, nil)
				mockVendorProcessorFactory.EXPECT().GetVendorProcessor(context.Background(), enachMandate1.GetVendor()).Return(mockVendorProcessor)
				mockVendorProcessor.EXPECT().GenerateAuthPayloadForMandateCreation(context.Background(), &enachvendor.AuthPayloadForMandateCreationRequest{
					RecurringPayment: recurringPayment,
					Mandate:          enachMandate1,
					MandateAction:    enachMandateAction1,
				}).Return(&enachvendor.AuthPayloadForMandateCreationResponse{
					AuthPayload: &enachPb.MandateCreationAuthorizationPayload{
						VendorSpecificAuthPayload: &enachPb.MandateCreationAuthorizationPayload_FederalBankPayload_{
							FederalBankPayload: &enachPb.MandateCreationAuthorizationPayload_FederalBankPayload{
								RedirectionUrl: "redirection-url-1",
								FormData: map[string]string{
									"appId":       "********",
									"responseUrl": "response-url-1",
								},
								ExitUrl: "exit-url-1",
							}}},
				}, nil)
			},
			want: &enachPb.GetAuthorizationPayloadForMandateCreationResponse{
				Status: rpc.StatusOk(),
				AuthPayload: &enachPb.MandateCreationAuthorizationPayload{
					VendorSpecificAuthPayload: &enachPb.MandateCreationAuthorizationPayload_FederalBankPayload_{
						FederalBankPayload: &enachPb.MandateCreationAuthorizationPayload_FederalBankPayload{
							RedirectionUrl: "redirection-url-1",
							FormData: map[string]string{
								"appId":       "********",
								"responseUrl": "response-url-1",
							},
							ExitUrl: "exit-url-1",
						}},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockRecurringPaymentClient := mockRecurPaymentClient.NewMockRecurringPaymentServiceClient(ctr)
			mockMandateDao := daoMocks.NewMockEnachMandateDao(ctr)
			mockMandateActionDao := daoMocks.NewMockEnachMandateActionDao(ctr)
			mockVendorProcessorFactory := vendorMocks.NewMockIFactory(ctr)
			mockVendorProcessor := vendorMocks.NewMockIVendorProcessor(ctr)

			tt.setupMocks(mockRecurringPaymentClient, mockMandateDao, mockMandateActionDao, mockVendorProcessorFactory, mockVendorProcessor)

			s := &Service{
				mandateDao:             mockMandateDao,
				mandateActionDao:       mockMandateActionDao,
				vendorFactory:          mockVendorProcessorFactory,
				recurringPaymentClient: mockRecurringPaymentClient,
			}
			got, err := s.GetAuthorizationPayloadForMandateCreation(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAuthorizationPayloadForMandateCreation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetAuthorizationPayloadForMandateCreation() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetEnachMandate(t *testing.T) {
	enachMandate1 := &enachPb.EnachMandate{
		Id:                     "mandate-id-1",
		RecurringPaymentId:     "recurring-payment-id-1",
		Umrn:                   "umrn-1",
		RegistrationAuthMode:   enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
		RegistrationProvenance: enachEnumsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
		Vendor:                 commonvgpb.Vendor_FEDERAL_BANK,
	}

	type args struct {
		ctx context.Context
		req *enachPb.GetEnachMandateRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao)
		want       *enachPb.GetEnachMandateResponse
		wantErr    bool
	}{
		{
			name: "should return RecordNotFound rpc status code when no mandate exists with given umrn",
			args: args{
				ctx: context.Background(),
				req: &enachPb.GetEnachMandateRequest{
					Identifier: &enachPb.GetEnachMandateRequest_Umrn{Umrn: "umrn-1"},
				},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				mockMandateDao.EXPECT().GetByUmrn(context.Background(), "umrn-1", true).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &enachPb.GetEnachMandateResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "should return RecordNotFound rpc status code when no mandate exists with given recurringPaymentId",
			args: args{
				ctx: context.Background(),
				req: &enachPb.GetEnachMandateRequest{
					Identifier: &enachPb.GetEnachMandateRequest_RecurringPaymentId{RecurringPaymentId: "recurring-payment-id-1"},
				},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), "recurring-payment-id-1", true).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &enachPb.GetEnachMandateResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "should return ISE rpc status code when dao call to fetch mandate by umrn fails",
			args: args{
				ctx: context.Background(),
				req: &enachPb.GetEnachMandateRequest{
					Identifier: &enachPb.GetEnachMandateRequest_Umrn{Umrn: "umrn-1"},
				},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				mockMandateDao.EXPECT().GetByUmrn(context.Background(), "umrn-1", true).Return(nil, errors.New("error fetching mandate entry from db"))
			},
			want: &enachPb.GetEnachMandateResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "should return ISE rpc status code when dao call to fetch mandate by recurringPaymentId fails",
			args: args{
				ctx: context.Background(),
				req: &enachPb.GetEnachMandateRequest{
					Identifier: &enachPb.GetEnachMandateRequest_RecurringPaymentId{RecurringPaymentId: "recurring-payment-id-1"},
				},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), "recurring-payment-id-1", true).Return(nil, errors.New("error fetching mandate entry from db"))
			},
			want: &enachPb.GetEnachMandateResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "should return OK rpc status code with mandate details when mandate exists with given umrn",
			args: args{
				ctx: context.Background(),
				req: &enachPb.GetEnachMandateRequest{
					Identifier: &enachPb.GetEnachMandateRequest_Umrn{Umrn: "umrn-1"},
				},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				mockMandateDao.EXPECT().GetByUmrn(context.Background(), "umrn-1", true).Return(enachMandate1, nil)
			},
			want: &enachPb.GetEnachMandateResponse{
				Status:       rpc.StatusOk(),
				EnachMandate: enachMandate1,
			},
			wantErr: false,
		},
		{
			name: "should return OK rpc status code with mandate details when mandate exists with given recurringPaymentId",
			args: args{
				ctx: context.Background(),
				req: &enachPb.GetEnachMandateRequest{
					Identifier: &enachPb.GetEnachMandateRequest_RecurringPaymentId{RecurringPaymentId: "recurring-payment-id-1"},
				},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), "recurring-payment-id-1", true).Return(enachMandate1, nil)
			},
			want: &enachPb.GetEnachMandateResponse{
				Status:       rpc.StatusOk(),
				EnachMandate: enachMandate1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockMandateDao := daoMocks.NewMockEnachMandateDao(ctr)
			mockMandateActionDao := daoMocks.NewMockEnachMandateActionDao(ctr)

			tt.setupMocks(mockMandateDao, mockMandateActionDao)

			s := &Service{
				mandateDao:       mockMandateDao,
				mandateActionDao: mockMandateActionDao,
			}
			got, err := s.GetEnachMandate(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetEnachMandate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetEnachMandate() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetMandateActionStatus(t *testing.T) {
	mandateAction1 := &enachPb.EnachMandateAction{
		Id:              "mandate-action-id-1",
		ClientRequestId: "client-request-id-1",
		EnachMandateId:  "mandate-id-1",
		ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE,
		ActionStatus:    enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS,
		VendorRequestId: "vendor-request-id-1",
		UpdatedAt:       timestamp.Now(),
	}

	type args struct {
		ctx context.Context
		req *enachPb.GetMandateActionStatusRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao)
		want       *enachPb.GetMandateActionStatusResponse
		wantErr    bool
	}{
		{
			name: "should return RecordNotFound rpc status code when no mandate action exists with given clientRequestId",
			args: args{
				ctx: context.Background(),
				req: &enachPb.GetMandateActionStatusRequest{
					ClientRequestId: "client-request-id-1",
				},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), "client-request-id-1").Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &enachPb.GetMandateActionStatusResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "should return ISE rpc status code when dao call to fetch mandate action fails",
			args: args{
				ctx: context.Background(),
				req: &enachPb.GetMandateActionStatusRequest{
					ClientRequestId: "client-request-id-1",
				},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), "client-request-id-1").Return(nil, errors.New("error fetching action entry from db"))
			},
			want: &enachPb.GetMandateActionStatusResponse{
				Status: rpc.StatusInternalWithDebugMsg("error fetching create mandate action entry from db using clientRequestId"),
			},
			wantErr: false,
		},
		{
			name: "should return OK rpc status with mandate action status, subStatus and lastUpdated time",
			args: args{
				ctx: context.Background(),
				req: &enachPb.GetMandateActionStatusRequest{
					ClientRequestId: "client-request-id-1",
				},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), "client-request-id-1").Return(mandateAction1, nil)
			},
			want: &enachPb.GetMandateActionStatusResponse{
				Status:          rpc.StatusOk(),
				ActionStatus:    mandateAction1.GetActionStatus(),
				ActionSubStatus: mandateAction1.GetActionSubStatus(),
				ActionUpdatedAt: mandateAction1.GetUpdatedAt(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockMandateDao := daoMocks.NewMockEnachMandateDao(ctr)
			mockMandateActionDao := daoMocks.NewMockEnachMandateActionDao(ctr)

			tt.setupMocks(mockMandateDao, mockMandateActionDao)

			s := &Service{
				mandateDao:       mockMandateDao,
				mandateActionDao: mockMandateActionDao,
			}
			got, err := s.GetMandateActionStatus(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetMandateActionStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetMandateActionStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetRecurringPaymentIdByVendorReqId(t *testing.T) {
	enachMandate1 := &enachPb.EnachMandate{
		Id:                     "mandate-id-1",
		RecurringPaymentId:     "recurring-payment-id-1",
		Umrn:                   "umrn-1",
		RegistrationAuthMode:   enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
		RegistrationProvenance: enachEnumsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
		Vendor:                 commonvgpb.Vendor_FEDERAL_BANK,
	}
	mandateAction1 := &enachPb.EnachMandateAction{
		Id:              "enach-mandate-action-1",
		ClientRequestId: "client-request-id-1",
		EnachMandateId:  enachMandate1.GetId(),
		ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE,
		ActionStatus:    enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS,
		VendorRequestId: "vendor-request-id-1",
	}

	type args struct {
		ctx context.Context
		req *enachPb.GetRecurringPaymentIdByVendorReqIdRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao)
		want       *enachPb.GetRecurringPaymentIdByVendorReqIdResponse
		wantErr    bool
	}{
		{
			name: "should return RecordNotFound rpc status code when no mandate action exists with given actionType and vendorRequestId",
			args: args{
				ctx: context.Background(),
				req: &enachPb.GetRecurringPaymentIdByVendorReqIdRequest{
					ActionType:  enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE,
					VendorReqId: "vendor-request-id-1",
				},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				mockActionDao.EXPECT().GetByActionTypeAndVendorRequestId(context.Background(), enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE, "vendor-request-id-1").Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &enachPb.GetRecurringPaymentIdByVendorReqIdResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "should return ISE rpc status code when dao call to fetch mandate action fails",
			args: args{
				ctx: context.Background(),
				req: &enachPb.GetRecurringPaymentIdByVendorReqIdRequest{
					ActionType:  enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE,
					VendorReqId: "vendor-request-id-1",
				},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				mockActionDao.EXPECT().GetByActionTypeAndVendorRequestId(context.Background(), enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE, "vendor-request-id-1").Return(nil, errors.New("error fetching mandate action entry from db"))
			},
			want: &enachPb.GetRecurringPaymentIdByVendorReqIdResponse{
				Status: rpc.StatusInternalWithDebugMsg("error fetching mandate action entry from db using actionType and vendorRequestId"),
			},
			wantErr: false,
		},
		{
			name: "should return ISE rpc status code when dao call to fetch mandate fails",
			args: args{
				ctx: context.Background(),
				req: &enachPb.GetRecurringPaymentIdByVendorReqIdRequest{
					ActionType:  enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE,
					VendorReqId: "vendor-request-id-1",
				},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				mockActionDao.EXPECT().GetByActionTypeAndVendorRequestId(context.Background(), enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE, "vendor-request-id-1").Return(mandateAction1, nil)
				mockMandateDao.EXPECT().GetById(context.Background(), mandateAction1.GetEnachMandateId()).Return(nil, errors.New("error fetching mandate entry from db"))
			},
			want: &enachPb.GetRecurringPaymentIdByVendorReqIdResponse{
				Status: rpc.StatusInternalWithDebugMsg("error fetching mandate entry from db using id"),
			},
			wantErr: false,
		},
		{
			name: "should return OK rpc status code with recurring payment id in response",
			args: args{
				ctx: context.Background(),
				req: &enachPb.GetRecurringPaymentIdByVendorReqIdRequest{
					ActionType:  enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE,
					VendorReqId: "vendor-request-id-1",
				},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockActionDao *daoMocks.MockEnachMandateActionDao) {
				mockActionDao.EXPECT().GetByActionTypeAndVendorRequestId(context.Background(), enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE, "vendor-request-id-1").Return(mandateAction1, nil)
				mockMandateDao.EXPECT().GetById(context.Background(), mandateAction1.GetEnachMandateId()).Return(enachMandate1, nil)
			},
			want: &enachPb.GetRecurringPaymentIdByVendorReqIdResponse{
				Status:             rpc.StatusOk(),
				RecurringPaymentId: enachMandate1.GetRecurringPaymentId(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockMandateDao := daoMocks.NewMockEnachMandateDao(ctr)
			mockMandateActionDao := daoMocks.NewMockEnachMandateActionDao(ctr)

			tt.setupMocks(mockMandateDao, mockMandateActionDao)

			s := &Service{
				mandateDao:       mockMandateDao,
				mandateActionDao: mockMandateActionDao,
			}
			got, err := s.GetRecurringPaymentIdByVendorReqId(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRecurringPaymentIdByVendorReqId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetRecurringPaymentIdByVendorReqId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func cloneEnachMandate(enachMandate *enachPb.EnachMandate) *enachPb.EnachMandate {
	return proto.Clone(enachMandate).(*enachPb.EnachMandate)
}

func cloneEnachMandateAction(mandateAction *enachPb.EnachMandateAction) *enachPb.EnachMandateAction {
	return proto.Clone(mandateAction).(*enachPb.EnachMandateAction)
}

// implement the custom matcher
func TestService_GetExecutionOrderDetailsForInboundNotification(t *testing.T) {

	const (
		mandateActionId = "mandate-action-id-1"
		mandateId       = "mandate-id-1"
		clientRequestId = "client-request-id-1"
	)

	executionOrderDetailsForInboundNotificationRequest := &enachPb.GetExecutionOrderDetailsForInboundNotificationRequest{
		EnachMandateId: mandateId,
		InboundNotificationDetails: &enachPb.GetExecutionOrderDetailsForInboundNotificationRequest_InboundNotificationDetails{
			Amount: moneyPb.AmountINR(200).GetPb(),
		},
	}

	enachMandateAction1 := &enachPb.EnachMandateAction{
		Id:              mandateActionId,
		ClientRequestId: clientRequestId,
		EnachMandateId:  mandateId,
		ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE,
		ActionStatus:    enachEnumsPb.EnachActionStatus_ACTION_STATUS_CREATED,
		ActionSubStatus: enachEnumsPb.EnachActionSubStatus_ACTION_SUB_STATUS_AWAITING_AUTHORIZATION,
		VendorRequestId: "",
		ActionMetadata: &enachPb.ActionMetadata{
			ActionTypeSpecificMetadata: &enachPb.ActionMetadata_ExecuteMetadata{
				ExecuteMetadata: &enachPb.ActionMetadata_ExecuteActionMetadata{
					Amount: moneyPb.AmountINR(200).GetPb(),
				},
			},
		},
		CreatedAt: nil,
		UpdatedAt: nil,
	}
	enachMandateActionList1 := []*enachPb.EnachMandateAction{enachMandateAction1}

	type args struct {
		ctx context.Context
		req *enachPb.GetExecutionOrderDetailsForInboundNotificationRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockMandateDao *daoMocks.MockEnachMandateDao, mockMandateActionDao *daoMocks.MockEnachMandateActionDao)
		want       *enachPb.GetExecutionOrderDetailsForInboundNotificationResponse
		wantErr    bool
	}{
		{
			name: "should return ISE rpc status code when dao call to fetch enach mandate action fails",
			args: args{
				ctx: context.Background(),
				req: executionOrderDetailsForInboundNotificationRequest,
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockMandateActionDao *daoMocks.MockEnachMandateActionDao) {
				mockMandateActionDao.EXPECT().GetByEnachMandateId(context.Background(), mandateId, gomock.Any(), gomock.Any()).Return(nil, errors.New("error fetching mandate action entry from db"))
			},
			want: &enachPb.GetExecutionOrderDetailsForInboundNotificationResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching mandate action entries from db using mandate action id and other filters.."),
			},
			wantErr: false,
		},
		{
			name: "should return record not found  rpc status code when dao call to fetch enach mandate action return record not found error",
			args: args{
				ctx: context.Background(),
				req: executionOrderDetailsForInboundNotificationRequest,
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockMandateActionDao *daoMocks.MockEnachMandateActionDao) {
				mockMandateActionDao.EXPECT().GetByEnachMandateId(context.Background(), mandateId, gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &enachPb.GetExecutionOrderDetailsForInboundNotificationResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "should return record not found rpc status when it successfully fetches some mandate actions from DB but not found any mandate with given amount",
			args: args{
				ctx: context.Background(),
				req: &enachPb.GetExecutionOrderDetailsForInboundNotificationRequest{
					EnachMandateId: mandateId,
					InboundNotificationDetails: &enachPb.GetExecutionOrderDetailsForInboundNotificationRequest_InboundNotificationDetails{
						Amount: moneyPb.AmountINR(500).GetPb(),
					},
				},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockMandateActionDao *daoMocks.MockEnachMandateActionDao) {
				mockMandateActionDao.EXPECT().GetByEnachMandateId(context.Background(), mandateId, gomock.Any(), gomock.Any()).Return(enachMandateActionList1, nil)
			},
			want: &enachPb.GetExecutionOrderDetailsForInboundNotificationResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "should return order client request id when successfully fetches some mandate actions form the DB which matches with the given amount",
			args: args{
				ctx: context.Background(),
				req: executionOrderDetailsForInboundNotificationRequest,
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockMandateActionDao *daoMocks.MockEnachMandateActionDao) {
				mockMandateActionDao.EXPECT().GetByEnachMandateId(context.Background(), mandateId, gomock.Any(), gomock.Any()).Return(enachMandateActionList1, nil)
			},
			want: &enachPb.GetExecutionOrderDetailsForInboundNotificationResponse{
				Status:               rpc.StatusOk(),
				OrderClientRequestId: mandateActionId,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockMandateDao := daoMocks.NewMockEnachMandateDao(ctr)
			mockMandateActionDao := daoMocks.NewMockEnachMandateActionDao(ctr)

			tt.setupMocks(mockMandateDao, mockMandateActionDao)

			s := &Service{
				mandateDao:       mockMandateDao,
				mandateActionDao: mockMandateActionDao,
			}
			got, err := s.GetExecutionOrderDetailsForInboundNotification(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExecutionOrderDetailsForInboundNotification error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetExecutionOrderDetailsForInboundNotification got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_IsEnachExecutionAllowed(t *testing.T) {

	const (
		recurringPaymentId = "recurring-payment-id-1"
		enachMandateId     = "enach-mandate-id-1"
	)

	type args struct {
		ctx context.Context
		req *enachPb.IsEnachMandateExecutionAllowedRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockMandateDao *daoMocks.MockEnachMandateDao, mockVendorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor)
		want       *enachPb.IsEnachMandateExecutionAllowedResponse
		wantErr    bool
	}{
		{
			name: "should return ISE rpc status code when dao call to fetch enach mandate fails",
			args: args{
				ctx: context.Background(),
				req: &enachPb.IsEnachMandateExecutionAllowedRequest{RecurringPaymentId: recurringPaymentId},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockVendorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor) {
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), recurringPaymentId, true).Return(nil, errors.New("error fetching mandate entry from db"))
			},
			want: &enachPb.IsEnachMandateExecutionAllowedResponse{
				Status:             rpc.StatusInternalWithDebugMsg("error while fetching enach mandate by recurring payment id"),
				IsExecutionAllowed: false,
			},
			wantErr: false,
		},
		{
			name: "should return failed precondition rpc status code when dao call to fetch enach mandate return record not found error",
			args: args{
				ctx: context.Background(),
				req: &enachPb.IsEnachMandateExecutionAllowedRequest{RecurringPaymentId: recurringPaymentId},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockVendorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor) {
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), recurringPaymentId, true).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &enachPb.IsEnachMandateExecutionAllowedResponse{
				Status:             rpc.StatusFailedPreconditionWithDebugMsg("enach mandate not found with given recurring payment id"),
				IsExecutionAllowed: false,
			},
			wantErr: false,
		},
		{
			name: "should return ISE rpc status code in case of unsupported vendor",
			args: args{
				ctx: context.Background(),
				req: &enachPb.IsEnachMandateExecutionAllowedRequest{RecurringPaymentId: recurringPaymentId},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockVendorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor) {
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), recurringPaymentId, true).Return(&enachPb.EnachMandate{
					Id:                 enachMandateId,
					RecurringPaymentId: recurringPaymentId,
					Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
				}, nil)
				mockVendorFactory.EXPECT().GetVendorProcessor(context.Background(), commonvgpb.Vendor_FEDERAL_BANK).Return(nil)
			},
			want: &enachPb.IsEnachMandateExecutionAllowedResponse{
				Status:             rpc.StatusInternalWithDebugMsg("unsupported vendor for enach"),
				IsExecutionAllowed: false,
			},
			wantErr: false,
		},
		{
			name: "should return ISE rpc status code when vendor processor return an error",
			args: args{
				ctx: context.Background(),
				req: &enachPb.IsEnachMandateExecutionAllowedRequest{RecurringPaymentId: recurringPaymentId},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockVendorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor) {
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), recurringPaymentId, true).Return(&enachPb.EnachMandate{
					Id:                 enachMandateId,
					RecurringPaymentId: recurringPaymentId,
					Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
				}, nil)
				mockVendorFactory.EXPECT().GetVendorProcessor(context.Background(), commonvgpb.Vendor_FEDERAL_BANK).Return(mockVendorProcessor)
				mockVendorProcessor.EXPECT().IsMandateExecutionAllowed(context.Background(), &enachvendor.IsMandateExecutionAllowedRequest{MandateId: enachMandateId}).Return(false, epifierrors.ErrTransient)
			},
			want: &enachPb.IsEnachMandateExecutionAllowedResponse{
				Status:             rpc.StatusInternalWithDebugMsg("error while checking if mandate execution is allowed"),
				IsExecutionAllowed: false,
			},
			wantErr: false,
		},
		{
			name: "should not allow execution of enach mandate when vendor processor checks fails",
			args: args{
				ctx: context.Background(),
				req: &enachPb.IsEnachMandateExecutionAllowedRequest{RecurringPaymentId: recurringPaymentId},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockVendorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor) {
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), recurringPaymentId, true).Return(&enachPb.EnachMandate{
					Id:                 enachMandateId,
					RecurringPaymentId: recurringPaymentId,
					Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
				}, nil)
				mockVendorFactory.EXPECT().GetVendorProcessor(context.Background(), commonvgpb.Vendor_FEDERAL_BANK).Return(mockVendorProcessor)
				mockVendorProcessor.EXPECT().IsMandateExecutionAllowed(context.Background(), &enachvendor.IsMandateExecutionAllowedRequest{MandateId: enachMandateId}).Return(false, nil)
			},
			want: &enachPb.IsEnachMandateExecutionAllowedResponse{
				Status:             rpc.StatusOk(),
				IsExecutionAllowed: false,
			},
			wantErr: false,
		},
		{
			name: "should successfully execute the enach mandate when vendor processor's checks pass",
			args: args{
				ctx: context.Background(),
				req: &enachPb.IsEnachMandateExecutionAllowedRequest{RecurringPaymentId: recurringPaymentId},
			},
			setupMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockVendorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor) {
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), recurringPaymentId, true).Return(&enachPb.EnachMandate{
					Id:                 enachMandateId,
					RecurringPaymentId: recurringPaymentId,
					Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
				}, nil)
				mockVendorFactory.EXPECT().GetVendorProcessor(context.Background(), commonvgpb.Vendor_FEDERAL_BANK).Return(mockVendorProcessor)
				mockVendorProcessor.EXPECT().IsMandateExecutionAllowed(context.Background(), &enachvendor.IsMandateExecutionAllowedRequest{MandateId: enachMandateId}).Return(true, nil)
			},
			want: &enachPb.IsEnachMandateExecutionAllowedResponse{
				Status:             rpc.StatusOk(),
				IsExecutionAllowed: true,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockMandateDao := daoMocks.NewMockEnachMandateDao(ctr)
			mockVendorFactory := vendorMocks.NewMockIFactory(ctr)
			mockVendorProcessor := vendorMocks.NewMockIVendorProcessor(ctr)

			tt.setupMocks(mockMandateDao, mockVendorFactory, mockVendorProcessor)

			s := &Service{
				config:        conf.EnachServiceConfig,
				mandateDao:    mockMandateDao,
				vendorFactory: mockVendorFactory,
			}
			got, err := s.IsEnachMandateExecutionAllowed(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsEnachExecutionAllowed error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("IsEnachExecutionAllowed got = %v, want %v", got, tt.want)
			}
		})
	}

}
