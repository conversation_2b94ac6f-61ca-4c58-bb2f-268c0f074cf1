package enach

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	moneypb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	mockCelestial "github.com/epifi/be-common/api/celestial/mocks"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/rpc"

	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	enachPayloadPb "github.com/epifi/gamma/api/recurringpayment/enach/workflow"
	daoMocks "github.com/epifi/gamma/recurringpayment/enach/dao/mocks"
	vendorMocks "github.com/epifi/gamma/recurringpayment/enach/enachvendor/mocks"
)

func TestService_InitiateExecution(t *testing.T) {
	defaultClientRequestid := "recurring-payment-action-id"
	defaultVendorRequestId := "enach-vendor-request-id"
	enachMandate1 := &enachPb.EnachMandate{
		Id:                     "mandate-id-1",
		RecurringPaymentId:     "recurring-payment-id-1",
		Umrn:                   "umrn-1",
		RegistrationAuthMode:   enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
		RegistrationProvenance: enachEnumsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
		Vendor:                 commonvgpb.Vendor_FEDERAL_BANK,
	}
	mandateAction1 := &enachPb.EnachMandateAction{
		ClientRequestId: defaultClientRequestid,
		EnachMandateId:  enachMandate1.GetId(),
		ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE,
		ActionStatus:    enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS,
		VendorRequestId: defaultVendorRequestId,
		ActionMetadata: &enachPb.ActionMetadata{
			ActionTypeSpecificMetadata: &enachPb.ActionMetadata_ExecuteMetadata{
				ExecuteMetadata: &enachPb.ActionMetadata_ExecuteActionMetadata{
					Amount: &moneypb.Money{
						Units: 10,
					},
				},
			},
		},
	}
	workflowPayload, _ := protojson.Marshal(&enachPayloadPb.ExecuteEnachViaPoolAccountTransferWorkflowPayload{
		MandateActionId: mandateAction1.GetId(),
	})
	type args struct {
		ctx context.Context
		req *enachPb.InitiateExecutionRequest
	}
	tests := []struct {
		name       string
		args       args
		setUpMocks func(mockMandateDao *daoMocks.MockEnachMandateDao, mockVendorProcessorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor, mockActionDao *daoMocks.MockEnachMandateActionDao, mockCelestialClient *mockCelestial.MockCelestialClient)
		want       *enachPb.InitiateExecutionResponse
		wantErr    error
	}{
		{
			name: "should return ISE (fail to check if action exists or not)",
			args: args{
				ctx: context.Background(),
				req: &enachPb.InitiateExecutionRequest{
					ClientRequestId:    "random-client-req-id",
					RecurringPaymentId: "random recurring payment id",
				},
			},
			setUpMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockVendorProcessorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor, mockActionDao *daoMocks.MockEnachMandateActionDao, mockCelestialClient *mockCelestial.MockCelestialClient) {
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), "random-client-req-id").Return(nil, errors.New("error while fetching mandate action"))
			},
			want: &enachPb.InitiateExecutionResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "should return ok (execute action is present for the given client request id)",
			args: args{
				ctx: context.Background(),
				req: &enachPb.InitiateExecutionRequest{
					ClientRequestId:    defaultClientRequestid,
					RecurringPaymentId: enachMandate1.GetRecurringPaymentId(),
				},
			},
			setUpMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockVendorProcessorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor, mockActionDao *daoMocks.MockEnachMandateActionDao, mockCelestialClient *mockCelestial.MockCelestialClient) {
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), defaultClientRequestid).Return(&enachPb.EnachMandateAction{}, nil)
			},
			want: &enachPb.InitiateExecutionResponse{
				Status: rpc.StatusOk(),
			},
		},
		{
			name: "should return precondition failed (no supported vendor found for execution)",
			args: args{
				ctx: context.Background(),
				req: &enachPb.InitiateExecutionRequest{
					ClientRequestId:    defaultClientRequestid,
					RecurringPaymentId: enachMandate1.GetRecurringPaymentId(),
				},
			},
			setUpMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockVendorProcessorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor, mockActionDao *daoMocks.MockEnachMandateActionDao, mockCelestialClient *mockCelestial.MockCelestialClient) {
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), defaultClientRequestid).Return(nil, nil)
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), enachMandate1.GetRecurringPaymentId(), true).Return(enachMandate1, nil)
				mockVendorProcessorFactory.EXPECT().GetVendorProcessor(context.Background(), enachMandate1.GetVendor()).Return(nil)
			},
			want: &enachPb.InitiateExecutionResponse{
				Status: rpc.StatusFailedPrecondition(),
			},
		},
		{
			name: "should return ISE (fail to fetch mandate)",
			args: args{
				ctx: context.Background(),
				req: &enachPb.InitiateExecutionRequest{
					ClientRequestId:    "random-client-req-id",
					RecurringPaymentId: "random recurring payment id",
				},
			},
			setUpMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockVendorProcessorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor, mockActionDao *daoMocks.MockEnachMandateActionDao, mockCelestialClient *mockCelestial.MockCelestialClient) {
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), "random-client-req-id").Return(nil, epifierrors.ErrRecordNotFound)
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), "random recurring payment id", true).Return(nil, errors.New("error while fetching enach mandate"))
			},
			want: &enachPb.InitiateExecutionResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "should return ISE (fail to create mandate action)",
			args: args{
				ctx: context.Background(),
				req: &enachPb.InitiateExecutionRequest{
					ClientRequestId:    defaultClientRequestid,
					RecurringPaymentId: enachMandate1.GetRecurringPaymentId(),
					Amount: &moneypb.Money{
						Units: 10,
					},
				},
			},
			setUpMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockVendorProcessorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor, mockActionDao *daoMocks.MockEnachMandateActionDao, mockCelestialClient *mockCelestial.MockCelestialClient) {
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), defaultClientRequestid).Return(nil, epifierrors.ErrRecordNotFound)
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), enachMandate1.GetRecurringPaymentId(), true).Return(enachMandate1, nil)
				mockVendorProcessorFactory.EXPECT().GetVendorProcessor(context.Background(), enachMandate1.GetVendor()).Return(mockVendorProcessor)
				mockVendorProcessor.EXPECT().GenerateMandateExecutionVendorRequestId(context.Background()).Return(defaultVendorRequestId)
				mockActionDao.EXPECT().Create(context.Background(), &enachPb.EnachMandateAction{
					ClientRequestId: defaultClientRequestid,
					EnachMandateId:  enachMandate1.GetId(),
					ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE,
					ActionStatus:    enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS,
					VendorRequestId: defaultVendorRequestId,
					ActionMetadata: &enachPb.ActionMetadata{
						ActionTypeSpecificMetadata: &enachPb.ActionMetadata_ExecuteMetadata{
							ExecuteMetadata: &enachPb.ActionMetadata_ExecuteActionMetadata{
								Amount: &moneypb.Money{
									Units: 10,
								},
							},
						},
					},
				}).Return(nil, errors.New("failed to create the mandate action"))
			},
			want: &enachPb.InitiateExecutionResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "should return ISE (failed to initiate the workflow)",
			args: args{
				ctx: context.Background(),
				req: &enachPb.InitiateExecutionRequest{
					ClientRequestId:    defaultClientRequestid,
					RecurringPaymentId: enachMandate1.GetRecurringPaymentId(),
					Amount: &moneypb.Money{
						Units: 10,
					},
				},
			},
			setUpMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockVendorProcessorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor, mockActionDao *daoMocks.MockEnachMandateActionDao, mockCelestialClient *mockCelestial.MockCelestialClient) {
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), defaultClientRequestid).Return(nil, epifierrors.ErrRecordNotFound)
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), enachMandate1.GetRecurringPaymentId(), true).Return(enachMandate1, nil)
				mockActionDao.EXPECT().Create(context.Background(), &enachPb.EnachMandateAction{
					ClientRequestId: defaultClientRequestid,
					EnachMandateId:  enachMandate1.GetId(),
					ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE,
					ActionStatus:    enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS,
					VendorRequestId: defaultVendorRequestId,
					ActionMetadata: &enachPb.ActionMetadata{
						ActionTypeSpecificMetadata: &enachPb.ActionMetadata_ExecuteMetadata{
							ExecuteMetadata: &enachPb.ActionMetadata_ExecuteActionMetadata{
								Amount: &moneypb.Money{
									Units: 10,
								},
							},
						},
					},
				}).Return(mandateAction1, nil)
				mockVendorProcessorFactory.EXPECT().GetVendorProcessor(context.Background(), enachMandate1.GetVendor()).Return(mockVendorProcessor)
				mockVendorProcessor.EXPECT().GenerateMandateExecutionVendorRequestId(context.Background()).Return(defaultVendorRequestId)
				mockCelestialClient.EXPECT().InitiateWorkflow(context.Background(), &celestialPb.InitiateWorkflowRequest{
					Params: &celestialPb.WorkflowCreationRequestParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id:     mandateAction1.GetId(),
							Client: workflowPb.Client_RECURRING_PAYMENT,
						},
						Payload:          workflowPayload,
						Ownership:        commontypes.Ownership_EPIFI_TECH,
						QualityOfService: celestialPb.QoS_GUARANTEED,
						Version:          workflowPb.Version_V0,
						Type:             celestial.GetTypeEnumFromWorkflowType(rpNs.ExecuteEnachViaPoolAccountTransfer),
					},
				}).Return(nil, errors.New("failed to initiate workflow"))
			},
			want: &enachPb.InitiateExecutionResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "succesffully initiated the exeuction workflow",
			args: args{
				ctx: context.Background(),
				req: &enachPb.InitiateExecutionRequest{
					ClientRequestId:    defaultClientRequestid,
					RecurringPaymentId: enachMandate1.GetRecurringPaymentId(),
					Amount: &moneypb.Money{
						Units: 10,
					},
				},
			},
			setUpMocks: func(mockMandateDao *daoMocks.MockEnachMandateDao, mockVendorProcessorFactory *vendorMocks.MockIFactory, mockVendorProcessor *vendorMocks.MockIVendorProcessor, mockActionDao *daoMocks.MockEnachMandateActionDao, mockCelestialClient *mockCelestial.MockCelestialClient) {
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), defaultClientRequestid).Return(nil, epifierrors.ErrRecordNotFound)
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), enachMandate1.GetRecurringPaymentId(), true).Return(enachMandate1, nil)
				mockActionDao.EXPECT().Create(context.Background(), &enachPb.EnachMandateAction{
					ClientRequestId: defaultClientRequestid,
					EnachMandateId:  enachMandate1.GetId(),
					ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE,
					ActionStatus:    enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS,
					VendorRequestId: defaultVendorRequestId,
					ActionMetadata: &enachPb.ActionMetadata{
						ActionTypeSpecificMetadata: &enachPb.ActionMetadata_ExecuteMetadata{
							ExecuteMetadata: &enachPb.ActionMetadata_ExecuteActionMetadata{
								Amount: &moneypb.Money{
									Units: 10,
								},
							},
						},
					},
				}).Return(mandateAction1, nil)
				mockVendorProcessorFactory.EXPECT().GetVendorProcessor(context.Background(), enachMandate1.GetVendor()).Return(mockVendorProcessor)
				mockVendorProcessor.EXPECT().GenerateMandateExecutionVendorRequestId(context.Background()).Return(defaultVendorRequestId)
				mockCelestialClient.EXPECT().InitiateWorkflow(context.Background(), &celestialPb.InitiateWorkflowRequest{
					Params: &celestialPb.WorkflowCreationRequestParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id:     mandateAction1.GetId(),
							Client: workflowPb.Client_RECURRING_PAYMENT,
						},
						Payload:          workflowPayload,
						Ownership:        commontypes.Ownership_EPIFI_TECH,
						QualityOfService: celestialPb.QoS_GUARANTEED,
						Version:          workflowPb.Version_V0,
						Type:             celestial.GetTypeEnumFromWorkflowType(rpNs.ExecuteEnachViaPoolAccountTransfer),
					},
				}).Return(&celestialPb.InitiateWorkflowResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want: &enachPb.InitiateExecutionResponse{
				Status: rpc.StatusOk(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockMandateDao := daoMocks.NewMockEnachMandateDao(ctr)
			mockActionDao := daoMocks.NewMockEnachMandateActionDao(ctr)
			mockCelestialAccount := mockCelestial.NewMockCelestialClient(ctr)
			mockVendorProcessorFactory := vendorMocks.NewMockIFactory(ctr)
			mockVendorProcessor := vendorMocks.NewMockIVendorProcessor(ctr)
			tt.setUpMocks(mockMandateDao, mockVendorProcessorFactory, mockVendorProcessor, mockActionDao, mockCelestialAccount)

			s := &Service{
				mandateDao:       mockMandateDao,
				mandateActionDao: mockActionDao,
				celestialClient:  mockCelestialAccount,
				vendorFactory:    mockVendorProcessorFactory,
			}
			got, err := s.InitiateExecution(tt.args.ctx, tt.args.req)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("InitiateExecution() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("InitiateExecution() got = %v, want %v", got, tt.want)
			}
		})
	}
}
