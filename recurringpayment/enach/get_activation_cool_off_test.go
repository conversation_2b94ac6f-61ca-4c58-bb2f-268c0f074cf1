package enach

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/types/known/durationpb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
)

func TestService_GetActivationCooldown(t *testing.T) {
	tests := []struct {
		name    string
		req     *enachPb.GetActivationCoolOffRequest
		want    *enachPb.GetActivationCoolOffResponse
		wantErr bool
	}{
		{
			name: "Should get InvalidArgument as status response if passing un-configured vendor",
			req: &enachPb.GetActivationCoolOffRequest{
				Vendor: commonvgpb.Vendor_VENDOR_UNSPECIFIED,
			},
			want: &enachPb.GetActivationCoolOffResponse{
				Status: rpcPb.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "Should get valid cooldown for federal if passing federal vendor",
			req: &enachPb.GetActivationCoolOffRequest{
				Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			},
			want: &enachPb.GetActivationCoolOffResponse{
				Status:          rpcPb.StatusOk(),
				CoolOffDuration: durationpb.New(time.Minute * 5),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			s := &Service{
				config: conf.EnachServiceConfig,
			}

			got, err := s.GetActivationCoolOff(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetActivationCoolOff() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetActivationCoolOff() got = %v, want %v", got, tt.want)
			}
		})
	}
}
