package enach

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/logger"

	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	enachPayloadPb "github.com/epifi/gamma/api/recurringpayment/enach/workflow"
)

// InitiateExecution is used to initiate the execution of the already created enach in the system
// steps followed in the rpc:
// 1. Check if execute action already exists for the given client request id (for idempotency)
// 2. Generate request id for execution mandate at vendor's end
// 3. Create enach_mandate_action_dao
// 4. Trigger the workflow for the enach execution
// nolint:funlen
func (s *Service) InitiateExecution(ctx context.Context, req *enachPb.InitiateExecutionRequest) (*enachPb.InitiateExecutionResponse, error) {
	fetchedExecuteAction, fetchedExecuteActionErr := s.mandateActionDao.GetByClientRequestId(ctx, req.GetClientRequestId())
	switch {
	case fetchedExecuteActionErr != nil && !errors.Is(fetchedExecuteActionErr, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "error while checking if execute action already exists", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(fetchedExecuteActionErr))
		return &enachPb.InitiateExecutionResponse{
			Status: rpc.StatusInternal(),
		}, nil
	case fetchedExecuteAction != nil:
		logger.Error(ctx, "execute action already exists for the given request", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()))
		return &enachPb.InitiateExecutionResponse{
			Status: rpc.StatusOk(),
		}, nil
	}

	enachMandate, enachMandateErr := s.mandateDao.GetByRecurringPaymentId(ctx, req.GetRecurringPaymentId(), true)
	if enachMandateErr != nil {
		logger.Error(ctx, "failed to fetch enach mandate by recurring payment id", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(enachMandateErr))
		return &enachPb.InitiateExecutionResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	// generate request id for creating mandate at vendor's end
	vendorProcessor := s.vendorFactory.GetVendorProcessor(ctx, enachMandate.GetVendor())
	if vendorProcessor == nil {
		logger.Error(ctx, "cannot process creation request, vendor is not supported for enach", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.String(logger.VENDOR, enachMandate.GetVendor().String()))
		return &enachPb.InitiateExecutionResponse{
			Status: rpc.StatusFailedPrecondition(),
		}, nil
	}
	vendorRequestId := vendorProcessor.GenerateMandateExecutionVendorRequestId(ctx)

	executeAction, executeActionErr := s.mandateActionDao.Create(ctx, &enachPb.EnachMandateAction{
		ClientRequestId: req.GetClientRequestId(),
		EnachMandateId:  enachMandate.GetId(),
		VendorRequestId: vendorRequestId,
		ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE,
		// execution status in progress
		// NOTE: not initialising with created status since we do not require precise bifurgation
		ActionStatus: enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS,
		ActionMetadata: &enachPb.ActionMetadata{
			ActionTypeSpecificMetadata: &enachPb.ActionMetadata_ExecuteMetadata{
				ExecuteMetadata: &enachPb.ActionMetadata_ExecuteActionMetadata{
					Amount: req.GetAmount(),
				},
			},
		},
	})
	if executeActionErr != nil {
		logger.Error(ctx, "failed to create execute enach action", zap.String(logger.MANDATE_ACTION_ID, enachMandate.GetId()), zap.Error(executeActionErr))
		return &enachPb.InitiateExecutionResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	initiateExecutionWorkflowErr := s.initiateExecutionWorkflowForGivenVendor(ctx, executeAction.GetId(), enachMandate.GetVendor())
	if initiateExecutionWorkflowErr != nil {
		logger.Error(ctx, "failed to initate execution workflow", zap.String(logger.MANDATE_ACTION_ID, enachMandate.GetId()), zap.Error(initiateExecutionWorkflowErr))
		return &enachPb.InitiateExecutionResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	return &enachPb.InitiateExecutionResponse{
		Status: rpc.StatusOk(),
	}, nil
}

// initiateExecutionWorkflowForGivenVendor triggerrs the workflow corresponding to the vendor
func (s *Service) initiateExecutionWorkflowForGivenVendor(ctx context.Context, mandateActionId string, vendor commonvgpb.Vendor) error {
	if vendor == commonvgpb.Vendor_FEDERAL_BANK {
		workflowPayload, marshalErr := protojson.Marshal(&enachPayloadPb.ExecuteEnachViaPoolAccountTransferWorkflowPayload{
			MandateActionId: mandateActionId,
		})
		if marshalErr != nil {
			return fmt.Errorf("error while marshalling enach execution workflow payload,err: %w", marshalErr)
		}
		initiateWorkflowRes, initiateWorfkflowErr := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
			Params: &celestialPb.WorkflowCreationRequestParams{
				ClientReqId: &workflowPb.ClientReqId{
					Id:     mandateActionId,
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				Payload:          workflowPayload,
				Ownership:        commontypes.Ownership_EPIFI_TECH,
				QualityOfService: celestialPb.QoS_GUARANTEED,
				Version:          workflowPb.Version_V0,
				Type:             celestial.GetTypeEnumFromWorkflowType(rpNs.ExecuteEnachViaPoolAccountTransfer),
			},
		})
		if err := epifigrpc.RPCError(initiateWorkflowRes, initiateWorfkflowErr); err != nil {
			return fmt.Errorf("error while initiating enach execution via pool account transafer, err: %w", initiateWorfkflowErr)
		}
	} else {
		return fmt.Errorf("no execution workflow present for the given vendor %s, err: %w", vendor.String(), epifierrors.ErrInvalidArgument)
	}
	return nil
}
