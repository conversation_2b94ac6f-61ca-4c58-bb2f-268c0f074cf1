package enach

import (
	"flag"
	"os"
	"testing"

	"gorm.io/gorm"

	rpServerConfig "github.com/epifi/gamma/recurringpayment/config/server"
	rpServerGenConf "github.com/epifi/gamma/recurringpayment/config/server/genconf"
	"github.com/epifi/gamma/recurringpayment/test"
)

var (
	db          *gorm.DB
	conf        *rpServerConfig.Config
	dynamicConf *rpServerGenConf.Config
)

// nolint:dogsled
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, conf, dynamicConf, db, teardown = test.InitTestServerForPGDB()

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
