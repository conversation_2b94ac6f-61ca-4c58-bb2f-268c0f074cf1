package enach

import (
	"context"
	"errors"
	"fmt"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"go.uber.org/zap"

	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	storage "github.com/epifi/be-common/pkg/storage/v2"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	rpServerConfig "github.com/epifi/gamma/recurringpayment/config/server"
	"github.com/epifi/gamma/recurringpayment/enach/dao"
	"github.com/epifi/gamma/recurringpayment/enach/enachvendor"
)

type Service struct {
	config                 *rpServerConfig.EnachServiceConfig
	mandateDao             dao.EnachMandateDao
	mandateActionDao       dao.EnachMandateActionDao
	vendorFactory          enachvendor.IFactory
	txnExecutor            storage.TxnExecutor
	recurringPaymentClient rpPb.RecurringPaymentServiceClient
	celestialClient        celestialPb.CelestialClient
}

func NewService(
	config *rpServerConfig.Config,
	mandateDao dao.EnachMandateDao,
	mandateActionDao dao.EnachMandateActionDao,
	txnExecutor storage.TxnExecutor,
	vendorFactory enachvendor.IFactory,
	recurringPaymentClient rpPb.RecurringPaymentServiceClient,
	celestialClient celestialPb.CelestialClient,
) *Service {
	return &Service{
		config:                 config.EnachServiceConfig,
		mandateDao:             mandateDao,
		mandateActionDao:       mandateActionDao,
		vendorFactory:          vendorFactory,
		txnExecutor:            txnExecutor,
		recurringPaymentClient: recurringPaymentClient,
		celestialClient:        celestialClient,
	}
}

// compile time check to make sure Service implements enachPb.EnachServiceServer
var _ enachPb.EnachServiceServer = &Service{}

// InitiateMandateCreation rpc is useful to initiate creation of a new enach mandate
// Note : this rpc should be used only for initiating on-app mandate creating flow and shouldn't be used for persisting off app mandates.
func (s *Service) InitiateMandateCreation(ctx context.Context, req *enachPb.InitiateMandateCreationRequest) (*enachPb.InitiateMandateCreationResponse, error) {
	// check if mandate has already been created for given recurring payment for idempotency
	fetchedMandate, fetchedMandateErr := s.mandateDao.GetByRecurringPaymentId(ctx, req.GetRecurringPaymentId(), true)
	switch {
	case fetchedMandateErr != nil && !errors.Is(fetchedMandateErr, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "error while checking if mandate alerady exists", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, req.GetRecurringPaymentId()))
		return &enachPb.InitiateMandateCreationResponse{
			Status: rpc.StatusInternal(),
		}, nil
	case fetchedMandate != nil:
		logger.Info(ctx, "mandate already exists for given recurring payment id", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		return &enachPb.InitiateMandateCreationResponse{
			Status: rpc.StatusOk(),
		}, nil
	}

	// generate request id for creating mandate at vendor's end
	vendorProcessor := s.vendorFactory.GetVendorProcessor(ctx, req.GetVendor())
	if vendorProcessor == nil {
		logger.Error(ctx, "cannot process creation request, vendor is not supported for enach", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.String(logger.VENDOR, req.GetVendor().String()))
		return &enachPb.InitiateMandateCreationResponse{Status: rpc.StatusFailedPreconditionWithDebugMsg("unsupported vendor for enach")}, nil
	}
	vendorRequestId := vendorProcessor.GenerateMandateCreationRequestId(ctx)

	// create mandate and mandate action entries in db in a db txn block
	txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		// create mandate entry in db
		mandateEntry, createMandateErr := s.mandateDao.Create(txnCtx, &enachPb.EnachMandate{
			RecurringPaymentId:   req.GetRecurringPaymentId(),
			RegistrationAuthMode: req.GetRegistrationAuthMode(),
			// this rpc is specific to in-app enach only so setting the registration provenance as FI_APP
			RegistrationProvenance: enachEnumsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
			Vendor:                 req.GetVendor(),
		}, commontypes.Ownership_EPIFI_TECH)
		if createMandateErr != nil {
			return fmt.Errorf("error creating mandate entry in db, err : %w", createMandateErr)
		}
		// create mandate action entry in db for the just created mandate entry
		_, createActionErr := s.mandateActionDao.Create(txnCtx, &enachPb.EnachMandateAction{
			ClientRequestId: req.GetClientRequestId(),
			EnachMandateId:  mandateEntry.GetId(),
			ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE,
			ActionStatus:    enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS,
			ActionSubStatus: enachEnumsPb.EnachActionSubStatus_ACTION_SUB_STATUS_AWAITING_AUTHORIZATION,
			VendorRequestId: vendorRequestId,
		})
		if createActionErr != nil {
			return fmt.Errorf("error creating mandate action entry in db, err : %w", createActionErr)
		}

		return nil
	})
	if txnErr != nil {
		logger.Error(ctx, "error creating mandate and mandate action entry in a db txn", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(txnErr))
		return &enachPb.InitiateMandateCreationResponse{Status: rpc.StatusInternalWithDebugMsg("error creating mandate and mandate action entry in a db txn")}, nil
	}

	return &enachPb.InitiateMandateCreationResponse{Status: rpc.StatusOk()}, nil
}

// GetAuthorizationPayloadForMandateCreation rpc is useful to fetch the authorization payload needed for a new mandate creation.
func (s *Service) GetAuthorizationPayloadForMandateCreation(ctx context.Context, req *enachPb.GetAuthorizationPayloadForMandateCreationRequest) (*enachPb.GetAuthorizationPayloadForMandateCreationResponse, error) {
	// fetch the recurring payment entry
	getRecurringPaymentRes, err := s.recurringPaymentClient.GetRecurringPaymentById(ctx, &rpPb.GetRecurringPaymentByIdRequest{
		Id: req.GetRecurringPaymentId(),
	})
	if err = epifigrpc.RPCError(getRecurringPaymentRes, err); err != nil {
		logger.Error(ctx, "error while fetching recurring payment for given id.", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(err))
		return &enachPb.GetAuthorizationPayloadForMandateCreationResponse{Status: rpc.StatusInternalWithDebugMsg("error while fetching recurring payment for given id.")}, nil
	}

	// fetch the mandate entry from db
	mandateEntry, err := s.mandateDao.GetByRecurringPaymentId(ctx, req.GetRecurringPaymentId(), true)
	if err != nil {
		logger.Error(ctx, "error fetching mandate entry from db using recurring payment id", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(err))
		return &enachPb.GetAuthorizationPayloadForMandateCreationResponse{Status: rpc.StatusInternalWithDebugMsg("error fetching mandate entry from db using recurring payment id")}, nil
	}

	// fetch mandate creation action entry
	mandateActionEntry, err := s.mandateActionDao.GetByActionTypeAndEnachMandateId(ctx, enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE, mandateEntry.GetId())
	if err != nil {
		logger.Error(ctx, "error fetching create mandate action entry from Enach Mandate Id", zap.String(logger.MANDATE_ID, mandateEntry.GetId()), zap.Error(err))
		return &enachPb.GetAuthorizationPayloadForMandateCreationResponse{Status: rpc.StatusInternalWithDebugMsg("error fetching create mandate action entry from db using mandate_id")}, nil
	}

	// generate vendor specific auth payload
	vendorProcessor := s.vendorFactory.GetVendorProcessor(ctx, mandateEntry.GetVendor())
	if vendorProcessor == nil {
		logger.Error(ctx, "cannot process creation request, vendor is not supported for enach", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		return &enachPb.GetAuthorizationPayloadForMandateCreationResponse{
			Status: rpc.StatusFailedPrecondition(),
		}, nil
	}
	authPayloadRes, err := vendorProcessor.GenerateAuthPayloadForMandateCreation(ctx, &enachvendor.AuthPayloadForMandateCreationRequest{
		RecurringPayment: getRecurringPaymentRes.GetRecurringPayment(),
		Mandate:          mandateEntry,
		MandateAction:    mandateActionEntry[0],
	})
	if err != nil {
		logger.Error(ctx, "error generating mandate creation auth payload", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(err))
		return &enachPb.GetAuthorizationPayloadForMandateCreationResponse{Status: rpc.StatusInternalWithDebugMsg("error generating mandate creation auth payload")}, nil
	}

	return &enachPb.GetAuthorizationPayloadForMandateCreationResponse{
		Status:      rpc.StatusOk(),
		AuthPayload: authPayloadRes.AuthPayload,
	}, nil
}

// AuthorizeMandateCreation rpc should be used to authorize enach mandate creation,
// for enach, the authorization is done by the payer off app on the destination bank (denotes the bank where the customer holds their account from which the automatic debit is made) portal, so this api is called when we receive a successful authorization callback from the bank.
func (s *Service) AuthorizeMandateCreation(ctx context.Context, req *enachPb.AuthorizeMandateCreationRequest) (*enachPb.AuthorizeMandateCreationResponse, error) {
	// fetch the mandate entry from db
	mandateEntry, err := s.mandateDao.GetByRecurringPaymentId(ctx, req.GetRecurringPaymentId(), true)
	if err != nil {
		logger.Error(ctx, "error fetching mandate entry from db using recurring payment id", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(err))
		return &enachPb.AuthorizeMandateCreationResponse{Status: rpc.StatusInternalWithDebugMsg("error fetching mandate entry from db using recurring payment id")}, nil
	}
	// fetch mandate creation action entry
	mandateCreationActions, err := s.mandateActionDao.GetByActionTypeAndEnachMandateId(ctx, enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE, mandateEntry.GetId())
	if err != nil {
		logger.Error(ctx, "error fetching create mandate action entry from db using enach mandate id", zap.String(logger.MANDATE_ID, mandateEntry.GetId()), zap.Error(err))
		return &enachPb.AuthorizeMandateCreationResponse{Status: rpc.StatusInternalWithDebugMsg("error fetching create mandate action entry from db using enach mandate id")}, nil
	}
	// there should be only a single create action entry for a given mandate
	mandateCreationActionEntry := mandateCreationActions[0]

	actionStatus, actionSubStatus := mandateCreationActionEntry.GetActionStatus(), mandateCreationActionEntry.GetActionSubStatus()

	logger.Info(ctx, "mandate id", zap.String(logger.ID, mandateEntry.GetId()), zap.Any(logger.MANDATE_ACION, mandateCreationActionEntry))
	// if action is already in success state, it implies creation was already authorized, returning success to ensure idempotency
	if actionStatus == enachEnumsPb.EnachActionStatus_ACTION_STATUS_SUCCESS {
		// logger.Info(ctx, "mandate id", zap.String(logger.ID, mandateEntry.GetId()), zap.Any())
		return &enachPb.AuthorizeMandateCreationResponse{Status: rpc.StatusOk()}, nil
	}
	// authorization is only allowed if action is in ACTION_SUB_STATUS_AWAITING_AUTHORIZATION state
	if actionStatus != enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS || actionSubStatus != enachEnumsPb.EnachActionSubStatus_ACTION_SUB_STATUS_AWAITING_AUTHORIZATION {
		logger.Error(ctx, "authorization is not allowed", zap.String(logger.STATUS, actionStatus.String()), zap.String(logger.SUB_STATUS, actionSubStatus.String()))
		return &enachPb.AuthorizeMandateCreationResponse{Status: rpc.StatusFailedPreconditionWithDebugMsg("authorization is not allowed")}, nil
	}

	// update action status and mandate details
	txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		mandateEntry.Umrn = req.GetUmrn()
		if mandateUpdateErr := s.mandateDao.Update(txnCtx, []enachEnumsPb.EnachMandateFieldMask{enachEnumsPb.EnachMandateFieldMask_ENACH_MANDATE_FIELD_MASK_UMRN}, mandateEntry); mandateUpdateErr != nil {
			return fmt.Errorf("error updating umrn details in mandate entry, err : %w", mandateUpdateErr)
		}
		mandateCreationActionEntry.ActionStatus = enachEnumsPb.EnachActionStatus_ACTION_STATUS_SUCCESS
		mandateCreationActionEntry.ActionSubStatus = enachEnumsPb.EnachActionSubStatus_ENACH_ACTION_SUB_STATUS_UNSPECIFIED
		mandateCreationActionEntry.ActionMetadata = &enachPb.ActionMetadata{
			ActionTypeSpecificMetadata: &enachPb.ActionMetadata_CreateMetadata{
				CreateMetadata: &enachPb.ActionMetadata_CreateActionMetadata{
					Umrn:                       req.GetUmrn(),
					NpciRefId:                  req.GetNpciRefId(),
					DestBankReferenceNumber:    req.GetDestBankReferenceNumber(),
					MerchantReferenceMessageId: req.GetMerchantReferenceMessageId(),
				}},
		}

		updateMask := []enachEnumsPb.EnachMandateActionFieldMask{enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_STATUS, enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_SUB_STATUS, enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_METADATA}
		if mandateActionUpdateErr := s.mandateActionDao.UpdateWithStatusCheck(txnCtx, updateMask, mandateCreationActionEntry, enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS, enachEnumsPb.EnachActionSubStatus_ACTION_SUB_STATUS_AWAITING_AUTHORIZATION); mandateActionUpdateErr != nil {
			return fmt.Errorf("error updating umrn details in mandate action entry, err : %w", mandateActionUpdateErr)
		}
		return nil
	})
	if txnErr != nil {
		logger.Error(ctx, "error updating mandate and mandate action entry in a db txn", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(txnErr))
		return &enachPb.AuthorizeMandateCreationResponse{Status: rpc.StatusInternalWithDebugMsg("error updating mandate and mandate action entry in a db txn")}, nil
	}

	return &enachPb.AuthorizeMandateCreationResponse{Status: rpc.StatusOk()}, nil
}

// GetMandateActionStatus rpc should be used to get the status of an already initiated action on an enach mandate.
func (s *Service) GetMandateActionStatus(ctx context.Context, req *enachPb.GetMandateActionStatusRequest) (*enachPb.GetMandateActionStatusResponse, error) {
	mandateActionEntry, err := s.mandateActionDao.GetByClientRequestId(ctx, req.GetClientRequestId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "mandate action not found", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()))
		return &enachPb.GetMandateActionStatusResponse{Status: rpc.StatusRecordNotFound()}, nil
	case err != nil:
		logger.Error(ctx, "error fetching create mandate action entry from db using clientRequestId", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(err))
		return &enachPb.GetMandateActionStatusResponse{Status: rpc.StatusInternalWithDebugMsg("error fetching create mandate action entry from db using clientRequestId")}, nil
	}

	actionStatus, actionSubStatus := mandateActionEntry.GetActionStatus(), mandateActionEntry.GetActionSubStatus()

	return &enachPb.GetMandateActionStatusResponse{
		Status:               rpc.StatusOk(),
		ActionStatus:         actionStatus,
		ActionSubStatus:      actionSubStatus,
		ActionUpdatedAt:      mandateActionEntry.GetUpdatedAt(),
		ActionDetailedStatus: mandateActionEntry.GetActionDetailedStatus(),
	}, nil
}

// GetRecurringPaymentIdByVendorReqId should be used to fetch the recurring payment id using the vendor request id with which enach mandate creation was initiated at vendor's end.
func (s *Service) GetRecurringPaymentIdByVendorReqId(ctx context.Context, req *enachPb.GetRecurringPaymentIdByVendorReqIdRequest) (*enachPb.GetRecurringPaymentIdByVendorReqIdResponse, error) {
	mandateActionEntry, err := s.mandateActionDao.GetByActionTypeAndVendorRequestId(ctx, req.GetActionType(), req.GetVendorReqId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "no mandate action entry found with given actionType and vendorRequestId", zap.String(logger.ACTION_TYPE, req.GetActionType().String()), zap.String(logger.VENDOR_REQUEST, req.GetVendorReqId()))
		return &enachPb.GetRecurringPaymentIdByVendorReqIdResponse{Status: rpc.StatusRecordNotFound()}, nil

	case err != nil:
		logger.Error(ctx, "error fetching mandate action entry from db using actionType and vendorRequestId", zap.String(logger.ACTION_TYPE, req.GetActionType().String()), zap.String(logger.VENDOR_REQUEST, req.GetVendorReqId()), zap.Error(err))
		return &enachPb.GetRecurringPaymentIdByVendorReqIdResponse{Status: rpc.StatusInternalWithDebugMsg("error fetching mandate action entry from db using actionType and vendorRequestId")}, nil
	}

	mandateEntry, err := s.mandateDao.GetById(ctx, mandateActionEntry.GetEnachMandateId())
	if err != nil {
		logger.Error(ctx, "error fetching mandate entry from db using id", zap.String(logger.MANDATE_ID, mandateActionEntry.GetId()), zap.Error(err))
		return &enachPb.GetRecurringPaymentIdByVendorReqIdResponse{Status: rpc.StatusInternalWithDebugMsg("error fetching mandate entry from db using id")}, nil
	}

	return &enachPb.GetRecurringPaymentIdByVendorReqIdResponse{
		Status:             rpc.StatusOk(),
		RecurringPaymentId: mandateEntry.GetRecurringPaymentId(),
	}, nil
}

// GetEnachMandate fetches the enach mandate using the given identifier.
func (s *Service) GetEnachMandate(ctx context.Context, req *enachPb.GetEnachMandateRequest) (*enachPb.GetEnachMandateResponse, error) {
	var (
		fetchedEnachMandate    *enachPb.EnachMandate
		fetchedEnachMandateErr error
	)
	switch req.GetIdentifier().(type) {
	case *enachPb.GetEnachMandateRequest_Umrn:
		fetchedEnachMandate, fetchedEnachMandateErr = s.mandateDao.GetByUmrn(ctx, req.GetUmrn(), true)
	case *enachPb.GetEnachMandateRequest_RecurringPaymentId:
		fetchedEnachMandate, fetchedEnachMandateErr = s.mandateDao.GetByRecurringPaymentId(ctx, req.GetRecurringPaymentId(), true)
	default:
		logger.Error(ctx, "unknown identifier for fetching the mandate")
		return &enachPb.GetEnachMandateResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}
	switch {
	case errors.Is(fetchedEnachMandateErr, epifierrors.ErrRecordNotFound):
		return &enachPb.GetEnachMandateResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	case fetchedEnachMandateErr != nil:
		logger.Error(ctx, "failed to fetch the enach mandate", zap.Error(fetchedEnachMandateErr), zap.Any(logger.REQUEST, req.GetIdentifier()))
		return &enachPb.GetEnachMandateResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	return &enachPb.GetEnachMandateResponse{
		Status:       rpc.StatusOk(),
		EnachMandate: fetchedEnachMandate,
	}, nil
}

// CreateOffAppEnachMandate idempotent rpc used to persist ENACH mandate that is initiated from 3rd party app/off-app like Groww.
// It is also used to persist ENACH mandate entities for in-app mandates created via PaymentGateway, since for Paymentgateway,
// we don't use the domain entities for orchestrating the flows, rather store these only for bookkeeping purposes.
// Hence, we create only the ENACH mandate domain entity and not the ENACH mandate action domain entity.
func (s *Service) CreateOffAppEnachMandate(ctx context.Context, req *enachPb.CreateOffAppEnachMandateRequest) (*enachPb.CreateOffAppEnachMandateResponse, error) {
	ctx = epificontext.WithOwnership(ctx, req.GetOwnership())

	// check if enach mandate already exists using recurring payment id
	existingEnachMandate, existingEnachMandateErr := s.mandateDao.GetByRecurringPaymentId(ctx, req.GetRecurringPaymentId(), true)
	if existingEnachMandateErr != nil && !errors.Is(existingEnachMandateErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "failed to check if enach mandate exists for the given recurring payment id", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(existingEnachMandateErr))
		return &enachPb.CreateOffAppEnachMandateResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	if existingEnachMandate != nil {
		return &enachPb.CreateOffAppEnachMandateResponse{
			Status:       rpc.StatusOk(),
			EnachMandate: existingEnachMandate,
		}, nil
	}
	// create new enach mandate with given request
	createdEnach, createdEnachErr := s.mandateDao.Create(ctx, &enachPb.EnachMandate{
		Umrn:                   req.GetUmrn(),
		RecurringPaymentId:     req.GetRecurringPaymentId(),
		RegistrationAuthMode:   req.GetRegistrationAuthMode(),
		RegistrationProvenance: req.GetProvenance(),
		Vendor:                 req.GetVendor(),
	}, req.GetOwnership())
	if createdEnachErr != nil {
		logger.Error(ctx, "failed to create off app enach", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(createdEnachErr))
		return &enachPb.CreateOffAppEnachMandateResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	return &enachPb.CreateOffAppEnachMandateResponse{
		Status:       rpc.StatusOk(),
		EnachMandate: createdEnach,
	}, nil
}

// todo (Vishal : id=60877) -> take vendor in request and add filter with vendor as well
func (s *Service) GetExecutionOrderDetailsForInboundNotification(ctx context.Context, request *enachPb.GetExecutionOrderDetailsForInboundNotificationRequest) (*enachPb.GetExecutionOrderDetailsForInboundNotificationResponse, error) {
	var (
		res = &enachPb.GetExecutionOrderDetailsForInboundNotificationResponse{}
	)

	enachMandateActionList, err := s.mandateActionDao.GetByEnachMandateId(ctx, request.GetEnachMandateId(), dao.WithActionType(enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE), dao.WithActionStatus(enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS))
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Info(ctx, "no entries found in db with the given enach mandate id and the required filters.", zap.String(logger.ID, request.GetEnachMandateId()))
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		logger.Error(ctx, "error while fetching the enach mandate actions with enach mandate id and the required filters", zap.Error(err), zap.String(logger.ID, request.GetEnachMandateId()))
		res.Status = rpc.StatusInternalWithDebugMsg("error while fetching mandate action entries from db using mandate action id and other filters..")
		return res, nil
	}

	for _, enachMandateAction := range enachMandateActionList {

		comparison, err := moneyPkg.CompareV2(enachMandateAction.GetActionMetadata().GetExecuteMetadata().GetAmount(), request.GetInboundNotificationDetails().GetAmount())
		if err != nil {
			logger.Info(ctx, "error while comparing mandate action amount with present amount", zap.String(logger.ID, enachMandateAction.GetId()))
			res.Status = rpc.StatusInternalWithDebugMsg("error while comparing the mandate action amount and the present amount")
			return res, nil
		}

		if comparison == 0 {
			res.OrderClientRequestId = enachMandateAction.GetId()
			res.Status = rpc.StatusOk()
			return res, nil
		}
	}

	logger.Info(ctx, "no entries found in db with the given enach mandate id and the required filters.", zap.String(logger.ID, request.GetEnachMandateId()))
	res.Status = rpc.StatusRecordNotFound()
	return res, nil
}

// IsEnachMandateExecutionAllowed is useful to check whether execution for a given enach mandate is allowed or not.
// It checks for minimum wait duration for a returned txn etc.
func (s *Service) IsEnachMandateExecutionAllowed(ctx context.Context, req *enachPb.IsEnachMandateExecutionAllowedRequest) (*enachPb.IsEnachMandateExecutionAllowedResponse, error) {
	enachMandate, err := s.mandateDao.GetByRecurringPaymentId(ctx, req.GetRecurringPaymentId(), true)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "enach mandate not found with given recurring payment id", zap.String(logger.ID, req.GetRecurringPaymentId()))
		return &enachPb.IsEnachMandateExecutionAllowedResponse{Status: rpc.StatusFailedPreconditionWithDebugMsg("enach mandate not found with given recurring payment id"), IsExecutionAllowed: false}, nil
	case err != nil:
		logger.Error(ctx, "error while fetching the enach mandate with given enach mandate id", zap.Error(err), zap.String(logger.ID, req.GetRecurringPaymentId()))
		return &enachPb.IsEnachMandateExecutionAllowedResponse{Status: rpc.StatusInternalWithDebugMsg("error while fetching enach mandate by recurring payment id"), IsExecutionAllowed: false}, nil
	}

	vendorProcessor := s.vendorFactory.GetVendorProcessor(ctx, enachMandate.GetVendor())
	if vendorProcessor == nil {
		logger.Error(ctx, "cannot process creation request, vendor is not supported for enach", zap.String(logger.VENDOR, enachMandate.GetVendor().String()), zap.String(logger.MANDATE_ID, enachMandate.GetId()))
		return &enachPb.IsEnachMandateExecutionAllowedResponse{Status: rpc.StatusInternalWithDebugMsg("unsupported vendor for enach")}, nil
	}

	isExecutionAllowed, err := vendorProcessor.IsMandateExecutionAllowed(ctx, &enachvendor.IsMandateExecutionAllowedRequest{MandateId: enachMandate.GetId()})
	if err != nil {
		logger.Error(ctx, "error while checking if mandate execution is allowed", zap.String(logger.ID, req.GetRecurringPaymentId()))
		return &enachPb.IsEnachMandateExecutionAllowedResponse{Status: rpc.StatusInternalWithDebugMsg("error while checking if mandate execution is allowed")}, nil
	}

	return &enachPb.IsEnachMandateExecutionAllowedResponse{Status: rpc.StatusOk(), IsExecutionAllowed: isExecutionAllowed}, nil
}
