package enach

import (
	"context"
	"reflect"
	"sort"
	"testing"

	"github.com/golang/mock/gomock"

	rpcPb "github.com/epifi/be-common/api/rpc"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	typesPb "github.com/epifi/gamma/api/typesv2"
	rpServerConfig "github.com/epifi/gamma/recurringpayment/config/server"
)

func TestService_GetSupportedBanks(t *testing.T) {
	tests := []struct {
		name string
		conf *rpServerConfig.EnachServiceConfig
		want *enachPb.GetSupportedBanksResponse
	}{
		{
			name: "should return 3 bank details if 3 bank names are valid",
			conf: &rpServerConfig.EnachServiceConfig{
				SupportedBankToDetailsMap: map[string]*rpServerConfig.EnachSupportedBankDetails{
					"kotak": {
						BankIconUrl:        "https://epifi-icons.pointz.in/amc_logos/Kotak.png",
						SupportedAuthModes: []string{"REGISTRATION_AUTH_MODE_NET_BANKING", "REGISTRATION_AUTH_MODE_DEBIT_CARD"},
					},
					"idfc": {
						BankIconUrl:        "https://epifi-icons.pointz.in/amc_logos/IDFC.png",
						SupportedAuthModes: []string{"REGISTRATION_AUTH_MODE_NET_BANKING"},
					},
					"axis": {
						BankIconUrl:        "https://epifi-icons.pointz.in/amc_logos/Axis.png",
						SupportedAuthModes: []string{"REGISTRATION_AUTH_MODE_DEBIT_CARD"},
					},
				},
			},
			want: &enachPb.GetSupportedBanksResponse{
				Status: rpcPb.StatusOk(),
				SupportedBanks: []*enachPb.GetSupportedBanksResponse_BankInfo{
					{
						BankName: typesPb.Bank_KOTAK,
						LogoUrl:  "https://epifi-icons.pointz.in/amc_logos/Kotak.png",
						SupportedAuthModes: []enachEnumsPb.EnachRegistrationAuthMode{
							enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
							enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_DEBIT_CARD,
						},
					},
					{
						BankName: typesPb.Bank_IDFC,
						LogoUrl:  "https://epifi-icons.pointz.in/amc_logos/IDFC.png",
						SupportedAuthModes: []enachEnumsPb.EnachRegistrationAuthMode{
							enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
						},
					},
					{
						BankName: typesPb.Bank_AXIS,
						LogoUrl:  "https://epifi-icons.pointz.in/amc_logos/Axis.png",
						SupportedAuthModes: []enachEnumsPb.EnachRegistrationAuthMode{
							enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_DEBIT_CARD,
						},
					},
				},
			},
		},
		{
			name: "should return 2 bank details if only banks have valid names with auth mode",
			conf: &rpServerConfig.EnachServiceConfig{
				SupportedBankToDetailsMap: map[string]*rpServerConfig.EnachSupportedBankDetails{
					"kotak": {
						BankIconUrl:        "https://epifi-icons.pointz.in/amc_logos/Kotak.png",
						SupportedAuthModes: []string{"REGISTRATION_AUTH_MODE_NET_BANKING", "REGISTRATION_AUTH_MODE_DEBIT_CARD"},
					},
					"idfc": {
						BankIconUrl:        "https://epifi-icons.pointz.in/amc_logos/IDFC.png",
						SupportedAuthModes: []string{"REGISTRATION_AUTH_MODE_NET_BANKING"},
					},
					"axis": {
						BankIconUrl:        "https://epifi-icons.pointz.in/amc_logos/Axis.png",
						SupportedAuthModes: []string{"DEBIT_CARD"},
					},
					"random-non-existent-name": {
						BankIconUrl:        "https://epifi-icons.pointz.in/amc_logos/random.png",
						SupportedAuthModes: []string{"REGISTRATION_AUTH_MODE_NET_BANKING"},
					},
				},
			},
			want: &enachPb.GetSupportedBanksResponse{
				Status: rpcPb.StatusOk(),
				SupportedBanks: []*enachPb.GetSupportedBanksResponse_BankInfo{
					{
						BankName: typesPb.Bank_KOTAK,
						LogoUrl:  "https://epifi-icons.pointz.in/amc_logos/Kotak.png",
						SupportedAuthModes: []enachEnumsPb.EnachRegistrationAuthMode{
							enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
							enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_DEBIT_CARD,
						},
					},
					{
						BankName: typesPb.Bank_IDFC,
						LogoUrl:  "https://epifi-icons.pointz.in/amc_logos/IDFC.png",
						SupportedAuthModes: []enachEnumsPb.EnachRegistrationAuthMode{
							enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			s := &Service{
				config: tt.conf,
			}

			got, _ := s.GetSupportedBanks(context.Background(), &enachPb.GetSupportedBanksRequest{})

			sort.Slice(got.GetSupportedBanks(), func(i, j int) bool {
				return got.GetSupportedBanks()[i].BankName > got.GetSupportedBanks()[j].BankName
			})
			sort.Slice(tt.want.GetSupportedBanks(), func(i, j int) bool {
				return tt.want.GetSupportedBanks()[i].BankName > tt.want.GetSupportedBanks()[j].BankName
			})

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetSupportedBanks() got = %v, want %v", got, tt.want)
			}
		})
	}
}
