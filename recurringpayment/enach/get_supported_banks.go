package enach

import (
	"context"
	"strings"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/be-common/pkg/logger"
)

func (s *Service) GetSupportedBanks(ctx context.Context, req *enachPb.GetSupportedBanksRequest) (*enachPb.GetSupportedBanksResponse, error) {
	res := &enachPb.GetSupportedBanksResponse{}

	res.SupportedBanks = s.getSupportedBanksList(ctx)
	res.Status = rpcPb.StatusOk()

	return res, nil
}

func (s *Service) getSupportedBanksList(ctx context.Context) []*enachPb.GetSupportedBanksResponse_BankInfo {
	var res []*enachPb.GetSupportedBanksResponse_BankInfo

	for bankName, bankDetails := range s.config.SupportedBankToDetailsMap {
		bankIndex, bankIndexOk := typesPb.Bank_value[strings.ToUpper(bankName)]
		if !bankIndexOk {
			// If we are getting an invalid name in the config, we are logging the error and handling gracefully.
			// We do this to ensure that addition on a single bank name which has some error does not break the whole api
			logger.Error(ctx, "unable to fetch bank index for bank name. bank name not defined in bank proto", zap.String("bank_name", bankName))
			continue
		}

		var supportedAuthModes []enachEnumsPb.EnachRegistrationAuthMode
		for _, supportedAuthModeEntry := range bankDetails.SupportedAuthModes {
			authorisationModeIndex, authorisationModeOk := enachEnumsPb.EnachRegistrationAuthMode_value[supportedAuthModeEntry]
			if !authorisationModeOk {
				// If we are getting an invalid auth mode in the config, we are logging the error and handling gracefully.
				// We do this to ensure that addition on a single auth mode which has some typo does not cause other auth modes to be rejected.
				logger.Error(ctx, "unable to fetch authorisation mode for auth mode entry. authorisation mode not defined in enach enums proto", zap.String("authorisation_mode", supportedAuthModeEntry))
				continue
			}
			supportedAuthModes = append(supportedAuthModes, enachEnumsPb.EnachRegistrationAuthMode(authorisationModeIndex))
		}

		if len(supportedAuthModes) != 0 {
			res = append(res, &enachPb.GetSupportedBanksResponse_BankInfo{
				BankName:           typesPb.Bank(bankIndex),
				LogoUrl:            bankDetails.BankIconUrl,
				SupportedAuthModes: supportedAuthModes,
			})
		}
	}
	return res
}
