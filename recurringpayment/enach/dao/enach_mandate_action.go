package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	"github.com/epifi/gamma/api/recurringpayment/enach/enums"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	"github.com/epifi/gamma/recurringpayment/enach/dao/model"
)

type EnachMandateActionDaoPgdb struct {
	db *gorm.DB
}

func NewEnachMandateActionDaoPgdb(db types.RecurringPaymentPGDB) *EnachMandateActionDaoPgdb {
	return &EnachMandateActionDaoPgdb{db: db}
}

// compile time check to ensure EnachMandateActionDaoPgdb implements EnachMandateActionDao
var _ EnachMandateActionDao = &EnachMandateActionDaoPgdb{}

var mandateActionFieldMaskToColumName = map[enachEnumsPb.EnachMandateActionFieldMask]string{
	enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_STATUS:                  "action_status",
	enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_SUB_STATUS:              "action_sub_status",
	enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_METADATA:                "action_metadata",
	enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_VENDOR_BATCH_REQUEST_ID: "vendor_batch_request_id",
	enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_DETAILED_STATUS:         "action_detailed_status",
}

func (e *EnachMandateActionDaoPgdb) Create(ctx context.Context, action *enachPb.EnachMandateAction) (*enachPb.EnachMandateAction, error) {
	defer metric_util.TrackDuration("recurringpayment/enach/dao", "EnachMandateActionDaoPgdb", "Create", time.Now())
	if action.GetEnachMandateId() == "" || action.GetClientRequestId() == "" ||
		action.GetActionType() == enachEnumsPb.EnachActionType_ENACH_ACTION_TYPE_UNSPECIFIED || action.GetActionStatus() == enachEnumsPb.EnachActionStatus_ENACH_ACTION_STATUS_UNSPECIFIED {
		return nil, fmt.Errorf("mandatory params missing, err :  %w", epifierrors.ErrInvalidArgument)
	}

	db := gormctxv2.FromContextOrDefault(ctx, e.db)

	actionDbModel := model.NewEnachMandateAction(action)
	if err := db.Create(actionDbModel).Error; err != nil {
		return nil, fmt.Errorf("failed to persist enach mandate action in db, err : %w", err)
	}

	return actionDbModel.GetProto(), nil
}

// nolint: dupl
func (e *EnachMandateActionDaoPgdb) Update(ctx context.Context, updateMask []enachEnumsPb.EnachMandateActionFieldMask, action *enachPb.EnachMandateAction) error {
	defer metric_util.TrackDuration("recurringpayment/enach/dao", "EnachMandateActionDaoPgdb", "Update", time.Now())
	if action.GetId() == "" {
		return fmt.Errorf("id cannot be empty for update query, err : %w", epifierrors.ErrInvalidArgument)
	}
	updateColumns := getColumnNamesFromMandateActionFieldMask(updateMask)
	if len(updateColumns) == 0 {
		return fmt.Errorf("no columns to update, err : %w", epifierrors.ErrInvalidArgument)
	}

	db := gormctxv2.FromContextOrDefault(ctx, e.db)

	actionDbModel := model.ConvertToEnachMandateActionUpdateDbModel(action)
	res := db.Model(actionDbModel).Where("id = ?", actionDbModel.Id).Select(updateColumns).Updates(actionDbModel)
	if res.Error != nil {
		return fmt.Errorf("error updating enach mandate action entry, err : %w", res.Error)
	}
	if res.RowsAffected == 0 {
		return epifierrors.ErrRowNotUpdated
	}

	return nil
}

func (e *EnachMandateActionDaoPgdb) UpdateWithStatusCheck(ctx context.Context, updateMask []enachEnumsPb.EnachMandateActionFieldMask, action *enachPb.EnachMandateAction, expectedCurrentStatus enachEnumsPb.EnachActionStatus, expectedCurrentSubStatus enachEnumsPb.EnachActionSubStatus) error {
	defer metric_util.TrackDuration("recurringpayment/enach/dao", "EnachMandateActionDaoPgdb", "UpdateWithStatusCheck", time.Now())
	if action.GetId() == "" {
		return fmt.Errorf("id cannot be empty for update query, err : %w", epifierrors.ErrInvalidArgument)
	}
	updateColumns := getColumnNamesFromMandateActionFieldMask(updateMask)
	if len(updateColumns) == 0 {
		return fmt.Errorf("no columns to update, err : %w", epifierrors.ErrInvalidArgument)
	}

	db := gormctxv2.FromContextOrDefault(ctx, e.db)

	actionDbModel := model.ConvertToEnachMandateActionUpdateDbModel(action)
	res := db.Model(actionDbModel).Where("id = ? and action_status = ? and action_sub_status = ?", actionDbModel.Id, expectedCurrentStatus, expectedCurrentSubStatus).Select(updateColumns).Updates(actionDbModel)
	if res.Error != nil {
		return fmt.Errorf("error updating enach mandate action entry, err : %w", res.Error)
	}
	if res.RowsAffected == 0 {
		return epifierrors.ErrRowNotUpdated
	}

	return nil
}

func (e *EnachMandateActionDaoPgdb) GetByActionTypeAndEnachMandateId(ctx context.Context, actionType enachEnumsPb.EnachActionType, mandateId string) ([]*enachPb.EnachMandateAction, error) {
	defer metric_util.TrackDuration("recurringpayment/enach/dao", "EnachMandateActionDaoPgdb", "GetByActionTypeAndEnachMandateId", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, e.db)

	var actionDbModels []*model.EnachMandateAction
	res := db.Where("action_type = ? AND enach_mandate_id = ?", actionType, mandateId).Find(&actionDbModels)
	if res.Error != nil {
		return nil, fmt.Errorf("failed to fetch enach mandate action by enach_mandate_id and action type, err : %w", res.Error)
	}
	if len(actionDbModels) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	actionProtos := make([]*enachPb.EnachMandateAction, len(actionDbModels))
	for i, actionDbModel := range actionDbModels {
		actionProtos[i] = actionDbModel.GetProto()
	}
	return actionProtos, nil
}

func (e *EnachMandateActionDaoPgdb) GetByClientRequestId(ctx context.Context, requestId string) (*enachPb.EnachMandateAction, error) {
	defer metric_util.TrackDuration("recurringpayment/enach/dao", "EnachMandateActionDaoPgdb", "GetByClientRequestId", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, e.db)

	actionDbModel := &model.EnachMandateAction{}
	res := db.Where("client_request_id = ?", requestId).Take(actionDbModel)
	if errors.Is(res.Error, gorm.ErrRecordNotFound) {
		return nil, epifierrors.ErrRecordNotFound
	}
	if res.Error != nil {
		return nil, fmt.Errorf("failed to fetch enach mandate action by client_request_id, err : %w", res.Error)
	}

	return actionDbModel.GetProto(), nil
}

func (e *EnachMandateActionDaoPgdb) GetByActionTypeAndVendorRequestId(ctx context.Context, actionType enums.EnachActionType, vendorRequestId string) (*enachPb.EnachMandateAction, error) {
	defer metric_util.TrackDuration("recurringpayment/enach/dao", "EnachMandateActionDaoPgdb", "GetByActionTypeAndVendorRequestId", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, e.db)

	actionDbModel := &model.EnachMandateAction{}
	res := db.Where("action_type = ? AND vendor_request_id = ?", actionType, vendorRequestId).Take(actionDbModel)
	if errors.Is(res.Error, gorm.ErrRecordNotFound) {
		return nil, epifierrors.ErrRecordNotFound
	}
	if res.Error != nil {
		return nil, fmt.Errorf("failed to fetch enach mandate action by action_type and vendor_request_id, err : %w", res.Error)
	}
	return actionDbModel.GetProto(), nil
}

func getColumnNamesFromMandateActionFieldMask(fieldMask []enachEnumsPb.EnachMandateActionFieldMask) []string {
	var columns []string
	for _, field := range fieldMask {
		columns = append(columns, mandateActionFieldMaskToColumName[field])
	}
	return columns
}

// nolint: dupl
func (e *EnachMandateActionDaoPgdb) GetById(ctx context.Context, id string) (*enachPb.EnachMandateAction, error) {
	defer metric_util.TrackDuration("recurringpayment/enach/dao", "EnachMandateActionDaoPgdb", "GetById", time.Now())
	if id == "" {
		return nil, fmt.Errorf("id cannot be empty, err :  %w", epifierrors.ErrInvalidArgument)
	}
	db := gormctxv2.FromContextOrDefault(ctx, e.db)

	actionDbModel := &model.EnachMandateAction{}
	res := db.Where("id = ?", id).Take(actionDbModel)
	if errors.Is(res.Error, gorm.ErrRecordNotFound) {
		return nil, epifierrors.ErrRecordNotFound
	}
	if res.Error != nil {
		return nil, fmt.Errorf("failed to fetch enach mandate by id, err : %w", res.Error)
	}

	return actionDbModel.GetProto(), nil
}

// GetByIds fetches DB records belonging to given enach mandate action ids. Returns error if record not found.
// nolint: dupl
func (e *EnachMandateActionDaoPgdb) GetByIds(ctx context.Context, mandateActionIds []string) ([]*enachPb.EnachMandateAction, error) {
	defer metric_util.TrackDuration("recurringpayment/enach/dao", "EnachMandateActionDaoPgdb", "GetByIds", time.Now())

	if len(mandateActionIds) == 0 {
		return nil, fmt.Errorf("list of mandateActionIds cannot be empty %w", epifierrors.ErrInvalidArgument)
	}
	for _, mandateActionId := range mandateActionIds {
		if mandateActionId == "" {
			return nil, fmt.Errorf("mandateActionId cannot be empty %w", epifierrors.ErrInvalidArgument)
		}
	}
	db := gormctxv2.FromContextOrDefault(ctx, e.db)

	var actionDbModels []*model.EnachMandateAction
	if err := db.Where("id IN (?)", mandateActionIds).Find(&actionDbModels).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch record for mandate action by mandateActionIds: %v: %w", mandateActionIds, err)
	}
	if len(actionDbModels) == 0 {
		return nil, fmt.Errorf("failed to fetch record for mandate action by mandateActionIds: %v: %w", mandateActionIds, epifierrors.ErrRecordNotFound)
	}

	return convertToMandateActionsProto(actionDbModels), nil
}
func convertToMandateActionsProto(mandateActionModels []*model.EnachMandateAction) []*enachPb.EnachMandateAction {
	var mandateActions = make([]*enachPb.EnachMandateAction, len(mandateActionModels))
	for i, mandateActionModel := range mandateActionModels {
		mandateActions[i] = mandateActionModel.GetProto()
	}
	return mandateActions
}

func (e *EnachMandateActionDaoPgdb) GetByEnachMandateId(ctx context.Context, enachMandateId string, options ...FilterOption) ([]*enachPb.EnachMandateAction, error) {
	defer metric_util.TrackDuration("recurringpayment/enach/dao", "EnachMandateActionDaoPgdb", "GetByEnachMandateId", time.Now())
	if enachMandateId == "" {
		return nil, fmt.Errorf("enach mandate id connot be empty, err : %w", epifierrors.ErrInvalidArgument)
	}
	db := gormctxv2.FromContextOrDefault(ctx, e.db)

	for _, opt := range options {
		db = opt.applyInGorm(db)
	}

	var actionDbModels []*model.EnachMandateAction
	err := db.Where("enach_mandate_id = ?", enachMandateId).Find(&actionDbModels).Error
	if err != nil {
		return nil, fmt.Errorf("failed to fetch enach mandate action by enach mandate id and reuired filters.., err : %s", err.Error())
	}
	if len(actionDbModels) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	actionProtos := make([]*enachPb.EnachMandateAction, len(actionDbModels))
	for i, actionDbModel := range actionDbModels {
		actionProtos[i] = actionDbModel.GetProto()
	}
	return actionProtos, nil
}

func (e *EnachMandateActionDaoPgdb) GetByVendorBatchRequestId(ctx context.Context, id string) ([]*enachPb.EnachMandateAction, error) {
	defer metric_util.TrackDuration("recurringpayment/enach/dao", "EnachMandateActionDaoPgdb", "GetByVendorBatchRequestId", time.Now())
	if id == "" {
		return nil, fmt.Errorf("id cannot be empty, err :  %w", epifierrors.ErrInvalidArgument)
	}
	db := gormctxv2.FromContextOrDefault(ctx, e.db)

	var actionDbModels []*model.EnachMandateAction
	err := db.Where("vendor_batch_request_id = ?", id).Find(&actionDbModels).Error
	if err != nil {
		return nil, fmt.Errorf("failed to fetch enach mandate actions by vendor batch request id, err : %s", err)
	}
	if len(actionDbModels) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	return convertToMandateActionsProto(actionDbModels), nil
}
