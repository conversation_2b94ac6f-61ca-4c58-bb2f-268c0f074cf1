package dao

import (
	"context"
	"errors"
	"testing"

	"google.golang.org/protobuf/proto"
	gormV2 "gorm.io/gorm"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"

	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumbsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	rpServerConfig "github.com/epifi/gamma/recurringpayment/config/server"
)

type EnachMandateTestSuite struct {
	db     *gormV2.DB
	dao    EnachMandateDao
	config *rpServerConfig.Config
}

func initialiseEnachMandateTestSuite(t *testing.T) (*EnachMandateTestSuite, func()) {
	idg := idgen.NewDomainIdGenerator(idgen.NewClock())
	dbResourceProvider, release := dbResourceProviderPool.GetDbConnProviderWithUseCase(t)
	attrIdGen := idgen.NewAttributedIdGen[*EnachMandatesAttributes](idgen.NewClock())
	emts := newEnachMandateTestSuite(db, NewEnachMandateDaoPgdb(db, idg, dbResourceProvider, true, attrIdGen), conf)
	return emts, func() {
		release(entitySegregatedTestTablesMap)
	}
}

func newEnachMandateTestSuite(db *gormV2.DB, dao EnachMandateDao, config *rpServerConfig.Config) *EnachMandateTestSuite {
	return &EnachMandateTestSuite{
		db:     db,
		dao:    dao,
		config: config,
	}
}

func TestEnachMandateDaoPgdb_Create(t *testing.T) {
	type args struct {
		ctx             context.Context
		enachMandate    *enachPb.EnachMandate
		entityOwnership commontypes.Ownership
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "successfully created the enach mandate for the given request and EPIFI_TECH ownership",
			args: args{
				ctx: context.Background(),
				enachMandate: &enachPb.EnachMandate{
					Umrn:                   "umrn-test",
					RecurringPaymentId:     "recurring-payment-id-test",
					RegistrationProvenance: enachEnumbsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
					RegistrationAuthMode:   enachEnumbsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_DEBIT_CARD,
				},
				entityOwnership: commontypes.Ownership_EPIFI_TECH,
			},
			wantErr: false,
		},
		{
			name: "successfully create the enach mandate for the given request and STOCK_GUARDIAN_TSP ownership",
			args: args{
				ctx: context.Background(),
				enachMandate: &enachPb.EnachMandate{
					Umrn:                   "umrn-test",
					RecurringPaymentId:     "recurring-payment-id-test",
					RegistrationProvenance: enachEnumbsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
					RegistrationAuthMode:   enachEnumbsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_DEBIT_CARD,
				},
				entityOwnership: commontypes.Ownership_STOCK_GUARDIAN_TSP,
			},
			wantErr: false,
		},
		{
			name: "enach mandate creation should fail due to duplicate umrn",
			args: args{
				ctx: context.Background(),
				enachMandate: &enachPb.EnachMandate{
					Umrn:                   "umrn-1",
					RecurringPaymentId:     "recurring-payment-id-test",
					RegistrationProvenance: enachEnumbsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
					RegistrationAuthMode:   enachEnumbsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_DEBIT_CARD,
				},
				entityOwnership: commontypes.Ownership_EPIFI_TECH,
			},
			wantErr: true,
		},
		{
			name: "enach mandate creation should fail due to duplicate umrn, in stockguardian ownership",
			args: args{
				ctx: context.Background(),
				enachMandate: &enachPb.EnachMandate{
					Umrn:                   "umrn-5",
					RecurringPaymentId:     "recurring-payment-id-test",
					RegistrationProvenance: enachEnumbsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
					RegistrationAuthMode:   enachEnumbsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_DEBIT_CARD,
				},
				entityOwnership: commontypes.Ownership_STOCK_GUARDIAN_TSP,
			},
			wantErr: true,
		},
		{
			name: "enach mandate creation should fail (mandatory param recurring payment id is empty)",
			args: args{
				ctx: context.Background(),
				enachMandate: &enachPb.EnachMandate{
					RecurringPaymentId:     "",
					RegistrationProvenance: enachEnumbsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
					RegistrationAuthMode:   enachEnumbsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_DEBIT_CARD,
				},
				entityOwnership: commontypes.Ownership_EPIFI_TECH,
			},
			wantErr: true,
		},
		{
			name: "failed to create (already exists)",
			args: args{
				ctx: context.Background(),
				enachMandate: &enachPb.EnachMandate{
					Umrn:                   enachMandateFixture1.GetUmrn(),
					RecurringPaymentId:     enachMandateFixture1.GetRecurringPaymentId(),
					RegistrationProvenance: enachMandateFixture1.GetRegistrationProvenance(),
					RegistrationAuthMode:   enachMandateFixture1.GetRegistrationAuthMode(),
				},
				entityOwnership: commontypes.Ownership_EPIFI_TECH,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			enachMandateTs, release := initialiseEnachMandateTestSuite(t)
			defer release()

			got, err := enachMandateTs.dao.Create(tt.args.ctx, tt.args.enachMandate, tt.args.entityOwnership)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			// if error did not occur then id should have been returned
			if !tt.wantErr {
				if got.Id == "" {
					t.Errorf("Create() success but generated id is nil")
					return
				}
			}
		})
	}
}

func TestEnachMandateDaoPgdb_Update(t *testing.T) {
	type args struct {
		ctx          context.Context
		updateMask   []enachEnumbsPb.EnachMandateFieldMask
		enachMandate *enachPb.EnachMandate
	}
	tests := []struct {
		name    string
		args    args
		wantErr error
	}{
		{
			name: "should successfully update the umrn in default ownership (EPIFI_TECH)",
			args: args{
				ctx:        context.Background(),
				updateMask: []enachEnumbsPb.EnachMandateFieldMask{enachEnumbsPb.EnachMandateFieldMask_ENACH_MANDATE_FIELD_MASK_UMRN},
				enachMandate: &enachPb.EnachMandate{
					Id: enachMandateFixture1.GetId(),
				},
			},
		},
		{
			name: "should successfully update the umrn in STOCKGUARDIAN_TSP ownership",
			args: args{
				ctx:        ctxWithSgOwnership,
				updateMask: []enachEnumbsPb.EnachMandateFieldMask{enachEnumbsPb.EnachMandateFieldMask_ENACH_MANDATE_FIELD_MASK_UMRN},
				enachMandate: &enachPb.EnachMandate{
					Id:   sgEnachMandateFixture1.GetId(),
					Umrn: "new-umrn",
				},
			},
		},
		{
			name: "failed to update (empty field mask)",
			args: args{
				ctx: context.Background(),
				enachMandate: &enachPb.EnachMandate{
					Id: "enach-mandate-id-1",
				},
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "failed to update (mandatory field id is empty)",
			args: args{
				ctx: context.Background(),
				enachMandate: &enachPb.EnachMandate{
					RecurringPaymentId: "recurring-payment-id",
				},
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "failed to update (no record found for the given request)",
			args: args{
				ctx:        context.Background(),
				updateMask: []enachEnumbsPb.EnachMandateFieldMask{enachEnumbsPb.EnachMandateFieldMask_ENACH_MANDATE_FIELD_MASK_UMRN},
				enachMandate: &enachPb.EnachMandate{
					Id:                 "random-id",
					RecurringPaymentId: "recurring-payment-id",
					Umrn:               "updated-umrn",
				},
			},
			wantErr: epifierrors.ErrRowNotUpdated,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			enachMandateTs, release := initialiseEnachMandateTestSuite(t)
			defer release()

			err := enachMandateTs.dao.Update(tt.args.ctx, tt.args.updateMask, tt.args.enachMandate)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("Update() got error %v, want error %v", err, tt.wantErr)
			}
		})
	}
}

func TestEnachMandateDaoPgdb_GetById(t *testing.T) {
	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		args    args
		want    *enachPb.EnachMandate
		wantErr error
	}{
		{
			name: "successfully fetch for given id in EPIFI_TECH ownership",
			args: args{
				ctx: context.Background(),
				id:  epifiEnachMandateFixture1.GetId(),
			},
			want: epifiEnachMandateFixture1,
		},
		{
			name: "successfully fetch for given id in STOCK_GUARDIAN_TSP ownership",
			args: args{
				ctx: context.Background(),
				id:  sgEnachMandateFixture1.GetId(),
			},
			want: sgEnachMandateFixture1,
		},
		{
			name: "failed to fetch (mandatory param id is empty)",
			args: args{
				ctx: context.Background(),
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "no record found for the given request",
			args: args{
				ctx: context.Background(),
				id:  "random-enach-mandate-id",
			},
			wantErr: epifierrors.ErrRecordNotFound,
		},
		{
			name: "no record found for the given request in EPIFI_TECH DB for STOCK_GUARDIAN_TSP ownership",
			args: args{
				ctx: ctxWithSgOwnership,
				id:  enachMandateFixture1.GetId(),
			},
			wantErr: epifierrors.ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			enachMandateTs, release := initialiseEnachMandateTestSuite(t)
			defer release()

			got, err := enachMandateTs.dao.GetById(tt.args.ctx, tt.args.id)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !compareEnachMandate(got, tt.want) {
				t.Errorf("GetById() got = %v, want = %v", got, tt.want)
			}
		})
	}
}

func TestEnachMandateDaoPgdb_GetByIds(t *testing.T) {
	type args struct {
		ctx context.Context
		ids []string
	}
	tests := []struct {
		name    string
		args    args
		want    []*enachPb.EnachMandate
		wantErr error
	}{
		{
			name: "should fetch successfully if mandate exists",
			args: args{
				ctx: context.Background(),
				ids: []string{enachMandateFixture1.GetId()},
			},
			want: []*enachPb.EnachMandate{enachMandateFixture1},
		},
		{
			name: "should fetch mandates across different ownerships",
			args: args{
				ctx: context.Background(),
				ids: []string{enachMandateFixture1.GetId(), sgEnachMandateFixture1.GetId(), epifiEnachMandateFixture1.GetId()},
			},
			want: []*enachPb.EnachMandate{enachMandateFixture1, sgEnachMandateFixture1, epifiEnachMandateFixture1},
		},
		{
			name: "should fail to fetch if mandate id list is empty",
			args: args{
				ctx: context.Background(),
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "should fail to fetch if mandate id list contains empty strings",
			args: args{
				ctx: context.Background(),
				ids: []string{""},
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "should fail to fetch if all mandate ids are incorrect",
			args: args{
				ctx: context.Background(),
				ids: []string{"random-enach-mandate-id"},
			},
			wantErr: epifierrors.ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			enachMandateTs, release := initialiseEnachMandateTestSuite(t)
			defer release()

			got, err := enachMandateTs.dao.GetByIds(tt.args.ctx, tt.args.ids)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetByIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !compareEnachMandates(got, tt.want) {
				t.Errorf("GetByIds() got = %v, want = %v", got, tt.want)
			}
		})
	}
}
func compareEnachMandates(actual, expected []*enachPb.EnachMandate) bool {
	if len(actual) != len(expected) {
		return false
	}

	for i := 0; i < len(actual); i++ {
		expected[i].CreatedAt = actual[i].CreatedAt
		expected[i].UpdatedAt = actual[i].UpdatedAt
		if !proto.Equal(actual[i], expected[i]) {
			return false
		}
	}

	return true
}

func TestEnachMandateDaoPgdb_GetByUmrn(t *testing.T) {
	type args struct {
		ctx    context.Context
		umrn   string
		fanOut bool
	}
	tests := []struct {
		name    string
		args    args
		want    *enachPb.EnachMandate
		wantErr error
	}{
		{
			name: "successfully fetch for given umrn for EPIFI_TECH ownership",
			args: args{
				ctx:    context.Background(),
				umrn:   enachMandateFixture1.GetUmrn(),
				fanOut: true,
			},
			want: enachMandateFixture1,
		},
		{
			name: "successfully fetch for given umrn from STOCK_GUARDIAN_TSP ownership",
			args: args{
				ctx:    context.Background(),
				umrn:   sgEnachMandateFixture1.GetUmrn(),
				fanOut: true,
			},
			want: sgEnachMandateFixture1,
		},
		{
			name: "should not fan out when fanOut is false and use the ownership from context which is STOCK_GUARDIAN_TSP",
			args: args{
				ctx:    ctxWithSgOwnership,
				umrn:   sgEnachMandateFixture1.GetUmrn(),
				fanOut: false,
			},
			want: sgEnachMandateFixture1,
		},
		{
			name: "should not fan out when fanOut is false and use the ownership from context which is EPIFI_TECH",
			args: args{
				ctx:    context.Background(),
				umrn:   sgEnachMandateFixture1.GetUmrn(),
				fanOut: false,
			},
			wantErr: epifierrors.ErrRecordNotFound,
		},
		{
			name: "failed to fetch (mandatory param umrn is empty)",
			args: args{
				ctx:    context.Background(),
				fanOut: true,
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "no record found for the umrn",
			args: args{
				ctx:    context.Background(),
				umrn:   "random-umrn",
				fanOut: true,
			},
			wantErr: epifierrors.ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			enachMandateTs, release := initialiseEnachMandateTestSuite(t)
			defer release()

			got, err := enachMandateTs.dao.GetByUmrn(tt.args.ctx, tt.args.umrn, tt.args.fanOut)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetByUmrn() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !compareEnachMandate(got, tt.want) {
				t.Errorf("GetByUmrn() got = %v, want = %v", got, tt.want)
			}
		})
	}
}

func TestEnachMandateDaoPgdb_GetByRecurringPaymentId(t *testing.T) {
	type args struct {
		ctx                context.Context
		recurringPaymentId string
		fanOut             bool
	}
	tests := []struct {
		name    string
		args    args
		want    *enachPb.EnachMandate
		wantErr error
	}{
		{
			name: "successfully fetched for given recurring payment id",
			args: args{
				ctx:                context.Background(),
				recurringPaymentId: enachMandateFixture1.GetRecurringPaymentId(),
				fanOut:             true,
			},
			want: enachMandateFixture1,
		},
		{
			name: "failed to fetch (mandatory param recurring payment id is empty)",
			args: args{
				ctx:    context.Background(),
				fanOut: true,
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "no record found for the given recurring payment id",
			args: args{
				ctx:                context.Background(),
				recurringPaymentId: "random-recurring-payment-id",
				fanOut:             true,
			},
			wantErr: epifierrors.ErrRecordNotFound,
		},
		{
			name: "should not fan out when fanOut is false and use the ownership from context which is STOCK_GUARDIAN_TSP",
			args: args{
				ctx:                ctxWithSgOwnership,
				recurringPaymentId: sgEnachMandateFixture1.GetRecurringPaymentId(),
				fanOut:             false,
			},
			want: sgEnachMandateFixture1,
		},
		{
			name: "should not fan out when fanOut is false and use the ownership from context which is EPIFI_TECH",
			args: args{
				ctx:                context.Background(),
				recurringPaymentId: sgEnachMandateFixture1.GetRecurringPaymentId(),
				fanOut:             false,
			},
			wantErr: epifierrors.ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			enachMandateTs, release := initialiseEnachMandateTestSuite(t)
			defer release()

			got, err := enachMandateTs.dao.GetByRecurringPaymentId(tt.args.ctx, tt.args.recurringPaymentId, tt.args.fanOut)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetByRecurringPaymentId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !compareEnachMandate(got, tt.want) {
				t.Errorf("GetByRecurringPaymentId() got = %v, want = %v", got, tt.want)
			}
		})
	}
}

func compareEnachMandate(actual, expected *enachPb.EnachMandate) bool {
	if actual != nil && expected != nil {
		expected.CreatedAt = actual.CreatedAt
		expected.UpdatedAt = actual.UpdatedAt
	}
	return proto.Equal(actual, expected)
}
