package dao

import (
	"context"
	"flag"
	"log"
	"os"
	"testing"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/cfg"
	serverCfg "github.com/epifi/be-common/pkg/cmd/config"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"
	testPkg "github.com/epifi/be-common/pkg/test/v2"
	"gorm.io/gorm"

	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	rpServerConfig "github.com/epifi/gamma/recurringpayment/config/server"
	"github.com/epifi/gamma/recurringpayment/test"
)

var (
	enachMandateFixture1 = &enachPb.EnachMandate{
		Id:                     "enach-mandate-id-1",
		Umrn:                   "umrn-1",
		RecurringPaymentId:     "recurring-payment-id-1",
		RegistrationAuthMode:   enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
		RegistrationProvenance: enachEnumsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
	}
	epifiEnachMandateFixture1 = &enachPb.EnachMandate{
		Id:                     "ENACHMAN8cY4emK2Z4R8241227_5feFb8zhrk",
		Umrn:                   "umrn-4",
		RecurringPaymentId:     "recurring-payment-id-4",
		RegistrationAuthMode:   enachEnumsPb.EnachRegistrationAuthMode_ENACH_REGISTRATION_AUTH_MODE_UNSPECIFIED,
		RegistrationProvenance: enachEnumsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
	}
	sgEnachMandateFixture1 = &enachPb.EnachMandate{
		Id:                     "ENACHMANByTeZrA5mL2Q241227_MbZRPgHhafW",
		Umrn:                   "umrn-5",
		RecurringPaymentId:     "recurring-payment-id-5",
		RegistrationAuthMode:   enachEnumsPb.EnachRegistrationAuthMode_ENACH_REGISTRATION_AUTH_MODE_UNSPECIFIED,
		RegistrationProvenance: enachEnumsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
	}

	enachMandateActionFixture1 = &enachPb.EnachMandateAction{
		Id:                   "43284dbf-3deb-4bdd-a8b8-3eb13d527e51",
		ClientRequestId:      "client-request-id-1",
		EnachMandateId:       "enach-mandate-id-1",
		ActionType:           enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE,
		ActionStatus:         enachEnumsPb.EnachActionStatus_ACTION_STATUS_CREATED,
		ActionMetadata:       &enachPb.ActionMetadata{ActionTypeSpecificMetadata: nil},
		ActionDetailedStatus: &enachPb.ActionDetailedStatus{ActionTypeSpecificMetadata: nil},
		VendorRequestId:      "vendor-req-id-1",
		VendorBatchRequestId: "vendor-batch-req-id-1",
	}
	enachMandateActionFixture2 = &enachPb.EnachMandateAction{
		Id:                   "43284dbf-3deb-4bdd-a8b8-3eb13d527e61",
		ClientRequestId:      "client-request-id-3",
		EnachMandateId:       "enach-mandate-id-3",
		ActionType:           enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE,
		ActionStatus:         enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS,
		ActionMetadata:       &enachPb.ActionMetadata{ActionTypeSpecificMetadata: nil},
		ActionDetailedStatus: &enachPb.ActionDetailedStatus{ActionTypeSpecificMetadata: nil},
		VendorRequestId:      "vendor-req-id-3",
		VendorBatchRequestId: "vendor-batch-req-id-3",
	}
	enachMandateActionFixture3 = &enachPb.EnachMandateAction{
		Id:                   "43284dbf-3deb-4bdd-a8b8-3eb13d527e63",
		ClientRequestId:      "client-request-id-5",
		EnachMandateId:       "enach-mandate-id-3",
		ActionType:           enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE,
		ActionStatus:         enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS,
		ActionMetadata:       &enachPb.ActionMetadata{ActionTypeSpecificMetadata: nil},
		ActionDetailedStatus: &enachPb.ActionDetailedStatus{ActionTypeSpecificMetadata: nil},
		VendorRequestId:      "vendor-req-id-5",
		VendorBatchRequestId: "vendor-batch-req-id-5",
	}
	enachMandateActionFixtureList1 = []*enachPb.EnachMandateAction{enachMandateActionFixture1}
	enachMandateActionFixtureList2 = []*enachPb.EnachMandateAction{enachMandateActionFixture3, enachMandateActionFixture2}

	dbResourceProviderPool testPkg.DbResourceProviderInstancePool
	cleanup                func()
)

var (
	db          *gorm.DB
	conf        *rpServerConfig.Config
	enachTables = []string{"enach_mandates", "enach_mandates_actions"}

	entitySegregatedTestTables = []string{"enach_mandates"}

	entitySegregatedTestTablesMap = map[commontypes.Ownership]map[commontypes.UseCase][]string{
		commontypes.Ownership_EPIFI_TECH: {
			commontypes.UseCase_USE_CASE_ENACH_MANDATE: entitySegregatedTestTables,
		},
		commontypes.Ownership_STOCK_GUARDIAN_TSP: {
			commontypes.UseCase_USE_CASE_ENACH_MANDATE: entitySegregatedTestTables,
		},
	}
	ctxWithSgOwnership = epificontext.WithOwnership(context.Background(), commontypes.Ownership_STOCK_GUARDIAN_TSP)
)

// nolint:dogsled
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, conf, _, db, teardown = test.InitTestServerForPGDB()

	dbResourceProviderPool, cleanup = InitConfigAndDBResourceProviderInstancePool()
	defer cleanup()

	// init enachMandateAction dao test suite
	enachMandateActionDao := NewEnachMandateActionDaoPgdb(db)
	enachMandateActionTs = newEnachMandateActionTestSuite(db, enachMandateActionDao, conf)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func InitConfigAndDBResourceProviderInstancePool() (testPkg.DbResourceProviderInstancePool, func()) {
	// Init config
	serverConf, err := serverCfg.Load(cfg.ORDER_SERVER)
	if err != nil {
		log.Fatal("failed to load config", err)
	}
	// Setup logger
	logger.Init(serverConf.Environment)
	ownershipToDbConfigMap := serverConf.UseCaseDBConfigMap

	// Currently set pool size to 1 since we don't support concurrency for DBResourceProviderInstancePool.
	dbResourceProviderInstancePool := testPkg.NewDbResourceProviderInstancePoolWithUseCase(testPkg.NewZapLogger(logger.Log), ownershipToDbConfigMap, 1)
	// Initializing dbUtilsProvider to get db connections and txn executors
	return dbResourceProviderInstancePool, func() {
		dbResourceProviderInstancePool.Cleanup(testPkg.NewZapLogger(logger.Log))
	}
}
