package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/errgroup"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/pkg/storage/v2/usecase"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	types2 "github.com/epifi/gamma/order/wire/types"
	"github.com/epifi/gamma/recurringpayment/enach/dao/model"
)

type EnachMandateDaoPgdb struct {
	db                 *gorm.DB
	idGen              idgen.IdGenerator
	attrIdGen          idgen.AttributedIdGen[*EnachMandatesAttributes]
	dbResourceProvider *usecase.DBResourceProvider[*gorm.DB]
	// true: db instance is going to be fetched based on ownership which will be fetched from context
	// false: db instance will be the default epifi CRDB
	useDbResourceProvider types2.EnableResourceProvider
}

func NewEnachMandateDaoPgdb(db types.RecurringPaymentPGDB, idGen idgen.IdGenerator, dbResourceProvider *usecase.DBResourceProvider[*gorm.DB], useDbResourceProvider types2.EnableResourceProvider, attrIdGen idgen.AttributedIdGen[*EnachMandatesAttributes]) *EnachMandateDaoPgdb {
	return &EnachMandateDaoPgdb{db: db, idGen: idGen, dbResourceProvider: dbResourceProvider, useDbResourceProvider: useDbResourceProvider, attrIdGen: attrIdGen}
}

// compile time check to ensure EnachMandateDaoPgdb implements EnachMandateDao
var _ EnachMandateDao = &EnachMandateDaoPgdb{}

var mandateFieldMaskToColumName = map[enachEnumsPb.EnachMandateFieldMask]string{
	enachEnumsPb.EnachMandateFieldMask_ENACH_MANDATE_FIELD_MASK_UMRN: "umrn",
}

// nolint: dupl
func (e *EnachMandateDaoPgdb) Create(ctx context.Context, enachMandate *enachPb.EnachMandate, ownership commontypes.Ownership) (*enachPb.EnachMandate, error) {
	defer metric_util.TrackDuration("recurringpayment/enach/dao", "EnachMandateDaoPgdb", "Create", time.Now())
	if enachMandate.GetRecurringPaymentId() == "" {
		return nil, fmt.Errorf("recurring paymnent id cannot be empty %w", epifierrors.ErrInvalidArgument)
	}

	ctx = epificontext.WithOwnership(ctx, ownership)

	db, resolveErr := getConnFromContextOrProviderWithDefaultHandling(ctx, e.dbResourceProvider, e.db, e.useDbResourceProvider)
	if resolveErr != nil {
		return nil, fmt.Errorf("error [%w] while resolving db connection", resolveErr)
	}

	id, err := getGeneratedId(ctx, idgen.EnachMandate, e.idGen, e.attrIdGen, e.useDbResourceProvider)
	if err != nil {
		return nil, fmt.Errorf("enach mandate id generation failed %w", err)
	}
	mandateDbModel := model.NewEnachMandate(id, enachMandate)
	if err := db.Create(mandateDbModel).Error; err != nil {
		return nil, fmt.Errorf("failed to create enach mandate in db %w", err)
	}
	return mandateDbModel.GetProto(), nil
}

// nolint: dupl
func (e *EnachMandateDaoPgdb) Update(ctx context.Context, updateMask []enachEnumsPb.EnachMandateFieldMask, mandate *enachPb.EnachMandate) error {
	defer metric_util.TrackDuration("recurringpayment/enach/dao", "EnachMandateDaoPgdb", "Update", time.Now())
	if mandate.GetId() == "" {
		return fmt.Errorf("id cannot be empty for update query, err : %w", epifierrors.ErrInvalidArgument)
	}
	updateColumns := getColumnNamesFromMandateFieldMask(updateMask)
	if len(updateColumns) == 0 {
		return fmt.Errorf("no columns to update, err : %w", epifierrors.ErrInvalidArgument)
	}

	db, resolveErr := getConnFromContextOrProviderWithDefaultHandling(ctx, e.dbResourceProvider, e.db, e.useDbResourceProvider)
	if resolveErr != nil {
		return fmt.Errorf("error [%w] while resolving db connection", resolveErr)
	}

	mandateDbModel := model.ConvertToEnachMandateUpdateDbModel(mandate)
	res := db.Model(mandateDbModel).Where("id = ?", mandateDbModel.Id).Select(updateColumns).Updates(mandateDbModel)
	if res.Error != nil {
		return fmt.Errorf("error updating enach mandate entry, err : %w", res.Error)
	}
	if res.RowsAffected == 0 {
		return epifierrors.ErrRowNotUpdated
	}

	return nil
}

// nolint: dupl
func (e *EnachMandateDaoPgdb) GetById(ctx context.Context, id string) (*enachPb.EnachMandate, error) {
	defer metric_util.TrackDuration("recurringpayment/enach/dao", "EnachMandateDaoPgdb", "GetById", time.Now())
	if id == "" {
		return nil, fmt.Errorf("id cannot be empty, err :  %w", epifierrors.ErrInvalidArgument)
	}

	ctx = getCtxWithOwnershipFromAttribute(ctx, idgen.EnachMandate, e.attrIdGen, id)

	db, resolveErr := getConnFromContextOrProviderWithDefaultHandling(ctx, e.dbResourceProvider, e.db, e.useDbResourceProvider)
	if resolveErr != nil {
		return nil, fmt.Errorf("error [%w] while resolving db connection", resolveErr)
	}

	mandateDbModel := &model.EnachMandate{}

	res := db.Where("id = ?", id).Take(mandateDbModel)
	if errors.Is(res.Error, gorm.ErrRecordNotFound) {
		return nil, epifierrors.ErrRecordNotFound
	}
	if res.Error != nil {
		return nil, fmt.Errorf("failed to fetch enach mandate by id, err : %w", res.Error)
	}

	return mandateDbModel.GetProto(), nil
}

// GetByIds fetches DB records belonging to given enach mandate ids. Returns error if record not found.
// nolint dupl
func (e *EnachMandateDaoPgdb) GetByIds(ctx context.Context, mandateIds []string) ([]*enachPb.EnachMandate, error) {
	defer metric_util.TrackDuration("recurringpayment/enach/dao", "EnachMandateDaoPgdb", "GetByIds", time.Now())

	if len(mandateIds) == 0 {
		return nil, fmt.Errorf("list of mandateIds cannot be empty %w", epifierrors.ErrInvalidArgument)
	}

	ownershipToIdsMap := make(map[commontypes.Ownership][]string)
	for _, mandateId := range mandateIds {
		if mandateId == "" {
			return nil, fmt.Errorf("mandateId cannot be empty %w", epifierrors.ErrInvalidArgument)
		}
		ow := getOwnershipFromAttribute(idgen.EnachMandate, e.attrIdGen, mandateId)
		ownershipToIdsMap[ow] = append(ownershipToIdsMap[ow], mandateId)
	}

	entitiesChan := make(chan []*model.EnachMandate, len(ownershipToIdsMap))

	grp, grpCtx := errgroup.WithContext(ctx)
	for ownership, ids := range ownershipToIdsMap {
		grp.Go(func() error {
			enachMandates, err := e.getMandatesByIdsForOwnership(epificontext.WithOwnership(grpCtx, ownership), ids)
			switch {
			case errors.Is(err, epifierrors.ErrRecordNotFound):
				return nil
			case err != nil:
				return fmt.Errorf("error in fetching ENACH mandates for ownership %s : %w", ownership.String(), err)
			default:
				entitiesChan <- enachMandates
				return nil
			}
		})
	}
	if err := grp.Wait(); err != nil {
		return nil, fmt.Errorf("error in fetching ENACH mandates: %w", err)
	}

	close(entitiesChan)

	fetchedEnachMandatesMap := make(map[string]*model.EnachMandate)
	for enachMandates := range entitiesChan {
		for _, enachMandate := range enachMandates {
			fetchedEnachMandatesMap[enachMandate.Id] = enachMandate
		}
	}
	if len(fetchedEnachMandatesMap) == 0 {
		return nil, fmt.Errorf("failed to fetch by enachMandateIds: %v: %w", mandateIds, epifierrors.ErrRecordNotFound)
	}

	resEnachMandates := make([]*model.EnachMandate, 0)
	for _, id := range mandateIds {
		resEnachMandates = append(resEnachMandates, fetchedEnachMandatesMap[id])
	}
	return convertToMandatesProto(resEnachMandates), nil
}

func (e *EnachMandateDaoPgdb) getMandatesByIdsForOwnership(ctx context.Context, ids []string) ([]*model.EnachMandate, error) {
	db, resolveErr := getConnFromContextOrProviderWithDefaultHandling(ctx, e.dbResourceProvider, e.db, e.useDbResourceProvider)
	if resolveErr != nil {
		return nil, fmt.Errorf("error [%w] while resolving db connection", resolveErr)
	}
	var mandateDbModels []*model.EnachMandate
	if err := db.Where("id IN (?)", ids).Find(&mandateDbModels).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch record for enach Mandate by mandateIds: %v: %w", ids, err)
	}
	if len(mandateDbModels) == 0 {
		return nil, fmt.Errorf("failed to fetch record for enach Mandate by mandateIds: %v: %w", ids, epifierrors.ErrRecordNotFound)
	}
	return mandateDbModels, nil
}

func convertToMandatesProto(mandateModels []*model.EnachMandate) []*enachPb.EnachMandate {
	var mandates = make([]*enachPb.EnachMandate, len(mandateModels))
	for i, mandateModel := range mandateModels {
		mandates[i] = mandateModel.GetProto()
	}
	return mandates
}

// GetByUmrn fetches the EnachMandate entity based on the UMRN, by querying DBs across multiple ownerships. For cases,
// when fanOut is false it uses the DB corresponding to the ownership passed in the context otherwise it
// simply queries all the DBs.
// nolint:dupl
func (e *EnachMandateDaoPgdb) GetByUmrn(ctx context.Context, umrn string, fanOut bool) (*enachPb.EnachMandate, error) {
	defer metric_util.TrackDuration("recurringpayment/enach/dao", "EnachMandateDaoPgdb", "GetByUmrn", time.Now())
	if umrn == "" {
		return nil, fmt.Errorf("umrn cannot be empty %w", epifierrors.ErrInvalidArgument)
	}

	queryFunc := func(db *gorm.DB) ([]*model.EnachMandate, error) {
		modelEnachMandate := &model.EnachMandate{}
		if err := db.Where("umrn = ?", umrn).Take(modelEnachMandate).Error; err != nil {
			return nil, fmt.Errorf("failed to fetch enach mandate by umrn, error: %w", err)
		}
		return []*model.EnachMandate{modelEnachMandate}, nil
	}

	var mdlRes []*model.EnachMandate
	var err error

	if !fanOut || !bool(e.useDbResourceProvider) {
		// When the caller doesn't pass fanOut then, use the DB connection
		// corresponding to the ownership passed in context.
		dbWithOwnership, resolveErr := getConnFromContextOrProviderWithDefaultHandling(ctx, e.dbResourceProvider, e.db, e.useDbResourceProvider)
		if resolveErr != nil {
			return nil, fmt.Errorf("error while resolving db connection, err: %w", resolveErr)
		}
		mdlRes, err = queryFunc(dbWithOwnership)
	} else {
		mdlRes, err = getMultiDbResponse[*model.EnachMandate](ctx, e.dbResourceProvider, queryFunc)
	}

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch enach mandate by umrn %w", err)
	} else if len(mdlRes) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return mdlRes[0].GetProto(), nil
}

// GetByRecurringPaymentId fetches the enach mandate for the given recurring payment id, by querying DBs across multiple ownerships.
// For cases, when fanOut is false it uses the DB corresponding to the ownership passed in the context otherwise it
// simply queries all the DBs.
// nolint:dupl
func (e *EnachMandateDaoPgdb) GetByRecurringPaymentId(ctx context.Context, recurringPaymentId string, fanOut bool) (*enachPb.EnachMandate, error) {
	defer metric_util.TrackDuration("recurringpayment/enach/dao", "EnachMandateDaoPgdb", "GetByRecurringPaymentId", time.Now())
	if recurringPaymentId == "" {
		return nil, fmt.Errorf("recurringPaymentId cannot be empty %w", epifierrors.ErrInvalidArgument)
	}

	queryFunc := func(db *gorm.DB) ([]*model.EnachMandate, error) {
		modelEnachMandate := &model.EnachMandate{}
		if err := db.Where("recurring_payment_id = ?", recurringPaymentId).Take(modelEnachMandate).Error; err != nil {
			return nil, fmt.Errorf("failed to fetch enach mandate by recurring_payment_id: %w", err)
		}
		return []*model.EnachMandate{modelEnachMandate}, nil
	}

	var mdlRes []*model.EnachMandate
	var err error
	if !fanOut || !bool(e.useDbResourceProvider) {
		// When the caller doesn't pass fanOut then, use the DB connection
		// corresponding to the ownership passed in context.
		dbWithOwnership, resolveErr := getConnFromContextOrProviderWithDefaultHandling(ctx, e.dbResourceProvider, e.db, e.useDbResourceProvider)
		if resolveErr != nil {
			return nil, fmt.Errorf("error while resolving db connection, err: %w", resolveErr)
		}
		mdlRes, err = queryFunc(dbWithOwnership)
	} else {
		mdlRes, err = getMultiDbResponse[*model.EnachMandate](ctx, e.dbResourceProvider, queryFunc)
	}

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch enach mandate by umrn %w", err)
	} else if len(mdlRes) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	return mdlRes[0].GetProto(), nil
}

func getColumnNamesFromMandateFieldMask(fieldMask []enachEnumsPb.EnachMandateFieldMask) []string {
	var columns []string
	for _, field := range fieldMask {
		columns = append(columns, mandateFieldMaskToColumName[field])
	}
	return columns
}

func getConnFromContextOrProviderWithDefaultHandling(ctx context.Context, dbResourceProvider *usecase.DBResourceProvider[*gorm.DB], defaultDb *gorm.DB, useDbResourceProvider types2.EnableResourceProvider) (*gorm.DB, error) {
	if !useDbResourceProvider {
		return gormctxv2.FromContextOrDefault(ctx, defaultDb), nil
	}
	// if we are not able to fetch ownership from context, the ownership will default to EPIFI_TECH
	ow := epificontext.OwnershipFromContext[context.Context](ctx)
	// hard-code use case here since, the ENACH Mandate DB is based on a single use-case.
	dbConn, err := dbResourceProvider.GetResource(ow, commontypes.UseCase_USE_CASE_ENACH_MANDATE)
	if err != nil {
		return nil, fmt.Errorf("error in fetching db from ownership :%s & %s : %w", ow, commontypes.UseCase_USE_CASE_ENACH_MANDATE, err)
	}
	return gormctxv2.FromContextOrDefault(ctx, dbConn), nil
}

func getGeneratedId(ctx context.Context, d idgen.Domain, idgen idgen.IdGenerator, attributedIdGen idgen.AttributedIdGen[*EnachMandatesAttributes], useDbResourceProvider types2.EnableResourceProvider) (string, error) {
	if !useDbResourceProvider {
		// If there is no use of db res provider/entity segregation, then there is no need of creating embedded ids
		return idgen.Get(d)
	}
	ownership := epificontext.OwnershipFromContext(ctx)
	return attributedIdGen.GetIdWithAttribute(d, &EnachMandatesAttributes{
		Ownership: int(ownership),
	})
}

func getOwnershipFromAttribute(domain idgen.Domain, attributedIdGen idgen.AttributedIdGen[*EnachMandatesAttributes], embeddedId string) commontypes.Ownership {
	attribute := attributedIdGen.GetAttributeFromId(domain, embeddedId)
	if attribute == nil {
		// Return the default ownership if no attribute is embedded in the id
		return commontypes.Ownership_EPIFI_TECH
	}
	return commontypes.Ownership(attribute.Ownership)
}

func getCtxWithOwnershipFromAttribute(ctx context.Context, domain idgen.Domain, attributedIdGen idgen.AttributedIdGen[*EnachMandatesAttributes], embeddedId string) context.Context {
	attribute := attributedIdGen.GetAttributeFromId(domain, embeddedId)
	if attribute == nil {
		return ctx
	}
	return epificontext.WithOwnership(ctx, commontypes.Ownership(attribute.Ownership))
}

func getUniqueDbsFromDbResourceProvider(dbResProvider *usecase.DBResourceProvider[*gorm.DB]) []*gorm.DB {
	uniqueDbs := make([]*gorm.DB, 0)
	for _, useCaseToDbMap := range dbResProvider.DBOwnershipMap {
		// This assumes that the DBs across all ownerships for
		// USE_CASE_ENACH_MANDATE must be unique in the given config
		dbForUseCase, ok := useCaseToDbMap[commontypes.UseCase_USE_CASE_ENACH_MANDATE]
		if !ok {
			continue
		}
		uniqueDbs = append(uniqueDbs, dbForUseCase)
	}
	return uniqueDbs
}

// getMultiDbResponse aggregates the response for a particular query function from multiple databases. This is generic in nature
// and can be used to aggregate any type of model data
func getMultiDbResponse[T any](ctx context.Context, dbResProvider *usecase.DBResourceProvider[*gorm.DB], queryFunc func(db *gorm.DB) ([]T, error)) ([]T, error) {
	uniqueDbs := getUniqueDbsFromDbResourceProvider(dbResProvider)

	collectedData := make([]T, 0)
	collectedDataChan := make(chan []T, len(uniqueDbs))
	grp, _ := errgroup.WithContext(ctx)
	for _, db := range uniqueDbs {
		grp.Go(func() error {
			res, err := queryFunc(db)
			switch {
			case storageV2.IsRecordNotFoundError(err):
				return nil
			case err != nil:
				return fmt.Errorf("failed to fetch record, error: %w", err)
			default:
				collectedDataChan <- res
				return nil
			}
		})
	}
	if err := grp.Wait(); err != nil {
		return nil, fmt.Errorf("failed to fetch record from one of the DBs: %w", err)
	}
	close(collectedDataChan)
	for data := range collectedDataChan {
		collectedData = append(collectedData, data...)
	}
	return collectedData, nil
}
