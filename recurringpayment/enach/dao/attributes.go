package dao

import (
	"encoding/json"
	"fmt"

	"github.com/btcsuite/btcutil/base58"
	"github.com/jonboulle/clockwork"

	"github.com/epifi/be-common/pkg/idgen"
)

type EnachMandatesAttributes struct {
	Ownership int `json:"o"`
}

func (r *EnachMandatesAttributes) ToString() (string, error) {
	if r == nil {
		return "", nil
	}
	jsonAttr, err := json.Marshal(r)
	if err != nil {
		return "", fmt.Errorf("error marshalling recurring payment attributes: %w", err)
	}
	return base58.Encode(jsonAttr), nil
}

func (r *EnachMandatesAttributes) FromString(s string) idgen.Attribute {
	var attr EnachMandatesAttributes
	err := json.Unmarshal(base58.Decode(s), &attr)
	if err != nil {
		return nil
	}
	return &attr
}

func ProvideAttributedIdGen(clk clockwork.Clock) idgen.AttributedIdGen[*EnachMandatesAttributes] {
	return idgen.NewAttributedIdGen[*EnachMandatesAttributes](clk)
}

var _ idgen.Attribute = &EnachMandatesAttributes{}
