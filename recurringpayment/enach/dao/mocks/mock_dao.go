// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/go/src/github.com/epifi/gamma/recurringpayment/enach/dao/dao.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	common "github.com/epifi/be-common/api/typesv2/common"
	enach "github.com/epifi/gamma/api/recurringpayment/enach"
	enums "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	dao "github.com/epifi/gamma/recurringpayment/enach/dao"
	gomock "github.com/golang/mock/gomock"
)

// MockEnachMandateActionDao is a mock of EnachMandateActionDao interface.
type MockEnachMandateActionDao struct {
	ctrl     *gomock.Controller
	recorder *MockEnachMandateActionDaoMockRecorder
}

// MockEnachMandateActionDaoMockRecorder is the mock recorder for MockEnachMandateActionDao.
type MockEnachMandateActionDaoMockRecorder struct {
	mock *MockEnachMandateActionDao
}

// NewMockEnachMandateActionDao creates a new mock instance.
func NewMockEnachMandateActionDao(ctrl *gomock.Controller) *MockEnachMandateActionDao {
	mock := &MockEnachMandateActionDao{ctrl: ctrl}
	mock.recorder = &MockEnachMandateActionDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEnachMandateActionDao) EXPECT() *MockEnachMandateActionDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockEnachMandateActionDao) Create(ctx context.Context, action *enach.EnachMandateAction) (*enach.EnachMandateAction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, action)
	ret0, _ := ret[0].(*enach.EnachMandateAction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockEnachMandateActionDaoMockRecorder) Create(ctx, action interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockEnachMandateActionDao)(nil).Create), ctx, action)
}

// GetByActionTypeAndEnachMandateId mocks base method.
func (m *MockEnachMandateActionDao) GetByActionTypeAndEnachMandateId(ctx context.Context, actionType enums.EnachActionType, mandateId string) ([]*enach.EnachMandateAction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActionTypeAndEnachMandateId", ctx, actionType, mandateId)
	ret0, _ := ret[0].([]*enach.EnachMandateAction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActionTypeAndEnachMandateId indicates an expected call of GetByActionTypeAndEnachMandateId.
func (mr *MockEnachMandateActionDaoMockRecorder) GetByActionTypeAndEnachMandateId(ctx, actionType, mandateId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActionTypeAndEnachMandateId", reflect.TypeOf((*MockEnachMandateActionDao)(nil).GetByActionTypeAndEnachMandateId), ctx, actionType, mandateId)
}

// GetByActionTypeAndVendorRequestId mocks base method.
func (m *MockEnachMandateActionDao) GetByActionTypeAndVendorRequestId(ctx context.Context, actionType enums.EnachActionType, vendorRequestId string) (*enach.EnachMandateAction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActionTypeAndVendorRequestId", ctx, actionType, vendorRequestId)
	ret0, _ := ret[0].(*enach.EnachMandateAction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActionTypeAndVendorRequestId indicates an expected call of GetByActionTypeAndVendorRequestId.
func (mr *MockEnachMandateActionDaoMockRecorder) GetByActionTypeAndVendorRequestId(ctx, actionType, vendorRequestId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActionTypeAndVendorRequestId", reflect.TypeOf((*MockEnachMandateActionDao)(nil).GetByActionTypeAndVendorRequestId), ctx, actionType, vendorRequestId)
}

// GetByClientRequestId mocks base method.
func (m *MockEnachMandateActionDao) GetByClientRequestId(ctx context.Context, clientRequestId string) (*enach.EnachMandateAction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByClientRequestId", ctx, clientRequestId)
	ret0, _ := ret[0].(*enach.EnachMandateAction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByClientRequestId indicates an expected call of GetByClientRequestId.
func (mr *MockEnachMandateActionDaoMockRecorder) GetByClientRequestId(ctx, clientRequestId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByClientRequestId", reflect.TypeOf((*MockEnachMandateActionDao)(nil).GetByClientRequestId), ctx, clientRequestId)
}

// GetByEnachMandateId mocks base method.
func (m *MockEnachMandateActionDao) GetByEnachMandateId(ctx context.Context, mandateId string, options ...dao.FilterOption) ([]*enach.EnachMandateAction, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, mandateId}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByEnachMandateId", varargs...)
	ret0, _ := ret[0].([]*enach.EnachMandateAction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByEnachMandateId indicates an expected call of GetByEnachMandateId.
func (mr *MockEnachMandateActionDaoMockRecorder) GetByEnachMandateId(ctx, mandateId interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, mandateId}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByEnachMandateId", reflect.TypeOf((*MockEnachMandateActionDao)(nil).GetByEnachMandateId), varargs...)
}

// GetById mocks base method.
func (m *MockEnachMandateActionDao) GetById(ctx context.Context, Id string) (*enach.EnachMandateAction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, Id)
	ret0, _ := ret[0].(*enach.EnachMandateAction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockEnachMandateActionDaoMockRecorder) GetById(ctx, Id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockEnachMandateActionDao)(nil).GetById), ctx, Id)
}

// GetByIds mocks base method.
func (m *MockEnachMandateActionDao) GetByIds(ctx context.Context, Id []string) ([]*enach.EnachMandateAction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByIds", ctx, Id)
	ret0, _ := ret[0].([]*enach.EnachMandateAction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByIds indicates an expected call of GetByIds.
func (mr *MockEnachMandateActionDaoMockRecorder) GetByIds(ctx, Id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByIds", reflect.TypeOf((*MockEnachMandateActionDao)(nil).GetByIds), ctx, Id)
}

// GetByVendorBatchRequestId mocks base method.
func (m *MockEnachMandateActionDao) GetByVendorBatchRequestId(ctx context.Context, Id string) ([]*enach.EnachMandateAction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByVendorBatchRequestId", ctx, Id)
	ret0, _ := ret[0].([]*enach.EnachMandateAction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByVendorBatchRequestId indicates an expected call of GetByVendorBatchRequestId.
func (mr *MockEnachMandateActionDaoMockRecorder) GetByVendorBatchRequestId(ctx, Id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByVendorBatchRequestId", reflect.TypeOf((*MockEnachMandateActionDao)(nil).GetByVendorBatchRequestId), ctx, Id)
}

// Update mocks base method.
func (m *MockEnachMandateActionDao) Update(ctx context.Context, updateMask []enums.EnachMandateActionFieldMask, action *enach.EnachMandateAction) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, updateMask, action)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockEnachMandateActionDaoMockRecorder) Update(ctx, updateMask, action interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockEnachMandateActionDao)(nil).Update), ctx, updateMask, action)
}

// UpdateWithStatusCheck mocks base method.
func (m *MockEnachMandateActionDao) UpdateWithStatusCheck(ctx context.Context, updateMask []enums.EnachMandateActionFieldMask, action *enach.EnachMandateAction, expectedCurrentStatus enums.EnachActionStatus, expectedCurrentSubStatus enums.EnachActionSubStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWithStatusCheck", ctx, updateMask, action, expectedCurrentStatus, expectedCurrentSubStatus)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWithStatusCheck indicates an expected call of UpdateWithStatusCheck.
func (mr *MockEnachMandateActionDaoMockRecorder) UpdateWithStatusCheck(ctx, updateMask, action, expectedCurrentStatus, expectedCurrentSubStatus interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWithStatusCheck", reflect.TypeOf((*MockEnachMandateActionDao)(nil).UpdateWithStatusCheck), ctx, updateMask, action, expectedCurrentStatus, expectedCurrentSubStatus)
}

// MockEnachMandateDao is a mock of EnachMandateDao interface.
type MockEnachMandateDao struct {
	ctrl     *gomock.Controller
	recorder *MockEnachMandateDaoMockRecorder
}

// MockEnachMandateDaoMockRecorder is the mock recorder for MockEnachMandateDao.
type MockEnachMandateDaoMockRecorder struct {
	mock *MockEnachMandateDao
}

// NewMockEnachMandateDao creates a new mock instance.
func NewMockEnachMandateDao(ctrl *gomock.Controller) *MockEnachMandateDao {
	mock := &MockEnachMandateDao{ctrl: ctrl}
	mock.recorder = &MockEnachMandateDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEnachMandateDao) EXPECT() *MockEnachMandateDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockEnachMandateDao) Create(ctx context.Context, enachMandate *enach.EnachMandate, ownership common.Ownership) (*enach.EnachMandate, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, enachMandate, ownership)
	ret0, _ := ret[0].(*enach.EnachMandate)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockEnachMandateDaoMockRecorder) Create(ctx, enachMandate, ownership interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockEnachMandateDao)(nil).Create), ctx, enachMandate, ownership)
}

// GetById mocks base method.
func (m *MockEnachMandateDao) GetById(ctx context.Context, Id string) (*enach.EnachMandate, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, Id)
	ret0, _ := ret[0].(*enach.EnachMandate)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockEnachMandateDaoMockRecorder) GetById(ctx, Id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockEnachMandateDao)(nil).GetById), ctx, Id)
}

// GetByIds mocks base method.
func (m *MockEnachMandateDao) GetByIds(ctx context.Context, Id []string) ([]*enach.EnachMandate, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByIds", ctx, Id)
	ret0, _ := ret[0].([]*enach.EnachMandate)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByIds indicates an expected call of GetByIds.
func (mr *MockEnachMandateDaoMockRecorder) GetByIds(ctx, Id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByIds", reflect.TypeOf((*MockEnachMandateDao)(nil).GetByIds), ctx, Id)
}

// GetByRecurringPaymentId mocks base method.
func (m *MockEnachMandateDao) GetByRecurringPaymentId(ctx context.Context, recurringPaymentId string, fanOut bool) (*enach.EnachMandate, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByRecurringPaymentId", ctx, recurringPaymentId, fanOut)
	ret0, _ := ret[0].(*enach.EnachMandate)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByRecurringPaymentId indicates an expected call of GetByRecurringPaymentId.
func (mr *MockEnachMandateDaoMockRecorder) GetByRecurringPaymentId(ctx, recurringPaymentId, fanOut interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByRecurringPaymentId", reflect.TypeOf((*MockEnachMandateDao)(nil).GetByRecurringPaymentId), ctx, recurringPaymentId, fanOut)
}

// GetByUmrn mocks base method.
func (m *MockEnachMandateDao) GetByUmrn(ctx context.Context, umrn string, fanOut bool) (*enach.EnachMandate, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByUmrn", ctx, umrn, fanOut)
	ret0, _ := ret[0].(*enach.EnachMandate)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByUmrn indicates an expected call of GetByUmrn.
func (mr *MockEnachMandateDaoMockRecorder) GetByUmrn(ctx, umrn, fanOut interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByUmrn", reflect.TypeOf((*MockEnachMandateDao)(nil).GetByUmrn), ctx, umrn, fanOut)
}

// Update mocks base method.
func (m *MockEnachMandateDao) Update(ctx context.Context, updateMask []enums.EnachMandateFieldMask, enachMandate *enach.EnachMandate) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, updateMask, enachMandate)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockEnachMandateDaoMockRecorder) Update(ctx, updateMask, enachMandate interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockEnachMandateDao)(nil).Update), ctx, updateMask, enachMandate)
}
