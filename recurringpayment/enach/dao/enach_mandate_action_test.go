package dao

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"google.golang.org/protobuf/proto"
	gormV2 "gorm.io/gorm"

	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	rpServerConfig "github.com/epifi/gamma/recurringpayment/config/server"
)

var enachMandateActionTs *EnachMandateActionTestSuite

type EnachMandateActionTestSuite struct {
	db     *gormV2.DB
	dao    EnachMandateActionDao
	config *rpServerConfig.Config
}

func newEnachMandateActionTestSuite(db *gormV2.DB, dao EnachMandateActionDao, config *rpServerConfig.Config) *EnachMandateActionTestSuite {
	return &EnachMandateActionTestSuite{
		db:     db,
		dao:    dao,
		config: config,
	}
}

func TestEnachMandateActionDaoPgdb_Create(t *testing.T) {
	type args struct {
		ctx    context.Context
		action *enachPb.EnachMandateAction
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "succesfully created the enach mandate action for the given request",
			args: args{
				ctx: context.Background(),
				action: &enachPb.EnachMandateAction{
					ClientRequestId: "client-request-id-test",
					EnachMandateId:  "enach-mandate-id-test",
					ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE,
					ActionStatus:    enachEnumsPb.EnachActionStatus_ACTION_STATUS_CREATED,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to create action (mandatory param enach mandate id is empty)",
			args: args{
				ctx: context.Background(),
				action: &enachPb.EnachMandateAction{
					ClientRequestId: "client-request-id-test",
					ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE,
					ActionStatus:    enachEnumsPb.EnachActionStatus_ACTION_STATUS_CREATED,
				},
			},
			wantErr: true,
		},
		{
			name: "failed to create action (mandatory param client request id is empty)",
			args: args{
				ctx: context.Background(),
				action: &enachPb.EnachMandateAction{
					EnachMandateId: "enach-mandate-id-test",
					ActionType:     enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE,
					ActionStatus:   enachEnumsPb.EnachActionStatus_ACTION_STATUS_CREATED,
				},
			},
			wantErr: true,
		},
		{
			name: "failed to create action (mandatory param action type is unspecified)",
			args: args{
				ctx: context.Background(),
				action: &enachPb.EnachMandateAction{
					ClientRequestId: "client-request-id-test",
					EnachMandateId:  "enach-mandate-id-test",
					ActionStatus:    enachEnumsPb.EnachActionStatus_ACTION_STATUS_CREATED,
				},
			},
			wantErr: true,
		},
		{
			name: "failed to create action (mandatory param action status is unspecified)",
			args: args{
				ctx: context.Background(),
				action: &enachPb.EnachMandateAction{
					ClientRequestId: "client-request-id-test",
					EnachMandateId:  "enach-mandate-id-test",
					ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE,
				},
			},
			wantErr: true,
		},
		{
			name: "failed to create action (already exists)",
			args: args{
				ctx: context.Background(),
				action: &enachPb.EnachMandateAction{
					ClientRequestId: enachMandateActionFixture1.GetClientRequestId(),
					EnachMandateId:  enachMandateActionFixture1.GetEnachMandateId(),
					ActionType:      enachMandateActionFixture1.GetActionType(),
					ActionStatus:    enachMandateActionFixture1.GetActionStatus(),
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, enachMandateActionTs.db, enachMandateActionTs.config.RecurringPaymentDb.GetName(), enachTables)
			got, err := enachMandateActionTs.dao.Create(tt.args.ctx, tt.args.action)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			// if error did not occur then id should have been returned
			if tt.wantErr == false {
				if got.Id == "" {
					t.Errorf("Create() success but generated id is nil")
					return
				}
			}
		})
	}
}

func TestEnachMandateActionDaoPgdb_GetByActionTypeAndEnachMandateId(t *testing.T) {
	type args struct {
		ctx        context.Context
		actionType enachEnumsPb.EnachActionType
		mandateId  string
	}
	tests := []struct {
		name    string
		args    args
		want    []*enachPb.EnachMandateAction
		wantErr error
	}{
		{
			name: "succesfully fetched enach mandate action",
			args: args{
				ctx:        context.Background(),
				actionType: enachMandateActionFixture1.GetActionType(),
				mandateId:  enachMandateActionFixture1.GetEnachMandateId(),
			},
			want:    []*enachPb.EnachMandateAction{enachMandateActionFixture1},
			wantErr: nil,
		},
		{
			name: "record not found",
			args: args{
				ctx:        context.Background(),
				actionType: enachEnumsPb.EnachActionType_ACTION_TYPE_PAUSE,
				mandateId:  "enach-mandate-id-1",
			},
			wantErr: epifierrors.ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, enachMandateActionTs.db, enachMandateActionTs.config.RecurringPaymentDb.GetName(), enachTables)
			got, err := enachMandateActionTs.dao.GetByActionTypeAndEnachMandateId(tt.args.ctx, tt.args.actionType, tt.args.mandateId)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetByActionTypeAndEnachMandateId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !compareEnachMandateActionList(got, tt.want) {
				t.Errorf("GetByActionTypeAndEnachMandateId() got = %v, want = %v", got, tt.want)
				return
			}
		})
	}
}
func TestEnachMandateActionDaoPgdb_GetByClientRequestId(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx             context.Context
		clientRequestId string
	}
	tests := []struct {
		name    string
		args    args
		want    *enachPb.EnachMandateAction
		wantErr error
	}{
		{
			name: "succesfully fetched enach mandate action",
			args: args{
				ctx:             context.Background(),
				clientRequestId: enachMandateActionFixture1.GetClientRequestId(),
			},
			want:    enachMandateActionFixture1,
			wantErr: nil,
		},
		{
			name: "record not found",
			args: args{
				ctx:             context.Background(),
				clientRequestId: "random-client-request-id",
			},
			wantErr: epifierrors.ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, enachMandateActionTs.db, enachMandateActionTs.config.RecurringPaymentDb.GetName(), enachTables)
			got, err := enachMandateActionTs.dao.GetByClientRequestId(tt.args.ctx, tt.args.clientRequestId)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetByActionTypeAndEnachMandateId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !compareEnachMandateAction(got, tt.want) {
				t.Errorf("GetByActionTypeAndEnachMandateId() got = %v, want = %v", got, tt.want)
				return
			}
		})
	}
}

func TestEnachMandateActionDaoPgdb_GetByActionTypeAndVendorRequestId(t *testing.T) {
	type args struct {
		ctx         context.Context
		actionType  enachEnumsPb.EnachActionType
		vendorReqId string
	}
	tests := []struct {
		name    string
		args    args
		want    *enachPb.EnachMandateAction
		wantErr error
	}{
		{
			name: "succesfully fetched enach mandate action",
			args: args{
				ctx:         context.Background(),
				actionType:  enachMandateActionFixture1.GetActionType(),
				vendorReqId: enachMandateActionFixture1.GetVendorRequestId(),
			},
			want:    enachMandateActionFixture1,
			wantErr: nil,
		},
		{
			name: "record not found",
			args: args{
				ctx:         context.Background(),
				vendorReqId: "random-vendor-request-id",
			},
			wantErr: epifierrors.ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, enachMandateActionTs.db, enachMandateActionTs.config.RecurringPaymentDb.GetName(), enachTables)
			got, err := enachMandateActionTs.dao.GetByActionTypeAndVendorRequestId(tt.args.ctx, tt.args.actionType, tt.args.vendorReqId)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetByActionTypeAndEnachMandateId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !compareEnachMandateAction(got, tt.want) {
				t.Errorf("GetByActionTypeAndEnachMandateId() got = %v, want = %v", got, tt.want)
				return
			}
		})
	}
}

func TestEnachMandateActionDaoPgdb_Update(t *testing.T) {
	type args struct {
		ctx        context.Context
		updateMask []enachEnumsPb.EnachMandateActionFieldMask
		action     *enachPb.EnachMandateAction
	}
	tests := []struct {
		name    string
		args    args
		wantErr error
	}{
		{
			name: "should successfully update the action status",
			args: args{
				ctx:        context.Background(),
				updateMask: []enachEnumsPb.EnachMandateActionFieldMask{enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_STATUS},
				action: &enachPb.EnachMandateAction{
					Id:           enachMandateActionFixture1.GetId(),
					ActionStatus: enachEnumsPb.EnachActionStatus_ACTION_STATUS_SUCCESS,
				},
			},
		},
		{
			name: "failed to update (empty field mask)",
			args: args{
				ctx: context.Background(),
				action: &enachPb.EnachMandateAction{
					Id:           uuid.NewString(),
					ActionStatus: enachEnumsPb.EnachActionStatus_ACTION_STATUS_SUCCESS,
				},
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "failed to update (mandatory field id is empty)",
			args: args{
				ctx:        context.Background(),
				updateMask: []enachEnumsPb.EnachMandateActionFieldMask{enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_STATUS},
				action: &enachPb.EnachMandateAction{
					ActionStatus: enachEnumsPb.EnachActionStatus_ACTION_STATUS_SUCCESS,
				},
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "failed to update (no record found for the given request)",
			args: args{
				ctx:        context.Background(),
				updateMask: []enachEnumsPb.EnachMandateActionFieldMask{enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_STATUS},
				action: &enachPb.EnachMandateAction{
					Id: uuid.NewString(),
				},
			},
			wantErr: epifierrors.ErrRowNotUpdated,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, enachMandateActionTs.db, enachMandateActionTs.config.RecurringPaymentDb.GetName(), enachTables)
			err := enachMandateActionTs.dao.Update(tt.args.ctx, tt.args.updateMask, tt.args.action)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("Update() got error= %v, want error= %v", err, tt.wantErr)
			}
		})
	}
}

func TestEnachMandateActionDaoPgdb_UpdateWithStatusCheck(t *testing.T) {
	type args struct {
		ctx                      context.Context
		updateMask               []enachEnumsPb.EnachMandateActionFieldMask
		expectedCurrentStatus    enachEnumsPb.EnachActionStatus
		expectedCurrentSubStatus enachEnumsPb.EnachActionSubStatus
		action                   *enachPb.EnachMandateAction
	}
	tests := []struct {
		name    string
		args    args
		wantErr error
	}{
		{
			name: "update should fail when action currentStatus does not matches with expectedCurrentStatus",
			args: args{
				ctx:        context.Background(),
				updateMask: []enachEnumsPb.EnachMandateActionFieldMask{enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_STATUS},
				action: &enachPb.EnachMandateAction{
					Id:           enachMandateActionFixture1.GetId(),
					ActionStatus: enachEnumsPb.EnachActionStatus_ACTION_STATUS_SUCCESS,
				},
				// current status in db is ACTION_STATUS_CREATED
				expectedCurrentStatus:    enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS,
				expectedCurrentSubStatus: enachMandateActionFixture1.GetActionSubStatus(),
			},
			wantErr: epifierrors.ErrRowNotUpdated,
		},
		{
			name: "update should fail when action currentSubStatus does not matches with expectedCurrentSubStatus",
			args: args{
				ctx:        context.Background(),
				updateMask: []enachEnumsPb.EnachMandateActionFieldMask{enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_STATUS},
				action: &enachPb.EnachMandateAction{
					Id:           enachMandateActionFixture1.GetId(),
					ActionStatus: enachEnumsPb.EnachActionStatus_ACTION_STATUS_SUCCESS,
				},
				expectedCurrentStatus: enachMandateActionFixture1.GetActionStatus(),
				// current status in db is ACTION_SUB_STATUS_UNSPECIFIED
				expectedCurrentSubStatus: enachEnumsPb.EnachActionSubStatus_ACTION_SUB_STATUS_AWAITING_AUTHORIZATION,
			},
			wantErr: epifierrors.ErrRowNotUpdated,
		},
		{
			name: "update should fail when update mask it empty",
			args: args{
				ctx:        context.Background(),
				updateMask: []enachEnumsPb.EnachMandateActionFieldMask{},
				action: &enachPb.EnachMandateAction{
					Id:           enachMandateActionFixture1.GetId(),
					ActionStatus: enachEnumsPb.EnachActionStatus_ACTION_STATUS_SUCCESS,
				},
				expectedCurrentStatus:    enachMandateActionFixture1.GetActionStatus(),
				expectedCurrentSubStatus: enachMandateActionFixture1.GetActionSubStatus(),
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "update should fail when action id is empty",
			args: args{
				ctx:        context.Background(),
				updateMask: []enachEnumsPb.EnachMandateActionFieldMask{enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_STATUS},
				action: &enachPb.EnachMandateAction{
					ActionStatus: enachEnumsPb.EnachActionStatus_ACTION_STATUS_SUCCESS,
				},
				expectedCurrentStatus:    enachMandateActionFixture1.GetActionStatus(),
				expectedCurrentSubStatus: enachMandateActionFixture1.GetActionSubStatus(),
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "update should fail when no action exists with given id",
			args: args{
				ctx:        context.Background(),
				updateMask: []enachEnumsPb.EnachMandateActionFieldMask{enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_STATUS},
				action: &enachPb.EnachMandateAction{
					Id:           uuid.NewString(),
					ActionStatus: enachEnumsPb.EnachActionStatus_ACTION_STATUS_SUCCESS,
				},
				expectedCurrentStatus:    enachMandateActionFixture1.GetActionStatus(),
				expectedCurrentSubStatus: enachMandateActionFixture1.GetActionSubStatus(),
			},
			wantErr: epifierrors.ErrRowNotUpdated,
		},
		{
			name: "update should be successful",
			args: args{
				ctx:        context.Background(),
				updateMask: []enachEnumsPb.EnachMandateActionFieldMask{enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_STATUS},
				action: &enachPb.EnachMandateAction{
					Id:           enachMandateActionFixture1.GetId(),
					ActionStatus: enachEnumsPb.EnachActionStatus_ACTION_STATUS_SUCCESS,
				},
				expectedCurrentStatus:    enachMandateActionFixture1.GetActionStatus(),
				expectedCurrentSubStatus: enachMandateActionFixture1.GetActionSubStatus(),
			},
		},
		{
			name: "Update should be successful(Update Enach Mandate Detailed Status)",
			args: args{
				ctx:        context.Background(),
				updateMask: []enachEnumsPb.EnachMandateActionFieldMask{enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_DETAILED_STATUS},
				action: &enachPb.EnachMandateAction{
					Id: enachMandateActionFixture1.GetId(),
					ActionDetailedStatus: &enachPb.ActionDetailedStatus{ActionTypeSpecificMetadata: &enachPb.ActionDetailedStatus_ExecuteActionDetailedStatus_{ExecuteActionDetailedStatus: &enachPb.ActionDetailedStatus_ExecuteActionDetailedStatus{
						NachTransactionFlag:       "2",
						NachTransactionReasonCode: "21",
						FiStatusCode:              "ENACH_EXEC_001",
						FiStatusDesc:              "Invalid UMRN or inactive mandate",
					}}},
				},
				expectedCurrentStatus:    enachMandateActionFixture1.GetActionStatus(),
				expectedCurrentSubStatus: enachMandateActionFixture1.GetActionSubStatus(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, enachMandateActionTs.db, enachMandateActionTs.config.RecurringPaymentDb.GetName(), enachTables)
			err := enachMandateActionTs.dao.UpdateWithStatusCheck(tt.args.ctx, tt.args.updateMask, tt.args.action, tt.args.expectedCurrentStatus, tt.args.expectedCurrentSubStatus)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("UpdateWithStatusCheck() got error= %v, want error= %v", err, tt.wantErr)
			}
		})
	}
}

func TestEnachMandateActionDaoPgdb_GetById(t *testing.T) {
	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		args    args
		want    *enachPb.EnachMandateAction
		wantErr error
	}{
		{
			name: "successfully fetched for given id",
			args: args{
				ctx: context.Background(),
				id:  enachMandateActionFixture1.GetId(),
			},
			want: enachMandateActionFixture1,
		},
		{
			name: "failed to fetch (mandatory param id is empty)",
			args: args{
				ctx: context.Background(),
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "no record found for the given request",
			args: args{
				ctx: context.Background(),
				id:  uuid.NewString(),
			},
			wantErr: epifierrors.ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		pkgTest.TruncateAndPopulateRdsFixtures(t, enachMandateActionTs.db, enachMandateActionTs.config.RecurringPaymentDb.GetName(), enachTables)
		t.Run(tt.name, func(t *testing.T) {
			got, err := enachMandateActionTs.dao.GetById(tt.args.ctx, tt.args.id)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !compareEnachMandateAction(got, tt.want) {
				t.Errorf("GetById() got = %v, want = %v", got, tt.want)
			}
		})
	}
}

func TestEnachMandateActionDaoPgdb_GetByIds(t *testing.T) {
	type args struct {
		ctx context.Context
		ids []string
	}
	tests := []struct {
		name    string
		args    args
		want    []*enachPb.EnachMandateAction
		wantErr error
	}{
		{
			name: "should fetch successfully if mandate action exists",
			args: args{
				ctx: context.Background(),
				ids: []string{enachMandateActionFixture1.GetId()},
			},
			want: []*enachPb.EnachMandateAction{enachMandateActionFixture1},
		},
		{
			name: "should fail to fetch if mandate action id list is empty",
			args: args{
				ctx: context.Background(),
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "should fail to fetch if mandate action id list contains empty strings",
			args: args{
				ctx: context.Background(),
				ids: []string{""},
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "should fail to fetch if all mandate action ids are incorrect",
			args: args{
				ctx: context.Background(),
				ids: []string{uuid.NewString()},
			},
			wantErr: epifierrors.ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		pkgTest.TruncateAndPopulateRdsFixtures(t, enachMandateActionTs.db, enachMandateActionTs.config.RecurringPaymentDb.GetName(), enachTables)
		t.Run(tt.name, func(t *testing.T) {
			got, err := enachMandateActionTs.dao.GetByIds(tt.args.ctx, tt.args.ids)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetByIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !compareEnachMandateActions(got, tt.want) {
				t.Errorf("GetByIds() got = %v, want = %v", got, tt.want)
			}
		})
	}
}
func compareEnachMandateActions(actual, expected []*enachPb.EnachMandateAction) bool {
	if len(actual) != len(expected) {
		return false
	}

	for i := 0; i < len(actual); i++ {
		expected[i].CreatedAt = actual[i].CreatedAt
		expected[i].UpdatedAt = actual[i].UpdatedAt
		if !proto.Equal(actual[i], expected[i]) {
			return false
		}
	}

	return true
}

func TestEnachMandateActionDaoPgdb_GetByEnachMandateId(t *testing.T) {
	type args struct {
		ctx     context.Context
		id      string
		options []FilterOption
	}
	tests := []struct {
		name    string
		args    args
		want    []*enachPb.EnachMandateAction
		wantErr error
	}{
		{
			name: "successfully fetched for given id",
			args: args{
				ctx:     context.Background(),
				id:      "enach-mandate-id-3",
				options: []FilterOption{WithActionType(enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE), WithActionStatus(enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS), WithOrderBy(false)},
			},
			want: enachMandateActionFixtureList2,
		},
		{
			name: "failed to fetch (mandatory param id is empty)",
			args: args{
				ctx: context.Background(),
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "no record found for the given request",
			args: args{
				ctx: context.Background(),
				id:  uuid.NewString(),
			},
			wantErr: epifierrors.ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		pkgTest.TruncateAndPopulateRdsFixtures(t, enachMandateActionTs.db, enachMandateActionTs.config.RecurringPaymentDb.GetName(), enachTables)
		t.Run(tt.name, func(t *testing.T) {
			got, err := enachMandateActionTs.dao.GetByEnachMandateId(tt.args.ctx, tt.args.id, tt.args.options...)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !compareEnachMandateActionList(got, tt.want) {
				t.Errorf("GetById() got = %v, want = %v", got, tt.want)
			}
		})
	}

}
func TestEnachMandateActionPgdb_GetByVendorBatchRequestId(t *testing.T) {
	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		args    args
		want    []*enachPb.EnachMandateAction
		wantErr error
	}{
		{
			name: "no record found for the given request",
			args: args{
				ctx: context.Background(),
				id:  uuid.NewString(),
			},
			wantErr: epifierrors.ErrRecordNotFound,
		},
		{
			name: "failed to fetch (mandatory param id is empty)",
			args: args{
				ctx: context.Background(),
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "successfully fatches enach mandate actions for the given vendor batch request id",
			args: args{
				ctx: context.Background(),
				id:  "vendor-batch-req-id-1",
			},
			want: enachMandateActionFixtureList1,
		},
	}
	for _, tt := range tests {
		pkgTest.TruncateAndPopulateRdsFixtures(t, enachMandateActionTs.db, enachMandateActionTs.config.RecurringPaymentDb.GetName(), enachTables)
		t.Run(tt.name, func(t *testing.T) {
			got, err := enachMandateActionTs.dao.GetByVendorBatchRequestId(tt.args.ctx, tt.args.id)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetByVendorBatchRequestId error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !compareEnachMandateActionList(got, tt.want) {
				t.Errorf("GetByVendorBatchRequestId got = %v, want = %v", got, tt.want)
			}
		})
	}
}

func compareEnachMandateActionList(actual, expected []*enachPb.EnachMandateAction) bool {
	if len(actual) != len(expected) {
		return false
	}
	for idx := range actual {
		if !compareEnachMandateAction(actual[idx], expected[idx]) {
			return false
		}
	}
	return true
}

func compareEnachMandateAction(actual, expected *enachPb.EnachMandateAction) bool {
	if actual != nil && expected != nil {
		expected.CreatedAt = actual.CreatedAt
		expected.UpdatedAt = actual.UpdatedAt
	}
	return proto.Equal(actual, expected)
}
