//go:generate mockgen -source=$PWD/dao.go -destination=$PWD/mocks/mock_dao.go -package=mocks
//go:generate dao_metrics_gen .

package dao

import (
	"context"

	"github.com/google/wire"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
)

var EnachMandateActionDaoWireSet = wire.NewSet(NewEnachMandateActionDaoPgdb, wire.Bind(new(EnachMandateActionDao), new(*EnachMandateActionDaoPgdb)))
var EnachMandateDaoWireSet = wire.NewSet(NewEnachMandateDaoPgdb, ProvideAttributedIdGen, wire.Bind(new(EnachMandateDao), new(*EnachMandateDaoPgdb)))

type EnachMandateActionDao interface {
	Create(ctx context.Context, action *enachPb.EnachMandateAction) (*enachPb.EnachMandateAction, error)
	Update(ctx context.Context, updateMask []enachEnumsPb.EnachMandateActionFieldMask, action *enachPb.EnachMandateAction) error
	UpdateWithStatusCheck(ctx context.Context, updateMask []enachEnumsPb.EnachMandateActionFieldMask, action *enachPb.EnachMandateAction, expectedCurrentStatus enachEnumsPb.EnachActionStatus, expectedCurrentSubStatus enachEnumsPb.EnachActionSubStatus) error
	GetByActionTypeAndEnachMandateId(ctx context.Context, actionType enachEnumsPb.EnachActionType, mandateId string) ([]*enachPb.EnachMandateAction, error)
	GetByClientRequestId(ctx context.Context, clientRequestId string) (*enachPb.EnachMandateAction, error)
	GetByActionTypeAndVendorRequestId(ctx context.Context, actionType enachEnumsPb.EnachActionType, vendorRequestId string) (*enachPb.EnachMandateAction, error)
	GetById(ctx context.Context, Id string) (*enachPb.EnachMandateAction, error)
	GetByVendorBatchRequestId(ctx context.Context, Id string) ([]*enachPb.EnachMandateAction, error)
	GetByIds(ctx context.Context, Id []string) ([]*enachPb.EnachMandateAction, error)
	GetByEnachMandateId(ctx context.Context, mandateId string, options ...FilterOption) ([]*enachPb.EnachMandateAction, error)
}

type EnachMandateDao interface {
	Create(ctx context.Context, enachMandate *enachPb.EnachMandate, ownership commontypes.Ownership) (*enachPb.EnachMandate, error)
	Update(ctx context.Context, updateMask []enachEnumsPb.EnachMandateFieldMask, enachMandate *enachPb.EnachMandate) error
	GetById(ctx context.Context, Id string) (*enachPb.EnachMandate, error)
	GetByIds(ctx context.Context, Id []string) ([]*enachPb.EnachMandate, error)
	// GetByUmrn fetches the EnachMandate entity based on the UMRN, by querying DBs across multiple ownerships. For cases,
	// when fanOut is false it uses the DB corresponding to the ownership passed in the context otherwise it
	// simply queries all the DBs.
	GetByUmrn(ctx context.Context, umrn string, fanOut bool) (*enachPb.EnachMandate, error)
	// GetByRecurringPaymentId fetches the enach mandate for the given recurring payment id, by querying DBs across multiple ownerships.
	// For cases, when fanOut is false it uses the DB corresponding to the ownership passed in the context otherwise it
	// simply queries all the DBs.
	GetByRecurringPaymentId(ctx context.Context, recurringPaymentId string, fanOut bool) (*enachPb.EnachMandate, error)
}
