package dao

import (
	"fmt"

	"gorm.io/gorm"

	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
)

// FilterOption interface to define generic dao filter options
type FilterOption interface {
	// applyInGorm applied the filter to gorm.DB
	applyInGorm(db *gorm.DB) *gorm.DB
}

// funcFilterOption wraps a function that modifies *gorm.DB into an
// implementation of the FilterOption interface.
type funcFilterOption struct {
	fn func(db *gorm.DB) *gorm.DB
}

func (fdo *funcFilterOption) applyInGorm(db *gorm.DB) *gorm.DB {
	return fdo.fn(db)
}

func newFuncFilterOption(fn func(db *gorm.DB) *gorm.DB) *funcFilterOption {
	return &funcFilterOption{fn: fn}
}

func WithActionType(actionType enachEnumsPb.EnachActionType) FilterOption {
	return newFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("action_type = ?", actionType)
	})
}

func WithActionStatus(actionStatus enachEnumsPb.EnachActionStatus) FilterOption {
	return newFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("action_status = ?", actionStatus)
	})
}

func WithOrderBy(isAscending bool) FilterOption {
	return newFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		orderClause := fmt.Sprintf("created_at %s", getSortOrder(isAscending))
		return db.Order(orderClause)
	})
}

func WithActionSubStatus(actionSubStatus enachEnumsPb.EnachActionSubStatus) FilterOption {
	return newFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("action_sub_status = ?", actionSubStatus)
	})
}

func getSortOrder(isAscending bool) string {
	if isAscending {
		return "ASC"
	}
	return "DESC"
}
