package model

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"time"

	"google.golang.org/protobuf/types/known/timestamppb"
	gormV2 "gorm.io/gorm"

	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	"github.com/epifi/be-common/pkg/nulltypes"
)

type EnachMandate struct {
	Id string `gorm:"primary_key"`

	// UMRN stands for unique mandate reference number, it's a unique identifier of an enach mandate at bank's end.
	// UMRN can be empty in case of Fi initated mandates untill we get from the vendor
	Umrn nulltypes.NullString

	// relationship between recurring payment and enach mandate is 1 to 1 i.e. only one enach mandate entry
	// would exist would a given recurring payment entry.
	RecurringPaymentId string

	// denotes the authentication mode selected by the payee for authorizing mandate registration,
	// can be UNSPECIFIED for external mandates whose registration was not done using Fi App
	RegistrationAuthMode enachEnumsPb.EnachRegistrationAuthMode

	// denotes the source from where the enach registration was performed by the user,
	// e.g. FI_APP is registration was performed on Fi App, will be EXTERNAL if enach registration was not performed on Fi App but on some external portal.
	RegistrationProvenance enachEnumsPb.EnachRegistrationProvenance

	// Vendor who is fulfilling the mandate creation e.g FEDERAL_BANK, DIGIO etc
	Vendor commonvgpb.Vendor

	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gormV2.DeletedAt
}

func (e *EnachMandate) GetProto() *enachPb.EnachMandate {
	return &enachPb.EnachMandate{
		Id:                     e.Id,
		Umrn:                   e.Umrn.GetValue(),
		RecurringPaymentId:     e.RecurringPaymentId,
		RegistrationAuthMode:   e.RegistrationAuthMode,
		RegistrationProvenance: e.RegistrationProvenance,
		Vendor:                 e.Vendor,
		CreatedAt:              timestamppb.New(e.CreatedAt),
		UpdatedAt:              timestamppb.New(e.UpdatedAt),
	}
}

func NewEnachMandate(id string, mandateProto *enachPb.EnachMandate) *EnachMandate {
	return &EnachMandate{
		Id:                     id,
		Umrn:                   nulltypes.NewNullString(mandateProto.GetUmrn()),
		RecurringPaymentId:     mandateProto.GetRecurringPaymentId(),
		RegistrationAuthMode:   mandateProto.GetRegistrationAuthMode(),
		RegistrationProvenance: mandateProto.GetRegistrationProvenance(),
		Vendor:                 mandateProto.GetVendor(),
	}
}

// ConvertToEnachMandateUpdateDbModel is useful for creating mandate db model for update queries
func ConvertToEnachMandateUpdateDbModel(mandateProto *enachPb.EnachMandate) *EnachMandate {
	return &EnachMandate{
		Id:                     mandateProto.GetId(),
		Umrn:                   nulltypes.NewNullString(mandateProto.GetUmrn()),
		RecurringPaymentId:     mandateProto.GetRecurringPaymentId(),
		RegistrationAuthMode:   mandateProto.GetRegistrationAuthMode(),
		RegistrationProvenance: mandateProto.GetRegistrationProvenance(),
		Vendor:                 mandateProto.GetVendor(),
	}
}
