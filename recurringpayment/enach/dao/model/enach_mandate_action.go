package model

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"
	gormV2 "gorm.io/gorm"

	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	"github.com/epifi/be-common/pkg/nulltypes"
)

// EnachMandateAction denotes the action on an enach mandate,
// action could be related to creation of an enach mandate or pausing an enach mandate etc which is denoted by action_type attribute.
type EnachMandateAction struct {
	// denotes the unique identifier for an enach mandate action.
	Id string `gorm:"type:uuid;default:gen_random_uuid();primaryKey"`

	// denotes the unique identifier for an enach mandate action.
	ClientRequestId string

	// denotes the id of enach mandate for which the action was request.
	EnachMandateId string

	// denotes the type of action e.g CREATE, PAUSE etc.
	ActionType enachEnumsPb.EnachActionType

	// denotes the current status of action e.g IN_PROGRESS, SUCCESS, FAILED etc
	ActionStatus enachEnumsPb.EnachActionStatus

	// denotes the current sub status of action
	ActionSubStatus enachEnumsPb.EnachActionSubStatus

	// stores additional metadata for the action like npci_ref_id for mandate creation action, execution amount info for execute action.
	ActionMetadata *enachPb.ActionMetadata

	// stores NACH transaction response flag, NACH transaction response reason code and fi status code
	ActionDetailedStatus *enachPb.ActionDetailedStatus

	// denotes the request id passed to vendor in vendor api call  in case vendor api call is needed for fulfilling the request
	// would be empty for request in which vendor intervention is not needed for fulfilling the request.
	VendorRequestId nulltypes.NullString

	// denotes the batch request id passed to vendor when the process is done by collating multiple entries and sending to vendor in form
	// of a batch
	// would be empty for cases in which vendor intervention is not needed or if the process is done individually for each request
	VendorBatchRequestId nulltypes.NullString

	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gormV2.DeletedAt
}

func (e *EnachMandateAction) GetProto() *enachPb.EnachMandateAction {
	return &enachPb.EnachMandateAction{
		Id:                   e.Id,
		ClientRequestId:      e.ClientRequestId,
		EnachMandateId:       e.EnachMandateId,
		ActionType:           e.ActionType,
		ActionStatus:         e.ActionStatus,
		ActionSubStatus:      e.ActionSubStatus,
		ActionDetailedStatus: e.ActionDetailedStatus,
		VendorRequestId:      e.VendorRequestId.GetValue(),
		ActionMetadata:       e.ActionMetadata,
		VendorBatchRequestId: e.VendorBatchRequestId.GetValue(),
		CreatedAt:            timestamppb.New(e.CreatedAt),
		UpdatedAt:            timestamppb.New(e.UpdatedAt),
	}
}

func NewEnachMandateAction(action *enachPb.EnachMandateAction) *EnachMandateAction {
	return &EnachMandateAction{
		ClientRequestId:      action.GetClientRequestId(),
		EnachMandateId:       action.GetEnachMandateId(),
		ActionType:           action.GetActionType(),
		ActionStatus:         action.GetActionStatus(),
		ActionSubStatus:      action.GetActionSubStatus(),
		ActionDetailedStatus: action.GetActionDetailedStatus(),
		VendorRequestId:      nulltypes.NewNullString(action.GetVendorRequestId()),
		VendorBatchRequestId: nulltypes.NewNullString(action.GetVendorBatchRequestId()),
		ActionMetadata:       action.GetActionMetadata(),
	}
}

// ConvertToEnachMandateActionUpdateDbModel is useful for creating mandate action db model for update queries
func ConvertToEnachMandateActionUpdateDbModel(action *enachPb.EnachMandateAction) *EnachMandateAction {
	return &EnachMandateAction{
		Id:                   action.GetId(),
		ClientRequestId:      action.GetClientRequestId(),
		EnachMandateId:       action.GetEnachMandateId(),
		ActionType:           action.GetActionType(),
		ActionStatus:         action.GetActionStatus(),
		ActionSubStatus:      action.GetActionSubStatus(),
		ActionDetailedStatus: action.GetActionDetailedStatus(),
		VendorRequestId:      nulltypes.NewNullString(action.GetVendorRequestId()),
		VendorBatchRequestId: nulltypes.NewNullString(action.GetVendorBatchRequestId()),
		ActionMetadata:       action.GetActionMetadata(),
	}
}

func (e *EnachMandateAction) TableName() string {
	return "enach_mandates_actions"
}
