package consumer

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"

	queuePb "github.com/epifi/be-common/api/queue"
	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/payment"
	consumerPb "github.com/epifi/gamma/api/recurringpayment/enach/consumer"
	"github.com/epifi/be-common/pkg/epifigrpc"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/logger"
	payPkg "github.com/epifi/gamma/pkg/pay"
)

type ConsumerService struct {
	celestialClient celestialPb.CelestialClient
}

func NewConsumerService(celestialClient celestialPb.CelestialClient) *ConsumerService {
	return &ConsumerService{celestialClient: celestialClient}
}

var _ consumerPb.ConsumerServer = &ConsumerService{}

var (
	getSuccessRes = func() *consumerPb.ConsumerResponse {
		return &consumerPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS}}
	}
	getPermanentFailureRes = func() *consumerPb.ConsumerResponse {
		return &consumerPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE}}
	}
	getTransientFailureRes = func() *consumerPb.ConsumerResponse {
		return &consumerPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}
	}
)

// ProcessOrderEventForExecutionStatusUpdate processes order update events for getting notified about the execution status of enach execution.
// It validates if the order is for an enach execution, if yes it checks the order status and updates the execution status of enach execution.
// If order is not related to enach execution, then it discards the event.
// **Note** : for now, we are only using order update event to know the status of epifi's pool to enach mandate destination account transfer txn,
func (c *ConsumerService) ProcessOrderEventForExecutionStatusUpdate(ctx context.Context, orderUpdate *orderPb.OrderUpdate) (*consumerPb.ConsumerResponse, error) {
	order := orderUpdate.GetOrderWithTransactions().GetOrder()
	logger.Debug(ctx, "received order update event in enach order update consumer", zap.String(logger.ORDER_ID, order.GetId()), zap.String(logger.ORDER_STATUS, order.GetStatus().String()))

	// discarding the order event if it's not related to enach.
	if order.GetWorkflow() != orderPb.OrderWorkflow_ENACH_EXECUTION_VIA_POOL_ACCOUNT_TRANSFER {
		return getSuccessRes(), nil
	}
	// we only need to process order events which are in terminal success state.
	if order.GetStatus() != orderPb.OrderStatus_PAID {
		logger.Info(ctx, "ignoring order update as order is not in terminal success state", zap.String(logger.ORDER_ID, order.GetId()), zap.String(logger.ORDER_STATUS, order.GetStatus().String()))
		return getSuccessRes(), nil
	}

	allTxns := orderUpdate.GetOrderWithTransactions().GetTransactions()
	// get txn status of pool to destination account transfer txn
	var poolToDestAccountTransferTxn *payment.Transaction
	for _, txn := range allTxns {
		if txn.GetPiFrom() == payPkg.FederalEnachPoolAccountPiId {
			poolToDestAccountTransferTxn = txn
			break
		}
	}
	// if pool to destination account txn is not present, return permanent failure as it's not as expected case for now.
	if poolToDestAccountTransferTxn == nil {
		logger.Error(ctx, "pool to destination account transfer txn not found in a successful enach order", zap.String(logger.ORDER_ID, order.GetId()), zap.String(logger.CLIENT_REQUEST_ID, order.GetClientReqId()))
		return getPermanentFailureRes(), nil
	}

	// we only need to signal the workflow once the txn is successful, currently we don't have a failed case for pool to destination account transfer, so not handling it for now.
	// returning permanent failure for following case, since it's not an expected case as order is successful so pool to destination account transfer txn shouldn't be in a failed state.
	if poolToDestAccountTransferTxn.GetStatus() != payment.TransactionStatus_SUCCESS {
		logger.Error(ctx, "pool to destination account transfer txn is not in a successful state for a successful enach order", zap.String(logger.ORDER_ID, order.GetId()), zap.String(logger.CLIENT_REQUEST_ID, order.GetClientReqId()))
		return getPermanentFailureRes(), nil
	}

	emptyPayloadBytes, err := protojson.Marshal(&emptyPb.Empty{})
	if err != nil {
		logger.Error(ctx, "error marshalling empty proto", zap.Error(err))
		return getPermanentFailureRes(), nil
	}
	// signal the enach execution workflow that destination account fund transfer is successful
	signalRes, err := c.celestialClient.SignalWorkflow(ctx, &celestialPb.SignalWorkflowRequest{
		Identifier:       &celestialPb.SignalWorkflowRequest_ClientReqId{ClientReqId: &celestialPb.ClientReqId{Id: order.GetClientReqId(), Client: workflowPb.Client_RECURRING_PAYMENT}},
		SignalId:         string(rpNs.EnachDestinationAccountFundTransferSuccessfulSignal),
		Payload:          emptyPayloadBytes,
		Ownership:        commontypes.Ownership_EPIFI_TECH,
		QualityOfService: celestialPb.QoS_GUARANTEED,
	})
	if rpcErr := epifigrpc.RPCError(signalRes, err); rpcErr != nil {
		logger.Error(ctx, "celestialClient.SignalWorkflow rpc call failed", zap.String(logger.ORDER_ID, order.GetId()), zap.String(logger.CLIENT_REQUEST_ID, order.GetClientReqId()), zap.Error(rpcErr))
		return getTransientFailureRes(), nil
	}
	logger.Info(ctx, "successfully sent destination fund transfer completed signal to enach execution workflow", zap.String(logger.ORDER_ID, order.GetId()), zap.String(logger.CLIENT_REQUEST_ID, order.GetClientReqId()))
	return getSuccessRes(), nil
}
