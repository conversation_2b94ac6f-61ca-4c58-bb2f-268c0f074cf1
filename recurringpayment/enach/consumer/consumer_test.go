package consumer

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	celestialPb "github.com/epifi/be-common/api/celestial"
	celestialMocks "github.com/epifi/be-common/api/celestial/mocks"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/rpc"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/payment"
	consumerPb "github.com/epifi/gamma/api/recurringpayment/enach/consumer"
	payPkg "github.com/epifi/gamma/pkg/pay"
)

func TestConsumerService_ProcessOrderEventForFundTransferStatusUpdate(t *testing.T) {
	type args struct {
		ctx context.Context
		req *orderPb.OrderUpdate
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockCelestialServiceClient *celestialMocks.MockCelestialClient)
		want       *consumerPb.ConsumerResponse
		wantErr    bool
	}{
		{
			name: "should return Success consumer response when order is not related to ENACH",
			args: args{
				ctx: context.Background(),
				req: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id: "order-id-1",
							// order workflow is not related to ENACH
							Workflow: orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
							Status:   orderPb.OrderStatus_PAID,
						},
						Transactions: []*payment.Transaction{
							{Id: "txn-id-1", PiFrom: "pi-from-1", PiTo: "pi-to-1", Status: payment.TransactionStatus_SUCCESS},
							{Id: "txn-id-1", PiFrom: payPkg.FederalEnachPoolAccountPiId, PiTo: "pi-to-2", Status: payment.TransactionStatus_SUCCESS},
						},
					},
				},
			},
			setupMocks: func(mockCelestialServiceClient *celestialMocks.MockCelestialClient) {
			},
			want: getSuccessRes(),
		},
		{
			name: "should return Success consumer response when order is not in terminal success state",
			args: args{
				ctx: context.Background(),
				req: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-id-1",
							ClientReqId: "client-request-id-1",
							Workflow:    orderPb.OrderWorkflow_ENACH_EXECUTION_VIA_POOL_ACCOUNT_TRANSFER,
							Status:      orderPb.OrderStatus_IN_PAYMENT,
						},
						Transactions: []*payment.Transaction{
							{Id: "txn-id-1", PiFrom: "pi-from-1", PiTo: "pi-to-1", Status: payment.TransactionStatus_IN_PROGRESS},
							{Id: "txn-id-1", PiFrom: payPkg.FederalEnachPoolAccountPiId, PiTo: "pi-to-2", Status: payment.TransactionStatus_SUCCESS},
						},
					},
				},
			},
			setupMocks: func(mockCelestialServiceClient *celestialMocks.MockCelestialClient) {
			},
			want: getSuccessRes(),
		},
		{
			name: "should return PermanentFailure consumer response when pool account transfer txn is not present in enach order",
			args: args{
				ctx: context.Background(),
				req: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-id-1",
							ClientReqId: "client-request-id-1",
							Workflow:    orderPb.OrderWorkflow_ENACH_EXECUTION_VIA_POOL_ACCOUNT_TRANSFER,
							Status:      orderPb.OrderStatus_PAID,
						},
						// pool account transfer txn is not present
						Transactions: []*payment.Transaction{
							{Id: "txn-id-1", PiFrom: "pi-from-1", PiTo: "pi-to-1", Status: payment.TransactionStatus_SUCCESS},
						},
					},
				},
			},
			setupMocks: func(mockCelestialServiceClient *celestialMocks.MockCelestialClient) {
			},
			want: getPermanentFailureRes(),
		},
		{
			name: "should return PermanentFailure consumer response when pool to destination account transfer txn is not in SUCCESS state",
			args: args{
				ctx: context.Background(),
				req: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-id-1",
							ClientReqId: "client-request-id-1",
							Workflow:    orderPb.OrderWorkflow_ENACH_EXECUTION_VIA_POOL_ACCOUNT_TRANSFER,
							Status:      orderPb.OrderStatus_PAID,
						},
						Transactions: []*payment.Transaction{
							{Id: "txn-id-1", PiFrom: "pi-from-1", PiTo: "pi-to-1", Status: payment.TransactionStatus_SUCCESS},
							{Id: "txn-id-1", PiFrom: payPkg.FederalEnachPoolAccountPiId, PiTo: "pi-to-2", Status: payment.TransactionStatus_FAILED},
						},
					},
				},
			},
			setupMocks: func(mockCelestialServiceClient *celestialMocks.MockCelestialClient) {
			},
			want: getPermanentFailureRes(),
		},
		{
			name: "should return TransientFailure consumer response when rpc call to signal the enach execution workflow fails",
			args: args{
				ctx: context.Background(),
				req: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-id-1",
							ClientReqId: "client-request-id-1",
							Workflow:    orderPb.OrderWorkflow_ENACH_EXECUTION_VIA_POOL_ACCOUNT_TRANSFER,
							Status:      orderPb.OrderStatus_PAID,
						},
						Transactions: []*payment.Transaction{
							{Id: "txn-id-1", PiFrom: "pi-from-1", PiTo: "pi-to-1", Status: payment.TransactionStatus_SUCCESS},
							{Id: "txn-id-1", PiFrom: payPkg.FederalEnachPoolAccountPiId, PiTo: "pi-to-2", Status: payment.TransactionStatus_SUCCESS},
						},
					},
				},
			},
			setupMocks: func(mockCelestialServiceClient *celestialMocks.MockCelestialClient) {
				mockCelestialServiceClient.EXPECT().SignalWorkflow(context.Background(), &celestialPb.SignalWorkflowRequest{
					Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							// workflow client request is same as order client request id.
							Id:     "client-request-id-1",
							Client: workflowPb.Client_RECURRING_PAYMENT,
						},
					},
					SignalId:         string(rpNs.EnachDestinationAccountFundTransferSuccessfulSignal),
					Payload:          []byte("{}"),
					Ownership:        commontypes.Ownership_EPIFI_TECH,
					QualityOfService: celestialPb.QoS_GUARANTEED,
				}).Return(&celestialPb.SignalWorkflowResponse{Status: rpc.StatusInternal()}, nil)
			},
			want: getTransientFailureRes(),
		},
		{
			name: "should return Success consumer response when rpc call to signal the enach execution workflow is successful",
			args: args{
				ctx: context.Background(),
				req: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-id-1",
							ClientReqId: "client-request-id-1",
							Workflow:    orderPb.OrderWorkflow_ENACH_EXECUTION_VIA_POOL_ACCOUNT_TRANSFER,
							Status:      orderPb.OrderStatus_PAID,
						},
						Transactions: []*payment.Transaction{
							{Id: "txn-id-1", PiFrom: "pi-from-1", PiTo: "pi-to-1", Status: payment.TransactionStatus_SUCCESS},
							{Id: "txn-id-1", PiFrom: payPkg.FederalEnachPoolAccountPiId, PiTo: "pi-to-2", Status: payment.TransactionStatus_SUCCESS},
						},
					},
				},
			},
			setupMocks: func(mockCelestialServiceClient *celestialMocks.MockCelestialClient) {
				mockCelestialServiceClient.EXPECT().SignalWorkflow(context.Background(), &celestialPb.SignalWorkflowRequest{
					Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							// workflow client request is same as order client request id.
							Id:     "client-request-id-1",
							Client: workflowPb.Client_RECURRING_PAYMENT,
						},
					},
					SignalId:         string(rpNs.EnachDestinationAccountFundTransferSuccessfulSignal),
					Payload:          []byte("{}"),
					Ownership:        commontypes.Ownership_EPIFI_TECH,
					QualityOfService: celestialPb.QoS_GUARANTEED,
				}).Return(&celestialPb.SignalWorkflowResponse{Status: rpc.StatusOk()}, nil)
			},
			want: getSuccessRes(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockCelestialSvcClient := celestialMocks.NewMockCelestialClient(ctr)

			tt.setupMocks(mockCelestialSvcClient)

			c := &ConsumerService{
				celestialClient: mockCelestialSvcClient,
			}
			got, err := c.ProcessOrderEventForExecutionStatusUpdate(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessOrderEventForExecutionStatusUpdate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("ProcessOrderEventForExecutionStatusUpdate() got = %v, want %v", got, tt.want)
			}
		})
	}
}
