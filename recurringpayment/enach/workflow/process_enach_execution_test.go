package workflow

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/testsuite"
	"google.golang.org/protobuf/encoding/protojson"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	enachActivityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	enachWorkflowPb "github.com/epifi/gamma/api/recurringpayment/enach/workflow"
	celestialActivityV2 "github.com/epifi/gamma/celestial/activity/v2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	enachActivity "github.com/epifi/gamma/recurringpayment/enach/activity"
)

func TestPresentEnachExecution(t *testing.T) {
	const (
		defaultPresentationFileS3Path       = "presentation/04052023/NACH_DR_04052023_UTILITY_CODE_EpifiTechnologies_001.txt"
		defaultPresentationFileName         = "NACH_DR_04052023_UTILITY_CODE_EpifiTechnologies_001-RES.txt"
		defaultPresentationBatchExecutionId = "default-presentation-batch-execution-id"

		enachMandateActionId1 = "test_enach_mandate_action_id_1"
		enachMandateActionId2 = "test_enach_mandate_action_id_2"
		enachMandateActionId3 = "test_enach_mandate_action_id_3"
	)

	var (
		temporalPermanentErr                  = temporal.NewNonRetryableApplicationError("Permanent Error", "", epifierrors.ErrPermanent)
		defaultCreateEnachPresentationFileRes = &enachActivityPb.CreateEnachPresentationFileResponse{
			PresentedActionExecutionIds:  []string{enachMandateActionId1, enachMandateActionId2, enachMandateActionId3},
			PresentationFileS3Path:       defaultPresentationFileS3Path,
			PresentationFileName:         defaultPresentationFileName,
			PresentationBatchExecutionId: defaultPresentationBatchExecutionId,
		}
		fileUploadedSignalPayload, _ = protojson.Marshal(&enachWorkflowPb.EnachPresentationFileUploadedSignal{
			PresentationBatchExecutionId: defaultPresentationBatchExecutionId,
		})
		defaultSendSignalToWorkflowsV2Req = &activityPb.SendSignalToWorkflowsV2Request{
			RequestHeader: &activityPb.RequestHeader{
				Ownership: commontypes.Ownership_EPIFI_TECH,
			},
			SignalId:  string(rpNs.EnachPresentationFileUploadedSignal),
			PayloadV1: fileUploadedSignalPayload,
			WorkflowIdentifiers: &activityPb.SendSignalToWorkflowsV2Request_ClientReqIdList{
				ClientReqIdList: &activityPb.ClientReqIdList{
					ClientReqIds: []*workflowPb.ClientReqId{
						{
							Id:     enachMandateActionId1,
							Client: workflowPb.Client_RECURRING_PAYMENT,
						},
						{
							Id:     enachMandateActionId2,
							Client: workflowPb.Client_RECURRING_PAYMENT,
						},
						{
							Id:     enachMandateActionId3,
							Client: workflowPb.Client_RECURRING_PAYMENT,
						},
					},
				},
			},
		}
	)

	tests := []struct {
		name              string
		skip              bool
		setupMocks        func(env *testsuite.TestWorkflowEnvironment)
		setupExpectations func(t *testing.T, env *testsuite.TestWorkflowEnvironment)
		wantErr           bool
	}{
		{
			name: "Should return error if CreateEnachPresentationFile activity returned error",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(rpNs.CreateEnachPresentationFile), mock.Anything, &enachActivityPb.CreateEnachPresentationFileRequest{}).
					Return(nil, temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(rpNs.CreateEnachPresentationFile), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.SendSignalToWorkflowsV2), 0)
				env.AssertNumberOfCalls(t, string(rpNs.UploadEnachPresentationFileToSFTP), 0)
			},
			wantErr: true,
		},
		{
			name: "Should terminate gracefully if no execution actions are waiting for execution",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(rpNs.CreateEnachPresentationFile), mock.Anything, &enachActivityPb.CreateEnachPresentationFileRequest{}).
					Return(&enachActivityPb.CreateEnachPresentationFileResponse{}, nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(rpNs.CreateEnachPresentationFile), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.SendSignalToWorkflowsV2), 0)
				env.AssertNumberOfCalls(t, string(rpNs.UploadEnachPresentationFileToSFTP), 0)
			},
			wantErr: false,
		},
		{
			name: "Should return error if encounter error while sending file uploaded signal",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(rpNs.CreateEnachPresentationFile), mock.Anything, &enachActivityPb.CreateEnachPresentationFileRequest{}).
					Return(defaultCreateEnachPresentationFileRes, nil)
				env.OnActivity(string(epifitemporal.SendSignalToWorkflowsV2), mock.Anything, defaultSendSignalToWorkflowsV2Req).
					Return(temporalPermanentErr)
			},

			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(rpNs.CreateEnachPresentationFile), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.SendSignalToWorkflowsV2), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UploadEnachPresentationFileToSFTP), 0)
			},
			wantErr: true,
		},
		{
			name: "Should return error if UploadEnachPresentationFileToSFTP activity returned error",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(rpNs.CreateEnachPresentationFile), mock.Anything, &enachActivityPb.CreateEnachPresentationFileRequest{}).
					Return(defaultCreateEnachPresentationFileRes, nil)
				env.OnActivity(string(epifitemporal.SendSignalToWorkflowsV2), mock.Anything, defaultSendSignalToWorkflowsV2Req).
					Return(nil)
				env.OnActivity(string(rpNs.UploadEnachPresentationFileToSFTP), mock.Anything, &enachActivityPb.UploadEnachPresentationFileToSFTPRequest{
					PresentationFileS3Path: defaultPresentationFileS3Path,
					PresentationFileName:   defaultPresentationFileName,
				}).
					Return(nil, temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(rpNs.CreateEnachPresentationFile), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.SendSignalToWorkflowsV2), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UploadEnachPresentationFileToSFTP), 1)
			},
			wantErr: true,
		},
		{
			name: "Should return success if file successfully uploaded to SFTP",
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(rpNs.CreateEnachPresentationFile), mock.Anything, &enachActivityPb.CreateEnachPresentationFileRequest{}).
					Return(defaultCreateEnachPresentationFileRes, nil)
				env.OnActivity(string(epifitemporal.SendSignalToWorkflowsV2), mock.Anything, defaultSendSignalToWorkflowsV2Req).
					Return(nil)
				env.OnActivity(string(rpNs.UploadEnachPresentationFileToSFTP), mock.Anything, &enachActivityPb.UploadEnachPresentationFileToSFTPRequest{
					PresentationFileS3Path: defaultPresentationFileS3Path,
					PresentationFileName:   defaultPresentationFileName,
				}).
					Return(&enachActivityPb.UploadEnachPresentationFileToSFTPResponse{}, nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(rpNs.CreateEnachPresentationFile), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.SendSignalToWorkflowsV2), 1)
				env.AssertNumberOfCalls(t, string(rpNs.UploadEnachPresentationFileToSFTP), 1)
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.skip {
				t.Skip()
			}
			env := wts.NewTestWorkflowEnvironment()
			env.RegisterWorkflow(PresentEnachExecution)
			env.RegisterActivity(&celestialActivityV2.Processor{})
			env.RegisterActivity(&enachActivity.Processor{})

			tt.setupMocks(env)

			env.ExecuteWorkflow(PresentEnachExecution, &enachWorkflowPb.PresentEnachExecutionWorkflowPayload{})

			assert.True(t, env.IsWorkflowCompleted())
			if (env.GetWorkflowError() != nil) != tt.wantErr {
				t.Errorf("PresentEnachExecution() error = %v, wantErr %v", env.GetWorkflowError(), tt.wantErr)
			}

			tt.setupExpectations(t, env)
			env.AssertExpectations(t)
		})
	}
}
