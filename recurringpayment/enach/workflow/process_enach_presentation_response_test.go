package workflow

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/testsuite"
	"google.golang.org/protobuf/encoding/protojson"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	enachActivityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	enachWorkflowPb "github.com/epifi/gamma/api/recurringpayment/enach/workflow"
	celestialActivityV2 "github.com/epifi/gamma/celestial/activity/v2"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	enachActivity "github.com/epifi/gamma/recurringpayment/enach/activity"
	enachConstants "github.com/epifi/gamma/recurringpayment/enach/constants"
)

func TestProcessEnachPresentationResponse(t *testing.T) {
	const (
		defaultPresentationResponseFileS3Path = "presentation/04052023/NACH_DR_04052023_UTILITY_CODE_EpifiTechnologies_001-RES.txt"

		enachMandateActionId1 = "test_enach_mandate_action_id_1"
		enachMandateActionId2 = "test_enach_mandate_action_id_2"
	)

	var (
		currentDayMinusOne                              = time.Now().In(datetime.IST).AddDate(0, 0, -1)
		temporalPermanentErr                            = temporal.NewNonRetryableApplicationError("Permanent Error", "", epifierrors.ErrPermanent)
		defaultDownloadEnachPresentationResponseFileReq = &enachActivityPb.DownloadEnachPresentationResponseFileRequest{
			FileSeqNumber:    1,
			FileMaxSeqNumber: 99,
			FileDateString:   currentDayMinusOne.Format(enachConstants.PresentationFileDateFormat),
		}
		defaultDownloadEnachPresentationResponseFileRes = &enachActivityPb.DownloadEnachPresentationResponseFileResponse{
			PresentationResponseFileS3Path: defaultPresentationResponseFileS3Path,
		}
		defaultParseEnachPresentationResponseFileReq = &enachActivityPb.ParseEnachPresentationResponseFileRequest{
			PresentationResponseFileS3Path: defaultPresentationResponseFileS3Path,
		}
		defaultParseEnachPresentationResponseFileRes = &enachActivityPb.ParseEnachPresentationResponseFileResponse{
			EnachExecutionStatusList: []*enachActivityPb.EnachExecutionStatus{
				{
					ExecutionActionId:  enachMandateActionId1,
					FundTransferStatus: enachEnumsPb.FundTransferStatus_FUND_TRANSFER_STATUS_SUCCESSFUL,
				},
				{
					ExecutionActionId:  enachMandateActionId2,
					FundTransferStatus: enachEnumsPb.FundTransferStatus_FUND_TRANSFER_STATUS_FAILED,
				},
			},
		}

		fundTransferSuccessSignalPayload, _ = protojson.Marshal(&enachWorkflowPb.EnachPoolAccountFundTransferStatusSignal{
			FundTransferStatus: enachEnumsPb.FundTransferStatus_FUND_TRANSFER_STATUS_SUCCESSFUL,
		})
		fundTransferFailureSignalPayload, _ = protojson.Marshal(&enachWorkflowPb.EnachPoolAccountFundTransferStatusSignal{
			FundTransferStatus: enachEnumsPb.FundTransferStatus_FUND_TRANSFER_STATUS_FAILED,
		})
		sendSignalToWorkflowsV2Req1 = &activityPb.SendSignalToWorkflowsV2Request{
			RequestHeader: &activityPb.RequestHeader{
				Ownership: commontypes.Ownership_EPIFI_TECH,
			},
			SignalId:  string(rpNs.EnachPoolAccountFundTransferStatusSignal),
			PayloadV1: fundTransferSuccessSignalPayload,
			WorkflowIdentifiers: &activityPb.SendSignalToWorkflowsV2Request_ClientReqIdList{
				ClientReqIdList: &activityPb.ClientReqIdList{
					ClientReqIds: []*workflowPb.ClientReqId{
						{
							Id:     enachMandateActionId1,
							Client: workflowPb.Client_RECURRING_PAYMENT,
						},
					},
				},
			},
		}
		sendSignalToWorkflowsV2Req2 = &activityPb.SendSignalToWorkflowsV2Request{
			RequestHeader: &activityPb.RequestHeader{
				Ownership: commontypes.Ownership_EPIFI_TECH,
			},
			SignalId:  string(rpNs.EnachPoolAccountFundTransferStatusSignal),
			PayloadV1: fundTransferFailureSignalPayload,
			WorkflowIdentifiers: &activityPb.SendSignalToWorkflowsV2Request_ClientReqIdList{
				ClientReqIdList: &activityPb.ClientReqIdList{
					ClientReqIds: []*workflowPb.ClientReqId{
						{
							Id:     enachMandateActionId2,
							Client: workflowPb.Client_RECURRING_PAYMENT,
						},
					},
				},
			},
		}
		requestPayloadMarshalled, _ = protojson.Marshal(&enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload{SettlementDate: &enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload_RelativeDate_{
			RelativeDate: enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload_CURRENT_DATE_MINUS_ONE,
		}})
		wfProcessingParams = &activityPb.GetWorkflowProcessingParamsV2Response{
			WfReqParams: &workflowPb.ProcessingParams{
				ClientReqId: &workflowPb.ClientReqId{
					Id: "client-request-id",
				},
				Payload: requestPayloadMarshalled,
			},
		}
	)

	type args struct {
		request *enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload
	}

	tests := []struct {
		name              string
		args              args
		skip              bool
		setupMocks        func(env *testsuite.TestWorkflowEnvironment)
		setupExpectations func(t *testing.T, env *testsuite.TestWorkflowEnvironment)
		wantErr           bool
	}{
		{
			name: "Should return error if DownloadEnachPresentationResponseFile activity returned error",
			args: args{
				request: &enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload{SettlementDate: &enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload_RelativeDate_{
					RelativeDate: enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload_CURRENT_DATE_MINUS_ONE,
				}},
			},
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(rpNs.DownloadEnachPresentationResponseFile), mock.Anything, defaultDownloadEnachPresentationResponseFileReq).
					Return(nil, temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(rpNs.DownloadEnachPresentationResponseFile), 1)
				env.AssertNumberOfCalls(t, string(rpNs.ParseEnachPresentationResponseFile), 0)
				env.AssertNumberOfCalls(t, string(epifitemporal.SendSignalToWorkflowsV2), 0)
			},
			wantErr: true,
		},
		{
			name: "Should return error if ParseEnachPresentationResponseFile activity returned error",
			args: args{
				request: &enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload{SettlementDate: &enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload_RelativeDate_{
					RelativeDate: enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload_CURRENT_DATE_MINUS_ONE,
				}},
			},
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(rpNs.DownloadEnachPresentationResponseFile), mock.Anything, defaultDownloadEnachPresentationResponseFileReq).
					Return(defaultDownloadEnachPresentationResponseFileRes, nil)
				env.OnActivity(string(rpNs.ParseEnachPresentationResponseFile), mock.Anything, defaultParseEnachPresentationResponseFileReq).
					Return(nil, temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(rpNs.DownloadEnachPresentationResponseFile), 1)
				env.AssertNumberOfCalls(t, string(rpNs.ParseEnachPresentationResponseFile), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.SendSignalToWorkflowsV2), 0)
			},
			wantErr: true,
		},
		{
			name: "Should return error if encounter error while sending file uploaded signal",
			args: args{
				request: &enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload{SettlementDate: &enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload_RelativeDate_{
					RelativeDate: enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload_CURRENT_DATE_MINUS_ONE,
				}},
			},
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(rpNs.DownloadEnachPresentationResponseFile), mock.Anything, defaultDownloadEnachPresentationResponseFileReq).
					Return(defaultDownloadEnachPresentationResponseFileRes, nil)
				env.OnActivity(string(rpNs.ParseEnachPresentationResponseFile), mock.Anything, defaultParseEnachPresentationResponseFileReq).
					Return(defaultParseEnachPresentationResponseFileRes, nil)
				env.OnActivity(string(epifitemporal.SendSignalToWorkflowsV2), mock.Anything, mock.Anything).
					Return(temporalPermanentErr).Times(2)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(rpNs.DownloadEnachPresentationResponseFile), 1)
				env.AssertNumberOfCalls(t, string(rpNs.ParseEnachPresentationResponseFile), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.SendSignalToWorkflowsV2), 2)
			},
			wantErr: true,
		},
		{
			name: "Should return success if all workflows signalled successfully",
			args: args{
				request: &enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload{SettlementDate: &enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload_RelativeDate_{
					RelativeDate: enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload_CURRENT_DATE_MINUS_ONE,
				}},
			},
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(rpNs.DownloadEnachPresentationResponseFile), mock.Anything, defaultDownloadEnachPresentationResponseFileReq).
					Return(defaultDownloadEnachPresentationResponseFileRes, nil)
				env.OnActivity(string(rpNs.ParseEnachPresentationResponseFile), mock.Anything, defaultParseEnachPresentationResponseFileReq).
					Return(defaultParseEnachPresentationResponseFileRes, nil)
				env.OnActivity(string(epifitemporal.SendSignalToWorkflowsV2), mock.Anything, sendSignalToWorkflowsV2Req1).
					Return(nil)
				env.OnActivity(string(epifitemporal.SendSignalToWorkflowsV2), mock.Anything, sendSignalToWorkflowsV2Req2).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(rpNs.DownloadEnachPresentationResponseFile), 1)
				env.AssertNumberOfCalls(t, string(rpNs.ParseEnachPresentationResponseFile), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.SendSignalToWorkflowsV2), 2)
			},
			wantErr: false,
		},
		{
			name: "Should return success if all workflows are signalled successfully if the workflow payload was fetched from celestial",
			args: args{
				request: nil,
			},
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, mock.Anything).Return(wfProcessingParams, nil)
				env.OnActivity(string(rpNs.DownloadEnachPresentationResponseFile), mock.Anything, defaultDownloadEnachPresentationResponseFileReq).
					Return(defaultDownloadEnachPresentationResponseFileRes, nil)
				env.OnActivity(string(rpNs.ParseEnachPresentationResponseFile), mock.Anything, defaultParseEnachPresentationResponseFileReq).
					Return(defaultParseEnachPresentationResponseFileRes, nil)
				env.OnActivity(string(epifitemporal.SendSignalToWorkflowsV2), mock.Anything, sendSignalToWorkflowsV2Req1).
					Return(nil)
				env.OnActivity(string(epifitemporal.SendSignalToWorkflowsV2), mock.Anything, sendSignalToWorkflowsV2Req2).
					Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(rpNs.DownloadEnachPresentationResponseFile), 1)
				env.AssertNumberOfCalls(t, string(rpNs.ParseEnachPresentationResponseFile), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.SendSignalToWorkflowsV2), 2)
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.skip {
				t.Skip()
			}
			env := wts.NewTestWorkflowEnvironment()
			env.RegisterWorkflow(ProcessEnachPresentationResponse)
			env.RegisterActivity(&celestialActivityV2.Processor{})
			env.RegisterActivity(&enachActivity.Processor{})

			tt.setupMocks(env)

			env.ExecuteWorkflow(ProcessEnachPresentationResponse, tt.args.request)

			assert.True(t, env.IsWorkflowCompleted())
			if (env.GetWorkflowError() != nil) != tt.wantErr {
				t.Errorf("ProcessEnachPresentationResponse() error = %v, wantErr %v", env.GetWorkflowError(), tt.wantErr)
			}

			tt.setupExpectations(t, env)
			env.AssertExpectations(t)
		})
	}
}
