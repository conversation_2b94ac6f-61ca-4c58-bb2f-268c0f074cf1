package workflow

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/testsuite"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/timestamppb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	enachActivityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	"github.com/epifi/gamma/api/recurringpayment/enach/enums"
	enachWorkflowPb "github.com/epifi/gamma/api/recurringpayment/enach/workflow"
	celestialActivityV2 "github.com/epifi/gamma/celestial/activity/v2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	enachActivity "github.com/epifi/gamma/recurringpayment/enach/activity"
)

func TestProcessEnachPresentationAckFile(t *testing.T) {

	const (
		presentationAckFileS3Path = "presentation/01102023/NACH_DR_01102023_UTILITY_CODE_EpifiTechnologies_001-ACK.txt"
		enachMandateId1           = "enach-mandate-id-1"
		enachMandateId2           = "enach-mandate-id-2"
	)
	type args struct {
		request *enachWorkflowPb.ProcessEnachPresentationAckFileWorkflowPayload
	}

	var (
		temporalPermanentErr                = temporal.NewNonRetryableApplicationError("Permanent Error", "", epifierrors.ErrPermanent)
		downloadEnachPresentationAckFileReq = &enachActivityPb.DownloadEnachPresentationAckFileRequest{
			SettlementDate: timestamppb.New(time.Date(2023, time.October, 1, 0, 0, 0, 0, time.UTC)),
		}
		downloadEnachPresentationAckFileRes = &enachActivityPb.DownloadEnachPresentationAckFileResponse{
			PresentationAckFileS3Path: presentationAckFileS3Path,
		}
		parseAndProcessEnachPresentationAckFileReq = &enachActivityPb.ParseAndProcessEnachPresentationAckFileRequest{
			PresentationAckFileS3Path: presentationAckFileS3Path,
		}
		parseEnachPresentationResponseFileRes = &enachActivityPb.ParseAndProcessEnachPresentationAckFileResponse{
			FailedEnachExecutionActionIds: []string{enachMandateId1, enachMandateId2},
		}
		signalPayload = &enachWorkflowPb.EnachPoolAccountFundTransferStatusSignal{
			FundTransferStatus: enums.FundTransferStatus_FUND_TRANSFER_STATUS_FAILED,
		}
		signalPayloadMarshalled, _ = protojson.Marshal(signalPayload)

		sendSignalToWorkflowsV2Req1 = &activityPb.SendSignalToWorkflowsV2Request{
			RequestHeader: &activityPb.RequestHeader{
				Ownership: commontypes.Ownership_EPIFI_TECH,
			},
			SignalId:  string(rpNs.EnachPoolAccountFundTransferStatusSignal),
			PayloadV1: signalPayloadMarshalled,
			WorkflowIdentifiers: &activityPb.SendSignalToWorkflowsV2Request_ClientReqIdList{
				ClientReqIdList: &activityPb.ClientReqIdList{
					ClientReqIds: []*workflowPb.ClientReqId{
						{
							Id:     enachMandateId1,
							Client: workflowPb.Client_RECURRING_PAYMENT,
						},
						{
							Id:     enachMandateId2,
							Client: workflowPb.Client_RECURRING_PAYMENT,
						},
					},
				},
			},
		}
		requestPayloadMarshalled, _ = protojson.Marshal(downloadEnachPresentationAckFileReq)
		wfProcessingParams          = &activityPb.GetWorkflowProcessingParamsV2Response{
			WfReqParams: &workflowPb.ProcessingParams{
				ClientReqId: &workflowPb.ClientReqId{
					Id: "client-request-id",
				},
				Payload: requestPayloadMarshalled,
			},
		}
	)

	tests := []struct {
		name              string
		args              args
		skip              bool
		setupMocks        func(env *testsuite.TestWorkflowEnvironment)
		setupExpectations func(t *testing.T, env *testsuite.TestWorkflowEnvironment)
		wantErr           bool
	}{
		{
			name: "Should return error if DownloadEnachPresentationAckFile activity returned error",
			args: args{
				request: &enachWorkflowPb.ProcessEnachPresentationAckFileWorkflowPayload{
					SettlementDate: timestamppb.New(time.Date(2023, time.October, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(rpNs.DownloadEnachPresentationAckFile), mock.Anything, downloadEnachPresentationAckFileReq).
					Return(nil, temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(rpNs.DownloadEnachPresentationAckFile), 1)
				env.AssertNumberOfCalls(t, string(rpNs.ParseAndProcessEnachPresentationAckFile), 0)
				env.AssertNumberOfCalls(t, string(epifitemporal.SendSignalToWorkflowsV2), 0)
			},
			wantErr: true,
		},
		{
			name: "Should return error if ParseEnachPresentationResponseFile activity returned error",
			args: args{
				request: &enachWorkflowPb.ProcessEnachPresentationAckFileWorkflowPayload{
					SettlementDate: timestamppb.New(time.Date(2023, time.October, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(rpNs.DownloadEnachPresentationAckFile), mock.Anything, downloadEnachPresentationAckFileReq).
					Return(downloadEnachPresentationAckFileRes, nil)
				env.OnActivity(string(rpNs.ParseAndProcessEnachPresentationAckFile), mock.Anything, parseAndProcessEnachPresentationAckFileReq).
					Return(nil, temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(rpNs.DownloadEnachPresentationAckFile), 1)
				env.AssertNumberOfCalls(t, string(rpNs.ParseAndProcessEnachPresentationAckFile), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.SendSignalToWorkflowsV2), 0)
			},
			wantErr: true,
		},
		{
			name: "Should return error if encounter error while sending file uploaded signal",
			args: args{
				request: &enachWorkflowPb.ProcessEnachPresentationAckFileWorkflowPayload{
					SettlementDate: timestamppb.New(time.Date(2023, time.October, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(rpNs.DownloadEnachPresentationAckFile), mock.Anything, downloadEnachPresentationAckFileReq).
					Return(downloadEnachPresentationAckFileRes, nil)
				env.OnActivity(string(rpNs.ParseAndProcessEnachPresentationAckFile), mock.Anything, parseAndProcessEnachPresentationAckFileReq).
					Return(parseEnachPresentationResponseFileRes, nil)
				env.OnActivity(string(epifitemporal.SendSignalToWorkflowsV2), mock.Anything, sendSignalToWorkflowsV2Req1).Return(temporalPermanentErr)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(rpNs.DownloadEnachPresentationAckFile), 1)
				env.AssertNumberOfCalls(t, string(rpNs.ParseAndProcessEnachPresentationAckFile), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.SendSignalToWorkflowsV2), 1)
			},
			wantErr: true,
		},
		{
			name: "Should return success if all workflows are signalled successfully",
			args: args{
				request: &enachWorkflowPb.ProcessEnachPresentationAckFileWorkflowPayload{
					SettlementDate: timestamppb.New(time.Date(2023, time.October, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(rpNs.DownloadEnachPresentationAckFile), mock.Anything, downloadEnachPresentationAckFileReq).
					Return(downloadEnachPresentationAckFileRes, nil)
				env.OnActivity(string(rpNs.ParseAndProcessEnachPresentationAckFile), mock.Anything, parseAndProcessEnachPresentationAckFileReq).
					Return(parseEnachPresentationResponseFileRes, nil)
				env.OnActivity(string(epifitemporal.SendSignalToWorkflowsV2), mock.Anything, sendSignalToWorkflowsV2Req1).Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(rpNs.DownloadEnachPresentationAckFile), 1)
				env.AssertNumberOfCalls(t, string(rpNs.ParseAndProcessEnachPresentationAckFile), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.SendSignalToWorkflowsV2), 1)
			},
			wantErr: false,
		},
		{
			name: "Should return success if all workflows are signalled successfully if the workflow payload was fetched from celestial",
			args: args{
				request: nil,
			},
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, mock.Anything).Return(wfProcessingParams, nil)
				env.OnActivity(string(rpNs.DownloadEnachPresentationAckFile), mock.Anything, downloadEnachPresentationAckFileReq).
					Return(downloadEnachPresentationAckFileRes, nil)
				env.OnActivity(string(rpNs.ParseAndProcessEnachPresentationAckFile), mock.Anything, parseAndProcessEnachPresentationAckFileReq).
					Return(parseEnachPresentationResponseFileRes, nil)
				env.OnActivity(string(epifitemporal.SendSignalToWorkflowsV2), mock.Anything, sendSignalToWorkflowsV2Req1).Return(nil)
			},
			setupExpectations: func(t *testing.T, env *testsuite.TestWorkflowEnvironment) {
				env.AssertNumberOfCalls(t, string(epifitemporal.GetWorkflowProcessingParamsV2), 1)
				env.AssertNumberOfCalls(t, string(rpNs.DownloadEnachPresentationAckFile), 1)
				env.AssertNumberOfCalls(t, string(rpNs.ParseAndProcessEnachPresentationAckFile), 1)
				env.AssertNumberOfCalls(t, string(epifitemporal.SendSignalToWorkflowsV2), 1)
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.skip {
				t.Skip()
			}
			env := wts.NewTestWorkflowEnvironment()
			env.RegisterWorkflow(ProcessEnachPresentationAckFile)
			env.RegisterActivity(&celestialActivityV2.Processor{})
			env.RegisterActivity(&enachActivity.Processor{})

			tt.setupMocks(env)

			env.ExecuteWorkflow(ProcessEnachPresentationAckFile, tt.args.request)

			assert.True(t, env.IsWorkflowCompleted())
			if (env.GetWorkflowError() != nil) != tt.wantErr {
				t.Errorf("ProcessEnachPresentationAckFile() error = %v, wantErr %v", env.GetWorkflowError(), tt.wantErr)
			}

			tt.setupExpectations(t, env)
			env.AssertExpectations(t)
		})
	}
}
