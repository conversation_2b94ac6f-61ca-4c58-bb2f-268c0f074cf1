package workflow

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/logger"
	enachActivityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	enachWorkflowPb "github.com/epifi/gamma/api/recurringpayment/enach/workflow"
	enachConstants "github.com/epifi/gamma/recurringpayment/enach/constants"
)

// ProcessEnachPresentationResponse is responsible for performing all the necessary processing once the processed enach file
// is received from vendor.
// This on a high level involves the following steps
// 1. Download the enach execution file from vendor's SFTP server
// 2. Parse the downloaded file
// 3. Send signal to corresponding workflows on the execution status of the first leg transaction of the enach fund transfer
func ProcessEnachPresentationResponse(ctx workflow.Context, req *enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload) error {
	lg := workflow.GetLogger(ctx)

	// **Note**:  In the actual flow this workflow is scheduled directly using temporal sdk so request field would be non null
	// but in integration test suite this workflow is triggered using celestial rpcs and the request field would be null in
	// such a case (celestial workflow payload needs to be manually unmarshalled) so the following block fetches the request
	// payload from celestial service.
	if req == nil {
		wfPayload := &enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload{}
		_, err := celestialPkg.GetWorkflowProcessingReqParams(ctx, wfPayload)
		if err != nil {
			lg.Error("error in getting workflow processing params", zap.Error(err))
			return fmt.Errorf("error in getting workflow processing params: %w", err)
		}
		req = wfPayload
	}

	downloadPresentationResponseFileRes := &enachActivityPb.DownloadEnachPresentationResponseFileResponse{}
	fileDateString, err := getFileDateString(ctx, req)
	if err != nil {
		lg.Error("error generating settlement date", zap.Any(logger.REQUEST, req), zap.Error(err))
		return fmt.Errorf("error generating settlement date, %w", err)
	}

	err = activityPkg.Execute(ctx, rpNs.DownloadEnachPresentationResponseFile, downloadPresentationResponseFileRes, &enachActivityPb.DownloadEnachPresentationResponseFileRequest{
		FileSeqNumber:    1,
		FileMaxSeqNumber: 99,
		FileDateString:   fileDateString,
	})
	if err != nil {
		lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(rpNs.DownloadEnachPresentationResponseFile)), zap.String("fileDate", fileDateString), zap.Error(err))
		return fmt.Errorf("%s activity initiation failed: error %w", rpNs.DownloadEnachPresentationResponseFile, err)
	}

	parsePresentationResponseFileRes := &enachActivityPb.ParseEnachPresentationResponseFileResponse{}
	err = activityPkg.Execute(ctx, rpNs.ParseEnachPresentationResponseFile, parsePresentationResponseFileRes, &enachActivityPb.ParseEnachPresentationResponseFileRequest{
		PresentationResponseFileS3Path: downloadPresentationResponseFileRes.GetPresentationResponseFileS3Path(),
	})
	if err != nil {
		lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(rpNs.ParseEnachPresentationResponseFile)), zap.String(logger.S3_PATH, downloadPresentationResponseFileRes.GetPresentationResponseFileS3Path()), zap.Error(err))
		return fmt.Errorf("%s activity initiation failed: error %w", rpNs.ParseEnachPresentationResponseFile, err)
	}

	err = sendExecutionStatusSignalToEnachExecutionWorkflow(ctx, parsePresentationResponseFileRes.GetEnachExecutionStatusList())
	if err != nil {
		lg.Error("failed sending execution status signal to enach execution workflow", zap.String("presentationResponseFileS3Path", downloadPresentationResponseFileRes.GetPresentationResponseFileS3Path()), zap.Any("executionStatusList", parsePresentationResponseFileRes.GetEnachExecutionStatusList()), zap.Error(err))
		return err
	}

	return nil
}

// sendExecutionStatusSignalToEnachExecutionWorkflow This function does two things:
// 1. Update the enach workflows that the presentation process completed with the final execution status for each execution
// 2. Todo(Vishal): [Incremental] Make sure that all the enach workflows status have been updated
func sendExecutionStatusSignalToEnachExecutionWorkflow(ctx workflow.Context, executionStatusList []*enachActivityPb.EnachExecutionStatus) error {
	var (
		lg                 = workflow.GetLogger(ctx)
		failedClientReqIds []string
	)

	// Update the enach workflows that the presentation process completed with the final execution status for each execution
	for _, executionStatusEntry := range executionStatusList {
		signalPayload := &enachWorkflowPb.EnachPoolAccountFundTransferStatusSignal{
			FundTransferStatus:        executionStatusEntry.GetFundTransferStatus(),
			NachTransactionFlag:       executionStatusEntry.GetNachTransactionFlag(),
			NachTransactionReasonCode: executionStatusEntry.GetNachTransactionResponseReasonCode(),
			FiStatusCode:              executionStatusEntry.GetFiStatusCode(),
			FiStatusDesc:              executionStatusEntry.GetFiStatusDesc(),
		}

		signalPayloadMarshalled, err := protojson.Marshal(signalPayload)
		if err != nil {
			lg.Error("failed to marshal signal payload for EnachPoolAccountFundTransferStatusSignal", zap.Any(logger.PAYLOAD, signalPayload), zap.String(logger.ACTION_ID, executionStatusEntry.GetExecutionActionId()))
			return fmt.Errorf("signal marshalling failed for %s: error %w", rpNs.EnachPoolAccountFundTransferStatusSignal, err)
		}

		sendSignalRes := &activityPb.SendSignalToWorkflowsV2Response{}
		if err = activityPkg.Execute(ctx, epifitemporal.SendSignalToWorkflowsV2, sendSignalRes, &activityPb.SendSignalToWorkflowsV2Request{
			RequestHeader: &activityPb.RequestHeader{
				Ownership: commontypes.Ownership_EPIFI_TECH,
			},
			SignalId:  string(rpNs.EnachPoolAccountFundTransferStatusSignal),
			PayloadV1: signalPayloadMarshalled,
			WorkflowIdentifiers: &activityPb.SendSignalToWorkflowsV2Request_ClientReqIdList{
				ClientReqIdList: &activityPb.ClientReqIdList{
					ClientReqIds: []*workflowPb.ClientReqId{
						{
							Id:     executionStatusEntry.GetExecutionActionId(),
							Client: workflowPb.Client_RECURRING_PAYMENT,
						},
					},
				},
			},
		}); err != nil {
			lg.Error("failed to send fund transfer status signal to enach execution workflow", zap.String(logger.ACTION_ID, executionStatusEntry.GetExecutionActionId()), zap.String(logger.STATUS, executionStatusEntry.GetFundTransferStatus().String()), zap.Error(err))
			failedClientReqIds = append(failedClientReqIds, executionStatusEntry.GetExecutionActionId())
			continue
		}
		lg.Info("successfully signalled pool transfer status to enach execution workflow", zap.String(logger.ACTION_ID, executionStatusEntry.GetExecutionActionId()), zap.String(logger.STATUS, executionStatusEntry.GetFundTransferStatus().String()))
	}

	if len(failedClientReqIds) != 0 {
		return fmt.Errorf("failed sending fund transfer status signal to enach execution workflow for the clientReqIds %s", failedClientReqIds)
	}

	return nil
}

func getFileDateString(ctx workflow.Context, req *enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload) (string, error) {
	currTime := workflow.Now(ctx).In(datetime.IST)

	// if absolute date is present use that, otherwise fallback use relative date
	if req.GetAbsoluteDate() != nil {
		return req.GetAbsoluteDate().AsTime().Format(enachConstants.PresentationFileDateFormat), nil
	}

	switch req.GetRelativeDate() {
	case enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload_CURRENT_DATE:
		return currTime.Format(enachConstants.PresentationFileDateFormat), nil
	case enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload_CURRENT_DATE_MINUS_ONE:
		return currTime.AddDate(0, 0, -1).Format(enachConstants.PresentationFileDateFormat), nil
	default:
		return "", fmt.Errorf("unhandled settlement day %v", req.GetRelativeDate())
	}
}
