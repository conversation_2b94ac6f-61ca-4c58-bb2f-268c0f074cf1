package workflow

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/timestamppb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/logger"
	enachActivityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	"github.com/epifi/gamma/api/recurringpayment/enach/enums"
	enachWorkflowPb "github.com/epifi/gamma/api/recurringpayment/enach/workflow"
)

func ProcessEnachPresentationAckFile(ctx workflow.Context, req *enachWorkflowPb.ProcessEnachPresentationAckFileWorkflowPayload) error {
	lg := workflow.GetLogger(ctx)

	// **Note**:  In the actual flow this workflow is scheduled directly using temporal sdk so request field would be non null
	// but in integration test suite this workflow is triggered using celestial rpcs and the request field would be null in
	// such a case (celestial workflow payload needs to be manually unmarshalled) so the following block fetches the request
	// payload from celestial service.
	if req == nil {
		wfPayload := &enachWorkflowPb.ProcessEnachPresentationAckFileWorkflowPayload{}
		_, err := celestialPkg.GetWorkflowProcessingReqParams(ctx, wfPayload)
		if err != nil {
			lg.Error("error in getting workflow processing params", zap.Error(err))
			return fmt.Errorf("error in getting workflow processing params: %w", err)
		}

		req = wfPayload
	}

	downloadPresentationResponseFileRes := &enachActivityPb.DownloadEnachPresentationAckFileResponse{}
	settlementDate := getSettlementDate(ctx, req.GetSettlementDate())
	err := activityPkg.Execute(ctx, rpNs.DownloadEnachPresentationAckFile, downloadPresentationResponseFileRes, &enachActivityPb.DownloadEnachPresentationAckFileRequest{
		SettlementDate: settlementDate,
	})
	if err != nil {
		lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(rpNs.DownloadEnachPresentationAckFile)), zap.String("settlementDateString", settlementDate.String()), zap.Error(err))
		return fmt.Errorf("%s activity initiation failed: error %w", rpNs.DownloadEnachPresentationAckFile, err)
	}

	parsePresentationAckResponseFileRes := &enachActivityPb.ParseAndProcessEnachPresentationAckFileResponse{}
	err = activityPkg.Execute(ctx, rpNs.ParseAndProcessEnachPresentationAckFile, parsePresentationAckResponseFileRes, &enachActivityPb.ParseAndProcessEnachPresentationAckFileRequest{
		PresentationAckFileS3Path: downloadPresentationResponseFileRes.GetPresentationAckFileS3Path(),
	})
	if err != nil {
		lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(rpNs.ParseAndProcessEnachPresentationAckFile)), zap.String(logger.S3_PATH, downloadPresentationResponseFileRes.GetPresentationAckFileS3Path()), zap.Error(err))
		return fmt.Errorf("%s activity initiation failed: error %w", rpNs.ParseAndProcessEnachPresentationAckFile, err)
	}

	err = sendFailedExecutionStatusSignalToEnachExecutionWorkflow(ctx, parsePresentationAckResponseFileRes.GetFailedEnachExecutionActionIds())
	if err != nil {
		lg.Error("error sending failed execution status signal to enach execution workflow", zap.Any(logger.ID, parsePresentationAckResponseFileRes.GetFailedEnachExecutionActionIds()), zap.Error(err))
		return err
	}

	return nil
}

func sendFailedExecutionStatusSignalToEnachExecutionWorkflow(ctx workflow.Context, enachExecutionActionIds []string) error {
	if len(enachExecutionActionIds) == 0 {
		return nil
	}

	lg := workflow.GetLogger(ctx)

	signalPayload := &enachWorkflowPb.EnachPoolAccountFundTransferStatusSignal{
		FundTransferStatus: enums.FundTransferStatus_FUND_TRANSFER_STATUS_FAILED,
	}

	signalPayloadMarshalled, err := protojson.Marshal(signalPayload)
	if err != nil {
		lg.Info("failed to marshal the signal payload", zap.String(logger.PAYLOAD, signalPayload.String()), zap.Error(err))
		return fmt.Errorf("signal marshal failed for %s: error %w", rpNs.EnachPoolAccountFundTransferStatusSignal, err)
	}

	// Update the enach workflows that the presentation process completed with the final execution status for each execution
	var clientReqIds []*workflowPb.ClientReqId
	for _, enachExecutionActionId := range enachExecutionActionIds {
		clientReqIds = append(clientReqIds, &workflowPb.ClientReqId{
			Id:     enachExecutionActionId,
			Client: workflowPb.Client_RECURRING_PAYMENT,
		})
	}

	sendSignalRes := &activityPb.SendSignalToWorkflowsV2Response{}
	if err = activityPkg.Execute(ctx, epifitemporal.SendSignalToWorkflowsV2, sendSignalRes, &activityPb.SendSignalToWorkflowsV2Request{
		RequestHeader: &activityPb.RequestHeader{
			Ownership: commontypes.Ownership_EPIFI_TECH,
		},
		SignalId:  string(rpNs.EnachPoolAccountFundTransferStatusSignal),
		PayloadV1: signalPayloadMarshalled,
		WorkflowIdentifiers: &activityPb.SendSignalToWorkflowsV2Request_ClientReqIdList{
			ClientReqIdList: &activityPb.ClientReqIdList{
				ClientReqIds: clientReqIds,
			},
		},
	}); err != nil {
		return fmt.Errorf("%s activity initiation failed: error %w", epifitemporal.SendSignalToWorkflowsV2, err)
	}
	lg.Info("Successfully signalled pool transfer status to all workflow.", zap.Strings(logger.CLIENT_REQUEST_IDS, enachExecutionActionIds), zap.Error(err))

	return nil
}

func getSettlementDate(ctx workflow.Context, timestamp *timestamppb.Timestamp) *timestamppb.Timestamp {
	if timestamp != nil {
		return timestamp
	}
	currTime := workflow.Now(ctx).In(datetime.IST)
	return timestamppb.New(currTime)
}
