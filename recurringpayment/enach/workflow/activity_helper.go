package workflow

import (
	"fmt"

	"go.temporal.io/sdk/workflow"

	celestialActivityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	recurringPaymentsPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachActivityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
)

func getEnachMandate(ctx workflow.Context, _ *workflowPb.ProcessingParams, mandateId string) (*enachPb.EnachMandate, error) {
	mandateRes := &enachActivityPb.GetEnachMandateResponse{}
	if err := activityPkg.Execute(ctx, rpNs.GetEnachMandate, mandateRes, &enachActivityPb.GetEnachMandateRequest{
		EnachMandateId: mandateId,
	}); err != nil {
		return nil, fmt.Errorf("activity failed to execute activity, activityName : %s, err : %w", string(rpNs.GetEnachMandate), err)
	}
	return mandateRes.GetEnachMandate(), nil
}

func getEnachMandateAction(ctx workflow.Context, _ *workflowPb.ProcessingParams, mandateActionId string) (*enachPb.EnachMandateAction, error) {
	mandateActionRes := &enachActivityPb.GetEnachMandateActionResponse{}
	if err := activityPkg.Execute(ctx, rpNs.GetEnachMandateAction, mandateActionRes, &enachActivityPb.GetEnachMandateActionRequest{
		EnachMandateActionId: mandateActionId,
	}); err != nil {
		return nil, fmt.Errorf("activity failed to execute activity, activityName : %s, err : %w", string(rpNs.GetEnachMandateAction), err)
	}
	return mandateActionRes.GetEnachMandateAction(), nil
}

func getRecurringPayment(ctx workflow.Context, _ *workflowPb.ProcessingParams, recurringPaymentId string) (*recurringPaymentsPb.RecurringPayment, error) {
	recurringPaymentRes := &rpActivityPb.GetRecurringPaymentResponse{}
	if err := activityPkg.Execute(ctx, rpNs.GetRecurringPayment, recurringPaymentRes, &rpActivityPb.GetRecurringPaymentRequest{
		RecurringPaymentId: recurringPaymentId,
	}); err != nil {
		return nil, fmt.Errorf("activity failed to execute activity, activityName : %s, err : %w", string(rpNs.GetRecurringPayment), err)
	}
	return recurringPaymentRes.GetRecurringPayment(), nil
}

func updateOrderStatus(ctx workflow.Context, orderClientReqId string, newStatus orderPb.OrderStatus, publishOrderUpdate bool) error {
	// nolint: gocritic
	// **Note** : intentionally using activityPkg.ExecuteRaw instead of activityPkg.Execute as UpdateOrder activity contract is following older conventions which is not supported by activityPkg.Execute method.
	if err := activityPkg.ExecuteRaw(ctx, epifitemporal.UpdateOrder, nil, orderClientReqId, newStatus); err != nil {
		return fmt.Errorf("activity failed to execute activity, activityName : %s, err : %w", string(epifitemporal.UpdateOrder), err)
	}
	if publishOrderUpdate {
		// publish order update event
		if err := activityPkg.Execute(ctx, epifitemporal.PublishOrderUpdate, &celestialActivityPb.Response{}, &celestialActivityPb.Request{
			RequestHeader: &celestialActivityPb.RequestHeader{
				ClientReqId: orderClientReqId,
				Ownership:   epificontext.OwnershipFromContext(ctx),
			},
		}); err != nil {
			return fmt.Errorf("activity failed to execute activity, activityName : %s, err : %w", string(epifitemporal.PublishOrderUpdate), err)
		}
	}
	return nil
}

func updateTxnStatus(ctx workflow.Context, txn *paymentPb.Transaction, currentStatus, newStatus paymentPb.TransactionStatus) error {
	if err := activityPkg.Execute(ctx, rpNs.UpdateTransaction, &rpActivityPb.UpdateTransactionResponse{}, &rpActivityPb.UpdateTransactionRequest{
		Transaction:   txn,
		ReqInfo:       &paymentPb.PaymentRequestInformation{ReqId: txn.GetReqId()},
		CurrentStatus: currentStatus,
		NextStatus:    newStatus,
	}); err != nil {
		return fmt.Errorf("activity failed to execute activity, activityName : %s, err : %w", string(rpNs.UpdateTransaction), err)
	}
	return nil
}

func updateEnachMandateActionStatus(ctx workflow.Context, _ *workflowPb.ProcessingParams, mandateActionId string, expectedCurrentStatus, newStatus enachEnumsPb.EnachActionStatus, expectedCurrentSubStatus, newSubStatus enachEnumsPb.EnachActionSubStatus, actionDetailedStatus *enachPb.ActionDetailedStatus) error {
	if err := activityPkg.Execute(ctx, rpNs.UpdateEnachMandateActionStatus, &enachActivityPb.UpdateEnachMandateActionStatusResponse{}, &enachActivityPb.UpdateEnachMandateActionStatusRequest{
		EnachMandateActionId:     mandateActionId,
		ExpectedCurrentStatus:    expectedCurrentStatus,
		ExpectedCurrentSubStatus: expectedCurrentSubStatus,
		NewStatus:                newStatus,
		NewSubStatus:             newSubStatus,
		ActionDetailedStatus:     actionDetailedStatus,
	}); err != nil {
		return fmt.Errorf("activity failed to execute activity, activityName : %s, err : %w", string(rpNs.UpdateEnachMandateActionStatus), err)
	}
	return nil
}
