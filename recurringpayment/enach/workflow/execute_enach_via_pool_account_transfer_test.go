package workflow

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.temporal.io/sdk/testsuite"
	"google.golang.org/protobuf/encoding/protojson"

	celestialActivityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epifitemporal"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	moneyPb "github.com/epifi/be-common/pkg/money"
	orderPb "github.com/epifi/gamma/api/order"
	orderActivityPb "github.com/epifi/gamma/api/order/activity"
	"github.com/epifi/gamma/api/order/payment"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachActivityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	enachWorkflowPb "github.com/epifi/gamma/api/recurringpayment/enach/workflow"
	celestialActivity "github.com/epifi/gamma/celestial/activity"
	celestialActivityV2 "github.com/epifi/gamma/celestial/activity/v2"
	orderActivity "github.com/epifi/gamma/order/activity"
	payPkg "github.com/epifi/gamma/pkg/pay"
	rpActivity "github.com/epifi/gamma/recurringpayment/activity"
	enachActivity "github.com/epifi/gamma/recurringpayment/enach/activity"
)

func TestExecuteEnachViaPoolAccountTransfer(t *testing.T) {
	const (
		defaultWorkflowRequestId            = "default-test-workflow-id"
		defaultWorkflowId                   = "default-test-workflow-id"
		defaultWorkflowClientRequestId      = "default-workflow-client-request-id"
		defaultPresentationBatchExecutionId = "default-presentation-batch-execution-id"

		recurringPaymentId = "recurring-payment-id-1"
		mandateId          = "mandate-id-1"
		mandateActionId    = "mandate-action-id-1"
		orderId            = "order-id-1"

		oneHourDuration   = 1 * time.Hour
		twoHourHour       = 2 * time.Hour
		sevenDaysDuration = 7 * 24 * time.Hour
	)

	var (
		recurringPayment1 = &rpPb.RecurringPayment{
			Id:          recurringPaymentId,
			PiFrom:      "pi-from-1",
			PiTo:        "pi-to-1",
			FromActorId: "from-actor-1",
			ToActorId:   "to-actor-1",
		}
		enachMandate1 = &enachPb.EnachMandate{
			Id:                     mandateId,
			Umrn:                   "umrn-1",
			RecurringPaymentId:     recurringPayment1.GetId(),
			RegistrationAuthMode:   enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
			RegistrationProvenance: enachEnumsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_FI_APP,
		}
		enachMandateAction1 = &enachPb.EnachMandateAction{
			Id:              mandateActionId,
			ClientRequestId: "client-request-id-1",
			EnachMandateId:  enachMandate1.GetId(),
			ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE,
			ActionStatus:    enachEnumsPb.EnachActionStatus_ACTION_STATUS_CREATED,
			VendorRequestId: "vendor-req-id-1",
			ActionMetadata: &enachPb.ActionMetadata{
				ActionTypeSpecificMetadata: &enachPb.ActionMetadata_ExecuteMetadata{
					ExecuteMetadata: &enachPb.ActionMetadata_ExecuteActionMetadata{
						Amount: moneyPb.AmountINR(15000).GetPb(),
					},
				}},
		}
		wfReq = &enachWorkflowPb.ExecuteEnachViaPoolAccountTransferWorkflowPayload{MandateActionId: mandateActionId}
	)

	wfReqMarshalledPayload, _ := protojson.Marshal(wfReq)
	defaultEnachPresentationFileUploadedSignalPayload, _ := protojson.Marshal(&enachWorkflowPb.EnachPresentationFileUploadedSignal{
		PresentationBatchExecutionId: defaultPresentationBatchExecutionId,
	})
	poolAccountFundTransferSuccessfulSignalPayload, _ := protojson.Marshal(&enachWorkflowPb.EnachPoolAccountFundTransferStatusSignal{FundTransferStatus: enachEnumsPb.FundTransferStatus_FUND_TRANSFER_STATUS_SUCCESSFUL})
	poolAccountFundTransferFailedSignalPayload, _ := protojson.Marshal(&enachWorkflowPb.EnachPoolAccountFundTransferStatusSignal{
		FundTransferStatus:        enachEnumsPb.FundTransferStatus_FUND_TRANSFER_STATUS_FAILED,
		NachTransactionFlag:       "0",
		NachTransactionReasonCode: "21",
		FiStatusCode:              "ENACH_EXEC_001",
		FiStatusDesc:              "Invalid UMRN or inactive mandate",
	})

	tests := []struct {
		name       string
		req        *enachWorkflowPb.ExecuteEnachViaPoolAccountTransferWorkflowPayload
		wantErr    bool
		setupMocks func(env *testsuite.TestWorkflowEnvironment)
	}{
		{
			name: "should update EnachOrderCreation stage to MANUAL_INTERVENTION state when order creation activity fails",
			req:  wfReq,
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &celestialActivityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &celestialActivityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowRequestId,
				}).Return(&celestialActivityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						Payload: wfReqMarshalledPayload,
						ClientReqId: &workflowPb.ClientReqId{
							Id:     defaultWorkflowClientRequestId,
							Client: workflowPb.Client_RECURRING_PAYMENT,
						},
					},
				}, nil)

				env.OnActivity(string(rpNs.GetEnachMandateAction), mock.Anything, &enachActivityPb.GetEnachMandateActionRequest{
					EnachMandateActionId: mandateActionId,
				}).Return(&enachActivityPb.GetEnachMandateActionResponse{
					EnachMandateAction: enachMandateAction1,
				}, nil)
				env.OnActivity(string(rpNs.GetEnachMandate), mock.Anything, &enachActivityPb.GetEnachMandateRequest{
					EnachMandateId: enachMandateAction1.GetEnachMandateId(),
				}).Return(&enachActivityPb.GetEnachMandateResponse{
					EnachMandate: enachMandate1,
				}, nil)
				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RecurringPaymentId: enachMandate1.GetRecurringPaymentId(),
				}).Return(&rpActivityPb.GetRecurringPaymentResponse{
					RecurringPayment: recurringPayment1,
				}, nil)

				mockInitWorkflowStageActivity(env, defaultWorkflowRequestId, rpNs.EnachExecutionOrderCreation, stagePb.Status_INITIATED)
				env.OnActivity(string(epifitemporal.CreateOrderV2), mock.Anything, &orderActivityPb.CreateOrderV2Request{
					FromActorId:   recurringPayment1.GetFromActorId(),
					ToActorId:     recurringPayment1.GetToActorId(),
					Workflow:      orderPb.OrderWorkflow_ENACH_EXECUTION_VIA_POOL_ACCOUNT_TRANSFER,
					Status:        orderPb.OrderStatus_CREATED,
					Provenance:    orderPb.OrderProvenance_INTERNAL,
					Amount:        enachMandateAction1.GetActionMetadata().GetExecuteMetadata().GetAmount(),
					ClientReqId:   enachMandateAction1.GetId(),
					WorkflowRefId: defaultWorkflowId,
					Tags:          []orderPb.OrderTag{orderPb.OrderTag_ENACH},
				}).Return(nil, errors.New("error"))
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachExecutionOrderCreation, stagePb.Status_MANUAL_INTERVENTION)
			},
			wantErr: true,
		},
		{
			name: "enach execution happy flow (destination account fund transfer status is received before pool account fund transfer status)",
			req:  wfReq,
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &celestialActivityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &celestialActivityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowRequestId,
				}).Return(&celestialActivityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						Payload: wfReqMarshalledPayload,
						ClientReqId: &workflowPb.ClientReqId{
							Id:     defaultWorkflowClientRequestId,
							Client: workflowPb.Client_RECURRING_PAYMENT,
						},
					},
				}, nil)

				env.OnActivity(string(rpNs.GetEnachMandateAction), mock.Anything, &enachActivityPb.GetEnachMandateActionRequest{
					EnachMandateActionId: mandateActionId,
				}).Return(&enachActivityPb.GetEnachMandateActionResponse{
					EnachMandateAction: enachMandateAction1,
				}, nil)
				env.OnActivity(string(rpNs.GetEnachMandate), mock.Anything, &enachActivityPb.GetEnachMandateRequest{
					EnachMandateId: enachMandateAction1.GetEnachMandateId(),
				}).Return(&enachActivityPb.GetEnachMandateResponse{
					EnachMandate: enachMandate1,
				}, nil)
				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RecurringPaymentId: enachMandate1.GetRecurringPaymentId(),
				}).Return(&rpActivityPb.GetRecurringPaymentResponse{
					RecurringPayment: recurringPayment1,
				}, nil)

				mockInitWorkflowStageActivity(env, defaultWorkflowRequestId, rpNs.EnachExecutionOrderCreation, stagePb.Status_INITIATED)
				env.OnActivity(string(epifitemporal.CreateOrderV2), mock.Anything, &orderActivityPb.CreateOrderV2Request{
					FromActorId:   recurringPayment1.GetFromActorId(),
					ToActorId:     recurringPayment1.GetToActorId(),
					Workflow:      orderPb.OrderWorkflow_ENACH_EXECUTION_VIA_POOL_ACCOUNT_TRANSFER,
					Status:        orderPb.OrderStatus_CREATED,
					Provenance:    orderPb.OrderProvenance_INTERNAL,
					Amount:        enachMandateAction1.GetActionMetadata().GetExecuteMetadata().GetAmount(),
					ClientReqId:   enachMandateAction1.GetId(),
					WorkflowRefId: defaultWorkflowId,
					Tags:          []orderPb.OrderTag{orderPb.OrderTag_ENACH},
				}).Return(&orderActivityPb.CreateOrderV2Response{OrderId: orderId}, nil)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachExecutionOrderCreation, stagePb.Status_SUCCESSFUL)

				mockInitWorkflowStageActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToPoolAccount, stagePb.Status_CREATED)
				transactionReq := &rpActivityPb.CreateTransactionRequest_TransactionRequestParams{
					PiFrom:          recurringPayment1.GetPiFrom(),
					PiTo:            payPkg.FederalEnachPoolAccountPiId,
					Utr:             enachMandateAction1.GetVendorRequestId(),
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Amount:          enachMandateAction1.GetActionMetadata().GetExecuteMetadata().GetAmount(),
					Status:          payment.TransactionStatus_CREATED,
					PaymentProtocol: payment.PaymentProtocol_ENACH,
					OrderId:         orderId,
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: enachMandateAction1.GetVendorRequestId(),
					},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				}
				txn := &payment.Transaction{
					PiFrom:          recurringPayment1.GetPiFrom(),
					PiTo:            payPkg.FederalEnachPoolAccountPiId,
					Utr:             enachMandateAction1.GetVendorRequestId(),
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Amount:          enachMandateAction1.GetActionMetadata().GetExecuteMetadata().GetAmount(),
					Status:          payment.TransactionStatus_CREATED,
					PaymentProtocol: payment.PaymentProtocol_ENACH,
					OrderId:         orderId,
					ReqId:           enachMandateAction1.GetVendorRequestId(),
					Ownership:       commontypes.Ownership_FEDERAL_BANK,
				}
				env.OnActivity(string(rpNs.CreateTransaction), mock.Anything, &rpActivityPb.CreateTransactionRequest{TransactionRequestParams: transactionReq}).Return(&rpActivityPb.CreateTransactionResponse{Transaction: txn}, nil)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToPoolAccount, stagePb.Status_BLOCKED)
				env.RegisterDelayedCallback(func() {
					err := env.SignalWorkflowByID(defaultWorkflowRequestId, string(rpNs.EnachPresentationFileUploadedSignal), defaultEnachPresentationFileUploadedSignalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, oneHourDuration)
				env.OnActivity(string(rpNs.UpdateVendorBatchRequestIdInMandateAction), mock.Anything, &enachActivityPb.UpdateVendorBatchRequestIdInMandateActionRequest{
					EnachMandateActionId: mandateActionId,
					VendorBatchRequestId: defaultPresentationBatchExecutionId,
				}).Return(nil, nil)
				env.OnActivity(string(rpNs.UpdateTransaction), mock.Anything, &rpActivityPb.UpdateTransactionRequest{
					Transaction:   txn,
					ReqInfo:       &payment.PaymentRequestInformation{ReqId: txn.GetReqId()},
					CurrentStatus: payment.TransactionStatus_CREATED,
					NextStatus:    payment.TransactionStatus_INITIATED,
				}).Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateOrder), mock.Anything, mandateActionId, orderPb.OrderStatus_IN_PAYMENT).Return(nil)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToPoolAccount, stagePb.Status_INITIATED)

				env.RegisterDelayedCallback(func() {
					err := env.SignalWorkflowByID(defaultWorkflowRequestId, string(rpNs.EnachDestinationAccountFundTransferSuccessfulSignal), nil)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, oneHourDuration)
				// pool account fund transfer signal is received an hour later than destination account fund transfer status signal
				env.RegisterDelayedCallback(func() {
					err := env.SignalWorkflowByID(defaultWorkflowRequestId, string(rpNs.EnachPoolAccountFundTransferStatusSignal), poolAccountFundTransferSuccessfulSignalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, twoHourHour)
				env.OnActivity(string(rpNs.UpdateTransaction), mock.Anything, &rpActivityPb.UpdateTransactionRequest{
					Transaction:   txn,
					ReqInfo:       &payment.PaymentRequestInformation{ReqId: txn.GetReqId()},
					CurrentStatus: payment.TransactionStatus_INITIATED,
					NextStatus:    payment.TransactionStatus_SUCCESS,
				}).Return(nil, nil)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToPoolAccount, stagePb.Status_SUCCESSFUL)

				mockInitWorkflowStageActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToDestinationAccount, stagePb.Status_INITIATED)
				mockUpdateMandateActionStatusActivity(env, mandateActionId, enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS, enachEnumsPb.EnachActionStatus_ACTION_STATUS_SUCCESS, nil)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToDestinationAccount, stagePb.Status_SUCCESSFUL)

			},
			wantErr: false,
		},
		{
			name: "should update PoolAccountTransfer stage to FAILED state when pool account transfer FAILED signal is received",
			req:  wfReq,
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &celestialActivityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &celestialActivityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowRequestId,
				}).Return(&celestialActivityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						Payload: wfReqMarshalledPayload,
						ClientReqId: &workflowPb.ClientReqId{
							Id:     defaultWorkflowClientRequestId,
							Client: workflowPb.Client_RECURRING_PAYMENT,
						},
					},
				}, nil)

				env.OnActivity(string(rpNs.GetEnachMandateAction), mock.Anything, &enachActivityPb.GetEnachMandateActionRequest{
					EnachMandateActionId: mandateActionId,
				}).Return(&enachActivityPb.GetEnachMandateActionResponse{
					EnachMandateAction: enachMandateAction1,
				}, nil)
				env.OnActivity(string(rpNs.GetEnachMandate), mock.Anything, &enachActivityPb.GetEnachMandateRequest{
					EnachMandateId: enachMandateAction1.GetEnachMandateId(),
				}).Return(&enachActivityPb.GetEnachMandateResponse{
					EnachMandate: enachMandate1,
				}, nil)
				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RecurringPaymentId: enachMandate1.GetRecurringPaymentId(),
				}).Return(&rpActivityPb.GetRecurringPaymentResponse{
					RecurringPayment: recurringPayment1,
				}, nil)

				mockInitWorkflowStageActivity(env, defaultWorkflowRequestId, rpNs.EnachExecutionOrderCreation, stagePb.Status_INITIATED)
				env.OnActivity(string(epifitemporal.CreateOrderV2), mock.Anything, &orderActivityPb.CreateOrderV2Request{
					FromActorId:   recurringPayment1.GetFromActorId(),
					ToActorId:     recurringPayment1.GetToActorId(),
					Workflow:      orderPb.OrderWorkflow_ENACH_EXECUTION_VIA_POOL_ACCOUNT_TRANSFER,
					Status:        orderPb.OrderStatus_CREATED,
					Provenance:    orderPb.OrderProvenance_INTERNAL,
					Amount:        enachMandateAction1.GetActionMetadata().GetExecuteMetadata().GetAmount(),
					ClientReqId:   enachMandateAction1.GetId(),
					WorkflowRefId: defaultWorkflowId,
					Tags:          []orderPb.OrderTag{orderPb.OrderTag_ENACH},
				}).Return(&orderActivityPb.CreateOrderV2Response{OrderId: orderId}, nil)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachExecutionOrderCreation, stagePb.Status_SUCCESSFUL)

				mockInitWorkflowStageActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToPoolAccount, stagePb.Status_CREATED)
				transactionReq := &rpActivityPb.CreateTransactionRequest_TransactionRequestParams{
					PiFrom:          recurringPayment1.GetPiFrom(),
					PiTo:            payPkg.FederalEnachPoolAccountPiId,
					Utr:             enachMandateAction1.GetVendorRequestId(),
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Amount:          enachMandateAction1.GetActionMetadata().GetExecuteMetadata().GetAmount(),
					Status:          payment.TransactionStatus_CREATED,
					PaymentProtocol: payment.PaymentProtocol_ENACH,
					OrderId:         orderId,
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: enachMandateAction1.GetVendorRequestId(),
					},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				}
				txn := &paymentPb.Transaction{
					PiFrom:          recurringPayment1.GetPiFrom(),
					PiTo:            payPkg.FederalEnachPoolAccountPiId,
					Utr:             enachMandateAction1.GetVendorRequestId(),
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Amount:          enachMandateAction1.GetActionMetadata().GetExecuteMetadata().GetAmount(),
					Status:          payment.TransactionStatus_CREATED,
					PaymentProtocol: payment.PaymentProtocol_ENACH,
					OrderId:         orderId,
					ReqId:           enachMandateAction1.GetVendorRequestId(),
					Ownership:       commontypes.Ownership_FEDERAL_BANK,
				}
				env.OnActivity(string(rpNs.CreateTransaction), mock.Anything, &rpActivityPb.CreateTransactionRequest{
					TransactionRequestParams: transactionReq,
				}).Return(&rpActivityPb.CreateTransactionResponse{Transaction: txn}, nil)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToPoolAccount, stagePb.Status_BLOCKED)
				env.RegisterDelayedCallback(func() {
					err := env.SignalWorkflowByID(defaultWorkflowRequestId, string(rpNs.EnachPresentationFileUploadedSignal), defaultEnachPresentationFileUploadedSignalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, oneHourDuration)
				env.OnActivity(string(rpNs.UpdateVendorBatchRequestIdInMandateAction), mock.Anything, &enachActivityPb.UpdateVendorBatchRequestIdInMandateActionRequest{
					EnachMandateActionId: mandateActionId,
					VendorBatchRequestId: defaultPresentationBatchExecutionId,
				}).Return(nil, nil)

				env.OnActivity(string(rpNs.UpdateTransaction), mock.Anything, &rpActivityPb.UpdateTransactionRequest{
					Transaction:   txn,
					ReqInfo:       &payment.PaymentRequestInformation{ReqId: txn.GetReqId()},
					CurrentStatus: payment.TransactionStatus_CREATED,
					NextStatus:    payment.TransactionStatus_INITIATED,
				}).Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateOrder), mock.Anything, mandateActionId, orderPb.OrderStatus_IN_PAYMENT).Return(nil)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToPoolAccount, stagePb.Status_INITIATED)

				env.RegisterDelayedCallback(func() {
					err := env.SignalWorkflowByID(defaultWorkflowRequestId, string(rpNs.EnachPoolAccountFundTransferStatusSignal), poolAccountFundTransferFailedSignalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, oneHourDuration)
				env.OnActivity(string(rpNs.UpdateTransaction), mock.Anything, &rpActivityPb.UpdateTransactionRequest{
					Transaction:   txn,
					ReqInfo:       &payment.PaymentRequestInformation{ReqId: txn.GetReqId()},
					CurrentStatus: payment.TransactionStatus_INITIATED,
					NextStatus:    payment.TransactionStatus_FAILED,
				}).Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateOrder), mock.Anything, mandateActionId, orderPb.OrderStatus_PAYMENT_FAILED).Return(nil)
				env.OnActivity(string(epifitemporal.PublishOrderUpdate), mock.Anything, &celestialActivityPb.Request{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: mandateActionId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				}).Return(nil)
				mockUpdateMandateActionStatusActivity(env, mandateActionId, enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS, enachEnumsPb.EnachActionStatus_ACTION_STATUS_FAILED, &enachPb.ActionDetailedStatus{
					ActionTypeSpecificMetadata: &enachPb.ActionDetailedStatus_ExecuteActionDetailedStatus_{ExecuteActionDetailedStatus: &enachPb.ActionDetailedStatus_ExecuteActionDetailedStatus{
						NachTransactionFlag:       "0",
						NachTransactionReasonCode: "21",
						FiStatusCode:              "ENACH_EXEC_001",
						FiStatusDesc:              "Invalid UMRN or inactive mandate",
					}},
				})
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToPoolAccount, stagePb.Status_FAILED)
			},
			wantErr: true,
		},
		{
			name: "should update PoolAccountFundTransfer stage to MANUAL_INTERVENTION when pool account fund transfer status signal times out",
			req:  wfReq,
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &celestialActivityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &celestialActivityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowRequestId,
				}).Return(&celestialActivityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						Payload: wfReqMarshalledPayload,
						ClientReqId: &workflowPb.ClientReqId{
							Id:     defaultWorkflowClientRequestId,
							Client: workflowPb.Client_RECURRING_PAYMENT,
						},
					},
				}, nil)

				env.OnActivity(string(rpNs.GetEnachMandateAction), mock.Anything, &enachActivityPb.GetEnachMandateActionRequest{
					EnachMandateActionId: mandateActionId,
				}).Return(&enachActivityPb.GetEnachMandateActionResponse{
					EnachMandateAction: enachMandateAction1,
				}, nil)
				env.OnActivity(string(rpNs.GetEnachMandate), mock.Anything, &enachActivityPb.GetEnachMandateRequest{
					EnachMandateId: enachMandateAction1.GetEnachMandateId(),
				}).Return(&enachActivityPb.GetEnachMandateResponse{
					EnachMandate: enachMandate1,
				}, nil)
				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RecurringPaymentId: enachMandate1.GetRecurringPaymentId(),
				}).Return(&rpActivityPb.GetRecurringPaymentResponse{
					RecurringPayment: recurringPayment1,
				}, nil)

				mockInitWorkflowStageActivity(env, defaultWorkflowRequestId, rpNs.EnachExecutionOrderCreation, stagePb.Status_INITIATED)
				env.OnActivity(string(epifitemporal.CreateOrderV2), mock.Anything, &orderActivityPb.CreateOrderV2Request{
					FromActorId:   recurringPayment1.GetFromActorId(),
					ToActorId:     recurringPayment1.GetToActorId(),
					Workflow:      orderPb.OrderWorkflow_ENACH_EXECUTION_VIA_POOL_ACCOUNT_TRANSFER,
					Status:        orderPb.OrderStatus_CREATED,
					Provenance:    orderPb.OrderProvenance_INTERNAL,
					Amount:        enachMandateAction1.GetActionMetadata().GetExecuteMetadata().GetAmount(),
					ClientReqId:   enachMandateAction1.GetId(),
					WorkflowRefId: defaultWorkflowId,
					Tags:          []orderPb.OrderTag{orderPb.OrderTag_ENACH},
				}).Return(&orderActivityPb.CreateOrderV2Response{OrderId: orderId}, nil)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachExecutionOrderCreation, stagePb.Status_SUCCESSFUL)

				mockInitWorkflowStageActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToPoolAccount, stagePb.Status_CREATED)
				transactionReq := &rpActivityPb.CreateTransactionRequest_TransactionRequestParams{
					PiFrom:          recurringPayment1.GetPiFrom(),
					PiTo:            payPkg.FederalEnachPoolAccountPiId,
					Utr:             enachMandateAction1.GetVendorRequestId(),
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Amount:          enachMandateAction1.GetActionMetadata().GetExecuteMetadata().GetAmount(),
					Status:          payment.TransactionStatus_CREATED,
					PaymentProtocol: payment.PaymentProtocol_ENACH,
					OrderId:         orderId,
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: enachMandateAction1.GetVendorRequestId(),
					},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				}
				txn := &payment.Transaction{
					PiFrom:          recurringPayment1.GetPiFrom(),
					PiTo:            payPkg.FederalEnachPoolAccountPiId,
					Utr:             enachMandateAction1.GetVendorRequestId(),
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Amount:          enachMandateAction1.GetActionMetadata().GetExecuteMetadata().GetAmount(),
					Status:          payment.TransactionStatus_CREATED,
					PaymentProtocol: payment.PaymentProtocol_ENACH,
					OrderId:         orderId,
					ReqId:           enachMandateAction1.GetVendorRequestId(),
					Ownership:       commontypes.Ownership_FEDERAL_BANK,
				}
				env.OnActivity(string(rpNs.CreateTransaction), mock.Anything, &rpActivityPb.CreateTransactionRequest{TransactionRequestParams: transactionReq}).Return(&rpActivityPb.CreateTransactionResponse{Transaction: txn}, nil)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToPoolAccount, stagePb.Status_BLOCKED)
				env.RegisterDelayedCallback(func() {
					err := env.SignalWorkflowByID(defaultWorkflowRequestId, string(rpNs.EnachPresentationFileUploadedSignal), defaultEnachPresentationFileUploadedSignalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, oneHourDuration)
				env.OnActivity(string(rpNs.UpdateVendorBatchRequestIdInMandateAction), mock.Anything, &enachActivityPb.UpdateVendorBatchRequestIdInMandateActionRequest{
					EnachMandateActionId: mandateActionId,
					VendorBatchRequestId: defaultPresentationBatchExecutionId,
				}).Return(nil, nil)

				env.OnActivity(string(rpNs.UpdateTransaction), mock.Anything, &rpActivityPb.UpdateTransactionRequest{
					Transaction:   txn,
					ReqInfo:       &payment.PaymentRequestInformation{ReqId: txn.GetReqId()},
					CurrentStatus: payment.TransactionStatus_CREATED,
					NextStatus:    payment.TransactionStatus_INITIATED,
				}).Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateOrder), mock.Anything, mandateActionId, orderPb.OrderStatus_IN_PAYMENT).Return(nil)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToPoolAccount, stagePb.Status_INITIATED)

				// pool account fund transfer status signal is received after 7 days so flow should timeout and stage should move to MANUAL_INTERVENTION state.
				env.RegisterDelayedCallback(func() {
					err := env.SignalWorkflowByID(defaultWorkflowRequestId, string(rpNs.EnachPoolAccountFundTransferStatusSignal), poolAccountFundTransferSuccessfulSignalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, sevenDaysDuration)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToPoolAccount, stagePb.Status_MANUAL_INTERVENTION)
			},
			wantErr: true,
		},
		{
			name: "should update DestinationAccountTransfer stage to MANUAL_INTERVENTION when destination account transfer status signal times out",
			req:  wfReq,
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &celestialActivityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &celestialActivityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowRequestId,
				}).Return(&celestialActivityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						Payload: wfReqMarshalledPayload,
						ClientReqId: &workflowPb.ClientReqId{
							Id:     defaultWorkflowClientRequestId,
							Client: workflowPb.Client_RECURRING_PAYMENT,
						},
					},
				}, nil)

				env.OnActivity(string(rpNs.GetEnachMandateAction), mock.Anything, &enachActivityPb.GetEnachMandateActionRequest{
					EnachMandateActionId: mandateActionId,
				}).Return(&enachActivityPb.GetEnachMandateActionResponse{
					EnachMandateAction: enachMandateAction1,
				}, nil)
				env.OnActivity(string(rpNs.GetEnachMandate), mock.Anything, &enachActivityPb.GetEnachMandateRequest{
					EnachMandateId: enachMandateAction1.GetEnachMandateId(),
				}).Return(&enachActivityPb.GetEnachMandateResponse{
					EnachMandate: enachMandate1,
				}, nil)
				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RecurringPaymentId: enachMandate1.GetRecurringPaymentId(),
				}).Return(&rpActivityPb.GetRecurringPaymentResponse{
					RecurringPayment: recurringPayment1,
				}, nil)

				mockInitWorkflowStageActivity(env, defaultWorkflowRequestId, rpNs.EnachExecutionOrderCreation, stagePb.Status_INITIATED)
				env.OnActivity(string(epifitemporal.CreateOrderV2), mock.Anything, &orderActivityPb.CreateOrderV2Request{
					FromActorId:   recurringPayment1.GetFromActorId(),
					ToActorId:     recurringPayment1.GetToActorId(),
					Workflow:      orderPb.OrderWorkflow_ENACH_EXECUTION_VIA_POOL_ACCOUNT_TRANSFER,
					Status:        orderPb.OrderStatus_CREATED,
					Provenance:    orderPb.OrderProvenance_INTERNAL,
					Amount:        enachMandateAction1.GetActionMetadata().GetExecuteMetadata().GetAmount(),
					ClientReqId:   enachMandateAction1.GetId(),
					WorkflowRefId: defaultWorkflowId,
					Tags:          []orderPb.OrderTag{orderPb.OrderTag_ENACH},
				}).Return(&orderActivityPb.CreateOrderV2Response{OrderId: orderId}, nil)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachExecutionOrderCreation, stagePb.Status_SUCCESSFUL)

				mockInitWorkflowStageActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToPoolAccount, stagePb.Status_CREATED)
				transactionReq := &rpActivityPb.CreateTransactionRequest_TransactionRequestParams{
					PiFrom:          recurringPayment1.GetPiFrom(),
					PiTo:            payPkg.FederalEnachPoolAccountPiId,
					Utr:             enachMandateAction1.GetVendorRequestId(),
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Amount:          enachMandateAction1.GetActionMetadata().GetExecuteMetadata().GetAmount(),
					Status:          payment.TransactionStatus_CREATED,
					PaymentProtocol: payment.PaymentProtocol_ENACH,
					OrderId:         orderId,
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: enachMandateAction1.GetVendorRequestId(),
					},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				}
				txn := &payment.Transaction{
					PiFrom:          recurringPayment1.GetPiFrom(),
					PiTo:            payPkg.FederalEnachPoolAccountPiId,
					Utr:             enachMandateAction1.GetVendorRequestId(),
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Amount:          enachMandateAction1.GetActionMetadata().GetExecuteMetadata().GetAmount(),
					Status:          payment.TransactionStatus_CREATED,
					PaymentProtocol: payment.PaymentProtocol_ENACH,
					OrderId:         orderId,
					ReqId:           enachMandateAction1.GetVendorRequestId(),
					Ownership:       commontypes.Ownership_FEDERAL_BANK,
				}
				env.OnActivity(string(rpNs.CreateTransaction), mock.Anything, &rpActivityPb.CreateTransactionRequest{TransactionRequestParams: transactionReq}).Return(&rpActivityPb.CreateTransactionResponse{Transaction: txn}, nil)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToPoolAccount, stagePb.Status_BLOCKED)
				env.RegisterDelayedCallback(func() {
					err := env.SignalWorkflowByID(defaultWorkflowRequestId, string(rpNs.EnachPresentationFileUploadedSignal), defaultEnachPresentationFileUploadedSignalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, oneHourDuration)
				env.OnActivity(string(rpNs.UpdateVendorBatchRequestIdInMandateAction), mock.Anything, &enachActivityPb.UpdateVendorBatchRequestIdInMandateActionRequest{
					EnachMandateActionId: mandateActionId,
					VendorBatchRequestId: defaultPresentationBatchExecutionId,
				}).Return(nil, nil)

				env.OnActivity(string(rpNs.UpdateTransaction), mock.Anything, &rpActivityPb.UpdateTransactionRequest{
					Transaction:   txn,
					ReqInfo:       &payment.PaymentRequestInformation{ReqId: txn.GetReqId()},
					CurrentStatus: payment.TransactionStatus_CREATED,
					NextStatus:    payment.TransactionStatus_INITIATED,
				}).Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateOrder), mock.Anything, mandateActionId, orderPb.OrderStatus_IN_PAYMENT).Return(nil)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToPoolAccount, stagePb.Status_INITIATED)

				env.RegisterDelayedCallback(func() {
					err := env.SignalWorkflowByID(defaultWorkflowRequestId, string(rpNs.EnachPoolAccountFundTransferStatusSignal), poolAccountFundTransferSuccessfulSignalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, oneHourDuration)
				env.OnActivity(string(rpNs.UpdateTransaction), mock.Anything, &rpActivityPb.UpdateTransactionRequest{
					Transaction:   txn,
					ReqInfo:       &payment.PaymentRequestInformation{ReqId: txn.GetReqId()},
					CurrentStatus: payment.TransactionStatus_INITIATED,
					NextStatus:    payment.TransactionStatus_SUCCESS,
				}).Return(nil, nil)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToPoolAccount, stagePb.Status_SUCCESSFUL)

				// pool account fund transfer status signal is received after 7 days so flow should timeout and stage should move to MANUAL_INTERVENTION state.
				mockInitWorkflowStageActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToDestinationAccount, stagePb.Status_INITIATED)
				env.RegisterDelayedCallback(func() {
					err := env.SignalWorkflowByID(defaultWorkflowRequestId, string(rpNs.EnachDestinationAccountFundTransferSuccessfulSignal), nil)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, sevenDaysDuration)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToDestinationAccount, stagePb.Status_MANUAL_INTERVENTION)
			},
			wantErr: true,
		},
		{
			name: "enach execution happy flow",
			req:  wfReq,
			setupMocks: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, &celestialActivityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &celestialActivityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowRequestId,
				}).Return(&celestialActivityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						Payload: wfReqMarshalledPayload,
						ClientReqId: &workflowPb.ClientReqId{
							Id:     defaultWorkflowClientRequestId,
							Client: workflowPb.Client_RECURRING_PAYMENT,
						},
					},
				}, nil)

				env.OnActivity(string(rpNs.GetEnachMandateAction), mock.Anything, &enachActivityPb.GetEnachMandateActionRequest{
					EnachMandateActionId: mandateActionId,
				}).Return(&enachActivityPb.GetEnachMandateActionResponse{
					EnachMandateAction: enachMandateAction1,
				}, nil)
				env.OnActivity(string(rpNs.GetEnachMandate), mock.Anything, &enachActivityPb.GetEnachMandateRequest{
					EnachMandateId: enachMandateAction1.GetEnachMandateId(),
				}).Return(&enachActivityPb.GetEnachMandateResponse{
					EnachMandate: enachMandate1,
				}, nil)
				env.OnActivity(string(rpNs.GetRecurringPayment), mock.Anything, &rpActivityPb.GetRecurringPaymentRequest{
					RecurringPaymentId: enachMandate1.GetRecurringPaymentId(),
				}).Return(&rpActivityPb.GetRecurringPaymentResponse{
					RecurringPayment: recurringPayment1,
				}, nil)

				mockInitWorkflowStageActivity(env, defaultWorkflowRequestId, rpNs.EnachExecutionOrderCreation, stagePb.Status_INITIATED)
				env.OnActivity(string(epifitemporal.CreateOrderV2), mock.Anything, &orderActivityPb.CreateOrderV2Request{
					FromActorId:   recurringPayment1.GetFromActorId(),
					ToActorId:     recurringPayment1.GetToActorId(),
					Workflow:      orderPb.OrderWorkflow_ENACH_EXECUTION_VIA_POOL_ACCOUNT_TRANSFER,
					Status:        orderPb.OrderStatus_CREATED,
					Provenance:    orderPb.OrderProvenance_INTERNAL,
					Amount:        enachMandateAction1.GetActionMetadata().GetExecuteMetadata().GetAmount(),
					ClientReqId:   enachMandateAction1.GetId(),
					WorkflowRefId: defaultWorkflowId,
					Tags:          []orderPb.OrderTag{orderPb.OrderTag_ENACH},
				}).Return(&orderActivityPb.CreateOrderV2Response{OrderId: orderId}, nil)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachExecutionOrderCreation, stagePb.Status_SUCCESSFUL)

				mockInitWorkflowStageActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToPoolAccount, stagePb.Status_CREATED)
				transactionReq := &rpActivityPb.CreateTransactionRequest_TransactionRequestParams{
					PiFrom:          recurringPayment1.GetPiFrom(),
					PiTo:            payPkg.FederalEnachPoolAccountPiId,
					Utr:             enachMandateAction1.GetVendorRequestId(),
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Amount:          enachMandateAction1.GetActionMetadata().GetExecuteMetadata().GetAmount(),
					Status:          payment.TransactionStatus_CREATED,
					PaymentProtocol: payment.PaymentProtocol_ENACH,
					OrderId:         orderId,
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: enachMandateAction1.GetVendorRequestId(),
					},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				}
				txn := &payment.Transaction{
					PiFrom:          recurringPayment1.GetPiFrom(),
					PiTo:            payPkg.FederalEnachPoolAccountPiId,
					Utr:             enachMandateAction1.GetVendorRequestId(),
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Amount:          enachMandateAction1.GetActionMetadata().GetExecuteMetadata().GetAmount(),
					Status:          payment.TransactionStatus_CREATED,
					PaymentProtocol: payment.PaymentProtocol_ENACH,
					OrderId:         orderId,
					ReqId:           enachMandateAction1.GetVendorRequestId(),
					Ownership:       commontypes.Ownership_FEDERAL_BANK,
				}
				env.OnActivity(string(rpNs.CreateTransaction), mock.Anything, &rpActivityPb.CreateTransactionRequest{TransactionRequestParams: transactionReq}).Return(&rpActivityPb.CreateTransactionResponse{Transaction: txn}, nil)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToPoolAccount, stagePb.Status_BLOCKED)
				env.RegisterDelayedCallback(func() {
					err := env.SignalWorkflowByID(defaultWorkflowRequestId, string(rpNs.EnachPresentationFileUploadedSignal), defaultEnachPresentationFileUploadedSignalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, oneHourDuration)
				env.OnActivity(string(rpNs.UpdateVendorBatchRequestIdInMandateAction), mock.Anything, &enachActivityPb.UpdateVendorBatchRequestIdInMandateActionRequest{
					EnachMandateActionId: mandateActionId,
					VendorBatchRequestId: defaultPresentationBatchExecutionId,
				}).Return(nil, nil)

				env.OnActivity(string(rpNs.UpdateTransaction), mock.Anything, &rpActivityPb.UpdateTransactionRequest{
					Transaction:   txn,
					ReqInfo:       &payment.PaymentRequestInformation{ReqId: txn.GetReqId()},
					CurrentStatus: payment.TransactionStatus_CREATED,
					NextStatus:    payment.TransactionStatus_INITIATED,
				}).Return(nil, nil)
				env.OnActivity(string(epifitemporal.UpdateOrder), mock.Anything, mandateActionId, orderPb.OrderStatus_IN_PAYMENT).Return(nil)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToPoolAccount, stagePb.Status_INITIATED)

				env.RegisterDelayedCallback(func() {
					err := env.SignalWorkflowByID(defaultWorkflowRequestId, string(rpNs.EnachPoolAccountFundTransferStatusSignal), poolAccountFundTransferSuccessfulSignalPayload)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, oneHourDuration)
				env.OnActivity(string(rpNs.UpdateTransaction), mock.Anything, &rpActivityPb.UpdateTransactionRequest{
					Transaction:   txn,
					ReqInfo:       &payment.PaymentRequestInformation{ReqId: txn.GetReqId()},
					CurrentStatus: payment.TransactionStatus_INITIATED,
					NextStatus:    payment.TransactionStatus_SUCCESS,
				}).Return(nil, nil)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToPoolAccount, stagePb.Status_SUCCESSFUL)

				mockInitWorkflowStageActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToDestinationAccount, stagePb.Status_INITIATED)
				env.RegisterDelayedCallback(func() {
					err := env.SignalWorkflowByID(defaultWorkflowRequestId, string(rpNs.EnachDestinationAccountFundTransferSuccessfulSignal), nil)
					if err != nil {
						t.Errorf("error sending signal: %v", err)
					}
				}, twoHourHour)
				mockUpdateMandateActionStatusActivity(env, mandateActionId, enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS, enachEnumsPb.EnachActionStatus_ACTION_STATUS_SUCCESS, nil)
				mockWorkflowStageUpdateActivity(env, defaultWorkflowRequestId, rpNs.EnachFundTransferToDestinationAccount, stagePb.Status_SUCCESSFUL)

			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			env := wts.NewTestWorkflowEnvironment()

			env.RegisterActivity(&celestialActivity.Processor{})
			env.RegisterActivity(&celestialActivityV2.Processor{})
			env.RegisterActivity(&orderActivity.Processor{})
			env.RegisterActivity(&rpActivity.Processor{})
			env.RegisterActivity(&enachActivity.Processor{})

			tt.setupMocks(env)

			env.ExecuteWorkflow(ExecuteEnachViaPoolAccountTransfer, tt.req)

			assert.True(t, env.IsWorkflowCompleted())

			if (env.GetWorkflowError() != nil) != tt.wantErr {
				t.Errorf("ExecuteEnachViaPoolAccountTransfer() error = %v, wantErr %v", env.GetWorkflowError(), tt.wantErr)
			}

			env.AssertExpectations(t)
		})
	}
}

// nolint: unparam
func mockInitWorkflowStageActivity(env *testsuite.TestWorkflowEnvironment, workflowReqId string, stage epifitemporal.Stage, status stagePb.Status) {
	env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, &celestialActivityPb.InitiateWorkflowStageV2Request{
		RequestHeader: &celestialActivityPb.RequestHeader{
			Ownership: commontypes.Ownership_EPIFI_TECH,
		},
		WfReqId:   workflowReqId,
		StageEnum: celestialPkg.GetStageEnumFromStage(stage),
		Status:    status,
	}).Return(nil)
}

// nolint: unparam
func mockWorkflowStageUpdateActivity(env *testsuite.TestWorkflowEnvironment, workflowReqId string, stage epifitemporal.Stage, stageStatus stagePb.Status) {
	env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, &celestialActivityPb.UpdateWorkflowStageRequest{
		RequestHeader: &celestialActivityPb.RequestHeader{
			Ownership: commontypes.Ownership_EPIFI_TECH,
		},
		WfReqId:   workflowReqId,
		StageEnum: celestialPkg.GetStageEnumFromStage(stage),
		Status:    stageStatus,
	}).Return(nil)

	if celestialPkg.IsTerminalStatus(stageStatus) {
		env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, &celestialActivityPb.PublishWorkflowUpdateEventV2Request{
			RequestHeader: &celestialActivityPb.RequestHeader{Ownership: commontypes.Ownership_EPIFI_TECH},
			WfReqId:       workflowReqId,
		}).Return(nil)
	}
}

func mockUpdateMandateActionStatusActivity(env *testsuite.TestWorkflowEnvironment, mandateActionId string, currentStatus, newStatus enachEnumsPb.EnachActionStatus, actionDetailedStatus *enachPb.ActionDetailedStatus) {
	env.OnActivity(string(rpNs.UpdateEnachMandateActionStatus), mock.Anything, &enachActivityPb.UpdateEnachMandateActionStatusRequest{
		EnachMandateActionId:  mandateActionId,
		ExpectedCurrentStatus: currentStatus,
		NewStatus:             newStatus,
		ActionDetailedStatus:  actionDetailedStatus,
	}).Return(&enachActivityPb.UpdateEnachMandateActionStatusResponse{}, nil)
}
