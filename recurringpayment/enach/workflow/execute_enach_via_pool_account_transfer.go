package workflow

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/logger"
	orderPb "github.com/epifi/gamma/api/order"
	orderActivityPb "github.com/epifi/gamma/api/order/activity"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	recurringPaymentsPb "github.com/epifi/gamma/api/recurringpayment"
	rpActivityPb "github.com/epifi/gamma/api/recurringpayment/activity"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachActivityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	enachWorkflowPb "github.com/epifi/gamma/api/recurringpayment/enach/workflow"
	payPkg "github.com/epifi/gamma/pkg/pay"
)

const (
	// presentation file is uploaded once every 24 hours so waiting for 24 hours here
	// **Note** : do not increase this value without increasing the cron schedule of presentation script.
	maxWaitTimeForEnachPresentationFileUploadedSignal = 24 * time.Hour
	// it can take upto 2 days (since pool account fund transfer initiation) to get the terminal status from vendor, adding additional buffer of 1 day.
	maxWaitTimeForPoolAccountFundTransferStatusSignal            = 72 * time.Hour
	maxWaitTimeForDestinationAccountFundTransferSuccessfulSignal = 72 * time.Hour
)

// ExecuteEnachViaPoolAccountTransfer workflow orchestrates execution of an enach mandate via Pool Account Fund transfer route.
// Following are the stages (in order) involved in the workflow :
// Stage 1 : Creation of an execution order at payment's end.
// Stage 2 : Transfer of funds from remitter account (of enach mandate recurring payment) to Pool account (created at enach vendor's end).
// Stage 3 : Transfer of funds from Pool Account to Beneficiary account (of enach mandate recurring payment).
// Stage 4 : Initiate reversal of funds from Pool Account back to remitter's account in case the last step of transfer of funds to beneficiary account fails.
//
//nolint:funlen
func ExecuteEnachViaPoolAccountTransfer(ctx workflow.Context, _ *enachWorkflowPb.ExecuteEnachViaPoolAccountTransferWorkflowPayload) error {
	lg := workflow.GetLogger(ctx)

	wfPayload := &enachWorkflowPb.ExecuteEnachViaPoolAccountTransferWorkflowPayload{}
	wfParams, err := celestialPkg.GetWorkflowProcessingReqParams(ctx, wfPayload)
	if err != nil {
		lg.Error("error in getting workflow processing params", zap.Error(err))
		return fmt.Errorf("error in getting workflow processing params: %w", err)
	}

	// get necessary data needed for most of the stages
	mandateExecutionActionId := wfPayload.GetMandateActionId()
	mandateActionEntry, err := getEnachMandateAction(ctx, wfParams, wfPayload.GetMandateActionId())
	if err != nil {
		lg.Error("error fetching mandate action entry", zap.String(logger.MANDATE_ACTION_ID, mandateExecutionActionId), zap.Error(err))
		return err
	}
	mandateEntry, err := getEnachMandate(ctx, wfParams, mandateActionEntry.GetEnachMandateId())
	if err != nil {
		lg.Error("error fetching mandate entry", zap.String(logger.MANDATE_ACTION_ID, mandateExecutionActionId), zap.Error(err))
		return err
	}
	recurringPayment, err := getRecurringPayment(ctx, wfParams, mandateEntry.GetRecurringPaymentId())
	if err != nil {
		lg.Error("error fetching recurring payment details", zap.String(logger.MANDATE_ACTION_ID, mandateExecutionActionId), zap.Error(err))
		return err
	}

	// ------------- Stage 1: Order Creation Stage-----------------
	orderId, stageStatus, stageErr := processCreateEnachExecutionOrderStage(ctx, mandateActionEntry, recurringPayment)
	if stageErr != nil {
		lg.Error("error in execution order creation stage", zap.String(logger.MANDATE_ACTION_ID, mandateExecutionActionId), zap.Error(stageErr))
		return fmt.Errorf("error in execution order creation stage, %w", stageErr)
	}
	if stageStatus != stagePb.Status_SUCCESSFUL {
		lg.Error("execution order creation stage is not successful", zap.String(logger.MANDATE_ACTION_ID, mandateExecutionActionId), zap.String(logger.STAGE_STATUS, stageStatus.String()))
		return fmt.Errorf("execution order creation stage is not successful, stageStatus : %s", stageStatus)
	}

	// -------------Stage 2: Pool Account Fund Transfer Stage-----------------
	isDestinationFundTransferAlsoComplete, stageStatus, stageErr := processFundTransferToPoolAccountStage(ctx, wfParams, orderId, mandateActionEntry, recurringPayment)
	if stageErr != nil {
		lg.Error("error in pool account fund transfer stage", zap.String(logger.MANDATE_ACTION_ID, mandateExecutionActionId), zap.Error(stageErr))
		return fmt.Errorf("error in pool account fund transfer stage, %w", stageErr)
	}
	if stageStatus != stagePb.Status_SUCCESSFUL {
		lg.Error("pool account fund transfer stage is not successful", zap.String(logger.MANDATE_ACTION_ID, mandateExecutionActionId), zap.String(logger.STAGE_STATUS, stageStatus.String()))
		return fmt.Errorf("pool account fund transfer stage is not successful, stageStatus : %s", stageStatus)
	}

	// -------------Stage 3: Destination Account Fund Transfer Stage-----------------
	stageStatus, stageErr = processFundTransferToDestinationAccountStage(ctx, wfParams, mandateExecutionActionId, isDestinationFundTransferAlsoComplete)
	if stageErr != nil {
		lg.Error("error in destination account fund transfer stage", zap.String(logger.MANDATE_ACTION_ID, mandateExecutionActionId), zap.Error(stageErr))
		return fmt.Errorf("error in destination account fund transfer stage, %w", stageErr)
	}
	switch stageStatus {
	// exit the workflow gracefully if the destination fund transfer was successful
	case stagePb.Status_SUCCESSFUL:
		// todo (utkarsh) : move log to debug level once flow is stable on prod
		lg.Info("destination account fund transfer stage successful", zap.String(logger.MANDATE_ACTION_ID, mandateExecutionActionId))
		return nil

	// initiate pool account fund transfer reversal if the destination fund transfer failed
	case stagePb.Status_FAILED:
		lg.Info("destination account fund transfer stage failed", zap.String(logger.MANDATE_ACTION_ID, mandateExecutionActionId))
		// -------------Stage 4: Pool Account Fund Transfer Reversal Stage-----------------
		stageStatus, stageErr = processPoolAccountFundTransferReversalStage(ctx, orderId, wfParams)
		if stageErr != nil {
			lg.Error("error in pool account fund transfer reversal stage", zap.String(logger.MANDATE_ACTION_ID, mandateExecutionActionId), zap.Error(stageErr))
			return fmt.Errorf("error in pool account fund transfer reversal stage, %w", stageErr)
		}
		if stageStatus != stagePb.Status_SUCCESSFUL {
			lg.Error("pool account fund transfer reversal stage is not successful", zap.String(logger.MANDATE_ACTION_ID, mandateExecutionActionId), zap.String(logger.STAGE_STATUS, stageStatus.String()))
			return fmt.Errorf("pool account fund transfer reversal stage is not successful, stageStatus : %s", stageStatus)
		}
	default:
		lg.Error("destination account fund transfer stage is not in successful or failed state", zap.String(logger.MANDATE_ACTION_ID, mandateExecutionActionId), zap.String(logger.STAGE_STATUS, stageStatus.String()))
		return fmt.Errorf("destination account fund transfer stage is not in successful or failed state, stageStatus : %s", stageStatus)
	}

	return nil
}

// processCreateEnachExecutionOrderStage creates an order for enach execution via pool account transfer.
func processCreateEnachExecutionOrderStage(ctx workflow.Context, mandateAction *enachPb.EnachMandateAction, recurringPayment *recurringPaymentsPb.RecurringPayment) (string, stagePb.Status, error) {
	var (
		lg = workflow.GetLogger(ctx)

		currentStage       = rpNs.EnachExecutionOrderCreation
		currentStageStatus = stagePb.Status_INITIATED
	)

	// -------------initiate order creation stage-----------------
	if err := celestialPkg.InitiateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
		return "", stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to initiate workflow stage: %s: %w", currentStage, err)
	}

	// create execution order entry in db
	createOrderRes := &orderActivityPb.CreateOrderV2Response{}
	if err := activityPkg.Execute(ctx, epifitemporal.CreateOrderV2, createOrderRes, &orderActivityPb.CreateOrderV2Request{
		FromActorId:   recurringPayment.GetFromActorId(),
		ToActorId:     recurringPayment.GetToActorId(),
		Workflow:      orderPb.OrderWorkflow_ENACH_EXECUTION_VIA_POOL_ACCOUNT_TRANSFER,
		Status:        orderPb.OrderStatus_CREATED,
		Provenance:    orderPb.OrderProvenance_INTERNAL,
		Amount:        mandateAction.GetActionMetadata().GetExecuteMetadata().GetAmount(),
		ClientReqId:   mandateAction.GetId(),
		WorkflowRefId: workflow.GetInfo(ctx).WorkflowExecution.ID,
		Tags:          []orderPb.OrderTag{orderPb.OrderTag_ENACH},
	},
	// marking the stage in MANUAL_INTERVENTION state and returning error to get alerted on such errors
	); err != nil {
		lg.Error("error while executing the CreateOrderV2 activity", zap.String(logger.MANDATE_ACTION_ID, mandateAction.GetId()), zap.Error(err))

		currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
		updateStageStatusErr := celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus)
		if updateStageStatusErr != nil {
			return "", stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s, stageStatus: %s, %w", currentStage, currentStageStatus, err)
		}

		return "", currentStageStatus, nil
	}

	// -------------update stage status to success-----------------
	currentStageStatus = stagePb.Status_SUCCESSFUL
	if err := celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
		return "", stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s, stageStatus: %s, %w", currentStage, currentStageStatus, err)
	}

	return createOrderRes.GetOrderId(), currentStageStatus, nil
}

// todo (utkarsh) : evaluate if we should break this stage further as the number of steps have increased quite a bit during implementation
// nolint: funlen
// processFundTransferToPoolAccountStage processes the pool account fund transfer stage of enach execution i.e it handles the transfer from funds from enach mandate source account to epifi's pool account.
// returns boolean denoting if destination fund transfer is also completed, stage status denoting the status of pool account fund transfer stage and error if any error occurs during stage processing.
func processFundTransferToPoolAccountStage(ctx workflow.Context, wfParams *workflowPb.ProcessingParams, orderId string, mandateAction *enachPb.EnachMandateAction, recurringPayment *recurringPaymentsPb.RecurringPayment) (bool, stagePb.Status, error) {
	var (
		lg                 = workflow.GetLogger(ctx)
		currentStage       = rpNs.EnachFundTransferToPoolAccount
		currentStageStatus = stagePb.Status_CREATED
	)

	// -------------Initiate fund transfer to pool account stage-----------------
	if err := celestialPkg.InitiateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
		return false, stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to initiate workflow stage: %s: %w", currentStage, err)
	}

	// create txn db entry for pool account transfer
	createTransactionReq := &rpActivityPb.CreateTransactionRequest_TransactionRequestParams{
		PiFrom:          recurringPayment.GetPiFrom(),
		PiTo:            payPkg.FederalEnachPoolAccountPiId,
		Utr:             mandateAction.GetVendorRequestId(),
		PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
		Amount:          mandateAction.GetActionMetadata().GetExecuteMetadata().GetAmount(),
		Status:          paymentPb.TransactionStatus_CREATED,
		PaymentProtocol: paymentPb.PaymentProtocol_ENACH,
		ReqInfo: &paymentPb.PaymentRequestInformation{
			ReqId: mandateAction.GetVendorRequestId(),
		},
		OrderId:   orderId,
		Ownership: commontypes.Ownership_FEDERAL_BANK,
	}
	// create txn for pool account transfer
	res := &rpActivityPb.CreateTransactionResponse{}
	if err := activityPkg.Execute(ctx, rpNs.CreateTransaction, res, &rpActivityPb.CreateTransactionRequest{
		TransactionRequestParams: createTransactionReq,
	}); err != nil {
		lg.Error("error while executing CreateTransaction activity", zap.String(logger.MANDATE_ACTION_ID, mandateAction.GetId()), zap.Error(err))

		currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
		updateStageStatusErr := celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus)
		if updateStageStatusErr != nil {
			return false, stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s, stageStatus: %s, %w", currentStage, currentStageStatus, err)
		}
		return false, currentStageStatus, nil
	}
	txn := res.GetTransaction()

	// -------------Block the Pool Account transfer stage until the txn is presented (to the sponsor bank) for execution---------
	currentStageStatus = stagePb.Status_BLOCKED
	if err := celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
		return false, stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s, stageStatus: %s, %w", currentStage, currentStageStatus, err)
	}

	var errToReturn error
	var presentationBatchExecutionId string
	workflow.NewSelector(ctx).
		AddReceive(workflow.GetSignalChannel(ctx, string(rpNs.EnachPresentationFileUploadedSignal)), func(c workflow.ReceiveChannel, more bool) {
			var b []byte
			c.Receive(ctx, &b)

			enachPresentationFileUploadedSigRes := enachWorkflowPb.EnachPresentationFileUploadedSignal{}
			if err := (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(b, &enachPresentationFileUploadedSigRes); err != nil {
				lg.Error("failed to unmarshall the signal payload signal", zap.String(logger.SIGNAL_NAME, string(rpNs.EnachPresentationFileUploadedSignal)), zap.String(logger.MANDATE_ACTION_ID, mandateAction.GetId()), zap.Error(err))
				errToReturn = fmt.Errorf("failed to unmarshall the signal payload, signal %s: %w", rpNs.EnachPresentationFileUploadedSignal, err)
				return
			}
			presentationBatchExecutionId = enachPresentationFileUploadedSigRes.GetPresentationBatchExecutionId()

			lg.Info("execution presented successfully", zap.String(logger.MANDATE_ACTION_ID, mandateAction.GetId()), zap.String(logger.BATCH_ID, presentationBatchExecutionId))
		}).
		AddFuture(workflow.NewTimer(ctx, maxWaitTimeForEnachPresentationFileUploadedSignal), func(f workflow.Future) {
			lg.Error("execution is not presented for execution yet", zap.String(logger.MANDATE_ACTION_ID, mandateAction.GetId()))
			errToReturn = fmt.Errorf("execution is not presented for execution yet")
		}).
		Select(ctx)

	if errToReturn != nil {
		lg.Error("error waiting for presentation file uploaded signal", zap.String(logger.MANDATE_ACTION_ID, mandateAction.GetId()), zap.Error(errToReturn))
		currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
		if err := celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
			return false, stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s, stageStatus: %s, %w", currentStage, currentStageStatus, err)
		}
		return false, currentStageStatus, nil
	}

	if err := activityPkg.Execute(ctx, rpNs.UpdateVendorBatchRequestIdInMandateAction, res, &enachActivityPb.UpdateVendorBatchRequestIdInMandateActionRequest{
		EnachMandateActionId: mandateAction.GetId(),
		VendorBatchRequestId: presentationBatchExecutionId,
	}); err != nil {
		lg.Error("error while executing UpdateVendorBatchRequestIdInMandateAction activity", zap.String(logger.MANDATE_ACTION_ID, mandateAction.GetId()), zap.Error(err))

		currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
		updateStageStatusErr := celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus)
		if updateStageStatusErr != nil {
			return false, stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s, stageStatus: %s, %w", currentStage, currentStageStatus, err)
		}
		return false, currentStageStatus, nil
	}

	// update order to IN_PAYMENT and txn status to INITIATED state after the txn is presented for execution.
	if err := updateOrderStatus(ctx, mandateAction.GetId(), orderPb.OrderStatus_IN_PAYMENT, false); err != nil {
		return false, stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update order to IN_PAYMENT state: %w", err)
	}
	if err := updateTxnStatus(ctx, txn, paymentPb.TransactionStatus_CREATED, paymentPb.TransactionStatus_INITIATED); err != nil {
		return false, stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update pool account transfer txn order to INITIATED state: %w", err)
	}

	currentStageStatus = stagePb.Status_INITIATED
	if err := celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
		return false, stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s, stageStatus: %s, %w", currentStage, currentStageStatus, err)
	}

	var signalErr error
	// --------------Wait for status update of presented execution-------------------------
	var poolAccountTransferStatus enachEnumsPb.FundTransferStatus
	var poolAccountTransferDetailedStatus *enachPb.ActionDetailedStatus

	isDestinationAccountFundTransferComplete := false
	workflow.NewSelector(ctx).
		AddReceive(workflow.GetSignalChannel(ctx, string(rpNs.EnachPoolAccountFundTransferStatusSignal)), func(c workflow.ReceiveChannel, more bool) {
			var payloadBytes []byte
			c.Receive(ctx, &payloadBytes)

			payload := &enachWorkflowPb.EnachPoolAccountFundTransferStatusSignal{}
			unmarshallErr := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(payloadBytes, payload)
			if unmarshallErr != nil {
				lg.Error("error unmarshalling pool account fund transfer signal payload", zap.String(logger.MANDATE_ACTION_ID, mandateAction.GetId()))
				signalErr = fmt.Errorf("error unmarshalling pool account fund transfer signal payloaD, %w", unmarshallErr)
				return
			}
			poolAccountTransferStatus = payload.GetFundTransferStatus()
			poolAccountTransferDetailedStatus = &enachPb.ActionDetailedStatus{
				ActionTypeSpecificMetadata: &enachPb.ActionDetailedStatus_ExecuteActionDetailedStatus_{
					ExecuteActionDetailedStatus: &enachPb.ActionDetailedStatus_ExecuteActionDetailedStatus{
						NachTransactionFlag:       payload.GetNachTransactionFlag(),
						NachTransactionReasonCode: payload.GetNachTransactionReasonCode(),
						FiStatusCode:              payload.GetFiStatusCode(),
						FiStatusDesc:              payload.GetFiStatusDesc(),
					},
				},
			}
			// todo (utkarsh) : move log to debug level once flow is stable on prod
			lg.Info("pool account fund transfer signal payload received successfully", zap.String(logger.MANDATE_ACTION_ID, mandateAction.GetId()))
		}).
		AddFuture(workflow.NewTimer(ctx, maxWaitTimeForPoolAccountFundTransferStatusSignal), func(f workflow.Future) {
			// todo (utkarsh) : move log to debug level once flow is stable on prod
			lg.Error("pool account fund transfer status signal not received yet", zap.String(logger.MANDATE_ACTION_ID, mandateAction.GetId()))
			signalErr = fmt.Errorf("pool account fund transfer status signal not received yet")
		}).
		AddReceive(workflow.GetSignalChannel(ctx, string(rpNs.EnachDestinationAccountFundTransferSuccessfulSignal)), func(c workflow.ReceiveChannel, more bool) {
			var payloadBytes []byte
			c.Receive(ctx, &payloadBytes)
			isDestinationAccountFundTransferComplete = true
			// todo (utkarsh) : move log to debug level once flow is stable on prod
			lg.Info("destination account fund transfer successful", zap.String(logger.MANDATE_ACTION_ID, mandateAction.GetId()))
		}).
		Select(ctx)

	if signalErr != nil {
		lg.Error("error waiting for pool account fund transfer status signal", zap.String(logger.MANDATE_ACTION_ID, mandateAction.GetId()), zap.Error(signalErr))
		currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
		if err := celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
			return false, stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s, stageStatus: %s, %w", currentStage, currentStageStatus, err)
		}
		return false, currentStageStatus, nil
	}

	if isDestinationAccountFundTransferComplete {
		// update txn to success state
		if err := updateTxnStatus(ctx, txn, paymentPb.TransactionStatus_INITIATED, paymentPb.TransactionStatus_SUCCESS); err != nil {
			return false, stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update pool account transfer txn order to SUCCESS state: %w", err)
		}

		currentStageStatus = stagePb.Status_SUCCESSFUL
		if err := celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
			return false, stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s, stageStatus: %s, %w", currentStage, currentStageStatus, err)
		}
		return isDestinationAccountFundTransferComplete, currentStageStatus, nil
	}

	switch poolAccountTransferStatus {
	// if fund transfer is successful, update txn to SUCCESS state
	case enachEnumsPb.FundTransferStatus_FUND_TRANSFER_STATUS_SUCCESSFUL:
		if err := updateTxnStatus(ctx, txn, paymentPb.TransactionStatus_INITIATED, paymentPb.TransactionStatus_SUCCESS); err != nil {
			return false, stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update pool account transfer txn order to SUCCESS state: %w", err)
		}
		currentStageStatus = stagePb.Status_SUCCESSFUL

	// if fund transfer permanently failed, update order, txn and mandate action to FAILED state
	case enachEnumsPb.FundTransferStatus_FUND_TRANSFER_STATUS_FAILED:
		if err := updateTxnStatus(ctx, txn, paymentPb.TransactionStatus_INITIATED, paymentPb.TransactionStatus_FAILED); err != nil {
			return false, stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update pool account transfer txn order to FAILED state: %w", err)
		}
		if err := updateOrderStatus(ctx, mandateAction.GetId(), orderPb.OrderStatus_PAYMENT_FAILED, true); err != nil {
			return false, stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update pool account transfer txn order to FAILED state: %w", err)
		}
		if err := updateEnachMandateActionStatus(ctx, wfParams, mandateAction.GetId(), enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS, enachEnumsPb.EnachActionStatus_ACTION_STATUS_FAILED, enachEnumsPb.EnachActionSubStatus_ENACH_ACTION_SUB_STATUS_UNSPECIFIED, enachEnumsPb.EnachActionSubStatus_ENACH_ACTION_SUB_STATUS_UNSPECIFIED, poolAccountTransferDetailedStatus); err != nil {
			return false, stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update mandate action status to FAILED state: %w", err)
		}
		currentStageStatus = stagePb.Status_FAILED

	default:
		return false, stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("unhandled pool account fund transfer status : %s", poolAccountTransferStatus)
	}
	if err := celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
		return false, stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s, stageStatus: %s, %w", currentStage, currentStageStatus, err)
	}
	return false, currentStageStatus, nil

}

// processFundTransferToDestinationAccountStage processes fund transfer from epifi's pool account to enach mandate's destination account.
// **Note** : we don't update the order and txn status in this stage as it is updated by the payments service itself which then signals this stage about destination fund transfer status.
func processFundTransferToDestinationAccountStage(ctx workflow.Context, wfParams *workflowPb.ProcessingParams, mandateActionId string, isDestinationFundTransferAlreadyComplete bool) (stagePb.Status, error) {
	var (
		lg                 = workflow.GetLogger(ctx)
		currentStage       = rpNs.EnachFundTransferToDestinationAccount
		currentStageStatus = stagePb.Status_INITIATED
	)

	// -------------Initiate destination account fund transfer stage-----------------
	if err := celestialPkg.InitiateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to initiate workflow stage: %s: %w", currentStage, err)
	}

	// if destination fund transfer was not already completed, wait for destination account fund transfer completed signal
	if !isDestinationFundTransferAlreadyComplete {
		var timeoutErr error

		workflow.NewSelector(ctx).
			AddReceive(workflow.GetSignalChannel(ctx, string(rpNs.EnachDestinationAccountFundTransferSuccessfulSignal)), func(c workflow.ReceiveChannel, more bool) {
				var payloadBytes []byte
				c.Receive(ctx, &payloadBytes)
				// todo (utkarsh) : move to debug level once flow is stable on prod
				lg.Info("destination account fund transfer successful", zap.String(logger.MANDATE_ACTION_ID, mandateActionId))
			}).
			AddFuture(workflow.NewTimer(ctx, maxWaitTimeForDestinationAccountFundTransferSuccessfulSignal), func(f workflow.Future) {
				lg.Error("destination account fund transfer status not received yet", zap.String(logger.MANDATE_ACTION_ID, mandateActionId))
				timeoutErr = fmt.Errorf("destination account fund transfer status not received yet")
			}).
			Select(ctx)

		// todo (utkarsh) : add support for received transfer failed signal once we have clarity on the process there

		if timeoutErr != nil {
			lg.Error("timeout while waiting for destination account fund transfer status signal", zap.String(logger.MANDATE_ACTION_ID, mandateActionId), zap.Error(timeoutErr))
			currentStageStatus = stagePb.Status_MANUAL_INTERVENTION
			if err := celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
				return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s, stageStatus: %s, %w", currentStage, currentStageStatus, err)
			}
			return currentStageStatus, nil
		}
	}
	// -------------mark mandate action status and stage status as success -----------------
	if err := updateEnachMandateActionStatus(ctx, wfParams, mandateActionId, enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS, enachEnumsPb.EnachActionStatus_ACTION_STATUS_SUCCESS, enachEnumsPb.EnachActionSubStatus_ENACH_ACTION_SUB_STATUS_UNSPECIFIED, enachEnumsPb.EnachActionSubStatus_ENACH_ACTION_SUB_STATUS_UNSPECIFIED, nil); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update mandate action status to SUCCESS state: %w", err)
	}

	currentStageStatus = stagePb.Status_SUCCESSFUL
	if err := celestialPkg.UpdateWorkflowStage(ctx, currentStage, currentStageStatus); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s, stageStatus: %s, %w", currentStage, currentStageStatus, err)
	}
	return currentStageStatus, nil
}

func processPoolAccountFundTransferReversalStage(ctx workflow.Context, executionOrderId string, wfParams *workflowPb.ProcessingParams) (stagePb.Status, error) {
	// todo (utkarsh) : implement this
	return stagePb.Status_STATUS_UNSPECIFIED, nil
}
