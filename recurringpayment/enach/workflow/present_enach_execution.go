package workflow

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	enachActivityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	enachWorkflowPb "github.com/epifi/gamma/api/recurringpayment/enach/workflow"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/logger"
)

// PresentEnachExecution is responsible for performing all the necessary processing uptill the enach execution file is
// uploaded on the vendor's SFTP servers
// This on a high level involves the following steps
// 1. Create the enach presentation file.
// 2. Send signal to corresponding workflows that the corresponding execution has been picked up for presentation with vendor
// 3. Upload the file to the vendor's SFTP servers
// nolint: funlen
func PresentEnachExecution(ctx workflow.Context, _ *enachWorkflowPb.PresentEnachExecutionWorkflowPayload) error {
	lg := workflow.GetLogger(ctx)

	createPresentationFileRes := &enachActivityPb.CreateEnachPresentationFileResponse{}
	err := activityPkg.Execute(ctx, rpNs.CreateEnachPresentationFile, createPresentationFileRes, &enachActivityPb.CreateEnachPresentationFileRequest{})
	if err != nil {
		lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(rpNs.CreateEnachPresentationFile)), zap.Error(err))
		return fmt.Errorf("%s activity initiation failed: error %w", rpNs.CreateEnachPresentationFile, err)
	}
	if len(createPresentationFileRes.GetPresentedActionExecutionIds()) == 0 {
		lg.Info("No action ids to process. Terminating the workflow....")
		return nil
	}

	// For thread safety, we are signalling the workflow before presenting the file to vendor.
	// If a workflow signalling fails, we do not present the file ensuring that in no circumstances is a workflow
	// presented twice.
	err = sendFilePresentedSignalToEnachExecutionWorkflow(ctx, createPresentationFileRes.GetPresentedActionExecutionIds(), createPresentationFileRes.GetPresentationBatchExecutionId())
	if err != nil {
		lg.Error("failed sending file presented signal to enach execution workflow", zap.String(logger.BATCH_ID, createPresentationFileRes.GetPresentationBatchExecutionId()), zap.Any("actionExecutionIds", createPresentationFileRes.GetPresentedActionExecutionIds()), zap.Error(err))
		return err
	}

	uploadPresentationFileRes := &enachActivityPb.UploadEnachPresentationFileToSFTPResponse{}
	err = activityPkg.Execute(ctx, rpNs.UploadEnachPresentationFileToSFTP, uploadPresentationFileRes, &enachActivityPb.UploadEnachPresentationFileToSFTPRequest{
		PresentationFileS3Path: createPresentationFileRes.GetPresentationFileS3Path(),
		PresentationFileName:   createPresentationFileRes.GetPresentationFileName(),
	})
	if err != nil {
		lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(rpNs.UploadEnachPresentationFileToSFTP)), zap.String(logger.S3_PATH, createPresentationFileRes.GetPresentationFileS3Path()), zap.String(logger.FILE_NAME, createPresentationFileRes.GetPresentationFileName()), zap.Any("actionExecutionIds", createPresentationFileRes.GetPresentedActionExecutionIds()), zap.Error(err))
		return fmt.Errorf("%s activity initiation failed: error %w", rpNs.UploadEnachPresentationFileToSFTP, err)
	}

	return nil
}

// sendFilePresentedSignalToEnachExecutionWorkflow This function does two things:
// 1. Update the enach workflows that the presentation process has started along with the batch id with which the presentation was initiated
// 2. Todo(Vishal): [Incremental] Make sure that all the enach workflows status have been updated
func sendFilePresentedSignalToEnachExecutionWorkflow(ctx workflow.Context, enachWorkflowClientReqIds []string, presentationBatchExecutionId string) error {
	lg := workflow.GetLogger(ctx)

	// Update the enach workflows that the presentation process has started along with the batch id with which the presentation was initiated
	signalPayload := &enachWorkflowPb.EnachPresentationFileUploadedSignal{
		PresentationBatchExecutionId: presentationBatchExecutionId,
	}
	signalPayloadMarshalled, err := protojson.Marshal(signalPayload)
	if err != nil {
		lg.Error("error marshalling EnachPresentationFileUploadedSignal payload", zap.String(logger.BATCH_ID, presentationBatchExecutionId), zap.Any("clientReqIds", enachWorkflowClientReqIds), zap.Error(err))
		return fmt.Errorf("signal marshal failed for %s: error %w", rpNs.EnachPresentationFileUploadedSignal, err)
	}

	var clientReqIds []*workflowPb.ClientReqId
	for _, clientReqId := range enachWorkflowClientReqIds {
		clientReqIds = append(clientReqIds, &workflowPb.ClientReqId{
			Id:     clientReqId,
			Client: workflowPb.Client_RECURRING_PAYMENT,
		})
	}

	sendSignalRes := &activityPb.SendSignalToWorkflowsV2Response{}
	if err = activityPkg.Execute(ctx, epifitemporal.SendSignalToWorkflowsV2, sendSignalRes, &activityPb.SendSignalToWorkflowsV2Request{
		RequestHeader: &activityPb.RequestHeader{
			Ownership: commontypes.Ownership_EPIFI_TECH,
		},
		SignalId:  string(rpNs.EnachPresentationFileUploadedSignal),
		PayloadV1: signalPayloadMarshalled,
		WorkflowIdentifiers: &activityPb.SendSignalToWorkflowsV2Request_ClientReqIdList{
			ClientReqIdList: &activityPb.ClientReqIdList{
				ClientReqIds: clientReqIds,
			},
		},
	}); err != nil {
		return fmt.Errorf("%s activity initiation failed: error %w", epifitemporal.SendSignalToWorkflowsV2, err)
	}
	lg.Info("Successfully signalled presentation file uploaded to all workflows.", zap.String(logger.BATCH_ID, presentationBatchExecutionId), zap.Strings(logger.CLIENT_REQUEST_IDS, enachWorkflowClientReqIds))

	return nil
}
