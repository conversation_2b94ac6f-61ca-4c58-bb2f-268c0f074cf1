package enachvendor

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
	daoMocks "github.com/epifi/gamma/recurringpayment/enach/dao/mocks"
)

func TestService_IsMandateExecutionAllowed(t *testing.T) {

	const (
		enachMandateId       = "enach-mandate-id-1"
		enachMandateActionId = "enach-mandate-action-id-1"
	)

	enachMandateAction1 := &enachPb.EnachMandateAction{
		Id:             enachMandateActionId,
		EnachMandateId: enachMandateId,
		ActionType:     enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE,
		ActionDetailedStatus: &enachPb.ActionDetailedStatus{ActionTypeSpecificMetadata: &enachPb.ActionDetailedStatus_ExecuteActionDetailedStatus_{ExecuteActionDetailedStatus: &enachPb.ActionDetailedStatus_ExecuteActionDetailedStatus{
			NachTransactionFlag: "0",
		}}},
		CreatedAt: timestamp.Now(),
	}
	enachMandateAction2 := &enachPb.EnachMandateAction{
		Id:             enachMandateActionId,
		EnachMandateId: enachMandateId,
		ActionType:     enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE,
		ActionDetailedStatus: &enachPb.ActionDetailedStatus{ActionTypeSpecificMetadata: &enachPb.ActionDetailedStatus_ExecuteActionDetailedStatus_{ExecuteActionDetailedStatus: &enachPb.ActionDetailedStatus_ExecuteActionDetailedStatus{
			NachTransactionFlag: "0",
		}}},
		CreatedAt: timestamp.New(time.Now().AddDate(0, 0, -4)),
	}

	type args struct {
		ctx context.Context
		req *IsMandateExecutionAllowedRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockMandateActionDao *daoMocks.MockEnachMandateActionDao)
		want       bool
		wantErr    error
	}{
		{
			name: "should return transient error when dao call to fetch enach mandate action fails",
			args: args{
				ctx: context.Background(),
				req: &IsMandateExecutionAllowedRequest{MandateId: enachMandateId},
			},
			setupMocks: func(mockMandateActionDao *daoMocks.MockEnachMandateActionDao) {
				mockMandateActionDao.EXPECT().GetByEnachMandateId(context.Background(), enachMandateId, gomock.Any()).Return(nil, epifierrors.ErrTransient)
			},
			want:    false,
			wantErr: epifierrors.ErrTransient,
		},
		{
			name: "gracefully return true when dao call to fectch mandate action return record not found error",
			args: args{
				ctx: context.Background(),
				req: &IsMandateExecutionAllowedRequest{MandateId: enachMandateId},
			},
			setupMocks: func(mockMandateActionDao *daoMocks.MockEnachMandateActionDao) {
				mockMandateActionDao.EXPECT().GetByEnachMandateId(context.Background(), enachMandateId, gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:    true,
			wantErr: nil,
		},
		{
			name: "should return false when enach execution request fails the compliance check",
			args: args{
				ctx: context.Background(),
				req: &IsMandateExecutionAllowedRequest{MandateId: enachMandateId},
			},
			setupMocks: func(mockMandateActionDao *daoMocks.MockEnachMandateActionDao) {
				mockMandateActionDao.EXPECT().GetByEnachMandateId(context.Background(), enachMandateId, gomock.Any()).Return([]*enachPb.EnachMandateAction{enachMandateAction1}, nil)
			},
			want:    false,
			wantErr: nil,
		},
		{
			name: "should return true when enach execution request passes the compliance check",
			args: args{
				ctx: context.Background(),
				req: &IsMandateExecutionAllowedRequest{MandateId: enachMandateId},
			},
			setupMocks: func(mockMandateActionDao *daoMocks.MockEnachMandateActionDao) {
				mockMandateActionDao.EXPECT().GetByEnachMandateId(context.Background(), enachMandateId, gomock.Any()).Return([]*enachPb.EnachMandateAction{enachMandateAction2}, nil)
			},
			want:    true,
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockMandateActionDao := daoMocks.NewMockEnachMandateActionDao(ctr)

			tt.setupMocks(mockMandateActionDao)

			s := &FederalProcessor{
				conf:             conf,
				enachFederalConf: conf.EnachServiceConfig.VendorConfig.FederalConfig,
				mandateActionDao: mockMandateActionDao,
			}
			got, err := s.IsMandateExecutionAllowed(tt.args.ctx, tt.args.req)
			if err != nil && !errors.Is(err, tt.wantErr) {
				t.Errorf("IsMandateExecutionAllowed() error = %v assertion failed", err)
				return
			}
			if got != tt.want {
				t.Errorf("IsMandateExecutionAllowed got = %v, want %v", got, tt.want)
			}
		})
	}
}
