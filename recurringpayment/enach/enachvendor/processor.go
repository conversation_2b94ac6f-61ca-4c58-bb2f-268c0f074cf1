//go:generate mockgen -source=$PWD/processor.go -destination=$PWD/mocks/mock_processor.go -package=mocks
package enachvendor

import (
	"context"

	"github.com/google/wire"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
)

var ProcessorWireSet = wire.NewSet(
	NewEnachFederalProcessor,
)

type IVendorProcessor interface {
	GenerateMandateCreationRequestId(ctx context.Context) string
	GenerateAuthPayloadForMandateCreation(ctx context.Context, request *AuthPayloadForMandateCreationRequest) (*AuthPayloadForMandateCreationResponse, error)
	GenerateMandateExecutionVendorRequestId(ctx context.Context) string
	IsMandateExecutionAllowed(ctx context.Context, request *IsMandateExecutionAllowedRequest) (bool, error)
}

type AuthPayloadForMandateCreationRequest struct {
	RecurringPayment *rpPb.RecurringPayment
	Mandate          *enachPb.EnachMandate
	MandateAction    *enachPb.EnachMandateAction
}

type AuthPayloadForMandateCreationResponse struct {
	AuthPayload *enachPb.MandateCreationAuthorizationPayload
}

type IsMandateExecutionAllowedRequest struct {
	MandateId string
}
