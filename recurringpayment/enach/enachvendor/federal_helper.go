package enachvendor

import (
	"context"
	"crypto"
	"crypto/hmac"
	"crypto/sha512"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"strings"

	pkgRsa "github.com/epifi/be-common/pkg/crypto/rsa"
)

func (f *FederalProcessor) getHmacChecksum(message, secretKey string) string {
	keyBytes := []byte(secretKey)
	messageBytes := []byte(message)

	h := hmac.New(sha512.New, keyBytes)

	h.Write(messageBytes)
	encodedData := h.Sum(nil)
	encodedString := hex.EncodeToString(encodedData)

	return encodedString
}

func (f *FederalProcessor) getEncryptedMsg(message string) (string, error) {
	publicKey, err := pkgRsa.ParsePKIXPublicKey([]byte(f.secretsConfig.Ids[FederalPublicKeySecretKeyName]))
	if err != nil {
		return "", fmt.Errorf("error while parsing public key: %w", err)
	}

	encryptor := pkgRsa.NewOAEPEncryptOnlyCryptor(publicKey, crypto.SHA256, crypto.SHA1)

	encodedMsg := base64.StdEncoding.EncodeToString([]byte(message))
	msgList := splitStringByLength(encodedMsg, 124)
	var encryptMsgList []string
	for _, msgFragment := range msgList {
		// Convert the message to bytes
		msgFragmentBytes := []byte(msgFragment)

		encrypted, err := encryptor.Encrypt(context.Background(), msgFragmentBytes, "")
		if err != nil {
			return "", fmt.Errorf("failed to encrypt fragment %s, error: %w", msgFragment, err)
		}

		// Base64 encode the encrypted bytes for easy printing and transmission
		encoded := base64.StdEncoding.EncodeToString(encrypted)

		encryptMsgList = append(encryptMsgList, encoded)
	}

	return strings.Join(encryptMsgList, "::"), nil

}

func splitStringByLength(s string, length int) []string {
	var substrings []string

	// Handle edge case when the string is empty or length is 0 or negative.
	if len(s) == 0 || length <= 0 {
		return substrings
	}

	// Loop through the string and split it into substrings of the specified length.
	for i := 0; i < len(s); i += length {
		end := i + length
		if end > len(s) {
			end = len(s)
		}
		substrings = append(substrings, s[i:end])
	}

	return substrings
}
