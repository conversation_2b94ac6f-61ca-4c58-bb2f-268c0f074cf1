//go:generate mockgen -source=$PWD/processor_factory.go -destination=$PWD/mocks/mock_processor_factory.go -package=mocks
package enachvendor

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"

	"github.com/google/wire"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
)

var FactoryWireSet = wire.NewSet(
	ProcessorWireSet, wire.NewSet(NewFactory, wire.Bind(new(IFactory), new(*Factory))),
)

type IFactory interface {
	GetVendorProcessor(ctx context.Context, vendor commonvgpb.Vendor) IVendorProcessor
}

type Factory struct {
	federalProcessor *FederalProcessor
}

func NewFactory(
	enachMandateCreationProcessor *FederalProcessor,
) *Factory {
	return &Factory{
		federalProcessor: enachMandateCreationProcessor,
	}
}

var _ IFactory = &Factory{}

func (f *Factory) GetVendorProcessor(ctx context.Context, vendor commonvgpb.Vendor) IVendorProcessor {
	switch vendor {
	case commonvgpb.Vendor_FEDERAL_BANK:
		return f.federalProcessor
	default:
		logger.WarnWithCtx(ctx, "unhandled vendor", zap.String(logger.VENDOR, vendor.String()))
		return nil
	}
}
