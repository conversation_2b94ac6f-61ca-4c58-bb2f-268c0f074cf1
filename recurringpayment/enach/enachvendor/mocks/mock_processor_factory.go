// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/go/src/github.com/epifi/gamma/recurringpayment/enach/enachvendor/processor_factory.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	enachvendor "github.com/epifi/gamma/recurringpayment/enach/enachvendor"
	gomock "github.com/golang/mock/gomock"
)

// MockIFactory is a mock of IFactory interface.
type MockIFactory struct {
	ctrl     *gomock.Controller
	recorder *MockIFactoryMockRecorder
}

// MockIFactoryMockRecorder is the mock recorder for MockIFactory.
type MockIFactoryMockRecorder struct {
	mock *MockIFactory
}

// NewMockIFactory creates a new mock instance.
func NewMockIFactory(ctrl *gomock.Controller) *MockIFactory {
	mock := &MockIFactory{ctrl: ctrl}
	mock.recorder = &MockIFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIFactory) EXPECT() *MockIFactoryMockRecorder {
	return m.recorder
}

// GetVendorProcessor mocks base method.
func (m *MockIFactory) GetVendorProcessor(ctx context.Context, vendor vendorgateway.Vendor) enachvendor.IVendorProcessor {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVendorProcessor", ctx, vendor)
	ret0, _ := ret[0].(enachvendor.IVendorProcessor)
	return ret0
}

// GetVendorProcessor indicates an expected call of GetVendorProcessor.
func (mr *MockIFactoryMockRecorder) GetVendorProcessor(ctx, vendor interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVendorProcessor", reflect.TypeOf((*MockIFactory)(nil).GetVendorProcessor), ctx, vendor)
}
