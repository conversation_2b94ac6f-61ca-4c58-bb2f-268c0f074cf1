// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/go/src/github.com/epifi/gamma/recurringpayment/enach/enachvendor/processor.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	enachvendor "github.com/epifi/gamma/recurringpayment/enach/enachvendor"
	gomock "github.com/golang/mock/gomock"
)

// MockIVendorProcessor is a mock of IVendorProcessor interface.
type MockIVendorProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockIVendorProcessorMockRecorder
}

// MockIVendorProcessorMockRecorder is the mock recorder for MockIVendorProcessor.
type MockIVendorProcessorMockRecorder struct {
	mock *MockIVendorProcessor
}

// NewMockIVendorProcessor creates a new mock instance.
func NewMockIVendorProcessor(ctrl *gomock.Controller) *MockIVendorProcessor {
	mock := &MockIVendorProcessor{ctrl: ctrl}
	mock.recorder = &MockIVendorProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIVendorProcessor) EXPECT() *MockIVendorProcessorMockRecorder {
	return m.recorder
}

// GenerateAuthPayloadForMandateCreation mocks base method.
func (m *MockIVendorProcessor) GenerateAuthPayloadForMandateCreation(ctx context.Context, request *enachvendor.AuthPayloadForMandateCreationRequest) (*enachvendor.AuthPayloadForMandateCreationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateAuthPayloadForMandateCreation", ctx, request)
	ret0, _ := ret[0].(*enachvendor.AuthPayloadForMandateCreationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateAuthPayloadForMandateCreation indicates an expected call of GenerateAuthPayloadForMandateCreation.
func (mr *MockIVendorProcessorMockRecorder) GenerateAuthPayloadForMandateCreation(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateAuthPayloadForMandateCreation", reflect.TypeOf((*MockIVendorProcessor)(nil).GenerateAuthPayloadForMandateCreation), ctx, request)
}

// GenerateMandateCreationRequestId mocks base method.
func (m *MockIVendorProcessor) GenerateMandateCreationRequestId(ctx context.Context) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateMandateCreationRequestId", ctx)
	ret0, _ := ret[0].(string)
	return ret0
}

// GenerateMandateCreationRequestId indicates an expected call of GenerateMandateCreationRequestId.
func (mr *MockIVendorProcessorMockRecorder) GenerateMandateCreationRequestId(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateMandateCreationRequestId", reflect.TypeOf((*MockIVendorProcessor)(nil).GenerateMandateCreationRequestId), ctx)
}

// GenerateMandateExecutionVendorRequestId mocks base method.
func (m *MockIVendorProcessor) GenerateMandateExecutionVendorRequestId(ctx context.Context) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateMandateExecutionVendorRequestId", ctx)
	ret0, _ := ret[0].(string)
	return ret0
}

// GenerateMandateExecutionVendorRequestId indicates an expected call of GenerateMandateExecutionVendorRequestId.
func (mr *MockIVendorProcessorMockRecorder) GenerateMandateExecutionVendorRequestId(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateMandateExecutionVendorRequestId", reflect.TypeOf((*MockIVendorProcessor)(nil).GenerateMandateExecutionVendorRequestId), ctx)
}

// IsMandateExecutionAllowed mocks base method.
func (m *MockIVendorProcessor) IsMandateExecutionAllowed(ctx context.Context, request *enachvendor.IsMandateExecutionAllowedRequest) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsMandateExecutionAllowed", ctx, request)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsMandateExecutionAllowed indicates an expected call of IsMandateExecutionAllowed.
func (mr *MockIVendorProcessorMockRecorder) IsMandateExecutionAllowed(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsMandateExecutionAllowed", reflect.TypeOf((*MockIVendorProcessor)(nil).IsMandateExecutionAllowed), ctx, request)
}
