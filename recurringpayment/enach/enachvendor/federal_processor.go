package enachvendor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"

	accountsPb "github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/recurringpayment/config/commons"
	rpServerConfig "github.com/epifi/gamma/recurringpayment/config/server"
	enachConstants "github.com/epifi/gamma/recurringpayment/enach/constants"
	"github.com/epifi/gamma/recurringpayment/enach/dao"
)

const (
	mandateCreationReqIdPrefix  = "EPIFIENACH"
	mandateExecutionReqIdPrefix = "EPIFINACHTXN"
)

type FederalProcessor struct {
	conf             *rpServerConfig.Config
	enachFederalConf *rpServerConfig.EnachFederalConfig
	secretsConfig    *cfg.Secrets
	enachSecrets     *commons.EnachSecrets
	userClient       userPb.UsersClient
	piClient         piPb.PiClient
	actorClient      actorPb.ActorClient
	mandateActionDao dao.EnachMandateActionDao
}

func NewEnachFederalProcessor(
	conf *rpServerConfig.Config,
	userClient userPb.UsersClient,
	piClient piPb.PiClient,
	actorClient actorPb.ActorClient,
	mandateActionDao dao.EnachMandateActionDao,
) (*FederalProcessor, error) {
	// unmarshall enach secret json to struct
	enachSecretsJson := conf.Secrets.Ids[commons.EnachSecretsKeyName]
	var secrets commons.EnachSecrets
	if err := json.Unmarshal([]byte(enachSecretsJson), &secrets); err != nil {
		logger.ErrorNoCtx("error unmarshalling enach secrets json")
		return nil, fmt.Errorf("error unmarshalling enach secrets json, err: %w", err)
	}

	return &FederalProcessor{
		conf:             conf,
		enachFederalConf: conf.EnachServiceConfig.VendorConfig.FederalConfig,
		enachSecrets:     &secrets,
		secretsConfig:    conf.Secrets,
		userClient:       userClient,
		piClient:         piClient,
		actorClient:      actorClient,
		mandateActionDao: mandateActionDao,
	}, nil
}

// compile time check to make sure FederalProcessor implements IVendorProcessor
var _ IVendorProcessor = &FederalProcessor{}

func (f *FederalProcessor) GenerateMandateCreationRequestId(ctx context.Context) string {
	// request id needs to 35 chars long alphanumeric string
	return mandateCreationReqIdPrefix + idgen.RandAlphaNumericString(25)
}

type FederalMandateCreationAuthPayloadData struct {
	UtilityCode       string `json:"utility_code"`
	Frequency         string `json:"frequency"`
	AmountType        string `json:"amount_type"`
	StartingFrom      string `json:"starting_from"`
	ValidUpto         string `json:"upto"`
	UntilCancelled    bool   `json:"or_until_cancel"`
	AutoCollection    bool   `json:"auto_collection"`
	LoanId            string `json:"loan_id"`
	Amount            string `json:"amount"`
	PayingBankCode    string `json:"paying_bank_code"`
	AccountNumber     string `json:"account_number"`
	SequenceType      string `json:"seq_type"`
	MobileNumber      string `json:"mobile_number"`
	PhoneNumber       string `json:"phone_number"`
	PanNumber         string `json:"pan_number"`
	TransactionNumber string `json:"transaction_number"`
	AccountType       string `json:"account_type"`
	AuthType          string `json:"auth_type"`
}

const (
	// nolint:gosec
	FederalPublicKeySecretKeyName = "EnachFederalPublicKey"
)

var (
	frequencyToStringMap = map[rpPb.AllowedFrequency]string{
		rpPb.AllowedFrequency_AS_PRESENTED: "ADHO",
		rpPb.AllowedFrequency_DAILY:        "DAIL",
		rpPb.AllowedFrequency_WEEKLY:       "WEEK",
		rpPb.AllowedFrequency_MONTHLY:      "MNTH",
		rpPb.AllowedFrequency_BI_MONTHLY:   "BIMN",
		rpPb.AllowedFrequency_QUARTERLY:    "QURT",
		rpPb.AllowedFrequency_HALF_YEARLY:  "MIAN",
		rpPb.AllowedFrequency_YEARLY:       "YEAR",
	}
	amountTypeToStringMap = map[rpPb.AmountType]string{
		rpPb.AmountType_EXACT:   "F",
		rpPb.AmountType_MAXIMUM: "V",
	}
	authTypeToStringMap = map[enachEnumsPb.EnachRegistrationAuthMode]string{
		enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_DEBIT_CARD:  "DebitCard",
		enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING: "NetBanking",
	}
	accountTypeToStringMap = map[accountsPb.Type]string{
		accountsPb.Type_SAVINGS:             "Savings",
		accountsPb.Type_CURRENT:             "Current",
		accountsPb.Type_CREDIT_CARD_ACCOUNT: "CC",
	}
)

// nolint:funlen
func (f *FederalProcessor) GenerateAuthPayloadForMandateCreation(ctx context.Context, req *AuthPayloadForMandateCreationRequest) (*AuthPayloadForMandateCreationResponse, error) {
	res := &AuthPayloadForMandateCreationResponse{}

	getFromPiDetails, err := f.piClient.GetPiById(ctx, &piPb.GetPiByIdRequest{
		Id: req.RecurringPayment.GetPiFrom(),
	})
	if err = epifigrpc.RPCError(getFromPiDetails, err); err != nil {
		return nil, fmt.Errorf("error while fetching piDetails for piId %s, err: %w", req.RecurringPayment.GetPiFrom(), err)
	}

	getToPiDetails, err := f.piClient.GetPiById(ctx, &piPb.GetPiByIdRequest{
		Id: req.RecurringPayment.GetPiTo(),
	})
	if err = epifigrpc.RPCError(getToPiDetails, err); err != nil {
		return nil, fmt.Errorf("error while fetching piDetails for piId %s, err: %w", req.RecurringPayment.GetPiTo(), err)
	}

	beneficiaryAccountNo := getToPiDetails.GetPaymentInstrument().GetAccount().GetActualAccountNumber()
	// for staging and qa env, we are passing a federal uat account number so that we can verify the federal mandate creation PWA flow on these envs (as the PWA flow only works for a uat federal beneficiary account).
	if f.conf.Application.Environment == cfg.StagingEnv || f.conf.Application.Environment == cfg.QaEnv {
		beneficiaryAccountNo = "**************"
	}

	dataPayload := &FederalMandateCreationAuthPayloadData{
		UtilityCode:       f.getUtilityCode(req.RecurringPayment.GetUiEntryPoint()),
		StartingFrom:      req.RecurringPayment.GetInterval().GetStartTime().AsTime().Format("2006-01-02"),
		ValidUpto:         req.RecurringPayment.GetInterval().GetEndTime().AsTime().Format("2006-01-02"),
		UntilCancelled:    false,
		AutoCollection:    false,
		LoanId:            beneficiaryAccountNo,
		Amount:            fmt.Sprintf("%d", req.RecurringPayment.GetAmount().GetUnits()),
		PayingBankCode:    getFromPiDetails.GetPaymentInstrument().GetAccount().GetIfscCode(),
		AccountNumber:     getFromPiDetails.GetPaymentInstrument().GetAccount().GetActualAccountNumber(),
		SequenceType:      "RCUR",
		TransactionNumber: req.MandateAction.GetVendorRequestId(),
	}
	logger.Debug(ctx, "generating auth payload for ENACH mandate flow. Use the vendorRequestId in publishing the callback event", zap.String("vendorRequestId", req.MandateAction.GetVendorRequestId()))
	frequencyStr, ok := frequencyToStringMap[req.RecurringPayment.GetRecurrenceRule().GetAllowedFrequency()]
	if !ok {
		return nil, fmt.Errorf("invalid value for frequency type %v. Unable to generate Auth paylaod", req.RecurringPayment.GetRecurrenceRule().GetAllowedFrequency())
	}
	dataPayload.Frequency = frequencyStr

	amountTypeStr, ok := amountTypeToStringMap[req.RecurringPayment.GetAmountType()]
	if !ok {
		return nil, fmt.Errorf("invalid value for amount type %v. Unable to generate Auth paylaod", req.RecurringPayment.GetAmountType())
	}
	dataPayload.AmountType = amountTypeStr

	authTypeStr, ok := authTypeToStringMap[req.Mandate.GetRegistrationAuthMode()]
	if !ok {
		return nil, fmt.Errorf("invalid value for auth type %v. Unable to generate Auth paylaod", req.Mandate.GetRegistrationAuthMode())
	}
	dataPayload.AuthType = authTypeStr

	accountTypeStr, ok := accountTypeToStringMap[getFromPiDetails.GetPaymentInstrument().GetAccount().GetAccountType()]
	if !ok {
		return nil, fmt.Errorf("invalid value for account type %v. Unable to generate Auth paylaod", getFromPiDetails.GetPaymentInstrument().GetAccount().GetAccountType())
	}
	dataPayload.AccountType = accountTypeStr

	dataPayloadBytes, err := json.Marshal(dataPayload)
	if err != nil {
		return nil, fmt.Errorf("error while marshalling data payload: %w", err)
	}
	dataPayloadStr := string(dataPayloadBytes)

	var encryptedDataPayload string

	if !f.enachFederalConf.MandateCreationConfig.EnableRsaEncryption {
		encryptedDataPayload = dataPayloadStr
	} else {
		encryptedDataPayload, err = f.getEncryptedMsg(dataPayloadStr)
		if err != nil {
			return nil, fmt.Errorf("error while encryption data payload: %w", err)
		}
	}

	res.AuthPayload = &enachPb.MandateCreationAuthorizationPayload{
		VendorSpecificAuthPayload: &enachPb.MandateCreationAuthorizationPayload_FederalBankPayload_{
			FederalBankPayload: &enachPb.MandateCreationAuthorizationPayload_FederalBankPayload{
				RedirectionUrl: f.enachFederalConf.MandateCreationConfig.RedirectionUrl,
				FormData: map[string]string{
					"app_id":       f.enachSecrets.AppId,
					"data":         encryptedDataPayload,
					"hash":         f.getHmacChecksum(f.enachFederalConf.MandateCreationConfig.ResponseUrl+dataPayloadStr, f.enachSecrets.HashKey),
					"response_url": f.enachFederalConf.MandateCreationConfig.ResponseUrl,
				},
				ExitUrl:               f.enachFederalConf.MandateCreationConfig.ExitUrl,
				RedirectionExpiryTime: timestamppb.New(time.Now().Add(f.enachFederalConf.MandateCreationConfig.RedirectionExpiry)),
			},
		},
	}
	return res, nil
}

func (f *FederalProcessor) GenerateMandateExecutionVendorRequestId(ctx context.Context) string {
	// request id needs to 30 chars long alphanumeric string
	return mandateExecutionReqIdPrefix + idgen.RandAlphaNumericString(18)
}

// IsMandateExecutionAllowed is useful to check whether execution for a given enach mandate is allowed or not, as of now execution for
// an enach mandate is not allowed for 3 days if last execution failed due to the txn being rejected by the destination bank due to return reason code.
func (f *FederalProcessor) IsMandateExecutionAllowed(ctx context.Context, req *IsMandateExecutionAllowedRequest) (bool, error) {

	enachMandateAction, err := f.mandateActionDao.GetByEnachMandateId(ctx, req.MandateId, dao.WithActionType(enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE), dao.WithOrderBy(false))
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "gracefully returning true as there is no mandate actions for the mandate id", zap.String(logger.ID, req.MandateId))
		return true, nil
	case err != nil:
		return false, fmt.Errorf("enach mandate action not found with the given enach mandate id and required filters..,%w", err)
	}
	// No Need of compliance check if transaction is not failed with return transaction flag
	if enachMandateAction[0].ActionDetailedStatus.GetExecuteActionDetailedStatus().GetNachTransactionFlag() != enachConstants.ReturnedTransactionFlag {
		return true, nil
	}
	// compliance check with a wait of 72 hours
	lastExecutionCreatedAt := enachMandateAction[0].CreatedAt
	difference := time.Now().Sub(lastExecutionCreatedAt.AsTime()).Seconds()
	if difference < f.enachFederalConf.MandateExecutionConfig.MinWaitDurationForRepresentingReturnedTxn.Seconds() {
		return false, nil
	}

	return true, nil
}

func (f *FederalProcessor) getUtilityCode(uiEntryPoint rpPb.UIEntryPoint) string {
	utilityCode, ok := f.enachSecrets.UIEntryPointToUtilityCodeMap[uiEntryPoint.String()]
	if !ok {
		utilityCode = f.enachSecrets.UtilityCode
	}
	return utilityCode
}
