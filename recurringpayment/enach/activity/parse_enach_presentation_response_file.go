package activity

import (
	"bufio"
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"strings"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	enachActivityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	payPkg "github.com/epifi/gamma/pkg/pay"
	enachConstants "github.com/epifi/gamma/recurringpayment/enach/constants"
)

const (
	presentationResponseFileEntryExpectedLength = 306
	txnStatusFlagStartIndex                     = 153
	txnStatusFlagEndIndex                       = 154
	txnReasonCodeStartIndex                     = 154
	txnReasonCodeEndIndex                       = 156
	vendorRequestIdStartIndex                   = 231
	vendorRequestIdEndIndex                     = 261
)

// ParseEnachPresentationResponseFile parses the presentation enach response received form the vendor and return the parsed details.
// Refer the link for presentation response file contract -> https://docs.google.com/spreadsheets/d/1azk4v-aHh73fIaFr5dcpa22RYDI3MuQk/edit#gid=690912910
// nolint: funlen
func (p *Processor) ParseEnachPresentationResponseFile(ctx context.Context, req *enachActivityPb.ParseEnachPresentationResponseFileRequest) (*enachActivityPb.ParseEnachPresentationResponseFileResponse, error) {
	var (
		res                 = &enachActivityPb.ParseEnachPresentationResponseFileResponse{}
		executionStatusList []*enachActivityPb.EnachExecutionStatus
	)

	// get file bytes for the presentation file from s3
	responseFileBytes, err := p.getPresentationRecordFileBytesFromS3(ctx, &getPresentationResponseFileBytesFromS3Req{
		presentationResponseFileS3Path: req.GetPresentationResponseFileS3Path(),
	})
	if err != nil {
		logger.Error(ctx, "unable to download file from s3", zap.String(logger.S3_PATH, req.GetPresentationResponseFileS3Path()), zap.Error(err))
		return nil, fmt.Errorf("unable to download file from s3 %s, %w", err, epifierrors.ErrTransient)
	}

	reader := bufio.NewReader(bytes.NewReader(responseFileBytes))

	// Reading header line
	_, fileReadErr := reader.ReadString('\n')
	if err != nil {
		logger.Error(ctx, "error while reading header file", zap.String(logger.S3_PATH, req.GetPresentationResponseFileS3Path()), zap.Error(err))
		return nil, fmt.Errorf("error while reading header file %s, %w", err, epifierrors.ErrTransient)
	}

	// Reading transaction entries
	var (
		fileLine   string
		linesCount = 0
	)
	for !errors.Is(fileReadErr, io.EOF) {
		linesCount++

		fileLine, fileReadErr = reader.ReadString('\n')
		// EOF error is returned on the last line, so not returning error in this case as we need to process the last line as well.
		if fileReadErr != nil && !errors.Is(fileReadErr, io.EOF) {
			logger.Error(ctx, "error while reading line entry", zap.Int("entryLineNumber", linesCount), zap.String(logger.S3_PATH, req.GetPresentationResponseFileS3Path()), zap.Error(fileReadErr))
			return nil, fmt.Errorf("error while reading line entry %s, %w", fileReadErr, epifierrors.ErrTransient)
		}

		// reader.ReadString method returns the string with new line character, so explicitly trimming out the delimiter from the string.
		fileLine = strings.TrimRight(fileLine, "\n\r")
		if len(fileLine) == 0 {
			break
		}

		var executionStatusEntry *enachActivityPb.EnachExecutionStatus
		executionStatusEntry, err = p.getExecutionStatusFromPresentationResponseEntry(ctx, fileLine)
		if err != nil {
			// Skipping entry and only logging error instead of returning one here is a conscious call.
			// If we detect a failure and reset the workflow to retry the activity, the already processed workflows may be
			// sent signal again but won't make any difference otherwise.
			// On the other hand returning err will block all workflows if one entry is not parsed correctly.
			logger.Error(ctx, "Skipping Entry!.. error while fetching ExecutionStatus from line entry", zap.Int("entryLineNumber", linesCount), zap.String(logger.S3_PATH, req.GetPresentationResponseFileS3Path()), zap.Error(err))
			continue
		}
		// When the transaction is in progress, we are returning execution status entry as nil for now.
		// Todo(Sidhant): Implement the long term fix where the status returned from here is pending in such cases
		// Also add handling in workflow such that the decision to update the db or not is taken by workflow.
		if executionStatusEntry != nil {
			executionStatusList = append(executionStatusList, executionStatusEntry)
		}
	}

	logger.Info(ctx, "presentation file read parsed successfully", zap.Int("linesCount", linesCount), zap.String(logger.S3_PATH, req.GetPresentationResponseFileS3Path()))

	res.EnachExecutionStatusList = executionStatusList
	return res, nil
}

func (p *Processor) getExecutionStatusFromPresentationResponseEntry(ctx context.Context, responseFileEntry string) (*enachActivityPb.EnachExecutionStatus, error) {
	if len(responseFileEntry) != presentationResponseFileEntryExpectedLength {
		return nil, fmt.Errorf("invalid value for responseFileEntry %s, err: %w", responseFileEntry, LengthExpectationsNotMatchedForField)
	}

	txnStatusFlagStr := responseFileEntry[txnStatusFlagStartIndex:txnStatusFlagEndIndex]
	txnReasonCodeStr := responseFileEntry[txnReasonCodeStartIndex:txnReasonCodeEndIndex]

	//  request id which was passed to vendor at the time of presentation
	vendorRequestId := strings.TrimSpace(responseFileEntry[vendorRequestIdStartIndex:vendorRequestIdEndIndex])
	executionActionId, err := p.getExecuteActionIdFromVendorReqId(ctx, vendorRequestId)
	if err != nil {
		return nil, fmt.Errorf("error while fetching execute action id, err: %w", err)
	}

	// This flag defines the status of the transaction Success/Failed/Pending.
	// The permissible values are 0,1,2,3,7.
	// Reference: section 10.9 (Flags in the response file) from the doc: https://www.npci.org.in/PDF/nach/notofied-document/NACH-Procedura-Guidelines-V-3.1.pdf
	switch txnStatusFlagStr {
	case enachConstants.AcceptedTransactionFlag:
		return &enachActivityPb.EnachExecutionStatus{
			ExecutionActionId:                 executionActionId,
			FundTransferStatus:                enachEnumsPb.FundTransferStatus_FUND_TRANSFER_STATUS_SUCCESSFUL,
			NachTransactionFlag:               txnStatusFlagStr,
			NachTransactionResponseReasonCode: txnReasonCodeStr,
			FiStatusCode:                      payPkg.GetEnachDetailedStatus(txnStatusFlagStr, txnReasonCodeStr).FiStatusCode,
			FiStatusDesc:                      payPkg.GetEnachDetailedStatus(txnStatusFlagStr, txnReasonCodeStr).FiStatusDesc,
		}, nil
	case enachConstants.RejectedTransactionFlag, enachConstants.ReturnedTransactionFlag:
		return &enachActivityPb.EnachExecutionStatus{
			ExecutionActionId:                 executionActionId,
			FundTransferStatus:                enachEnumsPb.FundTransferStatus_FUND_TRANSFER_STATUS_FAILED,
			NachTransactionFlag:               txnStatusFlagStr,
			NachTransactionResponseReasonCode: txnReasonCodeStr,
			FiStatusCode:                      payPkg.GetEnachDetailedStatus(txnStatusFlagStr, txnReasonCodeStr).FiStatusCode,
			FiStatusDesc:                      payPkg.GetEnachDetailedStatus(txnStatusFlagStr, txnReasonCodeStr).FiStatusDesc,
		}, nil
	case enachConstants.StatusPendingTransactionFlag, enachConstants.StatusDeemedTransactionFlag:
		logger.Info(ctx, "presented txn is still in pending state", zap.String("txnStatusFlag", txnStatusFlagStr))
		return nil, nil
	default:
		return nil, fmt.Errorf("unsuported value for transaction flag %s, err: %w", txnStatusFlagStr, epifierrors.ErrPermanent)
	}
}
func (p *Processor) getExecuteActionIdFromVendorReqId(ctx context.Context, vendorReqId string) (string, error) {
	// todo (Sidhant): batch this dao call.
	enachMandateAction, err := p.enachMandateActionDao.GetByActionTypeAndVendorRequestId(ctx, enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE, vendorReqId)
	if err != nil {
		return "", fmt.Errorf("failed to get enachMandateAction for vendor req id %s", vendorReqId)
	}

	return enachMandateAction.GetId(), nil
}

type getPresentationResponseFileBytesFromS3Req struct {
	presentationResponseFileS3Path string
}

// this can be used to fetching enach presentation related files from S3
func (p *Processor) getPresentationRecordFileBytesFromS3(ctx context.Context, req *getPresentationResponseFileBytesFromS3Req) ([]byte, error) {
	presentationResponseFileBytes, err := p.enachRecordsS3Client.Read(ctx, req.presentationResponseFileS3Path)
	if err != nil {
		return nil, err
	}

	return presentationResponseFileBytes, nil
}
