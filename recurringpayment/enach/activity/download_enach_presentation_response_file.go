package activity

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	enachActivityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	vgEnachPb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
)

func (p *Processor) DownloadEnachPresentationResponseFile(ctx context.Context, req *enachActivityPb.DownloadEnachPresentationResponseFileRequest) (*enachActivityPb.DownloadEnachPresentationResponseFileResponse, error) {
	var (
		res = &enachActivityPb.DownloadEnachPresentationResponseFileResponse{}
	)

	// hit and trial for all the seqNum in the range [file_seq_number,file_max_seq_number]
	// npci sends multiple intermidiate files to federal and federal sends to us with running sequence number
	// so the final sequence can be different daily
	// NOTE: according to the contract, the response file will contain all the presented executions and R3FC will only be present in the respone file
	// if the above contract is breached, please raise it with federal
	// mail thread: Re: Enach Integration requirements
	for seqNum := int64(1); seqNum <= req.GetFileMaxSeqNumber(); seqNum++ {
		responseFileName := fmt.Sprintf("NACH_DR_%s_%s_%s_%03d-R3FC-%02d-RES.txt", req.GetFileDateString(), p.enachSecrets.UtilityCode, p.enachConfig.PresentationFileConfig.UtilityName, req.GetFileSeqNumber(), seqNum)
		downloadPresentationFileRes, err := p.vgEnachClient.DownloadPresentationResponseFileFromSFTP(ctx, &vgEnachPb.DownloadPresentationResponseFileFromSFTPRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			},
			FileName: responseFileName,
		})
		switch {
		case err != nil:
			logger.Error(ctx, "error while downloading presentation response file from SFTP", zap.Error(err), zap.String(logger.FILE_NAME, responseFileName))
			return nil, fmt.Errorf("error while downloading presentation response file with name %s from SFTP, err: %s, %w", responseFileName, err, epifierrors.ErrTransient)
		case downloadPresentationFileRes.GetStatus().IsRecordNotFound():
			continue
		case downloadPresentationFileRes.GetStatus().IsSuccess():
			// file found successfully
			logger.Info(ctx, "Successfully fetched presentation response file from SFTP", zap.String(logger.FILE_NAME, responseFileName))
		default:
			logger.Error(ctx, "error while downloading presentation response file from SFTP", zap.Error(rpc.StatusAsError(downloadPresentationFileRes.GetStatus())), zap.String(logger.FILE_NAME, responseFileName))
			return nil, fmt.Errorf("error while downloading presentation response file with name %s from SFTP, err: %s, %w", responseFileName, rpc.StatusAsError(downloadPresentationFileRes.GetStatus()), epifierrors.ErrTransient)
		}

		s3FilePath, err := p.uploadPresentationRecordFileToS3(ctx, &uploadPresentationRecordFileToS3Req{
			fileBytes:            downloadPresentationFileRes.GetPresentationResponseFileBytes(),
			fileName:             responseFileName,
			settlementDateString: req.GetFileDateString(),
		})
		if err != nil {
			logger.Error(ctx, "unable to upload file to s3", zap.Error(err))
			return nil, fmt.Errorf("unable to upload file to s3 %s, %w", err, epifierrors.ErrTransient)
		}

		logger.Info(ctx, "successfully uploaded presentation response file to S3", zap.String(logger.FILE_NAME, responseFileName), zap.String(logger.S3_PATH, s3FilePath))

		res.PresentationResponseFileS3Path = s3FilePath
		return res, nil
	}

	// in case all the presented executions are rejected, then the response file has a different name than the one which is being checked above, so now checking if that all rejection related response file exists on SFTP server.
	responseFileName := fmt.Sprintf("NACH_DR_%s_%s_%s_%03d-RES.txt", req.GetFileDateString(), p.enachSecrets.UtilityCode, p.enachConfig.PresentationFileConfig.UtilityName, req.GetFileSeqNumber())
	downloadPresentationFileRes, err := p.vgEnachClient.DownloadPresentationResponseFileFromSFTP(ctx, &vgEnachPb.DownloadPresentationResponseFileFromSFTPRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		FileName: responseFileName,
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error while downloading presentation response file from SFTP", zap.Error(err), zap.String(logger.FILE_NAME, responseFileName))
		return nil, fmt.Errorf("error while downloading presentation response file with name %s from SFTP, err: %s, %w", responseFileName, err, epifierrors.ErrTransient)
	case downloadPresentationFileRes.GetStatus().IsRecordNotFound():
		logger.Info(ctx, fmt.Sprintf("no response file found with sequence number in range %d-%d", req.GetFileSeqNumber(), req.GetFileMaxSeqNumber()), zap.String(logger.FILE_NAME, responseFileName))
		return nil, fmt.Errorf("unable to download file SFTP. No such file found, err: %s, %w", epifierrors.ErrRecordNotFound.Error(), epifierrors.ErrPermanent)
	case downloadPresentationFileRes.GetStatus().IsSuccess():
		// file found successfully
		logger.Info(ctx, "Successfully fetched presentation response file from SFTP", zap.String(logger.FILE_NAME, responseFileName))
	default:
		logger.Error(ctx, "error while downloading presentation response file from SFTP", zap.Error(rpc.StatusAsError(downloadPresentationFileRes.GetStatus())), zap.String(logger.FILE_NAME, responseFileName))
		return nil, fmt.Errorf("error while downloading presentation response file with name %s from SFTP, err: %s, %w", responseFileName, rpc.StatusAsError(downloadPresentationFileRes.GetStatus()), epifierrors.ErrTransient)
	}

	s3FilePath, err := p.uploadPresentationRecordFileToS3(ctx, &uploadPresentationRecordFileToS3Req{
		fileBytes:            downloadPresentationFileRes.GetPresentationResponseFileBytes(),
		fileName:             responseFileName,
		settlementDateString: req.GetFileDateString(),
	})
	if err != nil {
		logger.Error(ctx, "unable to upload file to s3", zap.Error(err))
		return nil, fmt.Errorf("unable to upload file to s3 %s, %w", err, epifierrors.ErrTransient)
	}

	logger.Info(ctx, "successfully uploaded presentation response file to S3", zap.String(logger.FILE_NAME, responseFileName), zap.String(logger.S3_PATH, s3FilePath))

	res.PresentationResponseFileS3Path = s3FilePath
	return res, nil
}
