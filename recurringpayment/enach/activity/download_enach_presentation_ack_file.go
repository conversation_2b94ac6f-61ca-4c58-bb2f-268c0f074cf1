package activity

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	enachActivityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	vgEnachPb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
	vgEnachEnums "github.com/epifi/gamma/api/vendorgateway/openbanking/enach/enums"
	enachConstants "github.com/epifi/gamma/recurringpayment/enach/constants"
)

func (p *Processor) DownloadEnachPresentationAckFile(ctx context.Context, req *enachActivityPb.DownloadEnachPresentationAckFileRequest) (*enachActivityPb.DownloadEnachPresentationAckFileResponse, error) {
	var (
		res = &enachActivityPb.DownloadEnachPresentationAckFileResponse{}
	)

	ackFileName := fmt.Sprintf("NACH_DR_%s_%s_%s_001-ACK.xml", getAckFileDateString(req.GetSettlementDate()), p.enachSecrets.UtilityCode, p.enachConfig.PresentationFileConfig.UtilityName)
	downloadPresentationAckFileRes, err := p.vgEnachClient.DownloadFileFromSFTP(ctx, &vgEnachPb.DownloadFileFromSFTPRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		FileName: ackFileName,
		FileType: vgEnachEnums.EnachFileType_FILE_TYPE_PRESENTATION_ACK_FILE,
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error while downloading presentation ack file from SFTP", zap.Error(err), zap.String(logger.FILE_NAME, ackFileName))
		return nil, fmt.Errorf("error while downloading presentation ack file with name %s from SFTP, err: %s, %w", ackFileName, err, epifierrors.ErrTransient)
	case downloadPresentationAckFileRes.GetStatus().IsRecordNotFound():
		logger.Error(ctx, "unable to download presentation ack file from SFTP. No such file found", zap.String(logger.FILE_NAME, ackFileName))
		return nil, fmt.Errorf("unable to download presentation ack file with name %s from SFTP. No such file found, err: %s, %w", ackFileName, err, epifierrors.ErrPermanent)
	case downloadPresentationAckFileRes.GetStatus().IsSuccess():
		// file found successfully
		logger.Info(ctx, "Successfully fetched presentation ack file from SFTP", zap.String(logger.FILE_NAME, ackFileName))
	default:
		logger.Error(ctx, "error while downloading presentation response file from SFTP", zap.Error(rpc.StatusAsError(downloadPresentationAckFileRes.GetStatus())), zap.String(logger.FILE_NAME, ackFileName))
		return nil, fmt.Errorf("error while downloading presentation response file with name %s from SFTP, err: %s, %w", ackFileName, rpc.StatusAsError(downloadPresentationAckFileRes.GetStatus()), epifierrors.ErrTransient)
	}

	s3FilePath, err := p.uploadPresentationRecordFileToS3(ctx, &uploadPresentationRecordFileToS3Req{
		fileBytes:            downloadPresentationAckFileRes.GetFileData(),
		fileName:             ackFileName,
		settlementDateString: getAckFileDateString(req.GetSettlementDate()),
	})
	if err != nil {
		logger.Error(ctx, "error uploading presentation ack file to s3", zap.Error(err), zap.String(logger.FILE_NAME, ackFileName), zap.String(logger.S3_PATH, s3FilePath))
		return nil, fmt.Errorf("error uploading presentation ack file to s3 %s, %w", err, epifierrors.ErrTransient)
	}

	logger.Info(ctx, "successfully uploaded presentation ack file to the s3", zap.String(logger.FILE_NAME, ackFileName), zap.String(logger.S3_PATH, s3FilePath))
	res.PresentationAckFileS3Path = s3FilePath
	return res, nil
}

func getAckFileDateString(ts *timestampPb.Timestamp) string {
	return ts.AsTime().In(datetime.IST).Format(enachConstants.PresentationFileDateFormat)
}
