package activity_test

import (
	"os"
	"testing"

	"github.com/golang/mock/gomock"

	celestialMocks "github.com/epifi/be-common/api/celestial/mocks"
	s3Mocks "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	dateTimeMocks "github.com/epifi/be-common/pkg/datetime/mocks"
	epifitemporalLogging "github.com/epifi/be-common/pkg/epifitemporal/logging"
	epifitemporalTest "github.com/epifi/be-common/pkg/epifitemporal/test"
	"github.com/epifi/be-common/pkg/logger"

	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	recurringPaymentMocks "github.com/epifi/gamma/api/recurringpayment/mocks"
	vgEnachMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/enach/mocks"
	rpWorkerConfig "github.com/epifi/gamma/recurringpayment/config/worker"
	enachActivity "github.com/epifi/gamma/recurringpayment/enach/activity"
	enachDaoMocks "github.com/epifi/gamma/recurringpayment/enach/dao/mocks"
	"github.com/epifi/gamma/recurringpayment/test"
)

var (
	wts  epifitemporalTest.WorkflowTestSuite
	conf *rpWorkerConfig.Config
)

type mockedDependencies struct {
	clock                  *dateTimeMocks.MockTime
	enachMandateDao        *enachDaoMocks.MockEnachMandateDao
	enachMandateActionDao  *enachDaoMocks.MockEnachMandateActionDao
	celestialClient        *celestialMocks.MockCelestialClient
	piClient               *piMocks.MockPiClient
	recurringPaymentClient *recurringPaymentMocks.MockRecurringPaymentServiceClient
	s3Client               *s3Mocks.MockS3Client
	vgEnachClient          *vgEnachMocks.MockEnachClient
}

func newProcessorWithMocks(t *testing.T) (*enachActivity.Processor, *mockedDependencies, func()) {

	ctr := gomock.NewController(t)

	mockClock := dateTimeMocks.NewMockTime(ctr)
	mockEnachMandateDao := enachDaoMocks.NewMockEnachMandateDao(ctr)
	mockEnachMandateActionDao := enachDaoMocks.NewMockEnachMandateActionDao(ctr)
	mockCelestialClient := celestialMocks.NewMockCelestialClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockRecurringPaymentClient := recurringPaymentMocks.NewMockRecurringPaymentServiceClient(ctr)
	mockS3Client := s3Mocks.NewMockS3Client(ctr)
	mockVgEnachClient := vgEnachMocks.NewMockEnachClient(ctr)

	md := &mockedDependencies{
		clock:                  mockClock,
		enachMandateDao:        mockEnachMandateDao,
		enachMandateActionDao:  mockEnachMandateActionDao,
		celestialClient:        mockCelestialClient,
		piClient:               mockPiClient,
		recurringPaymentClient: mockRecurringPaymentClient,
		s3Client:               mockS3Client,
		vgEnachClient:          mockVgEnachClient,
	}

	act, err := enachActivity.NewProcessor(
		mockClock,
		conf,
		mockEnachMandateDao,
		mockEnachMandateActionDao,
		mockCelestialClient,
		mockPiClient,
		mockRecurringPaymentClient,
		mockS3Client,
		mockVgEnachClient,
	)
	if err != nil {
		logger.Panic("Failed to initialise enach activity processor")
	}

	return act, md, func() {
		ctr.Finish()
	}

}

// nolint:dogsled
func TestMain(m *testing.M) {
	var teardown func()
	conf, _, teardown = test.InitTestWorker(false)
	wts.SetLogger(epifitemporalLogging.NewZapAdapter(logger.Log))
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
