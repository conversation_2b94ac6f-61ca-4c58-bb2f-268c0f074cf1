// nolint:dupl
package activity

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	activityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
)

func (p *Processor) GetEnachMandateAction(ctx context.Context, req *activityPb.GetEnachMandateActionRequest) (*activityPb.GetEnachMandateActionResponse, error) {

	var (
		res = &activityPb.GetEnachMandateActionResponse{}
	)

	enachMandateAction, err := p.enachMandateActionDao.GetById(ctx, req.GetEnachMandateActionId())
	if err != nil {
		logger.Error(ctx, "error while fetching enach mandate action by id", zap.Error(err), zap.String(logger.ID, req.GetEnachMandateActionId()))
		return nil, fmt.Errorf("error while fetching enach mandate action by id, err: %s, %w", err, epifierrors.ErrTransient)
	}

	res.EnachMandateAction = enachMandateAction

	return res, nil
}
