// nolint:dupl
package activity

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	activityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

func (p *Processor) GetEnachMandate(ctx context.Context, req *activityPb.GetEnachMandateRequest) (*activityPb.GetEnachMandateResponse, error) {

	var (
		res = &activityPb.GetEnachMandateResponse{}
	)

	enachMandate, err := p.enachMandateDao.GetById(ctx, req.GetEnachMandateId())
	if err != nil {
		logger.Error(ctx, "error while fetching enach mandate by id", zap.Error(err), zap.String(logger.ID, req.GetEnachMandateId()))
		return nil, fmt.Errorf("error while fetching enach mandate by id, err: %s, %w", err, epifierrors.ErrTransient)

	}

	res.EnachMandate = enachMandate

	return res, nil
}
