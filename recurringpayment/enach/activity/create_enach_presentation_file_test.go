package activity_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"testing"
	"time"

	awsS3 "github.com/aws/aws-sdk-go-v2/service/s3/types"
	"go.uber.org/mock/gomock"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"

	celestialPb "github.com/epifi/be-common/api/celestial"
	celestialMocks "github.com/epifi/be-common/api/celestial/mocks"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	rpcPb "github.com/epifi/be-common/api/rpc"
	s3Mocks "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	dateTimeMocks "github.com/epifi/be-common/pkg/datetime/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	accountsPb "github.com/epifi/gamma/api/accounts"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachActivityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	recurringPaymentMocks "github.com/epifi/gamma/api/recurringpayment/mocks"
	enachDaoMocks "github.com/epifi/gamma/recurringpayment/enach/dao/mocks"
)

func TestProcessor_CreateEnachPresentationFile(t *testing.T) {
	const (
		umrn1                     = "test_umrn_1"
		piTo1                     = "test_pi_to_1"
		piFrom1                   = "test_pi_from_1"
		actorTo1                  = "test_actor_to_1"
		actorFrom1                = "test_actor_from_1"
		fromAccountNumber1        = "test_from_account_number_1"
		toAccountNumber1          = "test_to_account_number_1"
		fromIfscCode1             = "from_ifsc_1"
		toIfscCode1               = "to_ifsc_001"
		fromAccountName1          = "test_from_account_name_1"
		toAccountName1            = "test_to_account_name_1"
		workflowRequestId1        = "test_workflow_request_id_1"
		recurringPaymentActionId1 = "test_recurring_payment_action_id_1"
		recurringPaymentId1       = "test_recurring_payment_id_1"
		vendorRequestId1          = "test_vendor_request_id_1"
		enachMandateId1           = "test_enach_mandate_id_1"
		enachMandateActionId1     = "test_enach_mandate_action_id_1"

		umrn2                     = "test_umrn_2"
		piTo2                     = "test_pi_to_2"
		piFrom2                   = "test_pi_from_2"
		actorTo2                  = "test_actor_to_2"
		actorFrom2                = "test_actor_from_2"
		fromAccountNumber2        = "test_from_account_number_2"
		toAccountNumber2          = "test_to_account_number_2"
		fromIfscCode2             = "from_ifsc_2"
		toIfscCode2               = "to_ifsc_002"
		fromAccountName2          = "test_from_account_name_2"
		toAccountName2            = "test_to_account_name_2"
		workflowRequestId2        = "test_workflow_request_id_2"
		recurringPaymentActionId2 = "test_recurring_payment_action_id_2"
		recurringPaymentId2       = "test_recurring_payment_id_2"
		vendorRequestId2          = "test_vendor_request_id_2"
		enachMandateId2           = "test_enach_mandate_id_2"
		enachMandateActionId2     = "test_enach_mandate_action_id_2"

		defaultTimeString = "2023-06-08T17:02:52Z"
		defaultS3FilePath = "presentation/********/NACH_DR_********_UTILITY_CODE_EpifiTechnologies_001.txt"
	)

	var (
		defaultGetWorkflowRequestsByFiltersReq = &celestialPb.GetWorkflowRequestsByFiltersRequest{
			Status:    stagePb.Status_BLOCKED,
			TypeEnum:  celestialPkg.GetTypeEnumFromWorkflowType(rpNs.ExecuteEnachViaPoolAccountTransfer),
			StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnachFundTransferToPoolAccount),
		}
		defaultGetWorkflowRequestsByFiltersRes = &celestialPb.GetWorkflowRequestsByFiltersResponse{
			Status: rpcPb.StatusOk(),
			WfReqList: []*celestialPb.WorkflowRequest{
				{
					Id: workflowRequestId1,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     enachMandateActionId1,
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
					Ownership: commontypes.Ownership_EPIFI_TECH,
					StageEnum: &workflowPb.StageEnum{StageValue: string(rpNs.EnachFundTransferToPoolAccount)},
					TypeEnum:  &workflowPb.TypeEnum{TypeValue: string(rpNs.ExecuteEnachViaPoolAccountTransfer)},
				},
				{
					Id: workflowRequestId2,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     enachMandateActionId2,
						Client: workflowPb.Client_RECURRING_PAYMENT,
					},
					Ownership: commontypes.Ownership_EPIFI_TECH,
					StageEnum: &workflowPb.StageEnum{StageValue: string(rpNs.EnachFundTransferToPoolAccount)},
					TypeEnum:  &workflowPb.TypeEnum{TypeValue: string(rpNs.ExecuteEnachViaPoolAccountTransfer)},
				},
			},
		}

		recurringPayment1 = &rpPb.RecurringPayment{
			Id:                       recurringPaymentId1,
			FromActorId:              actorFrom1,
			ToActorId:                actorTo1,
			Type:                     rpPb.RecurringPaymentType_STANDING_INSTRUCTION,
			PiFrom:                   piFrom1,
			PiTo:                     piTo1,
			MaximumAllowedTxns:       20,
			PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
			PreferredPaymentProtocol: paymentPb.PaymentProtocol_NEFT,
			State:                    rpPb.RecurringPaymentState_ACTIVATED,
			AmountType:               rpPb.AmountType_MAXIMUM,
			ShareToPayee:             true,
		}
		fromPi1 = &piPb.PaymentInstrument{
			Id:   piFrom1,
			Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
			Identifier: &piPb.PaymentInstrument_Account{
				Account: &piPb.Account{
					ActualAccountNumber: fromAccountNumber1,
					IfscCode:            fromIfscCode1,
					AccountType:         accountsPb.Type_SAVINGS,
					Name:                fromAccountName1,
				},
			},
		}
		toPi1 = &piPb.PaymentInstrument{
			Id:   piTo1,
			Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
			Identifier: &piPb.PaymentInstrument_Account{
				Account: &piPb.Account{
					ActualAccountNumber: toAccountNumber1,
					IfscCode:            toIfscCode1,
					AccountType:         accountsPb.Type_SAVINGS,
					Name:                toAccountName1,
				},
			},
		}
		executionAmount1 = &money.Money{
			CurrencyCode: "INR",
			Units:        2000,
		}
		enachMandate1 = &enachPb.EnachMandate{
			Id:                   enachMandateId1,
			RecurringPaymentId:   recurringPaymentId1,
			Umrn:                 umrn1,
			RegistrationAuthMode: enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_DEBIT_CARD,
			Vendor:               commonvgpb.Vendor_FEDERAL_BANK,
		}
		enachMandateAction1 = &enachPb.EnachMandateAction{
			Id:              enachMandateActionId1,
			ClientRequestId: recurringPaymentActionId1,
			EnachMandateId:  enachMandateId1,
			ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE,
			ActionStatus:    enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS,
			VendorRequestId: vendorRequestId1,
			ActionMetadata: &enachPb.ActionMetadata{
				ActionTypeSpecificMetadata: &enachPb.ActionMetadata_ExecuteMetadata{
					ExecuteMetadata: &enachPb.ActionMetadata_ExecuteActionMetadata{
						Amount: executionAmount1,
					},
				},
			},
		}

		recurringPayment2 = &rpPb.RecurringPayment{
			Id:                       recurringPaymentId2,
			FromActorId:              actorFrom2,
			ToActorId:                actorTo2,
			Type:                     rpPb.RecurringPaymentType_STANDING_INSTRUCTION,
			PiFrom:                   piFrom2,
			PiTo:                     piTo2,
			MaximumAllowedTxns:       20,
			PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
			PreferredPaymentProtocol: paymentPb.PaymentProtocol_NEFT,
			State:                    rpPb.RecurringPaymentState_ACTIVATED,
			AmountType:               rpPb.AmountType_MAXIMUM,
			ShareToPayee:             true,
		}
		fromPi2 = &piPb.PaymentInstrument{
			Id:   piFrom2,
			Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
			Identifier: &piPb.PaymentInstrument_Account{
				Account: &piPb.Account{
					ActualAccountNumber: fromAccountNumber2,
					IfscCode:            fromIfscCode2,
					AccountType:         accountsPb.Type_SAVINGS,
					Name:                fromAccountName2,
				},
			},
		}
		toPi2 = &piPb.PaymentInstrument{
			Id:   piTo2,
			Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
			Identifier: &piPb.PaymentInstrument_Account{
				Account: &piPb.Account{
					ActualAccountNumber: toAccountNumber2,
					IfscCode:            toIfscCode2,
					AccountType:         accountsPb.Type_SAVINGS,
					Name:                toAccountName2,
				},
			},
		}
		executionAmount2 = &money.Money{
			CurrencyCode: "INR",
			Units:        510,
		}
		enachMandate2 = &enachPb.EnachMandate{
			Id:                   enachMandateId2,
			RecurringPaymentId:   recurringPaymentId2,
			Umrn:                 umrn2,
			RegistrationAuthMode: enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_DEBIT_CARD,
			Vendor:               commonvgpb.Vendor_FEDERAL_BANK,
		}
		enachMandateAction2 = &enachPb.EnachMandateAction{
			Id:              enachMandateActionId2,
			ClientRequestId: recurringPaymentActionId2,
			EnachMandateId:  enachMandateId2,
			ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE,
			ActionStatus:    enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS,
			VendorRequestId: vendorRequestId2,
			ActionMetadata: &enachPb.ActionMetadata{
				ActionTypeSpecificMetadata: &enachPb.ActionMetadata_ExecuteMetadata{
					ExecuteMetadata: &enachPb.ActionMetadata_ExecuteActionMetadata{
						Amount: executionAmount2,
					},
				},
			},
		}
	)

	tests := []struct {
		name       string
		req        *enachActivityPb.CreateEnachPresentationFileRequest
		setupMocks func(clock *dateTimeMocks.MockTime, enachMandateDao *enachDaoMocks.MockEnachMandateDao, enachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao, celestialClient *celestialMocks.MockCelestialClient, piClient *piMocks.MockPiClient, recurringPaymentClient *recurringPaymentMocks.MockRecurringPaymentServiceClient, s3Client *s3Mocks.MockS3Client)
		want       *enachActivityPb.CreateEnachPresentationFileResponse
		wantErr    bool
		assertErr  func(err error) bool
	}{
		{
			name: "Should return retryable error if unable to fetch workflows blocked for file presentation",
			req:  &enachActivityPb.CreateEnachPresentationFileRequest{},
			setupMocks: func(clock *dateTimeMocks.MockTime, enachMandateDao *enachDaoMocks.MockEnachMandateDao, enachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao, celestialClient *celestialMocks.MockCelestialClient, piClient *piMocks.MockPiClient, recurringPaymentClient *recurringPaymentMocks.MockRecurringPaymentServiceClient, s3Client *s3Mocks.MockS3Client) {
				celestialClient.EXPECT().GetWorkflowRequestsByFilters(gomock.Any(), defaultGetWorkflowRequestsByFiltersReq).Return(&celestialPb.GetWorkflowRequestsByFiltersResponse{
					Status: rpcPb.StatusInternal(),
				}, nil)
				timeNow, _ := time.Parse(time.RFC3339, defaultTimeString)
				clock.EXPECT().Now().Return(timeNow)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "Should terminate gracefully if no workflows are blocked for file presentation",
			req:  &enachActivityPb.CreateEnachPresentationFileRequest{},
			setupMocks: func(clock *dateTimeMocks.MockTime, enachMandateDao *enachDaoMocks.MockEnachMandateDao, enachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao, celestialClient *celestialMocks.MockCelestialClient, piClient *piMocks.MockPiClient, recurringPaymentClient *recurringPaymentMocks.MockRecurringPaymentServiceClient, s3Client *s3Mocks.MockS3Client) {
				celestialClient.EXPECT().GetWorkflowRequestsByFilters(gomock.Any(), defaultGetWorkflowRequestsByFiltersReq).Return(&celestialPb.GetWorkflowRequestsByFiltersResponse{
					Status: rpcPb.StatusRecordNotFound(),
				}, nil)
				timeNow, _ := time.Parse(time.RFC3339, defaultTimeString)
				clock.EXPECT().Now().Return(timeNow)
			},
			want:      &enachActivityPb.CreateEnachPresentationFileResponse{},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "Should return retryable error if unable to fetch mandate actions from ids",
			req:  &enachActivityPb.CreateEnachPresentationFileRequest{},
			setupMocks: func(clock *dateTimeMocks.MockTime, enachMandateDao *enachDaoMocks.MockEnachMandateDao, enachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao, celestialClient *celestialMocks.MockCelestialClient, piClient *piMocks.MockPiClient, recurringPaymentClient *recurringPaymentMocks.MockRecurringPaymentServiceClient, s3Client *s3Mocks.MockS3Client) {
				celestialClient.EXPECT().GetWorkflowRequestsByFilters(gomock.Any(), defaultGetWorkflowRequestsByFiltersReq).Return(defaultGetWorkflowRequestsByFiltersRes, nil)
				timeNow, _ := time.Parse(time.RFC3339, defaultTimeString)
				clock.EXPECT().Now().Return(timeNow)
				enachMandateActionDao.EXPECT().GetByIds(gomock.Any(), []string{enachMandateActionId1, enachMandateActionId2}).Return(nil, epifierrors.ErrPermanent)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "Should return retryable error if unable to fetch mandates from ids",
			req:  &enachActivityPb.CreateEnachPresentationFileRequest{},
			setupMocks: func(clock *dateTimeMocks.MockTime, enachMandateDao *enachDaoMocks.MockEnachMandateDao, enachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao, celestialClient *celestialMocks.MockCelestialClient, piClient *piMocks.MockPiClient, recurringPaymentClient *recurringPaymentMocks.MockRecurringPaymentServiceClient, s3Client *s3Mocks.MockS3Client) {
				celestialClient.EXPECT().GetWorkflowRequestsByFilters(gomock.Any(), defaultGetWorkflowRequestsByFiltersReq).Return(defaultGetWorkflowRequestsByFiltersRes, nil)
				timeNow, _ := time.Parse(time.RFC3339, defaultTimeString)
				clock.EXPECT().Now().Return(timeNow)
				enachMandateActionDao.EXPECT().GetByIds(gomock.Any(), []string{enachMandateActionId1, enachMandateActionId2}).Return([]*enachPb.EnachMandateAction{enachMandateAction1, enachMandateAction2}, nil)
				enachMandateDao.EXPECT().GetByIds(gomock.Any(), []string{enachMandateId1, enachMandateId2}).Return(nil, epifierrors.ErrPermanent)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "Should return retryable error if unable to fetch recurring payments by ids",
			req:  &enachActivityPb.CreateEnachPresentationFileRequest{},
			setupMocks: func(clock *dateTimeMocks.MockTime, enachMandateDao *enachDaoMocks.MockEnachMandateDao, enachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao, celestialClient *celestialMocks.MockCelestialClient, piClient *piMocks.MockPiClient, recurringPaymentClient *recurringPaymentMocks.MockRecurringPaymentServiceClient, s3Client *s3Mocks.MockS3Client) {
				celestialClient.EXPECT().GetWorkflowRequestsByFilters(gomock.Any(), defaultGetWorkflowRequestsByFiltersReq).Return(defaultGetWorkflowRequestsByFiltersRes, nil)
				timeNow, _ := time.Parse(time.RFC3339, defaultTimeString)
				clock.EXPECT().Now().Return(timeNow)
				enachMandateActionDao.EXPECT().GetByIds(gomock.Any(), []string{enachMandateActionId1, enachMandateActionId2}).Return([]*enachPb.EnachMandateAction{enachMandateAction1, enachMandateAction2}, nil)
				enachMandateDao.EXPECT().GetByIds(gomock.Any(), []string{enachMandateId1, enachMandateId2}).Return([]*enachPb.EnachMandate{enachMandate1, enachMandate2}, nil)
				recurringPaymentClient.EXPECT().GetRecurringPaymentsByIds(gomock.Any(), &rpPb.GetRecurringPaymentsByIdsRequest{
					Ids: []string{recurringPaymentId1, recurringPaymentId2},
				}).Return(nil, epifierrors.ErrPermanent)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "Should return retryable error if unable to fetch to pis by ids",
			req:  &enachActivityPb.CreateEnachPresentationFileRequest{},
			setupMocks: func(clock *dateTimeMocks.MockTime, enachMandateDao *enachDaoMocks.MockEnachMandateDao, enachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao, celestialClient *celestialMocks.MockCelestialClient, piClient *piMocks.MockPiClient, recurringPaymentClient *recurringPaymentMocks.MockRecurringPaymentServiceClient, s3Client *s3Mocks.MockS3Client) {
				celestialClient.EXPECT().GetWorkflowRequestsByFilters(gomock.Any(), defaultGetWorkflowRequestsByFiltersReq).Return(defaultGetWorkflowRequestsByFiltersRes, nil)
				timeNow, _ := time.Parse(time.RFC3339, defaultTimeString)
				clock.EXPECT().Now().Return(timeNow)
				enachMandateActionDao.EXPECT().GetByIds(gomock.Any(), []string{enachMandateActionId1, enachMandateActionId2}).Return([]*enachPb.EnachMandateAction{enachMandateAction1, enachMandateAction2}, nil)
				enachMandateDao.EXPECT().GetByIds(gomock.Any(), []string{enachMandateId1, enachMandateId2}).Return([]*enachPb.EnachMandate{enachMandate1, enachMandate2}, nil)
				recurringPaymentClient.EXPECT().GetRecurringPaymentsByIds(gomock.Any(), &rpPb.GetRecurringPaymentsByIdsRequest{
					Ids: []string{recurringPaymentId1, recurringPaymentId2},
				}).Return(&rpPb.GetRecurringPaymentsByIdsResponse{
					Status:            rpcPb.StatusOk(),
					RecurringPayments: []*rpPb.RecurringPayment{recurringPayment1, recurringPayment2},
				}, nil)
				piClient.EXPECT().GetPIsByIds(gomock.Any(), &piPb.GetPIsByIdsRequest{
					Ids: []string{piFrom1, piTo1, piFrom2, piTo2},
				}).Return(nil, epifierrors.ErrPermanent)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "Should return retryable error if unable to upload file to s3",
			req:  &enachActivityPb.CreateEnachPresentationFileRequest{},
			setupMocks: func(clock *dateTimeMocks.MockTime, enachMandateDao *enachDaoMocks.MockEnachMandateDao, enachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao, celestialClient *celestialMocks.MockCelestialClient, piClient *piMocks.MockPiClient, recurringPaymentClient *recurringPaymentMocks.MockRecurringPaymentServiceClient, s3Client *s3Mocks.MockS3Client) {
				celestialClient.EXPECT().GetWorkflowRequestsByFilters(gomock.Any(), defaultGetWorkflowRequestsByFiltersReq).Return(defaultGetWorkflowRequestsByFiltersRes, nil)
				timeNow, _ := time.Parse(time.RFC3339, defaultTimeString)
				clock.EXPECT().Now().Return(timeNow)
				enachMandateActionDao.EXPECT().GetByIds(gomock.Any(), []string{enachMandateActionId1, enachMandateActionId2}).Return([]*enachPb.EnachMandateAction{enachMandateAction1, enachMandateAction2}, nil)
				enachMandateDao.EXPECT().GetByIds(gomock.Any(), []string{enachMandateId1, enachMandateId2}).Return([]*enachPb.EnachMandate{enachMandate1, enachMandate2}, nil)
				recurringPaymentClient.EXPECT().GetRecurringPaymentsByIds(gomock.Any(), &rpPb.GetRecurringPaymentsByIdsRequest{
					Ids: []string{recurringPaymentId1, recurringPaymentId2},
				}).Return(&rpPb.GetRecurringPaymentsByIdsResponse{
					Status:            rpcPb.StatusOk(),
					RecurringPayments: []*rpPb.RecurringPayment{recurringPayment1, recurringPayment2},
				}, nil)
				piClient.EXPECT().GetPIsByIds(gomock.Any(), &piPb.GetPIsByIdsRequest{
					Ids: []string{piFrom1, piTo1, piFrom2, piTo2},
				}).Return(&piPb.GetPIsByIdsResponse{
					Status:             rpcPb.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{fromPi1, toPi1, fromPi2, toPi2},
				}, nil)
				s3Client.EXPECT().Write(gomock.Any(), defaultS3FilePath, gomock.Any(), string(awsS3.ObjectCannedACLBucketOwnerFullControl)).Return(epifierrors.ErrPermanent)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "Should return success if all steps completed successfully",
			req:  &enachActivityPb.CreateEnachPresentationFileRequest{},
			setupMocks: func(clock *dateTimeMocks.MockTime, enachMandateDao *enachDaoMocks.MockEnachMandateDao, enachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao, celestialClient *celestialMocks.MockCelestialClient, piClient *piMocks.MockPiClient, recurringPaymentClient *recurringPaymentMocks.MockRecurringPaymentServiceClient, s3Client *s3Mocks.MockS3Client) {
				celestialClient.EXPECT().GetWorkflowRequestsByFilters(gomock.Any(), defaultGetWorkflowRequestsByFiltersReq).Return(defaultGetWorkflowRequestsByFiltersRes, nil)
				timeNow, _ := time.Parse(time.RFC3339, defaultTimeString)
				clock.EXPECT().Now().Return(timeNow)
				enachMandateActionDao.EXPECT().GetByIds(gomock.Any(), []string{enachMandateActionId1, enachMandateActionId2}).Return([]*enachPb.EnachMandateAction{enachMandateAction1, enachMandateAction2}, nil)
				enachMandateDao.EXPECT().GetByIds(gomock.Any(), []string{enachMandateId1, enachMandateId2}).Return([]*enachPb.EnachMandate{enachMandate1, enachMandate2}, nil)
				recurringPaymentClient.EXPECT().GetRecurringPaymentsByIds(gomock.Any(), &rpPb.GetRecurringPaymentsByIdsRequest{
					Ids: []string{recurringPaymentId1, recurringPaymentId2},
				}).Return(&rpPb.GetRecurringPaymentsByIdsResponse{
					Status:            rpcPb.StatusOk(),
					RecurringPayments: []*rpPb.RecurringPayment{recurringPayment1, recurringPayment2},
				}, nil)
				piClient.EXPECT().GetPIsByIds(gomock.Any(), &piPb.GetPIsByIdsRequest{
					Ids: []string{piFrom1, piTo1, piFrom2, piTo2},
				}).Return(&piPb.GetPIsByIdsResponse{
					Status:             rpcPb.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{fromPi1, toPi1, fromPi2, toPi2},
				}, nil)
				s3Client.EXPECT().Write(gomock.Any(), defaultS3FilePath, gomock.Any(), string(awsS3.ObjectCannedACLBucketOwnerFullControl)).Return(nil)
			},
			want:      nil,
			wantErr:   false,
			assertErr: epifitemporal.IsRetryableError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md, assertTest := newProcessorWithMocks(t)
			defer assertTest()

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			tt.setupMocks(md.clock, md.enachMandateDao, md.enachMandateActionDao, md.celestialClient, md.piClient, md.recurringPaymentClient, md.s3Client)

			result := &enachActivityPb.CreateEnachPresentationFileResponse{}
			got, err := env.ExecuteActivity(rpNs.CreateEnachPresentationFile, tt.req)

			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("CreateEnachPresentationFile() error = %v failed to fetch type value from convertible", getErr)
					return
				}
				result.PresentationBatchExecutionId = ""
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("CreateEnachPresentationFile() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("CreateEnachPresentationFile() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("CreateEnachPresentationFile() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
