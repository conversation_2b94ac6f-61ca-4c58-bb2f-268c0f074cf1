package activity_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	rpcPb "github.com/epifi/be-common/api/rpc"
	s3mocks "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	enachActivityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	vgEnachPb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
	vgEnachMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/enach/mocks"
)

func TestProcessor_UploadEnachPresentationFileToSFTP(t *testing.T) {
	const (
		defaultS3PresentationFilePath = "default_S3_presentation_file_path"
		defaultFileName               = "default_file_name"
	)

	var (
		defaultPresentationFileBytes = []byte("default_test_presentation_file_bytes")
	)

	tests := []struct {
		name       string
		req        *enachActivityPb.UploadEnachPresentationFileToSFTPRequest
		setupMocks func(enachVgClient *vgEnachMocks.MockEnachClient, mockS3Client *s3mocks.MockS3Client)
		want       *enachActivityPb.UploadEnachPresentationFileToSFTPResponse
		wantErr    bool
	}{
		{
			name: "Should return error if failed to read file from S3",
			req: &enachActivityPb.UploadEnachPresentationFileToSFTPRequest{
				PresentationFileS3Path: defaultS3PresentationFilePath,
				PresentationFileName:   defaultFileName,
			},
			setupMocks: func(enachVgClient *vgEnachMocks.MockEnachClient, mockS3Client *s3mocks.MockS3Client) {
				mockS3Client.EXPECT().Read(gomock.Any(), defaultS3PresentationFilePath).Return(nil, epifierrors.ErrPermanent)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Should return error if VG API to upload file returned error",
			req: &enachActivityPb.UploadEnachPresentationFileToSFTPRequest{
				PresentationFileS3Path: defaultS3PresentationFilePath,
				PresentationFileName:   defaultFileName,
			},
			setupMocks: func(mockEnachVgClient *vgEnachMocks.MockEnachClient, mockS3Client *s3mocks.MockS3Client) {
				mockS3Client.EXPECT().Read(gomock.Any(), defaultS3PresentationFilePath).Return(defaultPresentationFileBytes, nil)
				mockEnachVgClient.EXPECT().UploadPresentationFileToSFTP(gomock.Any(), &vgEnachPb.UploadPresentationFileToSFTPRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					PresentationFileBytes: defaultPresentationFileBytes,
					PresentationFileName:  defaultFileName,
				}).
					Return(&vgEnachPb.UploadPresentationFileToSFTPResponse{
						Status: rpcPb.StatusInternal(),
					}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Should return success if VG API to completed successfully",
			req: &enachActivityPb.UploadEnachPresentationFileToSFTPRequest{
				PresentationFileS3Path: defaultS3PresentationFilePath,
				PresentationFileName:   defaultFileName,
			},
			setupMocks: func(mockEnachVgClient *vgEnachMocks.MockEnachClient, mockS3Client *s3mocks.MockS3Client) {
				mockS3Client.EXPECT().Read(gomock.Any(), defaultS3PresentationFilePath).Return(defaultPresentationFileBytes, nil)
				mockEnachVgClient.EXPECT().UploadPresentationFileToSFTP(gomock.Any(), &vgEnachPb.UploadPresentationFileToSFTPRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					PresentationFileBytes: defaultPresentationFileBytes,
					PresentationFileName:  defaultFileName,
				}).
					Return(&vgEnachPb.UploadPresentationFileToSFTPResponse{
						Status: rpcPb.StatusOk(),
					}, nil)
			},
			want:    &enachActivityPb.UploadEnachPresentationFileToSFTPResponse{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md, assertTest := newProcessorWithMocks(t)
			defer assertTest()

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			tt.setupMocks(md.vgEnachClient, md.s3Client)

			result := &enachActivityPb.UploadEnachPresentationFileToSFTPResponse{}
			got, err := env.ExecuteActivity(rpNs.UploadEnachPresentationFileToSFTP, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("UploadEnachPresentationFileToSFTP() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			if (err != nil) != tt.wantErr {
				t.Errorf("UploadEnachPresentationFileToSFTP() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !proto.Equal(result, tt.want) {
				t.Errorf("UploadEnachPresentationFileToSFTP() got = %v, want %v", result, tt.want)
				return
			}
		})
	}

}
