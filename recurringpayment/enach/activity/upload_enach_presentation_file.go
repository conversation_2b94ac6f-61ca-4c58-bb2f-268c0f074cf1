package activity

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	enachActivityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	vgEnachPb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
)

func (p *Processor) UploadEnachPresentationFileToSFTP(ctx context.Context, req *enachActivityPb.UploadEnachPresentationFileToSFTPRequest) (*enachActivityPb.UploadEnachPresentationFileToSFTPResponse, error) {
	var (
		res = &enachActivityPb.UploadEnachPresentationFileToSFTPResponse{}
	)

	presentationFileBytes, err := p.enachRecordsS3Client.Read(ctx, req.GetPresentationFileS3Path())
	if err != nil {
		return nil, fmt.Errorf("failed to fetch presentation file, err : %s, %w", err, epifierrors.ErrTransient)
	}

	uploadPresentationFileRes, err := p.vgEnachClient.UploadPresentationFileToSFTP(ctx, &vgEnachPb.UploadPresentationFileToSFTPRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		PresentationFileBytes: presentationFileBytes,
		PresentationFileName:  req.GetPresentationFileName(),
	})
	if err = epifigrpc.RPCError(uploadPresentationFileRes, err); err != nil {
		logger.Error(ctx, "error while uploading presentation file to SFTP", zap.Error(err))
		return nil, fmt.Errorf("error while uploading presentation file to SFTP, err: %s, %w", err, epifierrors.ErrTransient)
	}

	logger.Info(ctx, "successfully upload presentation file to SFTP", zap.String(logger.FILE_NAME, req.GetPresentationFileName()), zap.String(logger.S3_PATH, req.GetPresentationFileS3Path()))

	return res, nil
}
