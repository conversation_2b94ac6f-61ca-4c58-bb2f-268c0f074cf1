package activity

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/gamma/api/recurringpayment/enach"
	activityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

func (p *Processor) UpdateEnachMandateActionStatus(ctx context.Context, req *activityPb.UpdateEnachMandateActionStatusRequest) (*activityPb.UpdateEnachMandateActionStatusResponse, error) {
	var (
		res = &activityPb.UpdateEnachMandateActionStatusResponse{}
	)

	enachMandateAction, err := p.enachMandateActionDao.GetById(ctx, req.GetEnachMandateActionId())
	if err != nil {
		logger.Error(ctx, "error while fetching enach mandate action by id", zap.Error(err), zap.String(logger.ID, req.GetEnachMandateActionId()))
		return nil, fmt.Errorf("error while fetching enach mandate action by id, err: %s, %w", err, epifierrors.ErrTransient)
	}

	// Checking if the enach mandate action is already in updated state.
	// This is required for idempotency of activity
	if isMandateActionStatusAlreadyUpdated(enachMandateAction, req.GetNewStatus(), req.GetNewSubStatus()) {
		return res, nil
	}

	// updateMask contains the fields (column's) to be updated.
	updateMask := []enachEnumsPb.EnachMandateActionFieldMask{
		enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_STATUS,
		enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_SUB_STATUS,
		enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_DETAILED_STATUS,
	}
	// the query uses these fields (column's) to update the existing fields (column's)
	// consider it as the superposition of new fields (column's) over the old fields (column's) with the help of field mask.
	enachMandateAction.ActionStatus = req.NewStatus
	enachMandateAction.ActionSubStatus = req.NewSubStatus

	// update Action detailed status if present
	if req.GetActionDetailedStatus() != nil {
		updateMask = append(updateMask, enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_DETAILED_STATUS)
		enachMandateAction.ActionDetailedStatus = req.GetActionDetailedStatus()
	}

	err = p.enachMandateActionDao.UpdateWithStatusCheck(ctx, updateMask, enachMandateAction, req.GetExpectedCurrentStatus(), req.GetExpectedCurrentSubStatus())
	if err != nil {
		logger.Error(ctx, "error while updating enach mandate action status", zap.Error(err), zap.String(logger.MANDATE_ACION, enachMandateAction.String()))
		return nil, fmt.Errorf("error while updating enach mandate action status, err: %s, %w", err, epifierrors.ErrTransient)
	}

	return res, nil
}

func isMandateActionStatusAlreadyUpdated(enachMandateAction *enach.EnachMandateAction, newStatus enachEnumsPb.EnachActionStatus, newSubStatus enachEnumsPb.EnachActionSubStatus) bool {
	return enachMandateAction.ActionStatus == newStatus && enachMandateAction.ActionSubStatus == newSubStatus
}
