package activity_test

import (
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	s3Mocks "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachActivityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	enachConstants "github.com/epifi/gamma/recurringpayment/enach/constants"
	enachDaoMocks "github.com/epifi/gamma/recurringpayment/enach/dao/mocks"
)

func TestProcessor_ParseEnachPresentationResponseFile(t *testing.T) {
	const (
		defaultS3FilePath = "presentation/04052023/NACH_DR_04052023_UTILITY_CODE_EpifiTechnologies_001-RES.txt"

		enachMandateActionId1 = "test_enach_mandate_action_id_1"
		vendorRequestId1      = "QCHC3VPCBH9E"

		enachMandateActionId2 = "test_enach_mandate_action_id_2"
		vendorRequestId2      = "8NJSFFQ1CG3B"

		enachMandateActionId3 = "test_enach_mandate_action_id_3"
		vendorRequestId3      = "D8XT34CSE9Z6"

		enachMandateActionId4 = "test_enach_mandate_action_id_4"
		vendorRequestId4      = "1AJSFFQ1CG3B"

		enachMandateActionId5 = "test_enach_mandate_action_id_5"
		vendorRequestId5      = "2AJSFFQ1CG3B"
	)

	var (
		defaultParseEnachPresentationResponseFileReq = &enachActivityPb.ParseEnachPresentationResponseFileRequest{
			PresentationResponseFileS3Path: defaultS3FilePath,
		}
		defaultPresentationResponseFileHeader = "56       ADITYA BIRLA FASHION AND RETAIL LTD                                                                    00013814909320702202300000000000000000000   NACH00000000023114P230207001541083KC683049001                                     00000005021                                                         \n"
		presentationResponseFileEntry1        = "67                             Pavithra Weddings                                       ADITYA BIRLA FASHION             0000007426836                    100FDRL000108710870200001810                     FDRL0000379NACH00000000023114QCHC3VPCBH9E                  010000000000000000FDRL0000000004017646       \n"
		presentationResponseFileEntry2        = "67                             ZEENATH SILKS AND SAREES                                ADITYA BIRLA FASHION             0000001157878                    900FDRL000142714270200011909                     FDRL0000379NACH000000000231148NJSFFQ1CG3B                  010000000000000000FDRL0000000003986799       \n"
		presentationResponseFileEntry3        = "67                                                                                     ADITYA BIRLA FASHION             0000074724078                    001           920020035584071                               NACH00000000023114D8XT34CSE9Z6                  010000000000000000UTIB0000000013744303       \n"
		presentationResponseFileEntry4        = "67                             ZEENATH SILKS AND SAREES                                ADITYA BIRLA FASHION             0000001157878                    221FDRL000142714270200011909                     FDRL0000379NACH000000000231141AJSFFQ1CG3B                  010000000000000000FDRL0000000003986799       \n"
		presentationResponseFileEntry5        = "67                             ZEENATH SILKS AND SAREES                                ADITYA BIRLA FASHION             0000001157878                    000FDRL000142714270200011909                     FDRL0000379NACH000000000231142AJSFFQ1CG3B                  010000000000000000FDRL0000000003986799       "

		defaultResponseFileBytes = []byte(defaultPresentationResponseFileHeader + presentationResponseFileEntry1 + presentationResponseFileEntry2 + presentationResponseFileEntry3 + presentationResponseFileEntry4 + presentationResponseFileEntry5)

		enachMandateAction1 = &enachPb.EnachMandateAction{
			Id:              enachMandateActionId1,
			ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE,
			VendorRequestId: vendorRequestId1,
		}
		enachMandateAction2 = &enachPb.EnachMandateAction{
			Id:              enachMandateActionId2,
			ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE,
			VendorRequestId: vendorRequestId1,
		}
		enachMandateAction3 = &enachPb.EnachMandateAction{
			Id:              enachMandateActionId3,
			ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE,
			VendorRequestId: vendorRequestId1,
		}
		enachMandateAction4 = &enachPb.EnachMandateAction{
			Id:              enachMandateActionId4,
			ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE,
			VendorRequestId: vendorRequestId1,
		}
		enachMandateAction5 = &enachPb.EnachMandateAction{
			Id:              enachMandateActionId5,
			ActionType:      enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE,
			VendorRequestId: vendorRequestId1,
		}
	)

	tests := []struct {
		name       string
		req        *enachActivityPb.ParseEnachPresentationResponseFileRequest
		setupMocks func(s3Client *s3Mocks.MockS3Client, enachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao)
		want       *enachActivityPb.ParseEnachPresentationResponseFileResponse
		wantErr    bool
		assertErr  func(err error) bool
	}{
		{
			name: "Should return retryable error if unable to get file bytes from s3",
			req:  defaultParseEnachPresentationResponseFileReq,
			setupMocks: func(s3Client *s3Mocks.MockS3Client, enachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao) {
				s3Client.EXPECT().Read(gomock.Any(), defaultS3FilePath).Return(nil, epifierrors.ErrPermanent)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "Should return execution status list for 3 entries if only 3 entries have required transaction status flag (other two have invalid txn flag and in_progress response status respectively)",
			req:  defaultParseEnachPresentationResponseFileReq,
			setupMocks: func(s3Client *s3Mocks.MockS3Client, enachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao) {
				s3Client.EXPECT().Read(gomock.Any(), defaultS3FilePath).Return(defaultResponseFileBytes, nil)
				enachMandateActionDao.EXPECT().GetByActionTypeAndVendorRequestId(gomock.Any(), enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE, vendorRequestId1).Return(enachMandateAction1, nil)
				enachMandateActionDao.EXPECT().GetByActionTypeAndVendorRequestId(gomock.Any(), enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE, vendorRequestId2).Return(enachMandateAction2, nil)
				enachMandateActionDao.EXPECT().GetByActionTypeAndVendorRequestId(gomock.Any(), enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE, vendorRequestId3).Return(enachMandateAction3, nil)
				enachMandateActionDao.EXPECT().GetByActionTypeAndVendorRequestId(gomock.Any(), enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE, vendorRequestId4).Return(enachMandateAction4, nil)
				enachMandateActionDao.EXPECT().GetByActionTypeAndVendorRequestId(gomock.Any(), enachEnumsPb.EnachActionType_ACTION_TYPE_EXECUTE, vendorRequestId5).Return(enachMandateAction5, nil)
			},
			want: &enachActivityPb.ParseEnachPresentationResponseFileResponse{
				EnachExecutionStatusList: []*enachActivityPb.EnachExecutionStatus{
					{
						ExecutionActionId:                 enachMandateActionId1,
						FundTransferStatus:                enachEnumsPb.FundTransferStatus_FUND_TRANSFER_STATUS_SUCCESSFUL,
						NachTransactionFlag:               enachConstants.AcceptedTransactionFlag,
						NachTransactionResponseReasonCode: "00",
						FiStatusCode:                      "ENACH_EXEC_044",
						FiStatusDesc:                      "Accepted transaction",
					},
					{
						ExecutionActionId:                 enachMandateActionId3,
						FundTransferStatus:                enachEnumsPb.FundTransferStatus_FUND_TRANSFER_STATUS_FAILED,
						NachTransactionFlag:               enachConstants.ReturnedTransactionFlag,
						NachTransactionResponseReasonCode: "01",
						FiStatusCode:                      "ENACH_EXEC_045",
						FiStatusDesc:                      "Account Closed",
					},
					{
						ExecutionActionId:                 enachMandateActionId4,
						FundTransferStatus:                enachEnumsPb.FundTransferStatus_FUND_TRANSFER_STATUS_FAILED,
						NachTransactionFlag:               enachConstants.RejectedTransactionFlag,
						NachTransactionResponseReasonCode: "21",
						FiStatusCode:                      "ENACH_EXEC_001",
						FiStatusDesc:                      "Invalid UMRN or inactive mandate",
					},
					{
						ExecutionActionId:                 enachMandateActionId5,
						FundTransferStatus:                enachEnumsPb.FundTransferStatus_FUND_TRANSFER_STATUS_FAILED,
						NachTransactionFlag:               enachConstants.ReturnedTransactionFlag,
						NachTransactionResponseReasonCode: "00",
						FiStatusCode:                      "",
						FiStatusDesc:                      "",
					},
				},
			},
			wantErr:   false,
			assertErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md, assertTest := newProcessorWithMocks(t)
			defer assertTest()

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			tt.setupMocks(md.s3Client, md.enachMandateActionDao)

			result := &enachActivityPb.ParseEnachPresentationResponseFileResponse{}
			got, err := env.ExecuteActivity(rpNs.ParseEnachPresentationResponseFile, tt.req)

			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("ParseEnachPresentationResponseFile() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("ParseEnachPresentationResponseFile() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("ParseEnachPresentationResponseFile() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("ParseEnachPresentationResponseFile() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
