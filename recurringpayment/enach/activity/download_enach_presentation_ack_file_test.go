package activity_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"testing"
	"time"

	awsS3 "github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	s3mocks "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	enachActivityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	vgEnachPb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
	vgEnachEnums "github.com/epifi/gamma/api/vendorgateway/openbanking/enach/enums"
	vgEnachMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/enach/mocks"
)

func TestProcessor_DownloadEnachPresentationAckFile(t *testing.T) {

	const (
		presentationAckFileS3Path = "presentation/********/NACH_DR_********_UTILITY_CODE_EpifiTechnologies_001-ACK.xml"
	)

	var (
		downloadEnachPresentationAckFileRequest = &enachActivityPb.DownloadEnachPresentationAckFileRequest{
			SettlementDate: timestamppb.New(time.Date(2023, time.October, 1, 0, 0, 0, 0, time.UTC)),
		}
		FileData = []byte("default_test_presentation_ack_file_bytes")
	)

	tests := []struct {
		name       string
		req        *enachActivityPb.DownloadEnachPresentationAckFileRequest
		setupMocks func(enachVgClient *vgEnachMocks.MockEnachClient, mockS3Client *s3mocks.MockS3Client)
		want       *enachActivityPb.DownloadEnachPresentationAckFileResponse
		wantErr    bool
		assertErr  func(err error) bool
	}{
		{
			name: "Should return retryable error if VG API to download file returned error",
			req:  downloadEnachPresentationAckFileRequest,
			setupMocks: func(enachVgClient *vgEnachMocks.MockEnachClient, mockS3Client *s3mocks.MockS3Client) {
				enachVgClient.EXPECT().DownloadFileFromSFTP(gomock.Any(), &vgEnachPb.DownloadFileFromSFTPRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					FileName: "NACH_DR_********_UTILITY_CODE_EpifiTechnologies_001-ACK.xml",
					FileType: vgEnachEnums.EnachFileType_FILE_TYPE_PRESENTATION_ACK_FILE,
				}).Return(nil, epifierrors.ErrInvalidArgument)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "Should return non-retryable error if VG API return record not found error",
			req:  downloadEnachPresentationAckFileRequest,
			setupMocks: func(enachVgClient *vgEnachMocks.MockEnachClient, mockS3Client *s3mocks.MockS3Client) {
				enachVgClient.EXPECT().DownloadFileFromSFTP(gomock.Any(), &vgEnachPb.DownloadFileFromSFTPRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					FileName: "NACH_DR_********_UTILITY_CODE_EpifiTechnologies_001-ACK.xml",
					FileType: vgEnachEnums.EnachFileType_FILE_TYPE_PRESENTATION_ACK_FILE,
				}).Return(&vgEnachPb.DownloadFileFromSFTPResponse{
					Status: rpcPb.StatusRecordNotFound(),
				}, nil)
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "Should return retryable error if VG API return internal error",
			req:  downloadEnachPresentationAckFileRequest,
			setupMocks: func(enachVgClient *vgEnachMocks.MockEnachClient, mockS3Client *s3mocks.MockS3Client) {
				enachVgClient.EXPECT().DownloadFileFromSFTP(gomock.Any(), &vgEnachPb.DownloadFileFromSFTPRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					FileName: "NACH_DR_********_UTILITY_CODE_EpifiTechnologies_001-ACK.xml",
					FileType: vgEnachEnums.EnachFileType_FILE_TYPE_PRESENTATION_ACK_FILE,
				}).Return(&vgEnachPb.DownloadFileFromSFTPResponse{
					Status: rpcPb.StatusInternal(),
				}, nil)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "Should return transient error if uploading file to S3 failed",
			req:  downloadEnachPresentationAckFileRequest,
			setupMocks: func(enachVgClient *vgEnachMocks.MockEnachClient, mockS3Client *s3mocks.MockS3Client) {
				enachVgClient.EXPECT().DownloadFileFromSFTP(gomock.Any(), &vgEnachPb.DownloadFileFromSFTPRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					FileName: "NACH_DR_********_UTILITY_CODE_EpifiTechnologies_001-ACK.xml",
					FileType: vgEnachEnums.EnachFileType_FILE_TYPE_PRESENTATION_ACK_FILE,
				}).Return(&vgEnachPb.DownloadFileFromSFTPResponse{
					Status:   rpcPb.StatusOk(),
					FileData: FileData,
				}, nil)
				mockS3Client.EXPECT().Write(gomock.Any(), presentationAckFileS3Path, FileData, string(awsS3.ObjectCannedACLBucketOwnerFullControl)).Return(epifierrors.ErrTransient)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "Should return success if file fetched and uploaded to S3 successfully",
			req:  downloadEnachPresentationAckFileRequest,
			setupMocks: func(enachVgClient *vgEnachMocks.MockEnachClient, mockS3Client *s3mocks.MockS3Client) {
				enachVgClient.EXPECT().DownloadFileFromSFTP(gomock.Any(), &vgEnachPb.DownloadFileFromSFTPRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					FileName: "NACH_DR_********_UTILITY_CODE_EpifiTechnologies_001-ACK.xml",
					FileType: vgEnachEnums.EnachFileType_FILE_TYPE_PRESENTATION_ACK_FILE,
				}).Return(&vgEnachPb.DownloadFileFromSFTPResponse{
					Status:   rpcPb.StatusOk(),
					FileData: FileData,
				}, nil)
				mockS3Client.EXPECT().Write(gomock.Any(), presentationAckFileS3Path, FileData, string(awsS3.ObjectCannedACLBucketOwnerFullControl)).Return(nil)
			},
			want: &enachActivityPb.DownloadEnachPresentationAckFileResponse{
				PresentationAckFileS3Path: presentationAckFileS3Path,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md, assertTest := newProcessorWithMocks(t)
			defer assertTest()

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			tt.setupMocks(md.vgEnachClient, md.s3Client)

			result := &enachActivityPb.DownloadEnachPresentationAckFileResponse{}
			got, err := env.ExecuteActivity(rpNs.DownloadEnachPresentationAckFile, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("DownloadEnachPresentationAckFile() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("DownloadEnachPresentationAckFile() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("DownloadEnachPresentationAckFile() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("DownloadEnachPresentationAckFile() got = %v, want %v", result, tt.want)
				return
			}
		})
	}

}
