package activity_test

import (
	"testing"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/gamma/api/recurringpayment/enach"
	activityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	enachDaoMocks "github.com/epifi/gamma/recurringpayment/enach/dao/mocks"
)

func TestProcessor_GetEnachMandate(t *testing.T) {

	const (
		defaultEnachMandateId = "default_enach_mandate_id"
	)

	defaultEnachMandate := &enach.EnachMandate{
		Id: defaultEnachMandateId,
	}

	tests := []struct {
		name       string
		req        *activityPb.GetEnachMandateRequest
		setupMocks func(mockEnachMandateDao *enachDaoMocks.MockEnachMandateDao)
		want       *activityPb.GetEnachMandateResponse
		wantErr    bool
		assertErr  func(err error) bool
	}{
		{
			name: "Should return retryable error if unable to fetch enach mandate by id",
			req: &activityPb.GetEnachMandateRequest{
				EnachMandateId: defaultEnachMandateId,
			},
			setupMocks: func(mockEnachMandateDao *enachDaoMocks.MockEnachMandateDao) {
				mockEnachMandateDao.EXPECT().GetById(gomock.Any(), defaultEnachMandateId).Return(nil, epifierrors.ErrTransient)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return success reponse with mandate action details when dao call to fetch mandate is successful",
			req: &activityPb.GetEnachMandateRequest{
				EnachMandateId: defaultEnachMandateId,
			},
			setupMocks: func(mockEnachMandateDao *enachDaoMocks.MockEnachMandateDao) {
				mockEnachMandateDao.EXPECT().GetById(gomock.Any(), defaultEnachMandateId).Return(defaultEnachMandate, nil)
			},
			want: &activityPb.GetEnachMandateResponse{
				EnachMandate: defaultEnachMandate,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md, assertTest := newProcessorWithMocks(t)
			defer assertTest()

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			tt.setupMocks(md.enachMandateDao)

			result := &activityPb.GetEnachMandateResponse{}
			got, err := env.ExecuteActivity(rpNs.GetEnachMandate, tt.req)

			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("EnquireDomainActivationStatus() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("GetEnachMandate() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("GetEnachMandate() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("GetEnachMandate() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
