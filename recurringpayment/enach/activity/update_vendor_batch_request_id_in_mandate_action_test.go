package activity_test

import (
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/gamma/api/recurringpayment/enach"
	activityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	testPkg "github.com/epifi/be-common/pkg/test"
	enachDaoMocks "github.com/epifi/gamma/recurringpayment/enach/dao/mocks"
)

func TestProcessor_UpdateVendorBatchRequestIdInMandateAction(t *testing.T) {

	const (
		defaultEnachMandateActionId = "default_enach_mandate_action_id"
		defaultVendorBatchRequestId = "default_vendor_batch_request_id"
	)

	defaultEnachMandateAction := &enach.EnachMandateAction{
		Id: defaultEnachMandateActionId,
	}
	defaultUpdatedEnachMandateAction := &enach.EnachMandateAction{
		Id:                   defaultEnachMandateActionId,
		VendorBatchRequestId: defaultVendorBatchRequestId,
	}
	defaultUnExpectedEnachMandateAction := &enach.EnachMandateAction{
		Id:                   defaultEnachMandateActionId,
		VendorBatchRequestId: "random_vendor_batch_request_id",
	}

	defaultUpdateVendorBatchRequestIdInMandateActionRequest := &activityPb.UpdateVendorBatchRequestIdInMandateActionRequest{
		EnachMandateActionId: defaultEnachMandateActionId,
		VendorBatchRequestId: defaultVendorBatchRequestId,
	}

	updateMask := []enachEnumsPb.EnachMandateActionFieldMask{
		enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_VENDOR_BATCH_REQUEST_ID,
	}

	tests := []struct {
		name       string
		req        *activityPb.UpdateVendorBatchRequestIdInMandateActionRequest
		setupMocks func(mockEnachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao)
		want       *activityPb.UpdateVendorBatchRequestIdInMandateActionResponse
		wantErr    bool
		assertErr  func(err error) bool
	}{
		{
			name: "Should return retryable error if unable to fetch enach mandate action by id",
			req:  defaultUpdateVendorBatchRequestIdInMandateActionRequest,
			setupMocks: func(mockEnachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao) {
				mockEnachMandateActionDao.EXPECT().GetById(gomock.Any(), defaultEnachMandateActionId).Return(nil, epifierrors.ErrTransient)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "Should return empty response (i.e success) if the enach mandate action is already updated",
			req:  defaultUpdateVendorBatchRequestIdInMandateActionRequest,
			setupMocks: func(mockEnachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao) {
				mockEnachMandateActionDao.EXPECT().GetById(gomock.Any(), defaultEnachMandateActionId).Return(defaultUpdatedEnachMandateAction, nil)
			},
			want:    &activityPb.UpdateVendorBatchRequestIdInMandateActionResponse{},
			wantErr: false,
		},
		{
			name: "Should return non retryable error if enach mandate action is already in unexpected state",
			req:  defaultUpdateVendorBatchRequestIdInMandateActionRequest,
			setupMocks: func(mockEnachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao) {
				mockEnachMandateActionDao.EXPECT().GetById(gomock.Any(), defaultEnachMandateActionId).Return(defaultUnExpectedEnachMandateAction, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Should return retryable error if unable to update the enach mandate action",
			req:  defaultUpdateVendorBatchRequestIdInMandateActionRequest,
			setupMocks: func(mockEnachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao) {
				clonedDefaultEnachMandateAction := proto.Clone(defaultEnachMandateAction)
				mockEnachMandateActionDao.EXPECT().GetById(gomock.Any(), defaultEnachMandateActionId).Return(clonedDefaultEnachMandateAction, nil)
				mockEnachMandateActionDao.EXPECT().Update(gomock.Any(), updateMask, testPkg.NewProtoArgMatcher(defaultUpdatedEnachMandateAction)).Return(epifierrors.ErrTransient)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "Should return empty response (i.e success) if enach mandate action updated successfully",
			req:  defaultUpdateVendorBatchRequestIdInMandateActionRequest,
			setupMocks: func(mockEnachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao) {
				clonedDefaultEnachMandateAction := proto.Clone(defaultEnachMandateAction)
				mockEnachMandateActionDao.EXPECT().GetById(gomock.Any(), defaultEnachMandateActionId).Return(clonedDefaultEnachMandateAction, nil)
				mockEnachMandateActionDao.EXPECT().Update(gomock.Any(), updateMask, testPkg.NewProtoArgMatcher(defaultUpdatedEnachMandateAction)).Return(nil)
			},
			want:    &activityPb.UpdateVendorBatchRequestIdInMandateActionResponse{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			p, md, assertTest := newProcessorWithMocks(t)
			defer assertTest()

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			tt.setupMocks(md.enachMandateActionDao)

			result := &activityPb.UpdateVendorBatchRequestIdInMandateActionResponse{}
			got, err := env.ExecuteActivity(rpNs.UpdateVendorBatchRequestIdInMandateAction, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("UpdateVendorBatchRequestIdInMandateAction() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("UpdateVendorBatchRequestIdInMandateAction() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && tt.assertErr != nil && !tt.assertErr(err):
				t.Errorf("UpdateVendorBatchRequestIdInMandateAction() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("UpdateVendorBatchRequestIdInMandateAction() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
