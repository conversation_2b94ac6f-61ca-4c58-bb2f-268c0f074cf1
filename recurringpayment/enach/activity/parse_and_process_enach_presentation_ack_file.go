package activity

import (
	"context"
	"encoding/xml"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	enachActivityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
)

// Enach Presentation Ack Response Contract -> https://docs.google.com/spreadsheets/d/1b2vxUaz53dqw8ob9MMJM3NrtRW27oPk5S7abSCgxQmQ/edit#gid=1666207436
type AckResponse struct {
	XMLName         xml.Name         `xml:"Document"`
	FIToFIPmtStsRpt *FIToFIPmtStsRpt `xml:"FIToFIPmtStsRpt"`
}

type FIToFIPmtStsRpt struct {
	OrgnlGrpInfAndSts *OrgnlGrpInfAndSts `xml:"OrgnlGrpInfAndSts"`
}

type OrgnlGrpInfAndSts struct {
	OrgnlMsgId   string `xml:"OrgnlMsgId"`
	OrgnlMsgNmId string `xml:"OrgnlMsgNmId"`
	GrpSts       string `xml:"GrpSts"`
}

// nolint: funlen
func (p *Processor) ParseAndProcessEnachPresentationAckFile(ctx context.Context, req *enachActivityPb.ParseAndProcessEnachPresentationAckFileRequest) (*enachActivityPb.ParseAndProcessEnachPresentationAckFileResponse, error) {

	const (
		ackFileAcceptedResponseStatus          = "ACCP"
		ackFileRejectedResponseStatus          = "RJCT"
		ackFilePartiallyAcceptedResponseStatus = "PART"
	)
	var (
		res                               = &enachActivityPb.ParseAndProcessEnachPresentationAckFileResponse{}
		failedEnachExecutionActionIdsList []string
	)

	// get file bytes for the presentation file from s3
	responseFileBytes, err := p.getPresentationRecordFileBytesFromS3(ctx, &getPresentationResponseFileBytesFromS3Req{
		presentationResponseFileS3Path: req.GetPresentationAckFileS3Path(),
	})
	if err != nil {
		logger.Error(ctx, "unable to download file from s3", zap.String(logger.S3_PATH, req.GetPresentationAckFileS3Path()), zap.Error(err))
		return nil, fmt.Errorf("unable to download presentation ack file from s3 %s, %w", err, epifierrors.ErrTransient)
	}

	var ackResponse AckResponse
	err = xml.Unmarshal(responseFileBytes, &ackResponse)
	if err != nil {
		logger.Error(ctx, "failed to unmarshal ack response xml into struct", zap.String(logger.S3_PATH, req.GetPresentationAckFileS3Path()), zap.Error(err))
		return nil, fmt.Errorf("failed to unmarshal ack response xml into struct %s, %w", err, epifierrors.ErrPermanent)
	}
	presentationAckStatus := ackResponse.FIToFIPmtStsRpt.OrgnlGrpInfAndSts.GrpSts
	presentationRequestId := ackResponse.FIToFIPmtStsRpt.OrgnlGrpInfAndSts.OrgnlMsgId

	logger.Info(ctx, "presentation ack file is parsed successfully", zap.String(logger.BATCH_ID, presentationRequestId), zap.String(logger.STATUS, presentationAckStatus))

	switch presentationAckStatus {
	case ackFileAcceptedResponseStatus:
		// since presentation file is accepted by the vendor, no further processing is needed
		res.FailedEnachExecutionActionIds = nil
		return res, nil
	case ackFilePartiallyAcceptedResponseStatus:
		// As of now Federal is not sending this response status.
		// Federal can send this response in future, In this case we have to check this with federal since logic for partial accepted response is not handled.
		logger.Error(ctx, "unhandled ack file status", zap.String(logger.STATUS, presentationAckStatus), zap.String(logger.S3_PATH, req.GetPresentationAckFileS3Path()))
		return nil, fmt.Errorf("unhandled ack file status : PART, %s, %w", err, epifierrors.ErrPermanent)
	case ackFileRejectedResponseStatus:
		// fetch the enach mandate actions with vendor request id
		// marked the workflow for those mandates as failed.
	default:
		logger.Error(ctx, "GrpSts status is not handled", zap.String(logger.STATUS, presentationAckStatus), zap.String(logger.S3_PATH, req.GetPresentationAckFileS3Path()))
		return nil, fmt.Errorf("GrpSts status is not valid %s, %w", err, epifierrors.ErrPermanent)
	}

	// since presentation ack status is failed, fetching all the executions presented in the corresponding presentation
	// file using presentation request id to the mark those executions as failed at later stage
	enachMandateActions, err := p.enachMandateActionDao.GetByVendorBatchRequestId(ctx, presentationRequestId)
	if err != nil {
		logger.Error(ctx, "error while fetching enach mandate action by vendor batch request id", zap.Error(err), zap.String(logger.BATCH_ID, presentationRequestId))
		return nil, fmt.Errorf("error while fetching enach mandate action by vendor batch request id, err: %s, %w", err, epifierrors.ErrTransient)
	}

	for _, enachMandateAction := range enachMandateActions {
		failedEnachExecutionActionIdsList = append(failedEnachExecutionActionIdsList, enachMandateAction.GetId())
	}

	res.FailedEnachExecutionActionIds = failedEnachExecutionActionIdsList
	return res, nil
}
