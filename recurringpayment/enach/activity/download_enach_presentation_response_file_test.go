package activity_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"errors"
	"testing"

	awsS3 "github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	rpcPb "github.com/epifi/be-common/api/rpc"
	s3mocks "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	enachActivityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	vgEnachPb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
	vgEnachMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/enach/mocks"
)

func TestProcessor_DownloadEnachExecutionPresentationFile(t *testing.T) {
	const (
		defaultFileDateString                 = "********"
		defaultSequenceNumber                 = 1
		defaultMaxSequenceNumber              = 3
		defaultPresentationResponseFileS3Path = "presentation/********/NACH_DR_********_UTILITY_CODE_EpifiTechnologies_001-R3FC-01-RES.txt"
	)

	var (
		defaultDownloadEnachPresentationResponseFileReq = &enachActivityPb.DownloadEnachPresentationResponseFileRequest{
			FileSeqNumber:    defaultSequenceNumber,
			FileDateString:   defaultFileDateString,
			FileMaxSeqNumber: defaultMaxSequenceNumber,
		}
		defaultPresentationResponseFileBytes = []byte("default_test_presentation_response_file_bytes")
	)

	tests := []struct {
		name       string
		req        *enachActivityPb.DownloadEnachPresentationResponseFileRequest
		setupMocks func(enachVgClient *vgEnachMocks.MockEnachClient, mockS3Client *s3mocks.MockS3Client)
		want       *enachActivityPb.DownloadEnachPresentationResponseFileResponse
		wantErr    bool
		err        error
	}{
		{
			name: "Should return transient error if VG API to download file returned error",
			req:  defaultDownloadEnachPresentationResponseFileReq,
			setupMocks: func(mockEnachVgClient *vgEnachMocks.MockEnachClient, mockS3Client *s3mocks.MockS3Client) {
				mockEnachVgClient.EXPECT().DownloadPresentationResponseFileFromSFTP(gomock.Any(), &vgEnachPb.DownloadPresentationResponseFileFromSFTPRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					FileName: "NACH_DR_********_UTILITY_CODE_EpifiTechnologies_001-R3FC-01-RES.txt",
				}).
					Return(&vgEnachPb.DownloadPresentationResponseFileFromSFTPResponse{
						Status: rpcPb.StatusInternal(),
					}, nil)
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrTransient,
		},
		{
			name: "Should return record not found error if VG API to download file returned error",
			req:  defaultDownloadEnachPresentationResponseFileReq,
			setupMocks: func(mockEnachVgClient *vgEnachMocks.MockEnachClient, mockS3Client *s3mocks.MockS3Client) {
				mockEnachVgClient.EXPECT().DownloadPresentationResponseFileFromSFTP(gomock.Any(), &vgEnachPb.DownloadPresentationResponseFileFromSFTPRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					FileName: "NACH_DR_********_UTILITY_CODE_EpifiTechnologies_001-R3FC-01-RES.txt",
				}).
					Return(&vgEnachPb.DownloadPresentationResponseFileFromSFTPResponse{
						Status: rpcPb.StatusRecordNotFound(),
					}, nil)
				mockEnachVgClient.EXPECT().DownloadPresentationResponseFileFromSFTP(gomock.Any(), &vgEnachPb.DownloadPresentationResponseFileFromSFTPRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					FileName: "NACH_DR_********_UTILITY_CODE_EpifiTechnologies_001-R3FC-02-RES.txt",
				}).
					Return(&vgEnachPb.DownloadPresentationResponseFileFromSFTPResponse{
						Status: rpcPb.StatusRecordNotFound(),
					}, nil)
				mockEnachVgClient.EXPECT().DownloadPresentationResponseFileFromSFTP(gomock.Any(), &vgEnachPb.DownloadPresentationResponseFileFromSFTPRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					FileName: "NACH_DR_********_UTILITY_CODE_EpifiTechnologies_001-R3FC-03-RES.txt",
				}).
					Return(&vgEnachPb.DownloadPresentationResponseFileFromSFTPResponse{
						Status: rpcPb.StatusRecordNotFound(),
					}, nil)

				mockEnachVgClient.EXPECT().DownloadPresentationResponseFileFromSFTP(gomock.Any(), &vgEnachPb.DownloadPresentationResponseFileFromSFTPRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					FileName: "NACH_DR_********_UTILITY_CODE_EpifiTechnologies_001-RES.txt",
				}).Return(&vgEnachPb.DownloadPresentationResponseFileFromSFTPResponse{
					Status: rpcPb.StatusRecordNotFound(),
				}, nil)
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
		{
			name: "Should return transient error if uploading file to S3 failed",
			req:  defaultDownloadEnachPresentationResponseFileReq,
			setupMocks: func(mockEnachVgClient *vgEnachMocks.MockEnachClient, mockS3Client *s3mocks.MockS3Client) {
				mockEnachVgClient.EXPECT().DownloadPresentationResponseFileFromSFTP(gomock.Any(), &vgEnachPb.DownloadPresentationResponseFileFromSFTPRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					FileName: "NACH_DR_********_UTILITY_CODE_EpifiTechnologies_001-R3FC-01-RES.txt",
				}).
					Return(&vgEnachPb.DownloadPresentationResponseFileFromSFTPResponse{
						Status:                        rpcPb.StatusOk(),
						PresentationResponseFileBytes: defaultPresentationResponseFileBytes,
					}, nil)
				mockS3Client.EXPECT().Write(gomock.Any(), defaultPresentationResponseFileS3Path, defaultPresentationResponseFileBytes, string(awsS3.ObjectCannedACLBucketOwnerFullControl)).Return(epifierrors.ErrPermanent)
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrTransient,
		},
		{
			name: "Should return success if file fetched and uploaded successfully",
			req:  defaultDownloadEnachPresentationResponseFileReq,
			setupMocks: func(mockEnachVgClient *vgEnachMocks.MockEnachClient, mockS3Client *s3mocks.MockS3Client) {
				mockEnachVgClient.EXPECT().DownloadPresentationResponseFileFromSFTP(gomock.Any(), &vgEnachPb.DownloadPresentationResponseFileFromSFTPRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					FileName: "NACH_DR_********_UTILITY_CODE_EpifiTechnologies_001-R3FC-01-RES.txt",
				}).
					Return(&vgEnachPb.DownloadPresentationResponseFileFromSFTPResponse{
						Status:                        rpcPb.StatusOk(),
						PresentationResponseFileBytes: defaultPresentationResponseFileBytes,
					}, nil)
				mockS3Client.EXPECT().Write(gomock.Any(), defaultPresentationResponseFileS3Path, defaultPresentationResponseFileBytes, string(awsS3.ObjectCannedACLBucketOwnerFullControl)).Return(nil)
			},
			want: &enachActivityPb.DownloadEnachPresentationResponseFileResponse{
				PresentationResponseFileS3Path: defaultPresentationResponseFileS3Path,
			},
			wantErr: false,
		},
		{
			name: "Should return success if file fetched and uploaded successfully",
			req:  defaultDownloadEnachPresentationResponseFileReq,
			setupMocks: func(mockEnachVgClient *vgEnachMocks.MockEnachClient, mockS3Client *s3mocks.MockS3Client) {
				mockEnachVgClient.EXPECT().DownloadPresentationResponseFileFromSFTP(gomock.Any(), &vgEnachPb.DownloadPresentationResponseFileFromSFTPRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					FileName: "NACH_DR_********_UTILITY_CODE_EpifiTechnologies_001-R3FC-01-RES.txt",
				}).
					Return(&vgEnachPb.DownloadPresentationResponseFileFromSFTPResponse{
						Status: rpcPb.StatusRecordNotFound(),
					}, nil)
				mockEnachVgClient.EXPECT().DownloadPresentationResponseFileFromSFTP(gomock.Any(), &vgEnachPb.DownloadPresentationResponseFileFromSFTPRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					FileName: "NACH_DR_********_UTILITY_CODE_EpifiTechnologies_001-R3FC-02-RES.txt",
				}).
					Return(&vgEnachPb.DownloadPresentationResponseFileFromSFTPResponse{
						Status: rpcPb.StatusRecordNotFound(),
					}, nil)
				mockEnachVgClient.EXPECT().DownloadPresentationResponseFileFromSFTP(gomock.Any(), &vgEnachPb.DownloadPresentationResponseFileFromSFTPRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					FileName: "NACH_DR_********_UTILITY_CODE_EpifiTechnologies_001-R3FC-03-RES.txt",
				}).
					Return(&vgEnachPb.DownloadPresentationResponseFileFromSFTPResponse{
						Status: rpcPb.StatusRecordNotFound(),
					}, nil)

				mockEnachVgClient.EXPECT().DownloadPresentationResponseFileFromSFTP(gomock.Any(), &vgEnachPb.DownloadPresentationResponseFileFromSFTPRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					FileName: "NACH_DR_********_UTILITY_CODE_EpifiTechnologies_001-RES.txt",
				}).Return(&vgEnachPb.DownloadPresentationResponseFileFromSFTPResponse{
					Status:                        rpcPb.StatusOk(),
					PresentationResponseFileBytes: defaultPresentationResponseFileBytes,
				}, nil)
				mockS3Client.EXPECT().Write(gomock.Any(), "presentation/********/NACH_DR_********_UTILITY_CODE_EpifiTechnologies_001-RES.txt", defaultPresentationResponseFileBytes, string(awsS3.ObjectCannedACLBucketOwnerFullControl)).Return(nil)
			},
			want: &enachActivityPb.DownloadEnachPresentationResponseFileResponse{
				PresentationResponseFileS3Path: "presentation/********/NACH_DR_********_UTILITY_CODE_EpifiTechnologies_001-RES.txt",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md, assertTest := newProcessorWithMocks(t)
			defer assertTest()

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			tt.setupMocks(md.vgEnachClient, md.s3Client)

			result := &enachActivityPb.DownloadEnachPresentationResponseFileResponse{}
			got, err := env.ExecuteActivity(rpNs.DownloadEnachPresentationResponseFile, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("DownloadEnachPresentationResponseFile() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			if (err != nil) != tt.wantErr && !errors.Is(err, tt.err) {
				t.Errorf("DownloadEnachPresentationResponseFile() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !proto.Equal(result, tt.want) {
				t.Errorf("DownloadEnachPresentationResponseFile() got = %v, want %v", result, tt.want)
				return
			}
		})
	}

}
