package activity_test

import (
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/gamma/api/recurringpayment/enach"
	activityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	testPkg "github.com/epifi/be-common/pkg/test"
	enachDaoMocks "github.com/epifi/gamma/recurringpayment/enach/dao/mocks"
)

func TestProcessor_UpdateMandateActionStatus(t *testing.T) {

	const (
		defaultEnachMandateActionId = "default_enach_mandate_action_id"
	)

	defaultEnachMandateAction := &enach.EnachMandateAction{
		Id:              defaultEnachMandateActionId,
		ActionStatus:    enachEnumsPb.EnachActionStatus_ENACH_ACTION_STATUS_UNSPECIFIED,
		ActionSubStatus: enachEnumsPb.EnachActionSubStatus_ENACH_ACTION_SUB_STATUS_UNSPECIFIED,
	}

	defaultUpdateMandateActionStatusRequest := &activityPb.UpdateEnachMandateActionStatusRequest{
		EnachMandateActionId:     defaultEnachMandateActionId,
		ExpectedCurrentStatus:    enachEnumsPb.EnachActionStatus_ENACH_ACTION_STATUS_UNSPECIFIED,
		ExpectedCurrentSubStatus: enachEnumsPb.EnachActionSubStatus_ENACH_ACTION_SUB_STATUS_UNSPECIFIED,
		NewStatus:                enachEnumsPb.EnachActionStatus_ACTION_STATUS_CREATED,
		NewSubStatus:             enachEnumsPb.EnachActionSubStatus_ACTION_SUB_STATUS_AWAITING_AUTHORIZATION,
	}

	updateMask := []enachEnumsPb.EnachMandateActionFieldMask{
		enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_STATUS,
		enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_SUB_STATUS,
		enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_DETAILED_STATUS,
	}

	tests := []struct {
		name       string
		req        *activityPb.UpdateEnachMandateActionStatusRequest
		setupMocks func(mockEnachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao)
		want       *activityPb.UpdateEnachMandateActionStatusResponse
		wantErr    bool
		assertErr  func(err error) bool
	}{
		{
			name: "Should return retryable error if unable to fetch enach mandate action by id",
			req: &activityPb.UpdateEnachMandateActionStatusRequest{
				EnachMandateActionId:     defaultEnachMandateActionId,
				ExpectedCurrentStatus:    enachEnumsPb.EnachActionStatus_ENACH_ACTION_STATUS_UNSPECIFIED,
				ExpectedCurrentSubStatus: enachEnumsPb.EnachActionSubStatus_ENACH_ACTION_SUB_STATUS_UNSPECIFIED,
				NewStatus:                enachEnumsPb.EnachActionStatus_ACTION_STATUS_CREATED,
				NewSubStatus:             enachEnumsPb.EnachActionSubStatus_ACTION_SUB_STATUS_AWAITING_AUTHORIZATION,
			},
			setupMocks: func(mockEnachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao) {
				mockEnachMandateActionDao.EXPECT().GetById(gomock.Any(), defaultEnachMandateActionId).Return(nil, epifierrors.ErrTransient)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "Should return empty response (i.e success) if the enach mandate status and sub status is already updated",
			req: &activityPb.UpdateEnachMandateActionStatusRequest{
				EnachMandateActionId:     defaultEnachMandateActionId,
				ExpectedCurrentStatus:    enachEnumsPb.EnachActionStatus_ENACH_ACTION_STATUS_UNSPECIFIED,
				ExpectedCurrentSubStatus: enachEnumsPb.EnachActionSubStatus_ENACH_ACTION_SUB_STATUS_UNSPECIFIED,
				NewStatus:                enachEnumsPb.EnachActionStatus_ENACH_ACTION_STATUS_UNSPECIFIED,
				NewSubStatus:             enachEnumsPb.EnachActionSubStatus_ENACH_ACTION_SUB_STATUS_UNSPECIFIED,
			},
			setupMocks: func(mockEnachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao) {
				mockEnachMandateActionDao.EXPECT().GetById(gomock.Any(), defaultEnachMandateActionId).Return(defaultEnachMandateAction, nil)
			},
			want:    &activityPb.UpdateEnachMandateActionStatusResponse{},
			wantErr: false,
		},
		{
			name: "Should return retryable error if unable to update the enach mandate action status and substatus",
			req: &activityPb.UpdateEnachMandateActionStatusRequest{
				EnachMandateActionId:     defaultEnachMandateActionId,
				ExpectedCurrentStatus:    enachEnumsPb.EnachActionStatus_ENACH_ACTION_STATUS_UNSPECIFIED,
				ExpectedCurrentSubStatus: enachEnumsPb.EnachActionSubStatus_ENACH_ACTION_SUB_STATUS_UNSPECIFIED,
				NewStatus:                enachEnumsPb.EnachActionStatus_ACTION_STATUS_CREATED,
				NewSubStatus:             enachEnumsPb.EnachActionSubStatus_ACTION_SUB_STATUS_AWAITING_AUTHORIZATION,
			},
			setupMocks: func(mockEnachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao) {
				mockEnachMandateActionDao.EXPECT().GetById(gomock.Any(), defaultEnachMandateActionId).Return(defaultEnachMandateAction, nil)
				mockEnachMandateActionDao.EXPECT().UpdateWithStatusCheck(gomock.Any(), updateMask, testPkg.NewProtoArgMatcher(defaultEnachMandateAction), defaultUpdateMandateActionStatusRequest.GetExpectedCurrentStatus(), defaultUpdateMandateActionStatusRequest.GetExpectedCurrentSubStatus()).Return(epifierrors.ErrTransient)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "Should return empty response (i.e success) if enach mandate action status and substatus got updated successfully",
			req: &activityPb.UpdateEnachMandateActionStatusRequest{
				EnachMandateActionId:     defaultEnachMandateActionId,
				ExpectedCurrentStatus:    enachEnumsPb.EnachActionStatus_ENACH_ACTION_STATUS_UNSPECIFIED,
				ExpectedCurrentSubStatus: enachEnumsPb.EnachActionSubStatus_ENACH_ACTION_SUB_STATUS_UNSPECIFIED,
				NewStatus:                enachEnumsPb.EnachActionStatus_ACTION_STATUS_CREATED,
				NewSubStatus:             enachEnumsPb.EnachActionSubStatus_ACTION_SUB_STATUS_AWAITING_AUTHORIZATION,
			},
			setupMocks: func(mockEnachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao) {
				defaultEnachMandateAction := &enach.EnachMandateAction{
					Id:              defaultEnachMandateActionId,
					ActionStatus:    enachEnumsPb.EnachActionStatus_ENACH_ACTION_STATUS_UNSPECIFIED,
					ActionSubStatus: enachEnumsPb.EnachActionSubStatus_ENACH_ACTION_SUB_STATUS_UNSPECIFIED,
				}
				mockEnachMandateActionDao.EXPECT().GetById(gomock.Any(), defaultEnachMandateActionId).Return(defaultEnachMandateAction, nil)
				mockEnachMandateActionDao.EXPECT().UpdateWithStatusCheck(gomock.Any(), updateMask, testPkg.NewProtoArgMatcher(defaultEnachMandateAction), defaultUpdateMandateActionStatusRequest.GetExpectedCurrentStatus(), defaultUpdateMandateActionStatusRequest.GetExpectedCurrentSubStatus()).Return(nil)
			},
			want:    &activityPb.UpdateEnachMandateActionStatusResponse{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md, assertTest := newProcessorWithMocks(t)
			defer assertTest()

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			tt.setupMocks(md.enachMandateActionDao)

			result := &activityPb.UpdateEnachMandateActionStatusResponse{}
			got, err := env.ExecuteActivity(rpNs.UpdateEnachMandateActionStatus, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("UpdateEnachMandateActionStatus() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("UpdateMandateActionStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("UpdateMandateActionStatus() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("UpdateMandateActionStatus() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
