package activity_test

import (
	"testing"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/gamma/api/recurringpayment/enach"
	activityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	enachDaoMocks "github.com/epifi/gamma/recurringpayment/enach/dao/mocks"
)

func TestProcessor_GetEnachMandateAction(t *testing.T) {

	const (
		defaultEnachMandateActionId = "default_enach_mandate_action_id"
	)

	defaultEnachMandateAction := &enach.EnachMandateAction{
		Id: defaultEnachMandateActionId,
	}

	tests := []struct {
		name       string
		req        *activityPb.GetEnachMandateActionRequest
		setupMocks func(mockEnachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao)
		want       *activityPb.GetEnachMandateActionResponse
		wantErr    bool
		assertErr  func(err error) bool
	}{
		{
			name: "Should return retryable error if unable to fetch enach mandate action by id",
			req: &activityPb.GetEnachMandateActionRequest{
				EnachMandateActionId: defaultEnachMandateActionId,
			},
			setupMocks: func(mockEnachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao) {
				mockEnachMandateActionDao.EXPECT().GetById(gomock.Any(), defaultEnachMandateActionId).Return(nil, epifierrors.ErrTransient)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return success reponse with mandate action details when dao call to fetch mandate action is successful",
			req: &activityPb.GetEnachMandateActionRequest{
				EnachMandateActionId: defaultEnachMandateActionId,
			},
			setupMocks: func(mockEnachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao) {
				mockEnachMandateActionDao.EXPECT().GetById(gomock.Any(), defaultEnachMandateActionId).Return(defaultEnachMandateAction, nil)
			},
			want: &activityPb.GetEnachMandateActionResponse{
				EnachMandateAction: defaultEnachMandateAction,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md, assertTest := newProcessorWithMocks(t)
			defer assertTest()

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			tt.setupMocks(md.enachMandateActionDao)

			result := &activityPb.GetEnachMandateActionResponse{}
			got, err := env.ExecuteActivity(rpNs.GetEnachMandateAction, tt.req)

			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("EnquireDomainActivationStatus() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("GetEnachMandateAction() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("GetEnachMandateAction() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("GetEnachMandateAction() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
