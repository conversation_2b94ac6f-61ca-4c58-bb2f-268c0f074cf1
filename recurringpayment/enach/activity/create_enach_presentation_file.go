package activity

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"bytes"
	"context"
	"fmt"
	"strconv"

	awsS3 "github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"

	celestialPb "github.com/epifi/be-common/api/celestial"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	piPb "github.com/epifi/gamma/api/paymentinstrument"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachActivityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	enachConstants "github.com/epifi/gamma/recurringpayment/enach/constants"
)

const (
	presentationFileEntryExpectedLength = 306
	// Templates for the header and the entry are as per the conventions set in the API contracts between Fi and Federal.
	// Reference doc: https://docs.google.com/spreadsheets/d/121dSl4Ww_vZq_HjBSKmWdfM33u92R6Gj3ZkTiD4Fp9Y/edit#gid=0
	enachPresentationEntryTemplateString  = "%s%9s%s%3s%15s%-40s%9s%7s%-20s%13s%013d%10s%10s%1s%2s%-11s%-35s%-11s%-18s%-30s%-3s%15s%-20s%7s"
	enachPresentationHeaderTemplateString = "%s%7s%-40s%14s%9s%9s%15s%3s%13s%013d%-8s%10s%10s%3s%-18s%-18s%-11s%-35s%09d%2s%57s"
)

var (
	LengthExpectationsNotMatchedForField = fmt.Errorf("value of the field does not match the length expectations set as per contract %w", epifierrors.ErrPermanent)
)

// CreateEnachPresentationFile activity is responsible for creating the presentation file and uploading it to S3
// This activity on a high level preforms the following steps:
// 1. Fetch workflows for which we wish to create the execution file.
// 2. Get row entries to be added to the file.
// 3. Generate batch execution id and header entry
// 4. Write Entries to file
// 5. Upload file to S3
// Refer the link for presentation file contract -> https://docs.google.com/spreadsheets/d/1azk4v-aHh73fIaFr5dcpa22RYDI3MuQk/edit#gid=1579418525
// nolint: funlen
func (p *Processor) CreateEnachPresentationFile(ctx context.Context, req *enachActivityPb.CreateEnachPresentationFileRequest) (*enachActivityPb.CreateEnachPresentationFileResponse, error) {
	var (
		res = &enachActivityPb.CreateEnachPresentationFileResponse{}
		// settlement date should be presentation date + 1
		settlementDateString = p.clock.Now().AddDate(0, 0, 1).In(datetime.IST).Format(enachConstants.PresentationFileDateFormat)
	)

	logger.Info(ctx, "settlement date string", zap.String("settlementDateString", settlementDateString))

	// Fetch workflows for which we wish to create the execution file.
	getWorkflowRequestsByFiltersRes, err := p.celestialClient.GetWorkflowRequestsByFilters(ctx, &celestialPb.GetWorkflowRequestsByFiltersRequest{
		Status:    stagePb.Status_BLOCKED,
		TypeEnum:  celestialPkg.GetTypeEnumFromWorkflowType(rpNs.ExecuteEnachViaPoolAccountTransfer),
		StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.EnachFundTransferToPoolAccount),
	})
	if err = epifigrpc.RPCError(getWorkflowRequestsByFiltersRes, err); err != nil {
		if getWorkflowRequestsByFiltersRes.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "No workflows blocked on Enach Presentation file. Terminating presentation file generation gracefully")
			return res, nil
		}
		logger.Error(ctx, "error while fetching workflows blocked on pool account transfer", zap.Error(err))
		return nil, fmt.Errorf("error while fetching workflows blocked on pool account transfer, err: %s, %w", err, epifierrors.ErrTransient)
	}

	// Get row entries to be added to the file.
	fileEntriesAndTotalTxnAmtRes, err := p.getPresentationFileEntriesAndTotalTxnAmount(ctx, &getFileEntriesAndTxnAmountReq{
		workflowRequests:      getWorkflowRequestsByFiltersRes.GetWfReqList(),
		isLastActivityAttempt: req.GetRequestHeader().GetIsLastAttempt(),
	})
	if err != nil {
		logger.Error(ctx, "error while fetching file entries and total transaction amount", zap.Error(err))
		return nil, fmt.Errorf("error while fetching file entries and total transaction amount, err: %s, %w", err, epifierrors.ErrTransient)
	}
	if len(fileEntriesAndTotalTxnAmtRes.fileEntries) == 0 {
		logger.Error(ctx, "no entries to add to file")
		return nil, fmt.Errorf("no entries to add to file, %w", epifierrors.ErrTransient)
	}

	// Generate batch execution id and header entry
	batchExecutionId, err := p.generateMandateExecutionBatchRequestId(ctx, commonvgpb.Vendor_FEDERAL_BANK)
	if err != nil {
		logger.Error(ctx, "unable to generate batch request id", zap.Error(err))
		return nil, fmt.Errorf("unable to generate batch request id %s, %w", err, epifierrors.ErrTransient)
	}
	fileHeaderString, err := p.getPresentationFileHeaderString(&getPresentationFileHeaderStringReq{
		totalTxnAmount:       fileEntriesAndTotalTxnAmtRes.totalTxnAmount,
		numEntries:           len(fileEntriesAndTotalTxnAmtRes.fileEntries),
		batchRefNumber:       batchExecutionId,
		settlementDateString: settlementDateString,
	})
	if err != nil {
		logger.Error(ctx, "unable to fetch presentation file header string", zap.Error(err))
		return nil, fmt.Errorf("unable to fetch presentation file header string %s, %w", err, epifierrors.ErrPermanent)
	}

	// Write Entries to buffer
	bytesBuffer := new(bytes.Buffer)
	_, err = bytesBuffer.WriteString(fileHeaderString + "\n")
	if err != nil {
		logger.Error(ctx, "unable to write header string to file", zap.Error(err))
		return nil, fmt.Errorf("unable to write header string to file %s, %w", err, epifierrors.ErrTransient)
	}
	for _, debitEntry := range fileEntriesAndTotalTxnAmtRes.fileEntries {
		_, err = bytesBuffer.WriteString(debitEntry + "\n")
		if err != nil {
			logger.Error(ctx, "unable to write entry string to file", zap.Error(err))
			return nil, fmt.Errorf("unable to write entry string to file %s, %w", err, epifierrors.ErrTransient)
		}
	}

	// Upload file to S3
	fileName := p.getFileName(settlementDateString)
	s3FilePath, err := p.uploadPresentationRecordFileToS3(ctx, &uploadPresentationRecordFileToS3Req{
		fileBytes:            bytesBuffer.Bytes(),
		fileName:             fileName,
		settlementDateString: settlementDateString,
	})
	if err != nil {
		logger.Error(ctx, "unable to upload file to s3", zap.Error(err))
		return nil, fmt.Errorf("unable to upload file to s3 %s, %w", err, epifierrors.ErrTransient)
	}

	logger.Info(ctx, "successfully upload presentation file to S3", zap.String(logger.FILE_NAME, fileName), zap.String(logger.BATCH_ID, batchExecutionId), zap.String("settlementDateString", settlementDateString))

	res.PresentedActionExecutionIds = fileEntriesAndTotalTxnAmtRes.actionExecutionIds
	res.PresentationFileS3Path = s3FilePath
	res.PresentationFileName = fileName
	res.PresentationBatchExecutionId = batchExecutionId
	return res, nil
}

func (p *Processor) getFileName(settlementDateString string) string {
	return fmt.Sprintf("NACH_DR_%s_%s_%s_001.txt", settlementDateString, p.enachSecrets.UtilityCode, p.enachConfig.PresentationFileConfig.UtilityName)
}

type getFileEntriesAndTxnAmountReq struct {
	workflowRequests      []*celestialPb.WorkflowRequest
	isLastActivityAttempt bool
}
type getFileEntriesAndTxnAmountRes struct {
	fileEntries        []string
	actionExecutionIds []string
	totalTxnAmount     *money.Money
}

// nolint: funlen
func (p *Processor) getPresentationFileEntriesAndTotalTxnAmount(ctx context.Context, req *getFileEntriesAndTxnAmountReq) (*getFileEntriesAndTxnAmountRes, error) {
	var (
		res = &getFileEntriesAndTxnAmountRes{
			totalTxnAmount: &money.Money{
				CurrencyCode: "INR",
				Units:        0,
			},
		}
		actionExecutionIds []string
	)

	// While initiation of enach workflow in celestial, the enach action id was used as
	// the client request id. Hence, here the list of clientRequestIds for the blocked
	// workflows is named as actionExecutionIds and is later used to fetch the enach action,
	actionExecutionIds = getWorkflowClientReqIdsFromWorkflowRequests(req.workflowRequests)
	enachMandateActions, err := p.enachMandateActionDao.GetByIds(ctx, actionExecutionIds)
	if err != nil {
		logger.Error(ctx, "unable to fetch enach mandate actions by ids", zap.Error(err))
		return nil, fmt.Errorf("unable to fetch enach mandate actions by ids, err: %w", err)
	}
	if len(actionExecutionIds) != len(enachMandateActions) && !req.isLastActivityAttempt {
		logger.Error(ctx, "unable to fetch all enach mandate actions by ids", zap.Error(err))
		return nil, fmt.Errorf("unable to all fetch enach mandate actions by ids, err: %w", err)
	}
	actionIdToEnachMandateActionMap := getActionIdToMandateActionMap(enachMandateActions)

	enachMandateIds := getEnachMandateIdsFromActions(enachMandateActions)
	enachMandates, err := p.enachMandateDao.GetByIds(ctx, enachMandateIds)
	if err != nil {
		logger.Error(ctx, "unable to fetch enach mandates by ids", zap.Error(err))
		return nil, fmt.Errorf("unable to fetch enach mandates  by ids, err: %w", err)
	}
	if len(enachMandateIds) != len(enachMandates) && !req.isLastActivityAttempt {
		logger.Error(ctx, "unable to fetch all enach mandates by ids", zap.Error(err))
		return nil, fmt.Errorf("unable to all fetch enach mandates by ids, err: %w", err)
	}
	mandateIdToMandateMap := getMandateIdToMandateMap(enachMandates)

	recurringPaymentIds := getRecurringPaymentIdsFromMandates(enachMandates)
	getRecurringPaymentsByIdsRes, err := p.recurringPaymentClient.GetRecurringPaymentsByIds(ctx, &rpPb.GetRecurringPaymentsByIdsRequest{
		Ids: recurringPaymentIds,
	})
	if err = epifigrpc.RPCError(getRecurringPaymentsByIdsRes, err); err != nil {
		logger.Error(ctx, "unable to fetch recurring payments by ids", zap.Error(err))
		return nil, fmt.Errorf("unable to fetch recurring payments by ids, err: %w", err)
	}
	if len(recurringPaymentIds) != len(getRecurringPaymentsByIdsRes.GetRecurringPayments()) && !req.isLastActivityAttempt {
		logger.Error(ctx, "unable to fetch all recurring payments by ids", zap.Error(err))
		return nil, fmt.Errorf("unable to all recurring payments by ids, err: %w", err)
	}
	recurringPaymentIdToRpMap := getRecurringPaymentIdToRecurringPaymentMap(getRecurringPaymentsByIdsRes.GetRecurringPayments())

	paymentInstrumentIds := getPiIdsFromRecurringPayments(getRecurringPaymentsByIdsRes.GetRecurringPayments())
	getPisByIdsRes, err := p.piClient.GetPIsByIds(ctx, &piPb.GetPIsByIdsRequest{
		Ids: paymentInstrumentIds,
	})
	if err = epifigrpc.RPCError(getPisByIdsRes, err); err != nil {
		logger.Error(ctx, "unable to fetch payment instruments by ids", zap.Error(err))
		return nil, fmt.Errorf("unable to fetch payment instruments by ids, err: %w", err)
	}
	if len(paymentInstrumentIds) != len(getPisByIdsRes.GetPaymentinstruments()) && !req.isLastActivityAttempt {
		logger.Error(ctx, "unable to fetch all payment instruments by ids", zap.Error(err))
		return nil, fmt.Errorf("unable to all payment instruments by ids, err: %w", err)
	}
	paymentInstrumentIdToPiMap := getPaymentInstrumentIdToPiMap(getPisByIdsRes.GetPaymentinstruments())

	for _, blockedWorkflowReq := range req.workflowRequests {
		var (
			fileEntryString    string
			tempTotalTxnAmount *money.Money
		)

		enachMandateAction, ok := actionIdToEnachMandateActionMap[blockedWorkflowReq.GetClientReqId().GetId()]
		if !ok {
			logger.Error(ctx, "Skipping entry!.. enach mandate action not found for client req id", zap.String(logger.CLIENT_REQUEST_ID, blockedWorkflowReq.GetClientReqId().GetId()))
			continue
		}

		enachMandate, ok := mandateIdToMandateMap[enachMandateAction.GetEnachMandateId()]
		if !ok {
			logger.Error(ctx, "Skipping entry!.. enach mandate not found for mandate id", zap.String(logger.MANDATE_ID, enachMandateAction.GetEnachMandateId()))
			continue
		}

		recurringPayment, ok := recurringPaymentIdToRpMap[enachMandate.GetRecurringPaymentId()]
		if !ok {
			logger.Error(ctx, "Skipping entry!.. recurring payment not found for rp id", zap.String(logger.RECURRING_PAYMENT_ID, enachMandate.GetRecurringPaymentId()))
			continue
		}

		fromPi, ok := paymentInstrumentIdToPiMap[recurringPayment.GetPiFrom()]
		if !ok {
			logger.Error(ctx, "Skipping entry!.. from payment instrument not found for pi id", zap.String(logger.PI_ID, recurringPayment.GetPiFrom()))
			continue
		}

		toPi, ok := paymentInstrumentIdToPiMap[recurringPayment.GetPiTo()]
		if !ok {
			logger.Error(ctx, "Skipping entry!.. to payment instrument not found for pi id", zap.String(logger.PI_ID, recurringPayment.GetPiTo()))
			continue
		}

		fileEntryString, err = p.getPresentationFileEntryString(&getPresentationFileEntryStringReq{
			fromPi:        fromPi,
			toPi:          toPi,
			mandate:       enachMandate,
			mandateAction: enachMandateAction,
		})
		if err != nil {
			logger.Error(ctx, "Skipping entry!.. error while fetching presentation file entry string", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, enachMandateAction.GetClientRequestId()), zap.Error(err))
			continue
		}

		// Not storing the result in totalTransactionAmount is a conscious call here.
		// If the sum fails, we do not want to corrupt the entire file up till now.
		// todo (Sidhant): Add kibana alerts on entry skips.
		tempTotalTxnAmount, err = moneyPkg.Sum(res.totalTxnAmount, enachMandateAction.GetActionMetadata().GetExecuteMetadata().GetAmount())
		if err != nil {
			logger.Error(ctx, "Skipping entry!.. error while summing total transaction amount", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, enachMandateAction.GetClientRequestId()), zap.Error(err))
			continue
		}

		res.totalTxnAmount = tempTotalTxnAmount
		res.fileEntries = append(res.fileEntries, fileEntryString)
		res.actionExecutionIds = append(res.actionExecutionIds, blockedWorkflowReq.GetClientReqId().GetId())
	}

	return res, nil
}

func getWorkflowClientReqIdsFromWorkflowRequests(workflowRequests []*celestialPb.WorkflowRequest) []string {
	var blockedWorkflowClientReqIds []string

	for _, workflowRequest := range workflowRequests {
		blockedWorkflowClientReqIds = append(blockedWorkflowClientReqIds, workflowRequest.GetClientReqId().GetId())
	}

	return blockedWorkflowClientReqIds
}
func getActionIdToMandateActionMap(actions []*enachPb.EnachMandateAction) map[string]*enachPb.EnachMandateAction {
	actionIdToMandateActionMap := make(map[string]*enachPb.EnachMandateAction)

	for _, action := range actions {
		actionIdToMandateActionMap[action.GetId()] = action
	}
	return actionIdToMandateActionMap
}
func getEnachMandateIdsFromActions(actions []*enachPb.EnachMandateAction) []string {
	var enachMandateIds []string

	for _, action := range actions {
		enachMandateIds = append(enachMandateIds, action.GetEnachMandateId())
	}

	// deduping since we can have 2 executions of the same enach mandate
	enachMandateIds = lo.Uniq(enachMandateIds)
	return enachMandateIds
}
func getMandateIdToMandateMap(mandates []*enachPb.EnachMandate) map[string]*enachPb.EnachMandate {
	mandateIdToMandateMap := make(map[string]*enachPb.EnachMandate)

	for _, mandate := range mandates {
		mandateIdToMandateMap[mandate.GetId()] = mandate
	}
	return mandateIdToMandateMap
}
func getRecurringPaymentIdsFromMandates(mandates []*enachPb.EnachMandate) []string {
	var recurringPaymentIds []string

	for _, mandate := range mandates {
		recurringPaymentIds = append(recurringPaymentIds, mandate.GetRecurringPaymentId())
	}

	return recurringPaymentIds
}
func getRecurringPaymentIdToRecurringPaymentMap(recurringPayments []*rpPb.RecurringPayment) map[string]*rpPb.RecurringPayment {
	recurringPaymentIdToRpMap := make(map[string]*rpPb.RecurringPayment)

	for _, recurringPayment := range recurringPayments {
		recurringPaymentIdToRpMap[recurringPayment.GetId()] = recurringPayment
	}
	return recurringPaymentIdToRpMap
}
func getPiIdsFromRecurringPayments(recurringPayments []*rpPb.RecurringPayment) []string {
	var paymentInstrumentIds []string

	for _, recurringPayment := range recurringPayments {
		paymentInstrumentIds = append(paymentInstrumentIds, recurringPayment.GetPiFrom(), recurringPayment.GetPiTo())
	}

	// deduping since multiple recurring payments may be set up from or to the same pi.
	paymentInstrumentIds = lo.Uniq(paymentInstrumentIds)
	return paymentInstrumentIds
}
func getPaymentInstrumentIdToPiMap(paymentInstruments []*piPb.PaymentInstrument) map[string]*piPb.PaymentInstrument {
	paymentInstrumentIdToPiMap := make(map[string]*piPb.PaymentInstrument)

	for _, paymentInstrument := range paymentInstruments {
		paymentInstrumentIdToPiMap[paymentInstrument.GetId()] = paymentInstrument
	}
	return paymentInstrumentIdToPiMap
}

type getPresentationFileHeaderStringReq struct {
	totalTxnAmount       *money.Money
	numEntries           int
	batchRefNumber       string
	settlementDateString string
}

// nolint: funlen
func (p *Processor) getPresentationFileHeaderString(req *getPresentationFileHeaderStringReq) (string, error) {
	const (
		emptyString = ""
	)

	transactionCode := p.enachConfig.PresentationFileConfig.HeaderTransactionCode
	if len(transactionCode) != 2 {
		return "", fmt.Errorf("invalid value for transaction code %s, err: %w", transactionCode, LengthExpectationsNotMatchedForField)
	}

	username := p.enachConfig.PresentationFileConfig.UtilityName
	if len(username) > 40 {
		return "", fmt.Errorf("invalid value for username %s, err: %w", username, LengthExpectationsNotMatchedForField)
	}

	transactionAmountInPaise, err := moneyPkg.ToPaise(req.totalTxnAmount)
	if err != nil {
		return "", fmt.Errorf("unable to get money amount in paise, err: %w", err)
	}
	transactionAmountInPaiseStr := strconv.FormatInt(transactionAmountInPaise, 10)
	if len(transactionAmountInPaiseStr) > 13 {
		return "", fmt.Errorf("invalid value for transactionAmountInPaiseStr %s, err: %w", transactionAmountInPaiseStr, LengthExpectationsNotMatchedForField)
	}

	utilityCode := p.enachSecrets.UtilityCode
	if len(utilityCode) > 18 {
		return "", fmt.Errorf("invalid value for utilityCode %s, err: %w", utilityCode, LengthExpectationsNotMatchedForField)
	}

	batchId := req.batchRefNumber
	if len(batchId) > 18 {
		return "", fmt.Errorf("invalid value for batchId %s, err: %w", batchId, LengthExpectationsNotMatchedForField)
	}

	epifiFederalPoolAccountNumber := p.enachConfig.PresentationFileConfig.EpifiFederalPoolAccountNumber
	if len(epifiFederalPoolAccountNumber) > 35 {
		return "", fmt.Errorf("invalid value for epifiPoolAccountNumber %s, err: %w", epifiFederalPoolAccountNumber, LengthExpectationsNotMatchedForField)
	}

	numEntries := req.numEntries
	numEntriesStr := strconv.Itoa(numEntries)
	if len(numEntriesStr) > 9 {
		return "", fmt.Errorf("invalid value for numEntriesStr %s, err: %w", numEntriesStr, LengthExpectationsNotMatchedForField)
	}

	fileHeaderString := fmt.Sprintf(enachPresentationHeaderTemplateString,
		transactionCode, emptyString, username, emptyString, emptyString, emptyString, emptyString, emptyString,
		emptyString, transactionAmountInPaise, req.settlementDateString, emptyString, emptyString, emptyString, utilityCode,
		batchId, emptyString, epifiFederalPoolAccountNumber, numEntries, emptyString, emptyString)

	if len(fileHeaderString) != presentationFileEntryExpectedLength {
		return "", fmt.Errorf("invalid value for fileHeaderString %s, err: %w", fileHeaderString, LengthExpectationsNotMatchedForField)
	}

	return fileHeaderString, nil
}

type getPresentationFileEntryStringReq struct {
	fromPi        *piPb.PaymentInstrument
	toPi          *piPb.PaymentInstrument
	mandate       *enachPb.EnachMandate
	mandateAction *enachPb.EnachMandateAction
}

// nolint: funlen
func (p *Processor) getPresentationFileEntryString(req *getPresentationFileEntryStringReq) (string, error) {
	const (
		emptyString = ""
	)

	transactionCode := p.enachConfig.PresentationFileConfig.EntryTransactionCode
	if len(transactionCode) != 2 {
		return "", fmt.Errorf("invalid lvalue for transaction code %s, err: %w", transactionCode, LengthExpectationsNotMatchedForField)
	}

	// todo(Sidhant): Handle non bank account types here. https://monorail.pointz.in/p/fi-app/issues/detail?id=61804
	destinationAccountType, ok := p.enachConfig.PresentationFileConfig.AccountTypeToEntryDestinationAccountType[req.toPi.GetAccount().GetAccountType().String()]
	if !ok {
		return "", fmt.Errorf("destinationAccountType code not defined for account type %s, %w", req.toPi.GetAccount().GetAccountType().String(), epifierrors.ErrPermanent)
	}
	if len(destinationAccountType) != 2 {
		return "", fmt.Errorf("invalid value for destinationAccountType %s, err: %w", destinationAccountType, LengthExpectationsNotMatchedForField)
	}

	accountHolderName := req.toPi.GetAccount().GetName()
	if len(accountHolderName) > 40 {
		return "", fmt.Errorf("invalid value for accountHolderName %s, err: %w", accountHolderName, LengthExpectationsNotMatchedForField)
	}

	utilityName := p.enachConfig.PresentationFileConfig.UtilityName
	if len(utilityName) > 20 {
		return "", fmt.Errorf("invalid value for utilityName %s, err: %w", utilityName, LengthExpectationsNotMatchedForField)
	}

	transactionAmountInPaise, err := moneyPkg.ToPaise(req.mandateAction.GetActionMetadata().GetExecuteMetadata().GetAmount())
	if err != nil {
		return "", fmt.Errorf("unable to get money amount in paise.err: %w", err)
	}
	transactionAmountInPaiseStr := strconv.FormatInt(transactionAmountInPaise, 10)
	if len(transactionAmountInPaiseStr) > 13 {
		return "", fmt.Errorf("invalid value for transactionAmountInPaiseStr %s, err: %w", transactionAmountInPaiseStr, LengthExpectationsNotMatchedForField)
	}

	destinationBankIfsc := req.toPi.GetAccount().GetIfscCode()
	if len(destinationBankIfsc) != 11 {
		return "", fmt.Errorf("invalid value for destinationBankIfsc %s, err: %w", destinationBankIfsc, LengthExpectationsNotMatchedForField)
	}

	payerBankAccNumber := req.fromPi.GetAccount().GetActualAccountNumber()
	if len(payerBankAccNumber) > 35 {
		return "", fmt.Errorf("invalid value for payeeBankAccNumber %s, err: %w", payerBankAccNumber, LengthExpectationsNotMatchedForField)
	}

	utilityCode := p.enachSecrets.UtilityCode
	if len(utilityCode) > 18 {
		return "", fmt.Errorf("invalid value for utilityCode %s, err: %w", utilityCode, LengthExpectationsNotMatchedForField)
	}

	transactionRefNumber := req.mandateAction.GetVendorRequestId()
	if len(transactionRefNumber) > 30 {
		return "", fmt.Errorf("invalid value for transactionRefNumber %s, err: %w", transactionRefNumber, LengthExpectationsNotMatchedForField)
	}

	productType := p.enachConfig.PresentationFileConfig.EntryProductType
	if len(productType) > 3 {
		return "", fmt.Errorf("invalid value for productType %s, err: %w", productType, LengthExpectationsNotMatchedForField)
	}

	umrn := req.mandate.GetUmrn()
	if len(umrn) > 20 {
		return "", fmt.Errorf("invalid value for umrn %s, err: %w", umrn, LengthExpectationsNotMatchedForField)
	}

	fileEntryString := fmt.Sprintf(enachPresentationEntryTemplateString,
		transactionCode, emptyString, destinationAccountType, emptyString, emptyString, accountHolderName, emptyString,
		emptyString, utilityName, emptyString, transactionAmountInPaise, emptyString, emptyString, emptyString,
		emptyString, destinationBankIfsc, payerBankAccNumber, emptyString, utilityCode, transactionRefNumber,
		productType, emptyString, umrn, emptyString)

	if len(fileEntryString) != presentationFileEntryExpectedLength {
		return "", fmt.Errorf("invalid value for fileEntryString %s, err: %w", fileEntryString, LengthExpectationsNotMatchedForField)
	}

	return fileEntryString, nil
}

type uploadPresentationRecordFileToS3Req struct {
	fileBytes            []byte
	fileName             string
	settlementDateString string
}

func (p *Processor) uploadPresentationRecordFileToS3(ctx context.Context, req *uploadPresentationRecordFileToS3Req) (string, error) {
	// within the allotted destination folder, we are creating a folder name with settlement date to store the files
	// This is done to keep the files in belonging to the same date together
	s3FilePath := fmt.Sprintf("%s/%s/%s", p.enachConfig.FileRecordsBucket.PresentationFilesS3Directory, req.settlementDateString, req.fileName)

	if err := p.enachRecordsS3Client.Write(ctx, s3FilePath, req.fileBytes, string(awsS3.ObjectCannedACLBucketOwnerFullControl)); err != nil {
		return "", err
	}
	return s3FilePath, nil
}

// todo(Sidhant): Figure out how to have this implemented in enach vendor.
func (p *Processor) generateMandateExecutionBatchRequestId(_ context.Context, vendor commonvgpb.Vendor) (string, error) {
	switch vendor {
	case commonvgpb.Vendor_FEDERAL_BANK:
		// request id needs to 18 chars long alphanumeric string
		return fmt.Sprintf("FI%s", idgen.RandAlphaNumericString(16)), nil
	default:
		return "", fmt.Errorf("method not implemented for vendor type: %s %w", vendor.String(), epifierrors.ErrInvalidArgument)
	}
}
