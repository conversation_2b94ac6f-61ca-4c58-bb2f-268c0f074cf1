package activity

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	activityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

func (p *Processor) UpdateVendorBatchRequestIdInMandateAction(ctx context.Context, req *activityPb.UpdateVendorBatchRequestIdInMandateActionRequest) (*activityPb.UpdateVendorBatchRequestIdInMandateActionResponse, error) {
	var (
		res = &activityPb.UpdateVendorBatchRequestIdInMandateActionResponse{}
	)

	enachMandateAction, err := p.enachMandateActionDao.GetById(ctx, req.GetEnachMandateActionId())
	if err != nil {
		logger.Error(ctx, "error while fetching enach mandate action by id", zap.Error(err), zap.String(logger.ID, req.GetEnachMandateActionId()))
		return nil, fmt.Errorf("error while fetching enach mandate action by id, err: %s, %w", err, epifierrors.ErrTransient)
	}

	// Checking if the enach mandate action is already in updated state.
	// This is required for idempotency of activity
	if enachMandateAction.GetVendorBatchRequestId() == req.GetVendorBatchRequestId() {
		return res, nil
	}
	if enachMandateAction.GetVendorBatchRequestId() != "" {
		return nil, fmt.Errorf("enach mandate action already populated with unexpected value of batch execution id, existing value: %s, new value: %s, %w",
			enachMandateAction.GetVendorBatchRequestId(), req.GetVendorBatchRequestId(), epifierrors.ErrPermanent)
	}

	// updateMask contains the fields (column's) to be updated.
	updateMask := []enachEnumsPb.EnachMandateActionFieldMask{
		enachEnumsPb.EnachMandateActionFieldMask_ENACH_MANDATE_ACTION_FIELD_MASK_ACTION_VENDOR_BATCH_REQUEST_ID,
	}

	// the query uses these fields (column's) to update the existing fields (column's)
	// consider it as the superposition of new fields (column's) over the old fields (column's) with the help of field mask.
	enachMandateAction.VendorBatchRequestId = req.GetVendorBatchRequestId()

	err = p.enachMandateActionDao.Update(ctx, updateMask, enachMandateAction)
	if err != nil {
		logger.Error(ctx, "error while updating mandate action", zap.Error(err), zap.String(logger.MANDATE_ACION, enachMandateAction.String()))
		return nil, fmt.Errorf("error while updating mandate action, err: %s, %w", err, epifierrors.ErrTransient)
	}

	return res, nil
}
