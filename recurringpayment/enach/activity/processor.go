package activity

import (
	"encoding/json"
	"fmt"

	celestialPb "github.com/epifi/be-common/api/celestial"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	vgEnachPb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/recurringpayment/config/commons"
	rpWorkerConfig "github.com/epifi/gamma/recurringpayment/config/worker"
	"github.com/epifi/gamma/recurringpayment/enach/dao"
)

type Processor struct {
	clock                  datetime.Time
	enachMandateDao        dao.EnachMandateDao
	enachMandateActionDao  dao.EnachMandateActionDao
	celestialClient        celestialPb.CelestialClient
	piClient               piPb.PiClient
	recurringPaymentClient rpPb.RecurringPaymentServiceClient
	enachConfig            *rpWorkerConfig.EnachConfig
	enachRecordsS3Client   s3.S3Client
	enachSecrets           *commons.EnachSecrets
	vgEnachClient          vgEnachPb.EnachClient
}

func NewProcessor(
	clock datetime.Time,
	conf *rpWorkerConfig.Config,
	enachMandateDao dao.EnachMandateDao,
	enachMandateActionDao dao.EnachMandateActionDao,
	celestialClient celestialPb.CelestialClient,
	piClient piPb.PiClient,
	recurringPaymentClient rpPb.RecurringPaymentServiceClient,
	enachRecordsS3Client s3.S3Client,
	vgEnachClient vgEnachPb.EnachClient,
) (*Processor, error) {

	// unmarshall enach secret json to struct
	enachSecretsJson := conf.Secrets.Ids[commons.EnachSecretsKeyName]
	var secrets commons.EnachSecrets
	if err := json.Unmarshal([]byte(enachSecretsJson), &secrets); err != nil {
		logger.ErrorNoCtx("error unmarshalling enach secrets json")
		return nil, fmt.Errorf("error unmarshalling enach secrets json, err: %w", err)
	}

	return &Processor{
		clock:                  clock,
		enachMandateDao:        enachMandateDao,
		enachMandateActionDao:  enachMandateActionDao,
		celestialClient:        celestialClient,
		piClient:               piClient,
		recurringPaymentClient: recurringPaymentClient,
		enachConfig:            conf.EnachConfig,
		enachRecordsS3Client:   enachRecordsS3Client,
		enachSecrets:           &secrets,
		vgEnachClient:          vgEnachClient,
	}, nil
}
