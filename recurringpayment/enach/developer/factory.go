package developer

import (
	"fmt"

	"github.com/epifi/gamma/api/recurringpayment/enach/developer"
	"github.com/epifi/gamma/recurringpayment/enach/developer/processor"
)

type DevFactory struct {
	enachMandateProcessor       *processor.EnachMandateProcessor
	enachMandateActionProcessor *processor.EnachMandateActionProcessor
}

func NewDevFactory(enachMandateProcessor *processor.EnachMandateProcessor, enachMandateActionProcessor *processor.EnachMandateActionProcessor) *DevFactory {
	return &DevFactory{
		enachMandateProcessor:       enachMandateProcessor,
		enachMandateActionProcessor: enachMandateActionProcessor,
	}
}

func (d *DevFactory) getParameterListProcessor(entity developer.EnachEntity) (ParameterFetcher, error) {
	switch entity {
	case developer.EnachEntity_ENACH_MANDATE:
		return d.enachMandateProcessor, nil
	case developer.EnachEntity_ENACH_MANDATE_ACTION:
		return d.enachMandateActionProcessor, nil
	}
	return nil, fmt.Errorf("no valid implementation found")
}

func (d *DevFactory) getDataProcessor(entity developer.EnachEntity) (DataFetcher, error) {
	switch entity {
	case developer.EnachEntity_ENACH_MANDATE:
		return d.enachMandateProcessor, nil
	case developer.EnachEntity_ENACH_MANDATE_ACTION:
		return d.enachMandateActionProcessor, nil
	}
	return nil, fmt.Errorf("no valid implementation found")
}
