package developer

import (
	"context"
	"errors"

	"go.uber.org/zap"
	gormV2 "gorm.io/gorm"

	rpcPb "github.com/epifi/be-common/api/rpc"
	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/recurringpayment/enach/developer"
	"github.com/epifi/be-common/pkg/logger"
)

type EnachDevService struct {
	fac *DevFactory
}

func NewEnachDevService(fac *DevFactory) *EnachDevService {
	return &EnachDevService{fac: fac}
}

func (e *EnachDevService) GetEntityList(ctx context.Context, req *cxDsPb.GetEntityListRequest) (*cxDsPb.GetEntityListResponse, error) {
	return &cxDsPb.GetEntityListResponse{
		Status:     rpcPb.StatusOk(),
		EntityList: []string{developer.EnachEntity_ENACH_MANDATE.String(), developer.EnachEntity_ENACH_MANDATE_ACTION.String()},
	}, nil
}

func (e *EnachDevService) GetParameterList(ctx context.Context, req *cxDsPb.GetParameterListRequest) (*cxDsPb.GetParameterListResponse, error) {

	ent, ok := developer.EnachEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetParameterListResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available"),
		}, nil
	}
	paramFetcher, err := e.fac.getParameterListProcessor(developer.EnachEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available", zap.Error(err))
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	paramList, err := paramFetcher.FetchParamList(ctx, developer.EnachEntity(ent))
	if err != nil {
		logger.Error(ctx, "unable to fetch parameter list", zap.Error(err))
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxDsPb.GetParameterListResponse{
		Status:        rpcPb.StatusOk(),
		ParameterList: paramList,
	}, nil
}

func (e *EnachDevService) GetData(ctx context.Context, req *cxDsPb.GetDataRequest) (*cxDsPb.GetDataResponse, error) {
	ent, ok := developer.EnachEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetDataResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available"),
		}, nil
	}
	dataFetcher, err := e.fac.getDataProcessor(developer.EnachEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available", zap.Error(err))
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	jsonResp, err := dataFetcher.FetchData(ctx, developer.EnachEntity(ent), req.GetFilters())
	if err != nil {
		logger.Error(ctx, "unable to get data", zap.Error(err))
		if errors.Is(err, gormV2.ErrRecordNotFound) {
			return &cxDsPb.GetDataResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxDsPb.GetDataResponse{
		Status:       rpcPb.StatusOk(),
		JsonResponse: jsonResp,
	}, nil
}
