package processor

import (
	"context"
	"encoding/json"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	"github.com/epifi/gamma/api/recurringpayment/enach/developer"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/recurringpayment/enach/dao"
)

type EnachMandateActionProcessor struct {
	enachMandateActionDao dao.EnachMandateActionDao
}

func NewEnachMandateActionProcessor(enachMandateActionDao dao.EnachMandateActionDao) *EnachMandateActionProcessor {
	return &EnachMandateActionProcessor{
		enachMandateActionDao: enachMandateActionDao,
	}
}

func (d *EnachMandateActionProcessor) FetchParamList(ctx context.Context, entity developer.EnachEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            id,
			Label:           "Enach Mandate Action Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            clientRequestId,
			Label:           "Client Request Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            enachMandateId,
			Label:           "Enach Mandate Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            vendorRequestId,
			Label:           "Vendor Request Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            actionType,
			Label:           "Mandate Action Type",
			Type:            db_state.ParameterDataType_DROPDOWN,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
			Options:         getPossibleEnachMandateActionTypes(),
		},
		{
			Name:            actionStatus,
			Label:           "Mandate Action Status",
			Type:            db_state.ParameterDataType_DROPDOWN,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
			Options:         getPossibleEnachMandateActionStatuses(),
		},
	}
	return paramList, nil
}

// nolint:funlen
func (d *EnachMandateActionProcessor) FetchData(ctx context.Context, entity developer.EnachEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", ErrNilFilter
	}

	var (
		rpAId                    string
		vendorReqId              string
		enachManId               string
		enachMandateActionType   string
		enachMandateActionStatus string
		clientReqId              string
	)
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case id:
			rpAId = filter.GetStringValue()
		case vendorRequestId:
			vendorReqId = filter.GetStringValue()
		case enachMandateId:
			enachManId = filter.GetStringValue()
		case clientRequestId:
			clientReqId = filter.GetStringValue()
		case actionType:
			enachMandateActionType = filter.GetDropdownValue()
		case actionStatus:
			enachMandateActionStatus = filter.GetDropdownValue()
		default:
			return "", fmt.Errorf("unknown parameter name %s", filter.GetParameterName())
		}
	}

	var (
		enachMandateAction  *enachPb.EnachMandateAction
		enachMandateActions []*enachPb.EnachMandateAction
		data                []byte
		err                 error
	)

	switch {
	case rpAId != "":
		enachMandateAction, err = d.enachMandateActionDao.GetById(ctx, rpAId)
		if err != nil {
			logger.Error(ctx, "error while fetching enach mandate action details by mandate action id filter", zap.String(logger.ID, id), zap.Error(err))
			return "", err
		}
		data, err = json.Marshal(enachMandateAction)
	case clientReqId != "":
		enachMandateAction, err = d.enachMandateActionDao.GetByClientRequestId(ctx, clientReqId)
		if err != nil {
			logger.Error(ctx, "error while fetching enach mandate action details by recurring payment action id filter", zap.String(logger.RECURRING_PAYMENT_ACTION_ID, clientReqId), zap.Error(err))
			return "", err
		}
		data, err = json.Marshal(enachMandateAction)
	case vendorReqId != "":
		var actionTypeEnum enachEnumsPb.EnachActionType
		if enachMandateActionType != "" && enachMandateActionType != enachEnumsPb.EnachActionType_ENACH_ACTION_TYPE_UNSPECIFIED.String() {
			actionTypeEnum = enachEnumsPb.EnachActionType(enachEnumsPb.EnachActionType_value[enachMandateActionType])
		}
		enachMandateAction, err = d.enachMandateActionDao.GetByActionTypeAndVendorRequestId(ctx, actionTypeEnum, vendorReqId)
		if err != nil {
			logger.Error(ctx, "error while fetching enach mandate action details by vendor request id & action type filters", zap.String(logger.VENDOR_ID, vendorReqId), zap.Error(err))
			return "", err
		}
		data, err = json.Marshal(enachMandateAction)
	case enachManId != "":
		var (
			actionTypeEnum   enachEnumsPb.EnachActionType
			actionStatusEnum enachEnumsPb.EnachActionStatus
		)
		var queryFilters []dao.FilterOption
		if enachMandateActionType != "" && enachMandateActionType != enachEnumsPb.EnachActionType_ENACH_ACTION_TYPE_UNSPECIFIED.String() {
			actionTypeEnum = enachEnumsPb.EnachActionType(enachEnumsPb.EnachActionType_value[enachMandateActionType])
			queryFilters = append(queryFilters, dao.WithActionType(actionTypeEnum))
		}
		if enachMandateActionStatus != "" && enachMandateActionStatus != enachEnumsPb.EnachActionStatus_ENACH_ACTION_STATUS_UNSPECIFIED.String() {
			actionStatusEnum = enachEnumsPb.EnachActionStatus(enachEnumsPb.EnachActionStatus_value[enachMandateActionStatus])
			queryFilters = append(queryFilters, dao.WithActionStatus(actionStatusEnum))
		}
		enachMandateActions, err = d.enachMandateActionDao.GetByEnachMandateId(ctx, enachManId, queryFilters...)
		if err != nil {
			logger.Error(ctx, "error while fetching enach mandate action details by enach mandates id with filters...", zap.String(logger.ID, enachManId), zap.Error(err))
			return "", err
		}
		data, err = json.Marshal(enachMandateActions)
	}

	if err != nil {
		return "", fmt.Errorf("cannot marshal enach mandate actions struct to json: %w", err)
	}
	return string(data), nil
}

func getPossibleEnachMandateActionTypes() []string {
	var actionsTypes []string
	for _, action := range enachEnumsPb.EnachActionType_name {
		if action != enachEnumsPb.EnachActionType_ENACH_ACTION_TYPE_UNSPECIFIED.String() {
			actionsTypes = append(actionsTypes, action)
		}
	}
	return actionsTypes
}

func getPossibleEnachMandateActionStatuses() []string {
	var actionStatuses []string
	for _, status := range enachEnumsPb.EnachActionStatus_name {
		if status != enachEnumsPb.EnachActionStatus_ENACH_ACTION_STATUS_UNSPECIFIED.String() {
			actionStatuses = append(actionStatuses, status)
		}
	}
	return actionStatuses
}
