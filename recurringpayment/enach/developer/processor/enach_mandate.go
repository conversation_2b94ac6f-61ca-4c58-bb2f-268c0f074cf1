package processor

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	"github.com/epifi/gamma/api/recurringpayment/enach/developer"
	"github.com/epifi/gamma/recurringpayment/enach/dao"
)

type EnachMandateProcessor struct {
	enachMandateDao dao.EnachMandateDao
}

func NewEnachMandateProcessor(enachMandateDao dao.EnachMandateDao) *EnachMandateProcessor {
	return &EnachMandateProcessor{
		enachMandateDao: enachMandateDao,
	}
}

func (d *EnachMandateProcessor) FetchParamList(ctx context.Context, entity developer.EnachEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            id,
			Label:           "Enach Mandate Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            recurringPaymentId,
			Label:           "Recurring Payment Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            umrn,
			Label:           "Umrn",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            ownership,
			Label:           "Ownership",
			Type:            db_state.ParameterDataType_DROPDOWN,
			Options:         getOwnershipList(),
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
	}
	return paramList, nil
}

// nolint:funlen
func (d *EnachMandateProcessor) FetchData(ctx context.Context, entity developer.EnachEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", ErrNilFilter
	}
	var (
		mandateId       string
		recPaymentId    string
		enachManateUmrn string
		inputOwnership  commontypes.Ownership
	)
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case id:
			mandateId = filter.GetStringValue()
		case recurringPaymentId:
			recPaymentId = filter.GetStringValue()
		case umrn:
			enachManateUmrn = filter.GetStringValue()
		case ownership:
			inputOwnership = commontypes.Ownership(commontypes.Ownership_value[filter.GetDropdownValue()])
		default:
			return "", fmt.Errorf("unknown parameter name %s", filter.GetParameterName())
		}
	}

	var (
		data         []byte
		enachMandate *enachPb.EnachMandate
		err          error
	)

	ctx = epificontext.WithOwnership(ctx, inputOwnership)

	switch {
	case mandateId != "":
		enachMandate, err = d.enachMandateDao.GetById(ctx, mandateId)
		if err != nil {
			logger.Error(ctx, "error while fetching the enach mandates details by enach mandate id", zap.String(logger.ID, mandateId), zap.Error(err))
			return "", fmt.Errorf("error while fetching the enach mandates details by enach mandate id %s", err)
		}
	case recPaymentId != "":
		enachMandate, err = d.enachMandateDao.GetByRecurringPaymentId(ctx, recPaymentId, false)
		if err != nil {
			logger.Error(ctx, "error while fetching the enach mandate details for given recurring payment id", zap.String(logger.RECURRING_PAYMENT_ID, recPaymentId), zap.Error(err))
			return "", fmt.Errorf("error while fetching the enach mandate details for given recurring payment id: %s", err)
		}
	case enachManateUmrn != "":
		enachMandate, err = d.enachMandateDao.GetByUmrn(ctx, enachManateUmrn, false)
		if err != nil {
			logger.Error(ctx, "error while fetching the enach mandate details for given umrn", zap.String(logger.UMRN, enachManateUmrn), zap.Error(err))
			return "", fmt.Errorf("error while fetching the enach mandate details for given umrn %s", err)
		}
	}
	data, err = protojson.Marshal(enachMandate)
	if err != nil {
		logger.Info(ctx, "cannot marshal enach mandate struct to json", zap.Error(err), zap.String(logger.ID, enachMandate.GetId()))
		return "", fmt.Errorf("cannot marshal enach mandate struct to json: %w", err)
	}
	return string(data), nil
}

func getOwnershipList() []string {
	ownershipList := make([]string, 0)
	for _, ownership := range commontypes.Ownership_name {
		ownershipList = append(ownershipList, ownership)
	}
	return ownershipList
}
