package recurringpayment

import (
	"context"
	"errors"
	"fmt"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/golang/protobuf/ptypes"
	"github.com/google/uuid"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	beOrderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/domain"
	bePaymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/paymentinstrument"
	pb "github.com/epifi/gamma/api/recurringpayment"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	upiPb "github.com/epifi/gamma/api/upi"
	mandatePb "github.com/epifi/gamma/api/upi/mandate"
	"github.com/epifi/gamma/api/vendors"
	"github.com/epifi/gamma/frontend/pay/transaction"
	"github.com/epifi/gamma/pkg/pay"
	"github.com/epifi/gamma/recurringpayment/dao"
)

// ProcessRecurringPaymentCreation checks the status of the recurring payment creation
// This RPC is called by central order orchestrator
// The RPC is responsible for checking the status of the recurring payment creation for the domain services.
// If recurring payment creation is successful then DomainProcessingStatus SUCCESS is returned and if txn failed then DomainProcessingStatus
// PERMANENT_FAILURE is returned.
// If recurring payment creation is in progress state then status is checked with the domain services and returned accordingly
// nolint: funlen
func (s *Service) ProcessRecurringPaymentCreation(ctx context.Context, req *domain.ProcessFulfilmentRequest) (*domain.ProcessFulfilmentResponse, error) {
	var (
		actionStateToUpdate pb.ActionState
		stateToUpdate       pb.RecurringPaymentState
	)
	res := &domain.ProcessFulfilmentResponse{}
	responseHeader := &domain.DomainResponseHeader{}
	res.ResponseHeader = responseHeader
	recurringPaymentCreationInfo := pb.RecurringPaymentCreationInfo{}
	unmarshalOptions := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	if unmarshalErr := unmarshalOptions.Unmarshal(req.GetPayload(), &recurringPaymentCreationInfo); unmarshalErr != nil {
		logger.Error(ctx, "failed to unmarshal payload for recurring payment creation", zap.Error(unmarshalErr))
		responseHeader.Status = domain.DomainProcessingStatus_PERMANENT_FAILURE
		return res, nil
	}
	recurringPaymentId := recurringPaymentCreationInfo.GetRecurringPaymentId()
	clientRequestId := recurringPaymentCreationInfo.GetClientRequestId()
	requestId := recurringPaymentCreationInfo.GetReqId()
	recurringPayment, err := s.recurringPaymentDao.GetById(ctx, recurringPaymentId)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "recurring payment record not found", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
		responseHeader.Status = domain.DomainProcessingStatus_PERMANENT_FAILURE
		return res, nil
	case err != nil:
		logger.Error(ctx, "error while fetching recurring payment", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId),
			zap.Error(err))
		responseHeader.Status = domain.DomainProcessingStatus_TRANSIENT_FAILURE
		return res, nil
	default:
		logger.Debug(ctx, "recurring payment entry fetched successfully")
	}
	switch recurringPayment.GetState() {
	// recurring payment created successfully
	case pb.RecurringPaymentState_ACTIVATED, pb.RecurringPaymentState_MODIFY_QUEUED, pb.RecurringPaymentState_MODIFY_INITIATED,
		pb.RecurringPaymentState_MODIFY_AUTHORISED, pb.RecurringPaymentState_PAUSE_QUEUED, pb.RecurringPaymentState_PAUSE_INITIATED,
		pb.RecurringPaymentState_PAUSE_AUTHORISED, pb.RecurringPaymentState_PAUSED, pb.RecurringPaymentState_REVOKE_QUEUED,
		pb.RecurringPaymentState_REVOKE_INITIATED, pb.RecurringPaymentState_REVOKE_AUTHORISED, pb.RecurringPaymentState_REVOKED,
		pb.RecurringPaymentState_EXPIRED:
		// once a mandate is created, we auto trigger execution for some cases
		// for more details check function documentation
		err = s.checkAndCreateOneTimeMandateExecutionOrder(ctx, recurringPayment)
		if err != nil {
			logger.Error(ctx, "error while checking and creating mandate execution order",
				zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
			responseHeader.Status = domain.GetStatusFrom(err)
		} else {
			logger.Info(ctx, "recurring payment created successfully", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
			responseHeader.Status = domain.DomainProcessingStatus_SUCCESS
		}
	// recurring payment creation failed
	case pb.RecurringPaymentState_FAILED:
		logger.Info(ctx, "recurring payment creation failed", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
		responseHeader.Status = domain.DomainProcessingStatus_PERMANENT_FAILURE
	// recurring payment creation entry created
	// pending to be initiated with vendor after user authorisation
	case pb.RecurringPaymentState_CREATION_QUEUED, pb.RecurringPaymentState_CREATION_INITIATED:
		logger.Info(ctx, "recurring payment authorisation pending", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
		responseHeader.Status = domain.DomainProcessingStatus_NO_OP
	// recurring payment creation initiated with vendor
	// we can check status with the domain services for current state
	case pb.RecurringPaymentState_CREATION_AUTHORISED:
		logger.Info(ctx, "checking recurring payment creation status", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
		status := s.checkCreationStatusWithDomainService(ctx, recurringPayment, requestId)
		switch status {
		case domain.DomainProcessingStatus_SUCCESS:
			stateToUpdate = pb.RecurringPaymentState_ACTIVATED
			actionStateToUpdate = pb.ActionState_ACTION_SUCCESS
		case domain.DomainProcessingStatus_PERMANENT_FAILURE:
			stateToUpdate = pb.RecurringPaymentState_FAILED
			actionStateToUpdate = pb.ActionState_ACTION_FAILURE
		default:
			responseHeader.Status = status
			return res, nil
		}
		// updating recurring payment parameters in case of success/ permanent failure
		recurringPaymentAction, dbErr := s.recurringPaymentActionsDao.GetByClientRequestId(ctx, clientRequestId, false)
		switch {
		case errors.Is(dbErr, epifierrors.ErrRecordNotFound):
			logger.Error(ctx, "record not found for recurring payment action", zap.String(logger.CLIENT_REQUEST_ID,
				clientRequestId), zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
			responseHeader.Status = domain.DomainProcessingStatus_PERMANENT_FAILURE
			return res, nil
		case dbErr != nil:
			logger.Error(ctx, "error in fetching create action", zap.String(logger.RECURRING_PAYMENT_ID,
				recurringPaymentId), zap.Error(dbErr), zap.String(logger.CLIENT_REQUEST_ID, clientRequestId))
			responseHeader.Status = domain.DomainProcessingStatus_TRANSIENT_FAILURE
			return res, nil
		default:
			logger.Debug(ctx, "action fetched successfully", zap.String(logger.RECURRING_PAYMENT_ID,
				recurringPaymentId), zap.String(logger.CLIENT_REQUEST_ID, clientRequestId))
		}

		err = s.updateRecurringPaymentAndActionInTxnBlock(ctx, recurringPaymentAction, recurringPayment,
			nil, nil, stateToUpdate, actionStateToUpdate)
		if err != nil {
			logger.Error(ctx, "error in updating recurring payment and action state",
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId), zap.Error(err))
			responseHeader.Status = domain.GetStatusFrom(err)
			return res, nil
		} else if stateToUpdate == pb.RecurringPaymentState_ACTIVATED {
			err = s.checkAndCreateOneTimeMandateExecutionOrder(ctx, recurringPayment)
			if err != nil {
				logger.Error(ctx, "error checking and creating mandate execution order", zap.Error(err))
				responseHeader.Status = domain.GetStatusFrom(err)
				return res, nil
			}

		}
		responseHeader.Status = status
	default:
		logger.Error(ctx, "invalid recurring payment state", zap.String(logger.STATE, recurringPayment.GetState().String()),
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
		responseHeader.Status = domain.DomainProcessingStatus_TRANSIENT_FAILURE
	}
	return res, nil
}

// checkCreationStatusWithDomainService checks the status of recurring payment creation with domain services
// In case of standing instruction state request will never be in INITIATED state
func (s *Service) checkCreationStatusWithDomainService(ctx context.Context, recurringPayment *pb.RecurringPayment, reqId string) domain.DomainProcessingStatus {
	switch recurringPayment.GetType() {
	case pb.RecurringPaymentType_STANDING_INSTRUCTION:
		statusRes, err := s.siClient.GetActionStatus(ctx, &siPb.GetActionStatusRequest{
			RecurringPaymentId: recurringPayment.GetId(),
			RequestId:          reqId,
			Action:             siPb.RequestType_CREATE,
		})
		switch {
		case err != nil:
			logger.Error(ctx, "error in fetching create status from SI", zap.Error(err), zap.String(logger.REQUEST_ID, reqId))
			return domain.DomainProcessingStatus_TRANSIENT_FAILURE
		case !statusRes.GetStatus().IsSuccess():
			logger.Error(ctx, "non success status in fetching create status from SI", zap.Error(err), zap.String(logger.REQUEST_ID, reqId))
			return domain.DomainProcessingStatus_TRANSIENT_FAILURE
		default:
			switch statusRes.GetState() {
			case siPb.State_SUCCESS:
				return domain.DomainProcessingStatus_SUCCESS
			case siPb.State_FAILED:
				return domain.DomainProcessingStatus_PERMANENT_FAILURE
			default:
				return domain.DomainProcessingStatus_TRANSIENT_FAILURE
			}
		}
	case pb.RecurringPaymentType_UPI_MANDATES:
		err := s.checkMandateRequestStatus(ctx, reqId, recurringPayment)
		if err != nil {
			return domain.GetStatusFrom(err)
		}
		return domain.DomainProcessingStatus_SUCCESS
	default:
		return domain.DomainProcessingStatus_PERMANENT_FAILURE
	}
}

// checkAndCreateOneTimeMandateExecutionOrder checks if mandate execution order needs to be created for the current recurring payment
// and creates the order if not already created
// creates  a mandate if all the following condition matches
// 1) recurring payment is of type mandates
// 2) recurrence is one time
// 3) share to payee flag is true
// 3) payee is internal
// 4) execution action is not present in progress/success state
// nolint:funlen
func (s *Service) checkAndCreateOneTimeMandateExecutionOrder(ctx context.Context, recurringPayment *pb.RecurringPayment) error {
	if recurringPayment.GetType() != pb.RecurringPaymentType_UPI_MANDATES || !recurringPayment.GetShareToPayee() ||
		recurringPayment.GetRecurrenceRule().GetAllowedFrequency() != pb.AllowedFrequency_ONE_TIME {
		return nil
	}
	isPayeeInternal, err := s.isActorInternal(ctx, recurringPayment.GetFromActorId())
	if err != nil {
		return fmt.Errorf("error checking if payee is internal :%v :%w", err, epifierrors.ErrTransient)
	}
	if !isPayeeInternal {
		return nil
	}

	_, err = s.recurringPaymentActionsDao.GetByRecurringPaymentId(ctx, recurringPayment.GetId(),
		dao.WithActionsFilter([]pb.Action{pb.Action_EXECUTE}),
		dao.WithActionStateFilter([]pb.ActionState{pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_SUCCESS}))
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return fmt.Errorf("error fetching action by recurring payment id :%s :%v %w",
			recurringPayment.GetId(), err, epifierrors.ErrTransient)
	} else if err == nil {
		logger.Info(ctx, "execution action already present for recurring payment",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		return nil
	}

	paymentDetails, err := getPaymentDetails(
		recurringPayment.GetPiFrom(),
		recurringPayment.GetPiTo(),
		bePaymentPb.PaymentProtocol_UPI,
		recurringPayment.GetAmount())
	clientReqId := uuid.New().String()
	if err != nil {
		return fmt.Errorf("error fetching payment details :%v %w", err, epifierrors.ErrTransient)
	}

	mandateRes, err := s.upiMandateClient.GetMandate(ctx, &mandatePb.GetMandateRequest{
		Identifier: &mandatePb.GetMandateRequest_RecurringPaymentId{
			RecurringPaymentId: recurringPayment.GetId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(mandateRes, err); rpcErr != nil {
		return fmt.Errorf("error fetching mandate for recurrig payment id :%w", rpcErr)
	}

	payerVpa := mandateRes.GetMandate().GetUmn()
	payeeVpa, err := s.getVpaForPiId(ctx, recurringPayment.GetPiTo())
	if err != nil {
		return fmt.Errorf("error fetching payee vpa for pi: %w", err)
	}

	res, err := s.ExecuteRecurringPayment(ctx, &pb.ExecuteRecurringPaymentRequest{
		RecurringPaymentId: recurringPayment.GetId(),
		Amount:             recurringPayment.GetAmount(),
		ClientRequestId:    clientReqId,
		OrderStatus:        beOrderPb.OrderStatus_IN_PAYMENT,
		ExecutionPayload: &pb.ExecuteRecurringPaymentRequest_UpiMandateExecutionInfo{
			UpiMandateExecutionInfo: &pb.UpiMandateExecuteInfo{
				PaymentRequestInfo: &bePaymentPb.PaymentRequestInformation{
					ReqId: paymentDetails.GetReqId(),
					UpiInfo: &bePaymentPb.PaymentRequestInformation_UPI{
						MerchantRefId:      paymentDetails.GetMerchantRefId(),
						RefUrl:             paymentDetails.GetReferenceUrl(),
						TxnOriginTimestamp: ptypes.TimestampNow(),
						InitiationMode:     paymentDetails.GetInitiationMode(),
						Purpose:            paymentDetails.GetPurpose(),
						Mcc:                paymentDetails.GetMcc(),
						OrgId:              paymentDetails.GetOrgId(),
						MerchantDetails: &upiPb.MerchantDetails{
							MerchantId:         paymentDetails.GetMerchantId(),
							MerchantStoreId:    paymentDetails.GetMerchantStoreId(),
							MerchantTerminalId: paymentDetails.GetMerchantTerminalId(),
						},
						PayerVpa:        payerVpa,
						PayeeVpa:        payeeVpa,
						TransactionType: upiPb.TransactionType_COLLECT,
					},
				},
				SeqNum: 1,
			},
		},
	})

	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		if res.GetStatus().IsAlreadyExists() {
			logger.Info(ctx, "order already exists")
			return nil
		}
		return fmt.Errorf("error executing recurring payment :%v %w", rpcErr, epifierrors.ErrTransient)
	}
	return nil
}

// getPaymentDetails creates and returns payment details required for order creation
func getPaymentDetails(
	piFromId, piToId string,
	protocol bePaymentPb.PaymentProtocol,
	amount *money.Money,
) (*beOrderPb.PaymentDetails, error) {
	var (
		err                                                             error
		txnReqId, merchantRefId, refUrl, initiationMode, purpose, orgId string
	)

	// generate merchant ref id and txn req id only if not present
	txnReqId, merchantRefId, err = pay.GenerateTxnReqIdAndMerchantRefId(commonvgpb.Vendor_FEDERAL_BANK,
		bePaymentPb.PaymentProtocol_UPI, txnReqId, merchantRefId)
	if err != nil {
		return nil, fmt.Errorf("failed to generate req id and merchant ref id for the transaction: %w", err)
	}

	// swap reference URL with the static one in case of empty reference url
	if refUrl == "" {
		refUrl = transaction.EPIFI_URL
	}
	// initialise the initiation mode with default if it is empty
	if initiationMode == "" {
		initiationMode = vendors.DefaultInitiationModeMandateExecution
	}
	// initialise purpose with default if it is empty
	if purpose == "" {
		purpose = vendors.DefaultPurpose
	}
	if orgId == "" {
		orgId = transaction.EPIFI_ORG_ID
	}

	paymentDetails := &beOrderPb.PaymentDetails{
		PiFrom:          piFromId,
		PiTo:            piToId,
		Amount:          amount,
		ReqId:           txnReqId,
		PaymentProtocol: protocol,
		MerchantRefId:   merchantRefId,
		ReferenceUrl:    refUrl,
		InitiationMode:  initiationMode,
		Purpose:         purpose,
		OrgId:           orgId,
	}

	return paymentDetails, nil
}

// getVpaForPiId returns the vpa for given pi id
func (s *Service) getVpaForPiId(ctx context.Context, piId string) (string, error) {
	res, err := s.piClient.GetPiById(ctx, &paymentinstrument.GetPiByIdRequest{Id: piId})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		return "", fmt.Errorf("error fetching vpa for piId: %s :%v :%w", piId, rpcErr, epifierrors.ErrTransient)
	}
	return res.GetPaymentInstrument().GetVPA()
}
