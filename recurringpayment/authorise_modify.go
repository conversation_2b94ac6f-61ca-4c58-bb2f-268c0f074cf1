package recurringpayment

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	pb "github.com/epifi/gamma/api/recurringpayment"
	payloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	"github.com/epifi/gamma/pkg/recurringpayment"
)

// nolint: funlen, dupl
// We are using the client as workflowPb.Client_RECURRING_PAYMENT across recurring payment workflows to prevent exposure of backend information on Client side
func (s *Service) AuthoriseRecurringPaymentModify(ctx context.Context, req *pb.AuthoriseRecurringPaymentModifyRequest) (*pb.AuthoriseRecurringPaymentModifyResponse, error) {
	var (
		res = &pb.AuthoriseRecurringPaymentModifyResponse{}
	)
	clientReqId := req.GetClientRequestId()
	if clientReqId == "" {
		clientReqId = req.GetClientId().GetId()
	}
	recurringPayment, err := s.recurringPaymentProcessor.GetRecurringPaymentById(ctx, req.GetRecurringPaymentId())
	if err != nil {
		logger.Error(ctx, "couldn't fetch recurring payment for given recurring payment id", zap.Error(err),
			zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	}

	err = validateActorIdForRequest(recurringPayment, req.GetCurrentActorId())
	if err != nil {
		logger.Error(ctx, "failed to validate actor", zap.String(logger.ACTOR_ID, req.GetCurrentActorId()))
		res.Status = rpc.StatusPermissionDenied()
		return res, nil
	}
	if !(recurringPayment.GetState() == pb.RecurringPaymentState_MODIFY_QUEUED ||
		recurringPayment.GetState() == pb.RecurringPaymentState_MODIFY_INITIATED) {
		logger.Error(ctx, "recurring payment not in expected state for initiating modify request at domain service",
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.String(logger.STATE, recurringPayment.GetState().String()))
		res.Status = rpc.StatusFailedPrecondition()
		return res, nil
	}
	recurringPaymentAction, err := s.recurringPaymentActionsDao.GetByClientRequestId(ctx, req.GetClientRequestId(), false)
	if err != nil {
		logger.Error(ctx, "error in fetching modify action", zap.String(logger.RECURRING_PAYMENT_ID,
			req.GetRecurringPaymentId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	status := s.validateActionExpiry(ctx, recurringPaymentAction, recurringPayment, pb.RecurringPaymentState_ACTIVATED)
	if status != nil {
		res.Status = status
		return res, nil
	}
	recurringPaymentState, err := s.getRecurringPaymentStateForInitiation(
		ctx,
		req.GetCurrentActorId(),
		recurringPayment.GetFromActorId(),
		pb.Action_MODIFY,
	)
	if err != nil {
		logger.Error(ctx, "error while fetching recurring payment state for initiation", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	err = s.initiateOrchestrationToModifyRecurringPayment(ctx, req, recurringPaymentAction, recurringPayment)
	if err != nil {
		logger.Error(ctx, "error in initiating recurring payment orchestration for modification", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	}

	err = s.recurringPaymentDao.UpdateAndChangeStatus(ctx, recurringPayment, nil,
		recurringPayment.GetState(), recurringPaymentState)
	if err != nil {
		logger.Error(ctx, "error in updating recurring payment state to modify initiated", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	recurringPayment.State = recurringPaymentState
	actionDetailedStatus, err := s.initiateModifyWithDomainService(ctx, recurringPayment, recurringPaymentAction, req)
	if err != nil {
		logger.Error(ctx, "error in authorising recurring payment for modify", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	}

	s.publishRecurringPaymentActionEvent(ctx, recurringPaymentAction, recurringPayment)

	res.Status = rpc.StatusOk()
	if actionDetailedStatus != nil {
		res.ActionDetailedStatus = actionDetailedStatus
	}
	return res, nil
}

func (s *Service) initiateModifyWithDomainService(ctx context.Context, recurringPayment *pb.RecurringPayment,
	recurringPaymentAction *pb.RecurringPaymentsAction, req *pb.AuthoriseRecurringPaymentModifyRequest) (*pb.ActionDetailedStatus_DetailedStatus,
	error) {
	updatedParams := recurringPaymentAction.GetActionMetadata().GetModifyActionMetadata().GetUpdatedParams()
	var (
		actionStateToUpdate pb.ActionState
		updateMask          []pb.RecurringPaymentFieldMask
	)
	switch recurringPayment.GetType() {
	case pb.RecurringPaymentType_STANDING_INSTRUCTION:
		// We will update the state back to activated as modify has reached terminal state of failure/success
		err := s.initiateModifyWithSIService(ctx, updatedParams, recurringPayment, req.GetCredential().GetPartnerSdkCredBlock())
		stateToBeUpdated := pb.RecurringPaymentState_ACTIVATED
		if err != nil {
			actionStateToUpdate = pb.ActionState_ACTION_FAILURE
		} else {
			recurringPayment.Amount = updatedParams.GetMaximumAmountLimit()
			recurringPayment.Interval = updatedParams.GetInterval()
			recurringPayment.MaximumAllowedTxns = updatedParams.GetMaximumAllowedTxns()
			recurringPayment.RecurrenceRule = updatedParams.GetRecurrenceRule()
			updateMask = append(updateMask, getUpdateFieldMask(updatedParams)...)
			actionStateToUpdate = pb.ActionState_ACTION_SUCCESS
		}
		err = s.updateRecurringPaymentAndActionInTxnBlock(ctx, recurringPaymentAction, recurringPayment, updateMask,
			nil, stateToBeUpdated, actionStateToUpdate)
		if err != nil {
			return nil, fmt.Errorf("error in updating recurring payment and action %w %s", rpc.StatusAsError(rpc.StatusInternal()),
				req.GetRecurringPaymentId())
		}
		if actionStateToUpdate == pb.ActionState_ACTION_SUCCESS {
			return nil, nil
		} else {
			return nil, fmt.Errorf("error in initiating modify with SI service %w %s", rpc.StatusAsError(rpc.StatusInternal()),
				req.GetRecurringPaymentId())
		}
	case pb.RecurringPaymentType_UPI_MANDATES:
		actionDetailedStatus, err := s.authoriseMandateAction(
			ctx,
			req.GetCredential(),
			recurringPayment,
			recurringPaymentAction,
			req.GetCurrentActorId(),
		)
		if err != nil {
			logger.Error(ctx, "error while authorising mandate modify", zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()),
				zap.Error(err))
		}
		return actionDetailedStatus, nil
	default:
		return nil, fmt.Errorf("recurring payment type not supported for given recurring payment %w", rpc.StatusAsError(rpc.StatusInternal()))
	}
}

func (s *Service) initiateModifyWithSIService(ctx context.Context, updatedParams *pb.MutableParams,
	recurringPayment *pb.RecurringPayment, credBlock string) error {
	res, err := s.siClient.AuthoriseModify(ctx, &siPb.AuthoriseModifyRequest{
		RecurringPaymentId:  recurringPayment.GetId(),
		MaximumAmountLimit:  updatedParams.GetMaximumAmountLimit(),
		StartDate:           updatedParams.GetInterval().GetStartTime(),
		EndDate:             updatedParams.GetInterval().GetEndTime(),
		AllowedFrequency:    updatedParams.GetRecurrenceRule().GetAllowedFrequency(),
		MaximumAllowedTxns:  updatedParams.GetMaximumAllowedTxns(),
		PartnerSdkCredBlock: credBlock,
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		return fmt.Errorf("error in initiating standing instruction modify %w", te)
	}
	return nil
}

func (s *Service) updateRecurringPayment(ctx context.Context, recurringPayment *pb.RecurringPayment,
	updateFieldMask []pb.RecurringPaymentFieldMask, currentState, stateToUpdate pb.RecurringPaymentState) error {
	err := s.recurringPaymentDao.UpdateAndChangeStatus(ctx, recurringPayment, updateFieldMask, currentState, stateToUpdate)
	if err != nil {
		return fmt.Errorf("error in updating recurring payment %w", err)
	}
	return nil
}

func getUpdateFieldMask(updatedParams *pb.MutableParams) []pb.RecurringPaymentFieldMask {
	var (
		updateMask []pb.RecurringPaymentFieldMask
	)
	if updatedParams.GetMaximumAmountLimit() != nil {
		updateMask = append(updateMask, pb.RecurringPaymentFieldMask_MAXIMUM_AMOUNT_LIMIT)
	}
	if updatedParams.GetRecurrenceRule() != nil {
		updateMask = append(updateMask, pb.RecurringPaymentFieldMask_RECURRENCE_RULE)
	}
	if updatedParams.GetMaximumAllowedTxns() != 0 {
		updateMask = append(updateMask, pb.RecurringPaymentFieldMask_MAXIMUM_ALLOWED_TXNS)
	}
	if updatedParams.GetInterval().GetStartTime() != nil {
		updateMask = append(updateMask, pb.RecurringPaymentFieldMask_START_DATE)
	}
	if updatedParams.GetInterval().GetEndTime() != nil {
		updateMask = append(updateMask, pb.RecurringPaymentFieldMask_END_DATE)
	}
	return updateMask
}

func (s *Service) initiateOrchestrationToModifyRecurringPayment(ctx context.Context, req *pb.AuthoriseRecurringPaymentModifyRequest,
	recurringPaymentAction *pb.RecurringPaymentsAction, recurringPayment *pb.RecurringPayment) error {
	clientReqId := req.GetClientRequestId()
	if clientReqId == "" {
		clientReqId = req.GetClientId().GetId()
	}
	if s.featureFlags.EnableRecurringPaymentModificationViaCelestial() {
		workflowReq, processorErr := s.celestialProcessor.GetWorkflowByClientRequestId(ctx, clientReqId, workflowPb.Client_RECURRING_PAYMENT)
		if processorErr != nil {
			return fmt.Errorf("error fetching workflow request for given client req ID %s %w",
				clientReqId, processorErr,
			)
		}
		// workflow waits for signal only for 10 minutes after initiation, and will time out after that
		if time.Since(workflowReq.GetCreatedAt().AsTime()) > s.config.RecurringPaymentModificationParams.AuthorisationTimeLimit {
			return fmt.Errorf("workflow can't be authorised after 10 minutes %w", rpc.StatusAsError(rpc.StatusPermissionDenied()))
		}

		// sending signal to workflow
		payload, marshalErr := protojson.Marshal(&payloadPb.ModifyRecurringPaymentAuthSignal{})
		if marshalErr != nil {
			return fmt.Errorf("error in marshalling modify recurring payment auth signal payload %w %s", rpc.StatusAsError(rpc.StatusInternal()), clientReqId)
		}
		err := s.celestialProcessor.SignalWorkflow(ctx, clientReqId, string(rpNs.ModifyRecurringPaymentAuthSignal),
			workflowPb.Client_RECURRING_PAYMENT, payload, recurringPayment.GetEntityOwnership(), recurringpayment.OwnershipToUseCaseForRecPay[recurringPayment.GetEntityOwnership()])
		if err != nil {
			return fmt.Errorf("error while signaling workflow for modification %w %s", rpc.StatusAsError(rpc.StatusInternal()), recurringPaymentAction.GetClientRequestId())
		}
	} else {
		err := s.initiateOrderProcessing(ctx, recurringPaymentAction.GetClientRequestId())
		if err != nil {
			return fmt.Errorf("error while triggering order processing for modify %w %s", rpc.StatusAsError(rpc.StatusInternal()),
				recurringPaymentAction.GetClientRequestId())
		}
	}
	return nil
}
