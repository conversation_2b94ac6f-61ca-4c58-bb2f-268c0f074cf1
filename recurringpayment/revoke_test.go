// nolint: goimports
package recurringpayment

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/money"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	actorPb "github.com/epifi/gamma/api/actor"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	orderPb "github.com/epifi/gamma/api/order"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	accountPiMocks "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	pb "github.com/epifi/gamma/api/recurringpayment"
	payloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	"github.com/epifi/gamma/api/recurringpayment/standinginstruction/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
	mocks3 "github.com/epifi/gamma/api/upi/mandate/mocks"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	"github.com/epifi/gamma/recurringpayment/internal"
	mocks2 "github.com/epifi/gamma/recurringpayment/internal/mocks"
)

type revokeMandateReqMatcher struct {
	want *upiMandatePb.RevokeMandateRequest
}

func newRevokeMandateReqMatcher(want *upiMandatePb.RevokeMandateRequest) *revokeMandateReqMatcher {
	return &revokeMandateReqMatcher{
		want: want,
	}
}

func (ce *revokeMandateReqMatcher) Matches(x interface{}) bool {
	got, ok := x.(*upiMandatePb.RevokeMandateRequest)
	if !ok {
		return false
	}

	if (ce.want.MandateRequestPayload != nil) != (got.MandateRequestPayload != nil) {
		return false
	}
	if got.MandateRequestPayload == nil {
		return true
	}
	ce.want.MandateRequestPayload.MandateOriginTimestamp = got.MandateRequestPayload.MandateOriginTimestamp
	return reflect.DeepEqual(ce.want, got)
}

func (ce *revokeMandateReqMatcher) String() string {
	return fmt.Sprintf("want: %v", ce.want)
}

func TestService_CreateRevokeAttempt(t *testing.T) {
	var (
		mockOrderClient                *orderMocks.MockOrderServiceClient
		mockRecurringPaymentDao        *daoMocks.MockRecurringPaymentDao
		mockRecurringPaymentsActionDao *daoMocks.MockRecurringPaymentsActionDao
		mockSIClient                   *mocks.MockStandingInstructionServiceClient
		mockRecurringPaymentProcessor  *mocks2.MockRecurringPaymentProcessor
		mockActor                      *actorMocks.MockActorClient
		mockPiClient                   *piMocks.MockPiClient
		mockMandateClient              *mocks3.MockMandateServiceClient
		mockAccountPiClient            *accountPiMocks.MockAccountPIRelationClient
		mockCelestialProcessor         *mocks2.MockCelestialProcessor
	)

	startDate := timestampPb.New(time.Now())
	endDate := timestampPb.New(time.Now().Add(48 * time.Hour))

	recurringPayment := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}
	recurringPaymentAuthorised := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_REVOKE_AUTHORISED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}
	recurringPaymentMandate := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_UPI,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}

	mandateRecurringPayment := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns: 10,
		PartnerBank:        commonvgpb.Vendor_FEDERAL_BANK,
		State:              pb.RecurringPaymentState_ACTIVATED,
		Ownership:          pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:       pb.UIEntryPoint_FIT,
		InitiatedBy:        pb.InitiatedBy_PAYER,
	}

	orderPayload := &pb.RecurringPaymentRevokeInfo{
		RecurringPaymentId: "rp-1",
		RequestId:          "request-id-1",
		ClientRequestId:    "client-req-1",
		InitiatedBy:        pb.InitiatedBy_PAYER,
	}
	marshalledOrderPayload, _ := protojson.Marshal(orderPayload)

	orderPayload2 := &pb.RecurringPaymentRevokeInfo{
		RecurringPaymentId: "rp-1",
		RequestId:          "request-2",
		ClientRequestId:    "client-req-1",
		InitiatedBy:        pb.InitiatedBy_PAYER,
	}
	marshalledOrderPayload2, _ := protojson.Marshal(orderPayload2)
	authRequiredRpPayload, _ := protojson.Marshal(&pb.RecurringPaymentRevokeInfo{
		RecurringPaymentId: recurringPayment.GetId(),
		ClientRequestId:    "client-req-1",
		RequestId:          "request-id-1",
	})
	bypassAuthRpPayload, _ := protojson.Marshal(&pb.RecurringPaymentRevokeInfo{
		RecurringPaymentId: recurringPayment.GetId(),
		ClientRequestId:    "client-req-1",
		RequestId:          "request-id-1",
		BypassAuth:         true,
	})
	signalPayload, _ := protojson.Marshal(&payloadPb.RevokeRecurringPaymentAuthSignal{})
	tests := []struct {
		name           string
		req            *pb.CreateRevokeAttemptRequest
		setupMockCalls func()
		want           *pb.CreateRevokeAttemptResponse
		wantErr        bool
	}{
		{
			name: "retrying same request and order not created",
			req: &pb.CreateRevokeAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentRevokeViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(),
					"rp-1", gomock.Any(), gomock.Any()).Return([]*pb.RecurringPaymentsAction{
					{
						ClientRequestId: "client-req-1",
						VendorRequestId: "request-1",
					},
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(),
					gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockTypeV2(&internal.GetCredBlockTypeV2Request{
					RecurringPaymentType: pb.RecurringPaymentType_STANDING_INSTRUCTION,
					ActorRole:            pb.ActorRole_ACTOR_ROLE_PAYER,
					Action:               pb.Action_REVOKE,
				}).Return(&internal.GetCredBlockTypeV2Response{
					CredBlockType: pb.CredBlockType_PARTNER_SDK,
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					ClientRequestId: "client-req-1",
					VendorRequestId: "request-2",
				}, nil)
				mockSIClient.EXPECT().CreateRevokeAttempt(gomock.Any(), &siPb.CreateRevokeAttemptRequest{
					RecurringPaymentId: "rp-1",
					TransactionId:      "request-2",
				}).Return(&siPb.CreateRevokeAttemptResponse{Status: rpc.StatusOk()}, nil)
				mockOrderClient.EXPECT().CreateOrder(gomock.Any(), &orderPb.CreateOrderRequest{
					ActorFrom:    "actor-1",
					ActorTo:      "actor-2",
					Workflow:     orderPb.OrderWorkflow_REVOKE_RECURRING_PAYMENT,
					Provenance:   orderPb.OrderProvenance_USER_APP,
					OrderPayload: marshalledOrderPayload2,
					Amount:       money.ZeroINR().Pb,
					Status:       orderPb.OrderStatus_CREATED,
					ClientReqId:  "client-req-1",
				}).Return(&orderPb.CreateOrderResponse{
					Status: rpc.StatusOk(),
					Order:  &orderPb.Order{Id: "order-id-1"},
				}, nil)
			},
			want: &pb.CreateRevokeAttemptResponse{
				Status:                   rpc.StatusOk(),
				OrderId:                  "order-id-1",
				TxnId:                    "request-2",
				IsAuthenticationRequired: false,
				CredBlockType:            pb.CredBlockType_PARTNER_SDK,
			},
			wantErr: false,
		},
		{
			name: "revoke attempt created successfully for SI using oms",
			req: &pb.CreateRevokeAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				TransactionId:      "request-id-1",
				Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentRevokeViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(),
					"rp-1", gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(),
					gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockTypeV2(&internal.GetCredBlockTypeV2Request{
					RecurringPaymentType: pb.RecurringPaymentType_STANDING_INSTRUCTION,
					ActorRole:            pb.ActorRole_ACTOR_ROLE_PAYER,
					Action:               pb.Action_REVOKE,
				}).Return(&internal.GetCredBlockTypeV2Response{
					CredBlockType: pb.CredBlockType_PARTNER_SDK,
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment, nil,
					pb.RecurringPaymentState_ACTIVATED, pb.RecurringPaymentState_REVOKE_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_REVOKE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}).Return(nil, nil)
				mockSIClient.EXPECT().CreateRevokeAttempt(gomock.Any(), &siPb.CreateRevokeAttemptRequest{
					RecurringPaymentId: "rp-1",
					TransactionId:      "request-id-1",
				}).Return(&siPb.CreateRevokeAttemptResponse{Status: rpc.StatusOk()}, nil)
				mockOrderClient.EXPECT().CreateOrder(gomock.Any(), &orderPb.CreateOrderRequest{
					ActorFrom:    "actor-1",
					ActorTo:      "actor-2",
					Workflow:     orderPb.OrderWorkflow_REVOKE_RECURRING_PAYMENT,
					Provenance:   orderPb.OrderProvenance_USER_APP,
					OrderPayload: marshalledOrderPayload,
					Amount:       money.ZeroINR().Pb,
					Status:       orderPb.OrderStatus_CREATED,
					ClientReqId:  "client-req-1",
				}).Return(&orderPb.CreateOrderResponse{
					Status: rpc.StatusOk(),
					Order:  &orderPb.Order{Id: "order-id-1"},
				}, nil)
			},
			want: &pb.CreateRevokeAttemptResponse{
				Status:                   rpc.StatusOk(),
				OrderId:                  "order-id-1",
				TxnId:                    "request-id-1",
				IsAuthenticationRequired: false,
				CredBlockType:            pb.CredBlockType_PARTNER_SDK,
			},
			wantErr: false,
		},
		{
			name: "revoke action already exists",
			req: &pb.CreateRevokeAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				TransactionId:      "request-id-1",
				Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
			},
			setupMockCalls: func() {
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(),
					"rp-1", gomock.Any(), gomock.Any()).Return([]*pb.RecurringPaymentsAction{
					{
						RecurringPaymentId: "rp-1",
						ClientRequestId:    "client-req-id",
						Action:             pb.Action_REVOKE,
						State:              pb.ActionState_ACTION_CREATED,
					},
				}, nil)
			},
			want: &pb.CreateRevokeAttemptResponse{
				Status: rpc.StatusFailedPrecondition(),
			},
			wantErr: false,
		},
		{
			name: "revoke attempt created successfully for SI using celestial",
			req: &pb.CreateRevokeAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				CurrentActorId: "actor-1",
				TransactionId:  "request-id-1",
				Provenance:     pb.RecurrencePaymentProvenance_USER_APP,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentRevokeViaCelestial(true, true, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(),
					"rp-1", gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(),
					gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockTypeV2(&internal.GetCredBlockTypeV2Request{
					RecurringPaymentType: pb.RecurringPaymentType_STANDING_INSTRUCTION,
					ActorRole:            pb.ActorRole_ACTOR_ROLE_PAYER,
					Action:               pb.Action_REVOKE,
				}).Return(&internal.GetCredBlockTypeV2Response{
					CredBlockType: pb.CredBlockType_PARTNER_SDK,
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment, nil,
					pb.RecurringPaymentState_ACTIVATED, pb.RecurringPaymentState_REVOKE_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_REVOKE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}).Return(nil, nil)
				mockSIClient.EXPECT().CreateRevokeAttempt(gomock.Any(), &siPb.CreateRevokeAttemptRequest{
					RecurringPaymentId: "rp-1",
					TransactionId:      "request-id-1",
				}).Return(&siPb.CreateRevokeAttemptResponse{Status: rpc.StatusOk()}, nil)

				wfTypeEnum := celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_REVOKE_RECURRING_PAYMENT)
				mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				}, "actor-1", bypassAuthRpPayload, wfTypeEnum, workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT).Return(nil)
			},
			want: &pb.CreateRevokeAttemptResponse{
				Status:                   rpc.StatusOk(),
				TxnId:                    "request-id-1",
				IsAuthenticationRequired: false,
				CredBlockType:            pb.CredBlockType_PARTNER_SDK,
			},
			wantErr: false,
		},
		{
			name: "revoke action already exists",
			req: &pb.CreateRevokeAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				TransactionId:      "request-id-1",
				Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
			},
			setupMockCalls: func() {
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(),
					"rp-1", gomock.Any(), gomock.Any()).Return([]*pb.RecurringPaymentsAction{
					{
						RecurringPaymentId: "rp-1",
						ClientRequestId:    "client-req-id",
						Action:             pb.Action_REVOKE,
						State:              pb.ActionState_ACTION_CREATED,
					},
				}, nil)
			},
			want: &pb.CreateRevokeAttemptResponse{
				Status: rpc.StatusFailedPrecondition(),
			},
			wantErr: false,
		},
		{
			name: "error while fetching order",
			req: &pb.CreateRevokeAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				TransactionId:      "request-id-1",
				Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
			},
			setupMockCalls: func() {
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(),
					"rp-1", gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(nil, fmt.Errorf("error"))
			},
			want:    &pb.CreateRevokeAttemptResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "error while fetching cred block type",
			req: &pb.CreateRevokeAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				TransactionId:      "request-id-1",
				Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentRevokeViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(),
					"rp-1", gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(),
					gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockTypeV2(&internal.GetCredBlockTypeV2Request{
					RecurringPaymentType: pb.RecurringPaymentType_STANDING_INSTRUCTION,
					ActorRole:            pb.ActorRole_ACTOR_ROLE_PAYER,
					Action:               pb.Action_REVOKE,
				}).Return(nil, fmt.Errorf("error"))
			},
			want:    &pb.CreateRevokeAttemptResponse{Status: rpc.StatusInvalidArgument()},
			wantErr: false,
		},
		{
			name: "failed to update rp status",
			req: &pb.CreateRevokeAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				CurrentActorId: "actor-2",
				TransactionId:  "request-id-1",
				Provenance:     pb.RecurrencePaymentProvenance_USER_APP,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentRevokeViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(),
					"rp-1", gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(),
					gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(mandateRecurringPayment, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockTypeV2(&internal.GetCredBlockTypeV2Request{
					RecurringPaymentType: pb.RecurringPaymentType_UPI_MANDATES,
					ActorRole:            pb.ActorRole_ACTOR_ROLE_PAYEE,
					Action:               pb.Action_REVOKE,
				}).Return(&internal.GetCredBlockTypeV2Response{
					IsAuthRequired: true,
					CredBlockType:  pb.CredBlockType_PARTNER_SDK,
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), mandateRecurringPayment, nil,
					pb.RecurringPaymentState_ACTIVATED, pb.RecurringPaymentState_REVOKE_QUEUED).Return(fmt.Errorf("error"))
			},
			want:    &pb.CreateRevokeAttemptResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "revoke attempt created successfully for mandate using oms flow",
			req: &pb.CreateRevokeAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				TransactionId:      "request-id-1",
				Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentRevokeViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(),
					"rp-1", gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(),
					gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPaymentMandate, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockTypeV2(&internal.GetCredBlockTypeV2Request{
					RecurringPaymentType: pb.RecurringPaymentType_UPI_MANDATES,
					ActorRole:            pb.ActorRole_ACTOR_ROLE_PAYER,
					Action:               pb.Action_REVOKE,
				}).Return(&internal.GetCredBlockTypeV2Response{
					IsAuthRequired: true,
					CredBlockType:  pb.CredBlockType_NPCI,
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPaymentMandate, nil,
					pb.RecurringPaymentState_ACTIVATED, pb.RecurringPaymentState_REVOKE_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_REVOKE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}).Return(nil, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "payer@vpa",
							},
						},
						VerifiedName: "abc",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-2"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "payee@vpa",
							},
						},
						VerifiedName: "xyz",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockAccountPiClient.EXPECT().GetByPiId(gomock.Any(), &accountPiPb.GetByPiIdRequest{PiId: "pi-1"}).Return(&accountPiPb.GetByPiIdResponse{
					Status:    rpc.StatusOk(),
					AccountId: "account-id-1",
				}, nil)
				mockMandateClient.EXPECT().GetMandateRequestParameters(gomock.Any(), &upiMandatePb.GetMandateRequestParametersRequest{
					ReqId: "request-id-1",
				}).Return(&upiMandatePb.GetMandateRequestParametersResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "payer@vpa",
							},
						},
						VerifiedName: "abc",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-2"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "payee@vpa",
							},
						},
						VerifiedName: "xyz",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockOrderClient.EXPECT().CreateOrder(gomock.Any(), &orderPb.CreateOrderRequest{
					ActorFrom:    "actor-1",
					ActorTo:      "actor-2",
					Workflow:     orderPb.OrderWorkflow_REVOKE_RECURRING_PAYMENT,
					Provenance:   orderPb.OrderProvenance_USER_APP,
					OrderPayload: marshalledOrderPayload,
					Amount:       money.ZeroINR().Pb,
					Status:       orderPb.OrderStatus_CREATED,
					ClientReqId:  "client-req-1",
				}).Return(&orderPb.CreateOrderResponse{
					Status: rpc.StatusOk(),
					Order:  &orderPb.Order{Id: "order-id-1"},
				}, nil)
				mockActor.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor-2"}).Return(
					&actorPb.GetEntityDetailsByActorIdResponse{Name: &commontypes.Name{FirstName: "payee"}, Status: rpc.StatusOk()}, nil)
				mockMandateClient.EXPECT().RevokeMandate(gomock.Any(), newRevokeMandateReqMatcher(&upiMandatePb.RevokeMandateRequest{
					RecurringPaymentId: "rp-1",
					ReqId:              "request-id-1",
					MandateRequestPayload: &upiMandatePb.Payload{
						PayerVpa:    "payer@vpa",
						PayeeVpa:    "payee@vpa",
						MandateName: "xyz",
					},
					CurrentActorRole: upiMandatePb.ActorRole_ACTOR_ROLE_PAYER,
					PartnerBank:      commonvgpb.Vendor_FEDERAL_BANK,
					InitiatedBy:      upiMandatePb.MandateInitiatedBy_PAYER,
				})).Return(&upiMandatePb.RevokeMandateResponse{Status: rpc.StatusOk()}, nil)
			},
			want: &pb.CreateRevokeAttemptResponse{
				Status:                   rpc.StatusOk(),
				OrderId:                  "order-id-1",
				TxnId:                    "request-id-1",
				IsAuthenticationRequired: true,
				CredBlockType:            pb.CredBlockType_NPCI,
				TransactionAttributes: []*pb.TransactionAttribute{
					{
						PayerAccountId:                "account-id-1",
						TransactionId:                 "request-id-1",
						PaymentProtocol:               paymentPb.PaymentProtocol_UPI,
						PayeeActorName:                "payee",
						Amount:                        money.ZeroINR().Pb,
						PayerPaymentInstrument:        "payer@vpa",
						PayeePaymentInstrument:        "payee@vpa",
						DisplayPayeePaymentInstrument: "payee@vpa",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "revoke attempt created successfully for mandate using celestial flow",
			req: &pb.CreateRevokeAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				TransactionId:      "request-id-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				Provenance: pb.RecurrencePaymentProvenance_USER_APP,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentRevokeViaCelestial(true, true, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(),
					"rp-1", gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(),
					gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPaymentMandate, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockTypeV2(&internal.GetCredBlockTypeV2Request{
					RecurringPaymentType: pb.RecurringPaymentType_UPI_MANDATES,
					ActorRole:            pb.ActorRole_ACTOR_ROLE_PAYER,
					Action:               pb.Action_REVOKE,
				}).Return(&internal.GetCredBlockTypeV2Response{
					IsAuthRequired: true,
					CredBlockType:  pb.CredBlockType_NPCI,
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPaymentMandate, nil,
					pb.RecurringPaymentState_ACTIVATED, pb.RecurringPaymentState_REVOKE_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_REVOKE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}).Return(nil, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "payer@vpa",
							},
						},
						VerifiedName: "abc",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-2"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "payee@vpa",
							},
						},
						VerifiedName: "xyz",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockAccountPiClient.EXPECT().GetByPiId(gomock.Any(), &accountPiPb.GetByPiIdRequest{PiId: "pi-1"}).Return(&accountPiPb.GetByPiIdResponse{
					Status:    rpc.StatusOk(),
					AccountId: "account-id-1",
				}, nil)
				mockMandateClient.EXPECT().GetMandateRequestParameters(gomock.Any(), &upiMandatePb.GetMandateRequestParametersRequest{
					ReqId: "request-id-1",
				}).Return(&upiMandatePb.GetMandateRequestParametersResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "payer@vpa",
							},
						},
						VerifiedName: "abc",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-2"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "payee@vpa",
							},
						},
						VerifiedName: "xyz",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				}, "actor-1", authRequiredRpPayload, celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_REVOKE_RECURRING_PAYMENT), workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT).Return(nil)
				mockActor.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor-2"}).Return(
					&actorPb.GetEntityDetailsByActorIdResponse{Name: &commontypes.Name{FirstName: "payee"}, Status: rpc.StatusOk()}, nil)
				mockMandateClient.EXPECT().RevokeMandate(gomock.Any(), newRevokeMandateReqMatcher(&upiMandatePb.RevokeMandateRequest{
					RecurringPaymentId: "rp-1",
					ReqId:              "request-id-1",
					MandateRequestPayload: &upiMandatePb.Payload{
						PayerVpa:    "payer@vpa",
						PayeeVpa:    "payee@vpa",
						MandateName: "xyz",
					},
					CurrentActorRole: upiMandatePb.ActorRole_ACTOR_ROLE_PAYER,
					PartnerBank:      commonvgpb.Vendor_FEDERAL_BANK,
					InitiatedBy:      upiMandatePb.MandateInitiatedBy_PAYER,
				})).Return(&upiMandatePb.RevokeMandateResponse{Status: rpc.StatusOk()}, nil)
			},
			want: &pb.CreateRevokeAttemptResponse{
				Status:                   rpc.StatusOk(),
				TxnId:                    "request-id-1",
				IsAuthenticationRequired: true,
				CredBlockType:            pb.CredBlockType_NPCI,
				TransactionAttributes: []*pb.TransactionAttribute{
					{
						PayerAccountId:                "account-id-1",
						TransactionId:                 "request-id-1",
						PaymentProtocol:               paymentPb.PaymentProtocol_UPI,
						PayeeActorName:                "payee",
						Amount:                        money.ZeroINR().Pb,
						PayerPaymentInstrument:        "payer@vpa",
						PayeePaymentInstrument:        "payee@vpa",
						DisplayPayeePaymentInstrument: "payee@vpa",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "failed to initiate celestial workflow for recurring payment revoke",
			req: &pb.CreateRevokeAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				TransactionId:      "request-id-1",
				Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentRevokeViaCelestial(true, true, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(),
					"rp-1", gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(),
					gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockTypeV2(&internal.GetCredBlockTypeV2Request{
					RecurringPaymentType: pb.RecurringPaymentType_STANDING_INSTRUCTION,
					ActorRole:            pb.ActorRole_ACTOR_ROLE_PAYER,
					Action:               pb.Action_REVOKE,
				}).Return(&internal.GetCredBlockTypeV2Response{
					IsAuthRequired: false,
					CredBlockType:  pb.CredBlockType_CRED_BLOCK_TYPE_UNSPECIFIED,
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment, nil,
					pb.RecurringPaymentState_ACTIVATED, pb.RecurringPaymentState_REVOKE_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_REVOKE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}).Return(nil, nil)
				mockSIClient.EXPECT().CreateRevokeAttempt(gomock.Any(), &siPb.CreateRevokeAttemptRequest{
					RecurringPaymentId: "rp-1",
					TransactionId:      "request-id-1",
				}).Return(&siPb.CreateRevokeAttemptResponse{Status: rpc.StatusOk()}, nil)
				mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				}, "actor-1", bypassAuthRpPayload, celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_REVOKE_RECURRING_PAYMENT), workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT).Return(rpc.StatusAsError(rpc.StatusInternal()))
			},
			want: &pb.CreateRevokeAttemptResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "failed to create revoke attempt due to error in signalling workflow in case of authorised request",
			req: &pb.CreateRevokeAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				TransactionId:      "request-id-1",
				Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentRevokeViaCelestial(true, true, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(),
					"rp-1", gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(),
					gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPaymentAuthorised, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockTypeV2(&internal.GetCredBlockTypeV2Request{
					RecurringPaymentType: pb.RecurringPaymentType_STANDING_INSTRUCTION,
					ActorRole:            pb.ActorRole_ACTOR_ROLE_PAYER,
					Action:               pb.Action_REVOKE,
				}).Return(&internal.GetCredBlockTypeV2Response{
					IsAuthRequired: false,
					CredBlockType:  pb.CredBlockType_NPCI,
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPaymentAuthorised, nil,
					recurringPaymentAuthorised.GetState(), pb.RecurringPaymentState_REVOKE_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_REVOKE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}).Return(nil, nil)
				mockSIClient.EXPECT().CreateRevokeAttempt(gomock.Any(), &siPb.CreateRevokeAttemptRequest{
					RecurringPaymentId: "rp-1",
					TransactionId:      "request-id-1",
				}).Return(&siPb.CreateRevokeAttemptResponse{Status: rpc.StatusOk()}, nil)
				mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				}, "actor-1", bypassAuthRpPayload, celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_REVOKE_RECURRING_PAYMENT), workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT).Return(nil)
				mockCelestialProcessor.EXPECT().SignalWorkflow(gomock.Any(),
					"client-req-1", string(rpNs.RevokeRecurringPaymentAuthSignal), workflowPb.Client_RECURRING_PAYMENT, signalPayload, gomock.Any(), gomock.Any()).Return(
					rpc.StatusAsError(rpc.StatusInternal()))
			},
			want: &pb.CreateRevokeAttemptResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "successfully created revoke attempt in case of authorised request",
			req: &pb.CreateRevokeAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				TransactionId:      "request-id-1",
				Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentRevokeViaCelestial(true, true, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(),
					"rp-1", gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(),
					gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPaymentAuthorised, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockTypeV2(&internal.GetCredBlockTypeV2Request{
					RecurringPaymentType: pb.RecurringPaymentType_STANDING_INSTRUCTION,
					ActorRole:            pb.ActorRole_ACTOR_ROLE_PAYER,
					Action:               pb.Action_REVOKE,
				}).Return(&internal.GetCredBlockTypeV2Response{
					IsAuthRequired: false,
					CredBlockType:  pb.CredBlockType_PARTNER_SDK,
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPaymentAuthorised, nil,
					recurringPaymentAuthorised.GetState(), pb.RecurringPaymentState_REVOKE_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_REVOKE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}).Return(nil, nil)
				mockSIClient.EXPECT().CreateRevokeAttempt(gomock.Any(), &siPb.CreateRevokeAttemptRequest{
					RecurringPaymentId: "rp-1",
					TransactionId:      "request-id-1",
				}).Return(&siPb.CreateRevokeAttemptResponse{Status: rpc.StatusOk()}, nil)
				mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				}, "actor-1", bypassAuthRpPayload, celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_REVOKE_RECURRING_PAYMENT), workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT).Return(nil)
				mockCelestialProcessor.EXPECT().SignalWorkflow(gomock.Any(),
					"client-req-1", string(rpNs.RevokeRecurringPaymentAuthSignal), workflowPb.Client_RECURRING_PAYMENT, signalPayload, gomock.Any(), gomock.Any()).Return(
					rpc.StatusAsError(rpc.StatusOk()))
			},
			want: &pb.CreateRevokeAttemptResponse{
				Status:                   rpc.StatusOk(),
				TxnId:                    "request-id-1",
				IsAuthenticationRequired: false,
				CredBlockType:            pb.CredBlockType_PARTNER_SDK,
			},
			wantErr: false,
		},
		{
			name: "should create revoke attempt successfully without auth for upi mandate using celestial flow",
			req: &pb.CreateRevokeAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				CurrentActorRole:   pb.ActorRole_ACTOR_ROLE_PAYER,
				TransactionId:      "request-id-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				Provenance:  pb.RecurrencePaymentProvenance_USER_APP,
				InitiatedBy: pb.InitiatedBy_PAYEE,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentRevokeViaCelestial(true, true, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(),
					"rp-1", gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(),
					gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPaymentMandate, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockTypeV2(&internal.GetCredBlockTypeV2Request{
					RecurringPaymentType: pb.RecurringPaymentType_UPI_MANDATES,
					ActorRole:            pb.ActorRole_ACTOR_ROLE_PAYER,
					Action:               pb.Action_REVOKE,
					InitiatedBy:          pb.InitiatedBy_PAYEE,
				}).Return(&internal.GetCredBlockTypeV2Response{
					IsAuthRequired: false,
					CredBlockType:  pb.CredBlockType_NPCI,
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPaymentMandate, nil,
					pb.RecurringPaymentState_ACTIVATED, pb.RecurringPaymentState_REVOKE_AUTHORISED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_REVOKE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}).Return(nil, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "payer@vpa",
							},
						},
						VerifiedName: "abc",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-2"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "payee@vpa",
							},
						},
						VerifiedName: "xyz",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				}, "actor-1", bypassAuthRpPayload, celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_REVOKE_RECURRING_PAYMENT), workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT).Return(nil)
				mockMandateClient.EXPECT().RevokeMandate(gomock.Any(), newRevokeMandateReqMatcher(&upiMandatePb.RevokeMandateRequest{
					RecurringPaymentId: "rp-1",
					ReqId:              "request-id-1",
					MandateRequestPayload: &upiMandatePb.Payload{
						PayerVpa:    "payer@vpa",
						PayeeVpa:    "payee@vpa",
						MandateName: "xyz",
					},
					CurrentActorRole: upiMandatePb.ActorRole_ACTOR_ROLE_PAYER,
					PartnerBank:      commonvgpb.Vendor_FEDERAL_BANK,
					InitiatedBy:      upiMandatePb.MandateInitiatedBy_PAYEE,
				})).Return(&upiMandatePb.RevokeMandateResponse{Status: rpc.StatusOk()}, nil)
			},
			want: &pb.CreateRevokeAttemptResponse{
				Status:                   rpc.StatusOk(),
				TxnId:                    "request-id-1",
				IsAuthenticationRequired: false,
				CredBlockType:            pb.CredBlockType_NPCI,
			},
			wantErr: false,
		},
		{
			name: "should create revoke attempt without auth successfully for SI using celestial",
			req: &pb.CreateRevokeAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				CurrentActorId: "actor-2",
				TransactionId:  "request-id-1",
				Provenance:     pb.RecurrencePaymentProvenance_USER_APP,
				InitiatedBy:    pb.InitiatedBy_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentRevokeViaCelestial(true, true, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(),
					"rp-1", gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(),
					gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockTypeV2(&internal.GetCredBlockTypeV2Request{
					RecurringPaymentType: pb.RecurringPaymentType_STANDING_INSTRUCTION,
					ActorRole:            pb.ActorRole_ACTOR_ROLE_PAYEE,
					InitiatedBy:          pb.InitiatedBy_PAYER,
					Action:               pb.Action_REVOKE,
				}).Return(&internal.GetCredBlockTypeV2Response{
					IsAuthRequired: false,
					CredBlockType:  pb.CredBlockType_PARTNER_SDK,
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment, nil,
					pb.RecurringPaymentState_ACTIVATED, pb.RecurringPaymentState_REVOKE_AUTHORISED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_REVOKE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}).Return(nil, nil)
				mockSIClient.EXPECT().CreateRevokeAttempt(gomock.Any(), &siPb.CreateRevokeAttemptRequest{
					RecurringPaymentId: "rp-1",
					TransactionId:      "request-id-1",
				}).Return(&siPb.CreateRevokeAttemptResponse{Status: rpc.StatusOk()}, nil)

				wfTypeEnum := celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_REVOKE_RECURRING_PAYMENT)
				mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				}, "actor-2", bypassAuthRpPayload, wfTypeEnum, workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT).Return(nil)
			},
			want: &pb.CreateRevokeAttemptResponse{
				Status:                   rpc.StatusOk(),
				TxnId:                    "request-id-1",
				IsAuthenticationRequired: false,
				CredBlockType:            pb.CredBlockType_PARTNER_SDK,
			},
			wantErr: false,
		},
		{
			name: "should fail when UPI mandate has pending executions in in-flight state",
			req: &pb.CreateRevokeAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				CurrentActorRole:   pb.ActorRole_ACTOR_ROLE_PAYER,
			},
			setupMockCalls: func() {
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"},
				}).Return(&orderPb.GetOrderResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPaymentMandate, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any()).Return([]*pb.RecurringPaymentsAction{
					{
						RecurringPaymentId: "rp-1",
						ClientRequestId:    "exec-req-1",
						Action:             pb.Action_EXECUTE,
						State:              pb.ActionState_ACTION_CREATED,
					},
				}, nil)
				mockOrderClient.EXPECT().GetOrdersWithTransactions(gomock.Any(), &orderPb.GetOrdersWithTransactionsRequest{
					OrderIdentifiers: []*orderPb.OrderIdentifier{
						{
							Identifier: &orderPb.OrderIdentifier_ClientReqId{
								ClientReqId: "exec-req-1",
							},
						},
					},
				}).Return(&orderPb.GetOrdersWithTransactionsResponse{
					Status: rpc.StatusOk(),
					OrderWithTransactions: []*orderPb.OrderWithTransactions{
						{
							Order: &orderPb.Order{
								Id:          "order-1",
								ClientReqId: "exec-req-1",
							},
							Transactions: []*paymentPb.Transaction{
								{
									Id:     "txn-1",
									Status: paymentPb.TransactionStatus_IN_PROGRESS,
								},
							},
						},
					},
				}, nil)
			},
			want: &pb.CreateRevokeAttemptResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "should allow revoke if transactions of pending actions are in terminal status",
			req: &pb.CreateRevokeAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				TransactionId:      "request-id-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				Provenance: pb.RecurrencePaymentProvenance_USER_APP,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentRevokeViaCelestial(true, true, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(),
					"rp-1", gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any()).Return([]*pb.RecurringPaymentsAction{
					{
						RecurringPaymentId: "rp-1",
						ClientRequestId:    "exec-req-1",
						Action:             pb.Action_EXECUTE,
						State:              pb.ActionState_ACTION_CREATED,
					},
				}, nil)
				mockOrderClient.EXPECT().GetOrdersWithTransactions(gomock.Any(), &orderPb.GetOrdersWithTransactionsRequest{
					OrderIdentifiers: []*orderPb.OrderIdentifier{
						{
							Identifier: &orderPb.OrderIdentifier_ClientReqId{
								ClientReqId: "exec-req-1",
							},
						},
					},
				}).Return(&orderPb.GetOrdersWithTransactionsResponse{
					Status: rpc.StatusOk(),
					OrderWithTransactions: []*orderPb.OrderWithTransactions{
						{
							Order: &orderPb.Order{
								Id:          "order-1",
								ClientReqId: "exec-req-1",
							},
							Transactions: []*paymentPb.Transaction{
								{
									Id:     "txn-1",
									Status: paymentPb.TransactionStatus_FAILED,
								},
							},
						},
					},
				}, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPaymentMandate, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockTypeV2(&internal.GetCredBlockTypeV2Request{
					RecurringPaymentType: pb.RecurringPaymentType_UPI_MANDATES,
					ActorRole:            pb.ActorRole_ACTOR_ROLE_PAYER,
					Action:               pb.Action_REVOKE,
				}).Return(&internal.GetCredBlockTypeV2Response{
					IsAuthRequired: true,
					CredBlockType:  pb.CredBlockType_NPCI,
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPaymentMandate, nil,
					pb.RecurringPaymentState_ACTIVATED, pb.RecurringPaymentState_REVOKE_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_REVOKE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "request-id-1",
				}).Return(nil, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "payer@vpa",
							},
						},
						VerifiedName: "abc",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-2"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "payee@vpa",
							},
						},
						VerifiedName: "xyz",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockAccountPiClient.EXPECT().GetByPiId(gomock.Any(), &accountPiPb.GetByPiIdRequest{PiId: "pi-1"}).Return(&accountPiPb.GetByPiIdResponse{
					Status:    rpc.StatusOk(),
					AccountId: "account-id-1",
				}, nil)
				mockMandateClient.EXPECT().GetMandateRequestParameters(gomock.Any(), &upiMandatePb.GetMandateRequestParametersRequest{
					ReqId: "request-id-1",
				}).Return(&upiMandatePb.GetMandateRequestParametersResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "payer@vpa",
							},
						},
						VerifiedName: "abc",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-2"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "payee@vpa",
							},
						},
						VerifiedName: "xyz",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				}, "actor-1", authRequiredRpPayload, celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_REVOKE_RECURRING_PAYMENT), workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT).Return(nil)
				mockActor.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor-2"}).Return(
					&actorPb.GetEntityDetailsByActorIdResponse{Name: &commontypes.Name{FirstName: "payee"}, Status: rpc.StatusOk()}, nil)
				mockMandateClient.EXPECT().RevokeMandate(gomock.Any(), newRevokeMandateReqMatcher(&upiMandatePb.RevokeMandateRequest{
					RecurringPaymentId: "rp-1",
					ReqId:              "request-id-1",
					MandateRequestPayload: &upiMandatePb.Payload{
						PayerVpa:    "payer@vpa",
						PayeeVpa:    "payee@vpa",
						MandateName: "xyz",
					},
					CurrentActorRole: upiMandatePb.ActorRole_ACTOR_ROLE_PAYER,
					PartnerBank:      commonvgpb.Vendor_FEDERAL_BANK,
					InitiatedBy:      upiMandatePb.MandateInitiatedBy_PAYER,
				})).Return(&upiMandatePb.RevokeMandateResponse{Status: rpc.StatusOk()}, nil)
			},
			want: &pb.CreateRevokeAttemptResponse{
				Status:                   rpc.StatusOk(),
				TxnId:                    "request-id-1",
				IsAuthenticationRequired: true,
				CredBlockType:            pb.CredBlockType_NPCI,
				TransactionAttributes: []*pb.TransactionAttribute{
					{
						PayerAccountId:                "account-id-1",
						TransactionId:                 "request-id-1",
						PaymentProtocol:               paymentPb.PaymentProtocol_UPI,
						PayeeActorName:                "payee",
						Amount:                        money.ZeroINR().Pb,
						PayerPaymentInstrument:        "payer@vpa",
						PayeePaymentInstrument:        "payee@vpa",
						DisplayPayeePaymentInstrument: "payee@vpa",
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()
			mockOrderClient = orderMocks.NewMockOrderServiceClient(ctr)
			mockRecurringPaymentDao = daoMocks.NewMockRecurringPaymentDao(ctr)
			mockRecurringPaymentsActionDao = daoMocks.NewMockRecurringPaymentsActionDao(ctr)
			mockSIClient = mocks.NewMockStandingInstructionServiceClient(ctr)
			mockRecurringPaymentProcessor = mocks2.NewMockRecurringPaymentProcessor(ctr)
			mockActor = actorMocks.NewMockActorClient(ctr)
			mockPiClient = piMocks.NewMockPiClient(ctr)
			mockMandateClient = mocks3.NewMockMandateServiceClient(ctr)
			mockAccountPiClient = accountPiMocks.NewMockAccountPIRelationClient(ctr)
			mockCelestialProcessor = mocks2.NewMockCelestialProcessor(ctr)
			svc := NewService(mockRecurringPaymentDao, mockOrderClient, mockSIClient, mockRecurringPaymentsActionDao, nil, mockActor, nil, nil, nil, conf, mockRecurringPaymentProcessor, mockMandateClient, mockAccountPiClient, mockPiClient, nil, nil, nil, nil, dynamicConf.FeatureFlags(), mockCelestialProcessor, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

			tt.setupMockCalls()
			got, err := svc.CreateRevokeAttempt(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateRevokeAttempt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("CreateRevokeAttempt() got = %v, want %v", got, tt.want)
			}
		})
	}
}
