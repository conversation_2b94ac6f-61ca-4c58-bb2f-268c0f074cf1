package recurringpayment

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/order/domain"
	pb "github.com/epifi/gamma/api/recurringpayment"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
)

var (
	errActionFailed = fmt.Errorf("action failed %w", domain.ErrPermanent)
)

// nolint: funlen
func (s *Service) ProcessRecurringPaymentModify(ctx context.Context, req *domain.ProcessFulfilmentRequest) (*domain.ProcessFulfilmentResponse, error) {
	res := &domain.ProcessFulfilmentResponse{}
	responseHeader := &domain.DomainResponseHeader{}
	res.ResponseHeader = responseHeader
	recurringPaymentModifyInfo := &pb.RecurringPaymentModifyInfo{}
	unmarshalOptions := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	if unmarshalErr := unmarshalOptions.Unmarshal(req.GetPayload(), recurringPaymentModifyInfo); unmarshalErr != nil {
		logger.Error(ctx, "failed to unmarshal payload for recurring payment modify", zap.Error(unmarshalErr))
		responseHeader.Status = domain.DomainProcessingStatus_PERMANENT_FAILURE
		return res, nil
	}
	recurringPaymentId := recurringPaymentModifyInfo.GetRecurringPaymentId()
	requestId := recurringPaymentModifyInfo.GetRequestId()
	clientRequestId := recurringPaymentModifyInfo.GetClientRequestId()
	recurringPayment, err := s.recurringPaymentDao.GetById(ctx, recurringPaymentId)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "recurring payment record not found", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
		responseHeader.Status = domain.DomainProcessingStatus_PERMANENT_FAILURE
		return res, nil
	case err != nil:
		logger.Error(ctx, "error while fetching recurring payment", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId),
			zap.Error(err))
		responseHeader.Status = domain.DomainProcessingStatus_TRANSIENT_FAILURE
		return res, nil
	default:
		logger.Debug(ctx, "recurring payment entry fetched successfully")
	}
	switch recurringPayment.GetState() {
	case pb.RecurringPaymentState_MODIFY_INITIATED:
		// TODO(priyansh) : Add threshold for moving order to failure
		logger.Info(ctx, "recurring payment modify initiation pending", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
		responseHeader.Status = domain.DomainProcessingStatus_NO_OP
	case pb.RecurringPaymentState_MODIFY_QUEUED:
		logger.Info(ctx, "recurring payment modify not initiated by user")
		recurringPaymentAction, dbErr := s.recurringPaymentActionsDao.GetByClientRequestId(ctx, clientRequestId, false)
		if dbErr != nil {
			logger.Error(ctx, "error in fetching modify action", zap.String(logger.RECURRING_PAYMENT_ID,
				recurringPaymentId), zap.Error(dbErr), zap.String(logger.CLIENT_REQUEST_ID, clientRequestId))
			responseHeader.Status = domain.GetStatusFrom(dbErr)
			return res, nil
		}
		err = s.checkAuthorisationThreshold(ctx, recurringPayment, recurringPaymentAction,
			pb.RecurringPaymentState_ACTIVATED)
		if err != nil {
			logger.Error(ctx, "error in checking authorisation threshold", zap.Error(err),
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
			responseHeader.Status = domain.GetStatusFrom(err)
			return res, nil
		}
		responseHeader.Status = domain.DomainProcessingStatus_NO_OP
		return res, nil
	case pb.RecurringPaymentState_ACTIVATED:
		logger.Debug(ctx, "checking recurring payment modify status with domain services",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
		err = s.getActionStatusFromDomainService(ctx, recurringPayment, requestId, siPb.RequestType_MODIFY)
		if err != nil {
			logger.Error(ctx, "error in modify", zap.Error(err))
			responseHeader.Status = domain.GetStatusFrom(err)
			return res, nil
		}
		err = s.checkAndCreateOneTimeMandateExecutionOrder(ctx, recurringPayment)
		if err != nil {
			logger.Error(ctx, "error while checking and creating mandate execution order",
				zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
			responseHeader.Status = domain.GetStatusFrom(err)
		} else {
			responseHeader.Status = domain.DomainProcessingStatus_SUCCESS
		}
	case pb.RecurringPaymentState_MODIFY_AUTHORISED:
		var (
			updateMask             []pb.RecurringPaymentFieldMask
			recurringPaymentAction *pb.RecurringPaymentsAction
			actionStateToUpdate    pb.ActionState
			stateToUpdate          pb.RecurringPaymentState
		)
		logger.Debug(ctx, "checking recurring payment modify status with domain services")
		err = s.getActionStatusFromDomainService(ctx, recurringPayment, requestId, siPb.RequestType_MODIFY)
		switch {
		// roll back state back to activated for permanent failure
		case errors.Is(err, errActionFailed) || errors.Is(err, epifierrors.ErrPermanent):
			// updating recurring payment parameters in case of success
			recurringPaymentAction, err = s.recurringPaymentActionsDao.GetByClientRequestId(ctx, clientRequestId, false)
			if err != nil {
				logger.Error(ctx, "error in fetching modify action", zap.String(logger.RECURRING_PAYMENT_ID,
					recurringPaymentId), zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, clientRequestId))
				responseHeader.Status = domain.GetStatusFrom(err)
				return res, nil
			}
			actionStateToUpdate = pb.ActionState_ACTION_FAILURE
			stateToUpdate = pb.RecurringPaymentState_ACTIVATED
			responseHeader.Status = domain.DomainProcessingStatus_PERMANENT_FAILURE
		case err != nil:
			logger.Error(ctx, "error in fetching status from domain service",
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId), zap.Error(err))
			responseHeader.Status = domain.GetStatusFrom(err)
			return res, nil
		default:
			// updating recurring payment parameters in case of success
			recurringPaymentAction, err = s.recurringPaymentActionsDao.GetByClientRequestId(ctx, clientRequestId, false)
			if err != nil {
				logger.Error(ctx, "error in fetching modify action", zap.String(logger.RECURRING_PAYMENT_ID,
					recurringPaymentId), zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, clientRequestId))
				responseHeader.Status = domain.GetStatusFrom(err)
				return res, nil
			}
			updatedParams := recurringPaymentAction.GetActionMetadata().GetModifyActionMetadata().GetUpdatedParams()
			if updatedParams.GetMaximumAmountLimit() != nil {
				recurringPayment.Amount = updatedParams.GetMaximumAmountLimit()
			}
			if updatedParams.GetInterval() != nil {
				recurringPayment.Interval = updatedParams.GetInterval()
			}
			if updatedParams.GetMaximumAllowedTxns() != 0 {
				recurringPayment.MaximumAllowedTxns = updatedParams.GetMaximumAllowedTxns()
			}
			if updatedParams.GetRecurrenceRule() != nil {
				recurringPayment.RecurrenceRule = updatedParams.GetRecurrenceRule()
			}
			updateMask = append(updateMask, getUpdateFieldMask(updatedParams)...)
			stateToUpdate = pb.RecurringPaymentState_ACTIVATED
			actionStateToUpdate = pb.ActionState_ACTION_SUCCESS
			responseHeader.Status = domain.DomainProcessingStatus_SUCCESS
		}
		err = s.updateRecurringPaymentAndActionInTxnBlock(ctx, recurringPaymentAction, recurringPayment,
			updateMask, nil, stateToUpdate, actionStateToUpdate)
		if err != nil {
			logger.Error(ctx, "error in updating recurring payment and action", zap.Error(err),
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
			responseHeader.Status = domain.GetStatusFrom(err)
			return res, nil
		}
		// for mandate modification in case the execution is not initiated with the vendor
		// we cancel the existing order.
		// once the mandate modification reaches terminal state we create an order again
		// with updated params if modify action is success else with original params
		if actionStateToUpdate == pb.ActionState_ACTION_SUCCESS ||
			actionStateToUpdate == pb.ActionState_ACTION_FAILURE ||
			actionStateToUpdate == pb.ActionState_ACTION_EXPIRED {
			err = s.checkAndCreateOneTimeMandateExecutionOrder(ctx, recurringPayment)
			if err != nil {
				logger.Error(ctx, "error while checking and creating mandate execution order",
					zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
				responseHeader.Status = domain.GetStatusFrom(err)
				return res, nil
			}
		}
	default:
		responseHeader.Status = domain.DomainProcessingStatus_SUCCESS
	}
	return res, nil
}

func (s *Service) getActionStatusFromDomainService(ctx context.Context, recurringPayment *pb.RecurringPayment,
	requestId string, action siPb.RequestType) error {
	switch recurringPayment.GetType() {
	case pb.RecurringPaymentType_STANDING_INSTRUCTION:
		res, err := s.siClient.GetActionStatus(ctx, &siPb.GetActionStatusRequest{
			RecurringPaymentId: recurringPayment.GetId(),
			RequestId:          requestId,
			Action:             action,
		})
		if te := epifigrpc.RPCError(res, err); te != nil {
			return fmt.Errorf("error while fetching action status from domain service %w", te)
		}
		switch res.GetState() {
		case siPb.State_SUCCESS:
			return nil
		case siPb.State_FAILED:
			return errActionFailed
		default:
			return fmt.Errorf("unexpected state for standing instruction action %w", domain.ErrTransient)
		}
	case pb.RecurringPaymentType_UPI_MANDATES:
		return s.checkMandateRequestStatus(ctx, requestId, recurringPayment)
	default:
		return fmt.Errorf("recurring payment type not supported %s %w", recurringPayment.GetType().String(), domain.ErrPermanent)
	}
}

func (s *Service) updateRecurringPaymentAndActionInTxnBlock(ctx context.Context, action *pb.RecurringPaymentsAction,
	recurringPayment *pb.RecurringPayment, updateRecurringPaymentFieldMask []pb.RecurringPaymentFieldMask,
	updateRecurringPaymentActionFieldMask []pb.RecurringPaymentActionFieldMask, recurringPaymentStateToUpdate pb.RecurringPaymentState,
	actionStateToUpdate pb.ActionState) error {
	txnErr := storagev2.RunCRDBTxn(ctx, func(txnCtx context.Context) error {
		if recurringPayment.GetState() != recurringPaymentStateToUpdate {
			err := s.recurringPaymentDao.UpdateAndChangeStatus(txnCtx, recurringPayment, updateRecurringPaymentFieldMask,
				recurringPayment.GetState(), recurringPaymentStateToUpdate)
			if err != nil {
				return fmt.Errorf("error while updating recurring payment state for recurring payment %w", err)
			}
		}
		if action.GetState() != actionStateToUpdate {
			err := s.recurringPaymentActionsDao.UpdateAndChangeStatus(txnCtx, action, updateRecurringPaymentActionFieldMask,
				action.GetState(), actionStateToUpdate)
			if err != nil {
				return fmt.Errorf("error in updating recurring payment action %w", err)
			}
		}
		return nil
	})
	if txnErr != nil {
		return fmt.Errorf("error in updating %w", txnErr)
	}
	return nil
}
