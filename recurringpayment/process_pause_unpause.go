package recurringpayment

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	domainPb "github.com/epifi/gamma/api/order/domain"
	pb "github.com/epifi/gamma/api/recurringpayment"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
)

// nolint: funlen
func (s *Service) ProcessPauseUnpause(ctx context.Context, req *domainPb.ProcessFulfilmentRequest) (*domainPb.ProcessFulfilmentResponse, error) {
	var (
		actionStateToUpdate pb.ActionState
		stateToUpdate       pb.RecurringPaymentState
	)
	res := &domainPb.ProcessFulfilmentResponse{}
	responseHeader := &domainPb.DomainResponseHeader{}
	res.ResponseHeader = responseHeader
	recurringPaymentPauseUnpauseInfo := &pb.RecurringPaymentPauseUnpauseInfo{}
	unmarshalOptions := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	if unmarshalErr := unmarshalOptions.Unmarshal(req.GetPayload(), recurringPaymentPauseUnpauseInfo); unmarshalErr != nil {
		logger.Error(ctx, "failed to unmarshal payload for recurring payment pause unpause", zap.Error(unmarshalErr))
		responseHeader.Status = domainPb.DomainProcessingStatus_PERMANENT_FAILURE
		return res, nil
	}
	recurringPaymentId := recurringPaymentPauseUnpauseInfo.GetRecurringPaymentId()
	clientRequestId := recurringPaymentPauseUnpauseInfo.GetClientRequestId()
	recurringPayment, err := s.recurringPaymentDao.GetById(ctx, recurringPaymentId)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "recurring payment record not found", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
		responseHeader.Status = domainPb.DomainProcessingStatus_PERMANENT_FAILURE
		return res, nil
	case err != nil:
		logger.Error(ctx, "error while fetching recurring payment", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId),
			zap.Error(err))
		responseHeader.Status = domainPb.DomainProcessingStatus_TRANSIENT_FAILURE
		return res, nil
	default:
		logger.Debug(ctx, "recurring payment entry fetched successfully")
	}
	recurringPaymentAction, err := s.recurringPaymentActionsDao.GetByClientRequestId(ctx, clientRequestId, false)
	if err != nil {
		logger.Error(ctx, "error in fetching recurringPayment action", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId), zap.String(logger.CLIENT_REQUEST_ID, clientRequestId))
		responseHeader.Status = domainPb.GetStatusFrom(err)
		return res, nil
	}
	switch recurringPaymentAction.GetAction() {
	case pb.Action_PAUSE:
		switch recurringPayment.GetState() {
		case pb.RecurringPaymentState_PAUSE_QUEUED:
			logger.Info(ctx, "recurring payment pause not initiated by user")
			err = s.checkAuthorisationThreshold(ctx, recurringPayment, recurringPaymentAction,
				pb.RecurringPaymentState_ACTIVATED)
			if err != nil {
				logger.Error(ctx, "error in checking authorisation threshold", zap.Error(err),
					zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
				responseHeader.Status = domainPb.GetStatusFrom(err)
				return res, nil
			}
			responseHeader.Status = domainPb.DomainProcessingStatus_NO_OP
			return res, nil
		case pb.RecurringPaymentState_PAUSED:
			responseHeader.Status = domainPb.DomainProcessingStatus_SUCCESS
			return res, nil
		case pb.RecurringPaymentState_ACTIVATED:
			responseHeader.Status = domainPb.DomainProcessingStatus_PERMANENT_FAILURE
			return res, nil
		case pb.RecurringPaymentState_PAUSE_INITIATED, pb.RecurringPaymentState_PAUSE_AUTHORISED:
			var (
				updateFieldMask []pb.RecurringPaymentFieldMask
			)
			err = s.getActionStatusFromDomainService(ctx, recurringPayment, recurringPaymentAction.GetVendorRequestId(),
				siPb.RequestType_REQUEST_TYPE_UNSPECIFIED)
			switch {
			case errors.Is(err, errActionFailed) || errors.Is(err, epifierrors.ErrPermanent):
				actionStateToUpdate = pb.ActionState_ACTION_FAILURE
				stateToUpdate = pb.RecurringPaymentState_ACTIVATED
				responseHeader.Status = domainPb.DomainProcessingStatus_PERMANENT_FAILURE
			case err != nil:
				logger.Error(ctx, "error in fetching status from domain service",
					zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId), zap.Error(err))
				responseHeader.Status = domainPb.GetStatusFrom(err)
				return res, nil
			default:
				if recurringPaymentAction.GetActionMetadata().GetPauseActionMetadata().GetPauseInterval() != nil {
					updateFieldMask = append(updateFieldMask, pb.RecurringPaymentFieldMask_PAUSE_INTERVAL)
					recurringPayment.PauseInterval = recurringPaymentAction.GetActionMetadata().GetPauseActionMetadata().GetPauseInterval()
					// If start time for pause interval is greater than current time then we will update the state to TO_BE_PAUSED
					// otherwise we will update it to paused
					if time.Since(recurringPaymentAction.GetActionMetadata().GetPauseActionMetadata().GetPauseInterval().GetStartTime().AsTime()) > 0 {
						stateToUpdate = pb.RecurringPaymentState_PAUSED
					} else {
						stateToUpdate = pb.RecurringPaymentState_TO_BE_PAUSED
					}
				}
				stateToUpdate = pb.RecurringPaymentState_PAUSED
				actionStateToUpdate = pb.ActionState_ACTION_SUCCESS
				responseHeader.Status = domainPb.DomainProcessingStatus_SUCCESS
			}
			err = s.updateRecurringPaymentAndActionInTxnBlock(ctx, recurringPaymentAction, recurringPayment,
				updateFieldMask, nil, stateToUpdate, actionStateToUpdate)
			if err != nil {
				logger.Error(ctx, "error in updating recurring payment and action", zap.Error(err),
					zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
				responseHeader.Status = domainPb.GetStatusFrom(err)
				return res, nil
			}
			return res, nil
		default:
			responseHeader.Status = domainPb.DomainProcessingStatus_PERMANENT_FAILURE
			return res, nil
		}
	case pb.Action_UNPAUSE:
		switch recurringPayment.GetState() {
		case pb.RecurringPaymentState_UNPAUSE_QUEUED:
			logger.Info(ctx, "recurring payment un pause not initiated by user")
			err = s.checkAuthorisationThreshold(ctx, recurringPayment, recurringPaymentAction,
				pb.RecurringPaymentState_PAUSED)
			if err != nil {
				logger.Error(ctx, "error in checking authorisation threshold", zap.Error(err),
					zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
				responseHeader.Status = domainPb.GetStatusFrom(err)
				return res, nil
			}
			responseHeader.Status = domainPb.DomainProcessingStatus_NO_OP
			return res, nil
		case pb.RecurringPaymentState_ACTIVATED:
			responseHeader.Status = domainPb.DomainProcessingStatus_SUCCESS
			return res, nil
		case pb.RecurringPaymentState_PAUSED:
			responseHeader.Status = domainPb.DomainProcessingStatus_PERMANENT_FAILURE
			return res, nil
		case pb.RecurringPaymentState_UNPAUSE_INITIATED, pb.RecurringPaymentState_UNPAUSE_AUTHORISED:
			var (
				updateFieldMask []pb.RecurringPaymentFieldMask
			)
			err = s.getActionStatusFromDomainService(ctx, recurringPayment, recurringPaymentAction.GetVendorRequestId(),
				siPb.RequestType_REQUEST_TYPE_UNSPECIFIED)
			switch {
			case errors.Is(err, errActionFailed) || errors.Is(err, epifierrors.ErrPermanent):
				actionStateToUpdate = pb.ActionState_ACTION_FAILURE
				stateToUpdate = pb.RecurringPaymentState_PAUSED
				responseHeader.Status = domainPb.DomainProcessingStatus_PERMANENT_FAILURE
			case err != nil:
				logger.Error(ctx, "error in fetching status from domain service",
					zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId), zap.Error(err))
				responseHeader.Status = domainPb.GetStatusFrom(err)
				return res, nil
			default:
				if recurringPayment.GetPauseInterval() != nil {
					recurringPayment.PauseInterval = nil
					updateFieldMask = append(updateFieldMask, pb.RecurringPaymentFieldMask_PAUSE_INTERVAL)
				}
				actionStateToUpdate = pb.ActionState_ACTION_SUCCESS
				stateToUpdate = pb.RecurringPaymentState_ACTIVATED
				responseHeader.Status = domainPb.DomainProcessingStatus_SUCCESS
			}
			err = s.updateRecurringPaymentAndActionInTxnBlock(ctx, recurringPaymentAction, recurringPayment,
				updateFieldMask, nil, stateToUpdate, actionStateToUpdate)
			if err != nil {
				logger.Error(ctx, "error in updating recurring payment and action", zap.Error(err),
					zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
				responseHeader.Status = domainPb.GetStatusFrom(err)
				return res, nil
			}
			return res, nil
		default:
			responseHeader.Status = domainPb.DomainProcessingStatus_PERMANENT_FAILURE
			return res, nil
		}
	default:
		logger.Error(ctx, "unexpected action", zap.String(logger.ACTION_TYPE,
			recurringPaymentAction.GetAction().String()), zap.Error(err))
		responseHeader.Status = domainPb.DomainProcessingStatus_PERMANENT_FAILURE
		return res, nil
	}
}

// checkAuthorisationThreshold fetches the threshold time from config if authorisation is not initiated by user and roll backs the
// state to previous state if time is greater than threshold time for an action
func (s *Service) checkAuthorisationThreshold(ctx context.Context,
	recurringPayment *pb.RecurringPayment,
	recurringPaymentAction *pb.RecurringPaymentsAction,
	recurringPaymentStateToUpdate pb.RecurringPaymentState,
) error {
	thresholdTime, ok := (*s.config.AuthenticationTimeLimitForAction)[recurringPayment.GetType().String()][recurringPayment.GetInitiatedBy().String()][recurringPaymentAction.GetAction().String()]
	if !ok {
		return fmt.Errorf("error in fetching threshold time for action %s %w", recurringPaymentAction.GetAction().String(), domainPb.ErrPermanent)
	}
	if time.Since(recurringPaymentAction.GetCreatedAt().AsTime()) > thresholdTime {
		logger.Info(ctx, "exceeded threshold time for initiating action", zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		err := s.updateRecurringPaymentAndActionInTxnBlock(ctx, recurringPaymentAction, recurringPayment,
			nil, nil, recurringPaymentStateToUpdate, pb.ActionState_ACTION_FAILURE)
		if err != nil {
			return fmt.Errorf("error in updating recurring payment and action %w", err)
		}
		return domainPb.ErrPermanent
	}
	return nil
}
