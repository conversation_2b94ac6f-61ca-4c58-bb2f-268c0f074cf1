// nolint: goimports
package recurringpayment

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/epificontext"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/emptypb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	"github.com/epifi/gamma/api/frontend/deeplink"
	pb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/pkg/pay"
	"github.com/epifi/gamma/recurringpayment/helper"
	"github.com/epifi/gamma/recurringpayment/internal/actionstatusfetcher"
)

// InitiateCreationAuthorizationV1 rpc is useful to initiate authorization for recurring payment creation,
// it returns deeplink to the screen where the user needs to authorize the recurring payment creation like FEDERAL_SECURE_PIN screen, REDIRECTION_URL deeplink for enach authorization.
func (s *Service) InitiateCreationAuthorizationV1(ctx context.Context, req *pb.InitiateCreationAuthorizationV1Request) (*pb.InitiateCreationAuthorizationV1Response, error) {
	// get recurring payment create action from the client request id
	action, err := s.recurringPaymentActionsDao.GetByClientRequestId(ctx, req.GetClientRequestId(), true)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "no recurring payment action found with given clientRequestId", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()))
		return &pb.InitiateCreationAuthorizationV1Response{Status: rpc.StatusFailedPreconditionWithDebugMsg("no recurring payment action found with given clientRequestId")}, nil
	case err != nil:
		logger.Error(ctx, "error fetching recurring payment action from db by client req id", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(err))
		return &pb.InitiateCreationAuthorizationV1Response{Status: rpc.StatusInternalWithDebugMsg("error fetching recurring payment action from db")}, nil
	}

	// get recurring payment by id
	recurringPayment, err := s.recurringPaymentDao.GetById(ctx, action.GetRecurringPaymentId())
	if err != nil {
		logger.Error(ctx, "error fetching recurring payment from db using id", zap.String(logger.RECURRING_PAYMENT_ID, action.GetRecurringPaymentId()), zap.Error(err))
		return &pb.InitiateCreationAuthorizationV1Response{Status: rpc.StatusInternalWithDebugMsg("error fetching recurring payment by id from db")}, nil
	}
	ctx = epificontext.WithOwnership(ctx, recurringPayment.GetEntityOwnership())

	// get current action status from action status fetcher to validate is initiate authorization is allowed or not
	actionStatusFetcher := s.actionStatusFetcherFactory.GetActionStatusFetcher(ctx, action.GetAction(), recurringPayment.GetType(), recurringPayment.GetPaymentRoute())
	if actionStatusFetcher == nil {
		logger.Error(ctx, "no action status fetched exists for action and recurring payment type", zap.String(logger.ACTION_TYPE, action.GetAction().String()), zap.String(logger.RECURRING_PAYMENT_TYPE, recurringPayment.GetType().String()))
		return &pb.InitiateCreationAuthorizationV1Response{Status: rpc.StatusInternalWithDebugMsg("no action status fetched exists for action and recurring payment type")}, nil
	}

	// get action status, sub status and next step deeplink
	actionStatus, actionSubStatus, _, _, err := actionStatusFetcher.GetActionStatusAndNextStepDeeplink(ctx, recurringPayment, action, &actionstatusfetcher.Metadata{})
	if err != nil {
		logger.Error(ctx, "error fetching status for action", zap.String(logger.ACTION_ID, action.GetId()), zap.Error(err))
		return &pb.InitiateCreationAuthorizationV1Response{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	// here not failing the request even if the auth is already initiated, as the user can retry the auth initiation in case of drop offs.
	// Adding this here to unblock user who retry auth after drop offs.
	if actionStatus != pb.ActionState_ACTION_IN_PROGRESS || (actionSubStatus != pb.ActionSubState_ACTION_SUB_STATE_BLOCKED_ON_AUTHORIZATION && actionSubStatus != pb.ActionSubState_ACTION_SUB_STATE_AUTHORIZATION_INITIATED) {
		logger.Error(ctx, "can't initiate authorization, recurring payment is not blocked on authorization", zap.String(logger.ACTION_ID, action.GetId()), zap.String("actionStatus", actionStatus.String()), zap.String("actionSubStatus", actionSubStatus.String()))
		return &pb.InitiateCreationAuthorizationV1Response{Status: rpc.StatusFailedPreconditionWithDebugMsg("can't initiate authorization, recurring payment is not blocked on authorization")}, nil
	}

	// generate domain specific authorization deeplink
	authDeeplink, err := s.getDomainSpecificCreationAuthDeeplink(ctx, recurringPayment.GetId(), recurringPayment.GetType(), recurringPayment.GetPaymentRoute())
	if err != nil {
		logger.Error(ctx, "error fetching domain specific auth deeplink", zap.String(logger.ID, recurringPayment.GetId()), zap.String(logger.RECURRING_PAYMENT_TYPE, recurringPayment.GetType().String()), zap.Error(err))
		return &pb.InitiateCreationAuthorizationV1Response{Status: rpc.StatusInternalWithDebugMsg("error fetching domain specific auth deeplink")}, nil
	}

	// send auth signal to creation workflow
	workflowClientReqId := action.GetClientRequestId()
	if sendSignalErr := s.sendAuthorizationInitSignalToCreationV1Workflow(ctx, workflowClientReqId); sendSignalErr != nil {
		logger.Error(ctx, "error sending auth initiated signal to workflow", zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()), zap.Error(sendSignalErr))
		return &pb.InitiateCreationAuthorizationV1Response{Status: rpc.StatusInternalWithDebugMsg("error sending auth initiated signal to creation workflow")}, nil
	}

	pollingScreenDeeplink := helper.GetRecurringPaymentActionPollingScreenDeeplink(action.GetClientRequestId(), 2*time.Second, 0)

	return &pb.InitiateCreationAuthorizationV1Response{
		Status:              rpc.StatusOk(),
		AuthDeeplink:        authDeeplink,
		PostAuthRedirection: pollingScreenDeeplink,
	}, nil
}

func (s *Service) getDomainSpecificCreationAuthDeeplink(ctx context.Context, recurringPaymentId string, recurringPaymentType pb.RecurringPaymentType, recurringPaymentRoute pb.RecurringPaymentRoute) (*deeplink.Deeplink, error) {
	// generate domain specific authorization deeplink
	domainProcessor, err := s.domainCreationProcessorFactory.GetProcessor(recurringPaymentType, recurringPaymentRoute)
	if err != nil {
		return nil, fmt.Errorf("error fetching domain processor for recurring payment type, %w", err)
	}
	authDeeplink, err := domainProcessor.GetAuthorizationDeeplink(ctx, recurringPaymentId)
	if err != nil {
		return nil, fmt.Errorf("error fetching auth deeplink, %w", err)
	}
	return authDeeplink, nil
}

func (s *Service) sendAuthorizationInitSignalToCreationV1Workflow(ctx context.Context, workflowReqId string) error {
	authorizationInitSignalPayload, marshalErr := protojson.Marshal(&emptypb.Empty{})
	if marshalErr != nil {
		return fmt.Errorf("error marshalling auth init signal payload, %w", marshalErr)
	}

	signalWorkflowResp, err := s.celestialClient.SignalWorkflow(ctx, &celestialPb.SignalWorkflowRequest{
		Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
			ClientReqId: &celestialPb.ClientReqId{
				Id:     workflowReqId,
				Client: workflowPb.Client_RECURRING_PAYMENT,
			},
		},
		SignalId:         string(rpNs.CreateRecurringPaymentV1AuthorisationInitiatedSignal),
		Payload:          authorizationInitSignalPayload,
		QualityOfService: celestialPb.QoS_BEST_EFFORT,
		Ownership:        epificontext.OwnershipFromContext(ctx),
		UseCase:          pay.OwnershipToUseCaseForPay[epificontext.OwnershipFromContext(ctx)],
	})
	if rpcErr := epifigrpc.RPCError(signalWorkflowResp, err); rpcErr != nil {
		return fmt.Errorf("error sending auth initiated signal to creation workflow, %w", rpcErr)
	}
	return nil
}
