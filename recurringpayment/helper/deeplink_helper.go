package helper

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"time"

	"google.golang.org/protobuf/types/known/durationpb"

	"github.com/epifi/gamma/api/frontend/deeplink"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/recurringpayment"
	salaryScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/salaryprogram"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
)

func GetRecurringPaymentActionPollingScreenDeeplink(clientReqId string, pollDelay time.Duration, pollAttempt int32) *deeplink.Deeplink {
	pollingScreenOptions, _ := deeplinkV3.GetScreenOptionV2(&rpScreenOptionsPb.RecurringPaymentPollingScreenOptions{
		ClientReqId: clientReqId,
		PollDelay:   durationpb.New(pollDelay),
		PollAttempt: pollAttempt,
		PollingText: &deeplink.InfoItemV2{
			// todo (harleen) : update this text once design is finalized
			Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Processing...."}},
		},
	})
	return &deeplink.Deeplink{
		Screen:          deeplink.Screen_RECURRING_PAYMENT_POLLING_SCREEN,
		ScreenOptionsV2: pollingScreenOptions,
	}
}

// GetRecurringPaymentActionHaultScreen is used to return the hault screen to the client post an action taken on the recurring payment
// it will redirect to the deeplink given by the respective domain service
// that deeplink will take care to fetch the status of the RP and based upon that show success/failures/in_progress screens
func GetRecurringPaymentActionHaultScreen(actionEntryPoint rpPb.UIEntryPoint, clientReqId string, defaultScreen *deeplink.Deeplink) *deeplink.Deeplink {
	if actionEntryPoint == rpPb.UIEntryPoint_SALARY_LITE {
		screenOptions, _ := deeplinkV3.GetScreenOptionV2(&salaryScreenOptionsPb.SalaryLiteMandateStatusScreenOptions{
			ClientRequestId: clientReqId,
		})
		return &deeplink.Deeplink{
			Screen:          deeplink.Screen_SALARY_LITE_MANDATE_STATUS_SCREEN,
			ScreenOptionsV2: screenOptions,
		}
	}
	return defaultScreen
}
