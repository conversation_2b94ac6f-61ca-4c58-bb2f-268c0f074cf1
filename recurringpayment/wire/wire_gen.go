// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"fmt"
	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/pkg/storage/v2/usecase"
	genconf2 "github.com/epifi/be-common/pkg/vendorapi/config/genconf"
	"github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/card/debitcardmandate"
	"github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/investment/mutualfund/catalog"
	"github.com/epifi/gamma/api/merchant"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/recurringpayment/enach"
	paymentgateway2 "github.com/epifi/gamma/api/recurringpayment/paymentgateway"
	"github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/timeline"
	"github.com/epifi/gamma/api/tspuser"
	"github.com/epifi/gamma/api/upcomingtransactions"
	"github.com/epifi/gamma/api/upi"
	"github.com/epifi/gamma/api/upi/mandate"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	catalog2 "github.com/epifi/gamma/api/usstocks/catalog"
	enach3 "github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
	"github.com/epifi/gamma/api/vendorgateway/pg"
	types6 "github.com/epifi/gamma/order/wire/types"
	dao2 "github.com/epifi/gamma/pay/dao"
	"github.com/epifi/gamma/pkg/feature/release"
	genconf4 "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	"github.com/epifi/gamma/pkg/vendorapi/inhouse/wire"
	recurringpayment2 "github.com/epifi/gamma/recurringpayment"
	"github.com/epifi/gamma/recurringpayment/activity"
	"github.com/epifi/gamma/recurringpayment/config/commons"
	"github.com/epifi/gamma/recurringpayment/config/server"
	"github.com/epifi/gamma/recurringpayment/config/server/genconf"
	"github.com/epifi/gamma/recurringpayment/config/worker"
	genconf3 "github.com/epifi/gamma/recurringpayment/config/worker/genconf"
	"github.com/epifi/gamma/recurringpayment/consumer"
	"github.com/epifi/gamma/recurringpayment/cx"
	"github.com/epifi/gamma/recurringpayment/dao"
	"github.com/epifi/gamma/recurringpayment/developer"
	"github.com/epifi/gamma/recurringpayment/developer/processor"
	enach2 "github.com/epifi/gamma/recurringpayment/enach"
	activity2 "github.com/epifi/gamma/recurringpayment/enach/activity"
	consumer2 "github.com/epifi/gamma/recurringpayment/enach/consumer"
	dao3 "github.com/epifi/gamma/recurringpayment/enach/dao"
	developer2 "github.com/epifi/gamma/recurringpayment/enach/developer"
	processor2 "github.com/epifi/gamma/recurringpayment/enach/developer/processor"
	"github.com/epifi/gamma/recurringpayment/enach/enachvendor"
	"github.com/epifi/gamma/recurringpayment/internal"
	"github.com/epifi/gamma/recurringpayment/internal/actionstatusfetcher"
	actor2 "github.com/epifi/gamma/recurringpayment/internal/actor"
	celestial2 "github.com/epifi/gamma/recurringpayment/internal/celestial"
	"github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor"
	"github.com/epifi/gamma/recurringpayment/internal/domainexecutionprocessor"
	"github.com/epifi/gamma/recurringpayment/internal/domainrevokeprocessor"
	"github.com/epifi/gamma/recurringpayment/internal/executeRPNoAuth"
	upi2 "github.com/epifi/gamma/recurringpayment/internal/upi"
	user2 "github.com/epifi/gamma/recurringpayment/internal/user"
	"github.com/epifi/gamma/recurringpayment/internal/validationrules"
	"github.com/epifi/gamma/recurringpayment/paymentgateway"
	standinginstruction2 "github.com/epifi/gamma/recurringpayment/standinginstruction"
	types5 "github.com/epifi/gamma/recurringpayment/types"
	types2 "github.com/epifi/gamma/recurringpayment/wire/types"
	types3 "github.com/epifi/gamma/upi/wire/types"
	types4 "github.com/epifi/gamma/vendorgateway/wire/types"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitialisePaymentGatewayService(conf *server.Config, vgPgClient pg.PaymentGatewayClient, payClient pay.PayClient, orderClient order.OrderServiceClient, db types.EpifiCRDB, userClient user.UsersClient, celestialClient celestial.CelestialClient, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, client paymentinstrument.PiClient, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], usecaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB], tspUserClient tspuser.TspUserServiceClient, groupClient group.GroupClient, actorClient actor.ActorClient, enachClient enach.EnachServiceClient, dynConf *genconf.Config) *paymentgateway.Service {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	enableResourceProvider := DbResourceProviderEnableFlag(dynConf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	recurringPaymentDaoCRDB := dao.NewRecurringPaymentDao(db, domainIdGenerator, dbResourceProvider, enableResourceProvider, attributedIdGen)
	orderVendorOrderMapDaoCRDB := dao2.NewOrderVendorOrderMapDao(db)
	recurringPaymentsActionDaoCRDB := dao.NewRecurringPaymentsActionDao(db, attributedIdGen, dbResourceProvider, enableResourceProvider, orderVendorOrderMapDaoCRDB)
	recurringPaymentsVendorDetailsDaoCRDB := dao.NewRecurringPaymentsVendorDetailsDao(domainIdGenerator, usecaseDbResourceProvider)
	celestialProcessor := celestial2.NewCelestialProcessor(celestialClient, conf)
	nbfcUserProcessor := user2.NewNbfcUserProcessor(tspUserClient, actorClient)
	userProcessor := user2.NewUserProcessor(userClient, groupClient)
	userProcessorFactory := user2.NewUserProcessorFactory(nbfcUserProcessor, userProcessor)
	service := paymentgateway.NewService(conf, dynConf, vgPgClient, payClient, enachClient, recurringPaymentDaoCRDB, recurringPaymentsActionDaoCRDB, orderClient, userClient, recurringPaymentsVendorDetailsDaoCRDB, celestialProcessor, recurringPaymentClient, client, userProcessorFactory, clock)
	return service
}

func InitialiseRecurringPaymentService(db types.EpifiCRDB, siClient standinginstruction.StandingInstructionServiceClient, orderClient order.OrderServiceClient, savingsClient savings.SavingsClient, actorClient actor.ActorClient, authClient auth.AuthClient, userClient user.UsersClient, paymentClient payment.PaymentClient, config *server.Config, upiMandateClient mandate.MandateServiceClient, accountPIRelationsClient account_pi.AccountPIRelationClient, piClient paymentinstrument.PiClient, timelineClient timeline.TimelineServiceClient, upiClient upi.UPIClient, commsClient types2.RecPaymentCommsClientWithInterceptors, genConf *genconf.Config, celestialClient celestial.CelestialClient, recurringPaymentLockRedisStore types3.UpiRedisStore, inPaymentOrderPublisher types2.InPaymentOrderUpdatePublisher, userGrpClient group.GroupClient, enachClient enach.EnachServiceClient, accountBalanceClient balance.BalanceClient, enachVgClient types4.VgEnachClientWithInterceptors, fetchAndCreateOffAppRecurringPaymentPublisher types2.FetchAndCreateOffAppRecurringPaymentPublisher, operationStatusClient operstatus.OperationalStatusServiceClient, upcomingTxnClient upcomingtransactions.UpcomingTransactionsClient, merchantClient merchant.MerchantServiceClient, catalogManagerClient catalog.CatalogManagerClient, depositClient deposit.DepositClient, pgServiceClient paymentgateway2.PaymentGatewayServiceClient, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], usecaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB], tspUserClient tspuser.TspUserServiceClient, payClient pay.PayClient, eventBroker events.Broker, usStocksCatalogClient catalog2.CatalogManagerClient) *recurringpayment2.Service {
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	enableResourceProvider := DbResourceProviderEnableFlag(genConf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	recurringPaymentDaoCRDB := dao.NewRecurringPaymentDao(db, domainIdGenerator, dbResourceProvider, enableResourceProvider, attributedIdGen)
	orderVendorOrderMapDaoCRDB := dao2.NewOrderVendorOrderMapDao(db)
	recurringPaymentsActionDaoCRDB := dao.NewRecurringPaymentsActionDao(db, attributedIdGen, dbResourceProvider, enableResourceProvider, orderVendorOrderMapDaoCRDB)
	processor := internal.NewProcessor(actorClient, recurringPaymentsActionDaoCRDB, recurringPaymentDaoCRDB)
	commsCommsClient := types2.CommsClientProvider(commsClient)
	siExecutionParams := SIExecutionParamsProvider(genConf)
	featureFlags := FeatureFlagsProvider(genConf)
	celestialProcessor := celestial2.NewCelestialProcessor(celestialClient, config)
	client := provideRecurringPaymentLockRedisClient(recurringPaymentLockRedisStore)
	redisRwLock := lock.NewRedisRwLock(client, clock)
	enachCreateActionStatusFetcher := actionstatusfetcher.NewEnachCreateActionStatusFetcher(enachClient, celestialClient)
	enachExecuteActionStatusFetcher := actionstatusfetcher.NewEnachExecuteActionStatusFetcher()
	defaultCreationActionStatusFetcher := actionstatusfetcher.NewDefaultCreationActionStatusFetcher(celestialClient)
	defaultExecutionActionStatusFetcher := actionstatusfetcher.NewDefaultExecutionActionStatusFetcher()
	pgEmandateExecutionActionStatusFetcher := actionstatusfetcher.NewPgEmandateExecutionActionStatusFetcher(defaultExecutionActionStatusFetcher)
	pgUpiCreationActionStatusFetcher := actionstatusfetcher.NewPgUpiCreationActionStatusFetcher(defaultCreationActionStatusFetcher)
	pgUpiExecutionActionStatusFetcher := actionstatusfetcher.NewPgUpiExecutionActionStatusFetcher(defaultExecutionActionStatusFetcher)
	pgEmandateCreationActionStatusFetcher := actionstatusfetcher.NewPgEmandateCreationActionStatusFetcher(defaultCreationActionStatusFetcher)
	factory := actionstatusfetcher.NewFactory(enachCreateActionStatusFetcher, enachExecuteActionStatusFetcher, defaultCreationActionStatusFetcher, defaultExecutionActionStatusFetcher, pgEmandateExecutionActionStatusFetcher, pgUpiCreationActionStatusFetcher, pgUpiExecutionActionStatusFetcher, pgEmandateCreationActionStatusFetcher)
	enachCreationValidationConfig := provideEnachCreationValidationConfig(config)
	enachMandateCreationProcessor := domaincreationprocessor.NewENachMandatesProcessor(enachCreationValidationConfig, enachClient)
	pgEmandateProcessor := domaincreationprocessor.NewPgEmandateProcessor(pgServiceClient, recurringPaymentsActionDaoCRDB, recurringPaymentDaoCRDB, piClient, payClient)
	pgUpiProcessor := domaincreationprocessor.NewPgUpiProcessor(pgServiceClient, recurringPaymentsActionDaoCRDB, recurringPaymentDaoCRDB, payClient)
	domainCreationProcessorFactoryImpl := domaincreationprocessor.NewDomainCreationProcessorFactoryImpl(enachMandateCreationProcessor, pgEmandateProcessor, pgUpiProcessor)
	enachEnachClient := types4.VgEnachClientProvider(enachVgClient)
	enachMandateExecutionProcessor := domainexecutionprocessor.NewEnachMandateExecutionProcessor(enachClient)
	recurringPaymentsVendorDetailsDaoCRDB := dao.NewRecurringPaymentsVendorDetailsDao(domainIdGenerator, usecaseDbResourceProvider)
	domainexecutionprocessorPgUpiProcessor := domainexecutionprocessor.NewPgUpiProcessor(recurringPaymentsActionDaoCRDB, recurringPaymentDaoCRDB, pgServiceClient, recurringPaymentsVendorDetailsDaoCRDB)
	domainexecutionprocessorPgEmandateProcessor := domainexecutionprocessor.NewPgEmandateProcessor(recurringPaymentsActionDaoCRDB, recurringPaymentDaoCRDB, pgServiceClient, recurringPaymentsVendorDetailsDaoCRDB)
	domainExecutionProcessoryFactoryImpl := domainexecutionprocessor.NewDomainExecutionProcessoryFactoryImpl(enachMandateExecutionProcessor, domainexecutionprocessorPgUpiProcessor, domainexecutionprocessorPgEmandateProcessor)
	iValidationRule := validationrules.InitExecutionValidationChain(recurringPaymentsActionDaoCRDB, operationStatusClient, domainExecutionProcessoryFactoryImpl, accountPIRelationsClient, accountBalanceClient)
	upiProcessor := upi2.NewUpiProcessor(config)
	service := recurringpayment2.NewService(recurringPaymentDaoCRDB, orderClient, siClient, recurringPaymentsActionDaoCRDB, savingsClient, actorClient, authClient, userClient, paymentClient, config, processor, upiMandateClient, accountPIRelationsClient, piClient, timelineClient, upiClient, commsCommsClient, siExecutionParams, featureFlags, celestialProcessor, redisRwLock, inPaymentOrderPublisher, factory, enachClient, domainCreationProcessorFactoryImpl, accountBalanceClient, enachEnachClient, celestialClient, fetchAndCreateOffAppRecurringPaymentPublisher, domainExecutionProcessoryFactoryImpl, iValidationRule, upcomingTxnClient, merchantClient, catalogManagerClient, depositClient, recurringPaymentsVendorDetailsDaoCRDB, upiProcessor, txnExecutorProvider, eventBroker, usStocksCatalogClient)
	return service
}

// todo(Utkarsh): Add wireset for IFactory and fix enach service initiation
func InitialiseRecurringPaymentConsumerService(rpClient recurringpayment.RecurringPaymentServiceClient, savingsClient savings.SavingsClient, enachVgClient types4.VgEnachClientWithInterceptors, piClient paymentinstrument.PiClient, enachClient enach.EnachServiceClient, usersClient user.UsersClient, groupClient group.GroupClient, db types.EpifiCRDB, actorClient actor.ActorClient, merchantClient merchant.MerchantServiceClient, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], conf *server.Config, dynConf *genconf.Config, eventsBroker events.Broker, cpClient provisioning.CardProvisioningClient, dcMandateClient debitcardmandate.DebitCardMandateServiceClient, timelineClient timeline.TimelineServiceClient, orderClient order.OrderServiceClient, vendorapiGenConf *genconf2.Config, publisher types2.FetchAndCreateFailedEnachTransactionPublisher) *consumer.ConsumerService {
	enachEnachClient := types4.VgEnachClientProvider(enachVgClient)
	actorProcessor := actor2.NewActorProcessor(actorClient, piClient, merchantClient)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	enableResourceProvider := DbResourceProviderEnableFlag(dynConf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	recurringPaymentDaoCRDB := dao.NewRecurringPaymentDao(db, domainIdGenerator, dbResourceProvider, enableResourceProvider, attributedIdGen)
	orderVendorOrderMapDaoCRDB := dao2.NewOrderVendorOrderMapDao(db)
	recurringPaymentsActionDaoCRDB := dao.NewRecurringPaymentsActionDao(db, attributedIdGen, dbResourceProvider, enableResourceProvider, orderVendorOrderMapDaoCRDB)
	uuidGenerator := idgen.NewUuidGenerator()
	string2 := envProvider(conf)
	service := wire.InitializeMerchantResolutionService(vendorapiGenConf, string2)
	featureReleaseConfig := FeatureReleaseConfigProvider(dynConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, usersClient, groupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	consumerService := consumer.NewConsumerService(dynConf, rpClient, savingsClient, enachEnachClient, piClient, actorProcessor, enachClient, recurringPaymentDaoCRDB, recurringPaymentsActionDaoCRDB, eventsBroker, cpClient, dcMandateClient, actorClient, merchantClient, timelineClient, orderClient, uuidGenerator, service, publisher, clock, evaluator)
	return consumerService
}

func InitialiseStandingInstructionService(db types.EpifiCRDB, siVGClient types4.SIVGClientWithInterceptors, piClient paymentinstrument.PiClient, authClient auth.AuthClient, savingsClient savings.SavingsClient, userClient user.UsersClient, actorClient actor.ActorClient, vgPaymentClient types4.VgPaymentClientWithInterceptors, client bankcust.BankCustomerServiceClient, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], conf *server.Config, dynConf *genconf.Config) *standinginstruction2.Service {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	standingInstructionDaoCRDB := dao.NewStandingInstructionDao(db, domainIdGenerator)
	standingInstructionClient := types4.SIVGClientProvider(siVGClient)
	enableResourceProvider := DbResourceProviderEnableFlag(dynConf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	recurringPaymentDaoCRDB := dao.NewRecurringPaymentDao(db, domainIdGenerator, dbResourceProvider, enableResourceProvider, attributedIdGen)
	standingInstructionRequestDaoCRDB := dao.NewStandingInstructionRequestDao(db)
	paymentClient := types4.VgPaymentClientProvider(vgPaymentClient)
	service := standinginstruction2.NewService(standingInstructionDaoCRDB, standingInstructionClient, piClient, savingsClient, authClient, recurringPaymentDaoCRDB, userClient, actorClient, standingInstructionRequestDaoCRDB, paymentClient, client)
	return service
}

func InitialiseDevService(db types.EpifiCRDB, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], usecaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB], conf *server.Config, config *genconf.Config) *developer.RecurringPaymentDevService {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	enableResourceProvider := DbResourceProviderEnableFlag(config)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	recurringPaymentDaoCRDB := dao.NewRecurringPaymentDao(db, domainIdGenerator, dbResourceProvider, enableResourceProvider, attributedIdGen)
	devRecurringPaymentEntity := processor.NewDevRecurringPaymentEntity(recurringPaymentDaoCRDB)
	orderVendorOrderMapDaoCRDB := dao2.NewOrderVendorOrderMapDao(db)
	recurringPaymentsActionDaoCRDB := dao.NewRecurringPaymentsActionDao(db, attributedIdGen, dbResourceProvider, enableResourceProvider, orderVendorOrderMapDaoCRDB)
	devRecurringPaymentActionEntity := processor.NewDevRecurringPaymentActionEntity(recurringPaymentsActionDaoCRDB)
	recurringPaymentsVendorDetailsDaoCRDB := dao.NewRecurringPaymentsVendorDetailsDao(domainIdGenerator, usecaseDbResourceProvider)
	devRecurringPaymentsVendorDetailsEntity := processor.NewDevRecurringPaymentsVendorDetailsEntity(recurringPaymentsVendorDetailsDaoCRDB)
	standingInstructionDaoCRDB := dao.NewStandingInstructionDao(db, domainIdGenerator)
	devSIEntity := processor.NewDevSIEntity(standingInstructionDaoCRDB)
	standingInstructionRequestDaoCRDB := dao.NewStandingInstructionRequestDao(db)
	devSIRequestEntity := processor.NewDevSIRequestEntity(standingInstructionRequestDaoCRDB)
	devFactory := developer.NewDevFactory(devRecurringPaymentEntity, devRecurringPaymentActionEntity, devRecurringPaymentsVendorDetailsEntity, devSIEntity, devSIRequestEntity)
	recurringPaymentDevService := developer.NewRecurringPaymentDevService(devFactory)
	return recurringPaymentDevService
}

func InitialiseCxService(db types.EpifiCRDB, orderClient order.OrderServiceClient, conf *server.Config, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], dynConf *genconf.Config) *cx.Service {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	enableResourceProvider := DbResourceProviderEnableFlag(dynConf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	recurringPaymentDaoCRDB := dao.NewRecurringPaymentDao(db, domainIdGenerator, dbResourceProvider, enableResourceProvider, attributedIdGen)
	orderVendorOrderMapDaoCRDB := dao2.NewOrderVendorOrderMapDao(db)
	recurringPaymentsActionDaoCRDB := dao.NewRecurringPaymentsActionDao(db, attributedIdGen, dbResourceProvider, enableResourceProvider, orderVendorOrderMapDaoCRDB)
	service := cx.NewService(recurringPaymentDaoCRDB, recurringPaymentsActionDaoCRDB, orderClient)
	return service
}

func InitializeActivityProcessor(db types.EpifiCRDB, rpClient recurringpayment.RecurringPaymentServiceClient, actorClient actor.ActorClient, config *worker.Config, orderClient order.OrderServiceClient, siClient standinginstruction.StandingInstructionServiceClient, payClient pay.PayClient, paymentClient payment.PaymentClient, EnachClient enach.EnachServiceClient, commsClient comms.CommsClient, recurringPaymentActionUpdatePublisher types5.RecurringPaymentActionUpdateEventPublisher, piClient paymentinstrument.PiClient, merchantClient merchant.MerchantServiceClient, enachCreationValidationConfig *commons.EnachCreationValidationConfig, pgServiceClient paymentgateway2.PaymentGatewayServiceClient, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], usecaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB], dynConf *genconf3.Config) *activity.Processor {
	clock := idgen.NewClock()
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	enableResourceProvider := WorkerResourceProviderEnableFlag(dynConf)
	orderVendorOrderMapDaoCRDB := dao2.NewOrderVendorOrderMapDao(db)
	recurringPaymentsActionDaoCRDB := dao.NewRecurringPaymentsActionDao(db, attributedIdGen, dbResourceProvider, enableResourceProvider, orderVendorOrderMapDaoCRDB)
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	recurringPaymentDaoCRDB := dao.NewRecurringPaymentDao(db, domainIdGenerator, dbResourceProvider, enableResourceProvider, attributedIdGen)
	internalProcessor := internal.NewProcessor(actorClient, recurringPaymentsActionDaoCRDB, recurringPaymentDaoCRDB)
	actorProcessor := actor2.NewActorProcessor(actorClient, piClient, merchantClient)
	gormDB := GormProvider(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	standingInstructionProcessor := executeRPNoAuth.NewStandingInstructionProcessor(siClient)
	upiMandatesProcessor := executeRPNoAuth.NewUPIMandatesProcessor()
	executeRecurringPaymentNoAuthFactoryImpl := executeRPNoAuth.NewExecuteRecurringPaymentNoAuthFactoryImpl(standingInstructionProcessor, upiMandatesProcessor)
	enachMandateCreationProcessor := domaincreationprocessor.NewENachMandatesProcessor(enachCreationValidationConfig, EnachClient)
	pgEmandateProcessor := domaincreationprocessor.NewPgEmandateProcessor(pgServiceClient, recurringPaymentsActionDaoCRDB, recurringPaymentDaoCRDB, piClient, payClient)
	pgUpiProcessor := domaincreationprocessor.NewPgUpiProcessor(pgServiceClient, recurringPaymentsActionDaoCRDB, recurringPaymentDaoCRDB, payClient)
	domainCreationProcessorFactoryImpl := domaincreationprocessor.NewDomainCreationProcessorFactoryImpl(enachMandateCreationProcessor, pgEmandateProcessor, pgUpiProcessor)
	enachMandateExecutionProcessor := domainexecutionprocessor.NewEnachMandateExecutionProcessor(EnachClient)
	recurringPaymentsVendorDetailsDaoCRDB := dao.NewRecurringPaymentsVendorDetailsDao(domainIdGenerator, usecaseDbResourceProvider)
	domainexecutionprocessorPgUpiProcessor := domainexecutionprocessor.NewPgUpiProcessor(recurringPaymentsActionDaoCRDB, recurringPaymentDaoCRDB, pgServiceClient, recurringPaymentsVendorDetailsDaoCRDB)
	domainexecutionprocessorPgEmandateProcessor := domainexecutionprocessor.NewPgEmandateProcessor(recurringPaymentsActionDaoCRDB, recurringPaymentDaoCRDB, pgServiceClient, recurringPaymentsVendorDetailsDaoCRDB)
	domainExecutionProcessoryFactoryImpl := domainexecutionprocessor.NewDomainExecutionProcessoryFactoryImpl(enachMandateExecutionProcessor, domainexecutionprocessorPgUpiProcessor, domainexecutionprocessorPgEmandateProcessor)
	domainrevokeprocessorPgEmandateProcessor := domainrevokeprocessor.NewPgEmandateProcessor(recurringPaymentsActionDaoCRDB, recurringPaymentDaoCRDB, pgServiceClient, recurringPaymentsVendorDetailsDaoCRDB)
	domainRevokeProcessorFactoryImpl := domainrevokeprocessor.NewDomainRevokeProcessorFactoryImpl(domainrevokeprocessorPgEmandateProcessor)
	activityProcessor := activity.NewProcessor(rpClient, internalProcessor, actorProcessor, recurringPaymentDaoCRDB, config, orderClient, crdbIdempotentTxnExecutor, recurringPaymentsActionDaoCRDB, executeRecurringPaymentNoAuthFactoryImpl, payClient, paymentClient, commsClient, domainCreationProcessorFactoryImpl, domainExecutionProcessoryFactoryImpl, domainRevokeProcessorFactoryImpl, recurringPaymentActionUpdatePublisher)
	return activityProcessor
}

func InitialiseEnachService(db types.RecurringPaymentPGDB, usecaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB], config *server.Config, genconf4 *genconf.Config, rpClient recurringpayment.RecurringPaymentServiceClient, actorClient actor.ActorClient, piClient paymentinstrument.PiClient, userClient user.UsersClient, celestialClient celestial.CelestialClient) (*enach2.Service, error) {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	enableResourceProvider := EnachMandateDaoEntitySegregationFlag(genconf4)
	attributedIdGen := dao3.ProvideAttributedIdGen(clock)
	enachMandateDaoPgdb := dao3.NewEnachMandateDaoPgdb(db, domainIdGenerator, usecaseDbResourceProvider, enableResourceProvider, attributedIdGen)
	enachMandateActionDaoPgdb := dao3.NewEnachMandateActionDaoPgdb(db)
	gormDB := types.RecurringPaymentPGDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	federalProcessor, err := enachvendor.NewEnachFederalProcessor(config, userClient, piClient, actorClient, enachMandateActionDaoPgdb)
	if err != nil {
		return nil, err
	}
	factory := enachvendor.NewFactory(federalProcessor)
	service := enach2.NewService(config, enachMandateDaoPgdb, enachMandateActionDaoPgdb, gormTxnExecutor, factory, rpClient, celestialClient)
	return service, nil
}

func InitializeEnachConsumerService(celestialClient celestial.CelestialClient) *consumer2.ConsumerService {
	consumerService := consumer2.NewConsumerService(celestialClient)
	return consumerService
}

func InitializeEnachDevService(genconf4 *genconf.Config, db types.RecurringPaymentPGDB, usecaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB]) *developer2.EnachDevService {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	enableResourceProvider := EnachMandateDaoEntitySegregationFlag(genconf4)
	attributedIdGen := dao3.ProvideAttributedIdGen(clock)
	enachMandateDaoPgdb := dao3.NewEnachMandateDaoPgdb(db, domainIdGenerator, usecaseDbResourceProvider, enableResourceProvider, attributedIdGen)
	enachMandateProcessor := processor2.NewEnachMandateProcessor(enachMandateDaoPgdb)
	enachMandateActionDaoPgdb := dao3.NewEnachMandateActionDaoPgdb(db)
	enachMandateActionProcessor := processor2.NewEnachMandateActionProcessor(enachMandateActionDaoPgdb)
	devFactory := developer2.NewDevFactory(enachMandateProcessor, enachMandateActionProcessor)
	enachDevService := developer2.NewEnachDevService(devFactory)
	return enachDevService
}

func InitialiseEnachActivityProcessor(db types.RecurringPaymentPGDB, usecaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB], config *worker.Config, genconf4 *genconf3.Config, rpClient recurringpayment.RecurringPaymentServiceClient, piClient paymentinstrument.PiClient, celestialClient celestial.CelestialClient, enachRecordsS3Client s3.S3Client, actorClient actor.ActorClient, userClient user.UsersClient, vgEnachClient enach3.EnachClient) (*activity2.Processor, error) {
	defaultTime := datetime.NewDefaultTime()
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	enableResourceProvider := EnachMandateDaoWorkerEntitySegregationFlag(genconf4)
	attributedIdGen := dao3.ProvideAttributedIdGen(clock)
	enachMandateDaoPgdb := dao3.NewEnachMandateDaoPgdb(db, domainIdGenerator, usecaseDbResourceProvider, enableResourceProvider, attributedIdGen)
	enachMandateActionDaoPgdb := dao3.NewEnachMandateActionDaoPgdb(db)
	activityProcessor, err := activity2.NewProcessor(defaultTime, config, enachMandateDaoPgdb, enachMandateActionDaoPgdb, celestialClient, piClient, rpClient, enachRecordsS3Client, vgEnachClient)
	if err != nil {
		return nil, err
	}
	return activityProcessor, nil
}

// wire.go:

func provideRecurringPaymentLockRedisClient(client types3.UpiRedisStore) *redis.Client {
	return client
}

func provideEnachCreationValidationConfig(config *server.Config) *commons.EnachCreationValidationConfig {
	return config.EnachCreationValidationConfig
}

func FeatureReleaseConfigProvider(conf *genconf.Config) *genconf4.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}

func SIExecutionParamsProvider(conf *genconf.Config) *genconf.SIExecutionParams {
	return conf.SIExecutionParams()
}

func FeatureFlagsProvider(conf *genconf.Config) *genconf.FeatureFlags {
	return conf.FeatureFlags()
}

func GormProvider(db types.EpifiCRDB) *gorm.DB {
	return db
}

func envProvider(conf *server.Config) string {
	return conf.Application.Environment
}

func DbResourceProviderEnableFlag(conf *genconf.Config) types6.EnableResourceProvider {
	logger.InfoNoCtx(fmt.Sprintf("Entity segregation flag in recurring payment config : %v", conf.EnableEntitySegregation()))
	return types6.EnableResourceProvider(conf.EnableEntitySegregation())
}

func EnachMandateDaoEntitySegregationFlag(conf *genconf.Config) types6.EnableResourceProvider {
	logger.InfoNoCtx("Disabling entity segregation for enach mandate dao", zap.Bool("entity_segregation", conf.EnableEnachMandateEntitySegregation()))
	return types6.EnableResourceProvider(conf.EnableEnachMandateEntitySegregation())
}

func WorkerResourceProviderEnableFlag(conf *genconf3.Config) types6.EnableResourceProvider {
	logger.InfoNoCtx(fmt.Sprintf("Entity segregation flag in recurring payment worker config : %v", conf.EnableEntitySegregation()))
	return types6.EnableResourceProvider(conf.EnableEntitySegregation())
}

func EnachMandateDaoWorkerEntitySegregationFlag(conf *genconf3.Config) types6.EnableResourceProvider {
	logger.InfoNoCtx("Disabling entity segregation for enach mandate dao", zap.Bool("entity_segregation", conf.EnableEnachMandateEntitySegregation()))
	return types6.EnableResourceProvider(conf.EnableEnachMandateEntitySegregation())
}
