//go:build wireinject
// +build wireinject

//go:generate wire
package wire

import (
	"fmt"

	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/pkg/storage/v2/usecase"

	celestialPb "github.com/epifi/be-common/api/celestial"

	dcMandatePb "github.com/epifi/gamma/api/card/debitcardmandate"
	usStocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	releaseEvaluatorPb "github.com/epifi/gamma/pkg/feature/release"
	releaseGenConfig "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	"github.com/epifi/gamma/recurringpayment/internal/domainrevokeprocessor"

	vendorapiGenConf "github.com/epifi/be-common/pkg/vendorapi/config/genconf"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/accounts/operstatus"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	cpPb "github.com/epifi/gamma/api/card/provisioning"
	commsPb "github.com/epifi/gamma/api/comms"
	depositPb "github.com/epifi/gamma/api/deposit"
	catalogManagerPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	merchantPb "github.com/epifi/gamma/api/merchant"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPIPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	paymentgateway2 "github.com/epifi/gamma/api/recurringpayment/paymentgateway"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	savingsClientPb "github.com/epifi/gamma/api/savings"
	savingsPb "github.com/epifi/gamma/api/savings"
	timelinePb "github.com/epifi/gamma/api/timeline"
	tspUserPb "github.com/epifi/gamma/api/tspuser"
	upcomingTxnsPb "github.com/epifi/gamma/api/upcomingtransactions"
	upiPb "github.com/epifi/gamma/api/upi"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
	usersPb "github.com/epifi/gamma/api/user"
	userGroup "github.com/epifi/gamma/api/user/group"
	vgEnachPb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
	vgPgPb "github.com/epifi/gamma/api/vendorgateway/pg"
	types3 "github.com/epifi/gamma/order/wire/types"
	vendorapiPkgWire "github.com/epifi/gamma/pkg/vendorapi/inhouse/wire"
	recurringpayment2 "github.com/epifi/gamma/recurringpayment"
	rpActivity "github.com/epifi/gamma/recurringpayment/activity"
	commonConfig "github.com/epifi/gamma/recurringpayment/config/commons"
	rpServerConfig "github.com/epifi/gamma/recurringpayment/config/server"
	rpServerGenConf "github.com/epifi/gamma/recurringpayment/config/server/genconf"
	rpWorkerConfig "github.com/epifi/gamma/recurringpayment/config/worker"
	rpworkergenconfig "github.com/epifi/gamma/recurringpayment/config/worker/genconf"
	rpConsumer "github.com/epifi/gamma/recurringpayment/consumer"
	recurringpaymentCx "github.com/epifi/gamma/recurringpayment/cx"
	dao "github.com/epifi/gamma/recurringpayment/dao"
	"github.com/epifi/gamma/recurringpayment/developer"
	"github.com/epifi/gamma/recurringpayment/developer/processor"
	"github.com/epifi/gamma/recurringpayment/enach"
	enachActivity "github.com/epifi/gamma/recurringpayment/enach/activity"
	enachConsumer "github.com/epifi/gamma/recurringpayment/enach/consumer"
	enachDao "github.com/epifi/gamma/recurringpayment/enach/dao"
	enachDeveloper "github.com/epifi/gamma/recurringpayment/enach/developer"
	enachProcessor "github.com/epifi/gamma/recurringpayment/enach/developer/processor"
	"github.com/epifi/gamma/recurringpayment/enach/enachvendor"
	"github.com/epifi/gamma/recurringpayment/internal"
	"github.com/epifi/gamma/recurringpayment/internal/actionstatusfetcher"
	"github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor"
	"github.com/epifi/gamma/recurringpayment/internal/domainexecutionprocessor"
	"github.com/epifi/gamma/recurringpayment/internal/executeRPNoAuth"
	"github.com/epifi/gamma/recurringpayment/internal/validationrules"
	"github.com/epifi/gamma/recurringpayment/paymentgateway"
	"github.com/epifi/gamma/recurringpayment/standinginstruction"
	rpTypes "github.com/epifi/gamma/recurringpayment/types"
	types2 "github.com/epifi/gamma/recurringpayment/wire/types"
	upitypes "github.com/epifi/gamma/upi/wire/types"
	vgtypes "github.com/epifi/gamma/vendorgateway/wire/types"
)

func provideRecurringPaymentLockRedisClient(client upitypes.UpiRedisStore) *redis.Client {
	return client
}

func provideEnachCreationValidationConfig(config *rpServerConfig.Config) *commonConfig.EnachCreationValidationConfig {
	return config.EnachCreationValidationConfig
}

func InitialisePaymentGatewayService(conf *rpServerConfig.Config,
	vgPgClient vgPgPb.PaymentGatewayClient,
	payClient payPb.PayClient,
	orderClient orderPb.OrderServiceClient,
	db types.EpifiCRDB,
	userClient usersPb.UsersClient,
	celestialClient celestialPb.CelestialClient,
	recurringPaymentClient rpPb.RecurringPaymentServiceClient,
	client piPb.PiClient,
	dbResourceProvider *storageV2.DBResourceProvider[*gorm.DB],
	usecaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB],
	tspUserClient tspUserPb.TspUserServiceClient,
	groupClient userGroup.GroupClient,
	actorClient actorPb.ActorClient,
	enachClient enachPb.EnachServiceClient,
	dynConf *rpServerGenConf.Config) *paymentgateway.Service {
	wire.Build(
		dao.WireSet,
		idgen.WireSet,
		internal.WireSet,
		idgen.NewClock,
		paymentgateway.NewService,
		DbResourceProviderEnableFlag,
	)
	return &paymentgateway.Service{}
}

func InitialiseRecurringPaymentService(
	db types.EpifiCRDB,
	siClient siPb.StandingInstructionServiceClient,
	orderClient orderPb.OrderServiceClient,
	savingsClient savingsPb.SavingsClient,
	actorClient actorPb.ActorClient,
	authClient authPb.AuthClient,
	userClient usersPb.UsersClient,
	paymentClient paymentPb.PaymentClient,
	config *rpServerConfig.Config,
	upiMandateClient upiMandatePb.MandateServiceClient,
	accountPIRelationsClient accountPIPb.AccountPIRelationClient,
	piClient piPb.PiClient,
	timelineClient timelinePb.TimelineServiceClient,
	upiClient upiPb.UPIClient,
	commsClient types2.RecPaymentCommsClientWithInterceptors,
	genConf *rpServerGenConf.Config,
	celestialClient celestialPb.CelestialClient,
	recurringPaymentLockRedisStore upitypes.UpiRedisStore,
	inPaymentOrderPublisher types2.InPaymentOrderUpdatePublisher,
	userGrpClient userGroup.GroupClient,
	enachClient enachPb.EnachServiceClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	enachVgClient vgtypes.VgEnachClientWithInterceptors,
	fetchAndCreateOffAppRecurringPaymentPublisher types2.FetchAndCreateOffAppRecurringPaymentPublisher,
	operationStatusClient operstatus.OperationalStatusServiceClient,
	upcomingTxnClient upcomingTxnsPb.UpcomingTransactionsClient,
	merchantClient merchantPb.MerchantServiceClient,
	catalogManagerClient catalogManagerPb.CatalogManagerClient,
	depositClient depositPb.DepositClient,
	pgServiceClient paymentgateway2.PaymentGatewayServiceClient,
	dbResourceProvider *storageV2.DBResourceProvider[*gorm.DB],
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	usecaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB],
	tspUserClient tspUserPb.TspUserServiceClient,
	payClient payPb.PayClient,
	eventBroker events.Broker,
	usStocksCatalogClient usStocksCatalogPb.CatalogManagerClient) *recurringpayment2.Service {
	wire.Build(
		vgtypes.VgEnachClientProvider,
		types2.CommsClientProvider,
		dao.WireSet,
		idgen.WireSet,
		internal.WireSet,
		domaincreationprocessor.DomainCreationProcessorFactoryWireSet,
		actionstatusfetcher.AllActionStatusFetchersWireSet,
		actionstatusfetcher.FactoryWireSet,
		recurringpayment2.NewService,
		SIExecutionParamsProvider,
		FeatureFlagsProvider,
		provideRecurringPaymentLockRedisClient,
		provideEnachCreationValidationConfig,
		lock.DefaultLockMangerWireSet,
		domainexecutionprocessor.DomainExecutionProcessorFactoryWireSet,
		validationrules.InitExecutionValidationChain,
		DbResourceProviderEnableFlag,
		wire.Bind(new(storageV2.IDbResourceProvider[storageV2.IdempotentTxnExecutor]), new(*storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor])),
	)
	return &recurringpayment2.Service{}
}

// todo(Utkarsh): Add wireset for IFactory and fix enach service initiation
func InitialiseRecurringPaymentConsumerService(
	rpClient rpPb.RecurringPaymentServiceClient,
	savingsClient savingsClientPb.SavingsClient,
	enachVgClient vgtypes.VgEnachClientWithInterceptors,
	piClient piPb.PiClient,
	enachClient enachPb.EnachServiceClient,
	usersClient usersPb.UsersClient,
	groupClient userGroup.GroupClient,
	db types.EpifiCRDB,
	actorClient actorPb.ActorClient,
	merchantClient merchantPb.MerchantServiceClient,
	dbResourceProvider *storageV2.DBResourceProvider[*gorm.DB],
	conf *rpServerConfig.Config,
	dynConf *rpServerGenConf.Config,
	eventsBroker events.Broker,
	cpClient cpPb.CardProvisioningClient,
	dcMandateClient dcMandatePb.DebitCardMandateServiceClient,
	timelineClient timelinePb.TimelineServiceClient,
	orderClient orderPb.OrderServiceClient,
	vendorapiGenConf *vendorapiGenConf.Config,
	publisher types2.FetchAndCreateFailedEnachTransactionPublisher,
) *rpConsumer.ConsumerService {
	wire.Build(
		vgtypes.VgEnachClientProvider,
		internal.WireSet,
		dao.WireSet,
		idgen.NewClock,
		idgen.WireSet,
		idgen.UuidGeneratorWireSet,
		envProvider,
		vendorapiPkgWire.MerchantResolutionPkgWireSet,
		rpConsumer.NewConsumerService,
		releaseEvaluatorPb.EvaluatorWireSet,
		FeatureReleaseConfigProvider,
		DbResourceProviderEnableFlag,
	)
	return &rpConsumer.ConsumerService{}
}

func FeatureReleaseConfigProvider(conf *rpServerGenConf.Config) *releaseGenConfig.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}

func SIExecutionParamsProvider(conf *rpServerGenConf.Config) *rpServerGenConf.SIExecutionParams {
	return conf.SIExecutionParams()
}

func FeatureFlagsProvider(conf *rpServerGenConf.Config) *rpServerGenConf.FeatureFlags {
	return conf.FeatureFlags()
}

func InitialiseStandingInstructionService(
	db types.EpifiCRDB,
	siVGClient vgtypes.SIVGClientWithInterceptors,
	piClient piPb.PiClient,
	authClient authPb.AuthClient,
	savingsClient savingsPb.SavingsClient,
	userClient usersPb.UsersClient,
	actorClient actorPb.ActorClient,
	vgPaymentClient vgtypes.VgPaymentClientWithInterceptors,
	client bankcust.BankCustomerServiceClient,
	dbResourceProvider *storageV2.DBResourceProvider[*gorm.DB],
	conf *rpServerConfig.Config,
	dynConf *rpServerGenConf.Config,
) *standinginstruction.Service {
	wire.Build(DbResourceProviderEnableFlag, vgtypes.SIVGClientProvider, vgtypes.VgPaymentClientProvider, dao.WireSet, idgen.NewClock, idgen.WireSet, standinginstruction.NewService)
	return &standinginstruction.Service{}
}

func InitialiseDevService(db types.EpifiCRDB, dbResourceProvider *storageV2.DBResourceProvider[*gorm.DB], usecaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB], conf *rpServerConfig.Config, config *rpServerGenConf.Config) *developer.RecurringPaymentDevService {
	wire.Build(
		dao.WireSet,
		idgen.NewClock,
		idgen.WireSet,
		developer.NewRecurringPaymentDevService,
		developer.NewDevFactory,
		processor.NewDevRecurringPaymentEntity,
		processor.NewDevRecurringPaymentActionEntity,
		processor.NewDevRecurringPaymentsVendorDetailsEntity,
		processor.NewDevSIEntity,
		processor.NewDevSIRequestEntity,
		DbResourceProviderEnableFlag,
	)
	return &developer.RecurringPaymentDevService{}
}

func InitialiseCxService(
	db types.EpifiCRDB,
	orderClient orderPb.OrderServiceClient,
	conf *rpServerConfig.Config,
	dbResourceProvider *storageV2.DBResourceProvider[*gorm.DB],
	dynConf *rpServerGenConf.Config) *recurringpaymentCx.Service {
	wire.Build(DbResourceProviderEnableFlag, dao.WireSet, idgen.NewClock, idgen.WireSet, recurringpaymentCx.NewService)
	return &recurringpaymentCx.Service{}
}

func InitializeActivityProcessor(
	db types.EpifiCRDB,
	rpClient rpPb.RecurringPaymentServiceClient,
	actorClient actorPb.ActorClient,
	config *rpWorkerConfig.Config,
	orderClient orderPb.OrderServiceClient,
	siClient siPb.StandingInstructionServiceClient,
	payClient payPb.PayClient,
	paymentClient paymentPb.PaymentClient,
	EnachClient enachPb.EnachServiceClient,
	commsClient commsPb.CommsClient,
	recurringPaymentActionUpdatePublisher rpTypes.RecurringPaymentActionUpdateEventPublisher,
	piClient piPb.PiClient,
	merchantClient merchantPb.MerchantServiceClient,
	enachCreationValidationConfig *commonConfig.EnachCreationValidationConfig,
	pgServiceClient paymentgateway2.PaymentGatewayServiceClient,
	dbResourceProvider *storageV2.DBResourceProvider[*gorm.DB],
	usecaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB],
	dynConf *rpworkergenconfig.Config,
) *rpActivity.Processor {
	wire.Build(
		GormProvider,
		rpActivity.NewProcessor,
		WorkerResourceProviderEnableFlag,
		executeRPNoAuth.ExecuteRecurringPaymentNoAuthFactoryWireSet,
		internal.WireSet,
		domaincreationprocessor.DomainCreationProcessorFactoryWireSet,
		domainexecutionprocessor.DomainExecutionProcessorFactoryWireSet,
		domainrevokeprocessor.DomainRevokeProcessorFactoryWireSet,
		storageV2.IdempotentTxnExecutorWireSet,
		idgen.NewClock,
		idgen.WireSet,
		dao.WireSet,
	)
	return &rpActivity.Processor{}
}

func GormProvider(db types.EpifiCRDB) *gorm.DB {
	return db
}

func InitialiseEnachService(
	db types.RecurringPaymentPGDB,
	usecaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB],
	config *rpServerConfig.Config,
	genconf *rpServerGenConf.Config,
	rpClient rpPb.RecurringPaymentServiceClient,
	actorClient actorPb.ActorClient,
	piClient piPb.PiClient,
	userClient usersPb.UsersClient,
	celestialClient celestialPb.CelestialClient,
) (*enach.Service, error) {
	wire.Build(
		types.RecurringPaymentPGDBProvider,
		EnachMandateDaoEntitySegregationFlag,
		idgen.WireSet,
		idgen.NewClock,
		enachDao.EnachMandateDaoWireSet,
		enachDao.EnachMandateActionDaoWireSet,
		storageV2.DefaultTxnExecutorWireSet,
		enachvendor.FactoryWireSet,
		enach.NewService,
	)
	return &enach.Service{}, nil
}

func InitializeEnachConsumerService(celestialClient celestialPb.CelestialClient) *enachConsumer.ConsumerService {
	wire.Build(enachConsumer.NewConsumerService)
	return &enachConsumer.ConsumerService{}
}

func InitializeEnachDevService(genconf *rpServerGenConf.Config, db types.RecurringPaymentPGDB, usecaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB]) *enachDeveloper.EnachDevService {
	wire.Build(
		idgen.WireSet,
		EnachMandateDaoEntitySegregationFlag,
		idgen.NewClock,
		enachDao.EnachMandateDaoWireSet,
		enachDao.EnachMandateActionDaoWireSet,
		enachDeveloper.NewDevFactory,
		enachDeveloper.NewEnachDevService,
		enachProcessor.NewEnachMandateProcessor,
		enachProcessor.NewEnachMandateActionProcessor,
	)
	return &enachDeveloper.EnachDevService{}
}

func InitialiseEnachActivityProcessor(
	db types.RecurringPaymentPGDB,
	usecaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB],
	config *rpWorkerConfig.Config,
	genconf *rpworkergenconfig.Config,
	rpClient rpPb.RecurringPaymentServiceClient,
	piClient piPb.PiClient,
	celestialClient celestialPb.CelestialClient,
	enachRecordsS3Client s3.S3Client,
	actorClient actorPb.ActorClient,
	userClient usersPb.UsersClient,
	vgEnachClient vgEnachPb.EnachClient,
) (*enachActivity.Processor, error) {
	wire.Build(
		idgen.WireSet,
		EnachMandateDaoWorkerEntitySegregationFlag,
		idgen.NewClock,
		datetime.WireDefaultTimeSet,
		enachDao.EnachMandateDaoWireSet,
		enachDao.EnachMandateActionDaoWireSet,
		enachActivity.NewProcessor,
	)
	return &enachActivity.Processor{}, nil
}

func envProvider(conf *rpServerConfig.Config) string {
	return conf.Application.Environment
}

func DbResourceProviderEnableFlag(conf *rpServerGenConf.Config) types3.EnableResourceProvider {
	logger.InfoNoCtx(fmt.Sprintf("Entity segregation flag in recurring payment config : %v", conf.EnableEntitySegregation()))
	return types3.EnableResourceProvider(conf.EnableEntitySegregation())
}

func EnachMandateDaoEntitySegregationFlag(conf *rpServerGenConf.Config) types3.EnableResourceProvider {
	logger.InfoNoCtx("Disabling entity segregation for enach mandate dao", zap.Bool("entity_segregation", conf.EnableEnachMandateEntitySegregation()))
	return types3.EnableResourceProvider(conf.EnableEnachMandateEntitySegregation())
}

func WorkerResourceProviderEnableFlag(conf *rpworkergenconfig.Config) types3.EnableResourceProvider {
	logger.InfoNoCtx(fmt.Sprintf("Entity segregation flag in recurring payment worker config : %v", conf.EnableEntitySegregation()))
	return types3.EnableResourceProvider(conf.EnableEntitySegregation())
}

func EnachMandateDaoWorkerEntitySegregationFlag(conf *rpworkergenconfig.Config) types3.EnableResourceProvider {
	logger.InfoNoCtx("Disabling entity segregation for enach mandate dao", zap.Bool("entity_segregation", conf.EnableEnachMandateEntitySegregation()))
	return types3.EnableResourceProvider(conf.EnableEnachMandateEntitySegregation())
}
