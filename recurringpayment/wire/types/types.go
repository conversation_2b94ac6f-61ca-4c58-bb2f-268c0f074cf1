package types

import (
	"github.com/redis/go-redis/v9"

	"github.com/epifi/gamma/api/comms"

	"github.com/epifi/be-common/pkg/queue"
)

type RecurringPaymentLockRedisStore *redis.Client

type InPaymentOrderUpdatePublisher queue.Publisher

type FetchAndCreateOffAppRecurringPaymentPublisher queue.Publisher

type FetchAndCreateFailedEnachTransactionPublisher queue.Publisher

type RecPaymentCommsClientWithInterceptors comms.CommsClient

func CommsClientProvider(cl RecPaymentCommsClientWithInterceptors) comms.CommsClient {
	return cl
}
