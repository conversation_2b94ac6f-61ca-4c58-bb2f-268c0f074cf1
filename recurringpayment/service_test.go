package recurringpayment

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/samber/lo"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"

	testPkg "github.com/epifi/be-common/pkg/test"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	"github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/frontend/deeplink"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	enachMocks "github.com/epifi/gamma/api/recurringpayment/enach/mocks"
	payloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	"github.com/epifi/gamma/recurringpayment/internal/adaptor"
	internalMocks "github.com/epifi/gamma/recurringpayment/internal/mocks"

	accountPiMocks "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"

	actorPb "github.com/epifi/gamma/api/actor"
	commspb "github.com/epifi/gamma/api/comms"
	fcmpb "github.com/epifi/gamma/api/frontend/fcm"

	queuePb "github.com/epifi/be-common/api/queue"

	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	commsMocks "github.com/epifi/gamma/api/comms/mocks"
	types "github.com/epifi/gamma/api/typesv2"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/money"

	orderPb "github.com/epifi/gamma/api/order"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	pb "github.com/epifi/gamma/api/recurringpayment"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	"github.com/epifi/gamma/api/recurringpayment/standinginstruction/mocks"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
	mocks3 "github.com/epifi/gamma/api/upi/mandate/mocks"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	mocks2 "github.com/epifi/gamma/recurringpayment/internal/mocks"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/timestamppb"
)

var (
	postAuthDeeplink = &deeplink.Deeplink{
		Screen:        deeplink.Screen_AUTH_STATUS_POLL_SCREEN,
		ScreenOptions: &deeplink.Deeplink_AuthStatusPollScreenOptions{},
	}

	postActionDeeplink = &deeplink.Deeplink{
		Screen:        deeplink.Screen_AUTH_STATUS_POLL_SCREEN,
		ScreenOptions: &deeplink.Deeplink_AuthStatusPollScreenOptions{},
	}

	postAuthDeeplinkAny   = lo.Must(anypb.New(postAuthDeeplink))
	postActionDeeplinkAny = lo.Must(anypb.New(postActionDeeplink))
)

func TestService_CreateRecurringPayment(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockOrderClient := orderMocks.NewMockOrderServiceClient(ctr)
	mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
	mockRecurringPaymentsActionDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
	mockSIClient := mocks.NewMockStandingInstructionServiceClient(ctr)
	mockRecurringPaymentProcessor := mocks2.NewMockRecurringPaymentProcessor(ctr)
	mockActor := actorMocks.NewMockActorClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockAccountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
	mockMandateClient := mocks3.NewMockMandateServiceClient(ctr)
	mockCelestialProcessor := internalMocks.NewMockCelestialProcessor(ctr)

	svc := NewService(mockRecurringPaymentDao, mockOrderClient, mockSIClient, mockRecurringPaymentsActionDao, nil, mockActor, nil, nil, nil, conf, mockRecurringPaymentProcessor, mockMandateClient, mockAccountPiClient, mockPiClient, nil, nil, nil, nil, dynamicConf.FeatureFlags(), mockCelestialProcessor, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	startDate := timestampPb.New(time.Now())
	endDate := timestampPb.New(time.Now().Add(48 * time.Hour))
	endDate2 := timestampPb.New(time.Now().Add(91 * 24 * time.Hour))

	recurringPayment := &pb.RecurringPayment{
		Id:          "id-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_CREATION_QUEUED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}
	recurringPaymentPayee := &pb.RecurringPayment{
		Id:          "id-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_CREATION_QUEUED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYEE,
	}

	marshalledOrderPayload, _ := protojson.Marshal(&pb.RecurringPaymentCreationInfo{
		RecurringPaymentId: "id-1",
		ClientRequestId:    "client-req-1",
		ReqId:              "txn-id-1",
	})
	recurringPaymentMandate := &pb.RecurringPayment{
		Id:          "id-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_UPI,
		State:                    pb.RecurringPaymentState_CREATION_QUEUED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}

	rpPayload, _ := protojson.Marshal(&pb.RecurringPaymentCreationInfo{
		RecurringPaymentId: recurringPayment.GetId(),
		ClientRequestId:    "client-req-1",
		ReqId:              "txn-id-1",
	})
	payload, _ := protojson.Marshal(&payloadPb.CreateRecurringPaymentAuthSignal{})
	mandatePayload, _ := protojson.Marshal(&upiMandatePb.MandateCreationPayload{
		Revokeable:   false,
		ShareToPayee: false,
		BlockFund:    false,
		Umn:          "",
		ReqInfo: &upiMandatePb.Payload{
			Mcc:     "6211",
			Purpose: "01",
		},
	})
	tests := []struct {
		name           string
		req            *pb.CreateRecurringPaymentRequest
		setupMockCalls func()
		want           *pb.CreateRecurringPaymentResponse
		wantErr        bool
	}{
		{
			name: "recurring payment creation failed due to empty client req id",
			req: &pb.CreateRecurringPaymentRequest{
				FromActorId: "actor-1",
				ToActorId:   "actor-2",
			},
			setupMockCalls: func() {},
			want: &pb.CreateRecurringPaymentResponse{
				Status: rpc.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "block or Spam User",
			req: &pb.CreateRecurringPaymentRequest{
				ClientRequestId: "client-req-id",
				FromActorId:     "actor-1",
				ToActorId:       "actor-2",
			},
			setupMockCalls: func() {
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-1",
					OtherActorId:   "actor-2",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_BLOCKED,
				}, nil)
			},
			want: &pb.CreateRecurringPaymentResponse{
				Status: rpc.NewStatusWithoutDebug(uint32(pb.CreateRecurringPaymentResponse_BLOCKED_USER), "User is blocked"),
			},
			wantErr: false,
		},
		{
			name: "created recurring payment successfully via payee for oms based flow",
			req: &pb.CreateRecurringPaymentRequest{
				CurrentActorId: "actor-1",
				FromActorId:    "actor-1",
				ToActorId:      "actor-2",
				Type:           pb.RecurringPaymentType_STANDING_INSTRUCTION,
				PiFrom:         "pi-1",
				PiTo:           "pi-2",
				Amount:         money.ZeroINR().Pb,
				AmountType:     pb.AmountType_MAXIMUM,
				Interval: &types.Interval{
					StartTime: startDate,
					EndTime:   endDate,
				},
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
					Day:              5,
				},
				MaximumAllowedTxns:       10,
				PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
				Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
				Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
				UiEntryPoint:             pb.UIEntryPoint_FIT,
				ClientRequestId:          "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				InitiatedBy:   pb.InitiatedBy_PAYEE,
				TransactionId: "txn-id-1",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-1",
					OtherActorId:   "actor-2",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-2",
					OtherActorId:   "actor-1",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentActionByClientReqId(gomock.Any(), "client-req-1").Return(nil, rpc.StatusAsError(rpc.StatusRecordNotFound()))
				mockRecurringPaymentDao.EXPECT().Create(gomock.Any(), &pb.RecurringPayment{
					FromActorId: "actor-1",
					ToActorId:   "actor-2",
					Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
					PiFrom:      "pi-1",
					PiTo:        "pi-2",
					Amount:      money.ZeroINR().Pb,
					AmountType:  pb.AmountType_MAXIMUM,
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule: &pb.RecurrenceRule{
						AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
						Day:              5,
					},
					MaximumAllowedTxns:       10,
					PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
					PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					State:                    pb.RecurringPaymentState_CREATION_INITIATED,
					Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
					Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
					UiEntryPoint:             pb.UIEntryPoint_FIT,
					InitiatedBy:              pb.InitiatedBy_PAYEE,
				}, commontypes.Ownership_EPIFI_TECH).Return(recurringPaymentPayee, nil)
				mockOrderClient.EXPECT().CreateOrder(gomock.Any(), &orderPb.CreateOrderRequest{
					ActorFrom:    "actor-1",
					ActorTo:      "actor-2",
					Workflow:     orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT,
					Provenance:   orderPb.OrderProvenance_USER_APP,
					OrderPayload: marshalledOrderPayload,
					Amount:       money.ZeroINR().Pb,
					Status:       orderPb.OrderStatus_CREATED,
					UiEntryPoint: orderPb.UIEntryPoint_FITTT,
					ClientReqId:  "client-req-1",
				}).Return(&orderPb.CreateOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:          "order-id-1",
						ClientReqId: "client-req-1",
					},
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "id-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_CREATE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "txn-id-1",
				}).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "id-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_CREATE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "txn-id-1",
				}, nil)
				mockSIClient.EXPECT().Create(gomock.Any(), &siPb.CreateRequest{
					RecurringPaymentId: "id-1",
					Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
					RequestId:          "txn-id-1",
				}).Return(&siPb.CreateResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_STANDING_INSTRUCTION,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(true, pb.CredBlockType_PARTNER_SDK, nil)
				mockAccountPiClient.EXPECT().GetByPiId(gomock.Any(), &accountPiPb.GetByPiIdRequest{PiId: "pi-1"}).Return(&accountPiPb.GetByPiIdResponse{
					Status:    rpc.StatusOk(),
					AccountId: "account-id-1",
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: "**********",
								SecureAccountNumber: "xxxxxx7890",
								IfscCode:            "FEDR0000",
								AccountType:         accounts.Type_SAVINGS,
								Name:                "test-actor-1",
							},
						},
						VerifiedName: "abc",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-2"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: "**********",
								SecureAccountNumber: "xxxxxx4321",
								IfscCode:            "FEDR0000",
								AccountType:         accounts.Type_SAVINGS,
								Name:                "test-actor-2",
							},
						},
						VerifiedName: "xyz",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockActor.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor-2"}).Return(
					&actorPb.GetEntityDetailsByActorIdResponse{Name: &commontypes.Name{FirstName: "actorName"}, Status: rpc.StatusOk()}, nil)
			},
			want: &pb.CreateRecurringPaymentResponse{
				Status:                   rpc.StatusOk(),
				OrderId:                  "order-id-1",
				RecurringPaymentId:       "id-1",
				IsAuthenticationRequired: true,
				CredBlockType:            pb.CredBlockType_PARTNER_SDK,
				TransactionId:            "txn-id-1",
				TransactionAttributes: []*pb.TransactionAttribute{
					{
						PayerAccountId:                "account-id-1",
						TransactionId:                 "txn-id-1",
						PaymentProtocol:               paymentPb.PaymentProtocol_INTRA_BANK,
						PayeeActorName:                "actorName",
						PayerMaskedAccountNumber:      "xxxxxx7890",
						Amount:                        money.ZeroINR().Pb,
						PayerPaymentInstrument:        "xxxxxx7890",
						PayeePaymentInstrument:        "xxxxxx4321",
						DisplayPayeePaymentInstrument: "xxxxxx4321",
					},
				},
				NextAction: &deeplink.Deeplink{
					Screen: deeplink.Screen_FEDERAL_SECURE_PIN,
					ScreenOptions: &deeplink.Deeplink_FederalSecurePinScreenOptions{
						FederalSecurePinScreenOptions: &deeplink.FederalSecurePinScreenOptions{
							TxnAttributes: adaptor.ConvertToDeepLinkTransactionAttributes(&pb.TransactionAttribute{
								PayerAccountId:                "account-id-1",
								TransactionId:                 "txn-id-1",
								PaymentProtocol:               paymentPb.PaymentProtocol_INTRA_BANK,
								PayeeActorName:                "actorName",
								PayerMaskedAccountNumber:      "xxxxxx7890",
								Amount:                        money.ZeroINR().Pb,
								PayerPaymentInstrument:        "xxxxxx7890",
								PayeePaymentInstrument:        "xxxxxx4321",
								DisplayPayeePaymentInstrument: "xxxxxx4321",
							},
								"client-req-1"),
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "created recurring payment successfully",
			req: &pb.CreateRecurringPaymentRequest{
				CurrentActorId: "actor-1",
				FromActorId:    "actor-1",
				ToActorId:      "actor-2",
				Type:           pb.RecurringPaymentType_STANDING_INSTRUCTION,
				PiFrom:         "pi-1",
				PiTo:           "pi-2",
				Amount:         money.ZeroINR().Pb,
				AmountType:     pb.AmountType_MAXIMUM,
				Interval: &types.Interval{
					StartTime: startDate,
					EndTime:   endDate,
				},
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
					Day:              5,
				},
				MaximumAllowedTxns:       10,
				PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
				Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
				Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
				UiEntryPoint:             pb.UIEntryPoint_FIT,
				ClientRequestId:          "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				InitiatedBy:   pb.InitiatedBy_PAYER,
				TransactionId: "txn-id-1",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-1",
					OtherActorId:   "actor-2",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-2",
					OtherActorId:   "actor-1",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentActionByClientReqId(gomock.Any(), "client-req-1").Return(nil, rpc.StatusAsError(rpc.StatusRecordNotFound()))
				mockRecurringPaymentDao.EXPECT().Create(gomock.Any(), &pb.RecurringPayment{
					FromActorId: "actor-1",
					ToActorId:   "actor-2",
					Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
					PiFrom:      "pi-1",
					PiTo:        "pi-2",
					Amount:      money.ZeroINR().Pb,
					AmountType:  pb.AmountType_MAXIMUM,
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule: &pb.RecurrenceRule{
						AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
						Day:              5,
					},
					MaximumAllowedTxns:       10,
					PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
					PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					State:                    pb.RecurringPaymentState_CREATION_QUEUED,
					Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
					Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
					UiEntryPoint:             pb.UIEntryPoint_FIT,
					InitiatedBy:              pb.InitiatedBy_PAYER,
				}, commontypes.Ownership_EPIFI_TECH).Return(recurringPayment, nil)
				mockOrderClient.EXPECT().CreateOrder(gomock.Any(), &orderPb.CreateOrderRequest{
					ActorFrom:    "actor-1",
					ActorTo:      "actor-2",
					Workflow:     orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT,
					Provenance:   orderPb.OrderProvenance_USER_APP,
					OrderPayload: marshalledOrderPayload,
					Amount:       money.ZeroINR().Pb,
					Status:       orderPb.OrderStatus_CREATED,
					UiEntryPoint: orderPb.UIEntryPoint_FITTT,
					ClientReqId:  "client-req-1",
				}).Return(&orderPb.CreateOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:          "order-id-1",
						ClientReqId: "client-req-1",
					},
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "id-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_CREATE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "txn-id-1",
				}).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "id-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_CREATE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "txn-id-1",
				}, nil)
				mockSIClient.EXPECT().Create(gomock.Any(), &siPb.CreateRequest{
					RecurringPaymentId: "id-1",
					Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
					RequestId:          "txn-id-1",
				}).Return(&siPb.CreateResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_STANDING_INSTRUCTION,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(true, pb.CredBlockType_PARTNER_SDK, nil)
				mockAccountPiClient.EXPECT().GetByPiId(gomock.Any(), &accountPiPb.GetByPiIdRequest{PiId: "pi-1"}).Return(&accountPiPb.GetByPiIdResponse{
					Status:    rpc.StatusOk(),
					AccountId: "account-id-1",
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: "**********",
								SecureAccountNumber: "xxxxxx7890",
								IfscCode:            "FEDR0000",
								AccountType:         accounts.Type_SAVINGS,
								Name:                "test-actor-1",
							},
						},
						VerifiedName: "abc",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-2"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: "**********",
								SecureAccountNumber: "xxxxxx4321",
								IfscCode:            "FEDR0000",
								AccountType:         accounts.Type_SAVINGS,
								Name:                "test-actor-2",
							},
						},
						VerifiedName: "xyz",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockActor.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor-2"}).Return(
					&actorPb.GetEntityDetailsByActorIdResponse{Name: &commontypes.Name{FirstName: "actorName"}, Status: rpc.StatusOk()}, nil)
			},
			want: &pb.CreateRecurringPaymentResponse{
				Status:                   rpc.StatusOk(),
				OrderId:                  "order-id-1",
				RecurringPaymentId:       "id-1",
				IsAuthenticationRequired: true,
				CredBlockType:            pb.CredBlockType_PARTNER_SDK,
				TransactionId:            "txn-id-1",
				TransactionAttributes: []*pb.TransactionAttribute{
					{
						PayerAccountId:                "account-id-1",
						TransactionId:                 "txn-id-1",
						PaymentProtocol:               paymentPb.PaymentProtocol_INTRA_BANK,
						PayeeActorName:                "actorName",
						PayerMaskedAccountNumber:      "xxxxxx7890",
						Amount:                        money.ZeroINR().Pb,
						PayerPaymentInstrument:        "xxxxxx7890",
						PayeePaymentInstrument:        "xxxxxx4321",
						DisplayPayeePaymentInstrument: "xxxxxx4321",
					},
				},
				NextAction: &deeplink.Deeplink{
					Screen: deeplink.Screen_FEDERAL_SECURE_PIN,
					ScreenOptions: &deeplink.Deeplink_FederalSecurePinScreenOptions{
						FederalSecurePinScreenOptions: &deeplink.FederalSecurePinScreenOptions{
							TxnAttributes: adaptor.ConvertToDeepLinkTransactionAttributes(&pb.TransactionAttribute{
								PayerAccountId:                "account-id-1",
								TransactionId:                 "txn-id-1",
								PaymentProtocol:               paymentPb.PaymentProtocol_INTRA_BANK,
								PayeeActorName:                "actorName",
								PayerMaskedAccountNumber:      "xxxxxx7890",
								Amount:                        money.ZeroINR().Pb,
								PayerPaymentInstrument:        "xxxxxx7890",
								PayeePaymentInstrument:        "xxxxxx4321",
								DisplayPayeePaymentInstrument: "xxxxxx4321",
							},
								"client-req-1"),
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "order exists with client request id",
			req: &pb.CreateRecurringPaymentRequest{
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-2",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "",
					OtherActorId:   "",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "",
					OtherActorId:   "",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-2"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:           "order-2",
						OrderPayload: marshalledOrderPayload,
					},
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentActionByClientReqId(gomock.Any(), "client-req-2").Return(&pb.RecurringPaymentsAction{
					Id:                 "id",
					RecurringPaymentId: "id-1",
					ClientRequestId:    "client-req-2",
				}, nil)
			},
			want: &pb.CreateRecurringPaymentResponse{
				Status:             rpc.StatusAlreadyExists(),
				OrderId:            "order-2",
				RecurringPaymentId: "id-1",
			},
			wantErr: false,
		},
		{
			name: "error while fetching order for client request id",
			req: &pb.CreateRecurringPaymentRequest{
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-3",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "",
					OtherActorId:   "",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "",
					OtherActorId:   "",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-3"}}).Return(&orderPb.GetOrderResponse{Status: rpc.StatusInternal()}, nil)
			},
			want: &pb.CreateRecurringPaymentResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "error in creating order",
			req: &pb.CreateRecurringPaymentRequest{
				CurrentActorId: "actor-1",
				FromActorId:    "actor-1",
				ToActorId:      "actor-2",
				Type:           pb.RecurringPaymentType_STANDING_INSTRUCTION,
				PiFrom:         "pi-1",
				PiTo:           "pi-2",
				Amount:         money.ZeroINR().Pb,
				AmountType:     pb.AmountType_MAXIMUM,
				Interval: &types.Interval{
					StartTime: startDate,
					EndTime:   endDate,
				},
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
					Day:              5,
				},
				MaximumAllowedTxns:       10,
				PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
				Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
				Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
				UiEntryPoint:             pb.UIEntryPoint_FIT,
				ClientRequestId:          "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				InitiatedBy:   pb.InitiatedBy_PAYER,
				TransactionId: "txn-id-1",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-1",
					OtherActorId:   "actor-2",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-2",
					OtherActorId:   "actor-1",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentActionByClientReqId(gomock.Any(), "client-req-1").Return(nil, rpc.StatusAsError(rpc.StatusRecordNotFound()))
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_STANDING_INSTRUCTION,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(true, pb.CredBlockType_PARTNER_SDK, nil)
				mockRecurringPaymentDao.EXPECT().Create(gomock.Any(), &pb.RecurringPayment{
					FromActorId: "actor-1",
					ToActorId:   "actor-2",
					Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
					PiFrom:      "pi-1",
					PiTo:        "pi-2",
					Amount:      money.ZeroINR().Pb,
					AmountType:  pb.AmountType_MAXIMUM,
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule: &pb.RecurrenceRule{
						AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
						Day:              5,
					},
					MaximumAllowedTxns:       10,
					PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
					PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					State:                    pb.RecurringPaymentState_CREATION_QUEUED,
					Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
					Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
					UiEntryPoint:             pb.UIEntryPoint_FIT,
					InitiatedBy:              pb.InitiatedBy_PAYER,
				}, commontypes.Ownership_EPIFI_TECH).Return(recurringPayment, nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), newCreateActionReqMatcher(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "id-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_CREATE,
					State:              pb.ActionState_ACTION_CREATED,
				})).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "id-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_CREATE,
				}, nil)
				mockSIClient.EXPECT().Create(gomock.Any(), newCreateSIReqMatcher(&siPb.CreateRequest{
					RecurringPaymentId: "id-1",
					Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
					RequestId:          "txn-id-1",
				})).Return(&siPb.CreateResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockOrderClient.EXPECT().CreateOrder(gomock.Any(), &orderPb.CreateOrderRequest{
					ActorFrom:    "actor-1",
					ActorTo:      "actor-2",
					Workflow:     orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT,
					Provenance:   orderPb.OrderProvenance_USER_APP,
					OrderPayload: marshalledOrderPayload,
					Status:       orderPb.OrderStatus_CREATED,
					UiEntryPoint: orderPb.UIEntryPoint_FITTT,
					Amount:       money.ZeroINR().Pb,
					ClientReqId:  "client-req-1",
				}).Return(&orderPb.CreateOrderResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want:    &pb.CreateRecurringPaymentResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "error in creating domain service entity",
			req: &pb.CreateRecurringPaymentRequest{
				CurrentActorId: "actor-1",
				FromActorId:    "actor-1",
				ToActorId:      "actor-2",
				Type:           pb.RecurringPaymentType_STANDING_INSTRUCTION,
				PiFrom:         "pi-1",
				PiTo:           "pi-2",
				Amount:         money.ZeroINR().Pb,
				AmountType:     pb.AmountType_MAXIMUM,
				Interval: &types.Interval{
					StartTime: startDate,
					EndTime:   endDate,
				},
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
					Day:              5,
				},
				MaximumAllowedTxns:       10,
				PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
				Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
				Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
				UiEntryPoint:             pb.UIEntryPoint_FIT,
				ClientRequestId:          "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				InitiatedBy:   pb.InitiatedBy_PAYER,
				TransactionId: "txn-id-1",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-1",
					OtherActorId:   "actor-2",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-2",
					OtherActorId:   "actor-1",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentActionByClientReqId(gomock.Any(), "client-req-1").Return(nil, rpc.StatusAsError(rpc.StatusRecordNotFound()))
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_STANDING_INSTRUCTION,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(true, pb.CredBlockType_PARTNER_SDK, nil)
				mockRecurringPaymentDao.EXPECT().Create(gomock.Any(), &pb.RecurringPayment{
					FromActorId: "actor-1",
					ToActorId:   "actor-2",
					Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
					PiFrom:      "pi-1",
					PiTo:        "pi-2",
					Amount:      money.ZeroINR().Pb,
					AmountType:  pb.AmountType_MAXIMUM,
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule: &pb.RecurrenceRule{
						AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
						Day:              5,
					},
					MaximumAllowedTxns:       10,
					PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
					PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					State:                    pb.RecurringPaymentState_CREATION_QUEUED,
					Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
					Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
					UiEntryPoint:             pb.UIEntryPoint_FIT,
					InitiatedBy:              pb.InitiatedBy_PAYER,
				}, commontypes.Ownership_EPIFI_TECH).Return(recurringPayment, nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), newCreateActionReqMatcher(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "id-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_CREATE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "txn-id-1",
				})).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "id-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_CREATE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "txn-id-1",
				}, nil)
				mockSIClient.EXPECT().Create(gomock.Any(), newCreateSIReqMatcher(&siPb.CreateRequest{
					RecurringPaymentId: "id-1",
					Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
					RequestId:          "txn-id-1",
				})).Return(&siPb.CreateResponse{
					Status: rpc.StatusInternal(),
				}, nil)

			},
			want:    &pb.CreateRecurringPaymentResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "error in validating creation due to invalid end date for one time mandate",
			req: &pb.CreateRecurringPaymentRequest{
				CurrentActorId: "actor-1",
				FromActorId:    "actor-1",
				ToActorId:      "actor-2",
				Type:           pb.RecurringPaymentType_UPI_MANDATES,
				PiFrom:         "pi-1",
				PiTo:           "pi-2",
				Amount:         money.ZeroINR().Pb,
				AmountType:     pb.AmountType_MAXIMUM,
				Interval: &types.Interval{
					StartTime: startDate,
					EndTime:   endDate2,
				},
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_ONE_TIME,
				},
				PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
				Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
				Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
				UiEntryPoint:             pb.UIEntryPoint_FIT,
				ClientRequestId:          "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				InitiatedBy:   pb.InitiatedBy_PAYER,
				TransactionId: "txn-id-1",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-1",
					OtherActorId:   "actor-2",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-2",
					OtherActorId:   "actor-1",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{Status: rpc.StatusRecordNotFound()}, nil)
			},
			want: &pb.CreateRecurringPaymentResponse{Status: rpc.NewStatusWithoutDebug(uint32(pb.CreateRecurringPaymentResponse_INVALID_START_AND_END_DATE),
				"invalid start and end date for recurring payment")},
			wantErr: false,
		},
		{
			name: "error in validating creation due to invalid start and end date for mandate",
			req: &pb.CreateRecurringPaymentRequest{
				FromActorId: "actor-1",
				ToActorId:   "actor-2",
				Type:        pb.RecurringPaymentType_UPI_MANDATES,
				PiFrom:      "pi-1",
				PiTo:        "pi-2",
				Amount:      money.ZeroINR().Pb,
				AmountType:  pb.AmountType_MAXIMUM,
				Interval: &types.Interval{
					StartTime: endDate2,
					EndTime:   startDate,
				},
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_ONE_TIME,
				},
				PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
				Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
				Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
				UiEntryPoint:             pb.UIEntryPoint_FIT,
				ClientRequestId:          "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				InitiatedBy:   pb.InitiatedBy_PAYER,
				TransactionId: "txn-id-1",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-1",
					OtherActorId:   "actor-2",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-2",
					OtherActorId:   "actor-1",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{Status: rpc.StatusRecordNotFound()}, nil)
			},
			want: &pb.CreateRecurringPaymentResponse{Status: rpc.NewStatusWithoutDebug(uint32(pb.CreateRecurringPaymentResponse_INVALID_START_AND_END_DATE),
				"invalid start and end date for recurring payment")},
			wantErr: false,
		},
		{
			name: "created mandate successfully",
			req: &pb.CreateRecurringPaymentRequest{
				CurrentActorId: "actor-1",
				FromActorId:    "actor-1",
				ToActorId:      "actor-2",
				Type:           pb.RecurringPaymentType_UPI_MANDATES,
				PiFrom:         "pi-1",
				PiTo:           "pi-2",
				Amount:         money.ZeroINR().Pb,
				AmountType:     pb.AmountType_MAXIMUM,
				Interval: &types.Interval{
					StartTime: startDate,
					EndTime:   endDate,
				},
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
					Day:              5,
				},
				MaximumAllowedTxns:       10,
				PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				PreferredPaymentProtocol: paymentPb.PaymentProtocol_UPI,
				Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
				Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
				UiEntryPoint:             pb.UIEntryPoint_FIT,
				ClientRequestId:          "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				InitiatedBy:   pb.InitiatedBy_PAYER,
				TransactionId: "txn-id-1",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-1",
					OtherActorId:   "actor-2",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-2",
					OtherActorId:   "actor-1",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentActionByClientReqId(gomock.Any(), "client-req-1").Return(nil, rpc.StatusAsError(rpc.StatusRecordNotFound()))
				mockRecurringPaymentDao.EXPECT().Create(gomock.Any(), &pb.RecurringPayment{
					FromActorId: "actor-1",
					ToActorId:   "actor-2",
					Type:        pb.RecurringPaymentType_UPI_MANDATES,
					PiFrom:      "pi-1",
					PiTo:        "pi-2",
					Amount:      money.ZeroINR().Pb,
					AmountType:  pb.AmountType_MAXIMUM,
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule: &pb.RecurrenceRule{
						AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
						Day:              5,
					},
					MaximumAllowedTxns:       10,
					PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
					PreferredPaymentProtocol: paymentPb.PaymentProtocol_UPI,
					State:                    pb.RecurringPaymentState_CREATION_QUEUED,
					Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
					Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
					UiEntryPoint:             pb.UIEntryPoint_FIT,
					InitiatedBy:              pb.InitiatedBy_PAYER,
				}, commontypes.Ownership_EPIFI_TECH).Return(recurringPaymentMandate, nil)
				mockOrderClient.EXPECT().CreateOrder(gomock.Any(), &orderPb.CreateOrderRequest{
					ActorFrom:    "actor-1",
					ActorTo:      "actor-2",
					Workflow:     orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT,
					Provenance:   orderPb.OrderProvenance_USER_APP,
					OrderPayload: marshalledOrderPayload,
					Amount:       money.ZeroINR().Pb,
					Status:       orderPb.OrderStatus_CREATED,
					UiEntryPoint: orderPb.UIEntryPoint_FITTT,
					ClientReqId:  "client-req-1",
				}).Return(&orderPb.CreateOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:          "order-id-1",
						ClientReqId: "client-req-1",
					},
				}, nil)
				mockMandateClient.EXPECT().GetMandateRequestParameters(gomock.Any(), &upiMandatePb.GetMandateRequestParametersRequest{
					ReqId: "txn-id-1",
				}).Return(&upiMandatePb.GetMandateRequestParametersResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "id-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_CREATE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "txn-id-1",
				}).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "id-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_CREATE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "txn-id-1",
				}, nil)
				mockMandateClient.EXPECT().CreateMandate(gomock.Any(), &upiMandatePb.CreateMandateRequest{
					RecurringPaymentId: "id-1",
					InitiatedBy:        upiMandatePb.MandateInitiatedBy_PAYER,
					CurrentActorRole:   upiMandatePb.ActorRole_ACTOR_ROLE_PAYER,
					PartnerBank:        commonvgpb.Vendor_FEDERAL_BANK,
					ReqId:              "txn-id-1",
					CurrentActorId:     "actor-1",
				}).Return(&upiMandatePb.CreateMandateResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_UPI_MANDATES,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(true, pb.CredBlockType_NPCI, nil)
				mockAccountPiClient.EXPECT().GetByPiId(gomock.Any(), &accountPiPb.GetByPiIdRequest{PiId: "pi-1"}).Return(&accountPiPb.GetByPiIdResponse{
					Status:    rpc.StatusOk(),
					AccountId: "account-id-1",
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "payer@vpa",
							},
						},
						VerifiedName: "abc",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockActor.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor-2"}).Return(
					&actorPb.GetEntityDetailsByActorIdResponse{Name: &commontypes.Name{FirstName: "payee-name"}, Status: rpc.StatusOk()}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-2"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "payee@vpa",
							},
						},
						VerifiedName: "xyz",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
			},
			want: &pb.CreateRecurringPaymentResponse{
				Status:                   rpc.StatusOk(),
				OrderId:                  "order-id-1",
				RecurringPaymentId:       "id-1",
				IsAuthenticationRequired: true,
				CredBlockType:            pb.CredBlockType_NPCI,
				TransactionId:            "txn-id-1",
				TransactionAttributes: []*pb.TransactionAttribute{
					{
						PayerAccountId:                "account-id-1",
						TransactionId:                 "txn-id-1",
						PaymentProtocol:               paymentPb.PaymentProtocol_UPI,
						PayeeActorName:                "payee-name",
						Amount:                        money.ZeroINR().Pb,
						PayerPaymentInstrument:        "payer@vpa",
						PayeePaymentInstrument:        "payee@vpa",
						DisplayPayeePaymentInstrument: "payee@vpa",
					},
				},
				NextAction: &deeplink.Deeplink{
					Screen: deeplink.Screen_FEDERAL_SECURE_PIN,
					ScreenOptions: &deeplink.Deeplink_FederalSecurePinScreenOptions{
						FederalSecurePinScreenOptions: &deeplink.FederalSecurePinScreenOptions{
							TxnAttributes: adaptor.ConvertToDeepLinkTransactionAttributes(&pb.TransactionAttribute{
								PayerAccountId:                "account-id-1",
								TransactionId:                 "txn-id-1",
								PaymentProtocol:               paymentPb.PaymentProtocol_UPI,
								PayeeActorName:                "payee-name",
								Amount:                        money.ZeroINR().Pb,
								PayerPaymentInstrument:        "payer@vpa",
								PayeePaymentInstrument:        "payee@vpa",
								DisplayPayeePaymentInstrument: "payee@vpa",
							},
								"client-req-1"),
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "error while fetching workflow for client req id",
			req: &pb.CreateRecurringPaymentRequest{
				ClientRequestId: "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				InitiatedBy: pb.InitiatedBy_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "",
					OtherActorId:   "",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "",
					OtherActorId:   "",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).Return(
					nil, rpc.StatusAsError(rpc.StatusInternal()))
			},
			want: &pb.CreateRecurringPaymentResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "workflow exists with client request id",
			req: &pb.CreateRecurringPaymentRequest{
				ClientRequestId: "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				InitiatedBy: pb.InitiatedBy_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "",
					OtherActorId:   "",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "",
					OtherActorId:   "",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).Return(
					&celestialPb.WorkflowRequest{
						Id:      "workflow-req-id",
						ActorId: "",
						Stage:   workflowPb.Stage_CREATION,
						Status:  stagePb.Status_INITIATED,
						Version: workflowPb.Version_V1,
						Type:    workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER,
						Payload: marshalledOrderPayload,
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client-req-1",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentActionByClientReqId(gomock.Any(), "client-req-1").Return(&pb.RecurringPaymentsAction{
					Id:                 "id",
					RecurringPaymentId: "id-1",
					ClientRequestId:    "client-req-1",
				}, nil)
			},
			want: &pb.CreateRecurringPaymentResponse{
				Status:             rpc.StatusAlreadyExists(),
				RecurringPaymentId: "id-1",
			},
			wantErr: false,
		},
		{
			name: "error in initiating workflow",
			req: &pb.CreateRecurringPaymentRequest{
				CurrentActorId: "actor-1",
				FromActorId:    "actor-1",
				ToActorId:      "actor-2",
				Type:           pb.RecurringPaymentType_STANDING_INSTRUCTION,
				PiFrom:         "pi-1",
				PiTo:           "pi-2",
				Amount:         money.ZeroINR().Pb,
				AmountType:     pb.AmountType_MAXIMUM,
				Interval: &types.Interval{
					StartTime: startDate,
					EndTime:   endDate,
				},
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
					Day:              5,
				},
				MaximumAllowedTxns:       10,
				PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
				Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
				Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
				UiEntryPoint:             pb.UIEntryPoint_FIT,
				ClientRequestId:          "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				InitiatedBy:   pb.InitiatedBy_PAYER,
				TransactionId: "txn-id-1",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-1",
					OtherActorId:   "actor-2",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-2",
					OtherActorId:   "actor-1",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).Return(nil, rpc.StatusAsError(rpc.StatusRecordNotFound()))
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentActionByClientReqId(gomock.Any(), "client-req-1").Return(nil, rpc.StatusAsError(rpc.StatusRecordNotFound()))
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_STANDING_INSTRUCTION,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(true, pb.CredBlockType_PARTNER_SDK, nil)
				mockRecurringPaymentDao.EXPECT().Create(gomock.Any(), &pb.RecurringPayment{
					FromActorId: "actor-1",
					ToActorId:   "actor-2",
					Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
					PiFrom:      "pi-1",
					PiTo:        "pi-2",
					Amount:      money.ZeroINR().Pb,
					AmountType:  pb.AmountType_MAXIMUM,
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule: &pb.RecurrenceRule{
						AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
						Day:              5,
					},
					MaximumAllowedTxns:       10,
					PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
					PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					State:                    pb.RecurringPaymentState_CREATION_QUEUED,
					Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
					Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
					UiEntryPoint:             pb.UIEntryPoint_FIT,
					InitiatedBy:              pb.InitiatedBy_PAYER,
				}, commontypes.Ownership_EPIFI_TECH).Return(recurringPayment, nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), newCreateActionReqMatcher(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "id-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_CREATE,
					State:              pb.ActionState_ACTION_CREATED,
				})).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "id-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_CREATE,
				}, nil)
				mockSIClient.EXPECT().Create(gomock.Any(), newCreateSIReqMatcher(&siPb.CreateRequest{
					RecurringPaymentId: "id-1",
					Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
					RequestId:          "txn-id-1",
				})).Return(&siPb.CreateResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				}, "actor-1", rpPayload, celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER), workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT).Return(
					rpc.StatusAsError(rpc.StatusInternal()),
				)
			},
			want:    &pb.CreateRecurringPaymentResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "error in signaling workflow",
			req: &pb.CreateRecurringPaymentRequest{
				FromActorId: "actor-1",
				ToActorId:   "actor-2",
				Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
				PiFrom:      "pi-1",
				PiTo:        "pi-2",
				Amount:      money.ZeroINR().Pb,
				AmountType:  pb.AmountType_MAXIMUM,
				Interval: &types.Interval{
					StartTime: startDate,
					EndTime:   endDate,
				},
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
					Day:              5,
				},
				MaximumAllowedTxns:       10,
				PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
				Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
				Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
				UiEntryPoint:             pb.UIEntryPoint_FIT,
				ClientRequestId:          "client-req-1",
				InitiatedBy:              pb.InitiatedBy_PAYER,
				Payload:                  nil,
				CurrentActorId:           "actor-1",
				TransactionId:            "txn-id-1",
				Remarks:                  "",
				ShareToPayee:             false,
				Expiry:                   nil,
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-1",
					OtherActorId:   "actor-2",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-2",
					OtherActorId:   "actor-1",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).Return(nil, rpc.StatusAsError(rpc.StatusRecordNotFound()))
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentActionByClientReqId(gomock.Any(), "client-req-1").Return(nil, rpc.StatusAsError(rpc.StatusRecordNotFound()))
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_STANDING_INSTRUCTION,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(true, pb.CredBlockType_PARTNER_SDK, nil)
				mockRecurringPaymentDao.EXPECT().Create(gomock.Any(), &pb.RecurringPayment{
					FromActorId: "actor-1",
					ToActorId:   "actor-2",
					Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
					PiFrom:      "pi-1",
					PiTo:        "pi-2",
					Amount:      money.ZeroINR().Pb,
					AmountType:  pb.AmountType_MAXIMUM,
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule: &pb.RecurrenceRule{
						AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
						Day:              5,
					},
					MaximumAllowedTxns:       10,
					PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
					PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					State:                    pb.RecurringPaymentState_CREATION_QUEUED,
					Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
					Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
					UiEntryPoint:             pb.UIEntryPoint_FIT,
					InitiatedBy:              pb.InitiatedBy_PAYER,
				}, commontypes.Ownership_EPIFI_TECH).Return(&pb.RecurringPayment{
					Id:          "id-1",
					FromActorId: "actor-1",
					ToActorId:   "actor-2",
					Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
					PiFrom:      "pi-1",
					PiTo:        "pi-2",
					Amount:      money.ZeroINR().Pb,
					AmountType:  pb.AmountType_MAXIMUM,
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule: &pb.RecurrenceRule{
						AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
						Day:              5,
					},
					MaximumAllowedTxns:       10,
					PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
					PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					State:                    pb.RecurringPaymentState_CREATION_AUTHORISED,
					Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
					Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
					UiEntryPoint:             pb.UIEntryPoint_FIT,
					InitiatedBy:              pb.InitiatedBy_PAYER,
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), newCreateActionReqMatcher(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "id-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_CREATE,
					State:              pb.ActionState_ACTION_CREATED,
				})).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "id-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_CREATE,
				}, nil)
				mockSIClient.EXPECT().Create(gomock.Any(), newCreateSIReqMatcher(&siPb.CreateRequest{
					RecurringPaymentId: "id-1",
					Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
					RequestId:          "txn-id-1",
				})).Return(&siPb.CreateResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				}, "actor-1", rpPayload, celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER), workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT).Return(
					nil,
				)
				mockCelestialProcessor.EXPECT().SignalWorkflow(gomock.Any(),
					"client-req-1",
					string(rpNs.CreateRecurringPaymentAuthSignal),
					workflowPb.Client_RECURRING_PAYMENT, payload, gomock.Any(), gomock.Any()).
					Return(rpc.StatusAsError(rpc.StatusInternal()))
			},
			want:    &pb.CreateRecurringPaymentResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "created recurring payment successfully using celestial flow",
			req: &pb.CreateRecurringPaymentRequest{
				CurrentActorId: "actor-1",
				FromActorId:    "actor-1",
				ToActorId:      "actor-2",
				Type:           pb.RecurringPaymentType_STANDING_INSTRUCTION,
				PiFrom:         "pi-1",
				PiTo:           "pi-2",
				Amount:         money.ZeroINR().Pb,
				AmountType:     pb.AmountType_MAXIMUM,
				Interval: &types.Interval{
					StartTime: startDate,
					EndTime:   endDate,
				},
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
					Day:              5,
				},
				MaximumAllowedTxns:       10,
				PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
				Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
				Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
				UiEntryPoint:             pb.UIEntryPoint_FIT,
				ClientRequestId:          "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				InitiatedBy:   pb.InitiatedBy_PAYER,
				TransactionId: "txn-id-1",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-1",
					OtherActorId:   "actor-2",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-2",
					OtherActorId:   "actor-1",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).Return(nil, rpc.StatusAsError(rpc.StatusRecordNotFound()))
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentActionByClientReqId(gomock.Any(), "client-req-1").Return(nil, rpc.StatusAsError(rpc.StatusRecordNotFound()))
				mockRecurringPaymentDao.EXPECT().Create(gomock.Any(), &pb.RecurringPayment{
					FromActorId: "actor-1",
					ToActorId:   "actor-2",
					Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
					PiFrom:      "pi-1",
					PiTo:        "pi-2",
					Amount:      money.ZeroINR().Pb,
					AmountType:  pb.AmountType_MAXIMUM,
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule: &pb.RecurrenceRule{
						AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
						Day:              5,
					},
					MaximumAllowedTxns:       10,
					PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
					PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					State:                    pb.RecurringPaymentState_CREATION_QUEUED,
					Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
					Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
					UiEntryPoint:             pb.UIEntryPoint_FIT,
					InitiatedBy:              pb.InitiatedBy_PAYER,
				}, commontypes.Ownership_EPIFI_TECH).Return(recurringPayment, nil)
				mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				}, "actor-1", rpPayload, celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER), workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT).Return(
					nil,
				)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "id-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_CREATE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "txn-id-1",
				}).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "id-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_CREATE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "txn-id-1",
				}, nil)
				mockSIClient.EXPECT().Create(gomock.Any(), &siPb.CreateRequest{
					RecurringPaymentId: "id-1",
					Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
					RequestId:          "txn-id-1",
				}).Return(&siPb.CreateResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_STANDING_INSTRUCTION,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(true, pb.CredBlockType_PARTNER_SDK, nil)
				mockAccountPiClient.EXPECT().GetByPiId(gomock.Any(), &accountPiPb.GetByPiIdRequest{PiId: "pi-1"}).Return(&accountPiPb.GetByPiIdResponse{
					Status:    rpc.StatusOk(),
					AccountId: "account-id-1",
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: "**********",
								SecureAccountNumber: "xxxxxx7890",
								IfscCode:            "FEDR0000",
								AccountType:         accounts.Type_SAVINGS,
								Name:                "test-actor-1",
							},
						},
						VerifiedName: "abc",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-2"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: "**********",
								SecureAccountNumber: "xxxxxx4321",
								IfscCode:            "FEDR0000",
								AccountType:         accounts.Type_SAVINGS,
								Name:                "test-actor-2",
							},
						},
						VerifiedName: "xyz",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockActor.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor-2"}).Return(
					&actorPb.GetEntityDetailsByActorIdResponse{Name: &commontypes.Name{FirstName: "actorName"}, Status: rpc.StatusOk()}, nil)
			},
			want: &pb.CreateRecurringPaymentResponse{
				Status:                   rpc.StatusOk(),
				RecurringPaymentId:       "id-1",
				IsAuthenticationRequired: true,
				CredBlockType:            pb.CredBlockType_PARTNER_SDK,
				TransactionId:            "txn-id-1",
				TransactionAttributes: []*pb.TransactionAttribute{
					{
						PayerAccountId:                "account-id-1",
						TransactionId:                 "txn-id-1",
						PaymentProtocol:               paymentPb.PaymentProtocol_INTRA_BANK,
						PayeeActorName:                "actorName",
						PayerMaskedAccountNumber:      "xxxxxx7890",
						Amount:                        money.ZeroINR().Pb,
						PayerPaymentInstrument:        "xxxxxx7890",
						PayeePaymentInstrument:        "xxxxxx4321",
						DisplayPayeePaymentInstrument: "xxxxxx4321",
					},
				},
				NextAction: &deeplink.Deeplink{
					Screen: deeplink.Screen_FEDERAL_SECURE_PIN,
					ScreenOptions: &deeplink.Deeplink_FederalSecurePinScreenOptions{
						FederalSecurePinScreenOptions: &deeplink.FederalSecurePinScreenOptions{
							TxnAttributes: adaptor.ConvertToDeepLinkTransactionAttributes(&pb.TransactionAttribute{
								PayerAccountId:                "account-id-1",
								TransactionId:                 "txn-id-1",
								PaymentProtocol:               paymentPb.PaymentProtocol_INTRA_BANK,
								PayeeActorName:                "actorName",
								PayerMaskedAccountNumber:      "xxxxxx7890",
								Amount:                        money.ZeroINR().Pb,
								PayerPaymentInstrument:        "xxxxxx7890",
								PayeePaymentInstrument:        "xxxxxx4321",
								DisplayPayeePaymentInstrument: "xxxxxx4321",
							},
								"client-req-1"),
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "error in validating creation due to amount limit exceeded for mcc and purpose",
			req: &pb.CreateRecurringPaymentRequest{
				CurrentActorId: "actor-1",
				FromActorId:    "actor-1",
				ToActorId:      "actor-2",
				Type:           pb.RecurringPaymentType_UPI_MANDATES,
				PiFrom:         "pi-1",
				PiTo:           "pi-2",
				Amount: &moneyPb.Money{
					Units: 600000,
					Nanos: 0,
				},
				AmountType: pb.AmountType_MAXIMUM,
				Interval: &types.Interval{
					StartTime: startDate,
					EndTime:   endDate,
				},
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_ONE_TIME,
				},
				PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
				Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
				Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
				UiEntryPoint:             pb.UIEntryPoint_FIT,
				ClientRequestId:          "client-req-1",
				InitiatedBy:              pb.InitiatedBy_PAYER,
				Payload:                  mandatePayload,
				TransactionId:            "txn-id-1",
				ShareToPayee:             false,
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-1",
					OtherActorId:   "actor-2",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-2",
					OtherActorId:   "actor-1",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{Status: rpc.StatusRecordNotFound()}, nil)
			},
			want: &pb.CreateRecurringPaymentResponse{Status: rpc.NewStatusWithoutDebug(uint32(pb.CreateRecurringPaymentResponse_AMOUNT_LIMIT_EXCEEDED),
				"amount limit exceeded for mandate creation")},
			wantErr: false,
		},
		{
			name: "create recurring payment with postAuthDeeplink and postExecuteDeeplinks passed in input",
			req: &pb.CreateRecurringPaymentRequest{
				CurrentActorId: "actor-1",
				FromActorId:    "actor-1",
				ToActorId:      "actor-2",
				Type:           pb.RecurringPaymentType_STANDING_INSTRUCTION,
				PiFrom:         "pi-1",
				PiTo:           "pi-2",
				Amount:         money.ZeroINR().Pb,
				AmountType:     pb.AmountType_MAXIMUM,
				Interval: &types.Interval{
					StartTime: startDate,
					EndTime:   endDate,
				},
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
					Day:              5,
				},
				MaximumAllowedTxns:       10,
				PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
				PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
				Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
				Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
				UiEntryPoint:             pb.UIEntryPoint_FIT,
				ClientRequestId:          "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				InitiatedBy:                          pb.InitiatedBy_PAYER,
				TransactionId:                        "txn-id-1",
				PostAuthorisationAction:              postAuthDeeplink,
				PostRecurringPaymentCreationDeeplink: postActionDeeplink,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-1",
					OtherActorId:   "actor-2",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActor.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "actor-2",
					OtherActorId:   "actor-1",
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).Return(nil, rpc.StatusAsError(rpc.StatusRecordNotFound()))
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentActionByClientReqId(gomock.Any(), "client-req-1").Return(nil, rpc.StatusAsError(rpc.StatusRecordNotFound()))
				mockRecurringPaymentDao.EXPECT().Create(gomock.Any(), &pb.RecurringPayment{
					FromActorId: "actor-1",
					ToActorId:   "actor-2",
					Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
					PiFrom:      "pi-1",
					PiTo:        "pi-2",
					Amount:      money.ZeroINR().Pb,
					AmountType:  pb.AmountType_MAXIMUM,
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule: &pb.RecurrenceRule{
						AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
						Day:              5,
					},
					MaximumAllowedTxns:       10,
					PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
					PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					State:                    pb.RecurringPaymentState_CREATION_QUEUED,
					Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
					Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
					UiEntryPoint:             pb.UIEntryPoint_FIT,
					InitiatedBy:              pb.InitiatedBy_PAYER,
				}, commontypes.Ownership_EPIFI_TECH).Return(recurringPayment, nil)
				mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				}, "actor-1", rpPayload, celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER), workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT).Return(
					nil,
				)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "id-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_CREATE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "txn-id-1",
					PostAuthDeeplink:   postAuthDeeplinkAny,
					PostActionDeeplink: postActionDeeplinkAny,
				}).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "id-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_CREATE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "txn-id-1",
					PostAuthDeeplink:   postAuthDeeplinkAny,
					PostActionDeeplink: postActionDeeplinkAny,
				}, nil)
				mockSIClient.EXPECT().Create(gomock.Any(), &siPb.CreateRequest{
					RecurringPaymentId: "id-1",
					Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
					RequestId:          "txn-id-1",
				}).Return(&siPb.CreateResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_STANDING_INSTRUCTION,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(true, pb.CredBlockType_PARTNER_SDK, nil)
				mockAccountPiClient.EXPECT().GetByPiId(gomock.Any(), &accountPiPb.GetByPiIdRequest{PiId: "pi-1"}).Return(&accountPiPb.GetByPiIdResponse{
					Status:    rpc.StatusOk(),
					AccountId: "account-id-1",
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: "**********",
								SecureAccountNumber: "xxxxxx7890",
								IfscCode:            "FEDR0000",
								AccountType:         accounts.Type_SAVINGS,
								Name:                "test-actor-1",
							},
						},
						VerifiedName: "abc",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-2"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: "**********",
								SecureAccountNumber: "xxxxxx4321",
								IfscCode:            "FEDR0000",
								AccountType:         accounts.Type_SAVINGS,
								Name:                "test-actor-2",
							},
						},
						VerifiedName: "xyz",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockActor.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor-2"}).Return(
					&actorPb.GetEntityDetailsByActorIdResponse{Name: &commontypes.Name{FirstName: "actorName"}, Status: rpc.StatusOk()}, nil)
			},
			want: &pb.CreateRecurringPaymentResponse{
				Status:                   rpc.StatusOk(),
				RecurringPaymentId:       "id-1",
				IsAuthenticationRequired: true,
				CredBlockType:            pb.CredBlockType_PARTNER_SDK,
				TransactionId:            "txn-id-1",
				TransactionAttributes: []*pb.TransactionAttribute{
					{
						PayerAccountId:                "account-id-1",
						TransactionId:                 "txn-id-1",
						PaymentProtocol:               paymentPb.PaymentProtocol_INTRA_BANK,
						PayeeActorName:                "actorName",
						PayerMaskedAccountNumber:      "xxxxxx7890",
						Amount:                        money.ZeroINR().Pb,
						PayerPaymentInstrument:        "xxxxxx7890",
						PayeePaymentInstrument:        "xxxxxx4321",
						DisplayPayeePaymentInstrument: "xxxxxx4321",
					},
				},
				NextAction: &deeplink.Deeplink{
					Screen: deeplink.Screen_FEDERAL_SECURE_PIN,
					ScreenOptions: &deeplink.Deeplink_FederalSecurePinScreenOptions{
						FederalSecurePinScreenOptions: &deeplink.FederalSecurePinScreenOptions{
							TxnAttributes: adaptor.ConvertToDeepLinkTransactionAttributes(&pb.TransactionAttribute{
								PayerAccountId:                "account-id-1",
								TransactionId:                 "txn-id-1",
								PaymentProtocol:               paymentPb.PaymentProtocol_INTRA_BANK,
								PayeeActorName:                "actorName",
								PayerMaskedAccountNumber:      "xxxxxx7890",
								Amount:                        money.ZeroINR().Pb,
								PayerPaymentInstrument:        "xxxxxx7890",
								PayeePaymentInstrument:        "xxxxxx4321",
								DisplayPayeePaymentInstrument: "xxxxxx4321",
							},
								"client-req-1"),
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.CreateRecurringPayment(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateRecurringPayment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("CreateRecurringPayment() got = %v, want %v", got, tt.want)
			}
		})
	}
}

type createSIReqMatcher struct {
	want *siPb.CreateRequest
}

func newCreateSIReqMatcher(want *siPb.CreateRequest) *createSIReqMatcher {
	return &createSIReqMatcher{
		want: want,
	}
}

func (ce *createSIReqMatcher) Matches(x interface{}) bool {
	got, ok := x.(*siPb.CreateRequest)
	if !ok {
		return false
	}

	ce.want.RequestId = got.RequestId
	return reflect.DeepEqual(ce.want, got)
}

func (ce *createSIReqMatcher) String() string {
	return fmt.Sprintf("want: %v", ce.want)
}

type createActionReqMatcher struct {
	want *pb.RecurringPaymentsAction
}

func newCreateActionReqMatcher(want *pb.RecurringPaymentsAction) *createActionReqMatcher {
	return &createActionReqMatcher{
		want: want,
	}
}

func (ce *createActionReqMatcher) Matches(x interface{}) bool {
	got, ok := x.(*pb.RecurringPaymentsAction)
	if !ok {
		return false
	}

	ce.want.VendorRequestId = got.VendorRequestId
	return reflect.DeepEqual(ce.want, got)
}

func (ce *createActionReqMatcher) String() string {
	return fmt.Sprintf("want: %v", ce.want)
}

func TestService_ProcessRecurringPaymentsNotification(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockActorClient := actorMocks.NewMockActorClient(ctr)
	mockCommsClient := commsMocks.NewMockCommsClient(ctr)
	mockOrderClient := orderMocks.NewMockOrderServiceClient(ctr)
	mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
	mockRecurringPaymentActionDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
	mockRecurringPaymentProcessor := mocks2.NewMockRecurringPaymentProcessor(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)

	s := NewService(mockRecurringPaymentDao, mockOrderClient, nil, mockRecurringPaymentActionDao, nil, mockActorClient, nil, nil, nil, conf, mockRecurringPaymentProcessor, nil, nil, mockPiClient, nil, nil, mockCommsClient, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	currTime := time.Now()
	currDate := datetime.DateToString(datetime.TimeToDateInLoc(currTime, datetime.IST), "January 2, 2006", datetime.IST)

	tests := []struct {
		name              string
		recrurringPayment *pb.RecurringPayment
		rpcinfo           *pb.RecurringPaymentCreationInfo
		rpcexec           *pb.RecurringPaymentExecutionInfo
		rpcrev            *pb.RecurringPaymentRevokeInfo
		rpcmod            *pb.RecurringPaymentModifyInfo
		rpcpup            *pb.RecurringPaymentPauseUnpauseInfo
		rpAction          *pb.RecurringPaymentsAction
		req               *orderPb.OrderUpdate
		toActor           *actorPb.GetActorByIdResponse
		fromEntity        *actorPb.GetEntityDetailsByActorIdResponse
		toEntity          *actorPb.GetEntityDetailsByActorIdResponse
		setupMockCalls    func(*orderPb.OrderUpdate, *pb.RecurringPayment, *pb.RecurringPaymentCreationInfo,
			*pb.RecurringPaymentsAction, *actorPb.GetEntityDetailsByActorIdResponse, *actorPb.GetEntityDetailsByActorIdResponse, *actorPb.GetActorByIdResponse)
		want    *pb.ProcessRecurringPaymentsNotificationResponse
		wantErr bool
	}{
		// SI created
		{
			name: "SI created",
			recrurringPayment: &pb.RecurringPayment{
				Id:          "rp-1",
				Amount:      money.ZeroINR().Pb,
				Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
				PiTo:        "piyush@vpa",
				InitiatedBy: pb.InitiatedBy_PAYEE,
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_WEEKLY,
				},
			},
			rpcinfo: &pb.RecurringPaymentCreationInfo{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				ReqId:              "req-id-1",
			},
			toActor: &actorPb.GetActorByIdResponse{Status: rpc.StatusOk(),
				Actor: &types.Actor{
					Type: types.Actor_USER,
				},
			},
			fromEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett1",
				Name: &commontypes.Name{FirstName: "Tushar"}},
			toEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett2",
				Name: &commontypes.Name{FirstName: "Piyush"}},
			req: &orderPb.OrderUpdate{
				OrderWithTransactions: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:          "Order-1",
						FromActorId: "Actor-1",
						ToActorId:   "Actor-2",
						Status:      orderPb.OrderStatus_FULFILLED,
						Workflow:    orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT,
						ClientReqId: "client-req-1",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id: "Transaction-1",
						},
					},
				},
			},
			setupMockCalls: func(ou *orderPb.OrderUpdate, rpo *pb.RecurringPayment, rpci *pb.RecurringPaymentCreationInfo,
				rpa *pb.RecurringPaymentsAction, fme *actorPb.GetEntityDetailsByActorIdResponse, toe *actorPb.GetEntityDetailsByActorIdResponse, toActor *actorPb.GetActorByIdResponse) {

				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), rpo.GetId()).Return(rpo, nil).Times(1)

				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: ou.GetOrderWithTransactions().GetOrder().GetToActorId()}).Return(toActor, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetFromActorId()}).
					Return(fme, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetToActorId()}).
					Return(toe, nil).Times(1)

				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{
					Id: rpo.GetPiTo(),
				}).Return(&piPb.GetPiByIdResponse{Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{Account: &piPb.Account{
							SecureAccountNumber: "xxx00",
						}},
					}}, nil)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), newCreateMatch(&commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_NOTIFICATION,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Notification{
						Notification: &commspb.NotificationMessage{
							Notification: &fcmpb.Notification{
								NotificationType: fcmpb.NotificationType_SYSTEM_TRAY,
								NotificationTemplates: &fcmpb.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcmpb.SystemTrayTemplate{
									CommonTemplateFields: &fcmpb.CommonTemplateFields{
										Title: "We're ready to roll! 🏄🏻‍♀️",
										Body:  "Your auto-payment rule has been created. INR 0.00 will be sent to Piyush. It will occur weekly.",
										// Body:  "Piyush wants to collect INR 0.00 for UPI-AutoPay. Tap to open Fi & grant this!",
										IconAttributes: &fcmpb.IconAttributes{
											IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png",
										},
										Deeplink: &deeplink.Deeplink{
											Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
											ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
												RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
													RecurringPaymentId: "rp-1",
												},
											},
										},
										ExpireAt: timestamppb.New(time.Now().Add(time.Hour)),
									},
								},
								},
							},
						},
					},
				})).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), &commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_SMS,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Sms{
						Sms: &commspb.SMSMessage{
							SmsOption: &commspb.SmsOption{
								Option: &commspb.SmsOption_SiCreatedSmsOption{
									SiCreatedSmsOption: &commspb.SICreatedSmsOption{
										SmsType: commspb.SmsType_SI_CREATED,
										Option: &commspb.SICreatedSmsOption_SiCreatedV1{
											SiCreatedV1: &commspb.SICreatedSmsOptionV1{
												TemplateVersion: commspb.TemplateVersion_VERSION_V1,
												SiAmount:        rpo.GetAmount(),
												SiFrequency:     "weekly",
												PayeeName:       toe.GetName(),
											},
										},
									},
								},
							},
						},
					},
				}).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

			},
			want: &pb.ProcessRecurringPaymentsNotificationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		// SI declined
		{
			name: "SI declined",
			recrurringPayment: &pb.RecurringPayment{
				Id:          "rp-1",
				Amount:      money.ZeroINR().Pb,
				Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
				PiTo:        "piyush@vpa",
				InitiatedBy: pb.InitiatedBy_PAYEE,
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_WEEKLY,
				},
			},
			rpcinfo: &pb.RecurringPaymentCreationInfo{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				ReqId:              "req-id-1",
			},
			toActor: &actorPb.GetActorByIdResponse{Status: rpc.StatusOk(),
				Actor: &types.Actor{
					Type: types.Actor_USER,
				},
			},
			fromEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett1",
				Name: &commontypes.Name{FirstName: "Tushar"}},
			toEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett2",
				Name: &commontypes.Name{FirstName: "Piyush"}},
			req: &orderPb.OrderUpdate{
				OrderWithTransactions: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:          "Order-1",
						FromActorId: "Actor-1",
						ToActorId:   "Actor-2",
						Status:      orderPb.OrderStatus_FULFILLMENT_FAILED,
						Workflow:    orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT,
						ClientReqId: "client-req-1",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id: "Transaction-1",
						},
					},
				},
			},
			setupMockCalls: func(ou *orderPb.OrderUpdate, rpo *pb.RecurringPayment, rpci *pb.RecurringPaymentCreationInfo,
				rpa *pb.RecurringPaymentsAction, fme *actorPb.GetEntityDetailsByActorIdResponse, toe *actorPb.GetEntityDetailsByActorIdResponse, toActor *actorPb.GetActorByIdResponse) {

				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), rpo.GetId()).Return(rpo, nil).Times(1)

				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: ou.GetOrderWithTransactions().GetOrder().GetToActorId()}).Return(toActor, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetFromActorId()}).
					Return(fme, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetToActorId()}).
					Return(toe, nil).Times(1)

				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{
					Id: rpo.GetPiTo(),
				}).Return(&piPb.GetPiByIdResponse{Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{Account: &piPb.Account{
							SecureAccountNumber: "xxx00",
						}},
					}}, nil)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), newCreateMatch(&commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_NOTIFICATION,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Notification{
						Notification: &commspb.NotificationMessage{
							Notification: &fcmpb.Notification{
								NotificationType: fcmpb.NotificationType_SYSTEM_TRAY,
								NotificationTemplates: &fcmpb.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcmpb.SystemTrayTemplate{
									CommonTemplateFields: &fcmpb.CommonTemplateFields{
										Title: "⚠️ Something went wrong.️",
										Body:  "Your instructions to send INR 0.00 to user could not be created. Try again in some time?",
										IconAttributes: &fcmpb.IconAttributes{
											IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png",
										},
										Deeplink: &deeplink.Deeplink{
											Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
											ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
												RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
													RecurringPaymentId: "rp-1",
												},
											},
										},
										ExpireAt: timestamppb.New(time.Now().Add(time.Hour)),
									},
								},
								},
							},
						},
					},
				})).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), &commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_SMS,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Sms{
						Sms: &commspb.SMSMessage{
							SmsOption: &commspb.SmsOption{
								Option: &commspb.SmsOption_SiDeclinedSmsOption{
									SiDeclinedSmsOption: &commspb.SIDeclinedSmsOption{
										SmsType: commspb.SmsType_SI_DECLINED,
										Option: &commspb.SIDeclinedSmsOption_SiDeclinedV1{
											SiDeclinedV1: &commspb.SIDeclinedSmsOptionV1{
												TemplateVersion: commspb.TemplateVersion_VERSION_V1,
												SiAmount:        rpo.GetAmount(),
												PayeeName:       toe.GetName(),
											},
										},
									},
								},
							},
						},
					},
				}).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

			},
			want: &pb.ProcessRecurringPaymentsNotificationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "SI execution successful",
			recrurringPayment: &pb.RecurringPayment{
				Id:          "rp-1",
				Amount:      money.ZeroINR().Pb,
				Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
				PiTo:        "piyush@vpa",
				InitiatedBy: pb.InitiatedBy_PAYEE,
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_WEEKLY,
				},
			},
			rpcexec: &pb.RecurringPaymentExecutionInfo{
				RecurringPaymentId: "rp-1",
			},
			toActor: &actorPb.GetActorByIdResponse{Status: rpc.StatusOk(),
				Actor: &types.Actor{
					Type: types.Actor_USER,
				},
			},
			fromEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett1",
				Name: &commontypes.Name{FirstName: "Tushar"}},
			toEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett2",
				Name: &commontypes.Name{FirstName: "Piyush"}},
			req: &orderPb.OrderUpdate{
				OrderWithTransactions: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:          "Order-1",
						Amount:      money.ZeroINR().Pb,
						FromActorId: "Actor-1",
						ToActorId:   "Actor-2",
						Status:      orderPb.OrderStatus_PAID,
						Workflow:    orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT,
						ClientReqId: "client-req-1",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id: "Transaction-1",
						},
					},
				},
			},
			setupMockCalls: func(ou *orderPb.OrderUpdate, rpo *pb.RecurringPayment, rpci *pb.RecurringPaymentCreationInfo,
				rpa *pb.RecurringPaymentsAction, fme *actorPb.GetEntityDetailsByActorIdResponse, toe *actorPb.GetEntityDetailsByActorIdResponse, toActor *actorPb.GetActorByIdResponse) {

				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), rpo.GetId()).Return(rpo, nil).Times(1)

				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: ou.GetOrderWithTransactions().GetOrder().GetToActorId()}).Return(toActor, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetFromActorId()}).
					Return(fme, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetToActorId()}).
					Return(toe, nil).Times(1)

				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{
					Id: rpo.GetPiTo(),
				}).Return(&piPb.GetPiByIdResponse{Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{Account: &piPb.Account{
							SecureAccountNumber: "xxx00",
						}},
					}}, nil)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), newCreateMatch(&commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_NOTIFICATION,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Notification{
						Notification: &commspb.NotificationMessage{
							Notification: &fcmpb.Notification{
								NotificationType: fcmpb.NotificationType_SYSTEM_TRAY,
								NotificationTemplates: &fcmpb.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcmpb.SystemTrayTemplate{
									CommonTemplateFields: &fcmpb.CommonTemplateFields{
										Title: "3, 2, 1... Done! 💥️",
										Body:  "Based on your instructions, we just successfully transferred INR 0.00 to xxx00.",
										IconAttributes: &fcmpb.IconAttributes{
											IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png",
										},
										Deeplink: &deeplink.Deeplink{
											Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
											ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
												RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
													RecurringPaymentId: "rp-1",
												},
											},
										},
										ExpireAt: timestamppb.New(time.Now().Add(time.Hour)),
									},
								},
								},
							},
						},
					},
				})).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), &commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_SMS,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Sms{
						Sms: &commspb.SMSMessage{
							SmsOption: &commspb.SmsOption{
								Option: &commspb.SmsOption_SiExecutionSuccessfulSmsOption{
									SiExecutionSuccessfulSmsOption: &commspb.SIExecutionSuccessfulSmsOption{
										SmsType: commspb.SmsType_SI_EXECUTION_SUCCESSFUL,
										Option: &commspb.SIExecutionSuccessfulSmsOption_SiExecutionSuccessfulV1{
											SiExecutionSuccessfulV1: &commspb.SIExecutionSuccessfulSmsOptionV1{
												TemplateVersion: commspb.TemplateVersion_VERSION_V1,
												// using order amount in notification body as execution amount can be different than recurringPayment set amount
												SiAmount:        ou.GetOrderWithTransactions().GetOrder().GetAmount(),
												SiExecutionDate: currDate,
												PayeeName:       toe.GetName(),
											},
										},
									},
								},
							},
						},
					},
				}).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

			},
			want: &pb.ProcessRecurringPaymentsNotificationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "SI execution Failed",
			recrurringPayment: &pb.RecurringPayment{
				Id:          "rp-1",
				Amount:      money.ZeroINR().Pb,
				Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
				PiTo:        "piyush@vpa",
				InitiatedBy: pb.InitiatedBy_PAYEE,
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_WEEKLY,
				},
			},
			rpcexec: &pb.RecurringPaymentExecutionInfo{
				RecurringPaymentId: "rp-1",
			},
			toActor: &actorPb.GetActorByIdResponse{Status: rpc.StatusOk(),
				Actor: &types.Actor{
					Type: types.Actor_USER,
				},
			},
			fromEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett1",
				Name: &commontypes.Name{FirstName: "Tushar"}},
			toEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett2",
				Name: &commontypes.Name{FirstName: "Piyush"}},
			req: &orderPb.OrderUpdate{
				OrderWithTransactions: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:          "Order-1",
						Amount:      money.ZeroINR().Pb,
						FromActorId: "Actor-1",
						ToActorId:   "Actor-2",
						Status:      orderPb.OrderStatus_PAYMENT_FAILED,
						Workflow:    orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT,
						ClientReqId: "client-req-1",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id: "Transaction-1",
						},
					},
				},
			},
			setupMockCalls: func(ou *orderPb.OrderUpdate, rpo *pb.RecurringPayment, rpci *pb.RecurringPaymentCreationInfo,
				rpa *pb.RecurringPaymentsAction, fme *actorPb.GetEntityDetailsByActorIdResponse, toe *actorPb.GetEntityDetailsByActorIdResponse, toActor *actorPb.GetActorByIdResponse) {

				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), rpo.GetId()).Return(rpo, nil).Times(1)

				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: ou.GetOrderWithTransactions().GetOrder().GetToActorId()}).Return(toActor, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetFromActorId()}).
					Return(fme, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetToActorId()}).
					Return(toe, nil).Times(1)

				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{
					Id: rpo.GetPiTo(),
				}).Return(&piPb.GetPiByIdResponse{Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{Account: &piPb.Account{
							SecureAccountNumber: "xxx00",
						}},
					}}, nil)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), newCreateMatch(&commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_NOTIFICATION,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Notification{
						Notification: &commspb.NotificationMessage{
							Notification: &fcmpb.Notification{
								NotificationType: fcmpb.NotificationType_SYSTEM_TRAY,
								NotificationTemplates: &fcmpb.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcmpb.SystemTrayTemplate{
									CommonTemplateFields: &fcmpb.CommonTemplateFields{
										Title: "Uh-oh! We ran into a problem ⚙️",
										Body:  "Your instructions to send INR 0.00 to user couldn't be executed. Tap to know more.",
										IconAttributes: &fcmpb.IconAttributes{
											IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png",
										},
										Deeplink: &deeplink.Deeplink{
											Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
											ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
												RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
													RecurringPaymentId: "rp-1",
												},
											},
										},
										ExpireAt: timestamppb.New(time.Now().Add(time.Hour)),
									},
								},
								},
							},
						},
					},
				})).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), &commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_SMS,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Sms{
						Sms: &commspb.SMSMessage{
							SmsOption: &commspb.SmsOption{
								Option: &commspb.SmsOption_SiExecutionFailedSmsOption{
									SiExecutionFailedSmsOption: &commspb.SIExecutionFailedSmsOption{
										SmsType: commspb.SmsType_SI_EXECUTION_FAILED,
										Option: &commspb.SIExecutionFailedSmsOption_SiExecutionFailedV1{
											SiExecutionFailedV1: &commspb.SIExecutionFailedSmsOptionV1{
												TemplateVersion: commspb.TemplateVersion_VERSION_V1,
												// using order amount in notification body as execution amount can be different than recurringPayment set amount
												SiAmount:  ou.GetOrderWithTransactions().GetOrder().GetAmount(),
												PayeeName: toe.GetName(),
											},
										},
									},
								},
							},
						},
					},
				}).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

			},
			want: &pb.ProcessRecurringPaymentsNotificationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},

		{
			name: "Mandate Received",
			recrurringPayment: &pb.RecurringPayment{
				Id:          "rp-1",
				Amount:      money.ZeroINR().Pb,
				Type:        pb.RecurringPaymentType_UPI_MANDATES,
				State:       pb.RecurringPaymentState_CREATION_INITIATED,
				PiTo:        "piyush@vpa",
				InitiatedBy: pb.InitiatedBy_PAYEE,
			},
			rpcinfo: &pb.RecurringPaymentCreationInfo{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				ReqId:              "req-id-1",
			},
			fromEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett1",
				Name: &commontypes.Name{FirstName: "Tushar"}},
			toEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett2",
				Name: &commontypes.Name{FirstName: "Piyush"}},
			req: &orderPb.OrderUpdate{
				OrderWithTransactions: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:          "Order-1",
						FromActorId: "Actor-1",
						ToActorId:   "Actor-2",
						Status:      orderPb.OrderStatus_CREATED,
						Workflow:    orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT,
						ClientReqId: "client-req-1",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id: "Transaction-1",
						},
					},
				},
			},
			setupMockCalls: func(ou *orderPb.OrderUpdate, rpo *pb.RecurringPayment, rpci *pb.RecurringPaymentCreationInfo,
				rpa *pb.RecurringPaymentsAction, fme *actorPb.GetEntityDetailsByActorIdResponse, toe *actorPb.GetEntityDetailsByActorIdResponse, toActor *actorPb.GetActorByIdResponse) {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), rpo.GetId()).Return(rpo, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetFromActorId()}).
					Return(fme, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetToActorId()}).
					Return(toe, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), newCreateMatch(&commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_NOTIFICATION,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Notification{
						Notification: &commspb.NotificationMessage{
							Notification: &fcmpb.Notification{
								NotificationType: fcmpb.NotificationType_SYSTEM_TRAY,
								NotificationTemplates: &fcmpb.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcmpb.SystemTrayTemplate{
									CommonTemplateFields: &fcmpb.CommonTemplateFields{
										Title: "UPI auto-payment request ⚡️",
										Body:  "Piyush wants to collect INR 0.00 for UPI-AutoPay. Tap to open Fi & grant this!",
										IconAttributes: &fcmpb.IconAttributes{
											IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png",
										},
										Deeplink: &deeplink.Deeplink{
											Screen: deeplink.Screen_AUTHORIZE_RECURRING_PAYMENT_SCREEN,
											ScreenOptions: &deeplink.Deeplink_AuthorizeRecurringPaymentScreenOptions{
												AuthorizeRecurringPaymentScreenOptions: &deeplink.AuthorizeRecurringPaymentScreenOptions{
													RecurringPaymentId: "rp-1",
												},
											},
										},
										ExpireAt: timestamppb.New(time.Now().Add(time.Hour)),
									},
								},
								},
							},
						},
					},
				})).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), &commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_SMS,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Sms{
						Sms: &commspb.SMSMessage{
							SmsOption: &commspb.SmsOption{
								Option: &commspb.SmsOption_MandateReceivedSmsOption{
									MandateReceivedSmsOption: &commspb.MandateReceivedSmsOption{
										SmsType: commspb.SmsType_MANDATE_RECEIVED,
										Option: &commspb.MandateReceivedSmsOption_MandateReceivedV1{
											MandateReceivedV1: &commspb.MandateReceivedSmsOptionV1{
												TemplateVersion: commspb.TemplateVersion_VERSION_V1,
												MandateAmount:   rpo.GetAmount(),
												PayeeName:       toe.GetName(),
											},
										},
									},
								},
							},
						},
					},
				}).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

			},
			want: &pb.ProcessRecurringPaymentsNotificationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},

		{
			name: "Mandate Approved",
			recrurringPayment: &pb.RecurringPayment{
				Id:     "rp-1",
				Amount: money.ZeroINR().Pb,
				Type:   pb.RecurringPaymentType_UPI_MANDATES,
				State:  pb.RecurringPaymentState_CREATION_AUTHORISED,
				PiTo:   "piyush@vpa",
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_WEEKLY,
				},
				Interval: &types.Interval{
					StartTime: timestampPb.Now(),
				},
			},
			rpcinfo: &pb.RecurringPaymentCreationInfo{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				ReqId:              "req-id-1",
			},
			fromEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett1",
				Name: &commontypes.Name{FirstName: "Tushar"}},
			toEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett2",
				Name: &commontypes.Name{FirstName: "Piyush"}},
			req: &orderPb.OrderUpdate{
				OrderWithTransactions: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:          "Order-1",
						FromActorId: "Actor-1",
						ToActorId:   "Actor-2",
						Status:      orderPb.OrderStatus_IN_FULFILLMENT,
						Workflow:    orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT,
						ClientReqId: "client-req-1",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id: "Transaction-1",
						},
					},
				},
			},
			setupMockCalls: func(ou *orderPb.OrderUpdate, rpo *pb.RecurringPayment, rpci *pb.RecurringPaymentCreationInfo,
				rpa *pb.RecurringPaymentsAction, fme *actorPb.GetEntityDetailsByActorIdResponse, toe *actorPb.GetEntityDetailsByActorIdResponse, toActor *actorPb.GetActorByIdResponse) {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), rpo.GetId()).Return(rpo, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetFromActorId()}).
					Return(fme, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetToActorId()}).
					Return(toe, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), newCreateMatch(&commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_NOTIFICATION,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Notification{
						Notification: &commspb.NotificationMessage{
							Notification: &fcmpb.Notification{
								NotificationType: fcmpb.NotificationType_SYSTEM_TRAY,
								NotificationTemplates: &fcmpb.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcmpb.SystemTrayTemplate{
									CommonTemplateFields: &fcmpb.CommonTemplateFields{
										Title: "Auto-payment authorised 🥳",
										Body:  "Every week, Piyush will receive INR 0.00 from you.",
										Deeplink: &deeplink.Deeplink{
											Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
											ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
												RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
													RecurringPaymentId: "rp-1",
												},
											},
										},
										IconAttributes: &fcmpb.IconAttributes{
											IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png",
										},
										ExpireAt: timestamppb.New(time.Now().Add(time.Hour)),
									},
								},
								},
							},
						},
					},
				})).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), &commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_SMS,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Sms{
						Sms: &commspb.SMSMessage{
							SmsOption: &commspb.SmsOption{
								Option: &commspb.SmsOption_MandateApprovedSmsOption{
									MandateApprovedSmsOption: &commspb.MandateApprovedSmsOption{
										SmsType: commspb.SmsType_MANDATE_APPROVED,
										Option: &commspb.MandateApprovedSmsOption_MandateApprovedV1{
											MandateApprovedV1: &commspb.MandateApprovedSmsOptionV1{
												TemplateVersion:  commspb.TemplateVersion_VERSION_V1,
												MandateAmount:    rpo.GetAmount(),
												PayeeName:        toe.GetName(),
												MandateFrequency: "weekly",
												MandateStartDate: currDate,
											},
										},
									},
								},
							},
						},
					},
				}).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

			},
			want: &pb.ProcessRecurringPaymentsNotificationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},

		{
			name: "Mandate Declined",
			recrurringPayment: &pb.RecurringPayment{
				Id:     "rp-1",
				Amount: money.ZeroINR().Pb,
				Type:   pb.RecurringPaymentType_UPI_MANDATES,
				State:  pb.RecurringPaymentState_CREATION_AUTHORISED,
				PiTo:   "piyush@vpa",
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_WEEKLY,
				},
				Interval: &types.Interval{
					StartTime: timestampPb.Now(),
				},
			},
			rpcinfo: &pb.RecurringPaymentCreationInfo{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				ReqId:              "req-id-1",
			},
			fromEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett1",
				Name: &commontypes.Name{FirstName: "Tushar"}},
			toEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett2",
				Name: &commontypes.Name{FirstName: "Piyush"}},
			req: &orderPb.OrderUpdate{
				OrderWithTransactions: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:          "Order-1",
						FromActorId: "Actor-1",
						ToActorId:   "Actor-2",
						Status:      orderPb.OrderStatus_FULFILLMENT_FAILED,
						Workflow:    orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT,
						ClientReqId: "client-req-1",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id: "Transaction-1",
						},
					},
				},
			},
			setupMockCalls: func(ou *orderPb.OrderUpdate, rpo *pb.RecurringPayment, rpci *pb.RecurringPaymentCreationInfo,
				rpa *pb.RecurringPaymentsAction, fme *actorPb.GetEntityDetailsByActorIdResponse, toe *actorPb.GetEntityDetailsByActorIdResponse, toActor *actorPb.GetActorByIdResponse) {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), rpo.GetId()).Return(rpo, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetFromActorId()}).
					Return(fme, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetToActorId()}).
					Return(toe, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), newCreateMatch(&commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_NOTIFICATION,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Notification{
						Notification: &commspb.NotificationMessage{
							Notification: &fcmpb.Notification{
								NotificationType: fcmpb.NotificationType_SYSTEM_TRAY,
								NotificationTemplates: &fcmpb.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcmpb.SystemTrayTemplate{
									CommonTemplateFields: &fcmpb.CommonTemplateFields{
										Title: "You've declined an auto-payment 🛑",
										Body:  "Piyush will not receive your weekly payment of INR 0.00",
										IconAttributes: &fcmpb.IconAttributes{
											IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png",
										},
										Deeplink: &deeplink.Deeplink{
											Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
											ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
												RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
													RecurringPaymentId: "rp-1",
												},
											},
										},
										ExpireAt: timestamppb.New(time.Now().Add(time.Hour)),
									},
								},
								},
							},
						},
					},
				})).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), &commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_SMS,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Sms{
						Sms: &commspb.SMSMessage{
							SmsOption: &commspb.SmsOption{
								Option: &commspb.SmsOption_MandateDeclinedSmsOption{
									MandateDeclinedSmsOption: &commspb.MandateDeclinedSmsOption{
										SmsType: commspb.SmsType_MANDATE_DECLINED,
										Option: &commspb.MandateDeclinedSmsOption_MandateDeclinedV1{
											MandateDeclinedV1: &commspb.MandateDeclinedSmsOptionV1{
												TemplateVersion:      commspb.TemplateVersion_VERSION_V1,
												MandateAmount:        rpo.GetAmount(),
												PayeeName:            toe.GetName(),
												MandateFrequency:     "weekly",
												MandateExecutionDate: currDate,
											},
										},
									},
								},
							},
						},
					},
				}).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

			},
			want: &pb.ProcessRecurringPaymentsNotificationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},

		{
			name: "Mandate Created",
			recrurringPayment: &pb.RecurringPayment{
				Id:     "rp-1",
				Amount: money.ZeroINR().Pb,
				Type:   pb.RecurringPaymentType_UPI_MANDATES,
				State:  pb.RecurringPaymentState_CREATION_AUTHORISED,
				PiTo:   "piyush@vpa",
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_WEEKLY,
				},
			},
			rpcinfo: &pb.RecurringPaymentCreationInfo{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				ReqId:              "req-id-1",
			},
			fromEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett1",
				Name: &commontypes.Name{FirstName: "Tushar"}},
			toEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett2",
				Name: &commontypes.Name{FirstName: "Piyush"}},
			req: &orderPb.OrderUpdate{
				OrderWithTransactions: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:          "Order-1",
						FromActorId: "Actor-1",
						ToActorId:   "Actor-2",
						Status:      orderPb.OrderStatus_FULFILLED,
						Workflow:    orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT,
						ClientReqId: "client-req-1",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id: "Transaction-1",
						},
					},
				},
			},
			setupMockCalls: func(ou *orderPb.OrderUpdate, rpo *pb.RecurringPayment, rpci *pb.RecurringPaymentCreationInfo,
				rpa *pb.RecurringPaymentsAction, fme *actorPb.GetEntityDetailsByActorIdResponse, toe *actorPb.GetEntityDetailsByActorIdResponse, toActor *actorPb.GetActorByIdResponse) {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), rpo.GetId()).Return(rpo, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetFromActorId()}).
					Return(fme, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetToActorId()}).
					Return(toe, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), newCreateMatch(&commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_NOTIFICATION,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Notification{
						Notification: &commspb.NotificationMessage{
							Notification: &fcmpb.Notification{
								NotificationType: fcmpb.NotificationType_SYSTEM_TRAY,
								NotificationTemplates: &fcmpb.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcmpb.SystemTrayTemplate{
									CommonTemplateFields: &fcmpb.CommonTemplateFields{
										Title: "Woohoo! Auto-payment registered 🎯",
										Body:  "Every week, Piyush will receive a UPI auto-payment of INR 0.00 from you.",
										IconAttributes: &fcmpb.IconAttributes{
											IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png",
										},
										Deeplink: &deeplink.Deeplink{
											Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
											ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
												RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
													RecurringPaymentId: "rp-1",
												},
											},
										},
										ExpireAt: timestamppb.New(time.Now().Add(time.Hour)),
									},
								},
								},
							},
						},
					},
				})).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), &commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_SMS,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Sms{
						Sms: &commspb.SMSMessage{
							SmsOption: &commspb.SmsOption{
								Option: &commspb.SmsOption_MandateCreatedSmsOption{
									MandateCreatedSmsOption: &commspb.MandateCreatedSmsOption{
										SmsType: commspb.SmsType_MANDATE_CREATED,
										Option: &commspb.MandateCreatedSmsOption_MandateCreatedV1{
											MandateCreatedV1: &commspb.MandateCreatedSmsOptionV1{
												TemplateVersion:  commspb.TemplateVersion_VERSION_V1,
												MandateAmount:    rpo.GetAmount(),
												MandateFrequency: "weekly",
												PayerName:        fme.GetName(),
												PayeeName:        toe.GetName(),
											},
										},
									},
								},
							},
						},
					},
				}).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

			},
			want: &pb.ProcessRecurringPaymentsNotificationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},

		{
			name: "Successful Mandate Execution",
			recrurringPayment: &pb.RecurringPayment{
				Id:     "rp-1",
				Amount: money.ZeroINR().Pb,
				Type:   pb.RecurringPaymentType_UPI_MANDATES,
				State:  pb.RecurringPaymentState_CREATION_AUTHORISED,
				PiTo:   "pi-1",
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_WEEKLY,
				},
			},
			rpcexec: &pb.RecurringPaymentExecutionInfo{
				RecurringPaymentId: "rp-1",
				Amount:             money.ZeroINR().Pb,
			},

			fromEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett1",
				Name: &commontypes.Name{FirstName: "Tushar"}},
			toEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett2",
				Name: &commontypes.Name{FirstName: "Piyush"}},
			req: &orderPb.OrderUpdate{
				OrderWithTransactions: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:          "Order-1",
						Amount:      money.ZeroINR().Pb,
						FromActorId: "Actor-1",
						ToActorId:   "Actor-2",
						Status:      orderPb.OrderStatus_PAID,
						Workflow:    orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT,
						ClientReqId: "client-req-1",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id: "Transaction-1",
						},
					},
				},
			},
			setupMockCalls: func(ou *orderPb.OrderUpdate, rpo *pb.RecurringPayment, rpci *pb.RecurringPaymentCreationInfo,
				rpa *pb.RecurringPaymentsAction, fme *actorPb.GetEntityDetailsByActorIdResponse, toe *actorPb.GetEntityDetailsByActorIdResponse, toActor *actorPb.GetActorByIdResponse) {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), rpo.GetId()).Return(rpo, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetFromActorId()}).
					Return(fme, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetToActorId()}).
					Return(toe, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), newCreateMatch(&commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_NOTIFICATION,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Notification{
						Notification: &commspb.NotificationMessage{
							Notification: &fcmpb.Notification{
								NotificationType: fcmpb.NotificationType_SYSTEM_TRAY,
								NotificationTemplates: &fcmpb.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcmpb.SystemTrayTemplate{
									CommonTemplateFields: &fcmpb.CommonTemplateFields{
										Title: "Auto-payment successful ✅",
										Body:  fmt.Sprintf("We've sent INR 0.00 to Piyush. The UPI auto-payment occurred on %s.", currDate),
										IconAttributes: &fcmpb.IconAttributes{
											IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png",
										},
										Deeplink: &deeplink.Deeplink{
											Screen: deeplink.Screen_TRANSACTION_RECEIPT,
											ScreenOptions: &deeplink.Deeplink_TransactionReceiptScreenOptions{
												TransactionReceiptScreenOptions: &deeplink.TransactionReceiptScreenOptions{
													OrderId: "Order-1",
												},
											},
										},
										ExpireAt: timestamppb.New(time.Now().Add(time.Hour)),
									},
								},
								},
							},
						},
					},
				})).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), &commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_SMS,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Sms{
						Sms: &commspb.SMSMessage{
							SmsOption: &commspb.SmsOption{
								Option: &commspb.SmsOption_MandateExecutionSuccessfulSmsOption{
									MandateExecutionSuccessfulSmsOption: &commspb.MandateExecutionSuccessfulSmsOption{
										SmsType: commspb.SmsType_MANDATE_EXECUTION_SUCCESSFUL,
										Option: &commspb.MandateExecutionSuccessfulSmsOption_MandateExecutionSuccessfulV1{
											MandateExecutionSuccessfulV1: &commspb.MandateExecutionSuccessfulSmsOptionV1{
												TemplateVersion:      commspb.TemplateVersion_VERSION_V1,
												MandateAmount:        rpo.GetAmount(),
												PayeeName:            toe.GetName(),
												MandateExecutionDate: currDate,
											},
										},
									},
								},
							},
						},
					},
				}).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

			},
			want: &pb.ProcessRecurringPaymentsNotificationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},

		{
			name: "Failed Mandate Execution",
			recrurringPayment: &pb.RecurringPayment{
				Id:     "rp-1",
				Amount: money.ZeroINR().Pb,
				Type:   pb.RecurringPaymentType_UPI_MANDATES,
				State:  pb.RecurringPaymentState_ACTIVATED,
				PiTo:   "piyush@vpa",
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_WEEKLY,
				},
			},
			rpcexec: &pb.RecurringPaymentExecutionInfo{
				RecurringPaymentId: "rp-1",
				Amount:             money.ZeroINR().Pb,
			},
			fromEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett1",
				Name: &commontypes.Name{FirstName: "Tushar"}},
			toEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett2",
				Name: &commontypes.Name{FirstName: "Piyush"}},
			req: &orderPb.OrderUpdate{
				OrderWithTransactions: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:          "Order-1",
						Amount:      money.ZeroINR().Pb,
						FromActorId: "Actor-1",
						ToActorId:   "Actor-2",
						Status:      orderPb.OrderStatus_PAYMENT_FAILED,
						Workflow:    orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT,
						ClientReqId: "client-req-1",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id: "Transaction-1",
						},
					},
				},
			},
			setupMockCalls: func(ou *orderPb.OrderUpdate, rpo *pb.RecurringPayment, rpci *pb.RecurringPaymentCreationInfo,
				rpa *pb.RecurringPaymentsAction, fme *actorPb.GetEntityDetailsByActorIdResponse, toe *actorPb.GetEntityDetailsByActorIdResponse, toActor *actorPb.GetActorByIdResponse) {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), rpo.GetId()).Return(rpo, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetFromActorId()}).
					Return(fme, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetToActorId()}).
					Return(toe, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), newCreateMatch(&commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_NOTIFICATION,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Notification{
						Notification: &commspb.NotificationMessage{
							Notification: &fcmpb.Notification{
								NotificationType: fcmpb.NotificationType_SYSTEM_TRAY,
								NotificationTemplates: &fcmpb.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcmpb.SystemTrayTemplate{
									CommonTemplateFields: &fcmpb.CommonTemplateFields{
										Title: "Uh-oh! Your UPI auto-payment failed 🚧",
										Body:  "An auto-payment of INR 0.00 to Piyush did not go through. Tap here to investigate!",
										IconAttributes: &fcmpb.IconAttributes{
											IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png",
										},
										Deeplink: &deeplink.Deeplink{
											Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
											ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
												RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
													RecurringPaymentId: "rp-1",
												},
											},
										},
										ExpireAt: timestamppb.New(time.Now().Add(time.Hour)),
									},
								},
								},
							},
						},
					},
				})).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), &commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_SMS,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Sms{
						Sms: &commspb.SMSMessage{
							SmsOption: &commspb.SmsOption{
								Option: &commspb.SmsOption_MandateExecutionFailedSmsOption{
									MandateExecutionFailedSmsOption: &commspb.MandateExecutionFailedSmsOption{
										SmsType: commspb.SmsType_MANDATE_EXECUTION_FAILED,
										Option: &commspb.MandateExecutionFailedSmsOption_MandateExecutionFailedV1{
											MandateExecutionFailedV1: &commspb.MandateExecutionFailedSmsOptionV1{
												TemplateVersion:      commspb.TemplateVersion_VERSION_V1,
												MandateAmount:        rpo.GetAmount(),
												PayeeName:            toe.GetName(),
												MandateExecutionDate: currDate,
											},
										},
									},
								},
							},
						},
					},
				}).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

			},
			want: &pb.ProcessRecurringPaymentsNotificationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},

		{
			name: "Mandate Revoked",
			recrurringPayment: &pb.RecurringPayment{
				Id:     "rp-1",
				Amount: money.ZeroINR().Pb,
				Type:   pb.RecurringPaymentType_UPI_MANDATES,
				State:  pb.RecurringPaymentState_ACTIVATED,
				PiTo:   "piyush@vpa",
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_WEEKLY,
				},
			},
			rpcrev: &pb.RecurringPaymentRevokeInfo{
				InitiatedBy:        pb.InitiatedBy_PAYER,
				RecurringPaymentId: "rp-1",
			},
			rpAction: &pb.RecurringPaymentsAction{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-id-1",
			},
			fromEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett1",
				Name: &commontypes.Name{FirstName: "Tushar"}},
			toEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett2",
				Name: &commontypes.Name{FirstName: "Piyush"}},
			req: &orderPb.OrderUpdate{
				OrderWithTransactions: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:          "Order-1",
						FromActorId: "Actor-1",
						ToActorId:   "Actor-2",
						Status:      orderPb.OrderStatus_FULFILLED,
						Workflow:    orderPb.OrderWorkflow_REVOKE_RECURRING_PAYMENT,
						ClientReqId: "client-req-1",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id: "Transaction-1",
						},
					},
				},
			},
			setupMockCalls: func(ou *orderPb.OrderUpdate, rpo *pb.RecurringPayment, rpci *pb.RecurringPaymentCreationInfo,
				rpa *pb.RecurringPaymentsAction, fme *actorPb.GetEntityDetailsByActorIdResponse, toe *actorPb.GetEntityDetailsByActorIdResponse, toActor *actorPb.GetActorByIdResponse) {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), rpo.GetId()).Return(rpo, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetFromActorId()}).
					Return(fme, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetToActorId()}).
					Return(toe, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), newCreateMatch(&commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_NOTIFICATION,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Notification{
						Notification: &commspb.NotificationMessage{
							Notification: &fcmpb.Notification{
								NotificationType: fcmpb.NotificationType_SYSTEM_TRAY,
								NotificationTemplates: &fcmpb.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcmpb.SystemTrayTemplate{
									CommonTemplateFields: &fcmpb.CommonTemplateFields{
										Title: "Auto-payment cancelled 🚨",
										Body:  "Heads up! Piyush will no longer receive INR 0.00 from you for UPI AutoPay.",
										IconAttributes: &fcmpb.IconAttributes{
											IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png",
										},
										Deeplink: &deeplink.Deeplink{
											Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
											ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
												RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
													RecurringPaymentId: "rp-1",
												},
											},
										},
										ExpireAt: timestamppb.New(time.Now().Add(time.Hour)),
									},
								},
								},
							},
						},
					},
				})).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), &commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_SMS,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Sms{
						Sms: &commspb.SMSMessage{
							SmsOption: &commspb.SmsOption{
								Option: &commspb.SmsOption_MandateRevokedSmsOption{
									MandateRevokedSmsOption: &commspb.MandateRevokedSmsOption{
										SmsType: commspb.SmsType_MANDATE_REVOKED,
										Option: &commspb.MandateRevokedSmsOption_MandateRevokedV1{
											MandateRevokedV1: &commspb.MandateRevokedSmsOptionV1{
												TemplateVersion: commspb.TemplateVersion_VERSION_V1,
												MandateAmount:   rpo.GetAmount(),
												PayeeName:       toe.GetName(),
											},
										},
									},
								},
							},
						},
					},
				}).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

			},
			want: &pb.ProcessRecurringPaymentsNotificationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},

		{
			name: "Mandate Modified",
			recrurringPayment: &pb.RecurringPayment{
				Id:     "rp-1",
				Amount: money.ZeroINR().Pb,
				Type:   pb.RecurringPaymentType_UPI_MANDATES,
				State:  pb.RecurringPaymentState_MODIFY_AUTHORISED,
				PiTo:   "piyush@vpa",
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_WEEKLY,
				},
			},
			rpcmod: &pb.RecurringPaymentModifyInfo{
				RecurringPaymentId: "rp-1",
			},
			fromEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett1",
				Name: &commontypes.Name{FirstName: "Tushar"}},
			toEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett2",
				Name: &commontypes.Name{FirstName: "Piyush"}},
			req: &orderPb.OrderUpdate{
				OrderWithTransactions: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:          "Order-1",
						FromActorId: "Actor-1",
						ToActorId:   "Actor-2",
						Status:      orderPb.OrderStatus_FULFILLED,
						Workflow:    orderPb.OrderWorkflow_MODIFY_RECURRING_PAYMENT,
						ClientReqId: "client-req-1",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id: "Transaction-1",
						},
					},
				},
			},
			setupMockCalls: func(ou *orderPb.OrderUpdate, rpo *pb.RecurringPayment, rpci *pb.RecurringPaymentCreationInfo,
				rpa *pb.RecurringPaymentsAction, fme *actorPb.GetEntityDetailsByActorIdResponse, toe *actorPb.GetEntityDetailsByActorIdResponse, toActor *actorPb.GetActorByIdResponse) {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), rpo.GetId()).Return(rpo, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetFromActorId()}).
					Return(fme, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetToActorId()}).
					Return(toe, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), newCreateMatch(&commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_NOTIFICATION,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Notification{
						Notification: &commspb.NotificationMessage{
							Notification: &fcmpb.Notification{
								NotificationType: fcmpb.NotificationType_SYSTEM_TRAY,
								NotificationTemplates: &fcmpb.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcmpb.SystemTrayTemplate{
									CommonTemplateFields: &fcmpb.CommonTemplateFields{
										Title: "Auto-payment details revised 🔧",
										Body:  "As instructed, we've modified your UPI AutoPay towards Piyush for INR 0.00",
										IconAttributes: &fcmpb.IconAttributes{
											IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png",
										},
										Deeplink: &deeplink.Deeplink{
											Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
											ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
												RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
													RecurringPaymentId: "rp-1",
												},
											},
										},
										ExpireAt: timestamppb.New(time.Now().Add(time.Hour)),
									},
								},
								},
							},
						},
					},
				})).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), &commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_SMS,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Sms{
						Sms: &commspb.SMSMessage{
							SmsOption: &commspb.SmsOption{
								Option: &commspb.SmsOption_MandateModifiedSmsOption{
									MandateModifiedSmsOption: &commspb.MandateModifiedSmsOption{
										SmsType: commspb.SmsType_MANDATE_MODIFIED,
										Option: &commspb.MandateModifiedSmsOption_MandateModifiedV1{
											MandateModifiedV1: &commspb.MandateModifiedSmsOptionV1{
												TemplateVersion: commspb.TemplateVersion_VERSION_V1,
												MandateAmount:   rpo.GetAmount(),
												PayeeName:       toe.GetName(),
											},
										},
									},
								},
							},
						},
					},
				}).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

			},
			want: &pb.ProcessRecurringPaymentsNotificationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},

		{
			name: "Mandate Authorized",
			recrurringPayment: &pb.RecurringPayment{
				Id:     "rp-1",
				Amount: money.ZeroINR().Pb,
				Type:   pb.RecurringPaymentType_UPI_MANDATES,
				State:  pb.RecurringPaymentState_CREATION_AUTHORISED,
				PiTo:   "piyush@vpa",
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_WEEKLY,
				},
			},
			rpcexec: &pb.RecurringPaymentExecutionInfo{
				RecurringPaymentId: "rp-1",
			},
			fromEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett1",
				Name: &commontypes.Name{FirstName: "Tushar"}},
			toEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett2",
				Name: &commontypes.Name{FirstName: "Piyush"}},
			req: &orderPb.OrderUpdate{
				OrderWithTransactions: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Amount:      money.ZeroINR().Pb,
						Id:          "Order-1",
						FromActorId: "Actor-1",
						ToActorId:   "Actor-2",
						Status:      orderPb.OrderStatus_IN_PAYMENT,
						Workflow:    orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_WITH_AUTH,
						ClientReqId: "client-req-1",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id: "Transaction-1",
						},
					},
				},
			},
			setupMockCalls: func(ou *orderPb.OrderUpdate, rpo *pb.RecurringPayment, rpci *pb.RecurringPaymentCreationInfo,
				rpa *pb.RecurringPaymentsAction, fme *actorPb.GetEntityDetailsByActorIdResponse, toe *actorPb.GetEntityDetailsByActorIdResponse, toActor *actorPb.GetActorByIdResponse) {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), rpo.GetId()).Return(rpo, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetFromActorId()}).
					Return(fme, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetToActorId()}).
					Return(toe, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), newCreateMatch(&commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_NOTIFICATION,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Notification{
						Notification: &commspb.NotificationMessage{
							Notification: &fcmpb.Notification{
								NotificationType: fcmpb.NotificationType_SYSTEM_TRAY,
								NotificationTemplates: &fcmpb.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcmpb.SystemTrayTemplate{
									CommonTemplateFields: &fcmpb.CommonTemplateFields{
										Title: "Your authorisation is required 📝",
										Body:  "Piyush has sent an AutoPay request for INR 0.00. Tap to open Fi & authorise it.",
										IconAttributes: &fcmpb.IconAttributes{
											IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png",
										},
										Deeplink: &deeplink.Deeplink{
											Screen: deeplink.Screen_AUTHORIZE_RECURRING_PAYMENT_SCREEN,
											ScreenOptions: &deeplink.Deeplink_AuthorizeRecurringPaymentScreenOptions{
												AuthorizeRecurringPaymentScreenOptions: &deeplink.AuthorizeRecurringPaymentScreenOptions{
													RecurringPaymentId: "rp-1",
												},
											},
										},
										ExpireAt: timestamppb.New(time.Now().Add(time.Hour)),
									},
								},
								},
							},
						},
					},
				})).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), &commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_SMS,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Sms{
						Sms: &commspb.SMSMessage{
							SmsOption: &commspb.SmsOption{
								Option: &commspb.SmsOption_MandateAuthorisedSmsOption{
									MandateAuthorisedSmsOption: &commspb.MandateAuthorisedSmsOption{
										SmsType: commspb.SmsType_MANDATE_AUTHORISED,
										Option: &commspb.MandateAuthorisedSmsOption_MandateAuthorisedV1{
											MandateAuthorisedV1: &commspb.MandateAuthorisedSmsOptionV1{
												TemplateVersion: commspb.TemplateVersion_VERSION_V1,
												Amount:          ou.GetOrderWithTransactions().GetOrder().GetAmount(),
												PayeeName:       toe.GetName(),
											},
										},
									},
								},
							},
						},
					},
				}).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

			},
			want: &pb.ProcessRecurringPaymentsNotificationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "Mandate Acceptance for Payee",
			recrurringPayment: &pb.RecurringPayment{
				Id:     "rp-1",
				Amount: money.ZeroINR().Pb,
				Type:   pb.RecurringPaymentType_UPI_MANDATES,
				State:  pb.RecurringPaymentState_CREATION_AUTHORISED,
				PiTo:   "piyush@vpa",
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_WEEKLY,
				},
				ShareToPayee: true,
			},
			rpcexec: &pb.RecurringPaymentExecutionInfo{
				RecurringPaymentId: "rp-1",
			},
			fromEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett1",
				Name: &commontypes.Name{FirstName: "Tushar"}},
			toEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett2",
				Name: &commontypes.Name{FirstName: "Piyush"}},
			req: &orderPb.OrderUpdate{
				OrderWithTransactions: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Amount:      money.ZeroINR().Pb,
						Id:          "Order-1",
						FromActorId: "Actor-1",
						ToActorId:   "Actor-2",
						Status:      orderPb.OrderStatus_IN_PAYMENT,
						Workflow:    orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH,
						ClientReqId: "client-req-1",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id: "Transaction-1",
						},
					},
				},
			},
			setupMockCalls: func(ou *orderPb.OrderUpdate, rpo *pb.RecurringPayment, rpci *pb.RecurringPaymentCreationInfo,
				rpa *pb.RecurringPaymentsAction, fme *actorPb.GetEntityDetailsByActorIdResponse, toe *actorPb.GetEntityDetailsByActorIdResponse, toActor *actorPb.GetActorByIdResponse) {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), rpo.GetId()).Return(rpo, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetFromActorId()}).
					Return(fme, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetToActorId()}).
					Return(toe, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), newCreateMatch(&commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_NOTIFICATION,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: toe.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Notification{
						Notification: &commspb.NotificationMessage{
							Notification: &fcmpb.Notification{
								NotificationType: fcmpb.NotificationType_SYSTEM_TRAY,
								NotificationTemplates: &fcmpb.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcmpb.SystemTrayTemplate{
									CommonTemplateFields: &fcmpb.CommonTemplateFields{
										Title: "Someone's trying to send you money 💸",
										Body:  "Tushar wants to send you INR 0.00 via UPI auto-payment. Tap to open Fi & accept it.",
										IconAttributes: &fcmpb.IconAttributes{
											IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png",
										},
										Deeplink: &deeplink.Deeplink{
											Screen: deeplink.Screen_AUTHORIZE_RECURRING_PAYMENT_SCREEN,
											ScreenOptions: &deeplink.Deeplink_AuthorizeRecurringPaymentScreenOptions{
												AuthorizeRecurringPaymentScreenOptions: &deeplink.AuthorizeRecurringPaymentScreenOptions{
													RecurringPaymentId: "rp-1",
												},
											},
										},
										ExpireAt: timestamppb.New(time.Now().Add(time.Hour)),
									},
								},
								},
							},
						},
					},
				})).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), &commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_SMS,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: toe.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Sms{
						Sms: &commspb.SMSMessage{
							SmsOption: &commspb.SmsOption{
								Option: &commspb.SmsOption_MandateAcceptanceSmsOption{
									MandateAcceptanceSmsOption: &commspb.MandateAcceptanceSmsOption{
										SmsType: commspb.SmsType_MANDATE_ACCEPTANCE,
										Option: &commspb.MandateAcceptanceSmsOption_MandateAcceptanceV1{
											MandateAcceptanceV1: &commspb.MandateAcceptanceSmsOptionV1{
												TemplateVersion: commspb.TemplateVersion_VERSION_V1,
												Amount:          ou.GetOrderWithTransactions().GetOrder().GetAmount(),
												PayerName:       fme.GetName(),
											},
										},
									},
								},
							},
						},
					},
				}).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

			},
			want: &pb.ProcessRecurringPaymentsNotificationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "Mandate Paused",
			recrurringPayment: &pb.RecurringPayment{
				Id:     "rp-1",
				Amount: money.ZeroINR().Pb,
				Type:   pb.RecurringPaymentType_UPI_MANDATES,
				State:  pb.RecurringPaymentState_PAUSED,
				PiTo:   "piyush@vpa",
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_WEEKLY,
				},
			},
			rpcpup: &pb.RecurringPaymentPauseUnpauseInfo{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				Action:             pb.Action_PAUSE,
			},
			fromEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett1",
				Name: &commontypes.Name{FirstName: "Tushar"}},
			toEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett2",
				Name: &commontypes.Name{FirstName: "Piyush"}},
			req: &orderPb.OrderUpdate{
				OrderWithTransactions: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Amount:      money.ZeroINR().Pb,
						Id:          "Order-1",
						FromActorId: "Actor-1",
						ToActorId:   "Actor-2",
						Status:      orderPb.OrderStatus_FULFILLED,
						Workflow:    orderPb.OrderWorkflow_PAUSE_OR_UNPAUSE_RECURRING_PAYMENT,
						ClientReqId: "client-req-1",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id: "Transaction-1",
						},
					},
				},
			},
			setupMockCalls: func(ou *orderPb.OrderUpdate, rpo *pb.RecurringPayment, rpci *pb.RecurringPaymentCreationInfo,
				rpa *pb.RecurringPaymentsAction, fme *actorPb.GetEntityDetailsByActorIdResponse, toe *actorPb.GetEntityDetailsByActorIdResponse, toActor *actorPb.GetActorByIdResponse) {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), rpo.GetId()).Return(rpo, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetFromActorId()}).
					Return(fme, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetToActorId()}).
					Return(toe, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), newCreateMatch(&commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_NOTIFICATION,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Notification{
						Notification: &commspb.NotificationMessage{
							Notification: &fcmpb.Notification{
								NotificationType: fcmpb.NotificationType_SYSTEM_TRAY,
								NotificationTemplates: &fcmpb.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcmpb.SystemTrayTemplate{
									CommonTemplateFields: &fcmpb.CommonTemplateFields{
										Title: "UPI auto-payments paused ⏳",
										Body:  "All your current UPI auto-payments are taking a breather. Want to reactivate them? Tap here!",
										IconAttributes: &fcmpb.IconAttributes{
											IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png",
										},
										Deeplink: &deeplink.Deeplink{
											Screen: deeplink.Screen_AUTHORIZE_RECURRING_PAYMENT_SCREEN,
											ScreenOptions: &deeplink.Deeplink_AuthorizeRecurringPaymentScreenOptions{
												AuthorizeRecurringPaymentScreenOptions: &deeplink.AuthorizeRecurringPaymentScreenOptions{
													RecurringPaymentId: "rp-1",
												},
											},
										},
										ExpireAt: timestamppb.New(time.Now().Add(time.Hour)),
									},
								},
								},
							},
						},
					},
				})).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), &commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_SMS,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Sms{
						Sms: &commspb.SMSMessage{
							SmsOption: &commspb.SmsOption{
								Option: &commspb.SmsOption_MandatePausedSmsOption{
									MandatePausedSmsOption: &commspb.MandatePausedSmsOption{
										SmsType: commspb.SmsType_MANDATE_PAUSED,
										Option: &commspb.MandatePausedSmsOption_MandatePausedV1{
											MandatePausedV1: &commspb.MandatePausedSmsOptionV1{
												TemplateVersion: commspb.TemplateVersion_VERSION_V1,
												PayerName:       fme.GetName(),
												PayeeName:       toe.GetName(),
											},
										},
									},
								},
							},
						},
					},
				}).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

			},
			want: &pb.ProcessRecurringPaymentsNotificationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "Mandate Unpaused",
			recrurringPayment: &pb.RecurringPayment{
				Id:     "rp-1",
				Amount: money.ZeroINR().Pb,
				Type:   pb.RecurringPaymentType_UPI_MANDATES,
				State:  pb.RecurringPaymentState_PAUSED,
				PiTo:   "piyush@vpa",
				RecurrenceRule: &pb.RecurrenceRule{
					AllowedFrequency: pb.AllowedFrequency_WEEKLY,
				},
			},
			rpcpup: &pb.RecurringPaymentPauseUnpauseInfo{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				Action:             pb.Action_UNPAUSE,
			},
			fromEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett1",
				Name: &commontypes.Name{FirstName: "Tushar"}},
			toEntity: &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk(), EntityId: "ett2",
				Name: &commontypes.Name{FirstName: "Piyush"}},
			req: &orderPb.OrderUpdate{
				OrderWithTransactions: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Amount:      money.ZeroINR().Pb,
						Id:          "Order-1",
						FromActorId: "Actor-1",
						ToActorId:   "Actor-2",
						Status:      orderPb.OrderStatus_FULFILLED,
						Workflow:    orderPb.OrderWorkflow_PAUSE_OR_UNPAUSE_RECURRING_PAYMENT,
						ClientReqId: "client-req-1",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id: "Transaction-1",
						},
					},
				},
			},
			setupMockCalls: func(ou *orderPb.OrderUpdate, rpo *pb.RecurringPayment, rpci *pb.RecurringPaymentCreationInfo,
				rpa *pb.RecurringPaymentsAction, fme *actorPb.GetEntityDetailsByActorIdResponse, toe *actorPb.GetEntityDetailsByActorIdResponse, toActor *actorPb.GetActorByIdResponse) {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), rpo.GetId()).Return(rpo, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetFromActorId()}).
					Return(fme, nil).Times(1)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: ou.GetOrderWithTransactions().GetOrder().GetToActorId()}).
					Return(toe, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), newCreateMatch(&commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_NOTIFICATION,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Notification{
						Notification: &commspb.NotificationMessage{
							Notification: &fcmpb.Notification{
								NotificationType: fcmpb.NotificationType_SYSTEM_TRAY,
								NotificationTemplates: &fcmpb.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcmpb.SystemTrayTemplate{
									CommonTemplateFields: &fcmpb.CommonTemplateFields{
										Title: "Auto-payments successfully resumed 🚀",
										Body:  "We heard you loud & clear, Tushar. All your UPI auto-payments are back on track!",
										IconAttributes: &fcmpb.IconAttributes{
											IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png",
										},
										Deeplink: &deeplink.Deeplink{
											Screen: deeplink.Screen_AUTHORIZE_RECURRING_PAYMENT_SCREEN,
											ScreenOptions: &deeplink.Deeplink_AuthorizeRecurringPaymentScreenOptions{
												AuthorizeRecurringPaymentScreenOptions: &deeplink.AuthorizeRecurringPaymentScreenOptions{
													RecurringPaymentId: "rp-1",
												},
											},
										},
										ExpireAt: timestamppb.New(time.Now().Add(time.Hour)),
									},
								},
								},
							},
						},
					},
				})).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

				mockCommsClient.EXPECT().SendMessage(gomock.Any(), &commspb.SendMessageRequest{
					Type:   commspb.QoS_GUARANTEED,
					Medium: commspb.Medium_SMS,
					UserIdentifier: &commspb.SendMessageRequest_UserId{
						UserId: fme.GetEntityId(),
					},
					Message: &commspb.SendMessageRequest_Sms{
						Sms: &commspb.SMSMessage{
							SmsOption: &commspb.SmsOption{
								Option: &commspb.SmsOption_MandateUnpausedSmsOption{
									MandateUnpausedSmsOption: &commspb.MandateUnpausedSmsOption{
										SmsType: commspb.SmsType_MANDATE_UNPAUSED,
										Option: &commspb.MandateUnpausedSmsOption_MandateUnpausedV1{
											MandateUnpausedV1: &commspb.MandateUnpausedSmsOptionV1{
												TemplateVersion: commspb.TemplateVersion_VERSION_V1,
												PayeeName:       toe.GetName(),
											},
										},
									},
								},
							},
						},
					},
				}).Return(&commspb.SendMessageResponse{MessageId: "", Status: rpc.StatusOk()}, nil).Times(1)

			},
			want: &pb.ProcessRecurringPaymentsNotificationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			switch {
			case tt.rpcinfo != nil:
				tt.req.OrderWithTransactions.Order.OrderPayload, _ = protojson.Marshal(tt.rpcinfo)
			case tt.rpcexec != nil:
				tt.req.OrderWithTransactions.Order.OrderPayload, _ = protojson.Marshal(tt.rpcexec)
			case tt.rpcrev != nil:
				tt.req.OrderWithTransactions.Order.OrderPayload, _ = protojson.Marshal(tt.rpcrev)
			case tt.rpcmod != nil:
				tt.req.OrderWithTransactions.Order.OrderPayload, _ = protojson.Marshal(tt.rpcmod)
			case tt.rpcpup != nil:
				tt.req.OrderWithTransactions.Order.OrderPayload, _ = protojson.Marshal(tt.rpcpup)
			}
			tt.setupMockCalls(tt.req, tt.recrurringPayment, tt.rpcinfo, tt.rpAction, tt.fromEntity, tt.toEntity, tt.toActor)

			got, err := s.ProcessRecurringPaymentsNotification(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessRecurringPaymentsNotification() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessRecurringPaymentsNotification() got = %v, want %v", got, tt.want)
			}
		})
	}
}

type CreateMatch struct {
	want *commspb.SendMessageRequest
}

func newCreateMatch(want *commspb.SendMessageRequest) *CreateMatch {
	return &CreateMatch{
		want: want,
	}
}

func (ce *CreateMatch) Matches(x interface{}) bool {
	got, ok := x.(*commspb.SendMessageRequest)
	if !ok {
		return false
	}

	expireAt := got.GetNotification().GetNotification().GetSystemTrayTemplate().GetCommonTemplateFields().GetExpireAt()
	ce.want.GetNotification().GetNotification().GetSystemTrayTemplate().GetCommonTemplateFields().ExpireAt = expireAt
	return reflect.DeepEqual(ce.want, got)
}

func (ce *CreateMatch) String() string {
	return fmt.Sprintf("want: %v", ce.want)
}

var (
	mccwarningMessage = &commontypes.Text{
		DisplayValue: &commontypes.Text_PlainString{
			PlainString: "Transaction declined due to insufficiency of funds for this UPI Autopay is a punishable offence under Section 25 of the Payment & Settlement Act, 2007 (PSS Act)",
		},
		BgColor:   "#F4E7BF",
		FontColor: "#555555",
		FontStyle: &commontypes.Text_StandardFontStyle{
			StandardFontStyle: commontypes.FontStyle_BODY_XS,
		},
	}
)

func TestService_GetRecurringPaymentActionStatus(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockOrderClient := orderMocks.NewMockOrderServiceClient(ctr)
	mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
	mockRecurringPaymentsActionDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
	mockSIClient := mocks.NewMockStandingInstructionServiceClient(ctr)
	mockRecurringPaymentProcessor := mocks2.NewMockRecurringPaymentProcessor(ctr)
	mockActor := actorMocks.NewMockActorClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockAccountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
	mockCelestialProcessor := internalMocks.NewMockCelestialProcessor(ctr)

	svc := NewService(mockRecurringPaymentDao, mockOrderClient, mockSIClient, mockRecurringPaymentsActionDao, nil, mockActor, nil, nil, nil, conf, mockRecurringPaymentProcessor, nil, mockAccountPiClient, mockPiClient, nil, nil, nil, nil, dynamicConf.FeatureFlags(), mockCelestialProcessor, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	tests := []struct {
		name           string
		req            *pb.GetRecurringPaymentActionStatusRequest
		setupMockCalls func()
		want           *pb.GetRecurringPaymentActionStatusResponse
		wantErr        bool
	}{
		{
			name: "recurring payment status fetch failed due to empty client req id",
			req: &pb.GetRecurringPaymentActionStatusRequest{
				RecurringPaymentId: "rp-1",
				Action:             pb.Action_CREATE,
				CurrentActorId:     "actor-1",
			},
			setupMockCalls: func() {},
			want: &pb.GetRecurringPaymentActionStatusResponse{
				Status: rpc.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "fetched action status successfully for create",
			req: &pb.GetRecurringPaymentActionStatusRequest{
				RecurringPaymentId: "rp-1",
				Action:             pb.Action_CREATE,
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(&pb.RecurringPayment{
					Id:          "rp-1",
					FromActorId: "actor-1",
					ToActorId:   "actor-2",
					PiTo:        "piTo",
					State:       pb.RecurringPaymentState_ACTIVATED,
				}, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), testPkg.NewProtoArgMatcher(&orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}})).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Status: orderPb.OrderStatus_FULFILLED,
					},
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					Action: pb.Action_CREATE,
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{
					Id: "piTo",
				}).Return(&piPb.GetPiByIdResponse{
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								MerchantDetails: &piPb.Upi_MerchantDetails{
									Mcc: "7322",
								},
							},
						},
					},
				}, nil)
			},
			want: &pb.GetRecurringPaymentActionStatusResponse{
				Status:         rpc.StatusOk(),
				ActionState:    pb.GetRecurringPaymentActionStatusResponse_SUCCESS,
				WarningMessage: mccwarningMessage,
			},
			wantErr: false,
		},
		{
			name: "fetched action state for execution and return utr for pending transaction",
			req: &pb.GetRecurringPaymentActionStatusRequest{
				RecurringPaymentId: "rp-1",
				Action:             pb.Action_EXECUTE,
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
			},
			setupMockCalls: func() {
				_ = dynamicConf.FeatureFlags().SetEnableRecurringPaymentExecutionWithoutAuthViaCelestial(false, false, nil)
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(&pb.RecurringPayment{
					Id:          "rp-1",
					FromActorId: "actor-1",
					ToActorId:   "actor-2",
					PiTo:        "piTo",
					Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
					State:       pb.RecurringPaymentState_ACTIVATED,
				}, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:     "order-id-1",
						Status: orderPb.OrderStatus_IN_PAYMENT,
					},
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					Action: pb.Action_EXECUTE,
				}, nil)
				mockOrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{OrderId: "order-id-1"}).Return(&orderPb.GetOrderWithTransactionsResponse{
					Status: rpc.StatusOk(),
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:     "order-id-1",
							Status: orderPb.OrderStatus_IN_PAYMENT,
						},
						Transactions: []*paymentPb.Transaction{
							{
								Utr:    "utr-1",
								Status: paymentPb.TransactionStatus_IN_PROGRESS,
							},
						},
					},
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{
					Id: "piTo",
				}).Return(&piPb.GetPiByIdResponse{
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								MerchantDetails: &piPb.Upi_MerchantDetails{
									Mcc: "7322",
								},
							},
						},
					},
				}, nil)
			},
			want: &pb.GetRecurringPaymentActionStatusResponse{
				Status:      rpc.StatusOk(),
				ActionState: pb.GetRecurringPaymentActionStatusResponse_IN_PROGRESS,
				MetaData: &pb.GetRecurringPaymentActionStatusResponse_ExecutionMetaData{ExecutionMetaData: &pb.ExecutionMetaData{
					UtrRefNumber: "utr-1",
				}},
				WarningMessage: mccwarningMessage,
			},
			wantErr: false,
		},
		{
			name: "fetched action state for execution and return failure code for failed transaction",
			req: &pb.GetRecurringPaymentActionStatusRequest{
				RecurringPaymentId: "rp-1",
				Action:             pb.Action_EXECUTE,
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(&pb.RecurringPayment{
					Id:          "rp-1",
					FromActorId: "actor-1",
					ToActorId:   "actor-2",
					PiTo:        "piTo",
					Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
					State:       pb.RecurringPaymentState_ACTIVATED,
				}, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:     "order-id-1",
						Status: orderPb.OrderStatus_PAYMENT_FAILED,
					},
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					Action: pb.Action_EXECUTE,
				}, nil)
				mockOrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{OrderId: "order-id-1"}).Return(&orderPb.GetOrderWithTransactionsResponse{
					Status: rpc.StatusOk(),
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:     "order-id-1",
							Status: orderPb.OrderStatus_PAYMENT_FAILED,
						},
						Transactions: []*paymentPb.Transaction{
							{
								Utr:    "utr-1",
								Status: paymentPb.TransactionStatus_FAILED,
								DetailedStatus: &paymentPb.TransactionDetailedStatus{
									DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
										{
											StatusCodePayer: "epifi-code-1",
										},
									},
								},
							},
						},
					},
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{
					Id: "piTo",
				}).Return(&piPb.GetPiByIdResponse{
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								MerchantDetails: &piPb.Upi_MerchantDetails{
									Mcc: "7322",
								},
							},
						},
					},
				}, nil)
			},
			want: &pb.GetRecurringPaymentActionStatusResponse{
				Status:      rpc.StatusOk(),
				ActionState: pb.GetRecurringPaymentActionStatusResponse_FAILURE,
				MetaData: &pb.GetRecurringPaymentActionStatusResponse_ExecutionMetaData{ExecutionMetaData: &pb.ExecutionMetaData{
					UtrRefNumber:        "utr-1",
					FailureResponseCode: "epifi-code-1",
				}},
				WarningMessage: mccwarningMessage,
			},
			wantErr: false,
		},
		{
			name: "recurring payment not found",
			req: &pb.GetRecurringPaymentActionStatusRequest{
				RecurringPaymentId: "rp-1",
				Action:             pb.Action_EXECUTE,
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &pb.GetRecurringPaymentActionStatusResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "invalid action type",
			req: &pb.GetRecurringPaymentActionStatusRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(&pb.RecurringPayment{
					Id:          "rp-1",
					FromActorId: "actor-1",
					ToActorId:   "actor-2",
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					Action: pb.Action_ACTION_UNSPECIFIED,
				}, nil)
			},
			want: &pb.GetRecurringPaymentActionStatusResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "fetched action status successfully for create using celestial flow",
			req: &pb.GetRecurringPaymentActionStatusRequest{
				RecurringPaymentId: "rp-1",
				Action:             pb.Action_CREATE,
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(&pb.RecurringPayment{
					Id:          "rp-1",
					FromActorId: "actor-1",
					ToActorId:   "actor-2",
					PiTo:        "piTo",
					State:       pb.RecurringPaymentState_ACTIVATED,
				}, nil)
				mockCelestialProcessor.
					EXPECT().
					GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).
					Return(&celestialPb.WorkflowRequest{Id: "workflow-req-id", Status: stagePb.Status_SUCCESSFUL}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					Action: pb.Action_CREATE,
				}, nil)

				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{
					Id: "piTo",
				}).Return(&piPb.GetPiByIdResponse{
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								MerchantDetails: &piPb.Upi_MerchantDetails{
									Mcc: "7322",
								},
							},
						},
					},
				}, nil)
			},
			want: &pb.GetRecurringPaymentActionStatusResponse{
				Status:         rpc.StatusOk(),
				ActionState:    pb.GetRecurringPaymentActionStatusResponse_SUCCESS,
				WarningMessage: mccwarningMessage,
			},
			wantErr: false,
		},
		{
			name: "failed to fetch action status using celestial and oms flow, because workflow was not found",
			req: &pb.GetRecurringPaymentActionStatusRequest{
				RecurringPaymentId: "rp-1",
				Action:             pb.Action_CREATE,
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_USER_APP,
				},
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(&pb.RecurringPayment{
					Id:          "rp-1",
					FromActorId: "actor-1",
					ToActorId:   "actor-2",
					State:       pb.RecurringPaymentState_ACTIVATED,
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					Action: pb.Action_CREATE,
				}, nil)
				mockCelestialProcessor.
					EXPECT().
					GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).
					Return(nil, rpc.StatusAsError(rpc.StatusRecordNotFound()))
				mockCelestialProcessor.
					EXPECT().
					GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_USER_APP).
					Return(nil, rpc.StatusAsError(rpc.StatusRecordNotFound()))
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			want: &pb.GetRecurringPaymentActionStatusResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "failed to fetch action status using celestial flow, because workflow status was failed",
			req: &pb.GetRecurringPaymentActionStatusRequest{
				RecurringPaymentId: "rp-1",
				Action:             pb.Action_CREATE,
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(&pb.RecurringPayment{
					Id:          "rp-1",
					FromActorId: "actor-1",
					ToActorId:   "actor-2",
					PiTo:        "piTo",
					State:       pb.RecurringPaymentState_ACTIVATED,
				}, nil)
				mockCelestialProcessor.
					EXPECT().
					GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).
					Return(&celestialPb.WorkflowRequest{Id: "workflow-req-id", Status: stagePb.Status_FAILED}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					Action: pb.Action_CREATE,
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{
					Id: "piTo",
				}).Return(&piPb.GetPiByIdResponse{
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								MerchantDetails: &piPb.Upi_MerchantDetails{
									Mcc: "7322",
								},
							},
						},
					},
				}, nil)
			},
			want: &pb.GetRecurringPaymentActionStatusResponse{
				Status:         rpc.StatusOk(),
				ActionState:    pb.GetRecurringPaymentActionStatusResponse_FAILURE,
				WarningMessage: mccwarningMessage,
			},
			wantErr: false,
		},
		{
			name: "failed to fetch action status using celestial flow, because celestial processor returned error",
			req: &pb.GetRecurringPaymentActionStatusRequest{
				RecurringPaymentId: "rp-1",
				Action:             pb.Action_CREATE,
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(&pb.RecurringPayment{
					Id:          "rp-1",
					FromActorId: "actor-1",
					ToActorId:   "actor-2",
					State:       pb.RecurringPaymentState_ACTIVATED,
				}, nil)
				mockCelestialProcessor.
					EXPECT().
					GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).
					Return(nil, rpc.StatusAsError(rpc.StatusInternal()))
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					Action: pb.Action_CREATE,
				}, nil)
			},
			want: &pb.GetRecurringPaymentActionStatusResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "failed to fetch action status using celestial flow, because workflow status was failed",
			req: &pb.GetRecurringPaymentActionStatusRequest{
				RecurringPaymentId: "rp-1",
				Action:             pb.Action_CREATE,
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(&pb.RecurringPayment{
					Id:          "rp-1",
					FromActorId: "actor-1",
					ToActorId:   "actor-2",
					State:       pb.RecurringPaymentState_ACTIVATED,
				}, nil)
				mockCelestialProcessor.
					EXPECT().
					GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).
					Return(&celestialPb.WorkflowRequest{Id: "workflow-req-id", Status: stagePb.Status_STATUS_UNSPECIFIED}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					Action: pb.Action_CREATE,
				}, nil)
			},
			want: &pb.GetRecurringPaymentActionStatusResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "fetched action state for execution using celestial flow and return utr for pending transaction",
			req: &pb.GetRecurringPaymentActionStatusRequest{
				RecurringPaymentId: "rp-1",
				Action:             pb.Action_EXECUTE,
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				_ = dynamicConf.FeatureFlags().SetEnableRecurringPaymentExecutionWithoutAuthViaCelestial(true, false, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(&pb.RecurringPayment{
					Id:          "rp-1",
					FromActorId: "actor-1",
					ToActorId:   "actor-2",
					PiTo:        "piTo",
					Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
					State:       pb.RecurringPaymentState_ACTIVATED,
				}, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:            "order-id-1",
						Status:        orderPb.OrderStatus_IN_PAYMENT,
						WorkflowRefId: "wf-req-id",
					},
				}, nil)
				mockCelestialProcessor.
					EXPECT().
					GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).
					Return(&celestialPb.WorkflowRequest{Id: "workflow-req-id", Status: stagePb.Status_MANUAL_INTERVENTION}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					Action: pb.Action_EXECUTE,
				}, nil)
				mockOrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{OrderId: "order-id-1"}).Return(&orderPb.GetOrderWithTransactionsResponse{
					Status: rpc.StatusOk(),
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:     "order-id-1",
							Status: orderPb.OrderStatus_IN_PAYMENT,
						},
						Transactions: []*paymentPb.Transaction{
							{
								Utr:    "utr-1",
								Status: paymentPb.TransactionStatus_IN_PROGRESS,
							},
						},
					},
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{
					Id: "piTo",
				}).Return(&piPb.GetPiByIdResponse{
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								MerchantDetails: &piPb.Upi_MerchantDetails{
									Mcc: "7322",
								},
							},
						},
					},
				}, nil)
			},
			want: &pb.GetRecurringPaymentActionStatusResponse{
				Status:      rpc.StatusOk(),
				ActionState: pb.GetRecurringPaymentActionStatusResponse_IN_PROGRESS,
				MetaData: &pb.GetRecurringPaymentActionStatusResponse_ExecutionMetaData{ExecutionMetaData: &pb.ExecutionMetaData{
					UtrRefNumber: "utr-1",
				}},
				WarningMessage: mccwarningMessage,
			},
			wantErr: false,
		},
		{
			name: "fetched action status successfully for create with postActionDeeplink",
			req: &pb.GetRecurringPaymentActionStatusRequest{
				RecurringPaymentId: "rp-1",
				Action:             pb.Action_CREATE,
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(&pb.RecurringPayment{
					Id:          "rp-1",
					FromActorId: "actor-1",
					ToActorId:   "actor-2",
					PiTo:        "piTo",
					State:       pb.RecurringPaymentState_ACTIVATED,
				}, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), testPkg.NewProtoArgMatcher(&orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}})).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Status: orderPb.OrderStatus_FULFILLED,
					},
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					Action:             pb.Action_CREATE,
					PostActionDeeplink: postActionDeeplinkAny,
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{
					Id: "piTo",
				}).Return(&piPb.GetPiByIdResponse{
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								MerchantDetails: &piPb.Upi_MerchantDetails{
									Mcc: "7322",
								},
							},
						},
					},
				}, nil)
			},
			want: &pb.GetRecurringPaymentActionStatusResponse{
				Status:             rpc.StatusOk(),
				ActionState:        pb.GetRecurringPaymentActionStatusResponse_SUCCESS,
				WarningMessage:     mccwarningMessage,
				PostActionDeeplink: postActionDeeplink,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.GetRecurringPaymentActionStatus(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRecurringPaymentActionStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetRecurringPaymentActionStatus() got = %v\n, want %v\n", got, tt.want)
			}
		})
	}
}

func TestService_GetRecurringPaymentIdByVendorRequestId(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.GetRecurringPaymentIdByVendorRequestIdRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockEnachClient *enachMocks.MockEnachServiceClient)
		want       *pb.GetRecurringPaymentIdByVendorRequestIdResponse
		wantErr    bool
	}{
		{
			name: "should return ISE rpc status code when enach rpc call to fetch recurring payment id fails",
			args: args{
				ctx: context.Background(),
				req: &pb.GetRecurringPaymentIdByVendorRequestIdRequest{
					RecurringPaymentType: pb.RecurringPaymentType_ENACH_MANDATES,
					VendorRequestId:      "vendor-request-id-1",
				},
			},
			setupMocks: func(mockEnachClient *enachMocks.MockEnachServiceClient) {
				mockEnachClient.EXPECT().GetRecurringPaymentIdByVendorReqId(context.Background(), &enachPb.GetRecurringPaymentIdByVendorReqIdRequest{ActionType: enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE,
					VendorReqId: "vendor-request-id-1"}).Return(&enachPb.GetRecurringPaymentIdByVendorReqIdResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: &pb.GetRecurringPaymentIdByVendorRequestIdResponse{
				Status: rpc.StatusInternalWithDebugMsg("enachClient.GetRecurringPaymentIdByVendorReqId rpc call failed"),
			},
		},
		{
			name: "should return OK rpc status code with recurring payment id",
			args: args{
				ctx: context.Background(),
				req: &pb.GetRecurringPaymentIdByVendorRequestIdRequest{
					RecurringPaymentType: pb.RecurringPaymentType_ENACH_MANDATES,
					VendorRequestId:      "vendor-request-id-1",
				},
			},
			setupMocks: func(mockEnachClient *enachMocks.MockEnachServiceClient) {
				mockEnachClient.EXPECT().GetRecurringPaymentIdByVendorReqId(context.Background(), &enachPb.GetRecurringPaymentIdByVendorReqIdRequest{ActionType: enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE,
					VendorReqId: "vendor-request-id-1"}).Return(&enachPb.GetRecurringPaymentIdByVendorReqIdResponse{
					Status:             rpc.StatusOk(),
					RecurringPaymentId: "recurring-payment-id-1",
				}, nil)
			},
			want: &pb.GetRecurringPaymentIdByVendorRequestIdResponse{
				Status:             rpc.StatusOk(),
				RecurringPaymentId: "recurring-payment-id-1",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockEnachClient := enachMocks.NewMockEnachServiceClient(ctr)

			tt.setupMocks(mockEnachClient)

			s := &Service{
				enachClient: mockEnachClient,
			}
			got, err := s.GetRecurringPaymentIdByVendorRequestId(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRecurringPaymentIdByVendorRequestId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetRecurringPaymentIdByVendorRequestId() got = %v, want %v", got, tt.want)
			}
		})
	}
}
