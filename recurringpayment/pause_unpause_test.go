package recurringpayment

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	"github.com/epifi/gamma/api/upi/mandate"
	"github.com/epifi/gamma/api/upi/mandate/mocks"
	internalMocks "github.com/epifi/gamma/recurringpayment/internal/mocks"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/money"

	orderPb "github.com/epifi/gamma/api/order"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	pb "github.com/epifi/gamma/api/recurringpayment"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	mocks2 "github.com/epifi/gamma/recurringpayment/internal/mocks"
)

func TestService_CreatePauseOrUnpauseAttempt(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockOrderClient := orderMocks.NewMockOrderServiceClient(ctr)
	mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
	mockRecurringPaymentsActionDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
	mockRecurringPaymentProcessor := mocks2.NewMockRecurringPaymentProcessor(ctr)
	mockUpiMandateClient := mocks.NewMockMandateServiceClient(ctr)
	mockCelestialProcessor := internalMocks.NewMockCelestialProcessor(ctr)

	mockRecurringPaymentVendorDetailsDao := daoMocks.NewMockRecurringPaymentsVendorDetailsDao(ctr)
	svc := NewService(mockRecurringPaymentDao, mockOrderClient, nil, mockRecurringPaymentsActionDao, nil, nil, nil, nil, nil, conf, mockRecurringPaymentProcessor, mockUpiMandateClient, nil, nil, nil, nil, nil, nil, dynamicConf.FeatureFlags(), mockCelestialProcessor, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, mockRecurringPaymentVendorDetailsDao, nil, nil, nil, nil)

	mandatePayload := &mandate.Payload{}

	marshalledMandatePayload, _ := protojson.Marshal(mandatePayload)

	rp1 := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		Amount:      money.AmountINR(100).Pb,
		State:       pb.RecurringPaymentState_ACTIVATED,
		InitiatedBy: pb.InitiatedBy_PAYER,
	}

	marshalledPayload, _ := protojson.Marshal(&pb.RecurringPaymentPauseUnpauseInfo{
		RecurringPaymentId: "rp-1",
		ClientRequestId:    "client-req-1",
		Action:             pb.Action_PAUSE,
	})
	tests := []struct {
		name           string
		req            *pb.CreatePauseOrUnpauseAttemptRequest
		setupMockCalls func()
		want           *pb.CreatePauseOrUnpauseAttemptResponse
		wantErr        bool
	}{
		{
			name: "created pause attempt successfully",
			req: &pb.CreatePauseOrUnpauseAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				Action:             pb.Action_PAUSE,
				CurrentActorId:     "actor-1",
				TransactionId:      "txn-id-1",
				CurrentActorRole:   pb.ActorRole_ACTOR_ROLE_PAYER,
				Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
				Payload:            marshalledMandatePayload,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentPauseUnpauseViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(rp1, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(),
					gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_UPI_MANDATES,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(false, pb.CredBlockType_CRED_BLOCK_TYPE_UNSPECIFIED, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), rp1, nil,
					pb.RecurringPaymentState_ACTIVATED, pb.RecurringPaymentState_PAUSE_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_PAUSE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "txn-id-1",
					InitiatedBy:        pb.InitiatedBy_PAYER,
				}).Return(nil, nil)
				mockUpiMandateClient.EXPECT().PauseUnpauseMandate(gomock.Any(), &mandate.PauseUnpauseMandateRequest{
					RecurringPaymentId:    "rp-1",
					MandateRequestPayload: mandatePayload,
					ReqId:                 "txn-id-1",
					CurrentActorRole:      mandate.ActorRole_ACTOR_ROLE_PAYER,
					ReqAction:             mandate.MandateType_PAUSE,
				}).Return(&mandate.PauseUnpauseMandateResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockOrderClient.EXPECT().CreateOrder(gomock.Any(), &orderPb.CreateOrderRequest{
					ActorFrom:    "actor-1",
					ActorTo:      "actor-2",
					Workflow:     orderPb.OrderWorkflow_PAUSE_OR_UNPAUSE_RECURRING_PAYMENT,
					Provenance:   orderPb.OrderProvenance_USER_APP,
					OrderPayload: marshalledPayload,
					Amount:       rp1.GetAmount(),
					Status:       orderPb.OrderStatus_CREATED,
					ClientReqId:  "client-req-1",
				}).Return(&orderPb.CreateOrderResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want: &pb.CreatePauseOrUnpauseAttemptResponse{
				Status:                   rpc.StatusOk(),
				TxnId:                    "txn-id-1",
				IsAuthenticationRequired: false,
				CredBlockType:            pb.CredBlockType_CRED_BLOCK_TYPE_UNSPECIFIED,
			},
			wantErr: false,
		},
		{
			name: "failed due to pending action exists for pause",
			req: &pb.CreatePauseOrUnpauseAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				Action:             pb.Action_PAUSE,
				CurrentActorId:     "actor-1",
				TransactionId:      "txn-id-1",
				CurrentActorRole:   pb.ActorRole_ACTOR_ROLE_PAYER,
				Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
			},
			setupMockCalls: func() {
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(), gomock.Any()).Return([]*pb.RecurringPaymentsAction{
					{
						RecurringPaymentId: "rp-1",
						ClientRequestId:    "client-req-2",
						Action:             pb.Action_PAUSE,
					},
				}, nil)
			},
			want: &pb.CreatePauseOrUnpauseAttemptResponse{
				Status: rpc.StatusFailedPrecondition(),
			},
			wantErr: false,
		},
		{
			name: "order already exists",
			req: &pb.CreatePauseOrUnpauseAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				Action:             pb.Action_PAUSE,
				CurrentActorId:     "actor-1",
				TransactionId:      "txn-id-1",
				CurrentActorRole:   pb.ActorRole_ACTOR_ROLE_PAYER,
				Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
			},
			setupMockCalls: func() {
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want: &pb.CreatePauseOrUnpauseAttemptResponse{
				Status: rpc.StatusAlreadyExists(),
			},
			wantErr: false,
		},
		{
			name: "error in fetching recurring payment",
			req: &pb.CreatePauseOrUnpauseAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				Action:             pb.Action_PAUSE,
				CurrentActorId:     "actor-1",
				TransactionId:      "txn-id-1",
				CurrentActorRole:   pb.ActorRole_ACTOR_ROLE_PAYER,
				Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
			},
			setupMockCalls: func() {
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(nil, fmt.Errorf("error"))
			},
			want: &pb.CreatePauseOrUnpauseAttemptResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "failure in fetching auth type ",
			req: &pb.CreatePauseOrUnpauseAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				Action:             pb.Action_PAUSE,
				CurrentActorId:     "actor-1",
				TransactionId:      "txn-id-1",
				CurrentActorRole:   pb.ActorRole_ACTOR_ROLE_PAYER,
				Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
			},
			setupMockCalls: func() {
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(rp1, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(),
					gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_UPI_MANDATES,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(false, pb.CredBlockType_CRED_BLOCK_TYPE_UNSPECIFIED, fmt.Errorf("error"))
			},
			want: &pb.CreatePauseOrUnpauseAttemptResponse{
				Status: rpc.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "error in updating recurring payment pause attempt successfully",
			req: &pb.CreatePauseOrUnpauseAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				Action:             pb.Action_PAUSE,
				CurrentActorId:     "actor-1",
				TransactionId:      "txn-id-1",
				CurrentActorRole:   pb.ActorRole_ACTOR_ROLE_PAYER,
				Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
			},
			setupMockCalls: func() {
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(rp1, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(),
					gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_UPI_MANDATES,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(false, pb.CredBlockType_CRED_BLOCK_TYPE_UNSPECIFIED, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(nil,
					epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), rp1, nil,
					pb.RecurringPaymentState_ACTIVATED, pb.RecurringPaymentState_PAUSE_QUEUED).Return(fmt.Errorf("error"))
			},
			want: &pb.CreatePauseOrUnpauseAttemptResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "error in creating order",
			req: &pb.CreatePauseOrUnpauseAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				Action:             pb.Action_PAUSE,
				CurrentActorId:     "actor-1",
				TransactionId:      "txn-id-1",
				CurrentActorRole:   pb.ActorRole_ACTOR_ROLE_PAYER,
				Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
				Payload:            marshalledMandatePayload,
			},
			setupMockCalls: func() {
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(rp1, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(),
					gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_UPI_MANDATES,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(false, pb.CredBlockType_CRED_BLOCK_TYPE_UNSPECIFIED, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(nil,
					epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), rp1, nil,
					pb.RecurringPaymentState_ACTIVATED, pb.RecurringPaymentState_PAUSE_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_PAUSE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "txn-id-1",
					InitiatedBy:        pb.InitiatedBy_PAYER,
				}).Return(nil, nil)
				mockUpiMandateClient.EXPECT().PauseUnpauseMandate(gomock.Any(), &mandate.PauseUnpauseMandateRequest{
					RecurringPaymentId:    "rp-1",
					MandateRequestPayload: mandatePayload,
					ReqId:                 "txn-id-1",
					CurrentActorRole:      mandate.ActorRole_ACTOR_ROLE_PAYER,
					ReqAction:             mandate.MandateType_PAUSE,
				}).Return(&mandate.PauseUnpauseMandateResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockOrderClient.EXPECT().CreateOrder(gomock.Any(), &orderPb.CreateOrderRequest{
					ActorFrom:    "actor-1",
					ActorTo:      "actor-2",
					Workflow:     orderPb.OrderWorkflow_PAUSE_OR_UNPAUSE_RECURRING_PAYMENT,
					Provenance:   orderPb.OrderProvenance_USER_APP,
					OrderPayload: marshalledPayload,
					Amount:       rp1.GetAmount(),
					Status:       orderPb.OrderStatus_CREATED,
					ClientReqId:  "client-req-1",
				}).Return(&orderPb.CreateOrderResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: &pb.CreatePauseOrUnpauseAttemptResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "created pause attempt successfully using celestial flow",
			req: &pb.CreatePauseOrUnpauseAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				Action:             pb.Action_PAUSE,
				CurrentActorId:     "actor-1",
				TransactionId:      "txn-id-1",
				CurrentActorRole:   pb.ActorRole_ACTOR_ROLE_PAYER,
				Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
				Payload:            marshalledMandatePayload,
				ClientId: &workflowPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentPauseUnpauseViaCelestial(true, true, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				}, "actor-1", marshalledPayload, celestialPkg.GetTypeEnumFromWorkflowType(rpNs.PauseUnpauseRecurringPayment), workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT).Return(nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(rp1, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(),
					gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_UPI_MANDATES,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(false, pb.CredBlockType_CRED_BLOCK_TYPE_UNSPECIFIED, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), rp1, nil,
					pb.RecurringPaymentState_ACTIVATED, pb.RecurringPaymentState_PAUSE_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_PAUSE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "txn-id-1",
					InitiatedBy:        pb.InitiatedBy_PAYER,
				}).Return(nil, nil)
				mockUpiMandateClient.EXPECT().PauseUnpauseMandate(gomock.Any(), &mandate.PauseUnpauseMandateRequest{
					RecurringPaymentId:    "rp-1",
					MandateRequestPayload: mandatePayload,
					ReqId:                 "txn-id-1",
					CurrentActorRole:      mandate.ActorRole_ACTOR_ROLE_PAYER,
					ReqAction:             mandate.MandateType_PAUSE,
				}).Return(&mandate.PauseUnpauseMandateResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want: &pb.CreatePauseOrUnpauseAttemptResponse{
				Status:                   rpc.StatusOk(),
				TxnId:                    "txn-id-1",
				IsAuthenticationRequired: false,
				CredBlockType:            pb.CredBlockType_CRED_BLOCK_TYPE_UNSPECIFIED,
			},
			wantErr: false,
		},
		{
			name: "should create pause attempt with state PAUSE_QUEUED when current actor role is empty",
			req: &pb.CreatePauseOrUnpauseAttemptRequest{
				RecurringPaymentId: "rp-1",
				ClientRequestId:    "client-req-1",
				Action:             pb.Action_PAUSE,
				CurrentActorId:     "actor-1",
				TransactionId:      "txn-id-1",
				Provenance:         pb.RecurrencePaymentProvenance_USER_APP,
				Payload:            marshalledMandatePayload,
				ClientId: &workflowPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentPauseUnpauseViaCelestial(true, true, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				}, "actor-1", marshalledPayload, celestialPkg.GetTypeEnumFromWorkflowType(rpNs.PauseUnpauseRecurringPayment), workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT).Return(nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(rp1, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(),
					gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_UPI_MANDATES,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(false, pb.CredBlockType_CRED_BLOCK_TYPE_UNSPECIFIED, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), rp1, nil,
					pb.RecurringPaymentState_ACTIVATED, pb.RecurringPaymentState_PAUSE_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_PAUSE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "txn-id-1",
					InitiatedBy:        pb.InitiatedBy_PAYER,
				}).Return(nil, nil)
				mockUpiMandateClient.EXPECT().PauseUnpauseMandate(gomock.Any(), &mandate.PauseUnpauseMandateRequest{
					RecurringPaymentId:    "rp-1",
					MandateRequestPayload: mandatePayload,
					ReqId:                 "txn-id-1",
					CurrentActorRole:      mandate.ActorRole_ACTOR_ROLE_PAYER,
					ReqAction:             mandate.MandateType_PAUSE,
				}).Return(&mandate.PauseUnpauseMandateResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want: &pb.CreatePauseOrUnpauseAttemptResponse{
				Status:                   rpc.StatusOk(),
				TxnId:                    "txn-id-1",
				IsAuthenticationRequired: false,
				CredBlockType:            pb.CredBlockType_CRED_BLOCK_TYPE_UNSPECIFIED,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.CreatePauseOrUnpauseAttempt(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreatePauseOrUnpauseAttempt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CreatePauseOrUnpauseAttempt() got = %v, want %v", got, tt.want)
			}
		})
	}
}
