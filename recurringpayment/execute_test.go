package recurringpayment

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/test"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	accountBalanceMock "github.com/epifi/gamma/api/accounts/balance/mocks"
	actorPb "github.com/epifi/gamma/api/actor"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	authMocks "github.com/epifi/gamma/api/auth/mocks"
	orderPb "github.com/epifi/gamma/api/order"
	mocks2 "github.com/epifi/gamma/api/order/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	mocks4 "github.com/epifi/gamma/api/order/payment/mocks"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	pb "github.com/epifi/gamma/api/recurringpayment"
	payloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	"github.com/epifi/gamma/api/recurringpayment/standinginstruction/mocks"
	savingsPb "github.com/epifi/gamma/api/savings"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	timelinePb "github.com/epifi/gamma/api/timeline"
	mocks3 "github.com/epifi/gamma/api/timeline/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
	mocks5 "github.com/epifi/gamma/api/upi/mandate/mocks"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	dao2 "github.com/epifi/gamma/recurringpayment/dao"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	internalMocks "github.com/epifi/gamma/recurringpayment/internal/mocks"
)

func TestService_ExecuteRecurringPayment(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
	mockOrderClient := mocks2.NewMockOrderServiceClient(ctr)
	mockPaymentClient := mocks4.NewMockPaymentClient(ctr)
	mockRecurringPaymentActionsDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
	mockSIClient := mocks.NewMockStandingInstructionServiceClient(ctr)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctr)
	mockActorClient := actorMocks.NewMockActorClient(ctr)
	mockAuthClient := authMocks.NewMockAuthClient(ctr)
	mockUserClient := userMocks.NewMockUsersClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockTimelineClient := mocks3.NewMockTimelineServiceClient(ctr)
	mockRecurringPaymentProcessor := internalMocks.NewMockRecurringPaymentProcessor(ctr)
	mockUpiMandateClient := mocks5.NewMockMandateServiceClient(ctr)
	mockCelestialProcessor := internalMocks.NewMockCelestialProcessor(ctr)
	mockaccountBalanceClient := accountBalanceMock.NewMockBalanceClient(ctr)
	mockUpiProcessor := internalMocks.NewMockUpiProcessor(ctr)

	mockRecurringPaymentVendorDetailsDao := daoMocks.NewMockRecurringPaymentsVendorDetailsDao(ctr)
	svc := NewService(mockRecurringPaymentDao, mockOrderClient, mockSIClient, mockRecurringPaymentActionsDao, mockSavingsClient, mockActorClient, mockAuthClient, mockUserClient, mockPaymentClient, conf, mockRecurringPaymentProcessor, mockUpiMandateClient, nil, mockPiClient, mockTimelineClient, nil, nil, dynamicConf.SIExecutionParams(), dynamicConf.FeatureFlags(), mockCelestialProcessor, nil, nil, nil, nil, nil, mockaccountBalanceClient, nil, nil, nil, nil, nil, nil, nil, nil, nil, mockRecurringPaymentVendorDetailsDao, mockUpiProcessor, nil, nil, nil)

	startDate := timestampPb.New(time.Now())
	endDate := timestampPb.New(time.Now().Add(48 * time.Hour))

	recurringPayment := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(5000).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}

	recurringPayment3 := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(5000).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_DAILY,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_PAUSED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
	}

	recurringPayment4 := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(5000).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
		ShareToPayee:             true,
	}

	recurringPayment5 := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(5000).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
		},
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYEE,
	}

	workflowPayload, _ := protojson.Marshal(&payloadPb.ExecuteRecurringPaymentWithoutAuth{})
	amount := money.AmountINR(100).GetPb()
	marshalledOrderPayload, _ := protojson.Marshal(&pb.RecurringPaymentExecutionInfo{
		RecurringPaymentId: recurringPayment.GetId(),
		Amount:             amount,
	})

	tests := []struct {
		name           string
		req            *pb.ExecuteRecurringPaymentRequest
		setupMockCalls func()
		want           *pb.ExecuteRecurringPaymentResponse
		wantErr        bool
	}{
		{
			name: "failed to fetch balance due to no account level access",
			req: &pb.ExecuteRecurringPaymentRequest{
				RecurringPaymentId: "rp-1",
				Amount:             money.AmountINR(100).GetPb(),
				ClientRequestId:    "client-request-id-1",
				ClientId:           &celestialPb.ClientReqId{Id: "client-request-id-1", Client: workflowPb.Client_RECURRING_PAYMENT},
			},
			setupMockCalls: func() {
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-request-id-1"}}).Return(&orderPb.GetOrderResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockRecurringPaymentActionsDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", false).Return(nil,
					epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment, nil)

				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: recurringPayment.GetFromActorId()}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type:     types.Actor_USER,
						EntityId: "entity-1",
					},
				}, nil)
				mockSavingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{PrimaryUserId: "entity-1"}}).Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{
					Id: "account-id-1",
				}}, nil)
				mockaccountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), &accountBalancePb.GetAccountBalanceRequest{
					Identifier: &accountBalancePb.GetAccountBalanceRequest_Id{Id: "account-id-1"},
					ActorId:    recurringPayment.GetFromActorId(),
				}).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.NewStatus(uint32(savingsPb.GetAccountBalanceV1Response_ACCOUNT_NO_LEVEL_ACCESS), "", ""),
				}, nil)
			},
			want: &pb.ExecuteRecurringPaymentResponse{
				Status:       rpc.NewStatus(uint32(pb.ExecuteRecurringPaymentResponse_ACCOUNT_NO_LEVEL_ACCESS), "no account level access to user", ""),
				OrderWithTxn: &orderPb.OrderWithTransactions{},
			},
			wantErr: false,
		},
		{
			name: "initiated execution successfully with as presented allowed frequency",
			req: &pb.ExecuteRecurringPaymentRequest{
				RecurringPaymentId: "rp-1",
				Amount:             amount,
				ClientRequestId:    "client-request-id-1",
				Tags:               []pb.RecurringPaymentTag{pb.RecurringPaymentTag_MUTUAL_FUND},
				ClientId:           &celestialPb.ClientReqId{Id: "client-request-id-1", Client: workflowPb.Client_RECURRING_PAYMENT},
			},
			setupMockCalls: func() {
				_ = dynamicConf.FeatureFlags().SetEnableRecurringPaymentExecutionWithoutAuthViaCelestial(true, false, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-request-id-1"}}).Return(&orderPb.GetOrderResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockRecurringPaymentActionsDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", false).Return(nil,
					epifierrors.ErrRecordNotFound)

				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: recurringPayment.GetFromActorId()}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type:     types.Actor_USER,
						EntityId: "entity-1",
					},
				}, nil)
				mockSavingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{PrimaryUserId: "entity-1"}}).Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{
					Id: "account-id-1",
				}}, nil)
				mockaccountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), &accountBalancePb.GetAccountBalanceRequest{
					Identifier: &accountBalancePb.GetAccountBalanceRequest_Id{Id: "account-id-1"},
					ActorId:    recurringPayment.GetFromActorId(),
				}).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &moneyPb.Money{
						CurrencyCode: money.RupeeCurrencyCode,
						Units:        1000,
						Nanos:        0,
					},
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().FetchActorName(gomock.Any(), recurringPayment.GetFromActorId()).Return(&commontypes.Name{
					FirstName:  "a",
					MiddleName: "b",
					LastName:   "c",
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().FetchActorName(gomock.Any(), recurringPayment.GetToActorId()).Return(&commontypes.Name{
					FirstName:  "x",
					MiddleName: "y",
					LastName:   "z",
				}, nil)
				mockTimelineClient.EXPECT().Create(gomock.Any(), &timelinePb.CreateRequest{
					PrimaryActorId:       recurringPayment.GetFromActorId(),
					SecondaryActorId:     recurringPayment.GetToActorId(),
					PrimaryActorName:     "A B C",
					SecondaryActorName:   "X Y Z",
					PrimaryActorSource:   timelinePb.TimelineSource_ACCOUNT_NUMBER,
					SecondaryActorSource: timelinePb.TimelineSource_ACCOUNT_NUMBER,
					Ownership:            timelinePb.Ownership_EPIFI_TECH,
				}).Return(&timelinePb.CreateResponse{Status: rpc.StatusOk(), Timeline: &timelinePb.Timeline{
					PrimaryActorId:   recurringPayment.GetFromActorId(),
					SecondaryActorId: recurringPayment.GetToActorId(),
				}}, nil)
				mockOrderClient.EXPECT().CreateOrderWithTransaction(gomock.Any(), newCreateOrderWithTransactionReqMatcher(&orderPb.CreateOrderWithTransactionRequest{
					OrderParam: &orderPb.CreateOrderWithTransactionRequest_OrderCreationParams{
						ActorFrom:    recurringPayment.GetFromActorId(),
						ActorTo:      recurringPayment.GetToActorId(),
						Workflow:     orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT,
						OrderPayload: marshalledOrderPayload,
						Status:       orderPb.OrderStatus_CREATED,
						ClientReqId:  "client-request-id-1",
						Provenance:   orderPb.OrderProvenance_INTERNAL,
						Tags:         []orderPb.OrderTag{orderPb.OrderTag_MUTUAL_FUND, orderPb.OrderTag_STANDING_INSTRUCTION},
					},
					TransactionParam: &orderPb.CreateOrderWithTransactionRequest_TransactionCreationParams{
						PiFrom:          recurringPayment.GetPiFrom(),
						PiTo:            recurringPayment.GetPiTo(),
						PaymentProtocol: paymentPb.PaymentProtocol(dynamicConf.SIExecutionParams().PaymentProtocol()),
						Status:          paymentPb.TransactionStatus_CREATED,
						ReqInfo:         &paymentPb.PaymentRequestInformation{ReqId: ""},
						Ownership:       commontypes.Ownership_FEDERAL_BANK,
					},
					Amount:                    amount,
					DisableWorkflowProcessing: dynamicConf.FeatureFlags().EnableRecurringPaymentExecutionWithoutAuthViaCelestial(),
				})).Return(&orderPb.CreateOrderWithTransactionResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:           "order-1",
						FromActorId:  recurringPayment.GetFromActorId(),
						ToActorId:    recurringPayment.GetToActorId(),
						Workflow:     orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT,
						Status:       orderPb.OrderStatus_CREATED,
						OrderPayload: marshalledOrderPayload,
						Amount:       amount,
						ClientReqId:  "client-request-id-1",
					},
					Transaction: &paymentPb.Transaction{Id: "tn-1"},
				}, nil)
				if dynamicConf.FeatureFlags().EnableRecurringPaymentExecutionWithoutAuthViaCelestial() {
					mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
						Id:     "client-request-id-1",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					}, "actor-1", workflowPayload, celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_EXECUTE_RECURRING_PAYMENT_WITHOUT_AUTH), workflowPb.Version_V0, celestialPb.QoS_GUARANTEED).Return(
						nil,
					)
				}
				mockRecurringPaymentActionsDao.EXPECT().Create(gomock.Any(), newCreateActionReqMatcher(&pb.RecurringPaymentsAction{
					RecurringPaymentId: recurringPayment.GetId(),
					ClientRequestId:    "client-request-id-1",
					Action:             pb.Action_EXECUTE,
					State:              pb.ActionState_ACTION_CREATED,
				})).Return(&pb.RecurringPaymentsAction{
					Id:                 "action-1",
					RecurringPaymentId: recurringPayment.GetId(),
					ClientRequestId:    "client-request-id-1",
					Action:             pb.Action_EXECUTE,
				}, nil)
			},
			want: &pb.ExecuteRecurringPaymentResponse{
				Status:  rpc.StatusOk(),
				OrderId: "order-1",
				OrderWithTxn: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:           "order-1",
						FromActorId:  recurringPayment.GetFromActorId(),
						ToActorId:    recurringPayment.GetToActorId(),
						Workflow:     orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT,
						Status:       orderPb.OrderStatus_CREATED,
						OrderPayload: marshalledOrderPayload,
						Amount:       amount,
						ClientReqId:  "client-request-id-1",
					},
					Transactions: []*paymentPb.Transaction{
						{Id: "tn-1"},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "initiated execution successfully with as presented allowed frequency and rp initiated by payee",
			req: &pb.ExecuteRecurringPaymentRequest{
				RecurringPaymentId: "rp-1",
				Amount:             amount,
				ClientRequestId:    "client-request-id-1",
				ClientId:           &celestialPb.ClientReqId{Id: "client-request-id-1", Client: workflowPb.Client_RECURRING_PAYMENT},
			},
			setupMockCalls: func() {
				_ = dynamicConf.FeatureFlags().SetEnableRecurringPaymentExecutionWithoutAuthViaCelestial(true, false, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-request-id-1"}}).Return(&orderPb.GetOrderResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment5, nil)
				mockRecurringPaymentActionsDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", false).Return(nil,
					epifierrors.ErrRecordNotFound)

				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: recurringPayment.GetFromActorId()}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type:     types.Actor_USER,
						EntityId: "entity-1",
					},
				}, nil)
				mockSavingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{PrimaryUserId: "entity-1"}}).Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{
					Id: "account-id-1",
				}}, nil)
				mockaccountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), &accountBalancePb.GetAccountBalanceRequest{
					Identifier: &accountBalancePb.GetAccountBalanceRequest_Id{Id: "account-id-1"},
					ActorId:    recurringPayment.GetFromActorId(),
				}).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &moneyPb.Money{
						CurrencyCode: money.RupeeCurrencyCode,
						Units:        1000,
						Nanos:        0,
					},
				}, nil)

				mockRecurringPaymentProcessor.EXPECT().FetchActorName(gomock.Any(), recurringPayment.GetFromActorId()).Return(&commontypes.Name{
					FirstName:  "a",
					MiddleName: "b",
					LastName:   "c",
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().FetchActorName(gomock.Any(), recurringPayment.GetToActorId()).Return(&commontypes.Name{
					FirstName:  "x",
					MiddleName: "y",
					LastName:   "z",
				}, nil)
				mockTimelineClient.EXPECT().Create(gomock.Any(), &timelinePb.CreateRequest{
					PrimaryActorId:       recurringPayment.GetToActorId(),
					SecondaryActorId:     recurringPayment.GetFromActorId(),
					PrimaryActorName:     "X Y Z",
					SecondaryActorName:   "A B C",
					PrimaryActorSource:   timelinePb.TimelineSource_ACCOUNT_NUMBER,
					SecondaryActorSource: timelinePb.TimelineSource_ACCOUNT_NUMBER,
					Ownership:            timelinePb.Ownership_EPIFI_TECH,
				}).Return(&timelinePb.CreateResponse{Status: rpc.StatusOk(), Timeline: &timelinePb.Timeline{
					PrimaryActorId:   recurringPayment.GetFromActorId(),
					SecondaryActorId: recurringPayment.GetToActorId(),
				}}, nil)
				mockTimelineClient.EXPECT().UpdateTimelineForActors(gomock.Any(), &timelinePb.UpdateTimelineForActorsRequest{
					PrimaryActorId:   recurringPayment.GetFromActorId(),
					SecondaryActorId: recurringPayment.GetToActorId(),
					SetVisibility:    true,
				}).Return(&timelinePb.UpdateTimelineForActorsResponse{Status: rpc.StatusOk()}, nil)
				mockOrderClient.EXPECT().CreateOrderWithTransaction(gomock.Any(), newCreateOrderWithTransactionReqMatcher(&orderPb.CreateOrderWithTransactionRequest{
					OrderParam: &orderPb.CreateOrderWithTransactionRequest_OrderCreationParams{
						ActorFrom:    recurringPayment.GetFromActorId(),
						ActorTo:      recurringPayment.GetToActorId(),
						Workflow:     orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT,
						OrderPayload: marshalledOrderPayload,
						Status:       orderPb.OrderStatus_CREATED,
						ClientReqId:  "client-request-id-1",
						Provenance:   orderPb.OrderProvenance_INTERNAL,
						Tags:         []orderPb.OrderTag{orderPb.OrderTag_STANDING_INSTRUCTION},
					},
					TransactionParam: &orderPb.CreateOrderWithTransactionRequest_TransactionCreationParams{
						PiFrom:          recurringPayment.GetPiFrom(),
						PiTo:            recurringPayment.GetPiTo(),
						PaymentProtocol: paymentPb.PaymentProtocol(dynamicConf.SIExecutionParams().PaymentProtocol()),
						Status:          paymentPb.TransactionStatus_CREATED,
						ReqInfo:         &paymentPb.PaymentRequestInformation{ReqId: ""},
						Ownership:       commontypes.Ownership_FEDERAL_BANK,
					},
					Amount:                    amount,
					DisableWorkflowProcessing: dynamicConf.FeatureFlags().EnableRecurringPaymentExecutionWithoutAuthViaCelestial(),
				})).Return(&orderPb.CreateOrderWithTransactionResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:           "order-1",
						FromActorId:  recurringPayment.GetFromActorId(),
						ToActorId:    recurringPayment.GetToActorId(),
						Workflow:     orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT,
						Status:       orderPb.OrderStatus_CREATED,
						OrderPayload: marshalledOrderPayload,
						Amount:       amount,
						ClientReqId:  "client-request-id-1",
					},
					Transaction: &paymentPb.Transaction{Id: "tn-1"},
				}, nil)
				if dynamicConf.FeatureFlags().EnableRecurringPaymentExecutionWithoutAuthViaCelestial() {
					mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
						Id:     "client-request-id-1",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					}, "actor-1", workflowPayload, celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_EXECUTE_RECURRING_PAYMENT_WITHOUT_AUTH), workflowPb.Version_V0, celestialPb.QoS_GUARANTEED).Return(
						nil,
					)
				}
				mockRecurringPaymentActionsDao.EXPECT().Create(gomock.Any(), newCreateActionReqMatcher(&pb.RecurringPaymentsAction{
					RecurringPaymentId: recurringPayment.GetId(),
					ClientRequestId:    "client-request-id-1",
					Action:             pb.Action_EXECUTE,
					State:              pb.ActionState_ACTION_CREATED,
				})).Return(&pb.RecurringPaymentsAction{
					Id:                 "action-1",
					RecurringPaymentId: recurringPayment.GetId(),
					ClientRequestId:    "client-request-id-1",
					Action:             pb.Action_EXECUTE,
				}, nil)
			},
			want: &pb.ExecuteRecurringPaymentResponse{
				Status:  rpc.StatusOk(),
				OrderId: "order-1",
				OrderWithTxn: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:           "order-1",
						FromActorId:  recurringPayment.GetFromActorId(),
						ToActorId:    recurringPayment.GetToActorId(),
						Workflow:     orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT,
						Status:       orderPb.OrderStatus_CREATED,
						OrderPayload: marshalledOrderPayload,
						Amount:       amount,
						ClientReqId:  "client-request-id-1",
					},
					Transactions: []*paymentPb.Transaction{
						{Id: "tn-1"},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "order already exist for given client id",
			req: &pb.ExecuteRecurringPaymentRequest{
				RecurringPaymentId: "rp-1",
				Amount:             amount,
				ClientRequestId:    "client-request-id-1",
				ClientId:           &celestialPb.ClientReqId{Id: "client-request-id-1", Client: workflowPb.Client_RECURRING_PAYMENT},
			},
			setupMockCalls: func() {
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-request-id-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order:  &orderPb.Order{Id: "order-id-1"},
				}, nil)
			},
			want: &pb.ExecuteRecurringPaymentResponse{
				Status:  rpc.StatusAlreadyExists(),
				OrderId: "order-id-1",
			},
			wantErr: false,
		},
		{
			name: "failed to fetch recurring payment",
			req: &pb.ExecuteRecurringPaymentRequest{
				RecurringPaymentId: "rp-1",
				Amount:             amount,
				ClientRequestId:    "client-request-id-1",
				ClientId:           &celestialPb.ClientReqId{Id: "client-request-id-1", Client: workflowPb.Client_RECURRING_PAYMENT},
			},
			setupMockCalls: func() {
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-request-id-1"}}).Return(&orderPb.GetOrderResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(nil, fmt.Errorf("error"))
			},
			want: &pb.ExecuteRecurringPaymentResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "recurring payment in paused state",
			req: &pb.ExecuteRecurringPaymentRequest{
				RecurringPaymentId: "rp-1",
				Amount:             amount,
				ClientRequestId:    "client-request-id-1",
				ClientId:           &celestialPb.ClientReqId{Id: "client-request-id-1", Client: workflowPb.Client_RECURRING_PAYMENT},
			},
			setupMockCalls: func() {
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-request-id-1"}}).Return(&orderPb.GetOrderResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockRecurringPaymentActionsDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", false).Return(nil,
					epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment3, nil)
			},
			want: &pb.ExecuteRecurringPaymentResponse{Status: rpc.NewStatusWithoutDebug(uint32(pb.ExecuteRecurringPaymentResponse_MANDATE_PAUSED),
				"mandate is paused"), OrderWithTxn: &orderPb.OrderWithTransactions{}},
			wantErr: false,
		},
		{
			name: "balance less than amount to be transferred",
			req: &pb.ExecuteRecurringPaymentRequest{
				RecurringPaymentId: "rp-1",
				Amount:             money.AmountINR(100).GetPb(),
				ClientRequestId:    "client-request-id-1",
				ClientId:           &celestialPb.ClientReqId{Id: "client-request-id-1", Client: workflowPb.Client_RECURRING_PAYMENT},
			},
			setupMockCalls: func() {
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-request-id-1"}}).Return(&orderPb.GetOrderResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockRecurringPaymentActionsDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", false).Return(nil,
					epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment, nil)

				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: recurringPayment.GetFromActorId()}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type:     types.Actor_USER,
						EntityId: "entity-1",
					},
				}, nil)
				mockSavingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{PrimaryUserId: "entity-1"}}).Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{
					Id: "account-id-1",
				}}, nil)
				mockaccountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), &accountBalancePb.GetAccountBalanceRequest{
					Identifier: &accountBalancePb.GetAccountBalanceRequest_Id{Id: "account-id-1"},
					ActorId:    recurringPayment.GetFromActorId(),
				}).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &moneyPb.Money{
						CurrencyCode: money.RupeeCurrencyCode,
						Units:        50,
						Nanos:        0,
					},
				}, nil)
			},
			want: &pb.ExecuteRecurringPaymentResponse{
				Status: rpc.NewStatusWithoutDebug(uint32(pb.ExecuteRecurringPaymentResponse_INSUFFICIENT_BALANCE),
					"account balance less that amount to be transferred"),
				OrderWithTxn: &orderPb.OrderWithTransactions{},
			},
			wantErr: false,
		},
		{
			name: "failed to fetch balance",
			req: &pb.ExecuteRecurringPaymentRequest{
				RecurringPaymentId: "rp-1",
				Amount:             money.AmountINR(100).GetPb(),
				ClientRequestId:    "client-request-id-1",
				ClientId:           &celestialPb.ClientReqId{Id: "client-request-id-1", Client: workflowPb.Client_RECURRING_PAYMENT},
			},
			setupMockCalls: func() {
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-request-id-1"}}).Return(&orderPb.GetOrderResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockRecurringPaymentActionsDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", false).Return(nil,
					epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment, nil)

				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: recurringPayment.GetFromActorId()}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type:     types.Actor_USER,
						EntityId: "entity-1",
					},
				}, nil)
				mockSavingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{PrimaryUserId: "entity-1"}}).Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{
					Id: "account-id-1",
				}}, nil)
				mockaccountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), &accountBalancePb.GetAccountBalanceRequest{
					Identifier: &accountBalancePb.GetAccountBalanceRequest_Id{Id: "account-id-1"},
					ActorId:    recurringPayment.GetFromActorId(),
				}).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: &pb.ExecuteRecurringPaymentResponse{
				Status:       rpc.StatusInternal(),
				OrderWithTxn: &orderPb.OrderWithTransactions{},
			},
			wantErr: false,
		},
		{
			name: "initiated execution successfully with as presented allowed frequency for mandate",
			req: &pb.ExecuteRecurringPaymentRequest{
				RecurringPaymentId: "rp-1",
				Amount:             amount,
				ClientRequestId:    "client-request-id-1",
				ClientId:           &celestialPb.ClientReqId{Id: "client-request-id-1", Client: workflowPb.Client_RECURRING_PAYMENT},
				ExecutionPayload: &pb.ExecuteRecurringPaymentRequest_UpiMandateExecutionInfo{
					UpiMandateExecutionInfo: &pb.UpiMandateExecuteInfo{
						PaymentRequestInfo: &paymentPb.PaymentRequestInformation{
							ReqId: "client-request-id-1",
							UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
								PayerVpa: "from@vpa",
								PayeeVpa: "to@vpa",
							},
						},
						Utr:    "utr",
						Note:   "note",
						SeqNum: 1,
					},
				},
			},
			setupMockCalls: func() {
				_ = dynamicConf.FeatureFlags().SetEnableRecurringPaymentExecutionWithAuthViaCelestial(true, false, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "client-request-id-1"}}).
					Return(&orderPb.GetOrderResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockRecurringPaymentActionsDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", false).Return(nil,
					epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment4, nil)
				mockUpiProcessor.EXPECT().IsAuthRequiredForMandateExecution(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
				mockUpiMandateClient.EXPECT().GetMandate(gomock.Any(), &upiMandatePb.GetMandateRequest{
					Identifier: &upiMandatePb.GetMandateRequest_RecurringPaymentId{RecurringPaymentId: "rp-1"},
				}).Return(&upiMandatePb.GetMandateResponse{
					Status:  rpc.StatusOk(),
					Mandate: &upiMandatePb.MandateEntity{Umn: "from@vpa"},
				}, nil)

				mockPiClient.EXPECT().GetPi(gomock.Any(), &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_UPI,
					Identifier: &piPb.GetPiRequest_UpiRequestParams_{
						UpiRequestParams: &piPb.GetPiRequest_UpiRequestParams{
							Vpa: "to@vpa",
						},
					},
				}).Return(&piPb.GetPiResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id: "pi-2",
					},
				}, nil)

				mockPiClient.EXPECT().GetPi(gomock.Any(), &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_UPI,
					Identifier: &piPb.GetPiRequest_UpiRequestParams_{
						UpiRequestParams: &piPb.GetPiRequest_UpiRequestParams{
							Vpa: "from@vpa",
						},
					},
				}).Return(&piPb.GetPiResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				}, nil)

				mockRecurringPaymentProcessor.EXPECT().FetchActorName(gomock.Any(), recurringPayment4.GetFromActorId()).Return(&commontypes.Name{
					FirstName:  "a",
					MiddleName: "b",
					LastName:   "c",
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().FetchActorName(gomock.Any(), recurringPayment4.GetToActorId()).Return(&commontypes.Name{
					FirstName:  "x",
					MiddleName: "y",
					LastName:   "z",
				}, nil)
				mockTimelineClient.EXPECT().Create(gomock.Any(), &timelinePb.CreateRequest{
					PrimaryActorId:       recurringPayment4.GetFromActorId(),
					SecondaryActorId:     recurringPayment4.GetToActorId(),
					PrimaryActorName:     "A B C",
					SecondaryActorName:   "X Y Z",
					PrimaryActorSource:   timelinePb.TimelineSource_VPA,
					SecondaryActorSource: timelinePb.TimelineSource_VPA,
					Ownership:            timelinePb.Ownership_EPIFI_TECH,
				}).Return(&timelinePb.CreateResponse{Status: rpc.StatusOk(), Timeline: &timelinePb.Timeline{
					PrimaryActorId:   recurringPayment4.GetFromActorId(),
					SecondaryActorId: recurringPayment4.GetToActorId(),
				}}, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: recurringPayment4.GetToActorId()}).
					Return(&actorPb.GetActorByIdResponse{
						Status: rpc.StatusOk(),
						Actor:  &types.Actor{Type: types.Actor_USER},
					}, nil)
				mockTimelineClient.EXPECT().UpdateTimelineForActors(gomock.Any(), &timelinePb.UpdateTimelineForActorsRequest{
					PrimaryActorId:   recurringPayment.GetFromActorId(),
					SecondaryActorId: recurringPayment.GetToActorId(),
					SetVisibility:    true,
				}).Return(&timelinePb.UpdateTimelineForActorsResponse{Status: rpc.StatusOk()}, nil)
				mockRecurringPaymentActionsDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), recurringPayment.GetId(),
					newFilterOptionMatcher([]dao2.FilterOption{dao2.WithActionsFilter([]pb.Action{pb.Action_EXECUTE}),
						dao2.WithOrderByCreatedAt(true),
						dao2.WithActionStateFilter([]pb.ActionState{pb.ActionState_ACTION_SUCCESS})})).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().CreateOrderWithTransaction(gomock.Any(), newCreateOrderWithTransactionReqMatcher(&orderPb.CreateOrderWithTransactionRequest{
					OrderParam: &orderPb.CreateOrderWithTransactionRequest_OrderCreationParams{
						ActorFrom:    recurringPayment4.GetFromActorId(),
						ActorTo:      recurringPayment4.GetToActorId(),
						Workflow:     orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH,
						OrderPayload: marshalledOrderPayload,
						Status:       orderPb.OrderStatus_CREATED,
						ClientReqId:  "client-request-id-1",
						Provenance:   orderPb.OrderProvenance_INTERNAL,
						Tags:         []orderPb.OrderTag{orderPb.OrderTag_UPI_MANDATES},
					},
					TransactionParam: &orderPb.CreateOrderWithTransactionRequest_TransactionCreationParams{
						PiFrom:          recurringPayment4.GetPiFrom(),
						PiTo:            recurringPayment4.GetPiTo(),
						PaymentProtocol: recurringPayment4.GetPreferredPaymentProtocol(),
						Status:          paymentPb.TransactionStatus_CREATED,
						ReqInfo: &paymentPb.PaymentRequestInformation{
							ReqId: "client-request-id-1",
							UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
								PayerVpa: "from@vpa",
								PayeeVpa: "to@vpa",
							},
						},
						Remarks:   "note",
						Utr:       "utr",
						Ownership: commontypes.Ownership_FEDERAL_BANK,
					},
					Amount:                    amount,
					DisableWorkflowProcessing: dynamicConf.FeatureFlags().EnableRecurringPaymentExecutionWithAuthViaCelestial(),
				})).Return(&orderPb.CreateOrderWithTransactionResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:           "order-1",
						FromActorId:  recurringPayment.GetFromActorId(),
						ToActorId:    recurringPayment.GetToActorId(),
						Workflow:     orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT,
						Status:       orderPb.OrderStatus_CREATED,
						OrderPayload: marshalledOrderPayload,
						Amount:       amount,
						ClientReqId:  "client-request-id-1",
					},
					Transaction: &paymentPb.Transaction{Id: "tn-1"},
				}, nil)
				if dynamicConf.FeatureFlags().EnableRecurringPaymentExecutionWithAuthViaCelestial() {
					mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
						Id:     "client-request-id-1",
						Client: workflowPb.Client_RECURRING_PAYMENT,
					}, "actor-1", workflowPayload, celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_EXECUTE_RECURRING_PAYMENT_WITHOUT_AUTH), workflowPb.Version_V0, celestialPb.QoS_GUARANTEED).Return(
						nil,
					)
				}
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: recurringPayment4.GetFromActorId()}).
					Return(&actorPb.GetActorByIdResponse{
						Status: rpc.StatusOk(),
						Actor:  &types.Actor{Type: types.Actor_USER},
					}, nil)
				mockRecurringPaymentActionsDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: recurringPayment.GetId(),
					ClientRequestId:    "client-request-id-1",
					Action:             pb.Action_EXECUTE,
					State:              pb.ActionState_ACTION_CREATED,
					ActionMetadata:     &pb.ActionMetadata{ExecuteActionMetadate: &pb.ExecuteActionMetaData{SeqNum: 1}},
				}).Return(&pb.RecurringPaymentsAction{
					Id:                 "action-1",
					RecurringPaymentId: recurringPayment.GetId(),
					ClientRequestId:    "client-request-id-1",
					Action:             pb.Action_EXECUTE,
				}, nil)
			},
			want: &pb.ExecuteRecurringPaymentResponse{
				Status:  rpc.StatusOk(),
				OrderId: "order-1",
				OrderWithTxn: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:           "order-1",
						FromActorId:  recurringPayment.GetFromActorId(),
						ToActorId:    recurringPayment.GetToActorId(),
						Workflow:     orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT,
						Status:       orderPb.OrderStatus_CREATED,
						OrderPayload: marshalledOrderPayload,
						Amount:       amount,
						ClientReqId:  "client-request-id-1",
					},
					Transactions: []*paymentPb.Transaction{
						{Id: "tn-1"},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "initiated execution failue with as presented allowed frequency for mandate due to mismatch pi",
			req: &pb.ExecuteRecurringPaymentRequest{
				RecurringPaymentId: "rp-1",
				Amount:             amount,
				ClientRequestId:    "client-request-id-1",
				ClientId:           &celestialPb.ClientReqId{Id: "client-request-id-1", Client: workflowPb.Client_RECURRING_PAYMENT},
				ExecutionPayload: &pb.ExecuteRecurringPaymentRequest_UpiMandateExecutionInfo{
					UpiMandateExecutionInfo: &pb.UpiMandateExecuteInfo{
						PaymentRequestInfo: &paymentPb.PaymentRequestInformation{
							ReqId: "client-request-id-1",
							UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
								PayerVpa: "from@vpa",
								PayeeVpa: "to@vpa",
							},
						},
						Utr:  "utr",
						Note: "note",
					},
				},
			},
			setupMockCalls: func() {
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "client-request-id-1"}}).
					Return(&orderPb.GetOrderResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockRecurringPaymentActionsDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", false).Return(nil,
					epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment4, nil)
				mockUpiMandateClient.EXPECT().GetMandate(gomock.Any(), &upiMandatePb.GetMandateRequest{
					Identifier: &upiMandatePb.GetMandateRequest_RecurringPaymentId{RecurringPaymentId: "rp-1"},
				}).Return(&upiMandatePb.GetMandateResponse{
					Status:  rpc.StatusOk(),
					Mandate: &upiMandatePb.MandateEntity{Umn: "fr@vpa"},
				}, nil)

				mockPiClient.EXPECT().GetPi(gomock.Any(), &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_UPI,
					Identifier: &piPb.GetPiRequest_UpiRequestParams_{
						UpiRequestParams: &piPb.GetPiRequest_UpiRequestParams{
							Vpa: "to@vpa",
						},
					},
				}).Return(&piPb.GetPiResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id: "pi-2",
					},
				}, nil)
				mockUpiProcessor.EXPECT().IsAuthRequiredForMandateExecution(gomock.Any(), &pb.UpiMandateExecuteInfo{
					PaymentRequestInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "client-request-id-1",
						UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
							PayerVpa: "from@vpa",
							PayeeVpa: "to@vpa",
						},
					},
					Utr:  "utr",
					Note: "note",
				}, pb.AllowedFrequency_AS_PRESENTED, test.NewProtoArgMatcher(money.AmountINR(100).GetPb()), nil).Return(false, nil)
			},
			want: &pb.ExecuteRecurringPaymentResponse{
				Status: rpc.NewStatusWithoutDebug(uint32(pb.ExecuteRecurringPaymentResponse_INVALID_PI),
					"invalid pi in payment request info"),
				OrderId:      "",
				OrderWithTxn: &orderPb.OrderWithTransactions{},
			},
			wantErr: false,
		},
		{
			name: "initiated execution failue with as presented allowed frequency for mandate due to mismatch pi",
			req: &pb.ExecuteRecurringPaymentRequest{
				RecurringPaymentId: "rp-1",
				Amount:             amount,
				ClientRequestId:    "client-request-id-1",
				ClientId:           &celestialPb.ClientReqId{Id: "client-request-id-1", Client: workflowPb.Client_RECURRING_PAYMENT},
				ExecutionPayload: &pb.ExecuteRecurringPaymentRequest_UpiMandateExecutionInfo{
					UpiMandateExecutionInfo: &pb.UpiMandateExecuteInfo{
						PaymentRequestInfo: &paymentPb.PaymentRequestInformation{
							ReqId: "client-request-id-1",
							UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
								PayerVpa: "from@vpa",
								PayeeVpa: "to@vpa",
							},
						},
						Utr:  "utr",
						Note: "note",
					},
				},
			},
			setupMockCalls: func() {
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "client-request-id-1"}}).
					Return(&orderPb.GetOrderResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockRecurringPaymentActionsDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", false).Return(nil,
					epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment4, nil)
				mockUpiMandateClient.EXPECT().GetMandate(gomock.Any(), &upiMandatePb.GetMandateRequest{
					Identifier: &upiMandatePb.GetMandateRequest_RecurringPaymentId{RecurringPaymentId: "rp-1"},
				}).Return(&upiMandatePb.GetMandateResponse{
					Status:  rpc.StatusOk(),
					Mandate: &upiMandatePb.MandateEntity{Umn: "fr@vpa"},
				}, nil)

				mockPiClient.EXPECT().GetPi(gomock.Any(), &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_UPI,
					Identifier: &piPb.GetPiRequest_UpiRequestParams_{
						UpiRequestParams: &piPb.GetPiRequest_UpiRequestParams{
							Vpa: "to@vpa",
						},
					},
				}).Return(&piPb.GetPiResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id: "pi-2",
					},
				}, nil)
				mockUpiProcessor.EXPECT().IsAuthRequiredForMandateExecution(gomock.Any(), &pb.UpiMandateExecuteInfo{
					PaymentRequestInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "client-request-id-1",
						UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
							PayerVpa: "from@vpa",
							PayeeVpa: "to@vpa",
						},
					},
					Utr:  "utr",
					Note: "note",
				}, pb.AllowedFrequency_AS_PRESENTED, test.NewProtoArgMatcher(money.AmountINR(100).GetPb()), nil).Return(true, nil)
			},
			want: &pb.ExecuteRecurringPaymentResponse{
				Status: rpc.NewStatusWithoutDebug(uint32(pb.ExecuteRecurringPaymentResponse_INVALID_PI),
					"invalid pi in payment request info"),
				OrderId:      "",
				OrderWithTxn: &orderPb.OrderWithTransactions{},
			},
			wantErr: false,
		},
		{
			name: "return unknown status code in case we don't know if order and transaction were created",
			req: &pb.ExecuteRecurringPaymentRequest{
				RecurringPaymentId: "rp-1",
				Amount:             amount,
				ClientRequestId:    "client-request-id-1",
				ClientId:           &celestialPb.ClientReqId{Id: "client-request-id-1", Client: workflowPb.Client_RECURRING_PAYMENT},
			},
			setupMockCalls: func() {
				_ = dynamicConf.FeatureFlags().SetEnableRecurringPaymentExecutionWithoutAuthViaCelestial(true, false, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-request-id-1"}}).Return(&orderPb.GetOrderResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment5, nil)
				mockRecurringPaymentActionsDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", false).Return(nil,
					epifierrors.ErrRecordNotFound)

				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: recurringPayment.GetFromActorId()}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type:     types.Actor_USER,
						EntityId: "entity-1",
					},
				}, nil)
				mockSavingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{PrimaryUserId: "entity-1"}}).Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{
					Id: "account-id-1",
				}}, nil)
				mockaccountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), &accountBalancePb.GetAccountBalanceRequest{
					Identifier: &accountBalancePb.GetAccountBalanceRequest_Id{Id: "account-id-1"},
					ActorId:    recurringPayment.GetFromActorId(),
				}).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &moneyPb.Money{
						CurrencyCode: money.RupeeCurrencyCode,
						Units:        1000,
						Nanos:        0,
					},
				}, nil)

				mockRecurringPaymentProcessor.EXPECT().FetchActorName(gomock.Any(), recurringPayment.GetFromActorId()).Return(&commontypes.Name{
					FirstName:  "a",
					MiddleName: "b",
					LastName:   "c",
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().FetchActorName(gomock.Any(), recurringPayment.GetToActorId()).Return(&commontypes.Name{
					FirstName:  "x",
					MiddleName: "y",
					LastName:   "z",
				}, nil)
				mockTimelineClient.EXPECT().Create(gomock.Any(), &timelinePb.CreateRequest{
					PrimaryActorId:       recurringPayment.GetToActorId(),
					SecondaryActorId:     recurringPayment.GetFromActorId(),
					PrimaryActorName:     "X Y Z",
					SecondaryActorName:   "A B C",
					PrimaryActorSource:   timelinePb.TimelineSource_ACCOUNT_NUMBER,
					SecondaryActorSource: timelinePb.TimelineSource_ACCOUNT_NUMBER,
					Ownership:            timelinePb.Ownership_EPIFI_TECH,
				}).Return(&timelinePb.CreateResponse{Status: rpc.StatusOk(), Timeline: &timelinePb.Timeline{
					PrimaryActorId:   recurringPayment.GetFromActorId(),
					SecondaryActorId: recurringPayment.GetToActorId(),
				}}, nil)
				mockTimelineClient.EXPECT().UpdateTimelineForActors(gomock.Any(), &timelinePb.UpdateTimelineForActorsRequest{
					PrimaryActorId:   recurringPayment.GetFromActorId(),
					SecondaryActorId: recurringPayment.GetToActorId(),
					SetVisibility:    true,
				}).Return(&timelinePb.UpdateTimelineForActorsResponse{Status: rpc.StatusOk()}, nil)
				mockOrderClient.EXPECT().CreateOrderWithTransaction(gomock.Any(), newCreateOrderWithTransactionReqMatcher(&orderPb.CreateOrderWithTransactionRequest{
					OrderParam: &orderPb.CreateOrderWithTransactionRequest_OrderCreationParams{
						ActorFrom:    recurringPayment.GetFromActorId(),
						ActorTo:      recurringPayment.GetToActorId(),
						Workflow:     orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT,
						OrderPayload: marshalledOrderPayload,
						Status:       orderPb.OrderStatus_CREATED,
						ClientReqId:  "client-request-id-1",
						Provenance:   orderPb.OrderProvenance_INTERNAL,
						Tags:         []orderPb.OrderTag{orderPb.OrderTag_STANDING_INSTRUCTION},
					},
					TransactionParam: &orderPb.CreateOrderWithTransactionRequest_TransactionCreationParams{
						PiFrom:          recurringPayment.GetPiFrom(),
						PiTo:            recurringPayment.GetPiTo(),
						PaymentProtocol: paymentPb.PaymentProtocol(dynamicConf.SIExecutionParams().PaymentProtocol()),
						Status:          paymentPb.TransactionStatus_CREATED,
						ReqInfo:         &paymentPb.PaymentRequestInformation{ReqId: ""},
						Ownership:       commontypes.Ownership_FEDERAL_BANK,
					},
					Amount:                    amount,
					DisableWorkflowProcessing: dynamicConf.FeatureFlags().EnableRecurringPaymentExecutionWithoutAuthViaCelestial(),
				})).Return(&orderPb.CreateOrderWithTransactionResponse{
					Status: rpc.StatusInternal(),
				}, nil)
				mockRecurringPaymentActionsDao.EXPECT().Create(gomock.Any(), newCreateActionReqMatcher(&pb.RecurringPaymentsAction{
					RecurringPaymentId: recurringPayment.GetId(),
					ClientRequestId:    "client-request-id-1",
					Action:             pb.Action_EXECUTE,
					State:              pb.ActionState_ACTION_CREATED,
				})).Return(&pb.RecurringPaymentsAction{
					Id:                 "action-1",
					RecurringPaymentId: recurringPayment.GetId(),
					ClientRequestId:    "client-request-id-1",
					Action:             pb.Action_EXECUTE,
				}, nil)
			},
			want: &pb.ExecuteRecurringPaymentResponse{
				Status: rpc.StatusUnknown(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.ExecuteRecurringPayment(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExecuteRecurringPayment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want.GetStatus().GetCode() == got.Status.Code {
				tt.want.Status = got.Status
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ExecuteRecurringPayment() got = %v, want %v", got, tt.want)
			}
		})
	}
}

type CreateOrderWithTransactionReqMatcher struct {
	want *orderPb.CreateOrderWithTransactionRequest
}

func newCreateOrderWithTransactionReqMatcher(want *orderPb.CreateOrderWithTransactionRequest) *CreateOrderWithTransactionReqMatcher {
	return &CreateOrderWithTransactionReqMatcher{
		want: want,
	}
}

func (ce *CreateOrderWithTransactionReqMatcher) Matches(x interface{}) bool {
	got, ok := x.(*orderPb.CreateOrderWithTransactionRequest)
	if !ok {
		return false
	}

	ce.want.TransactionParam.ReqInfo.ReqId = got.TransactionParam.ReqInfo.ReqId
	return reflect.DeepEqual(ce.want, got)
}

func (ce *CreateOrderWithTransactionReqMatcher) String() string {
	return fmt.Sprintf("want: %v", ce.want)
}
