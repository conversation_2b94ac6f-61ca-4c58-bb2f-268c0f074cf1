package mapping

import (
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	enachVgPb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach/enums"
)

var EnachMandateAmountTypeToRecurringPaymentAmountType = map[enachVgPb.AmountType]rpPb.AmountType{
	enachVgPb.AmountType_AMOUNT_TYPE_EXACT:   rpPb.AmountType_EXACT,
	enachVgPb.AmountType_AMOUNT_TYPE_MAXIMUM: rpPb.AmountType_MAXIMUM,
}

var EnachMandateFrequencyToRecurringPaymentFrequency = map[enachVgPb.Frequency]rpPb.AllowedFrequency{
	enachVgPb.Frequency_FREQUENCY_ONE_TIME:     rpPb.AllowedFrequency_ONE_TIME,
	enachVgPb.Frequency_FREQUENCY_DAILY:        rpPb.AllowedFrequency_DAILY,
	enachVgPb.Frequency_FREQUENCY_WEEKLY:       rpPb.AllowedFrequency_WEEKLY,
	enachVgPb.Frequency_FREQUENCY_FORTNIGHTLY:  rpPb.AllowedFrequency_FORTNIGHTLY,
	enachVgPb.Frequency_FREQUENCY_MONTHLY:      rpPb.AllowedFrequency_MONTHLY,
	enachVgPb.Frequency_FREQUENCY_BI_MONTHLY:   rpPb.AllowedFrequency_BI_MONTHLY,
	enachVgPb.Frequency_FREQUENCY_QUARTERLY:    rpPb.AllowedFrequency_QUARTERLY,
	enachVgPb.Frequency_FREQUENCY_HALF_YEARLY:  rpPb.AllowedFrequency_HALF_YEARLY,
	enachVgPb.Frequency_FREQUENCY_YEARLY:       rpPb.AllowedFrequency_YEARLY,
	enachVgPb.Frequency_FREQUENCY_AS_PRESENTED: rpPb.AllowedFrequency_AS_PRESENTED,
}
