package recurringpayment

import (
	"flag"
	"os"
	"testing"

	"gorm.io/gorm"

	"github.com/epifi/gamma/pay/config/server"
	"github.com/epifi/gamma/pkg/pay/pgauthkeys"
	rpServerConfig "github.com/epifi/gamma/recurringpayment/config/server"
	rpServerGenConf "github.com/epifi/gamma/recurringpayment/config/server/genconf"
	"github.com/epifi/gamma/recurringpayment/test"
)

var (
	db          *gorm.DB
	conf        *rpServerConfig.Config
	dynamicConf *rpServerGenConf.Config
)

// nolint:dogsled
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, conf, dynamicConf, db, teardown = test.InitTestServerV2()
	rpPgProgToAuthSecretMap := make(map[string]*server.PgProgramToAuthSecret)
	for k, v := range conf.PgProgramToAuthSecretMap {
		rpPgProgToAuthSecretMap[k] = &server.PgProgramToAuthSecret{
			AuthParam:      v.AuthParam,
			AuthParamValue: v.AuthParamValue,
		}
	}
	err := pgauthkeys.InitialisePgProgramToAuthParams(rpPgProgToAuthSecretMap)
	if err != nil {
		return
	}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
