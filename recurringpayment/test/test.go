package test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"log"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	testV2 "github.com/epifi/be-common/pkg/test/v2"
	rpServerConfig "github.com/epifi/gamma/recurringpayment/config/server"
	rpServerGenConf "github.com/epifi/gamma/recurringpayment/config/server/genconf"
	rpWorkerConfig "github.com/epifi/gamma/recurringpayment/config/worker"
)

var (
	conf       *rpServerConfig.Config
	workerConf *rpWorkerConfig.Config
)

// nolint: dupl
// InitTestServer initiates components needed for tests
// Will be invoked from TestMain but can be called from individual tests for *special* cases only
func InitTestServerV2() (string, *rpServerConfig.Config, *rpServerGenConf.Config, *gorm.DB, func()) {
	var err error
	// Init config
	conf, err = rpServerConfig.Load()
	if err != nil {
		log.Fatal("failed to load config", err)
	}

	// Setup logger
	logger.Init(conf.Application.Environment)

	dynamicConf, err := rpServerGenConf.Load()
	if err != nil {
		log.Fatal("failed to load dynamic conf", err)
	}
	// Init db connection
	db, dbName, err := testV2.NewRandomScopedCrdbWithConfig(conf.EpifiDb, conf.Tracing.Enable)
	if err != nil {
		log.Fatal("failed to connect to db", err)
	}
	storageV2.InitDefaultCRDBTransactionExecutor(db)

	err = testV2.DropTestDatabase(db, conf.EpifiDb.GetName())
	if err != nil {
		log.Fatal("failed to drop DB", err)
	}

	sqlDb, _ := db.DB()

	return dbName, conf, dynamicConf, db, func() {
		_ = logger.Log.Sync()
		_ = sqlDb.Close()
	}
}

// nolint: dupl
// InitTestWorker initiates components needed for tests
// Will be invoked from TestMain but can be called from individual tests for *special* cases only
func InitTestWorker(initDb bool) (*rpWorkerConfig.Config, *gorm.DB, func()) {
	var err error
	// Init config
	workerConf, err = rpWorkerConfig.Load()
	if err != nil {
		log.Fatal("failed to load config", err)
	}

	// Setup logger
	logger.Init(workerConf.Application.Environment)

	err = epifitemporal.InitWorkflowParams(workerConf.WorkflowParamsList.GetWorkflowParamsMap())
	if err != nil {
		logger.Panic("failed to load workflow params", zap.Error(err))
	}

	err = epifitemporal.InitDefaultActivityParams(workerConf.DefaultActivityParamsList.GetActivityParamsMap())
	if err != nil {
		logger.Panic("failed to load default activity params", zap.Error(err))
	}
	if !initDb {
		return workerConf, nil, func() {
			_ = logger.Log.Sync()
		}
	}

	// Init db connection
	dbConfigMap := workerConf.DbConfigMap.GetOwnershipToDbConfigMap()
	dbConnProvider, _, teardown, err := storageV2.NewCRDBResourceProvider(dbConfigMap, false)
	if err != nil {
		logger.Panic("failed to connect to db", zap.Error(err))
	}

	db, err := dbConnProvider.GetResourceForOwnership(commontypes.Ownership_EPIFI_TECH)
	if err != nil {
		logger.Panic("no db connection found for federal bank", zap.Error(err))
	}

	storageV2.InitDefaultCRDBTransactionExecutor(db)
	err = testV2.DropTestDatabase(db, dbConfigMap[commontypes.Ownership_EPIFI_TECH].GetName())
	if err != nil {
		logger.Panic("failed to drop DB", zap.Error(err))
	}

	return workerConf, nil, func() {
		_ = logger.Log.Sync()
		teardown()
	}
}

func InitTestServerForPGDB() (string, *rpServerConfig.Config, *rpServerGenConf.Config, *gorm.DB, func()) {
	var err error
	// Init config
	conf, err = rpServerConfig.Load()
	if err != nil {
		log.Fatal("failed to load config", err)
	}

	// Setup logger
	logger.Init(conf.Application.Name)
	dynamicConf, confGenErr := rpServerGenConf.Load()
	if confGenErr != nil {
		log.Fatal("failed to load config", err)
	}

	// Init db connection
	db, _, closeFn, err := testV2.PrepareRandomScopedRdsTestDb(conf.RecurringPaymentDb, false)
	if err != nil {
		log.Fatalf("error making connection to test rds database %v", err)
	}
	return "", conf, dynamicConf, db, func() {
		closeFn()
		_ = logger.Log.Sync()
	}
}
