package recurringpayment

import (
	"context"
	"errors"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	recurringPaymentPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

// GetRecurringPaymentById returns recurring payment for the given id
func (s *Service) GetRecurringPaymentById(ctx context.Context, req *recurringPaymentPb.GetRecurringPaymentByIdRequest) (*recurringPaymentPb.GetRecurringPaymentByIdResponse, error) {
	var (
		res = &recurringPaymentPb.GetRecurringPaymentByIdResponse{}
	)
	res.Status = &rpc.Status{}

	rp, err := s.recurringPaymentDao.GetById(ctx, req.GetId())
	if err != nil {
		logger.Error(ctx, "error fetching recurring payment",
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		if errors.Is(err, epifierrors.ErrRecordNotFound) || errors.Is(err, gorm.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
		}
		return res, nil
	}

	res.RecurringPayment = rp
	res.Status = rpc.StatusOk()
	return res, nil
}
