package recurringpayment

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"gorm.io/gorm"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	"github.com/epifi/gamma/pkg/recurringpayment"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	orderPb "github.com/epifi/gamma/api/order"
	pb "github.com/epifi/gamma/api/recurringpayment"
	payloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
)

// We are using the client as workflowPb.Client_RECURRING_PAYMENT across recurring payment workflows to prevent exposure of backend information on Client side
func (s *Service) AuthoriseExecute(ctx context.Context, req *pb.AuthoriseExecuteRequest) (*pb.AuthoriseExecuteResponse, error) {
	var (
		recurringPaymentId string
		recurringPayment   *pb.RecurringPayment
	)

	res := &pb.AuthoriseExecuteResponse{}
	clientReqId := req.GetClientId().GetId()
	if clientReqId == "" {
		clientReqId = req.GetClientRequestId()
	}

	if clientReqId == "" {
		logger.Error(ctx, "ClientRequestId can't be empty")
		res.Status = rpcPb.StatusInvalidArgument()
		return res, nil
	}

	orderRes, err := s.orderClient.GetOrder(ctx, &orderPb.GetOrderRequest{
		Identifier: &orderPb.GetOrderRequest_ClientReqId{
			ClientReqId: clientReqId,
		},
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error in getting orderWithTxn", zap.String(logger.CLIENT_REQUEST_ID, clientReqId), zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	case orderRes.GetStatus().IsRecordNotFound():
		logger.Error(ctx, "order not found", zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		res.Status = rpcPb.StatusRecordNotFound()
		return res, nil
	case !orderRes.GetStatus().IsSuccess():
		logger.Error(ctx, "non-success error code while fetching order", zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	rpPayload := &pb.RecurringPaymentExecutionInfo{}
	err = protojson.Unmarshal(orderRes.GetOrder().GetOrderPayload(), rpPayload)
	if err != nil {
		logger.Error(ctx, "failed to unmarshal order: %v", zap.String(logger.ORDER_ID, orderRes.GetOrder().GetId()), zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	recurringPaymentId = rpPayload.GetRecurringPaymentId()

	recurringPayment, err = s.recurringPaymentDao.GetById(ctx, recurringPaymentId)
	if err != nil {
		logger.Error(ctx, "error fetching recurring payment", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId), zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		if errors.Is(err, epifierrors.ErrRecordNotFound) || errors.Is(err, gorm.ErrRecordNotFound) {
			res.Status = rpcPb.StatusRecordNotFound()
		}
		return res, nil
	}

	executionAllowed, status := s.isExecutionAllowed(ctx, recurringPayment)
	if !executionAllowed {
		res.Status = status
		return res, nil
	}

	if s.featureFlags.EnableRecurringPaymentExecutionWithAuthViaCelestial() {
		// sending signal to workflow
		err = s.signalWorkflow(ctx, &celestialPb.ClientReqId{
			Id:     clientReqId,
			Client: workflowPb.Client_RECURRING_PAYMENT,
		}, recurringPayment)
		if err != nil {
			logger.Error(ctx, "error while signaling workflow for creation", zap.Error(err),
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()), zap.String(logger.CLIENT_REQUEST_ID,
					clientReqId))
			res.Status = rpcPb.StatusFromError(err)
			return res, nil
		}
	} else {
		err = s.initiateOrderProcessing(ctx, clientReqId)
		if err != nil {
			logger.Error(ctx, "error while triggering order processing for execution", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID,
				clientReqId))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}

	switch orderRes.GetOrder().GetWorkflow() {
	case orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_WITH_AUTH, orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH:
		status = s.handleMandateExecution(ctx, req, orderRes.GetOrder().GetId())
		if !status.IsSuccess() {
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	default:
		logger.Info(ctx, "unknown workflow ", zap.String(logger.WORKFLOW, orderRes.GetOrder().GetWorkflow().String()))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) handleMandateExecution(ctx context.Context, req *pb.AuthoriseExecuteRequest, orderId string) *rpcPb.Status {
	initiateMandateExecutionReq := &upiMandatePb.InitiateMandateExecutionRequest{
		OrderId: orderId,
		ActorId: req.GetCurrentActorId(),
	}
	if req.GetCredential() != nil {
		initiateMandateExecutionReq.AuthHeader = &upiMandatePb.InitiateMandateExecutionRequest_AuthHeader{
			Device:        req.GetCredential().GetMandateHeader().GetDevice(),
			NpciCredBlock: req.GetCredential().GetMandateHeader().GetNpciCredBlock(),
		}
	}

	initiateMandateRes, err := s.upiMandateClient.InitiateMandateExecution(ctx, initiateMandateExecutionReq)

	if initErr := epifigrpc.RPCError(initiateMandateRes, err); initErr != nil {
		logger.Error(ctx, "error in initiating one time collect", zap.Error(initErr))
		return rpcPb.StatusInternal()
	}
	logger.Debug(ctx, "one time collect initiated", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()))
	return rpcPb.StatusOk()
}

// isExecutionAllowed checks whether the recurring payment execution should proceed
func (s *Service) isExecutionAllowed(ctx context.Context, recurringPayment *pb.RecurringPayment) (bool, *rpcPb.Status) {

	switch {
	case recurringPayment.GetState() == pb.RecurringPaymentState_PAUSED:
		logger.Error(ctx, "execution not allowed as recurring payment in paused state", zap.String(logger.STATE, recurringPayment.GetState().String()))
		return false, rpcPb.StatusFailedPrecondition()
	case recurringPayment.GetState() == pb.RecurringPaymentState_TO_BE_PAUSED &&
		recurringPayment.GetPauseInterval() != nil && time.Since(recurringPayment.GetPauseInterval().GetStartTime().AsTime()) > 0 &&
		time.Since(recurringPayment.GetPauseInterval().GetEndTime().AsTime()) < 0:
		err := s.recurringPaymentDao.UpdateAndChangeStatus(ctx, recurringPayment, nil, recurringPayment.GetState(), pb.RecurringPaymentState_PAUSED)
		if err != nil {
			logger.Error(ctx, "error in updating recurring payment", zap.Error(err), zap.String(logger.STATE, recurringPayment.GetState().String()))
			return false, rpcPb.StatusInternal()
		}
		logger.Error(ctx, "execution not allowed as recurring payment in paused state", zap.String(logger.STATE, recurringPayment.GetState().String()))
		return false, rpcPb.StatusFailedPrecondition()
	}
	return true, rpcPb.StatusOk()
}

func (s *Service) signalWorkflow(ctx context.Context, clientId *celestialPb.ClientReqId, recurringPayment *pb.RecurringPayment) error {
	workflowReq, processorErr := s.celestialProcessor.GetWorkflowByClientRequestId(ctx, clientId.GetId(), clientId.GetClient())
	if processorErr != nil {
		return fmt.Errorf("error fetching workflow request for given client req ID %w", processorErr)
	}

	// workflow waits for signal only for 10 minutes after initiation, and will time out after that
	if time.Since(workflowReq.GetCreatedAt().AsTime()) > s.config.RecurringPaymentExecutionParams.AuthorisationTimeLimit {
		return fmt.Errorf("workflow can't be authorised after 10 minutes %w", rpcPb.StatusAsError(rpcPb.StatusPermissionDenied()))
	}

	// sending signal to workflow
	payload, marshalErr := protojson.Marshal(&payloadPb.ExecuteRecurringPaymentAuthSignal{})
	if marshalErr != nil {
		return fmt.Errorf("error in marshalling execute recurring payment auth signal payload %w", rpcPb.StatusAsError(rpcPb.StatusInternal()))
	}

	err := s.celestialProcessor.SignalWorkflow(ctx, clientId.GetId(), string(rpNs.ExecuteRecurringPaymentAuthSignal), clientId.GetClient(), payload, recurringPayment.GetEntityOwnership(), recurringpayment.OwnershipToUseCaseForRecPay[recurringPayment.GetEntityOwnership()])
	if err != nil {
		return fmt.Errorf("error while signaling workflow for creation %w", rpcPb.StatusAsError(rpcPb.StatusInternal()))
	}

	return nil
}
