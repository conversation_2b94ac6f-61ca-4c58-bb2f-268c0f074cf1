package recurringpayment

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/pkg/errors"

	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/gamma/api/order/payment"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	orderPb "github.com/epifi/gamma/api/order"
	pb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/recurringpayment/dao"
)

// nolint: funlen
func (s *Service) GetExecutionsForRecurringPayment(ctx context.Context, req *pb.GetExecutionsForRecurringPaymentRequest) (*pb.GetExecutionsForRecurringPaymentResponse, error) {
	var (
		res        = &pb.GetExecutionsForRecurringPaymentResponse{}
		executions []*pb.GetExecutionsForRecurringPaymentResponse_ExecutionInfo
		bgColor    string
	)

	clientReqIdToOrderMap := make(map[string]*orderPb.Order)
	clientReqIdToTransactionMap := make(map[string]*payment.Transaction)
	clientReqIdToPayeeActorIdMap := make(map[string]string)

	actions, _, err := s.getActionsForRecurringPayment(ctx, req)
	switch {
	case storagev2.IsRecordNotFoundError(err):
		logger.Error(ctx, "record not found for execute actions", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching execute actions",
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	case len(actions) == 0:
		logger.Error(ctx, "record not found for execute actions", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	default:
		logger.Info(ctx, "fetched actions successfully", zap.Int(logger.LENGTH, len(actions)),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
	}
	// TODO(team) : Add batch rpc to fetch orders with txns by client request Id's

	orderWithTransactionsReq := &orderPb.GetOrdersWithTransactionsRequest{}

	for i := range actions {
		orderIdentifier := &orderPb.OrderIdentifier{
			Identifier: &orderPb.OrderIdentifier_ClientReqId{ClientReqId: actions[i].GetClientRequestId()},
		}
		orderWithTransactionsReq.OrderIdentifiers = append(orderWithTransactionsReq.GetOrderIdentifiers(), orderIdentifier)
	}

	orderWithTransactions, rpcStatus := s.getOrdersWithTransactions(ctx, orderWithTransactionsReq)
	res.Status = rpcStatus
	switch {
	case rpcStatus.IsRecordNotFound():
		logger.Debug(ctx, "Record not found for orders with transactions.", zap.String(logger.RPC_STATUS, rpcStatus.String()))
		return res, nil
	case !rpcStatus.IsSuccess():
		logger.Error(ctx, "error while fetching orders with transactions.", zap.String(logger.RPC_STATUS, rpcStatus.String()))
		return res, nil
	}

	for i := range orderWithTransactions {
		clientReqIdToOrderMap[orderWithTransactions[i].GetOrder().GetClientReqId()] = orderWithTransactions[i].GetOrder()
		clientReqIdToTransactionMap[orderWithTransactions[i].GetOrder().GetClientReqId()] = orderWithTransactions[i].GetTransactions()[0]
		clientReqIdToPayeeActorIdMap[orderWithTransactions[i].GetOrder().GetClientReqId()] = orderWithTransactions[i].GetOrder().GetToActorId()
	}

	for i := range actions {
		clientReqId := actions[i].GetClientRequestId()
		// if order not exist for a given clientReqId, we cannot link the payment details with the corresponding recurring payment.
		// Thus, logging for now to evaluate.
		if _, exists := clientReqIdToOrderMap[clientReqId]; !exists {
			logger.WarnWithCtx(ctx, "order not found for clientReqId (recurring payment actions)", zap.String("clientReqId", clientReqId))
			continue
		}
		imageUrl, name, err := s.getNameAndImageUrl(ctx, clientReqIdToPayeeActorIdMap[clientReqId])
		if err != nil {
			logger.Error(ctx, "error in fetching image url and name", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		if imageUrl == "" {
			bgColor = actorPb.GetColourCodeForActor(clientReqIdToPayeeActorIdMap[clientReqId])
		}
		executions = append(executions, &pb.GetExecutionsForRecurringPaymentResponse_ExecutionInfo{
			Amount:               clientReqIdToOrderMap[actions[i].GetClientRequestId()].GetAmount(),
			ExecutedAt:           clientReqIdToTransactionMap[actions[i].GetClientRequestId()].GetExecutionTS(),
			ImageUrl:             imageUrl,
			PayeeName:            name.ToSentenceCaseString(),
			BgColor:              bgColor,
			OrderId:              clientReqIdToOrderMap[clientReqId].GetId(),
			CreatedAt:            actions[i].GetCreatedAt(),
			ExecutionActionState: actions[i].GetState(),
			RecurringPaymentTypeSpecificExecutionInfo: actions[i].GetActionMetadata().GetExecuteActionMetadate().GetRecurringPaymentTypeSpecificExecutionInfo(),
		})
	}
	res.Executions = executions
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) getNameAndImageUrl(ctx context.Context, actorId string) (string, *commontypes.Name, error) {
	getEntityDetailsRes, err := s.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{
		ActorId: actorId,
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error fetching entity details ", zap.String("actor-id", actorId),
			zap.Error(err))
		return "", nil, err
	case !getEntityDetailsRes.GetStatus().IsSuccess():
		logger.Error(ctx, "error fetching entity details, non-success ", zap.String(logger.ACTOR_ID, actorId),
			zap.Uint32(logger.STATUS_CODE, getEntityDetailsRes.GetStatus().GetCode()), zap.Error(err))
		return "", nil, fmt.Errorf("non success state while fetching entity details")
	default:
		return getEntityDetailsRes.GetProfileImageUrl(), getEntityDetailsRes.GetName(), nil
	}
}

// getActionsForRecurringPayment - fetched the actions for recurring payment and handles any specific needs/filters as per recurring payment type,
// for eg - we'll show all actions (failed , success) for dc mandates while we'll show only success actions for other type of mandates
func (s *Service) getActionsForRecurringPayment(ctx context.Context, req *pb.GetExecutionsForRecurringPaymentRequest) ([]*pb.RecurringPaymentsAction, pb.RecurringPaymentType, error) {
	recurringPyament, err := s.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	if err != nil {
		return nil, pb.RecurringPaymentType_RECURRING_PAYMENT_TYPE_UNSPECIFIED, errors.Wrap(err, "error while fetching recurring payment by id")
	}

	var (
		actions   []*pb.RecurringPaymentsAction
		actionErr error
	)

	actions, actionErr = s.recurringPaymentActionsDao.GetActionsByRecurringPaymentId(ctx,
		req.GetRecurringPaymentId(),
		req.GetStartTimestamp().AsTime(),
		req.GetPageSize(),
		req.GetOffset(),
		req.GetDescending(),
		dao.WithActionsFilter([]pb.Action{pb.Action_EXECUTE}),
		dao.WithActionStateFilter([]pb.ActionState{pb.ActionState_ACTION_SUCCESS, pb.ActionState_ACTION_FAILURE, pb.ActionState_ACTION_EXPIRED, pb.ActionState_ACTION_IN_PROGRESS, pb.ActionState_ACTION_REJECT}))
	return actions, recurringPyament.GetType(), actionErr
}
