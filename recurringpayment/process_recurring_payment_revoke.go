package recurringpayment

import (
	"context"
	"errors"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	domainPb "github.com/epifi/gamma/api/order/domain"
	pb "github.com/epifi/gamma/api/recurringpayment"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
)

// nolint: funlen
func (s *Service) ProcessRecurringPaymentRevoke(ctx context.Context, req *domainPb.ProcessFulfilmentRequest) (*domainPb.ProcessFulfilmentResponse, error) {
	var (
		stateToUpdate       pb.RecurringPaymentState
		actionStateToUpdate pb.ActionState
	)
	res := &domainPb.ProcessFulfilmentResponse{}
	responseHeader := &domainPb.DomainResponseHeader{}
	res.ResponseHeader = responseHeader
	recurringPaymentRevokeInfo := &pb.RecurringPaymentRevokeInfo{}
	unmarshalOptions := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	if unmarshalErr := unmarshalOptions.Unmarshal(req.GetPayload(), recurringPaymentRevokeInfo); unmarshalErr != nil {
		logger.Error(ctx, "failed to unmarshal payload for recurring payment revoke", zap.Error(unmarshalErr))
		responseHeader.Status = domainPb.DomainProcessingStatus_PERMANENT_FAILURE
		return res, nil
	}
	recurringPaymentId := recurringPaymentRevokeInfo.GetRecurringPaymentId()
	requestId := recurringPaymentRevokeInfo.GetRequestId()
	clientRequestId := recurringPaymentRevokeInfo.GetClientRequestId()
	recurringPayment, err := s.recurringPaymentDao.GetById(ctx, recurringPaymentId)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "recurring payment record not found", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
		responseHeader.Status = domainPb.DomainProcessingStatus_PERMANENT_FAILURE
		return res, nil
	case err != nil:
		logger.Error(ctx, "error while fetching recurring payment", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId),
			zap.Error(err))
		responseHeader.Status = domainPb.DomainProcessingStatus_TRANSIENT_FAILURE
		return res, nil
	default:
		logger.Debug(ctx, "recurring payment entry fetched successfully")
	}
	recurringPaymentAction, err := s.recurringPaymentActionsDao.GetByClientRequestId(ctx, clientRequestId, false)
	if err != nil {
		logger.Error(ctx, "error in fetching recurringPayment action", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId), zap.String(logger.CLIENT_REQUEST_ID, clientRequestId))
		responseHeader.Status = domainPb.GetStatusFrom(err)
		return res, nil
	}
	switch recurringPayment.GetState() {
	case pb.RecurringPaymentState_REVOKED:
		responseHeader.Status = domainPb.DomainProcessingStatus_SUCCESS
		return res, nil
	case pb.RecurringPaymentState_REVOKE_QUEUED:
		logger.Info(ctx, "recurring payment revoke not initiated by user")
		err = s.checkAuthorisationThreshold(ctx, recurringPayment, recurringPaymentAction,
			pb.RecurringPaymentState_ACTIVATED)
		if err != nil {
			logger.Error(ctx, "error in checking authorisation threshold", zap.Error(err),
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
			responseHeader.Status = domainPb.GetStatusFrom(err)
			return res, nil
		}
		responseHeader.Status = domainPb.DomainProcessingStatus_NO_OP
		return res, nil
	case pb.RecurringPaymentState_ACTIVATED:
		responseHeader.Status = domainPb.DomainProcessingStatus_PERMANENT_FAILURE
		return res, nil
	case pb.RecurringPaymentState_REVOKE_INITIATED, pb.RecurringPaymentState_REVOKE_AUTHORISED:
		err = s.getActionStatusFromDomainService(ctx, recurringPayment, requestId, siPb.RequestType_REVOKE)
		switch {
		case errors.Is(err, errActionFailed):
			actionStateToUpdate = pb.ActionState_ACTION_FAILURE
			stateToUpdate = pb.RecurringPaymentState_ACTIVATED
			responseHeader.Status = domainPb.DomainProcessingStatus_PERMANENT_FAILURE
		case err != nil:
			logger.Error(ctx, "error in fetching status from domain service",
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId), zap.Error(err))
			responseHeader.Status = domainPb.GetStatusFrom(err)
			return res, nil
		default:
			actionStateToUpdate = pb.ActionState_ACTION_SUCCESS
			stateToUpdate = pb.RecurringPaymentState_REVOKED
			responseHeader.Status = domainPb.DomainProcessingStatus_SUCCESS
		}
		err = s.updateRecurringPaymentAndActionInTxnBlock(ctx, recurringPaymentAction, recurringPayment,
			nil, nil, stateToUpdate, actionStateToUpdate)
		if err != nil {
			logger.Error(ctx, "error in updating recurring payment and action", zap.Error(err),
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
			responseHeader.Status = domainPb.GetStatusFrom(err)
			return res, nil
		}
		return res, nil
	default:
		responseHeader.Status = domainPb.DomainProcessingStatus_PERMANENT_FAILURE
	}
	return res, nil
}
