package recurringpayment

import (
	"context"
	"errors"
	"fmt"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"gorm.io/gorm"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpcPb "github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"

	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	pb "github.com/epifi/gamma/api/recurringpayment"
	payloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
	"github.com/epifi/gamma/pkg/pay"
	"github.com/epifi/gamma/pkg/recurringpayment"
	"github.com/epifi/gamma/recurringpayment/internal"
)

var (
	roleToInitiatedBy = map[pb.ActorRole]pb.InitiatedBy{
		pb.ActorRole_ACTOR_ROLE_PAYER: pb.InitiatedBy_PAYER,
		pb.ActorRole_ACTOR_ROLE_PAYEE: pb.InitiatedBy_PAYEE,
	}
)

// CreateRevokeAttempt - creates revoke attempt for an existing recurring payment flow
// We are using the client as workflowPb.Client_RECURRING_PAYMENT across recurring payment workflows to prevent exposure of backend information on Client side
// nolint: funlen
func (s *Service) CreateRevokeAttempt(ctx context.Context, req *pb.CreateRevokeAttemptRequest) (*pb.CreateRevokeAttemptResponse, error) {
	var (
		res            = &pb.CreateRevokeAttemptResponse{}
		transactionId  string
		currentActorId string
	)
	clientReqId := req.GetClientId().GetId()
	if clientReqId == "" {
		clientReqId = req.GetClientRequestId()
	}
	err := s.checkPendingActions(ctx, req.GetRecurringPaymentId(), clientReqId, pb.Action_REVOKE)
	switch {
	case errors.Is(err, failedPreconditionError):
		logger.Info(ctx, "pending revoke requests exists..fast failing revoke attempt",
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		res.Status = rpcPb.StatusFailedPrecondition()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error while fetching revoke actions", zap.String(logger.RECURRING_PAYMENT_ID,
			req.GetRecurringPaymentId()), zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	default:
		logger.Debug(ctx, "continuing revoke request")
	}

	order, status := s.getOrderByClientRequestId(ctx, clientReqId)
	switch {
	case status.IsSuccess():
		res.OrderId = order.GetId()
		res.Status = rpcPb.StatusAlreadyExists()
		return res, nil
	case status.IsRecordNotFound():
		logger.Debug(ctx, "order record not found, new recurring payment revoke requested",
			zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
	default:
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	recurringPayment, err := s.recurringPaymentProcessor.GetRecurringPaymentById(ctx, req.GetRecurringPaymentId())
	if err != nil {
		logger.Error(ctx, "couldn't fetch recurring payment for given recurring payment id", zap.Error(err),
			zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		res.Status = rpcPb.StatusFromError(err)
		return res, nil
	}

	err = s.validatePendingExecutions(ctx, recurringPayment)
	if err != nil {
		logger.Error(ctx, "failed due to validation of pending Execution of Recurring PaymentId:", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	currentActorId, req.CurrentActorRole = getCurrentActorIdAndRole(req.GetCurrentActorId(), req.GetCurrentActorRole(), recurringPayment)
	getCredBlockRes, err := s.recurringPaymentProcessor.GetCredBlockTypeV2(&internal.GetCredBlockTypeV2Request{
		RecurringPaymentType: recurringPayment.GetType(),
		ActorRole:            req.GetCurrentActorRole(),
		InitiatedBy:          req.GetInitiatedBy(),
		Action:               pb.Action_REVOKE,
	})
	if err != nil {
		logger.Error(ctx, "error in fetching cred block type", zap.Error(err))
		res.Status = rpcPb.StatusInvalidArgument()
		return res, nil
	}
	credBlockType := getCredBlockRes.CredBlockType
	isAuthRequired := getCredBlockRes.IsAuthRequired
	if req.GetTransactionId() == "" {
		transactionId, err = generateRecurringPaymentRevokeTransactionId(recurringPayment.GetType(), recurringPayment.GetPartnerBank())
		if err != nil {
			logger.Error(ctx, "error while generating transaction id for recurring payment revoke", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	} else {
		transactionId = req.GetTransactionId()
	}

	action, err := s.recurringPaymentActionsDao.GetByClientRequestId(ctx, clientReqId, false)
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrRecordNotFound):
		txnErr := s.updateRecurringPaymentDetailsInTxnBlockRevoke(ctx, req, clientReqId, recurringPayment, currentActorId, transactionId)
		if txnErr != nil {
			logger.Error(ctx, "failed to commit transaction for recurring payment revoke",
				zap.String(logger.CLIENT_REQUEST_ID, clientReqId), zap.Error(txnErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	case err != nil:
		logger.Error(ctx, "error in fetching revoke action", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	default:
		transactionId = action.GetVendorRequestId()
	}

	err = s.revokeDomainServiceEntity(ctx, recurringPayment, transactionId, req.GetPayload(),
		req.GetInitiatedBy(), req.GetCurrentActorRole())
	if err != nil {
		logger.Error(ctx, "error in revoking domain entity", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	if s.featureFlags.EnableRecurringPaymentRevokeViaCelestial() {
		err = s.initiateRevokeWorkflowViaCelestial(ctx, recurringPayment, &workflowPb.ClientReqId{
			Id:     clientReqId,
			Client: workflowPb.Client_RECURRING_PAYMENT,
		}, currentActorId, transactionId, !isAuthRequired)
		if err != nil {
			logger.Error(ctx, "error while initiating celestial workflow", zap.Error(err),
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
			status := rpcPb.StatusFromError(err)
			res.Status = status
			return res, nil
		}
	} else {
		order, err = s.createOrderForRevoke(ctx, recurringPayment, clientReqId, transactionId, req.GetProvenance())
		if err != nil {
			logger.Error(ctx, "error in creating order for recurring payment revoke", zap.Error(err),
				zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}

	if recurringPayment.GetState() == pb.RecurringPaymentState_REVOKE_AUTHORISED {
		err = s.initiateRevokeWorkflowOrchestration(ctx, &celestialPb.ClientReqId{
			Id:     clientReqId,
			Client: workflowPb.Client_RECURRING_PAYMENT,
		}, recurringPayment)
		if err != nil {
			logger.Error(ctx, "error while initiating orchestration", zap.Error(err),
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
			status := rpcPb.StatusFromError(err)
			res.Status = status
			return res, nil
		}
	}
	if isAuthRequired {
		transactionAttributes, err := s.getTransactionAttributes(ctx, recurringPayment, nil, transactionId, money.ZeroINR().GetPb())
		if err != nil {
			logger.Error(ctx, "error in fetching transaction attributes", zap.Error(err), zap.String(logger.REQUEST_ID, transactionId))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		res.TransactionAttributes = transactionAttributes
	}

	s.publishRecurringPaymentActionEvent(ctx, action, recurringPayment)

	res.IsAuthenticationRequired = isAuthRequired
	res.CredBlockType = credBlockType
	res.TxnId = transactionId
	res.OrderId = order.GetId()
	res.Status = rpcPb.StatusOk()
	return res, nil
}

// nolint: dupl
// initiateRevokeWorkflowOrchestration - initiates orchestration for recurring payment revoke flow
func (s *Service) initiateRevokeWorkflowOrchestration(ctx context.Context, clientId *celestialPb.ClientReqId, recurringPayment *pb.RecurringPayment) error {
	if s.featureFlags.EnableRecurringPaymentRevokeViaCelestial() {
		// sending signal to workflow
		payload, marshalErr := protojson.Marshal(&payloadPb.RevokeRecurringPaymentAuthSignal{})
		if marshalErr != nil {
			return fmt.Errorf("error in marshalling revoke recurring payment auth signal payload %w %s", rpcPb.StatusAsError(rpcPb.StatusInternal()), clientId.GetId())
		}
		err := s.celestialProcessor.SignalWorkflow(ctx, clientId.GetId(),
			string(rpNs.RevokeRecurringPaymentAuthSignal), clientId.GetClient(), payload, recurringPayment.GetEntityOwnership(), recurringpayment.OwnershipToUseCaseForRecPay[recurringPayment.GetEntityOwnership()])
		if err != nil {
			return fmt.Errorf("error while signaling workflow %w %s", rpcPb.StatusAsError(rpcPb.StatusInternal()),
				clientId.GetId())
		}
	} else {
		err := s.initiateOrderProcessing(ctx, clientId.GetId())
		if err != nil {
			return fmt.Errorf("error while triggering order processing for revoke %w %s", rpcPb.StatusAsError(rpcPb.StatusInternal()),
				clientId.GetId())
		}
	}
	return nil
}

// nolint: dupl
// initiateRevokeWorkflowViaCelestial - initiates revoke workflow for recurring payment via celestial based entities
func (s *Service) initiateRevokeWorkflowViaCelestial(ctx context.Context, recurringPayment *pb.RecurringPayment, clientId *workflowPb.ClientReqId, currentActorId string, reqId string, bypassAuth bool) error {
	var rpPayload []byte
	rpPayload, err := protojson.Marshal(&pb.RecurringPaymentRevokeInfo{
		RecurringPaymentId: recurringPayment.GetId(),
		ClientRequestId:    clientId.GetId(),
		RequestId:          reqId,
		BypassAuth:         bypassAuth,
	})
	if err != nil {
		return fmt.Errorf("error while marshalling recurring payment revoke info %w", rpcPb.StatusAsError(rpcPb.StatusInternal()))
	}
	err = s.celestialProcessor.InitiateWorkflowV2(ctx, clientId, currentActorId, rpPayload,
		celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_REVOKE_RECURRING_PAYMENT), workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT)
	if err != nil {
		return fmt.Errorf("error while initiating workflow %w %s", rpcPb.StatusAsError(rpcPb.StatusInternal()),
			recurringPayment.GetId())
	}
	return nil
}

// updateRecurringPaymentDetailsInTxnBlockRevoke - updates recurring payment details in txn block for revoke action
func (s *Service) updateRecurringPaymentDetailsInTxnBlockRevoke(ctx context.Context, req *pb.CreateRevokeAttemptRequest, clientReqId string,
	recurringPayment *pb.RecurringPayment, currentActorId string, txnId string) error {
	return storagev2.RunCRDBTxn(ctx, func(txnCtx context.Context) error {
		if req.GetInitiatedBy() == pb.InitiatedBy_INITIATED_BY_UNSPECIFIED {
			req.InitiatedBy = roleToInitiatedBy[req.GetCurrentActorRole()]
		}
		initiatedBy := lo.Ternary(recurringPayment.GetType() == pb.RecurringPaymentType_UPI_MANDATES, req.GetInitiatedBy(), recurringPayment.GetInitiatedBy())
		recurringPaymentStatus, err := getRecurringPaymentRequestState(initiatedBy,
			recurringPayment.GetFromActorId(), currentActorId, pb.Action_REVOKE)
		if err != nil {
			return fmt.Errorf("error fetching recurring payment action :%w", err)
		}
		err = s.recurringPaymentDao.UpdateAndChangeStatus(txnCtx, recurringPayment,
			nil, recurringPayment.GetState(), recurringPaymentStatus)
		if err != nil {
			return fmt.Errorf("error while updating recurring payment state for recurring payment revoke %w", err)
		}
		_, err = s.recurringPaymentActionsDao.Create(txnCtx, &pb.RecurringPaymentsAction{
			RecurringPaymentId: recurringPayment.GetId(),
			ClientRequestId:    req.GetClientRequestId(),
			Action:             pb.Action_REVOKE,
			State:              pb.ActionState_ACTION_CREATED,
			VendorRequestId:    txnId,
			ExpireAt:           req.GetExpiry(),
			Remarks:            req.GetRemarks(),
		})
		if err != nil {
			return fmt.Errorf("error in creating revoke action %w", err)
		}
		return nil
	})
}

// revokeDomainServiceEntity - initiates revoke with domain service entities
//
//	nolint:dupl
func (s *Service) revokeDomainServiceEntity(
	ctx context.Context,
	recurringPayment *pb.RecurringPayment,
	txnId string,
	payload []byte,
	reqInitiatedBy pb.InitiatedBy,
	currentActorRole pb.ActorRole,
) error {
	switch recurringPayment.GetType() {
	case pb.RecurringPaymentType_STANDING_INSTRUCTION:
		res, err := s.siClient.CreateRevokeAttempt(ctx, &siPb.CreateRevokeAttemptRequest{
			RecurringPaymentId: recurringPayment.GetId(),
			TransactionId:      txnId,
		})
		if te := epifigrpc.RPCError(res, err); te != nil {
			if res != nil && res.GetStatus().IsAlreadyExists() {
				return nil
			}
			return fmt.Errorf("error in revoking standing instruction %w", te)
		}
		return nil
	case pb.RecurringPaymentType_UPI_MANDATES:
		var (
			requestPayload = &upiMandatePb.Payload{}
			err            error
		)
		if payload == nil {
			requestPayload, err = s.createMandatePayload(ctx, recurringPayment)
			if err != nil {
				return fmt.Errorf("error in create mandate payload :%w", err)
			}
		} else {
			err = protojson.Unmarshal(payload, requestPayload)
			if err != nil {
				return fmt.Errorf("error unmarshalling payload :%w", err)
			}
		}
		res, err := s.upiMandateClient.RevokeMandate(ctx, &upiMandatePb.RevokeMandateRequest{
			RecurringPaymentId:    recurringPayment.GetId(),
			ReqId:                 txnId,
			MandateRequestPayload: requestPayload,
			CurrentActorRole:      recurringPaymentToMandateActorRole[currentActorRole],
			PartnerBank:           recurringPayment.GetPartnerBank(),
			InitiatedBy:           recurringPaymentToMandateInitiateByMap[reqInitiatedBy],
		})
		if te := epifigrpc.RPCError(res, err); te != nil {
			if res != nil && res.GetStatus().IsAlreadyExists() {
				return nil
			}
			return fmt.Errorf("error in revoking recurring payment %w", te)
		}
		return nil
	default:
		return fmt.Errorf("unsupported recurring payment %s", recurringPayment.GetType().String())
	}
}

// createOrderForRevoke - creates order entity for revoke recurring payment flow (in case of oms based flow)
// nolint: dupl
func (s *Service) createOrderForRevoke(ctx context.Context, recurringPayment *pb.RecurringPayment, clientRequestId, txnId string, provenance pb.RecurrencePaymentProvenance) (*orderPb.Order, error) {
	orderPayload, err := protojson.Marshal(&pb.RecurringPaymentRevokeInfo{
		RecurringPaymentId: recurringPayment.GetId(),
		RequestId:          txnId,
		ClientRequestId:    clientRequestId,
		InitiatedBy:        recurringPayment.GetInitiatedBy(),
	})
	if err != nil {
		return nil, fmt.Errorf("error while marshalling recurring payment revoke info %w", err)
	}

	orderProvenance, ok := recurringPaymentProvenanceToOrderProvenanceMap[provenance]
	if !ok {
		return nil, fmt.Errorf("failed to fetch order provenance")
	}

	createOrderRes, err := s.orderClient.CreateOrder(ctx, &orderPb.CreateOrderRequest{
		ActorFrom:    recurringPayment.GetFromActorId(),
		ActorTo:      recurringPayment.GetToActorId(),
		Workflow:     orderPb.OrderWorkflow_REVOKE_RECURRING_PAYMENT,
		Provenance:   orderProvenance,
		OrderPayload: orderPayload,
		Amount:       recurringPayment.GetAmount(),
		Status:       orderPb.OrderStatus_CREATED,
		ClientReqId:  clientRequestId,
	})
	if te := epifigrpc.RPCError(createOrderRes, err); te != nil {
		return nil, fmt.Errorf("error while creating order %w", te)
	}
	return createOrderRes.GetOrder(), nil
}

// generateRecurringPaymentRevokeTransactionId - generates transaction id for revoke recurring payment request
func generateRecurringPaymentRevokeTransactionId(recurringPaymentType pb.RecurringPaymentType, partnerBank commonvgpb.Vendor) (string, error) {
	switch recurringPaymentType {
	case pb.RecurringPaymentType_STANDING_INSTRUCTION:
		switch partnerBank {
		case commonvgpb.Vendor_FEDERAL_BANK:
			return idgen.FederalRandomDigitsSequence(pay.FederalRevokeStandingInstructionPrefix, 5), nil
		default:
			return "", fmt.Errorf("vendor not supported for standing instruction creation %s", partnerBank.String())
		}
	case pb.RecurringPaymentType_UPI_MANDATES:
		switch partnerBank {
		case commonvgpb.Vendor_FEDERAL_BANK:
			return pay.GenerateVendorRequestId(partnerBank, paymentPb.PaymentProtocol_UPI)
		default:
			return "", fmt.Errorf("vendor not supported for standing instruction creation %s", partnerBank.String())
		}
	default:
		return "", fmt.Errorf("recurring payment type not supported %s", recurringPaymentType.String())
	}
}
