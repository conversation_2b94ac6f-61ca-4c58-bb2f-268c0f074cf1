package recurringpayment

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/thoas/go-funk"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpcPb "github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	actorPb "github.com/epifi/gamma/api/actor"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	pb "github.com/epifi/gamma/api/recurringpayment"
	payloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	savingsPb "github.com/epifi/gamma/api/savings"
	timelinePb "github.com/epifi/gamma/api/timeline"
	types "github.com/epifi/gamma/api/typesv2"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
	"github.com/epifi/gamma/pkg/pay"
	"github.com/epifi/gamma/recurringpayment/dao"
	"github.com/epifi/gamma/recurringpayment/internal"
)

var (
	// recurring payment states in which payment transfer can be done
	executionStates = []pb.RecurringPaymentState{pb.RecurringPaymentState_ACTIVATED, pb.RecurringPaymentState_MODIFY_QUEUED,
		pb.RecurringPaymentState_MODIFY_INITIATED, pb.RecurringPaymentState_MODIFY_AUTHORISED,
		pb.RecurringPaymentState_PAUSE_QUEUED, pb.RecurringPaymentState_PAUSE_INITIATED,
		pb.RecurringPaymentState_PAUSE_AUTHORISED, pb.RecurringPaymentState_REVOKE_QUEUED,
		pb.RecurringPaymentState_REVOKE_INITIATED, pb.RecurringPaymentState_REVOKE_AUTHORISED,
		pb.RecurringPaymentState_TO_BE_PAUSED}
	recurringPaymentTagToOrderTagMap = map[pb.RecurringPaymentTag]orderPb.OrderTag{
		pb.RecurringPaymentTag_RECURRING_PAYMENT_TAG_UNSPECIFIED: orderPb.OrderTag_ORDER_TAGS_UNSPECIFIED,
		pb.RecurringPaymentTag_MUTUAL_FUND:                       orderPb.OrderTag_MUTUAL_FUND,
		pb.RecurringPaymentTag_FIT_AUTO_PAY:                      orderPb.OrderTag_FIT,
		pb.RecurringPaymentTag_LOANS_ES_AUTO_PAY:                 orderPb.OrderTag_LOAN_EARLY_SALARY,
		pb.RecurringPaymentTag_US_STOCKS:                         orderPb.OrderTag_US_STOCKS,
	}
)

// nolint: funlen
// We are using the client as workflowPb.Client_RECURRING_PAYMENT across recurring payment workflows to prevent exposure of backend information on Client side
func (s *Service) ExecuteRecurringPayment(ctx context.Context, req *pb.ExecuteRecurringPaymentRequest) (*pb.ExecuteRecurringPaymentResponse, error) {
	var (
		res            = &pb.ExecuteRecurringPaymentResponse{}
		order          *orderPb.Order
		txn            *paymentPb.Transaction
		actionMetaDeta *pb.ActionMetadata
	)

	orderWorkflow := orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT
	orderStatus := orderPb.OrderStatus_CREATED
	clientReqId := req.GetClientId().GetId()
	if clientReqId == "" {
		clientReqId = req.GetClientRequestId()
	}

	getOrderRes, err := s.orderClient.GetOrder(ctx, &orderPb.GetOrderRequest{
		Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: clientReqId},
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error while fetching order for client request id",
			zap.String(logger.CLIENT_REQUEST_ID, clientReqId), zap.Error(err))
		res.Status = rpcPb.StatusInternal()
	case getOrderRes.GetStatus().IsSuccess():
		logger.Info(ctx, "fetched order successfully for client request id",
			zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		res.OrderId = getOrderRes.GetOrder().GetId()
		res.Status = rpcPb.StatusAlreadyExists()
		return res, nil
	case getOrderRes.GetStatus().IsRecordNotFound():
		logger.Debug(ctx, "order record not found, new recurring payment execution requested",
			zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
	default:
		logger.Error(ctx, "non success status while fetching order", zap.Uint32(logger.STATUS_CODE, getOrderRes.GetStatus().GetCode()))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	recurringPayment, err := s.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "recurring payment not found", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpcPb.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error while fetching recurring payment", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	default:
		logger.Debug(ctx, "recurring payment fetched successfully")
	}

	recurringPayment, err = s.checkAndUpdateRecurringPaymentState(ctx, recurringPayment)
	if err != nil {
		logger.Error(ctx, "error in checkAndUpdateRecurringPaymentState()", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID,
			req.GetRecurringPaymentId()))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if recurringPayment.GetState() == pb.RecurringPaymentState_COMPLETED {
		logger.Info(ctx, "recurring payment in completed state", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpcPb.StatusFailedPrecondition()
		return res, nil
	}

	if recurringPayment.GetType() == pb.RecurringPaymentType_UPI_MANDATES {

		isAuthRequired, authErr := s.upiProcessor.IsAuthRequiredForMandateExecution(ctx, req.GetUpiMandateExecutionInfo(), recurringPayment.GetRecurrenceRule().GetAllowedFrequency(),
			req.GetAmount(), recurringPayment.GetCreatedAt())

		if authErr != nil {
			logger.Error(ctx, "error in checking if auth is required for mandate execution", zap.Error(authErr), zap.String(logger.RECURRING_PAYMENT_ID,
				req.GetRecurringPaymentId()))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		if isAuthRequired {
			orderWorkflow = orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_WITH_AUTH
			orderStatus = orderPb.OrderStatus_COLLECT_REGISTERED
		} else {
			orderWorkflow = orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH
		}
	}
	if req.GetOrderStatus() != orderPb.OrderStatus_ORDER_STATUS_UNSPECIFIED {
		orderStatus = req.GetOrderStatus()
	}

	requestId, err := generateRecurringPaymentExecuteTransactionId(recurringPayment.GetType(), recurringPayment.GetPartnerBank())
	if err != nil {
		logger.Error(ctx, "error in generating request id for recurring payment execution", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	action, err := s.recurringPaymentActionsDao.GetByClientRequestId(ctx, clientReqId, false)
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrRecordNotFound):
		_, status := s.validateExecutionRequest(ctx, recurringPayment, req.GetAmount(), req.GetUpiMandateExecutionInfo())
		if !status.IsSuccess() {
			logger.Error(ctx, "error in validating execution request", zap.String(logger.CLIENT_REQUEST_ID, clientReqId),
				zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.String(logger.STATUS_CODE, status.String()))
			// not creating the order if we didn't find the recurring payment action, as it is not needed
			// if createFailureOrder {
			// 	order, err = s.createOrderForExecutionValidationFailure(ctx, req.GetAmount(), recurringPayment, req.GetClientRequestId(), orderWorkflow, req.GetTags())
			// 	if err != nil {
			// 		logger.Error(ctx, "error in creating order for validation failure", zap.Error(err))
			// 		res.Status = rpcPb.StatusInternal()
			// 		return res, nil
			// 	}
			// 	res.OrderId = order.GetId()
			// }
			// res.OrderId = order.GetId()
			res.OrderWithTxn = &orderPb.OrderWithTransactions{}
			res.Status = status
			return res, nil
		}

		if recurringPayment.GetType() == pb.RecurringPaymentType_UPI_MANDATES {
			actionMetaDeta = &pb.ActionMetadata{
				ExecuteActionMetadate: &pb.ExecuteActionMetaData{SeqNum: req.GetUpiMandateExecutionInfo().GetSeqNum()}}
		}
		action, err = s.createRecurringPaymentAction(ctx, recurringPayment.GetId(), clientReqId, requestId,
			pb.Action_EXECUTE, actionMetaDeta, pb.ActionState_ACTION_CREATED, "", req.GetExpiry(), nil, nil)
		if err != nil {
			logger.Error(ctx, "error in creating execute action",
				zap.String(logger.CLIENT_REQUEST_ID, clientReqId), zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	case err != nil:
		logger.Error(ctx, "error in fetching execution action", zap.String(logger.CLIENT_REQUEST_ID,
			clientReqId), zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()), zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	default:
		requestId = action.GetVendorRequestId()
	}
	err = s.createTimeline(ctx, recurringPayment)
	if err != nil {
		logger.Error(ctx, "error in creating recurring payment execution timeline",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()), zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	order, txn, err = s.createOrderAndTransactionForExecution(ctx, req.GetAmount(), recurringPayment, clientReqId,
		requestId, req.GetUpiMandateExecutionInfo(), orderWorkflow, orderStatus, req.GetTags())
	// NOTE :  In case of timeouts or other ambiguous errors, we don't know
	// if order and transaction were created
	// We'll return unknown status code in such cases.
	if err != nil {
		logger.Error(ctx, "error in creating order and transaction for recurring payment execution",
			zap.String(logger.CLIENT_REQUEST_ID, clientReqId), zap.Error(err))
		res.Status = rpcPb.StatusUnknown()
		return res, nil
	}

	// only when workflow is handled by celestial
	err = s.initiateCelestialWorkflowIfRequired(ctx, req, recurringPayment, orderWorkflow)
	if err != nil {
		logger.Error(ctx, "error while initiating celestial workflow", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		status := rpcPb.StatusFromError(err)
		res.Status = status
	}

	err = s.checkAndTriggerExecutionWorkflowProcessing(ctx, orderWorkflow, recurringPayment, clientReqId)
	if err != nil {
		logger.Error(ctx, "failed to trigger execution order processing", zap.String(logger.CLIENT_REQUEST_ID,
			clientReqId), zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	logger.Info(ctx, "recurring payment execution triggered successfully", zap.String(logger.RECURRING_PAYMENT_ID,
		req.GetRecurringPaymentId()), zap.String(logger.CLIENT_REQUEST_ID, clientReqId), zap.String(logger.REQUEST_ID, requestId))

	s.publishRecurringPaymentActionEvent(ctx, action, recurringPayment)

	res.OrderId = order.GetId()
	res.OrderWithTxn = &orderPb.OrderWithTransactions{
		Order: order,
		Transactions: []*paymentPb.Transaction{
			txn,
		},
	}
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) createOrderAndTransactionForExecution(
	ctx context.Context,
	amount *moneyPb.Money,
	recurringPayment *pb.RecurringPayment,
	clientReqId, requestId string,
	upiMandateExecuteInfo *pb.UpiMandateExecuteInfo,
	orderWorkflow orderPb.OrderWorkflow,
	orderStatus orderPb.OrderStatus,
	tags []pb.RecurringPaymentTag,
) (*orderPb.Order, *paymentPb.Transaction, error) {
	var (
		reqInfo                   = &paymentPb.PaymentRequestInformation{}
		piFrom                    string
		paymentProtocol           paymentPb.PaymentProtocol
		disableWorkflowProcessing bool
	)

	orderPayload, err := protojson.Marshal(&pb.RecurringPaymentExecutionInfo{
		RecurringPaymentId: recurringPayment.GetId(),
		Amount:             amount,
	})
	if err != nil {
		return nil, nil, fmt.Errorf("error while marshalling recurring payment execution info %w", err)
	}
	switch recurringPayment.GetType() {
	case pb.RecurringPaymentType_STANDING_INSTRUCTION:
		reqInfo.ReqId = requestId
		piFrom = recurringPayment.GetPiFrom()
		paymentProtocol = paymentPb.PaymentProtocol(s.siExecutionParamsConf.PaymentProtocol())
		disableWorkflowProcessing = s.featureFlags.EnableRecurringPaymentExecutionWithoutAuthViaCelestial()
	case pb.RecurringPaymentType_UPI_MANDATES:
		reqInfo = upiMandateExecuteInfo.GetPaymentRequestInfo()
		payerVpa := reqInfo.GetUpiInfo().GetPayerVpa()
		piFrom, err = s.getPiIdForVpa(ctx, payerVpa)
		if err != nil {
			return nil, nil, fmt.Errorf("error fetching pi for vpa: %w", err)
		}
		paymentProtocol = recurringPayment.GetPreferredPaymentProtocol()
		disableWorkflowProcessing = s.featureFlags.EnableRecurringPaymentExecutionWithAuthViaCelestial()
	default:
		return nil, nil, fmt.Errorf("invalid recyrrung payment type %s", recurringPayment.GetType().String())
	}

	createOrderWithTxnRes, err := s.orderClient.CreateOrderWithTransaction(ctx, &orderPb.CreateOrderWithTransactionRequest{
		OrderParam: &orderPb.CreateOrderWithTransactionRequest_OrderCreationParams{
			ActorFrom:    recurringPayment.GetFromActorId(),
			ActorTo:      recurringPayment.GetToActorId(),
			Workflow:     orderWorkflow,
			OrderPayload: orderPayload,
			Status:       orderStatus,
			ClientReqId:  clientReqId,
			Provenance:   orderPb.OrderProvenance_INTERNAL,
			Tags:         getOrderTags(ctx, tags, recurringPayment.GetType()),
		},
		TransactionParam: &orderPb.CreateOrderWithTransactionRequest_TransactionCreationParams{
			PiFrom:          piFrom,
			PiTo:            recurringPayment.GetPiTo(),
			PaymentProtocol: paymentProtocol,
			Status:          paymentPb.TransactionStatus_CREATED,
			ReqInfo:         reqInfo,
			Utr:             upiMandateExecuteInfo.GetUtr(),
			Remarks:         upiMandateExecuteInfo.GetNote(),
			Ownership:       commontypes.Ownership_FEDERAL_BANK,
		},
		Amount:                    amount,
		DisableWorkflowProcessing: disableWorkflowProcessing,
	})
	if te := epifigrpc.RPCError(createOrderWithTxnRes, err); te != nil {
		return nil, nil, fmt.Errorf("error while creating order with txn for recurring payment execution %w", te)
	}
	return createOrderWithTxnRes.GetOrder(), createOrderWithTxnRes.GetTransaction(), nil
}

// validateExecutionRequest will validate the following
//  1. State of recurring payment : Recurring payment state should one of execution states
//  2. Balance : Current balance should be greater than amount to be transferred
//  3. No of transactions : We will check if transactions executed successfully or in progress is less than
//     the maximum count of transactions over the frequency period
//  4. Amount transferred till now : We will check if amount transferred successfully or in progress is less than
//     the maximum count of transactions over the frequency period. In case of exact amount type we will only check for
//     transaction count.
//
// TODO (team) : chain of responsibility can be used here to make it more extensible. Open/Close principle.
// https://monorail-prod-321008.el.r.appspot.com/p/fi-app/issues/detail?id=15062
// We will return flag to specify if a failure order needs to be created in case of permanent failure. If the flag is false then
// the same request can be retried
// nolint: funlen
func (s *Service) validateExecutionRequest(ctx context.Context, recurringPayment *pb.RecurringPayment, amount *moneyPb.Money,
	upiMandateExecutionInfo *pb.UpiMandateExecuteInfo) (bool, *rpcPb.Status) {
	if !funk.Contains(executionStates, recurringPayment.GetState()) {
		logger.Error(ctx, "invalid recurring payment state", zap.String(logger.STATE, recurringPayment.GetState().String()),
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		switch recurringPayment.GetState() {
		case pb.RecurringPaymentState_PAUSED:
			return true, rpcPb.NewStatusWithoutDebug(uint32(pb.ExecuteRecurringPaymentResponse_MANDATE_PAUSED),
				"mandate is paused")
		case pb.RecurringPaymentState_COMPLETED:
			return true, rpcPb.NewStatusWithoutDebug(uint32(pb.ExecuteRecurringPaymentResponse_MANDATE_EXPIRED),
				"mandate is completed")
		case pb.RecurringPaymentState_REVOKED:
			return true, rpcPb.NewStatusWithoutDebug(uint32(pb.ExecuteRecurringPaymentResponse_MANDATE_REVOKED),
				"mandate is revoked")
		default:
			return true, rpcPb.StatusFailedPreconditionWithDebugMsg(fmt.Sprintf("invalid recurring payment state %s", recurringPayment.GetState().String()))
		}
	}

	if recurringPayment.GetType() == pb.RecurringPaymentType_UPI_MANDATES {
		status := s.validateExecuteMandateRequest(ctx, recurringPayment, upiMandateExecutionInfo)
		if !status.IsSuccess() {
			return true, status
		}
	}

	switch recurringPayment.GetAmountType() {
	case pb.AmountType_MAXIMUM:
		if money.Compare(recurringPayment.GetAmount(), amount) == -1 {
			logger.Error(ctx, "amount to be transferred greater than the maximum limit", zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
			return true, rpcPb.NewStatusWithoutDebug(uint32(pb.ExecuteRecurringPaymentResponse_MAXIMUM_AMOUNT_LIMIT_BREACHED),
				"amount to be transferred greater than the maximum limit")
		}
	case pb.AmountType_EXACT:
		if money.Compare(recurringPayment.GetAmount(), amount) != 0 {
			logger.Error(ctx, "amount not equal to the transferable amount", zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
			return true, rpcPb.NewStatusWithoutDebug(uint32(pb.ExecuteRecurringPaymentResponse_MAXIMUM_AMOUNT_LIMIT_BREACHED),
				"amount not equal to the transferable amount")
		}
	default:
		return true, rpcPb.StatusInvalidArgumentWithDebugMsg(fmt.Sprintf("invalid amount type %s", recurringPayment.GetAmountType().String()))
	}

	// do not validate SA balance for UPI mandates scenarios
	if recurringPayment.GetType() != pb.RecurringPaymentType_UPI_MANDATES {
		createFailureOrder, status := s.validateAccountBalance(ctx, recurringPayment.GetFromActorId(), amount)
		if status != nil {
			logger.Error(ctx, "failed to validate balance for user", zap.String(logger.STATUS_CODE, status.String()))
			return createFailureOrder, status
		}
	}

	// we need not check for no of transactions and maximum amount for AS_PRESENTED or ONE_TIME frequency
	if recurringPayment.GetRecurrenceRule().GetAllowedFrequency() == pb.AllowedFrequency_AS_PRESENTED ||
		recurringPayment.GetRecurrenceRule().GetAllowedFrequency() == pb.AllowedFrequency_ONE_TIME {
		return false, rpcPb.StatusOk()
	}

	amountAlreadyTransferred, transactionCount, err := s.getAmountAndTransactionCount(ctx, recurringPayment)
	if err != nil {
		logger.Error(ctx, "error in fetching amount and transactions",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()), zap.Error(err))
		return true, rpcPb.StatusInternalWithDebugMsg("error in fetching amount and transactions")
	}
	// We need not check for total amount sum in case of Exact amount type
	if recurringPayment.GetAmountType() == pb.AmountType_MAXIMUM {
		// amount already transferred + amount to be transferred greater than the maximum amount limit
		amountToBeTransferred, err := money.Sum(amountAlreadyTransferred, amount)
		if err != nil {
			logger.Error(ctx, "error in money.Sum()", zap.Error(err))
			return true, rpcPb.StatusInternalWithDebugMsg("error in money.Sum()")
		}
		if money.Compare(amountToBeTransferred, recurringPayment.GetAmount()) == 1 {
			logger.Error(ctx, "amount to be transferred greater than the maximum limit", zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
			return true, rpcPb.NewStatusWithoutDebug(uint32(pb.ExecuteRecurringPaymentResponse_MAXIMUM_AMOUNT_LIMIT_BREACHED),
				"amount to be transferred greater than the maximum limit")
		}
	}
	if int32(transactionCount+1) > recurringPayment.GetMaximumAllowedTxns() {
		logger.Error(ctx, "transaction count greater than the maximum limit", zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		return true, rpcPb.NewStatusWithoutDebug(uint32(pb.ExecuteRecurringPaymentResponse_MAXIMUM_TRANSACTION_COUNT_BREACHED),
			"transaction count greater than the maximum limit")
	}
	return false, rpcPb.StatusOk()
}

// validateAccountBalance checks the current account balance of the user if the user is internal
// and validate it with the amount to be transferred. In case if user is external we will not validate the account balance
// In case of failure we will return a flag to denote if we want to create a failure order or not. In case of failure in fetching
// balance we will allow the client to retry with the same request as the failures are intermittent in those cases
func (s *Service) validateAccountBalance(ctx context.Context, actorId string,
	amountToBeTransferred *moneyPb.Money) (bool, *rpcPb.Status) {
	getActorRes, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{
		Id: actorId,
	})
	if te := epifigrpc.RPCError(getActorRes, err); te != nil {
		logger.Error(ctx, "error in fetching actor by id", zap.Error(te))
		return true, rpcPb.StatusInternal()
	}
	if getActorRes.GetActor().GetType() != types.Actor_USER {
		return false, nil
	}
	entityId := getActorRes.GetActor().GetEntityId()
	savingAccountId, err := internal.GetSavingsAccountId(ctx, entityId, s.savingsClient)
	if err != nil {
		logger.Error(ctx, "error while fetching savings account id", zap.Error(err))
		return true, rpcPb.StatusInternal()
	}
	currentBalance, err := s.getSavingsAccountBalance(ctx, savingAccountId, actorId)
	if err != nil {
		if errors.Is(err, epifierrors.ErrPermissionDenied) {
			return false, rpcPb.NewStatus(uint32(pb.ExecuteRecurringPaymentResponse_ACCOUNT_NO_LEVEL_ACCESS), "no account level access to user", "")
		}
		logger.Error(ctx, "error in fetching current balance", zap.Error(err))
		return false, rpcPb.StatusInternal()
	}
	if money.Compare(currentBalance, amountToBeTransferred) == -1 {
		return true, rpcPb.NewStatusWithoutDebug(uint32(pb.ExecuteRecurringPaymentResponse_INSUFFICIENT_BALANCE),
			"account balance less that amount to be transferred")
	}
	return false, nil
}

// getSavingsAccountBalance gets the savings account available balance
func (s *Service) getSavingsAccountBalance(ctx context.Context, savingsAccountId, actorId string) (*moneyPb.Money, error) {
	res, err := s.paySavingsBalanceClient.GetAccountBalance(ctx, &accountBalancePb.GetAccountBalanceRequest{
		Identifier: &accountBalancePb.GetAccountBalanceRequest_Id{Id: savingsAccountId},
		ActorId:    actorId,
	})
	switch {
	case err != nil:
		return nil, fmt.Errorf("account.balance.GetAccountBalance failed to fetch savings account balance : %w", err)
	case res.GetStatus().GetCode() == uint32(savingsPb.GetAccountBalanceResponse_ACCOUNT_NO_LEVEL_ACCESS):
		return nil, fmt.Errorf("user has no access level for account: %w", epifierrors.ErrPermissionDenied)
	case !res.GetStatus().IsSuccess():
		return nil, fmt.Errorf("account.balance.GetAccountBalance returned unsuccessful status response : %v", res.GetStatus())
	default:
		return res.GetAvailableBalance(), nil
	}
}

// getAmountAndTransactionCount fetches the amount and count of transactions during the allowed frequency range
func (s *Service) getAmountAndTransactionCount(ctx context.Context, recurringPayment *pb.RecurringPayment) (*moneyPb.Money, int, error) {
	var (
		fromTime time.Time
		amount   = money.ZeroINR().GetPb()
		count    int
	)
	switch recurringPayment.GetRecurrenceRule().GetAllowedFrequency() {
	case pb.AllowedFrequency_DAILY:
		fromTime = time.Now().Add(-24 * time.Hour)
	case pb.AllowedFrequency_WEEKLY:
		fromTime = time.Now().Add(-7 * 24 * time.Hour)
	case pb.AllowedFrequency_FORTNIGHTLY:
		fromTime = time.Now().Add(-14 * 24 * time.Hour)
	case pb.AllowedFrequency_MONTHLY:
		fromTime = time.Now().Add(-30 * 24 * time.Hour)
	case pb.AllowedFrequency_BI_MONTHLY:
		fromTime = time.Now().Add(-60 * 24 * time.Hour)
	case pb.AllowedFrequency_QUARTERLY:
		fromTime = time.Now().Add(-90 * 24 * time.Hour)
	case pb.AllowedFrequency_HALF_YEARLY:
		fromTime = time.Now().Add(-180 * 24 * time.Hour)
	case pb.AllowedFrequency_YEARLY:
		fromTime = time.Now().Add(-360 * 24 * time.Hour)
	default:
		return nil, 0, fmt.Errorf("invalid allowed frequency %s", recurringPayment.GetRecurrenceRule().GetAllowedFrequency().String())
	}
	actions, err := s.recurringPaymentActionsDao.GetByRecurringPaymentId(ctx, recurringPayment.GetId(),
		dao.WithActionsFilter([]pb.Action{pb.Action_EXECUTE}))
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return money.ZeroINR().GetPb(), 0, nil
	case err != nil:
		return nil, 0, fmt.Errorf("error while fetching actions %w", err)
	default:
		logger.Debug(ctx, "execute actions fetched successfully")
	}

	// TODO(team) : Add rpc for fetching orders by client request ids with state and time filters

	orderWithTransactionsReq := &orderPb.GetOrdersWithTransactionsRequest{}

	for i := range actions {
		if actions[i].GetState() == pb.ActionState_ACTION_FAILURE {
			continue
		}
		orderIdentifier := &orderPb.OrderIdentifier{
			Identifier: &orderPb.OrderIdentifier_ClientReqId{ClientReqId: actions[i].GetClientRequestId()},
		}
		orderWithTransactionsReq.OrderIdentifiers = append(orderWithTransactionsReq.GetOrderIdentifiers(), orderIdentifier)
	}

	orderWithTransactions, rpcStatus := s.getOrdersWithTransactions(ctx, orderWithTransactionsReq)
	switch {
	case rpcStatus.IsRecordNotFound():
		logger.Debug(ctx, "Record not found for orders with transactions.", zap.String(logger.RPC_STATUS, rpcStatus.String()))
		return nil, 0, nil
	case !rpcStatus.IsSuccess():
		return nil, 0, fmt.Errorf("no orders found or error while fetching orders with transactions. rpcStatus: %v",
			rpcStatus.String())
	}

	for i := range orderWithTransactions {
		txn := orderWithTransactions[i].GetTransactions()[0]
		if !(txn.GetStatus() == paymentPb.TransactionStatus_SUCCESS || txn.GetStatus() == paymentPb.TransactionStatus_IN_PROGRESS ||
			txn.GetStatus() == paymentPb.TransactionStatus_INITIATED || txn.GetStatus() == paymentPb.TransactionStatus_CREATED) {
			continue
		}
		if txn.GetExecutionTS().AsTime().After(fromTime) {
			amount, err = money.Sum(amount, txn.GetAmount())
			if err != nil {
				return nil, 0, fmt.Errorf("error in money.Sum() %w", err)
			}
			count++
		}
	}
	return amount, count, nil
}

func (s *Service) createOrderForExecutionValidationFailure(ctx context.Context, amount *moneyPb.Money,
	recurringPayment *pb.RecurringPayment, clientReqId string, orderWorkFlow orderPb.OrderWorkflow, tags []pb.RecurringPaymentTag) (*orderPb.Order, error) {
	orderPayload, err := protojson.Marshal(&pb.RecurringPaymentExecutionInfo{
		RecurringPaymentId: recurringPayment.GetId(),
		Amount:             amount,
	})
	if err != nil {
		return nil, fmt.Errorf("error while marshalling recurring payment execution info %w", err)
	}
	createOrderRes, err := s.orderClient.CreateOrder(ctx, &orderPb.CreateOrderRequest{
		ActorFrom:    recurringPayment.GetFromActorId(),
		ActorTo:      recurringPayment.GetToActorId(),
		Workflow:     orderWorkFlow,
		OrderPayload: orderPayload,
		Amount:       amount,
		Status:       orderPb.OrderStatus_PAYMENT_FAILED,
		Provenance:   orderPb.OrderProvenance_INTERNAL,
		ClientReqId:  clientReqId,
		Tags:         getOrderTags(ctx, tags, recurringPayment.GetType()),
	})
	// TODO (Shubham) : Create a workflow request with failed status here
	if te := epifigrpc.RPCError(createOrderRes, err); te != nil {
		return nil, fmt.Errorf("error in creating order %w", te)
	}
	return createOrderRes.GetOrder(), nil
}

func (s *Service) validateExecuteMandateRequest(ctx context.Context, recurringPayment *pb.RecurringPayment,
	upiMandateExecutionInfo *pb.UpiMandateExecuteInfo) *rpcPb.Status {

	// for mandate umn is the payer vpa
	mandateRes, err := s.upiMandateClient.GetMandate(ctx, &upiMandatePb.GetMandateRequest{
		Identifier: &upiMandatePb.GetMandateRequest_RecurringPaymentId{RecurringPaymentId: recurringPayment.GetId()},
	})
	if rpcErr := epifigrpc.RPCError(mandateRes, err); rpcErr != nil {
		logger.Error(ctx, "error fetching mandate",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()), zap.Error(rpcErr))
		return rpcPb.StatusFromError(rpcErr)
	}

	toPiRes, err := s.piClient.GetPi(ctx, &piPb.GetPiRequest{
		Type: piPb.PaymentInstrumentType_UPI,
		Identifier: &piPb.GetPiRequest_UpiRequestParams_{
			UpiRequestParams: &piPb.GetPiRequest_UpiRequestParams{
				Vpa: upiMandateExecutionInfo.GetPaymentRequestInfo().GetUpiInfo().GetPayeeVpa(),
			},
		},
	})
	if te := epifigrpc.RPCError(toPiRes, err); te != nil {
		logger.Error(ctx, "error in getting pi from payer vpa for mandate", zap.Error(err))
		return rpcPb.StatusInternal()
	}
	if mandateRes.GetMandate().GetUmn() != upiMandateExecutionInfo.GetPaymentRequestInfo().GetUpiInfo().GetPayerVpa() ||
		recurringPayment.GetPiTo() != toPiRes.GetPaymentInstrument().GetId() {
		logger.Error(ctx, "invalid pi received in the request", zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		return rpcPb.NewStatusWithoutDebug(uint32(pb.ExecuteRecurringPaymentResponse_INVALID_PI),
			"invalid pi in payment request info")
	}

	return s.validateSeqNumber(ctx, recurringPayment.GetId(), upiMandateExecutionInfo.GetSeqNum())
}

func generateRecurringPaymentExecuteTransactionId(recurringPaymentType pb.RecurringPaymentType,
	partnerBank commonvgpb.Vendor) (string, error) {
	switch recurringPaymentType {
	case pb.RecurringPaymentType_STANDING_INSTRUCTION:
		switch partnerBank {
		case commonvgpb.Vendor_FEDERAL_BANK:
			return idgen.FederalRandomDigitsSequence(pay.FederalExecuteStandingInstructionPrefix, 5), nil
		default:
			return "", fmt.Errorf("vendor not supported for standing instruction execute %s", partnerBank.String())
		}
	case pb.RecurringPaymentType_UPI_MANDATES:
		// for recurring payment requestId should pe same passed in UPI Execution payload.
		return "", nil
	default:
		return "", fmt.Errorf("recurring payment type not supported %s", recurringPaymentType.String())
	}
}

// createTimeline creates the timeline for the actors involved in the recurring payment
// if the second actor belongs to epifi and share to payee flag is set to true, also sets
// the visibility of the timeline to true for second actor
func (s *Service) createTimeline(ctx context.Context, recurringPayment *pb.RecurringPayment) error {
	var (
		primaryActorId   string
		secondaryActorId string
		source           timelinePb.TimelineSource
	)
	timelineOwnership, ok := recurringPaymentOwnershipToTimelineOwnershipMap[recurringPayment.GetOwnership()]
	if !ok {
		return fmt.Errorf("invalid recurring payment ownership %s", recurringPayment.GetOwnership().String())
	}
	if recurringPayment.GetInitiatedBy() == pb.InitiatedBy_PAYER {
		primaryActorId = recurringPayment.GetFromActorId()
		secondaryActorId = recurringPayment.GetToActorId()
	} else {
		primaryActorId = recurringPayment.GetToActorId()
		secondaryActorId = recurringPayment.GetFromActorId()
	}
	switch recurringPayment.GetType() {
	case pb.RecurringPaymentType_STANDING_INSTRUCTION:
		source = timelinePb.TimelineSource_ACCOUNT_NUMBER
	case pb.RecurringPaymentType_UPI_MANDATES:
		source = timelinePb.TimelineSource_VPA
	default:
		return fmt.Errorf("invalid recurring payment type %s", recurringPayment.GetType())
	}
	primaryActorName, err := s.recurringPaymentProcessor.FetchActorName(ctx, primaryActorId)
	if err != nil {
		return fmt.Errorf("failed to fetch actor name %w", err)
	}

	secondaryActorName, err := s.recurringPaymentProcessor.FetchActorName(ctx, secondaryActorId)
	if err != nil {
		return fmt.Errorf("failed to fetch actor name %w", err)
	}
	createTimelineRes, err := s.timelineClient.Create(ctx, &timelinePb.CreateRequest{
		PrimaryActorId:       primaryActorId,
		SecondaryActorId:     secondaryActorId,
		PrimaryActorName:     primaryActorName.ToSentenceCaseString(),
		SecondaryActorName:   secondaryActorName.ToSentenceCaseString(),
		PrimaryActorSource:   source,
		SecondaryActorSource: source,
		Ownership:            timelineOwnership,
	})
	if te := epifigrpc.RPCError(createTimelineRes, err); te != nil {
		return fmt.Errorf("error in creating timeline %w", te)
	}

	if createTimelineRes.GetTimeline().IsVisibleToSecondaryActor() {
		return nil
	}

	// if the time primary actor is not same as the current primary actor id
	// we need to update the visibility of the timeline, no need to check if the second actor
	// is internal or not
	if createTimelineRes.GetTimeline().GetPrimaryActorId() == primaryActorId {
		if !recurringPayment.GetShareToPayee() {
			return nil
		}
		actorRes, actorResErr := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: secondaryActorId})
		if rpcErr := epifigrpc.RPCError(actorRes, actorResErr); rpcErr != nil {
			return fmt.Errorf("error fetching actor by actor id :%s :%w", secondaryActorId, rpcErr)
		}
		if actorRes.GetActor().GetType() != types.Actor_USER {
			return nil
		}
	}

	updateTimelineRes, err := s.timelineClient.UpdateTimelineForActors(ctx, &timelinePb.UpdateTimelineForActorsRequest{
		PrimaryActorId:   createTimelineRes.GetTimeline().GetPrimaryActorId(),
		SecondaryActorId: createTimelineRes.GetTimeline().GetSecondaryActorId(),
		SetVisibility:    true,
	})
	if rpcErr := epifigrpc.RPCError(updateTimelineRes, err); rpcErr != nil {
		return fmt.Errorf("error updating timeline visiblilty :%w", rpcErr)
	}
	return nil
}

// getPiIdForVpa returns pi id for the given vpa
func (s *Service) getPiIdForVpa(ctx context.Context, vpa string) (string, error) {
	piRes, err := s.piClient.GetPi(ctx, &piPb.GetPiRequest{
		Identifier: &piPb.GetPiRequest_UpiRequestParams_{
			UpiRequestParams: &piPb.GetPiRequest_UpiRequestParams{Vpa: vpa},
		},
		Type: piPb.PaymentInstrumentType_UPI,
	})
	if rpcErr := epifigrpc.RPCError(piRes, err); rpcErr != nil {
		if piRes.GetStatus().IsRecordNotFound() {
			return "", fmt.Errorf("no pi for vpa :%s :%v :%w", vpa, rpcErr, epifierrors.ErrPermanent)
		}
		return "", fmt.Errorf("error fetching pi for vpa :%s :%v :%w", vpa, rpcErr, epifierrors.ErrTransient)
	}
	return piRes.GetPaymentInstrument().GetId(), nil
}

func (s *Service) validateSeqNumber(ctx context.Context, recurringPaymentId string, seqNum int32) *rpcPb.Status {

	actions, err := s.recurringPaymentActionsDao.GetByRecurringPaymentId(ctx, recurringPaymentId,
		dao.WithActionsFilter([]pb.Action{pb.Action_EXECUTE}), dao.WithOrderByCreatedAt(true),
		dao.WithActionStateFilter([]pb.ActionState{pb.ActionState_ACTION_SUCCESS}))
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) || errors.Is(err, gorm.ErrRecordNotFound) {

			// if the current sequence number is 1, then there will be no records found
			// so we should return ok status
			if seqNum == 1 {
				return rpcPb.StatusOk()
			}
			logger.Error(ctx, "invalid sequence number passed",
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId), zap.Int(logger.SEQ_NUM, int(seqNum)), zap.Error(err))
			return rpcPb.NewStatusWithoutDebug(uint32(pb.ExecuteRecurringPaymentResponse_INVALID_SEQ_NUM),
				"invalid sequence number")
		}

		logger.Error(ctx, "error in getting recurring payment actions",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId), zap.Int(logger.SEQ_NUM, int(seqNum)), zap.Error(err))
		return rpcPb.StatusInternal()
	}

	if seqNum <= (actions[0].GetActionMetadata().GetExecuteActionMetadate().GetSeqNum()) {
		logger.Error(ctx, "mandate is already honoured",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId), zap.Int(logger.SEQ_NUM, int(seqNum)))
		return rpcPb.NewStatusWithoutDebug(uint32(pb.ExecuteRecurringPaymentResponse_MANDATE_ALREADY_HONOURED),
			"mandate is already honoured")
	}

	// check if current seq num is next to the previous sequence, that is it should be 1 greater than the
	// previous sequence number
	if seqNum > (actions[0].GetActionMetadata().GetExecuteActionMetadate().GetSeqNum() + 1) {
		logger.Error(ctx, "invalid sequence number passed",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId), zap.Int(logger.SEQ_NUM, int(seqNum)))
		return rpcPb.NewStatusWithoutDebug(uint32(pb.ExecuteRecurringPaymentResponse_INVALID_SEQ_NUM),
			"invalid sequence number")
	}
	return rpcPb.StatusOk()
}

// checkAndTriggerExecutionWorkflowProcessing checks if order workflow is COLLECT_RECURRING_PAYMENT_NO_AUTH and triggers
// processing only if payee is external
func (s *Service) checkAndTriggerExecutionWorkflowProcessing(ctx context.Context, orderWorkflow orderPb.OrderWorkflow,
	recurringPayment *pb.RecurringPayment, clientRequestId string) error {
	if orderWorkflow != orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH {
		return nil
	}
	isPayeeInternal, err := s.isActorInternal(ctx, recurringPayment.GetFromActorId())
	if err != nil {
		return fmt.Errorf("error checking if payee is internal %w", err)
	}
	if !isPayeeInternal {
		err = s.initiateOrderProcessing(ctx, clientRequestId)
		if err != nil {
			return fmt.Errorf("error while triggering order processing for execution %w", err)
		}
		logger.Info(ctx, "triggering order processing for execution successfully",
			zap.String(logger.CLIENT_REQUEST_ID, clientRequestId))
	}
	return nil
}

// getOrderTags return order tags corresponding to the recurring payment tags
// In case if an order tag does not exist corresponding to a recurring payment tag we will just log the error and continue
func getOrderTags(ctx context.Context, tags []pb.RecurringPaymentTag, recurringPaymentType pb.RecurringPaymentType) []orderPb.OrderTag {
	var (
		orderTags []orderPb.OrderTag
	)
	for _, tag := range tags {
		orderTag, ok := recurringPaymentTagToOrderTagMap[tag]
		if !ok {
			logger.Error(ctx, "order tag does not exist for recurring payment tag", zap.String(logger.RECURRING_PAYMENT_TAG, tag.String()))
			continue
		}
		orderTags = append(orderTags, orderTag)
	}
	switch recurringPaymentType {
	case pb.RecurringPaymentType_STANDING_INSTRUCTION:
		orderTags = append(orderTags, orderPb.OrderTag_STANDING_INSTRUCTION)
	case pb.RecurringPaymentType_UPI_MANDATES:
		orderTags = append(orderTags, orderPb.OrderTag_UPI_MANDATES)
	default:
		logger.Error(ctx, fmt.Sprintf("invalid recurring payment type %s", recurringPaymentType.String()))
	}
	return orderTags
}

func (s *Service) initiateCelestialWorkflowIfRequired(ctx context.Context, req *pb.ExecuteRecurringPaymentRequest, recurringPayment *pb.RecurringPayment, workflow orderPb.OrderWorkflow) error {
	clientReqId := req.GetClientRequestId()
	if clientReqId == "" {
		clientReqId = req.GetClientId().GetId()
	}
	switch workflow {
	case orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT, orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH:
		if s.featureFlags.EnableRecurringPaymentExecutionWithoutAuthViaCelestial() {
			if err := s.initiateWorkflow(ctx, recurringPayment, &workflowPb.ClientReqId{
				Id:     clientReqId,
				Client: workflowPb.Client_RECURRING_PAYMENT,
			}, &payloadPb.ExecuteRecurringPaymentWithoutAuth{},
				workflowPb.Type_EXECUTE_RECURRING_PAYMENT_WITHOUT_AUTH, celestialPb.QoS_GUARANTEED); err != nil {
				return err
			}
		}
	case orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_WITH_AUTH:
		if s.featureFlags.EnableRecurringPaymentExecutionWithAuthViaCelestial() {
			if err := s.initiateWorkflow(ctx, recurringPayment, &workflowPb.ClientReqId{
				Id:     clientReqId,
				Client: workflowPb.Client_RECURRING_PAYMENT,
			}, &payloadPb.ExecuteRecurringPaymentWithAuth{},
				workflowPb.Type_EXECUTE_RECURRING_PAYMENT_WITH_AUTH, celestialPb.QoS_GUARANTEED); err != nil {
				return err
			}
		}
	default:
		return fmt.Errorf("unknown order workflow found %s", workflow)
	}

	return nil
}

func (s *Service) initiateWorkflow(ctx context.Context, recurringPayment *pb.RecurringPayment, clientId *workflowPb.ClientReqId,
	payloadMessage proto.Message, wfType workflowPb.Type, qos celestialPb.QoS) error {
	payload, err := protojson.Marshal(payloadMessage)
	if err != nil {
		return fmt.Errorf("error while marshalling payload for the workflow %v %w", err, rpcPb.StatusAsError(rpcPb.StatusInternal()))
	}
	err = s.celestialProcessor.InitiateWorkflowV2(ctx, clientId, recurringPayment.GetFromActorId(), payload,
		celestialPkg.GetTypeEnumFromProtoEnum(wfType), workflowPb.Version_V0, qos)
	if err != nil {
		return fmt.Errorf("error while initiating workflow %v %w", err, rpcPb.StatusAsError(rpcPb.StatusInternal()))
	}
	return nil
}
