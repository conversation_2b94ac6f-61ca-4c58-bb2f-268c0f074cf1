// nolint: goimports
package recurringpayment

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	testPkg "github.com/epifi/be-common/pkg/test"

	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"

	actorPb "github.com/epifi/gamma/api/actor"
	payloadPb "github.com/epifi/gamma/api/recurringpayment/payload"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/money"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	orderPb "github.com/epifi/gamma/api/order"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	accountPiMocks "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	pb "github.com/epifi/gamma/api/recurringpayment"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	"github.com/epifi/gamma/api/recurringpayment/standinginstruction/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
	mocks3 "github.com/epifi/gamma/api/upi/mandate/mocks"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	internalMocks "github.com/epifi/gamma/recurringpayment/internal/mocks"
)

type modifyMandateReqMatcher struct {
	want *upiMandatePb.ModifyMandateRequest
}

func newModifyMandateReqMatcher(want *upiMandatePb.ModifyMandateRequest) *modifyMandateReqMatcher {
	return &modifyMandateReqMatcher{
		want: want,
	}
}

func (ce *modifyMandateReqMatcher) Matches(x interface{}) bool {
	got, ok := x.(*upiMandatePb.ModifyMandateRequest)
	if !ok {
		return false
	}

	if (ce.want.MandateRequestPayload != nil) != (got.MandateRequestPayload != nil) {
		return false
	}
	if got.MandateRequestPayload == nil {
		return true
	}
	ce.want.MandateRequestPayload.MandateOriginTimestamp = got.MandateRequestPayload.MandateOriginTimestamp
	return reflect.DeepEqual(ce.want, got)
}

func (ce *modifyMandateReqMatcher) String() string {
	return fmt.Sprintf("want: %v", ce.want)
}

func TestService_CreateModifyAttempt(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockOrderClient := orderMocks.NewMockOrderServiceClient(ctr)
	mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
	mockRecurringPaymentsActionDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
	mockSIClient := mocks.NewMockStandingInstructionServiceClient(ctr)
	mockRecurringPaymentProcessor := internalMocks.NewMockRecurringPaymentProcessor(ctr)
	mockActor := actorMocks.NewMockActorClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockMandateClient := mocks3.NewMockMandateServiceClient(ctr)
	mockAccountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
	mockCelestialProcessor := internalMocks.NewMockCelestialProcessor(ctr)

	svc := NewService(mockRecurringPaymentDao, mockOrderClient, mockSIClient, mockRecurringPaymentsActionDao, nil, mockActor, nil, nil, nil, conf, mockRecurringPaymentProcessor, mockMandateClient, mockAccountPiClient, mockPiClient, nil, nil, nil, nil, dynamicConf.FeatureFlags(), mockCelestialProcessor, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	startDate := timestampPb.New(time.Now())
	endDate := timestampPb.New(time.Now().Add(48 * time.Hour))

	recurringPayment := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_CREATION_QUEUED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}
	recurringPaymentAuthorised := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_MODIFY_AUTHORISED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}
	createdAt := timestampPb.New(time.Now())
	endDate2 := timestampPb.New(time.Now().Add(91 * 24 * time.Hour))

	recurringPayment2 := &pb.RecurringPayment{
		Id:          "rp-2",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_ONE_TIME,
		},
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_CREATION_QUEUED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
		CreatedAt:                createdAt,
	}

	recurringPayment3 := &pb.RecurringPayment{
		Id:          "rp-2",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   timestampPb.New(time.Now().Add(12 * time.Hour)),
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_ONE_TIME,
		},
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_CREATION_QUEUED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
		CreatedAt:                createdAt,
	}

	recurringPaymentMandate := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_UPI,
		State:                    pb.RecurringPaymentState_CREATION_QUEUED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}

	payload := &pb.RecurringPaymentModifyInfo{
		RecurringPaymentId: "rp-1",
		RequestId:          "req-id-1",
		ClientRequestId:    "client-req-1",
	}
	marshalledPayload, _ := protojson.Marshal(payload)
	rpPayload, _ := protojson.Marshal(&pb.RecurringPaymentModifyInfo{
		RecurringPaymentId: recurringPayment.GetId(),
		ClientRequestId:    "client-req-1",
		RequestId:          "req-id-1",
	})
	signalPayload, _ := protojson.Marshal(&payloadPb.ModifyRecurringPaymentAuthSignal{})
	tests := []struct {
		name           string
		req            *pb.CreateModifyAttemptRequest
		setupMockCalls func()
		want           *pb.CreateModifyAttemptResponse
		wantErr        bool
	}{
		{
			name: "failed to fetch modify attempt",
			req: &pb.CreateModifyAttemptRequest{
				RecurringPaymentId: "rp-1",
				MutableParams: &pb.MutableParams{
					MaximumAmountLimit: money.AmountINR(100).GetPb(),
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_ALLOWED_FREQUENCY_UNSPECIFIED},
					MaximumAllowedTxns: 10,
				},
				ClientRequestId:  "client-req-1",
				Provenance:       pb.RecurrencePaymentProvenance_USER_APP,
				CurrentActorId:   "actor-1",
				InitiatedBy:      pb.InitiatedBy_PAYER,
				ReqId:            "req-id-1",
				CurrentActorRole: pb.ActorRole_ACTOR_ROLE_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrInvalidSQL)
			},
			want:    &pb.CreateModifyAttemptResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "modify attempt already exists",
			req: &pb.CreateModifyAttemptRequest{
				RecurringPaymentId: "rp-1",
				MutableParams: &pb.MutableParams{
					MaximumAmountLimit: money.AmountINR(100).GetPb(),
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_ALLOWED_FREQUENCY_UNSPECIFIED},
					MaximumAllowedTxns: 10,
				},
				ClientRequestId:  "client-req-1",
				Provenance:       pb.RecurrencePaymentProvenance_USER_APP,
				CurrentActorId:   "actor-1",
				InitiatedBy:      pb.InitiatedBy_PAYER,
				ReqId:            "req-id-1",
				CurrentActorRole: pb.ActorRole_ACTOR_ROLE_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return([]*pb.RecurringPaymentsAction{
					{
						RecurringPaymentId: "rp-1",
						ClientRequestId:    "client-req-2",
						Action:             pb.Action_MODIFY,
					},
				}, nil)
			},
			want:    &pb.CreateModifyAttemptResponse{Status: rpc.StatusFailedPrecondition()},
			wantErr: false,
		},
		{
			name: "order exists",
			req: &pb.CreateModifyAttemptRequest{
				RecurringPaymentId: "rp-1",
				MutableParams: &pb.MutableParams{
					MaximumAmountLimit: money.AmountINR(100).GetPb(),
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_ALLOWED_FREQUENCY_UNSPECIFIED},
					MaximumAllowedTxns: 10,
				},
				ClientRequestId:  "client-req-1",
				Provenance:       pb.RecurrencePaymentProvenance_USER_APP,
				CurrentActorId:   "actor-1",
				InitiatedBy:      pb.InitiatedBy_PAYER,
				ReqId:            "req-id-1",
				CurrentActorRole: pb.ActorRole_ACTOR_ROLE_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order:  &orderPb.Order{Id: "order-1"},
				}, nil)
			},
			want: &pb.CreateModifyAttemptResponse{
				Status:  rpc.StatusAlreadyExists(),
				OrderId: "order-1",
			},
			wantErr: false,
		},
		{
			name: "failed to fetch recurring payment",
			req: &pb.CreateModifyAttemptRequest{
				RecurringPaymentId: "rp-1",
				MutableParams: &pb.MutableParams{
					MaximumAmountLimit: money.AmountINR(100).GetPb(),
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_ALLOWED_FREQUENCY_UNSPECIFIED},
					MaximumAllowedTxns: 10,
				},
				ClientRequestId:  "client-req-1",
				Provenance:       pb.RecurrencePaymentProvenance_USER_APP,
				CurrentActorId:   "actor-1",
				InitiatedBy:      pb.InitiatedBy_PAYER,
				ReqId:            "req-id-1",
				CurrentActorRole: pb.ActorRole_ACTOR_ROLE_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(nil, epifierrors.ErrTransient)
			},
			want: &pb.CreateModifyAttemptResponse{
				Status: rpc.StatusFromError(epifierrors.ErrTransient),
			},
			wantErr: false,
		},
		{
			name: "failed to fetch cred block type",
			req: &pb.CreateModifyAttemptRequest{
				RecurringPaymentId: "rp-1",
				MutableParams: &pb.MutableParams{
					MaximumAmountLimit: money.AmountINR(100).GetPb(),
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_ALLOWED_FREQUENCY_UNSPECIFIED},
					MaximumAllowedTxns: 10,
				},
				ClientRequestId:  "client-req-1",
				Provenance:       pb.RecurrencePaymentProvenance_USER_APP,
				CurrentActorId:   "actor-1",
				InitiatedBy:      pb.InitiatedBy_PAYER,
				ReqId:            "req-id-1",
				CurrentActorRole: pb.ActorRole_ACTOR_ROLE_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_STANDING_INSTRUCTION,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(false, pb.CredBlockType_CRED_BLOCK_TYPE_UNSPECIFIED, fmt.Errorf("eror"))
			},
			want: &pb.CreateModifyAttemptResponse{
				Status: rpc.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "failed to create modify action",
			req: &pb.CreateModifyAttemptRequest{
				RecurringPaymentId: "rp-1",
				MutableParams: &pb.MutableParams{
					MaximumAmountLimit: money.AmountINR(100).GetPb(),
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
					MaximumAllowedTxns: 8,
				},
				ClientRequestId:  "client-req-1",
				Provenance:       pb.RecurrencePaymentProvenance_USER_APP,
				CurrentActorId:   "actor-1",
				InitiatedBy:      pb.InitiatedBy_PAYER,
				ReqId:            "req-id-1",
				CurrentActorRole: pb.ActorRole_ACTOR_ROLE_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound).Times(2)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_STANDING_INSTRUCTION,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(false, pb.CredBlockType_PARTNER_SDK, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(),
					"client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment, nil,
					recurringPayment.GetState(), pb.RecurringPaymentState_MODIFY_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_MODIFY,
					ActionMetadata: &pb.ActionMetadata{
						ModifyActionMetadata: &pb.ModifyActionMetaData{
							ExistingParams: &pb.MutableParams{
								MaximumAmountLimit: money.ZeroINR().Pb,
								Interval: &types.Interval{
									StartTime: startDate,
									EndTime:   endDate,
								},
								RecurrenceRule: &pb.RecurrenceRule{
									AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
									Day:              5,
								},
								MaximumAllowedTxns: 10,
							},
							UpdatedParams: &pb.MutableParams{
								MaximumAmountLimit: money.AmountINR(100).GetPb(),
								Interval: &types.Interval{
									StartTime: startDate,
									EndTime:   endDate,
								},
								RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
								MaximumAllowedTxns: 8,
							},
						},
					},
					State:           pb.ActionState_ACTION_CREATED,
					VendorRequestId: "req-id-1",
				}).Return(nil, fmt.Errorf("error"))
			},
			want: &pb.CreateModifyAttemptResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "failed to create modify attempt for domain entity",
			req: &pb.CreateModifyAttemptRequest{
				RecurringPaymentId: "rp-1",
				MutableParams: &pb.MutableParams{
					MaximumAmountLimit: money.AmountINR(100).GetPb(),
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
					MaximumAllowedTxns: 10,
				},
				ClientRequestId:  "client-req-1",
				Provenance:       pb.RecurrencePaymentProvenance_USER_APP,
				CurrentActorId:   "actor-1",
				InitiatedBy:      pb.InitiatedBy_PAYER,
				ReqId:            "req-id-1",
				CurrentActorRole: pb.ActorRole_ACTOR_ROLE_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_STANDING_INSTRUCTION,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(false, pb.CredBlockType_PARTNER_SDK, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(),
					"client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment, nil,
					recurringPayment.GetState(), pb.RecurringPaymentState_MODIFY_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_MODIFY,
					ActionMetadata: &pb.ActionMetadata{
						ModifyActionMetadata: &pb.ModifyActionMetaData{
							ExistingParams: &pb.MutableParams{
								MaximumAmountLimit: money.ZeroINR().Pb,
								Interval: &types.Interval{
									StartTime: startDate,
									EndTime:   endDate,
								},
								RecurrenceRule: &pb.RecurrenceRule{
									AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
									Day:              5,
								},
								MaximumAllowedTxns: 10,
							},
							UpdatedParams: &pb.MutableParams{
								MaximumAmountLimit: money.AmountINR(100).GetPb(),
								Interval: &types.Interval{
									StartTime: startDate,
									EndTime:   endDate,
								},
								RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
								MaximumAllowedTxns: 10,
							},
						},
					},
					State:           pb.ActionState_ACTION_CREATED,
					VendorRequestId: "req-id-1",
				}).Return(nil, nil)
				mockSIClient.EXPECT().CreateModifyAttempt(gomock.Any(), &siPb.CreateModifyAttemptRequest{
					RecurringPaymentId: "rp-1",
					TransactionId:      "req-id-1",
				}).Return(&siPb.CreateModifyAttemptResponse{Status: rpc.StatusInternal()}, nil)
			},
			want: &pb.CreateModifyAttemptResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "failed to create order",
			req: &pb.CreateModifyAttemptRequest{
				RecurringPaymentId: "rp-1",
				MutableParams: &pb.MutableParams{
					MaximumAmountLimit: money.AmountINR(100).GetPb(),
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
					MaximumAllowedTxns: 10,
				},
				ClientRequestId:  "client-req-1",
				Provenance:       pb.RecurrencePaymentProvenance_USER_APP,
				CurrentActorId:   "actor-1",
				InitiatedBy:      pb.InitiatedBy_PAYER,
				ReqId:            "req-id-1",
				CurrentActorRole: pb.ActorRole_ACTOR_ROLE_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_STANDING_INSTRUCTION,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(false, pb.CredBlockType_PARTNER_SDK, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(),
					"client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment, nil,
					recurringPayment.GetState(), pb.RecurringPaymentState_MODIFY_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_MODIFY,
					ActionMetadata: &pb.ActionMetadata{
						ModifyActionMetadata: &pb.ModifyActionMetaData{
							ExistingParams: &pb.MutableParams{
								MaximumAmountLimit: money.ZeroINR().Pb,
								Interval: &types.Interval{
									StartTime: startDate,
									EndTime:   endDate,
								},
								RecurrenceRule: &pb.RecurrenceRule{
									AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
									Day:              5,
								},
								MaximumAllowedTxns: 10,
							},
							UpdatedParams: &pb.MutableParams{
								MaximumAmountLimit: money.AmountINR(100).GetPb(),
								Interval: &types.Interval{
									StartTime: startDate,
									EndTime:   endDate,
								},
								RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
								MaximumAllowedTxns: 10,
							},
						},
					},
					State:           pb.ActionState_ACTION_CREATED,
					VendorRequestId: "req-id-1",
				}).Return(nil, nil)
				mockSIClient.EXPECT().CreateModifyAttempt(gomock.Any(), &siPb.CreateModifyAttemptRequest{
					RecurringPaymentId: "rp-1",
					TransactionId:      "req-id-1",
				}).Return(&siPb.CreateModifyAttemptResponse{Status: rpc.StatusOk()}, nil)
				mockOrderClient.EXPECT().CreateOrder(gomock.Any(), &orderPb.CreateOrderRequest{
					ActorFrom:    "actor-1",
					ActorTo:      "actor-2",
					Workflow:     orderPb.OrderWorkflow_MODIFY_RECURRING_PAYMENT,
					Provenance:   orderPb.OrderProvenance_USER_APP,
					OrderPayload: marshalledPayload,
					Amount:       money.AmountINR(100).GetPb(),
					Status:       orderPb.OrderStatus_CREATED,
					ClientReqId:  "client-req-1",
				}).Return(&orderPb.CreateOrderResponse{Status: rpc.StatusInternal()}, nil)
			},
			want: &pb.CreateModifyAttemptResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "failed to initiate celestial workflow for recurring payment modification",
			req: &pb.CreateModifyAttemptRequest{
				RecurringPaymentId: "rp-1",
				MutableParams: &pb.MutableParams{
					MaximumAmountLimit: money.AmountINR(100).GetPb(),
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
					MaximumAllowedTxns: 10,
				},
				ClientRequestId: "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				Provenance:       pb.RecurrencePaymentProvenance_USER_APP,
				CurrentActorId:   "actor-1",
				InitiatedBy:      pb.InitiatedBy_PAYER,
				ReqId:            "req-id-1",
				CurrentActorRole: pb.ActorRole_ACTOR_ROLE_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_STANDING_INSTRUCTION,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(false, pb.CredBlockType_PARTNER_SDK, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(),
					"client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment, nil,
					recurringPayment.GetState(), pb.RecurringPaymentState_MODIFY_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_MODIFY,
					ActionMetadata: &pb.ActionMetadata{
						ModifyActionMetadata: &pb.ModifyActionMetaData{
							ExistingParams: &pb.MutableParams{
								MaximumAmountLimit: money.ZeroINR().Pb,
								Interval: &types.Interval{
									StartTime: startDate,
									EndTime:   endDate,
								},
								RecurrenceRule: &pb.RecurrenceRule{
									AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
									Day:              5,
								},
								MaximumAllowedTxns: 10,
							},
							UpdatedParams: &pb.MutableParams{
								MaximumAmountLimit: money.AmountINR(100).GetPb(),
								Interval: &types.Interval{
									StartTime: startDate,
									EndTime:   endDate,
								},
								RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
								MaximumAllowedTxns: 10,
							},
						},
					},
					State:           pb.ActionState_ACTION_CREATED,
					VendorRequestId: "req-id-1",
				}).Return(nil, nil)
				mockSIClient.EXPECT().CreateModifyAttempt(gomock.Any(), &siPb.CreateModifyAttemptRequest{
					RecurringPaymentId: "rp-1",
					TransactionId:      "req-id-1",
				}).Return(&siPb.CreateModifyAttemptResponse{Status: rpc.StatusOk()}, nil)
				mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				}, "actor-1", rpPayload, celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_MODIFY_RECURRING_PAYMENT), workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT).Return(rpc.StatusAsError(rpc.StatusInternal()))
			},
			want: &pb.CreateModifyAttemptResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "created modify attempt successfully for SI using oms flow",
			req: &pb.CreateModifyAttemptRequest{
				RecurringPaymentId: "rp-1",
				MutableParams: &pb.MutableParams{
					MaximumAmountLimit: money.AmountINR(100).GetPb(),
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
					MaximumAllowedTxns: 10,
				},
				ClientRequestId:  "client-req-1",
				Provenance:       pb.RecurrencePaymentProvenance_USER_APP,
				CurrentActorId:   "actor-1",
				InitiatedBy:      pb.InitiatedBy_PAYER,
				ReqId:            "req-id-1",
				CurrentActorRole: pb.ActorRole_ACTOR_ROLE_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(),
					"client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_STANDING_INSTRUCTION,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(false, pb.CredBlockType_PARTNER_SDK, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment, nil,
					recurringPayment.GetState(), pb.RecurringPaymentState_MODIFY_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_MODIFY,
					ActionMetadata: &pb.ActionMetadata{
						ModifyActionMetadata: &pb.ModifyActionMetaData{
							ExistingParams: &pb.MutableParams{
								MaximumAmountLimit: money.ZeroINR().Pb,
								Interval: &types.Interval{
									StartTime: startDate,
									EndTime:   endDate,
								},
								RecurrenceRule: &pb.RecurrenceRule{
									AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
									Day:              5,
								},
								MaximumAllowedTxns: 10,
							},
							UpdatedParams: &pb.MutableParams{
								MaximumAmountLimit: money.AmountINR(100).GetPb(),
								Interval: &types.Interval{
									StartTime: startDate,
									EndTime:   endDate,
								},
								RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
								MaximumAllowedTxns: 10,
							},
						},
					},
					State:           pb.ActionState_ACTION_CREATED,
					VendorRequestId: "req-id-1",
				}).Return(nil, nil)
				mockOrderClient.EXPECT().CreateOrder(gomock.Any(), &orderPb.CreateOrderRequest{
					ActorFrom:    "actor-1",
					ActorTo:      "actor-2",
					Workflow:     orderPb.OrderWorkflow_MODIFY_RECURRING_PAYMENT,
					Provenance:   orderPb.OrderProvenance_USER_APP,
					OrderPayload: marshalledPayload,
					Amount:       money.AmountINR(100).GetPb(),
					Status:       orderPb.OrderStatus_CREATED,
					ClientReqId:  "client-req-1",
				}).Return(&orderPb.CreateOrderResponse{
					Status: rpc.StatusOk(),
					Order:  &orderPb.Order{Id: "order-id-1"},
				}, nil)
				mockSIClient.EXPECT().CreateModifyAttempt(gomock.Any(), &siPb.CreateModifyAttemptRequest{
					RecurringPaymentId: "rp-1",
					TransactionId:      "req-id-1",
				}).Return(&siPb.CreateModifyAttemptResponse{Status: rpc.StatusOk()}, nil)
			},
			want: &pb.CreateModifyAttemptResponse{
				Status:                   rpc.StatusOk(),
				OrderId:                  "order-id-1",
				TxnId:                    "req-id-1",
				IsAuthenticationRequired: false,
				CredBlockType:            pb.CredBlockType_PARTNER_SDK,
			},
			wantErr: false,
		},
		{
			name: "created modify attempt successfully for SI using celestial flow",
			req: &pb.CreateModifyAttemptRequest{
				RecurringPaymentId: "rp-1",
				MutableParams: &pb.MutableParams{
					MaximumAmountLimit: money.AmountINR(100).GetPb(),
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
					MaximumAllowedTxns: 10,
				},
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				ClientRequestId:  "client-req-1",
				Provenance:       pb.RecurrencePaymentProvenance_USER_APP,
				CurrentActorId:   "actor-1",
				InitiatedBy:      pb.InitiatedBy_PAYER,
				ReqId:            "req-id-1",
				CurrentActorRole: pb.ActorRole_ACTOR_ROLE_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(),
					"client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_STANDING_INSTRUCTION,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(false, pb.CredBlockType_PARTNER_SDK, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment, nil,
					recurringPayment.GetState(), pb.RecurringPaymentState_MODIFY_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_MODIFY,
					ActionMetadata: &pb.ActionMetadata{
						ModifyActionMetadata: &pb.ModifyActionMetaData{
							ExistingParams: &pb.MutableParams{
								MaximumAmountLimit: money.ZeroINR().Pb,
								Interval: &types.Interval{
									StartTime: startDate,
									EndTime:   endDate,
								},
								RecurrenceRule: &pb.RecurrenceRule{
									AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
									Day:              5,
								},
								MaximumAllowedTxns: 10,
							},
							UpdatedParams: &pb.MutableParams{
								MaximumAmountLimit: money.AmountINR(100).GetPb(),
								Interval: &types.Interval{
									StartTime: startDate,
									EndTime:   endDate,
								},
								RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
								MaximumAllowedTxns: 10,
							},
						},
					},
					State:           pb.ActionState_ACTION_CREATED,
					VendorRequestId: "req-id-1",
				}).Return(nil, nil)
				mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				}, "actor-1", rpPayload, celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_MODIFY_RECURRING_PAYMENT), workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT).Return(nil)
				mockSIClient.EXPECT().CreateModifyAttempt(gomock.Any(), &siPb.CreateModifyAttemptRequest{
					RecurringPaymentId: "rp-1",
					TransactionId:      "req-id-1",
				}).Return(&siPb.CreateModifyAttemptResponse{Status: rpc.StatusOk()}, nil)
			},
			want: &pb.CreateModifyAttemptResponse{
				Status:                   rpc.StatusOk(),
				TxnId:                    "req-id-1",
				IsAuthenticationRequired: false,
				CredBlockType:            pb.CredBlockType_PARTNER_SDK,
			},
			wantErr: false,
		},
		{
			name: "non terminal action exists",
			req: &pb.CreateModifyAttemptRequest{
				RecurringPaymentId: "rp-1",
				MutableParams: &pb.MutableParams{
					MaximumAmountLimit: money.AmountINR(100).GetPb(),
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
					MaximumAllowedTxns: 10,
				},
				ClientRequestId:  "client-req-1",
				Provenance:       pb.RecurrencePaymentProvenance_USER_APP,
				CurrentActorId:   "actor-1",
				InitiatedBy:      pb.InitiatedBy_PAYER,
				ReqId:            "req-id-1",
				CurrentActorRole: pb.ActorRole_ACTOR_ROLE_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(), gomock.Any()).Return([]*pb.RecurringPaymentsAction{
					{
						RecurringPaymentId: "rp-1",
						ClientRequestId:    "client-req-2",
						Action:             pb.Action_MODIFY,
						State:              pb.ActionState_ACTION_CREATED,
					},
				}, nil)
			},
			want:    &pb.CreateModifyAttemptResponse{Status: rpc.StatusFailedPrecondition()},
			wantErr: false,
		},
		{
			name: "retrying same request and order exists",
			req: &pb.CreateModifyAttemptRequest{
				RecurringPaymentId: "rp-1",
				MutableParams: &pb.MutableParams{
					MaximumAmountLimit: money.AmountINR(100).GetPb(),
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
					MaximumAllowedTxns: 10,
				},
				ClientRequestId:  "client-req-1",
				Provenance:       pb.RecurrencePaymentProvenance_USER_APP,
				CurrentActorId:   "actor-1",
				InitiatedBy:      pb.InitiatedBy_PAYER,
				ReqId:            "req-id-1",
				CurrentActorRole: pb.ActorRole_ACTOR_ROLE_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1", gomock.Any(), gomock.Any()).Return([]*pb.RecurringPaymentsAction{
					{
						RecurringPaymentId: "rp-1",
						ClientRequestId:    "client-req-1",
						Action:             pb.Action_MODIFY,
						State:              pb.ActionState_ACTION_CREATED,
					},
				}, nil)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order:  &orderPb.Order{Id: "order-id-1"},
				}, nil)
			},
			want: &pb.CreateModifyAttemptResponse{
				Status:  rpc.StatusAlreadyExists(),
				OrderId: "order-id-1",
			},
			wantErr: false,
		},
		{
			name: "error in validating modification due to invalid end date for one time mandate",
			req: &pb.CreateModifyAttemptRequest{
				RecurringPaymentId: "rp-2",
				MutableParams: &pb.MutableParams{
					MaximumAmountLimit: money.AmountINR(100).GetPb(),
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate2,
					},
				},
				ClientRequestId:  "client-req-1",
				Provenance:       pb.RecurrencePaymentProvenance_USER_APP,
				CurrentActorId:   "actor-1",
				InitiatedBy:      pb.InitiatedBy_PAYER,
				ReqId:            "req-id-1",
				CurrentActorRole: pb.ActorRole_ACTOR_ROLE_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-2",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-2").Return(recurringPayment2, nil)
			},
			want: &pb.CreateModifyAttemptResponse{
				Status: rpc.NewStatusWithoutDebug(uint32(pb.CreateModifyAttemptResponse_INVALID_END_DATE),
					"end date is greater than allowed end date"),
			},
			wantErr: false,
		},
		{
			name: "error in validating modification due to modification time exceeded",
			req: &pb.CreateModifyAttemptRequest{
				RecurringPaymentId: "rp-2",
				MutableParams: &pb.MutableParams{
					MaximumAmountLimit: money.AmountINR(100).GetPb(),
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   timestampPb.New(time.Now().Add(12 * time.Hour)),
					},
				},
				ClientRequestId:  "client-req-1",
				Provenance:       pb.RecurrencePaymentProvenance_USER_APP,
				CurrentActorId:   "actor-1",
				InitiatedBy:      pb.InitiatedBy_PAYER,
				ReqId:            "req-id-1",
				CurrentActorRole: pb.ActorRole_ACTOR_ROLE_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-2",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-2").Return(recurringPayment3, nil)
			},
			want: &pb.CreateModifyAttemptResponse{
				Status: rpc.NewStatusWithoutDebug(uint32(pb.CreateModifyAttemptResponse_MODIFICATION_TIME_EXCEEDED),
					"modification time exceeded"),
			},
			wantErr: false,
		},
		{
			name: "created modify attempt successfully for a mandate using oms",
			req: &pb.CreateModifyAttemptRequest{
				RecurringPaymentId: "rp-1",
				MutableParams: &pb.MutableParams{
					MaximumAmountLimit: money.AmountINR(100).GetPb(),
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
					MaximumAllowedTxns: 10,
				},
				ClientRequestId:  "client-req-1",
				Provenance:       pb.RecurrencePaymentProvenance_USER_APP,
				CurrentActorId:   "actor-1",
				InitiatedBy:      pb.InitiatedBy_PAYER,
				ReqId:            "req-id-1",
				CurrentActorRole: pb.ActorRole_ACTOR_ROLE_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound).Times(2)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(),
					"client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPaymentMandate, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_UPI_MANDATES,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(true, pb.CredBlockType_NPCI, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPaymentMandate, nil,
					recurringPayment.GetState(), pb.RecurringPaymentState_MODIFY_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_MODIFY,
					ActionMetadata: &pb.ActionMetadata{
						ModifyActionMetadata: &pb.ModifyActionMetaData{
							ExistingParams: &pb.MutableParams{
								MaximumAmountLimit: money.ZeroINR().Pb,
								Interval: &types.Interval{
									StartTime: startDate,
									EndTime:   endDate,
								},
								RecurrenceRule: &pb.RecurrenceRule{
									AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
									Day:              5,
								},
								MaximumAllowedTxns: 10,
							},
							UpdatedParams: &pb.MutableParams{
								MaximumAmountLimit: money.AmountINR(100).GetPb(),
								Interval: &types.Interval{
									StartTime: startDate,
									EndTime:   endDate,
								},
								RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
								MaximumAllowedTxns: 10,
							},
						},
					},
					State:           pb.ActionState_ACTION_CREATED,
					VendorRequestId: "req-id-1",
				}).Return(nil, nil)
				mockOrderClient.EXPECT().CreateOrder(gomock.Any(), &orderPb.CreateOrderRequest{
					ActorFrom:    "actor-1",
					ActorTo:      "actor-2",
					Workflow:     orderPb.OrderWorkflow_MODIFY_RECURRING_PAYMENT,
					Provenance:   orderPb.OrderProvenance_USER_APP,
					OrderPayload: marshalledPayload,
					Amount:       money.AmountINR(100).GetPb(),
					Status:       orderPb.OrderStatus_CREATED,
					ClientReqId:  "client-req-1",
				}).Return(&orderPb.CreateOrderResponse{
					Status: rpc.StatusOk(),
					Order:  &orderPb.Order{Id: "order-id-1"},
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-1"}).
					Return(&piPb.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa: "payer@vpa",
								},
							},
							VerifiedName: "payer-name",
						},
					}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-2"}).
					Return(&piPb.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa: "payee@vpa",
								},
							},
							VerifiedName: "payee-name",
						},
					}, nil)
				mockMandateClient.EXPECT().ModifyMandate(gomock.Any(), newModifyMandateReqMatcher(&upiMandatePb.ModifyMandateRequest{
					RecurringPaymentId: "rp-1",
					ReqId:              "req-id-1",
					MandateRequestPayload: &upiMandatePb.Payload{
						PayerVpa:    "payer@vpa",
						PayeeVpa:    "payee@vpa",
						MandateName: "payee-name",
					},
					CurrentActorRole: upiMandatePb.ActorRole_ACTOR_ROLE_PAYER,
					PartnerBank:      commonvgpb.Vendor_FEDERAL_BANK,
				})).Return(&upiMandatePb.ModifyMandateResponse{Status: rpc.StatusOk()}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "payer@vpa",
							},
						},
						VerifiedName: "abc",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-2"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "payee@vpa",
							},
						},
						VerifiedName: "xyz",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockAccountPiClient.EXPECT().GetByPiId(gomock.Any(), &accountPiPb.GetByPiIdRequest{PiId: "pi-1"}).Return(&accountPiPb.GetByPiIdResponse{
					Status:    rpc.StatusOk(),
					AccountId: "account-id-1",
				}, nil)
				mockMandateClient.EXPECT().GetMandateRequestParameters(gomock.Any(), &upiMandatePb.GetMandateRequestParametersRequest{
					ReqId: "req-id-1",
				}).Return(&upiMandatePb.GetMandateRequestParametersResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockActor.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor-2"}).
					Return(&actorPb.GetEntityDetailsByActorIdResponse{
						Status: rpc.StatusOk(),
						Name: &commontypes.Name{
							FirstName: "payee",
						},
					}, nil)
			},
			want: &pb.CreateModifyAttemptResponse{
				Status:                   rpc.StatusOk(),
				OrderId:                  "order-id-1",
				TxnId:                    "req-id-1",
				IsAuthenticationRequired: true,
				CredBlockType:            pb.CredBlockType_NPCI,
				TransactionAttributes: []*pb.TransactionAttribute{
					{
						PayerAccountId:                "account-id-1",
						TransactionId:                 "req-id-1",
						PaymentProtocol:               paymentPb.PaymentProtocol_UPI,
						PayeeActorName:                "payee",
						Amount:                        money.ZeroINR().Pb,
						PayerPaymentInstrument:        "payer@vpa",
						PayeePaymentInstrument:        "payee@vpa",
						DisplayPayeePaymentInstrument: "payee@vpa",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "failed to create modify attempt due to error in signalling workflow in case of authorised request",
			req: &pb.CreateModifyAttemptRequest{
				RecurringPaymentId: "rp-1",
				MutableParams: &pb.MutableParams{
					MaximumAmountLimit: money.AmountINR(100).GetPb(),
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
					MaximumAllowedTxns: 10,
				},
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				ClientRequestId:  "client-req-1",
				Provenance:       pb.RecurrencePaymentProvenance_USER_APP,
				CurrentActorId:   "actor-1",
				InitiatedBy:      pb.InitiatedBy_PAYER,
				ReqId:            "req-id-1",
				CurrentActorRole: pb.ActorRole_ACTOR_ROLE_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(),
					"client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPaymentAuthorised, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_STANDING_INSTRUCTION,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(false, pb.CredBlockType_PARTNER_SDK, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPaymentAuthorised, nil,
					recurringPaymentAuthorised.GetState(), pb.RecurringPaymentState_MODIFY_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_MODIFY,
					ActionMetadata: &pb.ActionMetadata{
						ModifyActionMetadata: &pb.ModifyActionMetaData{
							ExistingParams: &pb.MutableParams{
								MaximumAmountLimit: money.ZeroINR().Pb,
								Interval: &types.Interval{
									StartTime: startDate,
									EndTime:   endDate,
								},
								RecurrenceRule: &pb.RecurrenceRule{
									AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
									Day:              5,
								},
								MaximumAllowedTxns: 10,
							},
							UpdatedParams: &pb.MutableParams{
								MaximumAmountLimit: money.AmountINR(100).GetPb(),
								Interval: &types.Interval{
									StartTime: startDate,
									EndTime:   endDate,
								},
								RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
								MaximumAllowedTxns: 10,
							},
						},
					},
					State:           pb.ActionState_ACTION_CREATED,
					VendorRequestId: "req-id-1",
				}).Return(nil, nil)
				mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				}, "actor-1", rpPayload, celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_MODIFY_RECURRING_PAYMENT), workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT).Return(nil)
				mockCelestialProcessor.EXPECT().SignalWorkflow(gomock.Any(), "client-req-1",
					string(rpNs.ModifyRecurringPaymentAuthSignal), workflowPb.Client_RECURRING_PAYMENT, signalPayload, gomock.Any(), gomock.Any()).Return(rpc.StatusAsError(rpc.StatusInternal()))
				mockSIClient.EXPECT().CreateModifyAttempt(gomock.Any(), &siPb.CreateModifyAttemptRequest{
					RecurringPaymentId: "rp-1",
					TransactionId:      "req-id-1",
				}).Return(&siPb.CreateModifyAttemptResponse{Status: rpc.StatusOk()}, nil)
			},
			want: &pb.CreateModifyAttemptResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "successfully created modify attempt in case of authorised request",
			req: &pb.CreateModifyAttemptRequest{
				RecurringPaymentId: "rp-1",
				MutableParams: &pb.MutableParams{
					MaximumAmountLimit: money.AmountINR(100).GetPb(),
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
					MaximumAllowedTxns: 10,
				},
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				ClientRequestId:  "client-req-1",
				Provenance:       pb.RecurrencePaymentProvenance_USER_APP,
				CurrentActorId:   "actor-1",
				InitiatedBy:      pb.InitiatedBy_PAYER,
				ReqId:            "req-id-1",
				CurrentActorRole: pb.ActorRole_ACTOR_ROLE_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(),
					"client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPaymentAuthorised, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_STANDING_INSTRUCTION,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(false, pb.CredBlockType_PARTNER_SDK, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPaymentAuthorised, nil,
					recurringPaymentAuthorised.GetState(), pb.RecurringPaymentState_MODIFY_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_MODIFY,
					ActionMetadata: &pb.ActionMetadata{
						ModifyActionMetadata: &pb.ModifyActionMetaData{
							ExistingParams: &pb.MutableParams{
								MaximumAmountLimit: money.ZeroINR().Pb,
								Interval: &types.Interval{
									StartTime: startDate,
									EndTime:   endDate,
								},
								RecurrenceRule: &pb.RecurrenceRule{
									AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
									Day:              5,
								},
								MaximumAllowedTxns: 10,
							},
							UpdatedParams: &pb.MutableParams{
								MaximumAmountLimit: money.AmountINR(100).GetPb(),
								Interval: &types.Interval{
									StartTime: startDate,
									EndTime:   endDate,
								},
								RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
								MaximumAllowedTxns: 10,
							},
						},
					},
					State:           pb.ActionState_ACTION_CREATED,
					VendorRequestId: "req-id-1",
				}).Return(nil, nil)
				mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				}, "actor-1", rpPayload, celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_MODIFY_RECURRING_PAYMENT), workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT).Return(nil)
				mockCelestialProcessor.EXPECT().SignalWorkflow(gomock.Any(), "client-req-1",
					string(rpNs.ModifyRecurringPaymentAuthSignal), workflowPb.Client_RECURRING_PAYMENT, signalPayload, gomock.Any(), gomock.Any()).Return(nil)
				mockSIClient.EXPECT().CreateModifyAttempt(gomock.Any(), &siPb.CreateModifyAttemptRequest{
					RecurringPaymentId: "rp-1",
					TransactionId:      "req-id-1",
				}).Return(&siPb.CreateModifyAttemptResponse{Status: rpc.StatusOk()}, nil)
			},
			want: &pb.CreateModifyAttemptResponse{
				Status:                   rpc.StatusOk(),
				TxnId:                    "req-id-1",
				IsAuthenticationRequired: false,
				CredBlockType:            pb.CredBlockType_PARTNER_SDK,
			},
			wantErr: false,
		},
		{
			name: "created modify attempt successfully for a mandate using celestial",
			req: &pb.CreateModifyAttemptRequest{
				RecurringPaymentId: "rp-1",
				MutableParams: &pb.MutableParams{
					MaximumAmountLimit: money.AmountINR(100).GetPb(),
					Interval: &types.Interval{
						StartTime: startDate,
						EndTime:   endDate,
					},
					RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
					MaximumAllowedTxns: 10,
				},
				ClientRequestId: "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				Provenance:       pb.RecurrencePaymentProvenance_USER_APP,
				CurrentActorId:   "actor-1",
				InitiatedBy:      pb.InitiatedBy_PAYER,
				ReqId:            "req-id-1",
				CurrentActorRole: pb.ActorRole_ACTOR_ROLE_PAYER,
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentsActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-1",
					gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound).Times(2)
				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(),
					"client-req-1", false).Return(nil, epifierrors.ErrRecordNotFound)
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPaymentMandate, nil)
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(pb.RecurringPaymentType_UPI_MANDATES,
					pb.ActorRole_ACTOR_ROLE_PAYER).Return(true, pb.CredBlockType_NPCI, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPaymentMandate, nil,
					recurringPayment.GetState(), pb.RecurringPaymentState_MODIFY_QUEUED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().Create(gomock.Any(), testPkg.NewProtoArgMatcher(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_MODIFY,
					ActionMetadata: &pb.ActionMetadata{
						ModifyActionMetadata: &pb.ModifyActionMetaData{
							ExistingParams: &pb.MutableParams{
								MaximumAmountLimit: money.ZeroINR().Pb,
								Interval: &types.Interval{
									StartTime: startDate,
									EndTime:   endDate,
								},
								RecurrenceRule: &pb.RecurrenceRule{
									AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
									Day:              5,
								},
								MaximumAllowedTxns: 10,
							},
							UpdatedParams: &pb.MutableParams{
								MaximumAmountLimit: money.AmountINR(100).GetPb(),
								Interval: &types.Interval{
									StartTime: startDate,
									EndTime:   endDate,
								},
								RecurrenceRule:     &pb.RecurrenceRule{AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED},
								MaximumAllowedTxns: 10,
							},
						},
					},
					State:           pb.ActionState_ACTION_CREATED,
					VendorRequestId: "req-id-1",
				})).Return(nil, nil)
				mockCelestialProcessor.EXPECT().InitiateWorkflowV2(gomock.Any(), &workflowPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				}, "actor-1", rpPayload, celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_MODIFY_RECURRING_PAYMENT), workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT).Return(nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-1"}).
					Return(&piPb.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa: "payer@vpa",
								},
							},
							VerifiedName: "payer-name",
						},
					}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-2"}).
					Return(&piPb.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa: "payee@vpa",
								},
							},
							VerifiedName: "payee-name",
						},
					}, nil)
				mockMandateClient.EXPECT().ModifyMandate(gomock.Any(), newModifyMandateReqMatcher(&upiMandatePb.ModifyMandateRequest{
					RecurringPaymentId: "rp-1",
					ReqId:              "req-id-1",
					MandateRequestPayload: &upiMandatePb.Payload{
						PayerVpa:    "payer@vpa",
						PayeeVpa:    "payee@vpa",
						MandateName: "payee-name",
					},
					CurrentActorRole: upiMandatePb.ActorRole_ACTOR_ROLE_PAYER,
					PartnerBank:      commonvgpb.Vendor_FEDERAL_BANK,
				})).Return(&upiMandatePb.ModifyMandateResponse{Status: rpc.StatusOk()}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "payer@vpa",
							},
						},
						VerifiedName: "abc",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-2"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "payee@vpa",
							},
						},
						VerifiedName: "xyz",
						State:        piPb.PaymentInstrumentState_VERIFIED,
					},
				}, nil)
				mockAccountPiClient.EXPECT().GetByPiId(gomock.Any(), &accountPiPb.GetByPiIdRequest{PiId: "pi-1"}).Return(&accountPiPb.GetByPiIdResponse{
					Status:    rpc.StatusOk(),
					AccountId: "account-id-1",
				}, nil)
				mockMandateClient.EXPECT().GetMandateRequestParameters(gomock.Any(), &upiMandatePb.GetMandateRequestParametersRequest{
					ReqId: "req-id-1",
				}).Return(&upiMandatePb.GetMandateRequestParametersResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockActor.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor-2"}).
					Return(&actorPb.GetEntityDetailsByActorIdResponse{
						Status: rpc.StatusOk(),
						Name: &commontypes.Name{
							FirstName: "payee",
						},
					}, nil)
			},
			want: &pb.CreateModifyAttemptResponse{
				Status:                   rpc.StatusOk(),
				TxnId:                    "req-id-1",
				IsAuthenticationRequired: true,
				CredBlockType:            pb.CredBlockType_NPCI,
				TransactionAttributes: []*pb.TransactionAttribute{
					{
						PayerAccountId:                "account-id-1",
						TransactionId:                 "req-id-1",
						PaymentProtocol:               paymentPb.PaymentProtocol_UPI,
						PayeeActorName:                "payee",
						Amount:                        money.ZeroINR().Pb,
						PayerPaymentInstrument:        "payer@vpa",
						PayeePaymentInstrument:        "payee@vpa",
						DisplayPayeePaymentInstrument: "payee@vpa",
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.CreateModifyAttempt(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateModifyAttempt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("CreateModifyAttempt() got = %v, want %v", got, tt.want)
			}
		})
	}
}
