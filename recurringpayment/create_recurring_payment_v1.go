package recurringpayment

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"

	rpcPb "github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	celestial2 "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	actorPb "github.com/epifi/gamma/api/actor"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpPayloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/pkg/pay"
	"github.com/epifi/gamma/pkg/pay/pgauthkeys"
	"github.com/epifi/gamma/recurringpayment/helper"
	domainCreationProcessor2 "github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor"
)

var (
	ErrorUserBlockedOrReported            = errors.New("either payer or payee is blocked or Reported")
	ErrorInvalidStartEndDate              = errors.New("invalid start end end date combination")
	ErrorAmountLimitExceeded              = errors.New("amount limit for recurring payment creation is exceeded")
	ErrPayerPayeeActorOrPiCombinationSame = errors.New("payer-payee actor/pi combination cannot be same")
)

// CreateRecurringPaymentV1 triggers creations of a new recurring payment in our system which can be of the type Standing Instruction, ENach etc.
// It accepts the list of details needed for creation of the recurring payment along with payload which is needed for
// creation of the recurring payment and is different for each recurring payment type.
// nolint: funlen
func (s *Service) CreateRecurringPaymentV1(ctx context.Context, req *rpPb.CreateRecurringPaymentV1Request) (*rpPb.CreateRecurringPaymentV1Response, error) {
	var initiatedBy rpPb.InitiatedBy
	switch {
	case req.GetRecurringPaymentDetails().GetFromActorId() == req.GetCurrentActorId():
		initiatedBy = rpPb.InitiatedBy_PAYER
	case req.GetRecurringPaymentDetails().GetToActorId() == req.GetCurrentActorId():
		initiatedBy = rpPb.InitiatedBy_PAYEE
	default:
		logger.Error(ctx, "recurring payment creation not initiated by either payer or payee", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()))
		return &rpPb.CreateRecurringPaymentV1Response{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("recurring payment creation not initiated by either payer or payee")}, nil
	}
	// identification of payment route i.e decision between native and pg routes
	paymentRoute, err := s.getPaymentRoute(ctx, req, initiatedBy)
	if err != nil {
		logger.Error(ctx, "error in determining payment route", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetCurrentActorId()))
		return &rpPb.CreateRecurringPaymentV1Response{Status: rpcPb.StatusInternalWithDebugMsg("error in determining payment route for the recurring payment")}, nil
	}
	var entityOwnership commontypes.Ownership
	var externalVendor commonvgpb.Vendor
	switch paymentRoute {
	case rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_EXTERNAL:
		pgProgramsForPiId := pgauthkeys.GetAllPgProgramsForPiId(req.GetRecurringPaymentDetails().GetPiTo())
		if len(pgProgramsForPiId) == 0 {
			entityOwnership = commontypes.Ownership_EPIFI_TECH
		} else {
			entityOwnership = pgProgramsForPiId[0].GetEntityOwnership()
			externalVendor = pgProgramsForPiId[0].GetPgVendor()
		}
	default:
		entityOwnership = commontypes.Ownership_EPIFI_TECH
	}
	ctx = epificontext.WithOwnership(ctx, entityOwnership)
	// Idempotency check
	existingRPA, err := s.recurringPaymentActionsDao.GetByClientRequestId(ctx, req.GetClientRequestId(), false)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "no existing recurring payment action found with given client request id", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()))
	case err != nil:
		logger.Error(ctx, "error fetching existing recurring payment action entry by clientRequestId", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(err))
		return &rpPb.CreateRecurringPaymentV1Response{Status: rpcPb.StatusInternalWithDebugMsg("error fetching existing recurring payment action entry by clientRequestId")}, nil
	// case where existing recurring payment action exists
	default:
		if existingRPA.GetAction() != rpPb.Action_CREATE {
			logger.Error(ctx, "another recurring payment action not related to creation exists with given clientRequestId", zap.String(logger.ACTION_TYPE, existingRPA.GetAction().String()), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()))
			return &rpPb.CreateRecurringPaymentV1Response{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("another recurring payment action not related to creation exists with given clientRequestId")}, nil
		}
		logger.Info(ctx, "recurring payment creation request already exists with given client request id", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()))

		// initiating workflow again as the last call to rpc might have failed at the workflow initiation step and action entry would still have been created, so initiating the workflow again (with same requestId) to make the rpc idempotent.
		initiateWorkflowErr := s.initiateCreateRecurringPaymentV1Workflow(ctx, existingRPA, req.GetRecurringPaymentTypeSpecificPayload(), entityOwnership)
		if initiateWorkflowErr != nil {
			logger.Error(ctx, "error initiating recurring payment creation v1 workflow", zap.String(logger.CLIENT_REQUEST_ID, existingRPA.GetClientRequestId()), zap.Error(initiateWorkflowErr))
			return &rpPb.CreateRecurringPaymentV1Response{Status: rpcPb.StatusInternalWithDebugMsg("error initiating recurring payment creation v1 workflow")}, nil
		}

		return &rpPb.CreateRecurringPaymentV1Response{
			Status:             rpcPb.StatusOk(),
			RecurringPaymentId: existingRPA.GetRecurringPaymentId(),
			NextActionDeeplink: helper.GetRecurringPaymentActionPollingScreenDeeplink(existingRPA.GetClientRequestId(), 3*time.Second, 0),
		}, nil
	}

	// validate creation request
	validationErr := s.validateRecurringPaymentCreationV1(ctx, &validateRecurringPaymentCreationV1Req{
		recurringPaymentType:   req.GetRecurringPaymentDetails().GetType(),
		fromActorId:            req.GetRecurringPaymentDetails().GetFromActorId(),
		toActorId:              req.GetRecurringPaymentDetails().GetToActorId(),
		piFrom:                 req.GetRecurringPaymentDetails().GetPiFrom(),
		piTo:                   req.GetRecurringPaymentDetails().GetPiTo(),
		interval:               req.GetRecurringPaymentDetails().GetInterval(),
		allowedFrequency:       req.GetRecurringPaymentDetails().GetRecurrenceRule().GetAllowedFrequency(),
		recurringPaymentAmount: req.GetRecurringPaymentDetails().GetAmount(),
		recurringPaymentRoute:  paymentRoute,
	})
	switch {
	case errors.Is(validationErr, ErrorUserBlockedOrReported):
		logger.Info(ctx, "either payer or payee has blocked the other", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()),
			zap.String(logger.FROM_ACTOR_ID, req.GetRecurringPaymentDetails().GetFromActorId()), zap.String(logger.TO_ACTOR_ID, req.GetRecurringPaymentDetails().GetToActorId()),
			zap.String(logger.PI_FROM, req.GetRecurringPaymentDetails().GetPiFrom()), zap.String(logger.PI_TO, req.GetRecurringPaymentDetails().GetPiTo()),
		)
		return &rpPb.CreateRecurringPaymentV1Response{Status: rpcPb.NewStatusWithoutDebug(uint32(rpPb.CreateRecurringPaymentV1Response_BLOCKED_USER), "User is blocked")}, nil
	case errors.Is(validationErr, ErrorInvalidStartEndDate):
		logger.Error(ctx, "invalid start or end date", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Any("interval", req.GetRecurringPaymentDetails().GetInterval()))
		return &rpPb.CreateRecurringPaymentV1Response{Status: rpcPb.NewStatusWithoutDebug(uint32(rpPb.CreateRecurringPaymentV1Response_INVALID_START_AND_END_DATE), "Invalid start and end date for recurring payment")}, nil
	case errors.Is(validationErr, ErrPayerPayeeActorOrPiCombinationSame):
		logger.Error(ctx, "payer-payee actor/pi combination cannot be same", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(validationErr),
			zap.String(logger.FROM_ACTOR_ID, req.GetRecurringPaymentDetails().GetFromActorId()), zap.String(logger.TO_ACTOR_ID, req.GetRecurringPaymentDetails().GetToActorId()),
			zap.String(logger.PI_FROM, req.GetRecurringPaymentDetails().GetPiFrom()), zap.String(logger.PI_TO, req.GetRecurringPaymentDetails().GetPiTo()),
		)
		return &rpPb.CreateRecurringPaymentV1Response{Status: rpcPb.StatusFailedPrecondition()}, nil
	case validationErr != nil:
		logger.Error(ctx, "error validating recurring payment creation request", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(validationErr),
			zap.String(logger.FROM_ACTOR_ID, req.GetRecurringPaymentDetails().GetFromActorId()), zap.String(logger.TO_ACTOR_ID, req.GetRecurringPaymentDetails().GetToActorId()),
			zap.String(logger.PI_FROM, req.GetRecurringPaymentDetails().GetPiFrom()), zap.String(logger.PI_TO, req.GetRecurringPaymentDetails().GetPiTo()),
		)
		return &rpPb.CreateRecurringPaymentV1Response{Status: rpcPb.StatusInternalWithDebugMsg("error validating recurring payment creation request")}, nil
	}

	var (
		createdRP  *rpPb.RecurringPayment
		createdRPA *rpPb.RecurringPaymentsAction
	)

	txnExecProvider, err := s.txnExecutorProvider.GetResourceForOwnership(entityOwnership)
	if err != nil {
		logger.Error(ctx, "error getting txn executor for entity ownership", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(err))
		return &rpPb.CreateRecurringPaymentV1Response{Status: rpcPb.StatusInternalWithDebugMsg("error getting txn executor for entity ownership")}, nil
	}

	if txnErr := txnExecProvider.RunTxn(ctx, func(txnCtx context.Context) error {
		createdRP, err = s.recurringPaymentDao.Create(txnCtx, &rpPb.RecurringPayment{
			FromActorId:        req.GetRecurringPaymentDetails().GetFromActorId(),
			ToActorId:          req.GetRecurringPaymentDetails().GetToActorId(),
			Type:               req.GetRecurringPaymentDetails().GetType(),
			PiFrom:             req.GetRecurringPaymentDetails().GetPiFrom(),
			PiTo:               req.GetRecurringPaymentDetails().GetPiTo(),
			Amount:             req.GetRecurringPaymentDetails().GetAmount(),
			Interval:           req.GetRecurringPaymentDetails().GetInterval(),
			RecurrenceRule:     req.GetRecurringPaymentDetails().GetRecurrenceRule(),
			MaximumAllowedTxns: req.GetRecurringPaymentDetails().GetMaximumAllowedTxns(),
			PartnerBank:        req.GetRecurringPaymentDetails().GetPartnerBank(),
			State:              rpPb.RecurringPaymentState_CREATION_QUEUED,
			Ownership:          req.GetRecurringPaymentDetails().GetOwnership(),
			Provenance:         req.GetRecurringPaymentDetails().GetProvenance(),
			UiEntryPoint:       req.GetRecurringPaymentDetails().GetUiEntryPoint(),
			InitiatedBy:        initiatedBy,
			AmountType:         req.GetRecurringPaymentDetails().GetAmountType(),
			ShareToPayee:       req.GetRecurringPaymentDetails().GetShareToPayee(),
			PaymentRoute:       paymentRoute,
			EntityOwnership:    entityOwnership,
		}, entityOwnership)
		if err != nil {
			return fmt.Errorf("error while creating RecurringPayment, %w", err)
		}
		var actionMetadata *rpPb.ActionMetadata
		redirectAction := req.GetPostCreationDomainDeeplink()
		if redirectAction != nil {
			actionMetadata = &rpPb.ActionMetadata{
				CreateActionMetadata: &rpPb.CreateActionMetadata{
					PostCreationDomainDeeplink: redirectAction,
				},
			}
		}
		createdRPA, err = s.recurringPaymentActionsDao.Create(txnCtx, &rpPb.RecurringPaymentsAction{
			RecurringPaymentId: createdRP.GetId(),
			ClientRequestId:    req.GetClientRequestId(),
			Action:             rpPb.Action_CREATE,
			State:              rpPb.ActionState_ACTION_CREATED,
			ExpireAt:           req.GetExpiry(),
			InitiatedBy:        getInitiatedBy(req.GetCurrentActorId(), req.GetRecurringPaymentDetails().GetFromActorId(), req.GetRecurringPaymentDetails().GetToActorId()),
			ActionMetadata:     actionMetadata,
		})
		if err != nil {
			return fmt.Errorf("error while creating RecurringPaymentAction, %w", err)
		}

		if paymentRoute == rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_EXTERNAL {
			_, err = s.rpVendorDetailsDao.Create(txnCtx, &rpPb.RecurringPaymentsVendorDetails{
				ActorId:            req.GetCurrentActorId(),
				RecurringPaymentId: createdRP.GetId(),
				Vendor:             externalVendor,
			})
			if err != nil {
				return fmt.Errorf("error while creating RecurringPaymentVendorDetails, %w", err)
			}
		}
		return nil
	}); txnErr != nil {
		logger.Error(ctx, "txn error while creating recurring payment and recurring payment action in db", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(txnErr))
		return &rpPb.CreateRecurringPaymentV1Response{Status: rpcPb.StatusInternalWithDebugMsg("txn error while creating recurring payment and recurring payment action in db")}, nil
	}
	initiateWorkflowErr := s.initiateCreateRecurringPaymentV1Workflow(ctx, createdRPA, req.GetRecurringPaymentTypeSpecificPayload(), entityOwnership)
	if initiateWorkflowErr != nil {
		logger.Error(ctx, "error initiating recurring payment creation v1 workflow", zap.String(logger.CLIENT_REQUEST_ID, existingRPA.GetClientRequestId()), zap.Error(initiateWorkflowErr))
		return &rpPb.CreateRecurringPaymentV1Response{Status: rpcPb.StatusInternalWithDebugMsg("error initiating recurring payment creation v1 workflow")}, nil
	}

	return &rpPb.CreateRecurringPaymentV1Response{
		Status:             rpcPb.StatusOk(),
		RecurringPaymentId: createdRP.GetId(),
		NextActionDeeplink: helper.GetRecurringPaymentActionPollingScreenDeeplink(createdRPA.GetClientRequestId(), 3*time.Second, 0),
	}, nil
}

// getPaymentRoute determines based on the request whether to initiate the recurring payment using the
// fi-federal system or whether to use an external fulfillment method
func (s *Service) getPaymentRoute(ctx context.Context, req *rpPb.CreateRecurringPaymentV1Request, initiatedBy rpPb.InitiatedBy) (rpPb.RecurringPaymentRoute, error) {
	switch req.GetHardPaymentRoute() {
	case rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE:
		return rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE, nil
	case rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_EXTERNAL:
		pgProgramsForPiId := pgauthkeys.GetAllPgProgramsForPiId(req.GetRecurringPaymentDetails().GetPiTo())
		if len(pgProgramsForPiId) == 0 {
			// In case the beneficiary PI is not supported by any PG program, then native flow
			// will be triggered
			return rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE, nil
		}
		return rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_EXTERNAL, nil
	default:
		// since no hard route is provided, it will be determined based on other parameters of the request
		switch req.GetRecurringPaymentDetails().GetType() {
		case rpPb.RecurringPaymentType_STANDING_INSTRUCTION:
			return rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE, nil
		case rpPb.RecurringPaymentType_ENACH_MANDATES,
			rpPb.RecurringPaymentType_UPI_MANDATES:
			if initiatedBy == rpPb.InitiatedBy_PAYEE {
				// payee initiated transactions cannot be served via an external payment gateway
				return rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE, nil
			}
			piTo := req.GetRecurringPaymentDetails().GetPiTo()
			if len(piTo) == 0 {
				// returning native for backward compatibility since for external route, a pi_to is
				// always expected
				return rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE, nil
			}

			// Checking for cases where money has to be deposited into the user's account like in case of salary program
			isPiToFiSavingsAccount, err := s.isPiFiSavingsAccount(ctx, piTo, req.GetCurrentActorId())
			if err != nil {
				return rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED, fmt.Errorf("error in determining if given pi [%s] is a fi fed savings account : %w", req.GetRecurringPaymentDetails().GetPiTo(), err)
			}
			if isPiToFiSavingsAccount {
				return rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE, nil
			}
			// Checking for case where money has to be debited from the the user's fi-federal savings account
			isPiFromFiSavingsAccount, err := s.isPiFiSavingsAccount(ctx, req.GetRecurringPaymentDetails().GetPiFrom(), req.GetCurrentActorId())
			if err != nil {
				return rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED, fmt.Errorf("error in determining if given pi [%s] is a fi fed savings account : %w", req.GetRecurringPaymentDetails().GetPiFrom(), err)
			}
			if isPiFromFiSavingsAccount {
				return rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE, nil
			}
			pgProgramsForPiId := pgauthkeys.GetAllPgProgramsForPiId(req.GetRecurringPaymentDetails().GetPiTo())
			if len(pgProgramsForPiId) == 0 {
				// In case the beneficiary PI is not supported by any PG program, then native flow
				// will be triggered
				return rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE, nil
			}
			return rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_EXTERNAL, nil
		default:
			// for backward compatibility, returning native route
			return rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE, nil
		}
	}
}

// isPiFiSavingsAccount will identify if the given PI Id is a fi-federal savings
// account or an external account
func (s *Service) isPiFiSavingsAccount(ctx context.Context, piId, actorId string) (bool, error) {
	if len(piId) == 0 {
		// defaulting to false since there is no pi to verify
		return false, nil
	}
	accPiResp, err := s.accountPIRelationsClient.GetByPiId(ctx, &accountPiPb.GetByPiIdRequest{
		PiId: piId,
	})
	switch {
	case err != nil:
		return false, fmt.Errorf("error while calling GetByPiId rpc to fetch account-pi relation for pi-id = %s : %w", piId, err)
	case accPiResp.GetStatus().IsRecordNotFound():
		// not returning error since this is a valid case where the pi is not associated with any
		// internal savings account
		return false, nil
	case !accPiResp.GetStatus().IsSuccess():
		return false, fmt.Errorf("error while fetching account-pi relation for pi-id = %s, status: %s", piId, accPiResp.GetStatus().String())
	}

	saRes, err := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_Id{
			Id: accPiResp.GetAccountId(),
		},
	})
	switch {
	case status.Code(err) == codes.NotFound:
		// not returning error since this is a valid case where the account id is not associated with any
		// internal savings account
		return false, nil
	case err != nil:
		return false, fmt.Errorf("error while fetching savings account for account id = %s : %w", accPiResp.GetAccountId(), err)
	}

	return saRes.GetAccount().GetId() == accPiResp.GetAccountId(), nil
}

func (s *Service) initiateCreateRecurringPaymentV1Workflow(ctx context.Context, recurringPaymentAction *rpPb.RecurringPaymentsAction, recurringPaymentTypeSpecificPayload *rpPayloadPb.RecurringPaymentTypeSpecificCreationPayload, ownership commontypes.Ownership) error {
	workflowPayload, payloadMarshallErr := protojson.Marshal(&rpPayloadPb.CreateRecurringPaymentV1WorkflowPayload{
		RecurringPaymentActionId:            recurringPaymentAction.GetId(),
		RecurringPaymentId:                  recurringPaymentAction.GetRecurringPaymentId(),
		Expiry:                              recurringPaymentAction.GetExpireAt(),
		RecurringPaymentTypeSpecificPayload: recurringPaymentTypeSpecificPayload,
	})
	if payloadMarshallErr != nil {
		return fmt.Errorf("error while marshalling recurring payment creation workflow payload, err : %w", payloadMarshallErr)
	}
	initiateWorkflowRes, initiateWorkflowErr := s.celestialClient.InitiateWorkflow(ctx, &celestial2.InitiateWorkflowRequest{
		Params: &celestial2.WorkflowCreationRequestParams{
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(rpNs.CreateRecurringPaymentV1),
			Payload: workflowPayload,
			ClientReqId: &workflowPb.ClientReqId{
				Id:     recurringPaymentAction.GetClientRequestId(),
				Client: workflowPb.Client_RECURRING_PAYMENT,
			},
			Ownership:        ownership,
			UseCase:          pay.OwnershipToUseCaseForPay[ownership],
			QualityOfService: celestial2.QoS_BEST_EFFORT,
		},
	})
	if initiateWorkflowErr != nil || !initiateWorkflowRes.GetStatus().IsSuccess() && !initiateWorkflowRes.GetStatus().IsAlreadyExists() {
		logger.Error(ctx, "celestialClient.InitiateWorkflow rpc call failed", zap.String(logger.CLIENT_REQUEST_ID, recurringPaymentAction.GetClientRequestId()), zap.Any(logger.RPC_STATUS, initiateWorkflowRes.GetStatus()), zap.Error(initiateWorkflowErr))
		return fmt.Errorf("celestialClient.InitiateWorkflow rpc call failed")
	}
	return nil
}

func getInitiatedBy(currentActorId, fromActorId, toActorId string) rpPb.InitiatedBy {
	switch {
	case currentActorId == fromActorId:
		return rpPb.InitiatedBy_PAYER
	case currentActorId == toActorId:
		return rpPb.InitiatedBy_PAYEE
	default:
		return rpPb.InitiatedBy_INITIATED_BY_UNSPECIFIED
	}
}

type validateRecurringPaymentCreationV1Req struct {
	recurringPaymentType   rpPb.RecurringPaymentType
	fromActorId            string
	toActorId              string
	piFrom                 string
	piTo                   string
	interval               *types.Interval
	payload                []byte
	allowedFrequency       rpPb.AllowedFrequency
	recurringPaymentAmount *money.Money
	recurringPaymentRoute  rpPb.RecurringPaymentRoute
}

// validateRecurringPaymentCreationV1 runs the following validations
// 1. Check payee is not blocked/reported by payer and vice versa.
// 2. Check start and end time is valid.
// 3. Check domain specific validations for the recurring payment type.
func (s *Service) validateRecurringPaymentCreationV1(ctx context.Context, req *validateRecurringPaymentCreationV1Req) error {
	// Check payee and payer should not be same
	if req.piFrom == req.piTo || req.fromActorId == req.toActorId {
		return ErrPayerPayeeActorOrPiCombinationSame
	}
	// Check payee is not blocked by payer and vice versa
	hasEitherUserBlockedTheOther, err := s.hasEitherUserBlockedTheOther(ctx, req.fromActorId, req.toActorId)
	if err != nil {
		return fmt.Errorf("error while trying to validate creation request, %w", err)
	}
	if hasEitherUserBlockedTheOther {
		return ErrorUserBlockedOrReported
	}

	currTime := time.Now()
	startTime := req.interval.GetStartTime().AsTime()
	endTime := req.interval.GetEndTime().AsTime()

	if startTime.Before(currTime) || startTime.After(endTime) {
		return ErrorInvalidStartEndDate
	}

	// Check domain specific validation for the recurring payment type
	domainCreationProcessor, err := s.domainCreationProcessorFactory.GetProcessor(req.recurringPaymentType, req.recurringPaymentRoute)
	if err != nil {
		return fmt.Errorf("error while fetching processor implementation, %w", err)
	}
	err = domainCreationProcessor.ValidateCreation(ctx, &domainCreationProcessor2.ValidateCreationReq{
		StartTime:              startTime,
		EndTime:                endTime,
		AllowedFrequency:       req.allowedFrequency,
		Payload:                req.payload,
		RecurringPaymentAmount: req.recurringPaymentAmount,
	})
	if err != nil {
		return fmt.Errorf("domain specific validation for recurring payment, %w", err)
	}

	return nil
}

// hasEitherUserBlockedTheOther returns false if neither actors have blocked or reported each other.
func (s *Service) hasEitherUserBlockedTheOther(ctx context.Context, fromActorId, toActorId string) (bool, error) {
	getRelationRes, err := s.actorClient.GetRelationshipWithActor(ctx, &actorPb.GetRelationshipWithActorRequest{
		CurrentActorId: fromActorId,
		OtherActorId:   toActorId,
	})
	if err = epifigrpc.RPCError(getRelationRes, err); err != nil {
		return false, fmt.Errorf("unable to fetch relationship between payer %s, and payee %s, err: %w", fromActorId, toActorId, err)
	}
	if getRelationRes.GetRelationship() == actorPb.GetRelationshipWithActorResponse_BLOCKED || getRelationRes.GetRelationship() == actorPb.GetRelationshipWithActorResponse_REPORTED {
		return true, nil
	}

	getRelationRes, err = s.actorClient.GetRelationshipWithActor(ctx, &actorPb.GetRelationshipWithActorRequest{
		CurrentActorId: toActorId,
		OtherActorId:   fromActorId,
	})
	if err = epifigrpc.RPCError(getRelationRes, err); err != nil {
		return false, fmt.Errorf("unable to fetch relationship between payer %s, and payee %s, err: %w", fromActorId, toActorId, err)
	}
	if getRelationRes.GetRelationship() == actorPb.GetRelationshipWithActorResponse_BLOCKED || getRelationRes.GetRelationship() == actorPb.GetRelationshipWithActorResponse_REPORTED {
		return true, nil
	}

	return false, nil
}
