// nolint: goimports
package recurringpayment

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/epificontext"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/logger"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	pb "github.com/epifi/gamma/api/recurringpayment"
	payloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	"github.com/epifi/gamma/pkg/recurringpayment"
)

// nolint: dupl
// We are using the client as workflowPb.Client_RECURRING_PAYMENT across recurring payment workflows to prevent exposure of backend information on Client side
func (s *Service) AuthoriseRecurringPaymentCreation(ctx context.Context,
	req *pb.AuthoriseRecurringPaymentCreationRequest) (*pb.AuthoriseRecurringPaymentCreationResponse, error) {
	var (
		res                  = &pb.AuthoriseRecurringPaymentCreationResponse{}
		stateToUpdate        = pb.RecurringPaymentState_RECURRING_PAYMENT_STATE_UNSPECIFIED
		actionStateToUpdate  pb.ActionState
		actionDetailedStatus *pb.ActionDetailedStatus_DetailedStatus
	)
	clientReqId := req.GetClientId().GetId()
	if clientReqId == "" {
		clientReqId = req.GetClientRequestId()
	}

	if clientReqId == "" {
		logger.Error(ctx, "ClientRequestId can't be empty")
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	recurringPayment, err := s.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "record not found for recurring payment id", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error while fetching recurring payment", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		logger.Debug(ctx, "recurring payment record fetched successfully", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
	}
	ctx = epificontext.WithOwnership(ctx, recurringPayment.GetEntityOwnership())

	err = validateActorIdForRequest(recurringPayment, req.GetCurrentActorId())
	if err != nil {
		logger.Error(ctx, "failed to validate actor", zap.String(logger.ACTOR_ID, req.GetCurrentActorId()))
		res.Status = rpc.StatusPermissionDenied()
		return res, nil
	}

	recurringPaymentAction, err := s.recurringPaymentActionsDao.GetByClientRequestId(ctx, clientReqId, false)
	if err != nil {
		logger.Error(ctx, "failed to fetch create action", zap.String(logger.CLIENT_REQUEST_ID, clientReqId), zap.Error(err))
		res.Status = rpc.StatusPermissionDenied()
		return res, nil
	}

	postAuthDeeplink, err := s.setPostAuthDeeplink(recurringPaymentAction)
	if err != nil {
		logger.Error(ctx, "error while setting post auth deeplink", zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	status := s.validateActionExpiry(ctx, recurringPaymentAction, recurringPayment, pb.RecurringPaymentState_FAILED)
	if status != nil {
		res.Status = status
		return res, nil
	}

	if !s.featureFlags.EnableRecurringPaymentCreationViaCelestial() {
		_, status = s.getOrderByClientRequestId(ctx, clientReqId)
		if !status.IsSuccess() {
			logger.Error(ctx, "error in fetching order for client request id",
				zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}

	switch {
	case recurringPayment.GetState() == pb.RecurringPaymentState_CREATION_AUTHORISED:
		logger.Info(ctx, "recurring payment authorisation already initiated",
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		res.Status = rpc.StatusOk()
		return res, nil
	// for payer initiated mandates is the current actor is payer, then the recurring payment will be in QUEUED state
	// for all the other cases recurring payment will be in INITIATED state
	case recurringPayment.GetState() != pb.RecurringPaymentState_CREATION_INITIATED && recurringPayment.GetState() != pb.RecurringPaymentState_CREATION_QUEUED:
		logger.Error(ctx, "recurring payment not in the right state to be authorised", zap.String(logger.STATE, recurringPayment.GetState().String()),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		res.Status = rpc.StatusFailedPrecondition()
		return res, nil
	}

	recurringPaymentState, err := s.getRecurringPaymentStateForInitiation(
		ctx,
		req.GetCurrentActorId(),
		recurringPayment.GetFromActorId(),
		pb.Action_CREATE,
	)
	if err != nil {
		logger.Error(ctx, "error while fetching recurring payment state for initiation", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if s.featureFlags.EnableRecurringPaymentCreationViaCelestial() {

		workflowReq, processorErr := s.celestialProcessor.GetWorkflowByClientRequestId(ctx, clientReqId, workflowPb.Client_RECURRING_PAYMENT)
		if processorErr != nil {
			logger.Error(ctx, "error fetching workflow request for given client req ID", zap.Error(processorErr),
				zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
			res.Status = rpc.StatusFromError(processorErr)
			return res, nil
		}

		// workflow waits for signal only for 10 minutes after initiation, and will time out after that
		if time.Since(workflowReq.GetCreatedAt().AsTime()) > s.config.RecurringPaymentCreationParams.AuthorisationTimeLimit {
			logger.Error(ctx, "workflow can't be authorised after 10 minutes")
			res.Status = rpc.StatusInternal()
			return res, nil

		}

		// sending signal to workflow
		payload, marshalErr := protojson.Marshal(&payloadPb.CreateRecurringPaymentAuthSignal{})
		if marshalErr != nil {
			logger.Error(ctx, "error in marshalling create recurring payment auth signal payload", zap.Error(marshalErr), zap.String(
				logger.CLIENT_REQUEST_ID, clientReqId))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		err = s.celestialProcessor.SignalWorkflow(ctx, clientReqId, string(rpNs.CreateRecurringPaymentAuthSignal), workflowPb.Client_RECURRING_PAYMENT, payload, recurringPayment.GetEntityOwnership(), recurringpayment.OwnershipToUseCaseForRecPay[recurringPayment.GetEntityOwnership()])
		if err != nil {
			logger.Error(ctx, "error while signaling workflow for creation", zap.Error(err),
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()), zap.String(logger.CLIENT_REQUEST_ID,
					recurringPaymentAction.GetClientRequestId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	} else {
		err = s.initiateOrderProcessing(ctx, recurringPaymentAction.GetClientRequestId())
		if err != nil {
			logger.Error(ctx, "error while triggering order processing for creation", zap.Error(err),
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()), zap.String(logger.CLIENT_REQUEST_ID,
					recurringPaymentAction.GetClientRequestId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}

	err = s.updateRecurringPaymentState(ctx, recurringPayment, recurringPaymentState)
	if err != nil {
		logger.Error(ctx, "error while updating recurring payment state", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	recurringPayment.State = recurringPaymentState

	switch recurringPayment.GetType() {
	case pb.RecurringPaymentType_STANDING_INSTRUCTION:
		actionDetailedStatus, err = s.authoriseStandingInstructionCreation(ctx, recurringPayment, req, recurringPaymentAction)
		if err != nil {
			logger.Error(ctx, "error while authorising SI creation", zap.Error(err),
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
			stateToUpdate = pb.RecurringPaymentState_FAILED
			actionStateToUpdate = pb.ActionState_ACTION_FAILURE
			status = rpc.StatusFromError(err)
		} else {
			stateToUpdate = pb.RecurringPaymentState_ACTIVATED
			actionStateToUpdate = pb.ActionState_ACTION_SUCCESS
		}
		res.ActionDetailedStatus = actionDetailedStatus

	case pb.RecurringPaymentType_UPI_MANDATES:
		actionDetailedStatus, err = s.authoriseMandateAction(
			ctx,
			req.GetCredential(),
			recurringPayment,
			recurringPaymentAction,
			req.GetCurrentActorId(),
		)
		if err != nil {
			logger.Error(ctx, "error while authorising mandate creation", zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()),
				zap.Error(err))
		}
		res.ActionDetailedStatus = actionDetailedStatus
	default:
		logger.Error(ctx, "invalid recurring payment type", zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()),
			zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if stateToUpdate != pb.RecurringPaymentState_RECURRING_PAYMENT_STATE_UNSPECIFIED {
		recurringpayment.AddActionDetailedStatus(recurringPaymentAction, &pb.ActionDetailedStatus{
			DetailedStatusList: []*pb.ActionDetailedStatus_DetailedStatus{
				actionDetailedStatus,
			},
		})
		err = s.updateRecurringPaymentAndActionInTxnBlock(ctx, recurringPaymentAction, recurringPayment,
			nil, []pb.RecurringPaymentActionFieldMask{pb.RecurringPaymentActionFieldMask_ACTION_DETAILED_STATUS},
			stateToUpdate, actionStateToUpdate)
		if err != nil {
			logger.Error(ctx, "error while updating recurring payment and action state", zap.Error(err),
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		}
	}

	s.publishRecurringPaymentActionEvent(ctx, recurringPaymentAction, recurringPayment)

	if status != nil {
		res.Status = status
		return res, nil
	}

	res.PostAuthoriseDeeplink = postAuthDeeplink
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) updateRecurringPaymentState(ctx context.Context, recurringPayment *pb.RecurringPayment, stateToUpdate pb.RecurringPaymentState) error {
	err := s.recurringPaymentDao.UpdateAndChangeStatus(ctx, recurringPayment, nil, recurringPayment.GetState(), stateToUpdate)
	if err != nil {
		return fmt.Errorf("error while updating recurring payment state")
	}
	return nil
}

func (s *Service) authoriseStandingInstructionCreation(ctx context.Context, recurringPayment *pb.RecurringPayment, req *pb.AuthoriseRecurringPaymentCreationRequest,
	recurringPaymentAction *pb.RecurringPaymentsAction) (*pb.ActionDetailedStatus_DetailedStatus, error) {
	authoriseSICreationRes, err := s.siClient.AuthoriseCreation(ctx, &siPb.AuthoriseCreationRequest{
		RecurringPaymentId:       recurringPayment.GetId(),
		FromActorId:              recurringPayment.GetFromActorId(),
		ToActorId:                recurringPayment.GetToActorId(),
		PiFrom:                   recurringPayment.GetPiFrom(),
		PiTo:                     recurringPayment.GetPiTo(),
		MaximumAmountLimit:       recurringPayment.GetAmount(),
		Interval:                 recurringPayment.GetInterval(),
		AllowedFrequency:         recurringPayment.GetRecurrenceRule().GetAllowedFrequency(),
		MaximumAllowedTxns:       recurringPayment.GetMaximumAllowedTxns(),
		PartnerBank:              recurringPayment.GetPartnerBank(),
		PreferredPaymentProtocol: recurringPayment.GetPreferredPaymentProtocol(),
		PartnerSdkCredBlock:      req.GetCredential().GetPartnerSdkCredBlock(),
	})
	te := epifigrpc.RPCError(authoriseSICreationRes, err)
	if te != nil {
		logger.Error(ctx, "error authorising si creation", zap.Error(te))
	}
	return recurringpayment.GetActionDetailedStatus(recurringPayment.GetPreferredPaymentProtocol(), authoriseSICreationRes.GetActionDetailedStatus()), te
}

// validateActorIdForRequest checks if the actor who initiated the request is either payer or payee
func validateActorIdForRequest(recurringPayment *pb.RecurringPayment, currentActorId string) error {
	if currentActorId == recurringPayment.GetFromActorId() || currentActorId == recurringPayment.GetToActorId() {
		return nil
	}
	return fmt.Errorf("actor does not have permission for request")
}

func (s *Service) setPostAuthDeeplink(recurringPaymentAction *pb.RecurringPaymentsAction) (*deeplinkPb.Deeplink, error) {
	switch {
	case recurringPaymentAction.GetPostAuthDeeplink() != nil:
		postAuthDeeplink := &deeplinkPb.Deeplink{}
		err := recurringPaymentAction.GetPostAuthDeeplink().UnmarshalTo(postAuthDeeplink)
		if err != nil {
			return nil, fmt.Errorf("failed to unmarshal post auth deeplink %v %w", recurringPaymentAction.GetRecurringPaymentId(), err)
		}
		return postAuthDeeplink, nil

		// what should be the default status screen in this case?
	default:
		return nil, nil
	}
}
