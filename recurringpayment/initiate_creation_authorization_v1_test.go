package recurringpayment

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"

	celestialPb "github.com/epifi/be-common/api/celestial"
	celestialMocks "github.com/epifi/be-common/api/celestial/mocks"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/recurringpayment"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	"github.com/epifi/gamma/recurringpayment/helper"
	"github.com/epifi/gamma/recurringpayment/internal/actionstatusfetcher"
	actionStatusFetcherMocks "github.com/epifi/gamma/recurringpayment/internal/actionstatusfetcher/mocks"
	domainCreationProcessorMocks "github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor/mocks"
)

func TestService_InitiateCreationAuthorizationV1(t *testing.T) {
	recurringPayment1 := &recurringpayment.RecurringPayment{
		Id:   "recurring-payment-id-1",
		Type: recurringpayment.RecurringPaymentType_ENACH_MANDATES,
	}
	recurringPaymentAction1 := &recurringpayment.RecurringPaymentsAction{
		Id:                 "recurring-payment-action-id-1",
		ClientRequestId:    "client-request-id-1",
		RecurringPaymentId: recurringPayment1.GetId(),
		Action:             recurringpayment.Action_CREATE,
	}
	emptyPbMarshalled, _ := protojson.Marshal(&emptyPb.Empty{})

	pollingScreenDeeplink := helper.GetRecurringPaymentActionPollingScreenDeeplink(recurringPaymentAction1.GetClientRequestId(), 2*time.Second, 0)

	type args struct {
		ctx context.Context
		req *recurringpayment.InitiateCreationAuthorizationV1Request
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, actionStatusFetcher *actionStatusFetcherMocks.MockIActionStatusFetcher, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockCelestialServiceClient *celestialMocks.MockCelestialClient)
		want       *recurringpayment.InitiateCreationAuthorizationV1Response
		wantErr    bool
	}{
		{
			name: "should return ISE rpc status code when dao call to fetch recurring payment action fails",
			args: args{
				ctx: context.Background(),
				req: &recurringpayment.InitiateCreationAuthorizationV1Request{ClientRequestId: "client-request-id-1"},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, actionStatusFetcher *actionStatusFetcherMocks.MockIActionStatusFetcher, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockCelestialServiceClient *celestialMocks.MockCelestialClient) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", true).Return(nil, errors.New("error"))
			},
			want: &recurringpayment.InitiateCreationAuthorizationV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error fetching recurring payment action from db"),
			},
		},
		{
			name: "should return ISE rpc status code when dao call to fetch recurring payment action return record not found error",
			args: args{
				ctx: context.Background(),
				req: &recurringpayment.InitiateCreationAuthorizationV1Request{ClientRequestId: "client-request-id-1"},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, actionStatusFetcher *actionStatusFetcherMocks.MockIActionStatusFetcher, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockCelestialServiceClient *celestialMocks.MockCelestialClient) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", true).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &recurringpayment.InitiateCreationAuthorizationV1Response{
				Status: rpc.StatusFailedPreconditionWithDebugMsg("no recurring payment action found with given clientRequestId"),
			},
		},
		{
			name: "should return ISE rpc status code when dao call to fetch recurring payment by id fails",
			args: args{
				ctx: context.Background(),
				req: &recurringpayment.InitiateCreationAuthorizationV1Request{ClientRequestId: "client-request-id-1"},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, actionStatusFetcher *actionStatusFetcherMocks.MockIActionStatusFetcher, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockCelestialServiceClient *celestialMocks.MockCelestialClient) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", true).Return(recurringPaymentAction1, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "recurring-payment-id-1").Return(nil, errors.New("error while fetching recurring payment"))
			},
			want: &recurringpayment.InitiateCreationAuthorizationV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error fetching recurring payment by id from db"),
			},
		},
		{
			name: "should return FailedPrecondition rpc status when recurring payment is not currently blocked on authorization state",
			args: args{
				ctx: context.Background(),
				req: &recurringpayment.InitiateCreationAuthorizationV1Request{ClientRequestId: "client-request-id-1"},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, actionStatusFetcher *actionStatusFetcherMocks.MockIActionStatusFetcher, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockCelestialServiceClient *celestialMocks.MockCelestialClient) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", true).Return(recurringPaymentAction1, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "recurring-payment-id-1").Return(recurringPayment1, nil)
				actionStatusFetcher.EXPECT().GetActionStatusAndNextStepDeeplink(gomock.Any(), recurringPayment1, recurringPaymentAction1, &actionstatusfetcher.Metadata{}).Return(recurringpayment.ActionState_ACTION_SUCCESS, recurringpayment.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, &deeplink.Deeplink{Screen: deeplink.Screen_AUTHORIZE_RECURRING_PAYMENT_SCREEN}, nil, nil)
			},
			want: &recurringpayment.InitiateCreationAuthorizationV1Response{
				Status: rpc.StatusFailedPreconditionWithDebugMsg("can't initiate authorization, recurring payment is not blocked on authorization"),
			},
		},
		{
			name: "should return ISE when call to fetch auth deeplink from domain processor fails",
			args: args{
				ctx: context.Background(),
				req: &recurringpayment.InitiateCreationAuthorizationV1Request{ClientRequestId: "client-request-id-1"},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, actionStatusFetcher *actionStatusFetcherMocks.MockIActionStatusFetcher, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockCelestialServiceClient *celestialMocks.MockCelestialClient) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", true).Return(recurringPaymentAction1, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "recurring-payment-id-1").Return(recurringPayment1, nil)
				actionStatusFetcher.EXPECT().GetActionStatusAndNextStepDeeplink(gomock.Any(), recurringPayment1, recurringPaymentAction1, &actionstatusfetcher.Metadata{}).Return(recurringpayment.ActionState_ACTION_IN_PROGRESS, recurringpayment.ActionSubState_ACTION_SUB_STATE_BLOCKED_ON_AUTHORIZATION, &deeplink.Deeplink{Screen: deeplink.Screen_AUTHORIZE_RECURRING_PAYMENT_SCREEN}, nil, nil)
				mockDomainCreationProcessor.EXPECT().GetAuthorizationDeeplink(gomock.Any(), "recurring-payment-id-1").Return(nil, errors.New("error"))
			},
			want: &recurringpayment.InitiateCreationAuthorizationV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error fetching domain specific auth deeplink"),
			},
		},
		{
			name: "should return ISE rpc status when there's an error while sending auth initiated signal to workflow",
			args: args{
				ctx: context.Background(),
				req: &recurringpayment.InitiateCreationAuthorizationV1Request{ClientRequestId: "client-request-id-1"},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, actionStatusFetcher *actionStatusFetcherMocks.MockIActionStatusFetcher, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockCelestialServiceClient *celestialMocks.MockCelestialClient) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", true).Return(recurringPaymentAction1, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "recurring-payment-id-1").Return(recurringPayment1, nil)
				actionStatusFetcher.EXPECT().GetActionStatusAndNextStepDeeplink(gomock.Any(), recurringPayment1, recurringPaymentAction1, &actionstatusfetcher.Metadata{}).Return(recurringpayment.ActionState_ACTION_IN_PROGRESS, recurringpayment.ActionSubState_ACTION_SUB_STATE_BLOCKED_ON_AUTHORIZATION, &deeplink.Deeplink{Screen: deeplink.Screen_AUTHORIZE_RECURRING_PAYMENT_SCREEN}, nil, nil)
				mockDomainCreationProcessor.EXPECT().GetAuthorizationDeeplink(gomock.Any(), "recurring-payment-id-1").Return(&deeplink.Deeplink{Screen: deeplink.Screen_AUTHORIZE_RECURRING_PAYMENT_SCREEN}, nil)
				mockCelestialServiceClient.EXPECT().SignalWorkflow(gomock.Any(), &celestialPb.SignalWorkflowRequest{
					Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Client: workflowPb.Client_RECURRING_PAYMENT,
							Id:     recurringPaymentAction1.GetClientRequestId(),
						}},
					SignalId:         string(rpNs.CreateRecurringPaymentV1AuthorisationInitiatedSignal),
					Payload:          emptyPbMarshalled,
					Ownership:        commontypes.Ownership_EPIFI_TECH,
					QualityOfService: celestialPb.QoS_BEST_EFFORT,
				}).Return(&celestialPb.SignalWorkflowResponse{Status: rpc.StatusInternal()}, nil)
			},
			want: &recurringpayment.InitiateCreationAuthorizationV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error sending auth initiated signal to creation workflow"),
			},
		},
		{
			name: "should return OK rpc status code with authorization details in response",
			args: args{
				ctx: context.Background(),
				req: &recurringpayment.InitiateCreationAuthorizationV1Request{ClientRequestId: "client-request-id-1"},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, actionStatusFetcher *actionStatusFetcherMocks.MockIActionStatusFetcher, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockCelestialServiceClient *celestialMocks.MockCelestialClient) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-request-id-1", true).Return(recurringPaymentAction1, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "recurring-payment-id-1").Return(recurringPayment1, nil)
				actionStatusFetcher.EXPECT().GetActionStatusAndNextStepDeeplink(gomock.Any(), recurringPayment1, recurringPaymentAction1, &actionstatusfetcher.Metadata{}).Return(recurringpayment.ActionState_ACTION_IN_PROGRESS, recurringpayment.ActionSubState_ACTION_SUB_STATE_BLOCKED_ON_AUTHORIZATION, &deeplink.Deeplink{Screen: deeplink.Screen_AUTHORIZE_RECURRING_PAYMENT_SCREEN}, nil, nil)
				mockCelestialServiceClient.EXPECT().SignalWorkflow(gomock.Any(), &celestialPb.SignalWorkflowRequest{
					Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Client: workflowPb.Client_RECURRING_PAYMENT,
							Id:     recurringPaymentAction1.GetClientRequestId(),
						}},
					SignalId:         string(rpNs.CreateRecurringPaymentV1AuthorisationInitiatedSignal),
					Payload:          emptyPbMarshalled,
					Ownership:        commontypes.Ownership_EPIFI_TECH,
					QualityOfService: celestialPb.QoS_BEST_EFFORT,
				}).Return(&celestialPb.SignalWorkflowResponse{Status: rpc.StatusOk()}, nil)
				mockDomainCreationProcessor.EXPECT().GetAuthorizationDeeplink(gomock.Any(), "recurring-payment-id-1").Return(&deeplink.Deeplink{Screen: deeplink.Screen_AUTHORIZE_RECURRING_PAYMENT_SCREEN}, nil)
			},
			want: &recurringpayment.InitiateCreationAuthorizationV1Response{
				Status:              rpc.StatusOk(),
				AuthDeeplink:        &deeplink.Deeplink{Screen: deeplink.Screen_AUTHORIZE_RECURRING_PAYMENT_SCREEN},
				PostAuthRedirection: pollingScreenDeeplink,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
			mockRecurringPaymentActionsDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
			mockActionStatusFetcherFactory := actionStatusFetcherMocks.NewMockIActionStatusFetcherFactory(ctr)
			mockActionStatusFetcher := actionStatusFetcherMocks.NewMockIActionStatusFetcher(ctr)
			mockActionStatusFetcherFactory.EXPECT().GetActionStatusFetcher(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockActionStatusFetcher).AnyTimes()
			mockDomainCreationProcessorFactory := domainCreationProcessorMocks.NewMockDomainCreationProcessorFactory(ctr)
			mockDomainCreationProcessor := domainCreationProcessorMocks.NewMockDomainCreationProcessorV1(ctr)
			mockDomainCreationProcessorFactory.EXPECT().GetProcessor(gomock.Any(), gomock.Any()).Return(mockDomainCreationProcessor, nil).AnyTimes()
			mockCelestialServiceClient := celestialMocks.NewMockCelestialClient(ctr)

			tt.setupMocks(mockRecurringPaymentDao, mockRecurringPaymentActionsDao, mockActionStatusFetcher, mockDomainCreationProcessor, mockCelestialServiceClient)

			s := &Service{
				recurringPaymentDao:            mockRecurringPaymentDao,
				recurringPaymentActionsDao:     mockRecurringPaymentActionsDao,
				actionStatusFetcherFactory:     mockActionStatusFetcherFactory,
				domainCreationProcessorFactory: mockDomainCreationProcessorFactory,
				celestialClient:                mockCelestialServiceClient,
			}
			got, err := s.InitiateCreationAuthorizationV1(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("InitiateCreationAuthorizationV1() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("InitiateCreationAuthorizationV1() got = %v, want %v", got, tt.want)
			}
		})
	}
}
