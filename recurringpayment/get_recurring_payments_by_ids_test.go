package recurringpayment

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"

	rpcPb "github.com/epifi/be-common/api/rpc"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/be-common/pkg/epifierrors"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
)

func TestService_GetRecurringPaymentsByIds(t *testing.T) {
	const (
		defaultRecurringPaymentId = "default_test_recurring_payment_id"
		defaultActorFrom          = "default_test_actor_from"
		defaultActorTo            = "default_test_actor_to"
		defaultPiFrom             = "default_test_pi_from"
		defaultPiTo               = "default_test_pi_to"
	)

	defaultRecurringPayment := &rpPb.RecurringPayment{
		Id:          defaultRecurringPaymentId,
		FromActorId: defaultActorFrom,
		ToActorId:   defaultActorTo,
		Type:        rpPb.RecurringPaymentType_ENACH_MANDATES,
		PiFrom:      defaultPiFrom,
		PiTo:        defaultPiTo,
		State:       rpPb.RecurringPaymentState_ACTIVATED,
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		RecurrenceRule: &rpPb.RecurrenceRule{
			AllowedFrequency: rpPb.AllowedFrequency_DAILY,
		},
		Amount: &money.Money{
			CurrencyCode: "INR",
			Units:        500,
			Nanos:        0,
		},
	}

	tests := []struct {
		name       string
		req        *rpPb.GetRecurringPaymentsByIdsRequest
		setupMocks func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao)
		want       *rpPb.GetRecurringPaymentsByIdsResponse
		wantErr    bool
	}{
		{
			name: "should return ISE if dao call to fetch recurring payments by ids failed with permanent error",
			req: &rpPb.GetRecurringPaymentsByIdsRequest{
				Ids: []string{defaultRecurringPaymentId},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao) {
				mockRecurringPaymentDao.EXPECT().GetByIds(gomock.Any(), []string{defaultRecurringPaymentId}).
					Return(nil, epifierrors.ErrPermanent)
			},
			want: &rpPb.GetRecurringPaymentsByIdsResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "should return statusRecordNotFound if dao call to fetch recurring payments by ids failed with record not found",
			req: &rpPb.GetRecurringPaymentsByIdsRequest{
				Ids: []string{defaultRecurringPaymentId},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao) {
				mockRecurringPaymentDao.EXPECT().GetByIds(gomock.Any(), []string{defaultRecurringPaymentId}).
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &rpPb.GetRecurringPaymentsByIdsResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "should return fetched recurring payments if dao call succeeded.",
			req: &rpPb.GetRecurringPaymentsByIdsRequest{
				Ids: []string{defaultRecurringPaymentId},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao) {
				mockRecurringPaymentDao.EXPECT().GetByIds(gomock.Any(), []string{defaultRecurringPaymentId}).
					Return([]*rpPb.RecurringPayment{
						defaultRecurringPayment,
					}, nil)
			},
			want: &rpPb.GetRecurringPaymentsByIdsResponse{
				Status: rpcPb.StatusOk(),
				RecurringPayments: []*rpPb.RecurringPayment{
					defaultRecurringPayment,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
			tt.setupMocks(mockRecurringPaymentDao)

			svc := &Service{
				recurringPaymentDao: mockRecurringPaymentDao,
			}

			got, err := svc.GetRecurringPaymentsByIds(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRecurringPaymentsByIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetRecurringPaymentsByIds() got = %v, want %v", got, tt.want)
			}
		})
	}
}
