package recurringpayment

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"
	"gorm.io/gorm"

	celestial2 "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpcPb "github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	pb "github.com/epifi/gamma/api/recurringpayment"
	payloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
	"github.com/epifi/gamma/pkg/pay"
	"github.com/epifi/gamma/pkg/recurringpayment"
)

// nolint: funlen
var (
	actionToMandateTypeMap = map[pb.Action]upiMandatePb.MandateType{
		pb.Action_PAUSE:   upiMandatePb.MandateType_PAUSE,
		pb.Action_UNPAUSE: upiMandatePb.MandateType_UNPAUSE,
	}
)

// We are using the client as workflowPb.Client_RECURRING_PAYMENT across recurring payment workflows to prevent exposure of backend information on Client side
func (s *Service) CreatePauseOrUnpauseAttempt(ctx context.Context, req *pb.CreatePauseOrUnpauseAttemptRequest) (*pb.CreatePauseOrUnpauseAttemptResponse, error) {
	var (
		res                    = &pb.CreatePauseOrUnpauseAttemptResponse{}
		txnId                  string
		currentActorId         string
		currentActorRole       pb.ActorRole
		recurringPaymentStatus pb.RecurringPaymentState
	)
	clientReqId := req.GetClientId().GetId()
	if clientReqId == "" {
		clientReqId = req.GetClientRequestId()
	}
	err := s.checkPendingActions(ctx, req.GetRecurringPaymentId(), clientReqId, req.GetAction())
	switch {
	case errors.Is(err, failedPreconditionError):
		logger.Info(ctx, "pending pause unpause requests exists..fast failing pause/unpause attempt",
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		res.Status = rpcPb.StatusFailedPrecondition()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error while fetching pause/unpause actions", zap.String(logger.RECURRING_PAYMENT_ID,
			req.GetRecurringPaymentId()), zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	default:
		logger.Debug(ctx, "continuing pause unpause request")
	}
	_, status := s.getOrderByClientRequestId(ctx, clientReqId)
	switch {
	case status.IsSuccess():
		res.Status = rpcPb.StatusAlreadyExists()
		return res, nil
	case status.IsRecordNotFound():
		logger.Debug(ctx, "order record not found, new recurring payment creation requested",
			zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
	default:
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	recurringPayment, err := s.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "recurring payment not found", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpcPb.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error while fetching recurring payment", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	default:
		logger.Debug(ctx, "recurring payment fetched successfully")
	}
	err = s.validatePauseUnpauseRequest(ctx, recurringPayment, req.GetAction())
	if err != nil {
		logger.Error(ctx, "validation failue for pause/unpause request", zap.String(logger.STATE,
			recurringPayment.GetState().String()), zap.String(logger.RECURRING_PAYMENT_ID,
			recurringPayment.GetId()), zap.String(logger.ACTION_TYPE, req.GetAction().String()), zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	currentActorId, currentActorRole = getCurrentActorIdAndRole(req.GetCurrentActorId(), req.GetCurrentActorRole(), recurringPayment)
	isAuthRequired, credBlockType, err := s.recurringPaymentProcessor.GetCredBlockType(recurringPayment.GetType(), currentActorRole)
	if err != nil {
		logger.Error(ctx, "error in fetching cred block type", zap.Error(err))
		res.Status = rpcPb.StatusInvalidArgument()
		return res, nil
	}
	txnId = req.GetTransactionId()
	if txnId == "" {
		txnId, err = generateRecurringPaymentPauseUnpauseTransactionId(recurringPayment.GetType(), recurringPayment.GetPartnerBank())
		if err != nil {
			logger.Error(ctx, "error while generating transaction id for recurring payment pause/unpause", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}
	action, err := s.recurringPaymentActionsDao.GetByClientRequestId(ctx, clientReqId, false)
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrRecordNotFound):
		if req.GetInitiatedBy() == pb.InitiatedBy_INITIATED_BY_UNSPECIFIED {
			req.InitiatedBy = roleToInitiatedBy[currentActorRole]
		}
		txnErr := storageV2.RunCRDBTxn(ctx, func(txnCtx context.Context) error {
			recurringPaymentStatus, err = getRecurringPaymentRequestState(req.GetInitiatedBy(),
				recurringPayment.GetFromActorId(), currentActorId, req.GetAction())
			if err != nil {
				return fmt.Errorf("error fetching recurring payment status :%w", err)
			}
			err = s.recurringPaymentDao.UpdateAndChangeStatus(txnCtx, recurringPayment,
				nil, recurringPayment.GetState(), recurringPaymentStatus)
			if err != nil {
				return fmt.Errorf("error while updating recurring payment state for recurring payment pause/unpause %w", err)
			}
			var actionMetadata *pb.ActionMetadata
			if req.GetAction() == pb.Action_PAUSE && req.GetPauseInterval() != nil {
				actionMetadata = &pb.ActionMetadata{PauseActionMetadata: &pb.PauseActionMetaData{PauseInterval: req.GetPauseInterval()}}
			}
			action, err = s.recurringPaymentActionsDao.Create(txnCtx, &pb.RecurringPaymentsAction{
				RecurringPaymentId: req.GetRecurringPaymentId(),
				ClientRequestId:    clientReqId,
				Action:             req.GetAction(),
				State:              pb.ActionState_ACTION_CREATED,
				VendorRequestId:    txnId,
				Remarks:            req.GetRemarks(),
				ExpireAt:           req.GetExpiry(),
				InitiatedBy:        req.GetInitiatedBy(),
				ActionMetadata:     actionMetadata,
			})
			if err != nil {
				return fmt.Errorf("error while creating recurring payment action %w", err)
			}
			return nil
		})
		if txnErr != nil {
			logger.Error(ctx, "failed to commit transaction for recurring payment pause unpause",
				zap.String(logger.CLIENT_REQUEST_ID, clientReqId), zap.String(logger.RECURRING_PAYMENT_ID,
					req.GetRecurringPaymentId()), zap.Error(txnErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	case err != nil:
		logger.Error(ctx, "error in fetching action", zap.Error(err), zap.String(logger.ACTION_TYPE,
			req.GetAction().String()), zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	default:
		txnId = action.GetVendorRequestId()
	}
	err = s.pauseUnpauseDomainServiceEntity(ctx, recurringPayment, txnId, req.GetPayload(), currentActorRole, req.GetAction())
	if err != nil {
		logger.Error(ctx, "error in pause unpause domain entity", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if isAuthRequired && credBlockType == pb.CredBlockType_NPCI {
		transactionAttributes, err1 := s.getTransactionAttributes(ctx, recurringPayment, nil, txnId, nil)
		if err1 != nil {
			logger.Error(ctx, "error in fetching transaction attributes", zap.Error(err1), zap.String(logger.REQUEST_ID, txnId))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		res.TransactionAttributes = transactionAttributes
	}
	if s.featureFlags.EnableRecurringPaymentPauseUnpauseViaCelestial() {
		err = s.initiateRecurringPaymentPauseUnpauseWorkflowViaCelestial(ctx, recurringPayment, &workflowPb.ClientReqId{
			Id:     clientReqId,
			Client: workflowPb.Client_RECURRING_PAYMENT,
		}, req.GetCurrentActorId(), req.GetAction())
		if err != nil {
			logger.Error(ctx, "error while initiating celestial workflow", zap.Error(err),
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
			status := rpcPb.StatusFromError(err)
			res.Status = status
			return res, nil
		}
	} else {
		_, err = s.createOrderForPauseUnpause(
			ctx,
			recurringPayment,
			clientReqId,
			recurringPayment.GetAmount(),
			req.GetProvenance(),
			req.GetAction(),
		)
	}
	if err != nil {
		logger.Error(ctx, "error in creating order for recurring payment pause/unpause", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if (req.GetAction() == pb.Action_PAUSE && recurringPaymentStatus == pb.RecurringPaymentState_PAUSE_AUTHORISED) ||
		(req.GetAction() == pb.Action_UNPAUSE && recurringPaymentStatus == pb.RecurringPaymentState_UNPAUSE_AUTHORISED) {
		err = s.initiatePauseUnpauseWorkflowOrchestration(ctx, &workflowPb.ClientReqId{
			Id:     clientReqId,
			Client: workflowPb.Client_RECURRING_PAYMENT,
		}, recurringPayment)
		if err != nil {
			logger.Error(ctx, "error while triggering order processing for pause/unpause", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID,
				clientReqId), zap.String(logger.ACTION_TYPE, req.GetAction().String()))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}

	s.publishRecurringPaymentActionEvent(ctx, action, recurringPayment)

	res.IsAuthenticationRequired = isAuthRequired
	res.CredBlockType = credBlockType
	res.TxnId = txnId
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func generateRecurringPaymentPauseUnpauseTransactionId(recurringPaymentType pb.RecurringPaymentType, partnerBank commonvgpb.Vendor) (string, error) {
	switch recurringPaymentType {
	case pb.RecurringPaymentType_UPI_MANDATES:
		switch partnerBank {
		case commonvgpb.Vendor_FEDERAL_BANK:
			return pay.GenerateVendorRequestId(partnerBank, paymentPb.PaymentProtocol_UPI)
		default:
			return "", fmt.Errorf("vendor not supported for standing instruction pause unpause %s", partnerBank.String())
		}
	default:
		return "", fmt.Errorf("recurring payment type not supported for pause unpause %s", recurringPaymentType.String())
	}
}

// nolint: dupl
func (s *Service) createOrderForPauseUnpause(
	ctx context.Context,
	recurringPayment *pb.RecurringPayment,
	clientRequestId string,
	amount *money.Money,
	provenance pb.RecurrencePaymentProvenance,
	action pb.Action,
) (*orderPb.Order, error) {
	orderPayload, err := protojson.Marshal(&pb.RecurringPaymentPauseUnpauseInfo{
		RecurringPaymentId: recurringPayment.GetId(),
		ClientRequestId:    clientRequestId,
		Action:             action,
	})
	if err != nil {
		return nil, fmt.Errorf("error while marshalling recurring payment pause unpause info %w", err)
	}
	orderProvenance, ok := recurringPaymentProvenanceToOrderProvenanceMap[provenance]
	if !ok {
		return nil, fmt.Errorf("failed to fetch order provenance")
	}
	createOrderRes, err := s.orderClient.CreateOrder(ctx, &orderPb.CreateOrderRequest{
		ActorFrom:    recurringPayment.GetFromActorId(),
		ActorTo:      recurringPayment.GetToActorId(),
		Workflow:     orderPb.OrderWorkflow_PAUSE_OR_UNPAUSE_RECURRING_PAYMENT,
		Status:       orderPb.OrderStatus_CREATED,
		OrderPayload: orderPayload,
		ClientReqId:  clientRequestId,
		Provenance:   orderProvenance,
		Amount:       amount,
	})
	if te := epifigrpc.RPCError(createOrderRes, err); te != nil {
		return nil, fmt.Errorf("error while creating order %w", te)
	}
	return createOrderRes.GetOrder(), nil
}

func (s *Service) pauseUnpauseDomainServiceEntity(
	ctx context.Context,
	recurringPayment *pb.RecurringPayment,
	txnId string,
	payload []byte,
	currentActorRole pb.ActorRole,
	action pb.Action) error {
	switch recurringPayment.GetType() {
	case pb.RecurringPaymentType_UPI_MANDATES:
		var (
			requestPayload = &upiMandatePb.Payload{}
			err            error
		)
		if payload == nil {
			if requestPayload, err = s.createMandatePayload(ctx, recurringPayment); err != nil {
				return fmt.Errorf("error in create mandate payload :%w", err)
			}
		} else {
			err = protojson.Unmarshal(payload, requestPayload)
			if err != nil {
				return fmt.Errorf("error unmarshalling payload :%w", err)
			}
		}
		res, err := s.upiMandateClient.PauseUnpauseMandate(ctx, &upiMandatePb.PauseUnpauseMandateRequest{
			RecurringPaymentId:    recurringPayment.GetId(),
			ReqId:                 txnId,
			MandateRequestPayload: requestPayload,
			CurrentActorRole:      recurringPaymentToMandateActorRole[currentActorRole],
			PartnerBank:           recurringPayment.GetPartnerBank(),
			ReqAction:             actionToMandateTypeMap[action],
		})
		if te := epifigrpc.RPCError(res, err); te != nil {
			if res.GetStatus().IsAlreadyExists() {
				return nil
			}
			return fmt.Errorf("error in pausing/unpausing recurring payment %w", te)
		}
		return nil
	default:
		return fmt.Errorf("unsupported recurring payment %s", recurringPayment.GetType().String())
	}
}

// validatePauseUnpauseRequest does initial validations for recurring payment state and actions.
// For pause request recurring payment should be in activated state and there should be no pending execute actions
// For unpause recurring payment should be in paused state
func (s *Service) validatePauseUnpauseRequest(ctx context.Context, recurringPayment *pb.RecurringPayment, action pb.Action) error {
	switch action {
	case pb.Action_PAUSE:
		if recurringPayment.GetState() != pb.RecurringPaymentState_ACTIVATED {
			return fmt.Errorf("recurring payment not in expected state for pause %s", recurringPayment.GetState().String())
		}
		return s.validatePendingExecutions(ctx, recurringPayment)
	case pb.Action_UNPAUSE:
		if recurringPayment.GetState() != pb.RecurringPaymentState_PAUSED {
			return fmt.Errorf("recurring payment not in expected state for un pause")
		}
	default:
		return fmt.Errorf("unsupported action recieved %s", action.String())
	}
	return nil
}

// nolint: dupl
// initiateRecurringPaymentPauseUnpauseWorkflowViaCelestial - initiates recurring payment pause unpause workflow via celestial
func (s *Service) initiateRecurringPaymentPauseUnpauseWorkflowViaCelestial(ctx context.Context, recurringPayment *pb.RecurringPayment, clientId *workflowPb.ClientReqId, currentActorId string, action pb.Action) error {
	var rpPayload []byte
	rpPayload, err := protojson.Marshal(&pb.RecurringPaymentPauseUnpauseInfo{
		RecurringPaymentId: recurringPayment.GetId(),
		ClientRequestId:    clientId.GetId(),
		Action:             action,
	})
	if err != nil {
		return fmt.Errorf("error while marshalling recurring payment pause/unpause info %w", rpcPb.StatusAsError(rpcPb.StatusInternal()))
	}
	err = s.celestialProcessor.InitiateWorkflowV2(ctx, clientId, currentActorId, rpPayload, celestialPkg.GetTypeEnumFromWorkflowType(rpNs.PauseUnpauseRecurringPayment), workflowPb.Version_V0, celestial2.QoS_BEST_EFFORT)
	if err != nil {
		return fmt.Errorf("error while initiating workflow %w %s", rpcPb.StatusAsError(rpcPb.StatusInternal()),
			recurringPayment.GetId())
	}
	return nil
}

// nolint: dupl
// initiatePauseUnpauseWorkflowOrchestration - initiates orchestration for recurring payment pause unpause flow
func (s *Service) initiatePauseUnpauseWorkflowOrchestration(ctx context.Context, clientId *workflowPb.ClientReqId, recurringPayment *pb.RecurringPayment) error {
	if s.featureFlags.EnableRecurringPaymentPauseUnpauseViaCelestial() {
		// sending signal to workflow
		payload, marshalErr := protojson.Marshal(&payloadPb.PauseUnpauseRecurringPaymentAuthSignal{})
		if marshalErr != nil {
			return fmt.Errorf("error in marshalling pause/unpause recurring payment auth signal payload %w %s", rpcPb.StatusAsError(rpcPb.StatusInternal()), clientId.GetId())
		}
		err := s.celestialProcessor.SignalWorkflow(ctx, clientId.GetId(),
			string(rpNs.PauseUnpauseRecurringPaymentAuthSignal), clientId.GetClient(), payload, recurringPayment.GetEntityOwnership(), recurringpayment.OwnershipToUseCaseForRecPay[recurringPayment.GetEntityOwnership()])
		if err != nil {
			return fmt.Errorf("error while signaling workflow %w %s", rpcPb.StatusAsError(rpcPb.StatusInternal()),
				clientId.GetId())
		}
	} else {
		err := s.initiateOrderProcessing(ctx, clientId.GetId())
		if err != nil {
			return fmt.Errorf("error while triggering order processing for pause/unpause %w %s", rpcPb.StatusAsError(rpcPb.StatusInternal()),
				clientId.GetId())
		}
	}
	return nil
}
