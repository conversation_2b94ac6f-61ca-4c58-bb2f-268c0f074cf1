package recurringpayment

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/money"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	actorPb "github.com/epifi/gamma/api/actor"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	orderPb "github.com/epifi/gamma/api/order"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	pb "github.com/epifi/gamma/api/recurringpayment"
	payloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	"github.com/epifi/gamma/api/recurringpayment/standinginstruction/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	internalMocks "github.com/epifi/gamma/recurringpayment/internal/mocks"
)

func TestService_AuthoriseRecurringPaymentModify(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockOrderClient := orderMocks.NewMockOrderServiceClient(ctr)
	mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
	mockRecurringPaymentVendorDetailsDao := daoMocks.NewMockRecurringPaymentsVendorDetailsDao(ctr)
	mockRecurringPaymentsActionDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
	mockSIClient := mocks.NewMockStandingInstructionServiceClient(ctr)
	mockRecurringPaymentProcessor := internalMocks.NewMockRecurringPaymentProcessor(ctr)
	mockActorClient := actorMocks.NewMockActorClient(ctr)
	mockCelestialProcessor := internalMocks.NewMockCelestialProcessor(ctr)

	svc := NewService(mockRecurringPaymentDao, mockOrderClient, mockSIClient, mockRecurringPaymentsActionDao, nil, mockActorClient, nil, nil, nil, conf, mockRecurringPaymentProcessor, nil, nil, nil, nil, nil, nil, nil, dynamicConf.FeatureFlags(), mockCelestialProcessor, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, mockRecurringPaymentVendorDetailsDao, nil, nil, nil, nil)

	startDate := timestampPb.New(time.Now())
	endDate := timestampPb.New(time.Now().Add(48 * time.Hour))

	recurringPayment2 := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_MODIFY_QUEUED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}
	recurringPayment1 := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_MODIFY_QUEUED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}
	updatedRecurringPayment1 := &pb.RecurringPayment{
		Id:                       "rp-1",
		FromActorId:              "actor-1",
		ToActorId:                "actor-2",
		Type:                     pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:                   "pi-1",
		PiTo:                     "pi-2",
		Amount:                   money.AmountINR(1000).Pb,
		AmountType:               pb.AmountType_MAXIMUM,
		MaximumAllowedTxns:       20,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_MODIFY_AUTHORISED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}
	recurringPayment3 := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_MODIFY_QUEUED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}
	updatedRecurringPayment3 := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_MODIFY_AUTHORISED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}

	action1 := &pb.RecurringPaymentsAction{
		RecurringPaymentId: "rp-1",
		ClientRequestId:    "client-req-1",
		Action:             pb.Action_MODIFY,
		ActionMetadata: &pb.ActionMetadata{
			ModifyActionMetadata: &pb.ModifyActionMetaData{
				UpdatedParams: &pb.MutableParams{
					MaximumAmountLimit: money.AmountINR(1000).GetPb(),
					MaximumAllowedTxns: 20,
				},
			},
		},
		State:           pb.ActionState_ACTION_CREATED,
		VendorRequestId: "request-1",
		ExpireAt:        timestampPb.New(time.Now().Add(100 * time.Minute)),
	}
	payload, _ := protojson.Marshal(&payloadPb.ModifyRecurringPaymentAuthSignal{})
	tests := []struct {
		name           string
		req            *pb.AuthoriseRecurringPaymentModifyRequest
		setupMockCalls func()
		want           *pb.AuthoriseRecurringPaymentModifyResponse
		wantErr        bool
	}{
		{
			name: "actor validation failed",
			req: &pb.AuthoriseRecurringPaymentModifyRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "secure-pin"}},
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-3",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment2, nil)
			},
			want:    &pb.AuthoriseRecurringPaymentModifyResponse{Status: rpc.StatusPermissionDenied()},
			wantErr: false,
		},
		{
			name: "state validation failed",
			req: &pb.AuthoriseRecurringPaymentModifyRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "secure-pin"}},
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(&pb.RecurringPayment{
					FromActorId: "actor-1",
					State:       pb.RecurringPaymentState_ACTIVATED,
				}, nil)
			},
			want:    &pb.AuthoriseRecurringPaymentModifyResponse{Status: rpc.StatusFailedPrecondition()},
			wantErr: false,
		},
		{
			name: "action record not found",
			req: &pb.AuthoriseRecurringPaymentModifyRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "secure-pin"}},
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment2, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(nil,
					epifierrors.ErrRecordNotFound)
			},
			want:    &pb.AuthoriseRecurringPaymentModifyResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "failed to authorise domain service using oms flow",
			req: &pb.AuthoriseRecurringPaymentModifyRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "secure-pin"}},
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment3, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(action1, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  &types.Actor{Type: types.Actor_USER},
				}, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment3, nil,
					recurringPayment3.GetState(), pb.RecurringPaymentState_MODIFY_AUTHORISED).Return(nil)
				mockOrderClient.EXPECT().InitiateOrderProcessing(gomock.Any(), &orderPb.InitiateOrderProcessingRequest{Identifier: &orderPb.InitiateOrderProcessingRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.InitiateOrderProcessingResponse{Status: rpc.StatusOk()}, nil)
				mockSIClient.EXPECT().AuthoriseModify(gomock.Any(), &siPb.AuthoriseModifyRequest{
					RecurringPaymentId:  "rp-1",
					MaximumAmountLimit:  money.AmountINR(1000).GetPb(),
					MaximumAllowedTxns:  20,
					PartnerSdkCredBlock: "secure-pin",
				}).Return(&siPb.AuthoriseModifyResponse{Status: rpc.StatusInternal()}, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), updatedRecurringPayment3,
					nil, pb.RecurringPaymentState_MODIFY_AUTHORISED, pb.RecurringPaymentState_ACTIVATED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), action1, nil,
					pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_FAILURE).Return(nil)
			},
			want:    &pb.AuthoriseRecurringPaymentModifyResponse{Status: rpc.StatusFromError(rpc.StatusAsError(rpc.StatusInternal()))},
			wantErr: false,
		},
		{
			name: "failed to authorise domain service using celestial flow",
			req: &pb.AuthoriseRecurringPaymentModifyRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "secure-pin"}},
				ClientRequestId:    "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				CurrentActorId: "actor-1",
			},
			setupMockCalls: func() {
				recurringPayment3.State = pb.RecurringPaymentState_MODIFY_QUEUED
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment3, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(action1, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  &types.Actor{Type: types.Actor_USER},
				}, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment3, nil,
					recurringPayment3.GetState(), pb.RecurringPaymentState_MODIFY_AUTHORISED).Return(nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).
					Return(&celestialPb.WorkflowRequest{
						Id:        "workflow_req_id",
						CreatedAt: timestampPb.Now(),
					}, nil)
				mockCelestialProcessor.EXPECT().SignalWorkflow(gomock.Any(), "client-req-1", string(rpNs.ModifyRecurringPaymentAuthSignal), workflowPb.Client_RECURRING_PAYMENT, payload, gomock.Any(), gomock.Any()).Return(nil)
				mockSIClient.EXPECT().AuthoriseModify(gomock.Any(), &siPb.AuthoriseModifyRequest{
					RecurringPaymentId:  "rp-1",
					MaximumAmountLimit:  money.AmountINR(1000).GetPb(),
					MaximumAllowedTxns:  20,
					PartnerSdkCredBlock: "secure-pin",
				}).Return(&siPb.AuthoriseModifyResponse{Status: rpc.StatusInternal()}, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), updatedRecurringPayment3,
					nil, pb.RecurringPaymentState_MODIFY_AUTHORISED, pb.RecurringPaymentState_ACTIVATED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), action1, nil,
					pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_FAILURE).Return(nil)
			},
			want:    &pb.AuthoriseRecurringPaymentModifyResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "failed to authorise, failed to fetch workflow for given client req ID",
			req: &pb.AuthoriseRecurringPaymentModifyRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "secure-pin"}},
				ClientRequestId:    "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				CurrentActorId: "actor-1",
			},
			setupMockCalls: func() {
				recurringPayment3.State = pb.RecurringPaymentState_MODIFY_QUEUED
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment3, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(action1, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  &types.Actor{Type: types.Actor_USER},
				}, nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).
					Return(nil, rpc.StatusAsError(rpc.StatusRecordNotFound()))
			},
			want:    &pb.AuthoriseRecurringPaymentModifyResponse{Status: rpc.StatusRecordNotFound()},
			wantErr: false,
		},
		{
			name: "failed to authorise, workflow timed out after 10 mins",
			req: &pb.AuthoriseRecurringPaymentModifyRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "secure-pin"}},
				ClientRequestId:    "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				CurrentActorId: "actor-1",
			},
			setupMockCalls: func() {
				recurringPayment3.State = pb.RecurringPaymentState_MODIFY_QUEUED
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment3, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(action1, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  &types.Actor{Type: types.Actor_USER},
				}, nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).
					Return(&celestialPb.WorkflowRequest{
						Id:        "workflow_req_id",
						CreatedAt: timestampPb.New(time.Now().Add(-15 * time.Minute)),
					}, nil)
			},
			want:    &pb.AuthoriseRecurringPaymentModifyResponse{Status: rpc.StatusPermissionDenied()},
			wantErr: false,
		},
		{
			name: "failed to authorise, failed to signal workflow from celestial",
			req: &pb.AuthoriseRecurringPaymentModifyRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "secure-pin"}},
				ClientRequestId:    "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				CurrentActorId: "actor-1",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment3, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(action1, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  &types.Actor{Type: types.Actor_USER},
				}, nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).
					Return(&celestialPb.WorkflowRequest{
						Id:        "workflow_req_id",
						CreatedAt: timestampPb.Now(),
					}, nil)
				mockCelestialProcessor.EXPECT().SignalWorkflow(gomock.Any(), "client-req-1", string(rpNs.ModifyRecurringPaymentAuthSignal), workflowPb.Client_RECURRING_PAYMENT, payload, gomock.Any(), gomock.Any()).Return(errors.New("server issue"))
			},
			want:    &pb.AuthoriseRecurringPaymentModifyResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "authorised modify attempt successfully via oms flow",
			req: &pb.AuthoriseRecurringPaymentModifyRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "secure-pin"}},
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment1, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(action1, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  &types.Actor{Type: types.Actor_USER},
				}, nil)
				mockOrderClient.EXPECT().InitiateOrderProcessing(gomock.Any(), &orderPb.InitiateOrderProcessingRequest{Identifier: &orderPb.InitiateOrderProcessingRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.InitiateOrderProcessingResponse{Status: rpc.StatusOk()}, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment1, nil,
					recurringPayment1.GetState(), pb.RecurringPaymentState_MODIFY_AUTHORISED).Return(nil)
				mockSIClient.EXPECT().AuthoriseModify(gomock.Any(), &siPb.AuthoriseModifyRequest{
					RecurringPaymentId:  "rp-1",
					MaximumAmountLimit:  money.AmountINR(1000).GetPb(),
					MaximumAllowedTxns:  20,
					PartnerSdkCredBlock: "secure-pin",
				}).Return(&siPb.AuthoriseModifyResponse{Status: rpc.StatusOk()}, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), updatedRecurringPayment1,
					[]pb.RecurringPaymentFieldMask{pb.RecurringPaymentFieldMask_MAXIMUM_AMOUNT_LIMIT, pb.RecurringPaymentFieldMask_MAXIMUM_ALLOWED_TXNS},
					pb.RecurringPaymentState_MODIFY_AUTHORISED, pb.RecurringPaymentState_ACTIVATED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), action1, nil,
					pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_SUCCESS).Return(nil)
			},
			want:    &pb.AuthoriseRecurringPaymentModifyResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
		{
			name: "authorised modify attempt successfully via celestial",
			req: &pb.AuthoriseRecurringPaymentModifyRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "secure-pin"}},
				ClientRequestId:    "client-req-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				CurrentActorId: "actor-1",
			},
			setupMockCalls: func() {
				recurringPayment1.State = pb.RecurringPaymentState_MODIFY_QUEUED
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentModificationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentProcessor.EXPECT().GetRecurringPaymentById(gomock.Any(), "rp-1").Return(recurringPayment1, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(action1, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  &types.Actor{Type: types.Actor_USER},
				}, nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).
					Return(&celestialPb.WorkflowRequest{
						Id:        "workflow_req_id",
						CreatedAt: timestampPb.Now(),
					}, nil)
				mockCelestialProcessor.EXPECT().SignalWorkflow(gomock.Any(), "client-req-1", string(rpNs.ModifyRecurringPaymentAuthSignal), workflowPb.Client_RECURRING_PAYMENT, payload, gomock.Any(), gomock.Any()).Return(nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment1, nil,
					recurringPayment1.GetState(), pb.RecurringPaymentState_MODIFY_AUTHORISED).Return(nil)
				mockSIClient.EXPECT().AuthoriseModify(gomock.Any(), &siPb.AuthoriseModifyRequest{
					RecurringPaymentId:  "rp-1",
					MaximumAmountLimit:  money.AmountINR(1000).GetPb(),
					MaximumAllowedTxns:  20,
					PartnerSdkCredBlock: "secure-pin",
				}).Return(&siPb.AuthoriseModifyResponse{Status: rpc.StatusOk()}, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), updatedRecurringPayment1,
					[]pb.RecurringPaymentFieldMask{pb.RecurringPaymentFieldMask_MAXIMUM_AMOUNT_LIMIT, pb.RecurringPaymentFieldMask_MAXIMUM_ALLOWED_TXNS},
					pb.RecurringPaymentState_MODIFY_AUTHORISED, pb.RecurringPaymentState_ACTIVATED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), action1, nil,
					pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_SUCCESS).Return(nil)
			},
			want:    &pb.AuthoriseRecurringPaymentModifyResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.AuthoriseRecurringPaymentModify(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AuthoriseRecurringPaymentModify() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("AuthoriseRecurringPaymentModify() got = %v, want %v", got, tt.want)
			}
		})
	}
}
