// nolint: goimports
package recurringpayment

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/cfg"
	storageV2Mocks "github.com/epifi/be-common/pkg/storage/v2/mocks"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/durationpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"

	celestial2 "github.com/epifi/be-common/api/celestial"
	celestialMocks "github.com/epifi/be-common/api/celestial/mocks"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	accountPb "github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	actorPbMocks "github.com/epifi/gamma/api/actor/mocks"
	"github.com/epifi/gamma/api/frontend/deeplink"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	mocks2 "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	"github.com/epifi/gamma/api/paymentinstrument/mocks"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	payloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	rpPayloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	savingsPb "github.com/epifi/gamma/api/savings"
	mocks3 "github.com/epifi/gamma/api/savings/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	rpScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/recurringpayment"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/baseprovider"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	domainCreationProcessor "github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor"
	domainCreationProcessorMocks "github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor/mocks"
)

func TestService_CreateRecurringPaymentV1(t *testing.T) {

	const (
		id                  = "recurring-payment-action-id-1"
		clientRequestId     = "client-request-id-1"
		clientRequestId2    = "client-request-id-1"
		recurringPaymentId  = "recurring-payment-id-1"
		recurringPaymentId2 = "recurring-payment-id-2"
		fromActorId         = "from-actor-id-1"
		toActorId           = "to-actor-id-1"
		piActorFrom         = "pi-actor-from"
		piActorTo           = "pi-actor-to"
		piActorTo2          = "paymentinstrument-creditcard-federal-pool-account-1"
	)

	ctxWithOwnership := epificontext.WithOwnership(context.Background(), commontypes.Ownership_EPIFI_TECH)

	createRecurringPaymentReq1 := &rpPb.CreateRecurringPaymentV1Request{
		ClientRequestId:  clientRequestId,
		CurrentActorId:   fromActorId,
		HardPaymentRoute: rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE,
		RecurringPaymentDetails: &rpPb.RecurringPaymentCreationDetails{
			FromActorId: fromActorId,
			ToActorId:   toActorId,
			Type:        rpPb.RecurringPaymentType_ENACH_MANDATES,
			PiFrom:      piActorFrom,
			PiTo:        piActorTo,
			Amount:      moneyPb.AmountINR(1600).GetPb(),
			Interval: &types.Interval{
				StartTime: timestamppb.New(time.Now().Add(time.Hour * 24 * 4)),
				EndTime:   timestamppb.New(time.Now().Add(time.Hour * 24 * 50)),
			},
			RecurrenceRule: &rpPb.RecurrenceRule{
				AllowedFrequency: rpPb.AllowedFrequency_MONTHLY,
			},
			MaximumAllowedTxns: 10,
			PartnerBank:        commonvgpb.Vendor_FEDERAL_BANK,
			Ownership:          rpPb.RecurringPaymentOwnership_EPIFI_TECH,
			Provenance:         rpPb.RecurrencePaymentProvenance_USER_APP,
			UiEntryPoint:       rpPb.UIEntryPoint_SALARY_LITE,
			AmountType:         rpPb.AmountType_EXACT,
		},
		RecurringPaymentTypeSpecificPayload: &payloadPb.RecurringPaymentTypeSpecificCreationPayload{
			Payload: &payloadPb.RecurringPaymentTypeSpecificCreationPayload_EnachCreationPayload{
				EnachCreationPayload: &payloadPb.EnachCreationPayload{
					AuthorisationMode: enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
				},
			},
		},
	}

	createRecurringPaymentReq2 := &rpPb.CreateRecurringPaymentV1Request{
		HardPaymentRoute: rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE,
		ClientRequestId:  clientRequestId,
		CurrentActorId:   toActorId,
		RecurringPaymentDetails: &rpPb.RecurringPaymentCreationDetails{
			FromActorId: fromActorId,
			ToActorId:   toActorId,
			Type:        rpPb.RecurringPaymentType_ENACH_MANDATES,
			PiFrom:      piActorFrom,
			PiTo:        piActorTo,
			Amount:      moneyPb.AmountINR(1600).GetPb(),
			Interval: &types.Interval{
				StartTime: timestamppb.New(time.Now().Add(time.Hour * 24 * 4)),
				EndTime:   timestamppb.New(time.Now().Add(time.Hour * 24 * 50)),
			},
			RecurrenceRule: &rpPb.RecurrenceRule{
				AllowedFrequency: rpPb.AllowedFrequency_MONTHLY,
			},
			MaximumAllowedTxns: 10,
			PartnerBank:        commonvgpb.Vendor_FEDERAL_BANK,
			Ownership:          rpPb.RecurringPaymentOwnership_EPIFI_TECH,
			Provenance:         rpPb.RecurrencePaymentProvenance_USER_APP,
			UiEntryPoint:       rpPb.UIEntryPoint_SALARY_LITE,
			AmountType:         rpPb.AmountType_EXACT,
		},
		RecurringPaymentTypeSpecificPayload: &payloadPb.RecurringPaymentTypeSpecificCreationPayload{
			Payload: &payloadPb.RecurringPaymentTypeSpecificCreationPayload_EnachCreationPayload{
				EnachCreationPayload: &payloadPb.EnachCreationPayload{
					AuthorisationMode: enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
				},
			},
		},
	}

	createRecurringPaymentReq3 := &rpPb.CreateRecurringPaymentV1Request{
		ClientRequestId: clientRequestId2,
		CurrentActorId:  toActorId,
		RecurringPaymentDetails: &rpPb.RecurringPaymentCreationDetails{
			FromActorId: fromActorId,
			ToActorId:   toActorId,
			Type:        rpPb.RecurringPaymentType_ENACH_MANDATES,
			PiFrom:      piActorFrom,
			PiTo:        piActorTo2,
			Amount:      moneyPb.AmountINR(1600).GetPb(),
			Interval: &types.Interval{
				StartTime: timestamppb.New(time.Now().Add(time.Hour * 24 * 4)),
				EndTime:   timestamppb.New(time.Now().Add(time.Hour * 24 * 50)),
			},
			RecurrenceRule: &rpPb.RecurrenceRule{
				AllowedFrequency: rpPb.AllowedFrequency_MONTHLY,
			},
			MaximumAllowedTxns: 10,
			PartnerBank:        commonvgpb.Vendor_FEDERAL_BANK,
			Ownership:          rpPb.RecurringPaymentOwnership_EPIFI_TECH,
			Provenance:         rpPb.RecurrencePaymentProvenance_USER_APP,
			UiEntryPoint:       rpPb.UIEntryPoint_SALARY_LITE,
			AmountType:         rpPb.AmountType_EXACT,
		},
		HardPaymentRoute:           rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_EXTERNAL,
		PostCreationDomainDeeplink: baseprovider.GetLoanDashboardScreenDeepLink(),
	}

	recurringPayment := &rpPb.RecurringPayment{
		FromActorId:        createRecurringPaymentReq1.GetRecurringPaymentDetails().GetFromActorId(),
		ToActorId:          createRecurringPaymentReq1.GetRecurringPaymentDetails().GetToActorId(),
		Type:               createRecurringPaymentReq1.GetRecurringPaymentDetails().GetType(),
		PiFrom:             createRecurringPaymentReq1.GetRecurringPaymentDetails().GetPiFrom(),
		PiTo:               createRecurringPaymentReq1.GetRecurringPaymentDetails().GetPiTo(),
		Amount:             createRecurringPaymentReq1.GetRecurringPaymentDetails().GetAmount(),
		Interval:           createRecurringPaymentReq1.GetRecurringPaymentDetails().GetInterval(),
		RecurrenceRule:     createRecurringPaymentReq1.GetRecurringPaymentDetails().GetRecurrenceRule(),
		MaximumAllowedTxns: createRecurringPaymentReq1.GetRecurringPaymentDetails().GetMaximumAllowedTxns(),
		PartnerBank:        createRecurringPaymentReq1.GetRecurringPaymentDetails().GetPartnerBank(),
		State:              rpPb.RecurringPaymentState_CREATION_QUEUED,
		Ownership:          createRecurringPaymentReq1.GetRecurringPaymentDetails().GetOwnership(),
		Provenance:         createRecurringPaymentReq1.GetRecurringPaymentDetails().GetProvenance(),
		UiEntryPoint:       createRecurringPaymentReq1.GetRecurringPaymentDetails().GetUiEntryPoint(),
		InitiatedBy:        rpPb.InitiatedBy_PAYER,
		AmountType:         createRecurringPaymentReq1.GetRecurringPaymentDetails().GetAmountType(),
		ShareToPayee:       createRecurringPaymentReq1.GetRecurringPaymentDetails().GetShareToPayee(),
		PaymentRoute:       rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE,
	}

	pollingScreenOptions, _ := deeplinkV3.GetScreenOptionV2(&rpScreenOptionsPb.RecurringPaymentPollingScreenOptions{
		ClientReqId: clientRequestId,
		PollDelay:   durationpb.New(time.Second * 3),
		PollAttempt: 0,
		PollingText: &deeplink.InfoItemV2{
			Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Processing...."}},
		},
	})

	workflowPayload, payloadMarshallErr := protojson.Marshal(&rpPayloadPb.CreateRecurringPaymentV1WorkflowPayload{
		RecurringPaymentActionId: id,
		RecurringPaymentId:       recurringPaymentId,
		RecurringPaymentTypeSpecificPayload: &payloadPb.RecurringPaymentTypeSpecificCreationPayload{
			Payload: &payloadPb.RecurringPaymentTypeSpecificCreationPayload_EnachCreationPayload{
				EnachCreationPayload: &payloadPb.EnachCreationPayload{
					AuthorisationMode: enachEnumsPb.EnachRegistrationAuthMode_REGISTRATION_AUTH_MODE_NET_BANKING,
				},
			},
		},
	})
	if payloadMarshallErr != nil {
		logger.Panic("failed to generate workflow payload")
	}

	workflowPayload2, payloadMarshallErr2 := protojson.Marshal(&rpPayloadPb.CreateRecurringPaymentV1WorkflowPayload{
		RecurringPaymentActionId: id,
		RecurringPaymentId:       recurringPaymentId2,
	})
	if payloadMarshallErr2 != nil {
		logger.Panic("failed to generate workflow payload")
	}

	initiateWorkflowRequest := &celestial2.InitiateWorkflowRequest{
		Params: &celestial2.WorkflowCreationRequestParams{
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(rpNs.CreateRecurringPaymentV1),
			Payload: workflowPayload,
			ClientReqId: &workflowPb.ClientReqId{
				Id:     clientRequestId,
				Client: workflowPb.Client_RECURRING_PAYMENT,
			},
			Ownership:        commontypes.Ownership_EPIFI_TECH,
			QualityOfService: celestial2.QoS_BEST_EFFORT,
		},
	}
	initiateWorkflowRequest2 := &celestial2.InitiateWorkflowRequest{
		Params: &celestial2.WorkflowCreationRequestParams{
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(rpNs.CreateRecurringPaymentV1),
			Payload: workflowPayload2,
			ClientReqId: &workflowPb.ClientReqId{
				Id:     clientRequestId2,
				Client: workflowPb.Client_RECURRING_PAYMENT,
			},
			Ownership:        commontypes.Ownership_EPIFI_TECH,
			QualityOfService: celestial2.QoS_BEST_EFFORT,
		},
	}
	type args struct {
		ctx context.Context
		req *rpPb.CreateRecurringPaymentV1Request
	}
	var tests = []struct {
		name       string
		args       args
		setupMocks func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockCelestialClient *celestialMocks.MockCelestialClient, mockActorClient *actorPbMocks.MockActorClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockRecurringPaymentVendorDetails *daoMocks.MockRecurringPaymentsVendorDetailsDao)
		want       *rpPb.CreateRecurringPaymentV1Response
		wantErr    bool
	}{
		{
			name: "should return rpc response status internal with debug message if unable to fetch existing recurring payment action by client request id",
			args: args{
				ctx: context.Background(),
				req: &rpPb.CreateRecurringPaymentV1Request{
					ClientRequestId:  clientRequestId,
					HardPaymentRoute: rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE,
				},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockCelestialClient *celestialMocks.MockCelestialClient, mockActorClient *actorPbMocks.MockActorClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockRecurringPaymentVendorDetails *daoMocks.MockRecurringPaymentsVendorDetailsDao) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(ctxWithOwnership, clientRequestId, false).Return(nil, epifierrors.ErrTransient)
			},
			want: &rpPb.CreateRecurringPaymentV1Response{
				Status: rpcPb.StatusInternalWithDebugMsg("error fetching existing recurring payment action entry by clientRequestId"),
			},
			wantErr: false,
		},
		{
			name: "should return rpc response invalid argument with debug message if recurring payment action with given client request-id already exists and the recurring-payment-action is not in create state.",
			args: args{
				ctx: context.Background(),
				req: &rpPb.CreateRecurringPaymentV1Request{
					ClientRequestId:  clientRequestId,
					HardPaymentRoute: rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE,
				},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockCelestialClient *celestialMocks.MockCelestialClient, mockActorClient *actorPbMocks.MockActorClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockRecurringPaymentVendorDetails *daoMocks.MockRecurringPaymentsVendorDetailsDao) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(ctxWithOwnership, clientRequestId, false).Return(&rpPb.RecurringPaymentsAction{
					ClientRequestId: clientRequestId,
					Action:          rpPb.Action_EXECUTE,
				}, nil)
			},
			want: &rpPb.CreateRecurringPaymentV1Response{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("another recurring payment action not related to creation exists with given clientRequestId"),
			},
			wantErr: false,
		},
		{
			name: "should return rpc response status internal with debug message if unable to initial the recurring payment creation v1 workflow again when recurring payment action already in created state",
			args: args{
				ctx: context.Background(),
				req: createRecurringPaymentReq1,
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockCelestialClient *celestialMocks.MockCelestialClient, mockActorClient *actorPbMocks.MockActorClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockRecurringPaymentVendorDetails *daoMocks.MockRecurringPaymentsVendorDetailsDao) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(ctxWithOwnership, clientRequestId, false).Return(&rpPb.RecurringPaymentsAction{
					Id:                 id,
					RecurringPaymentId: recurringPaymentId,
					ClientRequestId:    clientRequestId,
					Action:             rpPb.Action_CREATE,
				}, nil)
				mockCelestialClient.EXPECT().InitiateWorkflow(ctxWithOwnership, initiateWorkflowRequest).Return(nil, errors.New("celestialClient.InitiateWorkflow rpc call failed"))
			},
			want: &rpPb.CreateRecurringPaymentV1Response{
				Status: rpcPb.StatusInternalWithDebugMsg("error initiating recurring payment creation v1 workflow"),
			},
			wantErr: false,
		},
		{
			name: "should initiate the workflow again if recurring payment action already in created state",
			args: args{
				ctx: context.Background(),
				req: createRecurringPaymentReq1,
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockCelestialClient *celestialMocks.MockCelestialClient, mockActorClient *actorPbMocks.MockActorClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockRecurringPaymentVendorDetails *daoMocks.MockRecurringPaymentsVendorDetailsDao) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(ctxWithOwnership, clientRequestId, false).Return(&rpPb.RecurringPaymentsAction{
					Id:                 id,
					RecurringPaymentId: recurringPaymentId,
					ClientRequestId:    clientRequestId,
					Action:             rpPb.Action_CREATE,
				}, nil)
				mockCelestialClient.EXPECT().InitiateWorkflow(ctxWithOwnership, initiateWorkflowRequest).Return(&celestial2.InitiateWorkflowResponse{
					Status: rpcPb.StatusAlreadyExists(),
				}, nil)
			},
			want: &rpPb.CreateRecurringPaymentV1Response{
				Status:             rpcPb.StatusOk(),
				RecurringPaymentId: recurringPaymentId,
				NextActionDeeplink: &deeplink.Deeplink{
					Screen:          deeplink.Screen_RECURRING_PAYMENT_POLLING_SCREEN,
					ScreenOptionsV2: pollingScreenOptions,
				},
			},
			wantErr: false,
		},
		{
			name: "should return rpc response status internal with debug message if unable to check fromActor user has blocked the toActor user",
			args: args{
				ctx: context.Background(),
				req: createRecurringPaymentReq1,
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockCelestialClient *celestialMocks.MockCelestialClient, mockActorClient *actorPbMocks.MockActorClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockRecurringPaymentVendorDetails *daoMocks.MockRecurringPaymentsVendorDetailsDao) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(ctxWithOwnership, clientRequestId, false).Return(nil, epifierrors.ErrRecordNotFound)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: fromActorId,
					OtherActorId:   toActorId,
				}).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:    &rpPb.CreateRecurringPaymentV1Response{Status: rpcPb.StatusInternalWithDebugMsg("error validating recurring payment creation request")},
			wantErr: false,
		},
		{
			name: "should return validation error if fromActor user has blocked the toActor user",
			args: args{
				ctx: context.Background(),
				req: createRecurringPaymentReq1,
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockCelestialClient *celestialMocks.MockCelestialClient, mockActorClient *actorPbMocks.MockActorClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockRecurringPaymentVendorDetails *daoMocks.MockRecurringPaymentsVendorDetailsDao) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(ctxWithOwnership, clientRequestId, false).Return(nil, epifierrors.ErrRecordNotFound)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: fromActorId,
					OtherActorId:   toActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_BLOCKED,
				}, nil)
			},
			want: &rpPb.CreateRecurringPaymentV1Response{
				Status: rpcPb.NewStatusWithoutDebug(uint32(rpPb.CreateRecurringPaymentV1Response_BLOCKED_USER), "User is blocked"),
			},
			wantErr: false,
		},
		{
			name: "should return rpc response status internal with debug message if unable to check toActor user has blocked the fromActor user",
			args: args{
				ctx: context.Background(),
				req: createRecurringPaymentReq1,
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockCelestialClient *celestialMocks.MockCelestialClient, mockActorClient *actorPbMocks.MockActorClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockRecurringPaymentVendorDetails *daoMocks.MockRecurringPaymentsVendorDetailsDao) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(ctxWithOwnership, clientRequestId, false).Return(nil, epifierrors.ErrRecordNotFound)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: fromActorId,
					OtherActorId:   toActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: toActorId,
					OtherActorId:   fromActorId,
				}).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:    &rpPb.CreateRecurringPaymentV1Response{Status: rpcPb.StatusInternalWithDebugMsg("error validating recurring payment creation request")},
			wantErr: false,
		},
		{
			name: "should return validation error if fromActor user has blocked the toActor user",
			args: args{
				ctx: context.Background(),
				req: createRecurringPaymentReq1,
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockCelestialClient *celestialMocks.MockCelestialClient, mockActorClient *actorPbMocks.MockActorClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockRecurringPaymentVendorDetails *daoMocks.MockRecurringPaymentsVendorDetailsDao) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(ctxWithOwnership, clientRequestId, false).Return(nil, epifierrors.ErrRecordNotFound)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: fromActorId,
					OtherActorId:   toActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: toActorId,
					OtherActorId:   fromActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_BLOCKED,
				}, nil)
			},
			want: &rpPb.CreateRecurringPaymentV1Response{
				Status: rpcPb.NewStatusWithoutDebug(uint32(rpPb.CreateRecurringPaymentV1Response_BLOCKED_USER), "User is blocked"),
			},
			wantErr: false,
		},
		{
			name: "should return rpc response with debug message if create recurring payment-v1 request's start and end date is invalid",
			args: args{
				ctx: context.Background(),
				req: &rpPb.CreateRecurringPaymentV1Request{
					ClientRequestId: clientRequestId,
					CurrentActorId:  fromActorId,
					RecurringPaymentDetails: &rpPb.RecurringPaymentCreationDetails{
						FromActorId: fromActorId,
						ToActorId:   toActorId,
						Type:        rpPb.RecurringPaymentType_ENACH_MANDATES,
						PiFrom:      piActorFrom,
						PiTo:        piActorTo,
						Amount:      moneyPb.AmountINR(1600).GetPb(),
						Interval: &types.Interval{
							StartTime: timestamppb.New(time.Now().Add(time.Hour * 24 * 51)),
							EndTime:   timestamppb.New(time.Now().Add(time.Hour * 24 * 50)),
						},
						RecurrenceRule: &rpPb.RecurrenceRule{
							AllowedFrequency: rpPb.AllowedFrequency_MONTHLY,
						},
						MaximumAllowedTxns: 10,
						PartnerBank:        commonvgpb.Vendor_FEDERAL_BANK,
						Ownership:          rpPb.RecurringPaymentOwnership_EPIFI_TECH,
						Provenance:         rpPb.RecurrencePaymentProvenance_USER_APP,
						UiEntryPoint:       rpPb.UIEntryPoint_SALARY_LITE,
						AmountType:         rpPb.AmountType_EXACT,
					},
					RecurringPaymentTypeSpecificPayload: createRecurringPaymentReq1.GetRecurringPaymentTypeSpecificPayload(),
					HardPaymentRoute:                    rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE,
				},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockCelestialClient *celestialMocks.MockCelestialClient, mockActorClient *actorPbMocks.MockActorClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockRecurringPaymentVendorDetails *daoMocks.MockRecurringPaymentsVendorDetailsDao) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(ctxWithOwnership, clientRequestId, false).Return(nil, epifierrors.ErrRecordNotFound)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: fromActorId,
					OtherActorId:   toActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: toActorId,
					OtherActorId:   fromActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
			},
			want: &rpPb.CreateRecurringPaymentV1Response{
				Status: rpcPb.NewStatusWithoutDebug(uint32(rpPb.CreateRecurringPaymentV1Response_INVALID_START_AND_END_DATE), "Invalid start and end date for recurring payment"),
			},
			wantErr: false,
		},
		{
			name: "should return FailedPrecondition if the payer-payee actor/pi combination is same, i.e. trying to setup recurring payment from the same account for receiving",
			args: args{
				ctx: context.Background(),
				req: &rpPb.CreateRecurringPaymentV1Request{
					ClientRequestId: clientRequestId,
					CurrentActorId:  fromActorId,
					RecurringPaymentDetails: &rpPb.RecurringPaymentCreationDetails{
						FromActorId: "actor-id",
						ToActorId:   "actor-id",
						Type:        rpPb.RecurringPaymentType_ENACH_MANDATES,
						PiFrom:      "pi-id-1",
						PiTo:        "pi-id-1",
						Amount:      moneyPb.AmountINR(1600).GetPb(),
						Interval: &types.Interval{
							StartTime: timestamppb.New(time.Now().Add(time.Hour * 24 * 51)),
							EndTime:   timestamppb.New(time.Now().Add(time.Hour * 24 * 50)),
						},
						RecurrenceRule: &rpPb.RecurrenceRule{
							AllowedFrequency: rpPb.AllowedFrequency_MONTHLY,
						},
						MaximumAllowedTxns: 10,
						PartnerBank:        commonvgpb.Vendor_FEDERAL_BANK,
						Ownership:          rpPb.RecurringPaymentOwnership_EPIFI_TECH,
						Provenance:         rpPb.RecurrencePaymentProvenance_USER_APP,
						UiEntryPoint:       rpPb.UIEntryPoint_SALARY_LITE,
						AmountType:         rpPb.AmountType_EXACT,
					},
					RecurringPaymentTypeSpecificPayload: createRecurringPaymentReq1.GetRecurringPaymentTypeSpecificPayload(),
					HardPaymentRoute:                    rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE,
				},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockCelestialClient *celestialMocks.MockCelestialClient, mockActorClient *actorPbMocks.MockActorClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockRecurringPaymentVendorDetails *daoMocks.MockRecurringPaymentsVendorDetailsDao) {
			},
			want: &rpPb.CreateRecurringPaymentV1Response{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("recurring payment creation not initiated by either payer or payee"),
			},
			wantErr: false,
		},
		{
			name: "should return rpc response with debug message if create recurring payment-v1 request's start and end date is invalid (start date is before the current date)",
			args: args{
				ctx: context.Background(),
				req: &rpPb.CreateRecurringPaymentV1Request{
					ClientRequestId: clientRequestId,
					CurrentActorId:  fromActorId,
					RecurringPaymentDetails: &rpPb.RecurringPaymentCreationDetails{
						FromActorId: fromActorId,
						ToActorId:   toActorId,
						Type:        rpPb.RecurringPaymentType_ENACH_MANDATES,
						PiFrom:      piActorFrom,
						PiTo:        piActorTo,
						Amount:      moneyPb.AmountINR(1600).GetPb(),
						Interval: &types.Interval{
							StartTime: timestamppb.New(time.Now().Add(time.Hour * 24 * -1)),
							EndTime:   timestamppb.New(time.Now().Add(time.Hour * 24 * 50)),
						},
						RecurrenceRule: &rpPb.RecurrenceRule{
							AllowedFrequency: rpPb.AllowedFrequency_MONTHLY,
						},
						MaximumAllowedTxns: 10,
						PartnerBank:        commonvgpb.Vendor_FEDERAL_BANK,
						Ownership:          rpPb.RecurringPaymentOwnership_EPIFI_TECH,
						Provenance:         rpPb.RecurrencePaymentProvenance_USER_APP,
						UiEntryPoint:       rpPb.UIEntryPoint_SALARY_LITE,
						AmountType:         rpPb.AmountType_EXACT,
					},
					RecurringPaymentTypeSpecificPayload: createRecurringPaymentReq1.GetRecurringPaymentTypeSpecificPayload(),
					HardPaymentRoute:                    rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE,
				},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockCelestialClient *celestialMocks.MockCelestialClient, mockActorClient *actorPbMocks.MockActorClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockRecurringPaymentVendorDetails *daoMocks.MockRecurringPaymentsVendorDetailsDao) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(ctxWithOwnership, clientRequestId, false).Return(nil, epifierrors.ErrRecordNotFound)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: fromActorId,
					OtherActorId:   toActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: toActorId,
					OtherActorId:   fromActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
			},
			want: &rpPb.CreateRecurringPaymentV1Response{
				Status: rpcPb.NewStatusWithoutDebug(uint32(rpPb.CreateRecurringPaymentV1Response_INVALID_START_AND_END_DATE), "Invalid start and end date for recurring payment"),
			},
			wantErr: false,
		},
		{
			name: "should return rpc response status internal with debug message if unable to fetch enach domain creation processor",
			args: args{
				ctx: context.Background(),
				req: createRecurringPaymentReq1,
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockCelestialClient *celestialMocks.MockCelestialClient, mockActorClient *actorPbMocks.MockActorClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockRecurringPaymentVendorDetails *daoMocks.MockRecurringPaymentsVendorDetailsDao) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(ctxWithOwnership, clientRequestId, false).Return(nil, epifierrors.ErrRecordNotFound)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: fromActorId,
					OtherActorId:   toActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: toActorId,
					OtherActorId:   fromActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockDomainCreationProcessor.EXPECT().ValidateCreation(ctxWithOwnership, &domainCreationProcessor.ValidateCreationReq{
					StartTime:              createRecurringPaymentReq1.GetRecurringPaymentDetails().GetInterval().GetStartTime().AsTime(),
					EndTime:                createRecurringPaymentReq1.GetRecurringPaymentDetails().GetInterval().GetEndTime().AsTime(),
					AllowedFrequency:       createRecurringPaymentReq1.GetRecurringPaymentDetails().GetRecurrenceRule().GetAllowedFrequency(),
					RecurringPaymentAmount: createRecurringPaymentReq1.GetRecurringPaymentDetails().GetAmount(),
				}).Return(epifierrors.ErrTransient)
			},
			want:    &rpPb.CreateRecurringPaymentV1Response{Status: rpcPb.StatusInternalWithDebugMsg("error validating recurring payment creation request")},
			wantErr: false,
		},
		{
			name: "should return rpc response status internal with debug message if fails to create the recurring payment entry in the database",
			args: args{
				ctx: context.Background(),
				req: createRecurringPaymentReq1,
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockCelestialClient *celestialMocks.MockCelestialClient, mockActorClient *actorPbMocks.MockActorClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockRecurringPaymentVendorDetails *daoMocks.MockRecurringPaymentsVendorDetailsDao) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(ctxWithOwnership, clientRequestId, false).Return(nil, epifierrors.ErrRecordNotFound)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: fromActorId,
					OtherActorId:   toActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: toActorId,
					OtherActorId:   fromActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockDomainCreationProcessor.EXPECT().ValidateCreation(ctxWithOwnership, &domainCreationProcessor.ValidateCreationReq{
					StartTime:              createRecurringPaymentReq1.GetRecurringPaymentDetails().GetInterval().GetStartTime().AsTime(),
					EndTime:                createRecurringPaymentReq1.GetRecurringPaymentDetails().GetInterval().GetEndTime().AsTime(),
					AllowedFrequency:       createRecurringPaymentReq1.GetRecurringPaymentDetails().GetRecurrenceRule().GetAllowedFrequency(),
					RecurringPaymentAmount: createRecurringPaymentReq1.GetRecurringPaymentDetails().GetAmount(),
				})
				mockRecurringPaymentDao.EXPECT().Create(gomock.Any(), recurringPayment, commontypes.Ownership_EPIFI_TECH).Return(nil, epifierrors.ErrTransient)
			},
			want: &rpPb.CreateRecurringPaymentV1Response{
				Status: rpcPb.StatusInternalWithDebugMsg("txn error while creating recurring payment and recurring payment action in db"),
			},
			wantErr: false,
		},
		{
			name: "should return rpc response status internal with debug message if fails to create the recurring payment action entry in the database",
			args: args{
				ctx: context.Background(),
				req: createRecurringPaymentReq1,
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockCelestialClient *celestialMocks.MockCelestialClient, mockActorClient *actorPbMocks.MockActorClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockRecurringPaymentVendorDetails *daoMocks.MockRecurringPaymentsVendorDetailsDao) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(ctxWithOwnership, clientRequestId, false).Return(nil, epifierrors.ErrRecordNotFound)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: fromActorId,
					OtherActorId:   toActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: toActorId,
					OtherActorId:   fromActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockDomainCreationProcessor.EXPECT().ValidateCreation(ctxWithOwnership, &domainCreationProcessor.ValidateCreationReq{
					StartTime:              createRecurringPaymentReq1.GetRecurringPaymentDetails().GetInterval().GetStartTime().AsTime(),
					EndTime:                createRecurringPaymentReq1.GetRecurringPaymentDetails().GetInterval().GetEndTime().AsTime(),
					AllowedFrequency:       createRecurringPaymentReq1.GetRecurringPaymentDetails().GetRecurrenceRule().GetAllowedFrequency(),
					RecurringPaymentAmount: createRecurringPaymentReq1.GetRecurringPaymentDetails().GetAmount(),
				})
				mockRecurringPaymentDao.EXPECT().Create(gomock.Any(), recurringPayment, commontypes.Ownership_EPIFI_TECH).Return(&rpPb.RecurringPayment{
					Id:           recurringPaymentId,
					PaymentRoute: rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE,
				}, nil)
				mockRecurringPaymentActionDao.EXPECT().Create(gomock.Any(), &rpPb.RecurringPaymentsAction{
					RecurringPaymentId: recurringPaymentId,
					ClientRequestId:    createRecurringPaymentReq1.GetClientRequestId(),
					Action:             rpPb.Action_CREATE,
					State:              rpPb.ActionState_ACTION_CREATED,
					ExpireAt:           createRecurringPaymentReq1.GetExpiry(),
					InitiatedBy:        rpPb.InitiatedBy_PAYER,
				}).Return(nil, epifierrors.ErrTransient)
			},
			want: &rpPb.CreateRecurringPaymentV1Response{
				Status: rpcPb.StatusInternalWithDebugMsg("txn error while creating recurring payment and recurring payment action in db"),
			},
			wantErr: false,
		},
		{
			name: "should return rpc response status internal with debug message if unable to initiate recurring payment creation v1 workflow after creating the transaction",
			args: args{
				ctx: context.Background(),
				req: createRecurringPaymentReq1,
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockCelestialClient *celestialMocks.MockCelestialClient, mockActorClient *actorPbMocks.MockActorClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockRecurringPaymentVendorDetails *daoMocks.MockRecurringPaymentsVendorDetailsDao) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(ctxWithOwnership, clientRequestId, false).Return(nil, epifierrors.ErrRecordNotFound)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: fromActorId,
					OtherActorId:   toActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: toActorId,
					OtherActorId:   fromActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockDomainCreationProcessor.EXPECT().ValidateCreation(ctxWithOwnership, &domainCreationProcessor.ValidateCreationReq{
					StartTime:              createRecurringPaymentReq1.GetRecurringPaymentDetails().GetInterval().GetStartTime().AsTime(),
					EndTime:                createRecurringPaymentReq1.GetRecurringPaymentDetails().GetInterval().GetEndTime().AsTime(),
					AllowedFrequency:       createRecurringPaymentReq1.GetRecurringPaymentDetails().GetRecurrenceRule().GetAllowedFrequency(),
					RecurringPaymentAmount: createRecurringPaymentReq1.GetRecurringPaymentDetails().GetAmount(),
				})
				mockRecurringPaymentDao.EXPECT().Create(gomock.Any(), recurringPayment, commontypes.Ownership_EPIFI_TECH).Return(&rpPb.RecurringPayment{
					Id:           recurringPaymentId,
					PaymentRoute: rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE,
				}, nil)
				mockRecurringPaymentActionDao.EXPECT().Create(gomock.Any(), &rpPb.RecurringPaymentsAction{
					RecurringPaymentId: recurringPaymentId,
					ClientRequestId:    createRecurringPaymentReq1.GetClientRequestId(),
					Action:             rpPb.Action_CREATE,
					State:              rpPb.ActionState_ACTION_CREATED,
					ExpireAt:           createRecurringPaymentReq1.GetExpiry(),
					InitiatedBy:        rpPb.InitiatedBy_PAYER,
				}).Return(&rpPb.RecurringPaymentsAction{
					Id:                 id,
					RecurringPaymentId: recurringPaymentId,
					ClientRequestId:    clientRequestId,
					Action:             rpPb.Action_CREATE,
				}, nil)
				mockCelestialClient.EXPECT().InitiateWorkflow(ctxWithOwnership, initiateWorkflowRequest).Return(nil, epifierrors.ErrTransient)
			},
			want:    &rpPb.CreateRecurringPaymentV1Response{Status: rpcPb.StatusInternalWithDebugMsg("error initiating recurring payment creation v1 workflow")},
			wantErr: false,
		},
		{
			name: "enach creation happy flow, considering fromActorId as currentActorId",
			args: args{
				ctx: context.Background(),
				req: createRecurringPaymentReq1,
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockCelestialClient *celestialMocks.MockCelestialClient, mockActorClient *actorPbMocks.MockActorClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockRecurringPaymentVendorDetails *daoMocks.MockRecurringPaymentsVendorDetailsDao) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(ctxWithOwnership, clientRequestId, false).Return(nil, epifierrors.ErrRecordNotFound)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: fromActorId,
					OtherActorId:   toActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: toActorId,
					OtherActorId:   fromActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockDomainCreationProcessor.EXPECT().ValidateCreation(ctxWithOwnership, &domainCreationProcessor.ValidateCreationReq{
					StartTime:              createRecurringPaymentReq1.GetRecurringPaymentDetails().GetInterval().GetStartTime().AsTime(),
					EndTime:                createRecurringPaymentReq1.GetRecurringPaymentDetails().GetInterval().GetEndTime().AsTime(),
					AllowedFrequency:       createRecurringPaymentReq1.GetRecurringPaymentDetails().GetRecurrenceRule().GetAllowedFrequency(),
					RecurringPaymentAmount: createRecurringPaymentReq1.GetRecurringPaymentDetails().GetAmount(),
				})
				mockRecurringPaymentDao.EXPECT().Create(gomock.Any(), recurringPayment, commontypes.Ownership_EPIFI_TECH).Return(&rpPb.RecurringPayment{
					Id:           recurringPaymentId,
					PaymentRoute: rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE,
				}, nil)
				mockRecurringPaymentActionDao.EXPECT().Create(gomock.Any(), &rpPb.RecurringPaymentsAction{
					RecurringPaymentId: recurringPaymentId,
					ClientRequestId:    createRecurringPaymentReq1.GetClientRequestId(),
					Action:             rpPb.Action_CREATE,
					State:              rpPb.ActionState_ACTION_CREATED,
					ExpireAt:           createRecurringPaymentReq1.GetExpiry(),
					InitiatedBy:        rpPb.InitiatedBy_PAYER,
				}).Return(&rpPb.RecurringPaymentsAction{
					Id:                 id,
					RecurringPaymentId: recurringPaymentId,
					ClientRequestId:    clientRequestId,
					Action:             rpPb.Action_CREATE,
				}, nil)
				mockCelestialClient.EXPECT().InitiateWorkflow(ctxWithOwnership, initiateWorkflowRequest).Return(&celestial2.InitiateWorkflowResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
			},
			want: &rpPb.CreateRecurringPaymentV1Response{
				Status:             rpcPb.StatusOk(),
				RecurringPaymentId: recurringPaymentId,
				NextActionDeeplink: &deeplink.Deeplink{
					Screen:          deeplink.Screen_RECURRING_PAYMENT_POLLING_SCREEN,
					ScreenOptionsV2: pollingScreenOptions,
				},
			},
			wantErr: false,
		},
		{
			name: "enach creation happy flow, considering toActorId as currentActorId",
			args: args{
				ctx: context.Background(),
				req: createRecurringPaymentReq2,
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockCelestialClient *celestialMocks.MockCelestialClient, mockActorClient *actorPbMocks.MockActorClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockRecurringPaymentVendorDetails *daoMocks.MockRecurringPaymentsVendorDetailsDao) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(ctxWithOwnership, clientRequestId, false).Return(nil, epifierrors.ErrRecordNotFound)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: fromActorId,
					OtherActorId:   toActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActorClient.EXPECT().GetRelationshipWithActor(ctxWithOwnership, &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: toActorId,
					OtherActorId:   fromActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockDomainCreationProcessor.EXPECT().ValidateCreation(ctxWithOwnership, &domainCreationProcessor.ValidateCreationReq{
					StartTime:              createRecurringPaymentReq2.GetRecurringPaymentDetails().GetInterval().GetStartTime().AsTime(),
					EndTime:                createRecurringPaymentReq2.GetRecurringPaymentDetails().GetInterval().GetEndTime().AsTime(),
					AllowedFrequency:       createRecurringPaymentReq2.GetRecurringPaymentDetails().GetRecurrenceRule().GetAllowedFrequency(),
					RecurringPaymentAmount: createRecurringPaymentReq2.GetRecurringPaymentDetails().GetAmount(),
				})
				mockRecurringPaymentDao.EXPECT().Create(gomock.Any(), &rpPb.RecurringPayment{
					FromActorId:        createRecurringPaymentReq2.GetRecurringPaymentDetails().GetFromActorId(),
					ToActorId:          createRecurringPaymentReq2.GetRecurringPaymentDetails().GetToActorId(),
					Type:               createRecurringPaymentReq2.GetRecurringPaymentDetails().GetType(),
					PiFrom:             createRecurringPaymentReq2.GetRecurringPaymentDetails().GetPiFrom(),
					PiTo:               createRecurringPaymentReq2.GetRecurringPaymentDetails().GetPiTo(),
					Amount:             createRecurringPaymentReq2.GetRecurringPaymentDetails().GetAmount(),
					Interval:           createRecurringPaymentReq2.GetRecurringPaymentDetails().GetInterval(),
					RecurrenceRule:     createRecurringPaymentReq2.GetRecurringPaymentDetails().GetRecurrenceRule(),
					MaximumAllowedTxns: createRecurringPaymentReq2.GetRecurringPaymentDetails().GetMaximumAllowedTxns(),
					PartnerBank:        createRecurringPaymentReq2.GetRecurringPaymentDetails().GetPartnerBank(),
					State:              rpPb.RecurringPaymentState_CREATION_QUEUED,
					Ownership:          createRecurringPaymentReq2.GetRecurringPaymentDetails().GetOwnership(),
					Provenance:         createRecurringPaymentReq2.GetRecurringPaymentDetails().GetProvenance(),
					UiEntryPoint:       createRecurringPaymentReq2.GetRecurringPaymentDetails().GetUiEntryPoint(),
					InitiatedBy:        rpPb.InitiatedBy_PAYEE,
					AmountType:         createRecurringPaymentReq2.GetRecurringPaymentDetails().GetAmountType(),
					ShareToPayee:       createRecurringPaymentReq2.GetRecurringPaymentDetails().GetShareToPayee(),
					PaymentRoute:       rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE,
				}, commontypes.Ownership_EPIFI_TECH).Return(&rpPb.RecurringPayment{
					Id: recurringPaymentId,
				}, nil)
				mockRecurringPaymentActionDao.EXPECT().Create(gomock.Any(), &rpPb.RecurringPaymentsAction{
					RecurringPaymentId: recurringPaymentId,
					ClientRequestId:    createRecurringPaymentReq2.GetClientRequestId(),
					Action:             rpPb.Action_CREATE,
					State:              rpPb.ActionState_ACTION_CREATED,
					ExpireAt:           createRecurringPaymentReq2.GetExpiry(),
					InitiatedBy:        rpPb.InitiatedBy_PAYEE,
				}).Return(&rpPb.RecurringPaymentsAction{
					Id:                 id,
					RecurringPaymentId: recurringPaymentId,
					ClientRequestId:    clientRequestId,
					Action:             rpPb.Action_CREATE,
				}, nil)
				mockCelestialClient.EXPECT().InitiateWorkflow(ctxWithOwnership, initiateWorkflowRequest).Return(&celestial2.InitiateWorkflowResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
			},
			want: &rpPb.CreateRecurringPaymentV1Response{
				Status:             rpcPb.StatusOk(),
				RecurringPaymentId: recurringPaymentId,
				NextActionDeeplink: &deeplink.Deeplink{
					Screen:          deeplink.Screen_RECURRING_PAYMENT_POLLING_SCREEN,
					ScreenOptionsV2: pollingScreenOptions,
				},
			},
			wantErr: false,
		},
		{
			name: "successful recurring payment registration using payment gateway",
			args: args{
				ctx: context.Background(),
				req: createRecurringPaymentReq3,
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockCelestialClient *celestialMocks.MockCelestialClient, mockActorClient *actorPbMocks.MockActorClient, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockRecurringPaymentVendorDetails *daoMocks.MockRecurringPaymentsVendorDetailsDao) {
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), clientRequestId2, false).Return(nil, epifierrors.ErrRecordNotFound)
				mockActorClient.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: fromActorId,
					OtherActorId:   toActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockActorClient.EXPECT().GetRelationshipWithActor(gomock.Any(), &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: toActorId,
					OtherActorId:   fromActorId,
				}).Return(&actorPb.GetRelationshipWithActorResponse{
					Status:       rpcPb.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				}, nil)
				mockDomainCreationProcessor.EXPECT().ValidateCreation(gomock.Any(), &domainCreationProcessor.ValidateCreationReq{
					StartTime:              createRecurringPaymentReq3.GetRecurringPaymentDetails().GetInterval().GetStartTime().AsTime(),
					EndTime:                createRecurringPaymentReq3.GetRecurringPaymentDetails().GetInterval().GetEndTime().AsTime(),
					AllowedFrequency:       createRecurringPaymentReq3.GetRecurringPaymentDetails().GetRecurrenceRule().GetAllowedFrequency(),
					RecurringPaymentAmount: createRecurringPaymentReq3.GetRecurringPaymentDetails().GetAmount(),
				})
				mockRecurringPaymentDao.EXPECT().Create(gomock.Any(), &rpPb.RecurringPayment{
					FromActorId:        createRecurringPaymentReq3.GetRecurringPaymentDetails().GetFromActorId(),
					ToActorId:          createRecurringPaymentReq3.GetRecurringPaymentDetails().GetToActorId(),
					Type:               createRecurringPaymentReq3.GetRecurringPaymentDetails().GetType(),
					PiFrom:             createRecurringPaymentReq3.GetRecurringPaymentDetails().GetPiFrom(),
					PiTo:               createRecurringPaymentReq3.GetRecurringPaymentDetails().GetPiTo(),
					Amount:             createRecurringPaymentReq3.GetRecurringPaymentDetails().GetAmount(),
					Interval:           createRecurringPaymentReq3.GetRecurringPaymentDetails().GetInterval(),
					RecurrenceRule:     createRecurringPaymentReq3.GetRecurringPaymentDetails().GetRecurrenceRule(),
					MaximumAllowedTxns: createRecurringPaymentReq3.GetRecurringPaymentDetails().GetMaximumAllowedTxns(),
					PartnerBank:        createRecurringPaymentReq3.GetRecurringPaymentDetails().GetPartnerBank(),
					State:              rpPb.RecurringPaymentState_CREATION_QUEUED,
					Ownership:          createRecurringPaymentReq3.GetRecurringPaymentDetails().GetOwnership(),
					Provenance:         createRecurringPaymentReq3.GetRecurringPaymentDetails().GetProvenance(),
					UiEntryPoint:       createRecurringPaymentReq3.GetRecurringPaymentDetails().GetUiEntryPoint(),
					InitiatedBy:        rpPb.InitiatedBy_PAYEE,
					AmountType:         createRecurringPaymentReq3.GetRecurringPaymentDetails().GetAmountType(),
					ShareToPayee:       createRecurringPaymentReq3.GetRecurringPaymentDetails().GetShareToPayee(),
					PaymentRoute:       rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_EXTERNAL,
				}, commontypes.Ownership_EPIFI_TECH).Return(&rpPb.RecurringPayment{
					Id: recurringPaymentId2,
				}, nil)
				mockRecurringPaymentVendorDetails.EXPECT().Create(gomock.Any(), &rpPb.RecurringPaymentsVendorDetails{
					ActorId:            toActorId,
					RecurringPaymentId: recurringPaymentId2,
					Vendor:             commonvgpb.Vendor_RAZORPAY,
				}).Return(&rpPb.RecurringPaymentsVendorDetails{
					ActorId:            toActorId,
					RecurringPaymentId: recurringPaymentId2,
					Vendor:             commonvgpb.Vendor_RAZORPAY,
				}, nil)
				mockRecurringPaymentActionDao.EXPECT().Create(gomock.Any(), &rpPb.RecurringPaymentsAction{
					RecurringPaymentId: recurringPaymentId2,
					ClientRequestId:    createRecurringPaymentReq3.GetClientRequestId(),
					Action:             rpPb.Action_CREATE,
					State:              rpPb.ActionState_ACTION_CREATED,
					ExpireAt:           createRecurringPaymentReq3.GetExpiry(),
					InitiatedBy:        rpPb.InitiatedBy_PAYEE,
					ActionMetadata: &rpPb.ActionMetadata{
						CreateActionMetadata: &rpPb.CreateActionMetadata{PostCreationDomainDeeplink: baseprovider.GetLoanDashboardScreenDeepLink()},
					},
				}).Return(&rpPb.RecurringPaymentsAction{
					Id:                 id,
					RecurringPaymentId: recurringPaymentId2,
					ClientRequestId:    clientRequestId2,
					Action:             rpPb.Action_CREATE,
				}, nil)
				mockCelestialClient.EXPECT().InitiateWorkflow(gomock.Any(), initiateWorkflowRequest2).Return(&celestial2.InitiateWorkflowResponse{
					Status: rpcPb.StatusOk(),
				}, nil)

			},
			want: &rpPb.CreateRecurringPaymentV1Response{
				Status:             rpcPb.StatusOk(),
				RecurringPaymentId: recurringPaymentId2,
				NextActionDeeplink: &deeplink.Deeplink{
					Screen:          deeplink.Screen_RECURRING_PAYMENT_POLLING_SCREEN,
					ScreenOptionsV2: pollingScreenOptions,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
			mockRecurringPaymentActionDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
			mockPiClient := mocks.NewMockPiClient(ctr)
			mockCelestialClient := celestialMocks.NewMockCelestialClient(ctr)
			mockActorClient := actorPbMocks.NewMockActorClient(ctr)
			mockDomainCreationProcessorFactory := domainCreationProcessorMocks.NewMockDomainCreationProcessorFactory(ctr)
			mockDomainCreationProcessor := domainCreationProcessorMocks.NewMockDomainCreationProcessorV1(ctr)
			mockDomainCreationProcessorFactory.EXPECT().GetProcessor(gomock.Any(), gomock.Any()).Return(mockDomainCreationProcessor, nil).AnyTimes()
			mockRecurringPaymentVendorDetails := daoMocks.NewMockRecurringPaymentsVendorDetailsDao(ctr)

			tt.setupMocks(mockRecurringPaymentDao, mockRecurringPaymentActionDao, mockCelestialClient, mockActorClient, mockDomainCreationProcessor, mockRecurringPaymentVendorDetails)

			s := &Service{
				recurringPaymentDao:            mockRecurringPaymentDao,
				recurringPaymentActionsDao:     mockRecurringPaymentActionDao,
				celestialClient:                mockCelestialClient,
				actorClient:                    mockActorClient,
				domainCreationProcessorFactory: mockDomainCreationProcessorFactory,
				piClient:                       mockPiClient,
				rpVendorDetailsDao:             mockRecurringPaymentVendorDetails,
				txnExecutorProvider: storageV2Mocks.NewPassThroughMockIdempotentTxnExecutorProvider(map[commontypes.Ownership]*cfg.DB{
					commontypes.Ownership_EPIFI_TECH: {},
				}),
			}
			got, err := s.CreateRecurringPaymentV1(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateRecurringPaymentV1() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("CreateRecurringPaymentV1() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_getPaymentRoute(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockAccountPiRelationClient := mocks2.NewMockAccountPIRelationClient(ctrl)
	mockSavingsClient := mocks3.NewMockSavingsClient(ctrl)
	txnExecProvider := storageV2Mocks.NewPassThroughMockIdempotentTxnExecutorProvider(map[commontypes.Ownership]*cfg.DB{
		commontypes.Ownership_EPIFI_TECH: {},
	})
	s := &Service{
		accountPIRelationsClient: mockAccountPiRelationClient,
		savingsClient:            mockSavingsClient,
		txnExecutorProvider:      txnExecProvider,
	}

	tests := []struct {
		name          string
		req           *rpPb.CreateRecurringPaymentV1Request
		initiatedBy   rpPb.InitiatedBy
		setupMocks    func()
		expectedRoute rpPb.RecurringPaymentRoute
		expectedError error
	}{
		{
			name: "HardPaymentRoute is NATIVE",
			req: &rpPb.CreateRecurringPaymentV1Request{
				HardPaymentRoute: rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE,
			},
			initiatedBy:   rpPb.InitiatedBy_PAYER,
			setupMocks:    func() {},
			expectedRoute: rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE,
			expectedError: nil,
		},
		{
			name: "HardPaymentRoute is EXTERNAL",
			req: &rpPb.CreateRecurringPaymentV1Request{
				HardPaymentRoute: rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_EXTERNAL,
				RecurringPaymentDetails: &rpPb.RecurringPaymentCreationDetails{
					PiTo: "paymentinstrument-creditcard-federal-pool-account-1",
				},
			},
			initiatedBy:   rpPb.InitiatedBy_PAYER,
			setupMocks:    func() {},
			expectedRoute: rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_EXTERNAL,
			expectedError: nil,
		},
		{
			name: "No HardPaymentRoute, Type is STANDING_INSTRUCTION",
			req: &rpPb.CreateRecurringPaymentV1Request{
				RecurringPaymentDetails: &rpPb.RecurringPaymentCreationDetails{
					Type: rpPb.RecurringPaymentType_STANDING_INSTRUCTION,
				},
			},
			initiatedBy:   rpPb.InitiatedBy_PAYER,
			setupMocks:    func() {},
			expectedRoute: rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE,
			expectedError: nil,
		},
		{
			name: "No HardPaymentRoute, Type is ENACH_MANDATES and the pi to is not a savings account pi",
			req: &rpPb.CreateRecurringPaymentV1Request{
				RecurringPaymentDetails: &rpPb.RecurringPaymentCreationDetails{
					Type:   rpPb.RecurringPaymentType_ENACH_MANDATES,
					PiFrom: "pi-from-1",
					PiTo:   "paymentinstrument-creditcard-federal-pool-account-1",
				},
				CurrentActorId: "actor-id-1",
			},
			initiatedBy: rpPb.InitiatedBy_PAYER,
			setupMocks: func() {
				mockAccountPiRelationClient.EXPECT().GetByPiId(epificontext.WithOwnership(context.Background(), commontypes.Ownership_EPIFI_TECH), &accountPiPb.GetByPiIdRequest{
					PiId: "paymentinstrument-creditcard-federal-pool-account-1",
				}).Return(&accountPiPb.GetByPiIdResponse{
					Status:      rpcPb.StatusOk(),
					ActorId:     "actor-id-1",
					AccountId:   "account-id-1",
					AccountType: accountPb.Type_SAVINGS,
				}, nil)
				mockAccountPiRelationClient.EXPECT().GetByPiId(epificontext.WithOwnership(context.Background(), commontypes.Ownership_EPIFI_TECH), &accountPiPb.GetByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&accountPiPb.GetByPiIdResponse{
					Status:      rpcPb.StatusOk(),
					ActorId:     "actor-id-1",
					AccountId:   "account-id-1",
					AccountType: accountPb.Type_SAVINGS,
				}, nil)
				mockSavingsClient.EXPECT().GetAccount(epificontext.WithOwnership(context.Background(), commontypes.Ownership_EPIFI_TECH), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_Id{
						Id: "account-id-1",
					},
				}).Return(&savingsPb.GetAccountResponse{
					Status: rpcPb.StatusOk(),
					Account: &savingsPb.Account{
						Id: "account-id-2",
					},
				}, nil).Times(2)
			},
			expectedRoute: rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_EXTERNAL,
			expectedError: nil,
		},
		{
			name: "No HardPaymentRoute, Type is ENACH_MANDATES and the pi to is a savings account pi",
			req: &rpPb.CreateRecurringPaymentV1Request{
				RecurringPaymentDetails: &rpPb.RecurringPaymentCreationDetails{
					Type:   rpPb.RecurringPaymentType_ENACH_MANDATES,
					PiFrom: "pi-from-1",
					PiTo:   "paymentinstrument-creditcard-federal-pool-account-1",
				},
				CurrentActorId: "actor-id-1",
			},
			initiatedBy: rpPb.InitiatedBy_PAYER,
			setupMocks: func() {
				mockAccountPiRelationClient.EXPECT().GetByPiId(epificontext.WithOwnership(context.Background(), commontypes.Ownership_EPIFI_TECH), &accountPiPb.GetByPiIdRequest{
					PiId: "paymentinstrument-creditcard-federal-pool-account-1",
				}).Return(&accountPiPb.GetByPiIdResponse{
					Status:      rpcPb.StatusOk(),
					ActorId:     "actor-id-1",
					AccountId:   "account-id-1",
					AccountType: accountPb.Type_SAVINGS,
				}, nil)
				mockSavingsClient.EXPECT().GetAccount(epificontext.WithOwnership(context.Background(), commontypes.Ownership_EPIFI_TECH), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_Id{
						Id: "account-id-1",
					},
				}).Return(&savingsPb.GetAccountResponse{
					Status: rpcPb.StatusOk(),
					Account: &savingsPb.Account{
						Id: "account-id-1",
					},
				}, nil)
			},
			expectedRoute: rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE,
			expectedError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()
			route, err := s.getPaymentRoute(epificontext.WithOwnership(context.Background(), commontypes.Ownership_EPIFI_TECH), tt.req, tt.initiatedBy)
			if tt.expectedError == nil && err != nil {
				t.Errorf("expected no error, got %v", err)
			}
			if !errors.Is(err, tt.expectedError) {
				t.Errorf("expected error %v, got %v", tt.expectedError, err)
			}
			if tt.expectedRoute != route {
				t.Errorf("expected route %v, got %v", tt.expectedRoute, route)
			}
		})
	}
}
