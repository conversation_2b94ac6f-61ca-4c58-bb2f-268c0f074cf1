package recurringpayment

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"

	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	"github.com/epifi/gamma/api/recurringpayment"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	"github.com/epifi/gamma/recurringpayment/internal/actionstatusfetcher"
	actionStatusFetcherMocks "github.com/epifi/gamma/recurringpayment/internal/actionstatusfetcher/mocks"
	"github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor"
	domainCreationProcessorMocks "github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor/mocks"
	internalMocks "github.com/epifi/gamma/recurringpayment/internal/mocks"
)

func TestService_AuthorizeCreationV1(t *testing.T) {
	recurringPayment1 := &recurringpayment.RecurringPayment{
		Id:   "recurring-payment-id-1",
		Type: recurringpayment.RecurringPaymentType_ENACH_MANDATES,
	}
	recurringPaymentAction1 := &recurringpayment.RecurringPaymentsAction{
		Id:                 "recurring-payment-action-id-1",
		RecurringPaymentId: recurringPayment1.GetId(),
		Action:             recurringpayment.Action_CREATE,
	}

	type args struct {
		ctx context.Context
		req *recurringpayment.AuthorizeCreationV1Request
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockCelestialProcessor *internalMocks.MockCelestialProcessor, mockActionStatusFetcher *actionStatusFetcherMocks.MockIActionStatusFetcher)
		want       *recurringpayment.AuthorizeCreationV1Response
		wantErr    bool
	}{
		{
			name: "should return ISE rpc status code when dao call to fetch recurring payment fails",
			args: args{
				ctx: context.Background(),
				req: &recurringpayment.AuthorizeCreationV1Request{
					RecurringPaymentId: "recurring-payment-id-1",
					AuthCredential: &recurringpayment.Credential{
						Params: &recurringpayment.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "dummy-cred-block"},
					},
					AuthMetadata: &recurringpayment.RecurringPaymentCreationAuthMetadata{
						RecurringPaymentTypeSpecificMetadata: &recurringpayment.RecurringPaymentCreationAuthMetadata_EnachMetadata{
							EnachMetadata: &recurringpayment.RecurringPaymentCreationAuthMetadata_ENachAuthMetadata{
								Umrn:                       "UMRN-1",
								NpciRefId:                  "npci_ref_id-1",
								DestBankReferenceNumber:    "dest-bank-ref-number-1",
								MerchantReferenceMessageId: "merchant-ref-message-id-1",
							},
						},
					},
				},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockCelestialProcessor *internalMocks.MockCelestialProcessor, mockActionStatusFetcher *actionStatusFetcherMocks.MockIActionStatusFetcher) {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "recurring-payment-id-1").Return(nil, errors.New("error"))
			},
			want: &recurringpayment.AuthorizeCreationV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error fetching recurring payment by id from db"),
			},
		},
		{
			name: "should return ISE rpc status code when dao call to fetch recurring payment action fails",
			args: args{
				ctx: context.Background(),
				req: &recurringpayment.AuthorizeCreationV1Request{
					RecurringPaymentId: "recurring-payment-id-1",
					AuthCredential: &recurringpayment.Credential{
						Params: &recurringpayment.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "dummy-cred-block"},
					},
					AuthMetadata: &recurringpayment.RecurringPaymentCreationAuthMetadata{
						RecurringPaymentTypeSpecificMetadata: &recurringpayment.RecurringPaymentCreationAuthMetadata_EnachMetadata{
							EnachMetadata: &recurringpayment.RecurringPaymentCreationAuthMetadata_ENachAuthMetadata{
								Umrn:                       "UMRN-1",
								NpciRefId:                  "npci_ref_id-1",
								DestBankReferenceNumber:    "dest-bank-ref-number-1",
								MerchantReferenceMessageId: "merchant-ref-message-id-1",
							},
						},
					},
				},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockCelestialProcessor *internalMocks.MockCelestialProcessor, mockActionStatusFetcher *actionStatusFetcherMocks.MockIActionStatusFetcher) {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "recurring-payment-id-1").Return(recurringPayment1, nil)
				mockRecurringPaymentActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "recurring-payment-id-1", gomock.Any()).Return(nil, errors.New("error"))
			},
			want: &recurringpayment.AuthorizeCreationV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error fetching recurring payment action from db"),
			},
		},
		{
			name: "should return ISE rpc status code when domain authorization fails",
			args: args{
				ctx: context.Background(),
				req: &recurringpayment.AuthorizeCreationV1Request{
					RecurringPaymentId: "recurring-payment-id-1",
					AuthCredential: &recurringpayment.Credential{
						Params: &recurringpayment.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "dummy-cred-block"},
					},
					AuthMetadata: &recurringpayment.RecurringPaymentCreationAuthMetadata{
						RecurringPaymentTypeSpecificMetadata: &recurringpayment.RecurringPaymentCreationAuthMetadata_EnachMetadata{
							EnachMetadata: &recurringpayment.RecurringPaymentCreationAuthMetadata_ENachAuthMetadata{
								Umrn:                       "UMRN-1",
								NpciRefId:                  "npci_ref_id-1",
								DestBankReferenceNumber:    "dest-bank-ref-number-1",
								MerchantReferenceMessageId: "merchant-ref-message-id-1",
							},
						},
					},
				},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockCelestialProcessor *internalMocks.MockCelestialProcessor, mockActionStatusFetcher *actionStatusFetcherMocks.MockIActionStatusFetcher) {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "recurring-payment-id-1").Return(recurringPayment1, nil)
				mockRecurringPaymentActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "recurring-payment-id-1", gomock.Any()).Return([]*recurringpayment.RecurringPaymentsAction{recurringPaymentAction1}, nil)
				mockActionStatusFetcher.EXPECT().GetActionStatusAndNextStepDeeplink(gomock.Any(), recurringPayment1, recurringPaymentAction1, &actionstatusfetcher.Metadata{}).Return(recurringpayment.ActionState_ACTION_IN_PROGRESS, recurringpayment.ActionSubState_ACTION_SUB_STATE_AUTHORIZATION_INITIATED, nil, nil, nil)
				mockDomainCreationProcessor.EXPECT().AuthorizeCreation(gomock.Any(), &domaincreationprocessor.AuthorizeCreationReq{
					RecurringPaymentId: "recurring-payment-id-1",
					AuthCredential: &recurringpayment.Credential{
						Params: &recurringpayment.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "dummy-cred-block"},
					},
					AuthMetadata: &recurringpayment.RecurringPaymentCreationAuthMetadata{
						RecurringPaymentTypeSpecificMetadata: &recurringpayment.RecurringPaymentCreationAuthMetadata_EnachMetadata{
							EnachMetadata: &recurringpayment.RecurringPaymentCreationAuthMetadata_ENachAuthMetadata{
								Umrn:                       "UMRN-1",
								NpciRefId:                  "npci_ref_id-1",
								DestBankReferenceNumber:    "dest-bank-ref-number-1",
								MerchantReferenceMessageId: "merchant-ref-message-id-1",
							},
						},
					},
				}).Return(errors.New("error"))
			},
			want: &recurringpayment.AuthorizeCreationV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error authorizing domain creation"),
			},
		},
		{
			name: "should return FailedPrecondition rpc status code when recurring payment is not in current state to be authorized",
			args: args{
				ctx: context.Background(),
				req: &recurringpayment.AuthorizeCreationV1Request{
					RecurringPaymentId: "recurring-payment-id-1",
					AuthCredential: &recurringpayment.Credential{
						Params: &recurringpayment.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "dummy-cred-block"},
					},
					AuthMetadata: &recurringpayment.RecurringPaymentCreationAuthMetadata{
						RecurringPaymentTypeSpecificMetadata: &recurringpayment.RecurringPaymentCreationAuthMetadata_EnachMetadata{
							EnachMetadata: &recurringpayment.RecurringPaymentCreationAuthMetadata_ENachAuthMetadata{
								Umrn:                       "UMRN-1",
								NpciRefId:                  "npci_ref_id-1",
								DestBankReferenceNumber:    "dest-bank-ref-number-1",
								MerchantReferenceMessageId: "merchant-ref-message-id-1",
							},
						},
					},
				},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockCelestialProcessor *internalMocks.MockCelestialProcessor, mockActionStatusFetcher *actionStatusFetcherMocks.MockIActionStatusFetcher) {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "recurring-payment-id-1").Return(recurringPayment1, nil)
				mockRecurringPaymentActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "recurring-payment-id-1", gomock.Any()).Return([]*recurringpayment.RecurringPaymentsAction{recurringPaymentAction1}, nil)
				mockActionStatusFetcher.EXPECT().GetActionStatusAndNextStepDeeplink(gomock.Any(), recurringPayment1, recurringPaymentAction1, &actionstatusfetcher.Metadata{}).Return(recurringpayment.ActionState_ACTION_IN_PROGRESS, recurringpayment.ActionSubState_ACTION_SUB_STATE_BLOCKED_ON_AUTHORIZATION, nil, nil, nil)
			},
			want: &recurringpayment.AuthorizeCreationV1Response{
				Status: rpc.StatusFailedPreconditionWithDebugMsg("recurring payment is not in a state to be authorized"),
			},
		},
		{
			name: "should return OK rpc status code when authorization is completed successfully",
			args: args{
				ctx: context.Background(),
				req: &recurringpayment.AuthorizeCreationV1Request{
					RecurringPaymentId: "recurring-payment-id-1",
					AuthCredential: &recurringpayment.Credential{
						Params: &recurringpayment.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "dummy-cred-block"},
					},
					AuthMetadata: &recurringpayment.RecurringPaymentCreationAuthMetadata{
						RecurringPaymentTypeSpecificMetadata: &recurringpayment.RecurringPaymentCreationAuthMetadata_EnachMetadata{
							EnachMetadata: &recurringpayment.RecurringPaymentCreationAuthMetadata_ENachAuthMetadata{
								Umrn:                       "UMRN-1",
								NpciRefId:                  "npci_ref_id-1",
								DestBankReferenceNumber:    "dest-bank-ref-number-1",
								MerchantReferenceMessageId: "merchant-ref-message-id-1",
							},
						},
					},
				},
			},
			setupMocks: func(mockRecurringPaymentDao *daoMocks.MockRecurringPaymentDao, mockRecurringPaymentActionDao *daoMocks.MockRecurringPaymentsActionDao, mockDomainCreationProcessor *domainCreationProcessorMocks.MockDomainCreationProcessorV1, mockCelestialProcessor *internalMocks.MockCelestialProcessor, mockActionStatusFetcher *actionStatusFetcherMocks.MockIActionStatusFetcher) {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "recurring-payment-id-1").Return(recurringPayment1, nil)
				mockRecurringPaymentActionDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "recurring-payment-id-1", gomock.Any()).Return([]*recurringpayment.RecurringPaymentsAction{recurringPaymentAction1}, nil)
				mockActionStatusFetcher.EXPECT().GetActionStatusAndNextStepDeeplink(gomock.Any(), recurringPayment1, recurringPaymentAction1, &actionstatusfetcher.Metadata{}).Return(recurringpayment.ActionState_ACTION_IN_PROGRESS, recurringpayment.ActionSubState_ACTION_SUB_STATE_AUTHORIZATION_INITIATED, nil, nil, nil)
				mockDomainCreationProcessor.EXPECT().AuthorizeCreation(gomock.Any(), &domaincreationprocessor.AuthorizeCreationReq{
					RecurringPaymentId: "recurring-payment-id-1",
					AuthCredential: &recurringpayment.Credential{
						Params: &recurringpayment.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "dummy-cred-block"},
					},
					AuthMetadata: &recurringpayment.RecurringPaymentCreationAuthMetadata{
						RecurringPaymentTypeSpecificMetadata: &recurringpayment.RecurringPaymentCreationAuthMetadata_EnachMetadata{
							EnachMetadata: &recurringpayment.RecurringPaymentCreationAuthMetadata_ENachAuthMetadata{
								Umrn:                       "UMRN-1",
								NpciRefId:                  "npci_ref_id-1",
								DestBankReferenceNumber:    "dest-bank-ref-number-1",
								MerchantReferenceMessageId: "merchant-ref-message-id-1",
							},
						},
					},
				}).Return(nil)
				mockCelestialProcessor.EXPECT().SignalWorkflow(gomock.Any(), recurringPaymentAction1.GetClientRequestId(), string(rpNs.CreateRecurringPaymentV1AuthorisationCompletedSignal), workflowPb.Client_RECURRING_PAYMENT, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
			want: &recurringpayment.AuthorizeCreationV1Response{
				Status: rpc.StatusOk(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
			mockRecurringPaymentActionsDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
			mockDomainCreationProcessorFactory := domainCreationProcessorMocks.NewMockDomainCreationProcessorFactory(ctr)
			mockDomainCreationProcessor := domainCreationProcessorMocks.NewMockDomainCreationProcessorV1(ctr)
			mockDomainCreationProcessorFactory.EXPECT().GetProcessor(gomock.Any(), gomock.Any()).Return(mockDomainCreationProcessor, nil).AnyTimes()
			mockCelestialProcessor := internalMocks.NewMockCelestialProcessor(ctr)
			mockActionStatusFetcherFactory := actionStatusFetcherMocks.NewMockIActionStatusFetcherFactory(ctr)
			mockActionStatusFetcher := actionStatusFetcherMocks.NewMockIActionStatusFetcher(ctr)
			mockActionStatusFetcherFactory.EXPECT().GetActionStatusFetcher(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockActionStatusFetcher).AnyTimes()

			tt.setupMocks(mockRecurringPaymentDao, mockRecurringPaymentActionsDao, mockDomainCreationProcessor, mockCelestialProcessor, mockActionStatusFetcher)

			s := &Service{
				recurringPaymentDao:            mockRecurringPaymentDao,
				recurringPaymentActionsDao:     mockRecurringPaymentActionsDao,
				domainCreationProcessorFactory: mockDomainCreationProcessorFactory,
				celestialProcessor:             mockCelestialProcessor,
				actionStatusFetcherFactory:     mockActionStatusFetcherFactory,
			}
			got, err := s.AuthorizeCreationV1(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AuthorizeCreationV1() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("AuthorizeCreationV1() got = %v, want %v", got, tt.want)
			}
		})
	}
}
