package recurringpayment

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"sort"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	datePb "google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	balanceEnumsPb "github.com/epifi/gamma/api/accounts/balance/enums"
	balanceMocksPb "github.com/epifi/gamma/api/accounts/balance/mocks"
	actorPb "github.com/epifi/gamma/api/actor"
	actorMocksPb "github.com/epifi/gamma/api/actor/mocks"
	depositPb "github.com/epifi/gamma/api/deposit"
	depositMocksPb "github.com/epifi/gamma/api/deposit/mocks"
	mutualFundPb "github.com/epifi/gamma/api/investment/mutualfund"
	catalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	catalogMocksPb "github.com/epifi/gamma/api/investment/mutualfund/catalog/mocks"
	merchantPb "github.com/epifi/gamma/api/merchant"
	merchantMocksPb "github.com/epifi/gamma/api/merchant/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	rePb "github.com/epifi/gamma/api/recurringpayment"
	rePbEnums "github.com/epifi/gamma/api/recurringpayment/enums"
	savingsPb "github.com/epifi/gamma/api/savings"
	savingsMocksPb "github.com/epifi/gamma/api/savings/mocks"
	upcomingTxnsPb "github.com/epifi/gamma/api/upcomingtransactions"
	upcomingTxnMockPb "github.com/epifi/gamma/api/upcomingtransactions/mocks"
	upcomingTxnPbEnums "github.com/epifi/gamma/api/upcomingtransactions/model"
	usstocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	usstocksMocksPb "github.com/epifi/gamma/api/usstocks/catalog/mocks"
	"github.com/epifi/gamma/pkg/investment"
)

func TestService_GetUpcomingTransactions(t *testing.T) {
	var (
		defaultFromTime = timestampPb.Now()
		defaultToTime   = timestampPb.New(datetime.DateToTimeV2(&datePb.Date{
			Year:  2025,
			Month: 2,
			Day:   3,
		}, datetime.IST))
		defaultActorId   = "default-actor-id"
		defaultSource    = []rePbEnums.UpcomingTransactionSource{rePbEnums.UpcomingTransactionSource_DS, rePbEnums.UpcomingTransactionSource_FITTT}
		defaultEntryType = paymentPb.AccountingEntryType_DEBIT
		defaultRequest   = &rePb.GetUpcomingTransactionsRequest{
			ActorId:         defaultActorId,
			FromTime:        defaultFromTime,
			ToTime:          defaultToTime,
			Source:          defaultSource,
			AccountingEntry: defaultEntryType,
		}

		defaultGetUpcomingTxnsForActorRequest = &upcomingTxnsPb.GetUpcomingTxnsForActorRequest{
			ActorId:         defaultActorId,
			FromTime:        defaultFromTime,
			ToTime:          defaultToTime,
			TxnSources:      []upcomingTxnPbEnums.TxnSource{upcomingTxnPbEnums.TxnSource_TXN_SOURCE_DS, upcomingTxnPbEnums.TxnSource_TXN_SOURCE_FITTT},
			AccountingEntry: defaultEntryType,
		}
		defaultMinTime             = timestampPb.New(time.Now().Add(4 * 24 * time.Hour))
		defaultMaxTime             = timestampPb.New(time.Now().Add(6 * 24 * time.Hour))
		defaultMinAmount           = &moneyPb.Money{CurrencyCode: moneyPkg.RupeeCurrencyCode, Units: 500}
		defaultMerchantId          = "default-merchant-id"
		defaultMerchantUpcomingTxn = &upcomingTxnsPb.UpcomingTransaction{
			EntityType:      upcomingTxnPbEnums.EntityType_ENTITY_TYPE_MERCHANT,
			DerivedEntityId: defaultMerchantId,
			MinAmount:       defaultMinAmount,
			MinTime:         defaultMinTime,
			MaxTime:         defaultMaxTime,
			CreditDebit:     defaultEntryType,
			TxnSource:       upcomingTxnPbEnums.TxnSource_TXN_SOURCE_DS,
		}
		defaultOtherActorId     = "default-other-actor-id"
		defaultActorUpcomingTxn = &upcomingTxnsPb.UpcomingTransaction{
			EntityType:      upcomingTxnPbEnums.EntityType_ENTITY_TYPE_ACTOR,
			DerivedEntityId: defaultOtherActorId,
			MinAmount:       defaultMinAmount,
			MinTime:         defaultMinTime,
			MaxTime:         defaultMaxTime,
			CreditDebit:     defaultEntryType,
			TxnSource:       upcomingTxnPbEnums.TxnSource_TXN_SOURCE_DS,
		}

		defaultSdId                 = "default-sd-id"
		defaultSdDepositUpcomingTxn = &upcomingTxnsPb.UpcomingTransaction{
			EntityType:      upcomingTxnPbEnums.EntityType_ENTITY_TYPE_SD_DEPOSIT_ACCOUNT,
			DerivedEntityId: defaultSdId,
			MinAmount:       defaultMinAmount,
			MinTime:         defaultMinTime,
			MaxTime:         defaultMaxTime,
			CreditDebit:     defaultEntryType,
			TxnSource:       upcomingTxnPbEnums.TxnSource_TXN_SOURCE_DS,
		}

		defaultMutualFundId          = "default-mutual-fund-id"
		defaultMutualFundUpcomingTxn = &upcomingTxnsPb.UpcomingTransaction{
			EntityType:      upcomingTxnPbEnums.EntityType_ENTITY_TYPE_MUTUAL_FUND,
			DerivedEntityId: defaultMutualFundId,
			MinAmount:       defaultMinAmount,
			MinTime:         defaultMinTime,
			MaxTime:         defaultMaxTime,
			CreditDebit:     defaultEntryType,
			TxnSource:       upcomingTxnPbEnums.TxnSource_TXN_SOURCE_DS,
		}

		defaultStockId          = "default-stock-id"
		defaultStockUpcomingTxn = &upcomingTxnsPb.UpcomingTransaction{
			EntityType:      upcomingTxnPbEnums.EntityType_ENTITY_TYPE_USSTOCKS,
			DerivedEntityId: defaultStockId,
			MinAmount:       defaultMinAmount,
			MinTime:         defaultMinTime,
			MaxTime:         defaultMaxTime,
			CreditDebit:     defaultEntryType,
			TxnSource:       upcomingTxnPbEnums.TxnSource_TXN_SOURCE_DS,
		}

		defaultGetUpcomingTxnsForActorResponse = &upcomingTxnsPb.GetUpcomingTxnsForActorResponse{
			Status: rpc.StatusOk(),
			Transactions: []*upcomingTxnsPb.UpcomingTransaction{
				defaultMerchantUpcomingTxn,
				defaultActorUpcomingTxn,
				defaultSdDepositUpcomingTxn,
				defaultMutualFundUpcomingTxn,
				defaultStockUpcomingTxn,
			},
		}

		defaultGetMerchantsRequest = &merchantPb.GetMerchantsRequest{
			Identifier: &merchantPb.GetMerchantsRequest_MerchantIdentifier_{
				MerchantIdentifier: &merchantPb.GetMerchantsRequest_MerchantIdentifier{
					MerchantIds: []string{defaultMerchantId},
				},
			},
		}
		defaultLogoUrl              = "default-logo-url"
		defaultMerchantName         = "Groww"
		defaultGetMerchantsResponse = &merchantPb.GetMerchantsResponse{
			Status: rpc.StatusOk(),
			Merchants: []*merchantPb.Merchant{
				{
					Id:          defaultMerchantId,
					LogoUrl:     defaultLogoUrl,
					DisplayName: defaultMerchantName,
				},
			},
		}

		defaultGetEntityDetailsRequest = &actorPb.GetEntityDetailsRequest{
			ActorIds: []string{defaultOtherActorId},
		}
		defaultOtherActorName           = "default-other-actor-name"
		defaultGetEntityDetailsResponse = &actorPb.GetEntityDetailsResponse{
			Status: rpc.StatusOk(),
			EntityDetails: []*actorPb.GetEntityDetailsResponse_EntityDetail{
				{
					ActorId: defaultOtherActorId,
					Name: &commontypes.Name{
						FirstName: defaultOtherActorName,
					},
					ProfileImageUrl: defaultLogoUrl,
				},
			},
		}

		defaultGetMutualFundsRequest = &catalogPb.GetMutualFundsRequest{
			FundIdentifier: mutualFundPb.MutualFundIdentifier_MUTUAL_FUND_IDENTIFIER_ID,
			Ids:            []string{defaultMutualFundId},
		}
		defaultMutualFundName         = "ICICI Prudential"
		defaultMutualFundIconUrl, _   = investment.IconsForAmc[mutualFundPb.Amc_AXIS]
		defaultGetMutualFundsResponse = &catalogPb.GetMutualFundsResponse{
			Status: rpc.StatusOk(),
			MutualFunds: map[string]*mutualFundPb.MutualFund{
				defaultMutualFundId: {
					Id:  defaultMutualFundId,
					Amc: mutualFundPb.Amc_AXIS,
					NameData: &mutualFundPb.FundNameMetadata{
						DisplayName: defaultMutualFundName,
					},
				},
			},
		}

		defaultGetStocksRequest = &usstocksCatalogPb.GetStocksRequest{
			Identifiers: &usstocksCatalogPb.GetStocksRequest_StockIds{
				StockIds: &usstocksCatalogPb.RepeatedStrings{
					Ids: []string{defaultStockId},
				},
			},
		}
		defaultStockSymbol       = "AMZN"
		defaultStockLogoUrl      = "default-stock-logo-url"
		defaultStockShortName    = "Amazon.com"
		defaultGetStocksResponse = &usstocksCatalogPb.GetStocksResponse{
			Status: rpc.StatusOk(),
			Stocks: map[string]*usstocksCatalogPb.Stock{
				defaultStockId: {
					Id:       defaultStockId,
					Symbol:   defaultStockSymbol,
					Exchange: usstocksCatalogPb.Exchange_EXCHANGE_NAS,
					CompanyInfo: &usstocksCatalogPb.CompanyInfo{
						LogoUrl: defaultStockLogoUrl,
						CompanyName: &usstocksCatalogPb.CompanyName{
							ShortName: defaultStockShortName,
						},
					},
				},
			},
		}
		emptyGetStocksResponse = &usstocksCatalogPb.GetStocksResponse{
			Status: rpc.StatusOk(),
			Stocks: map[string]*usstocksCatalogPb.Stock{},
		}

		defaultDepositsGetByIdRequest = &depositPb.GetByIdRequest{
			Id: defaultSdId,
		}
		defaultSdName                 = "travel savings"
		defaultDepositGetByIdResponse = &depositPb.GetByIdResponse{
			Status: rpc.StatusOk(),
			Account: &depositPb.DepositAccount{
				Id:   defaultSdId,
				Name: defaultSdName,
				DepositIcon: &commontypes.Image{
					ImageUrl: defaultLogoUrl,
				},
			},
		}

		defaultSavingsAccountId            = "default-savings-account-id"
		defaultGetSavingsEssentialsRequest = &savingsPb.GetSavingsAccountEssentialsRequest{
			Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
				ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
					ActorId:     defaultActorId,
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
		}
		defaultGetSavingsEssentialsResponse = &savingsPb.GetSavingsAccountEssentialsResponse{
			Status: rpc.StatusOk(),
			Account: &savingsPb.SavingsAccountEssentials{
				Id: defaultSavingsAccountId,
			},
		}

		defaultGetBalanceRequest = &accountBalancePb.GetAccountBalanceRequest{
			Identifier: &accountBalancePb.GetAccountBalanceRequest_Id{
				Id: defaultSavingsAccountId,
			},
			ActorId:       defaultActorId,
			DataFreshness: balanceEnumsPb.DataFreshness_LAST_KNOWN_BALANCE_FROM_DB,
		}
		defaultGetBalanceResponse = &accountBalancePb.GetAccountBalanceResponse{
			Status:           rpc.StatusOk(),
			AvailableBalance: &moneyPb.Money{CurrencyCode: "INR", Units: 1500},
		}
		defaultGetBalanceResponseWithSufficientFunds = &accountBalancePb.GetAccountBalanceResponse{
			Status:           rpc.StatusOk(),
			AvailableBalance: &moneyPb.Money{CurrencyCode: "INR", Units: ********},
		}

		defaultResponse = &rePb.GetUpcomingTransactionsResponse{
			Status: rpc.StatusOk(),
			UpcomingTxns: []*rePb.UpcomingTransaction{
				{
					Type:          rePbEnums.UpcomingTransactionEntityType_MUTUAL_FUND,
					Source:        rePbEnums.UpcomingTransactionSource_DS,
					EntityId:      defaultMutualFundId,
					EntityName:    defaultMutualFundName,
					MinTime:       defaultMinTime,
					MaxTime:       defaultMaxTime,
					MinAmount:     defaultMinAmount,
					EntityIconUrl: defaultMutualFundIconUrl,
					EntryType:     defaultEntryType,
				},
				{
					Type:          rePbEnums.UpcomingTransactionEntityType_SD_DEPOSIT_ACCOUNT,
					Source:        rePbEnums.UpcomingTransactionSource_DS,
					EntityId:      defaultSdId,
					EntityName:    defaultSdName,
					MinTime:       defaultMinTime,
					MaxTime:       defaultMaxTime,
					MinAmount:     defaultMinAmount,
					EntityIconUrl: defaultLogoUrl,
					EntryType:     defaultEntryType,
				},
				{
					Type:          rePbEnums.UpcomingTransactionEntityType_ACTOR,
					Source:        rePbEnums.UpcomingTransactionSource_DS,
					EntityId:      defaultOtherActorId,
					EntityName:    defaultOtherActorName,
					MinTime:       defaultMinTime,
					MaxTime:       defaultMaxTime,
					MinAmount:     defaultMinAmount,
					EntityIconUrl: defaultLogoUrl,
					EntryType:     defaultEntryType,
				},
				{
					Type:          rePbEnums.UpcomingTransactionEntityType_MERCHANT,
					Source:        rePbEnums.UpcomingTransactionSource_DS,
					EntityId:      defaultMerchantId,
					EntityName:    defaultMerchantName,
					MinTime:       defaultMinTime,
					MaxTime:       defaultMaxTime,
					MinAmount:     defaultMinAmount,
					EntityIconUrl: defaultLogoUrl,
					EntryType:     defaultEntryType,
				},
				{
					Type:          rePbEnums.UpcomingTransactionEntityType_USSTOCKS,
					Source:        rePbEnums.UpcomingTransactionSource_DS,
					EntityId:      defaultStockId,
					EntityName:    defaultStockShortName,
					MinTime:       defaultMinTime,
					MaxTime:       defaultMaxTime,
					MinAmount:     defaultMinAmount,
					EntityIconUrl: defaultStockLogoUrl,
					EntryType:     defaultEntryType,
				},
			},
			AccountDetails: []*rePb.GetUpcomingTransactionsResponse_AccountDetails{
				{
					AccountId:  defaultSavingsAccountId,
					FundsToAdd: &moneyPb.Money{CurrencyCode: "INR", Units: 1000},
				},
			},
		}
		responseWithErrorInUpcomingTxns = &rePb.GetUpcomingTransactionsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while enriching the upcoming transactions"),
		}
		defaultResponseWithNoAccountDetails = &rePb.GetUpcomingTransactionsResponse{
			Status: rpc.StatusOk(),
			UpcomingTxns: []*rePb.UpcomingTransaction{
				{
					Type:          rePbEnums.UpcomingTransactionEntityType_MUTUAL_FUND,
					Source:        rePbEnums.UpcomingTransactionSource_DS,
					EntityId:      defaultMutualFundId,
					EntityName:    defaultMutualFundName,
					MinTime:       defaultMinTime,
					MaxTime:       defaultMaxTime,
					MinAmount:     defaultMinAmount,
					EntityIconUrl: defaultMutualFundIconUrl,
					EntryType:     defaultEntryType,
				},
				{
					Type:          rePbEnums.UpcomingTransactionEntityType_SD_DEPOSIT_ACCOUNT,
					Source:        rePbEnums.UpcomingTransactionSource_DS,
					EntityId:      defaultSdId,
					EntityName:    defaultSdName,
					MinTime:       defaultMinTime,
					MaxTime:       defaultMaxTime,
					MinAmount:     defaultMinAmount,
					EntityIconUrl: defaultLogoUrl,
					EntryType:     defaultEntryType,
				},
				{
					Type:          rePbEnums.UpcomingTransactionEntityType_ACTOR,
					Source:        rePbEnums.UpcomingTransactionSource_DS,
					EntityId:      defaultOtherActorId,
					EntityName:    defaultOtherActorName,
					MinTime:       defaultMinTime,
					MaxTime:       defaultMaxTime,
					MinAmount:     defaultMinAmount,
					EntityIconUrl: defaultLogoUrl,
					EntryType:     defaultEntryType,
				},
				{
					Type:          rePbEnums.UpcomingTransactionEntityType_MERCHANT,
					Source:        rePbEnums.UpcomingTransactionSource_DS,
					EntityId:      defaultMerchantId,
					EntityName:    defaultMerchantName,
					MinTime:       defaultMinTime,
					MaxTime:       defaultMaxTime,
					MinAmount:     defaultMinAmount,
					EntityIconUrl: defaultLogoUrl,
					EntryType:     defaultEntryType,
				},
				{
					Type:          rePbEnums.UpcomingTransactionEntityType_USSTOCKS,
					Source:        rePbEnums.UpcomingTransactionSource_DS,
					EntityId:      defaultStockId,
					EntityName:    defaultStockShortName,
					MinTime:       defaultMinTime,
					MaxTime:       defaultMaxTime,
					MinAmount:     defaultMinAmount,
					EntityIconUrl: defaultStockLogoUrl,
					EntryType:     defaultEntryType,
				},
			},
		}
	)
	type args struct {
		ctx context.Context
		req *rePb.GetUpcomingTransactionsRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(upcomingTxnClient *upcomingTxnMockPb.MockUpcomingTransactionsClient, actorClient *actorMocksPb.MockActorClient, merchantClient *merchantMocksPb.MockMerchantServiceClient,
			mfCatalogClient *catalogMocksPb.MockCatalogManagerClient, depositClient *depositMocksPb.MockDepositClient, savingsClient *savingsMocksPb.MockSavingsClient, balanceClient *balanceMocksPb.MockBalanceClient, usstocksClient *usstocksMocksPb.MockCatalogManagerClient)
		want    *rePb.GetUpcomingTransactionsResponse
		wantErr bool
	}{
		{
			name: "record not found (no upcoming transactions)",
			args: args{
				ctx: context.Background(),
				req: defaultRequest,
			},
			setupMocks: func(upcomingTxnClient *upcomingTxnMockPb.MockUpcomingTransactionsClient, actorClient *actorMocksPb.MockActorClient, merchantClient *merchantMocksPb.MockMerchantServiceClient,
				mfCatalogClient *catalogMocksPb.MockCatalogManagerClient, depositClient *depositMocksPb.MockDepositClient, savingsClient *savingsMocksPb.MockSavingsClient, balanceClient *balanceMocksPb.MockBalanceClient, usstocksClient *usstocksMocksPb.MockCatalogManagerClient) {
				upcomingTxnClient.EXPECT().GetUpcomingTxnsForActor(gomock.Any(), defaultGetUpcomingTxnsForActorRequest).Return(&upcomingTxnsPb.GetUpcomingTxnsForActorResponse{Status: rpc.StatusRecordNotFound()}, nil)
			},
			want: &rePb.GetUpcomingTransactionsResponse{
				Status: rpc.StatusRecordNotFoundWithDebugMsg("no upcoming transactions for the given user"),
			},
		},
		{
			name: "failed (error while txns from upcoming txns service)",
			args: args{
				ctx: context.Background(),
				req: defaultRequest,
			},
			setupMocks: func(upcomingTxnClient *upcomingTxnMockPb.MockUpcomingTransactionsClient, actorClient *actorMocksPb.MockActorClient, merchantClient *merchantMocksPb.MockMerchantServiceClient,
				mfCatalogClient *catalogMocksPb.MockCatalogManagerClient, depositClient *depositMocksPb.MockDepositClient, savingsClient *savingsMocksPb.MockSavingsClient, balanceClient *balanceMocksPb.MockBalanceClient, usstocksClient *usstocksMocksPb.MockCatalogManagerClient) {
				upcomingTxnClient.EXPECT().GetUpcomingTxnsForActor(gomock.Any(), defaultGetUpcomingTxnsForActorRequest).Return(&upcomingTxnsPb.GetUpcomingTxnsForActorResponse{Status: rpc.StatusInternal()}, nil)
			},
			want: &rePb.GetUpcomingTransactionsResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to fetch upcoming transactions for the actor"),
			},
		},
		{
			name: "failed (error while merchants transactions details)",
			args: args{
				ctx: context.Background(),
				req: defaultRequest,
			},
			setupMocks: func(upcomingTxnClient *upcomingTxnMockPb.MockUpcomingTransactionsClient, actorClient *actorMocksPb.MockActorClient, merchantClient *merchantMocksPb.MockMerchantServiceClient,
				mfCatalogClient *catalogMocksPb.MockCatalogManagerClient, depositClient *depositMocksPb.MockDepositClient, savingsClient *savingsMocksPb.MockSavingsClient, balanceClient *balanceMocksPb.MockBalanceClient, usstocksClient *usstocksMocksPb.MockCatalogManagerClient) {
				upcomingTxnClient.EXPECT().GetUpcomingTxnsForActor(gomock.Any(), defaultGetUpcomingTxnsForActorRequest).Return(defaultGetUpcomingTxnsForActorResponse, nil)
				merchantClient.EXPECT().GetMerchants(gomock.Any(), defaultGetMerchantsRequest).Return(&merchantPb.GetMerchantsResponse{Status: rpc.StatusInternal()}, nil)
				actorClient.EXPECT().GetEntityDetails(gomock.Any(), defaultGetEntityDetailsRequest).Return(&actorPb.GetEntityDetailsResponse{Status: rpc.StatusInternal()}, nil)
				mfCatalogClient.EXPECT().GetMutualFunds(gomock.Any(), defaultGetMutualFundsRequest).Return(&catalogPb.GetMutualFundsResponse{Status: rpc.StatusInternal()}, nil)
				depositClient.EXPECT().GetById(gomock.Any(), defaultDepositsGetByIdRequest).Return(&depositPb.GetByIdResponse{Status: rpc.StatusInternal()}, nil)
				usstocksClient.EXPECT().GetStocks(gomock.Any(), defaultGetStocksRequest).Return(&usstocksCatalogPb.GetStocksResponse{Status: rpc.StatusInternal()}, nil)
			},
			want: &rePb.GetUpcomingTransactionsResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while enriching the upcoming transactions"),
			},
		},
		{
			name: "failed (error while actor transactions details)",
			args: args{
				ctx: context.Background(),
				req: defaultRequest,
			},
			setupMocks: func(upcomingTxnClient *upcomingTxnMockPb.MockUpcomingTransactionsClient, actorClient *actorMocksPb.MockActorClient, merchantClient *merchantMocksPb.MockMerchantServiceClient,
				mfCatalogClient *catalogMocksPb.MockCatalogManagerClient, depositClient *depositMocksPb.MockDepositClient, savingsClient *savingsMocksPb.MockSavingsClient, balanceClient *balanceMocksPb.MockBalanceClient, usstocksClient *usstocksMocksPb.MockCatalogManagerClient) {
				upcomingTxnClient.EXPECT().GetUpcomingTxnsForActor(gomock.Any(), defaultGetUpcomingTxnsForActorRequest).Return(defaultGetUpcomingTxnsForActorResponse, nil)
				merchantClient.EXPECT().GetMerchants(gomock.Any(), defaultGetMerchantsRequest).Return(defaultGetMerchantsResponse, nil)
				actorClient.EXPECT().GetEntityDetails(gomock.Any(), defaultGetEntityDetailsRequest).Return(&actorPb.GetEntityDetailsResponse{Status: rpc.StatusInternal()}, nil)
				mfCatalogClient.EXPECT().GetMutualFunds(gomock.Any(), defaultGetMutualFundsRequest).Return(&catalogPb.GetMutualFundsResponse{Status: rpc.StatusInternal()}, nil)
				depositClient.EXPECT().GetById(gomock.Any(), defaultDepositsGetByIdRequest).Return(&depositPb.GetByIdResponse{Status: rpc.StatusInternal()}, nil)
				usstocksClient.EXPECT().GetStocks(gomock.Any(), defaultGetStocksRequest).Return(&usstocksCatalogPb.GetStocksResponse{Status: rpc.StatusInternal()}, nil)
			},
			want: &rePb.GetUpcomingTransactionsResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while enriching the upcoming transactions"),
			},
		},
		{
			name: "failed (error while mutual funds transactions details)",
			args: args{
				ctx: context.Background(),
				req: defaultRequest,
			},
			setupMocks: func(upcomingTxnClient *upcomingTxnMockPb.MockUpcomingTransactionsClient, actorClient *actorMocksPb.MockActorClient, merchantClient *merchantMocksPb.MockMerchantServiceClient,
				mfCatalogClient *catalogMocksPb.MockCatalogManagerClient, depositClient *depositMocksPb.MockDepositClient, savingsClient *savingsMocksPb.MockSavingsClient, balanceClient *balanceMocksPb.MockBalanceClient, usstocksClient *usstocksMocksPb.MockCatalogManagerClient) {
				upcomingTxnClient.EXPECT().GetUpcomingTxnsForActor(gomock.Any(), defaultGetUpcomingTxnsForActorRequest).Return(defaultGetUpcomingTxnsForActorResponse, nil)
				merchantClient.EXPECT().GetMerchants(gomock.Any(), defaultGetMerchantsRequest).Return(defaultGetMerchantsResponse, nil)
				actorClient.EXPECT().GetEntityDetails(gomock.Any(), defaultGetEntityDetailsRequest).Return(defaultGetEntityDetailsResponse, nil)
				usstocksClient.EXPECT().GetStocks(gomock.Any(), defaultGetStocksRequest).Return(defaultGetStocksResponse, nil)
				mfCatalogClient.EXPECT().GetMutualFunds(gomock.Any(), defaultGetMutualFundsRequest).Return(&catalogPb.GetMutualFundsResponse{Status: rpc.StatusInternal()}, nil)
				depositClient.EXPECT().GetById(gomock.Any(), defaultDepositsGetByIdRequest).Return(&depositPb.GetByIdResponse{Status: rpc.StatusInternal()}, nil)
			},
			want: &rePb.GetUpcomingTransactionsResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while enriching the upcoming transactions"),
			},
		},
		{
			name: "failed (error while depoist transactions details)",
			args: args{
				ctx: context.Background(),
				req: defaultRequest,
			},
			setupMocks: func(upcomingTxnClient *upcomingTxnMockPb.MockUpcomingTransactionsClient, actorClient *actorMocksPb.MockActorClient, merchantClient *merchantMocksPb.MockMerchantServiceClient,
				mfCatalogClient *catalogMocksPb.MockCatalogManagerClient, depositClient *depositMocksPb.MockDepositClient, savingsClient *savingsMocksPb.MockSavingsClient, balanceClient *balanceMocksPb.MockBalanceClient, usstocksClient *usstocksMocksPb.MockCatalogManagerClient) {
				upcomingTxnClient.EXPECT().GetUpcomingTxnsForActor(gomock.Any(), defaultGetUpcomingTxnsForActorRequest).Return(defaultGetUpcomingTxnsForActorResponse, nil)
				merchantClient.EXPECT().GetMerchants(gomock.Any(), defaultGetMerchantsRequest).Return(defaultGetMerchantsResponse, nil)
				actorClient.EXPECT().GetEntityDetails(gomock.Any(), defaultGetEntityDetailsRequest).Return(defaultGetEntityDetailsResponse, nil)
				mfCatalogClient.EXPECT().GetMutualFunds(gomock.Any(), defaultGetMutualFundsRequest).Return(defaultGetMutualFundsResponse, nil)
				usstocksClient.EXPECT().GetStocks(gomock.Any(), defaultGetStocksRequest).Return(defaultGetStocksResponse, nil)
				depositClient.EXPECT().GetById(gomock.Any(), defaultDepositsGetByIdRequest).Return(&depositPb.GetByIdResponse{Status: rpc.StatusInternal()}, nil)
			},
			want: &rePb.GetUpcomingTransactionsResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while enriching the upcoming transactions"),
			},
		},
		{
			name: "failed (error while fetching savings accounts)",
			args: args{
				ctx: context.Background(),
				req: defaultRequest,
			},
			setupMocks: func(upcomingTxnClient *upcomingTxnMockPb.MockUpcomingTransactionsClient, actorClient *actorMocksPb.MockActorClient, merchantClient *merchantMocksPb.MockMerchantServiceClient,
				mfCatalogClient *catalogMocksPb.MockCatalogManagerClient, depositClient *depositMocksPb.MockDepositClient, savingsClient *savingsMocksPb.MockSavingsClient, balanceClient *balanceMocksPb.MockBalanceClient, usstocksClient *usstocksMocksPb.MockCatalogManagerClient) {
				upcomingTxnClient.EXPECT().GetUpcomingTxnsForActor(gomock.Any(), defaultGetUpcomingTxnsForActorRequest).Return(defaultGetUpcomingTxnsForActorResponse, nil)
				merchantClient.EXPECT().GetMerchants(gomock.Any(), defaultGetMerchantsRequest).Return(defaultGetMerchantsResponse, nil)
				actorClient.EXPECT().GetEntityDetails(gomock.Any(), defaultGetEntityDetailsRequest).Return(defaultGetEntityDetailsResponse, nil)
				mfCatalogClient.EXPECT().GetMutualFunds(gomock.Any(), defaultGetMutualFundsRequest).Return(defaultGetMutualFundsResponse, nil)
				depositClient.EXPECT().GetById(gomock.Any(), defaultDepositsGetByIdRequest).Return(defaultDepositGetByIdResponse, nil)
				usstocksClient.EXPECT().GetStocks(gomock.Any(), defaultGetStocksRequest).Return(defaultGetStocksResponse, nil)
				savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), defaultGetSavingsEssentialsRequest).Return(&savingsPb.GetSavingsAccountEssentialsResponse{Status: rpc.StatusInternal()}, nil)
			},
			want: &rePb.GetUpcomingTransactionsResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to fetch account details and add funds to be added"),
			},
		},
		{
			name: "failed (error while fetching account balance)",
			args: args{
				ctx: context.Background(),
				req: defaultRequest,
			},
			setupMocks: func(upcomingTxnClient *upcomingTxnMockPb.MockUpcomingTransactionsClient, actorClient *actorMocksPb.MockActorClient, merchantClient *merchantMocksPb.MockMerchantServiceClient,
				mfCatalogClient *catalogMocksPb.MockCatalogManagerClient, depositClient *depositMocksPb.MockDepositClient, savingsClient *savingsMocksPb.MockSavingsClient, balanceClient *balanceMocksPb.MockBalanceClient, usstocksClient *usstocksMocksPb.MockCatalogManagerClient) {
				upcomingTxnClient.EXPECT().GetUpcomingTxnsForActor(gomock.Any(), defaultGetUpcomingTxnsForActorRequest).Return(defaultGetUpcomingTxnsForActorResponse, nil)
				merchantClient.EXPECT().GetMerchants(gomock.Any(), defaultGetMerchantsRequest).Return(defaultGetMerchantsResponse, nil)
				actorClient.EXPECT().GetEntityDetails(gomock.Any(), defaultGetEntityDetailsRequest).Return(defaultGetEntityDetailsResponse, nil)
				mfCatalogClient.EXPECT().GetMutualFunds(gomock.Any(), defaultGetMutualFundsRequest).Return(defaultGetMutualFundsResponse, nil)
				depositClient.EXPECT().GetById(gomock.Any(), defaultDepositsGetByIdRequest).Return(defaultDepositGetByIdResponse, nil)
				savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), defaultGetSavingsEssentialsRequest).Return(defaultGetSavingsEssentialsResponse, nil)
				usstocksClient.EXPECT().GetStocks(gomock.Any(), defaultGetStocksRequest).Return(defaultGetStocksResponse, nil)
				balanceClient.EXPECT().GetAccountBalance(gomock.Any(), defaultGetBalanceRequest).Return(&accountBalancePb.GetAccountBalanceResponse{Status: rpc.StatusInternal()}, nil)
			},
			want: &rePb.GetUpcomingTransactionsResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to fetch account details and add funds to be added"),
			},
		},
		{
			name: "failed (error while enriching the upcoming transactions)",
			args: args{
				ctx: context.Background(),
				req: defaultRequest,
			},
			setupMocks: func(upcomingTxnClient *upcomingTxnMockPb.MockUpcomingTransactionsClient, actorClient *actorMocksPb.MockActorClient, merchantClient *merchantMocksPb.MockMerchantServiceClient,
				mfCatalogClient *catalogMocksPb.MockCatalogManagerClient, depositClient *depositMocksPb.MockDepositClient, savingsClient *savingsMocksPb.MockSavingsClient, balanceClient *balanceMocksPb.MockBalanceClient, usstocksClient *usstocksMocksPb.MockCatalogManagerClient) {
				upcomingTxnClient.EXPECT().GetUpcomingTxnsForActor(gomock.Any(), defaultGetUpcomingTxnsForActorRequest).Return(defaultGetUpcomingTxnsForActorResponse, nil)
				merchantClient.EXPECT().GetMerchants(gomock.Any(), defaultGetMerchantsRequest).Return(defaultGetMerchantsResponse, nil)
				actorClient.EXPECT().GetEntityDetails(gomock.Any(), defaultGetEntityDetailsRequest).Return(defaultGetEntityDetailsResponse, nil)
				mfCatalogClient.EXPECT().GetMutualFunds(gomock.Any(), defaultGetMutualFundsRequest).Return(defaultGetMutualFundsResponse, nil)
				depositClient.EXPECT().GetById(gomock.Any(), defaultDepositsGetByIdRequest).Return(defaultDepositGetByIdResponse, nil)
				savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), defaultGetSavingsEssentialsRequest).Return(defaultGetSavingsEssentialsResponse, nil).AnyTimes()
				balanceClient.EXPECT().GetAccountBalance(gomock.Any(), defaultGetBalanceRequest).Return(defaultGetBalanceResponse, nil).AnyTimes()
				usstocksClient.EXPECT().GetStocks(gomock.Any(), defaultGetStocksRequest).Return(emptyGetStocksResponse, nil)
			},
			want:    responseWithErrorInUpcomingTxns,
			wantErr: false,
		},
		{
			name: "failed (error while usstocks transactions details)",
			args: args{
				ctx: context.Background(),
				req: defaultRequest,
			},
			setupMocks: func(upcomingTxnClient *upcomingTxnMockPb.MockUpcomingTransactionsClient, actorClient *actorMocksPb.MockActorClient, merchantClient *merchantMocksPb.MockMerchantServiceClient,
				mfCatalogClient *catalogMocksPb.MockCatalogManagerClient, depositClient *depositMocksPb.MockDepositClient, savingsClient *savingsMocksPb.MockSavingsClient, balanceClient *balanceMocksPb.MockBalanceClient, usstocksClient *usstocksMocksPb.MockCatalogManagerClient) {
				upcomingTxnClient.EXPECT().GetUpcomingTxnsForActor(gomock.Any(), defaultGetUpcomingTxnsForActorRequest).Return(defaultGetUpcomingTxnsForActorResponse, nil)
				merchantClient.EXPECT().GetMerchants(gomock.Any(), defaultGetMerchantsRequest).Return(defaultGetMerchantsResponse, nil)
				actorClient.EXPECT().GetEntityDetails(gomock.Any(), defaultGetEntityDetailsRequest).Return(defaultGetEntityDetailsResponse, nil)
				mfCatalogClient.EXPECT().GetMutualFunds(gomock.Any(), defaultGetMutualFundsRequest).Return(defaultGetMutualFundsResponse, nil)
				depositClient.EXPECT().GetById(gomock.Any(), defaultDepositsGetByIdRequest).Return(defaultDepositGetByIdResponse, nil)
				usstocksClient.EXPECT().GetStocks(gomock.Any(), defaultGetStocksRequest).Return(&usstocksCatalogPb.GetStocksResponse{Status: rpc.StatusInternal()}, nil)
			},
			want:    responseWithErrorInUpcomingTxns,
			wantErr: false,
		},
		{
			name: "successfully fetched upcoming transactions (no add funds banner required)",
			args: args{
				ctx: context.Background(),
				req: defaultRequest,
			},
			setupMocks: func(upcomingTxnClient *upcomingTxnMockPb.MockUpcomingTransactionsClient, actorClient *actorMocksPb.MockActorClient, merchantClient *merchantMocksPb.MockMerchantServiceClient,
				mfCatalogClient *catalogMocksPb.MockCatalogManagerClient, depositClient *depositMocksPb.MockDepositClient, savingsClient *savingsMocksPb.MockSavingsClient, balanceClient *balanceMocksPb.MockBalanceClient, usstocksClient *usstocksMocksPb.MockCatalogManagerClient) {
				upcomingTxnClient.EXPECT().GetUpcomingTxnsForActor(gomock.Any(), defaultGetUpcomingTxnsForActorRequest).Return(defaultGetUpcomingTxnsForActorResponse, nil)
				merchantClient.EXPECT().GetMerchants(gomock.Any(), defaultGetMerchantsRequest).Return(defaultGetMerchantsResponse, nil)
				actorClient.EXPECT().GetEntityDetails(gomock.Any(), defaultGetEntityDetailsRequest).Return(defaultGetEntityDetailsResponse, nil)
				mfCatalogClient.EXPECT().GetMutualFunds(gomock.Any(), defaultGetMutualFundsRequest).Return(defaultGetMutualFundsResponse, nil)
				depositClient.EXPECT().GetById(gomock.Any(), defaultDepositsGetByIdRequest).Return(defaultDepositGetByIdResponse, nil)
				savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), defaultGetSavingsEssentialsRequest).Return(defaultGetSavingsEssentialsResponse, nil)
				balanceClient.EXPECT().GetAccountBalance(gomock.Any(), defaultGetBalanceRequest).Return(defaultGetBalanceResponseWithSufficientFunds, nil)
				usstocksClient.EXPECT().GetStocks(gomock.Any(), defaultGetStocksRequest).Return(defaultGetStocksResponse, nil)
			},
			want: defaultResponseWithNoAccountDetails,
		},
		{
			name: "successfully fetched upcoming transactions",
			args: args{
				ctx: context.Background(),
				req: defaultRequest,
			},
			setupMocks: func(upcomingTxnClient *upcomingTxnMockPb.MockUpcomingTransactionsClient, actorClient *actorMocksPb.MockActorClient, merchantClient *merchantMocksPb.MockMerchantServiceClient,
				mfCatalogClient *catalogMocksPb.MockCatalogManagerClient, depositClient *depositMocksPb.MockDepositClient, savingsClient *savingsMocksPb.MockSavingsClient, balanceClient *balanceMocksPb.MockBalanceClient, usstocksClient *usstocksMocksPb.MockCatalogManagerClient) {
				upcomingTxnClient.EXPECT().GetUpcomingTxnsForActor(gomock.Any(), defaultGetUpcomingTxnsForActorRequest).Return(defaultGetUpcomingTxnsForActorResponse, nil)
				merchantClient.EXPECT().GetMerchants(gomock.Any(), defaultGetMerchantsRequest).Return(defaultGetMerchantsResponse, nil)
				actorClient.EXPECT().GetEntityDetails(gomock.Any(), defaultGetEntityDetailsRequest).Return(defaultGetEntityDetailsResponse, nil)
				mfCatalogClient.EXPECT().GetMutualFunds(gomock.Any(), defaultGetMutualFundsRequest).Return(defaultGetMutualFundsResponse, nil)
				depositClient.EXPECT().GetById(gomock.Any(), defaultDepositsGetByIdRequest).Return(defaultDepositGetByIdResponse, nil)
				savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), defaultGetSavingsEssentialsRequest).Return(defaultGetSavingsEssentialsResponse, nil)
				balanceClient.EXPECT().GetAccountBalance(gomock.Any(), defaultGetBalanceRequest).Return(defaultGetBalanceResponse, nil)
				usstocksClient.EXPECT().GetStocks(gomock.Any(), defaultGetStocksRequest).Return(defaultGetStocksResponse, nil)
			},
			want: defaultResponse,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockUpcomingTxnClient := upcomingTxnMockPb.NewMockUpcomingTransactionsClient(ctr)
			mockActorClient := actorMocksPb.NewMockActorClient(ctr)
			mockMerchantClient := merchantMocksPb.NewMockMerchantServiceClient(ctr)
			mockCatalogMfClient := catalogMocksPb.NewMockCatalogManagerClient(ctr)
			mockDepositClient := depositMocksPb.NewMockDepositClient(ctr)
			mockSavingsClient := savingsMocksPb.NewMockSavingsClient(ctr)
			mockBalanceClient := balanceMocksPb.NewMockBalanceClient(ctr)
			mockUsstocksClient := usstocksMocksPb.NewMockCatalogManagerClient(ctr)
			tt.setupMocks(mockUpcomingTxnClient, mockActorClient, mockMerchantClient, mockCatalogMfClient, mockDepositClient, mockSavingsClient, mockBalanceClient, mockUsstocksClient)
			s := &Service{
				upcomingTxnClient:       mockUpcomingTxnClient,
				actorClient:             mockActorClient,
				merchantClient:          mockMerchantClient,
				mfCatalogManagerClient:  mockCatalogMfClient,
				depositClient:           mockDepositClient,
				savingsClient:           mockSavingsClient,
				paySavingsBalanceClient: mockBalanceClient,
				usStocksCatalogClient:   mockUsstocksClient,
			}
			got, err := s.GetUpcomingTransactions(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUpcomingTransactions() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !compareUpcomingTxns(got, tt.want) {
				t.Errorf("GetUpcomingTransactions(), got: %v, want: %v", got, tt.want)
			}
		})
	}
}

func compareUpcomingTxns(got, want *rePb.GetUpcomingTransactionsResponse) bool {
	if got == nil && want == nil {
		return true
	}
	if got == nil || want == nil {
		return false
	}
	if got.GetStatus().GetCode() != want.GetStatus().GetCode() {
		return false
	}
	if len(got.GetAccountDetails()) != len(want.GetAccountDetails()) {
		return false
	}
	for ind := range got.GetAccountDetails() {
		if !proto.Equal(got.GetAccountDetails()[ind], want.GetAccountDetails()[ind]) {
			return false
		}
	}
	if len(got.GetUpcomingTxns()) != len(want.GetUpcomingTxns()) {
		return false
	}
	gotUpcomingTxns := got.GetUpcomingTxns()
	wnatUpcomingTxns := want.GetUpcomingTxns()
	sort.Slice(gotUpcomingTxns, func(i, j int) bool {
		return gotUpcomingTxns[i].GetType().String() < gotUpcomingTxns[j].GetType().String()
	})
	sort.Slice(wnatUpcomingTxns, func(i, j int) bool {
		return wnatUpcomingTxns[i].GetType().String() < wnatUpcomingTxns[j].GetType().String()
	})
	for ind := range gotUpcomingTxns {
		if !proto.Equal(gotUpcomingTxns[ind], wnatUpcomingTxns[ind]) {
			return false
		}
	}
	return true
}
