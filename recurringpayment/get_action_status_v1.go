// nolint: goimports
package recurringpayment

import (
	"context"
	"errors"

	"github.com/epifi/be-common/pkg/epificontext"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	pb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/recurringpayment/internal/actionstatusfetcher"
)

// GetActionStatusV1 returns the current status of an already initiated action,
// it also returns the redirection deeplink to the next step to be performed by the user in case user intervention is needed to complete the action.
func (s *Service) GetActionStatusV1(ctx context.Context, req *pb.GetActionStatusV1Request) (*pb.GetActionStatusV1Response, error) {
	// get action from db
	action, err := s.recurringPaymentActionsDao.GetByClientRequestId(ctx, req.GetClientRequestId(), true)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "no recurring payment action found with given clientRequestId", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()))
		return &pb.GetActionStatusV1Response{Status: rpc.StatusRecordNotFound()}, nil
	case err != nil:
		logger.Error(ctx, "error fetching recurring payment action from db using client request id", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(err))
		return &pb.GetActionStatusV1Response{Status: rpc.StatusInternalWithDebugMsg("error fetching recurring payment action from db using client request id")}, nil
	}

	// get recurring payment type using recurring payment id
	// todo (utkarsh) : add field mask to fetch only recurring payment type from db
	recurringPayment, err := s.recurringPaymentDao.GetById(ctx, action.GetRecurringPaymentId())
	if err != nil {
		logger.Error(ctx, "error fetching recurring payment from db using id", zap.String(logger.ID, action.GetRecurringPaymentId()), zap.Error(err))
		return &pb.GetActionStatusV1Response{Status: rpc.StatusInternalWithDebugMsg("error fetching recurring payment from db using id")}, nil
	}
	ctx = epificontext.WithOwnership(ctx, recurringPayment.GetEntityOwnership())
	// get specific processor for getting the action status based on action and recurring payment type
	actionStatusFetcher := s.actionStatusFetcherFactory.GetActionStatusFetcher(ctx, action.GetAction(), recurringPayment.GetType(), recurringPayment.GetPaymentRoute())
	if actionStatusFetcher == nil {
		logger.Error(ctx, "no action status fetched exists for action and recurring payment type", zap.String(logger.ACTION_TYPE, action.GetAction().String()), zap.String(logger.RECURRING_PAYMENT_TYPE, recurringPayment.GetType().String()))
		return &pb.GetActionStatusV1Response{Status: rpc.StatusInternalWithDebugMsg("no action status fetched exists for action and recurring payment type")}, nil
	}

	// get action status, sub status and next step deeplink
	actionStatus, actionSubStatus, nextStepDeeplink, detailedStatus, err := actionStatusFetcher.GetActionStatusAndNextStepDeeplink(ctx, recurringPayment, action, &actionstatusfetcher.Metadata{
		PollAttempt: req.GetPollAttempt(),
	})
	if err != nil {
		logger.Error(ctx, "error fetching status for action", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(err))
		return &pb.GetActionStatusV1Response{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	return &pb.GetActionStatusV1Response{
		Status:                   rpc.StatusOk(),
		ActionStatus:             actionStatus,
		ActionSubStatus:          actionSubStatus,
		NextStepDeeplink:         nextStepDeeplink,
		ActionDetailedStatusInfo: detailedStatus,
	}, nil
}
