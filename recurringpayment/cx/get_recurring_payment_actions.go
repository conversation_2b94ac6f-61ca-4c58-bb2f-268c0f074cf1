package cx

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	orderPb "github.com/epifi/gamma/api/order"
	recurringPaymentPb "github.com/epifi/gamma/api/recurringpayment"
	cxPb "github.com/epifi/gamma/api/recurringpayment/cx"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
)

// GetRecurringPaymentActions returns the actions for given recurring payment id
func (s *Service) GetRecurringPaymentActions(ctx context.Context, req *cxPb.GetRecurringPaymentActionsRequest) (*cxPb.GetRecurringPaymentActionsResponse, error) {
	var (
		res = &cxPb.GetRecurringPaymentActionsResponse{}
	)
	recurringPayment, err := s.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	if err != nil {
		logger.Error(ctx, "error fetching recurring payment",
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) || errors.Is(err, gorm.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	token, err := pagination.GetPageToken(req.GetPageContext())
	if err != nil {
		logger.Error(ctx, "error unmarshalling token", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	actions, err := s.recurringPaymentActionsDao.GetActionsByRecurringPaymentId(
		ctx, req.GetRecurringPaymentId(), token.GetTimestamp().AsTime(),
		int32(req.GetPageContext().GetPageSize())+1, int32(token.GetOffset()),
		token.GetIsReverse(),
	)
	if err != nil {
		logger.Error(ctx, "error fetching recurring payment actions",
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) || errors.Is(err, gorm.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	rows, pageRes, err := pagination.NewPageCtxResp(
		token,
		int(req.GetPageContext().GetPageSize()),
		recurringPaymentPb.RecurringPaymentActionRows(actions),
	)
	if err != nil {
		logger.Error(ctx, "error fetching page context", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	actionDetails, err := s.convertToActionDetails(ctx, rows.(recurringPaymentPb.RecurringPaymentActionRows), recurringPayment, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error converting to action details", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	res.ActionDetails = actionDetails
	res.PageContext = pageRes
	res.Status = rpc.StatusOk()
	return res, nil
}

// convertToActionDetails converts the recurring payment action rows to action details
// nolint: funlen
func (s *Service) convertToActionDetails(
	ctx context.Context,
	actions []*recurringPaymentPb.RecurringPaymentsAction,
	recurringPayment *recurringPaymentPb.RecurringPayment,
	currentActorId string,
) ([]*cxPb.ActionDetails, error) {
	var (
		actionDetails                 []*cxPb.ActionDetails
		orderWithTransactionsReq      = &orderPb.GetOrdersWithTransactionsRequest{}
		clientReqIdToOrderWithTxnsMap = map[string]*orderPb.OrderWithTransactions{}
		getOrderRes                   = &orderPb.GetOrdersWithTransactionsResponse{}
		err                           error
	)
	for _, action := range actions {
		if action.GetAction() == recurringPaymentPb.Action_EXECUTE {
			orderIdentifier := &orderPb.OrderIdentifier{
				Identifier: &orderPb.OrderIdentifier_ClientReqId{ClientReqId: action.GetClientRequestId()},
			}
			orderWithTransactionsReq.OrderIdentifiers = append(orderWithTransactionsReq.OrderIdentifiers, orderIdentifier)
		}
	}

	if len(orderWithTransactionsReq.GetOrderIdentifiers()) != 0 {
		getOrderRes, err = s.orderClient.GetOrdersWithTransactions(ctx, orderWithTransactionsReq)
		if rpcErr := epifigrpc.RPCError(getOrderRes, err); rpcErr != nil {
			if !getOrderRes.GetStatus().IsRecordNotFound() {
				return nil, fmt.Errorf("error fetching order with transactions for executions: %w", rpcErr)
			}
		}
	}
	for _, orderWithTxn := range getOrderRes.GetOrderWithTransactions() {
		clientReqIdToOrderWithTxnsMap[orderWithTxn.GetOrder().GetClientReqId()] = orderWithTxn
	}
	for _, action := range actions {
		var detailedStatus []*cxPb.DetailedStatus
		actionDetail := &cxPb.ActionDetails{
			Action:    action.GetAction(),
			Status:    action.GetState(),
			ExpireAt:  action.GetExpireAt(),
			CreatedAt: action.GetCreatedAt(),
		}
		for _, status := range action.GetActionDetailedStatus().GetDetailedStatusList() {
			if recurringPayment.GetFromActorId() == currentActorId {
				detailedStatus = append(detailedStatus, &cxPb.DetailedStatus{
					StatusCode:  status.GetStatusCodePayer(),
					Description: status.GetStatusDescriptionPayer(),
				})
			} else {
				detailedStatus = append(detailedStatus, &cxPb.DetailedStatus{
					StatusCode:  status.GetStatusCodePayee(),
					Description: status.GetStatusDescriptionPayee(),
				})
			}
		}
		actionDetail.ActionDetailedStatus = detailedStatus
		if orderWithTxn, ok := clientReqIdToOrderWithTxnsMap[action.GetClientRequestId()]; ok {
			txns := orderWithTxn.GetTransactions()
			if len(txns) != 0 {
				actionDetail.Utr = txns[0].GetUtr()
			}
		}
		actionDetails = append(actionDetails, actionDetail)

	}
	return actionDetails, nil
}
