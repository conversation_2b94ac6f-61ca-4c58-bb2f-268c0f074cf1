package cx

import (
	"context"
	"errors"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	orderPb "github.com/epifi/gamma/api/order"
	recurringPaymentPb "github.com/epifi/gamma/api/recurringpayment"
	cxPb "github.com/epifi/gamma/api/recurringpayment/cx"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
	"github.com/epifi/gamma/recurringpayment/dao"
)

type Service struct {
	cxPb.UnimplementedCXServer
	recurringPaymentDao        dao.RecurringPaymentDao
	recurringPaymentActionsDao dao.RecurringPaymentsActionDao
	orderClient                orderPb.OrderServiceClient
}

func NewService(
	recurringPaymentDao dao.RecurringPaymentDao,
	recurringPaymentActionsDao dao.RecurringPaymentsActionDao,
	orderClient orderPb.OrderServiceClient,
) *Service {
	return &Service{
		recurringPaymentDao:        recurringPaymentDao,
		recurringPaymentActionsDao: recurringPaymentActionsDao,
		orderClient:                orderClient,
	}
}

// nolint: funlen
func (s *Service) GetRecurringPaymentDetailsByActorId(ctx context.Context, req *cxPb.GetRecurringPaymentDetailsByActorIdRequest) (*cxPb.GetRecurringPaymentDetailsByActorIdResponse, error) {
	var (
		res        *cxPb.GetRecurringPaymentDetailsByActorIdResponse
		startTime  time.Time
		endTime    time.Time
		descending bool
		offset     int32
	)
	res = &cxPb.GetRecurringPaymentDetailsByActorIdResponse{}
	startTime = *datetime.DateToTime(req.GetFromDate(), time.UTC)
	endTime = *datetime.DateToTime(req.GetToDate(), time.UTC)

	token, err := pagination.GetPageToken(req.GetPageContext())
	if err != nil {
		logger.Error(ctx, "invalid page token", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if req.GetPageContext().GetToken() != nil {
		switch req.GetPageContext().GetToken().(type) {
		case *rpc.PageContextRequest_BeforeToken:
			descending = true
			offset = int32(token.GetOffset())
			if token.GetTimestamp() != nil {
				endTime = token.GetTimestamp().AsTime()
			}
		case *rpc.PageContextRequest_AfterToken:
			descending = false
			offset = int32(token.GetOffset())
			if token.GetTimestamp() != nil {
				startTime = token.GetTimestamp().AsTime()
			}
		default:
			logger.Error(ctx, "PageToken does not match any type", zap.Any("request", req))
			res.Status = rpc.StatusInvalidArgument()
			return res, nil
		}
	}

	// passing page size + 1 as we need to fetch an extra row to create page context response
	recurringPayments, err := s.recurringPaymentDao.GetByActorIdInTimeWindow(
		ctx,
		req.GetActorId(),
		startTime,
		endTime,
		int32(req.GetPageContext().GetPageSize())+1,
		offset,
		descending,
		nil,
	)
	if err != nil {
		logger.Error(ctx, "error fetching recurring payments", zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) || errors.Is(err, gorm.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
		} else {
			res.Status = rpc.StatusInternal()
		}
		return res, nil
	}

	rows, pageRes, err := pagination.NewPageCtxResp(
		token,
		int(req.GetPageContext().GetPageSize()),
		recurringPaymentPb.RecurringPaymentRows(recurringPayments),
	)
	if err != nil {
		logger.Error(ctx, "error fetching page context", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	res.Details = convertToDetails(rows.(recurringPaymentPb.RecurringPaymentRows))
	res.PageContext = pageRes
	res.Status = rpc.StatusOk()
	return res, nil
}

func convertToDetails(recurringPayments []*recurringPaymentPb.RecurringPayment) []*cxPb.RecurringPaymentDetails {
	var (
		details []*cxPb.RecurringPaymentDetails
	)

	for _, recurringPayment := range recurringPayments {
		details = append(details, &cxPb.RecurringPaymentDetails{
			RecurringPaymentId: recurringPayment.GetId(),
			FromActorId:        recurringPayment.GetFromActorId(),
			ToActorId:          recurringPayment.GetToActorId(),
			PiFrom:             recurringPayment.GetPiFrom(),
			PiTo:               recurringPayment.GetPiTo(),
			Amount:             recurringPayment.GetAmount(),
			Type:               recurringPayment.GetType(),
			Interval:           recurringPayment.GetInterval(),
			RecurrenceRule:     recurringPayment.GetRecurrenceRule(),
			PartnerBank:        recurringPayment.GetPartnerBank(),
			State:              recurringPayment.GetState(),
			AmountType:         recurringPayment.GetAmountType(),
			ShareToPayee:       recurringPayment.GetShareToPayee(),
			PauseInterval:      recurringPayment.GetPauseInterval(),
			CreatedAt:          recurringPayment.GetCreatedAt(),
			UpdatedAt:          recurringPayment.GetUpdatedAt(),
		})
	}

	return details
}
