package cx_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"

	"google.golang.org/genproto/googleapis/type/date"

	"github.com/epifi/be-common/api/rpc"
	pb "github.com/epifi/gamma/api/recurringpayment"
	cxPb "github.com/epifi/gamma/api/recurringpayment/cx"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/gamma/recurringpayment/cx"
	"github.com/epifi/gamma/recurringpayment/dao/mocks"
)

func TestService_GetRecurringPaymentDetailsByActorId(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockRecurringPaymentDao := mocks.NewMockRecurringPaymentDao(ctr)

	svc := cx.NewService(mockRecurringPaymentDao, nil, nil)

	type mockGetByActorIdInTimeWindow struct {
		enable     bool
		actorId    string
		startTime  time.Time
		endTime    time.Time
		limit      int32
		offset     int32
		descending bool
		states     []pb.RecurringPaymentState
		details    []*pb.RecurringPayment
		err        error
	}
	fromDate := &date.Date{
		Year:  2021,
		Month: 10,
		Day:   10,
	}
	toDate := &date.Date{
		Year:  2021,
		Month: 11,
		Day:   11,
	}
	startTime := *datetime.DateToTime(fromDate, time.UTC)
	endTime := *datetime.DateToTime(toDate, time.UTC)
	tests := []struct {
		name                         string
		req                          *cxPb.GetRecurringPaymentDetailsByActorIdRequest
		res                          *cxPb.GetRecurringPaymentDetailsByActorIdResponse
		mockGetByActorIdInTimeWindow mockGetByActorIdInTimeWindow
		wantErr                      bool
	}{
		{
			name: "got recurring payment details successfully",
			req: &cxPb.GetRecurringPaymentDetailsByActorIdRequest{
				ActorId:  "actor-1",
				FromDate: fromDate,
				ToDate:   toDate,
				PageContext: &rpc.PageContextRequest{
					PageSize: 2,
				},
			},
			res: &cxPb.GetRecurringPaymentDetailsByActorIdResponse{
				Status: rpc.StatusOk(),
				Details: []*cxPb.RecurringPaymentDetails{
					{
						RecurringPaymentId: "id-1",
						FromActorId:        "actor-1",
						ToActorId:          "actor-2",
					},
					{
						RecurringPaymentId: "id-2",
						FromActorId:        "actor-1",
						ToActorId:          "actor-2",
					},
				},
				PageContext: &rpc.PageContextResponse{
					AfterToken: "eyJUaW1lc3RhbXAiOnt9LCJPZmZzZXQiOjIsIklzUmV2ZXJzZSI6ZmFsc2V9",
					HasAfter:   true,
				},
			},
			mockGetByActorIdInTimeWindow: mockGetByActorIdInTimeWindow{
				enable:    true,
				actorId:   "actor-1",
				startTime: startTime,
				endTime:   endTime,
				limit:     3,
				details: []*pb.RecurringPayment{
					{
						Id:          "id-1",
						FromActorId: "actor-1",
						ToActorId:   "actor-2",
					},
					{
						Id:          "id-2",
						FromActorId: "actor-1",
						ToActorId:   "actor-2",
					},
					{
						Id:          "id-3",
						FromActorId: "actor-1",
						ToActorId:   "actor-2",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "error while fetching recurring payments",
			req: &cxPb.GetRecurringPaymentDetailsByActorIdRequest{
				ActorId:  "actor-1",
				FromDate: fromDate,
				ToDate:   toDate,
				PageContext: &rpc.PageContextRequest{
					PageSize: 10,
				},
			},
			res: &cxPb.GetRecurringPaymentDetailsByActorIdResponse{
				Status: rpc.StatusInternal(),
			},
			mockGetByActorIdInTimeWindow: mockGetByActorIdInTimeWindow{
				enable:    true,
				actorId:   "actor-1",
				startTime: startTime,
				endTime:   endTime,
				limit:     11,
				err:       errors.New("error fetching recurring payment"),
			},
			wantErr: false,
		},
		{
			name: "record not found",
			req: &cxPb.GetRecurringPaymentDetailsByActorIdRequest{
				ActorId:  "actor-1",
				FromDate: fromDate,
				ToDate:   toDate,
				PageContext: &rpc.PageContextRequest{
					PageSize: 10,
				},
			},
			res: &cxPb.GetRecurringPaymentDetailsByActorIdResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			mockGetByActorIdInTimeWindow: mockGetByActorIdInTimeWindow{
				enable:    true,
				actorId:   "actor-1",
				startTime: startTime,
				endTime:   endTime,
				limit:     11,
				err:       epifierrors.ErrRecordNotFound,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByActorIdInTimeWindow.enable {
				mockRecurringPaymentDao.EXPECT().GetByActorIdInTimeWindow(
					context.Background(),
					tt.mockGetByActorIdInTimeWindow.actorId,
					tt.mockGetByActorIdInTimeWindow.startTime,
					tt.mockGetByActorIdInTimeWindow.endTime,
					tt.mockGetByActorIdInTimeWindow.limit,
					tt.mockGetByActorIdInTimeWindow.offset,
					tt.mockGetByActorIdInTimeWindow.descending,
					tt.mockGetByActorIdInTimeWindow.states,
				).Return(tt.mockGetByActorIdInTimeWindow.details, tt.mockGetByActorIdInTimeWindow.err)
			}

			got, err := svc.GetRecurringPaymentDetailsByActorId(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRecurringPaymentDetailsByActorId gotErr: %v wantErr: %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.res) {
				t.Errorf("GetRecurringPaymentDetailsByActorId got: %v want: %v", got, tt.res)
				return
			}
		})
	}
}
