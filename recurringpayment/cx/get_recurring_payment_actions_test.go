package cx_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"
	"github.com/stretchr/testify/assert"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	orderPb "github.com/epifi/gamma/api/order"
	mocks2 "github.com/epifi/gamma/api/order/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	recurringPaymentPb "github.com/epifi/gamma/api/recurringpayment"
	cxPb "github.com/epifi/gamma/api/recurringpayment/cx"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/pagination"
	"github.com/epifi/gamma/recurringpayment/cx"
	"github.com/epifi/gamma/recurringpayment/dao/mocks"
)

func TestService_GetRecurringPaymentActions(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockRecurringPaymentDao := mocks.NewMockRecurringPaymentDao(ctr)
	mockRecurringPaymentActionsDao := mocks.NewMockRecurringPaymentsActionDao(ctr)
	mockOrder := mocks2.NewMockOrderServiceClient(ctr)
	svc := cx.NewService(mockRecurringPaymentDao, mockRecurringPaymentActionsDao, mockOrder)

	currTimestamp := timestampPb.Now()
	currTime := currTimestamp.AsTime()

	token := &pagination.PageToken{
		Timestamp: currTimestamp,
		Offset:    10,
		IsReverse: false,
	}

	tokenStr, err := token.Marshal()
	assert.Nil(t, err)

	type mockGetById struct {
		enable             bool
		recurringPaymentId string
		recurringPayment   *recurringPaymentPb.RecurringPayment
		err                error
	}
	type mockGetActionsByRecurringPaymentId struct {
		enable             bool
		recurringPaymentId string
		startTime          time.Time
		limit              int32
		offset             int32
		descending         bool
		actions            []*recurringPaymentPb.RecurringPaymentsAction
		err                error
	}
	type mockGetOrdersWithTransactions struct {
		enable bool
		req    *orderPb.GetOrdersWithTransactionsRequest
		res    *orderPb.GetOrdersWithTransactionsResponse
		err    error
	}
	tests := []struct {
		name                               string
		req                                *cxPb.GetRecurringPaymentActionsRequest
		mockGetById                        mockGetById
		mockGetActionsByRecurringPaymentId mockGetActionsByRecurringPaymentId
		mockGetOrdersWithTransactions      mockGetOrdersWithTransactions
		res                                *cxPb.GetRecurringPaymentActionsResponse
		wantErr                            bool
	}{
		{
			name: "got actions successfully with current actor as payer",
			req: &cxPb.GetRecurringPaymentActionsRequest{
				RecurringPaymentId: "id-1",
				ActorId:            "actor-1",
				PageContext: &rpc.PageContextRequest{
					Token: &rpc.PageContextRequest_BeforeToken{
						BeforeToken: tokenStr,
					},
					PageSize: 10,
				},
			},
			mockGetById: mockGetById{
				enable:             true,
				recurringPaymentId: "id-1",
				recurringPayment: &recurringPaymentPb.RecurringPayment{
					Id:          "id-1",
					FromActorId: "actor-1",
				},
			},
			mockGetActionsByRecurringPaymentId: mockGetActionsByRecurringPaymentId{
				enable:             true,
				recurringPaymentId: "id-1",
				startTime:          currTime,
				limit:              11,
				offset:             10,
				actions: []*recurringPaymentPb.RecurringPaymentsAction{
					{
						RecurringPaymentId: "id-1",
						Action:             recurringPaymentPb.Action_MODIFY,
						ActionDetailedStatus: &recurringPaymentPb.ActionDetailedStatus{
							DetailedStatusList: []*recurringPaymentPb.ActionDetailedStatus_DetailedStatus{
								{
									StatusCodePayer:        "payer-code",
									StatusDescriptionPayer: "payer-description",
									StatusCodePayee:        "payee-code",
									StatusDescriptionPayee: "payee-description",
								},
							},
						},
					},
					{
						RecurringPaymentId: "id-2",
						Action:             recurringPaymentPb.Action_MODIFY,
						ActionMetadata: &recurringPaymentPb.ActionMetadata{
							ModifyActionMetadata: &recurringPaymentPb.ModifyActionMetaData{
								ExistingParams: &recurringPaymentPb.MutableParams{
									MaximumAllowedTxns: 10,
								},
								UpdatedParams: &recurringPaymentPb.MutableParams{
									MaximumAllowedTxns: 20,
								},
							},
						},
						ActionDetailedStatus: &recurringPaymentPb.ActionDetailedStatus{
							DetailedStatusList: []*recurringPaymentPb.ActionDetailedStatus_DetailedStatus{
								{
									StatusCodePayer:        "payer-code",
									StatusDescriptionPayer: "payer-description",
									StatusCodePayee:        "payee-code",
									StatusDescriptionPayee: "payee-description",
								},
							},
						},
					},
				},
			},
			res: &cxPb.GetRecurringPaymentActionsResponse{
				Status: rpc.StatusOk(),
				ActionDetails: []*cxPb.ActionDetails{
					{
						Action: recurringPaymentPb.Action_MODIFY,
						ActionDetailedStatus: []*cxPb.DetailedStatus{
							{
								StatusCode:  "payer-code",
								Description: "payer-description",
							},
						},
					},
					{
						Action: recurringPaymentPb.Action_MODIFY,
						ActionDetailedStatus: []*cxPb.DetailedStatus{
							{
								StatusCode:  "payer-code",
								Description: "payer-description",
							},
						},
					},
				},
				PageContext: &rpc.PageContextResponse{
					BeforeToken: "eyJUaW1lc3RhbXAiOnt9LCJPZmZzZXQiOjIsIklzUmV2ZXJzZSI6dHJ1ZX0=",
					HasBefore:   true,
				},
			},
		},
		{
			name: "got actions successfully with current actor as payee",
			req: &cxPb.GetRecurringPaymentActionsRequest{
				RecurringPaymentId: "id-1",
				ActorId:            "actor-2",
				PageContext: &rpc.PageContextRequest{
					Token: &rpc.PageContextRequest_BeforeToken{
						BeforeToken: tokenStr,
					},
					PageSize: 10,
				},
			},
			mockGetById: mockGetById{
				enable:             true,
				recurringPaymentId: "id-1",
				recurringPayment: &recurringPaymentPb.RecurringPayment{
					Id:          "id-1",
					FromActorId: "actor-1",
				},
			},
			mockGetActionsByRecurringPaymentId: mockGetActionsByRecurringPaymentId{
				enable:             true,
				recurringPaymentId: "id-1",
				startTime:          currTime,
				limit:              11,
				offset:             10,
				descending:         false,
				actions: []*recurringPaymentPb.RecurringPaymentsAction{
					{
						RecurringPaymentId: "id-1",
						Action:             recurringPaymentPb.Action_CREATE,
						ActionDetailedStatus: &recurringPaymentPb.ActionDetailedStatus{
							DetailedStatusList: []*recurringPaymentPb.ActionDetailedStatus_DetailedStatus{
								{
									StatusCodePayer:        "payer-code",
									StatusDescriptionPayer: "payer-description",
									StatusCodePayee:        "payee-code",
									StatusDescriptionPayee: "payee-description",
								},
							},
						},
					},
					{
						RecurringPaymentId: "id-2",
						Action:             recurringPaymentPb.Action_MODIFY,
						ActionMetadata: &recurringPaymentPb.ActionMetadata{
							ModifyActionMetadata: &recurringPaymentPb.ModifyActionMetaData{
								ExistingParams: &recurringPaymentPb.MutableParams{
									MaximumAllowedTxns: 10,
								},
								UpdatedParams: &recurringPaymentPb.MutableParams{
									MaximumAllowedTxns: 20,
								},
							},
						},
						ActionDetailedStatus: &recurringPaymentPb.ActionDetailedStatus{
							DetailedStatusList: []*recurringPaymentPb.ActionDetailedStatus_DetailedStatus{
								{
									StatusCodePayer:        "payer-code",
									StatusDescriptionPayer: "payer-description",
									StatusCodePayee:        "payee-code",
									StatusDescriptionPayee: "payee-description",
								},
							},
						},
					},
				},
				err: nil,
			},
			res: &cxPb.GetRecurringPaymentActionsResponse{
				Status: rpc.StatusOk(),
				ActionDetails: []*cxPb.ActionDetails{
					{
						Action: recurringPaymentPb.Action_CREATE,
						ActionDetailedStatus: []*cxPb.DetailedStatus{
							{
								StatusCode:  "payee-code",
								Description: "payee-description",
							},
						},
					},
					{
						Action: recurringPaymentPb.Action_MODIFY,
						ActionDetailedStatus: []*cxPb.DetailedStatus{
							{
								StatusCode:  "payee-code",
								Description: "payee-description",
							},
						},
					},
				},
				PageContext: &rpc.PageContextResponse{
					BeforeToken: "eyJUaW1lc3RhbXAiOnt9LCJPZmZzZXQiOjIsIklzUmV2ZXJzZSI6dHJ1ZX0=",
					HasBefore:   true,
				},
			},
		},
		{
			name: "error fetching recurring payment",
			req: &cxPb.GetRecurringPaymentActionsRequest{
				RecurringPaymentId: "id-1",
				ActorId:            "actor-1",
			},
			mockGetById: mockGetById{
				enable:             true,
				recurringPaymentId: "id-1",
				err:                errors.New("error fetching recurring payment id"),
			},
			res: &cxPb.GetRecurringPaymentActionsResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "no actions found",
			req: &cxPb.GetRecurringPaymentActionsRequest{
				RecurringPaymentId: "id-1",
				ActorId:            "actor-1",
				PageContext: &rpc.PageContextRequest{
					Token: &rpc.PageContextRequest_BeforeToken{
						BeforeToken: tokenStr,
					},
					PageSize: 10,
				},
			},
			mockGetById: mockGetById{
				enable:             true,
				recurringPaymentId: "id-1",
				recurringPayment: &recurringPaymentPb.RecurringPayment{
					Id:          "id-1",
					FromActorId: "actor-1",
				},
			},
			mockGetActionsByRecurringPaymentId: mockGetActionsByRecurringPaymentId{
				enable:             true,
				recurringPaymentId: "id-1",
				startTime:          currTime,
				limit:              11,
				offset:             10,
				err:                epifierrors.ErrRecordNotFound,
			},
			res: &cxPb.GetRecurringPaymentActionsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
		{
			name: "got actions successfully with current actor as payer with executions",
			req: &cxPb.GetRecurringPaymentActionsRequest{
				RecurringPaymentId: "id-1",
				ActorId:            "actor-1",
				PageContext: &rpc.PageContextRequest{
					Token: &rpc.PageContextRequest_BeforeToken{
						BeforeToken: tokenStr,
					},
					PageSize: 10,
				},
			},
			mockGetById: mockGetById{
				enable:             true,
				recurringPaymentId: "id-1",
				recurringPayment: &recurringPaymentPb.RecurringPayment{
					Id:          "id-1",
					FromActorId: "actor-1",
				},
			},
			mockGetActionsByRecurringPaymentId: mockGetActionsByRecurringPaymentId{
				enable:             true,
				recurringPaymentId: "id-1",
				startTime:          currTime,
				limit:              11,
				offset:             10,
				actions: []*recurringPaymentPb.RecurringPaymentsAction{
					{
						RecurringPaymentId: "id-1",
						Action:             recurringPaymentPb.Action_EXECUTE,
						ClientRequestId:    "client-req-id-1",
						ActionDetailedStatus: &recurringPaymentPb.ActionDetailedStatus{
							DetailedStatusList: []*recurringPaymentPb.ActionDetailedStatus_DetailedStatus{
								{
									StatusCodePayer:        "payer-code",
									StatusDescriptionPayer: "payer-description",
									StatusCodePayee:        "payee-code",
									StatusDescriptionPayee: "payee-description",
								},
							},
						},
					},
					{
						RecurringPaymentId: "id-2",
						Action:             recurringPaymentPb.Action_MODIFY,
						ActionMetadata: &recurringPaymentPb.ActionMetadata{
							ModifyActionMetadata: &recurringPaymentPb.ModifyActionMetaData{
								ExistingParams: &recurringPaymentPb.MutableParams{
									MaximumAllowedTxns: 10,
								},
								UpdatedParams: &recurringPaymentPb.MutableParams{
									MaximumAllowedTxns: 20,
								},
							},
						},
						ActionDetailedStatus: &recurringPaymentPb.ActionDetailedStatus{
							DetailedStatusList: []*recurringPaymentPb.ActionDetailedStatus_DetailedStatus{
								{
									StatusCodePayer:        "payer-code",
									StatusDescriptionPayer: "payer-description",
									StatusCodePayee:        "payee-code",
									StatusDescriptionPayee: "payee-description",
								},
							},
						},
					},
				},
			},
			mockGetOrdersWithTransactions: mockGetOrdersWithTransactions{
				enable: true,
				req: &orderPb.GetOrdersWithTransactionsRequest{OrderIdentifiers: []*orderPb.OrderIdentifier{
					{
						Identifier: &orderPb.OrderIdentifier_ClientReqId{ClientReqId: "client-req-id-1"},
					},
				}},
				res: &orderPb.GetOrdersWithTransactionsResponse{
					Status: rpc.StatusOk(),
					OrderWithTransactions: []*orderPb.OrderWithTransactions{
						{
							Order: &orderPb.Order{
								ClientReqId: "client-req-id-1",
							},
							Transactions: []*paymentPb.Transaction{
								{
									Utr: "utr-1",
								},
							},
						},
					},
				},
				err: nil,
			},
			res: &cxPb.GetRecurringPaymentActionsResponse{
				Status: rpc.StatusOk(),
				ActionDetails: []*cxPb.ActionDetails{
					{
						Action: recurringPaymentPb.Action_EXECUTE,
						ActionDetailedStatus: []*cxPb.DetailedStatus{
							{
								StatusCode:  "payer-code",
								Description: "payer-description",
							},
						},
						Utr: "utr-1",
					},
					{
						Action: recurringPaymentPb.Action_MODIFY,
						ActionDetailedStatus: []*cxPb.DetailedStatus{
							{
								StatusCode:  "payer-code",
								Description: "payer-description",
							},
						},
					},
				},
				PageContext: &rpc.PageContextResponse{
					BeforeToken: "eyJUaW1lc3RhbXAiOnt9LCJPZmZzZXQiOjIsIklzUmV2ZXJzZSI6dHJ1ZX0=",
					HasBefore:   true,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetById.enable {
				mockRecurringPaymentDao.EXPECT().GetById(context.Background(), tt.mockGetById.recurringPaymentId).
					Return(tt.mockGetById.recurringPayment, tt.mockGetById.err)
			}
			if tt.mockGetActionsByRecurringPaymentId.enable {
				mockRecurringPaymentActionsDao.EXPECT().GetActionsByRecurringPaymentId(
					context.Background(),
					tt.mockGetActionsByRecurringPaymentId.recurringPaymentId,
					tt.mockGetActionsByRecurringPaymentId.startTime,
					tt.mockGetActionsByRecurringPaymentId.limit,
					tt.mockGetActionsByRecurringPaymentId.offset,
					tt.mockGetActionsByRecurringPaymentId.descending,
				).Return(tt.mockGetActionsByRecurringPaymentId.actions, tt.mockGetActionsByRecurringPaymentId.err)
			}
			if tt.mockGetOrdersWithTransactions.enable {
				mockOrder.EXPECT().GetOrdersWithTransactions(gomock.Any(), tt.mockGetOrdersWithTransactions.req).
					Return(tt.mockGetOrdersWithTransactions.res, tt.mockGetOrdersWithTransactions.err)
			}

			got, err := svc.GetRecurringPaymentActions(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRecurringPaymentActions gotErr :%v wantErr :%v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.res) {
				t.Errorf("GetRecurringPaymentActions got :%v want :%v", got, tt.res)
				return
			}
		})
	}
}
