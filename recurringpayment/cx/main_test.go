package cx_test

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/recurringpayment/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
// nolint:dogsled
func TestMain(m *testing.M) {
	flag.Parse()

	var teardown func()
	_, _, _, _, teardown = test.InitTestServerV2()

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
