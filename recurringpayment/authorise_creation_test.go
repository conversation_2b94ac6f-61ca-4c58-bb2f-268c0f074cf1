package recurringpayment

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"
	"github.com/samber/lo"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/anypb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/money"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	actorPb "github.com/epifi/gamma/api/actor"
	mocks2 "github.com/epifi/gamma/api/actor/mocks"
	"github.com/epifi/gamma/api/frontend/deeplink"
	orderPb "github.com/epifi/gamma/api/order"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	pb "github.com/epifi/gamma/api/recurringpayment"
	payloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	"github.com/epifi/gamma/api/recurringpayment/standinginstruction/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	internalMocks "github.com/epifi/gamma/recurringpayment/internal/mocks"
)

func TestService_AuthoriseRecurringPaymentCreation(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
	mockRecurringPaymentVendorDetailsDao := daoMocks.NewMockRecurringPaymentsVendorDetailsDao(ctr)
	mockSIClient := mocks.NewMockStandingInstructionServiceClient(ctr)
	mockOrderClient := orderMocks.NewMockOrderServiceClient(ctr)
	mockRecurringPaymentActionDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
	mockActorClient := mocks2.NewMockActorClient(ctr)
	mockCelestialProcessor := internalMocks.NewMockCelestialProcessor(ctr)

	payload, _ := protojson.Marshal(&payloadPb.CreateRecurringPaymentAuthSignal{})

	startDate := timestampPb.New(time.Now())
	endDate := timestampPb.New(time.Now().Add(48 * time.Hour))

	svc := NewService(mockRecurringPaymentDao, mockOrderClient, mockSIClient, mockRecurringPaymentActionDao, nil, mockActorClient, nil, nil, nil, conf, nil, nil, nil, nil, nil, nil, nil, nil, dynamicConf.FeatureFlags(), mockCelestialProcessor, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, mockRecurringPaymentVendorDetailsDao, nil, nil, nil, nil)
	recurringPayment := &pb.RecurringPayment{
		Id:          "id-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_CREATION_AUTHORISED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
	}
	recurringPayment2 := &pb.RecurringPayment{
		Id:          "id-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_CREATION_QUEUED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
	}
	action := &pb.RecurringPaymentsAction{
		RecurringPaymentId: "rp-1",
		ClientRequestId:    "client-req-1",
		Action:             pb.Action_CREATE,
		State:              pb.ActionState_ACTION_CREATED,
		VendorRequestId:    "vendor-id",
	}
	postAuthDeeplink := &deeplink.Deeplink{
		Screen:        deeplink.Screen_AUTH_STATUS_POLL_SCREEN,
		ScreenOptions: &deeplink.Deeplink_AuthStatusPollScreenOptions{},
	}

	postExecuteDeeplink := &deeplink.Deeplink{
		Screen:        deeplink.Screen_AUTH_STATUS_POLL_SCREEN,
		ScreenOptions: &deeplink.Deeplink_AuthStatusPollScreenOptions{},
	}

	postAuthDeeplinkAny := lo.Must(anypb.New(postAuthDeeplink))
	postExecuteDeeplinkAny := lo.Must(anypb.New(postExecuteDeeplink))
	actionWithDeeplinks := &pb.RecurringPaymentsAction{
		RecurringPaymentId: "rp-1",
		ClientRequestId:    "client-req-1",
		Action:             pb.Action_CREATE,
		State:              pb.ActionState_ACTION_CREATED,
		VendorRequestId:    "vendor-id",
		PostActionDeeplink: postExecuteDeeplinkAny,
		PostAuthDeeplink:   postAuthDeeplinkAny,
	}
	tests := []struct {
		name           string
		req            *pb.AuthoriseRecurringPaymentCreationRequest
		setupMockCalls func()
		want           *pb.AuthoriseRecurringPaymentCreationResponse
		wantErr        bool
	}{
		{
			name: "recurring payment creation failed due to empty client req id",
			req: &pb.AuthoriseRecurringPaymentCreationRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "asdfg"}},
				CurrentActorId:     "actor-1",
			},
			setupMockCalls: func() {},
			want: &pb.AuthoriseRecurringPaymentCreationResponse{
				Status: rpc.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "failed to initiate, failed to fetch workflow for given client req ID",
			req: &pb.AuthoriseRecurringPaymentCreationRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "asdfg"}},
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment2, nil)
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(action, nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).
					Return(nil, fmt.Errorf("record not found %v %w", epifierrors.ErrRecordNotFound, rpc.StatusAsError(rpc.StatusRecordNotFound())))
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: recurringPayment.GetFromActorId()}).
					Return(&actorPb.GetActorByIdResponse{
						Status: rpc.StatusOk(),
						Actor:  &types.Actor{Type: types.Actor_USER},
					}, nil)
			},
			want:    &pb.AuthoriseRecurringPaymentCreationResponse{Status: rpc.StatusRecordNotFound()},
			wantErr: false,
		},
		{
			name: "failed to initiate, workflow timed out after 10 mins",
			req: &pb.AuthoriseRecurringPaymentCreationRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "asdfg"}},
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				recurringPayment2.State = pb.RecurringPaymentState_CREATION_QUEUED
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment2, nil)
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(action, nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).
					Return(&celestialPb.WorkflowRequest{
						Id:        "workflow_req_id",
						CreatedAt: timestampPb.New(time.Now().Add(-15 * time.Minute)),
					}, nil)

				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: recurringPayment.GetFromActorId()}).
					Return(&actorPb.GetActorByIdResponse{
						Status: rpc.StatusOk(),
						Actor:  &types.Actor{Type: types.Actor_USER},
					}, nil)
			},
			want:    &pb.AuthoriseRecurringPaymentCreationResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "failed to initiate, failed to signal workflow from celestial",
			req: &pb.AuthoriseRecurringPaymentCreationRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "asdfg"}},
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				recurringPayment2.State = pb.RecurringPaymentState_CREATION_QUEUED
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment2, nil)
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(action, nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).
					Return(&celestialPb.WorkflowRequest{
						Id:        "workflow_req_id",
						CreatedAt: timestampPb.Now(),
					}, nil)
				mockCelestialProcessor.EXPECT().SignalWorkflow(gomock.Any(), "client-req-1", string(rpNs.CreateRecurringPaymentAuthSignal), workflowPb.Client_RECURRING_PAYMENT, payload, gomock.Any(), gomock.Any()).Return(errors.New("server issue"))
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: recurringPayment.GetFromActorId()}).
					Return(&actorPb.GetActorByIdResponse{
						Status: rpc.StatusOk(),
						Actor:  &types.Actor{Type: types.Actor_USER},
					}, nil)
			},
			want:    &pb.AuthoriseRecurringPaymentCreationResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "domain service initiation via Celestial failed",
			req: &pb.AuthoriseRecurringPaymentCreationRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "asdfg"}},
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				recurringPayment2.State = pb.RecurringPaymentState_CREATION_QUEUED
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment2, nil)
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(action, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: recurringPayment.GetFromActorId()}).
					Return(&actorPb.GetActorByIdResponse{
						Status: rpc.StatusOk(),
						Actor:  &types.Actor{Type: types.Actor_USER},
					}, nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).
					Return(&celestialPb.WorkflowRequest{
						Id:        "workflow_req_id",
						CreatedAt: timestampPb.Now(),
					}, nil)
				mockCelestialProcessor.EXPECT().SignalWorkflow(gomock.Any(), "client-req-1", string(rpNs.CreateRecurringPaymentAuthSignal), workflowPb.Client_RECURRING_PAYMENT, payload, gomock.Any(), gomock.Any()).Return(nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment2,
					nil, pb.RecurringPaymentState_CREATION_QUEUED, pb.RecurringPaymentState_CREATION_AUTHORISED).Return(nil)

				mockSIClient.EXPECT().AuthoriseCreation(gomock.Any(), &siPb.AuthoriseCreationRequest{
					RecurringPaymentId:       recurringPayment2.Id,
					FromActorId:              recurringPayment2.FromActorId,
					ToActorId:                recurringPayment2.ToActorId,
					PiFrom:                   recurringPayment2.PiFrom,
					PiTo:                     recurringPayment2.PiTo,
					MaximumAmountLimit:       recurringPayment2.Amount,
					Interval:                 recurringPayment2.Interval,
					AllowedFrequency:         recurringPayment2.RecurrenceRule.AllowedFrequency,
					MaximumAllowedTxns:       recurringPayment2.MaximumAllowedTxns,
					PartnerBank:              recurringPayment2.PartnerBank,
					PreferredPaymentProtocol: recurringPayment2.PreferredPaymentProtocol,
					PartnerSdkCredBlock:      "asdfg",
				}).Return(&siPb.AuthoriseCreationResponse{
					ActionDetailedStatus: &pb.ActionDetailedStatus{
						DetailedStatusList: []*pb.ActionDetailedStatus_DetailedStatus{
							{
								SystemErrorDescription: "error",
								ErrorCategory:          pb.ActionDetailedStatus_DetailedStatus_SYSTEM_ERROR,
								Api:                    pb.ActionDetailedStatus_DetailedStatus_CREATE_SI,
							},
						},
					},
				}, fmt.Errorf("error"))
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment,
					nil, pb.RecurringPaymentState_CREATION_AUTHORISED, pb.RecurringPaymentState_FAILED).Return(nil)
				mockRecurringPaymentActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), action, []pb.RecurringPaymentActionFieldMask{pb.RecurringPaymentActionFieldMask_ACTION_DETAILED_STATUS},
					pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_FAILURE).Return(nil)

			},
			want: &pb.AuthoriseRecurringPaymentCreationResponse{Status: rpc.StatusFromError(errors.New("error")),
				ActionDetailedStatus: &pb.ActionDetailedStatus_DetailedStatus{
					SystemErrorDescription: "error",
					ErrorCategory:          pb.ActionDetailedStatus_DetailedStatus_SYSTEM_ERROR,
					Api:                    pb.ActionDetailedStatus_DetailedStatus_CREATE_SI,
				},
			},
			wantErr: false,
		},
		{
			name: "initiated successfully via Celestial",
			req: &pb.AuthoriseRecurringPaymentCreationRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "asdfg"}},
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				action.ActionDetailedStatus = nil
				recurringPayment2.State = pb.RecurringPaymentState_CREATION_QUEUED
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment2, nil)
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(action, nil)
				mockSIClient.EXPECT().AuthoriseCreation(gomock.Any(), &siPb.AuthoriseCreationRequest{
					RecurringPaymentId:       recurringPayment.Id,
					FromActorId:              recurringPayment.GetFromActorId(),
					ToActorId:                recurringPayment.GetToActorId(),
					PiFrom:                   recurringPayment.PiFrom,
					PiTo:                     recurringPayment.PiTo,
					MaximumAmountLimit:       recurringPayment.Amount,
					Interval:                 recurringPayment.Interval,
					AllowedFrequency:         recurringPayment.RecurrenceRule.AllowedFrequency,
					MaximumAllowedTxns:       recurringPayment.MaximumAllowedTxns,
					PartnerBank:              recurringPayment.PartnerBank,
					PreferredPaymentProtocol: recurringPayment.PreferredPaymentProtocol,
					PartnerSdkCredBlock:      "asdfg",
				}).Return(&siPb.AuthoriseCreationResponse{Status: rpc.StatusOk()}, nil)

				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).
					Return(&celestialPb.WorkflowRequest{
						Id:        "workflow_req_id",
						CreatedAt: timestampPb.Now(),
					}, nil)
				mockCelestialProcessor.EXPECT().SignalWorkflow(gomock.Any(), "client-req-1", string(rpNs.CreateRecurringPaymentAuthSignal), workflowPb.Client_RECURRING_PAYMENT, payload, gomock.Any(), gomock.Any()).Return(nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment2, nil,
					pb.RecurringPaymentState_CREATION_QUEUED, pb.RecurringPaymentState_CREATION_AUTHORISED).Return(nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment, nil,
					pb.RecurringPaymentState_CREATION_AUTHORISED, pb.RecurringPaymentState_ACTIVATED).Return(nil)
				mockRecurringPaymentActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), action, []pb.RecurringPaymentActionFieldMask{pb.RecurringPaymentActionFieldMask_ACTION_DETAILED_STATUS},
					pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_SUCCESS).Return(nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: recurringPayment.GetFromActorId()}).
					Return(&actorPb.GetActorByIdResponse{
						Status: rpc.StatusOk(),
						Actor:  &types.Actor{Type: types.Actor_USER},
					}, nil)
			},
			want:    &pb.AuthoriseRecurringPaymentCreationResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
		{
			name: "initiated successfully via OMS",
			req: &pb.AuthoriseRecurringPaymentCreationRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "asdfg"}},
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				recurringPayment2.State = pb.RecurringPaymentState_CREATION_QUEUED
				action.ActionDetailedStatus = nil
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment2, nil)
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(action, nil)
				mockSIClient.EXPECT().AuthoriseCreation(gomock.Any(), &siPb.AuthoriseCreationRequest{
					RecurringPaymentId:       recurringPayment.Id,
					FromActorId:              recurringPayment.GetFromActorId(),
					ToActorId:                recurringPayment.GetToActorId(),
					PiFrom:                   recurringPayment.PiFrom,
					PiTo:                     recurringPayment.PiTo,
					MaximumAmountLimit:       recurringPayment.Amount,
					Interval:                 recurringPayment.Interval,
					AllowedFrequency:         recurringPayment.RecurrenceRule.AllowedFrequency,
					MaximumAllowedTxns:       recurringPayment.MaximumAllowedTxns,
					PartnerBank:              recurringPayment.PartnerBank,
					PreferredPaymentProtocol: recurringPayment.PreferredPaymentProtocol,
					PartnerSdkCredBlock:      "asdfg",
				}).Return(&siPb.AuthoriseCreationResponse{Status: rpc.StatusOk()}, nil)

				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{Status: rpc.StatusOk()}, nil)
				mockOrderClient.EXPECT().InitiateOrderProcessing(gomock.Any(), &orderPb.InitiateOrderProcessingRequest{Identifier: &orderPb.InitiateOrderProcessingRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.InitiateOrderProcessingResponse{Status: rpc.StatusOk()}, nil)

				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment2, nil,
					pb.RecurringPaymentState_CREATION_QUEUED, pb.RecurringPaymentState_CREATION_AUTHORISED).Return(nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment, nil,
					pb.RecurringPaymentState_CREATION_AUTHORISED, pb.RecurringPaymentState_ACTIVATED).Return(nil)
				mockRecurringPaymentActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), action, []pb.RecurringPaymentActionFieldMask{pb.RecurringPaymentActionFieldMask_ACTION_DETAILED_STATUS},
					pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_SUCCESS).Return(nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: recurringPayment.GetFromActorId()}).
					Return(&actorPb.GetActorByIdResponse{
						Status: rpc.StatusOk(),
						Actor:  &types.Actor{Type: types.Actor_USER},
					}, nil)
			},
			want:    &pb.AuthoriseRecurringPaymentCreationResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
		{
			name: "domain service initiation via OMS failed",
			req: &pb.AuthoriseRecurringPaymentCreationRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "asdfg"}},
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				recurringPayment2.State = pb.RecurringPaymentState_CREATION_QUEUED
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(false, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment2, nil)
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(action, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: recurringPayment.GetFromActorId()}).
					Return(&actorPb.GetActorByIdResponse{
						Status: rpc.StatusOk(),
						Actor:  &types.Actor{Type: types.Actor_USER},
					}, nil)

				mockOrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.GetOrderResponse{Status: rpc.StatusOk()}, nil)
				mockOrderClient.EXPECT().InitiateOrderProcessing(gomock.Any(), &orderPb.InitiateOrderProcessingRequest{Identifier: &orderPb.InitiateOrderProcessingRequest_ClientReqId{ClientReqId: "client-req-1"}}).Return(&orderPb.InitiateOrderProcessingResponse{Status: rpc.StatusOk()}, nil)

				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment2,
					nil, pb.RecurringPaymentState_CREATION_QUEUED, pb.RecurringPaymentState_CREATION_AUTHORISED).Return(nil)

				mockSIClient.EXPECT().AuthoriseCreation(gomock.Any(), &siPb.AuthoriseCreationRequest{
					RecurringPaymentId:       recurringPayment2.Id,
					FromActorId:              recurringPayment2.FromActorId,
					ToActorId:                recurringPayment2.ToActorId,
					PiFrom:                   recurringPayment2.PiFrom,
					PiTo:                     recurringPayment2.PiTo,
					MaximumAmountLimit:       recurringPayment2.Amount,
					Interval:                 recurringPayment2.Interval,
					AllowedFrequency:         recurringPayment2.RecurrenceRule.AllowedFrequency,
					MaximumAllowedTxns:       recurringPayment2.MaximumAllowedTxns,
					PartnerBank:              recurringPayment2.PartnerBank,
					PreferredPaymentProtocol: recurringPayment2.PreferredPaymentProtocol,
					PartnerSdkCredBlock:      "asdfg",
				}).Return(&siPb.AuthoriseCreationResponse{
					ActionDetailedStatus: &pb.ActionDetailedStatus{
						DetailedStatusList: []*pb.ActionDetailedStatus_DetailedStatus{
							{
								SystemErrorDescription: "error",
								ErrorCategory:          pb.ActionDetailedStatus_DetailedStatus_SYSTEM_ERROR,
								Api:                    pb.ActionDetailedStatus_DetailedStatus_CREATE_SI,
							},
						},
					},
				}, fmt.Errorf("error"))
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment,
					nil, pb.RecurringPaymentState_CREATION_AUTHORISED, pb.RecurringPaymentState_FAILED).Return(nil)
				mockRecurringPaymentActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), action, []pb.RecurringPaymentActionFieldMask{pb.RecurringPaymentActionFieldMask_ACTION_DETAILED_STATUS},
					pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_FAILURE).Return(nil)

			},
			want: &pb.AuthoriseRecurringPaymentCreationResponse{Status: rpc.StatusFromError(errors.New("error")),
				ActionDetailedStatus: &pb.ActionDetailedStatus_DetailedStatus{
					SystemErrorDescription: "error",
					ErrorCategory:          pb.ActionDetailedStatus_DetailedStatus_SYSTEM_ERROR,
					Api:                    pb.ActionDetailedStatus_DetailedStatus_CREATE_SI,
				},
			},
			wantErr: false,
		},
		{
			name: "error in fetching recurring payment",
			req: &pb.AuthoriseRecurringPaymentCreationRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "asdfg"}},
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(nil, epifierrors.ErrInvalidSQL)
			},
			want:    &pb.AuthoriseRecurringPaymentCreationResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "authorise creation with post auth deeplink",
			req: &pb.AuthoriseRecurringPaymentCreationRequest{
				RecurringPaymentId: "rp-1",
				Credential:         &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "asdfg"}},
				ClientRequestId:    "client-req-1",
				CurrentActorId:     "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				action.ActionDetailedStatus = nil
				recurringPayment2.State = pb.RecurringPaymentState_CREATION_QUEUED
				err := dynamicConf.FeatureFlags().SetEnableRecurringPaymentCreationViaCelestial(true, false, nil)
				if err != nil {
					return
				}
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment2, nil)
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(actionWithDeeplinks, nil)
				mockSIClient.EXPECT().AuthoriseCreation(gomock.Any(), &siPb.AuthoriseCreationRequest{
					RecurringPaymentId:       recurringPayment.Id,
					FromActorId:              recurringPayment.GetFromActorId(),
					ToActorId:                recurringPayment.GetToActorId(),
					PiFrom:                   recurringPayment.PiFrom,
					PiTo:                     recurringPayment.PiTo,
					MaximumAmountLimit:       recurringPayment.Amount,
					Interval:                 recurringPayment.Interval,
					AllowedFrequency:         recurringPayment.RecurrenceRule.AllowedFrequency,
					MaximumAllowedTxns:       recurringPayment.MaximumAllowedTxns,
					PartnerBank:              recurringPayment.PartnerBank,
					PreferredPaymentProtocol: recurringPayment.PreferredPaymentProtocol,
					PartnerSdkCredBlock:      "asdfg",
				}).Return(&siPb.AuthoriseCreationResponse{Status: rpc.StatusOk()}, nil)

				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).
					Return(&celestialPb.WorkflowRequest{
						Id:        "workflow_req_id",
						CreatedAt: timestampPb.Now(),
					}, nil)
				mockCelestialProcessor.EXPECT().SignalWorkflow(gomock.Any(), "client-req-1", string(rpNs.CreateRecurringPaymentAuthSignal), workflowPb.Client_RECURRING_PAYMENT, payload, gomock.Any(), gomock.Any()).Return(nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment2, nil,
					pb.RecurringPaymentState_CREATION_QUEUED, pb.RecurringPaymentState_CREATION_AUTHORISED).Return(nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment, nil,
					pb.RecurringPaymentState_CREATION_AUTHORISED, pb.RecurringPaymentState_ACTIVATED).Return(nil)
				mockRecurringPaymentActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), actionWithDeeplinks, []pb.RecurringPaymentActionFieldMask{pb.RecurringPaymentActionFieldMask_ACTION_DETAILED_STATUS},
					pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_SUCCESS).Return(nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: recurringPayment.GetFromActorId()}).
					Return(&actorPb.GetActorByIdResponse{
						Status: rpc.StatusOk(),
						Actor:  &types.Actor{Type: types.Actor_USER},
					}, nil)
			},
			want: &pb.AuthoriseRecurringPaymentCreationResponse{
				Status:                rpc.StatusOk(),
				PostAuthoriseDeeplink: postAuthDeeplink,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.AuthoriseRecurringPaymentCreation(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AuthoriseRecurringPaymentCreation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("AuthoriseRecurringPaymentCreation() got = %v, want %v", got, tt.want)
			}
		})
	}
}
