package recurringpayment

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpcPb "github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	pb "github.com/epifi/gamma/api/recurringpayment"
	payloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	types "github.com/epifi/gamma/api/typesv2"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
	"github.com/epifi/gamma/pkg/pay"
	"github.com/epifi/gamma/pkg/recurringpayment"
	"github.com/epifi/gamma/recurringpayment/dao"
)

// nolint: funlen
// We are using the client as workflowPb.Client_RECURRING_PAYMENT across recurring payment workflows to prevent exposure of backend information on Client side
func (s *Service) CreateModifyAttempt(ctx context.Context, req *pb.CreateModifyAttemptRequest) (*pb.CreateModifyAttemptResponse, error) {
	var (
		res              = &pb.CreateModifyAttemptResponse{}
		txnId            string
		currentActorId   string
		currentActorRole pb.ActorRole
	)
	clientReqId := req.GetClientId().GetId()
	if clientReqId == "" {
		clientReqId = req.GetClientRequestId()
	}
	err := s.checkPendingActions(ctx, req.GetRecurringPaymentId(), clientReqId, pb.Action_MODIFY)
	switch {
	case errors.Is(err, failedPreconditionError):
		logger.Info(ctx, "pending modify requests exists..fast failing modify attempt",
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		res.Status = rpcPb.StatusFailedPrecondition()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error while fetching modify actions", zap.String(logger.RECURRING_PAYMENT_ID,
			req.GetRecurringPaymentId()), zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	default:
		logger.Debug(ctx, "continuing modify request")
	}
	order, status := s.getOrderByClientRequestId(ctx, clientReqId)
	switch {
	case status.IsSuccess():
		res.OrderId = order.GetId()
		res.Status = rpcPb.StatusAlreadyExists()
		return res, nil
	case status.IsRecordNotFound():
		logger.Debug(ctx, "order record not found, new recurring payment modify requested",
			zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
	default:
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	recurringPayment, err := s.recurringPaymentProcessor.GetRecurringPaymentById(ctx, req.GetRecurringPaymentId())
	if err != nil {
		logger.Error(ctx, "couldn't fetch recurring payment for given recurring payment id", zap.Error(err),
			zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		res.Status = rpcPb.StatusFromError(err)
		return res, nil
	}

	status = s.validateRecurringPaymentModification(recurringPayment.GetRecurrenceRule().GetAllowedFrequency(),
		recurringPayment.GetType(), req.GetMutableParams().GetInterval().GetEndTime().AsTime(), recurringPayment.GetInterval(),
		recurringPayment.GetCreatedAt().AsTime(), recurringPayment.GetState())
	if !status.IsSuccess() {
		logger.Error(ctx, "error in modifying recurring payment")
		res.Status = status
		return res, nil
	}
	if !skipPendingExecutionCheck(recurringPayment, req) {
		err = s.validatePendingExecutions(ctx, recurringPayment)
		if err != nil {
			logger.Error(ctx, "failed due to validation of pending Execution of Recurring PaymentId:", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
			res.Status = rpcPb.StatusFailedPreconditionWithDebugMsg("Error in validation")
			return res, nil
		}
	} else {
		logger.Info(ctx, "skipping pending execution check", zap.String(logger.RECURRING_PAYMENT_ID,
			req.GetRecurringPaymentId()), zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
	}

	currentActorId, currentActorRole = getCurrentActorIdAndRole(req.GetCurrentActorId(), req.GetCurrentActorRole(), recurringPayment)
	isAuthRequired, credBlockType, err := s.recurringPaymentProcessor.GetCredBlockType(recurringPayment.GetType(), currentActorRole)
	if err != nil {
		logger.Error(ctx, "error in fetching cred block type", zap.Error(err))
		res.Status = rpcPb.StatusInvalidArgument()
		return res, nil
	}
	txnId = req.GetReqId()
	if txnId == "" {
		txnId, err = generateRecurringPaymentModifyTransactionId(recurringPayment.GetType(), recurringPayment.GetPartnerBank())
		if err != nil {
			logger.Error(ctx, "error while generating transaction id for recurring payment modification", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}
	action, err := s.recurringPaymentActionsDao.GetByClientRequestId(ctx, clientReqId, false)
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrRecordNotFound):
		txnErr := s.updateRecurringPaymentDetailsInTxnBlock(ctx, req, clientReqId, recurringPayment, currentActorId, txnId)
		if txnErr != nil {
			logger.Error(ctx, "failed to commit transaction for recurring payment modify",
				zap.String(logger.CLIENT_REQUEST_ID, clientReqId), zap.Error(txnErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	case err != nil:
		logger.Error(ctx, "error in fetching modify action", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	default:
		txnId = action.GetVendorRequestId()
	}
	err = s.modifyDomainServiceEntity(ctx, recurringPayment, txnId, req.GetPayload(), currentActorRole)
	if err != nil {
		logger.Error(ctx, "error in modifying domain entity", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	// only when workflow is handled by celestial
	if s.featureFlags.EnableRecurringPaymentModificationViaCelestial() {
		err = s.initiateRecurringPaymentModificationWorkflowViaCelestial(ctx, recurringPayment, &workflowPb.ClientReqId{
			Id:     clientReqId,
			Client: workflowPb.Client_RECURRING_PAYMENT,
		}, req.GetCurrentActorId(), txnId)
		if err != nil {
			logger.Error(ctx, "error while initiating celestial workflow", zap.Error(err),
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
			status := rpcPb.StatusFromError(err)
			res.Status = status
			return res, nil
		}
	} else {
		order, err = s.createOrderForModify(
			ctx,
			recurringPayment,
			clientReqId,
			txnId,
			req.GetMutableParams().GetMaximumAmountLimit(),
			req.GetProvenance(),
		)
		if err != nil {
			logger.Error(ctx, "error in creating order for recurring payment modify", zap.Error(err),
				zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}

	if recurringPayment.GetState() == pb.RecurringPaymentState_MODIFY_AUTHORISED {
		err = s.initiateWorkflowOrchestrationIfAuthorised(ctx, &celestialPb.ClientReqId{
			Id:     clientReqId,
			Client: workflowPb.Client_RECURRING_PAYMENT,
		}, recurringPayment)
		if err != nil {
			logger.Error(ctx, "error while initiating orchestration", zap.Error(err),
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
			status := rpcPb.StatusFromError(err)
			res.Status = status
			return res, nil
		}
	}

	amount := req.GetMutableParams().GetMaximumAmountLimit()
	if amount == nil {
		amount = recurringPayment.GetAmount()
	}
	if isAuthRequired {
		transactionAttributes, err := s.getTransactionAttributes(ctx, recurringPayment, nil, txnId, amount)
		if err != nil {
			logger.Error(ctx, "error in fetching transaction attributes", zap.Error(err), zap.String(logger.REQUEST_ID, txnId))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		res.TransactionAttributes = transactionAttributes
	}

	s.publishRecurringPaymentActionEvent(ctx, nil, recurringPayment)

	res.IsAuthenticationRequired = isAuthRequired
	res.CredBlockType = credBlockType
	res.TxnId = txnId
	res.OrderId = order.GetId()
	res.Status = rpcPb.StatusOk()
	return res, nil
}

// nolint: dupl
func (s *Service) initiateWorkflowOrchestrationIfAuthorised(ctx context.Context, clientId *celestialPb.ClientReqId, recurringPayment *pb.RecurringPayment) error {
	if s.featureFlags.EnableRecurringPaymentModificationViaCelestial() {
		// sending signal to workflow
		payload, marshalErr := protojson.Marshal(&payloadPb.ModifyRecurringPaymentAuthSignal{})
		if marshalErr != nil {
			return fmt.Errorf("error in marshalling modify recurring payment auth signal payload %w %s", rpcPb.StatusAsError(rpcPb.StatusInternal()), clientId.GetId())
		}
		err := s.celestialProcessor.SignalWorkflow(ctx, clientId.GetId(),
			string(rpNs.ModifyRecurringPaymentAuthSignal), clientId.GetClient(), payload, recurringPayment.GetEntityOwnership(), recurringpayment.OwnershipToUseCaseForRecPay[recurringPayment.GetEntityOwnership()])
		if err != nil {
			return fmt.Errorf("error while signaling workflow %w %s", rpcPb.StatusAsError(rpcPb.StatusInternal()),
				clientId.GetId())
		}
	} else {
		err := s.initiateOrderProcessing(ctx, clientId.GetId())
		if err != nil {
			return fmt.Errorf("error while triggering order processing for modify %w %s", rpcPb.StatusAsError(rpcPb.StatusInternal()),
				clientId.GetId())
		}
	}
	return nil
}
func (s *Service) updateRecurringPaymentDetailsInTxnBlock(ctx context.Context, req *pb.CreateModifyAttemptRequest, clientReqId string,
	recurringPayment *pb.RecurringPayment, currentActorId string, txnId string) error {
	return storagev2.RunCRDBTxn(ctx, func(txnCtx context.Context) error {
		recurringPaymentStatus, err := getRecurringPaymentRequestState(recurringPayment.GetInitiatedBy(),
			recurringPayment.GetFromActorId(), currentActorId, pb.Action_MODIFY)
		if err != nil {
			return fmt.Errorf("error fetching recurring payment action :%w", err)
		}
		err = s.recurringPaymentDao.UpdateAndChangeStatus(txnCtx, recurringPayment,
			nil, recurringPayment.GetState(), recurringPaymentStatus)
		if err != nil {
			return fmt.Errorf("error while updating recurring payment state for recurring payment modify %w", err)
		}
		_, err = s.recurringPaymentActionsDao.Create(txnCtx, &pb.RecurringPaymentsAction{
			RecurringPaymentId: recurringPayment.GetId(),
			ClientRequestId:    clientReqId,
			Action:             pb.Action_MODIFY,
			ActionMetadata: &pb.ActionMetadata{ModifyActionMetadata: &pb.ModifyActionMetaData{
				ExistingParams: &pb.MutableParams{
					MaximumAmountLimit: recurringPayment.GetAmount(),
					Interval:           recurringPayment.GetInterval(),
					RecurrenceRule:     recurringPayment.GetRecurrenceRule(),
					MaximumAllowedTxns: recurringPayment.GetMaximumAllowedTxns(),
				},
				UpdatedParams: &pb.MutableParams{
					MaximumAmountLimit: req.GetMutableParams().GetMaximumAmountLimit(),
					Interval:           req.GetMutableParams().GetInterval(),
					RecurrenceRule:     req.GetMutableParams().GetRecurrenceRule(),
					MaximumAllowedTxns: req.GetMutableParams().GetMaximumAllowedTxns(),
				},
			}},
			VendorRequestId: txnId,
			State:           pb.ActionState_ACTION_CREATED,
			ExpireAt:        req.GetExpiry(),
			Remarks:         req.GetRemarks(),
		})
		if err != nil {
			return fmt.Errorf("error in creating modify action %w", err)
		}
		return nil
	})
}

// nolint: dupl
// initiateRecurringPaymentModificationWorkflowViaCelestial - initiates recurring payment modification workflow via celestial
func (s *Service) initiateRecurringPaymentModificationWorkflowViaCelestial(ctx context.Context, recurringPayment *pb.RecurringPayment, clientId *workflowPb.ClientReqId, currentActorId string, reqId string) error {
	var rpPayload []byte
	rpPayload, err := protojson.Marshal(&pb.RecurringPaymentModifyInfo{
		RecurringPaymentId: recurringPayment.GetId(),
		ClientRequestId:    clientId.GetId(),
		RequestId:          reqId,
	})
	if err != nil {
		return fmt.Errorf("error while marshalling recurring payment modify info %w", rpcPb.StatusAsError(rpcPb.StatusInternal()))
	}
	err = s.celestialProcessor.InitiateWorkflowV2(ctx, clientId, currentActorId, rpPayload, celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_MODIFY_RECURRING_PAYMENT), workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT)
	if err != nil {
		return fmt.Errorf("error while initiating workflow %w %s", rpcPb.StatusAsError(rpcPb.StatusInternal()),
			recurringPayment.GetId())
	}
	return nil
}

// nolint: dupl
func (s *Service) createOrderForModify(
	ctx context.Context,
	recurringPayment *pb.RecurringPayment,
	clientRequestId, requestId string,
	amount *money.Money,
	provenance pb.RecurrencePaymentProvenance,
) (*orderPb.Order, error) {
	orderPayload, err := protojson.Marshal(&pb.RecurringPaymentModifyInfo{
		RecurringPaymentId: recurringPayment.GetId(),
		RequestId:          requestId,
		ClientRequestId:    clientRequestId,
	})
	if err != nil {
		return nil, fmt.Errorf("error while marshalling recurring payment modify info %w", err)
	}
	orderProvenance, ok := recurringPaymentProvenanceToOrderProvenanceMap[provenance]
	if !ok {
		return nil, fmt.Errorf("failed to fetch order provenance")
	}
	createOrderRes, err := s.orderClient.CreateOrder(ctx, &orderPb.CreateOrderRequest{
		ActorFrom:    recurringPayment.GetFromActorId(),
		ActorTo:      recurringPayment.GetToActorId(),
		Workflow:     orderPb.OrderWorkflow_MODIFY_RECURRING_PAYMENT,
		Status:       orderPb.OrderStatus_CREATED,
		OrderPayload: orderPayload,
		ClientReqId:  clientRequestId,
		Provenance:   orderProvenance,
		Amount:       amount,
	})
	if te := epifigrpc.RPCError(createOrderRes, err); te != nil {
		return nil, fmt.Errorf("error while creating order %w", te)
	}
	return createOrderRes.GetOrder(), nil
}

// nolint:dupl
func (s *Service) modifyDomainServiceEntity(
	ctx context.Context,
	recurringPayment *pb.RecurringPayment,
	txnId string,
	payload []byte,
	currentActorRole pb.ActorRole,
) error {
	switch recurringPayment.GetType() {
	case pb.RecurringPaymentType_STANDING_INSTRUCTION:
		res, err := s.siClient.CreateModifyAttempt(ctx, &siPb.CreateModifyAttemptRequest{
			RecurringPaymentId: recurringPayment.GetId(),
			TransactionId:      txnId,
		})
		if te := epifigrpc.RPCError(res, err); te != nil {
			if res != nil && res.GetStatus().IsAlreadyExists() {
				return nil
			}
			return fmt.Errorf("error in modifying standing instruction %w", te)
		}
		return nil
	case pb.RecurringPaymentType_UPI_MANDATES:
		var (
			requestPayload = &upiMandatePb.Payload{}
			err            error
		)
		if payload == nil {
			requestPayload, err = s.createMandatePayload(ctx, recurringPayment)
			if err != nil {
				return fmt.Errorf("error in create mandate payload :%w", err)
			}
		} else {
			err = protojson.Unmarshal(payload, requestPayload)
			if err != nil {
				return fmt.Errorf("error unmarshalling payload :%w", err)
			}
		}
		res, err := s.upiMandateClient.ModifyMandate(ctx, &upiMandatePb.ModifyMandateRequest{
			RecurringPaymentId:    recurringPayment.GetId(),
			ReqId:                 txnId,
			MandateRequestPayload: requestPayload,
			CurrentActorRole:      recurringPaymentToMandateActorRole[currentActorRole],
			PartnerBank:           recurringPayment.GetPartnerBank(),
		})
		if te := epifigrpc.RPCError(res, err); te != nil {
			if res != nil && res.GetStatus().IsAlreadyExists() {
				return nil
			}
			return fmt.Errorf("error in modifying recurring payment %w", te)
		}
		return nil
	default:
		return fmt.Errorf("unsupported recurring payment %s", recurringPayment.GetType().String())
	}
}

func generateRecurringPaymentModifyTransactionId(recurringPaymentType pb.RecurringPaymentType, partnerBank commonvgpb.Vendor) (string, error) {
	switch recurringPaymentType {
	case pb.RecurringPaymentType_STANDING_INSTRUCTION:
		switch partnerBank {
		case commonvgpb.Vendor_FEDERAL_BANK:
			return idgen.FederalRandomDigitsSequence(pay.FederalModifyStandingInstructionPrefix, 5), nil
		default:
			return "", fmt.Errorf("vendor not supported for standing instruction modify %s", partnerBank.String())
		}
	case pb.RecurringPaymentType_UPI_MANDATES:
		switch partnerBank {
		case commonvgpb.Vendor_FEDERAL_BANK:
			return pay.GenerateVendorRequestId(partnerBank, paymentPb.PaymentProtocol_UPI)
		default:
			return "", fmt.Errorf("vendor not supported for standing instruction creation %s", partnerBank.String())
		}
	default:
		return "", fmt.Errorf("recurring payment type not supported %s", recurringPaymentType.String())
	}
}

// createMandatePayload creates mandate specific payload which needs to sent in upi domain service request
func (s *Service) createMandatePayload(ctx context.Context, recurringPayment *pb.RecurringPayment) (*upiMandatePb.Payload, error) {
	var (
		mandateName string
	)
	getPayerPiRes, err := s.piClient.GetPiById(ctx, &piPb.GetPiByIdRequest{Id: recurringPayment.GetPiFrom()})
	if te := epifigrpc.RPCError(getPayerPiRes, err); te != nil {
		return nil, fmt.Errorf("error in fetching payer pi %w", te)
	}
	getPayeePiRes, err := s.piClient.GetPiById(ctx, &piPb.GetPiByIdRequest{Id: recurringPayment.GetPiTo()})
	if te := epifigrpc.RPCError(getPayeePiRes, err); te != nil {
		return nil, fmt.Errorf("error in fetching payee pi %w", te)
	}
	mandateName = getPayeePiRes.GetPaymentInstrument().GetVerifiedName()
	if recurringPayment.GetInitiatedBy() == pb.InitiatedBy_PAYEE {
		mandateName = getPayerPiRes.GetPaymentInstrument().GetVerifiedName()
	}
	return &upiMandatePb.Payload{
		PayerVpa:               getPayerPiRes.GetPaymentInstrument().GetUpi().GetVpa(),
		PayeeVpa:               getPayeePiRes.GetPaymentInstrument().GetUpi().GetVpa(),
		MandateName:            mandateName,
		MandateOriginTimestamp: timestamppb.Now(),
	}, nil
}

// getCurrentActorIdAndRole fetches the current actor id and role from combination of actor id and
// role passed in the request parameters.
func getCurrentActorIdAndRole(actorId string, actorRole pb.ActorRole, recurringPayment *pb.RecurringPayment) (string, pb.ActorRole) {
	var (
		currentActorRole pb.ActorRole
		currentActorId   string
	)
	if actorId != "" {
		currentActorId = actorId
		currentActorRole = pb.ActorRole_ACTOR_ROLE_PAYEE
		if actorId == recurringPayment.GetFromActorId() {
			currentActorRole = pb.ActorRole_ACTOR_ROLE_PAYER
		}
	} else {
		currentActorRole = actorRole
		currentActorId = recurringPayment.GetToActorId()
		if actorRole == pb.ActorRole_ACTOR_ROLE_PAYER {
			currentActorId = recurringPayment.GetFromActorId()
		}
	}
	return currentActorId, currentActorRole
}

// nolint: funlen
// checking if there exists any recurring payment action for execute which is in created state/in progress state or not.
// If actions exists then for those actions we will check if txn is in created state.
// If all of them are in created state we will move them to rejected state and allow modify/revoke otherwise we will fail the modify/revoke request.
// TODO(akk) - verify if this check is really required
func (s *Service) validatePendingExecutions(ctx context.Context, recurringPayment *pb.RecurringPayment) error {
	var (
		// these imply transaction is in flight or in a possible pending state
		validPendingTxnStatuses = []paymentPb.TransactionStatus{
			paymentPb.TransactionStatus_IN_PROGRESS,
			paymentPb.TransactionStatus_INITIATED,
			paymentPb.TransactionStatus_MANUAL_INTERVENTION,
			paymentPb.TransactionStatus_UNKNOWN,
		}
		recurringPaymentId = recurringPayment.GetId()
	)
	executeActions, err := s.recurringPaymentActionsDao.GetByRecurringPaymentId(ctx, recurringPaymentId,
		dao.WithActionsFilter([]pb.Action{pb.Action_EXECUTE}), dao.WithActionStateFilter([]pb.ActionState{pb.ActionState_ACTION_CREATED}))

	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "no pending requests for execute")
		return nil
	case err != nil:
		logger.Error(ctx, "Error in Getting execute action", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
		return err
	}

	executeClientRequestIdMap := map[string]*pb.RecurringPaymentsAction{}
	var req []*orderPb.OrderIdentifier
	for i := range executeActions {
		executeClientRequestIdMap[executeActions[i].GetClientRequestId()] = executeActions[i]
		req = append(req, &orderPb.OrderIdentifier{
			Identifier: &orderPb.OrderIdentifier_ClientReqId{
				ClientReqId: executeActions[i].GetClientRequestId(),
			},
		})
	}
	txn, err := s.orderClient.GetOrdersWithTransactions(ctx, &orderPb.GetOrdersWithTransactionsRequest{OrderIdentifiers: req})

	switch {
	case err != nil:
		logger.Error(ctx, "Error in Getting txn", zap.Error(err))
		return err
	case txn.GetStatus().IsRecordNotFound():
		logger.Error(ctx, "no order and txn is found")
		return nil
	case !txn.GetStatus().IsSuccess():
		logger.Error(ctx, "not getting success status from rpc")
		return fmt.Errorf("not getting success status from rpc")
	}

	flag := 0
	for i := range txn.GetOrderWithTransactions() {
		// restricting new logic of pending execution validation to UPI mandates to start with
		if recurringPayment.GetType() == pb.RecurringPaymentType_UPI_MANDATES {
			if lo.Contains(validPendingTxnStatuses, txn.GetOrderWithTransactions()[i].GetTransactions()[0].GetStatus()) {
				logger.Error(ctx, "Transaction is in possible in-flight status", zap.String(logger.CLIENT_REQUEST_ID, txn.GetOrderWithTransactions()[i].GetOrder().GetClientReqId()))
				flag = 1
			}
		} else {
			if txn.GetOrderWithTransactions()[i].GetTransactions()[0].GetStatus() != paymentPb.TransactionStatus_CREATED {
				logger.Error(ctx, "Transaction in not in created state for clientReqId", zap.String(logger.CLIENT_REQUEST_ID, txn.GetOrderWithTransactions()[i].GetOrder().GetClientReqId()))
				flag = 1
			}
		}
	}
	if flag == 1 {
		return fmt.Errorf("unsafe to revoke since some transactions are in flight")
	}

	for i := range txn.GetOrderWithTransactions() {
		if txn.OrderWithTransactions[i].GetTransactions()[0].GetStatus() == paymentPb.TransactionStatus_CREATED {
			txn.OrderWithTransactions[i].Order.Status = orderPb.OrderStatus_REJECTED
			txn.OrderWithTransactions[i].Transactions[0].Status = paymentPb.TransactionStatus_REJECTED
			_, err = s.orderClient.UpdateOrder(ctx, &orderPb.UpdateOrderRequest{
				Order:      txn.GetOrderWithTransactions()[i].GetOrder(),
				FieldMasks: []orderPb.OrderFieldMask{orderPb.OrderFieldMask_STATUS},
			})

			if err != nil {
				logger.Error(ctx, "Error in updating order", zap.Error(err), zap.String(logger.ORDER_ID, txn.GetOrderWithTransactions()[i].GetOrder().GetId()))
				return err
			}

			err = s.updateTransaction(ctx, txn.OrderWithTransactions[i].Transactions[0], []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS})
			if err != nil {
				logger.Error(ctx, "Error in updating transaction", zap.Error(err), zap.String(logger.TXN_ID, txn.OrderWithTransactions[i].Transactions[0].GetId()))
				return err
			}
			action := executeClientRequestIdMap[txn.OrderWithTransactions[i].Order.GetClientReqId()]
			err = s.recurringPaymentActionsDao.UpdateAndChangeStatus(ctx, action,
				[]pb.RecurringPaymentActionFieldMask{pb.RecurringPaymentActionFieldMask_ACTION_STATE}, action.GetState(), pb.ActionState_ACTION_REJECT)
			if err != nil {
				logger.Error(ctx, "Error in updating recurring payment action", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, action.GetClientRequestId()))
				return err
			}
		}
	}

	return nil
}

// validateRecurringPaymentModification checks whether the modification for recurring payment is allowed or not
// For example - in case of mandates it checks whether the new end date is a valid end date or not
func (s *Service) validateRecurringPaymentModification(allowedFrequency pb.AllowedFrequency,
	recurringPaymentType pb.RecurringPaymentType, newEndTime time.Time, interval *types.Interval, createdTime time.Time,
	recurringPaymentState pb.RecurringPaymentState) *rpcPb.Status {

	if recurringPaymentState == pb.RecurringPaymentState_REVOKED {
		return rpcPb.NewStatusWithoutDebug(uint32(pb.CreateModifyAttemptResponse_MANDATE_REVOKED),
			"mandate already revoked")
	}

	endTime := interval.GetEndTime().AsTime()
	currTime := time.Now().UTC()

	status := rpcPb.StatusOk()

	if recurringPaymentType == pb.RecurringPaymentType_UPI_MANDATES {
		switch {
		case endTime.Sub(currTime) < time.Duration(s.config.UpiMandateValidationParams.AllowedHoursForModification)*time.Hour:
			status = rpcPb.NewStatusWithoutDebug(uint32(pb.CreateModifyAttemptResponse_MODIFICATION_TIME_EXCEEDED),
				"modification time exceeded")

		// if end date is greater than allowed end date for one time mandate
		case int(newEndTime.Sub(createdTime).Hours()) > 24*s.config.UpiMandateValidationParams.MaximumAllowedDurationBetweenStartAndEndDate:
			if allowedFrequency == pb.AllowedFrequency_ONE_TIME {
				status = rpcPb.NewStatusWithoutDebug(uint32(pb.CreateModifyAttemptResponse_INVALID_END_DATE),
					"end date is greater than allowed end date")
			}
		}
	}
	return status
}

// we will skip pending execution checks if recurring payment is of type standing instruction and amount to be updated is greater than
// the current amount and start date is less than or equal to the previous start date and end date is greater than the current date
// and frequency if not updated and maximum allowed txns is greater than current maximum allowed txns
func skipPendingExecutionCheck(recurringPayment *pb.RecurringPayment, req *pb.CreateModifyAttemptRequest) bool {
	if recurringPayment.GetType() == pb.RecurringPaymentType_STANDING_INSTRUCTION &&
		moneyPkg.Compare(req.GetMutableParams().GetMaximumAmountLimit(), recurringPayment.GetAmount()) == 1 &&
		(req.GetMutableParams().GetInterval().GetEndTime() == nil || req.GetMutableParams().GetInterval().GetEndTime().AsTime().After(time.Now())) &&
		(req.GetMutableParams().GetInterval().GetStartTime() == nil || !req.GetMutableParams().GetInterval().GetStartTime().AsTime().After(recurringPayment.GetInterval().GetStartTime().AsTime())) &&
		(req.GetMutableParams().GetRecurrenceRule().GetAllowedFrequency() == 0 || req.GetMutableParams().GetRecurrenceRule().GetAllowedFrequency() == recurringPayment.GetRecurrenceRule().GetAllowedFrequency()) &&
		(req.GetMutableParams().GetMaximumAllowedTxns() == 0 || req.GetMutableParams().GetMaximumAllowedTxns() >= recurringPayment.GetMaximumAllowedTxns()) {
		return true
	}
	return false
}
