package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

const (
	EventOffAppEnachMandateActionServer = "OffAppEnachMandateActionServer"
	EventUpiMandateAction               = "UpiMandateAction"
)

type OffAppEnachMandateAction struct {
	EventId            string
	ProspectId         string
	ActorId            string
	SessionId          string
	EventType          string
	State              string
	MandateStartTime   time.Time
	MandateEndTime     time.Time
	Amount             string
	Frequency          string
	MerchantName       string
	Timestamp          time.Time
	RecurringPaymentId string
}

func NewOffAppEnachMandateAction(actorId, rpId, merchantName, mandateAmount, frequency, currState string, mandateStart, mandateEnd time.Time) *OffAppEnachMandateAction {
	return &OffAppEnachMandateAction{
		EventId:            uuid.NewString(),
		ProspectId:         "",
		ActorId:            actorId,
		SessionId:          "",
		MandateStartTime:   mandateStart,
		MandateEndTime:     mandateEnd,
		Amount:             mandateAmount,
		Frequency:          frequency,
		MerchantName:       merchantName,
		EventType:          events.EventTrack,
		Timestamp:          time.Now(),
		State:              currState,
		RecurringPaymentId: rpId}
}

func (i *OffAppEnachMandateAction) GetEventType() string {
	return events.EventTrack
}

func (i *OffAppEnachMandateAction) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(i, properties)
	return properties
}

func (i *OffAppEnachMandateAction) GetEventTraits() map[string]interface{} {
	return nil
}

func (i *OffAppEnachMandateAction) GetEventId() string {
	return i.EventId
}

func (i *OffAppEnachMandateAction) GetUserId() string {
	return i.ActorId
}

func (i *OffAppEnachMandateAction) GetProspectId() string {
	return i.ProspectId
}

func (i *OffAppEnachMandateAction) GetEventName() string {
	return EventOffAppEnachMandateActionServer
}

type UpiMandateAction struct {
	EventId               string
	ProspectId            string
	ActorId               string
	InitiatedBy           string
	Action                string
	SessionId             string
	EventType             string
	RecurringPaymentState string
	ActionState           string
	MandateStartTime      time.Time
	MandateEndTime        time.Time
	Amount                string
	Frequency             string
	Timestamp             time.Time
	RecurringPaymentId    string
}

func NewUpiMandateAction(actorId, initiatedBy, action, state, actionState, rpId, amount, frequency string, mandateStart, mandateEnd time.Time) *UpiMandateAction {
	return &UpiMandateAction{
		EventId:               uuid.NewString(),
		ProspectId:            "",
		ActorId:               actorId,
		SessionId:             "",
		EventType:             events.EventTrack,
		RecurringPaymentState: state,
		ActionState:           actionState,
		Timestamp:             time.Now(),
		InitiatedBy:           initiatedBy,
		Action:                action,
		RecurringPaymentId:    rpId,
		MandateEndTime:        mandateEnd,
		MandateStartTime:      mandateStart,
		Amount:                amount,
		Frequency:             frequency,
	}
}

func (i *UpiMandateAction) GetEventType() string {
	return events.EventTrack
}

func (i *UpiMandateAction) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(i, properties)
	return properties
}

func (i *UpiMandateAction) GetEventTraits() map[string]interface{} {
	return nil
}

func (i *UpiMandateAction) GetEventId() string {
	return i.EventId
}

func (i *UpiMandateAction) GetUserId() string {
	return i.ActorId
}

func (i *UpiMandateAction) GetProspectId() string {
	return i.ProspectId
}

func (i *UpiMandateAction) GetEventName() string {
	return EventUpiMandateAction
}
