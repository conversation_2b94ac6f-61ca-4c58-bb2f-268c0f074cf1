package recurringpayment

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	vgEnachPb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/recurringpayment/mapping"
)

// GetRecurringPaymentEssentials fetches the recurring payment essentials for the given request identifier from the respective vendor
// for eg. In case of enach mandates, we can fetch the recurring payment details from the vendor
// NOTE: this should only be called when the recurring payment is set on third party app like Groww.
func (s *Service) GetRecurringPaymentEssentials(ctx context.Context, req *rpPb.GetRecurringPaymentEssentialsRequest) (*rpPb.GetRecurringPaymentEssentialsResponse, error) {
	switch req.GetIdentifier().(type) {
	case *rpPb.GetRecurringPaymentEssentialsRequest_EnachIdentifier:
		recurringPaymentEssential, enachData, err := s.getRecuringPaymentDetailsFromVendorForEnach(ctx, req.GetVendor(), req.GetActorId(), req.GetEnachIdentifier().GetUmrn())
		switch {
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			logger.Error(ctx, "no data found for the given request", zap.Any(logger.REQUEST, req))
			return &rpPb.GetRecurringPaymentEssentialsResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		case err != nil:
			logger.Error(ctx, "failed to fetch recurring payment details for the given request", zap.Error(err), zap.Any(logger.REQUEST, req.GetIdentifier()))
			return &rpPb.GetRecurringPaymentEssentialsResponse{
				Status: rpc.StatusInternal(),
			}, nil
		}
		return &rpPb.GetRecurringPaymentEssentialsResponse{
			Status:                           rpc.StatusOk(),
			RecurringPaymentEssentials:       recurringPaymentEssential,
			RecurringPaymentTypeSpecificData: enachData,
		}, nil
	default:
		return &rpPb.GetRecurringPaymentEssentialsResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}
}

func (s *Service) getRecuringPaymentDetailsFromVendorForEnach(ctx context.Context, vendor commonvgpb.Vendor, actorId, umrn string) (*rpPb.RecurringPaymentEssentials, *rpPb.GetRecurringPaymentEssentialsResponse_EnachData_, error) {
	enachMandates, enachMandatesErr := s.getEnachMandates(ctx, vendor, actorId)
	if enachMandatesErr != nil {
		return nil, nil, fmt.Errorf("failed to fetch the list of enach mandates %w", enachMandatesErr)
	}

	enachMandateForGivenUmrn, enachMandateForGivenUmrnErr := getEnachDetailsForGivenUmrn(enachMandates, umrn)
	if enachMandateForGivenUmrnErr != nil {
		return nil, nil, fmt.Errorf("failed to fetch enach mandate for given umrn %w", enachMandateForGivenUmrnErr)
	}
	return convertEnachMandateVgResponseToRecurringPaymentEssentials(vendor, enachMandateForGivenUmrn), &rpPb.GetRecurringPaymentEssentialsResponse_EnachData_{
		EnachData: &rpPb.GetRecurringPaymentEssentialsResponse_EnachData{
			Umrn:    umrn,
			OrgName: enachMandateForGivenUmrn.GetOrgName(),
		},
	}, nil
}

func (s *Service) getEnachMandates(ctx context.Context, vendor commonvgpb.Vendor, actorId string) ([]*vgEnachPb.EnachMandate, error) {
	// fetch the savings account id from actor id
	fetchedAccount, fetchedAccountErr := s.savingsClient.GetSavingsAccountEssentials(ctx, &savings.GetSavingsAccountEssentialsRequest{
		Filter: &savings.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savings.ActorIdBankFilter{
				ActorId:     actorId,
				PartnerBank: vendor,
			},
		},
	})
	if err := epifigrpc.RPCError(fetchedAccount, fetchedAccountErr); err != nil {
		return nil, fmt.Errorf("failed to fetch account by actor id %w", err)
	}
	// todo(Harleen Singh): evaluate moving this to enach service, as of now the decision falls in the grey area. When more vg apis come for other types of rp, then evaulate
	// fetch the list of enach mandates from vendor
	fetchedRpDetailsForEnach, fetchedRpDetailsForEnachErr := s.enachVgClient.ListEnachMandate(ctx, &vgEnachPb.ListEnachMandateRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: vendor,
		},
		AccountNumber: fetchedAccount.GetAccount().GetAccountNo(),
	})
	if err := epifigrpc.RPCError(fetchedRpDetailsForEnach, fetchedRpDetailsForEnachErr); err != nil && !fetchedRpDetailsForEnach.GetStatus().IsRecordNotFound() {
		return nil, fmt.Errorf("failed to fetch active mandates from the vendor,err: %w", err)
	}
	if fetchedRpDetailsForEnach.GetStatus().IsRecordNotFound() {
		return nil, epifierrors.ErrRecordNotFound
	}
	return fetchedRpDetailsForEnach.GetEnachMandates(), nil
}

func getEnachDetailsForGivenUmrn(enachMandates []*vgEnachPb.EnachMandate, umrn string) (*vgEnachPb.EnachMandate, error) {
	for _, enachMandate := range enachMandates {
		if enachMandate.GetUmrn() == umrn {
			return enachMandate, nil
		}
	}
	return nil, epifierrors.ErrRecordNotFound
}

func convertEnachMandateVgResponseToRecurringPaymentEssentials(vendor commonvgpb.Vendor, enachMandate *vgEnachPb.EnachMandate) *rpPb.RecurringPaymentEssentials {
	return &rpPb.RecurringPaymentEssentials{
		Type: rpPb.RecurringPaymentType_ENACH_MANDATES,
		Interval: &types.Interval{
			StartTime: enachMandate.GetMandateStartTime(),
			EndTime:   enachMandate.GetMandateExpireTime(),
		},
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_ENACH,
		PartnerBank:              vendor,
		MinAmount:                enachMandate.GetMinAmount(),
		MaxAmount:                enachMandate.GetMaxAmount(),
		AmountType:               mapping.EnachMandateAmountTypeToRecurringPaymentAmountType[enachMandate.GetAmountType()],
		Provenance:               rpPb.RecurrencePaymentProvenance_EXTERNAL,
		State:                    rpPb.RecurringPaymentState_ACTIVATED,
		RecurrenceRule: &rpPb.RecurrenceRule{
			AllowedFrequency: mapping.EnachMandateFrequencyToRecurringPaymentFrequency[enachMandate.GetMandateFrequency()],
		},
	}
}
