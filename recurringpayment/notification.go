package recurringpayment

import (
	"context"
	"fmt"
	"time"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/queue"

	actorPb "github.com/epifi/gamma/api/actor"
	commspb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/fcm"
	orderPb "github.com/epifi/gamma/api/order"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	pb "github.com/epifi/gamma/api/recurringpayment"
	types "github.com/epifi/gamma/api/typesv2"
	payPkg "github.com/epifi/gamma/pkg/pay"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	setFrequency = "set frequency"
	user         = "user"
	merchant     = "merchant"
)

var (
	mandateFrequencyMap = map[int]string{2: "day", 3: "week", 4: "fortnight", 5: "month", 6: "bi month",
		7: "quarter", 8: "half year", 9: "year", 10: "set frequency"}
)

func (s *Service) getMandateFrequency(allowedFrequency int) string {
	if freq, ok := mandateFrequencyMap[allowedFrequency]; ok {
		return freq
	}
	return setFrequency
}

//nolint:funlen
func (s *Service) ProcessRecurringPaymentsNotification(ctx context.Context, req *orderPb.OrderUpdate) (
	*pb.ProcessRecurringPaymentsNotificationResponse, error) {
	var (
		recurringPaymentId string
		res                = &pb.ProcessRecurringPaymentsNotificationResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{},
		}
		currTime = time.Now()
		currDate = datetime.DateToString(datetime.TimeToDateInLoc(currTime, datetime.IST), s.config.UpiMandateNotifDateFormat, datetime.IST)
	)

	order := req.GetOrderWithTransactions().GetOrder()
	if order == nil {
		logger.Error(ctx, "Received nil order in ProcessRecurringPaymentsNotification")
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
		return res, nil
	}

	recurringPaymentId, err := s.getRpIdFromOrder(order)
	if err != nil {
		logger.Error(ctx, "Failed to Unmarshal OrderPayload", zap.Error(err), zap.String(logger.ORDER_ID, order.GetId()))
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
		return res, nil
	}

	// recurring payment order details
	recurringPayment, err := s.recurringPaymentDao.GetById(ctx, recurringPaymentId)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "recurringPayment record not found", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
		return res, nil
	case err != nil:
		logger.Error(ctx, "Failed to get recurringPayment from dao layer", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
		return res, nil
	default:
		logger.Info(ctx, "recurringPayment fetched successfully", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
	}

	fromActorEntity, err := s.getActorDetails(ctx, order.GetFromActorId())
	if err != nil {
		logger.Error(ctx, "Couldn't fetch fromActor entities from actor client!", zap.Error(err), zap.String(logger.ACTOR_ID, order.GetFromActorId()))
		res.ResponseHeader.Status = queue.GetStatusFrom(err)
		return res, nil
	}
	toActorEntity, err := s.getActorDetails(ctx, order.GetToActorId())
	if err != nil {
		logger.Error(ctx, "Couldn't fetch toActor entities from actor client!", zap.Error(err), zap.String(logger.ACTOR_ID, order.GetToActorId()))
		res.ResponseHeader.Status = queue.GetStatusFrom(err)
		return res, nil
	}

	switch recurringPayment.GetType() { //nolint:exhaustive
	case pb.RecurringPaymentType_STANDING_INSTRUCTION:

		actorDetails, err := s.getActorById(ctx, order.GetToActorId()) //nolint:govet
		if err != nil {
			logger.Error(ctx, "Couldn't fetch toActor details from actor client!", zap.Error(err), zap.String(logger.ACTOR_ID, order.GetToActorId()))
			res.ResponseHeader.Status = queue.GetStatusFrom(err)
			return res, nil
		}
		isMerchantActor := s.getIfMerchantActor(actorDetails.GetActor().GetType())
		getPiRes, err := s.piClient.GetPiById(ctx, &piPb.GetPiByIdRequest{Id: recurringPayment.GetPiTo()})
		var piToMaskedAccountNumber string
		switch {
		case err != nil:
			logger.Error(ctx, "error while fetching payment instrument for toPi", zap.Error(err), zap.String(logger.PI_ID, recurringPayment.GetPiTo()))
			res.ResponseHeader.Status = queue.GetStatusFrom(err)
			return res, nil
		case !getPiRes.GetStatus().IsSuccess():
			logger.Error(ctx, "non success status while fetching card payment instrument for toPi",
				zap.String(logger.RPC_STATUS, getPiRes.GetStatus().String()), zap.String(logger.PI_ID, recurringPayment.GetPiTo()))
		default:
			piToActor := getPiRes.GetPaymentInstrument()
			switch piToActor.GetIdentifier().(type) {
			case *piPb.PaymentInstrument_Account:
				piToMaskedAccountNumber = piToActor.GetAccount().GetSecureAccountNumber()
			default:
				logger.Error(ctx, "payment instrument of toActor is not of account type", zap.String(logger.PI_ID, recurringPayment.GetPiTo()))
				res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
				return res, nil
			}
		}
		if err = s.sendSINotification(ctx, order, recurringPayment, fromActorEntity, toActorEntity, currDate, isMerchantActor, piToMaskedAccountNumber); err != nil {
			logger.Error(ctx, "Internal Error from comms client when sending PN!", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
			res.ResponseHeader.Status = queue.GetStatusFrom(err)
			return res, nil
		}
		if err = s.sendSISms(ctx, order, recurringPayment, fromActorEntity, toActorEntity, currDate); err != nil {
			logger.Error(ctx, "Internal Error from comms client when sending SMS!", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
			res.ResponseHeader.Status = queue.GetStatusFrom(err)
			return res, nil
		}
	case pb.RecurringPaymentType_UPI_MANDATES:
		if err = s.sendMandateNotification(ctx, order, recurringPayment, fromActorEntity, toActorEntity, currDate); err != nil {
			logger.Error(ctx, "Internal Error from comms client when sending PN!", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
			res.ResponseHeader.Status = queue.GetStatusFrom(err)
			return res, nil
		}
		if err = s.sendMandateSms(ctx, order, recurringPayment, fromActorEntity, toActorEntity, currDate,
			datetime.DateToString(datetime.TimeToDateInLoc(recurringPayment.GetInterval().GetStartTime().AsTime(), datetime.IST), s.config.UpiMandateNotifDateFormat, datetime.IST)); err != nil {
			logger.Error(ctx, "Internal Error from comms client when sending SMS!", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
			res.ResponseHeader.Status = queue.GetStatusFrom(err)
			return res, nil
		}
	}

	res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_SUCCESS
	return res, nil
}

func (s *Service) getIfMerchantActor(actorType types.Actor_Type) bool {
	if actorType == types.Actor_MERCHANT || actorType == types.Actor_EXTERNAL_MERCHANT {
		return true
	}
	return false
}

func (s *Service) getRpIdFromOrder(order *orderPb.Order) (string, error) {
	var (
		orderWorkflow      = order.GetWorkflow()
		err                error
		recurringPaymentId string
	)
	switch orderWorkflow {
	case orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT:
		rpPayload := &pb.RecurringPaymentCreationInfo{}
		err = protojson.Unmarshal(order.GetOrderPayload(), rpPayload)
		if err == nil {
			recurringPaymentId = rpPayload.GetRecurringPaymentId()
		}
	case orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT, orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_WITH_AUTH,
		orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH:
		rpPayload := &pb.RecurringPaymentExecutionInfo{}
		err = protojson.Unmarshal(order.GetOrderPayload(), rpPayload)
		if err == nil {
			recurringPaymentId = rpPayload.GetRecurringPaymentId()
		}
	case orderPb.OrderWorkflow_REVOKE_RECURRING_PAYMENT:
		rpPayload := &pb.RecurringPaymentRevokeInfo{}
		err = protojson.Unmarshal(order.GetOrderPayload(), rpPayload)
		if err == nil {
			recurringPaymentId = rpPayload.GetRecurringPaymentId()
		}
	case orderPb.OrderWorkflow_MODIFY_RECURRING_PAYMENT:
		rpPayload := &pb.RecurringPaymentModifyInfo{}
		err = protojson.Unmarshal(order.GetOrderPayload(), rpPayload)
		if err == nil {
			recurringPaymentId = rpPayload.GetRecurringPaymentId()
		}
	case orderPb.OrderWorkflow_PAUSE_OR_UNPAUSE_RECURRING_PAYMENT:
		rpPayload := &pb.RecurringPaymentPauseUnpauseInfo{}
		err = protojson.Unmarshal(order.GetOrderPayload(), rpPayload)
		if err == nil {
			recurringPaymentId = rpPayload.GetRecurringPaymentId()
		}
	default:
		err = fmt.Errorf("order Workflow isn't of recurringPayment type. OrderWorkflow: %s", orderWorkflow.String())
	}
	return recurringPaymentId, err
}

// nolint: funlen
func (s *Service) sendMandateNotification(
	ctx context.Context,
	order *orderPb.Order,
	recurringPayment *pb.RecurringPayment,
	fromActorEntity *actorPb.GetEntityDetailsByActorIdResponse,
	toActorEntity *actorPb.GetEntityDetailsByActorIdResponse,
	currDate string,
) error {

	// All the notification are being send to fromActor/PAYER.
	var (
		notificationMessage = &commspb.SendMessageRequest{}
		commonFields        = &fcm.CommonTemplateFields{}
		notifType           fcm.NotificationType
		notifTTL            time.Duration
		orderWorkflow       = order.GetWorkflow()
		orderStatus         = order.GetStatus()
	)

	notificationMessage.Type = commspb.QoS_GUARANTEED
	notificationMessage.Medium = commspb.Medium_NOTIFICATION
	notificationMessage.UserIdentifier = &commspb.SendMessageRequest_UserId{UserId: fromActorEntity.GetEntityId()}

	switch {
	// Mandate Received
	case orderWorkflow == orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT && orderStatus == orderPb.OrderStatus_CREATED &&
		recurringPayment.GetInitiatedBy() == pb.InitiatedBy_PAYEE:

		commonFields.Title = s.config.RecurringPaymentNotificationParams.MandateReceivedPayer.Title
		commonFields.Body = fmt.Sprintf(s.config.RecurringPaymentNotificationParams.MandateReceivedPayer.Body,
			toActorEntity.GetName().GetFirstName(), money.ToDisplayStringWithINRSymbol(recurringPayment.GetAmount()))
		commonFields.Deeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_AUTHORIZE_RECURRING_PAYMENT_SCREEN,
			ScreenOptions: &deeplink.Deeplink_AuthorizeRecurringPaymentScreenOptions{
				AuthorizeRecurringPaymentScreenOptions: &deeplink.AuthorizeRecurringPaymentScreenOptions{
					RecurringPaymentId: recurringPayment.GetId(),
				},
			},
		}
		commonFields.IconAttributes = convertToFCMIconAttribute(s.config.RecurringPaymentNotificationParams.MandateReceivedPayer.IconAttr)
		notifType = s.config.RecurringPaymentNotificationParams.MandateReceivedPayer.NotificationType.ToFCMNotificationTypeEnum()
		notifTTL = s.config.RecurringPaymentNotificationParams.MandateReceivedPayer.NotificationExpiry
		// Mandate Approved
	case orderWorkflow == orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT && orderStatus == orderPb.OrderStatus_IN_FULFILLMENT &&
		recurringPayment.GetState() == pb.RecurringPaymentState_CREATION_AUTHORISED:

		commonFields.Body = fmt.Sprintf(s.config.RecurringPaymentNotificationParams.MandateApprovedPayer.Body,
			s.getMandateFrequency(int(recurringPayment.GetRecurrenceRule().GetAllowedFrequency())),
			toActorEntity.GetName().ToString(), money.ToDisplayStringWithINRSymbol(recurringPayment.GetAmount()))
		commonFields.Title = s.config.RecurringPaymentNotificationParams.MandateApprovedPayer.Title
		commonFields.Deeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
				RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
					RecurringPaymentId: recurringPayment.GetId(),
				},
			},
		}
		commonFields.IconAttributes = convertToFCMIconAttribute(s.config.RecurringPaymentNotificationParams.MandateApprovedPayer.IconAttr)
		notifType = s.config.RecurringPaymentNotificationParams.MandateApprovedPayer.NotificationType.ToFCMNotificationTypeEnum()
		notifTTL = s.config.RecurringPaymentNotificationParams.MandateApprovedPayer.NotificationExpiry
		// Mandate Declined
	case orderWorkflow == orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT && orderStatus == orderPb.OrderStatus_FULFILLMENT_FAILED:

		commonFields.Body = fmt.Sprintf(s.config.RecurringPaymentNotificationParams.MandateDeclinedPayer.Body, toActorEntity.GetName().GetFirstName(),
			s.config.RecurringPaymentFrequencyMapping[recurringPayment.GetRecurrenceRule().GetAllowedFrequency().String()],
			money.ToDisplayStringWithINRSymbol(recurringPayment.GetAmount()))
		commonFields.Title = s.config.RecurringPaymentNotificationParams.MandateDeclinedPayer.Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.config.RecurringPaymentNotificationParams.MandateDeclinedPayer.IconAttr)
		commonFields.Deeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
				RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
					RecurringPaymentId: recurringPayment.GetId(),
				},
			},
		}
		notifType = s.config.RecurringPaymentNotificationParams.MandateDeclinedPayer.NotificationType.ToFCMNotificationTypeEnum()
		notifTTL = s.config.RecurringPaymentNotificationParams.MandateDeclinedPayer.NotificationExpiry
	// Mandate Created
	case orderWorkflow == orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT && orderStatus == orderPb.OrderStatus_FULFILLED:

		commonFields.Body = fmt.Sprintf(s.config.RecurringPaymentNotificationParams.MandateCreationPayer.Body,
			s.getMandateFrequency(int(recurringPayment.GetRecurrenceRule().GetAllowedFrequency())),
			toActorEntity.GetName().GetFirstName(), money.ToDisplayStringWithINRSymbol(recurringPayment.GetAmount()))
		commonFields.Title = s.config.RecurringPaymentNotificationParams.MandateCreationPayer.Title
		commonFields.Deeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
				RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
					RecurringPaymentId: recurringPayment.GetId(),
				},
			},
		}
		commonFields.IconAttributes = convertToFCMIconAttribute(s.config.RecurringPaymentNotificationParams.MandateCreationPayer.IconAttr)
		notifType = s.config.RecurringPaymentNotificationParams.MandateCreationPayer.NotificationType.ToFCMNotificationTypeEnum()
		notifTTL = s.config.RecurringPaymentNotificationParams.MandateCreationPayer.NotificationExpiry
		// Successful Mandate Execution

	case order.IsRecurringPaymentExecutionOrder() && orderStatus == orderPb.OrderStatus_PAID:
		// using order amount in notification body as execution amount can be different than recurringPayment set amount
		commonFields.Body = fmt.Sprintf(s.config.RecurringPaymentNotificationParams.MandateExecutionSuccessPayer.Body,
			money.ToDisplayStringWithINRSymbol(order.GetAmount()), toActorEntity.GetName().ToString(), currDate)
		commonFields.Title = s.config.RecurringPaymentNotificationParams.MandateExecutionSuccessPayer.Title
		commonFields.Deeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_TRANSACTION_RECEIPT,
			ScreenOptions: &deeplink.Deeplink_TransactionReceiptScreenOptions{
				TransactionReceiptScreenOptions: &deeplink.TransactionReceiptScreenOptions{
					OrderId: order.GetId(),
				},
			},
		}
		commonFields.IconAttributes = convertToFCMIconAttribute(s.config.RecurringPaymentNotificationParams.MandateExecutionSuccessPayer.IconAttr)
		notifType = s.config.RecurringPaymentNotificationParams.MandateExecutionSuccessPayer.NotificationType.ToFCMNotificationTypeEnum()
		notifTTL = s.config.RecurringPaymentNotificationParams.MandateExecutionSuccessPayer.NotificationExpiry
		// Failed Mandate Execution
	case order.IsRecurringPaymentExecutionOrder() && orderStatus == orderPb.OrderStatus_PAYMENT_FAILED:
		// using order amount in notification body as execution amount can be different than recurringPayment set amount
		commonFields.Body = fmt.Sprintf(s.config.RecurringPaymentNotificationParams.MandateExecutionFailedPayer.Body,
			money.ToDisplayStringWithINRSymbol(order.GetAmount()), toActorEntity.GetName().ToString())
		commonFields.Title = s.config.RecurringPaymentNotificationParams.MandateExecutionFailedPayer.Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.config.RecurringPaymentNotificationParams.MandateExecutionFailedPayer.IconAttr)
		commonFields.Deeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
				RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
					RecurringPaymentId: recurringPayment.GetId(),
				},
			},
		}
		notifType = s.config.RecurringPaymentNotificationParams.MandateExecutionFailedPayer.NotificationType.ToFCMNotificationTypeEnum()
		notifTTL = s.config.RecurringPaymentNotificationParams.MandateExecutionFailedPayer.NotificationExpiry
		// Mandate Revoked
	case orderWorkflow == orderPb.OrderWorkflow_REVOKE_RECURRING_PAYMENT && orderStatus == orderPb.OrderStatus_FULFILLED:

		commonFields.Body = fmt.Sprintf(s.config.RecurringPaymentNotificationParams.MandateRevokedPayer.Body,
			toActorEntity.GetName().GetFirstName(), money.ToDisplayStringWithINRSymbol(recurringPayment.GetAmount()))
		commonFields.Title = s.config.RecurringPaymentNotificationParams.MandateRevokedPayer.Title
		commonFields.Deeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
				RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
					RecurringPaymentId: recurringPayment.GetId(),
				},
			},
		}
		commonFields.IconAttributes = convertToFCMIconAttribute(s.config.RecurringPaymentNotificationParams.MandateRevokedPayer.IconAttr)
		notifType = s.config.RecurringPaymentNotificationParams.MandateRevokedPayer.NotificationType.ToFCMNotificationTypeEnum()
		notifTTL = s.config.RecurringPaymentNotificationParams.MandateRevokedPayer.NotificationExpiry
		// Mandate Modified
	case orderWorkflow == orderPb.OrderWorkflow_MODIFY_RECURRING_PAYMENT && orderStatus == orderPb.OrderStatus_FULFILLED:

		commonFields.Body = fmt.Sprintf(s.config.RecurringPaymentNotificationParams.MandateModifiedPayer.Body,
			toActorEntity.GetName().GetFirstName(), money.ToDisplayStringWithINRSymbol(recurringPayment.GetAmount()))
		commonFields.Title = s.config.RecurringPaymentNotificationParams.MandateModifiedPayer.Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.config.RecurringPaymentNotificationParams.MandateModifiedPayer.IconAttr)
		commonFields.Deeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
				RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
					RecurringPaymentId: recurringPayment.GetId(),
				},
			},
		}
		notifType = s.config.RecurringPaymentNotificationParams.MandateModifiedPayer.NotificationType.ToFCMNotificationTypeEnum()
		notifTTL = s.config.RecurringPaymentNotificationParams.MandateModifiedPayer.NotificationExpiry
		// Mandate Authorized
	case orderWorkflow == orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_WITH_AUTH && orderStatus == orderPb.OrderStatus_IN_PAYMENT:
		// using order amount in notification body as execution amount can be different than recurringPayment set amount
		commonFields.Body = fmt.Sprintf(s.config.RecurringPaymentNotificationParams.MandateAuthorizedPayer.Body,
			toActorEntity.GetName().GetFirstName(), money.ToDisplayStringWithINRSymbol(order.GetAmount()))
		commonFields.Title = s.config.RecurringPaymentNotificationParams.MandateAuthorizedPayer.Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.config.RecurringPaymentNotificationParams.MandateAuthorizedPayer.IconAttr)
		commonFields.Deeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_AUTHORIZE_RECURRING_PAYMENT_SCREEN,
			ScreenOptions: &deeplink.Deeplink_AuthorizeRecurringPaymentScreenOptions{
				AuthorizeRecurringPaymentScreenOptions: &deeplink.AuthorizeRecurringPaymentScreenOptions{
					RecurringPaymentId: recurringPayment.GetId(),
				},
			},
		}
		notifType = s.config.RecurringPaymentNotificationParams.MandateAuthorizedPayer.NotificationType.ToFCMNotificationTypeEnum()
		notifTTL = s.config.RecurringPaymentNotificationParams.MandateAuthorizedPayer.NotificationExpiry
		// Mandate Acceptance for Payee
	case orderWorkflow == orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH && orderStatus == orderPb.OrderStatus_IN_PAYMENT &&
		recurringPayment.ShareToPayee:
		// using order amount in notification body as execution amount can be different than recurringPayment set amount
		notificationMessage.UserIdentifier = &commspb.SendMessageRequest_UserId{UserId: toActorEntity.GetEntityId()}
		commonFields.Body = fmt.Sprintf(s.config.RecurringPaymentNotificationParams.MandateAcceptancePayee.Body,
			fromActorEntity.GetName().GetFirstName(), money.ToDisplayStringWithINRSymbol(order.GetAmount()))
		commonFields.Title = s.config.RecurringPaymentNotificationParams.MandateAcceptancePayee.Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.config.RecurringPaymentNotificationParams.MandateAcceptancePayee.IconAttr)
		commonFields.Deeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_AUTHORIZE_RECURRING_PAYMENT_SCREEN,
			ScreenOptions: &deeplink.Deeplink_AuthorizeRecurringPaymentScreenOptions{
				AuthorizeRecurringPaymentScreenOptions: &deeplink.AuthorizeRecurringPaymentScreenOptions{
					RecurringPaymentId: recurringPayment.GetId(),
				},
			},
		}
		notifType = s.config.RecurringPaymentNotificationParams.MandateAcceptancePayee.NotificationType.ToFCMNotificationTypeEnum()
		notifTTL = s.config.RecurringPaymentNotificationParams.MandateAcceptancePayee.NotificationExpiry
		// Mandate Paused/Unpause
	case orderWorkflow == orderPb.OrderWorkflow_PAUSE_OR_UNPAUSE_RECURRING_PAYMENT && orderStatus == orderPb.OrderStatus_FULFILLED:

		rpPayload := &pb.RecurringPaymentPauseUnpauseInfo{}
		if err := protojson.Unmarshal(order.GetOrderPayload(), rpPayload); err != nil {
			return fmt.Errorf("error unmarshaling order payload for puaseUnpause %w", err)
		}
		switch {
		// Paused
		case rpPayload.GetAction() == pb.Action_PAUSE:
			commonFields.Body = s.config.RecurringPaymentNotificationParams.MandatePausedPayer.Body
			commonFields.Title = s.config.RecurringPaymentNotificationParams.MandatePausedPayer.Title
			commonFields.IconAttributes = convertToFCMIconAttribute(s.config.RecurringPaymentNotificationParams.MandatePausedPayer.IconAttr)
			commonFields.Deeplink = &deeplink.Deeplink{
				Screen: deeplink.Screen_AUTHORIZE_RECURRING_PAYMENT_SCREEN,
				ScreenOptions: &deeplink.Deeplink_AuthorizeRecurringPaymentScreenOptions{
					AuthorizeRecurringPaymentScreenOptions: &deeplink.AuthorizeRecurringPaymentScreenOptions{
						RecurringPaymentId: recurringPayment.GetId(),
					},
				},
			}
			notifType = s.config.RecurringPaymentNotificationParams.MandatePausedPayer.NotificationType.ToFCMNotificationTypeEnum()
			notifTTL = s.config.RecurringPaymentNotificationParams.MandatePausedPayer.NotificationExpiry
			// Unpause
		case rpPayload.GetAction() == pb.Action_UNPAUSE:
			commonFields.Body = fmt.Sprintf(s.config.RecurringPaymentNotificationParams.MandateUnpausedPayer.Body, fromActorEntity.GetName().GetFirstName())
			commonFields.Title = s.config.RecurringPaymentNotificationParams.MandateUnpausedPayer.Title
			commonFields.IconAttributes = convertToFCMIconAttribute(s.config.RecurringPaymentNotificationParams.MandateUnpausedPayer.IconAttr)
			commonFields.Deeplink = &deeplink.Deeplink{
				Screen: deeplink.Screen_AUTHORIZE_RECURRING_PAYMENT_SCREEN,
				ScreenOptions: &deeplink.Deeplink_AuthorizeRecurringPaymentScreenOptions{
					AuthorizeRecurringPaymentScreenOptions: &deeplink.AuthorizeRecurringPaymentScreenOptions{
						RecurringPaymentId: recurringPayment.GetId(),
					},
				},
			}
			notifType = s.config.RecurringPaymentNotificationParams.MandateUnpausedPayer.NotificationType.ToFCMNotificationTypeEnum()
			notifTTL = s.config.RecurringPaymentNotificationParams.MandateUnpausedPayer.NotificationExpiry
		default:
			return nil
		}
	default:
		// no Notifications needs to be sent
		logger.Info(ctx, "No notification template for this recurringPayment state", zap.String(logger.ORDER_STATUS, orderStatus.String()),
			zap.String(logger.ORDER_WORKFLOW, orderWorkflow.String()))
		return nil
	}
	fcmNotificationWithCommonFields := initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL)
	notificationMessage.Message = &commspb.SendMessageRequest_Notification{
		Notification: &commspb.NotificationMessage{
			Notification: fcmNotificationWithCommonFields,
		},
	}
	res, err := s.commsClient.SendMessage(ctx, notificationMessage)
	switch {
	case err != nil:
		return fmt.Errorf("commsClient.SendMessage() failed %v, %w", err, queue.ErrRPC)
	case !res.GetStatus().IsSuccess():
		return fmt.Errorf("commsClient.SendMessage() returned unsuccessful status %v, %w", res.GetStatus(), queue.ErrRPC)
	default:
		logger.Info(ctx, "Successfully sent Notification from comms service", zap.String(logger.QUEUE_MESSAGE_ID, res.GetMessageId()))
		return nil
	}
}

// nolint: funlen
func (s *Service) sendSINotification(ctx context.Context, order *orderPb.Order, recurringPayment *pb.RecurringPayment,
	fromActorEntity *actorPb.GetEntityDetailsByActorIdResponse, toActorEntity *actorPb.GetEntityDetailsByActorIdResponse,
	currDate string, isMerchantActor bool, piToMaskedAccountNumber string) error {

	// All the notification are being send to fromActor/PAYER.
	var (
		notificationMessage = &commspb.SendMessageRequest{}
		commonFields        = &fcm.CommonTemplateFields{}
		notifType           fcm.NotificationType
		notifTTL            time.Duration
		orderWorkflow       = order.GetWorkflow()
		orderStatus         = order.GetStatus()
		toActorType         = user
	)

	notificationMessage.Type = commspb.QoS_GUARANTEED
	notificationMessage.Medium = commspb.Medium_NOTIFICATION
	notificationMessage.UserIdentifier = &commspb.SendMessageRequest_UserId{UserId: fromActorEntity.GetEntityId()}

	if isMerchantActor {
		toActorType = merchant
	}

	switch {
	// SI Decline
	case orderWorkflow == orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT && orderStatus == orderPb.OrderStatus_FULFILLMENT_FAILED:

		commonFields.Body = fmt.Sprintf(s.config.RecurringPaymentNotificationParams.SIDeclinedPayer.Body, money.ToDisplayStringWithINRSymbol(recurringPayment.GetAmount()),
			toActorType)
		commonFields.Title = s.config.RecurringPaymentNotificationParams.SIDeclinedPayer.Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.config.RecurringPaymentNotificationParams.SIDeclinedPayer.IconAttr)
		commonFields.Deeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
				RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
					RecurringPaymentId: recurringPayment.GetId(),
				},
			},
		}
		notifType = s.config.RecurringPaymentNotificationParams.SIDeclinedPayer.NotificationType.ToFCMNotificationTypeEnum()
		notifTTL = s.config.RecurringPaymentNotificationParams.SIDeclinedPayer.NotificationExpiry
	// SI Created
	case orderWorkflow == orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT && orderStatus == orderPb.OrderStatus_FULFILLED:

		commonFields.Body = fmt.Sprintf(s.config.RecurringPaymentNotificationParams.SICreationPayer.Body,
			money.ToDisplayStringWithINRSymbol(recurringPayment.GetAmount()),
			toActorEntity.GetName().GetFirstName(),
			s.config.RecurringPaymentFrequencyMapping[recurringPayment.GetRecurrenceRule().GetAllowedFrequency().String()])
		commonFields.Title = s.config.RecurringPaymentNotificationParams.SICreationPayer.Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.config.RecurringPaymentNotificationParams.SICreationPayer.IconAttr)
		commonFields.Deeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
				RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
					RecurringPaymentId: recurringPayment.GetId(),
				},
			},
		}
		notifType = s.config.RecurringPaymentNotificationParams.SICreationPayer.NotificationType.ToFCMNotificationTypeEnum()
		notifTTL = s.config.RecurringPaymentNotificationParams.SICreationPayer.NotificationExpiry
	// Successful SI Execution
	case order.IsRecurringPaymentExecutionOrder() && orderStatus == orderPb.OrderStatus_PAID:
		// using order amount in notification body as execution amount can be different than recurringPayment set amount
		commonFields.Body = fmt.Sprintf(s.config.RecurringPaymentNotificationParams.SIExecutionSuccessPayer.Body,
			money.ToDisplayStringWithINRSymbol(order.GetAmount()), piToMaskedAccountNumber)
		commonFields.Title = s.config.RecurringPaymentNotificationParams.SIExecutionSuccessPayer.Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.config.RecurringPaymentNotificationParams.SIExecutionSuccessPayer.IconAttr)
		commonFields.Deeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
				RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
					RecurringPaymentId: recurringPayment.GetId(),
				},
			},
		}
		notifType = s.config.RecurringPaymentNotificationParams.SIExecutionSuccessPayer.NotificationType.ToFCMNotificationTypeEnum()
		notifTTL = s.config.RecurringPaymentNotificationParams.SIExecutionSuccessPayer.NotificationExpiry
	// Failed SI Execution
	case order.IsRecurringPaymentExecutionOrder() && orderStatus == orderPb.OrderStatus_PAYMENT_FAILED:
		// using order amount in notification body as execution amount can be different than recurringPayment set amount
		commonFields.Body = fmt.Sprintf(s.config.RecurringPaymentNotificationParams.SIExecutionFailedPayer.Body,
			money.ToDisplayStringWithINRSymbol(order.GetAmount()), toActorType)
		commonFields.Title = s.config.RecurringPaymentNotificationParams.SIExecutionFailedPayer.Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.config.RecurringPaymentNotificationParams.SIExecutionFailedPayer.IconAttr)
		commonFields.Deeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_RECURRING_PAYMENT_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_RecurringPaymentDetailsScreenOptions{
				RecurringPaymentDetailsScreenOptions: &deeplink.RecurringPaymentDetailsScreenOptions{
					RecurringPaymentId: recurringPayment.GetId(),
				},
			},
		}
		notifType = s.config.RecurringPaymentNotificationParams.SIExecutionFailedPayer.NotificationType.ToFCMNotificationTypeEnum()
		notifTTL = s.config.RecurringPaymentNotificationParams.SIExecutionFailedPayer.NotificationExpiry
	default:
		logger.Debug(ctx, "No notification template for this order state", zap.String(logger.ORDER_STATUS, orderStatus.String()),
			zap.String(logger.ORDER_WORKFLOW, orderWorkflow.String()), zap.String(logger.ORDER_ID, order.GetId()),
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		return nil
	}
	fcmNotificationWithCommonFields := initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL)
	notificationMessage.Message = &commspb.SendMessageRequest_Notification{
		Notification: &commspb.NotificationMessage{
			Notification: fcmNotificationWithCommonFields,
		},
	}
	res, err := s.commsClient.SendMessage(ctx, notificationMessage)
	switch {
	case err != nil:
		return fmt.Errorf("commsClient.SendMessage() failed %v, %w", err, queue.ErrRPC)
	case res.GetStatus().IsRecordNotFound():
		return fmt.Errorf("commsClient.SendMessage() record not found %v, %w", res.GetStatus(), queue.ErrRecordNotFound)
	case !res.GetStatus().IsSuccess():
		return fmt.Errorf("commsClient.SendMessage() returned unsuccessful status %v, %w", res.GetStatus(), queue.ErrRPC)
	default:
		logger.Info(ctx, "Successfully sent Notification from comms service", zap.String(logger.QUEUE_MESSAGE_ID, res.GetMessageId()))
		return nil
	}
}

// nolint: funlen
func (s *Service) sendMandateSms(ctx context.Context, order *orderPb.Order, recurringPayment *pb.RecurringPayment, fromActorEntity *actorPb.GetEntityDetailsByActorIdResponse,
	toActorEntity *actorPb.GetEntityDetailsByActorIdResponse, currDate string, mandateStartDate string) error {

	var (
		smsMessage    = &commspb.SendMessageRequest{}
		smsContent    *commspb.SendMessageRequest_Sms
		orderWorkflow = order.GetWorkflow()
		orderStatus   = order.GetStatus()
	)
	smsMessage.Type = commspb.QoS_GUARANTEED
	smsMessage.Medium = commspb.Medium_SMS
	smsMessage.UserIdentifier = &commspb.SendMessageRequest_UserId{UserId: fromActorEntity.GetEntityId()}

	switch {
	// Mandate Received
	case orderWorkflow == orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT && orderStatus == orderPb.OrderStatus_CREATED &&
		recurringPayment.GetInitiatedBy() == pb.InitiatedBy_PAYEE:

		smsContent = &commspb.SendMessageRequest_Sms{
			Sms: &commspb.SMSMessage{
				SmsOption: &commspb.SmsOption{
					Option: &commspb.SmsOption_MandateReceivedSmsOption{
						MandateReceivedSmsOption: &commspb.MandateReceivedSmsOption{
							SmsType: commspb.SmsType_MANDATE_RECEIVED,
							Option: &commspb.MandateReceivedSmsOption_MandateReceivedV1{
								MandateReceivedV1: &commspb.MandateReceivedSmsOptionV1{
									TemplateVersion: commspb.TemplateVersion_VERSION_V1,
									MandateAmount:   recurringPayment.GetAmount(),
									PayeeName:       toActorEntity.GetName(),
								},
							},
						},
					},
				},
			},
		}
		// Mandate Approved
	case orderWorkflow == orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT && orderStatus == orderPb.OrderStatus_IN_FULFILLMENT &&
		recurringPayment.GetState() == pb.RecurringPaymentState_CREATION_AUTHORISED:

		smsContent = &commspb.SendMessageRequest_Sms{
			Sms: &commspb.SMSMessage{
				SmsOption: &commspb.SmsOption{
					Option: &commspb.SmsOption_MandateApprovedSmsOption{
						MandateApprovedSmsOption: &commspb.MandateApprovedSmsOption{
							SmsType: commspb.SmsType_MANDATE_APPROVED,
							Option: &commspb.MandateApprovedSmsOption_MandateApprovedV1{
								MandateApprovedV1: &commspb.MandateApprovedSmsOptionV1{
									MandateStartDate: mandateStartDate,
									TemplateVersion:  commspb.TemplateVersion_VERSION_V1,
									MandateAmount:    recurringPayment.GetAmount(),
									PayeeName:        toActorEntity.GetName(),
									MandateFrequency: s.config.RecurringPaymentFrequencyMapping[recurringPayment.GetRecurrenceRule().GetAllowedFrequency().String()],
								},
							},
						},
					},
				},
			},
		}
		// Mandate Declined
	case orderWorkflow == orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT && orderStatus == orderPb.OrderStatus_FULFILLMENT_FAILED:

		smsContent = &commspb.SendMessageRequest_Sms{
			Sms: &commspb.SMSMessage{
				SmsOption: &commspb.SmsOption{
					Option: &commspb.SmsOption_MandateDeclinedSmsOption{
						MandateDeclinedSmsOption: &commspb.MandateDeclinedSmsOption{
							SmsType: commspb.SmsType_MANDATE_DECLINED,
							Option: &commspb.MandateDeclinedSmsOption_MandateDeclinedV1{
								MandateDeclinedV1: &commspb.MandateDeclinedSmsOptionV1{
									MandateExecutionDate: currDate,
									TemplateVersion:      commspb.TemplateVersion_VERSION_V1,
									MandateAmount:        recurringPayment.GetAmount(),
									PayeeName:            toActorEntity.GetName(),
									MandateFrequency:     s.config.RecurringPaymentFrequencyMapping[recurringPayment.GetRecurrenceRule().GetAllowedFrequency().String()],
								},
							},
						},
					},
				},
			},
		}

	// Mandate Created
	case orderWorkflow == orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT && orderStatus == orderPb.OrderStatus_FULFILLED:

		smsContent = &commspb.SendMessageRequest_Sms{
			Sms: &commspb.SMSMessage{
				SmsOption: &commspb.SmsOption{
					Option: &commspb.SmsOption_MandateCreatedSmsOption{
						MandateCreatedSmsOption: &commspb.MandateCreatedSmsOption{
							SmsType: commspb.SmsType_MANDATE_CREATED,
							Option: &commspb.MandateCreatedSmsOption_MandateCreatedV1{
								MandateCreatedV1: &commspb.MandateCreatedSmsOptionV1{
									TemplateVersion:  commspb.TemplateVersion_VERSION_V1,
									MandateAmount:    recurringPayment.GetAmount(),
									PayeeName:        toActorEntity.GetName(),
									PayerName:        fromActorEntity.GetName(),
									MandateFrequency: s.config.RecurringPaymentFrequencyMapping[recurringPayment.GetRecurrenceRule().GetAllowedFrequency().String()],
								},
							},
						},
					},
				},
			},
		}

	// Successful Mandate Execution
	case order.IsRecurringPaymentExecutionOrder() && orderStatus == orderPb.OrderStatus_PAID:

		smsContent = &commspb.SendMessageRequest_Sms{
			Sms: &commspb.SMSMessage{
				SmsOption: &commspb.SmsOption{
					Option: &commspb.SmsOption_MandateExecutionSuccessfulSmsOption{
						MandateExecutionSuccessfulSmsOption: &commspb.MandateExecutionSuccessfulSmsOption{
							SmsType: commspb.SmsType_MANDATE_EXECUTION_SUCCESSFUL,
							Option: &commspb.MandateExecutionSuccessfulSmsOption_MandateExecutionSuccessfulV1{
								MandateExecutionSuccessfulV1: &commspb.MandateExecutionSuccessfulSmsOptionV1{
									TemplateVersion: commspb.TemplateVersion_VERSION_V1,
									// using order amount in notification body as execution amount can be different than recurringPayment set amount
									MandateAmount:        order.GetAmount(),
									PayeeName:            toActorEntity.GetName(),
									MandateExecutionDate: currDate,
								},
							},
						},
					},
				},
			},
		}

		// Failed Mandate Execution
	case order.IsRecurringPaymentExecutionOrder() && orderStatus == orderPb.OrderStatus_PAYMENT_FAILED:

		smsContent = &commspb.SendMessageRequest_Sms{
			Sms: &commspb.SMSMessage{
				SmsOption: &commspb.SmsOption{
					Option: &commspb.SmsOption_MandateExecutionFailedSmsOption{
						MandateExecutionFailedSmsOption: &commspb.MandateExecutionFailedSmsOption{
							SmsType: commspb.SmsType_MANDATE_EXECUTION_FAILED,
							Option: &commspb.MandateExecutionFailedSmsOption_MandateExecutionFailedV1{
								MandateExecutionFailedV1: &commspb.MandateExecutionFailedSmsOptionV1{
									TemplateVersion: commspb.TemplateVersion_VERSION_V1,
									// using order amount in notification body as execution amount can be different than recurringPayment set amount
									MandateAmount:        order.GetAmount(),
									PayeeName:            toActorEntity.GetName(),
									MandateExecutionDate: currDate,
								},
							},
						},
					},
				},
			},
		}

		// Mandate Revoked
	case orderWorkflow == orderPb.OrderWorkflow_REVOKE_RECURRING_PAYMENT && orderStatus == orderPb.OrderStatus_FULFILLED:

		smsContent = &commspb.SendMessageRequest_Sms{
			Sms: &commspb.SMSMessage{
				SmsOption: &commspb.SmsOption{
					Option: &commspb.SmsOption_MandateRevokedSmsOption{
						MandateRevokedSmsOption: &commspb.MandateRevokedSmsOption{
							SmsType: commspb.SmsType_MANDATE_REVOKED,
							Option: &commspb.MandateRevokedSmsOption_MandateRevokedV1{
								MandateRevokedV1: &commspb.MandateRevokedSmsOptionV1{
									TemplateVersion: commspb.TemplateVersion_VERSION_V1,
									MandateAmount:   recurringPayment.GetAmount(),
									PayeeName:       toActorEntity.GetName(),
								},
							},
						},
					},
				},
			},
		}
	// Mandate Modified
	case orderWorkflow == orderPb.OrderWorkflow_MODIFY_RECURRING_PAYMENT && orderStatus == orderPb.OrderStatus_FULFILLED:

		smsContent = &commspb.SendMessageRequest_Sms{
			Sms: &commspb.SMSMessage{
				SmsOption: &commspb.SmsOption{
					Option: &commspb.SmsOption_MandateModifiedSmsOption{
						MandateModifiedSmsOption: &commspb.MandateModifiedSmsOption{
							SmsType: commspb.SmsType_MANDATE_MODIFIED,
							Option: &commspb.MandateModifiedSmsOption_MandateModifiedV1{
								MandateModifiedV1: &commspb.MandateModifiedSmsOptionV1{
									TemplateVersion: commspb.TemplateVersion_VERSION_V1,
									MandateAmount:   recurringPayment.GetAmount(),
									PayeeName:       toActorEntity.GetName(),
								},
							},
						},
					},
				},
			},
		}
		// Mandate Authorized
	case orderWorkflow == orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_WITH_AUTH && orderStatus == orderPb.OrderStatus_IN_PAYMENT:

		smsContent = &commspb.SendMessageRequest_Sms{
			Sms: &commspb.SMSMessage{
				SmsOption: &commspb.SmsOption{
					Option: &commspb.SmsOption_MandateAuthorisedSmsOption{
						MandateAuthorisedSmsOption: &commspb.MandateAuthorisedSmsOption{
							SmsType: commspb.SmsType_MANDATE_AUTHORISED,
							Option: &commspb.MandateAuthorisedSmsOption_MandateAuthorisedV1{
								MandateAuthorisedV1: &commspb.MandateAuthorisedSmsOptionV1{
									TemplateVersion: commspb.TemplateVersion_VERSION_V1,
									// using order amount in notification body as execution amount can be different than recurringPayment set amount
									Amount:    order.GetAmount(),
									PayeeName: toActorEntity.GetName(),
								},
							},
						},
					},
				},
			},
		}
		// Mandate Acceptance
	case orderWorkflow == orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH && orderStatus == orderPb.OrderStatus_IN_PAYMENT &&
		recurringPayment.GetShareToPayee():

		smsMessage.UserIdentifier = &commspb.SendMessageRequest_UserId{UserId: toActorEntity.GetEntityId()}
		smsContent = &commspb.SendMessageRequest_Sms{
			Sms: &commspb.SMSMessage{
				SmsOption: &commspb.SmsOption{
					Option: &commspb.SmsOption_MandateAcceptanceSmsOption{
						MandateAcceptanceSmsOption: &commspb.MandateAcceptanceSmsOption{
							SmsType: commspb.SmsType_MANDATE_ACCEPTANCE,
							Option: &commspb.MandateAcceptanceSmsOption_MandateAcceptanceV1{
								MandateAcceptanceV1: &commspb.MandateAcceptanceSmsOptionV1{
									TemplateVersion: commspb.TemplateVersion_VERSION_V1,
									// using order amount in notification body as execution amount can be different than recurringPayment set amount
									Amount:    order.GetAmount(),
									PayerName: fromActorEntity.GetName(),
								},
							},
						},
					},
				},
			},
		}
		// pause unpause
	case orderWorkflow == orderPb.OrderWorkflow_PAUSE_OR_UNPAUSE_RECURRING_PAYMENT && orderStatus == orderPb.OrderStatus_FULFILLED:

		rpPayload := &pb.RecurringPaymentPauseUnpauseInfo{}
		if err := protojson.Unmarshal(order.GetOrderPayload(), rpPayload); err != nil {
			return fmt.Errorf("error unmarshaling order payload for puaseUnpause %w", err)
		}
		switch {
		case rpPayload.GetAction() == pb.Action_PAUSE:
			smsContent = &commspb.SendMessageRequest_Sms{
				Sms: &commspb.SMSMessage{
					SmsOption: &commspb.SmsOption{
						Option: &commspb.SmsOption_MandatePausedSmsOption{
							MandatePausedSmsOption: &commspb.MandatePausedSmsOption{
								SmsType: commspb.SmsType_MANDATE_PAUSED,
								Option: &commspb.MandatePausedSmsOption_MandatePausedV1{
									MandatePausedV1: &commspb.MandatePausedSmsOptionV1{
										TemplateVersion: commspb.TemplateVersion_VERSION_V1,
										PayerName:       fromActorEntity.GetName(),
										PayeeName:       toActorEntity.GetName(),
									},
								},
							},
						},
					},
				},
			}
		case rpPayload.GetAction() == pb.Action_UNPAUSE:
			smsContent = &commspb.SendMessageRequest_Sms{
				Sms: &commspb.SMSMessage{
					SmsOption: &commspb.SmsOption{
						Option: &commspb.SmsOption_MandateUnpausedSmsOption{
							MandateUnpausedSmsOption: &commspb.MandateUnpausedSmsOption{
								SmsType: commspb.SmsType_MANDATE_UNPAUSED,
								Option: &commspb.MandateUnpausedSmsOption_MandateUnpausedV1{
									MandateUnpausedV1: &commspb.MandateUnpausedSmsOptionV1{
										TemplateVersion: commspb.TemplateVersion_VERSION_V1,
										PayeeName:       toActorEntity.GetName(),
									},
								},
							},
						},
					},
				},
			}
		default:
			return nil
		}
	default:
		// no Notifications needs to be sent
		logger.Info(ctx, "No sms template for this recurringPayment state", zap.String(logger.ORDER_STATUS, orderStatus.String()),
			zap.String(logger.ORDER_WORKFLOW, orderWorkflow.String()))
		return nil
	}
	smsMessage.Message = smsContent
	res, err := s.commsClient.SendMessage(ctx, smsMessage)
	switch {
	case err != nil:
		return fmt.Errorf("commsClient.SendMessage() failed %v, %w", err, queue.ErrRPC)
	case !res.GetStatus().IsSuccess():
		return fmt.Errorf("commsClient.SendMessage() returned unsuccessful status %v, %w", res.GetStatus(), queue.ErrRPC)
	default:
		logger.Info(ctx, "Successfully sent SMS from comms service", zap.String(logger.QUEUE_MESSAGE_ID, res.GetMessageId()))
		return nil
	}
}

// nolint: funlen
func (s *Service) sendSISms(ctx context.Context, order *orderPb.Order, recurringPayment *pb.RecurringPayment, fromActorEntity *actorPb.GetEntityDetailsByActorIdResponse,
	toActorEntity *actorPb.GetEntityDetailsByActorIdResponse, currDate string) error {
	var (
		smsMessage    = &commspb.SendMessageRequest{}
		smsContent    *commspb.SendMessageRequest_Sms
		orderWorkflow = order.GetWorkflow()
		orderStatus   = order.GetStatus()
	)
	smsMessage.Type = commspb.QoS_GUARANTEED
	smsMessage.Medium = commspb.Medium_SMS
	smsMessage.UserIdentifier = &commspb.SendMessageRequest_UserId{UserId: fromActorEntity.GetEntityId()}

	switch {
	// SI Decline
	case orderWorkflow == orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT && orderStatus == orderPb.OrderStatus_FULFILLMENT_FAILED:

		smsContent = &commspb.SendMessageRequest_Sms{
			Sms: &commspb.SMSMessage{
				SmsOption: &commspb.SmsOption{
					Option: &commspb.SmsOption_SiDeclinedSmsOption{
						SiDeclinedSmsOption: &commspb.SIDeclinedSmsOption{
							SmsType: commspb.SmsType_SI_DECLINED,
							Option: &commspb.SIDeclinedSmsOption_SiDeclinedV1{
								SiDeclinedV1: &commspb.SIDeclinedSmsOptionV1{
									TemplateVersion: commspb.TemplateVersion_VERSION_V1,
									SiAmount:        recurringPayment.GetAmount(),
									PayeeName:       toActorEntity.GetName(),
								},
							},
						},
					},
				},
			},
		}

	// SI Created
	case orderWorkflow == orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT && orderStatus == orderPb.OrderStatus_FULFILLED:

		smsContent = &commspb.SendMessageRequest_Sms{
			Sms: &commspb.SMSMessage{
				SmsOption: &commspb.SmsOption{
					Option: &commspb.SmsOption_SiCreatedSmsOption{
						SiCreatedSmsOption: &commspb.SICreatedSmsOption{
							SmsType: commspb.SmsType_SI_CREATED,
							Option: &commspb.SICreatedSmsOption_SiCreatedV1{
								SiCreatedV1: &commspb.SICreatedSmsOptionV1{
									TemplateVersion: commspb.TemplateVersion_VERSION_V1,
									SiAmount:        recurringPayment.GetAmount(),
									SiFrequency:     s.config.RecurringPaymentFrequencyMapping[recurringPayment.GetRecurrenceRule().GetAllowedFrequency().String()],
									PayeeName:       toActorEntity.GetName(),
								},
							},
						},
					},
				},
			},
		}

	// Successful SI Execution

	case order.IsRecurringPaymentExecutionOrder() && orderStatus == orderPb.OrderStatus_PAID:

		smsContent = &commspb.SendMessageRequest_Sms{
			Sms: &commspb.SMSMessage{
				SmsOption: &commspb.SmsOption{
					Option: &commspb.SmsOption_SiExecutionSuccessfulSmsOption{
						SiExecutionSuccessfulSmsOption: &commspb.SIExecutionSuccessfulSmsOption{
							SmsType: commspb.SmsType_SI_EXECUTION_SUCCESSFUL,
							Option: &commspb.SIExecutionSuccessfulSmsOption_SiExecutionSuccessfulV1{
								SiExecutionSuccessfulV1: &commspb.SIExecutionSuccessfulSmsOptionV1{
									TemplateVersion: commspb.TemplateVersion_VERSION_V1,
									// using order amount in notification body as execution amount can be different than recurringPayment set amount
									SiAmount:        order.GetAmount(),
									SiExecutionDate: currDate,
									PayeeName:       toActorEntity.GetName(),
								},
							},
						},
					},
				},
			},
		}

	// Failed SI Execution
	case order.IsRecurringPaymentExecutionOrder() && orderStatus == orderPb.OrderStatus_PAYMENT_FAILED:

		smsContent = &commspb.SendMessageRequest_Sms{
			Sms: &commspb.SMSMessage{
				SmsOption: &commspb.SmsOption{
					Option: &commspb.SmsOption_SiExecutionFailedSmsOption{
						SiExecutionFailedSmsOption: &commspb.SIExecutionFailedSmsOption{
							SmsType: commspb.SmsType_SI_EXECUTION_FAILED,
							Option: &commspb.SIExecutionFailedSmsOption_SiExecutionFailedV1{
								SiExecutionFailedV1: &commspb.SIExecutionFailedSmsOptionV1{
									TemplateVersion: commspb.TemplateVersion_VERSION_V1,
									// using order amount in notification body as execution amount can be different than recurringPayment set amount
									SiAmount:  order.GetAmount(),
									PayeeName: toActorEntity.GetName(),
								},
							},
						},
					},
				},
			},
		}
	default:
		// no Notifications needs to be sent
		logger.Info(ctx, "No sms template for this recurringPayment state", zap.String(logger.ORDER_STATUS, orderStatus.String()),
			zap.String(logger.ORDER_WORKFLOW, orderWorkflow.String()))
		return nil

	}
	smsMessage.Message = smsContent
	res, err := s.commsClient.SendMessage(ctx, smsMessage)
	switch {
	case err != nil:
		return fmt.Errorf("commsClient.SendMessage() failed %v, %w", err, queue.ErrRPC)
	case res.GetStatus().IsRecordNotFound():
		return fmt.Errorf("commsClient.SendMessage() record not found %v, %w", res.GetStatus(), queue.ErrRecordNotFound)
	case !res.GetStatus().IsSuccess():
		return fmt.Errorf("commsClient.SendMessage() returned unsuccessful status %v, %w", res.GetStatus(), queue.ErrRPC)
	default:
		logger.Info(ctx, "Successfully sent SMS from comms service", zap.String(logger.QUEUE_MESSAGE_ID, res.GetMessageId()))
		return nil
	}
}

// initFcmNotificationWithCommonFields initializes fcm notification with common fields based on notification type
func initFcmNotificationWithCommonFields(commonFields *fcm.CommonTemplateFields, notificationType fcm.NotificationType, notifTTL time.Duration) *fcm.Notification {
	notification := &fcm.Notification{}

	if notifTTL != 0 {
		commonFields.ExpireAt = timestamppb.New(time.Now().Add(notifTTL))
	}

	switch notificationType { //nolint:exhaustive
	case fcm.NotificationType_IN_APP:
		notification.NotificationTemplates = &fcm.Notification_InAppTemplate{
			InAppTemplate: &fcm.InAppTemplate{
				CommonTemplateFields: commonFields,
				AfterClickAction:     fcm.AfterClickAction_PERSIST,
			},
		}
	case fcm.NotificationType_FULL_SCREEN:
		notification.NotificationTemplates = &fcm.Notification_FullscreenTemplate{FullscreenTemplate: &fcm.FullScreenTemplate{CommonTemplateFields: commonFields}}
	case fcm.NotificationType_BACKGROUND:
		notification.NotificationTemplates = &fcm.Notification_BackgroundTemplate{BackgroundTemplate: &fcm.BackgroundTemplate{CommonTemplateFields: commonFields}}
	case fcm.NotificationType_SYSTEM_TRAY:
		notification.NotificationTemplates = &fcm.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcm.SystemTrayTemplate{CommonTemplateFields: commonFields}}
	}

	notification.NotificationType = notificationType
	return notification
}

func convertToFCMIconAttribute(iconAttr *payPkg.IconAttribute) *fcm.IconAttributes {
	if iconAttr == nil {
		return nil
	}
	return &fcm.IconAttributes{
		IconUrl:         iconAttr.IconURL,
		IconName:        iconAttr.IconName,
		BackgroundColor: iconAttr.ColourCode,
	}
}

func (s *Service) getActorById(ctx context.Context, actorId string) (*actorPb.GetActorByIdResponse, error) { // nolint:dupl
	res, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{
		Id: actorId,
	})
	switch {
	case err != nil:
		return nil, fmt.Errorf("actorClient.getActorTYpe() failed %v, %w", err, queue.ErrRPC)
	case res.GetStatus().IsRecordNotFound():
		return nil, fmt.Errorf("actorClient.getActorTYpe() returned record not found for actor: %v: %w", actorId, queue.ErrPermanent)
	case !res.GetStatus().IsSuccess():
		return nil, fmt.Errorf("actorClient.getActorTYpe() returned unsuccessful status %v, %w", res.GetStatus(), queue.ErrRPC)
	default:
		return res, nil
	}
}

func (s *Service) getActorDetails(ctx context.Context, actorId string) (*actorPb.GetEntityDetailsByActorIdResponse, error) { // nolint:dupl
	res, err := s.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{ActorId: actorId})
	switch {
	case err != nil:
		return nil, fmt.Errorf("actorClient.GetEntityDetailsByActorId() failed %v, %w", err, queue.ErrRPC)
	case res.GetStatus().IsRecordNotFound():
		return nil, fmt.Errorf("actorClient.GetEntityDetailsByActorId() returned record not found for actor: %v: %w", actorId, queue.ErrPermanent)
	case !res.GetStatus().IsSuccess():
		return nil, fmt.Errorf("actorClient.GetEntityDetailsByActorId() returned unsuccessful status %v, %w", res.GetStatus(), queue.ErrRPC)
	default:
		return res, nil
	}
}
