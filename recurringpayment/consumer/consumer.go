package consumer

import (
	"context"
	"fmt"
	"strings"

	"github.com/jonboulle/clockwork"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"

	queuePb "github.com/epifi/be-common/api/queue"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	idGen "github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/pkg/feature/release"

	actorPb "github.com/epifi/gamma/api/actor"
	cardPb "github.com/epifi/gamma/api/card"
	dcMandatePb "github.com/epifi/gamma/api/card/debitcardmandate"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	cpPb "github.com/epifi/gamma/api/card/provisioning"
	merchantPb "github.com/epifi/gamma/api/merchant"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	parsedTxnEnums "github.com/epifi/gamma/api/order/payment/notification/enums"
	"github.com/epifi/gamma/api/paymentinstrument"
	recurringpaymentPb "github.com/epifi/gamma/api/recurringpayment"
	consumerPb "github.com/epifi/gamma/api/recurringpayment/consumer"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	savingsClientPb "github.com/epifi/gamma/api/savings"
	timelinePb "github.com/epifi/gamma/api/timeline"
	vgMerchantResolutionPb "github.com/epifi/gamma/api/vendorgateway/merchantresolution"
	enachVgPb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
	"github.com/epifi/gamma/pkg/vendorapi/inhouse/merchantresolution"
	rpServerGenConf "github.com/epifi/gamma/recurringpayment/config/server/genconf"
	"github.com/epifi/gamma/recurringpayment/dao"
	"github.com/epifi/gamma/recurringpayment/internal"
	"github.com/epifi/gamma/recurringpayment/wire/types"
)

type ConsumerService struct {
	gconf                    *rpServerGenConf.Config
	recurringPaymentClient   recurringpaymentPb.RecurringPaymentServiceClient
	savingsClient            savingsClientPb.SavingsClient
	enachVgClient            enachVgPb.EnachClient
	piClient                 paymentinstrument.PiClient
	actorProcessor           internal.ActorProcessor
	enachClient              enachPb.EnachServiceClient
	rpDao                    dao.RecurringPaymentDao
	rpActionDao              dao.RecurringPaymentsActionDao
	eventBroker              events.Broker
	cpClient                 cpPb.CardProvisioningClient
	dcMandateClient          dcMandatePb.DebitCardMandateServiceClient
	actorClient              actorPb.ActorClient
	merchantClient           merchantPb.MerchantServiceClient
	timelineClient           timelinePb.TimelineServiceClient
	orderClient              orderPb.OrderServiceClient
	uuidGen                  idGen.IUuidGenerator
	merchantResolutionClient vgMerchantResolutionPb.MerchantResolutionServer
	failedEnachTxnPublisher  types.FetchAndCreateFailedEnachTransactionPublisher
	clock                    clockwork.Clock
	releaseEvaluator         release.IEvaluator
}

func NewConsumerService(gconf *rpServerGenConf.Config, recurringPaymentClient recurringpaymentPb.RecurringPaymentServiceClient, savingsClient savingsClientPb.SavingsClient,
	enachVgClient enachVgPb.EnachClient,
	piClient paymentinstrument.PiClient,
	actorProcessor internal.ActorProcessor, enachClient enachPb.EnachServiceClient, rpDao dao.RecurringPaymentDao,
	rpActionDao dao.RecurringPaymentsActionDao, eventBroker events.Broker,
	cpClient cpPb.CardProvisioningClient, dcMandateClient dcMandatePb.DebitCardMandateServiceClient,
	actorClient actorPb.ActorClient, merchantClient merchantPb.MerchantServiceClient, timelineClient timelinePb.TimelineServiceClient, orderClient orderPb.OrderServiceClient,
	uuidGen idGen.IUuidGenerator, merchantResClient *merchantresolution.Service,
	failedEnachTxnPublisher types.FetchAndCreateFailedEnachTransactionPublisher,
	clock clockwork.Clock, releaseEvaluator release.IEvaluator,
) *ConsumerService {
	return &ConsumerService{
		gconf:                    gconf,
		recurringPaymentClient:   recurringPaymentClient,
		savingsClient:            savingsClient,
		enachVgClient:            enachVgClient,
		piClient:                 piClient,
		actorProcessor:           actorProcessor,
		enachClient:              enachClient,
		rpDao:                    rpDao,
		rpActionDao:              rpActionDao,
		eventBroker:              eventBroker,
		cpClient:                 cpClient,
		dcMandateClient:          dcMandateClient,
		actorClient:              actorClient,
		merchantClient:           merchantClient,
		timelineClient:           timelineClient,
		orderClient:              orderClient,
		uuidGen:                  uuidGen,
		merchantResolutionClient: merchantResClient,
		failedEnachTxnPublisher:  failedEnachTxnPublisher,
		clock:                    clock,
		releaseEvaluator:         releaseEvaluator,
	}
}

// compile time check to ensure ConsumerService implements ConsumerServer
var _ consumerPb.ConsumerServer = &ConsumerService{}

var (
	getSuccessRes = func() *consumerPb.ConsumerResponse {
		return &consumerPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS}}
	}
	getPermanentFailureRes = func() *consumerPb.ConsumerResponse {
		return &consumerPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE}}
	}
	getTransientFailureRes = func() *consumerPb.ConsumerResponse {
		return &consumerPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}
	}

	switchNotificationStatusToRpActionStateMap = map[cardEnumsPb.TransactionState]recurringpaymentPb.ActionState{
		cardEnumsPb.TransactionState_TRANSACTION_STATE_FAILURE: recurringpaymentPb.ActionState_ACTION_FAILURE,
		cardEnumsPb.TransactionState_TRANSACTION_STATE_SUCCESS: recurringpaymentPb.ActionState_ACTION_SUCCESS,
	}
)

const (
	enachParticularsDelimiter = "/"
	rawNotificationDebitKey   = "DEBIT"
)

// ProcessRecurringPaymentCreationAuthorizationVendorCallback processes the authorization callback received from the vendor for recurring payment creation authorization.
// For some recurring payment types like ENACH, authorization happens off app and we'll receive authorization callback from vendor once user completes the authorization.
func (c *ConsumerService) ProcessRecurringPaymentCreationAuthorizationVendorCallback(ctx context.Context, req *consumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest) (*consumerPb.ConsumerResponse, error) {
	// get recurring payment for which authorization callback is received
	recurringPaymentIdRes, err := c.recurringPaymentClient.GetRecurringPaymentIdByVendorRequestId(ctx, &recurringpaymentPb.GetRecurringPaymentIdByVendorRequestIdRequest{
		RecurringPaymentType: req.GetRecurringPaymentType(),
		VendorRequestId:      req.GetVendorRequestId(),
	})
	if rpcErr := epifigrpc.RPCError(recurringPaymentIdRes, err); rpcErr != nil {
		logger.Error(ctx, "recurringPaymentClient.GetRecurringPaymentByVendorRequestId rpc call failed", zap.Any(logger.REQUEST, req))
		return getTransientFailureRes(), nil
	}

	authorizeRecurringPaymentReq := &recurringpaymentPb.AuthorizeCreationV1Request{RecurringPaymentId: recurringPaymentIdRes.GetRecurringPaymentId()}
	authorizeRecurringPaymentReq.AuthMetadata, err = getRecurringPaymentCreationAuthMetaFromVendorAuthCallback(ctx, req)
	if err != nil {
		logger.Error(ctx, "error fetching auth metadata from vendor callback", zap.Any(logger.VENDOR_REQUEST, req.GetVendorRequestId()), zap.Error(err))
		return getPermanentFailureRes(), nil
	}

	// authorize recurring payment creation on receiving vendor callbacks
	authorizeCreationRes, err := c.recurringPaymentClient.AuthorizeCreationV1(ctx, authorizeRecurringPaymentReq)
	if rpcErr := epifigrpc.RPCError(authorizeCreationRes, err); rpcErr != nil {
		logger.Error(ctx, "recurringPaymentClient.AuthorizeCreationV1 rpc call failed", zap.Any(logger.VENDOR_REQUEST, req.GetVendorRequestId()), zap.Error(rpcErr))
		return getTransientFailureRes(), nil
	}

	return getSuccessRes(), nil
}

func getRecurringPaymentCreationAuthMetaFromVendorAuthCallback(_ context.Context, vendorCallbackReq *consumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest) (*recurringpaymentPb.RecurringPaymentCreationAuthMetadata, error) {
	creationAuthMetadata := &recurringpaymentPb.RecurringPaymentCreationAuthMetadata{}

	// populate recurring payment type specific metadata
	switch vendorCallbackReq.GetRecurringPaymentType() {
	case recurringpaymentPb.RecurringPaymentType_ENACH_MANDATES:
		creationAuthMetadata.RecurringPaymentTypeSpecificMetadata = &recurringpaymentPb.RecurringPaymentCreationAuthMetadata_EnachMetadata{
			EnachMetadata: &recurringpaymentPb.RecurringPaymentCreationAuthMetadata_ENachAuthMetadata{
				Umrn:                       vendorCallbackReq.GetEnachPayload().GetUmrn(),
				NpciRefId:                  vendorCallbackReq.GetEnachPayload().GetNpciRefId(),
				DestBankReferenceNumber:    vendorCallbackReq.GetEnachPayload().GetDestBankReferenceNumber(),
				MerchantReferenceMessageId: vendorCallbackReq.GetEnachPayload().GetMerchantReferenceMessageId(),
			},
		}
	default:
		return nil, fmt.Errorf("unsupported recurring payment type for creation auth metadata, %s", vendorCallbackReq.GetRecurringPaymentType().String())
	}

	return creationAuthMetadata, nil
}

func (c *ConsumerService) CreateOffAppRecurringPayment(ctx context.Context, request *consumerPb.CreateOffAppRecurringPaymentRequest) (*consumerPb.ConsumerResponse, error) {
	// TODO implement me
	panic("implement me")
}

func (c *ConsumerService) ProcessOffAppRecurringPaymentExecution(ctx context.Context, req *orderPb.OrderUpdate) (*consumerPb.ConsumerResponse, error) {
	var (
		err error
	)

	// no transactions to be linked to recurring payment
	if len(req.GetOrderWithTransactions().GetTransactions()) == 0 {
		logger.Error(ctx, "no transactions found in order update", zap.String(logger.ORDER_ID, req.GetOrderWithTransactions().GetOrder().GetId()))
		return getPermanentFailureRes(), nil
	}

	// cannot link order to a recurring payment without client request id
	if req.GetOrderWithTransactions().GetOrder().GetClientReqId() == "" {
		logger.Error(ctx, "client request id not found in order update", zap.String(logger.ORDER_ID, req.GetOrderWithTransactions().GetOrder().GetId()))
		return getPermanentFailureRes(), nil
	}

	switch {
	case req.GetOrderWithTransactions().GetTransactions()[0].GetMetadata().GetParserTemplate().GetId() == parsedTxnEnums.ParserTemplateId_PARSER_TEMPLATE_ID_OFF_APP_ENACH:
		if req.GetOrderWithTransactions().GetOrder().GetStatus() != orderPb.OrderStatus_PAID {
			logger.Debug(ctx, "skipping processing of enach failed txn", zap.String(logger.CLIENT_REQUEST_ID, req.GetOrderWithTransactions().GetOrder().GetClientReqId()))
			return getSuccessRes(), nil
		}
		err = c.processOffAppEnachTransaction(ctx, req)

	case lo.Some(req.GetOrderWithTransactions().GetOrder().GetTags(), []orderPb.OrderTag{orderPb.OrderTag_DC_MANDATE_REGISTER, orderPb.OrderTag_DC_MANDATE_PAYMENT}):
		err = c.processOffAppDcMandateTransaction(ctx, req)

	case c.isOffAppEnachFailedTransaction(req):
		err = c.processOffAppEnachFailedTransaction(ctx, req)

	default:
		logger.Error(ctx, "unsupported transaction type for off app recurring payment execution", zap.String(logger.ORDER_ID, req.GetOrderWithTransactions().GetOrder().GetId()), zap.String(logger.TEMPLATE_ID, req.GetOrderWithTransactions().GetTransactions()[0].GetMetadata().GetParserTemplate().GetId().String()))
		return getPermanentFailureRes(), nil
	}

	switch {
	case err == nil:
		logger.Info(ctx, "off app recurring payment transaction processed successfully", zap.String(logger.ORDER_ID, req.GetOrderWithTransactions().GetOrder().GetId()))
		return getSuccessRes(), nil
	case errors.Is(err, epifierrors.ErrTransient):
		logger.Error(ctx, "transient error processing off app recurring payment transaction", zap.Error(err), zap.String(logger.ORDER_ID, req.GetOrderWithTransactions().GetOrder().GetId()))
		return getTransientFailureRes(), nil
	case errors.Is(err, epifierrors.ErrPermanent):
		logger.Error(ctx, "permanent error processing off app recurring payment transaction", zap.Error(err), zap.String(logger.ORDER_ID, req.GetOrderWithTransactions().GetOrder().GetId()))
		return getPermanentFailureRes(), nil
	default:
		logger.Error(ctx, "unknown error processing off app recurring payment transaction", zap.Error(err), zap.String(logger.ORDER_ID, req.GetOrderWithTransactions().GetOrder().GetId()))
		return getTransientFailureRes(), nil
	}
}

func (c *ConsumerService) isOffAppEnachFailedTransaction(req *orderPb.OrderUpdate) bool {
	if len(req.GetOrderWithTransactions().GetTransactions()) == 0 {
		return false
	}
	if req.GetOrderWithTransactions().GetOrder().GetWorkflow() == orderPb.OrderWorkflow_NO_OP &&
		req.GetOrderWithTransactions().GetOrder().GetProvenance() == orderPb.OrderProvenance_EXTERNAL &&
		req.GetOrderWithTransactions().GetTransactions()[0].GetPaymentProtocol() == paymentPb.PaymentProtocol_ENACH &&
		req.GetOrderWithTransactions().GetTransactions()[0].GetStatus() == paymentPb.TransactionStatus_FAILED {
		return true
	}
	return false
}

func (c *ConsumerService) processOffAppDcMandateTransaction(ctx context.Context, req *orderPb.OrderUpdate) error {
	// fetch switch notification from debit card service
	cardNotificationResp, switchFetchErr := c.cpClient.GetCardSwitchNotification(ctx, &cpPb.GetCardSwitchNotificationRequest{
		Identifier: &cpPb.GetCardSwitchNotificationRequest_ExternalRefId{
			ExternalRefId: req.GetOrderWithTransactions().GetOrder().GetClientReqId(),
		},
	})
	if te := epifigrpc.RPCError(cardNotificationResp, switchFetchErr); te != nil {
		if cardNotificationResp.GetStatus().IsRecordNotFound() {
			return errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("dc switch notification not found for externalRefId: %s", req.GetOrderWithTransactions().GetOrder().GetClientReqId()))
		}
		return errors.Wrap(te, fmt.Sprintf("error while fetching switch notification with externalRefId: %s", req.GetOrderWithTransactions().GetOrder().GetClientReqId()))
	}

	// fetch merchant from merchant service
	merchantResp, merErr := c.merchantClient.GetMerchant(ctx, &merchantPb.GetMerchantRequest{
		Identifier: &merchantPb.GetMerchantRequest_PiId{PiId: req.GetOrderWithTransactions().GetTransactions()[0].GetPiTo()},
	})
	if te := epifigrpc.RPCError(merchantResp, merErr); te != nil {
		if merchantResp.GetStatus().IsRecordNotFound() {
			return errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("merchant not found for PiId: %s", req.GetOrderWithTransactions().GetTransactions()[0].GetPiTo()))
		}
		return errors.Wrap(merErr, fmt.Sprintf("error in fetching merchant for PiId: %s", req.GetOrderWithTransactions().GetTransactions()[0].GetPiTo()))
	}

	// check if recurring payment action already exists
	rpAction, rpActionErr := c.rpActionDao.GetByClientRequestId(ctx, req.GetOrderWithTransactions().GetOrder().GetClientReqId(), false)
	switch {
	case errors.Is(rpActionErr, epifierrors.ErrRecordNotFound), storageV2.IsRecordNotFoundError(rpActionErr):

		// check if mandate already exists
		dcMandateResp, dcMandateErr := c.dcMandateClient.GetDebitCardMandate(ctx, &dcMandatePb.GetDebitCardMandateRequest{
			Identifier: &dcMandatePb.GetDebitCardMandateRequest_DedupeId{
				DedupeId: &dcMandatePb.DebitCardMandateDedupeId{
					CardId:     cardNotificationResp.GetCardNotification().GetCardId(),
					MerchantId: merchantResp.GetMerchant().GetId(),
				},
			},
		})
		if rpcErr := epifigrpc.RPCError(dcMandateResp, dcMandateErr); rpcErr != nil && !dcMandateResp.GetStatus().IsRecordNotFound() {
			return errors.Wrap(rpcErr, "error in fetching debit card mandate")
		}
		if dcMandateResp.GetStatus().IsRecordNotFound() {
			return c.processOffAppRecurringPaymentCreationForDcMandate(ctx, req, cardNotificationResp.GetCardNotification(), merchantResp.GetMerchant().GetId())
		}
		return c.createRecurringPaymentExecutionActionForDcMandate(ctx, req, cardNotificationResp.GetCardNotification(), dcMandateResp.GetDebitCardMandate().GetRecurringPaymentId())

	case rpActionErr != nil:
		return errors.Wrap(rpActionErr, fmt.Sprintf("error while fetching ecurring payment action by clientRequestId: %s", req.GetOrderWithTransactions().GetOrder().GetClientReqId()))
	default:
		logger.Info(ctx, "recurring payment action already exists", zap.String(logger.RECURRING_PAYMENT_ID, rpAction.GetRecurringPaymentId()))
		// CreateOffAppDebitCardMandate rpc first checks if mandate already exists or not for recurringPaymentId, if it does then return AlreadyExists status
		dcMandateResp, dcMandateErr := c.dcMandateClient.CreateOffAppDebitCardMandate(ctx, &dcMandatePb.CreateOffAppDebitCardMandateRequest{
			CardId:             cardNotificationResp.GetCardNotification().GetCardId(),
			MerchantId:         merchantResp.GetMerchant().GetId(),
			RecurringPaymentId: rpAction.GetRecurringPaymentId(),
			Provenance:         cardEnumsPb.DebitCardMandateRegistrationProvenance_DEBIT_CARD_MANDATE_REGISTRATION_PROVENANCE_EXTERNAL,
		})
		if te := epifigrpc.RPCError(dcMandateResp, dcMandateErr); te != nil && !dcMandateResp.GetStatus().IsAlreadyExists() {
			return errors.Wrap(te, fmt.Sprintf("error while creating dc mandate for already existing recurring payment with recurringPayemntId: %s", rpAction.GetRecurringPaymentId()))
		}
		return nil
	}
}

func (c *ConsumerService) createRecurringPaymentExecutionActionForDcMandate(ctx context.Context, req *orderPb.OrderUpdate, switchNotification *cardPb.CardNotification, recurringPaymentId string) error {
	_, createRpActionErr := c.rpActionDao.Create(ctx, &recurringpaymentPb.RecurringPaymentsAction{
		RecurringPaymentId: recurringPaymentId,
		ClientRequestId:    req.GetOrderWithTransactions().GetOrder().GetClientReqId(),
		Action:             recurringpaymentPb.Action_EXECUTE,
		ActionMetadata: &recurringpaymentPb.ActionMetadata{
			ExecuteActionMetadate: &recurringpaymentPb.ExecuteActionMetaData{
				Amount: req.GetOrderWithTransactions().GetOrder().GetAmount(),
				RecurringPaymentTypeSpecificExecutionInfo: &recurringpaymentPb.RecurringPaymentTypeSpecificExecutionInfo{
					Data: &recurringpaymentPb.RecurringPaymentTypeSpecificExecutionInfo_DcMandateExecutionInfo_{
						DcMandateExecutionInfo: &recurringpaymentPb.RecurringPaymentTypeSpecificExecutionInfo_DcMandateExecutionInfo{
							IsRegistrationExecution: lo.Contains(req.GetOrderWithTransactions().GetOrder().GetTags(), orderPb.OrderTag_DC_MANDATE_REGISTER),
						},
					},
				},
			},
		},
		State: switchNotificationStatusToRpActionStateMap[switchNotification.GetStatus()],
	})
	if createRpActionErr != nil {
		return errors.Wrap(createRpActionErr, "error while creating off recurring payment execution action")
	}
	return nil
}

func (c *ConsumerService) processOffAppRecurringPaymentCreationForDcMandate(ctx context.Context, req *orderPb.OrderUpdate, switchNotification *cardPb.CardNotification, merchantId string) error {
	var (
		recurringPaymentId string
	)
	// build recurring payment object from transaction data
	recurringPaymentObj := c.getRecurringPaymentObjectForDcMandate(req)
	// create recurring payment in transaction block to avoid the race conditions when multiple threads are trying to do the same
	createRpInTxnBlockErr := storageV2.RunCRDBTxn(ctx, func(txnCtx context.Context) error {
		createRecurringPayment, createRecurringPaymentErr := c.rpDao.Create(txnCtx, recurringPaymentObj, commontypes.Ownership_EPIFI_TECH)
		if createRecurringPaymentErr != nil {
			return createRecurringPaymentErr
		}

		createActionErr := c.createRecurringPaymentExecutionActionForDcMandate(txnCtx, req, switchNotification, createRecurringPayment.GetId())
		if createActionErr != nil {
			return createActionErr
		}
		recurringPaymentId = createRecurringPayment.GetId()
		return nil
	})
	if createRpInTxnBlockErr != nil {
		return errors.Wrap(createRpInTxnBlockErr, "error in creating recurring payment for dc mandate")
	}

	// create dc mandate entry,
	dcMandate, dcMandateErr := c.dcMandateClient.CreateOffAppDebitCardMandate(ctx, &dcMandatePb.CreateOffAppDebitCardMandateRequest{
		CardId:             switchNotification.GetCardId(),
		MerchantId:         merchantId,
		RecurringPaymentId: recurringPaymentId,
		Provenance:         cardEnumsPb.DebitCardMandateRegistrationProvenance_DEBIT_CARD_MANDATE_REGISTRATION_PROVENANCE_EXTERNAL,
	})
	if err := epifigrpc.RPCError(dcMandate, dcMandateErr); err != nil && !dcMandate.GetStatus().IsAlreadyExists() {
		return errors.Wrap(err, "error in creating debit card mandate")
	}
	return nil
}

func (c *ConsumerService) getRecurringPaymentObjectForDcMandate(req *orderPb.OrderUpdate) *recurringpaymentPb.RecurringPayment {
	return &recurringpaymentPb.RecurringPayment{
		FromActorId:              req.GetOrderWithTransactions().GetOrder().GetFromActorId(),
		ToActorId:                req.GetOrderWithTransactions().GetOrder().GetToActorId(),
		PiFrom:                   req.GetOrderWithTransactions().GetTransactions()[0].GetPiFrom(),
		PiTo:                     req.GetOrderWithTransactions().GetTransactions()[0].GetPiTo(),
		Type:                     recurringpaymentPb.RecurringPaymentType_DEBIT_CARD_MANDATES,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_CARD,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		Amount:                   req.GetOrderWithTransactions().GetOrder().GetAmount(),
		// populating with zero(UNSPECIFIED) as we'll not have value for this field.
		AmountType:     0,
		Provenance:     recurringpaymentPb.RecurrencePaymentProvenance_EXTERNAL,
		State:          recurringpaymentPb.RecurringPaymentState_ACTIVATED,
		RecurrenceRule: &recurringpaymentPb.RecurrenceRule{},
	}
}

func (c *ConsumerService) processOffAppEnachTransaction(ctx context.Context, req *orderPb.OrderUpdate) error {
	umrn := getUmrnFromOffAppEnachDebitTxn(req.GetOrderWithTransactions().GetTransactions()[0])
	if umrn == "" {
		return errors.Wrap(epifierrors.ErrPermanent, "umrn not found in off app enach transaction")
	}

	recurringPaymentId, err := c.fetchRecurringPaymentId(ctx, umrn, req.GetOrderWithTransactions())
	if err != nil {
		return errors.Wrap(err, "error fetching recurring payment id")
	}

	// create recurring payment execution action for the transaction and link to order
	err = c.createRecurringPaymentExecutionActionIfNotExists(
		ctx,
		req.GetOrderWithTransactions().GetOrder().GetClientReqId(),
		recurringPaymentId,
		recurringpaymentPb.ActionState_ACTION_SUCCESS,
	)
	if err != nil {
		return errors.Wrap(err, fmt.Sprintf("error creating recurring payment execution action for umrn %s", umrn))
	}

	return nil

}

func (c *ConsumerService) createRecurringPaymentExecutionActionIfNotExists(ctx context.Context, clientRequestId, recurringPaymentId string, rpActionState recurringpaymentPb.ActionState) error {
	_, err := c.rpActionDao.GetByClientRequestId(ctx, clientRequestId, false)
	switch {
	case err == nil:
		return nil
	case storagev2.IsRecordNotFoundError(err):
		_, createErr := c.rpActionDao.Create(ctx, &recurringpaymentPb.RecurringPaymentsAction{
			RecurringPaymentId: recurringPaymentId,
			ClientRequestId:    clientRequestId,
			Action:             recurringpaymentPb.Action_EXECUTE,
			State:              rpActionState,
		})
		if createErr != nil {
			return errors.Wrap(createErr, "error creating recurring payment action")
		}
		return nil
	default:
		return errors.Wrap(err, "error fetching recurring payment action")
	}
}

func (c *ConsumerService) fetchRecurringPaymentId(ctx context.Context, umrn string, orderWithTxns *orderPb.OrderWithTransactions) (string, error) {
	// initial check if recurring payment action exists for the umrn
	rpAction, err := c.rpActionDao.GetByClientRequestId(ctx, hashUmrn(umrn), false)
	switch {
	case storagev2.IsRecordNotFoundError(err):
		// fetch and create enach mandates for the actor since RP does not exist
		err = c.fetchAndCreateOffAppRecurringPaymentsForEnach(ctx, orderWithTxns.GetOrder().GetFromActorId(), orderWithTxns.GetTransactions()[0].GetPartnerBank())
		if err != nil {
			return "", errors.Wrap(err, "error fetching and creating enach mandate")
		}
	case err != nil:
		return "", errors.Wrap(err, fmt.Sprintf("error fetching recurring payment action for umrn %s", umrn))
	}

	// TODO(akk) - refactor so that we don't need to fetch RP action again
	if rpAction == nil {
		// re-fetch RP action and throw permanent error if not found
		rpAction, err = c.rpActionDao.GetByClientRequestId(ctx, hashUmrn(umrn), false)
		switch {
		case storagev2.IsRecordNotFoundError(err):
			return "", errors.Wrap(epifierrors.ErrPermanent, "recurring payment action not found for umrn")
		case err != nil:
			return "", errors.Wrap(err, fmt.Sprintf("error fetching recurring payment action for umrn %s", umrn))
		}
	}

	return rpAction.GetRecurringPaymentId(), nil
}

func (c *ConsumerService) processOffAppEnachFailedTransaction(ctx context.Context, req *orderPb.OrderUpdate) error {
	umrn := getUmrnFromOffAppEnachDebitFailedTxn(req.GetOrderWithTransactions().GetTransactions()[0])
	if umrn == "" {
		return errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("umrn not found in off app enach transaction with id: %s", req.GetOrderWithTransactions().GetTransactions()[0].GetId()))
	}

	recurringPaymentId, err := c.fetchRecurringPaymentId(ctx, umrn, req.GetOrderWithTransactions())
	if err != nil {
		return errors.Wrap(err, fmt.Sprintf("error fetching recurring payment id for UMRN: %s, txn id: %s", umrn, req.GetOrderWithTransactions().GetTransactions()[0].GetId()))
	}

	err = c.createRecurringPaymentExecutionActionIfNotExists(
		ctx,
		req.GetOrderWithTransactions().GetOrder().GetClientReqId(),
		recurringPaymentId,
		recurringpaymentPb.ActionState_ACTION_FAILURE,
	)
	if err != nil {
		return errors.Wrap(err, fmt.Sprintf("error creating recurring payment execution action for umrn %s, txn id: %s", umrn, req.GetOrderWithTransactions().GetTransactions()[0].GetId()))
	}

	return nil
}

func getUmrnFromOffAppEnachDebitTxn(txn *paymentPb.Transaction) string {
	rawNotifcationDetails, ok := txn.GetRawNotificationDetails()[rawNotificationDebitKey]
	if !ok {
		return ""
	}

	particulars := rawNotifcationDetails.GetParticulars()
	splitParticulars := strings.Split(particulars, enachParticularsDelimiter)
	if len(splitParticulars) < 2 {
		return ""
	}
	return splitParticulars[2]
}

func getUmrnFromOffAppEnachDebitFailedTxn(txn *paymentPb.Transaction) string {
	subStrs := strings.Split(txn.GetRemarks(), " ")
	if len(subStrs) > 1 {
		return subStrs[1]
	}
	return ""
}
