package consumer

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	eventMocks "github.com/epifi/be-common/pkg/events/mocks"
	mock_queue "github.com/epifi/be-common/pkg/queue/mocks"

	"context"
	"errors"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/epifierrors"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/paymentinstrument"
	mockPi "github.com/epifi/gamma/api/paymentinstrument/mocks"
	"github.com/epifi/gamma/api/recurringpayment"
	rpConsumerPb "github.com/epifi/gamma/api/recurringpayment/consumer"
	enach2 "github.com/epifi/gamma/api/recurringpayment/enach"
	enums2 "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	mocks4 "github.com/epifi/gamma/api/recurringpayment/enach/mocks"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/enach/enums"
	mocks2 "github.com/epifi/gamma/api/vendorgateway/openbanking/enach/mocks"
	releaseMock "github.com/epifi/gamma/pkg/feature/release/mocks"
	mocks3 "github.com/epifi/gamma/recurringpayment/dao/mocks"
	"github.com/epifi/gamma/recurringpayment/internal/actor"
	mock_internal "github.com/epifi/gamma/recurringpayment/internal/mocks"
)

func TestConsumerService_FetchAndCreateOffAppRecurringPayments(t *testing.T) {
	type args struct {
		ctx context.Context
		req *rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest
	}
	defaultInternalActorId := "default-internal-actor-id"
	defaultAccountNumber := "default-account-number"
	defaultIfsc := "default-ifsc"
	defaultUmrn := "default-umrn"
	umrn1 := "umrn-1"
	umrn2 := "umrn-2"
	umrn3 := "umrn-3"
	umrn4 := "umrn-4"
	defaultOrgName := "Groww"
	orgName1 := "org-name-1"
	orgName2 := "org-name-2"
	orgName3 := "org-name-3"
	orgName4 := "org-name-4"
	defaultInternalActorPiId := "default-internal-actor-pi-id"
	defaultOtherActorId := "default-other-actor-id"
	defaultOtherActorPiId := "default-other-actor-pi-id"
	defaultOtherActorPi := &paymentinstrument.PaymentInstrument{
		Id:           "default-other-actor-pi-id",
		VerifiedName: "Groww",
	}
	defaultClientReqId := crypto.GetSHA1InBase32(defaultUmrn)
	clientReqId1 := crypto.GetSHA1InBase32(umrn1)
	clientReqId2 := crypto.GetSHA1InBase32(umrn2)
	clientReqId3 := crypto.GetSHA1InBase32(umrn3)
	clientReqId4 := crypto.GetSHA1InBase32(umrn4)
	defaultRecurringPaymentId := "defaul-recurring-payment-id"
	recurringPaymentId1 := "recurring-payment-id-1"
	recurringPaymentId2 := "recurring-payment-id-2"
	recurringPaymentId3 := "recurring-payment-id-3"
	recurringPaymentId4 := "recurring-payment-id-4"
	defaultGetSavingsAccountsEssentialReq := &savings.GetSavingsAccountEssentialsRequest{
		Filter: &savings.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savings.ActorIdBankFilter{
				ActorId:     defaultInternalActorId,
				PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	}
	defaultGetSavingsAccountsEssentialRes := &savings.GetSavingsAccountEssentialsResponse{
		Status: rpc.StatusOk(),
		Account: &savings.SavingsAccountEssentials{
			AccountNo: defaultAccountNumber,
			IfscCode:  defaultIfsc,
		},
	}
	defaultListMandatesReq := &enach.ListEnachMandateRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		AccountNumber: defaultAccountNumber,
	}
	defaultListMandatesRes := &enach.ListEnachMandateResponse{
		Status: rpc.StatusOk(),
		EnachMandates: []*enach.EnachMandate{
			{
				Umrn:             defaultUmrn,
				OrgName:          defaultOrgName,
				MandateFrequency: enums.Frequency_FREQUENCY_MONTHLY,
				MandateStatus:    enums.MandateStatus_MANDATE_STATUS_ACTIVE,
			},
		},
	}
	defaultListMandatesRes2 := &enach.ListEnachMandateResponse{
		Status: rpc.StatusOk(),
		EnachMandates: []*enach.EnachMandate{
			{
				Umrn:             defaultUmrn,
				OrgName:          defaultOrgName,
				MandateFrequency: enums.Frequency_FREQUENCY_MONTHLY,
				MandateStatus:    enums.MandateStatus_MANDATE_STATUS_CANCELLED,
			},
		},
	}
	DefaultListMandateResForMultipleMandates := &enach.ListEnachMandateResponse{
		Status: rpc.StatusOk(),
		EnachMandates: []*enach.EnachMandate{
			{
				Umrn:             umrn1,
				OrgName:          orgName1,
				MandateFrequency: enums.Frequency_FREQUENCY_MONTHLY,
				MandateStatus:    enums.MandateStatus_MANDATE_STATUS_ACTIVE,
			},
			{
				Umrn:             umrn2,
				OrgName:          orgName2,
				MandateFrequency: enums.Frequency_FREQUENCY_YEARLY,
				MandateStatus:    enums.MandateStatus_MANDATE_STATUS_ACTIVE,
			},
			{
				Umrn:             umrn3,
				OrgName:          orgName3,
				MandateFrequency: enums.Frequency_FREQUENCY_WEEKLY,
				MandateStatus:    enums.MandateStatus_MANDATE_STATUS_CANCELLED,
			},
			{
				Umrn:             umrn4,
				OrgName:          orgName4,
				MandateFrequency: enums.Frequency_FREQUENCY_MONTHLY,
				MandateStatus:    enums.MandateStatus_MANDATE_STATUS_CANCELLED,
			},
		},
	}
	defaultGetPiReq := &paymentinstrument.GetPiRequest{
		Type: paymentinstrument.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &paymentinstrument.GetPiRequest_AccountRequestParams_{
			AccountRequestParams: &paymentinstrument.GetPiRequest_AccountRequestParams{
				ActualAccountNumber: defaultAccountNumber,
				IfscCode:            defaultIfsc,
			},
		},
	}
	defaultGetPiRes := &paymentinstrument.GetPiResponse{
		Status: rpc.StatusOk(),
		PaymentInstrument: &paymentinstrument.PaymentInstrument{
			Id: defaultInternalActorPiId,
		},
	}
	defaultResolveOtherActorAndPiForOffAppRecurringPaymentReq := &actor.ResolveOtherActorAndPiForOffAppRecurringPaymentReq{
		InternalActorId: defaultInternalActorId,
		EnachPayload: &actor.EnachPayloadToResolveActorAndPi{
			OrgName: defaultOrgName,
		},
	}
	defaultRecurringPaymentReq := &recurringpayment.RecurringPayment{
		FromActorId: defaultInternalActorId,
		ToActorId:   defaultOtherActorId,
		PiFrom:      defaultInternalActorPiId,
		PiTo:        defaultOtherActorPiId,
		Type:        recurringpayment.RecurringPaymentType_ENACH_MANDATES,
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		RecurrenceRule: &recurringpayment.RecurrenceRule{
			AllowedFrequency: recurringpayment.AllowedFrequency_MONTHLY,
		},
		Provenance:               recurringpayment.RecurrencePaymentProvenance_EXTERNAL,
		Interval:                 &types.Interval{},
		State:                    recurringpayment.RecurringPaymentState_ACTIVATED,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_ENACH,
	}
	defaultRecurringPaymentRes := &recurringpayment.RecurringPayment{
		Id:          defaultRecurringPaymentId,
		FromActorId: defaultInternalActorId,
		ToActorId:   defaultOtherActorId,
		PiFrom:      defaultInternalActorPiId,
		PiTo:        defaultOtherActorPiId,
		Type:        recurringpayment.RecurringPaymentType_ENACH_MANDATES,
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		RecurrenceRule: &recurringpayment.RecurrenceRule{
			AllowedFrequency: recurringpayment.AllowedFrequency_MONTHLY,
		},
		Provenance:               recurringpayment.RecurrencePaymentProvenance_EXTERNAL,
		Interval:                 &types.Interval{},
		State:                    recurringpayment.RecurringPaymentState_ACTIVATED,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_ENACH,
	}
	recurringPaymentRes1 := &recurringpayment.RecurringPayment{
		Id:          recurringPaymentId1,
		FromActorId: defaultInternalActorId,
		ToActorId:   defaultOtherActorId,
		PiFrom:      defaultInternalActorPiId,
		PiTo:        defaultOtherActorPiId,
		Type:        recurringpayment.RecurringPaymentType_ENACH_MANDATES,
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		RecurrenceRule: &recurringpayment.RecurrenceRule{
			AllowedFrequency: recurringpayment.AllowedFrequency_MONTHLY,
		},
		Provenance:               recurringpayment.RecurrencePaymentProvenance_EXTERNAL,
		Interval:                 &types.Interval{},
		State:                    recurringpayment.RecurringPaymentState_ACTIVATED,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_ENACH,
	}
	recurringPaymentRes2 := &recurringpayment.RecurringPayment{
		Id:          recurringPaymentId2,
		FromActorId: defaultInternalActorId,
		ToActorId:   defaultOtherActorId,
		PiFrom:      defaultInternalActorPiId,
		PiTo:        defaultOtherActorPiId,
		Type:        recurringpayment.RecurringPaymentType_ENACH_MANDATES,
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		RecurrenceRule: &recurringpayment.RecurrenceRule{
			AllowedFrequency: recurringpayment.AllowedFrequency_YEARLY,
		},
		Provenance:               recurringpayment.RecurrencePaymentProvenance_EXTERNAL,
		Interval:                 &types.Interval{},
		State:                    recurringpayment.RecurringPaymentState_ACTIVATED,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_ENACH,
	}
	recurringPaymentRes3 := &recurringpayment.RecurringPayment{
		Id:          recurringPaymentId3,
		FromActorId: defaultInternalActorId,
		ToActorId:   defaultOtherActorId,
		PiFrom:      defaultInternalActorPiId,
		PiTo:        defaultOtherActorPiId,
		Type:        recurringpayment.RecurringPaymentType_ENACH_MANDATES,
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		RecurrenceRule: &recurringpayment.RecurrenceRule{
			AllowedFrequency: recurringpayment.AllowedFrequency_WEEKLY,
		},
		Provenance:               recurringpayment.RecurrencePaymentProvenance_EXTERNAL,
		Interval:                 &types.Interval{},
		State:                    recurringpayment.RecurringPaymentState_ACTIVATED,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_ENACH,
	}
	recurringPaymentRes4 := &recurringpayment.RecurringPayment{
		Id:          recurringPaymentId4,
		FromActorId: defaultInternalActorId,
		ToActorId:   defaultOtherActorId,
		PiFrom:      defaultInternalActorPiId,
		PiTo:        defaultOtherActorPiId,
		Type:        recurringpayment.RecurringPaymentType_ENACH_MANDATES,
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		RecurrenceRule: &recurringpayment.RecurrenceRule{
			AllowedFrequency: recurringpayment.AllowedFrequency_MONTHLY,
		},
		Provenance:               recurringpayment.RecurrencePaymentProvenance_EXTERNAL,
		Interval:                 &types.Interval{},
		State:                    recurringpayment.RecurringPaymentState_ACTIVATED,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_ENACH,
	}
	defaultRecurringPaymentActionReq := &recurringpayment.RecurringPaymentsAction{
		RecurringPaymentId: defaultRecurringPaymentId,
		ClientRequestId:    defaultClientReqId,
		Action:             recurringpayment.Action_CREATE,
		State:              recurringpayment.ActionState_ACTION_SUCCESS,
	}
	recurringPaymentAction1 := &recurringpayment.RecurringPaymentsAction{
		RecurringPaymentId: recurringPaymentId1,
		ClientRequestId:    clientReqId1,
		Action:             recurringpayment.Action_CREATE,
		State:              recurringpayment.ActionState_ACTION_SUCCESS,
	}
	recurringPaymentAction2 := &recurringpayment.RecurringPaymentsAction{
		RecurringPaymentId: recurringPaymentId2,
		ClientRequestId:    clientReqId2,
		Action:             recurringpayment.Action_CREATE,
		State:              recurringpayment.ActionState_ACTION_SUCCESS,
	}
	recurringPaymentAction3 := &recurringpayment.RecurringPaymentsAction{
		RecurringPaymentId: recurringPaymentId3,
		ClientRequestId:    clientReqId3,
		Action:             recurringpayment.Action_CREATE,
		State:              recurringpayment.ActionState_ACTION_SUCCESS,
	}
	recurringPaymentAction4 := &recurringpayment.RecurringPaymentsAction{
		RecurringPaymentId: recurringPaymentId4,
		ClientRequestId:    clientReqId4,
		Action:             recurringpayment.Action_CREATE,
		State:              recurringpayment.ActionState_ACTION_SUCCESS,
	}
	defaultCreateOffAppEnachReq := &enach2.CreateOffAppEnachMandateRequest{
		Umrn:               defaultUmrn,
		RecurringPaymentId: defaultRecurringPaymentId,
		Provenance:         enums2.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_EXTERNAL,
		Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
		Ownership:          commontypes.Ownership_EPIFI_TECH,
	}
	tests := []struct {
		name       string
		args       args
		want       *rpConsumerPb.ConsumerResponse
		setupMocks func(mockSavingsClient *mocks.MockSavingsClient, mochEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockReleaseEvaluator *releaseMock.MockIEvaluator, mockFailedEnachTxnPublisher *mock_queue.MockPublisher)
		wantErr    error
	}{
		{
			name: "should return permanent error (no account found for given internal actor id)",
			args: args{
				ctx: context.Background(),
				req: &rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: defaultInternalActorId,
				},
			},
			setupMocks: func(mockSavingsClient *mocks.MockSavingsClient, mochEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockReleaseEvaluator *releaseMock.MockIEvaluator, mockFailedEnachTxnPublisher *mock_queue.MockPublisher) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(&savings.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			want: getPermanentFailureRes(),
		},
		{
			name: "should return transient error (error while fetching savings accounts essentials)",
			args: args{
				ctx: context.Background(),
				req: &rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: defaultInternalActorId,
				},
			},
			setupMocks: func(mockSavingsClient *mocks.MockSavingsClient, mochEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockReleaseEvaluator *releaseMock.MockIEvaluator, mockFailedEnachTxnPublisher *mock_queue.MockPublisher) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(&savings.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: getTransientFailureRes(),
		},
		{
			name: "should return transient error (error while fetching mandates from vendor)",
			args: args{
				ctx: context.Background(),
				req: &rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: defaultInternalActorId,
				},
			},
			setupMocks: func(mockSavingsClient *mocks.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockReleaseEvaluator *releaseMock.MockIEvaluator, mockFailedEnachTxnPublisher *mock_queue.MockPublisher) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(defaultGetSavingsAccountsEssentialRes, nil)
				mockEnachVgClient.EXPECT().ListEnachMandate(context.Background(), defaultListMandatesReq).Return(&enach.ListEnachMandateResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: getTransientFailureRes(),
		},
		{
			name: "should return permanant error (no record found for the given account)",
			args: args{
				ctx: context.Background(),
				req: &rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: defaultInternalActorId,
				},
			},
			setupMocks: func(mockSavingsClient *mocks.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockReleaseEvaluator *releaseMock.MockIEvaluator, mockFailedEnachTxnPublisher *mock_queue.MockPublisher) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(defaultGetSavingsAccountsEssentialRes, nil)
				mockEnachVgClient.EXPECT().ListEnachMandate(context.Background(), defaultListMandatesReq).Return(&enach.ListEnachMandateResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			want: getPermanentFailureRes(),
		},
		{
			name: "should return transient error (error while resolving internal actor pi)",
			args: args{
				ctx: context.Background(),
				req: &rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: defaultInternalActorId,
				},
			},
			setupMocks: func(mockSavingsClient *mocks.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockReleaseEvaluator *releaseMock.MockIEvaluator, mockFailedEnachTxnPublisher *mock_queue.MockPublisher) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(defaultGetSavingsAccountsEssentialRes, nil)
				mockEnachVgClient.EXPECT().ListEnachMandate(context.Background(), defaultListMandatesReq).Return(defaultListMandatesRes, nil)
				mockPiClient.EXPECT().GetPi(context.Background(), defaultGetPiReq).Return(&paymentinstrument.GetPiResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: getTransientFailureRes(),
		},
		{
			name: "should return transient error (failed to check if enach exists)",
			args: args{
				ctx: context.Background(),
				req: &rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: defaultInternalActorId,
				},
			},
			setupMocks: func(mockSavingsClient *mocks.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockReleaseEvaluator *releaseMock.MockIEvaluator, mockFailedEnachTxnPublisher *mock_queue.MockPublisher) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(defaultGetSavingsAccountsEssentialRes, nil)
				mockEnachVgClient.EXPECT().ListEnachMandate(context.Background(), defaultListMandatesReq).Return(defaultListMandatesRes, nil)
				mockPiClient.EXPECT().GetPi(context.Background(), defaultGetPiReq).Return(defaultGetPiRes, nil)
				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: defaultUmrn},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusInternal()}, nil)
			},
			want: getTransientFailureRes(),
		},
		{
			name: "should return successfully (enach mandate already exists with same state)",
			args: args{
				ctx: context.Background(),
				req: &rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: defaultInternalActorId,
				},
			},
			setupMocks: func(mockSavingsClient *mocks.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockReleaseEvaluator *releaseMock.MockIEvaluator, mockFailedEnachTxnPublisher *mock_queue.MockPublisher) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(defaultGetSavingsAccountsEssentialRes, nil)
				mockEnachVgClient.EXPECT().ListEnachMandate(context.Background(), defaultListMandatesReq).Return(defaultListMandatesRes, nil)
				mockPiClient.EXPECT().GetPi(context.Background(), defaultGetPiReq).Return(defaultGetPiRes, nil)
				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: defaultUmrn},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusOk()}, nil)
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientReqId, false).Return(defaultRecurringPaymentActionReq, nil)
				mockDao.EXPECT().GetById(context.Background(), defaultRecurringPaymentId).Return(defaultRecurringPaymentRes, nil)
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpayment.RecurringPaymentType_ENACH_MANDATES, defaultResolveOtherActorAndPiForOffAppRecurringPaymentReq).
					Return(defaultOtherActorPi, defaultOtherActorId, nil)
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
				mockFailedEnachTxnPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", nil)
			},
			want: getSuccessRes(),
		},
		{
			name: "error while fetching recurring payment action (mandate already exists)",
			args: args{
				ctx: context.Background(),
				req: &rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: defaultInternalActorId,
				},
			},
			setupMocks: func(mockSavingsClient *mocks.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockReleaseEvaluator *releaseMock.MockIEvaluator, mockFailedEnachTxnPublisher *mock_queue.MockPublisher) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(defaultGetSavingsAccountsEssentialRes, nil)
				mockEnachVgClient.EXPECT().ListEnachMandate(context.Background(), defaultListMandatesReq).Return(defaultListMandatesRes, nil)
				mockPiClient.EXPECT().GetPi(context.Background(), defaultGetPiReq).Return(defaultGetPiRes, nil)
				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: defaultUmrn},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusOk()}, nil)
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientReqId, false).Return(nil, errors.New("pgdb is down"))
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpayment.RecurringPaymentType_ENACH_MANDATES, defaultResolveOtherActorAndPiForOffAppRecurringPaymentReq).
					Return(defaultOtherActorPi, defaultOtherActorId, nil)
			},
			want: getTransientFailureRes(),
		},
		{
			name: "error while fetching recurring payment (mandate already exists)",
			args: args{
				ctx: context.Background(),
				req: &rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: defaultInternalActorId,
				},
			},
			setupMocks: func(mockSavingsClient *mocks.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockReleaseEvaluator *releaseMock.MockIEvaluator, mockFailedEnachTxnPublisher *mock_queue.MockPublisher) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(defaultGetSavingsAccountsEssentialRes, nil)
				mockEnachVgClient.EXPECT().ListEnachMandate(context.Background(), defaultListMandatesReq).Return(defaultListMandatesRes, nil)
				mockPiClient.EXPECT().GetPi(context.Background(), defaultGetPiReq).Return(defaultGetPiRes, nil)
				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: defaultUmrn},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusOk()}, nil)
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientReqId, false).Return(defaultRecurringPaymentActionReq, nil)
				mockDao.EXPECT().GetById(context.Background(), defaultRecurringPaymentId).Return(nil, errors.New("pgdb is down"))
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpayment.RecurringPaymentType_ENACH_MANDATES, defaultResolveOtherActorAndPiForOffAppRecurringPaymentReq).
					Return(defaultOtherActorPi, defaultOtherActorId, nil)
			},
			want: getTransientFailureRes(),
		},
		{
			name: "error while updating recurring payment (mandate already exists)",
			args: args{
				ctx: context.Background(),
				req: &rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: defaultInternalActorId,
				},
			},
			setupMocks: func(mockSavingsClient *mocks.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockReleaseEvaluator *releaseMock.MockIEvaluator, mockFailedEnachTxnPublisher *mock_queue.MockPublisher) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(defaultGetSavingsAccountsEssentialRes, nil)
				mockEnachVgClient.EXPECT().ListEnachMandate(context.Background(), defaultListMandatesReq).Return(defaultListMandatesRes2, nil)
				mockPiClient.EXPECT().GetPi(context.Background(), defaultGetPiReq).Return(defaultGetPiRes, nil)
				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: defaultUmrn},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusOk()}, nil)
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientReqId, false).Return(defaultRecurringPaymentActionReq, nil)
				mockDao.EXPECT().GetById(context.Background(), defaultRecurringPaymentId).Return(defaultRecurringPaymentRes, nil)
				mockDao.EXPECT().UpdateAndChangeStatus(context.Background(), defaultRecurringPaymentRes, []recurringpayment.RecurringPaymentFieldMask{recurringpayment.RecurringPaymentFieldMask_STATE}, recurringpayment.RecurringPaymentState_ACTIVATED, recurringpayment.RecurringPaymentState_REVOKED).Return(errors.New("pgdb is down"))
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpayment.RecurringPaymentType_ENACH_MANDATES, defaultResolveOtherActorAndPiForOffAppRecurringPaymentReq).
					Return(defaultOtherActorPi, defaultOtherActorId, nil)
			},
			want: getTransientFailureRes(),
		},
		{
			name: "should return transient error (While updating the recurring payment for multiple mandates(all mandates already exist) if we failed to fetch one of recurring payment, but rest of the mandates updated successfully)",
			args: args{
				ctx: context.Background(),
				req: &rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: defaultInternalActorId,
				},
			},
			setupMocks: func(mockSavingsClient *mocks.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockReleaseEvaluator *releaseMock.MockIEvaluator, mockFailedEnachTxnPublisher *mock_queue.MockPublisher) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(defaultGetSavingsAccountsEssentialRes, nil)
				mockEnachVgClient.EXPECT().ListEnachMandate(context.Background(), defaultListMandatesReq).Return(DefaultListMandateResForMultipleMandates, nil)
				mockPiClient.EXPECT().GetPi(context.Background(), defaultGetPiReq).Return(defaultGetPiRes, nil)

				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: umrn1},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusOk()}, nil)
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), clientReqId1, false).Return(recurringPaymentAction1, nil)
				mockDao.EXPECT().GetById(context.Background(), recurringPaymentId1).Return(recurringPaymentRes1, nil)
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpayment.RecurringPaymentType_ENACH_MANDATES, gomock.Any()).
					Return(defaultOtherActorPi, defaultOtherActorId, nil)

				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: umrn2},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusOk()}, nil)
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), clientReqId2, false).Return(recurringPaymentAction2, nil)
				mockDao.EXPECT().GetById(context.Background(), recurringPaymentId2).Return(nil, fmt.Errorf("failed to fetch recurring payment from db"))
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpayment.RecurringPaymentType_ENACH_MANDATES, gomock.Any()).
					Return(defaultOtherActorPi, defaultOtherActorId, nil)

				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: umrn3},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusOk()}, nil)
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), clientReqId3, false).Return(recurringPaymentAction3, nil)
				mockDao.EXPECT().GetById(context.Background(), recurringPaymentId3).Return(recurringPaymentRes3, nil)
				mockDao.EXPECT().UpdateAndChangeStatus(context.Background(), recurringPaymentRes3, []recurringpayment.RecurringPaymentFieldMask{recurringpayment.RecurringPaymentFieldMask_STATE}, recurringpayment.RecurringPaymentState_ACTIVATED, recurringpayment.RecurringPaymentState_REVOKED).Return(nil)
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpayment.RecurringPaymentType_ENACH_MANDATES, gomock.Any()).
					Return(defaultOtherActorPi, defaultOtherActorId, nil)

				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: umrn4},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusOk()}, nil)
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), clientReqId4, false).Return(nil, fmt.Errorf("failed to fetch recurring payment action from db"))
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpayment.RecurringPaymentType_ENACH_MANDATES, gomock.Any()).
					Return(defaultOtherActorPi, defaultOtherActorId, nil)
			},
			want: getTransientFailureRes(),
		},
		{
			name: "successfully update the recurring payment (mandate already exists)",
			args: args{
				ctx: context.Background(),
				req: &rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: defaultInternalActorId,
				},
			},
			setupMocks: func(mockSavingsClient *mocks.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockReleaseEvaluator *releaseMock.MockIEvaluator, mockFailedEnachTxnPublisher *mock_queue.MockPublisher) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(defaultGetSavingsAccountsEssentialRes, nil)
				mockEnachVgClient.EXPECT().ListEnachMandate(context.Background(), defaultListMandatesReq).Return(defaultListMandatesRes2, nil)
				mockPiClient.EXPECT().GetPi(context.Background(), defaultGetPiReq).Return(defaultGetPiRes, nil)
				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: defaultUmrn},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusOk()}, nil)
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientReqId, false).Return(defaultRecurringPaymentActionReq, nil)
				mockDao.EXPECT().GetById(context.Background(), defaultRecurringPaymentId).Return(defaultRecurringPaymentRes, nil)
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpayment.RecurringPaymentType_ENACH_MANDATES, defaultResolveOtherActorAndPiForOffAppRecurringPaymentReq).
					Return(defaultOtherActorPi, defaultOtherActorId, nil)
				mockDao.EXPECT().UpdateAndChangeStatus(context.Background(), defaultRecurringPaymentRes, []recurringpayment.RecurringPaymentFieldMask{recurringpayment.RecurringPaymentFieldMask_STATE}, recurringpayment.RecurringPaymentState_ACTIVATED, recurringpayment.RecurringPaymentState_REVOKED).Return(nil)
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
				mockFailedEnachTxnPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", nil)
			},
			want: getSuccessRes(),
		},
		{
			name: "successfully update required recurring payments (all mandates already exists)",
			args: args{
				ctx: context.Background(),
				req: &rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: defaultInternalActorId,
				},
			},
			setupMocks: func(mockSavingsClient *mocks.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockReleaseEvaluator *releaseMock.MockIEvaluator, mockFailedEnachTxnPublisher *mock_queue.MockPublisher) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(defaultGetSavingsAccountsEssentialRes, nil)
				mockEnachVgClient.EXPECT().ListEnachMandate(context.Background(), defaultListMandatesReq).Return(DefaultListMandateResForMultipleMandates, nil)
				mockPiClient.EXPECT().GetPi(context.Background(), defaultGetPiReq).Return(defaultGetPiRes, nil)

				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: umrn1},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusOk()}, nil)
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), clientReqId1, false).Return(recurringPaymentAction1, nil)
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpayment.RecurringPaymentType_ENACH_MANDATES, gomock.Any()).
					Return(defaultOtherActorPi, defaultOtherActorId, nil)
				mockDao.EXPECT().GetById(context.Background(), recurringPaymentId1).Return(recurringPaymentRes1, nil)

				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: umrn2},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusOk()}, nil)
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), clientReqId2, false).Return(recurringPaymentAction2, nil)
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpayment.RecurringPaymentType_ENACH_MANDATES, gomock.Any()).
					Return(defaultOtherActorPi, defaultOtherActorId, nil)
				mockDao.EXPECT().GetById(context.Background(), recurringPaymentId2).Return(recurringPaymentRes2, nil)

				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: umrn3},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusOk()}, nil)
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), clientReqId3, false).Return(recurringPaymentAction3, nil)
				mockDao.EXPECT().GetById(context.Background(), recurringPaymentId3).Return(recurringPaymentRes3, nil)
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpayment.RecurringPaymentType_ENACH_MANDATES, gomock.Any()).
					Return(defaultOtherActorPi, defaultOtherActorId, nil)
				mockDao.EXPECT().UpdateAndChangeStatus(context.Background(), recurringPaymentRes3, []recurringpayment.RecurringPaymentFieldMask{recurringpayment.RecurringPaymentFieldMask_STATE}, recurringpayment.RecurringPaymentState_ACTIVATED, recurringpayment.RecurringPaymentState_REVOKED).Return(nil)

				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: umrn4},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusOk()}, nil)
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), clientReqId4, false).Return(recurringPaymentAction4, nil)
				mockDao.EXPECT().GetById(context.Background(), recurringPaymentId4).Return(recurringPaymentRes4, nil)
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpayment.RecurringPaymentType_ENACH_MANDATES, gomock.Any()).
					Return(defaultOtherActorPi, defaultOtherActorId, nil)
				mockDao.EXPECT().UpdateAndChangeStatus(context.Background(), recurringPaymentRes4, []recurringpayment.RecurringPaymentFieldMask{recurringpayment.RecurringPaymentFieldMask_STATE}, recurringpayment.RecurringPaymentState_ACTIVATED, recurringpayment.RecurringPaymentState_REVOKED).Return(nil)
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
				mockFailedEnachTxnPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", nil)
			},
			want: getSuccessRes(),
		},
		{
			name: "should return transient error (error while resolving other actor and other actor pi)",
			args: args{
				ctx: context.Background(),
				req: &rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: defaultInternalActorId,
				},
			},
			setupMocks: func(mockSavingsClient *mocks.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockReleaseEvaluator *releaseMock.MockIEvaluator, mockFailedEnachTxnPublisher *mock_queue.MockPublisher) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(defaultGetSavingsAccountsEssentialRes, nil)
				mockEnachVgClient.EXPECT().ListEnachMandate(context.Background(), defaultListMandatesReq).Return(defaultListMandatesRes, nil)
				mockPiClient.EXPECT().GetPi(context.Background(), defaultGetPiReq).Return(defaultGetPiRes, nil)
				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: defaultUmrn},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpayment.RecurringPaymentType_ENACH_MANDATES, defaultResolveOtherActorAndPiForOffAppRecurringPaymentReq).
					Return(nil, "", errors.New("error while resolving other actor and other actor pi"))
			},
			want: getTransientFailureRes(),
		},
		{
			name: "should return transient error (error while fetching recurring payment action)",
			args: args{
				ctx: context.Background(),
				req: &rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: defaultInternalActorId,
				},
			},
			setupMocks: func(mockSavingsClient *mocks.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockReleaseEvaluator *releaseMock.MockIEvaluator, mockFailedEnachTxnPublisher *mock_queue.MockPublisher) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(defaultGetSavingsAccountsEssentialRes, nil)
				mockEnachVgClient.EXPECT().ListEnachMandate(context.Background(), defaultListMandatesReq).Return(defaultListMandatesRes, nil)
				mockPiClient.EXPECT().GetPi(context.Background(), defaultGetPiReq).Return(defaultGetPiRes, nil)
				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: defaultUmrn},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpayment.RecurringPaymentType_ENACH_MANDATES, defaultResolveOtherActorAndPiForOffAppRecurringPaymentReq).
					Return(defaultOtherActorPi, defaultOtherActorId, nil)
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientReqId, false).Return(nil, errors.New("error while fetching recurring payment action"))
			},
			want: getTransientFailureRes(),
		},
		{
			name: "should return successfully (recurring payment already exists)",
			args: args{
				ctx: context.Background(),
				req: &rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: defaultInternalActorId,
				},
			},
			setupMocks: func(mockSavingsClient *mocks.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockReleaseEvaluator *releaseMock.MockIEvaluator, mockFailedEnachTxnPublisher *mock_queue.MockPublisher) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(defaultGetSavingsAccountsEssentialRes, nil)
				mockEnachVgClient.EXPECT().ListEnachMandate(context.Background(), defaultListMandatesReq).Return(defaultListMandatesRes, nil)
				mockPiClient.EXPECT().GetPi(context.Background(), defaultGetPiReq).Return(defaultGetPiRes, nil)
				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: defaultUmrn},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpayment.RecurringPaymentType_ENACH_MANDATES, defaultResolveOtherActorAndPiForOffAppRecurringPaymentReq).
					Return(defaultOtherActorPi, defaultOtherActorId, nil)
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientReqId, false).Return(&recurringpayment.RecurringPaymentsAction{}, nil)
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
				mockFailedEnachTxnPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", nil)
			},
			want: getSuccessRes(),
		},
		{
			name: "should return transient error (failed to create recurring payment)",
			args: args{
				ctx: context.Background(),
				req: &rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: defaultInternalActorId,
				},
			},
			setupMocks: func(mockSavingsClient *mocks.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockReleaseEvaluator *releaseMock.MockIEvaluator, mockFailedEnachTxnPublisher *mock_queue.MockPublisher) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(defaultGetSavingsAccountsEssentialRes, nil)
				mockEnachVgClient.EXPECT().ListEnachMandate(context.Background(), defaultListMandatesReq).Return(defaultListMandatesRes, nil)
				mockPiClient.EXPECT().GetPi(context.Background(), defaultGetPiReq).Return(defaultGetPiRes, nil)
				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: defaultUmrn},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpayment.RecurringPaymentType_ENACH_MANDATES, defaultResolveOtherActorAndPiForOffAppRecurringPaymentReq).
					Return(defaultOtherActorPi, defaultOtherActorId, nil)
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientReqId, false).Return(nil, epifierrors.ErrRecordNotFound)
				mockDao.EXPECT().Create(gomock.Any(), defaultRecurringPaymentReq, commontypes.Ownership_EPIFI_TECH).Return(nil, errors.New("failed to create recurring payment"))
			},
			want: getTransientFailureRes(),
		},
		{
			name: "should return transient error (failed to create recurring payment action)",
			args: args{
				ctx: context.Background(),
				req: &rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: defaultInternalActorId,
				},
			},
			setupMocks: func(mockSavingsClient *mocks.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockReleaseEvaluator *releaseMock.MockIEvaluator, mockFailedEnachTxnPublisher *mock_queue.MockPublisher) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(defaultGetSavingsAccountsEssentialRes, nil)
				mockEnachVgClient.EXPECT().ListEnachMandate(context.Background(), defaultListMandatesReq).Return(defaultListMandatesRes, nil)
				mockPiClient.EXPECT().GetPi(context.Background(), defaultGetPiReq).Return(defaultGetPiRes, nil)
				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: defaultUmrn},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpayment.RecurringPaymentType_ENACH_MANDATES, defaultResolveOtherActorAndPiForOffAppRecurringPaymentReq).
					Return(defaultOtherActorPi, defaultOtherActorId, nil)
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientReqId, false).Return(nil, epifierrors.ErrRecordNotFound)
				mockDao.EXPECT().Create(gomock.Any(), defaultRecurringPaymentReq, commontypes.Ownership_EPIFI_TECH).Return(defaultRecurringPaymentRes, nil)
				mockActionDao.EXPECT().Create(gomock.Any(), defaultRecurringPaymentActionReq).Return(nil, errors.New("error while creating recurring payment action"))
			},
			want: getTransientFailureRes(),
		},
		{
			name: "should return transient error (failed to create enach mandate action)",
			args: args{
				ctx: context.Background(),
				req: &rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: defaultInternalActorId,
				},
			},
			setupMocks: func(mockSavingsClient *mocks.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockReleaseEvaluator *releaseMock.MockIEvaluator, mockFailedEnachTxnPublisher *mock_queue.MockPublisher) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(defaultGetSavingsAccountsEssentialRes, nil)
				mockEnachVgClient.EXPECT().ListEnachMandate(context.Background(), defaultListMandatesReq).Return(defaultListMandatesRes, nil)
				mockPiClient.EXPECT().GetPi(context.Background(), defaultGetPiReq).Return(defaultGetPiRes, nil)
				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: defaultUmrn},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpayment.RecurringPaymentType_ENACH_MANDATES, defaultResolveOtherActorAndPiForOffAppRecurringPaymentReq).
					Return(defaultOtherActorPi, defaultOtherActorId, nil)
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientReqId, false).Return(nil, epifierrors.ErrRecordNotFound)
				mockDao.EXPECT().Create(gomock.Any(), defaultRecurringPaymentReq, commontypes.Ownership_EPIFI_TECH).Return(defaultRecurringPaymentRes, nil)
				mockActionDao.EXPECT().Create(gomock.Any(), defaultRecurringPaymentActionReq).Return(nil, nil)
				mockEnachClient.EXPECT().CreateOffAppEnachMandate(context.Background(), defaultCreateOffAppEnachReq).Return(&enach2.CreateOffAppEnachMandateResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: getTransientFailureRes(),
		},
		{
			name: "happy flow (persisted off app recurring payment)",
			args: args{
				ctx: context.Background(),
				req: &rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: defaultInternalActorId,
				},
			},
			setupMocks: func(mockSavingsClient *mocks.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockReleaseEvaluator *releaseMock.MockIEvaluator, mockFailedEnachTxnPublisher *mock_queue.MockPublisher) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(defaultGetSavingsAccountsEssentialRes, nil)
				mockEnachVgClient.EXPECT().ListEnachMandate(context.Background(), defaultListMandatesReq).Return(defaultListMandatesRes, nil)
				mockPiClient.EXPECT().GetPi(context.Background(), defaultGetPiReq).Return(defaultGetPiRes, nil)
				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: defaultUmrn},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpayment.RecurringPaymentType_ENACH_MANDATES, defaultResolveOtherActorAndPiForOffAppRecurringPaymentReq).
					Return(defaultOtherActorPi, defaultOtherActorId, nil)
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientReqId, false).Return(nil, epifierrors.ErrRecordNotFound)
				mockDao.EXPECT().Create(gomock.Any(), defaultRecurringPaymentReq, commontypes.Ownership_EPIFI_TECH).Return(defaultRecurringPaymentRes, nil)
				mockActionDao.EXPECT().Create(gomock.Any(), defaultRecurringPaymentActionReq).Return(nil, nil)
				mockEnachClient.EXPECT().CreateOffAppEnachMandate(context.Background(), defaultCreateOffAppEnachReq).Return(&enach2.CreateOffAppEnachMandateResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
				mockFailedEnachTxnPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", nil)
			},
			want: getSuccessRes(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockSavingsClient := mocks.NewMockSavingsClient(ctr)
			mockEnachVgClient := mocks2.NewMockEnachClient(ctr)
			mockPiClient := mockPi.NewMockPiClient(ctr)
			mockActorProcessor := mock_internal.NewMockActorProcessor(ctr)
			mockActionDao := mocks3.NewMockRecurringPaymentsActionDao(ctr)
			mockDao := mocks3.NewMockRecurringPaymentDao(ctr)
			mockEnachClient := mocks4.NewMockEnachServiceClient(ctr)
			mockEventBroker := eventMocks.NewMockBroker(ctr)
			mockReleaseEvaluator := releaseMock.NewMockIEvaluator(ctr)
			mockFailedEnachTxnPublisher := mock_queue.NewMockPublisher(ctr)

			tt.setupMocks(mockSavingsClient, mockEnachVgClient, mockPiClient, mockActorProcessor, mockActionDao, mockDao, mockEnachClient, mockReleaseEvaluator, mockFailedEnachTxnPublisher)
			mockEventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
			s := &ConsumerService{
				gconf:                   gconf,
				savingsClient:           mockSavingsClient,
				enachVgClient:           mockEnachVgClient,
				piClient:                mockPiClient,
				actorProcessor:          mockActorProcessor,
				rpActionDao:             mockActionDao,
				rpDao:                   mockDao,
				enachClient:             mockEnachClient,
				eventBroker:             mockEventBroker,
				failedEnachTxnPublisher: mockFailedEnachTxnPublisher,
				releaseEvaluator:        mockReleaseEvaluator,
			}

			got, err := s.FetchAndCreateOffAppRecurringPayments(tt.args.ctx, tt.args.req)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("FetchAndCreateOffAppRecurringPayments() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("FetchAndCreateOffAppRecurringPayments() got = %v, want %v", got, tt.want)
			}
		})
	}
}
