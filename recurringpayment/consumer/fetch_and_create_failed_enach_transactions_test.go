package consumer

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/jonboulle/clockwork"
	"github.com/stretchr/testify/require"
	datePb "google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	idgenMocks "github.com/epifi/be-common/pkg/idgen/mocks"

	orderPb "github.com/epifi/gamma/api/order"
	mockOrderPb "github.com/epifi/gamma/api/order/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	mockPi "github.com/epifi/gamma/api/paymentinstrument/mocks"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpConsumerPb "github.com/epifi/gamma/api/recurringpayment/consumer"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	mocks4 "github.com/epifi/gamma/api/recurringpayment/enach/mocks"
	savingsPb "github.com/epifi/gamma/api/savings"
	mockSavingsPb "github.com/epifi/gamma/api/savings/mocks"
	timelinePb "github.com/epifi/gamma/api/timeline"
	timelineMocks "github.com/epifi/gamma/api/timeline/mocks"
	merchantResolutionPb "github.com/epifi/gamma/api/vendorgateway/merchantresolution"
	mockVgMerchantPb "github.com/epifi/gamma/api/vendorgateway/merchantresolution/mocks"
	vgEnachPb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
	vgEnachEnumsPb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach/enums"
	mocks2 "github.com/epifi/gamma/api/vendorgateway/openbanking/enach/mocks"
	actorProcessor "github.com/epifi/gamma/recurringpayment/internal/actor"
	mock_internal "github.com/epifi/gamma/recurringpayment/internal/mocks"
)

type testArgs struct {
	ctx       context.Context
	actorID   string
	accountNo string
	umrn      string
	userName  string
	fromDate  *datePb.Date
	toDate    *datePb.Date
}

func TestConsumerService_FetchAndCreateFailedEnachTransactions(t *testing.T) {
	t.Parallel()

	ctr := gomock.NewController(t)
	defer ctr.Finish()

	curTime := time.Now()

	// Create mocks for all dependencies
	mockEnachVgClient := mocks2.NewMockEnachClient(ctr)
	mockEnachClient := mocks4.NewMockEnachServiceClient(ctr)
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)
	mockOrderClient := mockOrderPb.NewMockOrderServiceClient(ctr)
	mockActorProcessor := mock_internal.NewMockActorProcessor(ctr)
	mockSavingsClient := mockSavingsPb.NewMockSavingsClient(ctr)
	mockPiClient := mockPi.NewMockPiClient(ctr)
	mockMerchantResolutionClient := mockVgMerchantPb.NewMockMerchantResolutionServer(ctr)
	mockUuidGen := idgenMocks.NewMockIUuidGenerator(ctr)
	mockClock := clockwork.NewFakeClockAt(curTime)

	// Create test service with mocks
	service := &ConsumerService{
		gconf:                    gconf,
		enachVgClient:            mockEnachVgClient,
		enachClient:              mockEnachClient,
		timelineClient:           mockTimelineClient,
		orderClient:              mockOrderClient,
		actorProcessor:           mockActorProcessor,
		savingsClient:            mockSavingsClient,
		piClient:                 mockPiClient,
		merchantResolutionClient: mockMerchantResolutionClient,
		uuidGen:                  mockUuidGen,
		clock:                    mockClock,
	}

	// Setup common test arguments
	now := time.Now()
	commonArgs := testArgs{
		ctx:       context.Background(),
		actorID:   "test-actor-id",
		accountNo: "test-account-no",
		umrn:      "test-umrn",
		userName:  "test-merchant",
		fromDate: &datePb.Date{
			Year:  int32(now.Add(-24 * time.Hour).Year()),
			Month: int32(now.Add(-24 * time.Hour).Month()),
			Day:   int32(now.Add(-24 * time.Hour).Day()),
		},
		toDate: &datePb.Date{
			Year:  int32(now.Year()),
			Month: int32(now.Month()),
			Day:   int32(now.Day()),
		},
	}

	testCases := []struct {
		name          string
		args          testArgs
		setupMocks    func(args testArgs)
		expectedError bool
		expectedRes   *rpConsumerPb.ConsumerResponse
	}{
		{
			name: "Should successfully create txn for failed ENACH transactions",
			args: commonArgs,
			setupMocks: func(args testArgs) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     args.actorID,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpcPb.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						Id:          "sa-id-1",
						AccountNo:   args.accountNo,
						ActorId:     args.actorID,
						IfscCode:    "ifsc-1",
						State:       savingsPb.State_CREATED,
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					},
				}, nil)

				mockPiClient.EXPECT().GetPi(gomock.Any(), &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.GetPiRequest_AccountRequestParams_{
						AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
							ActualAccountNumber: args.accountNo,
							IfscCode:            "ifsc-1",
						},
					},
				}).Return(&piPb.GetPiResponse{
					Status: rpcPb.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:   "pi-id-1",
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: args.accountNo,
								Name:                "user-name-1",
							},
						},
					},
				}, nil)

				// Mock FetchEnachTransactions
				mockEnachVgClient.EXPECT().
					FetchEnachTransactions(gomock.Any(), &vgEnachPb.FetchEnachTransactionsRequest{
						Header:            &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
						TransactionFilter: &vgEnachPb.FetchEnachTransactionsRequest_AccountNumber{AccountNumber: args.accountNo},
						FromDate:          args.fromDate,
						ToDate:            args.toDate,
					}).
					Return(&vgEnachPb.FetchEnachTransactionsResponse{
						Status: rpcPb.StatusOk(),
						Transactions: []*vgEnachPb.EnachTransaction{
							{
								Umrn:     args.umrn,
								UserName: args.userName,
								ResponseStatus: &vgEnachPb.TransactionStatus{
									Status: vgEnachEnumsPb.TransactionStatus_TRANSACTION_STATUS_FAILED,
								},
							},
						},
					}, nil)

				mockMerchantResolutionClient.EXPECT().MerchantResolution(gomock.Any(), &merchantResolutionPb.MerchantResolutionRequest{
					Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IN_HOUSE},
					RawMerchantName: args.userName,
					IsFiTxn:         true,
					IsMerchantTxn:   true,
				}).Return(&merchantResolutionPb.MerchantResolutionResponse{
					Status:       rpcPb.StatusOk(),
					DsMerchantId: "merchant-id-1",
					MerchantName: args.userName,
				}, nil)

				mockUuidGen.EXPECT().GenerateUuid().Return("client-req-id-1")

				// Mock GetEnachMandate
				mockEnachClient.EXPECT().
					GetEnachMandate(gomock.Any(), &enachPb.GetEnachMandateRequest{
						Identifier: &enachPb.GetEnachMandateRequest_Umrn{
							Umrn: args.umrn,
						},
					}).
					Return(&enachPb.GetEnachMandateResponse{
						Status: rpcPb.StatusOk(),
					}, nil)

				// Mock ResolveOtherActorAndPiForOffAppRecurringPayment
				mockActorProcessor.EXPECT().
					ResolveOtherActorAndPiForOffAppRecurringPayment(
						gomock.Any(),
						rpPb.RecurringPaymentType_ENACH_MANDATES,
						&actorProcessor.ResolveOtherActorAndPiForOffAppRecurringPaymentReq{
							InternalActorId: args.actorID,
							EnachPayload: &actorProcessor.EnachPayloadToResolveActorAndPi{
								OrgName: args.userName,
							},
						},
					).
					Return(&piPb.PaymentInstrument{
						Id:                   "pi-1",
						Type:                 piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier:           &piPb.PaymentInstrument_Account{},
						VerifiedName:         "netflix",
						State:                0,
						Capabilities:         nil,
						CreatedAt:            nil,
						UpdatedAt:            nil,
						DeletedAt:            nil,
						IssuerClassification: 0,
						Ownership:            0,
						LastVerifiedAt:       nil,
					}, "other-actor-id", nil)

				// Mock Create Timeline
				mockTimelineClient.EXPECT().
					Create(gomock.Any(), &timelinePb.CreateRequest{
						PrimaryActorId:     args.actorID,
						SecondaryActorId:   "other-actor-id",
						PrimaryActorName:   "user-name-1",
						SecondaryActorName: "netflix",
						Ownership:          timelinePb.Ownership_EPIFI_TECH,
					}).
					Return(&timelinePb.CreateResponse{
						Status: rpcPb.StatusOk(),
					}, nil)

				// Mock CreateOrderWithTransaction
				mockOrderClient.EXPECT().
					CreateOrderWithTransaction(gomock.Any(), &orderPb.CreateOrderWithTransactionRequest{
						OrderParam: &orderPb.CreateOrderWithTransactionRequest_OrderCreationParams{
							ActorFrom:   "test-actor-id",
							ActorTo:     "other-actor-id",
							Workflow:    orderPb.OrderWorkflow_NO_OP,
							Provenance:  orderPb.OrderProvenance_EXTERNAL,
							Status:      orderPb.OrderStatus_PAYMENT_FAILED,
							ClientReqId: "client-req-id-1",
						},
						TransactionParam: &orderPb.CreateOrderWithTransactionRequest_TransactionCreationParams{
							PiFrom:          "pi-id-1",
							PiTo:            "pi-1",
							Remarks:         "UMRN: test-umrn",
							PaymentProtocol: paymentPb.PaymentProtocol_ENACH,
							Status:          paymentPb.TransactionStatus_FAILED,
							ProtocolStatus:  0,
							ReqInfo:         nil,
							ExecutedAt:      nil,
							DetailedStatus: &paymentPb.TransactionDetailedStatus{
								DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
									{
										StatusCodePayer:        "ENA9999",
										StatusDescriptionPayer: "Unknown Status",
										StatusCodePayee:        "",
										StatusDescriptionPayee: "",
										ErrorCategory:          0,
										CreatedAt:              timestamppb.New(curTime),
										State:                  paymentPb.TransactionDetailedStatus_DetailedStatus_FAILURE,
										Api:                    paymentPb.TransactionDetailedStatus_DetailedStatus_ENACH_FAILED_TXN_SYNC,
									},
								},
							},
							OverrideDedupeId: &orderPb.CreateOrderWithTransactionRequest_TransactionCreationParams_DedupeId{
								OverrideDedupeTime: nil,
							},
						},
						DisableWorkflowProcessing: true,
					}).
					Return(&orderPb.CreateOrderWithTransactionResponse{
						Status:      rpcPb.StatusOk(),
						Order:       &orderPb.Order{},
						Transaction: &paymentPb.Transaction{},
					}, nil)
			},
			expectedError: false,
			expectedRes:   getSuccessRes(),
		},
		{
			name: "Should handle transaction already exists gracefully",
			args: commonArgs,
			setupMocks: func(args testArgs) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     args.actorID,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpcPb.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						Id:          "sa-id-1",
						AccountNo:   args.accountNo,
						ActorId:     args.actorID,
						IfscCode:    "ifsc-1",
						State:       savingsPb.State_CREATED,
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					},
				}, nil)

				mockPiClient.EXPECT().GetPi(gomock.Any(), &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.GetPiRequest_AccountRequestParams_{
						AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
							ActualAccountNumber: args.accountNo,
							IfscCode:            "ifsc-1",
						},
					},
				}).Return(&piPb.GetPiResponse{
					Status: rpcPb.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:   "pi-id-1",
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: args.accountNo,
								Name:                "user-name-1",
							},
						},
					},
				}, nil)

				// Mock FetchEnachTransactions
				mockEnachVgClient.EXPECT().
					FetchEnachTransactions(gomock.Any(), &vgEnachPb.FetchEnachTransactionsRequest{
						Header:            &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
						TransactionFilter: &vgEnachPb.FetchEnachTransactionsRequest_AccountNumber{AccountNumber: args.accountNo},
						FromDate:          args.fromDate,
						ToDate:            args.toDate,
					}).
					Return(&vgEnachPb.FetchEnachTransactionsResponse{
						Status: rpcPb.StatusOk(),
						Transactions: []*vgEnachPb.EnachTransaction{
							{
								Umrn:     args.umrn,
								UserName: args.userName,
								ResponseStatus: &vgEnachPb.TransactionStatus{
									Status: vgEnachEnumsPb.TransactionStatus_TRANSACTION_STATUS_FAILED,
								},
							},
						},
					}, nil)

				mockMerchantResolutionClient.EXPECT().MerchantResolution(gomock.Any(), &merchantResolutionPb.MerchantResolutionRequest{
					Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IN_HOUSE},
					RawMerchantName: args.userName,
					IsFiTxn:         true,
					IsMerchantTxn:   true,
				}).Return(&merchantResolutionPb.MerchantResolutionResponse{
					Status:       rpcPb.StatusOk(),
					DsMerchantId: "merchant-id-1",
					MerchantName: args.userName,
				}, nil)

				mockUuidGen.EXPECT().GenerateUuid().Return("client-req-id-1")

				// Mock GetEnachMandate
				mockEnachClient.EXPECT().
					GetEnachMandate(gomock.Any(), &enachPb.GetEnachMandateRequest{
						Identifier: &enachPb.GetEnachMandateRequest_Umrn{
							Umrn: args.umrn,
						},
					}).
					Return(&enachPb.GetEnachMandateResponse{
						Status: rpcPb.StatusOk(),
					}, nil)

				// Mock ResolveOtherActorAndPiForOffAppRecurringPayment
				mockActorProcessor.EXPECT().
					ResolveOtherActorAndPiForOffAppRecurringPayment(
						gomock.Any(),
						rpPb.RecurringPaymentType_ENACH_MANDATES,
						&actorProcessor.ResolveOtherActorAndPiForOffAppRecurringPaymentReq{
							InternalActorId: args.actorID,
							EnachPayload: &actorProcessor.EnachPayloadToResolveActorAndPi{
								OrgName: args.userName,
							},
						},
					).
					Return(&piPb.PaymentInstrument{
						Id:                   "pi-1",
						Type:                 piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier:           &piPb.PaymentInstrument_Account{},
						VerifiedName:         "netflix",
						State:                0,
						Capabilities:         nil,
						CreatedAt:            nil,
						UpdatedAt:            nil,
						DeletedAt:            nil,
						IssuerClassification: 0,
						Ownership:            0,
						LastVerifiedAt:       nil,
					}, "other-actor-id", nil)

				// Mock Create Timeline
				mockTimelineClient.EXPECT().
					Create(gomock.Any(), &timelinePb.CreateRequest{
						PrimaryActorId:     args.actorID,
						SecondaryActorId:   "other-actor-id",
						PrimaryActorName:   "user-name-1",
						SecondaryActorName: "netflix",
						Ownership:          timelinePb.Ownership_EPIFI_TECH,
					}).
					Return(&timelinePb.CreateResponse{
						Status: rpcPb.StatusOk(),
					}, nil)

				// Mock CreateOrderWithTransaction to return AlreadyExists
				mockOrderClient.EXPECT().
					CreateOrderWithTransaction(gomock.Any(), gomock.Any()).
					Return(&orderPb.CreateOrderWithTransactionResponse{
						Status: rpcPb.StatusAlreadyExists(),
					}, nil)
			},
			expectedError: false,
			expectedRes:   getSuccessRes(),
		},
		{
			name: "Should return transient error if ENACH transactions cannot be fetched",
			args: commonArgs,
			setupMocks: func(args testArgs) {

				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     args.actorID,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpcPb.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						Id:          "sa-id-1",
						AccountNo:   args.accountNo,
						ActorId:     args.actorID,
						IfscCode:    "ifsc-1",
						State:       savingsPb.State_CREATED,
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					},
				}, nil)

				// Mock FetchEnachTransactions with error
				mockEnachVgClient.EXPECT().
					FetchEnachTransactions(gomock.Any(), gomock.Any()).
					Return(&vgEnachPb.FetchEnachTransactionsResponse{
						Status: rpcPb.StatusInternal(),
					}, nil)
			},
			expectedError: false,
			expectedRes:   getTransientFailureRes(),
		},
		{
			name: "Should return permanent error if no failed transactions are found",
			args: commonArgs,
			setupMocks: func(args testArgs) {

				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     args.actorID,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpcPb.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						Id:          "sa-id-1",
						AccountNo:   args.accountNo,
						ActorId:     args.actorID,
						IfscCode:    "ifsc-1",
						State:       savingsPb.State_CREATED,
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					},
				}, nil)

				// Mock FetchEnachTransactions for a UMRN which does not exist in our system.
				mockEnachVgClient.EXPECT().
					FetchEnachTransactions(gomock.Any(), gomock.Any()).
					Return(&vgEnachPb.FetchEnachTransactionsResponse{
						Status: rpcPb.StatusRecordNotFound(),
					}, nil)
			},
			expectedError: false,
			expectedRes:   getPermanentFailureRes(),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.setupMocks(tc.args)

			res, err := service.FetchAndCreateFailedEnachTransactions(tc.args.ctx, &rpConsumerPb.FetchAndCreateFailedEnachTransactionsRequest{
				ActorId:  tc.args.actorID,
				FromDate: tc.args.fromDate,
				ToDate:   tc.args.toDate,
			})

			if tc.expectedError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tc.expectedRes, res)
			}
		})
	}
}
