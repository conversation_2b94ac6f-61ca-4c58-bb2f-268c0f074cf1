package consumer

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpConsumerPb "github.com/epifi/gamma/api/recurringpayment/consumer"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	"github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	vgEnachPb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
	vgEnachEnumPb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach/enums"
	"github.com/epifi/gamma/pkg/feature/release"
	rpEvents "github.com/epifi/gamma/recurringpayment/events"
	actorProcessor "github.com/epifi/gamma/recurringpayment/internal/actor"
	"github.com/epifi/gamma/recurringpayment/mapping"
)

// FetchAndCreateOffAppRecurringPayments fetches all the off app recurring payments from vendor and creates recurring payments for them if not already created.
func (s *ConsumerService) FetchAndCreateOffAppRecurringPayments(ctx context.Context, req *rpConsumerPb.FetchAndCreateOffAppRecurringPaymentsRequest) (*rpConsumerPb.ConsumerResponse, error) {
	// as of now we only have use case for enach
	err := s.fetchAndCreateOffAppRecurringPaymentsForEnach(ctx, req.GetActorId(), commonvgpb.Vendor_FEDERAL_BANK)
	switch {
	case errors.Is(err, epifierrors.ErrTransient):
		logger.Error(ctx, "transient error while fetching and creating recurring payments", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return getTransientFailureRes(), nil
	case errors.Is(err, epifierrors.ErrPermanent):
		logger.Error(ctx, "permanent error while fetching and creating recurring payments", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return getPermanentFailureRes(), nil
	case err != nil:
		logger.Error(ctx, "unknown error while fetching and creating recurring payments", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return getTransientFailureRes(), nil
	}
	return getSuccessRes(), nil
}

// fetchAndCreateOffAppRecurringPaymentsForEnach fetches the list of enach mandates from vendor and populate the recurring payments and mandates table
// NOTE: if for the given mandate, it already exists in our table, the status of the mandate will be updated if required
func (s *ConsumerService) fetchAndCreateOffAppRecurringPaymentsForEnach(ctx context.Context, internalActorId string, partnerBank commonvgpb.Vendor) error {

	// step 1: fetch the savings account essentials
	savingsAccountEssentials, savingsAccountEssentialsErr := s.fetchSavingsAccount(ctx, internalActorId, partnerBank)
	if savingsAccountEssentialsErr != nil {
		return savingsAccountEssentialsErr
	}

	// step 2: fetch the active mandates set up on given account number
	enachMandatesList, enachMandatesListErr := s.enachVgClient.ListEnachMandate(ctx, &vgEnachPb.ListEnachMandateRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: partnerBank,
		},
		AccountNumber: savingsAccountEssentials.GetAccountNo(),
	})

	if err := epifigrpc.RPCError(enachMandatesList, enachMandatesListErr); err != nil && !enachMandatesList.GetStatus().IsRecordNotFound() {
		return fmt.Errorf("failed to fetch the active mandates %w", epifierrors.ErrTransient)
	}
	if enachMandatesList.GetStatus().IsRecordNotFound() {
		return fmt.Errorf("no active mandates found for the given account, err: %w", epifierrors.ErrPermanent)
	}

	// step 3: resolve internal actor pi id
	internalActorPi, internalActorPiErr := s.resolveInternalActorPi(ctx, savingsAccountEssentials.GetAccountNo(), savingsAccountEssentials.GetIfscCode())
	if internalActorPiErr != nil {
		return internalActorPiErr
	}

	// step 4: resolve pi and actor for each mandate
	gotUpdateMandateStatusErr := false
	for _, mandate := range enachMandatesList.GetEnachMandates() {
		// check if we already have entry in enach service
		// todo(Harleen Singh): move this to batch rpc call (https://monorail.pointz.in/p/fi-app/issues/detail?id=62240)
		enachMandate, enachMandateErr := s.enachClient.GetEnachMandate(ctx, &enachPb.GetEnachMandateRequest{
			Identifier: &enachPb.GetEnachMandateRequest_Umrn{
				Umrn: mandate.GetUmrn(),
			},
		})
		if err := epifigrpc.RPCError(enachMandate, enachMandateErr); err != nil && !enachMandate.GetStatus().IsRecordNotFound() {
			return fmt.Errorf("failed to check if enach exists in our system,err: %w", epifierrors.ErrTransient)
		}

		otherActorPi, otherActorId, err := s.actorProcessor.ResolveOtherActorAndPiForOffAppRecurringPayment(ctx, rpPb.RecurringPaymentType_ENACH_MANDATES, &actorProcessor.ResolveOtherActorAndPiForOffAppRecurringPaymentReq{
			InternalActorId: internalActorId,
			EnachPayload: &actorProcessor.EnachPayloadToResolveActorAndPi{
				OrgName: mandate.GetOrgName(),
			},
		})
		if err != nil {
			return fmt.Errorf("failed to resolve other actor %s, %w", err.Error(), epifierrors.ErrTransient)
		}
		logger.Info(ctx, "resolved Other actor pi id and actor id: ", zap.String(logger.ACTOR_ID_V2, otherActorId), zap.String("pi id", otherActorPi.GetId()))
		// step 5: create recurring payment object
		recurringPaymentObj := convertEnachMandateVgResponseToRecurringPayment(internalActorId, otherActorId, internalActorPi.GetId(), otherActorPi.GetId(), partnerBank, mandate)

		// check if mandate already exists in our system
		if enachMandate.GetStatus().IsSuccess() {
			isStatusUpdated, updateErr := s.updateMandateStatus(ctx, mandate)
			if updateErr != nil {
				gotUpdateMandateStatusErr = true
				logger.Error(ctx, "failed to update mandate status for eNACH mandate", zap.Error(updateErr), zap.String(logger.UMRN, mandate.GetUmrn()))
			}
			// Fire off app enach action event in case status is updated for the mandate
			if isStatusUpdated {
				goroutine.RunWithDefaultTimeout(epificontext.WithEventAttributesV2(ctx), func(gctx context.Context) {
					s.eventBroker.AddToBatch(gctx, rpEvents.NewOffAppEnachMandateAction(recurringPaymentObj.GetFromActorId(), hashUmrn(mandate.GetUmrn()), otherActorPi.GetVerifiedName(), moneyPkg.ToDisplayStringWithoutSymbol(mandate.GetMaxAmount()), mandate.GetMandateFrequency().String(), mandateStateToRecurringPaymentState[mandate.GetMandateStatus()].String(), recurringPaymentObj.GetInterval().GetStartTime().AsTime(), recurringPaymentObj.GetInterval().GetEndTime().AsTime()))
				})
			}
			continue
		}

		// step 6: create recurring payment
		createRecurringPaymentErr := s.createRecurringPayment(ctx, mandate.GetUmrn(), recurringPaymentObj)
		if createRecurringPaymentErr != nil {
			return createRecurringPaymentErr
		}

		// Event to identify when off app enach mandates are created in the system
		goroutine.RunWithDefaultTimeout(epificontext.WithEventAttributesV2(ctx), func(gctx context.Context) {
			s.eventBroker.AddToBatch(gctx, rpEvents.NewOffAppEnachMandateAction(recurringPaymentObj.GetFromActorId(), hashUmrn(mandate.GetUmrn()), otherActorPi.GetVerifiedName(), moneyPkg.ToDisplayStringWithoutSymbol(mandate.GetMaxAmount()), mandate.GetMandateFrequency().String(), mandateStateToRecurringPaymentState[mandate.GetMandateStatus()].String(), recurringPaymentObj.GetInterval().GetStartTime().AsTime(), recurringPaymentObj.GetInterval().GetEndTime().AsTime()))
		})
	}
	if gotUpdateMandateStatusErr {
		return fmt.Errorf("failed to update mandates status in our db")
	}

	if s.isFailedEnachTransactionsFeatureEnabledForActor(ctx, internalActorId) {
		fromTime, toTime := time.Now().Add(-time.Duration(s.gconf.EnachConsumerConfig().NumDaysToFetchFailedTxns())*24*time.Hour).In(datetime.IST), time.Now().In(datetime.IST)
		_, publishErr := s.failedEnachTxnPublisher.Publish(ctx, &rpConsumerPb.FetchAndCreateFailedEnachTransactionsRequest{
			ActorId: internalActorId,
			FromDate: &date.Date{
				Year:  int32(fromTime.Year()),
				Month: int32(fromTime.Month()),
				Day:   int32(fromTime.Day()),
			},
			ToDate: &date.Date{
				Year:  int32(toTime.Year()),
				Month: int32(toTime.Month()),
				Day:   int32(toTime.Day()),
			},
		})
		if publishErr != nil {
			// currently not returning the error to see how often we hit this case.
			logger.Error(ctx, "failed to publish message to failed enach txn queue", zap.Error(publishErr))
		}
	}
	return nil
}

// isFailedEnachTransactionsFeatureEnabledForActor checks if the failed enach transactions feature is enabled for a specific actor
func (s *ConsumerService) isFailedEnachTransactionsFeatureEnabledForActor(ctx context.Context, actorId string) bool {
	// Define the feature constraint
	constraint := release.NewCommonConstraintData(types.Feature_FEATURE_FAILED_ENACH_TRANSACTIONS).WithActorId(actorId)

	// We mostly will be trying to create failed enach txns via jenkins job which does not have a platform and version
	// In such cases, we will automatically assume that the platform constraints are passed by
	// hard-coding platform constraints that pass the platform version requirements.
	evalCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()
	if epificontext.AppPlatformFromContext(ctx) == commontypes.Platform_PLATFORM_UNSPECIFIED {
		evalCtx = epificontext.CtxWithAppPlatform(evalCtx, commontypes.Platform_ANDROID)
		evalCtx = epificontext.CtxWithAppVersionCode(evalCtx, "457")
	}

	// Evaluate if the feature is enabled for this actor
	isEnabled, err := s.releaseEvaluator.Evaluate(evalCtx, constraint)
	if err != nil {
		logger.Error(ctx, "Error evaluating feature flag",
			zap.String(logger.ACTOR_ID_V2, actorId),
			zap.Error(err))
		return false
	}

	return isEnabled
}

// NOTE: we have dedupe logic on recurring payment action because we do not have any identifier to fetch recurring payment
// so recurring payment action is created only to have the dedupe logic in case of off-app recurring payment
func (s *ConsumerService) createRecurringPayment(ctx context.Context, umrn string, recurringPayment *rpPb.RecurringPayment) error {

	// NOTE: hashed umrn is used as the client request id in the recurring payment create action
	// this is required to dedupe the recurring payment for the same umrn
	hashedUmrn := hashUmrn(umrn)
	// check if recurring payment action already exists
	rpAction, rpActionErr := s.rpActionDao.GetByClientRequestId(ctx, hashedUmrn, false)
	switch {
	case rpActionErr != nil && !errors.Is(rpActionErr, epifierrors.ErrRecordNotFound):
		return fmt.Errorf("failed to fetch recurring payment action,client request id: %s err: %w", hashedUmrn, rpActionErr)
	case rpAction != nil:
		logger.Info(ctx, "recurring payment action already exists", zap.String(logger.RECURRING_PAYMENT_ID, rpAction.GetRecurringPaymentId()))
		return nil
	}
	var recurringPaymentId string
	// create recurring payment in transaction block to avoid the race condtions when multiple threads are trying to do the same
	createRpInTxnBlockErr := storageV2.RunCRDBTxn(ctx, func(txnCtx context.Context) error {
		createRecurringPayment, createRecurringPaymentErr := s.rpDao.Create(txnCtx, recurringPayment, commontypes.Ownership_EPIFI_TECH)
		if createRecurringPaymentErr != nil {
			return createRecurringPaymentErr
		}
		_, createActionErr := s.rpActionDao.Create(txnCtx, &rpPb.RecurringPaymentsAction{
			RecurringPaymentId: createRecurringPayment.GetId(),
			ClientRequestId:    hashedUmrn,
			Action:             rpPb.Action_CREATE,
			State:              rpPb.ActionState_ACTION_SUCCESS,
		})
		if createActionErr != nil {
			return createActionErr
		}
		recurringPaymentId = createRecurringPayment.GetId()
		return nil
	})
	if createRpInTxnBlockErr != nil {
		return fmt.Errorf("failed to create the recurring payment %s %w", createRpInTxnBlockErr.Error(), epifierrors.ErrTransient)
	}
	// create enach mandate entry
	enachMandate, enachMandateErr := s.enachClient.CreateOffAppEnachMandate(ctx, &enachPb.CreateOffAppEnachMandateRequest{
		Umrn:               umrn,
		RecurringPaymentId: recurringPaymentId,
		Provenance:         enachEnumsPb.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_EXTERNAL,
		Vendor:             recurringPayment.GetPartnerBank(),
		Ownership:          commontypes.Ownership_EPIFI_TECH,
	})
	if err := epifigrpc.RPCError(enachMandate, enachMandateErr); err != nil {
		return fmt.Errorf("error while creating enach mandate,err: %s, %w", err.Error(), epifierrors.ErrTransient)
	}

	return nil
}

func (s *ConsumerService) fetchSavingsAccount(ctx context.Context, actorId string, partnerBank commonvgpb.Vendor) (*savings.SavingsAccountEssentials, error) {
	savingsAccountEssentials, savingsAccountEssentialsErr := s.savingsClient.GetSavingsAccountEssentials(ctx, &savings.GetSavingsAccountEssentialsRequest{
		Filter: &savings.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savings.ActorIdBankFilter{
				ActorId:     actorId,
				PartnerBank: partnerBank,
			},
		},
	})
	switch {
	case savingsAccountEssentials.GetStatus().IsRecordNotFound():
		return nil, fmt.Errorf("no savings account found %w", epifierrors.ErrPermanent)
	case !savingsAccountEssentials.GetStatus().IsSuccess() || savingsAccountEssentialsErr != nil:
		return nil, fmt.Errorf("failed to fetch essentials %w", epifierrors.ErrTransient)
	default:
		return savingsAccountEssentials.GetAccount(), nil
	}
}

func (s *ConsumerService) resolveInternalActorPi(ctx context.Context, accountNumber, ifsc string) (*piPb.PaymentInstrument, error) {
	fetchedPi, fetchedPiErr := s.piClient.GetPi(ctx, &piPb.GetPiRequest{
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.GetPiRequest_AccountRequestParams_{
			AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
				ActualAccountNumber: accountNumber,
				IfscCode:            ifsc,
			},
		},
	})
	if err := epifigrpc.RPCError(fetchedPi, fetchedPiErr); err != nil {
		return nil, fmt.Errorf("failed to fetch internal pi: %s, %w", err.Error(), epifierrors.ErrTransient)
	}
	return fetchedPi.GetPaymentInstrument(), nil
}

func convertEnachMandateVgResponseToRecurringPayment(fromActorId, toActorId, piFrom, piTo string, vendor commonvgpb.Vendor, enachMandate *vgEnachPb.EnachMandate) *rpPb.RecurringPayment {
	return &rpPb.RecurringPayment{
		FromActorId: fromActorId,
		ToActorId:   toActorId,
		PiFrom:      piFrom,
		PiTo:        piTo,
		Type:        rpPb.RecurringPaymentType_ENACH_MANDATES,
		Interval: &types.Interval{
			StartTime: enachMandate.GetMandateStartTime(),
			// this will be nill
			EndTime: enachMandate.GetMandateExpireTime(),
		},
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_ENACH,
		PartnerBank:              vendor,
		Amount:                   enachMandate.GetMaxAmount(),
		AmountType:               mapping.EnachMandateAmountTypeToRecurringPaymentAmountType[enachMandate.GetAmountType()],
		Provenance:               rpPb.RecurrencePaymentProvenance_EXTERNAL,
		State:                    mandateStateToRecurringPaymentState[enachMandate.GetMandateStatus()],
		RecurrenceRule: &rpPb.RecurrenceRule{
			AllowedFrequency: mapping.EnachMandateFrequencyToRecurringPaymentFrequency[enachMandate.GetMandateFrequency()],
		},
	}
}

func (s *ConsumerService) updateMandateStatus(ctx context.Context, enachMandate *vgEnachPb.EnachMandate) (bool, error) {
	// fetch the recurring payment action
	rpAction, rpActionErr := s.rpActionDao.GetByClientRequestId(ctx, hashUmrn(enachMandate.GetUmrn()), false)
	if rpActionErr != nil {
		return false, fmt.Errorf("failed to fetch rp action,err: %w", rpActionErr)
	}
	// fetch the recurring payment
	rp, rpErr := s.rpDao.GetById(ctx, rpAction.GetRecurringPaymentId())
	if rpErr != nil {
		return false, fmt.Errorf("failed to fetch rp,err: %w", rpErr)
	}
	currentMandateState := mandateStateToRecurringPaymentState[enachMandate.GetMandateStatus()]
	// update to the latest status
	// NOTE: only state can be updated for an off-app enach mandate as of now
	if rp.GetState() != currentMandateState {
		updateErr := s.rpDao.UpdateAndChangeStatus(ctx, rp, []rpPb.RecurringPaymentFieldMask{rpPb.RecurringPaymentFieldMask_STATE}, rp.GetState(), currentMandateState)
		if updateErr != nil {
			return false, fmt.Errorf("failed to update rp state,err: %w", updateErr)
		}
		return true, nil
	}
	return false, nil
}

// NOTE: do not change the hashing logic since will fail the deduping logic
func hashUmrn(umrn string) string {
	return crypto.GetSHA1InBase32(umrn)
}

var (
	mandateStateToRecurringPaymentState = map[vgEnachEnumPb.MandateStatus]rpPb.RecurringPaymentState{
		vgEnachEnumPb.MandateStatus_MANDATE_STATUS_ACTIVE:    rpPb.RecurringPaymentState_ACTIVATED,
		vgEnachEnumPb.MandateStatus_MANDATE_STATUS_EXPIRED:   rpPb.RecurringPaymentState_EXPIRED,
		vgEnachEnumPb.MandateStatus_MANDATE_STATUS_CANCELLED: rpPb.RecurringPaymentState_REVOKED,
	}
)
