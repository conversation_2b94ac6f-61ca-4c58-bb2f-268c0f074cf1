package consumer

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/epifierrors"
	eventMock "github.com/epifi/be-common/pkg/events/mocks"
	mock_queue "github.com/epifi/be-common/pkg/queue/mocks"

	cardPb "github.com/epifi/gamma/api/card"
	dcMandatePb "github.com/epifi/gamma/api/card/debitcardmandate"
	dcMandateMocks "github.com/epifi/gamma/api/card/debitcardmandate/mocks"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	cpPb "github.com/epifi/gamma/api/card/provisioning"
	cardMocks "github.com/epifi/gamma/api/card/provisioning/mocks"
	merchantPb "github.com/epifi/gamma/api/merchant"
	merchantMocksPb "github.com/epifi/gamma/api/merchant/mocks"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	parsedTxnEnums "github.com/epifi/gamma/api/order/payment/notification/enums"
	"github.com/epifi/gamma/api/paymentinstrument"
	mockPi "github.com/epifi/gamma/api/paymentinstrument/mocks"
	recurringpaymentPb "github.com/epifi/gamma/api/recurringpayment"
	consumerPb "github.com/epifi/gamma/api/recurringpayment/consumer"
	enach2 "github.com/epifi/gamma/api/recurringpayment/enach"
	enums2 "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	mocks4 "github.com/epifi/gamma/api/recurringpayment/enach/mocks"
	recurringPaymentMocks "github.com/epifi/gamma/api/recurringpayment/mocks"
	"github.com/epifi/gamma/api/savings"
	savingsMock "github.com/epifi/gamma/api/savings/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/enach/enums"
	mocks2 "github.com/epifi/gamma/api/vendorgateway/openbanking/enach/mocks"
	releaseMock "github.com/epifi/gamma/pkg/feature/release/mocks"
	mocks3 "github.com/epifi/gamma/recurringpayment/dao/mocks"
	"github.com/epifi/gamma/recurringpayment/internal/actor"
	mock_internal "github.com/epifi/gamma/recurringpayment/internal/mocks"
)

func TestConsumerService_ProcessRecurringPaymentCreationAuthorizationVendorCallback(t *testing.T) {
	type args struct {
		ctx context.Context
		req *consumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockRecurringPaymentClient *recurringPaymentMocks.MockRecurringPaymentServiceClient)
		want       *consumerPb.ConsumerResponse
		wantErr    bool
	}{
		{
			name: "should return TransientFailure consume response when rpc call to fetch recurring payment id fails",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest{
					VendorRequestId:      "vendor-request-id-1",
					RecurringPaymentType: recurringpaymentPb.RecurringPaymentType_ENACH_MANDATES,
					RecurringPaymentTypeSpecificPayload: &consumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayload_{
						EnachPayload: &consumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayload{
							Umrn:                       "UMRN-1",
							NpciRefId:                  "npci-ref-id-1",
							DestBankReferenceNumber:    "dest-bank-reference-number-1",
							MerchantReferenceMessageId: "merchant-reference-message-id-1",
						},
					},
				},
			},
			setupMocks: func(mockRecurringPaymentClient *recurringPaymentMocks.MockRecurringPaymentServiceClient) {
				mockRecurringPaymentClient.EXPECT().GetRecurringPaymentIdByVendorRequestId(context.Background(), &recurringpaymentPb.GetRecurringPaymentIdByVendorRequestIdRequest{
					RecurringPaymentType: recurringpaymentPb.RecurringPaymentType_ENACH_MANDATES,
					VendorRequestId:      "vendor-request-id-1",
				}).Return(&recurringpaymentPb.GetRecurringPaymentIdByVendorRequestIdResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: getTransientFailureRes(),
		},
		{
			name: "should return PermanentFailure consume response when recurring payment type is not supported for creation authorization vendor callback",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest{
					VendorRequestId: "vendor-request-id-1",
					// upi mandate recurring payment type is not supported for creation authorization callbacks from vendor
					RecurringPaymentType: recurringpaymentPb.RecurringPaymentType_UPI_MANDATES,
				},
			},
			setupMocks: func(mockRecurringPaymentClient *recurringPaymentMocks.MockRecurringPaymentServiceClient) {
				mockRecurringPaymentClient.EXPECT().GetRecurringPaymentIdByVendorRequestId(context.Background(), &recurringpaymentPb.GetRecurringPaymentIdByVendorRequestIdRequest{
					RecurringPaymentType: recurringpaymentPb.RecurringPaymentType_UPI_MANDATES,
					VendorRequestId:      "vendor-request-id-1",
				}).Return(&recurringpaymentPb.GetRecurringPaymentIdByVendorRequestIdResponse{
					Status:             rpc.StatusOk(),
					RecurringPaymentId: "recurring-payment-id-1",
				}, nil)
			},
			want: getPermanentFailureRes(),
		},
		{
			name: "should return TransientFailure consume response when authorize recurring payment creation rpc call fails",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest{
					VendorRequestId:      "vendor-request-id-1",
					RecurringPaymentType: recurringpaymentPb.RecurringPaymentType_ENACH_MANDATES,
					RecurringPaymentTypeSpecificPayload: &consumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayload_{
						EnachPayload: &consumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayload{
							Umrn:                       "UMRN-1",
							NpciRefId:                  "npci-ref-id-1",
							DestBankReferenceNumber:    "dest-bank-reference-number-1",
							MerchantReferenceMessageId: "merchant-reference-message-id-1",
						},
					},
				},
			},
			setupMocks: func(mockRecurringPaymentClient *recurringPaymentMocks.MockRecurringPaymentServiceClient) {
				mockRecurringPaymentClient.EXPECT().GetRecurringPaymentIdByVendorRequestId(context.Background(), &recurringpaymentPb.GetRecurringPaymentIdByVendorRequestIdRequest{
					RecurringPaymentType: recurringpaymentPb.RecurringPaymentType_ENACH_MANDATES,
					VendorRequestId:      "vendor-request-id-1",
				}).Return(&recurringpaymentPb.GetRecurringPaymentIdByVendorRequestIdResponse{
					Status:             rpc.StatusOk(),
					RecurringPaymentId: "recurring-payment-id-1",
				}, nil)

				mockRecurringPaymentClient.EXPECT().AuthorizeCreationV1(context.Background(), &recurringpaymentPb.AuthorizeCreationV1Request{
					RecurringPaymentId: "recurring-payment-id-1",
					AuthMetadata: &recurringpaymentPb.RecurringPaymentCreationAuthMetadata{
						RecurringPaymentTypeSpecificMetadata: &recurringpaymentPb.RecurringPaymentCreationAuthMetadata_EnachMetadata{
							EnachMetadata: &recurringpaymentPb.RecurringPaymentCreationAuthMetadata_ENachAuthMetadata{
								Umrn:                       "UMRN-1",
								NpciRefId:                  "npci-ref-id-1",
								DestBankReferenceNumber:    "dest-bank-reference-number-1",
								MerchantReferenceMessageId: "merchant-reference-message-id-1",
							},
						},
					},
				}).Return(&recurringpaymentPb.AuthorizeCreationV1Response{Status: rpc.StatusInternal()}, nil)
			},
			want: getTransientFailureRes(),
		},
		{
			name: "should return Success consume response",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest{
					VendorRequestId:      "vendor-request-id-1",
					RecurringPaymentType: recurringpaymentPb.RecurringPaymentType_ENACH_MANDATES,
					RecurringPaymentTypeSpecificPayload: &consumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayload_{
						EnachPayload: &consumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayload{
							Umrn:                       "UMRN-1",
							NpciRefId:                  "npci-ref-id-1",
							DestBankReferenceNumber:    "dest-bank-reference-number-1",
							MerchantReferenceMessageId: "merchant-reference-message-id-1",
						},
					},
				},
			},
			setupMocks: func(mockRecurringPaymentClient *recurringPaymentMocks.MockRecurringPaymentServiceClient) {
				mockRecurringPaymentClient.EXPECT().GetRecurringPaymentIdByVendorRequestId(context.Background(), &recurringpaymentPb.GetRecurringPaymentIdByVendorRequestIdRequest{
					RecurringPaymentType: recurringpaymentPb.RecurringPaymentType_ENACH_MANDATES,
					VendorRequestId:      "vendor-request-id-1",
				}).Return(&recurringpaymentPb.GetRecurringPaymentIdByVendorRequestIdResponse{
					Status:             rpc.StatusOk(),
					RecurringPaymentId: "recurring-payment-id-1",
				}, nil)

				mockRecurringPaymentClient.EXPECT().AuthorizeCreationV1(context.Background(), &recurringpaymentPb.AuthorizeCreationV1Request{
					RecurringPaymentId: "recurring-payment-id-1",
					AuthMetadata: &recurringpaymentPb.RecurringPaymentCreationAuthMetadata{
						RecurringPaymentTypeSpecificMetadata: &recurringpaymentPb.RecurringPaymentCreationAuthMetadata_EnachMetadata{
							EnachMetadata: &recurringpaymentPb.RecurringPaymentCreationAuthMetadata_ENachAuthMetadata{
								Umrn:                       "UMRN-1",
								NpciRefId:                  "npci-ref-id-1",
								DestBankReferenceNumber:    "dest-bank-reference-number-1",
								MerchantReferenceMessageId: "merchant-reference-message-id-1",
							},
						},
					},
				}).Return(&recurringpaymentPb.AuthorizeCreationV1Response{Status: rpc.StatusOk()}, nil)
			},
			want: getSuccessRes(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockRecurringPaymentSvcClient := recurringPaymentMocks.NewMockRecurringPaymentServiceClient(ctr)

			tt.setupMocks(mockRecurringPaymentSvcClient)

			c := &ConsumerService{
				recurringPaymentClient: mockRecurringPaymentSvcClient,
			}
			got, err := c.ProcessRecurringPaymentCreationAuthorizationVendorCallback(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessRecurringPaymentCreationAuthorizationVendorCallback() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("ProcessRecurringPaymentCreationAuthorizationVendorCallback() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestConsumerService_ProcessOffAppRecurringPaymentExecution(t *testing.T) {
	defaultInternalActorId := "default-internal-actor-id"
	defaultAccountNumber := "default-account-number"
	defaultIfsc := "default-ifsc"
	defaultUmrn := "default-umrn"
	defaultOrgName := "Groww"
	defaultInternalActorPiId := "default-internal-actor-pi-id"
	defaultOtherActorId := "default-other-actor-id"
	defaultOtherActorPiId := "default-other-actor-pi-id"
	defaultOtherActorPi := &paymentinstrument.PaymentInstrument{
		Id:           "default-other-actor-pi-id",
		VerifiedName: "Groww",
	}
	defaultClientReqId := crypto.GetSHA1InBase32(defaultUmrn)
	defaultRecurringPaymentId := "defaul-recurring-payment-id"
	defaultGetSavingsAccountsEssentialReq := &savings.GetSavingsAccountEssentialsRequest{
		Filter: &savings.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savings.ActorIdBankFilter{
				ActorId:     defaultInternalActorId,
				PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	}
	defaultGetSavingsAccountsEssentialRes := &savings.GetSavingsAccountEssentialsResponse{
		Status: rpc.StatusOk(),
		Account: &savings.SavingsAccountEssentials{
			AccountNo: defaultAccountNumber,
			IfscCode:  defaultIfsc,
		},
	}
	defaultListMandatesReq := &enach.ListEnachMandateRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		AccountNumber: defaultAccountNumber,
	}
	defaultListMandatesRes := &enach.ListEnachMandateResponse{
		Status: rpc.StatusOk(),
		EnachMandates: []*enach.EnachMandate{
			{
				Umrn:             defaultUmrn,
				OrgName:          defaultOrgName,
				MandateFrequency: enums.Frequency_FREQUENCY_MONTHLY,
				MandateStatus:    enums.MandateStatus_MANDATE_STATUS_ACTIVE,
			},
		},
	}
	defaultGetPiReq := &paymentinstrument.GetPiRequest{
		Type: paymentinstrument.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &paymentinstrument.GetPiRequest_AccountRequestParams_{
			AccountRequestParams: &paymentinstrument.GetPiRequest_AccountRequestParams{
				ActualAccountNumber: defaultAccountNumber,
				IfscCode:            defaultIfsc,
			},
		},
	}
	defaultGetPiRes := &paymentinstrument.GetPiResponse{
		Status: rpc.StatusOk(),
		PaymentInstrument: &paymentinstrument.PaymentInstrument{
			Id: defaultInternalActorPiId,
		},
	}
	defaultResolveOtherActorAndPiForOffAppRecurringPaymentReq := &actor.ResolveOtherActorAndPiForOffAppRecurringPaymentReq{
		InternalActorId: defaultInternalActorId,
		EnachPayload: &actor.EnachPayloadToResolveActorAndPi{
			OrgName: defaultOrgName,
		},
	}
	defaultRecurringPaymentReq := &recurringpaymentPb.RecurringPayment{
		FromActorId: defaultInternalActorId,
		ToActorId:   defaultOtherActorId,
		PiFrom:      defaultInternalActorPiId,
		PiTo:        defaultOtherActorPiId,
		Type:        recurringpaymentPb.RecurringPaymentType_ENACH_MANDATES,
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		RecurrenceRule: &recurringpaymentPb.RecurrenceRule{
			AllowedFrequency: recurringpaymentPb.AllowedFrequency_MONTHLY,
		},
		Provenance:               recurringpaymentPb.RecurrencePaymentProvenance_EXTERNAL,
		Interval:                 &types.Interval{},
		State:                    recurringpaymentPb.RecurringPaymentState_ACTIVATED,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_ENACH,
	}
	defaultRecurringPaymentRes := &recurringpaymentPb.RecurringPayment{
		Id:          defaultRecurringPaymentId,
		FromActorId: defaultInternalActorId,
		ToActorId:   defaultOtherActorId,
		PiFrom:      defaultInternalActorPiId,
		PiTo:        defaultOtherActorPiId,
		Type:        recurringpaymentPb.RecurringPaymentType_ENACH_MANDATES,
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		RecurrenceRule: &recurringpaymentPb.RecurrenceRule{
			AllowedFrequency: recurringpaymentPb.AllowedFrequency_MONTHLY,
		},
		Provenance:               recurringpaymentPb.RecurrencePaymentProvenance_EXTERNAL,
		Interval:                 &types.Interval{},
		State:                    recurringpaymentPb.RecurringPaymentState_ACTIVATED,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_ENACH,
	}
	defaultRecurringPaymentActionReq := &recurringpaymentPb.RecurringPaymentsAction{
		RecurringPaymentId: defaultRecurringPaymentId,
		ClientRequestId:    defaultClientReqId,
		Action:             recurringpaymentPb.Action_CREATE,
		State:              recurringpaymentPb.ActionState_ACTION_SUCCESS,
	}
	defaultCreateOffAppEnachReq := &enach2.CreateOffAppEnachMandateRequest{
		Umrn:               defaultUmrn,
		RecurringPaymentId: defaultRecurringPaymentId,
		Provenance:         enums2.EnachRegistrationProvenance_REGISTRATION_PROVENANCE_EXTERNAL,
		Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
	}
	type args struct {
		ctx context.Context
		req *orderPb.OrderUpdate
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockSavingsClient *savingsMock.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockDcMandateClient *dcMandateMocks.MockDebitCardMandateServiceClient, mockMerchantClient *merchantMocksPb.MockMerchantServiceClient, mockCardClient *cardMocks.MockCardProvisioningClient, mockFailedEnachTxnPublisher *mock_queue.MockPublisher, releaseEvaluator *releaseMock.MockIEvaluator)
		want       *consumerPb.ConsumerResponse
		wantErr    bool
	}{
		{
			name: "should return successfully after linking enach txn with recurring payment",
			args: args{
				ctx: context.Background(),
				req: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-1",
							FromActorId: "default-internal-actor-id",
							ClientReqId: "client-1",
							Status:      orderPb.OrderStatus_PAID,
						},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-1",
								PaymentProtocol: paymentPb.PaymentProtocol_ENACH,
								RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
									"DEBIT": {
										Particulars: "ACDH/XXXX/default-umrn/OOOO",
									},
								},
								Metadata: &paymentPb.Metadata{
									ParserTemplate: &paymentPb.ParserTemplate{
										Id:      parsedTxnEnums.ParserTemplateId_PARSER_TEMPLATE_ID_OFF_APP_ENACH,
										Version: "v1.0",
									},
									FetchedRemitterDetails: nil,
								},
								PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
							},
						},
					},
				},
			},
			setupMocks: func(mockSavingsClient *savingsMock.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockDcMandateClient *dcMandateMocks.MockDebitCardMandateServiceClient, mockMerchantClient *merchantMocksPb.MockMerchantServiceClient, mockCardClient *cardMocks.MockCardProvisioningClient, mockFailedEnachTxnPublisher *mock_queue.MockPublisher, releaseEvaluator *releaseMock.MockIEvaluator) {
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), hashUmrn(defaultUmrn), false).Return(defaultRecurringPaymentActionReq, nil)
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), "client-1", false).Return(nil, gorm.ErrRecordNotFound)
				mockActionDao.EXPECT().Create(context.Background(), &recurringpaymentPb.RecurringPaymentsAction{
					RecurringPaymentId: defaultRecurringPaymentId,
					ClientRequestId:    "client-1",
					Action:             recurringpaymentPb.Action_EXECUTE,
					State:              recurringpaymentPb.ActionState_ACTION_SUCCESS,
				}).Return(&recurringpaymentPb.RecurringPaymentsAction{}, nil)
			},
			want: &consumerPb.ConsumerResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_SUCCESS},
			},
			wantErr: false,
		},
		{
			name: "should return successfully if RP action already exists for the client request id",
			args: args{
				ctx: context.Background(),
				req: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-1",
							FromActorId: "default-internal-actor-id",
							ClientReqId: "client-1",
							Status:      orderPb.OrderStatus_PAID,
						},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-1",
								PaymentProtocol: paymentPb.PaymentProtocol_ENACH,
								RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
									"DEBIT": {
										Particulars: "ACDH/XXXX/default-umrn/OOOO",
									},
								},
								Metadata: &paymentPb.Metadata{
									ParserTemplate: &paymentPb.ParserTemplate{
										Id:      parsedTxnEnums.ParserTemplateId_PARSER_TEMPLATE_ID_OFF_APP_ENACH,
										Version: "v1.0",
									},
									FetchedRemitterDetails: nil,
								},
								PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
							},
						},
					},
				},
			},
			setupMocks: func(mockSavingsClient *savingsMock.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockDcMandateClient *dcMandateMocks.MockDebitCardMandateServiceClient, mockMerchantClient *merchantMocksPb.MockMerchantServiceClient, mockCardClient *cardMocks.MockCardProvisioningClient, mockFailedEnachTxnPublisher *mock_queue.MockPublisher, releaseEvaluator *releaseMock.MockIEvaluator) {
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), hashUmrn(defaultUmrn), false).Return(defaultRecurringPaymentActionReq, nil)
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), "client-1", false).Return(&recurringpaymentPb.RecurringPaymentsAction{}, nil)
			},
			want: &consumerPb.ConsumerResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_SUCCESS},
			},
			wantErr: false,
		},
		{
			name: "should throw transient failure if rp action fetch fails(not RNF)",
			args: args{
				ctx: context.Background(),
				req: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-1",
							FromActorId: "default-internal-actor-id",
							ClientReqId: "client-1",
							Status:      orderPb.OrderStatus_PAID,
						},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-1",
								PaymentProtocol: paymentPb.PaymentProtocol_ENACH,
								RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
									"DEBIT": {
										Particulars: "ACDH/XXXX/default-umrn/OOOO",
									},
								},
								Metadata: &paymentPb.Metadata{
									ParserTemplate: &paymentPb.ParserTemplate{
										Id:      parsedTxnEnums.ParserTemplateId_PARSER_TEMPLATE_ID_OFF_APP_ENACH,
										Version: "v1.0",
									},
									FetchedRemitterDetails: nil,
								},
								PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
							},
						},
					},
				},
			},
			setupMocks: func(mockSavingsClient *savingsMock.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockDcMandateClient *dcMandateMocks.MockDebitCardMandateServiceClient, mockMerchantClient *merchantMocksPb.MockMerchantServiceClient, mockCardClient *cardMocks.MockCardProvisioningClient, mockFailedEnachTxnPublisher *mock_queue.MockPublisher, releaseEvaluator *releaseMock.MockIEvaluator) {
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), hashUmrn(defaultUmrn), false).Return(defaultRecurringPaymentActionReq, nil)
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), "client-1", false).Return(nil, errors.New("error"))
			},
			want: &consumerPb.ConsumerResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE},
			},
			wantErr: false,
		},
		{
			name: "should throw transient failure if rp action create fails",
			args: args{
				ctx: context.Background(),
				req: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-1",
							FromActorId: "default-internal-actor-id",
							ClientReqId: "client-1",
							Status:      orderPb.OrderStatus_PAID,
						},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-1",
								PaymentProtocol: paymentPb.PaymentProtocol_ENACH,
								RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
									"DEBIT": {
										Particulars: "ACDH/XXXX/default-umrn/OOOO",
									},
								},
								Metadata: &paymentPb.Metadata{
									ParserTemplate: &paymentPb.ParserTemplate{
										Id:      parsedTxnEnums.ParserTemplateId_PARSER_TEMPLATE_ID_OFF_APP_ENACH,
										Version: "v1.0",
									},
									FetchedRemitterDetails: nil,
								},
								PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
							},
						},
					},
				},
			},
			setupMocks: func(mockSavingsClient *savingsMock.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockDcMandateClient *dcMandateMocks.MockDebitCardMandateServiceClient, mockMerchantClient *merchantMocksPb.MockMerchantServiceClient, mockCardClient *cardMocks.MockCardProvisioningClient, mockFailedEnachTxnPublisher *mock_queue.MockPublisher, releaseEvaluator *releaseMock.MockIEvaluator) {
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), hashUmrn(defaultUmrn), false).Return(defaultRecurringPaymentActionReq, nil)
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), "client-1", false).Return(nil, gorm.ErrRecordNotFound)
				mockActionDao.EXPECT().Create(context.Background(), &recurringpaymentPb.RecurringPaymentsAction{
					RecurringPaymentId: defaultRecurringPaymentId,
					ClientRequestId:    "client-1",
					Action:             recurringpaymentPb.Action_EXECUTE,
					State:              recurringpaymentPb.ActionState_ACTION_SUCCESS,
				}).Return(nil, errors.New("error"))
			},
			want: &consumerPb.ConsumerResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE},
			},
			wantErr: false,
		},
		{
			name: "should fetch enach from vendor and link txn successfully",
			args: args{
				ctx: context.Background(),
				req: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-1",
							FromActorId: "default-internal-actor-id",
							ClientReqId: "client-1",
							Status:      orderPb.OrderStatus_PAID,
						},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-1",
								PaymentProtocol: paymentPb.PaymentProtocol_ENACH,
								RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
									"DEBIT": {
										Particulars: "ACDH/XXXX/default-umrn/OOOO",
									},
								},
								Metadata: &paymentPb.Metadata{
									ParserTemplate: &paymentPb.ParserTemplate{
										Id:      parsedTxnEnums.ParserTemplateId_PARSER_TEMPLATE_ID_OFF_APP_ENACH,
										Version: "v1.0",
									},
									FetchedRemitterDetails: nil,
								},
								PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
							},
						},
					},
				},
			},
			setupMocks: func(mockSavingsClient *savingsMock.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockDcMandateClient *dcMandateMocks.MockDebitCardMandateServiceClient, mockMerchantClient *merchantMocksPb.MockMerchantServiceClient, mockCardClient *cardMocks.MockCardProvisioningClient, mockFailedEnachTxnPublisher *mock_queue.MockPublisher, releaseEvaluator *releaseMock.MockIEvaluator) {
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), hashUmrn(defaultUmrn), false).Return(nil, gorm.ErrRecordNotFound)
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(defaultGetSavingsAccountsEssentialRes, nil)
				mockEnachVgClient.EXPECT().ListEnachMandate(context.Background(), defaultListMandatesReq).Return(defaultListMandatesRes, nil)
				mockPiClient.EXPECT().GetPi(context.Background(), defaultGetPiReq).Return(defaultGetPiRes, nil)
				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: defaultUmrn},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpaymentPb.RecurringPaymentType_ENACH_MANDATES, defaultResolveOtherActorAndPiForOffAppRecurringPaymentReq).
					Return(defaultOtherActorPi, defaultOtherActorId, nil)
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientReqId, false).Return(nil, epifierrors.ErrRecordNotFound)
				mockDao.EXPECT().Create(gomock.Any(), defaultRecurringPaymentReq, commontypes.Ownership_EPIFI_TECH).Return(defaultRecurringPaymentRes, nil)
				mockActionDao.EXPECT().Create(gomock.Any(), defaultRecurringPaymentActionReq).Return(nil, nil)
				mockEnachClient.EXPECT().CreateOffAppEnachMandate(context.Background(), defaultCreateOffAppEnachReq).Return(&enach2.CreateOffAppEnachMandateResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), hashUmrn(defaultUmrn), false).Return(defaultRecurringPaymentActionReq, nil)
				mockActionDao.EXPECT().GetByClientRequestId(context.Background(), "client-1", false).Return(nil, gorm.ErrRecordNotFound)
				mockActionDao.EXPECT().Create(context.Background(), &recurringpaymentPb.RecurringPaymentsAction{
					RecurringPaymentId: defaultRecurringPaymentId,
					ClientRequestId:    "client-1",
					Action:             recurringpaymentPb.Action_EXECUTE,
					State:              recurringpaymentPb.ActionState_ACTION_SUCCESS,
				}).Return(nil, errors.New("error"))
				releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
				mockFailedEnachTxnPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", nil)
			},
			want: &consumerPb.ConsumerResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE},
			},
			wantErr: false,
		},
		{
			name: "should return permanent failure if umrn is empty",
			args: args{
				ctx: context.Background(),
				req: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-1",
							FromActorId: "default-internal-actor-id",
							ClientReqId: "client-1",
							Status:      orderPb.OrderStatus_PAID,
						},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-1",
								PaymentProtocol: paymentPb.PaymentProtocol_ENACH,
								RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
									"DEBIT": {
										Particulars: "ACDH/XXXX//OOOO",
									},
								},
								Metadata: &paymentPb.Metadata{
									ParserTemplate: &paymentPb.ParserTemplate{
										Id:      parsedTxnEnums.ParserTemplateId_PARSER_TEMPLATE_ID_OFF_APP_ENACH,
										Version: "v1.0",
									},
									FetchedRemitterDetails: nil,
								},
								PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
							},
						},
					},
				},
			},
			setupMocks: func(mockSavingsClient *savingsMock.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockDcMandateClient *dcMandateMocks.MockDebitCardMandateServiceClient, mockMerchantClient *merchantMocksPb.MockMerchantServiceClient, mockCardClient *cardMocks.MockCardProvisioningClient, mockFailedEnachTxnPublisher *mock_queue.MockPublisher, releaseEvaluator *releaseMock.MockIEvaluator) {
			},
			want: &consumerPb.ConsumerResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE},
			},
			wantErr: false,
		},
		{
			name: "should return permanent failure if client req id is empty",
			args: args{
				ctx: context.Background(),
				req: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-1",
							FromActorId: "default-internal-actor-id",
							Status:      orderPb.OrderStatus_PAID,
						},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-1",
								PaymentProtocol: paymentPb.PaymentProtocol_ENACH,
								RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
									"DEBIT": {
										Particulars: "ACDH/XXXX//OOOO",
									},
								},
								Metadata: &paymentPb.Metadata{
									ParserTemplate: &paymentPb.ParserTemplate{
										Id:      parsedTxnEnums.ParserTemplateId_PARSER_TEMPLATE_ID_OFF_APP_ENACH,
										Version: "v1.0",
									},
									FetchedRemitterDetails: nil,
								},
								PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
							},
						},
					},
				},
			},
			setupMocks: func(mockSavingsClient *savingsMock.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockDcMandateClient *dcMandateMocks.MockDebitCardMandateServiceClient, mockMerchantClient *merchantMocksPb.MockMerchantServiceClient, mockCardClient *cardMocks.MockCardProvisioningClient, mockFailedEnachTxnPublisher *mock_queue.MockPublisher, releaseEvaluator *releaseMock.MockIEvaluator) {
			},
			want: &consumerPb.ConsumerResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE},
			},
			wantErr: false,
		},
		{
			name: "should return permanent failure if rp action for creation not found after fetching enach from vendor",
			args: args{
				ctx: context.Background(),
				req: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-1",
							FromActorId: "default-internal-actor-id",
							ClientReqId: "client-1",
							Status:      orderPb.OrderStatus_PAID,
						},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-1",
								PaymentProtocol: paymentPb.PaymentProtocol_ENACH,
								RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
									"DEBIT": {
										Particulars: "ACDH/XXXX/default-umrn/OOOO",
									},
								},
								Metadata: &paymentPb.Metadata{
									ParserTemplate: &paymentPb.ParserTemplate{
										Id:      parsedTxnEnums.ParserTemplateId_PARSER_TEMPLATE_ID_OFF_APP_ENACH,
										Version: "v1.0",
									},
									FetchedRemitterDetails: nil,
								},
								PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
							},
						},
					},
				},
			},
			setupMocks: func(mockSavingsClient *savingsMock.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockDcMandateClient *dcMandateMocks.MockDebitCardMandateServiceClient, mockMerchantClient *merchantMocksPb.MockMerchantServiceClient, mockCardClient *cardMocks.MockCardProvisioningClient, mockFailedEnachTxnPublisher *mock_queue.MockPublisher, releaseEvaluator *releaseMock.MockIEvaluator) {
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), hashUmrn(defaultUmrn), false).Return(nil, gorm.ErrRecordNotFound)
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(context.Background(), defaultGetSavingsAccountsEssentialReq).Return(defaultGetSavingsAccountsEssentialRes, nil)
				mockEnachVgClient.EXPECT().ListEnachMandate(context.Background(), defaultListMandatesReq).Return(defaultListMandatesRes, nil)
				mockPiClient.EXPECT().GetPi(context.Background(), defaultGetPiReq).Return(defaultGetPiRes, nil)
				mockEnachClient.EXPECT().GetEnachMandate(context.Background(), &enach2.GetEnachMandateRequest{
					Identifier: &enach2.GetEnachMandateRequest_Umrn{Umrn: defaultUmrn},
				}).Return(&enach2.GetEnachMandateResponse{Status: rpc.StatusRecordNotFound()}, nil)
				mockActorProcessor.EXPECT().ResolveOtherActorAndPiForOffAppRecurringPayment(context.Background(), recurringpaymentPb.RecurringPaymentType_ENACH_MANDATES, defaultResolveOtherActorAndPiForOffAppRecurringPaymentReq).
					Return(defaultOtherActorPi, defaultOtherActorId, nil)
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientReqId, false).Return(nil, epifierrors.ErrRecordNotFound)
				mockDao.EXPECT().Create(gomock.Any(), defaultRecurringPaymentReq, commontypes.Ownership_EPIFI_TECH).Return(defaultRecurringPaymentRes, nil)
				mockActionDao.EXPECT().Create(gomock.Any(), defaultRecurringPaymentActionReq).Return(nil, nil)
				mockEnachClient.EXPECT().CreateOffAppEnachMandate(context.Background(), defaultCreateOffAppEnachReq).Return(&enach2.CreateOffAppEnachMandateResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), hashUmrn(defaultUmrn), false).Return(nil, gorm.ErrRecordNotFound)
				releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
				mockFailedEnachTxnPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", nil)
			},
			want: &consumerPb.ConsumerResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE},
			},
			wantErr: false,
		},
		{
			name: "should return success when recurring payment action already exists for the client request id for dc mandate txn",
			args: args{
				ctx: context.Background(),
				req: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-1",
							FromActorId: "default-internal-actor-id",
							ClientReqId: "client-1",
							Tags:        []orderPb.OrderTag{orderPb.OrderTag_DC_MANDATE_REGISTER},
						},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-1",
								PiFrom:          "pi-from-1",
								PiTo:            "pi-to-1",
								PaymentProtocol: paymentPb.PaymentProtocol_CARD,
								RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
									"DEBIT": {
										Particulars: "particulars/default-rrn",
									},
								},
								Metadata: &paymentPb.Metadata{
									ParserTemplate: &paymentPb.ParserTemplate{
										Id:      parsedTxnEnums.ParserTemplateId_PARSER_TEMPLATE_ID_GENERIC,
										Version: "v1.0",
									},
									FetchedRemitterDetails: nil,
								},
								PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
							},
						},
					},
				},
			},
			setupMocks: func(mockSavingsClient *savingsMock.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockDcMandateClient *dcMandateMocks.MockDebitCardMandateServiceClient, mockMerchantClient *merchantMocksPb.MockMerchantServiceClient, mockCardClient *cardMocks.MockCardProvisioningClient, mockFailedEnachTxnPublisher *mock_queue.MockPublisher, releaseEvaluator *releaseMock.MockIEvaluator) {
				mockCardClient.EXPECT().GetCardSwitchNotification(gomock.Any(), &cpPb.GetCardSwitchNotificationRequest{
					Identifier: &cpPb.GetCardSwitchNotificationRequest_ExternalRefId{ExternalRefId: "client-1"},
				}).Return(&cpPb.GetCardSwitchNotificationResponse{
					Status: rpc.StatusOk(),
					CardNotification: &cardPb.CardNotification{
						Id:                       "card-notification-1",
						CardId:                   "card-1",
						RetrievalReferenceNumber: "rrn-1",
						Status:                   cardEnumsPb.TransactionState_TRANSACTION_STATE_SUCCESS,
						ExternalRefId:            "client-1",
					},
				}, nil)
				mockMerchantClient.EXPECT().GetMerchant(gomock.Any(), &merchantPb.GetMerchantRequest{
					Identifier: &merchantPb.GetMerchantRequest_PiId{PiId: "pi-to-1"},
				}).Return(&merchantPb.GetMerchantResponse{
					Status:   rpc.StatusOk(),
					Merchant: &merchantPb.Merchant{Id: "merchant-id-1"},
				}, nil)
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-1", false).Return(&recurringpaymentPb.RecurringPaymentsAction{
					RecurringPaymentId: "recurring-payment-id-1",
					ClientRequestId:    "client-1",
					Action:             recurringpaymentPb.Action_CREATE,
					State:              recurringpaymentPb.ActionState_ACTION_SUCCESS,
					ActionMetadata: &recurringpaymentPb.ActionMetadata{
						ExecuteActionMetadate: &recurringpaymentPb.ExecuteActionMetaData{
							Amount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        10,
							},
						},
					},
				}, nil)
				mockDcMandateClient.EXPECT().CreateOffAppDebitCardMandate(gomock.Any(), &dcMandatePb.CreateOffAppDebitCardMandateRequest{
					CardId:             "card-1",
					MerchantId:         "merchant-id-1",
					RecurringPaymentId: "recurring-payment-id-1",
					Provenance:         cardEnumsPb.DebitCardMandateRegistrationProvenance_DEBIT_CARD_MANDATE_REGISTRATION_PROVENANCE_EXTERNAL,
				}).Return(&dcMandatePb.CreateOffAppDebitCardMandateResponse{
					Status: rpc.StatusOk(),
					DebitCardMandate: &dcMandatePb.DebitCardMandate{
						Id: "dc-mandate-id-1",
					},
				}, nil)
			},
			want: &consumerPb.ConsumerResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_SUCCESS},
			},
			wantErr: false,
		},
		{
			name: "should return success after linking dc mandate registration txn with recurring payment",
			args: args{
				ctx: context.Background(),
				req: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-1",
							FromActorId: "default-internal-actor-id",
							ToActorId:   "default-other-actor-id",
							ClientReqId: "client-1",
							Tags:        []orderPb.OrderTag{orderPb.OrderTag_DC_MANDATE_REGISTER},
							Amount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        10,
							},
						},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-1",
								PiFrom:          "pi-from-1",
								PiTo:            "pi-to-1",
								PaymentProtocol: paymentPb.PaymentProtocol_CARD,
								RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
									"DEBIT": {
										Particulars: "particulars/default-rrn",
									},
								},
								Metadata: &paymentPb.Metadata{
									ParserTemplate: &paymentPb.ParserTemplate{
										Id:      parsedTxnEnums.ParserTemplateId_PARSER_TEMPLATE_ID_GENERIC,
										Version: "v1.0",
									},
									FetchedRemitterDetails: nil,
								},
								PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
								Remarks:     "remark-1",
							},
						},
					},
				},
			},
			setupMocks: func(mockSavingsClient *savingsMock.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockDcMandateClient *dcMandateMocks.MockDebitCardMandateServiceClient, mockMerchantClient *merchantMocksPb.MockMerchantServiceClient, mockCardClient *cardMocks.MockCardProvisioningClient, mockFailedEnachTxnPublisher *mock_queue.MockPublisher, releaseEvaluator *releaseMock.MockIEvaluator) {
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-1", false).Return(nil, gorm.ErrRecordNotFound)
				mockCardClient.EXPECT().GetCardSwitchNotification(gomock.Any(), &cpPb.GetCardSwitchNotificationRequest{
					Identifier: &cpPb.GetCardSwitchNotificationRequest_ExternalRefId{ExternalRefId: "client-1"},
				}).Return(&cpPb.GetCardSwitchNotificationResponse{
					Status: rpc.StatusOk(),
					CardNotification: &cardPb.CardNotification{
						Id:                       "card-notification-1",
						CardId:                   "card-1",
						RetrievalReferenceNumber: "rrn-1",
						Status:                   cardEnumsPb.TransactionState_TRANSACTION_STATE_SUCCESS,
						ExternalRefId:            "client-1",
					},
				}, nil)
				mockMerchantClient.EXPECT().GetMerchant(gomock.Any(), &merchantPb.GetMerchantRequest{
					Identifier: &merchantPb.GetMerchantRequest_PiId{PiId: "pi-to-1"},
				}).Return(&merchantPb.GetMerchantResponse{
					Status:   rpc.StatusOk(),
					Merchant: &merchantPb.Merchant{Id: "merchant-id-1"},
				}, nil)
				mockDcMandateClient.EXPECT().GetDebitCardMandate(gomock.Any(), &dcMandatePb.GetDebitCardMandateRequest{
					Identifier: &dcMandatePb.GetDebitCardMandateRequest_DedupeId{
						DedupeId: &dcMandatePb.DebitCardMandateDedupeId{
							CardId:     "card-1",
							MerchantId: "merchant-id-1",
						},
					},
				}).Return(&dcMandatePb.GetDebitCardMandateResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockDao.EXPECT().Create(gomock.Any(), &recurringpaymentPb.RecurringPayment{
					FromActorId: "default-internal-actor-id",
					ToActorId:   "default-other-actor-id",
					Type:        recurringpaymentPb.RecurringPaymentType_DEBIT_CARD_MANDATES,
					PiFrom:      "pi-from-1",
					PiTo:        "pi-to-1",
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        10,
					},
					RecurrenceRule:           &recurringpaymentPb.RecurrenceRule{},
					PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
					PreferredPaymentProtocol: paymentPb.PaymentProtocol_CARD,
					State:                    recurringpaymentPb.RecurringPaymentState_ACTIVATED,
					Provenance:               recurringpaymentPb.RecurrencePaymentProvenance_EXTERNAL,
					AmountType:               0,
				}, commontypes.Ownership_EPIFI_TECH).Return(&recurringpaymentPb.RecurringPayment{Id: "recurring-payment-id-1"}, nil)
				mockActionDao.EXPECT().Create(gomock.Any(), &recurringpaymentPb.RecurringPaymentsAction{
					RecurringPaymentId: "recurring-payment-id-1",
					ClientRequestId:    "client-1",
					Action:             recurringpaymentPb.Action_EXECUTE,
					ActionMetadata: &recurringpaymentPb.ActionMetadata{
						ExecuteActionMetadate: &recurringpaymentPb.ExecuteActionMetaData{
							Amount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        10,
							},
							RecurringPaymentTypeSpecificExecutionInfo: &recurringpaymentPb.RecurringPaymentTypeSpecificExecutionInfo{
								Data: &recurringpaymentPb.RecurringPaymentTypeSpecificExecutionInfo_DcMandateExecutionInfo_{
									DcMandateExecutionInfo: &recurringpaymentPb.RecurringPaymentTypeSpecificExecutionInfo_DcMandateExecutionInfo{
										IsRegistrationExecution: true,
									},
								},
							},
						},
					},
					State: recurringpaymentPb.ActionState_ACTION_SUCCESS,
				}).Return(&recurringpaymentPb.RecurringPaymentsAction{}, nil)
				mockDcMandateClient.EXPECT().CreateOffAppDebitCardMandate(gomock.Any(), &dcMandatePb.CreateOffAppDebitCardMandateRequest{
					CardId:             "card-1",
					MerchantId:         "merchant-id-1",
					RecurringPaymentId: "recurring-payment-id-1",
					Provenance:         cardEnumsPb.DebitCardMandateRegistrationProvenance_DEBIT_CARD_MANDATE_REGISTRATION_PROVENANCE_EXTERNAL,
				}).Return(&dcMandatePb.CreateOffAppDebitCardMandateResponse{
					Status: rpc.StatusOk(),
					DebitCardMandate: &dcMandatePb.DebitCardMandate{
						Id: "dc-mandate-id-1",
					},
				}, nil)
			},
			want: &consumerPb.ConsumerResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_SUCCESS},
			},
			wantErr: false,
		},
		{
			name: "should return success after creating rp action for already existing dc mandate",
			args: args{
				ctx: context.Background(),
				req: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-1",
							FromActorId: "default-internal-actor-id",
							ToActorId:   "default-other-actor-id",
							ClientReqId: "client-1",
							Tags:        []orderPb.OrderTag{orderPb.OrderTag_DC_MANDATE_PAYMENT},
							Amount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        10,
							},
						},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-1",
								PiFrom:          "pi-from-1",
								PiTo:            "pi-to-1",
								PaymentProtocol: paymentPb.PaymentProtocol_CARD,
								RawNotificationDetails: map[string]*paymentPb.NotificationDetails{
									"DEBIT": {
										Particulars: "particulars/default-rrn",
									},
								},
								Metadata: &paymentPb.Metadata{
									ParserTemplate: &paymentPb.ParserTemplate{
										Id:      parsedTxnEnums.ParserTemplateId_PARSER_TEMPLATE_ID_GENERIC,
										Version: "v1.0",
									},
									FetchedRemitterDetails: nil,
								},
								PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
								Remarks:     "remark-1",
							},
						},
					},
				},
			},
			setupMocks: func(mockSavingsClient *savingsMock.MockSavingsClient, mockEnachVgClient *mocks2.MockEnachClient, mockPiClient *mockPi.MockPiClient, mockActorProcessor *mock_internal.MockActorProcessor, mockActionDao *mocks3.MockRecurringPaymentsActionDao, mockDao *mocks3.MockRecurringPaymentDao, mockEnachClient *mocks4.MockEnachServiceClient, mockDcMandateClient *dcMandateMocks.MockDebitCardMandateServiceClient, mockMerchantClient *merchantMocksPb.MockMerchantServiceClient, mockCardClient *cardMocks.MockCardProvisioningClient, mockFailedEnachTxnPublisher *mock_queue.MockPublisher, releaseEvaluator *releaseMock.MockIEvaluator) {
				mockActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-1", false).Return(nil, gorm.ErrRecordNotFound)
				mockCardClient.EXPECT().GetCardSwitchNotification(gomock.Any(), &cpPb.GetCardSwitchNotificationRequest{
					Identifier: &cpPb.GetCardSwitchNotificationRequest_ExternalRefId{ExternalRefId: "client-1"},
				}).Return(&cpPb.GetCardSwitchNotificationResponse{
					Status: rpc.StatusOk(),
					CardNotification: &cardPb.CardNotification{
						Id:                       "card-notification-1",
						CardId:                   "card-1",
						RetrievalReferenceNumber: "rrn-1",
						Status:                   cardEnumsPb.TransactionState_TRANSACTION_STATE_SUCCESS,
						ExternalRefId:            "client-1",
					},
				}, nil)
				mockMerchantClient.EXPECT().GetMerchant(gomock.Any(), &merchantPb.GetMerchantRequest{
					Identifier: &merchantPb.GetMerchantRequest_PiId{PiId: "pi-to-1"},
				}).Return(&merchantPb.GetMerchantResponse{
					Status:   rpc.StatusOk(),
					Merchant: &merchantPb.Merchant{Id: "merchant-id-1"},
				}, nil)
				mockDcMandateClient.EXPECT().GetDebitCardMandate(gomock.Any(), &dcMandatePb.GetDebitCardMandateRequest{
					Identifier: &dcMandatePb.GetDebitCardMandateRequest_DedupeId{
						DedupeId: &dcMandatePb.DebitCardMandateDedupeId{
							CardId:     "card-1",
							MerchantId: "merchant-id-1",
						},
					},
				}).Return(&dcMandatePb.GetDebitCardMandateResponse{
					Status: rpc.StatusOk(),
					DebitCardMandate: &dcMandatePb.DebitCardMandate{
						RecurringPaymentId: "recurring-payment-id-1",
					},
				}, nil)
				mockActionDao.EXPECT().Create(gomock.Any(), &recurringpaymentPb.RecurringPaymentsAction{
					RecurringPaymentId: "recurring-payment-id-1",
					ClientRequestId:    "client-1",
					Action:             recurringpaymentPb.Action_EXECUTE,
					ActionMetadata: &recurringpaymentPb.ActionMetadata{
						ExecuteActionMetadate: &recurringpaymentPb.ExecuteActionMetaData{
							Amount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        10,
							},
							RecurringPaymentTypeSpecificExecutionInfo: &recurringpaymentPb.RecurringPaymentTypeSpecificExecutionInfo{
								Data: &recurringpaymentPb.RecurringPaymentTypeSpecificExecutionInfo_DcMandateExecutionInfo_{
									DcMandateExecutionInfo: &recurringpaymentPb.RecurringPaymentTypeSpecificExecutionInfo_DcMandateExecutionInfo{
										IsRegistrationExecution: false,
									},
								},
							},
						},
					},
					State: recurringpaymentPb.ActionState_ACTION_SUCCESS,
				}).Return(&recurringpaymentPb.RecurringPaymentsAction{}, nil)
			},
			want: &consumerPb.ConsumerResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_SUCCESS},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockFailedEnachTxnPublisher := mock_queue.NewMockPublisher(ctr)
			mockSavingsClient := savingsMock.NewMockSavingsClient(ctr)
			mockEnachVgClient := mocks2.NewMockEnachClient(ctr)
			mockPiClient := mockPi.NewMockPiClient(ctr)
			mockActorProcessor := mock_internal.NewMockActorProcessor(ctr)
			mockActionDao := mocks3.NewMockRecurringPaymentsActionDao(ctr)
			mockDao := mocks3.NewMockRecurringPaymentDao(ctr)
			mockEnachClient := mocks4.NewMockEnachServiceClient(ctr)
			mockEventBroker := eventMock.NewMockBroker(ctr)
			mockDcMandateClient := dcMandateMocks.NewMockDebitCardMandateServiceClient(ctr)
			mockMerchantClient := merchantMocksPb.NewMockMerchantServiceClient(ctr)
			mockCardClient := cardMocks.NewMockCardProvisioningClient(ctr)
			mockReleaseEvaluator := releaseMock.NewMockIEvaluator(ctr)

			tt.setupMocks(mockSavingsClient, mockEnachVgClient, mockPiClient, mockActorProcessor, mockActionDao, mockDao, mockEnachClient, mockDcMandateClient, mockMerchantClient, mockCardClient, mockFailedEnachTxnPublisher, mockReleaseEvaluator)
			mockEventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
			c := &ConsumerService{
				gconf:                   gconf,
				savingsClient:           mockSavingsClient,
				enachVgClient:           mockEnachVgClient,
				piClient:                mockPiClient,
				actorProcessor:          mockActorProcessor,
				rpActionDao:             mockActionDao,
				rpDao:                   mockDao,
				enachClient:             mockEnachClient,
				eventBroker:             mockEventBroker,
				dcMandateClient:         mockDcMandateClient,
				merchantClient:          mockMerchantClient,
				cpClient:                mockCardClient,
				failedEnachTxnPublisher: mockFailedEnachTxnPublisher,
				releaseEvaluator:        mockReleaseEvaluator,
			}
			got, err := c.ProcessOffAppRecurringPaymentExecution(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessRecurringPaymentCreationAuthorizationVendorCallback() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("ProcessRecurringPaymentCreationAuthorizationVendorCallback() got = %v, want %v", got, tt.want)
			}
		})
	}

}
