package consumer

import (
	"flag"
	"os"
	"testing"

	rpServerGenConf "github.com/epifi/gamma/recurringpayment/config/server/genconf"
	"github.com/epifi/gamma/recurringpayment/test"
)

var gconf *rpServerGenConf.Config

// nolint:dogsled
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, _, gconf, _, teardown = test.InitTestServerV2()

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
