package consumer

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/multierr"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
	"k8s.io/apimachinery/pkg/util/waitgroup"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpConsumerPb "github.com/epifi/gamma/api/recurringpayment/consumer"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	timelinePb "github.com/epifi/gamma/api/timeline"
	merchantResolutionPb "github.com/epifi/gamma/api/vendorgateway/merchantresolution"
	vgEnachPb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/enach/enums"
	"github.com/epifi/gamma/api/vendors/paystatusmapping"
	actorProcessor "github.com/epifi/gamma/recurringpayment/internal/actor"
)

func (c *ConsumerService) FetchAndCreateFailedEnachTransactions(ctx context.Context, req *rpConsumerPb.FetchAndCreateFailedEnachTransactionsRequest) (*rpConsumerPb.ConsumerResponse, error) {

	logger.Info(ctx, "Packet Consumed in FetchAndCreateFailedEnachTransactions consumer", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
	// Validate date range
	fromDate := *datetime.DateToTime(req.GetFromDate(), datetime.IST)
	toDate := *datetime.DateToTime(req.GetToDate(), datetime.IST)

	if fromDate.After(toDate) {
		logger.Error(ctx, "invalid date range: fromDate is after toDate",
			zap.String(logger.ACTOR_ID_V2, req.GetActorId()),
			zap.Time("fromDate", fromDate),
			zap.Time("toDate", toDate))
		return getPermanentFailureRes(), nil
	}

	// Check if date range is beyond the max allowed days and return error if so
	maxAllowedDays := time.Duration(c.gconf.EnachConsumerConfig().MaxAllowedDaysToFetchFailedTxns()) * 24 * time.Hour
	if toDate.Sub(fromDate) > maxAllowedDays {
		logger.Error(ctx, "invalid date range: difference between fromDate and toDate is more than 2 days",
			zap.String(logger.ACTOR_ID_V2, req.GetActorId()),
			zap.Time("fromDate", fromDate),
			zap.Time("toDate", toDate),
			zap.Duration("difference", toDate.Sub(fromDate)))
		return getPermanentFailureRes(), nil
	}
	err := c.fetchAndCreateFailedEnachTransactions(ctx, req, commonvgpb.Vendor_FEDERAL_BANK)
	//nolint:dupl
	switch {
	case errors.Is(err, epifierrors.ErrTransient):
		logger.Error(ctx, "transient error while fetching and creating recurring payments", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return getTransientFailureRes(), nil
	case errors.Is(err, epifierrors.ErrPermanent):
		logger.Error(ctx, "permanent error while fetching and creating recurring payments", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return getPermanentFailureRes(), nil
	case err != nil:
		logger.Error(ctx, "unknown error while fetching and creating recurring payments", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return getTransientFailureRes(), nil
	}
	return getSuccessRes(), nil
}

func (c *ConsumerService) fetchAndCreateFailedEnachTransactions(
	ctx context.Context,
	req *rpConsumerPb.FetchAndCreateFailedEnachTransactionsRequest,
	partnerBank commonvgpb.Vendor,
) error {
	// step 1: fetch the savings account essentials
	savingsAccountEssentials, savingsAccountEssentialsErr := c.fetchSavingsAccount(ctx, req.GetActorId(), partnerBank)
	if savingsAccountEssentialsErr != nil {
		return savingsAccountEssentialsErr
	}

	// fetch failed ENACH transactions for the user
	enachTxns, enachTxnsErr := c.enachVgClient.FetchEnachTransactions(ctx, &vgEnachPb.FetchEnachTransactionsRequest{
		Header:            &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
		TransactionFilter: &vgEnachPb.FetchEnachTransactionsRequest_AccountNumber{AccountNumber: savingsAccountEssentials.GetAccountNo()},
		FromDate:          req.GetFromDate(),
		ToDate:            req.GetToDate(),
	})
	if err := epifigrpc.RPCError(enachTxns, enachTxnsErr); err != nil {
		if enachTxns.GetStatus().IsRecordNotFound() {
			return fmt.Errorf("no failed transactions found for the user %s, %s, %w", req.GetActorId(), epifierrors.ErrRecordNotFound.Error(), epifierrors.ErrPermanent)
		}
		return fmt.Errorf("failed to fetch the active mandates %s, %w", err.Error(), epifierrors.ErrTransient)
	}
	if enachTxns.GetStatus().IsSuccess() && len(enachTxns.GetTransactions()) == 0 {
		return fmt.Errorf("no failed transactions found for the user %s, %s, %w", req.GetActorId(), epifierrors.ErrRecordNotFound.Error(), epifierrors.ErrPermanent)
	}

	// Filter failed transactions and collect unique UMRNs
	var failedTxns []*vgEnachPb.EnachTransaction
	umrnSet := make(map[string]*enachPb.EnachMandate)
	for _, txn := range enachTxns.GetTransactions() {
		if txn.GetResponseStatus().GetStatus() == enums.TransactionStatus_TRANSACTION_STATUS_FAILED {
			failedTxns = append(failedTxns, txn)
			umrnSet[txn.GetUmrn()] = nil
		} else {
			// TODO(Sundeep): Remove this once monitoring the logs in prod.
			logger.Info(
				ctx,
				"found transaction in non-failed state, skipping",
				zap.String(logger.UMRN, txn.GetUmrn()),
				zap.String(logger.REFERENCE_ID, txn.GetTransactionReference()),
				zap.String(logger.ACTOR_ID_V2, req.GetActorId()),
			)
		}
	}

	if len(failedTxns) == 0 {
		logger.Info(ctx, "no failed ENACH transactions found", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return nil
	}

	// Batch fetch all mandates
	mandateMap := make(map[string]*enachPb.EnachMandate)
	for umrn, _ := range umrnSet {
		// TODO(Sundeep): Make a bulk API call to fetch all mandates
		enachMandate, enachMandateErr := c.enachClient.GetEnachMandate(ctx, &enachPb.GetEnachMandateRequest{
			Identifier: &enachPb.GetEnachMandateRequest_Umrn{
				Umrn: umrn,
			},
		})
		if err := epifigrpc.RPCError(enachMandate, enachMandateErr); err != nil {
			if !enachMandate.GetStatus().IsRecordNotFound() {
				return fmt.Errorf("failed to check if enach exists in our system for UMRN %s: %s, %w", umrn, err.Error(), epifierrors.ErrTransient)
			}
			logger.Error(ctx, "received failed ENACH transaction for a mandate that does not exist in our system", zap.String(logger.UMRN, umrn))
			continue
		}
		mandateMap[umrn] = enachMandate.GetEnachMandate()
	}

	internalActorPi, internalActorPiErr := c.resolveInternalActorPi(ctx, savingsAccountEssentials.GetAccountNo(), savingsAccountEssentials.GetIfscCode())
	if internalActorPiErr != nil {
		return internalActorPiErr
	}

	// Create buffered channel with size equal to number of transactions
	errChan := make(chan error, len(failedTxns))
	var wg waitgroup.SafeWaitGroup

	for _, enachTxn := range failedTxns {
		// Skip if mandate doesn't exist
		if _, exists := mandateMap[enachTxn.GetUmrn()]; !exists {
			// We have set up a kibana alert on the following log line to track the number of times this happens
			logger.Error(
				ctx,
				"received failed ENACH transaction for a mandate that does not exist in our system",
				zap.String(logger.REFERENCE_ID, enachTxn.GetTransactionReference()),
				zap.String(logger.UMRN, enachTxn.GetUmrn()),
			)
			// TODO: Evaluate if this can be gracefully handled by creating the mandate in our system using c.fetchAndCreateOffAppRecurringPaymentsForEnach
			// The decision needs to be based on how often we hit this case. For now we proceed with creating the remaining transactions.
			continue
		}

		enachTxn := enachTxn
		_ = wg.Add(1)
		goroutine.RunWithCtx(ctx, func(ctx context.Context) {
			defer wg.Done()

			// resolve merchant details
			merchantResolutionRes, merchantResolutionErr := c.merchantResolutionClient.MerchantResolution(ctx, &merchantResolutionPb.MerchantResolutionRequest{
				Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IN_HOUSE},
				RawMerchantName: enachTxn.GetUserName(),
				IsFiTxn:         true,
				IsMerchantTxn:   true,
			})
			if err := epifigrpc.RPCError(merchantResolutionRes, merchantResolutionErr); err != nil && !merchantResolutionRes.GetStatus().IsRecordNotFound() {
				logger.Error(
					ctx,
					"failed to fetch the sanitized merchant name",
					zap.String(logger.REFERENCE_ID, enachTxn.GetTransactionReference()),
					zap.String(logger.UMRN, enachTxn.GetUmrn()),
					zap.Error(err),
				)
				errChan <- fmt.Errorf("failed to fetch the sanitized merchant name for txn %s: %s, %w", enachTxn.GetTransactionReference(), err.Error(), epifierrors.ErrTransient)
				return
			}

			// resolve merchant details for the entity debiting the txn & create other Actor & PI
			otherActorPi, otherActorId, err := c.actorProcessor.ResolveOtherActorAndPiForOffAppRecurringPayment(ctx, rpPb.RecurringPaymentType_ENACH_MANDATES, &actorProcessor.ResolveOtherActorAndPiForOffAppRecurringPaymentReq{
				InternalActorId: req.GetActorId(),
				EnachPayload: &actorProcessor.EnachPayloadToResolveActorAndPi{
					OrgName: merchantResolutionRes.GetMerchantName(),
				},
			})
			if err != nil {
				logger.Error(
					ctx,
					"failed to resolve other actor for merchant",
					zap.String(logger.REFERENCE_ID, enachTxn.GetTransactionReference()),
					zap.String(logger.UMRN, enachTxn.GetUmrn()),
					zap.String(logger.MERCHANT_ID, merchantResolutionRes.GetDsMerchantId()),
					zap.Error(err),
				)
				errChan <- fmt.Errorf("failed to resolve other actor for txn %s: %s, %w", enachTxn.GetTransactionReference(), err.Error(), epifierrors.ErrTransient)
				return
			}

			// resolve timeline for other actor
			_, createTimelineErr := c.createTimeline(ctx, req.GetActorId(), otherActorId, internalActorPi.GetName(), otherActorPi.GetName(), timelinePb.Ownership_EPIFI_TECH)
			if createTimelineErr != nil {
				logger.Error(
					ctx,
					"failed to create timeline for transaction",
					zap.String(logger.REFERENCE_ID, enachTxn.GetTransactionReference()),
					zap.String(logger.UMRN, enachTxn.GetUmrn()),
					zap.String(logger.FROM_ACTOR_ID, req.GetActorId()),
					zap.String(logger.TO_ACTOR_ID, otherActorId),
				)
				errChan <- fmt.Errorf("failed to create timeline for txn %s: %s, %w", enachTxn.GetTransactionReference(), createTimelineErr.Error(), epifierrors.ErrTransient)
				return
			}

			// create order & txn and publish order update event
			_, _, createOrderAndTxnErr := c.createOffAppOrderAndTxnAndPublishOrderUpdate(ctx, enachTxn, internalActorPi, otherActorPi, req.GetActorId(), otherActorId)
			if createOrderAndTxnErr != nil {
				if errors.Is(createOrderAndTxnErr, epifierrors.ErrAlreadyExists) {
					logger.Info(ctx, "transaction already exists", zap.String(logger.REFERENCE_ID, enachTxn.GetTransactionReference()))
					errChan <- nil
					return
				}
				logger.Error(
					ctx,
					"failed to create order and txn",
					zap.String(logger.REFERENCE_ID, enachTxn.GetTransactionReference()),
					zap.String(logger.UMRN, enachTxn.GetUmrn()),
					zap.String(logger.FROM_ACTOR_ID, req.GetActorId()),
					zap.String(logger.TO_ACTOR_ID, otherActorId),
				)
				errChan <- fmt.Errorf("failed to create order and txn for txn %s: %s, %w", enachTxn.GetTransactionReference(), createOrderAndTxnErr.Error(), epifierrors.ErrTransient)
				return
			}
		})
	}

	// Start a goroutine to close errChan once all processing goroutines are done
	goroutine.RunWithCtx(ctx, func(ctx context.Context) {
		wg.Wait()
		close(errChan)
	})

	// Collect all errors and return a combined error
	var overallErr error
	for err := range errChan {
		if err != nil {
			overallErr = multierr.Append(overallErr, err)
		}
	}

	if overallErr != nil {
		return fmt.Errorf("error processing transactions: %w", overallErr)
	}
	return nil
}

// CreateTimeline creates timeline between two actors if not already exists
func (c *ConsumerService) createTimeline(ctx context.Context, primaryActorId string, secondaryActorId string, primaryActorName string, secondaryActorName string, ownership timelinePb.Ownership) (*timelinePb.Timeline, error) {
	createTimelineResponse, err := c.timelineClient.Create(ctx, &timelinePb.CreateRequest{
		PrimaryActorId:     primaryActorId,
		SecondaryActorId:   secondaryActorId,
		PrimaryActorName:   primaryActorName,
		SecondaryActorName: secondaryActorName,
		Ownership:          ownership,
	})

	switch {
	case err != nil:
		return nil, fmt.Errorf("timelineClient.Create() failed: %s, %w", err.Error(), epifierrors.ErrTransient)
	case !(createTimelineResponse.GetStatus().IsSuccess() || createTimelineResponse.GetStatus().IsAlreadyExists()):
		return nil, fmt.Errorf("unsuccessful status response from timelineClient.Create(): %s: %w", createTimelineResponse.GetStatus(), epifierrors.ErrTransient)
	default:
		return createTimelineResponse.GetTimeline(), nil
	}
}

func (c *ConsumerService) createOffAppOrderAndTxnAndPublishOrderUpdate(
	ctx context.Context,
	enachTxn *vgEnachPb.EnachTransaction,
	internalActorPi, otherActorPi *piPb.PaymentInstrument,
	internalActorId, otherActorId string) (*orderPb.Order, *paymentPb.Transaction, error) {

	txnStatusCodeMapping := paystatusmapping.GetEnachStatusMappingByStatusCode(enachTxn.GetResponseStatus().GetDetailedStatusCode())

	createOwtRes, createOwtErr := c.orderClient.CreateOrderWithTransaction(ctx, &orderPb.CreateOrderWithTransactionRequest{
		OrderParam: &orderPb.CreateOrderWithTransactionRequest_OrderCreationParams{
			ActorFrom:    internalActorId,
			ActorTo:      otherActorId,
			Workflow:     orderPb.OrderWorkflow_NO_OP,
			Provenance:   orderPb.OrderProvenance_EXTERNAL,
			OrderPayload: nil,
			Status:       orderPb.OrderStatus_PAYMENT_FAILED,
			Tags:         nil,
			Device:       nil,
			ClientReqId:  c.uuidGen.GenerateUuid(),
		},
		TransactionParam: &orderPb.CreateOrderWithTransactionRequest_TransactionCreationParams{
			PiFrom:          internalActorPi.GetId(),
			PiTo:            otherActorPi.GetId(),
			Remarks:         fmt.Sprintf("UMRN: %s", enachTxn.GetUmrn()),
			PaymentProtocol: paymentPb.PaymentProtocol_ENACH,
			Status:          paymentPb.TransactionStatus_FAILED,
			ProtocolStatus:  0,
			ReqInfo:         nil,
			// Store the transaction execution timestamp in ExecutedAt field (which maps
			// to PartnerExecutedAt field of transactions table) even though its deprecated and
			// there is no money movement involved, so that the correct transaction timestamp
			// can be displayed in the receipt.
			ExecutedAt: enachTxn.GetRequestTimestamp(),
			DetailedStatus: &paymentPb.TransactionDetailedStatus{
				Details: nil,
				DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
					{
						StatusCodePayer:         txnStatusCodeMapping.GetStatusCode(),
						StatusDescriptionPayer:  txnStatusCodeMapping.GetStatusDescription(),
						StatusCodePayee:         "",
						StatusDescriptionPayee:  "",
						ErrorCategory:           0,
						CreatedAt:               timestamppb.New(c.clock.Now()),
						State:                   paymentPb.TransactionDetailedStatus_DetailedStatus_FAILURE,
						CustomerLevelStatusList: nil,
						RawStatusCode:           txnStatusCodeMapping.GetRawStatusCode(),
						RawStatusDescription:    "",
						SystemErrorDescription:  "",
						Api:                     paymentPb.TransactionDetailedStatus_DetailedStatus_ENACH_FAILED_TXN_SYNC,
						UpdatedAt:               nil,
						Note:                    "",
						ReversalTransactionId:   "",
						ReversalCustRefId:       "",
					},
				},
			},
			Utr:                    enachTxn.GetTransactionReference(),
			PartnerRefId:           "",
			DebitedAt:              nil,
			CreditedAt:             nil,
			BatchSerialId:          "",
			CbsId:                  "",
			Ownership:              commontypes.Ownership_EPIFI_TECH,
			RawNotificationDetails: nil,
			// We override the timestamp that needs to be set in the dedupe Id, since we don't
			// set debitedAt/creditedAt in the transaction creation params as no money movement is involved.
			// But we need to set the dedupeId to the time when the transaction was actually attempted so that
			// we can maintain uniqueness in the transactions.
			OverrideDedupeId: &orderPb.CreateOrderWithTransactionRequest_TransactionCreationParams_DedupeId{
				OverrideDedupeTime: enachTxn.GetRequestTimestamp(),
			},
		},
		Amount:                    enachTxn.GetAmount(),
		DisableWorkflowProcessing: true,
	})
	if err := epifigrpc.RPCError(createOwtRes, createOwtErr); err != nil {
		if createOwtRes.GetStatus().IsAlreadyExists() {
			return nil, nil, fmt.Errorf("transaction already exists: %w", epifierrors.ErrAlreadyExists)
		}
		return nil, nil, fmt.Errorf("failed to create order with transaction %s, %w", err.Error(), epifierrors.ErrTransient)
	}
	return createOwtRes.GetOrder(), createOwtRes.GetTransaction(), nil
}
