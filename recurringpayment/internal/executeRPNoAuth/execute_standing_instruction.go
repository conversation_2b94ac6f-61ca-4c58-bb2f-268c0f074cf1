package executeRPNoAuth

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	"go.uber.org/zap"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/pkg/pay"
	"github.com/epifi/gamma/recurringpayment/internal"
)

type StandingInstructionProcessor struct {
	siClient siPb.StandingInstructionServiceClient
}

func NewStandingInstructionProcessor(
	siClient siPb.StandingInstructionServiceClient,
) *StandingInstructionProcessor {
	return &StandingInstructionProcessor{
		siClient: siClient,
	}
}

func (p *StandingInstructionProcessor) ValidatePayment(ctx context.Context) error {
	// todo: confirm if there is any SI specific validations to add here
	return nil
}

func (p *StandingInstructionProcessor) EnquirePaymentStatus(ctx context.Context, req *internal.EnquirePaymentStatusReq) (*internal.EnquirePaymentStatusRes, error) {
	res := &internal.EnquirePaymentStatusRes{}

	executeStatusRes, executeStatusErr := p.siClient.GetExecutionStatus(ctx, &siPb.GetExecutionStatusRequest{
		OriginalRequestId: req.OriginalRequestId,
		PartnerBank:       req.PartnerBank,
		ActorId:           req.ActorId,
		PaymentProtocol:   req.PaymentProtocol,
	})
	if executeStatusErr != nil {
		logger.Error(ctx, "unable to GetExecutionStatus from SI client", zap.Error(executeStatusErr))
		return nil, fmt.Errorf("unable to GetExecutionStatus from SI client, %w", executeStatusErr)
	}

	if executeStatusRes.Status.IsInProgress() {
		res.TransactionStatus = paymentPb.TransactionStatus_IN_PROGRESS
		return res, nil
	}
	if !executeStatusRes.Status.IsSuccess() {
		res.TransactionStatus = paymentPb.TransactionStatus_FAILED
		return res, nil
	}

	res.TransactionStatus = paymentPb.TransactionStatus_SUCCESS
	res.Utr = executeStatusRes.GetUtr()
	res.RawResponseCode = executeStatusRes.GetRawResponseCode()
	res.RawResponseDescription = executeStatusRes.GetRawResponseDescription()
	res.StatusCode = executeStatusRes.GetStatusCode()
	res.StatusDescriptionPayer = executeStatusRes.GetStatusDescriptionPayer()
	res.TransactionTimestamp = executeStatusRes.GetTransactionTimestamp()

	return res, nil
}

func (p *StandingInstructionProcessor) GetEnrichedTransaction(ctx context.Context, transaction *paymentPb.Transaction) (*paymentPb.Transaction, error) {
	transaction, err := updatePaymentProtocol(transaction)
	if err != nil {
		logger.Error(ctx, "unable to updatePaymentProtocol", zap.Error(err))
		return transaction, fmt.Errorf("unable to updatePaymentProtocol %w", err)
	}

	transaction, err = updateDedupeId(transaction)
	if err != nil {
		logger.Error(ctx, "unable to updateDedupeRequestId", zap.Error(err))
		return transaction, fmt.Errorf("unable to updateDedupeRequestId %w", err)
	}

	return transaction, nil
}
func updateDedupeId(transaction *paymentPb.Transaction) (*paymentPb.Transaction, error) {
	if transaction.GetPartnerBank() == commonvgpb.Vendor_FEDERAL_BANK {
		transaction.DedupeId = &paymentPb.DedupeId{
			RequestId: idgen.FederalRandomDigitsSequence(pay.FederalExecuteStandingInstructionPrefix, 5),
		}
		return transaction, nil
	}

	return transaction, fmt.Errorf("couldn't find a matching vendor for standing instruction execute %s", transaction.GetPartnerBank().String())
}
func updatePaymentProtocol(transaction *paymentPb.Transaction) (*paymentPb.Transaction, error) {
	transaction.PaymentProtocol = paymentPb.PaymentProtocol_NEFT
	return transaction, nil
}

func (p *StandingInstructionProcessor) ExecutePayment(ctx context.Context, recurringPaymentId string, transaction *paymentPb.Transaction) (*internal.ExecutePaymentRes, error) {
	res := &internal.ExecutePaymentRes{}

	executeRes, executeErr := p.siClient.Execute(ctx, &siPb.ExecuteRequest{
		RecurringPaymentId: recurringPaymentId,
		Amount:             transaction.GetAmount(),
		Protocol:           transaction.GetPaymentProtocol(),
		RequestId:          transaction.GetDedupeId().GetRequestId(),
	})
	if err := epifigrpc.RPCError(executeRes, executeErr); err != nil {
		logger.Error(ctx, "unable to Execute Standing Instruction Payment", zap.Error(err))
		return nil, fmt.Errorf("unable to Execute Standing Instruction Payment %w", err)
	}

	res.VendorResponseReason = executeRes.GetVendorResponseReason()
	res.VendorResponseCode = executeRes.GetVendorResponseCode()
	res.StatusCode = executeRes.GetStatusCode()
	res.StatusDescriptionPayer = executeRes.GetStatusDescriptionPayer()
	res.StatusDescriptionPayee = executeRes.GetStatusDescriptionPayee()

	return res, nil

}
