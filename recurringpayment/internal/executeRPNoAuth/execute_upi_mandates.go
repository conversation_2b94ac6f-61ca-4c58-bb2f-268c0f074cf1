package executeRPNoAuth

import (
	"context"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/recurringpayment/internal"
)

type UPIMandatesProcessor struct {
}

func NewUPIMandatesProcessor() *UPIMandatesProcessor {
	return &UPIMandatesProcessor{}
}

func (p *UPIMandatesProcessor) ValidatePayment(ctx context.Context) error {
	return nil
}

func (p *UPIMandatesProcessor) EnquirePaymentStatus(ctx context.Context, req *internal.EnquirePaymentStatusReq) (*internal.EnquirePaymentStatusRes, error) {
	return nil, nil
}

func (p *UPIMandatesProcessor) GetEnrichedTransaction(ctx context.Context, transaction *paymentPb.Transaction) (*paymentPb.Transaction, error) {
	return nil, nil
}

func (p *UPIMandatesProcessor) ExecutePayment(ctx context.Context, recurringPaymentId string, transaction *paymentPb.Transaction) (*internal.ExecutePaymentRes, error) {
	return nil, nil

}
