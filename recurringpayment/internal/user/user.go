package user

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
)

type UserProcessor struct {
	userClient    userPb.UsersClient
	userGrpClient userGroupPb.GroupClient
}

func NewUserProcessor(
	userClient userPb.UsersClient,
	userGrpClient userGroupPb.GroupClient,
) *UserProcessor {
	return &UserProcessor{
		userClient:    userClient,
		userGrpClient: userGrpClient,
	}
}

func (u *UserProcessor) GetUserFromActorId(ctx context.Context, actorId string, ownership commontypes.Ownership) (*UserDetails, error) {
	userRes, err := u.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
		WantPhotoInBase64: false,
	})
	if err != nil {
		logger.Error(ctx, "error fetching user id for actor ", zap.String(logger.ACTOR_ID_V2, actorId),
			zap.Error(err))
		return nil, err
	}
	return &UserDetails{
		PhoneNumber: userRes.GetUser().GetProfile().GetPhoneNumber(),
		Email:       userRes.GetUser().GetProfile().GetEmail(),
		Name:        userRes.GetUser().GetProfile().GetKycName(),
	}, nil
}

// GetUserGroups returns users groups to which a given user is linked
// in case user doesn't belong to any grp then empty slice is returned
func (u *UserProcessor) GetUserGroups(ctx context.Context, userId string) ([]commontypes.UserGroup, error) {
	userRes, userErr := u.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_Id{Id: userId},
	})
	if err := epifigrpc.RPCError(userRes, userErr); err != nil {
		return nil, fmt.Errorf("failed to fetch user: %s: %w", userId, err)
	}

	userGrpRes, userGrpErr := u.userGrpClient.GetGroupsMappedToEmail(ctx,
		&userGroupPb.GetGroupsMappedToEmailRequest{Email: userRes.GetUser().GetProfile().GetEmail()})
	if err := epifigrpc.RPCError(userGrpRes, userGrpErr); err != nil {
		return nil, fmt.Errorf("failed to fetch user group: %s: %w", userId, err)
	}

	return userGrpRes.GetGroups(), nil
}
