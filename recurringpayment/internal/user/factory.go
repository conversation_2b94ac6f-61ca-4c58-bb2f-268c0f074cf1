// nolint: goimports
//
//go:generate mockgen -source=factory.go -destination=mocks/mock_factory.go -package=mocks
package user

import (
	"context"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/google/wire"
)

type UserDetails struct {
	PhoneNumber *commontypes.PhoneNumber
	Email       string
	Name        *commontypes.Name
}

var UserProcessorFactoryWireSet = wire.NewSet(
	NewUserProcessorFactory, wire.Bind(new(IUserProcessorFactory), new(*UserProcessorFactory)),
	NewNbfcUserProcessor, NewUserProcessor)

type IUserProcessorFactory interface {
	GetUserProcessor(ctx context.Context, actorId string, ownership commontypes.Ownership) IUserProcessor
}

type UserProcessorFactory struct {
	nbfcUserProcessor    *NbfcUserProcessor
	defaultUserProcessor *UserProcessor
}

func NewUserProcessorFactory(nbfcUserProcessor *NbfcUserProcessor, defaultUserProcessor *UserProcessor) *UserProcessorFactory {
	return &UserProcessorFactory{
		nbfcUserProcessor:    nbfcUserProcessor,
		defaultUserProcessor: defaultUserProcessor,
	}
}

var _ IUserProcessorFactory = &UserProcessorFactory{}

func (u *UserProcessorFactory) GetUserProcessor(ctx context.Context, actorId string, ownership commontypes.Ownership) IUserProcessor {
	switch ownership {
	case commontypes.Ownership_STOCK_GUARDIAN_TSP:
		return u.nbfcUserProcessor
	default:
		return u.defaultUserProcessor
	}
}
