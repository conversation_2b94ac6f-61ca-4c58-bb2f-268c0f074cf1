package user

import (
	"context"
	"fmt"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifigrpc"

	actorPb "github.com/epifi/gamma/api/actor"
	tspUserPb "github.com/epifi/gamma/api/tspuser"
)

type NbfcUserProcessor struct {
	tspUserClient tspUserPb.TspUserServiceClient
	actorClient   actorPb.ActorClient
}

var _ IUserProcessor = &NbfcUserProcessor{}

func NewNbfcUserProcessor(tspUserClient tspUserPb.TspUserServiceClient, actorClient actorPb.ActorClient) *NbfcUserProcessor {
	return &NbfcUserProcessor{
		tspUserClient: tspUserClient,
		actorClient:   actorClient,
	}
}

func (u *NbfcUserProcessor) GetUserFromActorId(ctx context.Context, actorId string, ownership commontypes.Ownership) (*UserDetails, error) {
	actorDetails, err := u.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(actorDetails, err); te != nil {
		return nil, fmt.Errorf("failed to fetch actor details: %s: %w", actorId, te)
	}
	return &UserDetails{
		PhoneNumber: actorDetails.GetMobileNumber(),
		Email:       actorDetails.GetEmailId(),
		Name:        actorDetails.GetName(),
	}, nil
}

func (u *NbfcUserProcessor) GetUserGroups(ctx context.Context, userId string) ([]commontypes.UserGroup, error) {
	return nil, fmt.Errorf("concept of user groups not supported by NBFC users : %s", userId)
}
