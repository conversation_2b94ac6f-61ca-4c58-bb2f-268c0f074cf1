// Code generated by MockGen. DO NOT EDIT.
// Source: factory.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	common "github.com/epifi/be-common/api/typesv2/common"
	user "github.com/epifi/gamma/recurringpayment/internal/user"
	gomock "github.com/golang/mock/gomock"
)

// MockIUserProcessorFactory is a mock of IUserProcessorFactory interface.
type MockIUserProcessorFactory struct {
	ctrl     *gomock.Controller
	recorder *MockIUserProcessorFactoryMockRecorder
}

// MockIUserProcessorFactoryMockRecorder is the mock recorder for MockIUserProcessorFactory.
type MockIUserProcessorFactoryMockRecorder struct {
	mock *MockIUserProcessorFactory
}

// NewMockIUserProcessorFactory creates a new mock instance.
func NewMockIUserProcessorFactory(ctrl *gomock.Controller) *MockIUserProcessorFactory {
	mock := &MockIUserProcessorFactory{ctrl: ctrl}
	mock.recorder = &MockIUserProcessorFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIUserProcessorFactory) EXPECT() *MockIUserProcessorFactoryMockRecorder {
	return m.recorder
}

// GetUserProcessor mocks base method.
func (m *MockIUserProcessorFactory) GetUserProcessor(ctx context.Context, actorId string, ownership common.Ownership) user.IUserProcessor {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserProcessor", ctx, actorId, ownership)
	ret0, _ := ret[0].(user.IUserProcessor)
	return ret0
}

// GetUserProcessor indicates an expected call of GetUserProcessor.
func (mr *MockIUserProcessorFactoryMockRecorder) GetUserProcessor(ctx, actorId, ownership interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserProcessor", reflect.TypeOf((*MockIUserProcessorFactory)(nil).GetUserProcessor), ctx, actorId, ownership)
}
