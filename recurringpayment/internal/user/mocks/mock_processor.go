// Code generated by MockGen. DO NOT EDIT.
// Source: processor.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	common "github.com/epifi/be-common/api/typesv2/common"
	user "github.com/epifi/gamma/recurringpayment/internal/user"
	gomock "github.com/golang/mock/gomock"
)

// MockIUserProcessor is a mock of IUserProcessor interface.
type MockIUserProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockIUserProcessorMockRecorder
}

// MockIUserProcessorMockRecorder is the mock recorder for MockIUserProcessor.
type MockIUserProcessorMockRecorder struct {
	mock *MockIUserProcessor
}

// NewMockIUserProcessor creates a new mock instance.
func NewMockIUserProcessor(ctrl *gomock.Controller) *MockIUserProcessor {
	mock := &MockIUserProcessor{ctrl: ctrl}
	mock.recorder = &MockIUserProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIUserProcessor) EXPECT() *MockIUserProcessorMockRecorder {
	return m.recorder
}

// GetUserFromActorId mocks base method.
func (m *MockIUserProcessor) GetUserFromActorId(ctx context.Context, actorId string, ownership common.Ownership) (*user.UserDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserFromActorId", ctx, actorId, ownership)
	ret0, _ := ret[0].(*user.UserDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserFromActorId indicates an expected call of GetUserFromActorId.
func (mr *MockIUserProcessorMockRecorder) GetUserFromActorId(ctx, actorId, ownership interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserFromActorId", reflect.TypeOf((*MockIUserProcessor)(nil).GetUserFromActorId), ctx, actorId, ownership)
}

// GetUserGroups mocks base method.
func (m *MockIUserProcessor) GetUserGroups(ctx context.Context, userId string) ([]common.UserGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserGroups", ctx, userId)
	ret0, _ := ret[0].([]common.UserGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserGroups indicates an expected call of GetUserGroups.
func (mr *MockIUserProcessorMockRecorder) GetUserGroups(ctx, userId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserGroups", reflect.TypeOf((*MockIUserProcessor)(nil).GetUserGroups), ctx, userId)
}
