//go:generate mockgen -source=processor.go -destination=mocks/mock_processor.go -package=mocks
package user

import (
	"context"

	"github.com/epifi/be-common/api/typesv2/common"
)

type IUserProcessor interface {
	// GetUserFromActorId to fetch the user id of the user by given actor id
	// Return error if error encountered while fetching data
	GetUserFromActorId(ctx context.Context, actorId string, ownership common.Ownership) (*UserDetails, error)
	// GetUserGroups returns users groups to which a given user is linked
	// in case user doesn't belong to any grp then empty slice is returned
	GetUserGroups(ctx context.Context, userId string) ([]common.UserGroup, error)
}
