//go:generate mockgen -source=$PWD/processor.go -destination=$PWD/mocks/mock_processor.go -package=mocks

package domaincreationprocessor

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"time"

	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/durationpb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/frontend/deeplink"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpPayloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
)

type DomainCreationProcessorV1 interface {
	// ValidateCreation validates the recurring payment creation request
	ValidateCreation(ctx context.Context, req *ValidateCreationReq) error
	// CreateDomainEntities is used to create the entities on the domain services
	// This function must be idempotent since this will be called form within an activity.
	CreateDomainEntities(ctx context.Context, req *CreateDomainEntitiesReq) error
	// GetAuthorizationDeeplink returns deeplink where the user should be redirected to for performing authorization
	GetAuthorizationDeeplink(ctx context.Context, recurringPaymentId string) (*deeplink.Deeplink, error)
	// AuthorizeCreation is useful to authorize recurring payment creation.
	AuthorizeCreation(ctx context.Context, req *AuthorizeCreationReq) error
	// This function must be idempotent since this will be called form within an activity.
	EnquireDomainActivationStatus(ctx context.Context, req *EnquireDomainActivationStatusReq) (*EnquireDomainActivationStatusRes, error)
	// GetActivationCoolOff is used to fetch the cooldown after which the activation should be considered
	GetActivationCoolOff(ctx context.Context, vendor commonvgpb.Vendor) (*durationpb.Duration, error)
}

type ValidateCreationReq struct {
	StartTime              time.Time
	EndTime                time.Time
	AllowedFrequency       rpPb.AllowedFrequency
	Payload                []byte
	RecurringPaymentAmount *money.Money
}

type CreateDomainEntitiesReq struct {
	RecurringPayment                    *rpPb.RecurringPayment
	RecurringPaymentActionId            string
	Vendor                              commonvgpb.Vendor
	RecurringPaymentTypeSpecificPayload *rpPayloadPb.RecurringPaymentTypeSpecificCreationPayload
}

type AuthorizeCreationReq struct {
	RecurringPaymentId string
	AuthCredential     *rpPb.Credential
	AuthMetadata       *rpPb.RecurringPaymentCreationAuthMetadata
}

type EnquireDomainActivationStatusReq struct {
	RecurringPaymentActionId string
}

type EnquireDomainActivationStatusRes struct {
	ActivationStatus DomainActivationStatus
	StatusUpdatedAt  *timestampPb.Timestamp
	// ActionDetailedStatusInfo store the detailed status of the recurring payment action which can be
	// used to enrich the recurring payment action detailed status info in the DB.
	ActionDetailedStatusInfo *rpPb.ActionDetailedStatusInfo
}
type DomainActivationStatus string

const (
	DOMAIN_ACTIVATION_STATUS_UNSPECIFIED DomainActivationStatus = "DOMAIN_ACTIVATION_STATUS_UNSPECIFIED"
	DOMAIN_ACTIVATION_STATUS_SUCCESS     DomainActivationStatus = "ACTIVATION_SUCCESS"
	DOMAIN_ACTIVATION_STATUS_FAILURE     DomainActivationStatus = "ACTIVATION_FAILURE"
	DOMAIN_ACTIVATION_STATUS_INPROGRESS  DomainActivationStatus = "ACTIVATION_INPROGRESS"
)
