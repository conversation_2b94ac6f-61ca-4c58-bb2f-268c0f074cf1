package domaincreationprocessor

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/pay/pgerrorcodes"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/recurringpayment/enums"
	"github.com/epifi/gamma/api/recurringpayment/paymentgateway"
)

func TestPgUpiProcessor_EnquireDomainActivationStatus(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name       string
		setupMocks func(md *pgUpiProcessorMockDependencies)
		args       *EnquireDomainActivationStatusReq
		want       *EnquireDomainActivationStatusRes
		wantErr    bool
	}{
		{
			name: "Successful status enquiry",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpaDao.EXPECT().GetById(gomock.Any(), "action-id-1").Return(&rpPb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-id-1",
					ClientRequestId:    "client-request-id",
				}, nil)
				md.mockRpDao.EXPECT().GetById(gomock.Any(), "rp-id-1").Return(&rpPb.RecurringPayment{
					Id: "rp-id-1",
				}, nil)
				md.mockPgClient.EXPECT().GetOrderStatus(gomock.Any(), gomock.Any()).Return(&paymentgateway.GetOrderStatusResponse{
					Status:      rpc.StatusOk(),
					OrderStatus: enums.OrderStatus_ORDER_STATUS_PAID,
				}, nil)
			},
			args: &EnquireDomainActivationStatusReq{
				RecurringPaymentActionId: "action-id-1",
			},
			want: &EnquireDomainActivationStatusRes{
				ActivationStatus: DOMAIN_ACTIVATION_STATUS_SUCCESS,
				StatusUpdatedAt:  timestamppb.Now(),
			},
			wantErr: false,
		},
		{
			name: "Error fetching recurring payment action",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpaDao.EXPECT().GetById(gomock.Any(), "action-id").Return(nil, fmt.Errorf("error"))
			},
			args: &EnquireDomainActivationStatusReq{
				RecurringPaymentActionId: "action-id",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Error fetching recurring payment",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpaDao.EXPECT().GetById(gomock.Any(), "action-id").Return(&rpPb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-id",
				}, nil)
				md.mockRpDao.EXPECT().GetById(gomock.Any(), "rp-id").Return(nil, fmt.Errorf("error"))
			},
			args: &EnquireDomainActivationStatusReq{
				RecurringPaymentActionId: "action-id",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Error fetching order status",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpaDao.EXPECT().GetById(gomock.Any(), "action-id").Return(&rpPb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-id",
					ClientRequestId:    "client-request-id",
				}, nil)
				md.mockRpDao.EXPECT().GetById(gomock.Any(), "rp-id").Return(&rpPb.RecurringPayment{
					Id: "rp-id",
				}, nil)
				md.mockPgClient.EXPECT().GetOrderStatus(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("error"))
			},
			args: &EnquireDomainActivationStatusReq{
				RecurringPaymentActionId: "action-id",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Unknown order status",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpaDao.EXPECT().GetById(gomock.Any(), "action-id").Return(&rpPb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-id",
					ClientRequestId:    "client-request-id",
				}, nil)
				md.mockRpDao.EXPECT().GetById(gomock.Any(), "rp-id").Return(&rpPb.RecurringPayment{
					Id: "rp-id",
				}, nil)
				md.mockPgClient.EXPECT().GetOrderStatus(gomock.Any(), gomock.Any()).Return(&paymentgateway.GetOrderStatusResponse{
					Status:      rpc.StatusOk(),
					OrderStatus: enums.OrderStatus_ORDER_STATUS_IN_PROGRESS,
				}, nil)
			},
			args: &EnquireDomainActivationStatusReq{
				RecurringPaymentActionId: "action-id",
			},
			want: &EnquireDomainActivationStatusRes{
				ActivationStatus: DOMAIN_ACTIVATION_STATUS_INPROGRESS,
				StatusUpdatedAt:  timestamppb.Now(),
			},
			wantErr: false,
		},
		{
			name: "Order status paid, mandate registration in progress",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpaDao.EXPECT().GetById(gomock.Any(), "action-id").Return(&rpPb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-id",
					ClientRequestId:    "client-request-id",
				}, nil)
				md.mockRpDao.EXPECT().GetById(gomock.Any(), "rp-id").Return(&rpPb.RecurringPayment{
					Id: "rp-id",
				}, nil)
				md.mockPgClient.EXPECT().GetOrderStatus(gomock.Any(), gomock.Any()).Return(&paymentgateway.GetOrderStatusResponse{
					Status:      rpc.StatusOk(),
					OrderStatus: enums.OrderStatus_ORDER_STATUS_PAID,
					DetailedStatus: &rpPb.ActionDetailedStatusInfo{
						FiStatusCode:     pgerrorcodes.PaymentGatewayInProgressFiStatusCode,
						ErrorDescription: "",
					},
				}, nil)
			},
			args: &EnquireDomainActivationStatusReq{
				RecurringPaymentActionId: "action-id",
			},
			want: &EnquireDomainActivationStatusRes{
				ActivationStatus: DOMAIN_ACTIVATION_STATUS_INPROGRESS,
				StatusUpdatedAt:  timestamppb.Now(),
				ActionDetailedStatusInfo: &rpPb.ActionDetailedStatusInfo{
					FiStatusCode:     pgerrorcodes.PaymentGatewayInProgressFiStatusCode,
					ErrorDescription: "",
				},
			},
			wantErr: false,
		},
		{
			name: "Order status paid, mandate registration failed at bank",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpaDao.EXPECT().GetById(gomock.Any(), "action-id").Return(&rpPb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-id",
					ClientRequestId:    "client-request-id",
				}, nil)
				md.mockRpDao.EXPECT().GetById(gomock.Any(), "rp-id").Return(&rpPb.RecurringPayment{
					Id: "rp-id",
				}, nil)
				md.mockPgClient.EXPECT().GetOrderStatus(gomock.Any(), gomock.Any()).Return(&paymentgateway.GetOrderStatusResponse{
					Status:      rpc.StatusOk(),
					OrderStatus: enums.OrderStatus_ORDER_STATUS_PAID,
					DetailedStatus: &rpPb.ActionDetailedStatusInfo{
						FiStatusCode:     "RZP1001",
						ErrorDescription: "Mandate authorisation rejected by bank",
					},
				}, nil)
			},
			args: &EnquireDomainActivationStatusReq{
				RecurringPaymentActionId: "action-id",
			},
			want: &EnquireDomainActivationStatusRes{
				ActivationStatus: DOMAIN_ACTIVATION_STATUS_FAILURE,
				StatusUpdatedAt:  timestamppb.Now(),
				ActionDetailedStatusInfo: &rpPb.ActionDetailedStatusInfo{
					FiStatusCode:     "RZP1001",
					ErrorDescription: "Mandate authorisation rejected by bank",
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			processor, md, release := getUpiProcessorWithMocks(t)
			defer release()
			tt.setupMocks(md)
			got, err := processor.EnquireDomainActivationStatus(context.Background(), tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("EnquireDomainActivationStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want != nil {
				got.StatusUpdatedAt = tt.want.StatusUpdatedAt
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("EnquireDomainActivationStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}
