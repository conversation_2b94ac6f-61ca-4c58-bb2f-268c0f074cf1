package domaincreationprocessor

import (
	"context"
	"fmt"

	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/durationpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/epifi/gamma/api/frontend/deeplink"
	timelinePb "github.com/epifi/gamma/api/frontend/deeplink/timeline"
	txnPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	orderPb "github.com/epifi/gamma/api/order"
	payPb "github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	recurringPaymentPb "github.com/epifi/gamma/api/recurringpayment"
	rpPgPb "github.com/epifi/gamma/api/recurringpayment/paymentgateway"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	payScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pay"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/recurringpayment/dao"
)

var (
	paymentTypeToAuthorisationAmount = map[recurringPaymentPb.RecurringPaymentType]*types.Money{
		recurringPaymentPb.RecurringPaymentType_ENACH_MANDATES: {
			CurrencyCode: "INR",
			Units:        0,
		},
		recurringPaymentPb.RecurringPaymentType_UPI_MANDATES: {
			CurrencyCode: "INR",
			Units:        1,
		},
	}
)

type PgEmandateProcessor struct {
	pgClient  rpPgPb.PaymentGatewayServiceClient
	rpaDao    dao.RecurringPaymentsActionDao
	rpDao     dao.RecurringPaymentDao
	piClient  piPb.PiClient
	payClient payPb.PayClient
}

func NewPgEmandateProcessor(
	pgClient rpPgPb.PaymentGatewayServiceClient,
	rpaDao dao.RecurringPaymentsActionDao,
	rpDao dao.RecurringPaymentDao,
	piClient piPb.PiClient,
	payClient payPb.PayClient) *PgEmandateProcessor {
	return &PgEmandateProcessor{
		pgClient:  pgClient,
		rpaDao:    rpaDao,
		rpDao:     rpDao,
		piClient:  piClient,
		payClient: payClient,
	}
}

var _ DomainCreationProcessorV1 = &PgEmandateProcessor{}

func (p *PgEmandateProcessor) ValidateCreation(ctx context.Context, req *ValidateCreationReq) error {
	return nil
}

func (p *PgEmandateProcessor) CreateDomainEntities(ctx context.Context, req *CreateDomainEntitiesReq) error {
	return nil
}

func (p *PgEmandateProcessor) GetAuthorizationDeeplink(ctx context.Context, recurringPaymentId string) (*deeplink.Deeplink, error) {
	recurringPayment, err := p.rpDao.GetById(ctx, recurringPaymentId)
	if err != nil {
		return nil, fmt.Errorf("error [%w] in fetching recurring payment by id", err)
	}
	rpas, err := p.rpaDao.GetByRecurringPaymentId(ctx, recurringPaymentId, dao.WithOrderByCreatedAt(true))
	if err != nil {
		return nil, fmt.Errorf("error [%w] in fetching recurring payment action by recurring payment id", err)
	}
	rpa := rpas[0]
	orchMetaData := &payPb.ClientIdentificationTxnMetaData{
		ClientReqId:     rpa.GetClientRequestId(),
		Workflow:        orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT,
		EntityOwnership: recurringPayment.GetEntityOwnership(),
		DomainOrderData: &payPb.DomainOrderData{
			NextAction:            rpa.GetActionMetadata().GetCreateActionMetadata().GetPostCreationDomainDeeplink(),
			Amount:                paymentTypeToAuthorisationAmount[recurringPayment.GetType()].GetBeMoney(),
			ClientRequestIdExpiry: rpa.GetExpireAt(),
			ToPi:                  recurringPayment.GetPiTo(),
			ToActor:               recurringPayment.GetToActorId(),
			FromPi:                recurringPayment.GetPiFrom(),
		},
		TargetActorId: recurringPayment.GetFromActorId(),
	}
	if recurringPayment.GetPiFrom() != "" {
		piRes, piErr := p.piClient.GetPiById(ctx, &piPb.GetPiByIdRequest{
			Id: recurringPayment.GetPiFrom(),
		})
		if te := epifigrpc.RPCError(piRes, piErr); te != nil {
			return nil, fmt.Errorf("error [%w] in fetching pi by id : %s", te, recurringPayment.GetPiFrom())
		}
		account := piRes.GetPaymentInstrument().GetAccount()
		bankCode := ""
		if len(account.GetIfscCode()) >= 4 {
			bankCode = account.GetIfscCode()[:4]
		}
		orchMetaData.DomainOrderData.AccountsEligibleForPaymentFulfillment = []*payPb.AccountDetails{
			{
				AccountNumber: account.GetActualAccountNumber(),
				IfscCode:      account.GetIfscCode(),
				Name:          account.GetName(),
				BankName:      bankCode,
			},
		}
	}
	orchMetaDataBytes, err := proto.Marshal(orchMetaData)
	if err != nil {
		return nil, fmt.Errorf("error [%w] in marshalling orch metadata", err)
	}
	encryptRes, encryptErr := p.payClient.GetSignedData(ctx, &payPb.GetSignedDataRequest{
		Data: orchMetaDataBytes,
	})
	if te := epifigrpc.RPCError(encryptRes, encryptErr); te != nil {
		return nil, fmt.Errorf("error [%w] in getting signed data", te)
	}
	screenOptions := &payScreenOptions.PaymentOptionsScreenOptions{
		Header:                  &deeplink_screen_option.ScreenOptionHeader{},
		Amount:                  paymentTypeToAuthorisationAmount[recurringPayment.GetType()],
		TransactionUiEntryPoint: timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_EXTERNAL_MANDATE_REGISTRATION,
		OrchestrationMetadata:   encryptRes.GetSignedData(),
		OrderedPaymentOptionTypes: []txnPb.PaymentOptionType{
			txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_NETBANKING,
		},
		ActorTo:            recurringPayment.GetToActorId(),
		PiTo:               recurringPayment.GetPiTo(),
		RecurringPaymentId: recurringPaymentId,
	}
	return deeplinkV3.GetDeeplinkV3(deeplink.Screen_PAYMENT_OPTIONS_FULL_SCREEN, screenOptions)
}

func (p *PgEmandateProcessor) AuthorizeCreation(ctx context.Context, req *AuthorizeCreationReq) error {
	return nil
}

func (p *PgEmandateProcessor) EnquireDomainActivationStatus(ctx context.Context, req *EnquireDomainActivationStatusReq) (*EnquireDomainActivationStatusRes, error) {
	rpa, err := p.rpaDao.GetById(ctx, req.RecurringPaymentActionId)
	if err != nil {
		return nil, fmt.Errorf("error in fetching RPA for given id : %s : %w", req.RecurringPaymentActionId, err)
	}
	recurringPayment, err := p.rpDao.GetById(ctx, rpa.GetRecurringPaymentId())
	if err != nil {
		return nil, fmt.Errorf("error in fetching RP for given id : %s : %w", rpa.GetRecurringPaymentId(), err)
	}
	orderRes, err := p.pgClient.GetOrderStatus(ctx, &rpPgPb.GetOrderStatusRequest{
		RecurringPaymentId: rpa.GetRecurringPaymentId(),
		ClientRequestId:    rpa.GetClientRequestId(),
		Vendor:             recurringPayment.GetPartnerBank(),
		DataFreshness:      rpPgPb.GetOrderStatusRequest_REAL_TIME,
	})
	if te := epifigrpc.RPCError(orderRes, err); te != nil {
		return nil, fmt.Errorf("error in fetching pg order status for given id : %s : %w", rpa.GetRecurringPaymentId(), te)
	}
	return &EnquireDomainActivationStatusRes{
		ActivationStatus: getDomainActivationStatusFromOrderStatusRes(orderRes),
		// setting this to NOW because this is not getting persisted anywhere
		StatusUpdatedAt:          timestamppb.Now(),
		ActionDetailedStatusInfo: orderRes.GetDetailedStatus(),
	}, nil
}

func (p *PgEmandateProcessor) GetActivationCoolOff(ctx context.Context, vendor commonvgpb.Vendor) (*durationpb.Duration, error) {
	// keeping the activation cool off as 0 since the activation can be polled immediately after
	// the payment has been initiated . There is no cool-off from the pg vendor side
	return &durationpb.Duration{
		Seconds: 0,
		Nanos:   0,
	}, nil
}

var _ DomainCreationProcessorV1 = &PgEmandateProcessor{}
