// nolint:goimports
//go:generate mockgen -source=$PWD/factory.go -destination=$PWD/mocks/mock_factory.go -package=mocks

package domaincreationprocessor

import (
	"fmt"

	"github.com/google/wire"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/be-common/pkg/epifierrors"
)

var DomainCreationProcessorWireSet = wire.NewSet(
	NewENachMandatesProcessor,
	NewPgEmandateProcessor,
	NewPgUpiProcessor,
)

var DomainCreationProcessorFactoryWireSet = wire.NewSet(
	DomainCreationProcessorWireSet, wire.NewSet(NewDomainCreationProcessorFactoryImpl, wire.Bind(new(DomainCreationProcessorFactory), new(*DomainCreationProcessorFactoryImpl))),
)

type DomainCreationProcessorFactory interface {
	GetProcessor(paymentType rpPb.RecurringPaymentType, paymentRoute rpPb.RecurringPaymentRoute) (DomainCreationProcessorV1, error)
}

type DomainCreationProcessorFactoryImpl struct {
	EnachMandateCreationProcessor *EnachMandateCreationProcessor
	pgEmandateProcessor           *PgEmandateProcessor
	pgUpiProcessor                *PgUpiProcessor
}

func NewDomainCreationProcessorFactoryImpl(
	enachMandateCreationProcessor *EnachMandateCreationProcessor,
	pgEmandateProcessor *PgEmandateProcessor,
	pgUpiProcessor *PgUpiProcessor,

) *DomainCreationProcessorFactoryImpl {
	return &DomainCreationProcessorFactoryImpl{
		EnachMandateCreationProcessor: enachMandateCreationProcessor,
		pgEmandateProcessor:           pgEmandateProcessor,
		pgUpiProcessor:                pgUpiProcessor,
	}
}

func (a *DomainCreationProcessorFactoryImpl) GetProcessor(paymentType rpPb.RecurringPaymentType, paymentRoute rpPb.RecurringPaymentRoute) (DomainCreationProcessorV1, error) {
	switch paymentRoute {
	case rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE:
		return a.getNativeProcessor(paymentType)
	case rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_EXTERNAL:
		return a.getExternalProcessor(paymentType)
	default:
		// returning native processor by default for backward compatibility
		return a.getNativeProcessor(paymentType)
	}
}

func (a *DomainCreationProcessorFactoryImpl) getExternalProcessor(paymentType rpPb.RecurringPaymentType) (DomainCreationProcessorV1, error) {
	switch paymentType {
	case rpPb.RecurringPaymentType_UPI_MANDATES:
		return a.pgUpiProcessor, nil
	case rpPb.RecurringPaymentType_ENACH_MANDATES:
		return a.pgEmandateProcessor, nil
	default:
		return nil, fmt.Errorf("external factory implementation not found for %s. unsupported recurring payment type, %w", paymentType, epifierrors.ErrInvalidArgument)
	}
}

func (a *DomainCreationProcessorFactoryImpl) getNativeProcessor(paymentType rpPb.RecurringPaymentType) (DomainCreationProcessorV1, error) {
	switch paymentType {
	case rpPb.RecurringPaymentType_ENACH_MANDATES:
		return a.EnachMandateCreationProcessor, nil
	default:
		return nil, fmt.Errorf("native factory implementation not found for %s. unsupported recurring payment type, %w", paymentType, epifierrors.ErrInvalidArgument)
	}
}
