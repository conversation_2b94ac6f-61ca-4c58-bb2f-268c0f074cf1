package domaincreationprocessor

import (
	"context"
	"fmt"

	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/durationpb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/pay/pgerrorcodes"

	"github.com/epifi/gamma/api/frontend/deeplink"
	timelinePb "github.com/epifi/gamma/api/frontend/deeplink/timeline"
	txnPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	orderPb "github.com/epifi/gamma/api/order"
	payPb "github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/recurringpayment/enums"
	rpPgPb "github.com/epifi/gamma/api/recurringpayment/paymentgateway"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	payScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pay"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/recurringpayment/dao"
)

var (
	pgOrderStatusToDomainActivityStatus = map[enums.OrderStatus]DomainActivationStatus{
		enums.OrderStatus_ORDER_STATUS_PAID:        DOMAIN_ACTIVATION_STATUS_SUCCESS,
		enums.OrderStatus_ORDER_STATUS_IN_PROGRESS: DOMAIN_ACTIVATION_STATUS_INPROGRESS,
		enums.OrderStatus_ORDER_STATUS_FAILED:      DOMAIN_ACTIVATION_STATUS_FAILURE,
	}
)

type PgUpiProcessor struct {
	pgClient  rpPgPb.PaymentGatewayServiceClient
	rpaDao    dao.RecurringPaymentsActionDao
	rpDao     dao.RecurringPaymentDao
	payClient payPb.PayClient
}

func NewPgUpiProcessor(pgClient rpPgPb.PaymentGatewayServiceClient,
	rpaDao dao.RecurringPaymentsActionDao,
	rpDao dao.RecurringPaymentDao,
	payClient payPb.PayClient) *PgUpiProcessor {
	return &PgUpiProcessor{
		pgClient:  pgClient,
		rpaDao:    rpaDao,
		rpDao:     rpDao,
		payClient: payClient,
	}
}

func (p *PgUpiProcessor) ValidateCreation(ctx context.Context, req *ValidateCreationReq) error {
	return nil
}

func (p *PgUpiProcessor) CreateDomainEntities(ctx context.Context, req *CreateDomainEntitiesReq) error {
	return nil
}

func (p *PgUpiProcessor) GetAuthorizationDeeplink(ctx context.Context, recurringPaymentId string) (*deeplink.Deeplink, error) {
	recurringPayment, err := p.rpDao.GetById(ctx, recurringPaymentId)
	if err != nil {
		return nil, fmt.Errorf("error [%w] in fetching recurring payment by id %s", err, recurringPaymentId)
	}
	rpas, err := p.rpaDao.GetByRecurringPaymentId(ctx, recurringPaymentId, dao.WithOrderByCreatedAt(true))
	if err != nil {
		return nil, fmt.Errorf("error [%w] in fetching recurring payment action by recurring payment id %s", err, recurringPaymentId)
	}
	rpa := rpas[0]
	orchMetaData := &payPb.ClientIdentificationTxnMetaData{
		ClientReqId:     rpa.GetClientRequestId(),
		Workflow:        orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT,
		EntityOwnership: recurringPayment.GetEntityOwnership(),
		DomainOrderData: &payPb.DomainOrderData{
			NextAction:            rpa.GetActionMetadata().GetCreateActionMetadata().GetPostCreationDomainDeeplink(),
			Amount:                paymentTypeToAuthorisationAmount[recurringPayment.GetType()].GetBeMoney(),
			ClientRequestIdExpiry: rpa.GetExpireAt(),
			ToPi:                  recurringPayment.GetPiTo(),
			ToActor:               recurringPayment.GetToActorId(),
		},
		TargetActorId: recurringPayment.GetFromActorId(),
	}
	orchMetaDataBytes, err := proto.Marshal(orchMetaData)
	if err != nil {
		return nil, fmt.Errorf("error [%w] in marshalling orch metadata for recurring payment id %s", err, recurringPaymentId)
	}
	encryptRes, encryptErr := p.payClient.GetSignedData(ctx, &payPb.GetSignedDataRequest{
		Data: orchMetaDataBytes,
	})
	if te := epifigrpc.RPCError(encryptRes, encryptErr); te != nil {
		return nil, fmt.Errorf("error [%w] in getting signed data", te)
	}
	screenOptions := &payScreenOptions.PaymentOptionsScreenOptions{
		Header:                  &deeplink_screen_option.ScreenOptionHeader{},
		Amount:                  paymentTypeToAuthorisationAmount[recurringPayment.GetType()],
		TransactionUiEntryPoint: timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_EXTERNAL_MANDATE_REGISTRATION,
		OrchestrationMetadata:   encryptRes.GetSignedData(),
		ShouldHideCloseIcon:     false,
		OrderedPaymentOptionTypes: []txnPb.PaymentOptionType{
			txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_COLLECT,
		},
		ActorTo:             recurringPayment.GetToActorId(),
		PiTo:                recurringPayment.GetPiTo(),
		PostPaymentDeeplink: nil,
		RecurringPaymentId:  recurringPaymentId,
	}
	return deeplinkV3.GetDeeplinkV3(deeplink.Screen_PAYMENT_OPTIONS_FULL_SCREEN, screenOptions)
}

func (p *PgUpiProcessor) AuthorizeCreation(ctx context.Context, req *AuthorizeCreationReq) error {
	return nil
}

func (p *PgUpiProcessor) EnquireDomainActivationStatus(ctx context.Context, req *EnquireDomainActivationStatusReq) (*EnquireDomainActivationStatusRes, error) {
	rpa, err := p.rpaDao.GetById(ctx, req.RecurringPaymentActionId)
	if err != nil {
		return nil, fmt.Errorf("error in fetching RPA for given id : %s : %w", req.RecurringPaymentActionId, err)
	}
	recurringPayment, err := p.rpDao.GetById(ctx, rpa.GetRecurringPaymentId())
	if err != nil {
		return nil, fmt.Errorf("error in fetching RP for given id : %s : %w", rpa.GetRecurringPaymentId(), err)
	}
	orderRes, err := p.pgClient.GetOrderStatus(ctx, &rpPgPb.GetOrderStatusRequest{
		RecurringPaymentId: rpa.GetRecurringPaymentId(),
		ClientRequestId:    rpa.GetClientRequestId(),
		Vendor:             recurringPayment.GetPartnerBank(),
		DataFreshness:      rpPgPb.GetOrderStatusRequest_REAL_TIME,
	})
	if te := epifigrpc.RPCError(orderRes, err); te != nil {
		return nil, fmt.Errorf("error in fetching pg order status for given id : %s : %w", rpa.GetRecurringPaymentId(), te)
	}
	return &EnquireDomainActivationStatusRes{
		ActivationStatus: getDomainActivationStatusFromOrderStatusRes(orderRes),
		// setting now since we are not persisting it anywhere
		StatusUpdatedAt: timestampPb.Now(),
		// propagate the detailed action status to the caller, so that, it can be updated by the caller in DB.
		ActionDetailedStatusInfo: orderRes.GetDetailedStatus(),
	}, nil
}

// getDomainActivationStatusFromOrderStatusRes returns the domain activation status based on the order status response
// It takes into account the combination of Recurring Payment Action Detailed Status and the order status, because there
// can be cases when an order (and the associated payment) succeeds, but still the domain activation fails (for ex, the
// mandate authorisation fails on bank's end).
func getDomainActivationStatusFromOrderStatusRes(orderRes *rpPgPb.GetOrderStatusResponse) DomainActivationStatus {
	// Derive the domain activation status from the detailed action status first, since it contains additional status
	// related to the mandate registration irrespective of whether the order has failed or succeeded.
	if orderRes.GetDetailedStatus().GetFiStatusCode() == pgerrorcodes.PaymentGatewayInProgressFiStatusCode {
		return DOMAIN_ACTIVATION_STATUS_INPROGRESS
	}
	if orderRes.GetDetailedStatus().IsActionFailed() {
		return DOMAIN_ACTIVATION_STATUS_FAILURE
	}
	activationStatus, ok := pgOrderStatusToDomainActivityStatus[orderRes.GetOrderStatus()]
	if !ok {
		return DOMAIN_ACTIVATION_STATUS_INPROGRESS
	}
	return activationStatus
}

func (p *PgUpiProcessor) GetActivationCoolOff(ctx context.Context, vendor commonvgpb.Vendor) (*durationpb.Duration, error) {
	// keeping the activation cool off as 0 since the activation can be polled immediately after
	// the payment has been initiated . There is no cool-off from the pg vendor side
	return &durationpb.Duration{
		Seconds: 0,
		Nanos:   0,
	}, nil
}

var _ DomainCreationProcessorV1 = &PgUpiProcessor{}
