package domaincreationprocessor

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"time"

	"google.golang.org/protobuf/types/known/durationpb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/frontend/deeplink"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	pkgScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pkg"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/recurringpayment/config/commons"
)

type EnachMandateCreationProcessor struct {
	validationConfig *commons.EnachCreationValidationConfig
	enachClient      enachPb.EnachServiceClient
}

func NewENachMandatesProcessor(
	validationConfig *commons.EnachCreationValidationConfig,
	enachClient enachPb.EnachServiceClient,
) *EnachMandateCreationProcessor {
	return &EnachMandateCreationProcessor{
		validationConfig: validationConfig,
		enachClient:      enachClient,
	}
}

var (
	_ DomainCreationProcessorV1 = &EnachMandateCreationProcessor{}
)

func (e *EnachMandateCreationProcessor) ValidateCreation(ctx context.Context, req *ValidateCreationReq) error {
	if req.StartTime.Sub(time.Now()) < e.validationConfig.MinRequiredDelayForEnachStartTimeSinceCreation {
		return fmt.Errorf("start time should be atleast %s duration after the creation time", e.validationConfig.MinRequiredDelayForEnachStartTimeSinceCreation.String())
	}

	// check if recurring payment amount is not more than max allowed enach amount
	maxAllowedEnachAmount := money.AmountINR(int64(e.validationConfig.MaxAllowedAmountInRs)).GetPb()
	amountCompareRes, compareErr := money.CompareV2(maxAllowedEnachAmount, req.RecurringPaymentAmount)
	if compareErr != nil {
		return fmt.Errorf("error validation enach creation, %w", compareErr)
	}
	if amountCompareRes == -1 {
		return fmt.Errorf("enach amount is more than max allowed limt")
	}

	return nil
}

func (e *EnachMandateCreationProcessor) CreateDomainEntities(ctx context.Context, req *CreateDomainEntitiesReq) error {
	initiateMandateCreationRes, err := e.enachClient.InitiateMandateCreation(ctx, &enachPb.InitiateMandateCreationRequest{
		ClientRequestId:      req.RecurringPaymentActionId,
		RecurringPaymentId:   req.RecurringPayment.Id,
		RegistrationAuthMode: req.RecurringPaymentTypeSpecificPayload.GetEnachCreationPayload().GetAuthorisationMode(),
		Vendor:               req.Vendor,
	})
	if err = epifigrpc.RPCError(initiateMandateCreationRes, err); err != nil {
		return fmt.Errorf("error while initiating domain entity creation for recurring payment type: %s, err: %w", rpPb.RecurringPaymentType_ENACH_MANDATES, err)
	}
	return nil
}

func (e *EnachMandateCreationProcessor) GetAuthorizationDeeplink(ctx context.Context, recurringPaymentId string) (*deeplink.Deeplink, error) {
	authPayloadRes, err := e.enachClient.GetAuthorizationPayloadForMandateCreation(ctx, &enachPb.GetAuthorizationPayloadForMandateCreationRequest{
		RecurringPaymentId: recurringPaymentId,
	})
	if rpcErr := epifigrpc.RPCError(authPayloadRes, err); rpcErr != nil {
		return nil, fmt.Errorf("enachClient.GetAuthorizationPayloadForMandateCreation rpc call failed : %w", rpcErr)
	}
	// todo (utkarsh) : handle for other vendors as well as and when required
	federalBankAuthPayload := authPayloadRes.GetAuthPayload().GetFederalBankPayload()
	if federalBankAuthPayload == nil {
		return nil, fmt.Errorf("empty federal bank auth paylaod in GetAuthorizationPayloadForMandateCreation response")
	}

	authDeeplinkScreenOptions, _ := deeplinkv3.GetScreenOptionV2(&pkgScreenOptionsPb.ExternalRedirectionWithPayloadScreenOptions{
		RedirectionUrl: federalBankAuthPayload.GetRedirectionUrl(),
		Payload: &pkgScreenOptionsPb.ExternalRedirectionWithPayloadScreenOptions_FormData_{
			FormData: &pkgScreenOptionsPb.ExternalRedirectionWithPayloadScreenOptions_FormData{FormData: federalBankAuthPayload.GetFormData()},
		},
		ExitUrlPrefix:         federalBankAuthPayload.GetExitUrl(),
		RedirectionExpiryTime: federalBankAuthPayload.GetRedirectionExpiryTime(),
		ClearCookies:          true,
	})

	return &deeplink.Deeplink{
		Screen:          deeplink.Screen_EXTERNAL_REDIRECTION_WITH_PAYLOAD,
		ScreenOptionsV2: authDeeplinkScreenOptions,
	}, nil
}

func (e *EnachMandateCreationProcessor) AuthorizeCreation(ctx context.Context, req *AuthorizeCreationReq) error {
	authorizeRes, err := e.enachClient.AuthorizeMandateCreation(ctx, &enachPb.AuthorizeMandateCreationRequest{
		RecurringPaymentId:         req.RecurringPaymentId,
		Umrn:                       req.AuthMetadata.GetEnachMetadata().GetUmrn(),
		NpciRefId:                  req.AuthMetadata.GetEnachMetadata().GetNpciRefId(),
		DestBankReferenceNumber:    req.AuthMetadata.GetEnachMetadata().GetDestBankReferenceNumber(),
		MerchantReferenceMessageId: req.AuthMetadata.GetEnachMetadata().GetMerchantReferenceMessageId(),
	})
	if rpcErr := epifigrpc.RPCError(authorizeRes, err); rpcErr != nil {
		return fmt.Errorf("enachClient.AuthorizeMandateCreation rpc call failed : %w", rpcErr)
	}
	return nil
}

func (e *EnachMandateCreationProcessor) EnquireDomainActivationStatus(ctx context.Context, req *EnquireDomainActivationStatusReq) (*EnquireDomainActivationStatusRes, error) {

	getMandateActionStatusResponse, err := e.enachClient.GetMandateActionStatus(ctx, &enachPb.GetMandateActionStatusRequest{
		ClientRequestId: req.RecurringPaymentActionId,
	})
	if err = epifigrpc.RPCError(getMandateActionStatusResponse, err); err != nil {
		return nil, fmt.Errorf("error while fetching vendor activation for recurring payment type: %s, err: %w", rpPb.RecurringPaymentType_ENACH_MANDATES, err)
	}

	activationStatus, err := e.getVendorActionStatusFromEnachActionStatus(getMandateActionStatusResponse.GetActionStatus())
	if err != nil {
		return nil, fmt.Errorf("mapping for the action status not found, %w", epifierrors.ErrInvalidArgument)
	}

	res := &EnquireDomainActivationStatusRes{
		ActivationStatus: activationStatus,
		StatusUpdatedAt:  getMandateActionStatusResponse.GetActionUpdatedAt(),
	}

	return res, nil
}

func (e *EnachMandateCreationProcessor) getVendorActionStatusFromEnachActionStatus(status enachEnumsPb.EnachActionStatus) (DomainActivationStatus, error) {
	switch status {
	case enachEnumsPb.EnachActionStatus_ACTION_STATUS_SUCCESS:
		return DOMAIN_ACTIVATION_STATUS_SUCCESS, nil
	case enachEnumsPb.EnachActionStatus_ACTION_STATUS_FAILED:
		return DOMAIN_ACTIVATION_STATUS_FAILURE, nil
	case enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS:
		return DOMAIN_ACTIVATION_STATUS_INPROGRESS, nil
	default:
		return DOMAIN_ACTIVATION_STATUS_UNSPECIFIED, fmt.Errorf("mapping for the action status not found for status, %s", status)
	}
}

func (e *EnachMandateCreationProcessor) GetActivationCoolOff(ctx context.Context, vendor commonvgpb.Vendor) (*durationpb.Duration, error) {
	getActivationCoolOffRes, err := e.enachClient.GetActivationCoolOff(ctx, &enachPb.GetActivationCoolOffRequest{
		Vendor: vendor,
	})

	switch {
	case err != nil:
		return nil, fmt.Errorf("enachClient.GetActivationCoolOff rpc call failed : %w", err)
	case getActivationCoolOffRes.GetStatus().IsSuccess():
		return getActivationCoolOffRes.GetCoolOffDuration(), nil
	case getActivationCoolOffRes.GetStatus().IsInvalidArgument():
		return nil, epifierrors.ErrInvalidArgument
	default:
		return nil, fmt.Errorf("enachClient.GetActivationCoolOff rpc call failed : %w", rpc.StatusAsError(getActivationCoolOffRes.GetStatus()))
	}
}
