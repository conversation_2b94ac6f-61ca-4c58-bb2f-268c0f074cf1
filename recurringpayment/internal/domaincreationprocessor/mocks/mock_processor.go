// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/go/src/github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor/processor.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	domaincreationprocessor "github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor"
	gomock "github.com/golang/mock/gomock"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
)

// MockDomainCreationProcessorV1 is a mock of DomainCreationProcessorV1 interface.
type MockDomainCreationProcessorV1 struct {
	ctrl     *gomock.Controller
	recorder *MockDomainCreationProcessorV1MockRecorder
}

// MockDomainCreationProcessorV1MockRecorder is the mock recorder for MockDomainCreationProcessorV1.
type MockDomainCreationProcessorV1MockRecorder struct {
	mock *MockDomainCreationProcessorV1
}

// NewMockDomainCreationProcessorV1 creates a new mock instance.
func NewMockDomainCreationProcessorV1(ctrl *gomock.Controller) *MockDomainCreationProcessorV1 {
	mock := &MockDomainCreationProcessorV1{ctrl: ctrl}
	mock.recorder = &MockDomainCreationProcessorV1MockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDomainCreationProcessorV1) EXPECT() *MockDomainCreationProcessorV1MockRecorder {
	return m.recorder
}

// AuthorizeCreation mocks base method.
func (m *MockDomainCreationProcessorV1) AuthorizeCreation(ctx context.Context, req *domaincreationprocessor.AuthorizeCreationReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AuthorizeCreation", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// AuthorizeCreation indicates an expected call of AuthorizeCreation.
func (mr *MockDomainCreationProcessorV1MockRecorder) AuthorizeCreation(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AuthorizeCreation", reflect.TypeOf((*MockDomainCreationProcessorV1)(nil).AuthorizeCreation), ctx, req)
}

// CreateDomainEntities mocks base method.
func (m *MockDomainCreationProcessorV1) CreateDomainEntities(ctx context.Context, req *domaincreationprocessor.CreateDomainEntitiesReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateDomainEntities", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateDomainEntities indicates an expected call of CreateDomainEntities.
func (mr *MockDomainCreationProcessorV1MockRecorder) CreateDomainEntities(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDomainEntities", reflect.TypeOf((*MockDomainCreationProcessorV1)(nil).CreateDomainEntities), ctx, req)
}

// EnquireDomainActivationStatus mocks base method.
func (m *MockDomainCreationProcessorV1) EnquireDomainActivationStatus(ctx context.Context, req *domaincreationprocessor.EnquireDomainActivationStatusReq) (*domaincreationprocessor.EnquireDomainActivationStatusRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnquireDomainActivationStatus", ctx, req)
	ret0, _ := ret[0].(*domaincreationprocessor.EnquireDomainActivationStatusRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EnquireDomainActivationStatus indicates an expected call of EnquireDomainActivationStatus.
func (mr *MockDomainCreationProcessorV1MockRecorder) EnquireDomainActivationStatus(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnquireDomainActivationStatus", reflect.TypeOf((*MockDomainCreationProcessorV1)(nil).EnquireDomainActivationStatus), ctx, req)
}

// GetActivationCoolOff mocks base method.
func (m *MockDomainCreationProcessorV1) GetActivationCoolOff(ctx context.Context, vendor vendorgateway.Vendor) (*durationpb.Duration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActivationCoolOff", ctx, vendor)
	ret0, _ := ret[0].(*durationpb.Duration)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActivationCoolOff indicates an expected call of GetActivationCoolOff.
func (mr *MockDomainCreationProcessorV1MockRecorder) GetActivationCoolOff(ctx, vendor interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActivationCoolOff", reflect.TypeOf((*MockDomainCreationProcessorV1)(nil).GetActivationCoolOff), ctx, vendor)
}

// GetAuthorizationDeeplink mocks base method.
func (m *MockDomainCreationProcessorV1) GetAuthorizationDeeplink(ctx context.Context, recurringPaymentId string) (*deeplink.Deeplink, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuthorizationDeeplink", ctx, recurringPaymentId)
	ret0, _ := ret[0].(*deeplink.Deeplink)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuthorizationDeeplink indicates an expected call of GetAuthorizationDeeplink.
func (mr *MockDomainCreationProcessorV1MockRecorder) GetAuthorizationDeeplink(ctx, recurringPaymentId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthorizationDeeplink", reflect.TypeOf((*MockDomainCreationProcessorV1)(nil).GetAuthorizationDeeplink), ctx, recurringPaymentId)
}

// ValidateCreation mocks base method.
func (m *MockDomainCreationProcessorV1) ValidateCreation(ctx context.Context, req *domaincreationprocessor.ValidateCreationReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateCreation", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateCreation indicates an expected call of ValidateCreation.
func (mr *MockDomainCreationProcessorV1MockRecorder) ValidateCreation(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateCreation", reflect.TypeOf((*MockDomainCreationProcessorV1)(nil).ValidateCreation), ctx, req)
}
