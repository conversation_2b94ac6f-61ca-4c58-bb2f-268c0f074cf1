// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/go/src/github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor/factory.go

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	recurringpayment "github.com/epifi/gamma/api/recurringpayment"
	domaincreationprocessor "github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor"
	gomock "github.com/golang/mock/gomock"
)

// MockDomainCreationProcessorFactory is a mock of DomainCreationProcessorFactory interface.
type MockDomainCreationProcessorFactory struct {
	ctrl     *gomock.Controller
	recorder *MockDomainCreationProcessorFactoryMockRecorder
}

// MockDomainCreationProcessorFactoryMockRecorder is the mock recorder for MockDomainCreationProcessorFactory.
type MockDomainCreationProcessorFactoryMockRecorder struct {
	mock *MockDomainCreationProcessorFactory
}

// NewMockDomainCreationProcessorFactory creates a new mock instance.
func NewMockDomainCreationProcessorFactory(ctrl *gomock.Controller) *MockDomainCreationProcessorFactory {
	mock := &MockDomainCreationProcessorFactory{ctrl: ctrl}
	mock.recorder = &MockDomainCreationProcessorFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDomainCreationProcessorFactory) EXPECT() *MockDomainCreationProcessorFactoryMockRecorder {
	return m.recorder
}

// GetProcessor mocks base method.
func (m *MockDomainCreationProcessorFactory) GetProcessor(paymentType recurringpayment.RecurringPaymentType, paymentRoute recurringpayment.RecurringPaymentRoute) (domaincreationprocessor.DomainCreationProcessorV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProcessor", paymentType, paymentRoute)
	ret0, _ := ret[0].(domaincreationprocessor.DomainCreationProcessorV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProcessor indicates an expected call of GetProcessor.
func (mr *MockDomainCreationProcessorFactoryMockRecorder) GetProcessor(paymentType, paymentRoute interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProcessor", reflect.TypeOf((*MockDomainCreationProcessorFactory)(nil).GetProcessor), paymentType, paymentRoute)
}
