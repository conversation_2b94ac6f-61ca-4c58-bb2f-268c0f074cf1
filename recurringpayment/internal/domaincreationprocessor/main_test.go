package domaincreationprocessor

import (
	"testing"

	"github.com/golang/mock/gomock"

	mockPayClient "github.com/epifi/gamma/api/pay/mocks"
	mocks2 "github.com/epifi/gamma/api/recurringpayment/paymentgateway/mocks"
	"github.com/epifi/gamma/recurringpayment/dao/mocks"
)

type pgEmandateProcessorMockDependencies struct {
	mockRpaDao    *mocks.MockRecurringPaymentsActionDao
	mockRpDao     *mocks.MockRecurringPaymentDao
	mockPgClient  *mocks2.MockPaymentGatewayServiceClient
	mockPayClient *mockPayClient.MockPayClient
}

func getEmandateProcessorWithMocks(t *testing.T) (*PgEmandateProcessor, *pgEmandateProcessorMockDependencies, func()) {
	ctrl := gomock.NewController(t)
	mockRpaDao := mocks.NewMockRecurringPaymentsActionDao(ctrl)
	mockRpDao := mocks.NewMockRecurringPaymentDao(ctrl)
	mockPgClient := mocks2.NewMockPaymentGatewayServiceClient(ctrl)
	mockPaymentClient := mockPayClient.NewMockPayClient(ctrl)
	return &PgEmandateProcessor{
			pgClient:  mockPgClient,
			rpaDao:    mockRpaDao,
			rpDao:     mockRpDao,
			payClient: mockPaymentClient,
		}, &pgEmandateProcessorMockDependencies{
			mockRpaDao:    mockRpaDao,
			mockRpDao:     mockRpDao,
			mockPgClient:  mockPgClient,
			mockPayClient: mockPaymentClient,
		}, ctrl.Finish
}

type pgUpiProcessorMockDependencies struct {
	mockRpaDao    *mocks.MockRecurringPaymentsActionDao
	mockRpDao     *mocks.MockRecurringPaymentDao
	mockPgClient  *mocks2.MockPaymentGatewayServiceClient
	mockPayClient *mockPayClient.MockPayClient
}

func getUpiProcessorWithMocks(t *testing.T) (*PgUpiProcessor, *pgUpiProcessorMockDependencies, func()) {
	ctrl := gomock.NewController(t)
	mockRpaDao := mocks.NewMockRecurringPaymentsActionDao(ctrl)
	mockRpDao := mocks.NewMockRecurringPaymentDao(ctrl)
	mockPgClient := mocks2.NewMockPaymentGatewayServiceClient(ctrl)
	mockPaymentClient := mockPayClient.NewMockPayClient(ctrl)
	return &PgUpiProcessor{
			pgClient:  mockPgClient,
			rpaDao:    mockRpaDao,
			rpDao:     mockRpDao,
			payClient: mockPaymentClient,
		}, &pgUpiProcessorMockDependencies{
			mockRpaDao:    mockRpaDao,
			mockRpDao:     mockRpDao,
			mockPgClient:  mockPgClient,
			mockPayClient: mockPaymentClient,
		}, ctrl.Finish
}
