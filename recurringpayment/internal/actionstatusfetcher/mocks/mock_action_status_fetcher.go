// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/go/src/github.com/epifi/gamma/recurringpayment/internal/actionstatusfetcher/action_status_fetcher.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	recurringpayment "github.com/epifi/gamma/api/recurringpayment"
	actionstatusfetcher "github.com/epifi/gamma/recurringpayment/internal/actionstatusfetcher"
	gomock "github.com/golang/mock/gomock"
)

// MockIActionStatusFetcher is a mock of IActionStatusFetcher interface.
type MockIActionStatusFetcher struct {
	ctrl     *gomock.Controller
	recorder *MockIActionStatusFetcherMockRecorder
}

// MockIActionStatusFetcherMockRecorder is the mock recorder for MockIActionStatusFetcher.
type MockIActionStatusFetcherMockRecorder struct {
	mock *MockIActionStatusFetcher
}

// NewMockIActionStatusFetcher creates a new mock instance.
func NewMockIActionStatusFetcher(ctrl *gomock.Controller) *MockIActionStatusFetcher {
	mock := &MockIActionStatusFetcher{ctrl: ctrl}
	mock.recorder = &MockIActionStatusFetcherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIActionStatusFetcher) EXPECT() *MockIActionStatusFetcherMockRecorder {
	return m.recorder
}

// GetActionStatusAndNextStepDeeplink mocks base method.
func (m *MockIActionStatusFetcher) GetActionStatusAndNextStepDeeplink(ctx context.Context, recurringPayment *recurringpayment.RecurringPayment, action *recurringpayment.RecurringPaymentsAction, metaData *actionstatusfetcher.Metadata) (recurringpayment.ActionState, recurringpayment.ActionSubState, *deeplink.Deeplink, *recurringpayment.ActionDetailedStatusInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActionStatusAndNextStepDeeplink", ctx, recurringPayment, action, metaData)
	ret0, _ := ret[0].(recurringpayment.ActionState)
	ret1, _ := ret[1].(recurringpayment.ActionSubState)
	ret2, _ := ret[2].(*deeplink.Deeplink)
	ret3, _ := ret[3].(*recurringpayment.ActionDetailedStatusInfo)
	ret4, _ := ret[4].(error)
	return ret0, ret1, ret2, ret3, ret4
}

// GetActionStatusAndNextStepDeeplink indicates an expected call of GetActionStatusAndNextStepDeeplink.
func (mr *MockIActionStatusFetcherMockRecorder) GetActionStatusAndNextStepDeeplink(ctx, recurringPayment, action, metaData interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActionStatusAndNextStepDeeplink", reflect.TypeOf((*MockIActionStatusFetcher)(nil).GetActionStatusAndNextStepDeeplink), ctx, recurringPayment, action, metaData)
}
