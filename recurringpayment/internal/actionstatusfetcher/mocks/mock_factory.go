// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/go/src/github.com/epifi/gamma/recurringpayment/internal/actionstatusfetcher/factory.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	recurringpayment "github.com/epifi/gamma/api/recurringpayment"
	actionstatusfetcher "github.com/epifi/gamma/recurringpayment/internal/actionstatusfetcher"
	gomock "github.com/golang/mock/gomock"
)

// MockIActionStatusFetcherFactory is a mock of IActionStatusFetcherFactory interface.
type MockIActionStatusFetcherFactory struct {
	ctrl     *gomock.Controller
	recorder *MockIActionStatusFetcherFactoryMockRecorder
}

// MockIActionStatusFetcherFactoryMockRecorder is the mock recorder for MockIActionStatusFetcherFactory.
type MockIActionStatusFetcherFactoryMockRecorder struct {
	mock *MockIActionStatusFetcherFactory
}

// NewMockIActionStatusFetcherFactory creates a new mock instance.
func NewMockIActionStatusFetcherFactory(ctrl *gomock.Controller) *MockIActionStatusFetcherFactory {
	mock := &MockIActionStatusFetcherFactory{ctrl: ctrl}
	mock.recorder = &MockIActionStatusFetcherFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIActionStatusFetcherFactory) EXPECT() *MockIActionStatusFetcherFactoryMockRecorder {
	return m.recorder
}

// GetActionStatusFetcher mocks base method.
func (m *MockIActionStatusFetcherFactory) GetActionStatusFetcher(ctx context.Context, actionType recurringpayment.Action, recurringPaymentType recurringpayment.RecurringPaymentType, paymentRoute recurringpayment.RecurringPaymentRoute) actionstatusfetcher.IActionStatusFetcher {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActionStatusFetcher", ctx, actionType, recurringPaymentType)
	ret0, _ := ret[0].(actionstatusfetcher.IActionStatusFetcher)
	return ret0
}

// GetActionStatusFetcher indicates an expected call of GetActionStatusFetcher.
func (mr *MockIActionStatusFetcherFactoryMockRecorder) GetActionStatusFetcher(ctx, actionType, recurringPaymentType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActionStatusFetcher", reflect.TypeOf((*MockIActionStatusFetcherFactory)(nil).GetActionStatusFetcher), ctx, actionType, recurringPaymentType)
}
