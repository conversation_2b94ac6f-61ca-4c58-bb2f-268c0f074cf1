// nolint: dupl
package actionstatusfetcher

import (
	"github.com/epifi/be-common/pkg/epificontext"

	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	celestialWfStagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	"github.com/epifi/gamma/api/frontend/deeplink"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/pkg/pay"
	"github.com/epifi/gamma/recurringpayment/helper"
)

type DefaultCreationActionStatusFetcher struct {
	celestialClient celestialPb.CelestialClient
}

func NewDefaultCreationActionStatusFetcher(celestialClient celestialPb.CelestialClient) *DefaultCreationActionStatusFetcher {
	return &DefaultCreationActionStatusFetcher{celestialClient: celestialClient}
}

var _ IActionStatusFetcher = &DefaultCreationActionStatusFetcher{}

func (e *DefaultCreationActionStatusFetcher) GetActionStatusAndNextStepDeeplink(ctx context.Context, recurringPayment *rpPb.RecurringPayment, action *rpPb.RecurringPaymentsAction, pollingMetadata *Metadata) (rpPb.ActionState, rpPb.ActionSubState, *deeplink.Deeplink, *rpPb.ActionDetailedStatusInfo, error) {
	// handle terminal success and failure states here
	switch action.GetState() {
	case rpPb.ActionState_ACTION_FAILURE:
		return rpPb.ActionState_ACTION_FAILURE, rpPb.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, helper.GetRecurringPaymentActionHaultScreen(recurringPayment.GetUiEntryPoint(), action.GetClientRequestId(), action.GetActionMetadata().GetCreateActionMetadata().GetPostCreationDomainDeeplink()), nil, nil
	case rpPb.ActionState_ACTION_SUCCESS:
		return rpPb.ActionState_ACTION_SUCCESS, rpPb.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, helper.GetRecurringPaymentActionHaultScreen(recurringPayment.GetUiEntryPoint(), action.GetClientRequestId(), action.GetActionMetadata().GetCreateActionMetadata().GetPostCreationDomainDeeplink()), nil, nil
	default:
		logger.Debug(ctx, "action is not in terminal success/failure state so getting exact stage and status from workflow", zap.String(logger.ACTION_ID, action.GetId()), zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
	}

	// for in_progress cases, get the current stage and stage status from workflow to decide the action sub-status and deeplink to be returned
	workflowStatusRes, err := e.celestialClient.GetWorkflowStatus(ctx, &celestialPb.GetWorkflowStatusRequest{
		Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{ClientRequestId: &celestialPb.ClientReqId{Id: action.GetClientRequestId(), Client: workflowPb.Client_RECURRING_PAYMENT}},
		Ownership:  epificontext.OwnershipFromContext(ctx),
		UseCase:    pay.OwnershipToUseCaseForPay[epificontext.OwnershipFromContext(ctx)],
	})
	if rpcErr := epifigrpc.RPCError(workflowStatusRes, err); rpcErr != nil {
		return rpPb.ActionState_ACTION_STATE_UNSPECIFIED, rpPb.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, nil, nil, fmt.Errorf("celestialClient.GetWorkflowStatus rpc call failed, err : %w", rpcErr)
	}

	// get action status and next step deeplink based on workflow stage and status
	stage := celestialPkg.GetStageFromStageEnum(workflowStatusRes.GetWorkflowRequest().GetStageEnum())
	stageStatus := workflowStatusRes.GetWorkflowRequest().GetStatus()

	switch stage {
	case rpNs.CreateRecurringPaymentDomainEntityCreation:
		return rpPb.ActionState_ACTION_IN_PROGRESS, rpPb.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, getPollingOrHaultScreenDeeplink(action.GetClientRequestId(), 3*time.Second, recurringPayment.GetUiEntryPoint(), pollingMetadata), nil, nil
	case rpNs.CreateRecurringPaymentDomainActivation:
		return e.handleDomainActivationStageStatus(ctx, recurringPayment, action, stageStatus, pollingMetadata)
	case rpNs.CreateRecurringPaymentActivationCoolOff:
		return rpPb.ActionState_ACTION_IN_PROGRESS, rpPb.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, helper.GetRecurringPaymentActionHaultScreen(recurringPayment.GetUiEntryPoint(), action.GetClientRequestId(), nil), nil, nil
	case rpNs.CreateRecurringPaymentAuthorisation:
		return e.handleAuthorizationStageStatus(ctx, recurringPayment, action, stageStatus, pollingMetadata)
	default:
		logger.Error(ctx, "unhandled creation stage", zap.String(logger.ACTION_ID, action.GetId()), zap.String(logger.STAGE, string(stage)))
		// returning the polling screen as the stage might not be initialise yet as creation of a workflow is an async flow.
		return rpPb.ActionState_ACTION_IN_PROGRESS, rpPb.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, getPollingOrHaultScreenDeeplink(action.GetClientRequestId(), 3*time.Second, recurringPayment.GetUiEntryPoint(), pollingMetadata), nil, nil
	}
}

func (e *DefaultCreationActionStatusFetcher) handleDomainActivationStageStatus(_ context.Context, recurringPayment *rpPb.RecurringPayment, action *rpPb.RecurringPaymentsAction, stageStatus celestialWfStagePb.Status, pollingMetadata *Metadata) (rpPb.ActionState, rpPb.ActionSubState, *deeplink.Deeplink, *rpPb.ActionDetailedStatusInfo, error) {
	switch stageStatus {
	case celestialWfStagePb.Status_INITIATED:
		return rpPb.ActionState_ACTION_IN_PROGRESS, rpPb.ActionSubState_ACTION_SUB_STATE_AUTHORIZATION_COMPLETED, helper.GetRecurringPaymentActionHaultScreen(recurringPayment.GetUiEntryPoint(), action.GetClientRequestId(), action.GetActionMetadata().GetCreateActionMetadata().GetPostCreationDomainDeeplink()), nil, nil
	default:
		return rpPb.ActionState_ACTION_IN_PROGRESS, rpPb.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, getPollingOrHaultScreenDeeplink(action.GetClientRequestId(), 3*time.Second, recurringPayment.GetUiEntryPoint(), pollingMetadata), nil, nil
	}
}

func (e *DefaultCreationActionStatusFetcher) handleAuthorizationStageStatus(_ context.Context, recurringPayment *rpPb.RecurringPayment, action *rpPb.RecurringPaymentsAction, stageStatus celestialWfStagePb.Status, pollingMetadata *Metadata) (rpPb.ActionState, rpPb.ActionSubState, *deeplink.Deeplink, *rpPb.ActionDetailedStatusInfo, error) {
	switch stageStatus {
	// denotes that creation is blocked on authorization, so initiate authorization deeplink need to be returned
	case celestialWfStagePb.Status_BLOCKED:
		return rpPb.ActionState_ACTION_IN_PROGRESS, rpPb.ActionSubState_ACTION_SUB_STATE_BLOCKED_ON_AUTHORIZATION, getInitiateAuthorizationDeeplink(action.GetClientRequestId()), nil, nil
	// after auth init, if the user comes back to the polling screen, it will indicate a drop-off. So sending auth init  screen again
	case celestialWfStagePb.Status_PENDING:
		return rpPb.ActionState_ACTION_IN_PROGRESS, rpPb.ActionSubState_ACTION_SUB_STATE_AUTHORIZATION_INITIATED, helper.GetRecurringPaymentActionHaultScreen(recurringPayment.GetUiEntryPoint(), action.GetClientRequestId(), getInitiateAuthorizationDeeplink(action.GetClientRequestId())), nil, nil
	case celestialWfStagePb.Status_TIMEOUT, celestialWfStagePb.Status_FAILED:
		return rpPb.ActionState_ACTION_FAILURE, rpPb.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, helper.GetRecurringPaymentActionHaultScreen(recurringPayment.GetUiEntryPoint(), action.GetClientRequestId(), action.GetActionMetadata().GetCreateActionMetadata().GetPostCreationDomainDeeplink()), nil, nil
	default:
		return rpPb.ActionState_ACTION_IN_PROGRESS, rpPb.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, getPollingOrHaultScreenDeeplink(action.GetClientRequestId(), 1*time.Second, recurringPayment.GetUiEntryPoint(), pollingMetadata), nil, nil
	}
}
