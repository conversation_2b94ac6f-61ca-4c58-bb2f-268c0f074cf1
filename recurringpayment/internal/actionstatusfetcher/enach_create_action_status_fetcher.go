package actionstatusfetcher

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	celestialWfStagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	"github.com/epifi/gamma/api/frontend/deeplink"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	rpScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/recurringpayment"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/recurringpayment/helper"
)

type EnachCreateActionStatusFetcher struct {
	enachClient     enachPb.EnachServiceClient
	celestialClient celestialPb.CelestialClient
}

func NewEnachCreateActionStatusFetcher(
	enachClient enachPb.EnachServiceClient,
	celestialClient celestialPb.CelestialClient,
) *EnachCreateActionStatusFetcher {
	return &EnachCreateActionStatusFetcher{
		enachClient:     enachClient,
		celestialClient: celestialClient,
	}
}

// compile time check to make sure EnachCreateActionStatusFetcher implements IActionStatusFetcher
var _ IActionStatusFetcher = &EnachCreateActionStatusFetcher{}
var pollAttemptThresholdInEnachCreationFlow = 5

func (e *EnachCreateActionStatusFetcher) GetActionStatusAndNextStepDeeplink(ctx context.Context, recurringPayment *rpPb.RecurringPayment, action *rpPb.RecurringPaymentsAction, pollingMetadata *Metadata) (rpPb.ActionState, rpPb.ActionSubState, *deeplink.Deeplink, *rpPb.ActionDetailedStatusInfo, error) {
	// handle terminal success and failure states here
	switch action.GetState() {
	case rpPb.ActionState_ACTION_FAILURE:
		return rpPb.ActionState_ACTION_FAILURE, rpPb.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, helper.GetRecurringPaymentActionHaultScreen(recurringPayment.GetUiEntryPoint(), action.GetClientRequestId(), action.GetActionMetadata().GetCreateActionMetadata().GetPostCreationDomainDeeplink()), nil, nil
	case rpPb.ActionState_ACTION_SUCCESS:
		return rpPb.ActionState_ACTION_SUCCESS, rpPb.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, helper.GetRecurringPaymentActionHaultScreen(recurringPayment.GetUiEntryPoint(), action.GetClientRequestId(), action.GetActionMetadata().GetCreateActionMetadata().GetPostCreationDomainDeeplink()), nil, nil
	default:
		logger.Debug(ctx, "action is not in terminal success/failure state so getting exact stage and status from workflow", zap.String(logger.ACTION_ID, action.GetId()), zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
	}

	// for in_progress cases, get the current stage and stage status from workflow to decide the action sub-status and deeplink to be returned
	workflowStatusRes, err := e.celestialClient.GetWorkflowStatus(ctx, &celestialPb.GetWorkflowStatusRequest{
		Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{ClientRequestId: &celestialPb.ClientReqId{Id: action.GetClientRequestId(), Client: workflowPb.Client_RECURRING_PAYMENT}},
		Ownership:  commontypes.Ownership_EPIFI_TECH,
	})
	if rpcErr := epifigrpc.RPCError(workflowStatusRes, err); rpcErr != nil {
		return rpPb.ActionState_ACTION_STATE_UNSPECIFIED, rpPb.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, nil, nil, fmt.Errorf("celestialClient.GetWorkflowStatus rpc call failed, err : %w", rpcErr)
	}

	// get action status and next step deeplink based on workflow stage and status
	stage := celestialPkg.GetStageFromStageEnum(workflowStatusRes.GetWorkflowRequest().GetStageEnum())
	stageStatus := workflowStatusRes.GetWorkflowRequest().GetStatus()

	switch stage {
	case rpNs.CreateRecurringPaymentDomainEntityCreation:
		return rpPb.ActionState_ACTION_IN_PROGRESS, rpPb.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, getPollingOrHaultScreenDeeplink(action.GetClientRequestId(), 3*time.Second, recurringPayment.GetUiEntryPoint(), pollingMetadata), nil, nil
	case rpNs.CreateRecurringPaymentDomainActivation:
		return e.handleDomainActivationStageStatus(ctx, recurringPayment, action, stageStatus, pollingMetadata)
	case rpNs.CreateRecurringPaymentActivationCoolOff:
		return e.handleActivationCoolOffStageStatus(ctx, recurringPayment, action, stageStatus, pollingMetadata)
	case rpNs.CreateRecurringPaymentAuthorisation:
		return e.handleAuthorizationStageStatus(ctx, recurringPayment, action, stageStatus, pollingMetadata)
	default:
		logger.Error(ctx, "unhandled enach creation stage", zap.String(logger.ACTION_ID, action.GetId()), zap.String(logger.STAGE, string(stage)))
		// returning the polling screen as the stage might not be initialise yet as creation of a workflow is an async flow.
		return rpPb.ActionState_ACTION_IN_PROGRESS, rpPb.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, getPollingOrHaultScreenDeeplink(action.GetClientRequestId(), 3*time.Second, recurringPayment.GetUiEntryPoint(), pollingMetadata), nil, nil
	}
}

func (e *EnachCreateActionStatusFetcher) handleAuthorizationStageStatus(_ context.Context, recurringPayment *rpPb.RecurringPayment, action *rpPb.RecurringPaymentsAction, stageStatus celestialWfStagePb.Status, pollingMetadata *Metadata) (rpPb.ActionState, rpPb.ActionSubState, *deeplink.Deeplink, *rpPb.ActionDetailedStatusInfo, error) {
	switch stageStatus {
	// denotes that enach creation is blocked on authorization, so initiate authorization deeplink need to be returned
	case celestialWfStagePb.Status_BLOCKED:
		return rpPb.ActionState_ACTION_IN_PROGRESS, rpPb.ActionSubState_ACTION_SUB_STATE_BLOCKED_ON_AUTHORIZATION, getInitiateAuthorizationDeeplink(action.GetClientRequestId()), nil, nil
	// denotes that authorization was already initiated, but authorization callback/feedback is pending from vendor/Fi app, so in_progress halt deeplink should be returned
	case celestialWfStagePb.Status_PENDING:
		// enquiry of action status even after auth has been initiated indicates a drop off. So redirecting the user to the auth init screen.
		// Otherwise, a nil deeplink will be sent leading to some breakage in flow.
		return rpPb.ActionState_ACTION_IN_PROGRESS, rpPb.ActionSubState_ACTION_SUB_STATE_AUTHORIZATION_INITIATED, helper.GetRecurringPaymentActionHaultScreen(recurringPayment.GetUiEntryPoint(), action.GetClientRequestId(), getInitiateAuthorizationDeeplink(action.GetClientRequestId())), nil, nil
	case celestialWfStagePb.Status_TIMEOUT, celestialWfStagePb.Status_FAILED:
		return rpPb.ActionState_ACTION_FAILURE, rpPb.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, helper.GetRecurringPaymentActionHaultScreen(recurringPayment.GetUiEntryPoint(), action.GetClientRequestId(), action.GetActionMetadata().GetCreateActionMetadata().GetPostCreationDomainDeeplink()), nil, nil
	default:
		return rpPb.ActionState_ACTION_IN_PROGRESS, rpPb.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, getPollingOrHaultScreenDeeplink(action.GetClientRequestId(), 1*time.Second, recurringPayment.GetUiEntryPoint(), pollingMetadata), nil, nil
	}
}

// nolint:dupl
func (e *EnachCreateActionStatusFetcher) handleDomainActivationStageStatus(_ context.Context, recurringPayment *rpPb.RecurringPayment, action *rpPb.RecurringPaymentsAction, stageStatus celestialWfStagePb.Status, pollingMetadata *Metadata) (rpPb.ActionState, rpPb.ActionSubState, *deeplink.Deeplink, *rpPb.ActionDetailedStatusInfo, error) {
	switch stageStatus {
	case celestialWfStagePb.Status_INITIATED:
		return rpPb.ActionState_ACTION_IN_PROGRESS, rpPb.ActionSubState_ACTION_SUB_STATE_AUTHORIZATION_COMPLETED, helper.GetRecurringPaymentActionHaultScreen(recurringPayment.GetUiEntryPoint(), action.GetClientRequestId(), action.GetActionMetadata().GetCreateActionMetadata().GetPostCreationDomainDeeplink()), nil, nil
	default:
		return rpPb.ActionState_ACTION_IN_PROGRESS, rpPb.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, getPollingOrHaultScreenDeeplink(action.GetClientRequestId(), 3*time.Second, recurringPayment.GetUiEntryPoint(), pollingMetadata), nil, nil
	}
}

// nolint:dupl
// Although this code is currently similar to another handler, it is kept segregated for maintainability,
// as the logic is expected to diverge in the future.
func (e *EnachCreateActionStatusFetcher) handleActivationCoolOffStageStatus(_ context.Context, recurringPayment *rpPb.RecurringPayment, action *rpPb.RecurringPaymentsAction, stageStatus celestialWfStagePb.Status, pollingMetadata *Metadata) (rpPb.ActionState, rpPb.ActionSubState, *deeplink.Deeplink, *rpPb.ActionDetailedStatusInfo, error) {
	switch stageStatus {
	case celestialWfStagePb.Status_INITIATED:
		return rpPb.ActionState_ACTION_IN_PROGRESS, rpPb.ActionSubState_ACTION_SUB_STATE_AUTHORIZATION_COMPLETED, helper.GetRecurringPaymentActionHaultScreen(
			recurringPayment.GetUiEntryPoint(),
			action.GetClientRequestId(),
			action.GetActionMetadata().GetCreateActionMetadata().GetPostCreationDomainDeeplink(),
		), nil, nil
	default:
		return rpPb.ActionState_ACTION_IN_PROGRESS, rpPb.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, getPollingOrHaultScreenDeeplink(
			action.GetClientRequestId(),
			3*time.Second,
			recurringPayment.GetUiEntryPoint(),
			pollingMetadata,
		), nil, nil
	}
}

func getInitiateAuthorizationDeeplink(clientRequestId string) *deeplink.Deeplink {
	screenOptions, _ := deeplinkV3.GetScreenOptionV2(&rpScreenOptionsPb.InitiateRecurringPaymentCreationAuthorisationScreenOptions{
		ClientRequestId: clientRequestId,
	})
	return &deeplink.Deeplink{
		Screen:          deeplink.Screen_RECURRING_PAYMENT_INITIATE_CREATION_AUTHORISATION,
		ScreenOptionsV2: screenOptions,
	}
}

// getPollingOrHaultScreenDeeplink returns the respective deeplink(polling or hault) to the client based upon the continous number of polls done on the polling screen
func getPollingOrHaultScreenDeeplink(clientReqId string, pollDelay time.Duration, uiEntryPoint rpPb.UIEntryPoint, pollingMetadata *Metadata) *deeplink.Deeplink {
	if pollingMetadata == nil {
		pollingMetadata = &Metadata{}
	}
	// if number of continous poll attemps crosses the threshold, returns the hault screen to the client
	if pollingMetadata.PollAttempt > int32(pollAttemptThresholdInEnachCreationFlow) {
		return helper.GetRecurringPaymentActionHaultScreen(uiEntryPoint, clientReqId, nil)
	}
	return helper.GetRecurringPaymentActionPollingScreenDeeplink(clientReqId, pollDelay, pollingMetadata.PollAttempt)
}
