// nolint: dupl
package actionstatusfetcher

type PgEmandateCreationActionStatusFetcher struct {
	*DefaultCreationActionStatusFetcher
}

func NewPgEmandateCreationActionStatusFetcher(defaultCreationActionStatusFetcher *DefaultCreationActionStatusFetcher) *PgEmandateCreationActionStatusFetcher {
	return &PgEmandateCreationActionStatusFetcher{
		defaultCreationActionStatusFetcher,
	}
}

type PgEmandateExecutionActionStatusFetcher struct {
	*DefaultExecutionActionStatusFetcher
}

func NewPgEmandateExecutionActionStatusFetcher(defaultEmandateExecutionActionStatusFetcher *DefaultExecutionActionStatusFetcher) *PgEmandateExecutionActionStatusFetcher {
	return &PgEmandateExecutionActionStatusFetcher{
		defaultEmandateExecutionActionStatusFetcher,
	}
}

type PgUpiCreationActionStatusFetcher struct {
	*DefaultCreationActionStatusFetcher
}

func NewPgUpiCreationActionStatusFetcher(defaultCreationActionStatusFetcher *DefaultCreationActionStatusFetcher) *PgUpiCreationActionStatusFetcher {
	return &PgUpiCreationActionStatusFetcher{
		defaultCreationActionStatusFetcher,
	}
}

type PgUpiExecutionActionStatusFetcher struct {
	*DefaultExecutionActionStatusFetcher
}

func NewPgUpiExecutionActionStatusFetcher(defaultExecutionActionStatusFetcher *DefaultExecutionActionStatusFetcher) *PgUpiExecutionActionStatusFetcher {
	return &PgUpiExecutionActionStatusFetcher{
		defaultExecutionActionStatusFetcher,
	}
}
