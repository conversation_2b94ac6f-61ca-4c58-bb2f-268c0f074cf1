package actionstatusfetcher

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	celestialPb "github.com/epifi/be-common/api/celestial"
	celestialMocks "github.com/epifi/be-common/api/celestial/mocks"
	"github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/cfg"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/frontend/deeplink"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
)

func TestEnachCreateActionStatusFetcher_GetActionStatusAndNextStepDeeplink(t *testing.T) {
	t.Parallel()
	logger.Init(cfg.TestEnv)

	type args struct {
		recurringPayment *rpPb.RecurringPayment
		action           *rpPb.RecurringPaymentsAction
		pollingMetadata  *Metadata
	}
	type want struct {
		state          rpPb.ActionState
		sub            rpPb.ActionSubState
		expectedScreen deeplink.Screen
	}

	type mockWorkflowStatus struct {
		enabled  bool
		request  *celestialPb.GetWorkflowStatusRequest // can be nil for gomock.Any()
		wantResp *celestialPb.GetWorkflowStatusResponse
		wantErr  error
	}

	type testCase struct {
		name               string
		args               args
		want               want
		mockWorkflowStatus mockWorkflowStatus
		wantErr            bool
	}

	tests := []testCase{
		{
			name: "should return PostCreationDomainDeeplink if the workflow is in DomainActivationCooloff stage",
			args: args{
				recurringPayment: &rpPb.RecurringPayment{Id: "recurring-id", UiEntryPoint: rpPb.UIEntryPoint_UI_ENTRY_POINT_UNSPECIFIED},
				action: &rpPb.RecurringPaymentsAction{
					Id:              "action-id",
					ClientRequestId: "test-client-id",
					State:           rpPb.ActionState_ACTION_IN_PROGRESS,
					ActionMetadata: &rpPb.ActionMetadata{
						CreateActionMetadata: &rpPb.CreateActionMetadata{
							PostCreationDomainDeeplink: &deeplink.Deeplink{
								Screen: deeplink.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN,
							},
						},
					},
				},
				pollingMetadata: &Metadata{},
			},
			want: want{
				state:          rpPb.ActionState_ACTION_IN_PROGRESS,
				sub:            rpPb.ActionSubState_ACTION_SUB_STATE_AUTHORIZATION_COMPLETED,
				expectedScreen: deeplink.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN,
			},
			mockWorkflowStatus: mockWorkflowStatus{
				enabled: true,
				request: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: &celestialPb.ClientReqId{
							Id:     "test-client-id",
							Client: workflow.Client_RECURRING_PAYMENT,
						},
					},
					Ownership: common.Ownership_EPIFI_TECH,
				},
				wantResp: &celestialPb.GetWorkflowStatusResponse{
					Status: rpc.StatusOk(),
					WorkflowRequest: &celestialPb.WorkflowRequest{
						StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
						Status:    stage.Status_INITIATED,
					},
				},
				wantErr: nil,
			},
			wantErr: false,
		},
		{
			name: "should early return status as success if the RPA is already in SUCCESS state along with the deeplink",
			args: args{
				recurringPayment: &rpPb.RecurringPayment{Id: "recurring-id", UiEntryPoint: rpPb.UIEntryPoint_UI_ENTRY_POINT_UNSPECIFIED},
				action: &rpPb.RecurringPaymentsAction{
					Id:              "action-id",
					ClientRequestId: "test-client-id",
					State:           rpPb.ActionState_ACTION_SUCCESS,
					ActionMetadata: &rpPb.ActionMetadata{
						CreateActionMetadata: &rpPb.CreateActionMetadata{
							PostCreationDomainDeeplink: &deeplink.Deeplink{
								Screen: deeplink.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN,
							},
						},
					},
				},
				pollingMetadata: &Metadata{},
			},
			want: want{
				state:          rpPb.ActionState_ACTION_SUCCESS,
				sub:            rpPb.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED,
				expectedScreen: deeplink.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN,
			},
			mockWorkflowStatus: mockWorkflowStatus{
				enabled: false, // Should not be called
			},
			wantErr: false,
		},
		{
			name: "should return polling screen if in ActivationCoolOff stage with non-INITIATED or non-TERMINAL ( success or failure ) status",
			args: args{
				recurringPayment: &rpPb.RecurringPayment{Id: "recurring-id", UiEntryPoint: rpPb.UIEntryPoint_UI_ENTRY_POINT_UNSPECIFIED},
				action: &rpPb.RecurringPaymentsAction{
					Id:              "action-id",
					ClientRequestId: "test-client-id",
					State:           rpPb.ActionState_ACTION_CREATED,
					ActionMetadata: &rpPb.ActionMetadata{
						CreateActionMetadata: &rpPb.CreateActionMetadata{
							PostCreationDomainDeeplink: &deeplink.Deeplink{
								Screen: deeplink.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN,
							},
						},
					},
				},
				pollingMetadata: &Metadata{PollAttempt: 2},
			},
			want: want{
				state:          rpPb.ActionState_ACTION_IN_PROGRESS,
				sub:            rpPb.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED,
				expectedScreen: deeplink.Screen_RECURRING_PAYMENT_POLLING_SCREEN,
			},
			mockWorkflowStatus: mockWorkflowStatus{
				enabled: true,
				request: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: &celestialPb.ClientReqId{
							Id:     "test-client-id",
							Client: workflow.Client_RECURRING_PAYMENT,
						},
					},
					Ownership: common.Ownership_EPIFI_TECH,
				},
				wantResp: &celestialPb.GetWorkflowStatusResponse{
					Status: rpc.StatusOk(),
					WorkflowRequest: &celestialPb.WorkflowRequest{
						StageEnum: celestialPkg.GetStageEnumFromStage(rpNs.CreateRecurringPaymentActivationCoolOff),
						Status:    stage.Status_PENDING, // not INITIATED
					},
				},
				wantErr: nil,
			},
			wantErr: false,
		},
		{
			name: "should return error and unspecified state when getWorkflowStatus rpc fails",
			args: args{
				recurringPayment: &rpPb.RecurringPayment{Id: "recurring-id", UiEntryPoint: rpPb.UIEntryPoint_UI_ENTRY_POINT_UNSPECIFIED},
				action: &rpPb.RecurringPaymentsAction{
					Id:              "action-id",
					ClientRequestId: "test-client-id",
					State:           rpPb.ActionState_ACTION_IN_PROGRESS,
					ActionMetadata: &rpPb.ActionMetadata{
						CreateActionMetadata: &rpPb.CreateActionMetadata{
							PostCreationDomainDeeplink: &deeplink.Deeplink{
								Screen: deeplink.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN,
							},
						},
					},
				},
				pollingMetadata: &Metadata{},
			},
			want: want{
				state:          rpPb.ActionState_ACTION_STATE_UNSPECIFIED,
				sub:            rpPb.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED,
				expectedScreen: 0, // not used, since deeplink is nil
			},
			mockWorkflowStatus: mockWorkflowStatus{
				enabled: true,
				request: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: &celestialPb.ClientReqId{
							Id:     "test-client-id",
							Client: workflow.Client_RECURRING_PAYMENT,
						},
					},
					Ownership: common.Ownership_EPIFI_TECH,
				},
				wantResp: nil,
				wantErr:  assert.AnError,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockCelestial := celestialMocks.NewMockCelestialClient(ctrl)
			fetcher := &EnachCreateActionStatusFetcher{
				celestialClient: mockCelestial,
			}

			if tt.mockWorkflowStatus.enabled {
				mockCelestial.EXPECT().GetWorkflowStatus(gomock.Any(), tt.mockWorkflowStatus.request).
					Return(tt.mockWorkflowStatus.wantResp, tt.mockWorkflowStatus.wantErr)
			}

			state, sub, deeplink, _, err := fetcher.GetActionStatusAndNextStepDeeplink(
				context.Background(), tt.args.recurringPayment, tt.args.action, tt.args.pollingMetadata,
			)
			if tt.wantErr {
				if err == nil {
					t.Errorf("GetActionStatusAndNextStepDeeplink() expected error, got nil")
				}
				return
			}
			if err != nil {
				t.Errorf("GetActionStatusAndNextStepDeeplink() unexpected error: %v", err)
				return
			}
			assert.Equal(t, tt.want.state, state)
			assert.Equal(t, tt.want.sub, sub)
			assert.Equal(t, tt.want.expectedScreen, deeplink.Screen)
		})
	}
}
