//go:generate mockgen -source=$PWD/factory.go -destination=$PWD/mocks/mock_factory.go -package=mocks
package actionstatusfetcher

import (
	"context"

	"github.com/google/wire"

	pb "github.com/epifi/gamma/api/recurringpayment"
)

var FactoryWireSet = wire.NewSet(NewFactory, wire.Bind(new(IActionStatusFetcherFactory), new(*Factory)))

type IActionStatusFetcherFactory interface {
	GetActionStatusFetcher(ctx context.Context, actionType pb.Action, recurringPaymentType pb.RecurringPaymentType, paymentRoute pb.RecurringPaymentRoute) IActionStatusFetcher
}

type Factory struct {
	enachCreateActionStatusFetcher         *EnachCreateActionStatusFetcher
	enachExecuteActionStatusFetcher        *EnachExecuteActionStatusFetcher
	defaultCreateActionStatusFetcher       *DefaultCreationActionStatusFetcher
	defaultExecuteActionStatusFetcher      *DefaultExecutionActionStatusFetcher
	pgEmandateExecutionActionStatusFetcher *PgEmandateExecutionActionStatusFetcher
	pgUpiCreationActionStatusFetcher       *PgUpiCreationActionStatusFetcher
	pgUpiExecutionActionStatusFetcher      *PgUpiExecutionActionStatusFetcher
	pgEmandateCreationActionStatusFetcher  *PgEmandateCreationActionStatusFetcher
}

func NewFactory(
	enachCreateActionStatusFetcher *EnachCreateActionStatusFetcher,
	enachExecuteActionStatusFetcher *EnachExecuteActionStatusFetcher,
	defaultCreateActionStatusFetcher *DefaultCreationActionStatusFetcher,
	defaultExecuteActionStatusFetcher *DefaultExecutionActionStatusFetcher,
	pgEmandateExecutionActionStatusFetcher *PgEmandateExecutionActionStatusFetcher,
	pgUpiCreationActionStatusFetcher *PgUpiCreationActionStatusFetcher,
	pgUpiExecutionActionStatusFetcher *PgUpiExecutionActionStatusFetcher,
	pgEmandateCreationActionStatusFetcher *PgEmandateCreationActionStatusFetcher,
) *Factory {
	return &Factory{
		enachCreateActionStatusFetcher:         enachCreateActionStatusFetcher,
		enachExecuteActionStatusFetcher:        enachExecuteActionStatusFetcher,
		defaultCreateActionStatusFetcher:       defaultCreateActionStatusFetcher,
		defaultExecuteActionStatusFetcher:      defaultExecuteActionStatusFetcher,
		pgEmandateExecutionActionStatusFetcher: pgEmandateExecutionActionStatusFetcher,
		pgUpiCreationActionStatusFetcher:       pgUpiCreationActionStatusFetcher,
		pgUpiExecutionActionStatusFetcher:      pgUpiExecutionActionStatusFetcher,
		pgEmandateCreationActionStatusFetcher:  pgEmandateCreationActionStatusFetcher,
	}
}

// compile time check to make sure Factory implements IActionStatusFetcherFactory
var _ IActionStatusFetcherFactory = &Factory{}

func (f *Factory) GetActionStatusFetcher(ctx context.Context, actionType pb.Action, recurringPaymentType pb.RecurringPaymentType, paymentRoute pb.RecurringPaymentRoute) IActionStatusFetcher {
	switch paymentRoute {
	case pb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_EXTERNAL:
		return f.getExternalActionStatusFetcher(actionType, recurringPaymentType)
	case pb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE:
		return f.getNativeActionStatusFetcher(actionType, recurringPaymentType)
	default:
		// defaulting to native route handling for backward compatibility
		return f.getNativeActionStatusFetcher(actionType, recurringPaymentType)
	}
}

func (f *Factory) getExternalActionStatusFetcher(actionType pb.Action, recurringPaymentType pb.RecurringPaymentType) IActionStatusFetcher {
	switch actionType {
	case pb.Action_CREATE:
		switch recurringPaymentType {
		case pb.RecurringPaymentType_ENACH_MANDATES:
			return f.pgEmandateCreationActionStatusFetcher
		case pb.RecurringPaymentType_UPI_MANDATES:
			return f.pgUpiCreationActionStatusFetcher
		default:
			return f.defaultCreateActionStatusFetcher
		}
	case pb.Action_EXECUTE:
		switch recurringPaymentType {
		case pb.RecurringPaymentType_ENACH_MANDATES:
			return f.pgEmandateExecutionActionStatusFetcher
		case pb.RecurringPaymentType_UPI_MANDATES:
			return f.pgUpiExecutionActionStatusFetcher
		default:
			return f.defaultExecuteActionStatusFetcher
		}
	default:
		return nil
	}
}

func (f *Factory) getNativeActionStatusFetcher(actionType pb.Action, recurringPaymentType pb.RecurringPaymentType) IActionStatusFetcher {
	switch {
	case actionType == pb.Action_CREATE && recurringPaymentType == pb.RecurringPaymentType_ENACH_MANDATES:
		return f.enachCreateActionStatusFetcher
	case actionType == pb.Action_EXECUTE && recurringPaymentType == pb.RecurringPaymentType_ENACH_MANDATES:
		return f.enachExecuteActionStatusFetcher
	default:
		return nil
	}
}
