// nolint: dupl
package actionstatusfetcher

import (
	"context"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/recurringpayment"
)

type DefaultExecutionActionStatusFetcher struct{}

var _ IActionStatusFetcher = &DefaultExecutionActionStatusFetcher{}

func NewDefaultExecutionActionStatusFetcher() *DefaultExecutionActionStatusFetcher {
	return &DefaultExecutionActionStatusFetcher{}
}

func (d *DefaultExecutionActionStatusFetcher) GetActionStatusAndNextStepDeeplink(ctx context.Context, recurringPayment *recurringpayment.RecurringPayment, action *recurringpayment.RecurringPaymentsAction, metaData *Metadata) (recurringpayment.ActionState, recurringpayment.ActionSubState, *deeplink.Deeplink, *recurringpayment.ActionDetailedStatusInfo, error) {
	return action.GetState(), recurringpayment.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, nil, action.GetActionDetailedStatusInfo(), nil
}
