//go:generate mockgen -source=$PWD/action_status_fetcher.go -destination=$PWD/mocks/mock_action_status_fetcher.go -package=mocks
package actionstatusfetcher

import (
	"context"

	"github.com/google/wire"

	"github.com/epifi/gamma/api/frontend/deeplink"
	pb "github.com/epifi/gamma/api/recurringpayment"
)

var AllActionStatusFetchersWireSet = wire.NewSet(
	NewEnachCreateActionStatusFetcher,
	NewEnachExecuteActionStatusFetcher,
	NewDefaultCreationActionStatusFetcher,
	NewDefaultExecutionActionStatusFetcher,
	NewPgEmandateCreationActionStatusFetcher,
	NewPgEmandateExecutionActionStatusFetcher,
	NewPgUpiExecutionActionStatusFetcher,
	NewPgUpiCreationActionStatusFetcher,
)

type IActionStatusFetcher interface {
	GetActionStatusAndNextStepDeeplink(ctx context.Context, recurringPayment *pb.RecurringPayment, action *pb.RecurringPaymentsAction, metaData *Metadata) (pb.ActionState, pb.ActionSubState, *deeplink.Deeplink, *pb.ActionDetailedStatusInfo, error)
}

// Metadata is useful to modify the response fetched from the backend
// for e.g. based upon the number of continous polls done on the polling screen, hault screen will be returned
type Metadata struct {
	PollAttempt int32
}
