package actionstatusfetcher

import (
	"context"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/recurringpayment"
)

type EnachExecuteActionStatusFetcher struct{}

func NewEnachExecuteActionStatusFetcher() *EnachExecuteActionStatusFetcher {
	return &EnachExecuteActionStatusFetcher{}
}

// compile time check to make sure EnachExecuteActionStatusFetcher implements IActionStatusFetcher
var _ IActionStatusFetcher = &EnachExecuteActionStatusFetcher{}

func (e *EnachExecuteActionStatusFetcher) GetActionStatusAndNextStepDeeplink(ctx context.Context, recurringPayment *recurringpayment.RecurringPayment, action *recurringpayment.RecurringPaymentsAction, metaData *Metadata) (recurringpayment.ActionState, recurringpayment.ActionSubState, *deeplink.Deeplink, *recurringpayment.ActionDetailedStatusInfo, error) {
	return action.GetState(), recurringpayment.ActionSubState_ACTION_SUB_STATE_UNSPECIFIED, nil, action.GetActionDetailedStatusInfo(), nil
}
