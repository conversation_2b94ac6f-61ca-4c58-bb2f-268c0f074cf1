package domainexecutionprocessor

import (
	"os"
	"testing"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/golang/mock/gomock"

	mocks2 "github.com/epifi/gamma/api/recurringpayment/paymentgateway/mocks"
	"github.com/epifi/gamma/recurringpayment/dao/mocks"
)

type pgEmandateProcessorMockDependencies struct {
	mockRpaDao   *mocks.MockRecurringPaymentsActionDao
	mockRpDao    *mocks.MockRecurringPaymentDao
	mockPgClient *mocks2.MockPaymentGatewayServiceClient
	mockRpvdDao  *mocks.MockRecurringPaymentsVendorDetailsDao
}

func getEmandateProcessorWithMocks(t *testing.T) (*PgEmandateProcessor, *pgEmandateProcessorMockDependencies, func()) {
	ctrl := gomock.NewController(t)
	mockRpaDao := mocks.NewMockRecurringPaymentsActionDao(ctrl)
	mockRpDao := mocks.NewMockRecurringPaymentDao(ctrl)
	mockPgClient := mocks2.NewMockPaymentGatewayServiceClient(ctrl)
	mockRpvdDao := mocks.NewMockRecurringPaymentsVendorDetailsDao(ctrl)
	return &PgEmandateProcessor{
			pgClient: mockPgClient,
			rpaDao:   mockRpaDao,
			rpDao:    mockRpDao,
			rpVdDao:  mockRpvdDao,
		}, &pgEmandateProcessorMockDependencies{
			mockRpaDao:   mockRpaDao,
			mockRpDao:    mockRpDao,
			mockPgClient: mockPgClient,
			mockRpvdDao:  mockRpvdDao,
		}, ctrl.Finish
}

type pgUpiProcessorMockDependencies struct {
	mockRpaDao   *mocks.MockRecurringPaymentsActionDao
	mockRpDao    *mocks.MockRecurringPaymentDao
	mockPgClient *mocks2.MockPaymentGatewayServiceClient
	mockRpvdDao  *mocks.MockRecurringPaymentsVendorDetailsDao
}

func getUpiProcessorWithMocks(t *testing.T) (*PgUpiProcessor, *pgUpiProcessorMockDependencies, func()) {
	ctrl := gomock.NewController(t)
	mockRpaDao := mocks.NewMockRecurringPaymentsActionDao(ctrl)
	mockRpDao := mocks.NewMockRecurringPaymentDao(ctrl)
	mockPgClient := mocks2.NewMockPaymentGatewayServiceClient(ctrl)
	mockRpvdDao := mocks.NewMockRecurringPaymentsVendorDetailsDao(ctrl)
	return &PgUpiProcessor{
			pgClient: mockPgClient,
			rpaDao:   mockRpaDao,
			rpDao:    mockRpDao,
			rpvdDao:  mockRpvdDao,
		}, &pgUpiProcessorMockDependencies{
			mockRpaDao:   mockRpaDao,
			mockRpDao:    mockRpDao,
			mockPgClient: mockPgClient,
			mockRpvdDao:  mockRpvdDao,
		}, ctrl.Finish
}

func TestMain(m *testing.M) {
	logger.Init(cfg.TestEnv)
	exitCode := m.Run()
	os.Exit(exitCode)
}
