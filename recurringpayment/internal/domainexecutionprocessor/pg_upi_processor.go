package domainexecutionprocessor

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/recurringpayment/enums"
	"github.com/epifi/gamma/api/recurringpayment/paymentgateway"
	"github.com/epifi/gamma/recurringpayment/dao"
)

var (
	paymentTypeToExecutionLimit = map[rpPb.RecurringPaymentType]float64{
		// for upi payments not involving pin, the limit is 15000. Since execution
		// does not have any user action, we can only transact upto 15000 in a single
		// execution
		rpPb.RecurringPaymentType_UPI_MANDATES: 15000,
	}
	pgOrderStatusToDomainActivityStatus = map[enums.OrderStatus]DomainExecutionStatus{
		enums.OrderStatus_ORDER_STATUS_PAID:        DOMAIN_EXECUTION_STATUS_SUCCESS,
		enums.OrderStatus_ORDER_STATUS_IN_PROGRESS: DOMAIN_EXECUTION_STATUS_IN_PROGRESS,
		enums.OrderStatus_ORDER_STATUS_FAILED:      DOMAIN_EXECUTION_STATUS_FAILURE,
	}
)

type PgUpiProcessor struct {
	rpaDao   dao.RecurringPaymentsActionDao
	rpDao    dao.RecurringPaymentDao
	pgClient paymentgateway.PaymentGatewayServiceClient
	rpvdDao  dao.RecurringPaymentsVendorDetailsDao
}

func NewPgUpiProcessor(
	rpaDao dao.RecurringPaymentsActionDao,
	rpDao dao.RecurringPaymentDao,
	pgClient paymentgateway.PaymentGatewayServiceClient,
	rpvdDao dao.RecurringPaymentsVendorDetailsDao) *PgUpiProcessor {
	return &PgUpiProcessor{
		rpaDao:   rpaDao,
		rpDao:    rpDao,
		pgClient: pgClient,
		rpvdDao:  rpvdDao,
	}
}

func (p *PgUpiProcessor) InitiateExecution(ctx context.Context, req *InitiateExecutionAtDomainReq) (*InitiateExecutionAtDomainRes, error) {
	var (
		res      = &InitiateExecutionAtDomainRes{}
		pgVendor commonvgpb.Vendor
	)
	rpa, err := p.rpaDao.GetById(ctx, req.RecurringPaymentActionId)
	if err != nil {
		return nil, fmt.Errorf("error in fetching recurring payment action : %w", err)
	}

	rp, err := p.rpDao.GetById(ctx, rpa.GetRecurringPaymentId())
	if err != nil {
		return nil, fmt.Errorf("error in fetching recurring payment : %w", err)
	}

	ctx = epificontext.WithOwnership(ctx, rp.GetEntityOwnership())

	rpVd, err := p.rpvdDao.GetByRecurringPaymentId(ctx, rpa.GetRecurringPaymentId())
	if err != nil {
		return nil, fmt.Errorf("error in fetching recurring payment vendor details : %w", err)
	}

	pgVendor = rpVd.GetVendor()

	execLimit, ok := paymentTypeToExecutionLimit[rp.GetType()]
	if ok {
		executionAmountFloat, _ := moneyPkg.ToDecimal(req.ExecutionAmount).Float64()
		if executionAmountFloat > execLimit {
			return nil, fmt.Errorf("execution amount more than the threshold for given execution : %w", epifierrors.ErrInvalidArgument)
		}
	}

	orderRes, err := p.pgClient.CreateOrder(ctx, &paymentgateway.CreateOrderRequest{
		ClientRequestId:       rpa.GetClientRequestId(),
		RecurringPaymentId:    rpa.GetRecurringPaymentId(),
		OrderAmount:           req.ExecutionAmount,
		RecurringPaymentStage: enums.RecurringPaymentStage_RECURRING_PAYMENT_STAGE_EXECUTION,
		Vendor:                pgVendor,
	})
	if te := epifigrpc.RPCError(orderRes, err); te != nil {
		res.RecurringPaymentActionStatus = orderRes.GetDetailedStatus()
		logger.Error(ctx, "error in creating order for rp execution", zap.Error(te))
		return res, nil
	}

	res.RecurringPaymentActionStatus = orderRes.GetDetailedStatus()
	return res, nil
}

func (p *PgUpiProcessor) EnquireExecutionStatus(ctx context.Context, recurringPaymentActionId string) (*EnquireExecutionStatusAtDomainRes, error) {
	var (
		res = &EnquireExecutionStatusAtDomainRes{}
		ok  bool
	)
	rpa, err := p.rpaDao.GetById(ctx, recurringPaymentActionId)
	if err != nil {
		return nil, fmt.Errorf("error in fetching recurring payment action by id %s : %w", recurringPaymentActionId, err)
	}
	rp, err := p.rpDao.GetById(ctx, rpa.GetRecurringPaymentId())
	if err != nil {
		return nil, fmt.Errorf("error in fetching recurring payment by id %s %w", rpa.GetRecurringPaymentId(), err)
	}
	ctx = epificontext.WithOwnership(ctx, rp.GetEntityOwnership())
	rpvd, err := p.rpvdDao.GetByRecurringPaymentId(ctx, rp.GetId())
	if err != nil {
		return nil, fmt.Errorf("error in fetching recurring payments vendor details : %w", err)
	}
	// Not querying for status from vendor and only the internal order instead, since mandate execution depends on the
	// completion of the internal order. If we query for real-time status from vendor then, the there is a possibility
	// that the recurring payment execution completes before the internal order is marked as success.
	orderRes, err := p.pgClient.GetOrderStatus(ctx, &paymentgateway.GetOrderStatusRequest{
		RecurringPaymentId: rp.GetId(),
		ClientRequestId:    rpa.GetClientRequestId(),
		Vendor:             rpvd.GetVendor(),
	})
	if te := epifigrpc.RPCError(orderRes, err); te != nil {
		return nil, fmt.Errorf("error in fetching order status : %w", te)
	}
	res.ExecutionStatus, ok = pgOrderStatusToDomainActivityStatus[orderRes.GetOrderStatus()]
	if !ok {
		res.ExecutionStatus = DOMAIN_EXECUTION_STATUS_IN_PROGRESS
	}
	res.ActionDetailedStatusInfo = &rpPb.ActionDetailedStatusInfo{
		FiStatusCode:     orderRes.GetDetailedStatus().GetFiStatusCode(),
		ErrorDescription: orderRes.GetDetailedStatus().GetErrorDescription(),
	}
	return res, nil
}

func (p *PgUpiProcessor) IsAuthRequired(ctx context.Context, req *IsAuthRequiredReq) (bool, error) {
	return false, nil
}

func (p *PgUpiProcessor) IsExecutionAllowed(ctx context.Context, recurringPaymentId string) (bool, error) {
	rp, err := p.rpDao.GetById(ctx, recurringPaymentId)
	if err != nil {
		return false, fmt.Errorf("error in fetching recurring payment for given id : %s : %w ", recurringPaymentId, err)
	}
	if datetime.IsAfter(timestampPb.Now(), rp.GetInterval().GetEndTime()) {
		return false, nil
	}
	return true, nil
}
