//go:generate mockgen -source=$PWD/processor.go -destination=$PWD/mocks/mock_processor.go -package=mocks
package domainexecutionprocessor

import (
	"context"

	rpPb "github.com/epifi/gamma/api/recurringpayment"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	rpPayloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
)

type DomainExecutionProcessor interface {
	// InitiateExecution is responsible for initiating the execution at domain
	InitiateExecution(ctx context.Context, req *InitiateExecutionAtDomainReq) (*InitiateExecutionAtDomainRes, error)
	// EnquireExecutionStatus is used to enquire the execution status at domain post initiation
	EnquireExecutionStatus(ctx context.Context, recurringPaymentActionId string) (*EnquireExecutionStatusAtDomainRes, error)
	// IsAuthRequired is used to check if auth is required for the exeuction.
	IsAuthRequired(ctx context.Context, req *IsAuthRequiredReq) (bool, error)
	// IsExecutionAllowed is used to check if recurring payment execution is allowed or not
	IsExecutionAllowed(ctx context.Context, recurringPaymentId string) (bool, error)
}

type InitiateExecutionAtDomainReq struct {
	RecurringPaymentId string
	// used as client request id at domain
	RecurringPaymentActionId string
	ExecutionAmount          *moneyPb.Money
	Payload                  *rpPayloadPb.RecurringPaymentTypeSpecificExecutionPayload
}

type InitiateExecutionAtDomainRes struct {
	RecurringPaymentActionStatus *rpPb.ActionDetailedStatusInfo
}

func (i *InitiateExecutionAtDomainRes) GetRecurringPaymentActionStatus() *rpPb.ActionDetailedStatusInfo {
	if i != nil {
		return i.RecurringPaymentActionStatus
	}
	return nil
}

type EnquireExecutionStatusAtDomainRes struct {
	ExecutionStatus DomainExecutionStatus
	// stores the relevant action detailed status for execute mandate action such as fi status code etc.
	ActionDetailedStatusInfo *rpPb.ActionDetailedStatusInfo
}

type DomainExecutionStatus string

// status to be returned from the processor to the activity
// this can be extended to return granular level of status if required to the activity
const (
	DOMAIN_EXECUTION_STATUS_UNSPECIFIED DomainExecutionStatus = "DOMAIN_EXECUTION_STATUS_UNSPECIFIED"
	DOMAIN_EXECUTION_STATUS_SUCCESS     DomainExecutionStatus = "DOMAIN_EXECUTION_STATUS_SUCCESS"
	DOMAIN_EXECUTION_STATUS_FAILURE     DomainExecutionStatus = "DOMAIN_EXECUTION_STATUS_FAILURE"
	DOMAIN_EXECUTION_STATUS_IN_PROGRESS DomainExecutionStatus = "DOMAIN_EXECUTION_STATUS_IN_PROGRESS"
)

type IsAuthRequiredReq struct {
	RecurringPaymentId string
	Payload            *rpPayloadPb.RecurringPaymentTypeSpecificExecutionPayload
}
