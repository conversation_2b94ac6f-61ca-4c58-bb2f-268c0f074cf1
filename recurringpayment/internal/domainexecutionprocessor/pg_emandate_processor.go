package domainexecutionprocessor

import (
	"go.uber.org/zap"

	"context"
	"fmt"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/recurringpayment/enums"
	"github.com/epifi/gamma/api/recurringpayment/paymentgateway"
	"github.com/epifi/gamma/recurringpayment/dao"
)

type PgEmandateProcessor struct {
	rpaDao   dao.RecurringPaymentsActionDao
	rpDao    dao.RecurringPaymentDao
	pgClient paymentgateway.PaymentGatewayServiceClient
	rpVdDao  dao.RecurringPaymentsVendorDetailsDao
}

func NewPgEmandateProcessor(
	rpaDao dao.RecurringPaymentsActionDao,
	rpDao dao.RecurringPaymentDao,
	pgClient paymentgateway.PaymentGatewayServiceClient,
	rpVdDao dao.RecurringPaymentsVendorDetailsDao) *PgEmandateProcessor {
	return &PgEmandateProcessor{
		rpaDao:   rpaDao,
		rpDao:    rpDao,
		pgClient: pgClient,
		rpVdDao:  rpVdDao,
	}
}

func (p *PgEmandateProcessor) InitiateExecution(ctx context.Context, req *InitiateExecutionAtDomainReq) (*InitiateExecutionAtDomainRes, error) {
	var (
		res      = &InitiateExecutionAtDomainRes{}
		pgVendor commonvgpb.Vendor
	)
	rpa, err := p.rpaDao.GetById(ctx, req.RecurringPaymentActionId)
	if err != nil {
		return nil, fmt.Errorf("error in fetching recurring payment action : %w", err)
	}
	rp, err := p.rpDao.GetById(ctx, rpa.GetRecurringPaymentId())
	if err != nil {
		return nil, fmt.Errorf("error in fetching recurring payment : %w", err)
	}

	ctx = epificontext.WithOwnership(ctx, rp.GetEntityOwnership())
	rpVd, err := p.rpVdDao.GetByRecurringPaymentId(ctx, rpa.GetRecurringPaymentId())
	if err != nil {
		return nil, fmt.Errorf("error in fetching recurring payments vendor details for rp id : %s : %w", rpa.GetRecurringPaymentId(), err)
	}
	pgVendor = rpVd.GetVendor()
	orderRes, err := p.pgClient.CreateOrder(ctx, &paymentgateway.CreateOrderRequest{
		ClientRequestId:       rpa.GetClientRequestId(),
		RecurringPaymentId:    rpa.GetRecurringPaymentId(),
		OrderAmount:           req.ExecutionAmount,
		RecurringPaymentStage: enums.RecurringPaymentStage_RECURRING_PAYMENT_STAGE_EXECUTION,
		Vendor:                pgVendor,
	})
	if te := epifigrpc.RPCError(orderRes, err); te != nil {
		res.RecurringPaymentActionStatus = orderRes.GetDetailedStatus()
		logger.Error(ctx, "error in creating order for rp execution", zap.Error(te))
		return res, nil
	}
	res.RecurringPaymentActionStatus = orderRes.GetDetailedStatus()
	return res, nil
}

func (p *PgEmandateProcessor) EnquireExecutionStatus(ctx context.Context, recurringPaymentActionId string) (*EnquireExecutionStatusAtDomainRes, error) {
	var (
		res = &EnquireExecutionStatusAtDomainRes{}
		ok  bool
	)
	rpa, err := p.rpaDao.GetById(ctx, recurringPaymentActionId)
	if err != nil {
		return nil, fmt.Errorf("error in fetching recurring payment action : %w", err)
	}
	rp, err := p.rpDao.GetById(ctx, rpa.GetRecurringPaymentId())
	if err != nil {
		return nil, fmt.Errorf("error in fetching recurring payment : %w", err)
	}
	// overriding the ownership in the ctx at this point will not have any unexpected behaviours since this
	// will only affect the tables whose daos have db resource provider injected.
	ctx = epificontext.WithOwnership(ctx, rp.GetEntityOwnership())

	rpvd, err := p.rpVdDao.GetByRecurringPaymentId(ctx, rp.GetId())
	if err != nil {
		return nil, fmt.Errorf("error in fetching recurring payments vendor details : %w", err)
	}
	// Not querying for status from vendor and only the internal order instead, since mandate execution depends on the
	// completion of the internal order. If we query for real-time status from vendor then, the there is a possibility
	// that the recurring payment execution completes before the internal order is marked as success.
	orderRes, err := p.pgClient.GetOrderStatus(ctx, &paymentgateway.GetOrderStatusRequest{
		RecurringPaymentId: rp.GetId(),
		ClientRequestId:    rpa.GetClientRequestId(),
		Vendor:             rpvd.GetVendor(),
	})
	if te := epifigrpc.RPCError(orderRes, err); te != nil {
		return nil, fmt.Errorf("error in fetching order status : %w", te)
	}
	res.ExecutionStatus, ok = pgOrderStatusToDomainActivityStatus[orderRes.GetOrderStatus()]
	if !ok {
		res.ExecutionStatus = DOMAIN_EXECUTION_STATUS_IN_PROGRESS
	}
	res.ActionDetailedStatusInfo = &recurringpayment.ActionDetailedStatusInfo{
		FiStatusCode:     orderRes.GetDetailedStatus().GetFiStatusCode(),
		ErrorDescription: orderRes.GetDetailedStatus().GetErrorDescription(),
	}
	return res, nil
}

func (p *PgEmandateProcessor) IsAuthRequired(ctx context.Context, req *IsAuthRequiredReq) (bool, error) {
	return false, nil
}

func (p *PgEmandateProcessor) IsExecutionAllowed(ctx context.Context, recurringPaymentId string) (bool, error) {
	rp, err := p.rpDao.GetById(ctx, recurringPaymentId)
	if err != nil {
		return false, fmt.Errorf("error in fetching recurring payment for given id : %s : %w ", recurringPaymentId, err)
	}
	if datetime.IsAfter(timestampPb.Now(), rp.GetInterval().GetEndTime()) {
		return false, nil
	}
	return true, nil
}
