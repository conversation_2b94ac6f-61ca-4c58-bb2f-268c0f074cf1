package domainexecutionprocessor

import (
	"context"
	"fmt"

	rpPb "github.com/epifi/gamma/api/recurringpayment"

	"github.com/epifi/be-common/pkg/epifigrpc"

	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
)

type EnachMandateExecutionProcessor struct {
	enachClient enachPb.EnachServiceClient
}

func NewEnachMandateExecutionProcessor(enachClient enachPb.EnachServiceClient) *EnachMandateExecutionProcessor {
	return &EnachMandateExecutionProcessor{
		enachClient: enachClient,
	}
}

func (e *EnachMandateExecutionProcessor) InitiateExecution(ctx context.Context, req *InitiateExecutionAtDomainReq) (*InitiateExecutionAtDomainRes, error) {
	// make sync call to the enach exeuction rpc
	initExecutionRes, initExecutionErr := e.enachClient.InitiateExecution(ctx, &enachPb.InitiateExecutionRequest{
		ClientRequestId:    req.RecurringPaymentActionId,
		RecurringPaymentId: req.RecurringPaymentId,
		Amount:             req.ExecutionAmount,
	})
	// non-ok status is not expected from the rpc since we have made validation checks at recurring payment service itself
	if err := epifigrpc.RPCError(initExecutionRes, initExecutionErr); err != nil {
		return nil, fmt.Errorf("failed to initiate execution at domain, err: %w", err)
	}
	return &InitiateExecutionAtDomainRes{}, nil
}

func (e *EnachMandateExecutionProcessor) EnquireExecutionStatus(ctx context.Context, recurringPaymentActionId string) (*EnquireExecutionStatusAtDomainRes, error) {
	// make sync call to the enach enquiry api
	mandateActionStatusRes, mandateActionStatusErr := e.enachClient.GetMandateActionStatus(ctx, &enachPb.GetMandateActionStatusRequest{
		ClientRequestId: recurringPaymentActionId,
	})
	// non-ok status is not expected from the rpc since enach mandate action is already created in the system
	if err := epifigrpc.RPCError(mandateActionStatusRes, mandateActionStatusErr); err != nil {
		return nil, fmt.Errorf("failed to enquire execution status at domain,err: %w", err)
	}
	return &EnquireExecutionStatusAtDomainRes{
		ExecutionStatus: getEnachExecutionStatus(mandateActionStatusRes.GetActionStatus()),
		ActionDetailedStatusInfo: &rpPb.ActionDetailedStatusInfo{
			FiStatusCode: mandateActionStatusRes.GetActionDetailedStatus().GetExecuteActionDetailedStatus().GetFiStatusCode(),
		},
	}, nil
}

func getEnachExecutionStatus(status enachEnumsPb.EnachActionStatus) DomainExecutionStatus {
	switch {
	case status == enachEnumsPb.EnachActionStatus_ACTION_STATUS_SUCCESS:
		return DOMAIN_EXECUTION_STATUS_SUCCESS
	case status == enachEnumsPb.EnachActionStatus_ACTION_STATUS_FAILED:
		return DOMAIN_EXECUTION_STATUS_FAILURE
	case status == enachEnumsPb.EnachActionStatus_ACTION_STATUS_IN_PROGRESS:
		return DOMAIN_EXECUTION_STATUS_IN_PROGRESS
	default:
		return DOMAIN_EXECUTION_STATUS_UNSPECIFIED
	}
}

// todo(Harleen Singh): update the logic when use case comes where auth is required for enach execution
func (e *EnachMandateExecutionProcessor) IsAuthRequired(ctx context.Context, req *IsAuthRequiredReq) (bool, error) {
	return false, nil
}

func (e *EnachMandateExecutionProcessor) IsExecutionAllowed(ctx context.Context, recurringPaymentId string) (bool, error) {
	isEnachMandateExecutionAllowedRes, err := e.enachClient.IsEnachMandateExecutionAllowed(ctx, &enachPb.IsEnachMandateExecutionAllowedRequest{
		RecurringPaymentId: recurringPaymentId,
	})
	if err = epifigrpc.RPCError(isEnachMandateExecutionAllowedRes, err); err != nil {
		return false, fmt.Errorf("failed to fetch enach mandate enach mandate execution allowed response,err: %w", err)
	}
	return isEnachMandateExecutionAllowedRes.GetIsExecutionAllowed(), nil
}
