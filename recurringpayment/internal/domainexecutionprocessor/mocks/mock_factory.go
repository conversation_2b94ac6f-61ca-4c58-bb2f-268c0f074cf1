// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/go/src/github.com/epifi/gamma/recurringpayment/internal/domainexecutionprocessor/factory.go

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	recurringpayment "github.com/epifi/gamma/api/recurringpayment"
	domainexecutionprocessor "github.com/epifi/gamma/recurringpayment/internal/domainexecutionprocessor"
	gomock "github.com/golang/mock/gomock"
)

// MockDomainExecutionProcessorFactory is a mock of DomainExecutionProcessorFactory interface.
type MockDomainExecutionProcessorFactory struct {
	ctrl     *gomock.Controller
	recorder *MockDomainExecutionProcessorFactoryMockRecorder
}

// MockDomainExecutionProcessorFactoryMockRecorder is the mock recorder for MockDomainExecutionProcessorFactory.
type MockDomainExecutionProcessorFactoryMockRecorder struct {
	mock *MockDomainExecutionProcessorFactory
}

// NewMockDomainExecutionProcessorFactory creates a new mock instance.
func NewMockDomainExecutionProcessorFactory(ctrl *gomock.Controller) *MockDomainExecutionProcessorFactory {
	mock := &MockDomainExecutionProcessorFactory{ctrl: ctrl}
	mock.recorder = &MockDomainExecutionProcessorFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDomainExecutionProcessorFactory) EXPECT() *MockDomainExecutionProcessorFactoryMockRecorder {
	return m.recorder
}

// GetProcessor mocks base method.
func (m *MockDomainExecutionProcessorFactory) GetProcessor(rpType recurringpayment.RecurringPaymentType, rpRoute recurringpayment.RecurringPaymentRoute) (domainexecutionprocessor.DomainExecutionProcessor, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProcessor", rpType, rpRoute)
	ret0, _ := ret[0].(domainexecutionprocessor.DomainExecutionProcessor)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProcessor indicates an expected call of GetProcessor.
func (mr *MockDomainExecutionProcessorFactoryMockRecorder) GetProcessor(rpType, rpRoute interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProcessor", reflect.TypeOf((*MockDomainExecutionProcessorFactory)(nil).GetProcessor), rpType, rpRoute)
}
