// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/go/src/github.com/epifi/gamma/recurringpayment/internal/domainexecutionprocessor/processor.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	domainexecutionprocessor "github.com/epifi/gamma/recurringpayment/internal/domainexecutionprocessor"
	gomock "github.com/golang/mock/gomock"
)

// MockDomainExecutionProcessor is a mock of DomainExecutionProcessor interface.
type MockDomainExecutionProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockDomainExecutionProcessorMockRecorder
}

// MockDomainExecutionProcessorMockRecorder is the mock recorder for MockDomainExecutionProcessor.
type MockDomainExecutionProcessorMockRecorder struct {
	mock *MockDomainExecutionProcessor
}

// NewMockDomainExecutionProcessor creates a new mock instance.
func NewMockDomainExecutionProcessor(ctrl *gomock.Controller) *MockDomainExecutionProcessor {
	mock := &MockDomainExecutionProcessor{ctrl: ctrl}
	mock.recorder = &MockDomainExecutionProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDomainExecutionProcessor) EXPECT() *MockDomainExecutionProcessorMockRecorder {
	return m.recorder
}

// EnquireExecutionStatus mocks base method.
func (m *MockDomainExecutionProcessor) EnquireExecutionStatus(ctx context.Context, recurringPaymentActionId string) (*domainexecutionprocessor.EnquireExecutionStatusAtDomainRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnquireExecutionStatus", ctx, recurringPaymentActionId)
	ret0, _ := ret[0].(*domainexecutionprocessor.EnquireExecutionStatusAtDomainRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EnquireExecutionStatus indicates an expected call of EnquireExecutionStatus.
func (mr *MockDomainExecutionProcessorMockRecorder) EnquireExecutionStatus(ctx, recurringPaymentActionId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnquireExecutionStatus", reflect.TypeOf((*MockDomainExecutionProcessor)(nil).EnquireExecutionStatus), ctx, recurringPaymentActionId)
}

// InitiateExecution mocks base method.
func (m *MockDomainExecutionProcessor) InitiateExecution(ctx context.Context, req *domainexecutionprocessor.InitiateExecutionAtDomainReq) (*domainexecutionprocessor.InitiateExecutionAtDomainRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateExecution", ctx, req)
	ret0, _ := ret[0].(*domainexecutionprocessor.InitiateExecutionAtDomainRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateExecution indicates an expected call of InitiateExecution.
func (mr *MockDomainExecutionProcessorMockRecorder) InitiateExecution(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateExecution", reflect.TypeOf((*MockDomainExecutionProcessor)(nil).InitiateExecution), ctx, req)
}

// IsAuthRequired mocks base method.
func (m *MockDomainExecutionProcessor) IsAuthRequired(ctx context.Context, req *domainexecutionprocessor.IsAuthRequiredReq) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsAuthRequired", ctx, req)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsAuthRequired indicates an expected call of IsAuthRequired.
func (mr *MockDomainExecutionProcessorMockRecorder) IsAuthRequired(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsAuthRequired", reflect.TypeOf((*MockDomainExecutionProcessor)(nil).IsAuthRequired), ctx, req)
}

// IsExecutionAllowed mocks base method.
func (m *MockDomainExecutionProcessor) IsExecutionAllowed(ctx context.Context, recurringPaymentId string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsExecutionAllowed", ctx, recurringPaymentId)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsExecutionAllowed indicates an expected call of IsExecutionAllowed.
func (mr *MockDomainExecutionProcessorMockRecorder) IsExecutionAllowed(ctx, recurringPaymentId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsExecutionAllowed", reflect.TypeOf((*MockDomainExecutionProcessor)(nil).IsExecutionAllowed), ctx, recurringPaymentId)
}
