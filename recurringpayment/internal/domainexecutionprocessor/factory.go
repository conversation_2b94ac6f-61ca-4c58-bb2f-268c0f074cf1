//go:generate mockgen -source=$PWD/factory.go -destination=$PWD/mocks/mock_factory.go -package=mocks
package domainexecutionprocessor

import (
	"fmt"

	"github.com/google/wire"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/be-common/pkg/epifierrors"
)

var DomainExecutionProcessorWireSet = wire.NewSet(NewEnachMandateExecutionProcessor, NewPgEmandateProcessor, NewPgUpiProcessor)
var DomainExecutionProcessorFactoryWireSet = wire.NewSet(DomainExecutionProcessorWireSet, wire.NewSet(NewDomainExecutionProcessoryFactoryImpl, wire.Bind(new(DomainExecutionProcessorFactory), new(*DomainExecutionProcessoryFactoryImpl))))

type DomainExecutionProcessorFactory interface {
	GetProcessor(rpType rpPb.RecurringPaymentType, rpRoute rpPb.RecurringPaymentRoute) (DomainExecutionProcessor, error)
}

type DomainExecutionProcessoryFactoryImpl struct {
	enachExecutionProcessor *EnachMandateExecutionProcessor
	pgEmandateProcessor     *PgEmandateProcessor
	pgUpiProcessor          *PgUpiProcessor
}

func NewDomainExecutionProcessoryFactoryImpl(enachExecutionProcessor *EnachMandateExecutionProcessor, pgUpiProcessor *PgUpiProcessor, pgEmandateProcessor *PgEmandateProcessor) *DomainExecutionProcessoryFactoryImpl {
	return &DomainExecutionProcessoryFactoryImpl{
		enachExecutionProcessor: enachExecutionProcessor,
		pgEmandateProcessor:     pgEmandateProcessor,
		pgUpiProcessor:          pgUpiProcessor,
	}
}

func (d *DomainExecutionProcessoryFactoryImpl) GetProcessor(rpType rpPb.RecurringPaymentType, paymentRoute rpPb.RecurringPaymentRoute) (DomainExecutionProcessor, error) {
	switch paymentRoute {
	case rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE:
		return d.getNativeExecutionProcessor(rpType)
	case rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_EXTERNAL:
		return d.getExternalExecutionProcessor(rpType)
	default:
		return nil, fmt.Errorf("error in fetching execution processor for the payment route : %s %w", paymentRoute.String(), epifierrors.ErrInvalidArgument)
	}
}

func (d *DomainExecutionProcessoryFactoryImpl) getNativeExecutionProcessor(rpType rpPb.RecurringPaymentType) (DomainExecutionProcessor, error) {
	switch rpType {
	case rpPb.RecurringPaymentType_ENACH_MANDATES:
		return d.enachExecutionProcessor, nil
	default:
		return nil, fmt.Errorf("no processor implementation found for the domain execution, rpType: %s, err: %w", rpType.String(), epifierrors.ErrInvalidArgument)
	}
}

func (d *DomainExecutionProcessoryFactoryImpl) getExternalExecutionProcessor(rpType rpPb.RecurringPaymentType) (DomainExecutionProcessor, error) {
	switch rpType {
	case rpPb.RecurringPaymentType_ENACH_MANDATES:
		return d.pgEmandateProcessor, nil
	case rpPb.RecurringPaymentType_UPI_MANDATES:
		return d.pgUpiProcessor, nil
	default:
		return nil, fmt.Errorf("no processor implementation found for the external domain execution, rpType: %s, err: %w", rpType.String(), epifierrors.ErrInvalidArgument)
	}
}
