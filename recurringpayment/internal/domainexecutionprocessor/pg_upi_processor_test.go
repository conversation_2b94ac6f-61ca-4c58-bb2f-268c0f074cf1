package domainexecutionprocessor

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/pay/pgerrorcodes"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/recurringpayment/enums"
	"github.com/epifi/gamma/api/recurringpayment/paymentgateway"
	types "github.com/epifi/gamma/api/typesv2"
)

func TestPgUpiProcessor_EnquireExecutionStatus(t *testing.T) {

	type args struct {
		ctx                      context.Context
		RecurringPaymentActionId string
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func(md *pgUpiProcessorMockDependencies)
		want       *EnquireExecutionStatusAtDomainRes
		wantErr    bool
	}{
		{
			name: "Successful status enquiry",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpaDao.EXPECT().GetById(gomock.Any(), "action-id-1").Return(&rpPb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-id-1",
					ClientRequestId:    "client-request-id",
				}, nil)
				md.mockRpDao.EXPECT().GetById(gomock.Any(), "rp-id-1").Return(&rpPb.RecurringPayment{
					Id: "rp-id-1",
				}, nil)
				md.mockRpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id-1").Return(&rpPb.RecurringPaymentsVendorDetails{
					Vendor: commonvgpb.Vendor_RAZORPAY,
				}, nil)
				md.mockPgClient.EXPECT().GetOrderStatus(gomock.Any(), gomock.Any()).Return(&paymentgateway.GetOrderStatusResponse{
					Status:      rpc.StatusOk(),
					OrderStatus: enums.OrderStatus_ORDER_STATUS_PAID,
					DetailedStatus: &rpPb.ActionDetailedStatusInfo{
						FiStatusCode:     pgerrorcodes.PaymentGatewaySuccessFiStatusCode,
						ErrorDescription: "",
					},
				}, nil)
			},
			args: args{
				ctx:                      context.Background(),
				RecurringPaymentActionId: "action-id-1",
			},
			want: &EnquireExecutionStatusAtDomainRes{
				ExecutionStatus: DOMAIN_EXECUTION_STATUS_SUCCESS,
				ActionDetailedStatusInfo: &rpPb.ActionDetailedStatusInfo{
					FiStatusCode:     pgerrorcodes.PaymentGatewaySuccessFiStatusCode,
					ErrorDescription: "",
				},
			},
			wantErr: false,
		},
		{
			name: "Error fetching recurring payment action",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpaDao.EXPECT().GetById(gomock.Any(), "action-id").Return(nil, fmt.Errorf("error"))
			},
			args: args{
				ctx:                      context.Background(),
				RecurringPaymentActionId: "action-id",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Error fetching recurring payment",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpaDao.EXPECT().GetById(gomock.Any(), "action-id").Return(&rpPb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-id",
				}, nil)
				md.mockRpDao.EXPECT().GetById(gomock.Any(), "rp-id").Return(nil, fmt.Errorf("error"))
			},
			args: args{
				ctx:                      context.Background(),
				RecurringPaymentActionId: "action-id",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Error fetching recurring payment vendor details",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpaDao.EXPECT().GetById(gomock.Any(), "action-id").Return(&rpPb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-id",
				}, nil)
				md.mockRpDao.EXPECT().GetById(gomock.Any(), "rp-id").Return(&rpPb.RecurringPayment{
					Id: "rp-id",
				}, nil)
				md.mockRpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id").Return(nil, fmt.Errorf("error"))
			},
			args: args{
				ctx:                      context.Background(),
				RecurringPaymentActionId: "action-id",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Technical error in fetching order status",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpaDao.EXPECT().GetById(gomock.Any(), "action-id").Return(&rpPb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-id",
					ClientRequestId:    "client-request-id",
				}, nil)
				md.mockRpDao.EXPECT().GetById(gomock.Any(), "rp-id").Return(&rpPb.RecurringPayment{
					Id: "rp-id",
				}, nil)
				md.mockRpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id").Return(&rpPb.RecurringPaymentsVendorDetails{
					Vendor: commonvgpb.Vendor_RAZORPAY,
				}, nil)
				md.mockPgClient.EXPECT().GetOrderStatus(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("technical error"))
			},
			args: args{
				ctx:                      context.Background(),
				RecurringPaymentActionId: "action-id",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Order has failed due to business decline",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpaDao.EXPECT().GetById(gomock.Any(), "action-id").Return(&rpPb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-id",
					ClientRequestId:    "client-request-id",
				}, nil)
				md.mockRpDao.EXPECT().GetById(gomock.Any(), "rp-id").Return(&rpPb.RecurringPayment{
					Id: "rp-id",
				}, nil)
				md.mockRpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id").Return(&rpPb.RecurringPaymentsVendorDetails{
					Vendor: commonvgpb.Vendor_RAZORPAY,
				}, nil)
				md.mockPgClient.EXPECT().GetOrderStatus(gomock.Any(), gomock.Any()).Return(&paymentgateway.GetOrderStatusResponse{
					Status:      rpc.StatusOk(),
					OrderStatus: enums.OrderStatus_ORDER_STATUS_FAILED,
					DetailedStatus: &rpPb.ActionDetailedStatusInfo{
						FiStatusCode:     "RZP100",
						ErrorDescription: "input_validation_failed",
					},
				}, nil)
			},
			args: args{
				ctx:                      context.Background(),
				RecurringPaymentActionId: "action-id",
			},
			want: &EnquireExecutionStatusAtDomainRes{
				ExecutionStatus: DOMAIN_EXECUTION_STATUS_FAILURE,
				ActionDetailedStatusInfo: &rpPb.ActionDetailedStatusInfo{
					FiStatusCode:     "RZP100",
					ErrorDescription: "input_validation_failed",
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			processor, md, release := getUpiProcessorWithMocks(t)
			tt.setupMocks(md)
			defer release()
			got, err := processor.EnquireExecutionStatus(tt.args.ctx, tt.args.RecurringPaymentActionId)
			if (err != nil) != tt.wantErr {
				t.Errorf("EnquireExecutionStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("EnquireExecutionStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPgUpiProcessor_InitiateExecution(t *testing.T) {

	tests := []struct {
		name       string
		setupMocks func(md *pgUpiProcessorMockDependencies)
		req        *InitiateExecutionAtDomainReq
		wantRes    *InitiateExecutionAtDomainRes
		wantErr    bool
	}{
		{
			name: "Successful execution",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpaDao.EXPECT().GetById(gomock.Any(), "action-id-1").Return(&rpPb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-id-1",
					ClientRequestId:    "client-request-id",
				}, nil)
				md.mockRpDao.EXPECT().GetById(gomock.Any(), "rp-id-1").Return(&rpPb.RecurringPayment{
					Id: "rp-id-1",
				}, nil)
				md.mockRpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id-1").Return(&rpPb.RecurringPaymentsVendorDetails{
					Vendor: commonvgpb.Vendor_RAZORPAY,
				}, nil)
				md.mockPgClient.EXPECT().CreateOrder(gomock.Any(), gomock.Any()).Return(&paymentgateway.CreateOrderResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			req: &InitiateExecutionAtDomainReq{
				RecurringPaymentActionId: "action-id-1",
				ExecutionAmount:          &moneyPb.Money{CurrencyCode: "INR", Units: 1000},
			},
			wantRes: &InitiateExecutionAtDomainRes{RecurringPaymentActionStatus: nil},
			wantErr: false,
		},
		{
			name: "Error fetching recurring payment action",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpaDao.EXPECT().GetById(gomock.Any(), "action-id").Return(nil, fmt.Errorf("error"))
			},
			req: &InitiateExecutionAtDomainReq{
				RecurringPaymentActionId: "action-id",
				ExecutionAmount:          &moneyPb.Money{CurrencyCode: "INR", Units: 1000},
			},
			wantErr: true,
		},
		{
			name: "Error fetching recurring payment",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpaDao.EXPECT().GetById(gomock.Any(), "action-id").Return(&rpPb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-id",
				}, nil)
				md.mockRpDao.EXPECT().GetById(gomock.Any(), "rp-id").Return(nil, fmt.Errorf("error"))
			},
			req: &InitiateExecutionAtDomainReq{
				RecurringPaymentActionId: "action-id",
				ExecutionAmount:          &moneyPb.Money{CurrencyCode: "INR", Units: 1000},
			},
			wantErr: true,
		},
		{
			name: "Error fetching recurring payment vendor details",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpaDao.EXPECT().GetById(gomock.Any(), "action-id").Return(&rpPb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-id",
				}, nil)
				md.mockRpDao.EXPECT().GetById(gomock.Any(), "rp-id").Return(&rpPb.RecurringPayment{
					Id: "rp-id",
				}, nil)
				md.mockRpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id").Return(nil, fmt.Errorf("error"))
			},
			req: &InitiateExecutionAtDomainReq{
				RecurringPaymentActionId: "action-id",
				ExecutionAmount:          &moneyPb.Money{CurrencyCode: "INR", Units: 1000},
			},
			wantErr: true,
		},
		{
			name: "Execution amount exceeds the threshold",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpaDao.EXPECT().GetById(gomock.Any(), "action-id-2").Return(&rpPb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-id-2",
				}, nil)
				md.mockRpDao.EXPECT().GetById(gomock.Any(), "rp-id-2").Return(&rpPb.RecurringPayment{
					Id:   "rp-id-2",
					Type: rpPb.RecurringPaymentType_UPI_MANDATES,
				}, nil)
				md.mockRpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id-2").Return(&rpPb.RecurringPaymentsVendorDetails{
					Vendor: commonvgpb.Vendor_RAZORPAY,
				}, nil)
			},
			req: &InitiateExecutionAtDomainReq{
				RecurringPaymentActionId: "action-id-2",
				ExecutionAmount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        20000,
					Nanos:        0,
				},
			},
			wantErr: true,
		},
		{
			name: "Technical error in creating order",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpaDao.EXPECT().GetById(gomock.Any(), "action-id").Return(&rpPb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-id",
					ClientRequestId:    "client-request-id",
				}, nil)
				md.mockRpDao.EXPECT().GetById(gomock.Any(), "rp-id").Return(&rpPb.RecurringPayment{
					Id: "rp-id",
				}, nil)
				md.mockRpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id").Return(&rpPb.RecurringPaymentsVendorDetails{
					Vendor: commonvgpb.Vendor_RAZORPAY,
				}, nil)
				md.mockPgClient.EXPECT().CreateOrder(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("technical error"))
			},
			req: &InitiateExecutionAtDomainReq{
				RecurringPaymentActionId: "action-id",
				ExecutionAmount:          &moneyPb.Money{CurrencyCode: "INR", Units: 1000},
			},
			wantRes: &InitiateExecutionAtDomainRes{
				RecurringPaymentActionStatus: nil,
			},
			wantErr: false,
		},
		{
			name: "Business logic error in creating order",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpaDao.EXPECT().GetById(gomock.Any(), "action-id").Return(&rpPb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-id",
					ClientRequestId:    "client-request-id",
				}, nil)
				md.mockRpDao.EXPECT().GetById(gomock.Any(), "rp-id").Return(&rpPb.RecurringPayment{
					Id: "rp-id",
				}, nil)
				md.mockRpvdDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "rp-id").Return(&rpPb.RecurringPaymentsVendorDetails{
					Vendor: commonvgpb.Vendor_RAZORPAY,
				}, nil)
				md.mockPgClient.EXPECT().CreateOrder(gomock.Any(), gomock.Any()).Return(&paymentgateway.CreateOrderResponse{
					Status: rpc.StatusInternal(),
					DetailedStatus: &rpPb.ActionDetailedStatusInfo{
						FiStatusCode:     "RZP100",
						ErrorDescription: "src: business, step: payment_initiation, reason: input_validation_failed, field: amount, desc: Your payment amount is different from your order amount. To pay successfully, please try using right amount.",
					},
				}, fmt.Errorf("business error"))
			},
			req: &InitiateExecutionAtDomainReq{
				RecurringPaymentActionId: "action-id",
				ExecutionAmount:          &moneyPb.Money{CurrencyCode: "INR", Units: 1000},
			},
			wantRes: &InitiateExecutionAtDomainRes{
				RecurringPaymentActionStatus: &rpPb.ActionDetailedStatusInfo{
					FiStatusCode:     "RZP100",
					ErrorDescription: "src: business, step: payment_initiation, reason: input_validation_failed, field: amount, desc: Your payment amount is different from your order amount. To pay successfully, please try using right amount.",
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			processor, md, release := getUpiProcessorWithMocks(t)
			tt.setupMocks(md)
			defer release()
			_, err := processor.InitiateExecution(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("InitiateExecution() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestPgUpiProcessor_IsExecutionAllowed(t *testing.T) {

	type args struct {
		ctx                context.Context
		recurringPaymentId string
	}

	tests := []struct {
		name       string
		setupMocks func(md *pgUpiProcessorMockDependencies)
		args       args
		want       bool
		wantErr    bool
	}{
		{
			name: "Successful execution allowed",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpDao.EXPECT().GetById(gomock.Any(), "rp-id-1").Return(&rpPb.RecurringPayment{
					Id: "rp-id-1",
					Interval: &types.Interval{
						EndTime: timestampPb.New(time.Now().Add(24 * time.Hour)),
					},
				}, nil)
			},
			args: args{
				ctx:                context.Background(),
				recurringPaymentId: "rp-id-1",
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "Error fetching recurring payment",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpDao.EXPECT().GetById(gomock.Any(), "rp-id-2").Return(nil, fmt.Errorf("error"))
			},
			args: args{
				ctx:                context.Background(),
				recurringPaymentId: "rp-id-2",
			},
			want:    false,
			wantErr: true,
		},
		{
			name: "Execution not allowed due to end time",
			setupMocks: func(md *pgUpiProcessorMockDependencies) {
				md.mockRpDao.EXPECT().GetById(gomock.Any(), "rp-id-3").Return(&rpPb.RecurringPayment{
					Id: "rp-id-3",
					Interval: &types.Interval{
						EndTime: timestampPb.New(time.Now().Add(-24 * time.Hour)),
					},
				}, nil)
			},
			args: args{
				ctx:                context.Background(),
				recurringPaymentId: "rp-id-3",
			},
			want:    false,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			processor, md, release := getUpiProcessorWithMocks(t)
			tt.setupMocks(md)
			defer release()
			got, err := processor.IsExecutionAllowed(tt.args.ctx, tt.args.recurringPaymentId)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsExecutionAllowed() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("IsExecutionAllowed() got = %v, want %v", got, tt.want)
			}
		})
	}
}
