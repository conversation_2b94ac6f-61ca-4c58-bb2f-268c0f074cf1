package validationrules

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/samber/lo"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/accounts/balance/enums"
	operationalStatusEnums "github.com/epifi/gamma/api/accounts/enums"
	accountoperstatuspb "github.com/epifi/gamma/api/accounts/operstatus"
	accountPIPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/recurringpayment/dao"
	"github.com/epifi/gamma/recurringpayment/internal/domainexecutionprocessor"
)

type IValidationRule interface {
	Validate(ctx context.Context, req *ValidateReq) error
	SetNext(next IValidationRule)
}

type ValidateReq struct {
	Recurringpayment *rpPb.RecurringPayment
	Amount           *moneyPb.Money
}

type checkMandatoryParamsInValidateExecutionReq struct {
	next IValidationRule
}

func NewCheckMandatoryParamsInValidateExecutionReq() *checkMandatoryParamsInValidateExecutionReq {
	return &checkMandatoryParamsInValidateExecutionReq{}
}

func (c *checkMandatoryParamsInValidateExecutionReq) Validate(ctx context.Context, req *ValidateReq) error {
	if req.Amount == nil || req.Recurringpayment == nil {
		return epifierrors.ErrInvalidArgument
	}
	// todo(Harleen Singh): revisit this logic when recurring payment is created among 2 non-Fi accounts (for eg. TPAP account - TPAP account)
	return c.next.Validate(ctx, req)
}

func (c *checkMandatoryParamsInValidateExecutionReq) SetNext(next IValidationRule) {
	c.next = next
}

var _ IValidationRule = &checkMandatoryParamsInValidateExecutionReq{}

type executionValidationRecurringPaymentStateCheck struct {
	next IValidationRule
}

func NewExecutionValidationRecurringPaymentStateCheck() *executionValidationRecurringPaymentStateCheck {
	return &executionValidationRecurringPaymentStateCheck{}
}

var _ IValidationRule = &executionValidationRecurringPaymentStateCheck{}

func (v *executionValidationRecurringPaymentStateCheck) Validate(ctx context.Context, req *ValidateReq) error {
	// NOTE: In the newer recurring payment flows, we are allowing execution only if recurring payment is in activated state
	// todo(Harleen Singh): evaluate and deprecate the older states once the older flows are also migrated
	allowedStatesForExecution := []rpPb.RecurringPaymentState{rpPb.RecurringPaymentState_ACTIVATED}
	if !lo.Contains[rpPb.RecurringPaymentState](allowedStatesForExecution, req.Recurringpayment.GetState()) {
		switch req.Recurringpayment.GetState() {
		case rpPb.RecurringPaymentState_PAUSED:
			return ExecuteValidationErrRecurringPaymentInPausedState
		case rpPb.RecurringPaymentState_REVOKED:
			return ExecuteValidationErrRecurringPaymentInRevokedState
		case rpPb.RecurringPaymentState_EXPIRED:
			return ExecuteValidationErrRecurringPaymentInExpiredState
		case rpPb.RecurringPaymentState_FAILED:
			return ExecuteValidationErrRecurringPaymentInFailedState
		default:
			return fmt.Errorf("recurring payment can't be executed in it's current state, state : %s, %w", req.Recurringpayment.GetState(), epifierrors.ErrFailedPrecondition)
		}
	}
	return v.next.Validate(ctx, req)
}

func (v *executionValidationRecurringPaymentStateCheck) SetNext(next IValidationRule) {
	v.next = next
}

type executionValidationExecutionDateCheck struct {
	next IValidationRule
}

func NewExecutionValidationExecutionDateCheck() *executionValidationExecutionDateCheck {
	return &executionValidationExecutionDateCheck{}
}

var _ IValidationRule = &executionValidationExecutionDateCheck{}

func (v *executionValidationExecutionDateCheck) Validate(ctx context.Context, req *ValidateReq) error {
	currentTime := time.Now()
	recurringPaymentStartTime, recurringPaymentEndTime := req.Recurringpayment.GetInterval().GetStartTime(), req.Recurringpayment.GetInterval().GetEndTime()

	// current time should fall between recurring payment interval
	if currentTime.Before(recurringPaymentStartTime.AsTime()) || recurringPaymentEndTime != nil && currentTime.After(recurringPaymentEndTime.AsTime()) {
		return fmt.Errorf("current date is not between allowed interval for recurring payment execution")
	}
	return v.next.Validate(ctx, req)
}

func (v *executionValidationExecutionDateCheck) SetNext(next IValidationRule) {
	v.next = next
}

type executionValidationAmountCheck struct {
	next IValidationRule
}

func NewExecutionValidationAmountCheck() *executionValidationAmountCheck {
	return &executionValidationAmountCheck{}
}

var _ IValidationRule = &executionValidationAmountCheck{}

// executionValidationAmountCheck is responsible for checking the recurring payment Amount type and corresponding Amount passed for the execution
func (a *executionValidationAmountCheck) Validate(ctx context.Context, req *ValidateReq) error {
	diff, err := money.CompareV2(req.Recurringpayment.GetAmount(), req.Amount)
	if err != nil {
		return err
	}
	switch req.Recurringpayment.GetAmountType() {
	case rpPb.AmountType_MAXIMUM:
		if diff == -1 {
			return ExecuteValidationErrAmountGreaterThanMaxAllowedAmount
		}
	case rpPb.AmountType_EXACT:
		if diff != 0 {
			return ExecuteValidationErrAmountIsNotExact
		}
	default:
		return fmt.Errorf("unspecified Amount type,err: %w", epifierrors.ErrInvalidArgument)
	}
	return a.next.Validate(ctx, req)
}

func (a *executionValidationAmountCheck) SetNext(next IValidationRule) {
	a.next = next
}

type executionValidationDomainSpecificCheck struct {
	factory domainexecutionprocessor.DomainExecutionProcessorFactory
	next    IValidationRule
}

func NewExecutionValidationDomainSpecificCheck(factory domainexecutionprocessor.DomainExecutionProcessorFactory) *executionValidationDomainSpecificCheck {
	return &executionValidationDomainSpecificCheck{
		factory: factory,
	}
}

var _ IValidationRule = &executionValidationDomainSpecificCheck{}

func (d *executionValidationDomainSpecificCheck) Validate(ctx context.Context, req *ValidateReq) error {
	processor, processorErr := d.factory.GetProcessor(req.Recurringpayment.GetType(), req.Recurringpayment.GetPaymentRoute())
	if processorErr != nil {
		return fmt.Errorf("failed to fetch the domain processor, err: %w", processorErr)
	}
	isExecutionAllowed, isExecutionAllowedErr := processor.IsExecutionAllowed(ctx, req.Recurringpayment.Id)
	if isExecutionAllowedErr != nil {
		return fmt.Errorf("error while validation the request at domain, err: %w", isExecutionAllowedErr)
	}
	if !isExecutionAllowed {
		return ExecuteValidationErrDomainCheckFailed
	}
	return d.next.Validate(ctx, req)
}

func (d *executionValidationDomainSpecificCheck) SetNext(next IValidationRule) {
	d.next = next
}

type executionValidationRemitterAccountBalanceCheck struct {
	balanceClient   balance.BalanceClient
	accountPiClient accountPIPb.AccountPIRelationClient
	next            IValidationRule
}

func NewExecutionValidationRemitterAccountBalanceCheck(accountPiClient accountPIPb.AccountPIRelationClient, balanceClient balance.BalanceClient) *executionValidationRemitterAccountBalanceCheck {
	return &executionValidationRemitterAccountBalanceCheck{
		accountPiClient: accountPiClient,
		balanceClient:   balanceClient,
	}
}

var _ IValidationRule = &executionValidationRemitterAccountBalanceCheck{}

// executionValidationRemitterAccountBalanceCheck is responsible for checking sufficient funds only if the remitter account is an internal savings account, else ignore and move to next rule
func (b *executionValidationRemitterAccountBalanceCheck) Validate(ctx context.Context, req *ValidateReq) error {
	piFrom := req.Recurringpayment.GetPiFrom()
	// we will get account pi only for internal pis
	accountPi, accountPiErr := b.accountPiClient.GetByPiId(ctx, &accountPIPb.GetByPiIdRequest{
		PiId: piFrom,
	})
	if err := epifigrpc.RPCError(accountPi, accountPiErr); err != nil && !accountPi.GetStatus().IsRecordNotFound() {
		return fmt.Errorf("error while fetching account pi,piId: %s err: %w", piFrom, err)
	}
	// todo(Harleen Singh): refactor the logic once we have to check for non-savings accounts like credit card account
	if accountPi.GetAccountId() != "" && accountPi.GetAccountType() == accounts.Type_SAVINGS {
		hasSufficientBalance, hasSufficientBalanceErr := b.checkIfAccountHasSufficientBalance(ctx, req.Amount, req.Recurringpayment.GetFromActorId(), accountPi.GetAccountId())
		if hasSufficientBalanceErr != nil {
			return fmt.Errorf("error while checking if internal user has sufficient balance or not,err: %w", hasSufficientBalanceErr)
		}
		if !hasSufficientBalance {
			return ExecuteValidationErrInsufficientFunds
		}
	}
	return b.next.Validate(ctx, req)
}

func (b *executionValidationRemitterAccountBalanceCheck) checkIfAccountHasSufficientBalance(ctx context.Context, executionAmount *moneyPb.Money, actorId, accountId string) (bool, error) {
	// fetch the balance for the given account
	accountBalanceRes, accountBalanceErr := b.balanceClient.GetAccountBalance(ctx, &balance.GetAccountBalanceRequest{
		ActorId:       actorId,
		DataFreshness: enums.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
		Identifier: &balance.GetAccountBalanceRequest_Id{
			Id: accountId,
		},
	})
	if err := epifigrpc.RPCError(accountBalanceRes, accountBalanceErr); err != nil {
		return false, fmt.Errorf("failed to fetch the account balance,err: %w", err)
	}
	compareBalance, compareBalanceErr := money.CompareV2(executionAmount, accountBalanceRes.GetAvailableBalance())
	if compareBalanceErr != nil {
		return false, fmt.Errorf("error while comparing balance,err: %w", compareBalanceErr)
	}
	// execution Amount cannot be greater then the available balance
	if compareBalance == 1 {
		return false, nil
	}
	return true, nil
}

func (b *executionValidationRemitterAccountBalanceCheck) SetNext(next IValidationRule) {
	b.next = next
}

type executionValidationMaxExecutionCountCheck struct {
	actionDao dao.RecurringPaymentsActionDao
	next      IValidationRule
}

func NewExecutionValidationMaxCountCheck(actionDao dao.RecurringPaymentsActionDao) *executionValidationMaxExecutionCountCheck {
	return &executionValidationMaxExecutionCountCheck{
		actionDao: actionDao,
	}
}

var _ IValidationRule = &executionValidationMaxExecutionCountCheck{}

// executionValidationMaxExecutionCountCheck is responsible for checking if max allowed transactions has not been done yet based upon the recurrence rule
// NOTE: the count check is coupled with the recurrence rule, it does not mean the total number of executions done till now
// for e.g. if max allowed transactions is 2 and recurring rule frequency is daily, then in a single day, max 2 executions can happen
func (c *executionValidationMaxExecutionCountCheck) Validate(ctx context.Context, req *ValidateReq) error {
	// fetcht the sucessful actions for the given recurring payment and compare it with the maximul allowed transactions in the recurring payment
	var fromTime time.Time
	switch req.Recurringpayment.GetRecurrenceRule().GetAllowedFrequency() {
	case rpPb.AllowedFrequency_DAILY:
		fromTime = time.Now().Add(-24 * time.Hour)
	case rpPb.AllowedFrequency_WEEKLY:
		fromTime = time.Now().Add(-7 * 24 * time.Hour)
	case rpPb.AllowedFrequency_FORTNIGHTLY:
		fromTime = time.Now().Add(-14 * 24 * time.Hour)
	case rpPb.AllowedFrequency_MONTHLY:
		fromTime = time.Now().Add(-30 * 24 * time.Hour)
	case rpPb.AllowedFrequency_BI_MONTHLY:
		fromTime = time.Now().Add(-60 * 24 * time.Hour)
	case rpPb.AllowedFrequency_QUARTERLY:
		fromTime = time.Now().Add(-90 * 24 * time.Hour)
	case rpPb.AllowedFrequency_HALF_YEARLY:
		fromTime = time.Now().Add(-180 * 24 * time.Hour)
	case rpPb.AllowedFrequency_YEARLY:
		fromTime = time.Now().Add(-360 * 24 * time.Hour)
	case rpPb.AllowedFrequency_AS_PRESENTED:
		// we have to check all the executions done till now
		fromTime = time.Unix(0, 0)
	default:
		return fmt.Errorf("invalid allowed frequency,err: %w", epifierrors.ErrInvalidArgument)

	}
	// in case this value is zero, it implies that there is no max cap on number of allowed executions.
	if req.Recurringpayment.GetMaximumAllowedTxns() > 0 {
		executeActions, executeActionsErr := c.actionDao.GetByRecurringPaymentId(ctx, req.Recurringpayment.GetId(), dao.WithActionsFilter([]rpPb.Action{rpPb.Action_EXECUTE}), dao.WithActionStateFilter([]rpPb.ActionState{rpPb.ActionState_ACTION_IN_PROGRESS, rpPb.ActionState_ACTION_SUCCESS, rpPb.ActionState_ACTION_MANUAL_INTERVENTION}), dao.WithFromCreationTimeFilter(fromTime))
		if executeActionsErr != nil && !errors.Is(executeActionsErr, epifierrors.ErrRecordNotFound) {
			return executeActionsErr
		}
		if int32(len(executeActions)+1) > req.Recurringpayment.GetMaximumAllowedTxns() {
			return ExecuteValidationErrMaxExecutionCountReached
		}
	}
	return c.next.Validate(ctx, req)
}

func (c *executionValidationMaxExecutionCountCheck) SetNext(next IValidationRule) {
	c.next = next
}

type executionValidationBeneficiaryAccountStateCheck struct {
	acountOperationalStatusClient accountoperstatuspb.OperationalStatusServiceClient
	accountPiClient               accountPIPb.AccountPIRelationClient
	next                          IValidationRule
}

func NewExecutionValidationBeneficiaryAccountStateCheck(accountPiClient accountPIPb.AccountPIRelationClient, acountOperationalStatusClient accountoperstatuspb.OperationalStatusServiceClient) *executionValidationBeneficiaryAccountStateCheck {
	return &executionValidationBeneficiaryAccountStateCheck{
		accountPiClient:               accountPiClient,
		acountOperationalStatusClient: acountOperationalStatusClient,
	}
}

var _ IValidationRule = &executionValidationBeneficiaryAccountStateCheck{}

// executionValidationBeneficiaryAccountStateCheck is responsible for checking if beneficiary account is in valid state for execution only if the beneficiary account is an internal savings account
// nolint:dupl
func (a *executionValidationBeneficiaryAccountStateCheck) Validate(ctx context.Context, req *ValidateReq) error {
	// we will get account pi only for internal pis
	accountPi, accountPiErr := a.accountPiClient.GetByPiId(ctx, &accountPIPb.GetByPiIdRequest{
		PiId: req.Recurringpayment.GetPiTo(),
	})
	if err := epifigrpc.RPCError(accountPi, accountPiErr); err != nil && !accountPi.GetStatus().IsRecordNotFound() {
		return fmt.Errorf("error while fetching account pi, err: %w", err)
	}
	// todo(Harleen Singh): evaluate how to handle for other accounts like credit card
	if accountPi.GetAccountId() != "" && accountPi.GetAccountType() == accounts.Type_SAVINGS {
		accountOpearationalStatusRes, accountOpearationalStatusErr := a.acountOperationalStatusClient.GetOperationalStatus(ctx, &accountoperstatuspb.GetOperationalStatusRequest{
			DataFreshness: accountoperstatuspb.GetOperationalStatusRequest_DATA_FRESHNESS_10_MIN_STALE,
			AccountIdentifier: &accountoperstatuspb.GetOperationalStatusRequest_SavingsAccountId{
				SavingsAccountId: accountPi.GetAccountId(),
			},
		})
		// NOTE: when GetOperationalStatusResponse_ACC_NUMBER_PHONE_MISMATCH is returned, we do not have any other field in the response
		// concious call taken to ignore this case and allow enach fund transfer because credit transaction is expected to pass in the given case
		if err := epifigrpc.RPCError(accountOpearationalStatusRes, accountOpearationalStatusErr); err != nil && accountOpearationalStatusRes.GetStatus().GetCode() != uint32(accountoperstatuspb.GetOperationalStatusResponse_ACC_NUMBER_PHONE_MISMATCH) {
			return fmt.Errorf("error while fetching the account operational status,err: %w", err)
		}
		if accountOpearationalStatusRes.GetOperationalStatusInfo().GetOperationalStatus() != operationalStatusEnums.OperationalStatus_OPERATIONAL_STATUS_ACTIVE && accountOpearationalStatusRes.GetStatus().GetCode() != uint32(accountoperstatuspb.GetOperationalStatusResponse_ACC_NUMBER_PHONE_MISMATCH) {
			return ExecuteValidationErrInvalidBenaficiaryAccountState
		}
		if accountOpearationalStatusRes.GetOperationalStatusInfo().GetFreezeStatus() == operationalStatusEnums.FreezeStatus_FREEZE_STATUS_TOTAL_FREEZE || accountOpearationalStatusRes.GetOperationalStatusInfo().GetFreezeStatus() == operationalStatusEnums.FreezeStatus_FREEZE_STATUS_CREDIT_FREEZE {
			return ExecuteValidationErrInvalidBenaficiaryAccountState
		}
	}

	return a.next.Validate(ctx, req)
}

func (a *executionValidationBeneficiaryAccountStateCheck) SetNext(next IValidationRule) {
	a.next = next
}

type executionValidationRemitterAccountStateCheck struct {
	acountOperationalStatusClient accountoperstatuspb.OperationalStatusServiceClient
	accountPiClient               accountPIPb.AccountPIRelationClient
	next                          IValidationRule
}

func NewExecutionValidationRemitterAccountStateCheck(accountPiClient accountPIPb.AccountPIRelationClient, acountOperationalStatusClient accountoperstatuspb.OperationalStatusServiceClient) *executionValidationRemitterAccountStateCheck {
	return &executionValidationRemitterAccountStateCheck{
		accountPiClient:               accountPiClient,
		acountOperationalStatusClient: acountOperationalStatusClient,
	}
}

var _ IValidationRule = &executionValidationRemitterAccountStateCheck{}

// executionValidationRemitterAccountStateCheck is responsible for checking if remitter account is in valid state for execution only if the remitter account is an internal savings account
// nolint:dupl
func (a *executionValidationRemitterAccountStateCheck) Validate(ctx context.Context, req *ValidateReq) error {
	// we will get account pi only for internal pis
	accountPi, accountPiErr := a.accountPiClient.GetByPiId(ctx, &accountPIPb.GetByPiIdRequest{
		PiId: req.Recurringpayment.GetPiFrom(),
	})
	if err := epifigrpc.RPCError(accountPi, accountPiErr); err != nil && !accountPi.GetStatus().IsRecordNotFound() {
		return fmt.Errorf("error while fetching account pi, err: %w", err)
	}
	// todo(Harleen Singh): evaluate how to handle for other accounts like credit card
	if accountPi.GetAccountId() != "" && accountPi.GetAccountType() == accounts.Type_SAVINGS {
		accountOpearationalStatusRes, accountOpearationalStatusErr := a.acountOperationalStatusClient.GetOperationalStatus(ctx, &accountoperstatuspb.GetOperationalStatusRequest{
			DataFreshness: accountoperstatuspb.GetOperationalStatusRequest_DATA_FRESHNESS_10_MIN_STALE,
			AccountIdentifier: &accountoperstatuspb.GetOperationalStatusRequest_SavingsAccountId{
				SavingsAccountId: accountPi.GetAccountId(),
			},
		})
		if err := epifigrpc.RPCError(accountOpearationalStatusRes, accountOpearationalStatusErr); err != nil {
			return fmt.Errorf("error while fetching the account operational status,err: %w", err)
		}
		if accountOpearationalStatusRes.GetOperationalStatusInfo().GetOperationalStatus() != operationalStatusEnums.OperationalStatus_OPERATIONAL_STATUS_ACTIVE {
			return ExecuteValidationErrInvalidRemitterAccountState
		}
		if accountOpearationalStatusRes.GetOperationalStatusInfo().GetFreezeStatus() == operationalStatusEnums.FreezeStatus_FREEZE_STATUS_TOTAL_FREEZE || accountOpearationalStatusRes.GetOperationalStatusInfo().GetFreezeStatus() == operationalStatusEnums.FreezeStatus_FREEZE_STATUS_DEBIT_FREEZE {
			return ExecuteValidationErrInvalidRemitterAccountState
		}
	}
	return a.next.Validate(ctx, req)
}

func (a *executionValidationRemitterAccountStateCheck) SetNext(next IValidationRule) {
	a.next = next
}

type pendingActionCheck struct {
	actionDao dao.RecurringPaymentsActionDao
	next      IValidationRule
}

var _ IValidationRule = &pendingActionCheck{}

// pendingActionCheck is responsible for checking if there is any other action in non-terminal or manual intervention state. If yes, then it will not allow to create another action
func (p *pendingActionCheck) Validate(ctx context.Context, req *ValidateReq) error {
	pendingActions, pendingActionsErr := p.actionDao.GetByRecurringPaymentId(ctx, req.Recurringpayment.GetId(), dao.WithNotInActionStatesFilter([]rpPb.ActionState{rpPb.ActionState_ACTION_SUCCESS, rpPb.ActionState_ACTION_FAILURE, rpPb.ActionState_ACTION_EXPIRED, rpPb.ActionState_ACTION_REJECT}))
	if pendingActionsErr != nil && !errors.Is(pendingActionsErr, epifierrors.ErrRecordNotFound) {
		return fmt.Errorf("error while fetching recurring payment actions,err: %w", pendingActionsErr)
	}
	if len(pendingActions) != 0 {
		return ExecuteValidationErrNonTerminalActionExists
	}
	return p.next.Validate(ctx, req)
}

func (p *pendingActionCheck) SetNext(next IValidationRule) {
	p.next = next
}

func NewPendingActionCheck(actionDao dao.RecurringPaymentsActionDao) *pendingActionCheck {
	return &pendingActionCheck{
		actionDao: actionDao,
	}
}

type validationRulesTail struct {
	next IValidationRule
}

func newValidationRulesTail() *validationRulesTail {
	return &validationRulesTail{}
}

var _ IValidationRule = &validationRulesTail{}

// validationRulesTail is the last node of the chain and is responsible to end the validation chaining
func (v *validationRulesTail) Validate(ctx context.Context, req *ValidateReq) error {
	return nil
}

func (v *validationRulesTail) SetNext(next IValidationRule) {}
