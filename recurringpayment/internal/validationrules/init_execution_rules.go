package validationrules

import (
	"errors"
	"sync"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/gamma/api/accounts/balance"
	accountoperstatuspb "github.com/epifi/gamma/api/accounts/operstatus"
	accountPIPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/recurringpayment/dao"
	"github.com/epifi/gamma/recurringpayment/internal/domainexecutionprocessor"
)

// mapping of the error returned by the validation checks and the ExecuteRecurringPaymentV1 rpc

var (
	ExecuteValidationErrRecurringPaymentInPausedState     = errors.New("recurring payment is in paused state")
	ExecuteValidationErrRecurringPaymentInRevokedState    = errors.New("recurring payment is in revoked state")
	ExecuteValidationErrRecurringPaymentInExpiredState    = errors.New("recurring payment is in expired state")
	ExecuteValidationErrRecurringPaymentInFailedState     = errors.New("recurring payment is in failed state")
	ExecuteValidationErrAmountGreaterThanMaxAllowedAmount = errors.New("amount passed for execution is more than the maximum allowed Amount")
	ExecuteValidationErrAmountIsNotExact                  = errors.New("amount passed for execution is not exact as stored in recurring payment")
	ExecuteValidationErrDomainCheckFailed                 = errors.New("validation failed at domain")
	ExecuteValidationErrInsufficientFunds                 = errors.New("insufficient funds for execution")
	ExecuteValidationErrMaxExecutionCountReached          = errors.New("max number of executions has been done in the given frequency range")
	ExecuteValidationErrInvalidBenaficiaryAccountState    = errors.New("beneficiary account is not in valid state for execution")
	ExecuteValidationErrInvalidRemitterAccountState       = errors.New("remitter account is not in valid state for execution")
	ExecuteValidationErrNonTerminalActionExists           = errors.New("recurring payment already has a pending action")
)

// list of all the rules
type validationRuleName string

var (
	CHECK_MANDATORY_PARAMS                               validationRuleName = "checkMandatoryParamsInValidateExecutionReq"
	EXECUTION_VALIDATION_RECURRING_PAYMENT_STATE_CHECK   validationRuleName = "executionValidationRecurringPaymentStateCheck"
	EXECUTION_VALIDATION_EXECUTION_DATE_CHECK            validationRuleName = "executionValidationExecutionDateCheck"
	EXECUTION_VALIDATION_EXECUTION_AMOUNT_CHECK          validationRuleName = "executionValidationAmountCheck"
	EXECUTION_VALIDATION_MAX_EXECUTION_COUNT_CHECK       validationRuleName = "executionValidationMaxExecutionCountCheck"
	PENDING_ACTION_STATE_CHECK                           validationRuleName = "pendingActionCheck"
	EXECUTION_VALIDATION_BENAFICIARY_ACCOUNT_STATE_CHECK validationRuleName = "executionValidationBeneficiaryAccountStateCheck"
	EXECUTION_VALIDATION_REMITTER_ACCOUNT_STATE_CHECK    validationRuleName = "executionValidationRemitterAccountStateCheck"
	EXECUTION_VALIDATION_DOMAIN_SPECIFIC_CHECK           validationRuleName = "executionValidationDomainSpecificCheck"
	EXECUTION_VALIDATION_REMITTER_ACCOUNT_BALANCE_CHECK  validationRuleName = "executionValidationRemitterAccountBalanceCheck"
	// VALIDATION_RULES_TAIL is the last node of the chain and has no validation. It is used to gracefully exit from the validation chain once all the validations are done successfully
	VALIDATION_RULES_TAIL validationRuleName = "validationRulesTail"
)
var (
	once sync.Once
	// validation rule chain is created with validation rules in this order only,
	// **Note** : make sure to keep lighter  (in terms of processing) validation rules before heavier rules in the chain to avoid heavier rules from getting validated if lighter one fails
	validateExecutionRulesInOrder = []validationRuleName{CHECK_MANDATORY_PARAMS, EXECUTION_VALIDATION_RECURRING_PAYMENT_STATE_CHECK, EXECUTION_VALIDATION_EXECUTION_DATE_CHECK, EXECUTION_VALIDATION_MAX_EXECUTION_COUNT_CHECK, PENDING_ACTION_STATE_CHECK, EXECUTION_VALIDATION_BENAFICIARY_ACCOUNT_STATE_CHECK, EXECUTION_VALIDATION_REMITTER_ACCOUNT_STATE_CHECK, EXECUTION_VALIDATION_EXECUTION_AMOUNT_CHECK, EXECUTION_VALIDATION_DOMAIN_SPECIFIC_CHECK, EXECUTION_VALIDATION_REMITTER_ACCOUNT_BALANCE_CHECK}
	ruleNamesToRulesMap           = make(map[validationRuleName]IValidationRule)
	ErrorToExecutionStatusMap     = map[error]*rpc.Status{
		ExecuteValidationErrRecurringPaymentInPausedState:     rpc.NewStatusWithoutDebug(uint32(rpPb.ExecuteRecurringPaymentV1Response_MANDATE_PAUSED), "recurring payment is in paused state"),
		ExecuteValidationErrRecurringPaymentInRevokedState:    rpc.NewStatusWithoutDebug(uint32(rpPb.ExecuteRecurringPaymentV1Response_MANDATE_REVOKED), "recurring payment is in revoked state"),
		ExecuteValidationErrRecurringPaymentInExpiredState:    rpc.NewStatusWithoutDebug(uint32(rpPb.ExecuteRecurringPaymentV1Response_MANDATE_EXPIRED), "recurring payment is in expired state"),
		ExecuteValidationErrRecurringPaymentInFailedState:     rpc.NewStatusWithoutDebug(uint32(rpPb.ExecuteRecurringPaymentV1Response_MANDATE_FAILED), "recurring payment is in failed state"),
		ExecuteValidationErrMaxExecutionCountReached:          rpc.NewStatusWithoutDebug(uint32(rpPb.ExecuteRecurringPaymentV1Response_MAXIMUM_TRANSACTION_COUNT_BREACHED), "max number of executions has been done in the given frequency range"),
		ExecuteValidationErrAmountGreaterThanMaxAllowedAmount: rpc.NewStatusWithoutDebug(uint32(rpPb.ExecuteRecurringPaymentV1Response_MAXIMUM_AMOUNT_LIMIT_BREACHED), "Amount passed for execution is more than the maximum allowed Amount"),
	}
)

// InitExecutionValidationChain initialises the recurring payment execution validation rule chain
func InitExecutionValidationChain(rpActionDao dao.RecurringPaymentsActionDao, acountOperationalStatusClient accountoperstatuspb.OperationalStatusServiceClient, factory domainexecutionprocessor.DomainExecutionProcessorFactory, accountPiClient accountPIPb.AccountPIRelationClient, balanceClient balance.BalanceClient) IValidationRule {
	once.Do(func() {
		ruleNamesToRulesMap[CHECK_MANDATORY_PARAMS] = NewCheckMandatoryParamsInValidateExecutionReq()
		ruleNamesToRulesMap[EXECUTION_VALIDATION_RECURRING_PAYMENT_STATE_CHECK] = NewExecutionValidationRecurringPaymentStateCheck()
		ruleNamesToRulesMap[EXECUTION_VALIDATION_EXECUTION_DATE_CHECK] = NewExecutionValidationExecutionDateCheck()
		ruleNamesToRulesMap[EXECUTION_VALIDATION_EXECUTION_AMOUNT_CHECK] = NewExecutionValidationAmountCheck()
		ruleNamesToRulesMap[EXECUTION_VALIDATION_MAX_EXECUTION_COUNT_CHECK] = NewExecutionValidationMaxCountCheck(rpActionDao)
		ruleNamesToRulesMap[PENDING_ACTION_STATE_CHECK] = NewPendingActionCheck(rpActionDao)
		ruleNamesToRulesMap[EXECUTION_VALIDATION_BENAFICIARY_ACCOUNT_STATE_CHECK] = NewExecutionValidationBeneficiaryAccountStateCheck(accountPiClient, acountOperationalStatusClient)
		ruleNamesToRulesMap[EXECUTION_VALIDATION_REMITTER_ACCOUNT_STATE_CHECK] = NewExecutionValidationRemitterAccountStateCheck(accountPiClient, acountOperationalStatusClient)
		ruleNamesToRulesMap[EXECUTION_VALIDATION_DOMAIN_SPECIFIC_CHECK] = NewExecutionValidationDomainSpecificCheck(factory)
		ruleNamesToRulesMap[EXECUTION_VALIDATION_REMITTER_ACCOUNT_BALANCE_CHECK] = NewExecutionValidationRemitterAccountBalanceCheck(accountPiClient, balanceClient)
		ruleNamesToRulesMap[VALIDATION_RULES_TAIL] = newValidationRulesTail()
		// append the tail as the last node
		validateExecutionRulesInOrder = append(validateExecutionRulesInOrder, VALIDATION_RULES_TAIL)
		// join the links of the validation nodes
		for i := 0; i < len(validateExecutionRulesInOrder)-1; i++ {
			ruleNamesToRulesMap[validateExecutionRulesInOrder[i]].SetNext(ruleNamesToRulesMap[validateExecutionRulesInOrder[i+1]])
		}

	})
	return ruleNamesToRulesMap[validateExecutionRulesInOrder[0]]
}
