// Code generated by MockGen. DO NOT EDIT.
// Source: recurringpayment/internal/validationrules/validate_execution_v1.go

// Package mock_validationrules is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	validationrules "github.com/epifi/gamma/recurringpayment/internal/validationrules"
	gomock "github.com/golang/mock/gomock"
)

// MockIValidationRule is a mock of IValidationRule interface.
type MockIValidationRule struct {
	ctrl     *gomock.Controller
	recorder *MockIValidationRuleMockRecorder
}

// MockIValidationRuleMockRecorder is the mock recorder for MockIValidationRule.
type MockIValidationRuleMockRecorder struct {
	mock *MockIValidationRule
}

// NewMockIValidationRule creates a new mock instance.
func NewMockIValidationRule(ctrl *gomock.Controller) *MockIValidationRule {
	mock := &MockIValidationRule{ctrl: ctrl}
	mock.recorder = &MockIValidationRuleMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIValidationRule) EXPECT() *MockIValidationRuleMockRecorder {
	return m.recorder
}

// SetNext mocks base method.
func (m *MockIValidationRule) SetNext(next validationrules.IValidationRule) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetNext", next)
}

// SetNext indicates an expected call of SetNext.
func (mr *MockIValidationRuleMockRecorder) SetNext(next interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNext", reflect.TypeOf((*MockIValidationRule)(nil).SetNext), next)
}

// Validate mocks base method.
func (m *MockIValidationRule) Validate(ctx context.Context, req *validationrules.ValidateReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Validate", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// Validate indicates an expected call of Validate.
func (mr *MockIValidationRuleMockRecorder) Validate(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Validate", reflect.TypeOf((*MockIValidationRule)(nil).Validate), ctx, req)
}
