package validationrules_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	moneypb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/accounts/balance"
	enums3 "github.com/epifi/gamma/api/accounts/balance/enums"
	balanceClientMocks "github.com/epifi/gamma/api/accounts/balance/mocks"
	enums2 "github.com/epifi/gamma/api/accounts/enums"
	"github.com/epifi/gamma/api/accounts/operstatus"
	mockAccountClient "github.com/epifi/gamma/api/accounts/operstatus/mocks"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	accountPiMocks "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/recurringpayment/dao"
	actionDaoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	domainExecutionFactoryMocks "github.com/epifi/gamma/recurringpayment/internal/domainexecutionprocessor/mocks"
	"github.com/epifi/gamma/recurringpayment/internal/validationrules"
	"github.com/epifi/gamma/recurringpayment/internal/validationrules/mocks"
)

var (
	currentTime       = time.Now()
	time10MinsEarlier = currentTime.Add(-10 * time.Minute)
	time10MinsLater   = currentTime.Add(10 * time.Minute)
	time20MinsLater   = currentTime.Add(20 * time.Minute)
)

func Test_recurringPaymentStateCheck_validate(t *testing.T) {
	type args struct {
		ctx context.Context
		req *validationrules.ValidateReq
	}
	var (
		defaultRecurringPaymentId = "default-recurring-payment-id"
	)
	tests := []struct {
		name       string
		args       args
		setupMocks func(rule *mocks.MockIValidationRule)
		wantErr    error
	}{
		{
			name: "should fail (recurring payment in paused state)",
			args: args{
				ctx: context.Background(),
				req: &validationrules.ValidateReq{
					Recurringpayment: &rpPb.RecurringPayment{
						Id:    defaultRecurringPaymentId,
						State: rpPb.RecurringPaymentState_PAUSED,
					},
				},
			},
			wantErr: validationrules.ExecuteValidationErrRecurringPaymentInPausedState,
		},
		{
			name: "should fail (recurring payment in revoked state)",
			args: args{
				ctx: context.Background(),
				req: &validationrules.ValidateReq{
					Recurringpayment: &rpPb.RecurringPayment{
						Id:    defaultRecurringPaymentId,
						State: rpPb.RecurringPaymentState_REVOKED,
					},
				},
			},
			wantErr: validationrules.ExecuteValidationErrRecurringPaymentInRevokedState,
		},
		{
			name: "should fail (recurring payment in expired state)",
			args: args{
				ctx: context.Background(),
				req: &validationrules.ValidateReq{
					Recurringpayment: &rpPb.RecurringPayment{
						Id:    defaultRecurringPaymentId,
						State: rpPb.RecurringPaymentState_EXPIRED,
					},
				},
			},
			wantErr: validationrules.ExecuteValidationErrRecurringPaymentInExpiredState,
		},
		{
			name: "should fail (recurring payment in failed state)",
			args: args{
				ctx: context.Background(),
				req: &validationrules.ValidateReq{
					Recurringpayment: &rpPb.RecurringPayment{
						Id:    defaultRecurringPaymentId,
						State: rpPb.RecurringPaymentState_FAILED,
					},
				},
			},
			wantErr: validationrules.ExecuteValidationErrRecurringPaymentInFailedState,
		},
		{
			name: "should fail (recurring payment in non-execution unknown state)",
			args: args{
				ctx: context.Background(),
				req: &validationrules.ValidateReq{
					Recurringpayment: &rpPb.RecurringPayment{
						Id:    defaultRecurringPaymentId,
						State: rpPb.RecurringPaymentState_PAUSE_QUEUED,
					},
				},
			},
			wantErr: epifierrors.ErrFailedPrecondition,
		},
		{
			name: "should pass (recurring payment activated state)",
			args: args{
				ctx: context.Background(),
				req: &validationrules.ValidateReq{
					Recurringpayment: &rpPb.RecurringPayment{
						Id:    defaultRecurringPaymentId,
						State: rpPb.RecurringPaymentState_ACTIVATED,
					},
				},
			},
			setupMocks: func(rule *mocks.MockIValidationRule) {
				rule.EXPECT().Validate(context.Background(), &validationrules.ValidateReq{
					Recurringpayment: &rpPb.RecurringPayment{
						Id:    defaultRecurringPaymentId,
						State: rpPb.RecurringPaymentState_ACTIVATED,
					},
				}).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			validationRule := mocks.NewMockIValidationRule(ctr)
			if tt.setupMocks != nil {
				tt.setupMocks(validationRule)
			}
			v := validationrules.NewExecutionValidationRecurringPaymentStateCheck()
			v.SetNext(validationRule)
			if err := v.Validate(tt.args.ctx, tt.args.req); !errors.Is(err, tt.wantErr) {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_checkMandatoryParamsInValidateExecutionReq_validate(t *testing.T) {
	type args struct {
		ctx context.Context
		req *validationrules.ValidateReq
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(rule *mocks.MockIValidationRule)
		wantErr    error
	}{
		{
			name: "should return error (Amount is empty)",
			args: args{
				ctx: context.Background(),
				req: &validationrules.ValidateReq{
					Recurringpayment: &rpPb.RecurringPayment{},
				},
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "should return error (recurring payment is nil)",
			args: args{
				ctx: context.Background(),
				req: &validationrules.ValidateReq{
					Amount: &moneypb.Money{},
				},
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "should pass (all mandatory params are present)",
			args: args{
				ctx: context.Background(),
				req: &validationrules.ValidateReq{
					Recurringpayment: &rpPb.RecurringPayment{},
					Amount:           &moneypb.Money{},
				},
			},
			setupMocks: func(rule *mocks.MockIValidationRule) {
				rule.EXPECT().Validate(context.Background(), &validationrules.ValidateReq{
					Recurringpayment: &rpPb.RecurringPayment{},
					Amount:           &moneypb.Money{},
				}).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			validationRule := mocks.NewMockIValidationRule(ctr)
			if tt.setupMocks != nil {
				tt.setupMocks(validationRule)
			}
			c := validationrules.NewCheckMandatoryParamsInValidateExecutionReq()
			c.SetNext(validationRule)
			if err := c.Validate(tt.args.ctx, tt.args.req); !errors.Is(err, tt.wantErr) {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_executionAmountCheck_validate(t *testing.T) {
	type args struct {
		ctx context.Context
		req *validationrules.ValidateReq
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(rule *mocks.MockIValidationRule)
		wantErr    error
	}{
		{
			name: "should fail (error returned from comparison method)",
			args: args{
				ctx: context.Background(),
				req: &validationrules.ValidateReq{
					Recurringpayment: &rpPb.RecurringPayment{},
				},
			},
			wantErr: money.ErrNilInput,
		},
		{
			name: "should fail (Amount greater than max Amount limit)",
			args: args{
				ctx: context.Background(),
				req: &validationrules.ValidateReq{
					Recurringpayment: &rpPb.RecurringPayment{
						Id:         "recurring-payment-id",
						AmountType: rpPb.AmountType_MAXIMUM,
						Amount: &moneypb.Money{
							CurrencyCode: "IST",
							Units:        100,
						},
					},
					Amount: &moneypb.Money{
						CurrencyCode: "IST",
						Units:        200,
					},
				},
			},
			wantErr: validationrules.ExecuteValidationErrAmountGreaterThanMaxAllowedAmount,
		},
		{
			name: "should fail (Amount is not exact)",
			args: args{
				ctx: context.Background(),
				req: &validationrules.ValidateReq{
					Recurringpayment: &rpPb.RecurringPayment{
						AmountType: rpPb.AmountType_EXACT,
						Amount: &moneypb.Money{
							CurrencyCode: "IST",
							Units:        100,
						},
					},
					Amount: &moneypb.Money{
						CurrencyCode: "IST",
						Units:        200,
					},
				},
			},
			wantErr: validationrules.ExecuteValidationErrAmountIsNotExact,
		},
		{
			name: "should fail (unspecified Amount type)",
			args: args{
				ctx: context.Background(),
				req: &validationrules.ValidateReq{
					Recurringpayment: &rpPb.RecurringPayment{
						Amount: &moneypb.Money{
							CurrencyCode: "IST",
							Units:        100,
						},
					},
					Amount: &moneypb.Money{
						CurrencyCode: "IST",
						Units:        200,
					},
				},
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "should pass (exact Amount)",
			args: args{
				ctx: context.Background(),
				req: &validationrules.ValidateReq{
					Recurringpayment: &rpPb.RecurringPayment{
						AmountType: rpPb.AmountType_EXACT,
						Amount: &moneypb.Money{
							CurrencyCode: "IST",
							Units:        100,
						},
					},
					Amount: &moneypb.Money{
						CurrencyCode: "IST",
						Units:        100,
					},
				},
			},
			setupMocks: func(rule *mocks.MockIValidationRule) {
				rule.EXPECT().Validate(context.Background(), &validationrules.ValidateReq{
					Recurringpayment: &rpPb.RecurringPayment{
						AmountType: rpPb.AmountType_EXACT,
						Amount: &moneypb.Money{
							CurrencyCode: "IST",
							Units:        100,
						},
					},
					Amount: &moneypb.Money{
						CurrencyCode: "IST",
						Units:        100,
					},
				}).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			validationRule := mocks.NewMockIValidationRule(ctr)
			if tt.setupMocks != nil {
				tt.setupMocks(validationRule)
			}
			a := validationrules.NewExecutionValidationAmountCheck()
			a.SetNext(validationRule)
			if err := a.Validate(tt.args.ctx, tt.args.req); !errors.Is(err, tt.wantErr) {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_domainSpecificCheck_validate(t *testing.T) {
	const (
		recurringPaymentId = "recurring-payment-id-1"
	)
	type args struct {
		ctx context.Context
		req *validationrules.ValidateReq
	}
	var (
		defaultReq = &validationrules.ValidateReq{
			Recurringpayment: &rpPb.RecurringPayment{
				Id:   recurringPaymentId,
				Type: rpPb.RecurringPaymentType_ENACH_MANDATES,
			},
		}
	)
	tests := []struct {
		name       string
		args       args
		wantErr    error
		setupMocks func(factory *domainExecutionFactoryMocks.MockDomainExecutionProcessorFactory, processor *domainExecutionFactoryMocks.MockDomainExecutionProcessor, rule *mocks.MockIValidationRule)
	}{
		{
			name: "should fail (error while fetching the processor)",
			args: args{
				ctx: context.Background(),
				req: defaultReq,
			},
			setupMocks: func(factory *domainExecutionFactoryMocks.MockDomainExecutionProcessorFactory, processor *domainExecutionFactoryMocks.MockDomainExecutionProcessor, rule *mocks.MockIValidationRule) {
				factory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_ENACH_MANDATES, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).
					Return(nil, epifierrors.ErrInvalidArgument)
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "should fail (error while processing validation at domain)",
			args: args{
				ctx: context.Background(),
				req: defaultReq,
			},
			setupMocks: func(factory *domainExecutionFactoryMocks.MockDomainExecutionProcessorFactory, processor *domainExecutionFactoryMocks.MockDomainExecutionProcessor, rule *mocks.MockIValidationRule) {
				factory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_ENACH_MANDATES, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).
					Return(processor, nil)
				processor.EXPECT().IsExecutionAllowed(context.Background(), recurringPaymentId).
					Return(false, epifierrors.ErrInvalidArgument)
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "should fail (validation failed at domain)",
			args: args{
				ctx: context.Background(),
				req: defaultReq,
			},
			setupMocks: func(factory *domainExecutionFactoryMocks.MockDomainExecutionProcessorFactory, processor *domainExecutionFactoryMocks.MockDomainExecutionProcessor, rule *mocks.MockIValidationRule) {
				factory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_ENACH_MANDATES, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).
					Return(processor, nil)
				processor.EXPECT().IsExecutionAllowed(context.Background(), recurringPaymentId).
					Return(false, nil)
			},
			wantErr: validationrules.ExecuteValidationErrDomainCheckFailed,
		},
		{
			name: "should pass (validations passed at domain)",
			args: args{
				ctx: context.Background(),
				req: defaultReq,
			},
			setupMocks: func(factory *domainExecutionFactoryMocks.MockDomainExecutionProcessorFactory, processor *domainExecutionFactoryMocks.MockDomainExecutionProcessor, rule *mocks.MockIValidationRule) {
				factory.EXPECT().GetProcessor(rpPb.RecurringPaymentType_ENACH_MANDATES, rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_UNSPECIFIED).
					Return(processor, nil)
				processor.EXPECT().IsExecutionAllowed(context.Background(), recurringPaymentId).
					Return(true, nil)
				rule.EXPECT().Validate(context.Background(), defaultReq).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockFactory := domainExecutionFactoryMocks.NewMockDomainExecutionProcessorFactory(ctr)
			mockProcessor := domainExecutionFactoryMocks.NewMockDomainExecutionProcessor(ctr)
			validationRule := mocks.NewMockIValidationRule(ctr)
			tt.setupMocks(mockFactory, mockProcessor, validationRule)
			d := validationrules.NewExecutionValidationDomainSpecificCheck(mockFactory)
			d.SetNext(validationRule)
			if err := d.Validate(tt.args.ctx, tt.args.req); !errors.Is(err, tt.wantErr) {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_remitterAccountBalanceCheck_validate(t *testing.T) {
	var (
		defaultFromActorSavingsAccountId = "deault-from-actor-savings-account-id"
		defaultAmount                    = &moneypb.Money{
			CurrencyCode: money.RupeeCurrencyCode,
			Units:        100,
		}
		defaultFromActorId = "default-from-actor-id"
		defaultPiFrom      = "default-pi-from"
		defaultReq         = &validationrules.ValidateReq{
			Amount: defaultAmount,
			Recurringpayment: &rpPb.RecurringPayment{
				FromActorId: defaultFromActorId,
				PiFrom:      defaultPiFrom,
			},
		}
		defaultGetByPiIdReq = &account_pi.GetByPiIdRequest{
			PiId: defaultPiFrom,
		}
		defaultGetByPiIdRes = &account_pi.GetByPiIdResponse{
			Status:      rpc.StatusOk(),
			AccountId:   defaultFromActorSavingsAccountId,
			AccountType: accounts.Type_SAVINGS,
		}
		defaultGetBalanceReq = &balance.GetAccountBalanceRequest{
			ActorId:       defaultFromActorId,
			DataFreshness: enums3.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
			Identifier: &balance.GetAccountBalanceRequest_Id{
				Id: defaultFromActorSavingsAccountId,
			},
		}
	)
	type args struct {
		ctx context.Context
		req *validationrules.ValidateReq
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, balanceClient *balanceClientMocks.MockBalanceClient, rule *mocks.MockIValidationRule)
		err        error
		wantErr    bool
	}{
		{
			name: "should fail (error while fetching account pi info)",
			args: args{ctx: context.Background(), req: defaultReq},
			setupMocks: func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, balanceClient *balanceClientMocks.MockBalanceClient, rule *mocks.MockIValidationRule) {
				accountPiClient.EXPECT().GetByPiId(context.Background(), defaultGetByPiIdReq).
					Return(&account_pi.GetByPiIdResponse{Status: rpc.StatusInternal()}, nil)
			},
			wantErr: true,
		},
		{
			name: "should pass (account id is empty)",
			args: args{ctx: context.Background(), req: defaultReq},
			setupMocks: func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, balanceClient *balanceClientMocks.MockBalanceClient, rule *mocks.MockIValidationRule) {
				accountPiClient.EXPECT().GetByPiId(context.Background(), defaultGetByPiIdReq).
					Return(&account_pi.GetByPiIdResponse{Status: rpc.StatusRecordNotFound()}, nil)
				rule.EXPECT().Validate(context.Background(), defaultReq).Return(nil)
			},
		},
		{
			name: "should pass (account is not of savings type)",
			args: args{ctx: context.Background(), req: defaultReq},
			setupMocks: func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, balanceClient *balanceClientMocks.MockBalanceClient, rule *mocks.MockIValidationRule) {
				accountPiClient.EXPECT().GetByPiId(context.Background(), defaultGetByPiIdReq).
					Return(&account_pi.GetByPiIdResponse{Status: rpc.StatusOk(), AccountId: "credit-card-account", AccountType: accounts.Type_CREDIT_CARD_ACCOUNT}, nil)
				rule.EXPECT().Validate(context.Background(), defaultReq).Return(nil)
			},
		},
		{
			name: "should fail (error while fetching the balance)",
			args: args{ctx: context.Background(), req: defaultReq},
			setupMocks: func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, balanceClient *balanceClientMocks.MockBalanceClient, rule *mocks.MockIValidationRule) {
				accountPiClient.EXPECT().GetByPiId(context.Background(), defaultGetByPiIdReq).Return(defaultGetByPiIdRes, nil)
				balanceClient.EXPECT().GetAccountBalance(context.Background(), defaultGetBalanceReq).Return(&balance.GetAccountBalanceResponse{Status: rpc.StatusInternal()}, nil)
			},
			wantErr: true,
		},
		{
			name: "should fail (error while comparing the balance)",
			args: args{ctx: context.Background(), req: defaultReq},
			setupMocks: func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, balanceClient *balanceClientMocks.MockBalanceClient, rule *mocks.MockIValidationRule) {
				accountPiClient.EXPECT().GetByPiId(context.Background(), defaultGetByPiIdReq).Return(defaultGetByPiIdRes, nil)
				balanceClient.EXPECT().GetAccountBalance(context.Background(), defaultGetBalanceReq).Return(&balance.GetAccountBalanceResponse{Status: rpc.StatusOk(),
					AvailableBalance: &moneypb.Money{}}, nil)
			},
			wantErr: true,
		},
		{
			name: "should fail (available balance is less then execution Amount)",
			args: args{ctx: context.Background(), req: defaultReq},
			setupMocks: func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, balanceClient *balanceClientMocks.MockBalanceClient, rule *mocks.MockIValidationRule) {
				accountPiClient.EXPECT().GetByPiId(context.Background(), defaultGetByPiIdReq).Return(defaultGetByPiIdRes, nil)
				balanceClient.EXPECT().GetAccountBalance(context.Background(), defaultGetBalanceReq).Return(&balance.GetAccountBalanceResponse{Status: rpc.StatusOk(),
					AvailableBalance: &moneypb.Money{
						CurrencyCode: money.RupeeCurrencyCode,
						Units:        1,
					}}, nil)
			},
			wantErr: true,
			err:     validationrules.ExecuteValidationErrInsufficientFunds,
		},
		{
			name: "should pass (available balance is more then execution Amount)",
			args: args{ctx: context.Background(), req: defaultReq},
			setupMocks: func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, balanceClient *balanceClientMocks.MockBalanceClient, rule *mocks.MockIValidationRule) {
				accountPiClient.EXPECT().GetByPiId(context.Background(), defaultGetByPiIdReq).Return(defaultGetByPiIdRes, nil)
				balanceClient.EXPECT().GetAccountBalance(context.Background(), defaultGetBalanceReq).Return(&balance.GetAccountBalanceResponse{Status: rpc.StatusOk(),
					AvailableBalance: &moneypb.Money{
						CurrencyCode: money.RupeeCurrencyCode,
						Units:        10000,
					}}, nil)
				rule.EXPECT().Validate(context.Background(), defaultReq).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			balanceClient := balanceClientMocks.NewMockBalanceClient(ctr)
			accountPiclient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
			rule := mocks.NewMockIValidationRule(ctr)
			tt.setupMocks(accountPiclient, balanceClient, rule)
			b := validationrules.NewExecutionValidationRemitterAccountBalanceCheck(accountPiclient, balanceClient)
			b.SetNext(rule)
			err := b.Validate(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.err != nil && !errors.Is(tt.err, err) {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_maxExecutionCountCheck_validate(t *testing.T) {
	var (
		defaultRecurringPaymentId = "default-recurring-payment-id"
		defaultValidateReq        = &validationrules.ValidateReq{
			Recurringpayment: &rpPb.RecurringPayment{
				Id: defaultRecurringPaymentId,
				RecurrenceRule: &rpPb.RecurrenceRule{
					AllowedFrequency: rpPb.AllowedFrequency_AS_PRESENTED,
				},
				MaximumAllowedTxns: 1,
			},
		}
	)
	type args struct {
		ctx context.Context
		req *validationrules.ValidateReq
	}
	tests := []struct {
		name       string
		args       args
		wantErr    error
		setupMocks func(actionDao *actionDaoMocks.MockRecurringPaymentsActionDao, rule *mocks.MockIValidationRule)
	}{
		{
			name: "should fail (unspecified allowed frequency)",
			args: args{
				ctx: context.Background(),
				req: &validationrules.ValidateReq{Recurringpayment: &rpPb.RecurringPayment{}},
			},
			setupMocks: func(actionDao *actionDaoMocks.MockRecurringPaymentsActionDao, rule *mocks.MockIValidationRule) {},
			wantErr:    epifierrors.ErrInvalidArgument,
		},
		{
			name: "should fail (error while fetching execution actions)",
			args: args{
				ctx: context.Background(),
				req: defaultValidateReq,
			},
			setupMocks: func(actionDao *actionDaoMocks.MockRecurringPaymentsActionDao, rule *mocks.MockIValidationRule) {
				actionDao.EXPECT().GetByRecurringPaymentId(context.Background(), defaultRecurringPaymentId, gomock.AssignableToTypeOf(dao.WithActionsFilter([]rpPb.Action{rpPb.Action_EXECUTE})), gomock.AssignableToTypeOf(dao.WithActionStateFilter([]rpPb.ActionState{rpPb.ActionState_ACTION_IN_PROGRESS, rpPb.ActionState_ACTION_SUCCESS, rpPb.ActionState_ACTION_MANUAL_INTERVENTION})), gomock.Any()).
					Return(nil, epifierrors.ErrInvalidArgument)
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "validation should fail (max executions has already done)",
			args: args{
				ctx: context.Background(),
				req: defaultValidateReq,
			},
			setupMocks: func(actionDao *actionDaoMocks.MockRecurringPaymentsActionDao, rule *mocks.MockIValidationRule) {
				actionDao.EXPECT().GetByRecurringPaymentId(context.Background(), defaultRecurringPaymentId, gomock.AssignableToTypeOf(dao.WithActionsFilter([]rpPb.Action{rpPb.Action_EXECUTE})), gomock.AssignableToTypeOf(dao.WithActionStateFilter([]rpPb.ActionState{rpPb.ActionState_ACTION_IN_PROGRESS, rpPb.ActionState_ACTION_SUCCESS, rpPb.ActionState_ACTION_MANUAL_INTERVENTION})), gomock.Any()).
					Return([]*rpPb.RecurringPaymentsAction{{}}, nil)
			},
			wantErr: validationrules.ExecuteValidationErrMaxExecutionCountReached,
		},
		{
			name: "should pass",
			args: args{
				ctx: context.Background(),
				req: defaultValidateReq,
			},
			setupMocks: func(actionDao *actionDaoMocks.MockRecurringPaymentsActionDao, rule *mocks.MockIValidationRule) {
				actionDao.EXPECT().GetByRecurringPaymentId(context.Background(), defaultRecurringPaymentId, gomock.AssignableToTypeOf(dao.WithActionsFilter([]rpPb.Action{rpPb.Action_EXECUTE})), gomock.AssignableToTypeOf(dao.WithActionStateFilter([]rpPb.ActionState{rpPb.ActionState_ACTION_IN_PROGRESS, rpPb.ActionState_ACTION_SUCCESS, rpPb.ActionState_ACTION_MANUAL_INTERVENTION})), gomock.Any()).
					Return(nil, epifierrors.ErrRecordNotFound)
				rule.EXPECT().Validate(context.Background(), defaultValidateReq).Return(nil)
			},
		},
		{
			name: "should pass when no max cap on executions is configured",
			args: args{
				ctx: context.Background(),
				req: &validationrules.ValidateReq{
					Recurringpayment: &rpPb.RecurringPayment{
						Id: defaultRecurringPaymentId,
						RecurrenceRule: &rpPb.RecurrenceRule{
							AllowedFrequency: rpPb.AllowedFrequency_AS_PRESENTED,
						},
						// zero value implies that no max cap is configured.
						MaximumAllowedTxns: 0,
					},
				},
			},
			setupMocks: func(actionDao *actionDaoMocks.MockRecurringPaymentsActionDao, rule *mocks.MockIValidationRule) {
				rule.EXPECT().Validate(context.Background(), &validationrules.ValidateReq{
					Recurringpayment: &rpPb.RecurringPayment{
						Id: defaultRecurringPaymentId,
						RecurrenceRule: &rpPb.RecurrenceRule{
							AllowedFrequency: rpPb.AllowedFrequency_AS_PRESENTED,
						},
						// zero value implies that no max cap is configured.
						MaximumAllowedTxns: 0,
					},
				}).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			actionDao := actionDaoMocks.NewMockRecurringPaymentsActionDao(ctr)
			rule := mocks.NewMockIValidationRule(ctr)
			tt.setupMocks(actionDao, rule)
			c := validationrules.NewExecutionValidationMaxCountCheck(actionDao)
			c.SetNext(rule)
			if err := c.Validate(tt.args.ctx, tt.args.req); !errors.Is(err, tt.wantErr) {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_benaficiaryAccountStateCheck_validate(t *testing.T) {
	var (
		defaultToActorSavingsAccountId = "default-to-actor-savings-account-id"
		defaultPiTo                    = "default-pi-to"
		defaultReq                     = &validationrules.ValidateReq{
			Recurringpayment: &rpPb.RecurringPayment{
				ToActorId: "default-to-actor-id",
				PiTo:      defaultPiTo,
			},
		}
		defaultGetByPiIdReq = &account_pi.GetByPiIdRequest{
			PiId: defaultPiTo,
		}
		defaultGetByPiIdRes = &account_pi.GetByPiIdResponse{
			Status:      rpc.StatusOk(),
			AccountId:   defaultToActorSavingsAccountId,
			AccountType: accounts.Type_SAVINGS,
		}
		defaultGetOperationalStatusReq = &operstatus.GetOperationalStatusRequest{
			DataFreshness: operstatus.GetOperationalStatusRequest_DATA_FRESHNESS_10_MIN_STALE,
			AccountIdentifier: &operstatus.GetOperationalStatusRequest_SavingsAccountId{
				SavingsAccountId: defaultToActorSavingsAccountId,
			},
		}
	)
	type args struct {
		ctx context.Context
		req *validationrules.ValidateReq
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, acountOperationalStatusClient *mockAccountClient.MockOperationalStatusServiceClient, rule *mocks.MockIValidationRule)
		wantErr    bool
		err        error
	}{
		{
			name: "should fail (error while fetching account pi info)",
			args: args{
				ctx: context.Background(),
				req: defaultReq,
			},
			setupMocks: func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, acountOperationalStatusClient *mockAccountClient.MockOperationalStatusServiceClient, rule *mocks.MockIValidationRule) {
				accountPiClient.EXPECT().GetByPiId(context.Background(), defaultGetByPiIdReq).Return(&account_pi.GetByPiIdResponse{Status: rpc.StatusInternal()}, nil)
			},
			wantErr: true,
		},
		{
			name: "should pass (account id is empty)",
			args: args{
				ctx: context.Background(),
				req: defaultReq,
			},
			setupMocks: func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, acountOperationalStatusClient *mockAccountClient.MockOperationalStatusServiceClient, rule *mocks.MockIValidationRule) {
				accountPiClient.EXPECT().GetByPiId(context.Background(), defaultGetByPiIdReq).Return(&account_pi.GetByPiIdResponse{Status: rpc.StatusRecordNotFound()}, nil)
				rule.EXPECT().Validate(context.Background(), defaultReq).Return(nil)
			},
		},
		{
			name: "should pass (account id is not of type savings)",
			args: args{
				ctx: context.Background(),
				req: defaultReq,
			},
			setupMocks: func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, acountOperationalStatusClient *mockAccountClient.MockOperationalStatusServiceClient, rule *mocks.MockIValidationRule) {
				accountPiClient.EXPECT().GetByPiId(context.Background(), defaultGetByPiIdReq).Return(&account_pi.GetByPiIdResponse{Status: rpc.StatusOk(), AccountId: "credit-card-account", AccountType: accounts.Type_CREDIT_CARD_ACCOUNT}, nil)
				rule.EXPECT().Validate(context.Background(), defaultReq).Return(nil)
			},
		},
		{
			name: "should fail (failed to fetch the account state)",
			args: args{
				ctx: context.Background(),
				req: defaultReq,
			},
			setupMocks: func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, accountOperationalStatusClient *mockAccountClient.MockOperationalStatusServiceClient, rule *mocks.MockIValidationRule) {
				accountPiClient.EXPECT().GetByPiId(context.Background(), defaultGetByPiIdReq).Return(defaultGetByPiIdRes, nil)
				accountOperationalStatusClient.EXPECT().GetOperationalStatus(context.Background(), defaultGetOperationalStatusReq).Return(&operstatus.GetOperationalStatusResponse{Status: rpc.StatusInternal()}, nil)
			},
			wantErr: true,
		},
		{
			name: "should fail (beneficiary account operational status is not active)",
			args: args{
				ctx: context.Background(),
				req: defaultReq,
			},
			setupMocks: func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, acountOperationalStatusClient *mockAccountClient.MockOperationalStatusServiceClient, rule *mocks.MockIValidationRule) {
				accountPiClient.EXPECT().GetByPiId(context.Background(), defaultGetByPiIdReq).Return(defaultGetByPiIdRes, nil)
				acountOperationalStatusClient.EXPECT().GetOperationalStatus(context.Background(), defaultGetOperationalStatusReq).
					Return(&operstatus.GetOperationalStatusResponse{Status: rpc.StatusOk()}, nil)
			},
			wantErr: true,
			err:     validationrules.ExecuteValidationErrInvalidBenaficiaryAccountState,
		},
		{
			name: "should fail (beneficiary account is in frozen status)",
			args: args{
				ctx: context.Background(),
				req: defaultReq,
			},
			setupMocks: func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, acountOperationalStatusClient *mockAccountClient.MockOperationalStatusServiceClient, rule *mocks.MockIValidationRule) {
				accountPiClient.EXPECT().GetByPiId(context.Background(), defaultGetByPiIdReq).Return(defaultGetByPiIdRes, nil)
				acountOperationalStatusClient.EXPECT().GetOperationalStatus(context.Background(), defaultGetOperationalStatusReq).
					Return(&operstatus.GetOperationalStatusResponse{Status: rpc.StatusOk(),
						OperationalStatusInfo: &operstatus.OperationalStatusInfo{
							FreezeStatus: enums2.FreezeStatus_FREEZE_STATUS_TOTAL_FREEZE,
						}}, nil)
			},
			wantErr: true,
			err:     validationrules.ExecuteValidationErrInvalidBenaficiaryAccountState,
		},
		{
			name: "should pass",
			args: args{
				ctx: context.Background(),
				req: defaultReq,
			},
			setupMocks: func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, acountOperationalStatusClient *mockAccountClient.MockOperationalStatusServiceClient, rule *mocks.MockIValidationRule) {
				accountPiClient.EXPECT().GetByPiId(context.Background(), defaultGetByPiIdReq).Return(defaultGetByPiIdRes, nil)
				acountOperationalStatusClient.EXPECT().GetOperationalStatus(context.Background(), defaultGetOperationalStatusReq).
					Return(&operstatus.GetOperationalStatusResponse{Status: rpc.StatusOk(),
						OperationalStatusInfo: &operstatus.OperationalStatusInfo{
							OperationalStatus: enums2.OperationalStatus_OPERATIONAL_STATUS_ACTIVE,
						}}, nil)
				rule.EXPECT().Validate(context.Background(), defaultReq).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			accountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
			acountOperationalStatusClient := mockAccountClient.NewMockOperationalStatusServiceClient(ctr)
			rule := mocks.NewMockIValidationRule(ctr)
			tt.setupMocks(accountPiClient, acountOperationalStatusClient, rule)
			a := validationrules.NewExecutionValidationBeneficiaryAccountStateCheck(accountPiClient, acountOperationalStatusClient)
			a.SetNext(rule)
			err := a.Validate(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.err != nil && !errors.Is(tt.err, err) {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_remitterAccountStateCheck_validate(t *testing.T) {
	var (
		defaultFromActorSavingsAccountId = "default-from-actor-savings-account-id"
		defaultPiFrom                    = "default-pi-From"
		defaultReq                       = &validationrules.ValidateReq{
			Recurringpayment: &rpPb.RecurringPayment{
				ToActorId: "default-to-actor-id",
				PiFrom:    defaultPiFrom,
			},
		}
		defaultGetByPiIdReq = &account_pi.GetByPiIdRequest{
			PiId: defaultPiFrom,
		}
		defaultGetByPiIdRes = &account_pi.GetByPiIdResponse{
			Status:      rpc.StatusOk(),
			AccountId:   defaultFromActorSavingsAccountId,
			AccountType: accounts.Type_SAVINGS,
		}
		defaultGetOperationalStatusReq = &operstatus.GetOperationalStatusRequest{
			DataFreshness: operstatus.GetOperationalStatusRequest_DATA_FRESHNESS_10_MIN_STALE,
			AccountIdentifier: &operstatus.GetOperationalStatusRequest_SavingsAccountId{
				SavingsAccountId: defaultFromActorSavingsAccountId,
			},
		}
	)
	type args struct {
		ctx context.Context
		req *validationrules.ValidateReq
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, acountOperationalStatusClient *mockAccountClient.MockOperationalStatusServiceClient, rule *mocks.MockIValidationRule)
		wantErr    bool
		err        error
	}{
		{
			name: "should fail (error while fetching account pi info)",
			args: args{
				ctx: context.Background(),
				req: defaultReq,
			},
			setupMocks: func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, acountOperationalStatusClient *mockAccountClient.MockOperationalStatusServiceClient, rule *mocks.MockIValidationRule) {
				accountPiClient.EXPECT().GetByPiId(context.Background(), defaultGetByPiIdReq).Return(&account_pi.GetByPiIdResponse{Status: rpc.StatusInternal()}, nil)
			},
			wantErr: true,
		},
		{
			name: "should pass (account id is empty)",
			args: args{
				ctx: context.Background(),
				req: defaultReq,
			},
			setupMocks: func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, acountOperationalStatusClient *mockAccountClient.MockOperationalStatusServiceClient, rule *mocks.MockIValidationRule) {
				accountPiClient.EXPECT().GetByPiId(context.Background(), defaultGetByPiIdReq).Return(&account_pi.GetByPiIdResponse{Status: rpc.StatusRecordNotFound()}, nil)
				rule.EXPECT().Validate(context.Background(), defaultReq).Return(nil)
			},
		},
		{
			name: "should pass (account id is not of type savings)",
			args: args{
				ctx: context.Background(),
				req: defaultReq,
			},
			setupMocks: func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, acountOperationalStatusClient *mockAccountClient.MockOperationalStatusServiceClient, rule *mocks.MockIValidationRule) {
				accountPiClient.EXPECT().GetByPiId(context.Background(), defaultGetByPiIdReq).Return(&account_pi.GetByPiIdResponse{Status: rpc.StatusOk(), AccountId: "credit-card-account", AccountType: accounts.Type_CREDIT_CARD_ACCOUNT}, nil)
				rule.EXPECT().Validate(context.Background(), defaultReq).Return(nil)
			},
		},
		{
			name: "should fail (failed to fetch the account state)",
			args: args{
				ctx: context.Background(),
				req: defaultReq,
			},
			setupMocks: func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, accountOperationalStatusClient *mockAccountClient.MockOperationalStatusServiceClient, rule *mocks.MockIValidationRule) {
				accountPiClient.EXPECT().GetByPiId(context.Background(), defaultGetByPiIdReq).Return(defaultGetByPiIdRes, nil)
				accountOperationalStatusClient.EXPECT().GetOperationalStatus(context.Background(), defaultGetOperationalStatusReq).Return(&operstatus.GetOperationalStatusResponse{Status: rpc.StatusInternal()}, nil)
			},
			wantErr: true,
		},
		{
			name: "should fail (remitter account operational status is not active)",
			args: args{
				ctx: context.Background(),
				req: defaultReq,
			},
			setupMocks: func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, acountOperationalStatusClient *mockAccountClient.MockOperationalStatusServiceClient, rule *mocks.MockIValidationRule) {
				accountPiClient.EXPECT().GetByPiId(context.Background(), defaultGetByPiIdReq).Return(defaultGetByPiIdRes, nil)
				acountOperationalStatusClient.EXPECT().GetOperationalStatus(context.Background(), defaultGetOperationalStatusReq).
					Return(&operstatus.GetOperationalStatusResponse{Status: rpc.StatusOk()}, nil)
			},
			wantErr: true,
			err:     validationrules.ExecuteValidationErrInvalidRemitterAccountState,
		},
		{
			name: "should fail (remitter account is in frozen status)",
			args: args{
				ctx: context.Background(),
				req: defaultReq,
			},
			setupMocks: func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, acountOperationalStatusClient *mockAccountClient.MockOperationalStatusServiceClient, rule *mocks.MockIValidationRule) {
				accountPiClient.EXPECT().GetByPiId(context.Background(), defaultGetByPiIdReq).Return(defaultGetByPiIdRes, nil)
				acountOperationalStatusClient.EXPECT().GetOperationalStatus(context.Background(), defaultGetOperationalStatusReq).
					Return(&operstatus.GetOperationalStatusResponse{Status: rpc.StatusOk(),
						OperationalStatusInfo: &operstatus.OperationalStatusInfo{
							FreezeStatus: enums2.FreezeStatus_FREEZE_STATUS_TOTAL_FREEZE,
						}}, nil)
			},
			wantErr: true,
			err:     validationrules.ExecuteValidationErrInvalidRemitterAccountState,
		},
		{
			name: "should pass",
			args: args{
				ctx: context.Background(),
				req: defaultReq,
			},
			setupMocks: func(accountPiClient *accountPiMocks.MockAccountPIRelationClient, acountOperationalStatusClient *mockAccountClient.MockOperationalStatusServiceClient, rule *mocks.MockIValidationRule) {
				accountPiClient.EXPECT().GetByPiId(context.Background(), defaultGetByPiIdReq).Return(defaultGetByPiIdRes, nil)
				acountOperationalStatusClient.EXPECT().GetOperationalStatus(context.Background(), defaultGetOperationalStatusReq).
					Return(&operstatus.GetOperationalStatusResponse{Status: rpc.StatusOk(),
						OperationalStatusInfo: &operstatus.OperationalStatusInfo{
							OperationalStatus: enums2.OperationalStatus_OPERATIONAL_STATUS_ACTIVE,
						}}, nil)
				rule.EXPECT().Validate(context.Background(), defaultReq).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			accountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
			acountOperationalStatusClient := mockAccountClient.NewMockOperationalStatusServiceClient(ctr)
			rule := mocks.NewMockIValidationRule(ctr)
			tt.setupMocks(accountPiClient, acountOperationalStatusClient, rule)
			a := validationrules.NewExecutionValidationRemitterAccountStateCheck(accountPiClient, acountOperationalStatusClient)
			a.SetNext(rule)
			err := a.Validate(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.err != nil && !errors.Is(err, tt.err) {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_pendingActionCheck_Validate(t *testing.T) {
	var (
		defaultRecurringPaymentId = "default-recurring-payment-id"
		defaultValidateReq        = &validationrules.ValidateReq{
			Recurringpayment: &rpPb.RecurringPayment{
				Id: defaultRecurringPaymentId,
			},
		}
	)
	type args struct {
		ctx context.Context
		req *validationrules.ValidateReq
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(actionDao *actionDaoMocks.MockRecurringPaymentsActionDao, rule *mocks.MockIValidationRule)
		wantErr    error
	}{
		{
			name: "should fail (error while fetching recurring payment actions)",
			args: args{
				ctx: context.Background(),
				req: defaultValidateReq,
			},
			setupMocks: func(actionDao *actionDaoMocks.MockRecurringPaymentsActionDao, rule *mocks.MockIValidationRule) {
				actionDao.EXPECT().GetByRecurringPaymentId(context.Background(), defaultRecurringPaymentId, gomock.AssignableToTypeOf(dao.WithNotInActionStatesFilter([]rpPb.ActionState{rpPb.ActionState_ACTION_SUCCESS, rpPb.ActionState_ACTION_FAILURE, rpPb.ActionState_ACTION_REJECT, rpPb.ActionState_ACTION_EXPIRED}))).
					Return(nil, epifierrors.ErrInvalidArgument)
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "should fail (action exists in non-terminal state)",
			args: args{
				ctx: context.Background(),
				req: defaultValidateReq,
			},
			setupMocks: func(actionDao *actionDaoMocks.MockRecurringPaymentsActionDao, rule *mocks.MockIValidationRule) {
				actionDao.EXPECT().GetByRecurringPaymentId(context.Background(), defaultRecurringPaymentId, gomock.AssignableToTypeOf(dao.WithNotInActionStatesFilter([]rpPb.ActionState{rpPb.ActionState_ACTION_SUCCESS, rpPb.ActionState_ACTION_FAILURE, rpPb.ActionState_ACTION_REJECT, rpPb.ActionState_ACTION_EXPIRED}))).
					Return([]*rpPb.RecurringPaymentsAction{{}}, nil)
			},
			wantErr: validationrules.ExecuteValidationErrNonTerminalActionExists,
		},
		{
			name: "should pass",
			args: args{
				ctx: context.Background(),
				req: defaultValidateReq,
			},
			setupMocks: func(actionDao *actionDaoMocks.MockRecurringPaymentsActionDao, rule *mocks.MockIValidationRule) {
				actionDao.EXPECT().GetByRecurringPaymentId(context.Background(), defaultRecurringPaymentId, gomock.AssignableToTypeOf(dao.WithNotInActionStatesFilter([]rpPb.ActionState{rpPb.ActionState_ACTION_SUCCESS, rpPb.ActionState_ACTION_FAILURE, rpPb.ActionState_ACTION_REJECT, rpPb.ActionState_ACTION_EXPIRED}))).
					Return(nil, epifierrors.ErrRecordNotFound)
				rule.EXPECT().Validate(context.Background(), defaultValidateReq).Return(nil)
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		ctr := gomock.NewController(t)
		actionDao := actionDaoMocks.NewMockRecurringPaymentsActionDao(ctr)
		rule := mocks.NewMockIValidationRule(ctr)
		tt.setupMocks(actionDao, rule)
		p := validationrules.NewPendingActionCheck(actionDao)
		p.SetNext(rule)
		t.Run(tt.name, func(t *testing.T) {
			if err := p.Validate(tt.args.ctx, tt.args.req); !errors.Is(err, tt.wantErr) {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_executionValidationExecutionDateCheck_Validate(t *testing.T) {
	type args struct {
		ctx context.Context
		req *validationrules.ValidateReq
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(rule *mocks.MockIValidationRule)
		wantErr    bool
	}{
		{
			name: "should return error when recurring payment start time is more than current time",
			args: args{
				ctx: context.Background(),
				req: &validationrules.ValidateReq{
					Recurringpayment: &rpPb.RecurringPayment{Interval: &types.Interval{
						StartTime: timestampPb.New(time10MinsLater),
						EndTime:   timestampPb.New(time20MinsLater),
					}},
				},
			},
			setupMocks: func(rule *mocks.MockIValidationRule) {

			},
			wantErr: true,
		},
		{
			name: "should pass when current time is between recurring payment start and end time",
			args: args{
				ctx: context.Background(),
				req: &validationrules.ValidateReq{
					Recurringpayment: &rpPb.RecurringPayment{Interval: &types.Interval{
						StartTime: timestampPb.New(time10MinsEarlier),
						EndTime:   timestampPb.New(time10MinsLater),
					}},
				},
			},
			setupMocks: func(rule *mocks.MockIValidationRule) {
				rule.EXPECT().Validate(context.Background(), &validationrules.ValidateReq{
					Recurringpayment: &rpPb.RecurringPayment{Interval: &types.Interval{
						StartTime: timestampPb.New(time10MinsEarlier),
						EndTime:   timestampPb.New(time10MinsLater),
					}},
				}).Return(nil)
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		ctr := gomock.NewController(t)

		mockRule := mocks.NewMockIValidationRule(ctr)
		tt.setupMocks(mockRule)

		p := validationrules.NewExecutionValidationExecutionDateCheck()
		p.SetNext(mockRule)

		t.Run(tt.name, func(t *testing.T) {
			err := p.Validate(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
