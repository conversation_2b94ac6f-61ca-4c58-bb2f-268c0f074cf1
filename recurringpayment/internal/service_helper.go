package internal

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	"go.uber.org/zap"

	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

// GetDeviceAuthDetails fetches device details for a given actorId
// Returns deviceId, deviceToken, userProfileId and error
func GetDeviceAuthDetails(ctx context.Context, actorId string, authClient authPb.AuthClient) (deviceId,
	deviceToken, userProfileId string, err error) {
	getDeviceAuthResponse, err := authClient.GetDeviceAuth(ctx, &authPb.GetDeviceAuthRequest{
		ActorId: actorId,
	})
	switch {
	case err != nil:
		return "", "", "", err
	case !getDeviceAuthResponse.Status.IsSuccess():
		return "", "", "", fmt.Errorf("authClient.GetDeviceAuth() failed for actorId: %s, status: %s",
			actorId, getDeviceAuthResponse.GetStatus())
	default:
		return getDeviceAuthResponse.GetDevice().GetDeviceId(), getDeviceAuthResponse.GetDeviceToken(),
			getDeviceAuthResponse.GetUserProfileId(), nil
	}
}

// GetEntityDetails returns the entity id, mobile number and nil for an actor id
// returns "", nil, relevant error in case of failures/errors
func GetEntityDetails(ctx context.Context, actorId string, actorClient actorPb.ActorClient) (string,
	*commontypes.PhoneNumber, error) {
	getEntityDetailsRes, err := actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{
		ActorId: actorId,
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error fetching entity details ", zap.String("actor-id", actorId),
			zap.Error(err))
		return "", nil, err
	case !getEntityDetailsRes.GetStatus().IsSuccess():
		logger.Error(ctx, "error fetching entity details, non-success ", zap.String(logger.ACTOR_ID, actorId),
			zap.Uint32(logger.STATUS_CODE, getEntityDetailsRes.GetStatus().GetCode()), zap.Error(err))
		return "", nil, fmt.Errorf("non success state while fetching entity details")
	default:
		return getEntityDetailsRes.GetEntityId(), getEntityDetailsRes.GetMobileNumber(), nil
	}
}

// GetCustomerId returns the customerId, nil for a given userId and vendor
// returns "", relevant error in case for failures/errors
func GetCustomerId(ctx context.Context, userId string, vendor commonvgpb.Vendor,
	bankCustClient bankcust.BankCustomerServiceClient) (string, error) {
	customerDetailsRes, bcErr := bankCustClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: vendor,
		Identifier: &bankcust.GetBankCustomerRequest_UserId{
			UserId: userId,
		},
	})
	if rpcErr := epifigrpc.RPCError(customerDetailsRes, bcErr); rpcErr != nil {
		logger.Error(ctx, "error while fetching bank customer", zap.Error(rpcErr))
		return "", rpcErr
	}
	return customerDetailsRes.GetBankCustomer().GetVendorCustomerId(), nil
}

// GetSavingsAccountId returns the savings account id for an entity id
func GetSavingsAccountId(ctx context.Context, entityId string, savingsClient savingsPb.SavingsClient) (string, error) {
	savingsAccountRes, err := savingsClient.GetAccount(ctx,
		&savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{PrimaryUserId: entityId}})
	switch {
	case err != nil:
		return "", fmt.Errorf("failed to get savings account detail for entityId: %s %w", entityId, err)
	case savingsAccountRes.GetAccount() == nil:
		return "", fmt.Errorf("received nil account details entityId: %s", entityId)
	}
	return savingsAccountRes.GetAccount().GetId(), nil
}
