package adaptor

import (
	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	typesPb "github.com/epifi/gamma/api/typesv2"
)

func ConvertToDeepLinkTransactionAttributes(attribute *rpPb.TransactionAttribute, clientRequestId string) *deepLinkPb.TransactionAttributes {
	return &deepLinkPb.TransactionAttributes{
		PayerAccountId:                attribute.GetPayerAccountId(),
		RequestId:                     attribute.GetTransactionId(),
		MerchantRefId:                 attribute.GetMerchantRefId(),
		PaymentProtocol:               typesPb.PaymentProtocol(typesPb.PaymentProtocol_value[attribute.GetPaymentProtocol().String()]),
		ReferenceUrl:                  attribute.GetReferenceUrl(),
		PayeeActorName:                attribute.GetPayeeActorName(),
		PayerMaskedAccountNumber:      attribute.GetPayerMaskedAccountNumber(),
		Amount:                        typesPb.GetFromBeMoney(attribute.GetAmount()),
		Remarks:                       attribute.GetRemarks(),
		PayerPaymentInstrument:        attribute.GetPayerPaymentInstrument(),
		PayeePaymentInstrument:        attribute.GetPayeePaymentInstrument(),
		DisplayPayeePaymentInstrument: attribute.GetDisplayPayeePaymentInstrument(),
		PayerPaymentInstrumentId:      attribute.GetPayerPaymentInstrument(),
		PayeePaymentInstrumentId:      attribute.GetPayeePaymentInstrument(),
		ClientRequestId:               clientRequestId,
	}
}
