package internal

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	mockActor "github.com/epifi/gamma/api/actor/mocks"
	"github.com/epifi/gamma/api/recurringpayment"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
)

func TestProcessor_GetCredBlockTypeV2(t *testing.T) {
	var (
		mockActorClient                *mockActor.MockActorClient
		mockRecurringPaymentActionsDao *daoMocks.MockRecurringPaymentsActionDao
		mockRecurringPaymentDao        *daoMocks.MockRecurringPaymentDao
	)

	type args struct {
		recurringPaymentType recurringpayment.RecurringPaymentType
		actorRole            recurringpayment.ActorRole
		initiatedBy          recurringpayment.InitiatedBy
		action               recurringpayment.Action
	}
	tests := []struct {
		name    string
		args    args
		want    *GetCredBlockTypeV2Response
		wantErr bool
	}{
		{
			name: "should return true for payer role with standing instruction",
			args: args{
				recurringPaymentType: recurringpayment.RecurringPaymentType_STANDING_INSTRUCTION,
				actorRole:            recurringpayment.ActorRole_ACTOR_ROLE_PAYER,
				initiatedBy:          recurringpayment.InitiatedBy_PAYEE,
				action:               recurringpayment.Action_REVOKE,
			},
			want: &GetCredBlockTypeV2Response{
				IsAuthRequired: true,
				CredBlockType:  recurringpayment.CredBlockType_PARTNER_SDK,
			},
			wantErr: false,
		},
		{
			name: "should return false for payee role with standing instruction",
			args: args{
				recurringPaymentType: recurringpayment.RecurringPaymentType_STANDING_INSTRUCTION,
				actorRole:            recurringpayment.ActorRole_ACTOR_ROLE_PAYEE,
				initiatedBy:          recurringpayment.InitiatedBy_PAYEE,
				action:               recurringpayment.Action_REVOKE,
			},
			want: &GetCredBlockTypeV2Response{
				IsAuthRequired: false,
				CredBlockType:  recurringpayment.CredBlockType_CRED_BLOCK_TYPE_UNSPECIFIED,
			},
			wantErr: false,
		},
		{
			name: "should return false for UPI mandates and revoke action initiated by payee",
			args: args{
				recurringPaymentType: recurringpayment.RecurringPaymentType_UPI_MANDATES,
				actorRole:            recurringpayment.ActorRole_ACTOR_ROLE_PAYER,
				initiatedBy:          recurringpayment.InitiatedBy_PAYEE,
				action:               recurringpayment.Action_REVOKE,
			},
			want: &GetCredBlockTypeV2Response{
				IsAuthRequired: false,
				CredBlockType:  recurringpayment.CredBlockType_CRED_BLOCK_TYPE_UNSPECIFIED,
			},
			wantErr: false,
		},
		{
			name: "should return true for UPI mandates and revoke action initiated by payer",
			args: args{
				recurringPaymentType: recurringpayment.RecurringPaymentType_UPI_MANDATES,
				actorRole:            recurringpayment.ActorRole_ACTOR_ROLE_PAYER,
				initiatedBy:          recurringpayment.InitiatedBy_PAYER,
				action:               recurringpayment.Action_REVOKE,
			},
			want: &GetCredBlockTypeV2Response{
				IsAuthRequired: true,
				CredBlockType:  recurringpayment.CredBlockType_NPCI,
			},
			wantErr: false,
		},
		{
			name: "should return true for UPI mandates and modify action initiated by payer",
			args: args{
				recurringPaymentType: recurringpayment.RecurringPaymentType_UPI_MANDATES,
				actorRole:            recurringpayment.ActorRole_ACTOR_ROLE_PAYER,
				initiatedBy:          recurringpayment.InitiatedBy_PAYER,
				action:               recurringpayment.Action_MODIFY,
			},
			want: &GetCredBlockTypeV2Response{
				IsAuthRequired: true,
				CredBlockType:  recurringpayment.CredBlockType_NPCI,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockActorClient = mockActor.NewMockActorClient(ctr)
			mockRecurringPaymentDao = daoMocks.NewMockRecurringPaymentDao(ctr)
			mockRecurringPaymentActionsDao = daoMocks.NewMockRecurringPaymentsActionDao(ctr)
			p := NewProcessor(mockActorClient, mockRecurringPaymentActionsDao, mockRecurringPaymentDao)
			got, err := p.GetCredBlockTypeV2(&GetCredBlockTypeV2Request{
				RecurringPaymentType: tt.args.recurringPaymentType,
				ActorRole:            tt.args.actorRole,
				InitiatedBy:          tt.args.initiatedBy,
				Action:               tt.args.action,
			})
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCredBlockType() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetCredBlockType() got = %v, want %v, diff = %v", got, tt.want, diff)
			}
		})
	}
}
