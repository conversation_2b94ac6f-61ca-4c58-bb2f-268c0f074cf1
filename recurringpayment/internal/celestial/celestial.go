package celestial

import (
	"context"
	"fmt"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpServerConfig "github.com/epifi/gamma/recurringpayment/config/server"
)

type CelestialProcessor struct {
	celestialClient celestialPb.CelestialClient
	config          *rpServerConfig.Config
}

func NewCelestialProcessor(celestialClient celestialPb.CelestialClient, config *rpServerConfig.Config) *CelestialProcessor {
	return &CelestialProcessor{
		celestialClient: celestialClient,
		config:          config,
	}
}

func (p *CelestialProcessor) SignalWorkflow(ctx context.Context, clientReqID string, signalID string, client workflowPb.Client, payload []byte, ownership commontypes.Ownership, useCase commontypes.UseCase) error {
	signalWorkflowResp, err := p.celestialClient.SignalWorkflow(ctx, &celestialPb.SignalWorkflowRequest{
		Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
			ClientReqId: &celestialPb.ClientReqId{
				Id:     clientReqID,
				Client: client,
			},
		},
		SignalId:         signalID,
		Payload:          payload,
		QualityOfService: celestialPb.QoS_GUARANTEED,
		Ownership:        ownership,
		UseCase:          useCase,
	})
	if rpcErr := epifigrpc.RPCError(signalWorkflowResp, err); rpcErr != nil {
		return fmt.Errorf("failed to signal workflow %w", rpcErr)
	}

	return nil
}

func (p *CelestialProcessor) GetWorkflowByClientRequestId(ctx context.Context, clientRequestId string, client workflowPb.Client) (*celestialPb.WorkflowRequest, error) {
	resp, err := p.celestialClient.GetWorkflowStatus(ctx, &celestialPb.GetWorkflowStatusRequest{
		Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
			ClientRequestId: &celestialPb.ClientReqId{
				Id:     clientRequestId,
				Client: client,
			},
		},
	})
	switch {
	case err != nil:
		return nil, fmt.Errorf("error while fetching workflow request for given client request id %v %w", err, rpcPb.StatusAsError(rpcPb.StatusInternal()))
	case resp.GetStatus().IsSuccess():
		return resp.GetWorkflowRequest(), nil
	case resp.GetStatus().IsRecordNotFound():
		return nil, fmt.Errorf("workflow request not found for given client request id %w", rpcPb.StatusAsError(rpcPb.StatusRecordNotFound()))
	default:
		return nil, fmt.Errorf("non success status while fetching workflow request %v %w", resp.GetStatus().String(), rpcPb.StatusAsError(rpcPb.StatusInternal()))
	}
}

// InitiateWorkflow creates a workflow request of type CREATE_RECURRING_PAYMENT and with
// payload as RecurringPaymentCreationInfo.
// Deprecated: in favour of InitiateWorkflowV2
func (p *CelestialProcessor) InitiateWorkflow(ctx context.Context, clientReqId *celestialPb.ClientReqId, actorId string,
	payload []byte, workflowType workflowPb.Type, version workflowPb.Version) error {
	initiateWorkflowRes, err := p.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		ActorId:     actorId,
		Version:     version,
		Type:        workflowType,
		Payload:     payload,
		ClientReqId: clientReqId,
	})

	if te := epifigrpc.RPCError(initiateWorkflowRes, err); te != nil {
		return fmt.Errorf("error while initiating workflow for client req id %s, %w", clientReqId.GetId(), te)
	}
	return nil
}

// InitiateWorkflowV2 initiates the new celestial workflow for given params
func (p *CelestialProcessor) InitiateWorkflowV2(ctx context.Context, clientReqId *workflowPb.ClientReqId, actorId string, payload []byte, workflowType *workflowPb.TypeEnum, version workflowPb.Version, qos celestialPb.QoS) error {
	initiateWorkflowRes, err := p.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId:          actorId,
			Version:          version,
			Type:             workflowType,
			Payload:          payload,
			ClientReqId:      clientReqId,
			Ownership:        commontypes.Ownership_EPIFI_TECH,
			QualityOfService: qos,
		},
	})

	if te := epifigrpc.RPCError(initiateWorkflowRes, err); te != nil {
		return fmt.Errorf("error while initiating workflow for client req id %s, %w", clientReqId.GetId(), te)
	}
	return nil
}
