package celestial_test

import (
	"os"
	"testing"

	"github.com/golang/mock/gomock"

	celestialMocks "github.com/epifi/be-common/api/celestial/mocks"
	rpServerConfig "github.com/epifi/gamma/recurringpayment/config/server"
	"github.com/epifi/gamma/recurringpayment/internal/celestial"
	"github.com/epifi/gamma/recurringpayment/test"
)

var (
	conf *rpServerConfig.Config
)

type mockedDependencies struct {
	celestialClient *celestialMocks.MockCelestialClient
}

func newProcessorWithMocks(t *testing.T) (*celestial.CelestialProcessor, *mockedDependencies, func()) {
	ctr := gomock.NewController(t)
	mockCelestialClient := celestialMocks.NewMockCelestialClient(ctr)

	md := &mockedDependencies{
		celestialClient: mockCelestialClient,
	}

	svc := celestial.NewCelestialProcessor(
		mockCelestialClient,
		conf,
	)

	return svc, md, func() {
		ctr.Finish()
	}
}

// nolint:dogsled
func TestMain(m *testing.M) {
	var teardown func()
	_, conf, _, _, teardown = test.InitTestServerV2()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
