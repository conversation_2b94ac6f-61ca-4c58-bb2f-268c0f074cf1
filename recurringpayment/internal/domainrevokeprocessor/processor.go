//go:generate mockgen -source=$PWD/processor.go -destination=$PWD/mocks/mock_processor.go -package=mocks
package domainrevokeprocessor

import "context"

type DomainRevokeProcessor interface {
	// InitiateRevokeAtDomain is responsible for initiating the revoke of the recurring payment at domain
	InitiateRevokeAtDomain(ctx context.Context, req *InitiateRevokeAtDomainReq) (*InitiateRevokeAtDomainRes, error)
	// EnquireRevokeStatus is used to enquire the execution status at domain post initiation
	EnquireRevokeStatus(ctx context.Context, req *EnquireRevokeStatusAtDomainReq) (*EnquireRevokeStatusAtDomainRes, error)
	// IsAuthRequired is used to check if auth is required for the revoke flow.
	IsAuthRequired(ctx context.Context, req *IsAuthRequiredReq) (bool, error)
}

type InitiateRevokeAtDomainReq struct {
	RecurringPaymentActionId string
}

type InitiateRevokeAtDomainRes struct {
}

type EnquireRevokeStatusAtDomainReq struct {
	RecurringPaymentActionId string
}

type EnquireRevokeStatusAtDomainRes struct {
	RevokeStatus DomainRevokeStatus
}

type IsAuthRequiredReq struct {
}

type DomainRevokeStatus string

// status to be returned from the processor to the activity
// this can be extended to return granular level of status if required to the activity
const (
	DOMAIN_REVOKE_STATUS_UNSPECIFIED DomainRevokeStatus = "DOMAIN_REVOKE_STATUS_UNSPECIFIED"
	DOMAIN_REVOKE_STATUS_REVOKED     DomainRevokeStatus = "DOMAIN_REVOKE_STATUS_REVOKED"
	DOMAIN_REVOKE_STATUS_PENDING     DomainRevokeStatus = "DOMAIN_REVOKE_STATUS_PENDING"
	DOMAIN_REVOKE_STATUS_FAILED      DomainRevokeStatus = "DOMAIN_REVOKE_STATUS_FAILED"
)
