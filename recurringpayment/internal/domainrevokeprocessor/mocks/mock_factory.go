// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/go/src/github.com/epifi/gamma/recurringpayment/internal/domainrevokeprocessor/factory.go

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	recurringpayment "github.com/epifi/gamma/api/recurringpayment"
	domainrevokeprocessor "github.com/epifi/gamma/recurringpayment/internal/domainrevokeprocessor"
	gomock "github.com/golang/mock/gomock"
)

// MockDomainRevokeProcessorFactory is a mock of DomainRevokeProcessorFactory interface.
type MockDomainRevokeProcessorFactory struct {
	ctrl     *gomock.Controller
	recorder *MockDomainRevokeProcessorFactoryMockRecorder
}

// MockDomainRevokeProcessorFactoryMockRecorder is the mock recorder for MockDomainRevokeProcessorFactory.
type MockDomainRevokeProcessorFactoryMockRecorder struct {
	mock *MockDomainRevokeProcessorFactory
}

// NewMockDomainRevokeProcessorFactory creates a new mock instance.
func NewMockDomainRevokeProcessorFactory(ctrl *gomock.Controller) *MockDomainRevokeProcessorFactory {
	mock := &MockDomainRevokeProcessorFactory{ctrl: ctrl}
	mock.recorder = &MockDomainRevokeProcessorFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDomainRevokeProcessorFactory) EXPECT() *MockDomainRevokeProcessorFactoryMockRecorder {
	return m.recorder
}

// GetProcessor mocks base method.
func (m *MockDomainRevokeProcessorFactory) GetProcessor(rpType recurringpayment.RecurringPaymentType, rpRoute recurringpayment.RecurringPaymentRoute) (domainrevokeprocessor.DomainRevokeProcessor, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProcessor", rpType, rpRoute)
	ret0, _ := ret[0].(domainrevokeprocessor.DomainRevokeProcessor)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProcessor indicates an expected call of GetProcessor.
func (mr *MockDomainRevokeProcessorFactoryMockRecorder) GetProcessor(rpType, rpRoute interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProcessor", reflect.TypeOf((*MockDomainRevokeProcessorFactory)(nil).GetProcessor), rpType, rpRoute)
}
