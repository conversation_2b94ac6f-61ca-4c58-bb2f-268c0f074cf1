// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/go/src/github.com/epifi/gamma/recurringpayment/internal/domainrevokeprocessor/processor.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	domainrevokeprocessor "github.com/epifi/gamma/recurringpayment/internal/domainrevokeprocessor"
	gomock "github.com/golang/mock/gomock"
)

// MockDomainRevokeProcessor is a mock of DomainRevokeProcessor interface.
type MockDomainRevokeProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockDomainRevokeProcessorMockRecorder
}

// MockDomainRevokeProcessorMockRecorder is the mock recorder for MockDomainRevokeProcessor.
type MockDomainRevokeProcessorMockRecorder struct {
	mock *MockDomainRevokeProcessor
}

// NewMockDomainRevokeProcessor creates a new mock instance.
func NewMockDomainRevokeProcessor(ctrl *gomock.Controller) *MockDomainRevokeProcessor {
	mock := &MockDomainRevokeProcessor{ctrl: ctrl}
	mock.recorder = &MockDomainRevokeProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDomainRevokeProcessor) EXPECT() *MockDomainRevokeProcessorMockRecorder {
	return m.recorder
}

// EnquireRevokeStatus mocks base method.
func (m *MockDomainRevokeProcessor) EnquireRevokeStatus(ctx context.Context, req *domainrevokeprocessor.EnquireRevokeStatusAtDomainReq) (*domainrevokeprocessor.EnquireRevokeStatusAtDomainRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnquireRevokeStatus", ctx, req)
	ret0, _ := ret[0].(*domainrevokeprocessor.EnquireRevokeStatusAtDomainRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EnquireRevokeStatus indicates an expected call of EnquireRevokeStatus.
func (mr *MockDomainRevokeProcessorMockRecorder) EnquireRevokeStatus(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnquireRevokeStatus", reflect.TypeOf((*MockDomainRevokeProcessor)(nil).EnquireRevokeStatus), ctx, req)
}

// InitiateRevokeAtDomain mocks base method.
func (m *MockDomainRevokeProcessor) InitiateRevokeAtDomain(ctx context.Context, req *domainrevokeprocessor.InitiateRevokeAtDomainReq) (*domainrevokeprocessor.InitiateRevokeAtDomainRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateRevokeAtDomain", ctx, req)
	ret0, _ := ret[0].(*domainrevokeprocessor.InitiateRevokeAtDomainRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateRevokeAtDomain indicates an expected call of InitiateRevokeAtDomain.
func (mr *MockDomainRevokeProcessorMockRecorder) InitiateRevokeAtDomain(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateRevokeAtDomain", reflect.TypeOf((*MockDomainRevokeProcessor)(nil).InitiateRevokeAtDomain), ctx, req)
}

// IsAuthRequired mocks base method.
func (m *MockDomainRevokeProcessor) IsAuthRequired(ctx context.Context, req *domainrevokeprocessor.IsAuthRequiredReq) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsAuthRequired", ctx, req)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsAuthRequired indicates an expected call of IsAuthRequired.
func (mr *MockDomainRevokeProcessorMockRecorder) IsAuthRequired(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsAuthRequired", reflect.TypeOf((*MockDomainRevokeProcessor)(nil).IsAuthRequired), ctx, req)
}
