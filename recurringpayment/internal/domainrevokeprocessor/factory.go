//go:generate mockgen -source=$PWD/factory.go -destination=$PWD/mocks/mock_factory.go -package=mocks
package domainrevokeprocessor

import (
	"fmt"

	"github.com/google/wire"

	"github.com/epifi/be-common/pkg/epifierrors"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
)

var DomainRevokeProcessorWireSet = wire.NewSet(NewPgEmandateProcessor)
var DomainRevokeProcessorFactoryWireSet = wire.NewSet(DomainRevokeProcessorWireSet, wire.NewSet(NewDomainRevokeProcessorFactoryImpl, wire.Bind(new(DomainRevokeProcessorFactory), new(*DomainRevokeProcessorFactoryImpl))))

type DomainRevokeProcessorFactory interface {
	GetProcessor(rpType rpPb.RecurringPaymentType, rpRoute rpPb.RecurringPaymentRoute) (DomainRevokeProcessor, error)
}

type DomainRevokeProcessorFactoryImpl struct {
	pgEmandateProcessor *PgEmandateProcessor
}

func NewDomainRevokeProcessorFactoryImpl(pgEmandateProcessor *PgEmandateProcessor) *DomainRevokeProcessorFactoryImpl {
	return &DomainRevokeProcessorFactoryImpl{
		pgEmandateProcessor: pgEmandateProcessor,
	}
}

func (d *DomainRevokeProcessorFactoryImpl) GetProcessor(rpType rpPb.RecurringPaymentType, paymentRoute rpPb.RecurringPaymentRoute) (DomainRevokeProcessor, error) {
	switch paymentRoute {
	case rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE:
		return d.getNativeRevokeProcessor(rpType)
	case rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_EXTERNAL:
		return d.getExternalRevokeProcessor(rpType)
	default:
		return nil, fmt.Errorf("error in fetching execution processor for the payment route : %s %w", paymentRoute.String(), epifierrors.ErrInvalidArgument)
	}
}

func (d *DomainRevokeProcessorFactoryImpl) getNativeRevokeProcessor(rpType rpPb.RecurringPaymentType) (DomainRevokeProcessor, error) {
	// TODO: Remove the nolint once more cases are added to the switch.
	// nolint:gocritic
	switch rpType {
	default:
		return nil, fmt.Errorf("no domain revoke processor configured for recurring payment type: %s", rpType.String())
	}
}

func (d *DomainRevokeProcessorFactoryImpl) getExternalRevokeProcessor(rpType rpPb.RecurringPaymentType) (DomainRevokeProcessor, error) {
	switch rpType {
	case rpPb.RecurringPaymentType_ENACH_MANDATES:
		return d.pgEmandateProcessor, nil
	default:
		return nil, fmt.Errorf("no domain revoke processor configured for recurring payment type: %s", rpType.String())
	}
}
