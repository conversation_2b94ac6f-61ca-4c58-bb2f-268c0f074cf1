package domainrevokeprocessor

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/epifi/gamma/api/recurringpayment/paymentgateway"
	"github.com/epifi/gamma/recurringpayment/dao"
)

type PgEmandateProcessor struct {
	rpaDao   dao.RecurringPaymentsActionDao
	rpDao    dao.RecurringPaymentDao
	pgClient paymentgateway.PaymentGatewayServiceClient
	rpVdDao  dao.RecurringPaymentsVendorDetailsDao
}

func NewPgEmandateProcessor(
	rpaDao dao.RecurringPaymentsActionDao,
	rpDao dao.RecurringPaymentDao,
	pgClient paymentgateway.PaymentGatewayServiceClient,
	rpVdDao dao.RecurringPaymentsVendorDetailsDao) *PgEmandateProcessor {
	return &PgEmandateProcessor{
		rpaDao:   rpaDao,
		rpDao:    rpDao,
		pgClient: pgClient,
		rpVdDao:  rpVdDao,
	}
}

func (p *PgEmandateProcessor) InitiateRevokeAtDomain(ctx context.Context, req *InitiateRevokeAtDomainReq) (*InitiateRevokeAtDomainRes, error) {
	// no-op for PG Flow
	return &InitiateRevokeAtDomainRes{}, nil
}

func (p *PgEmandateProcessor) EnquireRevokeStatus(ctx context.Context, req *EnquireRevokeStatusAtDomainReq) (*EnquireRevokeStatusAtDomainRes, error) {
	rpa, err := p.rpaDao.GetById(ctx, req.RecurringPaymentActionId)
	if err != nil {
		return nil, fmt.Errorf("error in fetching recurring payment action : %w", err)
	}
	rp, err := p.rpDao.GetById(ctx, rpa.GetRecurringPaymentId())
	if err != nil {
		return nil, fmt.Errorf("error in fetching recurring payment : %w", err)
	}

	getMandateStatusRes, getMandateStatusErr := p.pgClient.GetMandateStatus(ctx, &paymentgateway.GetMandateStatusRequest{RecurringPaymentId: rp.GetId()})
	if te := epifigrpc.RPCError(getMandateStatusRes, getMandateStatusErr); te != nil {
		return nil, fmt.Errorf("error in fetching mandate status : %w", te)
	}

	mandateRevokeStatus := DOMAIN_REVOKE_STATUS_PENDING
	if getMandateStatusRes.GetMandateStatus() == paymentgateway.GetMandateStatusResponse_MANDATE_STATUS_CANCELLED {
		mandateRevokeStatus = DOMAIN_REVOKE_STATUS_REVOKED
	}

	return &EnquireRevokeStatusAtDomainRes{
		RevokeStatus: mandateRevokeStatus,
	}, nil
}

func (p *PgEmandateProcessor) IsAuthRequired(ctx context.Context, req *IsAuthRequiredReq) (bool, error) {
	return false, nil
}
