// Code generated by MockGen. DO NOT EDIT.
// Source: processor.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	celestial "github.com/epifi/be-common/api/celestial"
	workflow "github.com/epifi/be-common/api/celestial/workflow"
	common "github.com/epifi/be-common/api/typesv2/common"
	actor "github.com/epifi/gamma/api/actor"
	payment "github.com/epifi/gamma/api/order/payment"
	paymentinstrument "github.com/epifi/gamma/api/paymentinstrument"
	recurringpayment "github.com/epifi/gamma/api/recurringpayment"
	internal "github.com/epifi/gamma/recurringpayment/internal"
	actor0 "github.com/epifi/gamma/recurringpayment/internal/actor"
	gomock "github.com/golang/mock/gomock"
	money "google.golang.org/genproto/googleapis/type/money"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

// MockRecurringPaymentProcessor is a mock of RecurringPaymentProcessor interface.
type MockRecurringPaymentProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockRecurringPaymentProcessorMockRecorder
}

// MockRecurringPaymentProcessorMockRecorder is the mock recorder for MockRecurringPaymentProcessor.
type MockRecurringPaymentProcessorMockRecorder struct {
	mock *MockRecurringPaymentProcessor
}

// NewMockRecurringPaymentProcessor creates a new mock instance.
func NewMockRecurringPaymentProcessor(ctrl *gomock.Controller) *MockRecurringPaymentProcessor {
	mock := &MockRecurringPaymentProcessor{ctrl: ctrl}
	mock.recorder = &MockRecurringPaymentProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRecurringPaymentProcessor) EXPECT() *MockRecurringPaymentProcessorMockRecorder {
	return m.recorder
}

// FetchActorName mocks base method.
func (m *MockRecurringPaymentProcessor) FetchActorName(ctx context.Context, actorId string) (*common.Name, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchActorName", ctx, actorId)
	ret0, _ := ret[0].(*common.Name)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchActorName indicates an expected call of FetchActorName.
func (mr *MockRecurringPaymentProcessorMockRecorder) FetchActorName(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchActorName", reflect.TypeOf((*MockRecurringPaymentProcessor)(nil).FetchActorName), ctx, actorId)
}

// GetCredBlockType mocks base method.
func (m *MockRecurringPaymentProcessor) GetCredBlockType(recurringPaymentType recurringpayment.RecurringPaymentType, actorRole recurringpayment.ActorRole) (bool, recurringpayment.CredBlockType, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCredBlockType", recurringPaymentType, actorRole)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(recurringpayment.CredBlockType)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetCredBlockType indicates an expected call of GetCredBlockType.
func (mr *MockRecurringPaymentProcessorMockRecorder) GetCredBlockType(recurringPaymentType, actorRole interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCredBlockType", reflect.TypeOf((*MockRecurringPaymentProcessor)(nil).GetCredBlockType), recurringPaymentType, actorRole)
}

// GetCredBlockTypeV2 mocks base method.
func (m *MockRecurringPaymentProcessor) GetCredBlockTypeV2(request *internal.GetCredBlockTypeV2Request) (*internal.GetCredBlockTypeV2Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCredBlockTypeV2", request)
	ret0, _ := ret[0].(*internal.GetCredBlockTypeV2Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCredBlockTypeV2 indicates an expected call of GetCredBlockTypeV2.
func (mr *MockRecurringPaymentProcessorMockRecorder) GetCredBlockTypeV2(request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCredBlockTypeV2", reflect.TypeOf((*MockRecurringPaymentProcessor)(nil).GetCredBlockTypeV2), request)
}

// GetRecurringPaymentActionByClientReqId mocks base method.
func (m *MockRecurringPaymentProcessor) GetRecurringPaymentActionByClientReqId(ctx context.Context, clientReqId string) (*recurringpayment.RecurringPaymentsAction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecurringPaymentActionByClientReqId", ctx, clientReqId)
	ret0, _ := ret[0].(*recurringpayment.RecurringPaymentsAction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecurringPaymentActionByClientReqId indicates an expected call of GetRecurringPaymentActionByClientReqId.
func (mr *MockRecurringPaymentProcessorMockRecorder) GetRecurringPaymentActionByClientReqId(ctx, clientReqId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecurringPaymentActionByClientReqId", reflect.TypeOf((*MockRecurringPaymentProcessor)(nil).GetRecurringPaymentActionByClientReqId), ctx, clientReqId)
}

// GetRecurringPaymentById mocks base method.
func (m *MockRecurringPaymentProcessor) GetRecurringPaymentById(ctx context.Context, recurringPaymentId string) (*recurringpayment.RecurringPayment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecurringPaymentById", ctx, recurringPaymentId)
	ret0, _ := ret[0].(*recurringpayment.RecurringPayment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecurringPaymentById indicates an expected call of GetRecurringPaymentById.
func (mr *MockRecurringPaymentProcessorMockRecorder) GetRecurringPaymentById(ctx, recurringPaymentId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecurringPaymentById", reflect.TypeOf((*MockRecurringPaymentProcessor)(nil).GetRecurringPaymentById), ctx, recurringPaymentId)
}

// MockCelestialProcessor is a mock of CelestialProcessor interface.
type MockCelestialProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockCelestialProcessorMockRecorder
}

// MockCelestialProcessorMockRecorder is the mock recorder for MockCelestialProcessor.
type MockCelestialProcessorMockRecorder struct {
	mock *MockCelestialProcessor
}

// NewMockCelestialProcessor creates a new mock instance.
func NewMockCelestialProcessor(ctrl *gomock.Controller) *MockCelestialProcessor {
	mock := &MockCelestialProcessor{ctrl: ctrl}
	mock.recorder = &MockCelestialProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCelestialProcessor) EXPECT() *MockCelestialProcessorMockRecorder {
	return m.recorder
}

// GetWorkflowByClientRequestId mocks base method.
func (m *MockCelestialProcessor) GetWorkflowByClientRequestId(ctx context.Context, clientReqID string, client workflow.Client) (*celestial.WorkflowRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflowByClientRequestId", ctx, clientReqID, client)
	ret0, _ := ret[0].(*celestial.WorkflowRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflowByClientRequestId indicates an expected call of GetWorkflowByClientRequestId.
func (mr *MockCelestialProcessorMockRecorder) GetWorkflowByClientRequestId(ctx, clientReqID, client interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflowByClientRequestId", reflect.TypeOf((*MockCelestialProcessor)(nil).GetWorkflowByClientRequestId), ctx, clientReqID, client)
}

// InitiateWorkflow mocks base method.
func (m *MockCelestialProcessor) InitiateWorkflow(ctx context.Context, clientReqId *celestial.ClientReqId, actorId string, payload []byte, workflowType workflow.Type, version workflow.Version) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateWorkflow", ctx, clientReqId, actorId, payload, workflowType, version)
	ret0, _ := ret[0].(error)
	return ret0
}

// InitiateWorkflow indicates an expected call of InitiateWorkflow.
func (mr *MockCelestialProcessorMockRecorder) InitiateWorkflow(ctx, clientReqId, actorId, payload, workflowType, version interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateWorkflow", reflect.TypeOf((*MockCelestialProcessor)(nil).InitiateWorkflow), ctx, clientReqId, actorId, payload, workflowType, version)
}

// InitiateWorkflowV2 mocks base method.
func (m *MockCelestialProcessor) InitiateWorkflowV2(ctx context.Context, clientReqId *workflow.ClientReqId, actorId string, payload []byte, workflowType *workflow.TypeEnum, version workflow.Version, qos celestial.QoS) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateWorkflowV2", ctx, clientReqId, actorId, payload, workflowType, version, qos)
	ret0, _ := ret[0].(error)
	return ret0
}

// InitiateWorkflowV2 indicates an expected call of InitiateWorkflowV2.
func (mr *MockCelestialProcessorMockRecorder) InitiateWorkflowV2(ctx, clientReqId, actorId, payload, workflowType, version, qos interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateWorkflowV2", reflect.TypeOf((*MockCelestialProcessor)(nil).InitiateWorkflowV2), ctx, clientReqId, actorId, payload, workflowType, version, qos)
}

// SignalWorkflow mocks base method.
func (m *MockCelestialProcessor) SignalWorkflow(ctx context.Context, clientReqID, signalID string, client workflow.Client, payload []byte, ownership common.Ownership, useCase common.UseCase) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SignalWorkflow", ctx, clientReqID, signalID, client, payload, ownership, useCase)
	ret0, _ := ret[0].(error)
	return ret0
}

// SignalWorkflow indicates an expected call of SignalWorkflow.
func (mr *MockCelestialProcessorMockRecorder) SignalWorkflow(ctx, clientReqID, signalID, client, payload, ownership, useCase interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SignalWorkflow", reflect.TypeOf((*MockCelestialProcessor)(nil).SignalWorkflow), ctx, clientReqID, signalID, client, payload, ownership, useCase)
}

// MockActorProcessor is a mock of ActorProcessor interface.
type MockActorProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockActorProcessorMockRecorder
}

// MockActorProcessorMockRecorder is the mock recorder for MockActorProcessor.
type MockActorProcessorMockRecorder struct {
	mock *MockActorProcessor
}

// NewMockActorProcessor creates a new mock instance.
func NewMockActorProcessor(ctrl *gomock.Controller) *MockActorProcessor {
	mock := &MockActorProcessor{ctrl: ctrl}
	mock.recorder = &MockActorProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockActorProcessor) EXPECT() *MockActorProcessorMockRecorder {
	return m.recorder
}

// FetchActorName mocks base method.
func (m *MockActorProcessor) FetchActorName(ctx context.Context, actorId string) (*common.Name, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchActorName", ctx, actorId)
	ret0, _ := ret[0].(*common.Name)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchActorName indicates an expected call of FetchActorName.
func (mr *MockActorProcessorMockRecorder) FetchActorName(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchActorName", reflect.TypeOf((*MockActorProcessor)(nil).FetchActorName), ctx, actorId)
}

// GetActorDetails mocks base method.
func (m *MockActorProcessor) GetActorDetails(ctx context.Context, actorId string) (*actor.GetEntityDetailsByActorIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActorDetails", ctx, actorId)
	ret0, _ := ret[0].(*actor.GetEntityDetailsByActorIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActorDetails indicates an expected call of GetActorDetails.
func (mr *MockActorProcessorMockRecorder) GetActorDetails(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActorDetails", reflect.TypeOf((*MockActorProcessor)(nil).GetActorDetails), ctx, actorId)
}

// ResolveOtherActorAndPiForOffAppRecurringPayment mocks base method.
func (m *MockActorProcessor) ResolveOtherActorAndPiForOffAppRecurringPayment(ctx context.Context, rpType recurringpayment.RecurringPaymentType, req *actor0.ResolveOtherActorAndPiForOffAppRecurringPaymentReq) (*paymentinstrument.PaymentInstrument, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResolveOtherActorAndPiForOffAppRecurringPayment", ctx, rpType, req)
	ret0, _ := ret[0].(*paymentinstrument.PaymentInstrument)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ResolveOtherActorAndPiForOffAppRecurringPayment indicates an expected call of ResolveOtherActorAndPiForOffAppRecurringPayment.
func (mr *MockActorProcessorMockRecorder) ResolveOtherActorAndPiForOffAppRecurringPayment(ctx, rpType, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResolveOtherActorAndPiForOffAppRecurringPayment", reflect.TypeOf((*MockActorProcessor)(nil).ResolveOtherActorAndPiForOffAppRecurringPayment), ctx, rpType, req)
}

// MockUpiProcessor is a mock of UpiProcessor interface.
type MockUpiProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockUpiProcessorMockRecorder
}

// MockUpiProcessorMockRecorder is the mock recorder for MockUpiProcessor.
type MockUpiProcessorMockRecorder struct {
	mock *MockUpiProcessor
}

// NewMockUpiProcessor creates a new mock instance.
func NewMockUpiProcessor(ctrl *gomock.Controller) *MockUpiProcessor {
	mock := &MockUpiProcessor{ctrl: ctrl}
	mock.recorder = &MockUpiProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUpiProcessor) EXPECT() *MockUpiProcessorMockRecorder {
	return m.recorder
}

// IsAuthRequiredForMandateExecution mocks base method.
func (m *MockUpiProcessor) IsAuthRequiredForMandateExecution(ctx context.Context, executionInfo *recurringpayment.UpiMandateExecuteInfo, frequency recurringpayment.AllowedFrequency, amount *money.Money, createdAt *timestamppb.Timestamp) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsAuthRequiredForMandateExecution", ctx, executionInfo, frequency, amount, createdAt)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsAuthRequiredForMandateExecution indicates an expected call of IsAuthRequiredForMandateExecution.
func (mr *MockUpiProcessorMockRecorder) IsAuthRequiredForMandateExecution(ctx, executionInfo, frequency, amount, createdAt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsAuthRequiredForMandateExecution", reflect.TypeOf((*MockUpiProcessor)(nil).IsAuthRequiredForMandateExecution), ctx, executionInfo, frequency, amount, createdAt)
}

// MockExecuteRecurringPaymentNoAuthProcessorV1 is a mock of ExecuteRecurringPaymentNoAuthProcessorV1 interface.
type MockExecuteRecurringPaymentNoAuthProcessorV1 struct {
	ctrl     *gomock.Controller
	recorder *MockExecuteRecurringPaymentNoAuthProcessorV1MockRecorder
}

// MockExecuteRecurringPaymentNoAuthProcessorV1MockRecorder is the mock recorder for MockExecuteRecurringPaymentNoAuthProcessorV1.
type MockExecuteRecurringPaymentNoAuthProcessorV1MockRecorder struct {
	mock *MockExecuteRecurringPaymentNoAuthProcessorV1
}

// NewMockExecuteRecurringPaymentNoAuthProcessorV1 creates a new mock instance.
func NewMockExecuteRecurringPaymentNoAuthProcessorV1(ctrl *gomock.Controller) *MockExecuteRecurringPaymentNoAuthProcessorV1 {
	mock := &MockExecuteRecurringPaymentNoAuthProcessorV1{ctrl: ctrl}
	mock.recorder = &MockExecuteRecurringPaymentNoAuthProcessorV1MockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockExecuteRecurringPaymentNoAuthProcessorV1) EXPECT() *MockExecuteRecurringPaymentNoAuthProcessorV1MockRecorder {
	return m.recorder
}

// EnquirePaymentStatus mocks base method.
func (m *MockExecuteRecurringPaymentNoAuthProcessorV1) EnquirePaymentStatus(ctx context.Context, req *internal.EnquirePaymentStatusReq) (*internal.EnquirePaymentStatusRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnquirePaymentStatus", ctx, req)
	ret0, _ := ret[0].(*internal.EnquirePaymentStatusRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EnquirePaymentStatus indicates an expected call of EnquirePaymentStatus.
func (mr *MockExecuteRecurringPaymentNoAuthProcessorV1MockRecorder) EnquirePaymentStatus(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnquirePaymentStatus", reflect.TypeOf((*MockExecuteRecurringPaymentNoAuthProcessorV1)(nil).EnquirePaymentStatus), ctx, req)
}

// ExecutePayment mocks base method.
func (m *MockExecuteRecurringPaymentNoAuthProcessorV1) ExecutePayment(ctx context.Context, recurringPaymentId string, transaction *payment.Transaction) (*internal.ExecutePaymentRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecutePayment", ctx, recurringPaymentId, transaction)
	ret0, _ := ret[0].(*internal.ExecutePaymentRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExecutePayment indicates an expected call of ExecutePayment.
func (mr *MockExecuteRecurringPaymentNoAuthProcessorV1MockRecorder) ExecutePayment(ctx, recurringPaymentId, transaction interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecutePayment", reflect.TypeOf((*MockExecuteRecurringPaymentNoAuthProcessorV1)(nil).ExecutePayment), ctx, recurringPaymentId, transaction)
}

// GetEnrichedTransaction mocks base method.
func (m *MockExecuteRecurringPaymentNoAuthProcessorV1) GetEnrichedTransaction(ctx context.Context, transaction *payment.Transaction) (*payment.Transaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEnrichedTransaction", ctx, transaction)
	ret0, _ := ret[0].(*payment.Transaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEnrichedTransaction indicates an expected call of GetEnrichedTransaction.
func (mr *MockExecuteRecurringPaymentNoAuthProcessorV1MockRecorder) GetEnrichedTransaction(ctx, transaction interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnrichedTransaction", reflect.TypeOf((*MockExecuteRecurringPaymentNoAuthProcessorV1)(nil).GetEnrichedTransaction), ctx, transaction)
}

// ValidatePayment mocks base method.
func (m *MockExecuteRecurringPaymentNoAuthProcessorV1) ValidatePayment(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidatePayment", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidatePayment indicates an expected call of ValidatePayment.
func (mr *MockExecuteRecurringPaymentNoAuthProcessorV1MockRecorder) ValidatePayment(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidatePayment", reflect.TypeOf((*MockExecuteRecurringPaymentNoAuthProcessorV1)(nil).ValidatePayment), ctx)
}
