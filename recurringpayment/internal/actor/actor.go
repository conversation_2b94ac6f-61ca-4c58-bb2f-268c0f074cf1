package actor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/mask"

	"github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	merchantPb "github.com/epifi/gamma/api/merchant"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	pb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/pkg/merchant"
	"github.com/epifi/gamma/pkg/pay"
)

type ActorProcessor struct {
	actorClient    actorPb.ActorClient
	piClient       piPb.PiClient
	merchantClient merchantPb.MerchantServiceClient
}

func NewActorProcessor(actorClient actorPb.ActorClient, piClient piPb.PiClient,
	merchantClient merchantPb.MerchantServiceClient) *ActorProcessor {
	return &ActorProcessor{
		actorClient:    actorClient,
		piClient:       piClient,
		merchantClient: merchantClient,
	}
}

func (p *ActorProcessor) FetchActorName(ctx context.Context, actorId string) (*commontypes.Name, error) {
	actorResp, err := p.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(actorResp, err); te != nil {
		return nil, fmt.Errorf("error while fetching actor details %w", err)
	}
	return actorResp.GetName(), nil
}

func (p *ActorProcessor) GetActorDetails(ctx context.Context, actorId string) (*actorPb.GetEntityDetailsByActorIdResponse, error) {
	res, err := p.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{ActorId: actorId})
	switch {
	case err != nil:
		return nil, fmt.Errorf("actorClient.GetEntityDetailsByActorId() failed due to connection issues %w", epifierrors.ErrTransient)
	case res.GetStatus().IsRecordNotFound():
		return nil, fmt.Errorf("actorClient.GetEntityDetailsByActorId() returned record not found for actor: %v: %w", actorId, epifierrors.ErrPermanent)
	case !res.GetStatus().IsSuccess():
		return nil, fmt.Errorf("actorClient.GetEntityDetailsByActorId() returned unsuccessful status %v, %w", res.GetStatus().String(), epifierrors.ErrTransient)
	default:
		return res, nil
	}
}

type ResolveOtherActorAndPiForOffAppRecurringPaymentReq struct {
	// common fields
	InternalActorId string
	// recurring payment specific data which is used for the resolution
	EnachPayload *EnachPayloadToResolveActorAndPi
}

// EnachPayloadToResolveActorAndPi in case of enach, pi is resolved based upon the name similarly we are doing for the atm transactions
type EnachPayloadToResolveActorAndPi struct {
	OrgName string
}

func (p *ActorProcessor) ResolveOtherActorAndPiForOffAppRecurringPayment(ctx context.Context, rpType pb.RecurringPaymentType, req *ResolveOtherActorAndPiForOffAppRecurringPaymentReq) (*piPb.PaymentInstrument, string, error) {
	if rpType == pb.RecurringPaymentType_ENACH_MANDATES {
		return p.resolveOtherActorAndPiForOffAppEnach(ctx, req.InternalActorId, req.EnachPayload)
	}
	return nil, "", epifierrors.ErrInvalidArgument
}

func (p *ActorProcessor) resolveOtherActorAndPiForOffAppEnach(ctx context.Context, internalActorId string, req *EnachPayloadToResolveActorAndPi) (*piPb.PaymentInstrument, string, error) {
	// step 1: create pi and merchant
	otherActorPi, otherActorPiErr := p.resolveOtherActorPiAndMerchantForEnach(ctx, req.OrgName)
	if otherActorPiErr != nil {
		return nil, "", otherActorPiErr
	}
	// step 2: create other actor -> since off app enach can only be of type debit
	otherActorId, otherActorIdErr := p.resolveOtherActorForEnach(ctx, internalActorId, otherActorPi.GetId(), req.OrgName)
	if otherActorIdErr != nil {
		return nil, "", otherActorIdErr
	}
	return otherActorPi, otherActorId, nil
}

func (p *ActorProcessor) resolveOtherActorPiAndMerchantForEnach(ctx context.Context, orgName string) (*piPb.PaymentInstrument, error) {
	accountNumber := merchant.CreateUniqueAccountNumber(orgName)
	otherActorPi, otherActorPiErr := p.piClient.CreatePi(ctx, &piPb.CreatePiRequest{
		Type:         piPb.PaymentInstrumentType_GENERIC,
		VerifiedName: orgName,
		Identifier: &piPb.CreatePiRequest_Account{
			Account: &piPb.Account{
				ActualAccountNumber: accountNumber,
				IfscCode:            pay.FederalAccountGenericIfscCode,
				AccountType:         accounts.Type_SAVINGS,
				SecureAccountNumber: mask.MaskLastNDigits(accountNumber, 3, ""),
				Name:                orgName,
			},
		},
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		Ownership: piPb.Ownership_EPIFI_TECH,
	})
	if err := epifigrpc.RPCError(otherActorPi, otherActorPiErr); err != nil {
		return nil, fmt.Errorf("failed to create pi %w", err)
	}
	// create merchant based upon the name so that every transaction done with the given merchant resolves on the same timeline
	createMerchantResp, createMerchantErr := p.merchantClient.CreateMerchant(ctx, &merchantPb.CreateMerchantRequest{
		LegalName: orgName,
		BrandName: orgName,
		// todo(Harleen Singh): sync with product and see how to get the logos
		LogoUrl: "",
		PiId:    otherActorPi.GetPaymentInstrument().GetId(),
	})
	if err := epifigrpc.RPCError(createMerchantResp, createMerchantErr); err != nil {
		return nil, fmt.Errorf("failed to create merchant %w", err)
	}
	return otherActorPi.GetPaymentInstrument(), nil
}

func (p *ActorProcessor) resolveOtherActorForEnach(ctx context.Context, fromActorId, piTo, actorToName string) (string, error) {
	resolveActorTo, resolveActorToErr := p.actorClient.ResolveActorTo(ctx, &actorPb.ResolveActorToRequest{
		ActorFrom:   fromActorId,
		PiTo:        piTo,
		ActorToName: actorToName,
	})
	if err := epifigrpc.RPCError(resolveActorTo, resolveActorToErr); err != nil {
		return "", fmt.Errorf("failed to resolve other actor %w", err)
	}
	return resolveActorTo.GetActorTo(), nil
}
