package actor_test

import (
	"os"
	"testing"

	"github.com/golang/mock/gomock"

	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	merchantMocks "github.com/epifi/gamma/api/merchant/mocks"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	"github.com/epifi/gamma/recurringpayment/internal/actor"
	"github.com/epifi/gamma/recurringpayment/test"
)

type mockedDependencies struct {
	actorClient    *actorMocks.MockActorClient
	piClient       *piMocks.MockPiClient
	merchantClient *merchantMocks.MockMerchantServiceClient
}

func newProcessorWithMocks(t *testing.T) (*actor.ActorProcessor, *mockedDependencies, func()) {
	ctr := gomock.NewController(t)
	mockActorClient := actorMocks.NewMockActorClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockMerchantClient := merchantMocks.NewMockMerchantServiceClient(ctr)

	md := &mockedDependencies{
		actorClient:    mockActorClient,
		piClient:       mockPiClient,
		merchantClient: mockMerchantClient,
	}

	p := actor.NewActorProcessor(mockActorClient, mockPiClient, mockMerchantClient)

	return p, md, func() {
		ctr.Finish()
	}
}

// nolint:dogsled
func TestMain(m *testing.M) {
	var teardown func()
	_, _, _, _, teardown = test.InitTestServerV2()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
