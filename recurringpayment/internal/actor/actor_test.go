package actor_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/mask"

	"github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	merchant2 "github.com/epifi/gamma/api/merchant"
	merchantMocks "github.com/epifi/gamma/api/merchant/mocks"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	"github.com/epifi/gamma/api/recurringpayment"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/pkg/merchant"
	"github.com/epifi/gamma/pkg/pay"
	"github.com/epifi/gamma/recurringpayment/internal/actor"
)

func TestActorProcessor_FetchActorName(t *testing.T) {
	type args struct {
		ctx     context.Context
		actorId string
	}
	type mockActorClientEntityDetails struct {
		enable bool
		req    *actorPb.GetEntityDetailsByActorIdRequest
		res    *actorPb.GetEntityDetailsByActorIdResponse
		err    error
	}
	tests := []struct {
		name                         string
		args                         args
		mockActorClientEntityDetails mockActorClientEntityDetails
		want                         *commontypes.Name
		wantErr                      bool
	}{
		{
			name: "actor name fetched successfully",
			args: args{
				ctx:     context.Background(),
				actorId: "actor_Id",
			},
			mockActorClientEntityDetails: mockActorClientEntityDetails{
				enable: true,
				req:    &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor_Id"},
				res: &actorPb.GetEntityDetailsByActorIdResponse{
					Status: rpcPb.StatusOk(),
					Name: &commontypes.Name{
						FirstName:  "first",
						MiddleName: "",
						LastName:   "last",
						Honorific:  "",
					},
				},
				err: nil,
			},
			want: &commontypes.Name{
				FirstName:  "first",
				MiddleName: "",
				LastName:   "last",
				Honorific:  "",
			},
			wantErr: false,
		},
		{
			name: "failure in getting actor name due to connection issues",
			args: args{
				ctx:     context.Background(),
				actorId: "actor_Id",
			},
			mockActorClientEntityDetails: mockActorClientEntityDetails{
				enable: true,
				req:    &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor_Id"},
				res:    nil,
				err:    errors.New("error"),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failure in fetching actor name due to non-ok code",
			args: args{
				ctx:     context.Background(),
				actorId: "actor_Id",
			},
			mockActorClientEntityDetails: mockActorClientEntityDetails{
				enable: true,
				req:    &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor_Id"},
				res: &actorPb.GetEntityDetailsByActorIdResponse{
					Status: rpcPb.StatusInternal(),
				},
				err: nil,
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md, assertMocks := newProcessorWithMocks(t)

			if tt.mockActorClientEntityDetails.enable {
				md.actorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), tt.mockActorClientEntityDetails.req).Return(
					tt.mockActorClientEntityDetails.res, tt.mockActorClientEntityDetails.err)
			}

			got, err := p.FetchActorName(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("FetchActorName() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("FetchActorName() got = %v, want %v", got, tt.want)
			}
			assertMocks()
		})
	}
}

func TestActorProcessor_GetActorDetails(t *testing.T) {
	type args struct {
		ctx     context.Context
		actorId string
	}
	type mockActorClientEntityDetails struct {
		enable bool
		req    *actorPb.GetEntityDetailsByActorIdRequest
		res    *actorPb.GetEntityDetailsByActorIdResponse
		err    error
	}
	tests := []struct {
		name                         string
		mockActorClientEntityDetails mockActorClientEntityDetails
		args                         args
		want                         *actorPb.GetEntityDetailsByActorIdResponse
		wantErr                      bool
	}{
		{
			name: "actor details fetched successfully",
			args: args{
				ctx:     context.Background(),
				actorId: "actor_Id",
			},
			mockActorClientEntityDetails: mockActorClientEntityDetails{
				enable: true,
				req:    &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor_Id"},
				res: &actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpcPb.StatusOk(),
					EntityId: "entity-id",
					Name: &commontypes.Name{
						FirstName:  "first",
						MiddleName: "",
						LastName:   "last",
						Honorific:  "",
					},
				},
				err: nil,
			},
			want: &actorPb.GetEntityDetailsByActorIdResponse{
				Status:   rpcPb.StatusOk(),
				EntityId: "entity-id",
				Name: &commontypes.Name{
					FirstName:  "first",
					MiddleName: "",
					LastName:   "last",
					Honorific:  "",
				},
			},
			wantErr: false,
		},
		{
			name: "failure in getting actor details due to connection issues",
			args: args{
				ctx:     context.Background(),
				actorId: "actor_Id",
			},
			mockActorClientEntityDetails: mockActorClientEntityDetails{
				enable: true,
				req:    &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor_Id"},
				res:    nil,
				err:    errors.New("error"),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failure in fetching actor details due to non-ok code",
			args: args{
				ctx:     context.Background(),
				actorId: "actor_Id",
			},
			mockActorClientEntityDetails: mockActorClientEntityDetails{
				enable: true,
				req:    &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor_Id"},
				res: &actorPb.GetEntityDetailsByActorIdResponse{
					Status: rpcPb.StatusInternal(),
				},
				err: nil,
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md, assertMocks := newProcessorWithMocks(t)
			if tt.mockActorClientEntityDetails.enable {
				md.actorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), tt.mockActorClientEntityDetails.req).Return(
					tt.mockActorClientEntityDetails.res, tt.mockActorClientEntityDetails.err)
			}
			got, err := p.GetActorDetails(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetActorDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetActorDetails() got = %v, want %v", got, tt.want)
			}
			assertMocks()
		})
	}
}

func TestActorProcessor_ResolveOtherActorAndPiForOffAppRecurringPayment(t *testing.T) {
	type args struct {
		ctx    context.Context
		rpType recurringpayment.RecurringPaymentType
		req    *actor.ResolveOtherActorAndPiForOffAppRecurringPaymentReq
	}
	defaultOrgName := "Groww"
	defaultInternalActorId := "default-intenal-actor-id"
	defaultOtherActorId := "default-other-actor-id"
	defaultAccountNumber := merchant.CreateUniqueAccountNumber(defaultOrgName)
	defaultOtherActorPiId := "default-other-actor-pi-id"
	defaultCreatePiRequest := &piPb.CreatePiRequest{
		Type:         piPb.PaymentInstrumentType_GENERIC,
		VerifiedName: defaultOrgName,
		Identifier: &piPb.CreatePiRequest_Account{
			Account: &piPb.Account{
				ActualAccountNumber: defaultAccountNumber,
				IfscCode:            pay.FederalAccountGenericIfscCode,
				AccountType:         accounts.Type_SAVINGS,
				SecureAccountNumber: mask.MaskLastNDigits(defaultAccountNumber, 3, ""),
				Name:                defaultOrgName,
			},
		},
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		Ownership: piPb.Ownership_EPIFI_TECH,
	}
	defaultCreatePiResponse := &piPb.CreatePiResponse{
		Status: rpcPb.StatusOk(),
		PaymentInstrument: &piPb.PaymentInstrument{
			Id: defaultOtherActorPiId,
		},
	}
	defaultEnachPayload := &actor.EnachPayloadToResolveActorAndPi{
		OrgName: defaultOrgName,
	}
	defaultCreateMerchantRequest := &merchant2.CreateMerchantRequest{
		LegalName: defaultOrgName,
		BrandName: defaultOrgName,
		PiId:      defaultOtherActorPiId,
	}
	defaultCreateMerchantResponse := &merchant2.CreateMerchantResponse{
		Status: rpcPb.StatusOk(),
	}
	defaultResolveActorToRequest := &actorPb.ResolveActorToRequest{
		ActorFrom:   defaultInternalActorId,
		PiTo:        defaultOtherActorPiId,
		ActorToName: defaultOrgName,
	}
	defaultResolveOtherActorAndPiForOffAppRecurringPaymentReq := &actor.ResolveOtherActorAndPiForOffAppRecurringPaymentReq{
		InternalActorId: defaultInternalActorId,
		EnachPayload:    defaultEnachPayload,
	}
	tests := []struct {
		name        string
		args        args
		setupMocks  func(mockPiClient *piMocks.MockPiClient, mockMerchantClient *merchantMocks.MockMerchantServiceClient, mockActorClient *actorMocks.MockActorClient)
		wantPiId    string
		wantActorId string
		wantErr     bool
	}{
		{
			name: "should return error (not supported recurring payment type)",
			args: args{
				ctx: context.Background(),
			},
			setupMocks: func(mockPiClient *piMocks.MockPiClient, mockMerchantClient *merchantMocks.MockMerchantServiceClient, actorClient *actorMocks.MockActorClient) {
			},
			wantErr: true,
		},
		{
			name: "should return error (failed to create pi)",
			args: args{
				ctx:    context.Background(),
				rpType: rpPb.RecurringPaymentType_ENACH_MANDATES,
				req:    defaultResolveOtherActorAndPiForOffAppRecurringPaymentReq,
			},
			setupMocks: func(mockPiClient *piMocks.MockPiClient, mockMerchantClient *merchantMocks.MockMerchantServiceClient, actorClient *actorMocks.MockActorClient) {
				mockPiClient.EXPECT().CreatePi(context.Background(), defaultCreatePiRequest).Return(&piPb.CreatePiResponse{
					Status: rpcPb.StatusInternal(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "should return error (failed to create merchant)",
			args: args{
				ctx:    context.Background(),
				rpType: rpPb.RecurringPaymentType_ENACH_MANDATES,
				req:    defaultResolveOtherActorAndPiForOffAppRecurringPaymentReq,
			},
			setupMocks: func(mockPiClient *piMocks.MockPiClient, mockMerchantClient *merchantMocks.MockMerchantServiceClient, actorClient *actorMocks.MockActorClient) {
				mockPiClient.EXPECT().CreatePi(context.Background(), defaultCreatePiRequest).Return(defaultCreatePiResponse, nil)
				mockMerchantClient.EXPECT().CreateMerchant(context.Background(), defaultCreateMerchantRequest).
					Return(&merchant2.CreateMerchantResponse{
						Status: rpcPb.StatusInternal(),
					}, nil)
			},
			wantErr: true,
		},
		{
			name: "should return error (failed to resolve other actor)",
			args: args{
				ctx:    context.Background(),
				rpType: rpPb.RecurringPaymentType_ENACH_MANDATES,
				req:    defaultResolveOtherActorAndPiForOffAppRecurringPaymentReq,
			},
			setupMocks: func(mockPiClient *piMocks.MockPiClient, mockMerchantClient *merchantMocks.MockMerchantServiceClient, mockActorClient *actorMocks.MockActorClient) {
				mockPiClient.EXPECT().CreatePi(context.Background(), defaultCreatePiRequest).Return(defaultCreatePiResponse, nil)
				mockMerchantClient.EXPECT().CreateMerchant(context.Background(), defaultCreateMerchantRequest).
					Return(defaultCreateMerchantResponse, nil)
				mockActorClient.EXPECT().ResolveActorTo(context.Background(), defaultResolveActorToRequest).
					Return(&actorPb.ResolveActorToResponse{
						Status: rpcPb.StatusInternal(),
					}, nil)
			},
			wantErr: true,
		},
		{
			name: "happy flow (shuld successfully resolve other actor pi and other actor)",
			args: args{
				ctx:    context.Background(),
				rpType: rpPb.RecurringPaymentType_ENACH_MANDATES,
				req:    defaultResolveOtherActorAndPiForOffAppRecurringPaymentReq,
			},
			setupMocks: func(mockPiClient *piMocks.MockPiClient, mockMerchantClient *merchantMocks.MockMerchantServiceClient, mockActorClient *actorMocks.MockActorClient) {
				mockPiClient.EXPECT().CreatePi(context.Background(), defaultCreatePiRequest).Return(defaultCreatePiResponse, nil)
				mockMerchantClient.EXPECT().CreateMerchant(context.Background(), defaultCreateMerchantRequest).
					Return(defaultCreateMerchantResponse, nil)
				mockActorClient.EXPECT().ResolveActorTo(context.Background(), defaultResolveActorToRequest).
					Return(&actorPb.ResolveActorToResponse{
						Status:  rpcPb.StatusOk(),
						ActorTo: defaultOtherActorId,
					}, nil)
			},
			wantActorId: defaultOtherActorId,
			wantPiId:    defaultOtherActorPiId,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md, assertMocks := newProcessorWithMocks(t)
			tt.setupMocks(md.piClient, md.merchantClient, md.actorClient)
			pi, actorId, err := p.ResolveOtherActorAndPiForOffAppRecurringPayment(tt.args.ctx, tt.args.rpType, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ResolveOtherActorAndPiForOffAppRecurringPayment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if pi.GetId() != tt.wantPiId {
				t.Errorf("ResolveOtherActorAndPiForOffAppRecurringPayment() got = %v, want %v", pi.GetId(), tt.wantPiId)
			}
			if actorId != tt.wantActorId {
				t.Errorf("ResolveOtherActorAndPiForOffAppRecurringPayment() got1 = %v, want %v", actorId, tt.wantActorId)
			}
			assertMocks()
		})
	}
}
