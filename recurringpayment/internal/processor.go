//go:generate mockgen -source=processor.go -destination=mocks/mock_processor.go -package=mocks

package internal

import (
	"context"
	"errors"
	"fmt"

	"github.com/google/wire"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"

	actorPb "github.com/epifi/gamma/api/actor"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/recurringpayment"
	pb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/recurringpayment/dao"
	"github.com/epifi/gamma/recurringpayment/internal/actor"
	actorProcessor "github.com/epifi/gamma/recurringpayment/internal/actor"
	"github.com/epifi/gamma/recurringpayment/internal/celestial"
	upiProcessor "github.com/epifi/gamma/recurringpayment/internal/upi"
	"github.com/epifi/gamma/recurringpayment/internal/user"
)

var WireSet = wire.NewSet(
	NewProcessor, wire.Bind(new(RecurringPaymentProcessor), new(*Processor)),
	celestial.NewCelestialProcessor, wire.Bind(new(CelestialProcessor), new(*celestial.CelestialProcessor)),
	actorProcessor.NewActorProcessor, wire.Bind(new(ActorProcessor), new(*actor.ActorProcessor)),
	upiProcessor.NewUpiProcessor, wire.Bind(new(UpiProcessor), new(*upiProcessor.UpiProcessor)),
	user.UserProcessorFactoryWireSet,
)

var (
	_ ActorProcessor = &actorProcessor.ActorProcessor{}
)

type RecurringPaymentProcessor interface {
	// GetCredBlockType returns if we need authorisation, and if needed type of AUth required.
	// Eg : In case of standing instruction cred block type will be Partner SDK.
	// Deprecated: use GetCredBlockTypeV2 instead
	GetCredBlockType(recurringPaymentType pb.RecurringPaymentType, actorRole pb.ActorRole) (bool, pb.CredBlockType, error)
	// Deprecated: please use ActorProcessor going forward for actor specific methods
	FetchActorName(ctx context.Context, actorId string) (*commontypes.Name, error)
	// GetRecurringPaymentActionByClientReqId - fetches recurring payment action using client request ID
	GetRecurringPaymentActionByClientReqId(ctx context.Context, clientReqId string) (*pb.RecurringPaymentsAction, error)
	// GetRecurringPaymentById - fetches recurring payment by ID
	GetRecurringPaymentById(ctx context.Context, recurringPaymentId string) (*pb.RecurringPayment, error)
	// GetCredBlockTypeV2 accepts a request object instead of individual parameters and determines the cred block type required for the given request
	GetCredBlockTypeV2(request *GetCredBlockTypeV2Request) (*GetCredBlockTypeV2Response, error)
}

type CelestialProcessor interface {
	// SignalWorkflow helps in sending signal to the workflow, it takes client request ID and client enum as input
	SignalWorkflow(ctx context.Context, clientReqID string, signalID string, client workflowPb.Client, payload []byte, ownership commontypes.Ownership, useCase commontypes.UseCase) error
	// GetWorkflowByClientRequestId fetches workflow request for given client req ID
	GetWorkflowByClientRequestId(ctx context.Context, clientReqID string, client workflowPb.Client) (*celestialPb.WorkflowRequest, error)
	// InitiateWorkflow initiates a new workflow for given client req ID
	// Deprecated: in favour of InitiateWorkflowV2
	InitiateWorkflow(ctx context.Context, clientReqId *celestialPb.ClientReqId, actorId string, payload []byte, workflowType workflowPb.Type, version workflowPb.Version) error
	// InitiateWorkflowV2 initiates the new celestial workflow for given params
	InitiateWorkflowV2(ctx context.Context, clientReqId *workflowPb.ClientReqId, actorId string, payload []byte, workflowType *workflowPb.TypeEnum, version workflowPb.Version, qos celestialPb.QoS) error
}

type ActorProcessor interface {
	// FetchActorName - Returns the name of an actor for given actor Id
	FetchActorName(ctx context.Context, actorId string) (*commontypes.Name, error)
	// GetActorDetails - fetches entity level details like mobile number, name etc for a given actorId
	GetActorDetails(ctx context.Context, actorId string) (*actorPb.GetEntityDetailsByActorIdResponse, error)
	// ResolveOtherActorAndPiForOffAppRecurringPayment resolves the actor id and the pi for creating the recurring payment that was created on other apps like Groww
	ResolveOtherActorAndPiForOffAppRecurringPayment(ctx context.Context, rpType pb.RecurringPaymentType, req *actorProcessor.ResolveOtherActorAndPiForOffAppRecurringPaymentReq) (*piPb.PaymentInstrument, string, error)
}

type UpiProcessor interface {
	// IsAuthRequiredForMandateExecution returns if auth is required for the mandate execution or not
	IsAuthRequiredForMandateExecution(ctx context.Context, executionInfo *recurringpayment.UpiMandateExecuteInfo, frequency recurringpayment.AllowedFrequency, amount *moneyPb.Money, createdAt *timestamppb.Timestamp) (bool, error)
}

type EnquirePaymentStatusReq struct {
	OriginalRequestId string
	PartnerBank       commonvgpb.Vendor
	ActorId           string
	PaymentProtocol   paymentPb.PaymentProtocol
}
type EnquirePaymentStatusRes struct {
	TransactionStatus      paymentPb.TransactionStatus
	Utr                    string
	RawResponseCode        string
	RawResponseDescription string
	StatusCode             string
	StatusDescriptionPayer string
	TransactionTimestamp   *timestamppb.Timestamp
}
type ExecutePaymentRes struct {
	VendorResponseCode     string
	VendorResponseReason   string
	StatusCode             string
	StatusDescriptionPayer string
	StatusDescriptionPayee string
}

type GetCredBlockTypeV2Request struct {
	RecurringPaymentType pb.RecurringPaymentType
	ActorRole            pb.ActorRole
	InitiatedBy          pb.InitiatedBy
	Action               pb.Action
}

type GetCredBlockTypeV2Response struct {
	IsAuthRequired bool
	CredBlockType  pb.CredBlockType
}

// ExecuteRecurringPaymentNoAuthProcessorV1 defines the set of functions to be used for executing recurring payments without auth
// Since the calls to these functions are being made from activities,
// IT IS IMPORTANT TO ENSURE IDEMPOTENCY OF THE FOLLOWING FUNCTIONS.
type ExecuteRecurringPaymentNoAuthProcessorV1 interface {
	// ValidatePayment - Run set of validation checks specific to the recurring_payment_type
	ValidatePayment(ctx context.Context) error
	// GetEnrichedTransaction - Augment the default transaction with fields which are specifically needed for recurring_payment_type
	GetEnrichedTransaction(ctx context.Context, transaction *paymentPb.Transaction) (*paymentPb.Transaction, error)
	ExecutePayment(ctx context.Context, recurringPaymentId string, transaction *paymentPb.Transaction) (*ExecutePaymentRes, error)
	EnquirePaymentStatus(ctx context.Context, req *EnquirePaymentStatusReq) (*EnquirePaymentStatusRes, error)
}

var _ RecurringPaymentProcessor = &Processor{}

type Processor struct {
	actorClient                actorPb.ActorClient
	recurringPaymentActionsDao dao.RecurringPaymentsActionDao
	recurringPaymentDao        dao.RecurringPaymentDao
}

func NewProcessor(actorClient actorPb.ActorClient,
	recurringPaymentActionsDao dao.RecurringPaymentsActionDao,
	recurringPaymentDao dao.RecurringPaymentDao) *Processor {
	return &Processor{
		actorClient:                actorClient,
		recurringPaymentActionsDao: recurringPaymentActionsDao,
		recurringPaymentDao:        recurringPaymentDao,
	}
}

func (p *Processor) GetCredBlockType(recurringPaymentType pb.RecurringPaymentType, actorRole pb.ActorRole) (bool, pb.CredBlockType, error) {
	switch actorRole {
	case pb.ActorRole_ACTOR_ROLE_PAYEE:
		return false, pb.CredBlockType_CRED_BLOCK_TYPE_UNSPECIFIED, nil
	case pb.ActorRole_ACTOR_ROLE_PAYER:
		switch recurringPaymentType {
		case pb.RecurringPaymentType_STANDING_INSTRUCTION:
			return true, pb.CredBlockType_PARTNER_SDK, nil
		case pb.RecurringPaymentType_UPI_MANDATES:
			return true, pb.CredBlockType_NPCI, nil
		default:
			return false, pb.CredBlockType_CRED_BLOCK_TYPE_UNSPECIFIED, fmt.Errorf("invalid recurring payment type %s", recurringPaymentType.String())
		}
	default:
		return false, pb.CredBlockType_CRED_BLOCK_TYPE_UNSPECIFIED, fmt.Errorf("invalid actor role %s", actorRole.String())
	}
}

func (p *Processor) GetCredBlockTypeV2(request *GetCredBlockTypeV2Request) (*GetCredBlockTypeV2Response, error) {
	if request == nil {
		return nil, epifierrors.ErrInvalidArgument
	}
	res := &GetCredBlockTypeV2Response{
		IsAuthRequired: false,
		CredBlockType:  pb.CredBlockType_CRED_BLOCK_TYPE_UNSPECIFIED,
	}
	switch request.ActorRole {
	case pb.ActorRole_ACTOR_ROLE_PAYEE:
		return res, nil
	case pb.ActorRole_ACTOR_ROLE_PAYER:
		switch request.RecurringPaymentType {
		case pb.RecurringPaymentType_STANDING_INSTRUCTION:
			return &GetCredBlockTypeV2Response{
				IsAuthRequired: true,
				CredBlockType:  pb.CredBlockType_PARTNER_SDK,
			}, nil
		case pb.RecurringPaymentType_UPI_MANDATES:
			isAuthRequired, credBlockType := getCredblockForUpiMandates(request.InitiatedBy, request.Action)
			return &GetCredBlockTypeV2Response{
				IsAuthRequired: isAuthRequired,
				CredBlockType:  credBlockType,
			}, nil
		default:
			return nil, fmt.Errorf("invalid recurring payment type %s", request.RecurringPaymentType.String())
		}
	default:
		return nil, fmt.Errorf("invalid actor role %s", request.ActorRole.String())
	}
}

func getCredblockForUpiMandates(initiatedBy pb.InitiatedBy, action pb.Action) (bool, pb.CredBlockType) {
	switch action {
	case pb.Action_REVOKE:
		// payee initiated revokes do not need authorisation
		if initiatedBy == pb.InitiatedBy_PAYEE {
			return false, pb.CredBlockType_CRED_BLOCK_TYPE_UNSPECIFIED
		}
		return true, pb.CredBlockType_NPCI
	default:
		return true, pb.CredBlockType_NPCI
	}
}

func (p *Processor) FetchActorName(ctx context.Context, actorId string) (*commontypes.Name, error) {
	actorResp, err := p.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(actorResp, err); te != nil {
		return nil, fmt.Errorf("error while fetching actor details %w", err)
	}
	return actorResp.GetName(), nil
}

func (p *Processor) GetRecurringPaymentActionByClientReqId(ctx context.Context, clientReqId string) (*pb.RecurringPaymentsAction, error) {
	recurringPaymentActionResponse, err := p.recurringPaymentActionsDao.GetByClientRequestId(ctx, clientReqId, false)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return nil, fmt.Errorf("no recurring payments action for given client request id %v %w", err, rpcPb.StatusAsError(rpcPb.StatusRecordNotFound()))
	case err != nil:
		return nil, fmt.Errorf("failed to fetch recurring payment action corresponding to the given client req id due to connection issues %v %w",
			err, rpcPb.StatusAsError(rpcPb.StatusInternal()))
	default:
		logger.Debug(ctx, "recurringPayment action fetched successfully", zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
	}
	return recurringPaymentActionResponse, nil
}

func (p *Processor) GetRecurringPaymentById(ctx context.Context, recurringPaymentId string) (*pb.RecurringPayment, error) {
	recurringPayment, err := p.recurringPaymentDao.GetById(ctx, recurringPaymentId)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return nil, fmt.Errorf("recurringPayment record not found %w", epifierrors.ErrPermanent)
	case err != nil:
		return nil, fmt.Errorf("failed to fetch recurring payment corresponding to the given id due to connection issues %w",
			epifierrors.ErrTransient)
	default:
		logger.Debug(ctx, "recurringPayment fetched successfully", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
	}
	return recurringPayment, nil
}
