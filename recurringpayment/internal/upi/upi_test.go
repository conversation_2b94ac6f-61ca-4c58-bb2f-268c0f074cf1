package upi

import (
	"context"
	"os"
	"testing"

	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/recurringpayment"
	rpServerConfig "github.com/epifi/gamma/recurringpayment/config/server"
	"github.com/epifi/gamma/recurringpayment/test"
)

var (
	conf *rpServerConfig.Config

	executeInfo = &recurringpayment.UpiMandateExecuteInfo{
		PaymentRequestInfo: &paymentPb.PaymentRequestInformation{
			UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
				Mcc: "0000",
				TxnOriginTimestamp: &timestamppb.Timestamp{
					Seconds: 1234 + 60,
					Nanos:   0,
				},
			},
		},
	}

	executeInfoWithMCCHavingEnhancedLimit = &recurringpayment.UpiMandateExecuteInfo{
		PaymentRequestInfo: &paymentPb.PaymentRequestInformation{
			UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
				Mcc: "5413",
				TxnOriginTimestamp: &timestamppb.Timestamp{
					Seconds: 1234 + 60,
					Nanos:   0,
				},
			},
		},
	}
	executeInfoWithSeqNum1AndTimeMoreThanFiveMinutes = &recurringpayment.UpiMandateExecuteInfo{
		PaymentRequestInfo: &paymentPb.PaymentRequestInformation{
			UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
				Mcc: "0000",
				TxnOriginTimestamp: &timestamppb.Timestamp{
					// Adding 6 minutes to the createdAtTimestamp
					Seconds: 1234 + 360,
					Nanos:   0,
				},
			},
		},
		SeqNum: 1,
	}
	executeInfoWithSeqNum1AndTimeLessThanFiveMinutes = &recurringpayment.UpiMandateExecuteInfo{
		PaymentRequestInfo: &paymentPb.PaymentRequestInformation{
			UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
				Mcc: "0000",
				TxnOriginTimestamp: &timestamppb.Timestamp{
					// Adding 1 minutes to the createdAtTimestamp
					Seconds: 1234 + 60,
					Nanos:   0,
				},
			},
		},
		SeqNum: 1,
	}
	executeInfoWithSeqNum2 = &recurringpayment.UpiMandateExecuteInfo{
		PaymentRequestInfo: &paymentPb.PaymentRequestInformation{
			UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
				Mcc: "0000",
				TxnOriginTimestamp: &timestamppb.Timestamp{
					// Adding 6 minutes to the createdAtTimestamp
					Seconds: 1234 + 360,
					Nanos:   0,
				},
			},
		},
		SeqNum: 2,
	}

	createdAtTimestamp = &timestamppb.Timestamp{
		Seconds: 1234,
		Nanos:   0,
	}
)

func TestUpiProcessor_IsAuthRequiredForMandateExecution(t *testing.T) {

	type args struct {
		ctx         context.Context
		executeInfo *recurringpayment.UpiMandateExecuteInfo
		frequency   recurringpayment.AllowedFrequency
		amount      *moneyPb.Money
		createdAt   *timestamppb.Timestamp
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "Should return false when frequency is one time",
			args: args{
				ctx:         context.Background(),
				executeInfo: executeInfo,
				frequency:   recurringpayment.AllowedFrequency_ONE_TIME,
				amount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        16000,
					Nanos:        0,
				},
				createdAt: createdAtTimestamp,
			},
			want:    false,
			wantErr: false,
		},
		{
			name: "Should return false when frequency is weekly and amount is less than 15000",
			args: args{
				ctx:         context.Background(),
				executeInfo: executeInfo,
				frequency:   recurringpayment.AllowedFrequency_WEEKLY,
				amount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        100,
					Nanos:        0,
				},
				createdAt: createdAtTimestamp,
			},
			want:    false,
			wantErr: false,
		},
		{
			name: "Should return true when frequency is weekly and amount is more than 15000",
			args: args{
				ctx:         context.Background(),
				executeInfo: executeInfo,
				frequency:   recurringpayment.AllowedFrequency_WEEKLY,
				amount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        16000,
					Nanos:        0,
				},
				createdAt: createdAtTimestamp,
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "Should return false when frequency is weekly and amount is more than 15000 but less than 1 lac and mcc is 5413",
			args: args{
				ctx:         context.Background(),
				executeInfo: executeInfoWithMCCHavingEnhancedLimit,
				frequency:   recurringpayment.AllowedFrequency_WEEKLY,
				amount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        99000,
					Nanos:        0,
				},
				createdAt: createdAtTimestamp,
			},
			want:    false,
			wantErr: false,
		},
		{
			name: "Should return true when frequency is weekly and amount is more than 1lac and mcc is 5413",
			args: args{
				ctx:         context.Background(),
				executeInfo: executeInfoWithMCCHavingEnhancedLimit,
				frequency:   recurringpayment.AllowedFrequency_WEEKLY,
				amount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        100001,
					Nanos:        0,
				},
				createdAt: createdAtTimestamp,
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "Should return error when frequency is weekly and amount currency is not INR",
			args: args{
				ctx:         context.Background(),
				executeInfo: executeInfoWithMCCHavingEnhancedLimit,
				frequency:   recurringpayment.AllowedFrequency_WEEKLY,
				amount: &moneyPb.Money{
					CurrencyCode: "USD",
					Units:        100,
					Nanos:        0,
				},
				createdAt: createdAtTimestamp,
			},
			want:    false,
			wantErr: true,
		},
		{
			name: "Should return false when amount is less than 15000 and seqNum is 1 and time is less than 5 minutes",
			args: args{
				ctx:         context.Background(),
				executeInfo: executeInfoWithSeqNum1AndTimeLessThanFiveMinutes,
				frequency:   recurringpayment.AllowedFrequency_WEEKLY,
				amount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        100,
					Nanos:        0,
				},
				createdAt: createdAtTimestamp,
			},
			want:    false,
			wantErr: false,
		},
		{
			name: "Should return true when amount is less than 15000 and seqNum is 1 and time is greater than 5 minutes",
			args: args{
				ctx:         context.Background(),
				executeInfo: executeInfoWithSeqNum1AndTimeMoreThanFiveMinutes,
				frequency:   recurringpayment.AllowedFrequency_WEEKLY,
				amount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        100,
					Nanos:        0,
				},
				createdAt: createdAtTimestamp,
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "Should return false when amount is less than 15000 and seqNum is 2",
			args: args{
				ctx:         context.Background(),
				executeInfo: executeInfoWithSeqNum2,
				frequency:   recurringpayment.AllowedFrequency_WEEKLY,
				amount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        100,
					Nanos:        0,
				},
				createdAt: createdAtTimestamp,
			},
			want:    false,
			wantErr: false,
		},
		{
			name: "Should return true when amount is more than 15000 and seqNum is 1",
			args: args{
				ctx:         context.Background(),
				executeInfo: executeInfoWithSeqNum1AndTimeLessThanFiveMinutes,
				frequency:   recurringpayment.AllowedFrequency_WEEKLY,
				amount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        16000,
					Nanos:        0,
				},
				createdAt: createdAtTimestamp,
			},
			want:    true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &UpiProcessor{
				config: conf,
			}
			got, gotErr := p.IsAuthRequiredForMandateExecution(tt.args.ctx, tt.args.executeInfo, tt.args.frequency, tt.args.amount, tt.args.createdAt)

			if (gotErr != nil) != tt.wantErr {
				t.Errorf("IsAuthRequiredForMandateExecution() error = %v, wantErr %v", gotErr, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("IsAuthRequiredForMandateExecution() got = %v, want %v", got, tt.want)
			}
		})
	}
}

// nolint:dogsled
func TestMain(m *testing.M) {
	var teardownV2 func()
	_, conf, _, _, teardownV2 = test.InitTestServerV2()
	exitCode := m.Run()
	teardownV2()
	os.Exit(exitCode)
}
