// nolint: goimports
package upi

import (
	"context"
	"time"

	"github.com/epifi/be-common/pkg/logger"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/recurringpayment"
	rpServerConfig "github.com/epifi/gamma/recurringpayment/config/server"
)

type UpiProcessor struct {
	config *rpServerConfig.Config
}

func NewUpiProcessor(config *rpServerConfig.Config) *UpiProcessor {
	return &UpiProcessor{
		config: config,
	}
}

// IsAuthRequiredForMandateExecution returns if auth is required for the mandate execution or not
// 1. If Allowed Frequency is ONE_TIME, auth is not required
// 2. For the 1st mandate execution, auth is required if the execution time is after 5 minutes of mandate creation
// 3. If the amount is greater than the limit, auth is required
//   - Default limit is 15,000
//   - For Few MCCs custom limits are set as 1 lac
//
// If there is an issue with comparing the amount, it returns error
// TODO - Revisit this to see if we'll need auth for seqNum 1 and execution time is withing 5 minutes.
func (p *UpiProcessor) IsAuthRequiredForMandateExecution(ctx context.Context, executionInfo *recurringpayment.UpiMandateExecuteInfo, frequency recurringpayment.AllowedFrequency, amount *moneyPb.Money, mandateCreationTimestamp *timestamp.Timestamp) (bool, error) {

	mcc := executionInfo.GetPaymentRequestInfo().GetUpiInfo().GetMcc()
	seqNum := executionInfo.GetSeqNum()
	mandateExecutionTimeStamp := executionInfo.GetPaymentRequestInfo().GetUpiInfo().GetTxnOriginTimestamp()

	if frequency == recurringpayment.AllowedFrequency_ONE_TIME {
		return false, nil
	}

	if seqNum == 1 && isAuthRequiredForFirstExecution(ctx, mandateCreationTimestamp, mandateExecutionTimeStamp) {
		// For the 1st mandate execution, auth is required if the execution time is after 5 minutes of mandate creation
		return true, nil
	}

	// default limit for mandate execution for which auth is not required is 15,000
	limit := p.config.MaxAmountForMandateExecutionWithoutAFA

	// Check if the MCC exists in the map and use the corresponding value as the limit
	if customLimit, exists := p.config.MaxAmountLimitForMccWithoutAFA[mcc]; exists {
		limit = customLimit
	}
	compareRes, err := money.CompareV2(amount, limit)
	if err != nil {
		return false, err
	}
	return compareRes == 1, nil
}

// For the 1st mandate execution, auth is required if the execution time is after 5 minutes of mandate creation
// In case of any invalid timestamp, it returns true without any error , because we don't want to block the mandate execution
func isAuthRequiredForFirstExecution(ctx context.Context, creationTimestamp *timestamp.Timestamp, executionTimestamp *timestamp.Timestamp) bool {
	if creationTimestamp == nil {
		logger.Error(ctx, "isAuthRequiredForFirstExecution: creationTimestamp is nil")
		return true
	}
	if executionTimestamp == nil {
		logger.Error(ctx, "isAuthRequiredForFirstExecution: executionTimestamp is nil")
		return true
	}
	return executionTimestamp.AsTime().Sub(creationTimestamp.AsTime()) > 5*time.Minute
}
