version: '3'

networks:
  gamma_network:
    driver: bridge

services:
  cockroachdb:
    image: cockroachdb/cockroach:v23.2.20
    command: start-single-node --insecure
    ports:
      - "26257"
    expose:
      - "26257"
    restart: on-failure
    networks:
      - gamma_network

  cockroachdb_with_fixtures:
    build:
      context: .
      dockerfile: ./Dockerfile.crdb_with_fixtures
    ports:
      - "26257"
    expose:
      - "26257"
    restart: on-failure
    networks:
      - gamma_network

  postgresdb_with_fixtures:
    build:
      context: .
      dockerfile: ./Dockerfile.pgdb_with_fixtures
    ports:
      - "5432:5432"
    restart: on-failure
    networks:
      - gamma_network

  localstack:
    image: localstack/localstack:3.3.0
    environment:
      - ENVIRONMENT=${ENVIRONMENT}
      - SERVICES= s3, sqs, sns, dynamodb, sts, kms, kinesis
    ports:
      - "4566:4566"
    security_opt:
      - label=disable
    volumes:
      - "${PWD}/scripts/localstack:/etc/localstack/init/ready.d" # initializes queues on the container start
    networks:
      - gamma_network

  zinc:
    image: public.ecr.aws/zinclabs/zinc:0.2.8
    # https://docs.zincsearch.com/environment-variables/
    environment:
      - ZINC_DATA_PATH=/data
      - ZINC_FIRST_ADMIN_USER=admin
      - ZINC_FIRST_ADMIN_PASSWORD=Complexpass#123
      - ZINC_SHARD_NUM=1
      - ZINC_BATCH_SIZE=100
    ports:
      - "4080:4080"
    expose:
      - 4080
    restart: on-failure
    networks:
      - gamma_network
    volumes:
      - "${HOME}/data:/data"

  paymentinstrument:
    build:
      context: .
      args:
        service_name: paymentinstrument
      dockerfile: ./Dockerfile
    ports:
      - "8087"
    environment:
      ENVIRONMENT: development
      DB_HOST: cockroachdb
    depends_on:
      - cockroachdb
    restart: on-failure
    networks:
      - gamma_network

  savings:
    build:
      context: .
      args:
        service_name: savings
      dockerfile: ./Dockerfile
    ports:
      - "8084"
    environment:
      ENVIRONMENT: developement
      DB_HOST: cockroachdb
    depends_on:
      - cockroachdb
      - localstack
    restart: on-failure
    networks:
      - gamma_network

  comms:
    build:
      context: .
      args:
        service_name: comms
      dockerfile: ./Dockerfile
    ports:
      - "8085"
    environment:
      ENVIRONMENT: developement
      DB_HOST: cockroachdb
    depends_on:
      - cockroachdb
      - localstack
    restart: on-failure
    networks:
      - gamma_network

  order:
    build:
      context: .
      args:
        service_name: order
      dockerfile: ./Dockerfile
    ports:
      - "8091"
    environment:
      ENVIRONMENT: developement
      DB_HOST: cockroachdb
    depends_on:
      - cockroachdb
    restart: on-failure
    networks:
      - gamma_network

  rewards:
    build:
      context: .
      args:
        service_name: rewards
      dockerfile: ./Dockerfile
    ports:
      - "9091"
    environment:
      ENVIRONMENT: development
      DB_HOST: cockroachdb
    depends_on:
      - cockroachdb
    restart: on-failure
    networks:
      - gamma_network

  tokenizer:
    build:
      context: .
      args:
        service_name: tokenizer
      dockerfile: ./Dockerfile
    ports:
      - "9095"
    environment:
      ENVIRONMENT: development
    restart: on-failure
    networks:
      - gamma_network

  redis:
    image: redis:6.0.8
    ports:
      - "6379:6379"
    expose:
      - "6379"
    restart: on-failure
    networks:
      - gamma_network

  pinot:
    image: apachepinot/pinot:1.0.0
    ports:
      - "9000:9000"
      - "8000:8000"
    command: "QuickStart -type batch"
    restart: on-failure
    networks:
      - gamma_network

  pinot_local_arm64:
    image: apachepinot/pinot:0.12.0-arm64
    ports:
      - "9000:9000"
      - "8000:8000"
    command: "QuickStart -type batch"
    restart: on-failure
    networks:
      - gamma_network

  pinot_local:
    image: apachepinot/pinot:1.0.0
    ports:
      - "9000:9000"
      - "8000:8000"
    command: "QuickStart -type batch"
    restart: on-failure
    networks:
      - gamma_network

  postgresdb:
    container_name: postgresdb
    environment:
      POSTGRES_HOST_AUTH_METHOD: trust
      POSTGRES_USER: root
    image: postgres:14.9
    ports:
      - "5432:5432"
    networks:
      - gamma_network

  elasticsearch:
    container_name: temporal-elasticsearch
    environment:
      - cluster.routing.allocation.disk.threshold_enabled=true
      - cluster.routing.allocation.disk.watermark.low=512mb
      - cluster.routing.allocation.disk.watermark.high=256mb
      - cluster.routing.allocation.disk.watermark.flood_stage=128mb
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms256m -Xmx256m
      - xpack.security.enabled=false
    image: elasticsearch:8.8.0
    networks:
      - gamma_network
    volumes:
      - /var/lib/elasticsearch/data

  temporal:
    container_name: temporal
    depends_on:
      - postgresdb
      - elasticsearch
    environment:
      - DB=postgresql
      - DB_PORT=5432
      - POSTGRES_USER=root
      - POSTGRES_SEEDS=postgresdb
      - DYNAMIC_CONFIG_FILE_PATH=/etc/temporal/config/dynamicconfig/development.yml
      - DEV_NAMESPACE_LIST=/etc/temporal/namespace.txt
      - DEFAULT_NAMESPACE_RETENTION=6h
      - ENABLE_ES=true
      - ES_SEEDS=elasticsearch
      - ES_VERSION=v7
    image: temporalio/auto-setup:1.18.5
    security_opt:
      - label=disable
    ports:
      - "7233:7233"
    volumes:
      - "./build/temporal/auto-setup.sh:/etc/temporal/auto-setup.sh:ro"
      - "./build/temporal/dynamicconfig:/etc/temporal/config/dynamicconfig:ro"
      - "./build/temporal/namespace.txt:/etc/temporal/namespace.txt:ro"
    networks:
      - gamma_network

  temporal-admin-tools:
    container_name: temporal-admin-tools
    depends_on:
      - temporal
    environment:
      - TEMPORAL_CLI_ADDRESS=temporal:7233
    image: temporalio/admin-tools:1.15.2
    networks:
      - gamma_network

  temporal-ui:
    container_name: temporal-ui
    depends_on:
      - temporal
    environment:
      - TEMPORAL_ADDRESS=temporal:7233
      - TEMPORAL_CORS_ORIGINS=http://localhost:3000
    image: temporalio/ui:2.9.1
    ports:
      - "8088:8080"
    networks:
      - gamma_network

  # Jaeger
  jaeger:
    container_name: jaeger
    image: jaegertracing/all-in-one:1.43.0
    ports:
      - "16686:16686"
      - "14250:14250"
    networks:
      - gamma_network

  otel-collector:
    container_name: otel-collector
    image: otel/opentelemetry-collector:0.73.0
    command: [ "--config=/etc/otel-collector-config.yml" ]
    volumes:
      - ./build/otel/otel-collector-config.yml:/etc/otel-collector-config.yml
    ports:
      - "8888:8888"   # Prometheus metrics exposed by the collector
      - "8889:8889"   # Prometheus exporter metrics
      - "13133:13133" # health_check extension
      - "4317:4317" # collector port
      - "55680:55680"
      - "55679:55679" # zpages extension
    depends_on:
      - jaeger
    networks:
      - gamma_network


  keycloak:
    image: quay.io/keycloak/keycloak:22.0.0
    ports:
      - "5400:5400"
    environment:
      - KEYCLOAK_ADMIN=admin
      - KEYCLOAK_ADMIN_PASSWORD=admin
    networks:
      - gamma_network
    volumes:
      - ./data:/opt/jboss/keycloak/standalone/data
    command: [ "start-dev","--http-port=5400" ]
