// nolint: testifylint
package dao

import (
	"context"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/testing/protocmp"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/gamma/api/salaryestimation"
	salaryestimation2 "github.com/epifi/gamma/salaryestimation"
)

var (
	affectedTestTables = []string{"salary_estimation_attempt"}
)

func getSalaryEstimationAttemptDao(t *testing.T) (salaryestimation2.SalaryEstimationAttemptDao, func()) {
	dbInstance, dbInstanceRelease := dbInstancePool.GetDbInstance(t)
	salaryEstimationAttemptDao := NewSalaryEstimationAttemptPgDbDao(dbInstance.GetConnection())
	return salaryEstimationAttemptDao, func() { dbInstanceRelease(affectedTestTables) }
}

func TestSalaryEstimationAttemptDao_Create(t *testing.T) {
	t.Parallel()
	salaryEstimationAttemptDao, releaseDao := getSalaryEstimationAttemptDao(t)
	defer releaseDao()

	type args struct {
		ctx     context.Context
		attempt *salaryestimation.SalaryEstimationAttempt
	}
	tests := []struct {
		name    string
		args    args
		want    *salaryestimation.SalaryEstimationAttempt
		wantErr bool
	}{
		{
			name: "successful creation",
			args: args{
				ctx: context.Background(),
				attempt: &salaryestimation.SalaryEstimationAttempt{
					ClientReqId: "test-client-req-id-1",
					Source:      salaryestimation.Source_SOURCE_AA,
					ActorId:     "test-actor-id-1",
					Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
					ExpiryAt:    timestamp.New(time.Date(2025, 7, 11, 0, 8, 34, 0, datetime.IST)),
				},
			},
			want: &salaryestimation.SalaryEstimationAttempt{
				ClientReqId: "test-client-req-id-1",
				Source:      salaryestimation.Source_SOURCE_AA,
				ActorId:     "test-actor-id-1",
				Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
				ExpiryAt:    timestamp.New(time.Date(2025, 7, 11, 0, 8, 34, 0, datetime.IST)),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := salaryEstimationAttemptDao.Create(tt.args.ctx, tt.args.attempt)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&salaryestimation.SalaryEstimationAttempt{}, "id", "created_at", "updated_at"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("Create() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestSalaryEstimationAttemptDao_GetByClientReqID(t *testing.T) {
	t.Parallel()

	salaryEstimationAttemptDao, releaseDao := getSalaryEstimationAttemptDao(t)
	defer releaseDao()

	type args struct {
		ctx         context.Context
		clientReqID string
	}
	tests := []struct {
		name    string
		args    args
		want    *salaryestimation.SalaryEstimationAttempt
		wantErr bool
		setup   func() *salaryestimation.SalaryEstimationAttempt
	}{
		{
			name: "successful retrieval",
			args: args{
				ctx:         context.Background(),
				clientReqID: "unique-client-req-id",
			},
			want: &salaryestimation.SalaryEstimationAttempt{
				ClientReqId: "unique-client-req-id",
				Source:      salaryestimation.Source_SOURCE_AA,
				ActorId:     "test-actor-id",
				Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
				ExpiryAt:    timestamp.New(time.Date(2025, 7, 11, 0, 8, 34, 0, datetime.IST)),
			},
			wantErr: false,
			setup: func() *salaryestimation.SalaryEstimationAttempt {
				attempt := &salaryestimation.SalaryEstimationAttempt{
					ClientReqId: "unique-client-req-id",
					Source:      salaryestimation.Source_SOURCE_AA,
					ActorId:     "test-actor-id",
					Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
					ExpiryAt:    timestamp.New(time.Date(2025, 7, 11, 0, 8, 34, 0, datetime.IST)),
				}
				created, err := salaryEstimationAttemptDao.Create(context.Background(), attempt)
				require.NoError(t, err)
				return created
			},
		},
		{
			name: "not found",
			args: args{
				ctx:         context.Background(),
				clientReqID: "non-existent-client-req-id",
			},
			want:    nil,
			wantErr: true,
			setup:   func() *salaryestimation.SalaryEstimationAttempt { return nil },
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_ = tt.setup()
			got, err := salaryEstimationAttemptDao.GetByClientReqID(tt.args.ctx, tt.args.clientReqID)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByClientReqID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&salaryestimation.SalaryEstimationAttempt{}, "id", "created_at", "updated_at"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("GetByClientReqID() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestSalaryEstimationAttemptDao_Update(t *testing.T) {
	t.Parallel()
	testAttemptId := uuid.NewString()
	testAttemptClientReqId := "test-client-req-id"
	testActorId := "test-actor-id"

	salaryEstimationAttemptDao, releaseDao := getSalaryEstimationAttemptDao(t)
	defer releaseDao()

	type args struct {
		ctx        context.Context
		attempt    *salaryestimation.SalaryEstimationAttempt
		fieldMasks []salaryestimation.SalaryEstimationAttemptFieldMask
	}
	tests := []struct {
		name    string
		args    args
		want    *salaryestimation.SalaryEstimationAttempt
		wantErr bool
		setup   func() *salaryestimation.SalaryEstimationAttempt
	}{
		{
			name: "successful status update",
			args: args{
				ctx: context.Background(),
				fieldMasks: []salaryestimation.SalaryEstimationAttemptFieldMask{
					salaryestimation.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STATUS,
				},
				attempt: &salaryestimation.SalaryEstimationAttempt{
					Id:       testAttemptId,
					Step:     salaryestimation.AttemptStep_ATTEMPT_STEP_ANALYSIS,
					Status:   salaryestimation.AttemptStatus_ATTEMPT_STATUS_SUCCESSFUL,
					ExpiryAt: timestamp.New(time.Date(2025, 7, 11, 0, 8, 34, 0, datetime.IST)),
				},
			},
			want: &salaryestimation.SalaryEstimationAttempt{
				Id:          testAttemptId,
				ClientReqId: testAttemptClientReqId,
				Source:      salaryestimation.Source_SOURCE_AA,
				ActorId:     testActorId,
				Step:        salaryestimation.AttemptStep_ATTEMPT_STEP_ANALYSIS,
				Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_SUCCESSFUL,
				ExpiryAt:    timestamp.New(time.Date(2025, 7, 11, 0, 8, 34, 0, datetime.IST)),
			},
			wantErr: false,
			setup: func() *salaryestimation.SalaryEstimationAttempt {
				attempt := &salaryestimation.SalaryEstimationAttempt{
					Id:          testAttemptId,
					ClientReqId: testAttemptClientReqId,
					Source:      salaryestimation.Source_SOURCE_AA,
					ActorId:     testActorId,
					Step:        salaryestimation.AttemptStep_ATTEMPT_STEP_ANALYSIS,
					Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
					ExpiryAt:    timestamp.New(time.Date(2025, 7, 11, 0, 8, 34, 0, datetime.IST)),
				}
				created, err := salaryEstimationAttemptDao.Create(context.Background(), attempt)
				require.NoError(t, err)
				return created
			},
		},
		{
			name: "empty id should fail",
			args: args{
				ctx: context.Background(),
				attempt: &salaryestimation.SalaryEstimationAttempt{
					Id:     "", // empty ID
					Step:   salaryestimation.AttemptStep_ATTEMPT_STEP_ANALYSIS,
					Status: salaryestimation.AttemptStatus_ATTEMPT_STATUS_SUCCESSFUL,
				},
				fieldMasks: []salaryestimation.SalaryEstimationAttemptFieldMask{
					salaryestimation.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STATUS,
				},
			},
			want:    nil,
			wantErr: true,
			setup:   func() *salaryestimation.SalaryEstimationAttempt { return nil },
		},
		{
			name: "empty field masks should fail",
			args: args{
				ctx: context.Background(),
				attempt: &salaryestimation.SalaryEstimationAttempt{
					Id:     testAttemptId,
					Step:   salaryestimation.AttemptStep_ATTEMPT_STEP_ANALYSIS,
					Status: salaryestimation.AttemptStatus_ATTEMPT_STATUS_SUCCESSFUL,
				},
				fieldMasks: []salaryestimation.SalaryEstimationAttemptFieldMask{}, // empty field masks
			},
			want:    nil,
			wantErr: true,
			setup:   func() *salaryestimation.SalaryEstimationAttempt { return nil },
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_ = tt.setup()
			got, err := salaryEstimationAttemptDao.Update(tt.args.ctx, tt.args.attempt, tt.args.fieldMasks)
			if (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&salaryestimation.SalaryEstimationAttempt{}, "id", "created_at", "updated_at"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("Update() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestSalaryEstimationAttemptDao_GetByActorId(t *testing.T) {
	t.Parallel()

	salaryEstimationAttemptDao, releaseDao := getSalaryEstimationAttemptDao(t)
	defer releaseDao()

	testActorId := "actor-for-getbyactorid"
	otherActorId := "actor-for-getbyactorid-other"

	type args struct {
		ctx     context.Context
		actorId string
	}

	tests := []struct {
		name    string
		args    args
		want    []*salaryestimation.SalaryEstimationAttempt
		wantErr bool
		setup   func() []*salaryestimation.SalaryEstimationAttempt
	}{
		{
			name: "returns all attempts for actorId sorted by created_at desc",
			args: args{
				ctx:     context.Background(),
				actorId: testActorId,
			},
			want: []*salaryestimation.SalaryEstimationAttempt{
				{
					ClientReqId: "req-2",
					Source:      salaryestimation.Source_SOURCE_AA,
					ActorId:     testActorId,
					Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_SUCCESSFUL,
					ExpiryAt:    timestamp.New(time.Date(2025, 7, 12, 0, 8, 34, 0, datetime.IST)),
				},
				{
					ClientReqId: "req-1",
					Source:      salaryestimation.Source_SOURCE_AA,
					ActorId:     testActorId,
					Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
					ExpiryAt:    timestamp.New(time.Date(2025, 7, 11, 0, 8, 34, 0, datetime.IST)),
				},
			},
			wantErr: false,
			setup: func() []*salaryestimation.SalaryEstimationAttempt {
				// Create two attempts for testActorId and one for otherActorId
				var created []*salaryestimation.SalaryEstimationAttempt
				attempts := []*salaryestimation.SalaryEstimationAttempt{
					{
						ClientReqId: "req-1",
						Source:      salaryestimation.Source_SOURCE_AA,
						ActorId:     testActorId,
						Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
						ExpiryAt:    timestamp.New(time.Date(2025, 7, 11, 0, 8, 34, 0, datetime.IST)),
					},
					{
						ClientReqId: "req-2",
						Source:      salaryestimation.Source_SOURCE_AA,
						ActorId:     testActorId,
						Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_SUCCESSFUL,
						ExpiryAt:    timestamp.New(time.Date(2025, 7, 12, 0, 8, 34, 0, datetime.IST)),
					},
					{
						ClientReqId: "req-3",
						Source:      salaryestimation.Source_SOURCE_AA,
						ActorId:     otherActorId,
						Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_FAILED,
						ExpiryAt:    timestamp.New(time.Date(2025, 7, 13, 0, 8, 34, 0, datetime.IST)),
					},
				}
				for _, att := range attempts {
					createdAtt, err := salaryEstimationAttemptDao.Create(context.Background(), att)
					require.NoError(t, err)
					if att.ActorId == testActorId {
						created = append(created, createdAtt)
					}
				}
				// Sort by created_at desc for expected result
				if len(created) == 2 && created[0].CreatedAt != nil && created[1].CreatedAt != nil {
					if created[0].CreatedAt.AsTime().Before(created[1].CreatedAt.AsTime()) {
						created[0], created[1] = created[1], created[0]
					}
				}
				return created
			},
		},
		{
			name: "returns ErrRecordNotFound if no attempts for actorId",
			args: args{
				ctx:     context.Background(),
				actorId: "non-existent-actor",
			},
			want:    nil,
			wantErr: true,
			setup:   func() []*salaryestimation.SalaryEstimationAttempt { return nil },
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_ = tt.setup()
			got, err := salaryEstimationAttemptDao.GetByActorId(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				// Compare length and key fields, ignore id, created_at, updated_at
				require.Equal(t, len(tt.want), len(got))
				opts := []cmp.Option{
					protocmp.Transform(),
					protocmp.IgnoreFields(&salaryestimation.SalaryEstimationAttempt{}, "id", "created_at", "updated_at"),
				}
				for i := range tt.want {
					if diff := cmp.Diff(tt.want[i], got[i], opts...); diff != "" {
						t.Errorf("GetByActorId() mismatch (-want +got):\n%s", diff)
					}
				}
			} else {
				require.ErrorIs(t, err, epifierrors.ErrRecordNotFound)
			}
		})
	}
}
