package model

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/gamma/api/salaryestimation"
)

// SalaryEstimationAttempt represents the database model for salary estimation attempts
type SalaryEstimationAttempt struct {
	ID           string `gorm:"primary_key;type:uuid;default:gen_random_uuid()"`
	ClientReqID  string
	Source       salaryestimation.Source
	ActorID      string
	ClientParams *salaryestimation.ClientParams `gorm:"type:jsonb"`
	Step         salaryestimation.AttemptStep
	Status       salaryestimation.AttemptStatus
	SubStatus    salaryestimation.AttemptSubStatus
	AttemptInfo  *salaryestimation.AttemptInfo `gorm:"type:jsonb"`
	CreatedAt    time.Time
	UpdatedAt    time.Time
	ExpiryAt     time.Time
	DeletedAt    gorm.DeletedAt
}

// NewSalaryEstimationAttempt creates a new model from proto
func NewSalaryEstimationAttempt(proto *salaryestimation.SalaryEstimationAttempt) *SalaryEstimationAttempt {
	model := &SalaryEstimationAttempt{
		ID:           proto.GetId(),
		ClientReqID:  proto.GetClientReqId(),
		Source:       proto.GetSource(),
		ActorID:      proto.GetActorId(),
		ClientParams: proto.GetClientParams(),
		Step:         proto.GetStep(),
		Status:       proto.GetStatus(),
		SubStatus:    proto.GetSubStatus(),
		AttemptInfo:  proto.GetAttemptInfo(),
	}
	if proto.GetCreatedAt() != nil {
		model.CreatedAt = proto.GetCreatedAt().AsTime()
	}
	if proto.GetUpdatedAt() != nil {
		model.UpdatedAt = proto.GetUpdatedAt().AsTime()
	}
	if proto.GetExpiryAt() != nil {
		model.ExpiryAt = proto.GetExpiryAt().AsTime()
	}
	if proto.GetDeletedAt() != nil {
		model.DeletedAt = gorm.DeletedAt{
			Time:  proto.GetDeletedAt().AsTime(),
			Valid: true,
		}
	}
	return model
}

func (m *SalaryEstimationAttempt) TableName() string {
	return "salary_estimation_attempt"
}

// GetProto converts model to proto
func (m *SalaryEstimationAttempt) GetProto() *salaryestimation.SalaryEstimationAttempt {
	proto := &salaryestimation.SalaryEstimationAttempt{
		Id:           m.ID,
		ClientReqId:  m.ClientReqID,
		Source:       m.Source,
		ActorId:      m.ActorID,
		ClientParams: m.ClientParams,
		Step:         m.Step,
		Status:       m.Status,
		SubStatus:    m.SubStatus,
		AttemptInfo:  m.AttemptInfo,
		CreatedAt:    timestamppb.New(m.CreatedAt),
		UpdatedAt:    timestamppb.New(m.UpdatedAt),
		ExpiryAt:     timestamppb.New(m.ExpiryAt),
	}
	if m.DeletedAt.Valid {
		proto.DeletedAt = timestamppb.New(m.DeletedAt.Time)
	}
	return proto
}
