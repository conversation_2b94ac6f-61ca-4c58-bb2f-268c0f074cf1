package events

import (
	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

type SEEvent struct {
	EventName  string
	EventType  string
	EventId    string
	ActorId    string
	ProspectId string
}

const (
	NewAccountConnectionScreenLoadedEventName = "SENewAccountConnectionScreenLoaded"
	AccountSelectionScreenLoadedEventName     = "SEAccountSelectionScreenLoaded"
	SalaryVerifiedEventName                   = "SESalaryVerified"
	SalaryVerificationFailedEventName         = "SESalaryVerificationFailed"
)

func NewSalaryEstimationEvent(actorId, eventName string) *SEEvent {
	return &SEEvent{
		EventName:  eventName,
		EventType:  events.EventTrack,
		EventId:    uuid.New().String(),
		ActorId:    actorId,
		ProspectId: "",
	}
}

func (c *SEEvent) GetEventType() string {
	return c.EventType
}

func (c *SEEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *SEEvent) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *SEEvent) GetEventId() string {
	return c.EventId
}

func (c *SEEvent) GetUserId() string {
	return c.ActorId
}

func (c *SEEvent) GetProspectId() string {
	return c.ProspectId
}

func (c *SEEvent) GetEventName() string {
	return c.EventName
}
