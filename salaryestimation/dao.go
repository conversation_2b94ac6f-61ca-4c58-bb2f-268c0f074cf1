//go:generate mockgen -source=dao.go -destination=./mocks/mock_dao.go -package=mocks
//go:generate dao_metrics_gen .

package salaryestimation

import (
	"context"

	"github.com/epifi/gamma/api/salaryestimation"
)

// SalaryEstimationAttemptDao defines all the DAO operations for salary estimation attempts
type SalaryEstimationAttemptDao interface {
	// Create creates a new salary estimation attempt record
	Create(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt) (*salaryestimation.SalaryEstimationAttempt, error)

	// GetByClientReqID retrieves a salary estimation attempt by client request ID
	GetByClientReqID(ctx context.Context, clientReqID string) (*salaryestimation.SalaryEstimationAttempt, error)

	// GetByID retrieves a salary estimation attempt by ID
	GetByID(ctx context.Context, id string) (*salaryestimation.SalaryEstimationAttempt, error)

	// Update updates an existing salary estimation attempt record and returns the updated record
	Update(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt, fieldMasks []salaryestimation.SalaryEstimationAttemptFieldMask) (*salaryestimation.SalaryEstimationAttempt, error)

	// GetByActorId retrieves a salary estimation attempts filtered by actor id, sorted by created at desc
	GetByActorId(ctx context.Context, actorId string) ([]*salaryestimation.SalaryEstimationAttempt, error)
}
