package salaryestimation

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/connected_account/data_analytics"
	"github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	salaryestimationpb "github.com/epifi/gamma/api/salaryestimation"
	salaryestimation3 "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/salaryestimation"
	salaryEstimationTypes "github.com/epifi/gamma/api/typesv2/salaryestimation"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/salaryestimation/events"

	"github.com/pkg/errors"
	"go.uber.org/zap"
)

func (s *Service) getNextActionForAnalysisInput(ctx context.Context, req *salaryestimationpb.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	prevAnalysisParams := req.GetSourceFlowParams().GetAaDataFlowParams().GetAnalysisParams()
	if prevAnalysisParams.GetAttemptId() == "" {
		return nil, errors.New("no attempt id specified")
	}
	latestAnalysisStatusRes, err := s.caDataAnalyticsClient.GetAnalysisStatus(ctx, &data_analytics.GetAnalysisStatusRequest{
		ActorId:     req.GetActorId(),
		ClientReqId: prevAnalysisParams.GetAttemptId(),
	})
	if err = epifigrpc.RPCError(latestAnalysisStatusRes, err); err != nil {
		return nil, errors.Wrap(err, "error getting latest analysis status")
	}
	switch prevAnalysisParams.GetStatus() {
	default:
		logger.Info(ctx, fmt.Sprintf("unhandled prev analysis status: %s", prevAnalysisParams.GetStatus().String()))
		latestAnalysisStatus := getSalaryEstAnalysisStatusFromCaAnalysisStatus(latestAnalysisStatusRes.GetAnalysisStatus())
		return s.getLatestAnalysisStatusScreen(ctx, req, prevAnalysisParams.GetAttemptId(), latestAnalysisStatus)
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_IN_PROGRESS:
		latestAnalysisStatus := getSalaryEstAnalysisStatusFromCaAnalysisStatus(latestAnalysisStatusRes.GetAnalysisStatus())
		return s.getLatestAnalysisStatusScreen(ctx, req, prevAnalysisParams.GetAttemptId(), latestAnalysisStatus)
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_SUCCESSFUL:
		nextAction, err := s.getClientNextActionForSuccessfulAnalysis(ctx, req)
		if err != nil {
			return nil, errors.Wrap(err, "error getting next action for successful analysis")
		}
		return nextAction, nil
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED:
		nextAction, err := s.getClientNextActionForFailedAnalysis(ctx, req)
		if err != nil {
			return nil, errors.Wrap(err, "error getting next action for failed analysis")
		}
		return nextAction, nil
	}
}

func (s *Service) getLatestAnalysisStatusScreen(
	ctx context.Context,
	req *salaryestimationpb.ComputeSalaryRequest,
	analysisAttemptId string,
	latestAnalysisStatus salaryEstimationTypes.AnalysisStatus,
) (*deeplink.Deeplink, error) {
	commonScreenOptions := &salaryestimation3.IncomeAnalysisStatusScreen{
		BackgroundColor: widget.GetBlockBackgroundColour(colors.ColorSnow),
		VtsHeaderComponent: &widget.VisualElementTitleSubtitleElement{
			BackgroundColor: colors.ColorSnow,
		},
		Client:      req.GetClient().String(),
		ClientReqId: req.GetClientReqId(),
		Source:      req.GetSource().String(),
		AnalysisParams: &salaryEstimationTypes.AnalysisParams{
			AttemptId: analysisAttemptId,
			Status:    latestAnalysisStatus,
		},
	}
	switch latestAnalysisStatus {
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_IN_PROGRESS:
		commonScreenOptions.VtsHeaderComponent.VisualElement = common.
			GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/blue-hourglass.png").
			WithProperties(&common.VisualElementProperties{Width: 200, Height: 200})
		commonScreenOptions.VtsHeaderComponent.TitleText = common.GetPlainStringText("Reviewing your account to verify income").
			WithFontStyle(common.FontStyle_HEADLINE_L).WithFontColor(colors.ColorNight)
		commonScreenOptions.VtsHeaderComponent.SubtitleText = common.GetPlainStringText("This usually takes a few minutes").
			WithFontStyle(common.FontStyle_BODY_S).WithFontColor(colors.ColorLead)
		commonScreenOptions.AdditionalInformationBlock = &salaryestimation3.AdditionInformationBlock{
			BgColor: widget.GetBlockBackgroundColour("#FBF3E6"),
			LeftIcon: common.
				GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/shield-grey-outline.png").
				WithProperties(&common.VisualElementProperties{Width: 32, Height: 32}),
			Title: ui.NewITC().WithTexts(common.GetPlainStringText("In progress: Income review").
				WithFontStyle(common.FontStyle_SUBTITLE_2).WithFontColor(colors.ColorNight)),
			Subtitle: ui.NewITC().WithTexts(common.GetPlainStringText("Our best teams are on this. We'll notify you once its done").
				WithFontStyle(common.FontStyle_BODY_XS).WithFontColor(colors.ColorOnDarkMediumEmphasis)),
		}
		commonScreenOptions.AnalysisParams.RetryDelaySeconds = analysisStatusRetryDelaySeconds
		nextAction, err := s.getClientNextActionForInProgressAnalysis(ctx, req)
		if err != nil {
			return nil, errors.Wrap(err, "error getting next action for in progress analysis")
		}
		commonScreenOptions.Cta = &deeplink.Cta{
			Type:     deeplink.Cta_DONE,
			Text:     "Ok, got it!",
			Deeplink: nextAction,
		}
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_SUCCESSFUL:
		commonScreenOptions.VtsHeaderComponent.VisualElement = common.
			GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/white-ticker-green-bg.png").
			WithProperties(&common.VisualElementProperties{Width: 180, Height: 180})
		commonScreenOptions.VtsHeaderComponent.TitleText = common.GetPlainStringText("Verified successfully").
			WithFontStyle(common.FontStyle_HEADLINE_XL).WithFontColor(colors.ColorOnLightHighEmphasis)
		nextAction, err := s.getClientNextActionForSuccessfulAnalysis(ctx, req)
		if err != nil {
			return nil, errors.Wrap(err, "error getting next action for in progress analysis")
		}
		commonScreenOptions.Cta = &deeplink.Cta{
			Type:     deeplink.Cta_DONE,
			Text:     "Proceed",
			Deeplink: nextAction,
		}
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED:
		commonScreenOptions.VtsHeaderComponent.VisualElement = common.
			GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/white-exclamation-red-bg.png").
			WithProperties(&common.VisualElementProperties{Width: 200, Height: 200})
		commonScreenOptions.VtsHeaderComponent.TitleText = common.GetPlainStringText("Could not verify").
			WithFontStyle(common.FontStyle_HEADLINE_XL).WithFontColor(colors.ColorOnLightHighEmphasis)
		nextAction, err := s.getClientNextActionForFailedAnalysis(ctx, req)
		if err != nil {
			return nil, errors.Wrap(err, "error getting next action for in progress analysis")
		}
		commonScreenOptions.Cta = &deeplink.Cta{
			Type:     deeplink.Cta_DONE,
			Text:     "Okay",
			Deeplink: nextAction,
		}
	default:
		return nil, errors.Errorf("unknown analysis status: %s", latestAnalysisStatus)
	}

	switch latestAnalysisStatus {
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_SUCCESSFUL:
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), events.NewSalaryEstimationEvent(req.GetActorId(), events.SalaryVerifiedEventName))
		})
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED:
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), events.NewSalaryEstimationEvent(req.GetActorId(), events.SalaryVerificationFailedEventName))
		})
	default:
		// not sending in progress events to prevent event flooding
	}
	return &deeplink.Deeplink{
		Screen:          deeplink.Screen_INCOME_ANALYSIS_STATUS_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(commonScreenOptions),
	}, nil
}

func (s *Service) getLoansRedirectDL(ctx context.Context, req *salaryestimationpb.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	res, err := s.preApprovedLoanClient.GetRedirectDL(ctx, &palPb.GetRedirectDLRequest{
		ActorId:         req.GetActorId(),
		ClientRequestId: req.GetClientReqId(),
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		if res.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "client req id not found in loans", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
			return &deeplink.Deeplink{
				Screen: deeplink.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
			}, nil
		}
		return nil, errors.Wrap(err, "error getting loans redirect dl")
	}
	return res.GetRedirectDeeplink(), nil
}

func (s *Service) getClientNextActionForInProgressAnalysis(_ context.Context, req *salaryestimationpb.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	switch req.GetClient() {
	case salaryestimationpb.Client_CLIENT_LOANS:
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
		}, nil
	default:
		return nil, errors.Errorf("unknown client: %s", req.GetClient())
	}
}

func (s *Service) getClientNextActionForSuccessfulAnalysis(ctx context.Context, req *salaryestimationpb.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	switch req.GetClient() {
	case salaryestimationpb.Client_CLIENT_LOANS:
		return s.getLoansRedirectDL(ctx, req)
	default:
		return nil, errors.Errorf("unknown client: %s", req.GetClient())
	}
}

func (s *Service) getClientNextActionForFailedAnalysis(_ context.Context, req *salaryestimationpb.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	switch req.GetClient() {
	case salaryestimationpb.Client_CLIENT_LOANS:
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
		}, nil
	default:
		return nil, errors.Errorf("unknown client: %s", req.GetClient())
	}
}
