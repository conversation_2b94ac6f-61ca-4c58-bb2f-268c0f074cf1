package developer

import (
	"context"
	"errors"

	"github.com/epifi/gamma/salaryestimation"

	"go.uber.org/zap"
	"gorm.io/gorm"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	salEstDevPb "github.com/epifi/gamma/api/salaryestimation/developer"
)

type SalaryEstimationDevEntity struct {
	fac        *DevFactory
	attemptDao salaryestimation.SalaryEstimationAttemptDao
}

func NewSalaryEstimationDevEntity(
	fac *DevFactory,
	attemptDao salaryestimation.SalaryEstimationAttemptDao,
) *SalaryEstimationDevEntity {
	return &SalaryEstimationDevEntity{
		fac:        fac,
		attemptDao: attemptDao,
	}
}

func (s *SalaryEstimationDevEntity) GetEntityList(_ context.Context, _ *cxDsPb.GetEntityListRequest) (*cxDsPb.GetEntityListResponse, error) {
	return &cxDsPb.GetEntityListResponse{
		Status: rpcPb.StatusOk(),
		EntityList: []string{
			salEstDevPb.SalaryEstimationEntity_SALARY_ESTIMATION_ATTEMPTS.String(),
		},
	}, nil
}

func (s *SalaryEstimationDevEntity) GetParameterList(ctx context.Context, req *cxDsPb.GetParameterListRequest) (*cxDsPb.GetParameterListResponse, error) {
	ent, ok := salEstDevPb.SalaryEstimationEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetParameterListResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available in salary estimation dev service"),
		}, nil
	}
	paramFetcher, err := s.fac.getParameterListImpl(salEstDevPb.SalaryEstimationEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available")
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	paramList, err := paramFetcher.FetchParamList(ctx, salEstDevPb.SalaryEstimationEntity(ent))
	if err != nil {
		logger.Error(ctx, "unable to fetch parameter list")
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxDsPb.GetParameterListResponse{
		Status:        rpcPb.StatusOk(),
		ParameterList: paramList,
	}, nil
}

func (s *SalaryEstimationDevEntity) GetData(ctx context.Context, req *cxDsPb.GetDataRequest) (*cxDsPb.GetDataResponse, error) {
	ent, ok := salEstDevPb.SalaryEstimationEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetDataResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available in salary estimation dev service"),
		}, nil
	}
	dataFetcher, err := s.fac.getDataImpl(salEstDevPb.SalaryEstimationEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available")
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	jsonResp, err := dataFetcher.FetchData(ctx, salEstDevPb.SalaryEstimationEntity(ent), req.GetFilters())
	if err != nil {
		logger.Error(ctx, "unable to get data", zap.Error(err))
		if errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &cxDsPb.GetDataResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxDsPb.GetDataResponse{
		Status:       rpcPb.StatusOk(),
		JsonResponse: jsonResp,
	}, nil
}
