package developer

import (
	"fmt"

	salEstDevPb "github.com/epifi/gamma/api/salaryestimation/developer"
	"github.com/epifi/gamma/salaryestimation/developer/processor"
)

type DevFactory struct {
	SalaryEstimationAttemptProcessor *processor.SalaryEstimationAttemptProcessor
}

func NewDevFactory(salaryEstimationAttemptProcessor *processor.SalaryEstimationAttemptProcessor) *DevFactory {
	return &DevFactory{
		SalaryEstimationAttemptProcessor: salaryEstimationAttemptProcessor,
	}
}

func (d *DevFactory) getParameterListImpl(entity salEstDevPb.SalaryEstimationEntity) (IParameterFetcher, error) {
	switch entity {
	case salEstDevPb.SalaryEstimationEntity_SALARY_ESTIMATION_ATTEMPTS:
		return d.SalaryEstimationAttemptProcessor, nil
	default:
		return nil, fmt.Errorf("no valid implementation found")
	}
}

func (d *DevFactory) getDataImpl(entity salEstDevPb.SalaryEstimationEntity) (IDataFetcher, error) {
	switch entity {
	case salEstDevPb.SalaryEstimationEntity_SALARY_ESTIMATION_ATTEMPTS:
		return d.SalaryEstimationAttemptProcessor, nil
	default:
		return nil, fmt.Errorf("no valid implementation found")
	}
}
