package developer

import (
	"context"

	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	salEstDevPb "github.com/epifi/gamma/api/salaryestimation/developer"
)

type IParameterFetcher interface {
	FetchParamList(ctx context.Context, entity salEstDevPb.SalaryEstimationEntity) ([]*cxDsPb.ParameterMeta, error)
}

type IDataFetcher interface {
	FetchData(ctx context.Context, entity salEstDevPb.SalaryEstimationEntity, filters []*cxDsPb.Filter) (string, error)
}
