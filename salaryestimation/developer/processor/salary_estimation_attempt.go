package processor

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/epifi/gamma/salaryestimation"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	salEstDevPb "github.com/epifi/gamma/api/salaryestimation/developer"
)

type SalaryEstimationAttemptProcessor struct {
	attemptDao salaryestimation.SalaryEstimationAttemptDao
}

func NewSalaryEstimationAttemptProcessor(attemptDao salaryestimation.SalaryEstimationAttemptDao) *SalaryEstimationAttemptProcessor {
	return &SalaryEstimationAttemptProcessor{attemptDao: attemptDao}
}

func (p *SalaryEstimationAttemptProcessor) FetchParamList(_ context.Context, _ salEstDevPb.SalaryEstimationEntity) ([]*db_state.ParameterMeta, error) {
	return []*db_state.ParameterMeta{
		{
			Name:            ActorId,
			Label:           ActorIdLabel,
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            ClientReqId,
			Label:           ClientReqIdLabel,
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}, nil
}

func (p *SalaryEstimationAttemptProcessor) FetchData(ctx context.Context, _ salEstDevPb.SalaryEstimationEntity, filters []*db_state.Filter) (string, error) {
	var actorId, clientReqId string
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case ActorId:
			actorId = filter.GetStringValue()
		case ClientReqId:
			clientReqId = filter.GetStringValue()
		}
	}

	if clientReqId != "" {
		attempt, err := p.attemptDao.GetByClientReqID(ctx, clientReqId)
		if err != nil {
			return fmt.Sprintf("error fetching by client_req_id: %v", err), nil
		}
		b, err := json.Marshal(attempt)
		if err != nil {
			return err.Error(), nil
		}
		return string(b), nil
	}
	if actorId != "" {
		attempts, err := p.attemptDao.GetByActorId(ctx, actorId)
		if err != nil {
			return fmt.Sprintf("error fetching by client_req_id: %v", err), nil
		}
		b, err := json.Marshal(attempts)
		if err != nil {
			return err.Error(), nil
		}
		return string(b), nil
	}
	return "No valid filter provided", nil
}
