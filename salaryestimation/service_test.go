package salaryestimation

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	eventsMocks "github.com/epifi/be-common/pkg/events/mocks"
	"github.com/epifi/be-common/pkg/logger"
	caPb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/connected_account/analytics"
	caAnalyticsMocks "github.com/epifi/gamma/api/connected_account/analytics/mocks"
	"github.com/epifi/gamma/api/connected_account/data_analytics"
	caDataAnalyticsMocks "github.com/epifi/gamma/api/connected_account/data_analytics/mocks"
	"github.com/epifi/gamma/api/connected_account/external"
	caMocks "github.com/epifi/gamma/api/connected_account/mocks"
	"github.com/epifi/gamma/api/consent"
	consentMocks "github.com/epifi/gamma/api/consent/mocks"
	"github.com/epifi/gamma/api/datasharing"
	datasharingMocks "github.com/epifi/gamma/api/datasharing/mocks"
	"github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palMocks "github.com/epifi/gamma/api/preapprovedloan/mocks"
	"github.com/epifi/gamma/api/salaryestimation"
	datasharingTypes "github.com/epifi/gamma/api/typesv2/datasharing"
	salaryestimationTypes "github.com/epifi/gamma/api/typesv2/salaryestimation"
	genConf "github.com/epifi/gamma/salaryestimation/config/genconf"
	"github.com/epifi/gamma/salaryestimation/mocks"
)

func TestService_ComputeSalary(t *testing.T) {
	t.Parallel()

	logger.Init(cfg.TestEnv)
	type fields struct {
		conf                       *genConf.Config
		caAnalyticsClient          *caAnalyticsMocks.MockAnalyticsClient
		caDataAnalyticsClient      *caDataAnalyticsMocks.MockDataAnalyticsClient
		consentClient              *consentMocks.MockConsentClient
		dataSharingClient          *datasharingMocks.MockDataSharingClient
		connectedAccountClient     *caMocks.MockConnectedAccountClient
		preApprovedLoanClient      *palMocks.MockPreApprovedLoanClient
		eventBroker                *eventsMocks.MockBroker
		salaryEstimationAttemptDao *mocks.MockSalaryEstimationAttemptDao
	}
	type args struct {
		ctx context.Context
		req *salaryestimation.ComputeSalaryRequest
	}

	pendingAttempt := &salaryestimation.SalaryEstimationAttempt{
		ClientReqId: "client-req-1",
		ActorId:     "actor-1",
		Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
		Step:        salaryestimation.AttemptStep_ATTEMPT_STEP_ACCOUNT_CONNECTION,
		ExpiryAt:    timestamppb.New(time.Now().Add(1 * time.Hour)),
		ClientParams: &salaryestimation.ClientParams{
			Client: salaryestimation.Client_CLIENT_LOANS,
		},
		Source: salaryestimation.Source_SOURCE_AA,
	}
	accountSelectionAttempt := &salaryestimation.SalaryEstimationAttempt{
		ClientReqId: "client-req-1",
		ActorId:     "actor-1",
		Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
		Step:        salaryestimation.AttemptStep_ATTEMPT_STEP_ACCOUNT_SELECTION,
		ExpiryAt:    timestamppb.New(time.Now().Add(1 * time.Hour)),
		ClientParams: &salaryestimation.ClientParams{
			Client: salaryestimation.Client_CLIENT_LOANS,
		},
		Source: salaryestimation.Source_SOURCE_AA,
	}
	pendingAnalysisAttempt := &salaryestimation.SalaryEstimationAttempt{
		ClientReqId: "client-req-1",
		ActorId:     "actor-1",
		Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
		Step:        salaryestimation.AttemptStep_ATTEMPT_STEP_ANALYSIS,
		ExpiryAt:    timestamppb.New(time.Now().Add(1 * time.Hour)),
		ClientParams: &salaryestimation.ClientParams{
			Client: salaryestimation.Client_CLIENT_LOANS,
		},
		Source: salaryestimation.Source_SOURCE_AA,
	}
	successAnalysisAttempt := &salaryestimation.SalaryEstimationAttempt{
		ClientReqId: "client-req-1",
		ActorId:     "actor-1",
		Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_SUCCESSFUL,
		Step:        salaryestimation.AttemptStep_ATTEMPT_STEP_ANALYSIS,
		ExpiryAt:    timestamppb.New(time.Now().Add(1 * time.Hour)),
		ClientParams: &salaryestimation.ClientParams{
			Client: salaryestimation.Client_CLIENT_LOANS,
		},
		Source: salaryestimation.Source_SOURCE_AA,
	}
	clientReqId := "client-req-1"
	actorId := "actor-1"
	mockDeeplink := &deeplink.Deeplink{Screen: deeplink.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN}

	expiredPendingAttempt := &salaryestimation.SalaryEstimationAttempt{
		Id:          "attempt-2",
		ClientReqId: clientReqId,
		ActorId:     actorId,
		Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
		ExpiryAt:    timestamppb.New(time.Now().Add(-1 * time.Hour)),
		ClientParams: &salaryestimation.ClientParams{
			Client: salaryestimation.Client_CLIENT_LOANS,
		},
	}
	failedAttempt := &salaryestimation.SalaryEstimationAttempt{
		Id:          "attempt-3",
		ClientReqId: clientReqId,
		ActorId:     actorId,
		Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_FAILED,
		ClientParams: &salaryestimation.ClientParams{
			Client: salaryestimation.Client_CLIENT_LOANS,
		},
	}
	successfulAttempt := &salaryestimation.SalaryEstimationAttempt{
		Id:          "attempt-4",
		ClientReqId: clientReqId,
		ActorId:     actorId,
		Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_SUCCESSFUL,
		ClientParams: &salaryestimation.ClientParams{
			Client: salaryestimation.Client_CLIENT_LOANS,
		},
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func(f *fields)
		want       *salaryestimation.ComputeSalaryResponse
		wantErr    bool
	}{
		{
			name: "happy path: first call from client, holding screen",
			args: args{
				ctx: context.Background(),
				req: &salaryestimation.ComputeSalaryRequest{
					ClientReqId:          "client-req-1",
					ActorId:              "actor-1",
					Client:               salaryestimation.Client_CLIENT_LOANS,
					Source:               salaryestimationTypes.Source_SOURCE_AA,
					RequireHoldingScreen: true,
				},
			},
			setupMocks: func(f *fields) {
				f.salaryEstimationAttemptDao.EXPECT().GetByClientReqID(gomock.Any(), "client-req-1").Return(nil, epifierrors.ErrRecordNotFound)
				f.salaryEstimationAttemptDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(pendingAttempt, nil)
			},
			want: &salaryestimation.ComputeSalaryResponse{
				Status:        rpc.StatusOk(),
				NextAction:    &deeplink.Deeplink{Screen: deeplink.Screen_VERIFY_INCOME_HOME_SCREEN},
				AttemptStatus: salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
			},
		},
		{
			name: "happy path: first call from FE, no holding screen, no consents present, no account connected yet",
			args: args{
				ctx: epificontext.CtxWithAppPlatform(context.Background(), common.Platform_ANDROID),
				req: &salaryestimation.ComputeSalaryRequest{
					ClientReqId:          "client-req-1",
					ActorId:              "actor-1",
					Client:               salaryestimation.Client_CLIENT_LOANS,
					Source:               salaryestimationTypes.Source_SOURCE_AA,
					RequireHoldingScreen: false,
				},
			},
			setupMocks: func(f *fields) {
				f.salaryEstimationAttemptDao.EXPECT().GetByClientReqID(gomock.Any(), "client-req-1").Return(pendingAttempt, nil)
				f.consentClient.EXPECT().FetchConsent(gomock.Any(), &consent.FetchConsentRequest{
					ActorId:     "actor-1",
					ConsentType: consent.ConsentType_CONSENT_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP_V2,
					Owner:       common.Owner_OWNER_EPIFI_WEALTH,
				}).Return(&consent.FetchConsentResponse{Status: rpc.StatusRecordNotFound()}, nil)
				f.consentClient.EXPECT().FetchConsent(gomock.Any(), &consent.FetchConsentRequest{
					ActorId:     "actor-1",
					ConsentType: consent.ConsentType_CONSENT_TYPE_ONE_TIME_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP,
					Owner:       common.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.FetchConsentResponse{Status: rpc.StatusRecordNotFound()}, nil)
				f.connectedAccountClient.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(&caPb.GetAccountsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				f.connectedAccountClient.EXPECT().StartConnectionFlow(gomock.Any(), gomock.Any()).Return(&caPb.StartConnectionFlowResponse{
					Status:         rpc.StatusOk(),
					ConnectionFlow: &caPb.ConnectionFlow{Id: "ca-flow-id-1"},
				}, nil)

			},
			want: &salaryestimation.ComputeSalaryResponse{
				Status:        rpc.StatusOk(),
				NextAction:    &deeplink.Deeplink{Screen: deeplink.Screen_VERIFY_INCOME_HOME_SCREEN},
				AttemptStatus: salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
			},
		},
		{
			name: "happy path: first call from FE, no holding screen, one consent present, no account connected yet",
			args: args{
				ctx: epificontext.CtxWithAppPlatform(context.Background(), common.Platform_ANDROID),
				req: &salaryestimation.ComputeSalaryRequest{
					ClientReqId:          "client-req-1",
					ActorId:              "actor-1",
					Client:               salaryestimation.Client_CLIENT_LOANS,
					Source:               salaryestimationTypes.Source_SOURCE_AA,
					RequireHoldingScreen: false,
				},
			},
			setupMocks: func(f *fields) {
				f.salaryEstimationAttemptDao.EXPECT().GetByClientReqID(gomock.Any(), "client-req-1").Return(pendingAttempt, nil)
				f.consentClient.EXPECT().FetchConsent(gomock.Any(), &consent.FetchConsentRequest{
					ActorId:     "actor-1",
					ConsentType: consent.ConsentType_CONSENT_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP_V2,
					Owner:       common.Owner_OWNER_EPIFI_WEALTH,
				}).Return(&consent.FetchConsentResponse{Status: rpc.StatusOk(), ConsentId: "consent-1", ExpiresAt: timestamppb.New(time.Now().Add(time.Hour))}, nil)
				f.consentClient.EXPECT().FetchConsent(gomock.Any(), &consent.FetchConsentRequest{
					ActorId:     "actor-1",
					ConsentType: consent.ConsentType_CONSENT_TYPE_ONE_TIME_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP,
					Owner:       common.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.FetchConsentResponse{Status: rpc.StatusRecordNotFound()}, nil)
				f.connectedAccountClient.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(&caPb.GetAccountsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				f.connectedAccountClient.EXPECT().StartConnectionFlow(gomock.Any(), gomock.Any()).Return(&caPb.StartConnectionFlowResponse{
					Status:         rpc.StatusOk(),
					ConnectionFlow: &caPb.ConnectionFlow{Id: "ca-flow-id-1"},
				}, nil)
			},
			want: &salaryestimation.ComputeSalaryResponse{
				Status:        rpc.StatusOk(),
				NextAction:    &deeplink.Deeplink{Screen: deeplink.Screen_VERIFY_INCOME_HOME_SCREEN},
				AttemptStatus: salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
			},
		},
		{
			name: "happy path: one consent present, account connection in progress",
			args: args{
				ctx: epificontext.CtxWithAppPlatform(context.Background(), common.Platform_ANDROID),
				req: &salaryestimation.ComputeSalaryRequest{
					ClientReqId:          "client-req-1",
					ActorId:              "actor-1",
					Client:               salaryestimation.Client_CLIENT_LOANS,
					Source:               salaryestimationTypes.Source_SOURCE_AA,
					RequireHoldingScreen: false,
				},
			},
			setupMocks: func(f *fields) {
				f.salaryEstimationAttemptDao.EXPECT().GetByClientReqID(gomock.Any(), "client-req-1").Return(pendingAttempt, nil)
				f.consentClient.EXPECT().FetchConsent(gomock.Any(), &consent.FetchConsentRequest{
					ActorId:     "actor-1",
					ConsentType: consent.ConsentType_CONSENT_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP_V2,
					Owner:       common.Owner_OWNER_EPIFI_WEALTH,
				}).Return(&consent.FetchConsentResponse{Status: rpc.StatusOk(), ConsentId: "consent-1", ExpiresAt: timestamppb.New(time.Now().Add(time.Hour))}, nil)
				f.consentClient.EXPECT().FetchConsent(gomock.Any(), &consent.FetchConsentRequest{
					ActorId:     "actor-1",
					ConsentType: consent.ConsentType_CONSENT_TYPE_ONE_TIME_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP,
					Owner:       common.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.FetchConsentResponse{Status: rpc.StatusRecordNotFound()}, nil)
				f.connectedAccountClient.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(&caPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					AccountDetailsList: []*external.AccountDetails{
						{AccountId: "account-1"},
					},
				}, nil)
				f.connectedAccountClient.EXPECT().GetDataFetchAttempts(gomock.Any(), &caPb.GetDataFetchAttemptsRequest{
					PageContext: &rpc.PageContextRequest{PageSize: 500},
					Filter:      &caPb.GetDataFetchAttemptsRequest_AccountId{AccountId: "account-1"},
				}).Return(&caPb.GetDataFetchAttemptsResponse{
					Status: rpc.StatusOk(),
					DataFetchAttemptDetailsList: []*external.DataFetchAttemptDetails{
						{
							DataRangeFrom: timestamppb.New(time.Now().Add(-1 * time.Hour)),
							DataRangeTo:   timestamppb.Now(),
						},
					},
				}, nil)
			},
			want: &salaryestimation.ComputeSalaryResponse{
				Status:        rpc.StatusOk(),
				NextAction:    &deeplink.Deeplink{Screen: deeplink.Screen_INCOME_ANALYSIS_STATUS_SCREEN},
				AttemptStatus: salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
			},
		},
		{
			name: "happy path: one consent present, all accounts connected",
			args: args{
				ctx: epificontext.CtxWithAppPlatform(context.Background(), common.Platform_ANDROID),
				req: &salaryestimation.ComputeSalaryRequest{
					ClientReqId:          "client-req-1",
					ActorId:              "actor-1",
					Client:               salaryestimation.Client_CLIENT_LOANS,
					Source:               salaryestimationTypes.Source_SOURCE_AA,
					RequireHoldingScreen: false,
				},
			},
			setupMocks: func(f *fields) {
				f.salaryEstimationAttemptDao.EXPECT().GetByClientReqID(gomock.Any(), "client-req-1").Return(pendingAttempt, nil)
				f.consentClient.EXPECT().FetchConsent(gomock.Any(), &consent.FetchConsentRequest{
					ActorId:     "actor-1",
					ConsentType: consent.ConsentType_CONSENT_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP_V2,
					Owner:       common.Owner_OWNER_EPIFI_WEALTH,
				}).Return(&consent.FetchConsentResponse{Status: rpc.StatusOk(), ConsentId: "consent-1", ExpiresAt: timestamppb.New(time.Now().Add(time.Hour))}, nil)
				f.consentClient.EXPECT().FetchConsent(gomock.Any(), &consent.FetchConsentRequest{
					ActorId:     "actor-1",
					ConsentType: consent.ConsentType_CONSENT_TYPE_ONE_TIME_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP,
					Owner:       common.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.FetchConsentResponse{Status: rpc.StatusRecordNotFound()}, nil)
				f.connectedAccountClient.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(&caPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					AccountDetailsList: []*external.AccountDetails{
						{AccountId: "account-1"},
					},
				}, nil)
				f.connectedAccountClient.EXPECT().GetDataFetchAttempts(gomock.Any(), &caPb.GetDataFetchAttemptsRequest{
					PageContext: &rpc.PageContextRequest{PageSize: 500},
					Filter:      &caPb.GetDataFetchAttemptsRequest_AccountId{AccountId: "account-1"},
				}).Return(&caPb.GetDataFetchAttemptsResponse{
					Status: rpc.StatusOk(),
					DataFetchAttemptDetailsList: []*external.DataFetchAttemptDetails{
						{
							DataRangeFrom: timestamppb.New(time.Now().Add(-1 * 8 * 30 * 24 * time.Hour)),
							DataRangeTo:   timestamppb.Now(),
						},
					},
				}, nil)
				f.salaryEstimationAttemptDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(accountSelectionAttempt, nil)
				f.connectedAccountClient.EXPECT().StartConnectionFlow(gomock.Any(), gomock.Any()).Return(&caPb.StartConnectionFlowResponse{Status: rpc.StatusOk(), ConnectionFlow: &caPb.ConnectionFlow{Id: "ca-id-1"}}, nil)
			},
			want: &salaryestimation.ComputeSalaryResponse{
				Status:        rpc.StatusOk(),
				NextAction:    &deeplink.Deeplink{Screen: deeplink.Screen_SALARY_ACCOUNT_SELECTION_SCREEN},
				AttemptStatus: salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
			},
		},
		{
			name: "happy path: new attempt is created and processed",
			args: args{
				ctx: epificontext.CtxWithAppPlatform(context.Background(), common.Platform_ANDROID),
				req: &salaryestimation.ComputeSalaryRequest{ClientReqId: clientReqId, ActorId: actorId, Source: salaryestimationTypes.Source_SOURCE_AA},
			},
			setupMocks: func(f *fields) {
				f.salaryEstimationAttemptDao.EXPECT().GetByClientReqID(gomock.Any(), clientReqId).Return(nil, epifierrors.ErrRecordNotFound)
				f.salaryEstimationAttemptDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(pendingAttempt, nil)
				f.consentClient.EXPECT().FetchConsent(gomock.Any(), gomock.Any()).Return(&consent.FetchConsentResponse{Status: rpc.StatusRecordNotFound()}, nil).AnyTimes()
				f.connectedAccountClient.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(&caPb.GetAccountsResponse{Status: rpc.StatusRecordNotFound()}, nil)
				f.connectedAccountClient.EXPECT().StartConnectionFlow(gomock.Any(), gomock.Any()).Return(&caPb.StartConnectionFlowResponse{
					Status:         rpc.StatusOk(),
					ConnectionFlow: &caPb.ConnectionFlow{Id: "ca-flow-id-1"},
				}, nil)
			},
			want: &salaryestimation.ComputeSalaryResponse{
				Status:        rpc.StatusOk(),
				NextAction:    &deeplink.Deeplink{Screen: deeplink.Screen_VERIFY_INCOME_HOME_SCREEN},
				AttemptStatus: salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
			},
			wantErr: false,
		},
		{
			name: "error path: failed to get existing attempt",
			args: args{
				ctx: context.Background(),
				req: &salaryestimation.ComputeSalaryRequest{ClientReqId: clientReqId, ActorId: actorId},
			},
			setupMocks: func(f *fields) {
				f.salaryEstimationAttemptDao.EXPECT().GetByClientReqID(gomock.Any(), clientReqId).Return(nil, errors.New("db error"))
			},
			want: &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()},
		},
		{
			name: "error path: failed to create new attempt",
			args: args{
				ctx: context.Background(),
				req: &salaryestimation.ComputeSalaryRequest{ClientReqId: clientReqId, ActorId: actorId, Source: salaryestimationTypes.Source_SOURCE_AA},
			},
			setupMocks: func(f *fields) {
				f.salaryEstimationAttemptDao.EXPECT().GetByClientReqID(gomock.Any(), clientReqId).Return(nil, epifierrors.ErrRecordNotFound)
				f.salaryEstimationAttemptDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, errors.New("db error"))
			},
			want: &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()},
		},
		{
			name: "happy path: expired attempt found",
			args: args{
				ctx: context.Background(),
				req: &salaryestimation.ComputeSalaryRequest{
					ClientReqId:          clientReqId,
					ActorId:              actorId,
					Client:               salaryestimation.Client_CLIENT_LOANS,
					RequireHoldingScreen: true,
				},
			},
			setupMocks: func(f *fields) {
				f.salaryEstimationAttemptDao.EXPECT().GetByClientReqID(gomock.Any(), clientReqId).Return(expiredPendingAttempt, nil)
				f.salaryEstimationAttemptDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
					func(_ context.Context, atmpt *salaryestimation.SalaryEstimationAttempt, _ []salaryestimation.SalaryEstimationAttemptFieldMask) (*salaryestimation.SalaryEstimationAttempt, error) {
						require.Equal(t, salaryestimation.AttemptStatus_ATTEMPT_STATUS_EXPIRED, atmpt.Status)
						return atmpt, nil
					})
			},
			want: &salaryestimation.ComputeSalaryResponse{
				Status:        rpc.StatusOk(),
				NextAction:    &deeplink.Deeplink{Screen: deeplink.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN},
				AttemptStatus: salaryestimation.AttemptStatus_ATTEMPT_STATUS_EXPIRED,
			},
		},
		{
			name: "happy path: already failed attempt found",
			args: args{
				ctx: context.Background(),
				req: &salaryestimation.ComputeSalaryRequest{
					ClientReqId:          clientReqId,
					ActorId:              actorId,
					Client:               salaryestimation.Client_CLIENT_LOANS,
					RequireHoldingScreen: true,
				},
			},
			setupMocks: func(f *fields) {
				f.salaryEstimationAttemptDao.EXPECT().GetByClientReqID(gomock.Any(), clientReqId).Return(failedAttempt, nil)
			},
			want: &salaryestimation.ComputeSalaryResponse{
				Status:        rpc.StatusOk(),
				NextAction:    &deeplink.Deeplink{Screen: deeplink.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN},
				AttemptStatus: salaryestimation.AttemptStatus_ATTEMPT_STATUS_FAILED,
			},
		},
		{
			name: "happy path: already successful attempt found",
			args: args{
				ctx: context.Background(),
				req: &salaryestimation.ComputeSalaryRequest{
					ClientReqId:          clientReqId,
					ActorId:              actorId,
					Client:               salaryestimation.Client_CLIENT_LOANS,
					RequireHoldingScreen: true,
				},
			},
			setupMocks: func(f *fields) {
				f.salaryEstimationAttemptDao.EXPECT().GetByClientReqID(gomock.Any(), clientReqId).Return(successfulAttempt, nil)
				f.preApprovedLoanClient.EXPECT().GetRedirectDL(gomock.Any(), gomock.Any()).Return(&palPb.GetRedirectDLResponse{Status: rpc.StatusOk(), RedirectDeeplink: mockDeeplink}, nil)
			},
			want: &salaryestimation.ComputeSalaryResponse{
				Status:        rpc.StatusOk(),
				NextAction:    mockDeeplink,
				AttemptStatus: salaryestimation.AttemptStatus_ATTEMPT_STATUS_SUCCESSFUL,
			},
		},
		{
			name: "happy path: data share input type, initiates analysis and data sharing",
			args: args{
				ctx: context.Background(),
				req: &salaryestimation.ComputeSalaryRequest{
					ClientReqId: clientReqId,
					ActorId:     actorId,
					Source:      salaryestimationTypes.Source_SOURCE_AA,
					SourceFlowParams: &salaryestimationTypes.SourceFlowParams{
						Params: &salaryestimationTypes.SourceFlowParams_AaDataFlowParams{
							AaDataFlowParams: &salaryestimationTypes.AaDataFlowParams{
								InputType: salaryestimationTypes.AaDataFlowInputType_AA_DATA_FLOW_INPUT_TYPE_DATA_SHARE,
								Params: &salaryestimationTypes.AaDataFlowParams_DataShareParams{
									DataShareParams: &salaryestimationTypes.DataShareParams{},
								},
							},
						},
					},
				},
			},
			setupMocks: func(f *fields) {
				f.salaryEstimationAttemptDao.EXPECT().GetByClientReqID(gomock.Any(), clientReqId).Return(pendingAttempt, nil)
				f.caAnalyticsClient.EXPECT().InitiateAnalysis(gomock.Any(), gomock.Any()).Return(&analytics.InitiateAnalysisResponse{Status: rpc.StatusOk()}, nil)
				f.salaryEstimationAttemptDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(pendingAttempt, nil)
				f.consentClient.EXPECT().FetchConsent(gomock.Any(), gomock.Any()).Return(&consent.FetchConsentResponse{Status: rpc.StatusRecordNotFound()}, nil)
				f.consentClient.EXPECT().FetchConsent(gomock.Any(), gomock.Any()).Return(&consent.FetchConsentResponse{Status: rpc.StatusOk(), ConsentId: "one-time-consent", ExpiresAt: timestamppb.New(time.Now().Add(time.Hour))}, nil)
				f.dataSharingClient.EXPECT().InitiateDataSharing(gomock.Any(), gomock.Any()).Return(&datasharing.InitiateDataSharingResponse{Status: rpc.StatusOk(), NextActionDeeplink: mockDeeplink}, nil)
			},
			want: &salaryestimation.ComputeSalaryResponse{
				Status:        rpc.StatusOk(),
				NextAction:    mockDeeplink,
				AttemptStatus: salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
			},
		},
		{
			name: "happy path: data status input type, initiates analysis and returns in-progress screen",
			args: args{
				ctx: context.Background(),
				req: &salaryestimation.ComputeSalaryRequest{
					ClientReqId: clientReqId,
					ActorId:     actorId,
					Source:      salaryestimationTypes.Source_SOURCE_AA,
					SourceFlowParams: &salaryestimationTypes.SourceFlowParams{
						Params: &salaryestimationTypes.SourceFlowParams_AaDataFlowParams{
							AaDataFlowParams: &salaryestimationTypes.AaDataFlowParams{
								InputType: salaryestimationTypes.AaDataFlowInputType_AA_DATA_FLOW_INPUT_TYPE_DATA_STATUS,
								Params: &salaryestimationTypes.AaDataFlowParams_DataStatusParams{
									DataStatusParams: &salaryestimationTypes.DataStatusParams{
										DataSharingRecord: &datasharingTypes.DataSharingRecord{},
									},
								},
							},
						},
					},
				},
			},
			setupMocks: func(f *fields) {
				f.salaryEstimationAttemptDao.EXPECT().GetByClientReqID(gomock.Any(), clientReqId).Return(pendingAttempt, nil)
				f.caDataAnalyticsClient.EXPECT().InitiateAnalysis(gomock.Any(), gomock.Any()).Return(&data_analytics.InitiateAnalysisResponse{Status: rpc.StatusOk()}, nil)
				f.salaryEstimationAttemptDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(pendingAttempt, nil)
				f.caDataAnalyticsClient.EXPECT().GetAnalysisStatus(gomock.Any(), gomock.Any()).Return(&data_analytics.GetAnalysisStatusResponse{Status: rpc.StatusOk(), AnalysisStatus: data_analytics.AnalysisStatus(salaryestimationTypes.AnalysisStatus_ANALYSIS_STATUS_IN_PROGRESS)}, nil)
			},
			want: &salaryestimation.ComputeSalaryResponse{
				Status:        rpc.StatusOk(),
				NextAction:    &deeplink.Deeplink{Screen: deeplink.Screen_INCOME_ANALYSIS_STATUS_SCREEN},
				AttemptStatus: salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
			},
		},
		{
			name: "happy path: analysis input type, analysis is successful",
			args: args{
				ctx: context.Background(),
				req: &salaryestimation.ComputeSalaryRequest{
					ClientReqId: clientReqId,
					ActorId:     actorId,
					Source:      salaryestimationTypes.Source_SOURCE_AA,
					SourceFlowParams: &salaryestimationTypes.SourceFlowParams{
						Params: &salaryestimationTypes.SourceFlowParams_AaDataFlowParams{
							AaDataFlowParams: &salaryestimationTypes.AaDataFlowParams{
								InputType: salaryestimationTypes.AaDataFlowInputType_AA_DATA_FLOW_INPUT_TYPE_ANALYSIS,
							},
						},
					},
				},
			},
			setupMocks: func(f *fields) {
				f.salaryEstimationAttemptDao.EXPECT().GetByClientReqID(gomock.Any(), clientReqId).Return(pendingAnalysisAttempt, nil)
				f.caDataAnalyticsClient.EXPECT().GetAnalysisStatus(gomock.Any(), gomock.Any()).Return(&data_analytics.GetAnalysisStatusResponse{Status: rpc.StatusOk(), AnalysisStatus: data_analytics.AnalysisStatus_ANALYSIS_STATUS_SUCCESSFUL}, nil)
				f.salaryEstimationAttemptDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(successAnalysisAttempt, nil)
				f.preApprovedLoanClient.EXPECT().GetRedirectDL(gomock.Any(), gomock.Any()).Return(&palPb.GetRedirectDLResponse{Status: rpc.StatusOk(), RedirectDeeplink: mockDeeplink}, nil)
			},
			want: &salaryestimation.ComputeSalaryResponse{
				Status:        rpc.StatusOk(),
				NextAction:    &deeplink.Deeplink{Screen: deeplink.Screen_INCOME_ANALYSIS_STATUS_SCREEN},
				AttemptStatus: salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
			},
		},
		{
			name: "error path: unhandled aa data flow input type",
			args: args{
				ctx: context.Background(),
				req: &salaryestimation.ComputeSalaryRequest{
					ClientReqId: clientReqId,
					ActorId:     actorId,
					Source:      salaryestimationTypes.Source_SOURCE_AA,
					SourceFlowParams: &salaryestimationTypes.SourceFlowParams{
						Params: &salaryestimationTypes.SourceFlowParams_AaDataFlowParams{
							AaDataFlowParams: &salaryestimationTypes.AaDataFlowParams{
								InputType: salaryestimationTypes.AaDataFlowInputType_AA_DATA_FLOW_INPUT_TYPE_UNSPECIFIED,
							},
						},
					},
				},
			},
			setupMocks: func(f *fields) {
				f.salaryEstimationAttemptDao.EXPECT().GetByClientReqID(gomock.Any(), clientReqId).Return(pendingAttempt, nil)
			},
			want: &salaryestimation.ComputeSalaryResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "stateful flag off: invalid source returns invalid argument",
			args: args{
				ctx: context.Background(),
				req: &salaryestimation.ComputeSalaryRequest{
					Source: salaryestimationTypes.Source_SOURCE_UNSPECIFIED,
				},
			},
			setupMocks: func(f *fields) {
				_ = f.conf.Flags().SetEnableStatefulSalaryEstimation(false, false, nil)
			},
			want: &salaryestimation.ComputeSalaryResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			f := &fields{
				conf: func() *genConf.Config {
					c, _ := genConf.NewConfig()
					_ = c.Flags().SetEnableStatefulSalaryEstimation(true, false, nil)
					return c
				}(),
				caAnalyticsClient:          caAnalyticsMocks.NewMockAnalyticsClient(ctrl),
				caDataAnalyticsClient:      caDataAnalyticsMocks.NewMockDataAnalyticsClient(ctrl),
				consentClient:              consentMocks.NewMockConsentClient(ctrl),
				dataSharingClient:          datasharingMocks.NewMockDataSharingClient(ctrl),
				connectedAccountClient:     caMocks.NewMockConnectedAccountClient(ctrl),
				preApprovedLoanClient:      palMocks.NewMockPreApprovedLoanClient(ctrl),
				eventBroker:                eventsMocks.NewMockBroker(ctrl),
				salaryEstimationAttemptDao: mocks.NewMockSalaryEstimationAttemptDao(ctrl),
			}

			if tt.setupMocks != nil {
				f.eventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
				tt.setupMocks(f)
			}

			s := NewService(
				f.conf,
				f.caAnalyticsClient,
				f.caDataAnalyticsClient,
				f.consentClient,
				f.dataSharingClient,
				f.connectedAccountClient,
				f.preApprovedLoanClient,
				f.eventBroker,
				f.salaryEstimationAttemptDao,
			)

			got, err := s.ComputeSalary(tt.args.ctx, tt.args.req)

			if (err != nil) != tt.wantErr {
				t.Errorf("Service.ComputeSalary() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			require.Equal(t, tt.want.Status, got.Status, "Status mismatch")
			if tt.want.AttemptStatus != salaryestimation.AttemptStatus_ATTEMPT_STATUS_UNSPECIFIED {
				require.Equal(t, tt.want.AttemptStatus, got.AttemptStatus, "AttemptStatus mismatch")
			}
			if tt.want.NextAction != nil {
				require.NotNil(t, got.NextAction, "NextAction should not be nil")
				require.Equal(t, tt.want.NextAction.Screen, got.NextAction.Screen, "NextAction.Screen mismatch")
			} else {
				require.Nil(t, got.NextAction, "NextAction should be nil")
			}
		})
	}
}
