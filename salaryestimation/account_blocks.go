package salaryestimation

import (
	"fmt"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	beCaExtPb "github.com/epifi/gamma/api/connected_account/external"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	salaryEstimationScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/salaryestimation"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/pkg/connectedaccount"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

func getAccountBlocks(accounts []*beCaExtPb.AccountDetails) ([]*salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions_BankAccountBlock, error) {
	if len(accounts) == 0 {
		return nil, errors.New("no accounts found")
	}
	if len(accounts) <= 2 {
		var blocks []*salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions_BankAccountBlock
		for _, account := range accounts {
			blocks = append(blocks, getIndividualAccountBlock(account))
		}
		return blocks, nil
	}
	return getGroupedBlocks(accounts), nil
}

func getGroupedBlocks(accounts []*beCaExtPb.AccountDetails) []*salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions_BankAccountBlock {
	var blocks []*salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions_BankAccountBlock
	firstBlock := getIndividualAccountBlock(accounts[0])
	blocks = append(blocks, firstBlock)
	var optionItems []*ui.OptionSelectionItem
	for i, account := range accounts {
		if i == 0 {
			continue
		}
		accountOptionItem := &ui.OptionSelectionItem{
			Identifier: account.GetAccountId(),
			OptionValue: ui.NewITC().
				WithLeftVisualElement(common.GetVisualElementImageFromUrl(account.GetFipMeta().GetLogoUrl()).
					WithProperties(&common.VisualElementProperties{Width: 32, Height: 32})).
				WithLeftImagePadding(12).
				WithTexts(common.GetPlainStringText(connectedaccount.GetAccountDisplayName(account)).
					WithFontStyle(common.FontStyle_SUBTITLE_M).WithFontColor(colors.ColorNight)),
		}
		optionItems = append(optionItems, accountOptionItem)
	}
	groupedBlockSubtitle := fmt.Sprintf("+ %d accounts", len(accounts)-1)
	groupedBlock := &salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions_BankAccountBlock{
		AccountId:       "",
		BackgroundColor: widget.GetBlockBackgroundColour(colors.ColorSnow),
		// TODO(Brijesh): Get account details and fill the below fields
		Logo: common.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/rupee-bank-building.png").
			WithProperties(&common.VisualElementProperties{Width: 36, Height: 36}),
		Title: nil,
		Subtitle: ui.NewITC().
			WithTexts(common.GetPlainStringText(groupedBlockSubtitle).
				WithFontStyle(common.FontStyle_SUBTITLE_M).WithFontColor(colors.ColorNight)),
		BorderColor:  widget.GetBlockBackgroundColour("#EFF2F6"),
		BorderRadius: 24,
		RightIcon: ui.NewITC().
			WithLeftVisualElement(
				common.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/chevron-down.png").
					WithProperties(&common.VisualElementProperties{Width: 28, Height: 28}),
			).WithDeeplink(&deeplink.Deeplink{
			Screen: deeplink.Screen_LOANS_OPTION_SELECTION_BOTTOM_SHEET,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&preapprovedloans.LoansOptionSelectionScreenOption{
				BgColor: "#E6E9ED",
				Title: common.GetPlainStringText("Your connected accounts").
					WithFontColor(colors.ColorNight).WithFontStyle(common.FontStyle_SUBTITLE_2),
				OptionView:         &ui.OptionSelectionView{Items: optionItems},
				PrimaryCta:         &deeplink.Cta{Type: deeplink.Cta_CONTINUE, DisplayTheme: deeplink.Cta_PRIMARY, Text: "Confirm"},
				NoSelectionAllowed: true,
			}),
		}),
	}
	blocks = append(blocks, groupedBlock)
	return blocks
}

func getIndividualAccountBlock(account *beCaExtPb.AccountDetails) *salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions_BankAccountBlock {
	return &salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions_BankAccountBlock{
		AccountId:       account.GetAccountId(),
		BackgroundColor: widget.GetBlockBackgroundColour(colors.ColorSnow),
		Logo: common.GetVisualElementImageFromUrl(account.GetFipMeta().GetLogoUrl()).
			WithProperties(&common.VisualElementProperties{Width: 36, Height: 36}),
		Title: nil,
		Subtitle: ui.NewITC().
			WithTexts(common.GetPlainStringText(connectedaccount.GetAccountDisplayName(account)).
				WithFontStyle(common.FontStyle_SUBTITLE_M).WithFontColor(colors.ColorNight)),
		BorderColor:  widget.GetBlockBackgroundColour("#EFF2F6"),
		BorderRadius: 24,
	}
}
