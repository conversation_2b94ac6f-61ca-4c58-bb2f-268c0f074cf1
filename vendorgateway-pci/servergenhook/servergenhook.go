package servergenhook

import (
	"go.uber.org/zap"
	"google.golang.org/grpc"

	cardResponseMapping "github.com/epifi/gamma/api/vendors/responsemapping/card"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	vgpciconf "github.com/epifi/gamma/vendorgateway-pci/config"
)

func BeforeVendorgatewayPciServerStartHook(s *grpc.Server) (func(), error) {
	cleanupFn := func() {}

	vgPciConf, err := vgpciconf.Load()
	if err != nil {
		logger.ErrorNoCtx("failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.VENDORGATEWAY_PCI_SERVICE))
		return cleanupFn, err
	}

	err = cardResponseMapping.LoadCardResponseStatusCodes(vgPciConf.Application.CardResponseStatusCodeFilePath)
	if err != nil {
		logger.Fatal("error loading card response status code", zap.Error(err))
		return cleanupFn, err
	}

	// Required for Tokenizer interceptor
	epifigrpc.GetTokenizerDescriptorsInstance().Load(s)

	// Setup Log Sampling rate descriptors.
	epifigrpc.GetRPCLogSamplingRatesMapInstance().Load(s)

	return cleanupFn, nil
}
