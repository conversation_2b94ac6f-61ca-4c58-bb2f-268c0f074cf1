// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"context"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	"github.com/epifi/be-common/api/vendorgateway"
	config3 "github.com/epifi/be-common/pkg/aws/v2/config"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/crypto/cryptormap"
	"github.com/epifi/be-common/pkg/crypto/rsa/v2"
	wire2 "github.com/epifi/be-common/pkg/crypto/wire"
	"github.com/epifi/be-common/pkg/goxmldsig"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/be-common/pkg/vendorapi/config/genconf"
	"github.com/epifi/gamma/api/tokenizer"
	http2 "github.com/epifi/gamma/api/vendors/http"
	"github.com/epifi/gamma/api/vendors/simulator"
	"github.com/epifi/gamma/vendorgateway-pci/config"
	config2 "github.com/epifi/gamma/vendorgateway/config"
	genconf2 "github.com/epifi/gamma/vendorgateway/config/genconf"
	"github.com/epifi/gamma/vendorgateway/lending/creditcard"
	"github.com/epifi/gamma/vendorgateway/openbanking/card"
	"github.com/google/wire"
	"net/http"
)

// Injectors from wire.go:

func InitializeCardProvisioningService(ctx context.Context, conf *config.Config, vendorapiPkgGenConf *genconf.Config, tokenizerClient tokenizer.TokenizerClient) (*card.Service, error) {
	client := secureHttpClientProvider(conf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(conf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiPkgGenConf, string2)
	configConfig := nilVgConfProvider()
	inMemoryCryptorStore, err := InitCryptors(ctx, conf)
	if err != nil {
		return nil, err
	}
	factory := card.NewFactory(configConfig, conf, inMemoryCryptorStore)
	service := card.NewService(httpRequestHandler, factory, tokenizerClient)
	return service, nil
}

func InitialiseCreditCardService(conf *config.Config, vendorapiPkgGenConf *genconf.Config) *creditcard.Service {
	client := secureHttpClientProvider(conf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(conf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiPkgGenConf, string2)
	configConfig := nilVgConfProvider()
	service := creditcard.NewCreditCardService(httpRequestHandler, configConfig, conf)
	return service
}

// wire.go:

func getHttpClient(conf *config.Config, insecure ...bool) *http.Client {
	env := conf.Application.Environment
	insecureSkipVerifyTLS := false
	if len(insecure) == 1 && insecure[0] {
		insecureSkipVerifyTLS = true
	}
	if cfg.IsSimulatedEnv(env) || insecureSkipVerifyTLS {
		return simulator.NewHttpClient(conf.Secrets.Ids[config.SimulatorCert], insecure...)
	}

	return http2.NewHttpClientWithSSLCert(conf.Secrets.Ids[config.EpiFiFederalClientSslCert], conf.Secrets.Ids[config.EpiFiFederalClientSslKey], conf.HttpClientConfig)
}

func secureHttpClientProvider(conf *config.Config) *http.Client {
	return getHttpClient(conf)
}

func nilSigningContextProvider() *dsig.SigningContext {
	return nil
}

func nilVgConfProvider() *config2.Config {
	return nil
}

func nilVgDynConfProvider() *genconf2.Config {
	return nil
}

var SecureHttpClientNilSignCtxWireSet = wire.NewSet(secureHttpClientProvider, nilSigningContextProvider)

func envProvider(conf *config.Config) string {
	return conf.Application.Environment
}

func InitCryptors(ctx context.Context, conf *config.Config) (*cryptormap.InMemoryCryptorStore, error) {
	var (
		rsaCryptor crypto.Cryptor
	)

	awsConf, err := config3.NewAWSConfig(ctx, conf.Aws.Region, true)
	if err != nil {
		return nil, fmt.Errorf("failed to initialise AWS config: %w", err)
	}

	es := wire2.InitializeInMemoryStore(secretsmanager.NewFromConfig(awsConf))

	err = es.AddInternalPrivateKeyFromSecret(conf.PGPInMemoryEntityStoreParams.InternalEntity)
	if err != nil {
		return nil, err
	}

	err = es.AddExternalPublicKeysFromSecret(conf.PGPInMemoryEntityStoreParams.ExternalEntity)
	if err != nil {
		return nil, err
	}

	pgpCryptor := wire2.InitializePGPCryptor(es)

	if conf.Flags.EnableFederalCardDecryptionByFallbackKey {
		rsaCryptor = v2.NewDecryptOnlyCryptor([]string{conf.Secrets.Ids[config.EpifiFederalCardDataPrivateKey],
			conf.Secrets.Ids[config.EpifiFederalCardDataPrivateKeyFallBack]})
	} else {
		rsaCryptor = v2.NewDecryptOnlyCryptor([]string{conf.Secrets.Ids[config.EpifiFederalCardDataPrivateKey]})
	}
	if rsaCryptor == nil {
		return nil, fmt.Errorf("failed to create RSA cryptor")
	}
	cryptorStore := &cryptormap.InMemoryCryptorStore{}
	cryptorStore.AddCryptor(vendorgateway.Vendor_FEDERAL_BANK, vendorgateway.CryptorType_PGP, pgpCryptor)
	cryptorStore.AddCryptor(vendorgateway.Vendor_FEDERAL_BANK, vendorgateway.CryptorType_RSA, rsaCryptor)
	cryptormap.NewVendorCryptorMap(cryptorStore)

	return cryptorStore, nil
}
