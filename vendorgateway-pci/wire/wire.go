//go:build wireinject
// +build wireinject

package wire

import (
	"context"
	"fmt"
	"net/http"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	awsconfig "github.com/epifi/be-common/pkg/aws/v2/config"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/crypto/cryptormap"
	epifiRsa "github.com/epifi/be-common/pkg/crypto/rsa/v2"
	cryptoWire "github.com/epifi/be-common/pkg/crypto/wire"
	dsig "github.com/epifi/be-common/pkg/goxmldsig"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	"github.com/epifi/be-common/pkg/vendorapi"
	vendorapiPkgGenConf "github.com/epifi/be-common/pkg/vendorapi/config/genconf"
	"github.com/google/wire"

	tokenizerPb "github.com/epifi/gamma/api/tokenizer"
	vendorClient "github.com/epifi/gamma/api/vendors/http"
	simClient "github.com/epifi/gamma/api/vendors/simulator"
	"github.com/epifi/gamma/vendorgateway-pci/config"
	vgConf "github.com/epifi/gamma/vendorgateway/config"
	vggenconf "github.com/epifi/gamma/vendorgateway/config/genconf"
	"github.com/epifi/gamma/vendorgateway/lending/creditcard"
	"github.com/epifi/gamma/vendorgateway/openbanking/card"
)

func getHttpClient(conf *config.Config, insecure ...bool) *http.Client {
	env := conf.Application.Environment
	insecureSkipVerifyTLS := false
	if len(insecure) == 1 && insecure[0] {
		insecureSkipVerifyTLS = true
	}
	if cfg.IsSimulatedEnv(env) || insecureSkipVerifyTLS {
		return simClient.NewHttpClient(conf.Secrets.Ids[config.SimulatorCert], insecure...)
	}

	return vendorClient.NewHttpClientWithSSLCert(conf.Secrets.Ids[config.EpiFiFederalClientSslCert], conf.Secrets.Ids[config.EpiFiFederalClientSslKey], conf.HttpClientConfig)
}

func secureHttpClientProvider(conf *config.Config) *http.Client {
	return getHttpClient(conf)
}

func nilSigningContextProvider() *dsig.SigningContext {
	return nil
}

func nilVgConfProvider() *vgConf.Config {
	return nil
}

func nilVgDynConfProvider() *vggenconf.Config {
	return nil
}

var SecureHttpClientNilSignCtxWireSet = wire.NewSet(secureHttpClientProvider, nilSigningContextProvider)

func InitializeCardProvisioningService(ctx context.Context, conf *config.Config, vendorapiPkgGenConf *vendorapiPkgGenConf.Config, tokenizerClient tokenizerPb.TokenizerClient) (*card.Service, error) {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		nilVgConfProvider,
		card.NewService,
		vendorapi.New,
		httpcontentredactor.GetInstance,
		card.NewFactory,
		wire.Bind(new(vendorapi.HttpDoer), new(*http.Client)),
		InitCryptors,
	)
	return &card.Service{}, nil
}

func envProvider(conf *config.Config) string {
	return conf.Application.Environment
}

func InitialiseCreditCardService(conf *config.Config, vendorapiPkgGenConf *vendorapiPkgGenConf.Config) *creditcard.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		nilVgConfProvider,
		creditcard.NewCreditCardService,
		httpcontentredactor.GetInstance,
		vendorapi.New,
		wire.Bind(new(vendorapi.HttpDoer), new(*http.Client)),
	)
	return &creditcard.Service{}
}

func InitCryptors(ctx context.Context, conf *config.Config) (*cryptormap.InMemoryCryptorStore, error) {
	var (
		rsaCryptor crypto.Cryptor
	)

	awsConf, err := awsconfig.NewAWSConfig(ctx, conf.Aws.Region, true)
	if err != nil {
		return nil, fmt.Errorf("failed to initialise AWS config: %w", err)
	}

	es := cryptoWire.InitializeInMemoryStore(secretsmanager.NewFromConfig(awsConf))

	err = es.AddInternalPrivateKeyFromSecret(conf.PGPInMemoryEntityStoreParams.InternalEntity)
	if err != nil {
		return nil, err
	}

	err = es.AddExternalPublicKeysFromSecret(conf.PGPInMemoryEntityStoreParams.ExternalEntity)
	if err != nil {
		return nil, err
	}

	pgpCryptor := cryptoWire.InitializePGPCryptor(es)

	if conf.Flags.EnableFederalCardDecryptionByFallbackKey {
		rsaCryptor = epifiRsa.NewDecryptOnlyCryptor([]string{conf.Secrets.Ids[config.EpifiFederalCardDataPrivateKey],
			conf.Secrets.Ids[config.EpifiFederalCardDataPrivateKeyFallBack]})
	} else {
		rsaCryptor = epifiRsa.NewDecryptOnlyCryptor([]string{conf.Secrets.Ids[config.EpifiFederalCardDataPrivateKey]})
	}
	if rsaCryptor == nil {
		return nil, fmt.Errorf("failed to create RSA cryptor")
	}
	cryptorStore := &cryptormap.InMemoryCryptorStore{}
	cryptorStore.AddCryptor(commonvgpb.Vendor_FEDERAL_BANK, commonvgpb.CryptorType_PGP, pgpCryptor)
	cryptorStore.AddCryptor(commonvgpb.Vendor_FEDERAL_BANK, commonvgpb.CryptorType_RSA, rsaCryptor)

	cryptormap.NewVendorCryptorMap(cryptorStore)

	return cryptorStore, nil
}
