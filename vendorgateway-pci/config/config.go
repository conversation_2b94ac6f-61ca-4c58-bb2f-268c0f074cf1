package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"

	testtenantep "github.com/epifi/gamma/testing/test_tenant/endpoint"

	"github.com/epifi/be-common/pkg/cfg"
)

var (
	once sync.Once
	conf *Config
	err  error
)

var (
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		conf, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return conf, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	configDirPath := testEnvConfigDir()
	config := &Config{}
	// loads config from file
	k, _, err := cfg.LoadConfigUsingKoanf(configDirPath, cfg.VENDORGATEWAY_PCI_SERVICE)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}
	err = k.UnmarshalWithConf("", config, cfg.DefaultUnmarshallingConfig(config))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	keyToSecret, err := cfg.LoadSecrets(config.Secrets.Ids, config.Application.Environment, config.Aws.Region)
	if err != nil {
		return nil, err
	}

	if err := updateDefaultConfig(config, keyToSecret); err != nil {
		return nil, err
	}
	if cfg.IsTestTenantEnabled() {
		err := testtenantep.UpdateLocalhostEndpoint(&config)
		if err != nil {
			return nil, err
		}
	}
	return config, nil
}

// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {
	if err := readAndSetEnv(c); err != nil {
		return fmt.Errorf("failed to read and set env var: %w", err)
	}

	cfg.UpdateSecretValues(&cfg.DB{GormV2: &cfg.GormV2Conf{}}, c.Secrets, keyToSecret)
	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Port = intVal
	}

	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

type Config struct {
	Application                  *application
	EpifiDb                      *cfg.DB
	Server                       *server
	Logging                      *cfg.Logging
	SecureLogging                *secureLogging
	Secrets                      *cfg.Secrets
	HttpClientConfig             *cfg.HttpClient
	Aws                          *aws
	Flags                        *Flags
	PGPInMemoryEntityStoreParams *cfg.PGPInMemoryEntityStoreParams
}

type secureLogging struct {
	EnableSecureLog bool
	SecureLogPath   string
	MaxSizeInMBs    int // megabytes
	MaxBackups      int // There will be MaxBackups + 1 total files
}

type application struct {
	Environment                    string
	Name                           string
	CardResponseStatusCodeFilePath string
	// map of vendor to Url's
	VendorToUrlMap map[string]*url
	Lending        *lending
}

type url struct {
	// Card Service
	DebitCardCreateURL                   string
	DebitCardCreateCallbackURL           string
	DebitCardActivateURL                 string
	DebitCardEnquiryUrl                  string
	DebitCardPinSetUrl                   string
	DebitCardPinChangeUrl                string
	DebitCardPinResetUrl                 string
	DebitCardPinValidationUrl            string
	DebitCardValidationUrl               string
	DebitCardBlockUrl                    string
	DebitCardSuspendOnOffUrl             string
	DebitCardLocationOnOffUrl            string
	DebitCardECommerceOnOffUrl           string
	DebitCardCVVEnquiryUrl               string
	DebitCardLimitEnquiry                string
	DebitCardUpdateLimit                 string
	DebitCardDeliveryTracking            string
	DebitCardPhysicalDispatchUrl         string
	DebitCardPhysicalDispatchCallbackUrl string
	DebitCardConsolidatedCardControlUrl  string

	// Enquiry status urls
	CustomerCreationEnquiryStatusURL            string
	AccountCreationEnquiryStatusURL             string
	CardCreationEnquiryStatusURL                string
	DeviceReRegistrationDetailsEnquiryStatusURL string
	MailingAddressModifyDetailsEnquiryStatusURL string
	ShippingAddressUpdateEnquiryStatusURL       string
	DeviceRegistrationDetailsEnquiryStatusURL   string
	PhysicalCardDispatchDetailsEnquiryStatus    string
}

type server struct {
	Port            int
	SecurePort      int
	HealthCheckPort int
}

type aws struct {
	Region string
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool
	// A flag to determine if a custom cert pool is to be used of the system cert pool
	UseCustomTrustedCertPool bool
	//
	TokenValidation bool
	// Flag to use fallback key for RSA cryptor
	// If true we will try decryption first using the existing key and in case of failures try with the fallback key
	EnableFederalCardDecryptionByFallbackKey bool
}

type lending struct {
	CreditCard *creditCard
}

type creditCard struct {
	M2P *m2PCreditCard
}

type m2PCreditCard struct {
	M2PHost          string
	M2PFallbackHost  string
	EnableEncryption bool
	RotateKey        bool
}
