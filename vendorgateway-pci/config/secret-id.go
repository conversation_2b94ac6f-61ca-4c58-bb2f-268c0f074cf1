package config

const (
	// Federal
	EpifiFederalPgpPrivateKey = "EpifiFederalPgpPrivateKey"
	FederalPgpPublicKey       = "FederalPgpPublicKey"
	// Adding #nosec to suppress G101: Potential hardcoded credentials

	EpifiFederalPgpPassphrase              = "EpifiFederalPgpPassphrase" // #nosec
	EpifiFederalCardDataPrivateKey         = "EpifiFederalCardDataPrivateKey"
	EpifiFederalCardDataPrivateKeyFallBack = "EpifiFederalCardDataPrivateKeyFallBack"

	SenderCode        = "SenderCode"
	ServiceAccessId   = "ServiceAccessId"
	ServiceAccessCode = "ServiceAccessCode"

	ClientId        = "ClientId"
	ClientSecretKey = "ClientSecretKey"

	// Certs
	SimulatorCert             = "SimulatorCert"
	EpiFiFederalClientSslCert = "EpiFiFederalClientSslCert"
	EpiFiFederalClientSslKey  = "EpiFiFederalClientSslKey"
)
