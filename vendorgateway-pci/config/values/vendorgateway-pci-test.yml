Application:
  Environment: "test"
  Name: "vendorgateway-pci"
  CardResponseStatusCodeFilePath: "./mappingJson/cardResponseStatusCodes.json"
  VendorToUrlMap:
    FEDERAL_BANK:
      DebitCardCreateURL: "https://localhost:9091/fedbnk/uat/neobanking-card/CardCreation"
      DebitCardCreateCallbackURL: "http://localhost:9098/openbanking/card/federal"
      DebitCardActivateURL: "https://localhost:9091/fedbnk/uat/neobanking-card/CardActivation"
      DebitCardEnquiryUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/CardEnqService"
      DebitCardPinSetUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/PinManagement"
      DebitCardPinChangeUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/PinManagement"
      DebitCardPinResetUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/PinManagement"
      DebitCardPinValidationUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/PinManagement"
      DebitCardBlockUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/CardControl"
      DebitCardSuspendOnOffUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/CardControl"
      DebitCardLocationOnOffUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/CardControl"
      DebitCardECommerceOnOffUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/CardControl"
      DebitCardCVVEnquiryUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/CVVEnquiry"
      DebitCardLimitEnquiry: "https://localhost:9091/fedbnk/uat/neobanking-card/LimitEnquiry"
      DebitCardUpdateLimit: "https://localhost:9091/fedbnk/uat/neobanking-card/LimitUpdate"
      DebitCardDeliveryTracking: "https://localhost:9091/fedbnk/uat/neobanking-card/DeliveryTracking"
      DebitCardPhysicalDispatchUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/cardDispatch"
      DebitCardPhysicalDispatchCallbackUrl: "https://localhost:9098/openbanking/cardPhysicalDispatch/federal"
      DebitCardConsolidatedCardControlUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/FHMCardONOFF"

      CustomerCreationEnquiryStatusURL: "https://localhost:9091/CustomerCreationEnquiryStatus"
      AccountCreationEnquiryStatusURL: "https://localhost:9091/AccountCreationEnquiryStatus"
      CardCreationEnquiryStatusURL: "https://localhost:9091/CardCreationEnquiryStatus"
      DeviceReRegistrationDetailsEnquiryStatusURL: "https://localhost:9091/DeviceReRegistrationDetailsEnquiryStatus"
      MailingAddressModifyDetailsEnquiryStatusURL: "https://localhost:9091/MailingAddressModifyDetailsEnquiryStatus"
      ShippingAddressUpdateEnquiryStatusURL: "https://localhost:9091/ShippingAddressUpdateEnquiryStatus"
      DeviceRegistrationDetailsEnquiryStatusURL: "https://localhost:9091/DeviceRegistrationDetailsEnquiryStatus"
      PhysicalCardDispatchDetailsEnquiryStatus: "https://localhost:9091/PhysicalCardDispatchDetailsEnquiryStatus"
  Lending:
    CreditCard:
      M2P:
        M2PHost: "https://sit-secure.yappay.in/"
        EnableEncryption: false
Server:
  Port: 8120
  SecurePort: 9527
  HealthCheckPort: 9999

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    #M2P
    # TODO : add the entityId for fi given by m2p
    M2PSecrets: '{"partnerId" : "FDEPIFICR", "partnerToken" : "Basic Q1JFRElUREVNTw==", "Authorization" : "Basic YWRtaW46YWRtaW4=", "TENANT" : "FDEPIFICR"}'
    M2PSecuredCardSecrets: '{"partnerId" : "FDEPIFISECCR", "partnerToken" : "Basic RkRFUElGSVNFQ0NS", "Authorization" : "Basic YWRtaW46YWRtaW4=", "TENANT" : "FDEPIFISECCR", "Source": "EPIFI", "AgencyId": "EPIFI", "MerchantId": "EPIFI", "AggregatorId": "f9f9df6deb5c69768d47a39be9d84114146e1f31"}'
    M2PMassUnsecuredCardSecrets: '{"partnerId" : "FDEPIFIMASSCR", "partnerToken" : "Basic Q1JFRElUREVNTw==", "Authorization" : "Basic YWRtaW46YWRtaW4=", "TENANT" : "FDEPIFIMASSCR", "Source": "EPIFI", "AgencyId": "EPIFI", "MerchantId": "EPIFI", "AggregatorId": "f9f9df6deb5c69768d47a39be9d84114146e1f31"}'
    #Federal
    EpifiFederalPgpPassphrase: "qwerty1234"
    EpifiFederalPgpPrivateKey: |
      -----BEGIN PGP PRIVATE KEY BLOCK-----

      lQdGBF5qBvgBEADGijefHuDTXqiReXdZcxbknDmueW5XGWzR0EHec9kRYkxcQy+H
      gY/q7RJ+5VNNM8KfWtdM+K2zoTdkv6uIp6eYEcC6YcRV1MeX6KbxGGpMzRmb5f0c
      d+gePS1WOeOWjjS+3AXmH0DO+v8bXDxAullL8L0jDYK1sRxWlu9tdwglyJx7IXQi
      VibCaSfrNDrTI7HmrLw8PZ/rfHq5prhEQ9cCeI+kHKz2Amiuts8PGl5M1MEx2oC8
      nSMtb3YXg32FiYkyOi/vXZ5jmltcR9PyhJnMkNFrZRcBxU5dWhB/CpD/IebaKbir
      KuH2Ig/0d8PvvLlyILMyto5C2hfho2QWbFisMuSF23x798CYrFqLBDjFersDDfIW
      4yKsMD+2J+9T/7nBUYdIUmCEmg+khtIVanpJnQQP+iqb8NB7oTevBXKaIST95NhK
      b7B6J9/IDy0xFyPFPtXQyHSoufAjJPfNIgFb8cMTIuQEfN402Fp6eH5NwlvECFWy
      y8S00VFxr8mHXZ1zmvYK6Kp3OLR+q0tpUKmWu6KR4UdKY2Lce7ciuFVGlw1ufBgE
      nxlJNguLDCD23aod78QTWI6ubnCRKTdDLoR7H3CRkIQYzQme3xKJRHdEBfEOioTd
      6+rc1VHp1iLlo7uAlk01wGVdwRmO3deSi1y367TWXy1tyFscOvlGuF6r9QARAQAB
      /gcDAo8iYHB6tWca6/vYjx8IhXmfges2Lti8I805xB7+Ooilxat4ZtBUQHjMGm36
      CPTHwxpca4HfrGIYkCPQ7RmIQTYJBir1NWciD/Dvk0F3TeBicXz9Qzg8nsIsmNug
      SUgHFmbUshT9hMYXAp6no4a+0vJhOH+Wg5CbXV++fl6VhxOoesftfoenJ1JJ2G4Y
      HWElJWuaY4ozf5APC9JrC4KKyjJpZFWg7GoHjyPi6DYNOP1UhXghB0r2biokoJJt
      HErCBXpH+yA2/8L/j0tfDT31IiP/Ge0m2mFIKt6SpBzkbyeY5Pr/ZfMIt4rPwsf8
      2O2B1aL5JF358cKsv4DGGjIv1G3joqyYbrub8j8zL0shYmiBfPeLXdW39NQxplxy
      BDiSCSMRa3IV3J7YztR87GEeKbcqS6gY0sVQB9HrkNTxqh9DG0kNwovZRhGAbK1+
      +1ctahA+XEWghkXF2U2VBJjT42OgzYtgAkyD1+vPeThEKfzOxMARGcavuURX+kiI
      e19MLPuFdJhcZ8pOUv3Y3Otpa5BgsjBUWObGVwUFe1XJ9Zhe+JOIg2QUj35kVReR
      NiyvYrh6QHxnlG15HAgpica3a1gEnxf6M5nXP2XOsmGX+SKzlnBtvkxCy9+3zopJ
      S3NReIK52YWX/NPTK4rKahbv+17xFyRk5msmxsxVO1X3nZbGRgy+49Uv3D8fc7JD
      u3hnpj0DgIaQXKGv/vlEqFtHUsonABXG9ylPvsTJ4I7gbUbmpLV1t8LmF5x4xFfw
      n0O++iBRUqzkLi2+6e5ZPTna24F8hWkAq6k++nVKFCSz2NoDcTwrjLRM/fkkqJe3
      g2X1tug7x0B+/MwbY0IJwhEaHbZpeGVuXr+XIEs2uleWZ3kiJJYHSyRHHpcc/IxQ
      6N4gflA/tZKwFuBBBnIRXt97m2WseTGcxF+lAuVbCS7mB2PFscYfkQVEaq7enHrN
      Cx62RlIf8ZnmeeIVmU9VylFOy4BlFcg4lwjuY2cgoX+q/zNwkhaoGRXlp039CjFW
      0R6OMV2iY3tUDpQyTki9lc4seSIKyN+LOEd74R6mHZEKNpuDJFmGDla5GDPW9ZP3
      s57zvRXDOjgrxsQNcZUcF/cmaIc+pdVzgs32P/ojaCW5h7ncr66hxms4VBvi1JVD
      N3Q74BAcHbyJ/FYVW1lgHSzDVXJL8EqctV6/HGg9e+IRRgYBIf0q4fUWURrIes6d
      /TQ2S1O/u536ncDTzhuh7MoC5UUv97Cmjovk5iLbdsYXUapJKMZX+duDw2LhOUzy
      kukrLShRR8BQ4aQndStLBGzorVjMOrPMFZQivwmwFEB3n4rXBeG4YtMAUvOZeZvM
      tMvD3gKh+0hkRgYRuQi0ZKBb3OSaw7O/Dvb+68wUMlPfoK+i1x5TIx4wY5ws3A37
      XM3i10aFbWouk84RZ5ntF4aDcFZ+r/T1BkzHc7e4HrcuYz3nAudxx+zqkjCM/MZv
      4dmTnw5ODcN97tXhnxaLcRVT9M+2inMOtuSVYa48y7AbyR/cSk9N46CVpTwwioiI
      5fpvHCTGy52m5HxXVwwNXqliWESGc5PefoN5IoDUzX5J6RVFcDw7n0bTNBbvxFHT
      mbsu2w+biEmQXqovAIW0YZK0Xnnu4J0DwKCb+8loxnNOTzANyfkUa2eskjZ7H2Q6
      J4aq7sqH/lAQU439uHeuCPmzwK2jlDmrUhO1SgJyHglP/tnSPkk+rCEivqIawqgr
      ATFhWgNUXP9zPBHejhiNFOO65AXVTivAmlFkZm6+di6XBD9lCF5dtP+0I0F1dG9n
      ZW5lcmF0ZWQgS2V5IDxhbmFuZEBlcGlmaS5jb20+iQJOBBMBCAA4FiEEhUtW2MRo
      EDU7awr4z4B1fb7ViawFAl5qBvgCGy8FCwkIBwIGFQoJCAsCBBYCAwECHgECF4AA
      CgkQz4B1fb7Viaw0RBAAtLprII+/f3u53627dzrbyIE/z19ih34v/WVsDiMpyUYK
      qgXvL9a0P8dP+EO/rERxH9jbt5KtJ1zE4bGoT7zEQr/a36gwAu5DwaFInaPTrqPZ
      5FL/xytReUcjuPb5W6qWF2mO6iSd3ZwQbo/ZNjqgGIem+ouPfPtyyz7mdsM42FRF
      ANZ6JWSpqpuEl1l1GNFfVEmJMM0SgfsSm2XClGgTHQ2hyRyH0zcBoojjOigqkEHd
      ytM3MVovn2z2+HjqSU1GMCT21dRKZnV/FkN8GHW4ITOUd2X1Z4eBt7y8/n/WIFbx
      uwbSRtYKULe5LjgMA+Cv6bUBRqruUOdW52iFpxU0Eyr6+T2kEJeXtyWkGA297B5W
      LtCWRPvQAdoj8Qxky2ch5zyrKfSPwpPF3o/HcPvodk5hYXkhE/AHNqqu0ORmtevj
      20pbH84yUfjJEKJbLKu8MEuKysxSgNN00gHmjVEJa98Cl+qMAdzNBpDiJTlriM6y
      uwychPPrgZoe98YKy2/xRsZ2s/BPRazqllwZ8i+x/Ks8iYC+lSrmGlAtZulwOS8g
      +Flz7yPuYZB9IXNRyd7dZNRlxrGlcD1VHUkgyoZvL3EgpKtUofaNJ1WYtXigEqe8
      aLci0kOkZjjhA9FhikU3VUf+jlLKR5sGx2xWPZAnASrZU8/pIGI6sUsxWmSBHvg=
      =c5Qs
      -----END PGP PRIVATE KEY BLOCK-----

    FederalPgpPublicKey: |
      -----BEGIN PGP PUBLIC KEY BLOCK-----

      mQINBF5or0QBEADLY8Gg8s3RoRjxtX3fyHvgpAl/jdE1v3QETt6mBBWK2jrxgDYv
      uGrE2dqHr6/XETpFzgsmGpXILijryvqu+6QL5/RQTXfSUyK+l14unUEWwAtIEPt9
      kbxxXs8QvTw4+PnskVCOOcmwPnamU7jHYmx3bR82KBW0lroSroAq+U0bQGWuDeei
      Ry5uhN6xzQHjEXJEEwLMn0UBJI/vf4B6jJ3Z3Se9yCcuUqEPCAId3Vz2nNFwEAQA
      kC/WeuaVnxqL/evRlNeEzPVKmbwzLhc9e2p4acPRdMSeu9lvmHuS7VTR0iAomjL2
      KCXCJWQ+LhkusKDTjh5U+/cZtRF3JWRzdjjIGhxjz87kzjQXup+0KETF06Uzm4BB
      dVodXV2YJKn3WsC9xolAOjZ3sFPHReOxzS2MVtzAp7hFUBagZ/PmokbG/lEkPqWT
      1YBGCrIZ1CRFWpQCZeA4gbVny/RAeMuaDJp+Sr6QVi/NPAGtMXregGxxuHYPRZmb
      6xNPxsTW0Ak31YzFYCj/1hMMtGLp81cc3FVGGZIZWHajm0LBBOHE6sjCaKdyl6Bo
      BHxhaAMRHg+H123JOCz1WdkyhE+5R+0sQiEZLNK7/hciVaLESrfblNCc5RhgQ3mm
      qLYydkZMqhi1zwDIKg7YueJoQM+gd6bTzNfqLGW2UPOQojJZ9SosxcHthwARAQAB
      tCNBdXRvZ2VuZXJhdGVkIEtleSA8YW5hbmRAZXBpZmkuY29tPokCTgQTAQgAOBYh
      BL2/CLI39EtWfkngfSioaI1AylhdBQJeaK9EAhsvBQsJCAcCBhUKCQgLAgQWAgMB
      Ah4BAheAAAoJECioaI1Aylhd1vYQAKRmtMDPSgmiAN5+eVDA75PPdmjemzkHyM2S
      lEOljzUeCZKOIY3JZCWwQEFdoru9HHmMqFPrHUSJMGqN19xc/JYeEMxv/hkZoeEv
      DPG70cvnJotDYqm++EFRH8u0VbvEWDJKMCBAKQ2AYcWsrq5EsA3FP/a+FvxriBgj
      tfA5pMSrcxCsLfqkfgrA9k2JQekJ5hTIi4p9Dcm3eaKr6fqmNW3FRTQ/Pg8M04vA
      WuwRwOqccUKauNAT+UrqOdJZWOJswlC5OumjHRXILoyBps8ALLuVMEkFyLEBDPLR
      D79bgxjIzHmOxjIJPv8NypFqr7Z/KNIknsGQb0+d0Nb9RgOIWDpqeiKHLI3ejsqJ
      S4+q4G0oSta5CKfg5lMN80pmFqeuSQ9ZU2PfQL9JadTerzWs+6ob+HLAAnXelcOJ
      aRO2y7jUa86cgkxxy64wiiZUup0d9rDN8o/ek0EUnzROXteiSvX2Rx8a8yd9k/FI
      j3iXEY5M0Mqo6/HapgNttRrCK9ku1mrJ5YnnkSS+DlBVYtXM1kL2pKSHVtdcxVxi
      yKNBHzAyG0cjHMatW3RBGSReYZbe5+g+QaxdqzVLK6DEFqp1n2kAM+h9aCdBrqwC
      TtIUbUa7OIiSXWCau5c5NgxESfwVDS6tBNA8BN/MxjcvpNsJ0xG/YmfCAy3eYFXF
      xiTYY1ba
      =bnCx
      -----END PGP PUBLIC KEY BLOCK-----
    SenderCode: "EpiFi_Test_Cd"
    ServiceAccessId: "EpiFi_Fed_Test"
    ServiceAccessCode: "EFed@123"
    EpifiFederalCardDataPrivateKey: |
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    EpifiFederalCardDataPrivateKeyFallBack: |
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    #TLS certs
    SimulatorCert: |
      -----BEGIN CERTIFICATE-----
      MIID4jCCAsqgAwIBAgIJAPHySk9LXHBYMA0GCSqGSIb3DQEBCwUAMIGJMQswCQYD
      VQQGEwJJTjESMBAGA1UECAwJS2FybmF0YWthMRIwEAYDVQQHDAlCYW5nYWxvcmUx
      KTAnBgNVBAoMIGVwaUZpIFRlY2hub2xvZ3kgUHJpdmF0ZSBMaW1pdGVkMRMwEQYD
      VQQLDApUZWNobm9sb2d5MRIwEAYDVQQDDAlsb2NhbGhvc3QwHhcNMjIwMTI1MTEz
      OTQ0WhcNMzExMDI1MTEzOTQ0WjCBiTELMAkGA1UEBhMCSU4xEjAQBgNVBAgMCUth
      cm5hdGFrYTESMBAGA1UEBwwJQmFuZ2Fsb3JlMSkwJwYDVQQKDCBlcGlGaSBUZWNo
      bm9sb2d5IFByaXZhdGUgTGltaXRlZDETMBEGA1UECwwKVGVjaG5vbG9neTESMBAG
      A1UEAwwJbG9jYWxob3N0MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA
      qEGrrXTlxnNe77CjSHKN1zLdURnicArDB4Jm0pTygLw7jSpLzl1ucJoJL+mdE3GB
      Pvm/mw8wwORWQ5pjCkIz+Wyyv9PbIMHkY0wK1TUFZ1pCzlWPPKoYzHrAp0cue5ab
      J1iSrsQivZGFw8HFYxCKFQ3aQYQF4MKwZLnRQK5PdWrIxCkcAE0MD+GbKUJOUhVh
      gFp6bOlsVRxzC9kiPN7kcNhCyomEvFN4SktRGRTrf6hMsk50qfY8oNHZIjYM4sYi
      O4uNbS4pEc7JL7a5mjIz1571jEuv+RQYgKjZep/2h1mifXTnKa8uZxBt7IkfYBaZ
      ie264TpFswJuCHj+PnXbMQIDAQABo0swSTALBgNVHQ8EBAMCBDAwEwYDVR0lBAww
      CgYIKwYBBQUHAwEwJQYDVR0RBB4wHIIJbG9jYWxob3N0gg8qLmxvY2FsaG9zdC5j
      b20wDQYJKoZIhvcNAQELBQADggEBABqPwuCGr5eIOY5fCHD7AUTgTOjiqvckZ1uo
      +2h96pd/IlLUUkWa90dvX4ig6Hp5zJdsinjTmPkkEqm64S4Ae1i1aKIfNrwl1uh3
      icgKw4RBTL+xk9qSAiUpX2Bd3JtvDhsfSJh5WPjnaIqGFeuH2Iihqq8LfUdRKKy/
      s6/RCpG3pZHc06DG8LbI7UGL6+q9YKyBC9rGWDPejVlKF+GydGMb70SZC1MbKLwv
      xyCiszQJgWxEIIVyLRKXBgWE0BUErzjCGHiIDXLmNRG+m7FRiY9Arawq5D4Hjqs9
      UkBbV8UGKWx35T62IRsTciClmd6hwRi9a2Z0tRIYj077wFEBYeM=
      -----END CERTIFICATE-----
    EpiFiFederalClientSslCert: |
      -----BEGIN CERTIFICATE-----
      MIIDiDCCAnACCQCEu/A4YFWX5zANBgkqhkiG9w0BAQsFADCBhTELMAkGA1UEBhMC
      SU4xEjAQBgNVBAgMCUthcm5hdGFrYTESMBAGA1UEBwwJQmFuZ2Fsb3JlMQ4wDAYD
      VQQKDAVlcGlGaTELMAkGA1UECwwCSVQxEjAQBgNVBAMMCWVwaWZpLmNvbTEdMBsG
      CSqGSIb3DQEJARYOdGVhbUBlcGlmaS5jb20wHhcNMjAwMjA0MTMwMTA1WhcNMjEw
      MjAzMTMwMTA1WjCBhTELMAkGA1UEBhMCSU4xEjAQBgNVBAgMCUthcm5hdGFrYTES
      MBAGA1UEBwwJQmFuZ2Fsb3JlMQ4wDAYDVQQKDAVlcGlGaTELMAkGA1UECwwCSVQx
      EjAQBgNVBAMMCWVwaWZpLmNvbTEdMBsGCSqGSIb3DQEJARYOdGVhbUBlcGlmaS5j
      b20wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCXVsjAUyQQO4CX66Sn
      NDDFljkhR0P5xQ7M1QFobfCIoj1zFb2DYgJtUzPFkhoFS4BRFxo6/iaEDmMUflql
      izZAAMOi+li+RhXiAl6Czns9PGgNmX6FSLP3dFFahjbNQn3aCNMKaalJ699+G8cF
      vi/jOcmb/ieYxQTGdYjmijRrV2MEc/0rmH6AhZFAxpFf8JpRGTKVCDxsAEn6vH0K
      /1RQF9xpFGfw5QTCOvGDdJj3XSYA02zEnY32WAKyFP/SMqYbg6KTXRdjEZr3fJHt
      oJps+bfiIfhgxvazUdW67nr1fO3Mhta1onSWnLTArmbJBAx0tv68DlCSanNgfBm0
      4uGlAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAHXRh59vyYkT5B6x4ecmrVPMhHUo
      bSQjWGgRXTSWXsClb+o4QwGfLqr+eUbpfUBd8qeK59nxs71/Ytnloc2sA/8cpgsw
      HQVAPJCGR/J2YvD7NlNgYVnyxF/efYStQCMER+duw2RLTZ4S7yuhwBfWxsvQnjTF
      J9BhAbZgOy8QWeGB8MPDMPWzE8KcaSuOy6F/qH3T3QnV1rWi/Jo2d4UTQyhRqqTa
      sC5w1S9fo/lfqt/c7UuKaC8d4SFU2F3Q9NnL4Cp59QGAmC1VSqQbEqfkpE7ZLRbf
      6e88i5/faH2vKBIrh5KZxp2LjZygaF3AhNgundhq/X9RfKu/qFf93t+Kric=
      -----END CERTIFICATE-----
    EpiFiFederalClientSslKey: |
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

    ClientId: "3086b14c-6922-42a4-972b-ba4560de24c8"
    ClientSecretKey: "rR2jG6aF0yH8fJ3wB2eW6rS0gV4vD6iR0uC7iW4qX6qW4uI2dX"
