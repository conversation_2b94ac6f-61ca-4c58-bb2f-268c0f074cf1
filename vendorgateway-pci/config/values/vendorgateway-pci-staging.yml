Application:
  Environment: "staging"
  Name: "vendorgateway-pci"
  CardResponseStatusCodeFilePath: "./mappingJson/cardResponseStatusCodes.json"
  VendorToUrlMap:
    FEDERAL_BANK:
      DebitCardCreateURL: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/CardCreation"
      DebitCardCreateCallbackURL: "https://vnotificationgw.staging.pointz.in/openbanking/card/federal"
      DebitCardActivateURL: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/CardActivation"
      DebitCardEnquiryUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/CardEnqService"
      DebitCardPinSetUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
      DebitCardPinChangeUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
      DebitCardPinResetUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
      DebitCardPinValidationUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
      DebitCardBlockUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
      DebitCardSuspendOnOffUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
      DebitCardLocationOnOffUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
      DebitCardECommerceOnOffUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
      DebitCardCVVEnquiryUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/CVVEnquiry"
      DebitCardLimitEnquiry: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/LimitEnquiry"
      DebitCardUpdateLimit: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/LimitUpdate"
      DebitCardDeliveryTracking: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/DeliveryTracking"
      DebitCardPhysicalDispatchUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/cardDispatch"
      DebitCardPhysicalDispatchCallbackUrl: "https://vnotificationgw.staging.pointz.in/openbanking/cardPhysicalDispatch/federal"
      DebitCardConsolidatedCardControlUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/FHMCardONOFF"

      CustomerCreationEnquiryStatusURL: "https://simulator.staging.pointz.in:9091/CustomerCreationEnquiryStatus"
      AccountCreationEnquiryStatusURL: "https://simulator.staging.pointz.in:9091/AccountCreationEnquiryStatus"
      CardCreationEnquiryStatusURL: "https://simulator.staging.pointz.in:9091/CardCreationEnquiryStatus"
      DeviceReRegistrationDetailsEnquiryStatusURL: "https://simulator.staging.pointz.in:9091/DeviceReRegistrationDetailsEnquiryStatus"
      MailingAddressModifyDetailsEnquiryStatusURL: "https://simulator.staging.pointz.in:9091/MailingAddressModifyDetailsEnquiryStatus"
      ShippingAddressUpdateEnquiryStatusURL: "https://simulator.staging.pointz.in:9091/ShippingAddressUpdateEnquiryStatus"
      DeviceRegistrationDetailsEnquiryStatusURL: "https://simulator.staging.pointz.in:9091/DeviceRegistrationDetailsEnquiryStatus"
      PhysicalCardDispatchDetailsEnquiryStatus: "https://simulator.staging.pointz.in:9091/PhysicalCardDispatchDetailsEnquiryStatus"
  Lending:
    CreditCard:
      M2P:
        M2PHost: "https://simulator.staging.pointz.in:9091/"
        M2PFallbackHost: "https://simulator.staging.pointz.in:9091/"
        EnableEncryption: false
Server:
  Port: 8120
  SecurePort: 9527
  HealthCheckPort: 9999

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    #M2P
    M2PSecrets: "staging/vg-vgpci/m2p"
    M2PSecuredCardSecrets: "staging/vg-vgpci/m2p/secured"
    M2PMassUnsecuredCardSecrets: "staging/vg-vgpci/m2p/mass-unsecured"
    EpifiM2pRsaPrivateKey: "staging/vg-vgpci/epifi-m2p-private-key"
    EpifiM2pRsaPublicKey: "staging/vg-vgpci/m2p-public-key"
    #Federal
    EpifiFederalPgpPrivateKey: "staging/pgp/pgp-epifi-key-fed-api"
    FederalPgpPublicKey: "staging/pgp/pgp-federal-dummy-key-for-sim"
    EpifiFederalPgpPassphrase: "staging/pgp/pgp-epifi-key-passphrase-fed-api"
    SenderCode: "staging/vg-vn-simulator-vgpci/federal-auth-sender-code"
    ServiceAccessId: "staging/vg-vn-vgpci/federal-auth-service-access-id"
    ServiceAccessCode: "staging/vg-vn-vgpci/federal-auth-service-access-code"
    EpifiFederalCardDataPrivateKey: "staging/vg-vngw-vgpci/federal-card-data-decryption"
    EpifiFederalCardDataPrivateKeyFallBack: "staging/vg-vngw-vgpci/rsa-federal-card-data"

    #TLS certs
    SimulatorCert: "staging/vg-vgpci/tls-client-cert-for-sim"
    EpiFiFederalClientSslCert: "staging/vg-vgpci/tls-client-cert-for-federal"
    EpiFiFederalClientSslKey: "staging/vg-vgpci/tls-client-priv-key-for-federal"
    ClientId: "staging/vg-vgpci/federal-auth-client-id"
    ClientSecretKey: "staging/vg-vgpci/federal-auth-client-secret-key"

PGPInMemoryEntityStoreParams:
  InternalEntity:
    - Secret: "staging/pgp/v1/pgp-epifi-fed-api"
  ExternalEntity:
    - Secret: "staging/pgp/v1/federal-simulator"
