{"ResponseCodes": [{"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0001", "StatusCode": "CARD_PIN_001"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0002", "StatusCode": "CARD_PIN_002"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0003", "StatusCode": "CARD_PIN_003"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0004", "StatusCode": "CARD_PIN_004"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0007", "StatusCode": "CARD_PIN_005"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0118", "StatusCode": "CARD_PIN_006"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0119", "StatusCode": "CARD_PIN_007"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0120", "StatusCode": "CARD_PIN_008"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0121", "StatusCode": "CARD_PIN_009"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0122", "StatusCode": "CARD_PIN_010"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0123", "StatusCode": "CARD_PIN_011"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0124", "StatusCode": "CARD_PIN_012"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0125", "StatusCode": "CARD_PIN_013"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0126", "StatusCode": "CARD_PIN_014"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0127", "StatusCode": "CARD_PIN_015"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0128", "StatusCode": "CARD_PIN_016"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0129", "StatusCode": "CARD_PIN_017"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0130", "StatusCode": "CARD_PIN_018"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0131", "StatusCode": "CARD_PIN_019"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "000", "StatusCode": "CARD_PIN_020"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "00", "StatusCode": "CARD_PIN_021"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0132", "StatusCode": "CARD_PIN_022"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0133", "StatusCode": "CARD_PIN_023"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0134", "StatusCode": "CARD_PIN_024"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0135", "StatusCode": "CARD_PIN_025"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0136", "StatusCode": "CARD_PIN_026"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0137", "StatusCode": "CARD_PIN_027"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0138", "StatusCode": "CARD_PIN_028"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0074", "StatusCode": "CARD_PIN_029"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE8888", "StatusCode": "CARD_PIN_030"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0059", "StatusCode": "CARD_PIN_031"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0011", "StatusCode": "CARD_PIN_032"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0066", "StatusCode": "CARD_PIN_033"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0061", "StatusCode": "CARD_PIN_034"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0062", "StatusCode": "CARD_PIN_035"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0064", "StatusCode": "CARD_PIN_036"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0072", "StatusCode": "CARD_PIN_037"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0073", "StatusCode": "CARD_PIN_038"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0108", "StatusCode": "CARD_PIN_039"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0102", "StatusCode": "CARD_PIN_040"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0109", "StatusCode": "CARD_PIN_041"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0110", "StatusCode": "CARD_PIN_042"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0070", "StatusCode": "CARD_PIN_043"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0012", "StatusCode": "CARD_PIN_044"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0113", "StatusCode": "CARD_PIN_045"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0114", "StatusCode": "CARD_PIN_046"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0115", "StatusCode": "CARD_PIN_047"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0067", "StatusCode": "CARD_PIN_048"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0065", "StatusCode": "CARD_PIN_049"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "T5", "StatusCode": "CARD_PIN_050"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "55", "StatusCode": "CARD_PIN_051"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "Q1", "StatusCode": "CARD_PIN_052"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "O6", "StatusCode": "CARD_PIN_053"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "56", "StatusCode": "CARD_PIN_054"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "41", "StatusCode": "CARD_PIN_055"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "91", "StatusCode": "CARD_PIN_056"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "16", "StatusCode": "CARD_PIN_057"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "38", "StatusCode": "CARD_PIN_058"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "75", "StatusCode": "CARD_PIN_059"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "67", "StatusCode": "CARD_PIN_060"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "14", "StatusCode": "CARD_PIN_061"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "57", "StatusCode": "CARD_PIN_062"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "54", "StatusCode": "CARD_PIN_063"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "909", "StatusCode": "CARD_PIN_064"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "62", "StatusCode": "CARD_PIN_065"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "36", "StatusCode": "CARD_PIN_066"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "F09", "StatusCode": "CARD_PIN_067"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "F08", "StatusCode": "CARD_PIN_068"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "F99", "StatusCode": "CARD_PIN_069"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "F07", "StatusCode": "CARD_PIN_070"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "F06", "StatusCode": "CARD_PIN_071"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "F05", "StatusCode": "CARD_PIN_072"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "91", "StatusCode": "CARD_PIN_073"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "98", "StatusCode": "CARD_PIN_074"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_PIN", "RawStatusCode": "OBE0159", "StatusCode": "CARD_PIN_075"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CREATION_ENQUIRY", "RawStatusCode": "OBE0071", "StatusCode": "CARD_CRTN_ENQ_001"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CREATION_ENQUIRY", "RawStatusCode": "OBE0007", "StatusCode": "CARD_CRTN_ENQ_002"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0000", "StatusCode": "CARD_CTRL_001"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0001", "StatusCode": "CARD_CTRL_002"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0002", "StatusCode": "CARD_CTRL_003"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0003", "StatusCode": "CARD_CTRL_004"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0004", "StatusCode": "CARD_CTRL_005"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0007", "StatusCode": "CARD_CTRL_006"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0046", "StatusCode": "CARD_CTRL_007"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0045", "StatusCode": "CARD_CTRL_008"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0044", "StatusCode": "CARD_CTRL_009"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0043", "StatusCode": "CARD_CTRL_010"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "17", "StatusCode": "CARD_CTRL_011"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "18", "StatusCode": "CARD_CTRL_012"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "16", "StatusCode": "CARD_CTRL_013"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "N6", "StatusCode": "CARD_CTRL_014"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "15", "StatusCode": "CARD_CTRL_015"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "N5", "StatusCode": "CARD_CTRL_016"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "27", "StatusCode": "CARD_CTRL_017"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE8888", "StatusCode": "CARD_CTRL_018"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0059", "StatusCode": "CARD_CTRL_019"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0011", "StatusCode": "CARD_CTRL_020"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0066", "StatusCode": "CARD_CTRL_021"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0061", "StatusCode": "CARD_CTRL_022"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0062", "StatusCode": "CARD_CTRL_023"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0064", "StatusCode": "CARD_CTRL_024"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0072", "StatusCode": "CARD_CTRL_025"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0073", "StatusCode": "CARD_CTRL_026"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0060", "StatusCode": "CARD_CTRL_027"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0102", "StatusCode": "CARD_CTRL_028"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0070", "StatusCode": "CARD_CTRL_029"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0113", "StatusCode": "CARD_CTRL_030"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0114", "StatusCode": "CARD_CTRL_031"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0115", "StatusCode": "CARD_CTRL_032"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0067", "StatusCode": "CARD_CTRL_033"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0065", "StatusCode": "CARD_CTRL_034"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "000", "StatusCode": "CARD_CTRL_035"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0170", "StatusCode": "CARD_CTRL_036"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CARD_CONTROL", "RawStatusCode": "OBE0999", "StatusCode": "CARD_CTRL_037"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "OBE0001", "StatusCode": "CARD_CVV_ENQ_001"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "OBE0002", "StatusCode": "CARD_CVV_ENQ_002"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "OBE0003", "StatusCode": "CARD_CVV_ENQ_003"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "OBE0004", "StatusCode": "CARD_CVV_ENQ_004"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "OBE0007", "StatusCode": "CARD_CVV_ENQ_005"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "OBE0045", "StatusCode": "CARD_CVV_ENQ_006"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "OBE8888", "StatusCode": "CARD_CVV_ENQ_007"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "OBE0059", "StatusCode": "CARD_CVV_ENQ_008"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "OBE0060", "StatusCode": "CARD_CVV_ENQ_009"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "OBE0045", "StatusCode": "CARD_CVV_ENQ_011"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "OBE0070", "StatusCode": "CARD_CVV_ENQ_012"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "OBE0073", "StatusCode": "CARD_CVV_ENQ_010"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "000", "StatusCode": "CARD_CVV_ENQ_013"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "00", "StatusCode": "CARD_CVV_ENQ_014"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "F07", "StatusCode": "CARD_CVV_ENQ_015"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "24", "StatusCode": "CARD_CVV_ENQ_016"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "27", "StatusCode": "CARD_CVV_ENQ_017"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "F132", "StatusCode": "CARD_CVV_ENQ_018"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "F142", "StatusCode": "CARD_CVV_ENQ_019"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "P1", "StatusCode": "CARD_CVV_ENQ_020"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "N1", "StatusCode": "CARD_CVV_ENQ_021"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "N2", "StatusCode": "CARD_CVV_ENQ_022"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "N3", "StatusCode": "CARD_CVV_ENQ_023"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "N6", "StatusCode": "CARD_CVV_ENQ_024"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "N9", "StatusCode": "CARD_CVV_ENQ_025"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "OBE0102", "StatusCode": "CARD_CVV_ENQ_026"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "OBE0011", "StatusCode": "CARD_CVV_ENQ_027"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "F444", "StatusCode": "CARD_CVV_ENQ_028"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "OBE0062", "StatusCode": "CARD_CVV_ENQ_029"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "OBE0064", "StatusCode": "CARD_CVV_ENQ_030"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CVV_ENQUIRY", "RawStatusCode": "OBE0061", "StatusCode": "CARD_CVV_ENQ_031"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "OBE0001", "StatusCode": "CARD_LMT_ENQ_001"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "OBE0002", "StatusCode": "CARD_LMT_ENQ_002"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "OBE0003", "StatusCode": "CARD_LMT_ENQ_003"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "OBE0004", "StatusCode": "CARD_LMT_ENQ_004"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "OBE0007", "StatusCode": "CARD_LMT_ENQ_005"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "OBE0045", "StatusCode": "CARD_LMT_ENQ_006"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "OBE8888", "StatusCode": "CARD_LMT_ENQ_007"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "00", "StatusCode": "CARD_LMT_ENQ_008"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "000", "StatusCode": "CARD_LMT_ENQ_009"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "F07", "StatusCode": "CARD_LMT_ENQ_010"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "OBE0059", "StatusCode": "CARD_LMT_ENQ_011"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "OBE0060", "StatusCode": "CARD_LMT_ENQ_012"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "OBE0045", "StatusCode": "CARD_LMT_ENQ_013"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "OBE0070", "StatusCode": "CARD_LMT_ENQ_014"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "OBE0102", "StatusCode": "CARD_LMT_ENQ_015"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "OBE0011", "StatusCode": "CARD_LMT_ENQ_016"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "OBE0066", "StatusCode": "CARD_LMT_ENQ_017"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "OBE0072", "StatusCode": "CARD_LMT_ENQ_018"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "OBE0073", "StatusCode": "CARD_LMT_ENQ_019"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "18", "StatusCode": "CARD_LMT_ENQ_020"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "16", "StatusCode": "CARD_LMT_ENQ_021"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "N6", "StatusCode": "CARD_LMT_ENQ_022"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "15", "StatusCode": "CARD_LMT_ENQ_023"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "N5", "StatusCode": "CARD_LMT_ENQ_024"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_ENQUIRY", "RawStatusCode": "27", "StatusCode": "CARD_LMT_ENQ_025"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0001", "StatusCode": "CARD_UPDT_001"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0002", "StatusCode": "CARD_UPDT_002"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0003", "StatusCode": "CARD_UPDT_003"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0004", "StatusCode": "CARD_UPDT_004"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0007", "StatusCode": "CARD_UPDT_005"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0045", "StatusCode": "CARD_UPDT_006"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE8888", "StatusCode": "CARD_UPDT_007"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "00", "StatusCode": "CARD_UPDT_008"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "000", "StatusCode": "CARD_UPDT_009"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "F07", "StatusCode": "CARD_UPDT_010"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0059", "StatusCode": "CARD_UPDT_011"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0060", "StatusCode": "CARD_UPDT_012"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0045", "StatusCode": "CARD_UPDT_013"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0101", "StatusCode": "CARD_UPDT_014"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0100", "StatusCode": "CARD_UPDT_015"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0099", "StatusCode": "CARD_UPDT_016"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0070", "StatusCode": "CARD_UPDT_017"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0102", "StatusCode": "CARD_UPDT_018"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0064", "StatusCode": "CARD_UPDT_019"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0011", "StatusCode": "CARD_UPDT_020"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0066", "StatusCode": "CARD_UPDT_021"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0072", "StatusCode": "CARD_UPDT_022"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0073", "StatusCode": "CARD_UPDT_023"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0106", "StatusCode": "CARD_UPDT_024"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0107", "StatusCode": "CARD_UPDT_025"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0113", "StatusCode": "CARD_UPDT_026"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0114", "StatusCode": "CARD_UPDT_027"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0115", "StatusCode": "CARD_UPDT_028"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0100", "StatusCode": "CARD_UPDT_029"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0101", "StatusCode": "CARD_UPDT_030"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0072", "StatusCode": "CARD_UPDT_032"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0067", "StatusCode": "CARD_UPDT_033"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0065", "StatusCode": "CARD_UPDT_034"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "18", "StatusCode": "CARD_UPDT_035"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "16", "StatusCode": "CARD_UPDT_036"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "N6", "StatusCode": "CARD_UPDT_037"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "15", "StatusCode": "CARD_UPDT_038"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "N5", "StatusCode": "CARD_UPDT_039"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "27", "StatusCode": "CARD_UPDT_040"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0061", "StatusCode": "CARD_UPDT_041"}, {"Vendor": "FEDERAL_BANK", "ApiType": "LIMIT_UPDATE", "RawStatusCode": "OBE0062", "StatusCode": "CARD_UPDT_042"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CREATE_CARD", "RawStatusCode": "OBE0001", "StatusCode": "CRT_CARD_001"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CREATE_CARD", "RawStatusCode": "OBE0002", "StatusCode": "CRT_CARD_002"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CREATE_CARD", "RawStatusCode": "OBE0003", "StatusCode": "CRT_CARD_003"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CREATE_CARD", "RawStatusCode": "OBE0004", "StatusCode": "CRT_CARD_004"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CREATE_CARD", "RawStatusCode": "OBE0060", "StatusCode": "CRT_CARD_005"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CREATE_CARD", "RawStatusCode": "OBE0069", "StatusCode": "CRT_CARD_006"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CREATE_CARD", "RawStatusCode": "OBE0059", "StatusCode": "CRT_CARD_007"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CREATE_CARD", "RawStatusCode": "OBE0011", "StatusCode": "CRT_CARD_008"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CREATE_CARD", "RawStatusCode": "OBE0103", "StatusCode": "CRT_CARD_009"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CREATE_CARD", "RawStatusCode": "OBE0104", "StatusCode": "CRT_CARD_010"}, {"Vendor": "FEDERAL_BANK", "ApiType": "CREATE_CARD", "RawStatusCode": "OBE8888", "StatusCode": "CRT_CARD_011"}]}