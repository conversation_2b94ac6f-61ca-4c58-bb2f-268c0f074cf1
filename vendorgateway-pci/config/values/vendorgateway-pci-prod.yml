Application:
  Environment: "prod"
  Name: "vendorgateway-pci"
  CardResponseStatusCodeFilePath: "./mappingJson/cardResponseStatusCodes.json"
  VendorToUrlMap:
    FEDERAL_BANK:
      DebitCardCreateURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardCreation"
      DebitCardCreateCallbackURL: "https://vnotificationgw.epifi.in/openbanking/card/federal"
      DebitCardActivateURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardActivation"
      DebitCardEnquiryUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardEnqService"
      DebitCardPinSetUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/PinManagement"
      DebitCardPinChangeUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/PinManagement"
      DebitCardPinResetUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/PinManagement"
      DebitCardPinValidationUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/PinManagement"
      DebitCardBlockUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardBlock"
      DebitCardSuspendOnOffUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardControl"
      DebitCardLocationOnOffUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardControl"
      DebitCardECommerceOnOffUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardControl"
      DebitCardCVVEnquiryUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardCVVEnq"
      DebitCardLimitEnquiry: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardLimitEnq"
      DebitCardUpdateLimit: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardLimitUpdate"
      DebitCardDeliveryTracking: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardDeliveryTrack"
      DebitCardPhysicalDispatchUrl: "https://gateway.federalbank.co.in/prod/prod/neobanking-card/v1.0.0/cardDispatch"
      DebitCardPhysicalDispatchCallbackUrl: "https://vnotificationgw.epifi.in/openbanking/cardPhysicalDispatch/federal"
      DebitCardConsolidatedCardControlUrl: "https://gateway.federalbank.co.in/prod/prod/neobanking-card/v1.0.0/FHMCardONOFF"

      CustomerCreationEnquiryStatusURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/enquiry/service"
      AccountCreationEnquiryStatusURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/enquiry/service"
      CardCreationEnquiryStatusURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/enquiry/service"
      DeviceReRegistrationDetailsEnquiryStatusURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/enquiry/service"
      MailingAddressModifyDetailsEnquiryStatusURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/enquiry/service"
      ShippingAddressUpdateEnquiryStatusURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/enquiry/service"
      DeviceRegistrationDetailsEnquiryStatusURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/enquiry/service"
      PhysicalCardDispatchDetailsEnquiryStatus: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/enquiry/service"
  Lending:
    CreditCard:
      M2P:
        M2PHost: "https://federalapi.m2pfintech.com/"
        M2PFallbackHost : "https://federalapi.m2pfintech.com/"
        EnableEncryption: true
        RotateKey: false

Server:
  Port: 8120
  SecurePort: 9527
  HealthCheckPort: 9999

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    #M2P
    M2PSecrets: "prod/vg-vgpci/m2p"
    M2PSecuredCardSecrets: "prod/vg-vgpci/m2p/secured"
    M2PMassUnsecuredCardSecrets: "prod/vg-vgpci/m2p/mass-unsecured"
    EpifiM2pRsaPrivateKey: "prod/vg-vgpci/epifi-m2p-private-key"
    EpifiM2pRsaPrivateKeyV2: "prod/vg-vgpci/epifi-m2p-private-key-2023"
    EpifiM2pRsaPublicKey: "prod/vg-vgpci/m2p-public-key"
    #Federal
    EpifiFederalPgpPrivateKey: "prod/pgp/pgp-epifi-fed-api-private-key"
    FederalPgpPublicKey: "prod/pgp/federal-pgp-pub-key-for-epifi"
    EpifiFederalPgpPassphrase: "prod/pgp/pgp-epifi-fed-api-password"
    SenderCode: "prod/vg-vn-simulator-vgpci/federal-auth-sender-code"
    ServiceAccessId: "prod/vg-vn-vgpci/federal-auth-service-access-id"
    ServiceAccessCode: "prod/vg-vn-vgpci/federal-auth-service-access-code"
    EpifiFederalCardDataPrivateKey: "prod/vg-vngw-vgpci/federal-card-data-decryption"
    EpifiFederalCardDataPrivateKeyFallBack: "prod/vg-vngw-vgpci/rsa-federal-card-data"

    #TLS certs
    SimulatorCert: "prod/vg-vgpci/tls-client-cert-for-sim"
    EpiFiFederalClientSslCert: "prod/vg-vgpci/tls-client-cert-for-federal-2024"
    EpiFiFederalClientSslKey: "prod/vg-vgpci/tls-client-priv-key-for-federal-2024"
    ClientId: "prod/vg-vgpci/federal-auth-client-id"
    ClientSecretKey: "prod/vg-vgpci/federal-auth-client-secret-key"

PGPInMemoryEntityStoreParams:
  InternalEntity:
    - Secret: "prod/pgp/v1/epifi-fed-api"
  ExternalEntity:
    - Secret: "prod/pgp/v1/federal-pgp-pub-key-for-epifi"
