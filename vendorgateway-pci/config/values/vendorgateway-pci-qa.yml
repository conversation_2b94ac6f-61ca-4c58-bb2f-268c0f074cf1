Application:
  Environment: "qa"
  Name: "vendorgateway-pci"
  CardResponseStatusCodeFilePath: "./mappingJson/cardResponseStatusCodes.json"
  VendorToUrlMap:
    FEDERAL_BANK:
      DebitCardCreateURL: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/CardCreation"
      DebitCardCreateCallbackURL: "https://vnotificationgw.qa.pointz.in/openbanking/card/federal"
      DebitCardActivateURL: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/CardActivation"
      DebitCardEnquiryUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/CardEnqService"
      DebitCardPinSetUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
      DebitCardPinChangeUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
      DebitCardPinResetUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
      DebitCardPinValidationUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
      DebitCardBlockUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
      DebitCardSuspendOnOffUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
      DebitCardLocationOnOffUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
      DebitCardECommerceOnOffUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
      DebitCardLimitEnquiry: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/LimitEnquiry"
      DebitCardUpdateLimit: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/LimitUpdate"
      DebitCardDeliveryTracking: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/DeliveryTracking"
      DebitCardCVVEnquiryUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/CVVEnquiry"
      DebitCardPhysicalDispatchUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/cardDispatch"
      DebitCardPhysicalDispatchCallbackUrl: "https://vnotificationgw.qa.pointz.in/openbanking/cardPhysicalDispatch/federal"
      DebitCardConsolidatedCardControlUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/FHMCardONOFF"

      CustomerCreationEnquiryStatusURL: "https://simulator.qa.pointz.in:9091/CustomerCreationEnquiryStatus"
      AccountCreationEnquiryStatusURL: "https://simulator.qa.pointz.in:9091/AccountCreationEnquiryStatus"
      CardCreationEnquiryStatusURL: "https://simulator.qa.pointz.in:9091/CardCreationEnquiryStatus"
      DeviceReRegistrationDetailsEnquiryStatusURL: "https://simulator.qa.pointz.in:9091/DeviceReRegistrationDetailsEnquiryStatus"
      MailingAddressModifyDetailsEnquiryStatusURL: "https://simulator.qa.pointz.in:9091/MailingAddressModifyDetailsEnquiryStatus"
      ShippingAddressUpdateEnquiryStatusURL: "https://simulator.qa.pointz.in:9091/ShippingAddressUpdateEnquiryStatus"
      DeviceRegistrationDetailsEnquiryStatusURL: "https://simulator.qa.pointz.in:9091/DeviceRegistrationDetailsEnquiryStatus"
      PhysicalCardDispatchDetailsEnquiryStatus: "https://simulator.qa.pointz.in:9091/PhysicalCardDispatchDetailsEnquiryStatus"
  Lending:
    CreditCard:
      M2P:
        M2PHost: "https://simulator.qa.pointz.in:9091/"
        M2PFallbackHost: "https://simulator.qa.pointz.in:9091/"
        EnableEncryption: false
Server:
  Port: 8120
  SecurePort: 9527
  HealthCheckPort: 9999

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    #M2P
    M2PSecrets: "qa/vg-vgpci/m2p"
    M2PSecuredCardSecrets: "qa/vg-vgpci/m2p/secured"
    M2PMassUnsecuredCardSecrets: "qa/vg-vgpci/m2p/mass-unsecured"
    EpifiM2pRsaPrivateKey: "qa/vg-vgpci/epifi-m2p-private-key"
    EpifiM2pRsaPublicKey: "qa/vg-vgpci/m2p-public-key"
    #Federal
    EpifiFederalPgpPrivateKey: "qa/pgp/pgp-epifi-key-fed-api"
    FederalPgpPublicKey: "qa/pgp/pgp-federal-dummy-key-for-sim"
    EpifiFederalPgpPassphrase: "qa/pgp/pgp-epifi-key-passphrase-fed-api"
    SenderCode: "qa/vg-vn-simulator-vgpci/federal-auth-sender-code"
    ServiceAccessId: "qa/vg-vn-vgpci/federal-auth-service-access-id"
    ServiceAccessCode: "qa/vg-vn-vgpci/federal-auth-service-access-code"
    EpifiFederalCardDataPrivateKey: "qa/vg-vngw-vgpci/rsa-federal-card-data"
    EpifiFederalCardDataPrivateKeyFallBack: "qa/vg-vngw-vgpci/rsa-federal-card-data"

    #TLS certs
    SimulatorCert: "qa/vg-vgpci/tls-client-cert-for-sim"
    EpiFiFederalClientSslCert: "qa/vg-vgpci/tls-client-cert-for-federal"
    EpiFiFederalClientSslKey: "qa/vg-vgpci/tls-client-priv-key-for-federal"
    ClientId: "qa/vg-vgpci/federal-auth-client-id"
    ClientSecretKey: "qa/vg-vgpci/federal-auth-client-secret-key"

PGPInMemoryEntityStoreParams:
  InternalEntity:
    - Secret: "qa/pgp/v1/pgp-epifi-fed-api"
  ExternalEntity:
    - Secret: "qa/pgp/v1/federal-simulator"
