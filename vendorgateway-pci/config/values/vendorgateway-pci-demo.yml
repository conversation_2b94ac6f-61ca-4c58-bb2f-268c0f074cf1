Application:
  Environment: "demo"
  Name: "vendorgateway-pci"
  CardResponseStatusCodeFilePath: "./mappingJson/cardResponseStatusCodes.json"
  VendorToUrlMap:
    FEDERAL_BANK:
      DebitCardCreateURL: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/CardCreation"
      DebitCardCreateCallbackURL: "https://vnotificationgw.demo.pointz.in/openbanking/card/federal"
      DebitCardActivateURL: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/CardActivation"
      DebitCardEnquiryUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/CardEnqService"
      DebitCardPinSetUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
      DebitCardPinChangeUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
      DebitCardPinResetUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
      DebitCardPinValidationUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
      DebitCardBlockUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
      DebitCardSuspendOnOffUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
      DebitCardLocationOnOffUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
      DebitCardECommerceOnOffUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
      DebitCardLimitEnquiry: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/LimitEnquiry"
      DebitCardUpdateLimit: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/LimitUpdate"
      DebitCardDeliveryTracking: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/DeliveryTracking"
      DebitCardCVVEnquiryUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/CVVEnquiry"
      DebitCardConsolidatedCardControlUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/FHMCardONOFF"
      DebitCardPhysicalDispatchUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/cardDispatch"
      DebitCardPhysicalDispatchCallbackUrl: "https://vnotificationgw.demo.pointz.in/openbanking/cardPhysicalDispatch/federal"
      CustomerCreationEnquiryStatusURL: "https://simulator.demo.pointz.in:9091/CustomerCreationEnquiryStatus"
      AccountCreationEnquiryStatusURL: "https://simulator.demo.pointz.in:9091/AccountCreationEnquiryStatus"
      CardCreationEnquiryStatusURL: "https://simulator.demo.pointz.in:9091/CardCreationEnquiryStatus"
      DeviceReRegistrationDetailsEnquiryStatusURL: "https://simulator.demo.pointz.in:9091/DeviceReRegistrationDetailsEnquiryStatus"
      MailingAddressModifyDetailsEnquiryStatusURL: "https://simulator.demo.pointz.in:9091/MailingAddressModifyDetailsEnquiryStatus"
      ShippingAddressUpdateEnquiryStatusURL: "https://simulator.demo.pointz.in:9091/ShippingAddressUpdateEnquiryStatus"
      DeviceRegistrationDetailsEnquiryStatusURL: "https://simulator.demo.pointz.in:9091/DeviceRegistrationDetailsEnquiryStatus"
      PhysicalCardDispatchDetailsEnquiryStatus: "https://simulator.demo.pointz.in:9091/PhysicalCardDispatchDetailsEnquiryStatus"
  Lending:
    CreditCard:
      M2P:
        M2PHost: "https://sit-secure.yappay.in/"
        M2PFallbackHost : "https://uat-bnpl.m2pfintech.com/"
        EnableEncryption: false
Server:
  Port: 8120
  SecurePort: 9527
  HealthCheckPort: 9999

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    #M2P
    M2PSecrets: "demo/vg-vgpci/m2p"
    M2PSecuredCardSecrets: "demo/vg-vgpci/m2p/secured"
    M2PMassUnsecuredCardSecrets: "demo/vg-vgpci/m2p/mass-unsecured"
    EpifiM2pRsaPrivateKey: "demo/vg-vgpci/epifi-m2p-private-key"
    EpifiM2pRsaPublicKey: "demo/vg-vgpci/m2p-public-key"
    #Federal
    EpifiFederalPgpPrivateKey: "demo/pgp/pgp-epifi-key-fed-api"
    FederalPgpPublicKey: "demo/pgp/pgp-federal-dummy-key-for-sim"
    EpifiFederalPgpPassphrase: "demo/pgp/pgp-epifi-key-passphrase-fed-api"
    EpifiFederalCardDataPrivateKey: "demo/vg-vngw-vgpci/rsa-federal-card-data"
    EpifiFederalCardDataPrivateKeyFallBack: "demo/vg-vngw-vgpci/rsa-federal-card-data"
    SenderCode: "demo/vg-vn-simulator-vgpci/federal-auth-sender-code"
    ServiceAccessId: "demo/vg-vn-vgpci/federal-auth-service-access-id"
    ServiceAccessCode: "demo/vg-vn-vgpci/federal-auth-service-access-code"
    ClientId: "demo/vg-vgpci/federal-auth-client-id"
    ClientSecretKey: "demo/vg-vgpci/federal-auth-client-secret-key"

    #TLS certs
    SimulatorCert: "demo/vg-vgpci/tls-client-cert-for-sim"
    EpiFiFederalClientSslCert: "demo/vg-vgpci/tls-client-cert-for-federal"
    EpiFiFederalClientSslKey: "demo/vg-vgpci/tls-client-priv-key-for-federal"

PGPInMemoryEntityStoreParams:
  InternalEntity:
    - Secret: "demo/pgp/v1/pgp-epifi-fed-api"
  ExternalEntity:
    - Secret: "demo/pgp/v1/federal-simulator"
