Application:
  Environment: "uat"
  Name: "vendorgateway-pci"
  CardResponseStatusCodeFilePath: "./mappingJson/cardResponseStatusCodes.json"
  VendorToUrlMap:
    FEDERAL_BANK:
      DebitCardCreateURL: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking-card/CardCreation"
      DebitCardCreateCallbackURL: "https://vnotificationgw.uat.pointz.in/openbanking/card/federal"
      DebitCardActivateURL: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking-card/CardActivation"
      DebitCardEnquiryUrl: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking-card/CardEnqService"
      DebitCardPinSetUrl: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking-card/PinManagement"
      DebitCardPinChangeUrl: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking-card/PinManagement"
      DebitCardPinResetUrl: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking-card/PinManagement"
      DebitCardPinValidationUrl: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking-card/PinManagement"
      DebitCardBlockUrl: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking-card/CardControl"
      DebitCardSuspendOnOffUrl: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking-card/CardControl"
      DebitCardLocationOnOffUrl: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking-card/CardControl"
      DebitCardECommerceOnOffUrl: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking-card/CardControl"
      DebitCardCVVEnquiryUrl: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking-card/CardCVVEnq"
      DebitCardLimitEnquiry: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking-card/CardLimitEnq"
      DebitCardUpdateLimit: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking-card/CardLimitUpdate"
      DebitCardDeliveryTracking: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking-card/DeliveryTracking"
      DebitCardPhysicalDispatchUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/neobanking-card/cardDispatch"
      DebitCardPhysicalDispatchCallbackUrl: "https://vnotificationgw.uat.pointz.in/openbanking/cardPhysicalDispatch/federal"
      DebitCardConsolidatedCardControlUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/neobanking-card/v1.0.0/FHMCardONOFF"

      CustomerCreationEnquiryStatusURL: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking/enquiry/service"
      AccountCreationEnquiryStatusURL: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking/enquiry/service"
      CardCreationEnquiryStatusURL: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking/enquiry/service"
      DeviceReRegistrationDetailsEnquiryStatusURL: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking/enquiry/service"
      MailingAddressModifyDetailsEnquiryStatusURL: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking/enquiry/service"
      ShippingAddressUpdateEnquiryStatusURL: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking/enquiry/service"
      DeviceRegistrationDetailsEnquiryStatusURL: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking/enquiry/service"
      PhysicalCardDispatchDetailsEnquiryStatus: "https://uatgateway.federalbank.co.in:444/fedbnk/uat/neobanking/enquiry/service"
  Lending:
    CreditCard:
      M2P:
        M2PHost: "https://ssltest.yappay.in/"
        M2PFallbackHost : "https://ssltest.yappay.in/"
        EnableEncryption: true
        RotateKey: true

Server:
  Port: 8120
  SecurePort: 9527
  HealthCheckPort: 9999

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    #M2P
    M2PSecrets: "uat/vg-vgpci/m2p"
    M2PSecuredCardSecrets: "uat/vg-vgpci/m2p/secured"
    M2PMassUnsecuredCardSecrets: "uat/vg-vgpci/m2p/mass-unsecured"
    EpifiM2pRsaPrivateKey: "uat/vg-vgpci/epifi-m2p-private-key"
    EpifiM2pRsaPrivateKeyV2: "uat/vg-vgpci/epifi-m2p-private-key-2023"
    EpifiM2pRsaPublicKey: "uat/vg-vgpci/m2p-public-key"
    #Federal
    EpifiFederalPgpPrivateKey: "uat/pgp/pgp-epifi-key-fed-api"
    FederalPgpPublicKey: "uat/pgp/federal-pgp-pub-key"
    EpifiFederalPgpPassphrase: "uat/pgp/pgp-epifi-key-passphrase-fed-api"
    SenderCode: "uat/vg-vn-simulator-vgpci/federal-auth-sender-code"
    ServiceAccessId: "uat/vg-vn-vgpci/federal-auth-service-access-id"
    ServiceAccessCode: "uat/vg-vn-vgpci/federal-auth-service-access-code"
    EpifiFederalCardDataPrivateKey: "uat/vg-vngw-vgpci/rsa-federal-card-data"
    EpifiFederalCardDataPrivateKeyFallBack: "uat/vg-vngw-vgpci/rsa-federal-card-data"

    #TLS certs
    SimulatorCert: "uat/vg-vgpci/tls-client-cert-for-sim"
    EpiFiFederalClientSslCert: "uat/vg-vgpci/tls-client-cert-for-federal"
    EpiFiFederalClientSslKey: "uat/vg-vgpci/tls-client-priv-key-for-federal"
    ClientId: "uat/vg-vgpci/federal-auth-client-id"
    ClientSecretKey: "uat/vg-vgpci/federal-auth-client-secret-key"

PGPInMemoryEntityStoreParams:
  InternalEntity:
    - Secret: "uat/pgp/v1/pgp-epifi-fed-api"
  ExternalEntity:
    - Secret: "uat/pgp/v1/federal-pgp-pub-key-for-epifi"
