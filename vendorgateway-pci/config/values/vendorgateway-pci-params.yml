Flags:
  TrimDebugMessageFromStatus: true
  TokenValidation: true
  UseCustomTrustedCertPool: true
  EnableFederalCardDecryptionByFallbackKey: true

SecureLogging:
  EnableSecureLog: true
  SecureLogPath: "/var/log/vendorgateway-pci/secure.log"
  MaxSizeInMBs: 5
  MaxBackups: 20

Logging:
  EnableLoggingToFile: true
  LogPath: "/var/log/vendorgateway-pci/info.log"
  MaxSizeInMBs: 1024
  MaxBackups: 1

# HTTP client config inspired from DefaultTransport of http package
# https://golang.org/src/net/http/transport.go?h=DefaultTransport#L42
HttpClientConfig:
  Transport:
    DialContext:
      Timeout: 30s
      KeepAlive: 30s
    TLSHandshakeTimeout: 10s
    MaxIdleConns: 100
    IdleConnTimeout: 90s
    MaxConnsPerHost: 250
    MaxIdleConnsPerHost: 50

PGPInMemoryEntityStoreParams:
  InternalEntity:
    - Secret: |
        {
         "key": "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",
         "cert": "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",
         "pass_phrase": "qwerty1234"
        }

  ExternalEntity:
    - Secret: |
        {
         "key": "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",
         "cert": "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",
         "pass_phrase": "qwerty1234"
        }
